// Code generated by running "go generate" in golang.org/x/text. DO NOT EDIT.

//go:build go1.10 && !go1.13

package norm

import "sync"

const (
	// Version is the Unicode edition from which the tables are derived.
	Version = "10.0.0"

	// MaxTransformChunkSize indicates the maximum number of bytes that Transform
	// may need to write atomically for any Form. Making a destination buffer at
	// least this size ensures that Transform can always make progress and that
	// the user does not need to grow the buffer on an ErrShortDst.
	MaxTransformChunkSize = 35 + maxNonStarters*4
)

var ccc = [55]uint8{
	0, 1, 7, 8, 9, 10, 11, 12,
	13, 14, 15, 16, 17, 18, 19, 20,
	21, 22, 23, 24, 25, 26, 27, 28,
	29, 30, 31, 32, 33, 34, 35, 36,
	84, 91, 103, 107, 118, 122, 129, 130,
	132, 202, 214, 216, 218, 220, 222, 224,
	226, 228, 230, 232, 233, 234, 240,
}

const (
	firstMulti            = 0x186D
	firstCCC              = 0x2C9E
	endMulti              = 0x2F60
	firstLeadingCCC       = 0x49AE
	firstCCCZeroExcept    = 0x4A78
	firstStarterWithNLead = 0x4A9F
	lastDecomp            = 0x4AA1
	maxDecomp             = 0x8000
)

// decomps: 19105 bytes
var decomps = [...]byte{
	// Bytes 0 - 3f
	0x00, 0x41, 0x20, 0x41, 0x21, 0x41, 0x22, 0x41,
	0x23, 0x41, 0x24, 0x41, 0x25, 0x41, 0x26, 0x41,
	0x27, 0x41, 0x28, 0x41, 0x29, 0x41, 0x2A, 0x41,
	0x2B, 0x41, 0x2C, 0x41, 0x2D, 0x41, 0x2E, 0x41,
	0x2F, 0x41, 0x30, 0x41, 0x31, 0x41, 0x32, 0x41,
	0x33, 0x41, 0x34, 0x41, 0x35, 0x41, 0x36, 0x41,
	0x37, 0x41, 0x38, 0x41, 0x39, 0x41, 0x3A, 0x41,
	0x3B, 0x41, 0x3C, 0x41, 0x3D, 0x41, 0x3E, 0x41,
	// Bytes 40 - 7f
	0x3F, 0x41, 0x40, 0x41, 0x41, 0x41, 0x42, 0x41,
	0x43, 0x41, 0x44, 0x41, 0x45, 0x41, 0x46, 0x41,
	0x47, 0x41, 0x48, 0x41, 0x49, 0x41, 0x4A, 0x41,
	0x4B, 0x41, 0x4C, 0x41, 0x4D, 0x41, 0x4E, 0x41,
	0x4F, 0x41, 0x50, 0x41, 0x51, 0x41, 0x52, 0x41,
	0x53, 0x41, 0x54, 0x41, 0x55, 0x41, 0x56, 0x41,
	0x57, 0x41, 0x58, 0x41, 0x59, 0x41, 0x5A, 0x41,
	0x5B, 0x41, 0x5C, 0x41, 0x5D, 0x41, 0x5E, 0x41,
	// Bytes 80 - bf
	0x5F, 0x41, 0x60, 0x41, 0x61, 0x41, 0x62, 0x41,
	0x63, 0x41, 0x64, 0x41, 0x65, 0x41, 0x66, 0x41,
	0x67, 0x41, 0x68, 0x41, 0x69, 0x41, 0x6A, 0x41,
	0x6B, 0x41, 0x6C, 0x41, 0x6D, 0x41, 0x6E, 0x41,
	0x6F, 0x41, 0x70, 0x41, 0x71, 0x41, 0x72, 0x41,
	0x73, 0x41, 0x74, 0x41, 0x75, 0x41, 0x76, 0x41,
	0x77, 0x41, 0x78, 0x41, 0x79, 0x41, 0x7A, 0x41,
	0x7B, 0x41, 0x7C, 0x41, 0x7D, 0x41, 0x7E, 0x42,
	// Bytes c0 - ff
	0xC2, 0xA2, 0x42, 0xC2, 0xA3, 0x42, 0xC2, 0xA5,
	0x42, 0xC2, 0xA6, 0x42, 0xC2, 0xAC, 0x42, 0xC2,
	0xB7, 0x42, 0xC3, 0x86, 0x42, 0xC3, 0xB0, 0x42,
	0xC4, 0xA6, 0x42, 0xC4, 0xA7, 0x42, 0xC4, 0xB1,
	0x42, 0xC5, 0x8B, 0x42, 0xC5, 0x93, 0x42, 0xC6,
	0x8E, 0x42, 0xC6, 0x90, 0x42, 0xC6, 0xAB, 0x42,
	0xC8, 0xA2, 0x42, 0xC8, 0xB7, 0x42, 0xC9, 0x90,
	0x42, 0xC9, 0x91, 0x42, 0xC9, 0x92, 0x42, 0xC9,
	// Bytes 100 - 13f
	0x94, 0x42, 0xC9, 0x95, 0x42, 0xC9, 0x99, 0x42,
	0xC9, 0x9B, 0x42, 0xC9, 0x9C, 0x42, 0xC9, 0x9F,
	0x42, 0xC9, 0xA1, 0x42, 0xC9, 0xA3, 0x42, 0xC9,
	0xA5, 0x42, 0xC9, 0xA6, 0x42, 0xC9, 0xA8, 0x42,
	0xC9, 0xA9, 0x42, 0xC9, 0xAA, 0x42, 0xC9, 0xAB,
	0x42, 0xC9, 0xAD, 0x42, 0xC9, 0xAF, 0x42, 0xC9,
	0xB0, 0x42, 0xC9, 0xB1, 0x42, 0xC9, 0xB2, 0x42,
	0xC9, 0xB3, 0x42, 0xC9, 0xB4, 0x42, 0xC9, 0xB5,
	// Bytes 140 - 17f
	0x42, 0xC9, 0xB8, 0x42, 0xC9, 0xB9, 0x42, 0xC9,
	0xBB, 0x42, 0xCA, 0x81, 0x42, 0xCA, 0x82, 0x42,
	0xCA, 0x83, 0x42, 0xCA, 0x89, 0x42, 0xCA, 0x8A,
	0x42, 0xCA, 0x8B, 0x42, 0xCA, 0x8C, 0x42, 0xCA,
	0x90, 0x42, 0xCA, 0x91, 0x42, 0xCA, 0x92, 0x42,
	0xCA, 0x95, 0x42, 0xCA, 0x9D, 0x42, 0xCA, 0x9F,
	0x42, 0xCA, 0xB9, 0x42, 0xCE, 0x91, 0x42, 0xCE,
	0x92, 0x42, 0xCE, 0x93, 0x42, 0xCE, 0x94, 0x42,
	// Bytes 180 - 1bf
	0xCE, 0x95, 0x42, 0xCE, 0x96, 0x42, 0xCE, 0x97,
	0x42, 0xCE, 0x98, 0x42, 0xCE, 0x99, 0x42, 0xCE,
	0x9A, 0x42, 0xCE, 0x9B, 0x42, 0xCE, 0x9C, 0x42,
	0xCE, 0x9D, 0x42, 0xCE, 0x9E, 0x42, 0xCE, 0x9F,
	0x42, 0xCE, 0xA0, 0x42, 0xCE, 0xA1, 0x42, 0xCE,
	0xA3, 0x42, 0xCE, 0xA4, 0x42, 0xCE, 0xA5, 0x42,
	0xCE, 0xA6, 0x42, 0xCE, 0xA7, 0x42, 0xCE, 0xA8,
	0x42, 0xCE, 0xA9, 0x42, 0xCE, 0xB1, 0x42, 0xCE,
	// Bytes 1c0 - 1ff
	0xB2, 0x42, 0xCE, 0xB3, 0x42, 0xCE, 0xB4, 0x42,
	0xCE, 0xB5, 0x42, 0xCE, 0xB6, 0x42, 0xCE, 0xB7,
	0x42, 0xCE, 0xB8, 0x42, 0xCE, 0xB9, 0x42, 0xCE,
	0xBA, 0x42, 0xCE, 0xBB, 0x42, 0xCE, 0xBC, 0x42,
	0xCE, 0xBD, 0x42, 0xCE, 0xBE, 0x42, 0xCE, 0xBF,
	0x42, 0xCF, 0x80, 0x42, 0xCF, 0x81, 0x42, 0xCF,
	0x82, 0x42, 0xCF, 0x83, 0x42, 0xCF, 0x84, 0x42,
	0xCF, 0x85, 0x42, 0xCF, 0x86, 0x42, 0xCF, 0x87,
	// Bytes 200 - 23f
	0x42, 0xCF, 0x88, 0x42, 0xCF, 0x89, 0x42, 0xCF,
	0x9C, 0x42, 0xCF, 0x9D, 0x42, 0xD0, 0xBD, 0x42,
	0xD1, 0x8A, 0x42, 0xD1, 0x8C, 0x42, 0xD7, 0x90,
	0x42, 0xD7, 0x91, 0x42, 0xD7, 0x92, 0x42, 0xD7,
	0x93, 0x42, 0xD7, 0x94, 0x42, 0xD7, 0x9B, 0x42,
	0xD7, 0x9C, 0x42, 0xD7, 0x9D, 0x42, 0xD7, 0xA2,
	0x42, 0xD7, 0xA8, 0x42, 0xD7, 0xAA, 0x42, 0xD8,
	0xA1, 0x42, 0xD8, 0xA7, 0x42, 0xD8, 0xA8, 0x42,
	// Bytes 240 - 27f
	0xD8, 0xA9, 0x42, 0xD8, 0xAA, 0x42, 0xD8, 0xAB,
	0x42, 0xD8, 0xAC, 0x42, 0xD8, 0xAD, 0x42, 0xD8,
	0xAE, 0x42, 0xD8, 0xAF, 0x42, 0xD8, 0xB0, 0x42,
	0xD8, 0xB1, 0x42, 0xD8, 0xB2, 0x42, 0xD8, 0xB3,
	0x42, 0xD8, 0xB4, 0x42, 0xD8, 0xB5, 0x42, 0xD8,
	0xB6, 0x42, 0xD8, 0xB7, 0x42, 0xD8, 0xB8, 0x42,
	0xD8, 0xB9, 0x42, 0xD8, 0xBA, 0x42, 0xD9, 0x81,
	0x42, 0xD9, 0x82, 0x42, 0xD9, 0x83, 0x42, 0xD9,
	// Bytes 280 - 2bf
	0x84, 0x42, 0xD9, 0x85, 0x42, 0xD9, 0x86, 0x42,
	0xD9, 0x87, 0x42, 0xD9, 0x88, 0x42, 0xD9, 0x89,
	0x42, 0xD9, 0x8A, 0x42, 0xD9, 0xAE, 0x42, 0xD9,
	0xAF, 0x42, 0xD9, 0xB1, 0x42, 0xD9, 0xB9, 0x42,
	0xD9, 0xBA, 0x42, 0xD9, 0xBB, 0x42, 0xD9, 0xBE,
	0x42, 0xD9, 0xBF, 0x42, 0xDA, 0x80, 0x42, 0xDA,
	0x83, 0x42, 0xDA, 0x84, 0x42, 0xDA, 0x86, 0x42,
	0xDA, 0x87, 0x42, 0xDA, 0x88, 0x42, 0xDA, 0x8C,
	// Bytes 2c0 - 2ff
	0x42, 0xDA, 0x8D, 0x42, 0xDA, 0x8E, 0x42, 0xDA,
	0x91, 0x42, 0xDA, 0x98, 0x42, 0xDA, 0xA1, 0x42,
	0xDA, 0xA4, 0x42, 0xDA, 0xA6, 0x42, 0xDA, 0xA9,
	0x42, 0xDA, 0xAD, 0x42, 0xDA, 0xAF, 0x42, 0xDA,
	0xB1, 0x42, 0xDA, 0xB3, 0x42, 0xDA, 0xBA, 0x42,
	0xDA, 0xBB, 0x42, 0xDA, 0xBE, 0x42, 0xDB, 0x81,
	0x42, 0xDB, 0x85, 0x42, 0xDB, 0x86, 0x42, 0xDB,
	0x87, 0x42, 0xDB, 0x88, 0x42, 0xDB, 0x89, 0x42,
	// Bytes 300 - 33f
	0xDB, 0x8B, 0x42, 0xDB, 0x8C, 0x42, 0xDB, 0x90,
	0x42, 0xDB, 0x92, 0x43, 0xE0, 0xBC, 0x8B, 0x43,
	0xE1, 0x83, 0x9C, 0x43, 0xE1, 0x84, 0x80, 0x43,
	0xE1, 0x84, 0x81, 0x43, 0xE1, 0x84, 0x82, 0x43,
	0xE1, 0x84, 0x83, 0x43, 0xE1, 0x84, 0x84, 0x43,
	0xE1, 0x84, 0x85, 0x43, 0xE1, 0x84, 0x86, 0x43,
	0xE1, 0x84, 0x87, 0x43, 0xE1, 0x84, 0x88, 0x43,
	0xE1, 0x84, 0x89, 0x43, 0xE1, 0x84, 0x8A, 0x43,
	// Bytes 340 - 37f
	0xE1, 0x84, 0x8B, 0x43, 0xE1, 0x84, 0x8C, 0x43,
	0xE1, 0x84, 0x8D, 0x43, 0xE1, 0x84, 0x8E, 0x43,
	0xE1, 0x84, 0x8F, 0x43, 0xE1, 0x84, 0x90, 0x43,
	0xE1, 0x84, 0x91, 0x43, 0xE1, 0x84, 0x92, 0x43,
	0xE1, 0x84, 0x94, 0x43, 0xE1, 0x84, 0x95, 0x43,
	0xE1, 0x84, 0x9A, 0x43, 0xE1, 0x84, 0x9C, 0x43,
	0xE1, 0x84, 0x9D, 0x43, 0xE1, 0x84, 0x9E, 0x43,
	0xE1, 0x84, 0xA0, 0x43, 0xE1, 0x84, 0xA1, 0x43,
	// Bytes 380 - 3bf
	0xE1, 0x84, 0xA2, 0x43, 0xE1, 0x84, 0xA3, 0x43,
	0xE1, 0x84, 0xA7, 0x43, 0xE1, 0x84, 0xA9, 0x43,
	0xE1, 0x84, 0xAB, 0x43, 0xE1, 0x84, 0xAC, 0x43,
	0xE1, 0x84, 0xAD, 0x43, 0xE1, 0x84, 0xAE, 0x43,
	0xE1, 0x84, 0xAF, 0x43, 0xE1, 0x84, 0xB2, 0x43,
	0xE1, 0x84, 0xB6, 0x43, 0xE1, 0x85, 0x80, 0x43,
	0xE1, 0x85, 0x87, 0x43, 0xE1, 0x85, 0x8C, 0x43,
	0xE1, 0x85, 0x97, 0x43, 0xE1, 0x85, 0x98, 0x43,
	// Bytes 3c0 - 3ff
	0xE1, 0x85, 0x99, 0x43, 0xE1, 0x85, 0xA0, 0x43,
	0xE1, 0x86, 0x84, 0x43, 0xE1, 0x86, 0x85, 0x43,
	0xE1, 0x86, 0x88, 0x43, 0xE1, 0x86, 0x91, 0x43,
	0xE1, 0x86, 0x92, 0x43, 0xE1, 0x86, 0x94, 0x43,
	0xE1, 0x86, 0x9E, 0x43, 0xE1, 0x86, 0xA1, 0x43,
	0xE1, 0x87, 0x87, 0x43, 0xE1, 0x87, 0x88, 0x43,
	0xE1, 0x87, 0x8C, 0x43, 0xE1, 0x87, 0x8E, 0x43,
	0xE1, 0x87, 0x93, 0x43, 0xE1, 0x87, 0x97, 0x43,
	// Bytes 400 - 43f
	0xE1, 0x87, 0x99, 0x43, 0xE1, 0x87, 0x9D, 0x43,
	0xE1, 0x87, 0x9F, 0x43, 0xE1, 0x87, 0xB1, 0x43,
	0xE1, 0x87, 0xB2, 0x43, 0xE1, 0xB4, 0x82, 0x43,
	0xE1, 0xB4, 0x96, 0x43, 0xE1, 0xB4, 0x97, 0x43,
	0xE1, 0xB4, 0x9C, 0x43, 0xE1, 0xB4, 0x9D, 0x43,
	0xE1, 0xB4, 0xA5, 0x43, 0xE1, 0xB5, 0xBB, 0x43,
	0xE1, 0xB6, 0x85, 0x43, 0xE2, 0x80, 0x82, 0x43,
	0xE2, 0x80, 0x83, 0x43, 0xE2, 0x80, 0x90, 0x43,
	// Bytes 440 - 47f
	0xE2, 0x80, 0x93, 0x43, 0xE2, 0x80, 0x94, 0x43,
	0xE2, 0x82, 0xA9, 0x43, 0xE2, 0x86, 0x90, 0x43,
	0xE2, 0x86, 0x91, 0x43, 0xE2, 0x86, 0x92, 0x43,
	0xE2, 0x86, 0x93, 0x43, 0xE2, 0x88, 0x82, 0x43,
	0xE2, 0x88, 0x87, 0x43, 0xE2, 0x88, 0x91, 0x43,
	0xE2, 0x88, 0x92, 0x43, 0xE2, 0x94, 0x82, 0x43,
	0xE2, 0x96, 0xA0, 0x43, 0xE2, 0x97, 0x8B, 0x43,
	0xE2, 0xA6, 0x85, 0x43, 0xE2, 0xA6, 0x86, 0x43,
	// Bytes 480 - 4bf
	0xE2, 0xB5, 0xA1, 0x43, 0xE3, 0x80, 0x81, 0x43,
	0xE3, 0x80, 0x82, 0x43, 0xE3, 0x80, 0x88, 0x43,
	0xE3, 0x80, 0x89, 0x43, 0xE3, 0x80, 0x8A, 0x43,
	0xE3, 0x80, 0x8B, 0x43, 0xE3, 0x80, 0x8C, 0x43,
	0xE3, 0x80, 0x8D, 0x43, 0xE3, 0x80, 0x8E, 0x43,
	0xE3, 0x80, 0x8F, 0x43, 0xE3, 0x80, 0x90, 0x43,
	0xE3, 0x80, 0x91, 0x43, 0xE3, 0x80, 0x92, 0x43,
	0xE3, 0x80, 0x94, 0x43, 0xE3, 0x80, 0x95, 0x43,
	// Bytes 4c0 - 4ff
	0xE3, 0x80, 0x96, 0x43, 0xE3, 0x80, 0x97, 0x43,
	0xE3, 0x82, 0xA1, 0x43, 0xE3, 0x82, 0xA2, 0x43,
	0xE3, 0x82, 0xA3, 0x43, 0xE3, 0x82, 0xA4, 0x43,
	0xE3, 0x82, 0xA5, 0x43, 0xE3, 0x82, 0xA6, 0x43,
	0xE3, 0x82, 0xA7, 0x43, 0xE3, 0x82, 0xA8, 0x43,
	0xE3, 0x82, 0xA9, 0x43, 0xE3, 0x82, 0xAA, 0x43,
	0xE3, 0x82, 0xAB, 0x43, 0xE3, 0x82, 0xAD, 0x43,
	0xE3, 0x82, 0xAF, 0x43, 0xE3, 0x82, 0xB1, 0x43,
	// Bytes 500 - 53f
	0xE3, 0x82, 0xB3, 0x43, 0xE3, 0x82, 0xB5, 0x43,
	0xE3, 0x82, 0xB7, 0x43, 0xE3, 0x82, 0xB9, 0x43,
	0xE3, 0x82, 0xBB, 0x43, 0xE3, 0x82, 0xBD, 0x43,
	0xE3, 0x82, 0xBF, 0x43, 0xE3, 0x83, 0x81, 0x43,
	0xE3, 0x83, 0x83, 0x43, 0xE3, 0x83, 0x84, 0x43,
	0xE3, 0x83, 0x86, 0x43, 0xE3, 0x83, 0x88, 0x43,
	0xE3, 0x83, 0x8A, 0x43, 0xE3, 0x83, 0x8B, 0x43,
	0xE3, 0x83, 0x8C, 0x43, 0xE3, 0x83, 0x8D, 0x43,
	// Bytes 540 - 57f
	0xE3, 0x83, 0x8E, 0x43, 0xE3, 0x83, 0x8F, 0x43,
	0xE3, 0x83, 0x92, 0x43, 0xE3, 0x83, 0x95, 0x43,
	0xE3, 0x83, 0x98, 0x43, 0xE3, 0x83, 0x9B, 0x43,
	0xE3, 0x83, 0x9E, 0x43, 0xE3, 0x83, 0x9F, 0x43,
	0xE3, 0x83, 0xA0, 0x43, 0xE3, 0x83, 0xA1, 0x43,
	0xE3, 0x83, 0xA2, 0x43, 0xE3, 0x83, 0xA3, 0x43,
	0xE3, 0x83, 0xA4, 0x43, 0xE3, 0x83, 0xA5, 0x43,
	0xE3, 0x83, 0xA6, 0x43, 0xE3, 0x83, 0xA7, 0x43,
	// Bytes 580 - 5bf
	0xE3, 0x83, 0xA8, 0x43, 0xE3, 0x83, 0xA9, 0x43,
	0xE3, 0x83, 0xAA, 0x43, 0xE3, 0x83, 0xAB, 0x43,
	0xE3, 0x83, 0xAC, 0x43, 0xE3, 0x83, 0xAD, 0x43,
	0xE3, 0x83, 0xAF, 0x43, 0xE3, 0x83, 0xB0, 0x43,
	0xE3, 0x83, 0xB1, 0x43, 0xE3, 0x83, 0xB2, 0x43,
	0xE3, 0x83, 0xB3, 0x43, 0xE3, 0x83, 0xBB, 0x43,
	0xE3, 0x83, 0xBC, 0x43, 0xE3, 0x92, 0x9E, 0x43,
	0xE3, 0x92, 0xB9, 0x43, 0xE3, 0x92, 0xBB, 0x43,
	// Bytes 5c0 - 5ff
	0xE3, 0x93, 0x9F, 0x43, 0xE3, 0x94, 0x95, 0x43,
	0xE3, 0x9B, 0xAE, 0x43, 0xE3, 0x9B, 0xBC, 0x43,
	0xE3, 0x9E, 0x81, 0x43, 0xE3, 0xA0, 0xAF, 0x43,
	0xE3, 0xA1, 0xA2, 0x43, 0xE3, 0xA1, 0xBC, 0x43,
	0xE3, 0xA3, 0x87, 0x43, 0xE3, 0xA3, 0xA3, 0x43,
	0xE3, 0xA4, 0x9C, 0x43, 0xE3, 0xA4, 0xBA, 0x43,
	0xE3, 0xA8, 0xAE, 0x43, 0xE3, 0xA9, 0xAC, 0x43,
	0xE3, 0xAB, 0xA4, 0x43, 0xE3, 0xAC, 0x88, 0x43,
	// Bytes 600 - 63f
	0xE3, 0xAC, 0x99, 0x43, 0xE3, 0xAD, 0x89, 0x43,
	0xE3, 0xAE, 0x9D, 0x43, 0xE3, 0xB0, 0x98, 0x43,
	0xE3, 0xB1, 0x8E, 0x43, 0xE3, 0xB4, 0xB3, 0x43,
	0xE3, 0xB6, 0x96, 0x43, 0xE3, 0xBA, 0xAC, 0x43,
	0xE3, 0xBA, 0xB8, 0x43, 0xE3, 0xBC, 0x9B, 0x43,
	0xE3, 0xBF, 0xBC, 0x43, 0xE4, 0x80, 0x88, 0x43,
	0xE4, 0x80, 0x98, 0x43, 0xE4, 0x80, 0xB9, 0x43,
	0xE4, 0x81, 0x86, 0x43, 0xE4, 0x82, 0x96, 0x43,
	// Bytes 640 - 67f
	0xE4, 0x83, 0xA3, 0x43, 0xE4, 0x84, 0xAF, 0x43,
	0xE4, 0x88, 0x82, 0x43, 0xE4, 0x88, 0xA7, 0x43,
	0xE4, 0x8A, 0xA0, 0x43, 0xE4, 0x8C, 0x81, 0x43,
	0xE4, 0x8C, 0xB4, 0x43, 0xE4, 0x8D, 0x99, 0x43,
	0xE4, 0x8F, 0x95, 0x43, 0xE4, 0x8F, 0x99, 0x43,
	0xE4, 0x90, 0x8B, 0x43, 0xE4, 0x91, 0xAB, 0x43,
	0xE4, 0x94, 0xAB, 0x43, 0xE4, 0x95, 0x9D, 0x43,
	0xE4, 0x95, 0xA1, 0x43, 0xE4, 0x95, 0xAB, 0x43,
	// Bytes 680 - 6bf
	0xE4, 0x97, 0x97, 0x43, 0xE4, 0x97, 0xB9, 0x43,
	0xE4, 0x98, 0xB5, 0x43, 0xE4, 0x9A, 0xBE, 0x43,
	0xE4, 0x9B, 0x87, 0x43, 0xE4, 0xA6, 0x95, 0x43,
	0xE4, 0xA7, 0xA6, 0x43, 0xE4, 0xA9, 0xAE, 0x43,
	0xE4, 0xA9, 0xB6, 0x43, 0xE4, 0xAA, 0xB2, 0x43,
	0xE4, 0xAC, 0xB3, 0x43, 0xE4, 0xAF, 0x8E, 0x43,
	0xE4, 0xB3, 0x8E, 0x43, 0xE4, 0xB3, 0xAD, 0x43,
	0xE4, 0xB3, 0xB8, 0x43, 0xE4, 0xB5, 0x96, 0x43,
	// Bytes 6c0 - 6ff
	0xE4, 0xB8, 0x80, 0x43, 0xE4, 0xB8, 0x81, 0x43,
	0xE4, 0xB8, 0x83, 0x43, 0xE4, 0xB8, 0x89, 0x43,
	0xE4, 0xB8, 0x8A, 0x43, 0xE4, 0xB8, 0x8B, 0x43,
	0xE4, 0xB8, 0x8D, 0x43, 0xE4, 0xB8, 0x99, 0x43,
	0xE4, 0xB8, 0xA6, 0x43, 0xE4, 0xB8, 0xA8, 0x43,
	0xE4, 0xB8, 0xAD, 0x43, 0xE4, 0xB8, 0xB2, 0x43,
	0xE4, 0xB8, 0xB6, 0x43, 0xE4, 0xB8, 0xB8, 0x43,
	0xE4, 0xB8, 0xB9, 0x43, 0xE4, 0xB8, 0xBD, 0x43,
	// Bytes 700 - 73f
	0xE4, 0xB8, 0xBF, 0x43, 0xE4, 0xB9, 0x81, 0x43,
	0xE4, 0xB9, 0x99, 0x43, 0xE4, 0xB9, 0x9D, 0x43,
	0xE4, 0xBA, 0x82, 0x43, 0xE4, 0xBA, 0x85, 0x43,
	0xE4, 0xBA, 0x86, 0x43, 0xE4, 0xBA, 0x8C, 0x43,
	0xE4, 0xBA, 0x94, 0x43, 0xE4, 0xBA, 0xA0, 0x43,
	0xE4, 0xBA, 0xA4, 0x43, 0xE4, 0xBA, 0xAE, 0x43,
	0xE4, 0xBA, 0xBA, 0x43, 0xE4, 0xBB, 0x80, 0x43,
	0xE4, 0xBB, 0x8C, 0x43, 0xE4, 0xBB, 0xA4, 0x43,
	// Bytes 740 - 77f
	0xE4, 0xBC, 0x81, 0x43, 0xE4, 0xBC, 0x91, 0x43,
	0xE4, 0xBD, 0xA0, 0x43, 0xE4, 0xBE, 0x80, 0x43,
	0xE4, 0xBE, 0x86, 0x43, 0xE4, 0xBE, 0x8B, 0x43,
	0xE4, 0xBE, 0xAE, 0x43, 0xE4, 0xBE, 0xBB, 0x43,
	0xE4, 0xBE, 0xBF, 0x43, 0xE5, 0x80, 0x82, 0x43,
	0xE5, 0x80, 0xAB, 0x43, 0xE5, 0x81, 0xBA, 0x43,
	0xE5, 0x82, 0x99, 0x43, 0xE5, 0x83, 0x8F, 0x43,
	0xE5, 0x83, 0x9A, 0x43, 0xE5, 0x83, 0xA7, 0x43,
	// Bytes 780 - 7bf
	0xE5, 0x84, 0xAA, 0x43, 0xE5, 0x84, 0xBF, 0x43,
	0xE5, 0x85, 0x80, 0x43, 0xE5, 0x85, 0x85, 0x43,
	0xE5, 0x85, 0x8D, 0x43, 0xE5, 0x85, 0x94, 0x43,
	0xE5, 0x85, 0xA4, 0x43, 0xE5, 0x85, 0xA5, 0x43,
	0xE5, 0x85, 0xA7, 0x43, 0xE5, 0x85, 0xA8, 0x43,
	0xE5, 0x85, 0xA9, 0x43, 0xE5, 0x85, 0xAB, 0x43,
	0xE5, 0x85, 0xAD, 0x43, 0xE5, 0x85, 0xB7, 0x43,
	0xE5, 0x86, 0x80, 0x43, 0xE5, 0x86, 0x82, 0x43,
	// Bytes 7c0 - 7ff
	0xE5, 0x86, 0x8D, 0x43, 0xE5, 0x86, 0x92, 0x43,
	0xE5, 0x86, 0x95, 0x43, 0xE5, 0x86, 0x96, 0x43,
	0xE5, 0x86, 0x97, 0x43, 0xE5, 0x86, 0x99, 0x43,
	0xE5, 0x86, 0xA4, 0x43, 0xE5, 0x86, 0xAB, 0x43,
	0xE5, 0x86, 0xAC, 0x43, 0xE5, 0x86, 0xB5, 0x43,
	0xE5, 0x86, 0xB7, 0x43, 0xE5, 0x87, 0x89, 0x43,
	0xE5, 0x87, 0x8C, 0x43, 0xE5, 0x87, 0x9C, 0x43,
	0xE5, 0x87, 0x9E, 0x43, 0xE5, 0x87, 0xA0, 0x43,
	// Bytes 800 - 83f
	0xE5, 0x87, 0xB5, 0x43, 0xE5, 0x88, 0x80, 0x43,
	0xE5, 0x88, 0x83, 0x43, 0xE5, 0x88, 0x87, 0x43,
	0xE5, 0x88, 0x97, 0x43, 0xE5, 0x88, 0x9D, 0x43,
	0xE5, 0x88, 0xA9, 0x43, 0xE5, 0x88, 0xBA, 0x43,
	0xE5, 0x88, 0xBB, 0x43, 0xE5, 0x89, 0x86, 0x43,
	0xE5, 0x89, 0x8D, 0x43, 0xE5, 0x89, 0xB2, 0x43,
	0xE5, 0x89, 0xB7, 0x43, 0xE5, 0x8A, 0x89, 0x43,
	0xE5, 0x8A, 0x9B, 0x43, 0xE5, 0x8A, 0xA3, 0x43,
	// Bytes 840 - 87f
	0xE5, 0x8A, 0xB3, 0x43, 0xE5, 0x8A, 0xB4, 0x43,
	0xE5, 0x8B, 0x87, 0x43, 0xE5, 0x8B, 0x89, 0x43,
	0xE5, 0x8B, 0x92, 0x43, 0xE5, 0x8B, 0x9E, 0x43,
	0xE5, 0x8B, 0xA4, 0x43, 0xE5, 0x8B, 0xB5, 0x43,
	0xE5, 0x8B, 0xB9, 0x43, 0xE5, 0x8B, 0xBA, 0x43,
	0xE5, 0x8C, 0x85, 0x43, 0xE5, 0x8C, 0x86, 0x43,
	0xE5, 0x8C, 0x95, 0x43, 0xE5, 0x8C, 0x97, 0x43,
	0xE5, 0x8C, 0x9A, 0x43, 0xE5, 0x8C, 0xB8, 0x43,
	// Bytes 880 - 8bf
	0xE5, 0x8C, 0xBB, 0x43, 0xE5, 0x8C, 0xBF, 0x43,
	0xE5, 0x8D, 0x81, 0x43, 0xE5, 0x8D, 0x84, 0x43,
	0xE5, 0x8D, 0x85, 0x43, 0xE5, 0x8D, 0x89, 0x43,
	0xE5, 0x8D, 0x91, 0x43, 0xE5, 0x8D, 0x94, 0x43,
	0xE5, 0x8D, 0x9A, 0x43, 0xE5, 0x8D, 0x9C, 0x43,
	0xE5, 0x8D, 0xA9, 0x43, 0xE5, 0x8D, 0xB0, 0x43,
	0xE5, 0x8D, 0xB3, 0x43, 0xE5, 0x8D, 0xB5, 0x43,
	0xE5, 0x8D, 0xBD, 0x43, 0xE5, 0x8D, 0xBF, 0x43,
	// Bytes 8c0 - 8ff
	0xE5, 0x8E, 0x82, 0x43, 0xE5, 0x8E, 0xB6, 0x43,
	0xE5, 0x8F, 0x83, 0x43, 0xE5, 0x8F, 0x88, 0x43,
	0xE5, 0x8F, 0x8A, 0x43, 0xE5, 0x8F, 0x8C, 0x43,
	0xE5, 0x8F, 0x9F, 0x43, 0xE5, 0x8F, 0xA3, 0x43,
	0xE5, 0x8F, 0xA5, 0x43, 0xE5, 0x8F, 0xAB, 0x43,
	0xE5, 0x8F, 0xAF, 0x43, 0xE5, 0x8F, 0xB1, 0x43,
	0xE5, 0x8F, 0xB3, 0x43, 0xE5, 0x90, 0x86, 0x43,
	0xE5, 0x90, 0x88, 0x43, 0xE5, 0x90, 0x8D, 0x43,
	// Bytes 900 - 93f
	0xE5, 0x90, 0x8F, 0x43, 0xE5, 0x90, 0x9D, 0x43,
	0xE5, 0x90, 0xB8, 0x43, 0xE5, 0x90, 0xB9, 0x43,
	0xE5, 0x91, 0x82, 0x43, 0xE5, 0x91, 0x88, 0x43,
	0xE5, 0x91, 0xA8, 0x43, 0xE5, 0x92, 0x9E, 0x43,
	0xE5, 0x92, 0xA2, 0x43, 0xE5, 0x92, 0xBD, 0x43,
	0xE5, 0x93, 0xB6, 0x43, 0xE5, 0x94, 0x90, 0x43,
	0xE5, 0x95, 0x8F, 0x43, 0xE5, 0x95, 0x93, 0x43,
	0xE5, 0x95, 0x95, 0x43, 0xE5, 0x95, 0xA3, 0x43,
	// Bytes 940 - 97f
	0xE5, 0x96, 0x84, 0x43, 0xE5, 0x96, 0x87, 0x43,
	0xE5, 0x96, 0x99, 0x43, 0xE5, 0x96, 0x9D, 0x43,
	0xE5, 0x96, 0xAB, 0x43, 0xE5, 0x96, 0xB3, 0x43,
	0xE5, 0x96, 0xB6, 0x43, 0xE5, 0x97, 0x80, 0x43,
	0xE5, 0x97, 0x82, 0x43, 0xE5, 0x97, 0xA2, 0x43,
	0xE5, 0x98, 0x86, 0x43, 0xE5, 0x99, 0x91, 0x43,
	0xE5, 0x99, 0xA8, 0x43, 0xE5, 0x99, 0xB4, 0x43,
	0xE5, 0x9B, 0x97, 0x43, 0xE5, 0x9B, 0x9B, 0x43,
	// Bytes 980 - 9bf
	0xE5, 0x9B, 0xB9, 0x43, 0xE5, 0x9C, 0x96, 0x43,
	0xE5, 0x9C, 0x97, 0x43, 0xE5, 0x9C, 0x9F, 0x43,
	0xE5, 0x9C, 0xB0, 0x43, 0xE5, 0x9E, 0x8B, 0x43,
	0xE5, 0x9F, 0x8E, 0x43, 0xE5, 0x9F, 0xB4, 0x43,
	0xE5, 0xA0, 0x8D, 0x43, 0xE5, 0xA0, 0xB1, 0x43,
	0xE5, 0xA0, 0xB2, 0x43, 0xE5, 0xA1, 0x80, 0x43,
	0xE5, 0xA1, 0x9A, 0x43, 0xE5, 0xA1, 0x9E, 0x43,
	0xE5, 0xA2, 0xA8, 0x43, 0xE5, 0xA2, 0xAC, 0x43,
	// Bytes 9c0 - 9ff
	0xE5, 0xA2, 0xB3, 0x43, 0xE5, 0xA3, 0x98, 0x43,
	0xE5, 0xA3, 0x9F, 0x43, 0xE5, 0xA3, 0xAB, 0x43,
	0xE5, 0xA3, 0xAE, 0x43, 0xE5, 0xA3, 0xB0, 0x43,
	0xE5, 0xA3, 0xB2, 0x43, 0xE5, 0xA3, 0xB7, 0x43,
	0xE5, 0xA4, 0x82, 0x43, 0xE5, 0xA4, 0x86, 0x43,
	0xE5, 0xA4, 0x8A, 0x43, 0xE5, 0xA4, 0x95, 0x43,
	0xE5, 0xA4, 0x9A, 0x43, 0xE5, 0xA4, 0x9C, 0x43,
	0xE5, 0xA4, 0xA2, 0x43, 0xE5, 0xA4, 0xA7, 0x43,
	// Bytes a00 - a3f
	0xE5, 0xA4, 0xA9, 0x43, 0xE5, 0xA5, 0x84, 0x43,
	0xE5, 0xA5, 0x88, 0x43, 0xE5, 0xA5, 0x91, 0x43,
	0xE5, 0xA5, 0x94, 0x43, 0xE5, 0xA5, 0xA2, 0x43,
	0xE5, 0xA5, 0xB3, 0x43, 0xE5, 0xA7, 0x98, 0x43,
	0xE5, 0xA7, 0xAC, 0x43, 0xE5, 0xA8, 0x9B, 0x43,
	0xE5, 0xA8, 0xA7, 0x43, 0xE5, 0xA9, 0xA2, 0x43,
	0xE5, 0xA9, 0xA6, 0x43, 0xE5, 0xAA, 0xB5, 0x43,
	0xE5, 0xAC, 0x88, 0x43, 0xE5, 0xAC, 0xA8, 0x43,
	// Bytes a40 - a7f
	0xE5, 0xAC, 0xBE, 0x43, 0xE5, 0xAD, 0x90, 0x43,
	0xE5, 0xAD, 0x97, 0x43, 0xE5, 0xAD, 0xA6, 0x43,
	0xE5, 0xAE, 0x80, 0x43, 0xE5, 0xAE, 0x85, 0x43,
	0xE5, 0xAE, 0x97, 0x43, 0xE5, 0xAF, 0x83, 0x43,
	0xE5, 0xAF, 0x98, 0x43, 0xE5, 0xAF, 0xA7, 0x43,
	0xE5, 0xAF, 0xAE, 0x43, 0xE5, 0xAF, 0xB3, 0x43,
	0xE5, 0xAF, 0xB8, 0x43, 0xE5, 0xAF, 0xBF, 0x43,
	0xE5, 0xB0, 0x86, 0x43, 0xE5, 0xB0, 0x8F, 0x43,
	// Bytes a80 - abf
	0xE5, 0xB0, 0xA2, 0x43, 0xE5, 0xB0, 0xB8, 0x43,
	0xE5, 0xB0, 0xBF, 0x43, 0xE5, 0xB1, 0xA0, 0x43,
	0xE5, 0xB1, 0xA2, 0x43, 0xE5, 0xB1, 0xA4, 0x43,
	0xE5, 0xB1, 0xA5, 0x43, 0xE5, 0xB1, 0xAE, 0x43,
	0xE5, 0xB1, 0xB1, 0x43, 0xE5, 0xB2, 0x8D, 0x43,
	0xE5, 0xB3, 0x80, 0x43, 0xE5, 0xB4, 0x99, 0x43,
	0xE5, 0xB5, 0x83, 0x43, 0xE5, 0xB5, 0x90, 0x43,
	0xE5, 0xB5, 0xAB, 0x43, 0xE5, 0xB5, 0xAE, 0x43,
	// Bytes ac0 - aff
	0xE5, 0xB5, 0xBC, 0x43, 0xE5, 0xB6, 0xB2, 0x43,
	0xE5, 0xB6, 0xBA, 0x43, 0xE5, 0xB7, 0x9B, 0x43,
	0xE5, 0xB7, 0xA1, 0x43, 0xE5, 0xB7, 0xA2, 0x43,
	0xE5, 0xB7, 0xA5, 0x43, 0xE5, 0xB7, 0xA6, 0x43,
	0xE5, 0xB7, 0xB1, 0x43, 0xE5, 0xB7, 0xBD, 0x43,
	0xE5, 0xB7, 0xBE, 0x43, 0xE5, 0xB8, 0xA8, 0x43,
	0xE5, 0xB8, 0xBD, 0x43, 0xE5, 0xB9, 0xA9, 0x43,
	0xE5, 0xB9, 0xB2, 0x43, 0xE5, 0xB9, 0xB4, 0x43,
	// Bytes b00 - b3f
	0xE5, 0xB9, 0xBA, 0x43, 0xE5, 0xB9, 0xBC, 0x43,
	0xE5, 0xB9, 0xBF, 0x43, 0xE5, 0xBA, 0xA6, 0x43,
	0xE5, 0xBA, 0xB0, 0x43, 0xE5, 0xBA, 0xB3, 0x43,
	0xE5, 0xBA, 0xB6, 0x43, 0xE5, 0xBB, 0x89, 0x43,
	0xE5, 0xBB, 0x8A, 0x43, 0xE5, 0xBB, 0x92, 0x43,
	0xE5, 0xBB, 0x93, 0x43, 0xE5, 0xBB, 0x99, 0x43,
	0xE5, 0xBB, 0xAC, 0x43, 0xE5, 0xBB, 0xB4, 0x43,
	0xE5, 0xBB, 0xBE, 0x43, 0xE5, 0xBC, 0x84, 0x43,
	// Bytes b40 - b7f
	0xE5, 0xBC, 0x8B, 0x43, 0xE5, 0xBC, 0x93, 0x43,
	0xE5, 0xBC, 0xA2, 0x43, 0xE5, 0xBD, 0x90, 0x43,
	0xE5, 0xBD, 0x93, 0x43, 0xE5, 0xBD, 0xA1, 0x43,
	0xE5, 0xBD, 0xA2, 0x43, 0xE5, 0xBD, 0xA9, 0x43,
	0xE5, 0xBD, 0xAB, 0x43, 0xE5, 0xBD, 0xB3, 0x43,
	0xE5, 0xBE, 0x8B, 0x43, 0xE5, 0xBE, 0x8C, 0x43,
	0xE5, 0xBE, 0x97, 0x43, 0xE5, 0xBE, 0x9A, 0x43,
	0xE5, 0xBE, 0xA9, 0x43, 0xE5, 0xBE, 0xAD, 0x43,
	// Bytes b80 - bbf
	0xE5, 0xBF, 0x83, 0x43, 0xE5, 0xBF, 0x8D, 0x43,
	0xE5, 0xBF, 0x97, 0x43, 0xE5, 0xBF, 0xB5, 0x43,
	0xE5, 0xBF, 0xB9, 0x43, 0xE6, 0x80, 0x92, 0x43,
	0xE6, 0x80, 0x9C, 0x43, 0xE6, 0x81, 0xB5, 0x43,
	0xE6, 0x82, 0x81, 0x43, 0xE6, 0x82, 0x94, 0x43,
	0xE6, 0x83, 0x87, 0x43, 0xE6, 0x83, 0x98, 0x43,
	0xE6, 0x83, 0xA1, 0x43, 0xE6, 0x84, 0x88, 0x43,
	0xE6, 0x85, 0x84, 0x43, 0xE6, 0x85, 0x88, 0x43,
	// Bytes bc0 - bff
	0xE6, 0x85, 0x8C, 0x43, 0xE6, 0x85, 0x8E, 0x43,
	0xE6, 0x85, 0xA0, 0x43, 0xE6, 0x85, 0xA8, 0x43,
	0xE6, 0x85, 0xBA, 0x43, 0xE6, 0x86, 0x8E, 0x43,
	0xE6, 0x86, 0x90, 0x43, 0xE6, 0x86, 0xA4, 0x43,
	0xE6, 0x86, 0xAF, 0x43, 0xE6, 0x86, 0xB2, 0x43,
	0xE6, 0x87, 0x9E, 0x43, 0xE6, 0x87, 0xB2, 0x43,
	0xE6, 0x87, 0xB6, 0x43, 0xE6, 0x88, 0x80, 0x43,
	0xE6, 0x88, 0x88, 0x43, 0xE6, 0x88, 0x90, 0x43,
	// Bytes c00 - c3f
	0xE6, 0x88, 0x9B, 0x43, 0xE6, 0x88, 0xAE, 0x43,
	0xE6, 0x88, 0xB4, 0x43, 0xE6, 0x88, 0xB6, 0x43,
	0xE6, 0x89, 0x8B, 0x43, 0xE6, 0x89, 0x93, 0x43,
	0xE6, 0x89, 0x9D, 0x43, 0xE6, 0x8A, 0x95, 0x43,
	0xE6, 0x8A, 0xB1, 0x43, 0xE6, 0x8B, 0x89, 0x43,
	0xE6, 0x8B, 0x8F, 0x43, 0xE6, 0x8B, 0x93, 0x43,
	0xE6, 0x8B, 0x94, 0x43, 0xE6, 0x8B, 0xBC, 0x43,
	0xE6, 0x8B, 0xBE, 0x43, 0xE6, 0x8C, 0x87, 0x43,
	// Bytes c40 - c7f
	0xE6, 0x8C, 0xBD, 0x43, 0xE6, 0x8D, 0x90, 0x43,
	0xE6, 0x8D, 0x95, 0x43, 0xE6, 0x8D, 0xA8, 0x43,
	0xE6, 0x8D, 0xBB, 0x43, 0xE6, 0x8E, 0x83, 0x43,
	0xE6, 0x8E, 0xA0, 0x43, 0xE6, 0x8E, 0xA9, 0x43,
	0xE6, 0x8F, 0x84, 0x43, 0xE6, 0x8F, 0x85, 0x43,
	0xE6, 0x8F, 0xA4, 0x43, 0xE6, 0x90, 0x9C, 0x43,
	0xE6, 0x90, 0xA2, 0x43, 0xE6, 0x91, 0x92, 0x43,
	0xE6, 0x91, 0xA9, 0x43, 0xE6, 0x91, 0xB7, 0x43,
	// Bytes c80 - cbf
	0xE6, 0x91, 0xBE, 0x43, 0xE6, 0x92, 0x9A, 0x43,
	0xE6, 0x92, 0x9D, 0x43, 0xE6, 0x93, 0x84, 0x43,
	0xE6, 0x94, 0xAF, 0x43, 0xE6, 0x94, 0xB4, 0x43,
	0xE6, 0x95, 0x8F, 0x43, 0xE6, 0x95, 0x96, 0x43,
	0xE6, 0x95, 0xAC, 0x43, 0xE6, 0x95, 0xB8, 0x43,
	0xE6, 0x96, 0x87, 0x43, 0xE6, 0x96, 0x97, 0x43,
	0xE6, 0x96, 0x99, 0x43, 0xE6, 0x96, 0xA4, 0x43,
	0xE6, 0x96, 0xB0, 0x43, 0xE6, 0x96, 0xB9, 0x43,
	// Bytes cc0 - cff
	0xE6, 0x97, 0x85, 0x43, 0xE6, 0x97, 0xA0, 0x43,
	0xE6, 0x97, 0xA2, 0x43, 0xE6, 0x97, 0xA3, 0x43,
	0xE6, 0x97, 0xA5, 0x43, 0xE6, 0x98, 0x93, 0x43,
	0xE6, 0x98, 0xA0, 0x43, 0xE6, 0x99, 0x89, 0x43,
	0xE6, 0x99, 0xB4, 0x43, 0xE6, 0x9A, 0x88, 0x43,
	0xE6, 0x9A, 0x91, 0x43, 0xE6, 0x9A, 0x9C, 0x43,
	0xE6, 0x9A, 0xB4, 0x43, 0xE6, 0x9B, 0x86, 0x43,
	0xE6, 0x9B, 0xB0, 0x43, 0xE6, 0x9B, 0xB4, 0x43,
	// Bytes d00 - d3f
	0xE6, 0x9B, 0xB8, 0x43, 0xE6, 0x9C, 0x80, 0x43,
	0xE6, 0x9C, 0x88, 0x43, 0xE6, 0x9C, 0x89, 0x43,
	0xE6, 0x9C, 0x97, 0x43, 0xE6, 0x9C, 0x9B, 0x43,
	0xE6, 0x9C, 0xA1, 0x43, 0xE6, 0x9C, 0xA8, 0x43,
	0xE6, 0x9D, 0x8E, 0x43, 0xE6, 0x9D, 0x93, 0x43,
	0xE6, 0x9D, 0x96, 0x43, 0xE6, 0x9D, 0x9E, 0x43,
	0xE6, 0x9D, 0xBB, 0x43, 0xE6, 0x9E, 0x85, 0x43,
	0xE6, 0x9E, 0x97, 0x43, 0xE6, 0x9F, 0xB3, 0x43,
	// Bytes d40 - d7f
	0xE6, 0x9F, 0xBA, 0x43, 0xE6, 0xA0, 0x97, 0x43,
	0xE6, 0xA0, 0x9F, 0x43, 0xE6, 0xA0, 0xAA, 0x43,
	0xE6, 0xA1, 0x92, 0x43, 0xE6, 0xA2, 0x81, 0x43,
	0xE6, 0xA2, 0x85, 0x43, 0xE6, 0xA2, 0x8E, 0x43,
	0xE6, 0xA2, 0xA8, 0x43, 0xE6, 0xA4, 0x94, 0x43,
	0xE6, 0xA5, 0x82, 0x43, 0xE6, 0xA6, 0xA3, 0x43,
	0xE6, 0xA7, 0xAA, 0x43, 0xE6, 0xA8, 0x82, 0x43,
	0xE6, 0xA8, 0x93, 0x43, 0xE6, 0xAA, 0xA8, 0x43,
	// Bytes d80 - dbf
	0xE6, 0xAB, 0x93, 0x43, 0xE6, 0xAB, 0x9B, 0x43,
	0xE6, 0xAC, 0x84, 0x43, 0xE6, 0xAC, 0xA0, 0x43,
	0xE6, 0xAC, 0xA1, 0x43, 0xE6, 0xAD, 0x94, 0x43,
	0xE6, 0xAD, 0xA2, 0x43, 0xE6, 0xAD, 0xA3, 0x43,
	0xE6, 0xAD, 0xB2, 0x43, 0xE6, 0xAD, 0xB7, 0x43,
	0xE6, 0xAD, 0xB9, 0x43, 0xE6, 0xAE, 0x9F, 0x43,
	0xE6, 0xAE, 0xAE, 0x43, 0xE6, 0xAE, 0xB3, 0x43,
	0xE6, 0xAE, 0xBA, 0x43, 0xE6, 0xAE, 0xBB, 0x43,
	// Bytes dc0 - dff
	0xE6, 0xAF, 0x8B, 0x43, 0xE6, 0xAF, 0x8D, 0x43,
	0xE6, 0xAF, 0x94, 0x43, 0xE6, 0xAF, 0x9B, 0x43,
	0xE6, 0xB0, 0x8F, 0x43, 0xE6, 0xB0, 0x94, 0x43,
	0xE6, 0xB0, 0xB4, 0x43, 0xE6, 0xB1, 0x8E, 0x43,
	0xE6, 0xB1, 0xA7, 0x43, 0xE6, 0xB2, 0x88, 0x43,
	0xE6, 0xB2, 0xBF, 0x43, 0xE6, 0xB3, 0x8C, 0x43,
	0xE6, 0xB3, 0x8D, 0x43, 0xE6, 0xB3, 0xA5, 0x43,
	0xE6, 0xB3, 0xA8, 0x43, 0xE6, 0xB4, 0x96, 0x43,
	// Bytes e00 - e3f
	0xE6, 0xB4, 0x9B, 0x43, 0xE6, 0xB4, 0x9E, 0x43,
	0xE6, 0xB4, 0xB4, 0x43, 0xE6, 0xB4, 0xBE, 0x43,
	0xE6, 0xB5, 0x81, 0x43, 0xE6, 0xB5, 0xA9, 0x43,
	0xE6, 0xB5, 0xAA, 0x43, 0xE6, 0xB5, 0xB7, 0x43,
	0xE6, 0xB5, 0xB8, 0x43, 0xE6, 0xB6, 0x85, 0x43,
	0xE6, 0xB7, 0x8B, 0x43, 0xE6, 0xB7, 0x9A, 0x43,
	0xE6, 0xB7, 0xAA, 0x43, 0xE6, 0xB7, 0xB9, 0x43,
	0xE6, 0xB8, 0x9A, 0x43, 0xE6, 0xB8, 0xAF, 0x43,
	// Bytes e40 - e7f
	0xE6, 0xB9, 0xAE, 0x43, 0xE6, 0xBA, 0x80, 0x43,
	0xE6, 0xBA, 0x9C, 0x43, 0xE6, 0xBA, 0xBA, 0x43,
	0xE6, 0xBB, 0x87, 0x43, 0xE6, 0xBB, 0x8B, 0x43,
	0xE6, 0xBB, 0x91, 0x43, 0xE6, 0xBB, 0x9B, 0x43,
	0xE6, 0xBC, 0x8F, 0x43, 0xE6, 0xBC, 0x94, 0x43,
	0xE6, 0xBC, 0xA2, 0x43, 0xE6, 0xBC, 0xA3, 0x43,
	0xE6, 0xBD, 0xAE, 0x43, 0xE6, 0xBF, 0x86, 0x43,
	0xE6, 0xBF, 0xAB, 0x43, 0xE6, 0xBF, 0xBE, 0x43,
	// Bytes e80 - ebf
	0xE7, 0x80, 0x9B, 0x43, 0xE7, 0x80, 0x9E, 0x43,
	0xE7, 0x80, 0xB9, 0x43, 0xE7, 0x81, 0x8A, 0x43,
	0xE7, 0x81, 0xAB, 0x43, 0xE7, 0x81, 0xB0, 0x43,
	0xE7, 0x81, 0xB7, 0x43, 0xE7, 0x81, 0xBD, 0x43,
	0xE7, 0x82, 0x99, 0x43, 0xE7, 0x82, 0xAD, 0x43,
	0xE7, 0x83, 0x88, 0x43, 0xE7, 0x83, 0x99, 0x43,
	0xE7, 0x84, 0xA1, 0x43, 0xE7, 0x85, 0x85, 0x43,
	0xE7, 0x85, 0x89, 0x43, 0xE7, 0x85, 0xAE, 0x43,
	// Bytes ec0 - eff
	0xE7, 0x86, 0x9C, 0x43, 0xE7, 0x87, 0x8E, 0x43,
	0xE7, 0x87, 0x90, 0x43, 0xE7, 0x88, 0x90, 0x43,
	0xE7, 0x88, 0x9B, 0x43, 0xE7, 0x88, 0xA8, 0x43,
	0xE7, 0x88, 0xAA, 0x43, 0xE7, 0x88, 0xAB, 0x43,
	0xE7, 0x88, 0xB5, 0x43, 0xE7, 0x88, 0xB6, 0x43,
	0xE7, 0x88, 0xBB, 0x43, 0xE7, 0x88, 0xBF, 0x43,
	0xE7, 0x89, 0x87, 0x43, 0xE7, 0x89, 0x90, 0x43,
	0xE7, 0x89, 0x99, 0x43, 0xE7, 0x89, 0x9B, 0x43,
	// Bytes f00 - f3f
	0xE7, 0x89, 0xA2, 0x43, 0xE7, 0x89, 0xB9, 0x43,
	0xE7, 0x8A, 0x80, 0x43, 0xE7, 0x8A, 0x95, 0x43,
	0xE7, 0x8A, 0xAC, 0x43, 0xE7, 0x8A, 0xAF, 0x43,
	0xE7, 0x8B, 0x80, 0x43, 0xE7, 0x8B, 0xBC, 0x43,
	0xE7, 0x8C, 0xAA, 0x43, 0xE7, 0x8D, 0xB5, 0x43,
	0xE7, 0x8D, 0xBA, 0x43, 0xE7, 0x8E, 0x84, 0x43,
	0xE7, 0x8E, 0x87, 0x43, 0xE7, 0x8E, 0x89, 0x43,
	0xE7, 0x8E, 0x8B, 0x43, 0xE7, 0x8E, 0xA5, 0x43,
	// Bytes f40 - f7f
	0xE7, 0x8E, 0xB2, 0x43, 0xE7, 0x8F, 0x9E, 0x43,
	0xE7, 0x90, 0x86, 0x43, 0xE7, 0x90, 0x89, 0x43,
	0xE7, 0x90, 0xA2, 0x43, 0xE7, 0x91, 0x87, 0x43,
	0xE7, 0x91, 0x9C, 0x43, 0xE7, 0x91, 0xA9, 0x43,
	0xE7, 0x91, 0xB1, 0x43, 0xE7, 0x92, 0x85, 0x43,
	0xE7, 0x92, 0x89, 0x43, 0xE7, 0x92, 0x98, 0x43,
	0xE7, 0x93, 0x8A, 0x43, 0xE7, 0x93, 0x9C, 0x43,
	0xE7, 0x93, 0xA6, 0x43, 0xE7, 0x94, 0x86, 0x43,
	// Bytes f80 - fbf
	0xE7, 0x94, 0x98, 0x43, 0xE7, 0x94, 0x9F, 0x43,
	0xE7, 0x94, 0xA4, 0x43, 0xE7, 0x94, 0xA8, 0x43,
	0xE7, 0x94, 0xB0, 0x43, 0xE7, 0x94, 0xB2, 0x43,
	0xE7, 0x94, 0xB3, 0x43, 0xE7, 0x94, 0xB7, 0x43,
	0xE7, 0x94, 0xBB, 0x43, 0xE7, 0x94, 0xBE, 0x43,
	0xE7, 0x95, 0x99, 0x43, 0xE7, 0x95, 0xA5, 0x43,
	0xE7, 0x95, 0xB0, 0x43, 0xE7, 0x96, 0x8B, 0x43,
	0xE7, 0x96, 0x92, 0x43, 0xE7, 0x97, 0xA2, 0x43,
	// Bytes fc0 - fff
	0xE7, 0x98, 0x90, 0x43, 0xE7, 0x98, 0x9D, 0x43,
	0xE7, 0x98, 0x9F, 0x43, 0xE7, 0x99, 0x82, 0x43,
	0xE7, 0x99, 0xA9, 0x43, 0xE7, 0x99, 0xB6, 0x43,
	0xE7, 0x99, 0xBD, 0x43, 0xE7, 0x9A, 0xAE, 0x43,
	0xE7, 0x9A, 0xBF, 0x43, 0xE7, 0x9B, 0x8A, 0x43,
	0xE7, 0x9B, 0x9B, 0x43, 0xE7, 0x9B, 0xA3, 0x43,
	0xE7, 0x9B, 0xA7, 0x43, 0xE7, 0x9B, 0xAE, 0x43,
	0xE7, 0x9B, 0xB4, 0x43, 0xE7, 0x9C, 0x81, 0x43,
	// Bytes 1000 - 103f
	0xE7, 0x9C, 0x9E, 0x43, 0xE7, 0x9C, 0x9F, 0x43,
	0xE7, 0x9D, 0x80, 0x43, 0xE7, 0x9D, 0x8A, 0x43,
	0xE7, 0x9E, 0x8B, 0x43, 0xE7, 0x9E, 0xA7, 0x43,
	0xE7, 0x9F, 0x9B, 0x43, 0xE7, 0x9F, 0xA2, 0x43,
	0xE7, 0x9F, 0xB3, 0x43, 0xE7, 0xA1, 0x8E, 0x43,
	0xE7, 0xA1, 0xAB, 0x43, 0xE7, 0xA2, 0x8C, 0x43,
	0xE7, 0xA2, 0x91, 0x43, 0xE7, 0xA3, 0x8A, 0x43,
	0xE7, 0xA3, 0x8C, 0x43, 0xE7, 0xA3, 0xBB, 0x43,
	// Bytes 1040 - 107f
	0xE7, 0xA4, 0xAA, 0x43, 0xE7, 0xA4, 0xBA, 0x43,
	0xE7, 0xA4, 0xBC, 0x43, 0xE7, 0xA4, 0xBE, 0x43,
	0xE7, 0xA5, 0x88, 0x43, 0xE7, 0xA5, 0x89, 0x43,
	0xE7, 0xA5, 0x90, 0x43, 0xE7, 0xA5, 0x96, 0x43,
	0xE7, 0xA5, 0x9D, 0x43, 0xE7, 0xA5, 0x9E, 0x43,
	0xE7, 0xA5, 0xA5, 0x43, 0xE7, 0xA5, 0xBF, 0x43,
	0xE7, 0xA6, 0x81, 0x43, 0xE7, 0xA6, 0x8D, 0x43,
	0xE7, 0xA6, 0x8E, 0x43, 0xE7, 0xA6, 0x8F, 0x43,
	// Bytes 1080 - 10bf
	0xE7, 0xA6, 0xAE, 0x43, 0xE7, 0xA6, 0xB8, 0x43,
	0xE7, 0xA6, 0xBE, 0x43, 0xE7, 0xA7, 0x8A, 0x43,
	0xE7, 0xA7, 0x98, 0x43, 0xE7, 0xA7, 0xAB, 0x43,
	0xE7, 0xA8, 0x9C, 0x43, 0xE7, 0xA9, 0x80, 0x43,
	0xE7, 0xA9, 0x8A, 0x43, 0xE7, 0xA9, 0x8F, 0x43,
	0xE7, 0xA9, 0xB4, 0x43, 0xE7, 0xA9, 0xBA, 0x43,
	0xE7, 0xAA, 0x81, 0x43, 0xE7, 0xAA, 0xB1, 0x43,
	0xE7, 0xAB, 0x8B, 0x43, 0xE7, 0xAB, 0xAE, 0x43,
	// Bytes 10c0 - 10ff
	0xE7, 0xAB, 0xB9, 0x43, 0xE7, 0xAC, 0xA0, 0x43,
	0xE7, 0xAE, 0x8F, 0x43, 0xE7, 0xAF, 0x80, 0x43,
	0xE7, 0xAF, 0x86, 0x43, 0xE7, 0xAF, 0x89, 0x43,
	0xE7, 0xB0, 0xBE, 0x43, 0xE7, 0xB1, 0xA0, 0x43,
	0xE7, 0xB1, 0xB3, 0x43, 0xE7, 0xB1, 0xBB, 0x43,
	0xE7, 0xB2, 0x92, 0x43, 0xE7, 0xB2, 0xBE, 0x43,
	0xE7, 0xB3, 0x92, 0x43, 0xE7, 0xB3, 0x96, 0x43,
	0xE7, 0xB3, 0xA3, 0x43, 0xE7, 0xB3, 0xA7, 0x43,
	// Bytes 1100 - 113f
	0xE7, 0xB3, 0xA8, 0x43, 0xE7, 0xB3, 0xB8, 0x43,
	0xE7, 0xB4, 0x80, 0x43, 0xE7, 0xB4, 0x90, 0x43,
	0xE7, 0xB4, 0xA2, 0x43, 0xE7, 0xB4, 0xAF, 0x43,
	0xE7, 0xB5, 0x82, 0x43, 0xE7, 0xB5, 0x9B, 0x43,
	0xE7, 0xB5, 0xA3, 0x43, 0xE7, 0xB6, 0xA0, 0x43,
	0xE7, 0xB6, 0xBE, 0x43, 0xE7, 0xB7, 0x87, 0x43,
	0xE7, 0xB7, 0xB4, 0x43, 0xE7, 0xB8, 0x82, 0x43,
	0xE7, 0xB8, 0x89, 0x43, 0xE7, 0xB8, 0xB7, 0x43,
	// Bytes 1140 - 117f
	0xE7, 0xB9, 0x81, 0x43, 0xE7, 0xB9, 0x85, 0x43,
	0xE7, 0xBC, 0xB6, 0x43, 0xE7, 0xBC, 0xBE, 0x43,
	0xE7, 0xBD, 0x91, 0x43, 0xE7, 0xBD, 0xB2, 0x43,
	0xE7, 0xBD, 0xB9, 0x43, 0xE7, 0xBD, 0xBA, 0x43,
	0xE7, 0xBE, 0x85, 0x43, 0xE7, 0xBE, 0x8A, 0x43,
	0xE7, 0xBE, 0x95, 0x43, 0xE7, 0xBE, 0x9A, 0x43,
	0xE7, 0xBE, 0xBD, 0x43, 0xE7, 0xBF, 0xBA, 0x43,
	0xE8, 0x80, 0x81, 0x43, 0xE8, 0x80, 0x85, 0x43,
	// Bytes 1180 - 11bf
	0xE8, 0x80, 0x8C, 0x43, 0xE8, 0x80, 0x92, 0x43,
	0xE8, 0x80, 0xB3, 0x43, 0xE8, 0x81, 0x86, 0x43,
	0xE8, 0x81, 0xA0, 0x43, 0xE8, 0x81, 0xAF, 0x43,
	0xE8, 0x81, 0xB0, 0x43, 0xE8, 0x81, 0xBE, 0x43,
	0xE8, 0x81, 0xBF, 0x43, 0xE8, 0x82, 0x89, 0x43,
	0xE8, 0x82, 0x8B, 0x43, 0xE8, 0x82, 0xAD, 0x43,
	0xE8, 0x82, 0xB2, 0x43, 0xE8, 0x84, 0x83, 0x43,
	0xE8, 0x84, 0xBE, 0x43, 0xE8, 0x87, 0x98, 0x43,
	// Bytes 11c0 - 11ff
	0xE8, 0x87, 0xA3, 0x43, 0xE8, 0x87, 0xA8, 0x43,
	0xE8, 0x87, 0xAA, 0x43, 0xE8, 0x87, 0xAD, 0x43,
	0xE8, 0x87, 0xB3, 0x43, 0xE8, 0x87, 0xBC, 0x43,
	0xE8, 0x88, 0x81, 0x43, 0xE8, 0x88, 0x84, 0x43,
	0xE8, 0x88, 0x8C, 0x43, 0xE8, 0x88, 0x98, 0x43,
	0xE8, 0x88, 0x9B, 0x43, 0xE8, 0x88, 0x9F, 0x43,
	0xE8, 0x89, 0xAE, 0x43, 0xE8, 0x89, 0xAF, 0x43,
	0xE8, 0x89, 0xB2, 0x43, 0xE8, 0x89, 0xB8, 0x43,
	// Bytes 1200 - 123f
	0xE8, 0x89, 0xB9, 0x43, 0xE8, 0x8A, 0x8B, 0x43,
	0xE8, 0x8A, 0x91, 0x43, 0xE8, 0x8A, 0x9D, 0x43,
	0xE8, 0x8A, 0xB1, 0x43, 0xE8, 0x8A, 0xB3, 0x43,
	0xE8, 0x8A, 0xBD, 0x43, 0xE8, 0x8B, 0xA5, 0x43,
	0xE8, 0x8B, 0xA6, 0x43, 0xE8, 0x8C, 0x9D, 0x43,
	0xE8, 0x8C, 0xA3, 0x43, 0xE8, 0x8C, 0xB6, 0x43,
	0xE8, 0x8D, 0x92, 0x43, 0xE8, 0x8D, 0x93, 0x43,
	0xE8, 0x8D, 0xA3, 0x43, 0xE8, 0x8E, 0xAD, 0x43,
	// Bytes 1240 - 127f
	0xE8, 0x8E, 0xBD, 0x43, 0xE8, 0x8F, 0x89, 0x43,
	0xE8, 0x8F, 0x8A, 0x43, 0xE8, 0x8F, 0x8C, 0x43,
	0xE8, 0x8F, 0x9C, 0x43, 0xE8, 0x8F, 0xA7, 0x43,
	0xE8, 0x8F, 0xAF, 0x43, 0xE8, 0x8F, 0xB1, 0x43,
	0xE8, 0x90, 0xBD, 0x43, 0xE8, 0x91, 0x89, 0x43,
	0xE8, 0x91, 0x97, 0x43, 0xE8, 0x93, 0xAE, 0x43,
	0xE8, 0x93, 0xB1, 0x43, 0xE8, 0x93, 0xB3, 0x43,
	0xE8, 0x93, 0xBC, 0x43, 0xE8, 0x94, 0x96, 0x43,
	// Bytes 1280 - 12bf
	0xE8, 0x95, 0xA4, 0x43, 0xE8, 0x97, 0x8D, 0x43,
	0xE8, 0x97, 0xBA, 0x43, 0xE8, 0x98, 0x86, 0x43,
	0xE8, 0x98, 0x92, 0x43, 0xE8, 0x98, 0xAD, 0x43,
	0xE8, 0x98, 0xBF, 0x43, 0xE8, 0x99, 0x8D, 0x43,
	0xE8, 0x99, 0x90, 0x43, 0xE8, 0x99, 0x9C, 0x43,
	0xE8, 0x99, 0xA7, 0x43, 0xE8, 0x99, 0xA9, 0x43,
	0xE8, 0x99, 0xAB, 0x43, 0xE8, 0x9A, 0x88, 0x43,
	0xE8, 0x9A, 0xA9, 0x43, 0xE8, 0x9B, 0xA2, 0x43,
	// Bytes 12c0 - 12ff
	0xE8, 0x9C, 0x8E, 0x43, 0xE8, 0x9C, 0xA8, 0x43,
	0xE8, 0x9D, 0xAB, 0x43, 0xE8, 0x9D, 0xB9, 0x43,
	0xE8, 0x9E, 0x86, 0x43, 0xE8, 0x9E, 0xBA, 0x43,
	0xE8, 0x9F, 0xA1, 0x43, 0xE8, 0xA0, 0x81, 0x43,
	0xE8, 0xA0, 0x9F, 0x43, 0xE8, 0xA1, 0x80, 0x43,
	0xE8, 0xA1, 0x8C, 0x43, 0xE8, 0xA1, 0xA0, 0x43,
	0xE8, 0xA1, 0xA3, 0x43, 0xE8, 0xA3, 0x82, 0x43,
	0xE8, 0xA3, 0x8F, 0x43, 0xE8, 0xA3, 0x97, 0x43,
	// Bytes 1300 - 133f
	0xE8, 0xA3, 0x9E, 0x43, 0xE8, 0xA3, 0xA1, 0x43,
	0xE8, 0xA3, 0xB8, 0x43, 0xE8, 0xA3, 0xBA, 0x43,
	0xE8, 0xA4, 0x90, 0x43, 0xE8, 0xA5, 0x81, 0x43,
	0xE8, 0xA5, 0xA4, 0x43, 0xE8, 0xA5, 0xBE, 0x43,
	0xE8, 0xA6, 0x86, 0x43, 0xE8, 0xA6, 0x8B, 0x43,
	0xE8, 0xA6, 0x96, 0x43, 0xE8, 0xA7, 0x92, 0x43,
	0xE8, 0xA7, 0xA3, 0x43, 0xE8, 0xA8, 0x80, 0x43,
	0xE8, 0xAA, 0xA0, 0x43, 0xE8, 0xAA, 0xAA, 0x43,
	// Bytes 1340 - 137f
	0xE8, 0xAA, 0xBF, 0x43, 0xE8, 0xAB, 0x8B, 0x43,
	0xE8, 0xAB, 0x92, 0x43, 0xE8, 0xAB, 0x96, 0x43,
	0xE8, 0xAB, 0xAD, 0x43, 0xE8, 0xAB, 0xB8, 0x43,
	0xE8, 0xAB, 0xBE, 0x43, 0xE8, 0xAC, 0x81, 0x43,
	0xE8, 0xAC, 0xB9, 0x43, 0xE8, 0xAD, 0x98, 0x43,
	0xE8, 0xAE, 0x80, 0x43, 0xE8, 0xAE, 0x8A, 0x43,
	0xE8, 0xB0, 0xB7, 0x43, 0xE8, 0xB1, 0x86, 0x43,
	0xE8, 0xB1, 0x88, 0x43, 0xE8, 0xB1, 0x95, 0x43,
	// Bytes 1380 - 13bf
	0xE8, 0xB1, 0xB8, 0x43, 0xE8, 0xB2, 0x9D, 0x43,
	0xE8, 0xB2, 0xA1, 0x43, 0xE8, 0xB2, 0xA9, 0x43,
	0xE8, 0xB2, 0xAB, 0x43, 0xE8, 0xB3, 0x81, 0x43,
	0xE8, 0xB3, 0x82, 0x43, 0xE8, 0xB3, 0x87, 0x43,
	0xE8, 0xB3, 0x88, 0x43, 0xE8, 0xB3, 0x93, 0x43,
	0xE8, 0xB4, 0x88, 0x43, 0xE8, 0xB4, 0x9B, 0x43,
	0xE8, 0xB5, 0xA4, 0x43, 0xE8, 0xB5, 0xB0, 0x43,
	0xE8, 0xB5, 0xB7, 0x43, 0xE8, 0xB6, 0xB3, 0x43,
	// Bytes 13c0 - 13ff
	0xE8, 0xB6, 0xBC, 0x43, 0xE8, 0xB7, 0x8B, 0x43,
	0xE8, 0xB7, 0xAF, 0x43, 0xE8, 0xB7, 0xB0, 0x43,
	0xE8, 0xBA, 0xAB, 0x43, 0xE8, 0xBB, 0x8A, 0x43,
	0xE8, 0xBB, 0x94, 0x43, 0xE8, 0xBC, 0xA6, 0x43,
	0xE8, 0xBC, 0xAA, 0x43, 0xE8, 0xBC, 0xB8, 0x43,
	0xE8, 0xBC, 0xBB, 0x43, 0xE8, 0xBD, 0xA2, 0x43,
	0xE8, 0xBE, 0x9B, 0x43, 0xE8, 0xBE, 0x9E, 0x43,
	0xE8, 0xBE, 0xB0, 0x43, 0xE8, 0xBE, 0xB5, 0x43,
	// Bytes 1400 - 143f
	0xE8, 0xBE, 0xB6, 0x43, 0xE9, 0x80, 0xA3, 0x43,
	0xE9, 0x80, 0xB8, 0x43, 0xE9, 0x81, 0x8A, 0x43,
	0xE9, 0x81, 0xA9, 0x43, 0xE9, 0x81, 0xB2, 0x43,
	0xE9, 0x81, 0xBC, 0x43, 0xE9, 0x82, 0x8F, 0x43,
	0xE9, 0x82, 0x91, 0x43, 0xE9, 0x82, 0x94, 0x43,
	0xE9, 0x83, 0x8E, 0x43, 0xE9, 0x83, 0x9E, 0x43,
	0xE9, 0x83, 0xB1, 0x43, 0xE9, 0x83, 0xBD, 0x43,
	0xE9, 0x84, 0x91, 0x43, 0xE9, 0x84, 0x9B, 0x43,
	// Bytes 1440 - 147f
	0xE9, 0x85, 0x89, 0x43, 0xE9, 0x85, 0x8D, 0x43,
	0xE9, 0x85, 0xAA, 0x43, 0xE9, 0x86, 0x99, 0x43,
	0xE9, 0x86, 0xB4, 0x43, 0xE9, 0x87, 0x86, 0x43,
	0xE9, 0x87, 0x8C, 0x43, 0xE9, 0x87, 0x8F, 0x43,
	0xE9, 0x87, 0x91, 0x43, 0xE9, 0x88, 0xB4, 0x43,
	0xE9, 0x88, 0xB8, 0x43, 0xE9, 0x89, 0xB6, 0x43,
	0xE9, 0x89, 0xBC, 0x43, 0xE9, 0x8B, 0x97, 0x43,
	0xE9, 0x8B, 0x98, 0x43, 0xE9, 0x8C, 0x84, 0x43,
	// Bytes 1480 - 14bf
	0xE9, 0x8D, 0x8A, 0x43, 0xE9, 0x8F, 0xB9, 0x43,
	0xE9, 0x90, 0x95, 0x43, 0xE9, 0x95, 0xB7, 0x43,
	0xE9, 0x96, 0x80, 0x43, 0xE9, 0x96, 0x8B, 0x43,
	0xE9, 0x96, 0xAD, 0x43, 0xE9, 0x96, 0xB7, 0x43,
	0xE9, 0x98, 0x9C, 0x43, 0xE9, 0x98, 0xAE, 0x43,
	0xE9, 0x99, 0x8B, 0x43, 0xE9, 0x99, 0x8D, 0x43,
	0xE9, 0x99, 0xB5, 0x43, 0xE9, 0x99, 0xB8, 0x43,
	0xE9, 0x99, 0xBC, 0x43, 0xE9, 0x9A, 0x86, 0x43,
	// Bytes 14c0 - 14ff
	0xE9, 0x9A, 0xA3, 0x43, 0xE9, 0x9A, 0xB6, 0x43,
	0xE9, 0x9A, 0xB7, 0x43, 0xE9, 0x9A, 0xB8, 0x43,
	0xE9, 0x9A, 0xB9, 0x43, 0xE9, 0x9B, 0x83, 0x43,
	0xE9, 0x9B, 0xA2, 0x43, 0xE9, 0x9B, 0xA3, 0x43,
	0xE9, 0x9B, 0xA8, 0x43, 0xE9, 0x9B, 0xB6, 0x43,
	0xE9, 0x9B, 0xB7, 0x43, 0xE9, 0x9C, 0xA3, 0x43,
	0xE9, 0x9C, 0xB2, 0x43, 0xE9, 0x9D, 0x88, 0x43,
	0xE9, 0x9D, 0x91, 0x43, 0xE9, 0x9D, 0x96, 0x43,
	// Bytes 1500 - 153f
	0xE9, 0x9D, 0x9E, 0x43, 0xE9, 0x9D, 0xA2, 0x43,
	0xE9, 0x9D, 0xA9, 0x43, 0xE9, 0x9F, 0x8B, 0x43,
	0xE9, 0x9F, 0x9B, 0x43, 0xE9, 0x9F, 0xA0, 0x43,
	0xE9, 0x9F, 0xAD, 0x43, 0xE9, 0x9F, 0xB3, 0x43,
	0xE9, 0x9F, 0xBF, 0x43, 0xE9, 0xA0, 0x81, 0x43,
	0xE9, 0xA0, 0x85, 0x43, 0xE9, 0xA0, 0x8B, 0x43,
	0xE9, 0xA0, 0x98, 0x43, 0xE9, 0xA0, 0xA9, 0x43,
	0xE9, 0xA0, 0xBB, 0x43, 0xE9, 0xA1, 0x9E, 0x43,
	// Bytes 1540 - 157f
	0xE9, 0xA2, 0xA8, 0x43, 0xE9, 0xA3, 0x9B, 0x43,
	0xE9, 0xA3, 0x9F, 0x43, 0xE9, 0xA3, 0xA2, 0x43,
	0xE9, 0xA3, 0xAF, 0x43, 0xE9, 0xA3, 0xBC, 0x43,
	0xE9, 0xA4, 0xA8, 0x43, 0xE9, 0xA4, 0xA9, 0x43,
	0xE9, 0xA6, 0x96, 0x43, 0xE9, 0xA6, 0x99, 0x43,
	0xE9, 0xA6, 0xA7, 0x43, 0xE9, 0xA6, 0xAC, 0x43,
	0xE9, 0xA7, 0x82, 0x43, 0xE9, 0xA7, 0xB1, 0x43,
	0xE9, 0xA7, 0xBE, 0x43, 0xE9, 0xA9, 0xAA, 0x43,
	// Bytes 1580 - 15bf
	0xE9, 0xAA, 0xA8, 0x43, 0xE9, 0xAB, 0x98, 0x43,
	0xE9, 0xAB, 0x9F, 0x43, 0xE9, 0xAC, 0x92, 0x43,
	0xE9, 0xAC, 0xA5, 0x43, 0xE9, 0xAC, 0xAF, 0x43,
	0xE9, 0xAC, 0xB2, 0x43, 0xE9, 0xAC, 0xBC, 0x43,
	0xE9, 0xAD, 0x9A, 0x43, 0xE9, 0xAD, 0xAF, 0x43,
	0xE9, 0xB1, 0x80, 0x43, 0xE9, 0xB1, 0x97, 0x43,
	0xE9, 0xB3, 0xA5, 0x43, 0xE9, 0xB3, 0xBD, 0x43,
	0xE9, 0xB5, 0xA7, 0x43, 0xE9, 0xB6, 0xB4, 0x43,
	// Bytes 15c0 - 15ff
	0xE9, 0xB7, 0xBA, 0x43, 0xE9, 0xB8, 0x9E, 0x43,
	0xE9, 0xB9, 0xB5, 0x43, 0xE9, 0xB9, 0xBF, 0x43,
	0xE9, 0xBA, 0x97, 0x43, 0xE9, 0xBA, 0x9F, 0x43,
	0xE9, 0xBA, 0xA5, 0x43, 0xE9, 0xBA, 0xBB, 0x43,
	0xE9, 0xBB, 0x83, 0x43, 0xE9, 0xBB, 0x8D, 0x43,
	0xE9, 0xBB, 0x8E, 0x43, 0xE9, 0xBB, 0x91, 0x43,
	0xE9, 0xBB, 0xB9, 0x43, 0xE9, 0xBB, 0xBD, 0x43,
	0xE9, 0xBB, 0xBE, 0x43, 0xE9, 0xBC, 0x85, 0x43,
	// Bytes 1600 - 163f
	0xE9, 0xBC, 0x8E, 0x43, 0xE9, 0xBC, 0x8F, 0x43,
	0xE9, 0xBC, 0x93, 0x43, 0xE9, 0xBC, 0x96, 0x43,
	0xE9, 0xBC, 0xA0, 0x43, 0xE9, 0xBC, 0xBB, 0x43,
	0xE9, 0xBD, 0x83, 0x43, 0xE9, 0xBD, 0x8A, 0x43,
	0xE9, 0xBD, 0x92, 0x43, 0xE9, 0xBE, 0x8D, 0x43,
	0xE9, 0xBE, 0x8E, 0x43, 0xE9, 0xBE, 0x9C, 0x43,
	0xE9, 0xBE, 0x9F, 0x43, 0xE9, 0xBE, 0xA0, 0x43,
	0xEA, 0x9C, 0xA7, 0x43, 0xEA, 0x9D, 0xAF, 0x43,
	// Bytes 1640 - 167f
	0xEA, 0xAC, 0xB7, 0x43, 0xEA, 0xAD, 0x92, 0x44,
	0xF0, 0xA0, 0x84, 0xA2, 0x44, 0xF0, 0xA0, 0x94,
	0x9C, 0x44, 0xF0, 0xA0, 0x94, 0xA5, 0x44, 0xF0,
	0xA0, 0x95, 0x8B, 0x44, 0xF0, 0xA0, 0x98, 0xBA,
	0x44, 0xF0, 0xA0, 0xA0, 0x84, 0x44, 0xF0, 0xA0,
	0xA3, 0x9E, 0x44, 0xF0, 0xA0, 0xA8, 0xAC, 0x44,
	0xF0, 0xA0, 0xAD, 0xA3, 0x44, 0xF0, 0xA1, 0x93,
	0xA4, 0x44, 0xF0, 0xA1, 0x9A, 0xA8, 0x44, 0xF0,
	// Bytes 1680 - 16bf
	0xA1, 0x9B, 0xAA, 0x44, 0xF0, 0xA1, 0xA7, 0x88,
	0x44, 0xF0, 0xA1, 0xAC, 0x98, 0x44, 0xF0, 0xA1,
	0xB4, 0x8B, 0x44, 0xF0, 0xA1, 0xB7, 0xA4, 0x44,
	0xF0, 0xA1, 0xB7, 0xA6, 0x44, 0xF0, 0xA2, 0x86,
	0x83, 0x44, 0xF0, 0xA2, 0x86, 0x9F, 0x44, 0xF0,
	0xA2, 0x8C, 0xB1, 0x44, 0xF0, 0xA2, 0x9B, 0x94,
	0x44, 0xF0, 0xA2, 0xA1, 0x84, 0x44, 0xF0, 0xA2,
	0xA1, 0x8A, 0x44, 0xF0, 0xA2, 0xAC, 0x8C, 0x44,
	// Bytes 16c0 - 16ff
	0xF0, 0xA2, 0xAF, 0xB1, 0x44, 0xF0, 0xA3, 0x80,
	0x8A, 0x44, 0xF0, 0xA3, 0x8A, 0xB8, 0x44, 0xF0,
	0xA3, 0x8D, 0x9F, 0x44, 0xF0, 0xA3, 0x8E, 0x93,
	0x44, 0xF0, 0xA3, 0x8E, 0x9C, 0x44, 0xF0, 0xA3,
	0x8F, 0x83, 0x44, 0xF0, 0xA3, 0x8F, 0x95, 0x44,
	0xF0, 0xA3, 0x91, 0xAD, 0x44, 0xF0, 0xA3, 0x9A,
	0xA3, 0x44, 0xF0, 0xA3, 0xA2, 0xA7, 0x44, 0xF0,
	0xA3, 0xAA, 0x8D, 0x44, 0xF0, 0xA3, 0xAB, 0xBA,
	// Bytes 1700 - 173f
	0x44, 0xF0, 0xA3, 0xB2, 0xBC, 0x44, 0xF0, 0xA3,
	0xB4, 0x9E, 0x44, 0xF0, 0xA3, 0xBB, 0x91, 0x44,
	0xF0, 0xA3, 0xBD, 0x9E, 0x44, 0xF0, 0xA3, 0xBE,
	0x8E, 0x44, 0xF0, 0xA4, 0x89, 0xA3, 0x44, 0xF0,
	0xA4, 0x8B, 0xAE, 0x44, 0xF0, 0xA4, 0x8E, 0xAB,
	0x44, 0xF0, 0xA4, 0x98, 0x88, 0x44, 0xF0, 0xA4,
	0x9C, 0xB5, 0x44, 0xF0, 0xA4, 0xA0, 0x94, 0x44,
	0xF0, 0xA4, 0xB0, 0xB6, 0x44, 0xF0, 0xA4, 0xB2,
	// Bytes 1740 - 177f
	0x92, 0x44, 0xF0, 0xA4, 0xBE, 0xA1, 0x44, 0xF0,
	0xA4, 0xBE, 0xB8, 0x44, 0xF0, 0xA5, 0x81, 0x84,
	0x44, 0xF0, 0xA5, 0x83, 0xB2, 0x44, 0xF0, 0xA5,
	0x83, 0xB3, 0x44, 0xF0, 0xA5, 0x84, 0x99, 0x44,
	0xF0, 0xA5, 0x84, 0xB3, 0x44, 0xF0, 0xA5, 0x89,
	0x89, 0x44, 0xF0, 0xA5, 0x90, 0x9D, 0x44, 0xF0,
	0xA5, 0x98, 0xA6, 0x44, 0xF0, 0xA5, 0x9A, 0x9A,
	0x44, 0xF0, 0xA5, 0x9B, 0x85, 0x44, 0xF0, 0xA5,
	// Bytes 1780 - 17bf
	0xA5, 0xBC, 0x44, 0xF0, 0xA5, 0xAA, 0xA7, 0x44,
	0xF0, 0xA5, 0xAE, 0xAB, 0x44, 0xF0, 0xA5, 0xB2,
	0x80, 0x44, 0xF0, 0xA5, 0xB3, 0x90, 0x44, 0xF0,
	0xA5, 0xBE, 0x86, 0x44, 0xF0, 0xA6, 0x87, 0x9A,
	0x44, 0xF0, 0xA6, 0x88, 0xA8, 0x44, 0xF0, 0xA6,
	0x89, 0x87, 0x44, 0xF0, 0xA6, 0x8B, 0x99, 0x44,
	0xF0, 0xA6, 0x8C, 0xBE, 0x44, 0xF0, 0xA6, 0x93,
	0x9A, 0x44, 0xF0, 0xA6, 0x94, 0xA3, 0x44, 0xF0,
	// Bytes 17c0 - 17ff
	0xA6, 0x96, 0xA8, 0x44, 0xF0, 0xA6, 0x9E, 0xA7,
	0x44, 0xF0, 0xA6, 0x9E, 0xB5, 0x44, 0xF0, 0xA6,
	0xAC, 0xBC, 0x44, 0xF0, 0xA6, 0xB0, 0xB6, 0x44,
	0xF0, 0xA6, 0xB3, 0x95, 0x44, 0xF0, 0xA6, 0xB5,
	0xAB, 0x44, 0xF0, 0xA6, 0xBC, 0xAC, 0x44, 0xF0,
	0xA6, 0xBE, 0xB1, 0x44, 0xF0, 0xA7, 0x83, 0x92,
	0x44, 0xF0, 0xA7, 0x8F, 0x8A, 0x44, 0xF0, 0xA7,
	0x99, 0xA7, 0x44, 0xF0, 0xA7, 0xA2, 0xAE, 0x44,
	// Bytes 1800 - 183f
	0xF0, 0xA7, 0xA5, 0xA6, 0x44, 0xF0, 0xA7, 0xB2,
	0xA8, 0x44, 0xF0, 0xA7, 0xBB, 0x93, 0x44, 0xF0,
	0xA7, 0xBC, 0xAF, 0x44, 0xF0, 0xA8, 0x97, 0x92,
	0x44, 0xF0, 0xA8, 0x97, 0xAD, 0x44, 0xF0, 0xA8,
	0x9C, 0xAE, 0x44, 0xF0, 0xA8, 0xAF, 0xBA, 0x44,
	0xF0, 0xA8, 0xB5, 0xB7, 0x44, 0xF0, 0xA9, 0x85,
	0x85, 0x44, 0xF0, 0xA9, 0x87, 0x9F, 0x44, 0xF0,
	0xA9, 0x88, 0x9A, 0x44, 0xF0, 0xA9, 0x90, 0x8A,
	// Bytes 1840 - 187f
	0x44, 0xF0, 0xA9, 0x92, 0x96, 0x44, 0xF0, 0xA9,
	0x96, 0xB6, 0x44, 0xF0, 0xA9, 0xAC, 0xB0, 0x44,
	0xF0, 0xAA, 0x83, 0x8E, 0x44, 0xF0, 0xAA, 0x84,
	0x85, 0x44, 0xF0, 0xAA, 0x88, 0x8E, 0x44, 0xF0,
	0xAA, 0x8A, 0x91, 0x44, 0xF0, 0xAA, 0x8E, 0x92,
	0x44, 0xF0, 0xAA, 0x98, 0x80, 0x42, 0x21, 0x21,
	0x42, 0x21, 0x3F, 0x42, 0x2E, 0x2E, 0x42, 0x30,
	0x2C, 0x42, 0x30, 0x2E, 0x42, 0x31, 0x2C, 0x42,
	// Bytes 1880 - 18bf
	0x31, 0x2E, 0x42, 0x31, 0x30, 0x42, 0x31, 0x31,
	0x42, 0x31, 0x32, 0x42, 0x31, 0x33, 0x42, 0x31,
	0x34, 0x42, 0x31, 0x35, 0x42, 0x31, 0x36, 0x42,
	0x31, 0x37, 0x42, 0x31, 0x38, 0x42, 0x31, 0x39,
	0x42, 0x32, 0x2C, 0x42, 0x32, 0x2E, 0x42, 0x32,
	0x30, 0x42, 0x32, 0x31, 0x42, 0x32, 0x32, 0x42,
	0x32, 0x33, 0x42, 0x32, 0x34, 0x42, 0x32, 0x35,
	0x42, 0x32, 0x36, 0x42, 0x32, 0x37, 0x42, 0x32,
	// Bytes 18c0 - 18ff
	0x38, 0x42, 0x32, 0x39, 0x42, 0x33, 0x2C, 0x42,
	0x33, 0x2E, 0x42, 0x33, 0x30, 0x42, 0x33, 0x31,
	0x42, 0x33, 0x32, 0x42, 0x33, 0x33, 0x42, 0x33,
	0x34, 0x42, 0x33, 0x35, 0x42, 0x33, 0x36, 0x42,
	0x33, 0x37, 0x42, 0x33, 0x38, 0x42, 0x33, 0x39,
	0x42, 0x34, 0x2C, 0x42, 0x34, 0x2E, 0x42, 0x34,
	0x30, 0x42, 0x34, 0x31, 0x42, 0x34, 0x32, 0x42,
	0x34, 0x33, 0x42, 0x34, 0x34, 0x42, 0x34, 0x35,
	// Bytes 1900 - 193f
	0x42, 0x34, 0x36, 0x42, 0x34, 0x37, 0x42, 0x34,
	0x38, 0x42, 0x34, 0x39, 0x42, 0x35, 0x2C, 0x42,
	0x35, 0x2E, 0x42, 0x35, 0x30, 0x42, 0x36, 0x2C,
	0x42, 0x36, 0x2E, 0x42, 0x37, 0x2C, 0x42, 0x37,
	0x2E, 0x42, 0x38, 0x2C, 0x42, 0x38, 0x2E, 0x42,
	0x39, 0x2C, 0x42, 0x39, 0x2E, 0x42, 0x3D, 0x3D,
	0x42, 0x3F, 0x21, 0x42, 0x3F, 0x3F, 0x42, 0x41,
	0x55, 0x42, 0x42, 0x71, 0x42, 0x43, 0x44, 0x42,
	// Bytes 1940 - 197f
	0x44, 0x4A, 0x42, 0x44, 0x5A, 0x42, 0x44, 0x7A,
	0x42, 0x47, 0x42, 0x42, 0x47, 0x79, 0x42, 0x48,
	0x50, 0x42, 0x48, 0x56, 0x42, 0x48, 0x67, 0x42,
	0x48, 0x7A, 0x42, 0x49, 0x49, 0x42, 0x49, 0x4A,
	0x42, 0x49, 0x55, 0x42, 0x49, 0x56, 0x42, 0x49,
	0x58, 0x42, 0x4B, 0x42, 0x42, 0x4B, 0x4B, 0x42,
	0x4B, 0x4D, 0x42, 0x4C, 0x4A, 0x42, 0x4C, 0x6A,
	0x42, 0x4D, 0x42, 0x42, 0x4D, 0x43, 0x42, 0x4D,
	// Bytes 1980 - 19bf
	0x44, 0x42, 0x4D, 0x56, 0x42, 0x4D, 0x57, 0x42,
	0x4E, 0x4A, 0x42, 0x4E, 0x6A, 0x42, 0x4E, 0x6F,
	0x42, 0x50, 0x48, 0x42, 0x50, 0x52, 0x42, 0x50,
	0x61, 0x42, 0x52, 0x73, 0x42, 0x53, 0x44, 0x42,
	0x53, 0x4D, 0x42, 0x53, 0x53, 0x42, 0x53, 0x76,
	0x42, 0x54, 0x4D, 0x42, 0x56, 0x49, 0x42, 0x57,
	0x43, 0x42, 0x57, 0x5A, 0x42, 0x57, 0x62, 0x42,
	0x58, 0x49, 0x42, 0x63, 0x63, 0x42, 0x63, 0x64,
	// Bytes 19c0 - 19ff
	0x42, 0x63, 0x6D, 0x42, 0x64, 0x42, 0x42, 0x64,
	0x61, 0x42, 0x64, 0x6C, 0x42, 0x64, 0x6D, 0x42,
	0x64, 0x7A, 0x42, 0x65, 0x56, 0x42, 0x66, 0x66,
	0x42, 0x66, 0x69, 0x42, 0x66, 0x6C, 0x42, 0x66,
	0x6D, 0x42, 0x68, 0x61, 0x42, 0x69, 0x69, 0x42,
	0x69, 0x6A, 0x42, 0x69, 0x6E, 0x42, 0x69, 0x76,
	0x42, 0x69, 0x78, 0x42, 0x6B, 0x41, 0x42, 0x6B,
	0x56, 0x42, 0x6B, 0x57, 0x42, 0x6B, 0x67, 0x42,
	// Bytes 1a00 - 1a3f
	0x6B, 0x6C, 0x42, 0x6B, 0x6D, 0x42, 0x6B, 0x74,
	0x42, 0x6C, 0x6A, 0x42, 0x6C, 0x6D, 0x42, 0x6C,
	0x6E, 0x42, 0x6C, 0x78, 0x42, 0x6D, 0x32, 0x42,
	0x6D, 0x33, 0x42, 0x6D, 0x41, 0x42, 0x6D, 0x56,
	0x42, 0x6D, 0x57, 0x42, 0x6D, 0x62, 0x42, 0x6D,
	0x67, 0x42, 0x6D, 0x6C, 0x42, 0x6D, 0x6D, 0x42,
	0x6D, 0x73, 0x42, 0x6E, 0x41, 0x42, 0x6E, 0x46,
	0x42, 0x6E, 0x56, 0x42, 0x6E, 0x57, 0x42, 0x6E,
	// Bytes 1a40 - 1a7f
	0x6A, 0x42, 0x6E, 0x6D, 0x42, 0x6E, 0x73, 0x42,
	0x6F, 0x56, 0x42, 0x70, 0x41, 0x42, 0x70, 0x46,
	0x42, 0x70, 0x56, 0x42, 0x70, 0x57, 0x42, 0x70,
	0x63, 0x42, 0x70, 0x73, 0x42, 0x73, 0x72, 0x42,
	0x73, 0x74, 0x42, 0x76, 0x69, 0x42, 0x78, 0x69,
	0x43, 0x28, 0x31, 0x29, 0x43, 0x28, 0x32, 0x29,
	0x43, 0x28, 0x33, 0x29, 0x43, 0x28, 0x34, 0x29,
	0x43, 0x28, 0x35, 0x29, 0x43, 0x28, 0x36, 0x29,
	// Bytes 1a80 - 1abf
	0x43, 0x28, 0x37, 0x29, 0x43, 0x28, 0x38, 0x29,
	0x43, 0x28, 0x39, 0x29, 0x43, 0x28, 0x41, 0x29,
	0x43, 0x28, 0x42, 0x29, 0x43, 0x28, 0x43, 0x29,
	0x43, 0x28, 0x44, 0x29, 0x43, 0x28, 0x45, 0x29,
	0x43, 0x28, 0x46, 0x29, 0x43, 0x28, 0x47, 0x29,
	0x43, 0x28, 0x48, 0x29, 0x43, 0x28, 0x49, 0x29,
	0x43, 0x28, 0x4A, 0x29, 0x43, 0x28, 0x4B, 0x29,
	0x43, 0x28, 0x4C, 0x29, 0x43, 0x28, 0x4D, 0x29,
	// Bytes 1ac0 - 1aff
	0x43, 0x28, 0x4E, 0x29, 0x43, 0x28, 0x4F, 0x29,
	0x43, 0x28, 0x50, 0x29, 0x43, 0x28, 0x51, 0x29,
	0x43, 0x28, 0x52, 0x29, 0x43, 0x28, 0x53, 0x29,
	0x43, 0x28, 0x54, 0x29, 0x43, 0x28, 0x55, 0x29,
	0x43, 0x28, 0x56, 0x29, 0x43, 0x28, 0x57, 0x29,
	0x43, 0x28, 0x58, 0x29, 0x43, 0x28, 0x59, 0x29,
	0x43, 0x28, 0x5A, 0x29, 0x43, 0x28, 0x61, 0x29,
	0x43, 0x28, 0x62, 0x29, 0x43, 0x28, 0x63, 0x29,
	// Bytes 1b00 - 1b3f
	0x43, 0x28, 0x64, 0x29, 0x43, 0x28, 0x65, 0x29,
	0x43, 0x28, 0x66, 0x29, 0x43, 0x28, 0x67, 0x29,
	0x43, 0x28, 0x68, 0x29, 0x43, 0x28, 0x69, 0x29,
	0x43, 0x28, 0x6A, 0x29, 0x43, 0x28, 0x6B, 0x29,
	0x43, 0x28, 0x6C, 0x29, 0x43, 0x28, 0x6D, 0x29,
	0x43, 0x28, 0x6E, 0x29, 0x43, 0x28, 0x6F, 0x29,
	0x43, 0x28, 0x70, 0x29, 0x43, 0x28, 0x71, 0x29,
	0x43, 0x28, 0x72, 0x29, 0x43, 0x28, 0x73, 0x29,
	// Bytes 1b40 - 1b7f
	0x43, 0x28, 0x74, 0x29, 0x43, 0x28, 0x75, 0x29,
	0x43, 0x28, 0x76, 0x29, 0x43, 0x28, 0x77, 0x29,
	0x43, 0x28, 0x78, 0x29, 0x43, 0x28, 0x79, 0x29,
	0x43, 0x28, 0x7A, 0x29, 0x43, 0x2E, 0x2E, 0x2E,
	0x43, 0x31, 0x30, 0x2E, 0x43, 0x31, 0x31, 0x2E,
	0x43, 0x31, 0x32, 0x2E, 0x43, 0x31, 0x33, 0x2E,
	0x43, 0x31, 0x34, 0x2E, 0x43, 0x31, 0x35, 0x2E,
	0x43, 0x31, 0x36, 0x2E, 0x43, 0x31, 0x37, 0x2E,
	// Bytes 1b80 - 1bbf
	0x43, 0x31, 0x38, 0x2E, 0x43, 0x31, 0x39, 0x2E,
	0x43, 0x32, 0x30, 0x2E, 0x43, 0x3A, 0x3A, 0x3D,
	0x43, 0x3D, 0x3D, 0x3D, 0x43, 0x43, 0x6F, 0x2E,
	0x43, 0x46, 0x41, 0x58, 0x43, 0x47, 0x48, 0x7A,
	0x43, 0x47, 0x50, 0x61, 0x43, 0x49, 0x49, 0x49,
	0x43, 0x4C, 0x54, 0x44, 0x43, 0x4C, 0xC2, 0xB7,
	0x43, 0x4D, 0x48, 0x7A, 0x43, 0x4D, 0x50, 0x61,
	0x43, 0x4D, 0xCE, 0xA9, 0x43, 0x50, 0x50, 0x4D,
	// Bytes 1bc0 - 1bff
	0x43, 0x50, 0x50, 0x56, 0x43, 0x50, 0x54, 0x45,
	0x43, 0x54, 0x45, 0x4C, 0x43, 0x54, 0x48, 0x7A,
	0x43, 0x56, 0x49, 0x49, 0x43, 0x58, 0x49, 0x49,
	0x43, 0x61, 0x2F, 0x63, 0x43, 0x61, 0x2F, 0x73,
	0x43, 0x61, 0xCA, 0xBE, 0x43, 0x62, 0x61, 0x72,
	0x43, 0x63, 0x2F, 0x6F, 0x43, 0x63, 0x2F, 0x75,
	0x43, 0x63, 0x61, 0x6C, 0x43, 0x63, 0x6D, 0x32,
	0x43, 0x63, 0x6D, 0x33, 0x43, 0x64, 0x6D, 0x32,
	// Bytes 1c00 - 1c3f
	0x43, 0x64, 0x6D, 0x33, 0x43, 0x65, 0x72, 0x67,
	0x43, 0x66, 0x66, 0x69, 0x43, 0x66, 0x66, 0x6C,
	0x43, 0x67, 0x61, 0x6C, 0x43, 0x68, 0x50, 0x61,
	0x43, 0x69, 0x69, 0x69, 0x43, 0x6B, 0x48, 0x7A,
	0x43, 0x6B, 0x50, 0x61, 0x43, 0x6B, 0x6D, 0x32,
	0x43, 0x6B, 0x6D, 0x33, 0x43, 0x6B, 0xCE, 0xA9,
	0x43, 0x6C, 0x6F, 0x67, 0x43, 0x6C, 0xC2, 0xB7,
	0x43, 0x6D, 0x69, 0x6C, 0x43, 0x6D, 0x6D, 0x32,
	// Bytes 1c40 - 1c7f
	0x43, 0x6D, 0x6D, 0x33, 0x43, 0x6D, 0x6F, 0x6C,
	0x43, 0x72, 0x61, 0x64, 0x43, 0x76, 0x69, 0x69,
	0x43, 0x78, 0x69, 0x69, 0x43, 0xC2, 0xB0, 0x43,
	0x43, 0xC2, 0xB0, 0x46, 0x43, 0xCA, 0xBC, 0x6E,
	0x43, 0xCE, 0xBC, 0x41, 0x43, 0xCE, 0xBC, 0x46,
	0x43, 0xCE, 0xBC, 0x56, 0x43, 0xCE, 0xBC, 0x57,
	0x43, 0xCE, 0xBC, 0x67, 0x43, 0xCE, 0xBC, 0x6C,
	0x43, 0xCE, 0xBC, 0x6D, 0x43, 0xCE, 0xBC, 0x73,
	// Bytes 1c80 - 1cbf
	0x44, 0x28, 0x31, 0x30, 0x29, 0x44, 0x28, 0x31,
	0x31, 0x29, 0x44, 0x28, 0x31, 0x32, 0x29, 0x44,
	0x28, 0x31, 0x33, 0x29, 0x44, 0x28, 0x31, 0x34,
	0x29, 0x44, 0x28, 0x31, 0x35, 0x29, 0x44, 0x28,
	0x31, 0x36, 0x29, 0x44, 0x28, 0x31, 0x37, 0x29,
	0x44, 0x28, 0x31, 0x38, 0x29, 0x44, 0x28, 0x31,
	0x39, 0x29, 0x44, 0x28, 0x32, 0x30, 0x29, 0x44,
	0x30, 0xE7, 0x82, 0xB9, 0x44, 0x31, 0xE2, 0x81,
	// Bytes 1cc0 - 1cff
	0x84, 0x44, 0x31, 0xE6, 0x97, 0xA5, 0x44, 0x31,
	0xE6, 0x9C, 0x88, 0x44, 0x31, 0xE7, 0x82, 0xB9,
	0x44, 0x32, 0xE6, 0x97, 0xA5, 0x44, 0x32, 0xE6,
	0x9C, 0x88, 0x44, 0x32, 0xE7, 0x82, 0xB9, 0x44,
	0x33, 0xE6, 0x97, 0xA5, 0x44, 0x33, 0xE6, 0x9C,
	0x88, 0x44, 0x33, 0xE7, 0x82, 0xB9, 0x44, 0x34,
	0xE6, 0x97, 0xA5, 0x44, 0x34, 0xE6, 0x9C, 0x88,
	0x44, 0x34, 0xE7, 0x82, 0xB9, 0x44, 0x35, 0xE6,
	// Bytes 1d00 - 1d3f
	0x97, 0xA5, 0x44, 0x35, 0xE6, 0x9C, 0x88, 0x44,
	0x35, 0xE7, 0x82, 0xB9, 0x44, 0x36, 0xE6, 0x97,
	0xA5, 0x44, 0x36, 0xE6, 0x9C, 0x88, 0x44, 0x36,
	0xE7, 0x82, 0xB9, 0x44, 0x37, 0xE6, 0x97, 0xA5,
	0x44, 0x37, 0xE6, 0x9C, 0x88, 0x44, 0x37, 0xE7,
	0x82, 0xB9, 0x44, 0x38, 0xE6, 0x97, 0xA5, 0x44,
	0x38, 0xE6, 0x9C, 0x88, 0x44, 0x38, 0xE7, 0x82,
	0xB9, 0x44, 0x39, 0xE6, 0x97, 0xA5, 0x44, 0x39,
	// Bytes 1d40 - 1d7f
	0xE6, 0x9C, 0x88, 0x44, 0x39, 0xE7, 0x82, 0xB9,
	0x44, 0x56, 0x49, 0x49, 0x49, 0x44, 0x61, 0x2E,
	0x6D, 0x2E, 0x44, 0x6B, 0x63, 0x61, 0x6C, 0x44,
	0x70, 0x2E, 0x6D, 0x2E, 0x44, 0x76, 0x69, 0x69,
	0x69, 0x44, 0xD5, 0xA5, 0xD6, 0x82, 0x44, 0xD5,
	0xB4, 0xD5, 0xA5, 0x44, 0xD5, 0xB4, 0xD5, 0xAB,
	0x44, 0xD5, 0xB4, 0xD5, 0xAD, 0x44, 0xD5, 0xB4,
	0xD5, 0xB6, 0x44, 0xD5, 0xBE, 0xD5, 0xB6, 0x44,
	// Bytes 1d80 - 1dbf
	0xD7, 0x90, 0xD7, 0x9C, 0x44, 0xD8, 0xA7, 0xD9,
	0xB4, 0x44, 0xD8, 0xA8, 0xD8, 0xAC, 0x44, 0xD8,
	0xA8, 0xD8, 0xAD, 0x44, 0xD8, 0xA8, 0xD8, 0xAE,
	0x44, 0xD8, 0xA8, 0xD8, 0xB1, 0x44, 0xD8, 0xA8,
	0xD8, 0xB2, 0x44, 0xD8, 0xA8, 0xD9, 0x85, 0x44,
	0xD8, 0xA8, 0xD9, 0x86, 0x44, 0xD8, 0xA8, 0xD9,
	0x87, 0x44, 0xD8, 0xA8, 0xD9, 0x89, 0x44, 0xD8,
	0xA8, 0xD9, 0x8A, 0x44, 0xD8, 0xAA, 0xD8, 0xAC,
	// Bytes 1dc0 - 1dff
	0x44, 0xD8, 0xAA, 0xD8, 0xAD, 0x44, 0xD8, 0xAA,
	0xD8, 0xAE, 0x44, 0xD8, 0xAA, 0xD8, 0xB1, 0x44,
	0xD8, 0xAA, 0xD8, 0xB2, 0x44, 0xD8, 0xAA, 0xD9,
	0x85, 0x44, 0xD8, 0xAA, 0xD9, 0x86, 0x44, 0xD8,
	0xAA, 0xD9, 0x87, 0x44, 0xD8, 0xAA, 0xD9, 0x89,
	0x44, 0xD8, 0xAA, 0xD9, 0x8A, 0x44, 0xD8, 0xAB,
	0xD8, 0xAC, 0x44, 0xD8, 0xAB, 0xD8, 0xB1, 0x44,
	0xD8, 0xAB, 0xD8, 0xB2, 0x44, 0xD8, 0xAB, 0xD9,
	// Bytes 1e00 - 1e3f
	0x85, 0x44, 0xD8, 0xAB, 0xD9, 0x86, 0x44, 0xD8,
	0xAB, 0xD9, 0x87, 0x44, 0xD8, 0xAB, 0xD9, 0x89,
	0x44, 0xD8, 0xAB, 0xD9, 0x8A, 0x44, 0xD8, 0xAC,
	0xD8, 0xAD, 0x44, 0xD8, 0xAC, 0xD9, 0x85, 0x44,
	0xD8, 0xAC, 0xD9, 0x89, 0x44, 0xD8, 0xAC, 0xD9,
	0x8A, 0x44, 0xD8, 0xAD, 0xD8, 0xAC, 0x44, 0xD8,
	0xAD, 0xD9, 0x85, 0x44, 0xD8, 0xAD, 0xD9, 0x89,
	0x44, 0xD8, 0xAD, 0xD9, 0x8A, 0x44, 0xD8, 0xAE,
	// Bytes 1e40 - 1e7f
	0xD8, 0xAC, 0x44, 0xD8, 0xAE, 0xD8, 0xAD, 0x44,
	0xD8, 0xAE, 0xD9, 0x85, 0x44, 0xD8, 0xAE, 0xD9,
	0x89, 0x44, 0xD8, 0xAE, 0xD9, 0x8A, 0x44, 0xD8,
	0xB3, 0xD8, 0xAC, 0x44, 0xD8, 0xB3, 0xD8, 0xAD,
	0x44, 0xD8, 0xB3, 0xD8, 0xAE, 0x44, 0xD8, 0xB3,
	0xD8, 0xB1, 0x44, 0xD8, 0xB3, 0xD9, 0x85, 0x44,
	0xD8, 0xB3, 0xD9, 0x87, 0x44, 0xD8, 0xB3, 0xD9,
	0x89, 0x44, 0xD8, 0xB3, 0xD9, 0x8A, 0x44, 0xD8,
	// Bytes 1e80 - 1ebf
	0xB4, 0xD8, 0xAC, 0x44, 0xD8, 0xB4, 0xD8, 0xAD,
	0x44, 0xD8, 0xB4, 0xD8, 0xAE, 0x44, 0xD8, 0xB4,
	0xD8, 0xB1, 0x44, 0xD8, 0xB4, 0xD9, 0x85, 0x44,
	0xD8, 0xB4, 0xD9, 0x87, 0x44, 0xD8, 0xB4, 0xD9,
	0x89, 0x44, 0xD8, 0xB4, 0xD9, 0x8A, 0x44, 0xD8,
	0xB5, 0xD8, 0xAD, 0x44, 0xD8, 0xB5, 0xD8, 0xAE,
	0x44, 0xD8, 0xB5, 0xD8, 0xB1, 0x44, 0xD8, 0xB5,
	0xD9, 0x85, 0x44, 0xD8, 0xB5, 0xD9, 0x89, 0x44,
	// Bytes 1ec0 - 1eff
	0xD8, 0xB5, 0xD9, 0x8A, 0x44, 0xD8, 0xB6, 0xD8,
	0xAC, 0x44, 0xD8, 0xB6, 0xD8, 0xAD, 0x44, 0xD8,
	0xB6, 0xD8, 0xAE, 0x44, 0xD8, 0xB6, 0xD8, 0xB1,
	0x44, 0xD8, 0xB6, 0xD9, 0x85, 0x44, 0xD8, 0xB6,
	0xD9, 0x89, 0x44, 0xD8, 0xB6, 0xD9, 0x8A, 0x44,
	0xD8, 0xB7, 0xD8, 0xAD, 0x44, 0xD8, 0xB7, 0xD9,
	0x85, 0x44, 0xD8, 0xB7, 0xD9, 0x89, 0x44, 0xD8,
	0xB7, 0xD9, 0x8A, 0x44, 0xD8, 0xB8, 0xD9, 0x85,
	// Bytes 1f00 - 1f3f
	0x44, 0xD8, 0xB9, 0xD8, 0xAC, 0x44, 0xD8, 0xB9,
	0xD9, 0x85, 0x44, 0xD8, 0xB9, 0xD9, 0x89, 0x44,
	0xD8, 0xB9, 0xD9, 0x8A, 0x44, 0xD8, 0xBA, 0xD8,
	0xAC, 0x44, 0xD8, 0xBA, 0xD9, 0x85, 0x44, 0xD8,
	0xBA, 0xD9, 0x89, 0x44, 0xD8, 0xBA, 0xD9, 0x8A,
	0x44, 0xD9, 0x81, 0xD8, 0xAC, 0x44, 0xD9, 0x81,
	0xD8, 0xAD, 0x44, 0xD9, 0x81, 0xD8, 0xAE, 0x44,
	0xD9, 0x81, 0xD9, 0x85, 0x44, 0xD9, 0x81, 0xD9,
	// Bytes 1f40 - 1f7f
	0x89, 0x44, 0xD9, 0x81, 0xD9, 0x8A, 0x44, 0xD9,
	0x82, 0xD8, 0xAD, 0x44, 0xD9, 0x82, 0xD9, 0x85,
	0x44, 0xD9, 0x82, 0xD9, 0x89, 0x44, 0xD9, 0x82,
	0xD9, 0x8A, 0x44, 0xD9, 0x83, 0xD8, 0xA7, 0x44,
	0xD9, 0x83, 0xD8, 0xAC, 0x44, 0xD9, 0x83, 0xD8,
	0xAD, 0x44, 0xD9, 0x83, 0xD8, 0xAE, 0x44, 0xD9,
	0x83, 0xD9, 0x84, 0x44, 0xD9, 0x83, 0xD9, 0x85,
	0x44, 0xD9, 0x83, 0xD9, 0x89, 0x44, 0xD9, 0x83,
	// Bytes 1f80 - 1fbf
	0xD9, 0x8A, 0x44, 0xD9, 0x84, 0xD8, 0xA7, 0x44,
	0xD9, 0x84, 0xD8, 0xAC, 0x44, 0xD9, 0x84, 0xD8,
	0xAD, 0x44, 0xD9, 0x84, 0xD8, 0xAE, 0x44, 0xD9,
	0x84, 0xD9, 0x85, 0x44, 0xD9, 0x84, 0xD9, 0x87,
	0x44, 0xD9, 0x84, 0xD9, 0x89, 0x44, 0xD9, 0x84,
	0xD9, 0x8A, 0x44, 0xD9, 0x85, 0xD8, 0xA7, 0x44,
	0xD9, 0x85, 0xD8, 0xAC, 0x44, 0xD9, 0x85, 0xD8,
	0xAD, 0x44, 0xD9, 0x85, 0xD8, 0xAE, 0x44, 0xD9,
	// Bytes 1fc0 - 1fff
	0x85, 0xD9, 0x85, 0x44, 0xD9, 0x85, 0xD9, 0x89,
	0x44, 0xD9, 0x85, 0xD9, 0x8A, 0x44, 0xD9, 0x86,
	0xD8, 0xAC, 0x44, 0xD9, 0x86, 0xD8, 0xAD, 0x44,
	0xD9, 0x86, 0xD8, 0xAE, 0x44, 0xD9, 0x86, 0xD8,
	0xB1, 0x44, 0xD9, 0x86, 0xD8, 0xB2, 0x44, 0xD9,
	0x86, 0xD9, 0x85, 0x44, 0xD9, 0x86, 0xD9, 0x86,
	0x44, 0xD9, 0x86, 0xD9, 0x87, 0x44, 0xD9, 0x86,
	0xD9, 0x89, 0x44, 0xD9, 0x86, 0xD9, 0x8A, 0x44,
	// Bytes 2000 - 203f
	0xD9, 0x87, 0xD8, 0xAC, 0x44, 0xD9, 0x87, 0xD9,
	0x85, 0x44, 0xD9, 0x87, 0xD9, 0x89, 0x44, 0xD9,
	0x87, 0xD9, 0x8A, 0x44, 0xD9, 0x88, 0xD9, 0xB4,
	0x44, 0xD9, 0x8A, 0xD8, 0xAC, 0x44, 0xD9, 0x8A,
	0xD8, 0xAD, 0x44, 0xD9, 0x8A, 0xD8, 0xAE, 0x44,
	0xD9, 0x8A, 0xD8, 0xB1, 0x44, 0xD9, 0x8A, 0xD8,
	0xB2, 0x44, 0xD9, 0x8A, 0xD9, 0x85, 0x44, 0xD9,
	0x8A, 0xD9, 0x86, 0x44, 0xD9, 0x8A, 0xD9, 0x87,
	// Bytes 2040 - 207f
	0x44, 0xD9, 0x8A, 0xD9, 0x89, 0x44, 0xD9, 0x8A,
	0xD9, 0x8A, 0x44, 0xD9, 0x8A, 0xD9, 0xB4, 0x44,
	0xDB, 0x87, 0xD9, 0xB4, 0x45, 0x28, 0xE1, 0x84,
	0x80, 0x29, 0x45, 0x28, 0xE1, 0x84, 0x82, 0x29,
	0x45, 0x28, 0xE1, 0x84, 0x83, 0x29, 0x45, 0x28,
	0xE1, 0x84, 0x85, 0x29, 0x45, 0x28, 0xE1, 0x84,
	0x86, 0x29, 0x45, 0x28, 0xE1, 0x84, 0x87, 0x29,
	0x45, 0x28, 0xE1, 0x84, 0x89, 0x29, 0x45, 0x28,
	// Bytes 2080 - 20bf
	0xE1, 0x84, 0x8B, 0x29, 0x45, 0x28, 0xE1, 0x84,
	0x8C, 0x29, 0x45, 0x28, 0xE1, 0x84, 0x8E, 0x29,
	0x45, 0x28, 0xE1, 0x84, 0x8F, 0x29, 0x45, 0x28,
	0xE1, 0x84, 0x90, 0x29, 0x45, 0x28, 0xE1, 0x84,
	0x91, 0x29, 0x45, 0x28, 0xE1, 0x84, 0x92, 0x29,
	0x45, 0x28, 0xE4, 0xB8, 0x80, 0x29, 0x45, 0x28,
	0xE4, 0xB8, 0x83, 0x29, 0x45, 0x28, 0xE4, 0xB8,
	0x89, 0x29, 0x45, 0x28, 0xE4, 0xB9, 0x9D, 0x29,
	// Bytes 20c0 - 20ff
	0x45, 0x28, 0xE4, 0xBA, 0x8C, 0x29, 0x45, 0x28,
	0xE4, 0xBA, 0x94, 0x29, 0x45, 0x28, 0xE4, 0xBB,
	0xA3, 0x29, 0x45, 0x28, 0xE4, 0xBC, 0x81, 0x29,
	0x45, 0x28, 0xE4, 0xBC, 0x91, 0x29, 0x45, 0x28,
	0xE5, 0x85, 0xAB, 0x29, 0x45, 0x28, 0xE5, 0x85,
	0xAD, 0x29, 0x45, 0x28, 0xE5, 0x8A, 0xB4, 0x29,
	0x45, 0x28, 0xE5, 0x8D, 0x81, 0x29, 0x45, 0x28,
	0xE5, 0x8D, 0x94, 0x29, 0x45, 0x28, 0xE5, 0x90,
	// Bytes 2100 - 213f
	0x8D, 0x29, 0x45, 0x28, 0xE5, 0x91, 0xBC, 0x29,
	0x45, 0x28, 0xE5, 0x9B, 0x9B, 0x29, 0x45, 0x28,
	0xE5, 0x9C, 0x9F, 0x29, 0x45, 0x28, 0xE5, 0xAD,
	0xA6, 0x29, 0x45, 0x28, 0xE6, 0x97, 0xA5, 0x29,
	0x45, 0x28, 0xE6, 0x9C, 0x88, 0x29, 0x45, 0x28,
	0xE6, 0x9C, 0x89, 0x29, 0x45, 0x28, 0xE6, 0x9C,
	0xA8, 0x29, 0x45, 0x28, 0xE6, 0xA0, 0xAA, 0x29,
	0x45, 0x28, 0xE6, 0xB0, 0xB4, 0x29, 0x45, 0x28,
	// Bytes 2140 - 217f
	0xE7, 0x81, 0xAB, 0x29, 0x45, 0x28, 0xE7, 0x89,
	0xB9, 0x29, 0x45, 0x28, 0xE7, 0x9B, 0xA3, 0x29,
	0x45, 0x28, 0xE7, 0xA4, 0xBE, 0x29, 0x45, 0x28,
	0xE7, 0xA5, 0x9D, 0x29, 0x45, 0x28, 0xE7, 0xA5,
	0xAD, 0x29, 0x45, 0x28, 0xE8, 0x87, 0xAA, 0x29,
	0x45, 0x28, 0xE8, 0x87, 0xB3, 0x29, 0x45, 0x28,
	0xE8, 0xB2, 0xA1, 0x29, 0x45, 0x28, 0xE8, 0xB3,
	0x87, 0x29, 0x45, 0x28, 0xE9, 0x87, 0x91, 0x29,
	// Bytes 2180 - 21bf
	0x45, 0x30, 0xE2, 0x81, 0x84, 0x33, 0x45, 0x31,
	0x30, 0xE6, 0x97, 0xA5, 0x45, 0x31, 0x30, 0xE6,
	0x9C, 0x88, 0x45, 0x31, 0x30, 0xE7, 0x82, 0xB9,
	0x45, 0x31, 0x31, 0xE6, 0x97, 0xA5, 0x45, 0x31,
	0x31, 0xE6, 0x9C, 0x88, 0x45, 0x31, 0x31, 0xE7,
	0x82, 0xB9, 0x45, 0x31, 0x32, 0xE6, 0x97, 0xA5,
	0x45, 0x31, 0x32, 0xE6, 0x9C, 0x88, 0x45, 0x31,
	0x32, 0xE7, 0x82, 0xB9, 0x45, 0x31, 0x33, 0xE6,
	// Bytes 21c0 - 21ff
	0x97, 0xA5, 0x45, 0x31, 0x33, 0xE7, 0x82, 0xB9,
	0x45, 0x31, 0x34, 0xE6, 0x97, 0xA5, 0x45, 0x31,
	0x34, 0xE7, 0x82, 0xB9, 0x45, 0x31, 0x35, 0xE6,
	0x97, 0xA5, 0x45, 0x31, 0x35, 0xE7, 0x82, 0xB9,
	0x45, 0x31, 0x36, 0xE6, 0x97, 0xA5, 0x45, 0x31,
	0x36, 0xE7, 0x82, 0xB9, 0x45, 0x31, 0x37, 0xE6,
	0x97, 0xA5, 0x45, 0x31, 0x37, 0xE7, 0x82, 0xB9,
	0x45, 0x31, 0x38, 0xE6, 0x97, 0xA5, 0x45, 0x31,
	// Bytes 2200 - 223f
	0x38, 0xE7, 0x82, 0xB9, 0x45, 0x31, 0x39, 0xE6,
	0x97, 0xA5, 0x45, 0x31, 0x39, 0xE7, 0x82, 0xB9,
	0x45, 0x31, 0xE2, 0x81, 0x84, 0x32, 0x45, 0x31,
	0xE2, 0x81, 0x84, 0x33, 0x45, 0x31, 0xE2, 0x81,
	0x84, 0x34, 0x45, 0x31, 0xE2, 0x81, 0x84, 0x35,
	0x45, 0x31, 0xE2, 0x81, 0x84, 0x36, 0x45, 0x31,
	0xE2, 0x81, 0x84, 0x37, 0x45, 0x31, 0xE2, 0x81,
	0x84, 0x38, 0x45, 0x31, 0xE2, 0x81, 0x84, 0x39,
	// Bytes 2240 - 227f
	0x45, 0x32, 0x30, 0xE6, 0x97, 0xA5, 0x45, 0x32,
	0x30, 0xE7, 0x82, 0xB9, 0x45, 0x32, 0x31, 0xE6,
	0x97, 0xA5, 0x45, 0x32, 0x31, 0xE7, 0x82, 0xB9,
	0x45, 0x32, 0x32, 0xE6, 0x97, 0xA5, 0x45, 0x32,
	0x32, 0xE7, 0x82, 0xB9, 0x45, 0x32, 0x33, 0xE6,
	0x97, 0xA5, 0x45, 0x32, 0x33, 0xE7, 0x82, 0xB9,
	0x45, 0x32, 0x34, 0xE6, 0x97, 0xA5, 0x45, 0x32,
	0x34, 0xE7, 0x82, 0xB9, 0x45, 0x32, 0x35, 0xE6,
	// Bytes 2280 - 22bf
	0x97, 0xA5, 0x45, 0x32, 0x36, 0xE6, 0x97, 0xA5,
	0x45, 0x32, 0x37, 0xE6, 0x97, 0xA5, 0x45, 0x32,
	0x38, 0xE6, 0x97, 0xA5, 0x45, 0x32, 0x39, 0xE6,
	0x97, 0xA5, 0x45, 0x32, 0xE2, 0x81, 0x84, 0x33,
	0x45, 0x32, 0xE2, 0x81, 0x84, 0x35, 0x45, 0x33,
	0x30, 0xE6, 0x97, 0xA5, 0x45, 0x33, 0x31, 0xE6,
	0x97, 0xA5, 0x45, 0x33, 0xE2, 0x81, 0x84, 0x34,
	0x45, 0x33, 0xE2, 0x81, 0x84, 0x35, 0x45, 0x33,
	// Bytes 22c0 - 22ff
	0xE2, 0x81, 0x84, 0x38, 0x45, 0x34, 0xE2, 0x81,
	0x84, 0x35, 0x45, 0x35, 0xE2, 0x81, 0x84, 0x36,
	0x45, 0x35, 0xE2, 0x81, 0x84, 0x38, 0x45, 0x37,
	0xE2, 0x81, 0x84, 0x38, 0x45, 0x41, 0xE2, 0x88,
	0x95, 0x6D, 0x45, 0x56, 0xE2, 0x88, 0x95, 0x6D,
	0x45, 0x6D, 0xE2, 0x88, 0x95, 0x73, 0x46, 0x31,
	0xE2, 0x81, 0x84, 0x31, 0x30, 0x46, 0x43, 0xE2,
	0x88, 0x95, 0x6B, 0x67, 0x46, 0x6D, 0xE2, 0x88,
	// Bytes 2300 - 233f
	0x95, 0x73, 0x32, 0x46, 0xD8, 0xA8, 0xD8, 0xAD,
	0xD9, 0x8A, 0x46, 0xD8, 0xA8, 0xD8, 0xAE, 0xD9,
	0x8A, 0x46, 0xD8, 0xAA, 0xD8, 0xAC, 0xD9, 0x85,
	0x46, 0xD8, 0xAA, 0xD8, 0xAC, 0xD9, 0x89, 0x46,
	0xD8, 0xAA, 0xD8, 0xAC, 0xD9, 0x8A, 0x46, 0xD8,
	0xAA, 0xD8, 0xAD, 0xD8, 0xAC, 0x46, 0xD8, 0xAA,
	0xD8, 0xAD, 0xD9, 0x85, 0x46, 0xD8, 0xAA, 0xD8,
	0xAE, 0xD9, 0x85, 0x46, 0xD8, 0xAA, 0xD8, 0xAE,
	// Bytes 2340 - 237f
	0xD9, 0x89, 0x46, 0xD8, 0xAA, 0xD8, 0xAE, 0xD9,
	0x8A, 0x46, 0xD8, 0xAA, 0xD9, 0x85, 0xD8, 0xAC,
	0x46, 0xD8, 0xAA, 0xD9, 0x85, 0xD8, 0xAD, 0x46,
	0xD8, 0xAA, 0xD9, 0x85, 0xD8, 0xAE, 0x46, 0xD8,
	0xAA, 0xD9, 0x85, 0xD9, 0x89, 0x46, 0xD8, 0xAA,
	0xD9, 0x85, 0xD9, 0x8A, 0x46, 0xD8, 0xAC, 0xD8,
	0xAD, 0xD9, 0x89, 0x46, 0xD8, 0xAC, 0xD8, 0xAD,
	0xD9, 0x8A, 0x46, 0xD8, 0xAC, 0xD9, 0x85, 0xD8,
	// Bytes 2380 - 23bf
	0xAD, 0x46, 0xD8, 0xAC, 0xD9, 0x85, 0xD9, 0x89,
	0x46, 0xD8, 0xAC, 0xD9, 0x85, 0xD9, 0x8A, 0x46,
	0xD8, 0xAD, 0xD8, 0xAC, 0xD9, 0x8A, 0x46, 0xD8,
	0xAD, 0xD9, 0x85, 0xD9, 0x89, 0x46, 0xD8, 0xAD,
	0xD9, 0x85, 0xD9, 0x8A, 0x46, 0xD8, 0xB3, 0xD8,
	0xAC, 0xD8, 0xAD, 0x46, 0xD8, 0xB3, 0xD8, 0xAC,
	0xD9, 0x89, 0x46, 0xD8, 0xB3, 0xD8, 0xAD, 0xD8,
	0xAC, 0x46, 0xD8, 0xB3, 0xD8, 0xAE, 0xD9, 0x89,
	// Bytes 23c0 - 23ff
	0x46, 0xD8, 0xB3, 0xD8, 0xAE, 0xD9, 0x8A, 0x46,
	0xD8, 0xB3, 0xD9, 0x85, 0xD8, 0xAC, 0x46, 0xD8,
	0xB3, 0xD9, 0x85, 0xD8, 0xAD, 0x46, 0xD8, 0xB3,
	0xD9, 0x85, 0xD9, 0x85, 0x46, 0xD8, 0xB4, 0xD8,
	0xAC, 0xD9, 0x8A, 0x46, 0xD8, 0xB4, 0xD8, 0xAD,
	0xD9, 0x85, 0x46, 0xD8, 0xB4, 0xD8, 0xAD, 0xD9,
	0x8A, 0x46, 0xD8, 0xB4, 0xD9, 0x85, 0xD8, 0xAE,
	0x46, 0xD8, 0xB4, 0xD9, 0x85, 0xD9, 0x85, 0x46,
	// Bytes 2400 - 243f
	0xD8, 0xB5, 0xD8, 0xAD, 0xD8, 0xAD, 0x46, 0xD8,
	0xB5, 0xD8, 0xAD, 0xD9, 0x8A, 0x46, 0xD8, 0xB5,
	0xD9, 0x84, 0xD9, 0x89, 0x46, 0xD8, 0xB5, 0xD9,
	0x84, 0xDB, 0x92, 0x46, 0xD8, 0xB5, 0xD9, 0x85,
	0xD9, 0x85, 0x46, 0xD8, 0xB6, 0xD8, 0xAD, 0xD9,
	0x89, 0x46, 0xD8, 0xB6, 0xD8, 0xAD, 0xD9, 0x8A,
	0x46, 0xD8, 0xB6, 0xD8, 0xAE, 0xD9, 0x85, 0x46,
	0xD8, 0xB7, 0xD9, 0x85, 0xD8, 0xAD, 0x46, 0xD8,
	// Bytes 2440 - 247f
	0xB7, 0xD9, 0x85, 0xD9, 0x85, 0x46, 0xD8, 0xB7,
	0xD9, 0x85, 0xD9, 0x8A, 0x46, 0xD8, 0xB9, 0xD8,
	0xAC, 0xD9, 0x85, 0x46, 0xD8, 0xB9, 0xD9, 0x85,
	0xD9, 0x85, 0x46, 0xD8, 0xB9, 0xD9, 0x85, 0xD9,
	0x89, 0x46, 0xD8, 0xB9, 0xD9, 0x85, 0xD9, 0x8A,
	0x46, 0xD8, 0xBA, 0xD9, 0x85, 0xD9, 0x85, 0x46,
	0xD8, 0xBA, 0xD9, 0x85, 0xD9, 0x89, 0x46, 0xD8,
	0xBA, 0xD9, 0x85, 0xD9, 0x8A, 0x46, 0xD9, 0x81,
	// Bytes 2480 - 24bf
	0xD8, 0xAE, 0xD9, 0x85, 0x46, 0xD9, 0x81, 0xD9,
	0x85, 0xD9, 0x8A, 0x46, 0xD9, 0x82, 0xD9, 0x84,
	0xDB, 0x92, 0x46, 0xD9, 0x82, 0xD9, 0x85, 0xD8,
	0xAD, 0x46, 0xD9, 0x82, 0xD9, 0x85, 0xD9, 0x85,
	0x46, 0xD9, 0x82, 0xD9, 0x85, 0xD9, 0x8A, 0x46,
	0xD9, 0x83, 0xD9, 0x85, 0xD9, 0x85, 0x46, 0xD9,
	0x83, 0xD9, 0x85, 0xD9, 0x8A, 0x46, 0xD9, 0x84,
	0xD8, 0xAC, 0xD8, 0xAC, 0x46, 0xD9, 0x84, 0xD8,
	// Bytes 24c0 - 24ff
	0xAC, 0xD9, 0x85, 0x46, 0xD9, 0x84, 0xD8, 0xAC,
	0xD9, 0x8A, 0x46, 0xD9, 0x84, 0xD8, 0xAD, 0xD9,
	0x85, 0x46, 0xD9, 0x84, 0xD8, 0xAD, 0xD9, 0x89,
	0x46, 0xD9, 0x84, 0xD8, 0xAD, 0xD9, 0x8A, 0x46,
	0xD9, 0x84, 0xD8, 0xAE, 0xD9, 0x85, 0x46, 0xD9,
	0x84, 0xD9, 0x85, 0xD8, 0xAD, 0x46, 0xD9, 0x84,
	0xD9, 0x85, 0xD9, 0x8A, 0x46, 0xD9, 0x85, 0xD8,
	0xAC, 0xD8, 0xAD, 0x46, 0xD9, 0x85, 0xD8, 0xAC,
	// Bytes 2500 - 253f
	0xD8, 0xAE, 0x46, 0xD9, 0x85, 0xD8, 0xAC, 0xD9,
	0x85, 0x46, 0xD9, 0x85, 0xD8, 0xAC, 0xD9, 0x8A,
	0x46, 0xD9, 0x85, 0xD8, 0xAD, 0xD8, 0xAC, 0x46,
	0xD9, 0x85, 0xD8, 0xAD, 0xD9, 0x85, 0x46, 0xD9,
	0x85, 0xD8, 0xAD, 0xD9, 0x8A, 0x46, 0xD9, 0x85,
	0xD8, 0xAE, 0xD8, 0xAC, 0x46, 0xD9, 0x85, 0xD8,
	0xAE, 0xD9, 0x85, 0x46, 0xD9, 0x85, 0xD8, 0xAE,
	0xD9, 0x8A, 0x46, 0xD9, 0x85, 0xD9, 0x85, 0xD9,
	// Bytes 2540 - 257f
	0x8A, 0x46, 0xD9, 0x86, 0xD8, 0xAC, 0xD8, 0xAD,
	0x46, 0xD9, 0x86, 0xD8, 0xAC, 0xD9, 0x85, 0x46,
	0xD9, 0x86, 0xD8, 0xAC, 0xD9, 0x89, 0x46, 0xD9,
	0x86, 0xD8, 0xAC, 0xD9, 0x8A, 0x46, 0xD9, 0x86,
	0xD8, 0xAD, 0xD9, 0x85, 0x46, 0xD9, 0x86, 0xD8,
	0xAD, 0xD9, 0x89, 0x46, 0xD9, 0x86, 0xD8, 0xAD,
	0xD9, 0x8A, 0x46, 0xD9, 0x86, 0xD9, 0x85, 0xD9,
	0x89, 0x46, 0xD9, 0x86, 0xD9, 0x85, 0xD9, 0x8A,
	// Bytes 2580 - 25bf
	0x46, 0xD9, 0x87, 0xD9, 0x85, 0xD8, 0xAC, 0x46,
	0xD9, 0x87, 0xD9, 0x85, 0xD9, 0x85, 0x46, 0xD9,
	0x8A, 0xD8, 0xAC, 0xD9, 0x8A, 0x46, 0xD9, 0x8A,
	0xD8, 0xAD, 0xD9, 0x8A, 0x46, 0xD9, 0x8A, 0xD9,
	0x85, 0xD9, 0x85, 0x46, 0xD9, 0x8A, 0xD9, 0x85,
	0xD9, 0x8A, 0x46, 0xD9, 0x8A, 0xD9, 0x94, 0xD8,
	0xA7, 0x46, 0xD9, 0x8A, 0xD9, 0x94, 0xD8, 0xAC,
	0x46, 0xD9, 0x8A, 0xD9, 0x94, 0xD8, 0xAD, 0x46,
	// Bytes 25c0 - 25ff
	0xD9, 0x8A, 0xD9, 0x94, 0xD8, 0xAE, 0x46, 0xD9,
	0x8A, 0xD9, 0x94, 0xD8, 0xB1, 0x46, 0xD9, 0x8A,
	0xD9, 0x94, 0xD8, 0xB2, 0x46, 0xD9, 0x8A, 0xD9,
	0x94, 0xD9, 0x85, 0x46, 0xD9, 0x8A, 0xD9, 0x94,
	0xD9, 0x86, 0x46, 0xD9, 0x8A, 0xD9, 0x94, 0xD9,
	0x87, 0x46, 0xD9, 0x8A, 0xD9, 0x94, 0xD9, 0x88,
	0x46, 0xD9, 0x8A, 0xD9, 0x94, 0xD9, 0x89, 0x46,
	0xD9, 0x8A, 0xD9, 0x94, 0xD9, 0x8A, 0x46, 0xD9,
	// Bytes 2600 - 263f
	0x8A, 0xD9, 0x94, 0xDB, 0x86, 0x46, 0xD9, 0x8A,
	0xD9, 0x94, 0xDB, 0x87, 0x46, 0xD9, 0x8A, 0xD9,
	0x94, 0xDB, 0x88, 0x46, 0xD9, 0x8A, 0xD9, 0x94,
	0xDB, 0x90, 0x46, 0xD9, 0x8A, 0xD9, 0x94, 0xDB,
	0x95, 0x46, 0xE0, 0xB9, 0x8D, 0xE0, 0xB8, 0xB2,
	0x46, 0xE0, 0xBA, 0xAB, 0xE0, 0xBA, 0x99, 0x46,
	0xE0, 0xBA, 0xAB, 0xE0, 0xBA, 0xA1, 0x46, 0xE0,
	0xBB, 0x8D, 0xE0, 0xBA, 0xB2, 0x46, 0xE0, 0xBD,
	// Bytes 2640 - 267f
	0x80, 0xE0, 0xBE, 0xB5, 0x46, 0xE0, 0xBD, 0x82,
	0xE0, 0xBE, 0xB7, 0x46, 0xE0, 0xBD, 0x8C, 0xE0,
	0xBE, 0xB7, 0x46, 0xE0, 0xBD, 0x91, 0xE0, 0xBE,
	0xB7, 0x46, 0xE0, 0xBD, 0x96, 0xE0, 0xBE, 0xB7,
	0x46, 0xE0, 0xBD, 0x9B, 0xE0, 0xBE, 0xB7, 0x46,
	0xE0, 0xBE, 0x90, 0xE0, 0xBE, 0xB5, 0x46, 0xE0,
	0xBE, 0x92, 0xE0, 0xBE, 0xB7, 0x46, 0xE0, 0xBE,
	0x9C, 0xE0, 0xBE, 0xB7, 0x46, 0xE0, 0xBE, 0xA1,
	// Bytes 2680 - 26bf
	0xE0, 0xBE, 0xB7, 0x46, 0xE0, 0xBE, 0xA6, 0xE0,
	0xBE, 0xB7, 0x46, 0xE0, 0xBE, 0xAB, 0xE0, 0xBE,
	0xB7, 0x46, 0xE2, 0x80, 0xB2, 0xE2, 0x80, 0xB2,
	0x46, 0xE2, 0x80, 0xB5, 0xE2, 0x80, 0xB5, 0x46,
	0xE2, 0x88, 0xAB, 0xE2, 0x88, 0xAB, 0x46, 0xE2,
	0x88, 0xAE, 0xE2, 0x88, 0xAE, 0x46, 0xE3, 0x81,
	0xBB, 0xE3, 0x81, 0x8B, 0x46, 0xE3, 0x82, 0x88,
	0xE3, 0x82, 0x8A, 0x46, 0xE3, 0x82, 0xAD, 0xE3,
	// Bytes 26c0 - 26ff
	0x83, 0xAD, 0x46, 0xE3, 0x82, 0xB3, 0xE3, 0x82,
	0xB3, 0x46, 0xE3, 0x82, 0xB3, 0xE3, 0x83, 0x88,
	0x46, 0xE3, 0x83, 0x88, 0xE3, 0x83, 0xB3, 0x46,
	0xE3, 0x83, 0x8A, 0xE3, 0x83, 0x8E, 0x46, 0xE3,
	0x83, 0x9B, 0xE3, 0x83, 0xB3, 0x46, 0xE3, 0x83,
	0x9F, 0xE3, 0x83, 0xAA, 0x46, 0xE3, 0x83, 0xAA,
	0xE3, 0x83, 0xA9, 0x46, 0xE3, 0x83, 0xAC, 0xE3,
	0x83, 0xA0, 0x46, 0xE5, 0xA4, 0xA7, 0xE6, 0xAD,
	// Bytes 2700 - 273f
	0xA3, 0x46, 0xE5, 0xB9, 0xB3, 0xE6, 0x88, 0x90,
	0x46, 0xE6, 0x98, 0x8E, 0xE6, 0xB2, 0xBB, 0x46,
	0xE6, 0x98, 0xAD, 0xE5, 0x92, 0x8C, 0x47, 0x72,
	0x61, 0x64, 0xE2, 0x88, 0x95, 0x73, 0x47, 0xE3,
	0x80, 0x94, 0x53, 0xE3, 0x80, 0x95, 0x48, 0x28,
	0xE1, 0x84, 0x80, 0xE1, 0x85, 0xA1, 0x29, 0x48,
	0x28, 0xE1, 0x84, 0x82, 0xE1, 0x85, 0xA1, 0x29,
	0x48, 0x28, 0xE1, 0x84, 0x83, 0xE1, 0x85, 0xA1,
	// Bytes 2740 - 277f
	0x29, 0x48, 0x28, 0xE1, 0x84, 0x85, 0xE1, 0x85,
	0xA1, 0x29, 0x48, 0x28, 0xE1, 0x84, 0x86, 0xE1,
	0x85, 0xA1, 0x29, 0x48, 0x28, 0xE1, 0x84, 0x87,
	0xE1, 0x85, 0xA1, 0x29, 0x48, 0x28, 0xE1, 0x84,
	0x89, 0xE1, 0x85, 0xA1, 0x29, 0x48, 0x28, 0xE1,
	0x84, 0x8B, 0xE1, 0x85, 0xA1, 0x29, 0x48, 0x28,
	0xE1, 0x84, 0x8C, 0xE1, 0x85, 0xA1, 0x29, 0x48,
	0x28, 0xE1, 0x84, 0x8C, 0xE1, 0x85, 0xAE, 0x29,
	// Bytes 2780 - 27bf
	0x48, 0x28, 0xE1, 0x84, 0x8E, 0xE1, 0x85, 0xA1,
	0x29, 0x48, 0x28, 0xE1, 0x84, 0x8F, 0xE1, 0x85,
	0xA1, 0x29, 0x48, 0x28, 0xE1, 0x84, 0x90, 0xE1,
	0x85, 0xA1, 0x29, 0x48, 0x28, 0xE1, 0x84, 0x91,
	0xE1, 0x85, 0xA1, 0x29, 0x48, 0x28, 0xE1, 0x84,
	0x92, 0xE1, 0x85, 0xA1, 0x29, 0x48, 0x72, 0x61,
	0x64, 0xE2, 0x88, 0x95, 0x73, 0x32, 0x48, 0xD8,
	0xA7, 0xD9, 0x83, 0xD8, 0xA8, 0xD8, 0xB1, 0x48,
	// Bytes 27c0 - 27ff
	0xD8, 0xA7, 0xD9, 0x84, 0xD9, 0x84, 0xD9, 0x87,
	0x48, 0xD8, 0xB1, 0xD8, 0xB3, 0xD9, 0x88, 0xD9,
	0x84, 0x48, 0xD8, 0xB1, 0xDB, 0x8C, 0xD8, 0xA7,
	0xD9, 0x84, 0x48, 0xD8, 0xB5, 0xD9, 0x84, 0xD8,
	0xB9, 0xD9, 0x85, 0x48, 0xD8, 0xB9, 0xD9, 0x84,
	0xD9, 0x8A, 0xD9, 0x87, 0x48, 0xD9, 0x85, 0xD8,
	0xAD, 0xD9, 0x85, 0xD8, 0xAF, 0x48, 0xD9, 0x88,
	0xD8, 0xB3, 0xD9, 0x84, 0xD9, 0x85, 0x49, 0xE2,
	// Bytes 2800 - 283f
	0x80, 0xB2, 0xE2, 0x80, 0xB2, 0xE2, 0x80, 0xB2,
	0x49, 0xE2, 0x80, 0xB5, 0xE2, 0x80, 0xB5, 0xE2,
	0x80, 0xB5, 0x49, 0xE2, 0x88, 0xAB, 0xE2, 0x88,
	0xAB, 0xE2, 0x88, 0xAB, 0x49, 0xE2, 0x88, 0xAE,
	0xE2, 0x88, 0xAE, 0xE2, 0x88, 0xAE, 0x49, 0xE3,
	0x80, 0x94, 0xE4, 0xB8, 0x89, 0xE3, 0x80, 0x95,
	0x49, 0xE3, 0x80, 0x94, 0xE4, 0xBA, 0x8C, 0xE3,
	0x80, 0x95, 0x49, 0xE3, 0x80, 0x94, 0xE5, 0x8B,
	// Bytes 2840 - 287f
	0x9D, 0xE3, 0x80, 0x95, 0x49, 0xE3, 0x80, 0x94,
	0xE5, 0xAE, 0x89, 0xE3, 0x80, 0x95, 0x49, 0xE3,
	0x80, 0x94, 0xE6, 0x89, 0x93, 0xE3, 0x80, 0x95,
	0x49, 0xE3, 0x80, 0x94, 0xE6, 0x95, 0x97, 0xE3,
	0x80, 0x95, 0x49, 0xE3, 0x80, 0x94, 0xE6, 0x9C,
	0xAC, 0xE3, 0x80, 0x95, 0x49, 0xE3, 0x80, 0x94,
	0xE7, 0x82, 0xB9, 0xE3, 0x80, 0x95, 0x49, 0xE3,
	0x80, 0x94, 0xE7, 0x9B, 0x97, 0xE3, 0x80, 0x95,
	// Bytes 2880 - 28bf
	0x49, 0xE3, 0x82, 0xA2, 0xE3, 0x83, 0xBC, 0xE3,
	0x83, 0xAB, 0x49, 0xE3, 0x82, 0xA4, 0xE3, 0x83,
	0xB3, 0xE3, 0x83, 0x81, 0x49, 0xE3, 0x82, 0xA6,
	0xE3, 0x82, 0xA9, 0xE3, 0x83, 0xB3, 0x49, 0xE3,
	0x82, 0xAA, 0xE3, 0x83, 0xB3, 0xE3, 0x82, 0xB9,
	0x49, 0xE3, 0x82, 0xAA, 0xE3, 0x83, 0xBC, 0xE3,
	0x83, 0xA0, 0x49, 0xE3, 0x82, 0xAB, 0xE3, 0x82,
	0xA4, 0xE3, 0x83, 0xAA, 0x49, 0xE3, 0x82, 0xB1,
	// Bytes 28c0 - 28ff
	0xE3, 0x83, 0xBC, 0xE3, 0x82, 0xB9, 0x49, 0xE3,
	0x82, 0xB3, 0xE3, 0x83, 0xAB, 0xE3, 0x83, 0x8A,
	0x49, 0xE3, 0x82, 0xBB, 0xE3, 0x83, 0xB3, 0xE3,
	0x83, 0x81, 0x49, 0xE3, 0x82, 0xBB, 0xE3, 0x83,
	0xB3, 0xE3, 0x83, 0x88, 0x49, 0xE3, 0x83, 0x86,
	0xE3, 0x82, 0x99, 0xE3, 0x82, 0xB7, 0x49, 0xE3,
	0x83, 0x88, 0xE3, 0x82, 0x99, 0xE3, 0x83, 0xAB,
	0x49, 0xE3, 0x83, 0x8E, 0xE3, 0x83, 0x83, 0xE3,
	// Bytes 2900 - 293f
	0x83, 0x88, 0x49, 0xE3, 0x83, 0x8F, 0xE3, 0x82,
	0xA4, 0xE3, 0x83, 0x84, 0x49, 0xE3, 0x83, 0x92,
	0xE3, 0x82, 0x99, 0xE3, 0x83, 0xAB, 0x49, 0xE3,
	0x83, 0x92, 0xE3, 0x82, 0x9A, 0xE3, 0x82, 0xB3,
	0x49, 0xE3, 0x83, 0x95, 0xE3, 0x83, 0xA9, 0xE3,
	0x83, 0xB3, 0x49, 0xE3, 0x83, 0x98, 0xE3, 0x82,
	0x9A, 0xE3, 0x82, 0xBD, 0x49, 0xE3, 0x83, 0x98,
	0xE3, 0x83, 0xAB, 0xE3, 0x83, 0x84, 0x49, 0xE3,
	// Bytes 2940 - 297f
	0x83, 0x9B, 0xE3, 0x83, 0xBC, 0xE3, 0x83, 0xAB,
	0x49, 0xE3, 0x83, 0x9B, 0xE3, 0x83, 0xBC, 0xE3,
	0x83, 0xB3, 0x49, 0xE3, 0x83, 0x9E, 0xE3, 0x82,
	0xA4, 0xE3, 0x83, 0xAB, 0x49, 0xE3, 0x83, 0x9E,
	0xE3, 0x83, 0x83, 0xE3, 0x83, 0x8F, 0x49, 0xE3,
	0x83, 0x9E, 0xE3, 0x83, 0xAB, 0xE3, 0x82, 0xAF,
	0x49, 0xE3, 0x83, 0xA4, 0xE3, 0x83, 0xBC, 0xE3,
	0x83, 0xAB, 0x49, 0xE3, 0x83, 0xA6, 0xE3, 0x82,
	// Bytes 2980 - 29bf
	0xA2, 0xE3, 0x83, 0xB3, 0x49, 0xE3, 0x83, 0xAF,
	0xE3, 0x83, 0x83, 0xE3, 0x83, 0x88, 0x4C, 0xE2,
	0x80, 0xB2, 0xE2, 0x80, 0xB2, 0xE2, 0x80, 0xB2,
	0xE2, 0x80, 0xB2, 0x4C, 0xE2, 0x88, 0xAB, 0xE2,
	0x88, 0xAB, 0xE2, 0x88, 0xAB, 0xE2, 0x88, 0xAB,
	0x4C, 0xE3, 0x82, 0xA2, 0xE3, 0x83, 0xAB, 0xE3,
	0x83, 0x95, 0xE3, 0x82, 0xA1, 0x4C, 0xE3, 0x82,
	0xA8, 0xE3, 0x83, 0xBC, 0xE3, 0x82, 0xAB, 0xE3,
	// Bytes 29c0 - 29ff
	0x83, 0xBC, 0x4C, 0xE3, 0x82, 0xAB, 0xE3, 0x82,
	0x99, 0xE3, 0x83, 0xAD, 0xE3, 0x83, 0xB3, 0x4C,
	0xE3, 0x82, 0xAB, 0xE3, 0x82, 0x99, 0xE3, 0x83,
	0xB3, 0xE3, 0x83, 0x9E, 0x4C, 0xE3, 0x82, 0xAB,
	0xE3, 0x83, 0xA9, 0xE3, 0x83, 0x83, 0xE3, 0x83,
	0x88, 0x4C, 0xE3, 0x82, 0xAB, 0xE3, 0x83, 0xAD,
	0xE3, 0x83, 0xAA, 0xE3, 0x83, 0xBC, 0x4C, 0xE3,
	0x82, 0xAD, 0xE3, 0x82, 0x99, 0xE3, 0x83, 0x8B,
	// Bytes 2a00 - 2a3f
	0xE3, 0x83, 0xBC, 0x4C, 0xE3, 0x82, 0xAD, 0xE3,
	0x83, 0xA5, 0xE3, 0x83, 0xAA, 0xE3, 0x83, 0xBC,
	0x4C, 0xE3, 0x82, 0xAF, 0xE3, 0x82, 0x99, 0xE3,
	0x83, 0xA9, 0xE3, 0x83, 0xA0, 0x4C, 0xE3, 0x82,
	0xAF, 0xE3, 0x83, 0xAD, 0xE3, 0x83, 0xBC, 0xE3,
	0x83, 0x8D, 0x4C, 0xE3, 0x82, 0xB5, 0xE3, 0x82,
	0xA4, 0xE3, 0x82, 0xAF, 0xE3, 0x83, 0xAB, 0x4C,
	0xE3, 0x82, 0xBF, 0xE3, 0x82, 0x99, 0xE3, 0x83,
	// Bytes 2a40 - 2a7f
	0xBC, 0xE3, 0x82, 0xB9, 0x4C, 0xE3, 0x83, 0x8F,
	0xE3, 0x82, 0x9A, 0xE3, 0x83, 0xBC, 0xE3, 0x83,
	0x84, 0x4C, 0xE3, 0x83, 0x92, 0xE3, 0x82, 0x9A,
	0xE3, 0x82, 0xAF, 0xE3, 0x83, 0xAB, 0x4C, 0xE3,
	0x83, 0x95, 0xE3, 0x82, 0xA3, 0xE3, 0x83, 0xBC,
	0xE3, 0x83, 0x88, 0x4C, 0xE3, 0x83, 0x98, 0xE3,
	0x82, 0x99, 0xE3, 0x83, 0xBC, 0xE3, 0x82, 0xBF,
	0x4C, 0xE3, 0x83, 0x98, 0xE3, 0x82, 0x9A, 0xE3,
	// Bytes 2a80 - 2abf
	0x83, 0x8B, 0xE3, 0x83, 0x92, 0x4C, 0xE3, 0x83,
	0x98, 0xE3, 0x82, 0x9A, 0xE3, 0x83, 0xB3, 0xE3,
	0x82, 0xB9, 0x4C, 0xE3, 0x83, 0x9B, 0xE3, 0x82,
	0x99, 0xE3, 0x83, 0xAB, 0xE3, 0x83, 0x88, 0x4C,
	0xE3, 0x83, 0x9E, 0xE3, 0x82, 0xA4, 0xE3, 0x82,
	0xAF, 0xE3, 0x83, 0xAD, 0x4C, 0xE3, 0x83, 0x9F,
	0xE3, 0x82, 0xAF, 0xE3, 0x83, 0xAD, 0xE3, 0x83,
	0xB3, 0x4C, 0xE3, 0x83, 0xA1, 0xE3, 0x83, 0xBC,
	// Bytes 2ac0 - 2aff
	0xE3, 0x83, 0x88, 0xE3, 0x83, 0xAB, 0x4C, 0xE3,
	0x83, 0xAA, 0xE3, 0x83, 0x83, 0xE3, 0x83, 0x88,
	0xE3, 0x83, 0xAB, 0x4C, 0xE3, 0x83, 0xAB, 0xE3,
	0x83, 0x92, 0xE3, 0x82, 0x9A, 0xE3, 0x83, 0xBC,
	0x4C, 0xE6, 0xA0, 0xAA, 0xE5, 0xBC, 0x8F, 0xE4,
	0xBC, 0x9A, 0xE7, 0xA4, 0xBE, 0x4E, 0x28, 0xE1,
	0x84, 0x8B, 0xE1, 0x85, 0xA9, 0xE1, 0x84, 0x92,
	0xE1, 0x85, 0xAE, 0x29, 0x4F, 0xD8, 0xAC, 0xD9,
	// Bytes 2b00 - 2b3f
	0x84, 0x20, 0xD8, 0xAC, 0xD9, 0x84, 0xD8, 0xA7,
	0xD9, 0x84, 0xD9, 0x87, 0x4F, 0xE3, 0x82, 0xA2,
	0xE3, 0x83, 0x8F, 0xE3, 0x82, 0x9A, 0xE3, 0x83,
	0xBC, 0xE3, 0x83, 0x88, 0x4F, 0xE3, 0x82, 0xA2,
	0xE3, 0x83, 0xB3, 0xE3, 0x83, 0x98, 0xE3, 0x82,
	0x9A, 0xE3, 0x82, 0xA2, 0x4F, 0xE3, 0x82, 0xAD,
	0xE3, 0x83, 0xAD, 0xE3, 0x83, 0xAF, 0xE3, 0x83,
	0x83, 0xE3, 0x83, 0x88, 0x4F, 0xE3, 0x82, 0xB5,
	// Bytes 2b40 - 2b7f
	0xE3, 0x83, 0xB3, 0xE3, 0x83, 0x81, 0xE3, 0x83,
	0xBC, 0xE3, 0x83, 0xA0, 0x4F, 0xE3, 0x83, 0x8F,
	0xE3, 0x82, 0x99, 0xE3, 0x83, 0xBC, 0xE3, 0x83,
	0xAC, 0xE3, 0x83, 0xAB, 0x4F, 0xE3, 0x83, 0x98,
	0xE3, 0x82, 0xAF, 0xE3, 0x82, 0xBF, 0xE3, 0x83,
	0xBC, 0xE3, 0x83, 0xAB, 0x4F, 0xE3, 0x83, 0x9B,
	0xE3, 0x82, 0x9A, 0xE3, 0x82, 0xA4, 0xE3, 0x83,
	0xB3, 0xE3, 0x83, 0x88, 0x4F, 0xE3, 0x83, 0x9E,
	// Bytes 2b80 - 2bbf
	0xE3, 0x83, 0xB3, 0xE3, 0x82, 0xB7, 0xE3, 0x83,
	0xA7, 0xE3, 0x83, 0xB3, 0x4F, 0xE3, 0x83, 0xA1,
	0xE3, 0x82, 0xAB, 0xE3, 0x82, 0x99, 0xE3, 0x83,
	0x88, 0xE3, 0x83, 0xB3, 0x4F, 0xE3, 0x83, 0xAB,
	0xE3, 0x83, 0xBC, 0xE3, 0x83, 0x95, 0xE3, 0x82,
	0x99, 0xE3, 0x83, 0xAB, 0x51, 0x28, 0xE1, 0x84,
	0x8B, 0xE1, 0x85, 0xA9, 0xE1, 0x84, 0x8C, 0xE1,
	0x85, 0xA5, 0xE1, 0x86, 0xAB, 0x29, 0x52, 0xE3,
	// Bytes 2bc0 - 2bff
	0x82, 0xAD, 0xE3, 0x82, 0x99, 0xE3, 0x83, 0xAB,
	0xE3, 0x82, 0xBF, 0xE3, 0x82, 0x99, 0xE3, 0x83,
	0xBC, 0x52, 0xE3, 0x82, 0xAD, 0xE3, 0x83, 0xAD,
	0xE3, 0x82, 0xAF, 0xE3, 0x82, 0x99, 0xE3, 0x83,
	0xA9, 0xE3, 0x83, 0xA0, 0x52, 0xE3, 0x82, 0xAD,
	0xE3, 0x83, 0xAD, 0xE3, 0x83, 0xA1, 0xE3, 0x83,
	0xBC, 0xE3, 0x83, 0x88, 0xE3, 0x83, 0xAB, 0x52,
	0xE3, 0x82, 0xAF, 0xE3, 0x82, 0x99, 0xE3, 0x83,
	// Bytes 2c00 - 2c3f
	0xA9, 0xE3, 0x83, 0xA0, 0xE3, 0x83, 0x88, 0xE3,
	0x83, 0xB3, 0x52, 0xE3, 0x82, 0xAF, 0xE3, 0x83,
	0xAB, 0xE3, 0x82, 0xBB, 0xE3, 0x82, 0x99, 0xE3,
	0x82, 0xA4, 0xE3, 0x83, 0xAD, 0x52, 0xE3, 0x83,
	0x8F, 0xE3, 0x82, 0x9A, 0xE3, 0x83, 0xBC, 0xE3,
	0x82, 0xBB, 0xE3, 0x83, 0xB3, 0xE3, 0x83, 0x88,
	0x52, 0xE3, 0x83, 0x92, 0xE3, 0x82, 0x9A, 0xE3,
	0x82, 0xA2, 0xE3, 0x82, 0xB9, 0xE3, 0x83, 0x88,
	// Bytes 2c40 - 2c7f
	0xE3, 0x83, 0xAB, 0x52, 0xE3, 0x83, 0x95, 0xE3,
	0x82, 0x99, 0xE3, 0x83, 0x83, 0xE3, 0x82, 0xB7,
	0xE3, 0x82, 0xA7, 0xE3, 0x83, 0xAB, 0x52, 0xE3,
	0x83, 0x9F, 0xE3, 0x83, 0xAA, 0xE3, 0x83, 0x8F,
	0xE3, 0x82, 0x99, 0xE3, 0x83, 0xBC, 0xE3, 0x83,
	0xAB, 0x52, 0xE3, 0x83, 0xAC, 0xE3, 0x83, 0xB3,
	0xE3, 0x83, 0x88, 0xE3, 0x82, 0xB1, 0xE3, 0x82,
	0x99, 0xE3, 0x83, 0xB3, 0x61, 0xD8, 0xB5, 0xD9,
	// Bytes 2c80 - 2cbf
	0x84, 0xD9, 0x89, 0x20, 0xD8, 0xA7, 0xD9, 0x84,
	0xD9, 0x84, 0xD9, 0x87, 0x20, 0xD8, 0xB9, 0xD9,
	0x84, 0xD9, 0x8A, 0xD9, 0x87, 0x20, 0xD9, 0x88,
	0xD8, 0xB3, 0xD9, 0x84, 0xD9, 0x85, 0x06, 0xE0,
	0xA7, 0x87, 0xE0, 0xA6, 0xBE, 0x01, 0x06, 0xE0,
	0xA7, 0x87, 0xE0, 0xA7, 0x97, 0x01, 0x06, 0xE0,
	0xAD, 0x87, 0xE0, 0xAC, 0xBE, 0x01, 0x06, 0xE0,
	0xAD, 0x87, 0xE0, 0xAD, 0x96, 0x01, 0x06, 0xE0,
	// Bytes 2cc0 - 2cff
	0xAD, 0x87, 0xE0, 0xAD, 0x97, 0x01, 0x06, 0xE0,
	0xAE, 0x92, 0xE0, 0xAF, 0x97, 0x01, 0x06, 0xE0,
	0xAF, 0x86, 0xE0, 0xAE, 0xBE, 0x01, 0x06, 0xE0,
	0xAF, 0x86, 0xE0, 0xAF, 0x97, 0x01, 0x06, 0xE0,
	0xAF, 0x87, 0xE0, 0xAE, 0xBE, 0x01, 0x06, 0xE0,
	0xB2, 0xBF, 0xE0, 0xB3, 0x95, 0x01, 0x06, 0xE0,
	0xB3, 0x86, 0xE0, 0xB3, 0x95, 0x01, 0x06, 0xE0,
	0xB3, 0x86, 0xE0, 0xB3, 0x96, 0x01, 0x06, 0xE0,
	// Bytes 2d00 - 2d3f
	0xB5, 0x86, 0xE0, 0xB4, 0xBE, 0x01, 0x06, 0xE0,
	0xB5, 0x86, 0xE0, 0xB5, 0x97, 0x01, 0x06, 0xE0,
	0xB5, 0x87, 0xE0, 0xB4, 0xBE, 0x01, 0x06, 0xE0,
	0xB7, 0x99, 0xE0, 0xB7, 0x9F, 0x01, 0x06, 0xE1,
	0x80, 0xA5, 0xE1, 0x80, 0xAE, 0x01, 0x06, 0xE1,
	0xAC, 0x85, 0xE1, 0xAC, 0xB5, 0x01, 0x06, 0xE1,
	0xAC, 0x87, 0xE1, 0xAC, 0xB5, 0x01, 0x06, 0xE1,
	0xAC, 0x89, 0xE1, 0xAC, 0xB5, 0x01, 0x06, 0xE1,
	// Bytes 2d40 - 2d7f
	0xAC, 0x8B, 0xE1, 0xAC, 0xB5, 0x01, 0x06, 0xE1,
	0xAC, 0x8D, 0xE1, 0xAC, 0xB5, 0x01, 0x06, 0xE1,
	0xAC, 0x91, 0xE1, 0xAC, 0xB5, 0x01, 0x06, 0xE1,
	0xAC, 0xBA, 0xE1, 0xAC, 0xB5, 0x01, 0x06, 0xE1,
	0xAC, 0xBC, 0xE1, 0xAC, 0xB5, 0x01, 0x06, 0xE1,
	0xAC, 0xBE, 0xE1, 0xAC, 0xB5, 0x01, 0x06, 0xE1,
	0xAC, 0xBF, 0xE1, 0xAC, 0xB5, 0x01, 0x06, 0xE1,
	0xAD, 0x82, 0xE1, 0xAC, 0xB5, 0x01, 0x08, 0xF0,
	// Bytes 2d80 - 2dbf
	0x91, 0x84, 0xB1, 0xF0, 0x91, 0x84, 0xA7, 0x01,
	0x08, 0xF0, 0x91, 0x84, 0xB2, 0xF0, 0x91, 0x84,
	0xA7, 0x01, 0x08, 0xF0, 0x91, 0x8D, 0x87, 0xF0,
	0x91, 0x8C, 0xBE, 0x01, 0x08, 0xF0, 0x91, 0x8D,
	0x87, 0xF0, 0x91, 0x8D, 0x97, 0x01, 0x08, 0xF0,
	0x91, 0x92, 0xB9, 0xF0, 0x91, 0x92, 0xB0, 0x01,
	0x08, 0xF0, 0x91, 0x92, 0xB9, 0xF0, 0x91, 0x92,
	0xBA, 0x01, 0x08, 0xF0, 0x91, 0x92, 0xB9, 0xF0,
	// Bytes 2dc0 - 2dff
	0x91, 0x92, 0xBD, 0x01, 0x08, 0xF0, 0x91, 0x96,
	0xB8, 0xF0, 0x91, 0x96, 0xAF, 0x01, 0x08, 0xF0,
	0x91, 0x96, 0xB9, 0xF0, 0x91, 0x96, 0xAF, 0x01,
	0x09, 0xE0, 0xB3, 0x86, 0xE0, 0xB3, 0x82, 0xE0,
	0xB3, 0x95, 0x02, 0x09, 0xE0, 0xB7, 0x99, 0xE0,
	0xB7, 0x8F, 0xE0, 0xB7, 0x8A, 0x12, 0x44, 0x44,
	0x5A, 0xCC, 0x8C, 0xC9, 0x44, 0x44, 0x7A, 0xCC,
	0x8C, 0xC9, 0x44, 0x64, 0x7A, 0xCC, 0x8C, 0xC9,
	// Bytes 2e00 - 2e3f
	0x46, 0xD9, 0x84, 0xD8, 0xA7, 0xD9, 0x93, 0xC9,
	0x46, 0xD9, 0x84, 0xD8, 0xA7, 0xD9, 0x94, 0xC9,
	0x46, 0xD9, 0x84, 0xD8, 0xA7, 0xD9, 0x95, 0xB5,
	0x46, 0xE1, 0x84, 0x80, 0xE1, 0x85, 0xA1, 0x01,
	0x46, 0xE1, 0x84, 0x82, 0xE1, 0x85, 0xA1, 0x01,
	0x46, 0xE1, 0x84, 0x83, 0xE1, 0x85, 0xA1, 0x01,
	0x46, 0xE1, 0x84, 0x85, 0xE1, 0x85, 0xA1, 0x01,
	0x46, 0xE1, 0x84, 0x86, 0xE1, 0x85, 0xA1, 0x01,
	// Bytes 2e40 - 2e7f
	0x46, 0xE1, 0x84, 0x87, 0xE1, 0x85, 0xA1, 0x01,
	0x46, 0xE1, 0x84, 0x89, 0xE1, 0x85, 0xA1, 0x01,
	0x46, 0xE1, 0x84, 0x8B, 0xE1, 0x85, 0xA1, 0x01,
	0x46, 0xE1, 0x84, 0x8B, 0xE1, 0x85, 0xAE, 0x01,
	0x46, 0xE1, 0x84, 0x8C, 0xE1, 0x85, 0xA1, 0x01,
	0x46, 0xE1, 0x84, 0x8E, 0xE1, 0x85, 0xA1, 0x01,
	0x46, 0xE1, 0x84, 0x8F, 0xE1, 0x85, 0xA1, 0x01,
	0x46, 0xE1, 0x84, 0x90, 0xE1, 0x85, 0xA1, 0x01,
	// Bytes 2e80 - 2ebf
	0x46, 0xE1, 0x84, 0x91, 0xE1, 0x85, 0xA1, 0x01,
	0x46, 0xE1, 0x84, 0x92, 0xE1, 0x85, 0xA1, 0x01,
	0x49, 0xE3, 0x83, 0xA1, 0xE3, 0x82, 0xAB, 0xE3,
	0x82, 0x99, 0x0D, 0x4C, 0xE1, 0x84, 0x8C, 0xE1,
	0x85, 0xAE, 0xE1, 0x84, 0x8B, 0xE1, 0x85, 0xB4,
	0x01, 0x4C, 0xE3, 0x82, 0xAD, 0xE3, 0x82, 0x99,
	0xE3, 0x82, 0xAB, 0xE3, 0x82, 0x99, 0x0D, 0x4C,
	0xE3, 0x82, 0xB3, 0xE3, 0x83, 0xBC, 0xE3, 0x83,
	// Bytes 2ec0 - 2eff
	0x9B, 0xE3, 0x82, 0x9A, 0x0D, 0x4C, 0xE3, 0x83,
	0xA4, 0xE3, 0x83, 0xBC, 0xE3, 0x83, 0x88, 0xE3,
	0x82, 0x99, 0x0D, 0x4F, 0xE1, 0x84, 0x8E, 0xE1,
	0x85, 0xA1, 0xE1, 0x86, 0xB7, 0xE1, 0x84, 0x80,
	0xE1, 0x85, 0xA9, 0x01, 0x4F, 0xE3, 0x82, 0xA4,
	0xE3, 0x83, 0x8B, 0xE3, 0x83, 0xB3, 0xE3, 0x82,
	0xAF, 0xE3, 0x82, 0x99, 0x0D, 0x4F, 0xE3, 0x82,
	0xB7, 0xE3, 0x83, 0xAA, 0xE3, 0x83, 0xB3, 0xE3,
	// Bytes 2f00 - 2f3f
	0x82, 0xAF, 0xE3, 0x82, 0x99, 0x0D, 0x4F, 0xE3,
	0x83, 0x98, 0xE3, 0x82, 0x9A, 0xE3, 0x83, 0xBC,
	0xE3, 0x82, 0xB7, 0xE3, 0x82, 0x99, 0x0D, 0x4F,
	0xE3, 0x83, 0x9B, 0xE3, 0x82, 0x9A, 0xE3, 0x83,
	0xB3, 0xE3, 0x83, 0x88, 0xE3, 0x82, 0x99, 0x0D,
	0x52, 0xE3, 0x82, 0xA8, 0xE3, 0x82, 0xB9, 0xE3,
	0x82, 0xAF, 0xE3, 0x83, 0xBC, 0xE3, 0x83, 0x88,
	0xE3, 0x82, 0x99, 0x0D, 0x52, 0xE3, 0x83, 0x95,
	// Bytes 2f40 - 2f7f
	0xE3, 0x82, 0xA1, 0xE3, 0x83, 0xA9, 0xE3, 0x83,
	0x83, 0xE3, 0x83, 0x88, 0xE3, 0x82, 0x99, 0x0D,
	0x86, 0xE0, 0xB3, 0x86, 0xE0, 0xB3, 0x82, 0x01,
	0x86, 0xE0, 0xB7, 0x99, 0xE0, 0xB7, 0x8F, 0x01,
	0x03, 0x3C, 0xCC, 0xB8, 0x05, 0x03, 0x3D, 0xCC,
	0xB8, 0x05, 0x03, 0x3E, 0xCC, 0xB8, 0x05, 0x03,
	0x41, 0xCC, 0x80, 0xC9, 0x03, 0x41, 0xCC, 0x81,
	0xC9, 0x03, 0x41, 0xCC, 0x83, 0xC9, 0x03, 0x41,
	// Bytes 2f80 - 2fbf
	0xCC, 0x84, 0xC9, 0x03, 0x41, 0xCC, 0x89, 0xC9,
	0x03, 0x41, 0xCC, 0x8C, 0xC9, 0x03, 0x41, 0xCC,
	0x8F, 0xC9, 0x03, 0x41, 0xCC, 0x91, 0xC9, 0x03,
	0x41, 0xCC, 0xA5, 0xB5, 0x03, 0x41, 0xCC, 0xA8,
	0xA5, 0x03, 0x42, 0xCC, 0x87, 0xC9, 0x03, 0x42,
	0xCC, 0xA3, 0xB5, 0x03, 0x42, 0xCC, 0xB1, 0xB5,
	0x03, 0x43, 0xCC, 0x81, 0xC9, 0x03, 0x43, 0xCC,
	0x82, 0xC9, 0x03, 0x43, 0xCC, 0x87, 0xC9, 0x03,
	// Bytes 2fc0 - 2fff
	0x43, 0xCC, 0x8C, 0xC9, 0x03, 0x44, 0xCC, 0x87,
	0xC9, 0x03, 0x44, 0xCC, 0x8C, 0xC9, 0x03, 0x44,
	0xCC, 0xA3, 0xB5, 0x03, 0x44, 0xCC, 0xA7, 0xA5,
	0x03, 0x44, 0xCC, 0xAD, 0xB5, 0x03, 0x44, 0xCC,
	0xB1, 0xB5, 0x03, 0x45, 0xCC, 0x80, 0xC9, 0x03,
	0x45, 0xCC, 0x81, 0xC9, 0x03, 0x45, 0xCC, 0x83,
	0xC9, 0x03, 0x45, 0xCC, 0x86, 0xC9, 0x03, 0x45,
	0xCC, 0x87, 0xC9, 0x03, 0x45, 0xCC, 0x88, 0xC9,
	// Bytes 3000 - 303f
	0x03, 0x45, 0xCC, 0x89, 0xC9, 0x03, 0x45, 0xCC,
	0x8C, 0xC9, 0x03, 0x45, 0xCC, 0x8F, 0xC9, 0x03,
	0x45, 0xCC, 0x91, 0xC9, 0x03, 0x45, 0xCC, 0xA8,
	0xA5, 0x03, 0x45, 0xCC, 0xAD, 0xB5, 0x03, 0x45,
	0xCC, 0xB0, 0xB5, 0x03, 0x46, 0xCC, 0x87, 0xC9,
	0x03, 0x47, 0xCC, 0x81, 0xC9, 0x03, 0x47, 0xCC,
	0x82, 0xC9, 0x03, 0x47, 0xCC, 0x84, 0xC9, 0x03,
	0x47, 0xCC, 0x86, 0xC9, 0x03, 0x47, 0xCC, 0x87,
	// Bytes 3040 - 307f
	0xC9, 0x03, 0x47, 0xCC, 0x8C, 0xC9, 0x03, 0x47,
	0xCC, 0xA7, 0xA5, 0x03, 0x48, 0xCC, 0x82, 0xC9,
	0x03, 0x48, 0xCC, 0x87, 0xC9, 0x03, 0x48, 0xCC,
	0x88, 0xC9, 0x03, 0x48, 0xCC, 0x8C, 0xC9, 0x03,
	0x48, 0xCC, 0xA3, 0xB5, 0x03, 0x48, 0xCC, 0xA7,
	0xA5, 0x03, 0x48, 0xCC, 0xAE, 0xB5, 0x03, 0x49,
	0xCC, 0x80, 0xC9, 0x03, 0x49, 0xCC, 0x81, 0xC9,
	0x03, 0x49, 0xCC, 0x82, 0xC9, 0x03, 0x49, 0xCC,
	// Bytes 3080 - 30bf
	0x83, 0xC9, 0x03, 0x49, 0xCC, 0x84, 0xC9, 0x03,
	0x49, 0xCC, 0x86, 0xC9, 0x03, 0x49, 0xCC, 0x87,
	0xC9, 0x03, 0x49, 0xCC, 0x89, 0xC9, 0x03, 0x49,
	0xCC, 0x8C, 0xC9, 0x03, 0x49, 0xCC, 0x8F, 0xC9,
	0x03, 0x49, 0xCC, 0x91, 0xC9, 0x03, 0x49, 0xCC,
	0xA3, 0xB5, 0x03, 0x49, 0xCC, 0xA8, 0xA5, 0x03,
	0x49, 0xCC, 0xB0, 0xB5, 0x03, 0x4A, 0xCC, 0x82,
	0xC9, 0x03, 0x4B, 0xCC, 0x81, 0xC9, 0x03, 0x4B,
	// Bytes 30c0 - 30ff
	0xCC, 0x8C, 0xC9, 0x03, 0x4B, 0xCC, 0xA3, 0xB5,
	0x03, 0x4B, 0xCC, 0xA7, 0xA5, 0x03, 0x4B, 0xCC,
	0xB1, 0xB5, 0x03, 0x4C, 0xCC, 0x81, 0xC9, 0x03,
	0x4C, 0xCC, 0x8C, 0xC9, 0x03, 0x4C, 0xCC, 0xA7,
	0xA5, 0x03, 0x4C, 0xCC, 0xAD, 0xB5, 0x03, 0x4C,
	0xCC, 0xB1, 0xB5, 0x03, 0x4D, 0xCC, 0x81, 0xC9,
	0x03, 0x4D, 0xCC, 0x87, 0xC9, 0x03, 0x4D, 0xCC,
	0xA3, 0xB5, 0x03, 0x4E, 0xCC, 0x80, 0xC9, 0x03,
	// Bytes 3100 - 313f
	0x4E, 0xCC, 0x81, 0xC9, 0x03, 0x4E, 0xCC, 0x83,
	0xC9, 0x03, 0x4E, 0xCC, 0x87, 0xC9, 0x03, 0x4E,
	0xCC, 0x8C, 0xC9, 0x03, 0x4E, 0xCC, 0xA3, 0xB5,
	0x03, 0x4E, 0xCC, 0xA7, 0xA5, 0x03, 0x4E, 0xCC,
	0xAD, 0xB5, 0x03, 0x4E, 0xCC, 0xB1, 0xB5, 0x03,
	0x4F, 0xCC, 0x80, 0xC9, 0x03, 0x4F, 0xCC, 0x81,
	0xC9, 0x03, 0x4F, 0xCC, 0x86, 0xC9, 0x03, 0x4F,
	0xCC, 0x89, 0xC9, 0x03, 0x4F, 0xCC, 0x8B, 0xC9,
	// Bytes 3140 - 317f
	0x03, 0x4F, 0xCC, 0x8C, 0xC9, 0x03, 0x4F, 0xCC,
	0x8F, 0xC9, 0x03, 0x4F, 0xCC, 0x91, 0xC9, 0x03,
	0x50, 0xCC, 0x81, 0xC9, 0x03, 0x50, 0xCC, 0x87,
	0xC9, 0x03, 0x52, 0xCC, 0x81, 0xC9, 0x03, 0x52,
	0xCC, 0x87, 0xC9, 0x03, 0x52, 0xCC, 0x8C, 0xC9,
	0x03, 0x52, 0xCC, 0x8F, 0xC9, 0x03, 0x52, 0xCC,
	0x91, 0xC9, 0x03, 0x52, 0xCC, 0xA7, 0xA5, 0x03,
	0x52, 0xCC, 0xB1, 0xB5, 0x03, 0x53, 0xCC, 0x82,
	// Bytes 3180 - 31bf
	0xC9, 0x03, 0x53, 0xCC, 0x87, 0xC9, 0x03, 0x53,
	0xCC, 0xA6, 0xB5, 0x03, 0x53, 0xCC, 0xA7, 0xA5,
	0x03, 0x54, 0xCC, 0x87, 0xC9, 0x03, 0x54, 0xCC,
	0x8C, 0xC9, 0x03, 0x54, 0xCC, 0xA3, 0xB5, 0x03,
	0x54, 0xCC, 0xA6, 0xB5, 0x03, 0x54, 0xCC, 0xA7,
	0xA5, 0x03, 0x54, 0xCC, 0xAD, 0xB5, 0x03, 0x54,
	0xCC, 0xB1, 0xB5, 0x03, 0x55, 0xCC, 0x80, 0xC9,
	0x03, 0x55, 0xCC, 0x81, 0xC9, 0x03, 0x55, 0xCC,
	// Bytes 31c0 - 31ff
	0x82, 0xC9, 0x03, 0x55, 0xCC, 0x86, 0xC9, 0x03,
	0x55, 0xCC, 0x89, 0xC9, 0x03, 0x55, 0xCC, 0x8A,
	0xC9, 0x03, 0x55, 0xCC, 0x8B, 0xC9, 0x03, 0x55,
	0xCC, 0x8C, 0xC9, 0x03, 0x55, 0xCC, 0x8F, 0xC9,
	0x03, 0x55, 0xCC, 0x91, 0xC9, 0x03, 0x55, 0xCC,
	0xA3, 0xB5, 0x03, 0x55, 0xCC, 0xA4, 0xB5, 0x03,
	0x55, 0xCC, 0xA8, 0xA5, 0x03, 0x55, 0xCC, 0xAD,
	0xB5, 0x03, 0x55, 0xCC, 0xB0, 0xB5, 0x03, 0x56,
	// Bytes 3200 - 323f
	0xCC, 0x83, 0xC9, 0x03, 0x56, 0xCC, 0xA3, 0xB5,
	0x03, 0x57, 0xCC, 0x80, 0xC9, 0x03, 0x57, 0xCC,
	0x81, 0xC9, 0x03, 0x57, 0xCC, 0x82, 0xC9, 0x03,
	0x57, 0xCC, 0x87, 0xC9, 0x03, 0x57, 0xCC, 0x88,
	0xC9, 0x03, 0x57, 0xCC, 0xA3, 0xB5, 0x03, 0x58,
	0xCC, 0x87, 0xC9, 0x03, 0x58, 0xCC, 0x88, 0xC9,
	0x03, 0x59, 0xCC, 0x80, 0xC9, 0x03, 0x59, 0xCC,
	0x81, 0xC9, 0x03, 0x59, 0xCC, 0x82, 0xC9, 0x03,
	// Bytes 3240 - 327f
	0x59, 0xCC, 0x83, 0xC9, 0x03, 0x59, 0xCC, 0x84,
	0xC9, 0x03, 0x59, 0xCC, 0x87, 0xC9, 0x03, 0x59,
	0xCC, 0x88, 0xC9, 0x03, 0x59, 0xCC, 0x89, 0xC9,
	0x03, 0x59, 0xCC, 0xA3, 0xB5, 0x03, 0x5A, 0xCC,
	0x81, 0xC9, 0x03, 0x5A, 0xCC, 0x82, 0xC9, 0x03,
	0x5A, 0xCC, 0x87, 0xC9, 0x03, 0x5A, 0xCC, 0x8C,
	0xC9, 0x03, 0x5A, 0xCC, 0xA3, 0xB5, 0x03, 0x5A,
	0xCC, 0xB1, 0xB5, 0x03, 0x61, 0xCC, 0x80, 0xC9,
	// Bytes 3280 - 32bf
	0x03, 0x61, 0xCC, 0x81, 0xC9, 0x03, 0x61, 0xCC,
	0x83, 0xC9, 0x03, 0x61, 0xCC, 0x84, 0xC9, 0x03,
	0x61, 0xCC, 0x89, 0xC9, 0x03, 0x61, 0xCC, 0x8C,
	0xC9, 0x03, 0x61, 0xCC, 0x8F, 0xC9, 0x03, 0x61,
	0xCC, 0x91, 0xC9, 0x03, 0x61, 0xCC, 0xA5, 0xB5,
	0x03, 0x61, 0xCC, 0xA8, 0xA5, 0x03, 0x62, 0xCC,
	0x87, 0xC9, 0x03, 0x62, 0xCC, 0xA3, 0xB5, 0x03,
	0x62, 0xCC, 0xB1, 0xB5, 0x03, 0x63, 0xCC, 0x81,
	// Bytes 32c0 - 32ff
	0xC9, 0x03, 0x63, 0xCC, 0x82, 0xC9, 0x03, 0x63,
	0xCC, 0x87, 0xC9, 0x03, 0x63, 0xCC, 0x8C, 0xC9,
	0x03, 0x64, 0xCC, 0x87, 0xC9, 0x03, 0x64, 0xCC,
	0x8C, 0xC9, 0x03, 0x64, 0xCC, 0xA3, 0xB5, 0x03,
	0x64, 0xCC, 0xA7, 0xA5, 0x03, 0x64, 0xCC, 0xAD,
	0xB5, 0x03, 0x64, 0xCC, 0xB1, 0xB5, 0x03, 0x65,
	0xCC, 0x80, 0xC9, 0x03, 0x65, 0xCC, 0x81, 0xC9,
	0x03, 0x65, 0xCC, 0x83, 0xC9, 0x03, 0x65, 0xCC,
	// Bytes 3300 - 333f
	0x86, 0xC9, 0x03, 0x65, 0xCC, 0x87, 0xC9, 0x03,
	0x65, 0xCC, 0x88, 0xC9, 0x03, 0x65, 0xCC, 0x89,
	0xC9, 0x03, 0x65, 0xCC, 0x8C, 0xC9, 0x03, 0x65,
	0xCC, 0x8F, 0xC9, 0x03, 0x65, 0xCC, 0x91, 0xC9,
	0x03, 0x65, 0xCC, 0xA8, 0xA5, 0x03, 0x65, 0xCC,
	0xAD, 0xB5, 0x03, 0x65, 0xCC, 0xB0, 0xB5, 0x03,
	0x66, 0xCC, 0x87, 0xC9, 0x03, 0x67, 0xCC, 0x81,
	0xC9, 0x03, 0x67, 0xCC, 0x82, 0xC9, 0x03, 0x67,
	// Bytes 3340 - 337f
	0xCC, 0x84, 0xC9, 0x03, 0x67, 0xCC, 0x86, 0xC9,
	0x03, 0x67, 0xCC, 0x87, 0xC9, 0x03, 0x67, 0xCC,
	0x8C, 0xC9, 0x03, 0x67, 0xCC, 0xA7, 0xA5, 0x03,
	0x68, 0xCC, 0x82, 0xC9, 0x03, 0x68, 0xCC, 0x87,
	0xC9, 0x03, 0x68, 0xCC, 0x88, 0xC9, 0x03, 0x68,
	0xCC, 0x8C, 0xC9, 0x03, 0x68, 0xCC, 0xA3, 0xB5,
	0x03, 0x68, 0xCC, 0xA7, 0xA5, 0x03, 0x68, 0xCC,
	0xAE, 0xB5, 0x03, 0x68, 0xCC, 0xB1, 0xB5, 0x03,
	// Bytes 3380 - 33bf
	0x69, 0xCC, 0x80, 0xC9, 0x03, 0x69, 0xCC, 0x81,
	0xC9, 0x03, 0x69, 0xCC, 0x82, 0xC9, 0x03, 0x69,
	0xCC, 0x83, 0xC9, 0x03, 0x69, 0xCC, 0x84, 0xC9,
	0x03, 0x69, 0xCC, 0x86, 0xC9, 0x03, 0x69, 0xCC,
	0x89, 0xC9, 0x03, 0x69, 0xCC, 0x8C, 0xC9, 0x03,
	0x69, 0xCC, 0x8F, 0xC9, 0x03, 0x69, 0xCC, 0x91,
	0xC9, 0x03, 0x69, 0xCC, 0xA3, 0xB5, 0x03, 0x69,
	0xCC, 0xA8, 0xA5, 0x03, 0x69, 0xCC, 0xB0, 0xB5,
	// Bytes 33c0 - 33ff
	0x03, 0x6A, 0xCC, 0x82, 0xC9, 0x03, 0x6A, 0xCC,
	0x8C, 0xC9, 0x03, 0x6B, 0xCC, 0x81, 0xC9, 0x03,
	0x6B, 0xCC, 0x8C, 0xC9, 0x03, 0x6B, 0xCC, 0xA3,
	0xB5, 0x03, 0x6B, 0xCC, 0xA7, 0xA5, 0x03, 0x6B,
	0xCC, 0xB1, 0xB5, 0x03, 0x6C, 0xCC, 0x81, 0xC9,
	0x03, 0x6C, 0xCC, 0x8C, 0xC9, 0x03, 0x6C, 0xCC,
	0xA7, 0xA5, 0x03, 0x6C, 0xCC, 0xAD, 0xB5, 0x03,
	0x6C, 0xCC, 0xB1, 0xB5, 0x03, 0x6D, 0xCC, 0x81,
	// Bytes 3400 - 343f
	0xC9, 0x03, 0x6D, 0xCC, 0x87, 0xC9, 0x03, 0x6D,
	0xCC, 0xA3, 0xB5, 0x03, 0x6E, 0xCC, 0x80, 0xC9,
	0x03, 0x6E, 0xCC, 0x81, 0xC9, 0x03, 0x6E, 0xCC,
	0x83, 0xC9, 0x03, 0x6E, 0xCC, 0x87, 0xC9, 0x03,
	0x6E, 0xCC, 0x8C, 0xC9, 0x03, 0x6E, 0xCC, 0xA3,
	0xB5, 0x03, 0x6E, 0xCC, 0xA7, 0xA5, 0x03, 0x6E,
	0xCC, 0xAD, 0xB5, 0x03, 0x6E, 0xCC, 0xB1, 0xB5,
	0x03, 0x6F, 0xCC, 0x80, 0xC9, 0x03, 0x6F, 0xCC,
	// Bytes 3440 - 347f
	0x81, 0xC9, 0x03, 0x6F, 0xCC, 0x86, 0xC9, 0x03,
	0x6F, 0xCC, 0x89, 0xC9, 0x03, 0x6F, 0xCC, 0x8B,
	0xC9, 0x03, 0x6F, 0xCC, 0x8C, 0xC9, 0x03, 0x6F,
	0xCC, 0x8F, 0xC9, 0x03, 0x6F, 0xCC, 0x91, 0xC9,
	0x03, 0x70, 0xCC, 0x81, 0xC9, 0x03, 0x70, 0xCC,
	0x87, 0xC9, 0x03, 0x72, 0xCC, 0x81, 0xC9, 0x03,
	0x72, 0xCC, 0x87, 0xC9, 0x03, 0x72, 0xCC, 0x8C,
	0xC9, 0x03, 0x72, 0xCC, 0x8F, 0xC9, 0x03, 0x72,
	// Bytes 3480 - 34bf
	0xCC, 0x91, 0xC9, 0x03, 0x72, 0xCC, 0xA7, 0xA5,
	0x03, 0x72, 0xCC, 0xB1, 0xB5, 0x03, 0x73, 0xCC,
	0x82, 0xC9, 0x03, 0x73, 0xCC, 0x87, 0xC9, 0x03,
	0x73, 0xCC, 0xA6, 0xB5, 0x03, 0x73, 0xCC, 0xA7,
	0xA5, 0x03, 0x74, 0xCC, 0x87, 0xC9, 0x03, 0x74,
	0xCC, 0x88, 0xC9, 0x03, 0x74, 0xCC, 0x8C, 0xC9,
	0x03, 0x74, 0xCC, 0xA3, 0xB5, 0x03, 0x74, 0xCC,
	0xA6, 0xB5, 0x03, 0x74, 0xCC, 0xA7, 0xA5, 0x03,
	// Bytes 34c0 - 34ff
	0x74, 0xCC, 0xAD, 0xB5, 0x03, 0x74, 0xCC, 0xB1,
	0xB5, 0x03, 0x75, 0xCC, 0x80, 0xC9, 0x03, 0x75,
	0xCC, 0x81, 0xC9, 0x03, 0x75, 0xCC, 0x82, 0xC9,
	0x03, 0x75, 0xCC, 0x86, 0xC9, 0x03, 0x75, 0xCC,
	0x89, 0xC9, 0x03, 0x75, 0xCC, 0x8A, 0xC9, 0x03,
	0x75, 0xCC, 0x8B, 0xC9, 0x03, 0x75, 0xCC, 0x8C,
	0xC9, 0x03, 0x75, 0xCC, 0x8F, 0xC9, 0x03, 0x75,
	0xCC, 0x91, 0xC9, 0x03, 0x75, 0xCC, 0xA3, 0xB5,
	// Bytes 3500 - 353f
	0x03, 0x75, 0xCC, 0xA4, 0xB5, 0x03, 0x75, 0xCC,
	0xA8, 0xA5, 0x03, 0x75, 0xCC, 0xAD, 0xB5, 0x03,
	0x75, 0xCC, 0xB0, 0xB5, 0x03, 0x76, 0xCC, 0x83,
	0xC9, 0x03, 0x76, 0xCC, 0xA3, 0xB5, 0x03, 0x77,
	0xCC, 0x80, 0xC9, 0x03, 0x77, 0xCC, 0x81, 0xC9,
	0x03, 0x77, 0xCC, 0x82, 0xC9, 0x03, 0x77, 0xCC,
	0x87, 0xC9, 0x03, 0x77, 0xCC, 0x88, 0xC9, 0x03,
	0x77, 0xCC, 0x8A, 0xC9, 0x03, 0x77, 0xCC, 0xA3,
	// Bytes 3540 - 357f
	0xB5, 0x03, 0x78, 0xCC, 0x87, 0xC9, 0x03, 0x78,
	0xCC, 0x88, 0xC9, 0x03, 0x79, 0xCC, 0x80, 0xC9,
	0x03, 0x79, 0xCC, 0x81, 0xC9, 0x03, 0x79, 0xCC,
	0x82, 0xC9, 0x03, 0x79, 0xCC, 0x83, 0xC9, 0x03,
	0x79, 0xCC, 0x84, 0xC9, 0x03, 0x79, 0xCC, 0x87,
	0xC9, 0x03, 0x79, 0xCC, 0x88, 0xC9, 0x03, 0x79,
	0xCC, 0x89, 0xC9, 0x03, 0x79, 0xCC, 0x8A, 0xC9,
	0x03, 0x79, 0xCC, 0xA3, 0xB5, 0x03, 0x7A, 0xCC,
	// Bytes 3580 - 35bf
	0x81, 0xC9, 0x03, 0x7A, 0xCC, 0x82, 0xC9, 0x03,
	0x7A, 0xCC, 0x87, 0xC9, 0x03, 0x7A, 0xCC, 0x8C,
	0xC9, 0x03, 0x7A, 0xCC, 0xA3, 0xB5, 0x03, 0x7A,
	0xCC, 0xB1, 0xB5, 0x04, 0xC2, 0xA8, 0xCC, 0x80,
	0xCA, 0x04, 0xC2, 0xA8, 0xCC, 0x81, 0xCA, 0x04,
	0xC2, 0xA8, 0xCD, 0x82, 0xCA, 0x04, 0xC3, 0x86,
	0xCC, 0x81, 0xC9, 0x04, 0xC3, 0x86, 0xCC, 0x84,
	0xC9, 0x04, 0xC3, 0x98, 0xCC, 0x81, 0xC9, 0x04,
	// Bytes 35c0 - 35ff
	0xC3, 0xA6, 0xCC, 0x81, 0xC9, 0x04, 0xC3, 0xA6,
	0xCC, 0x84, 0xC9, 0x04, 0xC3, 0xB8, 0xCC, 0x81,
	0xC9, 0x04, 0xC5, 0xBF, 0xCC, 0x87, 0xC9, 0x04,
	0xC6, 0xB7, 0xCC, 0x8C, 0xC9, 0x04, 0xCA, 0x92,
	0xCC, 0x8C, 0xC9, 0x04, 0xCE, 0x91, 0xCC, 0x80,
	0xC9, 0x04, 0xCE, 0x91, 0xCC, 0x81, 0xC9, 0x04,
	0xCE, 0x91, 0xCC, 0x84, 0xC9, 0x04, 0xCE, 0x91,
	0xCC, 0x86, 0xC9, 0x04, 0xCE, 0x91, 0xCD, 0x85,
	// Bytes 3600 - 363f
	0xD9, 0x04, 0xCE, 0x95, 0xCC, 0x80, 0xC9, 0x04,
	0xCE, 0x95, 0xCC, 0x81, 0xC9, 0x04, 0xCE, 0x97,
	0xCC, 0x80, 0xC9, 0x04, 0xCE, 0x97, 0xCC, 0x81,
	0xC9, 0x04, 0xCE, 0x97, 0xCD, 0x85, 0xD9, 0x04,
	0xCE, 0x99, 0xCC, 0x80, 0xC9, 0x04, 0xCE, 0x99,
	0xCC, 0x81, 0xC9, 0x04, 0xCE, 0x99, 0xCC, 0x84,
	0xC9, 0x04, 0xCE, 0x99, 0xCC, 0x86, 0xC9, 0x04,
	0xCE, 0x99, 0xCC, 0x88, 0xC9, 0x04, 0xCE, 0x9F,
	// Bytes 3640 - 367f
	0xCC, 0x80, 0xC9, 0x04, 0xCE, 0x9F, 0xCC, 0x81,
	0xC9, 0x04, 0xCE, 0xA1, 0xCC, 0x94, 0xC9, 0x04,
	0xCE, 0xA5, 0xCC, 0x80, 0xC9, 0x04, 0xCE, 0xA5,
	0xCC, 0x81, 0xC9, 0x04, 0xCE, 0xA5, 0xCC, 0x84,
	0xC9, 0x04, 0xCE, 0xA5, 0xCC, 0x86, 0xC9, 0x04,
	0xCE, 0xA5, 0xCC, 0x88, 0xC9, 0x04, 0xCE, 0xA9,
	0xCC, 0x80, 0xC9, 0x04, 0xCE, 0xA9, 0xCC, 0x81,
	0xC9, 0x04, 0xCE, 0xA9, 0xCD, 0x85, 0xD9, 0x04,
	// Bytes 3680 - 36bf
	0xCE, 0xB1, 0xCC, 0x84, 0xC9, 0x04, 0xCE, 0xB1,
	0xCC, 0x86, 0xC9, 0x04, 0xCE, 0xB1, 0xCD, 0x85,
	0xD9, 0x04, 0xCE, 0xB5, 0xCC, 0x80, 0xC9, 0x04,
	0xCE, 0xB5, 0xCC, 0x81, 0xC9, 0x04, 0xCE, 0xB7,
	0xCD, 0x85, 0xD9, 0x04, 0xCE, 0xB9, 0xCC, 0x80,
	0xC9, 0x04, 0xCE, 0xB9, 0xCC, 0x81, 0xC9, 0x04,
	0xCE, 0xB9, 0xCC, 0x84, 0xC9, 0x04, 0xCE, 0xB9,
	0xCC, 0x86, 0xC9, 0x04, 0xCE, 0xB9, 0xCD, 0x82,
	// Bytes 36c0 - 36ff
	0xC9, 0x04, 0xCE, 0xBF, 0xCC, 0x80, 0xC9, 0x04,
	0xCE, 0xBF, 0xCC, 0x81, 0xC9, 0x04, 0xCF, 0x81,
	0xCC, 0x93, 0xC9, 0x04, 0xCF, 0x81, 0xCC, 0x94,
	0xC9, 0x04, 0xCF, 0x85, 0xCC, 0x80, 0xC9, 0x04,
	0xCF, 0x85, 0xCC, 0x81, 0xC9, 0x04, 0xCF, 0x85,
	0xCC, 0x84, 0xC9, 0x04, 0xCF, 0x85, 0xCC, 0x86,
	0xC9, 0x04, 0xCF, 0x85, 0xCD, 0x82, 0xC9, 0x04,
	0xCF, 0x89, 0xCD, 0x85, 0xD9, 0x04, 0xCF, 0x92,
	// Bytes 3700 - 373f
	0xCC, 0x81, 0xC9, 0x04, 0xCF, 0x92, 0xCC, 0x88,
	0xC9, 0x04, 0xD0, 0x86, 0xCC, 0x88, 0xC9, 0x04,
	0xD0, 0x90, 0xCC, 0x86, 0xC9, 0x04, 0xD0, 0x90,
	0xCC, 0x88, 0xC9, 0x04, 0xD0, 0x93, 0xCC, 0x81,
	0xC9, 0x04, 0xD0, 0x95, 0xCC, 0x80, 0xC9, 0x04,
	0xD0, 0x95, 0xCC, 0x86, 0xC9, 0x04, 0xD0, 0x95,
	0xCC, 0x88, 0xC9, 0x04, 0xD0, 0x96, 0xCC, 0x86,
	0xC9, 0x04, 0xD0, 0x96, 0xCC, 0x88, 0xC9, 0x04,
	// Bytes 3740 - 377f
	0xD0, 0x97, 0xCC, 0x88, 0xC9, 0x04, 0xD0, 0x98,
	0xCC, 0x80, 0xC9, 0x04, 0xD0, 0x98, 0xCC, 0x84,
	0xC9, 0x04, 0xD0, 0x98, 0xCC, 0x86, 0xC9, 0x04,
	0xD0, 0x98, 0xCC, 0x88, 0xC9, 0x04, 0xD0, 0x9A,
	0xCC, 0x81, 0xC9, 0x04, 0xD0, 0x9E, 0xCC, 0x88,
	0xC9, 0x04, 0xD0, 0xA3, 0xCC, 0x84, 0xC9, 0x04,
	0xD0, 0xA3, 0xCC, 0x86, 0xC9, 0x04, 0xD0, 0xA3,
	0xCC, 0x88, 0xC9, 0x04, 0xD0, 0xA3, 0xCC, 0x8B,
	// Bytes 3780 - 37bf
	0xC9, 0x04, 0xD0, 0xA7, 0xCC, 0x88, 0xC9, 0x04,
	0xD0, 0xAB, 0xCC, 0x88, 0xC9, 0x04, 0xD0, 0xAD,
	0xCC, 0x88, 0xC9, 0x04, 0xD0, 0xB0, 0xCC, 0x86,
	0xC9, 0x04, 0xD0, 0xB0, 0xCC, 0x88, 0xC9, 0x04,
	0xD0, 0xB3, 0xCC, 0x81, 0xC9, 0x04, 0xD0, 0xB5,
	0xCC, 0x80, 0xC9, 0x04, 0xD0, 0xB5, 0xCC, 0x86,
	0xC9, 0x04, 0xD0, 0xB5, 0xCC, 0x88, 0xC9, 0x04,
	0xD0, 0xB6, 0xCC, 0x86, 0xC9, 0x04, 0xD0, 0xB6,
	// Bytes 37c0 - 37ff
	0xCC, 0x88, 0xC9, 0x04, 0xD0, 0xB7, 0xCC, 0x88,
	0xC9, 0x04, 0xD0, 0xB8, 0xCC, 0x80, 0xC9, 0x04,
	0xD0, 0xB8, 0xCC, 0x84, 0xC9, 0x04, 0xD0, 0xB8,
	0xCC, 0x86, 0xC9, 0x04, 0xD0, 0xB8, 0xCC, 0x88,
	0xC9, 0x04, 0xD0, 0xBA, 0xCC, 0x81, 0xC9, 0x04,
	0xD0, 0xBE, 0xCC, 0x88, 0xC9, 0x04, 0xD1, 0x83,
	0xCC, 0x84, 0xC9, 0x04, 0xD1, 0x83, 0xCC, 0x86,
	0xC9, 0x04, 0xD1, 0x83, 0xCC, 0x88, 0xC9, 0x04,
	// Bytes 3800 - 383f
	0xD1, 0x83, 0xCC, 0x8B, 0xC9, 0x04, 0xD1, 0x87,
	0xCC, 0x88, 0xC9, 0x04, 0xD1, 0x8B, 0xCC, 0x88,
	0xC9, 0x04, 0xD1, 0x8D, 0xCC, 0x88, 0xC9, 0x04,
	0xD1, 0x96, 0xCC, 0x88, 0xC9, 0x04, 0xD1, 0xB4,
	0xCC, 0x8F, 0xC9, 0x04, 0xD1, 0xB5, 0xCC, 0x8F,
	0xC9, 0x04, 0xD3, 0x98, 0xCC, 0x88, 0xC9, 0x04,
	0xD3, 0x99, 0xCC, 0x88, 0xC9, 0x04, 0xD3, 0xA8,
	0xCC, 0x88, 0xC9, 0x04, 0xD3, 0xA9, 0xCC, 0x88,
	// Bytes 3840 - 387f
	0xC9, 0x04, 0xD8, 0xA7, 0xD9, 0x93, 0xC9, 0x04,
	0xD8, 0xA7, 0xD9, 0x94, 0xC9, 0x04, 0xD8, 0xA7,
	0xD9, 0x95, 0xB5, 0x04, 0xD9, 0x88, 0xD9, 0x94,
	0xC9, 0x04, 0xD9, 0x8A, 0xD9, 0x94, 0xC9, 0x04,
	0xDB, 0x81, 0xD9, 0x94, 0xC9, 0x04, 0xDB, 0x92,
	0xD9, 0x94, 0xC9, 0x04, 0xDB, 0x95, 0xD9, 0x94,
	0xC9, 0x05, 0x41, 0xCC, 0x82, 0xCC, 0x80, 0xCA,
	0x05, 0x41, 0xCC, 0x82, 0xCC, 0x81, 0xCA, 0x05,
	// Bytes 3880 - 38bf
	0x41, 0xCC, 0x82, 0xCC, 0x83, 0xCA, 0x05, 0x41,
	0xCC, 0x82, 0xCC, 0x89, 0xCA, 0x05, 0x41, 0xCC,
	0x86, 0xCC, 0x80, 0xCA, 0x05, 0x41, 0xCC, 0x86,
	0xCC, 0x81, 0xCA, 0x05, 0x41, 0xCC, 0x86, 0xCC,
	0x83, 0xCA, 0x05, 0x41, 0xCC, 0x86, 0xCC, 0x89,
	0xCA, 0x05, 0x41, 0xCC, 0x87, 0xCC, 0x84, 0xCA,
	0x05, 0x41, 0xCC, 0x88, 0xCC, 0x84, 0xCA, 0x05,
	0x41, 0xCC, 0x8A, 0xCC, 0x81, 0xCA, 0x05, 0x41,
	// Bytes 38c0 - 38ff
	0xCC, 0xA3, 0xCC, 0x82, 0xCA, 0x05, 0x41, 0xCC,
	0xA3, 0xCC, 0x86, 0xCA, 0x05, 0x43, 0xCC, 0xA7,
	0xCC, 0x81, 0xCA, 0x05, 0x45, 0xCC, 0x82, 0xCC,
	0x80, 0xCA, 0x05, 0x45, 0xCC, 0x82, 0xCC, 0x81,
	0xCA, 0x05, 0x45, 0xCC, 0x82, 0xCC, 0x83, 0xCA,
	0x05, 0x45, 0xCC, 0x82, 0xCC, 0x89, 0xCA, 0x05,
	0x45, 0xCC, 0x84, 0xCC, 0x80, 0xCA, 0x05, 0x45,
	0xCC, 0x84, 0xCC, 0x81, 0xCA, 0x05, 0x45, 0xCC,
	// Bytes 3900 - 393f
	0xA3, 0xCC, 0x82, 0xCA, 0x05, 0x45, 0xCC, 0xA7,
	0xCC, 0x86, 0xCA, 0x05, 0x49, 0xCC, 0x88, 0xCC,
	0x81, 0xCA, 0x05, 0x4C, 0xCC, 0xA3, 0xCC, 0x84,
	0xCA, 0x05, 0x4F, 0xCC, 0x82, 0xCC, 0x80, 0xCA,
	0x05, 0x4F, 0xCC, 0x82, 0xCC, 0x81, 0xCA, 0x05,
	0x4F, 0xCC, 0x82, 0xCC, 0x83, 0xCA, 0x05, 0x4F,
	0xCC, 0x82, 0xCC, 0x89, 0xCA, 0x05, 0x4F, 0xCC,
	0x83, 0xCC, 0x81, 0xCA, 0x05, 0x4F, 0xCC, 0x83,
	// Bytes 3940 - 397f
	0xCC, 0x84, 0xCA, 0x05, 0x4F, 0xCC, 0x83, 0xCC,
	0x88, 0xCA, 0x05, 0x4F, 0xCC, 0x84, 0xCC, 0x80,
	0xCA, 0x05, 0x4F, 0xCC, 0x84, 0xCC, 0x81, 0xCA,
	0x05, 0x4F, 0xCC, 0x87, 0xCC, 0x84, 0xCA, 0x05,
	0x4F, 0xCC, 0x88, 0xCC, 0x84, 0xCA, 0x05, 0x4F,
	0xCC, 0x9B, 0xCC, 0x80, 0xCA, 0x05, 0x4F, 0xCC,
	0x9B, 0xCC, 0x81, 0xCA, 0x05, 0x4F, 0xCC, 0x9B,
	0xCC, 0x83, 0xCA, 0x05, 0x4F, 0xCC, 0x9B, 0xCC,
	// Bytes 3980 - 39bf
	0x89, 0xCA, 0x05, 0x4F, 0xCC, 0x9B, 0xCC, 0xA3,
	0xB6, 0x05, 0x4F, 0xCC, 0xA3, 0xCC, 0x82, 0xCA,
	0x05, 0x4F, 0xCC, 0xA8, 0xCC, 0x84, 0xCA, 0x05,
	0x52, 0xCC, 0xA3, 0xCC, 0x84, 0xCA, 0x05, 0x53,
	0xCC, 0x81, 0xCC, 0x87, 0xCA, 0x05, 0x53, 0xCC,
	0x8C, 0xCC, 0x87, 0xCA, 0x05, 0x53, 0xCC, 0xA3,
	0xCC, 0x87, 0xCA, 0x05, 0x55, 0xCC, 0x83, 0xCC,
	0x81, 0xCA, 0x05, 0x55, 0xCC, 0x84, 0xCC, 0x88,
	// Bytes 39c0 - 39ff
	0xCA, 0x05, 0x55, 0xCC, 0x88, 0xCC, 0x80, 0xCA,
	0x05, 0x55, 0xCC, 0x88, 0xCC, 0x81, 0xCA, 0x05,
	0x55, 0xCC, 0x88, 0xCC, 0x84, 0xCA, 0x05, 0x55,
	0xCC, 0x88, 0xCC, 0x8C, 0xCA, 0x05, 0x55, 0xCC,
	0x9B, 0xCC, 0x80, 0xCA, 0x05, 0x55, 0xCC, 0x9B,
	0xCC, 0x81, 0xCA, 0x05, 0x55, 0xCC, 0x9B, 0xCC,
	0x83, 0xCA, 0x05, 0x55, 0xCC, 0x9B, 0xCC, 0x89,
	0xCA, 0x05, 0x55, 0xCC, 0x9B, 0xCC, 0xA3, 0xB6,
	// Bytes 3a00 - 3a3f
	0x05, 0x61, 0xCC, 0x82, 0xCC, 0x80, 0xCA, 0x05,
	0x61, 0xCC, 0x82, 0xCC, 0x81, 0xCA, 0x05, 0x61,
	0xCC, 0x82, 0xCC, 0x83, 0xCA, 0x05, 0x61, 0xCC,
	0x82, 0xCC, 0x89, 0xCA, 0x05, 0x61, 0xCC, 0x86,
	0xCC, 0x80, 0xCA, 0x05, 0x61, 0xCC, 0x86, 0xCC,
	0x81, 0xCA, 0x05, 0x61, 0xCC, 0x86, 0xCC, 0x83,
	0xCA, 0x05, 0x61, 0xCC, 0x86, 0xCC, 0x89, 0xCA,
	0x05, 0x61, 0xCC, 0x87, 0xCC, 0x84, 0xCA, 0x05,
	// Bytes 3a40 - 3a7f
	0x61, 0xCC, 0x88, 0xCC, 0x84, 0xCA, 0x05, 0x61,
	0xCC, 0x8A, 0xCC, 0x81, 0xCA, 0x05, 0x61, 0xCC,
	0xA3, 0xCC, 0x82, 0xCA, 0x05, 0x61, 0xCC, 0xA3,
	0xCC, 0x86, 0xCA, 0x05, 0x63, 0xCC, 0xA7, 0xCC,
	0x81, 0xCA, 0x05, 0x65, 0xCC, 0x82, 0xCC, 0x80,
	0xCA, 0x05, 0x65, 0xCC, 0x82, 0xCC, 0x81, 0xCA,
	0x05, 0x65, 0xCC, 0x82, 0xCC, 0x83, 0xCA, 0x05,
	0x65, 0xCC, 0x82, 0xCC, 0x89, 0xCA, 0x05, 0x65,
	// Bytes 3a80 - 3abf
	0xCC, 0x84, 0xCC, 0x80, 0xCA, 0x05, 0x65, 0xCC,
	0x84, 0xCC, 0x81, 0xCA, 0x05, 0x65, 0xCC, 0xA3,
	0xCC, 0x82, 0xCA, 0x05, 0x65, 0xCC, 0xA7, 0xCC,
	0x86, 0xCA, 0x05, 0x69, 0xCC, 0x88, 0xCC, 0x81,
	0xCA, 0x05, 0x6C, 0xCC, 0xA3, 0xCC, 0x84, 0xCA,
	0x05, 0x6F, 0xCC, 0x82, 0xCC, 0x80, 0xCA, 0x05,
	0x6F, 0xCC, 0x82, 0xCC, 0x81, 0xCA, 0x05, 0x6F,
	0xCC, 0x82, 0xCC, 0x83, 0xCA, 0x05, 0x6F, 0xCC,
	// Bytes 3ac0 - 3aff
	0x82, 0xCC, 0x89, 0xCA, 0x05, 0x6F, 0xCC, 0x83,
	0xCC, 0x81, 0xCA, 0x05, 0x6F, 0xCC, 0x83, 0xCC,
	0x84, 0xCA, 0x05, 0x6F, 0xCC, 0x83, 0xCC, 0x88,
	0xCA, 0x05, 0x6F, 0xCC, 0x84, 0xCC, 0x80, 0xCA,
	0x05, 0x6F, 0xCC, 0x84, 0xCC, 0x81, 0xCA, 0x05,
	0x6F, 0xCC, 0x87, 0xCC, 0x84, 0xCA, 0x05, 0x6F,
	0xCC, 0x88, 0xCC, 0x84, 0xCA, 0x05, 0x6F, 0xCC,
	0x9B, 0xCC, 0x80, 0xCA, 0x05, 0x6F, 0xCC, 0x9B,
	// Bytes 3b00 - 3b3f
	0xCC, 0x81, 0xCA, 0x05, 0x6F, 0xCC, 0x9B, 0xCC,
	0x83, 0xCA, 0x05, 0x6F, 0xCC, 0x9B, 0xCC, 0x89,
	0xCA, 0x05, 0x6F, 0xCC, 0x9B, 0xCC, 0xA3, 0xB6,
	0x05, 0x6F, 0xCC, 0xA3, 0xCC, 0x82, 0xCA, 0x05,
	0x6F, 0xCC, 0xA8, 0xCC, 0x84, 0xCA, 0x05, 0x72,
	0xCC, 0xA3, 0xCC, 0x84, 0xCA, 0x05, 0x73, 0xCC,
	0x81, 0xCC, 0x87, 0xCA, 0x05, 0x73, 0xCC, 0x8C,
	0xCC, 0x87, 0xCA, 0x05, 0x73, 0xCC, 0xA3, 0xCC,
	// Bytes 3b40 - 3b7f
	0x87, 0xCA, 0x05, 0x75, 0xCC, 0x83, 0xCC, 0x81,
	0xCA, 0x05, 0x75, 0xCC, 0x84, 0xCC, 0x88, 0xCA,
	0x05, 0x75, 0xCC, 0x88, 0xCC, 0x80, 0xCA, 0x05,
	0x75, 0xCC, 0x88, 0xCC, 0x81, 0xCA, 0x05, 0x75,
	0xCC, 0x88, 0xCC, 0x84, 0xCA, 0x05, 0x75, 0xCC,
	0x88, 0xCC, 0x8C, 0xCA, 0x05, 0x75, 0xCC, 0x9B,
	0xCC, 0x80, 0xCA, 0x05, 0x75, 0xCC, 0x9B, 0xCC,
	0x81, 0xCA, 0x05, 0x75, 0xCC, 0x9B, 0xCC, 0x83,
	// Bytes 3b80 - 3bbf
	0xCA, 0x05, 0x75, 0xCC, 0x9B, 0xCC, 0x89, 0xCA,
	0x05, 0x75, 0xCC, 0x9B, 0xCC, 0xA3, 0xB6, 0x05,
	0xE1, 0xBE, 0xBF, 0xCC, 0x80, 0xCA, 0x05, 0xE1,
	0xBE, 0xBF, 0xCC, 0x81, 0xCA, 0x05, 0xE1, 0xBE,
	0xBF, 0xCD, 0x82, 0xCA, 0x05, 0xE1, 0xBF, 0xBE,
	0xCC, 0x80, 0xCA, 0x05, 0xE1, 0xBF, 0xBE, 0xCC,
	0x81, 0xCA, 0x05, 0xE1, 0xBF, 0xBE, 0xCD, 0x82,
	0xCA, 0x05, 0xE2, 0x86, 0x90, 0xCC, 0xB8, 0x05,
	// Bytes 3bc0 - 3bff
	0x05, 0xE2, 0x86, 0x92, 0xCC, 0xB8, 0x05, 0x05,
	0xE2, 0x86, 0x94, 0xCC, 0xB8, 0x05, 0x05, 0xE2,
	0x87, 0x90, 0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x87,
	0x92, 0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x87, 0x94,
	0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x88, 0x83, 0xCC,
	0xB8, 0x05, 0x05, 0xE2, 0x88, 0x88, 0xCC, 0xB8,
	0x05, 0x05, 0xE2, 0x88, 0x8B, 0xCC, 0xB8, 0x05,
	0x05, 0xE2, 0x88, 0xA3, 0xCC, 0xB8, 0x05, 0x05,
	// Bytes 3c00 - 3c3f
	0xE2, 0x88, 0xA5, 0xCC, 0xB8, 0x05, 0x05, 0xE2,
	0x88, 0xBC, 0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x89,
	0x83, 0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x89, 0x85,
	0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x89, 0x88, 0xCC,
	0xB8, 0x05, 0x05, 0xE2, 0x89, 0x8D, 0xCC, 0xB8,
	0x05, 0x05, 0xE2, 0x89, 0xA1, 0xCC, 0xB8, 0x05,
	0x05, 0xE2, 0x89, 0xA4, 0xCC, 0xB8, 0x05, 0x05,
	0xE2, 0x89, 0xA5, 0xCC, 0xB8, 0x05, 0x05, 0xE2,
	// Bytes 3c40 - 3c7f
	0x89, 0xB2, 0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x89,
	0xB3, 0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x89, 0xB6,
	0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x89, 0xB7, 0xCC,
	0xB8, 0x05, 0x05, 0xE2, 0x89, 0xBA, 0xCC, 0xB8,
	0x05, 0x05, 0xE2, 0x89, 0xBB, 0xCC, 0xB8, 0x05,
	0x05, 0xE2, 0x89, 0xBC, 0xCC, 0xB8, 0x05, 0x05,
	0xE2, 0x89, 0xBD, 0xCC, 0xB8, 0x05, 0x05, 0xE2,
	0x8A, 0x82, 0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x8A,
	// Bytes 3c80 - 3cbf
	0x83, 0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x8A, 0x86,
	0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x8A, 0x87, 0xCC,
	0xB8, 0x05, 0x05, 0xE2, 0x8A, 0x91, 0xCC, 0xB8,
	0x05, 0x05, 0xE2, 0x8A, 0x92, 0xCC, 0xB8, 0x05,
	0x05, 0xE2, 0x8A, 0xA2, 0xCC, 0xB8, 0x05, 0x05,
	0xE2, 0x8A, 0xA8, 0xCC, 0xB8, 0x05, 0x05, 0xE2,
	0x8A, 0xA9, 0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x8A,
	0xAB, 0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x8A, 0xB2,
	// Bytes 3cc0 - 3cff
	0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x8A, 0xB3, 0xCC,
	0xB8, 0x05, 0x05, 0xE2, 0x8A, 0xB4, 0xCC, 0xB8,
	0x05, 0x05, 0xE2, 0x8A, 0xB5, 0xCC, 0xB8, 0x05,
	0x06, 0xCE, 0x91, 0xCC, 0x93, 0xCD, 0x85, 0xDA,
	0x06, 0xCE, 0x91, 0xCC, 0x94, 0xCD, 0x85, 0xDA,
	0x06, 0xCE, 0x95, 0xCC, 0x93, 0xCC, 0x80, 0xCA,
	0x06, 0xCE, 0x95, 0xCC, 0x93, 0xCC, 0x81, 0xCA,
	0x06, 0xCE, 0x95, 0xCC, 0x94, 0xCC, 0x80, 0xCA,
	// Bytes 3d00 - 3d3f
	0x06, 0xCE, 0x95, 0xCC, 0x94, 0xCC, 0x81, 0xCA,
	0x06, 0xCE, 0x97, 0xCC, 0x93, 0xCD, 0x85, 0xDA,
	0x06, 0xCE, 0x97, 0xCC, 0x94, 0xCD, 0x85, 0xDA,
	0x06, 0xCE, 0x99, 0xCC, 0x93, 0xCC, 0x80, 0xCA,
	0x06, 0xCE, 0x99, 0xCC, 0x93, 0xCC, 0x81, 0xCA,
	0x06, 0xCE, 0x99, 0xCC, 0x93, 0xCD, 0x82, 0xCA,
	0x06, 0xCE, 0x99, 0xCC, 0x94, 0xCC, 0x80, 0xCA,
	0x06, 0xCE, 0x99, 0xCC, 0x94, 0xCC, 0x81, 0xCA,
	// Bytes 3d40 - 3d7f
	0x06, 0xCE, 0x99, 0xCC, 0x94, 0xCD, 0x82, 0xCA,
	0x06, 0xCE, 0x9F, 0xCC, 0x93, 0xCC, 0x80, 0xCA,
	0x06, 0xCE, 0x9F, 0xCC, 0x93, 0xCC, 0x81, 0xCA,
	0x06, 0xCE, 0x9F, 0xCC, 0x94, 0xCC, 0x80, 0xCA,
	0x06, 0xCE, 0x9F, 0xCC, 0x94, 0xCC, 0x81, 0xCA,
	0x06, 0xCE, 0xA5, 0xCC, 0x94, 0xCC, 0x80, 0xCA,
	0x06, 0xCE, 0xA5, 0xCC, 0x94, 0xCC, 0x81, 0xCA,
	0x06, 0xCE, 0xA5, 0xCC, 0x94, 0xCD, 0x82, 0xCA,
	// Bytes 3d80 - 3dbf
	0x06, 0xCE, 0xA9, 0xCC, 0x93, 0xCD, 0x85, 0xDA,
	0x06, 0xCE, 0xA9, 0xCC, 0x94, 0xCD, 0x85, 0xDA,
	0x06, 0xCE, 0xB1, 0xCC, 0x80, 0xCD, 0x85, 0xDA,
	0x06, 0xCE, 0xB1, 0xCC, 0x81, 0xCD, 0x85, 0xDA,
	0x06, 0xCE, 0xB1, 0xCC, 0x93, 0xCD, 0x85, 0xDA,
	0x06, 0xCE, 0xB1, 0xCC, 0x94, 0xCD, 0x85, 0xDA,
	0x06, 0xCE, 0xB1, 0xCD, 0x82, 0xCD, 0x85, 0xDA,
	0x06, 0xCE, 0xB5, 0xCC, 0x93, 0xCC, 0x80, 0xCA,
	// Bytes 3dc0 - 3dff
	0x06, 0xCE, 0xB5, 0xCC, 0x93, 0xCC, 0x81, 0xCA,
	0x06, 0xCE, 0xB5, 0xCC, 0x94, 0xCC, 0x80, 0xCA,
	0x06, 0xCE, 0xB5, 0xCC, 0x94, 0xCC, 0x81, 0xCA,
	0x06, 0xCE, 0xB7, 0xCC, 0x80, 0xCD, 0x85, 0xDA,
	0x06, 0xCE, 0xB7, 0xCC, 0x81, 0xCD, 0x85, 0xDA,
	0x06, 0xCE, 0xB7, 0xCC, 0x93, 0xCD, 0x85, 0xDA,
	0x06, 0xCE, 0xB7, 0xCC, 0x94, 0xCD, 0x85, 0xDA,
	0x06, 0xCE, 0xB7, 0xCD, 0x82, 0xCD, 0x85, 0xDA,
	// Bytes 3e00 - 3e3f
	0x06, 0xCE, 0xB9, 0xCC, 0x88, 0xCC, 0x80, 0xCA,
	0x06, 0xCE, 0xB9, 0xCC, 0x88, 0xCC, 0x81, 0xCA,
	0x06, 0xCE, 0xB9, 0xCC, 0x88, 0xCD, 0x82, 0xCA,
	0x06, 0xCE, 0xB9, 0xCC, 0x93, 0xCC, 0x80, 0xCA,
	0x06, 0xCE, 0xB9, 0xCC, 0x93, 0xCC, 0x81, 0xCA,
	0x06, 0xCE, 0xB9, 0xCC, 0x93, 0xCD, 0x82, 0xCA,
	0x06, 0xCE, 0xB9, 0xCC, 0x94, 0xCC, 0x80, 0xCA,
	0x06, 0xCE, 0xB9, 0xCC, 0x94, 0xCC, 0x81, 0xCA,
	// Bytes 3e40 - 3e7f
	0x06, 0xCE, 0xB9, 0xCC, 0x94, 0xCD, 0x82, 0xCA,
	0x06, 0xCE, 0xBF, 0xCC, 0x93, 0xCC, 0x80, 0xCA,
	0x06, 0xCE, 0xBF, 0xCC, 0x93, 0xCC, 0x81, 0xCA,
	0x06, 0xCE, 0xBF, 0xCC, 0x94, 0xCC, 0x80, 0xCA,
	0x06, 0xCE, 0xBF, 0xCC, 0x94, 0xCC, 0x81, 0xCA,
	0x06, 0xCF, 0x85, 0xCC, 0x88, 0xCC, 0x80, 0xCA,
	0x06, 0xCF, 0x85, 0xCC, 0x88, 0xCC, 0x81, 0xCA,
	0x06, 0xCF, 0x85, 0xCC, 0x88, 0xCD, 0x82, 0xCA,
	// Bytes 3e80 - 3ebf
	0x06, 0xCF, 0x85, 0xCC, 0x93, 0xCC, 0x80, 0xCA,
	0x06, 0xCF, 0x85, 0xCC, 0x93, 0xCC, 0x81, 0xCA,
	0x06, 0xCF, 0x85, 0xCC, 0x93, 0xCD, 0x82, 0xCA,
	0x06, 0xCF, 0x85, 0xCC, 0x94, 0xCC, 0x80, 0xCA,
	0x06, 0xCF, 0x85, 0xCC, 0x94, 0xCC, 0x81, 0xCA,
	0x06, 0xCF, 0x85, 0xCC, 0x94, 0xCD, 0x82, 0xCA,
	0x06, 0xCF, 0x89, 0xCC, 0x80, 0xCD, 0x85, 0xDA,
	0x06, 0xCF, 0x89, 0xCC, 0x81, 0xCD, 0x85, 0xDA,
	// Bytes 3ec0 - 3eff
	0x06, 0xCF, 0x89, 0xCC, 0x93, 0xCD, 0x85, 0xDA,
	0x06, 0xCF, 0x89, 0xCC, 0x94, 0xCD, 0x85, 0xDA,
	0x06, 0xCF, 0x89, 0xCD, 0x82, 0xCD, 0x85, 0xDA,
	0x06, 0xE0, 0xA4, 0xA8, 0xE0, 0xA4, 0xBC, 0x09,
	0x06, 0xE0, 0xA4, 0xB0, 0xE0, 0xA4, 0xBC, 0x09,
	0x06, 0xE0, 0xA4, 0xB3, 0xE0, 0xA4, 0xBC, 0x09,
	0x06, 0xE0, 0xB1, 0x86, 0xE0, 0xB1, 0x96, 0x85,
	0x06, 0xE0, 0xB7, 0x99, 0xE0, 0xB7, 0x8A, 0x11,
	// Bytes 3f00 - 3f3f
	0x06, 0xE3, 0x81, 0x86, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x81, 0x8B, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x81, 0x8D, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x81, 0x8F, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x81, 0x91, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x81, 0x93, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x81, 0x95, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x81, 0x97, 0xE3, 0x82, 0x99, 0x0D,
	// Bytes 3f40 - 3f7f
	0x06, 0xE3, 0x81, 0x99, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x81, 0x9B, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x81, 0x9D, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x81, 0x9F, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x81, 0xA1, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x81, 0xA4, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x81, 0xA6, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x81, 0xA8, 0xE3, 0x82, 0x99, 0x0D,
	// Bytes 3f80 - 3fbf
	0x06, 0xE3, 0x81, 0xAF, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x81, 0xAF, 0xE3, 0x82, 0x9A, 0x0D,
	0x06, 0xE3, 0x81, 0xB2, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x81, 0xB2, 0xE3, 0x82, 0x9A, 0x0D,
	0x06, 0xE3, 0x81, 0xB5, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x81, 0xB5, 0xE3, 0x82, 0x9A, 0x0D,
	0x06, 0xE3, 0x81, 0xB8, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x81, 0xB8, 0xE3, 0x82, 0x9A, 0x0D,
	// Bytes 3fc0 - 3fff
	0x06, 0xE3, 0x81, 0xBB, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x81, 0xBB, 0xE3, 0x82, 0x9A, 0x0D,
	0x06, 0xE3, 0x82, 0x9D, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x82, 0xA6, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x82, 0xAB, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x82, 0xAD, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x82, 0xAF, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x82, 0xB1, 0xE3, 0x82, 0x99, 0x0D,
	// Bytes 4000 - 403f
	0x06, 0xE3, 0x82, 0xB3, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x82, 0xB5, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x82, 0xB7, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x82, 0xB9, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x82, 0xBB, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x82, 0xBD, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x82, 0xBF, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x83, 0x81, 0xE3, 0x82, 0x99, 0x0D,
	// Bytes 4040 - 407f
	0x06, 0xE3, 0x83, 0x84, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x83, 0x86, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x83, 0x88, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x83, 0x8F, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x83, 0x8F, 0xE3, 0x82, 0x9A, 0x0D,
	0x06, 0xE3, 0x83, 0x92, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x83, 0x92, 0xE3, 0x82, 0x9A, 0x0D,
	0x06, 0xE3, 0x83, 0x95, 0xE3, 0x82, 0x99, 0x0D,
	// Bytes 4080 - 40bf
	0x06, 0xE3, 0x83, 0x95, 0xE3, 0x82, 0x9A, 0x0D,
	0x06, 0xE3, 0x83, 0x98, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x83, 0x98, 0xE3, 0x82, 0x9A, 0x0D,
	0x06, 0xE3, 0x83, 0x9B, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x83, 0x9B, 0xE3, 0x82, 0x9A, 0x0D,
	0x06, 0xE3, 0x83, 0xAF, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x83, 0xB0, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x83, 0xB1, 0xE3, 0x82, 0x99, 0x0D,
	// Bytes 40c0 - 40ff
	0x06, 0xE3, 0x83, 0xB2, 0xE3, 0x82, 0x99, 0x0D,
	0x06, 0xE3, 0x83, 0xBD, 0xE3, 0x82, 0x99, 0x0D,
	0x08, 0xCE, 0x91, 0xCC, 0x93, 0xCC, 0x80, 0xCD,
	0x85, 0xDB, 0x08, 0xCE, 0x91, 0xCC, 0x93, 0xCC,
	0x81, 0xCD, 0x85, 0xDB, 0x08, 0xCE, 0x91, 0xCC,
	0x93, 0xCD, 0x82, 0xCD, 0x85, 0xDB, 0x08, 0xCE,
	0x91, 0xCC, 0x94, 0xCC, 0x80, 0xCD, 0x85, 0xDB,
	0x08, 0xCE, 0x91, 0xCC, 0x94, 0xCC, 0x81, 0xCD,
	// Bytes 4100 - 413f
	0x85, 0xDB, 0x08, 0xCE, 0x91, 0xCC, 0x94, 0xCD,
	0x82, 0xCD, 0x85, 0xDB, 0x08, 0xCE, 0x97, 0xCC,
	0x93, 0xCC, 0x80, 0xCD, 0x85, 0xDB, 0x08, 0xCE,
	0x97, 0xCC, 0x93, 0xCC, 0x81, 0xCD, 0x85, 0xDB,
	0x08, 0xCE, 0x97, 0xCC, 0x93, 0xCD, 0x82, 0xCD,
	0x85, 0xDB, 0x08, 0xCE, 0x97, 0xCC, 0x94, 0xCC,
	0x80, 0xCD, 0x85, 0xDB, 0x08, 0xCE, 0x97, 0xCC,
	0x94, 0xCC, 0x81, 0xCD, 0x85, 0xDB, 0x08, 0xCE,
	// Bytes 4140 - 417f
	0x97, 0xCC, 0x94, 0xCD, 0x82, 0xCD, 0x85, 0xDB,
	0x08, 0xCE, 0xA9, 0xCC, 0x93, 0xCC, 0x80, 0xCD,
	0x85, 0xDB, 0x08, 0xCE, 0xA9, 0xCC, 0x93, 0xCC,
	0x81, 0xCD, 0x85, 0xDB, 0x08, 0xCE, 0xA9, 0xCC,
	0x93, 0xCD, 0x82, 0xCD, 0x85, 0xDB, 0x08, 0xCE,
	0xA9, 0xCC, 0x94, 0xCC, 0x80, 0xCD, 0x85, 0xDB,
	0x08, 0xCE, 0xA9, 0xCC, 0x94, 0xCC, 0x81, 0xCD,
	0x85, 0xDB, 0x08, 0xCE, 0xA9, 0xCC, 0x94, 0xCD,
	// Bytes 4180 - 41bf
	0x82, 0xCD, 0x85, 0xDB, 0x08, 0xCE, 0xB1, 0xCC,
	0x93, 0xCC, 0x80, 0xCD, 0x85, 0xDB, 0x08, 0xCE,
	0xB1, 0xCC, 0x93, 0xCC, 0x81, 0xCD, 0x85, 0xDB,
	0x08, 0xCE, 0xB1, 0xCC, 0x93, 0xCD, 0x82, 0xCD,
	0x85, 0xDB, 0x08, 0xCE, 0xB1, 0xCC, 0x94, 0xCC,
	0x80, 0xCD, 0x85, 0xDB, 0x08, 0xCE, 0xB1, 0xCC,
	0x94, 0xCC, 0x81, 0xCD, 0x85, 0xDB, 0x08, 0xCE,
	0xB1, 0xCC, 0x94, 0xCD, 0x82, 0xCD, 0x85, 0xDB,
	// Bytes 41c0 - 41ff
	0x08, 0xCE, 0xB7, 0xCC, 0x93, 0xCC, 0x80, 0xCD,
	0x85, 0xDB, 0x08, 0xCE, 0xB7, 0xCC, 0x93, 0xCC,
	0x81, 0xCD, 0x85, 0xDB, 0x08, 0xCE, 0xB7, 0xCC,
	0x93, 0xCD, 0x82, 0xCD, 0x85, 0xDB, 0x08, 0xCE,
	0xB7, 0xCC, 0x94, 0xCC, 0x80, 0xCD, 0x85, 0xDB,
	0x08, 0xCE, 0xB7, 0xCC, 0x94, 0xCC, 0x81, 0xCD,
	0x85, 0xDB, 0x08, 0xCE, 0xB7, 0xCC, 0x94, 0xCD,
	0x82, 0xCD, 0x85, 0xDB, 0x08, 0xCF, 0x89, 0xCC,
	// Bytes 4200 - 423f
	0x93, 0xCC, 0x80, 0xCD, 0x85, 0xDB, 0x08, 0xCF,
	0x89, 0xCC, 0x93, 0xCC, 0x81, 0xCD, 0x85, 0xDB,
	0x08, 0xCF, 0x89, 0xCC, 0x93, 0xCD, 0x82, 0xCD,
	0x85, 0xDB, 0x08, 0xCF, 0x89, 0xCC, 0x94, 0xCC,
	0x80, 0xCD, 0x85, 0xDB, 0x08, 0xCF, 0x89, 0xCC,
	0x94, 0xCC, 0x81, 0xCD, 0x85, 0xDB, 0x08, 0xCF,
	0x89, 0xCC, 0x94, 0xCD, 0x82, 0xCD, 0x85, 0xDB,
	0x08, 0xF0, 0x91, 0x82, 0x99, 0xF0, 0x91, 0x82,
	// Bytes 4240 - 427f
	0xBA, 0x09, 0x08, 0xF0, 0x91, 0x82, 0x9B, 0xF0,
	0x91, 0x82, 0xBA, 0x09, 0x08, 0xF0, 0x91, 0x82,
	0xA5, 0xF0, 0x91, 0x82, 0xBA, 0x09, 0x42, 0xC2,
	0xB4, 0x01, 0x43, 0x20, 0xCC, 0x81, 0xC9, 0x43,
	0x20, 0xCC, 0x83, 0xC9, 0x43, 0x20, 0xCC, 0x84,
	0xC9, 0x43, 0x20, 0xCC, 0x85, 0xC9, 0x43, 0x20,
	0xCC, 0x86, 0xC9, 0x43, 0x20, 0xCC, 0x87, 0xC9,
	0x43, 0x20, 0xCC, 0x88, 0xC9, 0x43, 0x20, 0xCC,
	// Bytes 4280 - 42bf
	0x8A, 0xC9, 0x43, 0x20, 0xCC, 0x8B, 0xC9, 0x43,
	0x20, 0xCC, 0x93, 0xC9, 0x43, 0x20, 0xCC, 0x94,
	0xC9, 0x43, 0x20, 0xCC, 0xA7, 0xA5, 0x43, 0x20,
	0xCC, 0xA8, 0xA5, 0x43, 0x20, 0xCC, 0xB3, 0xB5,
	0x43, 0x20, 0xCD, 0x82, 0xC9, 0x43, 0x20, 0xCD,
	0x85, 0xD9, 0x43, 0x20, 0xD9, 0x8B, 0x59, 0x43,
	0x20, 0xD9, 0x8C, 0x5D, 0x43, 0x20, 0xD9, 0x8D,
	0x61, 0x43, 0x20, 0xD9, 0x8E, 0x65, 0x43, 0x20,
	// Bytes 42c0 - 42ff
	0xD9, 0x8F, 0x69, 0x43, 0x20, 0xD9, 0x90, 0x6D,
	0x43, 0x20, 0xD9, 0x91, 0x71, 0x43, 0x20, 0xD9,
	0x92, 0x75, 0x43, 0x41, 0xCC, 0x8A, 0xC9, 0x43,
	0x73, 0xCC, 0x87, 0xC9, 0x44, 0x20, 0xE3, 0x82,
	0x99, 0x0D, 0x44, 0x20, 0xE3, 0x82, 0x9A, 0x0D,
	0x44, 0xC2, 0xA8, 0xCC, 0x81, 0xCA, 0x44, 0xCE,
	0x91, 0xCC, 0x81, 0xC9, 0x44, 0xCE, 0x95, 0xCC,
	0x81, 0xC9, 0x44, 0xCE, 0x97, 0xCC, 0x81, 0xC9,
	// Bytes 4300 - 433f
	0x44, 0xCE, 0x99, 0xCC, 0x81, 0xC9, 0x44, 0xCE,
	0x9F, 0xCC, 0x81, 0xC9, 0x44, 0xCE, 0xA5, 0xCC,
	0x81, 0xC9, 0x44, 0xCE, 0xA5, 0xCC, 0x88, 0xC9,
	0x44, 0xCE, 0xA9, 0xCC, 0x81, 0xC9, 0x44, 0xCE,
	0xB1, 0xCC, 0x81, 0xC9, 0x44, 0xCE, 0xB5, 0xCC,
	0x81, 0xC9, 0x44, 0xCE, 0xB7, 0xCC, 0x81, 0xC9,
	0x44, 0xCE, 0xB9, 0xCC, 0x81, 0xC9, 0x44, 0xCE,
	0xBF, 0xCC, 0x81, 0xC9, 0x44, 0xCF, 0x85, 0xCC,
	// Bytes 4340 - 437f
	0x81, 0xC9, 0x44, 0xCF, 0x89, 0xCC, 0x81, 0xC9,
	0x44, 0xD7, 0x90, 0xD6, 0xB7, 0x31, 0x44, 0xD7,
	0x90, 0xD6, 0xB8, 0x35, 0x44, 0xD7, 0x90, 0xD6,
	0xBC, 0x41, 0x44, 0xD7, 0x91, 0xD6, 0xBC, 0x41,
	0x44, 0xD7, 0x91, 0xD6, 0xBF, 0x49, 0x44, 0xD7,
	0x92, 0xD6, 0xBC, 0x41, 0x44, 0xD7, 0x93, 0xD6,
	0xBC, 0x41, 0x44, 0xD7, 0x94, 0xD6, 0xBC, 0x41,
	0x44, 0xD7, 0x95, 0xD6, 0xB9, 0x39, 0x44, 0xD7,
	// Bytes 4380 - 43bf
	0x95, 0xD6, 0xBC, 0x41, 0x44, 0xD7, 0x96, 0xD6,
	0xBC, 0x41, 0x44, 0xD7, 0x98, 0xD6, 0xBC, 0x41,
	0x44, 0xD7, 0x99, 0xD6, 0xB4, 0x25, 0x44, 0xD7,
	0x99, 0xD6, 0xBC, 0x41, 0x44, 0xD7, 0x9A, 0xD6,
	0xBC, 0x41, 0x44, 0xD7, 0x9B, 0xD6, 0xBC, 0x41,
	0x44, 0xD7, 0x9B, 0xD6, 0xBF, 0x49, 0x44, 0xD7,
	0x9C, 0xD6, 0xBC, 0x41, 0x44, 0xD7, 0x9E, 0xD6,
	0xBC, 0x41, 0x44, 0xD7, 0xA0, 0xD6, 0xBC, 0x41,
	// Bytes 43c0 - 43ff
	0x44, 0xD7, 0xA1, 0xD6, 0xBC, 0x41, 0x44, 0xD7,
	0xA3, 0xD6, 0xBC, 0x41, 0x44, 0xD7, 0xA4, 0xD6,
	0xBC, 0x41, 0x44, 0xD7, 0xA4, 0xD6, 0xBF, 0x49,
	0x44, 0xD7, 0xA6, 0xD6, 0xBC, 0x41, 0x44, 0xD7,
	0xA7, 0xD6, 0xBC, 0x41, 0x44, 0xD7, 0xA8, 0xD6,
	0xBC, 0x41, 0x44, 0xD7, 0xA9, 0xD6, 0xBC, 0x41,
	0x44, 0xD7, 0xA9, 0xD7, 0x81, 0x4D, 0x44, 0xD7,
	0xA9, 0xD7, 0x82, 0x51, 0x44, 0xD7, 0xAA, 0xD6,
	// Bytes 4400 - 443f
	0xBC, 0x41, 0x44, 0xD7, 0xB2, 0xD6, 0xB7, 0x31,
	0x44, 0xD8, 0xA7, 0xD9, 0x8B, 0x59, 0x44, 0xD8,
	0xA7, 0xD9, 0x93, 0xC9, 0x44, 0xD8, 0xA7, 0xD9,
	0x94, 0xC9, 0x44, 0xD8, 0xA7, 0xD9, 0x95, 0xB5,
	0x44, 0xD8, 0xB0, 0xD9, 0xB0, 0x79, 0x44, 0xD8,
	0xB1, 0xD9, 0xB0, 0x79, 0x44, 0xD9, 0x80, 0xD9,
	0x8B, 0x59, 0x44, 0xD9, 0x80, 0xD9, 0x8E, 0x65,
	0x44, 0xD9, 0x80, 0xD9, 0x8F, 0x69, 0x44, 0xD9,
	// Bytes 4440 - 447f
	0x80, 0xD9, 0x90, 0x6D, 0x44, 0xD9, 0x80, 0xD9,
	0x91, 0x71, 0x44, 0xD9, 0x80, 0xD9, 0x92, 0x75,
	0x44, 0xD9, 0x87, 0xD9, 0xB0, 0x79, 0x44, 0xD9,
	0x88, 0xD9, 0x94, 0xC9, 0x44, 0xD9, 0x89, 0xD9,
	0xB0, 0x79, 0x44, 0xD9, 0x8A, 0xD9, 0x94, 0xC9,
	0x44, 0xDB, 0x92, 0xD9, 0x94, 0xC9, 0x44, 0xDB,
	0x95, 0xD9, 0x94, 0xC9, 0x45, 0x20, 0xCC, 0x88,
	0xCC, 0x80, 0xCA, 0x45, 0x20, 0xCC, 0x88, 0xCC,
	// Bytes 4480 - 44bf
	0x81, 0xCA, 0x45, 0x20, 0xCC, 0x88, 0xCD, 0x82,
	0xCA, 0x45, 0x20, 0xCC, 0x93, 0xCC, 0x80, 0xCA,
	0x45, 0x20, 0xCC, 0x93, 0xCC, 0x81, 0xCA, 0x45,
	0x20, 0xCC, 0x93, 0xCD, 0x82, 0xCA, 0x45, 0x20,
	0xCC, 0x94, 0xCC, 0x80, 0xCA, 0x45, 0x20, 0xCC,
	0x94, 0xCC, 0x81, 0xCA, 0x45, 0x20, 0xCC, 0x94,
	0xCD, 0x82, 0xCA, 0x45, 0x20, 0xD9, 0x8C, 0xD9,
	0x91, 0x72, 0x45, 0x20, 0xD9, 0x8D, 0xD9, 0x91,
	// Bytes 44c0 - 44ff
	0x72, 0x45, 0x20, 0xD9, 0x8E, 0xD9, 0x91, 0x72,
	0x45, 0x20, 0xD9, 0x8F, 0xD9, 0x91, 0x72, 0x45,
	0x20, 0xD9, 0x90, 0xD9, 0x91, 0x72, 0x45, 0x20,
	0xD9, 0x91, 0xD9, 0xB0, 0x7A, 0x45, 0xE2, 0xAB,
	0x9D, 0xCC, 0xB8, 0x05, 0x46, 0xCE, 0xB9, 0xCC,
	0x88, 0xCC, 0x81, 0xCA, 0x46, 0xCF, 0x85, 0xCC,
	0x88, 0xCC, 0x81, 0xCA, 0x46, 0xD7, 0xA9, 0xD6,
	0xBC, 0xD7, 0x81, 0x4E, 0x46, 0xD7, 0xA9, 0xD6,
	// Bytes 4500 - 453f
	0xBC, 0xD7, 0x82, 0x52, 0x46, 0xD9, 0x80, 0xD9,
	0x8E, 0xD9, 0x91, 0x72, 0x46, 0xD9, 0x80, 0xD9,
	0x8F, 0xD9, 0x91, 0x72, 0x46, 0xD9, 0x80, 0xD9,
	0x90, 0xD9, 0x91, 0x72, 0x46, 0xE0, 0xA4, 0x95,
	0xE0, 0xA4, 0xBC, 0x09, 0x46, 0xE0, 0xA4, 0x96,
	0xE0, 0xA4, 0xBC, 0x09, 0x46, 0xE0, 0xA4, 0x97,
	0xE0, 0xA4, 0xBC, 0x09, 0x46, 0xE0, 0xA4, 0x9C,
	0xE0, 0xA4, 0xBC, 0x09, 0x46, 0xE0, 0xA4, 0xA1,
	// Bytes 4540 - 457f
	0xE0, 0xA4, 0xBC, 0x09, 0x46, 0xE0, 0xA4, 0xA2,
	0xE0, 0xA4, 0xBC, 0x09, 0x46, 0xE0, 0xA4, 0xAB,
	0xE0, 0xA4, 0xBC, 0x09, 0x46, 0xE0, 0xA4, 0xAF,
	0xE0, 0xA4, 0xBC, 0x09, 0x46, 0xE0, 0xA6, 0xA1,
	0xE0, 0xA6, 0xBC, 0x09, 0x46, 0xE0, 0xA6, 0xA2,
	0xE0, 0xA6, 0xBC, 0x09, 0x46, 0xE0, 0xA6, 0xAF,
	0xE0, 0xA6, 0xBC, 0x09, 0x46, 0xE0, 0xA8, 0x96,
	0xE0, 0xA8, 0xBC, 0x09, 0x46, 0xE0, 0xA8, 0x97,
	// Bytes 4580 - 45bf
	0xE0, 0xA8, 0xBC, 0x09, 0x46, 0xE0, 0xA8, 0x9C,
	0xE0, 0xA8, 0xBC, 0x09, 0x46, 0xE0, 0xA8, 0xAB,
	0xE0, 0xA8, 0xBC, 0x09, 0x46, 0xE0, 0xA8, 0xB2,
	0xE0, 0xA8, 0xBC, 0x09, 0x46, 0xE0, 0xA8, 0xB8,
	0xE0, 0xA8, 0xBC, 0x09, 0x46, 0xE0, 0xAC, 0xA1,
	0xE0, 0xAC, 0xBC, 0x09, 0x46, 0xE0, 0xAC, 0xA2,
	0xE0, 0xAC, 0xBC, 0x09, 0x46, 0xE0, 0xBE, 0xB2,
	0xE0, 0xBE, 0x80, 0x9D, 0x46, 0xE0, 0xBE, 0xB3,
	// Bytes 45c0 - 45ff
	0xE0, 0xBE, 0x80, 0x9D, 0x46, 0xE3, 0x83, 0x86,
	0xE3, 0x82, 0x99, 0x0D, 0x48, 0xF0, 0x9D, 0x85,
	0x97, 0xF0, 0x9D, 0x85, 0xA5, 0xAD, 0x48, 0xF0,
	0x9D, 0x85, 0x98, 0xF0, 0x9D, 0x85, 0xA5, 0xAD,
	0x48, 0xF0, 0x9D, 0x86, 0xB9, 0xF0, 0x9D, 0x85,
	0xA5, 0xAD, 0x48, 0xF0, 0x9D, 0x86, 0xBA, 0xF0,
	0x9D, 0x85, 0xA5, 0xAD, 0x49, 0xE0, 0xBE, 0xB2,
	0xE0, 0xBD, 0xB1, 0xE0, 0xBE, 0x80, 0x9E, 0x49,
	// Bytes 4600 - 463f
	0xE0, 0xBE, 0xB3, 0xE0, 0xBD, 0xB1, 0xE0, 0xBE,
	0x80, 0x9E, 0x4C, 0xF0, 0x9D, 0x85, 0x98, 0xF0,
	0x9D, 0x85, 0xA5, 0xF0, 0x9D, 0x85, 0xAE, 0xAE,
	0x4C, 0xF0, 0x9D, 0x85, 0x98, 0xF0, 0x9D, 0x85,
	0xA5, 0xF0, 0x9D, 0x85, 0xAF, 0xAE, 0x4C, 0xF0,
	0x9D, 0x85, 0x98, 0xF0, 0x9D, 0x85, 0xA5, 0xF0,
	0x9D, 0x85, 0xB0, 0xAE, 0x4C, 0xF0, 0x9D, 0x85,
	0x98, 0xF0, 0x9D, 0x85, 0xA5, 0xF0, 0x9D, 0x85,
	// Bytes 4640 - 467f
	0xB1, 0xAE, 0x4C, 0xF0, 0x9D, 0x85, 0x98, 0xF0,
	0x9D, 0x85, 0xA5, 0xF0, 0x9D, 0x85, 0xB2, 0xAE,
	0x4C, 0xF0, 0x9D, 0x86, 0xB9, 0xF0, 0x9D, 0x85,
	0xA5, 0xF0, 0x9D, 0x85, 0xAE, 0xAE, 0x4C, 0xF0,
	0x9D, 0x86, 0xB9, 0xF0, 0x9D, 0x85, 0xA5, 0xF0,
	0x9D, 0x85, 0xAF, 0xAE, 0x4C, 0xF0, 0x9D, 0x86,
	0xBA, 0xF0, 0x9D, 0x85, 0xA5, 0xF0, 0x9D, 0x85,
	0xAE, 0xAE, 0x4C, 0xF0, 0x9D, 0x86, 0xBA, 0xF0,
	// Bytes 4680 - 46bf
	0x9D, 0x85, 0xA5, 0xF0, 0x9D, 0x85, 0xAF, 0xAE,
	0x83, 0x41, 0xCC, 0x82, 0xC9, 0x83, 0x41, 0xCC,
	0x86, 0xC9, 0x83, 0x41, 0xCC, 0x87, 0xC9, 0x83,
	0x41, 0xCC, 0x88, 0xC9, 0x83, 0x41, 0xCC, 0x8A,
	0xC9, 0x83, 0x41, 0xCC, 0xA3, 0xB5, 0x83, 0x43,
	0xCC, 0xA7, 0xA5, 0x83, 0x45, 0xCC, 0x82, 0xC9,
	0x83, 0x45, 0xCC, 0x84, 0xC9, 0x83, 0x45, 0xCC,
	0xA3, 0xB5, 0x83, 0x45, 0xCC, 0xA7, 0xA5, 0x83,
	// Bytes 46c0 - 46ff
	0x49, 0xCC, 0x88, 0xC9, 0x83, 0x4C, 0xCC, 0xA3,
	0xB5, 0x83, 0x4F, 0xCC, 0x82, 0xC9, 0x83, 0x4F,
	0xCC, 0x83, 0xC9, 0x83, 0x4F, 0xCC, 0x84, 0xC9,
	0x83, 0x4F, 0xCC, 0x87, 0xC9, 0x83, 0x4F, 0xCC,
	0x88, 0xC9, 0x83, 0x4F, 0xCC, 0x9B, 0xAD, 0x83,
	0x4F, 0xCC, 0xA3, 0xB5, 0x83, 0x4F, 0xCC, 0xA8,
	0xA5, 0x83, 0x52, 0xCC, 0xA3, 0xB5, 0x83, 0x53,
	0xCC, 0x81, 0xC9, 0x83, 0x53, 0xCC, 0x8C, 0xC9,
	// Bytes 4700 - 473f
	0x83, 0x53, 0xCC, 0xA3, 0xB5, 0x83, 0x55, 0xCC,
	0x83, 0xC9, 0x83, 0x55, 0xCC, 0x84, 0xC9, 0x83,
	0x55, 0xCC, 0x88, 0xC9, 0x83, 0x55, 0xCC, 0x9B,
	0xAD, 0x83, 0x61, 0xCC, 0x82, 0xC9, 0x83, 0x61,
	0xCC, 0x86, 0xC9, 0x83, 0x61, 0xCC, 0x87, 0xC9,
	0x83, 0x61, 0xCC, 0x88, 0xC9, 0x83, 0x61, 0xCC,
	0x8A, 0xC9, 0x83, 0x61, 0xCC, 0xA3, 0xB5, 0x83,
	0x63, 0xCC, 0xA7, 0xA5, 0x83, 0x65, 0xCC, 0x82,
	// Bytes 4740 - 477f
	0xC9, 0x83, 0x65, 0xCC, 0x84, 0xC9, 0x83, 0x65,
	0xCC, 0xA3, 0xB5, 0x83, 0x65, 0xCC, 0xA7, 0xA5,
	0x83, 0x69, 0xCC, 0x88, 0xC9, 0x83, 0x6C, 0xCC,
	0xA3, 0xB5, 0x83, 0x6F, 0xCC, 0x82, 0xC9, 0x83,
	0x6F, 0xCC, 0x83, 0xC9, 0x83, 0x6F, 0xCC, 0x84,
	0xC9, 0x83, 0x6F, 0xCC, 0x87, 0xC9, 0x83, 0x6F,
	0xCC, 0x88, 0xC9, 0x83, 0x6F, 0xCC, 0x9B, 0xAD,
	0x83, 0x6F, 0xCC, 0xA3, 0xB5, 0x83, 0x6F, 0xCC,
	// Bytes 4780 - 47bf
	0xA8, 0xA5, 0x83, 0x72, 0xCC, 0xA3, 0xB5, 0x83,
	0x73, 0xCC, 0x81, 0xC9, 0x83, 0x73, 0xCC, 0x8C,
	0xC9, 0x83, 0x73, 0xCC, 0xA3, 0xB5, 0x83, 0x75,
	0xCC, 0x83, 0xC9, 0x83, 0x75, 0xCC, 0x84, 0xC9,
	0x83, 0x75, 0xCC, 0x88, 0xC9, 0x83, 0x75, 0xCC,
	0x9B, 0xAD, 0x84, 0xCE, 0x91, 0xCC, 0x93, 0xC9,
	0x84, 0xCE, 0x91, 0xCC, 0x94, 0xC9, 0x84, 0xCE,
	0x95, 0xCC, 0x93, 0xC9, 0x84, 0xCE, 0x95, 0xCC,
	// Bytes 47c0 - 47ff
	0x94, 0xC9, 0x84, 0xCE, 0x97, 0xCC, 0x93, 0xC9,
	0x84, 0xCE, 0x97, 0xCC, 0x94, 0xC9, 0x84, 0xCE,
	0x99, 0xCC, 0x93, 0xC9, 0x84, 0xCE, 0x99, 0xCC,
	0x94, 0xC9, 0x84, 0xCE, 0x9F, 0xCC, 0x93, 0xC9,
	0x84, 0xCE, 0x9F, 0xCC, 0x94, 0xC9, 0x84, 0xCE,
	0xA5, 0xCC, 0x94, 0xC9, 0x84, 0xCE, 0xA9, 0xCC,
	0x93, 0xC9, 0x84, 0xCE, 0xA9, 0xCC, 0x94, 0xC9,
	0x84, 0xCE, 0xB1, 0xCC, 0x80, 0xC9, 0x84, 0xCE,
	// Bytes 4800 - 483f
	0xB1, 0xCC, 0x81, 0xC9, 0x84, 0xCE, 0xB1, 0xCC,
	0x93, 0xC9, 0x84, 0xCE, 0xB1, 0xCC, 0x94, 0xC9,
	0x84, 0xCE, 0xB1, 0xCD, 0x82, 0xC9, 0x84, 0xCE,
	0xB5, 0xCC, 0x93, 0xC9, 0x84, 0xCE, 0xB5, 0xCC,
	0x94, 0xC9, 0x84, 0xCE, 0xB7, 0xCC, 0x80, 0xC9,
	0x84, 0xCE, 0xB7, 0xCC, 0x81, 0xC9, 0x84, 0xCE,
	0xB7, 0xCC, 0x93, 0xC9, 0x84, 0xCE, 0xB7, 0xCC,
	0x94, 0xC9, 0x84, 0xCE, 0xB7, 0xCD, 0x82, 0xC9,
	// Bytes 4840 - 487f
	0x84, 0xCE, 0xB9, 0xCC, 0x88, 0xC9, 0x84, 0xCE,
	0xB9, 0xCC, 0x93, 0xC9, 0x84, 0xCE, 0xB9, 0xCC,
	0x94, 0xC9, 0x84, 0xCE, 0xBF, 0xCC, 0x93, 0xC9,
	0x84, 0xCE, 0xBF, 0xCC, 0x94, 0xC9, 0x84, 0xCF,
	0x85, 0xCC, 0x88, 0xC9, 0x84, 0xCF, 0x85, 0xCC,
	0x93, 0xC9, 0x84, 0xCF, 0x85, 0xCC, 0x94, 0xC9,
	0x84, 0xCF, 0x89, 0xCC, 0x80, 0xC9, 0x84, 0xCF,
	0x89, 0xCC, 0x81, 0xC9, 0x84, 0xCF, 0x89, 0xCC,
	// Bytes 4880 - 48bf
	0x93, 0xC9, 0x84, 0xCF, 0x89, 0xCC, 0x94, 0xC9,
	0x84, 0xCF, 0x89, 0xCD, 0x82, 0xC9, 0x86, 0xCE,
	0x91, 0xCC, 0x93, 0xCC, 0x80, 0xCA, 0x86, 0xCE,
	0x91, 0xCC, 0x93, 0xCC, 0x81, 0xCA, 0x86, 0xCE,
	0x91, 0xCC, 0x93, 0xCD, 0x82, 0xCA, 0x86, 0xCE,
	0x91, 0xCC, 0x94, 0xCC, 0x80, 0xCA, 0x86, 0xCE,
	0x91, 0xCC, 0x94, 0xCC, 0x81, 0xCA, 0x86, 0xCE,
	0x91, 0xCC, 0x94, 0xCD, 0x82, 0xCA, 0x86, 0xCE,
	// Bytes 48c0 - 48ff
	0x97, 0xCC, 0x93, 0xCC, 0x80, 0xCA, 0x86, 0xCE,
	0x97, 0xCC, 0x93, 0xCC, 0x81, 0xCA, 0x86, 0xCE,
	0x97, 0xCC, 0x93, 0xCD, 0x82, 0xCA, 0x86, 0xCE,
	0x97, 0xCC, 0x94, 0xCC, 0x80, 0xCA, 0x86, 0xCE,
	0x97, 0xCC, 0x94, 0xCC, 0x81, 0xCA, 0x86, 0xCE,
	0x97, 0xCC, 0x94, 0xCD, 0x82, 0xCA, 0x86, 0xCE,
	0xA9, 0xCC, 0x93, 0xCC, 0x80, 0xCA, 0x86, 0xCE,
	0xA9, 0xCC, 0x93, 0xCC, 0x81, 0xCA, 0x86, 0xCE,
	// Bytes 4900 - 493f
	0xA9, 0xCC, 0x93, 0xCD, 0x82, 0xCA, 0x86, 0xCE,
	0xA9, 0xCC, 0x94, 0xCC, 0x80, 0xCA, 0x86, 0xCE,
	0xA9, 0xCC, 0x94, 0xCC, 0x81, 0xCA, 0x86, 0xCE,
	0xA9, 0xCC, 0x94, 0xCD, 0x82, 0xCA, 0x86, 0xCE,
	0xB1, 0xCC, 0x93, 0xCC, 0x80, 0xCA, 0x86, 0xCE,
	0xB1, 0xCC, 0x93, 0xCC, 0x81, 0xCA, 0x86, 0xCE,
	0xB1, 0xCC, 0x93, 0xCD, 0x82, 0xCA, 0x86, 0xCE,
	0xB1, 0xCC, 0x94, 0xCC, 0x80, 0xCA, 0x86, 0xCE,
	// Bytes 4940 - 497f
	0xB1, 0xCC, 0x94, 0xCC, 0x81, 0xCA, 0x86, 0xCE,
	0xB1, 0xCC, 0x94, 0xCD, 0x82, 0xCA, 0x86, 0xCE,
	0xB7, 0xCC, 0x93, 0xCC, 0x80, 0xCA, 0x86, 0xCE,
	0xB7, 0xCC, 0x93, 0xCC, 0x81, 0xCA, 0x86, 0xCE,
	0xB7, 0xCC, 0x93, 0xCD, 0x82, 0xCA, 0x86, 0xCE,
	0xB7, 0xCC, 0x94, 0xCC, 0x80, 0xCA, 0x86, 0xCE,
	0xB7, 0xCC, 0x94, 0xCC, 0x81, 0xCA, 0x86, 0xCE,
	0xB7, 0xCC, 0x94, 0xCD, 0x82, 0xCA, 0x86, 0xCF,
	// Bytes 4980 - 49bf
	0x89, 0xCC, 0x93, 0xCC, 0x80, 0xCA, 0x86, 0xCF,
	0x89, 0xCC, 0x93, 0xCC, 0x81, 0xCA, 0x86, 0xCF,
	0x89, 0xCC, 0x93, 0xCD, 0x82, 0xCA, 0x86, 0xCF,
	0x89, 0xCC, 0x94, 0xCC, 0x80, 0xCA, 0x86, 0xCF,
	0x89, 0xCC, 0x94, 0xCC, 0x81, 0xCA, 0x86, 0xCF,
	0x89, 0xCC, 0x94, 0xCD, 0x82, 0xCA, 0x42, 0xCC,
	0x80, 0xC9, 0x32, 0x42, 0xCC, 0x81, 0xC9, 0x32,
	0x42, 0xCC, 0x93, 0xC9, 0x32, 0x43, 0xE1, 0x85,
	// Bytes 49c0 - 49ff
	0xA1, 0x01, 0x00, 0x43, 0xE1, 0x85, 0xA2, 0x01,
	0x00, 0x43, 0xE1, 0x85, 0xA3, 0x01, 0x00, 0x43,
	0xE1, 0x85, 0xA4, 0x01, 0x00, 0x43, 0xE1, 0x85,
	0xA5, 0x01, 0x00, 0x43, 0xE1, 0x85, 0xA6, 0x01,
	0x00, 0x43, 0xE1, 0x85, 0xA7, 0x01, 0x00, 0x43,
	0xE1, 0x85, 0xA8, 0x01, 0x00, 0x43, 0xE1, 0x85,
	0xA9, 0x01, 0x00, 0x43, 0xE1, 0x85, 0xAA, 0x01,
	0x00, 0x43, 0xE1, 0x85, 0xAB, 0x01, 0x00, 0x43,
	// Bytes 4a00 - 4a3f
	0xE1, 0x85, 0xAC, 0x01, 0x00, 0x43, 0xE1, 0x85,
	0xAD, 0x01, 0x00, 0x43, 0xE1, 0x85, 0xAE, 0x01,
	0x00, 0x43, 0xE1, 0x85, 0xAF, 0x01, 0x00, 0x43,
	0xE1, 0x85, 0xB0, 0x01, 0x00, 0x43, 0xE1, 0x85,
	0xB1, 0x01, 0x00, 0x43, 0xE1, 0x85, 0xB2, 0x01,
	0x00, 0x43, 0xE1, 0x85, 0xB3, 0x01, 0x00, 0x43,
	0xE1, 0x85, 0xB4, 0x01, 0x00, 0x43, 0xE1, 0x85,
	0xB5, 0x01, 0x00, 0x43, 0xE1, 0x86, 0xAA, 0x01,
	// Bytes 4a40 - 4a7f
	0x00, 0x43, 0xE1, 0x86, 0xAC, 0x01, 0x00, 0x43,
	0xE1, 0x86, 0xAD, 0x01, 0x00, 0x43, 0xE1, 0x86,
	0xB0, 0x01, 0x00, 0x43, 0xE1, 0x86, 0xB1, 0x01,
	0x00, 0x43, 0xE1, 0x86, 0xB2, 0x01, 0x00, 0x43,
	0xE1, 0x86, 0xB3, 0x01, 0x00, 0x43, 0xE1, 0x86,
	0xB4, 0x01, 0x00, 0x43, 0xE1, 0x86, 0xB5, 0x01,
	0x00, 0x44, 0xCC, 0x88, 0xCC, 0x81, 0xCA, 0x32,
	0x43, 0xE3, 0x82, 0x99, 0x0D, 0x03, 0x43, 0xE3,
	// Bytes 4a80 - 4abf
	0x82, 0x9A, 0x0D, 0x03, 0x46, 0xE0, 0xBD, 0xB1,
	0xE0, 0xBD, 0xB2, 0x9E, 0x26, 0x46, 0xE0, 0xBD,
	0xB1, 0xE0, 0xBD, 0xB4, 0xA2, 0x26, 0x46, 0xE0,
	0xBD, 0xB1, 0xE0, 0xBE, 0x80, 0x9E, 0x26, 0x00,
	0x01,
}

// lookup returns the trie value for the first UTF-8 encoding in s and
// the width in bytes of this encoding. The size will be 0 if s does not
// hold enough bytes to complete the encoding. len(s) must be greater than 0.
func (t *nfcTrie) lookup(s []byte) (v uint16, sz int) {
	c0 := s[0]
	switch {
	case c0 < 0x80: // is ASCII
		return nfcValues[c0], 1
	case c0 < 0xC2:
		return 0, 1 // Illegal UTF-8: not a starter, not ASCII.
	case c0 < 0xE0: // 2-byte UTF-8
		if len(s) < 2 {
			return 0, 0
		}
		i := nfcIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c1), 2
	case c0 < 0xF0: // 3-byte UTF-8
		if len(s) < 3 {
			return 0, 0
		}
		i := nfcIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = nfcIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c2), 3
	case c0 < 0xF8: // 4-byte UTF-8
		if len(s) < 4 {
			return 0, 0
		}
		i := nfcIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = nfcIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		o = uint32(i)<<6 + uint32(c2)
		i = nfcIndex[o]
		c3 := s[3]
		if c3 < 0x80 || 0xC0 <= c3 {
			return 0, 3 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c3), 4
	}
	// Illegal rune
	return 0, 1
}

// lookupUnsafe returns the trie value for the first UTF-8 encoding in s.
// s must start with a full and valid UTF-8 encoded rune.
func (t *nfcTrie) lookupUnsafe(s []byte) uint16 {
	c0 := s[0]
	if c0 < 0x80 { // is ASCII
		return nfcValues[c0]
	}
	i := nfcIndex[c0]
	if c0 < 0xE0 { // 2-byte UTF-8
		return t.lookupValue(uint32(i), s[1])
	}
	i = nfcIndex[uint32(i)<<6+uint32(s[1])]
	if c0 < 0xF0 { // 3-byte UTF-8
		return t.lookupValue(uint32(i), s[2])
	}
	i = nfcIndex[uint32(i)<<6+uint32(s[2])]
	if c0 < 0xF8 { // 4-byte UTF-8
		return t.lookupValue(uint32(i), s[3])
	}
	return 0
}

// lookupString returns the trie value for the first UTF-8 encoding in s and
// the width in bytes of this encoding. The size will be 0 if s does not
// hold enough bytes to complete the encoding. len(s) must be greater than 0.
func (t *nfcTrie) lookupString(s string) (v uint16, sz int) {
	c0 := s[0]
	switch {
	case c0 < 0x80: // is ASCII
		return nfcValues[c0], 1
	case c0 < 0xC2:
		return 0, 1 // Illegal UTF-8: not a starter, not ASCII.
	case c0 < 0xE0: // 2-byte UTF-8
		if len(s) < 2 {
			return 0, 0
		}
		i := nfcIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c1), 2
	case c0 < 0xF0: // 3-byte UTF-8
		if len(s) < 3 {
			return 0, 0
		}
		i := nfcIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = nfcIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c2), 3
	case c0 < 0xF8: // 4-byte UTF-8
		if len(s) < 4 {
			return 0, 0
		}
		i := nfcIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = nfcIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		o = uint32(i)<<6 + uint32(c2)
		i = nfcIndex[o]
		c3 := s[3]
		if c3 < 0x80 || 0xC0 <= c3 {
			return 0, 3 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c3), 4
	}
	// Illegal rune
	return 0, 1
}

// lookupStringUnsafe returns the trie value for the first UTF-8 encoding in s.
// s must start with a full and valid UTF-8 encoded rune.
func (t *nfcTrie) lookupStringUnsafe(s string) uint16 {
	c0 := s[0]
	if c0 < 0x80 { // is ASCII
		return nfcValues[c0]
	}
	i := nfcIndex[c0]
	if c0 < 0xE0 { // 2-byte UTF-8
		return t.lookupValue(uint32(i), s[1])
	}
	i = nfcIndex[uint32(i)<<6+uint32(s[1])]
	if c0 < 0xF0 { // 3-byte UTF-8
		return t.lookupValue(uint32(i), s[2])
	}
	i = nfcIndex[uint32(i)<<6+uint32(s[2])]
	if c0 < 0xF8 { // 4-byte UTF-8
		return t.lookupValue(uint32(i), s[3])
	}
	return 0
}

// nfcTrie. Total size: 10442 bytes (10.20 KiB). Checksum: 4ba400a9d8208e03.
type nfcTrie struct{}

func newNfcTrie(i int) *nfcTrie {
	return &nfcTrie{}
}

// lookupValue determines the type of block n and looks up the value for b.
func (t *nfcTrie) lookupValue(n uint32, b byte) uint16 {
	switch {
	case n < 45:
		return uint16(nfcValues[n<<6+uint32(b)])
	default:
		n -= 45
		return uint16(nfcSparse.lookup(n, b))
	}
}

// nfcValues: 47 blocks, 3008 entries, 6016 bytes
// The third block is the zero block.
var nfcValues = [3008]uint16{
	// Block 0x0, offset 0x0
	0x3c: 0xa000, 0x3d: 0xa000, 0x3e: 0xa000,
	// Block 0x1, offset 0x40
	0x41: 0xa000, 0x42: 0xa000, 0x43: 0xa000, 0x44: 0xa000, 0x45: 0xa000,
	0x46: 0xa000, 0x47: 0xa000, 0x48: 0xa000, 0x49: 0xa000, 0x4a: 0xa000, 0x4b: 0xa000,
	0x4c: 0xa000, 0x4d: 0xa000, 0x4e: 0xa000, 0x4f: 0xa000, 0x50: 0xa000,
	0x52: 0xa000, 0x53: 0xa000, 0x54: 0xa000, 0x55: 0xa000, 0x56: 0xa000, 0x57: 0xa000,
	0x58: 0xa000, 0x59: 0xa000, 0x5a: 0xa000,
	0x61: 0xa000, 0x62: 0xa000, 0x63: 0xa000,
	0x64: 0xa000, 0x65: 0xa000, 0x66: 0xa000, 0x67: 0xa000, 0x68: 0xa000, 0x69: 0xa000,
	0x6a: 0xa000, 0x6b: 0xa000, 0x6c: 0xa000, 0x6d: 0xa000, 0x6e: 0xa000, 0x6f: 0xa000,
	0x70: 0xa000, 0x72: 0xa000, 0x73: 0xa000, 0x74: 0xa000, 0x75: 0xa000,
	0x76: 0xa000, 0x77: 0xa000, 0x78: 0xa000, 0x79: 0xa000, 0x7a: 0xa000,
	// Block 0x2, offset 0x80
	// Block 0x3, offset 0xc0
	0xc0: 0x2f6f, 0xc1: 0x2f74, 0xc2: 0x4688, 0xc3: 0x2f79, 0xc4: 0x4697, 0xc5: 0x469c,
	0xc6: 0xa000, 0xc7: 0x46a6, 0xc8: 0x2fe2, 0xc9: 0x2fe7, 0xca: 0x46ab, 0xcb: 0x2ffb,
	0xcc: 0x306e, 0xcd: 0x3073, 0xce: 0x3078, 0xcf: 0x46bf, 0xd1: 0x3104,
	0xd2: 0x3127, 0xd3: 0x312c, 0xd4: 0x46c9, 0xd5: 0x46ce, 0xd6: 0x46dd,
	0xd8: 0xa000, 0xd9: 0x31b3, 0xda: 0x31b8, 0xdb: 0x31bd, 0xdc: 0x470f, 0xdd: 0x3235,
	0xe0: 0x327b, 0xe1: 0x3280, 0xe2: 0x4719, 0xe3: 0x3285,
	0xe4: 0x4728, 0xe5: 0x472d, 0xe6: 0xa000, 0xe7: 0x4737, 0xe8: 0x32ee, 0xe9: 0x32f3,
	0xea: 0x473c, 0xeb: 0x3307, 0xec: 0x337f, 0xed: 0x3384, 0xee: 0x3389, 0xef: 0x4750,
	0xf1: 0x3415, 0xf2: 0x3438, 0xf3: 0x343d, 0xf4: 0x475a, 0xf5: 0x475f,
	0xf6: 0x476e, 0xf8: 0xa000, 0xf9: 0x34c9, 0xfa: 0x34ce, 0xfb: 0x34d3,
	0xfc: 0x47a0, 0xfd: 0x3550, 0xff: 0x3569,
	// Block 0x4, offset 0x100
	0x100: 0x2f7e, 0x101: 0x328a, 0x102: 0x468d, 0x103: 0x471e, 0x104: 0x2f9c, 0x105: 0x32a8,
	0x106: 0x2fb0, 0x107: 0x32bc, 0x108: 0x2fb5, 0x109: 0x32c1, 0x10a: 0x2fba, 0x10b: 0x32c6,
	0x10c: 0x2fbf, 0x10d: 0x32cb, 0x10e: 0x2fc9, 0x10f: 0x32d5,
	0x112: 0x46b0, 0x113: 0x4741, 0x114: 0x2ff1, 0x115: 0x32fd, 0x116: 0x2ff6, 0x117: 0x3302,
	0x118: 0x3014, 0x119: 0x3320, 0x11a: 0x3005, 0x11b: 0x3311, 0x11c: 0x302d, 0x11d: 0x3339,
	0x11e: 0x3037, 0x11f: 0x3343, 0x120: 0x303c, 0x121: 0x3348, 0x122: 0x3046, 0x123: 0x3352,
	0x124: 0x304b, 0x125: 0x3357, 0x128: 0x307d, 0x129: 0x338e,
	0x12a: 0x3082, 0x12b: 0x3393, 0x12c: 0x3087, 0x12d: 0x3398, 0x12e: 0x30aa, 0x12f: 0x33b6,
	0x130: 0x308c, 0x134: 0x30b4, 0x135: 0x33c0,
	0x136: 0x30c8, 0x137: 0x33d9, 0x139: 0x30d2, 0x13a: 0x33e3, 0x13b: 0x30dc,
	0x13c: 0x33ed, 0x13d: 0x30d7, 0x13e: 0x33e8,
	// Block 0x5, offset 0x140
	0x143: 0x30ff, 0x144: 0x3410, 0x145: 0x3118,
	0x146: 0x3429, 0x147: 0x310e, 0x148: 0x341f,
	0x14c: 0x46d3, 0x14d: 0x4764, 0x14e: 0x3131, 0x14f: 0x3442, 0x150: 0x313b, 0x151: 0x344c,
	0x154: 0x3159, 0x155: 0x346a, 0x156: 0x3172, 0x157: 0x3483,
	0x158: 0x3163, 0x159: 0x3474, 0x15a: 0x46f6, 0x15b: 0x4787, 0x15c: 0x317c, 0x15d: 0x348d,
	0x15e: 0x318b, 0x15f: 0x349c, 0x160: 0x46fb, 0x161: 0x478c, 0x162: 0x31a4, 0x163: 0x34ba,
	0x164: 0x3195, 0x165: 0x34ab, 0x168: 0x4705, 0x169: 0x4796,
	0x16a: 0x470a, 0x16b: 0x479b, 0x16c: 0x31c2, 0x16d: 0x34d8, 0x16e: 0x31cc, 0x16f: 0x34e2,
	0x170: 0x31d1, 0x171: 0x34e7, 0x172: 0x31ef, 0x173: 0x3505, 0x174: 0x3212, 0x175: 0x3528,
	0x176: 0x323a, 0x177: 0x3555, 0x178: 0x324e, 0x179: 0x325d, 0x17a: 0x357d, 0x17b: 0x3267,
	0x17c: 0x3587, 0x17d: 0x326c, 0x17e: 0x358c, 0x17f: 0xa000,
	// Block 0x6, offset 0x180
	0x184: 0x8100, 0x185: 0x8100,
	0x186: 0x8100,
	0x18d: 0x2f88, 0x18e: 0x3294, 0x18f: 0x3096, 0x190: 0x33a2, 0x191: 0x3140,
	0x192: 0x3451, 0x193: 0x31d6, 0x194: 0x34ec, 0x195: 0x39cf, 0x196: 0x3b5e, 0x197: 0x39c8,
	0x198: 0x3b57, 0x199: 0x39d6, 0x19a: 0x3b65, 0x19b: 0x39c1, 0x19c: 0x3b50,
	0x19e: 0x38b0, 0x19f: 0x3a3f, 0x1a0: 0x38a9, 0x1a1: 0x3a38, 0x1a2: 0x35b3, 0x1a3: 0x35c5,
	0x1a6: 0x3041, 0x1a7: 0x334d, 0x1a8: 0x30be, 0x1a9: 0x33cf,
	0x1aa: 0x46ec, 0x1ab: 0x477d, 0x1ac: 0x3990, 0x1ad: 0x3b1f, 0x1ae: 0x35d7, 0x1af: 0x35dd,
	0x1b0: 0x33c5, 0x1b4: 0x3028, 0x1b5: 0x3334,
	0x1b8: 0x30fa, 0x1b9: 0x340b, 0x1ba: 0x38b7, 0x1bb: 0x3a46,
	0x1bc: 0x35ad, 0x1bd: 0x35bf, 0x1be: 0x35b9, 0x1bf: 0x35cb,
	// Block 0x7, offset 0x1c0
	0x1c0: 0x2f8d, 0x1c1: 0x3299, 0x1c2: 0x2f92, 0x1c3: 0x329e, 0x1c4: 0x300a, 0x1c5: 0x3316,
	0x1c6: 0x300f, 0x1c7: 0x331b, 0x1c8: 0x309b, 0x1c9: 0x33a7, 0x1ca: 0x30a0, 0x1cb: 0x33ac,
	0x1cc: 0x3145, 0x1cd: 0x3456, 0x1ce: 0x314a, 0x1cf: 0x345b, 0x1d0: 0x3168, 0x1d1: 0x3479,
	0x1d2: 0x316d, 0x1d3: 0x347e, 0x1d4: 0x31db, 0x1d5: 0x34f1, 0x1d6: 0x31e0, 0x1d7: 0x34f6,
	0x1d8: 0x3186, 0x1d9: 0x3497, 0x1da: 0x319f, 0x1db: 0x34b5,
	0x1de: 0x305a, 0x1df: 0x3366,
	0x1e6: 0x4692, 0x1e7: 0x4723, 0x1e8: 0x46ba, 0x1e9: 0x474b,
	0x1ea: 0x395f, 0x1eb: 0x3aee, 0x1ec: 0x393c, 0x1ed: 0x3acb, 0x1ee: 0x46d8, 0x1ef: 0x4769,
	0x1f0: 0x3958, 0x1f1: 0x3ae7, 0x1f2: 0x3244, 0x1f3: 0x355f,
	// Block 0x8, offset 0x200
	0x200: 0x9932, 0x201: 0x9932, 0x202: 0x9932, 0x203: 0x9932, 0x204: 0x9932, 0x205: 0x8132,
	0x206: 0x9932, 0x207: 0x9932, 0x208: 0x9932, 0x209: 0x9932, 0x20a: 0x9932, 0x20b: 0x9932,
	0x20c: 0x9932, 0x20d: 0x8132, 0x20e: 0x8132, 0x20f: 0x9932, 0x210: 0x8132, 0x211: 0x9932,
	0x212: 0x8132, 0x213: 0x9932, 0x214: 0x9932, 0x215: 0x8133, 0x216: 0x812d, 0x217: 0x812d,
	0x218: 0x812d, 0x219: 0x812d, 0x21a: 0x8133, 0x21b: 0x992b, 0x21c: 0x812d, 0x21d: 0x812d,
	0x21e: 0x812d, 0x21f: 0x812d, 0x220: 0x812d, 0x221: 0x8129, 0x222: 0x8129, 0x223: 0x992d,
	0x224: 0x992d, 0x225: 0x992d, 0x226: 0x992d, 0x227: 0x9929, 0x228: 0x9929, 0x229: 0x812d,
	0x22a: 0x812d, 0x22b: 0x812d, 0x22c: 0x812d, 0x22d: 0x992d, 0x22e: 0x992d, 0x22f: 0x812d,
	0x230: 0x992d, 0x231: 0x992d, 0x232: 0x812d, 0x233: 0x812d, 0x234: 0x8101, 0x235: 0x8101,
	0x236: 0x8101, 0x237: 0x8101, 0x238: 0x9901, 0x239: 0x812d, 0x23a: 0x812d, 0x23b: 0x812d,
	0x23c: 0x812d, 0x23d: 0x8132, 0x23e: 0x8132, 0x23f: 0x8132,
	// Block 0x9, offset 0x240
	0x240: 0x49ae, 0x241: 0x49b3, 0x242: 0x9932, 0x243: 0x49b8, 0x244: 0x4a71, 0x245: 0x9936,
	0x246: 0x8132, 0x247: 0x812d, 0x248: 0x812d, 0x249: 0x812d, 0x24a: 0x8132, 0x24b: 0x8132,
	0x24c: 0x8132, 0x24d: 0x812d, 0x24e: 0x812d, 0x250: 0x8132, 0x251: 0x8132,
	0x252: 0x8132, 0x253: 0x812d, 0x254: 0x812d, 0x255: 0x812d, 0x256: 0x812d, 0x257: 0x8132,
	0x258: 0x8133, 0x259: 0x812d, 0x25a: 0x812d, 0x25b: 0x8132, 0x25c: 0x8134, 0x25d: 0x8135,
	0x25e: 0x8135, 0x25f: 0x8134, 0x260: 0x8135, 0x261: 0x8135, 0x262: 0x8134, 0x263: 0x8132,
	0x264: 0x8132, 0x265: 0x8132, 0x266: 0x8132, 0x267: 0x8132, 0x268: 0x8132, 0x269: 0x8132,
	0x26a: 0x8132, 0x26b: 0x8132, 0x26c: 0x8132, 0x26d: 0x8132, 0x26e: 0x8132, 0x26f: 0x8132,
	0x274: 0x0170,
	0x27a: 0x8100,
	0x27e: 0x0037,
	// Block 0xa, offset 0x280
	0x284: 0x8100, 0x285: 0x35a1,
	0x286: 0x35e9, 0x287: 0x00ce, 0x288: 0x3607, 0x289: 0x3613, 0x28a: 0x3625,
	0x28c: 0x3643, 0x28e: 0x3655, 0x28f: 0x3673, 0x290: 0x3e08, 0x291: 0xa000,
	0x295: 0xa000, 0x297: 0xa000,
	0x299: 0xa000,
	0x29f: 0xa000, 0x2a1: 0xa000,
	0x2a5: 0xa000, 0x2a9: 0xa000,
	0x2aa: 0x3637, 0x2ab: 0x3667, 0x2ac: 0x47fe, 0x2ad: 0x3697, 0x2ae: 0x4828, 0x2af: 0x36a9,
	0x2b0: 0x3e70, 0x2b1: 0xa000, 0x2b5: 0xa000,
	0x2b7: 0xa000, 0x2b9: 0xa000,
	0x2bf: 0xa000,
	// Block 0xb, offset 0x2c0
	0x2c0: 0x3721, 0x2c1: 0x372d, 0x2c3: 0x371b,
	0x2c6: 0xa000, 0x2c7: 0x3709,
	0x2cc: 0x375d, 0x2cd: 0x3745, 0x2ce: 0x376f, 0x2d0: 0xa000,
	0x2d3: 0xa000, 0x2d5: 0xa000, 0x2d6: 0xa000, 0x2d7: 0xa000,
	0x2d8: 0xa000, 0x2d9: 0x3751, 0x2da: 0xa000,
	0x2de: 0xa000, 0x2e3: 0xa000,
	0x2e7: 0xa000,
	0x2eb: 0xa000, 0x2ed: 0xa000,
	0x2f0: 0xa000, 0x2f3: 0xa000, 0x2f5: 0xa000,
	0x2f6: 0xa000, 0x2f7: 0xa000, 0x2f8: 0xa000, 0x2f9: 0x37d5, 0x2fa: 0xa000,
	0x2fe: 0xa000,
	// Block 0xc, offset 0x300
	0x301: 0x3733, 0x302: 0x37b7,
	0x310: 0x370f, 0x311: 0x3793,
	0x312: 0x3715, 0x313: 0x3799, 0x316: 0x3727, 0x317: 0x37ab,
	0x318: 0xa000, 0x319: 0xa000, 0x31a: 0x3829, 0x31b: 0x382f, 0x31c: 0x3739, 0x31d: 0x37bd,
	0x31e: 0x373f, 0x31f: 0x37c3, 0x322: 0x374b, 0x323: 0x37cf,
	0x324: 0x3757, 0x325: 0x37db, 0x326: 0x3763, 0x327: 0x37e7, 0x328: 0xa000, 0x329: 0xa000,
	0x32a: 0x3835, 0x32b: 0x383b, 0x32c: 0x378d, 0x32d: 0x3811, 0x32e: 0x3769, 0x32f: 0x37ed,
	0x330: 0x3775, 0x331: 0x37f9, 0x332: 0x377b, 0x333: 0x37ff, 0x334: 0x3781, 0x335: 0x3805,
	0x338: 0x3787, 0x339: 0x380b,
	// Block 0xd, offset 0x340
	0x351: 0x812d,
	0x352: 0x8132, 0x353: 0x8132, 0x354: 0x8132, 0x355: 0x8132, 0x356: 0x812d, 0x357: 0x8132,
	0x358: 0x8132, 0x359: 0x8132, 0x35a: 0x812e, 0x35b: 0x812d, 0x35c: 0x8132, 0x35d: 0x8132,
	0x35e: 0x8132, 0x35f: 0x8132, 0x360: 0x8132, 0x361: 0x8132, 0x362: 0x812d, 0x363: 0x812d,
	0x364: 0x812d, 0x365: 0x812d, 0x366: 0x812d, 0x367: 0x812d, 0x368: 0x8132, 0x369: 0x8132,
	0x36a: 0x812d, 0x36b: 0x8132, 0x36c: 0x8132, 0x36d: 0x812e, 0x36e: 0x8131, 0x36f: 0x8132,
	0x370: 0x8105, 0x371: 0x8106, 0x372: 0x8107, 0x373: 0x8108, 0x374: 0x8109, 0x375: 0x810a,
	0x376: 0x810b, 0x377: 0x810c, 0x378: 0x810d, 0x379: 0x810e, 0x37a: 0x810e, 0x37b: 0x810f,
	0x37c: 0x8110, 0x37d: 0x8111, 0x37f: 0x8112,
	// Block 0xe, offset 0x380
	0x388: 0xa000, 0x38a: 0xa000, 0x38b: 0x8116,
	0x38c: 0x8117, 0x38d: 0x8118, 0x38e: 0x8119, 0x38f: 0x811a, 0x390: 0x811b, 0x391: 0x811c,
	0x392: 0x811d, 0x393: 0x9932, 0x394: 0x9932, 0x395: 0x992d, 0x396: 0x812d, 0x397: 0x8132,
	0x398: 0x8132, 0x399: 0x8132, 0x39a: 0x8132, 0x39b: 0x8132, 0x39c: 0x812d, 0x39d: 0x8132,
	0x39e: 0x8132, 0x39f: 0x812d,
	0x3b0: 0x811e,
	// Block 0xf, offset 0x3c0
	0x3c5: 0xa000,
	0x3c6: 0x2d26, 0x3c7: 0xa000, 0x3c8: 0x2d2e, 0x3c9: 0xa000, 0x3ca: 0x2d36, 0x3cb: 0xa000,
	0x3cc: 0x2d3e, 0x3cd: 0xa000, 0x3ce: 0x2d46, 0x3d1: 0xa000,
	0x3d2: 0x2d4e,
	0x3f4: 0x8102, 0x3f5: 0x9900,
	0x3fa: 0xa000, 0x3fb: 0x2d56,
	0x3fc: 0xa000, 0x3fd: 0x2d5e, 0x3fe: 0xa000, 0x3ff: 0xa000,
	// Block 0x10, offset 0x400
	0x400: 0x8132, 0x401: 0x8132, 0x402: 0x812d, 0x403: 0x8132, 0x404: 0x8132, 0x405: 0x8132,
	0x406: 0x8132, 0x407: 0x8132, 0x408: 0x8132, 0x409: 0x8132, 0x40a: 0x812d, 0x40b: 0x8132,
	0x40c: 0x8132, 0x40d: 0x8135, 0x40e: 0x812a, 0x40f: 0x812d, 0x410: 0x8129, 0x411: 0x8132,
	0x412: 0x8132, 0x413: 0x8132, 0x414: 0x8132, 0x415: 0x8132, 0x416: 0x8132, 0x417: 0x8132,
	0x418: 0x8132, 0x419: 0x8132, 0x41a: 0x8132, 0x41b: 0x8132, 0x41c: 0x8132, 0x41d: 0x8132,
	0x41e: 0x8132, 0x41f: 0x8132, 0x420: 0x8132, 0x421: 0x8132, 0x422: 0x8132, 0x423: 0x8132,
	0x424: 0x8132, 0x425: 0x8132, 0x426: 0x8132, 0x427: 0x8132, 0x428: 0x8132, 0x429: 0x8132,
	0x42a: 0x8132, 0x42b: 0x8132, 0x42c: 0x8132, 0x42d: 0x8132, 0x42e: 0x8132, 0x42f: 0x8132,
	0x430: 0x8132, 0x431: 0x8132, 0x432: 0x8132, 0x433: 0x8132, 0x434: 0x8132, 0x435: 0x8132,
	0x436: 0x8133, 0x437: 0x8131, 0x438: 0x8131, 0x439: 0x812d, 0x43b: 0x8132,
	0x43c: 0x8134, 0x43d: 0x812d, 0x43e: 0x8132, 0x43f: 0x812d,
	// Block 0x11, offset 0x440
	0x440: 0x2f97, 0x441: 0x32a3, 0x442: 0x2fa1, 0x443: 0x32ad, 0x444: 0x2fa6, 0x445: 0x32b2,
	0x446: 0x2fab, 0x447: 0x32b7, 0x448: 0x38cc, 0x449: 0x3a5b, 0x44a: 0x2fc4, 0x44b: 0x32d0,
	0x44c: 0x2fce, 0x44d: 0x32da, 0x44e: 0x2fdd, 0x44f: 0x32e9, 0x450: 0x2fd3, 0x451: 0x32df,
	0x452: 0x2fd8, 0x453: 0x32e4, 0x454: 0x38ef, 0x455: 0x3a7e, 0x456: 0x38f6, 0x457: 0x3a85,
	0x458: 0x3019, 0x459: 0x3325, 0x45a: 0x301e, 0x45b: 0x332a, 0x45c: 0x3904, 0x45d: 0x3a93,
	0x45e: 0x3023, 0x45f: 0x332f, 0x460: 0x3032, 0x461: 0x333e, 0x462: 0x3050, 0x463: 0x335c,
	0x464: 0x305f, 0x465: 0x336b, 0x466: 0x3055, 0x467: 0x3361, 0x468: 0x3064, 0x469: 0x3370,
	0x46a: 0x3069, 0x46b: 0x3375, 0x46c: 0x30af, 0x46d: 0x33bb, 0x46e: 0x390b, 0x46f: 0x3a9a,
	0x470: 0x30b9, 0x471: 0x33ca, 0x472: 0x30c3, 0x473: 0x33d4, 0x474: 0x30cd, 0x475: 0x33de,
	0x476: 0x46c4, 0x477: 0x4755, 0x478: 0x3912, 0x479: 0x3aa1, 0x47a: 0x30e6, 0x47b: 0x33f7,
	0x47c: 0x30e1, 0x47d: 0x33f2, 0x47e: 0x30eb, 0x47f: 0x33fc,
	// Block 0x12, offset 0x480
	0x480: 0x30f0, 0x481: 0x3401, 0x482: 0x30f5, 0x483: 0x3406, 0x484: 0x3109, 0x485: 0x341a,
	0x486: 0x3113, 0x487: 0x3424, 0x488: 0x3122, 0x489: 0x3433, 0x48a: 0x311d, 0x48b: 0x342e,
	0x48c: 0x3935, 0x48d: 0x3ac4, 0x48e: 0x3943, 0x48f: 0x3ad2, 0x490: 0x394a, 0x491: 0x3ad9,
	0x492: 0x3951, 0x493: 0x3ae0, 0x494: 0x314f, 0x495: 0x3460, 0x496: 0x3154, 0x497: 0x3465,
	0x498: 0x315e, 0x499: 0x346f, 0x49a: 0x46f1, 0x49b: 0x4782, 0x49c: 0x3997, 0x49d: 0x3b26,
	0x49e: 0x3177, 0x49f: 0x3488, 0x4a0: 0x3181, 0x4a1: 0x3492, 0x4a2: 0x4700, 0x4a3: 0x4791,
	0x4a4: 0x399e, 0x4a5: 0x3b2d, 0x4a6: 0x39a5, 0x4a7: 0x3b34, 0x4a8: 0x39ac, 0x4a9: 0x3b3b,
	0x4aa: 0x3190, 0x4ab: 0x34a1, 0x4ac: 0x319a, 0x4ad: 0x34b0, 0x4ae: 0x31ae, 0x4af: 0x34c4,
	0x4b0: 0x31a9, 0x4b1: 0x34bf, 0x4b2: 0x31ea, 0x4b3: 0x3500, 0x4b4: 0x31f9, 0x4b5: 0x350f,
	0x4b6: 0x31f4, 0x4b7: 0x350a, 0x4b8: 0x39b3, 0x4b9: 0x3b42, 0x4ba: 0x39ba, 0x4bb: 0x3b49,
	0x4bc: 0x31fe, 0x4bd: 0x3514, 0x4be: 0x3203, 0x4bf: 0x3519,
	// Block 0x13, offset 0x4c0
	0x4c0: 0x3208, 0x4c1: 0x351e, 0x4c2: 0x320d, 0x4c3: 0x3523, 0x4c4: 0x321c, 0x4c5: 0x3532,
	0x4c6: 0x3217, 0x4c7: 0x352d, 0x4c8: 0x3221, 0x4c9: 0x353c, 0x4ca: 0x3226, 0x4cb: 0x3541,
	0x4cc: 0x322b, 0x4cd: 0x3546, 0x4ce: 0x3249, 0x4cf: 0x3564, 0x4d0: 0x3262, 0x4d1: 0x3582,
	0x4d2: 0x3271, 0x4d3: 0x3591, 0x4d4: 0x3276, 0x4d5: 0x3596, 0x4d6: 0x337a, 0x4d7: 0x34a6,
	0x4d8: 0x3537, 0x4d9: 0x3573, 0x4db: 0x35d1,
	0x4e0: 0x46a1, 0x4e1: 0x4732, 0x4e2: 0x2f83, 0x4e3: 0x328f,
	0x4e4: 0x3878, 0x4e5: 0x3a07, 0x4e6: 0x3871, 0x4e7: 0x3a00, 0x4e8: 0x3886, 0x4e9: 0x3a15,
	0x4ea: 0x387f, 0x4eb: 0x3a0e, 0x4ec: 0x38be, 0x4ed: 0x3a4d, 0x4ee: 0x3894, 0x4ef: 0x3a23,
	0x4f0: 0x388d, 0x4f1: 0x3a1c, 0x4f2: 0x38a2, 0x4f3: 0x3a31, 0x4f4: 0x389b, 0x4f5: 0x3a2a,
	0x4f6: 0x38c5, 0x4f7: 0x3a54, 0x4f8: 0x46b5, 0x4f9: 0x4746, 0x4fa: 0x3000, 0x4fb: 0x330c,
	0x4fc: 0x2fec, 0x4fd: 0x32f8, 0x4fe: 0x38da, 0x4ff: 0x3a69,
	// Block 0x14, offset 0x500
	0x500: 0x38d3, 0x501: 0x3a62, 0x502: 0x38e8, 0x503: 0x3a77, 0x504: 0x38e1, 0x505: 0x3a70,
	0x506: 0x38fd, 0x507: 0x3a8c, 0x508: 0x3091, 0x509: 0x339d, 0x50a: 0x30a5, 0x50b: 0x33b1,
	0x50c: 0x46e7, 0x50d: 0x4778, 0x50e: 0x3136, 0x50f: 0x3447, 0x510: 0x3920, 0x511: 0x3aaf,
	0x512: 0x3919, 0x513: 0x3aa8, 0x514: 0x392e, 0x515: 0x3abd, 0x516: 0x3927, 0x517: 0x3ab6,
	0x518: 0x3989, 0x519: 0x3b18, 0x51a: 0x396d, 0x51b: 0x3afc, 0x51c: 0x3966, 0x51d: 0x3af5,
	0x51e: 0x397b, 0x51f: 0x3b0a, 0x520: 0x3974, 0x521: 0x3b03, 0x522: 0x3982, 0x523: 0x3b11,
	0x524: 0x31e5, 0x525: 0x34fb, 0x526: 0x31c7, 0x527: 0x34dd, 0x528: 0x39e4, 0x529: 0x3b73,
	0x52a: 0x39dd, 0x52b: 0x3b6c, 0x52c: 0x39f2, 0x52d: 0x3b81, 0x52e: 0x39eb, 0x52f: 0x3b7a,
	0x530: 0x39f9, 0x531: 0x3b88, 0x532: 0x3230, 0x533: 0x354b, 0x534: 0x3258, 0x535: 0x3578,
	0x536: 0x3253, 0x537: 0x356e, 0x538: 0x323f, 0x539: 0x355a,
	// Block 0x15, offset 0x540
	0x540: 0x4804, 0x541: 0x480a, 0x542: 0x491e, 0x543: 0x4936, 0x544: 0x4926, 0x545: 0x493e,
	0x546: 0x492e, 0x547: 0x4946, 0x548: 0x47aa, 0x549: 0x47b0, 0x54a: 0x488e, 0x54b: 0x48a6,
	0x54c: 0x4896, 0x54d: 0x48ae, 0x54e: 0x489e, 0x54f: 0x48b6, 0x550: 0x4816, 0x551: 0x481c,
	0x552: 0x3db8, 0x553: 0x3dc8, 0x554: 0x3dc0, 0x555: 0x3dd0,
	0x558: 0x47b6, 0x559: 0x47bc, 0x55a: 0x3ce8, 0x55b: 0x3cf8, 0x55c: 0x3cf0, 0x55d: 0x3d00,
	0x560: 0x482e, 0x561: 0x4834, 0x562: 0x494e, 0x563: 0x4966,
	0x564: 0x4956, 0x565: 0x496e, 0x566: 0x495e, 0x567: 0x4976, 0x568: 0x47c2, 0x569: 0x47c8,
	0x56a: 0x48be, 0x56b: 0x48d6, 0x56c: 0x48c6, 0x56d: 0x48de, 0x56e: 0x48ce, 0x56f: 0x48e6,
	0x570: 0x4846, 0x571: 0x484c, 0x572: 0x3e18, 0x573: 0x3e30, 0x574: 0x3e20, 0x575: 0x3e38,
	0x576: 0x3e28, 0x577: 0x3e40, 0x578: 0x47ce, 0x579: 0x47d4, 0x57a: 0x3d18, 0x57b: 0x3d30,
	0x57c: 0x3d20, 0x57d: 0x3d38, 0x57e: 0x3d28, 0x57f: 0x3d40,
	// Block 0x16, offset 0x580
	0x580: 0x4852, 0x581: 0x4858, 0x582: 0x3e48, 0x583: 0x3e58, 0x584: 0x3e50, 0x585: 0x3e60,
	0x588: 0x47da, 0x589: 0x47e0, 0x58a: 0x3d48, 0x58b: 0x3d58,
	0x58c: 0x3d50, 0x58d: 0x3d60, 0x590: 0x4864, 0x591: 0x486a,
	0x592: 0x3e80, 0x593: 0x3e98, 0x594: 0x3e88, 0x595: 0x3ea0, 0x596: 0x3e90, 0x597: 0x3ea8,
	0x599: 0x47e6, 0x59b: 0x3d68, 0x59d: 0x3d70,
	0x59f: 0x3d78, 0x5a0: 0x487c, 0x5a1: 0x4882, 0x5a2: 0x497e, 0x5a3: 0x4996,
	0x5a4: 0x4986, 0x5a5: 0x499e, 0x5a6: 0x498e, 0x5a7: 0x49a6, 0x5a8: 0x47ec, 0x5a9: 0x47f2,
	0x5aa: 0x48ee, 0x5ab: 0x4906, 0x5ac: 0x48f6, 0x5ad: 0x490e, 0x5ae: 0x48fe, 0x5af: 0x4916,
	0x5b0: 0x47f8, 0x5b1: 0x431e, 0x5b2: 0x3691, 0x5b3: 0x4324, 0x5b4: 0x4822, 0x5b5: 0x432a,
	0x5b6: 0x36a3, 0x5b7: 0x4330, 0x5b8: 0x36c1, 0x5b9: 0x4336, 0x5ba: 0x36d9, 0x5bb: 0x433c,
	0x5bc: 0x4870, 0x5bd: 0x4342,
	// Block 0x17, offset 0x5c0
	0x5c0: 0x3da0, 0x5c1: 0x3da8, 0x5c2: 0x4184, 0x5c3: 0x41a2, 0x5c4: 0x418e, 0x5c5: 0x41ac,
	0x5c6: 0x4198, 0x5c7: 0x41b6, 0x5c8: 0x3cd8, 0x5c9: 0x3ce0, 0x5ca: 0x40d0, 0x5cb: 0x40ee,
	0x5cc: 0x40da, 0x5cd: 0x40f8, 0x5ce: 0x40e4, 0x5cf: 0x4102, 0x5d0: 0x3de8, 0x5d1: 0x3df0,
	0x5d2: 0x41c0, 0x5d3: 0x41de, 0x5d4: 0x41ca, 0x5d5: 0x41e8, 0x5d6: 0x41d4, 0x5d7: 0x41f2,
	0x5d8: 0x3d08, 0x5d9: 0x3d10, 0x5da: 0x410c, 0x5db: 0x412a, 0x5dc: 0x4116, 0x5dd: 0x4134,
	0x5de: 0x4120, 0x5df: 0x413e, 0x5e0: 0x3ec0, 0x5e1: 0x3ec8, 0x5e2: 0x41fc, 0x5e3: 0x421a,
	0x5e4: 0x4206, 0x5e5: 0x4224, 0x5e6: 0x4210, 0x5e7: 0x422e, 0x5e8: 0x3d80, 0x5e9: 0x3d88,
	0x5ea: 0x4148, 0x5eb: 0x4166, 0x5ec: 0x4152, 0x5ed: 0x4170, 0x5ee: 0x415c, 0x5ef: 0x417a,
	0x5f0: 0x3685, 0x5f1: 0x367f, 0x5f2: 0x3d90, 0x5f3: 0x368b, 0x5f4: 0x3d98,
	0x5f6: 0x4810, 0x5f7: 0x3db0, 0x5f8: 0x35f5, 0x5f9: 0x35ef, 0x5fa: 0x35e3, 0x5fb: 0x42ee,
	0x5fc: 0x35fb, 0x5fd: 0x8100, 0x5fe: 0x01d3, 0x5ff: 0xa100,
	// Block 0x18, offset 0x600
	0x600: 0x8100, 0x601: 0x35a7, 0x602: 0x3dd8, 0x603: 0x369d, 0x604: 0x3de0,
	0x606: 0x483a, 0x607: 0x3df8, 0x608: 0x3601, 0x609: 0x42f4, 0x60a: 0x360d, 0x60b: 0x42fa,
	0x60c: 0x3619, 0x60d: 0x3b8f, 0x60e: 0x3b96, 0x60f: 0x3b9d, 0x610: 0x36b5, 0x611: 0x36af,
	0x612: 0x3e00, 0x613: 0x44e4, 0x616: 0x36bb, 0x617: 0x3e10,
	0x618: 0x3631, 0x619: 0x362b, 0x61a: 0x361f, 0x61b: 0x4300, 0x61d: 0x3ba4,
	0x61e: 0x3bab, 0x61f: 0x3bb2, 0x620: 0x36eb, 0x621: 0x36e5, 0x622: 0x3e68, 0x623: 0x44ec,
	0x624: 0x36cd, 0x625: 0x36d3, 0x626: 0x36f1, 0x627: 0x3e78, 0x628: 0x3661, 0x629: 0x365b,
	0x62a: 0x364f, 0x62b: 0x430c, 0x62c: 0x3649, 0x62d: 0x359b, 0x62e: 0x42e8, 0x62f: 0x0081,
	0x632: 0x3eb0, 0x633: 0x36f7, 0x634: 0x3eb8,
	0x636: 0x4888, 0x637: 0x3ed0, 0x638: 0x363d, 0x639: 0x4306, 0x63a: 0x366d, 0x63b: 0x4318,
	0x63c: 0x3679, 0x63d: 0x4256, 0x63e: 0xa100,
	// Block 0x19, offset 0x640
	0x641: 0x3c06, 0x643: 0xa000, 0x644: 0x3c0d, 0x645: 0xa000,
	0x647: 0x3c14, 0x648: 0xa000, 0x649: 0x3c1b,
	0x64d: 0xa000,
	0x660: 0x2f65, 0x661: 0xa000, 0x662: 0x3c29,
	0x664: 0xa000, 0x665: 0xa000,
	0x66d: 0x3c22, 0x66e: 0x2f60, 0x66f: 0x2f6a,
	0x670: 0x3c30, 0x671: 0x3c37, 0x672: 0xa000, 0x673: 0xa000, 0x674: 0x3c3e, 0x675: 0x3c45,
	0x676: 0xa000, 0x677: 0xa000, 0x678: 0x3c4c, 0x679: 0x3c53, 0x67a: 0xa000, 0x67b: 0xa000,
	0x67c: 0xa000, 0x67d: 0xa000,
	// Block 0x1a, offset 0x680
	0x680: 0x3c5a, 0x681: 0x3c61, 0x682: 0xa000, 0x683: 0xa000, 0x684: 0x3c76, 0x685: 0x3c7d,
	0x686: 0xa000, 0x687: 0xa000, 0x688: 0x3c84, 0x689: 0x3c8b,
	0x691: 0xa000,
	0x692: 0xa000,
	0x6a2: 0xa000,
	0x6a8: 0xa000, 0x6a9: 0xa000,
	0x6ab: 0xa000, 0x6ac: 0x3ca0, 0x6ad: 0x3ca7, 0x6ae: 0x3cae, 0x6af: 0x3cb5,
	0x6b2: 0xa000, 0x6b3: 0xa000, 0x6b4: 0xa000, 0x6b5: 0xa000,
	// Block 0x1b, offset 0x6c0
	0x6c6: 0xa000, 0x6cb: 0xa000,
	0x6cc: 0x3f08, 0x6cd: 0xa000, 0x6ce: 0x3f10, 0x6cf: 0xa000, 0x6d0: 0x3f18, 0x6d1: 0xa000,
	0x6d2: 0x3f20, 0x6d3: 0xa000, 0x6d4: 0x3f28, 0x6d5: 0xa000, 0x6d6: 0x3f30, 0x6d7: 0xa000,
	0x6d8: 0x3f38, 0x6d9: 0xa000, 0x6da: 0x3f40, 0x6db: 0xa000, 0x6dc: 0x3f48, 0x6dd: 0xa000,
	0x6de: 0x3f50, 0x6df: 0xa000, 0x6e0: 0x3f58, 0x6e1: 0xa000, 0x6e2: 0x3f60,
	0x6e4: 0xa000, 0x6e5: 0x3f68, 0x6e6: 0xa000, 0x6e7: 0x3f70, 0x6e8: 0xa000, 0x6e9: 0x3f78,
	0x6ef: 0xa000,
	0x6f0: 0x3f80, 0x6f1: 0x3f88, 0x6f2: 0xa000, 0x6f3: 0x3f90, 0x6f4: 0x3f98, 0x6f5: 0xa000,
	0x6f6: 0x3fa0, 0x6f7: 0x3fa8, 0x6f8: 0xa000, 0x6f9: 0x3fb0, 0x6fa: 0x3fb8, 0x6fb: 0xa000,
	0x6fc: 0x3fc0, 0x6fd: 0x3fc8,
	// Block 0x1c, offset 0x700
	0x714: 0x3f00,
	0x719: 0x9903, 0x71a: 0x9903, 0x71b: 0x8100, 0x71c: 0x8100, 0x71d: 0xa000,
	0x71e: 0x3fd0,
	0x726: 0xa000,
	0x72b: 0xa000, 0x72c: 0x3fe0, 0x72d: 0xa000, 0x72e: 0x3fe8, 0x72f: 0xa000,
	0x730: 0x3ff0, 0x731: 0xa000, 0x732: 0x3ff8, 0x733: 0xa000, 0x734: 0x4000, 0x735: 0xa000,
	0x736: 0x4008, 0x737: 0xa000, 0x738: 0x4010, 0x739: 0xa000, 0x73a: 0x4018, 0x73b: 0xa000,
	0x73c: 0x4020, 0x73d: 0xa000, 0x73e: 0x4028, 0x73f: 0xa000,
	// Block 0x1d, offset 0x740
	0x740: 0x4030, 0x741: 0xa000, 0x742: 0x4038, 0x744: 0xa000, 0x745: 0x4040,
	0x746: 0xa000, 0x747: 0x4048, 0x748: 0xa000, 0x749: 0x4050,
	0x74f: 0xa000, 0x750: 0x4058, 0x751: 0x4060,
	0x752: 0xa000, 0x753: 0x4068, 0x754: 0x4070, 0x755: 0xa000, 0x756: 0x4078, 0x757: 0x4080,
	0x758: 0xa000, 0x759: 0x4088, 0x75a: 0x4090, 0x75b: 0xa000, 0x75c: 0x4098, 0x75d: 0x40a0,
	0x76f: 0xa000,
	0x770: 0xa000, 0x771: 0xa000, 0x772: 0xa000, 0x774: 0x3fd8,
	0x777: 0x40a8, 0x778: 0x40b0, 0x779: 0x40b8, 0x77a: 0x40c0,
	0x77d: 0xa000, 0x77e: 0x40c8,
	// Block 0x1e, offset 0x780
	0x780: 0x1377, 0x781: 0x0cfb, 0x782: 0x13d3, 0x783: 0x139f, 0x784: 0x0e57, 0x785: 0x06eb,
	0x786: 0x08df, 0x787: 0x162b, 0x788: 0x162b, 0x789: 0x0a0b, 0x78a: 0x145f, 0x78b: 0x0943,
	0x78c: 0x0a07, 0x78d: 0x0bef, 0x78e: 0x0fcf, 0x78f: 0x115f, 0x790: 0x1297, 0x791: 0x12d3,
	0x792: 0x1307, 0x793: 0x141b, 0x794: 0x0d73, 0x795: 0x0dff, 0x796: 0x0eab, 0x797: 0x0f43,
	0x798: 0x125f, 0x799: 0x1447, 0x79a: 0x1573, 0x79b: 0x070f, 0x79c: 0x08b3, 0x79d: 0x0d87,
	0x79e: 0x0ecf, 0x79f: 0x1293, 0x7a0: 0x15c3, 0x7a1: 0x0ab3, 0x7a2: 0x0e77, 0x7a3: 0x1283,
	0x7a4: 0x1317, 0x7a5: 0x0c23, 0x7a6: 0x11bb, 0x7a7: 0x12df, 0x7a8: 0x0b1f, 0x7a9: 0x0d0f,
	0x7aa: 0x0e17, 0x7ab: 0x0f1b, 0x7ac: 0x1427, 0x7ad: 0x074f, 0x7ae: 0x07e7, 0x7af: 0x0853,
	0x7b0: 0x0c8b, 0x7b1: 0x0d7f, 0x7b2: 0x0ecb, 0x7b3: 0x0fef, 0x7b4: 0x1177, 0x7b5: 0x128b,
	0x7b6: 0x12a3, 0x7b7: 0x13c7, 0x7b8: 0x14ef, 0x7b9: 0x15a3, 0x7ba: 0x15bf, 0x7bb: 0x102b,
	0x7bc: 0x106b, 0x7bd: 0x1123, 0x7be: 0x1243, 0x7bf: 0x147b,
	// Block 0x1f, offset 0x7c0
	0x7c0: 0x15cb, 0x7c1: 0x134b, 0x7c2: 0x09c7, 0x7c3: 0x0b3b, 0x7c4: 0x10db, 0x7c5: 0x119b,
	0x7c6: 0x0eff, 0x7c7: 0x1033, 0x7c8: 0x1397, 0x7c9: 0x14e7, 0x7ca: 0x09c3, 0x7cb: 0x0a8f,
	0x7cc: 0x0d77, 0x7cd: 0x0e2b, 0x7ce: 0x0e5f, 0x7cf: 0x1113, 0x7d0: 0x113b, 0x7d1: 0x14a7,
	0x7d2: 0x084f, 0x7d3: 0x11a7, 0x7d4: 0x07f3, 0x7d5: 0x07ef, 0x7d6: 0x1097, 0x7d7: 0x1127,
	0x7d8: 0x125b, 0x7d9: 0x14af, 0x7da: 0x1367, 0x7db: 0x0c27, 0x7dc: 0x0d73, 0x7dd: 0x1357,
	0x7de: 0x06f7, 0x7df: 0x0a63, 0x7e0: 0x0b93, 0x7e1: 0x0f2f, 0x7e2: 0x0faf, 0x7e3: 0x0873,
	0x7e4: 0x103b, 0x7e5: 0x075f, 0x7e6: 0x0b77, 0x7e7: 0x06d7, 0x7e8: 0x0deb, 0x7e9: 0x0ca3,
	0x7ea: 0x110f, 0x7eb: 0x08c7, 0x7ec: 0x09b3, 0x7ed: 0x0ffb, 0x7ee: 0x1263, 0x7ef: 0x133b,
	0x7f0: 0x0db7, 0x7f1: 0x13f7, 0x7f2: 0x0de3, 0x7f3: 0x0c37, 0x7f4: 0x121b, 0x7f5: 0x0c57,
	0x7f6: 0x0fab, 0x7f7: 0x072b, 0x7f8: 0x07a7, 0x7f9: 0x07eb, 0x7fa: 0x0d53, 0x7fb: 0x10fb,
	0x7fc: 0x11f3, 0x7fd: 0x1347, 0x7fe: 0x145b, 0x7ff: 0x085b,
	// Block 0x20, offset 0x800
	0x800: 0x090f, 0x801: 0x0a17, 0x802: 0x0b2f, 0x803: 0x0cbf, 0x804: 0x0e7b, 0x805: 0x103f,
	0x806: 0x1497, 0x807: 0x157b, 0x808: 0x15cf, 0x809: 0x15e7, 0x80a: 0x0837, 0x80b: 0x0cf3,
	0x80c: 0x0da3, 0x80d: 0x13eb, 0x80e: 0x0afb, 0x80f: 0x0bd7, 0x810: 0x0bf3, 0x811: 0x0c83,
	0x812: 0x0e6b, 0x813: 0x0eb7, 0x814: 0x0f67, 0x815: 0x108b, 0x816: 0x112f, 0x817: 0x1193,
	0x818: 0x13db, 0x819: 0x126b, 0x81a: 0x1403, 0x81b: 0x147f, 0x81c: 0x080f, 0x81d: 0x083b,
	0x81e: 0x0923, 0x81f: 0x0ea7, 0x820: 0x12f3, 0x821: 0x133b, 0x822: 0x0b1b, 0x823: 0x0b8b,
	0x824: 0x0c4f, 0x825: 0x0daf, 0x826: 0x10d7, 0x827: 0x0f23, 0x828: 0x073b, 0x829: 0x097f,
	0x82a: 0x0a63, 0x82b: 0x0ac7, 0x82c: 0x0b97, 0x82d: 0x0f3f, 0x82e: 0x0f5b, 0x82f: 0x116b,
	0x830: 0x118b, 0x831: 0x1463, 0x832: 0x14e3, 0x833: 0x14f3, 0x834: 0x152f, 0x835: 0x0753,
	0x836: 0x107f, 0x837: 0x144f, 0x838: 0x14cb, 0x839: 0x0baf, 0x83a: 0x0717, 0x83b: 0x0777,
	0x83c: 0x0a67, 0x83d: 0x0a87, 0x83e: 0x0caf, 0x83f: 0x0d73,
	// Block 0x21, offset 0x840
	0x840: 0x0ec3, 0x841: 0x0fcb, 0x842: 0x1277, 0x843: 0x1417, 0x844: 0x1623, 0x845: 0x0ce3,
	0x846: 0x14a3, 0x847: 0x0833, 0x848: 0x0d2f, 0x849: 0x0d3b, 0x84a: 0x0e0f, 0x84b: 0x0e47,
	0x84c: 0x0f4b, 0x84d: 0x0fa7, 0x84e: 0x1027, 0x84f: 0x110b, 0x850: 0x153b, 0x851: 0x07af,
	0x852: 0x0c03, 0x853: 0x14b3, 0x854: 0x0767, 0x855: 0x0aab, 0x856: 0x0e2f, 0x857: 0x13df,
	0x858: 0x0b67, 0x859: 0x0bb7, 0x85a: 0x0d43, 0x85b: 0x0f2f, 0x85c: 0x14bb, 0x85d: 0x0817,
	0x85e: 0x08ff, 0x85f: 0x0a97, 0x860: 0x0cd3, 0x861: 0x0d1f, 0x862: 0x0d5f, 0x863: 0x0df3,
	0x864: 0x0f47, 0x865: 0x0fbb, 0x866: 0x1157, 0x867: 0x12f7, 0x868: 0x1303, 0x869: 0x1457,
	0x86a: 0x14d7, 0x86b: 0x0883, 0x86c: 0x0e4b, 0x86d: 0x0903, 0x86e: 0x0ec7, 0x86f: 0x0f6b,
	0x870: 0x1287, 0x871: 0x14bf, 0x872: 0x15ab, 0x873: 0x15d3, 0x874: 0x0d37, 0x875: 0x0e27,
	0x876: 0x11c3, 0x877: 0x10b7, 0x878: 0x10c3, 0x879: 0x10e7, 0x87a: 0x0f17, 0x87b: 0x0e9f,
	0x87c: 0x1363, 0x87d: 0x0733, 0x87e: 0x122b, 0x87f: 0x081b,
	// Block 0x22, offset 0x880
	0x880: 0x080b, 0x881: 0x0b0b, 0x882: 0x0c2b, 0x883: 0x10f3, 0x884: 0x0a53, 0x885: 0x0e03,
	0x886: 0x0cef, 0x887: 0x13e7, 0x888: 0x12e7, 0x889: 0x14ab, 0x88a: 0x1323, 0x88b: 0x0b27,
	0x88c: 0x0787, 0x88d: 0x095b, 0x890: 0x09af,
	0x892: 0x0cdf, 0x895: 0x07f7, 0x896: 0x0f1f, 0x897: 0x0fe3,
	0x898: 0x1047, 0x899: 0x1063, 0x89a: 0x1067, 0x89b: 0x107b, 0x89c: 0x14fb, 0x89d: 0x10eb,
	0x89e: 0x116f, 0x8a0: 0x128f, 0x8a2: 0x1353,
	0x8a5: 0x1407, 0x8a6: 0x1433,
	0x8aa: 0x154f, 0x8ab: 0x1553, 0x8ac: 0x1557, 0x8ad: 0x15bb, 0x8ae: 0x142b, 0x8af: 0x14c7,
	0x8b0: 0x0757, 0x8b1: 0x077b, 0x8b2: 0x078f, 0x8b3: 0x084b, 0x8b4: 0x0857, 0x8b5: 0x0897,
	0x8b6: 0x094b, 0x8b7: 0x0967, 0x8b8: 0x096f, 0x8b9: 0x09ab, 0x8ba: 0x09b7, 0x8bb: 0x0a93,
	0x8bc: 0x0a9b, 0x8bd: 0x0ba3, 0x8be: 0x0bcb, 0x8bf: 0x0bd3,
	// Block 0x23, offset 0x8c0
	0x8c0: 0x0beb, 0x8c1: 0x0c97, 0x8c2: 0x0cc7, 0x8c3: 0x0ce7, 0x8c4: 0x0d57, 0x8c5: 0x0e1b,
	0x8c6: 0x0e37, 0x8c7: 0x0e67, 0x8c8: 0x0ebb, 0x8c9: 0x0edb, 0x8ca: 0x0f4f, 0x8cb: 0x102f,
	0x8cc: 0x104b, 0x8cd: 0x1053, 0x8ce: 0x104f, 0x8cf: 0x1057, 0x8d0: 0x105b, 0x8d1: 0x105f,
	0x8d2: 0x1073, 0x8d3: 0x1077, 0x8d4: 0x109b, 0x8d5: 0x10af, 0x8d6: 0x10cb, 0x8d7: 0x112f,
	0x8d8: 0x1137, 0x8d9: 0x113f, 0x8da: 0x1153, 0x8db: 0x117b, 0x8dc: 0x11cb, 0x8dd: 0x11ff,
	0x8de: 0x11ff, 0x8df: 0x1267, 0x8e0: 0x130f, 0x8e1: 0x1327, 0x8e2: 0x135b, 0x8e3: 0x135f,
	0x8e4: 0x13a3, 0x8e5: 0x13a7, 0x8e6: 0x13ff, 0x8e7: 0x1407, 0x8e8: 0x14db, 0x8e9: 0x151f,
	0x8ea: 0x1537, 0x8eb: 0x0b9b, 0x8ec: 0x171e, 0x8ed: 0x11e3,
	0x8f0: 0x06df, 0x8f1: 0x07e3, 0x8f2: 0x07a3, 0x8f3: 0x074b, 0x8f4: 0x078b, 0x8f5: 0x07b7,
	0x8f6: 0x0847, 0x8f7: 0x0863, 0x8f8: 0x094b, 0x8f9: 0x0937, 0x8fa: 0x0947, 0x8fb: 0x0963,
	0x8fc: 0x09af, 0x8fd: 0x09bf, 0x8fe: 0x0a03, 0x8ff: 0x0a0f,
	// Block 0x24, offset 0x900
	0x900: 0x0a2b, 0x901: 0x0a3b, 0x902: 0x0b23, 0x903: 0x0b2b, 0x904: 0x0b5b, 0x905: 0x0b7b,
	0x906: 0x0bab, 0x907: 0x0bc3, 0x908: 0x0bb3, 0x909: 0x0bd3, 0x90a: 0x0bc7, 0x90b: 0x0beb,
	0x90c: 0x0c07, 0x90d: 0x0c5f, 0x90e: 0x0c6b, 0x90f: 0x0c73, 0x910: 0x0c9b, 0x911: 0x0cdf,
	0x912: 0x0d0f, 0x913: 0x0d13, 0x914: 0x0d27, 0x915: 0x0da7, 0x916: 0x0db7, 0x917: 0x0e0f,
	0x918: 0x0e5b, 0x919: 0x0e53, 0x91a: 0x0e67, 0x91b: 0x0e83, 0x91c: 0x0ebb, 0x91d: 0x1013,
	0x91e: 0x0edf, 0x91f: 0x0f13, 0x920: 0x0f1f, 0x921: 0x0f5f, 0x922: 0x0f7b, 0x923: 0x0f9f,
	0x924: 0x0fc3, 0x925: 0x0fc7, 0x926: 0x0fe3, 0x927: 0x0fe7, 0x928: 0x0ff7, 0x929: 0x100b,
	0x92a: 0x1007, 0x92b: 0x1037, 0x92c: 0x10b3, 0x92d: 0x10cb, 0x92e: 0x10e3, 0x92f: 0x111b,
	0x930: 0x112f, 0x931: 0x114b, 0x932: 0x117b, 0x933: 0x122f, 0x934: 0x1257, 0x935: 0x12cb,
	0x936: 0x1313, 0x937: 0x131f, 0x938: 0x1327, 0x939: 0x133f, 0x93a: 0x1353, 0x93b: 0x1343,
	0x93c: 0x135b, 0x93d: 0x1357, 0x93e: 0x134f, 0x93f: 0x135f,
	// Block 0x25, offset 0x940
	0x940: 0x136b, 0x941: 0x13a7, 0x942: 0x13e3, 0x943: 0x1413, 0x944: 0x144b, 0x945: 0x146b,
	0x946: 0x14b7, 0x947: 0x14db, 0x948: 0x14fb, 0x949: 0x150f, 0x94a: 0x151f, 0x94b: 0x152b,
	0x94c: 0x1537, 0x94d: 0x158b, 0x94e: 0x162b, 0x94f: 0x16b5, 0x950: 0x16b0, 0x951: 0x16e2,
	0x952: 0x0607, 0x953: 0x062f, 0x954: 0x0633, 0x955: 0x1764, 0x956: 0x1791, 0x957: 0x1809,
	0x958: 0x1617, 0x959: 0x1627,
	// Block 0x26, offset 0x980
	0x980: 0x06fb, 0x981: 0x06f3, 0x982: 0x0703, 0x983: 0x1647, 0x984: 0x0747, 0x985: 0x0757,
	0x986: 0x075b, 0x987: 0x0763, 0x988: 0x076b, 0x989: 0x076f, 0x98a: 0x077b, 0x98b: 0x0773,
	0x98c: 0x05b3, 0x98d: 0x165b, 0x98e: 0x078f, 0x98f: 0x0793, 0x990: 0x0797, 0x991: 0x07b3,
	0x992: 0x164c, 0x993: 0x05b7, 0x994: 0x079f, 0x995: 0x07bf, 0x996: 0x1656, 0x997: 0x07cf,
	0x998: 0x07d7, 0x999: 0x0737, 0x99a: 0x07df, 0x99b: 0x07e3, 0x99c: 0x1831, 0x99d: 0x07ff,
	0x99e: 0x0807, 0x99f: 0x05bf, 0x9a0: 0x081f, 0x9a1: 0x0823, 0x9a2: 0x082b, 0x9a3: 0x082f,
	0x9a4: 0x05c3, 0x9a5: 0x0847, 0x9a6: 0x084b, 0x9a7: 0x0857, 0x9a8: 0x0863, 0x9a9: 0x0867,
	0x9aa: 0x086b, 0x9ab: 0x0873, 0x9ac: 0x0893, 0x9ad: 0x0897, 0x9ae: 0x089f, 0x9af: 0x08af,
	0x9b0: 0x08b7, 0x9b1: 0x08bb, 0x9b2: 0x08bb, 0x9b3: 0x08bb, 0x9b4: 0x166a, 0x9b5: 0x0e93,
	0x9b6: 0x08cf, 0x9b7: 0x08d7, 0x9b8: 0x166f, 0x9b9: 0x08e3, 0x9ba: 0x08eb, 0x9bb: 0x08f3,
	0x9bc: 0x091b, 0x9bd: 0x0907, 0x9be: 0x0913, 0x9bf: 0x0917,
	// Block 0x27, offset 0x9c0
	0x9c0: 0x091f, 0x9c1: 0x0927, 0x9c2: 0x092b, 0x9c3: 0x0933, 0x9c4: 0x093b, 0x9c5: 0x093f,
	0x9c6: 0x093f, 0x9c7: 0x0947, 0x9c8: 0x094f, 0x9c9: 0x0953, 0x9ca: 0x095f, 0x9cb: 0x0983,
	0x9cc: 0x0967, 0x9cd: 0x0987, 0x9ce: 0x096b, 0x9cf: 0x0973, 0x9d0: 0x080b, 0x9d1: 0x09cf,
	0x9d2: 0x0997, 0x9d3: 0x099b, 0x9d4: 0x099f, 0x9d5: 0x0993, 0x9d6: 0x09a7, 0x9d7: 0x09a3,
	0x9d8: 0x09bb, 0x9d9: 0x1674, 0x9da: 0x09d7, 0x9db: 0x09db, 0x9dc: 0x09e3, 0x9dd: 0x09ef,
	0x9de: 0x09f7, 0x9df: 0x0a13, 0x9e0: 0x1679, 0x9e1: 0x167e, 0x9e2: 0x0a1f, 0x9e3: 0x0a23,
	0x9e4: 0x0a27, 0x9e5: 0x0a1b, 0x9e6: 0x0a2f, 0x9e7: 0x05c7, 0x9e8: 0x05cb, 0x9e9: 0x0a37,
	0x9ea: 0x0a3f, 0x9eb: 0x0a3f, 0x9ec: 0x1683, 0x9ed: 0x0a5b, 0x9ee: 0x0a5f, 0x9ef: 0x0a63,
	0x9f0: 0x0a6b, 0x9f1: 0x1688, 0x9f2: 0x0a73, 0x9f3: 0x0a77, 0x9f4: 0x0b4f, 0x9f5: 0x0a7f,
	0x9f6: 0x05cf, 0x9f7: 0x0a8b, 0x9f8: 0x0a9b, 0x9f9: 0x0aa7, 0x9fa: 0x0aa3, 0x9fb: 0x1692,
	0x9fc: 0x0aaf, 0x9fd: 0x1697, 0x9fe: 0x0abb, 0x9ff: 0x0ab7,
	// Block 0x28, offset 0xa00
	0xa00: 0x0abf, 0xa01: 0x0acf, 0xa02: 0x0ad3, 0xa03: 0x05d3, 0xa04: 0x0ae3, 0xa05: 0x0aeb,
	0xa06: 0x0aef, 0xa07: 0x0af3, 0xa08: 0x05d7, 0xa09: 0x169c, 0xa0a: 0x05db, 0xa0b: 0x0b0f,
	0xa0c: 0x0b13, 0xa0d: 0x0b17, 0xa0e: 0x0b1f, 0xa0f: 0x1863, 0xa10: 0x0b37, 0xa11: 0x16a6,
	0xa12: 0x16a6, 0xa13: 0x11d7, 0xa14: 0x0b47, 0xa15: 0x0b47, 0xa16: 0x05df, 0xa17: 0x16c9,
	0xa18: 0x179b, 0xa19: 0x0b57, 0xa1a: 0x0b5f, 0xa1b: 0x05e3, 0xa1c: 0x0b73, 0xa1d: 0x0b83,
	0xa1e: 0x0b87, 0xa1f: 0x0b8f, 0xa20: 0x0b9f, 0xa21: 0x05eb, 0xa22: 0x05e7, 0xa23: 0x0ba3,
	0xa24: 0x16ab, 0xa25: 0x0ba7, 0xa26: 0x0bbb, 0xa27: 0x0bbf, 0xa28: 0x0bc3, 0xa29: 0x0bbf,
	0xa2a: 0x0bcf, 0xa2b: 0x0bd3, 0xa2c: 0x0be3, 0xa2d: 0x0bdb, 0xa2e: 0x0bdf, 0xa2f: 0x0be7,
	0xa30: 0x0beb, 0xa31: 0x0bef, 0xa32: 0x0bfb, 0xa33: 0x0bff, 0xa34: 0x0c17, 0xa35: 0x0c1f,
	0xa36: 0x0c2f, 0xa37: 0x0c43, 0xa38: 0x16ba, 0xa39: 0x0c3f, 0xa3a: 0x0c33, 0xa3b: 0x0c4b,
	0xa3c: 0x0c53, 0xa3d: 0x0c67, 0xa3e: 0x16bf, 0xa3f: 0x0c6f,
	// Block 0x29, offset 0xa40
	0xa40: 0x0c63, 0xa41: 0x0c5b, 0xa42: 0x05ef, 0xa43: 0x0c77, 0xa44: 0x0c7f, 0xa45: 0x0c87,
	0xa46: 0x0c7b, 0xa47: 0x05f3, 0xa48: 0x0c97, 0xa49: 0x0c9f, 0xa4a: 0x16c4, 0xa4b: 0x0ccb,
	0xa4c: 0x0cff, 0xa4d: 0x0cdb, 0xa4e: 0x05ff, 0xa4f: 0x0ce7, 0xa50: 0x05fb, 0xa51: 0x05f7,
	0xa52: 0x07c3, 0xa53: 0x07c7, 0xa54: 0x0d03, 0xa55: 0x0ceb, 0xa56: 0x11ab, 0xa57: 0x0663,
	0xa58: 0x0d0f, 0xa59: 0x0d13, 0xa5a: 0x0d17, 0xa5b: 0x0d2b, 0xa5c: 0x0d23, 0xa5d: 0x16dd,
	0xa5e: 0x0603, 0xa5f: 0x0d3f, 0xa60: 0x0d33, 0xa61: 0x0d4f, 0xa62: 0x0d57, 0xa63: 0x16e7,
	0xa64: 0x0d5b, 0xa65: 0x0d47, 0xa66: 0x0d63, 0xa67: 0x0607, 0xa68: 0x0d67, 0xa69: 0x0d6b,
	0xa6a: 0x0d6f, 0xa6b: 0x0d7b, 0xa6c: 0x16ec, 0xa6d: 0x0d83, 0xa6e: 0x060b, 0xa6f: 0x0d8f,
	0xa70: 0x16f1, 0xa71: 0x0d93, 0xa72: 0x060f, 0xa73: 0x0d9f, 0xa74: 0x0dab, 0xa75: 0x0db7,
	0xa76: 0x0dbb, 0xa77: 0x16f6, 0xa78: 0x168d, 0xa79: 0x16fb, 0xa7a: 0x0ddb, 0xa7b: 0x1700,
	0xa7c: 0x0de7, 0xa7d: 0x0def, 0xa7e: 0x0ddf, 0xa7f: 0x0dfb,
	// Block 0x2a, offset 0xa80
	0xa80: 0x0e0b, 0xa81: 0x0e1b, 0xa82: 0x0e0f, 0xa83: 0x0e13, 0xa84: 0x0e1f, 0xa85: 0x0e23,
	0xa86: 0x1705, 0xa87: 0x0e07, 0xa88: 0x0e3b, 0xa89: 0x0e3f, 0xa8a: 0x0613, 0xa8b: 0x0e53,
	0xa8c: 0x0e4f, 0xa8d: 0x170a, 0xa8e: 0x0e33, 0xa8f: 0x0e6f, 0xa90: 0x170f, 0xa91: 0x1714,
	0xa92: 0x0e73, 0xa93: 0x0e87, 0xa94: 0x0e83, 0xa95: 0x0e7f, 0xa96: 0x0617, 0xa97: 0x0e8b,
	0xa98: 0x0e9b, 0xa99: 0x0e97, 0xa9a: 0x0ea3, 0xa9b: 0x1651, 0xa9c: 0x0eb3, 0xa9d: 0x1719,
	0xa9e: 0x0ebf, 0xa9f: 0x1723, 0xaa0: 0x0ed3, 0xaa1: 0x0edf, 0xaa2: 0x0ef3, 0xaa3: 0x1728,
	0xaa4: 0x0f07, 0xaa5: 0x0f0b, 0xaa6: 0x172d, 0xaa7: 0x1732, 0xaa8: 0x0f27, 0xaa9: 0x0f37,
	0xaaa: 0x061b, 0xaab: 0x0f3b, 0xaac: 0x061f, 0xaad: 0x061f, 0xaae: 0x0f53, 0xaaf: 0x0f57,
	0xab0: 0x0f5f, 0xab1: 0x0f63, 0xab2: 0x0f6f, 0xab3: 0x0623, 0xab4: 0x0f87, 0xab5: 0x1737,
	0xab6: 0x0fa3, 0xab7: 0x173c, 0xab8: 0x0faf, 0xab9: 0x16a1, 0xaba: 0x0fbf, 0xabb: 0x1741,
	0xabc: 0x1746, 0xabd: 0x174b, 0xabe: 0x0627, 0xabf: 0x062b,
	// Block 0x2b, offset 0xac0
	0xac0: 0x0ff7, 0xac1: 0x1755, 0xac2: 0x1750, 0xac3: 0x175a, 0xac4: 0x175f, 0xac5: 0x0fff,
	0xac6: 0x1003, 0xac7: 0x1003, 0xac8: 0x100b, 0xac9: 0x0633, 0xaca: 0x100f, 0xacb: 0x0637,
	0xacc: 0x063b, 0xacd: 0x1769, 0xace: 0x1023, 0xacf: 0x102b, 0xad0: 0x1037, 0xad1: 0x063f,
	0xad2: 0x176e, 0xad3: 0x105b, 0xad4: 0x1773, 0xad5: 0x1778, 0xad6: 0x107b, 0xad7: 0x1093,
	0xad8: 0x0643, 0xad9: 0x109b, 0xada: 0x109f, 0xadb: 0x10a3, 0xadc: 0x177d, 0xadd: 0x1782,
	0xade: 0x1782, 0xadf: 0x10bb, 0xae0: 0x0647, 0xae1: 0x1787, 0xae2: 0x10cf, 0xae3: 0x10d3,
	0xae4: 0x064b, 0xae5: 0x178c, 0xae6: 0x10ef, 0xae7: 0x064f, 0xae8: 0x10ff, 0xae9: 0x10f7,
	0xaea: 0x1107, 0xaeb: 0x1796, 0xaec: 0x111f, 0xaed: 0x0653, 0xaee: 0x112b, 0xaef: 0x1133,
	0xaf0: 0x1143, 0xaf1: 0x0657, 0xaf2: 0x17a0, 0xaf3: 0x17a5, 0xaf4: 0x065b, 0xaf5: 0x17aa,
	0xaf6: 0x115b, 0xaf7: 0x17af, 0xaf8: 0x1167, 0xaf9: 0x1173, 0xafa: 0x117b, 0xafb: 0x17b4,
	0xafc: 0x17b9, 0xafd: 0x118f, 0xafe: 0x17be, 0xaff: 0x1197,
	// Block 0x2c, offset 0xb00
	0xb00: 0x16ce, 0xb01: 0x065f, 0xb02: 0x11af, 0xb03: 0x11b3, 0xb04: 0x0667, 0xb05: 0x11b7,
	0xb06: 0x0a33, 0xb07: 0x17c3, 0xb08: 0x17c8, 0xb09: 0x16d3, 0xb0a: 0x16d8, 0xb0b: 0x11d7,
	0xb0c: 0x11db, 0xb0d: 0x13f3, 0xb0e: 0x066b, 0xb0f: 0x1207, 0xb10: 0x1203, 0xb11: 0x120b,
	0xb12: 0x083f, 0xb13: 0x120f, 0xb14: 0x1213, 0xb15: 0x1217, 0xb16: 0x121f, 0xb17: 0x17cd,
	0xb18: 0x121b, 0xb19: 0x1223, 0xb1a: 0x1237, 0xb1b: 0x123b, 0xb1c: 0x1227, 0xb1d: 0x123f,
	0xb1e: 0x1253, 0xb1f: 0x1267, 0xb20: 0x1233, 0xb21: 0x1247, 0xb22: 0x124b, 0xb23: 0x124f,
	0xb24: 0x17d2, 0xb25: 0x17dc, 0xb26: 0x17d7, 0xb27: 0x066f, 0xb28: 0x126f, 0xb29: 0x1273,
	0xb2a: 0x127b, 0xb2b: 0x17f0, 0xb2c: 0x127f, 0xb2d: 0x17e1, 0xb2e: 0x0673, 0xb2f: 0x0677,
	0xb30: 0x17e6, 0xb31: 0x17eb, 0xb32: 0x067b, 0xb33: 0x129f, 0xb34: 0x12a3, 0xb35: 0x12a7,
	0xb36: 0x12ab, 0xb37: 0x12b7, 0xb38: 0x12b3, 0xb39: 0x12bf, 0xb3a: 0x12bb, 0xb3b: 0x12cb,
	0xb3c: 0x12c3, 0xb3d: 0x12c7, 0xb3e: 0x12cf, 0xb3f: 0x067f,
	// Block 0x2d, offset 0xb40
	0xb40: 0x12d7, 0xb41: 0x12db, 0xb42: 0x0683, 0xb43: 0x12eb, 0xb44: 0x12ef, 0xb45: 0x17f5,
	0xb46: 0x12fb, 0xb47: 0x12ff, 0xb48: 0x0687, 0xb49: 0x130b, 0xb4a: 0x05bb, 0xb4b: 0x17fa,
	0xb4c: 0x17ff, 0xb4d: 0x068b, 0xb4e: 0x068f, 0xb4f: 0x1337, 0xb50: 0x134f, 0xb51: 0x136b,
	0xb52: 0x137b, 0xb53: 0x1804, 0xb54: 0x138f, 0xb55: 0x1393, 0xb56: 0x13ab, 0xb57: 0x13b7,
	0xb58: 0x180e, 0xb59: 0x1660, 0xb5a: 0x13c3, 0xb5b: 0x13bf, 0xb5c: 0x13cb, 0xb5d: 0x1665,
	0xb5e: 0x13d7, 0xb5f: 0x13e3, 0xb60: 0x1813, 0xb61: 0x1818, 0xb62: 0x1423, 0xb63: 0x142f,
	0xb64: 0x1437, 0xb65: 0x181d, 0xb66: 0x143b, 0xb67: 0x1467, 0xb68: 0x1473, 0xb69: 0x1477,
	0xb6a: 0x146f, 0xb6b: 0x1483, 0xb6c: 0x1487, 0xb6d: 0x1822, 0xb6e: 0x1493, 0xb6f: 0x0693,
	0xb70: 0x149b, 0xb71: 0x1827, 0xb72: 0x0697, 0xb73: 0x14d3, 0xb74: 0x0ac3, 0xb75: 0x14eb,
	0xb76: 0x182c, 0xb77: 0x1836, 0xb78: 0x069b, 0xb79: 0x069f, 0xb7a: 0x1513, 0xb7b: 0x183b,
	0xb7c: 0x06a3, 0xb7d: 0x1840, 0xb7e: 0x152b, 0xb7f: 0x152b,
	// Block 0x2e, offset 0xb80
	0xb80: 0x1533, 0xb81: 0x1845, 0xb82: 0x154b, 0xb83: 0x06a7, 0xb84: 0x155b, 0xb85: 0x1567,
	0xb86: 0x156f, 0xb87: 0x1577, 0xb88: 0x06ab, 0xb89: 0x184a, 0xb8a: 0x158b, 0xb8b: 0x15a7,
	0xb8c: 0x15b3, 0xb8d: 0x06af, 0xb8e: 0x06b3, 0xb8f: 0x15b7, 0xb90: 0x184f, 0xb91: 0x06b7,
	0xb92: 0x1854, 0xb93: 0x1859, 0xb94: 0x185e, 0xb95: 0x15db, 0xb96: 0x06bb, 0xb97: 0x15ef,
	0xb98: 0x15f7, 0xb99: 0x15fb, 0xb9a: 0x1603, 0xb9b: 0x160b, 0xb9c: 0x1613, 0xb9d: 0x1868,
}

// nfcIndex: 22 blocks, 1408 entries, 1408 bytes
// Block 0 is the zero block.
var nfcIndex = [1408]uint8{
	// Block 0x0, offset 0x0
	// Block 0x1, offset 0x40
	// Block 0x2, offset 0x80
	// Block 0x3, offset 0xc0
	0xc2: 0x2d, 0xc3: 0x01, 0xc4: 0x02, 0xc5: 0x03, 0xc6: 0x2e, 0xc7: 0x04,
	0xc8: 0x05, 0xca: 0x2f, 0xcb: 0x30, 0xcc: 0x06, 0xcd: 0x07, 0xce: 0x08, 0xcf: 0x31,
	0xd0: 0x09, 0xd1: 0x32, 0xd2: 0x33, 0xd3: 0x0a, 0xd6: 0x0b, 0xd7: 0x34,
	0xd8: 0x35, 0xd9: 0x0c, 0xdb: 0x36, 0xdc: 0x37, 0xdd: 0x38, 0xdf: 0x39,
	0xe0: 0x02, 0xe1: 0x03, 0xe2: 0x04, 0xe3: 0x05,
	0xea: 0x06, 0xeb: 0x07, 0xec: 0x08, 0xed: 0x09, 0xef: 0x0a,
	0xf0: 0x13,
	// Block 0x4, offset 0x100
	0x120: 0x3a, 0x121: 0x3b, 0x123: 0x3c, 0x124: 0x3d, 0x125: 0x3e, 0x126: 0x3f, 0x127: 0x40,
	0x128: 0x41, 0x129: 0x42, 0x12a: 0x43, 0x12b: 0x44, 0x12c: 0x3f, 0x12d: 0x45, 0x12e: 0x46, 0x12f: 0x47,
	0x131: 0x48, 0x132: 0x49, 0x133: 0x4a, 0x134: 0x4b, 0x135: 0x4c, 0x137: 0x4d,
	0x138: 0x4e, 0x139: 0x4f, 0x13a: 0x50, 0x13b: 0x51, 0x13c: 0x52, 0x13d: 0x53, 0x13e: 0x54, 0x13f: 0x55,
	// Block 0x5, offset 0x140
	0x140: 0x56, 0x142: 0x57, 0x144: 0x58, 0x145: 0x59, 0x146: 0x5a, 0x147: 0x5b,
	0x14d: 0x5c,
	0x15c: 0x5d, 0x15f: 0x5e,
	0x162: 0x5f, 0x164: 0x60,
	0x168: 0x61, 0x169: 0x62, 0x16a: 0x63, 0x16c: 0x0d, 0x16d: 0x64, 0x16e: 0x65, 0x16f: 0x66,
	0x170: 0x67, 0x173: 0x68, 0x177: 0x0e,
	0x178: 0x0f, 0x179: 0x10, 0x17a: 0x11, 0x17b: 0x12, 0x17c: 0x13, 0x17d: 0x14, 0x17e: 0x15, 0x17f: 0x16,
	// Block 0x6, offset 0x180
	0x180: 0x69, 0x183: 0x6a, 0x184: 0x6b, 0x186: 0x6c, 0x187: 0x6d,
	0x188: 0x6e, 0x189: 0x17, 0x18a: 0x18, 0x18b: 0x6f, 0x18c: 0x70,
	0x1ab: 0x71,
	0x1b3: 0x72, 0x1b5: 0x73, 0x1b7: 0x74,
	// Block 0x7, offset 0x1c0
	0x1c0: 0x75, 0x1c1: 0x19, 0x1c2: 0x1a, 0x1c3: 0x1b, 0x1c4: 0x76, 0x1c5: 0x77,
	0x1c9: 0x78, 0x1cc: 0x79, 0x1cd: 0x7a,
	// Block 0x8, offset 0x200
	0x219: 0x7b, 0x21a: 0x7c, 0x21b: 0x7d,
	0x220: 0x7e, 0x223: 0x7f, 0x224: 0x80, 0x225: 0x81, 0x226: 0x82, 0x227: 0x83,
	0x22a: 0x84, 0x22b: 0x85, 0x22f: 0x86,
	0x230: 0x87, 0x231: 0x88, 0x232: 0x89, 0x233: 0x8a, 0x234: 0x8b, 0x235: 0x8c, 0x236: 0x8d, 0x237: 0x87,
	0x238: 0x88, 0x239: 0x89, 0x23a: 0x8a, 0x23b: 0x8b, 0x23c: 0x8c, 0x23d: 0x8d, 0x23e: 0x87, 0x23f: 0x88,
	// Block 0x9, offset 0x240
	0x240: 0x89, 0x241: 0x8a, 0x242: 0x8b, 0x243: 0x8c, 0x244: 0x8d, 0x245: 0x87, 0x246: 0x88, 0x247: 0x89,
	0x248: 0x8a, 0x249: 0x8b, 0x24a: 0x8c, 0x24b: 0x8d, 0x24c: 0x87, 0x24d: 0x88, 0x24e: 0x89, 0x24f: 0x8a,
	0x250: 0x8b, 0x251: 0x8c, 0x252: 0x8d, 0x253: 0x87, 0x254: 0x88, 0x255: 0x89, 0x256: 0x8a, 0x257: 0x8b,
	0x258: 0x8c, 0x259: 0x8d, 0x25a: 0x87, 0x25b: 0x88, 0x25c: 0x89, 0x25d: 0x8a, 0x25e: 0x8b, 0x25f: 0x8c,
	0x260: 0x8d, 0x261: 0x87, 0x262: 0x88, 0x263: 0x89, 0x264: 0x8a, 0x265: 0x8b, 0x266: 0x8c, 0x267: 0x8d,
	0x268: 0x87, 0x269: 0x88, 0x26a: 0x89, 0x26b: 0x8a, 0x26c: 0x8b, 0x26d: 0x8c, 0x26e: 0x8d, 0x26f: 0x87,
	0x270: 0x88, 0x271: 0x89, 0x272: 0x8a, 0x273: 0x8b, 0x274: 0x8c, 0x275: 0x8d, 0x276: 0x87, 0x277: 0x88,
	0x278: 0x89, 0x279: 0x8a, 0x27a: 0x8b, 0x27b: 0x8c, 0x27c: 0x8d, 0x27d: 0x87, 0x27e: 0x88, 0x27f: 0x89,
	// Block 0xa, offset 0x280
	0x280: 0x8a, 0x281: 0x8b, 0x282: 0x8c, 0x283: 0x8d, 0x284: 0x87, 0x285: 0x88, 0x286: 0x89, 0x287: 0x8a,
	0x288: 0x8b, 0x289: 0x8c, 0x28a: 0x8d, 0x28b: 0x87, 0x28c: 0x88, 0x28d: 0x89, 0x28e: 0x8a, 0x28f: 0x8b,
	0x290: 0x8c, 0x291: 0x8d, 0x292: 0x87, 0x293: 0x88, 0x294: 0x89, 0x295: 0x8a, 0x296: 0x8b, 0x297: 0x8c,
	0x298: 0x8d, 0x299: 0x87, 0x29a: 0x88, 0x29b: 0x89, 0x29c: 0x8a, 0x29d: 0x8b, 0x29e: 0x8c, 0x29f: 0x8d,
	0x2a0: 0x87, 0x2a1: 0x88, 0x2a2: 0x89, 0x2a3: 0x8a, 0x2a4: 0x8b, 0x2a5: 0x8c, 0x2a6: 0x8d, 0x2a7: 0x87,
	0x2a8: 0x88, 0x2a9: 0x89, 0x2aa: 0x8a, 0x2ab: 0x8b, 0x2ac: 0x8c, 0x2ad: 0x8d, 0x2ae: 0x87, 0x2af: 0x88,
	0x2b0: 0x89, 0x2b1: 0x8a, 0x2b2: 0x8b, 0x2b3: 0x8c, 0x2b4: 0x8d, 0x2b5: 0x87, 0x2b6: 0x88, 0x2b7: 0x89,
	0x2b8: 0x8a, 0x2b9: 0x8b, 0x2ba: 0x8c, 0x2bb: 0x8d, 0x2bc: 0x87, 0x2bd: 0x88, 0x2be: 0x89, 0x2bf: 0x8a,
	// Block 0xb, offset 0x2c0
	0x2c0: 0x8b, 0x2c1: 0x8c, 0x2c2: 0x8d, 0x2c3: 0x87, 0x2c4: 0x88, 0x2c5: 0x89, 0x2c6: 0x8a, 0x2c7: 0x8b,
	0x2c8: 0x8c, 0x2c9: 0x8d, 0x2ca: 0x87, 0x2cb: 0x88, 0x2cc: 0x89, 0x2cd: 0x8a, 0x2ce: 0x8b, 0x2cf: 0x8c,
	0x2d0: 0x8d, 0x2d1: 0x87, 0x2d2: 0x88, 0x2d3: 0x89, 0x2d4: 0x8a, 0x2d5: 0x8b, 0x2d6: 0x8c, 0x2d7: 0x8d,
	0x2d8: 0x87, 0x2d9: 0x88, 0x2da: 0x89, 0x2db: 0x8a, 0x2dc: 0x8b, 0x2dd: 0x8c, 0x2de: 0x8e,
	// Block 0xc, offset 0x300
	0x324: 0x1c, 0x325: 0x1d, 0x326: 0x1e, 0x327: 0x1f,
	0x328: 0x20, 0x329: 0x21, 0x32a: 0x22, 0x32b: 0x23, 0x32c: 0x8f, 0x32d: 0x90, 0x32e: 0x91,
	0x331: 0x92, 0x332: 0x93, 0x333: 0x94, 0x334: 0x95,
	0x338: 0x96, 0x339: 0x97, 0x33a: 0x98, 0x33b: 0x99, 0x33e: 0x9a, 0x33f: 0x9b,
	// Block 0xd, offset 0x340
	0x347: 0x9c,
	0x34b: 0x9d, 0x34d: 0x9e,
	0x368: 0x9f, 0x36b: 0xa0,
	// Block 0xe, offset 0x380
	0x381: 0xa1, 0x382: 0xa2, 0x384: 0xa3, 0x385: 0x82, 0x387: 0xa4,
	0x388: 0xa5, 0x38b: 0xa6, 0x38c: 0x3f, 0x38d: 0xa7,
	0x391: 0xa8, 0x392: 0xa9, 0x393: 0xaa, 0x396: 0xab, 0x397: 0xac,
	0x398: 0x73, 0x39a: 0xad, 0x39c: 0xae,
	0x3a8: 0xaf, 0x3a9: 0xb0, 0x3aa: 0xb1,
	0x3b0: 0x73, 0x3b5: 0xb2,
	// Block 0xf, offset 0x3c0
	0x3eb: 0xb3, 0x3ec: 0xb4,
	// Block 0x10, offset 0x400
	0x432: 0xb5,
	// Block 0x11, offset 0x440
	0x445: 0xb6, 0x446: 0xb7, 0x447: 0xb8,
	0x449: 0xb9,
	// Block 0x12, offset 0x480
	0x480: 0xba,
	0x4a3: 0xbb, 0x4a5: 0xbc,
	// Block 0x13, offset 0x4c0
	0x4c8: 0xbd,
	// Block 0x14, offset 0x500
	0x520: 0x24, 0x521: 0x25, 0x522: 0x26, 0x523: 0x27, 0x524: 0x28, 0x525: 0x29, 0x526: 0x2a, 0x527: 0x2b,
	0x528: 0x2c,
	// Block 0x15, offset 0x540
	0x550: 0x0b, 0x551: 0x0c, 0x556: 0x0d,
	0x55b: 0x0e, 0x55d: 0x0f, 0x55e: 0x10, 0x55f: 0x11,
	0x56f: 0x12,
}

// nfcSparseOffset: 145 entries, 290 bytes
var nfcSparseOffset = []uint16{0x0, 0x5, 0x9, 0xb, 0xd, 0x18, 0x28, 0x2a, 0x2f, 0x3a, 0x49, 0x56, 0x5e, 0x62, 0x67, 0x69, 0x7a, 0x82, 0x89, 0x8c, 0x93, 0x97, 0x9b, 0x9d, 0x9f, 0xa8, 0xac, 0xb3, 0xb8, 0xbb, 0xc5, 0xc8, 0xcf, 0xd7, 0xda, 0xdc, 0xde, 0xe0, 0xe5, 0xf6, 0x102, 0x104, 0x10a, 0x10c, 0x10e, 0x110, 0x112, 0x114, 0x116, 0x119, 0x11c, 0x11e, 0x121, 0x124, 0x128, 0x12d, 0x136, 0x138, 0x13b, 0x13d, 0x148, 0x14c, 0x15a, 0x15d, 0x163, 0x169, 0x174, 0x178, 0x17a, 0x17c, 0x17e, 0x180, 0x182, 0x188, 0x18c, 0x18e, 0x190, 0x198, 0x19c, 0x19f, 0x1a1, 0x1a3, 0x1a5, 0x1a8, 0x1aa, 0x1ac, 0x1ae, 0x1b0, 0x1b6, 0x1b9, 0x1bb, 0x1c2, 0x1c8, 0x1ce, 0x1d6, 0x1dc, 0x1e2, 0x1e8, 0x1ec, 0x1fa, 0x203, 0x206, 0x209, 0x20b, 0x20e, 0x210, 0x214, 0x219, 0x21b, 0x21d, 0x222, 0x228, 0x22a, 0x22c, 0x22e, 0x234, 0x237, 0x23a, 0x242, 0x249, 0x24c, 0x24f, 0x251, 0x259, 0x25c, 0x263, 0x266, 0x26c, 0x26e, 0x271, 0x273, 0x275, 0x277, 0x279, 0x27c, 0x27e, 0x280, 0x282, 0x28f, 0x299, 0x29b, 0x29d, 0x2a3, 0x2a5, 0x2a8}

// nfcSparseValues: 682 entries, 2728 bytes
var nfcSparseValues = [682]valueRange{
	// Block 0x0, offset 0x0
	{value: 0x0000, lo: 0x04},
	{value: 0xa100, lo: 0xa8, hi: 0xa8},
	{value: 0x8100, lo: 0xaf, hi: 0xaf},
	{value: 0x8100, lo: 0xb4, hi: 0xb4},
	{value: 0x8100, lo: 0xb8, hi: 0xb8},
	// Block 0x1, offset 0x5
	{value: 0x0091, lo: 0x03},
	{value: 0x46e2, lo: 0xa0, hi: 0xa1},
	{value: 0x4714, lo: 0xaf, hi: 0xb0},
	{value: 0xa000, lo: 0xb7, hi: 0xb7},
	// Block 0x2, offset 0x9
	{value: 0x0000, lo: 0x01},
	{value: 0xa000, lo: 0x92, hi: 0x92},
	// Block 0x3, offset 0xb
	{value: 0x0000, lo: 0x01},
	{value: 0x8100, lo: 0x98, hi: 0x9d},
	// Block 0x4, offset 0xd
	{value: 0x0006, lo: 0x0a},
	{value: 0xa000, lo: 0x81, hi: 0x81},
	{value: 0xa000, lo: 0x85, hi: 0x85},
	{value: 0xa000, lo: 0x89, hi: 0x89},
	{value: 0x4840, lo: 0x8a, hi: 0x8a},
	{value: 0x485e, lo: 0x8b, hi: 0x8b},
	{value: 0x36c7, lo: 0x8c, hi: 0x8c},
	{value: 0x36df, lo: 0x8d, hi: 0x8d},
	{value: 0x4876, lo: 0x8e, hi: 0x8e},
	{value: 0xa000, lo: 0x92, hi: 0x92},
	{value: 0x36fd, lo: 0x93, hi: 0x94},
	// Block 0x5, offset 0x18
	{value: 0x0000, lo: 0x0f},
	{value: 0xa000, lo: 0x83, hi: 0x83},
	{value: 0xa000, lo: 0x87, hi: 0x87},
	{value: 0xa000, lo: 0x8b, hi: 0x8b},
	{value: 0xa000, lo: 0x8d, hi: 0x8d},
	{value: 0x37a5, lo: 0x90, hi: 0x90},
	{value: 0x37b1, lo: 0x91, hi: 0x91},
	{value: 0x379f, lo: 0x93, hi: 0x93},
	{value: 0xa000, lo: 0x96, hi: 0x96},
	{value: 0x3817, lo: 0x97, hi: 0x97},
	{value: 0x37e1, lo: 0x9c, hi: 0x9c},
	{value: 0x37c9, lo: 0x9d, hi: 0x9d},
	{value: 0x37f3, lo: 0x9e, hi: 0x9e},
	{value: 0xa000, lo: 0xb4, hi: 0xb5},
	{value: 0x381d, lo: 0xb6, hi: 0xb6},
	{value: 0x3823, lo: 0xb7, hi: 0xb7},
	// Block 0x6, offset 0x28
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0x83, hi: 0x87},
	// Block 0x7, offset 0x2a
	{value: 0x0001, lo: 0x04},
	{value: 0x8113, lo: 0x81, hi: 0x82},
	{value: 0x8132, lo: 0x84, hi: 0x84},
	{value: 0x812d, lo: 0x85, hi: 0x85},
	{value: 0x810d, lo: 0x87, hi: 0x87},
	// Block 0x8, offset 0x2f
	{value: 0x0000, lo: 0x0a},
	{value: 0x8132, lo: 0x90, hi: 0x97},
	{value: 0x8119, lo: 0x98, hi: 0x98},
	{value: 0x811a, lo: 0x99, hi: 0x99},
	{value: 0x811b, lo: 0x9a, hi: 0x9a},
	{value: 0x3841, lo: 0xa2, hi: 0xa2},
	{value: 0x3847, lo: 0xa3, hi: 0xa3},
	{value: 0x3853, lo: 0xa4, hi: 0xa4},
	{value: 0x384d, lo: 0xa5, hi: 0xa5},
	{value: 0x3859, lo: 0xa6, hi: 0xa6},
	{value: 0xa000, lo: 0xa7, hi: 0xa7},
	// Block 0x9, offset 0x3a
	{value: 0x0000, lo: 0x0e},
	{value: 0x386b, lo: 0x80, hi: 0x80},
	{value: 0xa000, lo: 0x81, hi: 0x81},
	{value: 0x385f, lo: 0x82, hi: 0x82},
	{value: 0xa000, lo: 0x92, hi: 0x92},
	{value: 0x3865, lo: 0x93, hi: 0x93},
	{value: 0xa000, lo: 0x95, hi: 0x95},
	{value: 0x8132, lo: 0x96, hi: 0x9c},
	{value: 0x8132, lo: 0x9f, hi: 0xa2},
	{value: 0x812d, lo: 0xa3, hi: 0xa3},
	{value: 0x8132, lo: 0xa4, hi: 0xa4},
	{value: 0x8132, lo: 0xa7, hi: 0xa8},
	{value: 0x812d, lo: 0xaa, hi: 0xaa},
	{value: 0x8132, lo: 0xab, hi: 0xac},
	{value: 0x812d, lo: 0xad, hi: 0xad},
	// Block 0xa, offset 0x49
	{value: 0x0000, lo: 0x0c},
	{value: 0x811f, lo: 0x91, hi: 0x91},
	{value: 0x8132, lo: 0xb0, hi: 0xb0},
	{value: 0x812d, lo: 0xb1, hi: 0xb1},
	{value: 0x8132, lo: 0xb2, hi: 0xb3},
	{value: 0x812d, lo: 0xb4, hi: 0xb4},
	{value: 0x8132, lo: 0xb5, hi: 0xb6},
	{value: 0x812d, lo: 0xb7, hi: 0xb9},
	{value: 0x8132, lo: 0xba, hi: 0xba},
	{value: 0x812d, lo: 0xbb, hi: 0xbc},
	{value: 0x8132, lo: 0xbd, hi: 0xbd},
	{value: 0x812d, lo: 0xbe, hi: 0xbe},
	{value: 0x8132, lo: 0xbf, hi: 0xbf},
	// Block 0xb, offset 0x56
	{value: 0x0005, lo: 0x07},
	{value: 0x8132, lo: 0x80, hi: 0x80},
	{value: 0x8132, lo: 0x81, hi: 0x81},
	{value: 0x812d, lo: 0x82, hi: 0x83},
	{value: 0x812d, lo: 0x84, hi: 0x85},
	{value: 0x812d, lo: 0x86, hi: 0x87},
	{value: 0x812d, lo: 0x88, hi: 0x89},
	{value: 0x8132, lo: 0x8a, hi: 0x8a},
	// Block 0xc, offset 0x5e
	{value: 0x0000, lo: 0x03},
	{value: 0x8132, lo: 0xab, hi: 0xb1},
	{value: 0x812d, lo: 0xb2, hi: 0xb2},
	{value: 0x8132, lo: 0xb3, hi: 0xb3},
	// Block 0xd, offset 0x62
	{value: 0x0000, lo: 0x04},
	{value: 0x8132, lo: 0x96, hi: 0x99},
	{value: 0x8132, lo: 0x9b, hi: 0xa3},
	{value: 0x8132, lo: 0xa5, hi: 0xa7},
	{value: 0x8132, lo: 0xa9, hi: 0xad},
	// Block 0xe, offset 0x67
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0x99, hi: 0x9b},
	// Block 0xf, offset 0x69
	{value: 0x0000, lo: 0x10},
	{value: 0x8132, lo: 0x94, hi: 0xa1},
	{value: 0x812d, lo: 0xa3, hi: 0xa3},
	{value: 0x8132, lo: 0xa4, hi: 0xa5},
	{value: 0x812d, lo: 0xa6, hi: 0xa6},
	{value: 0x8132, lo: 0xa7, hi: 0xa8},
	{value: 0x812d, lo: 0xa9, hi: 0xa9},
	{value: 0x8132, lo: 0xaa, hi: 0xac},
	{value: 0x812d, lo: 0xad, hi: 0xaf},
	{value: 0x8116, lo: 0xb0, hi: 0xb0},
	{value: 0x8117, lo: 0xb1, hi: 0xb1},
	{value: 0x8118, lo: 0xb2, hi: 0xb2},
	{value: 0x8132, lo: 0xb3, hi: 0xb5},
	{value: 0x812d, lo: 0xb6, hi: 0xb6},
	{value: 0x8132, lo: 0xb7, hi: 0xb8},
	{value: 0x812d, lo: 0xb9, hi: 0xba},
	{value: 0x8132, lo: 0xbb, hi: 0xbf},
	// Block 0x10, offset 0x7a
	{value: 0x0000, lo: 0x07},
	{value: 0xa000, lo: 0xa8, hi: 0xa8},
	{value: 0x3ed8, lo: 0xa9, hi: 0xa9},
	{value: 0xa000, lo: 0xb0, hi: 0xb0},
	{value: 0x3ee0, lo: 0xb1, hi: 0xb1},
	{value: 0xa000, lo: 0xb3, hi: 0xb3},
	{value: 0x3ee8, lo: 0xb4, hi: 0xb4},
	{value: 0x9902, lo: 0xbc, hi: 0xbc},
	// Block 0x11, offset 0x82
	{value: 0x0008, lo: 0x06},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x8132, lo: 0x91, hi: 0x91},
	{value: 0x812d, lo: 0x92, hi: 0x92},
	{value: 0x8132, lo: 0x93, hi: 0x93},
	{value: 0x8132, lo: 0x94, hi: 0x94},
	{value: 0x451c, lo: 0x98, hi: 0x9f},
	// Block 0x12, offset 0x89
	{value: 0x0000, lo: 0x02},
	{value: 0x8102, lo: 0xbc, hi: 0xbc},
	{value: 0x9900, lo: 0xbe, hi: 0xbe},
	// Block 0x13, offset 0x8c
	{value: 0x0008, lo: 0x06},
	{value: 0xa000, lo: 0x87, hi: 0x87},
	{value: 0x2c9e, lo: 0x8b, hi: 0x8c},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x9900, lo: 0x97, hi: 0x97},
	{value: 0x455c, lo: 0x9c, hi: 0x9d},
	{value: 0x456c, lo: 0x9f, hi: 0x9f},
	// Block 0x14, offset 0x93
	{value: 0x0000, lo: 0x03},
	{value: 0x4594, lo: 0xb3, hi: 0xb3},
	{value: 0x459c, lo: 0xb6, hi: 0xb6},
	{value: 0x8102, lo: 0xbc, hi: 0xbc},
	// Block 0x15, offset 0x97
	{value: 0x0008, lo: 0x03},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x4574, lo: 0x99, hi: 0x9b},
	{value: 0x458c, lo: 0x9e, hi: 0x9e},
	// Block 0x16, offset 0x9b
	{value: 0x0000, lo: 0x01},
	{value: 0x8102, lo: 0xbc, hi: 0xbc},
	// Block 0x17, offset 0x9d
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	// Block 0x18, offset 0x9f
	{value: 0x0000, lo: 0x08},
	{value: 0xa000, lo: 0x87, hi: 0x87},
	{value: 0x2cb6, lo: 0x88, hi: 0x88},
	{value: 0x2cae, lo: 0x8b, hi: 0x8b},
	{value: 0x2cbe, lo: 0x8c, hi: 0x8c},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x9900, lo: 0x96, hi: 0x97},
	{value: 0x45a4, lo: 0x9c, hi: 0x9c},
	{value: 0x45ac, lo: 0x9d, hi: 0x9d},
	// Block 0x19, offset 0xa8
	{value: 0x0000, lo: 0x03},
	{value: 0xa000, lo: 0x92, hi: 0x92},
	{value: 0x2cc6, lo: 0x94, hi: 0x94},
	{value: 0x9900, lo: 0xbe, hi: 0xbe},
	// Block 0x1a, offset 0xac
	{value: 0x0000, lo: 0x06},
	{value: 0xa000, lo: 0x86, hi: 0x87},
	{value: 0x2cce, lo: 0x8a, hi: 0x8a},
	{value: 0x2cde, lo: 0x8b, hi: 0x8b},
	{value: 0x2cd6, lo: 0x8c, hi: 0x8c},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x9900, lo: 0x97, hi: 0x97},
	// Block 0x1b, offset 0xb3
	{value: 0x1801, lo: 0x04},
	{value: 0xa000, lo: 0x86, hi: 0x86},
	{value: 0x3ef0, lo: 0x88, hi: 0x88},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x8120, lo: 0x95, hi: 0x96},
	// Block 0x1c, offset 0xb8
	{value: 0x0000, lo: 0x02},
	{value: 0x8102, lo: 0xbc, hi: 0xbc},
	{value: 0xa000, lo: 0xbf, hi: 0xbf},
	// Block 0x1d, offset 0xbb
	{value: 0x0000, lo: 0x09},
	{value: 0x2ce6, lo: 0x80, hi: 0x80},
	{value: 0x9900, lo: 0x82, hi: 0x82},
	{value: 0xa000, lo: 0x86, hi: 0x86},
	{value: 0x2cee, lo: 0x87, hi: 0x87},
	{value: 0x2cf6, lo: 0x88, hi: 0x88},
	{value: 0x2f50, lo: 0x8a, hi: 0x8a},
	{value: 0x2dd8, lo: 0x8b, hi: 0x8b},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x9900, lo: 0x95, hi: 0x96},
	// Block 0x1e, offset 0xc5
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0xbb, hi: 0xbc},
	{value: 0x9900, lo: 0xbe, hi: 0xbe},
	// Block 0x1f, offset 0xc8
	{value: 0x0000, lo: 0x06},
	{value: 0xa000, lo: 0x86, hi: 0x87},
	{value: 0x2cfe, lo: 0x8a, hi: 0x8a},
	{value: 0x2d0e, lo: 0x8b, hi: 0x8b},
	{value: 0x2d06, lo: 0x8c, hi: 0x8c},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x9900, lo: 0x97, hi: 0x97},
	// Block 0x20, offset 0xcf
	{value: 0x6bea, lo: 0x07},
	{value: 0x9904, lo: 0x8a, hi: 0x8a},
	{value: 0x9900, lo: 0x8f, hi: 0x8f},
	{value: 0xa000, lo: 0x99, hi: 0x99},
	{value: 0x3ef8, lo: 0x9a, hi: 0x9a},
	{value: 0x2f58, lo: 0x9c, hi: 0x9c},
	{value: 0x2de3, lo: 0x9d, hi: 0x9d},
	{value: 0x2d16, lo: 0x9e, hi: 0x9f},
	// Block 0x21, offset 0xd7
	{value: 0x0000, lo: 0x02},
	{value: 0x8122, lo: 0xb8, hi: 0xb9},
	{value: 0x8104, lo: 0xba, hi: 0xba},
	// Block 0x22, offset 0xda
	{value: 0x0000, lo: 0x01},
	{value: 0x8123, lo: 0x88, hi: 0x8b},
	// Block 0x23, offset 0xdc
	{value: 0x0000, lo: 0x01},
	{value: 0x8124, lo: 0xb8, hi: 0xb9},
	// Block 0x24, offset 0xde
	{value: 0x0000, lo: 0x01},
	{value: 0x8125, lo: 0x88, hi: 0x8b},
	// Block 0x25, offset 0xe0
	{value: 0x0000, lo: 0x04},
	{value: 0x812d, lo: 0x98, hi: 0x99},
	{value: 0x812d, lo: 0xb5, hi: 0xb5},
	{value: 0x812d, lo: 0xb7, hi: 0xb7},
	{value: 0x812b, lo: 0xb9, hi: 0xb9},
	// Block 0x26, offset 0xe5
	{value: 0x0000, lo: 0x10},
	{value: 0x2644, lo: 0x83, hi: 0x83},
	{value: 0x264b, lo: 0x8d, hi: 0x8d},
	{value: 0x2652, lo: 0x92, hi: 0x92},
	{value: 0x2659, lo: 0x97, hi: 0x97},
	{value: 0x2660, lo: 0x9c, hi: 0x9c},
	{value: 0x263d, lo: 0xa9, hi: 0xa9},
	{value: 0x8126, lo: 0xb1, hi: 0xb1},
	{value: 0x8127, lo: 0xb2, hi: 0xb2},
	{value: 0x4a84, lo: 0xb3, hi: 0xb3},
	{value: 0x8128, lo: 0xb4, hi: 0xb4},
	{value: 0x4a8d, lo: 0xb5, hi: 0xb5},
	{value: 0x45b4, lo: 0xb6, hi: 0xb6},
	{value: 0x8200, lo: 0xb7, hi: 0xb7},
	{value: 0x45bc, lo: 0xb8, hi: 0xb8},
	{value: 0x8200, lo: 0xb9, hi: 0xb9},
	{value: 0x8127, lo: 0xba, hi: 0xbd},
	// Block 0x27, offset 0xf6
	{value: 0x0000, lo: 0x0b},
	{value: 0x8127, lo: 0x80, hi: 0x80},
	{value: 0x4a96, lo: 0x81, hi: 0x81},
	{value: 0x8132, lo: 0x82, hi: 0x83},
	{value: 0x8104, lo: 0x84, hi: 0x84},
	{value: 0x8132, lo: 0x86, hi: 0x87},
	{value: 0x266e, lo: 0x93, hi: 0x93},
	{value: 0x2675, lo: 0x9d, hi: 0x9d},
	{value: 0x267c, lo: 0xa2, hi: 0xa2},
	{value: 0x2683, lo: 0xa7, hi: 0xa7},
	{value: 0x268a, lo: 0xac, hi: 0xac},
	{value: 0x2667, lo: 0xb9, hi: 0xb9},
	// Block 0x28, offset 0x102
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0x86, hi: 0x86},
	// Block 0x29, offset 0x104
	{value: 0x0000, lo: 0x05},
	{value: 0xa000, lo: 0xa5, hi: 0xa5},
	{value: 0x2d1e, lo: 0xa6, hi: 0xa6},
	{value: 0x9900, lo: 0xae, hi: 0xae},
	{value: 0x8102, lo: 0xb7, hi: 0xb7},
	{value: 0x8104, lo: 0xb9, hi: 0xba},
	// Block 0x2a, offset 0x10a
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0x8d, hi: 0x8d},
	// Block 0x2b, offset 0x10c
	{value: 0x0000, lo: 0x01},
	{value: 0xa000, lo: 0x80, hi: 0x92},
	// Block 0x2c, offset 0x10e
	{value: 0x0000, lo: 0x01},
	{value: 0xb900, lo: 0xa1, hi: 0xb5},
	// Block 0x2d, offset 0x110
	{value: 0x0000, lo: 0x01},
	{value: 0x9900, lo: 0xa8, hi: 0xbf},
	// Block 0x2e, offset 0x112
	{value: 0x0000, lo: 0x01},
	{value: 0x9900, lo: 0x80, hi: 0x82},
	// Block 0x2f, offset 0x114
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0x9d, hi: 0x9f},
	// Block 0x30, offset 0x116
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x94, hi: 0x94},
	{value: 0x8104, lo: 0xb4, hi: 0xb4},
	// Block 0x31, offset 0x119
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x92, hi: 0x92},
	{value: 0x8132, lo: 0x9d, hi: 0x9d},
	// Block 0x32, offset 0x11c
	{value: 0x0000, lo: 0x01},
	{value: 0x8131, lo: 0xa9, hi: 0xa9},
	// Block 0x33, offset 0x11e
	{value: 0x0004, lo: 0x02},
	{value: 0x812e, lo: 0xb9, hi: 0xba},
	{value: 0x812d, lo: 0xbb, hi: 0xbb},
	// Block 0x34, offset 0x121
	{value: 0x0000, lo: 0x02},
	{value: 0x8132, lo: 0x97, hi: 0x97},
	{value: 0x812d, lo: 0x98, hi: 0x98},
	// Block 0x35, offset 0x124
	{value: 0x0000, lo: 0x03},
	{value: 0x8104, lo: 0xa0, hi: 0xa0},
	{value: 0x8132, lo: 0xb5, hi: 0xbc},
	{value: 0x812d, lo: 0xbf, hi: 0xbf},
	// Block 0x36, offset 0x128
	{value: 0x0000, lo: 0x04},
	{value: 0x8132, lo: 0xb0, hi: 0xb4},
	{value: 0x812d, lo: 0xb5, hi: 0xba},
	{value: 0x8132, lo: 0xbb, hi: 0xbc},
	{value: 0x812d, lo: 0xbd, hi: 0xbd},
	// Block 0x37, offset 0x12d
	{value: 0x0000, lo: 0x08},
	{value: 0x2d66, lo: 0x80, hi: 0x80},
	{value: 0x2d6e, lo: 0x81, hi: 0x81},
	{value: 0xa000, lo: 0x82, hi: 0x82},
	{value: 0x2d76, lo: 0x83, hi: 0x83},
	{value: 0x8104, lo: 0x84, hi: 0x84},
	{value: 0x8132, lo: 0xab, hi: 0xab},
	{value: 0x812d, lo: 0xac, hi: 0xac},
	{value: 0x8132, lo: 0xad, hi: 0xb3},
	// Block 0x38, offset 0x136
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0xaa, hi: 0xab},
	// Block 0x39, offset 0x138
	{value: 0x0000, lo: 0x02},
	{value: 0x8102, lo: 0xa6, hi: 0xa6},
	{value: 0x8104, lo: 0xb2, hi: 0xb3},
	// Block 0x3a, offset 0x13b
	{value: 0x0000, lo: 0x01},
	{value: 0x8102, lo: 0xb7, hi: 0xb7},
	// Block 0x3b, offset 0x13d
	{value: 0x0000, lo: 0x0a},
	{value: 0x8132, lo: 0x90, hi: 0x92},
	{value: 0x8101, lo: 0x94, hi: 0x94},
	{value: 0x812d, lo: 0x95, hi: 0x99},
	{value: 0x8132, lo: 0x9a, hi: 0x9b},
	{value: 0x812d, lo: 0x9c, hi: 0x9f},
	{value: 0x8132, lo: 0xa0, hi: 0xa0},
	{value: 0x8101, lo: 0xa2, hi: 0xa8},
	{value: 0x812d, lo: 0xad, hi: 0xad},
	{value: 0x8132, lo: 0xb4, hi: 0xb4},
	{value: 0x8132, lo: 0xb8, hi: 0xb9},
	// Block 0x3c, offset 0x148
	{value: 0x0004, lo: 0x03},
	{value: 0x0433, lo: 0x80, hi: 0x81},
	{value: 0x8100, lo: 0x97, hi: 0x97},
	{value: 0x8100, lo: 0xbe, hi: 0xbe},
	// Block 0x3d, offset 0x14c
	{value: 0x0000, lo: 0x0d},
	{value: 0x8132, lo: 0x90, hi: 0x91},
	{value: 0x8101, lo: 0x92, hi: 0x93},
	{value: 0x8132, lo: 0x94, hi: 0x97},
	{value: 0x8101, lo: 0x98, hi: 0x9a},
	{value: 0x8132, lo: 0x9b, hi: 0x9c},
	{value: 0x8132, lo: 0xa1, hi: 0xa1},
	{value: 0x8101, lo: 0xa5, hi: 0xa6},
	{value: 0x8132, lo: 0xa7, hi: 0xa7},
	{value: 0x812d, lo: 0xa8, hi: 0xa8},
	{value: 0x8132, lo: 0xa9, hi: 0xa9},
	{value: 0x8101, lo: 0xaa, hi: 0xab},
	{value: 0x812d, lo: 0xac, hi: 0xaf},
	{value: 0x8132, lo: 0xb0, hi: 0xb0},
	// Block 0x3e, offset 0x15a
	{value: 0x427b, lo: 0x02},
	{value: 0x01b8, lo: 0xa6, hi: 0xa6},
	{value: 0x0057, lo: 0xaa, hi: 0xab},
	// Block 0x3f, offset 0x15d
	{value: 0x0007, lo: 0x05},
	{value: 0xa000, lo: 0x90, hi: 0x90},
	{value: 0xa000, lo: 0x92, hi: 0x92},
	{value: 0xa000, lo: 0x94, hi: 0x94},
	{value: 0x3bb9, lo: 0x9a, hi: 0x9b},
	{value: 0x3bc7, lo: 0xae, hi: 0xae},
	// Block 0x40, offset 0x163
	{value: 0x000e, lo: 0x05},
	{value: 0x3bce, lo: 0x8d, hi: 0x8e},
	{value: 0x3bd5, lo: 0x8f, hi: 0x8f},
	{value: 0xa000, lo: 0x90, hi: 0x90},
	{value: 0xa000, lo: 0x92, hi: 0x92},
	{value: 0xa000, lo: 0x94, hi: 0x94},
	// Block 0x41, offset 0x169
	{value: 0x6408, lo: 0x0a},
	{value: 0xa000, lo: 0x83, hi: 0x83},
	{value: 0x3be3, lo: 0x84, hi: 0x84},
	{value: 0xa000, lo: 0x88, hi: 0x88},
	{value: 0x3bea, lo: 0x89, hi: 0x89},
	{value: 0xa000, lo: 0x8b, hi: 0x8b},
	{value: 0x3bf1, lo: 0x8c, hi: 0x8c},
	{value: 0xa000, lo: 0xa3, hi: 0xa3},
	{value: 0x3bf8, lo: 0xa4, hi: 0xa5},
	{value: 0x3bff, lo: 0xa6, hi: 0xa6},
	{value: 0xa000, lo: 0xbc, hi: 0xbc},
	// Block 0x42, offset 0x174
	{value: 0x0007, lo: 0x03},
	{value: 0x3c68, lo: 0xa0, hi: 0xa1},
	{value: 0x3c92, lo: 0xa2, hi: 0xa3},
	{value: 0x3cbc, lo: 0xaa, hi: 0xad},
	// Block 0x43, offset 0x178
	{value: 0x0004, lo: 0x01},
	{value: 0x048b, lo: 0xa9, hi: 0xaa},
	// Block 0x44, offset 0x17a
	{value: 0x0000, lo: 0x01},
	{value: 0x44dd, lo: 0x9c, hi: 0x9c},
	// Block 0x45, offset 0x17c
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0xaf, hi: 0xb1},
	// Block 0x46, offset 0x17e
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0xbf, hi: 0xbf},
	// Block 0x47, offset 0x180
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0xa0, hi: 0xbf},
	// Block 0x48, offset 0x182
	{value: 0x0000, lo: 0x05},
	{value: 0x812c, lo: 0xaa, hi: 0xaa},
	{value: 0x8131, lo: 0xab, hi: 0xab},
	{value: 0x8133, lo: 0xac, hi: 0xac},
	{value: 0x812e, lo: 0xad, hi: 0xad},
	{value: 0x812f, lo: 0xae, hi: 0xaf},
	// Block 0x49, offset 0x188
	{value: 0x0000, lo: 0x03},
	{value: 0x4a9f, lo: 0xb3, hi: 0xb3},
	{value: 0x4a9f, lo: 0xb5, hi: 0xb6},
	{value: 0x4a9f, lo: 0xba, hi: 0xbf},
	// Block 0x4a, offset 0x18c
	{value: 0x0000, lo: 0x01},
	{value: 0x4a9f, lo: 0x8f, hi: 0xa3},
	// Block 0x4b, offset 0x18e
	{value: 0x0000, lo: 0x01},
	{value: 0x8100, lo: 0xae, hi: 0xbe},
	// Block 0x4c, offset 0x190
	{value: 0x0000, lo: 0x07},
	{value: 0x8100, lo: 0x84, hi: 0x84},
	{value: 0x8100, lo: 0x87, hi: 0x87},
	{value: 0x8100, lo: 0x90, hi: 0x90},
	{value: 0x8100, lo: 0x9e, hi: 0x9e},
	{value: 0x8100, lo: 0xa1, hi: 0xa1},
	{value: 0x8100, lo: 0xb2, hi: 0xb2},
	{value: 0x8100, lo: 0xbb, hi: 0xbb},
	// Block 0x4d, offset 0x198
	{value: 0x0000, lo: 0x03},
	{value: 0x8100, lo: 0x80, hi: 0x80},
	{value: 0x8100, lo: 0x8b, hi: 0x8b},
	{value: 0x8100, lo: 0x8e, hi: 0x8e},
	// Block 0x4e, offset 0x19c
	{value: 0x0000, lo: 0x02},
	{value: 0x8132, lo: 0xaf, hi: 0xaf},
	{value: 0x8132, lo: 0xb4, hi: 0xbd},
	// Block 0x4f, offset 0x19f
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0x9e, hi: 0x9f},
	// Block 0x50, offset 0x1a1
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0xb0, hi: 0xb1},
	// Block 0x51, offset 0x1a3
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x86, hi: 0x86},
	// Block 0x52, offset 0x1a5
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x84, hi: 0x84},
	{value: 0x8132, lo: 0xa0, hi: 0xb1},
	// Block 0x53, offset 0x1a8
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0xab, hi: 0xad},
	// Block 0x54, offset 0x1aa
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x93, hi: 0x93},
	// Block 0x55, offset 0x1ac
	{value: 0x0000, lo: 0x01},
	{value: 0x8102, lo: 0xb3, hi: 0xb3},
	// Block 0x56, offset 0x1ae
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x80, hi: 0x80},
	// Block 0x57, offset 0x1b0
	{value: 0x0000, lo: 0x05},
	{value: 0x8132, lo: 0xb0, hi: 0xb0},
	{value: 0x8132, lo: 0xb2, hi: 0xb3},
	{value: 0x812d, lo: 0xb4, hi: 0xb4},
	{value: 0x8132, lo: 0xb7, hi: 0xb8},
	{value: 0x8132, lo: 0xbe, hi: 0xbf},
	// Block 0x58, offset 0x1b6
	{value: 0x0000, lo: 0x02},
	{value: 0x8132, lo: 0x81, hi: 0x81},
	{value: 0x8104, lo: 0xb6, hi: 0xb6},
	// Block 0x59, offset 0x1b9
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0xad, hi: 0xad},
	// Block 0x5a, offset 0x1bb
	{value: 0x0000, lo: 0x06},
	{value: 0xe500, lo: 0x80, hi: 0x80},
	{value: 0xc600, lo: 0x81, hi: 0x9b},
	{value: 0xe500, lo: 0x9c, hi: 0x9c},
	{value: 0xc600, lo: 0x9d, hi: 0xb7},
	{value: 0xe500, lo: 0xb8, hi: 0xb8},
	{value: 0xc600, lo: 0xb9, hi: 0xbf},
	// Block 0x5b, offset 0x1c2
	{value: 0x0000, lo: 0x05},
	{value: 0xc600, lo: 0x80, hi: 0x93},
	{value: 0xe500, lo: 0x94, hi: 0x94},
	{value: 0xc600, lo: 0x95, hi: 0xaf},
	{value: 0xe500, lo: 0xb0, hi: 0xb0},
	{value: 0xc600, lo: 0xb1, hi: 0xbf},
	// Block 0x5c, offset 0x1c8
	{value: 0x0000, lo: 0x05},
	{value: 0xc600, lo: 0x80, hi: 0x8b},
	{value: 0xe500, lo: 0x8c, hi: 0x8c},
	{value: 0xc600, lo: 0x8d, hi: 0xa7},
	{value: 0xe500, lo: 0xa8, hi: 0xa8},
	{value: 0xc600, lo: 0xa9, hi: 0xbf},
	// Block 0x5d, offset 0x1ce
	{value: 0x0000, lo: 0x07},
	{value: 0xc600, lo: 0x80, hi: 0x83},
	{value: 0xe500, lo: 0x84, hi: 0x84},
	{value: 0xc600, lo: 0x85, hi: 0x9f},
	{value: 0xe500, lo: 0xa0, hi: 0xa0},
	{value: 0xc600, lo: 0xa1, hi: 0xbb},
	{value: 0xe500, lo: 0xbc, hi: 0xbc},
	{value: 0xc600, lo: 0xbd, hi: 0xbf},
	// Block 0x5e, offset 0x1d6
	{value: 0x0000, lo: 0x05},
	{value: 0xc600, lo: 0x80, hi: 0x97},
	{value: 0xe500, lo: 0x98, hi: 0x98},
	{value: 0xc600, lo: 0x99, hi: 0xb3},
	{value: 0xe500, lo: 0xb4, hi: 0xb4},
	{value: 0xc600, lo: 0xb5, hi: 0xbf},
	// Block 0x5f, offset 0x1dc
	{value: 0x0000, lo: 0x05},
	{value: 0xc600, lo: 0x80, hi: 0x8f},
	{value: 0xe500, lo: 0x90, hi: 0x90},
	{value: 0xc600, lo: 0x91, hi: 0xab},
	{value: 0xe500, lo: 0xac, hi: 0xac},
	{value: 0xc600, lo: 0xad, hi: 0xbf},
	// Block 0x60, offset 0x1e2
	{value: 0x0000, lo: 0x05},
	{value: 0xc600, lo: 0x80, hi: 0x87},
	{value: 0xe500, lo: 0x88, hi: 0x88},
	{value: 0xc600, lo: 0x89, hi: 0xa3},
	{value: 0xe500, lo: 0xa4, hi: 0xa4},
	{value: 0xc600, lo: 0xa5, hi: 0xbf},
	// Block 0x61, offset 0x1e8
	{value: 0x0000, lo: 0x03},
	{value: 0xc600, lo: 0x80, hi: 0x87},
	{value: 0xe500, lo: 0x88, hi: 0x88},
	{value: 0xc600, lo: 0x89, hi: 0xa3},
	// Block 0x62, offset 0x1ec
	{value: 0x0006, lo: 0x0d},
	{value: 0x4390, lo: 0x9d, hi: 0x9d},
	{value: 0x8115, lo: 0x9e, hi: 0x9e},
	{value: 0x4402, lo: 0x9f, hi: 0x9f},
	{value: 0x43f0, lo: 0xaa, hi: 0xab},
	{value: 0x44f4, lo: 0xac, hi: 0xac},
	{value: 0x44fc, lo: 0xad, hi: 0xad},
	{value: 0x4348, lo: 0xae, hi: 0xb1},
	{value: 0x4366, lo: 0xb2, hi: 0xb4},
	{value: 0x437e, lo: 0xb5, hi: 0xb6},
	{value: 0x438a, lo: 0xb8, hi: 0xb8},
	{value: 0x4396, lo: 0xb9, hi: 0xbb},
	{value: 0x43ae, lo: 0xbc, hi: 0xbc},
	{value: 0x43b4, lo: 0xbe, hi: 0xbe},
	// Block 0x63, offset 0x1fa
	{value: 0x0006, lo: 0x08},
	{value: 0x43ba, lo: 0x80, hi: 0x81},
	{value: 0x43c6, lo: 0x83, hi: 0x84},
	{value: 0x43d8, lo: 0x86, hi: 0x89},
	{value: 0x43fc, lo: 0x8a, hi: 0x8a},
	{value: 0x4378, lo: 0x8b, hi: 0x8b},
	{value: 0x4360, lo: 0x8c, hi: 0x8c},
	{value: 0x43a8, lo: 0x8d, hi: 0x8d},
	{value: 0x43d2, lo: 0x8e, hi: 0x8e},
	// Block 0x64, offset 0x203
	{value: 0x0000, lo: 0x02},
	{value: 0x8100, lo: 0xa4, hi: 0xa5},
	{value: 0x8100, lo: 0xb0, hi: 0xb1},
	// Block 0x65, offset 0x206
	{value: 0x0000, lo: 0x02},
	{value: 0x8100, lo: 0x9b, hi: 0x9d},
	{value: 0x8200, lo: 0x9e, hi: 0xa3},
	// Block 0x66, offset 0x209
	{value: 0x0000, lo: 0x01},
	{value: 0x8100, lo: 0x90, hi: 0x90},
	// Block 0x67, offset 0x20b
	{value: 0x0000, lo: 0x02},
	{value: 0x8100, lo: 0x99, hi: 0x99},
	{value: 0x8200, lo: 0xb2, hi: 0xb4},
	// Block 0x68, offset 0x20e
	{value: 0x0000, lo: 0x01},
	{value: 0x8100, lo: 0xbc, hi: 0xbd},
	// Block 0x69, offset 0x210
	{value: 0x0000, lo: 0x03},
	{value: 0x8132, lo: 0xa0, hi: 0xa6},
	{value: 0x812d, lo: 0xa7, hi: 0xad},
	{value: 0x8132, lo: 0xae, hi: 0xaf},
	// Block 0x6a, offset 0x214
	{value: 0x0000, lo: 0x04},
	{value: 0x8100, lo: 0x89, hi: 0x8c},
	{value: 0x8100, lo: 0xb0, hi: 0xb2},
	{value: 0x8100, lo: 0xb4, hi: 0xb4},
	{value: 0x8100, lo: 0xb6, hi: 0xbf},
	// Block 0x6b, offset 0x219
	{value: 0x0000, lo: 0x01},
	{value: 0x8100, lo: 0x81, hi: 0x8c},
	// Block 0x6c, offset 0x21b
	{value: 0x0000, lo: 0x01},
	{value: 0x8100, lo: 0xb5, hi: 0xba},
	// Block 0x6d, offset 0x21d
	{value: 0x0000, lo: 0x04},
	{value: 0x4a9f, lo: 0x9e, hi: 0x9f},
	{value: 0x4a9f, lo: 0xa3, hi: 0xa3},
	{value: 0x4a9f, lo: 0xa5, hi: 0xa6},
	{value: 0x4a9f, lo: 0xaa, hi: 0xaf},
	// Block 0x6e, offset 0x222
	{value: 0x0000, lo: 0x05},
	{value: 0x4a9f, lo: 0x82, hi: 0x87},
	{value: 0x4a9f, lo: 0x8a, hi: 0x8f},
	{value: 0x4a9f, lo: 0x92, hi: 0x97},
	{value: 0x4a9f, lo: 0x9a, hi: 0x9c},
	{value: 0x8100, lo: 0xa3, hi: 0xa3},
	// Block 0x6f, offset 0x228
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0xbd, hi: 0xbd},
	// Block 0x70, offset 0x22a
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0xa0, hi: 0xa0},
	// Block 0x71, offset 0x22c
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0xb6, hi: 0xba},
	// Block 0x72, offset 0x22e
	{value: 0x002c, lo: 0x05},
	{value: 0x812d, lo: 0x8d, hi: 0x8d},
	{value: 0x8132, lo: 0x8f, hi: 0x8f},
	{value: 0x8132, lo: 0xb8, hi: 0xb8},
	{value: 0x8101, lo: 0xb9, hi: 0xba},
	{value: 0x8104, lo: 0xbf, hi: 0xbf},
	// Block 0x73, offset 0x234
	{value: 0x0000, lo: 0x02},
	{value: 0x8132, lo: 0xa5, hi: 0xa5},
	{value: 0x812d, lo: 0xa6, hi: 0xa6},
	// Block 0x74, offset 0x237
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x86, hi: 0x86},
	{value: 0x8104, lo: 0xbf, hi: 0xbf},
	// Block 0x75, offset 0x23a
	{value: 0x17fe, lo: 0x07},
	{value: 0xa000, lo: 0x99, hi: 0x99},
	{value: 0x4238, lo: 0x9a, hi: 0x9a},
	{value: 0xa000, lo: 0x9b, hi: 0x9b},
	{value: 0x4242, lo: 0x9c, hi: 0x9c},
	{value: 0xa000, lo: 0xa5, hi: 0xa5},
	{value: 0x424c, lo: 0xab, hi: 0xab},
	{value: 0x8104, lo: 0xb9, hi: 0xba},
	// Block 0x76, offset 0x242
	{value: 0x0000, lo: 0x06},
	{value: 0x8132, lo: 0x80, hi: 0x82},
	{value: 0x9900, lo: 0xa7, hi: 0xa7},
	{value: 0x2d7e, lo: 0xae, hi: 0xae},
	{value: 0x2d88, lo: 0xaf, hi: 0xaf},
	{value: 0xa000, lo: 0xb1, hi: 0xb2},
	{value: 0x8104, lo: 0xb3, hi: 0xb4},
	// Block 0x77, offset 0x249
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x80, hi: 0x80},
	{value: 0x8102, lo: 0x8a, hi: 0x8a},
	// Block 0x78, offset 0x24c
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0xb5, hi: 0xb5},
	{value: 0x8102, lo: 0xb6, hi: 0xb6},
	// Block 0x79, offset 0x24f
	{value: 0x0002, lo: 0x01},
	{value: 0x8102, lo: 0xa9, hi: 0xaa},
	// Block 0x7a, offset 0x251
	{value: 0x0000, lo: 0x07},
	{value: 0xa000, lo: 0x87, hi: 0x87},
	{value: 0x2d92, lo: 0x8b, hi: 0x8b},
	{value: 0x2d9c, lo: 0x8c, hi: 0x8c},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x9900, lo: 0x97, hi: 0x97},
	{value: 0x8132, lo: 0xa6, hi: 0xac},
	{value: 0x8132, lo: 0xb0, hi: 0xb4},
	// Block 0x7b, offset 0x259
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x82, hi: 0x82},
	{value: 0x8102, lo: 0x86, hi: 0x86},
	// Block 0x7c, offset 0x25c
	{value: 0x6b5a, lo: 0x06},
	{value: 0x9900, lo: 0xb0, hi: 0xb0},
	{value: 0xa000, lo: 0xb9, hi: 0xb9},
	{value: 0x9900, lo: 0xba, hi: 0xba},
	{value: 0x2db0, lo: 0xbb, hi: 0xbb},
	{value: 0x2da6, lo: 0xbc, hi: 0xbd},
	{value: 0x2dba, lo: 0xbe, hi: 0xbe},
	// Block 0x7d, offset 0x263
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x82, hi: 0x82},
	{value: 0x8102, lo: 0x83, hi: 0x83},
	// Block 0x7e, offset 0x266
	{value: 0x0000, lo: 0x05},
	{value: 0x9900, lo: 0xaf, hi: 0xaf},
	{value: 0xa000, lo: 0xb8, hi: 0xb9},
	{value: 0x2dc4, lo: 0xba, hi: 0xba},
	{value: 0x2dce, lo: 0xbb, hi: 0xbb},
	{value: 0x8104, lo: 0xbf, hi: 0xbf},
	// Block 0x7f, offset 0x26c
	{value: 0x0000, lo: 0x01},
	{value: 0x8102, lo: 0x80, hi: 0x80},
	// Block 0x80, offset 0x26e
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0xb6, hi: 0xb6},
	{value: 0x8102, lo: 0xb7, hi: 0xb7},
	// Block 0x81, offset 0x271
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0xab, hi: 0xab},
	// Block 0x82, offset 0x273
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0xb4, hi: 0xb4},
	// Block 0x83, offset 0x275
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x87, hi: 0x87},
	// Block 0x84, offset 0x277
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x99, hi: 0x99},
	// Block 0x85, offset 0x279
	{value: 0x0000, lo: 0x02},
	{value: 0x8102, lo: 0x82, hi: 0x82},
	{value: 0x8104, lo: 0x84, hi: 0x85},
	// Block 0x86, offset 0x27c
	{value: 0x0000, lo: 0x01},
	{value: 0x8101, lo: 0xb0, hi: 0xb4},
	// Block 0x87, offset 0x27e
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0xb0, hi: 0xb6},
	// Block 0x88, offset 0x280
	{value: 0x0000, lo: 0x01},
	{value: 0x8101, lo: 0x9e, hi: 0x9e},
	// Block 0x89, offset 0x282
	{value: 0x0000, lo: 0x0c},
	{value: 0x45cc, lo: 0x9e, hi: 0x9e},
	{value: 0x45d6, lo: 0x9f, hi: 0x9f},
	{value: 0x460a, lo: 0xa0, hi: 0xa0},
	{value: 0x4618, lo: 0xa1, hi: 0xa1},
	{value: 0x4626, lo: 0xa2, hi: 0xa2},
	{value: 0x4634, lo: 0xa3, hi: 0xa3},
	{value: 0x4642, lo: 0xa4, hi: 0xa4},
	{value: 0x812b, lo: 0xa5, hi: 0xa6},
	{value: 0x8101, lo: 0xa7, hi: 0xa9},
	{value: 0x8130, lo: 0xad, hi: 0xad},
	{value: 0x812b, lo: 0xae, hi: 0xb2},
	{value: 0x812d, lo: 0xbb, hi: 0xbf},
	// Block 0x8a, offset 0x28f
	{value: 0x0000, lo: 0x09},
	{value: 0x812d, lo: 0x80, hi: 0x82},
	{value: 0x8132, lo: 0x85, hi: 0x89},
	{value: 0x812d, lo: 0x8a, hi: 0x8b},
	{value: 0x8132, lo: 0xaa, hi: 0xad},
	{value: 0x45e0, lo: 0xbb, hi: 0xbb},
	{value: 0x45ea, lo: 0xbc, hi: 0xbc},
	{value: 0x4650, lo: 0xbd, hi: 0xbd},
	{value: 0x466c, lo: 0xbe, hi: 0xbe},
	{value: 0x465e, lo: 0xbf, hi: 0xbf},
	// Block 0x8b, offset 0x299
	{value: 0x0000, lo: 0x01},
	{value: 0x467a, lo: 0x80, hi: 0x80},
	// Block 0x8c, offset 0x29b
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0x82, hi: 0x84},
	// Block 0x8d, offset 0x29d
	{value: 0x0000, lo: 0x05},
	{value: 0x8132, lo: 0x80, hi: 0x86},
	{value: 0x8132, lo: 0x88, hi: 0x98},
	{value: 0x8132, lo: 0x9b, hi: 0xa1},
	{value: 0x8132, lo: 0xa3, hi: 0xa4},
	{value: 0x8132, lo: 0xa6, hi: 0xaa},
	// Block 0x8e, offset 0x2a3
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0x90, hi: 0x96},
	// Block 0x8f, offset 0x2a5
	{value: 0x0000, lo: 0x02},
	{value: 0x8132, lo: 0x84, hi: 0x89},
	{value: 0x8102, lo: 0x8a, hi: 0x8a},
	// Block 0x90, offset 0x2a8
	{value: 0x0000, lo: 0x01},
	{value: 0x8100, lo: 0x93, hi: 0x93},
}

// lookup returns the trie value for the first UTF-8 encoding in s and
// the width in bytes of this encoding. The size will be 0 if s does not
// hold enough bytes to complete the encoding. len(s) must be greater than 0.
func (t *nfkcTrie) lookup(s []byte) (v uint16, sz int) {
	c0 := s[0]
	switch {
	case c0 < 0x80: // is ASCII
		return nfkcValues[c0], 1
	case c0 < 0xC2:
		return 0, 1 // Illegal UTF-8: not a starter, not ASCII.
	case c0 < 0xE0: // 2-byte UTF-8
		if len(s) < 2 {
			return 0, 0
		}
		i := nfkcIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c1), 2
	case c0 < 0xF0: // 3-byte UTF-8
		if len(s) < 3 {
			return 0, 0
		}
		i := nfkcIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = nfkcIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c2), 3
	case c0 < 0xF8: // 4-byte UTF-8
		if len(s) < 4 {
			return 0, 0
		}
		i := nfkcIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = nfkcIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		o = uint32(i)<<6 + uint32(c2)
		i = nfkcIndex[o]
		c3 := s[3]
		if c3 < 0x80 || 0xC0 <= c3 {
			return 0, 3 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c3), 4
	}
	// Illegal rune
	return 0, 1
}

// lookupUnsafe returns the trie value for the first UTF-8 encoding in s.
// s must start with a full and valid UTF-8 encoded rune.
func (t *nfkcTrie) lookupUnsafe(s []byte) uint16 {
	c0 := s[0]
	if c0 < 0x80 { // is ASCII
		return nfkcValues[c0]
	}
	i := nfkcIndex[c0]
	if c0 < 0xE0 { // 2-byte UTF-8
		return t.lookupValue(uint32(i), s[1])
	}
	i = nfkcIndex[uint32(i)<<6+uint32(s[1])]
	if c0 < 0xF0 { // 3-byte UTF-8
		return t.lookupValue(uint32(i), s[2])
	}
	i = nfkcIndex[uint32(i)<<6+uint32(s[2])]
	if c0 < 0xF8 { // 4-byte UTF-8
		return t.lookupValue(uint32(i), s[3])
	}
	return 0
}

// lookupString returns the trie value for the first UTF-8 encoding in s and
// the width in bytes of this encoding. The size will be 0 if s does not
// hold enough bytes to complete the encoding. len(s) must be greater than 0.
func (t *nfkcTrie) lookupString(s string) (v uint16, sz int) {
	c0 := s[0]
	switch {
	case c0 < 0x80: // is ASCII
		return nfkcValues[c0], 1
	case c0 < 0xC2:
		return 0, 1 // Illegal UTF-8: not a starter, not ASCII.
	case c0 < 0xE0: // 2-byte UTF-8
		if len(s) < 2 {
			return 0, 0
		}
		i := nfkcIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c1), 2
	case c0 < 0xF0: // 3-byte UTF-8
		if len(s) < 3 {
			return 0, 0
		}
		i := nfkcIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = nfkcIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c2), 3
	case c0 < 0xF8: // 4-byte UTF-8
		if len(s) < 4 {
			return 0, 0
		}
		i := nfkcIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = nfkcIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		o = uint32(i)<<6 + uint32(c2)
		i = nfkcIndex[o]
		c3 := s[3]
		if c3 < 0x80 || 0xC0 <= c3 {
			return 0, 3 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c3), 4
	}
	// Illegal rune
	return 0, 1
}

// lookupStringUnsafe returns the trie value for the first UTF-8 encoding in s.
// s must start with a full and valid UTF-8 encoded rune.
func (t *nfkcTrie) lookupStringUnsafe(s string) uint16 {
	c0 := s[0]
	if c0 < 0x80 { // is ASCII
		return nfkcValues[c0]
	}
	i := nfkcIndex[c0]
	if c0 < 0xE0 { // 2-byte UTF-8
		return t.lookupValue(uint32(i), s[1])
	}
	i = nfkcIndex[uint32(i)<<6+uint32(s[1])]
	if c0 < 0xF0 { // 3-byte UTF-8
		return t.lookupValue(uint32(i), s[2])
	}
	i = nfkcIndex[uint32(i)<<6+uint32(s[2])]
	if c0 < 0xF8 { // 4-byte UTF-8
		return t.lookupValue(uint32(i), s[3])
	}
	return 0
}

// nfkcTrie. Total size: 17104 bytes (16.70 KiB). Checksum: d985061cf5307b35.
type nfkcTrie struct{}

func newNfkcTrie(i int) *nfkcTrie {
	return &nfkcTrie{}
}

// lookupValue determines the type of block n and looks up the value for b.
func (t *nfkcTrie) lookupValue(n uint32, b byte) uint16 {
	switch {
	case n < 91:
		return uint16(nfkcValues[n<<6+uint32(b)])
	default:
		n -= 91
		return uint16(nfkcSparse.lookup(n, b))
	}
}

// nfkcValues: 93 blocks, 5952 entries, 11904 bytes
// The third block is the zero block.
var nfkcValues = [5952]uint16{
	// Block 0x0, offset 0x0
	0x3c: 0xa000, 0x3d: 0xa000, 0x3e: 0xa000,
	// Block 0x1, offset 0x40
	0x41: 0xa000, 0x42: 0xa000, 0x43: 0xa000, 0x44: 0xa000, 0x45: 0xa000,
	0x46: 0xa000, 0x47: 0xa000, 0x48: 0xa000, 0x49: 0xa000, 0x4a: 0xa000, 0x4b: 0xa000,
	0x4c: 0xa000, 0x4d: 0xa000, 0x4e: 0xa000, 0x4f: 0xa000, 0x50: 0xa000,
	0x52: 0xa000, 0x53: 0xa000, 0x54: 0xa000, 0x55: 0xa000, 0x56: 0xa000, 0x57: 0xa000,
	0x58: 0xa000, 0x59: 0xa000, 0x5a: 0xa000,
	0x61: 0xa000, 0x62: 0xa000, 0x63: 0xa000,
	0x64: 0xa000, 0x65: 0xa000, 0x66: 0xa000, 0x67: 0xa000, 0x68: 0xa000, 0x69: 0xa000,
	0x6a: 0xa000, 0x6b: 0xa000, 0x6c: 0xa000, 0x6d: 0xa000, 0x6e: 0xa000, 0x6f: 0xa000,
	0x70: 0xa000, 0x72: 0xa000, 0x73: 0xa000, 0x74: 0xa000, 0x75: 0xa000,
	0x76: 0xa000, 0x77: 0xa000, 0x78: 0xa000, 0x79: 0xa000, 0x7a: 0xa000,
	// Block 0x2, offset 0x80
	// Block 0x3, offset 0xc0
	0xc0: 0x2f6f, 0xc1: 0x2f74, 0xc2: 0x4688, 0xc3: 0x2f79, 0xc4: 0x4697, 0xc5: 0x469c,
	0xc6: 0xa000, 0xc7: 0x46a6, 0xc8: 0x2fe2, 0xc9: 0x2fe7, 0xca: 0x46ab, 0xcb: 0x2ffb,
	0xcc: 0x306e, 0xcd: 0x3073, 0xce: 0x3078, 0xcf: 0x46bf, 0xd1: 0x3104,
	0xd2: 0x3127, 0xd3: 0x312c, 0xd4: 0x46c9, 0xd5: 0x46ce, 0xd6: 0x46dd,
	0xd8: 0xa000, 0xd9: 0x31b3, 0xda: 0x31b8, 0xdb: 0x31bd, 0xdc: 0x470f, 0xdd: 0x3235,
	0xe0: 0x327b, 0xe1: 0x3280, 0xe2: 0x4719, 0xe3: 0x3285,
	0xe4: 0x4728, 0xe5: 0x472d, 0xe6: 0xa000, 0xe7: 0x4737, 0xe8: 0x32ee, 0xe9: 0x32f3,
	0xea: 0x473c, 0xeb: 0x3307, 0xec: 0x337f, 0xed: 0x3384, 0xee: 0x3389, 0xef: 0x4750,
	0xf1: 0x3415, 0xf2: 0x3438, 0xf3: 0x343d, 0xf4: 0x475a, 0xf5: 0x475f,
	0xf6: 0x476e, 0xf8: 0xa000, 0xf9: 0x34c9, 0xfa: 0x34ce, 0xfb: 0x34d3,
	0xfc: 0x47a0, 0xfd: 0x3550, 0xff: 0x3569,
	// Block 0x4, offset 0x100
	0x100: 0x2f7e, 0x101: 0x328a, 0x102: 0x468d, 0x103: 0x471e, 0x104: 0x2f9c, 0x105: 0x32a8,
	0x106: 0x2fb0, 0x107: 0x32bc, 0x108: 0x2fb5, 0x109: 0x32c1, 0x10a: 0x2fba, 0x10b: 0x32c6,
	0x10c: 0x2fbf, 0x10d: 0x32cb, 0x10e: 0x2fc9, 0x10f: 0x32d5,
	0x112: 0x46b0, 0x113: 0x4741, 0x114: 0x2ff1, 0x115: 0x32fd, 0x116: 0x2ff6, 0x117: 0x3302,
	0x118: 0x3014, 0x119: 0x3320, 0x11a: 0x3005, 0x11b: 0x3311, 0x11c: 0x302d, 0x11d: 0x3339,
	0x11e: 0x3037, 0x11f: 0x3343, 0x120: 0x303c, 0x121: 0x3348, 0x122: 0x3046, 0x123: 0x3352,
	0x124: 0x304b, 0x125: 0x3357, 0x128: 0x307d, 0x129: 0x338e,
	0x12a: 0x3082, 0x12b: 0x3393, 0x12c: 0x3087, 0x12d: 0x3398, 0x12e: 0x30aa, 0x12f: 0x33b6,
	0x130: 0x308c, 0x132: 0x195d, 0x133: 0x19e7, 0x134: 0x30b4, 0x135: 0x33c0,
	0x136: 0x30c8, 0x137: 0x33d9, 0x139: 0x30d2, 0x13a: 0x33e3, 0x13b: 0x30dc,
	0x13c: 0x33ed, 0x13d: 0x30d7, 0x13e: 0x33e8, 0x13f: 0x1bac,
	// Block 0x5, offset 0x140
	0x140: 0x1c34, 0x143: 0x30ff, 0x144: 0x3410, 0x145: 0x3118,
	0x146: 0x3429, 0x147: 0x310e, 0x148: 0x341f, 0x149: 0x1c5c,
	0x14c: 0x46d3, 0x14d: 0x4764, 0x14e: 0x3131, 0x14f: 0x3442, 0x150: 0x313b, 0x151: 0x344c,
	0x154: 0x3159, 0x155: 0x346a, 0x156: 0x3172, 0x157: 0x3483,
	0x158: 0x3163, 0x159: 0x3474, 0x15a: 0x46f6, 0x15b: 0x4787, 0x15c: 0x317c, 0x15d: 0x348d,
	0x15e: 0x318b, 0x15f: 0x349c, 0x160: 0x46fb, 0x161: 0x478c, 0x162: 0x31a4, 0x163: 0x34ba,
	0x164: 0x3195, 0x165: 0x34ab, 0x168: 0x4705, 0x169: 0x4796,
	0x16a: 0x470a, 0x16b: 0x479b, 0x16c: 0x31c2, 0x16d: 0x34d8, 0x16e: 0x31cc, 0x16f: 0x34e2,
	0x170: 0x31d1, 0x171: 0x34e7, 0x172: 0x31ef, 0x173: 0x3505, 0x174: 0x3212, 0x175: 0x3528,
	0x176: 0x323a, 0x177: 0x3555, 0x178: 0x324e, 0x179: 0x325d, 0x17a: 0x357d, 0x17b: 0x3267,
	0x17c: 0x3587, 0x17d: 0x326c, 0x17e: 0x358c, 0x17f: 0x00a7,
	// Block 0x6, offset 0x180
	0x184: 0x2dee, 0x185: 0x2df4,
	0x186: 0x2dfa, 0x187: 0x1972, 0x188: 0x1975, 0x189: 0x1a08, 0x18a: 0x1987, 0x18b: 0x198a,
	0x18c: 0x1a3e, 0x18d: 0x2f88, 0x18e: 0x3294, 0x18f: 0x3096, 0x190: 0x33a2, 0x191: 0x3140,
	0x192: 0x3451, 0x193: 0x31d6, 0x194: 0x34ec, 0x195: 0x39cf, 0x196: 0x3b5e, 0x197: 0x39c8,
	0x198: 0x3b57, 0x199: 0x39d6, 0x19a: 0x3b65, 0x19b: 0x39c1, 0x19c: 0x3b50,
	0x19e: 0x38b0, 0x19f: 0x3a3f, 0x1a0: 0x38a9, 0x1a1: 0x3a38, 0x1a2: 0x35b3, 0x1a3: 0x35c5,
	0x1a6: 0x3041, 0x1a7: 0x334d, 0x1a8: 0x30be, 0x1a9: 0x33cf,
	0x1aa: 0x46ec, 0x1ab: 0x477d, 0x1ac: 0x3990, 0x1ad: 0x3b1f, 0x1ae: 0x35d7, 0x1af: 0x35dd,
	0x1b0: 0x33c5, 0x1b1: 0x1942, 0x1b2: 0x1945, 0x1b3: 0x19cf, 0x1b4: 0x3028, 0x1b5: 0x3334,
	0x1b8: 0x30fa, 0x1b9: 0x340b, 0x1ba: 0x38b7, 0x1bb: 0x3a46,
	0x1bc: 0x35ad, 0x1bd: 0x35bf, 0x1be: 0x35b9, 0x1bf: 0x35cb,
	// Block 0x7, offset 0x1c0
	0x1c0: 0x2f8d, 0x1c1: 0x3299, 0x1c2: 0x2f92, 0x1c3: 0x329e, 0x1c4: 0x300a, 0x1c5: 0x3316,
	0x1c6: 0x300f, 0x1c7: 0x331b, 0x1c8: 0x309b, 0x1c9: 0x33a7, 0x1ca: 0x30a0, 0x1cb: 0x33ac,
	0x1cc: 0x3145, 0x1cd: 0x3456, 0x1ce: 0x314a, 0x1cf: 0x345b, 0x1d0: 0x3168, 0x1d1: 0x3479,
	0x1d2: 0x316d, 0x1d3: 0x347e, 0x1d4: 0x31db, 0x1d5: 0x34f1, 0x1d6: 0x31e0, 0x1d7: 0x34f6,
	0x1d8: 0x3186, 0x1d9: 0x3497, 0x1da: 0x319f, 0x1db: 0x34b5,
	0x1de: 0x305a, 0x1df: 0x3366,
	0x1e6: 0x4692, 0x1e7: 0x4723, 0x1e8: 0x46ba, 0x1e9: 0x474b,
	0x1ea: 0x395f, 0x1eb: 0x3aee, 0x1ec: 0x393c, 0x1ed: 0x3acb, 0x1ee: 0x46d8, 0x1ef: 0x4769,
	0x1f0: 0x3958, 0x1f1: 0x3ae7, 0x1f2: 0x3244, 0x1f3: 0x355f,
	// Block 0x8, offset 0x200
	0x200: 0x9932, 0x201: 0x9932, 0x202: 0x9932, 0x203: 0x9932, 0x204: 0x9932, 0x205: 0x8132,
	0x206: 0x9932, 0x207: 0x9932, 0x208: 0x9932, 0x209: 0x9932, 0x20a: 0x9932, 0x20b: 0x9932,
	0x20c: 0x9932, 0x20d: 0x8132, 0x20e: 0x8132, 0x20f: 0x9932, 0x210: 0x8132, 0x211: 0x9932,
	0x212: 0x8132, 0x213: 0x9932, 0x214: 0x9932, 0x215: 0x8133, 0x216: 0x812d, 0x217: 0x812d,
	0x218: 0x812d, 0x219: 0x812d, 0x21a: 0x8133, 0x21b: 0x992b, 0x21c: 0x812d, 0x21d: 0x812d,
	0x21e: 0x812d, 0x21f: 0x812d, 0x220: 0x812d, 0x221: 0x8129, 0x222: 0x8129, 0x223: 0x992d,
	0x224: 0x992d, 0x225: 0x992d, 0x226: 0x992d, 0x227: 0x9929, 0x228: 0x9929, 0x229: 0x812d,
	0x22a: 0x812d, 0x22b: 0x812d, 0x22c: 0x812d, 0x22d: 0x992d, 0x22e: 0x992d, 0x22f: 0x812d,
	0x230: 0x992d, 0x231: 0x992d, 0x232: 0x812d, 0x233: 0x812d, 0x234: 0x8101, 0x235: 0x8101,
	0x236: 0x8101, 0x237: 0x8101, 0x238: 0x9901, 0x239: 0x812d, 0x23a: 0x812d, 0x23b: 0x812d,
	0x23c: 0x812d, 0x23d: 0x8132, 0x23e: 0x8132, 0x23f: 0x8132,
	// Block 0x9, offset 0x240
	0x240: 0x49ae, 0x241: 0x49b3, 0x242: 0x9932, 0x243: 0x49b8, 0x244: 0x4a71, 0x245: 0x9936,
	0x246: 0x8132, 0x247: 0x812d, 0x248: 0x812d, 0x249: 0x812d, 0x24a: 0x8132, 0x24b: 0x8132,
	0x24c: 0x8132, 0x24d: 0x812d, 0x24e: 0x812d, 0x250: 0x8132, 0x251: 0x8132,
	0x252: 0x8132, 0x253: 0x812d, 0x254: 0x812d, 0x255: 0x812d, 0x256: 0x812d, 0x257: 0x8132,
	0x258: 0x8133, 0x259: 0x812d, 0x25a: 0x812d, 0x25b: 0x8132, 0x25c: 0x8134, 0x25d: 0x8135,
	0x25e: 0x8135, 0x25f: 0x8134, 0x260: 0x8135, 0x261: 0x8135, 0x262: 0x8134, 0x263: 0x8132,
	0x264: 0x8132, 0x265: 0x8132, 0x266: 0x8132, 0x267: 0x8132, 0x268: 0x8132, 0x269: 0x8132,
	0x26a: 0x8132, 0x26b: 0x8132, 0x26c: 0x8132, 0x26d: 0x8132, 0x26e: 0x8132, 0x26f: 0x8132,
	0x274: 0x0170,
	0x27a: 0x42a5,
	0x27e: 0x0037,
	// Block 0xa, offset 0x280
	0x284: 0x425a, 0x285: 0x447b,
	0x286: 0x35e9, 0x287: 0x00ce, 0x288: 0x3607, 0x289: 0x3613, 0x28a: 0x3625,
	0x28c: 0x3643, 0x28e: 0x3655, 0x28f: 0x3673, 0x290: 0x3e08, 0x291: 0xa000,
	0x295: 0xa000, 0x297: 0xa000,
	0x299: 0xa000,
	0x29f: 0xa000, 0x2a1: 0xa000,
	0x2a5: 0xa000, 0x2a9: 0xa000,
	0x2aa: 0x3637, 0x2ab: 0x3667, 0x2ac: 0x47fe, 0x2ad: 0x3697, 0x2ae: 0x4828, 0x2af: 0x36a9,
	0x2b0: 0x3e70, 0x2b1: 0xa000, 0x2b5: 0xa000,
	0x2b7: 0xa000, 0x2b9: 0xa000,
	0x2bf: 0xa000,
	// Block 0xb, offset 0x2c0
	0x2c1: 0xa000, 0x2c5: 0xa000,
	0x2c9: 0xa000, 0x2ca: 0x4840, 0x2cb: 0x485e,
	0x2cc: 0x36c7, 0x2cd: 0x36df, 0x2ce: 0x4876, 0x2d0: 0x01be, 0x2d1: 0x01d0,
	0x2d2: 0x01ac, 0x2d3: 0x430c, 0x2d4: 0x4312, 0x2d5: 0x01fa, 0x2d6: 0x01e8,
	0x2f0: 0x01d6, 0x2f1: 0x01eb, 0x2f2: 0x01ee, 0x2f4: 0x0188, 0x2f5: 0x01c7,
	0x2f9: 0x01a6,
	// Block 0xc, offset 0x300
	0x300: 0x3721, 0x301: 0x372d, 0x303: 0x371b,
	0x306: 0xa000, 0x307: 0x3709,
	0x30c: 0x375d, 0x30d: 0x3745, 0x30e: 0x376f, 0x310: 0xa000,
	0x313: 0xa000, 0x315: 0xa000, 0x316: 0xa000, 0x317: 0xa000,
	0x318: 0xa000, 0x319: 0x3751, 0x31a: 0xa000,
	0x31e: 0xa000, 0x323: 0xa000,
	0x327: 0xa000,
	0x32b: 0xa000, 0x32d: 0xa000,
	0x330: 0xa000, 0x333: 0xa000, 0x335: 0xa000,
	0x336: 0xa000, 0x337: 0xa000, 0x338: 0xa000, 0x339: 0x37d5, 0x33a: 0xa000,
	0x33e: 0xa000,
	// Block 0xd, offset 0x340
	0x341: 0x3733, 0x342: 0x37b7,
	0x350: 0x370f, 0x351: 0x3793,
	0x352: 0x3715, 0x353: 0x3799, 0x356: 0x3727, 0x357: 0x37ab,
	0x358: 0xa000, 0x359: 0xa000, 0x35a: 0x3829, 0x35b: 0x382f, 0x35c: 0x3739, 0x35d: 0x37bd,
	0x35e: 0x373f, 0x35f: 0x37c3, 0x362: 0x374b, 0x363: 0x37cf,
	0x364: 0x3757, 0x365: 0x37db, 0x366: 0x3763, 0x367: 0x37e7, 0x368: 0xa000, 0x369: 0xa000,
	0x36a: 0x3835, 0x36b: 0x383b, 0x36c: 0x378d, 0x36d: 0x3811, 0x36e: 0x3769, 0x36f: 0x37ed,
	0x370: 0x3775, 0x371: 0x37f9, 0x372: 0x377b, 0x373: 0x37ff, 0x374: 0x3781, 0x375: 0x3805,
	0x378: 0x3787, 0x379: 0x380b,
	// Block 0xe, offset 0x380
	0x387: 0x1d61,
	0x391: 0x812d,
	0x392: 0x8132, 0x393: 0x8132, 0x394: 0x8132, 0x395: 0x8132, 0x396: 0x812d, 0x397: 0x8132,
	0x398: 0x8132, 0x399: 0x8132, 0x39a: 0x812e, 0x39b: 0x812d, 0x39c: 0x8132, 0x39d: 0x8132,
	0x39e: 0x8132, 0x39f: 0x8132, 0x3a0: 0x8132, 0x3a1: 0x8132, 0x3a2: 0x812d, 0x3a3: 0x812d,
	0x3a4: 0x812d, 0x3a5: 0x812d, 0x3a6: 0x812d, 0x3a7: 0x812d, 0x3a8: 0x8132, 0x3a9: 0x8132,
	0x3aa: 0x812d, 0x3ab: 0x8132, 0x3ac: 0x8132, 0x3ad: 0x812e, 0x3ae: 0x8131, 0x3af: 0x8132,
	0x3b0: 0x8105, 0x3b1: 0x8106, 0x3b2: 0x8107, 0x3b3: 0x8108, 0x3b4: 0x8109, 0x3b5: 0x810a,
	0x3b6: 0x810b, 0x3b7: 0x810c, 0x3b8: 0x810d, 0x3b9: 0x810e, 0x3ba: 0x810e, 0x3bb: 0x810f,
	0x3bc: 0x8110, 0x3bd: 0x8111, 0x3bf: 0x8112,
	// Block 0xf, offset 0x3c0
	0x3c8: 0xa000, 0x3ca: 0xa000, 0x3cb: 0x8116,
	0x3cc: 0x8117, 0x3cd: 0x8118, 0x3ce: 0x8119, 0x3cf: 0x811a, 0x3d0: 0x811b, 0x3d1: 0x811c,
	0x3d2: 0x811d, 0x3d3: 0x9932, 0x3d4: 0x9932, 0x3d5: 0x992d, 0x3d6: 0x812d, 0x3d7: 0x8132,
	0x3d8: 0x8132, 0x3d9: 0x8132, 0x3da: 0x8132, 0x3db: 0x8132, 0x3dc: 0x812d, 0x3dd: 0x8132,
	0x3de: 0x8132, 0x3df: 0x812d,
	0x3f0: 0x811e, 0x3f5: 0x1d84,
	0x3f6: 0x2013, 0x3f7: 0x204f, 0x3f8: 0x204a,
	// Block 0x10, offset 0x400
	0x405: 0xa000,
	0x406: 0x2d26, 0x407: 0xa000, 0x408: 0x2d2e, 0x409: 0xa000, 0x40a: 0x2d36, 0x40b: 0xa000,
	0x40c: 0x2d3e, 0x40d: 0xa000, 0x40e: 0x2d46, 0x411: 0xa000,
	0x412: 0x2d4e,
	0x434: 0x8102, 0x435: 0x9900,
	0x43a: 0xa000, 0x43b: 0x2d56,
	0x43c: 0xa000, 0x43d: 0x2d5e, 0x43e: 0xa000, 0x43f: 0xa000,
	// Block 0x11, offset 0x440
	0x440: 0x0069, 0x441: 0x006b, 0x442: 0x006f, 0x443: 0x0083, 0x444: 0x00f5, 0x445: 0x00f8,
	0x446: 0x0413, 0x447: 0x0085, 0x448: 0x0089, 0x449: 0x008b, 0x44a: 0x0104, 0x44b: 0x0107,
	0x44c: 0x010a, 0x44d: 0x008f, 0x44f: 0x0097, 0x450: 0x009b, 0x451: 0x00e0,
	0x452: 0x009f, 0x453: 0x00fe, 0x454: 0x0417, 0x455: 0x041b, 0x456: 0x00a1, 0x457: 0x00a9,
	0x458: 0x00ab, 0x459: 0x0423, 0x45a: 0x012b, 0x45b: 0x00ad, 0x45c: 0x0427, 0x45d: 0x01be,
	0x45e: 0x01c1, 0x45f: 0x01c4, 0x460: 0x01fa, 0x461: 0x01fd, 0x462: 0x0093, 0x463: 0x00a5,
	0x464: 0x00ab, 0x465: 0x00ad, 0x466: 0x01be, 0x467: 0x01c1, 0x468: 0x01eb, 0x469: 0x01fa,
	0x46a: 0x01fd,
	0x478: 0x020c,
	// Block 0x12, offset 0x480
	0x49b: 0x00fb, 0x49c: 0x0087, 0x49d: 0x0101,
	0x49e: 0x00d4, 0x49f: 0x010a, 0x4a0: 0x008d, 0x4a1: 0x010d, 0x4a2: 0x0110, 0x4a3: 0x0116,
	0x4a4: 0x011c, 0x4a5: 0x011f, 0x4a6: 0x0122, 0x4a7: 0x042b, 0x4a8: 0x016a, 0x4a9: 0x0128,
	0x4aa: 0x042f, 0x4ab: 0x016d, 0x4ac: 0x0131, 0x4ad: 0x012e, 0x4ae: 0x0134, 0x4af: 0x0137,
	0x4b0: 0x013a, 0x4b1: 0x013d, 0x4b2: 0x0140, 0x4b3: 0x014c, 0x4b4: 0x014f, 0x4b5: 0x00ec,
	0x4b6: 0x0152, 0x4b7: 0x0155, 0x4b8: 0x041f, 0x4b9: 0x0158, 0x4ba: 0x015b, 0x4bb: 0x00b5,
	0x4bc: 0x015e, 0x4bd: 0x0161, 0x4be: 0x0164, 0x4bf: 0x01d0,
	// Block 0x13, offset 0x4c0
	0x4c0: 0x8132, 0x4c1: 0x8132, 0x4c2: 0x812d, 0x4c3: 0x8132, 0x4c4: 0x8132, 0x4c5: 0x8132,
	0x4c6: 0x8132, 0x4c7: 0x8132, 0x4c8: 0x8132, 0x4c9: 0x8132, 0x4ca: 0x812d, 0x4cb: 0x8132,
	0x4cc: 0x8132, 0x4cd: 0x8135, 0x4ce: 0x812a, 0x4cf: 0x812d, 0x4d0: 0x8129, 0x4d1: 0x8132,
	0x4d2: 0x8132, 0x4d3: 0x8132, 0x4d4: 0x8132, 0x4d5: 0x8132, 0x4d6: 0x8132, 0x4d7: 0x8132,
	0x4d8: 0x8132, 0x4d9: 0x8132, 0x4da: 0x8132, 0x4db: 0x8132, 0x4dc: 0x8132, 0x4dd: 0x8132,
	0x4de: 0x8132, 0x4df: 0x8132, 0x4e0: 0x8132, 0x4e1: 0x8132, 0x4e2: 0x8132, 0x4e3: 0x8132,
	0x4e4: 0x8132, 0x4e5: 0x8132, 0x4e6: 0x8132, 0x4e7: 0x8132, 0x4e8: 0x8132, 0x4e9: 0x8132,
	0x4ea: 0x8132, 0x4eb: 0x8132, 0x4ec: 0x8132, 0x4ed: 0x8132, 0x4ee: 0x8132, 0x4ef: 0x8132,
	0x4f0: 0x8132, 0x4f1: 0x8132, 0x4f2: 0x8132, 0x4f3: 0x8132, 0x4f4: 0x8132, 0x4f5: 0x8132,
	0x4f6: 0x8133, 0x4f7: 0x8131, 0x4f8: 0x8131, 0x4f9: 0x812d, 0x4fb: 0x8132,
	0x4fc: 0x8134, 0x4fd: 0x812d, 0x4fe: 0x8132, 0x4ff: 0x812d,
	// Block 0x14, offset 0x500
	0x500: 0x2f97, 0x501: 0x32a3, 0x502: 0x2fa1, 0x503: 0x32ad, 0x504: 0x2fa6, 0x505: 0x32b2,
	0x506: 0x2fab, 0x507: 0x32b7, 0x508: 0x38cc, 0x509: 0x3a5b, 0x50a: 0x2fc4, 0x50b: 0x32d0,
	0x50c: 0x2fce, 0x50d: 0x32da, 0x50e: 0x2fdd, 0x50f: 0x32e9, 0x510: 0x2fd3, 0x511: 0x32df,
	0x512: 0x2fd8, 0x513: 0x32e4, 0x514: 0x38ef, 0x515: 0x3a7e, 0x516: 0x38f6, 0x517: 0x3a85,
	0x518: 0x3019, 0x519: 0x3325, 0x51a: 0x301e, 0x51b: 0x332a, 0x51c: 0x3904, 0x51d: 0x3a93,
	0x51e: 0x3023, 0x51f: 0x332f, 0x520: 0x3032, 0x521: 0x333e, 0x522: 0x3050, 0x523: 0x335c,
	0x524: 0x305f, 0x525: 0x336b, 0x526: 0x3055, 0x527: 0x3361, 0x528: 0x3064, 0x529: 0x3370,
	0x52a: 0x3069, 0x52b: 0x3375, 0x52c: 0x30af, 0x52d: 0x33bb, 0x52e: 0x390b, 0x52f: 0x3a9a,
	0x530: 0x30b9, 0x531: 0x33ca, 0x532: 0x30c3, 0x533: 0x33d4, 0x534: 0x30cd, 0x535: 0x33de,
	0x536: 0x46c4, 0x537: 0x4755, 0x538: 0x3912, 0x539: 0x3aa1, 0x53a: 0x30e6, 0x53b: 0x33f7,
	0x53c: 0x30e1, 0x53d: 0x33f2, 0x53e: 0x30eb, 0x53f: 0x33fc,
	// Block 0x15, offset 0x540
	0x540: 0x30f0, 0x541: 0x3401, 0x542: 0x30f5, 0x543: 0x3406, 0x544: 0x3109, 0x545: 0x341a,
	0x546: 0x3113, 0x547: 0x3424, 0x548: 0x3122, 0x549: 0x3433, 0x54a: 0x311d, 0x54b: 0x342e,
	0x54c: 0x3935, 0x54d: 0x3ac4, 0x54e: 0x3943, 0x54f: 0x3ad2, 0x550: 0x394a, 0x551: 0x3ad9,
	0x552: 0x3951, 0x553: 0x3ae0, 0x554: 0x314f, 0x555: 0x3460, 0x556: 0x3154, 0x557: 0x3465,
	0x558: 0x315e, 0x559: 0x346f, 0x55a: 0x46f1, 0x55b: 0x4782, 0x55c: 0x3997, 0x55d: 0x3b26,
	0x55e: 0x3177, 0x55f: 0x3488, 0x560: 0x3181, 0x561: 0x3492, 0x562: 0x4700, 0x563: 0x4791,
	0x564: 0x399e, 0x565: 0x3b2d, 0x566: 0x39a5, 0x567: 0x3b34, 0x568: 0x39ac, 0x569: 0x3b3b,
	0x56a: 0x3190, 0x56b: 0x34a1, 0x56c: 0x319a, 0x56d: 0x34b0, 0x56e: 0x31ae, 0x56f: 0x34c4,
	0x570: 0x31a9, 0x571: 0x34bf, 0x572: 0x31ea, 0x573: 0x3500, 0x574: 0x31f9, 0x575: 0x350f,
	0x576: 0x31f4, 0x577: 0x350a, 0x578: 0x39b3, 0x579: 0x3b42, 0x57a: 0x39ba, 0x57b: 0x3b49,
	0x57c: 0x31fe, 0x57d: 0x3514, 0x57e: 0x3203, 0x57f: 0x3519,
	// Block 0x16, offset 0x580
	0x580: 0x3208, 0x581: 0x351e, 0x582: 0x320d, 0x583: 0x3523, 0x584: 0x321c, 0x585: 0x3532,
	0x586: 0x3217, 0x587: 0x352d, 0x588: 0x3221, 0x589: 0x353c, 0x58a: 0x3226, 0x58b: 0x3541,
	0x58c: 0x322b, 0x58d: 0x3546, 0x58e: 0x3249, 0x58f: 0x3564, 0x590: 0x3262, 0x591: 0x3582,
	0x592: 0x3271, 0x593: 0x3591, 0x594: 0x3276, 0x595: 0x3596, 0x596: 0x337a, 0x597: 0x34a6,
	0x598: 0x3537, 0x599: 0x3573, 0x59a: 0x1be0, 0x59b: 0x42d7,
	0x5a0: 0x46a1, 0x5a1: 0x4732, 0x5a2: 0x2f83, 0x5a3: 0x328f,
	0x5a4: 0x3878, 0x5a5: 0x3a07, 0x5a6: 0x3871, 0x5a7: 0x3a00, 0x5a8: 0x3886, 0x5a9: 0x3a15,
	0x5aa: 0x387f, 0x5ab: 0x3a0e, 0x5ac: 0x38be, 0x5ad: 0x3a4d, 0x5ae: 0x3894, 0x5af: 0x3a23,
	0x5b0: 0x388d, 0x5b1: 0x3a1c, 0x5b2: 0x38a2, 0x5b3: 0x3a31, 0x5b4: 0x389b, 0x5b5: 0x3a2a,
	0x5b6: 0x38c5, 0x5b7: 0x3a54, 0x5b8: 0x46b5, 0x5b9: 0x4746, 0x5ba: 0x3000, 0x5bb: 0x330c,
	0x5bc: 0x2fec, 0x5bd: 0x32f8, 0x5be: 0x38da, 0x5bf: 0x3a69,
	// Block 0x17, offset 0x5c0
	0x5c0: 0x38d3, 0x5c1: 0x3a62, 0x5c2: 0x38e8, 0x5c3: 0x3a77, 0x5c4: 0x38e1, 0x5c5: 0x3a70,
	0x5c6: 0x38fd, 0x5c7: 0x3a8c, 0x5c8: 0x3091, 0x5c9: 0x339d, 0x5ca: 0x30a5, 0x5cb: 0x33b1,
	0x5cc: 0x46e7, 0x5cd: 0x4778, 0x5ce: 0x3136, 0x5cf: 0x3447, 0x5d0: 0x3920, 0x5d1: 0x3aaf,
	0x5d2: 0x3919, 0x5d3: 0x3aa8, 0x5d4: 0x392e, 0x5d5: 0x3abd, 0x5d6: 0x3927, 0x5d7: 0x3ab6,
	0x5d8: 0x3989, 0x5d9: 0x3b18, 0x5da: 0x396d, 0x5db: 0x3afc, 0x5dc: 0x3966, 0x5dd: 0x3af5,
	0x5de: 0x397b, 0x5df: 0x3b0a, 0x5e0: 0x3974, 0x5e1: 0x3b03, 0x5e2: 0x3982, 0x5e3: 0x3b11,
	0x5e4: 0x31e5, 0x5e5: 0x34fb, 0x5e6: 0x31c7, 0x5e7: 0x34dd, 0x5e8: 0x39e4, 0x5e9: 0x3b73,
	0x5ea: 0x39dd, 0x5eb: 0x3b6c, 0x5ec: 0x39f2, 0x5ed: 0x3b81, 0x5ee: 0x39eb, 0x5ef: 0x3b7a,
	0x5f0: 0x39f9, 0x5f1: 0x3b88, 0x5f2: 0x3230, 0x5f3: 0x354b, 0x5f4: 0x3258, 0x5f5: 0x3578,
	0x5f6: 0x3253, 0x5f7: 0x356e, 0x5f8: 0x323f, 0x5f9: 0x355a,
	// Block 0x18, offset 0x600
	0x600: 0x4804, 0x601: 0x480a, 0x602: 0x491e, 0x603: 0x4936, 0x604: 0x4926, 0x605: 0x493e,
	0x606: 0x492e, 0x607: 0x4946, 0x608: 0x47aa, 0x609: 0x47b0, 0x60a: 0x488e, 0x60b: 0x48a6,
	0x60c: 0x4896, 0x60d: 0x48ae, 0x60e: 0x489e, 0x60f: 0x48b6, 0x610: 0x4816, 0x611: 0x481c,
	0x612: 0x3db8, 0x613: 0x3dc8, 0x614: 0x3dc0, 0x615: 0x3dd0,
	0x618: 0x47b6, 0x619: 0x47bc, 0x61a: 0x3ce8, 0x61b: 0x3cf8, 0x61c: 0x3cf0, 0x61d: 0x3d00,
	0x620: 0x482e, 0x621: 0x4834, 0x622: 0x494e, 0x623: 0x4966,
	0x624: 0x4956, 0x625: 0x496e, 0x626: 0x495e, 0x627: 0x4976, 0x628: 0x47c2, 0x629: 0x47c8,
	0x62a: 0x48be, 0x62b: 0x48d6, 0x62c: 0x48c6, 0x62d: 0x48de, 0x62e: 0x48ce, 0x62f: 0x48e6,
	0x630: 0x4846, 0x631: 0x484c, 0x632: 0x3e18, 0x633: 0x3e30, 0x634: 0x3e20, 0x635: 0x3e38,
	0x636: 0x3e28, 0x637: 0x3e40, 0x638: 0x47ce, 0x639: 0x47d4, 0x63a: 0x3d18, 0x63b: 0x3d30,
	0x63c: 0x3d20, 0x63d: 0x3d38, 0x63e: 0x3d28, 0x63f: 0x3d40,
	// Block 0x19, offset 0x640
	0x640: 0x4852, 0x641: 0x4858, 0x642: 0x3e48, 0x643: 0x3e58, 0x644: 0x3e50, 0x645: 0x3e60,
	0x648: 0x47da, 0x649: 0x47e0, 0x64a: 0x3d48, 0x64b: 0x3d58,
	0x64c: 0x3d50, 0x64d: 0x3d60, 0x650: 0x4864, 0x651: 0x486a,
	0x652: 0x3e80, 0x653: 0x3e98, 0x654: 0x3e88, 0x655: 0x3ea0, 0x656: 0x3e90, 0x657: 0x3ea8,
	0x659: 0x47e6, 0x65b: 0x3d68, 0x65d: 0x3d70,
	0x65f: 0x3d78, 0x660: 0x487c, 0x661: 0x4882, 0x662: 0x497e, 0x663: 0x4996,
	0x664: 0x4986, 0x665: 0x499e, 0x666: 0x498e, 0x667: 0x49a6, 0x668: 0x47ec, 0x669: 0x47f2,
	0x66a: 0x48ee, 0x66b: 0x4906, 0x66c: 0x48f6, 0x66d: 0x490e, 0x66e: 0x48fe, 0x66f: 0x4916,
	0x670: 0x47f8, 0x671: 0x431e, 0x672: 0x3691, 0x673: 0x4324, 0x674: 0x4822, 0x675: 0x432a,
	0x676: 0x36a3, 0x677: 0x4330, 0x678: 0x36c1, 0x679: 0x4336, 0x67a: 0x36d9, 0x67b: 0x433c,
	0x67c: 0x4870, 0x67d: 0x4342,
	// Block 0x1a, offset 0x680
	0x680: 0x3da0, 0x681: 0x3da8, 0x682: 0x4184, 0x683: 0x41a2, 0x684: 0x418e, 0x685: 0x41ac,
	0x686: 0x4198, 0x687: 0x41b6, 0x688: 0x3cd8, 0x689: 0x3ce0, 0x68a: 0x40d0, 0x68b: 0x40ee,
	0x68c: 0x40da, 0x68d: 0x40f8, 0x68e: 0x40e4, 0x68f: 0x4102, 0x690: 0x3de8, 0x691: 0x3df0,
	0x692: 0x41c0, 0x693: 0x41de, 0x694: 0x41ca, 0x695: 0x41e8, 0x696: 0x41d4, 0x697: 0x41f2,
	0x698: 0x3d08, 0x699: 0x3d10, 0x69a: 0x410c, 0x69b: 0x412a, 0x69c: 0x4116, 0x69d: 0x4134,
	0x69e: 0x4120, 0x69f: 0x413e, 0x6a0: 0x3ec0, 0x6a1: 0x3ec8, 0x6a2: 0x41fc, 0x6a3: 0x421a,
	0x6a4: 0x4206, 0x6a5: 0x4224, 0x6a6: 0x4210, 0x6a7: 0x422e, 0x6a8: 0x3d80, 0x6a9: 0x3d88,
	0x6aa: 0x4148, 0x6ab: 0x4166, 0x6ac: 0x4152, 0x6ad: 0x4170, 0x6ae: 0x415c, 0x6af: 0x417a,
	0x6b0: 0x3685, 0x6b1: 0x367f, 0x6b2: 0x3d90, 0x6b3: 0x368b, 0x6b4: 0x3d98,
	0x6b6: 0x4810, 0x6b7: 0x3db0, 0x6b8: 0x35f5, 0x6b9: 0x35ef, 0x6ba: 0x35e3, 0x6bb: 0x42ee,
	0x6bc: 0x35fb, 0x6bd: 0x4287, 0x6be: 0x01d3, 0x6bf: 0x4287,
	// Block 0x1b, offset 0x6c0
	0x6c0: 0x42a0, 0x6c1: 0x4482, 0x6c2: 0x3dd8, 0x6c3: 0x369d, 0x6c4: 0x3de0,
	0x6c6: 0x483a, 0x6c7: 0x3df8, 0x6c8: 0x3601, 0x6c9: 0x42f4, 0x6ca: 0x360d, 0x6cb: 0x42fa,
	0x6cc: 0x3619, 0x6cd: 0x4489, 0x6ce: 0x4490, 0x6cf: 0x4497, 0x6d0: 0x36b5, 0x6d1: 0x36af,
	0x6d2: 0x3e00, 0x6d3: 0x44e4, 0x6d6: 0x36bb, 0x6d7: 0x3e10,
	0x6d8: 0x3631, 0x6d9: 0x362b, 0x6da: 0x361f, 0x6db: 0x4300, 0x6dd: 0x449e,
	0x6de: 0x44a5, 0x6df: 0x44ac, 0x6e0: 0x36eb, 0x6e1: 0x36e5, 0x6e2: 0x3e68, 0x6e3: 0x44ec,
	0x6e4: 0x36cd, 0x6e5: 0x36d3, 0x6e6: 0x36f1, 0x6e7: 0x3e78, 0x6e8: 0x3661, 0x6e9: 0x365b,
	0x6ea: 0x364f, 0x6eb: 0x430c, 0x6ec: 0x3649, 0x6ed: 0x4474, 0x6ee: 0x447b, 0x6ef: 0x0081,
	0x6f2: 0x3eb0, 0x6f3: 0x36f7, 0x6f4: 0x3eb8,
	0x6f6: 0x4888, 0x6f7: 0x3ed0, 0x6f8: 0x363d, 0x6f9: 0x4306, 0x6fa: 0x366d, 0x6fb: 0x4318,
	0x6fc: 0x3679, 0x6fd: 0x425a, 0x6fe: 0x428c,
	// Block 0x1c, offset 0x700
	0x700: 0x1bd8, 0x701: 0x1bdc, 0x702: 0x0047, 0x703: 0x1c54, 0x705: 0x1be8,
	0x706: 0x1bec, 0x707: 0x00e9, 0x709: 0x1c58, 0x70a: 0x008f, 0x70b: 0x0051,
	0x70c: 0x0051, 0x70d: 0x0051, 0x70e: 0x0091, 0x70f: 0x00da, 0x710: 0x0053, 0x711: 0x0053,
	0x712: 0x0059, 0x713: 0x0099, 0x715: 0x005d, 0x716: 0x198d,
	0x719: 0x0061, 0x71a: 0x0063, 0x71b: 0x0065, 0x71c: 0x0065, 0x71d: 0x0065,
	0x720: 0x199f, 0x721: 0x1bc8, 0x722: 0x19a8,
	0x724: 0x0075, 0x726: 0x01b8, 0x728: 0x0075,
	0x72a: 0x0057, 0x72b: 0x42d2, 0x72c: 0x0045, 0x72d: 0x0047, 0x72f: 0x008b,
	0x730: 0x004b, 0x731: 0x004d, 0x733: 0x005b, 0x734: 0x009f, 0x735: 0x0215,
	0x736: 0x0218, 0x737: 0x021b, 0x738: 0x021e, 0x739: 0x0093, 0x73b: 0x1b98,
	0x73c: 0x01e8, 0x73d: 0x01c1, 0x73e: 0x0179, 0x73f: 0x01a0,
	// Block 0x1d, offset 0x740
	0x740: 0x0463, 0x745: 0x0049,
	0x746: 0x0089, 0x747: 0x008b, 0x748: 0x0093, 0x749: 0x0095,
	0x750: 0x222e, 0x751: 0x223a,
	0x752: 0x22ee, 0x753: 0x2216, 0x754: 0x229a, 0x755: 0x2222, 0x756: 0x22a0, 0x757: 0x22b8,
	0x758: 0x22c4, 0x759: 0x2228, 0x75a: 0x22ca, 0x75b: 0x2234, 0x75c: 0x22be, 0x75d: 0x22d0,
	0x75e: 0x22d6, 0x75f: 0x1cbc, 0x760: 0x0053, 0x761: 0x195a, 0x762: 0x1ba4, 0x763: 0x1963,
	0x764: 0x006d, 0x765: 0x19ab, 0x766: 0x1bd0, 0x767: 0x1d48, 0x768: 0x1966, 0x769: 0x0071,
	0x76a: 0x19b7, 0x76b: 0x1bd4, 0x76c: 0x0059, 0x76d: 0x0047, 0x76e: 0x0049, 0x76f: 0x005b,
	0x770: 0x0093, 0x771: 0x19e4, 0x772: 0x1c18, 0x773: 0x19ed, 0x774: 0x00ad, 0x775: 0x1a62,
	0x776: 0x1c4c, 0x777: 0x1d5c, 0x778: 0x19f0, 0x779: 0x00b1, 0x77a: 0x1a65, 0x77b: 0x1c50,
	0x77c: 0x0099, 0x77d: 0x0087, 0x77e: 0x0089, 0x77f: 0x009b,
	// Block 0x1e, offset 0x780
	0x781: 0x3c06, 0x783: 0xa000, 0x784: 0x3c0d, 0x785: 0xa000,
	0x787: 0x3c14, 0x788: 0xa000, 0x789: 0x3c1b,
	0x78d: 0xa000,
	0x7a0: 0x2f65, 0x7a1: 0xa000, 0x7a2: 0x3c29,
	0x7a4: 0xa000, 0x7a5: 0xa000,
	0x7ad: 0x3c22, 0x7ae: 0x2f60, 0x7af: 0x2f6a,
	0x7b0: 0x3c30, 0x7b1: 0x3c37, 0x7b2: 0xa000, 0x7b3: 0xa000, 0x7b4: 0x3c3e, 0x7b5: 0x3c45,
	0x7b6: 0xa000, 0x7b7: 0xa000, 0x7b8: 0x3c4c, 0x7b9: 0x3c53, 0x7ba: 0xa000, 0x7bb: 0xa000,
	0x7bc: 0xa000, 0x7bd: 0xa000,
	// Block 0x1f, offset 0x7c0
	0x7c0: 0x3c5a, 0x7c1: 0x3c61, 0x7c2: 0xa000, 0x7c3: 0xa000, 0x7c4: 0x3c76, 0x7c5: 0x3c7d,
	0x7c6: 0xa000, 0x7c7: 0xa000, 0x7c8: 0x3c84, 0x7c9: 0x3c8b,
	0x7d1: 0xa000,
	0x7d2: 0xa000,
	0x7e2: 0xa000,
	0x7e8: 0xa000, 0x7e9: 0xa000,
	0x7eb: 0xa000, 0x7ec: 0x3ca0, 0x7ed: 0x3ca7, 0x7ee: 0x3cae, 0x7ef: 0x3cb5,
	0x7f2: 0xa000, 0x7f3: 0xa000, 0x7f4: 0xa000, 0x7f5: 0xa000,
	// Block 0x20, offset 0x800
	0x820: 0x0023, 0x821: 0x0025, 0x822: 0x0027, 0x823: 0x0029,
	0x824: 0x002b, 0x825: 0x002d, 0x826: 0x002f, 0x827: 0x0031, 0x828: 0x0033, 0x829: 0x1882,
	0x82a: 0x1885, 0x82b: 0x1888, 0x82c: 0x188b, 0x82d: 0x188e, 0x82e: 0x1891, 0x82f: 0x1894,
	0x830: 0x1897, 0x831: 0x189a, 0x832: 0x189d, 0x833: 0x18a6, 0x834: 0x1a68, 0x835: 0x1a6c,
	0x836: 0x1a70, 0x837: 0x1a74, 0x838: 0x1a78, 0x839: 0x1a7c, 0x83a: 0x1a80, 0x83b: 0x1a84,
	0x83c: 0x1a88, 0x83d: 0x1c80, 0x83e: 0x1c85, 0x83f: 0x1c8a,
	// Block 0x21, offset 0x840
	0x840: 0x1c8f, 0x841: 0x1c94, 0x842: 0x1c99, 0x843: 0x1c9e, 0x844: 0x1ca3, 0x845: 0x1ca8,
	0x846: 0x1cad, 0x847: 0x1cb2, 0x848: 0x187f, 0x849: 0x18a3, 0x84a: 0x18c7, 0x84b: 0x18eb,
	0x84c: 0x190f, 0x84d: 0x1918, 0x84e: 0x191e, 0x84f: 0x1924, 0x850: 0x192a, 0x851: 0x1b60,
	0x852: 0x1b64, 0x853: 0x1b68, 0x854: 0x1b6c, 0x855: 0x1b70, 0x856: 0x1b74, 0x857: 0x1b78,
	0x858: 0x1b7c, 0x859: 0x1b80, 0x85a: 0x1b84, 0x85b: 0x1b88, 0x85c: 0x1af4, 0x85d: 0x1af8,
	0x85e: 0x1afc, 0x85f: 0x1b00, 0x860: 0x1b04, 0x861: 0x1b08, 0x862: 0x1b0c, 0x863: 0x1b10,
	0x864: 0x1b14, 0x865: 0x1b18, 0x866: 0x1b1c, 0x867: 0x1b20, 0x868: 0x1b24, 0x869: 0x1b28,
	0x86a: 0x1b2c, 0x86b: 0x1b30, 0x86c: 0x1b34, 0x86d: 0x1b38, 0x86e: 0x1b3c, 0x86f: 0x1b40,
	0x870: 0x1b44, 0x871: 0x1b48, 0x872: 0x1b4c, 0x873: 0x1b50, 0x874: 0x1b54, 0x875: 0x1b58,
	0x876: 0x0043, 0x877: 0x0045, 0x878: 0x0047, 0x879: 0x0049, 0x87a: 0x004b, 0x87b: 0x004d,
	0x87c: 0x004f, 0x87d: 0x0051, 0x87e: 0x0053, 0x87f: 0x0055,
	// Block 0x22, offset 0x880
	0x880: 0x06bf, 0x881: 0x06e3, 0x882: 0x06ef, 0x883: 0x06ff, 0x884: 0x0707, 0x885: 0x0713,
	0x886: 0x071b, 0x887: 0x0723, 0x888: 0x072f, 0x889: 0x0783, 0x88a: 0x079b, 0x88b: 0x07ab,
	0x88c: 0x07bb, 0x88d: 0x07cb, 0x88e: 0x07db, 0x88f: 0x07fb, 0x890: 0x07ff, 0x891: 0x0803,
	0x892: 0x0837, 0x893: 0x085f, 0x894: 0x086f, 0x895: 0x0877, 0x896: 0x087b, 0x897: 0x0887,
	0x898: 0x08a3, 0x899: 0x08a7, 0x89a: 0x08bf, 0x89b: 0x08c3, 0x89c: 0x08cb, 0x89d: 0x08db,
	0x89e: 0x0977, 0x89f: 0x098b, 0x8a0: 0x09cb, 0x8a1: 0x09df, 0x8a2: 0x09e7, 0x8a3: 0x09eb,
	0x8a4: 0x09fb, 0x8a5: 0x0a17, 0x8a6: 0x0a43, 0x8a7: 0x0a4f, 0x8a8: 0x0a6f, 0x8a9: 0x0a7b,
	0x8aa: 0x0a7f, 0x8ab: 0x0a83, 0x8ac: 0x0a9b, 0x8ad: 0x0a9f, 0x8ae: 0x0acb, 0x8af: 0x0ad7,
	0x8b0: 0x0adf, 0x8b1: 0x0ae7, 0x8b2: 0x0af7, 0x8b3: 0x0aff, 0x8b4: 0x0b07, 0x8b5: 0x0b33,
	0x8b6: 0x0b37, 0x8b7: 0x0b3f, 0x8b8: 0x0b43, 0x8b9: 0x0b4b, 0x8ba: 0x0b53, 0x8bb: 0x0b63,
	0x8bc: 0x0b7f, 0x8bd: 0x0bf7, 0x8be: 0x0c0b, 0x8bf: 0x0c0f,
	// Block 0x23, offset 0x8c0
	0x8c0: 0x0c8f, 0x8c1: 0x0c93, 0x8c2: 0x0ca7, 0x8c3: 0x0cab, 0x8c4: 0x0cb3, 0x8c5: 0x0cbb,
	0x8c6: 0x0cc3, 0x8c7: 0x0ccf, 0x8c8: 0x0cf7, 0x8c9: 0x0d07, 0x8ca: 0x0d1b, 0x8cb: 0x0d8b,
	0x8cc: 0x0d97, 0x8cd: 0x0da7, 0x8ce: 0x0db3, 0x8cf: 0x0dbf, 0x8d0: 0x0dc7, 0x8d1: 0x0dcb,
	0x8d2: 0x0dcf, 0x8d3: 0x0dd3, 0x8d4: 0x0dd7, 0x8d5: 0x0e8f, 0x8d6: 0x0ed7, 0x8d7: 0x0ee3,
	0x8d8: 0x0ee7, 0x8d9: 0x0eeb, 0x8da: 0x0eef, 0x8db: 0x0ef7, 0x8dc: 0x0efb, 0x8dd: 0x0f0f,
	0x8de: 0x0f2b, 0x8df: 0x0f33, 0x8e0: 0x0f73, 0x8e1: 0x0f77, 0x8e2: 0x0f7f, 0x8e3: 0x0f83,
	0x8e4: 0x0f8b, 0x8e5: 0x0f8f, 0x8e6: 0x0fb3, 0x8e7: 0x0fb7, 0x8e8: 0x0fd3, 0x8e9: 0x0fd7,
	0x8ea: 0x0fdb, 0x8eb: 0x0fdf, 0x8ec: 0x0ff3, 0x8ed: 0x1017, 0x8ee: 0x101b, 0x8ef: 0x101f,
	0x8f0: 0x1043, 0x8f1: 0x1083, 0x8f2: 0x1087, 0x8f3: 0x10a7, 0x8f4: 0x10b7, 0x8f5: 0x10bf,
	0x8f6: 0x10df, 0x8f7: 0x1103, 0x8f8: 0x1147, 0x8f9: 0x114f, 0x8fa: 0x1163, 0x8fb: 0x116f,
	0x8fc: 0x1177, 0x8fd: 0x117f, 0x8fe: 0x1183, 0x8ff: 0x1187,
	// Block 0x24, offset 0x900
	0x900: 0x119f, 0x901: 0x11a3, 0x902: 0x11bf, 0x903: 0x11c7, 0x904: 0x11cf, 0x905: 0x11d3,
	0x906: 0x11df, 0x907: 0x11e7, 0x908: 0x11eb, 0x909: 0x11ef, 0x90a: 0x11f7, 0x90b: 0x11fb,
	0x90c: 0x129b, 0x90d: 0x12af, 0x90e: 0x12e3, 0x90f: 0x12e7, 0x910: 0x12ef, 0x911: 0x131b,
	0x912: 0x1323, 0x913: 0x132b, 0x914: 0x1333, 0x915: 0x136f, 0x916: 0x1373, 0x917: 0x137b,
	0x918: 0x137f, 0x919: 0x1383, 0x91a: 0x13af, 0x91b: 0x13b3, 0x91c: 0x13bb, 0x91d: 0x13cf,
	0x91e: 0x13d3, 0x91f: 0x13ef, 0x920: 0x13f7, 0x921: 0x13fb, 0x922: 0x141f, 0x923: 0x143f,
	0x924: 0x1453, 0x925: 0x1457, 0x926: 0x145f, 0x927: 0x148b, 0x928: 0x148f, 0x929: 0x149f,
	0x92a: 0x14c3, 0x92b: 0x14cf, 0x92c: 0x14df, 0x92d: 0x14f7, 0x92e: 0x14ff, 0x92f: 0x1503,
	0x930: 0x1507, 0x931: 0x150b, 0x932: 0x1517, 0x933: 0x151b, 0x934: 0x1523, 0x935: 0x153f,
	0x936: 0x1543, 0x937: 0x1547, 0x938: 0x155f, 0x939: 0x1563, 0x93a: 0x156b, 0x93b: 0x157f,
	0x93c: 0x1583, 0x93d: 0x1587, 0x93e: 0x158f, 0x93f: 0x1593,
	// Block 0x25, offset 0x940
	0x946: 0xa000, 0x94b: 0xa000,
	0x94c: 0x3f08, 0x94d: 0xa000, 0x94e: 0x3f10, 0x94f: 0xa000, 0x950: 0x3f18, 0x951: 0xa000,
	0x952: 0x3f20, 0x953: 0xa000, 0x954: 0x3f28, 0x955: 0xa000, 0x956: 0x3f30, 0x957: 0xa000,
	0x958: 0x3f38, 0x959: 0xa000, 0x95a: 0x3f40, 0x95b: 0xa000, 0x95c: 0x3f48, 0x95d: 0xa000,
	0x95e: 0x3f50, 0x95f: 0xa000, 0x960: 0x3f58, 0x961: 0xa000, 0x962: 0x3f60,
	0x964: 0xa000, 0x965: 0x3f68, 0x966: 0xa000, 0x967: 0x3f70, 0x968: 0xa000, 0x969: 0x3f78,
	0x96f: 0xa000,
	0x970: 0x3f80, 0x971: 0x3f88, 0x972: 0xa000, 0x973: 0x3f90, 0x974: 0x3f98, 0x975: 0xa000,
	0x976: 0x3fa0, 0x977: 0x3fa8, 0x978: 0xa000, 0x979: 0x3fb0, 0x97a: 0x3fb8, 0x97b: 0xa000,
	0x97c: 0x3fc0, 0x97d: 0x3fc8,
	// Block 0x26, offset 0x980
	0x994: 0x3f00,
	0x999: 0x9903, 0x99a: 0x9903, 0x99b: 0x42dc, 0x99c: 0x42e2, 0x99d: 0xa000,
	0x99e: 0x3fd0, 0x99f: 0x26b4,
	0x9a6: 0xa000,
	0x9ab: 0xa000, 0x9ac: 0x3fe0, 0x9ad: 0xa000, 0x9ae: 0x3fe8, 0x9af: 0xa000,
	0x9b0: 0x3ff0, 0x9b1: 0xa000, 0x9b2: 0x3ff8, 0x9b3: 0xa000, 0x9b4: 0x4000, 0x9b5: 0xa000,
	0x9b6: 0x4008, 0x9b7: 0xa000, 0x9b8: 0x4010, 0x9b9: 0xa000, 0x9ba: 0x4018, 0x9bb: 0xa000,
	0x9bc: 0x4020, 0x9bd: 0xa000, 0x9be: 0x4028, 0x9bf: 0xa000,
	// Block 0x27, offset 0x9c0
	0x9c0: 0x4030, 0x9c1: 0xa000, 0x9c2: 0x4038, 0x9c4: 0xa000, 0x9c5: 0x4040,
	0x9c6: 0xa000, 0x9c7: 0x4048, 0x9c8: 0xa000, 0x9c9: 0x4050,
	0x9cf: 0xa000, 0x9d0: 0x4058, 0x9d1: 0x4060,
	0x9d2: 0xa000, 0x9d3: 0x4068, 0x9d4: 0x4070, 0x9d5: 0xa000, 0x9d6: 0x4078, 0x9d7: 0x4080,
	0x9d8: 0xa000, 0x9d9: 0x4088, 0x9da: 0x4090, 0x9db: 0xa000, 0x9dc: 0x4098, 0x9dd: 0x40a0,
	0x9ef: 0xa000,
	0x9f0: 0xa000, 0x9f1: 0xa000, 0x9f2: 0xa000, 0x9f4: 0x3fd8,
	0x9f7: 0x40a8, 0x9f8: 0x40b0, 0x9f9: 0x40b8, 0x9fa: 0x40c0,
	0x9fd: 0xa000, 0x9fe: 0x40c8, 0x9ff: 0x26c9,
	// Block 0x28, offset 0xa00
	0xa00: 0x0367, 0xa01: 0x032b, 0xa02: 0x032f, 0xa03: 0x0333, 0xa04: 0x037b, 0xa05: 0x0337,
	0xa06: 0x033b, 0xa07: 0x033f, 0xa08: 0x0343, 0xa09: 0x0347, 0xa0a: 0x034b, 0xa0b: 0x034f,
	0xa0c: 0x0353, 0xa0d: 0x0357, 0xa0e: 0x035b, 0xa0f: 0x49bd, 0xa10: 0x49c3, 0xa11: 0x49c9,
	0xa12: 0x49cf, 0xa13: 0x49d5, 0xa14: 0x49db, 0xa15: 0x49e1, 0xa16: 0x49e7, 0xa17: 0x49ed,
	0xa18: 0x49f3, 0xa19: 0x49f9, 0xa1a: 0x49ff, 0xa1b: 0x4a05, 0xa1c: 0x4a0b, 0xa1d: 0x4a11,
	0xa1e: 0x4a17, 0xa1f: 0x4a1d, 0xa20: 0x4a23, 0xa21: 0x4a29, 0xa22: 0x4a2f, 0xa23: 0x4a35,
	0xa24: 0x03c3, 0xa25: 0x035f, 0xa26: 0x0363, 0xa27: 0x03e7, 0xa28: 0x03eb, 0xa29: 0x03ef,
	0xa2a: 0x03f3, 0xa2b: 0x03f7, 0xa2c: 0x03fb, 0xa2d: 0x03ff, 0xa2e: 0x036b, 0xa2f: 0x0403,
	0xa30: 0x0407, 0xa31: 0x036f, 0xa32: 0x0373, 0xa33: 0x0377, 0xa34: 0x037f, 0xa35: 0x0383,
	0xa36: 0x0387, 0xa37: 0x038b, 0xa38: 0x038f, 0xa39: 0x0393, 0xa3a: 0x0397, 0xa3b: 0x039b,
	0xa3c: 0x039f, 0xa3d: 0x03a3, 0xa3e: 0x03a7, 0xa3f: 0x03ab,
	// Block 0x29, offset 0xa40
	0xa40: 0x03af, 0xa41: 0x03b3, 0xa42: 0x040b, 0xa43: 0x040f, 0xa44: 0x03b7, 0xa45: 0x03bb,
	0xa46: 0x03bf, 0xa47: 0x03c7, 0xa48: 0x03cb, 0xa49: 0x03cf, 0xa4a: 0x03d3, 0xa4b: 0x03d7,
	0xa4c: 0x03db, 0xa4d: 0x03df, 0xa4e: 0x03e3,
	0xa52: 0x06bf, 0xa53: 0x071b, 0xa54: 0x06cb, 0xa55: 0x097b, 0xa56: 0x06cf, 0xa57: 0x06e7,
	0xa58: 0x06d3, 0xa59: 0x0f93, 0xa5a: 0x0707, 0xa5b: 0x06db, 0xa5c: 0x06c3, 0xa5d: 0x09ff,
	0xa5e: 0x098f, 0xa5f: 0x072f,
	// Block 0x2a, offset 0xa80
	0xa80: 0x2054, 0xa81: 0x205a, 0xa82: 0x2060, 0xa83: 0x2066, 0xa84: 0x206c, 0xa85: 0x2072,
	0xa86: 0x2078, 0xa87: 0x207e, 0xa88: 0x2084, 0xa89: 0x208a, 0xa8a: 0x2090, 0xa8b: 0x2096,
	0xa8c: 0x209c, 0xa8d: 0x20a2, 0xa8e: 0x2726, 0xa8f: 0x272f, 0xa90: 0x2738, 0xa91: 0x2741,
	0xa92: 0x274a, 0xa93: 0x2753, 0xa94: 0x275c, 0xa95: 0x2765, 0xa96: 0x276e, 0xa97: 0x2780,
	0xa98: 0x2789, 0xa99: 0x2792, 0xa9a: 0x279b, 0xa9b: 0x27a4, 0xa9c: 0x2777, 0xa9d: 0x2bac,
	0xa9e: 0x2aed, 0xaa0: 0x20a8, 0xaa1: 0x20c0, 0xaa2: 0x20b4, 0xaa3: 0x2108,
	0xaa4: 0x20c6, 0xaa5: 0x20e4, 0xaa6: 0x20ae, 0xaa7: 0x20de, 0xaa8: 0x20ba, 0xaa9: 0x20f0,
	0xaaa: 0x2120, 0xaab: 0x213e, 0xaac: 0x2138, 0xaad: 0x212c, 0xaae: 0x217a, 0xaaf: 0x210e,
	0xab0: 0x211a, 0xab1: 0x2132, 0xab2: 0x2126, 0xab3: 0x2150, 0xab4: 0x20fc, 0xab5: 0x2144,
	0xab6: 0x216e, 0xab7: 0x2156, 0xab8: 0x20ea, 0xab9: 0x20cc, 0xaba: 0x2102, 0xabb: 0x2114,
	0xabc: 0x214a, 0xabd: 0x20d2, 0xabe: 0x2174, 0xabf: 0x20f6,
	// Block 0x2b, offset 0xac0
	0xac0: 0x215c, 0xac1: 0x20d8, 0xac2: 0x2162, 0xac3: 0x2168, 0xac4: 0x092f, 0xac5: 0x0b03,
	0xac6: 0x0ca7, 0xac7: 0x10c7,
	0xad0: 0x1bc4, 0xad1: 0x18a9,
	0xad2: 0x18ac, 0xad3: 0x18af, 0xad4: 0x18b2, 0xad5: 0x18b5, 0xad6: 0x18b8, 0xad7: 0x18bb,
	0xad8: 0x18be, 0xad9: 0x18c1, 0xada: 0x18ca, 0xadb: 0x18cd, 0xadc: 0x18d0, 0xadd: 0x18d3,
	0xade: 0x18d6, 0xadf: 0x18d9, 0xae0: 0x0313, 0xae1: 0x031b, 0xae2: 0x031f, 0xae3: 0x0327,
	0xae4: 0x032b, 0xae5: 0x032f, 0xae6: 0x0337, 0xae7: 0x033f, 0xae8: 0x0343, 0xae9: 0x034b,
	0xaea: 0x034f, 0xaeb: 0x0353, 0xaec: 0x0357, 0xaed: 0x035b, 0xaee: 0x2e18, 0xaef: 0x2e20,
	0xaf0: 0x2e28, 0xaf1: 0x2e30, 0xaf2: 0x2e38, 0xaf3: 0x2e40, 0xaf4: 0x2e48, 0xaf5: 0x2e50,
	0xaf6: 0x2e60, 0xaf7: 0x2e68, 0xaf8: 0x2e70, 0xaf9: 0x2e78, 0xafa: 0x2e80, 0xafb: 0x2e88,
	0xafc: 0x2ed3, 0xafd: 0x2e9b, 0xafe: 0x2e58,
	// Block 0x2c, offset 0xb00
	0xb00: 0x06bf, 0xb01: 0x071b, 0xb02: 0x06cb, 0xb03: 0x097b, 0xb04: 0x071f, 0xb05: 0x07af,
	0xb06: 0x06c7, 0xb07: 0x07ab, 0xb08: 0x070b, 0xb09: 0x0887, 0xb0a: 0x0d07, 0xb0b: 0x0e8f,
	0xb0c: 0x0dd7, 0xb0d: 0x0d1b, 0xb0e: 0x145f, 0xb0f: 0x098b, 0xb10: 0x0ccf, 0xb11: 0x0d4b,
	0xb12: 0x0d0b, 0xb13: 0x104b, 0xb14: 0x08fb, 0xb15: 0x0f03, 0xb16: 0x1387, 0xb17: 0x105f,
	0xb18: 0x0843, 0xb19: 0x108f, 0xb1a: 0x0f9b, 0xb1b: 0x0a17, 0xb1c: 0x140f, 0xb1d: 0x077f,
	0xb1e: 0x08ab, 0xb1f: 0x0df7, 0xb20: 0x1527, 0xb21: 0x0743, 0xb22: 0x07d3, 0xb23: 0x0d9b,
	0xb24: 0x06cf, 0xb25: 0x06e7, 0xb26: 0x06d3, 0xb27: 0x0adb, 0xb28: 0x08ef, 0xb29: 0x087f,
	0xb2a: 0x0a57, 0xb2b: 0x0a4b, 0xb2c: 0x0feb, 0xb2d: 0x073f, 0xb2e: 0x139b, 0xb2f: 0x089b,
	0xb30: 0x09f3, 0xb31: 0x18dc, 0xb32: 0x18df, 0xb33: 0x18e2, 0xb34: 0x18e5, 0xb35: 0x18ee,
	0xb36: 0x18f1, 0xb37: 0x18f4, 0xb38: 0x18f7, 0xb39: 0x18fa, 0xb3a: 0x18fd, 0xb3b: 0x1900,
	0xb3c: 0x1903, 0xb3d: 0x1906, 0xb3e: 0x1909, 0xb3f: 0x1912,
	// Block 0x2d, offset 0xb40
	0xb40: 0x1cc6, 0xb41: 0x1cd5, 0xb42: 0x1ce4, 0xb43: 0x1cf3, 0xb44: 0x1d02, 0xb45: 0x1d11,
	0xb46: 0x1d20, 0xb47: 0x1d2f, 0xb48: 0x1d3e, 0xb49: 0x218c, 0xb4a: 0x219e, 0xb4b: 0x21b0,
	0xb4c: 0x1954, 0xb4d: 0x1c04, 0xb4e: 0x19d2, 0xb4f: 0x1ba8, 0xb50: 0x04cb, 0xb51: 0x04d3,
	0xb52: 0x04db, 0xb53: 0x04e3, 0xb54: 0x04eb, 0xb55: 0x04ef, 0xb56: 0x04f3, 0xb57: 0x04f7,
	0xb58: 0x04fb, 0xb59: 0x04ff, 0xb5a: 0x0503, 0xb5b: 0x0507, 0xb5c: 0x050b, 0xb5d: 0x050f,
	0xb5e: 0x0513, 0xb5f: 0x0517, 0xb60: 0x051b, 0xb61: 0x0523, 0xb62: 0x0527, 0xb63: 0x052b,
	0xb64: 0x052f, 0xb65: 0x0533, 0xb66: 0x0537, 0xb67: 0x053b, 0xb68: 0x053f, 0xb69: 0x0543,
	0xb6a: 0x0547, 0xb6b: 0x054b, 0xb6c: 0x054f, 0xb6d: 0x0553, 0xb6e: 0x0557, 0xb6f: 0x055b,
	0xb70: 0x055f, 0xb71: 0x0563, 0xb72: 0x0567, 0xb73: 0x056f, 0xb74: 0x0577, 0xb75: 0x057f,
	0xb76: 0x0583, 0xb77: 0x0587, 0xb78: 0x058b, 0xb79: 0x058f, 0xb7a: 0x0593, 0xb7b: 0x0597,
	0xb7c: 0x059b, 0xb7d: 0x059f, 0xb7e: 0x05a3,
	// Block 0x2e, offset 0xb80
	0xb80: 0x2b0c, 0xb81: 0x29a8, 0xb82: 0x2b1c, 0xb83: 0x2880, 0xb84: 0x2ee4, 0xb85: 0x288a,
	0xb86: 0x2894, 0xb87: 0x2f28, 0xb88: 0x29b5, 0xb89: 0x289e, 0xb8a: 0x28a8, 0xb8b: 0x28b2,
	0xb8c: 0x29dc, 0xb8d: 0x29e9, 0xb8e: 0x29c2, 0xb8f: 0x29cf, 0xb90: 0x2ea9, 0xb91: 0x29f6,
	0xb92: 0x2a03, 0xb93: 0x2bbe, 0xb94: 0x26bb, 0xb95: 0x2bd1, 0xb96: 0x2be4, 0xb97: 0x2b2c,
	0xb98: 0x2a10, 0xb99: 0x2bf7, 0xb9a: 0x2c0a, 0xb9b: 0x2a1d, 0xb9c: 0x28bc, 0xb9d: 0x28c6,
	0xb9e: 0x2eb7, 0xb9f: 0x2a2a, 0xba0: 0x2b3c, 0xba1: 0x2ef5, 0xba2: 0x28d0, 0xba3: 0x28da,
	0xba4: 0x2a37, 0xba5: 0x28e4, 0xba6: 0x28ee, 0xba7: 0x26d0, 0xba8: 0x26d7, 0xba9: 0x28f8,
	0xbaa: 0x2902, 0xbab: 0x2c1d, 0xbac: 0x2a44, 0xbad: 0x2b4c, 0xbae: 0x2c30, 0xbaf: 0x2a51,
	0xbb0: 0x2916, 0xbb1: 0x290c, 0xbb2: 0x2f3c, 0xbb3: 0x2a5e, 0xbb4: 0x2c43, 0xbb5: 0x2920,
	0xbb6: 0x2b5c, 0xbb7: 0x292a, 0xbb8: 0x2a78, 0xbb9: 0x2934, 0xbba: 0x2a85, 0xbbb: 0x2f06,
	0xbbc: 0x2a6b, 0xbbd: 0x2b6c, 0xbbe: 0x2a92, 0xbbf: 0x26de,
	// Block 0x2f, offset 0xbc0
	0xbc0: 0x2f17, 0xbc1: 0x293e, 0xbc2: 0x2948, 0xbc3: 0x2a9f, 0xbc4: 0x2952, 0xbc5: 0x295c,
	0xbc6: 0x2966, 0xbc7: 0x2b7c, 0xbc8: 0x2aac, 0xbc9: 0x26e5, 0xbca: 0x2c56, 0xbcb: 0x2e90,
	0xbcc: 0x2b8c, 0xbcd: 0x2ab9, 0xbce: 0x2ec5, 0xbcf: 0x2970, 0xbd0: 0x297a, 0xbd1: 0x2ac6,
	0xbd2: 0x26ec, 0xbd3: 0x2ad3, 0xbd4: 0x2b9c, 0xbd5: 0x26f3, 0xbd6: 0x2c69, 0xbd7: 0x2984,
	0xbd8: 0x1cb7, 0xbd9: 0x1ccb, 0xbda: 0x1cda, 0xbdb: 0x1ce9, 0xbdc: 0x1cf8, 0xbdd: 0x1d07,
	0xbde: 0x1d16, 0xbdf: 0x1d25, 0xbe0: 0x1d34, 0xbe1: 0x1d43, 0xbe2: 0x2192, 0xbe3: 0x21a4,
	0xbe4: 0x21b6, 0xbe5: 0x21c2, 0xbe6: 0x21ce, 0xbe7: 0x21da, 0xbe8: 0x21e6, 0xbe9: 0x21f2,
	0xbea: 0x21fe, 0xbeb: 0x220a, 0xbec: 0x2246, 0xbed: 0x2252, 0xbee: 0x225e, 0xbef: 0x226a,
	0xbf0: 0x2276, 0xbf1: 0x1c14, 0xbf2: 0x19c6, 0xbf3: 0x1936, 0xbf4: 0x1be4, 0xbf5: 0x1a47,
	0xbf6: 0x1a56, 0xbf7: 0x19cc, 0xbf8: 0x1bfc, 0xbf9: 0x1c00, 0xbfa: 0x1960, 0xbfb: 0x2701,
	0xbfc: 0x270f, 0xbfd: 0x26fa, 0xbfe: 0x2708, 0xbff: 0x2ae0,
	// Block 0x30, offset 0xc00
	0xc00: 0x1a4a, 0xc01: 0x1a32, 0xc02: 0x1c60, 0xc03: 0x1a1a, 0xc04: 0x19f3, 0xc05: 0x1969,
	0xc06: 0x1978, 0xc07: 0x1948, 0xc08: 0x1bf0, 0xc09: 0x1d52, 0xc0a: 0x1a4d, 0xc0b: 0x1a35,
	0xc0c: 0x1c64, 0xc0d: 0x1c70, 0xc0e: 0x1a26, 0xc0f: 0x19fc, 0xc10: 0x1957, 0xc11: 0x1c1c,
	0xc12: 0x1bb0, 0xc13: 0x1b9c, 0xc14: 0x1bcc, 0xc15: 0x1c74, 0xc16: 0x1a29, 0xc17: 0x19c9,
	0xc18: 0x19ff, 0xc19: 0x19de, 0xc1a: 0x1a41, 0xc1b: 0x1c78, 0xc1c: 0x1a2c, 0xc1d: 0x19c0,
	0xc1e: 0x1a02, 0xc1f: 0x1c3c, 0xc20: 0x1bf4, 0xc21: 0x1a14, 0xc22: 0x1c24, 0xc23: 0x1c40,
	0xc24: 0x1bf8, 0xc25: 0x1a17, 0xc26: 0x1c28, 0xc27: 0x22e8, 0xc28: 0x22fc, 0xc29: 0x1996,
	0xc2a: 0x1c20, 0xc2b: 0x1bb4, 0xc2c: 0x1ba0, 0xc2d: 0x1c48, 0xc2e: 0x2716, 0xc2f: 0x27ad,
	0xc30: 0x1a59, 0xc31: 0x1a44, 0xc32: 0x1c7c, 0xc33: 0x1a2f, 0xc34: 0x1a50, 0xc35: 0x1a38,
	0xc36: 0x1c68, 0xc37: 0x1a1d, 0xc38: 0x19f6, 0xc39: 0x1981, 0xc3a: 0x1a53, 0xc3b: 0x1a3b,
	0xc3c: 0x1c6c, 0xc3d: 0x1a20, 0xc3e: 0x19f9, 0xc3f: 0x1984,
	// Block 0x31, offset 0xc40
	0xc40: 0x1c2c, 0xc41: 0x1bb8, 0xc42: 0x1d4d, 0xc43: 0x1939, 0xc44: 0x19ba, 0xc45: 0x19bd,
	0xc46: 0x22f5, 0xc47: 0x1b94, 0xc48: 0x19c3, 0xc49: 0x194b, 0xc4a: 0x19e1, 0xc4b: 0x194e,
	0xc4c: 0x19ea, 0xc4d: 0x196c, 0xc4e: 0x196f, 0xc4f: 0x1a05, 0xc50: 0x1a0b, 0xc51: 0x1a0e,
	0xc52: 0x1c30, 0xc53: 0x1a11, 0xc54: 0x1a23, 0xc55: 0x1c38, 0xc56: 0x1c44, 0xc57: 0x1990,
	0xc58: 0x1d57, 0xc59: 0x1bbc, 0xc5a: 0x1993, 0xc5b: 0x1a5c, 0xc5c: 0x19a5, 0xc5d: 0x19b4,
	0xc5e: 0x22e2, 0xc5f: 0x22dc, 0xc60: 0x1cc1, 0xc61: 0x1cd0, 0xc62: 0x1cdf, 0xc63: 0x1cee,
	0xc64: 0x1cfd, 0xc65: 0x1d0c, 0xc66: 0x1d1b, 0xc67: 0x1d2a, 0xc68: 0x1d39, 0xc69: 0x2186,
	0xc6a: 0x2198, 0xc6b: 0x21aa, 0xc6c: 0x21bc, 0xc6d: 0x21c8, 0xc6e: 0x21d4, 0xc6f: 0x21e0,
	0xc70: 0x21ec, 0xc71: 0x21f8, 0xc72: 0x2204, 0xc73: 0x2240, 0xc74: 0x224c, 0xc75: 0x2258,
	0xc76: 0x2264, 0xc77: 0x2270, 0xc78: 0x227c, 0xc79: 0x2282, 0xc7a: 0x2288, 0xc7b: 0x228e,
	0xc7c: 0x2294, 0xc7d: 0x22a6, 0xc7e: 0x22ac, 0xc7f: 0x1c10,
	// Block 0x32, offset 0xc80
	0xc80: 0x1377, 0xc81: 0x0cfb, 0xc82: 0x13d3, 0xc83: 0x139f, 0xc84: 0x0e57, 0xc85: 0x06eb,
	0xc86: 0x08df, 0xc87: 0x162b, 0xc88: 0x162b, 0xc89: 0x0a0b, 0xc8a: 0x145f, 0xc8b: 0x0943,
	0xc8c: 0x0a07, 0xc8d: 0x0bef, 0xc8e: 0x0fcf, 0xc8f: 0x115f, 0xc90: 0x1297, 0xc91: 0x12d3,
	0xc92: 0x1307, 0xc93: 0x141b, 0xc94: 0x0d73, 0xc95: 0x0dff, 0xc96: 0x0eab, 0xc97: 0x0f43,
	0xc98: 0x125f, 0xc99: 0x1447, 0xc9a: 0x1573, 0xc9b: 0x070f, 0xc9c: 0x08b3, 0xc9d: 0x0d87,
	0xc9e: 0x0ecf, 0xc9f: 0x1293, 0xca0: 0x15c3, 0xca1: 0x0ab3, 0xca2: 0x0e77, 0xca3: 0x1283,
	0xca4: 0x1317, 0xca5: 0x0c23, 0xca6: 0x11bb, 0xca7: 0x12df, 0xca8: 0x0b1f, 0xca9: 0x0d0f,
	0xcaa: 0x0e17, 0xcab: 0x0f1b, 0xcac: 0x1427, 0xcad: 0x074f, 0xcae: 0x07e7, 0xcaf: 0x0853,
	0xcb0: 0x0c8b, 0xcb1: 0x0d7f, 0xcb2: 0x0ecb, 0xcb3: 0x0fef, 0xcb4: 0x1177, 0xcb5: 0x128b,
	0xcb6: 0x12a3, 0xcb7: 0x13c7, 0xcb8: 0x14ef, 0xcb9: 0x15a3, 0xcba: 0x15bf, 0xcbb: 0x102b,
	0xcbc: 0x106b, 0xcbd: 0x1123, 0xcbe: 0x1243, 0xcbf: 0x147b,
	// Block 0x33, offset 0xcc0
	0xcc0: 0x15cb, 0xcc1: 0x134b, 0xcc2: 0x09c7, 0xcc3: 0x0b3b, 0xcc4: 0x10db, 0xcc5: 0x119b,
	0xcc6: 0x0eff, 0xcc7: 0x1033, 0xcc8: 0x1397, 0xcc9: 0x14e7, 0xcca: 0x09c3, 0xccb: 0x0a8f,
	0xccc: 0x0d77, 0xccd: 0x0e2b, 0xcce: 0x0e5f, 0xccf: 0x1113, 0xcd0: 0x113b, 0xcd1: 0x14a7,
	0xcd2: 0x084f, 0xcd3: 0x11a7, 0xcd4: 0x07f3, 0xcd5: 0x07ef, 0xcd6: 0x1097, 0xcd7: 0x1127,
	0xcd8: 0x125b, 0xcd9: 0x14af, 0xcda: 0x1367, 0xcdb: 0x0c27, 0xcdc: 0x0d73, 0xcdd: 0x1357,
	0xcde: 0x06f7, 0xcdf: 0x0a63, 0xce0: 0x0b93, 0xce1: 0x0f2f, 0xce2: 0x0faf, 0xce3: 0x0873,
	0xce4: 0x103b, 0xce5: 0x075f, 0xce6: 0x0b77, 0xce7: 0x06d7, 0xce8: 0x0deb, 0xce9: 0x0ca3,
	0xcea: 0x110f, 0xceb: 0x08c7, 0xcec: 0x09b3, 0xced: 0x0ffb, 0xcee: 0x1263, 0xcef: 0x133b,
	0xcf0: 0x0db7, 0xcf1: 0x13f7, 0xcf2: 0x0de3, 0xcf3: 0x0c37, 0xcf4: 0x121b, 0xcf5: 0x0c57,
	0xcf6: 0x0fab, 0xcf7: 0x072b, 0xcf8: 0x07a7, 0xcf9: 0x07eb, 0xcfa: 0x0d53, 0xcfb: 0x10fb,
	0xcfc: 0x11f3, 0xcfd: 0x1347, 0xcfe: 0x145b, 0xcff: 0x085b,
	// Block 0x34, offset 0xd00
	0xd00: 0x090f, 0xd01: 0x0a17, 0xd02: 0x0b2f, 0xd03: 0x0cbf, 0xd04: 0x0e7b, 0xd05: 0x103f,
	0xd06: 0x1497, 0xd07: 0x157b, 0xd08: 0x15cf, 0xd09: 0x15e7, 0xd0a: 0x0837, 0xd0b: 0x0cf3,
	0xd0c: 0x0da3, 0xd0d: 0x13eb, 0xd0e: 0x0afb, 0xd0f: 0x0bd7, 0xd10: 0x0bf3, 0xd11: 0x0c83,
	0xd12: 0x0e6b, 0xd13: 0x0eb7, 0xd14: 0x0f67, 0xd15: 0x108b, 0xd16: 0x112f, 0xd17: 0x1193,
	0xd18: 0x13db, 0xd19: 0x126b, 0xd1a: 0x1403, 0xd1b: 0x147f, 0xd1c: 0x080f, 0xd1d: 0x083b,
	0xd1e: 0x0923, 0xd1f: 0x0ea7, 0xd20: 0x12f3, 0xd21: 0x133b, 0xd22: 0x0b1b, 0xd23: 0x0b8b,
	0xd24: 0x0c4f, 0xd25: 0x0daf, 0xd26: 0x10d7, 0xd27: 0x0f23, 0xd28: 0x073b, 0xd29: 0x097f,
	0xd2a: 0x0a63, 0xd2b: 0x0ac7, 0xd2c: 0x0b97, 0xd2d: 0x0f3f, 0xd2e: 0x0f5b, 0xd2f: 0x116b,
	0xd30: 0x118b, 0xd31: 0x1463, 0xd32: 0x14e3, 0xd33: 0x14f3, 0xd34: 0x152f, 0xd35: 0x0753,
	0xd36: 0x107f, 0xd37: 0x144f, 0xd38: 0x14cb, 0xd39: 0x0baf, 0xd3a: 0x0717, 0xd3b: 0x0777,
	0xd3c: 0x0a67, 0xd3d: 0x0a87, 0xd3e: 0x0caf, 0xd3f: 0x0d73,
	// Block 0x35, offset 0xd40
	0xd40: 0x0ec3, 0xd41: 0x0fcb, 0xd42: 0x1277, 0xd43: 0x1417, 0xd44: 0x1623, 0xd45: 0x0ce3,
	0xd46: 0x14a3, 0xd47: 0x0833, 0xd48: 0x0d2f, 0xd49: 0x0d3b, 0xd4a: 0x0e0f, 0xd4b: 0x0e47,
	0xd4c: 0x0f4b, 0xd4d: 0x0fa7, 0xd4e: 0x1027, 0xd4f: 0x110b, 0xd50: 0x153b, 0xd51: 0x07af,
	0xd52: 0x0c03, 0xd53: 0x14b3, 0xd54: 0x0767, 0xd55: 0x0aab, 0xd56: 0x0e2f, 0xd57: 0x13df,
	0xd58: 0x0b67, 0xd59: 0x0bb7, 0xd5a: 0x0d43, 0xd5b: 0x0f2f, 0xd5c: 0x14bb, 0xd5d: 0x0817,
	0xd5e: 0x08ff, 0xd5f: 0x0a97, 0xd60: 0x0cd3, 0xd61: 0x0d1f, 0xd62: 0x0d5f, 0xd63: 0x0df3,
	0xd64: 0x0f47, 0xd65: 0x0fbb, 0xd66: 0x1157, 0xd67: 0x12f7, 0xd68: 0x1303, 0xd69: 0x1457,
	0xd6a: 0x14d7, 0xd6b: 0x0883, 0xd6c: 0x0e4b, 0xd6d: 0x0903, 0xd6e: 0x0ec7, 0xd6f: 0x0f6b,
	0xd70: 0x1287, 0xd71: 0x14bf, 0xd72: 0x15ab, 0xd73: 0x15d3, 0xd74: 0x0d37, 0xd75: 0x0e27,
	0xd76: 0x11c3, 0xd77: 0x10b7, 0xd78: 0x10c3, 0xd79: 0x10e7, 0xd7a: 0x0f17, 0xd7b: 0x0e9f,
	0xd7c: 0x1363, 0xd7d: 0x0733, 0xd7e: 0x122b, 0xd7f: 0x081b,
	// Block 0x36, offset 0xd80
	0xd80: 0x080b, 0xd81: 0x0b0b, 0xd82: 0x0c2b, 0xd83: 0x10f3, 0xd84: 0x0a53, 0xd85: 0x0e03,
	0xd86: 0x0cef, 0xd87: 0x13e7, 0xd88: 0x12e7, 0xd89: 0x14ab, 0xd8a: 0x1323, 0xd8b: 0x0b27,
	0xd8c: 0x0787, 0xd8d: 0x095b, 0xd90: 0x09af,
	0xd92: 0x0cdf, 0xd95: 0x07f7, 0xd96: 0x0f1f, 0xd97: 0x0fe3,
	0xd98: 0x1047, 0xd99: 0x1063, 0xd9a: 0x1067, 0xd9b: 0x107b, 0xd9c: 0x14fb, 0xd9d: 0x10eb,
	0xd9e: 0x116f, 0xda0: 0x128f, 0xda2: 0x1353,
	0xda5: 0x1407, 0xda6: 0x1433,
	0xdaa: 0x154f, 0xdab: 0x1553, 0xdac: 0x1557, 0xdad: 0x15bb, 0xdae: 0x142b, 0xdaf: 0x14c7,
	0xdb0: 0x0757, 0xdb1: 0x077b, 0xdb2: 0x078f, 0xdb3: 0x084b, 0xdb4: 0x0857, 0xdb5: 0x0897,
	0xdb6: 0x094b, 0xdb7: 0x0967, 0xdb8: 0x096f, 0xdb9: 0x09ab, 0xdba: 0x09b7, 0xdbb: 0x0a93,
	0xdbc: 0x0a9b, 0xdbd: 0x0ba3, 0xdbe: 0x0bcb, 0xdbf: 0x0bd3,
	// Block 0x37, offset 0xdc0
	0xdc0: 0x0beb, 0xdc1: 0x0c97, 0xdc2: 0x0cc7, 0xdc3: 0x0ce7, 0xdc4: 0x0d57, 0xdc5: 0x0e1b,
	0xdc6: 0x0e37, 0xdc7: 0x0e67, 0xdc8: 0x0ebb, 0xdc9: 0x0edb, 0xdca: 0x0f4f, 0xdcb: 0x102f,
	0xdcc: 0x104b, 0xdcd: 0x1053, 0xdce: 0x104f, 0xdcf: 0x1057, 0xdd0: 0x105b, 0xdd1: 0x105f,
	0xdd2: 0x1073, 0xdd3: 0x1077, 0xdd4: 0x109b, 0xdd5: 0x10af, 0xdd6: 0x10cb, 0xdd7: 0x112f,
	0xdd8: 0x1137, 0xdd9: 0x113f, 0xdda: 0x1153, 0xddb: 0x117b, 0xddc: 0x11cb, 0xddd: 0x11ff,
	0xdde: 0x11ff, 0xddf: 0x1267, 0xde0: 0x130f, 0xde1: 0x1327, 0xde2: 0x135b, 0xde3: 0x135f,
	0xde4: 0x13a3, 0xde5: 0x13a7, 0xde6: 0x13ff, 0xde7: 0x1407, 0xde8: 0x14db, 0xde9: 0x151f,
	0xdea: 0x1537, 0xdeb: 0x0b9b, 0xdec: 0x171e, 0xded: 0x11e3,
	0xdf0: 0x06df, 0xdf1: 0x07e3, 0xdf2: 0x07a3, 0xdf3: 0x074b, 0xdf4: 0x078b, 0xdf5: 0x07b7,
	0xdf6: 0x0847, 0xdf7: 0x0863, 0xdf8: 0x094b, 0xdf9: 0x0937, 0xdfa: 0x0947, 0xdfb: 0x0963,
	0xdfc: 0x09af, 0xdfd: 0x09bf, 0xdfe: 0x0a03, 0xdff: 0x0a0f,
	// Block 0x38, offset 0xe00
	0xe00: 0x0a2b, 0xe01: 0x0a3b, 0xe02: 0x0b23, 0xe03: 0x0b2b, 0xe04: 0x0b5b, 0xe05: 0x0b7b,
	0xe06: 0x0bab, 0xe07: 0x0bc3, 0xe08: 0x0bb3, 0xe09: 0x0bd3, 0xe0a: 0x0bc7, 0xe0b: 0x0beb,
	0xe0c: 0x0c07, 0xe0d: 0x0c5f, 0xe0e: 0x0c6b, 0xe0f: 0x0c73, 0xe10: 0x0c9b, 0xe11: 0x0cdf,
	0xe12: 0x0d0f, 0xe13: 0x0d13, 0xe14: 0x0d27, 0xe15: 0x0da7, 0xe16: 0x0db7, 0xe17: 0x0e0f,
	0xe18: 0x0e5b, 0xe19: 0x0e53, 0xe1a: 0x0e67, 0xe1b: 0x0e83, 0xe1c: 0x0ebb, 0xe1d: 0x1013,
	0xe1e: 0x0edf, 0xe1f: 0x0f13, 0xe20: 0x0f1f, 0xe21: 0x0f5f, 0xe22: 0x0f7b, 0xe23: 0x0f9f,
	0xe24: 0x0fc3, 0xe25: 0x0fc7, 0xe26: 0x0fe3, 0xe27: 0x0fe7, 0xe28: 0x0ff7, 0xe29: 0x100b,
	0xe2a: 0x1007, 0xe2b: 0x1037, 0xe2c: 0x10b3, 0xe2d: 0x10cb, 0xe2e: 0x10e3, 0xe2f: 0x111b,
	0xe30: 0x112f, 0xe31: 0x114b, 0xe32: 0x117b, 0xe33: 0x122f, 0xe34: 0x1257, 0xe35: 0x12cb,
	0xe36: 0x1313, 0xe37: 0x131f, 0xe38: 0x1327, 0xe39: 0x133f, 0xe3a: 0x1353, 0xe3b: 0x1343,
	0xe3c: 0x135b, 0xe3d: 0x1357, 0xe3e: 0x134f, 0xe3f: 0x135f,
	// Block 0x39, offset 0xe40
	0xe40: 0x136b, 0xe41: 0x13a7, 0xe42: 0x13e3, 0xe43: 0x1413, 0xe44: 0x144b, 0xe45: 0x146b,
	0xe46: 0x14b7, 0xe47: 0x14db, 0xe48: 0x14fb, 0xe49: 0x150f, 0xe4a: 0x151f, 0xe4b: 0x152b,
	0xe4c: 0x1537, 0xe4d: 0x158b, 0xe4e: 0x162b, 0xe4f: 0x16b5, 0xe50: 0x16b0, 0xe51: 0x16e2,
	0xe52: 0x0607, 0xe53: 0x062f, 0xe54: 0x0633, 0xe55: 0x1764, 0xe56: 0x1791, 0xe57: 0x1809,
	0xe58: 0x1617, 0xe59: 0x1627,
	// Block 0x3a, offset 0xe80
	0xe80: 0x19d5, 0xe81: 0x19d8, 0xe82: 0x19db, 0xe83: 0x1c08, 0xe84: 0x1c0c, 0xe85: 0x1a5f,
	0xe86: 0x1a5f,
	0xe93: 0x1d75, 0xe94: 0x1d66, 0xe95: 0x1d6b, 0xe96: 0x1d7a, 0xe97: 0x1d70,
	0xe9d: 0x4390,
	0xe9e: 0x8115, 0xe9f: 0x4402, 0xea0: 0x022d, 0xea1: 0x0215, 0xea2: 0x021e, 0xea3: 0x0221,
	0xea4: 0x0224, 0xea5: 0x0227, 0xea6: 0x022a, 0xea7: 0x0230, 0xea8: 0x0233, 0xea9: 0x0017,
	0xeaa: 0x43f0, 0xeab: 0x43f6, 0xeac: 0x44f4, 0xead: 0x44fc, 0xeae: 0x4348, 0xeaf: 0x434e,
	0xeb0: 0x4354, 0xeb1: 0x435a, 0xeb2: 0x4366, 0xeb3: 0x436c, 0xeb4: 0x4372, 0xeb5: 0x437e,
	0xeb6: 0x4384, 0xeb8: 0x438a, 0xeb9: 0x4396, 0xeba: 0x439c, 0xebb: 0x43a2,
	0xebc: 0x43ae, 0xebe: 0x43b4,
	// Block 0x3b, offset 0xec0
	0xec0: 0x43ba, 0xec1: 0x43c0, 0xec3: 0x43c6, 0xec4: 0x43cc,
	0xec6: 0x43d8, 0xec7: 0x43de, 0xec8: 0x43e4, 0xec9: 0x43ea, 0xeca: 0x43fc, 0xecb: 0x4378,
	0xecc: 0x4360, 0xecd: 0x43a8, 0xece: 0x43d2, 0xecf: 0x1d7f, 0xed0: 0x0299, 0xed1: 0x0299,
	0xed2: 0x02a2, 0xed3: 0x02a2, 0xed4: 0x02a2, 0xed5: 0x02a2, 0xed6: 0x02a5, 0xed7: 0x02a5,
	0xed8: 0x02a5, 0xed9: 0x02a5, 0xeda: 0x02ab, 0xedb: 0x02ab, 0xedc: 0x02ab, 0xedd: 0x02ab,
	0xede: 0x029f, 0xedf: 0x029f, 0xee0: 0x029f, 0xee1: 0x029f, 0xee2: 0x02a8, 0xee3: 0x02a8,
	0xee4: 0x02a8, 0xee5: 0x02a8, 0xee6: 0x029c, 0xee7: 0x029c, 0xee8: 0x029c, 0xee9: 0x029c,
	0xeea: 0x02cf, 0xeeb: 0x02cf, 0xeec: 0x02cf, 0xeed: 0x02cf, 0xeee: 0x02d2, 0xeef: 0x02d2,
	0xef0: 0x02d2, 0xef1: 0x02d2, 0xef2: 0x02b1, 0xef3: 0x02b1, 0xef4: 0x02b1, 0xef5: 0x02b1,
	0xef6: 0x02ae, 0xef7: 0x02ae, 0xef8: 0x02ae, 0xef9: 0x02ae, 0xefa: 0x02b4, 0xefb: 0x02b4,
	0xefc: 0x02b4, 0xefd: 0x02b4, 0xefe: 0x02b7, 0xeff: 0x02b7,
	// Block 0x3c, offset 0xf00
	0xf00: 0x02b7, 0xf01: 0x02b7, 0xf02: 0x02c0, 0xf03: 0x02c0, 0xf04: 0x02bd, 0xf05: 0x02bd,
	0xf06: 0x02c3, 0xf07: 0x02c3, 0xf08: 0x02ba, 0xf09: 0x02ba, 0xf0a: 0x02c9, 0xf0b: 0x02c9,
	0xf0c: 0x02c6, 0xf0d: 0x02c6, 0xf0e: 0x02d5, 0xf0f: 0x02d5, 0xf10: 0x02d5, 0xf11: 0x02d5,
	0xf12: 0x02db, 0xf13: 0x02db, 0xf14: 0x02db, 0xf15: 0x02db, 0xf16: 0x02e1, 0xf17: 0x02e1,
	0xf18: 0x02e1, 0xf19: 0x02e1, 0xf1a: 0x02de, 0xf1b: 0x02de, 0xf1c: 0x02de, 0xf1d: 0x02de,
	0xf1e: 0x02e4, 0xf1f: 0x02e4, 0xf20: 0x02e7, 0xf21: 0x02e7, 0xf22: 0x02e7, 0xf23: 0x02e7,
	0xf24: 0x446e, 0xf25: 0x446e, 0xf26: 0x02ed, 0xf27: 0x02ed, 0xf28: 0x02ed, 0xf29: 0x02ed,
	0xf2a: 0x02ea, 0xf2b: 0x02ea, 0xf2c: 0x02ea, 0xf2d: 0x02ea, 0xf2e: 0x0308, 0xf2f: 0x0308,
	0xf30: 0x4468, 0xf31: 0x4468,
	// Block 0x3d, offset 0xf40
	0xf53: 0x02d8, 0xf54: 0x02d8, 0xf55: 0x02d8, 0xf56: 0x02d8, 0xf57: 0x02f6,
	0xf58: 0x02f6, 0xf59: 0x02f3, 0xf5a: 0x02f3, 0xf5b: 0x02f9, 0xf5c: 0x02f9, 0xf5d: 0x204f,
	0xf5e: 0x02ff, 0xf5f: 0x02ff, 0xf60: 0x02f0, 0xf61: 0x02f0, 0xf62: 0x02fc, 0xf63: 0x02fc,
	0xf64: 0x0305, 0xf65: 0x0305, 0xf66: 0x0305, 0xf67: 0x0305, 0xf68: 0x028d, 0xf69: 0x028d,
	0xf6a: 0x25aa, 0xf6b: 0x25aa, 0xf6c: 0x261a, 0xf6d: 0x261a, 0xf6e: 0x25e9, 0xf6f: 0x25e9,
	0xf70: 0x2605, 0xf71: 0x2605, 0xf72: 0x25fe, 0xf73: 0x25fe, 0xf74: 0x260c, 0xf75: 0x260c,
	0xf76: 0x2613, 0xf77: 0x2613, 0xf78: 0x2613, 0xf79: 0x25f0, 0xf7a: 0x25f0, 0xf7b: 0x25f0,
	0xf7c: 0x0302, 0xf7d: 0x0302, 0xf7e: 0x0302, 0xf7f: 0x0302,
	// Block 0x3e, offset 0xf80
	0xf80: 0x25b1, 0xf81: 0x25b8, 0xf82: 0x25d4, 0xf83: 0x25f0, 0xf84: 0x25f7, 0xf85: 0x1d89,
	0xf86: 0x1d8e, 0xf87: 0x1d93, 0xf88: 0x1da2, 0xf89: 0x1db1, 0xf8a: 0x1db6, 0xf8b: 0x1dbb,
	0xf8c: 0x1dc0, 0xf8d: 0x1dc5, 0xf8e: 0x1dd4, 0xf8f: 0x1de3, 0xf90: 0x1de8, 0xf91: 0x1ded,
	0xf92: 0x1dfc, 0xf93: 0x1e0b, 0xf94: 0x1e10, 0xf95: 0x1e15, 0xf96: 0x1e1a, 0xf97: 0x1e29,
	0xf98: 0x1e2e, 0xf99: 0x1e3d, 0xf9a: 0x1e42, 0xf9b: 0x1e47, 0xf9c: 0x1e56, 0xf9d: 0x1e5b,
	0xf9e: 0x1e60, 0xf9f: 0x1e6a, 0xfa0: 0x1ea6, 0xfa1: 0x1eb5, 0xfa2: 0x1ec4, 0xfa3: 0x1ec9,
	0xfa4: 0x1ece, 0xfa5: 0x1ed8, 0xfa6: 0x1ee7, 0xfa7: 0x1eec, 0xfa8: 0x1efb, 0xfa9: 0x1f00,
	0xfaa: 0x1f05, 0xfab: 0x1f14, 0xfac: 0x1f19, 0xfad: 0x1f28, 0xfae: 0x1f2d, 0xfaf: 0x1f32,
	0xfb0: 0x1f37, 0xfb1: 0x1f3c, 0xfb2: 0x1f41, 0xfb3: 0x1f46, 0xfb4: 0x1f4b, 0xfb5: 0x1f50,
	0xfb6: 0x1f55, 0xfb7: 0x1f5a, 0xfb8: 0x1f5f, 0xfb9: 0x1f64, 0xfba: 0x1f69, 0xfbb: 0x1f6e,
	0xfbc: 0x1f73, 0xfbd: 0x1f78, 0xfbe: 0x1f7d, 0xfbf: 0x1f87,
	// Block 0x3f, offset 0xfc0
	0xfc0: 0x1f8c, 0xfc1: 0x1f91, 0xfc2: 0x1f96, 0xfc3: 0x1fa0, 0xfc4: 0x1fa5, 0xfc5: 0x1faf,
	0xfc6: 0x1fb4, 0xfc7: 0x1fb9, 0xfc8: 0x1fbe, 0xfc9: 0x1fc3, 0xfca: 0x1fc8, 0xfcb: 0x1fcd,
	0xfcc: 0x1fd2, 0xfcd: 0x1fd7, 0xfce: 0x1fe6, 0xfcf: 0x1ff5, 0xfd0: 0x1ffa, 0xfd1: 0x1fff,
	0xfd2: 0x2004, 0xfd3: 0x2009, 0xfd4: 0x200e, 0xfd5: 0x2018, 0xfd6: 0x201d, 0xfd7: 0x2022,
	0xfd8: 0x2031, 0xfd9: 0x2040, 0xfda: 0x2045, 0xfdb: 0x4420, 0xfdc: 0x4426, 0xfdd: 0x445c,
	0xfde: 0x44b3, 0xfdf: 0x44ba, 0xfe0: 0x44c1, 0xfe1: 0x44c8, 0xfe2: 0x44cf, 0xfe3: 0x44d6,
	0xfe4: 0x25c6, 0xfe5: 0x25cd, 0xfe6: 0x25d4, 0xfe7: 0x25db, 0xfe8: 0x25f0, 0xfe9: 0x25f7,
	0xfea: 0x1d98, 0xfeb: 0x1d9d, 0xfec: 0x1da2, 0xfed: 0x1da7, 0xfee: 0x1db1, 0xfef: 0x1db6,
	0xff0: 0x1dca, 0xff1: 0x1dcf, 0xff2: 0x1dd4, 0xff3: 0x1dd9, 0xff4: 0x1de3, 0xff5: 0x1de8,
	0xff6: 0x1df2, 0xff7: 0x1df7, 0xff8: 0x1dfc, 0xff9: 0x1e01, 0xffa: 0x1e0b, 0xffb: 0x1e10,
	0xffc: 0x1f3c, 0xffd: 0x1f41, 0xffe: 0x1f50, 0xfff: 0x1f55,
	// Block 0x40, offset 0x1000
	0x1000: 0x1f5a, 0x1001: 0x1f6e, 0x1002: 0x1f73, 0x1003: 0x1f78, 0x1004: 0x1f7d, 0x1005: 0x1f96,
	0x1006: 0x1fa0, 0x1007: 0x1fa5, 0x1008: 0x1faa, 0x1009: 0x1fbe, 0x100a: 0x1fdc, 0x100b: 0x1fe1,
	0x100c: 0x1fe6, 0x100d: 0x1feb, 0x100e: 0x1ff5, 0x100f: 0x1ffa, 0x1010: 0x445c, 0x1011: 0x2027,
	0x1012: 0x202c, 0x1013: 0x2031, 0x1014: 0x2036, 0x1015: 0x2040, 0x1016: 0x2045, 0x1017: 0x25b1,
	0x1018: 0x25b8, 0x1019: 0x25bf, 0x101a: 0x25d4, 0x101b: 0x25e2, 0x101c: 0x1d89, 0x101d: 0x1d8e,
	0x101e: 0x1d93, 0x101f: 0x1da2, 0x1020: 0x1dac, 0x1021: 0x1dbb, 0x1022: 0x1dc0, 0x1023: 0x1dc5,
	0x1024: 0x1dd4, 0x1025: 0x1dde, 0x1026: 0x1dfc, 0x1027: 0x1e15, 0x1028: 0x1e1a, 0x1029: 0x1e29,
	0x102a: 0x1e2e, 0x102b: 0x1e3d, 0x102c: 0x1e47, 0x102d: 0x1e56, 0x102e: 0x1e5b, 0x102f: 0x1e60,
	0x1030: 0x1e6a, 0x1031: 0x1ea6, 0x1032: 0x1eab, 0x1033: 0x1eb5, 0x1034: 0x1ec4, 0x1035: 0x1ec9,
	0x1036: 0x1ece, 0x1037: 0x1ed8, 0x1038: 0x1ee7, 0x1039: 0x1efb, 0x103a: 0x1f00, 0x103b: 0x1f05,
	0x103c: 0x1f14, 0x103d: 0x1f19, 0x103e: 0x1f28, 0x103f: 0x1f2d,
	// Block 0x41, offset 0x1040
	0x1040: 0x1f32, 0x1041: 0x1f37, 0x1042: 0x1f46, 0x1043: 0x1f4b, 0x1044: 0x1f5f, 0x1045: 0x1f64,
	0x1046: 0x1f69, 0x1047: 0x1f6e, 0x1048: 0x1f73, 0x1049: 0x1f87, 0x104a: 0x1f8c, 0x104b: 0x1f91,
	0x104c: 0x1f96, 0x104d: 0x1f9b, 0x104e: 0x1faf, 0x104f: 0x1fb4, 0x1050: 0x1fb9, 0x1051: 0x1fbe,
	0x1052: 0x1fcd, 0x1053: 0x1fd2, 0x1054: 0x1fd7, 0x1055: 0x1fe6, 0x1056: 0x1ff0, 0x1057: 0x1fff,
	0x1058: 0x2004, 0x1059: 0x4450, 0x105a: 0x2018, 0x105b: 0x201d, 0x105c: 0x2022, 0x105d: 0x2031,
	0x105e: 0x203b, 0x105f: 0x25d4, 0x1060: 0x25e2, 0x1061: 0x1da2, 0x1062: 0x1dac, 0x1063: 0x1dd4,
	0x1064: 0x1dde, 0x1065: 0x1dfc, 0x1066: 0x1e06, 0x1067: 0x1e6a, 0x1068: 0x1e6f, 0x1069: 0x1e92,
	0x106a: 0x1e97, 0x106b: 0x1f6e, 0x106c: 0x1f73, 0x106d: 0x1f96, 0x106e: 0x1fe6, 0x106f: 0x1ff0,
	0x1070: 0x2031, 0x1071: 0x203b, 0x1072: 0x4504, 0x1073: 0x450c, 0x1074: 0x4514, 0x1075: 0x1ef1,
	0x1076: 0x1ef6, 0x1077: 0x1f0a, 0x1078: 0x1f0f, 0x1079: 0x1f1e, 0x107a: 0x1f23, 0x107b: 0x1e74,
	0x107c: 0x1e79, 0x107d: 0x1e9c, 0x107e: 0x1ea1, 0x107f: 0x1e33,
	// Block 0x42, offset 0x1080
	0x1080: 0x1e38, 0x1081: 0x1e1f, 0x1082: 0x1e24, 0x1083: 0x1e4c, 0x1084: 0x1e51, 0x1085: 0x1eba,
	0x1086: 0x1ebf, 0x1087: 0x1edd, 0x1088: 0x1ee2, 0x1089: 0x1e7e, 0x108a: 0x1e83, 0x108b: 0x1e88,
	0x108c: 0x1e92, 0x108d: 0x1e8d, 0x108e: 0x1e65, 0x108f: 0x1eb0, 0x1090: 0x1ed3, 0x1091: 0x1ef1,
	0x1092: 0x1ef6, 0x1093: 0x1f0a, 0x1094: 0x1f0f, 0x1095: 0x1f1e, 0x1096: 0x1f23, 0x1097: 0x1e74,
	0x1098: 0x1e79, 0x1099: 0x1e9c, 0x109a: 0x1ea1, 0x109b: 0x1e33, 0x109c: 0x1e38, 0x109d: 0x1e1f,
	0x109e: 0x1e24, 0x109f: 0x1e4c, 0x10a0: 0x1e51, 0x10a1: 0x1eba, 0x10a2: 0x1ebf, 0x10a3: 0x1edd,
	0x10a4: 0x1ee2, 0x10a5: 0x1e7e, 0x10a6: 0x1e83, 0x10a7: 0x1e88, 0x10a8: 0x1e92, 0x10a9: 0x1e8d,
	0x10aa: 0x1e65, 0x10ab: 0x1eb0, 0x10ac: 0x1ed3, 0x10ad: 0x1e7e, 0x10ae: 0x1e83, 0x10af: 0x1e88,
	0x10b0: 0x1e92, 0x10b1: 0x1e6f, 0x10b2: 0x1e97, 0x10b3: 0x1eec, 0x10b4: 0x1e56, 0x10b5: 0x1e5b,
	0x10b6: 0x1e60, 0x10b7: 0x1e7e, 0x10b8: 0x1e83, 0x10b9: 0x1e88, 0x10ba: 0x1eec, 0x10bb: 0x1efb,
	0x10bc: 0x4408, 0x10bd: 0x4408,
	// Block 0x43, offset 0x10c0
	0x10d0: 0x2311, 0x10d1: 0x2326,
	0x10d2: 0x2326, 0x10d3: 0x232d, 0x10d4: 0x2334, 0x10d5: 0x2349, 0x10d6: 0x2350, 0x10d7: 0x2357,
	0x10d8: 0x237a, 0x10d9: 0x237a, 0x10da: 0x239d, 0x10db: 0x2396, 0x10dc: 0x23b2, 0x10dd: 0x23a4,
	0x10de: 0x23ab, 0x10df: 0x23ce, 0x10e0: 0x23ce, 0x10e1: 0x23c7, 0x10e2: 0x23d5, 0x10e3: 0x23d5,
	0x10e4: 0x23ff, 0x10e5: 0x23ff, 0x10e6: 0x241b, 0x10e7: 0x23e3, 0x10e8: 0x23e3, 0x10e9: 0x23dc,
	0x10ea: 0x23f1, 0x10eb: 0x23f1, 0x10ec: 0x23f8, 0x10ed: 0x23f8, 0x10ee: 0x2422, 0x10ef: 0x2430,
	0x10f0: 0x2430, 0x10f1: 0x2437, 0x10f2: 0x2437, 0x10f3: 0x243e, 0x10f4: 0x2445, 0x10f5: 0x244c,
	0x10f6: 0x2453, 0x10f7: 0x2453, 0x10f8: 0x245a, 0x10f9: 0x2468, 0x10fa: 0x2476, 0x10fb: 0x246f,
	0x10fc: 0x247d, 0x10fd: 0x247d, 0x10fe: 0x2492, 0x10ff: 0x2499,
	// Block 0x44, offset 0x1100
	0x1100: 0x24ca, 0x1101: 0x24d8, 0x1102: 0x24d1, 0x1103: 0x24b5, 0x1104: 0x24b5, 0x1105: 0x24df,
	0x1106: 0x24df, 0x1107: 0x24e6, 0x1108: 0x24e6, 0x1109: 0x2510, 0x110a: 0x2517, 0x110b: 0x251e,
	0x110c: 0x24f4, 0x110d: 0x2502, 0x110e: 0x2525, 0x110f: 0x252c,
	0x1112: 0x24fb, 0x1113: 0x2580, 0x1114: 0x2587, 0x1115: 0x255d, 0x1116: 0x2564, 0x1117: 0x2548,
	0x1118: 0x2548, 0x1119: 0x254f, 0x111a: 0x2579, 0x111b: 0x2572, 0x111c: 0x259c, 0x111d: 0x259c,
	0x111e: 0x230a, 0x111f: 0x231f, 0x1120: 0x2318, 0x1121: 0x2342, 0x1122: 0x233b, 0x1123: 0x2365,
	0x1124: 0x235e, 0x1125: 0x2388, 0x1126: 0x236c, 0x1127: 0x2381, 0x1128: 0x23b9, 0x1129: 0x2406,
	0x112a: 0x23ea, 0x112b: 0x2429, 0x112c: 0x24c3, 0x112d: 0x24ed, 0x112e: 0x2595, 0x112f: 0x258e,
	0x1130: 0x25a3, 0x1131: 0x253a, 0x1132: 0x24a0, 0x1133: 0x256b, 0x1134: 0x2492, 0x1135: 0x24ca,
	0x1136: 0x2461, 0x1137: 0x24ae, 0x1138: 0x2541, 0x1139: 0x2533, 0x113a: 0x24bc, 0x113b: 0x24a7,
	0x113c: 0x24bc, 0x113d: 0x2541, 0x113e: 0x2373, 0x113f: 0x238f,
	// Block 0x45, offset 0x1140
	0x1140: 0x2509, 0x1141: 0x2484, 0x1142: 0x2303, 0x1143: 0x24a7, 0x1144: 0x244c, 0x1145: 0x241b,
	0x1146: 0x23c0, 0x1147: 0x2556,
	0x1170: 0x2414, 0x1171: 0x248b, 0x1172: 0x27bf, 0x1173: 0x27b6, 0x1174: 0x27ec, 0x1175: 0x27da,
	0x1176: 0x27c8, 0x1177: 0x27e3, 0x1178: 0x27f5, 0x1179: 0x240d, 0x117a: 0x2c7c, 0x117b: 0x2afc,
	0x117c: 0x27d1,
	// Block 0x46, offset 0x1180
	0x1190: 0x0019, 0x1191: 0x0483,
	0x1192: 0x0487, 0x1193: 0x0035, 0x1194: 0x0037, 0x1195: 0x0003, 0x1196: 0x003f, 0x1197: 0x04bf,
	0x1198: 0x04c3, 0x1199: 0x1b5c,
	0x11a0: 0x8132, 0x11a1: 0x8132, 0x11a2: 0x8132, 0x11a3: 0x8132,
	0x11a4: 0x8132, 0x11a5: 0x8132, 0x11a6: 0x8132, 0x11a7: 0x812d, 0x11a8: 0x812d, 0x11a9: 0x812d,
	0x11aa: 0x812d, 0x11ab: 0x812d, 0x11ac: 0x812d, 0x11ad: 0x812d, 0x11ae: 0x8132, 0x11af: 0x8132,
	0x11b0: 0x1873, 0x11b1: 0x0443, 0x11b2: 0x043f, 0x11b3: 0x007f, 0x11b4: 0x007f, 0x11b5: 0x0011,
	0x11b6: 0x0013, 0x11b7: 0x00b7, 0x11b8: 0x00bb, 0x11b9: 0x04b7, 0x11ba: 0x04bb, 0x11bb: 0x04ab,
	0x11bc: 0x04af, 0x11bd: 0x0493, 0x11be: 0x0497, 0x11bf: 0x048b,
	// Block 0x47, offset 0x11c0
	0x11c0: 0x048f, 0x11c1: 0x049b, 0x11c2: 0x049f, 0x11c3: 0x04a3, 0x11c4: 0x04a7,
	0x11c7: 0x0077, 0x11c8: 0x007b, 0x11c9: 0x4269, 0x11ca: 0x4269, 0x11cb: 0x4269,
	0x11cc: 0x4269, 0x11cd: 0x007f, 0x11ce: 0x007f, 0x11cf: 0x007f, 0x11d0: 0x0019, 0x11d1: 0x0483,
	0x11d2: 0x001d, 0x11d4: 0x0037, 0x11d5: 0x0035, 0x11d6: 0x003f, 0x11d7: 0x0003,
	0x11d8: 0x0443, 0x11d9: 0x0011, 0x11da: 0x0013, 0x11db: 0x00b7, 0x11dc: 0x00bb, 0x11dd: 0x04b7,
	0x11de: 0x04bb, 0x11df: 0x0007, 0x11e0: 0x000d, 0x11e1: 0x0015, 0x11e2: 0x0017, 0x11e3: 0x001b,
	0x11e4: 0x0039, 0x11e5: 0x003d, 0x11e6: 0x003b, 0x11e8: 0x0079, 0x11e9: 0x0009,
	0x11ea: 0x000b, 0x11eb: 0x0041,
	0x11f0: 0x42aa, 0x11f1: 0x442c, 0x11f2: 0x42af, 0x11f4: 0x42b4,
	0x11f6: 0x42b9, 0x11f7: 0x4432, 0x11f8: 0x42be, 0x11f9: 0x4438, 0x11fa: 0x42c3, 0x11fb: 0x443e,
	0x11fc: 0x42c8, 0x11fd: 0x4444, 0x11fe: 0x42cd, 0x11ff: 0x444a,
	// Block 0x48, offset 0x1200
	0x1200: 0x0236, 0x1201: 0x440e, 0x1202: 0x440e, 0x1203: 0x4414, 0x1204: 0x4414, 0x1205: 0x4456,
	0x1206: 0x4456, 0x1207: 0x441a, 0x1208: 0x441a, 0x1209: 0x4462, 0x120a: 0x4462, 0x120b: 0x4462,
	0x120c: 0x4462, 0x120d: 0x0239, 0x120e: 0x0239, 0x120f: 0x023c, 0x1210: 0x023c, 0x1211: 0x023c,
	0x1212: 0x023c, 0x1213: 0x023f, 0x1214: 0x023f, 0x1215: 0x0242, 0x1216: 0x0242, 0x1217: 0x0242,
	0x1218: 0x0242, 0x1219: 0x0245, 0x121a: 0x0245, 0x121b: 0x0245, 0x121c: 0x0245, 0x121d: 0x0248,
	0x121e: 0x0248, 0x121f: 0x0248, 0x1220: 0x0248, 0x1221: 0x024b, 0x1222: 0x024b, 0x1223: 0x024b,
	0x1224: 0x024b, 0x1225: 0x024e, 0x1226: 0x024e, 0x1227: 0x024e, 0x1228: 0x024e, 0x1229: 0x0251,
	0x122a: 0x0251, 0x122b: 0x0254, 0x122c: 0x0254, 0x122d: 0x0257, 0x122e: 0x0257, 0x122f: 0x025a,
	0x1230: 0x025a, 0x1231: 0x025d, 0x1232: 0x025d, 0x1233: 0x025d, 0x1234: 0x025d, 0x1235: 0x0260,
	0x1236: 0x0260, 0x1237: 0x0260, 0x1238: 0x0260, 0x1239: 0x0263, 0x123a: 0x0263, 0x123b: 0x0263,
	0x123c: 0x0263, 0x123d: 0x0266, 0x123e: 0x0266, 0x123f: 0x0266,
	// Block 0x49, offset 0x1240
	0x1240: 0x0266, 0x1241: 0x0269, 0x1242: 0x0269, 0x1243: 0x0269, 0x1244: 0x0269, 0x1245: 0x026c,
	0x1246: 0x026c, 0x1247: 0x026c, 0x1248: 0x026c, 0x1249: 0x026f, 0x124a: 0x026f, 0x124b: 0x026f,
	0x124c: 0x026f, 0x124d: 0x0272, 0x124e: 0x0272, 0x124f: 0x0272, 0x1250: 0x0272, 0x1251: 0x0275,
	0x1252: 0x0275, 0x1253: 0x0275, 0x1254: 0x0275, 0x1255: 0x0278, 0x1256: 0x0278, 0x1257: 0x0278,
	0x1258: 0x0278, 0x1259: 0x027b, 0x125a: 0x027b, 0x125b: 0x027b, 0x125c: 0x027b, 0x125d: 0x027e,
	0x125e: 0x027e, 0x125f: 0x027e, 0x1260: 0x027e, 0x1261: 0x0281, 0x1262: 0x0281, 0x1263: 0x0281,
	0x1264: 0x0281, 0x1265: 0x0284, 0x1266: 0x0284, 0x1267: 0x0284, 0x1268: 0x0284, 0x1269: 0x0287,
	0x126a: 0x0287, 0x126b: 0x0287, 0x126c: 0x0287, 0x126d: 0x028a, 0x126e: 0x028a, 0x126f: 0x028d,
	0x1270: 0x028d, 0x1271: 0x0290, 0x1272: 0x0290, 0x1273: 0x0290, 0x1274: 0x0290, 0x1275: 0x2e00,
	0x1276: 0x2e00, 0x1277: 0x2e08, 0x1278: 0x2e08, 0x1279: 0x2e10, 0x127a: 0x2e10, 0x127b: 0x1f82,
	0x127c: 0x1f82,
	// Block 0x4a, offset 0x1280
	0x1280: 0x0081, 0x1281: 0x0083, 0x1282: 0x0085, 0x1283: 0x0087, 0x1284: 0x0089, 0x1285: 0x008b,
	0x1286: 0x008d, 0x1287: 0x008f, 0x1288: 0x0091, 0x1289: 0x0093, 0x128a: 0x0095, 0x128b: 0x0097,
	0x128c: 0x0099, 0x128d: 0x009b, 0x128e: 0x009d, 0x128f: 0x009f, 0x1290: 0x00a1, 0x1291: 0x00a3,
	0x1292: 0x00a5, 0x1293: 0x00a7, 0x1294: 0x00a9, 0x1295: 0x00ab, 0x1296: 0x00ad, 0x1297: 0x00af,
	0x1298: 0x00b1, 0x1299: 0x00b3, 0x129a: 0x00b5, 0x129b: 0x00b7, 0x129c: 0x00b9, 0x129d: 0x00bb,
	0x129e: 0x00bd, 0x129f: 0x0477, 0x12a0: 0x047b, 0x12a1: 0x0487, 0x12a2: 0x049b, 0x12a3: 0x049f,
	0x12a4: 0x0483, 0x12a5: 0x05ab, 0x12a6: 0x05a3, 0x12a7: 0x04c7, 0x12a8: 0x04cf, 0x12a9: 0x04d7,
	0x12aa: 0x04df, 0x12ab: 0x04e7, 0x12ac: 0x056b, 0x12ad: 0x0573, 0x12ae: 0x057b, 0x12af: 0x051f,
	0x12b0: 0x05af, 0x12b1: 0x04cb, 0x12b2: 0x04d3, 0x12b3: 0x04db, 0x12b4: 0x04e3, 0x12b5: 0x04eb,
	0x12b6: 0x04ef, 0x12b7: 0x04f3, 0x12b8: 0x04f7, 0x12b9: 0x04fb, 0x12ba: 0x04ff, 0x12bb: 0x0503,
	0x12bc: 0x0507, 0x12bd: 0x050b, 0x12be: 0x050f, 0x12bf: 0x0513,
	// Block 0x4b, offset 0x12c0
	0x12c0: 0x0517, 0x12c1: 0x051b, 0x12c2: 0x0523, 0x12c3: 0x0527, 0x12c4: 0x052b, 0x12c5: 0x052f,
	0x12c6: 0x0533, 0x12c7: 0x0537, 0x12c8: 0x053b, 0x12c9: 0x053f, 0x12ca: 0x0543, 0x12cb: 0x0547,
	0x12cc: 0x054b, 0x12cd: 0x054f, 0x12ce: 0x0553, 0x12cf: 0x0557, 0x12d0: 0x055b, 0x12d1: 0x055f,
	0x12d2: 0x0563, 0x12d3: 0x0567, 0x12d4: 0x056f, 0x12d5: 0x0577, 0x12d6: 0x057f, 0x12d7: 0x0583,
	0x12d8: 0x0587, 0x12d9: 0x058b, 0x12da: 0x058f, 0x12db: 0x0593, 0x12dc: 0x0597, 0x12dd: 0x05a7,
	0x12de: 0x4a78, 0x12df: 0x4a7e, 0x12e0: 0x03c3, 0x12e1: 0x0313, 0x12e2: 0x0317, 0x12e3: 0x4a3b,
	0x12e4: 0x031b, 0x12e5: 0x4a41, 0x12e6: 0x4a47, 0x12e7: 0x031f, 0x12e8: 0x0323, 0x12e9: 0x0327,
	0x12ea: 0x4a4d, 0x12eb: 0x4a53, 0x12ec: 0x4a59, 0x12ed: 0x4a5f, 0x12ee: 0x4a65, 0x12ef: 0x4a6b,
	0x12f0: 0x0367, 0x12f1: 0x032b, 0x12f2: 0x032f, 0x12f3: 0x0333, 0x12f4: 0x037b, 0x12f5: 0x0337,
	0x12f6: 0x033b, 0x12f7: 0x033f, 0x12f8: 0x0343, 0x12f9: 0x0347, 0x12fa: 0x034b, 0x12fb: 0x034f,
	0x12fc: 0x0353, 0x12fd: 0x0357, 0x12fe: 0x035b,
	// Block 0x4c, offset 0x1300
	0x1302: 0x49bd, 0x1303: 0x49c3, 0x1304: 0x49c9, 0x1305: 0x49cf,
	0x1306: 0x49d5, 0x1307: 0x49db, 0x130a: 0x49e1, 0x130b: 0x49e7,
	0x130c: 0x49ed, 0x130d: 0x49f3, 0x130e: 0x49f9, 0x130f: 0x49ff,
	0x1312: 0x4a05, 0x1313: 0x4a0b, 0x1314: 0x4a11, 0x1315: 0x4a17, 0x1316: 0x4a1d, 0x1317: 0x4a23,
	0x131a: 0x4a29, 0x131b: 0x4a2f, 0x131c: 0x4a35,
	0x1320: 0x00bf, 0x1321: 0x00c2, 0x1322: 0x00cb, 0x1323: 0x4264,
	0x1324: 0x00c8, 0x1325: 0x00c5, 0x1326: 0x0447, 0x1328: 0x046b, 0x1329: 0x044b,
	0x132a: 0x044f, 0x132b: 0x0453, 0x132c: 0x0457, 0x132d: 0x046f, 0x132e: 0x0473,
	// Block 0x4d, offset 0x1340
	0x1340: 0x0063, 0x1341: 0x0065, 0x1342: 0x0067, 0x1343: 0x0069, 0x1344: 0x006b, 0x1345: 0x006d,
	0x1346: 0x006f, 0x1347: 0x0071, 0x1348: 0x0073, 0x1349: 0x0075, 0x134a: 0x0083, 0x134b: 0x0085,
	0x134c: 0x0087, 0x134d: 0x0089, 0x134e: 0x008b, 0x134f: 0x008d, 0x1350: 0x008f, 0x1351: 0x0091,
	0x1352: 0x0093, 0x1353: 0x0095, 0x1354: 0x0097, 0x1355: 0x0099, 0x1356: 0x009b, 0x1357: 0x009d,
	0x1358: 0x009f, 0x1359: 0x00a1, 0x135a: 0x00a3, 0x135b: 0x00a5, 0x135c: 0x00a7, 0x135d: 0x00a9,
	0x135e: 0x00ab, 0x135f: 0x00ad, 0x1360: 0x00af, 0x1361: 0x00b1, 0x1362: 0x00b3, 0x1363: 0x00b5,
	0x1364: 0x00dd, 0x1365: 0x00f2, 0x1368: 0x0173, 0x1369: 0x0176,
	0x136a: 0x0179, 0x136b: 0x017c, 0x136c: 0x017f, 0x136d: 0x0182, 0x136e: 0x0185, 0x136f: 0x0188,
	0x1370: 0x018b, 0x1371: 0x018e, 0x1372: 0x0191, 0x1373: 0x0194, 0x1374: 0x0197, 0x1375: 0x019a,
	0x1376: 0x019d, 0x1377: 0x01a0, 0x1378: 0x01a3, 0x1379: 0x0188, 0x137a: 0x01a6, 0x137b: 0x01a9,
	0x137c: 0x01ac, 0x137d: 0x01af, 0x137e: 0x01b2, 0x137f: 0x01b5,
	// Block 0x4e, offset 0x1380
	0x1380: 0x01fd, 0x1381: 0x0200, 0x1382: 0x0203, 0x1383: 0x045b, 0x1384: 0x01c7, 0x1385: 0x01d0,
	0x1386: 0x01d6, 0x1387: 0x01fa, 0x1388: 0x01eb, 0x1389: 0x01e8, 0x138a: 0x0206, 0x138b: 0x0209,
	0x138e: 0x0021, 0x138f: 0x0023, 0x1390: 0x0025, 0x1391: 0x0027,
	0x1392: 0x0029, 0x1393: 0x002b, 0x1394: 0x002d, 0x1395: 0x002f, 0x1396: 0x0031, 0x1397: 0x0033,
	0x1398: 0x0021, 0x1399: 0x0023, 0x139a: 0x0025, 0x139b: 0x0027, 0x139c: 0x0029, 0x139d: 0x002b,
	0x139e: 0x002d, 0x139f: 0x002f, 0x13a0: 0x0031, 0x13a1: 0x0033, 0x13a2: 0x0021, 0x13a3: 0x0023,
	0x13a4: 0x0025, 0x13a5: 0x0027, 0x13a6: 0x0029, 0x13a7: 0x002b, 0x13a8: 0x002d, 0x13a9: 0x002f,
	0x13aa: 0x0031, 0x13ab: 0x0033, 0x13ac: 0x0021, 0x13ad: 0x0023, 0x13ae: 0x0025, 0x13af: 0x0027,
	0x13b0: 0x0029, 0x13b1: 0x002b, 0x13b2: 0x002d, 0x13b3: 0x002f, 0x13b4: 0x0031, 0x13b5: 0x0033,
	0x13b6: 0x0021, 0x13b7: 0x0023, 0x13b8: 0x0025, 0x13b9: 0x0027, 0x13ba: 0x0029, 0x13bb: 0x002b,
	0x13bc: 0x002d, 0x13bd: 0x002f, 0x13be: 0x0031, 0x13bf: 0x0033,
	// Block 0x4f, offset 0x13c0
	0x13c0: 0x0239, 0x13c1: 0x023c, 0x13c2: 0x0248, 0x13c3: 0x0251, 0x13c5: 0x028a,
	0x13c6: 0x025a, 0x13c7: 0x024b, 0x13c8: 0x0269, 0x13c9: 0x0290, 0x13ca: 0x027b, 0x13cb: 0x027e,
	0x13cc: 0x0281, 0x13cd: 0x0284, 0x13ce: 0x025d, 0x13cf: 0x026f, 0x13d0: 0x0275, 0x13d1: 0x0263,
	0x13d2: 0x0278, 0x13d3: 0x0257, 0x13d4: 0x0260, 0x13d5: 0x0242, 0x13d6: 0x0245, 0x13d7: 0x024e,
	0x13d8: 0x0254, 0x13d9: 0x0266, 0x13da: 0x026c, 0x13db: 0x0272, 0x13dc: 0x0293, 0x13dd: 0x02e4,
	0x13de: 0x02cc, 0x13df: 0x0296, 0x13e1: 0x023c, 0x13e2: 0x0248,
	0x13e4: 0x0287, 0x13e7: 0x024b, 0x13e9: 0x0290,
	0x13ea: 0x027b, 0x13eb: 0x027e, 0x13ec: 0x0281, 0x13ed: 0x0284, 0x13ee: 0x025d, 0x13ef: 0x026f,
	0x13f0: 0x0275, 0x13f1: 0x0263, 0x13f2: 0x0278, 0x13f4: 0x0260, 0x13f5: 0x0242,
	0x13f6: 0x0245, 0x13f7: 0x024e, 0x13f9: 0x0266, 0x13fb: 0x0272,
	// Block 0x50, offset 0x1400
	0x1402: 0x0248,
	0x1407: 0x024b, 0x1409: 0x0290, 0x140b: 0x027e,
	0x140d: 0x0284, 0x140e: 0x025d, 0x140f: 0x026f, 0x1411: 0x0263,
	0x1412: 0x0278, 0x1414: 0x0260, 0x1417: 0x024e,
	0x1419: 0x0266, 0x141b: 0x0272, 0x141d: 0x02e4,
	0x141f: 0x0296, 0x1421: 0x023c, 0x1422: 0x0248,
	0x1424: 0x0287, 0x1427: 0x024b, 0x1428: 0x0269, 0x1429: 0x0290,
	0x142a: 0x027b, 0x142c: 0x0281, 0x142d: 0x0284, 0x142e: 0x025d, 0x142f: 0x026f,
	0x1430: 0x0275, 0x1431: 0x0263, 0x1432: 0x0278, 0x1434: 0x0260, 0x1435: 0x0242,
	0x1436: 0x0245, 0x1437: 0x024e, 0x1439: 0x0266, 0x143a: 0x026c, 0x143b: 0x0272,
	0x143c: 0x0293, 0x143e: 0x02cc,
	// Block 0x51, offset 0x1440
	0x1440: 0x0239, 0x1441: 0x023c, 0x1442: 0x0248, 0x1443: 0x0251, 0x1444: 0x0287, 0x1445: 0x028a,
	0x1446: 0x025a, 0x1447: 0x024b, 0x1448: 0x0269, 0x1449: 0x0290, 0x144b: 0x027e,
	0x144c: 0x0281, 0x144d: 0x0284, 0x144e: 0x025d, 0x144f: 0x026f, 0x1450: 0x0275, 0x1451: 0x0263,
	0x1452: 0x0278, 0x1453: 0x0257, 0x1454: 0x0260, 0x1455: 0x0242, 0x1456: 0x0245, 0x1457: 0x024e,
	0x1458: 0x0254, 0x1459: 0x0266, 0x145a: 0x026c, 0x145b: 0x0272,
	0x1461: 0x023c, 0x1462: 0x0248, 0x1463: 0x0251,
	0x1465: 0x028a, 0x1466: 0x025a, 0x1467: 0x024b, 0x1468: 0x0269, 0x1469: 0x0290,
	0x146b: 0x027e, 0x146c: 0x0281, 0x146d: 0x0284, 0x146e: 0x025d, 0x146f: 0x026f,
	0x1470: 0x0275, 0x1471: 0x0263, 0x1472: 0x0278, 0x1473: 0x0257, 0x1474: 0x0260, 0x1475: 0x0242,
	0x1476: 0x0245, 0x1477: 0x024e, 0x1478: 0x0254, 0x1479: 0x0266, 0x147a: 0x026c, 0x147b: 0x0272,
	// Block 0x52, offset 0x1480
	0x1480: 0x1879, 0x1481: 0x1876, 0x1482: 0x187c, 0x1483: 0x18a0, 0x1484: 0x18c4, 0x1485: 0x18e8,
	0x1486: 0x190c, 0x1487: 0x1915, 0x1488: 0x191b, 0x1489: 0x1921, 0x148a: 0x1927,
	0x1490: 0x1a8c, 0x1491: 0x1a90,
	0x1492: 0x1a94, 0x1493: 0x1a98, 0x1494: 0x1a9c, 0x1495: 0x1aa0, 0x1496: 0x1aa4, 0x1497: 0x1aa8,
	0x1498: 0x1aac, 0x1499: 0x1ab0, 0x149a: 0x1ab4, 0x149b: 0x1ab8, 0x149c: 0x1abc, 0x149d: 0x1ac0,
	0x149e: 0x1ac4, 0x149f: 0x1ac8, 0x14a0: 0x1acc, 0x14a1: 0x1ad0, 0x14a2: 0x1ad4, 0x14a3: 0x1ad8,
	0x14a4: 0x1adc, 0x14a5: 0x1ae0, 0x14a6: 0x1ae4, 0x14a7: 0x1ae8, 0x14a8: 0x1aec, 0x14a9: 0x1af0,
	0x14aa: 0x271e, 0x14ab: 0x0047, 0x14ac: 0x0065, 0x14ad: 0x193c, 0x14ae: 0x19b1,
	0x14b0: 0x0043, 0x14b1: 0x0045, 0x14b2: 0x0047, 0x14b3: 0x0049, 0x14b4: 0x004b, 0x14b5: 0x004d,
	0x14b6: 0x004f, 0x14b7: 0x0051, 0x14b8: 0x0053, 0x14b9: 0x0055, 0x14ba: 0x0057, 0x14bb: 0x0059,
	0x14bc: 0x005b, 0x14bd: 0x005d, 0x14be: 0x005f, 0x14bf: 0x0061,
	// Block 0x53, offset 0x14c0
	0x14c0: 0x26ad, 0x14c1: 0x26c2, 0x14c2: 0x0503,
	0x14d0: 0x0c0f, 0x14d1: 0x0a47,
	0x14d2: 0x08d3, 0x14d3: 0x45c4, 0x14d4: 0x071b, 0x14d5: 0x09ef, 0x14d6: 0x132f, 0x14d7: 0x09ff,
	0x14d8: 0x0727, 0x14d9: 0x0cd7, 0x14da: 0x0eaf, 0x14db: 0x0caf, 0x14dc: 0x0827, 0x14dd: 0x0b6b,
	0x14de: 0x07bf, 0x14df: 0x0cb7, 0x14e0: 0x0813, 0x14e1: 0x1117, 0x14e2: 0x0f83, 0x14e3: 0x138b,
	0x14e4: 0x09d3, 0x14e5: 0x090b, 0x14e6: 0x0e63, 0x14e7: 0x0c1b, 0x14e8: 0x0c47, 0x14e9: 0x06bf,
	0x14ea: 0x06cb, 0x14eb: 0x140b, 0x14ec: 0x0adb, 0x14ed: 0x06e7, 0x14ee: 0x08ef, 0x14ef: 0x0c3b,
	0x14f0: 0x13b3, 0x14f1: 0x0c13, 0x14f2: 0x106f, 0x14f3: 0x10ab, 0x14f4: 0x08f7, 0x14f5: 0x0e43,
	0x14f6: 0x0d0b, 0x14f7: 0x0d07, 0x14f8: 0x0f97, 0x14f9: 0x082b, 0x14fa: 0x0957, 0x14fb: 0x1443,
	// Block 0x54, offset 0x1500
	0x1500: 0x06fb, 0x1501: 0x06f3, 0x1502: 0x0703, 0x1503: 0x1647, 0x1504: 0x0747, 0x1505: 0x0757,
	0x1506: 0x075b, 0x1507: 0x0763, 0x1508: 0x076b, 0x1509: 0x076f, 0x150a: 0x077b, 0x150b: 0x0773,
	0x150c: 0x05b3, 0x150d: 0x165b, 0x150e: 0x078f, 0x150f: 0x0793, 0x1510: 0x0797, 0x1511: 0x07b3,
	0x1512: 0x164c, 0x1513: 0x05b7, 0x1514: 0x079f, 0x1515: 0x07bf, 0x1516: 0x1656, 0x1517: 0x07cf,
	0x1518: 0x07d7, 0x1519: 0x0737, 0x151a: 0x07df, 0x151b: 0x07e3, 0x151c: 0x1831, 0x151d: 0x07ff,
	0x151e: 0x0807, 0x151f: 0x05bf, 0x1520: 0x081f, 0x1521: 0x0823, 0x1522: 0x082b, 0x1523: 0x082f,
	0x1524: 0x05c3, 0x1525: 0x0847, 0x1526: 0x084b, 0x1527: 0x0857, 0x1528: 0x0863, 0x1529: 0x0867,
	0x152a: 0x086b, 0x152b: 0x0873, 0x152c: 0x0893, 0x152d: 0x0897, 0x152e: 0x089f, 0x152f: 0x08af,
	0x1530: 0x08b7, 0x1531: 0x08bb, 0x1532: 0x08bb, 0x1533: 0x08bb, 0x1534: 0x166a, 0x1535: 0x0e93,
	0x1536: 0x08cf, 0x1537: 0x08d7, 0x1538: 0x166f, 0x1539: 0x08e3, 0x153a: 0x08eb, 0x153b: 0x08f3,
	0x153c: 0x091b, 0x153d: 0x0907, 0x153e: 0x0913, 0x153f: 0x0917,
	// Block 0x55, offset 0x1540
	0x1540: 0x091f, 0x1541: 0x0927, 0x1542: 0x092b, 0x1543: 0x0933, 0x1544: 0x093b, 0x1545: 0x093f,
	0x1546: 0x093f, 0x1547: 0x0947, 0x1548: 0x094f, 0x1549: 0x0953, 0x154a: 0x095f, 0x154b: 0x0983,
	0x154c: 0x0967, 0x154d: 0x0987, 0x154e: 0x096b, 0x154f: 0x0973, 0x1550: 0x080b, 0x1551: 0x09cf,
	0x1552: 0x0997, 0x1553: 0x099b, 0x1554: 0x099f, 0x1555: 0x0993, 0x1556: 0x09a7, 0x1557: 0x09a3,
	0x1558: 0x09bb, 0x1559: 0x1674, 0x155a: 0x09d7, 0x155b: 0x09db, 0x155c: 0x09e3, 0x155d: 0x09ef,
	0x155e: 0x09f7, 0x155f: 0x0a13, 0x1560: 0x1679, 0x1561: 0x167e, 0x1562: 0x0a1f, 0x1563: 0x0a23,
	0x1564: 0x0a27, 0x1565: 0x0a1b, 0x1566: 0x0a2f, 0x1567: 0x05c7, 0x1568: 0x05cb, 0x1569: 0x0a37,
	0x156a: 0x0a3f, 0x156b: 0x0a3f, 0x156c: 0x1683, 0x156d: 0x0a5b, 0x156e: 0x0a5f, 0x156f: 0x0a63,
	0x1570: 0x0a6b, 0x1571: 0x1688, 0x1572: 0x0a73, 0x1573: 0x0a77, 0x1574: 0x0b4f, 0x1575: 0x0a7f,
	0x1576: 0x05cf, 0x1577: 0x0a8b, 0x1578: 0x0a9b, 0x1579: 0x0aa7, 0x157a: 0x0aa3, 0x157b: 0x1692,
	0x157c: 0x0aaf, 0x157d: 0x1697, 0x157e: 0x0abb, 0x157f: 0x0ab7,
	// Block 0x56, offset 0x1580
	0x1580: 0x0abf, 0x1581: 0x0acf, 0x1582: 0x0ad3, 0x1583: 0x05d3, 0x1584: 0x0ae3, 0x1585: 0x0aeb,
	0x1586: 0x0aef, 0x1587: 0x0af3, 0x1588: 0x05d7, 0x1589: 0x169c, 0x158a: 0x05db, 0x158b: 0x0b0f,
	0x158c: 0x0b13, 0x158d: 0x0b17, 0x158e: 0x0b1f, 0x158f: 0x1863, 0x1590: 0x0b37, 0x1591: 0x16a6,
	0x1592: 0x16a6, 0x1593: 0x11d7, 0x1594: 0x0b47, 0x1595: 0x0b47, 0x1596: 0x05df, 0x1597: 0x16c9,
	0x1598: 0x179b, 0x1599: 0x0b57, 0x159a: 0x0b5f, 0x159b: 0x05e3, 0x159c: 0x0b73, 0x159d: 0x0b83,
	0x159e: 0x0b87, 0x159f: 0x0b8f, 0x15a0: 0x0b9f, 0x15a1: 0x05eb, 0x15a2: 0x05e7, 0x15a3: 0x0ba3,
	0x15a4: 0x16ab, 0x15a5: 0x0ba7, 0x15a6: 0x0bbb, 0x15a7: 0x0bbf, 0x15a8: 0x0bc3, 0x15a9: 0x0bbf,
	0x15aa: 0x0bcf, 0x15ab: 0x0bd3, 0x15ac: 0x0be3, 0x15ad: 0x0bdb, 0x15ae: 0x0bdf, 0x15af: 0x0be7,
	0x15b0: 0x0beb, 0x15b1: 0x0bef, 0x15b2: 0x0bfb, 0x15b3: 0x0bff, 0x15b4: 0x0c17, 0x15b5: 0x0c1f,
	0x15b6: 0x0c2f, 0x15b7: 0x0c43, 0x15b8: 0x16ba, 0x15b9: 0x0c3f, 0x15ba: 0x0c33, 0x15bb: 0x0c4b,
	0x15bc: 0x0c53, 0x15bd: 0x0c67, 0x15be: 0x16bf, 0x15bf: 0x0c6f,
	// Block 0x57, offset 0x15c0
	0x15c0: 0x0c63, 0x15c1: 0x0c5b, 0x15c2: 0x05ef, 0x15c3: 0x0c77, 0x15c4: 0x0c7f, 0x15c5: 0x0c87,
	0x15c6: 0x0c7b, 0x15c7: 0x05f3, 0x15c8: 0x0c97, 0x15c9: 0x0c9f, 0x15ca: 0x16c4, 0x15cb: 0x0ccb,
	0x15cc: 0x0cff, 0x15cd: 0x0cdb, 0x15ce: 0x05ff, 0x15cf: 0x0ce7, 0x15d0: 0x05fb, 0x15d1: 0x05f7,
	0x15d2: 0x07c3, 0x15d3: 0x07c7, 0x15d4: 0x0d03, 0x15d5: 0x0ceb, 0x15d6: 0x11ab, 0x15d7: 0x0663,
	0x15d8: 0x0d0f, 0x15d9: 0x0d13, 0x15da: 0x0d17, 0x15db: 0x0d2b, 0x15dc: 0x0d23, 0x15dd: 0x16dd,
	0x15de: 0x0603, 0x15df: 0x0d3f, 0x15e0: 0x0d33, 0x15e1: 0x0d4f, 0x15e2: 0x0d57, 0x15e3: 0x16e7,
	0x15e4: 0x0d5b, 0x15e5: 0x0d47, 0x15e6: 0x0d63, 0x15e7: 0x0607, 0x15e8: 0x0d67, 0x15e9: 0x0d6b,
	0x15ea: 0x0d6f, 0x15eb: 0x0d7b, 0x15ec: 0x16ec, 0x15ed: 0x0d83, 0x15ee: 0x060b, 0x15ef: 0x0d8f,
	0x15f0: 0x16f1, 0x15f1: 0x0d93, 0x15f2: 0x060f, 0x15f3: 0x0d9f, 0x15f4: 0x0dab, 0x15f5: 0x0db7,
	0x15f6: 0x0dbb, 0x15f7: 0x16f6, 0x15f8: 0x168d, 0x15f9: 0x16fb, 0x15fa: 0x0ddb, 0x15fb: 0x1700,
	0x15fc: 0x0de7, 0x15fd: 0x0def, 0x15fe: 0x0ddf, 0x15ff: 0x0dfb,
	// Block 0x58, offset 0x1600
	0x1600: 0x0e0b, 0x1601: 0x0e1b, 0x1602: 0x0e0f, 0x1603: 0x0e13, 0x1604: 0x0e1f, 0x1605: 0x0e23,
	0x1606: 0x1705, 0x1607: 0x0e07, 0x1608: 0x0e3b, 0x1609: 0x0e3f, 0x160a: 0x0613, 0x160b: 0x0e53,
	0x160c: 0x0e4f, 0x160d: 0x170a, 0x160e: 0x0e33, 0x160f: 0x0e6f, 0x1610: 0x170f, 0x1611: 0x1714,
	0x1612: 0x0e73, 0x1613: 0x0e87, 0x1614: 0x0e83, 0x1615: 0x0e7f, 0x1616: 0x0617, 0x1617: 0x0e8b,
	0x1618: 0x0e9b, 0x1619: 0x0e97, 0x161a: 0x0ea3, 0x161b: 0x1651, 0x161c: 0x0eb3, 0x161d: 0x1719,
	0x161e: 0x0ebf, 0x161f: 0x1723, 0x1620: 0x0ed3, 0x1621: 0x0edf, 0x1622: 0x0ef3, 0x1623: 0x1728,
	0x1624: 0x0f07, 0x1625: 0x0f0b, 0x1626: 0x172d, 0x1627: 0x1732, 0x1628: 0x0f27, 0x1629: 0x0f37,
	0x162a: 0x061b, 0x162b: 0x0f3b, 0x162c: 0x061f, 0x162d: 0x061f, 0x162e: 0x0f53, 0x162f: 0x0f57,
	0x1630: 0x0f5f, 0x1631: 0x0f63, 0x1632: 0x0f6f, 0x1633: 0x0623, 0x1634: 0x0f87, 0x1635: 0x1737,
	0x1636: 0x0fa3, 0x1637: 0x173c, 0x1638: 0x0faf, 0x1639: 0x16a1, 0x163a: 0x0fbf, 0x163b: 0x1741,
	0x163c: 0x1746, 0x163d: 0x174b, 0x163e: 0x0627, 0x163f: 0x062b,
	// Block 0x59, offset 0x1640
	0x1640: 0x0ff7, 0x1641: 0x1755, 0x1642: 0x1750, 0x1643: 0x175a, 0x1644: 0x175f, 0x1645: 0x0fff,
	0x1646: 0x1003, 0x1647: 0x1003, 0x1648: 0x100b, 0x1649: 0x0633, 0x164a: 0x100f, 0x164b: 0x0637,
	0x164c: 0x063b, 0x164d: 0x1769, 0x164e: 0x1023, 0x164f: 0x102b, 0x1650: 0x1037, 0x1651: 0x063f,
	0x1652: 0x176e, 0x1653: 0x105b, 0x1654: 0x1773, 0x1655: 0x1778, 0x1656: 0x107b, 0x1657: 0x1093,
	0x1658: 0x0643, 0x1659: 0x109b, 0x165a: 0x109f, 0x165b: 0x10a3, 0x165c: 0x177d, 0x165d: 0x1782,
	0x165e: 0x1782, 0x165f: 0x10bb, 0x1660: 0x0647, 0x1661: 0x1787, 0x1662: 0x10cf, 0x1663: 0x10d3,
	0x1664: 0x064b, 0x1665: 0x178c, 0x1666: 0x10ef, 0x1667: 0x064f, 0x1668: 0x10ff, 0x1669: 0x10f7,
	0x166a: 0x1107, 0x166b: 0x1796, 0x166c: 0x111f, 0x166d: 0x0653, 0x166e: 0x112b, 0x166f: 0x1133,
	0x1670: 0x1143, 0x1671: 0x0657, 0x1672: 0x17a0, 0x1673: 0x17a5, 0x1674: 0x065b, 0x1675: 0x17aa,
	0x1676: 0x115b, 0x1677: 0x17af, 0x1678: 0x1167, 0x1679: 0x1173, 0x167a: 0x117b, 0x167b: 0x17b4,
	0x167c: 0x17b9, 0x167d: 0x118f, 0x167e: 0x17be, 0x167f: 0x1197,
	// Block 0x5a, offset 0x1680
	0x1680: 0x16ce, 0x1681: 0x065f, 0x1682: 0x11af, 0x1683: 0x11b3, 0x1684: 0x0667, 0x1685: 0x11b7,
	0x1686: 0x0a33, 0x1687: 0x17c3, 0x1688: 0x17c8, 0x1689: 0x16d3, 0x168a: 0x16d8, 0x168b: 0x11d7,
	0x168c: 0x11db, 0x168d: 0x13f3, 0x168e: 0x066b, 0x168f: 0x1207, 0x1690: 0x1203, 0x1691: 0x120b,
	0x1692: 0x083f, 0x1693: 0x120f, 0x1694: 0x1213, 0x1695: 0x1217, 0x1696: 0x121f, 0x1697: 0x17cd,
	0x1698: 0x121b, 0x1699: 0x1223, 0x169a: 0x1237, 0x169b: 0x123b, 0x169c: 0x1227, 0x169d: 0x123f,
	0x169e: 0x1253, 0x169f: 0x1267, 0x16a0: 0x1233, 0x16a1: 0x1247, 0x16a2: 0x124b, 0x16a3: 0x124f,
	0x16a4: 0x17d2, 0x16a5: 0x17dc, 0x16a6: 0x17d7, 0x16a7: 0x066f, 0x16a8: 0x126f, 0x16a9: 0x1273,
	0x16aa: 0x127b, 0x16ab: 0x17f0, 0x16ac: 0x127f, 0x16ad: 0x17e1, 0x16ae: 0x0673, 0x16af: 0x0677,
	0x16b0: 0x17e6, 0x16b1: 0x17eb, 0x16b2: 0x067b, 0x16b3: 0x129f, 0x16b4: 0x12a3, 0x16b5: 0x12a7,
	0x16b6: 0x12ab, 0x16b7: 0x12b7, 0x16b8: 0x12b3, 0x16b9: 0x12bf, 0x16ba: 0x12bb, 0x16bb: 0x12cb,
	0x16bc: 0x12c3, 0x16bd: 0x12c7, 0x16be: 0x12cf, 0x16bf: 0x067f,
	// Block 0x5b, offset 0x16c0
	0x16c0: 0x12d7, 0x16c1: 0x12db, 0x16c2: 0x0683, 0x16c3: 0x12eb, 0x16c4: 0x12ef, 0x16c5: 0x17f5,
	0x16c6: 0x12fb, 0x16c7: 0x12ff, 0x16c8: 0x0687, 0x16c9: 0x130b, 0x16ca: 0x05bb, 0x16cb: 0x17fa,
	0x16cc: 0x17ff, 0x16cd: 0x068b, 0x16ce: 0x068f, 0x16cf: 0x1337, 0x16d0: 0x134f, 0x16d1: 0x136b,
	0x16d2: 0x137b, 0x16d3: 0x1804, 0x16d4: 0x138f, 0x16d5: 0x1393, 0x16d6: 0x13ab, 0x16d7: 0x13b7,
	0x16d8: 0x180e, 0x16d9: 0x1660, 0x16da: 0x13c3, 0x16db: 0x13bf, 0x16dc: 0x13cb, 0x16dd: 0x1665,
	0x16de: 0x13d7, 0x16df: 0x13e3, 0x16e0: 0x1813, 0x16e1: 0x1818, 0x16e2: 0x1423, 0x16e3: 0x142f,
	0x16e4: 0x1437, 0x16e5: 0x181d, 0x16e6: 0x143b, 0x16e7: 0x1467, 0x16e8: 0x1473, 0x16e9: 0x1477,
	0x16ea: 0x146f, 0x16eb: 0x1483, 0x16ec: 0x1487, 0x16ed: 0x1822, 0x16ee: 0x1493, 0x16ef: 0x0693,
	0x16f0: 0x149b, 0x16f1: 0x1827, 0x16f2: 0x0697, 0x16f3: 0x14d3, 0x16f4: 0x0ac3, 0x16f5: 0x14eb,
	0x16f6: 0x182c, 0x16f7: 0x1836, 0x16f8: 0x069b, 0x16f9: 0x069f, 0x16fa: 0x1513, 0x16fb: 0x183b,
	0x16fc: 0x06a3, 0x16fd: 0x1840, 0x16fe: 0x152b, 0x16ff: 0x152b,
	// Block 0x5c, offset 0x1700
	0x1700: 0x1533, 0x1701: 0x1845, 0x1702: 0x154b, 0x1703: 0x06a7, 0x1704: 0x155b, 0x1705: 0x1567,
	0x1706: 0x156f, 0x1707: 0x1577, 0x1708: 0x06ab, 0x1709: 0x184a, 0x170a: 0x158b, 0x170b: 0x15a7,
	0x170c: 0x15b3, 0x170d: 0x06af, 0x170e: 0x06b3, 0x170f: 0x15b7, 0x1710: 0x184f, 0x1711: 0x06b7,
	0x1712: 0x1854, 0x1713: 0x1859, 0x1714: 0x185e, 0x1715: 0x15db, 0x1716: 0x06bb, 0x1717: 0x15ef,
	0x1718: 0x15f7, 0x1719: 0x15fb, 0x171a: 0x1603, 0x171b: 0x160b, 0x171c: 0x1613, 0x171d: 0x1868,
}

// nfkcIndex: 22 blocks, 1408 entries, 1408 bytes
// Block 0 is the zero block.
var nfkcIndex = [1408]uint8{
	// Block 0x0, offset 0x0
	// Block 0x1, offset 0x40
	// Block 0x2, offset 0x80
	// Block 0x3, offset 0xc0
	0xc2: 0x5b, 0xc3: 0x01, 0xc4: 0x02, 0xc5: 0x03, 0xc6: 0x5c, 0xc7: 0x04,
	0xc8: 0x05, 0xca: 0x5d, 0xcb: 0x5e, 0xcc: 0x06, 0xcd: 0x07, 0xce: 0x08, 0xcf: 0x09,
	0xd0: 0x0a, 0xd1: 0x5f, 0xd2: 0x60, 0xd3: 0x0b, 0xd6: 0x0c, 0xd7: 0x61,
	0xd8: 0x62, 0xd9: 0x0d, 0xdb: 0x63, 0xdc: 0x64, 0xdd: 0x65, 0xdf: 0x66,
	0xe0: 0x02, 0xe1: 0x03, 0xe2: 0x04, 0xe3: 0x05,
	0xea: 0x06, 0xeb: 0x07, 0xec: 0x08, 0xed: 0x09, 0xef: 0x0a,
	0xf0: 0x13,
	// Block 0x4, offset 0x100
	0x120: 0x67, 0x121: 0x68, 0x123: 0x69, 0x124: 0x6a, 0x125: 0x6b, 0x126: 0x6c, 0x127: 0x6d,
	0x128: 0x6e, 0x129: 0x6f, 0x12a: 0x70, 0x12b: 0x71, 0x12c: 0x6c, 0x12d: 0x72, 0x12e: 0x73, 0x12f: 0x74,
	0x131: 0x75, 0x132: 0x76, 0x133: 0x77, 0x134: 0x78, 0x135: 0x79, 0x137: 0x7a,
	0x138: 0x7b, 0x139: 0x7c, 0x13a: 0x7d, 0x13b: 0x7e, 0x13c: 0x7f, 0x13d: 0x80, 0x13e: 0x81, 0x13f: 0x82,
	// Block 0x5, offset 0x140
	0x140: 0x83, 0x142: 0x84, 0x143: 0x85, 0x144: 0x86, 0x145: 0x87, 0x146: 0x88, 0x147: 0x89,
	0x14d: 0x8a,
	0x15c: 0x8b, 0x15f: 0x8c,
	0x162: 0x8d, 0x164: 0x8e,
	0x168: 0x8f, 0x169: 0x90, 0x16a: 0x91, 0x16c: 0x0e, 0x16d: 0x92, 0x16e: 0x93, 0x16f: 0x94,
	0x170: 0x95, 0x173: 0x96, 0x174: 0x97, 0x175: 0x0f, 0x176: 0x10, 0x177: 0x11,
	0x178: 0x12, 0x179: 0x13, 0x17a: 0x14, 0x17b: 0x15, 0x17c: 0x16, 0x17d: 0x17, 0x17e: 0x18, 0x17f: 0x19,
	// Block 0x6, offset 0x180
	0x180: 0x98, 0x181: 0x99, 0x182: 0x9a, 0x183: 0x9b, 0x184: 0x1a, 0x185: 0x1b, 0x186: 0x9c, 0x187: 0x9d,
	0x188: 0x9e, 0x189: 0x1c, 0x18a: 0x1d, 0x18b: 0x9f, 0x18c: 0xa0,
	0x191: 0x1e, 0x192: 0x1f, 0x193: 0xa1,
	0x1a8: 0xa2, 0x1a9: 0xa3, 0x1ab: 0xa4,
	0x1b1: 0xa5, 0x1b3: 0xa6, 0x1b5: 0xa7, 0x1b7: 0xa8,
	0x1ba: 0xa9, 0x1bb: 0xaa, 0x1bc: 0x20, 0x1bd: 0x21, 0x1be: 0x22, 0x1bf: 0xab,
	// Block 0x7, offset 0x1c0
	0x1c0: 0xac, 0x1c1: 0x23, 0x1c2: 0x24, 0x1c3: 0x25, 0x1c4: 0xad, 0x1c5: 0x26, 0x1c6: 0x27,
	0x1c8: 0x28, 0x1c9: 0x29, 0x1ca: 0x2a, 0x1cb: 0x2b, 0x1cc: 0x2c, 0x1cd: 0x2d, 0x1ce: 0x2e, 0x1cf: 0x2f,
	// Block 0x8, offset 0x200
	0x219: 0xae, 0x21a: 0xaf, 0x21b: 0xb0, 0x21d: 0xb1, 0x21f: 0xb2,
	0x220: 0xb3, 0x223: 0xb4, 0x224: 0xb5, 0x225: 0xb6, 0x226: 0xb7, 0x227: 0xb8,
	0x22a: 0xb9, 0x22b: 0xba, 0x22d: 0xbb, 0x22f: 0xbc,
	0x230: 0xbd, 0x231: 0xbe, 0x232: 0xbf, 0x233: 0xc0, 0x234: 0xc1, 0x235: 0xc2, 0x236: 0xc3, 0x237: 0xbd,
	0x238: 0xbe, 0x239: 0xbf, 0x23a: 0xc0, 0x23b: 0xc1, 0x23c: 0xc2, 0x23d: 0xc3, 0x23e: 0xbd, 0x23f: 0xbe,
	// Block 0x9, offset 0x240
	0x240: 0xbf, 0x241: 0xc0, 0x242: 0xc1, 0x243: 0xc2, 0x244: 0xc3, 0x245: 0xbd, 0x246: 0xbe, 0x247: 0xbf,
	0x248: 0xc0, 0x249: 0xc1, 0x24a: 0xc2, 0x24b: 0xc3, 0x24c: 0xbd, 0x24d: 0xbe, 0x24e: 0xbf, 0x24f: 0xc0,
	0x250: 0xc1, 0x251: 0xc2, 0x252: 0xc3, 0x253: 0xbd, 0x254: 0xbe, 0x255: 0xbf, 0x256: 0xc0, 0x257: 0xc1,
	0x258: 0xc2, 0x259: 0xc3, 0x25a: 0xbd, 0x25b: 0xbe, 0x25c: 0xbf, 0x25d: 0xc0, 0x25e: 0xc1, 0x25f: 0xc2,
	0x260: 0xc3, 0x261: 0xbd, 0x262: 0xbe, 0x263: 0xbf, 0x264: 0xc0, 0x265: 0xc1, 0x266: 0xc2, 0x267: 0xc3,
	0x268: 0xbd, 0x269: 0xbe, 0x26a: 0xbf, 0x26b: 0xc0, 0x26c: 0xc1, 0x26d: 0xc2, 0x26e: 0xc3, 0x26f: 0xbd,
	0x270: 0xbe, 0x271: 0xbf, 0x272: 0xc0, 0x273: 0xc1, 0x274: 0xc2, 0x275: 0xc3, 0x276: 0xbd, 0x277: 0xbe,
	0x278: 0xbf, 0x279: 0xc0, 0x27a: 0xc1, 0x27b: 0xc2, 0x27c: 0xc3, 0x27d: 0xbd, 0x27e: 0xbe, 0x27f: 0xbf,
	// Block 0xa, offset 0x280
	0x280: 0xc0, 0x281: 0xc1, 0x282: 0xc2, 0x283: 0xc3, 0x284: 0xbd, 0x285: 0xbe, 0x286: 0xbf, 0x287: 0xc0,
	0x288: 0xc1, 0x289: 0xc2, 0x28a: 0xc3, 0x28b: 0xbd, 0x28c: 0xbe, 0x28d: 0xbf, 0x28e: 0xc0, 0x28f: 0xc1,
	0x290: 0xc2, 0x291: 0xc3, 0x292: 0xbd, 0x293: 0xbe, 0x294: 0xbf, 0x295: 0xc0, 0x296: 0xc1, 0x297: 0xc2,
	0x298: 0xc3, 0x299: 0xbd, 0x29a: 0xbe, 0x29b: 0xbf, 0x29c: 0xc0, 0x29d: 0xc1, 0x29e: 0xc2, 0x29f: 0xc3,
	0x2a0: 0xbd, 0x2a1: 0xbe, 0x2a2: 0xbf, 0x2a3: 0xc0, 0x2a4: 0xc1, 0x2a5: 0xc2, 0x2a6: 0xc3, 0x2a7: 0xbd,
	0x2a8: 0xbe, 0x2a9: 0xbf, 0x2aa: 0xc0, 0x2ab: 0xc1, 0x2ac: 0xc2, 0x2ad: 0xc3, 0x2ae: 0xbd, 0x2af: 0xbe,
	0x2b0: 0xbf, 0x2b1: 0xc0, 0x2b2: 0xc1, 0x2b3: 0xc2, 0x2b4: 0xc3, 0x2b5: 0xbd, 0x2b6: 0xbe, 0x2b7: 0xbf,
	0x2b8: 0xc0, 0x2b9: 0xc1, 0x2ba: 0xc2, 0x2bb: 0xc3, 0x2bc: 0xbd, 0x2bd: 0xbe, 0x2be: 0xbf, 0x2bf: 0xc0,
	// Block 0xb, offset 0x2c0
	0x2c0: 0xc1, 0x2c1: 0xc2, 0x2c2: 0xc3, 0x2c3: 0xbd, 0x2c4: 0xbe, 0x2c5: 0xbf, 0x2c6: 0xc0, 0x2c7: 0xc1,
	0x2c8: 0xc2, 0x2c9: 0xc3, 0x2ca: 0xbd, 0x2cb: 0xbe, 0x2cc: 0xbf, 0x2cd: 0xc0, 0x2ce: 0xc1, 0x2cf: 0xc2,
	0x2d0: 0xc3, 0x2d1: 0xbd, 0x2d2: 0xbe, 0x2d3: 0xbf, 0x2d4: 0xc0, 0x2d5: 0xc1, 0x2d6: 0xc2, 0x2d7: 0xc3,
	0x2d8: 0xbd, 0x2d9: 0xbe, 0x2da: 0xbf, 0x2db: 0xc0, 0x2dc: 0xc1, 0x2dd: 0xc2, 0x2de: 0xc4,
	// Block 0xc, offset 0x300
	0x324: 0x30, 0x325: 0x31, 0x326: 0x32, 0x327: 0x33,
	0x328: 0x34, 0x329: 0x35, 0x32a: 0x36, 0x32b: 0x37, 0x32c: 0x38, 0x32d: 0x39, 0x32e: 0x3a, 0x32f: 0x3b,
	0x330: 0x3c, 0x331: 0x3d, 0x332: 0x3e, 0x333: 0x3f, 0x334: 0x40, 0x335: 0x41, 0x336: 0x42, 0x337: 0x43,
	0x338: 0x44, 0x339: 0x45, 0x33a: 0x46, 0x33b: 0x47, 0x33c: 0xc5, 0x33d: 0x48, 0x33e: 0x49, 0x33f: 0x4a,
	// Block 0xd, offset 0x340
	0x347: 0xc6,
	0x34b: 0xc7, 0x34d: 0xc8,
	0x368: 0xc9, 0x36b: 0xca,
	// Block 0xe, offset 0x380
	0x381: 0xcb, 0x382: 0xcc, 0x384: 0xcd, 0x385: 0xb7, 0x387: 0xce,
	0x388: 0xcf, 0x38b: 0xd0, 0x38c: 0x6c, 0x38d: 0xd1,
	0x391: 0xd2, 0x392: 0xd3, 0x393: 0xd4, 0x396: 0xd5, 0x397: 0xd6,
	0x398: 0xd7, 0x39a: 0xd8, 0x39c: 0xd9,
	0x3a8: 0xda, 0x3a9: 0xdb, 0x3aa: 0xdc,
	0x3b0: 0xd7, 0x3b5: 0xdd,
	// Block 0xf, offset 0x3c0
	0x3eb: 0xde, 0x3ec: 0xdf,
	// Block 0x10, offset 0x400
	0x432: 0xe0,
	// Block 0x11, offset 0x440
	0x445: 0xe1, 0x446: 0xe2, 0x447: 0xe3,
	0x449: 0xe4,
	0x450: 0xe5, 0x451: 0xe6, 0x452: 0xe7, 0x453: 0xe8, 0x454: 0xe9, 0x455: 0xea, 0x456: 0xeb, 0x457: 0xec,
	0x458: 0xed, 0x459: 0xee, 0x45a: 0x4b, 0x45b: 0xef, 0x45c: 0xf0, 0x45d: 0xf1, 0x45e: 0xf2, 0x45f: 0x4c,
	// Block 0x12, offset 0x480
	0x480: 0xf3,
	0x4a3: 0xf4, 0x4a5: 0xf5,
	0x4b8: 0x4d, 0x4b9: 0x4e, 0x4ba: 0x4f,
	// Block 0x13, offset 0x4c0
	0x4c4: 0x50, 0x4c5: 0xf6, 0x4c6: 0xf7,
	0x4c8: 0x51, 0x4c9: 0xf8,
	// Block 0x14, offset 0x500
	0x520: 0x52, 0x521: 0x53, 0x522: 0x54, 0x523: 0x55, 0x524: 0x56, 0x525: 0x57, 0x526: 0x58, 0x527: 0x59,
	0x528: 0x5a,
	// Block 0x15, offset 0x540
	0x550: 0x0b, 0x551: 0x0c, 0x556: 0x0d,
	0x55b: 0x0e, 0x55d: 0x0f, 0x55e: 0x10, 0x55f: 0x11,
	0x56f: 0x12,
}

// nfkcSparseOffset: 158 entries, 316 bytes
var nfkcSparseOffset = []uint16{0x0, 0xe, 0x12, 0x1b, 0x25, 0x35, 0x37, 0x3c, 0x47, 0x56, 0x63, 0x6b, 0x6f, 0x74, 0x76, 0x87, 0x8f, 0x96, 0x99, 0xa0, 0xa4, 0xa8, 0xaa, 0xac, 0xb5, 0xb9, 0xc0, 0xc5, 0xc8, 0xd2, 0xd5, 0xdc, 0xe4, 0xe8, 0xea, 0xed, 0xf1, 0xf7, 0x108, 0x114, 0x116, 0x11c, 0x11e, 0x120, 0x122, 0x124, 0x126, 0x128, 0x12a, 0x12d, 0x130, 0x132, 0x135, 0x138, 0x13c, 0x141, 0x14a, 0x14c, 0x14f, 0x151, 0x15c, 0x167, 0x175, 0x183, 0x193, 0x1a1, 0x1a8, 0x1ae, 0x1bd, 0x1c1, 0x1c3, 0x1c7, 0x1c9, 0x1cc, 0x1ce, 0x1d1, 0x1d3, 0x1d6, 0x1d8, 0x1da, 0x1dc, 0x1e8, 0x1f2, 0x1fc, 0x1ff, 0x203, 0x205, 0x207, 0x209, 0x20b, 0x20e, 0x210, 0x212, 0x214, 0x216, 0x21c, 0x21f, 0x223, 0x225, 0x22c, 0x232, 0x238, 0x240, 0x246, 0x24c, 0x252, 0x256, 0x258, 0x25a, 0x25c, 0x25e, 0x264, 0x267, 0x26a, 0x272, 0x279, 0x27c, 0x27f, 0x281, 0x289, 0x28c, 0x293, 0x296, 0x29c, 0x29e, 0x2a0, 0x2a3, 0x2a5, 0x2a7, 0x2a9, 0x2ab, 0x2ae, 0x2b0, 0x2b2, 0x2b4, 0x2c1, 0x2cb, 0x2cd, 0x2cf, 0x2d3, 0x2d8, 0x2e4, 0x2e9, 0x2f2, 0x2f8, 0x2fd, 0x301, 0x306, 0x30a, 0x31a, 0x328, 0x336, 0x344, 0x34a, 0x34c, 0x34f, 0x359, 0x35b}

// nfkcSparseValues: 869 entries, 3476 bytes
var nfkcSparseValues = [869]valueRange{
	// Block 0x0, offset 0x0
	{value: 0x0002, lo: 0x0d},
	{value: 0x0001, lo: 0xa0, hi: 0xa0},
	{value: 0x4278, lo: 0xa8, hi: 0xa8},
	{value: 0x0083, lo: 0xaa, hi: 0xaa},
	{value: 0x4264, lo: 0xaf, hi: 0xaf},
	{value: 0x0025, lo: 0xb2, hi: 0xb3},
	{value: 0x425a, lo: 0xb4, hi: 0xb4},
	{value: 0x01dc, lo: 0xb5, hi: 0xb5},
	{value: 0x4291, lo: 0xb8, hi: 0xb8},
	{value: 0x0023, lo: 0xb9, hi: 0xb9},
	{value: 0x009f, lo: 0xba, hi: 0xba},
	{value: 0x221c, lo: 0xbc, hi: 0xbc},
	{value: 0x2210, lo: 0xbd, hi: 0xbd},
	{value: 0x22b2, lo: 0xbe, hi: 0xbe},
	// Block 0x1, offset 0xe
	{value: 0x0091, lo: 0x03},
	{value: 0x46e2, lo: 0xa0, hi: 0xa1},
	{value: 0x4714, lo: 0xaf, hi: 0xb0},
	{value: 0xa000, lo: 0xb7, hi: 0xb7},
	// Block 0x2, offset 0x12
	{value: 0x0003, lo: 0x08},
	{value: 0xa000, lo: 0x92, hi: 0x92},
	{value: 0x0091, lo: 0xb0, hi: 0xb0},
	{value: 0x0119, lo: 0xb1, hi: 0xb1},
	{value: 0x0095, lo: 0xb2, hi: 0xb2},
	{value: 0x00a5, lo: 0xb3, hi: 0xb3},
	{value: 0x0143, lo: 0xb4, hi: 0xb6},
	{value: 0x00af, lo: 0xb7, hi: 0xb7},
	{value: 0x00b3, lo: 0xb8, hi: 0xb8},
	// Block 0x3, offset 0x1b
	{value: 0x000a, lo: 0x09},
	{value: 0x426e, lo: 0x98, hi: 0x98},
	{value: 0x4273, lo: 0x99, hi: 0x9a},
	{value: 0x4296, lo: 0x9b, hi: 0x9b},
	{value: 0x425f, lo: 0x9c, hi: 0x9c},
	{value: 0x4282, lo: 0x9d, hi: 0x9d},
	{value: 0x0113, lo: 0xa0, hi: 0xa0},
	{value: 0x0099, lo: 0xa1, hi: 0xa1},
	{value: 0x00a7, lo: 0xa2, hi: 0xa3},
	{value: 0x0167, lo: 0xa4, hi: 0xa4},
	// Block 0x4, offset 0x25
	{value: 0x0000, lo: 0x0f},
	{value: 0xa000, lo: 0x83, hi: 0x83},
	{value: 0xa000, lo: 0x87, hi: 0x87},
	{value: 0xa000, lo: 0x8b, hi: 0x8b},
	{value: 0xa000, lo: 0x8d, hi: 0x8d},
	{value: 0x37a5, lo: 0x90, hi: 0x90},
	{value: 0x37b1, lo: 0x91, hi: 0x91},
	{value: 0x379f, lo: 0x93, hi: 0x93},
	{value: 0xa000, lo: 0x96, hi: 0x96},
	{value: 0x3817, lo: 0x97, hi: 0x97},
	{value: 0x37e1, lo: 0x9c, hi: 0x9c},
	{value: 0x37c9, lo: 0x9d, hi: 0x9d},
	{value: 0x37f3, lo: 0x9e, hi: 0x9e},
	{value: 0xa000, lo: 0xb4, hi: 0xb5},
	{value: 0x381d, lo: 0xb6, hi: 0xb6},
	{value: 0x3823, lo: 0xb7, hi: 0xb7},
	// Block 0x5, offset 0x35
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0x83, hi: 0x87},
	// Block 0x6, offset 0x37
	{value: 0x0001, lo: 0x04},
	{value: 0x8113, lo: 0x81, hi: 0x82},
	{value: 0x8132, lo: 0x84, hi: 0x84},
	{value: 0x812d, lo: 0x85, hi: 0x85},
	{value: 0x810d, lo: 0x87, hi: 0x87},
	// Block 0x7, offset 0x3c
	{value: 0x0000, lo: 0x0a},
	{value: 0x8132, lo: 0x90, hi: 0x97},
	{value: 0x8119, lo: 0x98, hi: 0x98},
	{value: 0x811a, lo: 0x99, hi: 0x99},
	{value: 0x811b, lo: 0x9a, hi: 0x9a},
	{value: 0x3841, lo: 0xa2, hi: 0xa2},
	{value: 0x3847, lo: 0xa3, hi: 0xa3},
	{value: 0x3853, lo: 0xa4, hi: 0xa4},
	{value: 0x384d, lo: 0xa5, hi: 0xa5},
	{value: 0x3859, lo: 0xa6, hi: 0xa6},
	{value: 0xa000, lo: 0xa7, hi: 0xa7},
	// Block 0x8, offset 0x47
	{value: 0x0000, lo: 0x0e},
	{value: 0x386b, lo: 0x80, hi: 0x80},
	{value: 0xa000, lo: 0x81, hi: 0x81},
	{value: 0x385f, lo: 0x82, hi: 0x82},
	{value: 0xa000, lo: 0x92, hi: 0x92},
	{value: 0x3865, lo: 0x93, hi: 0x93},
	{value: 0xa000, lo: 0x95, hi: 0x95},
	{value: 0x8132, lo: 0x96, hi: 0x9c},
	{value: 0x8132, lo: 0x9f, hi: 0xa2},
	{value: 0x812d, lo: 0xa3, hi: 0xa3},
	{value: 0x8132, lo: 0xa4, hi: 0xa4},
	{value: 0x8132, lo: 0xa7, hi: 0xa8},
	{value: 0x812d, lo: 0xaa, hi: 0xaa},
	{value: 0x8132, lo: 0xab, hi: 0xac},
	{value: 0x812d, lo: 0xad, hi: 0xad},
	// Block 0x9, offset 0x56
	{value: 0x0000, lo: 0x0c},
	{value: 0x811f, lo: 0x91, hi: 0x91},
	{value: 0x8132, lo: 0xb0, hi: 0xb0},
	{value: 0x812d, lo: 0xb1, hi: 0xb1},
	{value: 0x8132, lo: 0xb2, hi: 0xb3},
	{value: 0x812d, lo: 0xb4, hi: 0xb4},
	{value: 0x8132, lo: 0xb5, hi: 0xb6},
	{value: 0x812d, lo: 0xb7, hi: 0xb9},
	{value: 0x8132, lo: 0xba, hi: 0xba},
	{value: 0x812d, lo: 0xbb, hi: 0xbc},
	{value: 0x8132, lo: 0xbd, hi: 0xbd},
	{value: 0x812d, lo: 0xbe, hi: 0xbe},
	{value: 0x8132, lo: 0xbf, hi: 0xbf},
	// Block 0xa, offset 0x63
	{value: 0x0005, lo: 0x07},
	{value: 0x8132, lo: 0x80, hi: 0x80},
	{value: 0x8132, lo: 0x81, hi: 0x81},
	{value: 0x812d, lo: 0x82, hi: 0x83},
	{value: 0x812d, lo: 0x84, hi: 0x85},
	{value: 0x812d, lo: 0x86, hi: 0x87},
	{value: 0x812d, lo: 0x88, hi: 0x89},
	{value: 0x8132, lo: 0x8a, hi: 0x8a},
	// Block 0xb, offset 0x6b
	{value: 0x0000, lo: 0x03},
	{value: 0x8132, lo: 0xab, hi: 0xb1},
	{value: 0x812d, lo: 0xb2, hi: 0xb2},
	{value: 0x8132, lo: 0xb3, hi: 0xb3},
	// Block 0xc, offset 0x6f
	{value: 0x0000, lo: 0x04},
	{value: 0x8132, lo: 0x96, hi: 0x99},
	{value: 0x8132, lo: 0x9b, hi: 0xa3},
	{value: 0x8132, lo: 0xa5, hi: 0xa7},
	{value: 0x8132, lo: 0xa9, hi: 0xad},
	// Block 0xd, offset 0x74
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0x99, hi: 0x9b},
	// Block 0xe, offset 0x76
	{value: 0x0000, lo: 0x10},
	{value: 0x8132, lo: 0x94, hi: 0xa1},
	{value: 0x812d, lo: 0xa3, hi: 0xa3},
	{value: 0x8132, lo: 0xa4, hi: 0xa5},
	{value: 0x812d, lo: 0xa6, hi: 0xa6},
	{value: 0x8132, lo: 0xa7, hi: 0xa8},
	{value: 0x812d, lo: 0xa9, hi: 0xa9},
	{value: 0x8132, lo: 0xaa, hi: 0xac},
	{value: 0x812d, lo: 0xad, hi: 0xaf},
	{value: 0x8116, lo: 0xb0, hi: 0xb0},
	{value: 0x8117, lo: 0xb1, hi: 0xb1},
	{value: 0x8118, lo: 0xb2, hi: 0xb2},
	{value: 0x8132, lo: 0xb3, hi: 0xb5},
	{value: 0x812d, lo: 0xb6, hi: 0xb6},
	{value: 0x8132, lo: 0xb7, hi: 0xb8},
	{value: 0x812d, lo: 0xb9, hi: 0xba},
	{value: 0x8132, lo: 0xbb, hi: 0xbf},
	// Block 0xf, offset 0x87
	{value: 0x0000, lo: 0x07},
	{value: 0xa000, lo: 0xa8, hi: 0xa8},
	{value: 0x3ed8, lo: 0xa9, hi: 0xa9},
	{value: 0xa000, lo: 0xb0, hi: 0xb0},
	{value: 0x3ee0, lo: 0xb1, hi: 0xb1},
	{value: 0xa000, lo: 0xb3, hi: 0xb3},
	{value: 0x3ee8, lo: 0xb4, hi: 0xb4},
	{value: 0x9902, lo: 0xbc, hi: 0xbc},
	// Block 0x10, offset 0x8f
	{value: 0x0008, lo: 0x06},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x8132, lo: 0x91, hi: 0x91},
	{value: 0x812d, lo: 0x92, hi: 0x92},
	{value: 0x8132, lo: 0x93, hi: 0x93},
	{value: 0x8132, lo: 0x94, hi: 0x94},
	{value: 0x451c, lo: 0x98, hi: 0x9f},
	// Block 0x11, offset 0x96
	{value: 0x0000, lo: 0x02},
	{value: 0x8102, lo: 0xbc, hi: 0xbc},
	{value: 0x9900, lo: 0xbe, hi: 0xbe},
	// Block 0x12, offset 0x99
	{value: 0x0008, lo: 0x06},
	{value: 0xa000, lo: 0x87, hi: 0x87},
	{value: 0x2c9e, lo: 0x8b, hi: 0x8c},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x9900, lo: 0x97, hi: 0x97},
	{value: 0x455c, lo: 0x9c, hi: 0x9d},
	{value: 0x456c, lo: 0x9f, hi: 0x9f},
	// Block 0x13, offset 0xa0
	{value: 0x0000, lo: 0x03},
	{value: 0x4594, lo: 0xb3, hi: 0xb3},
	{value: 0x459c, lo: 0xb6, hi: 0xb6},
	{value: 0x8102, lo: 0xbc, hi: 0xbc},
	// Block 0x14, offset 0xa4
	{value: 0x0008, lo: 0x03},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x4574, lo: 0x99, hi: 0x9b},
	{value: 0x458c, lo: 0x9e, hi: 0x9e},
	// Block 0x15, offset 0xa8
	{value: 0x0000, lo: 0x01},
	{value: 0x8102, lo: 0xbc, hi: 0xbc},
	// Block 0x16, offset 0xaa
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	// Block 0x17, offset 0xac
	{value: 0x0000, lo: 0x08},
	{value: 0xa000, lo: 0x87, hi: 0x87},
	{value: 0x2cb6, lo: 0x88, hi: 0x88},
	{value: 0x2cae, lo: 0x8b, hi: 0x8b},
	{value: 0x2cbe, lo: 0x8c, hi: 0x8c},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x9900, lo: 0x96, hi: 0x97},
	{value: 0x45a4, lo: 0x9c, hi: 0x9c},
	{value: 0x45ac, lo: 0x9d, hi: 0x9d},
	// Block 0x18, offset 0xb5
	{value: 0x0000, lo: 0x03},
	{value: 0xa000, lo: 0x92, hi: 0x92},
	{value: 0x2cc6, lo: 0x94, hi: 0x94},
	{value: 0x9900, lo: 0xbe, hi: 0xbe},
	// Block 0x19, offset 0xb9
	{value: 0x0000, lo: 0x06},
	{value: 0xa000, lo: 0x86, hi: 0x87},
	{value: 0x2cce, lo: 0x8a, hi: 0x8a},
	{value: 0x2cde, lo: 0x8b, hi: 0x8b},
	{value: 0x2cd6, lo: 0x8c, hi: 0x8c},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x9900, lo: 0x97, hi: 0x97},
	// Block 0x1a, offset 0xc0
	{value: 0x1801, lo: 0x04},
	{value: 0xa000, lo: 0x86, hi: 0x86},
	{value: 0x3ef0, lo: 0x88, hi: 0x88},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x8120, lo: 0x95, hi: 0x96},
	// Block 0x1b, offset 0xc5
	{value: 0x0000, lo: 0x02},
	{value: 0x8102, lo: 0xbc, hi: 0xbc},
	{value: 0xa000, lo: 0xbf, hi: 0xbf},
	// Block 0x1c, offset 0xc8
	{value: 0x0000, lo: 0x09},
	{value: 0x2ce6, lo: 0x80, hi: 0x80},
	{value: 0x9900, lo: 0x82, hi: 0x82},
	{value: 0xa000, lo: 0x86, hi: 0x86},
	{value: 0x2cee, lo: 0x87, hi: 0x87},
	{value: 0x2cf6, lo: 0x88, hi: 0x88},
	{value: 0x2f50, lo: 0x8a, hi: 0x8a},
	{value: 0x2dd8, lo: 0x8b, hi: 0x8b},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x9900, lo: 0x95, hi: 0x96},
	// Block 0x1d, offset 0xd2
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0xbb, hi: 0xbc},
	{value: 0x9900, lo: 0xbe, hi: 0xbe},
	// Block 0x1e, offset 0xd5
	{value: 0x0000, lo: 0x06},
	{value: 0xa000, lo: 0x86, hi: 0x87},
	{value: 0x2cfe, lo: 0x8a, hi: 0x8a},
	{value: 0x2d0e, lo: 0x8b, hi: 0x8b},
	{value: 0x2d06, lo: 0x8c, hi: 0x8c},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x9900, lo: 0x97, hi: 0x97},
	// Block 0x1f, offset 0xdc
	{value: 0x6bea, lo: 0x07},
	{value: 0x9904, lo: 0x8a, hi: 0x8a},
	{value: 0x9900, lo: 0x8f, hi: 0x8f},
	{value: 0xa000, lo: 0x99, hi: 0x99},
	{value: 0x3ef8, lo: 0x9a, hi: 0x9a},
	{value: 0x2f58, lo: 0x9c, hi: 0x9c},
	{value: 0x2de3, lo: 0x9d, hi: 0x9d},
	{value: 0x2d16, lo: 0x9e, hi: 0x9f},
	// Block 0x20, offset 0xe4
	{value: 0x0000, lo: 0x03},
	{value: 0x2621, lo: 0xb3, hi: 0xb3},
	{value: 0x8122, lo: 0xb8, hi: 0xb9},
	{value: 0x8104, lo: 0xba, hi: 0xba},
	// Block 0x21, offset 0xe8
	{value: 0x0000, lo: 0x01},
	{value: 0x8123, lo: 0x88, hi: 0x8b},
	// Block 0x22, offset 0xea
	{value: 0x0000, lo: 0x02},
	{value: 0x2636, lo: 0xb3, hi: 0xb3},
	{value: 0x8124, lo: 0xb8, hi: 0xb9},
	// Block 0x23, offset 0xed
	{value: 0x0000, lo: 0x03},
	{value: 0x8125, lo: 0x88, hi: 0x8b},
	{value: 0x2628, lo: 0x9c, hi: 0x9c},
	{value: 0x262f, lo: 0x9d, hi: 0x9d},
	// Block 0x24, offset 0xf1
	{value: 0x0000, lo: 0x05},
	{value: 0x030b, lo: 0x8c, hi: 0x8c},
	{value: 0x812d, lo: 0x98, hi: 0x99},
	{value: 0x812d, lo: 0xb5, hi: 0xb5},
	{value: 0x812d, lo: 0xb7, hi: 0xb7},
	{value: 0x812b, lo: 0xb9, hi: 0xb9},
	// Block 0x25, offset 0xf7
	{value: 0x0000, lo: 0x10},
	{value: 0x2644, lo: 0x83, hi: 0x83},
	{value: 0x264b, lo: 0x8d, hi: 0x8d},
	{value: 0x2652, lo: 0x92, hi: 0x92},
	{value: 0x2659, lo: 0x97, hi: 0x97},
	{value: 0x2660, lo: 0x9c, hi: 0x9c},
	{value: 0x263d, lo: 0xa9, hi: 0xa9},
	{value: 0x8126, lo: 0xb1, hi: 0xb1},
	{value: 0x8127, lo: 0xb2, hi: 0xb2},
	{value: 0x4a84, lo: 0xb3, hi: 0xb3},
	{value: 0x8128, lo: 0xb4, hi: 0xb4},
	{value: 0x4a8d, lo: 0xb5, hi: 0xb5},
	{value: 0x45b4, lo: 0xb6, hi: 0xb6},
	{value: 0x45f4, lo: 0xb7, hi: 0xb7},
	{value: 0x45bc, lo: 0xb8, hi: 0xb8},
	{value: 0x45ff, lo: 0xb9, hi: 0xb9},
	{value: 0x8127, lo: 0xba, hi: 0xbd},
	// Block 0x26, offset 0x108
	{value: 0x0000, lo: 0x0b},
	{value: 0x8127, lo: 0x80, hi: 0x80},
	{value: 0x4a96, lo: 0x81, hi: 0x81},
	{value: 0x8132, lo: 0x82, hi: 0x83},
	{value: 0x8104, lo: 0x84, hi: 0x84},
	{value: 0x8132, lo: 0x86, hi: 0x87},
	{value: 0x266e, lo: 0x93, hi: 0x93},
	{value: 0x2675, lo: 0x9d, hi: 0x9d},
	{value: 0x267c, lo: 0xa2, hi: 0xa2},
	{value: 0x2683, lo: 0xa7, hi: 0xa7},
	{value: 0x268a, lo: 0xac, hi: 0xac},
	{value: 0x2667, lo: 0xb9, hi: 0xb9},
	// Block 0x27, offset 0x114
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0x86, hi: 0x86},
	// Block 0x28, offset 0x116
	{value: 0x0000, lo: 0x05},
	{value: 0xa000, lo: 0xa5, hi: 0xa5},
	{value: 0x2d1e, lo: 0xa6, hi: 0xa6},
	{value: 0x9900, lo: 0xae, hi: 0xae},
	{value: 0x8102, lo: 0xb7, hi: 0xb7},
	{value: 0x8104, lo: 0xb9, hi: 0xba},
	// Block 0x29, offset 0x11c
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0x8d, hi: 0x8d},
	// Block 0x2a, offset 0x11e
	{value: 0x0000, lo: 0x01},
	{value: 0x030f, lo: 0xbc, hi: 0xbc},
	// Block 0x2b, offset 0x120
	{value: 0x0000, lo: 0x01},
	{value: 0xa000, lo: 0x80, hi: 0x92},
	// Block 0x2c, offset 0x122
	{value: 0x0000, lo: 0x01},
	{value: 0xb900, lo: 0xa1, hi: 0xb5},
	// Block 0x2d, offset 0x124
	{value: 0x0000, lo: 0x01},
	{value: 0x9900, lo: 0xa8, hi: 0xbf},
	// Block 0x2e, offset 0x126
	{value: 0x0000, lo: 0x01},
	{value: 0x9900, lo: 0x80, hi: 0x82},
	// Block 0x2f, offset 0x128
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0x9d, hi: 0x9f},
	// Block 0x30, offset 0x12a
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x94, hi: 0x94},
	{value: 0x8104, lo: 0xb4, hi: 0xb4},
	// Block 0x31, offset 0x12d
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x92, hi: 0x92},
	{value: 0x8132, lo: 0x9d, hi: 0x9d},
	// Block 0x32, offset 0x130
	{value: 0x0000, lo: 0x01},
	{value: 0x8131, lo: 0xa9, hi: 0xa9},
	// Block 0x33, offset 0x132
	{value: 0x0004, lo: 0x02},
	{value: 0x812e, lo: 0xb9, hi: 0xba},
	{value: 0x812d, lo: 0xbb, hi: 0xbb},
	// Block 0x34, offset 0x135
	{value: 0x0000, lo: 0x02},
	{value: 0x8132, lo: 0x97, hi: 0x97},
	{value: 0x812d, lo: 0x98, hi: 0x98},
	// Block 0x35, offset 0x138
	{value: 0x0000, lo: 0x03},
	{value: 0x8104, lo: 0xa0, hi: 0xa0},
	{value: 0x8132, lo: 0xb5, hi: 0xbc},
	{value: 0x812d, lo: 0xbf, hi: 0xbf},
	// Block 0x36, offset 0x13c
	{value: 0x0000, lo: 0x04},
	{value: 0x8132, lo: 0xb0, hi: 0xb4},
	{value: 0x812d, lo: 0xb5, hi: 0xba},
	{value: 0x8132, lo: 0xbb, hi: 0xbc},
	{value: 0x812d, lo: 0xbd, hi: 0xbd},
	// Block 0x37, offset 0x141
	{value: 0x0000, lo: 0x08},
	{value: 0x2d66, lo: 0x80, hi: 0x80},
	{value: 0x2d6e, lo: 0x81, hi: 0x81},
	{value: 0xa000, lo: 0x82, hi: 0x82},
	{value: 0x2d76, lo: 0x83, hi: 0x83},
	{value: 0x8104, lo: 0x84, hi: 0x84},
	{value: 0x8132, lo: 0xab, hi: 0xab},
	{value: 0x812d, lo: 0xac, hi: 0xac},
	{value: 0x8132, lo: 0xad, hi: 0xb3},
	// Block 0x38, offset 0x14a
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0xaa, hi: 0xab},
	// Block 0x39, offset 0x14c
	{value: 0x0000, lo: 0x02},
	{value: 0x8102, lo: 0xa6, hi: 0xa6},
	{value: 0x8104, lo: 0xb2, hi: 0xb3},
	// Block 0x3a, offset 0x14f
	{value: 0x0000, lo: 0x01},
	{value: 0x8102, lo: 0xb7, hi: 0xb7},
	// Block 0x3b, offset 0x151
	{value: 0x0000, lo: 0x0a},
	{value: 0x8132, lo: 0x90, hi: 0x92},
	{value: 0x8101, lo: 0x94, hi: 0x94},
	{value: 0x812d, lo: 0x95, hi: 0x99},
	{value: 0x8132, lo: 0x9a, hi: 0x9b},
	{value: 0x812d, lo: 0x9c, hi: 0x9f},
	{value: 0x8132, lo: 0xa0, hi: 0xa0},
	{value: 0x8101, lo: 0xa2, hi: 0xa8},
	{value: 0x812d, lo: 0xad, hi: 0xad},
	{value: 0x8132, lo: 0xb4, hi: 0xb4},
	{value: 0x8132, lo: 0xb8, hi: 0xb9},
	// Block 0x3c, offset 0x15c
	{value: 0x0002, lo: 0x0a},
	{value: 0x0043, lo: 0xac, hi: 0xac},
	{value: 0x00d1, lo: 0xad, hi: 0xad},
	{value: 0x0045, lo: 0xae, hi: 0xae},
	{value: 0x0049, lo: 0xb0, hi: 0xb1},
	{value: 0x00e6, lo: 0xb2, hi: 0xb2},
	{value: 0x004f, lo: 0xb3, hi: 0xba},
	{value: 0x005f, lo: 0xbc, hi: 0xbc},
	{value: 0x00ef, lo: 0xbd, hi: 0xbd},
	{value: 0x0061, lo: 0xbe, hi: 0xbe},
	{value: 0x0065, lo: 0xbf, hi: 0xbf},
	// Block 0x3d, offset 0x167
	{value: 0x0000, lo: 0x0d},
	{value: 0x0001, lo: 0x80, hi: 0x8a},
	{value: 0x043b, lo: 0x91, hi: 0x91},
	{value: 0x429b, lo: 0x97, hi: 0x97},
	{value: 0x001d, lo: 0xa4, hi: 0xa4},
	{value: 0x1873, lo: 0xa5, hi: 0xa5},
	{value: 0x1b5c, lo: 0xa6, hi: 0xa6},
	{value: 0x0001, lo: 0xaf, hi: 0xaf},
	{value: 0x2691, lo: 0xb3, hi: 0xb3},
	{value: 0x27fe, lo: 0xb4, hi: 0xb4},
	{value: 0x2698, lo: 0xb6, hi: 0xb6},
	{value: 0x2808, lo: 0xb7, hi: 0xb7},
	{value: 0x186d, lo: 0xbc, hi: 0xbc},
	{value: 0x4269, lo: 0xbe, hi: 0xbe},
	// Block 0x3e, offset 0x175
	{value: 0x0002, lo: 0x0d},
	{value: 0x1933, lo: 0x87, hi: 0x87},
	{value: 0x1930, lo: 0x88, hi: 0x88},
	{value: 0x1870, lo: 0x89, hi: 0x89},
	{value: 0x298e, lo: 0x97, hi: 0x97},
	{value: 0x0001, lo: 0x9f, hi: 0x9f},
	{value: 0x0021, lo: 0xb0, hi: 0xb0},
	{value: 0x0093, lo: 0xb1, hi: 0xb1},
	{value: 0x0029, lo: 0xb4, hi: 0xb9},
	{value: 0x0017, lo: 0xba, hi: 0xba},
	{value: 0x0467, lo: 0xbb, hi: 0xbb},
	{value: 0x003b, lo: 0xbc, hi: 0xbc},
	{value: 0x0011, lo: 0xbd, hi: 0xbe},
	{value: 0x009d, lo: 0xbf, hi: 0xbf},
	// Block 0x3f, offset 0x183
	{value: 0x0002, lo: 0x0f},
	{value: 0x0021, lo: 0x80, hi: 0x89},
	{value: 0x0017, lo: 0x8a, hi: 0x8a},
	{value: 0x0467, lo: 0x8b, hi: 0x8b},
	{value: 0x003b, lo: 0x8c, hi: 0x8c},
	{value: 0x0011, lo: 0x8d, hi: 0x8e},
	{value: 0x0083, lo: 0x90, hi: 0x90},
	{value: 0x008b, lo: 0x91, hi: 0x91},
	{value: 0x009f, lo: 0x92, hi: 0x92},
	{value: 0x00b1, lo: 0x93, hi: 0x93},
	{value: 0x0104, lo: 0x94, hi: 0x94},
	{value: 0x0091, lo: 0x95, hi: 0x95},
	{value: 0x0097, lo: 0x96, hi: 0x99},
	{value: 0x00a1, lo: 0x9a, hi: 0x9a},
	{value: 0x00a7, lo: 0x9b, hi: 0x9c},
	{value: 0x1999, lo: 0xa8, hi: 0xa8},
	// Block 0x40, offset 0x193
	{value: 0x0000, lo: 0x0d},
	{value: 0x8132, lo: 0x90, hi: 0x91},
	{value: 0x8101, lo: 0x92, hi: 0x93},
	{value: 0x8132, lo: 0x94, hi: 0x97},
	{value: 0x8101, lo: 0x98, hi: 0x9a},
	{value: 0x8132, lo: 0x9b, hi: 0x9c},
	{value: 0x8132, lo: 0xa1, hi: 0xa1},
	{value: 0x8101, lo: 0xa5, hi: 0xa6},
	{value: 0x8132, lo: 0xa7, hi: 0xa7},
	{value: 0x812d, lo: 0xa8, hi: 0xa8},
	{value: 0x8132, lo: 0xa9, hi: 0xa9},
	{value: 0x8101, lo: 0xaa, hi: 0xab},
	{value: 0x812d, lo: 0xac, hi: 0xaf},
	{value: 0x8132, lo: 0xb0, hi: 0xb0},
	// Block 0x41, offset 0x1a1
	{value: 0x0007, lo: 0x06},
	{value: 0x2180, lo: 0x89, hi: 0x89},
	{value: 0xa000, lo: 0x90, hi: 0x90},
	{value: 0xa000, lo: 0x92, hi: 0x92},
	{value: 0xa000, lo: 0x94, hi: 0x94},
	{value: 0x3bb9, lo: 0x9a, hi: 0x9b},
	{value: 0x3bc7, lo: 0xae, hi: 0xae},
	// Block 0x42, offset 0x1a8
	{value: 0x000e, lo: 0x05},
	{value: 0x3bce, lo: 0x8d, hi: 0x8e},
	{value: 0x3bd5, lo: 0x8f, hi: 0x8f},
	{value: 0xa000, lo: 0x90, hi: 0x90},
	{value: 0xa000, lo: 0x92, hi: 0x92},
	{value: 0xa000, lo: 0x94, hi: 0x94},
	// Block 0x43, offset 0x1ae
	{value: 0x0173, lo: 0x0e},
	{value: 0xa000, lo: 0x83, hi: 0x83},
	{value: 0x3be3, lo: 0x84, hi: 0x84},
	{value: 0xa000, lo: 0x88, hi: 0x88},
	{value: 0x3bea, lo: 0x89, hi: 0x89},
	{value: 0xa000, lo: 0x8b, hi: 0x8b},
	{value: 0x3bf1, lo: 0x8c, hi: 0x8c},
	{value: 0xa000, lo: 0xa3, hi: 0xa3},
	{value: 0x3bf8, lo: 0xa4, hi: 0xa4},
	{value: 0xa000, lo: 0xa5, hi: 0xa5},
	{value: 0x3bff, lo: 0xa6, hi: 0xa6},
	{value: 0x269f, lo: 0xac, hi: 0xad},
	{value: 0x26a6, lo: 0xaf, hi: 0xaf},
	{value: 0x281c, lo: 0xb0, hi: 0xb0},
	{value: 0xa000, lo: 0xbc, hi: 0xbc},
	// Block 0x44, offset 0x1bd
	{value: 0x0007, lo: 0x03},
	{value: 0x3c68, lo: 0xa0, hi: 0xa1},
	{value: 0x3c92, lo: 0xa2, hi: 0xa3},
	{value: 0x3cbc, lo: 0xaa, hi: 0xad},
	// Block 0x45, offset 0x1c1
	{value: 0x0004, lo: 0x01},
	{value: 0x048b, lo: 0xa9, hi: 0xaa},
	// Block 0x46, offset 0x1c3
	{value: 0x0002, lo: 0x03},
	{value: 0x0057, lo: 0x80, hi: 0x8f},
	{value: 0x0083, lo: 0x90, hi: 0xa9},
	{value: 0x0021, lo: 0xaa, hi: 0xaa},
	// Block 0x47, offset 0x1c7
	{value: 0x0000, lo: 0x01},
	{value: 0x299b, lo: 0x8c, hi: 0x8c},
	// Block 0x48, offset 0x1c9
	{value: 0x0263, lo: 0x02},
	{value: 0x1b8c, lo: 0xb4, hi: 0xb4},
	{value: 0x192d, lo: 0xb5, hi: 0xb6},
	// Block 0x49, offset 0x1cc
	{value: 0x0000, lo: 0x01},
	{value: 0x44dd, lo: 0x9c, hi: 0x9c},
	// Block 0x4a, offset 0x1ce
	{value: 0x0000, lo: 0x02},
	{value: 0x0095, lo: 0xbc, hi: 0xbc},
	{value: 0x006d, lo: 0xbd, hi: 0xbd},
	// Block 0x4b, offset 0x1d1
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0xaf, hi: 0xb1},
	// Block 0x4c, offset 0x1d3
	{value: 0x0000, lo: 0x02},
	{value: 0x047f, lo: 0xaf, hi: 0xaf},
	{value: 0x8104, lo: 0xbf, hi: 0xbf},
	// Block 0x4d, offset 0x1d6
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0xa0, hi: 0xbf},
	// Block 0x4e, offset 0x1d8
	{value: 0x0000, lo: 0x01},
	{value: 0x0dc3, lo: 0x9f, hi: 0x9f},
	// Block 0x4f, offset 0x1da
	{value: 0x0000, lo: 0x01},
	{value: 0x162f, lo: 0xb3, hi: 0xb3},
	// Block 0x50, offset 0x1dc
	{value: 0x0004, lo: 0x0b},
	{value: 0x1597, lo: 0x80, hi: 0x82},
	{value: 0x15af, lo: 0x83, hi: 0x83},
	{value: 0x15c7, lo: 0x84, hi: 0x85},
	{value: 0x15d7, lo: 0x86, hi: 0x89},
	{value: 0x15eb, lo: 0x8a, hi: 0x8c},
	{value: 0x15ff, lo: 0x8d, hi: 0x8d},
	{value: 0x1607, lo: 0x8e, hi: 0x8e},
	{value: 0x160f, lo: 0x8f, hi: 0x90},
	{value: 0x161b, lo: 0x91, hi: 0x93},
	{value: 0x162b, lo: 0x94, hi: 0x94},
	{value: 0x1633, lo: 0x95, hi: 0x95},
	// Block 0x51, offset 0x1e8
	{value: 0x0004, lo: 0x09},
	{value: 0x0001, lo: 0x80, hi: 0x80},
	{value: 0x812c, lo: 0xaa, hi: 0xaa},
	{value: 0x8131, lo: 0xab, hi: 0xab},
	{value: 0x8133, lo: 0xac, hi: 0xac},
	{value: 0x812e, lo: 0xad, hi: 0xad},
	{value: 0x812f, lo: 0xae, hi: 0xae},
	{value: 0x812f, lo: 0xaf, hi: 0xaf},
	{value: 0x04b3, lo: 0xb6, hi: 0xb6},
	{value: 0x0887, lo: 0xb8, hi: 0xba},
	// Block 0x52, offset 0x1f2
	{value: 0x0006, lo: 0x09},
	{value: 0x0313, lo: 0xb1, hi: 0xb1},
	{value: 0x0317, lo: 0xb2, hi: 0xb2},
	{value: 0x4a3b, lo: 0xb3, hi: 0xb3},
	{value: 0x031b, lo: 0xb4, hi: 0xb4},
	{value: 0x4a41, lo: 0xb5, hi: 0xb6},
	{value: 0x031f, lo: 0xb7, hi: 0xb7},
	{value: 0x0323, lo: 0xb8, hi: 0xb8},
	{value: 0x0327, lo: 0xb9, hi: 0xb9},
	{value: 0x4a4d, lo: 0xba, hi: 0xbf},
	// Block 0x53, offset 0x1fc
	{value: 0x0000, lo: 0x02},
	{value: 0x8132, lo: 0xaf, hi: 0xaf},
	{value: 0x8132, lo: 0xb4, hi: 0xbd},
	// Block 0x54, offset 0x1ff
	{value: 0x0000, lo: 0x03},
	{value: 0x020f, lo: 0x9c, hi: 0x9c},
	{value: 0x0212, lo: 0x9d, hi: 0x9d},
	{value: 0x8132, lo: 0x9e, hi: 0x9f},
	// Block 0x55, offset 0x203
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0xb0, hi: 0xb1},
	// Block 0x56, offset 0x205
	{value: 0x0000, lo: 0x01},
	{value: 0x163b, lo: 0xb0, hi: 0xb0},
	// Block 0x57, offset 0x207
	{value: 0x000c, lo: 0x01},
	{value: 0x00d7, lo: 0xb8, hi: 0xb9},
	// Block 0x58, offset 0x209
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x86, hi: 0x86},
	// Block 0x59, offset 0x20b
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x84, hi: 0x84},
	{value: 0x8132, lo: 0xa0, hi: 0xb1},
	// Block 0x5a, offset 0x20e
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0xab, hi: 0xad},
	// Block 0x5b, offset 0x210
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x93, hi: 0x93},
	// Block 0x5c, offset 0x212
	{value: 0x0000, lo: 0x01},
	{value: 0x8102, lo: 0xb3, hi: 0xb3},
	// Block 0x5d, offset 0x214
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x80, hi: 0x80},
	// Block 0x5e, offset 0x216
	{value: 0x0000, lo: 0x05},
	{value: 0x8132, lo: 0xb0, hi: 0xb0},
	{value: 0x8132, lo: 0xb2, hi: 0xb3},
	{value: 0x812d, lo: 0xb4, hi: 0xb4},
	{value: 0x8132, lo: 0xb7, hi: 0xb8},
	{value: 0x8132, lo: 0xbe, hi: 0xbf},
	// Block 0x5f, offset 0x21c
	{value: 0x0000, lo: 0x02},
	{value: 0x8132, lo: 0x81, hi: 0x81},
	{value: 0x8104, lo: 0xb6, hi: 0xb6},
	// Block 0x60, offset 0x21f
	{value: 0x0008, lo: 0x03},
	{value: 0x1637, lo: 0x9c, hi: 0x9d},
	{value: 0x0125, lo: 0x9e, hi: 0x9e},
	{value: 0x1643, lo: 0x9f, hi: 0x9f},
	// Block 0x61, offset 0x223
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0xad, hi: 0xad},
	// Block 0x62, offset 0x225
	{value: 0x0000, lo: 0x06},
	{value: 0xe500, lo: 0x80, hi: 0x80},
	{value: 0xc600, lo: 0x81, hi: 0x9b},
	{value: 0xe500, lo: 0x9c, hi: 0x9c},
	{value: 0xc600, lo: 0x9d, hi: 0xb7},
	{value: 0xe500, lo: 0xb8, hi: 0xb8},
	{value: 0xc600, lo: 0xb9, hi: 0xbf},
	// Block 0x63, offset 0x22c
	{value: 0x0000, lo: 0x05},
	{value: 0xc600, lo: 0x80, hi: 0x93},
	{value: 0xe500, lo: 0x94, hi: 0x94},
	{value: 0xc600, lo: 0x95, hi: 0xaf},
	{value: 0xe500, lo: 0xb0, hi: 0xb0},
	{value: 0xc600, lo: 0xb1, hi: 0xbf},
	// Block 0x64, offset 0x232
	{value: 0x0000, lo: 0x05},
	{value: 0xc600, lo: 0x80, hi: 0x8b},
	{value: 0xe500, lo: 0x8c, hi: 0x8c},
	{value: 0xc600, lo: 0x8d, hi: 0xa7},
	{value: 0xe500, lo: 0xa8, hi: 0xa8},
	{value: 0xc600, lo: 0xa9, hi: 0xbf},
	// Block 0x65, offset 0x238
	{value: 0x0000, lo: 0x07},
	{value: 0xc600, lo: 0x80, hi: 0x83},
	{value: 0xe500, lo: 0x84, hi: 0x84},
	{value: 0xc600, lo: 0x85, hi: 0x9f},
	{value: 0xe500, lo: 0xa0, hi: 0xa0},
	{value: 0xc600, lo: 0xa1, hi: 0xbb},
	{value: 0xe500, lo: 0xbc, hi: 0xbc},
	{value: 0xc600, lo: 0xbd, hi: 0xbf},
	// Block 0x66, offset 0x240
	{value: 0x0000, lo: 0x05},
	{value: 0xc600, lo: 0x80, hi: 0x97},
	{value: 0xe500, lo: 0x98, hi: 0x98},
	{value: 0xc600, lo: 0x99, hi: 0xb3},
	{value: 0xe500, lo: 0xb4, hi: 0xb4},
	{value: 0xc600, lo: 0xb5, hi: 0xbf},
	// Block 0x67, offset 0x246
	{value: 0x0000, lo: 0x05},
	{value: 0xc600, lo: 0x80, hi: 0x8f},
	{value: 0xe500, lo: 0x90, hi: 0x90},
	{value: 0xc600, lo: 0x91, hi: 0xab},
	{value: 0xe500, lo: 0xac, hi: 0xac},
	{value: 0xc600, lo: 0xad, hi: 0xbf},
	// Block 0x68, offset 0x24c
	{value: 0x0000, lo: 0x05},
	{value: 0xc600, lo: 0x80, hi: 0x87},
	{value: 0xe500, lo: 0x88, hi: 0x88},
	{value: 0xc600, lo: 0x89, hi: 0xa3},
	{value: 0xe500, lo: 0xa4, hi: 0xa4},
	{value: 0xc600, lo: 0xa5, hi: 0xbf},
	// Block 0x69, offset 0x252
	{value: 0x0000, lo: 0x03},
	{value: 0xc600, lo: 0x80, hi: 0x87},
	{value: 0xe500, lo: 0x88, hi: 0x88},
	{value: 0xc600, lo: 0x89, hi: 0xa3},
	// Block 0x6a, offset 0x256
	{value: 0x0002, lo: 0x01},
	{value: 0x0003, lo: 0x81, hi: 0xbf},
	// Block 0x6b, offset 0x258
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0xbd, hi: 0xbd},
	// Block 0x6c, offset 0x25a
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0xa0, hi: 0xa0},
	// Block 0x6d, offset 0x25c
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0xb6, hi: 0xba},
	// Block 0x6e, offset 0x25e
	{value: 0x002c, lo: 0x05},
	{value: 0x812d, lo: 0x8d, hi: 0x8d},
	{value: 0x8132, lo: 0x8f, hi: 0x8f},
	{value: 0x8132, lo: 0xb8, hi: 0xb8},
	{value: 0x8101, lo: 0xb9, hi: 0xba},
	{value: 0x8104, lo: 0xbf, hi: 0xbf},
	// Block 0x6f, offset 0x264
	{value: 0x0000, lo: 0x02},
	{value: 0x8132, lo: 0xa5, hi: 0xa5},
	{value: 0x812d, lo: 0xa6, hi: 0xa6},
	// Block 0x70, offset 0x267
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x86, hi: 0x86},
	{value: 0x8104, lo: 0xbf, hi: 0xbf},
	// Block 0x71, offset 0x26a
	{value: 0x17fe, lo: 0x07},
	{value: 0xa000, lo: 0x99, hi: 0x99},
	{value: 0x4238, lo: 0x9a, hi: 0x9a},
	{value: 0xa000, lo: 0x9b, hi: 0x9b},
	{value: 0x4242, lo: 0x9c, hi: 0x9c},
	{value: 0xa000, lo: 0xa5, hi: 0xa5},
	{value: 0x424c, lo: 0xab, hi: 0xab},
	{value: 0x8104, lo: 0xb9, hi: 0xba},
	// Block 0x72, offset 0x272
	{value: 0x0000, lo: 0x06},
	{value: 0x8132, lo: 0x80, hi: 0x82},
	{value: 0x9900, lo: 0xa7, hi: 0xa7},
	{value: 0x2d7e, lo: 0xae, hi: 0xae},
	{value: 0x2d88, lo: 0xaf, hi: 0xaf},
	{value: 0xa000, lo: 0xb1, hi: 0xb2},
	{value: 0x8104, lo: 0xb3, hi: 0xb4},
	// Block 0x73, offset 0x279
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x80, hi: 0x80},
	{value: 0x8102, lo: 0x8a, hi: 0x8a},
	// Block 0x74, offset 0x27c
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0xb5, hi: 0xb5},
	{value: 0x8102, lo: 0xb6, hi: 0xb6},
	// Block 0x75, offset 0x27f
	{value: 0x0002, lo: 0x01},
	{value: 0x8102, lo: 0xa9, hi: 0xaa},
	// Block 0x76, offset 0x281
	{value: 0x0000, lo: 0x07},
	{value: 0xa000, lo: 0x87, hi: 0x87},
	{value: 0x2d92, lo: 0x8b, hi: 0x8b},
	{value: 0x2d9c, lo: 0x8c, hi: 0x8c},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x9900, lo: 0x97, hi: 0x97},
	{value: 0x8132, lo: 0xa6, hi: 0xac},
	{value: 0x8132, lo: 0xb0, hi: 0xb4},
	// Block 0x77, offset 0x289
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x82, hi: 0x82},
	{value: 0x8102, lo: 0x86, hi: 0x86},
	// Block 0x78, offset 0x28c
	{value: 0x6b5a, lo: 0x06},
	{value: 0x9900, lo: 0xb0, hi: 0xb0},
	{value: 0xa000, lo: 0xb9, hi: 0xb9},
	{value: 0x9900, lo: 0xba, hi: 0xba},
	{value: 0x2db0, lo: 0xbb, hi: 0xbb},
	{value: 0x2da6, lo: 0xbc, hi: 0xbd},
	{value: 0x2dba, lo: 0xbe, hi: 0xbe},
	// Block 0x79, offset 0x293
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x82, hi: 0x82},
	{value: 0x8102, lo: 0x83, hi: 0x83},
	// Block 0x7a, offset 0x296
	{value: 0x0000, lo: 0x05},
	{value: 0x9900, lo: 0xaf, hi: 0xaf},
	{value: 0xa000, lo: 0xb8, hi: 0xb9},
	{value: 0x2dc4, lo: 0xba, hi: 0xba},
	{value: 0x2dce, lo: 0xbb, hi: 0xbb},
	{value: 0x8104, lo: 0xbf, hi: 0xbf},
	// Block 0x7b, offset 0x29c
	{value: 0x0000, lo: 0x01},
	{value: 0x8102, lo: 0x80, hi: 0x80},
	// Block 0x7c, offset 0x29e
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0xbf, hi: 0xbf},
	// Block 0x7d, offset 0x2a0
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0xb6, hi: 0xb6},
	{value: 0x8102, lo: 0xb7, hi: 0xb7},
	// Block 0x7e, offset 0x2a3
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0xab, hi: 0xab},
	// Block 0x7f, offset 0x2a5
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0xb4, hi: 0xb4},
	// Block 0x80, offset 0x2a7
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x87, hi: 0x87},
	// Block 0x81, offset 0x2a9
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x99, hi: 0x99},
	// Block 0x82, offset 0x2ab
	{value: 0x0000, lo: 0x02},
	{value: 0x8102, lo: 0x82, hi: 0x82},
	{value: 0x8104, lo: 0x84, hi: 0x85},
	// Block 0x83, offset 0x2ae
	{value: 0x0000, lo: 0x01},
	{value: 0x8101, lo: 0xb0, hi: 0xb4},
	// Block 0x84, offset 0x2b0
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0xb0, hi: 0xb6},
	// Block 0x85, offset 0x2b2
	{value: 0x0000, lo: 0x01},
	{value: 0x8101, lo: 0x9e, hi: 0x9e},
	// Block 0x86, offset 0x2b4
	{value: 0x0000, lo: 0x0c},
	{value: 0x45cc, lo: 0x9e, hi: 0x9e},
	{value: 0x45d6, lo: 0x9f, hi: 0x9f},
	{value: 0x460a, lo: 0xa0, hi: 0xa0},
	{value: 0x4618, lo: 0xa1, hi: 0xa1},
	{value: 0x4626, lo: 0xa2, hi: 0xa2},
	{value: 0x4634, lo: 0xa3, hi: 0xa3},
	{value: 0x4642, lo: 0xa4, hi: 0xa4},
	{value: 0x812b, lo: 0xa5, hi: 0xa6},
	{value: 0x8101, lo: 0xa7, hi: 0xa9},
	{value: 0x8130, lo: 0xad, hi: 0xad},
	{value: 0x812b, lo: 0xae, hi: 0xb2},
	{value: 0x812d, lo: 0xbb, hi: 0xbf},
	// Block 0x87, offset 0x2c1
	{value: 0x0000, lo: 0x09},
	{value: 0x812d, lo: 0x80, hi: 0x82},
	{value: 0x8132, lo: 0x85, hi: 0x89},
	{value: 0x812d, lo: 0x8a, hi: 0x8b},
	{value: 0x8132, lo: 0xaa, hi: 0xad},
	{value: 0x45e0, lo: 0xbb, hi: 0xbb},
	{value: 0x45ea, lo: 0xbc, hi: 0xbc},
	{value: 0x4650, lo: 0xbd, hi: 0xbd},
	{value: 0x466c, lo: 0xbe, hi: 0xbe},
	{value: 0x465e, lo: 0xbf, hi: 0xbf},
	// Block 0x88, offset 0x2cb
	{value: 0x0000, lo: 0x01},
	{value: 0x467a, lo: 0x80, hi: 0x80},
	// Block 0x89, offset 0x2cd
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0x82, hi: 0x84},
	// Block 0x8a, offset 0x2cf
	{value: 0x0002, lo: 0x03},
	{value: 0x0043, lo: 0x80, hi: 0x99},
	{value: 0x0083, lo: 0x9a, hi: 0xb3},
	{value: 0x0043, lo: 0xb4, hi: 0xbf},
	// Block 0x8b, offset 0x2d3
	{value: 0x0002, lo: 0x04},
	{value: 0x005b, lo: 0x80, hi: 0x8d},
	{value: 0x0083, lo: 0x8e, hi: 0x94},
	{value: 0x0093, lo: 0x96, hi: 0xa7},
	{value: 0x0043, lo: 0xa8, hi: 0xbf},
	// Block 0x8c, offset 0x2d8
	{value: 0x0002, lo: 0x0b},
	{value: 0x0073, lo: 0x80, hi: 0x81},
	{value: 0x0083, lo: 0x82, hi: 0x9b},
	{value: 0x0043, lo: 0x9c, hi: 0x9c},
	{value: 0x0047, lo: 0x9e, hi: 0x9f},
	{value: 0x004f, lo: 0xa2, hi: 0xa2},
	{value: 0x0055, lo: 0xa5, hi: 0xa6},
	{value: 0x005d, lo: 0xa9, hi: 0xac},
	{value: 0x0067, lo: 0xae, hi: 0xb5},
	{value: 0x0083, lo: 0xb6, hi: 0xb9},
	{value: 0x008d, lo: 0xbb, hi: 0xbb},
	{value: 0x0091, lo: 0xbd, hi: 0xbf},
	// Block 0x8d, offset 0x2e4
	{value: 0x0002, lo: 0x04},
	{value: 0x0097, lo: 0x80, hi: 0x83},
	{value: 0x00a1, lo: 0x85, hi: 0x8f},
	{value: 0x0043, lo: 0x90, hi: 0xa9},
	{value: 0x0083, lo: 0xaa, hi: 0xbf},
	// Block 0x8e, offset 0x2e9
	{value: 0x0002, lo: 0x08},
	{value: 0x00af, lo: 0x80, hi: 0x83},
	{value: 0x0043, lo: 0x84, hi: 0x85},
	{value: 0x0049, lo: 0x87, hi: 0x8a},
	{value: 0x0055, lo: 0x8d, hi: 0x94},
	{value: 0x0067, lo: 0x96, hi: 0x9c},
	{value: 0x0083, lo: 0x9e, hi: 0xb7},
	{value: 0x0043, lo: 0xb8, hi: 0xb9},
	{value: 0x0049, lo: 0xbb, hi: 0xbe},
	// Block 0x8f, offset 0x2f2
	{value: 0x0002, lo: 0x05},
	{value: 0x0053, lo: 0x80, hi: 0x84},
	{value: 0x005f, lo: 0x86, hi: 0x86},
	{value: 0x0067, lo: 0x8a, hi: 0x90},
	{value: 0x0083, lo: 0x92, hi: 0xab},
	{value: 0x0043, lo: 0xac, hi: 0xbf},
	// Block 0x90, offset 0x2f8
	{value: 0x0002, lo: 0x04},
	{value: 0x006b, lo: 0x80, hi: 0x85},
	{value: 0x0083, lo: 0x86, hi: 0x9f},
	{value: 0x0043, lo: 0xa0, hi: 0xb9},
	{value: 0x0083, lo: 0xba, hi: 0xbf},
	// Block 0x91, offset 0x2fd
	{value: 0x0002, lo: 0x03},
	{value: 0x008f, lo: 0x80, hi: 0x93},
	{value: 0x0043, lo: 0x94, hi: 0xad},
	{value: 0x0083, lo: 0xae, hi: 0xbf},
	// Block 0x92, offset 0x301
	{value: 0x0002, lo: 0x04},
	{value: 0x00a7, lo: 0x80, hi: 0x87},
	{value: 0x0043, lo: 0x88, hi: 0xa1},
	{value: 0x0083, lo: 0xa2, hi: 0xbb},
	{value: 0x0043, lo: 0xbc, hi: 0xbf},
	// Block 0x93, offset 0x306
	{value: 0x0002, lo: 0x03},
	{value: 0x004b, lo: 0x80, hi: 0x95},
	{value: 0x0083, lo: 0x96, hi: 0xaf},
	{value: 0x0043, lo: 0xb0, hi: 0xbf},
	// Block 0x94, offset 0x30a
	{value: 0x0003, lo: 0x0f},
	{value: 0x01b8, lo: 0x80, hi: 0x80},
	{value: 0x045f, lo: 0x81, hi: 0x81},
	{value: 0x01bb, lo: 0x82, hi: 0x9a},
	{value: 0x045b, lo: 0x9b, hi: 0x9b},
	{value: 0x01c7, lo: 0x9c, hi: 0x9c},
	{value: 0x01d0, lo: 0x9d, hi: 0x9d},
	{value: 0x01d6, lo: 0x9e, hi: 0x9e},
	{value: 0x01fa, lo: 0x9f, hi: 0x9f},
	{value: 0x01eb, lo: 0xa0, hi: 0xa0},
	{value: 0x01e8, lo: 0xa1, hi: 0xa1},
	{value: 0x0173, lo: 0xa2, hi: 0xb2},
	{value: 0x0188, lo: 0xb3, hi: 0xb3},
	{value: 0x01a6, lo: 0xb4, hi: 0xba},
	{value: 0x045f, lo: 0xbb, hi: 0xbb},
	{value: 0x01bb, lo: 0xbc, hi: 0xbf},
	// Block 0x95, offset 0x31a
	{value: 0x0003, lo: 0x0d},
	{value: 0x01c7, lo: 0x80, hi: 0x94},
	{value: 0x045b, lo: 0x95, hi: 0x95},
	{value: 0x01c7, lo: 0x96, hi: 0x96},
	{value: 0x01d0, lo: 0x97, hi: 0x97},
	{value: 0x01d6, lo: 0x98, hi: 0x98},
	{value: 0x01fa, lo: 0x99, hi: 0x99},
	{value: 0x01eb, lo: 0x9a, hi: 0x9a},
	{value: 0x01e8, lo: 0x9b, hi: 0x9b},
	{value: 0x0173, lo: 0x9c, hi: 0xac},
	{value: 0x0188, lo: 0xad, hi: 0xad},
	{value: 0x01a6, lo: 0xae, hi: 0xb4},
	{value: 0x045f, lo: 0xb5, hi: 0xb5},
	{value: 0x01bb, lo: 0xb6, hi: 0xbf},
	// Block 0x96, offset 0x328
	{value: 0x0003, lo: 0x0d},
	{value: 0x01d9, lo: 0x80, hi: 0x8e},
	{value: 0x045b, lo: 0x8f, hi: 0x8f},
	{value: 0x01c7, lo: 0x90, hi: 0x90},
	{value: 0x01d0, lo: 0x91, hi: 0x91},
	{value: 0x01d6, lo: 0x92, hi: 0x92},
	{value: 0x01fa, lo: 0x93, hi: 0x93},
	{value: 0x01eb, lo: 0x94, hi: 0x94},
	{value: 0x01e8, lo: 0x95, hi: 0x95},
	{value: 0x0173, lo: 0x96, hi: 0xa6},
	{value: 0x0188, lo: 0xa7, hi: 0xa7},
	{value: 0x01a6, lo: 0xa8, hi: 0xae},
	{value: 0x045f, lo: 0xaf, hi: 0xaf},
	{value: 0x01bb, lo: 0xb0, hi: 0xbf},
	// Block 0x97, offset 0x336
	{value: 0x0003, lo: 0x0d},
	{value: 0x01eb, lo: 0x80, hi: 0x88},
	{value: 0x045b, lo: 0x89, hi: 0x89},
	{value: 0x01c7, lo: 0x8a, hi: 0x8a},
	{value: 0x01d0, lo: 0x8b, hi: 0x8b},
	{value: 0x01d6, lo: 0x8c, hi: 0x8c},
	{value: 0x01fa, lo: 0x8d, hi: 0x8d},
	{value: 0x01eb, lo: 0x8e, hi: 0x8e},
	{value: 0x01e8, lo: 0x8f, hi: 0x8f},
	{value: 0x0173, lo: 0x90, hi: 0xa0},
	{value: 0x0188, lo: 0xa1, hi: 0xa1},
	{value: 0x01a6, lo: 0xa2, hi: 0xa8},
	{value: 0x045f, lo: 0xa9, hi: 0xa9},
	{value: 0x01bb, lo: 0xaa, hi: 0xbf},
	// Block 0x98, offset 0x344
	{value: 0x0000, lo: 0x05},
	{value: 0x8132, lo: 0x80, hi: 0x86},
	{value: 0x8132, lo: 0x88, hi: 0x98},
	{value: 0x8132, lo: 0x9b, hi: 0xa1},
	{value: 0x8132, lo: 0xa3, hi: 0xa4},
	{value: 0x8132, lo: 0xa6, hi: 0xaa},
	// Block 0x99, offset 0x34a
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0x90, hi: 0x96},
	// Block 0x9a, offset 0x34c
	{value: 0x0000, lo: 0x02},
	{value: 0x8132, lo: 0x84, hi: 0x89},
	{value: 0x8102, lo: 0x8a, hi: 0x8a},
	// Block 0x9b, offset 0x34f
	{value: 0x0002, lo: 0x09},
	{value: 0x0063, lo: 0x80, hi: 0x89},
	{value: 0x1951, lo: 0x8a, hi: 0x8a},
	{value: 0x1981, lo: 0x8b, hi: 0x8b},
	{value: 0x199c, lo: 0x8c, hi: 0x8c},
	{value: 0x19a2, lo: 0x8d, hi: 0x8d},
	{value: 0x1bc0, lo: 0x8e, hi: 0x8e},
	{value: 0x19ae, lo: 0x8f, hi: 0x8f},
	{value: 0x197b, lo: 0xaa, hi: 0xaa},
	{value: 0x197e, lo: 0xab, hi: 0xab},
	// Block 0x9c, offset 0x359
	{value: 0x0000, lo: 0x01},
	{value: 0x193f, lo: 0x90, hi: 0x90},
	// Block 0x9d, offset 0x35b
	{value: 0x0028, lo: 0x09},
	{value: 0x2862, lo: 0x80, hi: 0x80},
	{value: 0x2826, lo: 0x81, hi: 0x81},
	{value: 0x2830, lo: 0x82, hi: 0x82},
	{value: 0x2844, lo: 0x83, hi: 0x84},
	{value: 0x284e, lo: 0x85, hi: 0x86},
	{value: 0x283a, lo: 0x87, hi: 0x87},
	{value: 0x2858, lo: 0x88, hi: 0x88},
	{value: 0x0b6f, lo: 0x90, hi: 0x90},
	{value: 0x08e7, lo: 0x91, hi: 0x91},
}

// recompMap: 7520 bytes (entries only)
var recompMap map[uint32]rune
var recompMapOnce sync.Once

const recompMapPacked = "" +
	"\x00A\x03\x00\x00\x00\x00\xc0" + // 0x00410300: 0x000000C0
	"\x00A\x03\x01\x00\x00\x00\xc1" + // 0x00410301: 0x000000C1
	"\x00A\x03\x02\x00\x00\x00\xc2" + // 0x00410302: 0x000000C2
	"\x00A\x03\x03\x00\x00\x00\xc3" + // 0x00410303: 0x000000C3
	"\x00A\x03\b\x00\x00\x00\xc4" + // 0x00410308: 0x000000C4
	"\x00A\x03\n\x00\x00\x00\xc5" + // 0x0041030A: 0x000000C5
	"\x00C\x03'\x00\x00\x00\xc7" + // 0x00430327: 0x000000C7
	"\x00E\x03\x00\x00\x00\x00\xc8" + // 0x00450300: 0x000000C8
	"\x00E\x03\x01\x00\x00\x00\xc9" + // 0x00450301: 0x000000C9
	"\x00E\x03\x02\x00\x00\x00\xca" + // 0x00450302: 0x000000CA
	"\x00E\x03\b\x00\x00\x00\xcb" + // 0x00450308: 0x000000CB
	"\x00I\x03\x00\x00\x00\x00\xcc" + // 0x00490300: 0x000000CC
	"\x00I\x03\x01\x00\x00\x00\xcd" + // 0x00490301: 0x000000CD
	"\x00I\x03\x02\x00\x00\x00\xce" + // 0x00490302: 0x000000CE
	"\x00I\x03\b\x00\x00\x00\xcf" + // 0x00490308: 0x000000CF
	"\x00N\x03\x03\x00\x00\x00\xd1" + // 0x004E0303: 0x000000D1
	"\x00O\x03\x00\x00\x00\x00\xd2" + // 0x004F0300: 0x000000D2
	"\x00O\x03\x01\x00\x00\x00\xd3" + // 0x004F0301: 0x000000D3
	"\x00O\x03\x02\x00\x00\x00\xd4" + // 0x004F0302: 0x000000D4
	"\x00O\x03\x03\x00\x00\x00\xd5" + // 0x004F0303: 0x000000D5
	"\x00O\x03\b\x00\x00\x00\xd6" + // 0x004F0308: 0x000000D6
	"\x00U\x03\x00\x00\x00\x00\xd9" + // 0x00550300: 0x000000D9
	"\x00U\x03\x01\x00\x00\x00\xda" + // 0x00550301: 0x000000DA
	"\x00U\x03\x02\x00\x00\x00\xdb" + // 0x00550302: 0x000000DB
	"\x00U\x03\b\x00\x00\x00\xdc" + // 0x00550308: 0x000000DC
	"\x00Y\x03\x01\x00\x00\x00\xdd" + // 0x00590301: 0x000000DD
	"\x00a\x03\x00\x00\x00\x00\xe0" + // 0x00610300: 0x000000E0
	"\x00a\x03\x01\x00\x00\x00\xe1" + // 0x00610301: 0x000000E1
	"\x00a\x03\x02\x00\x00\x00\xe2" + // 0x00610302: 0x000000E2
	"\x00a\x03\x03\x00\x00\x00\xe3" + // 0x00610303: 0x000000E3
	"\x00a\x03\b\x00\x00\x00\xe4" + // 0x00610308: 0x000000E4
	"\x00a\x03\n\x00\x00\x00\xe5" + // 0x0061030A: 0x000000E5
	"\x00c\x03'\x00\x00\x00\xe7" + // 0x00630327: 0x000000E7
	"\x00e\x03\x00\x00\x00\x00\xe8" + // 0x00650300: 0x000000E8
	"\x00e\x03\x01\x00\x00\x00\xe9" + // 0x00650301: 0x000000E9
	"\x00e\x03\x02\x00\x00\x00\xea" + // 0x00650302: 0x000000EA
	"\x00e\x03\b\x00\x00\x00\xeb" + // 0x00650308: 0x000000EB
	"\x00i\x03\x00\x00\x00\x00\xec" + // 0x00690300: 0x000000EC
	"\x00i\x03\x01\x00\x00\x00\xed" + // 0x00690301: 0x000000ED
	"\x00i\x03\x02\x00\x00\x00\xee" + // 0x00690302: 0x000000EE
	"\x00i\x03\b\x00\x00\x00\xef" + // 0x00690308: 0x000000EF
	"\x00n\x03\x03\x00\x00\x00\xf1" + // 0x006E0303: 0x000000F1
	"\x00o\x03\x00\x00\x00\x00\xf2" + // 0x006F0300: 0x000000F2
	"\x00o\x03\x01\x00\x00\x00\xf3" + // 0x006F0301: 0x000000F3
	"\x00o\x03\x02\x00\x00\x00\xf4" + // 0x006F0302: 0x000000F4
	"\x00o\x03\x03\x00\x00\x00\xf5" + // 0x006F0303: 0x000000F5
	"\x00o\x03\b\x00\x00\x00\xf6" + // 0x006F0308: 0x000000F6
	"\x00u\x03\x00\x00\x00\x00\xf9" + // 0x00750300: 0x000000F9
	"\x00u\x03\x01\x00\x00\x00\xfa" + // 0x00750301: 0x000000FA
	"\x00u\x03\x02\x00\x00\x00\xfb" + // 0x00750302: 0x000000FB
	"\x00u\x03\b\x00\x00\x00\xfc" + // 0x00750308: 0x000000FC
	"\x00y\x03\x01\x00\x00\x00\xfd" + // 0x00790301: 0x000000FD
	"\x00y\x03\b\x00\x00\x00\xff" + // 0x00790308: 0x000000FF
	"\x00A\x03\x04\x00\x00\x01\x00" + // 0x00410304: 0x00000100
	"\x00a\x03\x04\x00\x00\x01\x01" + // 0x00610304: 0x00000101
	"\x00A\x03\x06\x00\x00\x01\x02" + // 0x00410306: 0x00000102
	"\x00a\x03\x06\x00\x00\x01\x03" + // 0x00610306: 0x00000103
	"\x00A\x03(\x00\x00\x01\x04" + // 0x00410328: 0x00000104
	"\x00a\x03(\x00\x00\x01\x05" + // 0x00610328: 0x00000105
	"\x00C\x03\x01\x00\x00\x01\x06" + // 0x00430301: 0x00000106
	"\x00c\x03\x01\x00\x00\x01\a" + // 0x00630301: 0x00000107
	"\x00C\x03\x02\x00\x00\x01\b" + // 0x00430302: 0x00000108
	"\x00c\x03\x02\x00\x00\x01\t" + // 0x00630302: 0x00000109
	"\x00C\x03\a\x00\x00\x01\n" + // 0x00430307: 0x0000010A
	"\x00c\x03\a\x00\x00\x01\v" + // 0x00630307: 0x0000010B
	"\x00C\x03\f\x00\x00\x01\f" + // 0x0043030C: 0x0000010C
	"\x00c\x03\f\x00\x00\x01\r" + // 0x0063030C: 0x0000010D
	"\x00D\x03\f\x00\x00\x01\x0e" + // 0x0044030C: 0x0000010E
	"\x00d\x03\f\x00\x00\x01\x0f" + // 0x0064030C: 0x0000010F
	"\x00E\x03\x04\x00\x00\x01\x12" + // 0x00450304: 0x00000112
	"\x00e\x03\x04\x00\x00\x01\x13" + // 0x00650304: 0x00000113
	"\x00E\x03\x06\x00\x00\x01\x14" + // 0x00450306: 0x00000114
	"\x00e\x03\x06\x00\x00\x01\x15" + // 0x00650306: 0x00000115
	"\x00E\x03\a\x00\x00\x01\x16" + // 0x00450307: 0x00000116
	"\x00e\x03\a\x00\x00\x01\x17" + // 0x00650307: 0x00000117
	"\x00E\x03(\x00\x00\x01\x18" + // 0x00450328: 0x00000118
	"\x00e\x03(\x00\x00\x01\x19" + // 0x00650328: 0x00000119
	"\x00E\x03\f\x00\x00\x01\x1a" + // 0x0045030C: 0x0000011A
	"\x00e\x03\f\x00\x00\x01\x1b" + // 0x0065030C: 0x0000011B
	"\x00G\x03\x02\x00\x00\x01\x1c" + // 0x00470302: 0x0000011C
	"\x00g\x03\x02\x00\x00\x01\x1d" + // 0x00670302: 0x0000011D
	"\x00G\x03\x06\x00\x00\x01\x1e" + // 0x00470306: 0x0000011E
	"\x00g\x03\x06\x00\x00\x01\x1f" + // 0x00670306: 0x0000011F
	"\x00G\x03\a\x00\x00\x01 " + // 0x00470307: 0x00000120
	"\x00g\x03\a\x00\x00\x01!" + // 0x00670307: 0x00000121
	"\x00G\x03'\x00\x00\x01\"" + // 0x00470327: 0x00000122
	"\x00g\x03'\x00\x00\x01#" + // 0x00670327: 0x00000123
	"\x00H\x03\x02\x00\x00\x01$" + // 0x00480302: 0x00000124
	"\x00h\x03\x02\x00\x00\x01%" + // 0x00680302: 0x00000125
	"\x00I\x03\x03\x00\x00\x01(" + // 0x00490303: 0x00000128
	"\x00i\x03\x03\x00\x00\x01)" + // 0x00690303: 0x00000129
	"\x00I\x03\x04\x00\x00\x01*" + // 0x00490304: 0x0000012A
	"\x00i\x03\x04\x00\x00\x01+" + // 0x00690304: 0x0000012B
	"\x00I\x03\x06\x00\x00\x01," + // 0x00490306: 0x0000012C
	"\x00i\x03\x06\x00\x00\x01-" + // 0x00690306: 0x0000012D
	"\x00I\x03(\x00\x00\x01." + // 0x00490328: 0x0000012E
	"\x00i\x03(\x00\x00\x01/" + // 0x00690328: 0x0000012F
	"\x00I\x03\a\x00\x00\x010" + // 0x00490307: 0x00000130
	"\x00J\x03\x02\x00\x00\x014" + // 0x004A0302: 0x00000134
	"\x00j\x03\x02\x00\x00\x015" + // 0x006A0302: 0x00000135
	"\x00K\x03'\x00\x00\x016" + // 0x004B0327: 0x00000136
	"\x00k\x03'\x00\x00\x017" + // 0x006B0327: 0x00000137
	"\x00L\x03\x01\x00\x00\x019" + // 0x004C0301: 0x00000139
	"\x00l\x03\x01\x00\x00\x01:" + // 0x006C0301: 0x0000013A
	"\x00L\x03'\x00\x00\x01;" + // 0x004C0327: 0x0000013B
	"\x00l\x03'\x00\x00\x01<" + // 0x006C0327: 0x0000013C
	"\x00L\x03\f\x00\x00\x01=" + // 0x004C030C: 0x0000013D
	"\x00l\x03\f\x00\x00\x01>" + // 0x006C030C: 0x0000013E
	"\x00N\x03\x01\x00\x00\x01C" + // 0x004E0301: 0x00000143
	"\x00n\x03\x01\x00\x00\x01D" + // 0x006E0301: 0x00000144
	"\x00N\x03'\x00\x00\x01E" + // 0x004E0327: 0x00000145
	"\x00n\x03'\x00\x00\x01F" + // 0x006E0327: 0x00000146
	"\x00N\x03\f\x00\x00\x01G" + // 0x004E030C: 0x00000147
	"\x00n\x03\f\x00\x00\x01H" + // 0x006E030C: 0x00000148
	"\x00O\x03\x04\x00\x00\x01L" + // 0x004F0304: 0x0000014C
	"\x00o\x03\x04\x00\x00\x01M" + // 0x006F0304: 0x0000014D
	"\x00O\x03\x06\x00\x00\x01N" + // 0x004F0306: 0x0000014E
	"\x00o\x03\x06\x00\x00\x01O" + // 0x006F0306: 0x0000014F
	"\x00O\x03\v\x00\x00\x01P" + // 0x004F030B: 0x00000150
	"\x00o\x03\v\x00\x00\x01Q" + // 0x006F030B: 0x00000151
	"\x00R\x03\x01\x00\x00\x01T" + // 0x00520301: 0x00000154
	"\x00r\x03\x01\x00\x00\x01U" + // 0x00720301: 0x00000155
	"\x00R\x03'\x00\x00\x01V" + // 0x00520327: 0x00000156
	"\x00r\x03'\x00\x00\x01W" + // 0x00720327: 0x00000157
	"\x00R\x03\f\x00\x00\x01X" + // 0x0052030C: 0x00000158
	"\x00r\x03\f\x00\x00\x01Y" + // 0x0072030C: 0x00000159
	"\x00S\x03\x01\x00\x00\x01Z" + // 0x00530301: 0x0000015A
	"\x00s\x03\x01\x00\x00\x01[" + // 0x00730301: 0x0000015B
	"\x00S\x03\x02\x00\x00\x01\\" + // 0x00530302: 0x0000015C
	"\x00s\x03\x02\x00\x00\x01]" + // 0x00730302: 0x0000015D
	"\x00S\x03'\x00\x00\x01^" + // 0x00530327: 0x0000015E
	"\x00s\x03'\x00\x00\x01_" + // 0x00730327: 0x0000015F
	"\x00S\x03\f\x00\x00\x01`" + // 0x0053030C: 0x00000160
	"\x00s\x03\f\x00\x00\x01a" + // 0x0073030C: 0x00000161
	"\x00T\x03'\x00\x00\x01b" + // 0x00540327: 0x00000162
	"\x00t\x03'\x00\x00\x01c" + // 0x00740327: 0x00000163
	"\x00T\x03\f\x00\x00\x01d" + // 0x0054030C: 0x00000164
	"\x00t\x03\f\x00\x00\x01e" + // 0x0074030C: 0x00000165
	"\x00U\x03\x03\x00\x00\x01h" + // 0x00550303: 0x00000168
	"\x00u\x03\x03\x00\x00\x01i" + // 0x00750303: 0x00000169
	"\x00U\x03\x04\x00\x00\x01j" + // 0x00550304: 0x0000016A
	"\x00u\x03\x04\x00\x00\x01k" + // 0x00750304: 0x0000016B
	"\x00U\x03\x06\x00\x00\x01l" + // 0x00550306: 0x0000016C
	"\x00u\x03\x06\x00\x00\x01m" + // 0x00750306: 0x0000016D
	"\x00U\x03\n\x00\x00\x01n" + // 0x0055030A: 0x0000016E
	"\x00u\x03\n\x00\x00\x01o" + // 0x0075030A: 0x0000016F
	"\x00U\x03\v\x00\x00\x01p" + // 0x0055030B: 0x00000170
	"\x00u\x03\v\x00\x00\x01q" + // 0x0075030B: 0x00000171
	"\x00U\x03(\x00\x00\x01r" + // 0x00550328: 0x00000172
	"\x00u\x03(\x00\x00\x01s" + // 0x00750328: 0x00000173
	"\x00W\x03\x02\x00\x00\x01t" + // 0x00570302: 0x00000174
	"\x00w\x03\x02\x00\x00\x01u" + // 0x00770302: 0x00000175
	"\x00Y\x03\x02\x00\x00\x01v" + // 0x00590302: 0x00000176
	"\x00y\x03\x02\x00\x00\x01w" + // 0x00790302: 0x00000177
	"\x00Y\x03\b\x00\x00\x01x" + // 0x00590308: 0x00000178
	"\x00Z\x03\x01\x00\x00\x01y" + // 0x005A0301: 0x00000179
	"\x00z\x03\x01\x00\x00\x01z" + // 0x007A0301: 0x0000017A
	"\x00Z\x03\a\x00\x00\x01{" + // 0x005A0307: 0x0000017B
	"\x00z\x03\a\x00\x00\x01|" + // 0x007A0307: 0x0000017C
	"\x00Z\x03\f\x00\x00\x01}" + // 0x005A030C: 0x0000017D
	"\x00z\x03\f\x00\x00\x01~" + // 0x007A030C: 0x0000017E
	"\x00O\x03\x1b\x00\x00\x01\xa0" + // 0x004F031B: 0x000001A0
	"\x00o\x03\x1b\x00\x00\x01\xa1" + // 0x006F031B: 0x000001A1
	"\x00U\x03\x1b\x00\x00\x01\xaf" + // 0x0055031B: 0x000001AF
	"\x00u\x03\x1b\x00\x00\x01\xb0" + // 0x0075031B: 0x000001B0
	"\x00A\x03\f\x00\x00\x01\xcd" + // 0x0041030C: 0x000001CD
	"\x00a\x03\f\x00\x00\x01\xce" + // 0x0061030C: 0x000001CE
	"\x00I\x03\f\x00\x00\x01\xcf" + // 0x0049030C: 0x000001CF
	"\x00i\x03\f\x00\x00\x01\xd0" + // 0x0069030C: 0x000001D0
	"\x00O\x03\f\x00\x00\x01\xd1" + // 0x004F030C: 0x000001D1
	"\x00o\x03\f\x00\x00\x01\xd2" + // 0x006F030C: 0x000001D2
	"\x00U\x03\f\x00\x00\x01\xd3" + // 0x0055030C: 0x000001D3
	"\x00u\x03\f\x00\x00\x01\xd4" + // 0x0075030C: 0x000001D4
	"\x00\xdc\x03\x04\x00\x00\x01\xd5" + // 0x00DC0304: 0x000001D5
	"\x00\xfc\x03\x04\x00\x00\x01\xd6" + // 0x00FC0304: 0x000001D6
	"\x00\xdc\x03\x01\x00\x00\x01\xd7" + // 0x00DC0301: 0x000001D7
	"\x00\xfc\x03\x01\x00\x00\x01\xd8" + // 0x00FC0301: 0x000001D8
	"\x00\xdc\x03\f\x00\x00\x01\xd9" + // 0x00DC030C: 0x000001D9
	"\x00\xfc\x03\f\x00\x00\x01\xda" + // 0x00FC030C: 0x000001DA
	"\x00\xdc\x03\x00\x00\x00\x01\xdb" + // 0x00DC0300: 0x000001DB
	"\x00\xfc\x03\x00\x00\x00\x01\xdc" + // 0x00FC0300: 0x000001DC
	"\x00\xc4\x03\x04\x00\x00\x01\xde" + // 0x00C40304: 0x000001DE
	"\x00\xe4\x03\x04\x00\x00\x01\xdf" + // 0x00E40304: 0x000001DF
	"\x02&\x03\x04\x00\x00\x01\xe0" + // 0x02260304: 0x000001E0
	"\x02'\x03\x04\x00\x00\x01\xe1" + // 0x02270304: 0x000001E1
	"\x00\xc6\x03\x04\x00\x00\x01\xe2" + // 0x00C60304: 0x000001E2
	"\x00\xe6\x03\x04\x00\x00\x01\xe3" + // 0x00E60304: 0x000001E3
	"\x00G\x03\f\x00\x00\x01\xe6" + // 0x0047030C: 0x000001E6
	"\x00g\x03\f\x00\x00\x01\xe7" + // 0x0067030C: 0x000001E7
	"\x00K\x03\f\x00\x00\x01\xe8" + // 0x004B030C: 0x000001E8
	"\x00k\x03\f\x00\x00\x01\xe9" + // 0x006B030C: 0x000001E9
	"\x00O\x03(\x00\x00\x01\xea" + // 0x004F0328: 0x000001EA
	"\x00o\x03(\x00\x00\x01\xeb" + // 0x006F0328: 0x000001EB
	"\x01\xea\x03\x04\x00\x00\x01\xec" + // 0x01EA0304: 0x000001EC
	"\x01\xeb\x03\x04\x00\x00\x01\xed" + // 0x01EB0304: 0x000001ED
	"\x01\xb7\x03\f\x00\x00\x01\xee" + // 0x01B7030C: 0x000001EE
	"\x02\x92\x03\f\x00\x00\x01\xef" + // 0x0292030C: 0x000001EF
	"\x00j\x03\f\x00\x00\x01\xf0" + // 0x006A030C: 0x000001F0
	"\x00G\x03\x01\x00\x00\x01\xf4" + // 0x00470301: 0x000001F4
	"\x00g\x03\x01\x00\x00\x01\xf5" + // 0x00670301: 0x000001F5
	"\x00N\x03\x00\x00\x00\x01\xf8" + // 0x004E0300: 0x000001F8
	"\x00n\x03\x00\x00\x00\x01\xf9" + // 0x006E0300: 0x000001F9
	"\x00\xc5\x03\x01\x00\x00\x01\xfa" + // 0x00C50301: 0x000001FA
	"\x00\xe5\x03\x01\x00\x00\x01\xfb" + // 0x00E50301: 0x000001FB
	"\x00\xc6\x03\x01\x00\x00\x01\xfc" + // 0x00C60301: 0x000001FC
	"\x00\xe6\x03\x01\x00\x00\x01\xfd" + // 0x00E60301: 0x000001FD
	"\x00\xd8\x03\x01\x00\x00\x01\xfe" + // 0x00D80301: 0x000001FE
	"\x00\xf8\x03\x01\x00\x00\x01\xff" + // 0x00F80301: 0x000001FF
	"\x00A\x03\x0f\x00\x00\x02\x00" + // 0x0041030F: 0x00000200
	"\x00a\x03\x0f\x00\x00\x02\x01" + // 0x0061030F: 0x00000201
	"\x00A\x03\x11\x00\x00\x02\x02" + // 0x00410311: 0x00000202
	"\x00a\x03\x11\x00\x00\x02\x03" + // 0x00610311: 0x00000203
	"\x00E\x03\x0f\x00\x00\x02\x04" + // 0x0045030F: 0x00000204
	"\x00e\x03\x0f\x00\x00\x02\x05" + // 0x0065030F: 0x00000205
	"\x00E\x03\x11\x00\x00\x02\x06" + // 0x00450311: 0x00000206
	"\x00e\x03\x11\x00\x00\x02\a" + // 0x00650311: 0x00000207
	"\x00I\x03\x0f\x00\x00\x02\b" + // 0x0049030F: 0x00000208
	"\x00i\x03\x0f\x00\x00\x02\t" + // 0x0069030F: 0x00000209
	"\x00I\x03\x11\x00\x00\x02\n" + // 0x00490311: 0x0000020A
	"\x00i\x03\x11\x00\x00\x02\v" + // 0x00690311: 0x0000020B
	"\x00O\x03\x0f\x00\x00\x02\f" + // 0x004F030F: 0x0000020C
	"\x00o\x03\x0f\x00\x00\x02\r" + // 0x006F030F: 0x0000020D
	"\x00O\x03\x11\x00\x00\x02\x0e" + // 0x004F0311: 0x0000020E
	"\x00o\x03\x11\x00\x00\x02\x0f" + // 0x006F0311: 0x0000020F
	"\x00R\x03\x0f\x00\x00\x02\x10" + // 0x0052030F: 0x00000210
	"\x00r\x03\x0f\x00\x00\x02\x11" + // 0x0072030F: 0x00000211
	"\x00R\x03\x11\x00\x00\x02\x12" + // 0x00520311: 0x00000212
	"\x00r\x03\x11\x00\x00\x02\x13" + // 0x00720311: 0x00000213
	"\x00U\x03\x0f\x00\x00\x02\x14" + // 0x0055030F: 0x00000214
	"\x00u\x03\x0f\x00\x00\x02\x15" + // 0x0075030F: 0x00000215
	"\x00U\x03\x11\x00\x00\x02\x16" + // 0x00550311: 0x00000216
	"\x00u\x03\x11\x00\x00\x02\x17" + // 0x00750311: 0x00000217
	"\x00S\x03&\x00\x00\x02\x18" + // 0x00530326: 0x00000218
	"\x00s\x03&\x00\x00\x02\x19" + // 0x00730326: 0x00000219
	"\x00T\x03&\x00\x00\x02\x1a" + // 0x00540326: 0x0000021A
	"\x00t\x03&\x00\x00\x02\x1b" + // 0x00740326: 0x0000021B
	"\x00H\x03\f\x00\x00\x02\x1e" + // 0x0048030C: 0x0000021E
	"\x00h\x03\f\x00\x00\x02\x1f" + // 0x0068030C: 0x0000021F
	"\x00A\x03\a\x00\x00\x02&" + // 0x00410307: 0x00000226
	"\x00a\x03\a\x00\x00\x02'" + // 0x00610307: 0x00000227
	"\x00E\x03'\x00\x00\x02(" + // 0x00450327: 0x00000228
	"\x00e\x03'\x00\x00\x02)" + // 0x00650327: 0x00000229
	"\x00\xd6\x03\x04\x00\x00\x02*" + // 0x00D60304: 0x0000022A
	"\x00\xf6\x03\x04\x00\x00\x02+" + // 0x00F60304: 0x0000022B
	"\x00\xd5\x03\x04\x00\x00\x02," + // 0x00D50304: 0x0000022C
	"\x00\xf5\x03\x04\x00\x00\x02-" + // 0x00F50304: 0x0000022D
	"\x00O\x03\a\x00\x00\x02." + // 0x004F0307: 0x0000022E
	"\x00o\x03\a\x00\x00\x02/" + // 0x006F0307: 0x0000022F
	"\x02.\x03\x04\x00\x00\x020" + // 0x022E0304: 0x00000230
	"\x02/\x03\x04\x00\x00\x021" + // 0x022F0304: 0x00000231
	"\x00Y\x03\x04\x00\x00\x022" + // 0x00590304: 0x00000232
	"\x00y\x03\x04\x00\x00\x023" + // 0x00790304: 0x00000233
	"\x00\xa8\x03\x01\x00\x00\x03\x85" + // 0x00A80301: 0x00000385
	"\x03\x91\x03\x01\x00\x00\x03\x86" + // 0x03910301: 0x00000386
	"\x03\x95\x03\x01\x00\x00\x03\x88" + // 0x03950301: 0x00000388
	"\x03\x97\x03\x01\x00\x00\x03\x89" + // 0x03970301: 0x00000389
	"\x03\x99\x03\x01\x00\x00\x03\x8a" + // 0x03990301: 0x0000038A
	"\x03\x9f\x03\x01\x00\x00\x03\x8c" + // 0x039F0301: 0x0000038C
	"\x03\xa5\x03\x01\x00\x00\x03\x8e" + // 0x03A50301: 0x0000038E
	"\x03\xa9\x03\x01\x00\x00\x03\x8f" + // 0x03A90301: 0x0000038F
	"\x03\xca\x03\x01\x00\x00\x03\x90" + // 0x03CA0301: 0x00000390
	"\x03\x99\x03\b\x00\x00\x03\xaa" + // 0x03990308: 0x000003AA
	"\x03\xa5\x03\b\x00\x00\x03\xab" + // 0x03A50308: 0x000003AB
	"\x03\xb1\x03\x01\x00\x00\x03\xac" + // 0x03B10301: 0x000003AC
	"\x03\xb5\x03\x01\x00\x00\x03\xad" + // 0x03B50301: 0x000003AD
	"\x03\xb7\x03\x01\x00\x00\x03\xae" + // 0x03B70301: 0x000003AE
	"\x03\xb9\x03\x01\x00\x00\x03\xaf" + // 0x03B90301: 0x000003AF
	"\x03\xcb\x03\x01\x00\x00\x03\xb0" + // 0x03CB0301: 0x000003B0
	"\x03\xb9\x03\b\x00\x00\x03\xca" + // 0x03B90308: 0x000003CA
	"\x03\xc5\x03\b\x00\x00\x03\xcb" + // 0x03C50308: 0x000003CB
	"\x03\xbf\x03\x01\x00\x00\x03\xcc" + // 0x03BF0301: 0x000003CC
	"\x03\xc5\x03\x01\x00\x00\x03\xcd" + // 0x03C50301: 0x000003CD
	"\x03\xc9\x03\x01\x00\x00\x03\xce" + // 0x03C90301: 0x000003CE
	"\x03\xd2\x03\x01\x00\x00\x03\xd3" + // 0x03D20301: 0x000003D3
	"\x03\xd2\x03\b\x00\x00\x03\xd4" + // 0x03D20308: 0x000003D4
	"\x04\x15\x03\x00\x00\x00\x04\x00" + // 0x04150300: 0x00000400
	"\x04\x15\x03\b\x00\x00\x04\x01" + // 0x04150308: 0x00000401
	"\x04\x13\x03\x01\x00\x00\x04\x03" + // 0x04130301: 0x00000403
	"\x04\x06\x03\b\x00\x00\x04\a" + // 0x04060308: 0x00000407
	"\x04\x1a\x03\x01\x00\x00\x04\f" + // 0x041A0301: 0x0000040C
	"\x04\x18\x03\x00\x00\x00\x04\r" + // 0x04180300: 0x0000040D
	"\x04#\x03\x06\x00\x00\x04\x0e" + // 0x04230306: 0x0000040E
	"\x04\x18\x03\x06\x00\x00\x04\x19" + // 0x04180306: 0x00000419
	"\x048\x03\x06\x00\x00\x049" + // 0x04380306: 0x00000439
	"\x045\x03\x00\x00\x00\x04P" + // 0x04350300: 0x00000450
	"\x045\x03\b\x00\x00\x04Q" + // 0x04350308: 0x00000451
	"\x043\x03\x01\x00\x00\x04S" + // 0x04330301: 0x00000453
	"\x04V\x03\b\x00\x00\x04W" + // 0x04560308: 0x00000457
	"\x04:\x03\x01\x00\x00\x04\\" + // 0x043A0301: 0x0000045C
	"\x048\x03\x00\x00\x00\x04]" + // 0x04380300: 0x0000045D
	"\x04C\x03\x06\x00\x00\x04^" + // 0x04430306: 0x0000045E
	"\x04t\x03\x0f\x00\x00\x04v" + // 0x0474030F: 0x00000476
	"\x04u\x03\x0f\x00\x00\x04w" + // 0x0475030F: 0x00000477
	"\x04\x16\x03\x06\x00\x00\x04\xc1" + // 0x04160306: 0x000004C1
	"\x046\x03\x06\x00\x00\x04\xc2" + // 0x04360306: 0x000004C2
	"\x04\x10\x03\x06\x00\x00\x04\xd0" + // 0x04100306: 0x000004D0
	"\x040\x03\x06\x00\x00\x04\xd1" + // 0x04300306: 0x000004D1
	"\x04\x10\x03\b\x00\x00\x04\xd2" + // 0x04100308: 0x000004D2
	"\x040\x03\b\x00\x00\x04\xd3" + // 0x04300308: 0x000004D3
	"\x04\x15\x03\x06\x00\x00\x04\xd6" + // 0x04150306: 0x000004D6
	"\x045\x03\x06\x00\x00\x04\xd7" + // 0x04350306: 0x000004D7
	"\x04\xd8\x03\b\x00\x00\x04\xda" + // 0x04D80308: 0x000004DA
	"\x04\xd9\x03\b\x00\x00\x04\xdb" + // 0x04D90308: 0x000004DB
	"\x04\x16\x03\b\x00\x00\x04\xdc" + // 0x04160308: 0x000004DC
	"\x046\x03\b\x00\x00\x04\xdd" + // 0x04360308: 0x000004DD
	"\x04\x17\x03\b\x00\x00\x04\xde" + // 0x04170308: 0x000004DE
	"\x047\x03\b\x00\x00\x04\xdf" + // 0x04370308: 0x000004DF
	"\x04\x18\x03\x04\x00\x00\x04\xe2" + // 0x04180304: 0x000004E2
	"\x048\x03\x04\x00\x00\x04\xe3" + // 0x04380304: 0x000004E3
	"\x04\x18\x03\b\x00\x00\x04\xe4" + // 0x04180308: 0x000004E4
	"\x048\x03\b\x00\x00\x04\xe5" + // 0x04380308: 0x000004E5
	"\x04\x1e\x03\b\x00\x00\x04\xe6" + // 0x041E0308: 0x000004E6
	"\x04>\x03\b\x00\x00\x04\xe7" + // 0x043E0308: 0x000004E7
	"\x04\xe8\x03\b\x00\x00\x04\xea" + // 0x04E80308: 0x000004EA
	"\x04\xe9\x03\b\x00\x00\x04\xeb" + // 0x04E90308: 0x000004EB
	"\x04-\x03\b\x00\x00\x04\xec" + // 0x042D0308: 0x000004EC
	"\x04M\x03\b\x00\x00\x04\xed" + // 0x044D0308: 0x000004ED
	"\x04#\x03\x04\x00\x00\x04\xee" + // 0x04230304: 0x000004EE
	"\x04C\x03\x04\x00\x00\x04\xef" + // 0x04430304: 0x000004EF
	"\x04#\x03\b\x00\x00\x04\xf0" + // 0x04230308: 0x000004F0
	"\x04C\x03\b\x00\x00\x04\xf1" + // 0x04430308: 0x000004F1
	"\x04#\x03\v\x00\x00\x04\xf2" + // 0x0423030B: 0x000004F2
	"\x04C\x03\v\x00\x00\x04\xf3" + // 0x0443030B: 0x000004F3
	"\x04'\x03\b\x00\x00\x04\xf4" + // 0x04270308: 0x000004F4
	"\x04G\x03\b\x00\x00\x04\xf5" + // 0x04470308: 0x000004F5
	"\x04+\x03\b\x00\x00\x04\xf8" + // 0x042B0308: 0x000004F8
	"\x04K\x03\b\x00\x00\x04\xf9" + // 0x044B0308: 0x000004F9
	"\x06'\x06S\x00\x00\x06\"" + // 0x06270653: 0x00000622
	"\x06'\x06T\x00\x00\x06#" + // 0x06270654: 0x00000623
	"\x06H\x06T\x00\x00\x06$" + // 0x06480654: 0x00000624
	"\x06'\x06U\x00\x00\x06%" + // 0x06270655: 0x00000625
	"\x06J\x06T\x00\x00\x06&" + // 0x064A0654: 0x00000626
	"\x06\xd5\x06T\x00\x00\x06\xc0" + // 0x06D50654: 0x000006C0
	"\x06\xc1\x06T\x00\x00\x06\xc2" + // 0x06C10654: 0x000006C2
	"\x06\xd2\x06T\x00\x00\x06\xd3" + // 0x06D20654: 0x000006D3
	"\t(\t<\x00\x00\t)" + // 0x0928093C: 0x00000929
	"\t0\t<\x00\x00\t1" + // 0x0930093C: 0x00000931
	"\t3\t<\x00\x00\t4" + // 0x0933093C: 0x00000934
	"\t\xc7\t\xbe\x00\x00\t\xcb" + // 0x09C709BE: 0x000009CB
	"\t\xc7\t\xd7\x00\x00\t\xcc" + // 0x09C709D7: 0x000009CC
	"\vG\vV\x00\x00\vH" + // 0x0B470B56: 0x00000B48
	"\vG\v>\x00\x00\vK" + // 0x0B470B3E: 0x00000B4B
	"\vG\vW\x00\x00\vL" + // 0x0B470B57: 0x00000B4C
	"\v\x92\v\xd7\x00\x00\v\x94" + // 0x0B920BD7: 0x00000B94
	"\v\xc6\v\xbe\x00\x00\v\xca" + // 0x0BC60BBE: 0x00000BCA
	"\v\xc7\v\xbe\x00\x00\v\xcb" + // 0x0BC70BBE: 0x00000BCB
	"\v\xc6\v\xd7\x00\x00\v\xcc" + // 0x0BC60BD7: 0x00000BCC
	"\fF\fV\x00\x00\fH" + // 0x0C460C56: 0x00000C48
	"\f\xbf\f\xd5\x00\x00\f\xc0" + // 0x0CBF0CD5: 0x00000CC0
	"\f\xc6\f\xd5\x00\x00\f\xc7" + // 0x0CC60CD5: 0x00000CC7
	"\f\xc6\f\xd6\x00\x00\f\xc8" + // 0x0CC60CD6: 0x00000CC8
	"\f\xc6\f\xc2\x00\x00\f\xca" + // 0x0CC60CC2: 0x00000CCA
	"\f\xca\f\xd5\x00\x00\f\xcb" + // 0x0CCA0CD5: 0x00000CCB
	"\rF\r>\x00\x00\rJ" + // 0x0D460D3E: 0x00000D4A
	"\rG\r>\x00\x00\rK" + // 0x0D470D3E: 0x00000D4B
	"\rF\rW\x00\x00\rL" + // 0x0D460D57: 0x00000D4C
	"\r\xd9\r\xca\x00\x00\r\xda" + // 0x0DD90DCA: 0x00000DDA
	"\r\xd9\r\xcf\x00\x00\r\xdc" + // 0x0DD90DCF: 0x00000DDC
	"\r\xdc\r\xca\x00\x00\r\xdd" + // 0x0DDC0DCA: 0x00000DDD
	"\r\xd9\r\xdf\x00\x00\r\xde" + // 0x0DD90DDF: 0x00000DDE
	"\x10%\x10.\x00\x00\x10&" + // 0x1025102E: 0x00001026
	"\x1b\x05\x1b5\x00\x00\x1b\x06" + // 0x1B051B35: 0x00001B06
	"\x1b\a\x1b5\x00\x00\x1b\b" + // 0x1B071B35: 0x00001B08
	"\x1b\t\x1b5\x00\x00\x1b\n" + // 0x1B091B35: 0x00001B0A
	"\x1b\v\x1b5\x00\x00\x1b\f" + // 0x1B0B1B35: 0x00001B0C
	"\x1b\r\x1b5\x00\x00\x1b\x0e" + // 0x1B0D1B35: 0x00001B0E
	"\x1b\x11\x1b5\x00\x00\x1b\x12" + // 0x1B111B35: 0x00001B12
	"\x1b:\x1b5\x00\x00\x1b;" + // 0x1B3A1B35: 0x00001B3B
	"\x1b<\x1b5\x00\x00\x1b=" + // 0x1B3C1B35: 0x00001B3D
	"\x1b>\x1b5\x00\x00\x1b@" + // 0x1B3E1B35: 0x00001B40
	"\x1b?\x1b5\x00\x00\x1bA" + // 0x1B3F1B35: 0x00001B41
	"\x1bB\x1b5\x00\x00\x1bC" + // 0x1B421B35: 0x00001B43
	"\x00A\x03%\x00\x00\x1e\x00" + // 0x00410325: 0x00001E00
	"\x00a\x03%\x00\x00\x1e\x01" + // 0x00610325: 0x00001E01
	"\x00B\x03\a\x00\x00\x1e\x02" + // 0x00420307: 0x00001E02
	"\x00b\x03\a\x00\x00\x1e\x03" + // 0x00620307: 0x00001E03
	"\x00B\x03#\x00\x00\x1e\x04" + // 0x00420323: 0x00001E04
	"\x00b\x03#\x00\x00\x1e\x05" + // 0x00620323: 0x00001E05
	"\x00B\x031\x00\x00\x1e\x06" + // 0x00420331: 0x00001E06
	"\x00b\x031\x00\x00\x1e\a" + // 0x00620331: 0x00001E07
	"\x00\xc7\x03\x01\x00\x00\x1e\b" + // 0x00C70301: 0x00001E08
	"\x00\xe7\x03\x01\x00\x00\x1e\t" + // 0x00E70301: 0x00001E09
	"\x00D\x03\a\x00\x00\x1e\n" + // 0x00440307: 0x00001E0A
	"\x00d\x03\a\x00\x00\x1e\v" + // 0x00640307: 0x00001E0B
	"\x00D\x03#\x00\x00\x1e\f" + // 0x00440323: 0x00001E0C
	"\x00d\x03#\x00\x00\x1e\r" + // 0x00640323: 0x00001E0D
	"\x00D\x031\x00\x00\x1e\x0e" + // 0x00440331: 0x00001E0E
	"\x00d\x031\x00\x00\x1e\x0f" + // 0x00640331: 0x00001E0F
	"\x00D\x03'\x00\x00\x1e\x10" + // 0x00440327: 0x00001E10
	"\x00d\x03'\x00\x00\x1e\x11" + // 0x00640327: 0x00001E11
	"\x00D\x03-\x00\x00\x1e\x12" + // 0x0044032D: 0x00001E12
	"\x00d\x03-\x00\x00\x1e\x13" + // 0x0064032D: 0x00001E13
	"\x01\x12\x03\x00\x00\x00\x1e\x14" + // 0x01120300: 0x00001E14
	"\x01\x13\x03\x00\x00\x00\x1e\x15" + // 0x01130300: 0x00001E15
	"\x01\x12\x03\x01\x00\x00\x1e\x16" + // 0x01120301: 0x00001E16
	"\x01\x13\x03\x01\x00\x00\x1e\x17" + // 0x01130301: 0x00001E17
	"\x00E\x03-\x00\x00\x1e\x18" + // 0x0045032D: 0x00001E18
	"\x00e\x03-\x00\x00\x1e\x19" + // 0x0065032D: 0x00001E19
	"\x00E\x030\x00\x00\x1e\x1a" + // 0x00450330: 0x00001E1A
	"\x00e\x030\x00\x00\x1e\x1b" + // 0x00650330: 0x00001E1B
	"\x02(\x03\x06\x00\x00\x1e\x1c" + // 0x02280306: 0x00001E1C
	"\x02)\x03\x06\x00\x00\x1e\x1d" + // 0x02290306: 0x00001E1D
	"\x00F\x03\a\x00\x00\x1e\x1e" + // 0x00460307: 0x00001E1E
	"\x00f\x03\a\x00\x00\x1e\x1f" + // 0x00660307: 0x00001E1F
	"\x00G\x03\x04\x00\x00\x1e " + // 0x00470304: 0x00001E20
	"\x00g\x03\x04\x00\x00\x1e!" + // 0x00670304: 0x00001E21
	"\x00H\x03\a\x00\x00\x1e\"" + // 0x00480307: 0x00001E22
	"\x00h\x03\a\x00\x00\x1e#" + // 0x00680307: 0x00001E23
	"\x00H\x03#\x00\x00\x1e$" + // 0x00480323: 0x00001E24
	"\x00h\x03#\x00\x00\x1e%" + // 0x00680323: 0x00001E25
	"\x00H\x03\b\x00\x00\x1e&" + // 0x00480308: 0x00001E26
	"\x00h\x03\b\x00\x00\x1e'" + // 0x00680308: 0x00001E27
	"\x00H\x03'\x00\x00\x1e(" + // 0x00480327: 0x00001E28
	"\x00h\x03'\x00\x00\x1e)" + // 0x00680327: 0x00001E29
	"\x00H\x03.\x00\x00\x1e*" + // 0x0048032E: 0x00001E2A
	"\x00h\x03.\x00\x00\x1e+" + // 0x0068032E: 0x00001E2B
	"\x00I\x030\x00\x00\x1e," + // 0x00490330: 0x00001E2C
	"\x00i\x030\x00\x00\x1e-" + // 0x00690330: 0x00001E2D
	"\x00\xcf\x03\x01\x00\x00\x1e." + // 0x00CF0301: 0x00001E2E
	"\x00\xef\x03\x01\x00\x00\x1e/" + // 0x00EF0301: 0x00001E2F
	"\x00K\x03\x01\x00\x00\x1e0" + // 0x004B0301: 0x00001E30
	"\x00k\x03\x01\x00\x00\x1e1" + // 0x006B0301: 0x00001E31
	"\x00K\x03#\x00\x00\x1e2" + // 0x004B0323: 0x00001E32
	"\x00k\x03#\x00\x00\x1e3" + // 0x006B0323: 0x00001E33
	"\x00K\x031\x00\x00\x1e4" + // 0x004B0331: 0x00001E34
	"\x00k\x031\x00\x00\x1e5" + // 0x006B0331: 0x00001E35
	"\x00L\x03#\x00\x00\x1e6" + // 0x004C0323: 0x00001E36
	"\x00l\x03#\x00\x00\x1e7" + // 0x006C0323: 0x00001E37
	"\x1e6\x03\x04\x00\x00\x1e8" + // 0x1E360304: 0x00001E38
	"\x1e7\x03\x04\x00\x00\x1e9" + // 0x1E370304: 0x00001E39
	"\x00L\x031\x00\x00\x1e:" + // 0x004C0331: 0x00001E3A
	"\x00l\x031\x00\x00\x1e;" + // 0x006C0331: 0x00001E3B
	"\x00L\x03-\x00\x00\x1e<" + // 0x004C032D: 0x00001E3C
	"\x00l\x03-\x00\x00\x1e=" + // 0x006C032D: 0x00001E3D
	"\x00M\x03\x01\x00\x00\x1e>" + // 0x004D0301: 0x00001E3E
	"\x00m\x03\x01\x00\x00\x1e?" + // 0x006D0301: 0x00001E3F
	"\x00M\x03\a\x00\x00\x1e@" + // 0x004D0307: 0x00001E40
	"\x00m\x03\a\x00\x00\x1eA" + // 0x006D0307: 0x00001E41
	"\x00M\x03#\x00\x00\x1eB" + // 0x004D0323: 0x00001E42
	"\x00m\x03#\x00\x00\x1eC" + // 0x006D0323: 0x00001E43
	"\x00N\x03\a\x00\x00\x1eD" + // 0x004E0307: 0x00001E44
	"\x00n\x03\a\x00\x00\x1eE" + // 0x006E0307: 0x00001E45
	"\x00N\x03#\x00\x00\x1eF" + // 0x004E0323: 0x00001E46
	"\x00n\x03#\x00\x00\x1eG" + // 0x006E0323: 0x00001E47
	"\x00N\x031\x00\x00\x1eH" + // 0x004E0331: 0x00001E48
	"\x00n\x031\x00\x00\x1eI" + // 0x006E0331: 0x00001E49
	"\x00N\x03-\x00\x00\x1eJ" + // 0x004E032D: 0x00001E4A
	"\x00n\x03-\x00\x00\x1eK" + // 0x006E032D: 0x00001E4B
	"\x00\xd5\x03\x01\x00\x00\x1eL" + // 0x00D50301: 0x00001E4C
	"\x00\xf5\x03\x01\x00\x00\x1eM" + // 0x00F50301: 0x00001E4D
	"\x00\xd5\x03\b\x00\x00\x1eN" + // 0x00D50308: 0x00001E4E
	"\x00\xf5\x03\b\x00\x00\x1eO" + // 0x00F50308: 0x00001E4F
	"\x01L\x03\x00\x00\x00\x1eP" + // 0x014C0300: 0x00001E50
	"\x01M\x03\x00\x00\x00\x1eQ" + // 0x014D0300: 0x00001E51
	"\x01L\x03\x01\x00\x00\x1eR" + // 0x014C0301: 0x00001E52
	"\x01M\x03\x01\x00\x00\x1eS" + // 0x014D0301: 0x00001E53
	"\x00P\x03\x01\x00\x00\x1eT" + // 0x00500301: 0x00001E54
	"\x00p\x03\x01\x00\x00\x1eU" + // 0x00700301: 0x00001E55
	"\x00P\x03\a\x00\x00\x1eV" + // 0x00500307: 0x00001E56
	"\x00p\x03\a\x00\x00\x1eW" + // 0x00700307: 0x00001E57
	"\x00R\x03\a\x00\x00\x1eX" + // 0x00520307: 0x00001E58
	"\x00r\x03\a\x00\x00\x1eY" + // 0x00720307: 0x00001E59
	"\x00R\x03#\x00\x00\x1eZ" + // 0x00520323: 0x00001E5A
	"\x00r\x03#\x00\x00\x1e[" + // 0x00720323: 0x00001E5B
	"\x1eZ\x03\x04\x00\x00\x1e\\" + // 0x1E5A0304: 0x00001E5C
	"\x1e[\x03\x04\x00\x00\x1e]" + // 0x1E5B0304: 0x00001E5D
	"\x00R\x031\x00\x00\x1e^" + // 0x00520331: 0x00001E5E
	"\x00r\x031\x00\x00\x1e_" + // 0x00720331: 0x00001E5F
	"\x00S\x03\a\x00\x00\x1e`" + // 0x00530307: 0x00001E60
	"\x00s\x03\a\x00\x00\x1ea" + // 0x00730307: 0x00001E61
	"\x00S\x03#\x00\x00\x1eb" + // 0x00530323: 0x00001E62
	"\x00s\x03#\x00\x00\x1ec" + // 0x00730323: 0x00001E63
	"\x01Z\x03\a\x00\x00\x1ed" + // 0x015A0307: 0x00001E64
	"\x01[\x03\a\x00\x00\x1ee" + // 0x015B0307: 0x00001E65
	"\x01`\x03\a\x00\x00\x1ef" + // 0x01600307: 0x00001E66
	"\x01a\x03\a\x00\x00\x1eg" + // 0x01610307: 0x00001E67
	"\x1eb\x03\a\x00\x00\x1eh" + // 0x1E620307: 0x00001E68
	"\x1ec\x03\a\x00\x00\x1ei" + // 0x1E630307: 0x00001E69
	"\x00T\x03\a\x00\x00\x1ej" + // 0x00540307: 0x00001E6A
	"\x00t\x03\a\x00\x00\x1ek" + // 0x00740307: 0x00001E6B
	"\x00T\x03#\x00\x00\x1el" + // 0x00540323: 0x00001E6C
	"\x00t\x03#\x00\x00\x1em" + // 0x00740323: 0x00001E6D
	"\x00T\x031\x00\x00\x1en" + // 0x00540331: 0x00001E6E
	"\x00t\x031\x00\x00\x1eo" + // 0x00740331: 0x00001E6F
	"\x00T\x03-\x00\x00\x1ep" + // 0x0054032D: 0x00001E70
	"\x00t\x03-\x00\x00\x1eq" + // 0x0074032D: 0x00001E71
	"\x00U\x03$\x00\x00\x1er" + // 0x00550324: 0x00001E72
	"\x00u\x03$\x00\x00\x1es" + // 0x00750324: 0x00001E73
	"\x00U\x030\x00\x00\x1et" + // 0x00550330: 0x00001E74
	"\x00u\x030\x00\x00\x1eu" + // 0x00750330: 0x00001E75
	"\x00U\x03-\x00\x00\x1ev" + // 0x0055032D: 0x00001E76
	"\x00u\x03-\x00\x00\x1ew" + // 0x0075032D: 0x00001E77
	"\x01h\x03\x01\x00\x00\x1ex" + // 0x01680301: 0x00001E78
	"\x01i\x03\x01\x00\x00\x1ey" + // 0x01690301: 0x00001E79
	"\x01j\x03\b\x00\x00\x1ez" + // 0x016A0308: 0x00001E7A
	"\x01k\x03\b\x00\x00\x1e{" + // 0x016B0308: 0x00001E7B
	"\x00V\x03\x03\x00\x00\x1e|" + // 0x00560303: 0x00001E7C
	"\x00v\x03\x03\x00\x00\x1e}" + // 0x00760303: 0x00001E7D
	"\x00V\x03#\x00\x00\x1e~" + // 0x00560323: 0x00001E7E
	"\x00v\x03#\x00\x00\x1e\u007f" + // 0x00760323: 0x00001E7F
	"\x00W\x03\x00\x00\x00\x1e\x80" + // 0x00570300: 0x00001E80
	"\x00w\x03\x00\x00\x00\x1e\x81" + // 0x00770300: 0x00001E81
	"\x00W\x03\x01\x00\x00\x1e\x82" + // 0x00570301: 0x00001E82
	"\x00w\x03\x01\x00\x00\x1e\x83" + // 0x00770301: 0x00001E83
	"\x00W\x03\b\x00\x00\x1e\x84" + // 0x00570308: 0x00001E84
	"\x00w\x03\b\x00\x00\x1e\x85" + // 0x00770308: 0x00001E85
	"\x00W\x03\a\x00\x00\x1e\x86" + // 0x00570307: 0x00001E86
	"\x00w\x03\a\x00\x00\x1e\x87" + // 0x00770307: 0x00001E87
	"\x00W\x03#\x00\x00\x1e\x88" + // 0x00570323: 0x00001E88
	"\x00w\x03#\x00\x00\x1e\x89" + // 0x00770323: 0x00001E89
	"\x00X\x03\a\x00\x00\x1e\x8a" + // 0x00580307: 0x00001E8A
	"\x00x\x03\a\x00\x00\x1e\x8b" + // 0x00780307: 0x00001E8B
	"\x00X\x03\b\x00\x00\x1e\x8c" + // 0x00580308: 0x00001E8C
	"\x00x\x03\b\x00\x00\x1e\x8d" + // 0x00780308: 0x00001E8D
	"\x00Y\x03\a\x00\x00\x1e\x8e" + // 0x00590307: 0x00001E8E
	"\x00y\x03\a\x00\x00\x1e\x8f" + // 0x00790307: 0x00001E8F
	"\x00Z\x03\x02\x00\x00\x1e\x90" + // 0x005A0302: 0x00001E90
	"\x00z\x03\x02\x00\x00\x1e\x91" + // 0x007A0302: 0x00001E91
	"\x00Z\x03#\x00\x00\x1e\x92" + // 0x005A0323: 0x00001E92
	"\x00z\x03#\x00\x00\x1e\x93" + // 0x007A0323: 0x00001E93
	"\x00Z\x031\x00\x00\x1e\x94" + // 0x005A0331: 0x00001E94
	"\x00z\x031\x00\x00\x1e\x95" + // 0x007A0331: 0x00001E95
	"\x00h\x031\x00\x00\x1e\x96" + // 0x00680331: 0x00001E96
	"\x00t\x03\b\x00\x00\x1e\x97" + // 0x00740308: 0x00001E97
	"\x00w\x03\n\x00\x00\x1e\x98" + // 0x0077030A: 0x00001E98
	"\x00y\x03\n\x00\x00\x1e\x99" + // 0x0079030A: 0x00001E99
	"\x01\u007f\x03\a\x00\x00\x1e\x9b" + // 0x017F0307: 0x00001E9B
	"\x00A\x03#\x00\x00\x1e\xa0" + // 0x00410323: 0x00001EA0
	"\x00a\x03#\x00\x00\x1e\xa1" + // 0x00610323: 0x00001EA1
	"\x00A\x03\t\x00\x00\x1e\xa2" + // 0x00410309: 0x00001EA2
	"\x00a\x03\t\x00\x00\x1e\xa3" + // 0x00610309: 0x00001EA3
	"\x00\xc2\x03\x01\x00\x00\x1e\xa4" + // 0x00C20301: 0x00001EA4
	"\x00\xe2\x03\x01\x00\x00\x1e\xa5" + // 0x00E20301: 0x00001EA5
	"\x00\xc2\x03\x00\x00\x00\x1e\xa6" + // 0x00C20300: 0x00001EA6
	"\x00\xe2\x03\x00\x00\x00\x1e\xa7" + // 0x00E20300: 0x00001EA7
	"\x00\xc2\x03\t\x00\x00\x1e\xa8" + // 0x00C20309: 0x00001EA8
	"\x00\xe2\x03\t\x00\x00\x1e\xa9" + // 0x00E20309: 0x00001EA9
	"\x00\xc2\x03\x03\x00\x00\x1e\xaa" + // 0x00C20303: 0x00001EAA
	"\x00\xe2\x03\x03\x00\x00\x1e\xab" + // 0x00E20303: 0x00001EAB
	"\x1e\xa0\x03\x02\x00\x00\x1e\xac" + // 0x1EA00302: 0x00001EAC
	"\x1e\xa1\x03\x02\x00\x00\x1e\xad" + // 0x1EA10302: 0x00001EAD
	"\x01\x02\x03\x01\x00\x00\x1e\xae" + // 0x01020301: 0x00001EAE
	"\x01\x03\x03\x01\x00\x00\x1e\xaf" + // 0x01030301: 0x00001EAF
	"\x01\x02\x03\x00\x00\x00\x1e\xb0" + // 0x01020300: 0x00001EB0
	"\x01\x03\x03\x00\x00\x00\x1e\xb1" + // 0x01030300: 0x00001EB1
	"\x01\x02\x03\t\x00\x00\x1e\xb2" + // 0x01020309: 0x00001EB2
	"\x01\x03\x03\t\x00\x00\x1e\xb3" + // 0x01030309: 0x00001EB3
	"\x01\x02\x03\x03\x00\x00\x1e\xb4" + // 0x01020303: 0x00001EB4
	"\x01\x03\x03\x03\x00\x00\x1e\xb5" + // 0x01030303: 0x00001EB5
	"\x1e\xa0\x03\x06\x00\x00\x1e\xb6" + // 0x1EA00306: 0x00001EB6
	"\x1e\xa1\x03\x06\x00\x00\x1e\xb7" + // 0x1EA10306: 0x00001EB7
	"\x00E\x03#\x00\x00\x1e\xb8" + // 0x00450323: 0x00001EB8
	"\x00e\x03#\x00\x00\x1e\xb9" + // 0x00650323: 0x00001EB9
	"\x00E\x03\t\x00\x00\x1e\xba" + // 0x00450309: 0x00001EBA
	"\x00e\x03\t\x00\x00\x1e\xbb" + // 0x00650309: 0x00001EBB
	"\x00E\x03\x03\x00\x00\x1e\xbc" + // 0x00450303: 0x00001EBC
	"\x00e\x03\x03\x00\x00\x1e\xbd" + // 0x00650303: 0x00001EBD
	"\x00\xca\x03\x01\x00\x00\x1e\xbe" + // 0x00CA0301: 0x00001EBE
	"\x00\xea\x03\x01\x00\x00\x1e\xbf" + // 0x00EA0301: 0x00001EBF
	"\x00\xca\x03\x00\x00\x00\x1e\xc0" + // 0x00CA0300: 0x00001EC0
	"\x00\xea\x03\x00\x00\x00\x1e\xc1" + // 0x00EA0300: 0x00001EC1
	"\x00\xca\x03\t\x00\x00\x1e\xc2" + // 0x00CA0309: 0x00001EC2
	"\x00\xea\x03\t\x00\x00\x1e\xc3" + // 0x00EA0309: 0x00001EC3
	"\x00\xca\x03\x03\x00\x00\x1e\xc4" + // 0x00CA0303: 0x00001EC4
	"\x00\xea\x03\x03\x00\x00\x1e\xc5" + // 0x00EA0303: 0x00001EC5
	"\x1e\xb8\x03\x02\x00\x00\x1e\xc6" + // 0x1EB80302: 0x00001EC6
	"\x1e\xb9\x03\x02\x00\x00\x1e\xc7" + // 0x1EB90302: 0x00001EC7
	"\x00I\x03\t\x00\x00\x1e\xc8" + // 0x00490309: 0x00001EC8
	"\x00i\x03\t\x00\x00\x1e\xc9" + // 0x00690309: 0x00001EC9
	"\x00I\x03#\x00\x00\x1e\xca" + // 0x00490323: 0x00001ECA
	"\x00i\x03#\x00\x00\x1e\xcb" + // 0x00690323: 0x00001ECB
	"\x00O\x03#\x00\x00\x1e\xcc" + // 0x004F0323: 0x00001ECC
	"\x00o\x03#\x00\x00\x1e\xcd" + // 0x006F0323: 0x00001ECD
	"\x00O\x03\t\x00\x00\x1e\xce" + // 0x004F0309: 0x00001ECE
	"\x00o\x03\t\x00\x00\x1e\xcf" + // 0x006F0309: 0x00001ECF
	"\x00\xd4\x03\x01\x00\x00\x1e\xd0" + // 0x00D40301: 0x00001ED0
	"\x00\xf4\x03\x01\x00\x00\x1e\xd1" + // 0x00F40301: 0x00001ED1
	"\x00\xd4\x03\x00\x00\x00\x1e\xd2" + // 0x00D40300: 0x00001ED2
	"\x00\xf4\x03\x00\x00\x00\x1e\xd3" + // 0x00F40300: 0x00001ED3
	"\x00\xd4\x03\t\x00\x00\x1e\xd4" + // 0x00D40309: 0x00001ED4
	"\x00\xf4\x03\t\x00\x00\x1e\xd5" + // 0x00F40309: 0x00001ED5
	"\x00\xd4\x03\x03\x00\x00\x1e\xd6" + // 0x00D40303: 0x00001ED6
	"\x00\xf4\x03\x03\x00\x00\x1e\xd7" + // 0x00F40303: 0x00001ED7
	"\x1e\xcc\x03\x02\x00\x00\x1e\xd8" + // 0x1ECC0302: 0x00001ED8
	"\x1e\xcd\x03\x02\x00\x00\x1e\xd9" + // 0x1ECD0302: 0x00001ED9
	"\x01\xa0\x03\x01\x00\x00\x1e\xda" + // 0x01A00301: 0x00001EDA
	"\x01\xa1\x03\x01\x00\x00\x1e\xdb" + // 0x01A10301: 0x00001EDB
	"\x01\xa0\x03\x00\x00\x00\x1e\xdc" + // 0x01A00300: 0x00001EDC
	"\x01\xa1\x03\x00\x00\x00\x1e\xdd" + // 0x01A10300: 0x00001EDD
	"\x01\xa0\x03\t\x00\x00\x1e\xde" + // 0x01A00309: 0x00001EDE
	"\x01\xa1\x03\t\x00\x00\x1e\xdf" + // 0x01A10309: 0x00001EDF
	"\x01\xa0\x03\x03\x00\x00\x1e\xe0" + // 0x01A00303: 0x00001EE0
	"\x01\xa1\x03\x03\x00\x00\x1e\xe1" + // 0x01A10303: 0x00001EE1
	"\x01\xa0\x03#\x00\x00\x1e\xe2" + // 0x01A00323: 0x00001EE2
	"\x01\xa1\x03#\x00\x00\x1e\xe3" + // 0x01A10323: 0x00001EE3
	"\x00U\x03#\x00\x00\x1e\xe4" + // 0x00550323: 0x00001EE4
	"\x00u\x03#\x00\x00\x1e\xe5" + // 0x00750323: 0x00001EE5
	"\x00U\x03\t\x00\x00\x1e\xe6" + // 0x00550309: 0x00001EE6
	"\x00u\x03\t\x00\x00\x1e\xe7" + // 0x00750309: 0x00001EE7
	"\x01\xaf\x03\x01\x00\x00\x1e\xe8" + // 0x01AF0301: 0x00001EE8
	"\x01\xb0\x03\x01\x00\x00\x1e\xe9" + // 0x01B00301: 0x00001EE9
	"\x01\xaf\x03\x00\x00\x00\x1e\xea" + // 0x01AF0300: 0x00001EEA
	"\x01\xb0\x03\x00\x00\x00\x1e\xeb" + // 0x01B00300: 0x00001EEB
	"\x01\xaf\x03\t\x00\x00\x1e\xec" + // 0x01AF0309: 0x00001EEC
	"\x01\xb0\x03\t\x00\x00\x1e\xed" + // 0x01B00309: 0x00001EED
	"\x01\xaf\x03\x03\x00\x00\x1e\xee" + // 0x01AF0303: 0x00001EEE
	"\x01\xb0\x03\x03\x00\x00\x1e\xef" + // 0x01B00303: 0x00001EEF
	"\x01\xaf\x03#\x00\x00\x1e\xf0" + // 0x01AF0323: 0x00001EF0
	"\x01\xb0\x03#\x00\x00\x1e\xf1" + // 0x01B00323: 0x00001EF1
	"\x00Y\x03\x00\x00\x00\x1e\xf2" + // 0x00590300: 0x00001EF2
	"\x00y\x03\x00\x00\x00\x1e\xf3" + // 0x00790300: 0x00001EF3
	"\x00Y\x03#\x00\x00\x1e\xf4" + // 0x00590323: 0x00001EF4
	"\x00y\x03#\x00\x00\x1e\xf5" + // 0x00790323: 0x00001EF5
	"\x00Y\x03\t\x00\x00\x1e\xf6" + // 0x00590309: 0x00001EF6
	"\x00y\x03\t\x00\x00\x1e\xf7" + // 0x00790309: 0x00001EF7
	"\x00Y\x03\x03\x00\x00\x1e\xf8" + // 0x00590303: 0x00001EF8
	"\x00y\x03\x03\x00\x00\x1e\xf9" + // 0x00790303: 0x00001EF9
	"\x03\xb1\x03\x13\x00\x00\x1f\x00" + // 0x03B10313: 0x00001F00
	"\x03\xb1\x03\x14\x00\x00\x1f\x01" + // 0x03B10314: 0x00001F01
	"\x1f\x00\x03\x00\x00\x00\x1f\x02" + // 0x1F000300: 0x00001F02
	"\x1f\x01\x03\x00\x00\x00\x1f\x03" + // 0x1F010300: 0x00001F03
	"\x1f\x00\x03\x01\x00\x00\x1f\x04" + // 0x1F000301: 0x00001F04
	"\x1f\x01\x03\x01\x00\x00\x1f\x05" + // 0x1F010301: 0x00001F05
	"\x1f\x00\x03B\x00\x00\x1f\x06" + // 0x1F000342: 0x00001F06
	"\x1f\x01\x03B\x00\x00\x1f\a" + // 0x1F010342: 0x00001F07
	"\x03\x91\x03\x13\x00\x00\x1f\b" + // 0x03910313: 0x00001F08
	"\x03\x91\x03\x14\x00\x00\x1f\t" + // 0x03910314: 0x00001F09
	"\x1f\b\x03\x00\x00\x00\x1f\n" + // 0x1F080300: 0x00001F0A
	"\x1f\t\x03\x00\x00\x00\x1f\v" + // 0x1F090300: 0x00001F0B
	"\x1f\b\x03\x01\x00\x00\x1f\f" + // 0x1F080301: 0x00001F0C
	"\x1f\t\x03\x01\x00\x00\x1f\r" + // 0x1F090301: 0x00001F0D
	"\x1f\b\x03B\x00\x00\x1f\x0e" + // 0x1F080342: 0x00001F0E
	"\x1f\t\x03B\x00\x00\x1f\x0f" + // 0x1F090342: 0x00001F0F
	"\x03\xb5\x03\x13\x00\x00\x1f\x10" + // 0x03B50313: 0x00001F10
	"\x03\xb5\x03\x14\x00\x00\x1f\x11" + // 0x03B50314: 0x00001F11
	"\x1f\x10\x03\x00\x00\x00\x1f\x12" + // 0x1F100300: 0x00001F12
	"\x1f\x11\x03\x00\x00\x00\x1f\x13" + // 0x1F110300: 0x00001F13
	"\x1f\x10\x03\x01\x00\x00\x1f\x14" + // 0x1F100301: 0x00001F14
	"\x1f\x11\x03\x01\x00\x00\x1f\x15" + // 0x1F110301: 0x00001F15
	"\x03\x95\x03\x13\x00\x00\x1f\x18" + // 0x03950313: 0x00001F18
	"\x03\x95\x03\x14\x00\x00\x1f\x19" + // 0x03950314: 0x00001F19
	"\x1f\x18\x03\x00\x00\x00\x1f\x1a" + // 0x1F180300: 0x00001F1A
	"\x1f\x19\x03\x00\x00\x00\x1f\x1b" + // 0x1F190300: 0x00001F1B
	"\x1f\x18\x03\x01\x00\x00\x1f\x1c" + // 0x1F180301: 0x00001F1C
	"\x1f\x19\x03\x01\x00\x00\x1f\x1d" + // 0x1F190301: 0x00001F1D
	"\x03\xb7\x03\x13\x00\x00\x1f " + // 0x03B70313: 0x00001F20
	"\x03\xb7\x03\x14\x00\x00\x1f!" + // 0x03B70314: 0x00001F21
	"\x1f \x03\x00\x00\x00\x1f\"" + // 0x1F200300: 0x00001F22
	"\x1f!\x03\x00\x00\x00\x1f#" + // 0x1F210300: 0x00001F23
	"\x1f \x03\x01\x00\x00\x1f$" + // 0x1F200301: 0x00001F24
	"\x1f!\x03\x01\x00\x00\x1f%" + // 0x1F210301: 0x00001F25
	"\x1f \x03B\x00\x00\x1f&" + // 0x1F200342: 0x00001F26
	"\x1f!\x03B\x00\x00\x1f'" + // 0x1F210342: 0x00001F27
	"\x03\x97\x03\x13\x00\x00\x1f(" + // 0x03970313: 0x00001F28
	"\x03\x97\x03\x14\x00\x00\x1f)" + // 0x03970314: 0x00001F29
	"\x1f(\x03\x00\x00\x00\x1f*" + // 0x1F280300: 0x00001F2A
	"\x1f)\x03\x00\x00\x00\x1f+" + // 0x1F290300: 0x00001F2B
	"\x1f(\x03\x01\x00\x00\x1f," + // 0x1F280301: 0x00001F2C
	"\x1f)\x03\x01\x00\x00\x1f-" + // 0x1F290301: 0x00001F2D
	"\x1f(\x03B\x00\x00\x1f." + // 0x1F280342: 0x00001F2E
	"\x1f)\x03B\x00\x00\x1f/" + // 0x1F290342: 0x00001F2F
	"\x03\xb9\x03\x13\x00\x00\x1f0" + // 0x03B90313: 0x00001F30
	"\x03\xb9\x03\x14\x00\x00\x1f1" + // 0x03B90314: 0x00001F31
	"\x1f0\x03\x00\x00\x00\x1f2" + // 0x1F300300: 0x00001F32
	"\x1f1\x03\x00\x00\x00\x1f3" + // 0x1F310300: 0x00001F33
	"\x1f0\x03\x01\x00\x00\x1f4" + // 0x1F300301: 0x00001F34
	"\x1f1\x03\x01\x00\x00\x1f5" + // 0x1F310301: 0x00001F35
	"\x1f0\x03B\x00\x00\x1f6" + // 0x1F300342: 0x00001F36
	"\x1f1\x03B\x00\x00\x1f7" + // 0x1F310342: 0x00001F37
	"\x03\x99\x03\x13\x00\x00\x1f8" + // 0x03990313: 0x00001F38
	"\x03\x99\x03\x14\x00\x00\x1f9" + // 0x03990314: 0x00001F39
	"\x1f8\x03\x00\x00\x00\x1f:" + // 0x1F380300: 0x00001F3A
	"\x1f9\x03\x00\x00\x00\x1f;" + // 0x1F390300: 0x00001F3B
	"\x1f8\x03\x01\x00\x00\x1f<" + // 0x1F380301: 0x00001F3C
	"\x1f9\x03\x01\x00\x00\x1f=" + // 0x1F390301: 0x00001F3D
	"\x1f8\x03B\x00\x00\x1f>" + // 0x1F380342: 0x00001F3E
	"\x1f9\x03B\x00\x00\x1f?" + // 0x1F390342: 0x00001F3F
	"\x03\xbf\x03\x13\x00\x00\x1f@" + // 0x03BF0313: 0x00001F40
	"\x03\xbf\x03\x14\x00\x00\x1fA" + // 0x03BF0314: 0x00001F41
	"\x1f@\x03\x00\x00\x00\x1fB" + // 0x1F400300: 0x00001F42
	"\x1fA\x03\x00\x00\x00\x1fC" + // 0x1F410300: 0x00001F43
	"\x1f@\x03\x01\x00\x00\x1fD" + // 0x1F400301: 0x00001F44
	"\x1fA\x03\x01\x00\x00\x1fE" + // 0x1F410301: 0x00001F45
	"\x03\x9f\x03\x13\x00\x00\x1fH" + // 0x039F0313: 0x00001F48
	"\x03\x9f\x03\x14\x00\x00\x1fI" + // 0x039F0314: 0x00001F49
	"\x1fH\x03\x00\x00\x00\x1fJ" + // 0x1F480300: 0x00001F4A
	"\x1fI\x03\x00\x00\x00\x1fK" + // 0x1F490300: 0x00001F4B
	"\x1fH\x03\x01\x00\x00\x1fL" + // 0x1F480301: 0x00001F4C
	"\x1fI\x03\x01\x00\x00\x1fM" + // 0x1F490301: 0x00001F4D
	"\x03\xc5\x03\x13\x00\x00\x1fP" + // 0x03C50313: 0x00001F50
	"\x03\xc5\x03\x14\x00\x00\x1fQ" + // 0x03C50314: 0x00001F51
	"\x1fP\x03\x00\x00\x00\x1fR" + // 0x1F500300: 0x00001F52
	"\x1fQ\x03\x00\x00\x00\x1fS" + // 0x1F510300: 0x00001F53
	"\x1fP\x03\x01\x00\x00\x1fT" + // 0x1F500301: 0x00001F54
	"\x1fQ\x03\x01\x00\x00\x1fU" + // 0x1F510301: 0x00001F55
	"\x1fP\x03B\x00\x00\x1fV" + // 0x1F500342: 0x00001F56
	"\x1fQ\x03B\x00\x00\x1fW" + // 0x1F510342: 0x00001F57
	"\x03\xa5\x03\x14\x00\x00\x1fY" + // 0x03A50314: 0x00001F59
	"\x1fY\x03\x00\x00\x00\x1f[" + // 0x1F590300: 0x00001F5B
	"\x1fY\x03\x01\x00\x00\x1f]" + // 0x1F590301: 0x00001F5D
	"\x1fY\x03B\x00\x00\x1f_" + // 0x1F590342: 0x00001F5F
	"\x03\xc9\x03\x13\x00\x00\x1f`" + // 0x03C90313: 0x00001F60
	"\x03\xc9\x03\x14\x00\x00\x1fa" + // 0x03C90314: 0x00001F61
	"\x1f`\x03\x00\x00\x00\x1fb" + // 0x1F600300: 0x00001F62
	"\x1fa\x03\x00\x00\x00\x1fc" + // 0x1F610300: 0x00001F63
	"\x1f`\x03\x01\x00\x00\x1fd" + // 0x1F600301: 0x00001F64
	"\x1fa\x03\x01\x00\x00\x1fe" + // 0x1F610301: 0x00001F65
	"\x1f`\x03B\x00\x00\x1ff" + // 0x1F600342: 0x00001F66
	"\x1fa\x03B\x00\x00\x1fg" + // 0x1F610342: 0x00001F67
	"\x03\xa9\x03\x13\x00\x00\x1fh" + // 0x03A90313: 0x00001F68
	"\x03\xa9\x03\x14\x00\x00\x1fi" + // 0x03A90314: 0x00001F69
	"\x1fh\x03\x00\x00\x00\x1fj" + // 0x1F680300: 0x00001F6A
	"\x1fi\x03\x00\x00\x00\x1fk" + // 0x1F690300: 0x00001F6B
	"\x1fh\x03\x01\x00\x00\x1fl" + // 0x1F680301: 0x00001F6C
	"\x1fi\x03\x01\x00\x00\x1fm" + // 0x1F690301: 0x00001F6D
	"\x1fh\x03B\x00\x00\x1fn" + // 0x1F680342: 0x00001F6E
	"\x1fi\x03B\x00\x00\x1fo" + // 0x1F690342: 0x00001F6F
	"\x03\xb1\x03\x00\x00\x00\x1fp" + // 0x03B10300: 0x00001F70
	"\x03\xb5\x03\x00\x00\x00\x1fr" + // 0x03B50300: 0x00001F72
	"\x03\xb7\x03\x00\x00\x00\x1ft" + // 0x03B70300: 0x00001F74
	"\x03\xb9\x03\x00\x00\x00\x1fv" + // 0x03B90300: 0x00001F76
	"\x03\xbf\x03\x00\x00\x00\x1fx" + // 0x03BF0300: 0x00001F78
	"\x03\xc5\x03\x00\x00\x00\x1fz" + // 0x03C50300: 0x00001F7A
	"\x03\xc9\x03\x00\x00\x00\x1f|" + // 0x03C90300: 0x00001F7C
	"\x1f\x00\x03E\x00\x00\x1f\x80" + // 0x1F000345: 0x00001F80
	"\x1f\x01\x03E\x00\x00\x1f\x81" + // 0x1F010345: 0x00001F81
	"\x1f\x02\x03E\x00\x00\x1f\x82" + // 0x1F020345: 0x00001F82
	"\x1f\x03\x03E\x00\x00\x1f\x83" + // 0x1F030345: 0x00001F83
	"\x1f\x04\x03E\x00\x00\x1f\x84" + // 0x1F040345: 0x00001F84
	"\x1f\x05\x03E\x00\x00\x1f\x85" + // 0x1F050345: 0x00001F85
	"\x1f\x06\x03E\x00\x00\x1f\x86" + // 0x1F060345: 0x00001F86
	"\x1f\a\x03E\x00\x00\x1f\x87" + // 0x1F070345: 0x00001F87
	"\x1f\b\x03E\x00\x00\x1f\x88" + // 0x1F080345: 0x00001F88
	"\x1f\t\x03E\x00\x00\x1f\x89" + // 0x1F090345: 0x00001F89
	"\x1f\n\x03E\x00\x00\x1f\x8a" + // 0x1F0A0345: 0x00001F8A
	"\x1f\v\x03E\x00\x00\x1f\x8b" + // 0x1F0B0345: 0x00001F8B
	"\x1f\f\x03E\x00\x00\x1f\x8c" + // 0x1F0C0345: 0x00001F8C
	"\x1f\r\x03E\x00\x00\x1f\x8d" + // 0x1F0D0345: 0x00001F8D
	"\x1f\x0e\x03E\x00\x00\x1f\x8e" + // 0x1F0E0345: 0x00001F8E
	"\x1f\x0f\x03E\x00\x00\x1f\x8f" + // 0x1F0F0345: 0x00001F8F
	"\x1f \x03E\x00\x00\x1f\x90" + // 0x1F200345: 0x00001F90
	"\x1f!\x03E\x00\x00\x1f\x91" + // 0x1F210345: 0x00001F91
	"\x1f\"\x03E\x00\x00\x1f\x92" + // 0x1F220345: 0x00001F92
	"\x1f#\x03E\x00\x00\x1f\x93" + // 0x1F230345: 0x00001F93
	"\x1f$\x03E\x00\x00\x1f\x94" + // 0x1F240345: 0x00001F94
	"\x1f%\x03E\x00\x00\x1f\x95" + // 0x1F250345: 0x00001F95
	"\x1f&\x03E\x00\x00\x1f\x96" + // 0x1F260345: 0x00001F96
	"\x1f'\x03E\x00\x00\x1f\x97" + // 0x1F270345: 0x00001F97
	"\x1f(\x03E\x00\x00\x1f\x98" + // 0x1F280345: 0x00001F98
	"\x1f)\x03E\x00\x00\x1f\x99" + // 0x1F290345: 0x00001F99
	"\x1f*\x03E\x00\x00\x1f\x9a" + // 0x1F2A0345: 0x00001F9A
	"\x1f+\x03E\x00\x00\x1f\x9b" + // 0x1F2B0345: 0x00001F9B
	"\x1f,\x03E\x00\x00\x1f\x9c" + // 0x1F2C0345: 0x00001F9C
	"\x1f-\x03E\x00\x00\x1f\x9d" + // 0x1F2D0345: 0x00001F9D
	"\x1f.\x03E\x00\x00\x1f\x9e" + // 0x1F2E0345: 0x00001F9E
	"\x1f/\x03E\x00\x00\x1f\x9f" + // 0x1F2F0345: 0x00001F9F
	"\x1f`\x03E\x00\x00\x1f\xa0" + // 0x1F600345: 0x00001FA0
	"\x1fa\x03E\x00\x00\x1f\xa1" + // 0x1F610345: 0x00001FA1
	"\x1fb\x03E\x00\x00\x1f\xa2" + // 0x1F620345: 0x00001FA2
	"\x1fc\x03E\x00\x00\x1f\xa3" + // 0x1F630345: 0x00001FA3
	"\x1fd\x03E\x00\x00\x1f\xa4" + // 0x1F640345: 0x00001FA4
	"\x1fe\x03E\x00\x00\x1f\xa5" + // 0x1F650345: 0x00001FA5
	"\x1ff\x03E\x00\x00\x1f\xa6" + // 0x1F660345: 0x00001FA6
	"\x1fg\x03E\x00\x00\x1f\xa7" + // 0x1F670345: 0x00001FA7
	"\x1fh\x03E\x00\x00\x1f\xa8" + // 0x1F680345: 0x00001FA8
	"\x1fi\x03E\x00\x00\x1f\xa9" + // 0x1F690345: 0x00001FA9
	"\x1fj\x03E\x00\x00\x1f\xaa" + // 0x1F6A0345: 0x00001FAA
	"\x1fk\x03E\x00\x00\x1f\xab" + // 0x1F6B0345: 0x00001FAB
	"\x1fl\x03E\x00\x00\x1f\xac" + // 0x1F6C0345: 0x00001FAC
	"\x1fm\x03E\x00\x00\x1f\xad" + // 0x1F6D0345: 0x00001FAD
	"\x1fn\x03E\x00\x00\x1f\xae" + // 0x1F6E0345: 0x00001FAE
	"\x1fo\x03E\x00\x00\x1f\xaf" + // 0x1F6F0345: 0x00001FAF
	"\x03\xb1\x03\x06\x00\x00\x1f\xb0" + // 0x03B10306: 0x00001FB0
	"\x03\xb1\x03\x04\x00\x00\x1f\xb1" + // 0x03B10304: 0x00001FB1
	"\x1fp\x03E\x00\x00\x1f\xb2" + // 0x1F700345: 0x00001FB2
	"\x03\xb1\x03E\x00\x00\x1f\xb3" + // 0x03B10345: 0x00001FB3
	"\x03\xac\x03E\x00\x00\x1f\xb4" + // 0x03AC0345: 0x00001FB4
	"\x03\xb1\x03B\x00\x00\x1f\xb6" + // 0x03B10342: 0x00001FB6
	"\x1f\xb6\x03E\x00\x00\x1f\xb7" + // 0x1FB60345: 0x00001FB7
	"\x03\x91\x03\x06\x00\x00\x1f\xb8" + // 0x03910306: 0x00001FB8
	"\x03\x91\x03\x04\x00\x00\x1f\xb9" + // 0x03910304: 0x00001FB9
	"\x03\x91\x03\x00\x00\x00\x1f\xba" + // 0x03910300: 0x00001FBA
	"\x03\x91\x03E\x00\x00\x1f\xbc" + // 0x03910345: 0x00001FBC
	"\x00\xa8\x03B\x00\x00\x1f\xc1" + // 0x00A80342: 0x00001FC1
	"\x1ft\x03E\x00\x00\x1f\xc2" + // 0x1F740345: 0x00001FC2
	"\x03\xb7\x03E\x00\x00\x1f\xc3" + // 0x03B70345: 0x00001FC3
	"\x03\xae\x03E\x00\x00\x1f\xc4" + // 0x03AE0345: 0x00001FC4
	"\x03\xb7\x03B\x00\x00\x1f\xc6" + // 0x03B70342: 0x00001FC6
	"\x1f\xc6\x03E\x00\x00\x1f\xc7" + // 0x1FC60345: 0x00001FC7
	"\x03\x95\x03\x00\x00\x00\x1f\xc8" + // 0x03950300: 0x00001FC8
	"\x03\x97\x03\x00\x00\x00\x1f\xca" + // 0x03970300: 0x00001FCA
	"\x03\x97\x03E\x00\x00\x1f\xcc" + // 0x03970345: 0x00001FCC
	"\x1f\xbf\x03\x00\x00\x00\x1f\xcd" + // 0x1FBF0300: 0x00001FCD
	"\x1f\xbf\x03\x01\x00\x00\x1f\xce" + // 0x1FBF0301: 0x00001FCE
	"\x1f\xbf\x03B\x00\x00\x1f\xcf" + // 0x1FBF0342: 0x00001FCF
	"\x03\xb9\x03\x06\x00\x00\x1f\xd0" + // 0x03B90306: 0x00001FD0
	"\x03\xb9\x03\x04\x00\x00\x1f\xd1" + // 0x03B90304: 0x00001FD1
	"\x03\xca\x03\x00\x00\x00\x1f\xd2" + // 0x03CA0300: 0x00001FD2
	"\x03\xb9\x03B\x00\x00\x1f\xd6" + // 0x03B90342: 0x00001FD6
	"\x03\xca\x03B\x00\x00\x1f\xd7" + // 0x03CA0342: 0x00001FD7
	"\x03\x99\x03\x06\x00\x00\x1f\xd8" + // 0x03990306: 0x00001FD8
	"\x03\x99\x03\x04\x00\x00\x1f\xd9" + // 0x03990304: 0x00001FD9
	"\x03\x99\x03\x00\x00\x00\x1f\xda" + // 0x03990300: 0x00001FDA
	"\x1f\xfe\x03\x00\x00\x00\x1f\xdd" + // 0x1FFE0300: 0x00001FDD
	"\x1f\xfe\x03\x01\x00\x00\x1f\xde" + // 0x1FFE0301: 0x00001FDE
	"\x1f\xfe\x03B\x00\x00\x1f\xdf" + // 0x1FFE0342: 0x00001FDF
	"\x03\xc5\x03\x06\x00\x00\x1f\xe0" + // 0x03C50306: 0x00001FE0
	"\x03\xc5\x03\x04\x00\x00\x1f\xe1" + // 0x03C50304: 0x00001FE1
	"\x03\xcb\x03\x00\x00\x00\x1f\xe2" + // 0x03CB0300: 0x00001FE2
	"\x03\xc1\x03\x13\x00\x00\x1f\xe4" + // 0x03C10313: 0x00001FE4
	"\x03\xc1\x03\x14\x00\x00\x1f\xe5" + // 0x03C10314: 0x00001FE5
	"\x03\xc5\x03B\x00\x00\x1f\xe6" + // 0x03C50342: 0x00001FE6
	"\x03\xcb\x03B\x00\x00\x1f\xe7" + // 0x03CB0342: 0x00001FE7
	"\x03\xa5\x03\x06\x00\x00\x1f\xe8" + // 0x03A50306: 0x00001FE8
	"\x03\xa5\x03\x04\x00\x00\x1f\xe9" + // 0x03A50304: 0x00001FE9
	"\x03\xa5\x03\x00\x00\x00\x1f\xea" + // 0x03A50300: 0x00001FEA
	"\x03\xa1\x03\x14\x00\x00\x1f\xec" + // 0x03A10314: 0x00001FEC
	"\x00\xa8\x03\x00\x00\x00\x1f\xed" + // 0x00A80300: 0x00001FED
	"\x1f|\x03E\x00\x00\x1f\xf2" + // 0x1F7C0345: 0x00001FF2
	"\x03\xc9\x03E\x00\x00\x1f\xf3" + // 0x03C90345: 0x00001FF3
	"\x03\xce\x03E\x00\x00\x1f\xf4" + // 0x03CE0345: 0x00001FF4
	"\x03\xc9\x03B\x00\x00\x1f\xf6" + // 0x03C90342: 0x00001FF6
	"\x1f\xf6\x03E\x00\x00\x1f\xf7" + // 0x1FF60345: 0x00001FF7
	"\x03\x9f\x03\x00\x00\x00\x1f\xf8" + // 0x039F0300: 0x00001FF8
	"\x03\xa9\x03\x00\x00\x00\x1f\xfa" + // 0x03A90300: 0x00001FFA
	"\x03\xa9\x03E\x00\x00\x1f\xfc" + // 0x03A90345: 0x00001FFC
	"!\x90\x038\x00\x00!\x9a" + // 0x21900338: 0x0000219A
	"!\x92\x038\x00\x00!\x9b" + // 0x21920338: 0x0000219B
	"!\x94\x038\x00\x00!\xae" + // 0x21940338: 0x000021AE
	"!\xd0\x038\x00\x00!\xcd" + // 0x21D00338: 0x000021CD
	"!\xd4\x038\x00\x00!\xce" + // 0x21D40338: 0x000021CE
	"!\xd2\x038\x00\x00!\xcf" + // 0x21D20338: 0x000021CF
	"\"\x03\x038\x00\x00\"\x04" + // 0x22030338: 0x00002204
	"\"\b\x038\x00\x00\"\t" + // 0x22080338: 0x00002209
	"\"\v\x038\x00\x00\"\f" + // 0x220B0338: 0x0000220C
	"\"#\x038\x00\x00\"$" + // 0x22230338: 0x00002224
	"\"%\x038\x00\x00\"&" + // 0x22250338: 0x00002226
	"\"<\x038\x00\x00\"A" + // 0x223C0338: 0x00002241
	"\"C\x038\x00\x00\"D" + // 0x22430338: 0x00002244
	"\"E\x038\x00\x00\"G" + // 0x22450338: 0x00002247
	"\"H\x038\x00\x00\"I" + // 0x22480338: 0x00002249
	"\x00=\x038\x00\x00\"`" + // 0x003D0338: 0x00002260
	"\"a\x038\x00\x00\"b" + // 0x22610338: 0x00002262
	"\"M\x038\x00\x00\"m" + // 0x224D0338: 0x0000226D
	"\x00<\x038\x00\x00\"n" + // 0x003C0338: 0x0000226E
	"\x00>\x038\x00\x00\"o" + // 0x003E0338: 0x0000226F
	"\"d\x038\x00\x00\"p" + // 0x22640338: 0x00002270
	"\"e\x038\x00\x00\"q" + // 0x22650338: 0x00002271
	"\"r\x038\x00\x00\"t" + // 0x22720338: 0x00002274
	"\"s\x038\x00\x00\"u" + // 0x22730338: 0x00002275
	"\"v\x038\x00\x00\"x" + // 0x22760338: 0x00002278
	"\"w\x038\x00\x00\"y" + // 0x22770338: 0x00002279
	"\"z\x038\x00\x00\"\x80" + // 0x227A0338: 0x00002280
	"\"{\x038\x00\x00\"\x81" + // 0x227B0338: 0x00002281
	"\"\x82\x038\x00\x00\"\x84" + // 0x22820338: 0x00002284
	"\"\x83\x038\x00\x00\"\x85" + // 0x22830338: 0x00002285
	"\"\x86\x038\x00\x00\"\x88" + // 0x22860338: 0x00002288
	"\"\x87\x038\x00\x00\"\x89" + // 0x22870338: 0x00002289
	"\"\xa2\x038\x00\x00\"\xac" + // 0x22A20338: 0x000022AC
	"\"\xa8\x038\x00\x00\"\xad" + // 0x22A80338: 0x000022AD
	"\"\xa9\x038\x00\x00\"\xae" + // 0x22A90338: 0x000022AE
	"\"\xab\x038\x00\x00\"\xaf" + // 0x22AB0338: 0x000022AF
	"\"|\x038\x00\x00\"\xe0" + // 0x227C0338: 0x000022E0
	"\"}\x038\x00\x00\"\xe1" + // 0x227D0338: 0x000022E1
	"\"\x91\x038\x00\x00\"\xe2" + // 0x22910338: 0x000022E2
	"\"\x92\x038\x00\x00\"\xe3" + // 0x22920338: 0x000022E3
	"\"\xb2\x038\x00\x00\"\xea" + // 0x22B20338: 0x000022EA
	"\"\xb3\x038\x00\x00\"\xeb" + // 0x22B30338: 0x000022EB
	"\"\xb4\x038\x00\x00\"\xec" + // 0x22B40338: 0x000022EC
	"\"\xb5\x038\x00\x00\"\xed" + // 0x22B50338: 0x000022ED
	"0K0\x99\x00\x000L" + // 0x304B3099: 0x0000304C
	"0M0\x99\x00\x000N" + // 0x304D3099: 0x0000304E
	"0O0\x99\x00\x000P" + // 0x304F3099: 0x00003050
	"0Q0\x99\x00\x000R" + // 0x30513099: 0x00003052
	"0S0\x99\x00\x000T" + // 0x30533099: 0x00003054
	"0U0\x99\x00\x000V" + // 0x30553099: 0x00003056
	"0W0\x99\x00\x000X" + // 0x30573099: 0x00003058
	"0Y0\x99\x00\x000Z" + // 0x30593099: 0x0000305A
	"0[0\x99\x00\x000\\" + // 0x305B3099: 0x0000305C
	"0]0\x99\x00\x000^" + // 0x305D3099: 0x0000305E
	"0_0\x99\x00\x000`" + // 0x305F3099: 0x00003060
	"0a0\x99\x00\x000b" + // 0x30613099: 0x00003062
	"0d0\x99\x00\x000e" + // 0x30643099: 0x00003065
	"0f0\x99\x00\x000g" + // 0x30663099: 0x00003067
	"0h0\x99\x00\x000i" + // 0x30683099: 0x00003069
	"0o0\x99\x00\x000p" + // 0x306F3099: 0x00003070
	"0o0\x9a\x00\x000q" + // 0x306F309A: 0x00003071
	"0r0\x99\x00\x000s" + // 0x30723099: 0x00003073
	"0r0\x9a\x00\x000t" + // 0x3072309A: 0x00003074
	"0u0\x99\x00\x000v" + // 0x30753099: 0x00003076
	"0u0\x9a\x00\x000w" + // 0x3075309A: 0x00003077
	"0x0\x99\x00\x000y" + // 0x30783099: 0x00003079
	"0x0\x9a\x00\x000z" + // 0x3078309A: 0x0000307A
	"0{0\x99\x00\x000|" + // 0x307B3099: 0x0000307C
	"0{0\x9a\x00\x000}" + // 0x307B309A: 0x0000307D
	"0F0\x99\x00\x000\x94" + // 0x30463099: 0x00003094
	"0\x9d0\x99\x00\x000\x9e" + // 0x309D3099: 0x0000309E
	"0\xab0\x99\x00\x000\xac" + // 0x30AB3099: 0x000030AC
	"0\xad0\x99\x00\x000\xae" + // 0x30AD3099: 0x000030AE
	"0\xaf0\x99\x00\x000\xb0" + // 0x30AF3099: 0x000030B0
	"0\xb10\x99\x00\x000\xb2" + // 0x30B13099: 0x000030B2
	"0\xb30\x99\x00\x000\xb4" + // 0x30B33099: 0x000030B4
	"0\xb50\x99\x00\x000\xb6" + // 0x30B53099: 0x000030B6
	"0\xb70\x99\x00\x000\xb8" + // 0x30B73099: 0x000030B8
	"0\xb90\x99\x00\x000\xba" + // 0x30B93099: 0x000030BA
	"0\xbb0\x99\x00\x000\xbc" + // 0x30BB3099: 0x000030BC
	"0\xbd0\x99\x00\x000\xbe" + // 0x30BD3099: 0x000030BE
	"0\xbf0\x99\x00\x000\xc0" + // 0x30BF3099: 0x000030C0
	"0\xc10\x99\x00\x000\xc2" + // 0x30C13099: 0x000030C2
	"0\xc40\x99\x00\x000\xc5" + // 0x30C43099: 0x000030C5
	"0\xc60\x99\x00\x000\xc7" + // 0x30C63099: 0x000030C7
	"0\xc80\x99\x00\x000\xc9" + // 0x30C83099: 0x000030C9
	"0\xcf0\x99\x00\x000\xd0" + // 0x30CF3099: 0x000030D0
	"0\xcf0\x9a\x00\x000\xd1" + // 0x30CF309A: 0x000030D1
	"0\xd20\x99\x00\x000\xd3" + // 0x30D23099: 0x000030D3
	"0\xd20\x9a\x00\x000\xd4" + // 0x30D2309A: 0x000030D4
	"0\xd50\x99\x00\x000\xd6" + // 0x30D53099: 0x000030D6
	"0\xd50\x9a\x00\x000\xd7" + // 0x30D5309A: 0x000030D7
	"0\xd80\x99\x00\x000\xd9" + // 0x30D83099: 0x000030D9
	"0\xd80\x9a\x00\x000\xda" + // 0x30D8309A: 0x000030DA
	"0\xdb0\x99\x00\x000\xdc" + // 0x30DB3099: 0x000030DC
	"0\xdb0\x9a\x00\x000\xdd" + // 0x30DB309A: 0x000030DD
	"0\xa60\x99\x00\x000\xf4" + // 0x30A63099: 0x000030F4
	"0\xef0\x99\x00\x000\xf7" + // 0x30EF3099: 0x000030F7
	"0\xf00\x99\x00\x000\xf8" + // 0x30F03099: 0x000030F8
	"0\xf10\x99\x00\x000\xf9" + // 0x30F13099: 0x000030F9
	"0\xf20\x99\x00\x000\xfa" + // 0x30F23099: 0x000030FA
	"0\xfd0\x99\x00\x000\xfe" + // 0x30FD3099: 0x000030FE
	"\x10\x99\x10\xba\x00\x01\x10\x9a" + // 0x109910BA: 0x0001109A
	"\x10\x9b\x10\xba\x00\x01\x10\x9c" + // 0x109B10BA: 0x0001109C
	"\x10\xa5\x10\xba\x00\x01\x10\xab" + // 0x10A510BA: 0x000110AB
	"\x111\x11'\x00\x01\x11." + // 0x11311127: 0x0001112E
	"\x112\x11'\x00\x01\x11/" + // 0x11321127: 0x0001112F
	"\x13G\x13>\x00\x01\x13K" + // 0x1347133E: 0x0001134B
	"\x13G\x13W\x00\x01\x13L" + // 0x13471357: 0x0001134C
	"\x14\xb9\x14\xba\x00\x01\x14\xbb" + // 0x14B914BA: 0x000114BB
	"\x14\xb9\x14\xb0\x00\x01\x14\xbc" + // 0x14B914B0: 0x000114BC
	"\x14\xb9\x14\xbd\x00\x01\x14\xbe" + // 0x14B914BD: 0x000114BE
	"\x15\xb8\x15\xaf\x00\x01\x15\xba" + // 0x15B815AF: 0x000115BA
	"\x15\xb9\x15\xaf\x00\x01\x15\xbb" + // 0x15B915AF: 0x000115BB
	""
	// Total size of tables: 53KB (54226 bytes)
