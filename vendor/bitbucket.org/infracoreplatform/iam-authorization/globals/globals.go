package globals

const (
	AUTHORIZATION_RESOURCES_FILEPATH               = "iam_resources.yml"
	AUTHORIZATION_SERVICE_RESOURCES_FILEPATH       = "serviceConfig/"
	YML_FILE_EXTENSION                             = ".yml"
	ATTRIBUTE_CONTEXT_KEY                          = "attribute_filter"
	CONTEXT_KEY_ABAC_ATTRIBUTES                    = "abac_attributes"
	CONTEXT_KEY_RESOLVED_ABAC_ATTRIBUTES           = "resolved_abac_attributes"
	SELF_USER_ID_KEY                               = "selfUserId"
	INTERNAL_SERVER_ERROR                          = "internal server error"
	REQUEST_UNAUTHORIZED_MESSAGE                   = "request unauthorized"
	AUTHORIZATION_SERVICE_RESOURCES_LOCAL_FILEPATH = "vendor/bitbucket.org/infracoreplatform/iam-authorization/iamCompliance/serviceConfig/"
	ALLOW_EFFECT                                   = "allow"
	DENY_EFFECT                                    = "deny"
	ATTRIBUTE_ENFORCER_SUFFIX                      = "_enforce"
	RESTORATION_API_ENDPOINT                       = "/restore-internal/v2"
	RESOURCE_REGISTRATION_API_ENDPOINT             = "/resource/init/v2"
	INTERNAL_API_CALL_HOST                         = "http://user-service.go-apps.svc.cluster.local"
	FEATURE_FLAG_API_ENDPOINT                      = "^/feature_flag/v1$"
	// INTERNAL_API_CALL_HOST = "http://127.0.0.1:9001" // for local testing
)

// tables
const (
	TABLE_AUTH_RAMPING             = "users.auth_ramping"
	TABLE_RESOURCE                 = "users.resources"
	TABLE_RESOURCE_ATTRIBUTE       = "users.resource_attributes"
	TABLE_RESOURCE_POLICY          = "users.resource_policy"
	TABLE_POLICY_ENTITY_MAPPING    = "users.policy_entity_mapping"
	TABLE_ATTRIBUTES               = "users.attributes"
	TABLE_POLICY_ATTRIBUTE_MAPPING = "users.policy_attribute_mapping"
	TABLE_USER_ATTRIBUTES          = "users.user_attributes"
	TABLE_USER_ROLE_MAPPING        = "users.user_role_mapping"
)

// flags
const (
	FLAG_TYPE_API                      = "api"
	FLAG_TYPE_GLOBAL                   = "global"
	FLAG_MANDATORY_POLICY_REGISTRATION = "mandatory_policy_registration"
	FLAG_AUTHORIZATION_SWITCH          = "switch_authz_middleware"
	FLAG_RBAC_ABAC_VERSION_SWITCH      = "rbac_abac_version_switch"
	FLAG_RBAC_V2_SKIP                  = "rbac_v2_skip"
	FLAG_MANDATORY_POLICY_ACTIVATION   = "mandatory_policy_activation"
	FLAG_RBAC_V1_SKIP                  = "rbac_v1_skip"
	API_FEATURE_FLAG                   = "api_feature_flag"
)

// New Relic Keys
const (
	NEWRELIC_KEY_SOURCE_SERVICE            = "module"
	NEWRELIC_KEY_ENV                       = "env"
	NEWRELIC_KEY_AUTH_COVERAGE             = "auth_coverage"
	NEWRELIC_KEY_CONFIG_COVERAGE           = "config_coverage"
	NEWRELIC_KEY_RBAC_COVERAGE             = "rbac_coverage"
	NEWRELIC_KEY_ABAC_COVERAGE             = "abac_coverage"
	NEWRELIC_KEY_ENDPOINT_PATTERN          = "endpoint_pattern"
	NEWRELIC_KEY_ENDPOINT_PATH             = "endpoint_path"
	NEWRELIC_KEY_UNRESOLVED_ENDPOINT       = "unresolved_endpoint"
	NEWRELIC_KEY_REQUEST_METHOD            = "request_method"
	NEWRELIC_KEY_EVENT_NAME                = "IamAuthzMetricsV1"
	NEWRELIC_KEY_AUTHZ_EVENT_V2            = "IamAuthzMetricsV2"
	NEWRELIC_ERROR_MESSAGE                 = "internal server error"
	NEWRELIC_KEY_STATUS_CODE               = "status_code"
	NEWRELIC_KEY_CUSTOM_ERROR_CODE         = "custom_error_code"
	NEWRELIC_KEY_USER_ID                   = "user_id"
	NEWRELIC_KEY_AUTHZ_MIDDLEWARE_DURATION = "authz_middleware_duration_milliseconds"
	NEWRELIC_KEY_RBAC_DURATION             = "rbac_duration_milliseconds"
	NEWRELIC_KEY_ABAC_DURATION             = "abac_duration_milliseconds"
	NEWRELIC_KEY_RESOLVER_DURATION         = "resolver_duration_milliseconds"
	NEWRELIC_KEY_IS_POLICY_ENABLED         = "is_policy_enabled"
)

// Redis Keys
const (
	REDIS_KEY_RESOURCE_POLICY   = "resource_policy"
	REDIS_KEY_USER_ATTRIBUTES   = "user_attributes"
	REDIS_KEY_API_RESOURCE_TYPE = "api"
	REDIS_KEY_FEATURE_FLAG      = "feature_flag"
)

// Restoration Numbers
const (
	POLICY_RESTORATION_MAX_RETRIES       = 1
	POLICY_RESTORATION_INTERVAL          = 0
	FEATURE_FLAG_RESTORATION_MAX_RETRIES = 1
	FEATURE_FLAG_RESTORATION_INTERVAL    = 0
)

// Custom Error Codes
const (
	CODE_UNAUTHENTICATED       = 3005
	CODE_RBAC_UNAUTHORIZED     = 3002
	CODE_ABAC_UNAUTHORIZED     = 3003
	CODE_INTERNAL_SERVER_ERROR = 3004
)

// Error Messages
const (
	ERR_BULK_POLICY_REGISTRATION = "error while registering resource policies"
)

var MethodActionMap = map[string]string{
	"GET":    "read",
	"POST":   "create",
	"PUT":    "edit",
	"PATCH":  "edit",
	"DELETE": "delete",
}

var AllowDenyBoolMap = map[string]bool{
	"allow": true,
	"deny":  false,
}

var AllowDenyStringMap = map[bool]string{
	true:  "allow",
	false: "deny",
}
