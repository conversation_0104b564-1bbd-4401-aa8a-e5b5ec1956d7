# customer service
iam_authz_config:
  # Customer List v4
  - request_path: ^/v4$
    request_method: GET
    resources:
    - resource_identifier: customer.customer
      type: table
      policy: [read]
      
  # Create Customer V4
  - request_path: ^/v4$
    request_method: POST
    resources:
    - resource_identifier: customer.customer
      type: table
      policy: [create]

  # Customer Details V3
  - request_path: ^/CUST\d+\/v3$
    request_method: GET
    resources:
    - resource_identifier: customer.customer
      type: table
      policy: [read]

  # Customer Details V4
  - request_path: ^/CUST\d+\/v4$
    request_method: GET
    resources:
    - resource_identifier: customer.customer
      type: table
      policy: [read]

  # Update Customer V4
  - request_path: ^/CUST\d+\/v4$
    request_method: PUT
    resources:
    - resource_identifier: customer.customer
      type: table
      policy: [edit]

  # Site List V4
  - request_path: ^/sites/v4$
    request_method: GET
    resources:
    - resource_identifier: customer.customer_site
      type: table
      policy: [read]

  # Site List V3
  - request_path: ^/sites/v3$
    request_method: GET
    resources:
    - resource_identifier: customer.customer_site
      type: table
      policy: [read]

  # Site Details V3
  - request_path: ^/sites/SITE\d+\/v3$
    request_method: GET
    resources:
    - resource_identifier: customer.customer_site
      type: table
      policy: [read]
      
  # Site Details V4
  - request_path: ^/sites/SITE\d+\/v4$
    request_method: GET
    resources:
    - resource_identifier: customer.customer_site
      type: table
      policy: [read]
      
  # Create Site V4
  - request_path: ^/sites/v4$
    request_method: POST
    resources:
    - resource_identifier: customer.customer_site
      type: table
      policy: [create]
      
  # Branch List V3
  - request_path: ^/branches/v3$
    request_method: GET
    resources:
    - resource_identifier: customer.customer_branch
      type: table
      policy: [read]
      
  # Branch List V1
  - request_path: ^/branches/v1$
    request_method: GET
    resources:
    - resource_identifier: customer.customer_branch
      type: table
      policy: [read]
      
  # Branch Details V3
  - request_path: ^/branches/COFF\d+\/v3$
    request_method: GET
    resources:
    - resource_identifier: customer.customer_branch
      type: table
      policy: [read]
      
  # Branch Details V4
  - request_path: ^/branches/COFF\d+\/v4$
    request_method: GET
    resources:
    - resource_identifier: customer.customer_branch
      type: table
      policy: [read]
      
  # Create Branch V4
  - request_path: ^/branches/v4$
    request_method: POST
    resources:
    - resource_identifier: customer.customer_branch
      type: table
      policy: [create]
      
  # Update Branch V4
  - request_path: ^/branches/COFF\d+\/v4$
    request_method: PUT
    resources:
    - resource_identifier: customer.customer_branch
      type: table
      policy: [edit]
      
  # Customer Rep List V4
  - request_path: ^/representatives/v4$
    request_method: GET
    resources:
    - resource_identifier: customer.customer_representative
      type: table
      policy: [read]
      
  # Custoemr Rep List V3
  - request_path: ^/representatives/v3$
    request_method: GET
    resources:
    - resource_identifier: customer.customer_representative
      type: table
      policy: [read]
      
  # Customer Rep List V1
  - request_path: ^/representatives/v1$
    request_method: GET
    resources:
    - resource_identifier: customer.customer_representative
      type: table
      policy: [read]
      
  # Create Customer Rep V4
  - request_path: ^/representatives/v4$
    request_method: POST
    resources:
    - resource_identifier: customer.customer_representative
      type: table
      policy: [create]
      
  # Update Customer Rep V4
  - request_path: ^/representatives/CREP\d+\/v4$
    request_method: PUT
    resources:
    - resource_identifier: customer.customer_representative
      type: table
      policy: [edit]
      
  # Company List V1
  - request_path: ^/company/v1$
    request_method: GET
    resources:
    - resource_identifier: customer.company
      type: table
      policy: [read]
      
  # Create Company V1
  - request_path: ^/company/v1$
    request_method: POST
    resources:
    - resource_identifier: customer.company
      type: table
      policy: [create]
      
  # Company Details V1
  - request_path: ^/company/COMP\d+\/v1$
    request_method: GET
    resources:
    - resource_identifier: customer.company
      type: table
      policy: [read]
      
  # Upload Photos V1
  - request_path: ^/company/media/v1$
    request_method: POST
    resources:
    - resource_identifier: customer.company
      type: table
      policy: [read]
      
  # Get GST List V3
  - request_path: ^/gst/v3$
    request_method: GET
    resources:
    - resource_identifier: customer.customer_kyc_mapping
      type: table
      policy: [read]
      
  # Customer KYC Validation V3
  - request_path: ^/kyc/customer/v3$
    request_method: GET
    resources:
    - resource_identifier: customer.customer_kyc_mapping
      type: table
      policy: [read]
      
  # Validate GST details V4
  - request_path: ^/validate/gst/v4$
    request_method: POST
    resources:
    - resource_identifier: customer.customer_kyc_mapping
      type: table
      policy: [create]
      
  # Validate KYC details V4
  - request_path: ^/validate/kyc/v4$
    request_method: POST
    resources:
    - resource_identifier: customer.customer_kyc_mapping
      type: table
      policy: [create]
      