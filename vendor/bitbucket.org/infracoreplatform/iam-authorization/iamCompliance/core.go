package iamCompliance

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"regexp"

	"bitbucket.org/infracoreplatform/iam-authorization/globals"
	serviceConfig "bitbucket.org/infracoreplatform/iam-authorization/iamCompliance/serviceConfig"
	"bitbucket.org/infracoreplatform/iam-authorization/model"
	entitiesV2 "bitbucket.org/infracoreplatform/iam-authorization/model/payload/v2"
	"bitbucket.org/infracoreplatform/iam-authorization/repo"
	v2 "bitbucket.org/infracoreplatform/iam-authorization/repo/v2"
	"bitbucket.org/infracoreplatform/iam-authorization/utils"
	"bitbucket.org/infracoreplatform/server-utils/authorization/authzResolvers"
	"bitbucket.org/infracoreplatform/server-utils/cache"
	"bitbucket.org/infracoreplatform/server-utils/common"
	"bitbucket.org/infracoreplatform/server-utils/metrics"
	base_metrics "bitbucket.org/infracoreplatform/server-utils/metrics/base"

	"github.com/gorilla/mux"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

type Authz struct {
	Db                   *gorm.DB
	Repo                 repo.IResourceRepo
	RedisRepo            v2.IAuthzRedisCache
	CompositeObserver    metrics.CompositeObserver
	Cache                cache.RedisCacheInterface
	ExternalApiClient    ExternalApiClientInterface
	WhitelistedEndpoints []string
	AllEndpointRoutes    []*mux.Route
	Resolver             authzResolvers.IResolverBase
}

/*
The following function - Authorize is the main function of the
authorization service. This will include all the flows required
on the user for the authorization flows and other functions and
information in this service will supplement this function.
Overall, the function logic has three steps in the following order
	1. Fetch resource information mapped to endpoint
	2. Calculate intent token
	3. Fetch user permission and attributes from db
	4. Validate user permission against intent token [Coarse grained authz]
	5. Attach attributes to the request for each resource
*/

func (a Authz) Authorize(logglyLogger *logrus.Entry, config map[string]interface{}) func(http.Handler) http.Handler {
	return func(h http.Handler) http.Handler {
		return http.HandlerFunc(func(rw http.ResponseWriter, rq *http.Request) {
			ctx := rq.Context()
			authzStartTime := time.Now()
			logger := common.CreateRequestLogger(rq, logglyLogger)
			for _, endpoint := range a.WhitelistedEndpoints {
				if regexp.MustCompile(endpoint).MatchString(rq.URL.Path) {
					h.ServeHTTP(rw, rq)
					return
				}
				if regexp.MustCompile(globals.FEATURE_FLAG_API_ENDPOINT).MatchString(rq.URL.Path) {
					h.ServeHTTP(rw, rq)
					return
				}
			}
			// temporary check until we shift to v2
			if a.Cache == nil {
				logger.Error("[IAM-Authz][Authorize] cache not found in authz")
				common.HTTPFailWithErrorCodesV1(http.StatusText(http.StatusInternalServerError), globals.CODE_INTERNAL_SERVER_ERROR, http.StatusInternalServerError, rw, logger)
				return
			}
			userId, ok := rq.Context().Value("request_user_id").(int)
			if !ok {
				logger.Warn("[IAM-Authz][Authorize] user id not found in context, serving request for - " + rq.Method + ":" + rq.URL.Path)
				userId = 0
			}
			module, ok := config["module"].(string)
			if !ok {
				error_message := fmt.Sprintf("[IAM-Authz][Authorize] userId - %d, module name not found in authz", userId)
				logger.Error(error_message)
				common.HTTPFailWithErrorCodesV1(http.StatusText(http.StatusInternalServerError), globals.CODE_INTERNAL_SERVER_ERROR, http.StatusInternalServerError, rw, logger)
				return
			}
			env, ok := config["envName"].(string)
			if !ok {
				error_message := fmt.Sprintf("[IAM-Authz][Authorize] userId - %d, env name not found in authz", userId)
				logger.Error(error_message)
				common.HTTPFailWithErrorCodesV1(http.StatusText(http.StatusInternalServerError), globals.CODE_INTERNAL_SERVER_ERROR, http.StatusInternalServerError, rw, logger)
				return
			}
			redisBulkOutput, err := a.RedisRepo.GetRawPipelineData(a.Cache, env, module, userId, logger)
			if err != nil {
				logger.WithField("err", err).Error("[IAM-Authz][Authorize] error in getting raw bulk data from redis pipeline")
				common.HTTPFailWithErrorCodesV1(http.StatusText(http.StatusInternalServerError), globals.CODE_INTERNAL_SERVER_ERROR, http.StatusInternalServerError, rw, logger)
				return
			}
			switchtoV2, rbacV2Skip, mandatoryRegistration, mandatoryActivation, userAttributes, userResolverAttributesMap, policyRules, userRoles, err := a.GetRedisDataFromRawData(0, env, module, userId, redisBulkOutput, logger)
			if err != nil {
				logger.WithField("err", err).Error("[IAM-Authz][Authorize] error in getting flags and policies redis data")
				common.HTTPFailWithErrorCodesV1(http.StatusText(http.StatusInternalServerError), globals.CODE_INTERNAL_SERVER_ERROR, http.StatusInternalServerError, rw, logger)
				return
			}

			customEventObserver := a.CompositeObserver.GetCustomEventObserver()
			if !switchtoV2 {
				isAuthorized, attributeMap, err := a.AuthorizeRbacV1(rq, logger, module, userId)
				if err != nil {
					error_message := fmt.Sprintf("[IAM-Authz][Authorize] userId - %d, error at core layer from [AuthorizeRbacV1]", userId)
					logger.WithField("err", err).Error(error_message)
					common.HTTPFailWithErrorCodesV1(http.StatusText(http.StatusInternalServerError), globals.CODE_INTERNAL_SERVER_ERROR, http.StatusInternalServerError, rw, logger)
					return
				}
				if !isAuthorized {
					error_message := fmt.Sprintf("[IAM-Authz][Authorize] userId - %d, got unauthorized at [AuthorizeRbacV1] on request %s : %s", userId, rq.Method, rq.URL.Path)
					logger.Error(error_message)
					common.HTTPFailWithErrorCodesV1(http.StatusText(http.StatusForbidden), globals.CODE_RBAC_UNAUTHORIZED, http.StatusForbidden, rw, logger)
					return
				}
				ctx := context.WithValue(rq.Context(), globals.ATTRIBUTE_CONTEXT_KEY, attributeMap)
				rq = rq.WithContext(ctx)
			} else {
				rbacStartTime := time.Now()
				var eventValues map[string]interface{}
				customEvent := base_metrics.CustomEvent{}
				customEvent.Name = globals.NEWRELIC_KEY_AUTHZ_EVENT_V2
				eventValues = utils.EnrichEventDetailsV2(rq, module, env)
				if userId == 0 {
					logger.Error("[IAM-Authz][Authorize] user id not found in context for performing rbac and abac")
					common.HTTPFailWithErrorCodesV1(http.StatusText(http.StatusUnauthorized), globals.CODE_UNAUTHENTICATED, http.StatusUnauthorized, rw, logger)
					eventValues[globals.NEWRELIC_KEY_CUSTOM_ERROR_CODE] = globals.CODE_UNAUTHENTICATED
					eventValues[globals.NEWRELIC_KEY_STATUS_CODE] = http.StatusUnauthorized
					eventValues[globals.NEWRELIC_KEY_USER_ID] = 0
					customEvent.Values = eventValues
					customEventObserver.RecordCustomMetrics(customEvent)
					return
				}
				eventValues[globals.NEWRELIC_KEY_USER_ID] = userId
				isAuthorized, mandatoryAttribtues, nonMandatoryAttribtutes, resolverAttribtues, err := a.AuthorizeRbacV2(rq, logger, rbacV2Skip, mandatoryRegistration, mandatoryActivation, policyRules, module, env, userId, userRoles, &eventValues)
				if err != nil {
					error_message := fmt.Sprintf("[IAM-Authz][Authorize] userId - %d, error at core layer from [AuthorizeRbacV2]", userId)
					logger.WithField("err", err).Error(error_message)
					common.HTTPFailWithErrorCodesV1(http.StatusText(http.StatusInternalServerError), globals.CODE_INTERNAL_SERVER_ERROR, http.StatusInternalServerError, rw, logger)
					eventValues[globals.NEWRELIC_KEY_STATUS_CODE] = http.StatusInternalServerError
					eventValues[globals.NEWRELIC_KEY_CUSTOM_ERROR_CODE] = globals.CODE_INTERNAL_SERVER_ERROR
					customEvent.Values = eventValues
					customEventObserver.RecordCustomMetrics(customEvent)
					return
				}
				if !isAuthorized {
					error_message := fmt.Sprintf("[IAM-Authz][Authorize] userId - %d, got unauthorized at [AuthorizeRbacV2] on request %s : %s", userId, rq.Method, rq.URL.Path)
					logger.Error(error_message)
					common.HTTPFailWithErrorCodesV1(http.StatusText(http.StatusForbidden), globals.CODE_RBAC_UNAUTHORIZED, http.StatusForbidden, rw, logger)
					eventValues[globals.NEWRELIC_KEY_STATUS_CODE] = http.StatusForbidden
					eventValues[globals.NEWRELIC_KEY_CUSTOM_ERROR_CODE] = globals.CODE_RBAC_UNAUTHORIZED
					customEvent.Values = eventValues
					customEventObserver.RecordCustomMetrics(customEvent)
					return
				}

				resolverStartTime := time.Now()
				resolverOutputMap := make(map[string]map[string]interface{})
				for _, resolverAttribute := range resolverAttribtues {
					if userResolverAttributesMap[resolverAttribute] {
						if a.Resolver.HasResolver(resolverAttribute) {
							resolverOutput := a.Resolver.ResolveAttribute(resolverAttribute, userId, ctx)
							if resolverOutput.Error != nil {
								error_message := fmt.Sprintf("[IAM-Authz][Authorize] userId - %d, error at core layer from resolver - %s", userId, resolverAttribute)
								logger.WithField("err", resolverOutput.Error).Error(error_message)
								common.HTTPFailWithErrorCodesV1(http.StatusText(http.StatusInternalServerError), globals.CODE_INTERNAL_SERVER_ERROR, http.StatusInternalServerError, rw, logger)
								eventValues[globals.NEWRELIC_KEY_STATUS_CODE] = http.StatusInternalServerError
								eventValues[globals.NEWRELIC_KEY_CUSTOM_ERROR_CODE] = globals.CODE_INTERNAL_SERVER_ERROR
								customEvent.Values = eventValues
								customEventObserver.RecordCustomMetrics(customEvent)
								return
							}
							if len(resolverOutput.Values) > 0 {
								resolverOutputMap[resolverAttribute] = resolverOutput.Values
							} else {
								warning_message := fmt.Sprintf("[IAM-Authz][Authorize] userId - %d, resolver - %s returned empty values", userId, resolverAttribute)
								logger.Warn(warning_message)
							}
						} else {
							logger.Warn("[IAM-Authz][Authorize] " + resolverAttribute + " resolver not found")
						}
					} else {
						logger.Warn("[IAM-Authz][Authorize] " + resolverAttribute + " resolver not applied")
					}
				}
				if len(resolverOutputMap) > 0 {
					ctx = context.WithValue(ctx, globals.CONTEXT_KEY_RESOLVED_ABAC_ATTRIBUTES, resolverOutputMap)
					rq = rq.WithContext(ctx)
				}
				abacStartTime := time.Now()
				if len(mandatoryAttribtues) > 0 || len(nonMandatoryAttribtutes) > 0 {
					eventValues[globals.NEWRELIC_KEY_ABAC_COVERAGE] = true
					if len(userAttributes) > 0 {
						linkedAttributes := append(mandatoryAttribtues, nonMandatoryAttribtutes...)
						var linkedUserAttributes []entitiesV2.UserAttributes
						for _, linkedAttribute := range linkedAttributes {
							for _, userAttribute := range userAttributes {
								if userAttribute.AttributeKey == linkedAttribute {
									linkedUserAttributes = append(linkedUserAttributes, userAttribute)
								}
							}
						}
						// Check for selfUserId
						for i, userAttribute := range linkedUserAttributes {
							for j, attributeValue := range userAttribute.AttributeValue {
								if attributeValue == globals.SELF_USER_ID_KEY {
									linkedUserAttributes[i].AttributeValue[j] = strconv.Itoa(userId)
								}
							}
						}
						ctx = context.WithValue(ctx, globals.CONTEXT_KEY_ABAC_ATTRIBUTES, linkedUserAttributes)
						rq = rq.WithContext(ctx)
					}
					isAuthorized, err := a.AuthorizeAbacV2(rq, logger, module, env, userId, mandatoryAttribtues, nonMandatoryAttribtutes, userAttributes, &eventValues)
					if err != nil {
						error_message := fmt.Sprintf("[IAM-Authz][Authorize] userId - %d, error at core layer from [AuthorizeAbacV2]", userId)
						logger.WithField("err", err).Error(error_message)
						common.HTTPFailWithErrorCodesV1(http.StatusText(http.StatusInternalServerError), globals.CODE_INTERNAL_SERVER_ERROR, http.StatusInternalServerError, rw, logger)
						eventValues[globals.NEWRELIC_KEY_STATUS_CODE] = http.StatusInternalServerError
						eventValues[globals.NEWRELIC_KEY_CUSTOM_ERROR_CODE] = globals.CODE_INTERNAL_SERVER_ERROR
						customEvent.Values = eventValues
						customEventObserver.RecordCustomMetrics(customEvent)
						return
					}
					if !isAuthorized {
						error_message := fmt.Sprintf("[IAM-Authz][Authorize] userId - %d, got unauthorized at [AuthorizeAbacV2] on request %s : %s", userId, rq.Method, rq.URL.Path)
						logger.Error(error_message)
						common.HTTPFailWithErrorCodesV1(http.StatusText(http.StatusForbidden), globals.CODE_ABAC_UNAUTHORIZED, http.StatusForbidden, rw, logger)
						eventValues[globals.NEWRELIC_KEY_STATUS_CODE] = http.StatusForbidden
						eventValues[globals.NEWRELIC_KEY_CUSTOM_ERROR_CODE] = globals.CODE_ABAC_UNAUTHORIZED
						customEvent.Values = eventValues
						customEventObserver.RecordCustomMetrics(customEvent)
						return
					}
				}
				eventValues[globals.NEWRELIC_KEY_STATUS_CODE] = http.StatusAccepted
				authzMiddlewareDuration := time.Since(authzStartTime).Milliseconds()
				rbacDuration := resolverStartTime.Sub(rbacStartTime).Milliseconds()
				resolverDuration := abacStartTime.Sub(resolverStartTime).Milliseconds()
				abacDuration := time.Since(abacStartTime).Milliseconds()
				eventValues[globals.NEWRELIC_KEY_AUTHZ_MIDDLEWARE_DURATION] = authzMiddlewareDuration
				eventValues[globals.NEWRELIC_KEY_RBAC_DURATION] = rbacDuration
				eventValues[globals.NEWRELIC_KEY_ABAC_DURATION] = abacDuration
				eventValues[globals.NEWRELIC_KEY_RESOLVER_DURATION] = resolverDuration
				customEvent.Values = eventValues
				customEventObserver.RecordCustomMetrics(customEvent)
			}
			h.ServeHTTP(rw, rq)
		})
	}
}

func (a Authz) AuthorizeRbacV1(rq *http.Request, logger *logrus.Entry, serviceName string, userId int) (bool, map[string][]model.Attribute, error) {
	// Getting all resources assigned to endpoint
	var attributeSet map[string][]model.Attribute
	skipRBAC, exceptionResources, err := a.GetGlobalAuthzFlag(logger)
	if err != nil {
		error_message := fmt.Sprintf("[IAM-Authz][AuthorizeRbacV1] userId - %d, error at core layer from [GetGlobalAuthzFlag]", userId)
		logger.WithField("err", err).Error(error_message)
		return false, attributeSet, err
	}
	if skipRBAC {
		return true, attributeSet, nil
	}
	serviceConfig.Init()
	serviceResourceData, err := a.GetServiceResources(serviceName, logger)
	if err != nil {
		error_message := fmt.Sprintf("[IAM-Authz][AuthorizeRbacV1] userId - %d, error at core layer from [GetServiceResources]", userId)
		logger.WithField("err", err).Error(error_message)
		return false, attributeSet, err
	}

	endpointConfig := a.CheckEndpointConfig(rq, serviceResourceData)
	if endpointConfig.EndpointValid {
		if userId == 0 {
			logger.Error("[IAM-Authz][AuthorizeRbacV1] user id not found in authz despite having valid rbac config")
			return false, attributeSet, nil
		}
		if len(exceptionResources) > 0 {
			for _, exception := range exceptionResources {
				endpointPath := endpointConfig.RequestMethod + ":" + endpointConfig.RequestPath
				if endpointPath == exception {
					warning_message := fmt.Sprintf("[IAM-Authz][AuthorizeRbacV1] userId - %d, endpoint under exception for rbac - %s", userId, endpointPath)
					logger.Warn(warning_message)
					return true, attributeSet, nil
				}
			}
		}

		//check that endpoint is registered for feature flag or not
		endpointResources := utils.FetchEndpointResources(endpointConfig)
		userPermission, err := a.FetchUserPermission(userId, endpointResources, logger)
		if err != nil {
			error_message := fmt.Sprintf("[IAM-Authz][AuthorizeRbacV1] userId - %d, error at core layer from [FetchUserPermission]", userId)
			logger.WithField("err", err).Error(error_message)
			return false, attributeSet, err
		}
		if userPermission.Token == nil || len(userPermission.Token) == 0 {
			error_message := fmt.Sprintf("[IAM-Authz][AuthorizeRbacV1] userId - %d, user do not have any resource permissions", userId)
			logger.Error(error_message)
			return false, attributeSet, nil
		}

		// Validating user permission against endpoint policy
		var res bool
		attributeSet, res = utils.ValidateUserPermissionForResource(endpointConfig.Resources, userPermission)
		if !res {
			error_message := fmt.Sprintf("[IAM-Authz][AuthorizeRbacV1] userId - %d, user do not have necessary resource permissions", userId)
			logger.Error(error_message)
			return false, attributeSet, nil
		}
	} else if userId == 0 {
		warning_message := fmt.Sprintf("[IAM-Authz][AuthorizeRbacV1] user id not found in context, serving request for whitelisted endpoint - %s:%s", rq.Method, rq.URL.Path)
		logger.Warn(warning_message)
		return true, attributeSet, nil
	}
	return true, attributeSet, nil
}

// For API, policy =  endpoint-regex-with-prefix:<CRUD>
func (a Authz) AuthorizeRbacV2(rq *http.Request, logger *logrus.Entry, skipRbacV2 bool, mandatoryRegistration bool, mandatoryActivation bool, allRedisPolicies entitiesV2.PolicyRuleMap, module string, env string, userId int, userRoles []string, eventAttributesMap *map[string]interface{}) (bool, []string, []string, []string, error) {
	if skipRbacV2 {
		return true, []string{}, []string{}, []string{}, nil
	}
	endpointAction, ok := globals.MethodActionMap[rq.Method]
	if !ok {
		(*eventAttributesMap)[globals.NEWRELIC_KEY_REQUEST_METHOD] = rq.Method
		error_message := fmt.Sprintf("[IAM-Authz][AuthorizeRbacV2] userId - %d, %s request method is invalid for rbac", userId, rq.Method)
		logger.Error(error_message)
		return false, []string{}, []string{}, []string{}, errors.New("invalid request method for authz")
	}

	policies, err := FetchPolicyModelsFromRedisPolicies(allRedisPolicies)
	if err != nil {
		error_message := fmt.Sprintf("[IAM-Authz][AuthorizeRbacV2] userId - %d, error at core layer from [FetchPolicyModelsFromRedisPolicies]", userId)
		logger.WithField("err", err).Error(error_message)
		return false, []string{}, []string{}, []string{}, err
	}
	var endpointPath string
	for _, route := range a.AllEndpointRoutes {
		if route.Match(rq, &mux.RouteMatch{}) {
			pathTemplate, err := route.GetPathTemplate()
			if err != nil {
				logger.WithField("err", err).Error("[IAM-Authz][AuthorizeRbacV2] error getting path template")
				return false, []string{}, []string{}, []string{}, err
			}
			endpointPath = pathTemplate
		}
	}

	var eligiblePolicies []entitiesV2.ResourceAccess
	for _, policy := range policies {
		if policy.Action == endpointAction && endpointPath == policy.Resource {
			eligiblePolicies = append(eligiblePolicies, policy)
		}
	}

	if len(eligiblePolicies) == 0 {
		if !mandatoryRegistration {
			return true, []string{}, []string{}, []string{}, nil
		}
		error_message := fmt.Sprintf("[IAM-Authz][AuthorizeRbacV2] userId - %d, no eligible policies discovered on endpoint", userId)
		logger.Error(error_message)
		return false, []string{}, []string{}, []string{}, errors.New("no policies found for endpoint, please register the endpoint policy")
	} else if len(eligiblePolicies) > 1 {
		error_message := fmt.Sprintf("[IAM-Authz][AuthorizeRbacV2] userId - %d, multiple eligible policies discovered on endpoint", userId)
		logger.Error(error_message)
		return false, []string{}, []string{}, []string{}, errors.New("multiple eligible policies discovered on endpoint")
	}
	endpointPolicy := eligiblePolicies[0]
	(*eventAttributesMap)[globals.NEWRELIC_KEY_ENDPOINT_PATTERN] = endpointPolicy.Resource
	(*eventAttributesMap)[globals.NEWRELIC_KEY_IS_POLICY_ENABLED] = endpointPolicy.IsEnabled
	if !endpointPolicy.IsEnabled {
		if !mandatoryActivation {
			return true, []string{}, []string{}, []string{}, nil
		}
		return false, []string{}, []string{}, []string{}, errors.New("endpoint policy is not activated")
	}
	// TODO: Send X-App-Name from headers to new relic
	(*eventAttributesMap)[globals.NEWRELIC_KEY_RBAC_COVERAGE] = true

	// RBAC Decision
	var rbacAuthorized bool
	hasAllowedRoles := utils.HasCommonString(userRoles, endpointPolicy.AllowedRoles)
	hasDeniedRole := utils.HasCommonString(userRoles, endpointPolicy.DeniedRoles)
	userIdAllowed := utils.Contains(int64(userId), endpointPolicy.AllowedUserIds)
	userIdDenied := utils.Contains(int64(userId), endpointPolicy.DeniedUserIds)
	rbacAuthorized = (hasAllowedRoles || userIdAllowed) && !(hasDeniedRole || userIdDenied)
	if !rbacAuthorized {
		error_message := fmt.Sprintf("[IAM-Authz][AuthorizeRbacV2] userId - %d, user do not have necessary resource permissions. RBAC decision: %t - hasAllowedRoles: %t, hasDeniedRole: %t, userIdAllowed: %t, userIdDenied: %t", userId, rbacAuthorized, hasAllowedRoles, hasDeniedRole, userIdAllowed, userIdDenied)
		logger.Error(error_message)
	}
	mandatoryAttribtues, nonMandatoryAttribtues, resolverAttribtues := a.FilterResolverAttributes(endpointPolicy)
	return rbacAuthorized, mandatoryAttribtues, nonMandatoryAttribtues, resolverAttribtues, nil
}

func (a Authz) FilterResolverAttributes(endpointPolicy entitiesV2.ResourceAccess) ([]string, []string, []string) {
	var mandatoryAttribtues []string
	var nonMandatoryAttribtues []string
	var resolverAttribtues []string
	for _, attribute := range endpointPolicy.MandatoryAttribtues {
		if strings.HasSuffix(attribute, globals.ATTRIBUTE_ENFORCER_SUFFIX) {
			resolverAttribtues = append(resolverAttribtues, attribute)
		} else {
			mandatoryAttribtues = append(mandatoryAttribtues, attribute)
		}
	}
	for _, attribute := range endpointPolicy.NonMandatoryAttribtues {
		if strings.HasSuffix(attribute, globals.ATTRIBUTE_ENFORCER_SUFFIX) {
			resolverAttribtues = append(resolverAttribtues, attribute)
		} else {
			nonMandatoryAttribtues = append(nonMandatoryAttribtues, attribute)
		}
	}
	return mandatoryAttribtues, nonMandatoryAttribtues, resolverAttribtues
}

func (a Authz) AuthorizeAbacV2(rq *http.Request, logger *logrus.Entry, serviceName string, env string, userId int, mandatoryAttribtues []string, nonMandatoryAttributes []string, userAttributes []entitiesV2.UserAttributes, eventAttributesMap *map[string]interface{}) (bool, error) {
	var tempCount int
	for _, attributeKey := range mandatoryAttribtues {
		for _, userAttribute := range userAttributes {
			if userAttribute.AttributeKey == attributeKey {
				tempCount += 1
			}
		}
	}
	if tempCount < len(mandatoryAttribtues) {
		error_message := fmt.Sprintf("[IAM-Authz][AuthorizeAbacV2] userId - %d, some of mandatory attributes are not assigned to user", userId)
		logger.Error(error_message)
		return false, nil
	}

	linkedAttributes := append(mandatoryAttribtues, nonMandatoryAttributes...)

	linkedAttributesOnRequest := MatchRequestKeys(rq, linkedAttributes)
	if len(linkedAttributesOnRequest) == 0 {
		return true, nil
	}

	requestAttributes := make(map[string][]string)
	for key, value := range linkedAttributesOnRequest {
		requestAttributes[key] = convertToStringArray(value)
	}

	abacValidation, err := ValidateAttributes(requestAttributes, userAttributes)
	if err != nil {
		error_message := fmt.Sprintf("[IAM-Authz][AuthorizeAbacV2] userId - %d, error at core layer from [ValidateAttributes]", userId)
		logger.Error(error_message)
		return false, err
	}
	if !abacValidation {
		error_message := fmt.Sprintf("[IAM-Authz][AuthorizeAbacV2] userId - %d, abac request validation failed against user attributes", userId)
		logger.Error(error_message)
		return false, nil
	}
	return true, nil
}
