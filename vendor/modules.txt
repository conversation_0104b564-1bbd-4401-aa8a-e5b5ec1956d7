# bitbucket.org/infracoreplatform/iam-authorization v0.0.0-20250523122656-d90a4d48b5ba
## explicit; go 1.18
bitbucket.org/infracoreplatform/iam-authorization/globals
bitbucket.org/infracoreplatform/iam-authorization/iamCompliance
bitbucket.org/infracoreplatform/iam-authorization/iamCompliance/serviceConfig
bitbucket.org/infracoreplatform/iam-authorization/model
bitbucket.org/infracoreplatform/iam-authorization/model/gorm/v1
bitbucket.org/infracoreplatform/iam-authorization/model/payload/v2
bitbucket.org/infracoreplatform/iam-authorization/repo
bitbucket.org/infracoreplatform/iam-authorization/repo/v2
bitbucket.org/infracoreplatform/iam-authorization/utils
# bitbucket.org/infracoreplatform/proto-files v0.0.0-20240123184124-a75f7a85443a
## explicit
bitbucket.org/infracoreplatform/proto-files/common
bitbucket.org/infracoreplatform/proto-files/services/common
# bitbucket.org/infracoreplatform/server-utils v0.0.0-20250611063719-89cf0413b261
## explicit; go 1.18
bitbucket.org/infracoreplatform/server-utils/auth
bitbucket.org/infracoreplatform/server-utils/authentication/middleware
bitbucket.org/infracoreplatform/server-utils/authentication/session
bitbucket.org/infracoreplatform/server-utils/authentication/token
bitbucket.org/infracoreplatform/server-utils/authorization/authzResolvers
bitbucket.org/infracoreplatform/server-utils/cache
bitbucket.org/infracoreplatform/server-utils/cache/redissync
bitbucket.org/infracoreplatform/server-utils/cache/v2
bitbucket.org/infracoreplatform/server-utils/common
bitbucket.org/infracoreplatform/server-utils/common/v3
bitbucket.org/infracoreplatform/server-utils/cors
bitbucket.org/infracoreplatform/server-utils/httpresponse
bitbucket.org/infracoreplatform/server-utils/jwt
bitbucket.org/infracoreplatform/server-utils/metrics
bitbucket.org/infracoreplatform/server-utils/metrics/base
bitbucket.org/infracoreplatform/server-utils/metrics/v1
bitbucket.org/infracoreplatform/server-utils/metrics/v3
bitbucket.org/infracoreplatform/server-utils/model
bitbucket.org/infracoreplatform/server-utils/parser
bitbucket.org/infracoreplatform/server-utils/parser/v3
# github.com/bytedance/sonic v1.11.6
## explicit; go 1.16
github.com/bytedance/sonic
github.com/bytedance/sonic/ast
github.com/bytedance/sonic/decoder
github.com/bytedance/sonic/encoder
github.com/bytedance/sonic/internal/caching
github.com/bytedance/sonic/internal/cpu
github.com/bytedance/sonic/internal/decoder
github.com/bytedance/sonic/internal/encoder
github.com/bytedance/sonic/internal/jit
github.com/bytedance/sonic/internal/native
github.com/bytedance/sonic/internal/native/avx
github.com/bytedance/sonic/internal/native/avx2
github.com/bytedance/sonic/internal/native/neon
github.com/bytedance/sonic/internal/native/sse
github.com/bytedance/sonic/internal/native/types
github.com/bytedance/sonic/internal/resolver
github.com/bytedance/sonic/internal/rt
github.com/bytedance/sonic/option
github.com/bytedance/sonic/unquote
github.com/bytedance/sonic/utf8
# github.com/bytedance/sonic/loader v0.1.1
## explicit; go 1.16
github.com/bytedance/sonic/loader
github.com/bytedance/sonic/loader/internal/abi
github.com/bytedance/sonic/loader/internal/rt
# github.com/cloudwego/base64x v0.1.4
## explicit; go 1.16
github.com/cloudwego/base64x
# github.com/cloudwego/iasm v0.2.0
## explicit; go 1.16
github.com/cloudwego/iasm/expr
github.com/cloudwego/iasm/x86_64
# github.com/dgrijalva/jwt-go v3.2.0+incompatible
## explicit
github.com/dgrijalva/jwt-go
# github.com/felixge/httpsnoop v1.0.3
## explicit; go 1.13
github.com/felixge/httpsnoop
# github.com/gabriel-vasile/mimetype v1.4.3
## explicit; go 1.20
github.com/gabriel-vasile/mimetype
github.com/gabriel-vasile/mimetype/internal/charset
github.com/gabriel-vasile/mimetype/internal/json
github.com/gabriel-vasile/mimetype/internal/magic
# github.com/garyburd/redigo v1.6.4
## explicit; go 1.17
github.com/garyburd/redigo/internal
github.com/garyburd/redigo/redis
# github.com/gemnasium/logrus-graylog-hook/v3 v3.2.0
## explicit; go 1.20
github.com/gemnasium/logrus-graylog-hook/v3
# github.com/gin-contrib/sse v0.1.0
## explicit; go 1.12
github.com/gin-contrib/sse
# github.com/gin-gonic/gin v1.10.1
## explicit; go 1.20
github.com/gin-gonic/gin
github.com/gin-gonic/gin/binding
github.com/gin-gonic/gin/internal/bytesconv
github.com/gin-gonic/gin/internal/json
github.com/gin-gonic/gin/render
# github.com/go-ozzo/ozzo-validation/v4 v4.3.0
## explicit; go 1.13
github.com/go-ozzo/ozzo-validation/v4
# github.com/go-playground/locales v0.14.1
## explicit; go 1.17
github.com/go-playground/locales
github.com/go-playground/locales/currency
# github.com/go-playground/universal-translator v0.18.1
## explicit; go 1.18
github.com/go-playground/universal-translator
# github.com/go-playground/validator/v10 v10.20.0
## explicit; go 1.18
github.com/go-playground/validator/v10
# github.com/go-redsync/redsync/v4 v4.11.0
## explicit; go 1.20
github.com/go-redsync/redsync/v4
github.com/go-redsync/redsync/v4/redis
github.com/go-redsync/redsync/v4/redis/redigo
# github.com/goccy/go-json v0.10.2
## explicit; go 1.12
github.com/goccy/go-json
github.com/goccy/go-json/internal/decoder
github.com/goccy/go-json/internal/encoder
github.com/goccy/go-json/internal/encoder/vm
github.com/goccy/go-json/internal/encoder/vm_color
github.com/goccy/go-json/internal/encoder/vm_color_indent
github.com/goccy/go-json/internal/encoder/vm_indent
github.com/goccy/go-json/internal/errors
github.com/goccy/go-json/internal/runtime
# github.com/golang-jwt/jwt/v5 v5.2.2
## explicit; go 1.18
github.com/golang-jwt/jwt/v5
# github.com/golang/protobuf v1.5.3
## explicit; go 1.9
github.com/golang/protobuf/jsonpb
github.com/golang/protobuf/proto
github.com/golang/protobuf/ptypes
github.com/golang/protobuf/ptypes/any
github.com/golang/protobuf/ptypes/duration
github.com/golang/protobuf/ptypes/timestamp
# github.com/gomodule/redigo v1.8.9
## explicit; go 1.16
github.com/gomodule/redigo/redis
# github.com/gorilla/handlers v1.5.2
## explicit; go 1.20
github.com/gorilla/handlers
# github.com/gorilla/mux v1.8.1
## explicit; go 1.20
github.com/gorilla/mux
# github.com/hashicorp/errwrap v1.1.0
## explicit
github.com/hashicorp/errwrap
# github.com/hashicorp/go-multierror v1.1.1
## explicit; go 1.13
github.com/hashicorp/go-multierror
# github.com/jinzhu/inflection v1.0.0
## explicit
github.com/jinzhu/inflection
# github.com/jinzhu/now v1.1.5
## explicit; go 1.12
github.com/jinzhu/now
# github.com/json-iterator/go v1.1.12
## explicit; go 1.12
github.com/json-iterator/go
# github.com/justinas/alice v1.2.0
## explicit; go 1.12
github.com/justinas/alice
# github.com/kelseyhightower/envconfig v1.4.0
## explicit
github.com/kelseyhightower/envconfig
# github.com/klauspost/cpuid/v2 v2.2.7
## explicit; go 1.15
github.com/klauspost/cpuid/v2
# github.com/leodido/go-urn v1.4.0
## explicit; go 1.18
github.com/leodido/go-urn
github.com/leodido/go-urn/scim/schema
# github.com/lib/pq v1.10.9
## explicit; go 1.13
github.com/lib/pq
github.com/lib/pq/oid
github.com/lib/pq/scram
# github.com/mattn/go-isatty v0.0.20
## explicit; go 1.15
github.com/mattn/go-isatty
# github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd
## explicit
github.com/modern-go/concurrent
# github.com/modern-go/reflect2 v1.0.2
## explicit; go 1.12
github.com/modern-go/reflect2
# github.com/newrelic/go-agent v2.16.3+incompatible
## explicit
github.com/newrelic/go-agent
github.com/newrelic/go-agent/internal
github.com/newrelic/go-agent/internal/cat
github.com/newrelic/go-agent/internal/jsonx
github.com/newrelic/go-agent/internal/logger
github.com/newrelic/go-agent/internal/sysinfo
github.com/newrelic/go-agent/internal/utilization
# github.com/newrelic/go-agent/v3 v3.29.0
## explicit; go 1.19
github.com/newrelic/go-agent/v3/internal
github.com/newrelic/go-agent/v3/internal/cat
github.com/newrelic/go-agent/v3/internal/com_newrelic_trace_v1
github.com/newrelic/go-agent/v3/internal/jsonx
github.com/newrelic/go-agent/v3/internal/logcontext
github.com/newrelic/go-agent/v3/internal/logger
github.com/newrelic/go-agent/v3/internal/sysinfo
github.com/newrelic/go-agent/v3/internal/utilization
github.com/newrelic/go-agent/v3/newrelic
# github.com/pelletier/go-toml/v2 v2.2.2
## explicit; go 1.16
github.com/pelletier/go-toml/v2
github.com/pelletier/go-toml/v2/internal/characters
github.com/pelletier/go-toml/v2/internal/danger
github.com/pelletier/go-toml/v2/internal/tracker
github.com/pelletier/go-toml/v2/unstable
# github.com/pkg/errors v0.9.1
## explicit
github.com/pkg/errors
# github.com/sebest/logrusly v0.0.0-20180315190218-3235eccb8edc
## explicit
github.com/sebest/logrusly
# github.com/segmentio/go-loggly v0.5.0
## explicit
github.com/segmentio/go-loggly
# github.com/sirupsen/logrus v1.9.3
## explicit; go 1.13
github.com/sirupsen/logrus
# github.com/twitchyliquid64/golang-asm v0.15.1
## explicit; go 1.13
github.com/twitchyliquid64/golang-asm/asm/arch
github.com/twitchyliquid64/golang-asm/bio
github.com/twitchyliquid64/golang-asm/dwarf
github.com/twitchyliquid64/golang-asm/goobj
github.com/twitchyliquid64/golang-asm/obj
github.com/twitchyliquid64/golang-asm/obj/arm
github.com/twitchyliquid64/golang-asm/obj/arm64
github.com/twitchyliquid64/golang-asm/obj/mips
github.com/twitchyliquid64/golang-asm/obj/ppc64
github.com/twitchyliquid64/golang-asm/obj/riscv
github.com/twitchyliquid64/golang-asm/obj/s390x
github.com/twitchyliquid64/golang-asm/obj/wasm
github.com/twitchyliquid64/golang-asm/obj/x86
github.com/twitchyliquid64/golang-asm/objabi
github.com/twitchyliquid64/golang-asm/src
github.com/twitchyliquid64/golang-asm/sys
github.com/twitchyliquid64/golang-asm/unsafeheader
# github.com/ugorji/go/codec v1.2.12
## explicit; go 1.11
github.com/ugorji/go/codec
# golang.org/x/arch v0.8.0
## explicit; go 1.18
golang.org/x/arch/x86/x86asm
# golang.org/x/crypto v0.23.0
## explicit; go 1.18
golang.org/x/crypto/sha3
# golang.org/x/net v0.25.0
## explicit; go 1.18
golang.org/x/net/html
golang.org/x/net/html/atom
golang.org/x/net/http/httpguts
golang.org/x/net/http2
golang.org/x/net/http2/h2c
golang.org/x/net/http2/hpack
golang.org/x/net/idna
golang.org/x/net/internal/timeseries
golang.org/x/net/trace
# golang.org/x/sys v0.21.0
## explicit; go 1.18
golang.org/x/sys/cpu
golang.org/x/sys/unix
golang.org/x/sys/windows
# golang.org/x/text v0.15.0
## explicit; go 1.18
golang.org/x/text/internal/language
golang.org/x/text/internal/language/compact
golang.org/x/text/internal/tag
golang.org/x/text/language
golang.org/x/text/secure/bidirule
golang.org/x/text/transform
golang.org/x/text/unicode/bidi
golang.org/x/text/unicode/norm
# google.golang.org/genproto/googleapis/rpc v0.0.0-20231106174013-bbf56f31fb17
## explicit; go 1.19
google.golang.org/genproto/googleapis/rpc/status
# google.golang.org/grpc v1.56.3
## explicit; go 1.17
google.golang.org/grpc
google.golang.org/grpc/attributes
google.golang.org/grpc/backoff
google.golang.org/grpc/balancer
google.golang.org/grpc/balancer/base
google.golang.org/grpc/balancer/grpclb/state
google.golang.org/grpc/balancer/roundrobin
google.golang.org/grpc/binarylog/grpc_binarylog_v1
google.golang.org/grpc/channelz
google.golang.org/grpc/codes
google.golang.org/grpc/connectivity
google.golang.org/grpc/credentials
google.golang.org/grpc/credentials/insecure
google.golang.org/grpc/encoding
google.golang.org/grpc/encoding/proto
google.golang.org/grpc/grpclog
google.golang.org/grpc/internal
google.golang.org/grpc/internal/backoff
google.golang.org/grpc/internal/balancer/gracefulswitch
google.golang.org/grpc/internal/balancerload
google.golang.org/grpc/internal/binarylog
google.golang.org/grpc/internal/buffer
google.golang.org/grpc/internal/channelz
google.golang.org/grpc/internal/credentials
google.golang.org/grpc/internal/envconfig
google.golang.org/grpc/internal/grpclog
google.golang.org/grpc/internal/grpcrand
google.golang.org/grpc/internal/grpcsync
google.golang.org/grpc/internal/grpcutil
google.golang.org/grpc/internal/metadata
google.golang.org/grpc/internal/pretty
google.golang.org/grpc/internal/resolver
google.golang.org/grpc/internal/resolver/dns
google.golang.org/grpc/internal/resolver/passthrough
google.golang.org/grpc/internal/resolver/unix
google.golang.org/grpc/internal/serviceconfig
google.golang.org/grpc/internal/status
google.golang.org/grpc/internal/syscall
google.golang.org/grpc/internal/transport
google.golang.org/grpc/internal/transport/networktype
google.golang.org/grpc/keepalive
google.golang.org/grpc/metadata
google.golang.org/grpc/peer
google.golang.org/grpc/resolver
google.golang.org/grpc/serviceconfig
google.golang.org/grpc/stats
google.golang.org/grpc/status
google.golang.org/grpc/tap
# google.golang.org/protobuf v1.34.1
## explicit; go 1.17
google.golang.org/protobuf/encoding/protojson
google.golang.org/protobuf/encoding/prototext
google.golang.org/protobuf/encoding/protowire
google.golang.org/protobuf/internal/descfmt
google.golang.org/protobuf/internal/descopts
google.golang.org/protobuf/internal/detrand
google.golang.org/protobuf/internal/editiondefaults
google.golang.org/protobuf/internal/editionssupport
google.golang.org/protobuf/internal/encoding/defval
google.golang.org/protobuf/internal/encoding/json
google.golang.org/protobuf/internal/encoding/messageset
google.golang.org/protobuf/internal/encoding/tag
google.golang.org/protobuf/internal/encoding/text
google.golang.org/protobuf/internal/errors
google.golang.org/protobuf/internal/filedesc
google.golang.org/protobuf/internal/filetype
google.golang.org/protobuf/internal/flags
google.golang.org/protobuf/internal/genid
google.golang.org/protobuf/internal/impl
google.golang.org/protobuf/internal/order
google.golang.org/protobuf/internal/pragma
google.golang.org/protobuf/internal/set
google.golang.org/protobuf/internal/strs
google.golang.org/protobuf/internal/version
google.golang.org/protobuf/proto
google.golang.org/protobuf/reflect/protodesc
google.golang.org/protobuf/reflect/protoreflect
google.golang.org/protobuf/reflect/protoregistry
google.golang.org/protobuf/runtime/protoiface
google.golang.org/protobuf/runtime/protoimpl
google.golang.org/protobuf/types/descriptorpb
google.golang.org/protobuf/types/gofeaturespb
google.golang.org/protobuf/types/known/anypb
google.golang.org/protobuf/types/known/durationpb
google.golang.org/protobuf/types/known/timestamppb
# gopkg.in/guregu/null.v3 v3.5.0
## explicit
gopkg.in/guregu/null.v3
# gopkg.in/yaml.v3 v3.0.1
## explicit
gopkg.in/yaml.v3
# gorm.io/gorm v1.25.5
## explicit; go 1.18
gorm.io/gorm
gorm.io/gorm/clause
gorm.io/gorm/logger
gorm.io/gorm/schema
gorm.io/gorm/utils
