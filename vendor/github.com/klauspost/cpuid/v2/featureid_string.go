// Code generated by "stringer -type=FeatureID,Vendor"; DO NOT EDIT.

package cpuid

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[ADX-1]
	_ = x[AESNI-2]
	_ = x[AMD3DNOW-3]
	_ = x[AMD3DNOWEXT-4]
	_ = x[AMXBF16-5]
	_ = x[AMXFP16-6]
	_ = x[AMXINT8-7]
	_ = x[AMXTILE-8]
	_ = x[APX_F-9]
	_ = x[AVX-10]
	_ = x[AVX10-11]
	_ = x[AVX10_128-12]
	_ = x[AVX10_256-13]
	_ = x[AVX10_512-14]
	_ = x[AVX2-15]
	_ = x[AVX512BF16-16]
	_ = x[AVX512BITALG-17]
	_ = x[AVX512BW-18]
	_ = x[AVX512CD-19]
	_ = x[AVX512DQ-20]
	_ = x[AVX512ER-21]
	_ = x[AVX512F-22]
	_ = x[AVX512FP16-23]
	_ = x[AVX512IFMA-24]
	_ = x[AVX512PF-25]
	_ = x[AVX512VBMI-26]
	_ = x[AVX512VBMI2-27]
	_ = x[AVX512VL-28]
	_ = x[AVX512VNNI-29]
	_ = x[AVX512VP2INTERSECT-30]
	_ = x[AVX512VPOPCNTDQ-31]
	_ = x[AVXIFMA-32]
	_ = x[AVXNECONVERT-33]
	_ = x[AVXSLOW-34]
	_ = x[AVXVNNI-35]
	_ = x[AVXVNNIINT8-36]
	_ = x[BHI_CTRL-37]
	_ = x[BMI1-38]
	_ = x[BMI2-39]
	_ = x[CETIBT-40]
	_ = x[CETSS-41]
	_ = x[CLDEMOTE-42]
	_ = x[CLMUL-43]
	_ = x[CLZERO-44]
	_ = x[CMOV-45]
	_ = x[CMPCCXADD-46]
	_ = x[CMPSB_SCADBS_SHORT-47]
	_ = x[CMPXCHG8-48]
	_ = x[CPBOOST-49]
	_ = x[CPPC-50]
	_ = x[CX16-51]
	_ = x[EFER_LMSLE_UNS-52]
	_ = x[ENQCMD-53]
	_ = x[ERMS-54]
	_ = x[F16C-55]
	_ = x[FLUSH_L1D-56]
	_ = x[FMA3-57]
	_ = x[FMA4-58]
	_ = x[FP128-59]
	_ = x[FP256-60]
	_ = x[FSRM-61]
	_ = x[FXSR-62]
	_ = x[FXSROPT-63]
	_ = x[GFNI-64]
	_ = x[HLE-65]
	_ = x[HRESET-66]
	_ = x[HTT-67]
	_ = x[HWA-68]
	_ = x[HYBRID_CPU-69]
	_ = x[HYPERVISOR-70]
	_ = x[IA32_ARCH_CAP-71]
	_ = x[IA32_CORE_CAP-72]
	_ = x[IBPB-73]
	_ = x[IBPB_BRTYPE-74]
	_ = x[IBRS-75]
	_ = x[IBRS_PREFERRED-76]
	_ = x[IBRS_PROVIDES_SMP-77]
	_ = x[IBS-78]
	_ = x[IBSBRNTRGT-79]
	_ = x[IBSFETCHSAM-80]
	_ = x[IBSFFV-81]
	_ = x[IBSOPCNT-82]
	_ = x[IBSOPCNTEXT-83]
	_ = x[IBSOPSAM-84]
	_ = x[IBSRDWROPCNT-85]
	_ = x[IBSRIPINVALIDCHK-86]
	_ = x[IBS_FETCH_CTLX-87]
	_ = x[IBS_OPDATA4-88]
	_ = x[IBS_OPFUSE-89]
	_ = x[IBS_PREVENTHOST-90]
	_ = x[IBS_ZEN4-91]
	_ = x[IDPRED_CTRL-92]
	_ = x[INT_WBINVD-93]
	_ = x[INVLPGB-94]
	_ = x[KEYLOCKER-95]
	_ = x[KEYLOCKERW-96]
	_ = x[LAHF-97]
	_ = x[LAM-98]
	_ = x[LBRVIRT-99]
	_ = x[LZCNT-100]
	_ = x[MCAOVERFLOW-101]
	_ = x[MCDT_NO-102]
	_ = x[MCOMMIT-103]
	_ = x[MD_CLEAR-104]
	_ = x[MMX-105]
	_ = x[MMXEXT-106]
	_ = x[MOVBE-107]
	_ = x[MOVDIR64B-108]
	_ = x[MOVDIRI-109]
	_ = x[MOVSB_ZL-110]
	_ = x[MOVU-111]
	_ = x[MPX-112]
	_ = x[MSRIRC-113]
	_ = x[MSRLIST-114]
	_ = x[MSR_PAGEFLUSH-115]
	_ = x[NRIPS-116]
	_ = x[NX-117]
	_ = x[OSXSAVE-118]
	_ = x[PCONFIG-119]
	_ = x[POPCNT-120]
	_ = x[PPIN-121]
	_ = x[PREFETCHI-122]
	_ = x[PSFD-123]
	_ = x[RDPRU-124]
	_ = x[RDRAND-125]
	_ = x[RDSEED-126]
	_ = x[RDTSCP-127]
	_ = x[RRSBA_CTRL-128]
	_ = x[RTM-129]
	_ = x[RTM_ALWAYS_ABORT-130]
	_ = x[SBPB-131]
	_ = x[SERIALIZE-132]
	_ = x[SEV-133]
	_ = x[SEV_64BIT-134]
	_ = x[SEV_ALTERNATIVE-135]
	_ = x[SEV_DEBUGSWAP-136]
	_ = x[SEV_ES-137]
	_ = x[SEV_RESTRICTED-138]
	_ = x[SEV_SNP-139]
	_ = x[SGX-140]
	_ = x[SGXLC-141]
	_ = x[SHA-142]
	_ = x[SME-143]
	_ = x[SME_COHERENT-144]
	_ = x[SPEC_CTRL_SSBD-145]
	_ = x[SRBDS_CTRL-146]
	_ = x[SRSO_MSR_FIX-147]
	_ = x[SRSO_NO-148]
	_ = x[SRSO_USER_KERNEL_NO-149]
	_ = x[SSE-150]
	_ = x[SSE2-151]
	_ = x[SSE3-152]
	_ = x[SSE4-153]
	_ = x[SSE42-154]
	_ = x[SSE4A-155]
	_ = x[SSSE3-156]
	_ = x[STIBP-157]
	_ = x[STIBP_ALWAYSON-158]
	_ = x[STOSB_SHORT-159]
	_ = x[SUCCOR-160]
	_ = x[SVM-161]
	_ = x[SVMDA-162]
	_ = x[SVMFBASID-163]
	_ = x[SVML-164]
	_ = x[SVMNP-165]
	_ = x[SVMPF-166]
	_ = x[SVMPFT-167]
	_ = x[SYSCALL-168]
	_ = x[SYSEE-169]
	_ = x[TBM-170]
	_ = x[TDX_GUEST-171]
	_ = x[TLB_FLUSH_NESTED-172]
	_ = x[TME-173]
	_ = x[TOPEXT-174]
	_ = x[TSCRATEMSR-175]
	_ = x[TSXLDTRK-176]
	_ = x[VAES-177]
	_ = x[VMCBCLEAN-178]
	_ = x[VMPL-179]
	_ = x[VMSA_REGPROT-180]
	_ = x[VMX-181]
	_ = x[VPCLMULQDQ-182]
	_ = x[VTE-183]
	_ = x[WAITPKG-184]
	_ = x[WBNOINVD-185]
	_ = x[WRMSRNS-186]
	_ = x[X87-187]
	_ = x[XGETBV1-188]
	_ = x[XOP-189]
	_ = x[XSAVE-190]
	_ = x[XSAVEC-191]
	_ = x[XSAVEOPT-192]
	_ = x[XSAVES-193]
	_ = x[AESARM-194]
	_ = x[ARMCPUID-195]
	_ = x[ASIMD-196]
	_ = x[ASIMDDP-197]
	_ = x[ASIMDHP-198]
	_ = x[ASIMDRDM-199]
	_ = x[ATOMICS-200]
	_ = x[CRC32-201]
	_ = x[DCPOP-202]
	_ = x[EVTSTRM-203]
	_ = x[FCMA-204]
	_ = x[FP-205]
	_ = x[FPHP-206]
	_ = x[GPA-207]
	_ = x[JSCVT-208]
	_ = x[LRCPC-209]
	_ = x[PMULL-210]
	_ = x[SHA1-211]
	_ = x[SHA2-212]
	_ = x[SHA3-213]
	_ = x[SHA512-214]
	_ = x[SM3-215]
	_ = x[SM4-216]
	_ = x[SVE-217]
	_ = x[lastID-218]
	_ = x[firstID-0]
}

const _FeatureID_name = "firstIDADXAESNIAMD3DNOWAMD3DNOWEXTAMXBF16AMXFP16AMXINT8AMXTILEAPX_FAVXAVX10AVX10_128AVX10_256AVX10_512AVX2AVX512BF16AVX512BITALGAVX512BWAVX512CDAVX512DQAVX512ERAVX512FAVX512FP16AVX512IFMAAVX512PFAVX512VBMIAVX512VBMI2AVX512VLAVX512VNNIAVX512VP2INTERSECTAVX512VPOPCNTDQAVXIFMAAVXNECONVERTAVXSLOWAVXVNNIAVXVNNIINT8BHI_CTRLBMI1BMI2CETIBTCETSSCLDEMOTECLMULCLZEROCMOVCMPCCXADDCMPSB_SCADBS_SHORTCMPXCHG8CPBOOSTCPPCCX16EFER_LMSLE_UNSENQCMDERMSF16CFLUSH_L1DFMA3FMA4FP128FP256FSRMFXSRFXSROPTGFNIHLEHRESETHTTHWAHYBRID_CPUHYPERVISORIA32_ARCH_CAPIA32_CORE_CAPIBPBIBPB_BRTYPEIBRSIBRS_PREFERREDIBRS_PROVIDES_SMPIBSIBSBRNTRGTIBSFETCHSAMIBSFFVIBSOPCNTIBSOPCNTEXTIBSOPSAMIBSRDWROPCNTIBSRIPINVALIDCHKIBS_FETCH_CTLXIBS_OPDATA4IBS_OPFUSEIBS_PREVENTHOSTIBS_ZEN4IDPRED_CTRLINT_WBINVDINVLPGBKEYLOCKERKEYLOCKERWLAHFLAMLBRVIRTLZCNTMCAOVERFLOWMCDT_NOMCOMMITMD_CLEARMMXMMXEXTMOVBEMOVDIR64BMOVDIRIMOVSB_ZLMOVUMPXMSRIRCMSRLISTMSR_PAGEFLUSHNRIPSNXOSXSAVEPCONFIGPOPCNTPPINPREFETCHIPSFDRDPRURDRANDRDSEEDRDTSCPRRSBA_CTRLRTMRTM_ALWAYS_ABORTSBPBSERIALIZESEVSEV_64BITSEV_ALTERNATIVESEV_DEBUGSWAPSEV_ESSEV_RESTRICTEDSEV_SNPSGXSGXLCSHASMESME_COHERENTSPEC_CTRL_SSBDSRBDS_CTRLSRSO_MSR_FIXSRSO_NOSRSO_USER_KERNEL_NOSSESSE2SSE3SSE4SSE42SSE4ASSSE3STIBPSTIBP_ALWAYSONSTOSB_SHORTSUCCORSVMSVMDASVMFBASIDSVMLSVMNPSVMPFSVMPFTSYSCALLSYSEETBMTDX_GUESTTLB_FLUSH_NESTEDTMETOPEXTTSCRATEMSRTSXLDTRKVAESVMCBCLEANVMPLVMSA_REGPROTVMXVPCLMULQDQVTEWAITPKGWBNOINVDWRMSRNSX87XGETBV1XOPXSAVEXSAVECXSAVEOPTXSAVESAESARMARMCPUIDASIMDASIMDDPASIMDHPASIMDRDMATOMICSCRC32DCPOPEVTSTRMFCMAFPFPHPGPAJSCVTLRCPCPMULLSHA1SHA2SHA3SHA512SM3SM4SVElastID"

var _FeatureID_index = [...]uint16{0, 7, 10, 15, 23, 34, 41, 48, 55, 62, 67, 70, 75, 84, 93, 102, 106, 116, 128, 136, 144, 152, 160, 167, 177, 187, 195, 205, 216, 224, 234, 252, 267, 274, 286, 293, 300, 311, 319, 323, 327, 333, 338, 346, 351, 357, 361, 370, 388, 396, 403, 407, 411, 425, 431, 435, 439, 448, 452, 456, 461, 466, 470, 474, 481, 485, 488, 494, 497, 500, 510, 520, 533, 546, 550, 561, 565, 579, 596, 599, 609, 620, 626, 634, 645, 653, 665, 681, 695, 706, 716, 731, 739, 750, 760, 767, 776, 786, 790, 793, 800, 805, 816, 823, 830, 838, 841, 847, 852, 861, 868, 876, 880, 883, 889, 896, 909, 914, 916, 923, 930, 936, 940, 949, 953, 958, 964, 970, 976, 986, 989, 1005, 1009, 1018, 1021, 1030, 1045, 1058, 1064, 1078, 1085, 1088, 1093, 1096, 1099, 1111, 1125, 1135, 1147, 1154, 1173, 1176, 1180, 1184, 1188, 1193, 1198, 1203, 1208, 1222, 1233, 1239, 1242, 1247, 1256, 1260, 1265, 1270, 1276, 1283, 1288, 1291, 1300, 1316, 1319, 1325, 1335, 1343, 1347, 1356, 1360, 1372, 1375, 1385, 1388, 1395, 1403, 1410, 1413, 1420, 1423, 1428, 1434, 1442, 1448, 1454, 1462, 1467, 1474, 1481, 1489, 1496, 1501, 1506, 1513, 1517, 1519, 1523, 1526, 1531, 1536, 1541, 1545, 1549, 1553, 1559, 1562, 1565, 1568, 1574}

func (i FeatureID) String() string {
	if i < 0 || i >= FeatureID(len(_FeatureID_index)-1) {
		return "FeatureID(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _FeatureID_name[_FeatureID_index[i]:_FeatureID_index[i+1]]
}
func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[VendorUnknown-0]
	_ = x[Intel-1]
	_ = x[AMD-2]
	_ = x[VIA-3]
	_ = x[Transmeta-4]
	_ = x[NSC-5]
	_ = x[KVM-6]
	_ = x[MSVM-7]
	_ = x[VMware-8]
	_ = x[XenHVM-9]
	_ = x[Bhyve-10]
	_ = x[Hygon-11]
	_ = x[SiS-12]
	_ = x[RDC-13]
	_ = x[Ampere-14]
	_ = x[ARM-15]
	_ = x[Broadcom-16]
	_ = x[Cavium-17]
	_ = x[DEC-18]
	_ = x[Fujitsu-19]
	_ = x[Infineon-20]
	_ = x[Motorola-21]
	_ = x[NVIDIA-22]
	_ = x[AMCC-23]
	_ = x[Qualcomm-24]
	_ = x[Marvell-25]
	_ = x[lastVendor-26]
}

const _Vendor_name = "VendorUnknownIntelAMDVIATransmetaNSCKVMMSVMVMwareXenHVMBhyveHygonSiSRDCAmpereARMBroadcomCaviumDECFujitsuInfineonMotorolaNVIDIAAMCCQualcommMarvelllastVendor"

var _Vendor_index = [...]uint8{0, 13, 18, 21, 24, 33, 36, 39, 43, 49, 55, 60, 65, 68, 71, 77, 80, 88, 94, 97, 104, 112, 120, 126, 130, 138, 145, 155}

func (i Vendor) String() string {
	if i < 0 || i >= Vendor(len(_Vendor_index)-1) {
		return "Vendor(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _Vendor_name[_Vendor_index[i]:_Vendor_index[i+1]]
}
