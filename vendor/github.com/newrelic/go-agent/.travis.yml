language: go
go_import_path: github.com/newrelic/go-agent

matrix:
  include:
  #
  # Go Agent Version 2
  #
  - go: "1.3"
    env: DIRS=.
  - go: "1.4"
    env: DIRS=.
  - go: "1.5"
    env: DIRS=.
  - go: "1.6"
    env: DIRS=.
  - go: "1.7"
    env: DIRS=.
  - go: "1.8"
    env: DIRS=.
  - go: "1.9"
    env: DIRS=.
  - go: "1.10"
    env: DIRS=.
  - go: "1.11"
    env: DIRS=.
  - go: "1.12"
    env: DIRS=.
  - go: "1.13"
    env: DIRS=.
  - go: "1.13"
    env: DIRS=_integrations/nrawssdk
  - go: "1.13"
    env: DIRS=_integrations/nrecho
  - go: "1.13"
    env: DIRS=_integrations/nrgin/v1
  - go: "1.13"
    env: DIRS=_integrations/nrgorilla/v1
  - go: "1.13"
    env: DIRS=_integrations/nrlogrus
  - go: "1.13"
    env: DIRS=_integrations/nrlogxi/v1
  - go: "1.13"
    env: DIRS=_integrations/nrpkgerrors
  - go: "1.13"
    env: DIRS=_integrations/nrlambda
  - go: "1.13"
    env: DIRS=_integrations/nrmysql
  - go: "1.13"
    env: DIRS=_integrations/nrpq
  - go: "1.13"
    env: DIRS=_integrations/nrsqlite3
  - go: "1.13"
    env: DIRS=_integrations/nrgrpc
  # As of October 2019, errors result from go get -u github.com/micro/go-micro
  # - go: "1.13"
  #   env: DIRS=_integrations/nrmicro
  - go: "1.13"
    env: DIRS=_integrations/nrnats
  - go: "1.13"
    env: DIRS=_integrations/nrstan
  - go: "1.13"
    env: DIRS=_integrations/logcontext
  - go: "1.13"
    env: DIRS=_integrations/nrzap
  - go: "1.13"
    env: DIRS=_integrations/nrhttprouter
  - go: "1.13"
    env: DIRS=_integrations/nrb3
  - go: "1.13"
    env: DIRS=_integrations/nrmongo

  #
  # Go Agent Version 3
  #
  - go: "1.7"
    env: DIRS=v3/newrelic,v3/internal,v3/examples
  - go: "1.8"
    env: DIRS=v3/newrelic,v3/internal,v3/examples
  - go: "1.9"
    env: DIRS=v3/newrelic,v3/internal,v3/examples
  - go: "1.10"
    env: DIRS=v3/newrelic,v3/internal,v3/examples
  - go: "1.11"
    env: DIRS=v3/newrelic,v3/internal,v3/examples
  - go: "1.12"
    env: DIRS=v3/newrelic,v3/internal,v3/examples
  - go: "1.13.4"
    env: DIRS=v3/newrelic,v3/internal,v3/examples,v3/integrations/logcontext
  - go: "1.13.4"
    env:
      - DIRS=v3/integrations/nrawssdk-v1
      - EXTRATESTING="go get -u github.com/aws/aws-sdk-go@master"
  - go: "1.13.4"
    env:
      - DIRS=v3/integrations/nrawssdk-v2
      - EXTRATESTING="go get -u github.com/aws/aws-sdk-go-v2@master"
  - go: "1.13.4"
    env:
      - DIRS=v3/integrations/nrecho-v3
      # Test against the latest v3 Echo:
      - EXTRATESTING="go get -u github.com/labstack/echo@v3"
  - go: "1.13.4"
    env:
      - DIRS=v3/integrations/nrecho-v4
      - EXTRATESTING="go get -u github.com/labstack/echo/v4@master"
  - go: "1.13.4"
    env:
      - DIRS=v3/integrations/nrgin
      - EXTRATESTING="go get -u github.com/gin-gonic/gin@master"
  - go: "1.13.4"
    env:
      - DIRS=v3/integrations/nrgorilla
      - EXTRATESTING="go get -u github.com/gorilla/mux@master"
  - go: "1.13.4"
    env:
      - DIRS=v3/integrations/nrlogrus
      - EXTRATESTING="go get -u github.com/sirupsen/logrus@master"
  - go: "1.13.4"
    env:
      - DIRS=v3/integrations/nrlogxi
      - EXTRATESTING="go get -u github.com/mgutz/logxi@master"
  - go: "1.13.4"
    env:
      - DIRS=v3/integrations/nrpkgerrors
      - EXTRATESTING="go get -u github.com/pkg/errors@master"
  - go: "1.13.4"
    env:
      - DIRS=v3/integrations/nrlambda
      - EXTRATESTING="go get -u github.com/aws/aws-lambda-go@master"
  - go: "1.13.4"
    env:
      - DIRS=v3/integrations/nrmysql
      - EXTRATESTING="go get -u github.com/go-sql-driver/mysql@master"
  - go: "1.13.4"
    env:
      - DIRS=v3/integrations/nrpq
      - EXTRATESTING="go get -u github.com/lib/pq@master"
  - go: "1.13.4"
    env:
      - DIRS=v3/integrations/nrpq/example/sqlx
  - go: "1.13.4"
    env:
      - DIRS=v3/integrations/nrsqlite3
      - EXTRATESTING="go get -u github.com/mattn/go-sqlite3@master"
  - go: "1.13.4"
    env:
      - DIRS=v3/integrations/nrgrpc
      # As of Nov 2019, go get -u google.golang.org/grpc@master
      # doesn't work.  So instead let's test against the latest
      # released version.
      - EXTRATESTING="go get -u google.golang.org/grpc@latest"
  - go: "1.13.4"
    env:
      - DIRS=v3/integrations/nrmicro
      - EXTRATESTING="go get -u github.com/micro/go-micro@master"
  - go: "1.13.4"
    env:
      - DIRS=v3/integrations/nrnats
      - EXTRATESTING="go get -u github.com/nats-io/nats.go/@master"
  - go: "1.13.4"
    env:
      - DIRS=v3/integrations/nrnats/test
      - EXTRATESTING="go get -u github.com/nats-io/nats.go/@master"
  - go: "1.13.4"
    env:
      - DIRS=v3/integrations/nrstan
      - EXTRATESTING="go get -u github.com/nats-io/stan.go/@master"
  - go: "1.13.4"
    env:
      - DIRS=v3/integrations/nrstan/test
      - EXTRATESTING="go get -u github.com/nats-io/stan.go/@master"
  - go: "1.13.4"
    env:
      - DIRS=v3/integrations/nrstan/examples
      - EXTRATESTING="go get -u github.com/nats-io/stan.go/@master"
  - go: "1.13.4"
    env:
      - DIRS=v3/integrations/logcontext
      - EXTRATESTING="go get -u github.com/sirupsen/logrus@master"
  - go: "1.13.4"
    env:
      - DIRS=v3/integrations/nrzap
      - EXTRATESTING="go get -u go.uber.org/zap@master"
  - go: "1.13.4"
    env:
      - DIRS=v3/integrations/nrhttprouter
      - EXTRATESTING="go get -u github.com/julienschmidt/httprouter@master"
  - go: "1.13.4"
    env: DIRS=v3/integrations/nrb3
  - go: "1.13.4"
    env:
      - DIRS=v3/integrations/nrmongo
      - EXTRATESTING="go get -u go.mongodb.org/mongo-driver@master"

# Skip the install step. Don't `go get` dependencies.
install: true

script:
  - bash build-script.sh
