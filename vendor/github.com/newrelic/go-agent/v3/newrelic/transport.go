// Copyright 2020 New Relic Corporation. All rights reserved.
// SPDX-License-Identifier: Apache-2.0

// +build go1.13

package newrelic

import (
	"net"
	"net/http"
	"time"
)

var (
	// collectorDefaultTransport is the http.Transport to be used with
	// communication to the collector backend if a Transport is not set on the
	// Config.
	collectorDefaultTransport = &http.Transport{
		Proxy: http.ProxyFromEnvironment,
		DialContext: (&net.Dialer{
			Timeout:   30 * time.Second,
			KeepAlive: 30 * time.Second,
			DualStack: true,
		}).DialContext,
		ForceAttemptHTTP2:     true, // added in go 1.13
		MaxIdleConns:          100,
		MaxIdleConnsPerHost:   100, // note: different from default global transport
		IdleConnTimeout:       90 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
	}
)
