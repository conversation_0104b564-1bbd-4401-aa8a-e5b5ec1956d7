package internal

import (
	"bytes"
)

func addOptionalStringField(w *j<PERSON><PERSON>ieldsWriter, key, value string) {
	if value != "" {
		w.string<PERSON>ield(key, value)
	}
}

func intrinsicsJSON(e *TxnEvent, buf *bytes.Buffer) {
	w := jsonFieldsWriter{buf: buf}

	buf.WriteByte('{')

	w.float<PERSON>ield("totalTime", e.TotalTime.Seconds())

	if e.BetterCAT.Enabled {
		w.string<PERSON>ield("guid", e.BetterCAT.ID)
		w.string<PERSON>ield("traceId", e.BetterCAT.TraceID())
		w.writer<PERSON>ield("priority", e.BetterCAT.Priority)
		w.bool<PERSON>ield("sampled", e.BetterCAT.Sampled)
	}

	if e.CrossProcess.Used() {
		addOptionalStringField(&w, "client_cross_process_id", e.CrossProcess.ClientID)
		addOptionalStringField(&w, "trip_id", e.CrossProcess.TripID)
		addOptionalStringField(&w, "path_hash", e.<PERSON>Process.PathHash)
		addOptionalStringField(&w, "referring_transaction_guid", e.CrossProcess.ReferringTxnGUID)
	}

	if e.CrossProcess.IsSynthetics() {
		addOptionalStringField(&w, "synthetics_resource_id", e.CrossProcess.Synthetics.ResourceID)
		addOptionalStringField(&w, "synthetics_job_id", e.CrossProcess.Synthetics.JobID)
		addOptionalStringField(&w, "synthetics_monitor_id", e.CrossProcess.Synthetics.MonitorID)
	}

	buf.WriteByte('}')
}
