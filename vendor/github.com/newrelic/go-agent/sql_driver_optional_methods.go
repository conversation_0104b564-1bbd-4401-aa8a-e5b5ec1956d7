// +build go1.10

package newrelic

import "database/sql/driver"

func optionalMethodsDriver(dv *wrapDriver) driver.Driver {
	// GENERATED CODE DO NOT MODIFY
	// This code generated by internal/tools/interface-wrapping
	var (
		i0 int32 = 1 << 0
	)
	var interfaceSet int32
	if _, ok := dv.original.(driver.DriverContext); ok {
		interfaceSet |= i0
	}
	switch interfaceSet {
	default: // No optional interfaces implemented
		return struct {
			driver.Driver
		}{dv}
	case i0:
		return struct {
			driver.Driver
			driver.DriverContext
		}{dv, dv}
	}
}

func optionalMethodsStmt(stmt *wrapStmt) driver.Stmt {
	// GENERATED CODE DO NOT MODIFY
	// This code generated by internal/tools/interface-wrapping
	var (
		i0 int32 = 1 << 0
		i1 int32 = 1 << 1
		i2 int32 = 1 << 2
		i3 int32 = 1 << 3
	)
	var interfaceSet int32
	if _, ok := stmt.original.(driver.ColumnConverter); ok {
		interfaceSet |= i0
	}
	if _, ok := stmt.original.(driver.NamedValue<PERSON>he<PERSON>); ok {
		interfaceSet |= i1
	}
	if _, ok := stmt.original.(driver.StmtExecContext); ok {
		interfaceSet |= i2
	}
	if _, ok := stmt.original.(driver.StmtQueryContext); ok {
		interfaceSet |= i3
	}
	switch interfaceSet {
	default: // No optional interfaces implemented
		return struct {
			driver.Stmt
		}{stmt}
	case i0:
		return struct {
			driver.Stmt
			driver.ColumnConverter
		}{stmt, stmt}
	case i1:
		return struct {
			driver.Stmt
			driver.NamedValueChecker
		}{stmt, stmt}
	case i0 | i1:
		return struct {
			driver.Stmt
			driver.ColumnConverter
			driver.NamedValueChecker
		}{stmt, stmt, stmt}
	case i2:
		return struct {
			driver.Stmt
			driver.StmtExecContext
		}{stmt, stmt}
	case i0 | i2:
		return struct {
			driver.Stmt
			driver.ColumnConverter
			driver.StmtExecContext
		}{stmt, stmt, stmt}
	case i1 | i2:
		return struct {
			driver.Stmt
			driver.NamedValueChecker
			driver.StmtExecContext
		}{stmt, stmt, stmt}
	case i0 | i1 | i2:
		return struct {
			driver.Stmt
			driver.ColumnConverter
			driver.NamedValueChecker
			driver.StmtExecContext
		}{stmt, stmt, stmt, stmt}
	case i3:
		return struct {
			driver.Stmt
			driver.StmtQueryContext
		}{stmt, stmt}
	case i0 | i3:
		return struct {
			driver.Stmt
			driver.ColumnConverter
			driver.StmtQueryContext
		}{stmt, stmt, stmt}
	case i1 | i3:
		return struct {
			driver.Stmt
			driver.NamedValueChecker
			driver.StmtQueryContext
		}{stmt, stmt, stmt}
	case i0 | i1 | i3:
		return struct {
			driver.Stmt
			driver.ColumnConverter
			driver.NamedValueChecker
			driver.StmtQueryContext
		}{stmt, stmt, stmt, stmt}
	case i2 | i3:
		return struct {
			driver.Stmt
			driver.StmtExecContext
			driver.StmtQueryContext
		}{stmt, stmt, stmt}
	case i0 | i2 | i3:
		return struct {
			driver.Stmt
			driver.ColumnConverter
			driver.StmtExecContext
			driver.StmtQueryContext
		}{stmt, stmt, stmt, stmt}
	case i1 | i2 | i3:
		return struct {
			driver.Stmt
			driver.NamedValueChecker
			driver.StmtExecContext
			driver.StmtQueryContext
		}{stmt, stmt, stmt, stmt}
	case i0 | i1 | i2 | i3:
		return struct {
			driver.Stmt
			driver.ColumnConverter
			driver.NamedValueChecker
			driver.StmtExecContext
			driver.StmtQueryContext
		}{stmt, stmt, stmt, stmt, stmt}
	}
}

func optionalMethodsConn(conn *wrapConn) driver.Conn {
	// GENERATED CODE DO NOT MODIFY
	// This code generated by internal/tools/interface-wrapping
	var (
		i0 int32 = 1 << 0
		i1 int32 = 1 << 1
		i2 int32 = 1 << 2
		i3 int32 = 1 << 3
		i4 int32 = 1 << 4
		i5 int32 = 1 << 5
		i6 int32 = 1 << 6
		i7 int32 = 1 << 7
	)
	var interfaceSet int32
	if _, ok := conn.original.(driver.ConnBeginTx); ok {
		interfaceSet |= i0
	}
	if _, ok := conn.original.(driver.ConnPrepareContext); ok {
		interfaceSet |= i1
	}
	if _, ok := conn.original.(driver.Execer); ok {
		interfaceSet |= i2
	}
	if _, ok := conn.original.(driver.ExecerContext); ok {
		interfaceSet |= i3
	}
	if _, ok := conn.original.(driver.NamedValueChecker); ok {
		interfaceSet |= i4
	}
	if _, ok := conn.original.(driver.Pinger); ok {
		interfaceSet |= i5
	}
	if _, ok := conn.original.(driver.Queryer); ok {
		interfaceSet |= i6
	}
	if _, ok := conn.original.(driver.QueryerContext); ok {
		interfaceSet |= i7
	}
	switch interfaceSet {
	default: // No optional interfaces implemented
		return struct {
			driver.Conn
		}{conn}
	case i0:
		return struct {
			driver.Conn
			driver.ConnBeginTx
		}{conn, conn}
	case i1:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
		}{conn, conn}
	case i0 | i1:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
		}{conn, conn, conn}
	case i2:
		return struct {
			driver.Conn
			driver.Execer
		}{conn, conn}
	case i0 | i2:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
		}{conn, conn, conn}
	case i1 | i2:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
		}{conn, conn, conn}
	case i0 | i1 | i2:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
		}{conn, conn, conn, conn}
	case i3:
		return struct {
			driver.Conn
			driver.ExecerContext
		}{conn, conn}
	case i0 | i3:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ExecerContext
		}{conn, conn, conn}
	case i1 | i3:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.ExecerContext
		}{conn, conn, conn}
	case i0 | i1 | i3:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.ExecerContext
		}{conn, conn, conn, conn}
	case i2 | i3:
		return struct {
			driver.Conn
			driver.Execer
			driver.ExecerContext
		}{conn, conn, conn}
	case i0 | i2 | i3:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.ExecerContext
		}{conn, conn, conn, conn}
	case i1 | i2 | i3:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
		}{conn, conn, conn, conn}
	case i0 | i1 | i2 | i3:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
		}{conn, conn, conn, conn, conn}
	case i4:
		return struct {
			driver.Conn
			driver.NamedValueChecker
		}{conn, conn}
	case i0 | i4:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.NamedValueChecker
		}{conn, conn, conn}
	case i1 | i4:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.NamedValueChecker
		}{conn, conn, conn}
	case i0 | i1 | i4:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.NamedValueChecker
		}{conn, conn, conn, conn}
	case i2 | i4:
		return struct {
			driver.Conn
			driver.Execer
			driver.NamedValueChecker
		}{conn, conn, conn}
	case i0 | i2 | i4:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.NamedValueChecker
		}{conn, conn, conn, conn}
	case i1 | i2 | i4:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.NamedValueChecker
		}{conn, conn, conn, conn}
	case i0 | i1 | i2 | i4:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.NamedValueChecker
		}{conn, conn, conn, conn, conn}
	case i3 | i4:
		return struct {
			driver.Conn
			driver.ExecerContext
			driver.NamedValueChecker
		}{conn, conn, conn}
	case i0 | i3 | i4:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ExecerContext
			driver.NamedValueChecker
		}{conn, conn, conn, conn}
	case i1 | i3 | i4:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.NamedValueChecker
		}{conn, conn, conn, conn}
	case i0 | i1 | i3 | i4:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.NamedValueChecker
		}{conn, conn, conn, conn, conn}
	case i2 | i3 | i4:
		return struct {
			driver.Conn
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
		}{conn, conn, conn, conn}
	case i0 | i2 | i3 | i4:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
		}{conn, conn, conn, conn, conn}
	case i1 | i2 | i3 | i4:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
		}{conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i3 | i4:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
		}{conn, conn, conn, conn, conn, conn}
	case i5:
		return struct {
			driver.Conn
			driver.Pinger
		}{conn, conn}
	case i0 | i5:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Pinger
		}{conn, conn, conn}
	case i1 | i5:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Pinger
		}{conn, conn, conn}
	case i0 | i1 | i5:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Pinger
		}{conn, conn, conn, conn}
	case i2 | i5:
		return struct {
			driver.Conn
			driver.Execer
			driver.Pinger
		}{conn, conn, conn}
	case i0 | i2 | i5:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.Pinger
		}{conn, conn, conn, conn}
	case i1 | i2 | i5:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.Pinger
		}{conn, conn, conn, conn}
	case i0 | i1 | i2 | i5:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.Pinger
		}{conn, conn, conn, conn, conn}
	case i3 | i5:
		return struct {
			driver.Conn
			driver.ExecerContext
			driver.Pinger
		}{conn, conn, conn}
	case i0 | i3 | i5:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ExecerContext
			driver.Pinger
		}{conn, conn, conn, conn}
	case i1 | i3 | i5:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.Pinger
		}{conn, conn, conn, conn}
	case i0 | i1 | i3 | i5:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.Pinger
		}{conn, conn, conn, conn, conn}
	case i2 | i3 | i5:
		return struct {
			driver.Conn
			driver.Execer
			driver.ExecerContext
			driver.Pinger
		}{conn, conn, conn, conn}
	case i0 | i2 | i3 | i5:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.ExecerContext
			driver.Pinger
		}{conn, conn, conn, conn, conn}
	case i1 | i2 | i3 | i5:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.Pinger
		}{conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i3 | i5:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.Pinger
		}{conn, conn, conn, conn, conn, conn}
	case i4 | i5:
		return struct {
			driver.Conn
			driver.NamedValueChecker
			driver.Pinger
		}{conn, conn, conn}
	case i0 | i4 | i5:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.NamedValueChecker
			driver.Pinger
		}{conn, conn, conn, conn}
	case i1 | i4 | i5:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.NamedValueChecker
			driver.Pinger
		}{conn, conn, conn, conn}
	case i0 | i1 | i4 | i5:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.NamedValueChecker
			driver.Pinger
		}{conn, conn, conn, conn, conn}
	case i2 | i4 | i5:
		return struct {
			driver.Conn
			driver.Execer
			driver.NamedValueChecker
			driver.Pinger
		}{conn, conn, conn, conn}
	case i0 | i2 | i4 | i5:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.NamedValueChecker
			driver.Pinger
		}{conn, conn, conn, conn, conn}
	case i1 | i2 | i4 | i5:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.NamedValueChecker
			driver.Pinger
		}{conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i4 | i5:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.NamedValueChecker
			driver.Pinger
		}{conn, conn, conn, conn, conn, conn}
	case i3 | i4 | i5:
		return struct {
			driver.Conn
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
		}{conn, conn, conn, conn}
	case i0 | i3 | i4 | i5:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
		}{conn, conn, conn, conn, conn}
	case i1 | i3 | i4 | i5:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
		}{conn, conn, conn, conn, conn}
	case i0 | i1 | i3 | i4 | i5:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
		}{conn, conn, conn, conn, conn, conn}
	case i2 | i3 | i4 | i5:
		return struct {
			driver.Conn
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
		}{conn, conn, conn, conn, conn}
	case i0 | i2 | i3 | i4 | i5:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
		}{conn, conn, conn, conn, conn, conn}
	case i1 | i2 | i3 | i4 | i5:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
		}{conn, conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i3 | i4 | i5:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
		}{conn, conn, conn, conn, conn, conn, conn}
	case i6:
		return struct {
			driver.Conn
			driver.Queryer
		}{conn, conn}
	case i0 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Queryer
		}{conn, conn, conn}
	case i1 | i6:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Queryer
		}{conn, conn, conn}
	case i0 | i1 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Queryer
		}{conn, conn, conn, conn}
	case i2 | i6:
		return struct {
			driver.Conn
			driver.Execer
			driver.Queryer
		}{conn, conn, conn}
	case i0 | i2 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.Queryer
		}{conn, conn, conn, conn}
	case i1 | i2 | i6:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.Queryer
		}{conn, conn, conn, conn}
	case i0 | i1 | i2 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.Queryer
		}{conn, conn, conn, conn, conn}
	case i3 | i6:
		return struct {
			driver.Conn
			driver.ExecerContext
			driver.Queryer
		}{conn, conn, conn}
	case i0 | i3 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ExecerContext
			driver.Queryer
		}{conn, conn, conn, conn}
	case i1 | i3 | i6:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.Queryer
		}{conn, conn, conn, conn}
	case i0 | i1 | i3 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.Queryer
		}{conn, conn, conn, conn, conn}
	case i2 | i3 | i6:
		return struct {
			driver.Conn
			driver.Execer
			driver.ExecerContext
			driver.Queryer
		}{conn, conn, conn, conn}
	case i0 | i2 | i3 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.ExecerContext
			driver.Queryer
		}{conn, conn, conn, conn, conn}
	case i1 | i2 | i3 | i6:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.Queryer
		}{conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i3 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.Queryer
		}{conn, conn, conn, conn, conn, conn}
	case i4 | i6:
		return struct {
			driver.Conn
			driver.NamedValueChecker
			driver.Queryer
		}{conn, conn, conn}
	case i0 | i4 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.NamedValueChecker
			driver.Queryer
		}{conn, conn, conn, conn}
	case i1 | i4 | i6:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.NamedValueChecker
			driver.Queryer
		}{conn, conn, conn, conn}
	case i0 | i1 | i4 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.NamedValueChecker
			driver.Queryer
		}{conn, conn, conn, conn, conn}
	case i2 | i4 | i6:
		return struct {
			driver.Conn
			driver.Execer
			driver.NamedValueChecker
			driver.Queryer
		}{conn, conn, conn, conn}
	case i0 | i2 | i4 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.NamedValueChecker
			driver.Queryer
		}{conn, conn, conn, conn, conn}
	case i1 | i2 | i4 | i6:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.NamedValueChecker
			driver.Queryer
		}{conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i4 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.NamedValueChecker
			driver.Queryer
		}{conn, conn, conn, conn, conn, conn}
	case i3 | i4 | i6:
		return struct {
			driver.Conn
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Queryer
		}{conn, conn, conn, conn}
	case i0 | i3 | i4 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Queryer
		}{conn, conn, conn, conn, conn}
	case i1 | i3 | i4 | i6:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Queryer
		}{conn, conn, conn, conn, conn}
	case i0 | i1 | i3 | i4 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Queryer
		}{conn, conn, conn, conn, conn, conn}
	case i2 | i3 | i4 | i6:
		return struct {
			driver.Conn
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Queryer
		}{conn, conn, conn, conn, conn}
	case i0 | i2 | i3 | i4 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Queryer
		}{conn, conn, conn, conn, conn, conn}
	case i1 | i2 | i3 | i4 | i6:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Queryer
		}{conn, conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i3 | i4 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Queryer
		}{conn, conn, conn, conn, conn, conn, conn}
	case i5 | i6:
		return struct {
			driver.Conn
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn}
	case i0 | i5 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn}
	case i1 | i5 | i6:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn}
	case i0 | i1 | i5 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn}
	case i2 | i5 | i6:
		return struct {
			driver.Conn
			driver.Execer
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn}
	case i0 | i2 | i5 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn}
	case i1 | i2 | i5 | i6:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i5 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn, conn}
	case i3 | i5 | i6:
		return struct {
			driver.Conn
			driver.ExecerContext
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn}
	case i0 | i3 | i5 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ExecerContext
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn}
	case i1 | i3 | i5 | i6:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn}
	case i0 | i1 | i3 | i5 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn, conn}
	case i2 | i3 | i5 | i6:
		return struct {
			driver.Conn
			driver.Execer
			driver.ExecerContext
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn}
	case i0 | i2 | i3 | i5 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.ExecerContext
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn, conn}
	case i1 | i2 | i3 | i5 | i6:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i3 | i5 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn, conn, conn}
	case i4 | i5 | i6:
		return struct {
			driver.Conn
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn}
	case i0 | i4 | i5 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn}
	case i1 | i4 | i5 | i6:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn}
	case i0 | i1 | i4 | i5 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn, conn}
	case i2 | i4 | i5 | i6:
		return struct {
			driver.Conn
			driver.Execer
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn}
	case i0 | i2 | i4 | i5 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn, conn}
	case i1 | i2 | i4 | i5 | i6:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i4 | i5 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn, conn, conn}
	case i3 | i4 | i5 | i6:
		return struct {
			driver.Conn
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn}
	case i0 | i3 | i4 | i5 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn, conn}
	case i1 | i3 | i4 | i5 | i6:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn, conn}
	case i0 | i1 | i3 | i4 | i5 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn, conn, conn}
	case i2 | i3 | i4 | i5 | i6:
		return struct {
			driver.Conn
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn, conn}
	case i0 | i2 | i3 | i4 | i5 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn, conn, conn}
	case i1 | i2 | i3 | i4 | i5 | i6:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i3 | i4 | i5 | i6:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
		}{conn, conn, conn, conn, conn, conn, conn, conn}
	case i7:
		return struct {
			driver.Conn
			driver.QueryerContext
		}{conn, conn}
	case i0 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.QueryerContext
		}{conn, conn, conn}
	case i1 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.QueryerContext
		}{conn, conn, conn}
	case i0 | i1 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.QueryerContext
		}{conn, conn, conn, conn}
	case i2 | i7:
		return struct {
			driver.Conn
			driver.Execer
			driver.QueryerContext
		}{conn, conn, conn}
	case i0 | i2 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.QueryerContext
		}{conn, conn, conn, conn}
	case i1 | i2 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.QueryerContext
		}{conn, conn, conn, conn}
	case i0 | i1 | i2 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i3 | i7:
		return struct {
			driver.Conn
			driver.ExecerContext
			driver.QueryerContext
		}{conn, conn, conn}
	case i0 | i3 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ExecerContext
			driver.QueryerContext
		}{conn, conn, conn, conn}
	case i1 | i3 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.QueryerContext
		}{conn, conn, conn, conn}
	case i0 | i1 | i3 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i2 | i3 | i7:
		return struct {
			driver.Conn
			driver.Execer
			driver.ExecerContext
			driver.QueryerContext
		}{conn, conn, conn, conn}
	case i0 | i2 | i3 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.ExecerContext
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i1 | i2 | i3 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i3 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i4 | i7:
		return struct {
			driver.Conn
			driver.NamedValueChecker
			driver.QueryerContext
		}{conn, conn, conn}
	case i0 | i4 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.NamedValueChecker
			driver.QueryerContext
		}{conn, conn, conn, conn}
	case i1 | i4 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.NamedValueChecker
			driver.QueryerContext
		}{conn, conn, conn, conn}
	case i0 | i1 | i4 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.NamedValueChecker
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i2 | i4 | i7:
		return struct {
			driver.Conn
			driver.Execer
			driver.NamedValueChecker
			driver.QueryerContext
		}{conn, conn, conn, conn}
	case i0 | i2 | i4 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.NamedValueChecker
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i1 | i2 | i4 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.NamedValueChecker
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i4 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.NamedValueChecker
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i3 | i4 | i7:
		return struct {
			driver.Conn
			driver.ExecerContext
			driver.NamedValueChecker
			driver.QueryerContext
		}{conn, conn, conn, conn}
	case i0 | i3 | i4 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ExecerContext
			driver.NamedValueChecker
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i1 | i3 | i4 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.NamedValueChecker
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i0 | i1 | i3 | i4 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.NamedValueChecker
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i2 | i3 | i4 | i7:
		return struct {
			driver.Conn
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i0 | i2 | i3 | i4 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i1 | i2 | i3 | i4 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i3 | i4 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn}
	case i5 | i7:
		return struct {
			driver.Conn
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn}
	case i0 | i5 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn}
	case i1 | i5 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn}
	case i0 | i1 | i5 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i2 | i5 | i7:
		return struct {
			driver.Conn
			driver.Execer
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn}
	case i0 | i2 | i5 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i1 | i2 | i5 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i5 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i3 | i5 | i7:
		return struct {
			driver.Conn
			driver.ExecerContext
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn}
	case i0 | i3 | i5 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ExecerContext
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i1 | i3 | i5 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i0 | i1 | i3 | i5 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i2 | i3 | i5 | i7:
		return struct {
			driver.Conn
			driver.Execer
			driver.ExecerContext
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i0 | i2 | i3 | i5 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.ExecerContext
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i1 | i2 | i3 | i5 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i3 | i5 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn}
	case i4 | i5 | i7:
		return struct {
			driver.Conn
			driver.NamedValueChecker
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn}
	case i0 | i4 | i5 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.NamedValueChecker
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i1 | i4 | i5 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.NamedValueChecker
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i0 | i1 | i4 | i5 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.NamedValueChecker
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i2 | i4 | i5 | i7:
		return struct {
			driver.Conn
			driver.Execer
			driver.NamedValueChecker
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i0 | i2 | i4 | i5 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.NamedValueChecker
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i1 | i2 | i4 | i5 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.NamedValueChecker
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i4 | i5 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.NamedValueChecker
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn}
	case i3 | i4 | i5 | i7:
		return struct {
			driver.Conn
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i0 | i3 | i4 | i5 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i1 | i3 | i4 | i5 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i0 | i1 | i3 | i4 | i5 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn}
	case i2 | i3 | i4 | i5 | i7:
		return struct {
			driver.Conn
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i0 | i2 | i3 | i4 | i5 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn}
	case i1 | i2 | i3 | i4 | i5 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i3 | i4 | i5 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn, conn}
	case i6 | i7:
		return struct {
			driver.Conn
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn}
	case i0 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn}
	case i1 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn}
	case i0 | i1 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i2 | i6 | i7:
		return struct {
			driver.Conn
			driver.Execer
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn}
	case i0 | i2 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i1 | i2 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i3 | i6 | i7:
		return struct {
			driver.Conn
			driver.ExecerContext
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn}
	case i0 | i3 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ExecerContext
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i1 | i3 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i0 | i1 | i3 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i2 | i3 | i6 | i7:
		return struct {
			driver.Conn
			driver.Execer
			driver.ExecerContext
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i0 | i2 | i3 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.ExecerContext
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i1 | i2 | i3 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i3 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn}
	case i4 | i6 | i7:
		return struct {
			driver.Conn
			driver.NamedValueChecker
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn}
	case i0 | i4 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.NamedValueChecker
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i1 | i4 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.NamedValueChecker
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i0 | i1 | i4 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.NamedValueChecker
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i2 | i4 | i6 | i7:
		return struct {
			driver.Conn
			driver.Execer
			driver.NamedValueChecker
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i0 | i2 | i4 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.NamedValueChecker
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i1 | i2 | i4 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.NamedValueChecker
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i4 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.NamedValueChecker
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn}
	case i3 | i4 | i6 | i7:
		return struct {
			driver.Conn
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i0 | i3 | i4 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i1 | i3 | i4 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i0 | i1 | i3 | i4 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn}
	case i2 | i3 | i4 | i6 | i7:
		return struct {
			driver.Conn
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i0 | i2 | i3 | i4 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn}
	case i1 | i2 | i3 | i4 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i3 | i4 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn, conn}
	case i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn}
	case i0 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i1 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i0 | i1 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i2 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.Execer
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i0 | i2 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i1 | i2 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn}
	case i3 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ExecerContext
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i0 | i3 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ExecerContext
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i1 | i3 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i0 | i1 | i3 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn}
	case i2 | i3 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.Execer
			driver.ExecerContext
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i0 | i2 | i3 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.ExecerContext
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn}
	case i1 | i2 | i3 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i3 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn, conn}
	case i4 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn}
	case i0 | i4 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i1 | i4 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i0 | i1 | i4 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn}
	case i2 | i4 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.Execer
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i0 | i2 | i4 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn}
	case i1 | i2 | i4 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i4 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn, conn}
	case i3 | i4 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn}
	case i0 | i3 | i4 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn}
	case i1 | i3 | i4 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn}
	case i0 | i1 | i3 | i4 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn, conn}
	case i2 | i3 | i4 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn}
	case i0 | i2 | i3 | i4 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn, conn}
	case i1 | i2 | i3 | i4 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn, conn}
	case i0 | i1 | i2 | i3 | i4 | i5 | i6 | i7:
		return struct {
			driver.Conn
			driver.ConnBeginTx
			driver.ConnPrepareContext
			driver.Execer
			driver.ExecerContext
			driver.NamedValueChecker
			driver.Pinger
			driver.Queryer
			driver.QueryerContext
		}{conn, conn, conn, conn, conn, conn, conn, conn, conn}
	}
}
