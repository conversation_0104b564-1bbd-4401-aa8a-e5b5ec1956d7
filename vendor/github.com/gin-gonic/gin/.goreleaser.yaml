project_name: gin

builds:
  - # If true, skip the build.
    # Useful for library projects.
    # Default is false
    skip: true

changelog:
  # Set it to true if you wish to skip the changelog generation.
  # This may result in an empty release notes on GitHub/GitLab/Gitea.
  disable: false

  # Changelog generation implementation to use.
  #
  # Valid options are:
  # - `git`: uses `git log`;
  # - `github`: uses the compare GitHub API, appending the author login to the changelog.
  # - `gitlab`: uses the compare GitLab API, appending the author name and email to the changelog.
  # - `github-native`: uses the GitHub release notes generation API, disables the groups feature.
  #
  # Defaults to `git`.
  use: github

  # Sorts the changelog by the commit's messages.
  # Could either be asc, desc or empty
  # Default is empty
  sort: asc

  # Group commits messages by given regex and title.
  # Order value defines the order of the groups.
  # Proving no regex means all commits will be grouped under the default group.
  # Groups are disabled when using github-native, as it already groups things by itself.
  #
  # Default is no groups.
  groups:
    - title: Features
      regexp: "^.*feat[(\\w)]*:+.*$"
      order: 0
    - title: "Bug fixes"
      regexp: "^.*fix[(\\w)]*:+.*$"
      order: 1
    - title: "Enhancements"
      regexp: "^.*chore[(\\w)]*:+.*$"
      order: 2
    - title: "Refactor"
      regexp: "^.*refactor[(\\w)]*:+.*$"
      order: 3
    - title: "Build process updates"
      regexp: ^.*?(build|ci)(\(.+\))??!?:.+$
      order: 4
    - title: "Documentation updates"
      regexp: ^.*?docs?(\(.+\))??!?:.+$
      order: 4
    - title: Others
      order: 999
