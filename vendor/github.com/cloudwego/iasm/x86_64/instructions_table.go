//
// Copyright 2024 CloudWeGo Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

// Code generated by "mkasm_amd64.py", DO NOT EDIT.

package x86_64

const (
    _N_args  = 5
    _N_forms = 23
)

// Instructions maps all the instruction name to it's encoder function.
var Instructions = map[string]_InstructionEncoder {
    "adcb"             : __asm_proxy_ADCB__,
    "adcl"             : __asm_proxy_ADCL__,
    "adcq"             : __asm_proxy_ADCQ__,
    "adcw"             : __asm_proxy_ADCW__,
    "adcxl"            : __asm_proxy_ADCXL__,
    "adcxq"            : __asm_proxy_ADCXQ__,
    "addb"             : __asm_proxy_ADDB__,
    "addl"             : __asm_proxy_ADDL__,
    "addpd"            : __asm_proxy_ADDPD__,
    "addps"            : __asm_proxy_ADDPS__,
    "addq"             : __asm_proxy_ADDQ__,
    "addsd"            : __asm_proxy_ADDSD__,
    "addss"            : __asm_proxy_ADDSS__,
    "addsubpd"         : __asm_proxy_ADDSUBPD__,
    "addsubps"         : __asm_proxy_ADDSUBPS__,
    "addw"             : __asm_proxy_ADDW__,
    "adoxl"            : __asm_proxy_ADOXL__,
    "adoxq"            : __asm_proxy_ADOXQ__,
    "aesdec"           : __asm_proxy_AESDEC__,
    "aesdeclast"       : __asm_proxy_AESDECLAST__,
    "aesenc"           : __asm_proxy_AESENC__,
    "aesenclast"       : __asm_proxy_AESENCLAST__,
    "aesimc"           : __asm_proxy_AESIMC__,
    "aeskeygenassist"  : __asm_proxy_AESKEYGENASSIST__,
    "andb"             : __asm_proxy_ANDB__,
    "andl"             : __asm_proxy_ANDL__,
    "andnl"            : __asm_proxy_ANDNL__,
    "andnpd"           : __asm_proxy_ANDNPD__,
    "andnps"           : __asm_proxy_ANDNPS__,
    "andnq"            : __asm_proxy_ANDNQ__,
    "andpd"            : __asm_proxy_ANDPD__,
    "andps"            : __asm_proxy_ANDPS__,
    "andq"             : __asm_proxy_ANDQ__,
    "andw"             : __asm_proxy_ANDW__,
    "bextr"            : __asm_proxy_BEXTR__,
    "blcfill"          : __asm_proxy_BLCFILL__,
    "blci"             : __asm_proxy_BLCI__,
    "blcic"            : __asm_proxy_BLCIC__,
    "blcmsk"           : __asm_proxy_BLCMSK__,
    "blcs"             : __asm_proxy_BLCS__,
    "blendpd"          : __asm_proxy_BLENDPD__,
    "blendps"          : __asm_proxy_BLENDPS__,
    "blendvpd"         : __asm_proxy_BLENDVPD__,
    "blendvps"         : __asm_proxy_BLENDVPS__,
    "blsfill"          : __asm_proxy_BLSFILL__,
    "blsi"             : __asm_proxy_BLSI__,
    "blsic"            : __asm_proxy_BLSIC__,
    "blsmsk"           : __asm_proxy_BLSMSK__,
    "blsr"             : __asm_proxy_BLSR__,
    "bsfl"             : __asm_proxy_BSFL__,
    "bsfq"             : __asm_proxy_BSFQ__,
    "bsfw"             : __asm_proxy_BSFW__,
    "bsrl"             : __asm_proxy_BSRL__,
    "bsrq"             : __asm_proxy_BSRQ__,
    "bsrw"             : __asm_proxy_BSRW__,
    "bswapl"           : __asm_proxy_BSWAPL__,
    "bswapq"           : __asm_proxy_BSWAPQ__,
    "btcl"             : __asm_proxy_BTCL__,
    "btcq"             : __asm_proxy_BTCQ__,
    "btcw"             : __asm_proxy_BTCW__,
    "btl"              : __asm_proxy_BTL__,
    "btq"              : __asm_proxy_BTQ__,
    "btrl"             : __asm_proxy_BTRL__,
    "btrq"             : __asm_proxy_BTRQ__,
    "btrw"             : __asm_proxy_BTRW__,
    "btsl"             : __asm_proxy_BTSL__,
    "btsq"             : __asm_proxy_BTSQ__,
    "btsw"             : __asm_proxy_BTSW__,
    "btw"              : __asm_proxy_BTW__,
    "bzhi"             : __asm_proxy_BZHI__,
    "call"             : __asm_proxy_CALL__,
    "callq"            : __asm_proxy_CALLQ__,
    "cbtw"             : __asm_proxy_CBTW__,
    "clc"              : __asm_proxy_CLC__,
    "cld"              : __asm_proxy_CLD__,
    "clflush"          : __asm_proxy_CLFLUSH__,
    "clflushopt"       : __asm_proxy_CLFLUSHOPT__,
    "cltd"             : __asm_proxy_CLTD__,
    "cltq"             : __asm_proxy_CLTQ__,
    "clwb"             : __asm_proxy_CLWB__,
    "clzero"           : __asm_proxy_CLZERO__,
    "cmc"              : __asm_proxy_CMC__,
    "cmova"            : __asm_proxy_CMOVA__,
    "cmovae"           : __asm_proxy_CMOVAE__,
    "cmovb"            : __asm_proxy_CMOVB__,
    "cmovbe"           : __asm_proxy_CMOVBE__,
    "cmovc"            : __asm_proxy_CMOVC__,
    "cmove"            : __asm_proxy_CMOVE__,
    "cmovg"            : __asm_proxy_CMOVG__,
    "cmovge"           : __asm_proxy_CMOVGE__,
    "cmovl"            : __asm_proxy_CMOVL__,
    "cmovle"           : __asm_proxy_CMOVLE__,
    "cmovna"           : __asm_proxy_CMOVNA__,
    "cmovnae"          : __asm_proxy_CMOVNAE__,
    "cmovnb"           : __asm_proxy_CMOVNB__,
    "cmovnbe"          : __asm_proxy_CMOVNBE__,
    "cmovnc"           : __asm_proxy_CMOVNC__,
    "cmovne"           : __asm_proxy_CMOVNE__,
    "cmovng"           : __asm_proxy_CMOVNG__,
    "cmovnge"          : __asm_proxy_CMOVNGE__,
    "cmovnl"           : __asm_proxy_CMOVNL__,
    "cmovnle"          : __asm_proxy_CMOVNLE__,
    "cmovno"           : __asm_proxy_CMOVNO__,
    "cmovnp"           : __asm_proxy_CMOVNP__,
    "cmovns"           : __asm_proxy_CMOVNS__,
    "cmovnz"           : __asm_proxy_CMOVNZ__,
    "cmovo"            : __asm_proxy_CMOVO__,
    "cmovp"            : __asm_proxy_CMOVP__,
    "cmovpe"           : __asm_proxy_CMOVPE__,
    "cmovpo"           : __asm_proxy_CMOVPO__,
    "cmovs"            : __asm_proxy_CMOVS__,
    "cmovz"            : __asm_proxy_CMOVZ__,
    "cmpb"             : __asm_proxy_CMPB__,
    "cmpl"             : __asm_proxy_CMPL__,
    "cmppd"            : __asm_proxy_CMPPD__,
    "cmpps"            : __asm_proxy_CMPPS__,
    "cmpq"             : __asm_proxy_CMPQ__,
    "cmpsd"            : __asm_proxy_CMPSD__,
    "cmpss"            : __asm_proxy_CMPSS__,
    "cmpw"             : __asm_proxy_CMPW__,
    "cmpxchg16b"       : __asm_proxy_CMPXCHG16B__,
    "cmpxchg8b"        : __asm_proxy_CMPXCHG8B__,
    "cmpxchgb"         : __asm_proxy_CMPXCHGB__,
    "cmpxchgl"         : __asm_proxy_CMPXCHGL__,
    "cmpxchgq"         : __asm_proxy_CMPXCHGQ__,
    "cmpxchgw"         : __asm_proxy_CMPXCHGW__,
    "comisd"           : __asm_proxy_COMISD__,
    "comiss"           : __asm_proxy_COMISS__,
    "cpuid"            : __asm_proxy_CPUID__,
    "cqto"             : __asm_proxy_CQTO__,
    "crc32b"           : __asm_proxy_CRC32B__,
    "crc32l"           : __asm_proxy_CRC32L__,
    "crc32q"           : __asm_proxy_CRC32Q__,
    "crc32w"           : __asm_proxy_CRC32W__,
    "cvtdq2pd"         : __asm_proxy_CVTDQ2PD__,
    "cvtdq2ps"         : __asm_proxy_CVTDQ2PS__,
    "cvtpd2dq"         : __asm_proxy_CVTPD2DQ__,
    "cvtpd2pi"         : __asm_proxy_CVTPD2PI__,
    "cvtpd2ps"         : __asm_proxy_CVTPD2PS__,
    "cvtpi2pd"         : __asm_proxy_CVTPI2PD__,
    "cvtpi2ps"         : __asm_proxy_CVTPI2PS__,
    "cvtps2dq"         : __asm_proxy_CVTPS2DQ__,
    "cvtps2pd"         : __asm_proxy_CVTPS2PD__,
    "cvtps2pi"         : __asm_proxy_CVTPS2PI__,
    "cvtsd2si"         : __asm_proxy_CVTSD2SI__,
    "cvtsd2ss"         : __asm_proxy_CVTSD2SS__,
    "cvtsi2sd"         : __asm_proxy_CVTSI2SD__,
    "cvtsi2ss"         : __asm_proxy_CVTSI2SS__,
    "cvtss2sd"         : __asm_proxy_CVTSS2SD__,
    "cvtss2si"         : __asm_proxy_CVTSS2SI__,
    "cvttpd2dq"        : __asm_proxy_CVTTPD2DQ__,
    "cvttpd2pi"        : __asm_proxy_CVTTPD2PI__,
    "cvttps2dq"        : __asm_proxy_CVTTPS2DQ__,
    "cvttps2pi"        : __asm_proxy_CVTTPS2PI__,
    "cvttsd2si"        : __asm_proxy_CVTTSD2SI__,
    "cvttss2si"        : __asm_proxy_CVTTSS2SI__,
    "cwtd"             : __asm_proxy_CWTD__,
    "cwtl"             : __asm_proxy_CWTL__,
    "decb"             : __asm_proxy_DECB__,
    "decl"             : __asm_proxy_DECL__,
    "decq"             : __asm_proxy_DECQ__,
    "decw"             : __asm_proxy_DECW__,
    "divb"             : __asm_proxy_DIVB__,
    "divl"             : __asm_proxy_DIVL__,
    "divpd"            : __asm_proxy_DIVPD__,
    "divps"            : __asm_proxy_DIVPS__,
    "divq"             : __asm_proxy_DIVQ__,
    "divsd"            : __asm_proxy_DIVSD__,
    "divss"            : __asm_proxy_DIVSS__,
    "divw"             : __asm_proxy_DIVW__,
    "dppd"             : __asm_proxy_DPPD__,
    "dpps"             : __asm_proxy_DPPS__,
    "emms"             : __asm_proxy_EMMS__,
    "extractps"        : __asm_proxy_EXTRACTPS__,
    "extrq"            : __asm_proxy_EXTRQ__,
    "femms"            : __asm_proxy_FEMMS__,
    "haddpd"           : __asm_proxy_HADDPD__,
    "haddps"           : __asm_proxy_HADDPS__,
    "hsubpd"           : __asm_proxy_HSUBPD__,
    "hsubps"           : __asm_proxy_HSUBPS__,
    "idivb"            : __asm_proxy_IDIVB__,
    "idivl"            : __asm_proxy_IDIVL__,
    "idivq"            : __asm_proxy_IDIVQ__,
    "idivw"            : __asm_proxy_IDIVW__,
    "imulb"            : __asm_proxy_IMULB__,
    "imull"            : __asm_proxy_IMULL__,
    "imulq"            : __asm_proxy_IMULQ__,
    "imulw"            : __asm_proxy_IMULW__,
    "incb"             : __asm_proxy_INCB__,
    "incl"             : __asm_proxy_INCL__,
    "incq"             : __asm_proxy_INCQ__,
    "incw"             : __asm_proxy_INCW__,
    "insertps"         : __asm_proxy_INSERTPS__,
    "insertq"          : __asm_proxy_INSERTQ__,
    "int"              : __asm_proxy_INT__,
    "ja"               : __asm_proxy_JA__,
    "jae"              : __asm_proxy_JAE__,
    "jb"               : __asm_proxy_JB__,
    "jbe"              : __asm_proxy_JBE__,
    "jc"               : __asm_proxy_JC__,
    "je"               : __asm_proxy_JE__,
    "jecxz"            : __asm_proxy_JECXZ__,
    "jg"               : __asm_proxy_JG__,
    "jge"              : __asm_proxy_JGE__,
    "jl"               : __asm_proxy_JL__,
    "jle"              : __asm_proxy_JLE__,
    "jmp"              : __asm_proxy_JMP__,
    "jmpq"             : __asm_proxy_JMPQ__,
    "jna"              : __asm_proxy_JNA__,
    "jnae"             : __asm_proxy_JNAE__,
    "jnb"              : __asm_proxy_JNB__,
    "jnbe"             : __asm_proxy_JNBE__,
    "jnc"              : __asm_proxy_JNC__,
    "jne"              : __asm_proxy_JNE__,
    "jng"              : __asm_proxy_JNG__,
    "jnge"             : __asm_proxy_JNGE__,
    "jnl"              : __asm_proxy_JNL__,
    "jnle"             : __asm_proxy_JNLE__,
    "jno"              : __asm_proxy_JNO__,
    "jnp"              : __asm_proxy_JNP__,
    "jns"              : __asm_proxy_JNS__,
    "jnz"              : __asm_proxy_JNZ__,
    "jo"               : __asm_proxy_JO__,
    "jp"               : __asm_proxy_JP__,
    "jpe"              : __asm_proxy_JPE__,
    "jpo"              : __asm_proxy_JPO__,
    "jrcxz"            : __asm_proxy_JRCXZ__,
    "js"               : __asm_proxy_JS__,
    "jz"               : __asm_proxy_JZ__,
    "kaddb"            : __asm_proxy_KADDB__,
    "kaddd"            : __asm_proxy_KADDD__,
    "kaddq"            : __asm_proxy_KADDQ__,
    "kaddw"            : __asm_proxy_KADDW__,
    "kandb"            : __asm_proxy_KANDB__,
    "kandd"            : __asm_proxy_KANDD__,
    "kandnb"           : __asm_proxy_KANDNB__,
    "kandnd"           : __asm_proxy_KANDND__,
    "kandnq"           : __asm_proxy_KANDNQ__,
    "kandnw"           : __asm_proxy_KANDNW__,
    "kandq"            : __asm_proxy_KANDQ__,
    "kandw"            : __asm_proxy_KANDW__,
    "kmovb"            : __asm_proxy_KMOVB__,
    "kmovd"            : __asm_proxy_KMOVD__,
    "kmovq"            : __asm_proxy_KMOVQ__,
    "kmovw"            : __asm_proxy_KMOVW__,
    "knotb"            : __asm_proxy_KNOTB__,
    "knotd"            : __asm_proxy_KNOTD__,
    "knotq"            : __asm_proxy_KNOTQ__,
    "knotw"            : __asm_proxy_KNOTW__,
    "korb"             : __asm_proxy_KORB__,
    "kord"             : __asm_proxy_KORD__,
    "korq"             : __asm_proxy_KORQ__,
    "kortestb"         : __asm_proxy_KORTESTB__,
    "kortestd"         : __asm_proxy_KORTESTD__,
    "kortestq"         : __asm_proxy_KORTESTQ__,
    "kortestw"         : __asm_proxy_KORTESTW__,
    "korw"             : __asm_proxy_KORW__,
    "kshiftlb"         : __asm_proxy_KSHIFTLB__,
    "kshiftld"         : __asm_proxy_KSHIFTLD__,
    "kshiftlq"         : __asm_proxy_KSHIFTLQ__,
    "kshiftlw"         : __asm_proxy_KSHIFTLW__,
    "kshiftrb"         : __asm_proxy_KSHIFTRB__,
    "kshiftrd"         : __asm_proxy_KSHIFTRD__,
    "kshiftrq"         : __asm_proxy_KSHIFTRQ__,
    "kshiftrw"         : __asm_proxy_KSHIFTRW__,
    "ktestb"           : __asm_proxy_KTESTB__,
    "ktestd"           : __asm_proxy_KTESTD__,
    "ktestq"           : __asm_proxy_KTESTQ__,
    "ktestw"           : __asm_proxy_KTESTW__,
    "kunpckbw"         : __asm_proxy_KUNPCKBW__,
    "kunpckdq"         : __asm_proxy_KUNPCKDQ__,
    "kunpckwd"         : __asm_proxy_KUNPCKWD__,
    "kxnorb"           : __asm_proxy_KXNORB__,
    "kxnord"           : __asm_proxy_KXNORD__,
    "kxnorq"           : __asm_proxy_KXNORQ__,
    "kxnorw"           : __asm_proxy_KXNORW__,
    "kxorb"            : __asm_proxy_KXORB__,
    "kxord"            : __asm_proxy_KXORD__,
    "kxorq"            : __asm_proxy_KXORQ__,
    "kxorw"            : __asm_proxy_KXORW__,
    "lddqu"            : __asm_proxy_LDDQU__,
    "ldmxcsr"          : __asm_proxy_LDMXCSR__,
    "leal"             : __asm_proxy_LEAL__,
    "leaq"             : __asm_proxy_LEAQ__,
    "leaw"             : __asm_proxy_LEAW__,
    "lfence"           : __asm_proxy_LFENCE__,
    "lzcntl"           : __asm_proxy_LZCNTL__,
    "lzcntq"           : __asm_proxy_LZCNTQ__,
    "lzcntw"           : __asm_proxy_LZCNTW__,
    "maskmovdqu"       : __asm_proxy_MASKMOVDQU__,
    "maskmovq"         : __asm_proxy_MASKMOVQ__,
    "maxpd"            : __asm_proxy_MAXPD__,
    "maxps"            : __asm_proxy_MAXPS__,
    "maxsd"            : __asm_proxy_MAXSD__,
    "maxss"            : __asm_proxy_MAXSS__,
    "mfence"           : __asm_proxy_MFENCE__,
    "minpd"            : __asm_proxy_MINPD__,
    "minps"            : __asm_proxy_MINPS__,
    "minsd"            : __asm_proxy_MINSD__,
    "minss"            : __asm_proxy_MINSS__,
    "monitor"          : __asm_proxy_MONITOR__,
    "monitorx"         : __asm_proxy_MONITORX__,
    "movapd"           : __asm_proxy_MOVAPD__,
    "movaps"           : __asm_proxy_MOVAPS__,
    "movb"             : __asm_proxy_MOVB__,
    "movbel"           : __asm_proxy_MOVBEL__,
    "movbeq"           : __asm_proxy_MOVBEQ__,
    "movbew"           : __asm_proxy_MOVBEW__,
    "movd"             : __asm_proxy_MOVD__,
    "movddup"          : __asm_proxy_MOVDDUP__,
    "movdq2q"          : __asm_proxy_MOVDQ2Q__,
    "movdqa"           : __asm_proxy_MOVDQA__,
    "movdqu"           : __asm_proxy_MOVDQU__,
    "movhlps"          : __asm_proxy_MOVHLPS__,
    "movhpd"           : __asm_proxy_MOVHPD__,
    "movhps"           : __asm_proxy_MOVHPS__,
    "movl"             : __asm_proxy_MOVL__,
    "movlhps"          : __asm_proxy_MOVLHPS__,
    "movlpd"           : __asm_proxy_MOVLPD__,
    "movlps"           : __asm_proxy_MOVLPS__,
    "movmskpd"         : __asm_proxy_MOVMSKPD__,
    "movmskps"         : __asm_proxy_MOVMSKPS__,
    "movntdq"          : __asm_proxy_MOVNTDQ__,
    "movntdqa"         : __asm_proxy_MOVNTDQA__,
    "movntil"          : __asm_proxy_MOVNTIL__,
    "movntiq"          : __asm_proxy_MOVNTIQ__,
    "movntpd"          : __asm_proxy_MOVNTPD__,
    "movntps"          : __asm_proxy_MOVNTPS__,
    "movntq"           : __asm_proxy_MOVNTQ__,
    "movntsd"          : __asm_proxy_MOVNTSD__,
    "movntss"          : __asm_proxy_MOVNTSS__,
    "movq"             : __asm_proxy_MOVQ__,
    "movq2dq"          : __asm_proxy_MOVQ2DQ__,
    "movsbl"           : __asm_proxy_MOVSBL__,
    "movsbq"           : __asm_proxy_MOVSBQ__,
    "movsbw"           : __asm_proxy_MOVSBW__,
    "movsd"            : __asm_proxy_MOVSD__,
    "movshdup"         : __asm_proxy_MOVSHDUP__,
    "movsldup"         : __asm_proxy_MOVSLDUP__,
    "movslq"           : __asm_proxy_MOVSLQ__,
    "movss"            : __asm_proxy_MOVSS__,
    "movswl"           : __asm_proxy_MOVSWL__,
    "movswq"           : __asm_proxy_MOVSWQ__,
    "movupd"           : __asm_proxy_MOVUPD__,
    "movups"           : __asm_proxy_MOVUPS__,
    "movw"             : __asm_proxy_MOVW__,
    "movzbl"           : __asm_proxy_MOVZBL__,
    "movzbq"           : __asm_proxy_MOVZBQ__,
    "movzbw"           : __asm_proxy_MOVZBW__,
    "movzwl"           : __asm_proxy_MOVZWL__,
    "movzwq"           : __asm_proxy_MOVZWQ__,
    "mpsadbw"          : __asm_proxy_MPSADBW__,
    "mulb"             : __asm_proxy_MULB__,
    "mull"             : __asm_proxy_MULL__,
    "mulpd"            : __asm_proxy_MULPD__,
    "mulps"            : __asm_proxy_MULPS__,
    "mulq"             : __asm_proxy_MULQ__,
    "mulsd"            : __asm_proxy_MULSD__,
    "mulss"            : __asm_proxy_MULSS__,
    "mulw"             : __asm_proxy_MULW__,
    "mulxl"            : __asm_proxy_MULXL__,
    "mulxq"            : __asm_proxy_MULXQ__,
    "mwait"            : __asm_proxy_MWAIT__,
    "mwaitx"           : __asm_proxy_MWAITX__,
    "negb"             : __asm_proxy_NEGB__,
    "negl"             : __asm_proxy_NEGL__,
    "negq"             : __asm_proxy_NEGQ__,
    "negw"             : __asm_proxy_NEGW__,
    "nop"              : __asm_proxy_NOP__,
    "notb"             : __asm_proxy_NOTB__,
    "notl"             : __asm_proxy_NOTL__,
    "notq"             : __asm_proxy_NOTQ__,
    "notw"             : __asm_proxy_NOTW__,
    "orb"              : __asm_proxy_ORB__,
    "orl"              : __asm_proxy_ORL__,
    "orpd"             : __asm_proxy_ORPD__,
    "orps"             : __asm_proxy_ORPS__,
    "orq"              : __asm_proxy_ORQ__,
    "orw"              : __asm_proxy_ORW__,
    "pabsb"            : __asm_proxy_PABSB__,
    "pabsd"            : __asm_proxy_PABSD__,
    "pabsw"            : __asm_proxy_PABSW__,
    "packssdw"         : __asm_proxy_PACKSSDW__,
    "packsswb"         : __asm_proxy_PACKSSWB__,
    "packusdw"         : __asm_proxy_PACKUSDW__,
    "packuswb"         : __asm_proxy_PACKUSWB__,
    "paddb"            : __asm_proxy_PADDB__,
    "paddd"            : __asm_proxy_PADDD__,
    "paddq"            : __asm_proxy_PADDQ__,
    "paddsb"           : __asm_proxy_PADDSB__,
    "paddsw"           : __asm_proxy_PADDSW__,
    "paddusb"          : __asm_proxy_PADDUSB__,
    "paddusw"          : __asm_proxy_PADDUSW__,
    "paddw"            : __asm_proxy_PADDW__,
    "palignr"          : __asm_proxy_PALIGNR__,
    "pand"             : __asm_proxy_PAND__,
    "pandn"            : __asm_proxy_PANDN__,
    "pause"            : __asm_proxy_PAUSE__,
    "pavgb"            : __asm_proxy_PAVGB__,
    "pavgusb"          : __asm_proxy_PAVGUSB__,
    "pavgw"            : __asm_proxy_PAVGW__,
    "pblendvb"         : __asm_proxy_PBLENDVB__,
    "pblendw"          : __asm_proxy_PBLENDW__,
    "pclmulqdq"        : __asm_proxy_PCLMULQDQ__,
    "pcmpeqb"          : __asm_proxy_PCMPEQB__,
    "pcmpeqd"          : __asm_proxy_PCMPEQD__,
    "pcmpeqq"          : __asm_proxy_PCMPEQQ__,
    "pcmpeqw"          : __asm_proxy_PCMPEQW__,
    "pcmpestri"        : __asm_proxy_PCMPESTRI__,
    "pcmpestrm"        : __asm_proxy_PCMPESTRM__,
    "pcmpgtb"          : __asm_proxy_PCMPGTB__,
    "pcmpgtd"          : __asm_proxy_PCMPGTD__,
    "pcmpgtq"          : __asm_proxy_PCMPGTQ__,
    "pcmpgtw"          : __asm_proxy_PCMPGTW__,
    "pcmpistri"        : __asm_proxy_PCMPISTRI__,
    "pcmpistrm"        : __asm_proxy_PCMPISTRM__,
    "pdep"             : __asm_proxy_PDEP__,
    "pext"             : __asm_proxy_PEXT__,
    "pextrb"           : __asm_proxy_PEXTRB__,
    "pextrd"           : __asm_proxy_PEXTRD__,
    "pextrq"           : __asm_proxy_PEXTRQ__,
    "pextrw"           : __asm_proxy_PEXTRW__,
    "pf2id"            : __asm_proxy_PF2ID__,
    "pf2iw"            : __asm_proxy_PF2IW__,
    "pfacc"            : __asm_proxy_PFACC__,
    "pfadd"            : __asm_proxy_PFADD__,
    "pfcmpeq"          : __asm_proxy_PFCMPEQ__,
    "pfcmpge"          : __asm_proxy_PFCMPGE__,
    "pfcmpgt"          : __asm_proxy_PFCMPGT__,
    "pfmax"            : __asm_proxy_PFMAX__,
    "pfmin"            : __asm_proxy_PFMIN__,
    "pfmul"            : __asm_proxy_PFMUL__,
    "pfnacc"           : __asm_proxy_PFNACC__,
    "pfpnacc"          : __asm_proxy_PFPNACC__,
    "pfrcp"            : __asm_proxy_PFRCP__,
    "pfrcpit1"         : __asm_proxy_PFRCPIT1__,
    "pfrcpit2"         : __asm_proxy_PFRCPIT2__,
    "pfrsqit1"         : __asm_proxy_PFRSQIT1__,
    "pfrsqrt"          : __asm_proxy_PFRSQRT__,
    "pfsub"            : __asm_proxy_PFSUB__,
    "pfsubr"           : __asm_proxy_PFSUBR__,
    "phaddd"           : __asm_proxy_PHADDD__,
    "phaddsw"          : __asm_proxy_PHADDSW__,
    "phaddw"           : __asm_proxy_PHADDW__,
    "phminposuw"       : __asm_proxy_PHMINPOSUW__,
    "phsubd"           : __asm_proxy_PHSUBD__,
    "phsubsw"          : __asm_proxy_PHSUBSW__,
    "phsubw"           : __asm_proxy_PHSUBW__,
    "pi2fd"            : __asm_proxy_PI2FD__,
    "pi2fw"            : __asm_proxy_PI2FW__,
    "pinsrb"           : __asm_proxy_PINSRB__,
    "pinsrd"           : __asm_proxy_PINSRD__,
    "pinsrq"           : __asm_proxy_PINSRQ__,
    "pinsrw"           : __asm_proxy_PINSRW__,
    "pmaddubsw"        : __asm_proxy_PMADDUBSW__,
    "pmaddwd"          : __asm_proxy_PMADDWD__,
    "pmaxsb"           : __asm_proxy_PMAXSB__,
    "pmaxsd"           : __asm_proxy_PMAXSD__,
    "pmaxsw"           : __asm_proxy_PMAXSW__,
    "pmaxub"           : __asm_proxy_PMAXUB__,
    "pmaxud"           : __asm_proxy_PMAXUD__,
    "pmaxuw"           : __asm_proxy_PMAXUW__,
    "pminsb"           : __asm_proxy_PMINSB__,
    "pminsd"           : __asm_proxy_PMINSD__,
    "pminsw"           : __asm_proxy_PMINSW__,
    "pminub"           : __asm_proxy_PMINUB__,
    "pminud"           : __asm_proxy_PMINUD__,
    "pminuw"           : __asm_proxy_PMINUW__,
    "pmovmskb"         : __asm_proxy_PMOVMSKB__,
    "pmovsxbd"         : __asm_proxy_PMOVSXBD__,
    "pmovsxbq"         : __asm_proxy_PMOVSXBQ__,
    "pmovsxbw"         : __asm_proxy_PMOVSXBW__,
    "pmovsxdq"         : __asm_proxy_PMOVSXDQ__,
    "pmovsxwd"         : __asm_proxy_PMOVSXWD__,
    "pmovsxwq"         : __asm_proxy_PMOVSXWQ__,
    "pmovzxbd"         : __asm_proxy_PMOVZXBD__,
    "pmovzxbq"         : __asm_proxy_PMOVZXBQ__,
    "pmovzxbw"         : __asm_proxy_PMOVZXBW__,
    "pmovzxdq"         : __asm_proxy_PMOVZXDQ__,
    "pmovzxwd"         : __asm_proxy_PMOVZXWD__,
    "pmovzxwq"         : __asm_proxy_PMOVZXWQ__,
    "pmuldq"           : __asm_proxy_PMULDQ__,
    "pmulhrsw"         : __asm_proxy_PMULHRSW__,
    "pmulhrw"          : __asm_proxy_PMULHRW__,
    "pmulhuw"          : __asm_proxy_PMULHUW__,
    "pmulhw"           : __asm_proxy_PMULHW__,
    "pmulld"           : __asm_proxy_PMULLD__,
    "pmullw"           : __asm_proxy_PMULLW__,
    "pmuludq"          : __asm_proxy_PMULUDQ__,
    "popcntl"          : __asm_proxy_POPCNTL__,
    "popcntq"          : __asm_proxy_POPCNTQ__,
    "popcntw"          : __asm_proxy_POPCNTW__,
    "popq"             : __asm_proxy_POPQ__,
    "popw"             : __asm_proxy_POPW__,
    "por"              : __asm_proxy_POR__,
    "prefetch"         : __asm_proxy_PREFETCH__,
    "prefetchnta"      : __asm_proxy_PREFETCHNTA__,
    "prefetcht0"       : __asm_proxy_PREFETCHT0__,
    "prefetcht1"       : __asm_proxy_PREFETCHT1__,
    "prefetcht2"       : __asm_proxy_PREFETCHT2__,
    "prefetchw"        : __asm_proxy_PREFETCHW__,
    "prefetchwt1"      : __asm_proxy_PREFETCHWT1__,
    "psadbw"           : __asm_proxy_PSADBW__,
    "pshufb"           : __asm_proxy_PSHUFB__,
    "pshufd"           : __asm_proxy_PSHUFD__,
    "pshufhw"          : __asm_proxy_PSHUFHW__,
    "pshuflw"          : __asm_proxy_PSHUFLW__,
    "pshufw"           : __asm_proxy_PSHUFW__,
    "psignb"           : __asm_proxy_PSIGNB__,
    "psignd"           : __asm_proxy_PSIGND__,
    "psignw"           : __asm_proxy_PSIGNW__,
    "pslld"            : __asm_proxy_PSLLD__,
    "pslldq"           : __asm_proxy_PSLLDQ__,
    "psllq"            : __asm_proxy_PSLLQ__,
    "psllw"            : __asm_proxy_PSLLW__,
    "psrad"            : __asm_proxy_PSRAD__,
    "psraw"            : __asm_proxy_PSRAW__,
    "psrld"            : __asm_proxy_PSRLD__,
    "psrldq"           : __asm_proxy_PSRLDQ__,
    "psrlq"            : __asm_proxy_PSRLQ__,
    "psrlw"            : __asm_proxy_PSRLW__,
    "psubb"            : __asm_proxy_PSUBB__,
    "psubd"            : __asm_proxy_PSUBD__,
    "psubq"            : __asm_proxy_PSUBQ__,
    "psubsb"           : __asm_proxy_PSUBSB__,
    "psubsw"           : __asm_proxy_PSUBSW__,
    "psubusb"          : __asm_proxy_PSUBUSB__,
    "psubusw"          : __asm_proxy_PSUBUSW__,
    "psubw"            : __asm_proxy_PSUBW__,
    "pswapd"           : __asm_proxy_PSWAPD__,
    "ptest"            : __asm_proxy_PTEST__,
    "punpckhbw"        : __asm_proxy_PUNPCKHBW__,
    "punpckhdq"        : __asm_proxy_PUNPCKHDQ__,
    "punpckhqdq"       : __asm_proxy_PUNPCKHQDQ__,
    "punpckhwd"        : __asm_proxy_PUNPCKHWD__,
    "punpcklbw"        : __asm_proxy_PUNPCKLBW__,
    "punpckldq"        : __asm_proxy_PUNPCKLDQ__,
    "punpcklqdq"       : __asm_proxy_PUNPCKLQDQ__,
    "punpcklwd"        : __asm_proxy_PUNPCKLWD__,
    "pushq"            : __asm_proxy_PUSHQ__,
    "pushw"            : __asm_proxy_PUSHW__,
    "pxor"             : __asm_proxy_PXOR__,
    "rclb"             : __asm_proxy_RCLB__,
    "rcll"             : __asm_proxy_RCLL__,
    "rclq"             : __asm_proxy_RCLQ__,
    "rclw"             : __asm_proxy_RCLW__,
    "rcpps"            : __asm_proxy_RCPPS__,
    "rcpss"            : __asm_proxy_RCPSS__,
    "rcrb"             : __asm_proxy_RCRB__,
    "rcrl"             : __asm_proxy_RCRL__,
    "rcrq"             : __asm_proxy_RCRQ__,
    "rcrw"             : __asm_proxy_RCRW__,
    "rdrand"           : __asm_proxy_RDRAND__,
    "rdseed"           : __asm_proxy_RDSEED__,
    "rdtsc"            : __asm_proxy_RDTSC__,
    "rdtscp"           : __asm_proxy_RDTSCP__,
    "ret"              : __asm_proxy_RET__,
    "rolb"             : __asm_proxy_ROLB__,
    "roll"             : __asm_proxy_ROLL__,
    "rolq"             : __asm_proxy_ROLQ__,
    "rolw"             : __asm_proxy_ROLW__,
    "rorb"             : __asm_proxy_RORB__,
    "rorl"             : __asm_proxy_RORL__,
    "rorq"             : __asm_proxy_RORQ__,
    "rorw"             : __asm_proxy_RORW__,
    "rorxl"            : __asm_proxy_RORXL__,
    "rorxq"            : __asm_proxy_RORXQ__,
    "roundpd"          : __asm_proxy_ROUNDPD__,
    "roundps"          : __asm_proxy_ROUNDPS__,
    "roundsd"          : __asm_proxy_ROUNDSD__,
    "roundss"          : __asm_proxy_ROUNDSS__,
    "rsqrtps"          : __asm_proxy_RSQRTPS__,
    "rsqrtss"          : __asm_proxy_RSQRTSS__,
    "salb"             : __asm_proxy_SALB__,
    "sall"             : __asm_proxy_SALL__,
    "salq"             : __asm_proxy_SALQ__,
    "salw"             : __asm_proxy_SALW__,
    "sarb"             : __asm_proxy_SARB__,
    "sarl"             : __asm_proxy_SARL__,
    "sarq"             : __asm_proxy_SARQ__,
    "sarw"             : __asm_proxy_SARW__,
    "sarxl"            : __asm_proxy_SARXL__,
    "sarxq"            : __asm_proxy_SARXQ__,
    "sbbb"             : __asm_proxy_SBBB__,
    "sbbl"             : __asm_proxy_SBBL__,
    "sbbq"             : __asm_proxy_SBBQ__,
    "sbbw"             : __asm_proxy_SBBW__,
    "seta"             : __asm_proxy_SETA__,
    "setae"            : __asm_proxy_SETAE__,
    "setb"             : __asm_proxy_SETB__,
    "setbe"            : __asm_proxy_SETBE__,
    "setc"             : __asm_proxy_SETC__,
    "sete"             : __asm_proxy_SETE__,
    "setg"             : __asm_proxy_SETG__,
    "setge"            : __asm_proxy_SETGE__,
    "setl"             : __asm_proxy_SETL__,
    "setle"            : __asm_proxy_SETLE__,
    "setna"            : __asm_proxy_SETNA__,
    "setnae"           : __asm_proxy_SETNAE__,
    "setnb"            : __asm_proxy_SETNB__,
    "setnbe"           : __asm_proxy_SETNBE__,
    "setnc"            : __asm_proxy_SETNC__,
    "setne"            : __asm_proxy_SETNE__,
    "setng"            : __asm_proxy_SETNG__,
    "setnge"           : __asm_proxy_SETNGE__,
    "setnl"            : __asm_proxy_SETNL__,
    "setnle"           : __asm_proxy_SETNLE__,
    "setno"            : __asm_proxy_SETNO__,
    "setnp"            : __asm_proxy_SETNP__,
    "setns"            : __asm_proxy_SETNS__,
    "setnz"            : __asm_proxy_SETNZ__,
    "seto"             : __asm_proxy_SETO__,
    "setp"             : __asm_proxy_SETP__,
    "setpe"            : __asm_proxy_SETPE__,
    "setpo"            : __asm_proxy_SETPO__,
    "sets"             : __asm_proxy_SETS__,
    "setz"             : __asm_proxy_SETZ__,
    "sfence"           : __asm_proxy_SFENCE__,
    "sha1msg1"         : __asm_proxy_SHA1MSG1__,
    "sha1msg2"         : __asm_proxy_SHA1MSG2__,
    "sha1nexte"        : __asm_proxy_SHA1NEXTE__,
    "sha1rnds4"        : __asm_proxy_SHA1RNDS4__,
    "sha256msg1"       : __asm_proxy_SHA256MSG1__,
    "sha256msg2"       : __asm_proxy_SHA256MSG2__,
    "sha256rnds2"      : __asm_proxy_SHA256RNDS2__,
    "shlb"             : __asm_proxy_SHLB__,
    "shldl"            : __asm_proxy_SHLDL__,
    "shldq"            : __asm_proxy_SHLDQ__,
    "shldw"            : __asm_proxy_SHLDW__,
    "shll"             : __asm_proxy_SHLL__,
    "shlq"             : __asm_proxy_SHLQ__,
    "shlw"             : __asm_proxy_SHLW__,
    "shlxl"            : __asm_proxy_SHLXL__,
    "shlxq"            : __asm_proxy_SHLXQ__,
    "shrb"             : __asm_proxy_SHRB__,
    "shrdl"            : __asm_proxy_SHRDL__,
    "shrdq"            : __asm_proxy_SHRDQ__,
    "shrdw"            : __asm_proxy_SHRDW__,
    "shrl"             : __asm_proxy_SHRL__,
    "shrq"             : __asm_proxy_SHRQ__,
    "shrw"             : __asm_proxy_SHRW__,
    "shrxl"            : __asm_proxy_SHRXL__,
    "shrxq"            : __asm_proxy_SHRXQ__,
    "shufpd"           : __asm_proxy_SHUFPD__,
    "shufps"           : __asm_proxy_SHUFPS__,
    "sqrtpd"           : __asm_proxy_SQRTPD__,
    "sqrtps"           : __asm_proxy_SQRTPS__,
    "sqrtsd"           : __asm_proxy_SQRTSD__,
    "sqrtss"           : __asm_proxy_SQRTSS__,
    "stc"              : __asm_proxy_STC__,
    "std"              : __asm_proxy_STD__,
    "stmxcsr"          : __asm_proxy_STMXCSR__,
    "subb"             : __asm_proxy_SUBB__,
    "subl"             : __asm_proxy_SUBL__,
    "subpd"            : __asm_proxy_SUBPD__,
    "subps"            : __asm_proxy_SUBPS__,
    "subq"             : __asm_proxy_SUBQ__,
    "subsd"            : __asm_proxy_SUBSD__,
    "subss"            : __asm_proxy_SUBSS__,
    "subw"             : __asm_proxy_SUBW__,
    "syscall"          : __asm_proxy_SYSCALL__,
    "t1mskc"           : __asm_proxy_T1MSKC__,
    "testb"            : __asm_proxy_TESTB__,
    "testl"            : __asm_proxy_TESTL__,
    "testq"            : __asm_proxy_TESTQ__,
    "testw"            : __asm_proxy_TESTW__,
    "tzcntl"           : __asm_proxy_TZCNTL__,
    "tzcntq"           : __asm_proxy_TZCNTQ__,
    "tzcntw"           : __asm_proxy_TZCNTW__,
    "tzmsk"            : __asm_proxy_TZMSK__,
    "ucomisd"          : __asm_proxy_UCOMISD__,
    "ucomiss"          : __asm_proxy_UCOMISS__,
    "ud2"              : __asm_proxy_UD2__,
    "unpckhpd"         : __asm_proxy_UNPCKHPD__,
    "unpckhps"         : __asm_proxy_UNPCKHPS__,
    "unpcklpd"         : __asm_proxy_UNPCKLPD__,
    "unpcklps"         : __asm_proxy_UNPCKLPS__,
    "vaddpd"           : __asm_proxy_VADDPD__,
    "vaddps"           : __asm_proxy_VADDPS__,
    "vaddsd"           : __asm_proxy_VADDSD__,
    "vaddss"           : __asm_proxy_VADDSS__,
    "vaddsubpd"        : __asm_proxy_VADDSUBPD__,
    "vaddsubps"        : __asm_proxy_VADDSUBPS__,
    "vaesdec"          : __asm_proxy_VAESDEC__,
    "vaesdeclast"      : __asm_proxy_VAESDECLAST__,
    "vaesenc"          : __asm_proxy_VAESENC__,
    "vaesenclast"      : __asm_proxy_VAESENCLAST__,
    "vaesimc"          : __asm_proxy_VAESIMC__,
    "vaeskeygenassist" : __asm_proxy_VAESKEYGENASSIST__,
    "valignd"          : __asm_proxy_VALIGND__,
    "valignq"          : __asm_proxy_VALIGNQ__,
    "vandnpd"          : __asm_proxy_VANDNPD__,
    "vandnps"          : __asm_proxy_VANDNPS__,
    "vandpd"           : __asm_proxy_VANDPD__,
    "vandps"           : __asm_proxy_VANDPS__,
    "vblendmpd"        : __asm_proxy_VBLENDMPD__,
    "vblendmps"        : __asm_proxy_VBLENDMPS__,
    "vblendpd"         : __asm_proxy_VBLENDPD__,
    "vblendps"         : __asm_proxy_VBLENDPS__,
    "vblendvpd"        : __asm_proxy_VBLENDVPD__,
    "vblendvps"        : __asm_proxy_VBLENDVPS__,
    "vbroadcastf128"   : __asm_proxy_VBROADCASTF128__,
    "vbroadcastf32x2"  : __asm_proxy_VBROADCASTF32X2__,
    "vbroadcastf32x4"  : __asm_proxy_VBROADCASTF32X4__,
    "vbroadcastf32x8"  : __asm_proxy_VBROADCASTF32X8__,
    "vbroadcastf64x2"  : __asm_proxy_VBROADCASTF64X2__,
    "vbroadcastf64x4"  : __asm_proxy_VBROADCASTF64X4__,
    "vbroadcasti128"   : __asm_proxy_VBROADCASTI128__,
    "vbroadcasti32x2"  : __asm_proxy_VBROADCASTI32X2__,
    "vbroadcasti32x4"  : __asm_proxy_VBROADCASTI32X4__,
    "vbroadcasti32x8"  : __asm_proxy_VBROADCASTI32X8__,
    "vbroadcasti64x2"  : __asm_proxy_VBROADCASTI64X2__,
    "vbroadcasti64x4"  : __asm_proxy_VBROADCASTI64X4__,
    "vbroadcastsd"     : __asm_proxy_VBROADCASTSD__,
    "vbroadcastss"     : __asm_proxy_VBROADCASTSS__,
    "vcmppd"           : __asm_proxy_VCMPPD__,
    "vcmpps"           : __asm_proxy_VCMPPS__,
    "vcmpsd"           : __asm_proxy_VCMPSD__,
    "vcmpss"           : __asm_proxy_VCMPSS__,
    "vcomisd"          : __asm_proxy_VCOMISD__,
    "vcomiss"          : __asm_proxy_VCOMISS__,
    "vcompresspd"      : __asm_proxy_VCOMPRESSPD__,
    "vcompressps"      : __asm_proxy_VCOMPRESSPS__,
    "vcvtdq2pd"        : __asm_proxy_VCVTDQ2PD__,
    "vcvtdq2ps"        : __asm_proxy_VCVTDQ2PS__,
    "vcvtpd2dq"        : __asm_proxy_VCVTPD2DQ__,
    "vcvtpd2ps"        : __asm_proxy_VCVTPD2PS__,
    "vcvtpd2qq"        : __asm_proxy_VCVTPD2QQ__,
    "vcvtpd2udq"       : __asm_proxy_VCVTPD2UDQ__,
    "vcvtpd2uqq"       : __asm_proxy_VCVTPD2UQQ__,
    "vcvtph2ps"        : __asm_proxy_VCVTPH2PS__,
    "vcvtps2dq"        : __asm_proxy_VCVTPS2DQ__,
    "vcvtps2pd"        : __asm_proxy_VCVTPS2PD__,
    "vcvtps2ph"        : __asm_proxy_VCVTPS2PH__,
    "vcvtps2qq"        : __asm_proxy_VCVTPS2QQ__,
    "vcvtps2udq"       : __asm_proxy_VCVTPS2UDQ__,
    "vcvtps2uqq"       : __asm_proxy_VCVTPS2UQQ__,
    "vcvtqq2pd"        : __asm_proxy_VCVTQQ2PD__,
    "vcvtqq2ps"        : __asm_proxy_VCVTQQ2PS__,
    "vcvtsd2si"        : __asm_proxy_VCVTSD2SI__,
    "vcvtsd2ss"        : __asm_proxy_VCVTSD2SS__,
    "vcvtsd2usi"       : __asm_proxy_VCVTSD2USI__,
    "vcvtsi2sd"        : __asm_proxy_VCVTSI2SD__,
    "vcvtsi2ss"        : __asm_proxy_VCVTSI2SS__,
    "vcvtss2sd"        : __asm_proxy_VCVTSS2SD__,
    "vcvtss2si"        : __asm_proxy_VCVTSS2SI__,
    "vcvtss2usi"       : __asm_proxy_VCVTSS2USI__,
    "vcvttpd2dq"       : __asm_proxy_VCVTTPD2DQ__,
    "vcvttpd2qq"       : __asm_proxy_VCVTTPD2QQ__,
    "vcvttpd2udq"      : __asm_proxy_VCVTTPD2UDQ__,
    "vcvttpd2uqq"      : __asm_proxy_VCVTTPD2UQQ__,
    "vcvttps2dq"       : __asm_proxy_VCVTTPS2DQ__,
    "vcvttps2qq"       : __asm_proxy_VCVTTPS2QQ__,
    "vcvttps2udq"      : __asm_proxy_VCVTTPS2UDQ__,
    "vcvttps2uqq"      : __asm_proxy_VCVTTPS2UQQ__,
    "vcvttsd2si"       : __asm_proxy_VCVTTSD2SI__,
    "vcvttsd2usi"      : __asm_proxy_VCVTTSD2USI__,
    "vcvttss2si"       : __asm_proxy_VCVTTSS2SI__,
    "vcvttss2usi"      : __asm_proxy_VCVTTSS2USI__,
    "vcvtudq2pd"       : __asm_proxy_VCVTUDQ2PD__,
    "vcvtudq2ps"       : __asm_proxy_VCVTUDQ2PS__,
    "vcvtuqq2pd"       : __asm_proxy_VCVTUQQ2PD__,
    "vcvtuqq2ps"       : __asm_proxy_VCVTUQQ2PS__,
    "vcvtusi2sd"       : __asm_proxy_VCVTUSI2SD__,
    "vcvtusi2ss"       : __asm_proxy_VCVTUSI2SS__,
    "vdbpsadbw"        : __asm_proxy_VDBPSADBW__,
    "vdivpd"           : __asm_proxy_VDIVPD__,
    "vdivps"           : __asm_proxy_VDIVPS__,
    "vdivsd"           : __asm_proxy_VDIVSD__,
    "vdivss"           : __asm_proxy_VDIVSS__,
    "vdppd"            : __asm_proxy_VDPPD__,
    "vdpps"            : __asm_proxy_VDPPS__,
    "vexp2pd"          : __asm_proxy_VEXP2PD__,
    "vexp2ps"          : __asm_proxy_VEXP2PS__,
    "vexpandpd"        : __asm_proxy_VEXPANDPD__,
    "vexpandps"        : __asm_proxy_VEXPANDPS__,
    "vextractf128"     : __asm_proxy_VEXTRACTF128__,
    "vextractf32x4"    : __asm_proxy_VEXTRACTF32X4__,
    "vextractf32x8"    : __asm_proxy_VEXTRACTF32X8__,
    "vextractf64x2"    : __asm_proxy_VEXTRACTF64X2__,
    "vextractf64x4"    : __asm_proxy_VEXTRACTF64X4__,
    "vextracti128"     : __asm_proxy_VEXTRACTI128__,
    "vextracti32x4"    : __asm_proxy_VEXTRACTI32X4__,
    "vextracti32x8"    : __asm_proxy_VEXTRACTI32X8__,
    "vextracti64x2"    : __asm_proxy_VEXTRACTI64X2__,
    "vextracti64x4"    : __asm_proxy_VEXTRACTI64X4__,
    "vextractps"       : __asm_proxy_VEXTRACTPS__,
    "vfixupimmpd"      : __asm_proxy_VFIXUPIMMPD__,
    "vfixupimmps"      : __asm_proxy_VFIXUPIMMPS__,
    "vfixupimmsd"      : __asm_proxy_VFIXUPIMMSD__,
    "vfixupimmss"      : __asm_proxy_VFIXUPIMMSS__,
    "vfmadd132pd"      : __asm_proxy_VFMADD132PD__,
    "vfmadd132ps"      : __asm_proxy_VFMADD132PS__,
    "vfmadd132sd"      : __asm_proxy_VFMADD132SD__,
    "vfmadd132ss"      : __asm_proxy_VFMADD132SS__,
    "vfmadd213pd"      : __asm_proxy_VFMADD213PD__,
    "vfmadd213ps"      : __asm_proxy_VFMADD213PS__,
    "vfmadd213sd"      : __asm_proxy_VFMADD213SD__,
    "vfmadd213ss"      : __asm_proxy_VFMADD213SS__,
    "vfmadd231pd"      : __asm_proxy_VFMADD231PD__,
    "vfmadd231ps"      : __asm_proxy_VFMADD231PS__,
    "vfmadd231sd"      : __asm_proxy_VFMADD231SD__,
    "vfmadd231ss"      : __asm_proxy_VFMADD231SS__,
    "vfmaddpd"         : __asm_proxy_VFMADDPD__,
    "vfmaddps"         : __asm_proxy_VFMADDPS__,
    "vfmaddsd"         : __asm_proxy_VFMADDSD__,
    "vfmaddss"         : __asm_proxy_VFMADDSS__,
    "vfmaddsub132pd"   : __asm_proxy_VFMADDSUB132PD__,
    "vfmaddsub132ps"   : __asm_proxy_VFMADDSUB132PS__,
    "vfmaddsub213pd"   : __asm_proxy_VFMADDSUB213PD__,
    "vfmaddsub213ps"   : __asm_proxy_VFMADDSUB213PS__,
    "vfmaddsub231pd"   : __asm_proxy_VFMADDSUB231PD__,
    "vfmaddsub231ps"   : __asm_proxy_VFMADDSUB231PS__,
    "vfmaddsubpd"      : __asm_proxy_VFMADDSUBPD__,
    "vfmaddsubps"      : __asm_proxy_VFMADDSUBPS__,
    "vfmsub132pd"      : __asm_proxy_VFMSUB132PD__,
    "vfmsub132ps"      : __asm_proxy_VFMSUB132PS__,
    "vfmsub132sd"      : __asm_proxy_VFMSUB132SD__,
    "vfmsub132ss"      : __asm_proxy_VFMSUB132SS__,
    "vfmsub213pd"      : __asm_proxy_VFMSUB213PD__,
    "vfmsub213ps"      : __asm_proxy_VFMSUB213PS__,
    "vfmsub213sd"      : __asm_proxy_VFMSUB213SD__,
    "vfmsub213ss"      : __asm_proxy_VFMSUB213SS__,
    "vfmsub231pd"      : __asm_proxy_VFMSUB231PD__,
    "vfmsub231ps"      : __asm_proxy_VFMSUB231PS__,
    "vfmsub231sd"      : __asm_proxy_VFMSUB231SD__,
    "vfmsub231ss"      : __asm_proxy_VFMSUB231SS__,
    "vfmsubadd132pd"   : __asm_proxy_VFMSUBADD132PD__,
    "vfmsubadd132ps"   : __asm_proxy_VFMSUBADD132PS__,
    "vfmsubadd213pd"   : __asm_proxy_VFMSUBADD213PD__,
    "vfmsubadd213ps"   : __asm_proxy_VFMSUBADD213PS__,
    "vfmsubadd231pd"   : __asm_proxy_VFMSUBADD231PD__,
    "vfmsubadd231ps"   : __asm_proxy_VFMSUBADD231PS__,
    "vfmsubaddpd"      : __asm_proxy_VFMSUBADDPD__,
    "vfmsubaddps"      : __asm_proxy_VFMSUBADDPS__,
    "vfmsubpd"         : __asm_proxy_VFMSUBPD__,
    "vfmsubps"         : __asm_proxy_VFMSUBPS__,
    "vfmsubsd"         : __asm_proxy_VFMSUBSD__,
    "vfmsubss"         : __asm_proxy_VFMSUBSS__,
    "vfnmadd132pd"     : __asm_proxy_VFNMADD132PD__,
    "vfnmadd132ps"     : __asm_proxy_VFNMADD132PS__,
    "vfnmadd132sd"     : __asm_proxy_VFNMADD132SD__,
    "vfnmadd132ss"     : __asm_proxy_VFNMADD132SS__,
    "vfnmadd213pd"     : __asm_proxy_VFNMADD213PD__,
    "vfnmadd213ps"     : __asm_proxy_VFNMADD213PS__,
    "vfnmadd213sd"     : __asm_proxy_VFNMADD213SD__,
    "vfnmadd213ss"     : __asm_proxy_VFNMADD213SS__,
    "vfnmadd231pd"     : __asm_proxy_VFNMADD231PD__,
    "vfnmadd231ps"     : __asm_proxy_VFNMADD231PS__,
    "vfnmadd231sd"     : __asm_proxy_VFNMADD231SD__,
    "vfnmadd231ss"     : __asm_proxy_VFNMADD231SS__,
    "vfnmaddpd"        : __asm_proxy_VFNMADDPD__,
    "vfnmaddps"        : __asm_proxy_VFNMADDPS__,
    "vfnmaddsd"        : __asm_proxy_VFNMADDSD__,
    "vfnmaddss"        : __asm_proxy_VFNMADDSS__,
    "vfnmsub132pd"     : __asm_proxy_VFNMSUB132PD__,
    "vfnmsub132ps"     : __asm_proxy_VFNMSUB132PS__,
    "vfnmsub132sd"     : __asm_proxy_VFNMSUB132SD__,
    "vfnmsub132ss"     : __asm_proxy_VFNMSUB132SS__,
    "vfnmsub213pd"     : __asm_proxy_VFNMSUB213PD__,
    "vfnmsub213ps"     : __asm_proxy_VFNMSUB213PS__,
    "vfnmsub213sd"     : __asm_proxy_VFNMSUB213SD__,
    "vfnmsub213ss"     : __asm_proxy_VFNMSUB213SS__,
    "vfnmsub231pd"     : __asm_proxy_VFNMSUB231PD__,
    "vfnmsub231ps"     : __asm_proxy_VFNMSUB231PS__,
    "vfnmsub231sd"     : __asm_proxy_VFNMSUB231SD__,
    "vfnmsub231ss"     : __asm_proxy_VFNMSUB231SS__,
    "vfnmsubpd"        : __asm_proxy_VFNMSUBPD__,
    "vfnmsubps"        : __asm_proxy_VFNMSUBPS__,
    "vfnmsubsd"        : __asm_proxy_VFNMSUBSD__,
    "vfnmsubss"        : __asm_proxy_VFNMSUBSS__,
    "vfpclasspd"       : __asm_proxy_VFPCLASSPD__,
    "vfpclassps"       : __asm_proxy_VFPCLASSPS__,
    "vfpclasssd"       : __asm_proxy_VFPCLASSSD__,
    "vfpclassss"       : __asm_proxy_VFPCLASSSS__,
    "vfrczpd"          : __asm_proxy_VFRCZPD__,
    "vfrczps"          : __asm_proxy_VFRCZPS__,
    "vfrczsd"          : __asm_proxy_VFRCZSD__,
    "vfrczss"          : __asm_proxy_VFRCZSS__,
    "vgatherdpd"       : __asm_proxy_VGATHERDPD__,
    "vgatherdps"       : __asm_proxy_VGATHERDPS__,
    "vgatherpf0dpd"    : __asm_proxy_VGATHERPF0DPD__,
    "vgatherpf0dps"    : __asm_proxy_VGATHERPF0DPS__,
    "vgatherpf0qpd"    : __asm_proxy_VGATHERPF0QPD__,
    "vgatherpf0qps"    : __asm_proxy_VGATHERPF0QPS__,
    "vgatherpf1dpd"    : __asm_proxy_VGATHERPF1DPD__,
    "vgatherpf1dps"    : __asm_proxy_VGATHERPF1DPS__,
    "vgatherpf1qpd"    : __asm_proxy_VGATHERPF1QPD__,
    "vgatherpf1qps"    : __asm_proxy_VGATHERPF1QPS__,
    "vgatherqpd"       : __asm_proxy_VGATHERQPD__,
    "vgatherqps"       : __asm_proxy_VGATHERQPS__,
    "vgetexppd"        : __asm_proxy_VGETEXPPD__,
    "vgetexpps"        : __asm_proxy_VGETEXPPS__,
    "vgetexpsd"        : __asm_proxy_VGETEXPSD__,
    "vgetexpss"        : __asm_proxy_VGETEXPSS__,
    "vgetmantpd"       : __asm_proxy_VGETMANTPD__,
    "vgetmantps"       : __asm_proxy_VGETMANTPS__,
    "vgetmantsd"       : __asm_proxy_VGETMANTSD__,
    "vgetmantss"       : __asm_proxy_VGETMANTSS__,
    "vhaddpd"          : __asm_proxy_VHADDPD__,
    "vhaddps"          : __asm_proxy_VHADDPS__,
    "vhsubpd"          : __asm_proxy_VHSUBPD__,
    "vhsubps"          : __asm_proxy_VHSUBPS__,
    "vinsertf128"      : __asm_proxy_VINSERTF128__,
    "vinsertf32x4"     : __asm_proxy_VINSERTF32X4__,
    "vinsertf32x8"     : __asm_proxy_VINSERTF32X8__,
    "vinsertf64x2"     : __asm_proxy_VINSERTF64X2__,
    "vinsertf64x4"     : __asm_proxy_VINSERTF64X4__,
    "vinserti128"      : __asm_proxy_VINSERTI128__,
    "vinserti32x4"     : __asm_proxy_VINSERTI32X4__,
    "vinserti32x8"     : __asm_proxy_VINSERTI32X8__,
    "vinserti64x2"     : __asm_proxy_VINSERTI64X2__,
    "vinserti64x4"     : __asm_proxy_VINSERTI64X4__,
    "vinsertps"        : __asm_proxy_VINSERTPS__,
    "vlddqu"           : __asm_proxy_VLDDQU__,
    "vldmxcsr"         : __asm_proxy_VLDMXCSR__,
    "vmaskmovdqu"      : __asm_proxy_VMASKMOVDQU__,
    "vmaskmovpd"       : __asm_proxy_VMASKMOVPD__,
    "vmaskmovps"       : __asm_proxy_VMASKMOVPS__,
    "vmaxpd"           : __asm_proxy_VMAXPD__,
    "vmaxps"           : __asm_proxy_VMAXPS__,
    "vmaxsd"           : __asm_proxy_VMAXSD__,
    "vmaxss"           : __asm_proxy_VMAXSS__,
    "vminpd"           : __asm_proxy_VMINPD__,
    "vminps"           : __asm_proxy_VMINPS__,
    "vminsd"           : __asm_proxy_VMINSD__,
    "vminss"           : __asm_proxy_VMINSS__,
    "vmovapd"          : __asm_proxy_VMOVAPD__,
    "vmovaps"          : __asm_proxy_VMOVAPS__,
    "vmovd"            : __asm_proxy_VMOVD__,
    "vmovddup"         : __asm_proxy_VMOVDDUP__,
    "vmovdqa"          : __asm_proxy_VMOVDQA__,
    "vmovdqa32"        : __asm_proxy_VMOVDQA32__,
    "vmovdqa64"        : __asm_proxy_VMOVDQA64__,
    "vmovdqu"          : __asm_proxy_VMOVDQU__,
    "vmovdqu16"        : __asm_proxy_VMOVDQU16__,
    "vmovdqu32"        : __asm_proxy_VMOVDQU32__,
    "vmovdqu64"        : __asm_proxy_VMOVDQU64__,
    "vmovdqu8"         : __asm_proxy_VMOVDQU8__,
    "vmovhlps"         : __asm_proxy_VMOVHLPS__,
    "vmovhpd"          : __asm_proxy_VMOVHPD__,
    "vmovhps"          : __asm_proxy_VMOVHPS__,
    "vmovlhps"         : __asm_proxy_VMOVLHPS__,
    "vmovlpd"          : __asm_proxy_VMOVLPD__,
    "vmovlps"          : __asm_proxy_VMOVLPS__,
    "vmovmskpd"        : __asm_proxy_VMOVMSKPD__,
    "vmovmskps"        : __asm_proxy_VMOVMSKPS__,
    "vmovntdq"         : __asm_proxy_VMOVNTDQ__,
    "vmovntdqa"        : __asm_proxy_VMOVNTDQA__,
    "vmovntpd"         : __asm_proxy_VMOVNTPD__,
    "vmovntps"         : __asm_proxy_VMOVNTPS__,
    "vmovq"            : __asm_proxy_VMOVQ__,
    "vmovsd"           : __asm_proxy_VMOVSD__,
    "vmovshdup"        : __asm_proxy_VMOVSHDUP__,
    "vmovsldup"        : __asm_proxy_VMOVSLDUP__,
    "vmovss"           : __asm_proxy_VMOVSS__,
    "vmovupd"          : __asm_proxy_VMOVUPD__,
    "vmovups"          : __asm_proxy_VMOVUPS__,
    "vmpsadbw"         : __asm_proxy_VMPSADBW__,
    "vmulpd"           : __asm_proxy_VMULPD__,
    "vmulps"           : __asm_proxy_VMULPS__,
    "vmulsd"           : __asm_proxy_VMULSD__,
    "vmulss"           : __asm_proxy_VMULSS__,
    "vorpd"            : __asm_proxy_VORPD__,
    "vorps"            : __asm_proxy_VORPS__,
    "vpabsb"           : __asm_proxy_VPABSB__,
    "vpabsd"           : __asm_proxy_VPABSD__,
    "vpabsq"           : __asm_proxy_VPABSQ__,
    "vpabsw"           : __asm_proxy_VPABSW__,
    "vpackssdw"        : __asm_proxy_VPACKSSDW__,
    "vpacksswb"        : __asm_proxy_VPACKSSWB__,
    "vpackusdw"        : __asm_proxy_VPACKUSDW__,
    "vpackuswb"        : __asm_proxy_VPACKUSWB__,
    "vpaddb"           : __asm_proxy_VPADDB__,
    "vpaddd"           : __asm_proxy_VPADDD__,
    "vpaddq"           : __asm_proxy_VPADDQ__,
    "vpaddsb"          : __asm_proxy_VPADDSB__,
    "vpaddsw"          : __asm_proxy_VPADDSW__,
    "vpaddusb"         : __asm_proxy_VPADDUSB__,
    "vpaddusw"         : __asm_proxy_VPADDUSW__,
    "vpaddw"           : __asm_proxy_VPADDW__,
    "vpalignr"         : __asm_proxy_VPALIGNR__,
    "vpand"            : __asm_proxy_VPAND__,
    "vpandd"           : __asm_proxy_VPANDD__,
    "vpandn"           : __asm_proxy_VPANDN__,
    "vpandnd"          : __asm_proxy_VPANDND__,
    "vpandnq"          : __asm_proxy_VPANDNQ__,
    "vpandq"           : __asm_proxy_VPANDQ__,
    "vpavgb"           : __asm_proxy_VPAVGB__,
    "vpavgw"           : __asm_proxy_VPAVGW__,
    "vpblendd"         : __asm_proxy_VPBLENDD__,
    "vpblendmb"        : __asm_proxy_VPBLENDMB__,
    "vpblendmd"        : __asm_proxy_VPBLENDMD__,
    "vpblendmq"        : __asm_proxy_VPBLENDMQ__,
    "vpblendmw"        : __asm_proxy_VPBLENDMW__,
    "vpblendvb"        : __asm_proxy_VPBLENDVB__,
    "vpblendw"         : __asm_proxy_VPBLENDW__,
    "vpbroadcastb"     : __asm_proxy_VPBROADCASTB__,
    "vpbroadcastd"     : __asm_proxy_VPBROADCASTD__,
    "vpbroadcastmb2q"  : __asm_proxy_VPBROADCASTMB2Q__,
    "vpbroadcastmw2d"  : __asm_proxy_VPBROADCASTMW2D__,
    "vpbroadcastq"     : __asm_proxy_VPBROADCASTQ__,
    "vpbroadcastw"     : __asm_proxy_VPBROADCASTW__,
    "vpclmulqdq"       : __asm_proxy_VPCLMULQDQ__,
    "vpcmov"           : __asm_proxy_VPCMOV__,
    "vpcmpb"           : __asm_proxy_VPCMPB__,
    "vpcmpd"           : __asm_proxy_VPCMPD__,
    "vpcmpeqb"         : __asm_proxy_VPCMPEQB__,
    "vpcmpeqd"         : __asm_proxy_VPCMPEQD__,
    "vpcmpeqq"         : __asm_proxy_VPCMPEQQ__,
    "vpcmpeqw"         : __asm_proxy_VPCMPEQW__,
    "vpcmpestri"       : __asm_proxy_VPCMPESTRI__,
    "vpcmpestrm"       : __asm_proxy_VPCMPESTRM__,
    "vpcmpgtb"         : __asm_proxy_VPCMPGTB__,
    "vpcmpgtd"         : __asm_proxy_VPCMPGTD__,
    "vpcmpgtq"         : __asm_proxy_VPCMPGTQ__,
    "vpcmpgtw"         : __asm_proxy_VPCMPGTW__,
    "vpcmpistri"       : __asm_proxy_VPCMPISTRI__,
    "vpcmpistrm"       : __asm_proxy_VPCMPISTRM__,
    "vpcmpq"           : __asm_proxy_VPCMPQ__,
    "vpcmpub"          : __asm_proxy_VPCMPUB__,
    "vpcmpud"          : __asm_proxy_VPCMPUD__,
    "vpcmpuq"          : __asm_proxy_VPCMPUQ__,
    "vpcmpuw"          : __asm_proxy_VPCMPUW__,
    "vpcmpw"           : __asm_proxy_VPCMPW__,
    "vpcomb"           : __asm_proxy_VPCOMB__,
    "vpcomd"           : __asm_proxy_VPCOMD__,
    "vpcompressd"      : __asm_proxy_VPCOMPRESSD__,
    "vpcompressq"      : __asm_proxy_VPCOMPRESSQ__,
    "vpcomq"           : __asm_proxy_VPCOMQ__,
    "vpcomub"          : __asm_proxy_VPCOMUB__,
    "vpcomud"          : __asm_proxy_VPCOMUD__,
    "vpcomuq"          : __asm_proxy_VPCOMUQ__,
    "vpcomuw"          : __asm_proxy_VPCOMUW__,
    "vpcomw"           : __asm_proxy_VPCOMW__,
    "vpconflictd"      : __asm_proxy_VPCONFLICTD__,
    "vpconflictq"      : __asm_proxy_VPCONFLICTQ__,
    "vperm2f128"       : __asm_proxy_VPERM2F128__,
    "vperm2i128"       : __asm_proxy_VPERM2I128__,
    "vpermb"           : __asm_proxy_VPERMB__,
    "vpermd"           : __asm_proxy_VPERMD__,
    "vpermi2b"         : __asm_proxy_VPERMI2B__,
    "vpermi2d"         : __asm_proxy_VPERMI2D__,
    "vpermi2pd"        : __asm_proxy_VPERMI2PD__,
    "vpermi2ps"        : __asm_proxy_VPERMI2PS__,
    "vpermi2q"         : __asm_proxy_VPERMI2Q__,
    "vpermi2w"         : __asm_proxy_VPERMI2W__,
    "vpermil2pd"       : __asm_proxy_VPERMIL2PD__,
    "vpermil2ps"       : __asm_proxy_VPERMIL2PS__,
    "vpermilpd"        : __asm_proxy_VPERMILPD__,
    "vpermilps"        : __asm_proxy_VPERMILPS__,
    "vpermpd"          : __asm_proxy_VPERMPD__,
    "vpermps"          : __asm_proxy_VPERMPS__,
    "vpermq"           : __asm_proxy_VPERMQ__,
    "vpermt2b"         : __asm_proxy_VPERMT2B__,
    "vpermt2d"         : __asm_proxy_VPERMT2D__,
    "vpermt2pd"        : __asm_proxy_VPERMT2PD__,
    "vpermt2ps"        : __asm_proxy_VPERMT2PS__,
    "vpermt2q"         : __asm_proxy_VPERMT2Q__,
    "vpermt2w"         : __asm_proxy_VPERMT2W__,
    "vpermw"           : __asm_proxy_VPERMW__,
    "vpexpandd"        : __asm_proxy_VPEXPANDD__,
    "vpexpandq"        : __asm_proxy_VPEXPANDQ__,
    "vpextrb"          : __asm_proxy_VPEXTRB__,
    "vpextrd"          : __asm_proxy_VPEXTRD__,
    "vpextrq"          : __asm_proxy_VPEXTRQ__,
    "vpextrw"          : __asm_proxy_VPEXTRW__,
    "vpgatherdd"       : __asm_proxy_VPGATHERDD__,
    "vpgatherdq"       : __asm_proxy_VPGATHERDQ__,
    "vpgatherqd"       : __asm_proxy_VPGATHERQD__,
    "vpgatherqq"       : __asm_proxy_VPGATHERQQ__,
    "vphaddbd"         : __asm_proxy_VPHADDBD__,
    "vphaddbq"         : __asm_proxy_VPHADDBQ__,
    "vphaddbw"         : __asm_proxy_VPHADDBW__,
    "vphaddd"          : __asm_proxy_VPHADDD__,
    "vphadddq"         : __asm_proxy_VPHADDDQ__,
    "vphaddsw"         : __asm_proxy_VPHADDSW__,
    "vphaddubd"        : __asm_proxy_VPHADDUBD__,
    "vphaddubq"        : __asm_proxy_VPHADDUBQ__,
    "vphaddubw"        : __asm_proxy_VPHADDUBW__,
    "vphaddudq"        : __asm_proxy_VPHADDUDQ__,
    "vphadduwd"        : __asm_proxy_VPHADDUWD__,
    "vphadduwq"        : __asm_proxy_VPHADDUWQ__,
    "vphaddw"          : __asm_proxy_VPHADDW__,
    "vphaddwd"         : __asm_proxy_VPHADDWD__,
    "vphaddwq"         : __asm_proxy_VPHADDWQ__,
    "vphminposuw"      : __asm_proxy_VPHMINPOSUW__,
    "vphsubbw"         : __asm_proxy_VPHSUBBW__,
    "vphsubd"          : __asm_proxy_VPHSUBD__,
    "vphsubdq"         : __asm_proxy_VPHSUBDQ__,
    "vphsubsw"         : __asm_proxy_VPHSUBSW__,
    "vphsubw"          : __asm_proxy_VPHSUBW__,
    "vphsubwd"         : __asm_proxy_VPHSUBWD__,
    "vpinsrb"          : __asm_proxy_VPINSRB__,
    "vpinsrd"          : __asm_proxy_VPINSRD__,
    "vpinsrq"          : __asm_proxy_VPINSRQ__,
    "vpinsrw"          : __asm_proxy_VPINSRW__,
    "vplzcntd"         : __asm_proxy_VPLZCNTD__,
    "vplzcntq"         : __asm_proxy_VPLZCNTQ__,
    "vpmacsdd"         : __asm_proxy_VPMACSDD__,
    "vpmacsdqh"        : __asm_proxy_VPMACSDQH__,
    "vpmacsdql"        : __asm_proxy_VPMACSDQL__,
    "vpmacssdd"        : __asm_proxy_VPMACSSDD__,
    "vpmacssdqh"       : __asm_proxy_VPMACSSDQH__,
    "vpmacssdql"       : __asm_proxy_VPMACSSDQL__,
    "vpmacsswd"        : __asm_proxy_VPMACSSWD__,
    "vpmacssww"        : __asm_proxy_VPMACSSWW__,
    "vpmacswd"         : __asm_proxy_VPMACSWD__,
    "vpmacsww"         : __asm_proxy_VPMACSWW__,
    "vpmadcsswd"       : __asm_proxy_VPMADCSSWD__,
    "vpmadcswd"        : __asm_proxy_VPMADCSWD__,
    "vpmadd52huq"      : __asm_proxy_VPMADD52HUQ__,
    "vpmadd52luq"      : __asm_proxy_VPMADD52LUQ__,
    "vpmaddubsw"       : __asm_proxy_VPMADDUBSW__,
    "vpmaddwd"         : __asm_proxy_VPMADDWD__,
    "vpmaskmovd"       : __asm_proxy_VPMASKMOVD__,
    "vpmaskmovq"       : __asm_proxy_VPMASKMOVQ__,
    "vpmaxsb"          : __asm_proxy_VPMAXSB__,
    "vpmaxsd"          : __asm_proxy_VPMAXSD__,
    "vpmaxsq"          : __asm_proxy_VPMAXSQ__,
    "vpmaxsw"          : __asm_proxy_VPMAXSW__,
    "vpmaxub"          : __asm_proxy_VPMAXUB__,
    "vpmaxud"          : __asm_proxy_VPMAXUD__,
    "vpmaxuq"          : __asm_proxy_VPMAXUQ__,
    "vpmaxuw"          : __asm_proxy_VPMAXUW__,
    "vpminsb"          : __asm_proxy_VPMINSB__,
    "vpminsd"          : __asm_proxy_VPMINSD__,
    "vpminsq"          : __asm_proxy_VPMINSQ__,
    "vpminsw"          : __asm_proxy_VPMINSW__,
    "vpminub"          : __asm_proxy_VPMINUB__,
    "vpminud"          : __asm_proxy_VPMINUD__,
    "vpminuq"          : __asm_proxy_VPMINUQ__,
    "vpminuw"          : __asm_proxy_VPMINUW__,
    "vpmovb2m"         : __asm_proxy_VPMOVB2M__,
    "vpmovd2m"         : __asm_proxy_VPMOVD2M__,
    "vpmovdb"          : __asm_proxy_VPMOVDB__,
    "vpmovdw"          : __asm_proxy_VPMOVDW__,
    "vpmovm2b"         : __asm_proxy_VPMOVM2B__,
    "vpmovm2d"         : __asm_proxy_VPMOVM2D__,
    "vpmovm2q"         : __asm_proxy_VPMOVM2Q__,
    "vpmovm2w"         : __asm_proxy_VPMOVM2W__,
    "vpmovmskb"        : __asm_proxy_VPMOVMSKB__,
    "vpmovq2m"         : __asm_proxy_VPMOVQ2M__,
    "vpmovqb"          : __asm_proxy_VPMOVQB__,
    "vpmovqd"          : __asm_proxy_VPMOVQD__,
    "vpmovqw"          : __asm_proxy_VPMOVQW__,
    "vpmovsdb"         : __asm_proxy_VPMOVSDB__,
    "vpmovsdw"         : __asm_proxy_VPMOVSDW__,
    "vpmovsqb"         : __asm_proxy_VPMOVSQB__,
    "vpmovsqd"         : __asm_proxy_VPMOVSQD__,
    "vpmovsqw"         : __asm_proxy_VPMOVSQW__,
    "vpmovswb"         : __asm_proxy_VPMOVSWB__,
    "vpmovsxbd"        : __asm_proxy_VPMOVSXBD__,
    "vpmovsxbq"        : __asm_proxy_VPMOVSXBQ__,
    "vpmovsxbw"        : __asm_proxy_VPMOVSXBW__,
    "vpmovsxdq"        : __asm_proxy_VPMOVSXDQ__,
    "vpmovsxwd"        : __asm_proxy_VPMOVSXWD__,
    "vpmovsxwq"        : __asm_proxy_VPMOVSXWQ__,
    "vpmovusdb"        : __asm_proxy_VPMOVUSDB__,
    "vpmovusdw"        : __asm_proxy_VPMOVUSDW__,
    "vpmovusqb"        : __asm_proxy_VPMOVUSQB__,
    "vpmovusqd"        : __asm_proxy_VPMOVUSQD__,
    "vpmovusqw"        : __asm_proxy_VPMOVUSQW__,
    "vpmovuswb"        : __asm_proxy_VPMOVUSWB__,
    "vpmovw2m"         : __asm_proxy_VPMOVW2M__,
    "vpmovwb"          : __asm_proxy_VPMOVWB__,
    "vpmovzxbd"        : __asm_proxy_VPMOVZXBD__,
    "vpmovzxbq"        : __asm_proxy_VPMOVZXBQ__,
    "vpmovzxbw"        : __asm_proxy_VPMOVZXBW__,
    "vpmovzxdq"        : __asm_proxy_VPMOVZXDQ__,
    "vpmovzxwd"        : __asm_proxy_VPMOVZXWD__,
    "vpmovzxwq"        : __asm_proxy_VPMOVZXWQ__,
    "vpmuldq"          : __asm_proxy_VPMULDQ__,
    "vpmulhrsw"        : __asm_proxy_VPMULHRSW__,
    "vpmulhuw"         : __asm_proxy_VPMULHUW__,
    "vpmulhw"          : __asm_proxy_VPMULHW__,
    "vpmulld"          : __asm_proxy_VPMULLD__,
    "vpmullq"          : __asm_proxy_VPMULLQ__,
    "vpmullw"          : __asm_proxy_VPMULLW__,
    "vpmultishiftqb"   : __asm_proxy_VPMULTISHIFTQB__,
    "vpmuludq"         : __asm_proxy_VPMULUDQ__,
    "vpopcntd"         : __asm_proxy_VPOPCNTD__,
    "vpopcntq"         : __asm_proxy_VPOPCNTQ__,
    "vpor"             : __asm_proxy_VPOR__,
    "vpord"            : __asm_proxy_VPORD__,
    "vporq"            : __asm_proxy_VPORQ__,
    "vpperm"           : __asm_proxy_VPPERM__,
    "vprold"           : __asm_proxy_VPROLD__,
    "vprolq"           : __asm_proxy_VPROLQ__,
    "vprolvd"          : __asm_proxy_VPROLVD__,
    "vprolvq"          : __asm_proxy_VPROLVQ__,
    "vprord"           : __asm_proxy_VPRORD__,
    "vprorq"           : __asm_proxy_VPRORQ__,
    "vprorvd"          : __asm_proxy_VPRORVD__,
    "vprorvq"          : __asm_proxy_VPRORVQ__,
    "vprotb"           : __asm_proxy_VPROTB__,
    "vprotd"           : __asm_proxy_VPROTD__,
    "vprotq"           : __asm_proxy_VPROTQ__,
    "vprotw"           : __asm_proxy_VPROTW__,
    "vpsadbw"          : __asm_proxy_VPSADBW__,
    "vpscatterdd"      : __asm_proxy_VPSCATTERDD__,
    "vpscatterdq"      : __asm_proxy_VPSCATTERDQ__,
    "vpscatterqd"      : __asm_proxy_VPSCATTERQD__,
    "vpscatterqq"      : __asm_proxy_VPSCATTERQQ__,
    "vpshab"           : __asm_proxy_VPSHAB__,
    "vpshad"           : __asm_proxy_VPSHAD__,
    "vpshaq"           : __asm_proxy_VPSHAQ__,
    "vpshaw"           : __asm_proxy_VPSHAW__,
    "vpshlb"           : __asm_proxy_VPSHLB__,
    "vpshld"           : __asm_proxy_VPSHLD__,
    "vpshlq"           : __asm_proxy_VPSHLQ__,
    "vpshlw"           : __asm_proxy_VPSHLW__,
    "vpshufb"          : __asm_proxy_VPSHUFB__,
    "vpshufd"          : __asm_proxy_VPSHUFD__,
    "vpshufhw"         : __asm_proxy_VPSHUFHW__,
    "vpshuflw"         : __asm_proxy_VPSHUFLW__,
    "vpsignb"          : __asm_proxy_VPSIGNB__,
    "vpsignd"          : __asm_proxy_VPSIGND__,
    "vpsignw"          : __asm_proxy_VPSIGNW__,
    "vpslld"           : __asm_proxy_VPSLLD__,
    "vpslldq"          : __asm_proxy_VPSLLDQ__,
    "vpsllq"           : __asm_proxy_VPSLLQ__,
    "vpsllvd"          : __asm_proxy_VPSLLVD__,
    "vpsllvq"          : __asm_proxy_VPSLLVQ__,
    "vpsllvw"          : __asm_proxy_VPSLLVW__,
    "vpsllw"           : __asm_proxy_VPSLLW__,
    "vpsrad"           : __asm_proxy_VPSRAD__,
    "vpsraq"           : __asm_proxy_VPSRAQ__,
    "vpsravd"          : __asm_proxy_VPSRAVD__,
    "vpsravq"          : __asm_proxy_VPSRAVQ__,
    "vpsravw"          : __asm_proxy_VPSRAVW__,
    "vpsraw"           : __asm_proxy_VPSRAW__,
    "vpsrld"           : __asm_proxy_VPSRLD__,
    "vpsrldq"          : __asm_proxy_VPSRLDQ__,
    "vpsrlq"           : __asm_proxy_VPSRLQ__,
    "vpsrlvd"          : __asm_proxy_VPSRLVD__,
    "vpsrlvq"          : __asm_proxy_VPSRLVQ__,
    "vpsrlvw"          : __asm_proxy_VPSRLVW__,
    "vpsrlw"           : __asm_proxy_VPSRLW__,
    "vpsubb"           : __asm_proxy_VPSUBB__,
    "vpsubd"           : __asm_proxy_VPSUBD__,
    "vpsubq"           : __asm_proxy_VPSUBQ__,
    "vpsubsb"          : __asm_proxy_VPSUBSB__,
    "vpsubsw"          : __asm_proxy_VPSUBSW__,
    "vpsubusb"         : __asm_proxy_VPSUBUSB__,
    "vpsubusw"         : __asm_proxy_VPSUBUSW__,
    "vpsubw"           : __asm_proxy_VPSUBW__,
    "vpternlogd"       : __asm_proxy_VPTERNLOGD__,
    "vpternlogq"       : __asm_proxy_VPTERNLOGQ__,
    "vptest"           : __asm_proxy_VPTEST__,
    "vptestmb"         : __asm_proxy_VPTESTMB__,
    "vptestmd"         : __asm_proxy_VPTESTMD__,
    "vptestmq"         : __asm_proxy_VPTESTMQ__,
    "vptestmw"         : __asm_proxy_VPTESTMW__,
    "vptestnmb"        : __asm_proxy_VPTESTNMB__,
    "vptestnmd"        : __asm_proxy_VPTESTNMD__,
    "vptestnmq"        : __asm_proxy_VPTESTNMQ__,
    "vptestnmw"        : __asm_proxy_VPTESTNMW__,
    "vpunpckhbw"       : __asm_proxy_VPUNPCKHBW__,
    "vpunpckhdq"       : __asm_proxy_VPUNPCKHDQ__,
    "vpunpckhqdq"      : __asm_proxy_VPUNPCKHQDQ__,
    "vpunpckhwd"       : __asm_proxy_VPUNPCKHWD__,
    "vpunpcklbw"       : __asm_proxy_VPUNPCKLBW__,
    "vpunpckldq"       : __asm_proxy_VPUNPCKLDQ__,
    "vpunpcklqdq"      : __asm_proxy_VPUNPCKLQDQ__,
    "vpunpcklwd"       : __asm_proxy_VPUNPCKLWD__,
    "vpxor"            : __asm_proxy_VPXOR__,
    "vpxord"           : __asm_proxy_VPXORD__,
    "vpxorq"           : __asm_proxy_VPXORQ__,
    "vrangepd"         : __asm_proxy_VRANGEPD__,
    "vrangeps"         : __asm_proxy_VRANGEPS__,
    "vrangesd"         : __asm_proxy_VRANGESD__,
    "vrangess"         : __asm_proxy_VRANGESS__,
    "vrcp14pd"         : __asm_proxy_VRCP14PD__,
    "vrcp14ps"         : __asm_proxy_VRCP14PS__,
    "vrcp14sd"         : __asm_proxy_VRCP14SD__,
    "vrcp14ss"         : __asm_proxy_VRCP14SS__,
    "vrcp28pd"         : __asm_proxy_VRCP28PD__,
    "vrcp28ps"         : __asm_proxy_VRCP28PS__,
    "vrcp28sd"         : __asm_proxy_VRCP28SD__,
    "vrcp28ss"         : __asm_proxy_VRCP28SS__,
    "vrcpps"           : __asm_proxy_VRCPPS__,
    "vrcpss"           : __asm_proxy_VRCPSS__,
    "vreducepd"        : __asm_proxy_VREDUCEPD__,
    "vreduceps"        : __asm_proxy_VREDUCEPS__,
    "vreducesd"        : __asm_proxy_VREDUCESD__,
    "vreducess"        : __asm_proxy_VREDUCESS__,
    "vrndscalepd"      : __asm_proxy_VRNDSCALEPD__,
    "vrndscaleps"      : __asm_proxy_VRNDSCALEPS__,
    "vrndscalesd"      : __asm_proxy_VRNDSCALESD__,
    "vrndscaless"      : __asm_proxy_VRNDSCALESS__,
    "vroundpd"         : __asm_proxy_VROUNDPD__,
    "vroundps"         : __asm_proxy_VROUNDPS__,
    "vroundsd"         : __asm_proxy_VROUNDSD__,
    "vroundss"         : __asm_proxy_VROUNDSS__,
    "vrsqrt14pd"       : __asm_proxy_VRSQRT14PD__,
    "vrsqrt14ps"       : __asm_proxy_VRSQRT14PS__,
    "vrsqrt14sd"       : __asm_proxy_VRSQRT14SD__,
    "vrsqrt14ss"       : __asm_proxy_VRSQRT14SS__,
    "vrsqrt28pd"       : __asm_proxy_VRSQRT28PD__,
    "vrsqrt28ps"       : __asm_proxy_VRSQRT28PS__,
    "vrsqrt28sd"       : __asm_proxy_VRSQRT28SD__,
    "vrsqrt28ss"       : __asm_proxy_VRSQRT28SS__,
    "vrsqrtps"         : __asm_proxy_VRSQRTPS__,
    "vrsqrtss"         : __asm_proxy_VRSQRTSS__,
    "vscalefpd"        : __asm_proxy_VSCALEFPD__,
    "vscalefps"        : __asm_proxy_VSCALEFPS__,
    "vscalefsd"        : __asm_proxy_VSCALEFSD__,
    "vscalefss"        : __asm_proxy_VSCALEFSS__,
    "vscatterdpd"      : __asm_proxy_VSCATTERDPD__,
    "vscatterdps"      : __asm_proxy_VSCATTERDPS__,
    "vscatterpf0dpd"   : __asm_proxy_VSCATTERPF0DPD__,
    "vscatterpf0dps"   : __asm_proxy_VSCATTERPF0DPS__,
    "vscatterpf0qpd"   : __asm_proxy_VSCATTERPF0QPD__,
    "vscatterpf0qps"   : __asm_proxy_VSCATTERPF0QPS__,
    "vscatterpf1dpd"   : __asm_proxy_VSCATTERPF1DPD__,
    "vscatterpf1dps"   : __asm_proxy_VSCATTERPF1DPS__,
    "vscatterpf1qpd"   : __asm_proxy_VSCATTERPF1QPD__,
    "vscatterpf1qps"   : __asm_proxy_VSCATTERPF1QPS__,
    "vscatterqpd"      : __asm_proxy_VSCATTERQPD__,
    "vscatterqps"      : __asm_proxy_VSCATTERQPS__,
    "vshuff32x4"       : __asm_proxy_VSHUFF32X4__,
    "vshuff64x2"       : __asm_proxy_VSHUFF64X2__,
    "vshufi32x4"       : __asm_proxy_VSHUFI32X4__,
    "vshufi64x2"       : __asm_proxy_VSHUFI64X2__,
    "vshufpd"          : __asm_proxy_VSHUFPD__,
    "vshufps"          : __asm_proxy_VSHUFPS__,
    "vsqrtpd"          : __asm_proxy_VSQRTPD__,
    "vsqrtps"          : __asm_proxy_VSQRTPS__,
    "vsqrtsd"          : __asm_proxy_VSQRTSD__,
    "vsqrtss"          : __asm_proxy_VSQRTSS__,
    "vstmxcsr"         : __asm_proxy_VSTMXCSR__,
    "vsubpd"           : __asm_proxy_VSUBPD__,
    "vsubps"           : __asm_proxy_VSUBPS__,
    "vsubsd"           : __asm_proxy_VSUBSD__,
    "vsubss"           : __asm_proxy_VSUBSS__,
    "vtestpd"          : __asm_proxy_VTESTPD__,
    "vtestps"          : __asm_proxy_VTESTPS__,
    "vucomisd"         : __asm_proxy_VUCOMISD__,
    "vucomiss"         : __asm_proxy_VUCOMISS__,
    "vunpckhpd"        : __asm_proxy_VUNPCKHPD__,
    "vunpckhps"        : __asm_proxy_VUNPCKHPS__,
    "vunpcklpd"        : __asm_proxy_VUNPCKLPD__,
    "vunpcklps"        : __asm_proxy_VUNPCKLPS__,
    "vxorpd"           : __asm_proxy_VXORPD__,
    "vxorps"           : __asm_proxy_VXORPS__,
    "vzeroall"         : __asm_proxy_VZEROALL__,
    "vzeroupper"       : __asm_proxy_VZEROUPPER__,
    "xaddb"            : __asm_proxy_XADDB__,
    "xaddl"            : __asm_proxy_XADDL__,
    "xaddq"            : __asm_proxy_XADDQ__,
    "xaddw"            : __asm_proxy_XADDW__,
    "xchgb"            : __asm_proxy_XCHGB__,
    "xchgl"            : __asm_proxy_XCHGL__,
    "xchgq"            : __asm_proxy_XCHGQ__,
    "xchgw"            : __asm_proxy_XCHGW__,
    "xgetbv"           : __asm_proxy_XGETBV__,
    "xlatb"            : __asm_proxy_XLATB__,
    "xorb"             : __asm_proxy_XORB__,
    "xorl"             : __asm_proxy_XORL__,
    "xorpd"            : __asm_proxy_XORPD__,
    "xorps"            : __asm_proxy_XORPS__,
    "xorq"             : __asm_proxy_XORQ__,
    "xorw"             : __asm_proxy_XORW__,
}

func __asm_proxy_ADCB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ADCB(v[0], v[1])
    } else {
        panic("instruction ADCB takes exactly 2 operands")
    }
}

func __asm_proxy_ADCL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ADCL(v[0], v[1])
    } else {
        panic("instruction ADCL takes exactly 2 operands")
    }
}

func __asm_proxy_ADCQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ADCQ(v[0], v[1])
    } else {
        panic("instruction ADCQ takes exactly 2 operands")
    }
}

func __asm_proxy_ADCW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ADCW(v[0], v[1])
    } else {
        panic("instruction ADCW takes exactly 2 operands")
    }
}

func __asm_proxy_ADCXL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ADCXL(v[0], v[1])
    } else {
        panic("instruction ADCXL takes exactly 2 operands")
    }
}

func __asm_proxy_ADCXQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ADCXQ(v[0], v[1])
    } else {
        panic("instruction ADCXQ takes exactly 2 operands")
    }
}

func __asm_proxy_ADDB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ADDB(v[0], v[1])
    } else {
        panic("instruction ADDB takes exactly 2 operands")
    }
}

func __asm_proxy_ADDL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ADDL(v[0], v[1])
    } else {
        panic("instruction ADDL takes exactly 2 operands")
    }
}

func __asm_proxy_ADDPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ADDPD(v[0], v[1])
    } else {
        panic("instruction ADDPD takes exactly 2 operands")
    }
}

func __asm_proxy_ADDPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ADDPS(v[0], v[1])
    } else {
        panic("instruction ADDPS takes exactly 2 operands")
    }
}

func __asm_proxy_ADDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ADDQ(v[0], v[1])
    } else {
        panic("instruction ADDQ takes exactly 2 operands")
    }
}

func __asm_proxy_ADDSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ADDSD(v[0], v[1])
    } else {
        panic("instruction ADDSD takes exactly 2 operands")
    }
}

func __asm_proxy_ADDSS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ADDSS(v[0], v[1])
    } else {
        panic("instruction ADDSS takes exactly 2 operands")
    }
}

func __asm_proxy_ADDSUBPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ADDSUBPD(v[0], v[1])
    } else {
        panic("instruction ADDSUBPD takes exactly 2 operands")
    }
}

func __asm_proxy_ADDSUBPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ADDSUBPS(v[0], v[1])
    } else {
        panic("instruction ADDSUBPS takes exactly 2 operands")
    }
}

func __asm_proxy_ADDW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ADDW(v[0], v[1])
    } else {
        panic("instruction ADDW takes exactly 2 operands")
    }
}

func __asm_proxy_ADOXL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ADOXL(v[0], v[1])
    } else {
        panic("instruction ADOXL takes exactly 2 operands")
    }
}

func __asm_proxy_ADOXQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ADOXQ(v[0], v[1])
    } else {
        panic("instruction ADOXQ takes exactly 2 operands")
    }
}

func __asm_proxy_AESDEC__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.AESDEC(v[0], v[1])
    } else {
        panic("instruction AESDEC takes exactly 2 operands")
    }
}

func __asm_proxy_AESDECLAST__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.AESDECLAST(v[0], v[1])
    } else {
        panic("instruction AESDECLAST takes exactly 2 operands")
    }
}

func __asm_proxy_AESENC__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.AESENC(v[0], v[1])
    } else {
        panic("instruction AESENC takes exactly 2 operands")
    }
}

func __asm_proxy_AESENCLAST__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.AESENCLAST(v[0], v[1])
    } else {
        panic("instruction AESENCLAST takes exactly 2 operands")
    }
}

func __asm_proxy_AESIMC__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.AESIMC(v[0], v[1])
    } else {
        panic("instruction AESIMC takes exactly 2 operands")
    }
}

func __asm_proxy_AESKEYGENASSIST__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.AESKEYGENASSIST(v[0], v[1], v[2])
    } else {
        panic("instruction AESKEYGENASSIST takes exactly 3 operands")
    }
}

func __asm_proxy_ANDB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ANDB(v[0], v[1])
    } else {
        panic("instruction ANDB takes exactly 2 operands")
    }
}

func __asm_proxy_ANDL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ANDL(v[0], v[1])
    } else {
        panic("instruction ANDL takes exactly 2 operands")
    }
}

func __asm_proxy_ANDNL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.ANDNL(v[0], v[1], v[2])
    } else {
        panic("instruction ANDNL takes exactly 3 operands")
    }
}

func __asm_proxy_ANDNPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ANDNPD(v[0], v[1])
    } else {
        panic("instruction ANDNPD takes exactly 2 operands")
    }
}

func __asm_proxy_ANDNPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ANDNPS(v[0], v[1])
    } else {
        panic("instruction ANDNPS takes exactly 2 operands")
    }
}

func __asm_proxy_ANDNQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.ANDNQ(v[0], v[1], v[2])
    } else {
        panic("instruction ANDNQ takes exactly 3 operands")
    }
}

func __asm_proxy_ANDPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ANDPD(v[0], v[1])
    } else {
        panic("instruction ANDPD takes exactly 2 operands")
    }
}

func __asm_proxy_ANDPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ANDPS(v[0], v[1])
    } else {
        panic("instruction ANDPS takes exactly 2 operands")
    }
}

func __asm_proxy_ANDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ANDQ(v[0], v[1])
    } else {
        panic("instruction ANDQ takes exactly 2 operands")
    }
}

func __asm_proxy_ANDW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ANDW(v[0], v[1])
    } else {
        panic("instruction ANDW takes exactly 2 operands")
    }
}

func __asm_proxy_BEXTR__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.BEXTR(v[0], v[1], v[2])
    } else {
        panic("instruction BEXTR takes exactly 3 operands")
    }
}

func __asm_proxy_BLCFILL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BLCFILL(v[0], v[1])
    } else {
        panic("instruction BLCFILL takes exactly 2 operands")
    }
}

func __asm_proxy_BLCI__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BLCI(v[0], v[1])
    } else {
        panic("instruction BLCI takes exactly 2 operands")
    }
}

func __asm_proxy_BLCIC__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BLCIC(v[0], v[1])
    } else {
        panic("instruction BLCIC takes exactly 2 operands")
    }
}

func __asm_proxy_BLCMSK__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BLCMSK(v[0], v[1])
    } else {
        panic("instruction BLCMSK takes exactly 2 operands")
    }
}

func __asm_proxy_BLCS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BLCS(v[0], v[1])
    } else {
        panic("instruction BLCS takes exactly 2 operands")
    }
}

func __asm_proxy_BLENDPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.BLENDPD(v[0], v[1], v[2])
    } else {
        panic("instruction BLENDPD takes exactly 3 operands")
    }
}

func __asm_proxy_BLENDPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.BLENDPS(v[0], v[1], v[2])
    } else {
        panic("instruction BLENDPS takes exactly 3 operands")
    }
}

func __asm_proxy_BLENDVPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.BLENDVPD(v[0], v[1], v[2])
    } else {
        panic("instruction BLENDVPD takes exactly 3 operands")
    }
}

func __asm_proxy_BLENDVPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.BLENDVPS(v[0], v[1], v[2])
    } else {
        panic("instruction BLENDVPS takes exactly 3 operands")
    }
}

func __asm_proxy_BLSFILL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BLSFILL(v[0], v[1])
    } else {
        panic("instruction BLSFILL takes exactly 2 operands")
    }
}

func __asm_proxy_BLSI__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BLSI(v[0], v[1])
    } else {
        panic("instruction BLSI takes exactly 2 operands")
    }
}

func __asm_proxy_BLSIC__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BLSIC(v[0], v[1])
    } else {
        panic("instruction BLSIC takes exactly 2 operands")
    }
}

func __asm_proxy_BLSMSK__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BLSMSK(v[0], v[1])
    } else {
        panic("instruction BLSMSK takes exactly 2 operands")
    }
}

func __asm_proxy_BLSR__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BLSR(v[0], v[1])
    } else {
        panic("instruction BLSR takes exactly 2 operands")
    }
}

func __asm_proxy_BSFL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BSFL(v[0], v[1])
    } else {
        panic("instruction BSFL takes exactly 2 operands")
    }
}

func __asm_proxy_BSFQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BSFQ(v[0], v[1])
    } else {
        panic("instruction BSFQ takes exactly 2 operands")
    }
}

func __asm_proxy_BSFW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BSFW(v[0], v[1])
    } else {
        panic("instruction BSFW takes exactly 2 operands")
    }
}

func __asm_proxy_BSRL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BSRL(v[0], v[1])
    } else {
        panic("instruction BSRL takes exactly 2 operands")
    }
}

func __asm_proxy_BSRQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BSRQ(v[0], v[1])
    } else {
        panic("instruction BSRQ takes exactly 2 operands")
    }
}

func __asm_proxy_BSRW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BSRW(v[0], v[1])
    } else {
        panic("instruction BSRW takes exactly 2 operands")
    }
}

func __asm_proxy_BSWAPL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.BSWAPL(v[0])
    } else {
        panic("instruction BSWAPL takes exactly 1 operand")
    }
}

func __asm_proxy_BSWAPQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.BSWAPQ(v[0])
    } else {
        panic("instruction BSWAPQ takes exactly 1 operand")
    }
}

func __asm_proxy_BTCL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BTCL(v[0], v[1])
    } else {
        panic("instruction BTCL takes exactly 2 operands")
    }
}

func __asm_proxy_BTCQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BTCQ(v[0], v[1])
    } else {
        panic("instruction BTCQ takes exactly 2 operands")
    }
}

func __asm_proxy_BTCW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BTCW(v[0], v[1])
    } else {
        panic("instruction BTCW takes exactly 2 operands")
    }
}

func __asm_proxy_BTL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BTL(v[0], v[1])
    } else {
        panic("instruction BTL takes exactly 2 operands")
    }
}

func __asm_proxy_BTQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BTQ(v[0], v[1])
    } else {
        panic("instruction BTQ takes exactly 2 operands")
    }
}

func __asm_proxy_BTRL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BTRL(v[0], v[1])
    } else {
        panic("instruction BTRL takes exactly 2 operands")
    }
}

func __asm_proxy_BTRQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BTRQ(v[0], v[1])
    } else {
        panic("instruction BTRQ takes exactly 2 operands")
    }
}

func __asm_proxy_BTRW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BTRW(v[0], v[1])
    } else {
        panic("instruction BTRW takes exactly 2 operands")
    }
}

func __asm_proxy_BTSL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BTSL(v[0], v[1])
    } else {
        panic("instruction BTSL takes exactly 2 operands")
    }
}

func __asm_proxy_BTSQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BTSQ(v[0], v[1])
    } else {
        panic("instruction BTSQ takes exactly 2 operands")
    }
}

func __asm_proxy_BTSW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BTSW(v[0], v[1])
    } else {
        panic("instruction BTSW takes exactly 2 operands")
    }
}

func __asm_proxy_BTW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.BTW(v[0], v[1])
    } else {
        panic("instruction BTW takes exactly 2 operands")
    }
}

func __asm_proxy_BZHI__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.BZHI(v[0], v[1], v[2])
    } else {
        panic("instruction BZHI takes exactly 3 operands")
    }
}

func __asm_proxy_CALL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.CALL(v[0])
    } else {
        panic("instruction CALL takes exactly 1 operand")
    }
}

func __asm_proxy_CALLQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.CALLQ(v[0])
    } else {
        panic("instruction CALLQ takes exactly 1 operand")
    }
}

func __asm_proxy_CBTW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.CBTW()
    } else {
        panic("instruction CBTW takes no operands")
    }
}

func __asm_proxy_CLC__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.CLC()
    } else {
        panic("instruction CLC takes no operands")
    }
}

func __asm_proxy_CLD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.CLD()
    } else {
        panic("instruction CLD takes no operands")
    }
}

func __asm_proxy_CLFLUSH__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.CLFLUSH(v[0])
    } else {
        panic("instruction CLFLUSH takes exactly 1 operand")
    }
}

func __asm_proxy_CLFLUSHOPT__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.CLFLUSHOPT(v[0])
    } else {
        panic("instruction CLFLUSHOPT takes exactly 1 operand")
    }
}

func __asm_proxy_CLTD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.CLTD()
    } else {
        panic("instruction CLTD takes no operands")
    }
}

func __asm_proxy_CLTQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.CLTQ()
    } else {
        panic("instruction CLTQ takes no operands")
    }
}

func __asm_proxy_CLWB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.CLWB(v[0])
    } else {
        panic("instruction CLWB takes exactly 1 operand")
    }
}

func __asm_proxy_CLZERO__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.CLZERO()
    } else {
        panic("instruction CLZERO takes no operands")
    }
}

func __asm_proxy_CMC__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.CMC()
    } else {
        panic("instruction CMC takes no operands")
    }
}

func __asm_proxy_CMOVA__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVA(v[0], v[1])
    } else {
        panic("instruction CMOVA takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVAE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVAE(v[0], v[1])
    } else {
        panic("instruction CMOVAE takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVB(v[0], v[1])
    } else {
        panic("instruction CMOVB takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVBE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVBE(v[0], v[1])
    } else {
        panic("instruction CMOVBE takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVC__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVC(v[0], v[1])
    } else {
        panic("instruction CMOVC takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVE(v[0], v[1])
    } else {
        panic("instruction CMOVE takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVG__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVG(v[0], v[1])
    } else {
        panic("instruction CMOVG takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVGE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVGE(v[0], v[1])
    } else {
        panic("instruction CMOVGE takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVL(v[0], v[1])
    } else {
        panic("instruction CMOVL takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVLE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVLE(v[0], v[1])
    } else {
        panic("instruction CMOVLE takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVNA__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVNA(v[0], v[1])
    } else {
        panic("instruction CMOVNA takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVNAE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVNAE(v[0], v[1])
    } else {
        panic("instruction CMOVNAE takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVNB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVNB(v[0], v[1])
    } else {
        panic("instruction CMOVNB takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVNBE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVNBE(v[0], v[1])
    } else {
        panic("instruction CMOVNBE takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVNC__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVNC(v[0], v[1])
    } else {
        panic("instruction CMOVNC takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVNE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVNE(v[0], v[1])
    } else {
        panic("instruction CMOVNE takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVNG__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVNG(v[0], v[1])
    } else {
        panic("instruction CMOVNG takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVNGE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVNGE(v[0], v[1])
    } else {
        panic("instruction CMOVNGE takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVNL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVNL(v[0], v[1])
    } else {
        panic("instruction CMOVNL takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVNLE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVNLE(v[0], v[1])
    } else {
        panic("instruction CMOVNLE takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVNO__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVNO(v[0], v[1])
    } else {
        panic("instruction CMOVNO takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVNP__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVNP(v[0], v[1])
    } else {
        panic("instruction CMOVNP takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVNS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVNS(v[0], v[1])
    } else {
        panic("instruction CMOVNS takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVNZ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVNZ(v[0], v[1])
    } else {
        panic("instruction CMOVNZ takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVO__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVO(v[0], v[1])
    } else {
        panic("instruction CMOVO takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVP__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVP(v[0], v[1])
    } else {
        panic("instruction CMOVP takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVPE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVPE(v[0], v[1])
    } else {
        panic("instruction CMOVPE takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVPO__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVPO(v[0], v[1])
    } else {
        panic("instruction CMOVPO takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVS(v[0], v[1])
    } else {
        panic("instruction CMOVS takes exactly 2 operands")
    }
}

func __asm_proxy_CMOVZ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMOVZ(v[0], v[1])
    } else {
        panic("instruction CMOVZ takes exactly 2 operands")
    }
}

func __asm_proxy_CMPB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMPB(v[0], v[1])
    } else {
        panic("instruction CMPB takes exactly 2 operands")
    }
}

func __asm_proxy_CMPL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMPL(v[0], v[1])
    } else {
        panic("instruction CMPL takes exactly 2 operands")
    }
}

func __asm_proxy_CMPPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.CMPPD(v[0], v[1], v[2])
    } else {
        panic("instruction CMPPD takes exactly 3 operands")
    }
}

func __asm_proxy_CMPPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.CMPPS(v[0], v[1], v[2])
    } else {
        panic("instruction CMPPS takes exactly 3 operands")
    }
}

func __asm_proxy_CMPQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMPQ(v[0], v[1])
    } else {
        panic("instruction CMPQ takes exactly 2 operands")
    }
}

func __asm_proxy_CMPSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.CMPSD(v[0], v[1], v[2])
    } else {
        panic("instruction CMPSD takes exactly 3 operands")
    }
}

func __asm_proxy_CMPSS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.CMPSS(v[0], v[1], v[2])
    } else {
        panic("instruction CMPSS takes exactly 3 operands")
    }
}

func __asm_proxy_CMPW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMPW(v[0], v[1])
    } else {
        panic("instruction CMPW takes exactly 2 operands")
    }
}

func __asm_proxy_CMPXCHG16B__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.CMPXCHG16B(v[0])
    } else {
        panic("instruction CMPXCHG16B takes exactly 1 operand")
    }
}

func __asm_proxy_CMPXCHG8B__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.CMPXCHG8B(v[0])
    } else {
        panic("instruction CMPXCHG8B takes exactly 1 operand")
    }
}

func __asm_proxy_CMPXCHGB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMPXCHGB(v[0], v[1])
    } else {
        panic("instruction CMPXCHGB takes exactly 2 operands")
    }
}

func __asm_proxy_CMPXCHGL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMPXCHGL(v[0], v[1])
    } else {
        panic("instruction CMPXCHGL takes exactly 2 operands")
    }
}

func __asm_proxy_CMPXCHGQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMPXCHGQ(v[0], v[1])
    } else {
        panic("instruction CMPXCHGQ takes exactly 2 operands")
    }
}

func __asm_proxy_CMPXCHGW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CMPXCHGW(v[0], v[1])
    } else {
        panic("instruction CMPXCHGW takes exactly 2 operands")
    }
}

func __asm_proxy_COMISD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.COMISD(v[0], v[1])
    } else {
        panic("instruction COMISD takes exactly 2 operands")
    }
}

func __asm_proxy_COMISS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.COMISS(v[0], v[1])
    } else {
        panic("instruction COMISS takes exactly 2 operands")
    }
}

func __asm_proxy_CPUID__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.CPUID()
    } else {
        panic("instruction CPUID takes no operands")
    }
}

func __asm_proxy_CQTO__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.CQTO()
    } else {
        panic("instruction CQTO takes no operands")
    }
}

func __asm_proxy_CRC32B__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CRC32B(v[0], v[1])
    } else {
        panic("instruction CRC32B takes exactly 2 operands")
    }
}

func __asm_proxy_CRC32L__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CRC32L(v[0], v[1])
    } else {
        panic("instruction CRC32L takes exactly 2 operands")
    }
}

func __asm_proxy_CRC32Q__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CRC32Q(v[0], v[1])
    } else {
        panic("instruction CRC32Q takes exactly 2 operands")
    }
}

func __asm_proxy_CRC32W__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CRC32W(v[0], v[1])
    } else {
        panic("instruction CRC32W takes exactly 2 operands")
    }
}

func __asm_proxy_CVTDQ2PD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CVTDQ2PD(v[0], v[1])
    } else {
        panic("instruction CVTDQ2PD takes exactly 2 operands")
    }
}

func __asm_proxy_CVTDQ2PS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CVTDQ2PS(v[0], v[1])
    } else {
        panic("instruction CVTDQ2PS takes exactly 2 operands")
    }
}

func __asm_proxy_CVTPD2DQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CVTPD2DQ(v[0], v[1])
    } else {
        panic("instruction CVTPD2DQ takes exactly 2 operands")
    }
}

func __asm_proxy_CVTPD2PI__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CVTPD2PI(v[0], v[1])
    } else {
        panic("instruction CVTPD2PI takes exactly 2 operands")
    }
}

func __asm_proxy_CVTPD2PS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CVTPD2PS(v[0], v[1])
    } else {
        panic("instruction CVTPD2PS takes exactly 2 operands")
    }
}

func __asm_proxy_CVTPI2PD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CVTPI2PD(v[0], v[1])
    } else {
        panic("instruction CVTPI2PD takes exactly 2 operands")
    }
}

func __asm_proxy_CVTPI2PS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CVTPI2PS(v[0], v[1])
    } else {
        panic("instruction CVTPI2PS takes exactly 2 operands")
    }
}

func __asm_proxy_CVTPS2DQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CVTPS2DQ(v[0], v[1])
    } else {
        panic("instruction CVTPS2DQ takes exactly 2 operands")
    }
}

func __asm_proxy_CVTPS2PD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CVTPS2PD(v[0], v[1])
    } else {
        panic("instruction CVTPS2PD takes exactly 2 operands")
    }
}

func __asm_proxy_CVTPS2PI__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CVTPS2PI(v[0], v[1])
    } else {
        panic("instruction CVTPS2PI takes exactly 2 operands")
    }
}

func __asm_proxy_CVTSD2SI__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CVTSD2SI(v[0], v[1])
    } else {
        panic("instruction CVTSD2SI takes exactly 2 operands")
    }
}

func __asm_proxy_CVTSD2SS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CVTSD2SS(v[0], v[1])
    } else {
        panic("instruction CVTSD2SS takes exactly 2 operands")
    }
}

func __asm_proxy_CVTSI2SD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CVTSI2SD(v[0], v[1])
    } else {
        panic("instruction CVTSI2SD takes exactly 2 operands")
    }
}

func __asm_proxy_CVTSI2SS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CVTSI2SS(v[0], v[1])
    } else {
        panic("instruction CVTSI2SS takes exactly 2 operands")
    }
}

func __asm_proxy_CVTSS2SD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CVTSS2SD(v[0], v[1])
    } else {
        panic("instruction CVTSS2SD takes exactly 2 operands")
    }
}

func __asm_proxy_CVTSS2SI__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CVTSS2SI(v[0], v[1])
    } else {
        panic("instruction CVTSS2SI takes exactly 2 operands")
    }
}

func __asm_proxy_CVTTPD2DQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CVTTPD2DQ(v[0], v[1])
    } else {
        panic("instruction CVTTPD2DQ takes exactly 2 operands")
    }
}

func __asm_proxy_CVTTPD2PI__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CVTTPD2PI(v[0], v[1])
    } else {
        panic("instruction CVTTPD2PI takes exactly 2 operands")
    }
}

func __asm_proxy_CVTTPS2DQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CVTTPS2DQ(v[0], v[1])
    } else {
        panic("instruction CVTTPS2DQ takes exactly 2 operands")
    }
}

func __asm_proxy_CVTTPS2PI__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CVTTPS2PI(v[0], v[1])
    } else {
        panic("instruction CVTTPS2PI takes exactly 2 operands")
    }
}

func __asm_proxy_CVTTSD2SI__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CVTTSD2SI(v[0], v[1])
    } else {
        panic("instruction CVTTSD2SI takes exactly 2 operands")
    }
}

func __asm_proxy_CVTTSS2SI__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.CVTTSS2SI(v[0], v[1])
    } else {
        panic("instruction CVTTSS2SI takes exactly 2 operands")
    }
}

func __asm_proxy_CWTD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.CWTD()
    } else {
        panic("instruction CWTD takes no operands")
    }
}

func __asm_proxy_CWTL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.CWTL()
    } else {
        panic("instruction CWTL takes no operands")
    }
}

func __asm_proxy_DECB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.DECB(v[0])
    } else {
        panic("instruction DECB takes exactly 1 operand")
    }
}

func __asm_proxy_DECL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.DECL(v[0])
    } else {
        panic("instruction DECL takes exactly 1 operand")
    }
}

func __asm_proxy_DECQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.DECQ(v[0])
    } else {
        panic("instruction DECQ takes exactly 1 operand")
    }
}

func __asm_proxy_DECW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.DECW(v[0])
    } else {
        panic("instruction DECW takes exactly 1 operand")
    }
}

func __asm_proxy_DIVB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.DIVB(v[0])
    } else {
        panic("instruction DIVB takes exactly 1 operand")
    }
}

func __asm_proxy_DIVL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.DIVL(v[0])
    } else {
        panic("instruction DIVL takes exactly 1 operand")
    }
}

func __asm_proxy_DIVPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.DIVPD(v[0], v[1])
    } else {
        panic("instruction DIVPD takes exactly 2 operands")
    }
}

func __asm_proxy_DIVPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.DIVPS(v[0], v[1])
    } else {
        panic("instruction DIVPS takes exactly 2 operands")
    }
}

func __asm_proxy_DIVQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.DIVQ(v[0])
    } else {
        panic("instruction DIVQ takes exactly 1 operand")
    }
}

func __asm_proxy_DIVSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.DIVSD(v[0], v[1])
    } else {
        panic("instruction DIVSD takes exactly 2 operands")
    }
}

func __asm_proxy_DIVSS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.DIVSS(v[0], v[1])
    } else {
        panic("instruction DIVSS takes exactly 2 operands")
    }
}

func __asm_proxy_DIVW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.DIVW(v[0])
    } else {
        panic("instruction DIVW takes exactly 1 operand")
    }
}

func __asm_proxy_DPPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.DPPD(v[0], v[1], v[2])
    } else {
        panic("instruction DPPD takes exactly 3 operands")
    }
}

func __asm_proxy_DPPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.DPPS(v[0], v[1], v[2])
    } else {
        panic("instruction DPPS takes exactly 3 operands")
    }
}

func __asm_proxy_EMMS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.EMMS()
    } else {
        panic("instruction EMMS takes no operands")
    }
}

func __asm_proxy_EXTRACTPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.EXTRACTPS(v[0], v[1], v[2])
    } else {
        panic("instruction EXTRACTPS takes exactly 3 operands")
    }
}

func __asm_proxy_EXTRQ__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.EXTRQ(v[0], v[1])
        case 3  : return p.EXTRQ(v[0], v[1], v[2])
        default : panic("instruction EXTRQ takes 2 or 3 operands")
    }
}

func __asm_proxy_FEMMS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.FEMMS()
    } else {
        panic("instruction FEMMS takes no operands")
    }
}

func __asm_proxy_HADDPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.HADDPD(v[0], v[1])
    } else {
        panic("instruction HADDPD takes exactly 2 operands")
    }
}

func __asm_proxy_HADDPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.HADDPS(v[0], v[1])
    } else {
        panic("instruction HADDPS takes exactly 2 operands")
    }
}

func __asm_proxy_HSUBPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.HSUBPD(v[0], v[1])
    } else {
        panic("instruction HSUBPD takes exactly 2 operands")
    }
}

func __asm_proxy_HSUBPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.HSUBPS(v[0], v[1])
    } else {
        panic("instruction HSUBPS takes exactly 2 operands")
    }
}

func __asm_proxy_IDIVB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.IDIVB(v[0])
    } else {
        panic("instruction IDIVB takes exactly 1 operand")
    }
}

func __asm_proxy_IDIVL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.IDIVL(v[0])
    } else {
        panic("instruction IDIVL takes exactly 1 operand")
    }
}

func __asm_proxy_IDIVQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.IDIVQ(v[0])
    } else {
        panic("instruction IDIVQ takes exactly 1 operand")
    }
}

func __asm_proxy_IDIVW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.IDIVW(v[0])
    } else {
        panic("instruction IDIVW takes exactly 1 operand")
    }
}

func __asm_proxy_IMULB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.IMULB(v[0])
    } else {
        panic("instruction IMULB takes exactly 1 operand")
    }
}

func __asm_proxy_IMULL__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 1  : return p.IMULL(v[0])
        case 2  : return p.IMULL(v[0], v[1])
        case 3  : return p.IMULL(v[0], v[1], v[2])
        default : panic("instruction IMULL takes 1 or 2 or 3 operands")
    }
}

func __asm_proxy_IMULQ__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 1  : return p.IMULQ(v[0])
        case 2  : return p.IMULQ(v[0], v[1])
        case 3  : return p.IMULQ(v[0], v[1], v[2])
        default : panic("instruction IMULQ takes 1 or 2 or 3 operands")
    }
}

func __asm_proxy_IMULW__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 1  : return p.IMULW(v[0])
        case 2  : return p.IMULW(v[0], v[1])
        case 3  : return p.IMULW(v[0], v[1], v[2])
        default : panic("instruction IMULW takes 1 or 2 or 3 operands")
    }
}

func __asm_proxy_INCB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.INCB(v[0])
    } else {
        panic("instruction INCB takes exactly 1 operand")
    }
}

func __asm_proxy_INCL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.INCL(v[0])
    } else {
        panic("instruction INCL takes exactly 1 operand")
    }
}

func __asm_proxy_INCQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.INCQ(v[0])
    } else {
        panic("instruction INCQ takes exactly 1 operand")
    }
}

func __asm_proxy_INCW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.INCW(v[0])
    } else {
        panic("instruction INCW takes exactly 1 operand")
    }
}

func __asm_proxy_INSERTPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.INSERTPS(v[0], v[1], v[2])
    } else {
        panic("instruction INSERTPS takes exactly 3 operands")
    }
}

func __asm_proxy_INSERTQ__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.INSERTQ(v[0], v[1])
        case 4  : return p.INSERTQ(v[0], v[1], v[2], v[3])
        default : panic("instruction INSERTQ takes 2 or 4 operands")
    }
}

func __asm_proxy_INT__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.INT(v[0])
    } else {
        panic("instruction INT takes exactly 1 operand")
    }
}

func __asm_proxy_JA__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JA(v[0])
    } else {
        panic("instruction JA takes exactly 1 operand")
    }
}

func __asm_proxy_JAE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JAE(v[0])
    } else {
        panic("instruction JAE takes exactly 1 operand")
    }
}

func __asm_proxy_JB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JB(v[0])
    } else {
        panic("instruction JB takes exactly 1 operand")
    }
}

func __asm_proxy_JBE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JBE(v[0])
    } else {
        panic("instruction JBE takes exactly 1 operand")
    }
}

func __asm_proxy_JC__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JC(v[0])
    } else {
        panic("instruction JC takes exactly 1 operand")
    }
}

func __asm_proxy_JE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JE(v[0])
    } else {
        panic("instruction JE takes exactly 1 operand")
    }
}

func __asm_proxy_JECXZ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JECXZ(v[0])
    } else {
        panic("instruction JECXZ takes exactly 1 operand")
    }
}

func __asm_proxy_JG__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JG(v[0])
    } else {
        panic("instruction JG takes exactly 1 operand")
    }
}

func __asm_proxy_JGE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JGE(v[0])
    } else {
        panic("instruction JGE takes exactly 1 operand")
    }
}

func __asm_proxy_JL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JL(v[0])
    } else {
        panic("instruction JL takes exactly 1 operand")
    }
}

func __asm_proxy_JLE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JLE(v[0])
    } else {
        panic("instruction JLE takes exactly 1 operand")
    }
}

func __asm_proxy_JMP__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JMP(v[0])
    } else {
        panic("instruction JMP takes exactly 1 operand")
    }
}

func __asm_proxy_JMPQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JMPQ(v[0])
    } else {
        panic("instruction JMPQ takes exactly 1 operand")
    }
}

func __asm_proxy_JNA__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JNA(v[0])
    } else {
        panic("instruction JNA takes exactly 1 operand")
    }
}

func __asm_proxy_JNAE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JNAE(v[0])
    } else {
        panic("instruction JNAE takes exactly 1 operand")
    }
}

func __asm_proxy_JNB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JNB(v[0])
    } else {
        panic("instruction JNB takes exactly 1 operand")
    }
}

func __asm_proxy_JNBE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JNBE(v[0])
    } else {
        panic("instruction JNBE takes exactly 1 operand")
    }
}

func __asm_proxy_JNC__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JNC(v[0])
    } else {
        panic("instruction JNC takes exactly 1 operand")
    }
}

func __asm_proxy_JNE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JNE(v[0])
    } else {
        panic("instruction JNE takes exactly 1 operand")
    }
}

func __asm_proxy_JNG__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JNG(v[0])
    } else {
        panic("instruction JNG takes exactly 1 operand")
    }
}

func __asm_proxy_JNGE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JNGE(v[0])
    } else {
        panic("instruction JNGE takes exactly 1 operand")
    }
}

func __asm_proxy_JNL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JNL(v[0])
    } else {
        panic("instruction JNL takes exactly 1 operand")
    }
}

func __asm_proxy_JNLE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JNLE(v[0])
    } else {
        panic("instruction JNLE takes exactly 1 operand")
    }
}

func __asm_proxy_JNO__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JNO(v[0])
    } else {
        panic("instruction JNO takes exactly 1 operand")
    }
}

func __asm_proxy_JNP__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JNP(v[0])
    } else {
        panic("instruction JNP takes exactly 1 operand")
    }
}

func __asm_proxy_JNS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JNS(v[0])
    } else {
        panic("instruction JNS takes exactly 1 operand")
    }
}

func __asm_proxy_JNZ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JNZ(v[0])
    } else {
        panic("instruction JNZ takes exactly 1 operand")
    }
}

func __asm_proxy_JO__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JO(v[0])
    } else {
        panic("instruction JO takes exactly 1 operand")
    }
}

func __asm_proxy_JP__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JP(v[0])
    } else {
        panic("instruction JP takes exactly 1 operand")
    }
}

func __asm_proxy_JPE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JPE(v[0])
    } else {
        panic("instruction JPE takes exactly 1 operand")
    }
}

func __asm_proxy_JPO__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JPO(v[0])
    } else {
        panic("instruction JPO takes exactly 1 operand")
    }
}

func __asm_proxy_JRCXZ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JRCXZ(v[0])
    } else {
        panic("instruction JRCXZ takes exactly 1 operand")
    }
}

func __asm_proxy_JS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JS(v[0])
    } else {
        panic("instruction JS takes exactly 1 operand")
    }
}

func __asm_proxy_JZ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.JZ(v[0])
    } else {
        panic("instruction JZ takes exactly 1 operand")
    }
}

func __asm_proxy_KADDB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KADDB(v[0], v[1], v[2])
    } else {
        panic("instruction KADDB takes exactly 3 operands")
    }
}

func __asm_proxy_KADDD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KADDD(v[0], v[1], v[2])
    } else {
        panic("instruction KADDD takes exactly 3 operands")
    }
}

func __asm_proxy_KADDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KADDQ(v[0], v[1], v[2])
    } else {
        panic("instruction KADDQ takes exactly 3 operands")
    }
}

func __asm_proxy_KADDW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KADDW(v[0], v[1], v[2])
    } else {
        panic("instruction KADDW takes exactly 3 operands")
    }
}

func __asm_proxy_KANDB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KANDB(v[0], v[1], v[2])
    } else {
        panic("instruction KANDB takes exactly 3 operands")
    }
}

func __asm_proxy_KANDD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KANDD(v[0], v[1], v[2])
    } else {
        panic("instruction KANDD takes exactly 3 operands")
    }
}

func __asm_proxy_KANDNB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KANDNB(v[0], v[1], v[2])
    } else {
        panic("instruction KANDNB takes exactly 3 operands")
    }
}

func __asm_proxy_KANDND__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KANDND(v[0], v[1], v[2])
    } else {
        panic("instruction KANDND takes exactly 3 operands")
    }
}

func __asm_proxy_KANDNQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KANDNQ(v[0], v[1], v[2])
    } else {
        panic("instruction KANDNQ takes exactly 3 operands")
    }
}

func __asm_proxy_KANDNW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KANDNW(v[0], v[1], v[2])
    } else {
        panic("instruction KANDNW takes exactly 3 operands")
    }
}

func __asm_proxy_KANDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KANDQ(v[0], v[1], v[2])
    } else {
        panic("instruction KANDQ takes exactly 3 operands")
    }
}

func __asm_proxy_KANDW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KANDW(v[0], v[1], v[2])
    } else {
        panic("instruction KANDW takes exactly 3 operands")
    }
}

func __asm_proxy_KMOVB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.KMOVB(v[0], v[1])
    } else {
        panic("instruction KMOVB takes exactly 2 operands")
    }
}

func __asm_proxy_KMOVD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.KMOVD(v[0], v[1])
    } else {
        panic("instruction KMOVD takes exactly 2 operands")
    }
}

func __asm_proxy_KMOVQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.KMOVQ(v[0], v[1])
    } else {
        panic("instruction KMOVQ takes exactly 2 operands")
    }
}

func __asm_proxy_KMOVW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.KMOVW(v[0], v[1])
    } else {
        panic("instruction KMOVW takes exactly 2 operands")
    }
}

func __asm_proxy_KNOTB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.KNOTB(v[0], v[1])
    } else {
        panic("instruction KNOTB takes exactly 2 operands")
    }
}

func __asm_proxy_KNOTD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.KNOTD(v[0], v[1])
    } else {
        panic("instruction KNOTD takes exactly 2 operands")
    }
}

func __asm_proxy_KNOTQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.KNOTQ(v[0], v[1])
    } else {
        panic("instruction KNOTQ takes exactly 2 operands")
    }
}

func __asm_proxy_KNOTW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.KNOTW(v[0], v[1])
    } else {
        panic("instruction KNOTW takes exactly 2 operands")
    }
}

func __asm_proxy_KORB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KORB(v[0], v[1], v[2])
    } else {
        panic("instruction KORB takes exactly 3 operands")
    }
}

func __asm_proxy_KORD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KORD(v[0], v[1], v[2])
    } else {
        panic("instruction KORD takes exactly 3 operands")
    }
}

func __asm_proxy_KORQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KORQ(v[0], v[1], v[2])
    } else {
        panic("instruction KORQ takes exactly 3 operands")
    }
}

func __asm_proxy_KORTESTB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.KORTESTB(v[0], v[1])
    } else {
        panic("instruction KORTESTB takes exactly 2 operands")
    }
}

func __asm_proxy_KORTESTD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.KORTESTD(v[0], v[1])
    } else {
        panic("instruction KORTESTD takes exactly 2 operands")
    }
}

func __asm_proxy_KORTESTQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.KORTESTQ(v[0], v[1])
    } else {
        panic("instruction KORTESTQ takes exactly 2 operands")
    }
}

func __asm_proxy_KORTESTW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.KORTESTW(v[0], v[1])
    } else {
        panic("instruction KORTESTW takes exactly 2 operands")
    }
}

func __asm_proxy_KORW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KORW(v[0], v[1], v[2])
    } else {
        panic("instruction KORW takes exactly 3 operands")
    }
}

func __asm_proxy_KSHIFTLB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KSHIFTLB(v[0], v[1], v[2])
    } else {
        panic("instruction KSHIFTLB takes exactly 3 operands")
    }
}

func __asm_proxy_KSHIFTLD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KSHIFTLD(v[0], v[1], v[2])
    } else {
        panic("instruction KSHIFTLD takes exactly 3 operands")
    }
}

func __asm_proxy_KSHIFTLQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KSHIFTLQ(v[0], v[1], v[2])
    } else {
        panic("instruction KSHIFTLQ takes exactly 3 operands")
    }
}

func __asm_proxy_KSHIFTLW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KSHIFTLW(v[0], v[1], v[2])
    } else {
        panic("instruction KSHIFTLW takes exactly 3 operands")
    }
}

func __asm_proxy_KSHIFTRB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KSHIFTRB(v[0], v[1], v[2])
    } else {
        panic("instruction KSHIFTRB takes exactly 3 operands")
    }
}

func __asm_proxy_KSHIFTRD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KSHIFTRD(v[0], v[1], v[2])
    } else {
        panic("instruction KSHIFTRD takes exactly 3 operands")
    }
}

func __asm_proxy_KSHIFTRQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KSHIFTRQ(v[0], v[1], v[2])
    } else {
        panic("instruction KSHIFTRQ takes exactly 3 operands")
    }
}

func __asm_proxy_KSHIFTRW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KSHIFTRW(v[0], v[1], v[2])
    } else {
        panic("instruction KSHIFTRW takes exactly 3 operands")
    }
}

func __asm_proxy_KTESTB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.KTESTB(v[0], v[1])
    } else {
        panic("instruction KTESTB takes exactly 2 operands")
    }
}

func __asm_proxy_KTESTD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.KTESTD(v[0], v[1])
    } else {
        panic("instruction KTESTD takes exactly 2 operands")
    }
}

func __asm_proxy_KTESTQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.KTESTQ(v[0], v[1])
    } else {
        panic("instruction KTESTQ takes exactly 2 operands")
    }
}

func __asm_proxy_KTESTW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.KTESTW(v[0], v[1])
    } else {
        panic("instruction KTESTW takes exactly 2 operands")
    }
}

func __asm_proxy_KUNPCKBW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KUNPCKBW(v[0], v[1], v[2])
    } else {
        panic("instruction KUNPCKBW takes exactly 3 operands")
    }
}

func __asm_proxy_KUNPCKDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KUNPCKDQ(v[0], v[1], v[2])
    } else {
        panic("instruction KUNPCKDQ takes exactly 3 operands")
    }
}

func __asm_proxy_KUNPCKWD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KUNPCKWD(v[0], v[1], v[2])
    } else {
        panic("instruction KUNPCKWD takes exactly 3 operands")
    }
}

func __asm_proxy_KXNORB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KXNORB(v[0], v[1], v[2])
    } else {
        panic("instruction KXNORB takes exactly 3 operands")
    }
}

func __asm_proxy_KXNORD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KXNORD(v[0], v[1], v[2])
    } else {
        panic("instruction KXNORD takes exactly 3 operands")
    }
}

func __asm_proxy_KXNORQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KXNORQ(v[0], v[1], v[2])
    } else {
        panic("instruction KXNORQ takes exactly 3 operands")
    }
}

func __asm_proxy_KXNORW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KXNORW(v[0], v[1], v[2])
    } else {
        panic("instruction KXNORW takes exactly 3 operands")
    }
}

func __asm_proxy_KXORB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KXORB(v[0], v[1], v[2])
    } else {
        panic("instruction KXORB takes exactly 3 operands")
    }
}

func __asm_proxy_KXORD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KXORD(v[0], v[1], v[2])
    } else {
        panic("instruction KXORD takes exactly 3 operands")
    }
}

func __asm_proxy_KXORQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KXORQ(v[0], v[1], v[2])
    } else {
        panic("instruction KXORQ takes exactly 3 operands")
    }
}

func __asm_proxy_KXORW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.KXORW(v[0], v[1], v[2])
    } else {
        panic("instruction KXORW takes exactly 3 operands")
    }
}

func __asm_proxy_LDDQU__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.LDDQU(v[0], v[1])
    } else {
        panic("instruction LDDQU takes exactly 2 operands")
    }
}

func __asm_proxy_LDMXCSR__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.LDMXCSR(v[0])
    } else {
        panic("instruction LDMXCSR takes exactly 1 operand")
    }
}

func __asm_proxy_LEAL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.LEAL(v[0], v[1])
    } else {
        panic("instruction LEAL takes exactly 2 operands")
    }
}

func __asm_proxy_LEAQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.LEAQ(v[0], v[1])
    } else {
        panic("instruction LEAQ takes exactly 2 operands")
    }
}

func __asm_proxy_LEAW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.LEAW(v[0], v[1])
    } else {
        panic("instruction LEAW takes exactly 2 operands")
    }
}

func __asm_proxy_LFENCE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.LFENCE()
    } else {
        panic("instruction LFENCE takes no operands")
    }
}

func __asm_proxy_LZCNTL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.LZCNTL(v[0], v[1])
    } else {
        panic("instruction LZCNTL takes exactly 2 operands")
    }
}

func __asm_proxy_LZCNTQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.LZCNTQ(v[0], v[1])
    } else {
        panic("instruction LZCNTQ takes exactly 2 operands")
    }
}

func __asm_proxy_LZCNTW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.LZCNTW(v[0], v[1])
    } else {
        panic("instruction LZCNTW takes exactly 2 operands")
    }
}

func __asm_proxy_MASKMOVDQU__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MASKMOVDQU(v[0], v[1])
    } else {
        panic("instruction MASKMOVDQU takes exactly 2 operands")
    }
}

func __asm_proxy_MASKMOVQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MASKMOVQ(v[0], v[1])
    } else {
        panic("instruction MASKMOVQ takes exactly 2 operands")
    }
}

func __asm_proxy_MAXPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MAXPD(v[0], v[1])
    } else {
        panic("instruction MAXPD takes exactly 2 operands")
    }
}

func __asm_proxy_MAXPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MAXPS(v[0], v[1])
    } else {
        panic("instruction MAXPS takes exactly 2 operands")
    }
}

func __asm_proxy_MAXSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MAXSD(v[0], v[1])
    } else {
        panic("instruction MAXSD takes exactly 2 operands")
    }
}

func __asm_proxy_MAXSS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MAXSS(v[0], v[1])
    } else {
        panic("instruction MAXSS takes exactly 2 operands")
    }
}

func __asm_proxy_MFENCE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.MFENCE()
    } else {
        panic("instruction MFENCE takes no operands")
    }
}

func __asm_proxy_MINPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MINPD(v[0], v[1])
    } else {
        panic("instruction MINPD takes exactly 2 operands")
    }
}

func __asm_proxy_MINPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MINPS(v[0], v[1])
    } else {
        panic("instruction MINPS takes exactly 2 operands")
    }
}

func __asm_proxy_MINSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MINSD(v[0], v[1])
    } else {
        panic("instruction MINSD takes exactly 2 operands")
    }
}

func __asm_proxy_MINSS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MINSS(v[0], v[1])
    } else {
        panic("instruction MINSS takes exactly 2 operands")
    }
}

func __asm_proxy_MONITOR__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.MONITOR()
    } else {
        panic("instruction MONITOR takes no operands")
    }
}

func __asm_proxy_MONITORX__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.MONITORX()
    } else {
        panic("instruction MONITORX takes no operands")
    }
}

func __asm_proxy_MOVAPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVAPD(v[0], v[1])
    } else {
        panic("instruction MOVAPD takes exactly 2 operands")
    }
}

func __asm_proxy_MOVAPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVAPS(v[0], v[1])
    } else {
        panic("instruction MOVAPS takes exactly 2 operands")
    }
}

func __asm_proxy_MOVB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVB(v[0], v[1])
    } else {
        panic("instruction MOVB takes exactly 2 operands")
    }
}

func __asm_proxy_MOVBEL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVBEL(v[0], v[1])
    } else {
        panic("instruction MOVBEL takes exactly 2 operands")
    }
}

func __asm_proxy_MOVBEQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVBEQ(v[0], v[1])
    } else {
        panic("instruction MOVBEQ takes exactly 2 operands")
    }
}

func __asm_proxy_MOVBEW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVBEW(v[0], v[1])
    } else {
        panic("instruction MOVBEW takes exactly 2 operands")
    }
}

func __asm_proxy_MOVD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVD(v[0], v[1])
    } else {
        panic("instruction MOVD takes exactly 2 operands")
    }
}

func __asm_proxy_MOVDDUP__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVDDUP(v[0], v[1])
    } else {
        panic("instruction MOVDDUP takes exactly 2 operands")
    }
}

func __asm_proxy_MOVDQ2Q__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVDQ2Q(v[0], v[1])
    } else {
        panic("instruction MOVDQ2Q takes exactly 2 operands")
    }
}

func __asm_proxy_MOVDQA__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVDQA(v[0], v[1])
    } else {
        panic("instruction MOVDQA takes exactly 2 operands")
    }
}

func __asm_proxy_MOVDQU__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVDQU(v[0], v[1])
    } else {
        panic("instruction MOVDQU takes exactly 2 operands")
    }
}

func __asm_proxy_MOVHLPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVHLPS(v[0], v[1])
    } else {
        panic("instruction MOVHLPS takes exactly 2 operands")
    }
}

func __asm_proxy_MOVHPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVHPD(v[0], v[1])
    } else {
        panic("instruction MOVHPD takes exactly 2 operands")
    }
}

func __asm_proxy_MOVHPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVHPS(v[0], v[1])
    } else {
        panic("instruction MOVHPS takes exactly 2 operands")
    }
}

func __asm_proxy_MOVL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVL(v[0], v[1])
    } else {
        panic("instruction MOVL takes exactly 2 operands")
    }
}

func __asm_proxy_MOVLHPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVLHPS(v[0], v[1])
    } else {
        panic("instruction MOVLHPS takes exactly 2 operands")
    }
}

func __asm_proxy_MOVLPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVLPD(v[0], v[1])
    } else {
        panic("instruction MOVLPD takes exactly 2 operands")
    }
}

func __asm_proxy_MOVLPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVLPS(v[0], v[1])
    } else {
        panic("instruction MOVLPS takes exactly 2 operands")
    }
}

func __asm_proxy_MOVMSKPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVMSKPD(v[0], v[1])
    } else {
        panic("instruction MOVMSKPD takes exactly 2 operands")
    }
}

func __asm_proxy_MOVMSKPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVMSKPS(v[0], v[1])
    } else {
        panic("instruction MOVMSKPS takes exactly 2 operands")
    }
}

func __asm_proxy_MOVNTDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVNTDQ(v[0], v[1])
    } else {
        panic("instruction MOVNTDQ takes exactly 2 operands")
    }
}

func __asm_proxy_MOVNTDQA__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVNTDQA(v[0], v[1])
    } else {
        panic("instruction MOVNTDQA takes exactly 2 operands")
    }
}

func __asm_proxy_MOVNTIL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVNTIL(v[0], v[1])
    } else {
        panic("instruction MOVNTIL takes exactly 2 operands")
    }
}

func __asm_proxy_MOVNTIQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVNTIQ(v[0], v[1])
    } else {
        panic("instruction MOVNTIQ takes exactly 2 operands")
    }
}

func __asm_proxy_MOVNTPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVNTPD(v[0], v[1])
    } else {
        panic("instruction MOVNTPD takes exactly 2 operands")
    }
}

func __asm_proxy_MOVNTPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVNTPS(v[0], v[1])
    } else {
        panic("instruction MOVNTPS takes exactly 2 operands")
    }
}

func __asm_proxy_MOVNTQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVNTQ(v[0], v[1])
    } else {
        panic("instruction MOVNTQ takes exactly 2 operands")
    }
}

func __asm_proxy_MOVNTSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVNTSD(v[0], v[1])
    } else {
        panic("instruction MOVNTSD takes exactly 2 operands")
    }
}

func __asm_proxy_MOVNTSS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVNTSS(v[0], v[1])
    } else {
        panic("instruction MOVNTSS takes exactly 2 operands")
    }
}

func __asm_proxy_MOVQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVQ(v[0], v[1])
    } else {
        panic("instruction MOVQ takes exactly 2 operands")
    }
}

func __asm_proxy_MOVQ2DQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVQ2DQ(v[0], v[1])
    } else {
        panic("instruction MOVQ2DQ takes exactly 2 operands")
    }
}

func __asm_proxy_MOVSBL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVSBL(v[0], v[1])
    } else {
        panic("instruction MOVSBL takes exactly 2 operands")
    }
}

func __asm_proxy_MOVSBQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVSBQ(v[0], v[1])
    } else {
        panic("instruction MOVSBQ takes exactly 2 operands")
    }
}

func __asm_proxy_MOVSBW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVSBW(v[0], v[1])
    } else {
        panic("instruction MOVSBW takes exactly 2 operands")
    }
}

func __asm_proxy_MOVSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVSD(v[0], v[1])
    } else {
        panic("instruction MOVSD takes exactly 2 operands")
    }
}

func __asm_proxy_MOVSHDUP__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVSHDUP(v[0], v[1])
    } else {
        panic("instruction MOVSHDUP takes exactly 2 operands")
    }
}

func __asm_proxy_MOVSLDUP__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVSLDUP(v[0], v[1])
    } else {
        panic("instruction MOVSLDUP takes exactly 2 operands")
    }
}

func __asm_proxy_MOVSLQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVSLQ(v[0], v[1])
    } else {
        panic("instruction MOVSLQ takes exactly 2 operands")
    }
}

func __asm_proxy_MOVSS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVSS(v[0], v[1])
    } else {
        panic("instruction MOVSS takes exactly 2 operands")
    }
}

func __asm_proxy_MOVSWL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVSWL(v[0], v[1])
    } else {
        panic("instruction MOVSWL takes exactly 2 operands")
    }
}

func __asm_proxy_MOVSWQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVSWQ(v[0], v[1])
    } else {
        panic("instruction MOVSWQ takes exactly 2 operands")
    }
}

func __asm_proxy_MOVUPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVUPD(v[0], v[1])
    } else {
        panic("instruction MOVUPD takes exactly 2 operands")
    }
}

func __asm_proxy_MOVUPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVUPS(v[0], v[1])
    } else {
        panic("instruction MOVUPS takes exactly 2 operands")
    }
}

func __asm_proxy_MOVW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVW(v[0], v[1])
    } else {
        panic("instruction MOVW takes exactly 2 operands")
    }
}

func __asm_proxy_MOVZBL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVZBL(v[0], v[1])
    } else {
        panic("instruction MOVZBL takes exactly 2 operands")
    }
}

func __asm_proxy_MOVZBQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVZBQ(v[0], v[1])
    } else {
        panic("instruction MOVZBQ takes exactly 2 operands")
    }
}

func __asm_proxy_MOVZBW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVZBW(v[0], v[1])
    } else {
        panic("instruction MOVZBW takes exactly 2 operands")
    }
}

func __asm_proxy_MOVZWL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVZWL(v[0], v[1])
    } else {
        panic("instruction MOVZWL takes exactly 2 operands")
    }
}

func __asm_proxy_MOVZWQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MOVZWQ(v[0], v[1])
    } else {
        panic("instruction MOVZWQ takes exactly 2 operands")
    }
}

func __asm_proxy_MPSADBW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.MPSADBW(v[0], v[1], v[2])
    } else {
        panic("instruction MPSADBW takes exactly 3 operands")
    }
}

func __asm_proxy_MULB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.MULB(v[0])
    } else {
        panic("instruction MULB takes exactly 1 operand")
    }
}

func __asm_proxy_MULL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.MULL(v[0])
    } else {
        panic("instruction MULL takes exactly 1 operand")
    }
}

func __asm_proxy_MULPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MULPD(v[0], v[1])
    } else {
        panic("instruction MULPD takes exactly 2 operands")
    }
}

func __asm_proxy_MULPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MULPS(v[0], v[1])
    } else {
        panic("instruction MULPS takes exactly 2 operands")
    }
}

func __asm_proxy_MULQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.MULQ(v[0])
    } else {
        panic("instruction MULQ takes exactly 1 operand")
    }
}

func __asm_proxy_MULSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MULSD(v[0], v[1])
    } else {
        panic("instruction MULSD takes exactly 2 operands")
    }
}

func __asm_proxy_MULSS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.MULSS(v[0], v[1])
    } else {
        panic("instruction MULSS takes exactly 2 operands")
    }
}

func __asm_proxy_MULW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.MULW(v[0])
    } else {
        panic("instruction MULW takes exactly 1 operand")
    }
}

func __asm_proxy_MULXL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.MULXL(v[0], v[1], v[2])
    } else {
        panic("instruction MULXL takes exactly 3 operands")
    }
}

func __asm_proxy_MULXQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.MULXQ(v[0], v[1], v[2])
    } else {
        panic("instruction MULXQ takes exactly 3 operands")
    }
}

func __asm_proxy_MWAIT__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.MWAIT()
    } else {
        panic("instruction MWAIT takes no operands")
    }
}

func __asm_proxy_MWAITX__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.MWAITX()
    } else {
        panic("instruction MWAITX takes no operands")
    }
}

func __asm_proxy_NEGB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.NEGB(v[0])
    } else {
        panic("instruction NEGB takes exactly 1 operand")
    }
}

func __asm_proxy_NEGL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.NEGL(v[0])
    } else {
        panic("instruction NEGL takes exactly 1 operand")
    }
}

func __asm_proxy_NEGQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.NEGQ(v[0])
    } else {
        panic("instruction NEGQ takes exactly 1 operand")
    }
}

func __asm_proxy_NEGW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.NEGW(v[0])
    } else {
        panic("instruction NEGW takes exactly 1 operand")
    }
}

func __asm_proxy_NOP__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.NOP()
    } else {
        panic("instruction NOP takes no operands")
    }
}

func __asm_proxy_NOTB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.NOTB(v[0])
    } else {
        panic("instruction NOTB takes exactly 1 operand")
    }
}

func __asm_proxy_NOTL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.NOTL(v[0])
    } else {
        panic("instruction NOTL takes exactly 1 operand")
    }
}

func __asm_proxy_NOTQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.NOTQ(v[0])
    } else {
        panic("instruction NOTQ takes exactly 1 operand")
    }
}

func __asm_proxy_NOTW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.NOTW(v[0])
    } else {
        panic("instruction NOTW takes exactly 1 operand")
    }
}

func __asm_proxy_ORB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ORB(v[0], v[1])
    } else {
        panic("instruction ORB takes exactly 2 operands")
    }
}

func __asm_proxy_ORL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ORL(v[0], v[1])
    } else {
        panic("instruction ORL takes exactly 2 operands")
    }
}

func __asm_proxy_ORPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ORPD(v[0], v[1])
    } else {
        panic("instruction ORPD takes exactly 2 operands")
    }
}

func __asm_proxy_ORPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ORPS(v[0], v[1])
    } else {
        panic("instruction ORPS takes exactly 2 operands")
    }
}

func __asm_proxy_ORQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ORQ(v[0], v[1])
    } else {
        panic("instruction ORQ takes exactly 2 operands")
    }
}

func __asm_proxy_ORW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ORW(v[0], v[1])
    } else {
        panic("instruction ORW takes exactly 2 operands")
    }
}

func __asm_proxy_PABSB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PABSB(v[0], v[1])
    } else {
        panic("instruction PABSB takes exactly 2 operands")
    }
}

func __asm_proxy_PABSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PABSD(v[0], v[1])
    } else {
        panic("instruction PABSD takes exactly 2 operands")
    }
}

func __asm_proxy_PABSW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PABSW(v[0], v[1])
    } else {
        panic("instruction PABSW takes exactly 2 operands")
    }
}

func __asm_proxy_PACKSSDW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PACKSSDW(v[0], v[1])
    } else {
        panic("instruction PACKSSDW takes exactly 2 operands")
    }
}

func __asm_proxy_PACKSSWB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PACKSSWB(v[0], v[1])
    } else {
        panic("instruction PACKSSWB takes exactly 2 operands")
    }
}

func __asm_proxy_PACKUSDW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PACKUSDW(v[0], v[1])
    } else {
        panic("instruction PACKUSDW takes exactly 2 operands")
    }
}

func __asm_proxy_PACKUSWB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PACKUSWB(v[0], v[1])
    } else {
        panic("instruction PACKUSWB takes exactly 2 operands")
    }
}

func __asm_proxy_PADDB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PADDB(v[0], v[1])
    } else {
        panic("instruction PADDB takes exactly 2 operands")
    }
}

func __asm_proxy_PADDD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PADDD(v[0], v[1])
    } else {
        panic("instruction PADDD takes exactly 2 operands")
    }
}

func __asm_proxy_PADDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PADDQ(v[0], v[1])
    } else {
        panic("instruction PADDQ takes exactly 2 operands")
    }
}

func __asm_proxy_PADDSB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PADDSB(v[0], v[1])
    } else {
        panic("instruction PADDSB takes exactly 2 operands")
    }
}

func __asm_proxy_PADDSW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PADDSW(v[0], v[1])
    } else {
        panic("instruction PADDSW takes exactly 2 operands")
    }
}

func __asm_proxy_PADDUSB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PADDUSB(v[0], v[1])
    } else {
        panic("instruction PADDUSB takes exactly 2 operands")
    }
}

func __asm_proxy_PADDUSW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PADDUSW(v[0], v[1])
    } else {
        panic("instruction PADDUSW takes exactly 2 operands")
    }
}

func __asm_proxy_PADDW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PADDW(v[0], v[1])
    } else {
        panic("instruction PADDW takes exactly 2 operands")
    }
}

func __asm_proxy_PALIGNR__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.PALIGNR(v[0], v[1], v[2])
    } else {
        panic("instruction PALIGNR takes exactly 3 operands")
    }
}

func __asm_proxy_PAND__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PAND(v[0], v[1])
    } else {
        panic("instruction PAND takes exactly 2 operands")
    }
}

func __asm_proxy_PANDN__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PANDN(v[0], v[1])
    } else {
        panic("instruction PANDN takes exactly 2 operands")
    }
}

func __asm_proxy_PAUSE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.PAUSE()
    } else {
        panic("instruction PAUSE takes no operands")
    }
}

func __asm_proxy_PAVGB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PAVGB(v[0], v[1])
    } else {
        panic("instruction PAVGB takes exactly 2 operands")
    }
}

func __asm_proxy_PAVGUSB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PAVGUSB(v[0], v[1])
    } else {
        panic("instruction PAVGUSB takes exactly 2 operands")
    }
}

func __asm_proxy_PAVGW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PAVGW(v[0], v[1])
    } else {
        panic("instruction PAVGW takes exactly 2 operands")
    }
}

func __asm_proxy_PBLENDVB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.PBLENDVB(v[0], v[1], v[2])
    } else {
        panic("instruction PBLENDVB takes exactly 3 operands")
    }
}

func __asm_proxy_PBLENDW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.PBLENDW(v[0], v[1], v[2])
    } else {
        panic("instruction PBLENDW takes exactly 3 operands")
    }
}

func __asm_proxy_PCLMULQDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.PCLMULQDQ(v[0], v[1], v[2])
    } else {
        panic("instruction PCLMULQDQ takes exactly 3 operands")
    }
}

func __asm_proxy_PCMPEQB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PCMPEQB(v[0], v[1])
    } else {
        panic("instruction PCMPEQB takes exactly 2 operands")
    }
}

func __asm_proxy_PCMPEQD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PCMPEQD(v[0], v[1])
    } else {
        panic("instruction PCMPEQD takes exactly 2 operands")
    }
}

func __asm_proxy_PCMPEQQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PCMPEQQ(v[0], v[1])
    } else {
        panic("instruction PCMPEQQ takes exactly 2 operands")
    }
}

func __asm_proxy_PCMPEQW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PCMPEQW(v[0], v[1])
    } else {
        panic("instruction PCMPEQW takes exactly 2 operands")
    }
}

func __asm_proxy_PCMPESTRI__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.PCMPESTRI(v[0], v[1], v[2])
    } else {
        panic("instruction PCMPESTRI takes exactly 3 operands")
    }
}

func __asm_proxy_PCMPESTRM__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.PCMPESTRM(v[0], v[1], v[2])
    } else {
        panic("instruction PCMPESTRM takes exactly 3 operands")
    }
}

func __asm_proxy_PCMPGTB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PCMPGTB(v[0], v[1])
    } else {
        panic("instruction PCMPGTB takes exactly 2 operands")
    }
}

func __asm_proxy_PCMPGTD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PCMPGTD(v[0], v[1])
    } else {
        panic("instruction PCMPGTD takes exactly 2 operands")
    }
}

func __asm_proxy_PCMPGTQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PCMPGTQ(v[0], v[1])
    } else {
        panic("instruction PCMPGTQ takes exactly 2 operands")
    }
}

func __asm_proxy_PCMPGTW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PCMPGTW(v[0], v[1])
    } else {
        panic("instruction PCMPGTW takes exactly 2 operands")
    }
}

func __asm_proxy_PCMPISTRI__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.PCMPISTRI(v[0], v[1], v[2])
    } else {
        panic("instruction PCMPISTRI takes exactly 3 operands")
    }
}

func __asm_proxy_PCMPISTRM__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.PCMPISTRM(v[0], v[1], v[2])
    } else {
        panic("instruction PCMPISTRM takes exactly 3 operands")
    }
}

func __asm_proxy_PDEP__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.PDEP(v[0], v[1], v[2])
    } else {
        panic("instruction PDEP takes exactly 3 operands")
    }
}

func __asm_proxy_PEXT__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.PEXT(v[0], v[1], v[2])
    } else {
        panic("instruction PEXT takes exactly 3 operands")
    }
}

func __asm_proxy_PEXTRB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.PEXTRB(v[0], v[1], v[2])
    } else {
        panic("instruction PEXTRB takes exactly 3 operands")
    }
}

func __asm_proxy_PEXTRD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.PEXTRD(v[0], v[1], v[2])
    } else {
        panic("instruction PEXTRD takes exactly 3 operands")
    }
}

func __asm_proxy_PEXTRQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.PEXTRQ(v[0], v[1], v[2])
    } else {
        panic("instruction PEXTRQ takes exactly 3 operands")
    }
}

func __asm_proxy_PEXTRW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.PEXTRW(v[0], v[1], v[2])
    } else {
        panic("instruction PEXTRW takes exactly 3 operands")
    }
}

func __asm_proxy_PF2ID__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PF2ID(v[0], v[1])
    } else {
        panic("instruction PF2ID takes exactly 2 operands")
    }
}

func __asm_proxy_PF2IW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PF2IW(v[0], v[1])
    } else {
        panic("instruction PF2IW takes exactly 2 operands")
    }
}

func __asm_proxy_PFACC__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PFACC(v[0], v[1])
    } else {
        panic("instruction PFACC takes exactly 2 operands")
    }
}

func __asm_proxy_PFADD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PFADD(v[0], v[1])
    } else {
        panic("instruction PFADD takes exactly 2 operands")
    }
}

func __asm_proxy_PFCMPEQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PFCMPEQ(v[0], v[1])
    } else {
        panic("instruction PFCMPEQ takes exactly 2 operands")
    }
}

func __asm_proxy_PFCMPGE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PFCMPGE(v[0], v[1])
    } else {
        panic("instruction PFCMPGE takes exactly 2 operands")
    }
}

func __asm_proxy_PFCMPGT__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PFCMPGT(v[0], v[1])
    } else {
        panic("instruction PFCMPGT takes exactly 2 operands")
    }
}

func __asm_proxy_PFMAX__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PFMAX(v[0], v[1])
    } else {
        panic("instruction PFMAX takes exactly 2 operands")
    }
}

func __asm_proxy_PFMIN__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PFMIN(v[0], v[1])
    } else {
        panic("instruction PFMIN takes exactly 2 operands")
    }
}

func __asm_proxy_PFMUL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PFMUL(v[0], v[1])
    } else {
        panic("instruction PFMUL takes exactly 2 operands")
    }
}

func __asm_proxy_PFNACC__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PFNACC(v[0], v[1])
    } else {
        panic("instruction PFNACC takes exactly 2 operands")
    }
}

func __asm_proxy_PFPNACC__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PFPNACC(v[0], v[1])
    } else {
        panic("instruction PFPNACC takes exactly 2 operands")
    }
}

func __asm_proxy_PFRCP__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PFRCP(v[0], v[1])
    } else {
        panic("instruction PFRCP takes exactly 2 operands")
    }
}

func __asm_proxy_PFRCPIT1__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PFRCPIT1(v[0], v[1])
    } else {
        panic("instruction PFRCPIT1 takes exactly 2 operands")
    }
}

func __asm_proxy_PFRCPIT2__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PFRCPIT2(v[0], v[1])
    } else {
        panic("instruction PFRCPIT2 takes exactly 2 operands")
    }
}

func __asm_proxy_PFRSQIT1__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PFRSQIT1(v[0], v[1])
    } else {
        panic("instruction PFRSQIT1 takes exactly 2 operands")
    }
}

func __asm_proxy_PFRSQRT__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PFRSQRT(v[0], v[1])
    } else {
        panic("instruction PFRSQRT takes exactly 2 operands")
    }
}

func __asm_proxy_PFSUB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PFSUB(v[0], v[1])
    } else {
        panic("instruction PFSUB takes exactly 2 operands")
    }
}

func __asm_proxy_PFSUBR__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PFSUBR(v[0], v[1])
    } else {
        panic("instruction PFSUBR takes exactly 2 operands")
    }
}

func __asm_proxy_PHADDD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PHADDD(v[0], v[1])
    } else {
        panic("instruction PHADDD takes exactly 2 operands")
    }
}

func __asm_proxy_PHADDSW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PHADDSW(v[0], v[1])
    } else {
        panic("instruction PHADDSW takes exactly 2 operands")
    }
}

func __asm_proxy_PHADDW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PHADDW(v[0], v[1])
    } else {
        panic("instruction PHADDW takes exactly 2 operands")
    }
}

func __asm_proxy_PHMINPOSUW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PHMINPOSUW(v[0], v[1])
    } else {
        panic("instruction PHMINPOSUW takes exactly 2 operands")
    }
}

func __asm_proxy_PHSUBD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PHSUBD(v[0], v[1])
    } else {
        panic("instruction PHSUBD takes exactly 2 operands")
    }
}

func __asm_proxy_PHSUBSW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PHSUBSW(v[0], v[1])
    } else {
        panic("instruction PHSUBSW takes exactly 2 operands")
    }
}

func __asm_proxy_PHSUBW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PHSUBW(v[0], v[1])
    } else {
        panic("instruction PHSUBW takes exactly 2 operands")
    }
}

func __asm_proxy_PI2FD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PI2FD(v[0], v[1])
    } else {
        panic("instruction PI2FD takes exactly 2 operands")
    }
}

func __asm_proxy_PI2FW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PI2FW(v[0], v[1])
    } else {
        panic("instruction PI2FW takes exactly 2 operands")
    }
}

func __asm_proxy_PINSRB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.PINSRB(v[0], v[1], v[2])
    } else {
        panic("instruction PINSRB takes exactly 3 operands")
    }
}

func __asm_proxy_PINSRD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.PINSRD(v[0], v[1], v[2])
    } else {
        panic("instruction PINSRD takes exactly 3 operands")
    }
}

func __asm_proxy_PINSRQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.PINSRQ(v[0], v[1], v[2])
    } else {
        panic("instruction PINSRQ takes exactly 3 operands")
    }
}

func __asm_proxy_PINSRW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.PINSRW(v[0], v[1], v[2])
    } else {
        panic("instruction PINSRW takes exactly 3 operands")
    }
}

func __asm_proxy_PMADDUBSW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMADDUBSW(v[0], v[1])
    } else {
        panic("instruction PMADDUBSW takes exactly 2 operands")
    }
}

func __asm_proxy_PMADDWD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMADDWD(v[0], v[1])
    } else {
        panic("instruction PMADDWD takes exactly 2 operands")
    }
}

func __asm_proxy_PMAXSB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMAXSB(v[0], v[1])
    } else {
        panic("instruction PMAXSB takes exactly 2 operands")
    }
}

func __asm_proxy_PMAXSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMAXSD(v[0], v[1])
    } else {
        panic("instruction PMAXSD takes exactly 2 operands")
    }
}

func __asm_proxy_PMAXSW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMAXSW(v[0], v[1])
    } else {
        panic("instruction PMAXSW takes exactly 2 operands")
    }
}

func __asm_proxy_PMAXUB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMAXUB(v[0], v[1])
    } else {
        panic("instruction PMAXUB takes exactly 2 operands")
    }
}

func __asm_proxy_PMAXUD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMAXUD(v[0], v[1])
    } else {
        panic("instruction PMAXUD takes exactly 2 operands")
    }
}

func __asm_proxy_PMAXUW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMAXUW(v[0], v[1])
    } else {
        panic("instruction PMAXUW takes exactly 2 operands")
    }
}

func __asm_proxy_PMINSB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMINSB(v[0], v[1])
    } else {
        panic("instruction PMINSB takes exactly 2 operands")
    }
}

func __asm_proxy_PMINSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMINSD(v[0], v[1])
    } else {
        panic("instruction PMINSD takes exactly 2 operands")
    }
}

func __asm_proxy_PMINSW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMINSW(v[0], v[1])
    } else {
        panic("instruction PMINSW takes exactly 2 operands")
    }
}

func __asm_proxy_PMINUB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMINUB(v[0], v[1])
    } else {
        panic("instruction PMINUB takes exactly 2 operands")
    }
}

func __asm_proxy_PMINUD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMINUD(v[0], v[1])
    } else {
        panic("instruction PMINUD takes exactly 2 operands")
    }
}

func __asm_proxy_PMINUW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMINUW(v[0], v[1])
    } else {
        panic("instruction PMINUW takes exactly 2 operands")
    }
}

func __asm_proxy_PMOVMSKB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMOVMSKB(v[0], v[1])
    } else {
        panic("instruction PMOVMSKB takes exactly 2 operands")
    }
}

func __asm_proxy_PMOVSXBD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMOVSXBD(v[0], v[1])
    } else {
        panic("instruction PMOVSXBD takes exactly 2 operands")
    }
}

func __asm_proxy_PMOVSXBQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMOVSXBQ(v[0], v[1])
    } else {
        panic("instruction PMOVSXBQ takes exactly 2 operands")
    }
}

func __asm_proxy_PMOVSXBW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMOVSXBW(v[0], v[1])
    } else {
        panic("instruction PMOVSXBW takes exactly 2 operands")
    }
}

func __asm_proxy_PMOVSXDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMOVSXDQ(v[0], v[1])
    } else {
        panic("instruction PMOVSXDQ takes exactly 2 operands")
    }
}

func __asm_proxy_PMOVSXWD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMOVSXWD(v[0], v[1])
    } else {
        panic("instruction PMOVSXWD takes exactly 2 operands")
    }
}

func __asm_proxy_PMOVSXWQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMOVSXWQ(v[0], v[1])
    } else {
        panic("instruction PMOVSXWQ takes exactly 2 operands")
    }
}

func __asm_proxy_PMOVZXBD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMOVZXBD(v[0], v[1])
    } else {
        panic("instruction PMOVZXBD takes exactly 2 operands")
    }
}

func __asm_proxy_PMOVZXBQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMOVZXBQ(v[0], v[1])
    } else {
        panic("instruction PMOVZXBQ takes exactly 2 operands")
    }
}

func __asm_proxy_PMOVZXBW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMOVZXBW(v[0], v[1])
    } else {
        panic("instruction PMOVZXBW takes exactly 2 operands")
    }
}

func __asm_proxy_PMOVZXDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMOVZXDQ(v[0], v[1])
    } else {
        panic("instruction PMOVZXDQ takes exactly 2 operands")
    }
}

func __asm_proxy_PMOVZXWD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMOVZXWD(v[0], v[1])
    } else {
        panic("instruction PMOVZXWD takes exactly 2 operands")
    }
}

func __asm_proxy_PMOVZXWQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMOVZXWQ(v[0], v[1])
    } else {
        panic("instruction PMOVZXWQ takes exactly 2 operands")
    }
}

func __asm_proxy_PMULDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMULDQ(v[0], v[1])
    } else {
        panic("instruction PMULDQ takes exactly 2 operands")
    }
}

func __asm_proxy_PMULHRSW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMULHRSW(v[0], v[1])
    } else {
        panic("instruction PMULHRSW takes exactly 2 operands")
    }
}

func __asm_proxy_PMULHRW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMULHRW(v[0], v[1])
    } else {
        panic("instruction PMULHRW takes exactly 2 operands")
    }
}

func __asm_proxy_PMULHUW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMULHUW(v[0], v[1])
    } else {
        panic("instruction PMULHUW takes exactly 2 operands")
    }
}

func __asm_proxy_PMULHW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMULHW(v[0], v[1])
    } else {
        panic("instruction PMULHW takes exactly 2 operands")
    }
}

func __asm_proxy_PMULLD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMULLD(v[0], v[1])
    } else {
        panic("instruction PMULLD takes exactly 2 operands")
    }
}

func __asm_proxy_PMULLW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMULLW(v[0], v[1])
    } else {
        panic("instruction PMULLW takes exactly 2 operands")
    }
}

func __asm_proxy_PMULUDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PMULUDQ(v[0], v[1])
    } else {
        panic("instruction PMULUDQ takes exactly 2 operands")
    }
}

func __asm_proxy_POPCNTL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.POPCNTL(v[0], v[1])
    } else {
        panic("instruction POPCNTL takes exactly 2 operands")
    }
}

func __asm_proxy_POPCNTQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.POPCNTQ(v[0], v[1])
    } else {
        panic("instruction POPCNTQ takes exactly 2 operands")
    }
}

func __asm_proxy_POPCNTW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.POPCNTW(v[0], v[1])
    } else {
        panic("instruction POPCNTW takes exactly 2 operands")
    }
}

func __asm_proxy_POPQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.POPQ(v[0])
    } else {
        panic("instruction POPQ takes exactly 1 operand")
    }
}

func __asm_proxy_POPW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.POPW(v[0])
    } else {
        panic("instruction POPW takes exactly 1 operand")
    }
}

func __asm_proxy_POR__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.POR(v[0], v[1])
    } else {
        panic("instruction POR takes exactly 2 operands")
    }
}

func __asm_proxy_PREFETCH__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.PREFETCH(v[0])
    } else {
        panic("instruction PREFETCH takes exactly 1 operand")
    }
}

func __asm_proxy_PREFETCHNTA__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.PREFETCHNTA(v[0])
    } else {
        panic("instruction PREFETCHNTA takes exactly 1 operand")
    }
}

func __asm_proxy_PREFETCHT0__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.PREFETCHT0(v[0])
    } else {
        panic("instruction PREFETCHT0 takes exactly 1 operand")
    }
}

func __asm_proxy_PREFETCHT1__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.PREFETCHT1(v[0])
    } else {
        panic("instruction PREFETCHT1 takes exactly 1 operand")
    }
}

func __asm_proxy_PREFETCHT2__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.PREFETCHT2(v[0])
    } else {
        panic("instruction PREFETCHT2 takes exactly 1 operand")
    }
}

func __asm_proxy_PREFETCHW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.PREFETCHW(v[0])
    } else {
        panic("instruction PREFETCHW takes exactly 1 operand")
    }
}

func __asm_proxy_PREFETCHWT1__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.PREFETCHWT1(v[0])
    } else {
        panic("instruction PREFETCHWT1 takes exactly 1 operand")
    }
}

func __asm_proxy_PSADBW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PSADBW(v[0], v[1])
    } else {
        panic("instruction PSADBW takes exactly 2 operands")
    }
}

func __asm_proxy_PSHUFB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PSHUFB(v[0], v[1])
    } else {
        panic("instruction PSHUFB takes exactly 2 operands")
    }
}

func __asm_proxy_PSHUFD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.PSHUFD(v[0], v[1], v[2])
    } else {
        panic("instruction PSHUFD takes exactly 3 operands")
    }
}

func __asm_proxy_PSHUFHW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.PSHUFHW(v[0], v[1], v[2])
    } else {
        panic("instruction PSHUFHW takes exactly 3 operands")
    }
}

func __asm_proxy_PSHUFLW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.PSHUFLW(v[0], v[1], v[2])
    } else {
        panic("instruction PSHUFLW takes exactly 3 operands")
    }
}

func __asm_proxy_PSHUFW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.PSHUFW(v[0], v[1], v[2])
    } else {
        panic("instruction PSHUFW takes exactly 3 operands")
    }
}

func __asm_proxy_PSIGNB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PSIGNB(v[0], v[1])
    } else {
        panic("instruction PSIGNB takes exactly 2 operands")
    }
}

func __asm_proxy_PSIGND__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PSIGND(v[0], v[1])
    } else {
        panic("instruction PSIGND takes exactly 2 operands")
    }
}

func __asm_proxy_PSIGNW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PSIGNW(v[0], v[1])
    } else {
        panic("instruction PSIGNW takes exactly 2 operands")
    }
}

func __asm_proxy_PSLLD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PSLLD(v[0], v[1])
    } else {
        panic("instruction PSLLD takes exactly 2 operands")
    }
}

func __asm_proxy_PSLLDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PSLLDQ(v[0], v[1])
    } else {
        panic("instruction PSLLDQ takes exactly 2 operands")
    }
}

func __asm_proxy_PSLLQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PSLLQ(v[0], v[1])
    } else {
        panic("instruction PSLLQ takes exactly 2 operands")
    }
}

func __asm_proxy_PSLLW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PSLLW(v[0], v[1])
    } else {
        panic("instruction PSLLW takes exactly 2 operands")
    }
}

func __asm_proxy_PSRAD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PSRAD(v[0], v[1])
    } else {
        panic("instruction PSRAD takes exactly 2 operands")
    }
}

func __asm_proxy_PSRAW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PSRAW(v[0], v[1])
    } else {
        panic("instruction PSRAW takes exactly 2 operands")
    }
}

func __asm_proxy_PSRLD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PSRLD(v[0], v[1])
    } else {
        panic("instruction PSRLD takes exactly 2 operands")
    }
}

func __asm_proxy_PSRLDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PSRLDQ(v[0], v[1])
    } else {
        panic("instruction PSRLDQ takes exactly 2 operands")
    }
}

func __asm_proxy_PSRLQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PSRLQ(v[0], v[1])
    } else {
        panic("instruction PSRLQ takes exactly 2 operands")
    }
}

func __asm_proxy_PSRLW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PSRLW(v[0], v[1])
    } else {
        panic("instruction PSRLW takes exactly 2 operands")
    }
}

func __asm_proxy_PSUBB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PSUBB(v[0], v[1])
    } else {
        panic("instruction PSUBB takes exactly 2 operands")
    }
}

func __asm_proxy_PSUBD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PSUBD(v[0], v[1])
    } else {
        panic("instruction PSUBD takes exactly 2 operands")
    }
}

func __asm_proxy_PSUBQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PSUBQ(v[0], v[1])
    } else {
        panic("instruction PSUBQ takes exactly 2 operands")
    }
}

func __asm_proxy_PSUBSB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PSUBSB(v[0], v[1])
    } else {
        panic("instruction PSUBSB takes exactly 2 operands")
    }
}

func __asm_proxy_PSUBSW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PSUBSW(v[0], v[1])
    } else {
        panic("instruction PSUBSW takes exactly 2 operands")
    }
}

func __asm_proxy_PSUBUSB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PSUBUSB(v[0], v[1])
    } else {
        panic("instruction PSUBUSB takes exactly 2 operands")
    }
}

func __asm_proxy_PSUBUSW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PSUBUSW(v[0], v[1])
    } else {
        panic("instruction PSUBUSW takes exactly 2 operands")
    }
}

func __asm_proxy_PSUBW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PSUBW(v[0], v[1])
    } else {
        panic("instruction PSUBW takes exactly 2 operands")
    }
}

func __asm_proxy_PSWAPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PSWAPD(v[0], v[1])
    } else {
        panic("instruction PSWAPD takes exactly 2 operands")
    }
}

func __asm_proxy_PTEST__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PTEST(v[0], v[1])
    } else {
        panic("instruction PTEST takes exactly 2 operands")
    }
}

func __asm_proxy_PUNPCKHBW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PUNPCKHBW(v[0], v[1])
    } else {
        panic("instruction PUNPCKHBW takes exactly 2 operands")
    }
}

func __asm_proxy_PUNPCKHDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PUNPCKHDQ(v[0], v[1])
    } else {
        panic("instruction PUNPCKHDQ takes exactly 2 operands")
    }
}

func __asm_proxy_PUNPCKHQDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PUNPCKHQDQ(v[0], v[1])
    } else {
        panic("instruction PUNPCKHQDQ takes exactly 2 operands")
    }
}

func __asm_proxy_PUNPCKHWD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PUNPCKHWD(v[0], v[1])
    } else {
        panic("instruction PUNPCKHWD takes exactly 2 operands")
    }
}

func __asm_proxy_PUNPCKLBW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PUNPCKLBW(v[0], v[1])
    } else {
        panic("instruction PUNPCKLBW takes exactly 2 operands")
    }
}

func __asm_proxy_PUNPCKLDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PUNPCKLDQ(v[0], v[1])
    } else {
        panic("instruction PUNPCKLDQ takes exactly 2 operands")
    }
}

func __asm_proxy_PUNPCKLQDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PUNPCKLQDQ(v[0], v[1])
    } else {
        panic("instruction PUNPCKLQDQ takes exactly 2 operands")
    }
}

func __asm_proxy_PUNPCKLWD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PUNPCKLWD(v[0], v[1])
    } else {
        panic("instruction PUNPCKLWD takes exactly 2 operands")
    }
}

func __asm_proxy_PUSHQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.PUSHQ(v[0])
    } else {
        panic("instruction PUSHQ takes exactly 1 operand")
    }
}

func __asm_proxy_PUSHW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.PUSHW(v[0])
    } else {
        panic("instruction PUSHW takes exactly 1 operand")
    }
}

func __asm_proxy_PXOR__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.PXOR(v[0], v[1])
    } else {
        panic("instruction PXOR takes exactly 2 operands")
    }
}

func __asm_proxy_RCLB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.RCLB(v[0], v[1])
    } else {
        panic("instruction RCLB takes exactly 2 operands")
    }
}

func __asm_proxy_RCLL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.RCLL(v[0], v[1])
    } else {
        panic("instruction RCLL takes exactly 2 operands")
    }
}

func __asm_proxy_RCLQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.RCLQ(v[0], v[1])
    } else {
        panic("instruction RCLQ takes exactly 2 operands")
    }
}

func __asm_proxy_RCLW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.RCLW(v[0], v[1])
    } else {
        panic("instruction RCLW takes exactly 2 operands")
    }
}

func __asm_proxy_RCPPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.RCPPS(v[0], v[1])
    } else {
        panic("instruction RCPPS takes exactly 2 operands")
    }
}

func __asm_proxy_RCPSS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.RCPSS(v[0], v[1])
    } else {
        panic("instruction RCPSS takes exactly 2 operands")
    }
}

func __asm_proxy_RCRB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.RCRB(v[0], v[1])
    } else {
        panic("instruction RCRB takes exactly 2 operands")
    }
}

func __asm_proxy_RCRL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.RCRL(v[0], v[1])
    } else {
        panic("instruction RCRL takes exactly 2 operands")
    }
}

func __asm_proxy_RCRQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.RCRQ(v[0], v[1])
    } else {
        panic("instruction RCRQ takes exactly 2 operands")
    }
}

func __asm_proxy_RCRW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.RCRW(v[0], v[1])
    } else {
        panic("instruction RCRW takes exactly 2 operands")
    }
}

func __asm_proxy_RDRAND__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.RDRAND(v[0])
    } else {
        panic("instruction RDRAND takes exactly 1 operand")
    }
}

func __asm_proxy_RDSEED__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.RDSEED(v[0])
    } else {
        panic("instruction RDSEED takes exactly 1 operand")
    }
}

func __asm_proxy_RDTSC__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.RDTSC()
    } else {
        panic("instruction RDTSC takes no operands")
    }
}

func __asm_proxy_RDTSCP__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.RDTSCP()
    } else {
        panic("instruction RDTSCP takes no operands")
    }
}

func __asm_proxy_RET__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 0  : return p.RET()
        case 1  : return p.RET(v[0])
        default : panic("instruction RET takes 0 or 1 operands")
    }
}

func __asm_proxy_ROLB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ROLB(v[0], v[1])
    } else {
        panic("instruction ROLB takes exactly 2 operands")
    }
}

func __asm_proxy_ROLL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ROLL(v[0], v[1])
    } else {
        panic("instruction ROLL takes exactly 2 operands")
    }
}

func __asm_proxy_ROLQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ROLQ(v[0], v[1])
    } else {
        panic("instruction ROLQ takes exactly 2 operands")
    }
}

func __asm_proxy_ROLW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.ROLW(v[0], v[1])
    } else {
        panic("instruction ROLW takes exactly 2 operands")
    }
}

func __asm_proxy_RORB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.RORB(v[0], v[1])
    } else {
        panic("instruction RORB takes exactly 2 operands")
    }
}

func __asm_proxy_RORL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.RORL(v[0], v[1])
    } else {
        panic("instruction RORL takes exactly 2 operands")
    }
}

func __asm_proxy_RORQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.RORQ(v[0], v[1])
    } else {
        panic("instruction RORQ takes exactly 2 operands")
    }
}

func __asm_proxy_RORW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.RORW(v[0], v[1])
    } else {
        panic("instruction RORW takes exactly 2 operands")
    }
}

func __asm_proxy_RORXL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.RORXL(v[0], v[1], v[2])
    } else {
        panic("instruction RORXL takes exactly 3 operands")
    }
}

func __asm_proxy_RORXQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.RORXQ(v[0], v[1], v[2])
    } else {
        panic("instruction RORXQ takes exactly 3 operands")
    }
}

func __asm_proxy_ROUNDPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.ROUNDPD(v[0], v[1], v[2])
    } else {
        panic("instruction ROUNDPD takes exactly 3 operands")
    }
}

func __asm_proxy_ROUNDPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.ROUNDPS(v[0], v[1], v[2])
    } else {
        panic("instruction ROUNDPS takes exactly 3 operands")
    }
}

func __asm_proxy_ROUNDSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.ROUNDSD(v[0], v[1], v[2])
    } else {
        panic("instruction ROUNDSD takes exactly 3 operands")
    }
}

func __asm_proxy_ROUNDSS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.ROUNDSS(v[0], v[1], v[2])
    } else {
        panic("instruction ROUNDSS takes exactly 3 operands")
    }
}

func __asm_proxy_RSQRTPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.RSQRTPS(v[0], v[1])
    } else {
        panic("instruction RSQRTPS takes exactly 2 operands")
    }
}

func __asm_proxy_RSQRTSS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.RSQRTSS(v[0], v[1])
    } else {
        panic("instruction RSQRTSS takes exactly 2 operands")
    }
}

func __asm_proxy_SALB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SALB(v[0], v[1])
    } else {
        panic("instruction SALB takes exactly 2 operands")
    }
}

func __asm_proxy_SALL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SALL(v[0], v[1])
    } else {
        panic("instruction SALL takes exactly 2 operands")
    }
}

func __asm_proxy_SALQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SALQ(v[0], v[1])
    } else {
        panic("instruction SALQ takes exactly 2 operands")
    }
}

func __asm_proxy_SALW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SALW(v[0], v[1])
    } else {
        panic("instruction SALW takes exactly 2 operands")
    }
}

func __asm_proxy_SARB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SARB(v[0], v[1])
    } else {
        panic("instruction SARB takes exactly 2 operands")
    }
}

func __asm_proxy_SARL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SARL(v[0], v[1])
    } else {
        panic("instruction SARL takes exactly 2 operands")
    }
}

func __asm_proxy_SARQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SARQ(v[0], v[1])
    } else {
        panic("instruction SARQ takes exactly 2 operands")
    }
}

func __asm_proxy_SARW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SARW(v[0], v[1])
    } else {
        panic("instruction SARW takes exactly 2 operands")
    }
}

func __asm_proxy_SARXL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.SARXL(v[0], v[1], v[2])
    } else {
        panic("instruction SARXL takes exactly 3 operands")
    }
}

func __asm_proxy_SARXQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.SARXQ(v[0], v[1], v[2])
    } else {
        panic("instruction SARXQ takes exactly 3 operands")
    }
}

func __asm_proxy_SBBB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SBBB(v[0], v[1])
    } else {
        panic("instruction SBBB takes exactly 2 operands")
    }
}

func __asm_proxy_SBBL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SBBL(v[0], v[1])
    } else {
        panic("instruction SBBL takes exactly 2 operands")
    }
}

func __asm_proxy_SBBQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SBBQ(v[0], v[1])
    } else {
        panic("instruction SBBQ takes exactly 2 operands")
    }
}

func __asm_proxy_SBBW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SBBW(v[0], v[1])
    } else {
        panic("instruction SBBW takes exactly 2 operands")
    }
}

func __asm_proxy_SETA__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETA(v[0])
    } else {
        panic("instruction SETA takes exactly 1 operand")
    }
}

func __asm_proxy_SETAE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETAE(v[0])
    } else {
        panic("instruction SETAE takes exactly 1 operand")
    }
}

func __asm_proxy_SETB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETB(v[0])
    } else {
        panic("instruction SETB takes exactly 1 operand")
    }
}

func __asm_proxy_SETBE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETBE(v[0])
    } else {
        panic("instruction SETBE takes exactly 1 operand")
    }
}

func __asm_proxy_SETC__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETC(v[0])
    } else {
        panic("instruction SETC takes exactly 1 operand")
    }
}

func __asm_proxy_SETE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETE(v[0])
    } else {
        panic("instruction SETE takes exactly 1 operand")
    }
}

func __asm_proxy_SETG__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETG(v[0])
    } else {
        panic("instruction SETG takes exactly 1 operand")
    }
}

func __asm_proxy_SETGE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETGE(v[0])
    } else {
        panic("instruction SETGE takes exactly 1 operand")
    }
}

func __asm_proxy_SETL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETL(v[0])
    } else {
        panic("instruction SETL takes exactly 1 operand")
    }
}

func __asm_proxy_SETLE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETLE(v[0])
    } else {
        panic("instruction SETLE takes exactly 1 operand")
    }
}

func __asm_proxy_SETNA__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETNA(v[0])
    } else {
        panic("instruction SETNA takes exactly 1 operand")
    }
}

func __asm_proxy_SETNAE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETNAE(v[0])
    } else {
        panic("instruction SETNAE takes exactly 1 operand")
    }
}

func __asm_proxy_SETNB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETNB(v[0])
    } else {
        panic("instruction SETNB takes exactly 1 operand")
    }
}

func __asm_proxy_SETNBE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETNBE(v[0])
    } else {
        panic("instruction SETNBE takes exactly 1 operand")
    }
}

func __asm_proxy_SETNC__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETNC(v[0])
    } else {
        panic("instruction SETNC takes exactly 1 operand")
    }
}

func __asm_proxy_SETNE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETNE(v[0])
    } else {
        panic("instruction SETNE takes exactly 1 operand")
    }
}

func __asm_proxy_SETNG__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETNG(v[0])
    } else {
        panic("instruction SETNG takes exactly 1 operand")
    }
}

func __asm_proxy_SETNGE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETNGE(v[0])
    } else {
        panic("instruction SETNGE takes exactly 1 operand")
    }
}

func __asm_proxy_SETNL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETNL(v[0])
    } else {
        panic("instruction SETNL takes exactly 1 operand")
    }
}

func __asm_proxy_SETNLE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETNLE(v[0])
    } else {
        panic("instruction SETNLE takes exactly 1 operand")
    }
}

func __asm_proxy_SETNO__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETNO(v[0])
    } else {
        panic("instruction SETNO takes exactly 1 operand")
    }
}

func __asm_proxy_SETNP__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETNP(v[0])
    } else {
        panic("instruction SETNP takes exactly 1 operand")
    }
}

func __asm_proxy_SETNS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETNS(v[0])
    } else {
        panic("instruction SETNS takes exactly 1 operand")
    }
}

func __asm_proxy_SETNZ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETNZ(v[0])
    } else {
        panic("instruction SETNZ takes exactly 1 operand")
    }
}

func __asm_proxy_SETO__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETO(v[0])
    } else {
        panic("instruction SETO takes exactly 1 operand")
    }
}

func __asm_proxy_SETP__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETP(v[0])
    } else {
        panic("instruction SETP takes exactly 1 operand")
    }
}

func __asm_proxy_SETPE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETPE(v[0])
    } else {
        panic("instruction SETPE takes exactly 1 operand")
    }
}

func __asm_proxy_SETPO__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETPO(v[0])
    } else {
        panic("instruction SETPO takes exactly 1 operand")
    }
}

func __asm_proxy_SETS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETS(v[0])
    } else {
        panic("instruction SETS takes exactly 1 operand")
    }
}

func __asm_proxy_SETZ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.SETZ(v[0])
    } else {
        panic("instruction SETZ takes exactly 1 operand")
    }
}

func __asm_proxy_SFENCE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.SFENCE()
    } else {
        panic("instruction SFENCE takes no operands")
    }
}

func __asm_proxy_SHA1MSG1__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SHA1MSG1(v[0], v[1])
    } else {
        panic("instruction SHA1MSG1 takes exactly 2 operands")
    }
}

func __asm_proxy_SHA1MSG2__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SHA1MSG2(v[0], v[1])
    } else {
        panic("instruction SHA1MSG2 takes exactly 2 operands")
    }
}

func __asm_proxy_SHA1NEXTE__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SHA1NEXTE(v[0], v[1])
    } else {
        panic("instruction SHA1NEXTE takes exactly 2 operands")
    }
}

func __asm_proxy_SHA1RNDS4__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.SHA1RNDS4(v[0], v[1], v[2])
    } else {
        panic("instruction SHA1RNDS4 takes exactly 3 operands")
    }
}

func __asm_proxy_SHA256MSG1__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SHA256MSG1(v[0], v[1])
    } else {
        panic("instruction SHA256MSG1 takes exactly 2 operands")
    }
}

func __asm_proxy_SHA256MSG2__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SHA256MSG2(v[0], v[1])
    } else {
        panic("instruction SHA256MSG2 takes exactly 2 operands")
    }
}

func __asm_proxy_SHA256RNDS2__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.SHA256RNDS2(v[0], v[1], v[2])
    } else {
        panic("instruction SHA256RNDS2 takes exactly 3 operands")
    }
}

func __asm_proxy_SHLB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SHLB(v[0], v[1])
    } else {
        panic("instruction SHLB takes exactly 2 operands")
    }
}

func __asm_proxy_SHLDL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.SHLDL(v[0], v[1], v[2])
    } else {
        panic("instruction SHLDL takes exactly 3 operands")
    }
}

func __asm_proxy_SHLDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.SHLDQ(v[0], v[1], v[2])
    } else {
        panic("instruction SHLDQ takes exactly 3 operands")
    }
}

func __asm_proxy_SHLDW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.SHLDW(v[0], v[1], v[2])
    } else {
        panic("instruction SHLDW takes exactly 3 operands")
    }
}

func __asm_proxy_SHLL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SHLL(v[0], v[1])
    } else {
        panic("instruction SHLL takes exactly 2 operands")
    }
}

func __asm_proxy_SHLQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SHLQ(v[0], v[1])
    } else {
        panic("instruction SHLQ takes exactly 2 operands")
    }
}

func __asm_proxy_SHLW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SHLW(v[0], v[1])
    } else {
        panic("instruction SHLW takes exactly 2 operands")
    }
}

func __asm_proxy_SHLXL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.SHLXL(v[0], v[1], v[2])
    } else {
        panic("instruction SHLXL takes exactly 3 operands")
    }
}

func __asm_proxy_SHLXQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.SHLXQ(v[0], v[1], v[2])
    } else {
        panic("instruction SHLXQ takes exactly 3 operands")
    }
}

func __asm_proxy_SHRB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SHRB(v[0], v[1])
    } else {
        panic("instruction SHRB takes exactly 2 operands")
    }
}

func __asm_proxy_SHRDL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.SHRDL(v[0], v[1], v[2])
    } else {
        panic("instruction SHRDL takes exactly 3 operands")
    }
}

func __asm_proxy_SHRDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.SHRDQ(v[0], v[1], v[2])
    } else {
        panic("instruction SHRDQ takes exactly 3 operands")
    }
}

func __asm_proxy_SHRDW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.SHRDW(v[0], v[1], v[2])
    } else {
        panic("instruction SHRDW takes exactly 3 operands")
    }
}

func __asm_proxy_SHRL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SHRL(v[0], v[1])
    } else {
        panic("instruction SHRL takes exactly 2 operands")
    }
}

func __asm_proxy_SHRQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SHRQ(v[0], v[1])
    } else {
        panic("instruction SHRQ takes exactly 2 operands")
    }
}

func __asm_proxy_SHRW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SHRW(v[0], v[1])
    } else {
        panic("instruction SHRW takes exactly 2 operands")
    }
}

func __asm_proxy_SHRXL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.SHRXL(v[0], v[1], v[2])
    } else {
        panic("instruction SHRXL takes exactly 3 operands")
    }
}

func __asm_proxy_SHRXQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.SHRXQ(v[0], v[1], v[2])
    } else {
        panic("instruction SHRXQ takes exactly 3 operands")
    }
}

func __asm_proxy_SHUFPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.SHUFPD(v[0], v[1], v[2])
    } else {
        panic("instruction SHUFPD takes exactly 3 operands")
    }
}

func __asm_proxy_SHUFPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.SHUFPS(v[0], v[1], v[2])
    } else {
        panic("instruction SHUFPS takes exactly 3 operands")
    }
}

func __asm_proxy_SQRTPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SQRTPD(v[0], v[1])
    } else {
        panic("instruction SQRTPD takes exactly 2 operands")
    }
}

func __asm_proxy_SQRTPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SQRTPS(v[0], v[1])
    } else {
        panic("instruction SQRTPS takes exactly 2 operands")
    }
}

func __asm_proxy_SQRTSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SQRTSD(v[0], v[1])
    } else {
        panic("instruction SQRTSD takes exactly 2 operands")
    }
}

func __asm_proxy_SQRTSS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SQRTSS(v[0], v[1])
    } else {
        panic("instruction SQRTSS takes exactly 2 operands")
    }
}

func __asm_proxy_STC__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.STC()
    } else {
        panic("instruction STC takes no operands")
    }
}

func __asm_proxy_STD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.STD()
    } else {
        panic("instruction STD takes no operands")
    }
}

func __asm_proxy_STMXCSR__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.STMXCSR(v[0])
    } else {
        panic("instruction STMXCSR takes exactly 1 operand")
    }
}

func __asm_proxy_SUBB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SUBB(v[0], v[1])
    } else {
        panic("instruction SUBB takes exactly 2 operands")
    }
}

func __asm_proxy_SUBL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SUBL(v[0], v[1])
    } else {
        panic("instruction SUBL takes exactly 2 operands")
    }
}

func __asm_proxy_SUBPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SUBPD(v[0], v[1])
    } else {
        panic("instruction SUBPD takes exactly 2 operands")
    }
}

func __asm_proxy_SUBPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SUBPS(v[0], v[1])
    } else {
        panic("instruction SUBPS takes exactly 2 operands")
    }
}

func __asm_proxy_SUBQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SUBQ(v[0], v[1])
    } else {
        panic("instruction SUBQ takes exactly 2 operands")
    }
}

func __asm_proxy_SUBSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SUBSD(v[0], v[1])
    } else {
        panic("instruction SUBSD takes exactly 2 operands")
    }
}

func __asm_proxy_SUBSS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SUBSS(v[0], v[1])
    } else {
        panic("instruction SUBSS takes exactly 2 operands")
    }
}

func __asm_proxy_SUBW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.SUBW(v[0], v[1])
    } else {
        panic("instruction SUBW takes exactly 2 operands")
    }
}

func __asm_proxy_SYSCALL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.SYSCALL()
    } else {
        panic("instruction SYSCALL takes no operands")
    }
}

func __asm_proxy_T1MSKC__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.T1MSKC(v[0], v[1])
    } else {
        panic("instruction T1MSKC takes exactly 2 operands")
    }
}

func __asm_proxy_TESTB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.TESTB(v[0], v[1])
    } else {
        panic("instruction TESTB takes exactly 2 operands")
    }
}

func __asm_proxy_TESTL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.TESTL(v[0], v[1])
    } else {
        panic("instruction TESTL takes exactly 2 operands")
    }
}

func __asm_proxy_TESTQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.TESTQ(v[0], v[1])
    } else {
        panic("instruction TESTQ takes exactly 2 operands")
    }
}

func __asm_proxy_TESTW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.TESTW(v[0], v[1])
    } else {
        panic("instruction TESTW takes exactly 2 operands")
    }
}

func __asm_proxy_TZCNTL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.TZCNTL(v[0], v[1])
    } else {
        panic("instruction TZCNTL takes exactly 2 operands")
    }
}

func __asm_proxy_TZCNTQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.TZCNTQ(v[0], v[1])
    } else {
        panic("instruction TZCNTQ takes exactly 2 operands")
    }
}

func __asm_proxy_TZCNTW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.TZCNTW(v[0], v[1])
    } else {
        panic("instruction TZCNTW takes exactly 2 operands")
    }
}

func __asm_proxy_TZMSK__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.TZMSK(v[0], v[1])
    } else {
        panic("instruction TZMSK takes exactly 2 operands")
    }
}

func __asm_proxy_UCOMISD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.UCOMISD(v[0], v[1])
    } else {
        panic("instruction UCOMISD takes exactly 2 operands")
    }
}

func __asm_proxy_UCOMISS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.UCOMISS(v[0], v[1])
    } else {
        panic("instruction UCOMISS takes exactly 2 operands")
    }
}

func __asm_proxy_UD2__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.UD2()
    } else {
        panic("instruction UD2 takes no operands")
    }
}

func __asm_proxy_UNPCKHPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.UNPCKHPD(v[0], v[1])
    } else {
        panic("instruction UNPCKHPD takes exactly 2 operands")
    }
}

func __asm_proxy_UNPCKHPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.UNPCKHPS(v[0], v[1])
    } else {
        panic("instruction UNPCKHPS takes exactly 2 operands")
    }
}

func __asm_proxy_UNPCKLPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.UNPCKLPD(v[0], v[1])
    } else {
        panic("instruction UNPCKLPD takes exactly 2 operands")
    }
}

func __asm_proxy_UNPCKLPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.UNPCKLPS(v[0], v[1])
    } else {
        panic("instruction UNPCKLPS takes exactly 2 operands")
    }
}

func __asm_proxy_VADDPD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VADDPD(v[0], v[1], v[2])
        case 4  : return p.VADDPD(v[0], v[1], v[2], v[3])
        default : panic("instruction VADDPD takes 3 or 4 operands")
    }
}

func __asm_proxy_VADDPS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VADDPS(v[0], v[1], v[2])
        case 4  : return p.VADDPS(v[0], v[1], v[2], v[3])
        default : panic("instruction VADDPS takes 3 or 4 operands")
    }
}

func __asm_proxy_VADDSD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VADDSD(v[0], v[1], v[2])
        case 4  : return p.VADDSD(v[0], v[1], v[2], v[3])
        default : panic("instruction VADDSD takes 3 or 4 operands")
    }
}

func __asm_proxy_VADDSS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VADDSS(v[0], v[1], v[2])
        case 4  : return p.VADDSS(v[0], v[1], v[2], v[3])
        default : panic("instruction VADDSS takes 3 or 4 operands")
    }
}

func __asm_proxy_VADDSUBPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VADDSUBPD(v[0], v[1], v[2])
    } else {
        panic("instruction VADDSUBPD takes exactly 3 operands")
    }
}

func __asm_proxy_VADDSUBPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VADDSUBPS(v[0], v[1], v[2])
    } else {
        panic("instruction VADDSUBPS takes exactly 3 operands")
    }
}

func __asm_proxy_VAESDEC__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VAESDEC(v[0], v[1], v[2])
    } else {
        panic("instruction VAESDEC takes exactly 3 operands")
    }
}

func __asm_proxy_VAESDECLAST__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VAESDECLAST(v[0], v[1], v[2])
    } else {
        panic("instruction VAESDECLAST takes exactly 3 operands")
    }
}

func __asm_proxy_VAESENC__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VAESENC(v[0], v[1], v[2])
    } else {
        panic("instruction VAESENC takes exactly 3 operands")
    }
}

func __asm_proxy_VAESENCLAST__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VAESENCLAST(v[0], v[1], v[2])
    } else {
        panic("instruction VAESENCLAST takes exactly 3 operands")
    }
}

func __asm_proxy_VAESIMC__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VAESIMC(v[0], v[1])
    } else {
        panic("instruction VAESIMC takes exactly 2 operands")
    }
}

func __asm_proxy_VAESKEYGENASSIST__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VAESKEYGENASSIST(v[0], v[1], v[2])
    } else {
        panic("instruction VAESKEYGENASSIST takes exactly 3 operands")
    }
}

func __asm_proxy_VALIGND__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VALIGND(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VALIGND takes exactly 4 operands")
    }
}

func __asm_proxy_VALIGNQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VALIGNQ(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VALIGNQ takes exactly 4 operands")
    }
}

func __asm_proxy_VANDNPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VANDNPD(v[0], v[1], v[2])
    } else {
        panic("instruction VANDNPD takes exactly 3 operands")
    }
}

func __asm_proxy_VANDNPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VANDNPS(v[0], v[1], v[2])
    } else {
        panic("instruction VANDNPS takes exactly 3 operands")
    }
}

func __asm_proxy_VANDPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VANDPD(v[0], v[1], v[2])
    } else {
        panic("instruction VANDPD takes exactly 3 operands")
    }
}

func __asm_proxy_VANDPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VANDPS(v[0], v[1], v[2])
    } else {
        panic("instruction VANDPS takes exactly 3 operands")
    }
}

func __asm_proxy_VBLENDMPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VBLENDMPD(v[0], v[1], v[2])
    } else {
        panic("instruction VBLENDMPD takes exactly 3 operands")
    }
}

func __asm_proxy_VBLENDMPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VBLENDMPS(v[0], v[1], v[2])
    } else {
        panic("instruction VBLENDMPS takes exactly 3 operands")
    }
}

func __asm_proxy_VBLENDPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VBLENDPD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VBLENDPD takes exactly 4 operands")
    }
}

func __asm_proxy_VBLENDPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VBLENDPS(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VBLENDPS takes exactly 4 operands")
    }
}

func __asm_proxy_VBLENDVPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VBLENDVPD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VBLENDVPD takes exactly 4 operands")
    }
}

func __asm_proxy_VBLENDVPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VBLENDVPS(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VBLENDVPS takes exactly 4 operands")
    }
}

func __asm_proxy_VBROADCASTF128__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VBROADCASTF128(v[0], v[1])
    } else {
        panic("instruction VBROADCASTF128 takes exactly 2 operands")
    }
}

func __asm_proxy_VBROADCASTF32X2__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VBROADCASTF32X2(v[0], v[1])
    } else {
        panic("instruction VBROADCASTF32X2 takes exactly 2 operands")
    }
}

func __asm_proxy_VBROADCASTF32X4__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VBROADCASTF32X4(v[0], v[1])
    } else {
        panic("instruction VBROADCASTF32X4 takes exactly 2 operands")
    }
}

func __asm_proxy_VBROADCASTF32X8__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VBROADCASTF32X8(v[0], v[1])
    } else {
        panic("instruction VBROADCASTF32X8 takes exactly 2 operands")
    }
}

func __asm_proxy_VBROADCASTF64X2__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VBROADCASTF64X2(v[0], v[1])
    } else {
        panic("instruction VBROADCASTF64X2 takes exactly 2 operands")
    }
}

func __asm_proxy_VBROADCASTF64X4__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VBROADCASTF64X4(v[0], v[1])
    } else {
        panic("instruction VBROADCASTF64X4 takes exactly 2 operands")
    }
}

func __asm_proxy_VBROADCASTI128__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VBROADCASTI128(v[0], v[1])
    } else {
        panic("instruction VBROADCASTI128 takes exactly 2 operands")
    }
}

func __asm_proxy_VBROADCASTI32X2__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VBROADCASTI32X2(v[0], v[1])
    } else {
        panic("instruction VBROADCASTI32X2 takes exactly 2 operands")
    }
}

func __asm_proxy_VBROADCASTI32X4__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VBROADCASTI32X4(v[0], v[1])
    } else {
        panic("instruction VBROADCASTI32X4 takes exactly 2 operands")
    }
}

func __asm_proxy_VBROADCASTI32X8__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VBROADCASTI32X8(v[0], v[1])
    } else {
        panic("instruction VBROADCASTI32X8 takes exactly 2 operands")
    }
}

func __asm_proxy_VBROADCASTI64X2__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VBROADCASTI64X2(v[0], v[1])
    } else {
        panic("instruction VBROADCASTI64X2 takes exactly 2 operands")
    }
}

func __asm_proxy_VBROADCASTI64X4__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VBROADCASTI64X4(v[0], v[1])
    } else {
        panic("instruction VBROADCASTI64X4 takes exactly 2 operands")
    }
}

func __asm_proxy_VBROADCASTSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VBROADCASTSD(v[0], v[1])
    } else {
        panic("instruction VBROADCASTSD takes exactly 2 operands")
    }
}

func __asm_proxy_VBROADCASTSS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VBROADCASTSS(v[0], v[1])
    } else {
        panic("instruction VBROADCASTSS takes exactly 2 operands")
    }
}

func __asm_proxy_VCMPPD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 4  : return p.VCMPPD(v[0], v[1], v[2], v[3])
        case 5  : return p.VCMPPD(v[0], v[1], v[2], v[3], v[4])
        default : panic("instruction VCMPPD takes 4 or 5 operands")
    }
}

func __asm_proxy_VCMPPS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 4  : return p.VCMPPS(v[0], v[1], v[2], v[3])
        case 5  : return p.VCMPPS(v[0], v[1], v[2], v[3], v[4])
        default : panic("instruction VCMPPS takes 4 or 5 operands")
    }
}

func __asm_proxy_VCMPSD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 4  : return p.VCMPSD(v[0], v[1], v[2], v[3])
        case 5  : return p.VCMPSD(v[0], v[1], v[2], v[3], v[4])
        default : panic("instruction VCMPSD takes 4 or 5 operands")
    }
}

func __asm_proxy_VCMPSS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 4  : return p.VCMPSS(v[0], v[1], v[2], v[3])
        case 5  : return p.VCMPSS(v[0], v[1], v[2], v[3], v[4])
        default : panic("instruction VCMPSS takes 4 or 5 operands")
    }
}

func __asm_proxy_VCOMISD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCOMISD(v[0], v[1])
        case 3  : return p.VCOMISD(v[0], v[1], v[2])
        default : panic("instruction VCOMISD takes 2 or 3 operands")
    }
}

func __asm_proxy_VCOMISS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCOMISS(v[0], v[1])
        case 3  : return p.VCOMISS(v[0], v[1], v[2])
        default : panic("instruction VCOMISS takes 2 or 3 operands")
    }
}

func __asm_proxy_VCOMPRESSPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VCOMPRESSPD(v[0], v[1])
    } else {
        panic("instruction VCOMPRESSPD takes exactly 2 operands")
    }
}

func __asm_proxy_VCOMPRESSPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VCOMPRESSPS(v[0], v[1])
    } else {
        panic("instruction VCOMPRESSPS takes exactly 2 operands")
    }
}

func __asm_proxy_VCVTDQ2PD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VCVTDQ2PD(v[0], v[1])
    } else {
        panic("instruction VCVTDQ2PD takes exactly 2 operands")
    }
}

func __asm_proxy_VCVTDQ2PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTDQ2PS(v[0], v[1])
        case 3  : return p.VCVTDQ2PS(v[0], v[1], v[2])
        default : panic("instruction VCVTDQ2PS takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTPD2DQ__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTPD2DQ(v[0], v[1])
        case 3  : return p.VCVTPD2DQ(v[0], v[1], v[2])
        default : panic("instruction VCVTPD2DQ takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTPD2PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTPD2PS(v[0], v[1])
        case 3  : return p.VCVTPD2PS(v[0], v[1], v[2])
        default : panic("instruction VCVTPD2PS takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTPD2QQ__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTPD2QQ(v[0], v[1])
        case 3  : return p.VCVTPD2QQ(v[0], v[1], v[2])
        default : panic("instruction VCVTPD2QQ takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTPD2UDQ__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTPD2UDQ(v[0], v[1])
        case 3  : return p.VCVTPD2UDQ(v[0], v[1], v[2])
        default : panic("instruction VCVTPD2UDQ takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTPD2UQQ__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTPD2UQQ(v[0], v[1])
        case 3  : return p.VCVTPD2UQQ(v[0], v[1], v[2])
        default : panic("instruction VCVTPD2UQQ takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTPH2PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTPH2PS(v[0], v[1])
        case 3  : return p.VCVTPH2PS(v[0], v[1], v[2])
        default : panic("instruction VCVTPH2PS takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTPS2DQ__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTPS2DQ(v[0], v[1])
        case 3  : return p.VCVTPS2DQ(v[0], v[1], v[2])
        default : panic("instruction VCVTPS2DQ takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTPS2PD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTPS2PD(v[0], v[1])
        case 3  : return p.VCVTPS2PD(v[0], v[1], v[2])
        default : panic("instruction VCVTPS2PD takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTPS2PH__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VCVTPS2PH(v[0], v[1], v[2])
        case 4  : return p.VCVTPS2PH(v[0], v[1], v[2], v[3])
        default : panic("instruction VCVTPS2PH takes 3 or 4 operands")
    }
}

func __asm_proxy_VCVTPS2QQ__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTPS2QQ(v[0], v[1])
        case 3  : return p.VCVTPS2QQ(v[0], v[1], v[2])
        default : panic("instruction VCVTPS2QQ takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTPS2UDQ__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTPS2UDQ(v[0], v[1])
        case 3  : return p.VCVTPS2UDQ(v[0], v[1], v[2])
        default : panic("instruction VCVTPS2UDQ takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTPS2UQQ__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTPS2UQQ(v[0], v[1])
        case 3  : return p.VCVTPS2UQQ(v[0], v[1], v[2])
        default : panic("instruction VCVTPS2UQQ takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTQQ2PD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTQQ2PD(v[0], v[1])
        case 3  : return p.VCVTQQ2PD(v[0], v[1], v[2])
        default : panic("instruction VCVTQQ2PD takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTQQ2PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTQQ2PS(v[0], v[1])
        case 3  : return p.VCVTQQ2PS(v[0], v[1], v[2])
        default : panic("instruction VCVTQQ2PS takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTSD2SI__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTSD2SI(v[0], v[1])
        case 3  : return p.VCVTSD2SI(v[0], v[1], v[2])
        default : panic("instruction VCVTSD2SI takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTSD2SS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VCVTSD2SS(v[0], v[1], v[2])
        case 4  : return p.VCVTSD2SS(v[0], v[1], v[2], v[3])
        default : panic("instruction VCVTSD2SS takes 3 or 4 operands")
    }
}

func __asm_proxy_VCVTSD2USI__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTSD2USI(v[0], v[1])
        case 3  : return p.VCVTSD2USI(v[0], v[1], v[2])
        default : panic("instruction VCVTSD2USI takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTSI2SD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VCVTSI2SD(v[0], v[1], v[2])
        case 4  : return p.VCVTSI2SD(v[0], v[1], v[2], v[3])
        default : panic("instruction VCVTSI2SD takes 3 or 4 operands")
    }
}

func __asm_proxy_VCVTSI2SS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VCVTSI2SS(v[0], v[1], v[2])
        case 4  : return p.VCVTSI2SS(v[0], v[1], v[2], v[3])
        default : panic("instruction VCVTSI2SS takes 3 or 4 operands")
    }
}

func __asm_proxy_VCVTSS2SD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VCVTSS2SD(v[0], v[1], v[2])
        case 4  : return p.VCVTSS2SD(v[0], v[1], v[2], v[3])
        default : panic("instruction VCVTSS2SD takes 3 or 4 operands")
    }
}

func __asm_proxy_VCVTSS2SI__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTSS2SI(v[0], v[1])
        case 3  : return p.VCVTSS2SI(v[0], v[1], v[2])
        default : panic("instruction VCVTSS2SI takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTSS2USI__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTSS2USI(v[0], v[1])
        case 3  : return p.VCVTSS2USI(v[0], v[1], v[2])
        default : panic("instruction VCVTSS2USI takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTTPD2DQ__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTTPD2DQ(v[0], v[1])
        case 3  : return p.VCVTTPD2DQ(v[0], v[1], v[2])
        default : panic("instruction VCVTTPD2DQ takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTTPD2QQ__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTTPD2QQ(v[0], v[1])
        case 3  : return p.VCVTTPD2QQ(v[0], v[1], v[2])
        default : panic("instruction VCVTTPD2QQ takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTTPD2UDQ__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTTPD2UDQ(v[0], v[1])
        case 3  : return p.VCVTTPD2UDQ(v[0], v[1], v[2])
        default : panic("instruction VCVTTPD2UDQ takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTTPD2UQQ__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTTPD2UQQ(v[0], v[1])
        case 3  : return p.VCVTTPD2UQQ(v[0], v[1], v[2])
        default : panic("instruction VCVTTPD2UQQ takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTTPS2DQ__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTTPS2DQ(v[0], v[1])
        case 3  : return p.VCVTTPS2DQ(v[0], v[1], v[2])
        default : panic("instruction VCVTTPS2DQ takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTTPS2QQ__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTTPS2QQ(v[0], v[1])
        case 3  : return p.VCVTTPS2QQ(v[0], v[1], v[2])
        default : panic("instruction VCVTTPS2QQ takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTTPS2UDQ__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTTPS2UDQ(v[0], v[1])
        case 3  : return p.VCVTTPS2UDQ(v[0], v[1], v[2])
        default : panic("instruction VCVTTPS2UDQ takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTTPS2UQQ__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTTPS2UQQ(v[0], v[1])
        case 3  : return p.VCVTTPS2UQQ(v[0], v[1], v[2])
        default : panic("instruction VCVTTPS2UQQ takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTTSD2SI__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTTSD2SI(v[0], v[1])
        case 3  : return p.VCVTTSD2SI(v[0], v[1], v[2])
        default : panic("instruction VCVTTSD2SI takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTTSD2USI__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTTSD2USI(v[0], v[1])
        case 3  : return p.VCVTTSD2USI(v[0], v[1], v[2])
        default : panic("instruction VCVTTSD2USI takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTTSS2SI__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTTSS2SI(v[0], v[1])
        case 3  : return p.VCVTTSS2SI(v[0], v[1], v[2])
        default : panic("instruction VCVTTSS2SI takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTTSS2USI__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTTSS2USI(v[0], v[1])
        case 3  : return p.VCVTTSS2USI(v[0], v[1], v[2])
        default : panic("instruction VCVTTSS2USI takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTUDQ2PD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VCVTUDQ2PD(v[0], v[1])
    } else {
        panic("instruction VCVTUDQ2PD takes exactly 2 operands")
    }
}

func __asm_proxy_VCVTUDQ2PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTUDQ2PS(v[0], v[1])
        case 3  : return p.VCVTUDQ2PS(v[0], v[1], v[2])
        default : panic("instruction VCVTUDQ2PS takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTUQQ2PD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTUQQ2PD(v[0], v[1])
        case 3  : return p.VCVTUQQ2PD(v[0], v[1], v[2])
        default : panic("instruction VCVTUQQ2PD takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTUQQ2PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VCVTUQQ2PS(v[0], v[1])
        case 3  : return p.VCVTUQQ2PS(v[0], v[1], v[2])
        default : panic("instruction VCVTUQQ2PS takes 2 or 3 operands")
    }
}

func __asm_proxy_VCVTUSI2SD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VCVTUSI2SD(v[0], v[1], v[2])
        case 4  : return p.VCVTUSI2SD(v[0], v[1], v[2], v[3])
        default : panic("instruction VCVTUSI2SD takes 3 or 4 operands")
    }
}

func __asm_proxy_VCVTUSI2SS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VCVTUSI2SS(v[0], v[1], v[2])
        case 4  : return p.VCVTUSI2SS(v[0], v[1], v[2], v[3])
        default : panic("instruction VCVTUSI2SS takes 3 or 4 operands")
    }
}

func __asm_proxy_VDBPSADBW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VDBPSADBW(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VDBPSADBW takes exactly 4 operands")
    }
}

func __asm_proxy_VDIVPD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VDIVPD(v[0], v[1], v[2])
        case 4  : return p.VDIVPD(v[0], v[1], v[2], v[3])
        default : panic("instruction VDIVPD takes 3 or 4 operands")
    }
}

func __asm_proxy_VDIVPS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VDIVPS(v[0], v[1], v[2])
        case 4  : return p.VDIVPS(v[0], v[1], v[2], v[3])
        default : panic("instruction VDIVPS takes 3 or 4 operands")
    }
}

func __asm_proxy_VDIVSD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VDIVSD(v[0], v[1], v[2])
        case 4  : return p.VDIVSD(v[0], v[1], v[2], v[3])
        default : panic("instruction VDIVSD takes 3 or 4 operands")
    }
}

func __asm_proxy_VDIVSS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VDIVSS(v[0], v[1], v[2])
        case 4  : return p.VDIVSS(v[0], v[1], v[2], v[3])
        default : panic("instruction VDIVSS takes 3 or 4 operands")
    }
}

func __asm_proxy_VDPPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VDPPD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VDPPD takes exactly 4 operands")
    }
}

func __asm_proxy_VDPPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VDPPS(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VDPPS takes exactly 4 operands")
    }
}

func __asm_proxy_VEXP2PD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VEXP2PD(v[0], v[1])
        case 3  : return p.VEXP2PD(v[0], v[1], v[2])
        default : panic("instruction VEXP2PD takes 2 or 3 operands")
    }
}

func __asm_proxy_VEXP2PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VEXP2PS(v[0], v[1])
        case 3  : return p.VEXP2PS(v[0], v[1], v[2])
        default : panic("instruction VEXP2PS takes 2 or 3 operands")
    }
}

func __asm_proxy_VEXPANDPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VEXPANDPD(v[0], v[1])
    } else {
        panic("instruction VEXPANDPD takes exactly 2 operands")
    }
}

func __asm_proxy_VEXPANDPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VEXPANDPS(v[0], v[1])
    } else {
        panic("instruction VEXPANDPS takes exactly 2 operands")
    }
}

func __asm_proxy_VEXTRACTF128__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VEXTRACTF128(v[0], v[1], v[2])
    } else {
        panic("instruction VEXTRACTF128 takes exactly 3 operands")
    }
}

func __asm_proxy_VEXTRACTF32X4__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VEXTRACTF32X4(v[0], v[1], v[2])
    } else {
        panic("instruction VEXTRACTF32X4 takes exactly 3 operands")
    }
}

func __asm_proxy_VEXTRACTF32X8__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VEXTRACTF32X8(v[0], v[1], v[2])
    } else {
        panic("instruction VEXTRACTF32X8 takes exactly 3 operands")
    }
}

func __asm_proxy_VEXTRACTF64X2__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VEXTRACTF64X2(v[0], v[1], v[2])
    } else {
        panic("instruction VEXTRACTF64X2 takes exactly 3 operands")
    }
}

func __asm_proxy_VEXTRACTF64X4__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VEXTRACTF64X4(v[0], v[1], v[2])
    } else {
        panic("instruction VEXTRACTF64X4 takes exactly 3 operands")
    }
}

func __asm_proxy_VEXTRACTI128__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VEXTRACTI128(v[0], v[1], v[2])
    } else {
        panic("instruction VEXTRACTI128 takes exactly 3 operands")
    }
}

func __asm_proxy_VEXTRACTI32X4__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VEXTRACTI32X4(v[0], v[1], v[2])
    } else {
        panic("instruction VEXTRACTI32X4 takes exactly 3 operands")
    }
}

func __asm_proxy_VEXTRACTI32X8__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VEXTRACTI32X8(v[0], v[1], v[2])
    } else {
        panic("instruction VEXTRACTI32X8 takes exactly 3 operands")
    }
}

func __asm_proxy_VEXTRACTI64X2__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VEXTRACTI64X2(v[0], v[1], v[2])
    } else {
        panic("instruction VEXTRACTI64X2 takes exactly 3 operands")
    }
}

func __asm_proxy_VEXTRACTI64X4__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VEXTRACTI64X4(v[0], v[1], v[2])
    } else {
        panic("instruction VEXTRACTI64X4 takes exactly 3 operands")
    }
}

func __asm_proxy_VEXTRACTPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VEXTRACTPS(v[0], v[1], v[2])
    } else {
        panic("instruction VEXTRACTPS takes exactly 3 operands")
    }
}

func __asm_proxy_VFIXUPIMMPD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 4  : return p.VFIXUPIMMPD(v[0], v[1], v[2], v[3])
        case 5  : return p.VFIXUPIMMPD(v[0], v[1], v[2], v[3], v[4])
        default : panic("instruction VFIXUPIMMPD takes 4 or 5 operands")
    }
}

func __asm_proxy_VFIXUPIMMPS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 4  : return p.VFIXUPIMMPS(v[0], v[1], v[2], v[3])
        case 5  : return p.VFIXUPIMMPS(v[0], v[1], v[2], v[3], v[4])
        default : panic("instruction VFIXUPIMMPS takes 4 or 5 operands")
    }
}

func __asm_proxy_VFIXUPIMMSD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 4  : return p.VFIXUPIMMSD(v[0], v[1], v[2], v[3])
        case 5  : return p.VFIXUPIMMSD(v[0], v[1], v[2], v[3], v[4])
        default : panic("instruction VFIXUPIMMSD takes 4 or 5 operands")
    }
}

func __asm_proxy_VFIXUPIMMSS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 4  : return p.VFIXUPIMMSS(v[0], v[1], v[2], v[3])
        case 5  : return p.VFIXUPIMMSS(v[0], v[1], v[2], v[3], v[4])
        default : panic("instruction VFIXUPIMMSS takes 4 or 5 operands")
    }
}

func __asm_proxy_VFMADD132PD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMADD132PD(v[0], v[1], v[2])
        case 4  : return p.VFMADD132PD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMADD132PD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMADD132PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMADD132PS(v[0], v[1], v[2])
        case 4  : return p.VFMADD132PS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMADD132PS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMADD132SD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMADD132SD(v[0], v[1], v[2])
        case 4  : return p.VFMADD132SD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMADD132SD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMADD132SS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMADD132SS(v[0], v[1], v[2])
        case 4  : return p.VFMADD132SS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMADD132SS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMADD213PD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMADD213PD(v[0], v[1], v[2])
        case 4  : return p.VFMADD213PD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMADD213PD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMADD213PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMADD213PS(v[0], v[1], v[2])
        case 4  : return p.VFMADD213PS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMADD213PS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMADD213SD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMADD213SD(v[0], v[1], v[2])
        case 4  : return p.VFMADD213SD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMADD213SD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMADD213SS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMADD213SS(v[0], v[1], v[2])
        case 4  : return p.VFMADD213SS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMADD213SS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMADD231PD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMADD231PD(v[0], v[1], v[2])
        case 4  : return p.VFMADD231PD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMADD231PD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMADD231PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMADD231PS(v[0], v[1], v[2])
        case 4  : return p.VFMADD231PS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMADD231PS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMADD231SD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMADD231SD(v[0], v[1], v[2])
        case 4  : return p.VFMADD231SD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMADD231SD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMADD231SS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMADD231SS(v[0], v[1], v[2])
        case 4  : return p.VFMADD231SS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMADD231SS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMADDPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VFMADDPD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VFMADDPD takes exactly 4 operands")
    }
}

func __asm_proxy_VFMADDPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VFMADDPS(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VFMADDPS takes exactly 4 operands")
    }
}

func __asm_proxy_VFMADDSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VFMADDSD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VFMADDSD takes exactly 4 operands")
    }
}

func __asm_proxy_VFMADDSS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VFMADDSS(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VFMADDSS takes exactly 4 operands")
    }
}

func __asm_proxy_VFMADDSUB132PD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMADDSUB132PD(v[0], v[1], v[2])
        case 4  : return p.VFMADDSUB132PD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMADDSUB132PD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMADDSUB132PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMADDSUB132PS(v[0], v[1], v[2])
        case 4  : return p.VFMADDSUB132PS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMADDSUB132PS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMADDSUB213PD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMADDSUB213PD(v[0], v[1], v[2])
        case 4  : return p.VFMADDSUB213PD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMADDSUB213PD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMADDSUB213PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMADDSUB213PS(v[0], v[1], v[2])
        case 4  : return p.VFMADDSUB213PS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMADDSUB213PS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMADDSUB231PD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMADDSUB231PD(v[0], v[1], v[2])
        case 4  : return p.VFMADDSUB231PD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMADDSUB231PD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMADDSUB231PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMADDSUB231PS(v[0], v[1], v[2])
        case 4  : return p.VFMADDSUB231PS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMADDSUB231PS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMADDSUBPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VFMADDSUBPD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VFMADDSUBPD takes exactly 4 operands")
    }
}

func __asm_proxy_VFMADDSUBPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VFMADDSUBPS(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VFMADDSUBPS takes exactly 4 operands")
    }
}

func __asm_proxy_VFMSUB132PD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMSUB132PD(v[0], v[1], v[2])
        case 4  : return p.VFMSUB132PD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMSUB132PD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMSUB132PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMSUB132PS(v[0], v[1], v[2])
        case 4  : return p.VFMSUB132PS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMSUB132PS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMSUB132SD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMSUB132SD(v[0], v[1], v[2])
        case 4  : return p.VFMSUB132SD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMSUB132SD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMSUB132SS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMSUB132SS(v[0], v[1], v[2])
        case 4  : return p.VFMSUB132SS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMSUB132SS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMSUB213PD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMSUB213PD(v[0], v[1], v[2])
        case 4  : return p.VFMSUB213PD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMSUB213PD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMSUB213PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMSUB213PS(v[0], v[1], v[2])
        case 4  : return p.VFMSUB213PS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMSUB213PS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMSUB213SD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMSUB213SD(v[0], v[1], v[2])
        case 4  : return p.VFMSUB213SD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMSUB213SD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMSUB213SS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMSUB213SS(v[0], v[1], v[2])
        case 4  : return p.VFMSUB213SS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMSUB213SS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMSUB231PD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMSUB231PD(v[0], v[1], v[2])
        case 4  : return p.VFMSUB231PD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMSUB231PD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMSUB231PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMSUB231PS(v[0], v[1], v[2])
        case 4  : return p.VFMSUB231PS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMSUB231PS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMSUB231SD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMSUB231SD(v[0], v[1], v[2])
        case 4  : return p.VFMSUB231SD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMSUB231SD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMSUB231SS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMSUB231SS(v[0], v[1], v[2])
        case 4  : return p.VFMSUB231SS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMSUB231SS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMSUBADD132PD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMSUBADD132PD(v[0], v[1], v[2])
        case 4  : return p.VFMSUBADD132PD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMSUBADD132PD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMSUBADD132PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMSUBADD132PS(v[0], v[1], v[2])
        case 4  : return p.VFMSUBADD132PS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMSUBADD132PS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMSUBADD213PD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMSUBADD213PD(v[0], v[1], v[2])
        case 4  : return p.VFMSUBADD213PD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMSUBADD213PD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMSUBADD213PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMSUBADD213PS(v[0], v[1], v[2])
        case 4  : return p.VFMSUBADD213PS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMSUBADD213PS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMSUBADD231PD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMSUBADD231PD(v[0], v[1], v[2])
        case 4  : return p.VFMSUBADD231PD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMSUBADD231PD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMSUBADD231PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFMSUBADD231PS(v[0], v[1], v[2])
        case 4  : return p.VFMSUBADD231PS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFMSUBADD231PS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFMSUBADDPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VFMSUBADDPD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VFMSUBADDPD takes exactly 4 operands")
    }
}

func __asm_proxy_VFMSUBADDPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VFMSUBADDPS(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VFMSUBADDPS takes exactly 4 operands")
    }
}

func __asm_proxy_VFMSUBPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VFMSUBPD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VFMSUBPD takes exactly 4 operands")
    }
}

func __asm_proxy_VFMSUBPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VFMSUBPS(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VFMSUBPS takes exactly 4 operands")
    }
}

func __asm_proxy_VFMSUBSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VFMSUBSD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VFMSUBSD takes exactly 4 operands")
    }
}

func __asm_proxy_VFMSUBSS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VFMSUBSS(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VFMSUBSS takes exactly 4 operands")
    }
}

func __asm_proxy_VFNMADD132PD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFNMADD132PD(v[0], v[1], v[2])
        case 4  : return p.VFNMADD132PD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFNMADD132PD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFNMADD132PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFNMADD132PS(v[0], v[1], v[2])
        case 4  : return p.VFNMADD132PS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFNMADD132PS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFNMADD132SD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFNMADD132SD(v[0], v[1], v[2])
        case 4  : return p.VFNMADD132SD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFNMADD132SD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFNMADD132SS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFNMADD132SS(v[0], v[1], v[2])
        case 4  : return p.VFNMADD132SS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFNMADD132SS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFNMADD213PD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFNMADD213PD(v[0], v[1], v[2])
        case 4  : return p.VFNMADD213PD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFNMADD213PD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFNMADD213PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFNMADD213PS(v[0], v[1], v[2])
        case 4  : return p.VFNMADD213PS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFNMADD213PS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFNMADD213SD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFNMADD213SD(v[0], v[1], v[2])
        case 4  : return p.VFNMADD213SD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFNMADD213SD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFNMADD213SS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFNMADD213SS(v[0], v[1], v[2])
        case 4  : return p.VFNMADD213SS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFNMADD213SS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFNMADD231PD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFNMADD231PD(v[0], v[1], v[2])
        case 4  : return p.VFNMADD231PD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFNMADD231PD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFNMADD231PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFNMADD231PS(v[0], v[1], v[2])
        case 4  : return p.VFNMADD231PS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFNMADD231PS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFNMADD231SD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFNMADD231SD(v[0], v[1], v[2])
        case 4  : return p.VFNMADD231SD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFNMADD231SD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFNMADD231SS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFNMADD231SS(v[0], v[1], v[2])
        case 4  : return p.VFNMADD231SS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFNMADD231SS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFNMADDPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VFNMADDPD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VFNMADDPD takes exactly 4 operands")
    }
}

func __asm_proxy_VFNMADDPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VFNMADDPS(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VFNMADDPS takes exactly 4 operands")
    }
}

func __asm_proxy_VFNMADDSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VFNMADDSD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VFNMADDSD takes exactly 4 operands")
    }
}

func __asm_proxy_VFNMADDSS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VFNMADDSS(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VFNMADDSS takes exactly 4 operands")
    }
}

func __asm_proxy_VFNMSUB132PD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFNMSUB132PD(v[0], v[1], v[2])
        case 4  : return p.VFNMSUB132PD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFNMSUB132PD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFNMSUB132PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFNMSUB132PS(v[0], v[1], v[2])
        case 4  : return p.VFNMSUB132PS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFNMSUB132PS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFNMSUB132SD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFNMSUB132SD(v[0], v[1], v[2])
        case 4  : return p.VFNMSUB132SD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFNMSUB132SD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFNMSUB132SS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFNMSUB132SS(v[0], v[1], v[2])
        case 4  : return p.VFNMSUB132SS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFNMSUB132SS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFNMSUB213PD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFNMSUB213PD(v[0], v[1], v[2])
        case 4  : return p.VFNMSUB213PD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFNMSUB213PD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFNMSUB213PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFNMSUB213PS(v[0], v[1], v[2])
        case 4  : return p.VFNMSUB213PS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFNMSUB213PS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFNMSUB213SD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFNMSUB213SD(v[0], v[1], v[2])
        case 4  : return p.VFNMSUB213SD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFNMSUB213SD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFNMSUB213SS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFNMSUB213SS(v[0], v[1], v[2])
        case 4  : return p.VFNMSUB213SS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFNMSUB213SS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFNMSUB231PD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFNMSUB231PD(v[0], v[1], v[2])
        case 4  : return p.VFNMSUB231PD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFNMSUB231PD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFNMSUB231PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFNMSUB231PS(v[0], v[1], v[2])
        case 4  : return p.VFNMSUB231PS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFNMSUB231PS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFNMSUB231SD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFNMSUB231SD(v[0], v[1], v[2])
        case 4  : return p.VFNMSUB231SD(v[0], v[1], v[2], v[3])
        default : panic("instruction VFNMSUB231SD takes 3 or 4 operands")
    }
}

func __asm_proxy_VFNMSUB231SS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VFNMSUB231SS(v[0], v[1], v[2])
        case 4  : return p.VFNMSUB231SS(v[0], v[1], v[2], v[3])
        default : panic("instruction VFNMSUB231SS takes 3 or 4 operands")
    }
}

func __asm_proxy_VFNMSUBPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VFNMSUBPD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VFNMSUBPD takes exactly 4 operands")
    }
}

func __asm_proxy_VFNMSUBPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VFNMSUBPS(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VFNMSUBPS takes exactly 4 operands")
    }
}

func __asm_proxy_VFNMSUBSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VFNMSUBSD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VFNMSUBSD takes exactly 4 operands")
    }
}

func __asm_proxy_VFNMSUBSS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VFNMSUBSS(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VFNMSUBSS takes exactly 4 operands")
    }
}

func __asm_proxy_VFPCLASSPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VFPCLASSPD(v[0], v[1], v[2])
    } else {
        panic("instruction VFPCLASSPD takes exactly 3 operands")
    }
}

func __asm_proxy_VFPCLASSPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VFPCLASSPS(v[0], v[1], v[2])
    } else {
        panic("instruction VFPCLASSPS takes exactly 3 operands")
    }
}

func __asm_proxy_VFPCLASSSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VFPCLASSSD(v[0], v[1], v[2])
    } else {
        panic("instruction VFPCLASSSD takes exactly 3 operands")
    }
}

func __asm_proxy_VFPCLASSSS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VFPCLASSSS(v[0], v[1], v[2])
    } else {
        panic("instruction VFPCLASSSS takes exactly 3 operands")
    }
}

func __asm_proxy_VFRCZPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VFRCZPD(v[0], v[1])
    } else {
        panic("instruction VFRCZPD takes exactly 2 operands")
    }
}

func __asm_proxy_VFRCZPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VFRCZPS(v[0], v[1])
    } else {
        panic("instruction VFRCZPS takes exactly 2 operands")
    }
}

func __asm_proxy_VFRCZSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VFRCZSD(v[0], v[1])
    } else {
        panic("instruction VFRCZSD takes exactly 2 operands")
    }
}

func __asm_proxy_VFRCZSS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VFRCZSS(v[0], v[1])
    } else {
        panic("instruction VFRCZSS takes exactly 2 operands")
    }
}

func __asm_proxy_VGATHERDPD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VGATHERDPD(v[0], v[1])
        case 3  : return p.VGATHERDPD(v[0], v[1], v[2])
        default : panic("instruction VGATHERDPD takes 2 or 3 operands")
    }
}

func __asm_proxy_VGATHERDPS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VGATHERDPS(v[0], v[1])
        case 3  : return p.VGATHERDPS(v[0], v[1], v[2])
        default : panic("instruction VGATHERDPS takes 2 or 3 operands")
    }
}

func __asm_proxy_VGATHERPF0DPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.VGATHERPF0DPD(v[0])
    } else {
        panic("instruction VGATHERPF0DPD takes exactly 1 operand")
    }
}

func __asm_proxy_VGATHERPF0DPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.VGATHERPF0DPS(v[0])
    } else {
        panic("instruction VGATHERPF0DPS takes exactly 1 operand")
    }
}

func __asm_proxy_VGATHERPF0QPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.VGATHERPF0QPD(v[0])
    } else {
        panic("instruction VGATHERPF0QPD takes exactly 1 operand")
    }
}

func __asm_proxy_VGATHERPF0QPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.VGATHERPF0QPS(v[0])
    } else {
        panic("instruction VGATHERPF0QPS takes exactly 1 operand")
    }
}

func __asm_proxy_VGATHERPF1DPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.VGATHERPF1DPD(v[0])
    } else {
        panic("instruction VGATHERPF1DPD takes exactly 1 operand")
    }
}

func __asm_proxy_VGATHERPF1DPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.VGATHERPF1DPS(v[0])
    } else {
        panic("instruction VGATHERPF1DPS takes exactly 1 operand")
    }
}

func __asm_proxy_VGATHERPF1QPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.VGATHERPF1QPD(v[0])
    } else {
        panic("instruction VGATHERPF1QPD takes exactly 1 operand")
    }
}

func __asm_proxy_VGATHERPF1QPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.VGATHERPF1QPS(v[0])
    } else {
        panic("instruction VGATHERPF1QPS takes exactly 1 operand")
    }
}

func __asm_proxy_VGATHERQPD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VGATHERQPD(v[0], v[1])
        case 3  : return p.VGATHERQPD(v[0], v[1], v[2])
        default : panic("instruction VGATHERQPD takes 2 or 3 operands")
    }
}

func __asm_proxy_VGATHERQPS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VGATHERQPS(v[0], v[1])
        case 3  : return p.VGATHERQPS(v[0], v[1], v[2])
        default : panic("instruction VGATHERQPS takes 2 or 3 operands")
    }
}

func __asm_proxy_VGETEXPPD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VGETEXPPD(v[0], v[1])
        case 3  : return p.VGETEXPPD(v[0], v[1], v[2])
        default : panic("instruction VGETEXPPD takes 2 or 3 operands")
    }
}

func __asm_proxy_VGETEXPPS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VGETEXPPS(v[0], v[1])
        case 3  : return p.VGETEXPPS(v[0], v[1], v[2])
        default : panic("instruction VGETEXPPS takes 2 or 3 operands")
    }
}

func __asm_proxy_VGETEXPSD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VGETEXPSD(v[0], v[1], v[2])
        case 4  : return p.VGETEXPSD(v[0], v[1], v[2], v[3])
        default : panic("instruction VGETEXPSD takes 3 or 4 operands")
    }
}

func __asm_proxy_VGETEXPSS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VGETEXPSS(v[0], v[1], v[2])
        case 4  : return p.VGETEXPSS(v[0], v[1], v[2], v[3])
        default : panic("instruction VGETEXPSS takes 3 or 4 operands")
    }
}

func __asm_proxy_VGETMANTPD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VGETMANTPD(v[0], v[1], v[2])
        case 4  : return p.VGETMANTPD(v[0], v[1], v[2], v[3])
        default : panic("instruction VGETMANTPD takes 3 or 4 operands")
    }
}

func __asm_proxy_VGETMANTPS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VGETMANTPS(v[0], v[1], v[2])
        case 4  : return p.VGETMANTPS(v[0], v[1], v[2], v[3])
        default : panic("instruction VGETMANTPS takes 3 or 4 operands")
    }
}

func __asm_proxy_VGETMANTSD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 4  : return p.VGETMANTSD(v[0], v[1], v[2], v[3])
        case 5  : return p.VGETMANTSD(v[0], v[1], v[2], v[3], v[4])
        default : panic("instruction VGETMANTSD takes 4 or 5 operands")
    }
}

func __asm_proxy_VGETMANTSS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 4  : return p.VGETMANTSS(v[0], v[1], v[2], v[3])
        case 5  : return p.VGETMANTSS(v[0], v[1], v[2], v[3], v[4])
        default : panic("instruction VGETMANTSS takes 4 or 5 operands")
    }
}

func __asm_proxy_VHADDPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VHADDPD(v[0], v[1], v[2])
    } else {
        panic("instruction VHADDPD takes exactly 3 operands")
    }
}

func __asm_proxy_VHADDPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VHADDPS(v[0], v[1], v[2])
    } else {
        panic("instruction VHADDPS takes exactly 3 operands")
    }
}

func __asm_proxy_VHSUBPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VHSUBPD(v[0], v[1], v[2])
    } else {
        panic("instruction VHSUBPD takes exactly 3 operands")
    }
}

func __asm_proxy_VHSUBPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VHSUBPS(v[0], v[1], v[2])
    } else {
        panic("instruction VHSUBPS takes exactly 3 operands")
    }
}

func __asm_proxy_VINSERTF128__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VINSERTF128(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VINSERTF128 takes exactly 4 operands")
    }
}

func __asm_proxy_VINSERTF32X4__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VINSERTF32X4(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VINSERTF32X4 takes exactly 4 operands")
    }
}

func __asm_proxy_VINSERTF32X8__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VINSERTF32X8(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VINSERTF32X8 takes exactly 4 operands")
    }
}

func __asm_proxy_VINSERTF64X2__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VINSERTF64X2(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VINSERTF64X2 takes exactly 4 operands")
    }
}

func __asm_proxy_VINSERTF64X4__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VINSERTF64X4(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VINSERTF64X4 takes exactly 4 operands")
    }
}

func __asm_proxy_VINSERTI128__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VINSERTI128(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VINSERTI128 takes exactly 4 operands")
    }
}

func __asm_proxy_VINSERTI32X4__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VINSERTI32X4(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VINSERTI32X4 takes exactly 4 operands")
    }
}

func __asm_proxy_VINSERTI32X8__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VINSERTI32X8(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VINSERTI32X8 takes exactly 4 operands")
    }
}

func __asm_proxy_VINSERTI64X2__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VINSERTI64X2(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VINSERTI64X2 takes exactly 4 operands")
    }
}

func __asm_proxy_VINSERTI64X4__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VINSERTI64X4(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VINSERTI64X4 takes exactly 4 operands")
    }
}

func __asm_proxy_VINSERTPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VINSERTPS(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VINSERTPS takes exactly 4 operands")
    }
}

func __asm_proxy_VLDDQU__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VLDDQU(v[0], v[1])
    } else {
        panic("instruction VLDDQU takes exactly 2 operands")
    }
}

func __asm_proxy_VLDMXCSR__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.VLDMXCSR(v[0])
    } else {
        panic("instruction VLDMXCSR takes exactly 1 operand")
    }
}

func __asm_proxy_VMASKMOVDQU__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VMASKMOVDQU(v[0], v[1])
    } else {
        panic("instruction VMASKMOVDQU takes exactly 2 operands")
    }
}

func __asm_proxy_VMASKMOVPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VMASKMOVPD(v[0], v[1], v[2])
    } else {
        panic("instruction VMASKMOVPD takes exactly 3 operands")
    }
}

func __asm_proxy_VMASKMOVPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VMASKMOVPS(v[0], v[1], v[2])
    } else {
        panic("instruction VMASKMOVPS takes exactly 3 operands")
    }
}

func __asm_proxy_VMAXPD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VMAXPD(v[0], v[1], v[2])
        case 4  : return p.VMAXPD(v[0], v[1], v[2], v[3])
        default : panic("instruction VMAXPD takes 3 or 4 operands")
    }
}

func __asm_proxy_VMAXPS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VMAXPS(v[0], v[1], v[2])
        case 4  : return p.VMAXPS(v[0], v[1], v[2], v[3])
        default : panic("instruction VMAXPS takes 3 or 4 operands")
    }
}

func __asm_proxy_VMAXSD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VMAXSD(v[0], v[1], v[2])
        case 4  : return p.VMAXSD(v[0], v[1], v[2], v[3])
        default : panic("instruction VMAXSD takes 3 or 4 operands")
    }
}

func __asm_proxy_VMAXSS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VMAXSS(v[0], v[1], v[2])
        case 4  : return p.VMAXSS(v[0], v[1], v[2], v[3])
        default : panic("instruction VMAXSS takes 3 or 4 operands")
    }
}

func __asm_proxy_VMINPD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VMINPD(v[0], v[1], v[2])
        case 4  : return p.VMINPD(v[0], v[1], v[2], v[3])
        default : panic("instruction VMINPD takes 3 or 4 operands")
    }
}

func __asm_proxy_VMINPS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VMINPS(v[0], v[1], v[2])
        case 4  : return p.VMINPS(v[0], v[1], v[2], v[3])
        default : panic("instruction VMINPS takes 3 or 4 operands")
    }
}

func __asm_proxy_VMINSD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VMINSD(v[0], v[1], v[2])
        case 4  : return p.VMINSD(v[0], v[1], v[2], v[3])
        default : panic("instruction VMINSD takes 3 or 4 operands")
    }
}

func __asm_proxy_VMINSS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VMINSS(v[0], v[1], v[2])
        case 4  : return p.VMINSS(v[0], v[1], v[2], v[3])
        default : panic("instruction VMINSS takes 3 or 4 operands")
    }
}

func __asm_proxy_VMOVAPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VMOVAPD(v[0], v[1])
    } else {
        panic("instruction VMOVAPD takes exactly 2 operands")
    }
}

func __asm_proxy_VMOVAPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VMOVAPS(v[0], v[1])
    } else {
        panic("instruction VMOVAPS takes exactly 2 operands")
    }
}

func __asm_proxy_VMOVD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VMOVD(v[0], v[1])
    } else {
        panic("instruction VMOVD takes exactly 2 operands")
    }
}

func __asm_proxy_VMOVDDUP__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VMOVDDUP(v[0], v[1])
    } else {
        panic("instruction VMOVDDUP takes exactly 2 operands")
    }
}

func __asm_proxy_VMOVDQA__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VMOVDQA(v[0], v[1])
    } else {
        panic("instruction VMOVDQA takes exactly 2 operands")
    }
}

func __asm_proxy_VMOVDQA32__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VMOVDQA32(v[0], v[1])
    } else {
        panic("instruction VMOVDQA32 takes exactly 2 operands")
    }
}

func __asm_proxy_VMOVDQA64__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VMOVDQA64(v[0], v[1])
    } else {
        panic("instruction VMOVDQA64 takes exactly 2 operands")
    }
}

func __asm_proxy_VMOVDQU__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VMOVDQU(v[0], v[1])
    } else {
        panic("instruction VMOVDQU takes exactly 2 operands")
    }
}

func __asm_proxy_VMOVDQU16__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VMOVDQU16(v[0], v[1])
    } else {
        panic("instruction VMOVDQU16 takes exactly 2 operands")
    }
}

func __asm_proxy_VMOVDQU32__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VMOVDQU32(v[0], v[1])
    } else {
        panic("instruction VMOVDQU32 takes exactly 2 operands")
    }
}

func __asm_proxy_VMOVDQU64__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VMOVDQU64(v[0], v[1])
    } else {
        panic("instruction VMOVDQU64 takes exactly 2 operands")
    }
}

func __asm_proxy_VMOVDQU8__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VMOVDQU8(v[0], v[1])
    } else {
        panic("instruction VMOVDQU8 takes exactly 2 operands")
    }
}

func __asm_proxy_VMOVHLPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VMOVHLPS(v[0], v[1], v[2])
    } else {
        panic("instruction VMOVHLPS takes exactly 3 operands")
    }
}

func __asm_proxy_VMOVHPD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VMOVHPD(v[0], v[1])
        case 3  : return p.VMOVHPD(v[0], v[1], v[2])
        default : panic("instruction VMOVHPD takes 2 or 3 operands")
    }
}

func __asm_proxy_VMOVHPS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VMOVHPS(v[0], v[1])
        case 3  : return p.VMOVHPS(v[0], v[1], v[2])
        default : panic("instruction VMOVHPS takes 2 or 3 operands")
    }
}

func __asm_proxy_VMOVLHPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VMOVLHPS(v[0], v[1], v[2])
    } else {
        panic("instruction VMOVLHPS takes exactly 3 operands")
    }
}

func __asm_proxy_VMOVLPD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VMOVLPD(v[0], v[1])
        case 3  : return p.VMOVLPD(v[0], v[1], v[2])
        default : panic("instruction VMOVLPD takes 2 or 3 operands")
    }
}

func __asm_proxy_VMOVLPS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VMOVLPS(v[0], v[1])
        case 3  : return p.VMOVLPS(v[0], v[1], v[2])
        default : panic("instruction VMOVLPS takes 2 or 3 operands")
    }
}

func __asm_proxy_VMOVMSKPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VMOVMSKPD(v[0], v[1])
    } else {
        panic("instruction VMOVMSKPD takes exactly 2 operands")
    }
}

func __asm_proxy_VMOVMSKPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VMOVMSKPS(v[0], v[1])
    } else {
        panic("instruction VMOVMSKPS takes exactly 2 operands")
    }
}

func __asm_proxy_VMOVNTDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VMOVNTDQ(v[0], v[1])
    } else {
        panic("instruction VMOVNTDQ takes exactly 2 operands")
    }
}

func __asm_proxy_VMOVNTDQA__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VMOVNTDQA(v[0], v[1])
    } else {
        panic("instruction VMOVNTDQA takes exactly 2 operands")
    }
}

func __asm_proxy_VMOVNTPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VMOVNTPD(v[0], v[1])
    } else {
        panic("instruction VMOVNTPD takes exactly 2 operands")
    }
}

func __asm_proxy_VMOVNTPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VMOVNTPS(v[0], v[1])
    } else {
        panic("instruction VMOVNTPS takes exactly 2 operands")
    }
}

func __asm_proxy_VMOVQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VMOVQ(v[0], v[1])
    } else {
        panic("instruction VMOVQ takes exactly 2 operands")
    }
}

func __asm_proxy_VMOVSD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VMOVSD(v[0], v[1])
        case 3  : return p.VMOVSD(v[0], v[1], v[2])
        default : panic("instruction VMOVSD takes 2 or 3 operands")
    }
}

func __asm_proxy_VMOVSHDUP__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VMOVSHDUP(v[0], v[1])
    } else {
        panic("instruction VMOVSHDUP takes exactly 2 operands")
    }
}

func __asm_proxy_VMOVSLDUP__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VMOVSLDUP(v[0], v[1])
    } else {
        panic("instruction VMOVSLDUP takes exactly 2 operands")
    }
}

func __asm_proxy_VMOVSS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VMOVSS(v[0], v[1])
        case 3  : return p.VMOVSS(v[0], v[1], v[2])
        default : panic("instruction VMOVSS takes 2 or 3 operands")
    }
}

func __asm_proxy_VMOVUPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VMOVUPD(v[0], v[1])
    } else {
        panic("instruction VMOVUPD takes exactly 2 operands")
    }
}

func __asm_proxy_VMOVUPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VMOVUPS(v[0], v[1])
    } else {
        panic("instruction VMOVUPS takes exactly 2 operands")
    }
}

func __asm_proxy_VMPSADBW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VMPSADBW(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VMPSADBW takes exactly 4 operands")
    }
}

func __asm_proxy_VMULPD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VMULPD(v[0], v[1], v[2])
        case 4  : return p.VMULPD(v[0], v[1], v[2], v[3])
        default : panic("instruction VMULPD takes 3 or 4 operands")
    }
}

func __asm_proxy_VMULPS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VMULPS(v[0], v[1], v[2])
        case 4  : return p.VMULPS(v[0], v[1], v[2], v[3])
        default : panic("instruction VMULPS takes 3 or 4 operands")
    }
}

func __asm_proxy_VMULSD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VMULSD(v[0], v[1], v[2])
        case 4  : return p.VMULSD(v[0], v[1], v[2], v[3])
        default : panic("instruction VMULSD takes 3 or 4 operands")
    }
}

func __asm_proxy_VMULSS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VMULSS(v[0], v[1], v[2])
        case 4  : return p.VMULSS(v[0], v[1], v[2], v[3])
        default : panic("instruction VMULSS takes 3 or 4 operands")
    }
}

func __asm_proxy_VORPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VORPD(v[0], v[1], v[2])
    } else {
        panic("instruction VORPD takes exactly 3 operands")
    }
}

func __asm_proxy_VORPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VORPS(v[0], v[1], v[2])
    } else {
        panic("instruction VORPS takes exactly 3 operands")
    }
}

func __asm_proxy_VPABSB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPABSB(v[0], v[1])
    } else {
        panic("instruction VPABSB takes exactly 2 operands")
    }
}

func __asm_proxy_VPABSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPABSD(v[0], v[1])
    } else {
        panic("instruction VPABSD takes exactly 2 operands")
    }
}

func __asm_proxy_VPABSQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPABSQ(v[0], v[1])
    } else {
        panic("instruction VPABSQ takes exactly 2 operands")
    }
}

func __asm_proxy_VPABSW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPABSW(v[0], v[1])
    } else {
        panic("instruction VPABSW takes exactly 2 operands")
    }
}

func __asm_proxy_VPACKSSDW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPACKSSDW(v[0], v[1], v[2])
    } else {
        panic("instruction VPACKSSDW takes exactly 3 operands")
    }
}

func __asm_proxy_VPACKSSWB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPACKSSWB(v[0], v[1], v[2])
    } else {
        panic("instruction VPACKSSWB takes exactly 3 operands")
    }
}

func __asm_proxy_VPACKUSDW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPACKUSDW(v[0], v[1], v[2])
    } else {
        panic("instruction VPACKUSDW takes exactly 3 operands")
    }
}

func __asm_proxy_VPACKUSWB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPACKUSWB(v[0], v[1], v[2])
    } else {
        panic("instruction VPACKUSWB takes exactly 3 operands")
    }
}

func __asm_proxy_VPADDB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPADDB(v[0], v[1], v[2])
    } else {
        panic("instruction VPADDB takes exactly 3 operands")
    }
}

func __asm_proxy_VPADDD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPADDD(v[0], v[1], v[2])
    } else {
        panic("instruction VPADDD takes exactly 3 operands")
    }
}

func __asm_proxy_VPADDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPADDQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPADDQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPADDSB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPADDSB(v[0], v[1], v[2])
    } else {
        panic("instruction VPADDSB takes exactly 3 operands")
    }
}

func __asm_proxy_VPADDSW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPADDSW(v[0], v[1], v[2])
    } else {
        panic("instruction VPADDSW takes exactly 3 operands")
    }
}

func __asm_proxy_VPADDUSB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPADDUSB(v[0], v[1], v[2])
    } else {
        panic("instruction VPADDUSB takes exactly 3 operands")
    }
}

func __asm_proxy_VPADDUSW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPADDUSW(v[0], v[1], v[2])
    } else {
        panic("instruction VPADDUSW takes exactly 3 operands")
    }
}

func __asm_proxy_VPADDW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPADDW(v[0], v[1], v[2])
    } else {
        panic("instruction VPADDW takes exactly 3 operands")
    }
}

func __asm_proxy_VPALIGNR__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPALIGNR(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPALIGNR takes exactly 4 operands")
    }
}

func __asm_proxy_VPAND__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPAND(v[0], v[1], v[2])
    } else {
        panic("instruction VPAND takes exactly 3 operands")
    }
}

func __asm_proxy_VPANDD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPANDD(v[0], v[1], v[2])
    } else {
        panic("instruction VPANDD takes exactly 3 operands")
    }
}

func __asm_proxy_VPANDN__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPANDN(v[0], v[1], v[2])
    } else {
        panic("instruction VPANDN takes exactly 3 operands")
    }
}

func __asm_proxy_VPANDND__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPANDND(v[0], v[1], v[2])
    } else {
        panic("instruction VPANDND takes exactly 3 operands")
    }
}

func __asm_proxy_VPANDNQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPANDNQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPANDNQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPANDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPANDQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPANDQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPAVGB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPAVGB(v[0], v[1], v[2])
    } else {
        panic("instruction VPAVGB takes exactly 3 operands")
    }
}

func __asm_proxy_VPAVGW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPAVGW(v[0], v[1], v[2])
    } else {
        panic("instruction VPAVGW takes exactly 3 operands")
    }
}

func __asm_proxy_VPBLENDD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPBLENDD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPBLENDD takes exactly 4 operands")
    }
}

func __asm_proxy_VPBLENDMB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPBLENDMB(v[0], v[1], v[2])
    } else {
        panic("instruction VPBLENDMB takes exactly 3 operands")
    }
}

func __asm_proxy_VPBLENDMD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPBLENDMD(v[0], v[1], v[2])
    } else {
        panic("instruction VPBLENDMD takes exactly 3 operands")
    }
}

func __asm_proxy_VPBLENDMQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPBLENDMQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPBLENDMQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPBLENDMW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPBLENDMW(v[0], v[1], v[2])
    } else {
        panic("instruction VPBLENDMW takes exactly 3 operands")
    }
}

func __asm_proxy_VPBLENDVB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPBLENDVB(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPBLENDVB takes exactly 4 operands")
    }
}

func __asm_proxy_VPBLENDW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPBLENDW(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPBLENDW takes exactly 4 operands")
    }
}

func __asm_proxy_VPBROADCASTB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPBROADCASTB(v[0], v[1])
    } else {
        panic("instruction VPBROADCASTB takes exactly 2 operands")
    }
}

func __asm_proxy_VPBROADCASTD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPBROADCASTD(v[0], v[1])
    } else {
        panic("instruction VPBROADCASTD takes exactly 2 operands")
    }
}

func __asm_proxy_VPBROADCASTMB2Q__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPBROADCASTMB2Q(v[0], v[1])
    } else {
        panic("instruction VPBROADCASTMB2Q takes exactly 2 operands")
    }
}

func __asm_proxy_VPBROADCASTMW2D__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPBROADCASTMW2D(v[0], v[1])
    } else {
        panic("instruction VPBROADCASTMW2D takes exactly 2 operands")
    }
}

func __asm_proxy_VPBROADCASTQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPBROADCASTQ(v[0], v[1])
    } else {
        panic("instruction VPBROADCASTQ takes exactly 2 operands")
    }
}

func __asm_proxy_VPBROADCASTW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPBROADCASTW(v[0], v[1])
    } else {
        panic("instruction VPBROADCASTW takes exactly 2 operands")
    }
}

func __asm_proxy_VPCLMULQDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPCLMULQDQ(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPCLMULQDQ takes exactly 4 operands")
    }
}

func __asm_proxy_VPCMOV__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPCMOV(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPCMOV takes exactly 4 operands")
    }
}

func __asm_proxy_VPCMPB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPCMPB(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPCMPB takes exactly 4 operands")
    }
}

func __asm_proxy_VPCMPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPCMPD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPCMPD takes exactly 4 operands")
    }
}

func __asm_proxy_VPCMPEQB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPCMPEQB(v[0], v[1], v[2])
    } else {
        panic("instruction VPCMPEQB takes exactly 3 operands")
    }
}

func __asm_proxy_VPCMPEQD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPCMPEQD(v[0], v[1], v[2])
    } else {
        panic("instruction VPCMPEQD takes exactly 3 operands")
    }
}

func __asm_proxy_VPCMPEQQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPCMPEQQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPCMPEQQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPCMPEQW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPCMPEQW(v[0], v[1], v[2])
    } else {
        panic("instruction VPCMPEQW takes exactly 3 operands")
    }
}

func __asm_proxy_VPCMPESTRI__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPCMPESTRI(v[0], v[1], v[2])
    } else {
        panic("instruction VPCMPESTRI takes exactly 3 operands")
    }
}

func __asm_proxy_VPCMPESTRM__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPCMPESTRM(v[0], v[1], v[2])
    } else {
        panic("instruction VPCMPESTRM takes exactly 3 operands")
    }
}

func __asm_proxy_VPCMPGTB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPCMPGTB(v[0], v[1], v[2])
    } else {
        panic("instruction VPCMPGTB takes exactly 3 operands")
    }
}

func __asm_proxy_VPCMPGTD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPCMPGTD(v[0], v[1], v[2])
    } else {
        panic("instruction VPCMPGTD takes exactly 3 operands")
    }
}

func __asm_proxy_VPCMPGTQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPCMPGTQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPCMPGTQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPCMPGTW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPCMPGTW(v[0], v[1], v[2])
    } else {
        panic("instruction VPCMPGTW takes exactly 3 operands")
    }
}

func __asm_proxy_VPCMPISTRI__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPCMPISTRI(v[0], v[1], v[2])
    } else {
        panic("instruction VPCMPISTRI takes exactly 3 operands")
    }
}

func __asm_proxy_VPCMPISTRM__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPCMPISTRM(v[0], v[1], v[2])
    } else {
        panic("instruction VPCMPISTRM takes exactly 3 operands")
    }
}

func __asm_proxy_VPCMPQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPCMPQ(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPCMPQ takes exactly 4 operands")
    }
}

func __asm_proxy_VPCMPUB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPCMPUB(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPCMPUB takes exactly 4 operands")
    }
}

func __asm_proxy_VPCMPUD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPCMPUD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPCMPUD takes exactly 4 operands")
    }
}

func __asm_proxy_VPCMPUQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPCMPUQ(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPCMPUQ takes exactly 4 operands")
    }
}

func __asm_proxy_VPCMPUW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPCMPUW(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPCMPUW takes exactly 4 operands")
    }
}

func __asm_proxy_VPCMPW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPCMPW(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPCMPW takes exactly 4 operands")
    }
}

func __asm_proxy_VPCOMB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPCOMB(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPCOMB takes exactly 4 operands")
    }
}

func __asm_proxy_VPCOMD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPCOMD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPCOMD takes exactly 4 operands")
    }
}

func __asm_proxy_VPCOMPRESSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPCOMPRESSD(v[0], v[1])
    } else {
        panic("instruction VPCOMPRESSD takes exactly 2 operands")
    }
}

func __asm_proxy_VPCOMPRESSQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPCOMPRESSQ(v[0], v[1])
    } else {
        panic("instruction VPCOMPRESSQ takes exactly 2 operands")
    }
}

func __asm_proxy_VPCOMQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPCOMQ(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPCOMQ takes exactly 4 operands")
    }
}

func __asm_proxy_VPCOMUB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPCOMUB(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPCOMUB takes exactly 4 operands")
    }
}

func __asm_proxy_VPCOMUD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPCOMUD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPCOMUD takes exactly 4 operands")
    }
}

func __asm_proxy_VPCOMUQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPCOMUQ(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPCOMUQ takes exactly 4 operands")
    }
}

func __asm_proxy_VPCOMUW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPCOMUW(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPCOMUW takes exactly 4 operands")
    }
}

func __asm_proxy_VPCOMW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPCOMW(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPCOMW takes exactly 4 operands")
    }
}

func __asm_proxy_VPCONFLICTD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPCONFLICTD(v[0], v[1])
    } else {
        panic("instruction VPCONFLICTD takes exactly 2 operands")
    }
}

func __asm_proxy_VPCONFLICTQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPCONFLICTQ(v[0], v[1])
    } else {
        panic("instruction VPCONFLICTQ takes exactly 2 operands")
    }
}

func __asm_proxy_VPERM2F128__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPERM2F128(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPERM2F128 takes exactly 4 operands")
    }
}

func __asm_proxy_VPERM2I128__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPERM2I128(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPERM2I128 takes exactly 4 operands")
    }
}

func __asm_proxy_VPERMB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPERMB(v[0], v[1], v[2])
    } else {
        panic("instruction VPERMB takes exactly 3 operands")
    }
}

func __asm_proxy_VPERMD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPERMD(v[0], v[1], v[2])
    } else {
        panic("instruction VPERMD takes exactly 3 operands")
    }
}

func __asm_proxy_VPERMI2B__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPERMI2B(v[0], v[1], v[2])
    } else {
        panic("instruction VPERMI2B takes exactly 3 operands")
    }
}

func __asm_proxy_VPERMI2D__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPERMI2D(v[0], v[1], v[2])
    } else {
        panic("instruction VPERMI2D takes exactly 3 operands")
    }
}

func __asm_proxy_VPERMI2PD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPERMI2PD(v[0], v[1], v[2])
    } else {
        panic("instruction VPERMI2PD takes exactly 3 operands")
    }
}

func __asm_proxy_VPERMI2PS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPERMI2PS(v[0], v[1], v[2])
    } else {
        panic("instruction VPERMI2PS takes exactly 3 operands")
    }
}

func __asm_proxy_VPERMI2Q__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPERMI2Q(v[0], v[1], v[2])
    } else {
        panic("instruction VPERMI2Q takes exactly 3 operands")
    }
}

func __asm_proxy_VPERMI2W__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPERMI2W(v[0], v[1], v[2])
    } else {
        panic("instruction VPERMI2W takes exactly 3 operands")
    }
}

func __asm_proxy_VPERMIL2PD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 5 {
        return p.VPERMIL2PD(v[0], v[1], v[2], v[3], v[4])
    } else {
        panic("instruction VPERMIL2PD takes exactly 5 operands")
    }
}

func __asm_proxy_VPERMIL2PS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 5 {
        return p.VPERMIL2PS(v[0], v[1], v[2], v[3], v[4])
    } else {
        panic("instruction VPERMIL2PS takes exactly 5 operands")
    }
}

func __asm_proxy_VPERMILPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPERMILPD(v[0], v[1], v[2])
    } else {
        panic("instruction VPERMILPD takes exactly 3 operands")
    }
}

func __asm_proxy_VPERMILPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPERMILPS(v[0], v[1], v[2])
    } else {
        panic("instruction VPERMILPS takes exactly 3 operands")
    }
}

func __asm_proxy_VPERMPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPERMPD(v[0], v[1], v[2])
    } else {
        panic("instruction VPERMPD takes exactly 3 operands")
    }
}

func __asm_proxy_VPERMPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPERMPS(v[0], v[1], v[2])
    } else {
        panic("instruction VPERMPS takes exactly 3 operands")
    }
}

func __asm_proxy_VPERMQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPERMQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPERMQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPERMT2B__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPERMT2B(v[0], v[1], v[2])
    } else {
        panic("instruction VPERMT2B takes exactly 3 operands")
    }
}

func __asm_proxy_VPERMT2D__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPERMT2D(v[0], v[1], v[2])
    } else {
        panic("instruction VPERMT2D takes exactly 3 operands")
    }
}

func __asm_proxy_VPERMT2PD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPERMT2PD(v[0], v[1], v[2])
    } else {
        panic("instruction VPERMT2PD takes exactly 3 operands")
    }
}

func __asm_proxy_VPERMT2PS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPERMT2PS(v[0], v[1], v[2])
    } else {
        panic("instruction VPERMT2PS takes exactly 3 operands")
    }
}

func __asm_proxy_VPERMT2Q__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPERMT2Q(v[0], v[1], v[2])
    } else {
        panic("instruction VPERMT2Q takes exactly 3 operands")
    }
}

func __asm_proxy_VPERMT2W__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPERMT2W(v[0], v[1], v[2])
    } else {
        panic("instruction VPERMT2W takes exactly 3 operands")
    }
}

func __asm_proxy_VPERMW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPERMW(v[0], v[1], v[2])
    } else {
        panic("instruction VPERMW takes exactly 3 operands")
    }
}

func __asm_proxy_VPEXPANDD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPEXPANDD(v[0], v[1])
    } else {
        panic("instruction VPEXPANDD takes exactly 2 operands")
    }
}

func __asm_proxy_VPEXPANDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPEXPANDQ(v[0], v[1])
    } else {
        panic("instruction VPEXPANDQ takes exactly 2 operands")
    }
}

func __asm_proxy_VPEXTRB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPEXTRB(v[0], v[1], v[2])
    } else {
        panic("instruction VPEXTRB takes exactly 3 operands")
    }
}

func __asm_proxy_VPEXTRD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPEXTRD(v[0], v[1], v[2])
    } else {
        panic("instruction VPEXTRD takes exactly 3 operands")
    }
}

func __asm_proxy_VPEXTRQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPEXTRQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPEXTRQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPEXTRW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPEXTRW(v[0], v[1], v[2])
    } else {
        panic("instruction VPEXTRW takes exactly 3 operands")
    }
}

func __asm_proxy_VPGATHERDD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VPGATHERDD(v[0], v[1])
        case 3  : return p.VPGATHERDD(v[0], v[1], v[2])
        default : panic("instruction VPGATHERDD takes 2 or 3 operands")
    }
}

func __asm_proxy_VPGATHERDQ__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VPGATHERDQ(v[0], v[1])
        case 3  : return p.VPGATHERDQ(v[0], v[1], v[2])
        default : panic("instruction VPGATHERDQ takes 2 or 3 operands")
    }
}

func __asm_proxy_VPGATHERQD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VPGATHERQD(v[0], v[1])
        case 3  : return p.VPGATHERQD(v[0], v[1], v[2])
        default : panic("instruction VPGATHERQD takes 2 or 3 operands")
    }
}

func __asm_proxy_VPGATHERQQ__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VPGATHERQQ(v[0], v[1])
        case 3  : return p.VPGATHERQQ(v[0], v[1], v[2])
        default : panic("instruction VPGATHERQQ takes 2 or 3 operands")
    }
}

func __asm_proxy_VPHADDBD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPHADDBD(v[0], v[1])
    } else {
        panic("instruction VPHADDBD takes exactly 2 operands")
    }
}

func __asm_proxy_VPHADDBQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPHADDBQ(v[0], v[1])
    } else {
        panic("instruction VPHADDBQ takes exactly 2 operands")
    }
}

func __asm_proxy_VPHADDBW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPHADDBW(v[0], v[1])
    } else {
        panic("instruction VPHADDBW takes exactly 2 operands")
    }
}

func __asm_proxy_VPHADDD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPHADDD(v[0], v[1], v[2])
    } else {
        panic("instruction VPHADDD takes exactly 3 operands")
    }
}

func __asm_proxy_VPHADDDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPHADDDQ(v[0], v[1])
    } else {
        panic("instruction VPHADDDQ takes exactly 2 operands")
    }
}

func __asm_proxy_VPHADDSW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPHADDSW(v[0], v[1], v[2])
    } else {
        panic("instruction VPHADDSW takes exactly 3 operands")
    }
}

func __asm_proxy_VPHADDUBD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPHADDUBD(v[0], v[1])
    } else {
        panic("instruction VPHADDUBD takes exactly 2 operands")
    }
}

func __asm_proxy_VPHADDUBQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPHADDUBQ(v[0], v[1])
    } else {
        panic("instruction VPHADDUBQ takes exactly 2 operands")
    }
}

func __asm_proxy_VPHADDUBW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPHADDUBW(v[0], v[1])
    } else {
        panic("instruction VPHADDUBW takes exactly 2 operands")
    }
}

func __asm_proxy_VPHADDUDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPHADDUDQ(v[0], v[1])
    } else {
        panic("instruction VPHADDUDQ takes exactly 2 operands")
    }
}

func __asm_proxy_VPHADDUWD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPHADDUWD(v[0], v[1])
    } else {
        panic("instruction VPHADDUWD takes exactly 2 operands")
    }
}

func __asm_proxy_VPHADDUWQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPHADDUWQ(v[0], v[1])
    } else {
        panic("instruction VPHADDUWQ takes exactly 2 operands")
    }
}

func __asm_proxy_VPHADDW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPHADDW(v[0], v[1], v[2])
    } else {
        panic("instruction VPHADDW takes exactly 3 operands")
    }
}

func __asm_proxy_VPHADDWD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPHADDWD(v[0], v[1])
    } else {
        panic("instruction VPHADDWD takes exactly 2 operands")
    }
}

func __asm_proxy_VPHADDWQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPHADDWQ(v[0], v[1])
    } else {
        panic("instruction VPHADDWQ takes exactly 2 operands")
    }
}

func __asm_proxy_VPHMINPOSUW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPHMINPOSUW(v[0], v[1])
    } else {
        panic("instruction VPHMINPOSUW takes exactly 2 operands")
    }
}

func __asm_proxy_VPHSUBBW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPHSUBBW(v[0], v[1])
    } else {
        panic("instruction VPHSUBBW takes exactly 2 operands")
    }
}

func __asm_proxy_VPHSUBD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPHSUBD(v[0], v[1], v[2])
    } else {
        panic("instruction VPHSUBD takes exactly 3 operands")
    }
}

func __asm_proxy_VPHSUBDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPHSUBDQ(v[0], v[1])
    } else {
        panic("instruction VPHSUBDQ takes exactly 2 operands")
    }
}

func __asm_proxy_VPHSUBSW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPHSUBSW(v[0], v[1], v[2])
    } else {
        panic("instruction VPHSUBSW takes exactly 3 operands")
    }
}

func __asm_proxy_VPHSUBW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPHSUBW(v[0], v[1], v[2])
    } else {
        panic("instruction VPHSUBW takes exactly 3 operands")
    }
}

func __asm_proxy_VPHSUBWD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPHSUBWD(v[0], v[1])
    } else {
        panic("instruction VPHSUBWD takes exactly 2 operands")
    }
}

func __asm_proxy_VPINSRB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPINSRB(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPINSRB takes exactly 4 operands")
    }
}

func __asm_proxy_VPINSRD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPINSRD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPINSRD takes exactly 4 operands")
    }
}

func __asm_proxy_VPINSRQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPINSRQ(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPINSRQ takes exactly 4 operands")
    }
}

func __asm_proxy_VPINSRW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPINSRW(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPINSRW takes exactly 4 operands")
    }
}

func __asm_proxy_VPLZCNTD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPLZCNTD(v[0], v[1])
    } else {
        panic("instruction VPLZCNTD takes exactly 2 operands")
    }
}

func __asm_proxy_VPLZCNTQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPLZCNTQ(v[0], v[1])
    } else {
        panic("instruction VPLZCNTQ takes exactly 2 operands")
    }
}

func __asm_proxy_VPMACSDD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPMACSDD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPMACSDD takes exactly 4 operands")
    }
}

func __asm_proxy_VPMACSDQH__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPMACSDQH(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPMACSDQH takes exactly 4 operands")
    }
}

func __asm_proxy_VPMACSDQL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPMACSDQL(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPMACSDQL takes exactly 4 operands")
    }
}

func __asm_proxy_VPMACSSDD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPMACSSDD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPMACSSDD takes exactly 4 operands")
    }
}

func __asm_proxy_VPMACSSDQH__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPMACSSDQH(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPMACSSDQH takes exactly 4 operands")
    }
}

func __asm_proxy_VPMACSSDQL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPMACSSDQL(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPMACSSDQL takes exactly 4 operands")
    }
}

func __asm_proxy_VPMACSSWD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPMACSSWD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPMACSSWD takes exactly 4 operands")
    }
}

func __asm_proxy_VPMACSSWW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPMACSSWW(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPMACSSWW takes exactly 4 operands")
    }
}

func __asm_proxy_VPMACSWD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPMACSWD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPMACSWD takes exactly 4 operands")
    }
}

func __asm_proxy_VPMACSWW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPMACSWW(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPMACSWW takes exactly 4 operands")
    }
}

func __asm_proxy_VPMADCSSWD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPMADCSSWD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPMADCSSWD takes exactly 4 operands")
    }
}

func __asm_proxy_VPMADCSWD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPMADCSWD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPMADCSWD takes exactly 4 operands")
    }
}

func __asm_proxy_VPMADD52HUQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMADD52HUQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPMADD52HUQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPMADD52LUQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMADD52LUQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPMADD52LUQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPMADDUBSW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMADDUBSW(v[0], v[1], v[2])
    } else {
        panic("instruction VPMADDUBSW takes exactly 3 operands")
    }
}

func __asm_proxy_VPMADDWD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMADDWD(v[0], v[1], v[2])
    } else {
        panic("instruction VPMADDWD takes exactly 3 operands")
    }
}

func __asm_proxy_VPMASKMOVD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMASKMOVD(v[0], v[1], v[2])
    } else {
        panic("instruction VPMASKMOVD takes exactly 3 operands")
    }
}

func __asm_proxy_VPMASKMOVQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMASKMOVQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPMASKMOVQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPMAXSB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMAXSB(v[0], v[1], v[2])
    } else {
        panic("instruction VPMAXSB takes exactly 3 operands")
    }
}

func __asm_proxy_VPMAXSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMAXSD(v[0], v[1], v[2])
    } else {
        panic("instruction VPMAXSD takes exactly 3 operands")
    }
}

func __asm_proxy_VPMAXSQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMAXSQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPMAXSQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPMAXSW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMAXSW(v[0], v[1], v[2])
    } else {
        panic("instruction VPMAXSW takes exactly 3 operands")
    }
}

func __asm_proxy_VPMAXUB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMAXUB(v[0], v[1], v[2])
    } else {
        panic("instruction VPMAXUB takes exactly 3 operands")
    }
}

func __asm_proxy_VPMAXUD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMAXUD(v[0], v[1], v[2])
    } else {
        panic("instruction VPMAXUD takes exactly 3 operands")
    }
}

func __asm_proxy_VPMAXUQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMAXUQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPMAXUQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPMAXUW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMAXUW(v[0], v[1], v[2])
    } else {
        panic("instruction VPMAXUW takes exactly 3 operands")
    }
}

func __asm_proxy_VPMINSB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMINSB(v[0], v[1], v[2])
    } else {
        panic("instruction VPMINSB takes exactly 3 operands")
    }
}

func __asm_proxy_VPMINSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMINSD(v[0], v[1], v[2])
    } else {
        panic("instruction VPMINSD takes exactly 3 operands")
    }
}

func __asm_proxy_VPMINSQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMINSQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPMINSQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPMINSW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMINSW(v[0], v[1], v[2])
    } else {
        panic("instruction VPMINSW takes exactly 3 operands")
    }
}

func __asm_proxy_VPMINUB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMINUB(v[0], v[1], v[2])
    } else {
        panic("instruction VPMINUB takes exactly 3 operands")
    }
}

func __asm_proxy_VPMINUD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMINUD(v[0], v[1], v[2])
    } else {
        panic("instruction VPMINUD takes exactly 3 operands")
    }
}

func __asm_proxy_VPMINUQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMINUQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPMINUQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPMINUW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMINUW(v[0], v[1], v[2])
    } else {
        panic("instruction VPMINUW takes exactly 3 operands")
    }
}

func __asm_proxy_VPMOVB2M__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVB2M(v[0], v[1])
    } else {
        panic("instruction VPMOVB2M takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVD2M__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVD2M(v[0], v[1])
    } else {
        panic("instruction VPMOVD2M takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVDB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVDB(v[0], v[1])
    } else {
        panic("instruction VPMOVDB takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVDW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVDW(v[0], v[1])
    } else {
        panic("instruction VPMOVDW takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVM2B__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVM2B(v[0], v[1])
    } else {
        panic("instruction VPMOVM2B takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVM2D__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVM2D(v[0], v[1])
    } else {
        panic("instruction VPMOVM2D takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVM2Q__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVM2Q(v[0], v[1])
    } else {
        panic("instruction VPMOVM2Q takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVM2W__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVM2W(v[0], v[1])
    } else {
        panic("instruction VPMOVM2W takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVMSKB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVMSKB(v[0], v[1])
    } else {
        panic("instruction VPMOVMSKB takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVQ2M__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVQ2M(v[0], v[1])
    } else {
        panic("instruction VPMOVQ2M takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVQB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVQB(v[0], v[1])
    } else {
        panic("instruction VPMOVQB takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVQD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVQD(v[0], v[1])
    } else {
        panic("instruction VPMOVQD takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVQW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVQW(v[0], v[1])
    } else {
        panic("instruction VPMOVQW takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVSDB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVSDB(v[0], v[1])
    } else {
        panic("instruction VPMOVSDB takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVSDW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVSDW(v[0], v[1])
    } else {
        panic("instruction VPMOVSDW takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVSQB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVSQB(v[0], v[1])
    } else {
        panic("instruction VPMOVSQB takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVSQD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVSQD(v[0], v[1])
    } else {
        panic("instruction VPMOVSQD takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVSQW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVSQW(v[0], v[1])
    } else {
        panic("instruction VPMOVSQW takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVSWB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVSWB(v[0], v[1])
    } else {
        panic("instruction VPMOVSWB takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVSXBD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVSXBD(v[0], v[1])
    } else {
        panic("instruction VPMOVSXBD takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVSXBQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVSXBQ(v[0], v[1])
    } else {
        panic("instruction VPMOVSXBQ takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVSXBW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVSXBW(v[0], v[1])
    } else {
        panic("instruction VPMOVSXBW takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVSXDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVSXDQ(v[0], v[1])
    } else {
        panic("instruction VPMOVSXDQ takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVSXWD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVSXWD(v[0], v[1])
    } else {
        panic("instruction VPMOVSXWD takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVSXWQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVSXWQ(v[0], v[1])
    } else {
        panic("instruction VPMOVSXWQ takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVUSDB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVUSDB(v[0], v[1])
    } else {
        panic("instruction VPMOVUSDB takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVUSDW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVUSDW(v[0], v[1])
    } else {
        panic("instruction VPMOVUSDW takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVUSQB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVUSQB(v[0], v[1])
    } else {
        panic("instruction VPMOVUSQB takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVUSQD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVUSQD(v[0], v[1])
    } else {
        panic("instruction VPMOVUSQD takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVUSQW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVUSQW(v[0], v[1])
    } else {
        panic("instruction VPMOVUSQW takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVUSWB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVUSWB(v[0], v[1])
    } else {
        panic("instruction VPMOVUSWB takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVW2M__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVW2M(v[0], v[1])
    } else {
        panic("instruction VPMOVW2M takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVWB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVWB(v[0], v[1])
    } else {
        panic("instruction VPMOVWB takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVZXBD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVZXBD(v[0], v[1])
    } else {
        panic("instruction VPMOVZXBD takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVZXBQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVZXBQ(v[0], v[1])
    } else {
        panic("instruction VPMOVZXBQ takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVZXBW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVZXBW(v[0], v[1])
    } else {
        panic("instruction VPMOVZXBW takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVZXDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVZXDQ(v[0], v[1])
    } else {
        panic("instruction VPMOVZXDQ takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVZXWD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVZXWD(v[0], v[1])
    } else {
        panic("instruction VPMOVZXWD takes exactly 2 operands")
    }
}

func __asm_proxy_VPMOVZXWQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPMOVZXWQ(v[0], v[1])
    } else {
        panic("instruction VPMOVZXWQ takes exactly 2 operands")
    }
}

func __asm_proxy_VPMULDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMULDQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPMULDQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPMULHRSW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMULHRSW(v[0], v[1], v[2])
    } else {
        panic("instruction VPMULHRSW takes exactly 3 operands")
    }
}

func __asm_proxy_VPMULHUW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMULHUW(v[0], v[1], v[2])
    } else {
        panic("instruction VPMULHUW takes exactly 3 operands")
    }
}

func __asm_proxy_VPMULHW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMULHW(v[0], v[1], v[2])
    } else {
        panic("instruction VPMULHW takes exactly 3 operands")
    }
}

func __asm_proxy_VPMULLD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMULLD(v[0], v[1], v[2])
    } else {
        panic("instruction VPMULLD takes exactly 3 operands")
    }
}

func __asm_proxy_VPMULLQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMULLQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPMULLQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPMULLW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMULLW(v[0], v[1], v[2])
    } else {
        panic("instruction VPMULLW takes exactly 3 operands")
    }
}

func __asm_proxy_VPMULTISHIFTQB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMULTISHIFTQB(v[0], v[1], v[2])
    } else {
        panic("instruction VPMULTISHIFTQB takes exactly 3 operands")
    }
}

func __asm_proxy_VPMULUDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPMULUDQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPMULUDQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPOPCNTD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPOPCNTD(v[0], v[1])
    } else {
        panic("instruction VPOPCNTD takes exactly 2 operands")
    }
}

func __asm_proxy_VPOPCNTQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPOPCNTQ(v[0], v[1])
    } else {
        panic("instruction VPOPCNTQ takes exactly 2 operands")
    }
}

func __asm_proxy_VPOR__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPOR(v[0], v[1], v[2])
    } else {
        panic("instruction VPOR takes exactly 3 operands")
    }
}

func __asm_proxy_VPORD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPORD(v[0], v[1], v[2])
    } else {
        panic("instruction VPORD takes exactly 3 operands")
    }
}

func __asm_proxy_VPORQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPORQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPORQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPPERM__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPPERM(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPPERM takes exactly 4 operands")
    }
}

func __asm_proxy_VPROLD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPROLD(v[0], v[1], v[2])
    } else {
        panic("instruction VPROLD takes exactly 3 operands")
    }
}

func __asm_proxy_VPROLQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPROLQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPROLQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPROLVD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPROLVD(v[0], v[1], v[2])
    } else {
        panic("instruction VPROLVD takes exactly 3 operands")
    }
}

func __asm_proxy_VPROLVQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPROLVQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPROLVQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPRORD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPRORD(v[0], v[1], v[2])
    } else {
        panic("instruction VPRORD takes exactly 3 operands")
    }
}

func __asm_proxy_VPRORQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPRORQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPRORQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPRORVD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPRORVD(v[0], v[1], v[2])
    } else {
        panic("instruction VPRORVD takes exactly 3 operands")
    }
}

func __asm_proxy_VPRORVQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPRORVQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPRORVQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPROTB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPROTB(v[0], v[1], v[2])
    } else {
        panic("instruction VPROTB takes exactly 3 operands")
    }
}

func __asm_proxy_VPROTD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPROTD(v[0], v[1], v[2])
    } else {
        panic("instruction VPROTD takes exactly 3 operands")
    }
}

func __asm_proxy_VPROTQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPROTQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPROTQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPROTW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPROTW(v[0], v[1], v[2])
    } else {
        panic("instruction VPROTW takes exactly 3 operands")
    }
}

func __asm_proxy_VPSADBW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSADBW(v[0], v[1], v[2])
    } else {
        panic("instruction VPSADBW takes exactly 3 operands")
    }
}

func __asm_proxy_VPSCATTERDD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPSCATTERDD(v[0], v[1])
    } else {
        panic("instruction VPSCATTERDD takes exactly 2 operands")
    }
}

func __asm_proxy_VPSCATTERDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPSCATTERDQ(v[0], v[1])
    } else {
        panic("instruction VPSCATTERDQ takes exactly 2 operands")
    }
}

func __asm_proxy_VPSCATTERQD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPSCATTERQD(v[0], v[1])
    } else {
        panic("instruction VPSCATTERQD takes exactly 2 operands")
    }
}

func __asm_proxy_VPSCATTERQQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPSCATTERQQ(v[0], v[1])
    } else {
        panic("instruction VPSCATTERQQ takes exactly 2 operands")
    }
}

func __asm_proxy_VPSHAB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSHAB(v[0], v[1], v[2])
    } else {
        panic("instruction VPSHAB takes exactly 3 operands")
    }
}

func __asm_proxy_VPSHAD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSHAD(v[0], v[1], v[2])
    } else {
        panic("instruction VPSHAD takes exactly 3 operands")
    }
}

func __asm_proxy_VPSHAQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSHAQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPSHAQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPSHAW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSHAW(v[0], v[1], v[2])
    } else {
        panic("instruction VPSHAW takes exactly 3 operands")
    }
}

func __asm_proxy_VPSHLB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSHLB(v[0], v[1], v[2])
    } else {
        panic("instruction VPSHLB takes exactly 3 operands")
    }
}

func __asm_proxy_VPSHLD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSHLD(v[0], v[1], v[2])
    } else {
        panic("instruction VPSHLD takes exactly 3 operands")
    }
}

func __asm_proxy_VPSHLQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSHLQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPSHLQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPSHLW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSHLW(v[0], v[1], v[2])
    } else {
        panic("instruction VPSHLW takes exactly 3 operands")
    }
}

func __asm_proxy_VPSHUFB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSHUFB(v[0], v[1], v[2])
    } else {
        panic("instruction VPSHUFB takes exactly 3 operands")
    }
}

func __asm_proxy_VPSHUFD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSHUFD(v[0], v[1], v[2])
    } else {
        panic("instruction VPSHUFD takes exactly 3 operands")
    }
}

func __asm_proxy_VPSHUFHW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSHUFHW(v[0], v[1], v[2])
    } else {
        panic("instruction VPSHUFHW takes exactly 3 operands")
    }
}

func __asm_proxy_VPSHUFLW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSHUFLW(v[0], v[1], v[2])
    } else {
        panic("instruction VPSHUFLW takes exactly 3 operands")
    }
}

func __asm_proxy_VPSIGNB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSIGNB(v[0], v[1], v[2])
    } else {
        panic("instruction VPSIGNB takes exactly 3 operands")
    }
}

func __asm_proxy_VPSIGND__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSIGND(v[0], v[1], v[2])
    } else {
        panic("instruction VPSIGND takes exactly 3 operands")
    }
}

func __asm_proxy_VPSIGNW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSIGNW(v[0], v[1], v[2])
    } else {
        panic("instruction VPSIGNW takes exactly 3 operands")
    }
}

func __asm_proxy_VPSLLD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSLLD(v[0], v[1], v[2])
    } else {
        panic("instruction VPSLLD takes exactly 3 operands")
    }
}

func __asm_proxy_VPSLLDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSLLDQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPSLLDQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPSLLQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSLLQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPSLLQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPSLLVD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSLLVD(v[0], v[1], v[2])
    } else {
        panic("instruction VPSLLVD takes exactly 3 operands")
    }
}

func __asm_proxy_VPSLLVQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSLLVQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPSLLVQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPSLLVW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSLLVW(v[0], v[1], v[2])
    } else {
        panic("instruction VPSLLVW takes exactly 3 operands")
    }
}

func __asm_proxy_VPSLLW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSLLW(v[0], v[1], v[2])
    } else {
        panic("instruction VPSLLW takes exactly 3 operands")
    }
}

func __asm_proxy_VPSRAD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSRAD(v[0], v[1], v[2])
    } else {
        panic("instruction VPSRAD takes exactly 3 operands")
    }
}

func __asm_proxy_VPSRAQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSRAQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPSRAQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPSRAVD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSRAVD(v[0], v[1], v[2])
    } else {
        panic("instruction VPSRAVD takes exactly 3 operands")
    }
}

func __asm_proxy_VPSRAVQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSRAVQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPSRAVQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPSRAVW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSRAVW(v[0], v[1], v[2])
    } else {
        panic("instruction VPSRAVW takes exactly 3 operands")
    }
}

func __asm_proxy_VPSRAW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSRAW(v[0], v[1], v[2])
    } else {
        panic("instruction VPSRAW takes exactly 3 operands")
    }
}

func __asm_proxy_VPSRLD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSRLD(v[0], v[1], v[2])
    } else {
        panic("instruction VPSRLD takes exactly 3 operands")
    }
}

func __asm_proxy_VPSRLDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSRLDQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPSRLDQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPSRLQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSRLQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPSRLQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPSRLVD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSRLVD(v[0], v[1], v[2])
    } else {
        panic("instruction VPSRLVD takes exactly 3 operands")
    }
}

func __asm_proxy_VPSRLVQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSRLVQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPSRLVQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPSRLVW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSRLVW(v[0], v[1], v[2])
    } else {
        panic("instruction VPSRLVW takes exactly 3 operands")
    }
}

func __asm_proxy_VPSRLW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSRLW(v[0], v[1], v[2])
    } else {
        panic("instruction VPSRLW takes exactly 3 operands")
    }
}

func __asm_proxy_VPSUBB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSUBB(v[0], v[1], v[2])
    } else {
        panic("instruction VPSUBB takes exactly 3 operands")
    }
}

func __asm_proxy_VPSUBD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSUBD(v[0], v[1], v[2])
    } else {
        panic("instruction VPSUBD takes exactly 3 operands")
    }
}

func __asm_proxy_VPSUBQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSUBQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPSUBQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPSUBSB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSUBSB(v[0], v[1], v[2])
    } else {
        panic("instruction VPSUBSB takes exactly 3 operands")
    }
}

func __asm_proxy_VPSUBSW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSUBSW(v[0], v[1], v[2])
    } else {
        panic("instruction VPSUBSW takes exactly 3 operands")
    }
}

func __asm_proxy_VPSUBUSB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSUBUSB(v[0], v[1], v[2])
    } else {
        panic("instruction VPSUBUSB takes exactly 3 operands")
    }
}

func __asm_proxy_VPSUBUSW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSUBUSW(v[0], v[1], v[2])
    } else {
        panic("instruction VPSUBUSW takes exactly 3 operands")
    }
}

func __asm_proxy_VPSUBW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPSUBW(v[0], v[1], v[2])
    } else {
        panic("instruction VPSUBW takes exactly 3 operands")
    }
}

func __asm_proxy_VPTERNLOGD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPTERNLOGD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPTERNLOGD takes exactly 4 operands")
    }
}

func __asm_proxy_VPTERNLOGQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VPTERNLOGQ(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VPTERNLOGQ takes exactly 4 operands")
    }
}

func __asm_proxy_VPTEST__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VPTEST(v[0], v[1])
    } else {
        panic("instruction VPTEST takes exactly 2 operands")
    }
}

func __asm_proxy_VPTESTMB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPTESTMB(v[0], v[1], v[2])
    } else {
        panic("instruction VPTESTMB takes exactly 3 operands")
    }
}

func __asm_proxy_VPTESTMD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPTESTMD(v[0], v[1], v[2])
    } else {
        panic("instruction VPTESTMD takes exactly 3 operands")
    }
}

func __asm_proxy_VPTESTMQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPTESTMQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPTESTMQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPTESTMW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPTESTMW(v[0], v[1], v[2])
    } else {
        panic("instruction VPTESTMW takes exactly 3 operands")
    }
}

func __asm_proxy_VPTESTNMB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPTESTNMB(v[0], v[1], v[2])
    } else {
        panic("instruction VPTESTNMB takes exactly 3 operands")
    }
}

func __asm_proxy_VPTESTNMD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPTESTNMD(v[0], v[1], v[2])
    } else {
        panic("instruction VPTESTNMD takes exactly 3 operands")
    }
}

func __asm_proxy_VPTESTNMQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPTESTNMQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPTESTNMQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPTESTNMW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPTESTNMW(v[0], v[1], v[2])
    } else {
        panic("instruction VPTESTNMW takes exactly 3 operands")
    }
}

func __asm_proxy_VPUNPCKHBW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPUNPCKHBW(v[0], v[1], v[2])
    } else {
        panic("instruction VPUNPCKHBW takes exactly 3 operands")
    }
}

func __asm_proxy_VPUNPCKHDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPUNPCKHDQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPUNPCKHDQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPUNPCKHQDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPUNPCKHQDQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPUNPCKHQDQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPUNPCKHWD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPUNPCKHWD(v[0], v[1], v[2])
    } else {
        panic("instruction VPUNPCKHWD takes exactly 3 operands")
    }
}

func __asm_proxy_VPUNPCKLBW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPUNPCKLBW(v[0], v[1], v[2])
    } else {
        panic("instruction VPUNPCKLBW takes exactly 3 operands")
    }
}

func __asm_proxy_VPUNPCKLDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPUNPCKLDQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPUNPCKLDQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPUNPCKLQDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPUNPCKLQDQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPUNPCKLQDQ takes exactly 3 operands")
    }
}

func __asm_proxy_VPUNPCKLWD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPUNPCKLWD(v[0], v[1], v[2])
    } else {
        panic("instruction VPUNPCKLWD takes exactly 3 operands")
    }
}

func __asm_proxy_VPXOR__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPXOR(v[0], v[1], v[2])
    } else {
        panic("instruction VPXOR takes exactly 3 operands")
    }
}

func __asm_proxy_VPXORD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPXORD(v[0], v[1], v[2])
    } else {
        panic("instruction VPXORD takes exactly 3 operands")
    }
}

func __asm_proxy_VPXORQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VPXORQ(v[0], v[1], v[2])
    } else {
        panic("instruction VPXORQ takes exactly 3 operands")
    }
}

func __asm_proxy_VRANGEPD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 4  : return p.VRANGEPD(v[0], v[1], v[2], v[3])
        case 5  : return p.VRANGEPD(v[0], v[1], v[2], v[3], v[4])
        default : panic("instruction VRANGEPD takes 4 or 5 operands")
    }
}

func __asm_proxy_VRANGEPS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 4  : return p.VRANGEPS(v[0], v[1], v[2], v[3])
        case 5  : return p.VRANGEPS(v[0], v[1], v[2], v[3], v[4])
        default : panic("instruction VRANGEPS takes 4 or 5 operands")
    }
}

func __asm_proxy_VRANGESD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 4  : return p.VRANGESD(v[0], v[1], v[2], v[3])
        case 5  : return p.VRANGESD(v[0], v[1], v[2], v[3], v[4])
        default : panic("instruction VRANGESD takes 4 or 5 operands")
    }
}

func __asm_proxy_VRANGESS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 4  : return p.VRANGESS(v[0], v[1], v[2], v[3])
        case 5  : return p.VRANGESS(v[0], v[1], v[2], v[3], v[4])
        default : panic("instruction VRANGESS takes 4 or 5 operands")
    }
}

func __asm_proxy_VRCP14PD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VRCP14PD(v[0], v[1])
    } else {
        panic("instruction VRCP14PD takes exactly 2 operands")
    }
}

func __asm_proxy_VRCP14PS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VRCP14PS(v[0], v[1])
    } else {
        panic("instruction VRCP14PS takes exactly 2 operands")
    }
}

func __asm_proxy_VRCP14SD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VRCP14SD(v[0], v[1], v[2])
    } else {
        panic("instruction VRCP14SD takes exactly 3 operands")
    }
}

func __asm_proxy_VRCP14SS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VRCP14SS(v[0], v[1], v[2])
    } else {
        panic("instruction VRCP14SS takes exactly 3 operands")
    }
}

func __asm_proxy_VRCP28PD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VRCP28PD(v[0], v[1])
        case 3  : return p.VRCP28PD(v[0], v[1], v[2])
        default : panic("instruction VRCP28PD takes 2 or 3 operands")
    }
}

func __asm_proxy_VRCP28PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VRCP28PS(v[0], v[1])
        case 3  : return p.VRCP28PS(v[0], v[1], v[2])
        default : panic("instruction VRCP28PS takes 2 or 3 operands")
    }
}

func __asm_proxy_VRCP28SD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VRCP28SD(v[0], v[1], v[2])
        case 4  : return p.VRCP28SD(v[0], v[1], v[2], v[3])
        default : panic("instruction VRCP28SD takes 3 or 4 operands")
    }
}

func __asm_proxy_VRCP28SS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VRCP28SS(v[0], v[1], v[2])
        case 4  : return p.VRCP28SS(v[0], v[1], v[2], v[3])
        default : panic("instruction VRCP28SS takes 3 or 4 operands")
    }
}

func __asm_proxy_VRCPPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VRCPPS(v[0], v[1])
    } else {
        panic("instruction VRCPPS takes exactly 2 operands")
    }
}

func __asm_proxy_VRCPSS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VRCPSS(v[0], v[1], v[2])
    } else {
        panic("instruction VRCPSS takes exactly 3 operands")
    }
}

func __asm_proxy_VREDUCEPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VREDUCEPD(v[0], v[1], v[2])
    } else {
        panic("instruction VREDUCEPD takes exactly 3 operands")
    }
}

func __asm_proxy_VREDUCEPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VREDUCEPS(v[0], v[1], v[2])
    } else {
        panic("instruction VREDUCEPS takes exactly 3 operands")
    }
}

func __asm_proxy_VREDUCESD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VREDUCESD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VREDUCESD takes exactly 4 operands")
    }
}

func __asm_proxy_VREDUCESS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VREDUCESS(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VREDUCESS takes exactly 4 operands")
    }
}

func __asm_proxy_VRNDSCALEPD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VRNDSCALEPD(v[0], v[1], v[2])
        case 4  : return p.VRNDSCALEPD(v[0], v[1], v[2], v[3])
        default : panic("instruction VRNDSCALEPD takes 3 or 4 operands")
    }
}

func __asm_proxy_VRNDSCALEPS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VRNDSCALEPS(v[0], v[1], v[2])
        case 4  : return p.VRNDSCALEPS(v[0], v[1], v[2], v[3])
        default : panic("instruction VRNDSCALEPS takes 3 or 4 operands")
    }
}

func __asm_proxy_VRNDSCALESD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 4  : return p.VRNDSCALESD(v[0], v[1], v[2], v[3])
        case 5  : return p.VRNDSCALESD(v[0], v[1], v[2], v[3], v[4])
        default : panic("instruction VRNDSCALESD takes 4 or 5 operands")
    }
}

func __asm_proxy_VRNDSCALESS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 4  : return p.VRNDSCALESS(v[0], v[1], v[2], v[3])
        case 5  : return p.VRNDSCALESS(v[0], v[1], v[2], v[3], v[4])
        default : panic("instruction VRNDSCALESS takes 4 or 5 operands")
    }
}

func __asm_proxy_VROUNDPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VROUNDPD(v[0], v[1], v[2])
    } else {
        panic("instruction VROUNDPD takes exactly 3 operands")
    }
}

func __asm_proxy_VROUNDPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VROUNDPS(v[0], v[1], v[2])
    } else {
        panic("instruction VROUNDPS takes exactly 3 operands")
    }
}

func __asm_proxy_VROUNDSD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VROUNDSD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VROUNDSD takes exactly 4 operands")
    }
}

func __asm_proxy_VROUNDSS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VROUNDSS(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VROUNDSS takes exactly 4 operands")
    }
}

func __asm_proxy_VRSQRT14PD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VRSQRT14PD(v[0], v[1])
    } else {
        panic("instruction VRSQRT14PD takes exactly 2 operands")
    }
}

func __asm_proxy_VRSQRT14PS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VRSQRT14PS(v[0], v[1])
    } else {
        panic("instruction VRSQRT14PS takes exactly 2 operands")
    }
}

func __asm_proxy_VRSQRT14SD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VRSQRT14SD(v[0], v[1], v[2])
    } else {
        panic("instruction VRSQRT14SD takes exactly 3 operands")
    }
}

func __asm_proxy_VRSQRT14SS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VRSQRT14SS(v[0], v[1], v[2])
    } else {
        panic("instruction VRSQRT14SS takes exactly 3 operands")
    }
}

func __asm_proxy_VRSQRT28PD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VRSQRT28PD(v[0], v[1])
        case 3  : return p.VRSQRT28PD(v[0], v[1], v[2])
        default : panic("instruction VRSQRT28PD takes 2 or 3 operands")
    }
}

func __asm_proxy_VRSQRT28PS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VRSQRT28PS(v[0], v[1])
        case 3  : return p.VRSQRT28PS(v[0], v[1], v[2])
        default : panic("instruction VRSQRT28PS takes 2 or 3 operands")
    }
}

func __asm_proxy_VRSQRT28SD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VRSQRT28SD(v[0], v[1], v[2])
        case 4  : return p.VRSQRT28SD(v[0], v[1], v[2], v[3])
        default : panic("instruction VRSQRT28SD takes 3 or 4 operands")
    }
}

func __asm_proxy_VRSQRT28SS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VRSQRT28SS(v[0], v[1], v[2])
        case 4  : return p.VRSQRT28SS(v[0], v[1], v[2], v[3])
        default : panic("instruction VRSQRT28SS takes 3 or 4 operands")
    }
}

func __asm_proxy_VRSQRTPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VRSQRTPS(v[0], v[1])
    } else {
        panic("instruction VRSQRTPS takes exactly 2 operands")
    }
}

func __asm_proxy_VRSQRTSS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VRSQRTSS(v[0], v[1], v[2])
    } else {
        panic("instruction VRSQRTSS takes exactly 3 operands")
    }
}

func __asm_proxy_VSCALEFPD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VSCALEFPD(v[0], v[1], v[2])
        case 4  : return p.VSCALEFPD(v[0], v[1], v[2], v[3])
        default : panic("instruction VSCALEFPD takes 3 or 4 operands")
    }
}

func __asm_proxy_VSCALEFPS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VSCALEFPS(v[0], v[1], v[2])
        case 4  : return p.VSCALEFPS(v[0], v[1], v[2], v[3])
        default : panic("instruction VSCALEFPS takes 3 or 4 operands")
    }
}

func __asm_proxy_VSCALEFSD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VSCALEFSD(v[0], v[1], v[2])
        case 4  : return p.VSCALEFSD(v[0], v[1], v[2], v[3])
        default : panic("instruction VSCALEFSD takes 3 or 4 operands")
    }
}

func __asm_proxy_VSCALEFSS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VSCALEFSS(v[0], v[1], v[2])
        case 4  : return p.VSCALEFSS(v[0], v[1], v[2], v[3])
        default : panic("instruction VSCALEFSS takes 3 or 4 operands")
    }
}

func __asm_proxy_VSCATTERDPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VSCATTERDPD(v[0], v[1])
    } else {
        panic("instruction VSCATTERDPD takes exactly 2 operands")
    }
}

func __asm_proxy_VSCATTERDPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VSCATTERDPS(v[0], v[1])
    } else {
        panic("instruction VSCATTERDPS takes exactly 2 operands")
    }
}

func __asm_proxy_VSCATTERPF0DPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.VSCATTERPF0DPD(v[0])
    } else {
        panic("instruction VSCATTERPF0DPD takes exactly 1 operand")
    }
}

func __asm_proxy_VSCATTERPF0DPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.VSCATTERPF0DPS(v[0])
    } else {
        panic("instruction VSCATTERPF0DPS takes exactly 1 operand")
    }
}

func __asm_proxy_VSCATTERPF0QPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.VSCATTERPF0QPD(v[0])
    } else {
        panic("instruction VSCATTERPF0QPD takes exactly 1 operand")
    }
}

func __asm_proxy_VSCATTERPF0QPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.VSCATTERPF0QPS(v[0])
    } else {
        panic("instruction VSCATTERPF0QPS takes exactly 1 operand")
    }
}

func __asm_proxy_VSCATTERPF1DPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.VSCATTERPF1DPD(v[0])
    } else {
        panic("instruction VSCATTERPF1DPD takes exactly 1 operand")
    }
}

func __asm_proxy_VSCATTERPF1DPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.VSCATTERPF1DPS(v[0])
    } else {
        panic("instruction VSCATTERPF1DPS takes exactly 1 operand")
    }
}

func __asm_proxy_VSCATTERPF1QPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.VSCATTERPF1QPD(v[0])
    } else {
        panic("instruction VSCATTERPF1QPD takes exactly 1 operand")
    }
}

func __asm_proxy_VSCATTERPF1QPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.VSCATTERPF1QPS(v[0])
    } else {
        panic("instruction VSCATTERPF1QPS takes exactly 1 operand")
    }
}

func __asm_proxy_VSCATTERQPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VSCATTERQPD(v[0], v[1])
    } else {
        panic("instruction VSCATTERQPD takes exactly 2 operands")
    }
}

func __asm_proxy_VSCATTERQPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VSCATTERQPS(v[0], v[1])
    } else {
        panic("instruction VSCATTERQPS takes exactly 2 operands")
    }
}

func __asm_proxy_VSHUFF32X4__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VSHUFF32X4(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VSHUFF32X4 takes exactly 4 operands")
    }
}

func __asm_proxy_VSHUFF64X2__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VSHUFF64X2(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VSHUFF64X2 takes exactly 4 operands")
    }
}

func __asm_proxy_VSHUFI32X4__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VSHUFI32X4(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VSHUFI32X4 takes exactly 4 operands")
    }
}

func __asm_proxy_VSHUFI64X2__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VSHUFI64X2(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VSHUFI64X2 takes exactly 4 operands")
    }
}

func __asm_proxy_VSHUFPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VSHUFPD(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VSHUFPD takes exactly 4 operands")
    }
}

func __asm_proxy_VSHUFPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 4 {
        return p.VSHUFPS(v[0], v[1], v[2], v[3])
    } else {
        panic("instruction VSHUFPS takes exactly 4 operands")
    }
}

func __asm_proxy_VSQRTPD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VSQRTPD(v[0], v[1])
        case 3  : return p.VSQRTPD(v[0], v[1], v[2])
        default : panic("instruction VSQRTPD takes 2 or 3 operands")
    }
}

func __asm_proxy_VSQRTPS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VSQRTPS(v[0], v[1])
        case 3  : return p.VSQRTPS(v[0], v[1], v[2])
        default : panic("instruction VSQRTPS takes 2 or 3 operands")
    }
}

func __asm_proxy_VSQRTSD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VSQRTSD(v[0], v[1], v[2])
        case 4  : return p.VSQRTSD(v[0], v[1], v[2], v[3])
        default : panic("instruction VSQRTSD takes 3 or 4 operands")
    }
}

func __asm_proxy_VSQRTSS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VSQRTSS(v[0], v[1], v[2])
        case 4  : return p.VSQRTSS(v[0], v[1], v[2], v[3])
        default : panic("instruction VSQRTSS takes 3 or 4 operands")
    }
}

func __asm_proxy_VSTMXCSR__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 1 {
        return p.VSTMXCSR(v[0])
    } else {
        panic("instruction VSTMXCSR takes exactly 1 operand")
    }
}

func __asm_proxy_VSUBPD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VSUBPD(v[0], v[1], v[2])
        case 4  : return p.VSUBPD(v[0], v[1], v[2], v[3])
        default : panic("instruction VSUBPD takes 3 or 4 operands")
    }
}

func __asm_proxy_VSUBPS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VSUBPS(v[0], v[1], v[2])
        case 4  : return p.VSUBPS(v[0], v[1], v[2], v[3])
        default : panic("instruction VSUBPS takes 3 or 4 operands")
    }
}

func __asm_proxy_VSUBSD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VSUBSD(v[0], v[1], v[2])
        case 4  : return p.VSUBSD(v[0], v[1], v[2], v[3])
        default : panic("instruction VSUBSD takes 3 or 4 operands")
    }
}

func __asm_proxy_VSUBSS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 3  : return p.VSUBSS(v[0], v[1], v[2])
        case 4  : return p.VSUBSS(v[0], v[1], v[2], v[3])
        default : panic("instruction VSUBSS takes 3 or 4 operands")
    }
}

func __asm_proxy_VTESTPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VTESTPD(v[0], v[1])
    } else {
        panic("instruction VTESTPD takes exactly 2 operands")
    }
}

func __asm_proxy_VTESTPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.VTESTPS(v[0], v[1])
    } else {
        panic("instruction VTESTPS takes exactly 2 operands")
    }
}

func __asm_proxy_VUCOMISD__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VUCOMISD(v[0], v[1])
        case 3  : return p.VUCOMISD(v[0], v[1], v[2])
        default : panic("instruction VUCOMISD takes 2 or 3 operands")
    }
}

func __asm_proxy_VUCOMISS__(p *Program, v ...interface{}) *Instruction {
    switch len(v) {
        case 2  : return p.VUCOMISS(v[0], v[1])
        case 3  : return p.VUCOMISS(v[0], v[1], v[2])
        default : panic("instruction VUCOMISS takes 2 or 3 operands")
    }
}

func __asm_proxy_VUNPCKHPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VUNPCKHPD(v[0], v[1], v[2])
    } else {
        panic("instruction VUNPCKHPD takes exactly 3 operands")
    }
}

func __asm_proxy_VUNPCKHPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VUNPCKHPS(v[0], v[1], v[2])
    } else {
        panic("instruction VUNPCKHPS takes exactly 3 operands")
    }
}

func __asm_proxy_VUNPCKLPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VUNPCKLPD(v[0], v[1], v[2])
    } else {
        panic("instruction VUNPCKLPD takes exactly 3 operands")
    }
}

func __asm_proxy_VUNPCKLPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VUNPCKLPS(v[0], v[1], v[2])
    } else {
        panic("instruction VUNPCKLPS takes exactly 3 operands")
    }
}

func __asm_proxy_VXORPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VXORPD(v[0], v[1], v[2])
    } else {
        panic("instruction VXORPD takes exactly 3 operands")
    }
}

func __asm_proxy_VXORPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 3 {
        return p.VXORPS(v[0], v[1], v[2])
    } else {
        panic("instruction VXORPS takes exactly 3 operands")
    }
}

func __asm_proxy_VZEROALL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.VZEROALL()
    } else {
        panic("instruction VZEROALL takes no operands")
    }
}

func __asm_proxy_VZEROUPPER__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.VZEROUPPER()
    } else {
        panic("instruction VZEROUPPER takes no operands")
    }
}

func __asm_proxy_XADDB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.XADDB(v[0], v[1])
    } else {
        panic("instruction XADDB takes exactly 2 operands")
    }
}

func __asm_proxy_XADDL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.XADDL(v[0], v[1])
    } else {
        panic("instruction XADDL takes exactly 2 operands")
    }
}

func __asm_proxy_XADDQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.XADDQ(v[0], v[1])
    } else {
        panic("instruction XADDQ takes exactly 2 operands")
    }
}

func __asm_proxy_XADDW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.XADDW(v[0], v[1])
    } else {
        panic("instruction XADDW takes exactly 2 operands")
    }
}

func __asm_proxy_XCHGB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.XCHGB(v[0], v[1])
    } else {
        panic("instruction XCHGB takes exactly 2 operands")
    }
}

func __asm_proxy_XCHGL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.XCHGL(v[0], v[1])
    } else {
        panic("instruction XCHGL takes exactly 2 operands")
    }
}

func __asm_proxy_XCHGQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.XCHGQ(v[0], v[1])
    } else {
        panic("instruction XCHGQ takes exactly 2 operands")
    }
}

func __asm_proxy_XCHGW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.XCHGW(v[0], v[1])
    } else {
        panic("instruction XCHGW takes exactly 2 operands")
    }
}

func __asm_proxy_XGETBV__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.XGETBV()
    } else {
        panic("instruction XGETBV takes no operands")
    }
}

func __asm_proxy_XLATB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 0 {
        return p.XLATB()
    } else {
        panic("instruction XLATB takes no operands")
    }
}

func __asm_proxy_XORB__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.XORB(v[0], v[1])
    } else {
        panic("instruction XORB takes exactly 2 operands")
    }
}

func __asm_proxy_XORL__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.XORL(v[0], v[1])
    } else {
        panic("instruction XORL takes exactly 2 operands")
    }
}

func __asm_proxy_XORPD__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.XORPD(v[0], v[1])
    } else {
        panic("instruction XORPD takes exactly 2 operands")
    }
}

func __asm_proxy_XORPS__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.XORPS(v[0], v[1])
    } else {
        panic("instruction XORPS takes exactly 2 operands")
    }
}

func __asm_proxy_XORQ__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.XORQ(v[0], v[1])
    } else {
        panic("instruction XORQ takes exactly 2 operands")
    }
}

func __asm_proxy_XORW__(p *Program, v ...interface{}) *Instruction {
    if len(v) == 2 {
        return p.XORW(v[0], v[1])
    } else {
        panic("instruction XORW takes exactly 2 operands")
    }
}
