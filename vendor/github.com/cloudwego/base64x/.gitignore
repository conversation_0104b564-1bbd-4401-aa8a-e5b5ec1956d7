# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# the result of the go build
output*
output/*

# Files generated by IDEs
.idea/
*.iml

# Vim swap files
*.swp

# Vscode files
.vscode

