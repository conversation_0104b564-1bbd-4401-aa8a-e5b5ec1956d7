// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package base64x

var text__native_entry__ = []byte{
	0x48, 0x8d, 0x05, 0xf9, 0xff, 0xff, 0xff, // leaq         $-7(%rip), %rax
	0x48, 0x89, 0x44, 0x24, 0x08, //0x00000007 movq         %rax, $8(%rsp)
	0xc3, //0x0000000c retq         
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000000d .p2align 5, 0x00
	//0x00000020 LCPI0_0
	0x01, //0x00000020 .byte 1
	0x00, //0x00000021 .byte 0
	0x02, //0x00000022 .byte 2
	0x01, //0x00000023 .byte 1
	0x04, //0x00000024 .byte 4
	0x03, //0x00000025 .byte 3
	0x05, //0x00000026 .byte 5
	0x04, //0x00000027 .byte 4
	0x07, //0x00000028 .byte 7
	0x06, //0x00000029 .byte 6
	0x08, //0x0000002a .byte 8
	0x07, //0x0000002b .byte 7
	0x0a, //0x0000002c .byte 10
	0x09, //0x0000002d .byte 9
	0x0b, //0x0000002e .byte 11
	0x0a, //0x0000002f .byte 10
	0x01, //0x00000030 .byte 1
	0x00, //0x00000031 .byte 0
	0x02, //0x00000032 .byte 2
	0x01, //0x00000033 .byte 1
	0x04, //0x00000034 .byte 4
	0x03, //0x00000035 .byte 3
	0x05, //0x00000036 .byte 5
	0x04, //0x00000037 .byte 4
	0x07, //0x00000038 .byte 7
	0x06, //0x00000039 .byte 6
	0x08, //0x0000003a .byte 8
	0x07, //0x0000003b .byte 7
	0x0a, //0x0000003c .byte 10
	0x09, //0x0000003d .byte 9
	0x0b, //0x0000003e .byte 11
	0x0a, //0x0000003f .byte 10
	//0x00000040 LCPI0_1
	0x00, 0xfc, //0x00000040 .word 64512
	0xc0, 0x0f, //0x00000042 .word 4032
	0x00, 0xfc, //0x00000044 .word 64512
	0xc0, 0x0f, //0x00000046 .word 4032
	0x00, 0xfc, //0x00000048 .word 64512
	0xc0, 0x0f, //0x0000004a .word 4032
	0x00, 0xfc, //0x0000004c .word 64512
	0xc0, 0x0f, //0x0000004e .word 4032
	0x00, 0xfc, //0x00000050 .word 64512
	0xc0, 0x0f, //0x00000052 .word 4032
	0x00, 0xfc, //0x00000054 .word 64512
	0xc0, 0x0f, //0x00000056 .word 4032
	0x00, 0xfc, //0x00000058 .word 64512
	0xc0, 0x0f, //0x0000005a .word 4032
	0x00, 0xfc, //0x0000005c .word 64512
	0xc0, 0x0f, //0x0000005e .word 4032
	//0x00000060 LCPI0_2
	0x40, 0x00, //0x00000060 .word 64
	0x00, 0x04, //0x00000062 .word 1024
	0x40, 0x00, //0x00000064 .word 64
	0x00, 0x04, //0x00000066 .word 1024
	0x40, 0x00, //0x00000068 .word 64
	0x00, 0x04, //0x0000006a .word 1024
	0x40, 0x00, //0x0000006c .word 64
	0x00, 0x04, //0x0000006e .word 1024
	0x40, 0x00, //0x00000070 .word 64
	0x00, 0x04, //0x00000072 .word 1024
	0x40, 0x00, //0x00000074 .word 64
	0x00, 0x04, //0x00000076 .word 1024
	0x40, 0x00, //0x00000078 .word 64
	0x00, 0x04, //0x0000007a .word 1024
	0x40, 0x00, //0x0000007c .word 64
	0x00, 0x04, //0x0000007e .word 1024
	//0x00000080 LCPI0_3
	0xf0, 0x03, //0x00000080 .word 1008
	0x3f, 0x00, //0x00000082 .word 63
	0xf0, 0x03, //0x00000084 .word 1008
	0x3f, 0x00, //0x00000086 .word 63
	0xf0, 0x03, //0x00000088 .word 1008
	0x3f, 0x00, //0x0000008a .word 63
	0xf0, 0x03, //0x0000008c .word 1008
	0x3f, 0x00, //0x0000008e .word 63
	0xf0, 0x03, //0x00000090 .word 1008
	0x3f, 0x00, //0x00000092 .word 63
	0xf0, 0x03, //0x00000094 .word 1008
	0x3f, 0x00, //0x00000096 .word 63
	0xf0, 0x03, //0x00000098 .word 1008
	0x3f, 0x00, //0x0000009a .word 63
	0xf0, 0x03, //0x0000009c .word 1008
	0x3f, 0x00, //0x0000009e .word 63
	//0x000000a0 LCPI0_4
	0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1a, //0x000000a0 QUAD $0x1a1a1a1a1a1a1a1a; QUAD $0x1a1a1a1a1a1a1a1a  // .space 16, '\x1a\x1a\x1a\x1a\x1a\x1a\x1a\x1a\x1a\x1a\x1a\x1a\x1a\x1a\x1a\x1a'
	0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1a, 0x1a, //0x000000b0 QUAD $0x1a1a1a1a1a1a1a1a; QUAD $0x1a1a1a1a1a1a1a1a  // .space 16, '\x1a\x1a\x1a\x1a\x1a\x1a\x1a\x1a\x1a\x1a\x1a\x1a\x1a\x1a\x1a\x1a'
	//0x000000c0 LCPI0_5
	0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x000000c0 QUAD $0x3333333333333333; QUAD $0x3333333333333333  // .space 16, '3333333333333333'
	0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x000000d0 QUAD $0x3333333333333333; QUAD $0x3333333333333333  // .space 16, '3333333333333333'
	//0x000000e0 LCPI0_6
	0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, //0x000000e0 QUAD $0x0d0d0d0d0d0d0d0d; QUAD $0x0d0d0d0d0d0d0d0d  // .space 16, '\r\r\r\r\r\r\r\r\r\r\r\r\r\r\r\r'
	0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, 0x0d, //0x000000f0 QUAD $0x0d0d0d0d0d0d0d0d; QUAD $0x0d0d0d0d0d0d0d0d  // .space 16, '\r\r\r\r\r\r\r\r\r\r\r\r\r\r\r\r'
	//0x00000100 .p2align 4, 0x90
	//0x00000100 _b64encode
	0x55, //0x00000100 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000101 movq         %rsp, %rbp
	0x41, 0x57, //0x00000104 pushq        %r15
	0x41, 0x56, //0x00000106 pushq        %r14
	0x41, 0x54, //0x00000108 pushq        %r12
	0x53, //0x0000010a pushq        %rbx
	0x4c, 0x8b, 0x4e, 0x08, //0x0000010b movq         $8(%rsi), %r9
	0x4d, 0x85, 0xc9, //0x0000010f testq        %r9, %r9
	0x0f, 0x84, 0x3c, 0x03, 0x00, 0x00, //0x00000112 je           LBB0_26
	0x4c, 0x8b, 0x07, //0x00000118 movq         (%rdi), %r8
	0x4c, 0x03, 0x47, 0x08, //0x0000011b addq         $8(%rdi), %r8
	0x4c, 0x8b, 0x26, //0x0000011f movq         (%rsi), %r12
	0x4d, 0x01, 0xe1, //0x00000122 addq         %r12, %r9
	0xf6, 0xc2, 0x01, //0x00000125 testb        $1, %dl
	0x48, 0x8d, 0x0d, 0x11, 0x49, 0x00, 0x00, //0x00000128 leaq         $18705(%rip), %rcx  /* _VecEncodeCharsetStd+0(%rip) */
	0x4c, 0x8d, 0x15, 0x6a, 0x49, 0x00, 0x00, //0x0000012f leaq         $18794(%rip), %r10  /* _VecEncodeCharsetURL+0(%rip) */
	0x4c, 0x0f, 0x44, 0xd1, //0x00000136 cmoveq       %rcx, %r10
	0x48, 0x8d, 0x0d, 0xbf, 0x48, 0x00, 0x00, //0x0000013a leaq         $18623(%rip), %rcx  /* _TabEncodeCharsetStd+0(%rip) */
	0x4c, 0x8d, 0x1d, 0x18, 0x49, 0x00, 0x00, //0x00000141 leaq         $18712(%rip), %r11  /* _TabEncodeCharsetURL+0(%rip) */
	0x4c, 0x0f, 0x44, 0xd9, //0x00000148 cmoveq       %rcx, %r11
	0xf6, 0xc2, 0x04, //0x0000014c testb        $4, %dl
	0x0f, 0x84, 0xc8, 0x00, 0x00, 0x00, //0x0000014f je           LBB0_2
	0x49, 0x8d, 0x71, 0xe4, //0x00000155 leaq         $-28(%r9), %rsi
	0x49, 0x39, 0xf4, //0x00000159 cmpq         %rsi, %r12
	0x0f, 0x87, 0x4e, 0x02, 0x00, 0x00, //0x0000015c ja           LBB0_4
	0xc4, 0xc1, 0x7e, 0x6f, 0x02, //0x00000162 vmovdqu      (%r10), %ymm0
	0xc5, 0xfd, 0x6f, 0x0d, 0xb1, 0xfe, 0xff, 0xff, //0x00000167 vmovdqa      $-335(%rip), %ymm1  /* LCPI0_0+0(%rip) */
	0xc5, 0xfd, 0x6f, 0x15, 0xc9, 0xfe, 0xff, 0xff, //0x0000016f vmovdqa      $-311(%rip), %ymm2  /* LCPI0_1+0(%rip) */
	0xc5, 0xfd, 0x6f, 0x1d, 0xe1, 0xfe, 0xff, 0xff, //0x00000177 vmovdqa      $-287(%rip), %ymm3  /* LCPI0_2+0(%rip) */
	0xc5, 0xfd, 0x6f, 0x25, 0xf9, 0xfe, 0xff, 0xff, //0x0000017f vmovdqa      $-263(%rip), %ymm4  /* LCPI0_3+0(%rip) */
	0xc5, 0xfd, 0x6f, 0x2d, 0x11, 0xff, 0xff, 0xff, //0x00000187 vmovdqa      $-239(%rip), %ymm5  /* LCPI0_4+0(%rip) */
	0xc5, 0xfd, 0x6f, 0x35, 0x29, 0xff, 0xff, 0xff, //0x0000018f vmovdqa      $-215(%rip), %ymm6  /* LCPI0_5+0(%rip) */
	0xc5, 0xfd, 0x6f, 0x3d, 0x41, 0xff, 0xff, 0xff, //0x00000197 vmovdqa      $-191(%rip), %ymm7  /* LCPI0_6+0(%rip) */
	0x4d, 0x89, 0xc6, //0x0000019f movq         %r8, %r14
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000001a2 .p2align 4, 0x90
	//0x000001b0 LBB0_6
	0xc4, 0x41, 0x7a, 0x6f, 0x04, 0x24, //0x000001b0 vmovdqu      (%r12), %xmm8
	0xc4, 0x43, 0x3d, 0x38, 0x44, 0x24, 0x0c, 0x01, //0x000001b6 vinserti128  $1, $12(%r12), %ymm8, %ymm8
	0xc4, 0x62, 0x3d, 0x00, 0xc1, //0x000001be vpshufb      %ymm1, %ymm8, %ymm8
	0xc5, 0x3d, 0xdb, 0xca, //0x000001c3 vpand        %ymm2, %ymm8, %ymm9
	0xc5, 0x35, 0xe4, 0xcb, //0x000001c7 vpmulhuw     %ymm3, %ymm9, %ymm9
	0xc5, 0x3d, 0xdb, 0xc4, //0x000001cb vpand        %ymm4, %ymm8, %ymm8
	0xc4, 0xc1, 0x2d, 0x71, 0xf0, 0x08, //0x000001cf vpsllw       $8, %ymm8, %ymm10
	0xc4, 0xc1, 0x3d, 0x71, 0xf0, 0x04, //0x000001d5 vpsllw       $4, %ymm8, %ymm8
	0xc4, 0x43, 0x3d, 0x0e, 0xc2, 0xaa, //0x000001db vpblendw     $170, %ymm10, %ymm8, %ymm8
	0xc4, 0x41, 0x3d, 0xeb, 0xc1, //0x000001e1 vpor         %ymm9, %ymm8, %ymm8
	0xc4, 0x41, 0x55, 0x64, 0xc8, //0x000001e6 vpcmpgtb     %ymm8, %ymm5, %ymm9
	0xc5, 0x35, 0xdb, 0xcf, //0x000001eb vpand        %ymm7, %ymm9, %ymm9
	0xc5, 0x3d, 0xd8, 0xd6, //0x000001ef vpsubusb     %ymm6, %ymm8, %ymm10
	0xc4, 0x41, 0x35, 0xeb, 0xca, //0x000001f3 vpor         %ymm10, %ymm9, %ymm9
	0xc4, 0x42, 0x7d, 0x00, 0xc9, //0x000001f8 vpshufb      %ymm9, %ymm0, %ymm9
	0xc4, 0x41, 0x35, 0xfc, 0xc0, //0x000001fd vpaddb       %ymm8, %ymm9, %ymm8
	0xc4, 0x41, 0x7e, 0x7f, 0x06, //0x00000202 vmovdqu      %ymm8, (%r14)
	0x49, 0x83, 0xc6, 0x20, //0x00000207 addq         $32, %r14
	0x49, 0x83, 0xc4, 0x18, //0x0000020b addq         $24, %r12
	0x49, 0x39, 0xf4, //0x0000020f cmpq         %rsi, %r12
	0x0f, 0x86, 0x98, 0xff, 0xff, 0xff, //0x00000212 jbe          LBB0_6
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x00000218 jmp          LBB0_7
	//0x0000021d LBB0_2
	0x4d, 0x89, 0xc6, //0x0000021d movq         %r8, %r14
	//0x00000220 LBB0_7
	0x49, 0x8d, 0x71, 0xe8, //0x00000220 leaq         $-24(%r9), %rsi
	0x49, 0x39, 0xf4, //0x00000224 cmpq         %rsi, %r12
	0x0f, 0x87, 0x93, 0x00, 0x00, 0x00, //0x00000227 ja           LBB0_10
	//0x0000022d LBB0_8
	0x89, 0xd6, //0x0000022d movl         %edx, %esi
	0x83, 0xe6, 0x04, //0x0000022f andl         $4, %esi
	0x0f, 0x84, 0x88, 0x00, 0x00, 0x00, //0x00000232 je           LBB0_10
	0xc4, 0xc1, 0x7a, 0x6f, 0x04, 0x24, //0x00000238 vmovdqu      (%r12), %xmm0
	0xc4, 0xc1, 0x7a, 0x6f, 0x4c, 0x24, 0x08, //0x0000023e vmovdqu      $8(%r12), %xmm1
	0xc5, 0xf1, 0x73, 0xd9, 0x04, //0x00000245 vpsrldq      $4, %xmm1, %xmm1
	0xc4, 0xe3, 0x7d, 0x38, 0xc1, 0x01, //0x0000024a vinserti128  $1, %xmm1, %ymm0, %ymm0
	0xc4, 0xe2, 0x7d, 0x00, 0x05, 0xc7, 0xfd, 0xff, 0xff, //0x00000250 vpshufb      $-569(%rip), %ymm0, %ymm0  /* LCPI0_0+0(%rip) */
	0xc5, 0xfd, 0xdb, 0x0d, 0xdf, 0xfd, 0xff, 0xff, //0x00000259 vpand        $-545(%rip), %ymm0, %ymm1  /* LCPI0_1+0(%rip) */
	0xc5, 0xf5, 0xe4, 0x0d, 0xf7, 0xfd, 0xff, 0xff, //0x00000261 vpmulhuw     $-521(%rip), %ymm1, %ymm1  /* LCPI0_2+0(%rip) */
	0xc5, 0xfd, 0xdb, 0x05, 0x0f, 0xfe, 0xff, 0xff, //0x00000269 vpand        $-497(%rip), %ymm0, %ymm0  /* LCPI0_3+0(%rip) */
	0xc5, 0xed, 0x71, 0xf0, 0x08, //0x00000271 vpsllw       $8, %ymm0, %ymm2
	0xc5, 0xfd, 0x71, 0xf0, 0x04, //0x00000276 vpsllw       $4, %ymm0, %ymm0
	0xc4, 0xe3, 0x7d, 0x0e, 0xc2, 0xaa, //0x0000027b vpblendw     $170, %ymm2, %ymm0, %ymm0
	0xc5, 0xfd, 0xeb, 0xc1, //0x00000281 vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0x6f, 0x0d, 0x13, 0xfe, 0xff, 0xff, //0x00000285 vmovdqa      $-493(%rip), %ymm1  /* LCPI0_4+0(%rip) */
	0xc5, 0xf5, 0x64, 0xc8, //0x0000028d vpcmpgtb     %ymm0, %ymm1, %ymm1
	0xc4, 0xc1, 0x7e, 0x6f, 0x12, //0x00000291 vmovdqu      (%r10), %ymm2
	0xc5, 0xfd, 0xd8, 0x1d, 0x22, 0xfe, 0xff, 0xff, //0x00000296 vpsubusb     $-478(%rip), %ymm0, %ymm3  /* LCPI0_5+0(%rip) */
	0xc5, 0xf5, 0xdb, 0x0d, 0x3a, 0xfe, 0xff, 0xff, //0x0000029e vpand        $-454(%rip), %ymm1, %ymm1  /* LCPI0_6+0(%rip) */
	0xc5, 0xf5, 0xeb, 0xcb, //0x000002a6 vpor         %ymm3, %ymm1, %ymm1
	0xc4, 0xe2, 0x6d, 0x00, 0xc9, //0x000002aa vpshufb      %ymm1, %ymm2, %ymm1
	0xc5, 0xf5, 0xfc, 0xc0, //0x000002af vpaddb       %ymm0, %ymm1, %ymm0
	0xc4, 0xc1, 0x7e, 0x7f, 0x06, //0x000002b3 vmovdqu      %ymm0, (%r14)
	0x49, 0x83, 0xc6, 0x20, //0x000002b8 addq         $32, %r14
	0x49, 0x83, 0xc4, 0x18, //0x000002bc addq         $24, %r12
	//0x000002c0 LBB0_10
	0x4d, 0x39, 0xcc, //0x000002c0 cmpq         %r9, %r12
	0x0f, 0x84, 0x84, 0x01, 0x00, 0x00, //0x000002c3 je           LBB0_25
	0x4d, 0x8d, 0x51, 0xfc, //0x000002c9 leaq         $-4(%r9), %r10
	0x4d, 0x39, 0xd4, //0x000002cd cmpq         %r10, %r12
	0x0f, 0x87, 0x61, 0x00, 0x00, 0x00, //0x000002d0 ja           LBB0_14
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000002d6 .p2align 4, 0x90
	//0x000002e0 LBB0_12
	0x41, 0x8b, 0x34, 0x24, //0x000002e0 movl         (%r12), %esi
	0x0f, 0xce, //0x000002e4 bswapl       %esi
	0x49, 0x89, 0xf7, //0x000002e6 movq         %rsi, %r15
	0x49, 0xc1, 0xef, 0x1a, //0x000002e9 shrq         $26, %r15
	0x89, 0xf1, //0x000002ed movl         %esi, %ecx
	0xc1, 0xe9, 0x14, //0x000002ef shrl         $20, %ecx
	0x83, 0xe1, 0x3f, //0x000002f2 andl         $63, %ecx
	0x89, 0xf3, //0x000002f5 movl         %esi, %ebx
	0xc1, 0xeb, 0x0e, //0x000002f7 shrl         $14, %ebx
	0x83, 0xe3, 0x3f, //0x000002fa andl         $63, %ebx
	0xc1, 0xee, 0x08, //0x000002fd shrl         $8, %esi
	0x83, 0xe6, 0x3f, //0x00000300 andl         $63, %esi
	0x49, 0x83, 0xc4, 0x03, //0x00000303 addq         $3, %r12
	0x43, 0x0f, 0xb6, 0x04, 0x3b, //0x00000307 movzbl       (%r11,%r15), %eax
	0x41, 0x88, 0x06, //0x0000030c movb         %al, (%r14)
	0x41, 0x0f, 0xb6, 0x04, 0x0b, //0x0000030f movzbl       (%r11,%rcx), %eax
	0x41, 0x88, 0x46, 0x01, //0x00000314 movb         %al, $1(%r14)
	0x41, 0x0f, 0xb6, 0x04, 0x1b, //0x00000318 movzbl       (%r11,%rbx), %eax
	0x41, 0x88, 0x46, 0x02, //0x0000031d movb         %al, $2(%r14)
	0x41, 0x0f, 0xb6, 0x04, 0x33, //0x00000321 movzbl       (%r11,%rsi), %eax
	0x41, 0x88, 0x46, 0x03, //0x00000326 movb         %al, $3(%r14)
	0x49, 0x83, 0xc6, 0x04, //0x0000032a addq         $4, %r14
	0x4d, 0x39, 0xd4, //0x0000032e cmpq         %r10, %r12
	0x0f, 0x86, 0xa9, 0xff, 0xff, 0xff, //0x00000331 jbe          LBB0_12
	//0x00000337 LBB0_14
	0x4d, 0x29, 0xe1, //0x00000337 subq         %r12, %r9
	0x45, 0x0f, 0xb6, 0x14, 0x24, //0x0000033a movzbl       (%r12), %r10d
	0x49, 0x83, 0xf9, 0x01, //0x0000033f cmpq         $1, %r9
	0x0f, 0x84, 0xc0, 0x00, 0x00, 0x00, //0x00000343 je           LBB0_21
	0x4c, 0x89, 0xd6, //0x00000349 movq         %r10, %rsi
	0x48, 0xc1, 0xe6, 0x10, //0x0000034c shlq         $16, %rsi
	0x49, 0x83, 0xf9, 0x02, //0x00000350 cmpq         $2, %r9
	0x0f, 0x84, 0x6b, 0x00, 0x00, 0x00, //0x00000354 je           LBB0_18
	0x49, 0x83, 0xf9, 0x03, //0x0000035a cmpq         $3, %r9
	0x0f, 0x85, 0xe9, 0x00, 0x00, 0x00, //0x0000035e jne          LBB0_25
	0x41, 0x0f, 0xb6, 0x54, 0x24, 0x02, //0x00000364 movzbl       $2(%r12), %edx
	0x09, 0xd6, //0x0000036a orl          %edx, %esi
	0x41, 0x0f, 0xb6, 0x44, 0x24, 0x01, //0x0000036c movzbl       $1(%r12), %eax
	0xc1, 0xe0, 0x08, //0x00000372 shll         $8, %eax
	0x09, 0xf0, //0x00000375 orl          %esi, %eax
	0x49, 0xc1, 0xea, 0x02, //0x00000377 shrq         $2, %r10
	0x43, 0x8a, 0x0c, 0x13, //0x0000037b movb         (%r11,%r10), %cl
	0x41, 0x88, 0x0e, //0x0000037f movb         %cl, (%r14)
	0x89, 0xc1, //0x00000382 movl         %eax, %ecx
	0xc1, 0xe9, 0x0c, //0x00000384 shrl         $12, %ecx
	0x83, 0xe1, 0x3f, //0x00000387 andl         $63, %ecx
	0x41, 0x8a, 0x0c, 0x0b, //0x0000038a movb         (%r11,%rcx), %cl
	0x41, 0x88, 0x4e, 0x01, //0x0000038e movb         %cl, $1(%r14)
	0xc1, 0xe8, 0x06, //0x00000392 shrl         $6, %eax
	0x83, 0xe0, 0x3f, //0x00000395 andl         $63, %eax
	0x41, 0x8a, 0x04, 0x03, //0x00000398 movb         (%r11,%rax), %al
	0x41, 0x88, 0x46, 0x02, //0x0000039c movb         %al, $2(%r14)
	0x83, 0xe2, 0x3f, //0x000003a0 andl         $63, %edx
	0x41, 0x8a, 0x04, 0x13, //0x000003a3 movb         (%r11,%rdx), %al
	0x41, 0x88, 0x46, 0x03, //0x000003a7 movb         %al, $3(%r14)
	0xe9, 0x87, 0x00, 0x00, 0x00, //0x000003ab jmp          LBB0_24
	//0x000003b0 LBB0_4
	0x4d, 0x89, 0xc6, //0x000003b0 movq         %r8, %r14
	0x49, 0x8d, 0x71, 0xe8, //0x000003b3 leaq         $-24(%r9), %rsi
	0x49, 0x39, 0xf4, //0x000003b7 cmpq         %rsi, %r12
	0x0f, 0x86, 0x6d, 0xfe, 0xff, 0xff, //0x000003ba jbe          LBB0_8
	0xe9, 0xfb, 0xfe, 0xff, 0xff, //0x000003c0 jmp          LBB0_10
	//0x000003c5 LBB0_18
	0x41, 0x0f, 0xb6, 0x44, 0x24, 0x01, //0x000003c5 movzbl       $1(%r12), %eax
	0x89, 0xc1, //0x000003cb movl         %eax, %ecx
	0xc1, 0xe1, 0x08, //0x000003cd shll         $8, %ecx
	0x09, 0xf1, //0x000003d0 orl          %esi, %ecx
	0x49, 0xc1, 0xea, 0x02, //0x000003d2 shrq         $2, %r10
	0x43, 0x8a, 0x1c, 0x13, //0x000003d6 movb         (%r11,%r10), %bl
	0x41, 0x88, 0x1e, //0x000003da movb         %bl, (%r14)
	0xc1, 0xe9, 0x0c, //0x000003dd shrl         $12, %ecx
	0x83, 0xe1, 0x3f, //0x000003e0 andl         $63, %ecx
	0x41, 0x8a, 0x0c, 0x0b, //0x000003e3 movb         (%r11,%rcx), %cl
	0x41, 0x88, 0x4e, 0x01, //0x000003e7 movb         %cl, $1(%r14)
	0x83, 0xe0, 0x0f, //0x000003eb andl         $15, %eax
	0x41, 0x8a, 0x04, 0x83, //0x000003ee movb         (%r11,%rax,4), %al
	0x41, 0x88, 0x46, 0x02, //0x000003f2 movb         %al, $2(%r14)
	0xf6, 0xc2, 0x02, //0x000003f6 testb        $2, %dl
	0x0f, 0x85, 0x41, 0x00, 0x00, 0x00, //0x000003f9 jne          LBB0_19
	0x41, 0xc6, 0x46, 0x03, 0x3d, //0x000003ff movb         $61, $3(%r14)
	0xe9, 0x2e, 0x00, 0x00, 0x00, //0x00000404 jmp          LBB0_24
	//0x00000409 LBB0_21
	0x4c, 0x89, 0xd0, //0x00000409 movq         %r10, %rax
	0x48, 0xc1, 0xe8, 0x02, //0x0000040c shrq         $2, %rax
	0x41, 0x8a, 0x04, 0x03, //0x00000410 movb         (%r11,%rax), %al
	0x41, 0x88, 0x06, //0x00000414 movb         %al, (%r14)
	0x41, 0xc1, 0xe2, 0x04, //0x00000417 shll         $4, %r10d
	0x41, 0x83, 0xe2, 0x30, //0x0000041b andl         $48, %r10d
	0x43, 0x8a, 0x04, 0x13, //0x0000041f movb         (%r11,%r10), %al
	0x41, 0x88, 0x46, 0x01, //0x00000423 movb         %al, $1(%r14)
	0xf6, 0xc2, 0x02, //0x00000427 testb        $2, %dl
	0x0f, 0x85, 0x19, 0x00, 0x00, 0x00, //0x0000042a jne          LBB0_22
	0x66, 0x41, 0xc7, 0x46, 0x02, 0x3d, 0x3d, //0x00000430 movw         $15677, $2(%r14)
	//0x00000437 LBB0_24
	0x49, 0x83, 0xc6, 0x04, //0x00000437 addq         $4, %r14
	0xe9, 0x0d, 0x00, 0x00, 0x00, //0x0000043b jmp          LBB0_25
	//0x00000440 LBB0_19
	0x49, 0x83, 0xc6, 0x03, //0x00000440 addq         $3, %r14
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x00000444 jmp          LBB0_25
	//0x00000449 LBB0_22
	0x49, 0x83, 0xc6, 0x02, //0x00000449 addq         $2, %r14
	//0x0000044d LBB0_25
	0x4d, 0x29, 0xc6, //0x0000044d subq         %r8, %r14
	0x4c, 0x01, 0x77, 0x08, //0x00000450 addq         %r14, $8(%rdi)
	//0x00000454 LBB0_26
	0x5b, //0x00000454 popq         %rbx
	0x41, 0x5c, //0x00000455 popq         %r12
	0x41, 0x5e, //0x00000457 popq         %r14
	0x41, 0x5f, //0x00000459 popq         %r15
	0x5d, //0x0000045b popq         %rbp
	0xc5, 0xf8, 0x77, //0x0000045c vzeroupper   
	0xc3, //0x0000045f retq         
	//0x00000460 .p2align 5, 0x00
	//0x00000460 LCPI1_0
	0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000460 QUAD $0x0f0f0f0f0f0f0f0f; QUAD $0x0f0f0f0f0f0f0f0f  // .space 16, '\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f'
	0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000470 QUAD $0x0f0f0f0f0f0f0f0f; QUAD $0x0f0f0f0f0f0f0f0f  // .space 16, '\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f'
	//0x00000480 LCPI1_1
	0x01, //0x00000480 .byte 1
	0x02, //0x00000481 .byte 2
	0x04, //0x00000482 .byte 4
	0x08, //0x00000483 .byte 8
	0x10, //0x00000484 .byte 16
	0x20, //0x00000485 .byte 32
	0x40, //0x00000486 .byte 64
	0x80, //0x00000487 .byte 128
	0x00, //0x00000488 .byte 0
	0x00, //0x00000489 .byte 0
	0x00, //0x0000048a .byte 0
	0x00, //0x0000048b .byte 0
	0x00, //0x0000048c .byte 0
	0x00, //0x0000048d .byte 0
	0x00, //0x0000048e .byte 0
	0x00, //0x0000048f .byte 0
	0x01, //0x00000490 .byte 1
	0x02, //0x00000491 .byte 2
	0x04, //0x00000492 .byte 4
	0x08, //0x00000493 .byte 8
	0x10, //0x00000494 .byte 16
	0x20, //0x00000495 .byte 32
	0x40, //0x00000496 .byte 64
	0x80, //0x00000497 .byte 128
	0x00, //0x00000498 .byte 0
	0x00, //0x00000499 .byte 0
	0x00, //0x0000049a .byte 0
	0x00, //0x0000049b .byte 0
	0x00, //0x0000049c .byte 0
	0x00, //0x0000049d .byte 0
	0x00, //0x0000049e .byte 0
	0x00, //0x0000049f .byte 0
	//0x000004a0 LCPI1_2
	0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, //0x000004a0 QUAD $0x3f3f3f3f3f3f3f3f; QUAD $0x3f3f3f3f3f3f3f3f  // .space 16, '????????????????'
	0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, //0x000004b0 QUAD $0x3f3f3f3f3f3f3f3f; QUAD $0x3f3f3f3f3f3f3f3f  // .space 16, '????????????????'
	//0x000004c0 LCPI1_3
	0x40, //0x000004c0 .byte 64
	0x01, //0x000004c1 .byte 1
	0x40, //0x000004c2 .byte 64
	0x01, //0x000004c3 .byte 1
	0x40, //0x000004c4 .byte 64
	0x01, //0x000004c5 .byte 1
	0x40, //0x000004c6 .byte 64
	0x01, //0x000004c7 .byte 1
	0x40, //0x000004c8 .byte 64
	0x01, //0x000004c9 .byte 1
	0x40, //0x000004ca .byte 64
	0x01, //0x000004cb .byte 1
	0x40, //0x000004cc .byte 64
	0x01, //0x000004cd .byte 1
	0x40, //0x000004ce .byte 64
	0x01, //0x000004cf .byte 1
	0x40, //0x000004d0 .byte 64
	0x01, //0x000004d1 .byte 1
	0x40, //0x000004d2 .byte 64
	0x01, //0x000004d3 .byte 1
	0x40, //0x000004d4 .byte 64
	0x01, //0x000004d5 .byte 1
	0x40, //0x000004d6 .byte 64
	0x01, //0x000004d7 .byte 1
	0x40, //0x000004d8 .byte 64
	0x01, //0x000004d9 .byte 1
	0x40, //0x000004da .byte 64
	0x01, //0x000004db .byte 1
	0x40, //0x000004dc .byte 64
	0x01, //0x000004dd .byte 1
	0x40, //0x000004de .byte 64
	0x01, //0x000004df .byte 1
	//0x000004e0 LCPI1_4
	0x00, 0x10, //0x000004e0 .word 4096
	0x01, 0x00, //0x000004e2 .word 1
	0x00, 0x10, //0x000004e4 .word 4096
	0x01, 0x00, //0x000004e6 .word 1
	0x00, 0x10, //0x000004e8 .word 4096
	0x01, 0x00, //0x000004ea .word 1
	0x00, 0x10, //0x000004ec .word 4096
	0x01, 0x00, //0x000004ee .word 1
	0x00, 0x10, //0x000004f0 .word 4096
	0x01, 0x00, //0x000004f2 .word 1
	0x00, 0x10, //0x000004f4 .word 4096
	0x01, 0x00, //0x000004f6 .word 1
	0x00, 0x10, //0x000004f8 .word 4096
	0x01, 0x00, //0x000004fa .word 1
	0x00, 0x10, //0x000004fc .word 4096
	0x01, 0x00, //0x000004fe .word 1
	//0x00000500 LCPI1_6
	0x02, //0x00000500 .byte 2
	0x01, //0x00000501 .byte 1
	0x00, //0x00000502 .byte 0
	0x06, //0x00000503 .byte 6
	0x05, //0x00000504 .byte 5
	0x04, //0x00000505 .byte 4
	0x0a, //0x00000506 .byte 10
	0x09, //0x00000507 .byte 9
	0x08, //0x00000508 .byte 8
	0x0e, //0x00000509 .byte 14
	0x0d, //0x0000050a .byte 13
	0x0c, //0x0000050b .byte 12
	0x00, //0x0000050c BYTE $0x00  // .space 1, '\x00'
	0x00, //0x0000050d BYTE $0x00  // .space 1, '\x00'
	0x00, //0x0000050e BYTE $0x00  // .space 1, '\x00'
	0x00, //0x0000050f BYTE $0x00  // .space 1, '\x00'
	0x05, //0x00000510 .byte 5
	0x04, //0x00000511 .byte 4
	0x0a, //0x00000512 .byte 10
	0x09, //0x00000513 .byte 9
	0x08, //0x00000514 .byte 8
	0x0e, //0x00000515 .byte 14
	0x0d, //0x00000516 .byte 13
	0x0c, //0x00000517 .byte 12
	0x00, //0x00000518 BYTE $0x00  // .space 1, '\x00'
	0x00, //0x00000519 BYTE $0x00  // .space 1, '\x00'
	0x00, //0x0000051a BYTE $0x00  // .space 1, '\x00'
	0x00, //0x0000051b BYTE $0x00  // .space 1, '\x00'
	0x00, //0x0000051c BYTE $0x00  // .space 1, '\x00'
	0x00, //0x0000051d BYTE $0x00  // .space 1, '\x00'
	0x00, //0x0000051e BYTE $0x00  // .space 1, '\x00'
	0x00, //0x0000051f BYTE $0x00  // .space 1, '\x00'
	//0x00000520 .p2align 4, 0x00
	//0x00000520 LCPI1_5
	0x00, //0x00000520 BYTE $0x00  // .space 1, '\x00'
	0x00, //0x00000521 BYTE $0x00  // .space 1, '\x00'
	0x00, //0x00000522 BYTE $0x00  // .space 1, '\x00'
	0x00, //0x00000523 BYTE $0x00  // .space 1, '\x00'
	0x00, //0x00000524 BYTE $0x00  // .space 1, '\x00'
	0x00, //0x00000525 BYTE $0x00  // .space 1, '\x00'
	0x00, //0x00000526 BYTE $0x00  // .space 1, '\x00'
	0x00, //0x00000527 BYTE $0x00  // .space 1, '\x00'
	0x00, //0x00000528 BYTE $0x00  // .space 1, '\x00'
	0x00, //0x00000529 BYTE $0x00  // .space 1, '\x00'
	0x00, //0x0000052a BYTE $0x00  // .space 1, '\x00'
	0x00, //0x0000052b BYTE $0x00  // .space 1, '\x00'
	0x02, //0x0000052c .byte 2
	0x01, //0x0000052d .byte 1
	0x00, //0x0000052e .byte 0
	0x06, //0x0000052f .byte 6
	//0x00000530 .p2align 4, 0x90
	//0x00000530 _b64decode
	0x55, //0x00000530 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000531 movq         %rsp, %rbp
	0x41, 0x57, //0x00000534 pushq        %r15
	0x41, 0x56, //0x00000536 pushq        %r14
	0x41, 0x55, //0x00000538 pushq        %r13
	0x41, 0x54, //0x0000053a pushq        %r12
	0x53, //0x0000053c pushq        %rbx
	0x48, 0x83, 0xec, 0x68, //0x0000053d subq         $104, %rsp
	0x48, 0x85, 0xd2, //0x00000541 testq        %rdx, %rdx
	0x0f, 0x84, 0x3e, 0x11, 0x00, 0x00, //0x00000544 je           LBB1_282
	0x48, 0x8b, 0x1f, //0x0000054a movq         (%rdi), %rbx
	0x48, 0x8b, 0x47, 0x08, //0x0000054d movq         $8(%rdi), %rax
	0x48, 0x01, 0xd8, //0x00000551 addq         %rbx, %rax
	0x48, 0x89, 0xbd, 0x70, 0xff, 0xff, 0xff, //0x00000554 movq         %rdi, $-144(%rbp)
	0x48, 0x03, 0x5f, 0x10, //0x0000055b addq         $16(%rdi), %rbx
	0x48, 0x89, 0x5d, 0x90, //0x0000055f movq         %rbx, $-112(%rbp)
	0x48, 0x89, 0x55, 0x88, //0x00000563 movq         %rdx, $-120(%rbp)
	0x48, 0x8d, 0x1c, 0x16, //0x00000567 leaq         (%rsi,%rdx), %rbx
	0xf6, 0xc1, 0x01, //0x0000056b testb        $1, %cl
	0x48, 0x8d, 0x15, 0xcb, 0x45, 0x00, 0x00, //0x0000056e leaq         $17867(%rip), %rdx  /* _VecDecodeCharsetStd+0(%rip) */
	0x48, 0x8d, 0x3d, 0x44, 0x47, 0x00, 0x00, //0x00000575 leaq         $18244(%rip), %rdi  /* _VecDecodeCharsetURL+0(%rip) */
	0x48, 0x0f, 0x44, 0xfa, //0x0000057c cmoveq       %rdx, %rdi
	0x48, 0x89, 0x7d, 0xc0, //0x00000580 movq         %rdi, $-64(%rbp)
	0x48, 0x8d, 0x15, 0x35, 0x45, 0x00, 0x00, //0x00000584 leaq         $17717(%rip), %rdx  /* _VecDecodeTableStd+0(%rip) */
	0x4c, 0x8d, 0x0d, 0xae, 0x46, 0x00, 0x00, //0x0000058b leaq         $18094(%rip), %r9  /* _VecDecodeTableURL+0(%rip) */
	0x4c, 0x0f, 0x44, 0xca, //0x00000592 cmoveq       %rdx, %r9
	0x48, 0x89, 0x85, 0x78, 0xff, 0xff, 0xff, //0x00000596 movq         %rax, $-136(%rbp)
	0x48, 0x89, 0x45, 0xb0, //0x0000059d movq         %rax, $-80(%rbp)
	0x48, 0x89, 0x75, 0xa8, //0x000005a1 movq         %rsi, $-88(%rbp)
	0x49, 0x89, 0xf7, //0x000005a5 movq         %rsi, %r15
	0x89, 0x4d, 0xbc, //0x000005a8 movl         %ecx, $-68(%rbp)
	0xf6, 0xc1, 0x04, //0x000005ab testb        $4, %cl
	0x48, 0x89, 0x5d, 0xd0, //0x000005ae movq         %rbx, $-48(%rbp)
	0x0f, 0x85, 0xd7, 0x10, 0x00, 0x00, //0x000005b2 jne          LBB1_283
	//0x000005b8 LBB1_2
	0x48, 0x8b, 0x45, 0x90, //0x000005b8 movq         $-112(%rbp), %rax
	0x48, 0x83, 0xc0, 0xf8, //0x000005bc addq         $-8, %rax
	0x48, 0x89, 0x45, 0xc8, //0x000005c0 movq         %rax, $-56(%rbp)
	0x48, 0x3b, 0x45, 0xb0, //0x000005c4 cmpq         $-80(%rbp), %rax
	0x0f, 0x82, 0xfe, 0x22, 0x00, 0x00, //0x000005c8 jb           LBB1_574
	0x48, 0x8d, 0x43, 0xf8, //0x000005ce leaq         $-8(%rbx), %rax
	0x48, 0x89, 0x45, 0xa0, //0x000005d2 movq         %rax, $-96(%rbp)
	0x4c, 0x39, 0xf8, //0x000005d6 cmpq         %r15, %rax
	0x0f, 0x82, 0xed, 0x22, 0x00, 0x00, //0x000005d9 jb           LBB1_574
	0x48, 0x8b, 0x45, 0xa8, //0x000005df movq         $-88(%rbp), %rax
	0x48, 0x8b, 0x4d, 0x88, //0x000005e3 movq         $-120(%rbp), %rcx
	0x48, 0x8d, 0x54, 0x01, 0xfe, //0x000005e7 leaq         $-2(%rcx,%rax), %rdx
	0x48, 0x89, 0x55, 0x80, //0x000005ec movq         %rdx, $-128(%rbp)
	0x48, 0x8d, 0x44, 0x01, 0xfd, //0x000005f0 leaq         $-3(%rcx,%rax), %rax
	0x48, 0x89, 0x45, 0x98, //0x000005f5 movq         %rax, $-104(%rbp)
	0x4d, 0x89, 0xf9, //0x000005f9 movq         %r15, %r9
	0xe9, 0x71, 0x00, 0x00, 0x00, //0x000005fc jmp          LBB1_5
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000601 .p2align 4, 0x90
	//0x00000610 LBB1_6
	0x49, 0xc1, 0xe3, 0x3a, //0x00000610 shlq         $58, %r11
	0x49, 0xc1, 0xe0, 0x34, //0x00000614 shlq         $52, %r8
	0x4d, 0x09, 0xd8, //0x00000618 orq          %r11, %r8
	0x49, 0xc1, 0xe6, 0x2e, //0x0000061b shlq         $46, %r14
	0x48, 0xc1, 0xe1, 0x28, //0x0000061f shlq         $40, %rcx
	0x4c, 0x09, 0xf1, //0x00000623 orq          %r14, %rcx
	0x4c, 0x09, 0xc1, //0x00000626 orq          %r8, %rcx
	0x49, 0xc1, 0xe2, 0x22, //0x00000629 shlq         $34, %r10
	0x48, 0xc1, 0xe6, 0x1c, //0x0000062d shlq         $28, %rsi
	0x4c, 0x09, 0xd6, //0x00000631 orq          %r10, %rsi
	0x48, 0xc1, 0xe3, 0x16, //0x00000634 shlq         $22, %rbx
	0x48, 0x09, 0xf3, //0x00000638 orq          %rsi, %rbx
	0x48, 0x09, 0xcb, //0x0000063b orq          %rcx, %rbx
	0x48, 0xc1, 0xe0, 0x10, //0x0000063e shlq         $16, %rax
	0x48, 0x09, 0xd8, //0x00000642 orq          %rbx, %rax
	0x48, 0x0f, 0xc8, //0x00000645 bswapq       %rax
	0x48, 0x8b, 0x4d, 0xb0, //0x00000648 movq         $-80(%rbp), %rcx
	0x48, 0x89, 0x01, //0x0000064c movq         %rax, (%rcx)
	0x49, 0x83, 0xc1, 0x08, //0x0000064f addq         $8, %r9
	0x48, 0x83, 0xc1, 0x06, //0x00000653 addq         $6, %rcx
	0x48, 0x89, 0x4d, 0xb0, //0x00000657 movq         %rcx, $-80(%rbp)
	0x48, 0x39, 0x4d, 0xc8, //0x0000065b cmpq         %rcx, $-56(%rbp)
	0x0f, 0x82, 0x81, 0x22, 0x00, 0x00, //0x0000065f jb           LBB1_572
	//0x00000665 LBB1_223
	0x4d, 0x89, 0xcf, //0x00000665 movq         %r9, %r15
	0x4c, 0x39, 0x4d, 0xa0, //0x00000668 cmpq         %r9, $-96(%rbp)
	0x0f, 0x82, 0x74, 0x22, 0x00, 0x00, //0x0000066c jb           LBB1_572
	//0x00000672 LBB1_5
	0x41, 0x0f, 0xb6, 0x01, //0x00000672 movzbl       (%r9), %eax
	0x48, 0x8b, 0x55, 0xc0, //0x00000676 movq         $-64(%rbp), %rdx
	0x44, 0x0f, 0xb6, 0x1c, 0x02, //0x0000067a movzbl       (%rdx,%rax), %r11d
	0x41, 0x0f, 0xb6, 0x41, 0x01, //0x0000067f movzbl       $1(%r9), %eax
	0x44, 0x0f, 0xb6, 0x04, 0x02, //0x00000684 movzbl       (%rdx,%rax), %r8d
	0x41, 0x0f, 0xb6, 0x41, 0x02, //0x00000689 movzbl       $2(%r9), %eax
	0x44, 0x0f, 0xb6, 0x34, 0x02, //0x0000068e movzbl       (%rdx,%rax), %r14d
	0x41, 0x0f, 0xb6, 0x41, 0x03, //0x00000693 movzbl       $3(%r9), %eax
	0x0f, 0xb6, 0x0c, 0x02, //0x00000698 movzbl       (%rdx,%rax), %ecx
	0x41, 0x0f, 0xb6, 0x41, 0x04, //0x0000069c movzbl       $4(%r9), %eax
	0x44, 0x0f, 0xb6, 0x14, 0x02, //0x000006a1 movzbl       (%rdx,%rax), %r10d
	0x41, 0x0f, 0xb6, 0x41, 0x05, //0x000006a6 movzbl       $5(%r9), %eax
	0x0f, 0xb6, 0x34, 0x02, //0x000006ab movzbl       (%rdx,%rax), %esi
	0x41, 0x0f, 0xb6, 0x41, 0x06, //0x000006af movzbl       $6(%r9), %eax
	0x0f, 0xb6, 0x1c, 0x02, //0x000006b4 movzbl       (%rdx,%rax), %ebx
	0x41, 0x0f, 0xb6, 0x41, 0x07, //0x000006b8 movzbl       $7(%r9), %eax
	0x0f, 0xb6, 0x04, 0x02, //0x000006bd movzbl       (%rdx,%rax), %eax
	0x45, 0x89, 0xc5, //0x000006c1 movl         %r8d, %r13d
	0x45, 0x09, 0xdd, //0x000006c4 orl          %r11d, %r13d
	0x41, 0x89, 0xcc, //0x000006c7 movl         %ecx, %r12d
	0x45, 0x09, 0xf4, //0x000006ca orl          %r14d, %r12d
	0x45, 0x09, 0xec, //0x000006cd orl          %r13d, %r12d
	0x89, 0xf7, //0x000006d0 movl         %esi, %edi
	0x44, 0x09, 0xd7, //0x000006d2 orl          %r10d, %edi
	0x89, 0xda, //0x000006d5 movl         %ebx, %edx
	0x09, 0xfa, //0x000006d7 orl          %edi, %edx
	0x44, 0x09, 0xe2, //0x000006d9 orl          %r12d, %edx
	0x89, 0xc7, //0x000006dc movl         %eax, %edi
	0x09, 0xd7, //0x000006de orl          %edx, %edi
	0x40, 0x80, 0xff, 0xff, //0x000006e0 cmpb         $-1, %dil
	0x0f, 0x85, 0x26, 0xff, 0xff, 0xff, //0x000006e4 jne          LBB1_6
	0x48, 0x8b, 0x5d, 0xd0, //0x000006ea movq         $-48(%rbp), %rbx
	0x4c, 0x39, 0xcb, //0x000006ee cmpq         %r9, %rbx
	0x44, 0x8b, 0x55, 0xbc, //0x000006f1 movl         $-68(%rbp), %r10d
	0x0f, 0x86, 0x15, 0x02, 0x00, 0x00, //0x000006f5 jbe          LBB1_39
	0x41, 0xf6, 0xc2, 0x08, //0x000006fb testb        $8, %r10b
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x000006ff je           LBB1_11
	0x4c, 0x89, 0xca, //0x00000705 movq         %r9, %rdx
	0xe9, 0xbf, 0x00, 0x00, 0x00, //0x00000708 jmp          LBB1_22
	0x90, 0x90, 0x90, //0x0000070d .p2align 4, 0x90
	//0x00000710 LBB1_10
	0x49, 0xff, 0xc1, //0x00000710 incq         %r9
	0x49, 0x39, 0xd9, //0x00000713 cmpq         %rbx, %r9
	0x0f, 0x83, 0xde, 0x01, 0x00, 0x00, //0x00000716 jae          LBB1_37
	//0x0000071c LBB1_11
	0x41, 0x0f, 0xb6, 0x09, //0x0000071c movzbl       (%r9), %ecx
	0x48, 0x83, 0xf9, 0x0d, //0x00000720 cmpq         $13, %rcx
	0x0f, 0x84, 0xe6, 0xff, 0xff, 0xff, //0x00000724 je           LBB1_10
	0x80, 0xf9, 0x0a, //0x0000072a cmpb         $10, %cl
	0x0f, 0x84, 0xdd, 0xff, 0xff, 0xff, //0x0000072d je           LBB1_10
	0x48, 0x8b, 0x45, 0xc0, //0x00000733 movq         $-64(%rbp), %rax
	0x0f, 0xb6, 0x14, 0x08, //0x00000737 movzbl       (%rax,%rcx), %edx
	0x49, 0xff, 0xc1, //0x0000073b incq         %r9
	0x81, 0xfa, 0xff, 0x00, 0x00, 0x00, //0x0000073e cmpl         $255, %edx
	0x0f, 0x84, 0x81, 0x02, 0x00, 0x00, //0x00000744 je           LBB1_51
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x0000074a movl         $1, %r11d
	0x49, 0x39, 0xd9, //0x00000750 cmpq         %rbx, %r9
	0x0f, 0x82, 0x13, 0x00, 0x00, 0x00, //0x00000753 jb           LBB1_16
	0xe9, 0x8b, 0x0b, 0x00, 0x00, //0x00000759 jmp          LBB1_209
	0x90, 0x90, //0x0000075e .p2align 4, 0x90
	//0x00000760 LBB1_15
	0x49, 0xff, 0xc1, //0x00000760 incq         %r9
	0x49, 0x39, 0xd9, //0x00000763 cmpq         %rbx, %r9
	0x0f, 0x83, 0x3d, 0x04, 0x00, 0x00, //0x00000766 jae          LBB1_82
	//0x0000076c LBB1_16
	0x41, 0x0f, 0xb6, 0x09, //0x0000076c movzbl       (%r9), %ecx
	0x48, 0x83, 0xf9, 0x0d, //0x00000770 cmpq         $13, %rcx
	0x0f, 0x84, 0xe6, 0xff, 0xff, 0xff, //0x00000774 je           LBB1_15
	0x80, 0xf9, 0x0a, //0x0000077a cmpb         $10, %cl
	0x0f, 0x84, 0xdd, 0xff, 0xff, 0xff, //0x0000077d je           LBB1_15
	0x48, 0x8b, 0x45, 0xc0, //0x00000783 movq         $-64(%rbp), %rax
	0x0f, 0xb6, 0x04, 0x08, //0x00000787 movzbl       (%rax,%rcx), %eax
	0x49, 0xff, 0xc1, //0x0000078b incq         %r9
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x0000078e cmpl         $255, %eax
	0x0f, 0x84, 0x30, 0x06, 0x00, 0x00, //0x00000793 je           LBB1_115
	0xc1, 0xe2, 0x06, //0x00000799 shll         $6, %edx
	0x09, 0xc2, //0x0000079c orl          %eax, %edx
	0x41, 0xbb, 0x02, 0x00, 0x00, 0x00, //0x0000079e movl         $2, %r11d
	0x49, 0x39, 0xd9, //0x000007a4 cmpq         %rbx, %r9
	0x0f, 0x82, 0x95, 0x01, 0x00, 0x00, //0x000007a7 jb           LBB1_41
	0xe9, 0x37, 0x0b, 0x00, 0x00, //0x000007ad jmp          LBB1_209
	//0x000007b2 LBB1_20
	0x3c, 0x6e, //0x000007b2 cmpb         $110, %al
	0x0f, 0x85, 0xd6, 0x01, 0x00, 0x00, //0x000007b4 jne          LBB1_46
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000007ba .p2align 4, 0x90
	//0x000007c0 LBB1_21
	0x4c, 0x89, 0xca, //0x000007c0 movq         %r9, %rdx
	0x49, 0x39, 0xd9, //0x000007c3 cmpq         %rbx, %r9
	0x0f, 0x83, 0x2e, 0x01, 0x00, 0x00, //0x000007c6 jae          LBB1_37
	//0x000007cc LBB1_22
	0x48, 0x8d, 0x42, 0x01, //0x000007cc leaq         $1(%rdx), %rax
	0x0f, 0xb6, 0x0a, //0x000007d0 movzbl       (%rdx), %ecx
	0x80, 0xf9, 0x5c, //0x000007d3 cmpb         $92, %cl
	0x0f, 0x85, 0x04, 0x01, 0x00, 0x00, //0x000007d6 jne          LBB1_35
	0x4c, 0x8d, 0x4a, 0x02, //0x000007dc leaq         $2(%rdx), %r9
	0xb1, 0xff, //0x000007e0 movb         $-1, %cl
	0x49, 0x39, 0xd9, //0x000007e2 cmpq         %rbx, %r9
	0x0f, 0x87, 0x9d, 0x01, 0x00, 0x00, //0x000007e5 ja           LBB1_45
	0x0f, 0xb6, 0x00, //0x000007eb movzbl       (%rax), %eax
	0x3c, 0x71, //0x000007ee cmpb         $113, %al
	0x0f, 0x8e, 0xbc, 0xff, 0xff, 0xff, //0x000007f0 jle          LBB1_20
	0x3c, 0x72, //0x000007f6 cmpb         $114, %al
	0x0f, 0x84, 0xc2, 0xff, 0xff, 0xff, //0x000007f8 je           LBB1_21
	0x3c, 0x75, //0x000007fe cmpb         $117, %al
	0x0f, 0x85, 0x94, 0x01, 0x00, 0x00, //0x00000800 jne          LBB1_48
	0x48, 0x89, 0xd8, //0x00000806 movq         %rbx, %rax
	0x4c, 0x29, 0xc8, //0x00000809 subq         %r9, %rax
	0x48, 0x83, 0xf8, 0x04, //0x0000080c cmpq         $4, %rax
	0x0f, 0x8c, 0x84, 0x01, 0x00, 0x00, //0x00000810 jl           LBB1_48
	0x41, 0x8b, 0x01, //0x00000816 movl         (%r9), %eax
	0x89, 0xc6, //0x00000819 movl         %eax, %esi
	0xf7, 0xd6, //0x0000081b notl         %esi
	0x8d, 0xb8, 0xd0, 0xcf, 0xcf, 0xcf, //0x0000081d leal         $-808464432(%rax), %edi
	0x81, 0xe6, 0x80, 0x80, 0x80, 0x80, //0x00000823 andl         $-2139062144, %esi
	0x85, 0xfe, //0x00000829 testl        %edi, %esi
	0x0f, 0x85, 0x69, 0x01, 0x00, 0x00, //0x0000082b jne          LBB1_48
	0x8d, 0xb8, 0x19, 0x19, 0x19, 0x19, //0x00000831 leal         $421075225(%rax), %edi
	0x09, 0xc7, //0x00000837 orl          %eax, %edi
	0xf7, 0xc7, 0x80, 0x80, 0x80, 0x80, //0x00000839 testl        $-2139062144, %edi
	0x0f, 0x85, 0x55, 0x01, 0x00, 0x00, //0x0000083f jne          LBB1_48
	0x89, 0xc7, //0x00000845 movl         %eax, %edi
	0x81, 0xe7, 0x7f, 0x7f, 0x7f, 0x7f, //0x00000847 andl         $2139062143, %edi
	0xbb, 0xc0, 0xc0, 0xc0, 0xc0, //0x0000084d movl         $-1061109568, %ebx
	0x29, 0xfb, //0x00000852 subl         %edi, %ebx
	0x44, 0x8d, 0x87, 0x46, 0x46, 0x46, 0x46, //0x00000854 leal         $1179010630(%rdi), %r8d
	0x21, 0xf3, //0x0000085b andl         %esi, %ebx
	0x44, 0x85, 0xc3, //0x0000085d testl        %r8d, %ebx
	0x48, 0x8b, 0x5d, 0xd0, //0x00000860 movq         $-48(%rbp), %rbx
	0x0f, 0x85, 0x30, 0x01, 0x00, 0x00, //0x00000864 jne          LBB1_48
	0xbb, 0xe0, 0xe0, 0xe0, 0xe0, //0x0000086a movl         $-522133280, %ebx
	0x29, 0xfb, //0x0000086f subl         %edi, %ebx
	0x81, 0xc7, 0x39, 0x39, 0x39, 0x39, //0x00000871 addl         $960051513, %edi
	0x21, 0xde, //0x00000877 andl         %ebx, %esi
	0x48, 0x8b, 0x5d, 0xd0, //0x00000879 movq         $-48(%rbp), %rbx
	0x85, 0xfe, //0x0000087d testl        %edi, %esi
	0x0f, 0x85, 0x15, 0x01, 0x00, 0x00, //0x0000087f jne          LBB1_48
	0x0f, 0xc8, //0x00000885 bswapl       %eax
	0x89, 0xc1, //0x00000887 movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x00000889 shrl         $4, %ecx
	0xf7, 0xd1, //0x0000088c notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x0000088e andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x00000894 leal         (%rcx,%rcx,8), %ecx
	0x25, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000897 andl         $252645135, %eax
	0x01, 0xc8, //0x0000089c addl         %ecx, %eax
	0x89, 0xc1, //0x0000089e movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x000008a0 shrl         $4, %ecx
	0x09, 0xc1, //0x000008a3 orl          %eax, %ecx
	0x89, 0xc8, //0x000008a5 movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x000008a7 shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x000008aa andl         $65280, %eax
	0x89, 0xce, //0x000008af movl         %ecx, %esi
	0x81, 0xe6, 0x80, 0x00, 0x00, 0x00, //0x000008b1 andl         $128, %esi
	0x09, 0xc6, //0x000008b7 orl          %eax, %esi
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x000008b9 je           LBB1_34
	0xb9, 0xff, 0x00, 0x00, 0x00, //0x000008bf movl         $255, %ecx
	//0x000008c4 LBB1_34
	0x48, 0x83, 0xc2, 0x06, //0x000008c4 addq         $6, %rdx
	0x49, 0x89, 0xd1, //0x000008c8 movq         %rdx, %r9
	0x80, 0xf9, 0x0d, //0x000008cb cmpb         $13, %cl
	0x0f, 0x85, 0x18, 0x00, 0x00, 0x00, //0x000008ce jne          LBB1_36
	0xe9, 0xe7, 0xfe, 0xff, 0xff, //0x000008d4 jmp          LBB1_21
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000008d9 .p2align 4, 0x90
	//0x000008e0 LBB1_35
	0x49, 0x89, 0xc1, //0x000008e0 movq         %rax, %r9
	0x80, 0xf9, 0x0d, //0x000008e3 cmpb         $13, %cl
	0x0f, 0x84, 0xd4, 0xfe, 0xff, 0xff, //0x000008e6 je           LBB1_21
	//0x000008ec LBB1_36
	0x80, 0xf9, 0x0a, //0x000008ec cmpb         $10, %cl
	0x0f, 0x84, 0xcb, 0xfe, 0xff, 0xff, //0x000008ef je           LBB1_21
	0xe9, 0xa0, 0x00, 0x00, 0x00, //0x000008f5 jmp          LBB1_48
	//0x000008fa LBB1_37
	0x31, 0xd2, //0x000008fa xorl         %edx, %edx
	0x45, 0x31, 0xdb, //0x000008fc xorl         %r11d, %r11d
	//0x000008ff LBB1_38
	0x45, 0x85, 0xdb, //0x000008ff testl        %r11d, %r11d
	0x0f, 0x85, 0xe1, 0x09, 0x00, 0x00, //0x00000902 jne          LBB1_209
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000908 .p2align 4, 0x90
	//0x00000910 LBB1_39
	0x48, 0x8b, 0x4d, 0xb0, //0x00000910 movq         $-80(%rbp), %rcx
	0x48, 0x89, 0x4d, 0xb0, //0x00000914 movq         %rcx, $-80(%rbp)
	0x48, 0x39, 0x4d, 0xc8, //0x00000918 cmpq         %rcx, $-56(%rbp)
	0x0f, 0x83, 0x43, 0xfd, 0xff, 0xff, //0x0000091c jae          LBB1_223
	0xe9, 0xbf, 0x1f, 0x00, 0x00, //0x00000922 jmp          LBB1_572
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000927 .p2align 4, 0x90
	//0x00000930 LBB1_40
	0x49, 0xff, 0xc1, //0x00000930 incq         %r9
	0x41, 0xbb, 0x02, 0x00, 0x00, 0x00, //0x00000933 movl         $2, %r11d
	0x49, 0x39, 0xd9, //0x00000939 cmpq         %rbx, %r9
	0x0f, 0x83, 0xbd, 0xff, 0xff, 0xff, //0x0000093c jae          LBB1_38
	//0x00000942 LBB1_41
	0x41, 0x0f, 0xb6, 0x09, //0x00000942 movzbl       (%r9), %ecx
	0x48, 0x83, 0xf9, 0x0d, //0x00000946 cmpq         $13, %rcx
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x0000094a je           LBB1_40
	0x80, 0xf9, 0x0a, //0x00000950 cmpb         $10, %cl
	0x0f, 0x84, 0xd7, 0xff, 0xff, 0xff, //0x00000953 je           LBB1_40
	0x48, 0x8b, 0x45, 0xc0, //0x00000959 movq         $-64(%rbp), %rax
	0x0f, 0xb6, 0x04, 0x08, //0x0000095d movzbl       (%rax,%rcx), %eax
	0x49, 0xff, 0xc1, //0x00000961 incq         %r9
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00000964 cmpl         $255, %eax
	0x0f, 0x84, 0xea, 0x08, 0x00, 0x00, //0x00000969 je           LBB1_176
	0xc1, 0xe2, 0x06, //0x0000096f shll         $6, %edx
	0x09, 0xc2, //0x00000972 orl          %eax, %edx
	0x41, 0xbb, 0x03, 0x00, 0x00, 0x00, //0x00000974 movl         $3, %r11d
	0x49, 0x39, 0xd9, //0x0000097a cmpq         %rbx, %r9
	0x0f, 0x82, 0xba, 0x03, 0x00, 0x00, //0x0000097d jb           LBB1_105
	0xe9, 0x61, 0x09, 0x00, 0x00, //0x00000983 jmp          LBB1_209
	//0x00000988 LBB1_45
	0x49, 0x89, 0xc1, //0x00000988 movq         %rax, %r9
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x0000098b jmp          LBB1_48
	//0x00000990 LBB1_46
	0x3c, 0x2f, //0x00000990 cmpb         $47, %al
	0x0f, 0x85, 0x02, 0x00, 0x00, 0x00, //0x00000992 jne          LBB1_48
	0x89, 0xc1, //0x00000998 movl         %eax, %ecx
	//0x0000099a LBB1_48
	0x0f, 0xb6, 0xc1, //0x0000099a movzbl       %cl, %eax
	0x48, 0x8b, 0x55, 0xc0, //0x0000099d movq         $-64(%rbp), %rdx
	0x0f, 0xb6, 0x14, 0x02, //0x000009a1 movzbl       (%rdx,%rax), %edx
	0x81, 0xfa, 0xff, 0x00, 0x00, 0x00, //0x000009a5 cmpl         $255, %edx
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x000009ab je           LBB1_51
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x000009b1 movl         $1, %r11d
	0x49, 0x39, 0xd9, //0x000009b7 cmpq         %rbx, %r9
	0x0f, 0x83, 0x29, 0x09, 0x00, 0x00, //0x000009ba jae          LBB1_209
	0x41, 0x89, 0xd4, //0x000009c0 movl         %edx, %r12d
	0x4c, 0x89, 0xca, //0x000009c3 movq         %r9, %rdx
	0xe9, 0xcd, 0x00, 0x00, 0x00, //0x000009c6 jmp          LBB1_67
	//0x000009cb LBB1_51
	0x45, 0x31, 0xe4, //0x000009cb xorl         %r12d, %r12d
	0x45, 0x31, 0xdb, //0x000009ce xorl         %r11d, %r11d
	//0x000009d1 LBB1_52
	0x41, 0xf6, 0xc2, 0x02, //0x000009d1 testb        $2, %r10b
	0x0f, 0x85, 0x3a, 0x09, 0x00, 0x00, //0x000009d5 jne          LBB1_212
	0x41, 0x83, 0xfb, 0x02, //0x000009db cmpl         $2, %r11d
	0x0f, 0x82, 0x30, 0x09, 0x00, 0x00, //0x000009df jb           LBB1_212
	0x80, 0xf9, 0x3d, //0x000009e5 cmpb         $61, %cl
	0x0f, 0x85, 0x27, 0x09, 0x00, 0x00, //0x000009e8 jne          LBB1_212
	0x41, 0xbe, 0x05, 0x00, 0x00, 0x00, //0x000009ee movl         $5, %r14d
	0x45, 0x29, 0xde, //0x000009f4 subl         %r11d, %r14d
	0x41, 0xf6, 0xc2, 0x08, //0x000009f7 testb        $8, %r10b
	0x0f, 0x85, 0xb3, 0x01, 0x00, 0x00, //0x000009fb jne          LBB1_83
	0x4c, 0x39, 0x4d, 0xd0, //0x00000a01 cmpq         %r9, $-48(%rbp)
	0x0f, 0x86, 0x6f, 0x09, 0x00, 0x00, //0x00000a05 jbe          LBB1_216
	0x49, 0x8d, 0x49, 0x03, //0x00000a0b leaq         $3(%r9), %rcx
	0x48, 0x8b, 0x5d, 0x80, //0x00000a0f movq         $-128(%rbp), %rbx
	0x4c, 0x29, 0xcb, //0x00000a13 subq         %r9, %rbx
	0x49, 0x8d, 0x51, 0x04, //0x00000a16 leaq         $4(%r9), %rdx
	0x48, 0x8b, 0x7d, 0x98, //0x00000a1a movq         $-104(%rbp), %rdi
	0x4c, 0x29, 0xcf, //0x00000a1e subq         %r9, %rdi
	0xe9, 0x23, 0x00, 0x00, 0x00, //0x00000a21 jmp          LBB1_59
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000a26 .p2align 4, 0x90
	//0x00000a30 LBB1_58
	0x49, 0xff, 0xc1, //0x00000a30 incq         %r9
	0x48, 0xff, 0xc1, //0x00000a33 incq         %rcx
	0x48, 0xff, 0xcb, //0x00000a36 decq         %rbx
	0x48, 0xff, 0xc2, //0x00000a39 incq         %rdx
	0x48, 0xff, 0xcf, //0x00000a3c decq         %rdi
	0x4c, 0x39, 0x4d, 0xd0, //0x00000a3f cmpq         %r9, $-48(%rbp)
	0x0f, 0x84, 0x2d, 0x09, 0x00, 0x00, //0x00000a43 je           LBB1_215
	//0x00000a49 LBB1_59
	0x41, 0x0f, 0xb6, 0x01, //0x00000a49 movzbl       (%r9), %eax
	0x3c, 0x0a, //0x00000a4d cmpb         $10, %al
	0x0f, 0x84, 0xdb, 0xff, 0xff, 0xff, //0x00000a4f je           LBB1_58
	0x3c, 0x0d, //0x00000a55 cmpb         $13, %al
	0x0f, 0x84, 0xd3, 0xff, 0xff, 0xff, //0x00000a57 je           LBB1_58
	0x3c, 0x3d, //0x00000a5d cmpb         $61, %al
	0x0f, 0x85, 0x04, 0x05, 0x00, 0x00, //0x00000a5f jne          LBB1_144
	0x49, 0xff, 0xc1, //0x00000a65 incq         %r9
	0x41, 0x83, 0xfe, 0x02, //0x00000a68 cmpl         $2, %r14d
	0x0f, 0x84, 0xa3, 0x08, 0x00, 0x00, //0x00000a6c je           LBB1_212
	0x4c, 0x39, 0x4d, 0xd0, //0x00000a72 cmpq         %r9, $-48(%rbp)
	0x0f, 0x87, 0x15, 0x03, 0x00, 0x00, //0x00000a76 ja           LBB1_110
	0xe9, 0xf9, 0x08, 0x00, 0x00, //0x00000a7c jmp          LBB1_216
	//0x00000a81 LBB1_64
	0x3c, 0x6e, //0x00000a81 cmpb         $110, %al
	0x0f, 0x85, 0x4e, 0x03, 0x00, 0x00, //0x00000a83 jne          LBB1_116
	//0x00000a89 LBB1_65
	0x4d, 0x89, 0xc1, //0x00000a89 movq         %r8, %r9
	//0x00000a8c LBB1_66
	0x4c, 0x89, 0xca, //0x00000a8c movq         %r9, %rdx
	0x49, 0x39, 0xd9, //0x00000a8f cmpq         %rbx, %r9
	0x0f, 0x83, 0x85, 0x02, 0x00, 0x00, //0x00000a92 jae          LBB1_103
	//0x00000a98 LBB1_67
	0x4c, 0x8d, 0x4a, 0x01, //0x00000a98 leaq         $1(%rdx), %r9
	0x0f, 0xb6, 0x0a, //0x00000a9c movzbl       (%rdx), %ecx
	0x80, 0xf9, 0x5c, //0x00000a9f cmpb         $92, %cl
	0x0f, 0x85, 0xea, 0x00, 0x00, 0x00, //0x00000aa2 jne          LBB1_80
	0x4c, 0x8d, 0x42, 0x02, //0x00000aa8 leaq         $2(%rdx), %r8
	0xb1, 0xff, //0x00000aac movb         $-1, %cl
	0x49, 0x39, 0xd8, //0x00000aae cmpq         %rbx, %r8
	0x0f, 0x87, 0x31, 0x03, 0x00, 0x00, //0x00000ab1 ja           LBB1_119
	0x41, 0x0f, 0xb6, 0x01, //0x00000ab7 movzbl       (%r9), %eax
	0x3c, 0x71, //0x00000abb cmpb         $113, %al
	0x0f, 0x8e, 0xbe, 0xff, 0xff, 0xff, //0x00000abd jle          LBB1_64
	0x3c, 0x72, //0x00000ac3 cmpb         $114, %al
	0x0f, 0x84, 0xbe, 0xff, 0xff, 0xff, //0x00000ac5 je           LBB1_65
	0x3c, 0x75, //0x00000acb cmpb         $117, %al
	0x0f, 0x85, 0x0e, 0x03, 0x00, 0x00, //0x00000acd jne          LBB1_118
	0x48, 0x8b, 0x45, 0xd0, //0x00000ad3 movq         $-48(%rbp), %rax
	0x4c, 0x29, 0xc0, //0x00000ad7 subq         %r8, %rax
	0x48, 0x83, 0xf8, 0x04, //0x00000ada cmpq         $4, %rax
	0x0f, 0x8c, 0xfd, 0x02, 0x00, 0x00, //0x00000ade jl           LBB1_118
	0x41, 0x8b, 0x18, //0x00000ae4 movl         (%r8), %ebx
	0x89, 0xde, //0x00000ae7 movl         %ebx, %esi
	0xf7, 0xd6, //0x00000ae9 notl         %esi
	0x8d, 0x83, 0xd0, 0xcf, 0xcf, 0xcf, //0x00000aeb leal         $-808464432(%rbx), %eax
	0x81, 0xe6, 0x80, 0x80, 0x80, 0x80, //0x00000af1 andl         $-2139062144, %esi
	0x85, 0xc6, //0x00000af7 testl        %eax, %esi
	0x0f, 0x85, 0xe2, 0x02, 0x00, 0x00, //0x00000af9 jne          LBB1_118
	0x8d, 0x83, 0x19, 0x19, 0x19, 0x19, //0x00000aff leal         $421075225(%rbx), %eax
	0x09, 0xd8, //0x00000b05 orl          %ebx, %eax
	0xa9, 0x80, 0x80, 0x80, 0x80, //0x00000b07 testl        $-2139062144, %eax
	0x0f, 0x85, 0xcf, 0x02, 0x00, 0x00, //0x00000b0c jne          LBB1_118
	0x89, 0xd8, //0x00000b12 movl         %ebx, %eax
	0x25, 0x7f, 0x7f, 0x7f, 0x7f, //0x00000b14 andl         $2139062143, %eax
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x00000b19 movl         $-1061109568, %edi
	0x29, 0xc7, //0x00000b1e subl         %eax, %edi
	0x44, 0x8d, 0x88, 0x46, 0x46, 0x46, 0x46, //0x00000b20 leal         $1179010630(%rax), %r9d
	0x21, 0xf7, //0x00000b27 andl         %esi, %edi
	0x44, 0x85, 0xcf, //0x00000b29 testl        %r9d, %edi
	0x0f, 0x85, 0xaf, 0x02, 0x00, 0x00, //0x00000b2c jne          LBB1_118
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x00000b32 movl         $-522133280, %edi
	0x29, 0xc7, //0x00000b37 subl         %eax, %edi
	0x05, 0x39, 0x39, 0x39, 0x39, //0x00000b39 addl         $960051513, %eax
	0x21, 0xfe, //0x00000b3e andl         %edi, %esi
	0x85, 0xc6, //0x00000b40 testl        %eax, %esi
	0x0f, 0x85, 0x99, 0x02, 0x00, 0x00, //0x00000b42 jne          LBB1_118
	0x0f, 0xcb, //0x00000b48 bswapl       %ebx
	0x89, 0xd8, //0x00000b4a movl         %ebx, %eax
	0xc1, 0xe8, 0x04, //0x00000b4c shrl         $4, %eax
	0xf7, 0xd0, //0x00000b4f notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00000b51 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00000b56 leal         (%rax,%rax,8), %eax
	0x81, 0xe3, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000b59 andl         $252645135, %ebx
	0x01, 0xc3, //0x00000b5f addl         %eax, %ebx
	0x89, 0xd9, //0x00000b61 movl         %ebx, %ecx
	0xc1, 0xe9, 0x04, //0x00000b63 shrl         $4, %ecx
	0x09, 0xd9, //0x00000b66 orl          %ebx, %ecx
	0x89, 0xc8, //0x00000b68 movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x00000b6a shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x00000b6d andl         $65280, %eax
	0x89, 0xce, //0x00000b72 movl         %ecx, %esi
	0x81, 0xe6, 0x80, 0x00, 0x00, 0x00, //0x00000b74 andl         $128, %esi
	0x09, 0xc6, //0x00000b7a orl          %eax, %esi
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x00000b7c je           LBB1_79
	0xb9, 0xff, 0x00, 0x00, 0x00, //0x00000b82 movl         $255, %ecx
	//0x00000b87 LBB1_79
	0x48, 0x83, 0xc2, 0x06, //0x00000b87 addq         $6, %rdx
	0x49, 0x89, 0xd1, //0x00000b8b movq         %rdx, %r9
	0x48, 0x8b, 0x5d, 0xd0, //0x00000b8e movq         $-48(%rbp), %rbx
	//0x00000b92 LBB1_80
	0x80, 0xf9, 0x0a, //0x00000b92 cmpb         $10, %cl
	0x0f, 0x84, 0xf1, 0xfe, 0xff, 0xff, //0x00000b95 je           LBB1_66
	0x80, 0xf9, 0x0d, //0x00000b9b cmpb         $13, %cl
	0x0f, 0x84, 0xe8, 0xfe, 0xff, 0xff, //0x00000b9e je           LBB1_66
	0xe9, 0x3f, 0x02, 0x00, 0x00, //0x00000ba4 jmp          LBB1_119
	//0x00000ba9 LBB1_82
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00000ba9 movl         $1, %r11d
	0xe9, 0x4b, 0xfd, 0xff, 0xff, //0x00000baf jmp          LBB1_38
	//0x00000bb4 LBB1_83
	0x48, 0x8b, 0x55, 0xd0, //0x00000bb4 movq         $-48(%rbp), %rdx
	0x4c, 0x39, 0xca, //0x00000bb8 cmpq         %r9, %rdx
	0x0f, 0x87, 0x28, 0x00, 0x00, 0x00, //0x00000bbb ja           LBB1_86
	0xe9, 0xb4, 0x07, 0x00, 0x00, //0x00000bc1 jmp          LBB1_216
	//0x00000bc6 LBB1_102
	0x48, 0x89, 0xc1, //0x00000bc6 movq         %rax, %rcx
	0x49, 0x89, 0xc9, //0x00000bc9 movq         %rcx, %r9
	0x48, 0x39, 0xd1, //0x00000bcc cmpq         %rdx, %rcx
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x00000bcf jb           LBB1_86
	0xe9, 0x3b, 0x01, 0x00, 0x00, //0x00000bd5 jmp          LBB1_143
	//0x00000bda LBB1_84
	0x48, 0x89, 0xc8, //0x00000bda movq         %rcx, %rax
	0x49, 0x89, 0xc9, //0x00000bdd movq         %rcx, %r9
	0x48, 0x39, 0xd1, //0x00000be0 cmpq         %rdx, %rcx
	0x0f, 0x83, 0x2c, 0x01, 0x00, 0x00, //0x00000be3 jae          LBB1_143
	//0x00000be9 LBB1_86
	0x49, 0x8d, 0x41, 0x01, //0x00000be9 leaq         $1(%r9), %rax
	0x41, 0x0f, 0xb6, 0x09, //0x00000bed movzbl       (%r9), %ecx
	0x80, 0xf9, 0x5c, //0x00000bf1 cmpb         $92, %cl
	0x0f, 0x85, 0xe6, 0x00, 0x00, 0x00, //0x00000bf4 jne          LBB1_99
	0x49, 0x8d, 0x49, 0x02, //0x00000bfa leaq         $2(%r9), %rcx
	0x48, 0x39, 0xd1, //0x00000bfe cmpq         %rdx, %rcx
	0x0f, 0x87, 0x79, 0x0a, 0x00, 0x00, //0x00000c01 ja           LBB1_258
	0x0f, 0xb6, 0x00, //0x00000c07 movzbl       (%rax), %eax
	0x3c, 0x6e, //0x00000c0a cmpb         $110, %al
	0x0f, 0x84, 0xc8, 0xff, 0xff, 0xff, //0x00000c0c je           LBB1_84
	0x3c, 0x72, //0x00000c12 cmpb         $114, %al
	0x0f, 0x84, 0xc0, 0xff, 0xff, 0xff, //0x00000c14 je           LBB1_84
	0x3c, 0x75, //0x00000c1a cmpb         $117, %al
	0x0f, 0x85, 0x53, 0x0a, 0x00, 0x00, //0x00000c1c jne          LBB1_278
	0x48, 0x89, 0xd0, //0x00000c22 movq         %rdx, %rax
	0x48, 0x29, 0xc8, //0x00000c25 subq         %rcx, %rax
	0x48, 0x83, 0xf8, 0x04, //0x00000c28 cmpq         $4, %rax
	0x0f, 0x8c, 0x43, 0x0a, 0x00, 0x00, //0x00000c2c jl           LBB1_278
	0x8b, 0x01, //0x00000c32 movl         (%rcx), %eax
	0x89, 0xc2, //0x00000c34 movl         %eax, %edx
	0xf7, 0xd2, //0x00000c36 notl         %edx
	0x8d, 0xb0, 0xd0, 0xcf, 0xcf, 0xcf, //0x00000c38 leal         $-808464432(%rax), %esi
	0x81, 0xe2, 0x80, 0x80, 0x80, 0x80, //0x00000c3e andl         $-2139062144, %edx
	0x85, 0xf2, //0x00000c44 testl        %esi, %edx
	0x0f, 0x85, 0x29, 0x0a, 0x00, 0x00, //0x00000c46 jne          LBB1_278
	0x8d, 0xb0, 0x19, 0x19, 0x19, 0x19, //0x00000c4c leal         $421075225(%rax), %esi
	0x09, 0xc6, //0x00000c52 orl          %eax, %esi
	0xf7, 0xc6, 0x80, 0x80, 0x80, 0x80, //0x00000c54 testl        $-2139062144, %esi
	0x0f, 0x85, 0x15, 0x0a, 0x00, 0x00, //0x00000c5a jne          LBB1_278
	0x89, 0xc6, //0x00000c60 movl         %eax, %esi
	0x81, 0xe6, 0x7f, 0x7f, 0x7f, 0x7f, //0x00000c62 andl         $2139062143, %esi
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x00000c68 movl         $-1061109568, %edi
	0x29, 0xf7, //0x00000c6d subl         %esi, %edi
	0x8d, 0x9e, 0x46, 0x46, 0x46, 0x46, //0x00000c6f leal         $1179010630(%rsi), %ebx
	0x21, 0xd7, //0x00000c75 andl         %edx, %edi
	0x85, 0xdf, //0x00000c77 testl        %ebx, %edi
	0x0f, 0x85, 0xf6, 0x09, 0x00, 0x00, //0x00000c79 jne          LBB1_278
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x00000c7f movl         $-522133280, %edi
	0x29, 0xf7, //0x00000c84 subl         %esi, %edi
	0x81, 0xc6, 0x39, 0x39, 0x39, 0x39, //0x00000c86 addl         $960051513, %esi
	0x21, 0xfa, //0x00000c8c andl         %edi, %edx
	0x85, 0xf2, //0x00000c8e testl        %esi, %edx
	0x0f, 0x85, 0xdf, 0x09, 0x00, 0x00, //0x00000c90 jne          LBB1_278
	0x0f, 0xc8, //0x00000c96 bswapl       %eax
	0x89, 0xc1, //0x00000c98 movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x00000c9a shrl         $4, %ecx
	0xf7, 0xd1, //0x00000c9d notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x00000c9f andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x00000ca5 leal         (%rcx,%rcx,8), %ecx
	0x25, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000ca8 andl         $252645135, %eax
	0x01, 0xc8, //0x00000cad addl         %ecx, %eax
	0x89, 0xc1, //0x00000caf movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x00000cb1 shrl         $4, %ecx
	0x09, 0xc1, //0x00000cb4 orl          %eax, %ecx
	0x89, 0xc8, //0x00000cb6 movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x00000cb8 shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x00000cbb andl         $65280, %eax
	0x89, 0xca, //0x00000cc0 movl         %ecx, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00000cc2 andl         $128, %edx
	0x09, 0xc2, //0x00000cc8 orl          %eax, %edx
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x00000cca je           LBB1_98
	0xb9, 0xff, 0x00, 0x00, 0x00, //0x00000cd0 movl         $255, %ecx
	//0x00000cd5 LBB1_98
	0x49, 0x83, 0xc1, 0x06, //0x00000cd5 addq         $6, %r9
	0x4c, 0x89, 0xc8, //0x00000cd9 movq         %r9, %rax
	0x48, 0x8b, 0x55, 0xd0, //0x00000cdc movq         $-48(%rbp), %rdx
	//0x00000ce0 LBB1_99
	0x80, 0xf9, 0x0a, //0x00000ce0 cmpb         $10, %cl
	0x0f, 0x84, 0xdd, 0xfe, 0xff, 0xff, //0x00000ce3 je           LBB1_102
	0x80, 0xf9, 0x0d, //0x00000ce9 cmpb         $13, %cl
	0x0f, 0x84, 0xd4, 0xfe, 0xff, 0xff, //0x00000cec je           LBB1_102
	0x80, 0xf9, 0x3d, //0x00000cf2 cmpb         $61, %cl
	0x0f, 0x85, 0x85, 0x09, 0x00, 0x00, //0x00000cf5 jne          LBB1_258
	0x49, 0x89, 0xc1, //0x00000cfb movq         %rax, %r9
	0x41, 0x83, 0xfe, 0x02, //0x00000cfe cmpl         $2, %r14d
	0x0f, 0x84, 0x0d, 0x06, 0x00, 0x00, //0x00000d02 je           LBB1_212
	0x48, 0x8b, 0x75, 0xd0, //0x00000d08 movq         $-48(%rbp), %rsi
	0x48, 0x39, 0xc6, //0x00000d0c cmpq         %rax, %rsi
	0x0f, 0x87, 0x0e, 0x04, 0x00, 0x00, //0x00000d0f ja           LBB1_179
	//0x00000d15 LBB1_143
	0x49, 0x89, 0xc1, //0x00000d15 movq         %rax, %r9
	0xe9, 0x5d, 0x06, 0x00, 0x00, //0x00000d18 jmp          LBB1_216
	//0x00000d1d LBB1_103
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00000d1d movl         $1, %r11d
	0x44, 0x89, 0xe2, //0x00000d23 movl         %r12d, %edx
	0xe9, 0xd4, 0xfb, 0xff, 0xff, //0x00000d26 jmp          LBB1_38
	//0x00000d2b LBB1_104
	0x49, 0xff, 0xc1, //0x00000d2b incq         %r9
	0x41, 0xbb, 0x03, 0x00, 0x00, 0x00, //0x00000d2e movl         $3, %r11d
	0x49, 0x39, 0xd9, //0x00000d34 cmpq         %rbx, %r9
	0x0f, 0x83, 0xc2, 0xfb, 0xff, 0xff, //0x00000d37 jae          LBB1_38
	//0x00000d3d LBB1_105
	0x41, 0x0f, 0xb6, 0x09, //0x00000d3d movzbl       (%r9), %ecx
	0x48, 0x83, 0xf9, 0x0d, //0x00000d41 cmpq         $13, %rcx
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x00000d45 je           LBB1_104
	0x80, 0xf9, 0x0a, //0x00000d4b cmpb         $10, %cl
	0x0f, 0x84, 0xd7, 0xff, 0xff, 0xff, //0x00000d4e je           LBB1_104
	0x48, 0x8b, 0x45, 0xc0, //0x00000d54 movq         $-64(%rbp), %rax
	0x0f, 0xb6, 0x04, 0x08, //0x00000d58 movzbl       (%rax,%rcx), %eax
	0x49, 0xff, 0xc1, //0x00000d5c incq         %r9
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00000d5f cmpl         $255, %eax
	0x0f, 0x85, 0x74, 0x05, 0x00, 0x00, //0x00000d64 jne          LBB1_208
	0x41, 0x89, 0xd4, //0x00000d6a movl         %edx, %r12d
	0x41, 0xbb, 0x03, 0x00, 0x00, 0x00, //0x00000d6d movl         $3, %r11d
	0xe9, 0x59, 0xfc, 0xff, 0xff, //0x00000d73 jmp          LBB1_52
	//0x00000d78 LBB1_109
	0x49, 0xff, 0xc1, //0x00000d78 incq         %r9
	0x48, 0xff, 0xc1, //0x00000d7b incq         %rcx
	0x48, 0xff, 0xcb, //0x00000d7e decq         %rbx
	0x48, 0xff, 0xc2, //0x00000d81 incq         %rdx
	0x48, 0xff, 0xcf, //0x00000d84 decq         %rdi
	0x4c, 0x39, 0x4d, 0xd0, //0x00000d87 cmpq         %r9, $-48(%rbp)
	0x0f, 0x84, 0xe5, 0x05, 0x00, 0x00, //0x00000d8b je           LBB1_215
	//0x00000d91 LBB1_110
	0x41, 0x0f, 0xb6, 0x01, //0x00000d91 movzbl       (%r9), %eax
	0x3c, 0x0a, //0x00000d95 cmpb         $10, %al
	0x0f, 0x84, 0xdb, 0xff, 0xff, 0xff, //0x00000d97 je           LBB1_109
	0x3c, 0x0d, //0x00000d9d cmpb         $13, %al
	0x0f, 0x84, 0xd3, 0xff, 0xff, 0xff, //0x00000d9f je           LBB1_109
	0x3c, 0x3d, //0x00000da5 cmpb         $61, %al
	0x0f, 0x85, 0xbc, 0x01, 0x00, 0x00, //0x00000da7 jne          LBB1_144
	0x49, 0xff, 0xc1, //0x00000dad incq         %r9
	0x41, 0x83, 0xfe, 0x03, //0x00000db0 cmpl         $3, %r14d
	0x0f, 0x84, 0x5b, 0x05, 0x00, 0x00, //0x00000db4 je           LBB1_212
	0x4c, 0x39, 0x4d, 0xd0, //0x00000dba cmpq         %r9, $-48(%rbp)
	0x0f, 0x87, 0xb5, 0x04, 0x00, 0x00, //0x00000dbe ja           LBB1_197
	0xe9, 0xb1, 0x05, 0x00, 0x00, //0x00000dc4 jmp          LBB1_216
	//0x00000dc9 LBB1_115
	0x41, 0x89, 0xd4, //0x00000dc9 movl         %edx, %r12d
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00000dcc movl         $1, %r11d
	0xe9, 0xfa, 0xfb, 0xff, 0xff, //0x00000dd2 jmp          LBB1_52
	//0x00000dd7 LBB1_116
	0x3c, 0x2f, //0x00000dd7 cmpb         $47, %al
	0x0f, 0x85, 0x02, 0x00, 0x00, 0x00, //0x00000dd9 jne          LBB1_118
	0x89, 0xc1, //0x00000ddf movl         %eax, %ecx
	//0x00000de1 LBB1_118
	0x4d, 0x89, 0xc1, //0x00000de1 movq         %r8, %r9
	0x48, 0x8b, 0x5d, 0xd0, //0x00000de4 movq         $-48(%rbp), %rbx
	//0x00000de8 LBB1_119
	0x0f, 0xb6, 0xc1, //0x00000de8 movzbl       %cl, %eax
	0x48, 0x8b, 0x55, 0xc0, //0x00000deb movq         $-64(%rbp), %rdx
	0x0f, 0xb6, 0x04, 0x02, //0x00000def movzbl       (%rdx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00000df3 cmpl         $255, %eax
	0x0f, 0x84, 0x58, 0x01, 0x00, 0x00, //0x00000df8 je           LBB1_140
	0x44, 0x89, 0xe2, //0x00000dfe movl         %r12d, %edx
	0xc1, 0xe2, 0x06, //0x00000e01 shll         $6, %edx
	0x09, 0xc2, //0x00000e04 orl          %eax, %edx
	0x41, 0xbb, 0x02, 0x00, 0x00, 0x00, //0x00000e06 movl         $2, %r11d
	0x49, 0x39, 0xd9, //0x00000e0c cmpq         %rbx, %r9
	0x0f, 0x83, 0xd4, 0x04, 0x00, 0x00, //0x00000e0f jae          LBB1_209
	0x41, 0x89, 0xd4, //0x00000e15 movl         %edx, %r12d
	0xe9, 0x20, 0x00, 0x00, 0x00, //0x00000e18 jmp          LBB1_125
	//0x00000e1d LBB1_122
	0x3c, 0x6e, //0x00000e1d cmpb         $110, %al
	0x0f, 0x85, 0x52, 0x01, 0x00, 0x00, //0x00000e1f jne          LBB1_150
	//0x00000e25 LBB1_123
	0x48, 0x89, 0xd7, //0x00000e25 movq         %rdx, %rdi
	0x44, 0x89, 0xe2, //0x00000e28 movl         %r12d, %edx
	//0x00000e2b LBB1_124
	0x41, 0xbb, 0x02, 0x00, 0x00, 0x00, //0x00000e2b movl         $2, %r11d
	0x49, 0x89, 0xf9, //0x00000e31 movq         %rdi, %r9
	0x48, 0x39, 0xdf, //0x00000e34 cmpq         %rbx, %rdi
	0x0f, 0x83, 0x24, 0x01, 0x00, 0x00, //0x00000e37 jae          LBB1_142
	//0x00000e3d LBB1_125
	0x49, 0x8d, 0x79, 0x01, //0x00000e3d leaq         $1(%r9), %rdi
	0x41, 0x0f, 0xb6, 0x09, //0x00000e41 movzbl       (%r9), %ecx
	0x80, 0xf9, 0x5c, //0x00000e45 cmpb         $92, %cl
	0x0f, 0x85, 0xf1, 0x00, 0x00, 0x00, //0x00000e48 jne          LBB1_138
	0x49, 0x8d, 0x51, 0x02, //0x00000e4e leaq         $2(%r9), %rdx
	0xb1, 0xff, //0x00000e52 movb         $-1, %cl
	0x48, 0x39, 0xda, //0x00000e54 cmpq         %rbx, %rdx
	0x0f, 0x87, 0x27, 0x01, 0x00, 0x00, //0x00000e57 ja           LBB1_153
	0x0f, 0xb6, 0x07, //0x00000e5d movzbl       (%rdi), %eax
	0x3c, 0x71, //0x00000e60 cmpb         $113, %al
	0x0f, 0x8e, 0xb5, 0xff, 0xff, 0xff, //0x00000e62 jle          LBB1_122
	0x3c, 0x72, //0x00000e68 cmpb         $114, %al
	0x0f, 0x84, 0xb5, 0xff, 0xff, 0xff, //0x00000e6a je           LBB1_123
	0x3c, 0x75, //0x00000e70 cmpb         $117, %al
	0x0f, 0x85, 0x09, 0x01, 0x00, 0x00, //0x00000e72 jne          LBB1_152
	0x48, 0x89, 0xd8, //0x00000e78 movq         %rbx, %rax
	0x48, 0x29, 0xd0, //0x00000e7b subq         %rdx, %rax
	0x48, 0x83, 0xf8, 0x04, //0x00000e7e cmpq         $4, %rax
	0x0f, 0x8c, 0xf9, 0x00, 0x00, 0x00, //0x00000e82 jl           LBB1_152
	0x8b, 0x02, //0x00000e88 movl         (%rdx), %eax
	0x89, 0xc6, //0x00000e8a movl         %eax, %esi
	0xf7, 0xd6, //0x00000e8c notl         %esi
	0x8d, 0xb8, 0xd0, 0xcf, 0xcf, 0xcf, //0x00000e8e leal         $-808464432(%rax), %edi
	0x81, 0xe6, 0x80, 0x80, 0x80, 0x80, //0x00000e94 andl         $-2139062144, %esi
	0x85, 0xfe, //0x00000e9a testl        %edi, %esi
	0x0f, 0x85, 0xdf, 0x00, 0x00, 0x00, //0x00000e9c jne          LBB1_152
	0x8d, 0xb8, 0x19, 0x19, 0x19, 0x19, //0x00000ea2 leal         $421075225(%rax), %edi
	0x09, 0xc7, //0x00000ea8 orl          %eax, %edi
	0xf7, 0xc7, 0x80, 0x80, 0x80, 0x80, //0x00000eaa testl        $-2139062144, %edi
	0x0f, 0x85, 0xcb, 0x00, 0x00, 0x00, //0x00000eb0 jne          LBB1_152
	0x89, 0xc7, //0x00000eb6 movl         %eax, %edi
	0x81, 0xe7, 0x7f, 0x7f, 0x7f, 0x7f, //0x00000eb8 andl         $2139062143, %edi
	0xbb, 0xc0, 0xc0, 0xc0, 0xc0, //0x00000ebe movl         $-1061109568, %ebx
	0x29, 0xfb, //0x00000ec3 subl         %edi, %ebx
	0x44, 0x8d, 0x87, 0x46, 0x46, 0x46, 0x46, //0x00000ec5 leal         $1179010630(%rdi), %r8d
	0x21, 0xf3, //0x00000ecc andl         %esi, %ebx
	0x44, 0x85, 0xc3, //0x00000ece testl        %r8d, %ebx
	0x48, 0x8b, 0x5d, 0xd0, //0x00000ed1 movq         $-48(%rbp), %rbx
	0x0f, 0x85, 0xa6, 0x00, 0x00, 0x00, //0x00000ed5 jne          LBB1_152
	0xbb, 0xe0, 0xe0, 0xe0, 0xe0, //0x00000edb movl         $-522133280, %ebx
	0x29, 0xfb, //0x00000ee0 subl         %edi, %ebx
	0x81, 0xc7, 0x39, 0x39, 0x39, 0x39, //0x00000ee2 addl         $960051513, %edi
	0x21, 0xde, //0x00000ee8 andl         %ebx, %esi
	0x48, 0x8b, 0x5d, 0xd0, //0x00000eea movq         $-48(%rbp), %rbx
	0x85, 0xfe, //0x00000eee testl        %edi, %esi
	0x0f, 0x85, 0x8b, 0x00, 0x00, 0x00, //0x00000ef0 jne          LBB1_152
	0x0f, 0xc8, //0x00000ef6 bswapl       %eax
	0x89, 0xc1, //0x00000ef8 movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x00000efa shrl         $4, %ecx
	0xf7, 0xd1, //0x00000efd notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x00000eff andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x00000f05 leal         (%rcx,%rcx,8), %ecx
	0x25, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000f08 andl         $252645135, %eax
	0x01, 0xc8, //0x00000f0d addl         %ecx, %eax
	0x89, 0xc1, //0x00000f0f movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x00000f11 shrl         $4, %ecx
	0x09, 0xc1, //0x00000f14 orl          %eax, %ecx
	0x89, 0xc8, //0x00000f16 movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x00000f18 shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x00000f1b andl         $65280, %eax
	0x89, 0xca, //0x00000f20 movl         %ecx, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00000f22 andl         $128, %edx
	0x09, 0xc2, //0x00000f28 orl          %eax, %edx
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x00000f2a je           LBB1_137
	0xb9, 0xff, 0x00, 0x00, 0x00, //0x00000f30 movl         $255, %ecx
	//0x00000f35 LBB1_137
	0x49, 0x83, 0xc1, 0x06, //0x00000f35 addq         $6, %r9
	0x4c, 0x89, 0xcf, //0x00000f39 movq         %r9, %rdi
	0x44, 0x89, 0xe2, //0x00000f3c movl         %r12d, %edx
	//0x00000f3f LBB1_138
	0x80, 0xf9, 0x0a, //0x00000f3f cmpb         $10, %cl
	0x0f, 0x84, 0xe3, 0xfe, 0xff, 0xff, //0x00000f42 je           LBB1_124
	0x80, 0xf9, 0x0d, //0x00000f48 cmpb         $13, %cl
	0x0f, 0x84, 0xda, 0xfe, 0xff, 0xff, //0x00000f4b je           LBB1_124
	0xe9, 0x2e, 0x00, 0x00, 0x00, //0x00000f51 jmp          LBB1_153
	//0x00000f56 LBB1_140
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00000f56 movl         $1, %r11d
	0xe9, 0x70, 0xfa, 0xff, 0xff, //0x00000f5c jmp          LBB1_52
	//0x00000f61 LBB1_142
	0x49, 0x89, 0xf9, //0x00000f61 movq         %rdi, %r9
	0xe9, 0x96, 0xf9, 0xff, 0xff, //0x00000f64 jmp          LBB1_38
	//0x00000f69 LBB1_144
	0x49, 0xff, 0xc1, //0x00000f69 incq         %r9
	0x4c, 0x89, 0xca, //0x00000f6c movq         %r9, %rdx
	//0x00000f6f LBB1_145
	0x49, 0x89, 0xd1, //0x00000f6f movq         %rdx, %r9
	0xe9, 0x9e, 0x03, 0x00, 0x00, //0x00000f72 jmp          LBB1_212
	//0x00000f77 LBB1_150
	0x3c, 0x2f, //0x00000f77 cmpb         $47, %al
	0x0f, 0x85, 0x02, 0x00, 0x00, 0x00, //0x00000f79 jne          LBB1_152
	0x89, 0xc1, //0x00000f7f movl         %eax, %ecx
	//0x00000f81 LBB1_152
	0x48, 0x89, 0xd7, //0x00000f81 movq         %rdx, %rdi
	//0x00000f84 LBB1_153
	0x0f, 0xb6, 0xc1, //0x00000f84 movzbl       %cl, %eax
	0x48, 0x8b, 0x55, 0xc0, //0x00000f87 movq         $-64(%rbp), %rdx
	0x0f, 0xb6, 0x04, 0x02, //0x00000f8b movzbl       (%rdx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00000f8f cmpl         $255, %eax
	0x0f, 0x84, 0x50, 0x01, 0x00, 0x00, //0x00000f94 je           LBB1_174
	0x44, 0x89, 0xe2, //0x00000f9a movl         %r12d, %edx
	0xc1, 0xe2, 0x06, //0x00000f9d shll         $6, %edx
	0x09, 0xc2, //0x00000fa0 orl          %eax, %edx
	0x41, 0xbb, 0x03, 0x00, 0x00, 0x00, //0x00000fa2 movl         $3, %r11d
	0x48, 0x39, 0xdf, //0x00000fa8 cmpq         %rbx, %rdi
	0x0f, 0x83, 0x47, 0x01, 0x00, 0x00, //0x00000fab jae          LBB1_175
	0x41, 0x89, 0xd4, //0x00000fb1 movl         %edx, %r12d
	0xe9, 0x1d, 0x00, 0x00, 0x00, //0x00000fb4 jmp          LBB1_159
	//0x00000fb9 LBB1_156
	0x3c, 0x6e, //0x00000fb9 cmpb         $110, %al
	0x0f, 0x85, 0xf3, 0x02, 0x00, 0x00, //0x00000fbb jne          LBB1_203
	//0x00000fc1 LBB1_157
	0x4d, 0x89, 0xc1, //0x00000fc1 movq         %r8, %r9
	//0x00000fc4 LBB1_158
	0x41, 0xbb, 0x03, 0x00, 0x00, 0x00, //0x00000fc4 movl         $3, %r11d
	0x4c, 0x89, 0xcf, //0x00000fca movq         %r9, %rdi
	0x49, 0x39, 0xd9, //0x00000fcd cmpq         %rbx, %r9
	0x0f, 0x83, 0x29, 0xf9, 0xff, 0xff, //0x00000fd0 jae          LBB1_38
	//0x00000fd6 LBB1_159
	0x4c, 0x8d, 0x4f, 0x01, //0x00000fd6 leaq         $1(%rdi), %r9
	0x0f, 0xb6, 0x0f, //0x00000fda movzbl       (%rdi), %ecx
	0x80, 0xf9, 0x5c, //0x00000fdd cmpb         $92, %cl
	0x0f, 0x85, 0xed, 0x00, 0x00, 0x00, //0x00000fe0 jne          LBB1_172
	0x4c, 0x8d, 0x47, 0x02, //0x00000fe6 leaq         $2(%rdi), %r8
	0xb1, 0xff, //0x00000fea movb         $-1, %cl
	0x49, 0x39, 0xd8, //0x00000fec cmpq         %rbx, %r8
	0x0f, 0x87, 0xd0, 0x02, 0x00, 0x00, //0x00000fef ja           LBB1_206
	0x41, 0x0f, 0xb6, 0x01, //0x00000ff5 movzbl       (%r9), %eax
	0x3c, 0x71, //0x00000ff9 cmpb         $113, %al
	0x0f, 0x8e, 0xb8, 0xff, 0xff, 0xff, //0x00000ffb jle          LBB1_156
	0x3c, 0x72, //0x00001001 cmpb         $114, %al
	0x0f, 0x84, 0xb8, 0xff, 0xff, 0xff, //0x00001003 je           LBB1_157
	0x3c, 0x75, //0x00001009 cmpb         $117, %al
	0x0f, 0x85, 0xad, 0x02, 0x00, 0x00, //0x0000100b jne          LBB1_205
	0x48, 0x8b, 0x45, 0xd0, //0x00001011 movq         $-48(%rbp), %rax
	0x4c, 0x29, 0xc0, //0x00001015 subq         %r8, %rax
	0x48, 0x83, 0xf8, 0x04, //0x00001018 cmpq         $4, %rax
	0x0f, 0x8c, 0x9c, 0x02, 0x00, 0x00, //0x0000101c jl           LBB1_205
	0x41, 0x8b, 0x18, //0x00001022 movl         (%r8), %ebx
	0x89, 0xde, //0x00001025 movl         %ebx, %esi
	0xf7, 0xd6, //0x00001027 notl         %esi
	0x8d, 0x83, 0xd0, 0xcf, 0xcf, 0xcf, //0x00001029 leal         $-808464432(%rbx), %eax
	0x81, 0xe6, 0x80, 0x80, 0x80, 0x80, //0x0000102f andl         $-2139062144, %esi
	0x85, 0xc6, //0x00001035 testl        %eax, %esi
	0x0f, 0x85, 0x81, 0x02, 0x00, 0x00, //0x00001037 jne          LBB1_205
	0x8d, 0x83, 0x19, 0x19, 0x19, 0x19, //0x0000103d leal         $421075225(%rbx), %eax
	0x09, 0xd8, //0x00001043 orl          %ebx, %eax
	0xa9, 0x80, 0x80, 0x80, 0x80, //0x00001045 testl        $-2139062144, %eax
	0x0f, 0x85, 0x6e, 0x02, 0x00, 0x00, //0x0000104a jne          LBB1_205
	0x89, 0xd8, //0x00001050 movl         %ebx, %eax
	0x25, 0x7f, 0x7f, 0x7f, 0x7f, //0x00001052 andl         $2139062143, %eax
	0xba, 0xc0, 0xc0, 0xc0, 0xc0, //0x00001057 movl         $-1061109568, %edx
	0x29, 0xc2, //0x0000105c subl         %eax, %edx
	0x44, 0x8d, 0x88, 0x46, 0x46, 0x46, 0x46, //0x0000105e leal         $1179010630(%rax), %r9d
	0x21, 0xf2, //0x00001065 andl         %esi, %edx
	0x44, 0x85, 0xca, //0x00001067 testl        %r9d, %edx
	0x0f, 0x85, 0x4e, 0x02, 0x00, 0x00, //0x0000106a jne          LBB1_205
	0xba, 0xe0, 0xe0, 0xe0, 0xe0, //0x00001070 movl         $-522133280, %edx
	0x29, 0xc2, //0x00001075 subl         %eax, %edx
	0x05, 0x39, 0x39, 0x39, 0x39, //0x00001077 addl         $960051513, %eax
	0x21, 0xd6, //0x0000107c andl         %edx, %esi
	0x85, 0xc6, //0x0000107e testl        %eax, %esi
	0x0f, 0x85, 0x38, 0x02, 0x00, 0x00, //0x00001080 jne          LBB1_205
	0x0f, 0xcb, //0x00001086 bswapl       %ebx
	0x89, 0xd8, //0x00001088 movl         %ebx, %eax
	0xc1, 0xe8, 0x04, //0x0000108a shrl         $4, %eax
	0xf7, 0xd0, //0x0000108d notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x0000108f andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00001094 leal         (%rax,%rax,8), %eax
	0x81, 0xe3, 0x0f, 0x0f, 0x0f, 0x0f, //0x00001097 andl         $252645135, %ebx
	0x01, 0xc3, //0x0000109d addl         %eax, %ebx
	0x89, 0xd9, //0x0000109f movl         %ebx, %ecx
	0xc1, 0xe9, 0x04, //0x000010a1 shrl         $4, %ecx
	0x09, 0xd9, //0x000010a4 orl          %ebx, %ecx
	0x89, 0xc8, //0x000010a6 movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x000010a8 shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x000010ab andl         $65280, %eax
	0x89, 0xca, //0x000010b0 movl         %ecx, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x000010b2 andl         $128, %edx
	0x09, 0xc2, //0x000010b8 orl          %eax, %edx
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x000010ba je           LBB1_171
	0xb9, 0xff, 0x00, 0x00, 0x00, //0x000010c0 movl         $255, %ecx
	//0x000010c5 LBB1_171
	0x48, 0x83, 0xc7, 0x06, //0x000010c5 addq         $6, %rdi
	0x49, 0x89, 0xf9, //0x000010c9 movq         %rdi, %r9
	0x48, 0x8b, 0x5d, 0xd0, //0x000010cc movq         $-48(%rbp), %rbx
	0x44, 0x89, 0xe2, //0x000010d0 movl         %r12d, %edx
	//0x000010d3 LBB1_172
	0x80, 0xf9, 0x0a, //0x000010d3 cmpb         $10, %cl
	0x0f, 0x84, 0xe8, 0xfe, 0xff, 0xff, //0x000010d6 je           LBB1_158
	0x80, 0xf9, 0x0d, //0x000010dc cmpb         $13, %cl
	0x0f, 0x84, 0xdf, 0xfe, 0xff, 0xff, //0x000010df je           LBB1_158
	0xe9, 0xdb, 0x01, 0x00, 0x00, //0x000010e5 jmp          LBB1_206
	//0x000010ea LBB1_174
	0x41, 0xbb, 0x02, 0x00, 0x00, 0x00, //0x000010ea movl         $2, %r11d
	0x49, 0x89, 0xf9, //0x000010f0 movq         %rdi, %r9
	0xe9, 0xd9, 0xf8, 0xff, 0xff, //0x000010f3 jmp          LBB1_52
	//0x000010f8 LBB1_175
	0x49, 0x89, 0xf9, //0x000010f8 movq         %rdi, %r9
	0xe9, 0xe9, 0x01, 0x00, 0x00, //0x000010fb jmp          LBB1_209
	//0x00001100 LBB1_195
	0x4c, 0x89, 0xc9, //0x00001100 movq         %r9, %rcx
	0x48, 0x89, 0xc8, //0x00001103 movq         %rcx, %rax
	0x48, 0x39, 0xf1, //0x00001106 cmpq         %rsi, %rcx
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x00001109 jb           LBB1_179
	0xe9, 0x66, 0x02, 0x00, 0x00, //0x0000110f jmp          LBB1_216
	//0x00001114 LBB1_177
	0x49, 0x89, 0xc9, //0x00001114 movq         %rcx, %r9
	0x48, 0x89, 0xc8, //0x00001117 movq         %rcx, %rax
	0x48, 0x39, 0xf1, //0x0000111a cmpq         %rsi, %rcx
	0x0f, 0x83, 0x57, 0x02, 0x00, 0x00, //0x0000111d jae          LBB1_216
	//0x00001123 LBB1_179
	0x4c, 0x8d, 0x48, 0x01, //0x00001123 leaq         $1(%rax), %r9
	0x0f, 0xb6, 0x08, //0x00001127 movzbl       (%rax), %ecx
	0x80, 0xf9, 0x5c, //0x0000112a cmpb         $92, %cl
	0x0f, 0x85, 0xee, 0x00, 0x00, 0x00, //0x0000112d jne          LBB1_192
	0x48, 0x8d, 0x48, 0x02, //0x00001133 leaq         $2(%rax), %rcx
	0x48, 0x39, 0xf1, //0x00001137 cmpq         %rsi, %rcx
	0x0f, 0x87, 0x3d, 0x05, 0x00, 0x00, //0x0000113a ja           LBB1_279
	0x41, 0x0f, 0xb6, 0x11, //0x00001140 movzbl       (%r9), %edx
	0x80, 0xfa, 0x6e, //0x00001144 cmpb         $110, %dl
	0x0f, 0x84, 0xc7, 0xff, 0xff, 0xff, //0x00001147 je           LBB1_177
	0x80, 0xfa, 0x72, //0x0000114d cmpb         $114, %dl
	0x0f, 0x84, 0xbe, 0xff, 0xff, 0xff, //0x00001150 je           LBB1_177
	0x80, 0xfa, 0x75, //0x00001156 cmpb         $117, %dl
	0x0f, 0x85, 0x16, 0x05, 0x00, 0x00, //0x00001159 jne          LBB1_278
	0x48, 0x89, 0xf2, //0x0000115f movq         %rsi, %rdx
	0x48, 0x29, 0xca, //0x00001162 subq         %rcx, %rdx
	0x48, 0x83, 0xfa, 0x04, //0x00001165 cmpq         $4, %rdx
	0x0f, 0x8c, 0x06, 0x05, 0x00, 0x00, //0x00001169 jl           LBB1_278
	0x8b, 0x11, //0x0000116f movl         (%rcx), %edx
	0x89, 0xd6, //0x00001171 movl         %edx, %esi
	0xf7, 0xd6, //0x00001173 notl         %esi
	0x8d, 0xba, 0xd0, 0xcf, 0xcf, 0xcf, //0x00001175 leal         $-808464432(%rdx), %edi
	0x81, 0xe6, 0x80, 0x80, 0x80, 0x80, //0x0000117b andl         $-2139062144, %esi
	0x85, 0xfe, //0x00001181 testl        %edi, %esi
	0x0f, 0x85, 0xec, 0x04, 0x00, 0x00, //0x00001183 jne          LBB1_278
	0x8d, 0xba, 0x19, 0x19, 0x19, 0x19, //0x00001189 leal         $421075225(%rdx), %edi
	0x09, 0xd7, //0x0000118f orl          %edx, %edi
	0xf7, 0xc7, 0x80, 0x80, 0x80, 0x80, //0x00001191 testl        $-2139062144, %edi
	0x0f, 0x85, 0xd8, 0x04, 0x00, 0x00, //0x00001197 jne          LBB1_278
	0x89, 0xd7, //0x0000119d movl         %edx, %edi
	0x81, 0xe7, 0x7f, 0x7f, 0x7f, 0x7f, //0x0000119f andl         $2139062143, %edi
	0xbb, 0xc0, 0xc0, 0xc0, 0xc0, //0x000011a5 movl         $-1061109568, %ebx
	0x29, 0xfb, //0x000011aa subl         %edi, %ebx
	0x44, 0x8d, 0x87, 0x46, 0x46, 0x46, 0x46, //0x000011ac leal         $1179010630(%rdi), %r8d
	0x21, 0xf3, //0x000011b3 andl         %esi, %ebx
	0x44, 0x85, 0xc3, //0x000011b5 testl        %r8d, %ebx
	0x0f, 0x85, 0xb7, 0x04, 0x00, 0x00, //0x000011b8 jne          LBB1_278
	0xbb, 0xe0, 0xe0, 0xe0, 0xe0, //0x000011be movl         $-522133280, %ebx
	0x29, 0xfb, //0x000011c3 subl         %edi, %ebx
	0x81, 0xc7, 0x39, 0x39, 0x39, 0x39, //0x000011c5 addl         $960051513, %edi
	0x21, 0xde, //0x000011cb andl         %ebx, %esi
	0x85, 0xfe, //0x000011cd testl        %edi, %esi
	0x0f, 0x85, 0xa0, 0x04, 0x00, 0x00, //0x000011cf jne          LBB1_278
	0x0f, 0xca, //0x000011d5 bswapl       %edx
	0x89, 0xd1, //0x000011d7 movl         %edx, %ecx
	0xc1, 0xe9, 0x04, //0x000011d9 shrl         $4, %ecx
	0xf7, 0xd1, //0x000011dc notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x000011de andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x000011e4 leal         (%rcx,%rcx,8), %ecx
	0x81, 0xe2, 0x0f, 0x0f, 0x0f, 0x0f, //0x000011e7 andl         $252645135, %edx
	0x01, 0xca, //0x000011ed addl         %ecx, %edx
	0x89, 0xd1, //0x000011ef movl         %edx, %ecx
	0xc1, 0xe9, 0x04, //0x000011f1 shrl         $4, %ecx
	0x09, 0xd1, //0x000011f4 orl          %edx, %ecx
	0x89, 0xca, //0x000011f6 movl         %ecx, %edx
	0xc1, 0xea, 0x08, //0x000011f8 shrl         $8, %edx
	0x81, 0xe2, 0x00, 0xff, 0x00, 0x00, //0x000011fb andl         $65280, %edx
	0x89, 0xce, //0x00001201 movl         %ecx, %esi
	0x81, 0xe6, 0x80, 0x00, 0x00, 0x00, //0x00001203 andl         $128, %esi
	0x09, 0xd6, //0x00001209 orl          %edx, %esi
	0x48, 0x8b, 0x75, 0xd0, //0x0000120b movq         $-48(%rbp), %rsi
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x0000120f je           LBB1_191
	0xb9, 0xff, 0x00, 0x00, 0x00, //0x00001215 movl         $255, %ecx
	//0x0000121a LBB1_191
	0x48, 0x83, 0xc0, 0x06, //0x0000121a addq         $6, %rax
	0x49, 0x89, 0xc1, //0x0000121e movq         %rax, %r9
	//0x00001221 LBB1_192
	0x80, 0xf9, 0x0a, //0x00001221 cmpb         $10, %cl
	0x0f, 0x84, 0xd6, 0xfe, 0xff, 0xff, //0x00001224 je           LBB1_195
	0x80, 0xf9, 0x0d, //0x0000122a cmpb         $13, %cl
	0x0f, 0x84, 0xcd, 0xfe, 0xff, 0xff, //0x0000122d je           LBB1_195
	0x80, 0xf9, 0x3d, //0x00001233 cmpb         $61, %cl
	0x0f, 0x85, 0xd9, 0x00, 0x00, 0x00, //0x00001236 jne          LBB1_212
	0x41, 0x83, 0xfe, 0x03, //0x0000123c cmpl         $3, %r14d
	0x0f, 0x84, 0xcf, 0x00, 0x00, 0x00, //0x00001240 je           LBB1_212
	0x4c, 0x39, 0x4d, 0xd0, //0x00001246 cmpq         %r9, $-48(%rbp)
	0x0f, 0x86, 0x2a, 0x01, 0x00, 0x00, //0x0000124a jbe          LBB1_216
	0x48, 0x8b, 0x55, 0xd0, //0x00001250 movq         $-48(%rbp), %rdx
	0xe9, 0x8e, 0x01, 0x00, 0x00, //0x00001254 jmp          LBB1_234
	//0x00001259 LBB1_176
	0x41, 0x89, 0xd4, //0x00001259 movl         %edx, %r12d
	0x41, 0xbb, 0x02, 0x00, 0x00, 0x00, //0x0000125c movl         $2, %r11d
	0xe9, 0x6a, 0xf7, 0xff, 0xff, //0x00001262 jmp          LBB1_52
	//0x00001267 LBB1_196
	0x48, 0xff, 0xc1, //0x00001267 incq         %rcx
	0x48, 0xff, 0xc2, //0x0000126a incq         %rdx
	0x48, 0xff, 0xcf, //0x0000126d decq         %rdi
	0x48, 0xff, 0xcb, //0x00001270 decq         %rbx
	0x0f, 0x84, 0xfd, 0x00, 0x00, 0x00, //0x00001273 je           LBB1_215
	//0x00001279 LBB1_197
	0x0f, 0xb6, 0x41, 0xff, //0x00001279 movzbl       $-1(%rcx), %eax
	0x3c, 0x0a, //0x0000127d cmpb         $10, %al
	0x0f, 0x84, 0xe2, 0xff, 0xff, 0xff, //0x0000127f je           LBB1_196
	0x3c, 0x0d, //0x00001285 cmpb         $13, %al
	0x0f, 0x84, 0xda, 0xff, 0xff, 0xff, //0x00001287 je           LBB1_196
	0x3c, 0x3d, //0x0000128d cmpb         $61, %al
	0x0f, 0x85, 0x75, 0x02, 0x00, 0x00, //0x0000128f jne          LBB1_252
	0x49, 0x89, 0xc9, //0x00001295 movq         %rcx, %r9
	0x41, 0x83, 0xfe, 0x04, //0x00001298 cmpl         $4, %r14d
	0x0f, 0x84, 0x73, 0x00, 0x00, 0x00, //0x0000129c je           LBB1_212
	0x48, 0x39, 0x4d, 0xd0, //0x000012a2 cmpq         %rcx, $-48(%rbp)
	0x0f, 0x87, 0xaf, 0x00, 0x00, 0x00, //0x000012a6 ja           LBB1_229
	0x49, 0x89, 0xc9, //0x000012ac movq         %rcx, %r9
	0xe9, 0xc6, 0x00, 0x00, 0x00, //0x000012af jmp          LBB1_216
	//0x000012b4 LBB1_203
	0x3c, 0x2f, //0x000012b4 cmpb         $47, %al
	0x0f, 0x85, 0x02, 0x00, 0x00, 0x00, //0x000012b6 jne          LBB1_205
	0x89, 0xc1, //0x000012bc movl         %eax, %ecx
	//0x000012be LBB1_205
	0x4d, 0x89, 0xc1, //0x000012be movq         %r8, %r9
	0x48, 0x8b, 0x5d, 0xd0, //0x000012c1 movq         $-48(%rbp), %rbx
	//0x000012c5 LBB1_206
	0x0f, 0xb6, 0xc1, //0x000012c5 movzbl       %cl, %eax
	0x48, 0x8b, 0x55, 0xc0, //0x000012c8 movq         $-64(%rbp), %rdx
	0x0f, 0xb6, 0x04, 0x02, //0x000012cc movzbl       (%rdx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x000012d0 cmpl         $255, %eax
	0x0f, 0x84, 0x69, 0x00, 0x00, 0x00, //0x000012d5 je           LBB1_214
	0x44, 0x89, 0xe2, //0x000012db movl         %r12d, %edx
	//0x000012de LBB1_208
	0xc1, 0xe2, 0x06, //0x000012de shll         $6, %edx
	0x09, 0xc2, //0x000012e1 orl          %eax, %edx
	0x41, 0xbb, 0x04, 0x00, 0x00, 0x00, //0x000012e3 movl         $4, %r11d
	//0x000012e9 LBB1_209
	0x41, 0x89, 0xd4, //0x000012e9 movl         %edx, %r12d
	0x41, 0xf6, 0xc2, 0x02, //0x000012ec testb        $2, %r10b
	0x0f, 0x94, 0xc0, //0x000012f0 sete         %al
	0x41, 0x83, 0xfb, 0x01, //0x000012f3 cmpl         $1, %r11d
	0x0f, 0x94, 0xc1, //0x000012f7 sete         %cl
	0x49, 0x39, 0xd9, //0x000012fa cmpq         %rbx, %r9
	0x0f, 0x82, 0x77, 0x00, 0x00, 0x00, //0x000012fd jb           LBB1_216
	0x41, 0x83, 0xfb, 0x04, //0x00001303 cmpl         $4, %r11d
	0x0f, 0x84, 0x6d, 0x00, 0x00, 0x00, //0x00001307 je           LBB1_216
	0x08, 0xc8, //0x0000130d orb          %cl, %al
	0x0f, 0x84, 0x65, 0x00, 0x00, 0x00, //0x0000130f je           LBB1_216
	//0x00001315 LBB1_212
	0x49, 0x8d, 0x49, 0x01, //0x00001315 leaq         $1(%r9), %rcx
	0x4c, 0x39, 0x4d, 0xd0, //0x00001319 cmpq         %r9, $-48(%rbp)
	0x49, 0x0f, 0x45, 0xc9, //0x0000131d cmovneq      %r9, %rcx
	0x4c, 0x29, 0xf9, //0x00001321 subq         %r15, %rcx
	0x0f, 0x85, 0xb0, 0x36, 0x00, 0x00, //0x00001324 jne          LBB1_1140
	0x4d, 0x89, 0xf9, //0x0000132a movq         %r15, %r9
	0x48, 0x8b, 0x4d, 0xb0, //0x0000132d movq         $-80(%rbp), %rcx
	0x48, 0x89, 0x4d, 0xb0, //0x00001331 movq         %rcx, $-80(%rbp)
	0x48, 0x39, 0x4d, 0xc8, //0x00001335 cmpq         %rcx, $-56(%rbp)
	0x0f, 0x83, 0x26, 0xf3, 0xff, 0xff, //0x00001339 jae          LBB1_223
	0xe9, 0xa2, 0x15, 0x00, 0x00, //0x0000133f jmp          LBB1_572
	//0x00001344 LBB1_214
	0x41, 0xbb, 0x03, 0x00, 0x00, 0x00, //0x00001344 movl         $3, %r11d
	0xe9, 0x82, 0xf6, 0xff, 0xff, //0x0000134a jmp          LBB1_52
	//0x0000134f LBB1_228
	0x48, 0xff, 0xc2, //0x0000134f incq         %rdx
	0x48, 0xff, 0xcf, //0x00001352 decq         %rdi
	0x0f, 0x84, 0x1b, 0x00, 0x00, 0x00, //0x00001355 je           LBB1_215
	//0x0000135b LBB1_229
	0x0f, 0xb6, 0x42, 0xff, //0x0000135b movzbl       $-1(%rdx), %eax
	0x3c, 0x0a, //0x0000135f cmpb         $10, %al
	0x0f, 0x84, 0xe8, 0xff, 0xff, 0xff, //0x00001361 je           LBB1_228
	0x3c, 0x0d, //0x00001367 cmpb         $13, %al
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x00001369 je           LBB1_228
	0x3c, 0x3d, //0x0000136f cmpb         $61, %al
	0xe9, 0xf9, 0xfb, 0xff, 0xff, //0x00001371 jmp          LBB1_145
	//0x00001376 LBB1_215
	0x4c, 0x8b, 0x4d, 0xd0, //0x00001376 movq         $-48(%rbp), %r9
	//0x0000137a LBB1_216
	0xb0, 0x04, //0x0000137a movb         $4, %al
	0x44, 0x28, 0xd8, //0x0000137c subb         %r11b, %al
	0x0f, 0xb6, 0xc0, //0x0000137f movzbl       %al, %eax
	0x01, 0xc0, //0x00001382 addl         %eax, %eax
	0x8d, 0x0c, 0x40, //0x00001384 leal         (%rax,%rax,2), %ecx
	0x44, 0x89, 0xe0, //0x00001387 movl         %r12d, %eax
	0xd3, 0xe0, //0x0000138a shll         %cl, %eax
	0x41, 0x83, 0xfb, 0x02, //0x0000138c cmpl         $2, %r11d
	0x48, 0x8b, 0x4d, 0xb0, //0x00001390 movq         $-80(%rbp), %rcx
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00001394 je           LBB1_221
	0x41, 0x83, 0xfb, 0x03, //0x0000139a cmpl         $3, %r11d
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000139e je           LBB1_220
	0x41, 0x83, 0xfb, 0x04, //0x000013a4 cmpl         $4, %r11d
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x000013a8 jne          LBB1_222
	0x88, 0x41, 0x02, //0x000013ae movb         %al, $2(%rcx)
	//0x000013b1 LBB1_220
	0x88, 0x61, 0x01, //0x000013b1 movb         %ah, $1(%rcx)
	//0x000013b4 LBB1_221
	0xc1, 0xe8, 0x10, //0x000013b4 shrl         $16, %eax
	0x88, 0x01, //0x000013b7 movb         %al, (%rcx)
	//0x000013b9 LBB1_222
	0x44, 0x89, 0xd8, //0x000013b9 movl         %r11d, %eax
	0x48, 0x8d, 0x4c, 0x01, 0xff, //0x000013bc leaq         $-1(%rcx,%rax), %rcx
	0x48, 0x89, 0x4d, 0xb0, //0x000013c1 movq         %rcx, $-80(%rbp)
	0x48, 0x39, 0x4d, 0xc8, //0x000013c5 cmpq         %rcx, $-56(%rbp)
	0x0f, 0x83, 0x96, 0xf2, 0xff, 0xff, //0x000013c9 jae          LBB1_223
	0xe9, 0x12, 0x15, 0x00, 0x00, //0x000013cf jmp          LBB1_572
	//0x000013d4 LBB1_233
	0x48, 0x89, 0xc1, //0x000013d4 movq         %rax, %rcx
	0x48, 0x8b, 0x55, 0xd0, //0x000013d7 movq         $-48(%rbp), %rdx
	0x49, 0x89, 0xc9, //0x000013db movq         %rcx, %r9
	0x48, 0x39, 0xd1, //0x000013de cmpq         %rdx, %rcx
	0x0f, 0x83, 0x2e, 0xf9, 0xff, 0xff, //0x000013e1 jae          LBB1_143
	//0x000013e7 LBB1_234
	0x49, 0x8d, 0x41, 0x01, //0x000013e7 leaq         $1(%r9), %rax
	0x41, 0x0f, 0xb6, 0x09, //0x000013eb movzbl       (%r9), %ecx
	0x80, 0xf9, 0x5c, //0x000013ef cmpb         $92, %cl
	0x0f, 0x85, 0xe3, 0x00, 0x00, 0x00, //0x000013f2 jne          LBB1_247
	0x49, 0x8d, 0x49, 0x02, //0x000013f8 leaq         $2(%r9), %rcx
	0x48, 0x39, 0xd1, //0x000013fc cmpq         %rdx, %rcx
	0x0f, 0x87, 0x7b, 0x02, 0x00, 0x00, //0x000013ff ja           LBB1_258
	0x0f, 0xb6, 0x00, //0x00001405 movzbl       (%rax), %eax
	0x3c, 0x6e, //0x00001408 cmpb         $110, %al
	0x0f, 0x84, 0xe2, 0x00, 0x00, 0x00, //0x0000140a je           LBB1_249
	0x3c, 0x72, //0x00001410 cmpb         $114, %al
	0x0f, 0x84, 0xda, 0x00, 0x00, 0x00, //0x00001412 je           LBB1_249
	0x3c, 0x75, //0x00001418 cmpb         $117, %al
	0x0f, 0x85, 0x55, 0x02, 0x00, 0x00, //0x0000141a jne          LBB1_278
	0x48, 0x8b, 0x45, 0xd0, //0x00001420 movq         $-48(%rbp), %rax
	0x48, 0x29, 0xc8, //0x00001424 subq         %rcx, %rax
	0x48, 0x83, 0xf8, 0x04, //0x00001427 cmpq         $4, %rax
	0x0f, 0x8c, 0x44, 0x02, 0x00, 0x00, //0x0000142b jl           LBB1_278
	0x8b, 0x01, //0x00001431 movl         (%rcx), %eax
	0x89, 0xc2, //0x00001433 movl         %eax, %edx
	0xf7, 0xd2, //0x00001435 notl         %edx
	0x8d, 0xb0, 0xd0, 0xcf, 0xcf, 0xcf, //0x00001437 leal         $-808464432(%rax), %esi
	0x81, 0xe2, 0x80, 0x80, 0x80, 0x80, //0x0000143d andl         $-2139062144, %edx
	0x85, 0xf2, //0x00001443 testl        %esi, %edx
	0x0f, 0x85, 0x2a, 0x02, 0x00, 0x00, //0x00001445 jne          LBB1_278
	0x8d, 0xb0, 0x19, 0x19, 0x19, 0x19, //0x0000144b leal         $421075225(%rax), %esi
	0x09, 0xc6, //0x00001451 orl          %eax, %esi
	0xf7, 0xc6, 0x80, 0x80, 0x80, 0x80, //0x00001453 testl        $-2139062144, %esi
	0x0f, 0x85, 0x16, 0x02, 0x00, 0x00, //0x00001459 jne          LBB1_278
	0x89, 0xc6, //0x0000145f movl         %eax, %esi
	0x81, 0xe6, 0x7f, 0x7f, 0x7f, 0x7f, //0x00001461 andl         $2139062143, %esi
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x00001467 movl         $-1061109568, %edi
	0x29, 0xf7, //0x0000146c subl         %esi, %edi
	0x8d, 0x9e, 0x46, 0x46, 0x46, 0x46, //0x0000146e leal         $1179010630(%rsi), %ebx
	0x21, 0xd7, //0x00001474 andl         %edx, %edi
	0x85, 0xdf, //0x00001476 testl        %ebx, %edi
	0x0f, 0x85, 0xf7, 0x01, 0x00, 0x00, //0x00001478 jne          LBB1_278
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x0000147e movl         $-522133280, %edi
	0x29, 0xf7, //0x00001483 subl         %esi, %edi
	0x81, 0xc6, 0x39, 0x39, 0x39, 0x39, //0x00001485 addl         $960051513, %esi
	0x21, 0xfa, //0x0000148b andl         %edi, %edx
	0x85, 0xf2, //0x0000148d testl        %esi, %edx
	0x0f, 0x85, 0xe0, 0x01, 0x00, 0x00, //0x0000148f jne          LBB1_278
	0x0f, 0xc8, //0x00001495 bswapl       %eax
	0x89, 0xc1, //0x00001497 movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x00001499 shrl         $4, %ecx
	0xf7, 0xd1, //0x0000149c notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x0000149e andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x000014a4 leal         (%rcx,%rcx,8), %ecx
	0x25, 0x0f, 0x0f, 0x0f, 0x0f, //0x000014a7 andl         $252645135, %eax
	0x01, 0xc8, //0x000014ac addl         %ecx, %eax
	0x89, 0xc1, //0x000014ae movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x000014b0 shrl         $4, %ecx
	0x09, 0xc1, //0x000014b3 orl          %eax, %ecx
	0x89, 0xc8, //0x000014b5 movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x000014b7 shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x000014ba andl         $65280, %eax
	0x89, 0xca, //0x000014bf movl         %ecx, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x000014c1 andl         $128, %edx
	0x09, 0xc2, //0x000014c7 orl          %eax, %edx
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x000014c9 je           LBB1_246
	0xb9, 0xff, 0x00, 0x00, 0x00, //0x000014cf movl         $255, %ecx
	//0x000014d4 LBB1_246
	0x49, 0x83, 0xc1, 0x06, //0x000014d4 addq         $6, %r9
	0x4c, 0x89, 0xc8, //0x000014d8 movq         %r9, %rax
	//0x000014db LBB1_247
	0x80, 0xf9, 0x0a, //0x000014db cmpb         $10, %cl
	0x0f, 0x84, 0xf0, 0xfe, 0xff, 0xff, //0x000014de je           LBB1_233
	0x80, 0xf9, 0x0d, //0x000014e4 cmpb         $13, %cl
	0x0f, 0x84, 0xe7, 0xfe, 0xff, 0xff, //0x000014e7 je           LBB1_233
	0xe9, 0x23, 0x00, 0x00, 0x00, //0x000014ed jmp          LBB1_254
	//0x000014f2 LBB1_249
	0x48, 0x89, 0xc8, //0x000014f2 movq         %rcx, %rax
	0x48, 0x8b, 0x55, 0xd0, //0x000014f5 movq         $-48(%rbp), %rdx
	0x49, 0x89, 0xc9, //0x000014f9 movq         %rcx, %r9
	0x48, 0x39, 0xd1, //0x000014fc cmpq         %rdx, %rcx
	0x0f, 0x82, 0xe2, 0xfe, 0xff, 0xff, //0x000014ff jb           LBB1_234
	0xe9, 0x0b, 0xf8, 0xff, 0xff, //0x00001505 jmp          LBB1_143
	//0x0000150a LBB1_252
	0x48, 0x89, 0xca, //0x0000150a movq         %rcx, %rdx
	0x49, 0x89, 0xc9, //0x0000150d movq         %rcx, %r9
	0xe9, 0x00, 0xfe, 0xff, 0xff, //0x00001510 jmp          LBB1_212
	//0x00001515 LBB1_254
	0x80, 0xf9, 0x3d, //0x00001515 cmpb         $61, %cl
	0x0f, 0x85, 0x62, 0x01, 0x00, 0x00, //0x00001518 jne          LBB1_258
	0x49, 0x89, 0xc1, //0x0000151e movq         %rax, %r9
	0x41, 0x83, 0xfe, 0x04, //0x00001521 cmpl         $4, %r14d
	0x0f, 0x84, 0xea, 0xfd, 0xff, 0xff, //0x00001525 je           LBB1_212
	0x48, 0x39, 0x45, 0xd0, //0x0000152b cmpq         %rax, $-48(%rbp)
	0x0f, 0x87, 0x2a, 0x00, 0x00, 0x00, //0x0000152f ja           LBB1_261
	0xe9, 0xdb, 0xf7, 0xff, 0xff, //0x00001535 jmp          LBB1_143
	//0x0000153a LBB1_277
	0x4c, 0x89, 0xc9, //0x0000153a movq         %r9, %rcx
	0x48, 0x89, 0xc8, //0x0000153d movq         %rcx, %rax
	0x48, 0x3b, 0x4d, 0xd0, //0x00001540 cmpq         $-48(%rbp), %rcx
	0x0f, 0x82, 0x15, 0x00, 0x00, 0x00, //0x00001544 jb           LBB1_261
	0xe9, 0x2b, 0xfe, 0xff, 0xff, //0x0000154a jmp          LBB1_216
	//0x0000154f LBB1_259
	0x49, 0x89, 0xc9, //0x0000154f movq         %rcx, %r9
	0x48, 0x89, 0xc8, //0x00001552 movq         %rcx, %rax
	0x48, 0x3b, 0x4d, 0xd0, //0x00001555 cmpq         $-48(%rbp), %rcx
	0x0f, 0x83, 0x1b, 0xfe, 0xff, 0xff, //0x00001559 jae          LBB1_216
	//0x0000155f LBB1_261
	0x4c, 0x8d, 0x48, 0x01, //0x0000155f leaq         $1(%rax), %r9
	0x0f, 0xb6, 0x08, //0x00001563 movzbl       (%rax), %ecx
	0x80, 0xf9, 0x5c, //0x00001566 cmpb         $92, %cl
	0x0f, 0x85, 0xec, 0x00, 0x00, 0x00, //0x00001569 jne          LBB1_274
	0x48, 0x8d, 0x48, 0x02, //0x0000156f leaq         $2(%rax), %rcx
	0x48, 0x3b, 0x4d, 0xd0, //0x00001573 cmpq         $-48(%rbp), %rcx
	0x0f, 0x87, 0x00, 0x01, 0x00, 0x00, //0x00001577 ja           LBB1_279
	0x41, 0x0f, 0xb6, 0x11, //0x0000157d movzbl       (%r9), %edx
	0x80, 0xfa, 0x6e, //0x00001581 cmpb         $110, %dl
	0x0f, 0x84, 0xc5, 0xff, 0xff, 0xff, //0x00001584 je           LBB1_259
	0x80, 0xfa, 0x72, //0x0000158a cmpb         $114, %dl
	0x0f, 0x84, 0xbc, 0xff, 0xff, 0xff, //0x0000158d je           LBB1_259
	0x80, 0xfa, 0x75, //0x00001593 cmpb         $117, %dl
	0x0f, 0x85, 0xd9, 0x00, 0x00, 0x00, //0x00001596 jne          LBB1_278
	0x48, 0x8b, 0x55, 0xd0, //0x0000159c movq         $-48(%rbp), %rdx
	0x48, 0x29, 0xca, //0x000015a0 subq         %rcx, %rdx
	0x48, 0x83, 0xfa, 0x04, //0x000015a3 cmpq         $4, %rdx
	0x0f, 0x8c, 0xc8, 0x00, 0x00, 0x00, //0x000015a7 jl           LBB1_278
	0x8b, 0x11, //0x000015ad movl         (%rcx), %edx
	0x89, 0xd6, //0x000015af movl         %edx, %esi
	0xf7, 0xd6, //0x000015b1 notl         %esi
	0x8d, 0xba, 0xd0, 0xcf, 0xcf, 0xcf, //0x000015b3 leal         $-808464432(%rdx), %edi
	0x81, 0xe6, 0x80, 0x80, 0x80, 0x80, //0x000015b9 andl         $-2139062144, %esi
	0x85, 0xfe, //0x000015bf testl        %edi, %esi
	0x0f, 0x85, 0xae, 0x00, 0x00, 0x00, //0x000015c1 jne          LBB1_278
	0x8d, 0xba, 0x19, 0x19, 0x19, 0x19, //0x000015c7 leal         $421075225(%rdx), %edi
	0x09, 0xd7, //0x000015cd orl          %edx, %edi
	0xf7, 0xc7, 0x80, 0x80, 0x80, 0x80, //0x000015cf testl        $-2139062144, %edi
	0x0f, 0x85, 0x9a, 0x00, 0x00, 0x00, //0x000015d5 jne          LBB1_278
	0x89, 0xd7, //0x000015db movl         %edx, %edi
	0x81, 0xe7, 0x7f, 0x7f, 0x7f, 0x7f, //0x000015dd andl         $2139062143, %edi
	0xbb, 0xc0, 0xc0, 0xc0, 0xc0, //0x000015e3 movl         $-1061109568, %ebx
	0x29, 0xfb, //0x000015e8 subl         %edi, %ebx
	0x44, 0x8d, 0x87, 0x46, 0x46, 0x46, 0x46, //0x000015ea leal         $1179010630(%rdi), %r8d
	0x21, 0xf3, //0x000015f1 andl         %esi, %ebx
	0x44, 0x85, 0xc3, //0x000015f3 testl        %r8d, %ebx
	0x0f, 0x85, 0x79, 0x00, 0x00, 0x00, //0x000015f6 jne          LBB1_278
	0xbb, 0xe0, 0xe0, 0xe0, 0xe0, //0x000015fc movl         $-522133280, %ebx
	0x29, 0xfb, //0x00001601 subl         %edi, %ebx
	0x81, 0xc7, 0x39, 0x39, 0x39, 0x39, //0x00001603 addl         $960051513, %edi
	0x21, 0xde, //0x00001609 andl         %ebx, %esi
	0x85, 0xfe, //0x0000160b testl        %edi, %esi
	0x0f, 0x85, 0x62, 0x00, 0x00, 0x00, //0x0000160d jne          LBB1_278
	0x0f, 0xca, //0x00001613 bswapl       %edx
	0x89, 0xd1, //0x00001615 movl         %edx, %ecx
	0xc1, 0xe9, 0x04, //0x00001617 shrl         $4, %ecx
	0xf7, 0xd1, //0x0000161a notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x0000161c andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x00001622 leal         (%rcx,%rcx,8), %ecx
	0x81, 0xe2, 0x0f, 0x0f, 0x0f, 0x0f, //0x00001625 andl         $252645135, %edx
	0x01, 0xca, //0x0000162b addl         %ecx, %edx
	0x89, 0xd1, //0x0000162d movl         %edx, %ecx
	0xc1, 0xe9, 0x04, //0x0000162f shrl         $4, %ecx
	0x09, 0xd1, //0x00001632 orl          %edx, %ecx
	0x89, 0xca, //0x00001634 movl         %ecx, %edx
	0xc1, 0xea, 0x08, //0x00001636 shrl         $8, %edx
	0x81, 0xe2, 0x00, 0xff, 0x00, 0x00, //0x00001639 andl         $65280, %edx
	0x89, 0xce, //0x0000163f movl         %ecx, %esi
	0x81, 0xe6, 0x80, 0x00, 0x00, 0x00, //0x00001641 andl         $128, %esi
	0x09, 0xd6, //0x00001647 orl          %edx, %esi
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x00001649 je           LBB1_273
	0xb9, 0xff, 0x00, 0x00, 0x00, //0x0000164f movl         $255, %ecx
	//0x00001654 LBB1_273
	0x48, 0x83, 0xc0, 0x06, //0x00001654 addq         $6, %rax
	0x49, 0x89, 0xc1, //0x00001658 movq         %rax, %r9
	//0x0000165b LBB1_274
	0x80, 0xf9, 0x0a, //0x0000165b cmpb         $10, %cl
	0x0f, 0x84, 0xd6, 0xfe, 0xff, 0xff, //0x0000165e je           LBB1_277
	0x80, 0xf9, 0x0d, //0x00001664 cmpb         $13, %cl
	0x0f, 0x84, 0xcd, 0xfe, 0xff, 0xff, //0x00001667 je           LBB1_277
	0x80, 0xf9, 0x3d, //0x0000166d cmpb         $61, %cl
	0xe9, 0xa0, 0xfc, 0xff, 0xff, //0x00001670 jmp          LBB1_212
	//0x00001675 LBB1_278
	0x49, 0x89, 0xc9, //0x00001675 movq         %rcx, %r9
	0xe9, 0x98, 0xfc, 0xff, 0xff, //0x00001678 jmp          LBB1_212
	//0x0000167d LBB1_279
	0x4c, 0x89, 0xc8, //0x0000167d movq         %r9, %rax
	//0x00001680 LBB1_258
	0x49, 0x89, 0xc1, //0x00001680 movq         %rax, %r9
	0xe9, 0x8d, 0xfc, 0xff, 0xff, //0x00001683 jmp          LBB1_212
	//0x00001688 LBB1_282
	0x31, 0xc0, //0x00001688 xorl         %eax, %eax
	0xe9, 0x39, 0x33, 0x00, 0x00, //0x0000168a jmp          LBB1_1139
	//0x0000168f LBB1_283
	0x48, 0x8b, 0x45, 0x90, //0x0000168f movq         $-112(%rbp), %rax
	0x4c, 0x8d, 0x60, 0xe0, //0x00001693 leaq         $-32(%rax), %r12
	0x48, 0x8b, 0x85, 0x78, 0xff, 0xff, 0xff, //0x00001697 movq         $-136(%rbp), %rax
	0x49, 0x39, 0xc4, //0x0000169e cmpq         %rax, %r12
	0x0f, 0x82, 0xad, 0x22, 0x00, 0x00, //0x000016a1 jb           LBB1_854
	0x4c, 0x8d, 0x53, 0xe0, //0x000016a7 leaq         $-32(%rbx), %r10
	0x48, 0x89, 0x45, 0xb0, //0x000016ab movq         %rax, $-80(%rbp)
	0x48, 0x8b, 0x45, 0xa8, //0x000016af movq         $-88(%rbp), %rax
	0x49, 0x89, 0xc7, //0x000016b3 movq         %rax, %r15
	0x49, 0x39, 0xc2, //0x000016b6 cmpq         %rax, %r10
	0x0f, 0x82, 0xf9, 0xee, 0xff, 0xff, //0x000016b9 jb           LBB1_2
	0xc4, 0xc1, 0x7e, 0x6f, 0x41, 0x20, //0x000016bf vmovdqu      $32(%r9), %ymm0
	0x4c, 0x8b, 0x6d, 0xa8, //0x000016c5 movq         $-88(%rbp), %r13
	0x48, 0x8b, 0x45, 0x88, //0x000016c9 movq         $-120(%rbp), %rax
	0x4a, 0x8d, 0x4c, 0x28, 0xfe, //0x000016cd leaq         $-2(%rax,%r13), %rcx
	0x48, 0x89, 0x4d, 0xa0, //0x000016d2 movq         %rcx, $-96(%rbp)
	0x4a, 0x8d, 0x44, 0x28, 0xfd, //0x000016d6 leaq         $-3(%rax,%r13), %rax
	0x48, 0x89, 0x45, 0x80, //0x000016db movq         %rax, $-128(%rbp)
	0xc5, 0xfd, 0x6f, 0x0d, 0x79, 0xed, 0xff, 0xff, //0x000016df vmovdqa      $-4743(%rip), %ymm1  /* LCPI1_0+0(%rip) */
	0xc5, 0xfd, 0x6f, 0x15, 0x91, 0xed, 0xff, 0xff, //0x000016e7 vmovdqa      $-4719(%rip), %ymm2  /* LCPI1_1+0(%rip) */
	0xc5, 0xe1, 0xef, 0xdb, //0x000016ef vpxor        %xmm3, %xmm3, %xmm3
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x000016f3 movabsq      $-4294967296, %r8
	0xc5, 0x7d, 0x6f, 0x2d, 0x9b, 0xed, 0xff, 0xff, //0x000016fd vmovdqa      $-4709(%rip), %ymm13  /* LCPI1_2+0(%rip) */
	0xc5, 0xfd, 0x6f, 0x2d, 0xb3, 0xed, 0xff, 0xff, //0x00001705 vmovdqa      $-4685(%rip), %ymm5  /* LCPI1_3+0(%rip) */
	0xc5, 0xfd, 0x6f, 0x35, 0xcb, 0xed, 0xff, 0xff, //0x0000170d vmovdqa      $-4661(%rip), %ymm6  /* LCPI1_4+0(%rip) */
	0xc5, 0xf9, 0x6f, 0x3d, 0x03, 0xee, 0xff, 0xff, //0x00001715 vmovdqa      $-4605(%rip), %xmm7  /* LCPI1_5+0(%rip) */
	0xc5, 0x7d, 0x6f, 0x05, 0xdb, 0xed, 0xff, 0xff, //0x0000171d vmovdqa      $-4645(%rip), %ymm8  /* LCPI1_6+0(%rip) */
	0x48, 0x8b, 0x85, 0x78, 0xff, 0xff, 0xff, //0x00001725 movq         $-136(%rbp), %rax
	0x48, 0x89, 0x45, 0xb0, //0x0000172c movq         %rax, $-80(%rbp)
	0x4d, 0x89, 0xef, //0x00001730 movq         %r13, %r15
	0xe9, 0x77, 0x00, 0x00, 0x00, //0x00001733 jmp          LBB1_286
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001738 .p2align 4, 0x90
	//0x00001740 LBB1_295
	0xc4, 0x41, 0x35, 0x74, 0x59, 0x40, //0x00001740 vpcmpeqb     $64(%r9), %ymm9, %ymm11
	0xc4, 0x41, 0x7e, 0x6f, 0x21, //0x00001746 vmovdqu      (%r9), %ymm12
	0xc4, 0x42, 0x1d, 0x00, 0xd2, //0x0000174b vpshufb      %ymm10, %ymm12, %ymm10
	0xc4, 0x43, 0x2d, 0x4c, 0x51, 0x60, 0xb0, //0x00001750 vpblendvb    %ymm11, $96(%r9), %ymm10, %ymm10
	0xc4, 0x41, 0x2d, 0xfc, 0xc9, //0x00001757 vpaddb       %ymm9, %ymm10, %ymm9
	0xc4, 0x41, 0x35, 0xdb, 0xcd, //0x0000175c vpand        %ymm13, %ymm9, %ymm9
	0xc4, 0x62, 0x35, 0x04, 0xcd, //0x00001761 vpmaddubsw   %ymm5, %ymm9, %ymm9
	0xc5, 0x35, 0xf5, 0xce, //0x00001766 vpmaddwd     %ymm6, %ymm9, %ymm9
	0xc4, 0x63, 0x7d, 0x39, 0xcc, 0x01, //0x0000176a vextracti128 $1, %ymm9, %xmm4
	0xc4, 0xe2, 0x59, 0x00, 0xe7, //0x00001770 vpshufb      %xmm7, %xmm4, %xmm4
	0xc4, 0x42, 0x35, 0x00, 0xc8, //0x00001775 vpshufb      %ymm8, %ymm9, %ymm9
	0xc4, 0xe3, 0x35, 0x02, 0xe4, 0x08, //0x0000177a vpblendd     $8, %ymm4, %ymm9, %ymm4
	0xc4, 0xe3, 0x5d, 0x02, 0xe3, 0xc0, //0x00001780 vpblendd     $192, %ymm3, %ymm4, %ymm4
	0x48, 0x8b, 0x4d, 0xb0, //0x00001786 movq         $-80(%rbp), %rcx
	0xc5, 0xfe, 0x7f, 0x21, //0x0000178a vmovdqu      %ymm4, (%rcx)
	0x49, 0x83, 0xc7, 0x20, //0x0000178e addq         $32, %r15
	0x48, 0x83, 0xc1, 0x18, //0x00001792 addq         $24, %rcx
	0x48, 0x89, 0x4d, 0xb0, //0x00001796 movq         %rcx, $-80(%rbp)
	0x49, 0x39, 0xcc, //0x0000179a cmpq         %rcx, %r12
	0x0f, 0x82, 0x15, 0xee, 0xff, 0xff, //0x0000179d jb           LBB1_2
	//0x000017a3 LBB1_506
	0x4d, 0x89, 0xfd, //0x000017a3 movq         %r15, %r13
	0x4d, 0x39, 0xfa, //0x000017a6 cmpq         %r15, %r10
	0x0f, 0x82, 0x09, 0xee, 0xff, 0xff, //0x000017a9 jb           LBB1_2
	//0x000017af LBB1_286
	0xc4, 0x41, 0x7e, 0x6f, 0x4d, 0x00, //0x000017af vmovdqu      (%r13), %ymm9
	0xc4, 0xc1, 0x2d, 0x72, 0xd1, 0x04, //0x000017b5 vpsrld       $4, %ymm9, %ymm10
	0xc5, 0x2d, 0xdb, 0xd1, //0x000017bb vpand        %ymm1, %ymm10, %ymm10
	0xc5, 0x35, 0xdb, 0xd9, //0x000017bf vpand        %ymm1, %ymm9, %ymm11
	0xc4, 0x42, 0x7d, 0x00, 0xdb, //0x000017c3 vpshufb      %ymm11, %ymm0, %ymm11
	0xc4, 0x42, 0x6d, 0x00, 0xe2, //0x000017c8 vpshufb      %ymm10, %ymm2, %ymm12
	0xc4, 0x41, 0x1d, 0xdb, 0xdb, //0x000017cd vpand        %ymm11, %ymm12, %ymm11
	0xc5, 0x25, 0x74, 0xdb, //0x000017d2 vpcmpeqb     %ymm3, %ymm11, %ymm11
	0xc4, 0xc1, 0x7d, 0xd7, 0xc3, //0x000017d6 vpmovmskb    %ymm11, %eax
	0x4c, 0x09, 0xc0, //0x000017db orq          %r8, %rax
	0x48, 0x0f, 0xbc, 0xc0, //0x000017de bsfq         %rax, %rax
	0x83, 0xf8, 0x1f, //0x000017e2 cmpl         $31, %eax
	0x0f, 0x87, 0x55, 0xff, 0xff, 0xff, //0x000017e5 ja           LBB1_295
	0x4c, 0x39, 0xfb, //0x000017eb cmpq         %r15, %rbx
	0x0f, 0x86, 0x3c, 0x02, 0x00, 0x00, //0x000017ee jbe          LBB1_320
	0xf6, 0x45, 0xbc, 0x08, //0x000017f4 testb        $8, $-68(%rbp)
	0x0f, 0x84, 0x1e, 0x00, 0x00, 0x00, //0x000017f8 je           LBB1_291
	0x4c, 0x89, 0xf8, //0x000017fe movq         %r15, %rax
	0xe9, 0xd6, 0x00, 0x00, 0x00, //0x00001801 jmp          LBB1_303
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001806 .p2align 4, 0x90
	//0x00001810 LBB1_290
	0x49, 0xff, 0xc7, //0x00001810 incq         %r15
	0x49, 0x39, 0xdf, //0x00001813 cmpq         %rbx, %r15
	0x0f, 0x83, 0x00, 0x02, 0x00, 0x00, //0x00001816 jae          LBB1_318
	//0x0000181c LBB1_291
	0x45, 0x0f, 0xb6, 0x37, //0x0000181c movzbl       (%r15), %r14d
	0x49, 0x83, 0xfe, 0x0d, //0x00001820 cmpq         $13, %r14
	0x0f, 0x84, 0xe6, 0xff, 0xff, 0xff, //0x00001824 je           LBB1_290
	0x41, 0x80, 0xfe, 0x0a, //0x0000182a cmpb         $10, %r14b
	0x0f, 0x84, 0xdc, 0xff, 0xff, 0xff, //0x0000182e je           LBB1_290
	0x48, 0x8b, 0x45, 0xc0, //0x00001834 movq         $-64(%rbp), %rax
	0x42, 0x0f, 0xb6, 0x04, 0x30, //0x00001838 movzbl       (%rax,%r14), %eax
	0x49, 0xff, 0xc7, //0x0000183d incq         %r15
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00001840 cmpl         $255, %eax
	0x0f, 0x84, 0xb0, 0x02, 0x00, 0x00, //0x00001845 je           LBB1_333
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x0000184b movl         $1, %r11d
	0x49, 0x39, 0xdf, //0x00001851 cmpq         %rbx, %r15
	0x0f, 0x82, 0x12, 0x00, 0x00, 0x00, //0x00001854 jb           LBB1_297
	0xe9, 0x4e, 0x0c, 0x00, 0x00, //0x0000185a jmp          LBB1_492
	0x90, //0x0000185f .p2align 4, 0x90
	//0x00001860 LBB1_296
	0x49, 0xff, 0xc7, //0x00001860 incq         %r15
	0x49, 0x39, 0xdf, //0x00001863 cmpq         %rbx, %r15
	0x0f, 0x83, 0x90, 0x04, 0x00, 0x00, //0x00001866 jae          LBB1_364
	//0x0000186c LBB1_297
	0x45, 0x0f, 0xb6, 0x37, //0x0000186c movzbl       (%r15), %r14d
	0x49, 0x83, 0xfe, 0x0d, //0x00001870 cmpq         $13, %r14
	0x0f, 0x84, 0xe6, 0xff, 0xff, 0xff, //0x00001874 je           LBB1_296
	0x41, 0x80, 0xfe, 0x0a, //0x0000187a cmpb         $10, %r14b
	0x0f, 0x84, 0xdc, 0xff, 0xff, 0xff, //0x0000187e je           LBB1_296
	0x89, 0x45, 0xc8, //0x00001884 movl         %eax, $-56(%rbp)
	0x48, 0x8b, 0x45, 0xc0, //0x00001887 movq         $-64(%rbp), %rax
	0x42, 0x0f, 0xb6, 0x04, 0x30, //0x0000188b movzbl       (%rax,%r14), %eax
	0x49, 0xff, 0xc7, //0x00001890 incq         %r15
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00001893 cmpl         $255, %eax
	0x0f, 0x84, 0xe0, 0x06, 0x00, 0x00, //0x00001898 je           LBB1_422
	0x8b, 0x4d, 0xc8, //0x0000189e movl         $-56(%rbp), %ecx
	0xc1, 0xe1, 0x06, //0x000018a1 shll         $6, %ecx
	0x09, 0xc1, //0x000018a4 orl          %eax, %ecx
	0x89, 0xc8, //0x000018a6 movl         %ecx, %eax
	0x41, 0xbb, 0x02, 0x00, 0x00, 0x00, //0x000018a8 movl         $2, %r11d
	0x49, 0x39, 0xdf, //0x000018ae cmpq         %rbx, %r15
	0x0f, 0x82, 0xab, 0x01, 0x00, 0x00, //0x000018b1 jb           LBB1_322
	0xe9, 0xf1, 0x0b, 0x00, 0x00, //0x000018b7 jmp          LBB1_492
	//0x000018bc LBB1_301
	0x80, 0xfa, 0x6e, //0x000018bc cmpb         $110, %dl
	0x0f, 0x85, 0xf5, 0x01, 0x00, 0x00, //0x000018bf jne          LBB1_327
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000018c5 .p2align 4, 0x90
	//0x000018d0 LBB1_302
	0x4c, 0x89, 0xf8, //0x000018d0 movq         %r15, %rax
	0x49, 0x39, 0xdf, //0x000018d3 cmpq         %rbx, %r15
	0x0f, 0x83, 0x40, 0x01, 0x00, 0x00, //0x000018d6 jae          LBB1_318
	//0x000018dc LBB1_303
	0x48, 0x8d, 0x50, 0x01, //0x000018dc leaq         $1(%rax), %rdx
	0x44, 0x0f, 0xb6, 0x30, //0x000018e0 movzbl       (%rax), %r14d
	0x41, 0x80, 0xfe, 0x5c, //0x000018e4 cmpb         $92, %r14b
	0x0f, 0x85, 0x12, 0x01, 0x00, 0x00, //0x000018e8 jne          LBB1_316
	0x4c, 0x8d, 0x78, 0x02, //0x000018ee leaq         $2(%rax), %r15
	0x41, 0xb6, 0xff, //0x000018f2 movb         $-1, %r14b
	0x49, 0x39, 0xdf, //0x000018f5 cmpq         %rbx, %r15
	0x0f, 0x87, 0xb4, 0x01, 0x00, 0x00, //0x000018f8 ja           LBB1_326
	0x0f, 0xb6, 0x12, //0x000018fe movzbl       (%rdx), %edx
	0x80, 0xfa, 0x71, //0x00001901 cmpb         $113, %dl
	0x0f, 0x8e, 0xb2, 0xff, 0xff, 0xff, //0x00001904 jle          LBB1_301
	0x80, 0xfa, 0x72, //0x0000190a cmpb         $114, %dl
	0x0f, 0x84, 0xbd, 0xff, 0xff, 0xff, //0x0000190d je           LBB1_302
	0x80, 0xfa, 0x75, //0x00001913 cmpb         $117, %dl
	0x0f, 0x85, 0xaa, 0x01, 0x00, 0x00, //0x00001916 jne          LBB1_329
	0x48, 0x8b, 0x4d, 0xd0, //0x0000191c movq         $-48(%rbp), %rcx
	0x4c, 0x29, 0xf9, //0x00001920 subq         %r15, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00001923 cmpq         $4, %rcx
	0x0f, 0x8c, 0x99, 0x01, 0x00, 0x00, //0x00001927 jl           LBB1_329
	0x41, 0x8b, 0x1f, //0x0000192d movl         (%r15), %ebx
	0x89, 0xdf, //0x00001930 movl         %ebx, %edi
	0xf7, 0xd7, //0x00001932 notl         %edi
	0x8d, 0x8b, 0xd0, 0xcf, 0xcf, 0xcf, //0x00001934 leal         $-808464432(%rbx), %ecx
	0x81, 0xe7, 0x80, 0x80, 0x80, 0x80, //0x0000193a andl         $-2139062144, %edi
	0x85, 0xcf, //0x00001940 testl        %ecx, %edi
	0x0f, 0x85, 0x7e, 0x01, 0x00, 0x00, //0x00001942 jne          LBB1_329
	0x8d, 0x8b, 0x19, 0x19, 0x19, 0x19, //0x00001948 leal         $421075225(%rbx), %ecx
	0x09, 0xd9, //0x0000194e orl          %ebx, %ecx
	0xf7, 0xc1, 0x80, 0x80, 0x80, 0x80, //0x00001950 testl        $-2139062144, %ecx
	0x0f, 0x85, 0x6a, 0x01, 0x00, 0x00, //0x00001956 jne          LBB1_329
	0x89, 0xda, //0x0000195c movl         %ebx, %edx
	0x81, 0xe2, 0x7f, 0x7f, 0x7f, 0x7f, //0x0000195e andl         $2139062143, %edx
	0xb9, 0xc0, 0xc0, 0xc0, 0xc0, //0x00001964 movl         $-1061109568, %ecx
	0x29, 0xd1, //0x00001969 subl         %edx, %ecx
	0x8d, 0xb2, 0x46, 0x46, 0x46, 0x46, //0x0000196b leal         $1179010630(%rdx), %esi
	0x21, 0xf9, //0x00001971 andl         %edi, %ecx
	0x85, 0xf1, //0x00001973 testl        %esi, %ecx
	0x0f, 0x85, 0x4b, 0x01, 0x00, 0x00, //0x00001975 jne          LBB1_329
	0xb9, 0xe0, 0xe0, 0xe0, 0xe0, //0x0000197b movl         $-522133280, %ecx
	0x29, 0xd1, //0x00001980 subl         %edx, %ecx
	0x81, 0xc2, 0x39, 0x39, 0x39, 0x39, //0x00001982 addl         $960051513, %edx
	0x21, 0xcf, //0x00001988 andl         %ecx, %edi
	0x85, 0xd7, //0x0000198a testl        %edx, %edi
	0x0f, 0x85, 0x34, 0x01, 0x00, 0x00, //0x0000198c jne          LBB1_329
	0x0f, 0xcb, //0x00001992 bswapl       %ebx
	0x89, 0xd9, //0x00001994 movl         %ebx, %ecx
	0xc1, 0xe9, 0x04, //0x00001996 shrl         $4, %ecx
	0xf7, 0xd1, //0x00001999 notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x0000199b andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x000019a1 leal         (%rcx,%rcx,8), %ecx
	0x81, 0xe3, 0x0f, 0x0f, 0x0f, 0x0f, //0x000019a4 andl         $252645135, %ebx
	0x01, 0xcb, //0x000019aa addl         %ecx, %ebx
	0x41, 0x89, 0xde, //0x000019ac movl         %ebx, %r14d
	0x41, 0xc1, 0xee, 0x04, //0x000019af shrl         $4, %r14d
	0x41, 0x09, 0xde, //0x000019b3 orl          %ebx, %r14d
	0x44, 0x89, 0xf1, //0x000019b6 movl         %r14d, %ecx
	0xc1, 0xe9, 0x08, //0x000019b9 shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x000019bc andl         $65280, %ecx
	0x44, 0x89, 0xf2, //0x000019c2 movl         %r14d, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x000019c5 andl         $128, %edx
	0x09, 0xca, //0x000019cb orl          %ecx, %edx
	0x0f, 0x84, 0x06, 0x00, 0x00, 0x00, //0x000019cd je           LBB1_315
	0x41, 0xbe, 0xff, 0x00, 0x00, 0x00, //0x000019d3 movl         $255, %r14d
	//0x000019d9 LBB1_315
	0x48, 0x83, 0xc0, 0x06, //0x000019d9 addq         $6, %rax
	0x49, 0x89, 0xc7, //0x000019dd movq         %rax, %r15
	0x48, 0x8b, 0x5d, 0xd0, //0x000019e0 movq         $-48(%rbp), %rbx
	0x41, 0x80, 0xfe, 0x0d, //0x000019e4 cmpb         $13, %r14b
	0x0f, 0x85, 0x1f, 0x00, 0x00, 0x00, //0x000019e8 jne          LBB1_317
	0xe9, 0xdd, 0xfe, 0xff, 0xff, //0x000019ee jmp          LBB1_302
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000019f3 .p2align 4, 0x90
	//0x00001a00 LBB1_316
	0x49, 0x89, 0xd7, //0x00001a00 movq         %rdx, %r15
	0x41, 0x80, 0xfe, 0x0d, //0x00001a03 cmpb         $13, %r14b
	0x0f, 0x84, 0xc3, 0xfe, 0xff, 0xff, //0x00001a07 je           LBB1_302
	//0x00001a0d LBB1_317
	0x41, 0x80, 0xfe, 0x0a, //0x00001a0d cmpb         $10, %r14b
	0x0f, 0x84, 0xb9, 0xfe, 0xff, 0xff, //0x00001a11 je           LBB1_302
	0xe9, 0xae, 0x00, 0x00, 0x00, //0x00001a17 jmp          LBB1_330
	//0x00001a1c LBB1_318
	0x31, 0xc0, //0x00001a1c xorl         %eax, %eax
	0x45, 0x31, 0xdb, //0x00001a1e xorl         %r11d, %r11d
	//0x00001a21 LBB1_319
	0x45, 0x85, 0xdb, //0x00001a21 testl        %r11d, %r11d
	0x0f, 0x85, 0x83, 0x0a, 0x00, 0x00, //0x00001a24 jne          LBB1_492
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001a2a .p2align 4, 0x90
	//0x00001a30 LBB1_320
	0x48, 0x8b, 0x4d, 0xb0, //0x00001a30 movq         $-80(%rbp), %rcx
	0x48, 0x89, 0x4d, 0xb0, //0x00001a34 movq         %rcx, $-80(%rbp)
	0x49, 0x39, 0xcc, //0x00001a38 cmpq         %rcx, %r12
	0x0f, 0x83, 0x62, 0xfd, 0xff, 0xff, //0x00001a3b jae          LBB1_506
	0xe9, 0x72, 0xeb, 0xff, 0xff, //0x00001a41 jmp          LBB1_2
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001a46 .p2align 4, 0x90
	//0x00001a50 LBB1_321
	0x49, 0xff, 0xc7, //0x00001a50 incq         %r15
	0x41, 0xbb, 0x02, 0x00, 0x00, 0x00, //0x00001a53 movl         $2, %r11d
	0x49, 0x39, 0xdf, //0x00001a59 cmpq         %rbx, %r15
	0x0f, 0x83, 0xbf, 0xff, 0xff, 0xff, //0x00001a5c jae          LBB1_319
	//0x00001a62 LBB1_322
	0x45, 0x0f, 0xb6, 0x37, //0x00001a62 movzbl       (%r15), %r14d
	0x49, 0x83, 0xfe, 0x0d, //0x00001a66 cmpq         $13, %r14
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x00001a6a je           LBB1_321
	0x41, 0x80, 0xfe, 0x0a, //0x00001a70 cmpb         $10, %r14b
	0x0f, 0x84, 0xd6, 0xff, 0xff, 0xff, //0x00001a74 je           LBB1_321
	0x89, 0x45, 0xc8, //0x00001a7a movl         %eax, $-56(%rbp)
	0x48, 0x8b, 0x45, 0xc0, //0x00001a7d movq         $-64(%rbp), %rax
	0x42, 0x0f, 0xb6, 0x04, 0x30, //0x00001a81 movzbl       (%rax,%r14), %eax
	0x49, 0xff, 0xc7, //0x00001a86 incq         %r15
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00001a89 cmpl         $255, %eax
	0x0f, 0x84, 0x67, 0x09, 0x00, 0x00, //0x00001a8e je           LBB1_458
	0x8b, 0x4d, 0xc8, //0x00001a94 movl         $-56(%rbp), %ecx
	0xc1, 0xe1, 0x06, //0x00001a97 shll         $6, %ecx
	0x09, 0xc1, //0x00001a9a orl          %eax, %ecx
	0x89, 0xc8, //0x00001a9c movl         %ecx, %eax
	0x41, 0xbb, 0x03, 0x00, 0x00, 0x00, //0x00001a9e movl         $3, %r11d
	0x49, 0x39, 0xdf, //0x00001aa4 cmpq         %rbx, %r15
	0x0f, 0x82, 0xec, 0x03, 0x00, 0x00, //0x00001aa7 jb           LBB1_387
	0xe9, 0xfb, 0x09, 0x00, 0x00, //0x00001aad jmp          LBB1_492
	//0x00001ab2 LBB1_326
	0x49, 0x89, 0xd7, //0x00001ab2 movq         %rdx, %r15
	0xe9, 0x10, 0x00, 0x00, 0x00, //0x00001ab5 jmp          LBB1_330
	//0x00001aba LBB1_327
	0x80, 0xfa, 0x2f, //0x00001aba cmpb         $47, %dl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x00001abd jne          LBB1_329
	0x41, 0x89, 0xd6, //0x00001ac3 movl         %edx, %r14d
	//0x00001ac6 LBB1_329
	0x48, 0x8b, 0x5d, 0xd0, //0x00001ac6 movq         $-48(%rbp), %rbx
	//0x00001aca LBB1_330
	0x41, 0x0f, 0xb6, 0xc6, //0x00001aca movzbl       %r14b, %eax
	0x48, 0x8b, 0x4d, 0xc0, //0x00001ace movq         $-64(%rbp), %rcx
	0x0f, 0xb6, 0x04, 0x01, //0x00001ad2 movzbl       (%rcx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00001ad6 cmpl         $255, %eax
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00001adb je           LBB1_333
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00001ae1 movl         $1, %r11d
	0x49, 0x39, 0xdf, //0x00001ae7 cmpq         %rbx, %r15
	0x0f, 0x83, 0xbd, 0x09, 0x00, 0x00, //0x00001aea jae          LBB1_492
	0x89, 0x45, 0xc8, //0x00001af0 movl         %eax, $-56(%rbp)
	0x4c, 0x89, 0xf8, //0x00001af3 movq         %r15, %rax
	0xe9, 0xd4, 0x00, 0x00, 0x00, //0x00001af6 jmp          LBB1_349
	//0x00001afb LBB1_333
	0xc7, 0x45, 0xc8, 0x00, 0x00, 0x00, 0x00, //0x00001afb movl         $0, $-56(%rbp)
	0x45, 0x31, 0xdb, //0x00001b02 xorl         %r11d, %r11d
	//0x00001b05 LBB1_334
	0xf6, 0x45, 0xbc, 0x02, //0x00001b05 testb        $2, $-68(%rbp)
	0x0f, 0x85, 0xca, 0x09, 0x00, 0x00, //0x00001b09 jne          LBB1_495
	0x41, 0x83, 0xfb, 0x02, //0x00001b0f cmpl         $2, %r11d
	0x0f, 0x82, 0xc0, 0x09, 0x00, 0x00, //0x00001b13 jb           LBB1_495
	0x41, 0x80, 0xfe, 0x3d, //0x00001b19 cmpb         $61, %r14b
	0x0f, 0x85, 0xb6, 0x09, 0x00, 0x00, //0x00001b1d jne          LBB1_495
	0xbe, 0x05, 0x00, 0x00, 0x00, //0x00001b23 movl         $5, %esi
	0x44, 0x29, 0xde, //0x00001b28 subl         %r11d, %esi
	0xf6, 0x45, 0xbc, 0x08, //0x00001b2b testb        $8, $-68(%rbp)
	0x0f, 0x85, 0xd2, 0x01, 0x00, 0x00, //0x00001b2f jne          LBB1_365
	0x4c, 0x39, 0x7d, 0xd0, //0x00001b35 cmpq         %r15, $-48(%rbp)
	0x0f, 0x86, 0x74, 0x0b, 0x00, 0x00, //0x00001b39 jbe          LBB1_499
	0x49, 0x8d, 0x57, 0x03, //0x00001b3f leaq         $3(%r15), %rdx
	0x48, 0x8b, 0x45, 0xa0, //0x00001b43 movq         $-96(%rbp), %rax
	0x4c, 0x29, 0xf8, //0x00001b47 subq         %r15, %rax
	0x49, 0x8d, 0x5f, 0x04, //0x00001b4a leaq         $4(%r15), %rbx
	0x48, 0x8b, 0x7d, 0x80, //0x00001b4e movq         $-128(%rbp), %rdi
	0x4c, 0x29, 0xff, //0x00001b52 subq         %r15, %rdi
	0xe9, 0x1f, 0x00, 0x00, 0x00, //0x00001b55 jmp          LBB1_341
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001b5a .p2align 4, 0x90
	//0x00001b60 LBB1_340
	0x49, 0xff, 0xc7, //0x00001b60 incq         %r15
	0x48, 0xff, 0xc2, //0x00001b63 incq         %rdx
	0x48, 0xff, 0xc8, //0x00001b66 decq         %rax
	0x48, 0xff, 0xc3, //0x00001b69 incq         %rbx
	0x48, 0xff, 0xcf, //0x00001b6c decq         %rdi
	0x4c, 0x39, 0x7d, 0xd0, //0x00001b6f cmpq         %r15, $-48(%rbp)
	0x0f, 0x84, 0x51, 0x05, 0x00, 0x00, //0x00001b73 je           LBB1_498
	//0x00001b79 LBB1_341
	0x41, 0x0f, 0xb6, 0x0f, //0x00001b79 movzbl       (%r15), %ecx
	0x80, 0xf9, 0x0a, //0x00001b7d cmpb         $10, %cl
	0x0f, 0x84, 0xda, 0xff, 0xff, 0xff, //0x00001b80 je           LBB1_340
	0x80, 0xf9, 0x0d, //0x00001b86 cmpb         $13, %cl
	0x0f, 0x84, 0xd1, 0xff, 0xff, 0xff, //0x00001b89 je           LBB1_340
	0x80, 0xf9, 0x3d, //0x00001b8f cmpb         $61, %cl
	0x0f, 0x85, 0x43, 0x05, 0x00, 0x00, //0x00001b92 jne          LBB1_426
	0x49, 0xff, 0xc7, //0x00001b98 incq         %r15
	0x83, 0xfe, 0x02, //0x00001b9b cmpl         $2, %esi
	0x0f, 0x84, 0x35, 0x09, 0x00, 0x00, //0x00001b9e je           LBB1_495
	0x4c, 0x39, 0x7d, 0xd0, //0x00001ba4 cmpq         %r15, $-48(%rbp)
	0x0f, 0x87, 0x41, 0x03, 0x00, 0x00, //0x00001ba8 ja           LBB1_392
	0xe9, 0x00, 0x0b, 0x00, 0x00, //0x00001bae jmp          LBB1_499
	//0x00001bb3 LBB1_346
	0x80, 0xfb, 0x6e, //0x00001bb3 cmpb         $110, %bl
	0x0f, 0x85, 0x6d, 0x03, 0x00, 0x00, //0x00001bb6 jne          LBB1_398
	//0x00001bbc LBB1_347
	0x49, 0x89, 0xd7, //0x00001bbc movq         %rdx, %r15
	0x48, 0x8b, 0x5d, 0xd0, //0x00001bbf movq         $-48(%rbp), %rbx
	//0x00001bc3 LBB1_348
	0x4c, 0x89, 0xf8, //0x00001bc3 movq         %r15, %rax
	0x49, 0x39, 0xdf, //0x00001bc6 cmpq         %rbx, %r15
	0x0f, 0x83, 0xaa, 0x02, 0x00, 0x00, //0x00001bc9 jae          LBB1_385
	//0x00001bcf LBB1_349
	0x4c, 0x8d, 0x78, 0x01, //0x00001bcf leaq         $1(%rax), %r15
	0x44, 0x0f, 0xb6, 0x30, //0x00001bd3 movzbl       (%rax), %r14d
	0x41, 0x80, 0xfe, 0x5c, //0x00001bd7 cmpb         $92, %r14b
	0x0f, 0x85, 0x02, 0x01, 0x00, 0x00, //0x00001bdb jne          LBB1_362
	0x48, 0x8d, 0x50, 0x02, //0x00001be1 leaq         $2(%rax), %rdx
	0x41, 0xb6, 0xff, //0x00001be5 movb         $-1, %r14b
	0x48, 0x39, 0xda, //0x00001be8 cmpq         %rbx, %rdx
	0x0f, 0x87, 0x55, 0x03, 0x00, 0x00, //0x00001beb ja           LBB1_401
	0x41, 0x0f, 0xb6, 0x1f, //0x00001bf1 movzbl       (%r15), %ebx
	0x80, 0xfb, 0x71, //0x00001bf5 cmpb         $113, %bl
	0x0f, 0x8e, 0xb5, 0xff, 0xff, 0xff, //0x00001bf8 jle          LBB1_346
	0x80, 0xfb, 0x72, //0x00001bfe cmpb         $114, %bl
	0x0f, 0x84, 0xb5, 0xff, 0xff, 0xff, //0x00001c01 je           LBB1_347
	0x80, 0xfb, 0x75, //0x00001c07 cmpb         $117, %bl
	0x0f, 0x85, 0x25, 0x03, 0x00, 0x00, //0x00001c0a jne          LBB1_400
	0x48, 0x8b, 0x4d, 0xd0, //0x00001c10 movq         $-48(%rbp), %rcx
	0x48, 0x29, 0xd1, //0x00001c14 subq         %rdx, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00001c17 cmpq         $4, %rcx
	0x0f, 0x8c, 0x14, 0x03, 0x00, 0x00, //0x00001c1b jl           LBB1_400
	0x8b, 0x32, //0x00001c21 movl         (%rdx), %esi
	0x89, 0xf3, //0x00001c23 movl         %esi, %ebx
	0xf7, 0xd3, //0x00001c25 notl         %ebx
	0x8d, 0x8e, 0xd0, 0xcf, 0xcf, 0xcf, //0x00001c27 leal         $-808464432(%rsi), %ecx
	0x81, 0xe3, 0x80, 0x80, 0x80, 0x80, //0x00001c2d andl         $-2139062144, %ebx
	0x85, 0xcb, //0x00001c33 testl        %ecx, %ebx
	0x0f, 0x85, 0xfa, 0x02, 0x00, 0x00, //0x00001c35 jne          LBB1_400
	0x8d, 0x8e, 0x19, 0x19, 0x19, 0x19, //0x00001c3b leal         $421075225(%rsi), %ecx
	0x09, 0xf1, //0x00001c41 orl          %esi, %ecx
	0xf7, 0xc1, 0x80, 0x80, 0x80, 0x80, //0x00001c43 testl        $-2139062144, %ecx
	0x0f, 0x85, 0xe6, 0x02, 0x00, 0x00, //0x00001c49 jne          LBB1_400
	0x89, 0xf7, //0x00001c4f movl         %esi, %edi
	0x81, 0xe7, 0x7f, 0x7f, 0x7f, 0x7f, //0x00001c51 andl         $2139062143, %edi
	0xb9, 0xc0, 0xc0, 0xc0, 0xc0, //0x00001c57 movl         $-1061109568, %ecx
	0x29, 0xf9, //0x00001c5c subl         %edi, %ecx
	0x44, 0x8d, 0x87, 0x46, 0x46, 0x46, 0x46, //0x00001c5e leal         $1179010630(%rdi), %r8d
	0x21, 0xd9, //0x00001c65 andl         %ebx, %ecx
	0x44, 0x85, 0xc1, //0x00001c67 testl        %r8d, %ecx
	0x0f, 0x85, 0xc5, 0x02, 0x00, 0x00, //0x00001c6a jne          LBB1_400
	0xb9, 0xe0, 0xe0, 0xe0, 0xe0, //0x00001c70 movl         $-522133280, %ecx
	0x29, 0xf9, //0x00001c75 subl         %edi, %ecx
	0x81, 0xc7, 0x39, 0x39, 0x39, 0x39, //0x00001c77 addl         $960051513, %edi
	0x21, 0xcb, //0x00001c7d andl         %ecx, %ebx
	0x85, 0xfb, //0x00001c7f testl        %edi, %ebx
	0x0f, 0x85, 0xae, 0x02, 0x00, 0x00, //0x00001c81 jne          LBB1_400
	0x0f, 0xce, //0x00001c87 bswapl       %esi
	0x89, 0xf1, //0x00001c89 movl         %esi, %ecx
	0xc1, 0xe9, 0x04, //0x00001c8b shrl         $4, %ecx
	0xf7, 0xd1, //0x00001c8e notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x00001c90 andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x00001c96 leal         (%rcx,%rcx,8), %ecx
	0x81, 0xe6, 0x0f, 0x0f, 0x0f, 0x0f, //0x00001c99 andl         $252645135, %esi
	0x01, 0xce, //0x00001c9f addl         %ecx, %esi
	0x41, 0x89, 0xf6, //0x00001ca1 movl         %esi, %r14d
	0x41, 0xc1, 0xee, 0x04, //0x00001ca4 shrl         $4, %r14d
	0x41, 0x09, 0xf6, //0x00001ca8 orl          %esi, %r14d
	0x44, 0x89, 0xf1, //0x00001cab movl         %r14d, %ecx
	0xc1, 0xe9, 0x08, //0x00001cae shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x00001cb1 andl         $65280, %ecx
	0x44, 0x89, 0xf2, //0x00001cb7 movl         %r14d, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00001cba andl         $128, %edx
	0x09, 0xca, //0x00001cc0 orl          %ecx, %edx
	0x0f, 0x84, 0x06, 0x00, 0x00, 0x00, //0x00001cc2 je           LBB1_361
	0x41, 0xbe, 0xff, 0x00, 0x00, 0x00, //0x00001cc8 movl         $255, %r14d
	//0x00001cce LBB1_361
	0x48, 0x83, 0xc0, 0x06, //0x00001cce addq         $6, %rax
	0x49, 0x89, 0xc7, //0x00001cd2 movq         %rax, %r15
	0x48, 0x8b, 0x5d, 0xd0, //0x00001cd5 movq         $-48(%rbp), %rbx
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00001cd9 movabsq      $-4294967296, %r8
	//0x00001ce3 LBB1_362
	0x41, 0x80, 0xfe, 0x0a, //0x00001ce3 cmpb         $10, %r14b
	0x0f, 0x84, 0xd6, 0xfe, 0xff, 0xff, //0x00001ce7 je           LBB1_348
	0x41, 0x80, 0xfe, 0x0d, //0x00001ced cmpb         $13, %r14b
	0x0f, 0x84, 0xcc, 0xfe, 0xff, 0xff, //0x00001cf1 je           LBB1_348
	0xe9, 0x4a, 0x02, 0x00, 0x00, //0x00001cf7 jmp          LBB1_401
	//0x00001cfc LBB1_364
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00001cfc movl         $1, %r11d
	0xe9, 0x1a, 0xfd, 0xff, 0xff, //0x00001d02 jmp          LBB1_319
	//0x00001d07 LBB1_365
	0x48, 0x8b, 0x5d, 0xd0, //0x00001d07 movq         $-48(%rbp), %rbx
	0x4c, 0x39, 0xfb, //0x00001d0b cmpq         %r15, %rbx
	0x0f, 0x87, 0x28, 0x00, 0x00, 0x00, //0x00001d0e ja           LBB1_368
	0xe9, 0x9a, 0x09, 0x00, 0x00, //0x00001d14 jmp          LBB1_499
	//0x00001d19 LBB1_384
	0x49, 0x89, 0xd6, //0x00001d19 movq         %rdx, %r14
	0x4d, 0x89, 0xf7, //0x00001d1c movq         %r14, %r15
	0x49, 0x39, 0xde, //0x00001d1f cmpq         %rbx, %r14
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x00001d22 jb           LBB1_368
	0xe9, 0x44, 0x01, 0x00, 0x00, //0x00001d28 jmp          LBB1_425
	//0x00001d2d LBB1_366
	0x4c, 0x89, 0xf2, //0x00001d2d movq         %r14, %rdx
	0x4d, 0x89, 0xf7, //0x00001d30 movq         %r14, %r15
	0x49, 0x39, 0xde, //0x00001d33 cmpq         %rbx, %r14
	0x0f, 0x83, 0x35, 0x01, 0x00, 0x00, //0x00001d36 jae          LBB1_425
	//0x00001d3c LBB1_368
	0x49, 0x8d, 0x57, 0x01, //0x00001d3c leaq         $1(%r15), %rdx
	0x41, 0x0f, 0xb6, 0x3f, //0x00001d40 movzbl       (%r15), %edi
	0x40, 0x80, 0xff, 0x5c, //0x00001d44 cmpb         $92, %dil
	0x0f, 0x85, 0xe9, 0x00, 0x00, 0x00, //0x00001d48 jne          LBB1_381
	0x4d, 0x8d, 0x77, 0x02, //0x00001d4e leaq         $2(%r15), %r14
	0x49, 0x39, 0xde, //0x00001d52 cmpq         %rbx, %r14
	0x0f, 0x87, 0xe2, 0x07, 0x00, 0x00, //0x00001d55 ja           LBB1_459
	0x0f, 0xb6, 0x02, //0x00001d5b movzbl       (%rdx), %eax
	0x3c, 0x6e, //0x00001d5e cmpb         $110, %al
	0x0f, 0x84, 0xc7, 0xff, 0xff, 0xff, //0x00001d60 je           LBB1_366
	0x3c, 0x72, //0x00001d66 cmpb         $114, %al
	0x0f, 0x84, 0xbf, 0xff, 0xff, 0xff, //0x00001d68 je           LBB1_366
	0x3c, 0x75, //0x00001d6e cmpb         $117, %al
	0x0f, 0x85, 0x7d, 0x06, 0x00, 0x00, //0x00001d70 jne          LBB1_479
	0x48, 0x89, 0xd8, //0x00001d76 movq         %rbx, %rax
	0x4c, 0x29, 0xf0, //0x00001d79 subq         %r14, %rax
	0x48, 0x83, 0xf8, 0x04, //0x00001d7c cmpq         $4, %rax
	0x0f, 0x8c, 0x6d, 0x06, 0x00, 0x00, //0x00001d80 jl           LBB1_479
	0x41, 0x8b, 0x06, //0x00001d86 movl         (%r14), %eax
	0x89, 0xc2, //0x00001d89 movl         %eax, %edx
	0xf7, 0xd2, //0x00001d8b notl         %edx
	0x8d, 0x88, 0xd0, 0xcf, 0xcf, 0xcf, //0x00001d8d leal         $-808464432(%rax), %ecx
	0x81, 0xe2, 0x80, 0x80, 0x80, 0x80, //0x00001d93 andl         $-2139062144, %edx
	0x85, 0xca, //0x00001d99 testl        %ecx, %edx
	0x0f, 0x85, 0x52, 0x06, 0x00, 0x00, //0x00001d9b jne          LBB1_479
	0x8d, 0x88, 0x19, 0x19, 0x19, 0x19, //0x00001da1 leal         $421075225(%rax), %ecx
	0x09, 0xc1, //0x00001da7 orl          %eax, %ecx
	0xf7, 0xc1, 0x80, 0x80, 0x80, 0x80, //0x00001da9 testl        $-2139062144, %ecx
	0x0f, 0x85, 0x3e, 0x06, 0x00, 0x00, //0x00001daf jne          LBB1_479
	0x89, 0x75, 0x98, //0x00001db5 movl         %esi, $-104(%rbp)
	0x89, 0xc7, //0x00001db8 movl         %eax, %edi
	0x81, 0xe7, 0x7f, 0x7f, 0x7f, 0x7f, //0x00001dba andl         $2139062143, %edi
	0xb9, 0xc0, 0xc0, 0xc0, 0xc0, //0x00001dc0 movl         $-1061109568, %ecx
	0x29, 0xf9, //0x00001dc5 subl         %edi, %ecx
	0x8d, 0xb7, 0x46, 0x46, 0x46, 0x46, //0x00001dc7 leal         $1179010630(%rdi), %esi
	0x21, 0xd1, //0x00001dcd andl         %edx, %ecx
	0x85, 0xf1, //0x00001dcf testl        %esi, %ecx
	0x0f, 0x85, 0x1c, 0x06, 0x00, 0x00, //0x00001dd1 jne          LBB1_479
	0xb9, 0xe0, 0xe0, 0xe0, 0xe0, //0x00001dd7 movl         $-522133280, %ecx
	0x29, 0xf9, //0x00001ddc subl         %edi, %ecx
	0x81, 0xc7, 0x39, 0x39, 0x39, 0x39, //0x00001dde addl         $960051513, %edi
	0x21, 0xca, //0x00001de4 andl         %ecx, %edx
	0x85, 0xfa, //0x00001de6 testl        %edi, %edx
	0x0f, 0x85, 0x05, 0x06, 0x00, 0x00, //0x00001de8 jne          LBB1_479
	0x0f, 0xc8, //0x00001dee bswapl       %eax
	0x89, 0xc1, //0x00001df0 movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x00001df2 shrl         $4, %ecx
	0xf7, 0xd1, //0x00001df5 notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x00001df7 andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x00001dfd leal         (%rcx,%rcx,8), %ecx
	0x25, 0x0f, 0x0f, 0x0f, 0x0f, //0x00001e00 andl         $252645135, %eax
	0x01, 0xc8, //0x00001e05 addl         %ecx, %eax
	0x89, 0xc7, //0x00001e07 movl         %eax, %edi
	0xc1, 0xef, 0x04, //0x00001e09 shrl         $4, %edi
	0x09, 0xc7, //0x00001e0c orl          %eax, %edi
	0x89, 0xf8, //0x00001e0e movl         %edi, %eax
	0xc1, 0xe8, 0x08, //0x00001e10 shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x00001e13 andl         $65280, %eax
	0x89, 0xf9, //0x00001e18 movl         %edi, %ecx
	0x81, 0xe1, 0x80, 0x00, 0x00, 0x00, //0x00001e1a andl         $128, %ecx
	0x09, 0xc1, //0x00001e20 orl          %eax, %ecx
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x00001e22 je           LBB1_380
	0xbf, 0xff, 0x00, 0x00, 0x00, //0x00001e28 movl         $255, %edi
	//0x00001e2d LBB1_380
	0x49, 0x83, 0xc7, 0x06, //0x00001e2d addq         $6, %r15
	0x4c, 0x89, 0xfa, //0x00001e31 movq         %r15, %rdx
	0x8b, 0x75, 0x98, //0x00001e34 movl         $-104(%rbp), %esi
	//0x00001e37 LBB1_381
	0x40, 0x80, 0xff, 0x0a, //0x00001e37 cmpb         $10, %dil
	0x0f, 0x84, 0xd8, 0xfe, 0xff, 0xff, //0x00001e3b je           LBB1_384
	0x40, 0x80, 0xff, 0x0d, //0x00001e41 cmpb         $13, %dil
	0x0f, 0x84, 0xce, 0xfe, 0xff, 0xff, //0x00001e45 je           LBB1_384
	0x40, 0x80, 0xff, 0x3d, //0x00001e4b cmpb         $61, %dil
	0x0f, 0x85, 0xe8, 0x06, 0x00, 0x00, //0x00001e4f jne          LBB1_459
	0x49, 0x89, 0xd7, //0x00001e55 movq         %rdx, %r15
	0x83, 0xfe, 0x02, //0x00001e58 cmpl         $2, %esi
	0x0f, 0x84, 0x78, 0x06, 0x00, 0x00, //0x00001e5b je           LBB1_495
	0x89, 0x75, 0x98, //0x00001e61 movl         %esi, $-104(%rbp)
	0x48, 0x8b, 0x75, 0xd0, //0x00001e64 movq         $-48(%rbp), %rsi
	0x48, 0x39, 0xd6, //0x00001e68 cmpq         %rdx, %rsi
	0x0f, 0x87, 0x48, 0x04, 0x00, 0x00, //0x00001e6b ja           LBB1_462
	//0x00001e71 LBB1_425
	0x49, 0x89, 0xd7, //0x00001e71 movq         %rdx, %r15
	0xe9, 0x3a, 0x08, 0x00, 0x00, //0x00001e74 jmp          LBB1_499
	//0x00001e79 LBB1_385
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00001e79 movl         $1, %r11d
	0x8b, 0x45, 0xc8, //0x00001e7f movl         $-56(%rbp), %eax
	0xe9, 0x9a, 0xfb, 0xff, 0xff, //0x00001e82 jmp          LBB1_319
	//0x00001e87 LBB1_386
	0x49, 0xff, 0xc7, //0x00001e87 incq         %r15
	0x41, 0xbb, 0x03, 0x00, 0x00, 0x00, //0x00001e8a movl         $3, %r11d
	0x49, 0x39, 0xdf, //0x00001e90 cmpq         %rbx, %r15
	0x0f, 0x83, 0x88, 0xfb, 0xff, 0xff, //0x00001e93 jae          LBB1_319
	//0x00001e99 LBB1_387
	0x45, 0x0f, 0xb6, 0x37, //0x00001e99 movzbl       (%r15), %r14d
	0x49, 0x83, 0xfe, 0x0d, //0x00001e9d cmpq         $13, %r14
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x00001ea1 je           LBB1_386
	0x41, 0x80, 0xfe, 0x0a, //0x00001ea7 cmpb         $10, %r14b
	0x0f, 0x84, 0xd6, 0xff, 0xff, 0xff, //0x00001eab je           LBB1_386
	0x89, 0x45, 0xc8, //0x00001eb1 movl         %eax, $-56(%rbp)
	0x48, 0x8b, 0x45, 0xc0, //0x00001eb4 movq         $-64(%rbp), %rax
	0x42, 0x0f, 0xb6, 0x04, 0x30, //0x00001eb8 movzbl       (%rax,%r14), %eax
	0x49, 0xff, 0xc7, //0x00001ebd incq         %r15
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00001ec0 cmpl         $255, %eax
	0x0f, 0x85, 0xd2, 0x05, 0x00, 0x00, //0x00001ec5 jne          LBB1_491
	//0x00001ecb LBB1_497
	0x41, 0xbb, 0x03, 0x00, 0x00, 0x00, //0x00001ecb movl         $3, %r11d
	0xe9, 0x2f, 0xfc, 0xff, 0xff, //0x00001ed1 jmp          LBB1_334
	//0x00001ed6 LBB1_391
	0x49, 0xff, 0xc7, //0x00001ed6 incq         %r15
	0x48, 0xff, 0xc2, //0x00001ed9 incq         %rdx
	0x48, 0xff, 0xc8, //0x00001edc decq         %rax
	0x48, 0xff, 0xc3, //0x00001edf incq         %rbx
	0x48, 0xff, 0xcf, //0x00001ee2 decq         %rdi
	0x4c, 0x39, 0x7d, 0xd0, //0x00001ee5 cmpq         %r15, $-48(%rbp)
	0x0f, 0x84, 0xdb, 0x01, 0x00, 0x00, //0x00001ee9 je           LBB1_498
	//0x00001eef LBB1_392
	0x41, 0x0f, 0xb6, 0x0f, //0x00001eef movzbl       (%r15), %ecx
	0x80, 0xf9, 0x0a, //0x00001ef3 cmpb         $10, %cl
	0x0f, 0x84, 0xda, 0xff, 0xff, 0xff, //0x00001ef6 je           LBB1_391
	0x80, 0xf9, 0x0d, //0x00001efc cmpb         $13, %cl
	0x0f, 0x84, 0xd1, 0xff, 0xff, 0xff, //0x00001eff je           LBB1_391
	0x80, 0xf9, 0x3d, //0x00001f05 cmpb         $61, %cl
	0x0f, 0x85, 0xcd, 0x01, 0x00, 0x00, //0x00001f08 jne          LBB1_426
	0x49, 0xff, 0xc7, //0x00001f0e incq         %r15
	0x83, 0xfe, 0x03, //0x00001f11 cmpl         $3, %esi
	0x0f, 0x84, 0xbf, 0x05, 0x00, 0x00, //0x00001f14 je           LBB1_495
	0x4c, 0x39, 0x7d, 0xd0, //0x00001f1a cmpq         %r15, $-48(%rbp)
	0x0f, 0x87, 0xf4, 0x04, 0x00, 0x00, //0x00001f1e ja           LBB1_481
	0xe9, 0x8a, 0x07, 0x00, 0x00, //0x00001f24 jmp          LBB1_499
	//0x00001f29 LBB1_398
	0x80, 0xfb, 0x2f, //0x00001f29 cmpb         $47, %bl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x00001f2c jne          LBB1_400
	0x41, 0x89, 0xde, //0x00001f32 movl         %ebx, %r14d
	//0x00001f35 LBB1_400
	0x49, 0x89, 0xd7, //0x00001f35 movq         %rdx, %r15
	0x48, 0x8b, 0x5d, 0xd0, //0x00001f38 movq         $-48(%rbp), %rbx
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00001f3c movabsq      $-4294967296, %r8
	//0x00001f46 LBB1_401
	0x41, 0x0f, 0xb6, 0xc6, //0x00001f46 movzbl       %r14b, %eax
	0x48, 0x8b, 0x4d, 0xc0, //0x00001f4a movq         $-64(%rbp), %rcx
	0x0f, 0xb6, 0x04, 0x01, //0x00001f4e movzbl       (%rcx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00001f52 cmpl         $255, %eax
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x00001f57 je           LBB1_422
	0x8b, 0x4d, 0xc8, //0x00001f5d movl         $-56(%rbp), %ecx
	0xc1, 0xe1, 0x06, //0x00001f60 shll         $6, %ecx
	0x09, 0xc1, //0x00001f63 orl          %eax, %ecx
	0x89, 0xc8, //0x00001f65 movl         %ecx, %eax
	0x41, 0xbb, 0x02, 0x00, 0x00, 0x00, //0x00001f67 movl         $2, %r11d
	0x49, 0x39, 0xdf, //0x00001f6d cmpq         %rbx, %r15
	0x0f, 0x83, 0x37, 0x05, 0x00, 0x00, //0x00001f70 jae          LBB1_492
	0x89, 0x45, 0xc8, //0x00001f76 movl         %eax, $-56(%rbp)
	0xe9, 0x2c, 0x00, 0x00, 0x00, //0x00001f79 jmp          LBB1_407
	//0x00001f7e LBB1_422
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00001f7e movl         $1, %r11d
	0xe9, 0x7c, 0xfb, 0xff, 0xff, //0x00001f84 jmp          LBB1_334
	//0x00001f89 LBB1_404
	0x80, 0xfa, 0x6e, //0x00001f89 cmpb         $110, %dl
	0x0f, 0x85, 0x54, 0x01, 0x00, 0x00, //0x00001f8c jne          LBB1_432
	//0x00001f92 LBB1_405
	0x48, 0x89, 0xc2, //0x00001f92 movq         %rax, %rdx
	0x8b, 0x45, 0xc8, //0x00001f95 movl         $-56(%rbp), %eax
	//0x00001f98 LBB1_406
	0x41, 0xbb, 0x02, 0x00, 0x00, 0x00, //0x00001f98 movl         $2, %r11d
	0x49, 0x89, 0xd7, //0x00001f9e movq         %rdx, %r15
	0x48, 0x39, 0xda, //0x00001fa1 cmpq         %rbx, %rdx
	0x0f, 0x83, 0x29, 0x01, 0x00, 0x00, //0x00001fa4 jae          LBB1_424
	//0x00001faa LBB1_407
	0x49, 0x8d, 0x57, 0x01, //0x00001faa leaq         $1(%r15), %rdx
	0x45, 0x0f, 0xb6, 0x37, //0x00001fae movzbl       (%r15), %r14d
	0x41, 0x80, 0xfe, 0x5c, //0x00001fb2 cmpb         $92, %r14b
	0x0f, 0x85, 0xf5, 0x00, 0x00, 0x00, //0x00001fb6 jne          LBB1_420
	0x49, 0x8d, 0x47, 0x02, //0x00001fbc leaq         $2(%r15), %rax
	0x41, 0xb6, 0xff, //0x00001fc0 movb         $-1, %r14b
	0x48, 0x39, 0xd8, //0x00001fc3 cmpq         %rbx, %rax
	0x0f, 0x87, 0x29, 0x01, 0x00, 0x00, //0x00001fc6 ja           LBB1_435
	0x0f, 0xb6, 0x12, //0x00001fcc movzbl       (%rdx), %edx
	0x80, 0xfa, 0x71, //0x00001fcf cmpb         $113, %dl
	0x0f, 0x8e, 0xb1, 0xff, 0xff, 0xff, //0x00001fd2 jle          LBB1_404
	0x80, 0xfa, 0x72, //0x00001fd8 cmpb         $114, %dl
	0x0f, 0x84, 0xb1, 0xff, 0xff, 0xff, //0x00001fdb je           LBB1_405
	0x80, 0xfa, 0x75, //0x00001fe1 cmpb         $117, %dl
	0x0f, 0x85, 0x08, 0x01, 0x00, 0x00, //0x00001fe4 jne          LBB1_434
	0x48, 0x89, 0xd9, //0x00001fea movq         %rbx, %rcx
	0x48, 0x29, 0xc1, //0x00001fed subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00001ff0 cmpq         $4, %rcx
	0x0f, 0x8c, 0xf8, 0x00, 0x00, 0x00, //0x00001ff4 jl           LBB1_434
	0x8b, 0x10, //0x00001ffa movl         (%rax), %edx
	0x89, 0xd7, //0x00001ffc movl         %edx, %edi
	0xf7, 0xd7, //0x00001ffe notl         %edi
	0x8d, 0x8a, 0xd0, 0xcf, 0xcf, 0xcf, //0x00002000 leal         $-808464432(%rdx), %ecx
	0x81, 0xe7, 0x80, 0x80, 0x80, 0x80, //0x00002006 andl         $-2139062144, %edi
	0x85, 0xcf, //0x0000200c testl        %ecx, %edi
	0x0f, 0x85, 0xde, 0x00, 0x00, 0x00, //0x0000200e jne          LBB1_434
	0x8d, 0x8a, 0x19, 0x19, 0x19, 0x19, //0x00002014 leal         $421075225(%rdx), %ecx
	0x09, 0xd1, //0x0000201a orl          %edx, %ecx
	0xf7, 0xc1, 0x80, 0x80, 0x80, 0x80, //0x0000201c testl        $-2139062144, %ecx
	0x0f, 0x85, 0xca, 0x00, 0x00, 0x00, //0x00002022 jne          LBB1_434
	0x89, 0xd6, //0x00002028 movl         %edx, %esi
	0x81, 0xe6, 0x7f, 0x7f, 0x7f, 0x7f, //0x0000202a andl         $2139062143, %esi
	0xb9, 0xc0, 0xc0, 0xc0, 0xc0, //0x00002030 movl         $-1061109568, %ecx
	0x29, 0xf1, //0x00002035 subl         %esi, %ecx
	0x8d, 0x9e, 0x46, 0x46, 0x46, 0x46, //0x00002037 leal         $1179010630(%rsi), %ebx
	0x21, 0xf9, //0x0000203d andl         %edi, %ecx
	0x85, 0xd9, //0x0000203f testl        %ebx, %ecx
	0x48, 0x8b, 0x5d, 0xd0, //0x00002041 movq         $-48(%rbp), %rbx
	0x0f, 0x85, 0xa7, 0x00, 0x00, 0x00, //0x00002045 jne          LBB1_434
	0xb9, 0xe0, 0xe0, 0xe0, 0xe0, //0x0000204b movl         $-522133280, %ecx
	0x29, 0xf1, //0x00002050 subl         %esi, %ecx
	0x81, 0xc6, 0x39, 0x39, 0x39, 0x39, //0x00002052 addl         $960051513, %esi
	0x21, 0xcf, //0x00002058 andl         %ecx, %edi
	0x85, 0xf7, //0x0000205a testl        %esi, %edi
	0x0f, 0x85, 0x90, 0x00, 0x00, 0x00, //0x0000205c jne          LBB1_434
	0x0f, 0xca, //0x00002062 bswapl       %edx
	0x89, 0xd0, //0x00002064 movl         %edx, %eax
	0xc1, 0xe8, 0x04, //0x00002066 shrl         $4, %eax
	0xf7, 0xd0, //0x00002069 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x0000206b andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00002070 leal         (%rax,%rax,8), %eax
	0x81, 0xe2, 0x0f, 0x0f, 0x0f, 0x0f, //0x00002073 andl         $252645135, %edx
	0x01, 0xc2, //0x00002079 addl         %eax, %edx
	0x41, 0x89, 0xd6, //0x0000207b movl         %edx, %r14d
	0x41, 0xc1, 0xee, 0x04, //0x0000207e shrl         $4, %r14d
	0x41, 0x09, 0xd6, //0x00002082 orl          %edx, %r14d
	0x44, 0x89, 0xf0, //0x00002085 movl         %r14d, %eax
	0xc1, 0xe8, 0x08, //0x00002088 shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x0000208b andl         $65280, %eax
	0x44, 0x89, 0xf1, //0x00002090 movl         %r14d, %ecx
	0x81, 0xe1, 0x80, 0x00, 0x00, 0x00, //0x00002093 andl         $128, %ecx
	0x09, 0xc1, //0x00002099 orl          %eax, %ecx
	0x0f, 0x84, 0x06, 0x00, 0x00, 0x00, //0x0000209b je           LBB1_419
	0x41, 0xbe, 0xff, 0x00, 0x00, 0x00, //0x000020a1 movl         $255, %r14d
	//0x000020a7 LBB1_419
	0x49, 0x83, 0xc7, 0x06, //0x000020a7 addq         $6, %r15
	0x4c, 0x89, 0xfa, //0x000020ab movq         %r15, %rdx
	0x8b, 0x45, 0xc8, //0x000020ae movl         $-56(%rbp), %eax
	//0x000020b1 LBB1_420
	0x41, 0x80, 0xfe, 0x0a, //0x000020b1 cmpb         $10, %r14b
	0x0f, 0x84, 0xdd, 0xfe, 0xff, 0xff, //0x000020b5 je           LBB1_406
	0x41, 0x80, 0xfe, 0x0d, //0x000020bb cmpb         $13, %r14b
	0x0f, 0x84, 0xd3, 0xfe, 0xff, 0xff, //0x000020bf je           LBB1_406
	0xe9, 0x2b, 0x00, 0x00, 0x00, //0x000020c5 jmp          LBB1_435
	//0x000020ca LBB1_498
	0x4c, 0x8b, 0x7d, 0xd0, //0x000020ca movq         $-48(%rbp), %r15
	0xe9, 0xe0, 0x05, 0x00, 0x00, //0x000020ce jmp          LBB1_499
	//0x000020d3 LBB1_424
	0x49, 0x89, 0xd7, //0x000020d3 movq         %rdx, %r15
	0xe9, 0x46, 0xf9, 0xff, 0xff, //0x000020d6 jmp          LBB1_319
	//0x000020db LBB1_426
	0x49, 0xff, 0xc7, //0x000020db incq         %r15
	0x4c, 0x89, 0xfb, //0x000020de movq         %r15, %rbx
	0xe9, 0xf3, 0x03, 0x00, 0x00, //0x000020e1 jmp          LBB1_495
	//0x000020e6 LBB1_432
	0x80, 0xfa, 0x2f, //0x000020e6 cmpb         $47, %dl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x000020e9 jne          LBB1_434
	0x41, 0x89, 0xd6, //0x000020ef movl         %edx, %r14d
	//0x000020f2 LBB1_434
	0x48, 0x89, 0xc2, //0x000020f2 movq         %rax, %rdx
	//0x000020f5 LBB1_435
	0x41, 0x0f, 0xb6, 0xc6, //0x000020f5 movzbl       %r14b, %eax
	0x48, 0x8b, 0x4d, 0xc0, //0x000020f9 movq         $-64(%rbp), %rcx
	0x0f, 0xb6, 0x04, 0x01, //0x000020fd movzbl       (%rcx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00002101 cmpl         $255, %eax
	0x0f, 0x84, 0x74, 0x01, 0x00, 0x00, //0x00002106 je           LBB1_456
	0x8b, 0x4d, 0xc8, //0x0000210c movl         $-56(%rbp), %ecx
	0xc1, 0xe1, 0x06, //0x0000210f shll         $6, %ecx
	0x09, 0xc1, //0x00002112 orl          %eax, %ecx
	0x89, 0xc8, //0x00002114 movl         %ecx, %eax
	0x41, 0xbb, 0x03, 0x00, 0x00, 0x00, //0x00002116 movl         $3, %r11d
	0x48, 0x39, 0xda, //0x0000211c cmpq         %rbx, %rdx
	0x0f, 0x83, 0x69, 0x01, 0x00, 0x00, //0x0000211f jae          LBB1_457
	0x89, 0x45, 0xc8, //0x00002125 movl         %eax, $-56(%rbp)
	0xe9, 0x25, 0x00, 0x00, 0x00, //0x00002128 jmp          LBB1_441
	//0x0000212d LBB1_438
	0x80, 0xfb, 0x6e, //0x0000212d cmpb         $110, %bl
	0x0f, 0x85, 0x33, 0x03, 0x00, 0x00, //0x00002130 jne          LBB1_487
	//0x00002136 LBB1_439
	0x49, 0x89, 0xc7, //0x00002136 movq         %rax, %r15
	0x48, 0x8b, 0x5d, 0xd0, //0x00002139 movq         $-48(%rbp), %rbx
	0x8b, 0x45, 0xc8, //0x0000213d movl         $-56(%rbp), %eax
	//0x00002140 LBB1_440
	0x41, 0xbb, 0x03, 0x00, 0x00, 0x00, //0x00002140 movl         $3, %r11d
	0x4c, 0x89, 0xfa, //0x00002146 movq         %r15, %rdx
	0x49, 0x39, 0xdf, //0x00002149 cmpq         %rbx, %r15
	0x0f, 0x83, 0xcf, 0xf8, 0xff, 0xff, //0x0000214c jae          LBB1_319
	//0x00002152 LBB1_441
	0x4c, 0x8d, 0x7a, 0x01, //0x00002152 leaq         $1(%rdx), %r15
	0x44, 0x0f, 0xb6, 0x32, //0x00002156 movzbl       (%rdx), %r14d
	0x41, 0x80, 0xfe, 0x5c, //0x0000215a cmpb         $92, %r14b
	0x0f, 0x85, 0x03, 0x01, 0x00, 0x00, //0x0000215e jne          LBB1_454
	0x48, 0x8d, 0x42, 0x02, //0x00002164 leaq         $2(%rdx), %rax
	0x41, 0xb6, 0xff, //0x00002168 movb         $-1, %r14b
	0x48, 0x39, 0xd8, //0x0000216b cmpq         %rbx, %rax
	0x0f, 0x87, 0x12, 0x03, 0x00, 0x00, //0x0000216e ja           LBB1_490
	0x41, 0x0f, 0xb6, 0x1f, //0x00002174 movzbl       (%r15), %ebx
	0x80, 0xfb, 0x71, //0x00002178 cmpb         $113, %bl
	0x0f, 0x8e, 0xac, 0xff, 0xff, 0xff, //0x0000217b jle          LBB1_438
	0x80, 0xfb, 0x72, //0x00002181 cmpb         $114, %bl
	0x0f, 0x84, 0xac, 0xff, 0xff, 0xff, //0x00002184 je           LBB1_439
	0x80, 0xfb, 0x75, //0x0000218a cmpb         $117, %bl
	0x0f, 0x85, 0xe2, 0x02, 0x00, 0x00, //0x0000218d jne          LBB1_489
	0x48, 0x8b, 0x4d, 0xd0, //0x00002193 movq         $-48(%rbp), %rcx
	0x48, 0x29, 0xc1, //0x00002197 subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x0000219a cmpq         $4, %rcx
	0x0f, 0x8c, 0xd1, 0x02, 0x00, 0x00, //0x0000219e jl           LBB1_489
	0x8b, 0x38, //0x000021a4 movl         (%rax), %edi
	0x89, 0xfb, //0x000021a6 movl         %edi, %ebx
	0xf7, 0xd3, //0x000021a8 notl         %ebx
	0x8d, 0x8f, 0xd0, 0xcf, 0xcf, 0xcf, //0x000021aa leal         $-808464432(%rdi), %ecx
	0x81, 0xe3, 0x80, 0x80, 0x80, 0x80, //0x000021b0 andl         $-2139062144, %ebx
	0x85, 0xcb, //0x000021b6 testl        %ecx, %ebx
	0x0f, 0x85, 0xb7, 0x02, 0x00, 0x00, //0x000021b8 jne          LBB1_489
	0x8d, 0x8f, 0x19, 0x19, 0x19, 0x19, //0x000021be leal         $421075225(%rdi), %ecx
	0x09, 0xf9, //0x000021c4 orl          %edi, %ecx
	0xf7, 0xc1, 0x80, 0x80, 0x80, 0x80, //0x000021c6 testl        $-2139062144, %ecx
	0x0f, 0x85, 0xa3, 0x02, 0x00, 0x00, //0x000021cc jne          LBB1_489
	0x89, 0xfe, //0x000021d2 movl         %edi, %esi
	0x81, 0xe6, 0x7f, 0x7f, 0x7f, 0x7f, //0x000021d4 andl         $2139062143, %esi
	0xb9, 0xc0, 0xc0, 0xc0, 0xc0, //0x000021da movl         $-1061109568, %ecx
	0x29, 0xf1, //0x000021df subl         %esi, %ecx
	0x44, 0x8d, 0x86, 0x46, 0x46, 0x46, 0x46, //0x000021e1 leal         $1179010630(%rsi), %r8d
	0x21, 0xd9, //0x000021e8 andl         %ebx, %ecx
	0x44, 0x85, 0xc1, //0x000021ea testl        %r8d, %ecx
	0x0f, 0x85, 0x82, 0x02, 0x00, 0x00, //0x000021ed jne          LBB1_489
	0xb9, 0xe0, 0xe0, 0xe0, 0xe0, //0x000021f3 movl         $-522133280, %ecx
	0x29, 0xf1, //0x000021f8 subl         %esi, %ecx
	0x81, 0xc6, 0x39, 0x39, 0x39, 0x39, //0x000021fa addl         $960051513, %esi
	0x21, 0xcb, //0x00002200 andl         %ecx, %ebx
	0x85, 0xf3, //0x00002202 testl        %esi, %ebx
	0x0f, 0x85, 0x6b, 0x02, 0x00, 0x00, //0x00002204 jne          LBB1_489
	0x0f, 0xcf, //0x0000220a bswapl       %edi
	0x89, 0xf8, //0x0000220c movl         %edi, %eax
	0xc1, 0xe8, 0x04, //0x0000220e shrl         $4, %eax
	0xf7, 0xd0, //0x00002211 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00002213 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00002218 leal         (%rax,%rax,8), %eax
	0x81, 0xe7, 0x0f, 0x0f, 0x0f, 0x0f, //0x0000221b andl         $252645135, %edi
	0x01, 0xc7, //0x00002221 addl         %eax, %edi
	0x41, 0x89, 0xfe, //0x00002223 movl         %edi, %r14d
	0x41, 0xc1, 0xee, 0x04, //0x00002226 shrl         $4, %r14d
	0x41, 0x09, 0xfe, //0x0000222a orl          %edi, %r14d
	0x44, 0x89, 0xf0, //0x0000222d movl         %r14d, %eax
	0xc1, 0xe8, 0x08, //0x00002230 shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x00002233 andl         $65280, %eax
	0x44, 0x89, 0xf1, //0x00002238 movl         %r14d, %ecx
	0x81, 0xe1, 0x80, 0x00, 0x00, 0x00, //0x0000223b andl         $128, %ecx
	0x09, 0xc1, //0x00002241 orl          %eax, %ecx
	0x0f, 0x84, 0x06, 0x00, 0x00, 0x00, //0x00002243 je           LBB1_453
	0x41, 0xbe, 0xff, 0x00, 0x00, 0x00, //0x00002249 movl         $255, %r14d
	//0x0000224f LBB1_453
	0x48, 0x83, 0xc2, 0x06, //0x0000224f addq         $6, %rdx
	0x49, 0x89, 0xd7, //0x00002253 movq         %rdx, %r15
	0x48, 0x8b, 0x5d, 0xd0, //0x00002256 movq         $-48(%rbp), %rbx
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x0000225a movabsq      $-4294967296, %r8
	0x8b, 0x45, 0xc8, //0x00002264 movl         $-56(%rbp), %eax
	//0x00002267 LBB1_454
	0x41, 0x80, 0xfe, 0x0a, //0x00002267 cmpb         $10, %r14b
	0x0f, 0x84, 0xcf, 0xfe, 0xff, 0xff, //0x0000226b je           LBB1_440
	0x41, 0x80, 0xfe, 0x0d, //0x00002271 cmpb         $13, %r14b
	0x0f, 0x84, 0xc5, 0xfe, 0xff, 0xff, //0x00002275 je           LBB1_440
	0xe9, 0x06, 0x02, 0x00, 0x00, //0x0000227b jmp          LBB1_490
	//0x00002280 LBB1_456
	0x41, 0xbb, 0x02, 0x00, 0x00, 0x00, //0x00002280 movl         $2, %r11d
	0x49, 0x89, 0xd7, //0x00002286 movq         %rdx, %r15
	0xe9, 0x77, 0xf8, 0xff, 0xff, //0x00002289 jmp          LBB1_334
	//0x0000228e LBB1_457
	0x49, 0x89, 0xd7, //0x0000228e movq         %rdx, %r15
	0xe9, 0x17, 0x02, 0x00, 0x00, //0x00002291 jmp          LBB1_492
	//0x00002296 LBB1_478
	0x4d, 0x89, 0xfe, //0x00002296 movq         %r15, %r14
	0x4c, 0x89, 0xf2, //0x00002299 movq         %r14, %rdx
	0x49, 0x39, 0xf6, //0x0000229c cmpq         %rsi, %r14
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x0000229f jb           LBB1_462
	0xe9, 0x09, 0x04, 0x00, 0x00, //0x000022a5 jmp          LBB1_499
	//0x000022aa LBB1_460
	0x4d, 0x89, 0xf7, //0x000022aa movq         %r14, %r15
	0x4c, 0x89, 0xf2, //0x000022ad movq         %r14, %rdx
	0x49, 0x39, 0xf6, //0x000022b0 cmpq         %rsi, %r14
	0x0f, 0x83, 0xfa, 0x03, 0x00, 0x00, //0x000022b3 jae          LBB1_499
	//0x000022b9 LBB1_462
	0x4c, 0x8d, 0x7a, 0x01, //0x000022b9 leaq         $1(%rdx), %r15
	0x0f, 0xb6, 0x3a, //0x000022bd movzbl       (%rdx), %edi
	0x40, 0x80, 0xff, 0x5c, //0x000022c0 cmpb         $92, %dil
	0x0f, 0x85, 0xe8, 0x00, 0x00, 0x00, //0x000022c4 jne          LBB1_475
	0x4c, 0x8d, 0x72, 0x02, //0x000022ca leaq         $2(%rdx), %r14
	0x49, 0x39, 0xf6, //0x000022ce cmpq         %rsi, %r14
	0x0f, 0x87, 0x63, 0x02, 0x00, 0x00, //0x000022d1 ja           LBB1_517
	0x41, 0x0f, 0xb6, 0x07, //0x000022d7 movzbl       (%r15), %eax
	0x3c, 0x6e, //0x000022db cmpb         $110, %al
	0x0f, 0x84, 0xc7, 0xff, 0xff, 0xff, //0x000022dd je           LBB1_460
	0x3c, 0x72, //0x000022e3 cmpb         $114, %al
	0x0f, 0x84, 0xbf, 0xff, 0xff, 0xff, //0x000022e5 je           LBB1_460
	0x3c, 0x75, //0x000022eb cmpb         $117, %al
	0x0f, 0x85, 0x00, 0x01, 0x00, 0x00, //0x000022ed jne          LBB1_479
	0x48, 0x89, 0xf0, //0x000022f3 movq         %rsi, %rax
	0x4c, 0x29, 0xf0, //0x000022f6 subq         %r14, %rax
	0x48, 0x83, 0xf8, 0x04, //0x000022f9 cmpq         $4, %rax
	0x0f, 0x8c, 0xf0, 0x00, 0x00, 0x00, //0x000022fd jl           LBB1_479
	0x41, 0x8b, 0x06, //0x00002303 movl         (%r14), %eax
	0x89, 0xc7, //0x00002306 movl         %eax, %edi
	0xf7, 0xd7, //0x00002308 notl         %edi
	0x8d, 0x88, 0xd0, 0xcf, 0xcf, 0xcf, //0x0000230a leal         $-808464432(%rax), %ecx
	0x81, 0xe7, 0x80, 0x80, 0x80, 0x80, //0x00002310 andl         $-2139062144, %edi
	0x85, 0xcf, //0x00002316 testl        %ecx, %edi
	0x0f, 0x85, 0xd5, 0x00, 0x00, 0x00, //0x00002318 jne          LBB1_479
	0x8d, 0x88, 0x19, 0x19, 0x19, 0x19, //0x0000231e leal         $421075225(%rax), %ecx
	0x09, 0xc1, //0x00002324 orl          %eax, %ecx
	0xf7, 0xc1, 0x80, 0x80, 0x80, 0x80, //0x00002326 testl        $-2139062144, %ecx
	0x0f, 0x85, 0xc1, 0x00, 0x00, 0x00, //0x0000232c jne          LBB1_479
	0x89, 0xc6, //0x00002332 movl         %eax, %esi
	0x81, 0xe6, 0x7f, 0x7f, 0x7f, 0x7f, //0x00002334 andl         $2139062143, %esi
	0xb9, 0xc0, 0xc0, 0xc0, 0xc0, //0x0000233a movl         $-1061109568, %ecx
	0x29, 0xf1, //0x0000233f subl         %esi, %ecx
	0x8d, 0x9e, 0x46, 0x46, 0x46, 0x46, //0x00002341 leal         $1179010630(%rsi), %ebx
	0x21, 0xf9, //0x00002347 andl         %edi, %ecx
	0x85, 0xd9, //0x00002349 testl        %ebx, %ecx
	0x0f, 0x85, 0xa2, 0x00, 0x00, 0x00, //0x0000234b jne          LBB1_479
	0xb9, 0xe0, 0xe0, 0xe0, 0xe0, //0x00002351 movl         $-522133280, %ecx
	0x29, 0xf1, //0x00002356 subl         %esi, %ecx
	0x81, 0xc6, 0x39, 0x39, 0x39, 0x39, //0x00002358 addl         $960051513, %esi
	0x21, 0xcf, //0x0000235e andl         %ecx, %edi
	0x85, 0xf7, //0x00002360 testl        %esi, %edi
	0x48, 0x8b, 0x75, 0xd0, //0x00002362 movq         $-48(%rbp), %rsi
	0x0f, 0x85, 0x87, 0x00, 0x00, 0x00, //0x00002366 jne          LBB1_479
	0x0f, 0xc8, //0x0000236c bswapl       %eax
	0x89, 0xc1, //0x0000236e movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x00002370 shrl         $4, %ecx
	0xf7, 0xd1, //0x00002373 notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x00002375 andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x0000237b leal         (%rcx,%rcx,8), %ecx
	0x25, 0x0f, 0x0f, 0x0f, 0x0f, //0x0000237e andl         $252645135, %eax
	0x01, 0xc8, //0x00002383 addl         %ecx, %eax
	0x89, 0xc7, //0x00002385 movl         %eax, %edi
	0xc1, 0xef, 0x04, //0x00002387 shrl         $4, %edi
	0x09, 0xc7, //0x0000238a orl          %eax, %edi
	0x89, 0xf8, //0x0000238c movl         %edi, %eax
	0xc1, 0xe8, 0x08, //0x0000238e shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x00002391 andl         $65280, %eax
	0x89, 0xf9, //0x00002396 movl         %edi, %ecx
	0x81, 0xe1, 0x80, 0x00, 0x00, 0x00, //0x00002398 andl         $128, %ecx
	0x09, 0xc1, //0x0000239e orl          %eax, %ecx
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x000023a0 je           LBB1_474
	0xbf, 0xff, 0x00, 0x00, 0x00, //0x000023a6 movl         $255, %edi
	//0x000023ab LBB1_474
	0x48, 0x83, 0xc2, 0x06, //0x000023ab addq         $6, %rdx
	0x49, 0x89, 0xd7, //0x000023af movq         %rdx, %r15
	//0x000023b2 LBB1_475
	0x40, 0x80, 0xff, 0x0a, //0x000023b2 cmpb         $10, %dil
	0x0f, 0x84, 0xda, 0xfe, 0xff, 0xff, //0x000023b6 je           LBB1_478
	0x40, 0x80, 0xff, 0x0d, //0x000023bc cmpb         $13, %dil
	0x0f, 0x84, 0xd0, 0xfe, 0xff, 0xff, //0x000023c0 je           LBB1_478
	0x40, 0x80, 0xff, 0x3d, //0x000023c6 cmpb         $61, %dil
	0x0f, 0x85, 0x09, 0x01, 0x00, 0x00, //0x000023ca jne          LBB1_495
	0x83, 0x7d, 0x98, 0x03, //0x000023d0 cmpl         $3, $-104(%rbp)
	0x0f, 0x84, 0xff, 0x00, 0x00, 0x00, //0x000023d4 je           LBB1_495
	0x4c, 0x39, 0x7d, 0xd0, //0x000023da cmpq         %r15, $-48(%rbp)
	0x0f, 0x87, 0x86, 0x01, 0x00, 0x00, //0x000023de ja           LBB1_522
	//0x000023e4 LBB1_511
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x000023e4 movabsq      $-4294967296, %r8
	0xe9, 0xc0, 0x02, 0x00, 0x00, //0x000023ee jmp          LBB1_499
	//0x000023f3 LBB1_479
	0x4d, 0x89, 0xf7, //0x000023f3 movq         %r14, %r15
	0xe9, 0xde, 0x00, 0x00, 0x00, //0x000023f6 jmp          LBB1_495
	//0x000023fb LBB1_458
	0x41, 0xbb, 0x02, 0x00, 0x00, 0x00, //0x000023fb movl         $2, %r11d
	0xe9, 0xff, 0xf6, 0xff, 0xff, //0x00002401 jmp          LBB1_334
	//0x00002406 LBB1_480
	0x48, 0xff, 0xc2, //0x00002406 incq         %rdx
	0x48, 0xff, 0xc3, //0x00002409 incq         %rbx
	0x48, 0xff, 0xcf, //0x0000240c decq         %rdi
	0x48, 0xff, 0xc8, //0x0000240f decq         %rax
	0x0f, 0x84, 0x8d, 0x02, 0x00, 0x00, //0x00002412 je           LBB1_519
	//0x00002418 LBB1_481
	0x0f, 0xb6, 0x4a, 0xff, //0x00002418 movzbl       $-1(%rdx), %ecx
	0x80, 0xf9, 0x0a, //0x0000241c cmpb         $10, %cl
	0x0f, 0x84, 0xe1, 0xff, 0xff, 0xff, //0x0000241f je           LBB1_480
	0x80, 0xf9, 0x0d, //0x00002425 cmpb         $13, %cl
	0x0f, 0x84, 0xd8, 0xff, 0xff, 0xff, //0x00002428 je           LBB1_480
	0x80, 0xf9, 0x3d, //0x0000242e cmpb         $61, %cl
	0x0f, 0x85, 0xe5, 0x02, 0x00, 0x00, //0x00002431 jne          LBB1_539
	0x49, 0x89, 0xd7, //0x00002437 movq         %rdx, %r15
	0x83, 0xfe, 0x04, //0x0000243a cmpl         $4, %esi
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x0000243d movabsq      $-4294967296, %r8
	0x0f, 0x84, 0x8c, 0x00, 0x00, 0x00, //0x00002447 je           LBB1_495
	0x48, 0x39, 0x55, 0xd0, //0x0000244d cmpq         %rdx, $-48(%rbp)
	0x0f, 0x87, 0xbf, 0x00, 0x00, 0x00, //0x00002451 ja           LBB1_513
	//0x00002457 LBB1_486
	0x49, 0x89, 0xd7, //0x00002457 movq         %rdx, %r15
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x0000245a movabsq      $-4294967296, %r8
	0xe9, 0x4a, 0x02, 0x00, 0x00, //0x00002464 jmp          LBB1_499
	//0x00002469 LBB1_487
	0x80, 0xfb, 0x2f, //0x00002469 cmpb         $47, %bl
	0x0f, 0x85, 0x03, 0x00, 0x00, 0x00, //0x0000246c jne          LBB1_489
	0x41, 0x89, 0xde, //0x00002472 movl         %ebx, %r14d
	//0x00002475 LBB1_489
	0x49, 0x89, 0xc7, //0x00002475 movq         %rax, %r15
	0x48, 0x8b, 0x5d, 0xd0, //0x00002478 movq         $-48(%rbp), %rbx
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x0000247c movabsq      $-4294967296, %r8
	//0x00002486 LBB1_490
	0x41, 0x0f, 0xb6, 0xc6, //0x00002486 movzbl       %r14b, %eax
	0x48, 0x8b, 0x4d, 0xc0, //0x0000248a movq         $-64(%rbp), %rcx
	0x0f, 0xb6, 0x04, 0x01, //0x0000248e movzbl       (%rcx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00002492 cmpl         $255, %eax
	0x0f, 0x84, 0x2e, 0xfa, 0xff, 0xff, //0x00002497 je           LBB1_497
	//0x0000249d LBB1_491
	0x8b, 0x4d, 0xc8, //0x0000249d movl         $-56(%rbp), %ecx
	0xc1, 0xe1, 0x06, //0x000024a0 shll         $6, %ecx
	0x09, 0xc1, //0x000024a3 orl          %eax, %ecx
	0x89, 0xc8, //0x000024a5 movl         %ecx, %eax
	0x41, 0xbb, 0x04, 0x00, 0x00, 0x00, //0x000024a7 movl         $4, %r11d
	//0x000024ad LBB1_492
	0x89, 0x45, 0xc8, //0x000024ad movl         %eax, $-56(%rbp)
	0xf6, 0x45, 0xbc, 0x02, //0x000024b0 testb        $2, $-68(%rbp)
	0x0f, 0x94, 0xc0, //0x000024b4 sete         %al
	0x41, 0x83, 0xfb, 0x01, //0x000024b7 cmpl         $1, %r11d
	0x0f, 0x94, 0xc1, //0x000024bb sete         %cl
	0x49, 0x39, 0xdf, //0x000024be cmpq         %rbx, %r15
	0x0f, 0x82, 0xec, 0x01, 0x00, 0x00, //0x000024c1 jb           LBB1_499
	0x41, 0x83, 0xfb, 0x04, //0x000024c7 cmpl         $4, %r11d
	0x0f, 0x84, 0xe2, 0x01, 0x00, 0x00, //0x000024cb je           LBB1_499
	0x08, 0xc8, //0x000024d1 orb          %cl, %al
	0x0f, 0x84, 0xda, 0x01, 0x00, 0x00, //0x000024d3 je           LBB1_499
	//0x000024d9 LBB1_495
	0x49, 0x8d, 0x4f, 0x01, //0x000024d9 leaq         $1(%r15), %rcx
	0x48, 0x8b, 0x5d, 0xd0, //0x000024dd movq         $-48(%rbp), %rbx
	0x4c, 0x39, 0xfb, //0x000024e1 cmpq         %r15, %rbx
	0x49, 0x0f, 0x45, 0xcf, //0x000024e4 cmovneq      %r15, %rcx
	0x4c, 0x29, 0xe9, //0x000024e8 subq         %r13, %rcx
	0x0f, 0x85, 0xf8, 0x24, 0x00, 0x00, //0x000024eb jne          LBB1_1141
	0x4d, 0x89, 0xef, //0x000024f1 movq         %r13, %r15
	0x48, 0x8b, 0x4d, 0xb0, //0x000024f4 movq         $-80(%rbp), %rcx
	0x48, 0x89, 0x4d, 0xb0, //0x000024f8 movq         %rcx, $-80(%rbp)
	0x49, 0x39, 0xcc, //0x000024fc cmpq         %rcx, %r12
	0x0f, 0x83, 0x9e, 0xf2, 0xff, 0xff, //0x000024ff jae          LBB1_506
	0xe9, 0xae, 0xe0, 0xff, 0xff, //0x00002505 jmp          LBB1_2
	//0x0000250a LBB1_512
	0x48, 0xff, 0xc3, //0x0000250a incq         %rbx
	0x48, 0xff, 0xcf, //0x0000250d decq         %rdi
	0x0f, 0x84, 0x8f, 0x01, 0x00, 0x00, //0x00002510 je           LBB1_519
	//0x00002516 LBB1_513
	0x0f, 0xb6, 0x43, 0xff, //0x00002516 movzbl       $-1(%rbx), %eax
	0x3c, 0x0a, //0x0000251a cmpb         $10, %al
	0x0f, 0x84, 0xe8, 0xff, 0xff, 0xff, //0x0000251c je           LBB1_512
	0x3c, 0x0d, //0x00002522 cmpb         $13, %al
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x00002524 je           LBB1_512
	0x3c, 0x3d, //0x0000252a cmpb         $61, %al
	0x0f, 0x85, 0x64, 0x03, 0x00, 0x00, //0x0000252c jne          LBB1_545
	0x49, 0x89, 0xdf, //0x00002532 movq         %rbx, %r15
	0xe9, 0x71, 0x03, 0x00, 0x00, //0x00002535 jmp          LBB1_567
	//0x0000253a LBB1_517
	0x4c, 0x89, 0xfa, //0x0000253a movq         %r15, %rdx
	//0x0000253d LBB1_459
	0x49, 0x89, 0xd7, //0x0000253d movq         %rdx, %r15
	0xe9, 0x94, 0xff, 0xff, 0xff, //0x00002540 jmp          LBB1_495
	//0x00002545 LBB1_538
	0x49, 0x89, 0xd6, //0x00002545 movq         %rdx, %r14
	0x4d, 0x89, 0xf7, //0x00002548 movq         %r14, %r15
	0x4c, 0x3b, 0x75, 0xd0, //0x0000254b cmpq         $-48(%rbp), %r14
	0x0f, 0x82, 0x15, 0x00, 0x00, 0x00, //0x0000254f jb           LBB1_522
	0xe9, 0xfd, 0xfe, 0xff, 0xff, //0x00002555 jmp          LBB1_486
	//0x0000255a LBB1_520
	0x4c, 0x89, 0xf2, //0x0000255a movq         %r14, %rdx
	0x4d, 0x89, 0xf7, //0x0000255d movq         %r14, %r15
	0x4c, 0x3b, 0x75, 0xd0, //0x00002560 cmpq         $-48(%rbp), %r14
	0x0f, 0x83, 0xed, 0xfe, 0xff, 0xff, //0x00002564 jae          LBB1_486
	//0x0000256a LBB1_522
	0x49, 0x8d, 0x57, 0x01, //0x0000256a leaq         $1(%r15), %rdx
	0x41, 0x0f, 0xb6, 0x3f, //0x0000256e movzbl       (%r15), %edi
	0x40, 0x80, 0xff, 0x5c, //0x00002572 cmpb         $92, %dil
	0x0f, 0x85, 0xe5, 0x00, 0x00, 0x00, //0x00002576 jne          LBB1_535
	0x4d, 0x8d, 0x77, 0x02, //0x0000257c leaq         $2(%r15), %r14
	0x4c, 0x3b, 0x75, 0xd0, //0x00002580 cmpq         $-48(%rbp), %r14
	0x0f, 0x87, 0xa7, 0x01, 0x00, 0x00, //0x00002584 ja           LBB1_543
	0x0f, 0xb6, 0x02, //0x0000258a movzbl       (%rdx), %eax
	0x3c, 0x6e, //0x0000258d cmpb         $110, %al
	0x0f, 0x84, 0xc5, 0xff, 0xff, 0xff, //0x0000258f je           LBB1_520
	0x3c, 0x72, //0x00002595 cmpb         $114, %al
	0x0f, 0x84, 0xbd, 0xff, 0xff, 0xff, //0x00002597 je           LBB1_520
	0x3c, 0x75, //0x0000259d cmpb         $117, %al
	0x0f, 0x85, 0xdf, 0x02, 0x00, 0x00, //0x0000259f jne          LBB1_544
	0x48, 0x8b, 0x45, 0xd0, //0x000025a5 movq         $-48(%rbp), %rax
	0x4c, 0x29, 0xf0, //0x000025a9 subq         %r14, %rax
	0x48, 0x83, 0xf8, 0x04, //0x000025ac cmpq         $4, %rax
	0x0f, 0x8c, 0xc6, 0x02, 0x00, 0x00, //0x000025b0 jl           LBB1_566
	0x41, 0x8b, 0x06, //0x000025b6 movl         (%r14), %eax
	0x89, 0xc2, //0x000025b9 movl         %eax, %edx
	0xf7, 0xd2, //0x000025bb notl         %edx
	0x8d, 0x88, 0xd0, 0xcf, 0xcf, 0xcf, //0x000025bd leal         $-808464432(%rax), %ecx
	0x81, 0xe2, 0x80, 0x80, 0x80, 0x80, //0x000025c3 andl         $-2139062144, %edx
	0x85, 0xca, //0x000025c9 testl        %ecx, %edx
	0x0f, 0x85, 0xab, 0x02, 0x00, 0x00, //0x000025cb jne          LBB1_566
	0x8d, 0x88, 0x19, 0x19, 0x19, 0x19, //0x000025d1 leal         $421075225(%rax), %ecx
	0x09, 0xc1, //0x000025d7 orl          %eax, %ecx
	0xf7, 0xc1, 0x80, 0x80, 0x80, 0x80, //0x000025d9 testl        $-2139062144, %ecx
	0x0f, 0x85, 0x97, 0x02, 0x00, 0x00, //0x000025df jne          LBB1_566
	0x89, 0xc6, //0x000025e5 movl         %eax, %esi
	0x81, 0xe6, 0x7f, 0x7f, 0x7f, 0x7f, //0x000025e7 andl         $2139062143, %esi
	0xb9, 0xc0, 0xc0, 0xc0, 0xc0, //0x000025ed movl         $-1061109568, %ecx
	0x29, 0xf1, //0x000025f2 subl         %esi, %ecx
	0x8d, 0xbe, 0x46, 0x46, 0x46, 0x46, //0x000025f4 leal         $1179010630(%rsi), %edi
	0x21, 0xd1, //0x000025fa andl         %edx, %ecx
	0x85, 0xf9, //0x000025fc testl        %edi, %ecx
	0x0f, 0x85, 0x78, 0x02, 0x00, 0x00, //0x000025fe jne          LBB1_566
	0xb9, 0xe0, 0xe0, 0xe0, 0xe0, //0x00002604 movl         $-522133280, %ecx
	0x29, 0xf1, //0x00002609 subl         %esi, %ecx
	0x81, 0xc6, 0x39, 0x39, 0x39, 0x39, //0x0000260b addl         $960051513, %esi
	0x21, 0xca, //0x00002611 andl         %ecx, %edx
	0x85, 0xf2, //0x00002613 testl        %esi, %edx
	0x0f, 0x85, 0x61, 0x02, 0x00, 0x00, //0x00002615 jne          LBB1_566
	0x0f, 0xc8, //0x0000261b bswapl       %eax
	0x89, 0xc1, //0x0000261d movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x0000261f shrl         $4, %ecx
	0xf7, 0xd1, //0x00002622 notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x00002624 andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x0000262a leal         (%rcx,%rcx,8), %ecx
	0x25, 0x0f, 0x0f, 0x0f, 0x0f, //0x0000262d andl         $252645135, %eax
	0x01, 0xc8, //0x00002632 addl         %ecx, %eax
	0x89, 0xc7, //0x00002634 movl         %eax, %edi
	0xc1, 0xef, 0x04, //0x00002636 shrl         $4, %edi
	0x09, 0xc7, //0x00002639 orl          %eax, %edi
	0x89, 0xf8, //0x0000263b movl         %edi, %eax
	0xc1, 0xe8, 0x08, //0x0000263d shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x00002640 andl         $65280, %eax
	0x89, 0xf9, //0x00002645 movl         %edi, %ecx
	0x81, 0xe1, 0x80, 0x00, 0x00, 0x00, //0x00002647 andl         $128, %ecx
	0x09, 0xc1, //0x0000264d orl          %eax, %ecx
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x0000264f je           LBB1_534
	0xbf, 0xff, 0x00, 0x00, 0x00, //0x00002655 movl         $255, %edi
	//0x0000265a LBB1_534
	0x49, 0x83, 0xc7, 0x06, //0x0000265a addq         $6, %r15
	0x4c, 0x89, 0xfa, //0x0000265e movq         %r15, %rdx
	//0x00002661 LBB1_535
	0x40, 0x80, 0xff, 0x0a, //0x00002661 cmpb         $10, %dil
	0x0f, 0x84, 0xda, 0xfe, 0xff, 0xff, //0x00002665 je           LBB1_538
	0x40, 0x80, 0xff, 0x0d, //0x0000266b cmpb         $13, %dil
	0x0f, 0x84, 0xd0, 0xfe, 0xff, 0xff, //0x0000266f je           LBB1_538
	0x40, 0x80, 0xff, 0x3d, //0x00002675 cmpb         $61, %dil
	0x0f, 0x85, 0x29, 0x02, 0x00, 0x00, //0x00002679 jne          LBB1_546
	0x49, 0x89, 0xd7, //0x0000267f movq         %rdx, %r15
	0x83, 0x7d, 0x98, 0x04, //0x00002682 cmpl         $4, $-104(%rbp)
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00002686 movabsq      $-4294967296, %r8
	0x0f, 0x84, 0x43, 0xfe, 0xff, 0xff, //0x00002690 je           LBB1_495
	0x48, 0x39, 0x55, 0xd0, //0x00002696 cmpq         %rdx, $-48(%rbp)
	0x0f, 0x87, 0xc8, 0x00, 0x00, 0x00, //0x0000269a ja           LBB1_549
	0xe9, 0xb2, 0xfd, 0xff, 0xff, //0x000026a0 jmp          LBB1_486
	//0x000026a5 LBB1_519
	0x4c, 0x8b, 0x7d, 0xd0, //0x000026a5 movq         $-48(%rbp), %r15
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x000026a9 movabsq      $-4294967296, %r8
	//0x000026b3 LBB1_499
	0xb0, 0x04, //0x000026b3 movb         $4, %al
	0x44, 0x28, 0xd8, //0x000026b5 subb         %r11b, %al
	0x0f, 0xb6, 0xc0, //0x000026b8 movzbl       %al, %eax
	0x01, 0xc0, //0x000026bb addl         %eax, %eax
	0x8d, 0x0c, 0x40, //0x000026bd leal         (%rax,%rax,2), %ecx
	0x8b, 0x45, 0xc8, //0x000026c0 movl         $-56(%rbp), %eax
	0xd3, 0xe0, //0x000026c3 shll         %cl, %eax
	0x41, 0x83, 0xfb, 0x02, //0x000026c5 cmpl         $2, %r11d
	0x48, 0x8b, 0x5d, 0xd0, //0x000026c9 movq         $-48(%rbp), %rbx
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x000026cd je           LBB1_504
	0x41, 0x83, 0xfb, 0x03, //0x000026d3 cmpl         $3, %r11d
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x000026d7 je           LBB1_503
	0x41, 0x83, 0xfb, 0x04, //0x000026dd cmpl         $4, %r11d
	0x48, 0x8b, 0x4d, 0xb0, //0x000026e1 movq         $-80(%rbp), %rcx
	0x0f, 0x85, 0x17, 0x00, 0x00, 0x00, //0x000026e5 jne          LBB1_505
	0x88, 0x41, 0x02, //0x000026eb movb         %al, $2(%rcx)
	//0x000026ee LBB1_503
	0x89, 0xc1, //0x000026ee movl         %eax, %ecx
	0x48, 0x8b, 0x45, 0xb0, //0x000026f0 movq         $-80(%rbp), %rax
	0x88, 0x68, 0x01, //0x000026f4 movb         %ch, $1(%rax)
	0x89, 0xc8, //0x000026f7 movl         %ecx, %eax
	//0x000026f9 LBB1_504
	0xc1, 0xe8, 0x10, //0x000026f9 shrl         $16, %eax
	0x48, 0x8b, 0x4d, 0xb0, //0x000026fc movq         $-80(%rbp), %rcx
	0x88, 0x01, //0x00002700 movb         %al, (%rcx)
	//0x00002702 LBB1_505
	0x44, 0x89, 0xd8, //0x00002702 movl         %r11d, %eax
	0x48, 0x8d, 0x4c, 0x01, 0xff, //0x00002705 leaq         $-1(%rcx,%rax), %rcx
	0x48, 0x89, 0x4d, 0xb0, //0x0000270a movq         %rcx, $-80(%rbp)
	0x49, 0x39, 0xcc, //0x0000270e cmpq         %rcx, %r12
	0x0f, 0x83, 0x8c, 0xf0, 0xff, 0xff, //0x00002711 jae          LBB1_506
	0xe9, 0x9c, 0xde, 0xff, 0xff, //0x00002717 jmp          LBB1_2
	//0x0000271c LBB1_539
	0x48, 0x89, 0xd3, //0x0000271c movq         %rdx, %rbx
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x0000271f movabsq      $-4294967296, %r8
	0x49, 0x89, 0xd7, //0x00002729 movq         %rdx, %r15
	0xe9, 0xa8, 0xfd, 0xff, 0xff, //0x0000272c jmp          LBB1_495
	//0x00002731 LBB1_543
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00002731 movabsq      $-4294967296, %r8
	0x49, 0x89, 0xd7, //0x0000273b movq         %rdx, %r15
	0xe9, 0x96, 0xfd, 0xff, 0xff, //0x0000273e jmp          LBB1_495
	//0x00002743 LBB1_565
	0x4d, 0x89, 0xfe, //0x00002743 movq         %r15, %r14
	0x4c, 0x89, 0xf2, //0x00002746 movq         %r14, %rdx
	0x4c, 0x3b, 0x75, 0xd0, //0x00002749 cmpq         $-48(%rbp), %r14
	0x0f, 0x82, 0x15, 0x00, 0x00, 0x00, //0x0000274d jb           LBB1_549
	0xe9, 0x8c, 0xfc, 0xff, 0xff, //0x00002753 jmp          LBB1_511
	//0x00002758 LBB1_547
	0x4d, 0x89, 0xf7, //0x00002758 movq         %r14, %r15
	0x4c, 0x89, 0xf2, //0x0000275b movq         %r14, %rdx
	0x4c, 0x3b, 0x75, 0xd0, //0x0000275e cmpq         $-48(%rbp), %r14
	0x0f, 0x83, 0x7c, 0xfc, 0xff, 0xff, //0x00002762 jae          LBB1_511
	//0x00002768 LBB1_549
	0x4c, 0x8d, 0x7a, 0x01, //0x00002768 leaq         $1(%rdx), %r15
	0x0f, 0xb6, 0x3a, //0x0000276c movzbl       (%rdx), %edi
	0x40, 0x80, 0xff, 0x5c, //0x0000276f cmpb         $92, %dil
	0x0f, 0x85, 0xe6, 0x00, 0x00, 0x00, //0x00002773 jne          LBB1_562
	0x4c, 0x8d, 0x72, 0x02, //0x00002779 leaq         $2(%rdx), %r14
	0x4c, 0x3b, 0x75, 0xd0, //0x0000277d cmpq         $-48(%rbp), %r14
	0x0f, 0x87, 0x33, 0x01, 0x00, 0x00, //0x00002781 ja           LBB1_570
	0x41, 0x0f, 0xb6, 0x07, //0x00002787 movzbl       (%r15), %eax
	0x3c, 0x6e, //0x0000278b cmpb         $110, %al
	0x0f, 0x84, 0xc5, 0xff, 0xff, 0xff, //0x0000278d je           LBB1_547
	0x3c, 0x72, //0x00002793 cmpb         $114, %al
	0x0f, 0x84, 0xbd, 0xff, 0xff, 0xff, //0x00002795 je           LBB1_547
	0x3c, 0x75, //0x0000279b cmpb         $117, %al
	0x0f, 0x85, 0xe1, 0x00, 0x00, 0x00, //0x0000279d jne          LBB1_544
	0x48, 0x8b, 0x45, 0xd0, //0x000027a3 movq         $-48(%rbp), %rax
	0x4c, 0x29, 0xf0, //0x000027a7 subq         %r14, %rax
	0x48, 0x83, 0xf8, 0x04, //0x000027aa cmpq         $4, %rax
	0x0f, 0x8c, 0xc8, 0x00, 0x00, 0x00, //0x000027ae jl           LBB1_566
	0x41, 0x8b, 0x06, //0x000027b4 movl         (%r14), %eax
	0x89, 0xc7, //0x000027b7 movl         %eax, %edi
	0xf7, 0xd7, //0x000027b9 notl         %edi
	0x8d, 0x88, 0xd0, 0xcf, 0xcf, 0xcf, //0x000027bb leal         $-808464432(%rax), %ecx
	0x81, 0xe7, 0x80, 0x80, 0x80, 0x80, //0x000027c1 andl         $-2139062144, %edi
	0x85, 0xcf, //0x000027c7 testl        %ecx, %edi
	0x0f, 0x85, 0xad, 0x00, 0x00, 0x00, //0x000027c9 jne          LBB1_566
	0x8d, 0x88, 0x19, 0x19, 0x19, 0x19, //0x000027cf leal         $421075225(%rax), %ecx
	0x09, 0xc1, //0x000027d5 orl          %eax, %ecx
	0xf7, 0xc1, 0x80, 0x80, 0x80, 0x80, //0x000027d7 testl        $-2139062144, %ecx
	0x0f, 0x85, 0x99, 0x00, 0x00, 0x00, //0x000027dd jne          LBB1_566
	0x89, 0xc6, //0x000027e3 movl         %eax, %esi
	0x81, 0xe6, 0x7f, 0x7f, 0x7f, 0x7f, //0x000027e5 andl         $2139062143, %esi
	0xb9, 0xc0, 0xc0, 0xc0, 0xc0, //0x000027eb movl         $-1061109568, %ecx
	0x29, 0xf1, //0x000027f0 subl         %esi, %ecx
	0x8d, 0x9e, 0x46, 0x46, 0x46, 0x46, //0x000027f2 leal         $1179010630(%rsi), %ebx
	0x21, 0xf9, //0x000027f8 andl         %edi, %ecx
	0x85, 0xd9, //0x000027fa testl        %ebx, %ecx
	0x0f, 0x85, 0x7a, 0x00, 0x00, 0x00, //0x000027fc jne          LBB1_566
	0xb9, 0xe0, 0xe0, 0xe0, 0xe0, //0x00002802 movl         $-522133280, %ecx
	0x29, 0xf1, //0x00002807 subl         %esi, %ecx
	0x81, 0xc6, 0x39, 0x39, 0x39, 0x39, //0x00002809 addl         $960051513, %esi
	0x21, 0xcf, //0x0000280f andl         %ecx, %edi
	0x85, 0xf7, //0x00002811 testl        %esi, %edi
	0x0f, 0x85, 0x63, 0x00, 0x00, 0x00, //0x00002813 jne          LBB1_566
	0x0f, 0xc8, //0x00002819 bswapl       %eax
	0x89, 0xc1, //0x0000281b movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x0000281d shrl         $4, %ecx
	0xf7, 0xd1, //0x00002820 notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x00002822 andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x00002828 leal         (%rcx,%rcx,8), %ecx
	0x25, 0x0f, 0x0f, 0x0f, 0x0f, //0x0000282b andl         $252645135, %eax
	0x01, 0xc8, //0x00002830 addl         %ecx, %eax
	0x89, 0xc7, //0x00002832 movl         %eax, %edi
	0xc1, 0xef, 0x04, //0x00002834 shrl         $4, %edi
	0x09, 0xc7, //0x00002837 orl          %eax, %edi
	0x89, 0xf8, //0x00002839 movl         %edi, %eax
	0xc1, 0xe8, 0x08, //0x0000283b shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x0000283e andl         $65280, %eax
	0x89, 0xf9, //0x00002843 movl         %edi, %ecx
	0x81, 0xe1, 0x80, 0x00, 0x00, 0x00, //0x00002845 andl         $128, %ecx
	0x09, 0xc1, //0x0000284b orl          %eax, %ecx
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x0000284d je           LBB1_561
	0xbf, 0xff, 0x00, 0x00, 0x00, //0x00002853 movl         $255, %edi
	//0x00002858 LBB1_561
	0x48, 0x83, 0xc2, 0x06, //0x00002858 addq         $6, %rdx
	0x49, 0x89, 0xd7, //0x0000285c movq         %rdx, %r15
	//0x0000285f LBB1_562
	0x40, 0x80, 0xff, 0x0a, //0x0000285f cmpb         $10, %dil
	0x0f, 0x84, 0xda, 0xfe, 0xff, 0xff, //0x00002863 je           LBB1_565
	0x40, 0x80, 0xff, 0x0d, //0x00002869 cmpb         $13, %dil
	0x0f, 0x84, 0xd0, 0xfe, 0xff, 0xff, //0x0000286d je           LBB1_565
	0x40, 0x80, 0xff, 0x3d, //0x00002873 cmpb         $61, %dil
	0xe9, 0x2f, 0x00, 0x00, 0x00, //0x00002877 jmp          LBB1_567
	//0x0000287c LBB1_566
	0x4d, 0x89, 0xf7, //0x0000287c movq         %r14, %r15
	0xe9, 0x27, 0x00, 0x00, 0x00, //0x0000287f jmp          LBB1_567
	//0x00002884 LBB1_544
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00002884 movabsq      $-4294967296, %r8
	0x4d, 0x89, 0xf7, //0x0000288e movq         %r14, %r15
	0xe9, 0x43, 0xfc, 0xff, 0xff, //0x00002891 jmp          LBB1_495
	//0x00002896 LBB1_545
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00002896 movabsq      $-4294967296, %r8
	0x49, 0x89, 0xdf, //0x000028a0 movq         %rbx, %r15
	0xe9, 0x31, 0xfc, 0xff, 0xff, //0x000028a3 jmp          LBB1_495
	//0x000028a8 LBB1_546
	0x49, 0x89, 0xd7, //0x000028a8 movq         %rdx, %r15
	//0x000028ab LBB1_567
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x000028ab movabsq      $-4294967296, %r8
	0xe9, 0x1f, 0xfc, 0xff, 0xff, //0x000028b5 jmp          LBB1_495
	//0x000028ba LBB1_570
	0x4c, 0x89, 0xfa, //0x000028ba movq         %r15, %rdx
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x000028bd movabsq      $-4294967296, %r8
	0xe9, 0x0d, 0xfc, 0xff, 0xff, //0x000028c7 jmp          LBB1_495
	//0x000028cc LBB1_574
	0x4d, 0x89, 0xf9, //0x000028cc movq         %r15, %r9
	0x48, 0x8b, 0x45, 0x90, //0x000028cf movq         $-112(%rbp), %rax
	0x48, 0x83, 0xc0, 0xfc, //0x000028d3 addq         $-4, %rax
	0x48, 0x3b, 0x45, 0xb0, //0x000028d7 cmpq         $-80(%rbp), %rax
	0x0f, 0x83, 0x1e, 0x00, 0x00, 0x00, //0x000028db jae          LBB1_575
	0xe9, 0x34, 0x10, 0x00, 0x00, //0x000028e1 jmp          LBB1_851
	//0x000028e6 LBB1_572
	0x4d, 0x89, 0xcf, //0x000028e6 movq         %r9, %r15
	0x48, 0x8b, 0x5d, 0xd0, //0x000028e9 movq         $-48(%rbp), %rbx
	0x48, 0x8b, 0x45, 0x90, //0x000028ed movq         $-112(%rbp), %rax
	0x48, 0x83, 0xc0, 0xfc, //0x000028f1 addq         $-4, %rax
	0x48, 0x3b, 0x45, 0xb0, //0x000028f5 cmpq         $-80(%rbp), %rax
	0x0f, 0x82, 0x1b, 0x10, 0x00, 0x00, //0x000028f9 jb           LBB1_851
	//0x000028ff LBB1_575
	0x4c, 0x8d, 0x6b, 0xfc, //0x000028ff leaq         $-4(%rbx), %r13
	0x4d, 0x39, 0xfd, //0x00002903 cmpq         %r15, %r13
	0x0f, 0x82, 0x0e, 0x10, 0x00, 0x00, //0x00002906 jb           LBB1_851
	0x49, 0x89, 0xc6, //0x0000290c movq         %rax, %r14
	0x44, 0x8b, 0x65, 0xbc, //0x0000290f movl         $-68(%rbp), %r12d
	0x41, 0x83, 0xe4, 0x02, //0x00002913 andl         $2, %r12d
	0x48, 0x8b, 0x45, 0xa8, //0x00002917 movq         $-88(%rbp), %rax
	0x48, 0x8b, 0x4d, 0x88, //0x0000291b movq         $-120(%rbp), %rcx
	0x48, 0x8d, 0x54, 0x01, 0xfe, //0x0000291f leaq         $-2(%rcx,%rax), %rdx
	0x48, 0x89, 0x55, 0xa0, //0x00002924 movq         %rdx, $-96(%rbp)
	0x48, 0x8d, 0x44, 0x01, 0xfd, //0x00002928 leaq         $-3(%rcx,%rax), %rax
	0x48, 0x89, 0x45, 0x90, //0x0000292d movq         %rax, $-112(%rbp)
	0xe9, 0x45, 0x00, 0x00, 0x00, //0x00002931 jmp          LBB1_577
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002936 .p2align 4, 0x90
	//0x00002940 LBB1_578
	0xc1, 0xe2, 0x1a, //0x00002940 shll         $26, %edx
	0xc1, 0xe0, 0x14, //0x00002943 shll         $20, %eax
	0x09, 0xd0, //0x00002946 orl          %edx, %eax
	0xc1, 0xe3, 0x0e, //0x00002948 shll         $14, %ebx
	0xc1, 0xe1, 0x08, //0x0000294b shll         $8, %ecx
	0x09, 0xd9, //0x0000294e orl          %ebx, %ecx
	0x09, 0xc1, //0x00002950 orl          %eax, %ecx
	0x0f, 0xc9, //0x00002952 bswapl       %ecx
	0x48, 0x8b, 0x55, 0xb0, //0x00002954 movq         $-80(%rbp), %rdx
	0x89, 0x0a, //0x00002958 movl         %ecx, (%rdx)
	0x49, 0x83, 0xc7, 0x04, //0x0000295a addq         $4, %r15
	0x48, 0x83, 0xc2, 0x03, //0x0000295e addq         $3, %rdx
	0x48, 0x89, 0x55, 0xb0, //0x00002962 movq         %rdx, $-80(%rbp)
	0x49, 0x39, 0xd6, //0x00002966 cmpq         %rdx, %r14
	0x0f, 0x82, 0xbb, 0x0f, 0x00, 0x00, //0x00002969 jb           LBB1_852
	//0x0000296f LBB1_727
	0x4d, 0x89, 0xf9, //0x0000296f movq         %r15, %r9
	0x4d, 0x39, 0xfd, //0x00002972 cmpq         %r15, %r13
	0x0f, 0x82, 0xaf, 0x0f, 0x00, 0x00, //0x00002975 jb           LBB1_852
	//0x0000297b LBB1_577
	0x41, 0x0f, 0xb6, 0x07, //0x0000297b movzbl       (%r15), %eax
	0x48, 0x8b, 0x75, 0xc0, //0x0000297f movq         $-64(%rbp), %rsi
	0x0f, 0xb6, 0x14, 0x06, //0x00002983 movzbl       (%rsi,%rax), %edx
	0x41, 0x0f, 0xb6, 0x47, 0x01, //0x00002987 movzbl       $1(%r15), %eax
	0x0f, 0xb6, 0x04, 0x06, //0x0000298c movzbl       (%rsi,%rax), %eax
	0x41, 0x0f, 0xb6, 0x4f, 0x02, //0x00002990 movzbl       $2(%r15), %ecx
	0x0f, 0xb6, 0x1c, 0x0e, //0x00002995 movzbl       (%rsi,%rcx), %ebx
	0x41, 0x0f, 0xb6, 0x4f, 0x03, //0x00002999 movzbl       $3(%r15), %ecx
	0x0f, 0xb6, 0x0c, 0x0e, //0x0000299e movzbl       (%rsi,%rcx), %ecx
	0x89, 0xc7, //0x000029a2 movl         %eax, %edi
	0x09, 0xd7, //0x000029a4 orl          %edx, %edi
	0x89, 0xde, //0x000029a6 movl         %ebx, %esi
	0x09, 0xce, //0x000029a8 orl          %ecx, %esi
	0x09, 0xfe, //0x000029aa orl          %edi, %esi
	0x40, 0x80, 0xfe, 0xff, //0x000029ac cmpb         $-1, %sil
	0x0f, 0x85, 0x8a, 0xff, 0xff, 0xff, //0x000029b0 jne          LBB1_578
	0x48, 0x8b, 0x5d, 0xd0, //0x000029b6 movq         $-48(%rbp), %rbx
	0x4c, 0x39, 0xfb, //0x000029ba cmpq         %r15, %rbx
	0x0f, 0x86, 0x1d, 0x02, 0x00, 0x00, //0x000029bd jbe          LBB1_611
	0xf6, 0x45, 0xbc, 0x08, //0x000029c3 testb        $8, $-68(%rbp)
	0x0f, 0x84, 0x1f, 0x00, 0x00, 0x00, //0x000029c7 je           LBB1_583
	0x4c, 0x89, 0xfa, //0x000029cd movq         %r15, %rdx
	0xe9, 0xc7, 0x00, 0x00, 0x00, //0x000029d0 jmp          LBB1_594
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000029d5 .p2align 4, 0x90
	//0x000029e0 LBB1_582
	0x49, 0xff, 0xc7, //0x000029e0 incq         %r15
	0x49, 0x39, 0xdf, //0x000029e3 cmpq         %rbx, %r15
	0x0f, 0x83, 0xde, 0x01, 0x00, 0x00, //0x000029e6 jae          LBB1_609
	//0x000029ec LBB1_583
	0x41, 0x0f, 0xb6, 0x0f, //0x000029ec movzbl       (%r15), %ecx
	0x48, 0x83, 0xf9, 0x0d, //0x000029f0 cmpq         $13, %rcx
	0x0f, 0x84, 0xe6, 0xff, 0xff, 0xff, //0x000029f4 je           LBB1_582
	0x80, 0xf9, 0x0a, //0x000029fa cmpb         $10, %cl
	0x0f, 0x84, 0xdd, 0xff, 0xff, 0xff, //0x000029fd je           LBB1_582
	0x48, 0x8b, 0x45, 0xc0, //0x00002a03 movq         $-64(%rbp), %rax
	0x0f, 0xb6, 0x04, 0x08, //0x00002a07 movzbl       (%rax,%rcx), %eax
	0x49, 0xff, 0xc7, //0x00002a0b incq         %r15
	0x89, 0x45, 0xc8, //0x00002a0e movl         %eax, $-56(%rbp)
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00002a11 cmpl         $255, %eax
	0x0f, 0x84, 0x84, 0x02, 0x00, 0x00, //0x00002a16 je           LBB1_623
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00002a1c movl         $1, %r11d
	0x49, 0x39, 0xdf, //0x00002a22 cmpq         %rbx, %r15
	0x0f, 0x82, 0x11, 0x00, 0x00, 0x00, //0x00002a25 jb           LBB1_588
	0xe9, 0x99, 0x06, 0x00, 0x00, //0x00002a2b jmp          LBB1_693
	//0x00002a30 .p2align 4, 0x90
	//0x00002a30 LBB1_587
	0x49, 0xff, 0xc7, //0x00002a30 incq         %r15
	0x49, 0x39, 0xdf, //0x00002a33 cmpq         %rbx, %r15
	0x0f, 0x83, 0x3d, 0x04, 0x00, 0x00, //0x00002a36 jae          LBB1_675
	//0x00002a3c LBB1_588
	0x41, 0x0f, 0xb6, 0x0f, //0x00002a3c movzbl       (%r15), %ecx
	0x48, 0x83, 0xf9, 0x0d, //0x00002a40 cmpq         $13, %rcx
	0x0f, 0x84, 0xe6, 0xff, 0xff, 0xff, //0x00002a44 je           LBB1_587
	0x80, 0xf9, 0x0a, //0x00002a4a cmpb         $10, %cl
	0x0f, 0x84, 0xdd, 0xff, 0xff, 0xff, //0x00002a4d je           LBB1_587
	0x48, 0x8b, 0x45, 0xc0, //0x00002a53 movq         $-64(%rbp), %rax
	0x0f, 0xb6, 0x04, 0x08, //0x00002a57 movzbl       (%rax,%rcx), %eax
	0x49, 0xff, 0xc7, //0x00002a5b incq         %r15
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00002a5e cmpl         $255, %eax
	0x0f, 0x84, 0xb6, 0x06, 0x00, 0x00, //0x00002a63 je           LBB1_716
	0x8b, 0x4d, 0xc8, //0x00002a69 movl         $-56(%rbp), %ecx
	0xc1, 0xe1, 0x06, //0x00002a6c shll         $6, %ecx
	0x09, 0xc1, //0x00002a6f orl          %eax, %ecx
	0x89, 0x4d, 0xc8, //0x00002a71 movl         %ecx, $-56(%rbp)
	0x41, 0xbb, 0x02, 0x00, 0x00, 0x00, //0x00002a74 movl         $2, %r11d
	0x49, 0x39, 0xdf, //0x00002a7a cmpq         %rbx, %r15
	0x0f, 0x82, 0x8f, 0x01, 0x00, 0x00, //0x00002a7d jb           LBB1_613
	0xe9, 0x41, 0x06, 0x00, 0x00, //0x00002a83 jmp          LBB1_693
	//0x00002a88 LBB1_592
	0x3c, 0x6e, //0x00002a88 cmpb         $110, %al
	0x0f, 0x85, 0xd6, 0x01, 0x00, 0x00, //0x00002a8a jne          LBB1_618
	//0x00002a90 .p2align 4, 0x90
	//0x00002a90 LBB1_593
	0x4c, 0x89, 0xfa, //0x00002a90 movq         %r15, %rdx
	0x49, 0x39, 0xdf, //0x00002a93 cmpq         %rbx, %r15
	0x0f, 0x83, 0x2e, 0x01, 0x00, 0x00, //0x00002a96 jae          LBB1_609
	//0x00002a9c LBB1_594
	0x48, 0x8d, 0x42, 0x01, //0x00002a9c leaq         $1(%rdx), %rax
	0x0f, 0xb6, 0x0a, //0x00002aa0 movzbl       (%rdx), %ecx
	0x80, 0xf9, 0x5c, //0x00002aa3 cmpb         $92, %cl
	0x0f, 0x85, 0x04, 0x01, 0x00, 0x00, //0x00002aa6 jne          LBB1_607
	0x4c, 0x8d, 0x7a, 0x02, //0x00002aac leaq         $2(%rdx), %r15
	0xb1, 0xff, //0x00002ab0 movb         $-1, %cl
	0x49, 0x39, 0xdf, //0x00002ab2 cmpq         %rbx, %r15
	0x0f, 0x87, 0xa3, 0x01, 0x00, 0x00, //0x00002ab5 ja           LBB1_617
	0x0f, 0xb6, 0x00, //0x00002abb movzbl       (%rax), %eax
	0x3c, 0x71, //0x00002abe cmpb         $113, %al
	0x0f, 0x8e, 0xc2, 0xff, 0xff, 0xff, //0x00002ac0 jle          LBB1_592
	0x3c, 0x72, //0x00002ac6 cmpb         $114, %al
	0x0f, 0x84, 0xc2, 0xff, 0xff, 0xff, //0x00002ac8 je           LBB1_593
	0x3c, 0x75, //0x00002ace cmpb         $117, %al
	0x0f, 0x85, 0x9a, 0x01, 0x00, 0x00, //0x00002ad0 jne          LBB1_620
	0x48, 0x89, 0xd8, //0x00002ad6 movq         %rbx, %rax
	0x4c, 0x29, 0xf8, //0x00002ad9 subq         %r15, %rax
	0x48, 0x83, 0xf8, 0x04, //0x00002adc cmpq         $4, %rax
	0x0f, 0x8c, 0x8a, 0x01, 0x00, 0x00, //0x00002ae0 jl           LBB1_620
	0x41, 0x8b, 0x07, //0x00002ae6 movl         (%r15), %eax
	0x89, 0xc6, //0x00002ae9 movl         %eax, %esi
	0xf7, 0xd6, //0x00002aeb notl         %esi
	0x8d, 0xb8, 0xd0, 0xcf, 0xcf, 0xcf, //0x00002aed leal         $-808464432(%rax), %edi
	0x81, 0xe6, 0x80, 0x80, 0x80, 0x80, //0x00002af3 andl         $-2139062144, %esi
	0x85, 0xfe, //0x00002af9 testl        %edi, %esi
	0x0f, 0x85, 0x6f, 0x01, 0x00, 0x00, //0x00002afb jne          LBB1_620
	0x8d, 0xb8, 0x19, 0x19, 0x19, 0x19, //0x00002b01 leal         $421075225(%rax), %edi
	0x09, 0xc7, //0x00002b07 orl          %eax, %edi
	0xf7, 0xc7, 0x80, 0x80, 0x80, 0x80, //0x00002b09 testl        $-2139062144, %edi
	0x0f, 0x85, 0x5b, 0x01, 0x00, 0x00, //0x00002b0f jne          LBB1_620
	0x89, 0xc7, //0x00002b15 movl         %eax, %edi
	0x81, 0xe7, 0x7f, 0x7f, 0x7f, 0x7f, //0x00002b17 andl         $2139062143, %edi
	0xbb, 0xc0, 0xc0, 0xc0, 0xc0, //0x00002b1d movl         $-1061109568, %ebx
	0x29, 0xfb, //0x00002b22 subl         %edi, %ebx
	0x44, 0x8d, 0x87, 0x46, 0x46, 0x46, 0x46, //0x00002b24 leal         $1179010630(%rdi), %r8d
	0x21, 0xf3, //0x00002b2b andl         %esi, %ebx
	0x44, 0x85, 0xc3, //0x00002b2d testl        %r8d, %ebx
	0x48, 0x8b, 0x5d, 0xd0, //0x00002b30 movq         $-48(%rbp), %rbx
	0x0f, 0x85, 0x36, 0x01, 0x00, 0x00, //0x00002b34 jne          LBB1_620
	0xbb, 0xe0, 0xe0, 0xe0, 0xe0, //0x00002b3a movl         $-522133280, %ebx
	0x29, 0xfb, //0x00002b3f subl         %edi, %ebx
	0x81, 0xc7, 0x39, 0x39, 0x39, 0x39, //0x00002b41 addl         $960051513, %edi
	0x21, 0xde, //0x00002b47 andl         %ebx, %esi
	0x48, 0x8b, 0x5d, 0xd0, //0x00002b49 movq         $-48(%rbp), %rbx
	0x85, 0xfe, //0x00002b4d testl        %edi, %esi
	0x0f, 0x85, 0x1b, 0x01, 0x00, 0x00, //0x00002b4f jne          LBB1_620
	0x0f, 0xc8, //0x00002b55 bswapl       %eax
	0x89, 0xc1, //0x00002b57 movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x00002b59 shrl         $4, %ecx
	0xf7, 0xd1, //0x00002b5c notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x00002b5e andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x00002b64 leal         (%rcx,%rcx,8), %ecx
	0x25, 0x0f, 0x0f, 0x0f, 0x0f, //0x00002b67 andl         $252645135, %eax
	0x01, 0xc8, //0x00002b6c addl         %ecx, %eax
	0x89, 0xc1, //0x00002b6e movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x00002b70 shrl         $4, %ecx
	0x09, 0xc1, //0x00002b73 orl          %eax, %ecx
	0x89, 0xc8, //0x00002b75 movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x00002b77 shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x00002b7a andl         $65280, %eax
	0x89, 0xce, //0x00002b7f movl         %ecx, %esi
	0x81, 0xe6, 0x80, 0x00, 0x00, 0x00, //0x00002b81 andl         $128, %esi
	0x09, 0xc6, //0x00002b87 orl          %eax, %esi
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x00002b89 je           LBB1_606
	0xb9, 0xff, 0x00, 0x00, 0x00, //0x00002b8f movl         $255, %ecx
	//0x00002b94 LBB1_606
	0x48, 0x83, 0xc2, 0x06, //0x00002b94 addq         $6, %rdx
	0x49, 0x89, 0xd7, //0x00002b98 movq         %rdx, %r15
	0x80, 0xf9, 0x0d, //0x00002b9b cmpb         $13, %cl
	0x0f, 0x85, 0x18, 0x00, 0x00, 0x00, //0x00002b9e jne          LBB1_608
	0xe9, 0xe7, 0xfe, 0xff, 0xff, //0x00002ba4 jmp          LBB1_593
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002ba9 .p2align 4, 0x90
	//0x00002bb0 LBB1_607
	0x49, 0x89, 0xc7, //0x00002bb0 movq         %rax, %r15
	0x80, 0xf9, 0x0d, //0x00002bb3 cmpb         $13, %cl
	0x0f, 0x84, 0xd4, 0xfe, 0xff, 0xff, //0x00002bb6 je           LBB1_593
	//0x00002bbc LBB1_608
	0x80, 0xf9, 0x0a, //0x00002bbc cmpb         $10, %cl
	0x0f, 0x84, 0xcb, 0xfe, 0xff, 0xff, //0x00002bbf je           LBB1_593
	0xe9, 0xa6, 0x00, 0x00, 0x00, //0x00002bc5 jmp          LBB1_620
	//0x00002bca LBB1_609
	0xc7, 0x45, 0xc8, 0x00, 0x00, 0x00, 0x00, //0x00002bca movl         $0, $-56(%rbp)
	0x45, 0x31, 0xdb, //0x00002bd1 xorl         %r11d, %r11d
	//0x00002bd4 LBB1_610
	0x45, 0x85, 0xdb, //0x00002bd4 testl        %r11d, %r11d
	0x0f, 0x85, 0xec, 0x04, 0x00, 0x00, //0x00002bd7 jne          LBB1_693
	0x90, 0x90, 0x90, //0x00002bdd .p2align 4, 0x90
	//0x00002be0 LBB1_611
	0x48, 0x8b, 0x55, 0xb0, //0x00002be0 movq         $-80(%rbp), %rdx
	0x48, 0x89, 0x55, 0xb0, //0x00002be4 movq         %rdx, $-80(%rbp)
	0x49, 0x39, 0xd6, //0x00002be8 cmpq         %rdx, %r14
	0x0f, 0x83, 0x7e, 0xfd, 0xff, 0xff, //0x00002beb jae          LBB1_727
	0xe9, 0x34, 0x0d, 0x00, 0x00, //0x00002bf1 jmp          LBB1_852
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002bf6 .p2align 4, 0x90
	//0x00002c00 LBB1_612
	0x49, 0xff, 0xc7, //0x00002c00 incq         %r15
	0x41, 0xbb, 0x02, 0x00, 0x00, 0x00, //0x00002c03 movl         $2, %r11d
	0x49, 0x39, 0xdf, //0x00002c09 cmpq         %rbx, %r15
	0x0f, 0x83, 0xc2, 0xff, 0xff, 0xff, //0x00002c0c jae          LBB1_610
	//0x00002c12 LBB1_613
	0x41, 0x0f, 0xb6, 0x0f, //0x00002c12 movzbl       (%r15), %ecx
	0x48, 0x83, 0xf9, 0x0d, //0x00002c16 cmpq         $13, %rcx
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x00002c1a je           LBB1_612
	0x80, 0xf9, 0x0a, //0x00002c20 cmpb         $10, %cl
	0x0f, 0x84, 0xd7, 0xff, 0xff, 0xff, //0x00002c23 je           LBB1_612
	0x48, 0x8b, 0x45, 0xc0, //0x00002c29 movq         $-64(%rbp), %rax
	0x0f, 0xb6, 0x04, 0x08, //0x00002c2d movzbl       (%rax,%rcx), %eax
	0x49, 0xff, 0xc7, //0x00002c31 incq         %r15
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00002c34 cmpl         $255, %eax
	0x0f, 0x84, 0x0e, 0x09, 0x00, 0x00, //0x00002c39 je           LBB1_759
	0x8b, 0x4d, 0xc8, //0x00002c3f movl         $-56(%rbp), %ecx
	0xc1, 0xe1, 0x06, //0x00002c42 shll         $6, %ecx
	0x09, 0xc1, //0x00002c45 orl          %eax, %ecx
	0x89, 0x4d, 0xc8, //0x00002c47 movl         %ecx, $-56(%rbp)
	0x41, 0xbb, 0x03, 0x00, 0x00, 0x00, //0x00002c4a movl         $3, %r11d
	0x49, 0x39, 0xdf, //0x00002c50 cmpq         %rbx, %r15
	0x0f, 0x82, 0xa6, 0x03, 0x00, 0x00, //0x00002c53 jb           LBB1_677
	0xe9, 0x6b, 0x04, 0x00, 0x00, //0x00002c59 jmp          LBB1_693
	//0x00002c5e LBB1_617
	0x49, 0x89, 0xc7, //0x00002c5e movq         %rax, %r15
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00002c61 jmp          LBB1_620
	//0x00002c66 LBB1_618
	0x3c, 0x2f, //0x00002c66 cmpb         $47, %al
	0x0f, 0x85, 0x02, 0x00, 0x00, 0x00, //0x00002c68 jne          LBB1_620
	0x89, 0xc1, //0x00002c6e movl         %eax, %ecx
	//0x00002c70 LBB1_620
	0x0f, 0xb6, 0xc1, //0x00002c70 movzbl       %cl, %eax
	0x48, 0x8b, 0x55, 0xc0, //0x00002c73 movq         $-64(%rbp), %rdx
	0x0f, 0xb6, 0x04, 0x02, //0x00002c77 movzbl       (%rdx,%rax), %eax
	0x89, 0x45, 0xc8, //0x00002c7b movl         %eax, $-56(%rbp)
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00002c7e cmpl         $255, %eax
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00002c83 je           LBB1_623
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00002c89 movl         $1, %r11d
	0x49, 0x39, 0xdf, //0x00002c8f cmpq         %rbx, %r15
	0x0f, 0x83, 0x31, 0x04, 0x00, 0x00, //0x00002c92 jae          LBB1_693
	0x4c, 0x89, 0xfa, //0x00002c98 movq         %r15, %rdx
	0xe9, 0xc8, 0x00, 0x00, 0x00, //0x00002c9b jmp          LBB1_639
	//0x00002ca0 LBB1_623
	0xc7, 0x45, 0xc8, 0x00, 0x00, 0x00, 0x00, //0x00002ca0 movl         $0, $-56(%rbp)
	0x45, 0x31, 0xdb, //0x00002ca7 xorl         %r11d, %r11d
	//0x00002caa LBB1_624
	0x45, 0x85, 0xe4, //0x00002caa testl        %r12d, %r12d
	0x0f, 0x85, 0x3e, 0x04, 0x00, 0x00, //0x00002cad jne          LBB1_696
	0x41, 0x83, 0xfb, 0x02, //0x00002cb3 cmpl         $2, %r11d
	0x0f, 0x82, 0x34, 0x04, 0x00, 0x00, //0x00002cb7 jb           LBB1_696
	0x80, 0xf9, 0x3d, //0x00002cbd cmpb         $61, %cl
	0x0f, 0x85, 0x2b, 0x04, 0x00, 0x00, //0x00002cc0 jne          LBB1_696
	0x41, 0xba, 0x05, 0x00, 0x00, 0x00, //0x00002cc6 movl         $5, %r10d
	0x45, 0x29, 0xda, //0x00002ccc subl         %r11d, %r10d
	0xf6, 0x45, 0xbc, 0x08, //0x00002ccf testb        $8, $-68(%rbp)
	0x0f, 0x85, 0xab, 0x01, 0x00, 0x00, //0x00002cd3 jne          LBB1_655
	0x4c, 0x39, 0x7d, 0xd0, //0x00002cd9 cmpq         %r15, $-48(%rbp)
	0x0f, 0x86, 0x2a, 0x09, 0x00, 0x00, //0x00002cdd jbe          LBB1_720
	0x49, 0x8d, 0x4f, 0x03, //0x00002ce3 leaq         $3(%r15), %rcx
	0x48, 0x8b, 0x5d, 0xa0, //0x00002ce7 movq         $-96(%rbp), %rbx
	0x4c, 0x29, 0xfb, //0x00002ceb subq         %r15, %rbx
	0x49, 0x8d, 0x57, 0x04, //0x00002cee leaq         $4(%r15), %rdx
	0x48, 0x8b, 0x7d, 0x90, //0x00002cf2 movq         $-112(%rbp), %rdi
	0x4c, 0x29, 0xff, //0x00002cf6 subq         %r15, %rdi
	0xe9, 0x1b, 0x00, 0x00, 0x00, //0x00002cf9 jmp          LBB1_631
	0x90, 0x90, //0x00002cfe .p2align 4, 0x90
	//0x00002d00 LBB1_630
	0x49, 0xff, 0xc7, //0x00002d00 incq         %r15
	0x48, 0xff, 0xc1, //0x00002d03 incq         %rcx
	0x48, 0xff, 0xcb, //0x00002d06 decq         %rbx
	0x48, 0xff, 0xc2, //0x00002d09 incq         %rdx
	0x48, 0xff, 0xcf, //0x00002d0c decq         %rdi
	0x4c, 0x39, 0x7d, 0xd0, //0x00002d0f cmpq         %r15, $-48(%rbp)
	0x0f, 0x84, 0xf0, 0x08, 0x00, 0x00, //0x00002d13 je           LBB1_717
	//0x00002d19 LBB1_631
	0x41, 0x0f, 0xb6, 0x07, //0x00002d19 movzbl       (%r15), %eax
	0x3c, 0x0a, //0x00002d1d cmpb         $10, %al
	0x0f, 0x84, 0xdb, 0xff, 0xff, 0xff, //0x00002d1f je           LBB1_630
	0x3c, 0x0d, //0x00002d25 cmpb         $13, %al
	0x0f, 0x84, 0xd3, 0xff, 0xff, 0xff, //0x00002d27 je           LBB1_630
	0x3c, 0x3d, //0x00002d2d cmpb         $61, %al
	0x0f, 0x85, 0x30, 0x05, 0x00, 0x00, //0x00002d2f jne          LBB1_728
	0x49, 0xff, 0xc7, //0x00002d35 incq         %r15
	0x41, 0x83, 0xfa, 0x02, //0x00002d38 cmpl         $2, %r10d
	0x0f, 0x84, 0xaf, 0x03, 0x00, 0x00, //0x00002d3c je           LBB1_696
	0x4c, 0x39, 0x7d, 0xd0, //0x00002d42 cmpq         %r15, $-48(%rbp)
	0x0f, 0x87, 0x04, 0x03, 0x00, 0x00, //0x00002d46 ja           LBB1_682
	0xe9, 0xbc, 0x08, 0x00, 0x00, //0x00002d4c jmp          LBB1_720
	//0x00002d51 LBB1_636
	0x3c, 0x6e, //0x00002d51 cmpb         $110, %al
	0x0f, 0x85, 0x2f, 0x03, 0x00, 0x00, //0x00002d53 jne          LBB1_688
	//0x00002d59 LBB1_637
	0x4d, 0x89, 0xc7, //0x00002d59 movq         %r8, %r15
	//0x00002d5c LBB1_638
	0x4c, 0x89, 0xfa, //0x00002d5c movq         %r15, %rdx
	0x49, 0x39, 0xdf, //0x00002d5f cmpq         %rbx, %r15
	0x0f, 0x83, 0x11, 0x01, 0x00, 0x00, //0x00002d62 jae          LBB1_675
	//0x00002d68 LBB1_639
	0x4c, 0x8d, 0x7a, 0x01, //0x00002d68 leaq         $1(%rdx), %r15
	0x0f, 0xb6, 0x0a, //0x00002d6c movzbl       (%rdx), %ecx
	0x80, 0xf9, 0x5c, //0x00002d6f cmpb         $92, %cl
	0x0f, 0x85, 0xea, 0x00, 0x00, 0x00, //0x00002d72 jne          LBB1_652
	0x4c, 0x8d, 0x42, 0x02, //0x00002d78 leaq         $2(%rdx), %r8
	0xb1, 0xff, //0x00002d7c movb         $-1, %cl
	0x49, 0x39, 0xd8, //0x00002d7e cmpq         %rbx, %r8
	0x0f, 0x87, 0x12, 0x03, 0x00, 0x00, //0x00002d81 ja           LBB1_691
	0x41, 0x0f, 0xb6, 0x07, //0x00002d87 movzbl       (%r15), %eax
	0x3c, 0x71, //0x00002d8b cmpb         $113, %al
	0x0f, 0x8e, 0xbe, 0xff, 0xff, 0xff, //0x00002d8d jle          LBB1_636
	0x3c, 0x72, //0x00002d93 cmpb         $114, %al
	0x0f, 0x84, 0xbe, 0xff, 0xff, 0xff, //0x00002d95 je           LBB1_637
	0x3c, 0x75, //0x00002d9b cmpb         $117, %al
	0x0f, 0x85, 0xef, 0x02, 0x00, 0x00, //0x00002d9d jne          LBB1_690
	0x48, 0x8b, 0x45, 0xd0, //0x00002da3 movq         $-48(%rbp), %rax
	0x4c, 0x29, 0xc0, //0x00002da7 subq         %r8, %rax
	0x48, 0x83, 0xf8, 0x04, //0x00002daa cmpq         $4, %rax
	0x0f, 0x8c, 0xde, 0x02, 0x00, 0x00, //0x00002dae jl           LBB1_690
	0x41, 0x8b, 0x18, //0x00002db4 movl         (%r8), %ebx
	0x89, 0xde, //0x00002db7 movl         %ebx, %esi
	0xf7, 0xd6, //0x00002db9 notl         %esi
	0x8d, 0x83, 0xd0, 0xcf, 0xcf, 0xcf, //0x00002dbb leal         $-808464432(%rbx), %eax
	0x81, 0xe6, 0x80, 0x80, 0x80, 0x80, //0x00002dc1 andl         $-2139062144, %esi
	0x85, 0xc6, //0x00002dc7 testl        %eax, %esi
	0x0f, 0x85, 0xc3, 0x02, 0x00, 0x00, //0x00002dc9 jne          LBB1_690
	0x8d, 0x83, 0x19, 0x19, 0x19, 0x19, //0x00002dcf leal         $421075225(%rbx), %eax
	0x09, 0xd8, //0x00002dd5 orl          %ebx, %eax
	0xa9, 0x80, 0x80, 0x80, 0x80, //0x00002dd7 testl        $-2139062144, %eax
	0x0f, 0x85, 0xb0, 0x02, 0x00, 0x00, //0x00002ddc jne          LBB1_690
	0x89, 0xd8, //0x00002de2 movl         %ebx, %eax
	0x25, 0x7f, 0x7f, 0x7f, 0x7f, //0x00002de4 andl         $2139062143, %eax
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x00002de9 movl         $-1061109568, %edi
	0x29, 0xc7, //0x00002dee subl         %eax, %edi
	0x44, 0x8d, 0x90, 0x46, 0x46, 0x46, 0x46, //0x00002df0 leal         $1179010630(%rax), %r10d
	0x21, 0xf7, //0x00002df7 andl         %esi, %edi
	0x44, 0x85, 0xd7, //0x00002df9 testl        %r10d, %edi
	0x0f, 0x85, 0x90, 0x02, 0x00, 0x00, //0x00002dfc jne          LBB1_690
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x00002e02 movl         $-522133280, %edi
	0x29, 0xc7, //0x00002e07 subl         %eax, %edi
	0x05, 0x39, 0x39, 0x39, 0x39, //0x00002e09 addl         $960051513, %eax
	0x21, 0xfe, //0x00002e0e andl         %edi, %esi
	0x85, 0xc6, //0x00002e10 testl        %eax, %esi
	0x0f, 0x85, 0x7a, 0x02, 0x00, 0x00, //0x00002e12 jne          LBB1_690
	0x0f, 0xcb, //0x00002e18 bswapl       %ebx
	0x89, 0xd8, //0x00002e1a movl         %ebx, %eax
	0xc1, 0xe8, 0x04, //0x00002e1c shrl         $4, %eax
	0xf7, 0xd0, //0x00002e1f notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00002e21 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00002e26 leal         (%rax,%rax,8), %eax
	0x81, 0xe3, 0x0f, 0x0f, 0x0f, 0x0f, //0x00002e29 andl         $252645135, %ebx
	0x01, 0xc3, //0x00002e2f addl         %eax, %ebx
	0x89, 0xd9, //0x00002e31 movl         %ebx, %ecx
	0xc1, 0xe9, 0x04, //0x00002e33 shrl         $4, %ecx
	0x09, 0xd9, //0x00002e36 orl          %ebx, %ecx
	0x89, 0xc8, //0x00002e38 movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x00002e3a shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x00002e3d andl         $65280, %eax
	0x89, 0xce, //0x00002e42 movl         %ecx, %esi
	0x81, 0xe6, 0x80, 0x00, 0x00, 0x00, //0x00002e44 andl         $128, %esi
	0x09, 0xc6, //0x00002e4a orl          %eax, %esi
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x00002e4c je           LBB1_651
	0xb9, 0xff, 0x00, 0x00, 0x00, //0x00002e52 movl         $255, %ecx
	//0x00002e57 LBB1_651
	0x48, 0x83, 0xc2, 0x06, //0x00002e57 addq         $6, %rdx
	0x49, 0x89, 0xd7, //0x00002e5b movq         %rdx, %r15
	0x48, 0x8b, 0x5d, 0xd0, //0x00002e5e movq         $-48(%rbp), %rbx
	//0x00002e62 LBB1_652
	0x80, 0xf9, 0x0a, //0x00002e62 cmpb         $10, %cl
	0x0f, 0x84, 0xf1, 0xfe, 0xff, 0xff, //0x00002e65 je           LBB1_638
	0x80, 0xf9, 0x0d, //0x00002e6b cmpb         $13, %cl
	0x0f, 0x84, 0xe8, 0xfe, 0xff, 0xff, //0x00002e6e je           LBB1_638
	0xe9, 0x20, 0x02, 0x00, 0x00, //0x00002e74 jmp          LBB1_691
	//0x00002e79 LBB1_675
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00002e79 movl         $1, %r11d
	0xe9, 0x50, 0xfd, 0xff, 0xff, //0x00002e7f jmp          LBB1_610
	//0x00002e84 LBB1_655
	0x48, 0x8b, 0x55, 0xd0, //0x00002e84 movq         $-48(%rbp), %rdx
	0x4c, 0x39, 0xfa, //0x00002e88 cmpq         %r15, %rdx
	0x0f, 0x87, 0x28, 0x00, 0x00, 0x00, //0x00002e8b ja           LBB1_658
	0xe9, 0x77, 0x07, 0x00, 0x00, //0x00002e91 jmp          LBB1_720
	//0x00002e96 LBB1_674
	0x48, 0x89, 0xc1, //0x00002e96 movq         %rax, %rcx
	0x49, 0x89, 0xcf, //0x00002e99 movq         %rcx, %r15
	0x48, 0x39, 0xd1, //0x00002e9c cmpq         %rdx, %rcx
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x00002e9f jb           LBB1_658
	0xe9, 0x3b, 0x01, 0x00, 0x00, //0x00002ea5 jmp          LBB1_719
	//0x00002eaa LBB1_656
	0x48, 0x89, 0xc8, //0x00002eaa movq         %rcx, %rax
	0x49, 0x89, 0xcf, //0x00002ead movq         %rcx, %r15
	0x48, 0x39, 0xd1, //0x00002eb0 cmpq         %rdx, %rcx
	0x0f, 0x83, 0x2c, 0x01, 0x00, 0x00, //0x00002eb3 jae          LBB1_719
	//0x00002eb9 LBB1_658
	0x49, 0x8d, 0x47, 0x01, //0x00002eb9 leaq         $1(%r15), %rax
	0x41, 0x0f, 0xb6, 0x0f, //0x00002ebd movzbl       (%r15), %ecx
	0x80, 0xf9, 0x5c, //0x00002ec1 cmpb         $92, %cl
	0x0f, 0x85, 0xe6, 0x00, 0x00, 0x00, //0x00002ec4 jne          LBB1_671
	0x49, 0x8d, 0x4f, 0x02, //0x00002eca leaq         $2(%r15), %rcx
	0x48, 0x39, 0xd1, //0x00002ece cmpq         %rdx, %rcx
	0x0f, 0x87, 0x3b, 0x0a, 0x00, 0x00, //0x00002ed1 ja           LBB1_827
	0x0f, 0xb6, 0x00, //0x00002ed7 movzbl       (%rax), %eax
	0x3c, 0x6e, //0x00002eda cmpb         $110, %al
	0x0f, 0x84, 0xc8, 0xff, 0xff, 0xff, //0x00002edc je           LBB1_656
	0x3c, 0x72, //0x00002ee2 cmpb         $114, %al
	0x0f, 0x84, 0xc0, 0xff, 0xff, 0xff, //0x00002ee4 je           LBB1_656
	0x3c, 0x75, //0x00002eea cmpb         $117, %al
	0x0f, 0x85, 0x15, 0x0a, 0x00, 0x00, //0x00002eec jne          LBB1_847
	0x48, 0x89, 0xd0, //0x00002ef2 movq         %rdx, %rax
	0x48, 0x29, 0xc8, //0x00002ef5 subq         %rcx, %rax
	0x48, 0x83, 0xf8, 0x04, //0x00002ef8 cmpq         $4, %rax
	0x0f, 0x8c, 0x05, 0x0a, 0x00, 0x00, //0x00002efc jl           LBB1_847
	0x8b, 0x01, //0x00002f02 movl         (%rcx), %eax
	0x89, 0xc2, //0x00002f04 movl         %eax, %edx
	0xf7, 0xd2, //0x00002f06 notl         %edx
	0x8d, 0xb0, 0xd0, 0xcf, 0xcf, 0xcf, //0x00002f08 leal         $-808464432(%rax), %esi
	0x81, 0xe2, 0x80, 0x80, 0x80, 0x80, //0x00002f0e andl         $-2139062144, %edx
	0x85, 0xf2, //0x00002f14 testl        %esi, %edx
	0x0f, 0x85, 0xeb, 0x09, 0x00, 0x00, //0x00002f16 jne          LBB1_847
	0x8d, 0xb0, 0x19, 0x19, 0x19, 0x19, //0x00002f1c leal         $421075225(%rax), %esi
	0x09, 0xc6, //0x00002f22 orl          %eax, %esi
	0xf7, 0xc6, 0x80, 0x80, 0x80, 0x80, //0x00002f24 testl        $-2139062144, %esi
	0x0f, 0x85, 0xd7, 0x09, 0x00, 0x00, //0x00002f2a jne          LBB1_847
	0x89, 0xc6, //0x00002f30 movl         %eax, %esi
	0x81, 0xe6, 0x7f, 0x7f, 0x7f, 0x7f, //0x00002f32 andl         $2139062143, %esi
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x00002f38 movl         $-1061109568, %edi
	0x29, 0xf7, //0x00002f3d subl         %esi, %edi
	0x8d, 0x9e, 0x46, 0x46, 0x46, 0x46, //0x00002f3f leal         $1179010630(%rsi), %ebx
	0x21, 0xd7, //0x00002f45 andl         %edx, %edi
	0x85, 0xdf, //0x00002f47 testl        %ebx, %edi
	0x0f, 0x85, 0xb8, 0x09, 0x00, 0x00, //0x00002f49 jne          LBB1_847
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x00002f4f movl         $-522133280, %edi
	0x29, 0xf7, //0x00002f54 subl         %esi, %edi
	0x81, 0xc6, 0x39, 0x39, 0x39, 0x39, //0x00002f56 addl         $960051513, %esi
	0x21, 0xfa, //0x00002f5c andl         %edi, %edx
	0x85, 0xf2, //0x00002f5e testl        %esi, %edx
	0x0f, 0x85, 0xa1, 0x09, 0x00, 0x00, //0x00002f60 jne          LBB1_847
	0x0f, 0xc8, //0x00002f66 bswapl       %eax
	0x89, 0xc1, //0x00002f68 movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x00002f6a shrl         $4, %ecx
	0xf7, 0xd1, //0x00002f6d notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x00002f6f andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x00002f75 leal         (%rcx,%rcx,8), %ecx
	0x25, 0x0f, 0x0f, 0x0f, 0x0f, //0x00002f78 andl         $252645135, %eax
	0x01, 0xc8, //0x00002f7d addl         %ecx, %eax
	0x89, 0xc1, //0x00002f7f movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x00002f81 shrl         $4, %ecx
	0x09, 0xc1, //0x00002f84 orl          %eax, %ecx
	0x89, 0xc8, //0x00002f86 movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x00002f88 shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x00002f8b andl         $65280, %eax
	0x89, 0xca, //0x00002f90 movl         %ecx, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00002f92 andl         $128, %edx
	0x09, 0xc2, //0x00002f98 orl          %eax, %edx
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x00002f9a je           LBB1_670
	0xb9, 0xff, 0x00, 0x00, 0x00, //0x00002fa0 movl         $255, %ecx
	//0x00002fa5 LBB1_670
	0x49, 0x83, 0xc7, 0x06, //0x00002fa5 addq         $6, %r15
	0x4c, 0x89, 0xf8, //0x00002fa9 movq         %r15, %rax
	0x48, 0x8b, 0x55, 0xd0, //0x00002fac movq         $-48(%rbp), %rdx
	//0x00002fb0 LBB1_671
	0x80, 0xf9, 0x0a, //0x00002fb0 cmpb         $10, %cl
	0x0f, 0x84, 0xdd, 0xfe, 0xff, 0xff, //0x00002fb3 je           LBB1_674
	0x80, 0xf9, 0x0d, //0x00002fb9 cmpb         $13, %cl
	0x0f, 0x84, 0xd4, 0xfe, 0xff, 0xff, //0x00002fbc je           LBB1_674
	0x80, 0xf9, 0x3d, //0x00002fc2 cmpb         $61, %cl
	0x0f, 0x85, 0x47, 0x09, 0x00, 0x00, //0x00002fc5 jne          LBB1_827
	0x49, 0x89, 0xc7, //0x00002fcb movq         %rax, %r15
	0x41, 0x83, 0xfa, 0x02, //0x00002fce cmpl         $2, %r10d
	0x0f, 0x84, 0x19, 0x01, 0x00, 0x00, //0x00002fd2 je           LBB1_696
	0x48, 0x8b, 0x75, 0xd0, //0x00002fd8 movq         $-48(%rbp), %rsi
	0x48, 0x39, 0xc6, //0x00002fdc cmpq         %rax, %rsi
	0x0f, 0x87, 0x32, 0x04, 0x00, 0x00, //0x00002fdf ja           LBB1_762
	//0x00002fe5 LBB1_719
	0x49, 0x89, 0xc7, //0x00002fe5 movq         %rax, %r15
	0xe9, 0x20, 0x06, 0x00, 0x00, //0x00002fe8 jmp          LBB1_720
	//0x00002fed LBB1_676
	0x49, 0xff, 0xc7, //0x00002fed incq         %r15
	0x41, 0xbb, 0x03, 0x00, 0x00, 0x00, //0x00002ff0 movl         $3, %r11d
	0x49, 0x39, 0xdf, //0x00002ff6 cmpq         %rbx, %r15
	0x0f, 0x83, 0xd5, 0xfb, 0xff, 0xff, //0x00002ff9 jae          LBB1_610
	//0x00002fff LBB1_677
	0x41, 0x0f, 0xb6, 0x0f, //0x00002fff movzbl       (%r15), %ecx
	0x48, 0x83, 0xf9, 0x0d, //0x00003003 cmpq         $13, %rcx
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x00003007 je           LBB1_676
	0x80, 0xf9, 0x0a, //0x0000300d cmpb         $10, %cl
	0x0f, 0x84, 0xd7, 0xff, 0xff, 0xff, //0x00003010 je           LBB1_676
	0x48, 0x8b, 0x45, 0xc0, //0x00003016 movq         $-64(%rbp), %rax
	0x0f, 0xb6, 0x04, 0x08, //0x0000301a movzbl       (%rax,%rcx), %eax
	0x49, 0xff, 0xc7, //0x0000301e incq         %r15
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x00003021 cmpl         $255, %eax
	0x0f, 0x85, 0xa0, 0x05, 0x00, 0x00, //0x00003026 jne          LBB1_790
	//0x0000302c LBB1_791
	0x41, 0xbb, 0x03, 0x00, 0x00, 0x00, //0x0000302c movl         $3, %r11d
	0xe9, 0x73, 0xfc, 0xff, 0xff, //0x00003032 jmp          LBB1_624
	//0x00003037 LBB1_681
	0x49, 0xff, 0xc7, //0x00003037 incq         %r15
	0x48, 0xff, 0xc1, //0x0000303a incq         %rcx
	0x48, 0xff, 0xcb, //0x0000303d decq         %rbx
	0x48, 0xff, 0xc2, //0x00003040 incq         %rdx
	0x48, 0xff, 0xcf, //0x00003043 decq         %rdi
	0x4c, 0x39, 0x7d, 0xd0, //0x00003046 cmpq         %r15, $-48(%rbp)
	0x0f, 0x84, 0xb9, 0x05, 0x00, 0x00, //0x0000304a je           LBB1_717
	//0x00003050 LBB1_682
	0x41, 0x0f, 0xb6, 0x07, //0x00003050 movzbl       (%r15), %eax
	0x3c, 0x0a, //0x00003054 cmpb         $10, %al
	0x0f, 0x84, 0xdb, 0xff, 0xff, 0xff, //0x00003056 je           LBB1_681
	0x3c, 0x0d, //0x0000305c cmpb         $13, %al
	0x0f, 0x84, 0xd3, 0xff, 0xff, 0xff, //0x0000305e je           LBB1_681
	0x3c, 0x3d, //0x00003064 cmpb         $61, %al
	0x0f, 0x85, 0xf9, 0x01, 0x00, 0x00, //0x00003066 jne          LBB1_728
	0x49, 0xff, 0xc7, //0x0000306c incq         %r15
	0x41, 0x83, 0xfa, 0x03, //0x0000306f cmpl         $3, %r10d
	0x0f, 0x84, 0x78, 0x00, 0x00, 0x00, //0x00003073 je           LBB1_696
	0x4c, 0x39, 0x7d, 0xd0, //0x00003079 cmpq         %r15, $-48(%rbp)
	0x0f, 0x87, 0xe7, 0x04, 0x00, 0x00, //0x0000307d ja           LBB1_780
	0xe9, 0x85, 0x05, 0x00, 0x00, //0x00003083 jmp          LBB1_720
	//0x00003088 LBB1_688
	0x3c, 0x2f, //0x00003088 cmpb         $47, %al
	0x0f, 0x85, 0x02, 0x00, 0x00, 0x00, //0x0000308a jne          LBB1_690
	0x89, 0xc1, //0x00003090 movl         %eax, %ecx
	//0x00003092 LBB1_690
	0x4d, 0x89, 0xc7, //0x00003092 movq         %r8, %r15
	0x48, 0x8b, 0x5d, 0xd0, //0x00003095 movq         $-48(%rbp), %rbx
	//0x00003099 LBB1_691
	0x0f, 0xb6, 0xc1, //0x00003099 movzbl       %cl, %eax
	0x48, 0x8b, 0x55, 0xc0, //0x0000309c movq         $-64(%rbp), %rdx
	0x0f, 0xb6, 0x04, 0x02, //0x000030a0 movzbl       (%rdx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x000030a4 cmpl         $255, %eax
	0x0f, 0x84, 0x70, 0x00, 0x00, 0x00, //0x000030a9 je           LBB1_716
	0x8b, 0x4d, 0xc8, //0x000030af movl         $-56(%rbp), %ecx
	0xc1, 0xe1, 0x06, //0x000030b2 shll         $6, %ecx
	0x09, 0xc1, //0x000030b5 orl          %eax, %ecx
	0x89, 0x4d, 0xc8, //0x000030b7 movl         %ecx, $-56(%rbp)
	0x41, 0xbb, 0x02, 0x00, 0x00, 0x00, //0x000030ba movl         $2, %r11d
	0x49, 0x39, 0xdf, //0x000030c0 cmpq         %rbx, %r15
	0x0f, 0x82, 0x7e, 0x00, 0x00, 0x00, //0x000030c3 jb           LBB1_701
	//0x000030c9 LBB1_693
	0x45, 0x85, 0xe4, //0x000030c9 testl        %r12d, %r12d
	0x0f, 0x94, 0xc0, //0x000030cc sete         %al
	0x41, 0x83, 0xfb, 0x01, //0x000030cf cmpl         $1, %r11d
	0x0f, 0x94, 0xc1, //0x000030d3 sete         %cl
	0x49, 0x39, 0xdf, //0x000030d6 cmpq         %rbx, %r15
	0x0f, 0x82, 0x2e, 0x05, 0x00, 0x00, //0x000030d9 jb           LBB1_720
	0x41, 0x83, 0xfb, 0x04, //0x000030df cmpl         $4, %r11d
	0x0f, 0x84, 0x24, 0x05, 0x00, 0x00, //0x000030e3 je           LBB1_720
	0x08, 0xc8, //0x000030e9 orb          %cl, %al
	0x0f, 0x84, 0x1c, 0x05, 0x00, 0x00, //0x000030eb je           LBB1_720
	//0x000030f1 LBB1_696
	0x49, 0x8d, 0x4f, 0x01, //0x000030f1 leaq         $1(%r15), %rcx
	0x4c, 0x39, 0x7d, 0xd0, //0x000030f5 cmpq         %r15, $-48(%rbp)
	0x49, 0x0f, 0x45, 0xcf, //0x000030f9 cmovneq      %r15, %rcx
	0x4c, 0x29, 0xc9, //0x000030fd subq         %r9, %rcx
	0x0f, 0x85, 0x9a, 0x18, 0x00, 0x00, //0x00003100 jne          LBB1_1137
	0x4d, 0x89, 0xcf, //0x00003106 movq         %r9, %r15
	0x48, 0x8b, 0x55, 0xb0, //0x00003109 movq         $-80(%rbp), %rdx
	0x48, 0x89, 0x55, 0xb0, //0x0000310d movq         %rdx, $-80(%rbp)
	0x49, 0x39, 0xd6, //0x00003111 cmpq         %rdx, %r14
	0x0f, 0x83, 0x55, 0xf8, 0xff, 0xff, //0x00003114 jae          LBB1_727
	0xe9, 0x0b, 0x08, 0x00, 0x00, //0x0000311a jmp          LBB1_852
	//0x0000311f LBB1_716
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x0000311f movl         $1, %r11d
	0xe9, 0x80, 0xfb, 0xff, 0xff, //0x00003125 jmp          LBB1_624
	//0x0000312a LBB1_698
	0x3c, 0x6e, //0x0000312a cmpb         $110, %al
	0x0f, 0x85, 0x41, 0x01, 0x00, 0x00, //0x0000312c jne          LBB1_734
	//0x00003132 LBB1_699
	0x48, 0x89, 0xd7, //0x00003132 movq         %rdx, %rdi
	//0x00003135 LBB1_700
	0x41, 0xbb, 0x02, 0x00, 0x00, 0x00, //0x00003135 movl         $2, %r11d
	0x49, 0x89, 0xff, //0x0000313b movq         %rdi, %r15
	0x48, 0x39, 0xdf, //0x0000313e cmpq         %rbx, %rdi
	0x0f, 0x83, 0x16, 0x01, 0x00, 0x00, //0x00003141 jae          LBB1_718
	//0x00003147 LBB1_701
	0x49, 0x8d, 0x7f, 0x01, //0x00003147 leaq         $1(%r15), %rdi
	0x41, 0x0f, 0xb6, 0x0f, //0x0000314b movzbl       (%r15), %ecx
	0x80, 0xf9, 0x5c, //0x0000314f cmpb         $92, %cl
	0x0f, 0x85, 0xee, 0x00, 0x00, 0x00, //0x00003152 jne          LBB1_714
	0x49, 0x8d, 0x57, 0x02, //0x00003158 leaq         $2(%r15), %rdx
	0xb1, 0xff, //0x0000315c movb         $-1, %cl
	0x48, 0x39, 0xda, //0x0000315e cmpq         %rbx, %rdx
	0x0f, 0x87, 0x19, 0x01, 0x00, 0x00, //0x00003161 ja           LBB1_737
	0x0f, 0xb6, 0x07, //0x00003167 movzbl       (%rdi), %eax
	0x3c, 0x71, //0x0000316a cmpb         $113, %al
	0x0f, 0x8e, 0xb8, 0xff, 0xff, 0xff, //0x0000316c jle          LBB1_698
	0x3c, 0x72, //0x00003172 cmpb         $114, %al
	0x0f, 0x84, 0xb8, 0xff, 0xff, 0xff, //0x00003174 je           LBB1_699
	0x3c, 0x75, //0x0000317a cmpb         $117, %al
	0x0f, 0x85, 0xfb, 0x00, 0x00, 0x00, //0x0000317c jne          LBB1_736
	0x48, 0x89, 0xd8, //0x00003182 movq         %rbx, %rax
	0x48, 0x29, 0xd0, //0x00003185 subq         %rdx, %rax
	0x48, 0x83, 0xf8, 0x04, //0x00003188 cmpq         $4, %rax
	0x0f, 0x8c, 0xeb, 0x00, 0x00, 0x00, //0x0000318c jl           LBB1_736
	0x8b, 0x02, //0x00003192 movl         (%rdx), %eax
	0x89, 0xc6, //0x00003194 movl         %eax, %esi
	0xf7, 0xd6, //0x00003196 notl         %esi
	0x8d, 0xb8, 0xd0, 0xcf, 0xcf, 0xcf, //0x00003198 leal         $-808464432(%rax), %edi
	0x81, 0xe6, 0x80, 0x80, 0x80, 0x80, //0x0000319e andl         $-2139062144, %esi
	0x85, 0xfe, //0x000031a4 testl        %edi, %esi
	0x0f, 0x85, 0xd1, 0x00, 0x00, 0x00, //0x000031a6 jne          LBB1_736
	0x8d, 0xb8, 0x19, 0x19, 0x19, 0x19, //0x000031ac leal         $421075225(%rax), %edi
	0x09, 0xc7, //0x000031b2 orl          %eax, %edi
	0xf7, 0xc7, 0x80, 0x80, 0x80, 0x80, //0x000031b4 testl        $-2139062144, %edi
	0x0f, 0x85, 0xbd, 0x00, 0x00, 0x00, //0x000031ba jne          LBB1_736
	0x89, 0xc7, //0x000031c0 movl         %eax, %edi
	0x81, 0xe7, 0x7f, 0x7f, 0x7f, 0x7f, //0x000031c2 andl         $2139062143, %edi
	0xbb, 0xc0, 0xc0, 0xc0, 0xc0, //0x000031c8 movl         $-1061109568, %ebx
	0x29, 0xfb, //0x000031cd subl         %edi, %ebx
	0x44, 0x8d, 0x87, 0x46, 0x46, 0x46, 0x46, //0x000031cf leal         $1179010630(%rdi), %r8d
	0x21, 0xf3, //0x000031d6 andl         %esi, %ebx
	0x44, 0x85, 0xc3, //0x000031d8 testl        %r8d, %ebx
	0x48, 0x8b, 0x5d, 0xd0, //0x000031db movq         $-48(%rbp), %rbx
	0x0f, 0x85, 0x98, 0x00, 0x00, 0x00, //0x000031df jne          LBB1_736
	0xbb, 0xe0, 0xe0, 0xe0, 0xe0, //0x000031e5 movl         $-522133280, %ebx
	0x29, 0xfb, //0x000031ea subl         %edi, %ebx
	0x81, 0xc7, 0x39, 0x39, 0x39, 0x39, //0x000031ec addl         $960051513, %edi
	0x21, 0xde, //0x000031f2 andl         %ebx, %esi
	0x48, 0x8b, 0x5d, 0xd0, //0x000031f4 movq         $-48(%rbp), %rbx
	0x85, 0xfe, //0x000031f8 testl        %edi, %esi
	0x0f, 0x85, 0x7d, 0x00, 0x00, 0x00, //0x000031fa jne          LBB1_736
	0x0f, 0xc8, //0x00003200 bswapl       %eax
	0x89, 0xc1, //0x00003202 movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x00003204 shrl         $4, %ecx
	0xf7, 0xd1, //0x00003207 notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x00003209 andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x0000320f leal         (%rcx,%rcx,8), %ecx
	0x25, 0x0f, 0x0f, 0x0f, 0x0f, //0x00003212 andl         $252645135, %eax
	0x01, 0xc8, //0x00003217 addl         %ecx, %eax
	0x89, 0xc1, //0x00003219 movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x0000321b shrl         $4, %ecx
	0x09, 0xc1, //0x0000321e orl          %eax, %ecx
	0x89, 0xc8, //0x00003220 movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x00003222 shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x00003225 andl         $65280, %eax
	0x89, 0xca, //0x0000322a movl         %ecx, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x0000322c andl         $128, %edx
	0x09, 0xc2, //0x00003232 orl          %eax, %edx
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x00003234 je           LBB1_713
	0xb9, 0xff, 0x00, 0x00, 0x00, //0x0000323a movl         $255, %ecx
	//0x0000323f LBB1_713
	0x49, 0x83, 0xc7, 0x06, //0x0000323f addq         $6, %r15
	0x4c, 0x89, 0xff, //0x00003243 movq         %r15, %rdi
	//0x00003246 LBB1_714
	0x80, 0xf9, 0x0a, //0x00003246 cmpb         $10, %cl
	0x0f, 0x84, 0xe6, 0xfe, 0xff, 0xff, //0x00003249 je           LBB1_700
	0x80, 0xf9, 0x0d, //0x0000324f cmpb         $13, %cl
	0x0f, 0x84, 0xdd, 0xfe, 0xff, 0xff, //0x00003252 je           LBB1_700
	0xe9, 0x23, 0x00, 0x00, 0x00, //0x00003258 jmp          LBB1_737
	//0x0000325d LBB1_718
	0x49, 0x89, 0xff, //0x0000325d movq         %rdi, %r15
	0xe9, 0x6f, 0xf9, 0xff, 0xff, //0x00003260 jmp          LBB1_610
	//0x00003265 LBB1_728
	0x49, 0xff, 0xc7, //0x00003265 incq         %r15
	0x4c, 0x89, 0xfa, //0x00003268 movq         %r15, %rdx
	//0x0000326b LBB1_729
	0x49, 0x89, 0xd7, //0x0000326b movq         %rdx, %r15
	0xe9, 0x7e, 0xfe, 0xff, 0xff, //0x0000326e jmp          LBB1_696
	//0x00003273 LBB1_734
	0x3c, 0x2f, //0x00003273 cmpb         $47, %al
	0x0f, 0x85, 0x02, 0x00, 0x00, 0x00, //0x00003275 jne          LBB1_736
	0x89, 0xc1, //0x0000327b movl         %eax, %ecx
	//0x0000327d LBB1_736
	0x48, 0x89, 0xd7, //0x0000327d movq         %rdx, %rdi
	//0x00003280 LBB1_737
	0x0f, 0xb6, 0xc1, //0x00003280 movzbl       %cl, %eax
	0x48, 0x8b, 0x55, 0xc0, //0x00003283 movq         $-64(%rbp), %rdx
	0x0f, 0xb6, 0x04, 0x02, //0x00003287 movzbl       (%rdx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x0000328b cmpl         $255, %eax
	0x0f, 0x84, 0x50, 0x01, 0x00, 0x00, //0x00003290 je           LBB1_758
	0x8b, 0x4d, 0xc8, //0x00003296 movl         $-56(%rbp), %ecx
	0xc1, 0xe1, 0x06, //0x00003299 shll         $6, %ecx
	0x09, 0xc1, //0x0000329c orl          %eax, %ecx
	0x89, 0x4d, 0xc8, //0x0000329e movl         %ecx, $-56(%rbp)
	0x41, 0xbb, 0x03, 0x00, 0x00, 0x00, //0x000032a1 movl         $3, %r11d
	0x48, 0x39, 0xdf, //0x000032a7 cmpq         %rbx, %rdi
	0x0f, 0x82, 0x25, 0x00, 0x00, 0x00, //0x000032aa jb           LBB1_743
	0x49, 0x89, 0xff, //0x000032b0 movq         %rdi, %r15
	0xe9, 0x11, 0xfe, 0xff, 0xff, //0x000032b3 jmp          LBB1_693
	//0x000032b8 LBB1_740
	0x3c, 0x6e, //0x000032b8 cmpb         $110, %al
	0x0f, 0x85, 0xe5, 0x02, 0x00, 0x00, //0x000032ba jne          LBB1_786
	//0x000032c0 LBB1_741
	0x4d, 0x89, 0xc7, //0x000032c0 movq         %r8, %r15
	//0x000032c3 LBB1_742
	0x41, 0xbb, 0x03, 0x00, 0x00, 0x00, //0x000032c3 movl         $3, %r11d
	0x4c, 0x89, 0xff, //0x000032c9 movq         %r15, %rdi
	0x49, 0x39, 0xdf, //0x000032cc cmpq         %rbx, %r15
	0x0f, 0x83, 0xff, 0xf8, 0xff, 0xff, //0x000032cf jae          LBB1_610
	//0x000032d5 LBB1_743
	0x4c, 0x8d, 0x7f, 0x01, //0x000032d5 leaq         $1(%rdi), %r15
	0x0f, 0xb6, 0x0f, //0x000032d9 movzbl       (%rdi), %ecx
	0x80, 0xf9, 0x5c, //0x000032dc cmpb         $92, %cl
	0x0f, 0x85, 0xea, 0x00, 0x00, 0x00, //0x000032df jne          LBB1_756
	0x4c, 0x8d, 0x47, 0x02, //0x000032e5 leaq         $2(%rdi), %r8
	0xb1, 0xff, //0x000032e9 movb         $-1, %cl
	0x49, 0x39, 0xd8, //0x000032eb cmpq         %rbx, %r8
	0x0f, 0x87, 0xc2, 0x02, 0x00, 0x00, //0x000032ee ja           LBB1_789
	0x41, 0x0f, 0xb6, 0x07, //0x000032f4 movzbl       (%r15), %eax
	0x3c, 0x71, //0x000032f8 cmpb         $113, %al
	0x0f, 0x8e, 0xb8, 0xff, 0xff, 0xff, //0x000032fa jle          LBB1_740
	0x3c, 0x72, //0x00003300 cmpb         $114, %al
	0x0f, 0x84, 0xb8, 0xff, 0xff, 0xff, //0x00003302 je           LBB1_741
	0x3c, 0x75, //0x00003308 cmpb         $117, %al
	0x0f, 0x85, 0x9f, 0x02, 0x00, 0x00, //0x0000330a jne          LBB1_788
	0x48, 0x8b, 0x45, 0xd0, //0x00003310 movq         $-48(%rbp), %rax
	0x4c, 0x29, 0xc0, //0x00003314 subq         %r8, %rax
	0x48, 0x83, 0xf8, 0x04, //0x00003317 cmpq         $4, %rax
	0x0f, 0x8c, 0x8e, 0x02, 0x00, 0x00, //0x0000331b jl           LBB1_788
	0x41, 0x8b, 0x18, //0x00003321 movl         (%r8), %ebx
	0x89, 0xde, //0x00003324 movl         %ebx, %esi
	0xf7, 0xd6, //0x00003326 notl         %esi
	0x8d, 0x83, 0xd0, 0xcf, 0xcf, 0xcf, //0x00003328 leal         $-808464432(%rbx), %eax
	0x81, 0xe6, 0x80, 0x80, 0x80, 0x80, //0x0000332e andl         $-2139062144, %esi
	0x85, 0xc6, //0x00003334 testl        %eax, %esi
	0x0f, 0x85, 0x73, 0x02, 0x00, 0x00, //0x00003336 jne          LBB1_788
	0x8d, 0x83, 0x19, 0x19, 0x19, 0x19, //0x0000333c leal         $421075225(%rbx), %eax
	0x09, 0xd8, //0x00003342 orl          %ebx, %eax
	0xa9, 0x80, 0x80, 0x80, 0x80, //0x00003344 testl        $-2139062144, %eax
	0x0f, 0x85, 0x60, 0x02, 0x00, 0x00, //0x00003349 jne          LBB1_788
	0x89, 0xd8, //0x0000334f movl         %ebx, %eax
	0x25, 0x7f, 0x7f, 0x7f, 0x7f, //0x00003351 andl         $2139062143, %eax
	0xba, 0xc0, 0xc0, 0xc0, 0xc0, //0x00003356 movl         $-1061109568, %edx
	0x29, 0xc2, //0x0000335b subl         %eax, %edx
	0x44, 0x8d, 0x90, 0x46, 0x46, 0x46, 0x46, //0x0000335d leal         $1179010630(%rax), %r10d
	0x21, 0xf2, //0x00003364 andl         %esi, %edx
	0x44, 0x85, 0xd2, //0x00003366 testl        %r10d, %edx
	0x0f, 0x85, 0x40, 0x02, 0x00, 0x00, //0x00003369 jne          LBB1_788
	0xba, 0xe0, 0xe0, 0xe0, 0xe0, //0x0000336f movl         $-522133280, %edx
	0x29, 0xc2, //0x00003374 subl         %eax, %edx
	0x05, 0x39, 0x39, 0x39, 0x39, //0x00003376 addl         $960051513, %eax
	0x21, 0xd6, //0x0000337b andl         %edx, %esi
	0x85, 0xc6, //0x0000337d testl        %eax, %esi
	0x0f, 0x85, 0x2a, 0x02, 0x00, 0x00, //0x0000337f jne          LBB1_788
	0x0f, 0xcb, //0x00003385 bswapl       %ebx
	0x89, 0xd8, //0x00003387 movl         %ebx, %eax
	0xc1, 0xe8, 0x04, //0x00003389 shrl         $4, %eax
	0xf7, 0xd0, //0x0000338c notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x0000338e andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00003393 leal         (%rax,%rax,8), %eax
	0x81, 0xe3, 0x0f, 0x0f, 0x0f, 0x0f, //0x00003396 andl         $252645135, %ebx
	0x01, 0xc3, //0x0000339c addl         %eax, %ebx
	0x89, 0xd9, //0x0000339e movl         %ebx, %ecx
	0xc1, 0xe9, 0x04, //0x000033a0 shrl         $4, %ecx
	0x09, 0xd9, //0x000033a3 orl          %ebx, %ecx
	0x89, 0xc8, //0x000033a5 movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x000033a7 shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x000033aa andl         $65280, %eax
	0x89, 0xca, //0x000033af movl         %ecx, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x000033b1 andl         $128, %edx
	0x09, 0xc2, //0x000033b7 orl          %eax, %edx
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x000033b9 je           LBB1_755
	0xb9, 0xff, 0x00, 0x00, 0x00, //0x000033bf movl         $255, %ecx
	//0x000033c4 LBB1_755
	0x48, 0x83, 0xc7, 0x06, //0x000033c4 addq         $6, %rdi
	0x49, 0x89, 0xff, //0x000033c8 movq         %rdi, %r15
	0x48, 0x8b, 0x5d, 0xd0, //0x000033cb movq         $-48(%rbp), %rbx
	//0x000033cf LBB1_756
	0x80, 0xf9, 0x0a, //0x000033cf cmpb         $10, %cl
	0x0f, 0x84, 0xeb, 0xfe, 0xff, 0xff, //0x000033d2 je           LBB1_742
	0x80, 0xf9, 0x0d, //0x000033d8 cmpb         $13, %cl
	0x0f, 0x84, 0xe2, 0xfe, 0xff, 0xff, //0x000033db je           LBB1_742
	0xe9, 0xd0, 0x01, 0x00, 0x00, //0x000033e1 jmp          LBB1_789
	//0x000033e6 LBB1_758
	0x41, 0xbb, 0x02, 0x00, 0x00, 0x00, //0x000033e6 movl         $2, %r11d
	0x49, 0x89, 0xff, //0x000033ec movq         %rdi, %r15
	0xe9, 0xb6, 0xf8, 0xff, 0xff, //0x000033ef jmp          LBB1_624
	//0x000033f4 LBB1_778
	0x4c, 0x89, 0xf9, //0x000033f4 movq         %r15, %rcx
	0x48, 0x89, 0xc8, //0x000033f7 movq         %rcx, %rax
	0x48, 0x39, 0xf1, //0x000033fa cmpq         %rsi, %rcx
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x000033fd jb           LBB1_762
	0xe9, 0x05, 0x02, 0x00, 0x00, //0x00003403 jmp          LBB1_720
	//0x00003408 LBB1_760
	0x49, 0x89, 0xcf, //0x00003408 movq         %rcx, %r15
	0x48, 0x89, 0xc8, //0x0000340b movq         %rcx, %rax
	0x48, 0x39, 0xf1, //0x0000340e cmpq         %rsi, %rcx
	0x0f, 0x83, 0xf6, 0x01, 0x00, 0x00, //0x00003411 jae          LBB1_720
	//0x00003417 LBB1_762
	0x4c, 0x8d, 0x78, 0x01, //0x00003417 leaq         $1(%rax), %r15
	0x0f, 0xb6, 0x08, //0x0000341b movzbl       (%rax), %ecx
	0x80, 0xf9, 0x5c, //0x0000341e cmpb         $92, %cl
	0x0f, 0x85, 0xee, 0x00, 0x00, 0x00, //0x00003421 jne          LBB1_775
	0x48, 0x8d, 0x48, 0x02, //0x00003427 leaq         $2(%rax), %rcx
	0x48, 0x39, 0xf1, //0x0000342b cmpq         %rsi, %rcx
	0x0f, 0x87, 0xdb, 0x04, 0x00, 0x00, //0x0000342e ja           LBB1_848
	0x41, 0x0f, 0xb6, 0x17, //0x00003434 movzbl       (%r15), %edx
	0x80, 0xfa, 0x6e, //0x00003438 cmpb         $110, %dl
	0x0f, 0x84, 0xc7, 0xff, 0xff, 0xff, //0x0000343b je           LBB1_760
	0x80, 0xfa, 0x72, //0x00003441 cmpb         $114, %dl
	0x0f, 0x84, 0xbe, 0xff, 0xff, 0xff, //0x00003444 je           LBB1_760
	0x80, 0xfa, 0x75, //0x0000344a cmpb         $117, %dl
	0x0f, 0x85, 0xb4, 0x04, 0x00, 0x00, //0x0000344d jne          LBB1_847
	0x48, 0x89, 0xf2, //0x00003453 movq         %rsi, %rdx
	0x48, 0x29, 0xca, //0x00003456 subq         %rcx, %rdx
	0x48, 0x83, 0xfa, 0x04, //0x00003459 cmpq         $4, %rdx
	0x0f, 0x8c, 0xa4, 0x04, 0x00, 0x00, //0x0000345d jl           LBB1_847
	0x8b, 0x11, //0x00003463 movl         (%rcx), %edx
	0x89, 0xd6, //0x00003465 movl         %edx, %esi
	0xf7, 0xd6, //0x00003467 notl         %esi
	0x8d, 0xba, 0xd0, 0xcf, 0xcf, 0xcf, //0x00003469 leal         $-808464432(%rdx), %edi
	0x81, 0xe6, 0x80, 0x80, 0x80, 0x80, //0x0000346f andl         $-2139062144, %esi
	0x85, 0xfe, //0x00003475 testl        %edi, %esi
	0x0f, 0x85, 0x8a, 0x04, 0x00, 0x00, //0x00003477 jne          LBB1_847
	0x8d, 0xba, 0x19, 0x19, 0x19, 0x19, //0x0000347d leal         $421075225(%rdx), %edi
	0x09, 0xd7, //0x00003483 orl          %edx, %edi
	0xf7, 0xc7, 0x80, 0x80, 0x80, 0x80, //0x00003485 testl        $-2139062144, %edi
	0x0f, 0x85, 0x76, 0x04, 0x00, 0x00, //0x0000348b jne          LBB1_847
	0x89, 0xd7, //0x00003491 movl         %edx, %edi
	0x81, 0xe7, 0x7f, 0x7f, 0x7f, 0x7f, //0x00003493 andl         $2139062143, %edi
	0xbb, 0xc0, 0xc0, 0xc0, 0xc0, //0x00003499 movl         $-1061109568, %ebx
	0x29, 0xfb, //0x0000349e subl         %edi, %ebx
	0x44, 0x8d, 0x87, 0x46, 0x46, 0x46, 0x46, //0x000034a0 leal         $1179010630(%rdi), %r8d
	0x21, 0xf3, //0x000034a7 andl         %esi, %ebx
	0x44, 0x85, 0xc3, //0x000034a9 testl        %r8d, %ebx
	0x0f, 0x85, 0x55, 0x04, 0x00, 0x00, //0x000034ac jne          LBB1_847
	0xbb, 0xe0, 0xe0, 0xe0, 0xe0, //0x000034b2 movl         $-522133280, %ebx
	0x29, 0xfb, //0x000034b7 subl         %edi, %ebx
	0x81, 0xc7, 0x39, 0x39, 0x39, 0x39, //0x000034b9 addl         $960051513, %edi
	0x21, 0xde, //0x000034bf andl         %ebx, %esi
	0x85, 0xfe, //0x000034c1 testl        %edi, %esi
	0x0f, 0x85, 0x3e, 0x04, 0x00, 0x00, //0x000034c3 jne          LBB1_847
	0x0f, 0xca, //0x000034c9 bswapl       %edx
	0x89, 0xd1, //0x000034cb movl         %edx, %ecx
	0xc1, 0xe9, 0x04, //0x000034cd shrl         $4, %ecx
	0xf7, 0xd1, //0x000034d0 notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x000034d2 andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x000034d8 leal         (%rcx,%rcx,8), %ecx
	0x81, 0xe2, 0x0f, 0x0f, 0x0f, 0x0f, //0x000034db andl         $252645135, %edx
	0x01, 0xca, //0x000034e1 addl         %ecx, %edx
	0x89, 0xd1, //0x000034e3 movl         %edx, %ecx
	0xc1, 0xe9, 0x04, //0x000034e5 shrl         $4, %ecx
	0x09, 0xd1, //0x000034e8 orl          %edx, %ecx
	0x89, 0xca, //0x000034ea movl         %ecx, %edx
	0xc1, 0xea, 0x08, //0x000034ec shrl         $8, %edx
	0x81, 0xe2, 0x00, 0xff, 0x00, 0x00, //0x000034ef andl         $65280, %edx
	0x89, 0xce, //0x000034f5 movl         %ecx, %esi
	0x81, 0xe6, 0x80, 0x00, 0x00, 0x00, //0x000034f7 andl         $128, %esi
	0x09, 0xd6, //0x000034fd orl          %edx, %esi
	0x48, 0x8b, 0x75, 0xd0, //0x000034ff movq         $-48(%rbp), %rsi
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x00003503 je           LBB1_774
	0xb9, 0xff, 0x00, 0x00, 0x00, //0x00003509 movl         $255, %ecx
	//0x0000350e LBB1_774
	0x48, 0x83, 0xc0, 0x06, //0x0000350e addq         $6, %rax
	0x49, 0x89, 0xc7, //0x00003512 movq         %rax, %r15
	//0x00003515 LBB1_775
	0x80, 0xf9, 0x0a, //0x00003515 cmpb         $10, %cl
	0x0f, 0x84, 0xd6, 0xfe, 0xff, 0xff, //0x00003518 je           LBB1_778
	0x80, 0xf9, 0x0d, //0x0000351e cmpb         $13, %cl
	0x0f, 0x84, 0xcd, 0xfe, 0xff, 0xff, //0x00003521 je           LBB1_778
	0x80, 0xf9, 0x3d, //0x00003527 cmpb         $61, %cl
	0x0f, 0x85, 0xc1, 0xfb, 0xff, 0xff, //0x0000352a jne          LBB1_696
	0x41, 0x83, 0xfa, 0x03, //0x00003530 cmpl         $3, %r10d
	0x0f, 0x84, 0xb7, 0xfb, 0xff, 0xff, //0x00003534 je           LBB1_696
	0x4c, 0x39, 0x7d, 0xd0, //0x0000353a cmpq         %r15, $-48(%rbp)
	0x0f, 0x86, 0xc9, 0x00, 0x00, 0x00, //0x0000353e jbe          LBB1_720
	0x48, 0x8b, 0x55, 0xd0, //0x00003544 movq         $-48(%rbp), %rdx
	0xe9, 0x2c, 0x01, 0x00, 0x00, //0x00003548 jmp          LBB1_803
	//0x0000354d LBB1_759
	0x41, 0xbb, 0x02, 0x00, 0x00, 0x00, //0x0000354d movl         $2, %r11d
	0xe9, 0x52, 0xf7, 0xff, 0xff, //0x00003553 jmp          LBB1_624
	//0x00003558 LBB1_779
	0x48, 0xff, 0xc1, //0x00003558 incq         %rcx
	0x48, 0xff, 0xc2, //0x0000355b incq         %rdx
	0x48, 0xff, 0xcf, //0x0000355e decq         %rdi
	0x48, 0xff, 0xcb, //0x00003561 decq         %rbx
	0x0f, 0x84, 0x9f, 0x00, 0x00, 0x00, //0x00003564 je           LBB1_717
	//0x0000356a LBB1_780
	0x0f, 0xb6, 0x41, 0xff, //0x0000356a movzbl       $-1(%rcx), %eax
	0x3c, 0x0a, //0x0000356e cmpb         $10, %al
	0x0f, 0x84, 0xe2, 0xff, 0xff, 0xff, //0x00003570 je           LBB1_779
	0x3c, 0x0d, //0x00003576 cmpb         $13, %al
	0x0f, 0x84, 0xda, 0xff, 0xff, 0xff, //0x00003578 je           LBB1_779
	0x3c, 0x3d, //0x0000357e cmpb         $61, %al
	0x0f, 0x85, 0x16, 0x02, 0x00, 0x00, //0x00003580 jne          LBB1_821
	0x49, 0x89, 0xcf, //0x00003586 movq         %rcx, %r15
	0x41, 0x83, 0xfa, 0x04, //0x00003589 cmpl         $4, %r10d
	0x0f, 0x84, 0x5e, 0xfb, 0xff, 0xff, //0x0000358d je           LBB1_696
	0x48, 0x39, 0x4d, 0xd0, //0x00003593 cmpq         %rcx, $-48(%rbp)
	0x0f, 0x87, 0x51, 0x00, 0x00, 0x00, //0x00003597 ja           LBB1_798
	0x49, 0x89, 0xcf, //0x0000359d movq         %rcx, %r15
	0xe9, 0x68, 0x00, 0x00, 0x00, //0x000035a0 jmp          LBB1_720
	//0x000035a5 LBB1_786
	0x3c, 0x2f, //0x000035a5 cmpb         $47, %al
	0x0f, 0x85, 0x02, 0x00, 0x00, 0x00, //0x000035a7 jne          LBB1_788
	0x89, 0xc1, //0x000035ad movl         %eax, %ecx
	//0x000035af LBB1_788
	0x4d, 0x89, 0xc7, //0x000035af movq         %r8, %r15
	0x48, 0x8b, 0x5d, 0xd0, //0x000035b2 movq         $-48(%rbp), %rbx
	//0x000035b6 LBB1_789
	0x0f, 0xb6, 0xc1, //0x000035b6 movzbl       %cl, %eax
	0x48, 0x8b, 0x55, 0xc0, //0x000035b9 movq         $-64(%rbp), %rdx
	0x0f, 0xb6, 0x04, 0x02, //0x000035bd movzbl       (%rdx,%rax), %eax
	0x3d, 0xff, 0x00, 0x00, 0x00, //0x000035c1 cmpl         $255, %eax
	0x0f, 0x84, 0x60, 0xfa, 0xff, 0xff, //0x000035c6 je           LBB1_791
	//0x000035cc LBB1_790
	0x8b, 0x4d, 0xc8, //0x000035cc movl         $-56(%rbp), %ecx
	0xc1, 0xe1, 0x06, //0x000035cf shll         $6, %ecx
	0x09, 0xc1, //0x000035d2 orl          %eax, %ecx
	0x89, 0x4d, 0xc8, //0x000035d4 movl         %ecx, $-56(%rbp)
	0x41, 0xbb, 0x04, 0x00, 0x00, 0x00, //0x000035d7 movl         $4, %r11d
	0xe9, 0xe7, 0xfa, 0xff, 0xff, //0x000035dd jmp          LBB1_693
	//0x000035e2 LBB1_797
	0x48, 0xff, 0xc2, //0x000035e2 incq         %rdx
	0x48, 0xff, 0xcf, //0x000035e5 decq         %rdi
	0x0f, 0x84, 0x1b, 0x00, 0x00, 0x00, //0x000035e8 je           LBB1_717
	//0x000035ee LBB1_798
	0x0f, 0xb6, 0x42, 0xff, //0x000035ee movzbl       $-1(%rdx), %eax
	0x3c, 0x0a, //0x000035f2 cmpb         $10, %al
	0x0f, 0x84, 0xe8, 0xff, 0xff, 0xff, //0x000035f4 je           LBB1_797
	0x3c, 0x0d, //0x000035fa cmpb         $13, %al
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x000035fc je           LBB1_797
	0x3c, 0x3d, //0x00003602 cmpb         $61, %al
	0xe9, 0x62, 0xfc, 0xff, 0xff, //0x00003604 jmp          LBB1_729
	//0x00003609 LBB1_717
	0x4c, 0x8b, 0x7d, 0xd0, //0x00003609 movq         $-48(%rbp), %r15
	//0x0000360d LBB1_720
	0xb0, 0x04, //0x0000360d movb         $4, %al
	0x44, 0x28, 0xd8, //0x0000360f subb         %r11b, %al
	0x0f, 0xb6, 0xc0, //0x00003612 movzbl       %al, %eax
	0x01, 0xc0, //0x00003615 addl         %eax, %eax
	0x8d, 0x0c, 0x40, //0x00003617 leal         (%rax,%rax,2), %ecx
	0x8b, 0x45, 0xc8, //0x0000361a movl         $-56(%rbp), %eax
	0xd3, 0xe0, //0x0000361d shll         %cl, %eax
	0x41, 0x83, 0xfb, 0x02, //0x0000361f cmpl         $2, %r11d
	0x48, 0x8b, 0x55, 0xb0, //0x00003623 movq         $-80(%rbp), %rdx
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00003627 je           LBB1_725
	0x41, 0x83, 0xfb, 0x03, //0x0000362d cmpl         $3, %r11d
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x00003631 je           LBB1_724
	0x41, 0x83, 0xfb, 0x04, //0x00003637 cmpl         $4, %r11d
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x0000363b jne          LBB1_726
	0x88, 0x42, 0x02, //0x00003641 movb         %al, $2(%rdx)
	//0x00003644 LBB1_724
	0x88, 0x62, 0x01, //0x00003644 movb         %ah, $1(%rdx)
	//0x00003647 LBB1_725
	0xc1, 0xe8, 0x10, //0x00003647 shrl         $16, %eax
	0x88, 0x02, //0x0000364a movb         %al, (%rdx)
	//0x0000364c LBB1_726
	0x44, 0x89, 0xd8, //0x0000364c movl         %r11d, %eax
	0x48, 0x8d, 0x54, 0x02, 0xff, //0x0000364f leaq         $-1(%rdx,%rax), %rdx
	0x48, 0x89, 0x55, 0xb0, //0x00003654 movq         %rdx, $-80(%rbp)
	0x49, 0x39, 0xd6, //0x00003658 cmpq         %rdx, %r14
	0x0f, 0x83, 0x0e, 0xf3, 0xff, 0xff, //0x0000365b jae          LBB1_727
	0xe9, 0xc4, 0x02, 0x00, 0x00, //0x00003661 jmp          LBB1_852
	//0x00003666 LBB1_802
	0x48, 0x89, 0xc1, //0x00003666 movq         %rax, %rcx
	0x48, 0x8b, 0x55, 0xd0, //0x00003669 movq         $-48(%rbp), %rdx
	0x49, 0x89, 0xcf, //0x0000366d movq         %rcx, %r15
	0x48, 0x39, 0xd1, //0x00003670 cmpq         %rdx, %rcx
	0x0f, 0x83, 0x6c, 0xf9, 0xff, 0xff, //0x00003673 jae          LBB1_719
	//0x00003679 LBB1_803
	0x49, 0x8d, 0x47, 0x01, //0x00003679 leaq         $1(%r15), %rax
	0x41, 0x0f, 0xb6, 0x0f, //0x0000367d movzbl       (%r15), %ecx
	0x80, 0xf9, 0x5c, //0x00003681 cmpb         $92, %cl
	0x0f, 0x85, 0xe3, 0x00, 0x00, 0x00, //0x00003684 jne          LBB1_816
	0x49, 0x8d, 0x4f, 0x02, //0x0000368a leaq         $2(%r15), %rcx
	0x48, 0x39, 0xd1, //0x0000368e cmpq         %rdx, %rcx
	0x0f, 0x87, 0x7b, 0x02, 0x00, 0x00, //0x00003691 ja           LBB1_827
	0x0f, 0xb6, 0x00, //0x00003697 movzbl       (%rax), %eax
	0x3c, 0x6e, //0x0000369a cmpb         $110, %al
	0x0f, 0x84, 0xe2, 0x00, 0x00, 0x00, //0x0000369c je           LBB1_818
	0x3c, 0x72, //0x000036a2 cmpb         $114, %al
	0x0f, 0x84, 0xda, 0x00, 0x00, 0x00, //0x000036a4 je           LBB1_818
	0x3c, 0x75, //0x000036aa cmpb         $117, %al
	0x0f, 0x85, 0x55, 0x02, 0x00, 0x00, //0x000036ac jne          LBB1_847
	0x48, 0x8b, 0x45, 0xd0, //0x000036b2 movq         $-48(%rbp), %rax
	0x48, 0x29, 0xc8, //0x000036b6 subq         %rcx, %rax
	0x48, 0x83, 0xf8, 0x04, //0x000036b9 cmpq         $4, %rax
	0x0f, 0x8c, 0x44, 0x02, 0x00, 0x00, //0x000036bd jl           LBB1_847
	0x8b, 0x01, //0x000036c3 movl         (%rcx), %eax
	0x89, 0xc2, //0x000036c5 movl         %eax, %edx
	0xf7, 0xd2, //0x000036c7 notl         %edx
	0x8d, 0xb0, 0xd0, 0xcf, 0xcf, 0xcf, //0x000036c9 leal         $-808464432(%rax), %esi
	0x81, 0xe2, 0x80, 0x80, 0x80, 0x80, //0x000036cf andl         $-2139062144, %edx
	0x85, 0xf2, //0x000036d5 testl        %esi, %edx
	0x0f, 0x85, 0x2a, 0x02, 0x00, 0x00, //0x000036d7 jne          LBB1_847
	0x8d, 0xb0, 0x19, 0x19, 0x19, 0x19, //0x000036dd leal         $421075225(%rax), %esi
	0x09, 0xc6, //0x000036e3 orl          %eax, %esi
	0xf7, 0xc6, 0x80, 0x80, 0x80, 0x80, //0x000036e5 testl        $-2139062144, %esi
	0x0f, 0x85, 0x16, 0x02, 0x00, 0x00, //0x000036eb jne          LBB1_847
	0x89, 0xc6, //0x000036f1 movl         %eax, %esi
	0x81, 0xe6, 0x7f, 0x7f, 0x7f, 0x7f, //0x000036f3 andl         $2139062143, %esi
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x000036f9 movl         $-1061109568, %edi
	0x29, 0xf7, //0x000036fe subl         %esi, %edi
	0x8d, 0x9e, 0x46, 0x46, 0x46, 0x46, //0x00003700 leal         $1179010630(%rsi), %ebx
	0x21, 0xd7, //0x00003706 andl         %edx, %edi
	0x85, 0xdf, //0x00003708 testl        %ebx, %edi
	0x0f, 0x85, 0xf7, 0x01, 0x00, 0x00, //0x0000370a jne          LBB1_847
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x00003710 movl         $-522133280, %edi
	0x29, 0xf7, //0x00003715 subl         %esi, %edi
	0x81, 0xc6, 0x39, 0x39, 0x39, 0x39, //0x00003717 addl         $960051513, %esi
	0x21, 0xfa, //0x0000371d andl         %edi, %edx
	0x85, 0xf2, //0x0000371f testl        %esi, %edx
	0x0f, 0x85, 0xe0, 0x01, 0x00, 0x00, //0x00003721 jne          LBB1_847
	0x0f, 0xc8, //0x00003727 bswapl       %eax
	0x89, 0xc1, //0x00003729 movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x0000372b shrl         $4, %ecx
	0xf7, 0xd1, //0x0000372e notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x00003730 andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x00003736 leal         (%rcx,%rcx,8), %ecx
	0x25, 0x0f, 0x0f, 0x0f, 0x0f, //0x00003739 andl         $252645135, %eax
	0x01, 0xc8, //0x0000373e addl         %ecx, %eax
	0x89, 0xc1, //0x00003740 movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x00003742 shrl         $4, %ecx
	0x09, 0xc1, //0x00003745 orl          %eax, %ecx
	0x89, 0xc8, //0x00003747 movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x00003749 shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x0000374c andl         $65280, %eax
	0x89, 0xca, //0x00003751 movl         %ecx, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x00003753 andl         $128, %edx
	0x09, 0xc2, //0x00003759 orl          %eax, %edx
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x0000375b je           LBB1_815
	0xb9, 0xff, 0x00, 0x00, 0x00, //0x00003761 movl         $255, %ecx
	//0x00003766 LBB1_815
	0x49, 0x83, 0xc7, 0x06, //0x00003766 addq         $6, %r15
	0x4c, 0x89, 0xf8, //0x0000376a movq         %r15, %rax
	//0x0000376d LBB1_816
	0x80, 0xf9, 0x0a, //0x0000376d cmpb         $10, %cl
	0x0f, 0x84, 0xf0, 0xfe, 0xff, 0xff, //0x00003770 je           LBB1_802
	0x80, 0xf9, 0x0d, //0x00003776 cmpb         $13, %cl
	0x0f, 0x84, 0xe7, 0xfe, 0xff, 0xff, //0x00003779 je           LBB1_802
	0xe9, 0x23, 0x00, 0x00, 0x00, //0x0000377f jmp          LBB1_823
	//0x00003784 LBB1_818
	0x48, 0x89, 0xc8, //0x00003784 movq         %rcx, %rax
	0x48, 0x8b, 0x55, 0xd0, //0x00003787 movq         $-48(%rbp), %rdx
	0x49, 0x89, 0xcf, //0x0000378b movq         %rcx, %r15
	0x48, 0x39, 0xd1, //0x0000378e cmpq         %rdx, %rcx
	0x0f, 0x82, 0xe2, 0xfe, 0xff, 0xff, //0x00003791 jb           LBB1_803
	0xe9, 0x49, 0xf8, 0xff, 0xff, //0x00003797 jmp          LBB1_719
	//0x0000379c LBB1_821
	0x48, 0x89, 0xca, //0x0000379c movq         %rcx, %rdx
	0x49, 0x89, 0xcf, //0x0000379f movq         %rcx, %r15
	0xe9, 0x4a, 0xf9, 0xff, 0xff, //0x000037a2 jmp          LBB1_696
	//0x000037a7 LBB1_823
	0x80, 0xf9, 0x3d, //0x000037a7 cmpb         $61, %cl
	0x0f, 0x85, 0x62, 0x01, 0x00, 0x00, //0x000037aa jne          LBB1_827
	0x49, 0x89, 0xc7, //0x000037b0 movq         %rax, %r15
	0x41, 0x83, 0xfa, 0x04, //0x000037b3 cmpl         $4, %r10d
	0x0f, 0x84, 0x34, 0xf9, 0xff, 0xff, //0x000037b7 je           LBB1_696
	0x48, 0x39, 0x45, 0xd0, //0x000037bd cmpq         %rax, $-48(%rbp)
	0x0f, 0x87, 0x2a, 0x00, 0x00, 0x00, //0x000037c1 ja           LBB1_830
	0xe9, 0x19, 0xf8, 0xff, 0xff, //0x000037c7 jmp          LBB1_719
	//0x000037cc LBB1_846
	0x4c, 0x89, 0xf9, //0x000037cc movq         %r15, %rcx
	0x48, 0x89, 0xc8, //0x000037cf movq         %rcx, %rax
	0x48, 0x3b, 0x4d, 0xd0, //0x000037d2 cmpq         $-48(%rbp), %rcx
	0x0f, 0x82, 0x15, 0x00, 0x00, 0x00, //0x000037d6 jb           LBB1_830
	0xe9, 0x2c, 0xfe, 0xff, 0xff, //0x000037dc jmp          LBB1_720
	//0x000037e1 LBB1_828
	0x49, 0x89, 0xcf, //0x000037e1 movq         %rcx, %r15
	0x48, 0x89, 0xc8, //0x000037e4 movq         %rcx, %rax
	0x48, 0x3b, 0x4d, 0xd0, //0x000037e7 cmpq         $-48(%rbp), %rcx
	0x0f, 0x83, 0x1c, 0xfe, 0xff, 0xff, //0x000037eb jae          LBB1_720
	//0x000037f1 LBB1_830
	0x4c, 0x8d, 0x78, 0x01, //0x000037f1 leaq         $1(%rax), %r15
	0x0f, 0xb6, 0x08, //0x000037f5 movzbl       (%rax), %ecx
	0x80, 0xf9, 0x5c, //0x000037f8 cmpb         $92, %cl
	0x0f, 0x85, 0xec, 0x00, 0x00, 0x00, //0x000037fb jne          LBB1_843
	0x48, 0x8d, 0x48, 0x02, //0x00003801 leaq         $2(%rax), %rcx
	0x48, 0x3b, 0x4d, 0xd0, //0x00003805 cmpq         $-48(%rbp), %rcx
	0x0f, 0x87, 0x00, 0x01, 0x00, 0x00, //0x00003809 ja           LBB1_848
	0x41, 0x0f, 0xb6, 0x17, //0x0000380f movzbl       (%r15), %edx
	0x80, 0xfa, 0x6e, //0x00003813 cmpb         $110, %dl
	0x0f, 0x84, 0xc5, 0xff, 0xff, 0xff, //0x00003816 je           LBB1_828
	0x80, 0xfa, 0x72, //0x0000381c cmpb         $114, %dl
	0x0f, 0x84, 0xbc, 0xff, 0xff, 0xff, //0x0000381f je           LBB1_828
	0x80, 0xfa, 0x75, //0x00003825 cmpb         $117, %dl
	0x0f, 0x85, 0xd9, 0x00, 0x00, 0x00, //0x00003828 jne          LBB1_847
	0x48, 0x8b, 0x55, 0xd0, //0x0000382e movq         $-48(%rbp), %rdx
	0x48, 0x29, 0xca, //0x00003832 subq         %rcx, %rdx
	0x48, 0x83, 0xfa, 0x04, //0x00003835 cmpq         $4, %rdx
	0x0f, 0x8c, 0xc8, 0x00, 0x00, 0x00, //0x00003839 jl           LBB1_847
	0x8b, 0x11, //0x0000383f movl         (%rcx), %edx
	0x89, 0xd6, //0x00003841 movl         %edx, %esi
	0xf7, 0xd6, //0x00003843 notl         %esi
	0x8d, 0xba, 0xd0, 0xcf, 0xcf, 0xcf, //0x00003845 leal         $-808464432(%rdx), %edi
	0x81, 0xe6, 0x80, 0x80, 0x80, 0x80, //0x0000384b andl         $-2139062144, %esi
	0x85, 0xfe, //0x00003851 testl        %edi, %esi
	0x0f, 0x85, 0xae, 0x00, 0x00, 0x00, //0x00003853 jne          LBB1_847
	0x8d, 0xba, 0x19, 0x19, 0x19, 0x19, //0x00003859 leal         $421075225(%rdx), %edi
	0x09, 0xd7, //0x0000385f orl          %edx, %edi
	0xf7, 0xc7, 0x80, 0x80, 0x80, 0x80, //0x00003861 testl        $-2139062144, %edi
	0x0f, 0x85, 0x9a, 0x00, 0x00, 0x00, //0x00003867 jne          LBB1_847
	0x89, 0xd7, //0x0000386d movl         %edx, %edi
	0x81, 0xe7, 0x7f, 0x7f, 0x7f, 0x7f, //0x0000386f andl         $2139062143, %edi
	0xbb, 0xc0, 0xc0, 0xc0, 0xc0, //0x00003875 movl         $-1061109568, %ebx
	0x29, 0xfb, //0x0000387a subl         %edi, %ebx
	0x44, 0x8d, 0x87, 0x46, 0x46, 0x46, 0x46, //0x0000387c leal         $1179010630(%rdi), %r8d
	0x21, 0xf3, //0x00003883 andl         %esi, %ebx
	0x44, 0x85, 0xc3, //0x00003885 testl        %r8d, %ebx
	0x0f, 0x85, 0x79, 0x00, 0x00, 0x00, //0x00003888 jne          LBB1_847
	0xbb, 0xe0, 0xe0, 0xe0, 0xe0, //0x0000388e movl         $-522133280, %ebx
	0x29, 0xfb, //0x00003893 subl         %edi, %ebx
	0x81, 0xc7, 0x39, 0x39, 0x39, 0x39, //0x00003895 addl         $960051513, %edi
	0x21, 0xde, //0x0000389b andl         %ebx, %esi
	0x85, 0xfe, //0x0000389d testl        %edi, %esi
	0x0f, 0x85, 0x62, 0x00, 0x00, 0x00, //0x0000389f jne          LBB1_847
	0x0f, 0xca, //0x000038a5 bswapl       %edx
	0x89, 0xd1, //0x000038a7 movl         %edx, %ecx
	0xc1, 0xe9, 0x04, //0x000038a9 shrl         $4, %ecx
	0xf7, 0xd1, //0x000038ac notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x000038ae andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x000038b4 leal         (%rcx,%rcx,8), %ecx
	0x81, 0xe2, 0x0f, 0x0f, 0x0f, 0x0f, //0x000038b7 andl         $252645135, %edx
	0x01, 0xca, //0x000038bd addl         %ecx, %edx
	0x89, 0xd1, //0x000038bf movl         %edx, %ecx
	0xc1, 0xe9, 0x04, //0x000038c1 shrl         $4, %ecx
	0x09, 0xd1, //0x000038c4 orl          %edx, %ecx
	0x89, 0xca, //0x000038c6 movl         %ecx, %edx
	0xc1, 0xea, 0x08, //0x000038c8 shrl         $8, %edx
	0x81, 0xe2, 0x00, 0xff, 0x00, 0x00, //0x000038cb andl         $65280, %edx
	0x89, 0xce, //0x000038d1 movl         %ecx, %esi
	0x81, 0xe6, 0x80, 0x00, 0x00, 0x00, //0x000038d3 andl         $128, %esi
	0x09, 0xd6, //0x000038d9 orl          %edx, %esi
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x000038db je           LBB1_842
	0xb9, 0xff, 0x00, 0x00, 0x00, //0x000038e1 movl         $255, %ecx
	//0x000038e6 LBB1_842
	0x48, 0x83, 0xc0, 0x06, //0x000038e6 addq         $6, %rax
	0x49, 0x89, 0xc7, //0x000038ea movq         %rax, %r15
	//0x000038ed LBB1_843
	0x80, 0xf9, 0x0a, //0x000038ed cmpb         $10, %cl
	0x0f, 0x84, 0xd6, 0xfe, 0xff, 0xff, //0x000038f0 je           LBB1_846
	0x80, 0xf9, 0x0d, //0x000038f6 cmpb         $13, %cl
	0x0f, 0x84, 0xcd, 0xfe, 0xff, 0xff, //0x000038f9 je           LBB1_846
	0x80, 0xf9, 0x3d, //0x000038ff cmpb         $61, %cl
	0xe9, 0xea, 0xf7, 0xff, 0xff, //0x00003902 jmp          LBB1_696
	//0x00003907 LBB1_847
	0x49, 0x89, 0xcf, //0x00003907 movq         %rcx, %r15
	0xe9, 0xe2, 0xf7, 0xff, 0xff, //0x0000390a jmp          LBB1_696
	//0x0000390f LBB1_848
	0x4c, 0x89, 0xf8, //0x0000390f movq         %r15, %rax
	//0x00003912 LBB1_827
	0x49, 0x89, 0xc7, //0x00003912 movq         %rax, %r15
	0xe9, 0xd7, 0xf7, 0xff, 0xff, //0x00003915 jmp          LBB1_696
	//0x0000391a LBB1_851
	0x44, 0x8b, 0x75, 0xbc, //0x0000391a movl         $-68(%rbp), %r14d
	0x45, 0x89, 0xf4, //0x0000391e movl         %r14d, %r12d
	0x41, 0x83, 0xe4, 0x02, //0x00003921 andl         $2, %r12d
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x00003925 jmp          LBB1_853
	//0x0000392a LBB1_852
	0x4d, 0x89, 0xf9, //0x0000392a movq         %r15, %r9
	0x44, 0x8b, 0x75, 0xbc, //0x0000392d movl         $-68(%rbp), %r14d
	0x48, 0x8b, 0x5d, 0xd0, //0x00003931 movq         $-48(%rbp), %rbx
	//0x00003935 LBB1_853
	0x48, 0x8b, 0x45, 0xa8, //0x00003935 movq         $-88(%rbp), %rax
	0x48, 0x8b, 0x4d, 0x88, //0x00003939 movq         $-120(%rbp), %rcx
	0x48, 0x8d, 0x54, 0x01, 0xfe, //0x0000393d leaq         $-2(%rcx,%rax), %rdx
	0x48, 0x89, 0x55, 0xc8, //0x00003942 movq         %rdx, $-56(%rbp)
	0x48, 0x8d, 0x44, 0x01, 0xfd, //0x00003946 leaq         $-3(%rcx,%rax), %rax
	0x48, 0x89, 0x45, 0xa0, //0x0000394b movq         %rax, $-96(%rbp)
	0xe9, 0x37, 0x00, 0x00, 0x00, //0x0000394f jmp          LBB1_857
	//0x00003954 LBB1_854
	0x48, 0x89, 0x45, 0xb0, //0x00003954 movq         %rax, $-80(%rbp)
	0x4c, 0x8b, 0x7d, 0xa8, //0x00003958 movq         $-88(%rbp), %r15
	0xe9, 0x57, 0xcc, 0xff, 0xff, //0x0000395c jmp          LBB1_2
	//0x00003961 LBB1_1102
	0x49, 0x89, 0xc8, //0x00003961 movq         %rcx, %r8
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003964 .p2align 4, 0x90
	//0x00003970 LBB1_856
	0x49, 0x8d, 0x48, 0x01, //0x00003970 leaq         $1(%r8), %rcx
	0x4c, 0x39, 0xc3, //0x00003974 cmpq         %r8, %rbx
	0x49, 0x0f, 0x45, 0xc8, //0x00003977 cmovneq      %r8, %rcx
	0x4c, 0x29, 0xc9, //0x0000397b subq         %r9, %rcx
	0x44, 0x8b, 0x75, 0xbc, //0x0000397e movl         $-68(%rbp), %r14d
	0x48, 0x85, 0xc9, //0x00003982 testq        %rcx, %rcx
	0x0f, 0x85, 0x15, 0x10, 0x00, 0x00, //0x00003985 jne          LBB1_1137
	//0x0000398b LBB1_857
	0x4c, 0x39, 0xcb, //0x0000398b cmpq         %r9, %rbx
	0x0f, 0x86, 0x1b, 0x10, 0x00, 0x00, //0x0000398e jbe          LBB1_1138
	0x41, 0xf6, 0xc6, 0x08, //0x00003994 testb        $8, %r14b
	0x0f, 0x85, 0xc2, 0x00, 0x00, 0x00, //0x00003998 jne          LBB1_870
	0x4d, 0x89, 0xc8, //0x0000399e movq         %r9, %r8
	0xe9, 0x16, 0x00, 0x00, 0x00, //0x000039a1 jmp          LBB1_861
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000039a6 .p2align 4, 0x90
	//0x000039b0 LBB1_860
	0x49, 0xff, 0xc0, //0x000039b0 incq         %r8
	0x49, 0x39, 0xd8, //0x000039b3 cmpq         %rbx, %r8
	0x0f, 0x83, 0x04, 0x02, 0x00, 0x00, //0x000039b6 jae          LBB1_888
	//0x000039bc LBB1_861
	0x41, 0x0f, 0xb6, 0x00, //0x000039bc movzbl       (%r8), %eax
	0x48, 0x83, 0xf8, 0x0d, //0x000039c0 cmpq         $13, %rax
	0x0f, 0x84, 0xe6, 0xff, 0xff, 0xff, //0x000039c4 je           LBB1_860
	0x3c, 0x0a, //0x000039ca cmpb         $10, %al
	0x0f, 0x84, 0xde, 0xff, 0xff, 0xff, //0x000039cc je           LBB1_860
	0x48, 0x8b, 0x4d, 0xc0, //0x000039d2 movq         $-64(%rbp), %rcx
	0x0f, 0xb6, 0x14, 0x01, //0x000039d6 movzbl       (%rcx,%rax), %edx
	0x49, 0xff, 0xc0, //0x000039da incq         %r8
	0x81, 0xfa, 0xff, 0x00, 0x00, 0x00, //0x000039dd cmpl         $255, %edx
	0x0f, 0x84, 0xb7, 0x02, 0x00, 0x00, //0x000039e3 je           LBB1_902
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x000039e9 movl         $1, %r11d
	0x49, 0x39, 0xd8, //0x000039ef cmpq         %rbx, %r8
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x000039f2 jb           LBB1_866
	0xe9, 0xe3, 0x0b, 0x00, 0x00, //0x000039f8 jmp          LBB1_1060
	0x90, 0x90, 0x90, //0x000039fd .p2align 4, 0x90
	//0x00003a00 LBB1_865
	0x49, 0xff, 0xc0, //0x00003a00 incq         %r8
	0x49, 0x39, 0xd8, //0x00003a03 cmpq         %rbx, %r8
	0x0f, 0x83, 0x70, 0x04, 0x00, 0x00, //0x00003a06 jae          LBB1_933
	//0x00003a0c LBB1_866
	0x41, 0x0f, 0xb6, 0x00, //0x00003a0c movzbl       (%r8), %eax
	0x48, 0x83, 0xf8, 0x0d, //0x00003a10 cmpq         $13, %rax
	0x0f, 0x84, 0xe6, 0xff, 0xff, 0xff, //0x00003a14 je           LBB1_865
	0x3c, 0x0a, //0x00003a1a cmpb         $10, %al
	0x0f, 0x84, 0xde, 0xff, 0xff, 0xff, //0x00003a1c je           LBB1_865
	0x48, 0x8b, 0x4d, 0xc0, //0x00003a22 movq         $-64(%rbp), %rcx
	0x0f, 0xb6, 0x0c, 0x01, //0x00003a26 movzbl       (%rcx,%rax), %ecx
	0x49, 0xff, 0xc0, //0x00003a2a incq         %r8
	0x81, 0xf9, 0xff, 0x00, 0x00, 0x00, //0x00003a2d cmpl         $255, %ecx
	0x0f, 0x84, 0x25, 0x06, 0x00, 0x00, //0x00003a33 je           LBB1_960
	0xc1, 0xe2, 0x06, //0x00003a39 shll         $6, %edx
	0x09, 0xca, //0x00003a3c orl          %ecx, %edx
	0x41, 0xbb, 0x02, 0x00, 0x00, 0x00, //0x00003a3e movl         $2, %r11d
	0x49, 0x39, 0xd8, //0x00003a44 cmpq         %rbx, %r8
	0x0f, 0x82, 0xb5, 0x01, 0x00, 0x00, //0x00003a47 jb           LBB1_892
	0xe9, 0x8e, 0x0b, 0x00, 0x00, //0x00003a4d jmp          LBB1_1060
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003a52 .p2align 4, 0x90
	//0x00003a60 LBB1_870
	0x4c, 0x89, 0xca, //0x00003a60 movq         %r9, %rdx
	0xe9, 0x24, 0x00, 0x00, 0x00, //0x00003a63 jmp          LBB1_873
	//0x00003a68 LBB1_871
	0x80, 0xf9, 0x6e, //0x00003a68 cmpb         $110, %cl
	0x0f, 0x85, 0xdf, 0x01, 0x00, 0x00, //0x00003a6b jne          LBB1_897
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003a71 .p2align 4, 0x90
	//0x00003a80 LBB1_872
	0x4c, 0x89, 0xc2, //0x00003a80 movq         %r8, %rdx
	0x49, 0x39, 0xd8, //0x00003a83 cmpq         %rbx, %r8
	0x0f, 0x83, 0x34, 0x01, 0x00, 0x00, //0x00003a86 jae          LBB1_888
	//0x00003a8c LBB1_873
	0x48, 0x8d, 0x4a, 0x01, //0x00003a8c leaq         $1(%rdx), %rcx
	0x0f, 0xb6, 0x02, //0x00003a90 movzbl       (%rdx), %eax
	0x3c, 0x5c, //0x00003a93 cmpb         $92, %al
	0x0f, 0x85, 0x05, 0x01, 0x00, 0x00, //0x00003a95 jne          LBB1_886
	0x4c, 0x8d, 0x42, 0x02, //0x00003a9b leaq         $2(%rdx), %r8
	0xb0, 0xff, //0x00003a9f movb         $-1, %al
	0x49, 0x39, 0xd8, //0x00003aa1 cmpq         %rbx, %r8
	0x0f, 0x87, 0x9e, 0x01, 0x00, 0x00, //0x00003aa4 ja           LBB1_896
	0x0f, 0xb6, 0x09, //0x00003aaa movzbl       (%rcx), %ecx
	0x80, 0xf9, 0x71, //0x00003aad cmpb         $113, %cl
	0x0f, 0x8e, 0xb2, 0xff, 0xff, 0xff, //0x00003ab0 jle          LBB1_871
	0x80, 0xf9, 0x72, //0x00003ab6 cmpb         $114, %cl
	0x0f, 0x84, 0xc1, 0xff, 0xff, 0xff, //0x00003ab9 je           LBB1_872
	0x80, 0xf9, 0x75, //0x00003abf cmpb         $117, %cl
	0x0f, 0x85, 0x98, 0x01, 0x00, 0x00, //0x00003ac2 jne          LBB1_899
	0x48, 0x89, 0xd9, //0x00003ac8 movq         %rbx, %rcx
	0x4c, 0x29, 0xc1, //0x00003acb subq         %r8, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00003ace cmpq         $4, %rcx
	0x0f, 0x8c, 0x88, 0x01, 0x00, 0x00, //0x00003ad2 jl           LBB1_899
	0x41, 0x8b, 0x08, //0x00003ad8 movl         (%r8), %ecx
	0x89, 0xcf, //0x00003adb movl         %ecx, %edi
	0xf7, 0xd7, //0x00003add notl         %edi
	0x8d, 0xb1, 0xd0, 0xcf, 0xcf, 0xcf, //0x00003adf leal         $-808464432(%rcx), %esi
	0x81, 0xe7, 0x80, 0x80, 0x80, 0x80, //0x00003ae5 andl         $-2139062144, %edi
	0x85, 0xf7, //0x00003aeb testl        %esi, %edi
	0x0f, 0x85, 0x6d, 0x01, 0x00, 0x00, //0x00003aed jne          LBB1_899
	0x8d, 0xb1, 0x19, 0x19, 0x19, 0x19, //0x00003af3 leal         $421075225(%rcx), %esi
	0x09, 0xce, //0x00003af9 orl          %ecx, %esi
	0xf7, 0xc6, 0x80, 0x80, 0x80, 0x80, //0x00003afb testl        $-2139062144, %esi
	0x0f, 0x85, 0x59, 0x01, 0x00, 0x00, //0x00003b01 jne          LBB1_899
	0x89, 0xce, //0x00003b07 movl         %ecx, %esi
	0x81, 0xe6, 0x7f, 0x7f, 0x7f, 0x7f, //0x00003b09 andl         $2139062143, %esi
	0xbb, 0xc0, 0xc0, 0xc0, 0xc0, //0x00003b0f movl         $-1061109568, %ebx
	0x29, 0xf3, //0x00003b14 subl         %esi, %ebx
	0x44, 0x8d, 0x96, 0x46, 0x46, 0x46, 0x46, //0x00003b16 leal         $1179010630(%rsi), %r10d
	0x21, 0xfb, //0x00003b1d andl         %edi, %ebx
	0x44, 0x85, 0xd3, //0x00003b1f testl        %r10d, %ebx
	0x48, 0x8b, 0x5d, 0xd0, //0x00003b22 movq         $-48(%rbp), %rbx
	0x0f, 0x85, 0x34, 0x01, 0x00, 0x00, //0x00003b26 jne          LBB1_899
	0xbb, 0xe0, 0xe0, 0xe0, 0xe0, //0x00003b2c movl         $-522133280, %ebx
	0x29, 0xf3, //0x00003b31 subl         %esi, %ebx
	0x81, 0xc6, 0x39, 0x39, 0x39, 0x39, //0x00003b33 addl         $960051513, %esi
	0x21, 0xdf, //0x00003b39 andl         %ebx, %edi
	0x48, 0x8b, 0x5d, 0xd0, //0x00003b3b movq         $-48(%rbp), %rbx
	0x85, 0xf7, //0x00003b3f testl        %esi, %edi
	0x0f, 0x85, 0x19, 0x01, 0x00, 0x00, //0x00003b41 jne          LBB1_899
	0x0f, 0xc9, //0x00003b47 bswapl       %ecx
	0x89, 0xc8, //0x00003b49 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00003b4b shrl         $4, %eax
	0xf7, 0xd0, //0x00003b4e notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00003b50 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00003b55 leal         (%rax,%rax,8), %eax
	0x81, 0xe1, 0x0f, 0x0f, 0x0f, 0x0f, //0x00003b58 andl         $252645135, %ecx
	0x01, 0xc1, //0x00003b5e addl         %eax, %ecx
	0x89, 0xc8, //0x00003b60 movl         %ecx, %eax
	0xc1, 0xe8, 0x04, //0x00003b62 shrl         $4, %eax
	0x09, 0xc8, //0x00003b65 orl          %ecx, %eax
	0x89, 0xc1, //0x00003b67 movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x00003b69 shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x00003b6c andl         $65280, %ecx
	0x89, 0xc6, //0x00003b72 movl         %eax, %esi
	0x81, 0xe6, 0x80, 0x00, 0x00, 0x00, //0x00003b74 andl         $128, %esi
	0x09, 0xce, //0x00003b7a orl          %ecx, %esi
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x00003b7c je           LBB1_885
	0xb8, 0xff, 0x00, 0x00, 0x00, //0x00003b82 movl         $255, %eax
	//0x00003b87 LBB1_885
	0x48, 0x83, 0xc2, 0x06, //0x00003b87 addq         $6, %rdx
	0x49, 0x89, 0xd0, //0x00003b8b movq         %rdx, %r8
	0x3c, 0x0d, //0x00003b8e cmpb         $13, %al
	0x0f, 0x85, 0x15, 0x00, 0x00, 0x00, //0x00003b90 jne          LBB1_887
	0xe9, 0xe5, 0xfe, 0xff, 0xff, //0x00003b96 jmp          LBB1_872
	0x90, 0x90, 0x90, 0x90, 0x90, //0x00003b9b .p2align 4, 0x90
	//0x00003ba0 LBB1_886
	0x49, 0x89, 0xc8, //0x00003ba0 movq         %rcx, %r8
	0x3c, 0x0d, //0x00003ba3 cmpb         $13, %al
	0x0f, 0x84, 0xd5, 0xfe, 0xff, 0xff, //0x00003ba5 je           LBB1_872
	//0x00003bab LBB1_887
	0x3c, 0x0a, //0x00003bab cmpb         $10, %al
	0x0f, 0x84, 0xcd, 0xfe, 0xff, 0xff, //0x00003bad je           LBB1_872
	0xe9, 0xa8, 0x00, 0x00, 0x00, //0x00003bb3 jmp          LBB1_899
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003bb8 .p2align 4, 0x90
	//0x00003bc0 LBB1_888
	0x31, 0xd2, //0x00003bc0 xorl         %edx, %edx
	0x45, 0x31, 0xdb, //0x00003bc2 xorl         %r11d, %r11d
	//0x00003bc5 LBB1_889
	0x45, 0x85, 0xdb, //0x00003bc5 testl        %r11d, %r11d
	0x0f, 0x85, 0x12, 0x0a, 0x00, 0x00, //0x00003bc8 jne          LBB1_1060
	0x4d, 0x89, 0xc1, //0x00003bce movq         %r8, %r9
	0x31, 0xc9, //0x00003bd1 xorl         %ecx, %ecx
	0x48, 0x85, 0xc9, //0x00003bd3 testq        %rcx, %rcx
	0x0f, 0x84, 0xaf, 0xfd, 0xff, 0xff, //0x00003bd6 je           LBB1_857
	0xe9, 0xbf, 0x0d, 0x00, 0x00, //0x00003bdc jmp          LBB1_1137
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003be1 .p2align 4, 0x90
	//0x00003bf0 LBB1_891
	0x49, 0xff, 0xc0, //0x00003bf0 incq         %r8
	0x41, 0xbb, 0x02, 0x00, 0x00, 0x00, //0x00003bf3 movl         $2, %r11d
	0x49, 0x39, 0xd8, //0x00003bf9 cmpq         %rbx, %r8
	0x0f, 0x83, 0xc3, 0xff, 0xff, 0xff, //0x00003bfc jae          LBB1_889
	//0x00003c02 LBB1_892
	0x41, 0x0f, 0xb6, 0x00, //0x00003c02 movzbl       (%r8), %eax
	0x48, 0x83, 0xf8, 0x0d, //0x00003c06 cmpq         $13, %rax
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x00003c0a je           LBB1_891
	0x3c, 0x0a, //0x00003c10 cmpb         $10, %al
	0x0f, 0x84, 0xd8, 0xff, 0xff, 0xff, //0x00003c12 je           LBB1_891
	0x48, 0x8b, 0x4d, 0xc0, //0x00003c18 movq         $-64(%rbp), %rcx
	0x0f, 0xb6, 0x0c, 0x01, //0x00003c1c movzbl       (%rcx,%rax), %ecx
	0x49, 0xff, 0xc0, //0x00003c20 incq         %r8
	0x81, 0xf9, 0xff, 0x00, 0x00, 0x00, //0x00003c23 cmpl         $255, %ecx
	0x0f, 0x84, 0xbd, 0x07, 0x00, 0x00, //0x00003c29 je           LBB1_1025
	0xc1, 0xe2, 0x06, //0x00003c2f shll         $6, %edx
	0x09, 0xca, //0x00003c32 orl          %ecx, %edx
	0x41, 0xbb, 0x03, 0x00, 0x00, 0x00, //0x00003c34 movl         $3, %r11d
	0x49, 0x39, 0xd8, //0x00003c3a cmpq         %rbx, %r8
	0x0f, 0x82, 0x6f, 0x02, 0x00, 0x00, //0x00003c3d jb           LBB1_936
	0xe9, 0x98, 0x09, 0x00, 0x00, //0x00003c43 jmp          LBB1_1060
	//0x00003c48 LBB1_896
	0x49, 0x89, 0xc8, //0x00003c48 movq         %rcx, %r8
	0xe9, 0x10, 0x00, 0x00, 0x00, //0x00003c4b jmp          LBB1_899
	//0x00003c50 LBB1_897
	0x80, 0xf9, 0x2f, //0x00003c50 cmpb         $47, %cl
	0x0f, 0x85, 0x07, 0x00, 0x00, 0x00, //0x00003c53 jne          LBB1_899
	0x89, 0xc8, //0x00003c59 movl         %ecx, %eax
	0x90, 0x90, 0x90, 0x90, 0x90, //0x00003c5b .p2align 4, 0x90
	//0x00003c60 LBB1_899
	0x0f, 0xb6, 0xc8, //0x00003c60 movzbl       %al, %ecx
	0x48, 0x8b, 0x55, 0xc0, //0x00003c63 movq         $-64(%rbp), %rdx
	0x0f, 0xb6, 0x14, 0x0a, //0x00003c67 movzbl       (%rdx,%rcx), %edx
	0x81, 0xfa, 0xff, 0x00, 0x00, 0x00, //0x00003c6b cmpl         $255, %edx
	0x0f, 0x84, 0x29, 0x00, 0x00, 0x00, //0x00003c71 je           LBB1_902
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00003c77 movl         $1, %r11d
	0x49, 0x39, 0xd8, //0x00003c7d cmpq         %rbx, %r8
	0x0f, 0x83, 0x5a, 0x09, 0x00, 0x00, //0x00003c80 jae          LBB1_1060
	0x41, 0x89, 0xd7, //0x00003c86 movl         %edx, %r15d
	0x4c, 0x89, 0xc2, //0x00003c89 movq         %r8, %rdx
	0xe9, 0xd6, 0x00, 0x00, 0x00, //0x00003c8c jmp          LBB1_918
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003c91 .p2align 4, 0x90
	//0x00003ca0 LBB1_902
	0x45, 0x31, 0xff, //0x00003ca0 xorl         %r15d, %r15d
	0x45, 0x31, 0xdb, //0x00003ca3 xorl         %r11d, %r11d
	//0x00003ca6 LBB1_903
	0x45, 0x85, 0xe4, //0x00003ca6 testl        %r12d, %r12d
	0x0f, 0x85, 0xc1, 0xfc, 0xff, 0xff, //0x00003ca9 jne          LBB1_856
	0x41, 0x83, 0xfb, 0x02, //0x00003caf cmpl         $2, %r11d
	0x0f, 0x82, 0xb7, 0xfc, 0xff, 0xff, //0x00003cb3 jb           LBB1_856
	0x3c, 0x3d, //0x00003cb9 cmpb         $61, %al
	0x0f, 0x85, 0xaf, 0xfc, 0xff, 0xff, //0x00003cbb jne          LBB1_856
	0x41, 0xbe, 0x05, 0x00, 0x00, 0x00, //0x00003cc1 movl         $5, %r14d
	0x45, 0x29, 0xde, //0x00003cc7 subl         %r11d, %r14d
	0xf6, 0x45, 0xbc, 0x08, //0x00003cca testb        $8, $-68(%rbp)
	0x0f, 0x85, 0x19, 0x02, 0x00, 0x00, //0x00003cce jne          LBB1_940
	0x4c, 0x39, 0xc3, //0x00003cd4 cmpq         %r8, %rbx
	0x0f, 0x86, 0x2e, 0x09, 0x00, 0x00, //0x00003cd7 jbe          LBB1_1063
	0x49, 0x8d, 0x48, 0x03, //0x00003cdd leaq         $3(%r8), %rcx
	0x48, 0x8b, 0x75, 0xc8, //0x00003ce1 movq         $-56(%rbp), %rsi
	0x4c, 0x29, 0xc6, //0x00003ce5 subq         %r8, %rsi
	0x49, 0x8d, 0x50, 0x04, //0x00003ce8 leaq         $4(%r8), %rdx
	0x48, 0x8b, 0x7d, 0xa0, //0x00003cec movq         $-96(%rbp), %rdi
	0x4c, 0x29, 0xc7, //0x00003cf0 subq         %r8, %rdi
	0xe9, 0x20, 0x00, 0x00, 0x00, //0x00003cf3 jmp          LBB1_910
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003cf8 .p2align 4, 0x90
	//0x00003d00 LBB1_909
	0x49, 0xff, 0xc0, //0x00003d00 incq         %r8
	0x48, 0xff, 0xc1, //0x00003d03 incq         %rcx
	0x48, 0xff, 0xce, //0x00003d06 decq         %rsi
	0x48, 0xff, 0xc2, //0x00003d09 incq         %rdx
	0x48, 0xff, 0xcf, //0x00003d0c decq         %rdi
	0x4c, 0x39, 0xc3, //0x00003d0f cmpq         %r8, %rbx
	0x0f, 0x84, 0x7e, 0x08, 0x00, 0x00, //0x00003d12 je           LBB1_993
	//0x00003d18 LBB1_910
	0x41, 0x0f, 0xb6, 0x00, //0x00003d18 movzbl       (%r8), %eax
	0x3c, 0x0a, //0x00003d1c cmpb         $10, %al
	0x0f, 0x84, 0xdc, 0xff, 0xff, 0xff, //0x00003d1e je           LBB1_909
	0x3c, 0x0d, //0x00003d24 cmpb         $13, %al
	0x0f, 0x84, 0xd4, 0xff, 0xff, 0xff, //0x00003d26 je           LBB1_909
	0x3c, 0x3d, //0x00003d2c cmpb         $61, %al
	0x0f, 0x85, 0x1d, 0x05, 0x00, 0x00, //0x00003d2e jne          LBB1_855
	0x49, 0xff, 0xc0, //0x00003d34 incq         %r8
	0x41, 0x83, 0xfe, 0x02, //0x00003d37 cmpl         $2, %r14d
	0x0f, 0x84, 0x2f, 0xfc, 0xff, 0xff, //0x00003d3b je           LBB1_856
	0x4c, 0x39, 0xc3, //0x00003d41 cmpq         %r8, %rbx
	0x0f, 0x87, 0x3a, 0x03, 0x00, 0x00, //0x00003d44 ja           LBB1_962
	0xe9, 0xbc, 0x08, 0x00, 0x00, //0x00003d4a jmp          LBB1_1063
	//0x00003d4f LBB1_915
	0x80, 0xf9, 0x6e, //0x00003d4f cmpb         $110, %cl
	0x0f, 0x85, 0x63, 0x03, 0x00, 0x00, //0x00003d52 jne          LBB1_967
	//0x00003d58 LBB1_916
	0x4d, 0x89, 0xd0, //0x00003d58 movq         %r10, %r8
	//0x00003d5b LBB1_917
	0x4c, 0x89, 0xc2, //0x00003d5b movq         %r8, %rdx
	0x49, 0x39, 0xd8, //0x00003d5e cmpq         %rbx, %r8
	0x0f, 0x83, 0x20, 0x01, 0x00, 0x00, //0x00003d61 jae          LBB1_934
	//0x00003d67 LBB1_918
	0x4c, 0x8d, 0x42, 0x01, //0x00003d67 leaq         $1(%rdx), %r8
	0x0f, 0xb6, 0x02, //0x00003d6b movzbl       (%rdx), %eax
	0x3c, 0x5c, //0x00003d6e cmpb         $92, %al
	0x0f, 0x85, 0xf1, 0x00, 0x00, 0x00, //0x00003d70 jne          LBB1_931
	0x4c, 0x8d, 0x52, 0x02, //0x00003d76 leaq         $2(%rdx), %r10
	0xb0, 0xff, //0x00003d7a movb         $-1, %al
	0x49, 0x39, 0xda, //0x00003d7c cmpq         %rbx, %r10
	0x0f, 0x87, 0x48, 0x03, 0x00, 0x00, //0x00003d7f ja           LBB1_970
	0x41, 0x0f, 0xb6, 0x08, //0x00003d85 movzbl       (%r8), %ecx
	0x80, 0xf9, 0x71, //0x00003d89 cmpb         $113, %cl
	0x0f, 0x8e, 0xbd, 0xff, 0xff, 0xff, //0x00003d8c jle          LBB1_915
	0x80, 0xf9, 0x72, //0x00003d92 cmpb         $114, %cl
	0x0f, 0x84, 0xbd, 0xff, 0xff, 0xff, //0x00003d95 je           LBB1_916
	0x80, 0xf9, 0x75, //0x00003d9b cmpb         $117, %cl
	0x0f, 0x85, 0x22, 0x03, 0x00, 0x00, //0x00003d9e jne          LBB1_969
	0x48, 0x8b, 0x4d, 0xd0, //0x00003da4 movq         $-48(%rbp), %rcx
	0x4c, 0x29, 0xd1, //0x00003da8 subq         %r10, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00003dab cmpq         $4, %rcx
	0x0f, 0x8c, 0x11, 0x03, 0x00, 0x00, //0x00003daf jl           LBB1_969
	0x41, 0x8b, 0x3a, //0x00003db5 movl         (%r10), %edi
	0x89, 0xfb, //0x00003db8 movl         %edi, %ebx
	0xf7, 0xd3, //0x00003dba notl         %ebx
	0x8d, 0x8f, 0xd0, 0xcf, 0xcf, 0xcf, //0x00003dbc leal         $-808464432(%rdi), %ecx
	0x81, 0xe3, 0x80, 0x80, 0x80, 0x80, //0x00003dc2 andl         $-2139062144, %ebx
	0x85, 0xcb, //0x00003dc8 testl        %ecx, %ebx
	0x0f, 0x85, 0xf6, 0x02, 0x00, 0x00, //0x00003dca jne          LBB1_969
	0x8d, 0x8f, 0x19, 0x19, 0x19, 0x19, //0x00003dd0 leal         $421075225(%rdi), %ecx
	0x09, 0xf9, //0x00003dd6 orl          %edi, %ecx
	0xf7, 0xc1, 0x80, 0x80, 0x80, 0x80, //0x00003dd8 testl        $-2139062144, %ecx
	0x0f, 0x85, 0xe2, 0x02, 0x00, 0x00, //0x00003dde jne          LBB1_969
	0x89, 0xf9, //0x00003de4 movl         %edi, %ecx
	0x81, 0xe1, 0x7f, 0x7f, 0x7f, 0x7f, //0x00003de6 andl         $2139062143, %ecx
	0xbe, 0xc0, 0xc0, 0xc0, 0xc0, //0x00003dec movl         $-1061109568, %esi
	0x29, 0xce, //0x00003df1 subl         %ecx, %esi
	0x44, 0x8d, 0x81, 0x46, 0x46, 0x46, 0x46, //0x00003df3 leal         $1179010630(%rcx), %r8d
	0x21, 0xde, //0x00003dfa andl         %ebx, %esi
	0x44, 0x85, 0xc6, //0x00003dfc testl        %r8d, %esi
	0x0f, 0x85, 0xc1, 0x02, 0x00, 0x00, //0x00003dff jne          LBB1_969
	0xbe, 0xe0, 0xe0, 0xe0, 0xe0, //0x00003e05 movl         $-522133280, %esi
	0x29, 0xce, //0x00003e0a subl         %ecx, %esi
	0x81, 0xc1, 0x39, 0x39, 0x39, 0x39, //0x00003e0c addl         $960051513, %ecx
	0x21, 0xf3, //0x00003e12 andl         %esi, %ebx
	0x85, 0xcb, //0x00003e14 testl        %ecx, %ebx
	0x0f, 0x85, 0xaa, 0x02, 0x00, 0x00, //0x00003e16 jne          LBB1_969
	0x0f, 0xcf, //0x00003e1c bswapl       %edi
	0x89, 0xf8, //0x00003e1e movl         %edi, %eax
	0xc1, 0xe8, 0x04, //0x00003e20 shrl         $4, %eax
	0xf7, 0xd0, //0x00003e23 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00003e25 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00003e2a leal         (%rax,%rax,8), %eax
	0x81, 0xe7, 0x0f, 0x0f, 0x0f, 0x0f, //0x00003e2d andl         $252645135, %edi
	0x01, 0xc7, //0x00003e33 addl         %eax, %edi
	0x89, 0xf8, //0x00003e35 movl         %edi, %eax
	0xc1, 0xe8, 0x04, //0x00003e37 shrl         $4, %eax
	0x09, 0xf8, //0x00003e3a orl          %edi, %eax
	0x89, 0xc1, //0x00003e3c movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x00003e3e shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x00003e41 andl         $65280, %ecx
	0x89, 0xc6, //0x00003e47 movl         %eax, %esi
	0x81, 0xe6, 0x80, 0x00, 0x00, 0x00, //0x00003e49 andl         $128, %esi
	0x09, 0xce, //0x00003e4f orl          %ecx, %esi
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x00003e51 je           LBB1_930
	0xb8, 0xff, 0x00, 0x00, 0x00, //0x00003e57 movl         $255, %eax
	//0x00003e5c LBB1_930
	0x48, 0x83, 0xc2, 0x06, //0x00003e5c addq         $6, %rdx
	0x49, 0x89, 0xd0, //0x00003e60 movq         %rdx, %r8
	0x48, 0x8b, 0x5d, 0xd0, //0x00003e63 movq         $-48(%rbp), %rbx
	//0x00003e67 LBB1_931
	0x3c, 0x0a, //0x00003e67 cmpb         $10, %al
	0x0f, 0x84, 0xec, 0xfe, 0xff, 0xff, //0x00003e69 je           LBB1_917
	0x3c, 0x0d, //0x00003e6f cmpb         $13, %al
	0x0f, 0x84, 0xe4, 0xfe, 0xff, 0xff, //0x00003e71 je           LBB1_917
	0xe9, 0x51, 0x02, 0x00, 0x00, //0x00003e77 jmp          LBB1_970
	//0x00003e7c LBB1_933
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00003e7c movl         $1, %r11d
	0xe9, 0x3e, 0xfd, 0xff, 0xff, //0x00003e82 jmp          LBB1_889
	//0x00003e87 LBB1_934
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00003e87 movl         $1, %r11d
	0x44, 0x89, 0xfa, //0x00003e8d movl         %r15d, %edx
	0xe9, 0x30, 0xfd, 0xff, 0xff, //0x00003e90 jmp          LBB1_889
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003e95 .p2align 4, 0x90
	//0x00003ea0 LBB1_935
	0x49, 0xff, 0xc0, //0x00003ea0 incq         %r8
	0x41, 0xbb, 0x03, 0x00, 0x00, 0x00, //0x00003ea3 movl         $3, %r11d
	0x49, 0x39, 0xd8, //0x00003ea9 cmpq         %rbx, %r8
	0x0f, 0x83, 0x13, 0xfd, 0xff, 0xff, //0x00003eac jae          LBB1_889
	//0x00003eb2 LBB1_936
	0x41, 0x0f, 0xb6, 0x00, //0x00003eb2 movzbl       (%r8), %eax
	0x48, 0x83, 0xf8, 0x0d, //0x00003eb6 cmpq         $13, %rax
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x00003eba je           LBB1_935
	0x3c, 0x0a, //0x00003ec0 cmpb         $10, %al
	0x0f, 0x84, 0xd8, 0xff, 0xff, 0xff, //0x00003ec2 je           LBB1_935
	0x48, 0x8b, 0x4d, 0xc0, //0x00003ec8 movq         $-64(%rbp), %rcx
	0x0f, 0xb6, 0x0c, 0x01, //0x00003ecc movzbl       (%rcx,%rax), %ecx
	0x49, 0xff, 0xc0, //0x00003ed0 incq         %r8
	0x81, 0xf9, 0xff, 0x00, 0x00, 0x00, //0x00003ed3 cmpl         $255, %ecx
	0x0f, 0x85, 0xeb, 0x06, 0x00, 0x00, //0x00003ed9 jne          LBB1_1059
	0x41, 0x89, 0xd7, //0x00003edf movl         %edx, %r15d
	0x41, 0xbb, 0x03, 0x00, 0x00, 0x00, //0x00003ee2 movl         $3, %r11d
	0xe9, 0xb9, 0xfd, 0xff, 0xff, //0x00003ee8 jmp          LBB1_903
	//0x00003eed LBB1_940
	0x4c, 0x39, 0xc3, //0x00003eed cmpq         %r8, %rbx
	0x0f, 0x87, 0x39, 0x00, 0x00, 0x00, //0x00003ef0 ja           LBB1_943
	0xe9, 0x10, 0x07, 0x00, 0x00, //0x00003ef6 jmp          LBB1_1063
	0x90, 0x90, 0x90, 0x90, 0x90, //0x00003efb .p2align 4, 0x90
	//0x00003f00 LBB1_959
	0x4c, 0x89, 0xe9, //0x00003f00 movq         %r13, %rcx
	0x49, 0x89, 0xc8, //0x00003f03 movq         %rcx, %r8
	0x48, 0x39, 0xd9, //0x00003f06 cmpq         %rbx, %rcx
	0x0f, 0x82, 0x20, 0x00, 0x00, 0x00, //0x00003f09 jb           LBB1_943
	0xe9, 0x42, 0x01, 0x00, 0x00, //0x00003f0f jmp          LBB1_994
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003f14 .p2align 4, 0x90
	//0x00003f20 LBB1_941
	0x49, 0x89, 0xcd, //0x00003f20 movq         %rcx, %r13
	0x49, 0x89, 0xc8, //0x00003f23 movq         %rcx, %r8
	0x48, 0x39, 0xd9, //0x00003f26 cmpq         %rbx, %rcx
	0x0f, 0x83, 0x27, 0x01, 0x00, 0x00, //0x00003f29 jae          LBB1_994
	//0x00003f2f LBB1_943
	0x4d, 0x8d, 0x68, 0x01, //0x00003f2f leaq         $1(%r8), %r13
	0x41, 0x0f, 0xb6, 0x08, //0x00003f33 movzbl       (%r8), %ecx
	0x80, 0xf9, 0x5c, //0x00003f37 cmpb         $92, %cl
	0x0f, 0x85, 0xe5, 0x00, 0x00, 0x00, //0x00003f3a jne          LBB1_956
	0x49, 0x8d, 0x48, 0x02, //0x00003f40 leaq         $2(%r8), %rcx
	0x48, 0x39, 0xd9, //0x00003f44 cmpq         %rbx, %rcx
	0x0f, 0x87, 0x66, 0x07, 0x00, 0x00, //0x00003f47 ja           LBB1_1026
	0x41, 0x0f, 0xb6, 0x45, 0x00, //0x00003f4d movzbl       (%r13), %eax
	0x3c, 0x6e, //0x00003f52 cmpb         $110, %al
	0x0f, 0x84, 0xc6, 0xff, 0xff, 0xff, //0x00003f54 je           LBB1_941
	0x3c, 0x72, //0x00003f5a cmpb         $114, %al
	0x0f, 0x84, 0xbe, 0xff, 0xff, 0xff, //0x00003f5c je           LBB1_941
	0x3c, 0x75, //0x00003f62 cmpb         $117, %al
	0x0f, 0x85, 0xf7, 0xf9, 0xff, 0xff, //0x00003f64 jne          LBB1_1102
	0x48, 0x89, 0xd8, //0x00003f6a movq         %rbx, %rax
	0x48, 0x29, 0xc8, //0x00003f6d subq         %rcx, %rax
	0x48, 0x83, 0xf8, 0x04, //0x00003f70 cmpq         $4, %rax
	0x0f, 0x8c, 0xe7, 0xf9, 0xff, 0xff, //0x00003f74 jl           LBB1_1102
	0x8b, 0x11, //0x00003f7a movl         (%rcx), %edx
	0x89, 0xd6, //0x00003f7c movl         %edx, %esi
	0xf7, 0xd6, //0x00003f7e notl         %esi
	0x8d, 0x82, 0xd0, 0xcf, 0xcf, 0xcf, //0x00003f80 leal         $-808464432(%rdx), %eax
	0x81, 0xe6, 0x80, 0x80, 0x80, 0x80, //0x00003f86 andl         $-2139062144, %esi
	0x85, 0xc6, //0x00003f8c testl        %eax, %esi
	0x0f, 0x85, 0xcd, 0xf9, 0xff, 0xff, //0x00003f8e jne          LBB1_1102
	0x8d, 0x82, 0x19, 0x19, 0x19, 0x19, //0x00003f94 leal         $421075225(%rdx), %eax
	0x09, 0xd0, //0x00003f9a orl          %edx, %eax
	0xa9, 0x80, 0x80, 0x80, 0x80, //0x00003f9c testl        $-2139062144, %eax
	0x0f, 0x85, 0xba, 0xf9, 0xff, 0xff, //0x00003fa1 jne          LBB1_1102
	0x89, 0xd0, //0x00003fa7 movl         %edx, %eax
	0x25, 0x7f, 0x7f, 0x7f, 0x7f, //0x00003fa9 andl         $2139062143, %eax
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x00003fae movl         $-1061109568, %edi
	0x29, 0xc7, //0x00003fb3 subl         %eax, %edi
	0x8d, 0x98, 0x46, 0x46, 0x46, 0x46, //0x00003fb5 leal         $1179010630(%rax), %ebx
	0x21, 0xf7, //0x00003fbb andl         %esi, %edi
	0x85, 0xdf, //0x00003fbd testl        %ebx, %edi
	0x0f, 0x85, 0x9a, 0x09, 0x00, 0x00, //0x00003fbf jne          LBB1_1131
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x00003fc5 movl         $-522133280, %edi
	0x29, 0xc7, //0x00003fca subl         %eax, %edi
	0x05, 0x39, 0x39, 0x39, 0x39, //0x00003fcc addl         $960051513, %eax
	0x21, 0xfe, //0x00003fd1 andl         %edi, %esi
	0x85, 0xc6, //0x00003fd3 testl        %eax, %esi
	0x48, 0x8b, 0x5d, 0xd0, //0x00003fd5 movq         $-48(%rbp), %rbx
	0x0f, 0x85, 0x82, 0xf9, 0xff, 0xff, //0x00003fd9 jne          LBB1_1102
	0x0f, 0xca, //0x00003fdf bswapl       %edx
	0x89, 0xd0, //0x00003fe1 movl         %edx, %eax
	0xc1, 0xe8, 0x04, //0x00003fe3 shrl         $4, %eax
	0xf7, 0xd0, //0x00003fe6 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00003fe8 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00003fed leal         (%rax,%rax,8), %eax
	0x81, 0xe2, 0x0f, 0x0f, 0x0f, 0x0f, //0x00003ff0 andl         $252645135, %edx
	0x01, 0xc2, //0x00003ff6 addl         %eax, %edx
	0x89, 0xd1, //0x00003ff8 movl         %edx, %ecx
	0xc1, 0xe9, 0x04, //0x00003ffa shrl         $4, %ecx
	0x09, 0xd1, //0x00003ffd orl          %edx, %ecx
	0x89, 0xc8, //0x00003fff movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x00004001 shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x00004004 andl         $65280, %eax
	0x89, 0xca, //0x00004009 movl         %ecx, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x0000400b andl         $128, %edx
	0x09, 0xc2, //0x00004011 orl          %eax, %edx
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x00004013 je           LBB1_955
	0xb9, 0xff, 0x00, 0x00, 0x00, //0x00004019 movl         $255, %ecx
	//0x0000401e LBB1_955
	0x49, 0x83, 0xc0, 0x06, //0x0000401e addq         $6, %r8
	0x4d, 0x89, 0xc5, //0x00004022 movq         %r8, %r13
	//0x00004025 LBB1_956
	0x80, 0xf9, 0x0a, //0x00004025 cmpb         $10, %cl
	0x0f, 0x84, 0xd2, 0xfe, 0xff, 0xff, //0x00004028 je           LBB1_959
	0x80, 0xf9, 0x0d, //0x0000402e cmpb         $13, %cl
	0x0f, 0x84, 0xc9, 0xfe, 0xff, 0xff, //0x00004031 je           LBB1_959
	0x80, 0xf9, 0x3d, //0x00004037 cmpb         $61, %cl
	0x0f, 0x85, 0x73, 0x06, 0x00, 0x00, //0x0000403a jne          LBB1_1026
	0x4d, 0x89, 0xe8, //0x00004040 movq         %r13, %r8
	0x41, 0x83, 0xfe, 0x02, //0x00004043 cmpl         $2, %r14d
	0x0f, 0x84, 0x23, 0xf9, 0xff, 0xff, //0x00004047 je           LBB1_856
	0x4c, 0x39, 0xeb, //0x0000404d cmpq         %r13, %rbx
	0x0f, 0x87, 0xc7, 0x03, 0x00, 0x00, //0x00004050 ja           LBB1_1029
	//0x00004056 LBB1_994
	0x4d, 0x89, 0xe8, //0x00004056 movq         %r13, %r8
	0xe9, 0xad, 0x05, 0x00, 0x00, //0x00004059 jmp          LBB1_1063
	//0x0000405e LBB1_960
	0x41, 0x89, 0xd7, //0x0000405e movl         %edx, %r15d
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00004061 movl         $1, %r11d
	0xe9, 0x3a, 0xfc, 0xff, 0xff, //0x00004067 jmp          LBB1_903
	//0x0000406c LBB1_961
	0x49, 0xff, 0xc0, //0x0000406c incq         %r8
	0x48, 0xff, 0xc1, //0x0000406f incq         %rcx
	0x48, 0xff, 0xce, //0x00004072 decq         %rsi
	0x48, 0xff, 0xc2, //0x00004075 incq         %rdx
	0x48, 0xff, 0xcf, //0x00004078 decq         %rdi
	0x4c, 0x39, 0xc3, //0x0000407b cmpq         %r8, %rbx
	0x0f, 0x84, 0x12, 0x05, 0x00, 0x00, //0x0000407e je           LBB1_993
	//0x00004084 LBB1_962
	0x41, 0x0f, 0xb6, 0x00, //0x00004084 movzbl       (%r8), %eax
	0x3c, 0x0a, //0x00004088 cmpb         $10, %al
	0x0f, 0x84, 0xdc, 0xff, 0xff, 0xff, //0x0000408a je           LBB1_961
	0x3c, 0x0d, //0x00004090 cmpb         $13, %al
	0x0f, 0x84, 0xd4, 0xff, 0xff, 0xff, //0x00004092 je           LBB1_961
	0x3c, 0x3d, //0x00004098 cmpb         $61, %al
	0x0f, 0x85, 0xb1, 0x01, 0x00, 0x00, //0x0000409a jne          LBB1_855
	0x49, 0xff, 0xc0, //0x000040a0 incq         %r8
	0x41, 0x83, 0xfe, 0x03, //0x000040a3 cmpl         $3, %r14d
	0x0f, 0x84, 0xc3, 0xf8, 0xff, 0xff, //0x000040a7 je           LBB1_856
	0x4c, 0x39, 0xc3, //0x000040ad cmpq         %r8, %rbx
	0x0f, 0x87, 0xa2, 0x04, 0x00, 0x00, //0x000040b0 ja           LBB1_1048
	0xe9, 0x50, 0x05, 0x00, 0x00, //0x000040b6 jmp          LBB1_1063
	//0x000040bb LBB1_967
	0x80, 0xf9, 0x2f, //0x000040bb cmpb         $47, %cl
	0x0f, 0x85, 0x02, 0x00, 0x00, 0x00, //0x000040be jne          LBB1_969
	0x89, 0xc8, //0x000040c4 movl         %ecx, %eax
	//0x000040c6 LBB1_969
	0x4d, 0x89, 0xd0, //0x000040c6 movq         %r10, %r8
	0x48, 0x8b, 0x5d, 0xd0, //0x000040c9 movq         $-48(%rbp), %rbx
	//0x000040cd LBB1_970
	0x0f, 0xb6, 0xc8, //0x000040cd movzbl       %al, %ecx
	0x48, 0x8b, 0x55, 0xc0, //0x000040d0 movq         $-64(%rbp), %rdx
	0x0f, 0xb6, 0x0c, 0x0a, //0x000040d4 movzbl       (%rdx,%rcx), %ecx
	0x81, 0xf9, 0xff, 0x00, 0x00, 0x00, //0x000040d8 cmpl         $255, %ecx
	0x0f, 0x84, 0x5a, 0x01, 0x00, 0x00, //0x000040de je           LBB1_991
	0x44, 0x89, 0xfa, //0x000040e4 movl         %r15d, %edx
	0xc1, 0xe2, 0x06, //0x000040e7 shll         $6, %edx
	0x09, 0xca, //0x000040ea orl          %ecx, %edx
	0x41, 0xbb, 0x02, 0x00, 0x00, 0x00, //0x000040ec movl         $2, %r11d
	0x49, 0x39, 0xd8, //0x000040f2 cmpq         %rbx, %r8
	0x0f, 0x83, 0xe5, 0x04, 0x00, 0x00, //0x000040f5 jae          LBB1_1060
	0x41, 0x89, 0xd7, //0x000040fb movl         %edx, %r15d
	0xe9, 0x21, 0x00, 0x00, 0x00, //0x000040fe jmp          LBB1_976
	//0x00004103 LBB1_973
	0x80, 0xf9, 0x6e, //0x00004103 cmpb         $110, %cl
	0x0f, 0x85, 0x50, 0x01, 0x00, 0x00, //0x00004106 jne          LBB1_995
	//0x0000410c LBB1_974
	0x48, 0x89, 0xd7, //0x0000410c movq         %rdx, %rdi
	0x44, 0x89, 0xfa, //0x0000410f movl         %r15d, %edx
	//0x00004112 LBB1_975
	0x41, 0xbb, 0x02, 0x00, 0x00, 0x00, //0x00004112 movl         $2, %r11d
	0x49, 0x89, 0xf8, //0x00004118 movq         %rdi, %r8
	0x48, 0x39, 0xdf, //0x0000411b cmpq         %rbx, %rdi
	0x0f, 0x83, 0x25, 0x01, 0x00, 0x00, //0x0000411e jae          LBB1_992
	//0x00004124 LBB1_976
	0x49, 0x8d, 0x78, 0x01, //0x00004124 leaq         $1(%r8), %rdi
	0x41, 0x0f, 0xb6, 0x00, //0x00004128 movzbl       (%r8), %eax
	0x3c, 0x5c, //0x0000412c cmpb         $92, %al
	0x0f, 0x85, 0xf5, 0x00, 0x00, 0x00, //0x0000412e jne          LBB1_989
	0x49, 0x8d, 0x50, 0x02, //0x00004134 leaq         $2(%r8), %rdx
	0xb0, 0xff, //0x00004138 movb         $-1, %al
	0x48, 0x39, 0xda, //0x0000413a cmpq         %rbx, %rdx
	0x0f, 0x87, 0x27, 0x01, 0x00, 0x00, //0x0000413d ja           LBB1_998
	0x0f, 0xb6, 0x0f, //0x00004143 movzbl       (%rdi), %ecx
	0x80, 0xf9, 0x71, //0x00004146 cmpb         $113, %cl
	0x0f, 0x8e, 0xb4, 0xff, 0xff, 0xff, //0x00004149 jle          LBB1_973
	0x80, 0xf9, 0x72, //0x0000414f cmpb         $114, %cl
	0x0f, 0x84, 0xb4, 0xff, 0xff, 0xff, //0x00004152 je           LBB1_974
	0x80, 0xf9, 0x75, //0x00004158 cmpb         $117, %cl
	0x0f, 0x85, 0x06, 0x01, 0x00, 0x00, //0x0000415b jne          LBB1_997
	0x48, 0x89, 0xd9, //0x00004161 movq         %rbx, %rcx
	0x48, 0x29, 0xd1, //0x00004164 subq         %rdx, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00004167 cmpq         $4, %rcx
	0x0f, 0x8c, 0xf6, 0x00, 0x00, 0x00, //0x0000416b jl           LBB1_997
	0x8b, 0x32, //0x00004171 movl         (%rdx), %esi
	0x89, 0xf7, //0x00004173 movl         %esi, %edi
	0xf7, 0xd7, //0x00004175 notl         %edi
	0x8d, 0x8e, 0xd0, 0xcf, 0xcf, 0xcf, //0x00004177 leal         $-808464432(%rsi), %ecx
	0x81, 0xe7, 0x80, 0x80, 0x80, 0x80, //0x0000417d andl         $-2139062144, %edi
	0x85, 0xcf, //0x00004183 testl        %ecx, %edi
	0x0f, 0x85, 0xdc, 0x00, 0x00, 0x00, //0x00004185 jne          LBB1_997
	0x8d, 0x8e, 0x19, 0x19, 0x19, 0x19, //0x0000418b leal         $421075225(%rsi), %ecx
	0x09, 0xf1, //0x00004191 orl          %esi, %ecx
	0xf7, 0xc1, 0x80, 0x80, 0x80, 0x80, //0x00004193 testl        $-2139062144, %ecx
	0x0f, 0x85, 0xc8, 0x00, 0x00, 0x00, //0x00004199 jne          LBB1_997
	0x89, 0xf1, //0x0000419f movl         %esi, %ecx
	0x81, 0xe1, 0x7f, 0x7f, 0x7f, 0x7f, //0x000041a1 andl         $2139062143, %ecx
	0xbb, 0xc0, 0xc0, 0xc0, 0xc0, //0x000041a7 movl         $-1061109568, %ebx
	0x29, 0xcb, //0x000041ac subl         %ecx, %ebx
	0x44, 0x8d, 0x91, 0x46, 0x46, 0x46, 0x46, //0x000041ae leal         $1179010630(%rcx), %r10d
	0x21, 0xfb, //0x000041b5 andl         %edi, %ebx
	0x44, 0x85, 0xd3, //0x000041b7 testl        %r10d, %ebx
	0x48, 0x8b, 0x5d, 0xd0, //0x000041ba movq         $-48(%rbp), %rbx
	0x0f, 0x85, 0xa3, 0x00, 0x00, 0x00, //0x000041be jne          LBB1_997
	0xbb, 0xe0, 0xe0, 0xe0, 0xe0, //0x000041c4 movl         $-522133280, %ebx
	0x29, 0xcb, //0x000041c9 subl         %ecx, %ebx
	0x81, 0xc1, 0x39, 0x39, 0x39, 0x39, //0x000041cb addl         $960051513, %ecx
	0x21, 0xdf, //0x000041d1 andl         %ebx, %edi
	0x48, 0x8b, 0x5d, 0xd0, //0x000041d3 movq         $-48(%rbp), %rbx
	0x85, 0xcf, //0x000041d7 testl        %ecx, %edi
	0x0f, 0x85, 0x88, 0x00, 0x00, 0x00, //0x000041d9 jne          LBB1_997
	0x0f, 0xce, //0x000041df bswapl       %esi
	0x89, 0xf0, //0x000041e1 movl         %esi, %eax
	0xc1, 0xe8, 0x04, //0x000041e3 shrl         $4, %eax
	0xf7, 0xd0, //0x000041e6 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x000041e8 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x000041ed leal         (%rax,%rax,8), %eax
	0x81, 0xe6, 0x0f, 0x0f, 0x0f, 0x0f, //0x000041f0 andl         $252645135, %esi
	0x01, 0xc6, //0x000041f6 addl         %eax, %esi
	0x89, 0xf0, //0x000041f8 movl         %esi, %eax
	0xc1, 0xe8, 0x04, //0x000041fa shrl         $4, %eax
	0x09, 0xf0, //0x000041fd orl          %esi, %eax
	0x89, 0xc1, //0x000041ff movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x00004201 shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x00004204 andl         $65280, %ecx
	0x89, 0xc2, //0x0000420a movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x0000420c andl         $128, %edx
	0x09, 0xca, //0x00004212 orl          %ecx, %edx
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x00004214 je           LBB1_988
	0xb8, 0xff, 0x00, 0x00, 0x00, //0x0000421a movl         $255, %eax
	//0x0000421f LBB1_988
	0x49, 0x83, 0xc0, 0x06, //0x0000421f addq         $6, %r8
	0x4c, 0x89, 0xc7, //0x00004223 movq         %r8, %rdi
	0x44, 0x89, 0xfa, //0x00004226 movl         %r15d, %edx
	//0x00004229 LBB1_989
	0x3c, 0x0a, //0x00004229 cmpb         $10, %al
	0x0f, 0x84, 0xe1, 0xfe, 0xff, 0xff, //0x0000422b je           LBB1_975
	0x3c, 0x0d, //0x00004231 cmpb         $13, %al
	0x0f, 0x84, 0xd9, 0xfe, 0xff, 0xff, //0x00004233 je           LBB1_975
	0xe9, 0x2c, 0x00, 0x00, 0x00, //0x00004239 jmp          LBB1_998
	//0x0000423e LBB1_991
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x0000423e movl         $1, %r11d
	0xe9, 0x5d, 0xfa, 0xff, 0xff, //0x00004244 jmp          LBB1_903
	//0x00004249 LBB1_992
	0x49, 0x89, 0xf8, //0x00004249 movq         %rdi, %r8
	0xe9, 0x74, 0xf9, 0xff, 0xff, //0x0000424c jmp          LBB1_889
	//0x00004251 LBB1_855
	0x49, 0xff, 0xc0, //0x00004251 incq         %r8
	0x4c, 0x89, 0xc2, //0x00004254 movq         %r8, %rdx
	0xe9, 0x14, 0xf7, 0xff, 0xff, //0x00004257 jmp          LBB1_856
	//0x0000425c LBB1_995
	0x80, 0xf9, 0x2f, //0x0000425c cmpb         $47, %cl
	0x0f, 0x85, 0x02, 0x00, 0x00, 0x00, //0x0000425f jne          LBB1_997
	0x89, 0xc8, //0x00004265 movl         %ecx, %eax
	//0x00004267 LBB1_997
	0x48, 0x89, 0xd7, //0x00004267 movq         %rdx, %rdi
	//0x0000426a LBB1_998
	0x0f, 0xb6, 0xc8, //0x0000426a movzbl       %al, %ecx
	0x48, 0x8b, 0x55, 0xc0, //0x0000426d movq         $-64(%rbp), %rdx
	0x0f, 0xb6, 0x0c, 0x0a, //0x00004271 movzbl       (%rdx,%rcx), %ecx
	0x81, 0xf9, 0xff, 0x00, 0x00, 0x00, //0x00004275 cmpl         $255, %ecx
	0x0f, 0x84, 0x55, 0x01, 0x00, 0x00, //0x0000427b je           LBB1_1019
	0x44, 0x89, 0xfa, //0x00004281 movl         %r15d, %edx
	0xc1, 0xe2, 0x06, //0x00004284 shll         $6, %edx
	0x09, 0xca, //0x00004287 orl          %ecx, %edx
	0x41, 0xbb, 0x03, 0x00, 0x00, 0x00, //0x00004289 movl         $3, %r11d
	0x48, 0x39, 0xdf, //0x0000428f cmpq         %rbx, %rdi
	0x0f, 0x83, 0x4c, 0x01, 0x00, 0x00, //0x00004292 jae          LBB1_1020
	0x41, 0x89, 0xd7, //0x00004298 movl         %edx, %r15d
	0xe9, 0x1e, 0x00, 0x00, 0x00, //0x0000429b jmp          LBB1_1004
	//0x000042a0 LBB1_1001
	0x80, 0xf9, 0x6e, //0x000042a0 cmpb         $110, %cl
	0x0f, 0x85, 0xf5, 0x02, 0x00, 0x00, //0x000042a3 jne          LBB1_1054
	//0x000042a9 LBB1_1002
	0x4d, 0x89, 0xd0, //0x000042a9 movq         %r10, %r8
	//0x000042ac LBB1_1003
	0x41, 0xbb, 0x03, 0x00, 0x00, 0x00, //0x000042ac movl         $3, %r11d
	0x4c, 0x89, 0xc7, //0x000042b2 movq         %r8, %rdi
	0x49, 0x39, 0xd8, //0x000042b5 cmpq         %rbx, %r8
	0x0f, 0x83, 0x07, 0xf9, 0xff, 0xff, //0x000042b8 jae          LBB1_889
	//0x000042be LBB1_1004
	0x4c, 0x8d, 0x47, 0x01, //0x000042be leaq         $1(%rdi), %r8
	0x0f, 0xb6, 0x07, //0x000042c2 movzbl       (%rdi), %eax
	0x3c, 0x5c, //0x000042c5 cmpb         $92, %al
	0x0f, 0x85, 0xf4, 0x00, 0x00, 0x00, //0x000042c7 jne          LBB1_1017
	0x4c, 0x8d, 0x57, 0x02, //0x000042cd leaq         $2(%rdi), %r10
	0xb0, 0xff, //0x000042d1 movb         $-1, %al
	0x49, 0x39, 0xda, //0x000042d3 cmpq         %rbx, %r10
	0x0f, 0x87, 0xd4, 0x02, 0x00, 0x00, //0x000042d6 ja           LBB1_1057
	0x41, 0x0f, 0xb6, 0x08, //0x000042dc movzbl       (%r8), %ecx
	0x80, 0xf9, 0x71, //0x000042e0 cmpb         $113, %cl
	0x0f, 0x8e, 0xb7, 0xff, 0xff, 0xff, //0x000042e3 jle          LBB1_1001
	0x80, 0xf9, 0x72, //0x000042e9 cmpb         $114, %cl
	0x0f, 0x84, 0xb7, 0xff, 0xff, 0xff, //0x000042ec je           LBB1_1002
	0x80, 0xf9, 0x75, //0x000042f2 cmpb         $117, %cl
	0x0f, 0x85, 0xae, 0x02, 0x00, 0x00, //0x000042f5 jne          LBB1_1056
	0x48, 0x8b, 0x4d, 0xd0, //0x000042fb movq         $-48(%rbp), %rcx
	0x4c, 0x29, 0xd1, //0x000042ff subq         %r10, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00004302 cmpq         $4, %rcx
	0x0f, 0x8c, 0x9d, 0x02, 0x00, 0x00, //0x00004306 jl           LBB1_1056
	0x41, 0x8b, 0x32, //0x0000430c movl         (%r10), %esi
	0x89, 0xf3, //0x0000430f movl         %esi, %ebx
	0xf7, 0xd3, //0x00004311 notl         %ebx
	0x8d, 0x8e, 0xd0, 0xcf, 0xcf, 0xcf, //0x00004313 leal         $-808464432(%rsi), %ecx
	0x81, 0xe3, 0x80, 0x80, 0x80, 0x80, //0x00004319 andl         $-2139062144, %ebx
	0x85, 0xcb, //0x0000431f testl        %ecx, %ebx
	0x0f, 0x85, 0x82, 0x02, 0x00, 0x00, //0x00004321 jne          LBB1_1056
	0x8d, 0x8e, 0x19, 0x19, 0x19, 0x19, //0x00004327 leal         $421075225(%rsi), %ecx
	0x09, 0xf1, //0x0000432d orl          %esi, %ecx
	0xf7, 0xc1, 0x80, 0x80, 0x80, 0x80, //0x0000432f testl        $-2139062144, %ecx
	0x0f, 0x85, 0x6e, 0x02, 0x00, 0x00, //0x00004335 jne          LBB1_1056
	0x89, 0xf1, //0x0000433b movl         %esi, %ecx
	0x81, 0xe1, 0x7f, 0x7f, 0x7f, 0x7f, //0x0000433d andl         $2139062143, %ecx
	0xba, 0xc0, 0xc0, 0xc0, 0xc0, //0x00004343 movl         $-1061109568, %edx
	0x29, 0xca, //0x00004348 subl         %ecx, %edx
	0x44, 0x8d, 0x81, 0x46, 0x46, 0x46, 0x46, //0x0000434a leal         $1179010630(%rcx), %r8d
	0x21, 0xda, //0x00004351 andl         %ebx, %edx
	0x44, 0x85, 0xc2, //0x00004353 testl        %r8d, %edx
	0x0f, 0x85, 0x4d, 0x02, 0x00, 0x00, //0x00004356 jne          LBB1_1056
	0xba, 0xe0, 0xe0, 0xe0, 0xe0, //0x0000435c movl         $-522133280, %edx
	0x29, 0xca, //0x00004361 subl         %ecx, %edx
	0x81, 0xc1, 0x39, 0x39, 0x39, 0x39, //0x00004363 addl         $960051513, %ecx
	0x21, 0xd3, //0x00004369 andl         %edx, %ebx
	0x85, 0xcb, //0x0000436b testl        %ecx, %ebx
	0x0f, 0x85, 0x36, 0x02, 0x00, 0x00, //0x0000436d jne          LBB1_1056
	0x0f, 0xce, //0x00004373 bswapl       %esi
	0x89, 0xf0, //0x00004375 movl         %esi, %eax
	0xc1, 0xe8, 0x04, //0x00004377 shrl         $4, %eax
	0xf7, 0xd0, //0x0000437a notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x0000437c andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00004381 leal         (%rax,%rax,8), %eax
	0x81, 0xe6, 0x0f, 0x0f, 0x0f, 0x0f, //0x00004384 andl         $252645135, %esi
	0x01, 0xc6, //0x0000438a addl         %eax, %esi
	0x89, 0xf0, //0x0000438c movl         %esi, %eax
	0xc1, 0xe8, 0x04, //0x0000438e shrl         $4, %eax
	0x09, 0xf0, //0x00004391 orl          %esi, %eax
	0x89, 0xc1, //0x00004393 movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x00004395 shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x00004398 andl         $65280, %ecx
	0x89, 0xc2, //0x0000439e movl         %eax, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x000043a0 andl         $128, %edx
	0x09, 0xca, //0x000043a6 orl          %ecx, %edx
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x000043a8 je           LBB1_1016
	0xb8, 0xff, 0x00, 0x00, 0x00, //0x000043ae movl         $255, %eax
	//0x000043b3 LBB1_1016
	0x48, 0x83, 0xc7, 0x06, //0x000043b3 addq         $6, %rdi
	0x49, 0x89, 0xf8, //0x000043b7 movq         %rdi, %r8
	0x48, 0x8b, 0x5d, 0xd0, //0x000043ba movq         $-48(%rbp), %rbx
	0x44, 0x89, 0xfa, //0x000043be movl         %r15d, %edx
	//0x000043c1 LBB1_1017
	0x3c, 0x0a, //0x000043c1 cmpb         $10, %al
	0x0f, 0x84, 0xe3, 0xfe, 0xff, 0xff, //0x000043c3 je           LBB1_1003
	0x3c, 0x0d, //0x000043c9 cmpb         $13, %al
	0x0f, 0x84, 0xdb, 0xfe, 0xff, 0xff, //0x000043cb je           LBB1_1003
	0xe9, 0xda, 0x01, 0x00, 0x00, //0x000043d1 jmp          LBB1_1057
	//0x000043d6 LBB1_1019
	0x41, 0xbb, 0x02, 0x00, 0x00, 0x00, //0x000043d6 movl         $2, %r11d
	0x49, 0x89, 0xf8, //0x000043dc movq         %rdi, %r8
	0xe9, 0xc2, 0xf8, 0xff, 0xff, //0x000043df jmp          LBB1_903
	//0x000043e4 LBB1_1020
	0x49, 0x89, 0xf8, //0x000043e4 movq         %rdi, %r8
	0xe9, 0xf4, 0x01, 0x00, 0x00, //0x000043e7 jmp          LBB1_1060
	//0x000043ec LBB1_1025
	0x41, 0x89, 0xd7, //0x000043ec movl         %edx, %r15d
	0x41, 0xbb, 0x02, 0x00, 0x00, 0x00, //0x000043ef movl         $2, %r11d
	0xe9, 0xac, 0xf8, 0xff, 0xff, //0x000043f5 jmp          LBB1_903
	//0x000043fa LBB1_1045
	0x4c, 0x89, 0xc1, //0x000043fa movq         %r8, %rcx
	0x49, 0x89, 0xcd, //0x000043fd movq         %rcx, %r13
	0x48, 0x39, 0xd9, //0x00004400 cmpq         %rbx, %rcx
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x00004403 jb           LBB1_1029
	0xe9, 0xfd, 0x01, 0x00, 0x00, //0x00004409 jmp          LBB1_1063
	//0x0000440e LBB1_1027
	0x49, 0x89, 0xc8, //0x0000440e movq         %rcx, %r8
	0x49, 0x89, 0xcd, //0x00004411 movq         %rcx, %r13
	0x48, 0x39, 0xd9, //0x00004414 cmpq         %rbx, %rcx
	0x0f, 0x83, 0xee, 0x01, 0x00, 0x00, //0x00004417 jae          LBB1_1063
	//0x0000441d LBB1_1029
	0x4d, 0x8d, 0x45, 0x01, //0x0000441d leaq         $1(%r13), %r8
	0x41, 0x0f, 0xb6, 0x4d, 0x00, //0x00004421 movzbl       (%r13), %ecx
	0x80, 0xf9, 0x5c, //0x00004426 cmpb         $92, %cl
	0x0f, 0x85, 0xe4, 0x00, 0x00, 0x00, //0x00004429 jne          LBB1_1042
	0x49, 0x8d, 0x4d, 0x02, //0x0000442f leaq         $2(%r13), %rcx
	0x48, 0x39, 0xd9, //0x00004433 cmpq         %rbx, %rcx
	0x0f, 0x87, 0x74, 0x02, 0x00, 0x00, //0x00004436 ja           LBB1_1080
	0x41, 0x0f, 0xb6, 0x00, //0x0000443c movzbl       (%r8), %eax
	0x3c, 0x6e, //0x00004440 cmpb         $110, %al
	0x0f, 0x84, 0xc6, 0xff, 0xff, 0xff, //0x00004442 je           LBB1_1027
	0x3c, 0x72, //0x00004448 cmpb         $114, %al
	0x0f, 0x84, 0xbe, 0xff, 0xff, 0xff, //0x0000444a je           LBB1_1027
	0x3c, 0x75, //0x00004450 cmpb         $117, %al
	0x0f, 0x85, 0x09, 0xf5, 0xff, 0xff, //0x00004452 jne          LBB1_1102
	0x48, 0x89, 0xd8, //0x00004458 movq         %rbx, %rax
	0x48, 0x29, 0xc8, //0x0000445b subq         %rcx, %rax
	0x48, 0x83, 0xf8, 0x04, //0x0000445e cmpq         $4, %rax
	0x0f, 0x8c, 0xf9, 0xf4, 0xff, 0xff, //0x00004462 jl           LBB1_1102
	0x8b, 0x11, //0x00004468 movl         (%rcx), %edx
	0x89, 0xd6, //0x0000446a movl         %edx, %esi
	0xf7, 0xd6, //0x0000446c notl         %esi
	0x8d, 0x82, 0xd0, 0xcf, 0xcf, 0xcf, //0x0000446e leal         $-808464432(%rdx), %eax
	0x81, 0xe6, 0x80, 0x80, 0x80, 0x80, //0x00004474 andl         $-2139062144, %esi
	0x85, 0xc6, //0x0000447a testl        %eax, %esi
	0x0f, 0x85, 0xdf, 0xf4, 0xff, 0xff, //0x0000447c jne          LBB1_1102
	0x8d, 0x82, 0x19, 0x19, 0x19, 0x19, //0x00004482 leal         $421075225(%rdx), %eax
	0x09, 0xd0, //0x00004488 orl          %edx, %eax
	0xa9, 0x80, 0x80, 0x80, 0x80, //0x0000448a testl        $-2139062144, %eax
	0x0f, 0x85, 0xcc, 0xf4, 0xff, 0xff, //0x0000448f jne          LBB1_1102
	0x89, 0xd0, //0x00004495 movl         %edx, %eax
	0x25, 0x7f, 0x7f, 0x7f, 0x7f, //0x00004497 andl         $2139062143, %eax
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x0000449c movl         $-1061109568, %edi
	0x29, 0xc7, //0x000044a1 subl         %eax, %edi
	0x8d, 0x98, 0x46, 0x46, 0x46, 0x46, //0x000044a3 leal         $1179010630(%rax), %ebx
	0x21, 0xf7, //0x000044a9 andl         %esi, %edi
	0x85, 0xdf, //0x000044ab testl        %ebx, %edi
	0x0f, 0x85, 0xac, 0x04, 0x00, 0x00, //0x000044ad jne          LBB1_1131
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x000044b3 movl         $-522133280, %edi
	0x29, 0xc7, //0x000044b8 subl         %eax, %edi
	0x05, 0x39, 0x39, 0x39, 0x39, //0x000044ba addl         $960051513, %eax
	0x21, 0xfe, //0x000044bf andl         %edi, %esi
	0x85, 0xc6, //0x000044c1 testl        %eax, %esi
	0x48, 0x8b, 0x5d, 0xd0, //0x000044c3 movq         $-48(%rbp), %rbx
	0x0f, 0x85, 0x94, 0xf4, 0xff, 0xff, //0x000044c7 jne          LBB1_1102
	0x0f, 0xca, //0x000044cd bswapl       %edx
	0x89, 0xd0, //0x000044cf movl         %edx, %eax
	0xc1, 0xe8, 0x04, //0x000044d1 shrl         $4, %eax
	0xf7, 0xd0, //0x000044d4 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x000044d6 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x000044db leal         (%rax,%rax,8), %eax
	0x81, 0xe2, 0x0f, 0x0f, 0x0f, 0x0f, //0x000044de andl         $252645135, %edx
	0x01, 0xc2, //0x000044e4 addl         %eax, %edx
	0x89, 0xd1, //0x000044e6 movl         %edx, %ecx
	0xc1, 0xe9, 0x04, //0x000044e8 shrl         $4, %ecx
	0x09, 0xd1, //0x000044eb orl          %edx, %ecx
	0x89, 0xc8, //0x000044ed movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x000044ef shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x000044f2 andl         $65280, %eax
	0x89, 0xca, //0x000044f7 movl         %ecx, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x000044f9 andl         $128, %edx
	0x09, 0xc2, //0x000044ff orl          %eax, %edx
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x00004501 je           LBB1_1041
	0xb9, 0xff, 0x00, 0x00, 0x00, //0x00004507 movl         $255, %ecx
	//0x0000450c LBB1_1041
	0x49, 0x83, 0xc5, 0x06, //0x0000450c addq         $6, %r13
	0x4d, 0x89, 0xe8, //0x00004510 movq         %r13, %r8
	//0x00004513 LBB1_1042
	0x80, 0xf9, 0x0a, //0x00004513 cmpb         $10, %cl
	0x0f, 0x84, 0xde, 0xfe, 0xff, 0xff, //0x00004516 je           LBB1_1045
	0x80, 0xf9, 0x0d, //0x0000451c cmpb         $13, %cl
	0x0f, 0x84, 0xd5, 0xfe, 0xff, 0xff, //0x0000451f je           LBB1_1045
	0x80, 0xf9, 0x3d, //0x00004525 cmpb         $61, %cl
	0x0f, 0x85, 0x42, 0xf4, 0xff, 0xff, //0x00004528 jne          LBB1_856
	0x41, 0x83, 0xfe, 0x03, //0x0000452e cmpl         $3, %r14d
	0x0f, 0x84, 0x38, 0xf4, 0xff, 0xff, //0x00004532 je           LBB1_856
	0x4c, 0x39, 0xc3, //0x00004538 cmpq         %r8, %rbx
	0x0f, 0x87, 0x9d, 0x01, 0x00, 0x00, //0x0000453b ja           LBB1_1085
	0xe9, 0xc5, 0x00, 0x00, 0x00, //0x00004541 jmp          LBB1_1063
	//0x00004546 LBB1_1047
	0x48, 0xff, 0xc1, //0x00004546 incq         %rcx
	0x48, 0xff, 0xc2, //0x00004549 incq         %rdx
	0x48, 0xff, 0xcf, //0x0000454c decq         %rdi
	0x48, 0xff, 0xce, //0x0000454f decq         %rsi
	0x0f, 0x84, 0x3e, 0x00, 0x00, 0x00, //0x00004552 je           LBB1_993
	//0x00004558 LBB1_1048
	0x0f, 0xb6, 0x41, 0xff, //0x00004558 movzbl       $-1(%rcx), %eax
	0x3c, 0x0a, //0x0000455c cmpb         $10, %al
	0x0f, 0x84, 0xe2, 0xff, 0xff, 0xff, //0x0000455e je           LBB1_1047
	0x3c, 0x0d, //0x00004564 cmpb         $13, %al
	0x0f, 0x84, 0xda, 0xff, 0xff, 0xff, //0x00004566 je           LBB1_1047
	0x3c, 0x3d, //0x0000456c cmpb         $61, %al
	0x0f, 0x85, 0xa1, 0x02, 0x00, 0x00, //0x0000456e jne          LBB1_1103
	0x49, 0x89, 0xc8, //0x00004574 movq         %rcx, %r8
	0x41, 0x83, 0xfe, 0x04, //0x00004577 cmpl         $4, %r14d
	0x0f, 0x84, 0xef, 0xf3, 0xff, 0xff, //0x0000457b je           LBB1_856
	0x48, 0x39, 0xcb, //0x00004581 cmpq         %rcx, %rbx
	0x0f, 0x87, 0x02, 0x01, 0x00, 0x00, //0x00004584 ja           LBB1_1076
	0x49, 0x89, 0xc8, //0x0000458a movq         %rcx, %r8
	0x48, 0x8b, 0x5d, 0xd0, //0x0000458d movq         $-48(%rbp), %rbx
	0xe9, 0x75, 0x00, 0x00, 0x00, //0x00004591 jmp          LBB1_1063
	//0x00004596 LBB1_993
	0x49, 0x89, 0xd8, //0x00004596 movq         %rbx, %r8
	0xe9, 0x6d, 0x00, 0x00, 0x00, //0x00004599 jmp          LBB1_1063
	//0x0000459e LBB1_1054
	0x80, 0xf9, 0x2f, //0x0000459e cmpb         $47, %cl
	0x0f, 0x85, 0x02, 0x00, 0x00, 0x00, //0x000045a1 jne          LBB1_1056
	0x89, 0xc8, //0x000045a7 movl         %ecx, %eax
	//0x000045a9 LBB1_1056
	0x4d, 0x89, 0xd0, //0x000045a9 movq         %r10, %r8
	0x48, 0x8b, 0x5d, 0xd0, //0x000045ac movq         $-48(%rbp), %rbx
	//0x000045b0 LBB1_1057
	0x0f, 0xb6, 0xc8, //0x000045b0 movzbl       %al, %ecx
	0x48, 0x8b, 0x55, 0xc0, //0x000045b3 movq         $-64(%rbp), %rdx
	0x0f, 0xb6, 0x0c, 0x0a, //0x000045b7 movzbl       (%rdx,%rcx), %ecx
	0x81, 0xf9, 0xff, 0x00, 0x00, 0x00, //0x000045bb cmpl         $255, %ecx
	0x0f, 0x84, 0xae, 0x00, 0x00, 0x00, //0x000045c1 je           LBB1_1070
	0x44, 0x89, 0xfa, //0x000045c7 movl         %r15d, %edx
	//0x000045ca LBB1_1059
	0xc1, 0xe2, 0x06, //0x000045ca shll         $6, %edx
	0x09, 0xca, //0x000045cd orl          %ecx, %edx
	0x41, 0xbb, 0x04, 0x00, 0x00, 0x00, //0x000045cf movl         $4, %r11d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000045d5 .p2align 4, 0x90
	//0x000045e0 LBB1_1060
	0x41, 0x89, 0xd7, //0x000045e0 movl         %edx, %r15d
	0x45, 0x85, 0xe4, //0x000045e3 testl        %r12d, %r12d
	0x0f, 0x94, 0xc0, //0x000045e6 sete         %al
	0x41, 0x83, 0xfb, 0x01, //0x000045e9 cmpl         $1, %r11d
	0x0f, 0x94, 0xc1, //0x000045ed sete         %cl
	0x49, 0x39, 0xd8, //0x000045f0 cmpq         %rbx, %r8
	0x0f, 0x82, 0x12, 0x00, 0x00, 0x00, //0x000045f3 jb           LBB1_1063
	0x41, 0x83, 0xfb, 0x04, //0x000045f9 cmpl         $4, %r11d
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x000045fd je           LBB1_1063
	0x08, 0xc8, //0x00004603 orb          %cl, %al
	0x0f, 0x85, 0x65, 0xf3, 0xff, 0xff, //0x00004605 jne          LBB1_856
	//0x0000460b LBB1_1063
	0xb0, 0x04, //0x0000460b movb         $4, %al
	0x44, 0x28, 0xd8, //0x0000460d subb         %r11b, %al
	0x0f, 0xb6, 0xc0, //0x00004610 movzbl       %al, %eax
	0x01, 0xc0, //0x00004613 addl         %eax, %eax
	0x8d, 0x0c, 0x40, //0x00004615 leal         (%rax,%rax,2), %ecx
	0x44, 0x89, 0xfa, //0x00004618 movl         %r15d, %edx
	0xd3, 0xe2, //0x0000461b shll         %cl, %edx
	0x41, 0x83, 0xfb, 0x02, //0x0000461d cmpl         $2, %r11d
	0x0f, 0x84, 0x22, 0x00, 0x00, 0x00, //0x00004621 je           LBB1_1068
	0x41, 0x83, 0xfb, 0x03, //0x00004627 cmpl         $3, %r11d
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x0000462b je           LBB1_1067
	0x41, 0x83, 0xfb, 0x04, //0x00004631 cmpl         $4, %r11d
	0x48, 0x8b, 0x4d, 0xb0, //0x00004635 movq         $-80(%rbp), %rcx
	0x0f, 0x85, 0x13, 0x00, 0x00, 0x00, //0x00004639 jne          LBB1_1069
	0x88, 0x51, 0x02, //0x0000463f movb         %dl, $2(%rcx)
	//0x00004642 LBB1_1067
	0x48, 0x8b, 0x45, 0xb0, //0x00004642 movq         $-80(%rbp), %rax
	0x88, 0x70, 0x01, //0x00004646 movb         %dh, $1(%rax)
	//0x00004649 LBB1_1068
	0xc1, 0xea, 0x10, //0x00004649 shrl         $16, %edx
	0x48, 0x8b, 0x4d, 0xb0, //0x0000464c movq         $-80(%rbp), %rcx
	0x88, 0x11, //0x00004650 movb         %dl, (%rcx)
	//0x00004652 LBB1_1069
	0x44, 0x89, 0xd8, //0x00004652 movl         %r11d, %eax
	0x48, 0x8d, 0x4c, 0x01, 0xff, //0x00004655 leaq         $-1(%rcx,%rax), %rcx
	0x48, 0x89, 0x4d, 0xb0, //0x0000465a movq         %rcx, $-80(%rbp)
	0x4d, 0x89, 0xc1, //0x0000465e movq         %r8, %r9
	0x31, 0xc9, //0x00004661 xorl         %ecx, %ecx
	0x44, 0x8b, 0x75, 0xbc, //0x00004663 movl         $-68(%rbp), %r14d
	0x48, 0x85, 0xc9, //0x00004667 testq        %rcx, %rcx
	0x0f, 0x84, 0x1b, 0xf3, 0xff, 0xff, //0x0000466a je           LBB1_857
	0xe9, 0x2b, 0x03, 0x00, 0x00, //0x00004670 jmp          LBB1_1137
	//0x00004675 LBB1_1070
	0x41, 0xbb, 0x03, 0x00, 0x00, 0x00, //0x00004675 movl         $3, %r11d
	0xe9, 0x26, 0xf6, 0xff, 0xff, //0x0000467b jmp          LBB1_903
	//0x00004680 LBB1_1075
	0x48, 0xff, 0xc2, //0x00004680 incq         %rdx
	0x48, 0xff, 0xcf, //0x00004683 decq         %rdi
	0x0f, 0x84, 0x94, 0x01, 0x00, 0x00, //0x00004686 je           LBB1_1105
	//0x0000468c LBB1_1076
	0x0f, 0xb6, 0x42, 0xff, //0x0000468c movzbl       $-1(%rdx), %eax
	0x3c, 0x0a, //0x00004690 cmpb         $10, %al
	0x0f, 0x84, 0xe8, 0xff, 0xff, 0xff, //0x00004692 je           LBB1_1075
	0x3c, 0x0d, //0x00004698 cmpb         $13, %al
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x0000469a je           LBB1_1075
	0x3c, 0x3d, //0x000046a0 cmpb         $61, %al
	0x0f, 0x85, 0xc3, 0x02, 0x00, 0x00, //0x000046a2 jne          LBB1_1110
	0x49, 0x89, 0xd0, //0x000046a8 movq         %rdx, %r8
	0xe9, 0xb2, 0x02, 0x00, 0x00, //0x000046ab jmp          LBB1_1132
	//0x000046b0 LBB1_1080
	0x4d, 0x89, 0xc5, //0x000046b0 movq         %r8, %r13
	//0x000046b3 LBB1_1026
	0x4d, 0x89, 0xe8, //0x000046b3 movq         %r13, %r8
	0xe9, 0xb5, 0xf2, 0xff, 0xff, //0x000046b6 jmp          LBB1_856
	//0x000046bb LBB1_1101
	0x4c, 0x89, 0xe9, //0x000046bb movq         %r13, %rcx
	0x49, 0x89, 0xc8, //0x000046be movq         %rcx, %r8
	0x48, 0x39, 0xd9, //0x000046c1 cmpq         %rbx, %rcx
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x000046c4 jb           LBB1_1085
	0xe9, 0x87, 0xf9, 0xff, 0xff, //0x000046ca jmp          LBB1_994
	//0x000046cf LBB1_1083
	0x49, 0x89, 0xcd, //0x000046cf movq         %rcx, %r13
	0x49, 0x89, 0xc8, //0x000046d2 movq         %rcx, %r8
	0x48, 0x39, 0xd9, //0x000046d5 cmpq         %rbx, %rcx
	0x0f, 0x83, 0x78, 0xf9, 0xff, 0xff, //0x000046d8 jae          LBB1_994
	//0x000046de LBB1_1085
	0x4d, 0x8d, 0x68, 0x01, //0x000046de leaq         $1(%r8), %r13
	0x41, 0x0f, 0xb6, 0x08, //0x000046e2 movzbl       (%r8), %ecx
	0x80, 0xf9, 0x5c, //0x000046e6 cmpb         $92, %cl
	0x0f, 0x85, 0xe5, 0x00, 0x00, 0x00, //0x000046e9 jne          LBB1_1098
	0x49, 0x8d, 0x48, 0x02, //0x000046ef leaq         $2(%r8), %rcx
	0x48, 0x39, 0xd9, //0x000046f3 cmpq         %rbx, %rcx
	0x0f, 0x87, 0xb7, 0xff, 0xff, 0xff, //0x000046f6 ja           LBB1_1026
	0x41, 0x0f, 0xb6, 0x45, 0x00, //0x000046fc movzbl       (%r13), %eax
	0x3c, 0x6e, //0x00004701 cmpb         $110, %al
	0x0f, 0x84, 0xc6, 0xff, 0xff, 0xff, //0x00004703 je           LBB1_1083
	0x3c, 0x72, //0x00004709 cmpb         $114, %al
	0x0f, 0x84, 0xbe, 0xff, 0xff, 0xff, //0x0000470b je           LBB1_1083
	0x3c, 0x75, //0x00004711 cmpb         $117, %al
	0x0f, 0x85, 0x48, 0xf2, 0xff, 0xff, //0x00004713 jne          LBB1_1102
	0x48, 0x89, 0xd8, //0x00004719 movq         %rbx, %rax
	0x48, 0x29, 0xc8, //0x0000471c subq         %rcx, %rax
	0x48, 0x83, 0xf8, 0x04, //0x0000471f cmpq         $4, %rax
	0x0f, 0x8c, 0x36, 0x02, 0x00, 0x00, //0x00004723 jl           LBB1_1131
	0x8b, 0x11, //0x00004729 movl         (%rcx), %edx
	0x89, 0xd6, //0x0000472b movl         %edx, %esi
	0xf7, 0xd6, //0x0000472d notl         %esi
	0x8d, 0x82, 0xd0, 0xcf, 0xcf, 0xcf, //0x0000472f leal         $-808464432(%rdx), %eax
	0x81, 0xe6, 0x80, 0x80, 0x80, 0x80, //0x00004735 andl         $-2139062144, %esi
	0x85, 0xc6, //0x0000473b testl        %eax, %esi
	0x0f, 0x85, 0x1c, 0x02, 0x00, 0x00, //0x0000473d jne          LBB1_1131
	0x8d, 0x82, 0x19, 0x19, 0x19, 0x19, //0x00004743 leal         $421075225(%rdx), %eax
	0x09, 0xd0, //0x00004749 orl          %edx, %eax
	0xa9, 0x80, 0x80, 0x80, 0x80, //0x0000474b testl        $-2139062144, %eax
	0x0f, 0x85, 0x09, 0x02, 0x00, 0x00, //0x00004750 jne          LBB1_1131
	0x89, 0xd0, //0x00004756 movl         %edx, %eax
	0x25, 0x7f, 0x7f, 0x7f, 0x7f, //0x00004758 andl         $2139062143, %eax
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x0000475d movl         $-1061109568, %edi
	0x29, 0xc7, //0x00004762 subl         %eax, %edi
	0x8d, 0x98, 0x46, 0x46, 0x46, 0x46, //0x00004764 leal         $1179010630(%rax), %ebx
	0x21, 0xf7, //0x0000476a andl         %esi, %edi
	0x85, 0xdf, //0x0000476c testl        %ebx, %edi
	0x0f, 0x85, 0xeb, 0x01, 0x00, 0x00, //0x0000476e jne          LBB1_1131
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x00004774 movl         $-522133280, %edi
	0x29, 0xc7, //0x00004779 subl         %eax, %edi
	0x05, 0x39, 0x39, 0x39, 0x39, //0x0000477b addl         $960051513, %eax
	0x21, 0xfe, //0x00004780 andl         %edi, %esi
	0x85, 0xc6, //0x00004782 testl        %eax, %esi
	0x0f, 0x85, 0xd5, 0x01, 0x00, 0x00, //0x00004784 jne          LBB1_1131
	0x0f, 0xca, //0x0000478a bswapl       %edx
	0x89, 0xd0, //0x0000478c movl         %edx, %eax
	0xc1, 0xe8, 0x04, //0x0000478e shrl         $4, %eax
	0xf7, 0xd0, //0x00004791 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00004793 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00004798 leal         (%rax,%rax,8), %eax
	0x81, 0xe2, 0x0f, 0x0f, 0x0f, 0x0f, //0x0000479b andl         $252645135, %edx
	0x01, 0xc2, //0x000047a1 addl         %eax, %edx
	0x89, 0xd1, //0x000047a3 movl         %edx, %ecx
	0xc1, 0xe9, 0x04, //0x000047a5 shrl         $4, %ecx
	0x09, 0xd1, //0x000047a8 orl          %edx, %ecx
	0x89, 0xc8, //0x000047aa movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x000047ac shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x000047af andl         $65280, %eax
	0x89, 0xca, //0x000047b4 movl         %ecx, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x000047b6 andl         $128, %edx
	0x09, 0xc2, //0x000047bc orl          %eax, %edx
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x000047be je           LBB1_1097
	0xb9, 0xff, 0x00, 0x00, 0x00, //0x000047c4 movl         $255, %ecx
	//0x000047c9 LBB1_1097
	0x49, 0x83, 0xc0, 0x06, //0x000047c9 addq         $6, %r8
	0x4d, 0x89, 0xc5, //0x000047cd movq         %r8, %r13
	0x48, 0x8b, 0x5d, 0xd0, //0x000047d0 movq         $-48(%rbp), %rbx
	//0x000047d4 LBB1_1098
	0x80, 0xf9, 0x0a, //0x000047d4 cmpb         $10, %cl
	0x0f, 0x84, 0xde, 0xfe, 0xff, 0xff, //0x000047d7 je           LBB1_1101
	0x80, 0xf9, 0x0d, //0x000047dd cmpb         $13, %cl
	0x0f, 0x84, 0xd5, 0xfe, 0xff, 0xff, //0x000047e0 je           LBB1_1101
	0x80, 0xf9, 0x3d, //0x000047e6 cmpb         $61, %cl
	0x0f, 0x85, 0x88, 0x01, 0x00, 0x00, //0x000047e9 jne          LBB1_1111
	0x4d, 0x89, 0xe8, //0x000047ef movq         %r13, %r8
	0x41, 0x83, 0xfe, 0x04, //0x000047f2 cmpl         $4, %r14d
	0x48, 0x8b, 0x5d, 0xd0, //0x000047f6 movq         $-48(%rbp), %rbx
	0x0f, 0x84, 0x70, 0xf1, 0xff, 0xff, //0x000047fa je           LBB1_856
	0x4c, 0x39, 0xeb, //0x00004800 cmpq         %r13, %rbx
	0x0f, 0x87, 0x48, 0x00, 0x00, 0x00, //0x00004803 ja           LBB1_1114
	0x4d, 0x89, 0xe8, //0x00004809 movq         %r13, %r8
	0x48, 0x8b, 0x5d, 0xd0, //0x0000480c movq         $-48(%rbp), %rbx
	0xe9, 0xf6, 0xfd, 0xff, 0xff, //0x00004810 jmp          LBB1_1063
	//0x00004815 LBB1_1103
	0x48, 0x89, 0xca, //0x00004815 movq         %rcx, %rdx
	0x49, 0x89, 0xc8, //0x00004818 movq         %rcx, %r8
	0xe9, 0x50, 0xf1, 0xff, 0xff, //0x0000481b jmp          LBB1_856
	//0x00004820 LBB1_1105
	0x48, 0x8b, 0x5d, 0xd0, //0x00004820 movq         $-48(%rbp), %rbx
	0x49, 0x89, 0xd8, //0x00004824 movq         %rbx, %r8
	0xe9, 0xdf, 0xfd, 0xff, 0xff, //0x00004827 jmp          LBB1_1063
	//0x0000482c LBB1_1130
	0x4c, 0x89, 0xc1, //0x0000482c movq         %r8, %rcx
	0x49, 0x89, 0xcd, //0x0000482f movq         %rcx, %r13
	0x48, 0x3b, 0x4d, 0xd0, //0x00004832 cmpq         $-48(%rbp), %rcx
	0x0f, 0x82, 0x15, 0x00, 0x00, 0x00, //0x00004836 jb           LBB1_1114
	0xe9, 0x3e, 0x01, 0x00, 0x00, //0x0000483c jmp          LBB1_1133
	//0x00004841 LBB1_1112
	0x49, 0x89, 0xc8, //0x00004841 movq         %rcx, %r8
	0x49, 0x89, 0xcd, //0x00004844 movq         %rcx, %r13
	0x48, 0x3b, 0x4d, 0xd0, //0x00004847 cmpq         $-48(%rbp), %rcx
	0x0f, 0x83, 0x2e, 0x01, 0x00, 0x00, //0x0000484b jae          LBB1_1133
	//0x00004851 LBB1_1114
	0x4d, 0x8d, 0x45, 0x01, //0x00004851 leaq         $1(%r13), %r8
	0x41, 0x0f, 0xb6, 0x4d, 0x00, //0x00004855 movzbl       (%r13), %ecx
	0x80, 0xf9, 0x5c, //0x0000485a cmpb         $92, %cl
	0x0f, 0x85, 0xe2, 0x00, 0x00, 0x00, //0x0000485d jne          LBB1_1127
	0x49, 0x8d, 0x4d, 0x02, //0x00004863 leaq         $2(%r13), %rcx
	0x48, 0x3b, 0x4d, 0xd0, //0x00004867 cmpq         $-48(%rbp), %rcx
	0x0f, 0x87, 0x17, 0x01, 0x00, 0x00, //0x0000486b ja           LBB1_1135
	0x41, 0x0f, 0xb6, 0x00, //0x00004871 movzbl       (%r8), %eax
	0x3c, 0x6e, //0x00004875 cmpb         $110, %al
	0x0f, 0x84, 0xc4, 0xff, 0xff, 0xff, //0x00004877 je           LBB1_1112
	0x3c, 0x72, //0x0000487d cmpb         $114, %al
	0x0f, 0x84, 0xbc, 0xff, 0xff, 0xff, //0x0000487f je           LBB1_1112
	0x3c, 0x75, //0x00004885 cmpb         $117, %al
	0x0f, 0x85, 0x07, 0x01, 0x00, 0x00, //0x00004887 jne          LBB1_1136
	0x48, 0x8b, 0x45, 0xd0, //0x0000488d movq         $-48(%rbp), %rax
	0x48, 0x29, 0xc8, //0x00004891 subq         %rcx, %rax
	0x48, 0x83, 0xf8, 0x04, //0x00004894 cmpq         $4, %rax
	0x0f, 0x8c, 0xc1, 0x00, 0x00, 0x00, //0x00004898 jl           LBB1_1131
	0x8b, 0x11, //0x0000489e movl         (%rcx), %edx
	0x89, 0xd6, //0x000048a0 movl         %edx, %esi
	0xf7, 0xd6, //0x000048a2 notl         %esi
	0x8d, 0x82, 0xd0, 0xcf, 0xcf, 0xcf, //0x000048a4 leal         $-808464432(%rdx), %eax
	0x81, 0xe6, 0x80, 0x80, 0x80, 0x80, //0x000048aa andl         $-2139062144, %esi
	0x85, 0xc6, //0x000048b0 testl        %eax, %esi
	0x0f, 0x85, 0xa7, 0x00, 0x00, 0x00, //0x000048b2 jne          LBB1_1131
	0x8d, 0x82, 0x19, 0x19, 0x19, 0x19, //0x000048b8 leal         $421075225(%rdx), %eax
	0x09, 0xd0, //0x000048be orl          %edx, %eax
	0xa9, 0x80, 0x80, 0x80, 0x80, //0x000048c0 testl        $-2139062144, %eax
	0x0f, 0x85, 0x94, 0x00, 0x00, 0x00, //0x000048c5 jne          LBB1_1131
	0x89, 0xd0, //0x000048cb movl         %edx, %eax
	0x25, 0x7f, 0x7f, 0x7f, 0x7f, //0x000048cd andl         $2139062143, %eax
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x000048d2 movl         $-1061109568, %edi
	0x29, 0xc7, //0x000048d7 subl         %eax, %edi
	0x8d, 0x98, 0x46, 0x46, 0x46, 0x46, //0x000048d9 leal         $1179010630(%rax), %ebx
	0x21, 0xf7, //0x000048df andl         %esi, %edi
	0x85, 0xdf, //0x000048e1 testl        %ebx, %edi
	0x0f, 0x85, 0x76, 0x00, 0x00, 0x00, //0x000048e3 jne          LBB1_1131
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x000048e9 movl         $-522133280, %edi
	0x29, 0xc7, //0x000048ee subl         %eax, %edi
	0x05, 0x39, 0x39, 0x39, 0x39, //0x000048f0 addl         $960051513, %eax
	0x21, 0xfe, //0x000048f5 andl         %edi, %esi
	0x85, 0xc6, //0x000048f7 testl        %eax, %esi
	0x0f, 0x85, 0x60, 0x00, 0x00, 0x00, //0x000048f9 jne          LBB1_1131
	0x0f, 0xca, //0x000048ff bswapl       %edx
	0x89, 0xd0, //0x00004901 movl         %edx, %eax
	0xc1, 0xe8, 0x04, //0x00004903 shrl         $4, %eax
	0xf7, 0xd0, //0x00004906 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00004908 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x0000490d leal         (%rax,%rax,8), %eax
	0x81, 0xe2, 0x0f, 0x0f, 0x0f, 0x0f, //0x00004910 andl         $252645135, %edx
	0x01, 0xc2, //0x00004916 addl         %eax, %edx
	0x89, 0xd1, //0x00004918 movl         %edx, %ecx
	0xc1, 0xe9, 0x04, //0x0000491a shrl         $4, %ecx
	0x09, 0xd1, //0x0000491d orl          %edx, %ecx
	0x89, 0xc8, //0x0000491f movl         %ecx, %eax
	0xc1, 0xe8, 0x08, //0x00004921 shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x00004924 andl         $65280, %eax
	0x89, 0xca, //0x00004929 movl         %ecx, %edx
	0x81, 0xe2, 0x80, 0x00, 0x00, 0x00, //0x0000492b andl         $128, %edx
	0x09, 0xc2, //0x00004931 orl          %eax, %edx
	0x0f, 0x84, 0x05, 0x00, 0x00, 0x00, //0x00004933 je           LBB1_1126
	0xb9, 0xff, 0x00, 0x00, 0x00, //0x00004939 movl         $255, %ecx
	//0x0000493e LBB1_1126
	0x49, 0x83, 0xc5, 0x06, //0x0000493e addq         $6, %r13
	0x4d, 0x89, 0xe8, //0x00004942 movq         %r13, %r8
	//0x00004945 LBB1_1127
	0x80, 0xf9, 0x0a, //0x00004945 cmpb         $10, %cl
	0x0f, 0x84, 0xde, 0xfe, 0xff, 0xff, //0x00004948 je           LBB1_1130
	0x80, 0xf9, 0x0d, //0x0000494e cmpb         $13, %cl
	0x0f, 0x84, 0xd5, 0xfe, 0xff, 0xff, //0x00004951 je           LBB1_1130
	0x80, 0xf9, 0x3d, //0x00004957 cmpb         $61, %cl
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x0000495a jmp          LBB1_1132
	//0x0000495f LBB1_1131
	0x49, 0x89, 0xc8, //0x0000495f movq         %rcx, %r8
	//0x00004962 LBB1_1132
	0x48, 0x8b, 0x5d, 0xd0, //0x00004962 movq         $-48(%rbp), %rbx
	0xe9, 0x05, 0xf0, 0xff, 0xff, //0x00004966 jmp          LBB1_856
	//0x0000496b LBB1_1110
	0x48, 0x8b, 0x5d, 0xd0, //0x0000496b movq         $-48(%rbp), %rbx
	0x49, 0x89, 0xd0, //0x0000496f movq         %rdx, %r8
	0xe9, 0xf9, 0xef, 0xff, 0xff, //0x00004972 jmp          LBB1_856
	//0x00004977 LBB1_1111
	0x4d, 0x89, 0xe8, //0x00004977 movq         %r13, %r8
	0xe9, 0xe3, 0xff, 0xff, 0xff, //0x0000497a jmp          LBB1_1132
	//0x0000497f LBB1_1133
	0x48, 0x8b, 0x5d, 0xd0, //0x0000497f movq         $-48(%rbp), %rbx
	0xe9, 0x83, 0xfc, 0xff, 0xff, //0x00004983 jmp          LBB1_1063
	//0x00004988 LBB1_1135
	0x4d, 0x89, 0xc5, //0x00004988 movq         %r8, %r13
	0x48, 0x8b, 0x5d, 0xd0, //0x0000498b movq         $-48(%rbp), %rbx
	0xe9, 0xdc, 0xef, 0xff, 0xff, //0x0000498f jmp          LBB1_856
	//0x00004994 LBB1_1136
	0x48, 0x8b, 0x5d, 0xd0, //0x00004994 movq         $-48(%rbp), %rbx
	0x49, 0x89, 0xc8, //0x00004998 movq         %rcx, %r8
	0xe9, 0xd0, 0xef, 0xff, 0xff, //0x0000499b jmp          LBB1_856
	//0x000049a0 LBB1_1137
	0x48, 0x8b, 0x45, 0xa8, //0x000049a0 movq         $-88(%rbp), %rax
	0x4c, 0x29, 0xc8, //0x000049a4 subq         %r9, %rax
	0x48, 0x29, 0xc8, //0x000049a7 subq         %rcx, %rax
	0xe9, 0x19, 0x00, 0x00, 0x00, //0x000049aa jmp          LBB1_1139
	//0x000049af LBB1_1138
	0x48, 0x8b, 0x4d, 0xb0, //0x000049af movq         $-80(%rbp), %rcx
	0x48, 0x2b, 0x8d, 0x78, 0xff, 0xff, 0xff, //0x000049b3 subq         $-136(%rbp), %rcx
	0x48, 0x8b, 0x85, 0x70, 0xff, 0xff, 0xff, //0x000049ba movq         $-144(%rbp), %rax
	0x48, 0x01, 0x48, 0x08, //0x000049c1 addq         %rcx, $8(%rax)
	0x48, 0x89, 0xc8, //0x000049c5 movq         %rcx, %rax
	//0x000049c8 LBB1_1139
	0x48, 0x83, 0xc4, 0x68, //0x000049c8 addq         $104, %rsp
	0x5b, //0x000049cc popq         %rbx
	0x41, 0x5c, //0x000049cd popq         %r12
	0x41, 0x5d, //0x000049cf popq         %r13
	0x41, 0x5e, //0x000049d1 popq         %r14
	0x41, 0x5f, //0x000049d3 popq         %r15
	0x5d, //0x000049d5 popq         %rbp
	0xc5, 0xf8, 0x77, //0x000049d6 vzeroupper   
	0xc3, //0x000049d9 retq         
	//0x000049da LBB1_1140
	0x48, 0x8b, 0x45, 0xa8, //0x000049da movq         $-88(%rbp), %rax
	0x4c, 0x29, 0xf8, //0x000049de subq         %r15, %rax
	0x48, 0x29, 0xc8, //0x000049e1 subq         %rcx, %rax
	0xe9, 0xdf, 0xff, 0xff, 0xff, //0x000049e4 jmp          LBB1_1139
	//0x000049e9 LBB1_1141
	0x48, 0x8b, 0x45, 0xa8, //0x000049e9 movq         $-88(%rbp), %rax
	0x4c, 0x29, 0xe8, //0x000049ed subq         %r13, %rax
	0x48, 0x29, 0xc8, //0x000049f0 subq         %rcx, %rax
	0xe9, 0xd0, 0xff, 0xff, 0xff, //0x000049f3 jmp          LBB1_1139
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000049f8 .p2align 4, 0x00
	//0x00004a00 _TabEncodeCharsetStd
	0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f, 0x50, //0x00004a00 QUAD $0x4847464544434241; QUAD $0x504f4e4d4c4b4a49  // .ascii 16, 'ABCDEFGHIJKLMNOP'
	0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5a, 0x61, 0x62, 0x63, 0x64, 0x65, 0x66, //0x00004a10 QUAD $0x5857565554535251; QUAD $0x6665646362615a59  // .ascii 16, 'QRSTUVWXYZabcdef'
	0x67, 0x68, 0x69, 0x6a, 0x6b, 0x6c, 0x6d, 0x6e, 0x6f, 0x70, 0x71, 0x72, 0x73, 0x74, 0x75, 0x76, //0x00004a20 QUAD $0x6e6d6c6b6a696867; QUAD $0x767574737271706f  // .ascii 16, 'ghijklmnopqrstuv'
	0x77, 0x78, 0x79, 0x7a, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x2b, 0x2f, //0x00004a30 QUAD $0x333231307a797877; QUAD $0x2f2b393837363534  // .ascii 16, 'wxyz0123456789+/'
	//0x00004a40 .p2align 4, 0x00
	//0x00004a40 _VecEncodeCharsetStd
	0x47, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xed, 0xf0, 0x41, 0x00, 0x00, //0x00004a40 QUAD $0xfcfcfcfcfcfcfc47; QUAD $0x000041f0edfcfcfc  // .asciz 16, 'G\xfc\xfc\xfc\xfc\xfc\xfc\xfc\xfc\xfc\xfc\xed\xf0A\x00\x00'
	0x47, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xed, 0xf0, 0x41, 0x00, 0x00, //0x00004a50 QUAD $0xfcfcfcfcfcfcfc47; QUAD $0x000041f0edfcfcfc  // .asciz 16, 'G\xfc\xfc\xfc\xfc\xfc\xfc\xfc\xfc\xfc\xfc\xed\xf0A\x00\x00'
	//0x00004a60 .p2align 4, 0x00
	//0x00004a60 _TabEncodeCharsetURL
	0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f, 0x50, //0x00004a60 QUAD $0x4847464544434241; QUAD $0x504f4e4d4c4b4a49  // .ascii 16, 'ABCDEFGHIJKLMNOP'
	0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5a, 0x61, 0x62, 0x63, 0x64, 0x65, 0x66, //0x00004a70 QUAD $0x5857565554535251; QUAD $0x6665646362615a59  // .ascii 16, 'QRSTUVWXYZabcdef'
	0x67, 0x68, 0x69, 0x6a, 0x6b, 0x6c, 0x6d, 0x6e, 0x6f, 0x70, 0x71, 0x72, 0x73, 0x74, 0x75, 0x76, //0x00004a80 QUAD $0x6e6d6c6b6a696867; QUAD $0x767574737271706f  // .ascii 16, 'ghijklmnopqrstuv'
	0x77, 0x78, 0x79, 0x7a, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x2d, 0x5f, //0x00004a90 QUAD $0x333231307a797877; QUAD $0x5f2d393837363534  // .ascii 16, 'wxyz0123456789-_'
	//0x00004aa0 .p2align 4, 0x00
	//0x00004aa0 _VecEncodeCharsetURL
	0x47, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xef, 0x20, 0x41, 0x00, 0x00, //0x00004aa0 QUAD $0xfcfcfcfcfcfcfc47; QUAD $0x00004120effcfcfc  // .asciz 16, 'G\xfc\xfc\xfc\xfc\xfc\xfc\xfc\xfc\xfc\xfc\xef A\x00\x00'
	0x47, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xef, 0x20, 0x41, 0x00, 0x00, //0x00004ab0 QUAD $0xfcfcfcfcfcfcfc47; QUAD $0x00004120effcfcfc  // .asciz 16, 'G\xfc\xfc\xfc\xfc\xfc\xfc\xfc\xfc\xfc\xfc\xef A\x00\x00'
	//0x00004ac0 .p2align 4, 0x00
	//0x00004ac0 _VecDecodeTableStd
	0x00, 0x00, 0x13, 0x04, 0xbf, 0xbf, 0xb9, 0xb9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004ac0 QUAD $0xb9b9bfbf04130000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x13\x04\xbf\xbf\xb9\xb9\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x13, 0x04, 0xbf, 0xbf, 0xb9, 0xb9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004ad0 QUAD $0xb9b9bfbf04130000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x13\x04\xbf\xbf\xb9\xb9\x00\x00\x00\x00\x00\x00\x00\x00'
	0xa8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf0, 0x54, 0x50, 0x50, 0x50, 0x54, //0x00004ae0 QUAD $0xf8f8f8f8f8f8f8a8; QUAD $0x5450505054f0f8f8  // .ascii 16, '\xa8\xf8\xf8\xf8\xf8\xf8\xf8\xf8\xf8\xf8\xf0TPPPT'
	0xa8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf0, 0x54, 0x50, 0x50, 0x50, 0x54, //0x00004af0 QUAD $0xf8f8f8f8f8f8f8a8; QUAD $0x5450505054f0f8f8  // .ascii 16, '\xa8\xf8\xf8\xf8\xf8\xf8\xf8\xf8\xf8\xf8\xf0TPPPT'
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00004b00 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .ascii 16, '////////////////'
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00004b10 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .ascii 16, '////////////////'
	0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, //0x00004b20 QUAD $0x1010101010101010; QUAD $0x1010101010101010  // .ascii 16, '\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10'
	0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, //0x00004b30 QUAD $0x1010101010101010; QUAD $0x1010101010101010  // .ascii 16, '\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10\x10'
	//0x00004b40 .p2align 4, 0x00
	//0x00004b40 _VecDecodeCharsetStd
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00004b40 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00004b50 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3e, 0xff, 0xff, 0xff, 0x3f, //0x00004b60 QUAD $0xffffffffffffffff; QUAD $0x3fffffff3effffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff>\xff\xff\xff?'
	0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00004b70 QUAD $0x3b3a393837363534; QUAD $0xffffffffffff3d3c  // .ascii 16, '456789:;<=\xff\xff\xff\xff\xff\xff'
	0xff, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, //0x00004b80 QUAD $0x06050403020100ff; QUAD $0x0e0d0c0b0a090807  // .ascii 16, '\xff\x00\x01\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e'
	0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00004b90 QUAD $0x161514131211100f; QUAD $0xffffffffff191817  // .ascii 16, '\x0f\x10\x11\x12\x13\x14\x15\x16\x17\x18\x19\xff\xff\xff\xff\xff'
	0xff, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, //0x00004ba0 QUAD $0x201f1e1d1c1b1aff; QUAD $0x2827262524232221  // .ascii 16, '\xff\x1a\x1b\x1c\x1d\x1e\x1f !"#$%&\'('
	0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00004bb0 QUAD $0x302f2e2d2c2b2a29; QUAD $0xffffffffff333231  // .ascii 16, ')*+,-./0123\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00004bc0 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00004bd0 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00004be0 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00004bf0 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00004c00 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00004c10 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00004c20 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00004c30 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	//0x00004c40 .p2align 4, 0x00
	//0x00004c40 _VecDecodeTableURL
	0x00, 0x00, 0x11, 0x04, 0xbf, 0xbf, 0xb9, 0xb9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004c40 QUAD $0xb9b9bfbf04110000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x11\x04\xbf\xbf\xb9\xb9\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x11, 0x04, 0xbf, 0xbf, 0xb9, 0xb9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004c50 QUAD $0xb9b9bfbf04110000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x11\x04\xbf\xbf\xb9\xb9\x00\x00\x00\x00\x00\x00\x00\x00'
	0xa8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf0, 0x50, 0x50, 0x54, 0x50, 0x70, //0x00004c60 QUAD $0xf8f8f8f8f8f8f8a8; QUAD $0x7050545050f0f8f8  // .ascii 16, '\xa8\xf8\xf8\xf8\xf8\xf8\xf8\xf8\xf8\xf8\xf0PPTPp'
	0xa8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf0, 0x50, 0x50, 0x54, 0x50, 0x70, //0x00004c70 QUAD $0xf8f8f8f8f8f8f8a8; QUAD $0x7050545050f0f8f8  // .ascii 16, '\xa8\xf8\xf8\xf8\xf8\xf8\xf8\xf8\xf8\xf8\xf0PPTPp'
	0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, //0x00004c80 QUAD $0x5f5f5f5f5f5f5f5f; QUAD $0x5f5f5f5f5f5f5f5f  // .ascii 16, '________________'
	0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, 0x5f, //0x00004c90 QUAD $0x5f5f5f5f5f5f5f5f; QUAD $0x5f5f5f5f5f5f5f5f  // .ascii 16, '________________'
	0xe0, 0xe0, 0xe0, 0xe0, 0xe0, 0xe0, 0xe0, 0xe0, 0xe0, 0xe0, 0xe0, 0xe0, 0xe0, 0xe0, 0xe0, 0xe0, //0x00004ca0 QUAD $0xe0e0e0e0e0e0e0e0; QUAD $0xe0e0e0e0e0e0e0e0  // .ascii 16, '\xe0\xe0\xe0\xe0\xe0\xe0\xe0\xe0\xe0\xe0\xe0\xe0\xe0\xe0\xe0\xe0'
	0xe0, 0xe0, 0xe0, 0xe0, 0xe0, 0xe0, 0xe0, 0xe0, 0xe0, 0xe0, 0xe0, 0xe0, 0xe0, 0xe0, 0xe0, 0xe0, //0x00004cb0 QUAD $0xe0e0e0e0e0e0e0e0; QUAD $0xe0e0e0e0e0e0e0e0  // .ascii 16, '\xe0\xe0\xe0\xe0\xe0\xe0\xe0\xe0\xe0\xe0\xe0\xe0\xe0\xe0\xe0\xe0'
	//0x00004cc0 .p2align 4, 0x00
	//0x00004cc0 _VecDecodeCharsetURL
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00004cc0 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00004cd0 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3e, 0xff, 0xff, //0x00004ce0 QUAD $0xffffffffffffffff; QUAD $0xffff3effffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff>\xff\xff'
	0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00004cf0 QUAD $0x3b3a393837363534; QUAD $0xffffffffffff3d3c  // .ascii 16, '456789:;<=\xff\xff\xff\xff\xff\xff'
	0xff, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, //0x00004d00 QUAD $0x06050403020100ff; QUAD $0x0e0d0c0b0a090807  // .ascii 16, '\xff\x00\x01\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e'
	0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0xff, 0xff, 0xff, 0xff, 0x3f, //0x00004d10 QUAD $0x161514131211100f; QUAD $0x3fffffffff191817  // .ascii 16, '\x0f\x10\x11\x12\x13\x14\x15\x16\x17\x18\x19\xff\xff\xff\xff?'
	0xff, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, //0x00004d20 QUAD $0x201f1e1d1c1b1aff; QUAD $0x2827262524232221  // .ascii 16, '\xff\x1a\x1b\x1c\x1d\x1e\x1f !"#$%&\'('
	0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00004d30 QUAD $0x302f2e2d2c2b2a29; QUAD $0xffffffffff333231  // .ascii 16, ')*+,-./0123\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00004d40 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00004d50 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00004d60 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00004d70 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00004d80 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00004d90 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00004da0 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00004db0 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .ascii 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
}
