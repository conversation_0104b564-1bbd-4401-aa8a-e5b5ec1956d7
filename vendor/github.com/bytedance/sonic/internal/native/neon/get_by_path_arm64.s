// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

#include "go_asm.h"
#include "funcdata.h"
#include "textflag.h"

TEXT ·__get_by_path_entry__(SB), NOSPLIT, $192
	NO_LOCAL_POINTERS
	WORD $0x100000a0 // adr x0, .+20
	MOVD R0, ret(FP)
	RET
	  // .p2align 4, 0x00
lCPI0_0:
	WORD $0x08040201
	WORD $0x80402010
	WORD $0x08040201
	WORD $0x80402010
	// // .byte 1
// .byte 2
// .byte 4
// .byte 8
// .byte 16
// .byte 32
// .byte 64
// .byte 128
// .byte 1
// .byte 2
// .byte 4
// .byte 8
// .byte 16
// .byte 32
// .byte 64
// .byte 128

lCPI0_1:
	WORD $0x09010800
	WORD $0x0b030a02
	WORD $0x0d050c04
	WORD $0x0f070e06
	// // .byte 0
// .byte 8
// .byte 1
// .byte 9
// .byte 2
// .byte 10
// .byte 3
// .byte 11
// .byte 4
// .byte 12
// .byte 5
// .byte 13
// .byte 6
// .byte 14
// .byte 7
// .byte 15

lCPI0_2:
	WORD $0x00000001; WORD $0x00000000  // .quad 1
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	  // .p2align 2, 0x00
_get_by_path:
	WORD $0xd10343ff  // sub	sp, sp, #208
	WORD $0xa906effc  // stp	x28, x27, [sp, #104]
	WORD $0xa907e7fa  // stp	x26, x25, [sp, #120]
	WORD $0xa908dff8  // stp	x24, x23, [sp, #136]
	WORD $0xa909d7f6  // stp	x22, x21, [sp, #152]
	WORD $0xa90acff4  // stp	x20, x19, [sp, #168]
	WORD $0xa90bfbfd  // stp	fp, lr, [sp, #184]
	WORD $0xa93ffbfd  // stp	fp, lr, [sp, #-8]
	WORD $0xd10023fd  // sub	fp, sp, #8
	WORD $0xf9400448  // ldr	x8, [x2, #8]
	WORD $0xb40144a8  // cbz	x8, LBB0_492 $10388(%rip)
	WORD $0xf9400049  // ldr	x9, [x2]
	WORD $0x8b08112a  // add	x10, x9, x8, lsl #4
	WORD $0x910083e8  // add	x8, sp, #32
	WORD $0x9100810b  // add	x11, x8, #32
	WORD $0xf9400036  // ldr	x22, [x1]
	WORD $0x5280002c  // mov	w12, #1
	WORD $0xd284c00d  // mov	x13, #9728
	WORD $0xf2c0002d  // movk	x13, #1, lsl #32
	WORD $0x3200f3fe  // mov	w30, #1431655765
	WORD $0x3201f3e5  // mov	w5, #-1431655766
	WORD $0x4f01e440  // movi.16b	v0, #34
Lloh0:
	WORD $0x10fffbc8  // adr	x8, lCPI0_0 $-136(%rip)
Lloh1:
	WORD $0x3dc00101  // ldr	q1, [x8, lCPI0_0@PAGEOFF] $0(%rip)
Lloh2:
	WORD $0x10fffc08  // adr	x8, lCPI0_1 $-128(%rip)
Lloh3:
	WORD $0x3dc00102  // ldr	q2, [x8, lCPI0_1@PAGEOFF] $0(%rip)
	WORD $0x4f02e783  // movi.16b	v3, #92
	WORD $0xb201e3fc  // mov	x28, #-8608480567731124088
	WORD $0xf2e1111c  // movk	x28, #2184, lsl #48
	WORD $0x4f01e584  // movi.16b	v4, #44
	WORD $0xb202e3e7  // mov	x7, #4919131752989213764
	WORD $0xf2e08887  // movk	x7, #1092, lsl #48
	WORD $0xb200e3e4  // mov	x4, #1229782938247303441
	WORD $0x4f06e7e5  // movi.16b	v5, #223
	WORD $0x4f02e7a6  // movi.16b	v6, #93
	WORD $0x529fffe8  // mov	w8, #65535
	WORD $0x4f02e767  // movi.16b	v7, #91
	WORD $0x6f00e410  // movi.2d	v16, #0000000000000000
	WORD $0x4f03e771  // movi.16b	v17, #123
	WORD $0x4f03e7b2  // movi.16b	v18, #125
Lloh4:
	WORD $0x100262b0  // adr	x16, __UnquoteTab $19540(%rip)
Lloh5:
	WORD $0x91000210  // add	x16, x16, __UnquoteTab@PAGEOFF $0(%rip)
LBB0_2:
	WORD $0xa9405013  // ldp	x19, x20, [x0]
	WORD $0xeb1402df  // cmp	x22, x20
	WORD $0x54000162  // b.hs	LBB0_7 $44(%rip)
	WORD $0x38766a62  // ldrb	w2, [x19, x22]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x54000100  // b.eq	LBB0_7 $32(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x540000c0  // b.eq	LBB0_7 $24(%rip)
	WORD $0x51002c42  // sub	w2, w2, #11
	WORD $0x3100085f  // cmn	w2, #2
	WORD $0x54000062  // b.hs	LBB0_7 $12(%rip)
	WORD $0xaa1603f7  // mov	x23, x22
	WORD $0x1400003a  // b	LBB0_25 $232(%rip)
LBB0_7:
	WORD $0x910006d7  // add	x23, x22, #1
	WORD $0xeb1402ff  // cmp	x23, x20
	WORD $0x54000122  // b.hs	LBB0_11 $36(%rip)
	WORD $0x38776a62  // ldrb	w2, [x19, x23]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x540000c0  // b.eq	LBB0_11 $24(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x54000080  // b.eq	LBB0_11 $16(%rip)
	WORD $0x51002c42  // sub	w2, w2, #11
	WORD $0x3100085f  // cmn	w2, #2
	WORD $0x540005e3  // b.lo	LBB0_25 $188(%rip)
LBB0_11:
	WORD $0x91000ad7  // add	x23, x22, #2
	WORD $0xeb1402ff  // cmp	x23, x20
	WORD $0x54000122  // b.hs	LBB0_15 $36(%rip)
	WORD $0x38776a62  // ldrb	w2, [x19, x23]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x540000c0  // b.eq	LBB0_15 $24(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x54000080  // b.eq	LBB0_15 $16(%rip)
	WORD $0x51002c42  // sub	w2, w2, #11
	WORD $0x3100085f  // cmn	w2, #2
	WORD $0x54000483  // b.lo	LBB0_25 $144(%rip)
LBB0_15:
	WORD $0x91000ed7  // add	x23, x22, #3
	WORD $0xeb1402ff  // cmp	x23, x20
	WORD $0x54000122  // b.hs	LBB0_19 $36(%rip)
	WORD $0x38776a62  // ldrb	w2, [x19, x23]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x540000c0  // b.eq	LBB0_19 $24(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x54000080  // b.eq	LBB0_19 $16(%rip)
	WORD $0x51002c42  // sub	w2, w2, #11
	WORD $0x3100085f  // cmn	w2, #2
	WORD $0x54000323  // b.lo	LBB0_25 $100(%rip)
LBB0_19:
	WORD $0x910012d7  // add	x23, x22, #4
	WORD $0xeb1402ff  // cmp	x23, x20
	WORD $0x540001e2  // b.hs	LBB0_23 $60(%rip)
LBB0_20:
	WORD $0x38776a62  // ldrb	w2, [x19, x23]
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x9ac22182  // lsl	x2, x12, x2
	WORD $0x8a0d0042  // and	x2, x2, x13
	WORD $0xfa409844  // ccmp	x2, #0, #4, ls
	WORD $0x540001c0  // b.eq	LBB0_24 $56(%rip)
	WORD $0x910006f7  // add	x23, x23, #1
	WORD $0xeb17029f  // cmp	x20, x23
	WORD $0x54ffff01  // b.ne	LBB0_20 $-32(%rip)
LBB0_22:
	WORD $0x52800014  // mov	w20, #0
	WORD $0xaa1603f7  // mov	x23, x22
	WORD $0xf9400122  // ldr	x2, [x9]
	WORD $0xb50001e2  // cbnz	x2, LBB0_26 $60(%rip)
	WORD $0x14000f5d  // b	LBB0_801 $15732(%rip)
LBB0_23:
	WORD $0x52800014  // mov	w20, #0
	WORD $0xf9000037  // str	x23, [x1]
	WORD $0xf9400122  // ldr	x2, [x9]
	WORD $0xb5000142  // cbnz	x2, LBB0_26 $40(%rip)
	WORD $0x14000f58  // b	LBB0_801 $15712(%rip)
LBB0_24:
	WORD $0xeb1402ff  // cmp	x23, x20
	WORD $0x54fffea2  // b.hs	LBB0_22 $-44(%rip)
LBB0_25:
	WORD $0x910006e2  // add	x2, x23, #1
	WORD $0xf9000022  // str	x2, [x1]
	WORD $0x38776a74  // ldrb	w20, [x19, x23]
	WORD $0xaa0203f7  // mov	x23, x2
	WORD $0xf9400122  // ldr	x2, [x9]
	WORD $0xb401ea02  // cbz	x2, LBB0_801 $15680(%rip)
LBB0_26:
	WORD $0x39405c42  // ldrb	w2, [x2, #23]
	WORD $0x12001042  // and	w2, w2, #0x1f
	WORD $0x7100085f  // cmp	w2, #2
	WORD $0x5400bbc0  // b.eq	LBB0_316 $6008(%rip)
	WORD $0x7100605f  // cmp	w2, #24
	WORD $0x5401e941  // b.ne	LBB0_801 $15656(%rip)
	WORD $0x7101ee9f  // cmp	w20, #123
	WORD $0x5401f701  // b.ne	LBB0_829 $16096(%rip)
LBB0_29:
	WORD $0xf9400414  // ldr	x20, [x0, #8]
	WORD $0xeb1402ff  // cmp	x23, x20
	WORD $0x54000162  // b.hs	LBB0_34 $44(%rip)
	WORD $0x38776a62  // ldrb	w2, [x19, x23]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x54000100  // b.eq	LBB0_34 $32(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x540000c0  // b.eq	LBB0_34 $24(%rip)
	WORD $0x51002c42  // sub	w2, w2, #11
	WORD $0x3100085f  // cmn	w2, #2
	WORD $0x54000062  // b.hs	LBB0_34 $12(%rip)
	WORD $0xaa1703f5  // mov	x21, x23
	WORD $0x14000031  // b	LBB0_50 $196(%rip)
LBB0_34:
	WORD $0x910006f5  // add	x21, x23, #1
	WORD $0xeb1402bf  // cmp	x21, x20
	WORD $0x54000122  // b.hs	LBB0_38 $36(%rip)
	WORD $0x38756a62  // ldrb	w2, [x19, x21]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x540000c0  // b.eq	LBB0_38 $24(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x54000080  // b.eq	LBB0_38 $16(%rip)
	WORD $0x51002c42  // sub	w2, w2, #11
	WORD $0x3100085f  // cmn	w2, #2
	WORD $0x540004c3  // b.lo	LBB0_50 $152(%rip)
LBB0_38:
	WORD $0x91000af5  // add	x21, x23, #2
	WORD $0xeb1402bf  // cmp	x21, x20
	WORD $0x54000122  // b.hs	LBB0_42 $36(%rip)
	WORD $0x38756a62  // ldrb	w2, [x19, x21]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x540000c0  // b.eq	LBB0_42 $24(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x54000080  // b.eq	LBB0_42 $16(%rip)
	WORD $0x51002c42  // sub	w2, w2, #11
	WORD $0x3100085f  // cmn	w2, #2
	WORD $0x54000363  // b.lo	LBB0_50 $108(%rip)
LBB0_42:
	WORD $0x91000ef5  // add	x21, x23, #3
	WORD $0xeb1402bf  // cmp	x21, x20
	WORD $0x54000122  // b.hs	LBB0_46 $36(%rip)
	WORD $0x38756a62  // ldrb	w2, [x19, x21]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x540000c0  // b.eq	LBB0_46 $24(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x54000080  // b.eq	LBB0_46 $16(%rip)
	WORD $0x51002c42  // sub	w2, w2, #11
	WORD $0x3100085f  // cmn	w2, #2
	WORD $0x54000203  // b.lo	LBB0_50 $64(%rip)
LBB0_46:
	WORD $0x910012f5  // add	x21, x23, #4
	WORD $0xeb1402bf  // cmp	x21, x20
	WORD $0x5401e382  // b.hs	LBB0_802 $15472(%rip)
LBB0_47:
	WORD $0x38756a62  // ldrb	w2, [x19, x21]
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x9ac22182  // lsl	x2, x12, x2
	WORD $0x8a0d0042  // and	x2, x2, x13
	WORD $0xfa409844  // ccmp	x2, #0, #4, ls
	WORD $0x540000a0  // b.eq	LBB0_49 $20(%rip)
	WORD $0x910006b5  // add	x21, x21, #1
	WORD $0xeb15029f  // cmp	x20, x21
	WORD $0x54ffff01  // b.ne	LBB0_47 $-32(%rip)
	WORD $0x14000f7d  // b	LBB0_829 $15860(%rip)
LBB0_49:
	WORD $0xeb1402bf  // cmp	x21, x20
	WORD $0x5401ef62  // b.hs	LBB0_829 $15852(%rip)
LBB0_50:
	WORD $0x910006b9  // add	x25, x21, #1
	WORD $0xf9000039  // str	x25, [x1]
	WORD $0x38756a62  // ldrb	w2, [x19, x21]
	WORD $0x7100885f  // cmp	w2, #34
	WORD $0x5401ee81  // b.ne	LBB0_828 $15824(%rip)
	WORD $0xf940041c  // ldr	x28, [x0, #8]
	WORD $0xeb19039b  // subs	x27, x28, x25
	WORD $0x54023e20  // b.eq	LBB0_910 $18372(%rip)
	WORD $0x3200f3e5  // mov	w5, #1431655765
	WORD $0xf9400522  // ldr	x2, [x9, #8]
	WORD $0xa9406056  // ldp	x22, x24, [x2]
	WORD $0x8b190274  // add	x20, x19, x25
	WORD $0xf101037f  // cmp	x27, #64
	WORD $0x54005883  // b.lo	LBB0_203 $2832(%rip)
	WORD $0xd2800017  // mov	x23, #0
	WORD $0x9280001a  // mov	x26, #-1
LBB0_54:
	WORD $0x8b190262  // add	x2, x19, x25
	WORD $0xad405053  // ldp	q19, q20, [x2]
	WORD $0xad415855  // ldp	q21, q22, [x2, #32]
	WORD $0x6e208e77  // cmeq.16b	v23, v19, v0
	WORD $0x6e208e98  // cmeq.16b	v24, v20, v0
	WORD $0x6e208eb9  // cmeq.16b	v25, v21, v0
	WORD $0x6e208eda  // cmeq.16b	v26, v22, v0
	WORD $0x6e238e73  // cmeq.16b	v19, v19, v3
	WORD $0x6e238e94  // cmeq.16b	v20, v20, v3
	WORD $0x6e238eb5  // cmeq.16b	v21, v21, v3
	WORD $0x6e238ed6  // cmeq.16b	v22, v22, v3
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602e2  // fmov	w2, s23
	WORD $0x4e211f17  // and.16b	v23, v24, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602fe  // fmov	w30, s23
	WORD $0x4e211f37  // and.16b	v23, v25, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602e7  // fmov	w7, s23
	WORD $0x4e211f57  // and.16b	v23, v26, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f1  // fmov	w17, s23
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260266  // fmov	w6, s19
	WORD $0x4e211e93  // and.16b	v19, v20, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e26026e  // fmov	w14, s19
	WORD $0x4e211eb3  // and.16b	v19, v21, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e26026f  // fmov	w15, s19
	WORD $0x4e211ed3  // and.16b	v19, v22, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260270  // fmov	w16, s19
	WORD $0xd3607ce7  // lsl	x7, x7, #32
	WORD $0xaa11c0f1  // orr	x17, x7, x17, lsl #48
	WORD $0x53103fc7  // lsl	w7, w30, #16
	WORD $0xaa070231  // orr	x17, x17, x7
	WORD $0xaa02023e  // orr	lr, x17, x2
	WORD $0xd3607def  // lsl	x15, x15, #32
	WORD $0xaa10c1ef  // orr	x15, x15, x16, lsl #48
	WORD $0x53103dce  // lsl	w14, w14, #16
	WORD $0xaa0e01ee  // orr	x14, x15, x14
	WORD $0xaa0601c2  // orr	x2, x14, x6
	WORD $0xb5000102  // cbnz	x2, LBB0_58 $32(%rip)
	WORD $0xb5000197  // cbnz	x23, LBB0_59 $48(%rip)
	WORD $0xb50002de  // cbnz	lr, LBB0_60 $88(%rip)
LBB0_57:
	WORD $0xd101037b  // sub	x27, x27, #64
	WORD $0x91010339  // add	x25, x25, #64
	WORD $0xf100ff7f  // cmp	x27, #63
	WORD $0x54fff8a8  // b.hi	LBB0_54 $-236(%rip)
	WORD $0x1400027a  // b	LBB0_199 $2536(%rip)
LBB0_58:
	WORD $0xb100075f  // cmn	x26, #1
	WORD $0xdac0004e  // rbit	x14, x2
	WORD $0xdac011ce  // clz	x14, x14
	WORD $0x8b1901ce  // add	x14, x14, x25
	WORD $0x9a8e135a  // csel	x26, x26, x14, ne
LBB0_59:
	WORD $0x8a37004e  // bic	x14, x2, x23
	WORD $0xaa0e06ef  // orr	x15, x23, x14, lsl #1
	WORD $0x8a2f0050  // bic	x16, x2, x15
	WORD $0x9201f210  // and	x16, x16, #0xaaaaaaaaaaaaaaaa
	WORD $0xab0e020e  // adds	x14, x16, x14
	WORD $0x1a9f37f7  // cset	w23, hs
	WORD $0xd37ff9ce  // lsl	x14, x14, #1
	WORD $0xd200f1ce  // eor	x14, x14, #0x5555555555555555
	WORD $0x8a0f01ce  // and	x14, x14, x15
	WORD $0x8a2e03de  // bic	lr, lr, x14
	WORD $0xb4fffd9e  // cbz	lr, LBB0_57 $-80(%rip)
LBB0_60:
	WORD $0xdac003ce  // rbit	x14, lr
	WORD $0xdac011ce  // clz	x14, x14
	WORD $0x8b1901ce  // add	x14, x14, x25
	WORD $0x910005d7  // add	x23, x14, #1
	WORD $0x3200f3fe  // mov	w30, #1431655765
	WORD $0x3201f3e5  // mov	w5, #-1431655766
	WORD $0xb202e3e7  // mov	x7, #4919131752989213764
	WORD $0xf2e08887  // movk	x7, #1092, lsl #48
LBB0_61:
	WORD $0xb7fa3297  // tbnz	x23, #63, LBB0_911 $18000(%rip)
	WORD $0xf9000037  // str	x23, [x1]
	WORD $0xb100075f  // cmn	x26, #1
	WORD $0x54000060  // b.eq	LBB0_64 $12(%rip)
	WORD $0xeb17035f  // cmp	x26, x23
	WORD $0x54007c0d  // b.le	LBB0_247 $3968(%rip)
LBB0_64:
	WORD $0xcb1502ee  // sub	x14, x23, x21
	WORD $0xd10009c2  // sub	x2, x14, #2
	WORD $0xaa18004e  // orr	x14, x2, x24
	WORD $0xb201e3fc  // mov	x28, #-8608480567731124088
	WORD $0xf2e1111c  // movk	x28, #2184, lsl #48
	WORD $0xb40002ee  // cbz	x14, LBB0_70 $92(%rip)
	WORD $0xeb18005f  // cmp	x2, x24
	WORD $0x540002e1  // b.ne	LBB0_71 $92(%rip)
	WORD $0xd280001a  // mov	x26, #0
	WORD $0xaa1803f9  // mov	x25, x24
LBB0_67:
	WORD $0xf1004322  // subs	x2, x25, #16
	WORD $0x54001283  // b.lo	LBB0_116 $592(%rip)
	WORD $0x3cfa6a93  // ldr	q19, [x20, x26]
	WORD $0x3cfa6ad4  // ldr	q20, [x22, x26]
	WORD $0x6e338e93  // cmeq.16b	v19, v20, v19
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e26026e  // fmov	w14, s19
	WORD $0x0a2e010e  // bic	w14, w8, w14
	WORD $0x9100435a  // add	x26, x26, #16
	WORD $0xaa0203f9  // mov	x25, x2
	WORD $0x34fffe8e  // cbz	w14, LBB0_67 $-48(%rip)
	WORD $0x52800016  // mov	w22, #0
Lloh6:
	WORD $0x10023ff0  // adr	x16, __UnquoteTab $18428(%rip)
Lloh7:
	WORD $0x91000210  // add	x16, x16, __UnquoteTab@PAGEOFF $0(%rip)
	WORD $0x2a1f03f4  // mov	w20, wzr
	WORD $0x14000006  // b	LBB0_73 $24(%rip)
LBB0_70:
	WORD $0x52800034  // mov	w20, #1
	WORD $0x14000002  // b	LBB0_72 $8(%rip)
LBB0_71:
	WORD $0xd2800014  // mov	x20, #0
LBB0_72:
Lloh8:
	WORD $0x10023f10  // adr	x16, __UnquoteTab $18400(%rip)
Lloh9:
	WORD $0x91000210  // add	x16, x16, __UnquoteTab@PAGEOFF $0(%rip)
LBB0_73:
	WORD $0xf9400416  // ldr	x22, [x0, #8]
	WORD $0xeb1602ff  // cmp	x23, x22
	WORD $0x54000162  // b.hs	LBB0_78 $44(%rip)
	WORD $0x38776a62  // ldrb	w2, [x19, x23]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x54000100  // b.eq	LBB0_78 $32(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x540000c0  // b.eq	LBB0_78 $24(%rip)
	WORD $0x51002c4e  // sub	w14, w2, #11
	WORD $0x310009df  // cmn	w14, #2
	WORD $0x54000062  // b.hs	LBB0_78 $12(%rip)
	WORD $0xaa1703f5  // mov	x21, x23
	WORD $0x14000031  // b	LBB0_94 $196(%rip)
LBB0_78:
	WORD $0x910006f5  // add	x21, x23, #1
	WORD $0xeb1602bf  // cmp	x21, x22
	WORD $0x54000122  // b.hs	LBB0_82 $36(%rip)
	WORD $0x38756a62  // ldrb	w2, [x19, x21]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x540000c0  // b.eq	LBB0_82 $24(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x54000080  // b.eq	LBB0_82 $16(%rip)
	WORD $0x51002c4e  // sub	w14, w2, #11
	WORD $0x310009df  // cmn	w14, #2
	WORD $0x540004c3  // b.lo	LBB0_94 $152(%rip)
LBB0_82:
	WORD $0x91000af5  // add	x21, x23, #2
	WORD $0xeb1602bf  // cmp	x21, x22
	WORD $0x54000122  // b.hs	LBB0_86 $36(%rip)
	WORD $0x38756a62  // ldrb	w2, [x19, x21]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x540000c0  // b.eq	LBB0_86 $24(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x54000080  // b.eq	LBB0_86 $16(%rip)
	WORD $0x51002c4e  // sub	w14, w2, #11
	WORD $0x310009df  // cmn	w14, #2
	WORD $0x54000363  // b.lo	LBB0_94 $108(%rip)
LBB0_86:
	WORD $0x91000ef5  // add	x21, x23, #3
	WORD $0xeb1602bf  // cmp	x21, x22
	WORD $0x54000122  // b.hs	LBB0_90 $36(%rip)
	WORD $0x38756a62  // ldrb	w2, [x19, x21]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x540000c0  // b.eq	LBB0_90 $24(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x54000080  // b.eq	LBB0_90 $16(%rip)
	WORD $0x51002c4e  // sub	w14, w2, #11
	WORD $0x310009df  // cmn	w14, #2
	WORD $0x54000203  // b.lo	LBB0_94 $64(%rip)
LBB0_90:
	WORD $0x910012f5  // add	x21, x23, #4
	WORD $0xeb1602bf  // cmp	x21, x22
	WORD $0x5401ca62  // b.hs	LBB0_802 $14668(%rip)
LBB0_91:
	WORD $0x38756a6e  // ldrb	w14, [x19, x21]
	WORD $0x710081df  // cmp	w14, #32
	WORD $0x9ace218e  // lsl	x14, x12, x14
	WORD $0x8a0d01ce  // and	x14, x14, x13
	WORD $0xfa4099c4  // ccmp	x14, #0, #4, ls
	WORD $0x540000a0  // b.eq	LBB0_93 $20(%rip)
	WORD $0x910006b5  // add	x21, x21, #1
	WORD $0xeb1502df  // cmp	x22, x21
	WORD $0x54ffff01  // b.ne	LBB0_91 $-32(%rip)
	WORD $0x14000eb4  // b	LBB0_829 $15056(%rip)
LBB0_93:
	WORD $0xeb1602bf  // cmp	x21, x22
	WORD $0x5401d642  // b.hs	LBB0_829 $15048(%rip)
LBB0_94:
	WORD $0x910006b6  // add	x22, x21, #1
	WORD $0xf9000036  // str	x22, [x1]
	WORD $0x38756a6e  // ldrb	w14, [x19, x21]
	WORD $0x7100e9df  // cmp	w14, #58
	WORD $0x5401d5a1  // b.ne	LBB0_829 $15028(%rip)
	WORD $0xb5011474  // cbnz	x20, LBB0_491 $8844(%rip)
	WORD $0xf9400417  // ldr	x23, [x0, #8]
	WORD $0xeb1702df  // cmp	x22, x23
	WORD $0x54000162  // b.hs	LBB0_101 $44(%rip)
	WORD $0x38766a62  // ldrb	w2, [x19, x22]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x54000100  // b.eq	LBB0_101 $32(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x540000c0  // b.eq	LBB0_101 $24(%rip)
	WORD $0x51002c4e  // sub	w14, w2, #11
	WORD $0x310009df  // cmn	w14, #2
	WORD $0x54000062  // b.hs	LBB0_101 $12(%rip)
	WORD $0xaa1603f4  // mov	x20, x22
	WORD $0x14000060  // b	LBB0_125 $384(%rip)
LBB0_101:
	WORD $0x91000ab4  // add	x20, x21, #2
	WORD $0xeb17029f  // cmp	x20, x23
	WORD $0x54000122  // b.hs	LBB0_105 $36(%rip)
	WORD $0x38746a62  // ldrb	w2, [x19, x20]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x540000c0  // b.eq	LBB0_105 $24(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x54000080  // b.eq	LBB0_105 $16(%rip)
	WORD $0x51002c4e  // sub	w14, w2, #11
	WORD $0x310009df  // cmn	w14, #2
	WORD $0x54000aa3  // b.lo	LBB0_125 $340(%rip)
LBB0_105:
	WORD $0x91000eb4  // add	x20, x21, #3
	WORD $0xeb17029f  // cmp	x20, x23
	WORD $0x54000122  // b.hs	LBB0_109 $36(%rip)
	WORD $0x38746a62  // ldrb	w2, [x19, x20]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x540000c0  // b.eq	LBB0_109 $24(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x54000080  // b.eq	LBB0_109 $16(%rip)
	WORD $0x51002c4e  // sub	w14, w2, #11
	WORD $0x310009df  // cmn	w14, #2
	WORD $0x54000943  // b.lo	LBB0_125 $296(%rip)
LBB0_109:
	WORD $0x910012b4  // add	x20, x21, #4
	WORD $0xeb17029f  // cmp	x20, x23
	WORD $0x54000122  // b.hs	LBB0_113 $36(%rip)
	WORD $0x38746a62  // ldrb	w2, [x19, x20]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x540000c0  // b.eq	LBB0_113 $24(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x54000080  // b.eq	LBB0_113 $16(%rip)
	WORD $0x51002c4e  // sub	w14, w2, #11
	WORD $0x310009df  // cmn	w14, #2
	WORD $0x540007e3  // b.lo	LBB0_125 $252(%rip)
LBB0_113:
	WORD $0x910016b4  // add	x20, x21, #5
	WORD $0xeb17029f  // cmp	x20, x23
	WORD $0x54001162  // b.hs	LBB0_151 $556(%rip)
LBB0_114:
	WORD $0x38746a6e  // ldrb	w14, [x19, x20]
	WORD $0x710081df  // cmp	w14, #32
	WORD $0x9ace218e  // lsl	x14, x12, x14
	WORD $0x8a0d01ce  // and	x14, x14, x13
	WORD $0xfa4099c4  // ccmp	x14, #0, #4, ls
	WORD $0x54000680  // b.eq	LBB0_124 $208(%rip)
	WORD $0x91000694  // add	x20, x20, #1
	WORD $0xeb1402ff  // cmp	x23, x20
	WORD $0x54ffff01  // b.ne	LBB0_114 $-32(%rip)
	WORD $0x14000083  // b	LBB0_152 $524(%rip)
LBB0_116:
	WORD $0x0b1302ae  // add	w14, w21, w19
	WORD $0x0b1a01ce  // add	w14, w14, w26
	WORD $0x110005ce  // add	w14, w14, #1
	WORD $0x92402dce  // and	x14, x14, #0xfff
	WORD $0x8b1a02d5  // add	x21, x22, x26
	WORD $0x8b1a0294  // add	x20, x20, x26
	WORD $0xf13fc1df  // cmp	x14, #4080
	WORD $0x540002a8  // b.hi	LBB0_119 $84(%rip)
	WORD $0x92402eae  // and	x14, x21, #0xfff
	WORD $0xf13fc5df  // cmp	x14, #4081
	WORD $0x54000242  // b.hs	LBB0_119 $72(%rip)
	WORD $0x3dc00293  // ldr	q19, [x20]
	WORD $0x3dc002b4  // ldr	q20, [x21]
	WORD $0x6e338e93  // cmeq.16b	v19, v20, v19
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e26026e  // fmov	w14, s19
	WORD $0x6a2e010e  // bics	w14, w8, w14
	WORD $0x1a9f17ef  // cset	w15, eq
	WORD $0xdac001ce  // rbit	x14, x14
	WORD $0xdac011ce  // clz	x14, x14
	WORD $0xeb1901df  // cmp	x14, x25
	WORD $0x1a9f35f6  // csinc	w22, w15, wzr, lo
Lloh10:
	WORD $0x10022c10  // adr	x16, __UnquoteTab $17792(%rip)
Lloh11:
	WORD $0x91000210  // add	x16, x16, __UnquoteTab@PAGEOFF $0(%rip)
	WORD $0x2a1603f4  // mov	w20, w22
	WORD $0x17ffff67  // b	LBB0_73 $-612(%rip)
LBB0_119:
	WORD $0xeb1a031f  // cmp	x24, x26
	WORD $0x540000c1  // b.ne	LBB0_121 $24(%rip)
	WORD $0x52800036  // mov	w22, #1
Lloh12:
	WORD $0x10022b30  // adr	x16, __UnquoteTab $17764(%rip)
Lloh13:
	WORD $0x91000210  // add	x16, x16, __UnquoteTab@PAGEOFF $0(%rip)
	WORD $0x2a1603f4  // mov	w20, w22
	WORD $0x17ffff60  // b	LBB0_73 $-640(%rip)
LBB0_121:
	WORD $0x52800038  // mov	w24, #1
Lloh14:
	WORD $0x10022a90  // adr	x16, __UnquoteTab $17744(%rip)
Lloh15:
	WORD $0x91000210  // add	x16, x16, __UnquoteTab@PAGEOFF $0(%rip)
LBB0_122:
	WORD $0x3840168e  // ldrb	w14, [x20], #1
	WORD $0x384016af  // ldrb	w15, [x21], #1
	WORD $0x6b0f01df  // cmp	w14, w15
	WORD $0x1a9f17f6  // cset	w22, eq
	WORD $0xfa580324  // ccmp	x25, x24, #4, eq
	WORD $0x91000718  // add	x24, x24, #1
	WORD $0x54ffff41  // b.ne	LBB0_122 $-24(%rip)
	WORD $0x2a1603f4  // mov	w20, w22
	WORD $0x17ffff54  // b	LBB0_73 $-688(%rip)
LBB0_124:
	WORD $0xeb17029f  // cmp	x20, x23
	WORD $0x54000a42  // b.hs	LBB0_152 $328(%rip)
LBB0_125:
	WORD $0x91000696  // add	x22, x20, #1
	WORD $0xf9000036  // str	x22, [x1]
	WORD $0x38746a75  // ldrb	w21, [x19, x20]
	WORD $0x71016abf  // cmp	w21, #90
	WORD $0x540005ec  // b.gt	LBB0_140 $188(%rip)
	WORD $0x7100bebf  // cmp	w21, #47
	WORD $0x540007ad  // b.le	LBB0_145 $244(%rip)
	WORD $0x5100c2ae  // sub	w14, w21, #48
	WORD $0x710029df  // cmp	w14, #10
	WORD $0x540008c2  // b.hs	LBB0_151 $280(%rip)
LBB0_128:
	WORD $0xf940040e  // ldr	x14, [x0, #8]
	WORD $0xcb1601d4  // sub	x20, x14, x22
	WORD $0xf100429f  // cmp	x20, #16
	WORD $0x540001e3  // b.lo	LBB0_131 $60(%rip)
LBB0_129:
	WORD $0x3cf66a73  // ldr	q19, [x19, x22]
	WORD $0x6e248e74  // cmeq.16b	v20, v19, v4
	WORD $0x4e251e73  // and.16b	v19, v19, v5
	WORD $0x6e268e73  // cmeq.16b	v19, v19, v6
	WORD $0x4eb41e73  // orr.16b	v19, v19, v20
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260262  // fmov	w2, s19
	WORD $0x350002e2  // cbnz	w2, LBB0_139 $92(%rip)
	WORD $0xd1004294  // sub	x20, x20, #16
	WORD $0x910042d6  // add	x22, x22, #16
	WORD $0xf1003e9f  // cmp	x20, #15
	WORD $0x54fffe68  // b.hi	LBB0_129 $-52(%rip)
LBB0_131:
	WORD $0x8b160275  // add	x21, x19, x22
	WORD $0xb40001f4  // cbz	x20, LBB0_138 $60(%rip)
	WORD $0x8b1402b7  // add	x23, x21, x20
	WORD $0xcb1302b6  // sub	x22, x21, x19
LBB0_133:
	WORD $0x394002a2  // ldrb	w2, [x21]
	WORD $0x7100b05f  // cmp	w2, #44
	WORD $0x54005d60  // b.eq	LBB0_246 $2988(%rip)
	WORD $0x7101f45f  // cmp	w2, #125
	WORD $0x54005d20  // b.eq	LBB0_246 $2980(%rip)
	WORD $0x7101745f  // cmp	w2, #93
	WORD $0x54005ce0  // b.eq	LBB0_246 $2972(%rip)
	WORD $0x910006b5  // add	x21, x21, #1
	WORD $0x910006d6  // add	x22, x22, #1
	WORD $0xf1000694  // subs	x20, x20, #1
	WORD $0x54fffec1  // b.ne	LBB0_133 $-40(%rip)
	WORD $0xaa1703f5  // mov	x21, x23
LBB0_138:
	WORD $0xcb1302b6  // sub	x22, x21, x19
	WORD $0x1400042e  // b	LBB0_314 $4280(%rip)
LBB0_139:
	WORD $0x5ac0004e  // rbit	w14, w2
	WORD $0x5ac011ce  // clz	w14, w14
	WORD $0x8b1601d6  // add	x22, x14, x22
	WORD $0xf9000036  // str	x22, [x1]
	WORD $0x1400001f  // b	LBB0_152 $124(%rip)
LBB0_140:
	WORD $0x7101b6bf  // cmp	w21, #109
	WORD $0x540002ad  // b.le	LBB0_148 $84(%rip)
	WORD $0x7101babf  // cmp	w21, #110
	WORD $0x54002b20  // b.eq	LBB0_200 $1380(%rip)
	WORD $0x7101d2bf  // cmp	w21, #116
	WORD $0x54002ae0  // b.eq	LBB0_200 $1372(%rip)
	WORD $0x7101eebf  // cmp	w21, #123
	WORD $0x540002a1  // b.ne	LBB0_151 $84(%rip)
	WORD $0xd2800019  // mov	x25, #0
	WORD $0xd2800017  // mov	x23, #0
	WORD $0xd2800014  // mov	x20, #0
	WORD $0xd2800015  // mov	x21, #0
	WORD $0xf940040e  // ldr	x14, [x0, #8]
	WORD $0xcb1601d8  // sub	x24, x14, x22
	WORD $0x8b160273  // add	x19, x19, x22
	WORD $0x1400005a  // b	LBB0_175 $360(%rip)
LBB0_145:
	WORD $0x340001d5  // cbz	w21, LBB0_152 $56(%rip)
	WORD $0x71008abf  // cmp	w21, #34
	WORD $0x54002f00  // b.eq	LBB0_209 $1504(%rip)
	WORD $0x7100b6bf  // cmp	w21, #45
	WORD $0x54fff860  // b.eq	LBB0_128 $-244(%rip)
	WORD $0x14000007  // b	LBB0_151 $28(%rip)
LBB0_148:
	WORD $0x71016ebf  // cmp	w21, #91
	WORD $0x54003660  // b.eq	LBB0_218 $1740(%rip)
	WORD $0x71019abf  // cmp	w21, #102
	WORD $0x54000061  // b.ne	LBB0_151 $12(%rip)
	WORD $0x91001682  // add	x2, x20, #5
	WORD $0x14000142  // b	LBB0_201 $1288(%rip)
LBB0_151:
	WORD $0xf9000034  // str	x20, [x1]
	WORD $0xaa1403f6  // mov	x22, x20
LBB0_152:
	WORD $0xa9405013  // ldp	x19, x20, [x0]
	WORD $0xeb1402df  // cmp	x22, x20
	WORD $0x54000162  // b.hs	LBB0_157 $44(%rip)
	WORD $0x38766a62  // ldrb	w2, [x19, x22]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x54000100  // b.eq	LBB0_157 $32(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x540000c0  // b.eq	LBB0_157 $24(%rip)
	WORD $0x51002c4e  // sub	w14, w2, #11
	WORD $0x310009df  // cmn	w14, #2
	WORD $0x54000062  // b.hs	LBB0_157 $12(%rip)
	WORD $0xaa1603f5  // mov	x21, x22
	WORD $0x14000031  // b	LBB0_173 $196(%rip)
LBB0_157:
	WORD $0x910006d5  // add	x21, x22, #1
	WORD $0xeb1402bf  // cmp	x21, x20
	WORD $0x54000122  // b.hs	LBB0_161 $36(%rip)
	WORD $0x38756a62  // ldrb	w2, [x19, x21]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x540000c0  // b.eq	LBB0_161 $24(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x54000080  // b.eq	LBB0_161 $16(%rip)
	WORD $0x51002c4e  // sub	w14, w2, #11
	WORD $0x310009df  // cmn	w14, #2
	WORD $0x540004c3  // b.lo	LBB0_173 $152(%rip)
LBB0_161:
	WORD $0x91000ad5  // add	x21, x22, #2
	WORD $0xeb1402bf  // cmp	x21, x20
	WORD $0x54000122  // b.hs	LBB0_165 $36(%rip)
	WORD $0x38756a62  // ldrb	w2, [x19, x21]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x540000c0  // b.eq	LBB0_165 $24(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x54000080  // b.eq	LBB0_165 $16(%rip)
	WORD $0x51002c4e  // sub	w14, w2, #11
	WORD $0x310009df  // cmn	w14, #2
	WORD $0x54000363  // b.lo	LBB0_173 $108(%rip)
LBB0_165:
	WORD $0x91000ed5  // add	x21, x22, #3
	WORD $0xeb1402bf  // cmp	x21, x20
	WORD $0x54000122  // b.hs	LBB0_169 $36(%rip)
	WORD $0x38756a62  // ldrb	w2, [x19, x21]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x540000c0  // b.eq	LBB0_169 $24(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x54000080  // b.eq	LBB0_169 $16(%rip)
	WORD $0x51002c4e  // sub	w14, w2, #11
	WORD $0x310009df  // cmn	w14, #2
	WORD $0x54000203  // b.lo	LBB0_173 $64(%rip)
LBB0_169:
	WORD $0x910012d5  // add	x21, x22, #4
	WORD $0xeb1402bf  // cmp	x21, x20
	WORD $0x5401aa62  // b.hs	LBB0_802 $13644(%rip)
LBB0_170:
	WORD $0x38756a6e  // ldrb	w14, [x19, x21]
	WORD $0x710081df  // cmp	w14, #32
	WORD $0x9ace218e  // lsl	x14, x12, x14
	WORD $0x8a0d01ce  // and	x14, x14, x13
	WORD $0xfa4099c4  // ccmp	x14, #0, #4, ls
	WORD $0x540000a0  // b.eq	LBB0_172 $20(%rip)
	WORD $0x910006b5  // add	x21, x21, #1
	WORD $0xeb15029f  // cmp	x20, x21
	WORD $0x54ffff01  // b.ne	LBB0_170 $-32(%rip)
	WORD $0x14000db4  // b	LBB0_829 $14032(%rip)
LBB0_172:
	WORD $0xeb1402bf  // cmp	x21, x20
	WORD $0x5401b642  // b.hs	LBB0_829 $14024(%rip)
LBB0_173:
	WORD $0x910006b7  // add	x23, x21, #1
	WORD $0xf9000037  // str	x23, [x1]
	WORD $0x38756a62  // ldrb	w2, [x19, x21]
	WORD $0x7100b05f  // cmp	w2, #44
	WORD $0x54ffbec0  // b.eq	LBB0_29 $-2088(%rip)
	WORD $0x14000daa  // b	LBB0_828 $13992(%rip)
LBB0_174:
	WORD $0x937fff39  // asr	x25, x25, #63
	WORD $0x9e670353  // fmov	d19, x26
	WORD $0x0e205a73  // cnt.8b	v19, v19
	WORD $0x2e303a73  // uaddlv.8b	h19, v19
	WORD $0x1e26026e  // fmov	w14, s19
	WORD $0x8b1401d4  // add	x20, x14, x20
	WORD $0x91010273  // add	x19, x19, #64
	WORD $0xf9400ff8  // ldr	x24, [sp, #24]
LBB0_175:
	WORD $0xf101030e  // subs	x14, x24, #64
	WORD $0xf9000fee  // str	x14, [sp, #24]
	WORD $0x540016eb  // b.lt	LBB0_182 $732(%rip)
LBB0_176:
	WORD $0xb201e3f6  // mov	x22, #-8608480567731124088
	WORD $0xb203e3fb  // mov	x27, #2459565876494606882
	WORD $0xf2e0445b  // movk	x27, #546, lsl #48
	WORD $0xad405676  // ldp	q22, q21, [x19]
	WORD $0xad414e74  // ldp	q20, q19, [x19, #32]
	WORD $0x6e238ed7  // cmeq.16b	v23, v22, v3
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602ee  // fmov	w14, s23
	WORD $0x6e238eb7  // cmeq.16b	v23, v21, v3
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602ef  // fmov	w15, s23
	WORD $0x6e238e97  // cmeq.16b	v23, v20, v3
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f0  // fmov	w16, s23
	WORD $0x6e238e77  // cmeq.16b	v23, v19, v3
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f1  // fmov	w17, s23
	WORD $0xd3607e10  // lsl	x16, x16, #32
	WORD $0xaa11c210  // orr	x16, x16, x17, lsl #48
	WORD $0x53103def  // lsl	w15, w15, #16
	WORD $0xaa0f020f  // orr	x15, x16, x15
	WORD $0xaa0e01e2  // orr	x2, x15, x14
	WORD $0xaa17004e  // orr	x14, x2, x23
	WORD $0xb500008e  // cbnz	x14, LBB0_178 $16(%rip)
	WORD $0xd2800017  // mov	x23, #0
	WORD $0xd280001a  // mov	x26, #0
	WORD $0x1400000a  // b	LBB0_179 $40(%rip)
LBB0_178:
	WORD $0x8a37004e  // bic	x14, x2, x23
	WORD $0xaa0e06ef  // orr	x15, x23, x14, lsl #1
	WORD $0x8a2f0050  // bic	x16, x2, x15
	WORD $0x9201f210  // and	x16, x16, #0xaaaaaaaaaaaaaaaa
	WORD $0xab0e020e  // adds	x14, x16, x14
	WORD $0x1a9f37f7  // cset	w23, hs
	WORD $0xd37ff9ce  // lsl	x14, x14, #1
	WORD $0xd200f1ce  // eor	x14, x14, #0x5555555555555555
	WORD $0x8a0f01da  // and	x26, x14, x15
LBB0_179:
	WORD $0x6e208ed7  // cmeq.16b	v23, v22, v0
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602ee  // fmov	w14, s23
	WORD $0x6e208eb7  // cmeq.16b	v23, v21, v0
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602ef  // fmov	w15, s23
	WORD $0x6e208e97  // cmeq.16b	v23, v20, v0
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f0  // fmov	w16, s23
	WORD $0x6e208e77  // cmeq.16b	v23, v19, v0
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f1  // fmov	w17, s23
	WORD $0xd3607e10  // lsl	x16, x16, #32
	WORD $0xaa11c210  // orr	x16, x16, x17, lsl #48
	WORD $0x53103def  // lsl	w15, w15, #16
	WORD $0xaa0f020f  // orr	x15, x16, x15
	WORD $0xaa0e01ee  // orr	x14, x15, x14
	WORD $0x8a3a01ce  // bic	x14, x14, x26
	WORD $0x9200e1cf  // and	x15, x14, #0x1111111111111111
	WORD $0x9203e1d0  // and	x16, x14, #0x2222222222222222
	WORD $0x9202e1d1  // and	x17, x14, #0x4444444444444444
	WORD $0x9201e1ce  // and	x14, x14, #0x8888888888888888
	WORD $0x9b047de2  // mul	x2, x15, x4
	WORD $0x9b1c7e06  // mul	x6, x16, x28
	WORD $0xca060042  // eor	x2, x2, x6
	WORD $0x9b077e26  // mul	x6, x17, x7
	WORD $0xb202e3e5  // mov	x5, #4919131752989213764
	WORD $0xf2e08885  // movk	x5, #1092, lsl #48
	WORD $0x9b1b7dc7  // mul	x7, x14, x27
	WORD $0xca0700c6  // eor	x6, x6, x7
	WORD $0xca060042  // eor	x2, x2, x6
	WORD $0xb203e3fe  // mov	lr, #2459565876494606882
	WORD $0x9b1e7de6  // mul	x6, x15, lr
	WORD $0x9b047e07  // mul	x7, x16, x4
	WORD $0xca0700c6  // eor	x6, x6, x7
	WORD $0x9b1c7e27  // mul	x7, x17, x28
	WORD $0x9b057dda  // mul	x26, x14, x5
	WORD $0xca1a00e7  // eor	x7, x7, x26
	WORD $0xca0700c6  // eor	x6, x6, x7
	WORD $0xb202e3e5  // mov	x5, #4919131752989213764
	WORD $0x9b057de7  // mul	x7, x15, x5
	WORD $0x9b1e7e1a  // mul	x26, x16, lr
	WORD $0xca1a00e7  // eor	x7, x7, x26
	WORD $0x9b047e3a  // mul	x26, x17, x4
	WORD $0x9b1c7ddb  // mul	x27, x14, x28
	WORD $0xca1b035a  // eor	x26, x26, x27
	WORD $0xca1a00e7  // eor	x7, x7, x26
	WORD $0x9b167def  // mul	x15, x15, x22
	WORD $0x9b057e10  // mul	x16, x16, x5
	WORD $0xca1001ef  // eor	x15, x15, x16
	WORD $0x9b1e7e30  // mul	x16, x17, lr
	WORD $0x9b047dce  // mul	x14, x14, x4
	WORD $0xca0e020e  // eor	x14, x16, x14
	WORD $0xca0e01ee  // eor	x14, x15, x14
	WORD $0x9200e04f  // and	x15, x2, #0x1111111111111111
	WORD $0x9203e0d0  // and	x16, x6, #0x2222222222222222
	WORD $0x9202e0f1  // and	x17, x7, #0x4444444444444444
	WORD $0x9201e1ce  // and	x14, x14, #0x8888888888888888
	WORD $0xaa1001ef  // orr	x15, x15, x16
	WORD $0xaa0e022e  // orr	x14, x17, x14
	WORD $0xaa0e01ee  // orr	x14, x15, x14
	WORD $0xca1901d9  // eor	x25, x14, x25
	WORD $0x6e318ed7  // cmeq.16b	v23, v22, v17
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602ee  // fmov	w14, s23
	WORD $0x6e318eb7  // cmeq.16b	v23, v21, v17
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602ef  // fmov	w15, s23
	WORD $0x6e318e97  // cmeq.16b	v23, v20, v17
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f0  // fmov	w16, s23
	WORD $0x6e318e77  // cmeq.16b	v23, v19, v17
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f1  // fmov	w17, s23
	WORD $0xd3607e10  // lsl	x16, x16, #32
	WORD $0xaa11c210  // orr	x16, x16, x17, lsl #48
	WORD $0x53103def  // lsl	w15, w15, #16
	WORD $0xaa0f020f  // orr	x15, x16, x15
	WORD $0xaa0e01ee  // orr	x14, x15, x14
	WORD $0x8a3901da  // bic	x26, x14, x25
	WORD $0x6e328ed6  // cmeq.16b	v22, v22, v18
	WORD $0x4e211ed6  // and.16b	v22, v22, v1
	WORD $0x4e0202d6  // tbl.16b	v22, { v22 }, v2
	WORD $0x4e71bad6  // addv.8h	h22, v22
	WORD $0x1e2602ce  // fmov	w14, s22
	WORD $0x6e328eb5  // cmeq.16b	v21, v21, v18
	WORD $0x4e211eb5  // and.16b	v21, v21, v1
	WORD $0x4e0202b5  // tbl.16b	v21, { v21 }, v2
	WORD $0x4e71bab5  // addv.8h	h21, v21
	WORD $0x1e2602af  // fmov	w15, s21
	WORD $0x6e328e94  // cmeq.16b	v20, v20, v18
	WORD $0x4e211e94  // and.16b	v20, v20, v1
	WORD $0x4e020294  // tbl.16b	v20, { v20 }, v2
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260290  // fmov	w16, s20
	WORD $0x6e328e73  // cmeq.16b	v19, v19, v18
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260271  // fmov	w17, s19
	WORD $0xd3607e10  // lsl	x16, x16, #32
	WORD $0xaa11c210  // orr	x16, x16, x17, lsl #48
	WORD $0x53103def  // lsl	w15, w15, #16
	WORD $0xaa0f020f  // orr	x15, x16, x15
	WORD $0xaa0e01ee  // orr	x14, x15, x14
	WORD $0xb202e3e7  // mov	x7, #4919131752989213764
	WORD $0xf2e08887  // movk	x7, #1092, lsl #48
	WORD $0xea3901db  // bics	x27, x14, x25
	WORD $0x54ffe9a0  // b.eq	LBB0_174 $-716(%rip)
LBB0_180:
	WORD $0xd1000762  // sub	x2, x27, #1
	WORD $0x8a1a004e  // and	x14, x2, x26
	WORD $0x9e6701d3  // fmov	d19, x14
	WORD $0x0e205a73  // cnt.8b	v19, v19
	WORD $0x2e303a73  // uaddlv.8b	h19, v19
	WORD $0x1e26026e  // fmov	w14, s19
	WORD $0x8b1401ce  // add	x14, x14, x20
	WORD $0xeb1501df  // cmp	x14, x21
	WORD $0x54003589  // b.ls	LBB0_244 $1712(%rip)
	WORD $0x910006b5  // add	x21, x21, #1
	WORD $0xea1b005b  // ands	x27, x2, x27
	WORD $0x54fffea1  // b.ne	LBB0_180 $-44(%rip)
	WORD $0x17ffff40  // b	LBB0_174 $-768(%rip)
LBB0_182:
	WORD $0xf100031f  // cmp	x24, #0
	WORD $0x54005ccd  // b.le	LBB0_306 $2968(%rip)
	WORD $0xad0243f0  // stp	q16, q16, [sp, #64]
	WORD $0xad0143f0  // stp	q16, q16, [sp, #32]
	WORD $0x92402e68  // and	x8, x19, #0xfff
	WORD $0xf13f051f  // cmp	x8, #4033
	WORD $0x54000363  // b.lo	LBB0_194 $108(%rip)
	WORD $0xf100831a  // subs	x26, x24, #32
	WORD $0x540000a3  // b.lo	LBB0_186 $20(%rip)
	WORD $0xacc15273  // ldp	q19, q20, [x19], #32
	WORD $0xad0153f3  // stp	q19, q20, [sp, #32]
	WORD $0xaa0b03e8  // mov	x8, x11
	WORD $0x14000003  // b	LBB0_187 $12(%rip)
LBB0_186:
	WORD $0x910083e8  // add	x8, sp, #32
	WORD $0xaa1803fa  // mov	x26, x24
LBB0_187:
	WORD $0xf1004342  // subs	x2, x26, #16
	WORD $0x54000303  // b.lo	LBB0_195 $96(%rip)
	WORD $0x3cc10673  // ldr	q19, [x19], #16
	WORD $0x3c810513  // str	q19, [x8], #16
	WORD $0xaa0203fa  // mov	x26, x2
	WORD $0xf1002042  // subs	x2, x2, #8
	WORD $0x540002a2  // b.hs	LBB0_196 $84(%rip)
LBB0_189:
	WORD $0xf1001342  // subs	x2, x26, #4
	WORD $0x54000303  // b.lo	LBB0_197 $96(%rip)
LBB0_190:
	WORD $0xb840466e  // ldr	w14, [x19], #4
	WORD $0xb800450e  // str	w14, [x8], #4
	WORD $0xaa0203fa  // mov	x26, x2
	WORD $0xf1000842  // subs	x2, x2, #2
	WORD $0x540002a2  // b.hs	LBB0_198 $84(%rip)
LBB0_191:
	WORD $0xb400007a  // cbz	x26, LBB0_193 $12(%rip)
LBB0_192:
	WORD $0x3940026e  // ldrb	w14, [x19]
	WORD $0x3900010e  // strb	w14, [x8]
LBB0_193:
	WORD $0x910083f3  // add	x19, sp, #32
LBB0_194:
	WORD $0x529fffe8  // mov	w8, #65535
	WORD $0xb201e3fc  // mov	x28, #-8608480567731124088
	WORD $0xf2e1111c  // movk	x28, #2184, lsl #48
	WORD $0xb202e3e7  // mov	x7, #4919131752989213764
	WORD $0xf2e08887  // movk	x7, #1092, lsl #48
	WORD $0xb200e3e4  // mov	x4, #1229782938247303441
	WORD $0x17ffff23  // b	LBB0_176 $-884(%rip)
LBB0_195:
	WORD $0xf1002342  // subs	x2, x26, #8
	WORD $0x54fffda3  // b.lo	LBB0_189 $-76(%rip)
LBB0_196:
	WORD $0xf840866e  // ldr	x14, [x19], #8
	WORD $0xf800850e  // str	x14, [x8], #8
	WORD $0xaa0203fa  // mov	x26, x2
	WORD $0xf1001042  // subs	x2, x2, #4
	WORD $0x54fffd42  // b.hs	LBB0_190 $-88(%rip)
LBB0_197:
	WORD $0xf1000b42  // subs	x2, x26, #2
	WORD $0x54fffda3  // b.lo	LBB0_191 $-76(%rip)
LBB0_198:
	WORD $0x7840266e  // ldrh	w14, [x19], #2
	WORD $0x7800250e  // strh	w14, [x8], #2
	WORD $0xaa0203fa  // mov	x26, x2
	WORD $0xb5fffd42  // cbnz	x2, LBB0_192 $-88(%rip)
	WORD $0x17ffffeb  // b	LBB0_193 $-84(%rip)
LBB0_199:
	WORD $0x8b190270  // add	x16, x19, x25
	WORD $0xb202e3e7  // mov	x7, #4919131752989213764
	WORD $0xf2e08887  // movk	x7, #1092, lsl #48
	WORD $0x1400000b  // b	LBB0_204 $44(%rip)
LBB0_200:
	WORD $0x91001282  // add	x2, x20, #4
LBB0_201:
	WORD $0xf940040e  // ldr	x14, [x0, #8]
	WORD $0xeb0e005f  // cmp	x2, x14
	WORD $0x54ffd7e8  // b.hi	LBB0_152 $-1284(%rip)
	WORD $0xf9000022  // str	x2, [x1]
	WORD $0xaa0203f6  // mov	x22, x2
	WORD $0x17fffebc  // b	LBB0_152 $-1296(%rip)
LBB0_203:
	WORD $0xd2800017  // mov	x23, #0
	WORD $0x9280001a  // mov	x26, #-1
	WORD $0xaa1403f0  // mov	x16, x20
LBB0_204:
	WORD $0xf1008379  // subs	x25, x27, #32
	WORD $0x54004b03  // b.lo	LBB0_289 $2400(%rip)
	WORD $0xad405213  // ldp	q19, q20, [x16]
	WORD $0x6e208e75  // cmeq.16b	v21, v19, v0
	WORD $0x6e208e96  // cmeq.16b	v22, v20, v0
	WORD $0x6e238e73  // cmeq.16b	v19, v19, v3
	WORD $0x6e238e94  // cmeq.16b	v20, v20, v3
	WORD $0x4e211eb5  // and.16b	v21, v21, v1
	WORD $0x4e0202b5  // tbl.16b	v21, { v21 }, v2
	WORD $0x4e71bab5  // addv.8h	h21, v21
	WORD $0x1e2602bb  // fmov	w27, s21
	WORD $0x4e211ed5  // and.16b	v21, v22, v1
	WORD $0x4e0202b5  // tbl.16b	v21, { v21 }, v2
	WORD $0x4e71bab5  // addv.8h	h21, v21
	WORD $0x1e2602ae  // fmov	w14, s21
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e26027e  // fmov	w30, s19
	WORD $0x4e211e93  // and.16b	v19, v20, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e26026f  // fmov	w15, s19
	WORD $0x33103ddb  // bfi	w27, w14, #16, #16
	WORD $0x33103dfe  // bfi	w30, w15, #16, #16
	WORD $0x3500485e  // cbnz	w30, LBB0_290 $2312(%rip)
	WORD $0xb50048f7  // cbnz	x23, LBB0_291 $2332(%rip)
	WORD $0x3200f3fe  // mov	w30, #1431655765
	WORD $0x3201f3e5  // mov	w5, #-1431655766
	WORD $0xb4004adb  // cbz	x27, LBB0_292 $2392(%rip)
LBB0_208:
	WORD $0xdac0036e  // rbit	x14, x27
	WORD $0xdac011ce  // clz	x14, x14
	WORD $0xcb13020f  // sub	x15, x16, x19
	WORD $0x8b0e01ee  // add	x14, x15, x14
	WORD $0x910005d7  // add	x23, x14, #1
	WORD $0x17fffd6e  // b	LBB0_61 $-2632(%rip)
LBB0_209:
	WORD $0xf9400402  // ldr	x2, [x0, #8]
	WORD $0xcb160055  // sub	x21, x2, x22
	WORD $0xf10082bf  // cmp	x21, #32
	WORD $0x540045cb  // b.lt	LBB0_288 $2232(%rip)
	WORD $0xd2800015  // mov	x21, #0
	WORD $0xd2800019  // mov	x25, #0
	WORD $0x8b140278  // add	x24, x19, x20
	WORD $0xcb140057  // sub	x23, x2, x20
	WORD $0x528003fa  // mov	w26, #31
LBB0_211:
	WORD $0x8b15030e  // add	x14, x24, x21
	WORD $0x3cc011d3  // ldur	q19, [x14, #1]
	WORD $0x3cc111d4  // ldur	q20, [x14, #17]
	WORD $0x6e208e75  // cmeq.16b	v21, v19, v0
	WORD $0x4e211eb5  // and.16b	v21, v21, v1
	WORD $0x4e0202b5  // tbl.16b	v21, { v21 }, v2
	WORD $0x4e71bab5  // addv.8h	h21, v21
	WORD $0x1e2602bb  // fmov	w27, s21
	WORD $0x6e208e95  // cmeq.16b	v21, v20, v0
	WORD $0x4e211eb5  // and.16b	v21, v21, v1
	WORD $0x4e0202b5  // tbl.16b	v21, { v21 }, v2
	WORD $0x4e71bab5  // addv.8h	h21, v21
	WORD $0x1e2602ae  // fmov	w14, s21
	WORD $0x33103ddb  // bfi	w27, w14, #16, #16
	WORD $0x6e238e73  // cmeq.16b	v19, v19, v3
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260262  // fmov	w2, s19
	WORD $0x6e238e93  // cmeq.16b	v19, v20, v3
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e26026e  // fmov	w14, s19
	WORD $0x33103dc2  // bfi	w2, w14, #16, #16
	WORD $0x7100005f  // cmp	w2, #0
	WORD $0xfa400b20  // ccmp	x25, #0, #0, eq
	WORD $0x54000180  // b.eq	LBB0_213 $48(%rip)
	WORD $0x0a39004e  // bic	w14, w2, w25
	WORD $0x2a0e072f  // orr	w15, w25, w14, lsl #1
	WORD $0x0a050050  // and	w16, w2, w5
	WORD $0x0a2f0210  // bic	w16, w16, w15
	WORD $0x2b0e020e  // adds	w14, w16, w14
	WORD $0x1a9f37f9  // cset	w25, hs
	WORD $0x4a0e07ce  // eor	w14, w30, w14, lsl #1
	WORD $0x0a0f01ce  // and	w14, w14, w15
	WORD $0x2a2e03ee  // mvn	w14, w14
	WORD $0x8a1b01db  // and	x27, x14, x27
	WORD $0x14000002  // b	LBB0_214 $8(%rip)
LBB0_213:
	WORD $0xd2800019  // mov	x25, #0
LBB0_214:
	WORD $0xb500235b  // cbnz	x27, LBB0_245 $1128(%rip)
	WORD $0x910082b5  // add	x21, x21, #32
	WORD $0xd100835a  // sub	x26, x26, #32
	WORD $0x8b1a02ee  // add	x14, x23, x26
	WORD $0xf100fddf  // cmp	x14, #63
	WORD $0x54fffa6c  // b.gt	LBB0_211 $-180(%rip)
	WORD $0xb5004959  // cbnz	x25, LBB0_307 $2344(%rip)
	WORD $0x8b140268  // add	x8, x19, x20
	WORD $0x8b150108  // add	x8, x8, x21
	WORD $0x91000508  // add	x8, x8, #1
	WORD $0xaa3503ee  // mvn	x14, x21
	WORD $0x8b1701d5  // add	x21, x14, x23
	WORD $0xf10006bf  // cmp	x21, #1
	WORD $0x54004a6a  // b.ge	LBB0_310 $2380(%rip)
	WORD $0x14000260  // b	LBB0_315 $2432(%rip)
LBB0_218:
	WORD $0xd2800019  // mov	x25, #0
	WORD $0xd2800017  // mov	x23, #0
	WORD $0xd2800014  // mov	x20, #0
	WORD $0xd2800015  // mov	x21, #0
	WORD $0xf940040e  // ldr	x14, [x0, #8]
	WORD $0xcb1601d8  // sub	x24, x14, x22
	WORD $0x8b160273  // add	x19, x19, x22
	WORD $0x14000009  // b	LBB0_220 $36(%rip)
LBB0_219:
	WORD $0x937fff39  // asr	x25, x25, #63
	WORD $0x9e670353  // fmov	d19, x26
	WORD $0x0e205a73  // cnt.8b	v19, v19
	WORD $0x2e303a73  // uaddlv.8b	h19, v19
	WORD $0x1e26026e  // fmov	w14, s19
	WORD $0x8b1401d4  // add	x20, x14, x20
	WORD $0x91010273  // add	x19, x19, #64
	WORD $0xf9400ff8  // ldr	x24, [sp, #24]
LBB0_220:
	WORD $0xf101030e  // subs	x14, x24, #64
	WORD $0xf9000fee  // str	x14, [sp, #24]
	WORD $0x540016eb  // b.lt	LBB0_227 $732(%rip)
LBB0_221:
	WORD $0xb201e3f6  // mov	x22, #-8608480567731124088
	WORD $0xb203e3fb  // mov	x27, #2459565876494606882
	WORD $0xf2e0445b  // movk	x27, #546, lsl #48
	WORD $0xad405676  // ldp	q22, q21, [x19]
	WORD $0xad414e74  // ldp	q20, q19, [x19, #32]
	WORD $0x6e238ed7  // cmeq.16b	v23, v22, v3
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602ee  // fmov	w14, s23
	WORD $0x6e238eb7  // cmeq.16b	v23, v21, v3
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602ef  // fmov	w15, s23
	WORD $0x6e238e97  // cmeq.16b	v23, v20, v3
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f0  // fmov	w16, s23
	WORD $0x6e238e77  // cmeq.16b	v23, v19, v3
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f1  // fmov	w17, s23
	WORD $0xd3607e10  // lsl	x16, x16, #32
	WORD $0xaa11c210  // orr	x16, x16, x17, lsl #48
	WORD $0x53103def  // lsl	w15, w15, #16
	WORD $0xaa0f020f  // orr	x15, x16, x15
	WORD $0xaa0e01e2  // orr	x2, x15, x14
	WORD $0xaa17004e  // orr	x14, x2, x23
	WORD $0xb500008e  // cbnz	x14, LBB0_223 $16(%rip)
	WORD $0xd2800017  // mov	x23, #0
	WORD $0xd280001a  // mov	x26, #0
	WORD $0x1400000a  // b	LBB0_224 $40(%rip)
LBB0_223:
	WORD $0x8a37004e  // bic	x14, x2, x23
	WORD $0xaa0e06ef  // orr	x15, x23, x14, lsl #1
	WORD $0x8a2f0050  // bic	x16, x2, x15
	WORD $0x9201f210  // and	x16, x16, #0xaaaaaaaaaaaaaaaa
	WORD $0xab0e020e  // adds	x14, x16, x14
	WORD $0x1a9f37f7  // cset	w23, hs
	WORD $0xd37ff9ce  // lsl	x14, x14, #1
	WORD $0xd200f1ce  // eor	x14, x14, #0x5555555555555555
	WORD $0x8a0f01da  // and	x26, x14, x15
LBB0_224:
	WORD $0x6e208ed7  // cmeq.16b	v23, v22, v0
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602ee  // fmov	w14, s23
	WORD $0x6e208eb7  // cmeq.16b	v23, v21, v0
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602ef  // fmov	w15, s23
	WORD $0x6e208e97  // cmeq.16b	v23, v20, v0
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f0  // fmov	w16, s23
	WORD $0x6e208e77  // cmeq.16b	v23, v19, v0
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f1  // fmov	w17, s23
	WORD $0xd3607e10  // lsl	x16, x16, #32
	WORD $0xaa11c210  // orr	x16, x16, x17, lsl #48
	WORD $0x53103def  // lsl	w15, w15, #16
	WORD $0xaa0f020f  // orr	x15, x16, x15
	WORD $0xaa0e01ee  // orr	x14, x15, x14
	WORD $0x8a3a01ce  // bic	x14, x14, x26
	WORD $0x9200e1cf  // and	x15, x14, #0x1111111111111111
	WORD $0x9203e1d0  // and	x16, x14, #0x2222222222222222
	WORD $0x9202e1d1  // and	x17, x14, #0x4444444444444444
	WORD $0x9201e1ce  // and	x14, x14, #0x8888888888888888
	WORD $0x9b047de2  // mul	x2, x15, x4
	WORD $0x9b1c7e06  // mul	x6, x16, x28
	WORD $0xca060042  // eor	x2, x2, x6
	WORD $0x9b077e26  // mul	x6, x17, x7
	WORD $0xb202e3e5  // mov	x5, #4919131752989213764
	WORD $0xf2e08885  // movk	x5, #1092, lsl #48
	WORD $0x9b1b7dc7  // mul	x7, x14, x27
	WORD $0xca0700c6  // eor	x6, x6, x7
	WORD $0xca060042  // eor	x2, x2, x6
	WORD $0xb203e3fe  // mov	lr, #2459565876494606882
	WORD $0x9b1e7de6  // mul	x6, x15, lr
	WORD $0x9b047e07  // mul	x7, x16, x4
	WORD $0xca0700c6  // eor	x6, x6, x7
	WORD $0x9b1c7e27  // mul	x7, x17, x28
	WORD $0x9b057dda  // mul	x26, x14, x5
	WORD $0xca1a00e7  // eor	x7, x7, x26
	WORD $0xca0700c6  // eor	x6, x6, x7
	WORD $0xb202e3e5  // mov	x5, #4919131752989213764
	WORD $0x9b057de7  // mul	x7, x15, x5
	WORD $0x9b1e7e1a  // mul	x26, x16, lr
	WORD $0xca1a00e7  // eor	x7, x7, x26
	WORD $0x9b047e3a  // mul	x26, x17, x4
	WORD $0x9b1c7ddb  // mul	x27, x14, x28
	WORD $0xca1b035a  // eor	x26, x26, x27
	WORD $0xca1a00e7  // eor	x7, x7, x26
	WORD $0x9b167def  // mul	x15, x15, x22
	WORD $0x9b057e10  // mul	x16, x16, x5
	WORD $0xca1001ef  // eor	x15, x15, x16
	WORD $0x9b1e7e30  // mul	x16, x17, lr
	WORD $0x9b047dce  // mul	x14, x14, x4
	WORD $0xca0e020e  // eor	x14, x16, x14
	WORD $0xca0e01ee  // eor	x14, x15, x14
	WORD $0x9200e04f  // and	x15, x2, #0x1111111111111111
	WORD $0x9203e0d0  // and	x16, x6, #0x2222222222222222
	WORD $0x9202e0f1  // and	x17, x7, #0x4444444444444444
	WORD $0x9201e1ce  // and	x14, x14, #0x8888888888888888
	WORD $0xaa1001ef  // orr	x15, x15, x16
	WORD $0xaa0e022e  // orr	x14, x17, x14
	WORD $0xaa0e01ee  // orr	x14, x15, x14
	WORD $0xca1901d9  // eor	x25, x14, x25
	WORD $0x6e278ed7  // cmeq.16b	v23, v22, v7
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602ee  // fmov	w14, s23
	WORD $0x6e278eb7  // cmeq.16b	v23, v21, v7
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602ef  // fmov	w15, s23
	WORD $0x6e278e97  // cmeq.16b	v23, v20, v7
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f0  // fmov	w16, s23
	WORD $0x6e278e77  // cmeq.16b	v23, v19, v7
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f1  // fmov	w17, s23
	WORD $0xd3607e10  // lsl	x16, x16, #32
	WORD $0xaa11c210  // orr	x16, x16, x17, lsl #48
	WORD $0x53103def  // lsl	w15, w15, #16
	WORD $0xaa0f020f  // orr	x15, x16, x15
	WORD $0xaa0e01ee  // orr	x14, x15, x14
	WORD $0x8a3901da  // bic	x26, x14, x25
	WORD $0x6e268ed6  // cmeq.16b	v22, v22, v6
	WORD $0x4e211ed6  // and.16b	v22, v22, v1
	WORD $0x4e0202d6  // tbl.16b	v22, { v22 }, v2
	WORD $0x4e71bad6  // addv.8h	h22, v22
	WORD $0x1e2602ce  // fmov	w14, s22
	WORD $0x6e268eb5  // cmeq.16b	v21, v21, v6
	WORD $0x4e211eb5  // and.16b	v21, v21, v1
	WORD $0x4e0202b5  // tbl.16b	v21, { v21 }, v2
	WORD $0x4e71bab5  // addv.8h	h21, v21
	WORD $0x1e2602af  // fmov	w15, s21
	WORD $0x6e268e94  // cmeq.16b	v20, v20, v6
	WORD $0x4e211e94  // and.16b	v20, v20, v1
	WORD $0x4e020294  // tbl.16b	v20, { v20 }, v2
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260290  // fmov	w16, s20
	WORD $0x6e268e73  // cmeq.16b	v19, v19, v6
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260271  // fmov	w17, s19
	WORD $0xd3607e10  // lsl	x16, x16, #32
	WORD $0xaa11c210  // orr	x16, x16, x17, lsl #48
	WORD $0x53103def  // lsl	w15, w15, #16
	WORD $0xaa0f020f  // orr	x15, x16, x15
	WORD $0xaa0e01ee  // orr	x14, x15, x14
	WORD $0xb202e3e7  // mov	x7, #4919131752989213764
	WORD $0xf2e08887  // movk	x7, #1092, lsl #48
	WORD $0xea3901db  // bics	x27, x14, x25
	WORD $0x54ffe9a0  // b.eq	LBB0_219 $-716(%rip)
LBB0_225:
	WORD $0xd1000762  // sub	x2, x27, #1
	WORD $0x8a1a004e  // and	x14, x2, x26
	WORD $0x9e6701d3  // fmov	d19, x14
	WORD $0x0e205a73  // cnt.8b	v19, v19
	WORD $0x2e303a73  // uaddlv.8b	h19, v19
	WORD $0x1e26026e  // fmov	w14, s19
	WORD $0x8b1401ce  // add	x14, x14, x20
	WORD $0xeb1501df  // cmp	x14, x21
	WORD $0x54000769  // b.ls	LBB0_244 $236(%rip)
	WORD $0x910006b5  // add	x21, x21, #1
	WORD $0xea1b005b  // ands	x27, x2, x27
	WORD $0x54fffea1  // b.ne	LBB0_225 $-44(%rip)
	WORD $0x17ffff40  // b	LBB0_219 $-768(%rip)
LBB0_227:
	WORD $0xf100031f  // cmp	x24, #0
	WORD $0x54002ead  // b.le	LBB0_306 $1492(%rip)
	WORD $0xad0243f0  // stp	q16, q16, [sp, #64]
	WORD $0xad0143f0  // stp	q16, q16, [sp, #32]
	WORD $0x92402e68  // and	x8, x19, #0xfff
	WORD $0xf13f051f  // cmp	x8, #4033
	WORD $0x54000363  // b.lo	LBB0_239 $108(%rip)
	WORD $0xf100831a  // subs	x26, x24, #32
	WORD $0x540000a3  // b.lo	LBB0_231 $20(%rip)
	WORD $0xacc15273  // ldp	q19, q20, [x19], #32
	WORD $0xad0153f3  // stp	q19, q20, [sp, #32]
	WORD $0xaa0b03e8  // mov	x8, x11
	WORD $0x14000003  // b	LBB0_232 $12(%rip)
LBB0_231:
	WORD $0x910083e8  // add	x8, sp, #32
	WORD $0xaa1803fa  // mov	x26, x24
LBB0_232:
	WORD $0xf1004342  // subs	x2, x26, #16
	WORD $0x54000303  // b.lo	LBB0_240 $96(%rip)
	WORD $0x3cc10673  // ldr	q19, [x19], #16
	WORD $0x3c810513  // str	q19, [x8], #16
	WORD $0xaa0203fa  // mov	x26, x2
	WORD $0xf1002042  // subs	x2, x2, #8
	WORD $0x540002a2  // b.hs	LBB0_241 $84(%rip)
LBB0_234:
	WORD $0xf1001342  // subs	x2, x26, #4
	WORD $0x54000303  // b.lo	LBB0_242 $96(%rip)
LBB0_235:
	WORD $0xb840466e  // ldr	w14, [x19], #4
	WORD $0xb800450e  // str	w14, [x8], #4
	WORD $0xaa0203fa  // mov	x26, x2
	WORD $0xf1000842  // subs	x2, x2, #2
	WORD $0x540002a2  // b.hs	LBB0_243 $84(%rip)
LBB0_236:
	WORD $0xb400007a  // cbz	x26, LBB0_238 $12(%rip)
LBB0_237:
	WORD $0x3940026e  // ldrb	w14, [x19]
	WORD $0x3900010e  // strb	w14, [x8]
LBB0_238:
	WORD $0x910083f3  // add	x19, sp, #32
LBB0_239:
	WORD $0x529fffe8  // mov	w8, #65535
	WORD $0xb201e3fc  // mov	x28, #-8608480567731124088
	WORD $0xf2e1111c  // movk	x28, #2184, lsl #48
	WORD $0xb202e3e7  // mov	x7, #4919131752989213764
	WORD $0xf2e08887  // movk	x7, #1092, lsl #48
	WORD $0xb200e3e4  // mov	x4, #1229782938247303441
	WORD $0x17ffff23  // b	LBB0_221 $-884(%rip)
LBB0_240:
	WORD $0xf1002342  // subs	x2, x26, #8
	WORD $0x54fffda3  // b.lo	LBB0_234 $-76(%rip)
LBB0_241:
	WORD $0xf840866e  // ldr	x14, [x19], #8
	WORD $0xf800850e  // str	x14, [x8], #8
	WORD $0xaa0203fa  // mov	x26, x2
	WORD $0xf1001042  // subs	x2, x2, #4
	WORD $0x54fffd42  // b.hs	LBB0_235 $-88(%rip)
LBB0_242:
	WORD $0xf1000b42  // subs	x2, x26, #2
	WORD $0x54fffda3  // b.lo	LBB0_236 $-76(%rip)
LBB0_243:
	WORD $0x7840266e  // ldrh	w14, [x19], #2
	WORD $0x7800250e  // strh	w14, [x8], #2
	WORD $0xaa0203fa  // mov	x26, x2
	WORD $0xb5fffd42  // cbnz	x2, LBB0_237 $-88(%rip)
	WORD $0x17ffffeb  // b	LBB0_238 $-84(%rip)
LBB0_244:
	WORD $0xf940040e  // ldr	x14, [x0, #8]
	WORD $0xdac0036f  // rbit	x15, x27
	WORD $0xdac011ef  // clz	x15, x15
	WORD $0xcb1801ef  // sub	x15, x15, x24
	WORD $0x8b0e01ee  // add	x14, x15, x14
	WORD $0x910005cf  // add	x15, x14, #1
	WORD $0xf900002f  // str	x15, [x1]
	WORD $0xf9400410  // ldr	x16, [x0, #8]
	WORD $0xeb1001ff  // cmp	x15, x16
	WORD $0x9a8e2616  // csinc	x22, x16, x14, hs
	WORD $0xf9000036  // str	x22, [x1]
	WORD $0x17fffd4a  // b	LBB0_152 $-2776(%rip)
LBB0_245:
	WORD $0xdac0036e  // rbit	x14, x27
	WORD $0xdac011ce  // clz	x14, x14
	WORD $0x8b15028f  // add	x15, x20, x21
	WORD $0x8b0f01ce  // add	x14, x14, x15
	WORD $0x910009d6  // add	x22, x14, #2
LBB0_246:
	WORD $0xf9000036  // str	x22, [x1]
	WORD $0x17fffd43  // b	LBB0_152 $-2804(%rip)
LBB0_247:
	WORD $0x5284801a  // mov	w26, #9216
	WORD $0x72bf941a  // movk	w26, #64672, lsl #16
	WORD $0xf90013ff  // str	xzr, [sp, #32]
	WORD $0x8b170268  // add	x8, x19, x23
	WORD $0xd1000515  // sub	x21, x8, #1
	WORD $0x8b1802d9  // add	x25, x22, x24
	WORD $0xeb15029f  // cmp	x20, x21
	WORD $0x54001942  // b.hs	LBB0_285 $808(%rip)
	WORD $0xf100071f  // cmp	x24, #1
	WORD $0x5400190b  // b.lt	LBB0_285 $800(%rip)
LBB0_249:
	WORD $0x39400288  // ldrb	w8, [x20]
	WORD $0x7101711f  // cmp	w8, #92
	WORD $0x540001e1  // b.ne	LBB0_254 $60(%rip)
	WORD $0xcb1402bb  // sub	x27, x21, x20
	WORD $0xf100077f  // cmp	x27, #1
	WORD $0x5401becb  // b.lt	LBB0_937 $14296(%rip)
	WORD $0x39400688  // ldrb	w8, [x20, #1]
Lloh16:
	WORD $0x1001c4ee  // adr	x14, __UnquoteTab $14492(%rip)
Lloh17:
	WORD $0x910001ce  // add	x14, x14, __UnquoteTab@PAGEOFF $0(%rip)
	WORD $0x386869c8  // ldrb	w8, [x14, x8]
	WORD $0x7103fd1f  // cmp	w8, #255
	WORD $0x54000180  // b.eq	LBB0_256 $48(%rip)
	WORD $0x3401bd68  // cbz	w8, LBB0_935 $14252(%rip)
	WORD $0x390083e8  // strb	w8, [sp, #32]
	WORD $0x91000a94  // add	x20, x20, #2
	WORD $0x52800028  // mov	w8, #1
	WORD $0x14000049  // b	LBB0_265 $292(%rip)
LBB0_254:
	WORD $0x394002ce  // ldrb	w14, [x22]
	WORD $0x6b0e011f  // cmp	w8, w14
	WORD $0x54001801  // b.ne	LBB0_287 $768(%rip)
	WORD $0x91000694  // add	x20, x20, #1
	WORD $0x910006d6  // add	x22, x22, #1
	WORD $0x14000054  // b	LBB0_271 $336(%rip)
LBB0_256:
	WORD $0xf100137f  // cmp	x27, #4
	WORD $0x5401bc43  // b.lo	LBB0_936 $14216(%rip)
	WORD $0xb8402288  // ldur	w8, [x20, #2]
	WORD $0x5299fa0e  // mov	w14, #53200
	WORD $0x72b9f9ee  // movk	w14, #53199, lsl #16
	WORD $0x0b0e010e  // add	w14, w8, w14
	WORD $0x3201c3ef  // mov	w15, #-2139062144
	WORD $0x0a2801f8  // bic	w24, w15, w8
	WORD $0x6a0e031f  // tst	w24, w14
	WORD $0x5401ba81  // b.ne	LBB0_933 $14160(%rip)
	WORD $0x5283232e  // mov	w14, #6425
	WORD $0x72a3232e  // movk	w14, #6425, lsl #16
	WORD $0x0b0e010e  // add	w14, w8, w14
	WORD $0x2a0801ce  // orr	w14, w14, w8
	WORD $0x3201c3f0  // mov	w16, #-2139062144
	WORD $0x6a0f01df  // tst	w14, w15
	WORD $0x5401b9a1  // b.ne	LBB0_933 $14132(%rip)
	WORD $0x3200dbee  // mov	w14, #2139062143
	WORD $0x0a0e0102  // and	w2, w8, w14
	WORD $0x3202c7ee  // mov	w14, #-1061109568
	WORD $0x4b0201ce  // sub	w14, w14, w2
	WORD $0x5288c8cf  // mov	w15, #17990
	WORD $0x72a8c8cf  // movk	w15, #17990, lsl #16
	WORD $0x0b0f004f  // add	w15, w2, w15
	WORD $0x0a0e01ee  // and	w14, w15, w14
	WORD $0x6a1801df  // tst	w14, w24
	WORD $0x5401b861  // b.ne	LBB0_933 $14092(%rip)
	WORD $0x3203cbee  // mov	w14, #-522133280
	WORD $0x4b0201ce  // sub	w14, w14, w2
	WORD $0x5287272f  // mov	w15, #14649
	WORD $0x72a7272f  // movk	w15, #14649, lsl #16
	WORD $0x0b0f004f  // add	w15, w2, w15
	WORD $0x0a0e01ee  // and	w14, w15, w14
	WORD $0x6a1801df  // tst	w14, w24
	WORD $0x5401b761  // b.ne	LBB0_933 $14060(%rip)
	WORD $0x5ac00908  // rev	w8, w8
	WORD $0x3200c3ee  // mov	w14, #16843009
	WORD $0x0a6811ce  // bic	w14, w14, w8, lsr #4
	WORD $0x0b0e0dce  // add	w14, w14, w14, lsl #3
	WORD $0x3200cfef  // mov	w15, #252645135
	WORD $0x0a0f0108  // and	w8, w8, w15
	WORD $0x0b0801c8  // add	w8, w14, w8
	WORD $0x2a481108  // orr	w8, w8, w8, lsr #4
	WORD $0x53087d0e  // lsr	w14, w8, #8
	WORD $0x12181dc2  // and	w2, w14, #0xff00
	WORD $0xaa0203f8  // mov	x24, x2
	WORD $0x33001d18  // bfxil	w24, w8, #0, #8
	WORD $0x91001a9c  // add	x28, x20, #6
	WORD $0x7101ff1f  // cmp	w24, #127
	WORD $0x540004e9  // b.ls	LBB0_273 $156(%rip)
	WORD $0x711fff1f  // cmp	w24, #2047
	WORD $0x54000529  // b.ls	LBB0_274 $164(%rip)
	WORD $0x5140384e  // sub	w14, w2, #14, lsl #12
	WORD $0x312005df  // cmn	w14, #2049
	WORD $0x540005e8  // b.hi	LBB0_275 $188(%rip)
	WORD $0x530c7c4e  // lsr	w14, w2, #12
	WORD $0x321b09ce  // orr	w14, w14, #0xe0
	WORD $0x390083ee  // strb	w14, [sp, #32]
	WORD $0x5280100e  // mov	w14, #128
	WORD $0x33062f0e  // bfxil	w14, w24, #6, #6
	WORD $0x390087ee  // strb	w14, [sp, #33]
	WORD $0x5280100e  // mov	w14, #128
	WORD $0x3300150e  // bfxil	w14, w8, #0, #6
	WORD $0x39008bee  // strb	w14, [sp, #34]
	WORD $0xaa1c03f4  // mov	x20, x28
	WORD $0x52800068  // mov	w8, #3
LBB0_265:
	WORD $0x910083f8  // add	x24, sp, #32
	WORD $0xaa080308  // orr	x8, x24, x8
	WORD $0xeb1902df  // cmp	x22, x25
	WORD $0x54000182  // b.hs	LBB0_270 $48(%rip)
	WORD $0x910083f8  // add	x24, sp, #32
LBB0_267:
	WORD $0x394002ce  // ldrb	w14, [x22]
	WORD $0x3940030f  // ldrb	w15, [x24]
	WORD $0x6b0f01df  // cmp	w14, w15
	WORD $0x540000e1  // b.ne	LBB0_270 $28(%rip)
	WORD $0x910006d6  // add	x22, x22, #1
	WORD $0x91000718  // add	x24, x24, #1
	WORD $0xeb1902df  // cmp	x22, x25
	WORD $0x54000062  // b.hs	LBB0_270 $12(%rip)
	WORD $0xeb08031f  // cmp	x24, x8
	WORD $0x54fffee3  // b.lo	LBB0_267 $-36(%rip)
LBB0_270:
	WORD $0xeb08031f  // cmp	x24, x8
	WORD $0x54000d41  // b.ne	LBB0_287 $424(%rip)
LBB0_271:
	WORD $0xeb15029f  // cmp	x20, x21
	WORD $0x54000b82  // b.hs	LBB0_285 $368(%rip)
	WORD $0xeb1902df  // cmp	x22, x25
	WORD $0x54fff263  // b.lo	LBB0_249 $-436(%rip)
	WORD $0x14000059  // b	LBB0_285 $356(%rip)
LBB0_273:
	WORD $0x390083e8  // strb	w8, [sp, #32]
	WORD $0xaa1c03f4  // mov	x20, x28
	WORD $0x52800028  // mov	w8, #1
	WORD $0x17ffffe7  // b	LBB0_265 $-100(%rip)
LBB0_274:
	WORD $0x53067f0e  // lsr	w14, w24, #6
	WORD $0x321a05ce  // orr	w14, w14, #0xc0
	WORD $0x390083ee  // strb	w14, [sp, #32]
	WORD $0x5280100e  // mov	w14, #128
	WORD $0x3300150e  // bfxil	w14, w8, #0, #6
	WORD $0x390087ee  // strb	w14, [sp, #33]
	WORD $0xaa1c03f4  // mov	x20, x28
	WORD $0x52800048  // mov	w8, #2
	WORD $0x17ffffde  // b	LBB0_265 $-136(%rip)
LBB0_275:
	WORD $0x92800068  // mov	x8, #-4
	WORD $0xf1001b7f  // cmp	x27, #6
	WORD $0x5401b543  // b.lo	LBB0_950 $13992(%rip)
	WORD $0x530a7f0e  // lsr	w14, w24, #10
	WORD $0x7100d9df  // cmp	w14, #54
	WORD $0x5401b4e8  // b.hi	LBB0_950 $13980(%rip)
	WORD $0x39400388  // ldrb	w8, [x28]
	WORD $0x7101711f  // cmp	w8, #92
	WORD $0x5401b421  // b.ne	LBB0_949 $13956(%rip)
	WORD $0x39401e88  // ldrb	w8, [x20, #7]
	WORD $0x7101d51f  // cmp	w8, #117
	WORD $0x5401b3c1  // b.ne	LBB0_949 $13944(%rip)
	WORD $0xb9400a88  // ldr	w8, [x20, #8]
	WORD $0x5299fa0e  // mov	w14, #53200
	WORD $0x72b9f9ee  // movk	w14, #53199, lsl #16
	WORD $0x0b0e010e  // add	w14, w8, w14
	WORD $0x0a28021b  // bic	w27, w16, w8
	WORD $0x6a0e037f  // tst	w27, w14
	WORD $0x5401b2a1  // b.ne	LBB0_948 $13908(%rip)
	WORD $0x5283232e  // mov	w14, #6425
	WORD $0x72a3232e  // movk	w14, #6425, lsl #16
	WORD $0x0b0e010e  // add	w14, w8, w14
	WORD $0x2a0801ce  // orr	w14, w14, w8
	WORD $0x6a1001df  // tst	w14, w16
	WORD $0x5401b1e1  // b.ne	LBB0_948 $13884(%rip)
	WORD $0x3200dbee  // mov	w14, #2139062143
	WORD $0x0a0e0102  // and	w2, w8, w14
	WORD $0x3202c7ee  // mov	w14, #-1061109568
	WORD $0x4b0201ce  // sub	w14, w14, w2
	WORD $0x5288c8cf  // mov	w15, #17990
	WORD $0x72a8c8cf  // movk	w15, #17990, lsl #16
	WORD $0x0b0f004f  // add	w15, w2, w15
	WORD $0x0a0e01ee  // and	w14, w15, w14
	WORD $0x6a1b01df  // tst	w14, w27
	WORD $0x5401b0a1  // b.ne	LBB0_948 $13844(%rip)
	WORD $0x3203cbee  // mov	w14, #-522133280
	WORD $0x4b0201ce  // sub	w14, w14, w2
	WORD $0x5287272f  // mov	w15, #14649
	WORD $0x72a7272f  // movk	w15, #14649, lsl #16
	WORD $0x0b0f004f  // add	w15, w2, w15
	WORD $0x0a0e01ee  // and	w14, w15, w14
	WORD $0x6a1b01df  // tst	w14, w27
	WORD $0x5401afa1  // b.ne	LBB0_948 $13812(%rip)
	WORD $0x5ac00908  // rev	w8, w8
	WORD $0x3200c3ee  // mov	w14, #16843009
	WORD $0x0a6811ce  // bic	w14, w14, w8, lsr #4
	WORD $0x0b0e0dce  // add	w14, w14, w14, lsl #3
	WORD $0x3200cfef  // mov	w15, #252645135
	WORD $0x0a0f0108  // and	w8, w8, w15
	WORD $0x0b0801c8  // add	w8, w14, w8
	WORD $0x2a481108  // orr	w8, w8, w8, lsr #4
	WORD $0x53087d0e  // lsr	w14, w8, #8
	WORD $0x12181dc2  // and	w2, w14, #0xff00
	WORD $0x5140384e  // sub	w14, w2, #14, lsl #12
	WORD $0x311001df  // cmn	w14, #1024
	WORD $0x5401aee3  // b.lo	LBB0_951 $13788(%rip)
	WORD $0x12001d0e  // and	w14, w8, #0xff
	WORD $0x2a1829ce  // orr	w14, w14, w24, lsl #10
	WORD $0x0b1a004f  // add	w15, w2, w26
	WORD $0x0b0f01ce  // add	w14, w14, w15
	WORD $0x53127dcf  // lsr	w15, w14, #18
	WORD $0x321c0def  // orr	w15, w15, #0xf0
	WORD $0x390083ef  // strb	w15, [sp, #32]
	WORD $0x5280100f  // mov	w15, #128
	WORD $0x330c45cf  // bfxil	w15, w14, #12, #6
	WORD $0x390087ef  // strb	w15, [sp, #33]
	WORD $0x5280100f  // mov	w15, #128
	WORD $0x33062dcf  // bfxil	w15, w14, #6, #6
	WORD $0x39008bef  // strb	w15, [sp, #34]
	WORD $0x5280100e  // mov	w14, #128
	WORD $0x3300150e  // bfxil	w14, w8, #0, #6
	WORD $0x39008fee  // strb	w14, [sp, #35]
	WORD $0x91003294  // add	x20, x20, #12
	WORD $0x52800088  // mov	w8, #4
	WORD $0x17ffff93  // b	LBB0_265 $-436(%rip)
LBB0_285:
	WORD $0xeb15029f  // cmp	x20, x21
	WORD $0xfa5902c0  // ccmp	x22, x25, #0, eq
	WORD $0x1a9f17f4  // cset	w20, eq
LBB0_286:
	WORD $0x529fffe8  // mov	w8, #65535
	WORD $0x3200f3fe  // mov	w30, #1431655765
	WORD $0x3201f3e5  // mov	w5, #-1431655766
	WORD $0xb201e3fc  // mov	x28, #-8608480567731124088
	WORD $0xf2e1111c  // movk	x28, #2184, lsl #48
	WORD $0xb202e3e7  // mov	x7, #4919131752989213764
	WORD $0xf2e08887  // movk	x7, #1092, lsl #48
	WORD $0xb200e3e4  // mov	x4, #1229782938247303441
	WORD $0x17fffb64  // b	LBB0_72 $-4720(%rip)
LBB0_287:
	WORD $0xd2800014  // mov	x20, #0
	WORD $0x17fffff6  // b	LBB0_286 $-40(%rip)
LBB0_288:
	WORD $0x8b160268  // add	x8, x19, x22
	WORD $0xf10006bf  // cmp	x21, #1
	WORD $0x54000bca  // b.ge	LBB0_310 $376(%rip)
	WORD $0x1400006b  // b	LBB0_315 $428(%rip)
LBB0_289:
	WORD $0x3201f3e5  // mov	w5, #-1431655766
	WORD $0x1400001b  // b	LBB0_293 $108(%rip)
LBB0_290:
	WORD $0xdac003c8  // rbit	x8, lr
	WORD $0xdac01108  // clz	x8, x8
	WORD $0xcb13020e  // sub	x14, x16, x19
	WORD $0x8b0801c8  // add	x8, x14, x8
	WORD $0xb100075f  // cmn	x26, #1
	WORD $0x9a88135a  // csel	x26, x26, x8, ne
LBB0_291:
	WORD $0x0a3703c8  // bic	w8, w30, w23
	WORD $0x531f790e  // lsl	w14, w8, #1
	WORD $0x331f7917  // bfi	w23, w8, #1, #31
	WORD $0x0a2e03ce  // bic	w14, w30, w14
	WORD $0x1201f1ce  // and	w14, w14, #0xaaaaaaaa
	WORD $0x2b0801c8  // adds	w8, w14, w8
	WORD $0x4a0804a8  // eor	w8, w5, w8, lsl #1
	WORD $0x0a170108  // and	w8, w8, w23
	WORD $0x1a9f37f7  // cset	w23, hs
	WORD $0x2a2803e8  // mvn	w8, w8
	WORD $0x8a1b011b  // and	x27, x8, x27
	WORD $0x529fffe8  // mov	w8, #65535
	WORD $0xb202e3e7  // mov	x7, #4919131752989213764
	WORD $0xf2e08887  // movk	x7, #1092, lsl #48
	WORD $0xb200e3e4  // mov	x4, #1229782938247303441
	WORD $0x3200f3fe  // mov	w30, #1431655765
	WORD $0x3201f3e5  // mov	w5, #-1431655766
	WORD $0xb5ffb59b  // cbnz	x27, LBB0_208 $-2384(%rip)
LBB0_292:
	WORD $0x91008210  // add	x16, x16, #32
	WORD $0xaa1903fb  // mov	x27, x25
LBB0_293:
	WORD $0xb5000437  // cbnz	x23, LBB0_304 $132(%rip)
	WORD $0xb400039b  // cbz	x27, LBB0_302 $112(%rip)
LBB0_295:
	WORD $0xaa3303ee  // mvn	x14, x19
LBB0_296:
	WORD $0xaa1003fe  // mov	lr, x16
	WORD $0x384017d9  // ldrb	w25, [lr], #1
	WORD $0x71008b3f  // cmp	w25, #34
	WORD $0x54000300  // b.eq	LBB0_303 $96(%rip)
	WORD $0xd1000777  // sub	x23, x27, #1
	WORD $0x7101733f  // cmp	w25, #92
	WORD $0x540000a0  // b.eq	LBB0_299 $20(%rip)
	WORD $0xaa1e03f0  // mov	x16, lr
	WORD $0xaa1703fb  // mov	x27, x23
	WORD $0xb5fffef7  // cbnz	x23, LBB0_296 $-36(%rip)
	WORD $0x1400000e  // b	LBB0_301 $56(%rip)
LBB0_299:
	WORD $0xb4019457  // cbz	x23, LBB0_911 $12936(%rip)
	WORD $0x8b0e03c8  // add	x8, lr, x14
	WORD $0xb100075f  // cmn	x26, #1
	WORD $0x9a9a011a  // csel	x26, x8, x26, eq
	WORD $0x91000a10  // add	x16, x16, #2
	WORD $0xd1000b77  // sub	x23, x27, #2
	WORD $0x529fffe8  // mov	w8, #65535
	WORD $0x3201f3e5  // mov	w5, #-1431655766
	WORD $0xb202e3e7  // mov	x7, #4919131752989213764
	WORD $0xf2e08887  // movk	x7, #1092, lsl #48
	WORD $0xb200e3e4  // mov	x4, #1229782938247303441
	WORD $0xaa1703fb  // mov	x27, x23
	WORD $0xb5fffd37  // cbnz	x23, LBB0_296 $-92(%rip)
LBB0_301:
	WORD $0x71008b3f  // cmp	w25, #34
	WORD $0x54019281  // b.ne	LBB0_911 $12880(%rip)
LBB0_302:
	WORD $0xaa1003fe  // mov	lr, x16
LBB0_303:
	WORD $0xcb1303d7  // sub	x23, lr, x19
	WORD $0x3200f3fe  // mov	w30, #1431655765
	WORD $0x17fffafc  // b	LBB0_61 $-5136(%rip)
LBB0_304:
	WORD $0xb40191fb  // cbz	x27, LBB0_911 $12860(%rip)
	WORD $0xaa3303e8  // mvn	x8, x19
	WORD $0x8b080208  // add	x8, x16, x8
	WORD $0xb100075f  // cmn	x26, #1
	WORD $0x9a9a011a  // csel	x26, x8, x26, eq
	WORD $0x91000610  // add	x16, x16, #1
	WORD $0xd100077b  // sub	x27, x27, #1
	WORD $0x529fffe8  // mov	w8, #65535
	WORD $0xb202e3e7  // mov	x7, #4919131752989213764
	WORD $0xf2e08887  // movk	x7, #1092, lsl #48
	WORD $0xb200e3e4  // mov	x4, #1229782938247303441
	WORD $0xb5fffadb  // cbnz	x27, LBB0_295 $-168(%rip)
	WORD $0x17fffff0  // b	LBB0_302 $-64(%rip)
LBB0_306:
	WORD $0xf9400416  // ldr	x22, [x0, #8]
	WORD $0x1400001e  // b	LBB0_314 $120(%rip)
LBB0_307:
	WORD $0xd10006e8  // sub	x8, x23, #1
	WORD $0xeb15011f  // cmp	x8, x21
	WORD $0x54000380  // b.eq	LBB0_315 $112(%rip)
	WORD $0x8b140268  // add	x8, x19, x20
	WORD $0x8b150108  // add	x8, x8, x21
	WORD $0x91000908  // add	x8, x8, #2
	WORD $0xcb1502ee  // sub	x14, x23, x21
	WORD $0xd10009d5  // sub	x21, x14, #2
	WORD $0xf10006bf  // cmp	x21, #1
	WORD $0x540000ea  // b.ge	LBB0_310 $28(%rip)
	WORD $0x14000014  // b	LBB0_315 $80(%rip)
LBB0_309:
	WORD $0x92800022  // mov	x2, #-2
	WORD $0x52800054  // mov	w20, #2
	WORD $0x8b140108  // add	x8, x8, x20
	WORD $0xab0202b5  // adds	x21, x21, x2
	WORD $0x540001ed  // b.le	LBB0_315 $60(%rip)
LBB0_310:
	WORD $0x39400102  // ldrb	w2, [x8]
	WORD $0x7101705f  // cmp	w2, #92
	WORD $0x54ffff20  // b.eq	LBB0_309 $-28(%rip)
	WORD $0x7100885f  // cmp	w2, #34
	WORD $0x540000e0  // b.eq	LBB0_313 $28(%rip)
	WORD $0x92800002  // mov	x2, #-1
	WORD $0x52800034  // mov	w20, #1
	WORD $0x8b140108  // add	x8, x8, x20
	WORD $0xab0202b5  // adds	x21, x21, x2
	WORD $0x54fffeec  // b.gt	LBB0_310 $-36(%rip)
	WORD $0x14000004  // b	LBB0_315 $16(%rip)
LBB0_313:
	WORD $0xcb130108  // sub	x8, x8, x19
	WORD $0x91000516  // add	x22, x8, #1
LBB0_314:
	WORD $0xf9000036  // str	x22, [x1]
LBB0_315:
	WORD $0x529fffe8  // mov	w8, #65535
	WORD $0xb202e3e7  // mov	x7, #4919131752989213764
	WORD $0xf2e08887  // movk	x7, #1092, lsl #48
	WORD $0xb200e3e4  // mov	x4, #1229782938247303441
	WORD $0x17fffbf1  // b	LBB0_152 $-4156(%rip)
LBB0_316:
	WORD $0x71016e9f  // cmp	w20, #91
	WORD $0x54013ba1  // b.ne	LBB0_829 $10100(%rip)
	WORD $0xf9400522  // ldr	x2, [x9, #8]
	WORD $0xf9400054  // ldr	x20, [x2]
	WORD $0xb7f92d54  // tbnz	x20, #63, LBB0_801 $9640(%rip)
	WORD $0xf9400416  // ldr	x22, [x0, #8]
	WORD $0xeb1602ff  // cmp	x23, x22
	WORD $0x54000162  // b.hs	LBB0_323 $44(%rip)
	WORD $0x38776a62  // ldrb	w2, [x19, x23]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x54000100  // b.eq	LBB0_323 $32(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x540000c0  // b.eq	LBB0_323 $24(%rip)
	WORD $0x51002c4e  // sub	w14, w2, #11
	WORD $0x310009df  // cmn	w14, #2
	WORD $0x54000062  // b.hs	LBB0_323 $12(%rip)
	WORD $0xaa1703f5  // mov	x21, x23
	WORD $0x14000033  // b	LBB0_340 $204(%rip)
LBB0_323:
	WORD $0x910006f5  // add	x21, x23, #1
	WORD $0xeb1602bf  // cmp	x21, x22
	WORD $0x54000122  // b.hs	LBB0_327 $36(%rip)
	WORD $0x38756a62  // ldrb	w2, [x19, x21]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x540000c0  // b.eq	LBB0_327 $24(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x54000080  // b.eq	LBB0_327 $16(%rip)
	WORD $0x51002c4e  // sub	w14, w2, #11
	WORD $0x310009df  // cmn	w14, #2
	WORD $0x54000503  // b.lo	LBB0_340 $160(%rip)
LBB0_327:
	WORD $0x91000af5  // add	x21, x23, #2
	WORD $0xeb1602bf  // cmp	x21, x22
	WORD $0x54000122  // b.hs	LBB0_331 $36(%rip)
	WORD $0x38756a62  // ldrb	w2, [x19, x21]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x540000c0  // b.eq	LBB0_331 $24(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x54000080  // b.eq	LBB0_331 $16(%rip)
	WORD $0x51002c4e  // sub	w14, w2, #11
	WORD $0x310009df  // cmn	w14, #2
	WORD $0x540003a3  // b.lo	LBB0_340 $116(%rip)
LBB0_331:
	WORD $0x91000ef5  // add	x21, x23, #3
	WORD $0xeb1602bf  // cmp	x21, x22
	WORD $0x54000122  // b.hs	LBB0_335 $36(%rip)
	WORD $0x38756a62  // ldrb	w2, [x19, x21]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x540000c0  // b.eq	LBB0_335 $24(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x54000080  // b.eq	LBB0_335 $16(%rip)
	WORD $0x51002c4e  // sub	w14, w2, #11
	WORD $0x310009df  // cmn	w14, #2
	WORD $0x54000243  // b.lo	LBB0_340 $72(%rip)
LBB0_335:
	WORD $0x910012f5  // add	x21, x23, #4
	WORD $0xeb1602bf  // cmp	x21, x22
	WORD $0x54000162  // b.hs	LBB0_338 $44(%rip)
LBB0_336:
	WORD $0x38756a6e  // ldrb	w14, [x19, x21]
	WORD $0x710081df  // cmp	w14, #32
	WORD $0x9ace218e  // lsl	x14, x12, x14
	WORD $0x8a0d01ce  // and	x14, x14, x13
	WORD $0xfa4099c4  // ccmp	x14, #0, #4, ls
	WORD $0x540000e0  // b.eq	LBB0_339 $28(%rip)
	WORD $0x910006b5  // add	x21, x21, #1
	WORD $0xeb1502df  // cmp	x22, x21
	WORD $0x54ffff01  // b.ne	LBB0_336 $-32(%rip)
	WORD $0x1400000a  // b	LBB0_341 $40(%rip)
LBB0_338:
	WORD $0xaa1503f7  // mov	x23, x21
	WORD $0x14000008  // b	LBB0_341 $32(%rip)
LBB0_339:
	WORD $0xeb1602bf  // cmp	x21, x22
	WORD $0x540000c2  // b.hs	LBB0_341 $24(%rip)
LBB0_340:
	WORD $0x910006b7  // add	x23, x21, #1
	WORD $0xf9000037  // str	x23, [x1]
	WORD $0x38756a6e  // ldrb	w14, [x19, x21]
	WORD $0x710175df  // cmp	w14, #93
	WORD $0x540125e0  // b.eq	LBB0_804 $9404(%rip)
LBB0_341:
	WORD $0xd10006f6  // sub	x22, x23, #1
	WORD $0xf9000036  // str	x22, [x1]
LBB0_342:
	WORD $0xf1000694  // subs	x20, x20, #1
	WORD $0x5400712b  // b.lt	LBB0_491 $3620(%rip)
	WORD $0xf9400417  // ldr	x23, [x0, #8]
	WORD $0xeb1702df  // cmp	x22, x23
	WORD $0x54000162  // b.hs	LBB0_348 $44(%rip)
	WORD $0x38766a62  // ldrb	w2, [x19, x22]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x54000100  // b.eq	LBB0_348 $32(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x540000c0  // b.eq	LBB0_348 $24(%rip)
	WORD $0x51002c4e  // sub	w14, w2, #11
	WORD $0x310009df  // cmn	w14, #2
	WORD $0x54000062  // b.hs	LBB0_348 $12(%rip)
	WORD $0xaa1603f5  // mov	x21, x22
	WORD $0x14000031  // b	LBB0_364 $196(%rip)
LBB0_348:
	WORD $0x910006d5  // add	x21, x22, #1
	WORD $0xeb1702bf  // cmp	x21, x23
	WORD $0x54000122  // b.hs	LBB0_352 $36(%rip)
	WORD $0x38756a62  // ldrb	w2, [x19, x21]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x540000c0  // b.eq	LBB0_352 $24(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x54000080  // b.eq	LBB0_352 $16(%rip)
	WORD $0x51002c4e  // sub	w14, w2, #11
	WORD $0x310009df  // cmn	w14, #2
	WORD $0x540004c3  // b.lo	LBB0_364 $152(%rip)
LBB0_352:
	WORD $0x91000ad5  // add	x21, x22, #2
	WORD $0xeb1702bf  // cmp	x21, x23
	WORD $0x54000122  // b.hs	LBB0_356 $36(%rip)
	WORD $0x38756a62  // ldrb	w2, [x19, x21]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x540000c0  // b.eq	LBB0_356 $24(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x54000080  // b.eq	LBB0_356 $16(%rip)
	WORD $0x51002c4e  // sub	w14, w2, #11
	WORD $0x310009df  // cmn	w14, #2
	WORD $0x54000363  // b.lo	LBB0_364 $108(%rip)
LBB0_356:
	WORD $0x91000ed5  // add	x21, x22, #3
	WORD $0xeb1702bf  // cmp	x21, x23
	WORD $0x54000122  // b.hs	LBB0_360 $36(%rip)
	WORD $0x38756a62  // ldrb	w2, [x19, x21]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x540000c0  // b.eq	LBB0_360 $24(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x54000080  // b.eq	LBB0_360 $16(%rip)
	WORD $0x51002c4e  // sub	w14, w2, #11
	WORD $0x310009df  // cmn	w14, #2
	WORD $0x54000203  // b.lo	LBB0_364 $64(%rip)
LBB0_360:
	WORD $0x910012d5  // add	x21, x22, #4
	WORD $0xeb1702bf  // cmp	x21, x23
	WORD $0x54000ca2  // b.hs	LBB0_390 $404(%rip)
LBB0_361:
	WORD $0x38756a6e  // ldrb	w14, [x19, x21]
	WORD $0x710081df  // cmp	w14, #32
	WORD $0x9ace218e  // lsl	x14, x12, x14
	WORD $0x8a0d01ce  // and	x14, x14, x13
	WORD $0xfa4099c4  // ccmp	x14, #0, #4, ls
	WORD $0x540000a0  // b.eq	LBB0_363 $20(%rip)
	WORD $0x910006b5  // add	x21, x21, #1
	WORD $0xeb1502ff  // cmp	x23, x21
	WORD $0x54ffff01  // b.ne	LBB0_361 $-32(%rip)
	WORD $0x1400005d  // b	LBB0_391 $372(%rip)
LBB0_363:
	WORD $0xeb1702bf  // cmp	x21, x23
	WORD $0x54000b62  // b.hs	LBB0_391 $364(%rip)
LBB0_364:
	WORD $0x910006b6  // add	x22, x21, #1
	WORD $0xf9000036  // str	x22, [x1]
	WORD $0x38756a77  // ldrb	w23, [x19, x21]
	WORD $0x71016aff  // cmp	w23, #90
	WORD $0x5400070c  // b.gt	LBB0_379 $224(%rip)
	WORD $0x7100beff  // cmp	w23, #47
	WORD $0x540008cd  // b.le	LBB0_384 $280(%rip)
	WORD $0x5100c2ee  // sub	w14, w23, #48
	WORD $0x710029df  // cmp	w14, #10
	WORD $0x540009e2  // b.hs	LBB0_390 $316(%rip)
LBB0_367:
	WORD $0xf940040e  // ldr	x14, [x0, #8]
	WORD $0xcb1601d5  // sub	x21, x14, x22
	WORD $0xf10042bf  // cmp	x21, #16
	WORD $0x540001e3  // b.lo	LBB0_370 $60(%rip)
LBB0_368:
	WORD $0x3cf66a73  // ldr	q19, [x19, x22]
	WORD $0x6e248e74  // cmeq.16b	v20, v19, v4
	WORD $0x4e251e73  // and.16b	v19, v19, v5
	WORD $0x6e268e73  // cmeq.16b	v19, v19, v6
	WORD $0x4eb41e73  // orr.16b	v19, v19, v20
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260262  // fmov	w2, s19
	WORD $0x35000402  // cbnz	w2, LBB0_378 $128(%rip)
	WORD $0xd10042b5  // sub	x21, x21, #16
	WORD $0x910042d6  // add	x22, x22, #16
	WORD $0xf1003ebf  // cmp	x21, #15
	WORD $0x54fffe68  // b.hi	LBB0_368 $-52(%rip)
LBB0_370:
	WORD $0x8b160277  // add	x23, x19, x22
	WORD $0xb40001f5  // cbz	x21, LBB0_377 $60(%rip)
	WORD $0x8b1502f8  // add	x24, x23, x21
	WORD $0xcb1302f6  // sub	x22, x23, x19
LBB0_372:
	WORD $0x394002e2  // ldrb	w2, [x23]
	WORD $0x7100b05f  // cmp	w2, #44
	WORD $0x54005ce0  // b.eq	LBB0_479 $2972(%rip)
	WORD $0x7101f45f  // cmp	w2, #125
	WORD $0x54005ca0  // b.eq	LBB0_479 $2964(%rip)
	WORD $0x7101745f  // cmp	w2, #93
	WORD $0x54005c60  // b.eq	LBB0_479 $2956(%rip)
	WORD $0x910006f7  // add	x23, x23, #1
	WORD $0x910006d6  // add	x22, x22, #1
	WORD $0xf10006b5  // subs	x21, x21, #1
	WORD $0x54fffec1  // b.ne	LBB0_372 $-40(%rip)
	WORD $0xaa1803f7  // mov	x23, x24
LBB0_377:
	WORD $0xcb1302f6  // sub	x22, x23, x19
	WORD $0xf9000036  // str	x22, [x1]
	WORD $0x529fffe8  // mov	w8, #65535
	WORD $0x3200f3fe  // mov	w30, #1431655765
	WORD $0x3201f3e5  // mov	w5, #-1431655766
	WORD $0xb201e3fc  // mov	x28, #-8608480567731124088
	WORD $0xf2e1111c  // movk	x28, #2184, lsl #48
	WORD $0xb202e3e7  // mov	x7, #4919131752989213764
	WORD $0xf2e08887  // movk	x7, #1092, lsl #48
	WORD $0xb200e3e4  // mov	x4, #1229782938247303441
	WORD $0x14000024  // b	LBB0_391 $144(%rip)
LBB0_378:
	WORD $0x5ac0004e  // rbit	w14, w2
	WORD $0x5ac011ce  // clz	w14, w14
	WORD $0x8b1601d6  // add	x22, x14, x22
	WORD $0xf9000036  // str	x22, [x1]
	WORD $0x1400001f  // b	LBB0_391 $124(%rip)
LBB0_379:
	WORD $0x7101b6ff  // cmp	w23, #109
	WORD $0x540002ad  // b.le	LBB0_387 $84(%rip)
	WORD $0x7101baff  // cmp	w23, #110
	WORD $0x54002bc0  // b.eq	LBB0_438 $1400(%rip)
	WORD $0x7101d2ff  // cmp	w23, #116
	WORD $0x54002b80  // b.eq	LBB0_438 $1392(%rip)
	WORD $0x7101eeff  // cmp	w23, #123
	WORD $0x540002a1  // b.ne	LBB0_390 $84(%rip)
	WORD $0xd280001a  // mov	x26, #0
	WORD $0xd2800018  // mov	x24, #0
	WORD $0xd2800015  // mov	x21, #0
	WORD $0xd2800017  // mov	x23, #0
	WORD $0xf940040e  // ldr	x14, [x0, #8]
	WORD $0xcb1601cf  // sub	x15, x14, x22
	WORD $0x8b160273  // add	x19, x19, x22
	WORD $0x1400005c  // b	LBB0_414 $368(%rip)
LBB0_384:
	WORD $0x340001d7  // cbz	w23, LBB0_391 $56(%rip)
	WORD $0x71008aff  // cmp	w23, #34
	WORD $0x54002ac0  // b.eq	LBB0_441 $1368(%rip)
	WORD $0x7100b6ff  // cmp	w23, #45
	WORD $0x54fff740  // b.eq	LBB0_367 $-280(%rip)
	WORD $0x14000007  // b	LBB0_390 $28(%rip)
LBB0_387:
	WORD $0x71016eff  // cmp	w23, #91
	WORD $0x54003260  // b.eq	LBB0_451 $1612(%rip)
	WORD $0x71019aff  // cmp	w23, #102
	WORD $0x54000061  // b.ne	LBB0_390 $12(%rip)
	WORD $0x910016a2  // add	x2, x21, #5
	WORD $0x14000147  // b	LBB0_439 $1308(%rip)
LBB0_390:
	WORD $0xf9000035  // str	x21, [x1]
	WORD $0xaa1503f6  // mov	x22, x21
LBB0_391:
	WORD $0xa9405c13  // ldp	x19, x23, [x0]
	WORD $0xeb1702df  // cmp	x22, x23
	WORD $0x54000162  // b.hs	LBB0_396 $44(%rip)
	WORD $0x38766a62  // ldrb	w2, [x19, x22]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x54000100  // b.eq	LBB0_396 $32(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x540000c0  // b.eq	LBB0_396 $24(%rip)
	WORD $0x51002c4e  // sub	w14, w2, #11
	WORD $0x310009df  // cmn	w14, #2
	WORD $0x54000062  // b.hs	LBB0_396 $12(%rip)
	WORD $0xaa1603f5  // mov	x21, x22
	WORD $0x14000031  // b	LBB0_412 $196(%rip)
LBB0_396:
	WORD $0x910006d5  // add	x21, x22, #1
	WORD $0xeb1702bf  // cmp	x21, x23
	WORD $0x54000122  // b.hs	LBB0_400 $36(%rip)
	WORD $0x38756a62  // ldrb	w2, [x19, x21]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x540000c0  // b.eq	LBB0_400 $24(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x54000080  // b.eq	LBB0_400 $16(%rip)
	WORD $0x51002c4e  // sub	w14, w2, #11
	WORD $0x310009df  // cmn	w14, #2
	WORD $0x540004c3  // b.lo	LBB0_412 $152(%rip)
LBB0_400:
	WORD $0x91000ad5  // add	x21, x22, #2
	WORD $0xeb1702bf  // cmp	x21, x23
	WORD $0x54000122  // b.hs	LBB0_404 $36(%rip)
	WORD $0x38756a62  // ldrb	w2, [x19, x21]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x540000c0  // b.eq	LBB0_404 $24(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x54000080  // b.eq	LBB0_404 $16(%rip)
	WORD $0x51002c4e  // sub	w14, w2, #11
	WORD $0x310009df  // cmn	w14, #2
	WORD $0x54000363  // b.lo	LBB0_412 $108(%rip)
LBB0_404:
	WORD $0x91000ed5  // add	x21, x22, #3
	WORD $0xeb1702bf  // cmp	x21, x23
	WORD $0x54000122  // b.hs	LBB0_408 $36(%rip)
	WORD $0x38756a62  // ldrb	w2, [x19, x21]
	WORD $0x7100345f  // cmp	w2, #13
	WORD $0x540000c0  // b.eq	LBB0_408 $24(%rip)
	WORD $0x7100805f  // cmp	w2, #32
	WORD $0x54000080  // b.eq	LBB0_408 $16(%rip)
	WORD $0x51002c4e  // sub	w14, w2, #11
	WORD $0x310009df  // cmn	w14, #2
	WORD $0x54000203  // b.lo	LBB0_412 $64(%rip)
LBB0_408:
	WORD $0x910012d5  // add	x21, x22, #4
	WORD $0xeb1702bf  // cmp	x21, x23
	WORD $0x54010be2  // b.hs	LBB0_802 $8572(%rip)
LBB0_409:
	WORD $0x38756a6e  // ldrb	w14, [x19, x21]
	WORD $0x710081df  // cmp	w14, #32
	WORD $0x9ace218e  // lsl	x14, x12, x14
	WORD $0x8a0d01ce  // and	x14, x14, x13
	WORD $0xfa4099c4  // ccmp	x14, #0, #4, ls
	WORD $0x540000a0  // b.eq	LBB0_411 $20(%rip)
	WORD $0x910006b5  // add	x21, x21, #1
	WORD $0xeb1502ff  // cmp	x23, x21
	WORD $0x54ffff01  // b.ne	LBB0_409 $-32(%rip)
	WORD $0x140008c0  // b	LBB0_829 $8960(%rip)
LBB0_411:
	WORD $0xeb1702bf  // cmp	x21, x23
	WORD $0x540117c2  // b.hs	LBB0_829 $8952(%rip)
LBB0_412:
	WORD $0x910006b6  // add	x22, x21, #1
	WORD $0xf9000036  // str	x22, [x1]
	WORD $0x38756a62  // ldrb	w2, [x19, x21]
	WORD $0x7100b05f  // cmp	w2, #44
	WORD $0x54ffe4c0  // b.eq	LBB0_342 $-872(%rip)
	WORD $0x1400084f  // b	LBB0_803 $8508(%rip)
LBB0_413:
	WORD $0x937fff5a  // asr	x26, x26, #63
	WORD $0x9e670373  // fmov	d19, x27
	WORD $0x0e205a73  // cnt.8b	v19, v19
	WORD $0x2e303a73  // uaddlv.8b	h19, v19
	WORD $0x1e26026e  // fmov	w14, s19
	WORD $0x8b1501d5  // add	x21, x14, x21
	WORD $0x91010273  // add	x19, x19, #64
	WORD $0xf9400fef  // ldr	x15, [sp, #24]
	WORD $0xb201e3fc  // mov	x28, #-8608480567731124088
	WORD $0xf2e1111c  // movk	x28, #2184, lsl #48
LBB0_414:
	WORD $0xf10101ee  // subs	x14, x15, #64
	WORD $0xa9013bef  // stp	x15, x14, [sp, #16]
	WORD $0x5400176b  // b.lt	LBB0_421 $748(%rip)
LBB0_415:
	WORD $0xad405676  // ldp	q22, q21, [x19]
	WORD $0xad414e74  // ldp	q20, q19, [x19, #32]
	WORD $0x6e238ed7  // cmeq.16b	v23, v22, v3
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602ee  // fmov	w14, s23
	WORD $0x6e238eb7  // cmeq.16b	v23, v21, v3
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602ef  // fmov	w15, s23
	WORD $0x6e238e97  // cmeq.16b	v23, v20, v3
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f0  // fmov	w16, s23
	WORD $0x6e238e77  // cmeq.16b	v23, v19, v3
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f1  // fmov	w17, s23
	WORD $0xd3607e10  // lsl	x16, x16, #32
	WORD $0xaa11c210  // orr	x16, x16, x17, lsl #48
	WORD $0x53103def  // lsl	w15, w15, #16
	WORD $0xaa0f020f  // orr	x15, x16, x15
	WORD $0xaa0e01e2  // orr	x2, x15, x14
	WORD $0xaa18004e  // orr	x14, x2, x24
	WORD $0xb500008e  // cbnz	x14, LBB0_417 $16(%rip)
	WORD $0xd2800018  // mov	x24, #0
	WORD $0xd280001b  // mov	x27, #0
	WORD $0x1400000a  // b	LBB0_418 $40(%rip)
LBB0_417:
	WORD $0x8a38004e  // bic	x14, x2, x24
	WORD $0xaa0e070f  // orr	x15, x24, x14, lsl #1
	WORD $0x8a2f0050  // bic	x16, x2, x15
	WORD $0x9201f210  // and	x16, x16, #0xaaaaaaaaaaaaaaaa
	WORD $0xab0e020e  // adds	x14, x16, x14
	WORD $0x1a9f37f8  // cset	w24, hs
	WORD $0xd37ff9ce  // lsl	x14, x14, #1
	WORD $0xd200f1ce  // eor	x14, x14, #0x5555555555555555
	WORD $0x8a0f01db  // and	x27, x14, x15
LBB0_418:
	WORD $0x6e208ed7  // cmeq.16b	v23, v22, v0
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602ee  // fmov	w14, s23
	WORD $0x6e208eb7  // cmeq.16b	v23, v21, v0
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602ef  // fmov	w15, s23
	WORD $0x6e208e97  // cmeq.16b	v23, v20, v0
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f0  // fmov	w16, s23
	WORD $0x6e208e77  // cmeq.16b	v23, v19, v0
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f1  // fmov	w17, s23
	WORD $0xd3607e10  // lsl	x16, x16, #32
	WORD $0xaa11c210  // orr	x16, x16, x17, lsl #48
	WORD $0x53103def  // lsl	w15, w15, #16
	WORD $0xaa0f020f  // orr	x15, x16, x15
	WORD $0xaa0e01ee  // orr	x14, x15, x14
	WORD $0x8a3b01ce  // bic	x14, x14, x27
	WORD $0x9200e1cf  // and	x15, x14, #0x1111111111111111
	WORD $0x9203e1d0  // and	x16, x14, #0x2222222222222222
	WORD $0x9202e1d1  // and	x17, x14, #0x4444444444444444
	WORD $0x9201e1ce  // and	x14, x14, #0x8888888888888888
	WORD $0x9b047de2  // mul	x2, x15, x4
	WORD $0x9b1c7e06  // mul	x6, x16, x28
	WORD $0xca060042  // eor	x2, x2, x6
	WORD $0x9b077e26  // mul	x6, x17, x7
	WORD $0xb202e3fb  // mov	x27, #4919131752989213764
	WORD $0xf2e0889b  // movk	x27, #1092, lsl #48
	WORD $0xb203e3e7  // mov	x7, #2459565876494606882
	WORD $0xf2e04447  // movk	x7, #546, lsl #48
	WORD $0x9b077dc7  // mul	x7, x14, x7
	WORD $0xca0700c6  // eor	x6, x6, x7
	WORD $0xca060042  // eor	x2, x2, x6
	WORD $0xb200e3f6  // mov	x22, #1229782938247303441
	WORD $0xb203e3e4  // mov	x4, #2459565876494606882
	WORD $0x9b047de6  // mul	x6, x15, x4
	WORD $0x9b167e07  // mul	x7, x16, x22
	WORD $0xca0700c6  // eor	x6, x6, x7
	WORD $0x9b1c7e27  // mul	x7, x17, x28
	WORD $0x9b1b7ddb  // mul	x27, x14, x27
	WORD $0xca1b00e7  // eor	x7, x7, x27
	WORD $0xca0700c6  // eor	x6, x6, x7
	WORD $0xb202e3f9  // mov	x25, #4919131752989213764
	WORD $0x9b197de7  // mul	x7, x15, x25
	WORD $0x9b047e1b  // mul	x27, x16, x4
	WORD $0xca1b00e7  // eor	x7, x7, x27
	WORD $0x9b167e3b  // mul	x27, x17, x22
	WORD $0xb201e3fc  // mov	x28, #-8608480567731124088
	WORD $0xf2e1111c  // movk	x28, #2184, lsl #48
	WORD $0x9b1c7ddc  // mul	x28, x14, x28
	WORD $0xca1c037b  // eor	x27, x27, x28
	WORD $0xca1b00e7  // eor	x7, x7, x27
	WORD $0xb201e3f6  // mov	x22, #-8608480567731124088
	WORD $0x9b167def  // mul	x15, x15, x22
	WORD $0x9b197e10  // mul	x16, x16, x25
	WORD $0xca1001ef  // eor	x15, x15, x16
	WORD $0x9b047e30  // mul	x16, x17, x4
	WORD $0xb200e3e4  // mov	x4, #1229782938247303441
	WORD $0x9b047dce  // mul	x14, x14, x4
	WORD $0xca0e020e  // eor	x14, x16, x14
	WORD $0xca0e01ee  // eor	x14, x15, x14
	WORD $0x9200e04f  // and	x15, x2, #0x1111111111111111
	WORD $0x9203e0d0  // and	x16, x6, #0x2222222222222222
	WORD $0x9202e0f1  // and	x17, x7, #0x4444444444444444
	WORD $0x9201e1ce  // and	x14, x14, #0x8888888888888888
	WORD $0xaa1001ef  // orr	x15, x15, x16
	WORD $0xaa0e022e  // orr	x14, x17, x14
	WORD $0xaa0e01ee  // orr	x14, x15, x14
	WORD $0xca1a01da  // eor	x26, x14, x26
	WORD $0x6e318ed7  // cmeq.16b	v23, v22, v17
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602ee  // fmov	w14, s23
	WORD $0x6e318eb7  // cmeq.16b	v23, v21, v17
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602ef  // fmov	w15, s23
	WORD $0x6e318e97  // cmeq.16b	v23, v20, v17
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f0  // fmov	w16, s23
	WORD $0x6e318e77  // cmeq.16b	v23, v19, v17
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f1  // fmov	w17, s23
	WORD $0xd3607e10  // lsl	x16, x16, #32
	WORD $0xaa11c210  // orr	x16, x16, x17, lsl #48
	WORD $0x53103def  // lsl	w15, w15, #16
	WORD $0xaa0f020f  // orr	x15, x16, x15
	WORD $0xaa0e01ee  // orr	x14, x15, x14
	WORD $0x8a3a01db  // bic	x27, x14, x26
	WORD $0x6e328ed6  // cmeq.16b	v22, v22, v18
	WORD $0x4e211ed6  // and.16b	v22, v22, v1
	WORD $0x4e0202d6  // tbl.16b	v22, { v22 }, v2
	WORD $0x4e71bad6  // addv.8h	h22, v22
	WORD $0x1e2602ce  // fmov	w14, s22
	WORD $0x6e328eb5  // cmeq.16b	v21, v21, v18
	WORD $0x4e211eb5  // and.16b	v21, v21, v1
	WORD $0x4e0202b5  // tbl.16b	v21, { v21 }, v2
	WORD $0x4e71bab5  // addv.8h	h21, v21
	WORD $0x1e2602af  // fmov	w15, s21
	WORD $0x6e328e94  // cmeq.16b	v20, v20, v18
	WORD $0x4e211e94  // and.16b	v20, v20, v1
	WORD $0x4e020294  // tbl.16b	v20, { v20 }, v2
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260290  // fmov	w16, s20
	WORD $0x6e328e73  // cmeq.16b	v19, v19, v18
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260271  // fmov	w17, s19
	WORD $0xd3607e10  // lsl	x16, x16, #32
	WORD $0xaa11c210  // orr	x16, x16, x17, lsl #48
	WORD $0x53103def  // lsl	w15, w15, #16
	WORD $0xaa0f020f  // orr	x15, x16, x15
	WORD $0xaa0e01ee  // orr	x14, x15, x14
	WORD $0xb202e3e7  // mov	x7, #4919131752989213764
	WORD $0xf2e08887  // movk	x7, #1092, lsl #48
	WORD $0xea3a01dc  // bics	x28, x14, x26
	WORD $0x54ffe8e0  // b.eq	LBB0_413 $-740(%rip)
LBB0_419:
	WORD $0xd1000782  // sub	x2, x28, #1
	WORD $0x8a1b004e  // and	x14, x2, x27
	WORD $0x9e6701d3  // fmov	d19, x14
	WORD $0x0e205a73  // cnt.8b	v19, v19
	WORD $0x2e303a73  // uaddlv.8b	h19, v19
	WORD $0x1e26026e  // fmov	w14, s19
	WORD $0x8b1501ce  // add	x14, x14, x21
	WORD $0xeb1701df  // cmp	x14, x23
	WORD $0x540031e9  // b.ls	LBB0_477 $1596(%rip)
	WORD $0x910006f7  // add	x23, x23, #1
	WORD $0xea1c005c  // ands	x28, x2, x28
	WORD $0x54fffea1  // b.ne	LBB0_419 $-44(%rip)
	WORD $0x17ffff3a  // b	LBB0_413 $-792(%rip)
LBB0_421:
	WORD $0xf10001ff  // cmp	x15, #0
	WORD $0x540037cd  // b.le	LBB0_487 $1784(%rip)
	WORD $0xad0243f0  // stp	q16, q16, [sp, #64]
	WORD $0xad0143f0  // stp	q16, q16, [sp, #32]
	WORD $0x92402e68  // and	x8, x19, #0xfff
	WORD $0xf13f051f  // cmp	x8, #4033
	WORD $0x54000383  // b.lo	LBB0_433 $112(%rip)
	WORD $0xf9400be8  // ldr	x8, [sp, #16]
	WORD $0xf100811b  // subs	x27, x8, #32
	WORD $0x540000a3  // b.lo	LBB0_425 $20(%rip)
	WORD $0xacc15273  // ldp	q19, q20, [x19], #32
	WORD $0xad0153f3  // stp	q19, q20, [sp, #32]
	WORD $0xaa0b03e8  // mov	x8, x11
	WORD $0x14000003  // b	LBB0_426 $12(%rip)
LBB0_425:
	WORD $0x910083e8  // add	x8, sp, #32
	WORD $0xf9400bfb  // ldr	x27, [sp, #16]
LBB0_426:
	WORD $0xf1004362  // subs	x2, x27, #16
	WORD $0x54000343  // b.lo	LBB0_434 $104(%rip)
	WORD $0x3cc10673  // ldr	q19, [x19], #16
	WORD $0x3c810513  // str	q19, [x8], #16
	WORD $0xaa0203fb  // mov	x27, x2
	WORD $0xf1002042  // subs	x2, x2, #8
	WORD $0x540002e2  // b.hs	LBB0_435 $92(%rip)
LBB0_428:
	WORD $0xf1001362  // subs	x2, x27, #4
	WORD $0x54000343  // b.lo	LBB0_436 $104(%rip)
LBB0_429:
	WORD $0xb840466e  // ldr	w14, [x19], #4
	WORD $0xb800450e  // str	w14, [x8], #4
	WORD $0xaa0203fb  // mov	x27, x2
	WORD $0xf1000842  // subs	x2, x2, #2
	WORD $0x540002e2  // b.hs	LBB0_437 $92(%rip)
LBB0_430:
	WORD $0xb400007b  // cbz	x27, LBB0_432 $12(%rip)
LBB0_431:
	WORD $0x3940026e  // ldrb	w14, [x19]
	WORD $0x3900010e  // strb	w14, [x8]
LBB0_432:
	WORD $0x910083f3  // add	x19, sp, #32
LBB0_433:
	WORD $0x529fffe8  // mov	w8, #65535
	WORD $0x3200f3fe  // mov	w30, #1431655765
	WORD $0x3201f3e5  // mov	w5, #-1431655766
	WORD $0xb201e3fc  // mov	x28, #-8608480567731124088
	WORD $0xf2e1111c  // movk	x28, #2184, lsl #48
	WORD $0xb202e3e7  // mov	x7, #4919131752989213764
	WORD $0xf2e08887  // movk	x7, #1092, lsl #48
	WORD $0xb200e3e4  // mov	x4, #1229782938247303441
	WORD $0x17ffff1c  // b	LBB0_415 $-912(%rip)
LBB0_434:
	WORD $0xf1002362  // subs	x2, x27, #8
	WORD $0x54fffd63  // b.lo	LBB0_428 $-84(%rip)
LBB0_435:
	WORD $0xf840866e  // ldr	x14, [x19], #8
	WORD $0xf800850e  // str	x14, [x8], #8
	WORD $0xaa0203fb  // mov	x27, x2
	WORD $0xf1001042  // subs	x2, x2, #4
	WORD $0x54fffd02  // b.hs	LBB0_429 $-96(%rip)
LBB0_436:
	WORD $0xf1000b62  // subs	x2, x27, #2
	WORD $0x54fffd63  // b.lo	LBB0_430 $-84(%rip)
LBB0_437:
	WORD $0x7840266e  // ldrh	w14, [x19], #2
	WORD $0x7800250e  // strh	w14, [x8], #2
	WORD $0xaa0203fb  // mov	x27, x2
	WORD $0xb5fffd02  // cbnz	x2, LBB0_431 $-96(%rip)
	WORD $0x17ffffe9  // b	LBB0_432 $-92(%rip)
LBB0_438:
	WORD $0x910012a2  // add	x2, x21, #4
LBB0_439:
	WORD $0xf940040e  // ldr	x14, [x0, #8]
	WORD $0xeb0e005f  // cmp	x2, x14
	WORD $0x54ffd748  // b.hi	LBB0_391 $-1304(%rip)
	WORD $0xf9000022  // str	x2, [x1]
	WORD $0xaa0203f6  // mov	x22, x2
	WORD $0x17fffeb7  // b	LBB0_391 $-1316(%rip)
LBB0_441:
	WORD $0xf9400402  // ldr	x2, [x0, #8]
	WORD $0xcb160057  // sub	x23, x2, x22
	WORD $0xf10082ff  // cmp	x23, #32
	WORD $0x54002c8b  // b.lt	LBB0_480 $1424(%rip)
	WORD $0xaa1003f1  // mov	x17, x16
	WORD $0xd2800017  // mov	x23, #0
	WORD $0xd280001b  // mov	x27, #0
	WORD $0x8b150279  // add	x25, x19, x21
	WORD $0xcb150058  // sub	x24, x2, x21
	WORD $0x528003fa  // mov	w26, #31
LBB0_443:
	WORD $0x8b17032e  // add	x14, x25, x23
	WORD $0x3cc011d3  // ldur	q19, [x14, #1]
	WORD $0x3cc111d4  // ldur	q20, [x14, #17]
	WORD $0x6e208e75  // cmeq.16b	v21, v19, v0
	WORD $0x4e211eb5  // and.16b	v21, v21, v1
	WORD $0x4e0202b5  // tbl.16b	v21, { v21 }, v2
	WORD $0x4e71bab5  // addv.8h	h21, v21
	WORD $0x1e2602bc  // fmov	w28, s21
	WORD $0x6e208e95  // cmeq.16b	v21, v20, v0
	WORD $0x4e211eb5  // and.16b	v21, v21, v1
	WORD $0x4e0202b5  // tbl.16b	v21, { v21 }, v2
	WORD $0x4e71bab5  // addv.8h	h21, v21
	WORD $0x1e2602ae  // fmov	w14, s21
	WORD $0x33103ddc  // bfi	w28, w14, #16, #16
	WORD $0x6e238e73  // cmeq.16b	v19, v19, v3
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260262  // fmov	w2, s19
	WORD $0x6e238e93  // cmeq.16b	v19, v20, v3
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e26026e  // fmov	w14, s19
	WORD $0x33103dc2  // bfi	w2, w14, #16, #16
	WORD $0x7100005f  // cmp	w2, #0
	WORD $0xfa400b60  // ccmp	x27, #0, #0, eq
	WORD $0x54000180  // b.eq	LBB0_445 $48(%rip)
	WORD $0x0a3b004e  // bic	w14, w2, w27
	WORD $0x2a0e076f  // orr	w15, w27, w14, lsl #1
	WORD $0x0a050050  // and	w16, w2, w5
	WORD $0x0a2f0210  // bic	w16, w16, w15
	WORD $0x2b0e020e  // adds	w14, w16, w14
	WORD $0x1a9f37fb  // cset	w27, hs
	WORD $0x4a0e07ce  // eor	w14, w30, w14, lsl #1
	WORD $0x0a0f01ce  // and	w14, w14, w15
	WORD $0x2a2e03ee  // mvn	w14, w14
	WORD $0x8a1c01dc  // and	x28, x14, x28
	WORD $0x14000002  // b	LBB0_446 $8(%rip)
LBB0_445:
	WORD $0xd280001b  // mov	x27, #0
LBB0_446:
	WORD $0xb500253c  // cbnz	x28, LBB0_478 $1188(%rip)
	WORD $0x910082f7  // add	x23, x23, #32
	WORD $0xd100835a  // sub	x26, x26, #32
	WORD $0x8b1a030e  // add	x14, x24, x26
	WORD $0xf100fddf  // cmp	x14, #63
	WORD $0x54fffa6c  // b.gt	LBB0_443 $-180(%rip)
	WORD $0xb5002a9b  // cbnz	x27, LBB0_488 $1360(%rip)
	WORD $0x8b15026e  // add	x14, x19, x21
	WORD $0x8b1701ce  // add	x14, x14, x23
	WORD $0x910005d5  // add	x21, x14, #1
	WORD $0xaa3703ee  // mvn	x14, x23
	WORD $0x8b1801d7  // add	x23, x14, x24
	WORD $0xb201e3fc  // mov	x28, #-8608480567731124088
	WORD $0xf2e1111c  // movk	x28, #2184, lsl #48
LBB0_450:
	WORD $0xaa1103f0  // mov	x16, x17
	WORD $0x14000127  // b	LBB0_481 $1180(%rip)
LBB0_451:
	WORD $0xd280001a  // mov	x26, #0
	WORD $0xd2800018  // mov	x24, #0
	WORD $0xd2800015  // mov	x21, #0
	WORD $0xd2800017  // mov	x23, #0
	WORD $0xf940040e  // ldr	x14, [x0, #8]
	WORD $0xcb1601cf  // sub	x15, x14, x22
	WORD $0x8b160273  // add	x19, x19, x22
	WORD $0x1400000b  // b	LBB0_453 $44(%rip)
LBB0_452:
	WORD $0x937fff5a  // asr	x26, x26, #63
	WORD $0x9e670373  // fmov	d19, x27
	WORD $0x0e205a73  // cnt.8b	v19, v19
	WORD $0x2e303a73  // uaddlv.8b	h19, v19
	WORD $0x1e26026e  // fmov	w14, s19
	WORD $0x8b1501d5  // add	x21, x14, x21
	WORD $0x91010273  // add	x19, x19, #64
	WORD $0xf9400fef  // ldr	x15, [sp, #24]
	WORD $0xb201e3fc  // mov	x28, #-8608480567731124088
	WORD $0xf2e1111c  // movk	x28, #2184, lsl #48
LBB0_453:
	WORD $0xf10101ee  // subs	x14, x15, #64
	WORD $0xa9013bef  // stp	x15, x14, [sp, #16]
	WORD $0x5400176b  // b.lt	LBB0_460 $748(%rip)
LBB0_454:
	WORD $0xad405676  // ldp	q22, q21, [x19]
	WORD $0xad414e74  // ldp	q20, q19, [x19, #32]
	WORD $0x6e238ed7  // cmeq.16b	v23, v22, v3
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602ee  // fmov	w14, s23
	WORD $0x6e238eb7  // cmeq.16b	v23, v21, v3
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602ef  // fmov	w15, s23
	WORD $0x6e238e97  // cmeq.16b	v23, v20, v3
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f0  // fmov	w16, s23
	WORD $0x6e238e77  // cmeq.16b	v23, v19, v3
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f1  // fmov	w17, s23
	WORD $0xd3607e10  // lsl	x16, x16, #32
	WORD $0xaa11c210  // orr	x16, x16, x17, lsl #48
	WORD $0x53103def  // lsl	w15, w15, #16
	WORD $0xaa0f020f  // orr	x15, x16, x15
	WORD $0xaa0e01e2  // orr	x2, x15, x14
	WORD $0xaa18004e  // orr	x14, x2, x24
	WORD $0xb500008e  // cbnz	x14, LBB0_456 $16(%rip)
	WORD $0xd2800018  // mov	x24, #0
	WORD $0xd280001b  // mov	x27, #0
	WORD $0x1400000a  // b	LBB0_457 $40(%rip)
LBB0_456:
	WORD $0x8a38004e  // bic	x14, x2, x24
	WORD $0xaa0e070f  // orr	x15, x24, x14, lsl #1
	WORD $0x8a2f0050  // bic	x16, x2, x15
	WORD $0x9201f210  // and	x16, x16, #0xaaaaaaaaaaaaaaaa
	WORD $0xab0e020e  // adds	x14, x16, x14
	WORD $0x1a9f37f8  // cset	w24, hs
	WORD $0xd37ff9ce  // lsl	x14, x14, #1
	WORD $0xd200f1ce  // eor	x14, x14, #0x5555555555555555
	WORD $0x8a0f01db  // and	x27, x14, x15
LBB0_457:
	WORD $0x6e208ed7  // cmeq.16b	v23, v22, v0
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602ee  // fmov	w14, s23
	WORD $0x6e208eb7  // cmeq.16b	v23, v21, v0
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602ef  // fmov	w15, s23
	WORD $0x6e208e97  // cmeq.16b	v23, v20, v0
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f0  // fmov	w16, s23
	WORD $0x6e208e77  // cmeq.16b	v23, v19, v0
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f1  // fmov	w17, s23
	WORD $0xd3607e10  // lsl	x16, x16, #32
	WORD $0xaa11c210  // orr	x16, x16, x17, lsl #48
	WORD $0x53103def  // lsl	w15, w15, #16
	WORD $0xaa0f020f  // orr	x15, x16, x15
	WORD $0xaa0e01ee  // orr	x14, x15, x14
	WORD $0x8a3b01ce  // bic	x14, x14, x27
	WORD $0x9200e1cf  // and	x15, x14, #0x1111111111111111
	WORD $0x9203e1d0  // and	x16, x14, #0x2222222222222222
	WORD $0x9202e1d1  // and	x17, x14, #0x4444444444444444
	WORD $0x9201e1ce  // and	x14, x14, #0x8888888888888888
	WORD $0x9b047de2  // mul	x2, x15, x4
	WORD $0x9b1c7e06  // mul	x6, x16, x28
	WORD $0xca060042  // eor	x2, x2, x6
	WORD $0x9b077e26  // mul	x6, x17, x7
	WORD $0xb202e3fb  // mov	x27, #4919131752989213764
	WORD $0xf2e0889b  // movk	x27, #1092, lsl #48
	WORD $0xb203e3e7  // mov	x7, #2459565876494606882
	WORD $0xf2e04447  // movk	x7, #546, lsl #48
	WORD $0x9b077dc7  // mul	x7, x14, x7
	WORD $0xca0700c6  // eor	x6, x6, x7
	WORD $0xca060042  // eor	x2, x2, x6
	WORD $0xb200e3f6  // mov	x22, #1229782938247303441
	WORD $0xb203e3e4  // mov	x4, #2459565876494606882
	WORD $0x9b047de6  // mul	x6, x15, x4
	WORD $0x9b167e07  // mul	x7, x16, x22
	WORD $0xca0700c6  // eor	x6, x6, x7
	WORD $0x9b1c7e27  // mul	x7, x17, x28
	WORD $0x9b1b7ddb  // mul	x27, x14, x27
	WORD $0xca1b00e7  // eor	x7, x7, x27
	WORD $0xca0700c6  // eor	x6, x6, x7
	WORD $0xb202e3f9  // mov	x25, #4919131752989213764
	WORD $0x9b197de7  // mul	x7, x15, x25
	WORD $0x9b047e1b  // mul	x27, x16, x4
	WORD $0xca1b00e7  // eor	x7, x7, x27
	WORD $0x9b167e3b  // mul	x27, x17, x22
	WORD $0xb201e3fc  // mov	x28, #-8608480567731124088
	WORD $0xf2e1111c  // movk	x28, #2184, lsl #48
	WORD $0x9b1c7ddc  // mul	x28, x14, x28
	WORD $0xca1c037b  // eor	x27, x27, x28
	WORD $0xca1b00e7  // eor	x7, x7, x27
	WORD $0xb201e3f6  // mov	x22, #-8608480567731124088
	WORD $0x9b167def  // mul	x15, x15, x22
	WORD $0x9b197e10  // mul	x16, x16, x25
	WORD $0xca1001ef  // eor	x15, x15, x16
	WORD $0x9b047e30  // mul	x16, x17, x4
	WORD $0xb200e3e4  // mov	x4, #1229782938247303441
	WORD $0x9b047dce  // mul	x14, x14, x4
	WORD $0xca0e020e  // eor	x14, x16, x14
	WORD $0xca0e01ee  // eor	x14, x15, x14
	WORD $0x9200e04f  // and	x15, x2, #0x1111111111111111
	WORD $0x9203e0d0  // and	x16, x6, #0x2222222222222222
	WORD $0x9202e0f1  // and	x17, x7, #0x4444444444444444
	WORD $0x9201e1ce  // and	x14, x14, #0x8888888888888888
	WORD $0xaa1001ef  // orr	x15, x15, x16
	WORD $0xaa0e022e  // orr	x14, x17, x14
	WORD $0xaa0e01ee  // orr	x14, x15, x14
	WORD $0xca1a01da  // eor	x26, x14, x26
	WORD $0x6e278ed7  // cmeq.16b	v23, v22, v7
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602ee  // fmov	w14, s23
	WORD $0x6e278eb7  // cmeq.16b	v23, v21, v7
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602ef  // fmov	w15, s23
	WORD $0x6e278e97  // cmeq.16b	v23, v20, v7
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f0  // fmov	w16, s23
	WORD $0x6e278e77  // cmeq.16b	v23, v19, v7
	WORD $0x4e211ef7  // and.16b	v23, v23, v1
	WORD $0x4e0202f7  // tbl.16b	v23, { v23 }, v2
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f1  // fmov	w17, s23
	WORD $0xd3607e10  // lsl	x16, x16, #32
	WORD $0xaa11c210  // orr	x16, x16, x17, lsl #48
	WORD $0x53103def  // lsl	w15, w15, #16
	WORD $0xaa0f020f  // orr	x15, x16, x15
	WORD $0xaa0e01ee  // orr	x14, x15, x14
	WORD $0x8a3a01db  // bic	x27, x14, x26
	WORD $0x6e268ed6  // cmeq.16b	v22, v22, v6
	WORD $0x4e211ed6  // and.16b	v22, v22, v1
	WORD $0x4e0202d6  // tbl.16b	v22, { v22 }, v2
	WORD $0x4e71bad6  // addv.8h	h22, v22
	WORD $0x1e2602ce  // fmov	w14, s22
	WORD $0x6e268eb5  // cmeq.16b	v21, v21, v6
	WORD $0x4e211eb5  // and.16b	v21, v21, v1
	WORD $0x4e0202b5  // tbl.16b	v21, { v21 }, v2
	WORD $0x4e71bab5  // addv.8h	h21, v21
	WORD $0x1e2602af  // fmov	w15, s21
	WORD $0x6e268e94  // cmeq.16b	v20, v20, v6
	WORD $0x4e211e94  // and.16b	v20, v20, v1
	WORD $0x4e020294  // tbl.16b	v20, { v20 }, v2
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260290  // fmov	w16, s20
	WORD $0x6e268e73  // cmeq.16b	v19, v19, v6
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260271  // fmov	w17, s19
	WORD $0xd3607e10  // lsl	x16, x16, #32
	WORD $0xaa11c210  // orr	x16, x16, x17, lsl #48
	WORD $0x53103def  // lsl	w15, w15, #16
	WORD $0xaa0f020f  // orr	x15, x16, x15
	WORD $0xaa0e01ee  // orr	x14, x15, x14
	WORD $0xb202e3e7  // mov	x7, #4919131752989213764
	WORD $0xf2e08887  // movk	x7, #1092, lsl #48
	WORD $0xea3a01dc  // bics	x28, x14, x26
	WORD $0x54ffe8e0  // b.eq	LBB0_452 $-740(%rip)
LBB0_458:
	WORD $0xd1000782  // sub	x2, x28, #1
	WORD $0x8a1b004e  // and	x14, x2, x27
	WORD $0x9e6701d3  // fmov	d19, x14
	WORD $0x0e205a73  // cnt.8b	v19, v19
	WORD $0x2e303a73  // uaddlv.8b	h19, v19
	WORD $0x1e26026e  // fmov	w14, s19
	WORD $0x8b1501ce  // add	x14, x14, x21
	WORD $0xeb1701df  // cmp	x14, x23
	WORD $0x540007c9  // b.ls	LBB0_477 $248(%rip)
	WORD $0x910006f7  // add	x23, x23, #1
	WORD $0xea1c005c  // ands	x28, x2, x28
	WORD $0x54fffea1  // b.ne	LBB0_458 $-44(%rip)
	WORD $0x17ffff3a  // b	LBB0_452 $-792(%rip)
LBB0_460:
	WORD $0xf10001ff  // cmp	x15, #0
	WORD $0x54000dad  // b.le	LBB0_487 $436(%rip)
	WORD $0xad0243f0  // stp	q16, q16, [sp, #64]
	WORD $0xad0143f0  // stp	q16, q16, [sp, #32]
	WORD $0x92402e68  // and	x8, x19, #0xfff
	WORD $0xf13f051f  // cmp	x8, #4033
	WORD $0x54000383  // b.lo	LBB0_472 $112(%rip)
	WORD $0xf9400be8  // ldr	x8, [sp, #16]
	WORD $0xf100811b  // subs	x27, x8, #32
	WORD $0x540000a3  // b.lo	LBB0_464 $20(%rip)
	WORD $0xacc15273  // ldp	q19, q20, [x19], #32
	WORD $0xad0153f3  // stp	q19, q20, [sp, #32]
	WORD $0xaa0b03e8  // mov	x8, x11
	WORD $0x14000003  // b	LBB0_465 $12(%rip)
LBB0_464:
	WORD $0x910083e8  // add	x8, sp, #32
	WORD $0xf9400bfb  // ldr	x27, [sp, #16]
LBB0_465:
	WORD $0xf1004362  // subs	x2, x27, #16
	WORD $0x54000343  // b.lo	LBB0_473 $104(%rip)
	WORD $0x3cc10673  // ldr	q19, [x19], #16
	WORD $0x3c810513  // str	q19, [x8], #16
	WORD $0xaa0203fb  // mov	x27, x2
	WORD $0xf1002042  // subs	x2, x2, #8
	WORD $0x540002e2  // b.hs	LBB0_474 $92(%rip)
LBB0_467:
	WORD $0xf1001362  // subs	x2, x27, #4
	WORD $0x54000343  // b.lo	LBB0_475 $104(%rip)
LBB0_468:
	WORD $0xb840466e  // ldr	w14, [x19], #4
	WORD $0xb800450e  // str	w14, [x8], #4
	WORD $0xaa0203fb  // mov	x27, x2
	WORD $0xf1000842  // subs	x2, x2, #2
	WORD $0x540002e2  // b.hs	LBB0_476 $92(%rip)
LBB0_469:
	WORD $0xb400007b  // cbz	x27, LBB0_471 $12(%rip)
LBB0_470:
	WORD $0x3940026e  // ldrb	w14, [x19]
	WORD $0x3900010e  // strb	w14, [x8]
LBB0_471:
	WORD $0x910083f3  // add	x19, sp, #32
LBB0_472:
	WORD $0x529fffe8  // mov	w8, #65535
	WORD $0x3200f3fe  // mov	w30, #1431655765
	WORD $0x3201f3e5  // mov	w5, #-1431655766
	WORD $0xb201e3fc  // mov	x28, #-8608480567731124088
	WORD $0xf2e1111c  // movk	x28, #2184, lsl #48
	WORD $0xb202e3e7  // mov	x7, #4919131752989213764
	WORD $0xf2e08887  // movk	x7, #1092, lsl #48
	WORD $0xb200e3e4  // mov	x4, #1229782938247303441
	WORD $0x17ffff1c  // b	LBB0_454 $-912(%rip)
LBB0_473:
	WORD $0xf1002362  // subs	x2, x27, #8
	WORD $0x54fffd63  // b.lo	LBB0_467 $-84(%rip)
LBB0_474:
	WORD $0xf840866e  // ldr	x14, [x19], #8
	WORD $0xf800850e  // str	x14, [x8], #8
	WORD $0xaa0203fb  // mov	x27, x2
	WORD $0xf1001042  // subs	x2, x2, #4
	WORD $0x54fffd02  // b.hs	LBB0_468 $-96(%rip)
LBB0_475:
	WORD $0xf1000b62  // subs	x2, x27, #2
	WORD $0x54fffd63  // b.lo	LBB0_469 $-84(%rip)
LBB0_476:
	WORD $0x7840266e  // ldrh	w14, [x19], #2
	WORD $0x7800250e  // strh	w14, [x8], #2
	WORD $0xaa0203fb  // mov	x27, x2
	WORD $0xb5fffd02  // cbnz	x2, LBB0_470 $-96(%rip)
	WORD $0x17ffffe9  // b	LBB0_471 $-92(%rip)
LBB0_477:
	WORD $0xf940040e  // ldr	x14, [x0, #8]
	WORD $0xdac0038f  // rbit	x15, x28
	WORD $0xdac011ef  // clz	x15, x15
	WORD $0xf9400bf0  // ldr	x16, [sp, #16]
	WORD $0xcb1001ef  // sub	x15, x15, x16
	WORD $0x8b0e01ee  // add	x14, x15, x14
	WORD $0x910005cf  // add	x15, x14, #1
	WORD $0xf900002f  // str	x15, [x1]
	WORD $0xf9400410  // ldr	x16, [x0, #8]
	WORD $0xeb1001ff  // cmp	x15, x16
	WORD $0x9a8e2616  // csinc	x22, x16, x14, hs
	WORD $0xf9000036  // str	x22, [x1]
	WORD $0xb201e3fc  // mov	x28, #-8608480567731124088
	WORD $0xf2e1111c  // movk	x28, #2184, lsl #48
Lloh18:
	WORD $0x10012c10  // adr	x16, __UnquoteTab $9600(%rip)
Lloh19:
	WORD $0x91000210  // add	x16, x16, __UnquoteTab@PAGEOFF $0(%rip)
	WORD $0x17fffd5c  // b	LBB0_391 $-2704(%rip)
LBB0_478:
	WORD $0xdac0038e  // rbit	x14, x28
	WORD $0xdac011ce  // clz	x14, x14
	WORD $0x8b1702af  // add	x15, x21, x23
	WORD $0x8b0f01ce  // add	x14, x14, x15
	WORD $0x910009d6  // add	x22, x14, #2
	WORD $0xf9000036  // str	x22, [x1]
	WORD $0xb201e3fc  // mov	x28, #-8608480567731124088
	WORD $0xf2e1111c  // movk	x28, #2184, lsl #48
	WORD $0xaa1103f0  // mov	x16, x17
	WORD $0x17fffd52  // b	LBB0_391 $-2744(%rip)
LBB0_479:
	WORD $0xf9000036  // str	x22, [x1]
	WORD $0x17fffd50  // b	LBB0_391 $-2752(%rip)
LBB0_480:
	WORD $0x8b160275  // add	x21, x19, x22
LBB0_481:
	WORD $0xf10006ff  // cmp	x23, #1
	WORD $0x540000ea  // b.ge	LBB0_483 $28(%rip)
	WORD $0x17fffd4c  // b	LBB0_391 $-2768(%rip)
LBB0_482:
	WORD $0x92800022  // mov	x2, #-2
	WORD $0x52800058  // mov	w24, #2
	WORD $0x8b1802b5  // add	x21, x21, x24
	WORD $0xab0202f7  // adds	x23, x23, x2
	WORD $0x54ffa8ed  // b.le	LBB0_391 $-2788(%rip)
LBB0_483:
	WORD $0x394002a2  // ldrb	w2, [x21]
	WORD $0x7101705f  // cmp	w2, #92
	WORD $0x54ffff20  // b.eq	LBB0_482 $-28(%rip)
	WORD $0x7100885f  // cmp	w2, #34
	WORD $0x540000e0  // b.eq	LBB0_486 $28(%rip)
	WORD $0x92800002  // mov	x2, #-1
	WORD $0x52800038  // mov	w24, #1
	WORD $0x8b1802b5  // add	x21, x21, x24
	WORD $0xab0202f7  // adds	x23, x23, x2
	WORD $0x54fffeec  // b.gt	LBB0_483 $-36(%rip)
	WORD $0x17fffd3c  // b	LBB0_391 $-2832(%rip)
LBB0_486:
	WORD $0xcb1302ae  // sub	x14, x21, x19
	WORD $0x910005d6  // add	x22, x14, #1
	WORD $0xf9000036  // str	x22, [x1]
	WORD $0x17fffd38  // b	LBB0_391 $-2848(%rip)
LBB0_487:
	WORD $0xf9400416  // ldr	x22, [x0, #8]
	WORD $0xf9000036  // str	x22, [x1]
	WORD $0x529fffe8  // mov	w8, #65535
	WORD $0x3200f3fe  // mov	w30, #1431655765
	WORD $0x3201f3e5  // mov	w5, #-1431655766
	WORD $0xb201e3fc  // mov	x28, #-8608480567731124088
	WORD $0xf2e1111c  // movk	x28, #2184, lsl #48
	WORD $0xb202e3e7  // mov	x7, #4919131752989213764
	WORD $0xf2e08887  // movk	x7, #1092, lsl #48
	WORD $0xb200e3e4  // mov	x4, #1229782938247303441
Lloh20:
	WORD $0x100125f0  // adr	x16, __UnquoteTab $9404(%rip)
Lloh21:
	WORD $0x91000210  // add	x16, x16, __UnquoteTab@PAGEOFF $0(%rip)
	WORD $0x17fffd2b  // b	LBB0_391 $-2900(%rip)
LBB0_488:
	WORD $0xd1000708  // sub	x8, x24, #1
	WORD $0xeb17011f  // cmp	x8, x23
	WORD $0x54000161  // b.ne	LBB0_490 $44(%rip)
	WORD $0x529fffe8  // mov	w8, #65535
	WORD $0x3200f3fe  // mov	w30, #1431655765
	WORD $0x3201f3e5  // mov	w5, #-1431655766
	WORD $0xb201e3fc  // mov	x28, #-8608480567731124088
	WORD $0xf2e1111c  // movk	x28, #2184, lsl #48
	WORD $0xb202e3e7  // mov	x7, #4919131752989213764
	WORD $0xf2e08887  // movk	x7, #1092, lsl #48
	WORD $0xb200e3e4  // mov	x4, #1229782938247303441
	WORD $0xaa1103f0  // mov	x16, x17
	WORD $0x17fffd1e  // b	LBB0_391 $-2952(%rip)
LBB0_490:
	WORD $0x8b150268  // add	x8, x19, x21
	WORD $0x8b170108  // add	x8, x8, x23
	WORD $0x91000915  // add	x21, x8, #2
	WORD $0xcb170308  // sub	x8, x24, x23
	WORD $0xd1000917  // sub	x23, x8, #2
	WORD $0x529fffe8  // mov	w8, #65535
	WORD $0x3200f3fe  // mov	w30, #1431655765
	WORD $0x3201f3e5  // mov	w5, #-1431655766
	WORD $0xb201e3fc  // mov	x28, #-8608480567731124088
	WORD $0xf2e1111c  // movk	x28, #2184, lsl #48
	WORD $0xb202e3e7  // mov	x7, #4919131752989213764
	WORD $0xf2e08887  // movk	x7, #1092, lsl #48
	WORD $0xb200e3e4  // mov	x4, #1229782938247303441
	WORD $0x17fffe9a  // b	LBB0_450 $-1432(%rip)
LBB0_491:
	WORD $0x91004129  // add	x9, x9, #16
	WORD $0xeb0a013f  // cmp	x9, x10
	WORD $0x54febf81  // b.ne	LBB0_2 $-10256(%rip)
LBB0_492:
	WORD $0xb400aa83  // cbz	x3, LBB0_779 $5456(%rip)
Lloh22:
	WORD $0x10feb988  // adr	x8, lCPI0_2 $-10448(%rip)
Lloh23:
	WORD $0x3dc00100  // ldr	q0, [x8, lCPI0_2@PAGEOFF] $0(%rip)
	WORD $0xaa0303ea  // mov	x10, x3
	WORD $0x3c808540  // str	q0, [x10], #8
	WORD $0xf9400009  // ldr	x9, [x0]
	WORD $0xaa2903eb  // mvn	x11, x9
	WORD $0xf940003b  // ldr	x27, [x1]
	WORD $0xcb0903ec  // neg	x12, x9
	WORD $0xd100052d  // sub	x13, x9, #1
	WORD $0x9280000e  // mov	x14, #-1
	WORD $0x5280002f  // mov	w15, #1
	WORD $0xd284c010  // mov	x16, #9728
	WORD $0xf2c00030  // movk	x16, #1, lsl #32
	WORD $0x52800078  // mov	w24, #3
	WORD $0x52800085  // mov	w5, #4
	WORD $0x4f01e440  // movi.16b	v0, #34
	WORD $0x4f02e781  // movi.16b	v1, #92
Lloh24:
	WORD $0x10feb668  // adr	x8, lCPI0_0 $-10548(%rip)
Lloh25:
	WORD $0x3dc00102  // ldr	q2, [x8, lCPI0_0@PAGEOFF] $0(%rip)
Lloh26:
	WORD $0x10feb6a8  // adr	x8, lCPI0_1 $-10540(%rip)
Lloh27:
	WORD $0x3dc00103  // ldr	q3, [x8, lCPI0_1@PAGEOFF] $0(%rip)
	WORD $0x92f00006  // mov	x6, #9223372036854775807
	WORD $0xb20903f6  // mov	x22, #36028797027352576
	WORD $0xf2800036  // movk	x22, #1
	WORD $0x4f01e5c4  // movi.16b	v4, #46
	WORD $0x4f01e565  // movi.16b	v5, #43
	WORD $0x4f01e5a6  // movi.16b	v6, #45
	WORD $0x12800007  // mov	w7, #-1
	WORD $0x4f06e607  // movi.16b	v7, #208
	WORD $0x4f00e550  // movi.16b	v16, #10
	WORD $0x4f06e7f1  // movi.16b	v17, #223
	WORD $0x528000b7  // mov	w23, #5
	WORD $0x528d8c39  // mov	w25, #27745
	WORD $0x72acae79  // movk	w25, #25971, lsl #16
	WORD $0x528eadde  // mov	w30, #30062
	WORD $0x72ad8d9e  // movk	w30, #27756, lsl #16
	WORD $0x528e4e82  // mov	w2, #29300
	WORD $0x72acaea2  // movk	w2, #25973, lsl #16
	WORD $0x4f02e4b2  // movi.16b	v18, #69
	WORD $0x52800024  // mov	w4, #1
LBB0_494:
	WORD $0xf9400408  // ldr	x8, [x0, #8]
	WORD $0xeb08037f  // cmp	x27, x8
	WORD $0x54000162  // b.hs	LBB0_499 $44(%rip)
	WORD $0x387b6931  // ldrb	w17, [x9, x27]
	WORD $0x7100363f  // cmp	w17, #13
	WORD $0x54000100  // b.eq	LBB0_499 $32(%rip)
	WORD $0x7100823f  // cmp	w17, #32
	WORD $0x540000c0  // b.eq	LBB0_499 $24(%rip)
	WORD $0x51002e31  // sub	w17, w17, #11
	WORD $0x31000a3f  // cmn	w17, #2
	WORD $0x54000062  // b.hs	LBB0_499 $12(%rip)
	WORD $0xaa1b03fa  // mov	x26, x27
	WORD $0x14000031  // b	LBB0_515 $196(%rip)
LBB0_499:
	WORD $0x9100077a  // add	x26, x27, #1
	WORD $0xeb08035f  // cmp	x26, x8
	WORD $0x54000122  // b.hs	LBB0_503 $36(%rip)
	WORD $0x387a6931  // ldrb	w17, [x9, x26]
	WORD $0x7100363f  // cmp	w17, #13
	WORD $0x540000c0  // b.eq	LBB0_503 $24(%rip)
	WORD $0x7100823f  // cmp	w17, #32
	WORD $0x54000080  // b.eq	LBB0_503 $16(%rip)
	WORD $0x51002e31  // sub	w17, w17, #11
	WORD $0x31000a3f  // cmn	w17, #2
	WORD $0x540004c3  // b.lo	LBB0_515 $152(%rip)
LBB0_503:
	WORD $0x91000b7a  // add	x26, x27, #2
	WORD $0xeb08035f  // cmp	x26, x8
	WORD $0x54000122  // b.hs	LBB0_507 $36(%rip)
	WORD $0x387a6931  // ldrb	w17, [x9, x26]
	WORD $0x7100363f  // cmp	w17, #13
	WORD $0x540000c0  // b.eq	LBB0_507 $24(%rip)
	WORD $0x7100823f  // cmp	w17, #32
	WORD $0x54000080  // b.eq	LBB0_507 $16(%rip)
	WORD $0x51002e31  // sub	w17, w17, #11
	WORD $0x31000a3f  // cmn	w17, #2
	WORD $0x54000363  // b.lo	LBB0_515 $108(%rip)
LBB0_507:
	WORD $0x91000f7a  // add	x26, x27, #3
	WORD $0xeb08035f  // cmp	x26, x8
	WORD $0x54000122  // b.hs	LBB0_511 $36(%rip)
	WORD $0x387a6931  // ldrb	w17, [x9, x26]
	WORD $0x7100363f  // cmp	w17, #13
	WORD $0x540000c0  // b.eq	LBB0_511 $24(%rip)
	WORD $0x7100823f  // cmp	w17, #32
	WORD $0x54000080  // b.eq	LBB0_511 $16(%rip)
	WORD $0x51002e31  // sub	w17, w17, #11
	WORD $0x31000a3f  // cmn	w17, #2
	WORD $0x54000203  // b.lo	LBB0_515 $64(%rip)
LBB0_511:
	WORD $0x9100137a  // add	x26, x27, #4
	WORD $0xeb08035f  // cmp	x26, x8
	WORD $0x5400b742  // b.hs	LBB0_833 $5864(%rip)
LBB0_512:
	WORD $0x387a6931  // ldrb	w17, [x9, x26]
	WORD $0x7100823f  // cmp	w17, #32
	WORD $0x9ad121f1  // lsl	x17, x15, x17
	WORD $0x8a100231  // and	x17, x17, x16
	WORD $0xfa409a24  // ccmp	x17, #0, #4, ls
	WORD $0x540000a0  // b.eq	LBB0_514 $20(%rip)
	WORD $0x9100075a  // add	x26, x26, #1
	WORD $0xeb1a011f  // cmp	x8, x26
	WORD $0x54ffff01  // b.ne	LBB0_512 $-32(%rip)
	WORD $0x1400052e  // b	LBB0_799 $5304(%rip)
LBB0_514:
	WORD $0xeb08035f  // cmp	x26, x8
	WORD $0x5400a582  // b.hs	LBB0_799 $5296(%rip)
LBB0_515:
	WORD $0x9100075b  // add	x27, x26, #1
	WORD $0xf900003b  // str	x27, [x1]
	WORD $0x8b1a013c  // add	x28, x9, x26
	WORD $0x39400394  // ldrb	w20, [x28]
	WORD $0x3400a4f4  // cbz	w20, LBB0_799 $5276(%rip)
	WORD $0xd1000493  // sub	x19, x4, #1
	WORD $0xf8737948  // ldr	x8, [x10, x19, lsl #3]
	WORD $0xb10005df  // cmn	x14, #1
	WORD $0x9a8e034e  // csel	x14, x26, x14, eq
	WORD $0x71000d1f  // cmp	w8, #3
	WORD $0x54000dac  // b.gt	LBB0_532 $436(%rip)
	WORD $0x7100051f  // cmp	w8, #1
	WORD $0x54001cc0  // b.eq	LBB0_550 $920(%rip)
	WORD $0x7100091f  // cmp	w8, #2
	WORD $0x54001de0  // b.eq	LBB0_555 $956(%rip)
	WORD $0x71000d1f  // cmp	w8, #3
	WORD $0x54001d61  // b.ne	LBB0_554 $940(%rip)
	WORD $0x71008a9f  // cmp	w20, #34
	WORD $0x5400b221  // b.ne	LBB0_831 $5700(%rip)
	WORD $0xf8337945  // str	x5, [x10, x19, lsl #3]
	WORD $0xf9400408  // ldr	x8, [x0, #8]
	WORD $0xeb1b011e  // subs	lr, x8, x27
	WORD $0x54010aa0  // b.eq	LBB0_931 $8532(%rip)
	WORD $0xf10103df  // cmp	lr, #64
	WORD $0x54006a03  // b.lo	LBB0_706 $3392(%rip)
	WORD $0xd2800004  // mov	x4, #0
	WORD $0x9280001c  // mov	x28, #-1
LBB0_524:
	WORD $0x8b1b0131  // add	x17, x9, x27
	WORD $0xad405233  // ldp	q19, q20, [x17]
	WORD $0xad415a35  // ldp	q21, q22, [x17, #32]
	WORD $0x6e208e77  // cmeq.16b	v23, v19, v0
	WORD $0x6e208e98  // cmeq.16b	v24, v20, v0
	WORD $0x6e208eb9  // cmeq.16b	v25, v21, v0
	WORD $0x6e208eda  // cmeq.16b	v26, v22, v0
	WORD $0x6e218e73  // cmeq.16b	v19, v19, v1
	WORD $0x6e218e94  // cmeq.16b	v20, v20, v1
	WORD $0x6e218eb5  // cmeq.16b	v21, v21, v1
	WORD $0x6e218ed6  // cmeq.16b	v22, v22, v1
	WORD $0x4e221ef7  // and.16b	v23, v23, v2
	WORD $0x4e0302f7  // tbl.16b	v23, { v23 }, v3
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f1  // fmov	w17, s23
	WORD $0x4e221f17  // and.16b	v23, v24, v2
	WORD $0x4e0302f7  // tbl.16b	v23, { v23 }, v3
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602e6  // fmov	w6, s23
	WORD $0x4e221f37  // and.16b	v23, v25, v2
	WORD $0x4e0302f7  // tbl.16b	v23, { v23 }, v3
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f3  // fmov	w19, s23
	WORD $0x4e221f57  // and.16b	v23, v26, v2
	WORD $0x4e0302f7  // tbl.16b	v23, { v23 }, v3
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f4  // fmov	w20, s23
	WORD $0x4e221e73  // and.16b	v19, v19, v2
	WORD $0x4e030273  // tbl.16b	v19, { v19 }, v3
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260275  // fmov	w21, s19
	WORD $0x4e221e93  // and.16b	v19, v20, v2
	WORD $0x4e030273  // tbl.16b	v19, { v19 }, v3
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260276  // fmov	w22, s19
	WORD $0x4e221eb3  // and.16b	v19, v21, v2
	WORD $0x4e030273  // tbl.16b	v19, { v19 }, v3
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260277  // fmov	w23, s19
	WORD $0x4e221ed3  // and.16b	v19, v22, v2
	WORD $0x4e030273  // tbl.16b	v19, { v19 }, v3
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260279  // fmov	w25, s19
	WORD $0xd3607e73  // lsl	x19, x19, #32
	WORD $0xaa14c273  // orr	x19, x19, x20, lsl #48
	WORD $0x53103cc6  // lsl	w6, w6, #16
	WORD $0xaa060266  // orr	x6, x19, x6
	WORD $0xaa1100d3  // orr	x19, x6, x17
	WORD $0xd3607ef1  // lsl	x17, x23, #32
	WORD $0xaa19c231  // orr	x17, x17, x25, lsl #48
	WORD $0x53103ec6  // lsl	w6, w22, #16
	WORD $0xaa060231  // orr	x17, x17, x6
	WORD $0xaa150231  // orr	x17, x17, x21
	WORD $0xb5000111  // cbnz	x17, LBB0_528 $32(%rip)
	WORD $0xb5000184  // cbnz	x4, LBB0_529 $48(%rip)
	WORD $0xb50002d3  // cbnz	x19, LBB0_530 $88(%rip)
LBB0_527:
	WORD $0xd10103de  // sub	lr, lr, #64
	WORD $0x9101037b  // add	x27, x27, #64
	WORD $0xf100ffdf  // cmp	lr, #63
	WORD $0x54fff8a8  // b.hi	LBB0_524 $-236(%rip)
	WORD $0x140002fa  // b	LBB0_702 $3048(%rip)
LBB0_528:
	WORD $0xb100079f  // cmn	x28, #1
	WORD $0xdac00226  // rbit	x6, x17
	WORD $0xdac010c6  // clz	x6, x6
	WORD $0x8b1b00c6  // add	x6, x6, x27
	WORD $0x9a86139c  // csel	x28, x28, x6, ne
LBB0_529:
	WORD $0x8a240226  // bic	x6, x17, x4
	WORD $0xaa060494  // orr	x20, x4, x6, lsl #1
	WORD $0x8a340231  // bic	x17, x17, x20
	WORD $0x9201f231  // and	x17, x17, #0xaaaaaaaaaaaaaaaa
	WORD $0xab060231  // adds	x17, x17, x6
	WORD $0x1a9f37e4  // cset	w4, hs
	WORD $0xd37ffa31  // lsl	x17, x17, #1
	WORD $0xd200f231  // eor	x17, x17, #0x5555555555555555
	WORD $0x8a140231  // and	x17, x17, x20
	WORD $0x8a310273  // bic	x19, x19, x17
	WORD $0xb4fffd93  // cbz	x19, LBB0_527 $-80(%rip)
LBB0_530:
	WORD $0xdac00271  // rbit	x17, x19
	WORD $0xdac01231  // clz	x17, x17
	WORD $0x8b1b0231  // add	x17, x17, x27
	WORD $0x9100063b  // add	x27, x17, #1
	WORD $0x52800085  // mov	w5, #4
	WORD $0x92f00006  // mov	x6, #9223372036854775807
	WORD $0xb20903f6  // mov	x22, #36028797027352576
	WORD $0xf2800036  // movk	x22, #1
	WORD $0x528000b7  // mov	w23, #5
	WORD $0x528d8c39  // mov	w25, #27745
	WORD $0x72acae79  // movk	w25, #25971, lsl #16
	WORD $0x528eadde  // mov	w30, #30062
	WORD $0x72ad8d9e  // movk	w30, #27756, lsl #16
LBB0_531:
	WORD $0xb6f8439b  // tbz	x27, #63, LBB0_650 $2160(%rip)
	WORD $0x1400079a  // b	LBB0_903 $7784(%rip)
LBB0_532:
	WORD $0x7100111f  // cmp	w8, #4
	WORD $0x54000fe0  // b.eq	LBB0_552 $508(%rip)
	WORD $0x7100151f  // cmp	w8, #5
	WORD $0x54001100  // b.eq	LBB0_557 $544(%rip)
	WORD $0x7100191f  // cmp	w8, #6
	WORD $0x54000fe1  // b.ne	LBB0_554 $508(%rip)
	WORD $0x71008a9f  // cmp	w20, #34
	WORD $0x54001021  // b.ne	LBB0_556 $516(%rip)
	WORD $0x3200f3e2  // mov	w2, #1431655765
	WORD $0x52800048  // mov	w8, #2
	WORD $0xf8337948  // str	x8, [x10, x19, lsl #3]
	WORD $0xf9400408  // ldr	x8, [x0, #8]
	WORD $0xeb1b011e  // subs	lr, x8, x27
	WORD $0x5400fce0  // b.eq	LBB0_931 $8092(%rip)
	WORD $0xf10103df  // cmp	lr, #64
	WORD $0x540062c3  // b.lo	LBB0_714 $3160(%rip)
	WORD $0xd2800004  // mov	x4, #0
	WORD $0x9280001c  // mov	x28, #-1
LBB0_539:
	WORD $0x8b1b0131  // add	x17, x9, x27
	WORD $0xad405233  // ldp	q19, q20, [x17]
	WORD $0xad415a35  // ldp	q21, q22, [x17, #32]
	WORD $0x6e208e77  // cmeq.16b	v23, v19, v0
	WORD $0x6e208e98  // cmeq.16b	v24, v20, v0
	WORD $0x6e208eb9  // cmeq.16b	v25, v21, v0
	WORD $0x6e208eda  // cmeq.16b	v26, v22, v0
	WORD $0x6e218e73  // cmeq.16b	v19, v19, v1
	WORD $0x6e218e94  // cmeq.16b	v20, v20, v1
	WORD $0x6e218eb5  // cmeq.16b	v21, v21, v1
	WORD $0x6e218ed6  // cmeq.16b	v22, v22, v1
	WORD $0x4e221ef7  // and.16b	v23, v23, v2
	WORD $0x4e0302f7  // tbl.16b	v23, { v23 }, v3
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f1  // fmov	w17, s23
	WORD $0x4e221f17  // and.16b	v23, v24, v2
	WORD $0x4e0302f7  // tbl.16b	v23, { v23 }, v3
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602e6  // fmov	w6, s23
	WORD $0x4e221f37  // and.16b	v23, v25, v2
	WORD $0x4e0302f7  // tbl.16b	v23, { v23 }, v3
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f3  // fmov	w19, s23
	WORD $0x4e221f57  // and.16b	v23, v26, v2
	WORD $0x4e0302f7  // tbl.16b	v23, { v23 }, v3
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f4  // fmov	w20, s23
	WORD $0x4e221e73  // and.16b	v19, v19, v2
	WORD $0x4e030273  // tbl.16b	v19, { v19 }, v3
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260275  // fmov	w21, s19
	WORD $0x4e221e93  // and.16b	v19, v20, v2
	WORD $0x4e030273  // tbl.16b	v19, { v19 }, v3
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260276  // fmov	w22, s19
	WORD $0x4e221eb3  // and.16b	v19, v21, v2
	WORD $0x4e030273  // tbl.16b	v19, { v19 }, v3
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260277  // fmov	w23, s19
	WORD $0x4e221ed3  // and.16b	v19, v22, v2
	WORD $0x4e030273  // tbl.16b	v19, { v19 }, v3
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260279  // fmov	w25, s19
	WORD $0xd3607e73  // lsl	x19, x19, #32
	WORD $0xaa14c273  // orr	x19, x19, x20, lsl #48
	WORD $0x53103cc6  // lsl	w6, w6, #16
	WORD $0xaa060266  // orr	x6, x19, x6
	WORD $0xaa1100d3  // orr	x19, x6, x17
	WORD $0xd3607ef1  // lsl	x17, x23, #32
	WORD $0xaa19c231  // orr	x17, x17, x25, lsl #48
	WORD $0x53103ec6  // lsl	w6, w22, #16
	WORD $0xaa060231  // orr	x17, x17, x6
	WORD $0xaa150231  // orr	x17, x17, x21
	WORD $0xb5000111  // cbnz	x17, LBB0_543 $32(%rip)
	WORD $0xb5000184  // cbnz	x4, LBB0_544 $48(%rip)
	WORD $0xb50002d3  // cbnz	x19, LBB0_545 $88(%rip)
LBB0_542:
	WORD $0xd10103de  // sub	lr, lr, #64
	WORD $0x9101037b  // add	x27, x27, #64
	WORD $0xf100ffdf  // cmp	lr, #63
	WORD $0x54fff8a8  // b.hi	LBB0_539 $-236(%rip)
	WORD $0x140002d1  // b	LBB0_712 $2884(%rip)
LBB0_543:
	WORD $0xb100079f  // cmn	x28, #1
	WORD $0xdac00226  // rbit	x6, x17
	WORD $0xdac010c6  // clz	x6, x6
	WORD $0x8b1b00c6  // add	x6, x6, x27
	WORD $0x9a86139c  // csel	x28, x28, x6, ne
LBB0_544:
	WORD $0x8a240226  // bic	x6, x17, x4
	WORD $0xaa060494  // orr	x20, x4, x6, lsl #1
	WORD $0x8a340231  // bic	x17, x17, x20
	WORD $0x9201f231  // and	x17, x17, #0xaaaaaaaaaaaaaaaa
	WORD $0xab060231  // adds	x17, x17, x6
	WORD $0x1a9f37e4  // cset	w4, hs
	WORD $0xd37ffa31  // lsl	x17, x17, #1
	WORD $0xd200f231  // eor	x17, x17, #0x5555555555555555
	WORD $0x8a140231  // and	x17, x17, x20
	WORD $0x8a310273  // bic	x19, x19, x17
	WORD $0xb4fffd93  // cbz	x19, LBB0_542 $-80(%rip)
LBB0_545:
	WORD $0xdac00271  // rbit	x17, x19
	WORD $0xdac01231  // clz	x17, x17
	WORD $0x8b1b0231  // add	x17, x17, x27
	WORD $0x9100063b  // add	x27, x17, #1
LBB0_546:
	WORD $0x52800085  // mov	w5, #4
	WORD $0x92f00006  // mov	x6, #9223372036854775807
	WORD $0xb20903f6  // mov	x22, #36028797027352576
	WORD $0xf2800036  // movk	x22, #1
	WORD $0x528000b7  // mov	w23, #5
	WORD $0x528d8c39  // mov	w25, #27745
	WORD $0x72acae79  // movk	w25, #25971, lsl #16
	WORD $0x528eadde  // mov	w30, #30062
	WORD $0x72ad8d9e  // movk	w30, #27756, lsl #16
	WORD $0x528e4e82  // mov	w2, #29300
	WORD $0x72acaea2  // movk	w2, #25973, lsl #16
	WORD $0xb7f8e57b  // tbnz	x27, #63, LBB0_903 $7340(%rip)
LBB0_547:
	WORD $0xf900003b  // str	x27, [x1]
	WORD $0xaa1a03e8  // mov	x8, x26
	WORD $0xb27ff7f1  // mov	x17, #9223372036854775806
	WORD $0xeb11035f  // cmp	x26, x17
	WORD $0x54009748  // b.hi	LBB0_832 $4840(%rip)
	WORD $0xf9400068  // ldr	x8, [x3]
	WORD $0xf13ffd1f  // cmp	x8, #4095
	WORD $0x5400e42c  // b.gt	LBB0_902 $7300(%rip)
	WORD $0x91000511  // add	x17, x8, #1
	WORD $0xf9000071  // str	x17, [x3]
	WORD $0xf8287945  // str	x5, [x10, x8, lsl #3]
	WORD $0x140001a4  // b	LBB0_651 $1680(%rip)
LBB0_550:
	WORD $0x7100b29f  // cmp	w20, #44
	WORD $0x54001660  // b.eq	LBB0_597 $716(%rip)
	WORD $0x7101769f  // cmp	w20, #93
	WORD $0x540001e0  // b.eq	LBB0_558 $60(%rip)
	WORD $0x140004ad  // b	LBB0_831 $4788(%rip)
LBB0_552:
	WORD $0x7100ea9f  // cmp	w20, #58
	WORD $0x54009561  // b.ne	LBB0_831 $4780(%rip)
	WORD $0xf833795f  // str	xzr, [x10, x19, lsl #3]
	WORD $0x1400019b  // b	LBB0_651 $1644(%rip)
LBB0_554:
	WORD $0xf9000073  // str	x19, [x3]
	WORD $0x1400000e  // b	LBB0_560 $56(%rip)
LBB0_555:
	WORD $0x7100b29f  // cmp	w20, #44
	WORD $0x540015c0  // b.eq	LBB0_599 $696(%rip)
LBB0_556:
	WORD $0x7101f69f  // cmp	w20, #125
	WORD $0x54000080  // b.eq	LBB0_558 $16(%rip)
	WORD $0x140004a2  // b	LBB0_831 $4744(%rip)
LBB0_557:
	WORD $0x7101769f  // cmp	w20, #93
	WORD $0x540000c1  // b.ne	LBB0_559 $24(%rip)
LBB0_558:
	WORD $0xf9000073  // str	x19, [x3]
	WORD $0xaa1303e4  // mov	x4, x19
	WORD $0xaa0e03e8  // mov	x8, x14
	WORD $0xb5ffd793  // cbnz	x19, LBB0_494 $-1296(%rip)
	WORD $0x1400049c  // b	LBB0_832 $4720(%rip)
LBB0_559:
	WORD $0xf833794f  // str	x15, [x10, x19, lsl #3]
LBB0_560:
	WORD $0x92800028  // mov	x8, #-2
	WORD $0x71016a9f  // cmp	w20, #90
	WORD $0x540014cc  // b.gt	LBB0_601 $664(%rip)
	WORD $0x5100c291  // sub	w17, w20, #48
	WORD $0x71002a3f  // cmp	w17, #10
	WORD $0x54001c82  // b.hs	LBB0_621 $912(%rip)
	WORD $0xf9400408  // ldr	x8, [x0, #8]
	WORD $0xeb1a0113  // subs	x19, x8, x26
	WORD $0x5400e060  // b.eq	LBB0_905 $7180(%rip)
	WORD $0x39400388  // ldrb	w8, [x28]
	WORD $0x7100c11f  // cmp	w8, #48
	WORD $0x54000141  // b.ne	LBB0_567 $40(%rip)
	WORD $0xf100067f  // cmp	x19, #1
	WORD $0x54002f40  // b.eq	LBB0_650 $1512(%rip)
	WORD $0x387b6928  // ldrb	w8, [x9, x27]
	WORD $0x5100b908  // sub	w8, w8, #46
	WORD $0x7100dd1f  // cmp	w8, #55
	WORD $0x54002ec8  // b.hi	LBB0_650 $1496(%rip)
	WORD $0x9ac821e8  // lsl	x8, x15, x8
	WORD $0xea16011f  // tst	x8, x22
	WORD $0x54002e60  // b.eq	LBB0_650 $1484(%rip)
LBB0_567:
	WORD $0xf100427f  // cmp	x19, #16
	WORD $0x54005c03  // b.lo	LBB0_730 $2944(%rip)
	WORD $0xd2800014  // mov	x20, #0
	WORD $0xd280001e  // mov	lr, #0
	WORD $0x9280001b  // mov	x27, #-1
	WORD $0x92800004  // mov	x4, #-1
	WORD $0x92800008  // mov	x8, #-1
LBB0_569:
	WORD $0x3cfe6b93  // ldr	q19, [x28, lr]
	WORD $0x6e248e74  // cmeq.16b	v20, v19, v4
	WORD $0x6e258e75  // cmeq.16b	v21, v19, v5
	WORD $0x6e268e76  // cmeq.16b	v22, v19, v6
	WORD $0x4e278677  // add.16b	v23, v19, v7
	WORD $0x6e373617  // cmhi.16b	v23, v16, v23
	WORD $0x4e311e73  // and.16b	v19, v19, v17
	WORD $0x6e328e73  // cmeq.16b	v19, v19, v18
	WORD $0x4eb61eb5  // orr.16b	v21, v21, v22
	WORD $0x4eb41ef6  // orr.16b	v22, v23, v20
	WORD $0x4eb51e77  // orr.16b	v23, v19, v21
	WORD $0x4eb71ed6  // orr.16b	v22, v22, v23
	WORD $0x4e221e94  // and.16b	v20, v20, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260291  // fmov	w17, s20
	WORD $0x4e221e73  // and.16b	v19, v19, v2
	WORD $0x4e030273  // tbl.16b	v19, { v19 }, v3
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260276  // fmov	w22, s19
	WORD $0x4e221eb3  // and.16b	v19, v21, v2
	WORD $0x4e030273  // tbl.16b	v19, { v19 }, v3
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260279  // fmov	w25, s19
	WORD $0x4e221ed3  // and.16b	v19, v22, v2
	WORD $0x4e030273  // tbl.16b	v19, { v19 }, v3
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260266  // fmov	w6, s19
	WORD $0x2a2603e6  // mvn	w6, w6
	WORD $0x32103cc6  // orr	w6, w6, #0xffff0000
	WORD $0x5ac000c6  // rbit	w6, w6
	WORD $0x5ac010d5  // clz	w21, w6
	WORD $0x1ad520e6  // lsl	w6, w7, w21
	WORD $0x0a260237  // bic	w23, w17, w6
	WORD $0x0a2602d8  // bic	w24, w22, w6
	WORD $0x0a260322  // bic	w2, w25, w6
	WORD $0x710042bf  // cmp	w21, #16
	WORD $0x1a970226  // csel	w6, w17, w23, eq
	WORD $0x1a9802d7  // csel	w23, w22, w24, eq
	WORD $0x1a820336  // csel	w22, w25, w2, eq
	WORD $0x510004d1  // sub	w17, w6, #1
	WORD $0x6a060231  // ands	w17, w17, w6
	WORD $0x54004061  // b.ne	LBB0_703 $2060(%rip)
	WORD $0x510006f1  // sub	w17, w23, #1
	WORD $0x6a170231  // ands	w17, w17, w23
	WORD $0x54004001  // b.ne	LBB0_703 $2048(%rip)
	WORD $0x510006d1  // sub	w17, w22, #1
	WORD $0x6a160231  // ands	w17, w17, w22
	WORD $0x54003fa1  // b.ne	LBB0_703 $2036(%rip)
	WORD $0x340000c6  // cbz	w6, LBB0_575 $24(%rip)
	WORD $0x5ac000d1  // rbit	w17, w6
	WORD $0x5ac01231  // clz	w17, w17
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0x54003fa1  // b.ne	LBB0_704 $2036(%rip)
	WORD $0x8b1103c8  // add	x8, lr, x17
LBB0_575:
	WORD $0x340000d7  // cbz	w23, LBB0_578 $24(%rip)
	WORD $0x5ac002f1  // rbit	w17, w23
	WORD $0x5ac01231  // clz	w17, w17
	WORD $0xb100049f  // cmn	x4, #1
	WORD $0x54003ee1  // b.ne	LBB0_704 $2012(%rip)
	WORD $0x8b1103c4  // add	x4, lr, x17
LBB0_578:
	WORD $0x340000d6  // cbz	w22, LBB0_581 $24(%rip)
	WORD $0x5ac002d1  // rbit	w17, w22
	WORD $0x5ac01231  // clz	w17, w17
	WORD $0xb100077f  // cmn	x27, #1
	WORD $0x54003e21  // b.ne	LBB0_704 $1988(%rip)
	WORD $0x8b1103db  // add	x27, lr, x17
LBB0_581:
	WORD $0x710042bf  // cmp	w21, #16
	WORD $0x54000b01  // b.ne	LBB0_607 $352(%rip)
	WORD $0x910043de  // add	lr, lr, #16
	WORD $0xd1004294  // sub	x20, x20, #16
	WORD $0x8b140275  // add	x21, x19, x20
	WORD $0xf1003ebf  // cmp	x21, #15
	WORD $0x54fff6e8  // b.hi	LBB0_569 $-292(%rip)
	WORD $0x8b1e0394  // add	x20, x28, lr
	WORD $0xeb1e027f  // cmp	x19, lr
	WORD $0x54000a40  // b.eq	LBB0_608 $328(%rip)
LBB0_584:
	WORD $0x8b150296  // add	x22, x20, x21
	WORD $0x8b1a01b1  // add	x17, x13, x26
	WORD $0xcb140233  // sub	x19, x17, x20
	WORD $0xcb1c0297  // sub	x23, x20, x28
	WORD $0xaa1403fe  // mov	lr, x20
	WORD $0x52800078  // mov	w24, #3
	WORD $0x528e4e82  // mov	w2, #29300
	WORD $0x72acaea2  // movk	w2, #25973, lsl #16
	WORD $0x14000009  // b	LBB0_587 $36(%rip)
LBB0_585:
	WORD $0xb100049f  // cmn	x4, #1
	WORD $0xaa1703e4  // mov	x4, x23
	WORD $0x540003a1  // b.ne	LBB0_596 $116(%rip)
LBB0_586:
	WORD $0xd1000673  // sub	x19, x19, #1
	WORD $0x910006f7  // add	x23, x23, #1
	WORD $0xaa1e03f4  // mov	x20, lr
	WORD $0xd10006b5  // sub	x21, x21, #1
	WORD $0xb40022d5  // cbz	x21, LBB0_652 $1112(%rip)
LBB0_587:
	WORD $0x384017c6  // ldrb	w6, [lr], #1
	WORD $0x5100c0d1  // sub	w17, w6, #48
	WORD $0x71002a3f  // cmp	w17, #10
	WORD $0x54ffff03  // b.lo	LBB0_586 $-32(%rip)
	WORD $0x7100b4df  // cmp	w6, #45
	WORD $0x5400016d  // b.le	LBB0_593 $44(%rip)
	WORD $0x710194df  // cmp	w6, #101
	WORD $0x54fffe20  // b.eq	LBB0_585 $-60(%rip)
	WORD $0x710114df  // cmp	w6, #69
	WORD $0x54fffde0  // b.eq	LBB0_585 $-68(%rip)
	WORD $0x7100b8df  // cmp	w6, #46
	WORD $0x540006a1  // b.ne	LBB0_608 $212(%rip)
	WORD $0xb100051f  // cmn	x8, #1
	WORD $0xaa1703e8  // mov	x8, x23
	WORD $0x54fffda0  // b.eq	LBB0_586 $-76(%rip)
	WORD $0x14000008  // b	LBB0_596 $32(%rip)
LBB0_593:
	WORD $0x7100acdf  // cmp	w6, #43
	WORD $0x54000060  // b.eq	LBB0_595 $12(%rip)
	WORD $0x7100b4df  // cmp	w6, #45
	WORD $0x540005a1  // b.ne	LBB0_608 $180(%rip)
LBB0_595:
	WORD $0xb100077f  // cmn	x27, #1
	WORD $0xaa1703fb  // mov	x27, x23
	WORD $0x54fffca0  // b.eq	LBB0_586 $-108(%rip)
LBB0_596:
	WORD $0x52800085  // mov	w5, #4
	WORD $0x92f00006  // mov	x6, #9223372036854775807
	WORD $0xb20903f6  // mov	x22, #36028797027352576
	WORD $0xf2800036  // movk	x22, #1
	WORD $0x528000b7  // mov	w23, #5
	WORD $0x528d8c39  // mov	w25, #27745
	WORD $0x72acae79  // movk	w25, #25971, lsl #16
	WORD $0x528eadde  // mov	w30, #30062
	WORD $0x72ad8d9e  // movk	w30, #27756, lsl #16
	WORD $0xb6f809b3  // tbz	x19, #63, LBB0_620 $308(%rip)
	WORD $0x14000671  // b	LBB0_906 $6596(%rip)
LBB0_597:
	WORD $0xf13ffc9f  // cmp	x4, #4095
	WORD $0x5400ccec  // b.gt	LBB0_902 $6556(%rip)
	WORD $0x91000488  // add	x8, x4, #1
	WORD $0xf9000068  // str	x8, [x3]
	WORD $0xf824795f  // str	xzr, [x10, x4, lsl #3]
	WORD $0x140000ea  // b	LBB0_651 $936(%rip)
LBB0_599:
	WORD $0xf13ffc9f  // cmp	x4, #4095
	WORD $0x5400cc2c  // b.gt	LBB0_902 $6532(%rip)
	WORD $0x91000488  // add	x8, x4, #1
	WORD $0xf9000068  // str	x8, [x3]
	WORD $0xf8247958  // str	x24, [x10, x4, lsl #3]
	WORD $0x140000e4  // b	LBB0_651 $912(%rip)
LBB0_601:
	WORD $0x7101b69f  // cmp	w20, #109
	WORD $0x54000a2d  // b.le	LBB0_627 $324(%rip)
	WORD $0x7101ba9f  // cmp	w20, #110
	WORD $0x54000c80  // b.eq	LBB0_634 $400(%rip)
	WORD $0x7101d29f  // cmp	w20, #116
	WORD $0x54000b40  // b.eq	LBB0_632 $360(%rip)
	WORD $0x7101ee9f  // cmp	w20, #123
	WORD $0x54007d61  // b.ne	LBB0_832 $4012(%rip)
	WORD $0xf9400068  // ldr	x8, [x3]
	WORD $0xf13ffd1f  // cmp	x8, #4095
	WORD $0x5400ca4c  // b.gt	LBB0_902 $6472(%rip)
	WORD $0x91000511  // add	x17, x8, #1
	WORD $0xf9000071  // str	x17, [x3]
	WORD $0x528000d1  // mov	w17, #6
	WORD $0xf8287951  // str	x17, [x10, x8, lsl #3]
	WORD $0x140000d4  // b	LBB0_651 $848(%rip)
LBB0_607:
	WORD $0x8b354391  // add	x17, x28, w21, uxtw
	WORD $0x8b1e0234  // add	x20, x17, lr
LBB0_608:
	WORD $0x92800013  // mov	x19, #-1
	WORD $0xb400ca28  // cbz	x8, LBB0_906 $6468(%rip)
LBB0_609:
	WORD $0x52800085  // mov	w5, #4
	WORD $0x92f00006  // mov	x6, #9223372036854775807
	WORD $0xb20903f6  // mov	x22, #36028797027352576
	WORD $0xf2800036  // movk	x22, #1
	WORD $0x528000b7  // mov	w23, #5
	WORD $0x528d8c39  // mov	w25, #27745
	WORD $0x72acae79  // movk	w25, #25971, lsl #16
	WORD $0x528eadde  // mov	w30, #30062
	WORD $0x72ad8d9e  // movk	w30, #27756, lsl #16
	WORD $0xb400c8fb  // cbz	x27, LBB0_906 $6428(%rip)
	WORD $0x52800078  // mov	w24, #3
	WORD $0x528e4e82  // mov	w2, #29300
	WORD $0x72acaea2  // movk	w2, #25973, lsl #16
	WORD $0xb400c864  // cbz	x4, LBB0_906 $6412(%rip)
	WORD $0xcb1c0293  // sub	x19, x20, x28
	WORD $0xd1000671  // sub	x17, x19, #1
	WORD $0xeb11011f  // cmp	x8, x17
	WORD $0x54000300  // b.eq	LBB0_619 $96(%rip)
	WORD $0xeb11037f  // cmp	x27, x17
	WORD $0x540002c0  // b.eq	LBB0_619 $88(%rip)
	WORD $0xeb11009f  // cmp	x4, x17
	WORD $0x54000280  // b.eq	LBB0_619 $80(%rip)
	WORD $0xf1000771  // subs	x17, x27, #1
	WORD $0x5400006b  // b.lt	LBB0_616 $12(%rip)
	WORD $0xeb11009f  // cmp	x4, x17
	WORD $0x5400c701  // b.ne	LBB0_907 $6368(%rip)
LBB0_616:
	WORD $0xaa040111  // orr	x17, x8, x4
	WORD $0xb7f80071  // tbnz	x17, #63, LBB0_618 $12(%rip)
	WORD $0xeb04011f  // cmp	x8, x4
	WORD $0x5400c72a  // b.ge	LBB0_909 $6372(%rip)
LBB0_618:
	WORD $0xd37ffe31  // lsr	x17, x17, #63
	WORD $0x52000231  // eor	w17, w17, #0x1
	WORD $0xd1000482  // sub	x2, x4, #1
	WORD $0xeb02011f  // cmp	x8, x2
	WORD $0x528e4e82  // mov	w2, #29300
	WORD $0x72acaea2  // movk	w2, #25973, lsl #16
	WORD $0x1a9f17e8  // cset	w8, eq
	WORD $0x6a08023f  // tst	w17, w8
	WORD $0xda840273  // csinv	x19, x19, x4, eq
	WORD $0xb6f80093  // tbz	x19, #63, LBB0_620 $16(%rip)
	WORD $0x14000628  // b	LBB0_906 $6304(%rip)
LBB0_619:
	WORD $0xcb1303f3  // neg	x19, x19
	WORD $0xb7f8c4d3  // tbnz	x19, #63, LBB0_906 $6296(%rip)
LBB0_620:
	WORD $0x8b13035b  // add	x27, x26, x19
	WORD $0x1400009f  // b	LBB0_650 $636(%rip)
LBB0_621:
	WORD $0x71008a9f  // cmp	w20, #34
	WORD $0x540006a0  // b.eq	LBB0_639 $212(%rip)
	WORD $0x7100b69f  // cmp	w20, #45
	WORD $0x540075c1  // b.ne	LBB0_832 $3768(%rip)
	WORD $0xb20903e2  // mov	x2, #36028797027352576
	WORD $0xf2800022  // movk	x2, #1
	WORD $0xf9400408  // ldr	x8, [x0, #8]
	WORD $0xeb1b0116  // subs	x22, x8, x27
	WORD $0x5400c4e0  // b.eq	LBB0_912 $6300(%rip)
	WORD $0x8b1b0128  // add	x8, x9, x27
	WORD $0x39400111  // ldrb	w17, [x8]
	WORD $0x7100c23f  // cmp	w17, #48
	WORD $0x540014c1  // b.ne	LBB0_655 $664(%rip)
	WORD $0xf10006df  // cmp	x22, #1
	WORD $0x54001381  // b.ne	LBB0_653 $624(%rip)
LBB0_626:
	WORD $0x52800033  // mov	w19, #1
	WORD $0x1400013d  // b	LBB0_701 $1268(%rip)
LBB0_627:
	WORD $0x71016e9f  // cmp	w20, #91
	WORD $0x540003a0  // b.eq	LBB0_637 $116(%rip)
	WORD $0x71019a9f  // cmp	w20, #102
	WORD $0x540073a1  // b.ne	LBB0_832 $3700(%rip)
	WORD $0xf9400408  // ldr	x8, [x0, #8]
	WORD $0xd1001111  // sub	x17, x8, #4
	WORD $0xeb11035f  // cmp	x26, x17
	WORD $0x5400cde2  // b.hs	LBB0_940 $6588(%rip)
	WORD $0xb87b6928  // ldr	w8, [x9, x27]
	WORD $0x6b19011f  // cmp	w8, w25
	WORD $0x5400c301  // b.ne	LBB0_915 $6240(%rip)
	WORD $0x9100175b  // add	x27, x26, #5
	WORD $0x14000081  // b	LBB0_650 $516(%rip)
LBB0_632:
	WORD $0xf9400408  // ldr	x8, [x0, #8]
	WORD $0xd1000d11  // sub	x17, x8, #3
	WORD $0xeb11035f  // cmp	x26, x17
	WORD $0x5400ccc2  // b.hs	LBB0_940 $6552(%rip)
	WORD $0xb87a6928  // ldr	w8, [x9, x26]
	WORD $0x6b02011f  // cmp	w8, w2
	WORD $0x54000120  // b.eq	LBB0_636 $36(%rip)
	WORD $0x14000637  // b	LBB0_924 $6364(%rip)
LBB0_634:
	WORD $0xf9400408  // ldr	x8, [x0, #8]
	WORD $0xd1000d11  // sub	x17, x8, #3
	WORD $0xeb11035f  // cmp	x26, x17
	WORD $0x5400cbc2  // b.hs	LBB0_940 $6520(%rip)
	WORD $0xb87a6928  // ldr	w8, [x9, x26]
	WORD $0x6b1e011f  // cmp	w8, w30
	WORD $0x5400c381  // b.ne	LBB0_920 $6256(%rip)
LBB0_636:
	WORD $0x9100135b  // add	x27, x26, #4
	WORD $0x14000070  // b	LBB0_650 $448(%rip)
LBB0_637:
	WORD $0xf9400068  // ldr	x8, [x3]
	WORD $0xf13ffd1f  // cmp	x8, #4095
	WORD $0x5400bd4c  // b.gt	LBB0_902 $6056(%rip)
	WORD $0x91000511  // add	x17, x8, #1
	WORD $0xf9000071  // str	x17, [x3]
	WORD $0xf8287957  // str	x23, [x10, x8, lsl #3]
	WORD $0x1400006d  // b	LBB0_651 $436(%rip)
LBB0_639:
	WORD $0x3200f3f9  // mov	w25, #1431655765
	WORD $0xf9400408  // ldr	x8, [x0, #8]
	WORD $0xeb1b011e  // subs	lr, x8, x27
	WORD $0x5400c7e0  // b.eq	LBB0_931 $6396(%rip)
	WORD $0xf10103df  // cmp	lr, #64
	WORD $0x540035a3  // b.lo	LBB0_724 $1716(%rip)
	WORD $0xd2800004  // mov	x4, #0
	WORD $0x9280001c  // mov	x28, #-1
LBB0_642:
	WORD $0x8b1b0131  // add	x17, x9, x27
	WORD $0xad405233  // ldp	q19, q20, [x17]
	WORD $0xad415a35  // ldp	q21, q22, [x17, #32]
	WORD $0x6e208e77  // cmeq.16b	v23, v19, v0
	WORD $0x6e208e98  // cmeq.16b	v24, v20, v0
	WORD $0x6e208eb9  // cmeq.16b	v25, v21, v0
	WORD $0x6e208eda  // cmeq.16b	v26, v22, v0
	WORD $0x6e218e73  // cmeq.16b	v19, v19, v1
	WORD $0x6e218e94  // cmeq.16b	v20, v20, v1
	WORD $0x6e218eb5  // cmeq.16b	v21, v21, v1
	WORD $0x6e218ed6  // cmeq.16b	v22, v22, v1
	WORD $0x4e221ef7  // and.16b	v23, v23, v2
	WORD $0x4e0302f7  // tbl.16b	v23, { v23 }, v3
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602f1  // fmov	w17, s23
	WORD $0x4e221f17  // and.16b	v23, v24, v2
	WORD $0x4e0302f7  // tbl.16b	v23, { v23 }, v3
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602e2  // fmov	w2, s23
	WORD $0x4e221f37  // and.16b	v23, v25, v2
	WORD $0x4e0302f7  // tbl.16b	v23, { v23 }, v3
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602e5  // fmov	w5, s23
	WORD $0x4e221f57  // and.16b	v23, v26, v2
	WORD $0x4e0302f7  // tbl.16b	v23, { v23 }, v3
	WORD $0x4e71baf7  // addv.8h	h23, v23
	WORD $0x1e2602e6  // fmov	w6, s23
	WORD $0x4e221e73  // and.16b	v19, v19, v2
	WORD $0x4e030273  // tbl.16b	v19, { v19 }, v3
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260274  // fmov	w20, s19
	WORD $0x4e221e93  // and.16b	v19, v20, v2
	WORD $0x4e030273  // tbl.16b	v19, { v19 }, v3
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260275  // fmov	w21, s19
	WORD $0x4e221eb3  // and.16b	v19, v21, v2
	WORD $0x4e030273  // tbl.16b	v19, { v19 }, v3
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260276  // fmov	w22, s19
	WORD $0x4e221ed3  // and.16b	v19, v22, v2
	WORD $0x4e030273  // tbl.16b	v19, { v19 }, v3
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260277  // fmov	w23, s19
	WORD $0xd3607ca5  // lsl	x5, x5, #32
	WORD $0xaa06c0a5  // orr	x5, x5, x6, lsl #48
	WORD $0x53103c42  // lsl	w2, w2, #16
	WORD $0xaa0200a2  // orr	x2, x5, x2
	WORD $0xaa110053  // orr	x19, x2, x17
	WORD $0xd3607ed1  // lsl	x17, x22, #32
	WORD $0xaa17c231  // orr	x17, x17, x23, lsl #48
	WORD $0x53103ea2  // lsl	w2, w21, #16
	WORD $0xaa020231  // orr	x17, x17, x2
	WORD $0xaa140231  // orr	x17, x17, x20
	WORD $0xb5000151  // cbnz	x17, LBB0_646 $40(%rip)
	WORD $0xb50001c4  // cbnz	x4, LBB0_647 $56(%rip)
	WORD $0x528e4e82  // mov	w2, #29300
	WORD $0x72acaea2  // movk	w2, #25973, lsl #16
	WORD $0xb5000333  // cbnz	x19, LBB0_648 $100(%rip)
LBB0_645:
	WORD $0xd10103de  // sub	lr, lr, #64
	WORD $0x9101037b  // add	x27, x27, #64
	WORD $0xf100ffdf  // cmp	lr, #63
	WORD $0x54fff868  // b.hi	LBB0_642 $-244(%rip)
	WORD $0x14000166  // b	LBB0_722 $1432(%rip)
LBB0_646:
	WORD $0xb100079f  // cmn	x28, #1
	WORD $0xdac00222  // rbit	x2, x17
	WORD $0xdac01042  // clz	x2, x2
	WORD $0x8b1b0042  // add	x2, x2, x27
	WORD $0x9a82139c  // csel	x28, x28, x2, ne
LBB0_647:
	WORD $0x8a240222  // bic	x2, x17, x4
	WORD $0xaa020485  // orr	x5, x4, x2, lsl #1
	WORD $0x8a250231  // bic	x17, x17, x5
	WORD $0x9201f231  // and	x17, x17, #0xaaaaaaaaaaaaaaaa
	WORD $0xab020231  // adds	x17, x17, x2
	WORD $0x1a9f37e4  // cset	w4, hs
	WORD $0xd37ffa31  // lsl	x17, x17, #1
	WORD $0xd200f231  // eor	x17, x17, #0x5555555555555555
	WORD $0x8a050231  // and	x17, x17, x5
	WORD $0x8a310273  // bic	x19, x19, x17
	WORD $0x52800078  // mov	w24, #3
	WORD $0x528e4e82  // mov	w2, #29300
	WORD $0x72acaea2  // movk	w2, #25973, lsl #16
	WORD $0xb4fffd33  // cbz	x19, LBB0_645 $-92(%rip)
LBB0_648:
	WORD $0xdac00271  // rbit	x17, x19
	WORD $0xdac01231  // clz	x17, x17
	WORD $0x8b1b0231  // add	x17, x17, x27
	WORD $0x9100063b  // add	x27, x17, #1
LBB0_649:
	WORD $0x52800085  // mov	w5, #4
	WORD $0x92f00006  // mov	x6, #9223372036854775807
	WORD $0xb20903f6  // mov	x22, #36028797027352576
	WORD $0xf2800036  // movk	x22, #1
	WORD $0x528000b7  // mov	w23, #5
	WORD $0x528d8c39  // mov	w25, #27745
	WORD $0x72acae79  // movk	w25, #25971, lsl #16
	WORD $0x528eadde  // mov	w30, #30062
	WORD $0x72ad8d9e  // movk	w30, #27756, lsl #16
	WORD $0xb7f8b01b  // tbnz	x27, #63, LBB0_903 $5632(%rip)
LBB0_650:
	WORD $0xf900003b  // str	x27, [x1]
	WORD $0xaa1a03e8  // mov	x8, x26
	WORD $0xeb06035f  // cmp	x26, x6
	WORD $0x54006202  // b.hs	LBB0_832 $3136(%rip)
LBB0_651:
	WORD $0xf9400064  // ldr	x4, [x3]
	WORD $0xaa0e03e8  // mov	x8, x14
	WORD $0xb5ffa584  // cbnz	x4, LBB0_494 $-2896(%rip)
	WORD $0x1400030c  // b	LBB0_832 $3120(%rip)
LBB0_652:
	WORD $0xaa1603f4  // mov	x20, x22
	WORD $0x92800013  // mov	x19, #-1
	WORD $0xb5ffe568  // cbnz	x8, LBB0_609 $-852(%rip)
	WORD $0x1400057a  // b	LBB0_906 $5608(%rip)
LBB0_653:
	WORD $0x39400511  // ldrb	w17, [x8, #1]
	WORD $0x5100ba31  // sub	w17, w17, #46
	WORD $0x7100de3f  // cmp	w17, #55
	WORD $0x54ffec48  // b.hi	LBB0_626 $-632(%rip)
	WORD $0x9ad121f1  // lsl	x17, x15, x17
	WORD $0x52800033  // mov	w19, #1
	WORD $0xea02023f  // tst	x17, x2
	WORD $0x54001380  // b.eq	LBB0_701 $624(%rip)
LBB0_655:
	WORD $0xf10042df  // cmp	x22, #16
	WORD $0x54002c03  // b.lo	LBB0_731 $1408(%rip)
	WORD $0xd2800015  // mov	x21, #0
	WORD $0xd2800017  // mov	x23, #0
	WORD $0x9280001c  // mov	x28, #-1
	WORD $0x9280001e  // mov	lr, #-1
	WORD $0x92800004  // mov	x4, #-1
LBB0_657:
	WORD $0x3cf76913  // ldr	q19, [x8, x23]
	WORD $0x6e248e74  // cmeq.16b	v20, v19, v4
	WORD $0x6e258e75  // cmeq.16b	v21, v19, v5
	WORD $0x6e268e76  // cmeq.16b	v22, v19, v6
	WORD $0x4e278677  // add.16b	v23, v19, v7
	WORD $0x6e373617  // cmhi.16b	v23, v16, v23
	WORD $0x4e311e73  // and.16b	v19, v19, v17
	WORD $0x6e328e73  // cmeq.16b	v19, v19, v18
	WORD $0x4eb61eb5  // orr.16b	v21, v21, v22
	WORD $0x4eb41ef6  // orr.16b	v22, v23, v20
	WORD $0x4eb51e77  // orr.16b	v23, v19, v21
	WORD $0x4eb71ed6  // orr.16b	v22, v22, v23
	WORD $0x4e221e94  // and.16b	v20, v20, v2
	WORD $0x4e030294  // tbl.16b	v20, { v20 }, v3
	WORD $0x4e71ba94  // addv.8h	h20, v20
	WORD $0x1e260291  // fmov	w17, s20
	WORD $0x4e221e73  // and.16b	v19, v19, v2
	WORD $0x4e030273  // tbl.16b	v19, { v19 }, v3
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260262  // fmov	w2, s19
	WORD $0x4e221eb3  // and.16b	v19, v21, v2
	WORD $0x4e030273  // tbl.16b	v19, { v19 }, v3
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260274  // fmov	w20, s19
	WORD $0x4e221ed3  // and.16b	v19, v22, v2
	WORD $0x4e030273  // tbl.16b	v19, { v19 }, v3
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260266  // fmov	w6, s19
	WORD $0x2a2603e6  // mvn	w6, w6
	WORD $0x32103cc6  // orr	w6, w6, #0xffff0000
	WORD $0x5ac000c6  // rbit	w6, w6
	WORD $0x5ac010d3  // clz	w19, w6
	WORD $0x1ad320e6  // lsl	w6, w7, w19
	WORD $0x0a260238  // bic	w24, w17, w6
	WORD $0x0a260059  // bic	w25, w2, w6
	WORD $0x0a260285  // bic	w5, w20, w6
	WORD $0x7100427f  // cmp	w19, #16
	WORD $0x1a980231  // csel	w17, w17, w24, eq
	WORD $0x1a990046  // csel	w6, w2, w25, eq
	WORD $0x1a850294  // csel	w20, w20, w5, eq
	WORD $0x51000622  // sub	w2, w17, #1
	WORD $0x6a110059  // ands	w25, w2, w17
	WORD $0x54001f21  // b.ne	LBB0_721 $996(%rip)
	WORD $0x510004c2  // sub	w2, w6, #1
	WORD $0x6a060059  // ands	w25, w2, w6
	WORD $0x54001ec1  // b.ne	LBB0_721 $984(%rip)
	WORD $0x51000682  // sub	w2, w20, #1
	WORD $0x6a140059  // ands	w25, w2, w20
	WORD $0x54001e61  // b.ne	LBB0_721 $972(%rip)
	WORD $0x340000d1  // cbz	w17, LBB0_663 $24(%rip)
	WORD $0x5ac00231  // rbit	w17, w17
	WORD $0x5ac01231  // clz	w17, w17
	WORD $0xb100049f  // cmn	x4, #1
	WORD $0x54001ec1  // b.ne	LBB0_723 $984(%rip)
	WORD $0x8b1102e4  // add	x4, x23, x17
LBB0_663:
	WORD $0x340000c6  // cbz	w6, LBB0_666 $24(%rip)
	WORD $0x5ac000d1  // rbit	w17, w6
	WORD $0x5ac01231  // clz	w17, w17
	WORD $0xb10007df  // cmn	lr, #1
	WORD $0x54001e01  // b.ne	LBB0_723 $960(%rip)
	WORD $0x8b1102fe  // add	lr, x23, x17
LBB0_666:
	WORD $0x340000d4  // cbz	w20, LBB0_669 $24(%rip)
	WORD $0x5ac00291  // rbit	w17, w20
	WORD $0x5ac01231  // clz	w17, w17
	WORD $0xb100079f  // cmn	x28, #1
	WORD $0x54001d41  // b.ne	LBB0_723 $936(%rip)
	WORD $0x8b1102fc  // add	x28, x23, x17
LBB0_669:
	WORD $0x7100427f  // cmp	w19, #16
	WORD $0x540005e1  // b.ne	LBB0_687 $188(%rip)
	WORD $0x910042f7  // add	x23, x23, #16
	WORD $0xd10042b5  // sub	x21, x21, #16
	WORD $0x8b1502d3  // add	x19, x22, x21
	WORD $0xf1003e7f  // cmp	x19, #15
	WORD $0x54fff6e8  // b.hi	LBB0_657 $-292(%rip)
	WORD $0x8b170114  // add	x20, x8, x23
	WORD $0xeb1702df  // cmp	x22, x23
	WORD $0x52800078  // mov	w24, #3
	WORD $0x54000520  // b.eq	LBB0_688 $164(%rip)
LBB0_672:
	WORD $0x8b130282  // add	x2, x20, x19
	WORD $0x8b140191  // add	x17, x12, x20
	WORD $0xcb1a0236  // sub	x22, x17, x26
	WORD $0xaa1403f5  // mov	x21, x20
	WORD $0x14000008  // b	LBB0_676 $32(%rip)
LBB0_673:
	WORD $0xb100079f  // cmn	x28, #1
	WORD $0x540019a1  // b.ne	LBB0_720 $820(%rip)
	WORD $0xd10006dc  // sub	x28, x22, #1
LBB0_675:
	WORD $0x910006d6  // add	x22, x22, #1
	WORD $0xaa1503f4  // mov	x20, x21
	WORD $0xd1000673  // sub	x19, x19, #1
	WORD $0xb4001273  // cbz	x19, LBB0_713 $588(%rip)
LBB0_676:
	WORD $0x384016a6  // ldrb	w6, [x21], #1
	WORD $0x5100c0d1  // sub	w17, w6, #48
	WORD $0x71002a3f  // cmp	w17, #10
	WORD $0x54ffff23  // b.lo	LBB0_675 $-28(%rip)
	WORD $0x7100b4df  // cmp	w6, #45
	WORD $0x5400016d  // b.le	LBB0_683 $44(%rip)
	WORD $0x710194df  // cmp	w6, #101
	WORD $0x540001c0  // b.eq	LBB0_685 $56(%rip)
	WORD $0x710114df  // cmp	w6, #69
	WORD $0x54000180  // b.eq	LBB0_685 $48(%rip)
	WORD $0x7100b8df  // cmp	w6, #46
	WORD $0x54000221  // b.ne	LBB0_688 $68(%rip)
	WORD $0xb100049f  // cmn	x4, #1
	WORD $0x54001741  // b.ne	LBB0_720 $744(%rip)
	WORD $0xd10006c4  // sub	x4, x22, #1
	WORD $0x17ffffed  // b	LBB0_675 $-76(%rip)
LBB0_683:
	WORD $0x7100acdf  // cmp	w6, #43
	WORD $0x54fffd00  // b.eq	LBB0_673 $-96(%rip)
	WORD $0x7100b4df  // cmp	w6, #45
	WORD $0x54fffcc0  // b.eq	LBB0_673 $-104(%rip)
	WORD $0x14000008  // b	LBB0_688 $32(%rip)
LBB0_685:
	WORD $0xb10007df  // cmn	lr, #1
	WORD $0x54001621  // b.ne	LBB0_720 $708(%rip)
	WORD $0xd10006de  // sub	lr, x22, #1
	WORD $0x17ffffe4  // b	LBB0_675 $-112(%rip)
LBB0_687:
	WORD $0x8b334111  // add	x17, x8, w19, uxtw
	WORD $0x8b170234  // add	x20, x17, x23
	WORD $0x52800078  // mov	w24, #3
LBB0_688:
	WORD $0x92800013  // mov	x19, #-1
	WORD $0xb400a004  // cbz	x4, LBB0_913 $5120(%rip)
LBB0_689:
	WORD $0xb4009ffc  // cbz	x28, LBB0_913 $5116(%rip)
	WORD $0xb4009fde  // cbz	lr, LBB0_913 $5112(%rip)
	WORD $0xcb080288  // sub	x8, x20, x8
	WORD $0xd1000511  // sub	x17, x8, #1
	WORD $0xeb11009f  // cmp	x4, x17
	WORD $0x540002a0  // b.eq	LBB0_699 $84(%rip)
	WORD $0xeb11039f  // cmp	x28, x17
	WORD $0x54000260  // b.eq	LBB0_699 $76(%rip)
	WORD $0xeb1103df  // cmp	lr, x17
	WORD $0x54000220  // b.eq	LBB0_699 $68(%rip)
	WORD $0xf1000791  // subs	x17, x28, #1
	WORD $0x5400006b  // b.lt	LBB0_696 $12(%rip)
	WORD $0xeb1103df  // cmp	lr, x17
	WORD $0x54009e61  // b.ne	LBB0_914 $5068(%rip)
LBB0_696:
	WORD $0xaa1e0091  // orr	x17, x4, lr
	WORD $0xb7f80071  // tbnz	x17, #63, LBB0_698 $12(%rip)
	WORD $0xeb1e009f  // cmp	x4, lr
	WORD $0x5400a66a  // b.ge	LBB0_930 $5324(%rip)
LBB0_698:
	WORD $0xd37ffe31  // lsr	x17, x17, #63
	WORD $0x52000231  // eor	w17, w17, #0x1
	WORD $0xd10007c2  // sub	x2, lr, #1
	WORD $0xeb02009f  // cmp	x4, x2
	WORD $0x1a9f17e2  // cset	w2, eq
	WORD $0x6a02023f  // tst	w17, w2
	WORD $0xda9e0113  // csinv	x19, x8, lr, eq
	WORD $0x14000002  // b	LBB0_700 $8(%rip)
LBB0_699:
	WORD $0xcb0803f3  // neg	x19, x8
LBB0_700:
	WORD $0xb7f89c93  // tbnz	x19, #63, LBB0_913 $5008(%rip)
LBB0_701:
	WORD $0x8b1b027b  // add	x27, x19, x27
	WORD $0xf900003b  // str	x27, [x1]
	WORD $0xaa1a03e8  // mov	x8, x26
	WORD $0x92f00006  // mov	x6, #9223372036854775807
	WORD $0xeb06035f  // cmp	x26, x6
	WORD $0x52800085  // mov	w5, #4
	WORD $0xb20903f6  // mov	x22, #36028797027352576
	WORD $0xf2800036  // movk	x22, #1
	WORD $0x528000b7  // mov	w23, #5
	WORD $0x528d8c39  // mov	w25, #27745
	WORD $0x72acae79  // movk	w25, #25971, lsl #16
	WORD $0x528eadde  // mov	w30, #30062
	WORD $0x72ad8d9e  // movk	w30, #27756, lsl #16
	WORD $0x528e4e82  // mov	w2, #29300
	WORD $0x72acaea2  // movk	w2, #25973, lsl #16
	WORD $0x54ffe8c3  // b.lo	LBB0_651 $-744(%rip)
	WORD $0x14000254  // b	LBB0_832 $2384(%rip)
LBB0_702:
	WORD $0x8b1b013b  // add	x27, x9, x27
	WORD $0x14000019  // b	LBB0_707 $100(%rip)
LBB0_703:
	WORD $0x5ac00228  // rbit	w8, w17
	WORD $0x5ac01108  // clz	w8, w8
	WORD $0xaa3e03f1  // mvn	x17, lr
	WORD $0xcb080233  // sub	x19, x17, x8
	WORD $0x14000003  // b	LBB0_705 $12(%rip)
LBB0_704:
	WORD $0xaa3e03e8  // mvn	x8, lr
	WORD $0xcb314113  // sub	x19, x8, w17, uxtw
LBB0_705:
	WORD $0x52800078  // mov	w24, #3
	WORD $0x52800085  // mov	w5, #4
	WORD $0x92f00006  // mov	x6, #9223372036854775807
	WORD $0xb20903f6  // mov	x22, #36028797027352576
	WORD $0xf2800036  // movk	x22, #1
	WORD $0x528000b7  // mov	w23, #5
	WORD $0x528d8c39  // mov	w25, #27745
	WORD $0x72acae79  // movk	w25, #25971, lsl #16
	WORD $0x528eadde  // mov	w30, #30062
	WORD $0x72ad8d9e  // movk	w30, #27756, lsl #16
	WORD $0x528e4e82  // mov	w2, #29300
	WORD $0x72acaea2  // movk	w2, #25973, lsl #16
	WORD $0xb6ffd173  // tbz	x19, #63, LBB0_620 $-1492(%rip)
	WORD $0x140004af  // b	LBB0_906 $4796(%rip)
LBB0_706:
	WORD $0xd2800004  // mov	x4, #0
	WORD $0x8b1b013b  // add	x27, x9, x27
	WORD $0x9280001c  // mov	x28, #-1
LBB0_707:
	WORD $0xf10083d3  // subs	x19, lr, #32
	WORD $0x54001843  // b.lo	LBB0_735 $776(%rip)
	WORD $0xad405373  // ldp	q19, q20, [x27]
	WORD $0x6e208e75  // cmeq.16b	v21, v19, v0
	WORD $0x6e208e96  // cmeq.16b	v22, v20, v0
	WORD $0x6e218e73  // cmeq.16b	v19, v19, v1
	WORD $0x6e218e94  // cmeq.16b	v20, v20, v1
	WORD $0x4e221eb5  // and.16b	v21, v21, v2
	WORD $0x4e0302b5  // tbl.16b	v21, { v21 }, v3
	WORD $0x4e71bab5  // addv.8h	h21, v21
	WORD $0x1e2602b4  // fmov	w20, s21
	WORD $0x4e221ed5  // and.16b	v21, v22, v2
	WORD $0x4e0302b5  // tbl.16b	v21, { v21 }, v3
	WORD $0x4e71bab5  // addv.8h	h21, v21
	WORD $0x1e2602b1  // fmov	w17, s21
	WORD $0x4e221e73  // and.16b	v19, v19, v2
	WORD $0x4e030273  // tbl.16b	v19, { v19 }, v3
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260275  // fmov	w21, s19
	WORD $0x4e221e93  // and.16b	v19, v20, v2
	WORD $0x4e030273  // tbl.16b	v19, { v19 }, v3
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260266  // fmov	w6, s19
	WORD $0x33103e34  // bfi	w20, w17, #16, #16
	WORD $0x33103cd5  // bfi	w21, w6, #16, #16
	WORD $0x35001135  // cbnz	w21, LBB0_732 $548(%rip)
	WORD $0x52800085  // mov	w5, #4
	WORD $0x92f00006  // mov	x6, #9223372036854775807
	WORD $0xb20903f6  // mov	x22, #36028797027352576
	WORD $0xf2800036  // movk	x22, #1
	WORD $0x528000b7  // mov	w23, #5
	WORD $0x528d8c39  // mov	w25, #27745
	WORD $0x72acae79  // movk	w25, #25971, lsl #16
	WORD $0x528eadde  // mov	w30, #30062
	WORD $0x72ad8d9e  // movk	w30, #27756, lsl #16
	WORD $0xb50010a4  // cbnz	x4, LBB0_733 $532(%rip)
	WORD $0xb40013b4  // cbz	x20, LBB0_734 $628(%rip)
LBB0_711:
	WORD $0xdac00291  // rbit	x17, x20
	WORD $0xdac01231  // clz	x17, x17
	WORD $0xcb090364  // sub	x4, x27, x9
	WORD $0x8b110091  // add	x17, x4, x17
	WORD $0x9100063b  // add	x27, x17, #1
	WORD $0x17fffce0  // b	LBB0_531 $-3200(%rip)
LBB0_712:
	WORD $0x8b1b013b  // add	x27, x9, x27
	WORD $0x14000008  // b	LBB0_715 $32(%rip)
LBB0_713:
	WORD $0xaa0203f4  // mov	x20, x2
	WORD $0x92800013  // mov	x19, #-1
	WORD $0xb5fff144  // cbnz	x4, LBB0_689 $-472(%rip)
	WORD $0x14000488  // b	LBB0_913 $4640(%rip)
LBB0_714:
	WORD $0xd2800004  // mov	x4, #0
	WORD $0x8b1b013b  // add	x27, x9, x27
	WORD $0x9280001c  // mov	x28, #-1
LBB0_715:
	WORD $0xf10083d3  // subs	x19, lr, #32
	WORD $0x54001b63  // b.lo	LBB0_747 $876(%rip)
	WORD $0xad405373  // ldp	q19, q20, [x27]
	WORD $0x6e208e75  // cmeq.16b	v21, v19, v0
	WORD $0x6e208e96  // cmeq.16b	v22, v20, v0
	WORD $0x6e218e73  // cmeq.16b	v19, v19, v1
	WORD $0x6e218e94  // cmeq.16b	v20, v20, v1
	WORD $0x4e221eb5  // and.16b	v21, v21, v2
	WORD $0x4e0302b5  // tbl.16b	v21, { v21 }, v3
	WORD $0x4e71bab5  // addv.8h	h21, v21
	WORD $0x1e2602b4  // fmov	w20, s21
	WORD $0x4e221ed5  // and.16b	v21, v22, v2
	WORD $0x4e0302b5  // tbl.16b	v21, { v21 }, v3
	WORD $0x4e71bab5  // addv.8h	h21, v21
	WORD $0x1e2602b1  // fmov	w17, s21
	WORD $0x4e221e73  // and.16b	v19, v19, v2
	WORD $0x4e030273  // tbl.16b	v19, { v19 }, v3
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260275  // fmov	w21, s19
	WORD $0x4e221e93  // and.16b	v19, v20, v2
	WORD $0x4e030273  // tbl.16b	v19, { v19 }, v3
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260266  // fmov	w6, s19
	WORD $0x33103e34  // bfi	w20, w17, #16, #16
	WORD $0x33103cd5  // bfi	w21, w6, #16, #16
	WORD $0x35001535  // cbnz	w21, LBB0_744 $676(%rip)
	WORD $0x92f00006  // mov	x6, #9223372036854775807
	WORD $0xb20903f6  // mov	x22, #36028797027352576
	WORD $0xf2800036  // movk	x22, #1
	WORD $0xb5001564  // cbnz	x4, LBB0_745 $684(%rip)
	WORD $0x528e4e82  // mov	w2, #29300
	WORD $0x72acaea2  // movk	w2, #25973, lsl #16
	WORD $0xb4001754  // cbz	x20, LBB0_746 $744(%rip)
LBB0_719:
	WORD $0xdac00291  // rbit	x17, x20
	WORD $0xdac01231  // clz	x17, x17
	WORD $0xcb090364  // sub	x4, x27, x9
	WORD $0x8b110091  // add	x17, x4, x17
	WORD $0x9100063b  // add	x27, x17, #1
	WORD $0x52800085  // mov	w5, #4
	WORD $0x528000b7  // mov	w23, #5
	WORD $0x528d8c39  // mov	w25, #27745
	WORD $0x72acae79  // movk	w25, #25971, lsl #16
	WORD $0x528eadde  // mov	w30, #30062
	WORD $0x72ad8d9e  // movk	w30, #27756, lsl #16
	WORD $0xb6ffa37b  // tbz	x27, #63, LBB0_547 $-2964(%rip)
	WORD $0x14000444  // b	LBB0_903 $4368(%rip)
LBB0_720:
	WORD $0xcb1603f3  // neg	x19, x22
	WORD $0x17ffff71  // b	LBB0_700 $-572(%rip)
LBB0_721:
	WORD $0x5ac00328  // rbit	w8, w25
	WORD $0x5ac01108  // clz	w8, w8
	WORD $0xaa3703f1  // mvn	x17, x23
	WORD $0xcb080233  // sub	x19, x17, x8
	WORD $0x52800078  // mov	w24, #3
	WORD $0x17ffff6b  // b	LBB0_700 $-596(%rip)
LBB0_722:
	WORD $0x8b1b013b  // add	x27, x9, x27
	WORD $0x14000008  // b	LBB0_725 $32(%rip)
LBB0_723:
	WORD $0xaa3703e8  // mvn	x8, x23
	WORD $0xcb314113  // sub	x19, x8, w17, uxtw
	WORD $0x52800078  // mov	w24, #3
	WORD $0x17ffff65  // b	LBB0_700 $-620(%rip)
LBB0_724:
	WORD $0xd2800004  // mov	x4, #0
	WORD $0x8b1b013b  // add	x27, x9, x27
	WORD $0x9280001c  // mov	x28, #-1
LBB0_725:
	WORD $0xf10083d3  // subs	x19, lr, #32
	WORD $0x54001d23  // b.lo	LBB0_766 $932(%rip)
	WORD $0xad405373  // ldp	q19, q20, [x27]
	WORD $0x6e208e75  // cmeq.16b	v21, v19, v0
	WORD $0x6e208e96  // cmeq.16b	v22, v20, v0
	WORD $0x6e218e73  // cmeq.16b	v19, v19, v1
	WORD $0x6e218e94  // cmeq.16b	v20, v20, v1
	WORD $0x4e221eb5  // and.16b	v21, v21, v2
	WORD $0x4e0302b5  // tbl.16b	v21, { v21 }, v3
	WORD $0x4e71bab5  // addv.8h	h21, v21
	WORD $0x1e2602b4  // fmov	w20, s21
	WORD $0x4e221ed5  // and.16b	v21, v22, v2
	WORD $0x4e0302b5  // tbl.16b	v21, { v21 }, v3
	WORD $0x4e71bab5  // addv.8h	h21, v21
	WORD $0x1e2602b1  // fmov	w17, s21
	WORD $0x4e221e73  // and.16b	v19, v19, v2
	WORD $0x4e030273  // tbl.16b	v19, { v19 }, v3
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260275  // fmov	w21, s19
	WORD $0x4e221e93  // and.16b	v19, v20, v2
	WORD $0x4e030273  // tbl.16b	v19, { v19 }, v3
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260262  // fmov	w2, s19
	WORD $0x33103e34  // bfi	w20, w17, #16, #16
	WORD $0x33103c55  // bfi	w21, w2, #16, #16
	WORD $0x350017b5  // cbnz	w21, LBB0_763 $756(%rip)
	WORD $0xb5001844  // cbnz	x4, LBB0_764 $776(%rip)
	WORD $0xb40019b4  // cbz	x20, LBB0_765 $820(%rip)
LBB0_729:
	WORD $0xdac00291  // rbit	x17, x20
	WORD $0xdac01231  // clz	x17, x17
	WORD $0xcb090362  // sub	x2, x27, x9
	WORD $0x8b110051  // add	x17, x2, x17
	WORD $0x9100063b  // add	x27, x17, #1
	WORD $0x140000e3  // b	LBB0_776 $908(%rip)
LBB0_730:
	WORD $0x92800008  // mov	x8, #-1
	WORD $0xaa1c03f4  // mov	x20, x28
	WORD $0xaa1303f5  // mov	x21, x19
	WORD $0x92800004  // mov	x4, #-1
	WORD $0x9280001b  // mov	x27, #-1
	WORD $0x17fffd6e  // b	LBB0_584 $-2632(%rip)
LBB0_731:
	WORD $0x92800004  // mov	x4, #-1
	WORD $0xaa0803f4  // mov	x20, x8
	WORD $0xaa1603f3  // mov	x19, x22
	WORD $0x9280001e  // mov	lr, #-1
	WORD $0x9280001c  // mov	x28, #-1
	WORD $0x52800078  // mov	w24, #3
	WORD $0x17fffeee  // b	LBB0_672 $-1096(%rip)
LBB0_732:
	WORD $0xdac002b1  // rbit	x17, x21
	WORD $0xdac01231  // clz	x17, x17
	WORD $0xcb090366  // sub	x6, x27, x9
	WORD $0x8b1100d1  // add	x17, x6, x17
	WORD $0xb100079f  // cmn	x28, #1
	WORD $0x9a91139c  // csel	x28, x28, x17, ne
LBB0_733:
	WORD $0x0a2402b1  // bic	w17, w21, w4
	WORD $0x531f7a26  // lsl	w6, w17, #1
	WORD $0x331f7a24  // bfi	w4, w17, #1, #31
	WORD $0x0a2602a6  // bic	w6, w21, w6
	WORD $0x1201f0c6  // and	w6, w6, #0xaaaaaaaa
	WORD $0x2b1100d1  // adds	w17, w6, w17
	WORD $0x3200f3e2  // mov	w2, #1431655765
	WORD $0x4a110451  // eor	w17, w2, w17, lsl #1
	WORD $0x0a040231  // and	w17, w17, w4
	WORD $0x1a9f37e4  // cset	w4, hs
	WORD $0x2a3103f1  // mvn	w17, w17
	WORD $0x8a140234  // and	x20, x17, x20
	WORD $0x52800078  // mov	w24, #3
	WORD $0x52800085  // mov	w5, #4
	WORD $0x92f00006  // mov	x6, #9223372036854775807
	WORD $0xb20903f6  // mov	x22, #36028797027352576
	WORD $0xf2800036  // movk	x22, #1
	WORD $0x528000b7  // mov	w23, #5
	WORD $0x528d8c39  // mov	w25, #27745
	WORD $0x72acae79  // movk	w25, #25971, lsl #16
	WORD $0x528eadde  // mov	w30, #30062
	WORD $0x72ad8d9e  // movk	w30, #27756, lsl #16
	WORD $0x528e4e82  // mov	w2, #29300
	WORD $0x72acaea2  // movk	w2, #25973, lsl #16
	WORD $0xb5ffecb4  // cbnz	x20, LBB0_711 $-620(%rip)
LBB0_734:
	WORD $0x9100837b  // add	x27, x27, #32
	WORD $0xaa1303fe  // mov	lr, x19
LBB0_735:
	WORD $0xb5000d24  // cbnz	x4, LBB0_757 $420(%rip)
	WORD $0xb4000e5e  // cbz	lr, LBB0_759 $456(%rip)
LBB0_737:
	WORD $0xaa1b03f1  // mov	x17, x27
	WORD $0x38401624  // ldrb	w4, [x17], #1
	WORD $0x7100889f  // cmp	w4, #34
	WORD $0x540003e0  // b.eq	LBB0_743 $124(%rip)
	WORD $0xd10007c6  // sub	x6, lr, #1
	WORD $0x7101709f  // cmp	w4, #92
	WORD $0x540000a0  // b.eq	LBB0_740 $20(%rip)
	WORD $0xaa1103fb  // mov	x27, x17
	WORD $0xaa0603fe  // mov	lr, x6
	WORD $0xb5fffee6  // cbnz	x6, LBB0_737 $-36(%rip)
	WORD $0x14000009  // b	LBB0_742 $36(%rip)
LBB0_740:
	WORD $0xb4008626  // cbz	x6, LBB0_932 $4292(%rip)
	WORD $0x8b0b0231  // add	x17, x17, x11
	WORD $0xb100079f  // cmn	x28, #1
	WORD $0x9a9c023c  // csel	x28, x17, x28, eq
	WORD $0x91000b7b  // add	x27, x27, #2
	WORD $0xd1000bc6  // sub	x6, lr, #2
	WORD $0xaa0603fe  // mov	lr, x6
	WORD $0xb5fffdc6  // cbnz	x6, LBB0_737 $-72(%rip)
LBB0_742:
	WORD $0x7100889f  // cmp	w4, #34
	WORD $0x52800078  // mov	w24, #3
	WORD $0x52800085  // mov	w5, #4
	WORD $0x92f00006  // mov	x6, #9223372036854775807
	WORD $0xb20903f6  // mov	x22, #36028797027352576
	WORD $0xf2800036  // movk	x22, #1
	WORD $0x528000b7  // mov	w23, #5
	WORD $0x528d8c39  // mov	w25, #27745
	WORD $0x72acae79  // movk	w25, #25971, lsl #16
	WORD $0x528eadde  // mov	w30, #30062
	WORD $0x72ad8d9e  // movk	w30, #27756, lsl #16
	WORD $0x528e4e82  // mov	w2, #29300
	WORD $0x72acaea2  // movk	w2, #25973, lsl #16
	WORD $0x54000b40  // b.eq	LBB0_760 $360(%rip)
	WORD $0x1400041b  // b	LBB0_932 $4204(%rip)
LBB0_743:
	WORD $0xaa1103fb  // mov	x27, x17
	WORD $0x52800078  // mov	w24, #3
	WORD $0x52800085  // mov	w5, #4
	WORD $0x92f00006  // mov	x6, #9223372036854775807
	WORD $0xb20903f6  // mov	x22, #36028797027352576
	WORD $0xf2800036  // movk	x22, #1
	WORD $0x528000b7  // mov	w23, #5
	WORD $0x528d8c39  // mov	w25, #27745
	WORD $0x72acae79  // movk	w25, #25971, lsl #16
	WORD $0x528eadde  // mov	w30, #30062
	WORD $0x72ad8d9e  // movk	w30, #27756, lsl #16
	WORD $0x528e4e82  // mov	w2, #29300
	WORD $0x72acaea2  // movk	w2, #25973, lsl #16
	WORD $0xcb09023b  // sub	x27, x17, x9
	WORD $0x17fffc15  // b	LBB0_531 $-4012(%rip)
LBB0_744:
	WORD $0xdac002b1  // rbit	x17, x21
	WORD $0xdac01231  // clz	x17, x17
	WORD $0xcb090366  // sub	x6, x27, x9
	WORD $0x8b1100d1  // add	x17, x6, x17
	WORD $0xb100079f  // cmn	x28, #1
	WORD $0x9a91139c  // csel	x28, x28, x17, ne
LBB0_745:
	WORD $0x0a2402b1  // bic	w17, w21, w4
	WORD $0x531f7a26  // lsl	w6, w17, #1
	WORD $0x331f7a24  // bfi	w4, w17, #1, #31
	WORD $0x0a2602a6  // bic	w6, w21, w6
	WORD $0x1201f0c6  // and	w6, w6, #0xaaaaaaaa
	WORD $0x2b1100d1  // adds	w17, w6, w17
	WORD $0x4a110451  // eor	w17, w2, w17, lsl #1
	WORD $0x0a040231  // and	w17, w17, w4
	WORD $0x1a9f37e4  // cset	w4, hs
	WORD $0x2a3103f1  // mvn	w17, w17
	WORD $0x8a140234  // and	x20, x17, x20
	WORD $0x52800078  // mov	w24, #3
	WORD $0x92f00006  // mov	x6, #9223372036854775807
	WORD $0xb20903f6  // mov	x22, #36028797027352576
	WORD $0xf2800036  // movk	x22, #1
	WORD $0x528e4e82  // mov	w2, #29300
	WORD $0x72acaea2  // movk	w2, #25973, lsl #16
	WORD $0xb5ffe914  // cbnz	x20, LBB0_719 $-736(%rip)
LBB0_746:
	WORD $0x9100837b  // add	x27, x27, #32
	WORD $0xaa1303fe  // mov	lr, x19
LBB0_747:
	WORD $0xb5000624  // cbnz	x4, LBB0_761 $196(%rip)
	WORD $0xb400031e  // cbz	lr, LBB0_756 $96(%rip)
LBB0_749:
	WORD $0xaa1b03f1  // mov	x17, x27
	WORD $0x38401624  // ldrb	w4, [x17], #1
	WORD $0x7100889f  // cmp	w4, #34
	WORD $0x54000260  // b.eq	LBB0_755 $76(%rip)
	WORD $0xd10007c6  // sub	x6, lr, #1
	WORD $0x7101709f  // cmp	w4, #92
	WORD $0x540000a0  // b.eq	LBB0_752 $20(%rip)
	WORD $0xaa1103fb  // mov	x27, x17
	WORD $0xaa0603fe  // mov	lr, x6
	WORD $0xb5fffee6  // cbnz	x6, LBB0_749 $-36(%rip)
	WORD $0x14000009  // b	LBB0_754 $36(%rip)
LBB0_752:
	WORD $0xb4007c86  // cbz	x6, LBB0_932 $3984(%rip)
	WORD $0x8b0b0231  // add	x17, x17, x11
	WORD $0xb100079f  // cmn	x28, #1
	WORD $0x9a9c023c  // csel	x28, x17, x28, eq
	WORD $0x91000b7b  // add	x27, x27, #2
	WORD $0xd1000bc6  // sub	x6, lr, #2
	WORD $0xaa0603fe  // mov	lr, x6
	WORD $0xb5fffdc6  // cbnz	x6, LBB0_749 $-72(%rip)
LBB0_754:
	WORD $0x7100889f  // cmp	w4, #34
	WORD $0x54000060  // b.eq	LBB0_756 $12(%rip)
	WORD $0x140003da  // b	LBB0_932 $3944(%rip)
LBB0_755:
	WORD $0xaa1103fb  // mov	x27, x17
LBB0_756:
	WORD $0xcb09037b  // sub	x27, x27, x9
	WORD $0x52800078  // mov	w24, #3
	WORD $0x17fffc44  // b	LBB0_546 $-3824(%rip)
LBB0_757:
	WORD $0xb4007abe  // cbz	lr, LBB0_932 $3924(%rip)
	WORD $0x8b0b0371  // add	x17, x27, x11
	WORD $0xb100079f  // cmn	x28, #1
	WORD $0x9a9c023c  // csel	x28, x17, x28, eq
	WORD $0x9100077b  // add	x27, x27, #1
	WORD $0xd10007de  // sub	lr, lr, #1
	WORD $0x52800078  // mov	w24, #3
	WORD $0x528e4e82  // mov	w2, #29300
	WORD $0x72acaea2  // movk	w2, #25973, lsl #16
	WORD $0xb5fff21e  // cbnz	lr, LBB0_737 $-448(%rip)
LBB0_759:
	WORD $0x52800085  // mov	w5, #4
	WORD $0x92f00006  // mov	x6, #9223372036854775807
	WORD $0xb20903f6  // mov	x22, #36028797027352576
	WORD $0xf2800036  // movk	x22, #1
	WORD $0x528000b7  // mov	w23, #5
	WORD $0x528d8c39  // mov	w25, #27745
	WORD $0x72acae79  // movk	w25, #25971, lsl #16
	WORD $0x528eadde  // mov	w30, #30062
	WORD $0x72ad8d9e  // movk	w30, #27756, lsl #16
LBB0_760:
	WORD $0xcb09037b  // sub	x27, x27, x9
	WORD $0x17fffbca  // b	LBB0_531 $-4312(%rip)
LBB0_761:
	WORD $0xb400781e  // cbz	lr, LBB0_932 $3840(%rip)
	WORD $0x8b0b0371  // add	x17, x27, x11
	WORD $0xb100079f  // cmn	x28, #1
	WORD $0x9a9c023c  // csel	x28, x17, x28, eq
	WORD $0x9100077b  // add	x27, x27, #1
	WORD $0xd10007de  // sub	lr, lr, #1
	WORD $0xb5fff97e  // cbnz	lr, LBB0_749 $-212(%rip)
	WORD $0x17ffffe1  // b	LBB0_756 $-124(%rip)
LBB0_763:
	WORD $0xdac002b1  // rbit	x17, x21
	WORD $0xdac01231  // clz	x17, x17
	WORD $0xcb090362  // sub	x2, x27, x9
	WORD $0x8b110051  // add	x17, x2, x17
	WORD $0xb100079f  // cmn	x28, #1
	WORD $0x9a91139c  // csel	x28, x28, x17, ne
LBB0_764:
	WORD $0x0a2402b1  // bic	w17, w21, w4
	WORD $0x531f7a22  // lsl	w2, w17, #1
	WORD $0x331f7a24  // bfi	w4, w17, #1, #31
	WORD $0x0a2202a2  // bic	w2, w21, w2
	WORD $0x1201f042  // and	w2, w2, #0xaaaaaaaa
	WORD $0x2b110051  // adds	w17, w2, w17
	WORD $0x4a110731  // eor	w17, w25, w17, lsl #1
	WORD $0x0a040231  // and	w17, w17, w4
	WORD $0x1a9f37e4  // cset	w4, hs
	WORD $0x2a3103f1  // mvn	w17, w17
	WORD $0x8a140234  // and	x20, x17, x20
	WORD $0xb5ffe6b4  // cbnz	x20, LBB0_729 $-812(%rip)
LBB0_765:
	WORD $0x9100837b  // add	x27, x27, #32
	WORD $0xaa1303fe  // mov	lr, x19
LBB0_766:
	WORD $0xb50003c4  // cbnz	x4, LBB0_777 $120(%rip)
	WORD $0xb400031e  // cbz	lr, LBB0_775 $96(%rip)
LBB0_768:
	WORD $0xaa1b03f1  // mov	x17, x27
	WORD $0x38401624  // ldrb	w4, [x17], #1
	WORD $0x7100889f  // cmp	w4, #34
	WORD $0x54000260  // b.eq	LBB0_774 $76(%rip)
	WORD $0xd10007c6  // sub	x6, lr, #1
	WORD $0x7101709f  // cmp	w4, #92
	WORD $0x540000a0  // b.eq	LBB0_771 $20(%rip)
	WORD $0xaa1103fb  // mov	x27, x17
	WORD $0xaa0603fe  // mov	lr, x6
	WORD $0xb5fffee6  // cbnz	x6, LBB0_768 $-36(%rip)
	WORD $0x14000009  // b	LBB0_773 $36(%rip)
LBB0_771:
	WORD $0xb40072e6  // cbz	x6, LBB0_932 $3676(%rip)
	WORD $0x8b0b0231  // add	x17, x17, x11
	WORD $0xb100079f  // cmn	x28, #1
	WORD $0x9a9c023c  // csel	x28, x17, x28, eq
	WORD $0x91000b7b  // add	x27, x27, #2
	WORD $0xd1000bc6  // sub	x6, lr, #2
	WORD $0xaa0603fe  // mov	lr, x6
	WORD $0xb5fffdc6  // cbnz	x6, LBB0_768 $-72(%rip)
LBB0_773:
	WORD $0x7100889f  // cmp	w4, #34
	WORD $0x54000060  // b.eq	LBB0_775 $12(%rip)
	WORD $0x1400038d  // b	LBB0_932 $3636(%rip)
LBB0_774:
	WORD $0xaa1103fb  // mov	x27, x17
LBB0_775:
	WORD $0xcb09037b  // sub	x27, x27, x9
LBB0_776:
	WORD $0x52800078  // mov	w24, #3
	WORD $0x528e4e82  // mov	w2, #29300
	WORD $0x72acaea2  // movk	w2, #25973, lsl #16
	WORD $0x17fffda2  // b	LBB0_649 $-2424(%rip)
LBB0_777:
	WORD $0xb40070de  // cbz	lr, LBB0_932 $3608(%rip)
	WORD $0x8b0b0371  // add	x17, x27, x11
	WORD $0xb100079f  // cmn	x28, #1
	WORD $0x9a9c023c  // csel	x28, x17, x28, eq
	WORD $0x9100077b  // add	x27, x27, #1
	WORD $0xd10007de  // sub	lr, lr, #1
	WORD $0xb5fffbde  // cbnz	lr, LBB0_768 $-136(%rip)
	WORD $0x17fffff4  // b	LBB0_775 $-48(%rip)
LBB0_779:
	WORD $0xf940002b  // ldr	x11, [x1]
	WORD $0xa940200a  // ldp	x10, x8, [x0]
	WORD $0xeb08017f  // cmp	x11, x8
	WORD $0x54000142  // b.hs	LBB0_783 $40(%rip)
	WORD $0x386b6949  // ldrb	w9, [x10, x11]
	WORD $0x7100353f  // cmp	w9, #13
	WORD $0x540000e0  // b.eq	LBB0_783 $28(%rip)
	WORD $0x7100813f  // cmp	w9, #32
	WORD $0x540000a0  // b.eq	LBB0_783 $20(%rip)
	WORD $0x51002d2c  // sub	w12, w9, #11
	WORD $0xaa0b03e9  // mov	x9, x11
	WORD $0x3100099f  // cmn	w12, #2
	WORD $0x54000883  // b.lo	LBB0_806 $272(%rip)
LBB0_783:
	WORD $0x91000569  // add	x9, x11, #1
	WORD $0xeb08013f  // cmp	x9, x8
	WORD $0x54000122  // b.hs	LBB0_787 $36(%rip)
	WORD $0x3869694c  // ldrb	w12, [x10, x9]
	WORD $0x7100359f  // cmp	w12, #13
	WORD $0x540000c0  // b.eq	LBB0_787 $24(%rip)
	WORD $0x7100819f  // cmp	w12, #32
	WORD $0x54000080  // b.eq	LBB0_787 $16(%rip)
	WORD $0x51002d8c  // sub	w12, w12, #11
	WORD $0x3100099f  // cmn	w12, #2
	WORD $0x54000723  // b.lo	LBB0_806 $228(%rip)
LBB0_787:
	WORD $0x91000969  // add	x9, x11, #2
	WORD $0xeb08013f  // cmp	x9, x8
	WORD $0x54000122  // b.hs	LBB0_791 $36(%rip)
	WORD $0x3869694c  // ldrb	w12, [x10, x9]
	WORD $0x7100359f  // cmp	w12, #13
	WORD $0x540000c0  // b.eq	LBB0_791 $24(%rip)
	WORD $0x7100819f  // cmp	w12, #32
	WORD $0x54000080  // b.eq	LBB0_791 $16(%rip)
	WORD $0x51002d8c  // sub	w12, w12, #11
	WORD $0x3100099f  // cmn	w12, #2
	WORD $0x540005c3  // b.lo	LBB0_806 $184(%rip)
LBB0_791:
	WORD $0x91000d69  // add	x9, x11, #3
	WORD $0xeb08013f  // cmp	x9, x8
	WORD $0x54000122  // b.hs	LBB0_795 $36(%rip)
	WORD $0x3869694c  // ldrb	w12, [x10, x9]
	WORD $0x7100359f  // cmp	w12, #13
	WORD $0x540000c0  // b.eq	LBB0_795 $24(%rip)
	WORD $0x7100819f  // cmp	w12, #32
	WORD $0x54000080  // b.eq	LBB0_795 $16(%rip)
	WORD $0x51002d8c  // sub	w12, w12, #11
	WORD $0x3100099f  // cmn	w12, #2
	WORD $0x54000463  // b.lo	LBB0_806 $140(%rip)
LBB0_795:
	WORD $0x91001169  // add	x9, x11, #4
	WORD $0xeb08013f  // cmp	x9, x8
	WORD $0x540001e2  // b.hs	LBB0_800 $60(%rip)
	WORD $0x5280002b  // mov	w11, #1
	WORD $0xd284c00c  // mov	x12, #9728
	WORD $0xf2c0002c  // movk	x12, #1, lsl #32
LBB0_797:
	WORD $0x3869694d  // ldrb	w13, [x10, x9]
	WORD $0x710081bf  // cmp	w13, #32
	WORD $0x9acd216d  // lsl	x13, x11, x13
	WORD $0x8a0c01ad  // and	x13, x13, x12
	WORD $0xfa4099a4  // ccmp	x13, #0, #4, ls
	WORD $0x540002a0  // b.eq	LBB0_805 $84(%rip)
	WORD $0x91000529  // add	x9, x9, #1
	WORD $0xeb09011f  // cmp	x8, x9
	WORD $0x54ffff01  // b.ne	LBB0_797 $-32(%rip)
LBB0_799:
	WORD $0x92800008  // mov	x8, #-1
	WORD $0x14000078  // b	LBB0_832 $480(%rip)
LBB0_800:
	WORD $0xf9000029  // str	x9, [x1]
	WORD $0x92800008  // mov	x8, #-1
	WORD $0x14000075  // b	LBB0_832 $468(%rip)
LBB0_801:
	WORD $0xf9400028  // ldr	x8, [x1]
	WORD $0xd1000508  // sub	x8, x8, #1
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0x92800428  // mov	x8, #-34
	WORD $0x14000070  // b	LBB0_832 $448(%rip)
LBB0_802:
	WORD $0xf9000035  // str	x21, [x1]
	WORD $0x1400006a  // b	LBB0_829 $424(%rip)
LBB0_803:
	WORD $0x7101745f  // cmp	w2, #93
	WORD $0x54000d01  // b.ne	LBB0_829 $416(%rip)
LBB0_804:
	WORD $0xf9000035  // str	x21, [x1]
	WORD $0x92800408  // mov	x8, #-33
	WORD $0x14000069  // b	LBB0_832 $420(%rip)
LBB0_805:
	WORD $0xeb08013f  // cmp	x9, x8
	WORD $0x54fffdc2  // b.hs	LBB0_799 $-72(%rip)
LBB0_806:
	WORD $0x91000530  // add	x16, x9, #1
	WORD $0xf9000030  // str	x16, [x1]
	WORD $0x38696948  // ldrb	w8, [x10, x9]
	WORD $0x7101691f  // cmp	w8, #90
	WORD $0x540006ec  // b.gt	LBB0_823 $220(%rip)
	WORD $0x7100bd1f  // cmp	w8, #47
	WORD $0x54000d8d  // b.le	LBB0_834 $432(%rip)
	WORD $0x5100c108  // sub	w8, w8, #48
	WORD $0x7100291f  // cmp	w8, #10
	WORD $0x540057e2  // b.hs	LBB0_900 $2812(%rip)
LBB0_809:
	WORD $0xf9400408  // ldr	x8, [x0, #8]
	WORD $0xcb100108  // sub	x8, x8, x16
	WORD $0xf100411f  // cmp	x8, #16
	WORD $0x540002c3  // b.lo	LBB0_813 $88(%rip)
	WORD $0x4f01e580  // movi.16b	v0, #44
	WORD $0x4f06e7e1  // movi.16b	v1, #223
	WORD $0x4f02e7a2  // movi.16b	v2, #93
Lloh28:
	WORD $0x10fe020b  // adr	x11, lCPI0_0 $-16320(%rip)
Lloh29:
	WORD $0x3dc00163  // ldr	q3, [x11, lCPI0_0@PAGEOFF] $0(%rip)
Lloh30:
	WORD $0x10fe024b  // adr	x11, lCPI0_1 $-16312(%rip)
Lloh31:
	WORD $0x3dc00164  // ldr	q4, [x11, lCPI0_1@PAGEOFF] $0(%rip)
LBB0_811:
	WORD $0x3cf06945  // ldr	q5, [x10, x16]
	WORD $0x6e208ca6  // cmeq.16b	v6, v5, v0
	WORD $0x4e211ca5  // and.16b	v5, v5, v1
	WORD $0x6e228ca5  // cmeq.16b	v5, v5, v2
	WORD $0x4ea61ca5  // orr.16b	v5, v5, v6
	WORD $0x4e231ca5  // and.16b	v5, v5, v3
	WORD $0x4e0400a5  // tbl.16b	v5, { v5 }, v4
	WORD $0x4e71b8a5  // addv.8h	h5, v5
	WORD $0x1e2600ab  // fmov	w11, s5
	WORD $0x350002eb  // cbnz	w11, LBB0_821 $92(%rip)
	WORD $0xd1004108  // sub	x8, x8, #16
	WORD $0x91004210  // add	x16, x16, #16
	WORD $0xf1003d1f  // cmp	x8, #15
	WORD $0x54fffe68  // b.hi	LBB0_811 $-52(%rip)
LBB0_813:
	WORD $0x8b10014b  // add	x11, x10, x16
	WORD $0xb40001e8  // cbz	x8, LBB0_820 $60(%rip)
	WORD $0x8b08016c  // add	x12, x11, x8
	WORD $0xcb0a016d  // sub	x13, x11, x10
LBB0_815:
	WORD $0x3940016e  // ldrb	w14, [x11]
	WORD $0x7100b1df  // cmp	w14, #44
	WORD $0x54005640  // b.eq	LBB0_908 $2760(%rip)
	WORD $0x7101f5df  // cmp	w14, #125
	WORD $0x54005600  // b.eq	LBB0_908 $2752(%rip)
	WORD $0x710175df  // cmp	w14, #93
	WORD $0x540055c0  // b.eq	LBB0_908 $2744(%rip)
	WORD $0x9100056b  // add	x11, x11, #1
	WORD $0x910005ad  // add	x13, x13, #1
	WORD $0xf1000508  // subs	x8, x8, #1
	WORD $0x54fffec1  // b.ne	LBB0_815 $-40(%rip)
	WORD $0xaa0c03eb  // mov	x11, x12
LBB0_820:
	WORD $0xcb0a0168  // sub	x8, x11, x10
	WORD $0x14000004  // b	LBB0_822 $16(%rip)
LBB0_821:
	WORD $0x5ac00168  // rbit	w8, w11
	WORD $0x5ac01108  // clz	w8, w8
	WORD $0x8b100108  // add	x8, x8, x16
LBB0_822:
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0xaa0903e8  // mov	x8, x9
	WORD $0x1400002c  // b	LBB0_832 $176(%rip)
LBB0_823:
	WORD $0x7101b51f  // cmp	w8, #109
	WORD $0x5400078d  // b.le	LBB0_837 $240(%rip)
	WORD $0x7101b91f  // cmp	w8, #110
	WORD $0x54002560  // b.eq	LBB0_864 $1196(%rip)
	WORD $0x7101d11f  // cmp	w8, #116
	WORD $0x54002520  // b.eq	LBB0_864 $1188(%rip)
	WORD $0x7101ed1f  // cmp	w8, #123
	WORD $0x540050c1  // b.ne	LBB0_900 $2584(%rip)
	WORD $0xd2800007  // mov	x7, #0
	WORD $0xd280000f  // mov	x15, #0
	WORD $0xd2800008  // mov	x8, #0
	WORD $0xd280000b  // mov	x11, #0
	WORD $0xb201e3ec  // mov	x12, #-8608480567731124088
	WORD $0xf2e1110c  // movk	x12, #2184, lsl #48
	WORD $0xb202e3ed  // mov	x13, #4919131752989213764
	WORD $0xf2e0888d  // movk	x13, #1092, lsl #48
	WORD $0xb203e3ee  // mov	x14, #2459565876494606882
	WORD $0xf2e0444e  // movk	x14, #546, lsl #48
	WORD $0xf9400411  // ldr	x17, [x0, #8]
	WORD $0xcb100225  // sub	x5, x17, x16
	WORD $0x8b100150  // add	x16, x10, x16
	WORD $0x910083ea  // add	x10, sp, #32
	WORD $0x9100814a  // add	x10, x10, #32
	WORD $0x4f01e440  // movi.16b	v0, #34
Lloh32:
	WORD $0x10fdf9d1  // adr	x17, lCPI0_0 $-16584(%rip)
Lloh33:
	WORD $0x3dc00221  // ldr	q1, [x17, lCPI0_0@PAGEOFF] $0(%rip)
Lloh34:
	WORD $0x10fdfa11  // adr	x17, lCPI0_1 $-16576(%rip)
Lloh35:
	WORD $0x3dc00222  // ldr	q2, [x17, lCPI0_1@PAGEOFF] $0(%rip)
	WORD $0x4f02e783  // movi.16b	v3, #92
	WORD $0xb200e3f1  // mov	x17, #1229782938247303441
	WORD $0xb203e3e2  // mov	x2, #2459565876494606882
	WORD $0xb202e3e3  // mov	x3, #4919131752989213764
	WORD $0xb201e3e4  // mov	x4, #-8608480567731124088
	WORD $0x4f03e764  // movi.16b	v4, #123
	WORD $0x4f03e7a5  // movi.16b	v5, #125
	WORD $0x6f00e406  // movi.2d	v6, #0000000000000000
	WORD $0x1400002a  // b	LBB0_841 $168(%rip)
LBB0_828:
	WORD $0x7101f45f  // cmp	w2, #125
	WORD $0x54fff340  // b.eq	LBB0_804 $-408(%rip)
LBB0_829:
	WORD $0xf9400028  // ldr	x8, [x1]
	WORD $0xd1000508  // sub	x8, x8, #1
LBB0_830:
	WORD $0xf9000028  // str	x8, [x1]
LBB0_831:
	WORD $0x92800028  // mov	x8, #-2
LBB0_832:
	WORD $0xaa0803e0  // mov	x0, x8
	WORD $0xa94bfbfd  // ldp	fp, lr, [sp, #184]
	WORD $0xa94acff4  // ldp	x20, x19, [sp, #168]
	WORD $0xa949d7f6  // ldp	x22, x21, [sp, #152]
	WORD $0xa948dff8  // ldp	x24, x23, [sp, #136]
	WORD $0xa947e7fa  // ldp	x26, x25, [sp, #120]
	WORD $0xa946effc  // ldp	x28, x27, [sp, #104]
	WORD $0x910343ff  // add	sp, sp, #208
	WORD $0xd65f03c0  // ret
LBB0_833:
	WORD $0xf900003a  // str	x26, [x1]
	WORD $0x92800008  // mov	x8, #-1
	WORD $0x17fffff5  // b	LBB0_832 $-44(%rip)
LBB0_834:
	WORD $0x34ffef68  // cbz	w8, LBB0_799 $-532(%rip)
	WORD $0x7100891f  // cmp	w8, #34
	WORD $0x54001f40  // b.eq	LBB0_865 $1000(%rip)
	WORD $0x7100b51f  // cmp	w8, #45
	WORD $0x54fff280  // b.eq	LBB0_809 $-432(%rip)
	WORD $0x14000251  // b	LBB0_900 $2372(%rip)
LBB0_837:
	WORD $0x71016d1f  // cmp	w8, #91
	WORD $0x540027a0  // b.eq	LBB0_874 $1268(%rip)
	WORD $0x7101991f  // cmp	w8, #102
	WORD $0x540049a1  // b.ne	LBB0_900 $2356(%rip)
	WORD $0x91001528  // add	x8, x9, #5
	WORD $0xf940040a  // ldr	x10, [x0, #8]
	WORD $0xeb0a011f  // cmp	x8, x10
	WORD $0x54ffedc8  // b.hi	LBB0_799 $-584(%rip)
	WORD $0x17ffffb8  // b	LBB0_822 $-288(%rip)
LBB0_840:
	WORD $0x937ffce7  // asr	x7, x7, #63
	WORD $0x9e670267  // fmov	d7, x19
	WORD $0x0e2058e7  // cnt.8b	v7, v7
	WORD $0x2e3038e7  // uaddlv.8b	h7, v7
	WORD $0x1e2600e5  // fmov	w5, s7
	WORD $0x8b0800a8  // add	x8, x5, x8
	WORD $0x91010210  // add	x16, x16, #64
	WORD $0xaa0603e5  // mov	x5, x6
LBB0_841:
	WORD $0xf10100a6  // subs	x6, x5, #64
	WORD $0x540015cb  // b.lt	LBB0_848 $696(%rip)
LBB0_842:
	WORD $0xad404612  // ldp	q18, q17, [x16]
	WORD $0xad411e10  // ldp	q16, q7, [x16, #32]
	WORD $0x6e238e53  // cmeq.16b	v19, v18, v3
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260273  // fmov	w19, s19
	WORD $0x6e238e33  // cmeq.16b	v19, v17, v3
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260274  // fmov	w20, s19
	WORD $0x6e238e13  // cmeq.16b	v19, v16, v3
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260275  // fmov	w21, s19
	WORD $0x6e238cf3  // cmeq.16b	v19, v7, v3
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260276  // fmov	w22, s19
	WORD $0xd3607eb5  // lsl	x21, x21, #32
	WORD $0xaa16c2b5  // orr	x21, x21, x22, lsl #48
	WORD $0x53103e94  // lsl	w20, w20, #16
	WORD $0xaa1402b4  // orr	x20, x21, x20
	WORD $0xaa130293  // orr	x19, x20, x19
	WORD $0xaa0f0274  // orr	x20, x19, x15
	WORD $0xb5000094  // cbnz	x20, LBB0_844 $16(%rip)
	WORD $0xd280000f  // mov	x15, #0
	WORD $0xd2800013  // mov	x19, #0
	WORD $0x1400000a  // b	LBB0_845 $40(%rip)
LBB0_844:
	WORD $0x8a2f0274  // bic	x20, x19, x15
	WORD $0xaa1405f5  // orr	x21, x15, x20, lsl #1
	WORD $0x8a35026f  // bic	x15, x19, x21
	WORD $0x9201f1ef  // and	x15, x15, #0xaaaaaaaaaaaaaaaa
	WORD $0xab1401f3  // adds	x19, x15, x20
	WORD $0x1a9f37ef  // cset	w15, hs
	WORD $0xd37ffa73  // lsl	x19, x19, #1
	WORD $0xd200f273  // eor	x19, x19, #0x5555555555555555
	WORD $0x8a150273  // and	x19, x19, x21
LBB0_845:
	WORD $0x6e208e53  // cmeq.16b	v19, v18, v0
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260274  // fmov	w20, s19
	WORD $0x6e208e33  // cmeq.16b	v19, v17, v0
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260275  // fmov	w21, s19
	WORD $0x6e208e13  // cmeq.16b	v19, v16, v0
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260276  // fmov	w22, s19
	WORD $0x6e208cf3  // cmeq.16b	v19, v7, v0
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260277  // fmov	w23, s19
	WORD $0xd3607ed6  // lsl	x22, x22, #32
	WORD $0xaa17c2d6  // orr	x22, x22, x23, lsl #48
	WORD $0x53103eb5  // lsl	w21, w21, #16
	WORD $0xaa1502d5  // orr	x21, x22, x21
	WORD $0xaa1402b4  // orr	x20, x21, x20
	WORD $0x8a330293  // bic	x19, x20, x19
	WORD $0x9200e274  // and	x20, x19, #0x1111111111111111
	WORD $0x9203e275  // and	x21, x19, #0x2222222222222222
	WORD $0x9202e276  // and	x22, x19, #0x4444444444444444
	WORD $0x9201e273  // and	x19, x19, #0x8888888888888888
	WORD $0x9b117e97  // mul	x23, x20, x17
	WORD $0x9b0c7eb8  // mul	x24, x21, x12
	WORD $0xca1802f7  // eor	x23, x23, x24
	WORD $0x9b0d7ed8  // mul	x24, x22, x13
	WORD $0x9b0e7e79  // mul	x25, x19, x14
	WORD $0xca190318  // eor	x24, x24, x25
	WORD $0xca1802f7  // eor	x23, x23, x24
	WORD $0x9b027e98  // mul	x24, x20, x2
	WORD $0x9b117eb9  // mul	x25, x21, x17
	WORD $0xca190318  // eor	x24, x24, x25
	WORD $0x9b0c7ed9  // mul	x25, x22, x12
	WORD $0x9b0d7e7a  // mul	x26, x19, x13
	WORD $0xca1a0339  // eor	x25, x25, x26
	WORD $0xca190318  // eor	x24, x24, x25
	WORD $0x9b037e99  // mul	x25, x20, x3
	WORD $0x9b027eba  // mul	x26, x21, x2
	WORD $0xca1a0339  // eor	x25, x25, x26
	WORD $0x9b117eda  // mul	x26, x22, x17
	WORD $0x9b0c7e7b  // mul	x27, x19, x12
	WORD $0xca1b035a  // eor	x26, x26, x27
	WORD $0xca1a0339  // eor	x25, x25, x26
	WORD $0x9b047e94  // mul	x20, x20, x4
	WORD $0x9b037eb5  // mul	x21, x21, x3
	WORD $0xca150294  // eor	x20, x20, x21
	WORD $0x9b027ed5  // mul	x21, x22, x2
	WORD $0x9b117e73  // mul	x19, x19, x17
	WORD $0xca1302b3  // eor	x19, x21, x19
	WORD $0xca130293  // eor	x19, x20, x19
	WORD $0x9200e2f4  // and	x20, x23, #0x1111111111111111
	WORD $0x9203e315  // and	x21, x24, #0x2222222222222222
	WORD $0x9202e336  // and	x22, x25, #0x4444444444444444
	WORD $0x9201e273  // and	x19, x19, #0x8888888888888888
	WORD $0xaa150294  // orr	x20, x20, x21
	WORD $0xaa1302d3  // orr	x19, x22, x19
	WORD $0xaa130293  // orr	x19, x20, x19
	WORD $0xca070267  // eor	x7, x19, x7
	WORD $0x6e248e53  // cmeq.16b	v19, v18, v4
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260273  // fmov	w19, s19
	WORD $0x6e248e33  // cmeq.16b	v19, v17, v4
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260274  // fmov	w20, s19
	WORD $0x6e248e13  // cmeq.16b	v19, v16, v4
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260275  // fmov	w21, s19
	WORD $0x6e248cf3  // cmeq.16b	v19, v7, v4
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260276  // fmov	w22, s19
	WORD $0xd3607eb5  // lsl	x21, x21, #32
	WORD $0xaa16c2b5  // orr	x21, x21, x22, lsl #48
	WORD $0x53103e94  // lsl	w20, w20, #16
	WORD $0xaa1402b4  // orr	x20, x21, x20
	WORD $0xaa130293  // orr	x19, x20, x19
	WORD $0x8a270273  // bic	x19, x19, x7
	WORD $0x6e258e52  // cmeq.16b	v18, v18, v5
	WORD $0x4e211e52  // and.16b	v18, v18, v1
	WORD $0x4e020252  // tbl.16b	v18, { v18 }, v2
	WORD $0x4e71ba52  // addv.8h	h18, v18
	WORD $0x1e260254  // fmov	w20, s18
	WORD $0x6e258e31  // cmeq.16b	v17, v17, v5
	WORD $0x4e211e31  // and.16b	v17, v17, v1
	WORD $0x4e020231  // tbl.16b	v17, { v17 }, v2
	WORD $0x4e71ba31  // addv.8h	h17, v17
	WORD $0x1e260235  // fmov	w21, s17
	WORD $0x6e258e10  // cmeq.16b	v16, v16, v5
	WORD $0x4e211e10  // and.16b	v16, v16, v1
	WORD $0x4e020210  // tbl.16b	v16, { v16 }, v2
	WORD $0x4e71ba10  // addv.8h	h16, v16
	WORD $0x1e260216  // fmov	w22, s16
	WORD $0x6e258ce7  // cmeq.16b	v7, v7, v5
	WORD $0x4e211ce7  // and.16b	v7, v7, v1
	WORD $0x4e0200e7  // tbl.16b	v7, { v7 }, v2
	WORD $0x4e71b8e7  // addv.8h	h7, v7
	WORD $0x1e2600f7  // fmov	w23, s7
	WORD $0xd3607ed6  // lsl	x22, x22, #32
	WORD $0xaa17c2d6  // orr	x22, x22, x23, lsl #48
	WORD $0x53103eb5  // lsl	w21, w21, #16
	WORD $0xaa1502d5  // orr	x21, x22, x21
	WORD $0xaa1402b4  // orr	x20, x21, x20
	WORD $0xea270294  // bics	x20, x20, x7
	WORD $0x54ffeae0  // b.eq	LBB0_840 $-676(%rip)
LBB0_846:
	WORD $0xd1000695  // sub	x21, x20, #1
	WORD $0x8a1302b6  // and	x22, x21, x19
	WORD $0x9e6702c7  // fmov	d7, x22
	WORD $0x0e2058e7  // cnt.8b	v7, v7
	WORD $0x2e3038e7  // uaddlv.8b	h7, v7
	WORD $0x1e2600f6  // fmov	w22, s7
	WORD $0x8b0802d6  // add	x22, x22, x8
	WORD $0xeb0b02df  // cmp	x22, x11
	WORD $0x54003109  // b.ls	LBB0_899 $1568(%rip)
	WORD $0x9100056b  // add	x11, x11, #1
	WORD $0xea1402b4  // ands	x20, x21, x20
	WORD $0x54fffea1  // b.ne	LBB0_846 $-44(%rip)
	WORD $0x17ffff4a  // b	LBB0_840 $-728(%rip)
LBB0_848:
	WORD $0xf10000bf  // cmp	x5, #0
	WORD $0x5400404d  // b.le	LBB0_939 $2056(%rip)
	WORD $0xad021be6  // stp	q6, q6, [sp, #64]
	WORD $0xad011be6  // stp	q6, q6, [sp, #32]
	WORD $0x92402e13  // and	x19, x16, #0xfff
	WORD $0xf13f067f  // cmp	x19, #4033
	WORD $0x54ffe9a3  // b.lo	LBB0_842 $-716(%rip)
	WORD $0xf10080b4  // subs	x20, x5, #32
	WORD $0x540000a3  // b.lo	LBB0_852 $20(%rip)
	WORD $0xacc14207  // ldp	q7, q16, [x16], #32
	WORD $0xad0143e7  // stp	q7, q16, [sp, #32]
	WORD $0xaa0a03f3  // mov	x19, x10
	WORD $0x14000003  // b	LBB0_853 $12(%rip)
LBB0_852:
	WORD $0x910083f3  // add	x19, sp, #32
	WORD $0xaa0503f4  // mov	x20, x5
LBB0_853:
	WORD $0xf1004295  // subs	x21, x20, #16
	WORD $0x54000243  // b.lo	LBB0_859 $72(%rip)
	WORD $0x3cc10607  // ldr	q7, [x16], #16
	WORD $0x3c810667  // str	q7, [x19], #16
	WORD $0xaa1503f4  // mov	x20, x21
	WORD $0xf10022b5  // subs	x21, x21, #8
	WORD $0x540001e2  // b.hs	LBB0_860 $60(%rip)
LBB0_855:
	WORD $0xf1001295  // subs	x21, x20, #4
	WORD $0x54000243  // b.lo	LBB0_861 $72(%rip)
LBB0_856:
	WORD $0xb8404614  // ldr	w20, [x16], #4
	WORD $0xb8004674  // str	w20, [x19], #4
	WORD $0xaa1503f4  // mov	x20, x21
	WORD $0xf1000ab5  // subs	x21, x21, #2
	WORD $0x540001e2  // b.hs	LBB0_862 $60(%rip)
LBB0_857:
	WORD $0xb4000254  // cbz	x20, LBB0_863 $72(%rip)
LBB0_858:
	WORD $0x39400210  // ldrb	w16, [x16]
	WORD $0x39000270  // strb	w16, [x19]
	WORD $0x910083f0  // add	x16, sp, #32
	WORD $0x17ffff32  // b	LBB0_842 $-824(%rip)
LBB0_859:
	WORD $0xf1002295  // subs	x21, x20, #8
	WORD $0x54fffe63  // b.lo	LBB0_855 $-52(%rip)
LBB0_860:
	WORD $0xf8408614  // ldr	x20, [x16], #8
	WORD $0xf8008674  // str	x20, [x19], #8
	WORD $0xaa1503f4  // mov	x20, x21
	WORD $0xf10012b5  // subs	x21, x21, #4
	WORD $0x54fffe02  // b.hs	LBB0_856 $-64(%rip)
LBB0_861:
	WORD $0xf1000a95  // subs	x21, x20, #2
	WORD $0x54fffe63  // b.lo	LBB0_857 $-52(%rip)
LBB0_862:
	WORD $0x78402614  // ldrh	w20, [x16], #2
	WORD $0x78002674  // strh	w20, [x19], #2
	WORD $0xaa1503f4  // mov	x20, x21
	WORD $0xb5fffe15  // cbnz	x21, LBB0_858 $-64(%rip)
LBB0_863:
	WORD $0x910083f0  // add	x16, sp, #32
	WORD $0x17ffff23  // b	LBB0_842 $-884(%rip)
LBB0_864:
	WORD $0x91001128  // add	x8, x9, #4
	WORD $0xf940040a  // ldr	x10, [x0, #8]
	WORD $0xeb0a011f  // cmp	x8, x10
	WORD $0x54ffd028  // b.hi	LBB0_799 $-1532(%rip)
	WORD $0x17fffecb  // b	LBB0_822 $-1236(%rip)
LBB0_865:
	WORD $0xf9400411  // ldr	x17, [x0, #8]
	WORD $0xcb10022b  // sub	x11, x17, x16
	WORD $0xf100817f  // cmp	x11, #32
	WORD $0x540036ab  // b.lt	LBB0_929 $1748(%rip)
	WORD $0xd2800008  // mov	x8, #0
	WORD $0xd280000e  // mov	x14, #0
	WORD $0x3200f3ec  // mov	w12, #1431655765
	WORD $0x3201f3ed  // mov	w13, #-1431655766
	WORD $0x4f01e440  // movi.16b	v0, #34
	WORD $0x8b09014f  // add	x15, x10, x9
Lloh36:
	WORD $0x10fdd52b  // adr	x11, lCPI0_0 $-17756(%rip)
Lloh37:
	WORD $0x3dc00161  // ldr	q1, [x11, lCPI0_0@PAGEOFF] $0(%rip)
Lloh38:
	WORD $0x10fdd56b  // adr	x11, lCPI0_1 $-17748(%rip)
Lloh39:
	WORD $0x3dc00162  // ldr	q2, [x11, lCPI0_1@PAGEOFF] $0(%rip)
	WORD $0xcb09022b  // sub	x11, x17, x9
	WORD $0x4f02e783  // movi.16b	v3, #92
	WORD $0x528003f0  // mov	w16, #31
LBB0_867:
	WORD $0x8b0801f1  // add	x17, x15, x8
	WORD $0x3cc01224  // ldur	q4, [x17, #1]
	WORD $0x3cc11225  // ldur	q5, [x17, #17]
	WORD $0x6e208c86  // cmeq.16b	v6, v4, v0
	WORD $0x4e211cc6  // and.16b	v6, v6, v1
	WORD $0x4e0200c6  // tbl.16b	v6, { v6 }, v2
	WORD $0x4e71b8c6  // addv.8h	h6, v6
	WORD $0x1e2600d1  // fmov	w17, s6
	WORD $0x6e208ca6  // cmeq.16b	v6, v5, v0
	WORD $0x4e211cc6  // and.16b	v6, v6, v1
	WORD $0x4e0200c6  // tbl.16b	v6, { v6 }, v2
	WORD $0x4e71b8c6  // addv.8h	h6, v6
	WORD $0x1e2600c0  // fmov	w0, s6
	WORD $0x33103c11  // bfi	w17, w0, #16, #16
	WORD $0x6e238c84  // cmeq.16b	v4, v4, v3
	WORD $0x4e211c84  // and.16b	v4, v4, v1
	WORD $0x4e020084  // tbl.16b	v4, { v4 }, v2
	WORD $0x4e71b884  // addv.8h	h4, v4
	WORD $0x1e260080  // fmov	w0, s4
	WORD $0x6e238ca4  // cmeq.16b	v4, v5, v3
	WORD $0x4e211c84  // and.16b	v4, v4, v1
	WORD $0x4e020084  // tbl.16b	v4, { v4 }, v2
	WORD $0x4e71b884  // addv.8h	h4, v4
	WORD $0x1e260082  // fmov	w2, s4
	WORD $0x33103c40  // bfi	w0, w2, #16, #16
	WORD $0x7100001f  // cmp	w0, #0
	WORD $0xfa4009c0  // ccmp	x14, #0, #0, eq
	WORD $0x54000180  // b.eq	LBB0_869 $48(%rip)
	WORD $0x0a2e0002  // bic	w2, w0, w14
	WORD $0x2a0205c3  // orr	w3, w14, w2, lsl #1
	WORD $0x0a0d000e  // and	w14, w0, w13
	WORD $0x0a2301ce  // bic	w14, w14, w3
	WORD $0x2b0201c0  // adds	w0, w14, w2
	WORD $0x1a9f37ee  // cset	w14, hs
	WORD $0x4a000580  // eor	w0, w12, w0, lsl #1
	WORD $0x0a030000  // and	w0, w0, w3
	WORD $0x2a2003e0  // mvn	w0, w0
	WORD $0x8a110011  // and	x17, x0, x17
	WORD $0x14000002  // b	LBB0_870 $8(%rip)
LBB0_869:
	WORD $0xd280000e  // mov	x14, #0
LBB0_870:
	WORD $0xb5002471  // cbnz	x17, LBB0_901 $1164(%rip)
	WORD $0x91008108  // add	x8, x8, #32
	WORD $0xd1008210  // sub	x16, x16, #32
	WORD $0x8b100171  // add	x17, x11, x16
	WORD $0xf100fe3f  // cmp	x17, #63
	WORD $0x54fffa6c  // b.gt	LBB0_867 $-180(%rip)
	WORD $0xb500324e  // cbnz	x14, LBB0_941 $1608(%rip)
	WORD $0x8b09014c  // add	x12, x10, x9
	WORD $0x8b08018c  // add	x12, x12, x8
	WORD $0x9100058c  // add	x12, x12, #1
	WORD $0xaa2803e8  // mvn	x8, x8
	WORD $0x8b0b010b  // add	x11, x8, x11
	WORD $0xf100057f  // cmp	x11, #1
	WORD $0x5400338a  // b.ge	LBB0_944 $1648(%rip)
	WORD $0x17fffe38  // b	LBB0_799 $-1824(%rip)
LBB0_874:
	WORD $0xd2800007  // mov	x7, #0
	WORD $0xd280000f  // mov	x15, #0
	WORD $0xd2800008  // mov	x8, #0
	WORD $0xd280000b  // mov	x11, #0
	WORD $0xb201e3ec  // mov	x12, #-8608480567731124088
	WORD $0xf2e1110c  // movk	x12, #2184, lsl #48
	WORD $0xb202e3ed  // mov	x13, #4919131752989213764
	WORD $0xf2e0888d  // movk	x13, #1092, lsl #48
	WORD $0xb203e3ee  // mov	x14, #2459565876494606882
	WORD $0xf2e0444e  // movk	x14, #546, lsl #48
	WORD $0xf9400411  // ldr	x17, [x0, #8]
	WORD $0xcb100225  // sub	x5, x17, x16
	WORD $0x8b100150  // add	x16, x10, x16
	WORD $0x910083ea  // add	x10, sp, #32
	WORD $0x9100814a  // add	x10, x10, #32
	WORD $0x4f01e440  // movi.16b	v0, #34
Lloh40:
	WORD $0x10fdcb71  // adr	x17, lCPI0_0 $-18068(%rip)
Lloh41:
	WORD $0x3dc00221  // ldr	q1, [x17, lCPI0_0@PAGEOFF] $0(%rip)
Lloh42:
	WORD $0x10fdcbb1  // adr	x17, lCPI0_1 $-18060(%rip)
Lloh43:
	WORD $0x3dc00222  // ldr	q2, [x17, lCPI0_1@PAGEOFF] $0(%rip)
	WORD $0x4f02e783  // movi.16b	v3, #92
	WORD $0xb200e3f1  // mov	x17, #1229782938247303441
	WORD $0xb203e3e2  // mov	x2, #2459565876494606882
	WORD $0xb202e3e3  // mov	x3, #4919131752989213764
	WORD $0xb201e3e4  // mov	x4, #-8608480567731124088
	WORD $0x4f02e764  // movi.16b	v4, #91
	WORD $0x4f02e7a5  // movi.16b	v5, #93
	WORD $0x6f00e406  // movi.2d	v6, #0000000000000000
	WORD $0x14000009  // b	LBB0_876 $36(%rip)
LBB0_875:
	WORD $0x937ffce7  // asr	x7, x7, #63
	WORD $0x9e670267  // fmov	d7, x19
	WORD $0x0e2058e7  // cnt.8b	v7, v7
	WORD $0x2e3038e7  // uaddlv.8b	h7, v7
	WORD $0x1e2600e5  // fmov	w5, s7
	WORD $0x8b0800a8  // add	x8, x5, x8
	WORD $0x91010210  // add	x16, x16, #64
	WORD $0xaa0603e5  // mov	x5, x6
LBB0_876:
	WORD $0xf10100a6  // subs	x6, x5, #64
	WORD $0x540015cb  // b.lt	LBB0_883 $696(%rip)
LBB0_877:
	WORD $0xad404612  // ldp	q18, q17, [x16]
	WORD $0xad411e10  // ldp	q16, q7, [x16, #32]
	WORD $0x6e238e53  // cmeq.16b	v19, v18, v3
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260273  // fmov	w19, s19
	WORD $0x6e238e33  // cmeq.16b	v19, v17, v3
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260274  // fmov	w20, s19
	WORD $0x6e238e13  // cmeq.16b	v19, v16, v3
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260275  // fmov	w21, s19
	WORD $0x6e238cf3  // cmeq.16b	v19, v7, v3
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260276  // fmov	w22, s19
	WORD $0xd3607eb5  // lsl	x21, x21, #32
	WORD $0xaa16c2b5  // orr	x21, x21, x22, lsl #48
	WORD $0x53103e94  // lsl	w20, w20, #16
	WORD $0xaa1402b4  // orr	x20, x21, x20
	WORD $0xaa130293  // orr	x19, x20, x19
	WORD $0xaa0f0274  // orr	x20, x19, x15
	WORD $0xb5000094  // cbnz	x20, LBB0_879 $16(%rip)
	WORD $0xd280000f  // mov	x15, #0
	WORD $0xd2800013  // mov	x19, #0
	WORD $0x1400000a  // b	LBB0_880 $40(%rip)
LBB0_879:
	WORD $0x8a2f0274  // bic	x20, x19, x15
	WORD $0xaa1405f5  // orr	x21, x15, x20, lsl #1
	WORD $0x8a35026f  // bic	x15, x19, x21
	WORD $0x9201f1ef  // and	x15, x15, #0xaaaaaaaaaaaaaaaa
	WORD $0xab1401f3  // adds	x19, x15, x20
	WORD $0x1a9f37ef  // cset	w15, hs
	WORD $0xd37ffa73  // lsl	x19, x19, #1
	WORD $0xd200f273  // eor	x19, x19, #0x5555555555555555
	WORD $0x8a150273  // and	x19, x19, x21
LBB0_880:
	WORD $0x6e208e53  // cmeq.16b	v19, v18, v0
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260274  // fmov	w20, s19
	WORD $0x6e208e33  // cmeq.16b	v19, v17, v0
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260275  // fmov	w21, s19
	WORD $0x6e208e13  // cmeq.16b	v19, v16, v0
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260276  // fmov	w22, s19
	WORD $0x6e208cf3  // cmeq.16b	v19, v7, v0
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260277  // fmov	w23, s19
	WORD $0xd3607ed6  // lsl	x22, x22, #32
	WORD $0xaa17c2d6  // orr	x22, x22, x23, lsl #48
	WORD $0x53103eb5  // lsl	w21, w21, #16
	WORD $0xaa1502d5  // orr	x21, x22, x21
	WORD $0xaa1402b4  // orr	x20, x21, x20
	WORD $0x8a330293  // bic	x19, x20, x19
	WORD $0x9200e274  // and	x20, x19, #0x1111111111111111
	WORD $0x9203e275  // and	x21, x19, #0x2222222222222222
	WORD $0x9202e276  // and	x22, x19, #0x4444444444444444
	WORD $0x9201e273  // and	x19, x19, #0x8888888888888888
	WORD $0x9b117e97  // mul	x23, x20, x17
	WORD $0x9b0c7eb8  // mul	x24, x21, x12
	WORD $0xca1802f7  // eor	x23, x23, x24
	WORD $0x9b0d7ed8  // mul	x24, x22, x13
	WORD $0x9b0e7e79  // mul	x25, x19, x14
	WORD $0xca190318  // eor	x24, x24, x25
	WORD $0xca1802f7  // eor	x23, x23, x24
	WORD $0x9b027e98  // mul	x24, x20, x2
	WORD $0x9b117eb9  // mul	x25, x21, x17
	WORD $0xca190318  // eor	x24, x24, x25
	WORD $0x9b0c7ed9  // mul	x25, x22, x12
	WORD $0x9b0d7e7a  // mul	x26, x19, x13
	WORD $0xca1a0339  // eor	x25, x25, x26
	WORD $0xca190318  // eor	x24, x24, x25
	WORD $0x9b037e99  // mul	x25, x20, x3
	WORD $0x9b027eba  // mul	x26, x21, x2
	WORD $0xca1a0339  // eor	x25, x25, x26
	WORD $0x9b117eda  // mul	x26, x22, x17
	WORD $0x9b0c7e7b  // mul	x27, x19, x12
	WORD $0xca1b035a  // eor	x26, x26, x27
	WORD $0xca1a0339  // eor	x25, x25, x26
	WORD $0x9b047e94  // mul	x20, x20, x4
	WORD $0x9b037eb5  // mul	x21, x21, x3
	WORD $0xca150294  // eor	x20, x20, x21
	WORD $0x9b027ed5  // mul	x21, x22, x2
	WORD $0x9b117e73  // mul	x19, x19, x17
	WORD $0xca1302b3  // eor	x19, x21, x19
	WORD $0xca130293  // eor	x19, x20, x19
	WORD $0x9200e2f4  // and	x20, x23, #0x1111111111111111
	WORD $0x9203e315  // and	x21, x24, #0x2222222222222222
	WORD $0x9202e336  // and	x22, x25, #0x4444444444444444
	WORD $0x9201e273  // and	x19, x19, #0x8888888888888888
	WORD $0xaa150294  // orr	x20, x20, x21
	WORD $0xaa1302d3  // orr	x19, x22, x19
	WORD $0xaa130293  // orr	x19, x20, x19
	WORD $0xca070267  // eor	x7, x19, x7
	WORD $0x6e248e53  // cmeq.16b	v19, v18, v4
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260273  // fmov	w19, s19
	WORD $0x6e248e33  // cmeq.16b	v19, v17, v4
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260274  // fmov	w20, s19
	WORD $0x6e248e13  // cmeq.16b	v19, v16, v4
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260275  // fmov	w21, s19
	WORD $0x6e248cf3  // cmeq.16b	v19, v7, v4
	WORD $0x4e211e73  // and.16b	v19, v19, v1
	WORD $0x4e020273  // tbl.16b	v19, { v19 }, v2
	WORD $0x4e71ba73  // addv.8h	h19, v19
	WORD $0x1e260276  // fmov	w22, s19
	WORD $0xd3607eb5  // lsl	x21, x21, #32
	WORD $0xaa16c2b5  // orr	x21, x21, x22, lsl #48
	WORD $0x53103e94  // lsl	w20, w20, #16
	WORD $0xaa1402b4  // orr	x20, x21, x20
	WORD $0xaa130293  // orr	x19, x20, x19
	WORD $0x8a270273  // bic	x19, x19, x7
	WORD $0x6e258e52  // cmeq.16b	v18, v18, v5
	WORD $0x4e211e52  // and.16b	v18, v18, v1
	WORD $0x4e020252  // tbl.16b	v18, { v18 }, v2
	WORD $0x4e71ba52  // addv.8h	h18, v18
	WORD $0x1e260254  // fmov	w20, s18
	WORD $0x6e258e31  // cmeq.16b	v17, v17, v5
	WORD $0x4e211e31  // and.16b	v17, v17, v1
	WORD $0x4e020231  // tbl.16b	v17, { v17 }, v2
	WORD $0x4e71ba31  // addv.8h	h17, v17
	WORD $0x1e260235  // fmov	w21, s17
	WORD $0x6e258e10  // cmeq.16b	v16, v16, v5
	WORD $0x4e211e10  // and.16b	v16, v16, v1
	WORD $0x4e020210  // tbl.16b	v16, { v16 }, v2
	WORD $0x4e71ba10  // addv.8h	h16, v16
	WORD $0x1e260216  // fmov	w22, s16
	WORD $0x6e258ce7  // cmeq.16b	v7, v7, v5
	WORD $0x4e211ce7  // and.16b	v7, v7, v1
	WORD $0x4e0200e7  // tbl.16b	v7, { v7 }, v2
	WORD $0x4e71b8e7  // addv.8h	h7, v7
	WORD $0x1e2600f7  // fmov	w23, s7
	WORD $0xd3607ed6  // lsl	x22, x22, #32
	WORD $0xaa17c2d6  // orr	x22, x22, x23, lsl #48
	WORD $0x53103eb5  // lsl	w21, w21, #16
	WORD $0xaa1502d5  // orr	x21, x22, x21
	WORD $0xaa1402b4  // orr	x20, x21, x20
	WORD $0xea270294  // bics	x20, x20, x7
	WORD $0x54ffeae0  // b.eq	LBB0_875 $-676(%rip)
LBB0_881:
	WORD $0xd1000695  // sub	x21, x20, #1
	WORD $0x8a1302b6  // and	x22, x21, x19
	WORD $0x9e6702c7  // fmov	d7, x22
	WORD $0x0e2058e7  // cnt.8b	v7, v7
	WORD $0x2e3038e7  // uaddlv.8b	h7, v7
	WORD $0x1e2600f6  // fmov	w22, s7
	WORD $0x8b0802d6  // add	x22, x22, x8
	WORD $0xeb0b02df  // cmp	x22, x11
	WORD $0x540006c9  // b.ls	LBB0_899 $216(%rip)
	WORD $0x9100056b  // add	x11, x11, #1
	WORD $0xea1402b4  // ands	x20, x21, x20
	WORD $0x54fffea1  // b.ne	LBB0_881 $-44(%rip)
	WORD $0x17ffff4a  // b	LBB0_875 $-728(%rip)
LBB0_883:
	WORD $0xf10000bf  // cmp	x5, #0
	WORD $0x5400160d  // b.le	LBB0_939 $704(%rip)
	WORD $0xad021be6  // stp	q6, q6, [sp, #64]
	WORD $0xad011be6  // stp	q6, q6, [sp, #32]
	WORD $0x92402e13  // and	x19, x16, #0xfff
	WORD $0xf13f067f  // cmp	x19, #4033
	WORD $0x54ffe9a3  // b.lo	LBB0_877 $-716(%rip)
	WORD $0xf10080b4  // subs	x20, x5, #32
	WORD $0x540000a3  // b.lo	LBB0_887 $20(%rip)
	WORD $0xacc14207  // ldp	q7, q16, [x16], #32
	WORD $0xad0143e7  // stp	q7, q16, [sp, #32]
	WORD $0xaa0a03f3  // mov	x19, x10
	WORD $0x14000003  // b	LBB0_888 $12(%rip)
LBB0_887:
	WORD $0x910083f3  // add	x19, sp, #32
	WORD $0xaa0503f4  // mov	x20, x5
LBB0_888:
	WORD $0xf1004295  // subs	x21, x20, #16
	WORD $0x54000243  // b.lo	LBB0_894 $72(%rip)
	WORD $0x3cc10607  // ldr	q7, [x16], #16
	WORD $0x3c810667  // str	q7, [x19], #16
	WORD $0xaa1503f4  // mov	x20, x21
	WORD $0xf10022b5  // subs	x21, x21, #8
	WORD $0x540001e2  // b.hs	LBB0_895 $60(%rip)
LBB0_890:
	WORD $0xf1001295  // subs	x21, x20, #4
	WORD $0x54000243  // b.lo	LBB0_896 $72(%rip)
LBB0_891:
	WORD $0xb8404614  // ldr	w20, [x16], #4
	WORD $0xb8004674  // str	w20, [x19], #4
	WORD $0xaa1503f4  // mov	x20, x21
	WORD $0xf1000ab5  // subs	x21, x21, #2
	WORD $0x540001e2  // b.hs	LBB0_897 $60(%rip)
LBB0_892:
	WORD $0xb4000254  // cbz	x20, LBB0_898 $72(%rip)
LBB0_893:
	WORD $0x39400210  // ldrb	w16, [x16]
	WORD $0x39000270  // strb	w16, [x19]
	WORD $0x910083f0  // add	x16, sp, #32
	WORD $0x17ffff32  // b	LBB0_877 $-824(%rip)
LBB0_894:
	WORD $0xf1002295  // subs	x21, x20, #8
	WORD $0x54fffe63  // b.lo	LBB0_890 $-52(%rip)
LBB0_895:
	WORD $0xf8408614  // ldr	x20, [x16], #8
	WORD $0xf8008674  // str	x20, [x19], #8
	WORD $0xaa1503f4  // mov	x20, x21
	WORD $0xf10012b5  // subs	x21, x21, #4
	WORD $0x54fffe02  // b.hs	LBB0_891 $-64(%rip)
LBB0_896:
	WORD $0xf1000a95  // subs	x21, x20, #2
	WORD $0x54fffe63  // b.lo	LBB0_892 $-52(%rip)
LBB0_897:
	WORD $0x78402614  // ldrh	w20, [x16], #2
	WORD $0x78002674  // strh	w20, [x19], #2
	WORD $0xaa1503f4  // mov	x20, x21
	WORD $0xb5fffe15  // cbnz	x21, LBB0_893 $-64(%rip)
LBB0_898:
	WORD $0x910083f0  // add	x16, sp, #32
	WORD $0x17ffff23  // b	LBB0_877 $-884(%rip)
LBB0_899:
	WORD $0xf9400408  // ldr	x8, [x0, #8]
	WORD $0xdac0028a  // rbit	x10, x20
	WORD $0xdac0114a  // clz	x10, x10
	WORD $0xcb05014a  // sub	x10, x10, x5
	WORD $0x8b080148  // add	x8, x10, x8
	WORD $0x9100050a  // add	x10, x8, #1
	WORD $0xf900002a  // str	x10, [x1]
	WORD $0xf940040b  // ldr	x11, [x0, #8]
	WORD $0xeb0b015f  // cmp	x10, x11
	WORD $0x9a882568  // csinc	x8, x11, x8, hs
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0xda9f9128  // csinv	x8, x9, xzr, ls
	WORD $0x17fffd9f  // b	LBB0_832 $-2436(%rip)
LBB0_900:
	WORD $0xf9000029  // str	x9, [x1]
	WORD $0x17fffd9c  // b	LBB0_831 $-2448(%rip)
LBB0_901:
	WORD $0xdac0022a  // rbit	x10, x17
	WORD $0xdac0114a  // clz	x10, x10
	WORD $0x8b080128  // add	x8, x9, x8
	WORD $0x8b080148  // add	x8, x10, x8
	WORD $0x91000908  // add	x8, x8, #2
	WORD $0x17fffd69  // b	LBB0_822 $-2652(%rip)
LBB0_902:
	WORD $0x928000c8  // mov	x8, #-7
	WORD $0x17fffd95  // b	LBB0_832 $-2476(%rip)
LBB0_903:
	WORD $0xb100077f  // cmn	x27, #1
	WORD $0x9a9c0108  // csel	x8, x8, x28, eq
LBB0_904:
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0xaa1b03e8  // mov	x8, x27
	WORD $0x17fffd90  // b	LBB0_832 $-2496(%rip)
LBB0_905:
	WORD $0x92800013  // mov	x19, #-1
LBB0_906:
	WORD $0xaa3303fb  // mvn	x27, x19
LBB0_907:
	WORD $0x8b1a0368  // add	x8, x27, x26
	WORD $0x17fffd8a  // b	LBB0_830 $-2520(%rip)
LBB0_908:
	WORD $0xf900002d  // str	x13, [x1]
	WORD $0xaa0903e8  // mov	x8, x9
	WORD $0x17fffd89  // b	LBB0_832 $-2524(%rip)
LBB0_909:
	WORD $0xaa0803fb  // mov	x27, x8
	WORD $0x17fffffa  // b	LBB0_907 $-24(%rip)
LBB0_910:
	WORD $0xaa1903fc  // mov	x28, x25
LBB0_911:
	WORD $0xf900003c  // str	x28, [x1]
	WORD $0x92800008  // mov	x8, #-1
	WORD $0x17fffd83  // b	LBB0_832 $-2548(%rip)
LBB0_912:
	WORD $0x92800013  // mov	x19, #-1
LBB0_913:
	WORD $0xaa3303fc  // mvn	x28, x19
LBB0_914:
	WORD $0x8b1c0368  // add	x8, x27, x28
	WORD $0x17fffd7d  // b	LBB0_830 $-2572(%rip)
LBB0_915:
	WORD $0xf900003b  // str	x27, [x1]
	WORD $0x387b6928  // ldrb	w8, [x9, x27]
	WORD $0x7101851f  // cmp	w8, #97
	WORD $0x54ffaf41  // b.ne	LBB0_831 $-2584(%rip)
	WORD $0x91000b48  // add	x8, x26, #2
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0x38686928  // ldrb	w8, [x9, x8]
	WORD $0x7101b11f  // cmp	w8, #108
	WORD $0x54ffaea1  // b.ne	LBB0_831 $-2604(%rip)
	WORD $0x91000f48  // add	x8, x26, #3
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0x38686928  // ldrb	w8, [x9, x8]
	WORD $0x7101cd1f  // cmp	w8, #115
	WORD $0x54ffae01  // b.ne	LBB0_831 $-2624(%rip)
	WORD $0x91001348  // add	x8, x26, #4
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0x38686928  // ldrb	w8, [x9, x8]
	WORD $0x7101951f  // cmp	w8, #101
	WORD $0x54ffad61  // b.ne	LBB0_831 $-2644(%rip)
	WORD $0x91001748  // add	x8, x26, #5
	WORD $0x17fffd68  // b	LBB0_830 $-2656(%rip)
LBB0_920:
	WORD $0xf900003a  // str	x26, [x1]
	WORD $0x39400388  // ldrb	w8, [x28]
	WORD $0x7101b91f  // cmp	w8, #110
	WORD $0x54ffaca1  // b.ne	LBB0_831 $-2668(%rip)
	WORD $0x91000748  // add	x8, x26, #1
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0x38686928  // ldrb	w8, [x9, x8]
	WORD $0x7101d51f  // cmp	w8, #117
	WORD $0x54ffac01  // b.ne	LBB0_831 $-2688(%rip)
	WORD $0x91000b48  // add	x8, x26, #2
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0x38686928  // ldrb	w8, [x9, x8]
	WORD $0x7101b11f  // cmp	w8, #108
	WORD $0x54ffab61  // b.ne	LBB0_831 $-2708(%rip)
	WORD $0x91000f48  // add	x8, x26, #3
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0x38686928  // ldrb	w8, [x9, x8]
	WORD $0x7101b11f  // cmp	w8, #108
	WORD $0x54ffaac1  // b.ne	LBB0_831 $-2728(%rip)
	WORD $0x14000014  // b	LBB0_928 $80(%rip)
LBB0_924:
	WORD $0xf900003a  // str	x26, [x1]
	WORD $0x39400388  // ldrb	w8, [x28]
	WORD $0x7101d11f  // cmp	w8, #116
	WORD $0x54ffaa21  // b.ne	LBB0_831 $-2748(%rip)
	WORD $0x91000748  // add	x8, x26, #1
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0x38686928  // ldrb	w8, [x9, x8]
	WORD $0x7101c91f  // cmp	w8, #114
	WORD $0x54ffa981  // b.ne	LBB0_831 $-2768(%rip)
	WORD $0x91000b48  // add	x8, x26, #2
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0x38686928  // ldrb	w8, [x9, x8]
	WORD $0x7101d51f  // cmp	w8, #117
	WORD $0x54ffa8e1  // b.ne	LBB0_831 $-2788(%rip)
	WORD $0x91000f48  // add	x8, x26, #3
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0x38686928  // ldrb	w8, [x9, x8]
	WORD $0x7101951f  // cmp	w8, #101
	WORD $0x54ffa841  // b.ne	LBB0_831 $-2808(%rip)
LBB0_928:
	WORD $0x91001348  // add	x8, x26, #4
	WORD $0x17fffd3f  // b	LBB0_830 $-2820(%rip)
LBB0_929:
	WORD $0x8b10014c  // add	x12, x10, x16
	WORD $0xf100057f  // cmp	x11, #1
	WORD $0x5400050a  // b.ge	LBB0_944 $160(%rip)
	WORD $0x17fffcc4  // b	LBB0_799 $-3312(%rip)
LBB0_930:
	WORD $0xaa0403fc  // mov	x28, x4
	WORD $0x8b040368  // add	x8, x27, x4
	WORD $0x17fffd38  // b	LBB0_830 $-2848(%rip)
LBB0_931:
	WORD $0xaa1b03e8  // mov	x8, x27
LBB0_932:
	WORD $0x9280001b  // mov	x27, #-1
	WORD $0x17ffffa5  // b	LBB0_904 $-364(%rip)
LBB0_933:
	WORD $0x91000a94  // add	x20, x20, #2
LBB0_934:
	WORD $0x92800028  // mov	x8, #-2
	WORD $0x14000006  // b	LBB0_938 $24(%rip)
LBB0_935:
	WORD $0x91000694  // add	x20, x20, #1
	WORD $0x92800048  // mov	x8, #-3
	WORD $0x14000003  // b	LBB0_938 $12(%rip)
LBB0_936:
	WORD $0x91000694  // add	x20, x20, #1
LBB0_937:
	WORD $0x92800008  // mov	x8, #-1
LBB0_938:
	WORD $0xcb130289  // sub	x9, x20, x19
	WORD $0xf9000029  // str	x9, [x1]
	WORD $0x17fffd2c  // b	LBB0_832 $-2896(%rip)
LBB0_939:
	WORD $0xf9400408  // ldr	x8, [x0, #8]
LBB0_940:
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0x92800008  // mov	x8, #-1
	WORD $0x17fffd28  // b	LBB0_832 $-2912(%rip)
LBB0_941:
	WORD $0xd100056c  // sub	x12, x11, #1
	WORD $0xeb08019f  // cmp	x12, x8
	WORD $0x54ff9580  // b.eq	LBB0_799 $-3408(%rip)
	WORD $0x8b09014c  // add	x12, x10, x9
	WORD $0x8b08018c  // add	x12, x12, x8
	WORD $0x9100098c  // add	x12, x12, #2
	WORD $0xcb080168  // sub	x8, x11, x8
	WORD $0xd100090b  // sub	x11, x8, #2
	WORD $0xf100057f  // cmp	x11, #1
	WORD $0x5400010a  // b.ge	LBB0_944 $32(%rip)
	WORD $0x17fffca4  // b	LBB0_799 $-3440(%rip)
LBB0_943:
	WORD $0x9280002d  // mov	x13, #-2
	WORD $0x52800048  // mov	w8, #2
	WORD $0x8b08018c  // add	x12, x12, x8
	WORD $0x92800008  // mov	x8, #-1
	WORD $0xab0d016b  // adds	x11, x11, x13
	WORD $0x54ffa2ed  // b.le	LBB0_832 $-2980(%rip)
LBB0_944:
	WORD $0x39400188  // ldrb	w8, [x12]
	WORD $0x7101711f  // cmp	w8, #92
	WORD $0x54ffff00  // b.eq	LBB0_943 $-32(%rip)
	WORD $0x7100891f  // cmp	w8, #34
	WORD $0x54000100  // b.eq	LBB0_947 $32(%rip)
	WORD $0x9280000d  // mov	x13, #-1
	WORD $0x52800028  // mov	w8, #1
	WORD $0x8b08018c  // add	x12, x12, x8
	WORD $0x92800008  // mov	x8, #-1
	WORD $0xab0d016b  // adds	x11, x11, x13
	WORD $0x54fffecc  // b.gt	LBB0_944 $-40(%rip)
	WORD $0x17fffd0b  // b	LBB0_832 $-3028(%rip)
LBB0_947:
	WORD $0xcb0a0188  // sub	x8, x12, x10
	WORD $0x91000508  // add	x8, x8, #1
	WORD $0x17fffcda  // b	LBB0_822 $-3224(%rip)
LBB0_948:
	WORD $0x91002294  // add	x20, x20, #8
	WORD $0x17ffffd1  // b	LBB0_934 $-188(%rip)
LBB0_949:
	WORD $0x92800068  // mov	x8, #-4
	WORD $0xaa1c03f4  // mov	x20, x28
	WORD $0x17ffffd5  // b	LBB0_938 $-172(%rip)
LBB0_950:
	WORD $0xaa1c03f4  // mov	x20, x28
	WORD $0x17ffffd3  // b	LBB0_938 $-180(%rip)
LBB0_951:
	WORD $0x91002294  // add	x20, x20, #8
	WORD $0x92800068  // mov	x8, #-4
	WORD $0x17ffffd0  // b	LBB0_938 $-192(%rip)
	  // .p2align 2, 0x00
_MASK_USE_NUMBER:
	WORD $0x00000002  // .long 2
__UnquoteTab:
	WORD $0x00000000  // .ascii 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .ascii 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .ascii 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .ascii 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .ascii 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .ascii 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00"\x00'
	WORD $0x00000000  // .ascii 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00"\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .ascii 4, '\x00\x00\x00\x00\x00\x00"\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00220000  // .ascii 4, '\x00\x00"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00/'
	WORD $0x00000000  // .ascii 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00/\x00\x00\x00\x00'
	WORD $0x00000000  // .ascii 4, '\x00\x00\x00\x00\x00\x00\x00/\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x2f000000  // .ascii 4, '\x00\x00\x00/\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .ascii 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .ascii 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .ascii 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .ascii 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .ascii 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .ascii 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .ascii 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .ascii 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .ascii 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\\\x00\x00\x00'
	WORD $0x00000000  // .ascii 4, '\x00\x00\x00\x00\x00\x00\x00\x00\\\x00\x00\x00\x00\x00\x08\x00'
	WORD $0x00000000  // .ascii 4, '\x00\x00\x00\x00\\\x00\x00\x00\x00\x00\x08\x00\x00\x00\x0c\x00'
	WORD $0x0000005c  // .ascii 4, '\\\x00\x00\x00\x00\x00\x08\x00\x00\x00\x0c\x00\x00\x00\x00\x00'
	WORD $0x00080000  // .ascii 4, '\x00\x00\x08\x00\x00\x00\x0c\x00\x00\x00\x00\x00\x00\x00\n\x00'
	WORD $0x000c0000  // .ascii 4, '\x00\x00\x0c\x00\x00\x00\x00\x00\x00\x00\n\x00\x00\x00\r\x00'
	WORD $0x00000000  // .ascii 4, '\x00\x00\x00\x00\x00\x00\n\x00\x00\x00\r\x00\t\xff\x00\x00'
	WORD $0x000a0000  // .ascii 4, '\x00\x00\n\x00\x00\x00\r\x00\t\xff\x00\x00'
	WORD $0x000d0000  // .ascii 4, '\x00\x00\r\x00\t\xff\x00\x00'
	WORD $0x0000ff09  // .ascii 4, '\t\xff\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00\x00\x00\x00\x00'
	WORD $0x00000000  // .space 4, '\x00\x00\x00\x00'

TEXT ·__get_by_path(SB), NOSPLIT, $0-40
	NO_LOCAL_POINTERS

_entry:
	MOVD 16(g), R16
	SUB $272, RSP, R17
	CMP  R16, R17
	BLS  _stack_grow

_get_by_path:
	MOVD s+0(FP), R0
	MOVD p+8(FP), R1
	MOVD path+16(FP), R2
	MOVD m+24(FP), R3
	MOVD ·_subr__get_by_path(SB), R11
	WORD $0x1000005e // adr x30, .+8
	JMP (R11)
	MOVD R0, ret+32(FP)
	RET

_stack_grow:
	MOVD R30, R3
	CALL runtime·morestack_noctxt<>(SB)
	JMP  _entry
