// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

#include "go_asm.h"
#include "funcdata.h"
#include "textflag.h"

TEXT ·__vunsigned_entry__(SB), NOSPLIT, $16
	NO_LOCAL_POINTERS
	WORD $0x100000a0 // adr x0, .+20
	MOVD R0, ret(FP)
	RET
	  // .p2align 2, 0x00
_vunsigned:
	WORD $0xd10083ff  // sub	sp, sp, #32
	WORD $0xa900fbfd  // stp	fp, lr, [sp, #8]
	WORD $0xa93ffbfd  // stp	fp, lr, [sp, #-8]
	WORD $0xd10023fd  // sub	fp, sp, #8
	WORD $0xf9400028  // ldr	x8, [x1]
	WORD $0xa940240a  // ldp	x10, x9, [x0]
	WORD $0x5280012b  // mov	w11, #9
	WORD $0xa900fc5f  // stp	xzr, xzr, [x2, #8]
	WORD $0xf900004b  // str	x11, [x2]
	WORD $0xf940002b  // ldr	x11, [x1]
	WORD $0xf9000c4b  // str	x11, [x2, #24]
	WORD $0xeb09011f  // cmp	x8, x9
	WORD $0x54000162  // b.hs	LBB0_3 $44(%rip)
	WORD $0x8b08014b  // add	x11, x10, x8
	WORD $0x3940016c  // ldrb	w12, [x11]
	WORD $0x7100b59f  // cmp	w12, #45
	WORD $0x540001a1  // b.ne	LBB0_4 $52(%rip)
LBB0_2:
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0x928000a8  // mov	x8, #-6
	WORD $0xf9000048  // str	x8, [x2]
	WORD $0xa940fbfd  // ldp	fp, lr, [sp, #8]
	WORD $0x910083ff  // add	sp, sp, #32
	WORD $0xd65f03c0  // ret
LBB0_3:
	WORD $0xf9000029  // str	x9, [x1]
	WORD $0x92800008  // mov	x8, #-1
	WORD $0xf9000048  // str	x8, [x2]
	WORD $0xa940fbfd  // ldp	fp, lr, [sp, #8]
	WORD $0x910083ff  // add	sp, sp, #32
	WORD $0xd65f03c0  // ret
LBB0_4:
	WORD $0x5100e98d  // sub	w13, w12, #58
	WORD $0x31002dbf  // cmn	w13, #11
	WORD $0x540000e8  // b.hi	LBB0_6 $28(%rip)
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0x92800028  // mov	x8, #-2
	WORD $0xf9000048  // str	x8, [x2]
	WORD $0xa940fbfd  // ldp	fp, lr, [sp, #8]
	WORD $0x910083ff  // add	sp, sp, #32
	WORD $0xd65f03c0  // ret
LBB0_6:
	WORD $0x7100c19f  // cmp	w12, #48
	WORD $0x54000161  // b.ne	LBB0_8 $44(%rip)
	WORD $0x3940056b  // ldrb	w11, [x11, #1]
	WORD $0x5100b96b  // sub	w11, w11, #46
	WORD $0x7100dd7f  // cmp	w11, #55
	WORD $0x5280002c  // mov	w12, #1
	WORD $0x9acb218b  // lsl	x11, x12, x11
	WORD $0xb20903ec  // mov	x12, #36028797027352576
	WORD $0xf280002c  // movk	x12, #1
	WORD $0x8a0c016b  // and	x11, x11, x12
	WORD $0xfa409964  // ccmp	x11, #0, #4, ls
	WORD $0x540005a0  // b.eq	LBB0_21 $180(%rip)
LBB0_8:
	WORD $0xd280000b  // mov	x11, #0
	WORD $0xeb08013f  // cmp	x9, x8
	WORD $0x9a88812c  // csel	x12, x9, x8, hi
	WORD $0x5280014d  // mov	w13, #10
LBB0_9:
	WORD $0xeb08019f  // cmp	x12, x8
	WORD $0x54000440  // b.eq	LBB0_20 $136(%rip)
	WORD $0x3868694e  // ldrb	w14, [x10, x8]
	WORD $0x5100c1ce  // sub	w14, w14, #48
	WORD $0x710025df  // cmp	w14, #9
	WORD $0x54000288  // b.hi	LBB0_15 $80(%rip)
	WORD $0x9bcd7d6f  // umulh	x15, x11, x13
	WORD $0xeb0f03ff  // cmp	xzr, x15
	WORD $0x54000141  // b.ne	LBB0_14 $40(%rip)
	WORD $0x8b0b096b  // add	x11, x11, x11, lsl #2
	WORD $0xd37ff96b  // lsl	x11, x11, #1
	WORD $0x91000508  // add	x8, x8, #1
	WORD $0xab2e016b  // adds	x11, x11, w14, uxtb
	WORD $0x1a9f37ef  // cset	w15, hs
	WORD $0x934001ee  // sbfx	x14, x15, #0, #1
	WORD $0xca0f01cf  // eor	x15, x14, x15
	WORD $0xb500004f  // cbnz	x15, LBB0_14 $8(%rip)
	WORD $0xb6fffdee  // tbz	x14, #63, LBB0_9 $-68(%rip)
LBB0_14:
	WORD $0xd1000508  // sub	x8, x8, #1
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0x92800088  // mov	x8, #-5
	WORD $0xf9000048  // str	x8, [x2]
	WORD $0xa940fbfd  // ldp	fp, lr, [sp, #8]
	WORD $0x910083ff  // add	sp, sp, #32
	WORD $0xd65f03c0  // ret
LBB0_15:
	WORD $0xeb09011f  // cmp	x8, x9
	WORD $0x54000102  // b.hs	LBB0_19 $32(%rip)
	WORD $0x38686949  // ldrb	w9, [x10, x8]
	WORD $0x7100b93f  // cmp	w9, #46
	WORD $0x54fff7c0  // b.eq	LBB0_2 $-264(%rip)
	WORD $0x7101153f  // cmp	w9, #69
	WORD $0x54fff780  // b.eq	LBB0_2 $-272(%rip)
	WORD $0x7101953f  // cmp	w9, #101
	WORD $0x54fff740  // b.eq	LBB0_2 $-280(%rip)
LBB0_19:
	WORD $0xaa0803ec  // mov	x12, x8
LBB0_20:
	WORD $0xf900002c  // str	x12, [x1]
	WORD $0xf900084b  // str	x11, [x2, #16]
	WORD $0xa940fbfd  // ldp	fp, lr, [sp, #8]
	WORD $0x910083ff  // add	sp, sp, #32
	WORD $0xd65f03c0  // ret
LBB0_21:
	WORD $0x91000508  // add	x8, x8, #1
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0xa940fbfd  // ldp	fp, lr, [sp, #8]
	WORD $0x910083ff  // add	sp, sp, #32
	WORD $0xd65f03c0  // ret
	  // .p2align 2, 0x00
_MASK_USE_NUMBER:
	WORD $0x00000002  // .long 2

TEXT ·__vunsigned(SB), NOSPLIT, $0-24
	NO_LOCAL_POINTERS

_entry:
	MOVD 16(g), R16
	SUB $96, RSP, R17
	CMP  R16, R17
	BLS  _stack_grow

_vunsigned:
	MOVD s+0(FP), R0
	MOVD p+8(FP), R1
	MOVD v+16(FP), R2
	MOVD ·_subr__vunsigned(SB), R11
	WORD $0x1000005e // adr x30, .+8
	JMP (R11)
	RET

_stack_grow:
	MOVD R30, R3
	CALL runtime·morestack_noctxt<>(SB)
	JMP  _entry
