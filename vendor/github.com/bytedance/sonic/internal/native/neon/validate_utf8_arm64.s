// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

#include "go_asm.h"
#include "funcdata.h"
#include "textflag.h"

TEXT ·__validate_utf8_entry__(SB), NOSPLIT, $48
	NO_LOCAL_POINTERS
	WORD $0x100000a0 // adr x0, .+20
	MOVD R0, ret(FP)
	RET
	  // .p2align 2, 0x00
_validate_utf8:
	WORD $0xd10103ff  // sub	sp, sp, #64
	WORD $0xa901cff4  // stp	x20, x19, [sp, #24]
	WORD $0xa902fbfd  // stp	fp, lr, [sp, #40]
	WORD $0xa93ffbfd  // stp	fp, lr, [sp, #-8]
	WORD $0xd10023fd  // sub	fp, sp, #8
	WORD $0xa9402408  // ldp	x8, x9, [x0]
	WORD $0xf940002c  // ldr	x12, [x1]
	WORD $0x8b09010a  // add	x10, x8, x9
	WORD $0xd1000d4b  // sub	x11, x10, #3
	WORD $0x8b0c0109  // add	x9, x8, x12
	WORD $0xeb0b013f  // cmp	x9, x11
	WORD $0x54000722  // b.hs	LBB0_15 $228(%rip)
	WORD $0x52981e0c  // mov	w12, #49392
	WORD $0x72a0180c  // movk	w12, #192, lsl #16
	WORD $0x52901c0d  // mov	w13, #32992
	WORD $0x72a0100d  // movk	w13, #128, lsl #16
	WORD $0x9100204e  // add	x14, x2, #8
	WORD $0x528401ef  // mov	w15, #8207
	WORD $0x528401b0  // mov	w16, #8205
	WORD $0x52981c11  // mov	w17, #49376
	WORD $0x52901800  // mov	w0, #32960
	WORD $0x52981f03  // mov	w3, #49400
	WORD $0x72b81803  // movk	w3, #49344, lsl #16
	WORD $0x528600e4  // mov	w4, #12295
	WORD $0x52901e05  // mov	w5, #33008
	WORD $0x72b01005  // movk	w5, #32896, lsl #16
	WORD $0x52860066  // mov	w6, #12291
	WORD $0x14000005  // b	LBB0_4 $20(%rip)
LBB0_2:
	WORD $0x52800033  // mov	w19, #1
LBB0_3:
	WORD $0x8b130129  // add	x9, x9, x19
	WORD $0xeb0b013f  // cmp	x9, x11
	WORD $0x540004a2  // b.hs	LBB0_15 $148(%rip)
LBB0_4:
	WORD $0x39c00127  // ldrsb	w7, [x9]
	WORD $0x36ffff67  // tbz	w7, #31, LBB0_2 $-20(%rip)
	WORD $0xb9400127  // ldr	w7, [x9]
	WORD $0x0a0c00f3  // and	w19, w7, w12
	WORD $0x6b0d027f  // cmp	w19, w13
	WORD $0x0a0f00f3  // and	w19, w7, w15
	WORD $0x7a500264  // ccmp	w19, w16, #4, eq
	WORD $0x7a401a64  // ccmp	w19, #0, #4, ne
	WORD $0x54000341  // b.ne	LBB0_14 $104(%rip)
	WORD $0x0a1100f3  // and	w19, w7, w17
	WORD $0x121f0cf4  // and	w20, w7, #0x1e
	WORD $0x6b00027f  // cmp	w19, w0
	WORD $0x7a400a84  // ccmp	w20, #0, #4, eq
	WORD $0x54000261  // b.ne	LBB0_13 $76(%rip)
	WORD $0x0a0300f3  // and	w19, w7, w3
	WORD $0x6b05027f  // cmp	w19, w5
	WORD $0x540000e1  // b.ne	LBB0_11 $28(%rip)
	WORD $0x0a0400f3  // and	w19, w7, w4
	WORD $0x340000b3  // cbz	w19, LBB0_11 $20(%rip)
	WORD $0x52800093  // mov	w19, #4
	WORD $0x3617fd27  // tbz	w7, #2, LBB0_3 $-92(%rip)
	WORD $0x0a0600e7  // and	w7, w7, w6
	WORD $0x34fffce7  // cbz	w7, LBB0_3 $-100(%rip)
LBB0_11:
	WORD $0xcb080127  // sub	x7, x9, x8
	WORD $0xf9400053  // ldr	x19, [x2]
	WORD $0xf140067f  // cmp	x19, #1, lsl #12
	WORD $0x54000c62  // b.hs	LBB0_33 $396(%rip)
	WORD $0x93407ce7  // sxtw	x7, w7
	WORD $0x91000674  // add	x20, x19, #1
	WORD $0xf9000054  // str	x20, [x2]
	WORD $0xf83379c7  // str	x7, [x14, x19, lsl #3]
	WORD $0x17ffffdd  // b	LBB0_2 $-140(%rip)
LBB0_13:
	WORD $0x52800053  // mov	w19, #2
	WORD $0x17ffffdc  // b	LBB0_3 $-144(%rip)
LBB0_14:
	WORD $0x52800073  // mov	w19, #3
	WORD $0x17ffffda  // b	LBB0_3 $-152(%rip)
LBB0_15:
	WORD $0xeb0a013f  // cmp	x9, x10
	WORD $0x54000962  // b.hs	LBB0_31 $300(%rip)
	WORD $0x52981e0b  // mov	w11, #49392
	WORD $0x72a0180b  // movk	w11, #192, lsl #16
	WORD $0x52901c0c  // mov	w12, #32992
	WORD $0x72a0100c  // movk	w12, #128, lsl #16
	WORD $0x9100204d  // add	x13, x2, #8
	WORD $0x528401ee  // mov	w14, #8207
	WORD $0x528401af  // mov	w15, #8205
	WORD $0x52981c10  // mov	w16, #49376
	WORD $0x52901811  // mov	w17, #32960
	WORD $0x14000004  // b	LBB0_18 $16(%rip)
LBB0_17:
	WORD $0x91000529  // add	x9, x9, #1
	WORD $0xeb0a013f  // cmp	x9, x10
	WORD $0x540007c2  // b.hs	LBB0_31 $248(%rip)
LBB0_18:
	WORD $0x39c00120  // ldrsb	w0, [x9]
	WORD $0x36ffff80  // tbz	w0, #31, LBB0_17 $-16(%rip)
	WORD $0x390053ff  // strb	wzr, [sp, #20]
	WORD $0x39004bff  // strb	wzr, [sp, #18]
	WORD $0xcb090145  // sub	x5, x10, x9
	WORD $0xf10008a7  // subs	x7, x5, #2
	WORD $0x540001a3  // b.lo	LBB0_22 $52(%rip)
	WORD $0x39400120  // ldrb	w0, [x9]
	WORD $0x39400523  // ldrb	w3, [x9, #1]
	WORD $0x390053e0  // strb	w0, [sp, #20]
	WORD $0x91000926  // add	x6, x9, #2
	WORD $0x91004be4  // add	x4, sp, #18
	WORD $0xaa0703e5  // mov	x5, x7
	WORD $0xb4000167  // cbz	x7, LBB0_23 $44(%rip)
LBB0_21:
	WORD $0x394000c0  // ldrb	w0, [x6]
	WORD $0x39000080  // strb	w0, [x4]
	WORD $0x394053e0  // ldrb	w0, [sp, #20]
	WORD $0x39404be4  // ldrb	w4, [sp, #18]
	WORD $0x14000007  // b	LBB0_24 $28(%rip)
LBB0_22:
	WORD $0x52800000  // mov	w0, #0
	WORD $0x52800003  // mov	w3, #0
	WORD $0x910053e4  // add	x4, sp, #20
	WORD $0xaa0903e6  // mov	x6, x9
	WORD $0xb5fffee5  // cbnz	x5, LBB0_21 $-36(%rip)
LBB0_23:
	WORD $0x52800004  // mov	w4, #0
LBB0_24:
	WORD $0x53185c63  // lsl	w3, w3, #8
	WORD $0x2a044063  // orr	w3, w3, w4, lsl #16
	WORD $0x2a000063  // orr	w3, w3, w0
	WORD $0x0a0b0064  // and	w4, w3, w11
	WORD $0x6b0c009f  // cmp	w4, w12
	WORD $0x0a0e0064  // and	w4, w3, w14
	WORD $0x7a4f0084  // ccmp	w4, w15, #4, eq
	WORD $0x7a401884  // ccmp	w4, #0, #4, ne
	WORD $0x54000301  // b.ne	LBB0_30 $96(%rip)
	WORD $0x721f0c1f  // tst	w0, #0x1e
	WORD $0x54000120  // b.eq	LBB0_28 $36(%rip)
	WORD $0x0a100060  // and	w0, w3, w16
	WORD $0x6b11001f  // cmp	w0, w17
	WORD $0x540000c1  // b.ne	LBB0_28 $24(%rip)
	WORD $0x52800040  // mov	w0, #2
	WORD $0x8b000129  // add	x9, x9, x0
	WORD $0xeb0a013f  // cmp	x9, x10
	WORD $0x54fffac3  // b.lo	LBB0_18 $-168(%rip)
	WORD $0x14000012  // b	LBB0_31 $72(%rip)
LBB0_28:
	WORD $0xcb080120  // sub	x0, x9, x8
	WORD $0xf9400043  // ldr	x3, [x2]
	WORD $0xf140047f  // cmp	x3, #1, lsl #12
	WORD $0x540002a2  // b.hs	LBB0_32 $84(%rip)
	WORD $0x93407c00  // sxtw	x0, w0
	WORD $0x91000464  // add	x4, x3, #1
	WORD $0xf9000044  // str	x4, [x2]
	WORD $0xf82379a0  // str	x0, [x13, x3, lsl #3]
	WORD $0x52800020  // mov	w0, #1
	WORD $0x8b000129  // add	x9, x9, x0
	WORD $0xeb0a013f  // cmp	x9, x10
	WORD $0x54fff923  // b.lo	LBB0_18 $-220(%rip)
	WORD $0x14000005  // b	LBB0_31 $20(%rip)
LBB0_30:
	WORD $0x52800060  // mov	w0, #3
	WORD $0x8b000129  // add	x9, x9, x0
	WORD $0xeb0a013f  // cmp	x9, x10
	WORD $0x54fff883  // b.lo	LBB0_18 $-240(%rip)
LBB0_31:
	WORD $0xd2800000  // mov	x0, #0
	WORD $0xcb080128  // sub	x8, x9, x8
	WORD $0xf9000028  // str	x8, [x1]
	WORD $0xa942fbfd  // ldp	fp, lr, [sp, #40]
	WORD $0xa941cff4  // ldp	x20, x19, [sp, #24]
	WORD $0x910103ff  // add	sp, sp, #64
	WORD $0xd65f03c0  // ret
LBB0_32:
	WORD $0xf9000020  // str	x0, [x1]
	WORD $0x92800000  // mov	x0, #-1
	WORD $0xa942fbfd  // ldp	fp, lr, [sp, #40]
	WORD $0xa941cff4  // ldp	x20, x19, [sp, #24]
	WORD $0x910103ff  // add	sp, sp, #64
	WORD $0xd65f03c0  // ret
LBB0_33:
	WORD $0xf9000027  // str	x7, [x1]
	WORD $0x92800000  // mov	x0, #-1
	WORD $0xa942fbfd  // ldp	fp, lr, [sp, #40]
	WORD $0xa941cff4  // ldp	x20, x19, [sp, #24]
	WORD $0x910103ff  // add	sp, sp, #64
	WORD $0xd65f03c0  // ret
	  // .p2align 2, 0x00
_MASK_USE_NUMBER:
	WORD $0x00000002  // .long 2

TEXT ·__validate_utf8(SB), NOSPLIT, $0-32
	NO_LOCAL_POINTERS

_entry:
	MOVD 16(g), R16
	SUB $128, RSP, R17
	CMP  R16, R17
	BLS  _stack_grow

_validate_utf8:
	MOVD s+0(FP), R0
	MOVD p+8(FP), R1
	MOVD m+16(FP), R2
	MOVD ·_subr__validate_utf8(SB), R11
	WORD $0x1000005e // adr x30, .+8
	JMP (R11)
	MOVD R0, ret+24(FP)
	RET

_stack_grow:
	MOVD R30, R3
	CALL runtime·morestack_noctxt<>(SB)
	JMP  _entry
