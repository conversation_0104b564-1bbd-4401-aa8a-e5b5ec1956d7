// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_skip_array = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // .quad 1
	0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000008 .quad 5
	//0x00000010 LCPI0_11
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000010 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	//0x00000020 LCPI0_12
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x00000020 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	//0x00000030 LCPI0_13
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000030 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x00000040 LCPI0_14
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000040 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000050 LCPI0_15
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000050 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000060 LCPI0_16
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000060 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x00000070 LCPI0_17
	0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, //0x00000070 QUAD $0x6565656565656565; QUAD $0x6565656565656565  // .space 16, 'eeeeeeeeeeeeeeee'
	//0x00000080 .p2align 5, 0x00
	//0x00000080 LCPI0_1
	0x20, //0x00000080 .byte 32
	0x00, //0x00000081 .byte 0
	0x00, //0x00000082 .byte 0
	0x00, //0x00000083 .byte 0
	0x00, //0x00000084 .byte 0
	0x00, //0x00000085 .byte 0
	0x00, //0x00000086 .byte 0
	0x00, //0x00000087 .byte 0
	0x00, //0x00000088 .byte 0
	0x09, //0x00000089 .byte 9
	0x0a, //0x0000008a .byte 10
	0x00, //0x0000008b .byte 0
	0x00, //0x0000008c .byte 0
	0x0d, //0x0000008d .byte 13
	0x00, //0x0000008e .byte 0
	0x00, //0x0000008f .byte 0
	0x20, //0x00000090 .byte 32
	0x00, //0x00000091 .byte 0
	0x00, //0x00000092 .byte 0
	0x00, //0x00000093 .byte 0
	0x00, //0x00000094 .byte 0
	0x00, //0x00000095 .byte 0
	0x00, //0x00000096 .byte 0
	0x00, //0x00000097 .byte 0
	0x00, //0x00000098 .byte 0
	0x09, //0x00000099 .byte 9
	0x0a, //0x0000009a .byte 10
	0x00, //0x0000009b .byte 0
	0x00, //0x0000009c .byte 0
	0x0d, //0x0000009d .byte 13
	0x00, //0x0000009e .byte 0
	0x00, //0x0000009f .byte 0
	//0x000000a0 LCPI0_2
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x000000a0 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x000000b0 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x000000c0 LCPI0_3
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x000000c0 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x000000d0 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x000000e0 LCPI0_4
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x000000e0 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x000000f0 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000100 LCPI0_5
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000100 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000110 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	//0x00000120 LCPI0_6
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x00000120 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x00000130 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	//0x00000140 LCPI0_7
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000140 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000150 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x00000160 LCPI0_8
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000160 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000170 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000180 LCPI0_9
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000180 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000190 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x000001a0 LCPI0_10
	0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, //0x000001a0 QUAD $0x6565656565656565; QUAD $0x6565656565656565  // .space 16, 'eeeeeeeeeeeeeeee'
	0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, //0x000001b0 QUAD $0x6565656565656565; QUAD $0x6565656565656565  // .space 16, 'eeeeeeeeeeeeeeee'
	//0x000001c0 .p2align 4, 0x90
	//0x000001c0 _skip_array
	0x55, //0x000001c0 pushq        %rbp
	0x48, 0x89, 0xe5, //0x000001c1 movq         %rsp, %rbp
	0x41, 0x57, //0x000001c4 pushq        %r15
	0x41, 0x56, //0x000001c6 pushq        %r14
	0x41, 0x55, //0x000001c8 pushq        %r13
	0x41, 0x54, //0x000001ca pushq        %r12
	0x53, //0x000001cc pushq        %rbx
	0x48, 0x83, 0xec, 0x48, //0x000001cd subq         $72, %rsp
	0x48, 0x89, 0x4d, 0x98, //0x000001d1 movq         %rcx, $-104(%rbp)
	0x49, 0x89, 0xd6, //0x000001d5 movq         %rdx, %r14
	0x49, 0x89, 0xf2, //0x000001d8 movq         %rsi, %r10
	0x48, 0x89, 0x7d, 0xb0, //0x000001db movq         %rdi, $-80(%rbp)
	0xc5, 0xfa, 0x6f, 0x05, 0x19, 0xfe, 0xff, 0xff, //0x000001df vmovdqu      $-487(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0xc5, 0xfa, 0x7f, 0x02, //0x000001e7 vmovdqu      %xmm0, (%rdx)
	0x4c, 0x8b, 0x26, //0x000001eb movq         (%rsi), %r12
	0x48, 0xc7, 0x45, 0x90, 0xff, 0xff, 0xff, 0xff, //0x000001ee movq         $-1, $-112(%rbp)
	0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x000001f6 movl         $1, %r8d
	0xc5, 0xfe, 0x6f, 0x2d, 0x7c, 0xfe, 0xff, 0xff, //0x000001fc vmovdqu      $-388(%rip), %ymm5  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x94, 0xfe, 0xff, 0xff, //0x00000204 vmovdqu      $-364(%rip), %ymm6  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0xac, 0xfe, 0xff, 0xff, //0x0000020c vmovdqu      $-340(%rip), %ymm7  /* LCPI0_3+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0xc4, 0xfe, 0xff, 0xff, //0x00000214 vmovdqu      $-316(%rip), %ymm8  /* LCPI0_4+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000021c vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x15, 0xd7, 0xfe, 0xff, 0xff, //0x00000221 vmovdqu      $-297(%rip), %ymm10  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0xef, 0xfe, 0xff, 0xff, //0x00000229 vmovdqu      $-273(%rip), %ymm11  /* LCPI0_6+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x07, 0xff, 0xff, 0xff, //0x00000231 vmovdqu      $-249(%rip), %ymm12  /* LCPI0_7+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x1f, 0xff, 0xff, 0xff, //0x00000239 vmovdqu      $-225(%rip), %ymm13  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x37, 0xff, 0xff, 0xff, //0x00000241 vmovdqu      $-201(%rip), %ymm14  /* LCPI0_9+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0x4f, 0xff, 0xff, 0xff, //0x00000249 vmovdqu      $-177(%rip), %ymm15  /* LCPI0_10+0(%rip) */
	0x48, 0x89, 0x75, 0xd0, //0x00000251 movq         %rsi, $-48(%rbp)
	0x48, 0x89, 0x55, 0xc8, //0x00000255 movq         %rdx, $-56(%rbp)
	0xe9, 0x62, 0x00, 0x00, 0x00, //0x00000259 jmp          LBB0_5
	//0x0000025e LBB0_72
	0x4c, 0x89, 0xe1, //0x0000025e movq         %r12, %rcx
	//0x00000261 LBB0_199
	0x4c, 0x89, 0xe0, //0x00000261 movq         %r12, %rax
	0x4e, 0x8d, 0x64, 0x31, 0xff, //0x00000264 leaq         $-1(%rcx,%r14), %r12
	0x4c, 0x8b, 0x55, 0xd0, //0x00000269 movq         $-48(%rbp), %r10
	0x4d, 0x89, 0x22, //0x0000026d movq         %r12, (%r10)
	0x4c, 0x89, 0xe9, //0x00000270 movq         %r13, %rcx
	0x48, 0x85, 0xc0, //0x00000273 testq        %rax, %rax
	0x4c, 0x8b, 0x75, 0xc8, //0x00000276 movq         $-56(%rbp), %r14
	0x0f, 0x8e, 0x69, 0x25, 0x00, 0x00, //0x0000027a jle          LBB0_501
	//0x00000280 .p2align 4, 0x90
	//0x00000280 LBB0_3
	0x49, 0x8b, 0x16, //0x00000280 movq         (%r14), %rdx
	0x49, 0x89, 0xd0, //0x00000283 movq         %rdx, %r8
	0x48, 0x8b, 0x4d, 0x90, //0x00000286 movq         $-112(%rbp), %rcx
	0x48, 0x85, 0xd2, //0x0000028a testq        %rdx, %rdx
	0x0f, 0x85, 0x2d, 0x00, 0x00, 0x00, //0x0000028d jne          LBB0_5
	0xe9, 0x51, 0x25, 0x00, 0x00, //0x00000293 jmp          LBB0_501
	//0x00000298 LBB0_1
	0x4c, 0x89, 0xe0, //0x00000298 movq         %r12, %rax
	0x4d, 0x8d, 0x65, 0x04, //0x0000029b leaq         $4(%r13), %r12
	0x4d, 0x89, 0x22, //0x0000029f movq         %r12, (%r10)
	0x4c, 0x89, 0xe9, //0x000002a2 movq         %r13, %rcx
	0x48, 0x85, 0xc0, //0x000002a5 testq        %rax, %rax
	0x0f, 0x8f, 0xd2, 0xff, 0xff, 0xff, //0x000002a8 jg           LBB0_3
	0xe9, 0x36, 0x25, 0x00, 0x00, //0x000002ae jmp          LBB0_501
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000002b3 .p2align 4, 0x90
	//0x000002c0 LBB0_5
	0x48, 0x8b, 0x45, 0xb0, //0x000002c0 movq         $-80(%rbp), %rax
	0x4c, 0x8b, 0x08, //0x000002c4 movq         (%rax), %r9
	0x48, 0x8b, 0x48, 0x08, //0x000002c7 movq         $8(%rax), %rcx
	0x49, 0x39, 0xcc, //0x000002cb cmpq         %rcx, %r12
	0x0f, 0x83, 0x2c, 0x00, 0x00, 0x00, //0x000002ce jae          LBB0_10
	0x43, 0x8a, 0x04, 0x21, //0x000002d4 movb         (%r9,%r12), %al
	0x3c, 0x0d, //0x000002d8 cmpb         $13, %al
	0x0f, 0x84, 0x20, 0x00, 0x00, 0x00, //0x000002da je           LBB0_10
	0x3c, 0x20, //0x000002e0 cmpb         $32, %al
	0x0f, 0x84, 0x18, 0x00, 0x00, 0x00, //0x000002e2 je           LBB0_10
	0x04, 0xf7, //0x000002e8 addb         $-9, %al
	0x3c, 0x01, //0x000002ea cmpb         $1, %al
	0x0f, 0x86, 0x0e, 0x00, 0x00, 0x00, //0x000002ec jbe          LBB0_10
	0x4d, 0x89, 0xe5, //0x000002f2 movq         %r12, %r13
	0xe9, 0x8e, 0x01, 0x00, 0x00, //0x000002f5 jmp          LBB0_36
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000002fa .p2align 4, 0x90
	//0x00000300 LBB0_10
	0x4d, 0x8d, 0x6c, 0x24, 0x01, //0x00000300 leaq         $1(%r12), %r13
	0x49, 0x39, 0xcd, //0x00000305 cmpq         %rcx, %r13
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x00000308 jae          LBB0_14
	0x43, 0x8a, 0x14, 0x29, //0x0000030e movb         (%r9,%r13), %dl
	0x80, 0xfa, 0x0d, //0x00000312 cmpb         $13, %dl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000315 je           LBB0_14
	0x80, 0xfa, 0x20, //0x0000031b cmpb         $32, %dl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000031e je           LBB0_14
	0x80, 0xc2, 0xf7, //0x00000324 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00000327 cmpb         $1, %dl
	0x0f, 0x87, 0x58, 0x01, 0x00, 0x00, //0x0000032a ja           LBB0_36
	//0x00000330 .p2align 4, 0x90
	//0x00000330 LBB0_14
	0x4d, 0x8d, 0x6c, 0x24, 0x02, //0x00000330 leaq         $2(%r12), %r13
	0x49, 0x39, 0xcd, //0x00000335 cmpq         %rcx, %r13
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x00000338 jae          LBB0_18
	0x43, 0x8a, 0x14, 0x29, //0x0000033e movb         (%r9,%r13), %dl
	0x80, 0xfa, 0x0d, //0x00000342 cmpb         $13, %dl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000345 je           LBB0_18
	0x80, 0xfa, 0x20, //0x0000034b cmpb         $32, %dl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000034e je           LBB0_18
	0x80, 0xc2, 0xf7, //0x00000354 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00000357 cmpb         $1, %dl
	0x0f, 0x87, 0x28, 0x01, 0x00, 0x00, //0x0000035a ja           LBB0_36
	//0x00000360 .p2align 4, 0x90
	//0x00000360 LBB0_18
	0x4d, 0x8d, 0x6c, 0x24, 0x03, //0x00000360 leaq         $3(%r12), %r13
	0x49, 0x39, 0xcd, //0x00000365 cmpq         %rcx, %r13
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x00000368 jae          LBB0_22
	0x43, 0x8a, 0x14, 0x29, //0x0000036e movb         (%r9,%r13), %dl
	0x80, 0xfa, 0x0d, //0x00000372 cmpb         $13, %dl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000375 je           LBB0_22
	0x80, 0xfa, 0x20, //0x0000037b cmpb         $32, %dl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000037e je           LBB0_22
	0x80, 0xc2, 0xf7, //0x00000384 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00000387 cmpb         $1, %dl
	0x0f, 0x87, 0xf8, 0x00, 0x00, 0x00, //0x0000038a ja           LBB0_36
	//0x00000390 .p2align 4, 0x90
	//0x00000390 LBB0_22
	0x4d, 0x8d, 0x6c, 0x24, 0x04, //0x00000390 leaq         $4(%r12), %r13
	0x48, 0x89, 0xca, //0x00000395 movq         %rcx, %rdx
	0x4c, 0x29, 0xea, //0x00000398 subq         %r13, %rdx
	0x0f, 0x86, 0xd2, 0x23, 0x00, 0x00, //0x0000039b jbe          LBB0_473
	0x4d, 0x01, 0xcd, //0x000003a1 addq         %r9, %r13
	0x48, 0x83, 0xfa, 0x20, //0x000003a4 cmpq         $32, %rdx
	0x0f, 0x82, 0x56, 0x00, 0x00, 0x00, //0x000003a8 jb           LBB0_28
	0x48, 0x89, 0xce, //0x000003ae movq         %rcx, %rsi
	0x4c, 0x29, 0xe6, //0x000003b1 subq         %r12, %rsi
	0x48, 0x83, 0xc6, 0xdc, //0x000003b4 addq         $-36, %rsi
	0x48, 0x89, 0xf7, //0x000003b8 movq         %rsi, %rdi
	0x48, 0x83, 0xe7, 0xe0, //0x000003bb andq         $-32, %rdi
	0x4c, 0x01, 0xe7, //0x000003bf addq         %r12, %rdi
	0x49, 0x8d, 0x7c, 0x39, 0x24, //0x000003c2 leaq         $36(%r9,%rdi), %rdi
	0x83, 0xe6, 0x1f, //0x000003c7 andl         $31, %esi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000003ca .p2align 4, 0x90
	//0x000003d0 LBB0_25
	0xc4, 0xc1, 0x7e, 0x6f, 0x45, 0x00, //0x000003d0 vmovdqu      (%r13), %ymm0
	0xc4, 0xe2, 0x55, 0x00, 0xc8, //0x000003d6 vpshufb      %ymm0, %ymm5, %ymm1
	0xc5, 0xfd, 0x74, 0xc1, //0x000003db vpcmpeqb     %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd8, //0x000003df vpmovmskb    %ymm0, %ebx
	0x83, 0xfb, 0xff, //0x000003e3 cmpl         $-1, %ebx
	0x0f, 0x85, 0x84, 0x00, 0x00, 0x00, //0x000003e6 jne          LBB0_35
	0x49, 0x83, 0xc5, 0x20, //0x000003ec addq         $32, %r13
	0x48, 0x83, 0xc2, 0xe0, //0x000003f0 addq         $-32, %rdx
	0x48, 0x83, 0xfa, 0x1f, //0x000003f4 cmpq         $31, %rdx
	0x0f, 0x87, 0xd2, 0xff, 0xff, 0xff, //0x000003f8 ja           LBB0_25
	0x48, 0x89, 0xf2, //0x000003fe movq         %rsi, %rdx
	0x49, 0x89, 0xfd, //0x00000401 movq         %rdi, %r13
	//0x00000404 LBB0_28
	0x48, 0x85, 0xd2, //0x00000404 testq        %rdx, %rdx
	0x0f, 0x84, 0x47, 0x00, 0x00, 0x00, //0x00000407 je           LBB0_34
	0x49, 0x8d, 0x74, 0x15, 0x00, //0x0000040d leaq         (%r13,%rdx), %rsi
	0x49, 0xff, 0xc5, //0x00000412 incq         %r13
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000415 .p2align 4, 0x90
	//0x00000420 LBB0_30
	0x41, 0x0f, 0xbe, 0x7d, 0xff, //0x00000420 movsbl       $-1(%r13), %edi
	0x83, 0xff, 0x20, //0x00000425 cmpl         $32, %edi
	0x0f, 0x87, 0x09, 0x11, 0x00, 0x00, //0x00000428 ja           LBB0_268
	0x48, 0xb8, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000042e movabsq      $4294977024, %rax
	0x48, 0x0f, 0xa3, 0xf8, //0x00000438 btq          %rdi, %rax
	0x0f, 0x83, 0xf5, 0x10, 0x00, 0x00, //0x0000043c jae          LBB0_268
	0x48, 0xff, 0xca, //0x00000442 decq         %rdx
	0x49, 0xff, 0xc5, //0x00000445 incq         %r13
	0x48, 0x85, 0xd2, //0x00000448 testq        %rdx, %rdx
	0x0f, 0x85, 0xcf, 0xff, 0xff, 0xff, //0x0000044b jne          LBB0_30
	0x49, 0x89, 0xf5, //0x00000451 movq         %rsi, %r13
	//0x00000454 LBB0_34
	0x4d, 0x29, 0xcd, //0x00000454 subq         %r9, %r13
	0x49, 0x39, 0xcd, //0x00000457 cmpq         %rcx, %r13
	0x0f, 0x82, 0x28, 0x00, 0x00, 0x00, //0x0000045a jb           LBB0_36
	0xe9, 0x11, 0x23, 0x00, 0x00, //0x00000460 jmp          LBB0_474
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000465 .p2align 4, 0x90
	//0x00000470 LBB0_35
	0x4d, 0x29, 0xcd, //0x00000470 subq         %r9, %r13
	0xf7, 0xd3, //0x00000473 notl         %ebx
	0x48, 0x63, 0xd3, //0x00000475 movslq       %ebx, %rdx
	0x48, 0x0f, 0xbc, 0xd2, //0x00000478 bsfq         %rdx, %rdx
	0x49, 0x01, 0xd5, //0x0000047c addq         %rdx, %r13
	0x49, 0x39, 0xcd, //0x0000047f cmpq         %rcx, %r13
	0x0f, 0x83, 0xee, 0x22, 0x00, 0x00, //0x00000482 jae          LBB0_474
	//0x00000488 LBB0_36
	0x4d, 0x8d, 0x65, 0x01, //0x00000488 leaq         $1(%r13), %r12
	0x4d, 0x89, 0x22, //0x0000048c movq         %r12, (%r10)
	0x43, 0x0f, 0xbe, 0x3c, 0x29, //0x0000048f movsbl       (%r9,%r13), %edi
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000494 movq         $-1, %rcx
	0x85, 0xff, //0x0000049b testl        %edi, %edi
	0x0f, 0x84, 0x46, 0x23, 0x00, 0x00, //0x0000049d je           LBB0_501
	0x4c, 0x89, 0xee, //0x000004a3 movq         %r13, %rsi
	0x48, 0xf7, 0xd6, //0x000004a6 notq         %rsi
	0x49, 0x8d, 0x50, 0xff, //0x000004a9 leaq         $-1(%r8), %rdx
	0x43, 0x8b, 0x1c, 0xc6, //0x000004ad movl         (%r14,%r8,8), %ebx
	0x48, 0x8b, 0x45, 0x90, //0x000004b1 movq         $-112(%rbp), %rax
	0x48, 0x83, 0xf8, 0xff, //0x000004b5 cmpq         $-1, %rax
	0x49, 0x0f, 0x44, 0xc5, //0x000004b9 cmoveq       %r13, %rax
	0x48, 0x89, 0x45, 0x90, //0x000004bd movq         %rax, $-112(%rbp)
	0xff, 0xcb, //0x000004c1 decl         %ebx
	0x83, 0xfb, 0x05, //0x000004c3 cmpl         $5, %ebx
	0x0f, 0x87, 0x0d, 0x02, 0x00, 0x00, //0x000004c6 ja           LBB0_67
	0x48, 0x8d, 0x05, 0x45, 0x25, 0x00, 0x00, //0x000004cc leaq         $9541(%rip), %rax  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x1c, 0x98, //0x000004d3 movslq       (%rax,%rbx,4), %rbx
	0x48, 0x01, 0xc3, //0x000004d7 addq         %rax, %rbx
	0xff, 0xe3, //0x000004da jmpq         *%rbx
	//0x000004dc LBB0_39
	0x83, 0xff, 0x2c, //0x000004dc cmpl         $44, %edi
	0x0f, 0x84, 0x01, 0x06, 0x00, 0x00, //0x000004df je           LBB0_73
	0x83, 0xff, 0x5d, //0x000004e5 cmpl         $93, %edi
	0x0f, 0x84, 0xd3, 0x01, 0x00, 0x00, //0x000004e8 je           LBB0_41
	0xe9, 0xef, 0x22, 0x00, 0x00, //0x000004ee jmp          LBB0_500
	//0x000004f3 LBB0_42
	0x40, 0x80, 0xff, 0x5d, //0x000004f3 cmpb         $93, %dil
	0x0f, 0x84, 0xc4, 0x01, 0x00, 0x00, //0x000004f7 je           LBB0_41
	0x4b, 0xc7, 0x04, 0xc6, 0x01, 0x00, 0x00, 0x00, //0x000004fd movq         $1, (%r14,%r8,8)
	0x83, 0xff, 0x7b, //0x00000505 cmpl         $123, %edi
	0x0f, 0x86, 0xd7, 0x01, 0x00, 0x00, //0x00000508 jbe          LBB0_68
	0xe9, 0xcf, 0x22, 0x00, 0x00, //0x0000050e jmp          LBB0_500
	//0x00000513 LBB0_44
	0x40, 0x80, 0xff, 0x22, //0x00000513 cmpb         $34, %dil
	0x0f, 0x85, 0xc5, 0x22, 0x00, 0x00, //0x00000517 jne          LBB0_500
	0x4b, 0xc7, 0x04, 0xc6, 0x04, 0x00, 0x00, 0x00, //0x0000051d movq         $4, (%r14,%r8,8)
	0x48, 0x8b, 0x45, 0xb0, //0x00000525 movq         $-80(%rbp), %rax
	0x48, 0x8b, 0x50, 0x08, //0x00000529 movq         $8(%rax), %rdx
	0xf6, 0x45, 0x98, 0x20, //0x0000052d testb        $32, $-104(%rbp)
	0x4c, 0x89, 0x65, 0xa8, //0x00000531 movq         %r12, $-88(%rbp)
	0x48, 0x89, 0x55, 0xb8, //0x00000535 movq         %rdx, $-72(%rbp)
	0x0f, 0x85, 0xc9, 0x05, 0x00, 0x00, //0x00000539 jne          LBB0_75
	0x48, 0x89, 0xd1, //0x0000053f movq         %rdx, %rcx
	0x4c, 0x29, 0xe1, //0x00000542 subq         %r12, %rcx
	0x0f, 0x84, 0x34, 0x24, 0x00, 0x00, //0x00000545 je           LBB0_507
	0x4b, 0x8d, 0x1c, 0x21, //0x0000054b leaq         (%r9,%r12), %rbx
	0x48, 0x83, 0xf9, 0x40, //0x0000054f cmpq         $64, %rcx
	0x0f, 0x82, 0xea, 0x18, 0x00, 0x00, //0x00000553 jb           LBB0_356
	0x41, 0x89, 0xcf, //0x00000559 movl         %ecx, %r15d
	0x41, 0x83, 0xe7, 0x3f, //0x0000055c andl         $63, %r15d
	0x48, 0x8d, 0x54, 0x32, 0xc0, //0x00000560 leaq         $-64(%rdx,%rsi), %rdx
	0x48, 0x83, 0xe2, 0xc0, //0x00000565 andq         $-64, %rdx
	0x4c, 0x01, 0xe2, //0x00000569 addq         %r12, %rdx
	0x4d, 0x89, 0xcb, //0x0000056c movq         %r9, %r11
	0x4d, 0x8d, 0x4c, 0x11, 0x40, //0x0000056f leaq         $64(%r9,%rdx), %r9
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000574 movq         $-1, %r8
	0x45, 0x31, 0xf6, //0x0000057b xorl         %r14d, %r14d
	0x90, 0x90, //0x0000057e .p2align 4, 0x90
	//0x00000580 LBB0_49
	0xc5, 0xfe, 0x6f, 0x03, //0x00000580 vmovdqu      (%rbx), %ymm0
	0xc5, 0xfe, 0x6f, 0x4b, 0x20, //0x00000584 vmovdqu      $32(%rbx), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x00000589 vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x0000058d vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x00000591 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00000595 vpmovmskb    %ymm2, %edx
	0xc5, 0xfd, 0x74, 0xc7, //0x00000599 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x0000059d vpmovmskb    %ymm0, %esi
	0xc5, 0xf5, 0x74, 0xc7, //0x000005a1 vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x000005a5 vpmovmskb    %ymm0, %eax
	0x48, 0xc1, 0xe2, 0x20, //0x000005a9 shlq         $32, %rdx
	0x48, 0xc1, 0xe0, 0x20, //0x000005ad shlq         $32, %rax
	0x48, 0x09, 0xc6, //0x000005b1 orq          %rax, %rsi
	0x49, 0x83, 0xf8, 0xff, //0x000005b4 cmpq         $-1, %r8
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x000005b8 jne          LBB0_51
	0x48, 0x85, 0xf6, //0x000005be testq        %rsi, %rsi
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x000005c1 jne          LBB0_58
	//0x000005c7 LBB0_51
	0x48, 0x09, 0xfa, //0x000005c7 orq          %rdi, %rdx
	0x48, 0x89, 0xf0, //0x000005ca movq         %rsi, %rax
	0x4c, 0x09, 0xf0, //0x000005cd orq          %r14, %rax
	0x0f, 0x85, 0x3c, 0x00, 0x00, 0x00, //0x000005d0 jne          LBB0_59
	//0x000005d6 LBB0_52
	0x48, 0x85, 0xd2, //0x000005d6 testq        %rdx, %rdx
	0x0f, 0x85, 0xdb, 0x14, 0x00, 0x00, //0x000005d9 jne          LBB0_60
	//0x000005df LBB0_53
	0x48, 0x83, 0xc1, 0xc0, //0x000005df addq         $-64, %rcx
	0x48, 0x83, 0xc3, 0x40, //0x000005e3 addq         $64, %rbx
	0x48, 0x83, 0xf9, 0x3f, //0x000005e7 cmpq         $63, %rcx
	0x0f, 0x87, 0x8f, 0xff, 0xff, 0xff, //0x000005eb ja           LBB0_49
	0xe9, 0x83, 0x14, 0x00, 0x00, //0x000005f1 jmp          LBB0_54
	//0x000005f6 LBB0_58
	0x48, 0x89, 0xd8, //0x000005f6 movq         %rbx, %rax
	0x4c, 0x29, 0xd8, //0x000005f9 subq         %r11, %rax
	0x4c, 0x0f, 0xbc, 0xc6, //0x000005fc bsfq         %rsi, %r8
	0x49, 0x01, 0xc0, //0x00000600 addq         %rax, %r8
	0x48, 0x09, 0xfa, //0x00000603 orq          %rdi, %rdx
	0x48, 0x89, 0xf0, //0x00000606 movq         %rsi, %rax
	0x4c, 0x09, 0xf0, //0x00000609 orq          %r14, %rax
	0x0f, 0x84, 0xc4, 0xff, 0xff, 0xff, //0x0000060c je           LBB0_52
	//0x00000612 LBB0_59
	0x4c, 0x89, 0xf0, //0x00000612 movq         %r14, %rax
	0x48, 0xf7, 0xd0, //0x00000615 notq         %rax
	0x48, 0x21, 0xf0, //0x00000618 andq         %rsi, %rax
	0x4c, 0x8d, 0x14, 0x00, //0x0000061b leaq         (%rax,%rax), %r10
	0x4d, 0x09, 0xf2, //0x0000061f orq          %r14, %r10
	0x4c, 0x89, 0xd7, //0x00000622 movq         %r10, %rdi
	0x48, 0xf7, 0xd7, //0x00000625 notq         %rdi
	0x48, 0x21, 0xf7, //0x00000628 andq         %rsi, %rdi
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000062b movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf7, //0x00000635 andq         %rsi, %rdi
	0x45, 0x31, 0xf6, //0x00000638 xorl         %r14d, %r14d
	0x48, 0x01, 0xc7, //0x0000063b addq         %rax, %rdi
	0x41, 0x0f, 0x92, 0xc6, //0x0000063e setb         %r14b
	0x48, 0x01, 0xff, //0x00000642 addq         %rdi, %rdi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000645 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc7, //0x0000064f xorq         %rax, %rdi
	0x4c, 0x21, 0xd7, //0x00000652 andq         %r10, %rdi
	0x4c, 0x8b, 0x55, 0xd0, //0x00000655 movq         $-48(%rbp), %r10
	0x48, 0xf7, 0xd7, //0x00000659 notq         %rdi
	0x48, 0x21, 0xfa, //0x0000065c andq         %rdi, %rdx
	0x48, 0x85, 0xd2, //0x0000065f testq        %rdx, %rdx
	0x0f, 0x84, 0x77, 0xff, 0xff, 0xff, //0x00000662 je           LBB0_53
	0xe9, 0x4d, 0x14, 0x00, 0x00, //0x00000668 jmp          LBB0_60
	//0x0000066d LBB0_61
	0x40, 0x80, 0xff, 0x3a, //0x0000066d cmpb         $58, %dil
	0x0f, 0x85, 0x6b, 0x21, 0x00, 0x00, //0x00000671 jne          LBB0_500
	0x4b, 0xc7, 0x04, 0xc6, 0x00, 0x00, 0x00, 0x00, //0x00000677 movq         $0, (%r14,%r8,8)
	0xe9, 0xfc, 0xfb, 0xff, 0xff, //0x0000067f jmp          LBB0_3
	//0x00000684 LBB0_63
	0x83, 0xff, 0x2c, //0x00000684 cmpl         $44, %edi
	0x0f, 0x85, 0x2b, 0x00, 0x00, 0x00, //0x00000687 jne          LBB0_64
	0x49, 0x81, 0xf8, 0xff, 0x0f, 0x00, 0x00, //0x0000068d cmpq         $4095, %r8
	0x0f, 0x8f, 0xe8, 0x20, 0x00, 0x00, //0x00000694 jg           LBB0_489
	0x49, 0x8d, 0x40, 0x01, //0x0000069a leaq         $1(%r8), %rax
	0x49, 0x89, 0x06, //0x0000069e movq         %rax, (%r14)
	0x4b, 0xc7, 0x44, 0xc6, 0x08, 0x03, 0x00, 0x00, 0x00, //0x000006a1 movq         $3, $8(%r14,%r8,8)
	0xe9, 0xd1, 0xfb, 0xff, 0xff, //0x000006aa jmp          LBB0_3
	//0x000006af LBB0_65
	0x83, 0xff, 0x22, //0x000006af cmpl         $34, %edi
	0x0f, 0x84, 0xb7, 0x05, 0x00, 0x00, //0x000006b2 je           LBB0_92
	//0x000006b8 LBB0_64
	0x83, 0xff, 0x7d, //0x000006b8 cmpl         $125, %edi
	0x0f, 0x85, 0x21, 0x21, 0x00, 0x00, //0x000006bb jne          LBB0_500
	//0x000006c1 LBB0_41
	0x49, 0x89, 0x16, //0x000006c1 movq         %rdx, (%r14)
	0x49, 0x89, 0xd0, //0x000006c4 movq         %rdx, %r8
	0x48, 0x8b, 0x4d, 0x90, //0x000006c7 movq         $-112(%rbp), %rcx
	0x48, 0x85, 0xd2, //0x000006cb testq        %rdx, %rdx
	0x0f, 0x85, 0xec, 0xfb, 0xff, 0xff, //0x000006ce jne          LBB0_5
	0xe9, 0x10, 0x21, 0x00, 0x00, //0x000006d4 jmp          LBB0_501
	//0x000006d9 LBB0_67
	0x49, 0x89, 0x16, //0x000006d9 movq         %rdx, (%r14)
	0x83, 0xff, 0x7b, //0x000006dc cmpl         $123, %edi
	0x0f, 0x87, 0xfd, 0x20, 0x00, 0x00, //0x000006df ja           LBB0_500
	//0x000006e5 LBB0_68
	0x4f, 0x8d, 0x3c, 0x29, //0x000006e5 leaq         (%r9,%r13), %r15
	0x89, 0xf8, //0x000006e9 movl         %edi, %eax
	0x48, 0x8d, 0x15, 0x3e, 0x23, 0x00, 0x00, //0x000006eb leaq         $9022(%rip), %rdx  /* LJTI0_1+0(%rip) */
	0x48, 0x63, 0x04, 0x82, //0x000006f2 movslq       (%rdx,%rax,4), %rax
	0x48, 0x01, 0xd0, //0x000006f6 addq         %rdx, %rax
	0xff, 0xe0, //0x000006f9 jmpq         *%rax
	//0x000006fb LBB0_69
	0x48, 0x8b, 0x45, 0xb0, //0x000006fb movq         $-80(%rbp), %rax
	0x4c, 0x8b, 0x50, 0x08, //0x000006ff movq         $8(%rax), %r10
	0x4d, 0x29, 0xea, //0x00000703 subq         %r13, %r10
	0x0f, 0x84, 0x9c, 0x20, 0x00, 0x00, //0x00000706 je           LBB0_477
	0x41, 0x80, 0x3f, 0x30, //0x0000070c cmpb         $48, (%r15)
	0x0f, 0x85, 0x3a, 0x00, 0x00, 0x00, //0x00000710 jne          LBB0_132
	0x41, 0xbe, 0x01, 0x00, 0x00, 0x00, //0x00000716 movl         $1, %r14d
	0x49, 0x83, 0xfa, 0x01, //0x0000071c cmpq         $1, %r10
	0x0f, 0x84, 0x38, 0xfb, 0xff, 0xff, //0x00000720 je           LBB0_72
	0x43, 0x8a, 0x0c, 0x21, //0x00000726 movb         (%r9,%r12), %cl
	0x80, 0xc1, 0xd2, //0x0000072a addb         $-46, %cl
	0x80, 0xf9, 0x37, //0x0000072d cmpb         $55, %cl
	0x0f, 0x87, 0x28, 0xfb, 0xff, 0xff, //0x00000730 ja           LBB0_72
	0x0f, 0xb6, 0xc1, //0x00000736 movzbl       %cl, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00000739 movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x00000743 btq          %rax, %rcx
	0x4c, 0x89, 0xe1, //0x00000747 movq         %r12, %rcx
	0x0f, 0x83, 0x11, 0xfb, 0xff, 0xff, //0x0000074a jae          LBB0_199
	//0x00000750 LBB0_132
	0x4c, 0x89, 0x65, 0xa8, //0x00000750 movq         %r12, $-88(%rbp)
	0x49, 0x83, 0xfa, 0x20, //0x00000754 cmpq         $32, %r10
	0x0f, 0x82, 0xbe, 0x16, 0x00, 0x00, //0x00000758 jb           LBB0_355
	0x49, 0x8d, 0x4a, 0xe0, //0x0000075e leaq         $-32(%r10), %rcx
	0x48, 0x89, 0xc8, //0x00000762 movq         %rcx, %rax
	0x48, 0x83, 0xe0, 0xe0, //0x00000765 andq         $-32, %rax
	0x4e, 0x8d, 0x74, 0x38, 0x20, //0x00000769 leaq         $32(%rax,%r15), %r14
	0x83, 0xe1, 0x1f, //0x0000076e andl         $31, %ecx
	0x48, 0x89, 0x4d, 0xc0, //0x00000771 movq         %rcx, $-64(%rbp)
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000775 movq         $-1, %r8
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x0000077c movq         $-1, %r12
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000783 movq         $-1, %rax
	0x4d, 0x89, 0xfb, //0x0000078a movq         %r15, %r11
	0x90, 0x90, 0x90, //0x0000078d .p2align 4, 0x90
	//0x00000790 LBB0_134
	0xc4, 0xc1, 0x7e, 0x6f, 0x03, //0x00000790 vmovdqu      (%r11), %ymm0
	0xc4, 0xc1, 0x7d, 0x64, 0xca, //0x00000795 vpcmpgtb     %ymm10, %ymm0, %ymm1
	0xc5, 0xa5, 0x64, 0xd0, //0x0000079a vpcmpgtb     %ymm0, %ymm11, %ymm2
	0xc5, 0xf5, 0xdb, 0xca, //0x0000079e vpand        %ymm2, %ymm1, %ymm1
	0xc5, 0x9d, 0x74, 0xd0, //0x000007a2 vpcmpeqb     %ymm0, %ymm12, %ymm2
	0xc5, 0x95, 0x74, 0xd8, //0x000007a6 vpcmpeqb     %ymm0, %ymm13, %ymm3
	0xc5, 0xe5, 0xeb, 0xd2, //0x000007aa vpor         %ymm2, %ymm3, %ymm2
	0xc5, 0xbd, 0xeb, 0xd8, //0x000007ae vpor         %ymm0, %ymm8, %ymm3
	0xc5, 0x8d, 0x74, 0xc0, //0x000007b2 vpcmpeqb     %ymm0, %ymm14, %ymm0
	0xc5, 0xfd, 0xd7, 0xf8, //0x000007b6 vpmovmskb    %ymm0, %edi
	0xc5, 0x85, 0x74, 0xdb, //0x000007ba vpcmpeqb     %ymm3, %ymm15, %ymm3
	0xc5, 0xfd, 0xd7, 0xd3, //0x000007be vpmovmskb    %ymm3, %edx
	0xc5, 0xfd, 0xd7, 0xf2, //0x000007c2 vpmovmskb    %ymm2, %esi
	0xc5, 0xe5, 0xeb, 0xc0, //0x000007c6 vpor         %ymm0, %ymm3, %ymm0
	0xc5, 0xed, 0xeb, 0xc9, //0x000007ca vpor         %ymm1, %ymm2, %ymm1
	0xc5, 0xfd, 0xeb, 0xc1, //0x000007ce vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc8, //0x000007d2 vpmovmskb    %ymm0, %ecx
	0x48, 0xf7, 0xd1, //0x000007d6 notq         %rcx
	0x48, 0x0f, 0xbc, 0xc9, //0x000007d9 bsfq         %rcx, %rcx
	0x83, 0xf9, 0x20, //0x000007dd cmpl         $32, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x000007e0 je           LBB0_136
	0xbb, 0xff, 0xff, 0xff, 0xff, //0x000007e6 movl         $-1, %ebx
	0xd3, 0xe3, //0x000007eb shll         %cl, %ebx
	0xf7, 0xd3, //0x000007ed notl         %ebx
	0x21, 0xdf, //0x000007ef andl         %ebx, %edi
	0x21, 0xda, //0x000007f1 andl         %ebx, %edx
	0x21, 0xf3, //0x000007f3 andl         %esi, %ebx
	0x89, 0xde, //0x000007f5 movl         %ebx, %esi
	//0x000007f7 LBB0_136
	0x44, 0x8d, 0x4f, 0xff, //0x000007f7 leal         $-1(%rdi), %r9d
	0x41, 0x21, 0xf9, //0x000007fb andl         %edi, %r9d
	0x0f, 0x85, 0xab, 0x13, 0x00, 0x00, //0x000007fe jne          LBB0_345
	0x8d, 0x5a, 0xff, //0x00000804 leal         $-1(%rdx), %ebx
	0x21, 0xd3, //0x00000807 andl         %edx, %ebx
	0x0f, 0x85, 0x5e, 0x12, 0x00, 0x00, //0x00000809 jne          LBB0_339
	0x8d, 0x5e, 0xff, //0x0000080f leal         $-1(%rsi), %ebx
	0x21, 0xf3, //0x00000812 andl         %esi, %ebx
	0x0f, 0x85, 0x53, 0x12, 0x00, 0x00, //0x00000814 jne          LBB0_339
	0x85, 0xff, //0x0000081a testl        %edi, %edi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000081c je           LBB0_142
	0x4c, 0x89, 0xdb, //0x00000822 movq         %r11, %rbx
	0x4c, 0x29, 0xfb, //0x00000825 subq         %r15, %rbx
	0x0f, 0xbc, 0xff, //0x00000828 bsfl         %edi, %edi
	0x48, 0x01, 0xdf, //0x0000082b addq         %rbx, %rdi
	0x48, 0x83, 0xf8, 0xff, //0x0000082e cmpq         $-1, %rax
	0x0f, 0x85, 0x8d, 0x13, 0x00, 0x00, //0x00000832 jne          LBB0_347
	0x48, 0x89, 0xf8, //0x00000838 movq         %rdi, %rax
	//0x0000083b LBB0_142
	0x85, 0xd2, //0x0000083b testl        %edx, %edx
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000083d je           LBB0_145
	0x4c, 0x89, 0xdf, //0x00000843 movq         %r11, %rdi
	0x4c, 0x29, 0xff, //0x00000846 subq         %r15, %rdi
	0x0f, 0xbc, 0xd2, //0x00000849 bsfl         %edx, %edx
	0x48, 0x01, 0xfa, //0x0000084c addq         %rdi, %rdx
	0x49, 0x83, 0xfc, 0xff, //0x0000084f cmpq         $-1, %r12
	0x0f, 0x85, 0xf1, 0x12, 0x00, 0x00, //0x00000853 jne          LBB0_340
	0x49, 0x89, 0xd4, //0x00000859 movq         %rdx, %r12
	//0x0000085c LBB0_145
	0x85, 0xf6, //0x0000085c testl        %esi, %esi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000085e je           LBB0_148
	0x4c, 0x89, 0xdf, //0x00000864 movq         %r11, %rdi
	0x4c, 0x29, 0xff, //0x00000867 subq         %r15, %rdi
	0x0f, 0xbc, 0xd6, //0x0000086a bsfl         %esi, %edx
	0x48, 0x01, 0xfa, //0x0000086d addq         %rdi, %rdx
	0x49, 0x83, 0xf8, 0xff, //0x00000870 cmpq         $-1, %r8
	0x0f, 0x85, 0xd0, 0x12, 0x00, 0x00, //0x00000874 jne          LBB0_340
	0x49, 0x89, 0xd0, //0x0000087a movq         %rdx, %r8
	//0x0000087d LBB0_148
	0x83, 0xf9, 0x20, //0x0000087d cmpl         $32, %ecx
	0x0f, 0x85, 0x51, 0x05, 0x00, 0x00, //0x00000880 jne          LBB0_180
	0x49, 0x83, 0xc3, 0x20, //0x00000886 addq         $32, %r11
	0x49, 0x83, 0xc2, 0xe0, //0x0000088a addq         $-32, %r10
	0x49, 0x83, 0xfa, 0x1f, //0x0000088e cmpq         $31, %r10
	0x0f, 0x87, 0xf8, 0xfe, 0xff, 0xff, //0x00000892 ja           LBB0_134
	0xc5, 0xf8, 0x77, //0x00000898 vzeroupper   
	0xc5, 0x7e, 0x6f, 0x3d, 0xfd, 0xf8, 0xff, 0xff, //0x0000089b vmovdqu      $-1795(%rip), %ymm15  /* LCPI0_10+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0xd5, 0xf8, 0xff, 0xff, //0x000008a3 vmovdqu      $-1835(%rip), %ymm14  /* LCPI0_9+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0xad, 0xf8, 0xff, 0xff, //0x000008ab vmovdqu      $-1875(%rip), %ymm13  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x85, 0xf8, 0xff, 0xff, //0x000008b3 vmovdqu      $-1915(%rip), %ymm12  /* LCPI0_7+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x5d, 0xf8, 0xff, 0xff, //0x000008bb vmovdqu      $-1955(%rip), %ymm11  /* LCPI0_6+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0x35, 0xf8, 0xff, 0xff, //0x000008c3 vmovdqu      $-1995(%rip), %ymm10  /* LCPI0_5+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000008cb vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x05, 0x08, 0xf8, 0xff, 0xff, //0x000008d0 vmovdqu      $-2040(%rip), %ymm8  /* LCPI0_4+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0xe0, 0xf7, 0xff, 0xff, //0x000008d8 vmovdqu      $-2080(%rip), %ymm7  /* LCPI0_3+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0xb8, 0xf7, 0xff, 0xff, //0x000008e0 vmovdqu      $-2120(%rip), %ymm6  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x2d, 0x90, 0xf7, 0xff, 0xff, //0x000008e8 vmovdqu      $-2160(%rip), %ymm5  /* LCPI0_1+0(%rip) */
	0x4c, 0x8b, 0x55, 0xc0, //0x000008f0 movq         $-64(%rbp), %r10
	0x49, 0x83, 0xfa, 0x10, //0x000008f4 cmpq         $16, %r10
	0x0f, 0x82, 0x52, 0x01, 0x00, 0x00, //0x000008f8 jb           LBB0_169
	//0x000008fe LBB0_151
	0x4d, 0x8d, 0x4a, 0xf0, //0x000008fe leaq         $-16(%r10), %r9
	0x4c, 0x89, 0xc9, //0x00000902 movq         %r9, %rcx
	0x48, 0x83, 0xe1, 0xf0, //0x00000905 andq         $-16, %rcx
	0x4e, 0x8d, 0x5c, 0x31, 0x10, //0x00000909 leaq         $16(%rcx,%r14), %r11
	0x41, 0x83, 0xe1, 0x0f, //0x0000090e andl         $15, %r9d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000912 .p2align 4, 0x90
	//0x00000920 LBB0_152
	0xc4, 0xc1, 0x7a, 0x6f, 0x06, //0x00000920 vmovdqu      (%r14), %xmm0
	0xc5, 0xf9, 0x64, 0x0d, 0xe3, 0xf6, 0xff, 0xff, //0x00000925 vpcmpgtb     $-2333(%rip), %xmm0, %xmm1  /* LCPI0_11+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x15, 0xeb, 0xf6, 0xff, 0xff, //0x0000092d vmovdqu      $-2325(%rip), %xmm2  /* LCPI0_12+0(%rip) */
	0xc5, 0xe9, 0x64, 0xd0, //0x00000935 vpcmpgtb     %xmm0, %xmm2, %xmm2
	0xc5, 0xf1, 0xdb, 0xca, //0x00000939 vpand        %xmm2, %xmm1, %xmm1
	0xc5, 0xf9, 0x74, 0x15, 0xeb, 0xf6, 0xff, 0xff, //0x0000093d vpcmpeqb     $-2325(%rip), %xmm0, %xmm2  /* LCPI0_13+0(%rip) */
	0xc5, 0xf9, 0x74, 0x1d, 0xf3, 0xf6, 0xff, 0xff, //0x00000945 vpcmpeqb     $-2317(%rip), %xmm0, %xmm3  /* LCPI0_14+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xd2, //0x0000094d vpor         %xmm2, %xmm3, %xmm2
	0xc5, 0xf9, 0xeb, 0x1d, 0xf7, 0xf6, 0xff, 0xff, //0x00000951 vpor         $-2313(%rip), %xmm0, %xmm3  /* LCPI0_15+0(%rip) */
	0xc5, 0xf9, 0x74, 0x05, 0xff, 0xf6, 0xff, 0xff, //0x00000959 vpcmpeqb     $-2305(%rip), %xmm0, %xmm0  /* LCPI0_16+0(%rip) */
	0xc5, 0xe1, 0x74, 0x1d, 0x07, 0xf7, 0xff, 0xff, //0x00000961 vpcmpeqb     $-2297(%rip), %xmm3, %xmm3  /* LCPI0_17+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xe0, //0x00000969 vpor         %xmm0, %xmm3, %xmm4
	0xc5, 0xe9, 0xeb, 0xc9, //0x0000096d vpor         %xmm1, %xmm2, %xmm1
	0xc5, 0xd9, 0xeb, 0xc9, //0x00000971 vpor         %xmm1, %xmm4, %xmm1
	0xc5, 0xf9, 0xd7, 0xf8, //0x00000975 vpmovmskb    %xmm0, %edi
	0xc5, 0xf9, 0xd7, 0xf3, //0x00000979 vpmovmskb    %xmm3, %esi
	0xc5, 0xf9, 0xd7, 0xd2, //0x0000097d vpmovmskb    %xmm2, %edx
	0xc5, 0xf9, 0xd7, 0xc9, //0x00000981 vpmovmskb    %xmm1, %ecx
	0xbb, 0xff, 0xff, 0xff, 0xff, //0x00000985 movl         $4294967295, %ebx
	0x48, 0x31, 0xd9, //0x0000098a xorq         %rbx, %rcx
	0x48, 0x0f, 0xbc, 0xc9, //0x0000098d bsfq         %rcx, %rcx
	0x83, 0xf9, 0x10, //0x00000991 cmpl         $16, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x00000994 je           LBB0_154
	0xbb, 0xff, 0xff, 0xff, 0xff, //0x0000099a movl         $-1, %ebx
	0xd3, 0xe3, //0x0000099f shll         %cl, %ebx
	0xf7, 0xd3, //0x000009a1 notl         %ebx
	0x21, 0xdf, //0x000009a3 andl         %ebx, %edi
	0x21, 0xde, //0x000009a5 andl         %ebx, %esi
	0x21, 0xd3, //0x000009a7 andl         %edx, %ebx
	0x89, 0xda, //0x000009a9 movl         %ebx, %edx
	//0x000009ab LBB0_154
	0x8d, 0x5f, 0xff, //0x000009ab leal         $-1(%rdi), %ebx
	0x21, 0xfb, //0x000009ae andl         %edi, %ebx
	0x0f, 0x85, 0xe1, 0x11, 0x00, 0x00, //0x000009b0 jne          LBB0_344
	0x8d, 0x5e, 0xff, //0x000009b6 leal         $-1(%rsi), %ebx
	0x21, 0xf3, //0x000009b9 andl         %esi, %ebx
	0x0f, 0x85, 0xd6, 0x11, 0x00, 0x00, //0x000009bb jne          LBB0_344
	0x8d, 0x5a, 0xff, //0x000009c1 leal         $-1(%rdx), %ebx
	0x21, 0xd3, //0x000009c4 andl         %edx, %ebx
	0x0f, 0x85, 0xcb, 0x11, 0x00, 0x00, //0x000009c6 jne          LBB0_344
	0x85, 0xff, //0x000009cc testl        %edi, %edi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x000009ce je           LBB0_160
	0x4c, 0x89, 0xf3, //0x000009d4 movq         %r14, %rbx
	0x4c, 0x29, 0xfb, //0x000009d7 subq         %r15, %rbx
	0x0f, 0xbc, 0xff, //0x000009da bsfl         %edi, %edi
	0x48, 0x01, 0xdf, //0x000009dd addq         %rbx, %rdi
	0x48, 0x83, 0xf8, 0xff, //0x000009e0 cmpq         $-1, %rax
	0x0f, 0x85, 0xdb, 0x11, 0x00, 0x00, //0x000009e4 jne          LBB0_347
	0x48, 0x89, 0xf8, //0x000009ea movq         %rdi, %rax
	//0x000009ed LBB0_160
	0x85, 0xf6, //0x000009ed testl        %esi, %esi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x000009ef je           LBB0_163
	0x4c, 0x89, 0xf7, //0x000009f5 movq         %r14, %rdi
	0x4c, 0x29, 0xff, //0x000009f8 subq         %r15, %rdi
	0x0f, 0xbc, 0xf6, //0x000009fb bsfl         %esi, %esi
	0x48, 0x01, 0xfe, //0x000009fe addq         %rdi, %rsi
	0x49, 0x83, 0xfc, 0xff, //0x00000a01 cmpq         $-1, %r12
	0x0f, 0x85, 0xed, 0x12, 0x00, 0x00, //0x00000a05 jne          LBB0_350
	0x49, 0x89, 0xf4, //0x00000a0b movq         %rsi, %r12
	//0x00000a0e LBB0_163
	0x85, 0xd2, //0x00000a0e testl        %edx, %edx
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x00000a10 je           LBB0_166
	0x4c, 0x89, 0xf6, //0x00000a16 movq         %r14, %rsi
	0x4c, 0x29, 0xfe, //0x00000a19 subq         %r15, %rsi
	0x0f, 0xbc, 0xd2, //0x00000a1c bsfl         %edx, %edx
	0x48, 0x01, 0xf2, //0x00000a1f addq         %rsi, %rdx
	0x49, 0x83, 0xf8, 0xff, //0x00000a22 cmpq         $-1, %r8
	0x0f, 0x85, 0x1e, 0x11, 0x00, 0x00, //0x00000a26 jne          LBB0_340
	0x49, 0x89, 0xd0, //0x00000a2c movq         %rdx, %r8
	//0x00000a2f LBB0_166
	0x83, 0xf9, 0x10, //0x00000a2f cmpl         $16, %ecx
	0x0f, 0x85, 0x82, 0x05, 0x00, 0x00, //0x00000a32 jne          LBB0_186
	0x49, 0x83, 0xc6, 0x10, //0x00000a38 addq         $16, %r14
	0x49, 0x83, 0xc2, 0xf0, //0x00000a3c addq         $-16, %r10
	0x49, 0x83, 0xfa, 0x0f, //0x00000a40 cmpq         $15, %r10
	0x0f, 0x87, 0xd6, 0xfe, 0xff, 0xff, //0x00000a44 ja           LBB0_152
	0x4d, 0x89, 0xca, //0x00000a4a movq         %r9, %r10
	0x4d, 0x89, 0xde, //0x00000a4d movq         %r11, %r14
	//0x00000a50 LBB0_169
	0x4d, 0x85, 0xd2, //0x00000a50 testq        %r10, %r10
	0x0f, 0x84, 0x64, 0x05, 0x00, 0x00, //0x00000a53 je           LBB0_187
	0x4b, 0x8d, 0x0c, 0x16, //0x00000a59 leaq         (%r14,%r10), %rcx
	0xe9, 0x2a, 0x00, 0x00, 0x00, //0x00000a5d jmp          LBB0_174
	//0x00000a62 LBB0_171
	0x49, 0x89, 0xd6, //0x00000a62 movq         %rdx, %r14
	0x4d, 0x29, 0xfe, //0x00000a65 subq         %r15, %r14
	0x49, 0x83, 0xf8, 0xff, //0x00000a68 cmpq         $-1, %r8
	0x0f, 0x85, 0x15, 0x13, 0x00, 0x00, //0x00000a6c jne          LBB0_352
	0x49, 0xff, 0xce, //0x00000a72 decq         %r14
	0x4d, 0x89, 0xf0, //0x00000a75 movq         %r14, %r8
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000a78 .p2align 4, 0x90
	//0x00000a80 LBB0_173
	0x49, 0x89, 0xd6, //0x00000a80 movq         %rdx, %r14
	0x49, 0xff, 0xca, //0x00000a83 decq         %r10
	0x0f, 0x84, 0x3a, 0x12, 0x00, 0x00, //0x00000a86 je           LBB0_348
	//0x00000a8c LBB0_174
	0x41, 0x0f, 0xbe, 0x36, //0x00000a8c movsbl       (%r14), %esi
	0x83, 0xc6, 0xd5, //0x00000a90 addl         $-43, %esi
	0x83, 0xfe, 0x3a, //0x00000a93 cmpl         $58, %esi
	0x0f, 0x87, 0x21, 0x05, 0x00, 0x00, //0x00000a96 ja           LBB0_187
	0x49, 0x8d, 0x56, 0x01, //0x00000a9c leaq         $1(%r14), %rdx
	0x48, 0x8d, 0x3d, 0x65, 0x22, 0x00, 0x00, //0x00000aa0 leaq         $8805(%rip), %rdi  /* LJTI0_3+0(%rip) */
	0x48, 0x63, 0x34, 0xb7, //0x00000aa7 movslq       (%rdi,%rsi,4), %rsi
	0x48, 0x01, 0xfe, //0x00000aab addq         %rdi, %rsi
	0xff, 0xe6, //0x00000aae jmpq         *%rsi
	//0x00000ab0 LBB0_176
	0x49, 0x89, 0xd6, //0x00000ab0 movq         %rdx, %r14
	0x4d, 0x29, 0xfe, //0x00000ab3 subq         %r15, %r14
	0x49, 0x83, 0xfc, 0xff, //0x00000ab6 cmpq         $-1, %r12
	0x0f, 0x85, 0xc7, 0x12, 0x00, 0x00, //0x00000aba jne          LBB0_352
	0x49, 0xff, 0xce, //0x00000ac0 decq         %r14
	0x4d, 0x89, 0xf4, //0x00000ac3 movq         %r14, %r12
	0xe9, 0xb5, 0xff, 0xff, 0xff, //0x00000ac6 jmp          LBB0_173
	//0x00000acb LBB0_178
	0x49, 0x89, 0xd6, //0x00000acb movq         %rdx, %r14
	0x4d, 0x29, 0xfe, //0x00000ace subq         %r15, %r14
	0x48, 0x83, 0xf8, 0xff, //0x00000ad1 cmpq         $-1, %rax
	0x0f, 0x85, 0xac, 0x12, 0x00, 0x00, //0x00000ad5 jne          LBB0_352
	0x49, 0xff, 0xce, //0x00000adb decq         %r14
	0x4c, 0x89, 0xf0, //0x00000ade movq         %r14, %rax
	0xe9, 0x9a, 0xff, 0xff, 0xff, //0x00000ae1 jmp          LBB0_173
	//0x00000ae6 LBB0_73
	0x49, 0x81, 0xf8, 0xff, 0x0f, 0x00, 0x00, //0x00000ae6 cmpq         $4095, %r8
	0x0f, 0x8f, 0x8f, 0x1c, 0x00, 0x00, //0x00000aed jg           LBB0_489
	0x49, 0x8d, 0x40, 0x01, //0x00000af3 leaq         $1(%r8), %rax
	0x49, 0x89, 0x06, //0x00000af7 movq         %rax, (%r14)
	0x4b, 0xc7, 0x44, 0xc6, 0x08, 0x00, 0x00, 0x00, 0x00, //0x00000afa movq         $0, $8(%r14,%r8,8)
	0xe9, 0x78, 0xf7, 0xff, 0xff, //0x00000b03 jmp          LBB0_3
	//0x00000b08 LBB0_75
	0x49, 0x89, 0xd2, //0x00000b08 movq         %rdx, %r10
	0x4d, 0x29, 0xe2, //0x00000b0b subq         %r12, %r10
	0x0f, 0x84, 0x74, 0x1e, 0x00, 0x00, //0x00000b0e je           LBB0_508
	0x4c, 0x89, 0xe0, //0x00000b14 movq         %r12, %rax
	0x4d, 0x01, 0xcc, //0x00000b17 addq         %r9, %r12
	0x49, 0x83, 0xfa, 0x40, //0x00000b1a cmpq         $64, %r10
	0x4c, 0x89, 0x4d, 0xc0, //0x00000b1e movq         %r9, $-64(%rbp)
	0x0f, 0x82, 0x37, 0x13, 0x00, 0x00, //0x00000b22 jb           LBB0_357
	0x45, 0x89, 0xd6, //0x00000b28 movl         %r10d, %r14d
	0x41, 0x83, 0xe6, 0x3f, //0x00000b2b andl         $63, %r14d
	0x48, 0x8d, 0x4c, 0x32, 0xc0, //0x00000b2f leaq         $-64(%rdx,%rsi), %rcx
	0x48, 0x83, 0xe1, 0xc0, //0x00000b34 andq         $-64, %rcx
	0x48, 0x01, 0xc1, //0x00000b38 addq         %rax, %rcx
	0x49, 0x8d, 0x44, 0x09, 0x40, //0x00000b3b leaq         $64(%r9,%rcx), %rax
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000b40 movq         $-1, %r8
	0x45, 0x31, 0xff, //0x00000b47 xorl         %r15d, %r15d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000b4a .p2align 4, 0x90
	//0x00000b50 LBB0_78
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x00000b50 vmovdqu      (%r12), %ymm0
	0xc4, 0xc1, 0x7e, 0x6f, 0x4c, 0x24, 0x20, //0x00000b56 vmovdqu      $32(%r12), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x00000b5d vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0x7d, 0xd7, 0xca, //0x00000b61 vpmovmskb    %ymm2, %r9d
	0xc5, 0xf5, 0x74, 0xd6, //0x00000b65 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00000b69 vpmovmskb    %ymm2, %edx
	0xc5, 0xfd, 0x74, 0xd7, //0x00000b6d vpcmpeqb     %ymm7, %ymm0, %ymm2
	0xc5, 0x7d, 0xd7, 0xda, //0x00000b71 vpmovmskb    %ymm2, %r11d
	0xc5, 0xf5, 0x74, 0xd7, //0x00000b75 vpcmpeqb     %ymm7, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xda, //0x00000b79 vpmovmskb    %ymm2, %ebx
	0xc5, 0xbd, 0x64, 0xd0, //0x00000b7d vpcmpgtb     %ymm0, %ymm8, %ymm2
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x00000b81 vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xfd, 0xdb, 0xc2, //0x00000b86 vpand        %ymm2, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf8, //0x00000b8a vpmovmskb    %ymm0, %edi
	0xc5, 0xbd, 0x64, 0xc1, //0x00000b8e vpcmpgtb     %ymm1, %ymm8, %ymm0
	0xc4, 0xc1, 0x75, 0x64, 0xc9, //0x00000b92 vpcmpgtb     %ymm9, %ymm1, %ymm1
	0xc5, 0xf5, 0xdb, 0xc0, //0x00000b97 vpand        %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00000b9b vpmovmskb    %ymm0, %esi
	0x48, 0xc1, 0xe2, 0x20, //0x00000b9f shlq         $32, %rdx
	0x48, 0xc1, 0xe3, 0x20, //0x00000ba3 shlq         $32, %rbx
	0x49, 0x09, 0xdb, //0x00000ba7 orq          %rbx, %r11
	0x49, 0x83, 0xf8, 0xff, //0x00000baa cmpq         $-1, %r8
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00000bae jne          LBB0_80
	0x4d, 0x85, 0xdb, //0x00000bb4 testq        %r11, %r11
	0x0f, 0x85, 0x9f, 0x00, 0x00, 0x00, //0x00000bb7 jne          LBB0_89
	//0x00000bbd LBB0_80
	0x48, 0xc1, 0xe6, 0x20, //0x00000bbd shlq         $32, %rsi
	0x4c, 0x09, 0xca, //0x00000bc1 orq          %r9, %rdx
	0x4c, 0x89, 0xd9, //0x00000bc4 movq         %r11, %rcx
	0x4c, 0x09, 0xf9, //0x00000bc7 orq          %r15, %rcx
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x00000bca jne          LBB0_108
	0x48, 0x09, 0xfe, //0x00000bd0 orq          %rdi, %rsi
	0x48, 0x85, 0xd2, //0x00000bd3 testq        %rdx, %rdx
	0x0f, 0x85, 0xe1, 0x01, 0x00, 0x00, //0x00000bd6 jne          LBB0_109
	//0x00000bdc LBB0_82
	0x48, 0x85, 0xf6, //0x00000bdc testq        %rsi, %rsi
	0x0f, 0x85, 0x21, 0x1c, 0x00, 0x00, //0x00000bdf jne          LBB0_482
	0x49, 0x83, 0xc2, 0xc0, //0x00000be5 addq         $-64, %r10
	0x49, 0x83, 0xc4, 0x40, //0x00000be9 addq         $64, %r12
	0x49, 0x83, 0xfa, 0x3f, //0x00000bed cmpq         $63, %r10
	0x0f, 0x87, 0x59, 0xff, 0xff, 0xff, //0x00000bf1 ja           LBB0_78
	0xe9, 0xed, 0x0e, 0x00, 0x00, //0x00000bf7 jmp          LBB0_84
	//0x00000bfc LBB0_108
	0x4c, 0x89, 0xf9, //0x00000bfc movq         %r15, %rcx
	0x48, 0xf7, 0xd1, //0x00000bff notq         %rcx
	0x4c, 0x21, 0xd9, //0x00000c02 andq         %r11, %rcx
	0x4c, 0x8d, 0x0c, 0x09, //0x00000c05 leaq         (%rcx,%rcx), %r9
	0x4d, 0x09, 0xf9, //0x00000c09 orq          %r15, %r9
	0x4c, 0x89, 0xcb, //0x00000c0c movq         %r9, %rbx
	0x48, 0xf7, 0xd3, //0x00000c0f notq         %rbx
	0x4c, 0x21, 0xdb, //0x00000c12 andq         %r11, %rbx
	0x49, 0x89, 0xc3, //0x00000c15 movq         %rax, %r11
	0x48, 0xb8, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000c18 movabsq      $-6148914691236517206, %rax
	0x48, 0x21, 0xc3, //0x00000c22 andq         %rax, %rbx
	0x45, 0x31, 0xff, //0x00000c25 xorl         %r15d, %r15d
	0x48, 0x01, 0xcb, //0x00000c28 addq         %rcx, %rbx
	0x41, 0x0f, 0x92, 0xc7, //0x00000c2b setb         %r15b
	0x48, 0x01, 0xdb, //0x00000c2f addq         %rbx, %rbx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000c32 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc3, //0x00000c3c xorq         %rax, %rbx
	0x4c, 0x89, 0xd8, //0x00000c3f movq         %r11, %rax
	0x4c, 0x21, 0xcb, //0x00000c42 andq         %r9, %rbx
	0x48, 0xf7, 0xd3, //0x00000c45 notq         %rbx
	0x48, 0x21, 0xda, //0x00000c48 andq         %rbx, %rdx
	0x48, 0x09, 0xfe, //0x00000c4b orq          %rdi, %rsi
	0x48, 0x85, 0xd2, //0x00000c4e testq        %rdx, %rdx
	0x0f, 0x84, 0x85, 0xff, 0xff, 0xff, //0x00000c51 je           LBB0_82
	0xe9, 0x61, 0x01, 0x00, 0x00, //0x00000c57 jmp          LBB0_109
	//0x00000c5c LBB0_89
	0x4c, 0x89, 0xe3, //0x00000c5c movq         %r12, %rbx
	0x48, 0x2b, 0x5d, 0xc0, //0x00000c5f subq         $-64(%rbp), %rbx
	0x4d, 0x0f, 0xbc, 0xc3, //0x00000c63 bsfq         %r11, %r8
	0x49, 0x01, 0xd8, //0x00000c67 addq         %rbx, %r8
	0xe9, 0x4e, 0xff, 0xff, 0xff, //0x00000c6a jmp          LBB0_80
	//0x00000c6f LBB0_92
	0x4b, 0xc7, 0x04, 0xc6, 0x02, 0x00, 0x00, 0x00, //0x00000c6f movq         $2, (%r14,%r8,8)
	0x48, 0x8b, 0x45, 0xb0, //0x00000c77 movq         $-80(%rbp), %rax
	0x48, 0x8b, 0x40, 0x08, //0x00000c7b movq         $8(%rax), %rax
	0xf6, 0x45, 0x98, 0x20, //0x00000c7f testb        $32, $-104(%rbp)
	0x48, 0x89, 0x45, 0xb8, //0x00000c83 movq         %rax, $-72(%rbp)
	0x0f, 0x85, 0x6c, 0x01, 0x00, 0x00, //0x00000c87 jne          LBB0_111
	0x48, 0x89, 0xc1, //0x00000c8d movq         %rax, %rcx
	0x4c, 0x29, 0xe1, //0x00000c90 subq         %r12, %rcx
	0x0f, 0x84, 0xdd, 0x1c, 0x00, 0x00, //0x00000c93 je           LBB0_510
	0x4b, 0x8d, 0x1c, 0x21, //0x00000c99 leaq         (%r9,%r12), %rbx
	0x48, 0x83, 0xf9, 0x40, //0x00000c9d cmpq         $64, %rcx
	0x0f, 0x82, 0xf1, 0x11, 0x00, 0x00, //0x00000ca1 jb           LBB0_360
	0x4c, 0x89, 0xca, //0x00000ca7 movq         %r9, %rdx
	0x41, 0x89, 0xcf, //0x00000caa movl         %ecx, %r15d
	0x41, 0x83, 0xe7, 0x3f, //0x00000cad andl         $63, %r15d
	0x48, 0x8d, 0x44, 0x30, 0xc0, //0x00000cb1 leaq         $-64(%rax,%rsi), %rax
	0x48, 0x83, 0xe0, 0xc0, //0x00000cb6 andq         $-64, %rax
	0x4c, 0x01, 0xe0, //0x00000cba addq         %r12, %rax
	0x4d, 0x8d, 0x44, 0x01, 0x40, //0x00000cbd leaq         $64(%r9,%rax), %r8
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00000cc2 movq         $-1, %r11
	0x45, 0x31, 0xf6, //0x00000cc9 xorl         %r14d, %r14d
	0x90, 0x90, 0x90, 0x90, //0x00000ccc .p2align 4, 0x90
	//0x00000cd0 LBB0_96
	0xc5, 0xfe, 0x6f, 0x03, //0x00000cd0 vmovdqu      (%rbx), %ymm0
	0xc5, 0xfe, 0x6f, 0x4b, 0x20, //0x00000cd4 vmovdqu      $32(%rbx), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x00000cd9 vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00000cdd vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x00000ce1 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00000ce5 vpmovmskb    %ymm2, %edx
	0xc5, 0xfd, 0x74, 0xc7, //0x00000ce9 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00000ced vpmovmskb    %ymm0, %esi
	0xc5, 0xf5, 0x74, 0xc7, //0x00000cf1 vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x00000cf5 vpmovmskb    %ymm0, %eax
	0x48, 0xc1, 0xe2, 0x20, //0x00000cf9 shlq         $32, %rdx
	0x48, 0xc1, 0xe0, 0x20, //0x00000cfd shlq         $32, %rax
	0x48, 0x09, 0xc6, //0x00000d01 orq          %rax, %rsi
	0x49, 0x83, 0xfb, 0xff, //0x00000d04 cmpq         $-1, %r11
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00000d08 jne          LBB0_98
	0x48, 0x85, 0xf6, //0x00000d0e testq        %rsi, %rsi
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x00000d11 jne          LBB0_105
	//0x00000d17 LBB0_98
	0x48, 0x09, 0xfa, //0x00000d17 orq          %rdi, %rdx
	0x48, 0x89, 0xf0, //0x00000d1a movq         %rsi, %rax
	0x4c, 0x09, 0xf0, //0x00000d1d orq          %r14, %rax
	0x0f, 0x85, 0x3c, 0x00, 0x00, 0x00, //0x00000d20 jne          LBB0_106
	//0x00000d26 LBB0_99
	0x48, 0x85, 0xd2, //0x00000d26 testq        %rdx, %rdx
	0x0f, 0x85, 0xe6, 0x0e, 0x00, 0x00, //0x00000d29 jne          LBB0_107
	//0x00000d2f LBB0_100
	0x48, 0x83, 0xc1, 0xc0, //0x00000d2f addq         $-64, %rcx
	0x48, 0x83, 0xc3, 0x40, //0x00000d33 addq         $64, %rbx
	0x48, 0x83, 0xf9, 0x3f, //0x00000d37 cmpq         $63, %rcx
	0x0f, 0x87, 0x8f, 0xff, 0xff, 0xff, //0x00000d3b ja           LBB0_96
	0xe9, 0x8e, 0x0e, 0x00, 0x00, //0x00000d41 jmp          LBB0_101
	//0x00000d46 LBB0_105
	0x48, 0x89, 0xd8, //0x00000d46 movq         %rbx, %rax
	0x4c, 0x29, 0xc8, //0x00000d49 subq         %r9, %rax
	0x4c, 0x0f, 0xbc, 0xde, //0x00000d4c bsfq         %rsi, %r11
	0x49, 0x01, 0xc3, //0x00000d50 addq         %rax, %r11
	0x48, 0x09, 0xfa, //0x00000d53 orq          %rdi, %rdx
	0x48, 0x89, 0xf0, //0x00000d56 movq         %rsi, %rax
	0x4c, 0x09, 0xf0, //0x00000d59 orq          %r14, %rax
	0x0f, 0x84, 0xc4, 0xff, 0xff, 0xff, //0x00000d5c je           LBB0_99
	//0x00000d62 LBB0_106
	0x4c, 0x89, 0xf0, //0x00000d62 movq         %r14, %rax
	0x48, 0xf7, 0xd0, //0x00000d65 notq         %rax
	0x48, 0x21, 0xf0, //0x00000d68 andq         %rsi, %rax
	0x4c, 0x8d, 0x14, 0x00, //0x00000d6b leaq         (%rax,%rax), %r10
	0x4d, 0x09, 0xf2, //0x00000d6f orq          %r14, %r10
	0x4c, 0x89, 0xd7, //0x00000d72 movq         %r10, %rdi
	0x48, 0xf7, 0xd7, //0x00000d75 notq         %rdi
	0x48, 0x21, 0xf7, //0x00000d78 andq         %rsi, %rdi
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000d7b movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf7, //0x00000d85 andq         %rsi, %rdi
	0x45, 0x31, 0xf6, //0x00000d88 xorl         %r14d, %r14d
	0x48, 0x01, 0xc7, //0x00000d8b addq         %rax, %rdi
	0x41, 0x0f, 0x92, 0xc6, //0x00000d8e setb         %r14b
	0x48, 0x01, 0xff, //0x00000d92 addq         %rdi, %rdi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000d95 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc7, //0x00000d9f xorq         %rax, %rdi
	0x4c, 0x21, 0xd7, //0x00000da2 andq         %r10, %rdi
	0x4c, 0x8b, 0x55, 0xd0, //0x00000da5 movq         $-48(%rbp), %r10
	0x48, 0xf7, 0xd7, //0x00000da9 notq         %rdi
	0x48, 0x21, 0xfa, //0x00000dac andq         %rdi, %rdx
	0x48, 0x85, 0xd2, //0x00000daf testq        %rdx, %rdx
	0x0f, 0x84, 0x77, 0xff, 0xff, 0xff, //0x00000db2 je           LBB0_100
	0xe9, 0x58, 0x0e, 0x00, 0x00, //0x00000db8 jmp          LBB0_107
	//0x00000dbd LBB0_109
	0x48, 0x0f, 0xbc, 0xca, //0x00000dbd bsfq         %rdx, %rcx
	0x48, 0x85, 0xf6, //0x00000dc1 testq        %rsi, %rsi
	0x48, 0x8b, 0x45, 0xc0, //0x00000dc4 movq         $-64(%rbp), %rax
	0x0f, 0x84, 0xc9, 0x01, 0x00, 0x00, //0x00000dc8 je           LBB0_181
	0x48, 0x0f, 0xbc, 0xd6, //0x00000dce bsfq         %rsi, %rdx
	0xe9, 0xc5, 0x01, 0x00, 0x00, //0x00000dd2 jmp          LBB0_182
	//0x00000dd7 LBB0_180
	0x49, 0x01, 0xcb, //0x00000dd7 addq         %rcx, %r11
	0xc5, 0xf8, 0x77, //0x00000dda vzeroupper   
	0x4d, 0x89, 0xde, //0x00000ddd movq         %r11, %r14
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000de0 movq         $-1, %rcx
	0x4d, 0x85, 0xe4, //0x00000de7 testq        %r12, %r12
	0x48, 0x8b, 0x55, 0xd0, //0x00000dea movq         $-48(%rbp), %rdx
	0x0f, 0x85, 0xdd, 0x01, 0x00, 0x00, //0x00000dee jne          LBB0_188
	0xe9, 0xdc, 0x19, 0x00, 0x00, //0x00000df4 jmp          LBB0_481
	//0x00000df9 LBB0_111
	0x48, 0x89, 0xc3, //0x00000df9 movq         %rax, %rbx
	0x4c, 0x29, 0xe3, //0x00000dfc subq         %r12, %rbx
	0x0f, 0x84, 0x71, 0x1b, 0x00, 0x00, //0x00000dff je           LBB0_510
	0x4c, 0x89, 0xe1, //0x00000e05 movq         %r12, %rcx
	0x4d, 0x01, 0xcc, //0x00000e08 addq         %r9, %r12
	0x48, 0x83, 0xfb, 0x40, //0x00000e0b cmpq         $64, %rbx
	0x4c, 0x89, 0x4d, 0xc0, //0x00000e0f movq         %r9, $-64(%rbp)
	0x0f, 0x82, 0x98, 0x10, 0x00, 0x00, //0x00000e13 jb           LBB0_361
	0x41, 0x89, 0xde, //0x00000e19 movl         %ebx, %r14d
	0x41, 0x83, 0xe6, 0x3f, //0x00000e1c andl         $63, %r14d
	0x48, 0x8d, 0x44, 0x30, 0xc0, //0x00000e20 leaq         $-64(%rax,%rsi), %rax
	0x48, 0x83, 0xe0, 0xc0, //0x00000e25 andq         $-64, %rax
	0x49, 0x89, 0xc8, //0x00000e29 movq         %rcx, %r8
	0x48, 0x01, 0xc8, //0x00000e2c addq         %rcx, %rax
	0x49, 0x8d, 0x44, 0x01, 0x40, //0x00000e2f leaq         $64(%r9,%rax), %rax
	0x48, 0x89, 0x45, 0xa0, //0x00000e34 movq         %rax, $-96(%rbp)
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00000e38 movq         $-1, %r11
	0x45, 0x31, 0xff, //0x00000e3f xorl         %r15d, %r15d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000e42 .p2align 4, 0x90
	//0x00000e50 LBB0_114
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x00000e50 vmovdqu      (%r12), %ymm0
	0xc4, 0xc1, 0x7e, 0x6f, 0x4c, 0x24, 0x20, //0x00000e56 vmovdqu      $32(%r12), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x00000e5d vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0x7d, 0xd7, 0xca, //0x00000e61 vpmovmskb    %ymm2, %r9d
	0xc5, 0xf5, 0x74, 0xd6, //0x00000e65 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00000e69 vpmovmskb    %ymm2, %edx
	0xc5, 0xfd, 0x74, 0xd7, //0x00000e6d vpcmpeqb     %ymm7, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x00000e71 vpmovmskb    %ymm2, %ecx
	0xc5, 0xf5, 0x74, 0xd7, //0x00000e75 vpcmpeqb     %ymm7, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x00000e79 vpmovmskb    %ymm2, %eax
	0xc5, 0xbd, 0x64, 0xd0, //0x00000e7d vpcmpgtb     %ymm0, %ymm8, %ymm2
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x00000e81 vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xfd, 0xdb, 0xc2, //0x00000e86 vpand        %ymm2, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf8, //0x00000e8a vpmovmskb    %ymm0, %edi
	0xc5, 0xbd, 0x64, 0xc1, //0x00000e8e vpcmpgtb     %ymm1, %ymm8, %ymm0
	0xc4, 0xc1, 0x75, 0x64, 0xc9, //0x00000e92 vpcmpgtb     %ymm9, %ymm1, %ymm1
	0xc5, 0xf5, 0xdb, 0xc0, //0x00000e97 vpand        %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00000e9b vpmovmskb    %ymm0, %esi
	0x48, 0xc1, 0xe2, 0x20, //0x00000e9f shlq         $32, %rdx
	0x48, 0xc1, 0xe0, 0x20, //0x00000ea3 shlq         $32, %rax
	0x48, 0x09, 0xc1, //0x00000ea7 orq          %rax, %rcx
	0x49, 0x83, 0xfb, 0xff, //0x00000eaa cmpq         $-1, %r11
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00000eae jne          LBB0_116
	0x48, 0x85, 0xc9, //0x00000eb4 testq        %rcx, %rcx
	0x0f, 0x85, 0x9d, 0x00, 0x00, 0x00, //0x00000eb7 jne          LBB0_125
	//0x00000ebd LBB0_116
	0x48, 0xc1, 0xe6, 0x20, //0x00000ebd shlq         $32, %rsi
	0x4c, 0x09, 0xca, //0x00000ec1 orq          %r9, %rdx
	0x48, 0x89, 0xc8, //0x00000ec4 movq         %rcx, %rax
	0x4c, 0x09, 0xf8, //0x00000ec7 orq          %r15, %rax
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x00000eca jne          LBB0_126
	0x48, 0x09, 0xfe, //0x00000ed0 orq          %rdi, %rsi
	0x48, 0x85, 0xd2, //0x00000ed3 testq        %rdx, %rdx
	0x0f, 0x85, 0x91, 0x00, 0x00, 0x00, //0x00000ed6 jne          LBB0_127
	//0x00000edc LBB0_118
	0x48, 0x85, 0xf6, //0x00000edc testq        %rsi, %rsi
	0x0f, 0x85, 0x4f, 0x19, 0x00, 0x00, //0x00000edf jne          LBB0_502
	0x48, 0x83, 0xc3, 0xc0, //0x00000ee5 addq         $-64, %rbx
	0x49, 0x83, 0xc4, 0x40, //0x00000ee9 addq         $64, %r12
	0x48, 0x83, 0xfb, 0x3f, //0x00000eed cmpq         $63, %rbx
	0x0f, 0x87, 0x59, 0xff, 0xff, 0xff, //0x00000ef1 ja           LBB0_114
	0xe9, 0x68, 0x0d, 0x00, 0x00, //0x00000ef7 jmp          LBB0_120
	//0x00000efc LBB0_126
	0x4d, 0x89, 0xfa, //0x00000efc movq         %r15, %r10
	0x49, 0xf7, 0xd2, //0x00000eff notq         %r10
	0x49, 0x21, 0xca, //0x00000f02 andq         %rcx, %r10
	0x4f, 0x8d, 0x0c, 0x12, //0x00000f05 leaq         (%r10,%r10), %r9
	0x4d, 0x09, 0xf9, //0x00000f09 orq          %r15, %r9
	0x4c, 0x89, 0xc8, //0x00000f0c movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x00000f0f notq         %rax
	0x48, 0x21, 0xc8, //0x00000f12 andq         %rcx, %rax
	0x48, 0xb9, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000f15 movabsq      $-6148914691236517206, %rcx
	0x48, 0x21, 0xc8, //0x00000f1f andq         %rcx, %rax
	0x45, 0x31, 0xff, //0x00000f22 xorl         %r15d, %r15d
	0x4c, 0x01, 0xd0, //0x00000f25 addq         %r10, %rax
	0x4c, 0x8b, 0x55, 0xd0, //0x00000f28 movq         $-48(%rbp), %r10
	0x41, 0x0f, 0x92, 0xc7, //0x00000f2c setb         %r15b
	0x48, 0x01, 0xc0, //0x00000f30 addq         %rax, %rax
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000f33 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xc8, //0x00000f3d xorq         %rcx, %rax
	0x4c, 0x21, 0xc8, //0x00000f40 andq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x00000f43 notq         %rax
	0x48, 0x21, 0xc2, //0x00000f46 andq         %rax, %rdx
	0x48, 0x09, 0xfe, //0x00000f49 orq          %rdi, %rsi
	0x48, 0x85, 0xd2, //0x00000f4c testq        %rdx, %rdx
	0x0f, 0x84, 0x87, 0xff, 0xff, 0xff, //0x00000f4f je           LBB0_118
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x00000f55 jmp          LBB0_127
	//0x00000f5a LBB0_125
	0x4c, 0x89, 0xe0, //0x00000f5a movq         %r12, %rax
	0x48, 0x2b, 0x45, 0xc0, //0x00000f5d subq         $-64(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xd9, //0x00000f61 bsfq         %rcx, %r11
	0x49, 0x01, 0xc3, //0x00000f65 addq         %rax, %r11
	0xe9, 0x50, 0xff, 0xff, 0xff, //0x00000f68 jmp          LBB0_116
	//0x00000f6d LBB0_127
	0x48, 0x0f, 0xbc, 0xca, //0x00000f6d bsfq         %rdx, %rcx
	0x48, 0x85, 0xf6, //0x00000f71 testq        %rsi, %rsi
	0x48, 0x8b, 0x45, 0xc0, //0x00000f74 movq         $-64(%rbp), %rax
	0x0f, 0x84, 0x8d, 0x01, 0x00, 0x00, //0x00000f78 je           LBB0_203
	0x48, 0x0f, 0xbc, 0xd6, //0x00000f7e bsfq         %rsi, %rdx
	0x4c, 0x8b, 0x75, 0xc8, //0x00000f82 movq         $-56(%rbp), %r14
	0x49, 0x29, 0xc4, //0x00000f86 subq         %rax, %r12
	0x48, 0x39, 0xca, //0x00000f89 cmpq         %rcx, %rdx
	0x0f, 0x83, 0x8e, 0x01, 0x00, 0x00, //0x00000f8c jae          LBB0_204
	0xe9, 0x19, 0x1a, 0x00, 0x00, //0x00000f92 jmp          LBB0_129
	//0x00000f97 LBB0_181
	0xba, 0x40, 0x00, 0x00, 0x00, //0x00000f97 movl         $64, %edx
	//0x00000f9c LBB0_182
	0x4c, 0x8b, 0x55, 0xd0, //0x00000f9c movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x00000fa0 movq         $-56(%rbp), %r14
	0x49, 0x29, 0xc4, //0x00000fa4 subq         %rax, %r12
	0x48, 0x39, 0xca, //0x00000fa7 cmpq         %rcx, %rdx
	0x0f, 0x82, 0xe5, 0x19, 0x00, 0x00, //0x00000faa jb           LBB0_509
	0x4d, 0x8d, 0x64, 0x0c, 0x01, //0x00000fb0 leaq         $1(%r12,%rcx), %r12
	0xe9, 0x10, 0x0b, 0x00, 0x00, //0x00000fb5 jmp          LBB0_184
	//0x00000fba LBB0_186
	0x49, 0x01, 0xce, //0x00000fba addq         %rcx, %r14
	//0x00000fbd LBB0_187
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000fbd movq         $-1, %rcx
	0x4d, 0x85, 0xe4, //0x00000fc4 testq        %r12, %r12
	0x48, 0x8b, 0x55, 0xd0, //0x00000fc7 movq         $-48(%rbp), %rdx
	0x0f, 0x84, 0x04, 0x18, 0x00, 0x00, //0x00000fcb je           LBB0_481
	//0x00000fd1 LBB0_188
	0x4d, 0x85, 0xc0, //0x00000fd1 testq        %r8, %r8
	0x0f, 0x84, 0xfb, 0x17, 0x00, 0x00, //0x00000fd4 je           LBB0_481
	0x48, 0x85, 0xc0, //0x00000fda testq        %rax, %rax
	0x0f, 0x84, 0xf2, 0x17, 0x00, 0x00, //0x00000fdd je           LBB0_481
	0x4d, 0x29, 0xfe, //0x00000fe3 subq         %r15, %r14
	0x49, 0x8d, 0x4e, 0xff, //0x00000fe6 leaq         $-1(%r14), %rcx
	0x49, 0x39, 0xcc, //0x00000fea cmpq         %rcx, %r12
	0x0f, 0x84, 0x88, 0x00, 0x00, 0x00, //0x00000fed je           LBB0_196
	0x48, 0x39, 0xc8, //0x00000ff3 cmpq         %rcx, %rax
	0x0f, 0x84, 0x7f, 0x00, 0x00, 0x00, //0x00000ff6 je           LBB0_196
	0x49, 0x39, 0xc8, //0x00000ffc cmpq         %rcx, %r8
	0x0f, 0x84, 0x76, 0x00, 0x00, 0x00, //0x00000fff je           LBB0_196
	0x4d, 0x85, 0xc0, //0x00001005 testq        %r8, %r8
	0xc5, 0xfe, 0x6f, 0x2d, 0x70, 0xf0, 0xff, 0xff, //0x00001008 vmovdqu      $-3984(%rip), %ymm5  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x88, 0xf0, 0xff, 0xff, //0x00001010 vmovdqu      $-3960(%rip), %ymm6  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0xa0, 0xf0, 0xff, 0xff, //0x00001018 vmovdqu      $-3936(%rip), %ymm7  /* LCPI0_3+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0xb8, 0xf0, 0xff, 0xff, //0x00001020 vmovdqu      $-3912(%rip), %ymm8  /* LCPI0_4+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00001028 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x15, 0xcb, 0xf0, 0xff, 0xff, //0x0000102d vmovdqu      $-3893(%rip), %ymm10  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0xe3, 0xf0, 0xff, 0xff, //0x00001035 vmovdqu      $-3869(%rip), %ymm11  /* LCPI0_6+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0xfb, 0xf0, 0xff, 0xff, //0x0000103d vmovdqu      $-3845(%rip), %ymm12  /* LCPI0_7+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x13, 0xf1, 0xff, 0xff, //0x00001045 vmovdqu      $-3821(%rip), %ymm13  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x2b, 0xf1, 0xff, 0xff, //0x0000104d vmovdqu      $-3797(%rip), %ymm14  /* LCPI0_9+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0x43, 0xf1, 0xff, 0xff, //0x00001055 vmovdqu      $-3773(%rip), %ymm15  /* LCPI0_10+0(%rip) */
	0x0f, 0x8e, 0x85, 0x00, 0x00, 0x00, //0x0000105d jle          LBB0_200
	0x49, 0x8d, 0x48, 0xff, //0x00001063 leaq         $-1(%r8), %rcx
	0x49, 0x39, 0xcc, //0x00001067 cmpq         %rcx, %r12
	0x0f, 0x84, 0x78, 0x00, 0x00, 0x00, //0x0000106a je           LBB0_200
	0x49, 0xf7, 0xd0, //0x00001070 notq         %r8
	0x4d, 0x89, 0xc6, //0x00001073 movq         %r8, %r14
	0xe9, 0x58, 0x00, 0x00, 0x00, //0x00001076 jmp          LBB0_197
	//0x0000107b LBB0_196
	0x49, 0xf7, 0xde, //0x0000107b negq         %r14
	0xc5, 0xfe, 0x6f, 0x2d, 0xfa, 0xef, 0xff, 0xff, //0x0000107e vmovdqu      $-4102(%rip), %ymm5  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x12, 0xf0, 0xff, 0xff, //0x00001086 vmovdqu      $-4078(%rip), %ymm6  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x2a, 0xf0, 0xff, 0xff, //0x0000108e vmovdqu      $-4054(%rip), %ymm7  /* LCPI0_3+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x42, 0xf0, 0xff, 0xff, //0x00001096 vmovdqu      $-4030(%rip), %ymm8  /* LCPI0_4+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000109e vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x15, 0x55, 0xf0, 0xff, 0xff, //0x000010a3 vmovdqu      $-4011(%rip), %ymm10  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x6d, 0xf0, 0xff, 0xff, //0x000010ab vmovdqu      $-3987(%rip), %ymm11  /* LCPI0_6+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x85, 0xf0, 0xff, 0xff, //0x000010b3 vmovdqu      $-3963(%rip), %ymm12  /* LCPI0_7+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x9d, 0xf0, 0xff, 0xff, //0x000010bb vmovdqu      $-3939(%rip), %ymm13  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0xb5, 0xf0, 0xff, 0xff, //0x000010c3 vmovdqu      $-3915(%rip), %ymm14  /* LCPI0_9+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0xcd, 0xf0, 0xff, 0xff, //0x000010cb vmovdqu      $-3891(%rip), %ymm15  /* LCPI0_10+0(%rip) */
	//0x000010d3 LBB0_197
	0x4d, 0x85, 0xf6, //0x000010d3 testq        %r14, %r14
	0x4c, 0x8b, 0x65, 0xa8, //0x000010d6 movq         $-88(%rbp), %r12
	0x0f, 0x88, 0xf2, 0x16, 0x00, 0x00, //0x000010da js           LBB0_480
	0x48, 0x8b, 0x0a, //0x000010e0 movq         (%rdx), %rcx
	0xe9, 0x79, 0xf1, 0xff, 0xff, //0x000010e3 jmp          LBB0_199
	//0x000010e8 LBB0_200
	0x48, 0x89, 0xc1, //0x000010e8 movq         %rax, %rcx
	0x4c, 0x09, 0xe1, //0x000010eb orq          %r12, %rcx
	0x4c, 0x39, 0xe0, //0x000010ee cmpq         %r12, %rax
	0x0f, 0x8c, 0x98, 0x02, 0x00, 0x00, //0x000010f1 jl           LBB0_249
	0x48, 0x85, 0xc9, //0x000010f7 testq        %rcx, %rcx
	0x0f, 0x88, 0x8f, 0x02, 0x00, 0x00, //0x000010fa js           LBB0_249
	0x48, 0xf7, 0xd0, //0x00001100 notq         %rax
	0x49, 0x89, 0xc6, //0x00001103 movq         %rax, %r14
	0xe9, 0xc8, 0xff, 0xff, 0xff, //0x00001106 jmp          LBB0_197
	//0x0000110b LBB0_203
	0xba, 0x40, 0x00, 0x00, 0x00, //0x0000110b movl         $64, %edx
	0x4c, 0x8b, 0x75, 0xc8, //0x00001110 movq         $-56(%rbp), %r14
	0x49, 0x29, 0xc4, //0x00001114 subq         %rax, %r12
	0x48, 0x39, 0xca, //0x00001117 cmpq         %rcx, %rdx
	0x0f, 0x82, 0x90, 0x18, 0x00, 0x00, //0x0000111a jb           LBB0_129
	//0x00001120 LBB0_204
	0x4d, 0x8d, 0x64, 0x0c, 0x01, //0x00001120 leaq         $1(%r12,%rcx), %r12
	0xe9, 0xfe, 0x0a, 0x00, 0x00, //0x00001125 jmp          LBB0_205
	//0x0000112a LBB0_209
	0x49, 0x8b, 0x06, //0x0000112a movq         (%r14), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x0000112d cmpq         $4095, %rax
	0x0f, 0x8f, 0x49, 0x16, 0x00, 0x00, //0x00001133 jg           LBB0_489
	0x48, 0x8d, 0x48, 0x01, //0x00001139 leaq         $1(%rax), %rcx
	0x49, 0x89, 0x0e, //0x0000113d movq         %rcx, (%r14)
	0x49, 0xc7, 0x44, 0xc6, 0x08, 0x05, 0x00, 0x00, 0x00, //0x00001140 movq         $5, $8(%r14,%rax,8)
	0xe9, 0x32, 0xf1, 0xff, 0xff, //0x00001149 jmp          LBB0_3
	//0x0000114e LBB0_211
	0x48, 0x8b, 0x45, 0xb0, //0x0000114e movq         $-80(%rbp), %rax
	0x48, 0x8b, 0x40, 0x08, //0x00001152 movq         $8(%rax), %rax
	0xf6, 0x45, 0x98, 0x20, //0x00001156 testb        $32, $-104(%rbp)
	0x48, 0x89, 0x45, 0xb8, //0x0000115a movq         %rax, $-72(%rbp)
	0x0f, 0x85, 0x46, 0x02, 0x00, 0x00, //0x0000115e jne          LBB0_250
	0x48, 0x89, 0xc1, //0x00001164 movq         %rax, %rcx
	0x4c, 0x29, 0xe1, //0x00001167 subq         %r12, %rcx
	0x0f, 0x84, 0x06, 0x18, 0x00, 0x00, //0x0000116a je           LBB0_510
	0x4b, 0x8d, 0x1c, 0x21, //0x00001170 leaq         (%r9,%r12), %rbx
	0x48, 0x83, 0xf9, 0x40, //0x00001174 cmpq         $64, %rcx
	0x4c, 0x89, 0xca, //0x00001178 movq         %r9, %rdx
	0x0f, 0x82, 0x71, 0x0d, 0x00, 0x00, //0x0000117b jb           LBB0_363
	0x41, 0x89, 0xcf, //0x00001181 movl         %ecx, %r15d
	0x41, 0x83, 0xe7, 0x3f, //0x00001184 andl         $63, %r15d
	0x48, 0x8d, 0x44, 0x30, 0xc0, //0x00001188 leaq         $-64(%rax,%rsi), %rax
	0x48, 0x83, 0xe0, 0xc0, //0x0000118d andq         $-64, %rax
	0x4c, 0x01, 0xe0, //0x00001191 addq         %r12, %rax
	0x49, 0x89, 0xd1, //0x00001194 movq         %rdx, %r9
	0x4c, 0x8d, 0x44, 0x02, 0x40, //0x00001197 leaq         $64(%rdx,%rax), %r8
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x0000119c movq         $-1, %r11
	0x45, 0x31, 0xf6, //0x000011a3 xorl         %r14d, %r14d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000011a6 .p2align 4, 0x90
	//0x000011b0 LBB0_215
	0xc5, 0xfe, 0x6f, 0x03, //0x000011b0 vmovdqu      (%rbx), %ymm0
	0xc5, 0xfe, 0x6f, 0x4b, 0x20, //0x000011b4 vmovdqu      $32(%rbx), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x000011b9 vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x000011bd vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x000011c1 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x000011c5 vpmovmskb    %ymm2, %edx
	0xc5, 0xfd, 0x74, 0xc7, //0x000011c9 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x000011cd vpmovmskb    %ymm0, %esi
	0xc5, 0xf5, 0x74, 0xc7, //0x000011d1 vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x000011d5 vpmovmskb    %ymm0, %eax
	0x48, 0xc1, 0xe2, 0x20, //0x000011d9 shlq         $32, %rdx
	0x48, 0xc1, 0xe0, 0x20, //0x000011dd shlq         $32, %rax
	0x48, 0x09, 0xc6, //0x000011e1 orq          %rax, %rsi
	0x49, 0x83, 0xfb, 0xff, //0x000011e4 cmpq         $-1, %r11
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x000011e8 jne          LBB0_217
	0x48, 0x85, 0xf6, //0x000011ee testq        %rsi, %rsi
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x000011f1 jne          LBB0_224
	//0x000011f7 LBB0_217
	0x48, 0x09, 0xfa, //0x000011f7 orq          %rdi, %rdx
	0x48, 0x89, 0xf0, //0x000011fa movq         %rsi, %rax
	0x4c, 0x09, 0xf0, //0x000011fd orq          %r14, %rax
	0x0f, 0x85, 0x3c, 0x00, 0x00, 0x00, //0x00001200 jne          LBB0_225
	//0x00001206 LBB0_218
	0x48, 0x85, 0xd2, //0x00001206 testq        %rdx, %rdx
	0x0f, 0x85, 0x48, 0x0b, 0x00, 0x00, //0x00001209 jne          LBB0_226
	//0x0000120f LBB0_219
	0x48, 0x83, 0xc1, 0xc0, //0x0000120f addq         $-64, %rcx
	0x48, 0x83, 0xc3, 0x40, //0x00001213 addq         $64, %rbx
	0x48, 0x83, 0xf9, 0x3f, //0x00001217 cmpq         $63, %rcx
	0x0f, 0x87, 0x8f, 0xff, 0xff, 0xff, //0x0000121b ja           LBB0_215
	0xe9, 0xf0, 0x0a, 0x00, 0x00, //0x00001221 jmp          LBB0_220
	//0x00001226 LBB0_224
	0x48, 0x89, 0xd8, //0x00001226 movq         %rbx, %rax
	0x4c, 0x29, 0xc8, //0x00001229 subq         %r9, %rax
	0x4c, 0x0f, 0xbc, 0xde, //0x0000122c bsfq         %rsi, %r11
	0x49, 0x01, 0xc3, //0x00001230 addq         %rax, %r11
	0x48, 0x09, 0xfa, //0x00001233 orq          %rdi, %rdx
	0x48, 0x89, 0xf0, //0x00001236 movq         %rsi, %rax
	0x4c, 0x09, 0xf0, //0x00001239 orq          %r14, %rax
	0x0f, 0x84, 0xc4, 0xff, 0xff, 0xff, //0x0000123c je           LBB0_218
	//0x00001242 LBB0_225
	0x4c, 0x89, 0xf0, //0x00001242 movq         %r14, %rax
	0x48, 0xf7, 0xd0, //0x00001245 notq         %rax
	0x48, 0x21, 0xf0, //0x00001248 andq         %rsi, %rax
	0x4c, 0x8d, 0x14, 0x00, //0x0000124b leaq         (%rax,%rax), %r10
	0x4d, 0x09, 0xf2, //0x0000124f orq          %r14, %r10
	0x4c, 0x89, 0xd7, //0x00001252 movq         %r10, %rdi
	0x48, 0xf7, 0xd7, //0x00001255 notq         %rdi
	0x48, 0x21, 0xf7, //0x00001258 andq         %rsi, %rdi
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000125b movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf7, //0x00001265 andq         %rsi, %rdi
	0x45, 0x31, 0xf6, //0x00001268 xorl         %r14d, %r14d
	0x48, 0x01, 0xc7, //0x0000126b addq         %rax, %rdi
	0x41, 0x0f, 0x92, 0xc6, //0x0000126e setb         %r14b
	0x48, 0x01, 0xff, //0x00001272 addq         %rdi, %rdi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00001275 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc7, //0x0000127f xorq         %rax, %rdi
	0x4c, 0x21, 0xd7, //0x00001282 andq         %r10, %rdi
	0x4c, 0x8b, 0x55, 0xd0, //0x00001285 movq         $-48(%rbp), %r10
	0x48, 0xf7, 0xd7, //0x00001289 notq         %rdi
	0x48, 0x21, 0xfa, //0x0000128c andq         %rdi, %rdx
	0x48, 0x85, 0xd2, //0x0000128f testq        %rdx, %rdx
	0x0f, 0x84, 0x77, 0xff, 0xff, 0xff, //0x00001292 je           LBB0_219
	0xe9, 0xba, 0x0a, 0x00, 0x00, //0x00001298 jmp          LBB0_226
	//0x0000129d LBB0_227
	0x48, 0x8b, 0x45, 0xb0, //0x0000129d movq         $-80(%rbp), %rax
	0x4c, 0x8b, 0x70, 0x08, //0x000012a1 movq         $8(%rax), %r14
	0x4d, 0x29, 0xe6, //0x000012a5 subq         %r12, %r14
	0x0f, 0x84, 0xb3, 0x16, 0x00, 0x00, //0x000012a8 je           LBB0_498
	0x4c, 0x89, 0x65, 0xa8, //0x000012ae movq         %r12, $-88(%rbp)
	0x4c, 0x89, 0xc8, //0x000012b2 movq         %r9, %rax
	0x4c, 0x01, 0xe0, //0x000012b5 addq         %r12, %rax
	0x49, 0x89, 0xc1, //0x000012b8 movq         %rax, %r9
	0x80, 0x38, 0x30, //0x000012bb cmpb         $48, (%rax)
	0x0f, 0x85, 0xb5, 0x02, 0x00, 0x00, //0x000012be jne          LBB0_271
	0x41, 0xbf, 0x01, 0x00, 0x00, 0x00, //0x000012c4 movl         $1, %r15d
	0x49, 0x83, 0xfe, 0x01, //0x000012ca cmpq         $1, %r14
	0x0f, 0x85, 0x7a, 0x02, 0x00, 0x00, //0x000012ce jne          LBB0_269
	0x4c, 0x8b, 0x65, 0xa8, //0x000012d4 movq         $-88(%rbp), %r12
	0xe9, 0x9d, 0x08, 0x00, 0x00, //0x000012d8 jmp          LBB0_343
	//0x000012dd LBB0_231
	0x48, 0x8b, 0x45, 0xb0, //0x000012dd movq         $-80(%rbp), %rax
	0x48, 0x8b, 0x50, 0x08, //0x000012e1 movq         $8(%rax), %rdx
	0x48, 0x8d, 0x42, 0xfd, //0x000012e5 leaq         $-3(%rdx), %rax
	0x49, 0x39, 0xc5, //0x000012e9 cmpq         %rax, %r13
	0x0f, 0x83, 0x0c, 0x15, 0x00, 0x00, //0x000012ec jae          LBB0_490
	0x41, 0x81, 0x3f, 0x6e, 0x75, 0x6c, 0x6c, //0x000012f2 cmpl         $1819047278, (%r15)
	0x0f, 0x84, 0x99, 0xef, 0xff, 0xff, //0x000012f9 je           LBB0_1
	0xe9, 0x5a, 0x15, 0x00, 0x00, //0x000012ff jmp          LBB0_233
	//0x00001304 LBB0_238
	0x49, 0x8b, 0x06, //0x00001304 movq         (%r14), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00001307 cmpq         $4095, %rax
	0x0f, 0x8f, 0x6f, 0x14, 0x00, 0x00, //0x0000130d jg           LBB0_489
	0x48, 0x8d, 0x48, 0x01, //0x00001313 leaq         $1(%rax), %rcx
	0x49, 0x89, 0x0e, //0x00001317 movq         %rcx, (%r14)
	0x49, 0xc7, 0x44, 0xc6, 0x08, 0x06, 0x00, 0x00, 0x00, //0x0000131a movq         $6, $8(%r14,%rax,8)
	0xe9, 0x58, 0xef, 0xff, 0xff, //0x00001323 jmp          LBB0_3
	//0x00001328 LBB0_240
	0x48, 0x8b, 0x45, 0xb0, //0x00001328 movq         $-80(%rbp), %rax
	0x48, 0x8b, 0x50, 0x08, //0x0000132c movq         $8(%rax), %rdx
	0x48, 0x8d, 0x42, 0xfc, //0x00001330 leaq         $-4(%rdx), %rax
	0x49, 0x39, 0xc5, //0x00001334 cmpq         %rax, %r13
	0x0f, 0x83, 0xc1, 0x14, 0x00, 0x00, //0x00001337 jae          LBB0_490
	0x43, 0x8b, 0x14, 0x21, //0x0000133d movl         (%r9,%r12), %edx
	0x81, 0xfa, 0x61, 0x6c, 0x73, 0x65, //0x00001341 cmpl         $1702063201, %edx
	0x0f, 0x85, 0x63, 0x15, 0x00, 0x00, //0x00001347 jne          LBB0_491
	0x4c, 0x89, 0xe0, //0x0000134d movq         %r12, %rax
	0x4d, 0x8d, 0x65, 0x05, //0x00001350 leaq         $5(%r13), %r12
	0x4d, 0x89, 0x22, //0x00001354 movq         %r12, (%r10)
	0x4c, 0x89, 0xe9, //0x00001357 movq         %r13, %rcx
	0x48, 0x85, 0xc0, //0x0000135a testq        %rax, %rax
	0x0f, 0x8f, 0x1d, 0xef, 0xff, 0xff, //0x0000135d jg           LBB0_3
	0xe9, 0x81, 0x14, 0x00, 0x00, //0x00001363 jmp          LBB0_501
	//0x00001368 LBB0_243
	0x48, 0x8b, 0x45, 0xb0, //0x00001368 movq         $-80(%rbp), %rax
	0x48, 0x8b, 0x50, 0x08, //0x0000136c movq         $8(%rax), %rdx
	0x48, 0x8d, 0x42, 0xfd, //0x00001370 leaq         $-3(%rdx), %rax
	0x49, 0x39, 0xc5, //0x00001374 cmpq         %rax, %r13
	0x0f, 0x83, 0x81, 0x14, 0x00, 0x00, //0x00001377 jae          LBB0_490
	0x41, 0x81, 0x3f, 0x74, 0x72, 0x75, 0x65, //0x0000137d cmpl         $1702195828, (%r15)
	0x0f, 0x84, 0x0e, 0xef, 0xff, 0xff, //0x00001384 je           LBB0_1
	0xe9, 0x76, 0x15, 0x00, 0x00, //0x0000138a jmp          LBB0_245
	//0x0000138f LBB0_249
	0x48, 0x85, 0xc9, //0x0000138f testq        %rcx, %rcx
	0x49, 0x8d, 0x4c, 0x24, 0xff, //0x00001392 leaq         $-1(%r12), %rcx
	0x49, 0xf7, 0xd4, //0x00001397 notq         %r12
	0x4d, 0x0f, 0x48, 0xe6, //0x0000139a cmovsq       %r14, %r12
	0x48, 0x39, 0xc8, //0x0000139e cmpq         %rcx, %rax
	0x4d, 0x0f, 0x44, 0xf4, //0x000013a1 cmoveq       %r12, %r14
	0xe9, 0x29, 0xfd, 0xff, 0xff, //0x000013a5 jmp          LBB0_197
	//0x000013aa LBB0_250
	0x48, 0x89, 0xc3, //0x000013aa movq         %rax, %rbx
	0x4c, 0x29, 0xe3, //0x000013ad subq         %r12, %rbx
	0x0f, 0x84, 0xc0, 0x15, 0x00, 0x00, //0x000013b0 je           LBB0_510
	0x4c, 0x89, 0xe1, //0x000013b6 movq         %r12, %rcx
	0x4d, 0x01, 0xcc, //0x000013b9 addq         %r9, %r12
	0x48, 0x83, 0xfb, 0x40, //0x000013bc cmpq         $64, %rbx
	0x4c, 0x89, 0x4d, 0xc0, //0x000013c0 movq         %r9, $-64(%rbp)
	0x0f, 0x82, 0x44, 0x0b, 0x00, 0x00, //0x000013c4 jb           LBB0_364
	0x41, 0x89, 0xde, //0x000013ca movl         %ebx, %r14d
	0x41, 0x83, 0xe6, 0x3f, //0x000013cd andl         $63, %r14d
	0x48, 0x8d, 0x44, 0x30, 0xc0, //0x000013d1 leaq         $-64(%rax,%rsi), %rax
	0x48, 0x83, 0xe0, 0xc0, //0x000013d6 andq         $-64, %rax
	0x49, 0x89, 0xc8, //0x000013da movq         %rcx, %r8
	0x48, 0x01, 0xc8, //0x000013dd addq         %rcx, %rax
	0x49, 0x8d, 0x44, 0x01, 0x40, //0x000013e0 leaq         $64(%r9,%rax), %rax
	0x48, 0x89, 0x45, 0xa0, //0x000013e5 movq         %rax, $-96(%rbp)
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000013e9 movq         $-1, %r11
	0x45, 0x31, 0xff, //0x000013f0 xorl         %r15d, %r15d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000013f3 .p2align 4, 0x90
	//0x00001400 LBB0_253
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x00001400 vmovdqu      (%r12), %ymm0
	0xc4, 0xc1, 0x7e, 0x6f, 0x4c, 0x24, 0x20, //0x00001406 vmovdqu      $32(%r12), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x0000140d vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0x7d, 0xd7, 0xca, //0x00001411 vpmovmskb    %ymm2, %r9d
	0xc5, 0xf5, 0x74, 0xd6, //0x00001415 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00001419 vpmovmskb    %ymm2, %edx
	0xc5, 0xfd, 0x74, 0xd7, //0x0000141d vpcmpeqb     %ymm7, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x00001421 vpmovmskb    %ymm2, %ecx
	0xc5, 0xf5, 0x74, 0xd7, //0x00001425 vpcmpeqb     %ymm7, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x00001429 vpmovmskb    %ymm2, %eax
	0xc5, 0xbd, 0x64, 0xd0, //0x0000142d vpcmpgtb     %ymm0, %ymm8, %ymm2
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x00001431 vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xfd, 0xdb, 0xc2, //0x00001436 vpand        %ymm2, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf8, //0x0000143a vpmovmskb    %ymm0, %edi
	0xc5, 0xbd, 0x64, 0xc1, //0x0000143e vpcmpgtb     %ymm1, %ymm8, %ymm0
	0xc4, 0xc1, 0x75, 0x64, 0xc9, //0x00001442 vpcmpgtb     %ymm9, %ymm1, %ymm1
	0xc5, 0xf5, 0xdb, 0xc0, //0x00001447 vpand        %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x0000144b vpmovmskb    %ymm0, %esi
	0x48, 0xc1, 0xe2, 0x20, //0x0000144f shlq         $32, %rdx
	0x48, 0xc1, 0xe0, 0x20, //0x00001453 shlq         $32, %rax
	0x48, 0x09, 0xc1, //0x00001457 orq          %rax, %rcx
	0x49, 0x83, 0xfb, 0xff, //0x0000145a cmpq         $-1, %r11
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x0000145e jne          LBB0_255
	0x48, 0x85, 0xc9, //0x00001464 testq        %rcx, %rcx
	0x0f, 0x85, 0x9d, 0x00, 0x00, 0x00, //0x00001467 jne          LBB0_264
	//0x0000146d LBB0_255
	0x48, 0xc1, 0xe6, 0x20, //0x0000146d shlq         $32, %rsi
	0x4c, 0x09, 0xca, //0x00001471 orq          %r9, %rdx
	0x48, 0x89, 0xc8, //0x00001474 movq         %rcx, %rax
	0x4c, 0x09, 0xf8, //0x00001477 orq          %r15, %rax
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x0000147a jne          LBB0_265
	0x48, 0x09, 0xfe, //0x00001480 orq          %rdi, %rsi
	0x48, 0x85, 0xd2, //0x00001483 testq        %rdx, %rdx
	0x0f, 0x85, 0x91, 0x00, 0x00, 0x00, //0x00001486 jne          LBB0_266
	//0x0000148c LBB0_257
	0x48, 0x85, 0xf6, //0x0000148c testq        %rsi, %rsi
	0x0f, 0x85, 0x9f, 0x13, 0x00, 0x00, //0x0000148f jne          LBB0_502
	0x48, 0x83, 0xc3, 0xc0, //0x00001495 addq         $-64, %rbx
	0x49, 0x83, 0xc4, 0x40, //0x00001499 addq         $64, %r12
	0x48, 0x83, 0xfb, 0x3f, //0x0000149d cmpq         $63, %rbx
	0x0f, 0x87, 0x59, 0xff, 0xff, 0xff, //0x000014a1 ja           LBB0_253
	0xe9, 0xe7, 0x08, 0x00, 0x00, //0x000014a7 jmp          LBB0_259
	//0x000014ac LBB0_265
	0x4d, 0x89, 0xfa, //0x000014ac movq         %r15, %r10
	0x49, 0xf7, 0xd2, //0x000014af notq         %r10
	0x49, 0x21, 0xca, //0x000014b2 andq         %rcx, %r10
	0x4f, 0x8d, 0x0c, 0x12, //0x000014b5 leaq         (%r10,%r10), %r9
	0x4d, 0x09, 0xf9, //0x000014b9 orq          %r15, %r9
	0x4c, 0x89, 0xc8, //0x000014bc movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x000014bf notq         %rax
	0x48, 0x21, 0xc8, //0x000014c2 andq         %rcx, %rax
	0x48, 0xb9, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000014c5 movabsq      $-6148914691236517206, %rcx
	0x48, 0x21, 0xc8, //0x000014cf andq         %rcx, %rax
	0x45, 0x31, 0xff, //0x000014d2 xorl         %r15d, %r15d
	0x4c, 0x01, 0xd0, //0x000014d5 addq         %r10, %rax
	0x4c, 0x8b, 0x55, 0xd0, //0x000014d8 movq         $-48(%rbp), %r10
	0x41, 0x0f, 0x92, 0xc7, //0x000014dc setb         %r15b
	0x48, 0x01, 0xc0, //0x000014e0 addq         %rax, %rax
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000014e3 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xc8, //0x000014ed xorq         %rcx, %rax
	0x4c, 0x21, 0xc8, //0x000014f0 andq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x000014f3 notq         %rax
	0x48, 0x21, 0xc2, //0x000014f6 andq         %rax, %rdx
	0x48, 0x09, 0xfe, //0x000014f9 orq          %rdi, %rsi
	0x48, 0x85, 0xd2, //0x000014fc testq        %rdx, %rdx
	0x0f, 0x84, 0x87, 0xff, 0xff, 0xff, //0x000014ff je           LBB0_257
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x00001505 jmp          LBB0_266
	//0x0000150a LBB0_264
	0x4c, 0x89, 0xe0, //0x0000150a movq         %r12, %rax
	0x48, 0x2b, 0x45, 0xc0, //0x0000150d subq         $-64(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xd9, //0x00001511 bsfq         %rcx, %r11
	0x49, 0x01, 0xc3, //0x00001515 addq         %rax, %r11
	0xe9, 0x50, 0xff, 0xff, 0xff, //0x00001518 jmp          LBB0_255
	//0x0000151d LBB0_266
	0x48, 0x0f, 0xbc, 0xca, //0x0000151d bsfq         %rdx, %rcx
	0x48, 0x85, 0xf6, //0x00001521 testq        %rsi, %rsi
	0x48, 0x8b, 0x45, 0xc0, //0x00001524 movq         $-64(%rbp), %rax
	0x0f, 0x84, 0x20, 0x04, 0x00, 0x00, //0x00001528 je           LBB0_320
	0x48, 0x0f, 0xbc, 0xd6, //0x0000152e bsfq         %rsi, %rdx
	0xe9, 0x1c, 0x04, 0x00, 0x00, //0x00001532 jmp          LBB0_321
	//0x00001537 LBB0_268
	0x4c, 0x89, 0xca, //0x00001537 movq         %r9, %rdx
	0x48, 0xf7, 0xd2, //0x0000153a notq         %rdx
	0x49, 0x01, 0xd5, //0x0000153d addq         %rdx, %r13
	0x49, 0x39, 0xcd, //0x00001540 cmpq         %rcx, %r13
	0x0f, 0x82, 0x3f, 0xef, 0xff, 0xff, //0x00001543 jb           LBB0_36
	0xe9, 0x28, 0x12, 0x00, 0x00, //0x00001549 jmp          LBB0_474
	//0x0000154e LBB0_269
	0x41, 0x8a, 0x49, 0x01, //0x0000154e movb         $1(%r9), %cl
	0x80, 0xc1, 0xd2, //0x00001552 addb         $-46, %cl
	0x80, 0xf9, 0x37, //0x00001555 cmpb         $55, %cl
	0x4c, 0x8b, 0x65, 0xa8, //0x00001558 movq         $-88(%rbp), %r12
	0x0f, 0x87, 0x18, 0x06, 0x00, 0x00, //0x0000155c ja           LBB0_343
	0x0f, 0xb6, 0xc1, //0x00001562 movzbl       %cl, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00001565 movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x0000156f btq          %rax, %rcx
	0x0f, 0x83, 0x01, 0x06, 0x00, 0x00, //0x00001573 jae          LBB0_343
	//0x00001579 LBB0_271
	0x49, 0x83, 0xfe, 0x20, //0x00001579 cmpq         $32, %r14
	0x0f, 0x82, 0x52, 0x09, 0x00, 0x00, //0x0000157d jb           LBB0_362
	0x49, 0x8d, 0x4e, 0xe0, //0x00001583 leaq         $-32(%r14), %rcx
	0x48, 0x89, 0xc8, //0x00001587 movq         %rcx, %rax
	0x48, 0x83, 0xe0, 0xe0, //0x0000158a andq         $-32, %rax
	0x4d, 0x89, 0xca, //0x0000158e movq         %r9, %r10
	0x4e, 0x8d, 0x7c, 0x08, 0x20, //0x00001591 leaq         $32(%rax,%r9), %r15
	0x83, 0xe1, 0x1f, //0x00001596 andl         $31, %ecx
	0x48, 0x89, 0x4d, 0xc0, //0x00001599 movq         %rcx, $-64(%rbp)
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000159d movq         $-1, %r8
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000015a4 movq         $-1, %r11
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x000015ab movq         $-1, %r12
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000015b2 .p2align 4, 0x90
	//0x000015c0 LBB0_273
	0xc4, 0xc1, 0x7e, 0x6f, 0x02, //0x000015c0 vmovdqu      (%r10), %ymm0
	0xc4, 0xc1, 0x7d, 0x64, 0xca, //0x000015c5 vpcmpgtb     %ymm10, %ymm0, %ymm1
	0xc5, 0xa5, 0x64, 0xd0, //0x000015ca vpcmpgtb     %ymm0, %ymm11, %ymm2
	0xc5, 0xf5, 0xdb, 0xca, //0x000015ce vpand        %ymm2, %ymm1, %ymm1
	0xc5, 0x9d, 0x74, 0xd0, //0x000015d2 vpcmpeqb     %ymm0, %ymm12, %ymm2
	0xc5, 0x95, 0x74, 0xd8, //0x000015d6 vpcmpeqb     %ymm0, %ymm13, %ymm3
	0xc5, 0xe5, 0xeb, 0xd2, //0x000015da vpor         %ymm2, %ymm3, %ymm2
	0xc5, 0xbd, 0xeb, 0xd8, //0x000015de vpor         %ymm0, %ymm8, %ymm3
	0xc5, 0x8d, 0x74, 0xc0, //0x000015e2 vpcmpeqb     %ymm0, %ymm14, %ymm0
	0xc5, 0xfd, 0xd7, 0xf8, //0x000015e6 vpmovmskb    %ymm0, %edi
	0xc5, 0x85, 0x74, 0xdb, //0x000015ea vpcmpeqb     %ymm3, %ymm15, %ymm3
	0xc5, 0xfd, 0xd7, 0xd3, //0x000015ee vpmovmskb    %ymm3, %edx
	0xc5, 0xfd, 0xd7, 0xf2, //0x000015f2 vpmovmskb    %ymm2, %esi
	0xc5, 0xe5, 0xeb, 0xc0, //0x000015f6 vpor         %ymm0, %ymm3, %ymm0
	0xc5, 0xed, 0xeb, 0xc9, //0x000015fa vpor         %ymm1, %ymm2, %ymm1
	0xc5, 0xfd, 0xeb, 0xc1, //0x000015fe vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x00001602 vpmovmskb    %ymm0, %eax
	0x48, 0xf7, 0xd0, //0x00001606 notq         %rax
	0x48, 0x0f, 0xbc, 0xc8, //0x00001609 bsfq         %rax, %rcx
	0x83, 0xf9, 0x20, //0x0000160d cmpl         $32, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x00001610 je           LBB0_275
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00001616 movl         $-1, %eax
	0xd3, 0xe0, //0x0000161b shll         %cl, %eax
	0xf7, 0xd0, //0x0000161d notl         %eax
	0x21, 0xc7, //0x0000161f andl         %eax, %edi
	0x21, 0xc2, //0x00001621 andl         %eax, %edx
	0x21, 0xf0, //0x00001623 andl         %esi, %eax
	0x89, 0xc6, //0x00001625 movl         %eax, %esi
	//0x00001627 LBB0_275
	0x8d, 0x5f, 0xff, //0x00001627 leal         $-1(%rdi), %ebx
	0x21, 0xfb, //0x0000162a andl         %edi, %ebx
	0x0f, 0x85, 0xb0, 0x06, 0x00, 0x00, //0x0000162c jne          LBB0_349
	0x8d, 0x5a, 0xff, //0x00001632 leal         $-1(%rdx), %ebx
	0x21, 0xd3, //0x00001635 andl         %edx, %ebx
	0x0f, 0x85, 0xa5, 0x06, 0x00, 0x00, //0x00001637 jne          LBB0_349
	0x8d, 0x5e, 0xff, //0x0000163d leal         $-1(%rsi), %ebx
	0x21, 0xf3, //0x00001640 andl         %esi, %ebx
	0x0f, 0x85, 0x9a, 0x06, 0x00, 0x00, //0x00001642 jne          LBB0_349
	0x85, 0xff, //0x00001648 testl        %edi, %edi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000164a je           LBB0_281
	0x4c, 0x89, 0xd0, //0x00001650 movq         %r10, %rax
	0x4c, 0x29, 0xc8, //0x00001653 subq         %r9, %rax
	0x0f, 0xbc, 0xff, //0x00001656 bsfl         %edi, %edi
	0x48, 0x01, 0xc7, //0x00001659 addq         %rax, %rdi
	0x49, 0x83, 0xfc, 0xff, //0x0000165c cmpq         $-1, %r12
	0x0f, 0x85, 0xa7, 0x07, 0x00, 0x00, //0x00001660 jne          LBB0_354
	0x49, 0x89, 0xfc, //0x00001666 movq         %rdi, %r12
	//0x00001669 LBB0_281
	0x85, 0xd2, //0x00001669 testl        %edx, %edx
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000166b je           LBB0_284
	0x4c, 0x89, 0xd0, //0x00001671 movq         %r10, %rax
	0x4c, 0x29, 0xc8, //0x00001674 subq         %r9, %rax
	0x0f, 0xbc, 0xd2, //0x00001677 bsfl         %edx, %edx
	0x48, 0x01, 0xc2, //0x0000167a addq         %rax, %rdx
	0x49, 0x83, 0xfb, 0xff, //0x0000167d cmpq         $-1, %r11
	0x0f, 0x85, 0x80, 0x06, 0x00, 0x00, //0x00001681 jne          LBB0_351
	0x49, 0x89, 0xd3, //0x00001687 movq         %rdx, %r11
	//0x0000168a LBB0_284
	0x85, 0xf6, //0x0000168a testl        %esi, %esi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000168c je           LBB0_287
	0x4c, 0x89, 0xd0, //0x00001692 movq         %r10, %rax
	0x4c, 0x29, 0xc8, //0x00001695 subq         %r9, %rax
	0x0f, 0xbc, 0xd6, //0x00001698 bsfl         %esi, %edx
	0x48, 0x01, 0xc2, //0x0000169b addq         %rax, %rdx
	0x49, 0x83, 0xf8, 0xff, //0x0000169e cmpq         $-1, %r8
	0x0f, 0x85, 0x5f, 0x06, 0x00, 0x00, //0x000016a2 jne          LBB0_351
	0x49, 0x89, 0xd0, //0x000016a8 movq         %rdx, %r8
	//0x000016ab LBB0_287
	0x83, 0xf9, 0x20, //0x000016ab cmpl         $32, %ecx
	0x0f, 0x85, 0x62, 0x02, 0x00, 0x00, //0x000016ae jne          LBB0_496
	0x49, 0x83, 0xc2, 0x20, //0x000016b4 addq         $32, %r10
	0x49, 0x83, 0xc6, 0xe0, //0x000016b8 addq         $-32, %r14
	0x49, 0x83, 0xfe, 0x1f, //0x000016bc cmpq         $31, %r14
	0x0f, 0x87, 0xfa, 0xfe, 0xff, 0xff, //0x000016c0 ja           LBB0_273
	0xc5, 0xf8, 0x77, //0x000016c6 vzeroupper   
	0x4c, 0x8b, 0x75, 0xc0, //0x000016c9 movq         $-64(%rbp), %r14
	//0x000016cd LBB0_290
	0x49, 0x83, 0xfe, 0x10, //0x000016cd cmpq         $16, %r14
	0xc5, 0xfe, 0x6f, 0x2d, 0xa7, 0xe9, 0xff, 0xff, //0x000016d1 vmovdqu      $-5721(%rip), %ymm5  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0xbf, 0xe9, 0xff, 0xff, //0x000016d9 vmovdqu      $-5697(%rip), %ymm6  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0xd7, 0xe9, 0xff, 0xff, //0x000016e1 vmovdqu      $-5673(%rip), %ymm7  /* LCPI0_3+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0xef, 0xe9, 0xff, 0xff, //0x000016e9 vmovdqu      $-5649(%rip), %ymm8  /* LCPI0_4+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000016f1 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x15, 0x02, 0xea, 0xff, 0xff, //0x000016f6 vmovdqu      $-5630(%rip), %ymm10  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x1a, 0xea, 0xff, 0xff, //0x000016fe vmovdqu      $-5606(%rip), %ymm11  /* LCPI0_6+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x32, 0xea, 0xff, 0xff, //0x00001706 vmovdqu      $-5582(%rip), %ymm12  /* LCPI0_7+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x4a, 0xea, 0xff, 0xff, //0x0000170e vmovdqu      $-5558(%rip), %ymm13  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x62, 0xea, 0xff, 0xff, //0x00001716 vmovdqu      $-5534(%rip), %ymm14  /* LCPI0_9+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0x7a, 0xea, 0xff, 0xff, //0x0000171e vmovdqu      $-5510(%rip), %ymm15  /* LCPI0_10+0(%rip) */
	0x0f, 0x82, 0x55, 0x01, 0x00, 0x00, //0x00001726 jb           LBB0_309
	0x4d, 0x8d, 0x56, 0xf0, //0x0000172c leaq         $-16(%r14), %r10
	0x4c, 0x89, 0xd0, //0x00001730 movq         %r10, %rax
	0x48, 0x83, 0xe0, 0xf0, //0x00001733 andq         $-16, %rax
	0x4a, 0x8d, 0x44, 0x38, 0x10, //0x00001737 leaq         $16(%rax,%r15), %rax
	0x48, 0x89, 0x45, 0xc0, //0x0000173c movq         %rax, $-64(%rbp)
	0x41, 0x83, 0xe2, 0x0f, //0x00001740 andl         $15, %r10d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001744 .p2align 4, 0x90
	//0x00001750 LBB0_292
	0xc4, 0xc1, 0x7a, 0x6f, 0x07, //0x00001750 vmovdqu      (%r15), %xmm0
	0xc5, 0xf9, 0x64, 0x0d, 0xb3, 0xe8, 0xff, 0xff, //0x00001755 vpcmpgtb     $-5965(%rip), %xmm0, %xmm1  /* LCPI0_11+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x15, 0xbb, 0xe8, 0xff, 0xff, //0x0000175d vmovdqu      $-5957(%rip), %xmm2  /* LCPI0_12+0(%rip) */
	0xc5, 0xe9, 0x64, 0xd0, //0x00001765 vpcmpgtb     %xmm0, %xmm2, %xmm2
	0xc5, 0xf1, 0xdb, 0xca, //0x00001769 vpand        %xmm2, %xmm1, %xmm1
	0xc5, 0xf9, 0x74, 0x15, 0xbb, 0xe8, 0xff, 0xff, //0x0000176d vpcmpeqb     $-5957(%rip), %xmm0, %xmm2  /* LCPI0_13+0(%rip) */
	0xc5, 0xf9, 0x74, 0x1d, 0xc3, 0xe8, 0xff, 0xff, //0x00001775 vpcmpeqb     $-5949(%rip), %xmm0, %xmm3  /* LCPI0_14+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xd2, //0x0000177d vpor         %xmm2, %xmm3, %xmm2
	0xc5, 0xf9, 0xeb, 0x1d, 0xc7, 0xe8, 0xff, 0xff, //0x00001781 vpor         $-5945(%rip), %xmm0, %xmm3  /* LCPI0_15+0(%rip) */
	0xc5, 0xf9, 0x74, 0x05, 0xcf, 0xe8, 0xff, 0xff, //0x00001789 vpcmpeqb     $-5937(%rip), %xmm0, %xmm0  /* LCPI0_16+0(%rip) */
	0xc5, 0xe1, 0x74, 0x1d, 0xd7, 0xe8, 0xff, 0xff, //0x00001791 vpcmpeqb     $-5929(%rip), %xmm3, %xmm3  /* LCPI0_17+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xe0, //0x00001799 vpor         %xmm0, %xmm3, %xmm4
	0xc5, 0xe9, 0xeb, 0xc9, //0x0000179d vpor         %xmm1, %xmm2, %xmm1
	0xc5, 0xd9, 0xeb, 0xc9, //0x000017a1 vpor         %xmm1, %xmm4, %xmm1
	0xc5, 0xf9, 0xd7, 0xf8, //0x000017a5 vpmovmskb    %xmm0, %edi
	0xc5, 0xf9, 0xd7, 0xf3, //0x000017a9 vpmovmskb    %xmm3, %esi
	0xc5, 0xf9, 0xd7, 0xd2, //0x000017ad vpmovmskb    %xmm2, %edx
	0xc5, 0xf9, 0xd7, 0xc1, //0x000017b1 vpmovmskb    %xmm1, %eax
	0xb9, 0xff, 0xff, 0xff, 0xff, //0x000017b5 movl         $4294967295, %ecx
	0x48, 0x31, 0xc8, //0x000017ba xorq         %rcx, %rax
	0x48, 0x0f, 0xbc, 0xc8, //0x000017bd bsfq         %rax, %rcx
	0x83, 0xf9, 0x10, //0x000017c1 cmpl         $16, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x000017c4 je           LBB0_294
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x000017ca movl         $-1, %eax
	0xd3, 0xe0, //0x000017cf shll         %cl, %eax
	0xf7, 0xd0, //0x000017d1 notl         %eax
	0x21, 0xc7, //0x000017d3 andl         %eax, %edi
	0x21, 0xc6, //0x000017d5 andl         %eax, %esi
	0x21, 0xd0, //0x000017d7 andl         %edx, %eax
	0x89, 0xc2, //0x000017d9 movl         %eax, %edx
	//0x000017db LBB0_294
	0x8d, 0x5f, 0xff, //0x000017db leal         $-1(%rdi), %ebx
	0x21, 0xfb, //0x000017de andl         %edi, %ebx
	0x0f, 0x85, 0x0f, 0x06, 0x00, 0x00, //0x000017e0 jne          LBB0_353
	0x8d, 0x5e, 0xff, //0x000017e6 leal         $-1(%rsi), %ebx
	0x21, 0xf3, //0x000017e9 andl         %esi, %ebx
	0x0f, 0x85, 0x04, 0x06, 0x00, 0x00, //0x000017eb jne          LBB0_353
	0x8d, 0x5a, 0xff, //0x000017f1 leal         $-1(%rdx), %ebx
	0x21, 0xd3, //0x000017f4 andl         %edx, %ebx
	0x0f, 0x85, 0xf9, 0x05, 0x00, 0x00, //0x000017f6 jne          LBB0_353
	0x85, 0xff, //0x000017fc testl        %edi, %edi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x000017fe je           LBB0_300
	0x4c, 0x89, 0xf8, //0x00001804 movq         %r15, %rax
	0x4c, 0x29, 0xc8, //0x00001807 subq         %r9, %rax
	0x0f, 0xbc, 0xff, //0x0000180a bsfl         %edi, %edi
	0x48, 0x01, 0xc7, //0x0000180d addq         %rax, %rdi
	0x49, 0x83, 0xfc, 0xff, //0x00001810 cmpq         $-1, %r12
	0x0f, 0x85, 0xf3, 0x05, 0x00, 0x00, //0x00001814 jne          LBB0_354
	0x49, 0x89, 0xfc, //0x0000181a movq         %rdi, %r12
	//0x0000181d LBB0_300
	0x85, 0xf6, //0x0000181d testl        %esi, %esi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000181f je           LBB0_303
	0x4c, 0x89, 0xf8, //0x00001825 movq         %r15, %rax
	0x4c, 0x29, 0xc8, //0x00001828 subq         %r9, %rax
	0x0f, 0xbc, 0xf6, //0x0000182b bsfl         %esi, %esi
	0x48, 0x01, 0xc6, //0x0000182e addq         %rax, %rsi
	0x49, 0x83, 0xfb, 0xff, //0x00001831 cmpq         $-1, %r11
	0x0f, 0x85, 0x4e, 0x06, 0x00, 0x00, //0x00001835 jne          LBB0_359
	0x49, 0x89, 0xf3, //0x0000183b movq         %rsi, %r11
	//0x0000183e LBB0_303
	0x85, 0xd2, //0x0000183e testl        %edx, %edx
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x00001840 je           LBB0_306
	0x4c, 0x89, 0xf8, //0x00001846 movq         %r15, %rax
	0x4c, 0x29, 0xc8, //0x00001849 subq         %r9, %rax
	0x0f, 0xbc, 0xd2, //0x0000184c bsfl         %edx, %edx
	0x48, 0x01, 0xc2, //0x0000184f addq         %rax, %rdx
	0x49, 0x83, 0xf8, 0xff, //0x00001852 cmpq         $-1, %r8
	0x0f, 0x85, 0xab, 0x04, 0x00, 0x00, //0x00001856 jne          LBB0_351
	0x49, 0x89, 0xd0, //0x0000185c movq         %rdx, %r8
	//0x0000185f LBB0_306
	0x83, 0xf9, 0x10, //0x0000185f cmpl         $16, %ecx
	0x0f, 0x85, 0x05, 0x01, 0x00, 0x00, //0x00001862 jne          LBB0_325
	0x49, 0x83, 0xc7, 0x10, //0x00001868 addq         $16, %r15
	0x49, 0x83, 0xc6, 0xf0, //0x0000186c addq         $-16, %r14
	0x49, 0x83, 0xfe, 0x0f, //0x00001870 cmpq         $15, %r14
	0x0f, 0x87, 0xd6, 0xfe, 0xff, 0xff, //0x00001874 ja           LBB0_292
	0x4d, 0x89, 0xd6, //0x0000187a movq         %r10, %r14
	0x4c, 0x8b, 0x7d, 0xc0, //0x0000187d movq         $-64(%rbp), %r15
	//0x00001881 LBB0_309
	0x4d, 0x85, 0xf6, //0x00001881 testq        %r14, %r14
	0x4c, 0x8b, 0x55, 0xd0, //0x00001884 movq         $-48(%rbp), %r10
	0x0f, 0x84, 0xe6, 0x00, 0x00, 0x00, //0x00001888 je           LBB0_326
	0x4b, 0x8d, 0x0c, 0x37, //0x0000188e leaq         (%r15,%r14), %rcx
	0xe9, 0x25, 0x00, 0x00, 0x00, //0x00001892 jmp          LBB0_314
	//0x00001897 LBB0_311
	0x49, 0x89, 0xd7, //0x00001897 movq         %rdx, %r15
	0x4d, 0x29, 0xcf, //0x0000189a subq         %r9, %r15
	0x49, 0x83, 0xf8, 0xff, //0x0000189d cmpq         $-1, %r8
	0x0f, 0x85, 0x26, 0x06, 0x00, 0x00, //0x000018a1 jne          LBB0_365
	0x49, 0xff, 0xcf, //0x000018a7 decq         %r15
	0x4d, 0x89, 0xf8, //0x000018aa movq         %r15, %r8
	0x90, 0x90, 0x90, //0x000018ad .p2align 4, 0x90
	//0x000018b0 LBB0_313
	0x49, 0x89, 0xd7, //0x000018b0 movq         %rdx, %r15
	0x49, 0xff, 0xce, //0x000018b3 decq         %r14
	0x0f, 0x84, 0xbc, 0x05, 0x00, 0x00, //0x000018b6 je           LBB0_358
	//0x000018bc LBB0_314
	0x41, 0x0f, 0xbe, 0x37, //0x000018bc movsbl       (%r15), %esi
	0x83, 0xc6, 0xd5, //0x000018c0 addl         $-43, %esi
	0x83, 0xfe, 0x3a, //0x000018c3 cmpl         $58, %esi
	0x0f, 0x87, 0xa8, 0x00, 0x00, 0x00, //0x000018c6 ja           LBB0_326
	0x49, 0x8d, 0x57, 0x01, //0x000018cc leaq         $1(%r15), %rdx
	0x48, 0x8d, 0x3d, 0x49, 0x13, 0x00, 0x00, //0x000018d0 leaq         $4937(%rip), %rdi  /* LJTI0_2+0(%rip) */
	0x48, 0x63, 0x04, 0xb7, //0x000018d7 movslq       (%rdi,%rsi,4), %rax
	0x48, 0x01, 0xf8, //0x000018db addq         %rdi, %rax
	0xff, 0xe0, //0x000018de jmpq         *%rax
	//0x000018e0 LBB0_316
	0x49, 0x89, 0xd7, //0x000018e0 movq         %rdx, %r15
	0x4d, 0x29, 0xcf, //0x000018e3 subq         %r9, %r15
	0x49, 0x83, 0xfb, 0xff, //0x000018e6 cmpq         $-1, %r11
	0x0f, 0x85, 0xdd, 0x05, 0x00, 0x00, //0x000018ea jne          LBB0_365
	0x49, 0xff, 0xcf, //0x000018f0 decq         %r15
	0x4d, 0x89, 0xfb, //0x000018f3 movq         %r15, %r11
	0xe9, 0xb5, 0xff, 0xff, 0xff, //0x000018f6 jmp          LBB0_313
	//0x000018fb LBB0_318
	0x49, 0x89, 0xd7, //0x000018fb movq         %rdx, %r15
	0x4d, 0x29, 0xcf, //0x000018fe subq         %r9, %r15
	0x49, 0x83, 0xfc, 0xff, //0x00001901 cmpq         $-1, %r12
	0x0f, 0x85, 0xc2, 0x05, 0x00, 0x00, //0x00001905 jne          LBB0_365
	0x49, 0xff, 0xcf, //0x0000190b decq         %r15
	0x4d, 0x89, 0xfc, //0x0000190e movq         %r15, %r12
	0xe9, 0x9a, 0xff, 0xff, 0xff, //0x00001911 jmp          LBB0_313
	//0x00001916 LBB0_496
	0x49, 0x01, 0xca, //0x00001916 addq         %rcx, %r10
	0xc5, 0xf8, 0x77, //0x00001919 vzeroupper   
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000191c vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x3d, 0x97, 0xe7, 0xff, 0xff, //0x00001921 vmovdqu      $-6249(%rip), %ymm7  /* LCPI0_3+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x6f, 0xe7, 0xff, 0xff, //0x00001929 vmovdqu      $-6289(%rip), %ymm6  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x2d, 0x47, 0xe7, 0xff, 0xff, //0x00001931 vmovdqu      $-6329(%rip), %ymm5  /* LCPI0_1+0(%rip) */
	0x4d, 0x89, 0xd7, //0x00001939 movq         %r10, %r15
	0x4c, 0x8b, 0x55, 0xd0, //0x0000193c movq         $-48(%rbp), %r10
	0x4d, 0x85, 0xdb, //0x00001940 testq        %r11, %r11
	0x0f, 0x85, 0x34, 0x00, 0x00, 0x00, //0x00001943 jne          LBB0_327
	0xe9, 0x10, 0x10, 0x00, 0x00, //0x00001949 jmp          LBB0_497
	//0x0000194e LBB0_320
	0xba, 0x40, 0x00, 0x00, 0x00, //0x0000194e movl         $64, %edx
	//0x00001953 LBB0_321
	0x4c, 0x8b, 0x75, 0xc8, //0x00001953 movq         $-56(%rbp), %r14
	0x49, 0x29, 0xc4, //0x00001957 subq         %rax, %r12
	0x48, 0x39, 0xca, //0x0000195a cmpq         %rcx, %rdx
	0x0f, 0x82, 0x4d, 0x10, 0x00, 0x00, //0x0000195d jb           LBB0_129
	//0x00001963 LBB0_322
	0x4d, 0x8d, 0x64, 0x0c, 0x01, //0x00001963 leaq         $1(%r12,%rcx), %r12
	0xe9, 0xfd, 0x03, 0x00, 0x00, //0x00001968 jmp          LBB0_323
	//0x0000196d LBB0_325
	0x49, 0x01, 0xcf, //0x0000196d addq         %rcx, %r15
	0x4c, 0x8b, 0x55, 0xd0, //0x00001970 movq         $-48(%rbp), %r10
	//0x00001974 LBB0_326
	0x4d, 0x85, 0xdb, //0x00001974 testq        %r11, %r11
	0x0f, 0x84, 0xe1, 0x0f, 0x00, 0x00, //0x00001977 je           LBB0_497
	//0x0000197d LBB0_327
	0x4d, 0x85, 0xc0, //0x0000197d testq        %r8, %r8
	0x0f, 0x84, 0xd8, 0x0f, 0x00, 0x00, //0x00001980 je           LBB0_497
	0x4d, 0x85, 0xe4, //0x00001986 testq        %r12, %r12
	0x0f, 0x84, 0xcf, 0x0f, 0x00, 0x00, //0x00001989 je           LBB0_497
	0x4d, 0x29, 0xcf, //0x0000198f subq         %r9, %r15
	0x49, 0x8d, 0x4f, 0xff, //0x00001992 leaq         $-1(%r15), %rcx
	0x49, 0x39, 0xcb, //0x00001996 cmpq         %rcx, %r11
	0x0f, 0x84, 0x6b, 0x00, 0x00, 0x00, //0x00001999 je           LBB0_335
	0x49, 0x39, 0xcc, //0x0000199f cmpq         %rcx, %r12
	0x0f, 0x84, 0x62, 0x00, 0x00, 0x00, //0x000019a2 je           LBB0_335
	0x49, 0x39, 0xc8, //0x000019a8 cmpq         %rcx, %r8
	0x0f, 0x84, 0x59, 0x00, 0x00, 0x00, //0x000019ab je           LBB0_335
	0x4d, 0x85, 0xc0, //0x000019b1 testq        %r8, %r8
	0xc5, 0x7e, 0x6f, 0x05, 0x24, 0xe7, 0xff, 0xff, //0x000019b4 vmovdqu      $-6364(%rip), %ymm8  /* LCPI0_4+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0x3c, 0xe7, 0xff, 0xff, //0x000019bc vmovdqu      $-6340(%rip), %ymm10  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x54, 0xe7, 0xff, 0xff, //0x000019c4 vmovdqu      $-6316(%rip), %ymm11  /* LCPI0_6+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x6c, 0xe7, 0xff, 0xff, //0x000019cc vmovdqu      $-6292(%rip), %ymm12  /* LCPI0_7+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x84, 0xe7, 0xff, 0xff, //0x000019d4 vmovdqu      $-6268(%rip), %ymm13  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x9c, 0xe7, 0xff, 0xff, //0x000019dc vmovdqu      $-6244(%rip), %ymm14  /* LCPI0_9+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0xb4, 0xe7, 0xff, 0xff, //0x000019e4 vmovdqu      $-6220(%rip), %ymm15  /* LCPI0_10+0(%rip) */
	0x0f, 0x8e, 0x58, 0x00, 0x00, 0x00, //0x000019ec jle          LBB0_336
	0x49, 0x8d, 0x40, 0xff, //0x000019f2 leaq         $-1(%r8), %rax
	0x49, 0x39, 0xc3, //0x000019f6 cmpq         %rax, %r11
	0x0f, 0x84, 0x4b, 0x00, 0x00, 0x00, //0x000019f9 je           LBB0_336
	0x49, 0xf7, 0xd0, //0x000019ff notq         %r8
	0x4d, 0x89, 0xc7, //0x00001a02 movq         %r8, %r15
	0xe9, 0x64, 0x01, 0x00, 0x00, //0x00001a05 jmp          LBB0_342
	//0x00001a0a LBB0_335
	0x49, 0xf7, 0xdf, //0x00001a0a negq         %r15
	0xc5, 0x7e, 0x6f, 0x05, 0xcb, 0xe6, 0xff, 0xff, //0x00001a0d vmovdqu      $-6453(%rip), %ymm8  /* LCPI0_4+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0xe3, 0xe6, 0xff, 0xff, //0x00001a15 vmovdqu      $-6429(%rip), %ymm10  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0xfb, 0xe6, 0xff, 0xff, //0x00001a1d vmovdqu      $-6405(%rip), %ymm11  /* LCPI0_6+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x13, 0xe7, 0xff, 0xff, //0x00001a25 vmovdqu      $-6381(%rip), %ymm12  /* LCPI0_7+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x2b, 0xe7, 0xff, 0xff, //0x00001a2d vmovdqu      $-6357(%rip), %ymm13  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x43, 0xe7, 0xff, 0xff, //0x00001a35 vmovdqu      $-6333(%rip), %ymm14  /* LCPI0_9+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0x5b, 0xe7, 0xff, 0xff, //0x00001a3d vmovdqu      $-6309(%rip), %ymm15  /* LCPI0_10+0(%rip) */
	0xe9, 0x24, 0x01, 0x00, 0x00, //0x00001a45 jmp          LBB0_342
	//0x00001a4a LBB0_336
	0x4c, 0x89, 0xe1, //0x00001a4a movq         %r12, %rcx
	0x4c, 0x09, 0xd9, //0x00001a4d orq          %r11, %rcx
	0x4d, 0x39, 0xdc, //0x00001a50 cmpq         %r11, %r12
	0x0f, 0x8c, 0x00, 0x01, 0x00, 0x00, //0x00001a53 jl           LBB0_341
	0x48, 0x85, 0xc9, //0x00001a59 testq        %rcx, %rcx
	0x0f, 0x88, 0xf7, 0x00, 0x00, 0x00, //0x00001a5c js           LBB0_341
	0x49, 0xf7, 0xd4, //0x00001a62 notq         %r12
	0x4d, 0x89, 0xe7, //0x00001a65 movq         %r12, %r15
	0xe9, 0x01, 0x01, 0x00, 0x00, //0x00001a68 jmp          LBB0_342
	//0x00001a6d LBB0_339
	0x4d, 0x29, 0xfb, //0x00001a6d subq         %r15, %r11
	0x44, 0x0f, 0xbc, 0xf3, //0x00001a70 bsfl         %ebx, %r14d
	0xe9, 0x3d, 0x01, 0x00, 0x00, //0x00001a74 jmp          LBB0_346
	//0x00001a79 LBB0_54
	0x4c, 0x89, 0xf9, //0x00001a79 movq         %r15, %rcx
	0x4c, 0x89, 0xcb, //0x00001a7c movq         %r9, %rbx
	0x48, 0x83, 0xf9, 0x20, //0x00001a7f cmpq         $32, %rcx
	0x0f, 0x82, 0x0e, 0x05, 0x00, 0x00, //0x00001a83 jb           LBB0_370
	//0x00001a89 LBB0_55
	0xc5, 0xfe, 0x6f, 0x03, //0x00001a89 vmovdqu      (%rbx), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001a8d vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xd1, //0x00001a91 vpmovmskb    %ymm1, %edx
	0xc5, 0xfd, 0x74, 0xc7, //0x00001a95 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00001a99 vpmovmskb    %ymm0, %esi
	0x85, 0xf6, //0x00001a9d testl        %esi, %esi
	0x0f, 0x85, 0x89, 0x04, 0x00, 0x00, //0x00001a9f jne          LBB0_366
	0x4d, 0x85, 0xf6, //0x00001aa5 testq        %r14, %r14
	0x0f, 0x85, 0x97, 0x04, 0x00, 0x00, //0x00001aa8 jne          LBB0_368
	0x45, 0x31, 0xf6, //0x00001aae xorl         %r14d, %r14d
	0x48, 0x85, 0xd2, //0x00001ab1 testq        %rdx, %rdx
	0x0f, 0x84, 0xd5, 0x04, 0x00, 0x00, //0x00001ab4 je           LBB0_369
	//0x00001aba LBB0_60
	0x48, 0x0f, 0xbc, 0xc2, //0x00001aba bsfq         %rdx, %rax
	0x4c, 0x29, 0xdb, //0x00001abe subq         %r11, %rbx
	0x4c, 0x8d, 0x64, 0x03, 0x01, //0x00001ac1 leaq         $1(%rbx,%rax), %r12
	0x4c, 0x8b, 0x75, 0xc8, //0x00001ac6 movq         $-56(%rbp), %r14
	//0x00001aca LBB0_184
	0x4d, 0x85, 0xe4, //0x00001aca testq        %r12, %r12
	0x0f, 0x88, 0xbb, 0x0c, 0x00, 0x00, //0x00001acd js           LBB0_475
	0x4d, 0x89, 0x22, //0x00001ad3 movq         %r12, (%r10)
	0x4c, 0x89, 0xe9, //0x00001ad6 movq         %r13, %rcx
	0x48, 0x83, 0x7d, 0xa8, 0x00, //0x00001ad9 cmpq         $0, $-88(%rbp)
	0x0f, 0x8f, 0x9c, 0xe7, 0xff, 0xff, //0x00001ade jg           LBB0_3
	0xe9, 0x00, 0x0d, 0x00, 0x00, //0x00001ae4 jmp          LBB0_501
	//0x00001ae9 LBB0_84
	0x4d, 0x89, 0xf2, //0x00001ae9 movq         %r14, %r10
	0x49, 0x89, 0xc4, //0x00001aec movq         %rax, %r12
	0x4c, 0x8b, 0x75, 0xc8, //0x00001aef movq         $-56(%rbp), %r14
	0x49, 0x83, 0xfa, 0x20, //0x00001af3 cmpq         $32, %r10
	0x0f, 0x82, 0xd2, 0x05, 0x00, 0x00, //0x00001af7 jb           LBB0_388
	//0x00001afd LBB0_85
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x00001afd vmovdqu      (%r12), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001b03 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x00001b07 vpmovmskb    %ymm1, %esi
	0xc5, 0xfd, 0x74, 0xcf, //0x00001b0b vpcmpeqb     %ymm7, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xc9, //0x00001b0f vpmovmskb    %ymm1, %ecx
	0xc5, 0xbd, 0x64, 0xc8, //0x00001b13 vpcmpgtb     %ymm0, %ymm8, %ymm1
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x00001b17 vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xfd, 0xdb, 0xc1, //0x00001b1c vpand        %ymm1, %ymm0, %ymm0
	0xc5, 0x7d, 0xd7, 0xc8, //0x00001b20 vpmovmskb    %ymm0, %r9d
	0x85, 0xc9, //0x00001b24 testl        %ecx, %ecx
	0x0f, 0x85, 0xfa, 0x04, 0x00, 0x00, //0x00001b26 jne          LBB0_379
	0x4d, 0x85, 0xff, //0x00001b2c testq        %r15, %r15
	0x0f, 0x85, 0x09, 0x05, 0x00, 0x00, //0x00001b2f jne          LBB0_381
	0x45, 0x31, 0xff, //0x00001b35 xorl         %r15d, %r15d
	0x48, 0x85, 0xf6, //0x00001b38 testq        %rsi, %rsi
	0x0f, 0x84, 0x44, 0x05, 0x00, 0x00, //0x00001b3b je           LBB0_382
	//0x00001b41 LBB0_88
	0x48, 0x0f, 0xbc, 0xce, //0x00001b41 bsfq         %rsi, %rcx
	0xe9, 0x40, 0x05, 0x00, 0x00, //0x00001b45 jmp          LBB0_383
	//0x00001b4a LBB0_340
	0x48, 0xf7, 0xd2, //0x00001b4a notq         %rdx
	0x49, 0x89, 0xd6, //0x00001b4d movq         %rdx, %r14
	0x48, 0x8b, 0x55, 0xd0, //0x00001b50 movq         $-48(%rbp), %rdx
	0xe9, 0x7a, 0xf5, 0xff, 0xff, //0x00001b54 jmp          LBB0_197
	//0x00001b59 LBB0_341
	0x48, 0x85, 0xc9, //0x00001b59 testq        %rcx, %rcx
	0x49, 0x8d, 0x43, 0xff, //0x00001b5c leaq         $-1(%r11), %rax
	0x49, 0xf7, 0xd3, //0x00001b60 notq         %r11
	0x4d, 0x0f, 0x48, 0xdf, //0x00001b63 cmovsq       %r15, %r11
	0x49, 0x39, 0xc4, //0x00001b67 cmpq         %rax, %r12
	0x4d, 0x0f, 0x44, 0xfb, //0x00001b6a cmoveq       %r11, %r15
	//0x00001b6e LBB0_342
	0x4d, 0x8b, 0x22, //0x00001b6e movq         (%r10), %r12
	0x4d, 0x85, 0xff, //0x00001b71 testq        %r15, %r15
	0x0f, 0x88, 0xee, 0x0d, 0x00, 0x00, //0x00001b74 js           LBB0_499
	//0x00001b7a LBB0_343
	0x4d, 0x01, 0xfc, //0x00001b7a addq         %r15, %r12
	0x4d, 0x89, 0x22, //0x00001b7d movq         %r12, (%r10)
	0x4c, 0x89, 0xe9, //0x00001b80 movq         %r13, %rcx
	0x48, 0x83, 0x7d, 0xa8, 0x00, //0x00001b83 cmpq         $0, $-88(%rbp)
	0x4c, 0x8b, 0x75, 0xc8, //0x00001b88 movq         $-56(%rbp), %r14
	0x0f, 0x8f, 0xee, 0xe6, 0xff, 0xff, //0x00001b8c jg           LBB0_3
	0xe9, 0x52, 0x0c, 0x00, 0x00, //0x00001b92 jmp          LBB0_501
	//0x00001b97 LBB0_344
	0x4d, 0x29, 0xfe, //0x00001b97 subq         %r15, %r14
	0x0f, 0xbc, 0xc3, //0x00001b9a bsfl         %ebx, %eax
	0x4c, 0x01, 0xf0, //0x00001b9d addq         %r14, %rax
	0x48, 0xf7, 0xd0, //0x00001ba0 notq         %rax
	0x49, 0x89, 0xc6, //0x00001ba3 movq         %rax, %r14
	0x48, 0x8b, 0x55, 0xd0, //0x00001ba6 movq         $-48(%rbp), %rdx
	0xe9, 0x24, 0xf5, 0xff, 0xff, //0x00001baa jmp          LBB0_197
	//0x00001baf LBB0_345
	0x4d, 0x29, 0xfb, //0x00001baf subq         %r15, %r11
	0x45, 0x0f, 0xbc, 0xf1, //0x00001bb2 bsfl         %r9d, %r14d
	//0x00001bb6 LBB0_346
	0x4d, 0x01, 0xde, //0x00001bb6 addq         %r11, %r14
	0x49, 0xf7, 0xd6, //0x00001bb9 notq         %r14
	0x48, 0x8b, 0x55, 0xd0, //0x00001bbc movq         $-48(%rbp), %rdx
	0xe9, 0x0e, 0xf5, 0xff, 0xff, //0x00001bc0 jmp          LBB0_197
	//0x00001bc5 LBB0_347
	0x48, 0xf7, 0xd7, //0x00001bc5 notq         %rdi
	0x49, 0x89, 0xfe, //0x00001bc8 movq         %rdi, %r14
	0x48, 0x8b, 0x55, 0xd0, //0x00001bcb movq         $-48(%rbp), %rdx
	0xe9, 0xff, 0xf4, 0xff, 0xff, //0x00001bcf jmp          LBB0_197
	//0x00001bd4 LBB0_101
	0x4c, 0x89, 0xf9, //0x00001bd4 movq         %r15, %rcx
	0x4c, 0x89, 0xc3, //0x00001bd7 movq         %r8, %rbx
	0x48, 0x83, 0xf9, 0x20, //0x00001bda cmpq         $32, %rcx
	0x0f, 0x82, 0x02, 0x06, 0x00, 0x00, //0x00001bde jb           LBB0_402
	//0x00001be4 LBB0_102
	0xc5, 0xfe, 0x6f, 0x03, //0x00001be4 vmovdqu      (%rbx), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001be8 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xd1, //0x00001bec vpmovmskb    %ymm1, %edx
	0xc5, 0xfd, 0x74, 0xc7, //0x00001bf0 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00001bf4 vpmovmskb    %ymm0, %esi
	0x85, 0xf6, //0x00001bf8 testl        %esi, %esi
	0x0f, 0x85, 0x6f, 0x05, 0x00, 0x00, //0x00001bfa jne          LBB0_397
	0x4d, 0x85, 0xf6, //0x00001c00 testq        %r14, %r14
	0x0f, 0x85, 0x85, 0x05, 0x00, 0x00, //0x00001c03 jne          LBB0_399
	0x45, 0x31, 0xf6, //0x00001c09 xorl         %r14d, %r14d
	0x48, 0x85, 0xd2, //0x00001c0c testq        %rdx, %rdx
	0x0f, 0x84, 0xc9, 0x05, 0x00, 0x00, //0x00001c0f je           LBB0_401
	//0x00001c15 LBB0_107
	0x4d, 0x89, 0xe0, //0x00001c15 movq         %r12, %r8
	0x48, 0x0f, 0xbc, 0xc2, //0x00001c18 bsfq         %rdx, %rax
	0x4c, 0x29, 0xcb, //0x00001c1c subq         %r9, %rbx
	0x4c, 0x8d, 0x64, 0x03, 0x01, //0x00001c1f leaq         $1(%rbx,%rax), %r12
	0x4c, 0x8b, 0x75, 0xc8, //0x00001c24 movq         $-56(%rbp), %r14
	//0x00001c28 LBB0_205
	0x4d, 0x85, 0xe4, //0x00001c28 testq        %r12, %r12
	0x0f, 0x88, 0x87, 0x0b, 0x00, 0x00, //0x00001c2b js           LBB0_478
	0x4d, 0x89, 0x22, //0x00001c31 movq         %r12, (%r10)
	0x4c, 0x89, 0xe9, //0x00001c34 movq         %r13, %rcx
	0x4d, 0x85, 0xc0, //0x00001c37 testq        %r8, %r8
	0x0f, 0x8e, 0xa9, 0x0b, 0x00, 0x00, //0x00001c3a jle          LBB0_501
	0x49, 0x8b, 0x06, //0x00001c40 movq         (%r14), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00001c43 cmpq         $4095, %rax
	0x0f, 0x8f, 0x33, 0x0b, 0x00, 0x00, //0x00001c49 jg           LBB0_489
	0x48, 0x8d, 0x48, 0x01, //0x00001c4f leaq         $1(%rax), %rcx
	0x49, 0x89, 0x0e, //0x00001c53 movq         %rcx, (%r14)
	0x49, 0xc7, 0x44, 0xc6, 0x08, 0x04, 0x00, 0x00, 0x00, //0x00001c56 movq         $4, $8(%r14,%rax,8)
	0xe9, 0x1c, 0xe6, 0xff, 0xff, //0x00001c5f jmp          LBB0_3
	//0x00001c64 LBB0_120
	0x4c, 0x89, 0xf3, //0x00001c64 movq         %r14, %rbx
	0x4c, 0x8b, 0x65, 0xa0, //0x00001c67 movq         $-96(%rbp), %r12
	0x4c, 0x8b, 0x75, 0xc8, //0x00001c6b movq         $-56(%rbp), %r14
	0x48, 0x83, 0xfb, 0x20, //0x00001c6f cmpq         $32, %rbx
	0x0f, 0x82, 0xa3, 0x06, 0x00, 0x00, //0x00001c73 jb           LBB0_420
	//0x00001c79 LBB0_121
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x00001c79 vmovdqu      (%r12), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001c7f vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x00001c83 vpmovmskb    %ymm1, %esi
	0xc5, 0xfd, 0x74, 0xcf, //0x00001c87 vpcmpeqb     %ymm7, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xc9, //0x00001c8b vpmovmskb    %ymm1, %ecx
	0xc5, 0xbd, 0x64, 0xc8, //0x00001c8f vpcmpgtb     %ymm0, %ymm8, %ymm1
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x00001c93 vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xfd, 0xdb, 0xc1, //0x00001c98 vpand        %ymm1, %ymm0, %ymm0
	0xc5, 0x7d, 0xd7, 0xc8, //0x00001c9c vpmovmskb    %ymm0, %r9d
	0x85, 0xc9, //0x00001ca0 testl        %ecx, %ecx
	0x0f, 0x85, 0xd0, 0x05, 0x00, 0x00, //0x00001ca2 jne          LBB0_411
	0x4d, 0x85, 0xff, //0x00001ca8 testq        %r15, %r15
	0x0f, 0x85, 0xdf, 0x05, 0x00, 0x00, //0x00001cab jne          LBB0_413
	0x45, 0x31, 0xff, //0x00001cb1 xorl         %r15d, %r15d
	0x48, 0x85, 0xf6, //0x00001cb4 testq        %rsi, %rsi
	0x0f, 0x84, 0x1e, 0x06, 0x00, 0x00, //0x00001cb7 je           LBB0_414
	//0x00001cbd LBB0_124
	0x48, 0x0f, 0xbc, 0xce, //0x00001cbd bsfq         %rsi, %rcx
	0xe9, 0x1a, 0x06, 0x00, 0x00, //0x00001cc1 jmp          LBB0_415
	//0x00001cc6 LBB0_348
	0x49, 0x89, 0xce, //0x00001cc6 movq         %rcx, %r14
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001cc9 movq         $-1, %rcx
	0x4d, 0x85, 0xe4, //0x00001cd0 testq        %r12, %r12
	0x48, 0x8b, 0x55, 0xd0, //0x00001cd3 movq         $-48(%rbp), %rdx
	0x0f, 0x85, 0xf4, 0xf2, 0xff, 0xff, //0x00001cd7 jne          LBB0_188
	0xe9, 0xf3, 0x0a, 0x00, 0x00, //0x00001cdd jmp          LBB0_481
	//0x00001ce2 LBB0_349
	0x4d, 0x29, 0xca, //0x00001ce2 subq         %r9, %r10
	0x44, 0x0f, 0xbc, 0xfb, //0x00001ce5 bsfl         %ebx, %r15d
	0x4d, 0x01, 0xd7, //0x00001ce9 addq         %r10, %r15
	0x49, 0xf7, 0xd7, //0x00001cec notq         %r15
	0x4c, 0x8b, 0x55, 0xd0, //0x00001cef movq         $-48(%rbp), %r10
	0xe9, 0x76, 0xfe, 0xff, 0xff, //0x00001cf3 jmp          LBB0_342
	//0x00001cf8 LBB0_350
	0x48, 0xf7, 0xd6, //0x00001cf8 notq         %rsi
	0x49, 0x89, 0xf6, //0x00001cfb movq         %rsi, %r14
	0x48, 0x8b, 0x55, 0xd0, //0x00001cfe movq         $-48(%rbp), %rdx
	0xe9, 0xcc, 0xf3, 0xff, 0xff, //0x00001d02 jmp          LBB0_197
	//0x00001d07 LBB0_351
	0x48, 0xf7, 0xd2, //0x00001d07 notq         %rdx
	0x49, 0x89, 0xd7, //0x00001d0a movq         %rdx, %r15
	0x4c, 0x8b, 0x55, 0xd0, //0x00001d0d movq         $-48(%rbp), %r10
	0xe9, 0x58, 0xfe, 0xff, 0xff, //0x00001d11 jmp          LBB0_342
	//0x00001d16 LBB0_220
	0x4c, 0x89, 0xf9, //0x00001d16 movq         %r15, %rcx
	0x4c, 0x89, 0xc3, //0x00001d19 movq         %r8, %rbx
	0x48, 0x83, 0xf9, 0x20, //0x00001d1c cmpq         $32, %rcx
	0x0f, 0x82, 0xfd, 0x07, 0x00, 0x00, //0x00001d20 jb           LBB0_443
	//0x00001d26 LBB0_221
	0xc5, 0xfe, 0x6f, 0x03, //0x00001d26 vmovdqu      (%rbx), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001d2a vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xd1, //0x00001d2e vpmovmskb    %ymm1, %edx
	0xc5, 0xfd, 0x74, 0xc7, //0x00001d32 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00001d36 vpmovmskb    %ymm0, %esi
	0x85, 0xf6, //0x00001d3a testl        %esi, %esi
	0x0f, 0x85, 0x6a, 0x07, 0x00, 0x00, //0x00001d3c jne          LBB0_438
	0x4d, 0x85, 0xf6, //0x00001d42 testq        %r14, %r14
	0x0f, 0x85, 0x80, 0x07, 0x00, 0x00, //0x00001d45 jne          LBB0_440
	0x45, 0x31, 0xf6, //0x00001d4b xorl         %r14d, %r14d
	0x48, 0x85, 0xd2, //0x00001d4e testq        %rdx, %rdx
	0x0f, 0x84, 0xc4, 0x07, 0x00, 0x00, //0x00001d51 je           LBB0_442
	//0x00001d57 LBB0_226
	0x4d, 0x89, 0xe0, //0x00001d57 movq         %r12, %r8
	0x48, 0x0f, 0xbc, 0xc2, //0x00001d5a bsfq         %rdx, %rax
	0x4c, 0x29, 0xcb, //0x00001d5e subq         %r9, %rbx
	0x4c, 0x8d, 0x64, 0x03, 0x01, //0x00001d61 leaq         $1(%rbx,%rax), %r12
	0x4c, 0x8b, 0x75, 0xc8, //0x00001d66 movq         $-56(%rbp), %r14
	//0x00001d6a LBB0_323
	0x4d, 0x85, 0xe4, //0x00001d6a testq        %r12, %r12
	0x0f, 0x88, 0x45, 0x0a, 0x00, 0x00, //0x00001d6d js           LBB0_478
	0x4d, 0x89, 0x22, //0x00001d73 movq         %r12, (%r10)
	0x4c, 0x89, 0xe9, //0x00001d76 movq         %r13, %rcx
	0x4d, 0x85, 0xc0, //0x00001d79 testq        %r8, %r8
	0x0f, 0x8f, 0xfe, 0xe4, 0xff, 0xff, //0x00001d7c jg           LBB0_3
	0xe9, 0x62, 0x0a, 0x00, 0x00, //0x00001d82 jmp          LBB0_501
	//0x00001d87 LBB0_352
	0x49, 0xf7, 0xde, //0x00001d87 negq         %r14
	0x48, 0x8b, 0x55, 0xd0, //0x00001d8a movq         $-48(%rbp), %rdx
	0xe9, 0x40, 0xf3, 0xff, 0xff, //0x00001d8e jmp          LBB0_197
	//0x00001d93 LBB0_259
	0x4c, 0x89, 0xf3, //0x00001d93 movq         %r14, %rbx
	0x4c, 0x8b, 0x65, 0xa0, //0x00001d96 movq         $-96(%rbp), %r12
	0x48, 0x83, 0xfb, 0x20, //0x00001d9a cmpq         $32, %rbx
	0x4c, 0x8b, 0x75, 0xc8, //0x00001d9e movq         $-56(%rbp), %r14
	0x0f, 0x82, 0xb1, 0x08, 0x00, 0x00, //0x00001da2 jb           LBB0_460
	//0x00001da8 LBB0_260
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x00001da8 vmovdqu      (%r12), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001dae vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x00001db2 vpmovmskb    %ymm1, %esi
	0xc5, 0xfd, 0x74, 0xcf, //0x00001db6 vpcmpeqb     %ymm7, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xc9, //0x00001dba vpmovmskb    %ymm1, %ecx
	0xc5, 0xbd, 0x64, 0xc8, //0x00001dbe vpcmpgtb     %ymm0, %ymm8, %ymm1
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x00001dc2 vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xfd, 0xdb, 0xc1, //0x00001dc7 vpand        %ymm1, %ymm0, %ymm0
	0xc5, 0x7d, 0xd7, 0xc8, //0x00001dcb vpmovmskb    %ymm0, %r9d
	0x85, 0xc9, //0x00001dcf testl        %ecx, %ecx
	0x0f, 0x85, 0xde, 0x07, 0x00, 0x00, //0x00001dd1 jne          LBB0_452
	0x4d, 0x85, 0xff, //0x00001dd7 testq        %r15, %r15
	0x0f, 0x85, 0xed, 0x07, 0x00, 0x00, //0x00001dda jne          LBB0_454
	0x45, 0x31, 0xff, //0x00001de0 xorl         %r15d, %r15d
	0x48, 0x85, 0xf6, //0x00001de3 testq        %rsi, %rsi
	0x0f, 0x84, 0x2c, 0x08, 0x00, 0x00, //0x00001de6 je           LBB0_455
	//0x00001dec LBB0_263
	0x48, 0x0f, 0xbc, 0xce, //0x00001dec bsfq         %rsi, %rcx
	0xe9, 0x28, 0x08, 0x00, 0x00, //0x00001df0 jmp          LBB0_456
	//0x00001df5 LBB0_353
	0x4d, 0x29, 0xcf, //0x00001df5 subq         %r9, %r15
	0x0f, 0xbc, 0xc3, //0x00001df8 bsfl         %ebx, %eax
	0x4c, 0x01, 0xf8, //0x00001dfb addq         %r15, %rax
	0x48, 0xf7, 0xd0, //0x00001dfe notq         %rax
	0x49, 0x89, 0xc7, //0x00001e01 movq         %rax, %r15
	0x4c, 0x8b, 0x55, 0xd0, //0x00001e04 movq         $-48(%rbp), %r10
	0xe9, 0x61, 0xfd, 0xff, 0xff, //0x00001e08 jmp          LBB0_342
	//0x00001e0d LBB0_354
	0x48, 0xf7, 0xd7, //0x00001e0d notq         %rdi
	0x49, 0x89, 0xff, //0x00001e10 movq         %rdi, %r15
	0x4c, 0x8b, 0x55, 0xd0, //0x00001e13 movq         $-48(%rbp), %r10
	0xe9, 0x52, 0xfd, 0xff, 0xff, //0x00001e17 jmp          LBB0_342
	//0x00001e1c LBB0_355
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00001e1c movq         $-1, %r8
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00001e23 movq         $-1, %r12
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00001e2a movq         $-1, %rax
	0x4d, 0x89, 0xfe, //0x00001e31 movq         %r15, %r14
	0x49, 0x83, 0xfa, 0x10, //0x00001e34 cmpq         $16, %r10
	0x0f, 0x83, 0xc0, 0xea, 0xff, 0xff, //0x00001e38 jae          LBB0_151
	0xe9, 0x0d, 0xec, 0xff, 0xff, //0x00001e3e jmp          LBB0_169
	//0x00001e43 LBB0_356
	0x4d, 0x89, 0xcb, //0x00001e43 movq         %r9, %r11
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00001e46 movq         $-1, %r8
	0x45, 0x31, 0xf6, //0x00001e4d xorl         %r14d, %r14d
	0x48, 0x83, 0xf9, 0x20, //0x00001e50 cmpq         $32, %rcx
	0x0f, 0x83, 0x2f, 0xfc, 0xff, 0xff, //0x00001e54 jae          LBB0_55
	0xe9, 0x38, 0x01, 0x00, 0x00, //0x00001e5a jmp          LBB0_370
	//0x00001e5f LBB0_357
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00001e5f movq         $-1, %r8
	0x45, 0x31, 0xff, //0x00001e66 xorl         %r15d, %r15d
	0x49, 0x83, 0xfa, 0x20, //0x00001e69 cmpq         $32, %r10
	0x0f, 0x83, 0x8a, 0xfc, 0xff, 0xff, //0x00001e6d jae          LBB0_85
	0xe9, 0x57, 0x02, 0x00, 0x00, //0x00001e73 jmp          LBB0_388
	//0x00001e78 LBB0_358
	0x49, 0x89, 0xcf, //0x00001e78 movq         %rcx, %r15
	0x4d, 0x85, 0xdb, //0x00001e7b testq        %r11, %r11
	0x0f, 0x85, 0xf9, 0xfa, 0xff, 0xff, //0x00001e7e jne          LBB0_327
	0xe9, 0xd5, 0x0a, 0x00, 0x00, //0x00001e84 jmp          LBB0_497
	//0x00001e89 LBB0_359
	0x48, 0xf7, 0xd6, //0x00001e89 notq         %rsi
	0x49, 0x89, 0xf7, //0x00001e8c movq         %rsi, %r15
	0x4c, 0x8b, 0x55, 0xd0, //0x00001e8f movq         $-48(%rbp), %r10
	0xe9, 0xd6, 0xfc, 0xff, 0xff, //0x00001e93 jmp          LBB0_342
	//0x00001e98 LBB0_360
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001e98 movq         $-1, %r11
	0x45, 0x31, 0xf6, //0x00001e9f xorl         %r14d, %r14d
	0x48, 0x83, 0xf9, 0x20, //0x00001ea2 cmpq         $32, %rcx
	0x0f, 0x83, 0x38, 0xfd, 0xff, 0xff, //0x00001ea6 jae          LBB0_102
	0xe9, 0x35, 0x03, 0x00, 0x00, //0x00001eac jmp          LBB0_402
	//0x00001eb1 LBB0_361
	0x49, 0x89, 0xc8, //0x00001eb1 movq         %rcx, %r8
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001eb4 movq         $-1, %r11
	0x45, 0x31, 0xff, //0x00001ebb xorl         %r15d, %r15d
	0x48, 0x83, 0xfb, 0x20, //0x00001ebe cmpq         $32, %rbx
	0x0f, 0x83, 0xb1, 0xfd, 0xff, 0xff, //0x00001ec2 jae          LBB0_121
	0xe9, 0x4f, 0x04, 0x00, 0x00, //0x00001ec8 jmp          LBB0_420
	//0x00001ecd LBB0_365
	0x49, 0xf7, 0xdf, //0x00001ecd negq         %r15
	0xe9, 0x99, 0xfc, 0xff, 0xff, //0x00001ed0 jmp          LBB0_342
	//0x00001ed5 LBB0_362
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00001ed5 movq         $-1, %r8
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001edc movq         $-1, %r11
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00001ee3 movq         $-1, %r12
	0x4d, 0x89, 0xcf, //0x00001eea movq         %r9, %r15
	0xe9, 0xdb, 0xf7, 0xff, 0xff, //0x00001eed jmp          LBB0_290
	//0x00001ef2 LBB0_363
	0x49, 0x89, 0xd1, //0x00001ef2 movq         %rdx, %r9
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001ef5 movq         $-1, %r11
	0x45, 0x31, 0xf6, //0x00001efc xorl         %r14d, %r14d
	0x48, 0x83, 0xf9, 0x20, //0x00001eff cmpq         $32, %rcx
	0x0f, 0x83, 0x1d, 0xfe, 0xff, 0xff, //0x00001f03 jae          LBB0_221
	0xe9, 0x15, 0x06, 0x00, 0x00, //0x00001f09 jmp          LBB0_443
	//0x00001f0e LBB0_364
	0x49, 0x89, 0xc8, //0x00001f0e movq         %rcx, %r8
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001f11 movq         $-1, %r11
	0x45, 0x31, 0xff, //0x00001f18 xorl         %r15d, %r15d
	0x48, 0x83, 0xfb, 0x20, //0x00001f1b cmpq         $32, %rbx
	0x4c, 0x8b, 0x75, 0xc8, //0x00001f1f movq         $-56(%rbp), %r14
	0x0f, 0x83, 0x7f, 0xfe, 0xff, 0xff, //0x00001f23 jae          LBB0_260
	0xe9, 0x2b, 0x07, 0x00, 0x00, //0x00001f29 jmp          LBB0_460
	//0x00001f2e LBB0_366
	0x49, 0x83, 0xf8, 0xff, //0x00001f2e cmpq         $-1, %r8
	0x0f, 0x85, 0x0d, 0x00, 0x00, 0x00, //0x00001f32 jne          LBB0_368
	0x48, 0x89, 0xd8, //0x00001f38 movq         %rbx, %rax
	0x4c, 0x29, 0xd8, //0x00001f3b subq         %r11, %rax
	0x4c, 0x0f, 0xbc, 0xc6, //0x00001f3e bsfq         %rsi, %r8
	0x49, 0x01, 0xc0, //0x00001f42 addq         %rax, %r8
	//0x00001f45 LBB0_368
	0x44, 0x89, 0xf0, //0x00001f45 movl         %r14d, %eax
	0xf7, 0xd0, //0x00001f48 notl         %eax
	0x21, 0xf0, //0x00001f4a andl         %esi, %eax
	0x44, 0x8d, 0x0c, 0x00, //0x00001f4c leal         (%rax,%rax), %r9d
	0x45, 0x09, 0xf1, //0x00001f50 orl          %r14d, %r9d
	0x44, 0x89, 0xcf, //0x00001f53 movl         %r9d, %edi
	0xf7, 0xd7, //0x00001f56 notl         %edi
	0x21, 0xf7, //0x00001f58 andl         %esi, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001f5a andl         $-1431655766, %edi
	0x45, 0x31, 0xf6, //0x00001f60 xorl         %r14d, %r14d
	0x01, 0xc7, //0x00001f63 addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc6, //0x00001f65 setb         %r14b
	0x01, 0xff, //0x00001f69 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00001f6b xorl         $1431655765, %edi
	0x44, 0x21, 0xcf, //0x00001f71 andl         %r9d, %edi
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00001f74 movl         $4294967295, %eax
	0x31, 0xf8, //0x00001f79 xorl         %edi, %eax
	0x21, 0xc2, //0x00001f7b andl         %eax, %edx
	0x4c, 0x8b, 0x55, 0xd0, //0x00001f7d movq         $-48(%rbp), %r10
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00001f81 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xd2, //0x00001f86 testq        %rdx, %rdx
	0x0f, 0x85, 0x2b, 0xfb, 0xff, 0xff, //0x00001f89 jne          LBB0_60
	//0x00001f8f LBB0_369
	0x48, 0x83, 0xc3, 0x20, //0x00001f8f addq         $32, %rbx
	0x48, 0x83, 0xc1, 0xe0, //0x00001f93 addq         $-32, %rcx
	//0x00001f97 LBB0_370
	0x4d, 0x85, 0xf6, //0x00001f97 testq        %r14, %r14
	0x0f, 0x85, 0x1c, 0x04, 0x00, 0x00, //0x00001f9a jne          LBB0_429
	0x4c, 0x8b, 0x75, 0xc8, //0x00001fa0 movq         $-56(%rbp), %r14
	0x48, 0x85, 0xc9, //0x00001fa4 testq        %rcx, %rcx
	0x0f, 0x84, 0xeb, 0x07, 0x00, 0x00, //0x00001fa7 je           LBB0_476
	//0x00001fad LBB0_372
	0x4c, 0x89, 0xdf, //0x00001fad movq         %r11, %rdi
	0x48, 0xf7, 0xd7, //0x00001fb0 notq         %rdi
	//0x00001fb3 LBB0_373
	0x4c, 0x8d, 0x63, 0x01, //0x00001fb3 leaq         $1(%rbx), %r12
	0x0f, 0xb6, 0x13, //0x00001fb7 movzbl       (%rbx), %edx
	0x80, 0xfa, 0x22, //0x00001fba cmpb         $34, %dl
	0x0f, 0x84, 0x5b, 0x00, 0x00, 0x00, //0x00001fbd je           LBB0_378
	0x48, 0x8d, 0x71, 0xff, //0x00001fc3 leaq         $-1(%rcx), %rsi
	0x80, 0xfa, 0x5c, //0x00001fc7 cmpb         $92, %dl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00001fca je           LBB0_376
	0x48, 0x89, 0xf1, //0x00001fd0 movq         %rsi, %rcx
	0x4c, 0x89, 0xe3, //0x00001fd3 movq         %r12, %rbx
	0x48, 0x85, 0xf6, //0x00001fd6 testq        %rsi, %rsi
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x00001fd9 jne          LBB0_373
	0xe9, 0xb4, 0x07, 0x00, 0x00, //0x00001fdf jmp          LBB0_476
	//0x00001fe4 LBB0_376
	0x48, 0x85, 0xf6, //0x00001fe4 testq        %rsi, %rsi
	0x0f, 0x84, 0xba, 0x09, 0x00, 0x00, //0x00001fe7 je           LBB0_433
	0x49, 0x01, 0xfc, //0x00001fed addq         %rdi, %r12
	0x49, 0x83, 0xf8, 0xff, //0x00001ff0 cmpq         $-1, %r8
	0x4d, 0x0f, 0x44, 0xc4, //0x00001ff4 cmoveq       %r12, %r8
	0x48, 0x83, 0xc3, 0x02, //0x00001ff8 addq         $2, %rbx
	0x48, 0x83, 0xc1, 0xfe, //0x00001ffc addq         $-2, %rcx
	0x48, 0x89, 0xce, //0x00002000 movq         %rcx, %rsi
	0x4c, 0x8b, 0x55, 0xd0, //0x00002003 movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x00002007 movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000200b vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xf6, //0x00002010 testq        %rsi, %rsi
	0x0f, 0x85, 0x9a, 0xff, 0xff, 0xff, //0x00002013 jne          LBB0_373
	0xe9, 0x7a, 0x07, 0x00, 0x00, //0x00002019 jmp          LBB0_476
	//0x0000201e LBB0_378
	0x4d, 0x29, 0xdc, //0x0000201e subq         %r11, %r12
	0xe9, 0xa4, 0xfa, 0xff, 0xff, //0x00002021 jmp          LBB0_184
	//0x00002026 LBB0_379
	0x49, 0x83, 0xf8, 0xff, //0x00002026 cmpq         $-1, %r8
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x0000202a jne          LBB0_381
	0x4c, 0x89, 0xe2, //0x00002030 movq         %r12, %rdx
	0x48, 0x2b, 0x55, 0xc0, //0x00002033 subq         $-64(%rbp), %rdx
	0x4c, 0x0f, 0xbc, 0xc1, //0x00002037 bsfq         %rcx, %r8
	0x49, 0x01, 0xd0, //0x0000203b addq         %rdx, %r8
	//0x0000203e LBB0_381
	0x44, 0x89, 0xfa, //0x0000203e movl         %r15d, %edx
	0xf7, 0xd2, //0x00002041 notl         %edx
	0x21, 0xca, //0x00002043 andl         %ecx, %edx
	0x8d, 0x1c, 0x12, //0x00002045 leal         (%rdx,%rdx), %ebx
	0x44, 0x09, 0xfb, //0x00002048 orl          %r15d, %ebx
	0x89, 0xdf, //0x0000204b movl         %ebx, %edi
	0xf7, 0xd7, //0x0000204d notl         %edi
	0x21, 0xcf, //0x0000204f andl         %ecx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002051 andl         $-1431655766, %edi
	0x45, 0x31, 0xff, //0x00002057 xorl         %r15d, %r15d
	0x01, 0xd7, //0x0000205a addl         %edx, %edi
	0x41, 0x0f, 0x92, 0xc7, //0x0000205c setb         %r15b
	0x01, 0xff, //0x00002060 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00002062 xorl         $1431655765, %edi
	0x21, 0xdf, //0x00002068 andl         %ebx, %edi
	0xb9, 0xff, 0xff, 0xff, 0xff, //0x0000206a movl         $4294967295, %ecx
	0x31, 0xf9, //0x0000206f xorl         %edi, %ecx
	0x21, 0xce, //0x00002071 andl         %ecx, %esi
	0x4c, 0x8b, 0x75, 0xc8, //0x00002073 movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002077 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xf6, //0x0000207c testq        %rsi, %rsi
	0x0f, 0x85, 0xbc, 0xfa, 0xff, 0xff, //0x0000207f jne          LBB0_88
	//0x00002085 LBB0_382
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00002085 movl         $64, %ecx
	//0x0000208a LBB0_383
	0x49, 0x0f, 0xbc, 0xd1, //0x0000208a bsfq         %r9, %rdx
	0x45, 0x85, 0xc9, //0x0000208e testl        %r9d, %r9d
	0xbf, 0x40, 0x00, 0x00, 0x00, //0x00002091 movl         $64, %edi
	0x48, 0x0f, 0x45, 0xfa, //0x00002096 cmovneq      %rdx, %rdi
	0x48, 0x85, 0xf6, //0x0000209a testq        %rsi, %rsi
	0x0f, 0x84, 0x1b, 0x00, 0x00, 0x00, //0x0000209d je           LBB0_386
	0x4c, 0x2b, 0x65, 0xc0, //0x000020a3 subq         $-64(%rbp), %r12
	0x48, 0x39, 0xcf, //0x000020a7 cmpq         %rcx, %rdi
	0x0f, 0x82, 0x12, 0x09, 0x00, 0x00, //0x000020aa jb           LBB0_511
	0x4d, 0x8d, 0x64, 0x0c, 0x01, //0x000020b0 leaq         $1(%r12,%rcx), %r12
	0x4c, 0x8b, 0x55, 0xd0, //0x000020b5 movq         $-48(%rbp), %r10
	0xe9, 0x0c, 0xfa, 0xff, 0xff, //0x000020b9 jmp          LBB0_184
	//0x000020be LBB0_386
	0x45, 0x85, 0xc9, //0x000020be testl        %r9d, %r9d
	0x0f, 0x85, 0x0d, 0x09, 0x00, 0x00, //0x000020c1 jne          LBB0_512
	0x49, 0x83, 0xc4, 0x20, //0x000020c7 addq         $32, %r12
	0x49, 0x83, 0xc2, 0xe0, //0x000020cb addq         $-32, %r10
	//0x000020cf LBB0_388
	0x4d, 0x85, 0xff, //0x000020cf testq        %r15, %r15
	0x0f, 0x85, 0x1f, 0x03, 0x00, 0x00, //0x000020d2 jne          LBB0_431
	0x48, 0x8b, 0x45, 0xc0, //0x000020d8 movq         $-64(%rbp), %rax
	0x4d, 0x85, 0xd2, //0x000020dc testq        %r10, %r10
	0x0f, 0x84, 0xc2, 0x08, 0x00, 0x00, //0x000020df je           LBB0_433
	//0x000020e5 LBB0_390
	0x41, 0x0f, 0xb6, 0x0c, 0x24, //0x000020e5 movzbl       (%r12), %ecx
	0x80, 0xf9, 0x22, //0x000020ea cmpb         $34, %cl
	0x0f, 0x84, 0x6d, 0x00, 0x00, 0x00, //0x000020ed je           LBB0_396
	0x80, 0xf9, 0x5c, //0x000020f3 cmpb         $92, %cl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x000020f6 je           LBB0_394
	0x80, 0xf9, 0x1f, //0x000020fc cmpb         $31, %cl
	0x0f, 0x86, 0xed, 0x08, 0x00, 0x00, //0x000020ff jbe          LBB0_513
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00002105 movq         $-1, %rcx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x0000210c movl         $1, %edx
	0x49, 0x01, 0xd4, //0x00002111 addq         %rdx, %r12
	0x49, 0x01, 0xca, //0x00002114 addq         %rcx, %r10
	0x0f, 0x85, 0xc8, 0xff, 0xff, 0xff, //0x00002117 jne          LBB0_390
	0xe9, 0x85, 0x08, 0x00, 0x00, //0x0000211d jmp          LBB0_433
	//0x00002122 LBB0_394
	0x49, 0x83, 0xfa, 0x01, //0x00002122 cmpq         $1, %r10
	0x0f, 0x84, 0x7b, 0x08, 0x00, 0x00, //0x00002126 je           LBB0_433
	0x4c, 0x89, 0xe1, //0x0000212c movq         %r12, %rcx
	0x48, 0x29, 0xc1, //0x0000212f subq         %rax, %rcx
	0x49, 0x83, 0xf8, 0xff, //0x00002132 cmpq         $-1, %r8
	0x4c, 0x0f, 0x44, 0xc1, //0x00002136 cmoveq       %rcx, %r8
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x0000213a movq         $-2, %rcx
	0xba, 0x02, 0x00, 0x00, 0x00, //0x00002141 movl         $2, %edx
	0x4c, 0x8b, 0x75, 0xc8, //0x00002146 movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000214a vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x49, 0x01, 0xd4, //0x0000214f addq         %rdx, %r12
	0x49, 0x01, 0xca, //0x00002152 addq         %rcx, %r10
	0x0f, 0x85, 0x8a, 0xff, 0xff, 0xff, //0x00002155 jne          LBB0_390
	0xe9, 0x47, 0x08, 0x00, 0x00, //0x0000215b jmp          LBB0_433
	//0x00002160 LBB0_396
	0x49, 0x29, 0xc4, //0x00002160 subq         %rax, %r12
	0x49, 0xff, 0xc4, //0x00002163 incq         %r12
	0x4c, 0x8b, 0x55, 0xd0, //0x00002166 movq         $-48(%rbp), %r10
	0xe9, 0x5b, 0xf9, 0xff, 0xff, //0x0000216a jmp          LBB0_184
	//0x0000216f LBB0_397
	0x4d, 0x89, 0xc8, //0x0000216f movq         %r9, %r8
	0x49, 0x83, 0xfb, 0xff, //0x00002172 cmpq         $-1, %r11
	0x0f, 0x85, 0x15, 0x00, 0x00, 0x00, //0x00002176 jne          LBB0_400
	0x48, 0x89, 0xd8, //0x0000217c movq         %rbx, %rax
	0x4c, 0x29, 0xc0, //0x0000217f subq         %r8, %rax
	0x4c, 0x0f, 0xbc, 0xde, //0x00002182 bsfq         %rsi, %r11
	0x49, 0x01, 0xc3, //0x00002186 addq         %rax, %r11
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x00002189 jmp          LBB0_400
	//0x0000218e LBB0_399
	0x4d, 0x89, 0xc8, //0x0000218e movq         %r9, %r8
	//0x00002191 LBB0_400
	0x44, 0x89, 0xf0, //0x00002191 movl         %r14d, %eax
	0xf7, 0xd0, //0x00002194 notl         %eax
	0x21, 0xf0, //0x00002196 andl         %esi, %eax
	0x44, 0x8d, 0x0c, 0x00, //0x00002198 leal         (%rax,%rax), %r9d
	0x45, 0x09, 0xf1, //0x0000219c orl          %r14d, %r9d
	0x44, 0x89, 0xcf, //0x0000219f movl         %r9d, %edi
	0xf7, 0xd7, //0x000021a2 notl         %edi
	0x21, 0xf7, //0x000021a4 andl         %esi, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x000021a6 andl         $-1431655766, %edi
	0x45, 0x31, 0xf6, //0x000021ac xorl         %r14d, %r14d
	0x01, 0xc7, //0x000021af addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc6, //0x000021b1 setb         %r14b
	0x01, 0xff, //0x000021b5 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x000021b7 xorl         $1431655765, %edi
	0x44, 0x21, 0xcf, //0x000021bd andl         %r9d, %edi
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x000021c0 movl         $4294967295, %eax
	0x31, 0xf8, //0x000021c5 xorl         %edi, %eax
	0x21, 0xc2, //0x000021c7 andl         %eax, %edx
	0x4c, 0x8b, 0x55, 0xd0, //0x000021c9 movq         $-48(%rbp), %r10
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000021cd vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x4d, 0x89, 0xc1, //0x000021d2 movq         %r8, %r9
	0x48, 0x85, 0xd2, //0x000021d5 testq        %rdx, %rdx
	0x0f, 0x85, 0x37, 0xfa, 0xff, 0xff, //0x000021d8 jne          LBB0_107
	//0x000021de LBB0_401
	0x48, 0x83, 0xc3, 0x20, //0x000021de addq         $32, %rbx
	0x48, 0x83, 0xc1, 0xe0, //0x000021e2 addq         $-32, %rcx
	//0x000021e6 LBB0_402
	0x4d, 0x85, 0xf6, //0x000021e6 testq        %r14, %r14
	0x0f, 0x85, 0x43, 0x02, 0x00, 0x00, //0x000021e9 jne          LBB0_434
	0x4c, 0x8b, 0x75, 0xc8, //0x000021ef movq         $-56(%rbp), %r14
	0x48, 0x85, 0xc9, //0x000021f3 testq        %rcx, %rcx
	0x0f, 0x84, 0xc6, 0x05, 0x00, 0x00, //0x000021f6 je           LBB0_479
	//0x000021fc LBB0_404
	0x4d, 0x89, 0xe0, //0x000021fc movq         %r12, %r8
	0x4c, 0x89, 0xcf, //0x000021ff movq         %r9, %rdi
	0x48, 0xf7, 0xd7, //0x00002202 notq         %rdi
	//0x00002205 LBB0_405
	0x4c, 0x8d, 0x63, 0x01, //0x00002205 leaq         $1(%rbx), %r12
	0x0f, 0xb6, 0x13, //0x00002209 movzbl       (%rbx), %edx
	0x80, 0xfa, 0x22, //0x0000220c cmpb         $34, %dl
	0x0f, 0x84, 0x5b, 0x00, 0x00, 0x00, //0x0000220f je           LBB0_410
	0x48, 0x8d, 0x71, 0xff, //0x00002215 leaq         $-1(%rcx), %rsi
	0x80, 0xfa, 0x5c, //0x00002219 cmpb         $92, %dl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x0000221c je           LBB0_408
	0x48, 0x89, 0xf1, //0x00002222 movq         %rsi, %rcx
	0x4c, 0x89, 0xe3, //0x00002225 movq         %r12, %rbx
	0x48, 0x85, 0xf6, //0x00002228 testq        %rsi, %rsi
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x0000222b jne          LBB0_405
	0xe9, 0x8c, 0x05, 0x00, 0x00, //0x00002231 jmp          LBB0_479
	//0x00002236 LBB0_408
	0x48, 0x85, 0xf6, //0x00002236 testq        %rsi, %rsi
	0x0f, 0x84, 0xcf, 0x07, 0x00, 0x00, //0x00002239 je           LBB0_516
	0x49, 0x01, 0xfc, //0x0000223f addq         %rdi, %r12
	0x49, 0x83, 0xfb, 0xff, //0x00002242 cmpq         $-1, %r11
	0x4d, 0x0f, 0x44, 0xdc, //0x00002246 cmoveq       %r12, %r11
	0x48, 0x83, 0xc3, 0x02, //0x0000224a addq         $2, %rbx
	0x48, 0x83, 0xc1, 0xfe, //0x0000224e addq         $-2, %rcx
	0x48, 0x89, 0xce, //0x00002252 movq         %rcx, %rsi
	0x4c, 0x8b, 0x55, 0xd0, //0x00002255 movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x00002259 movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000225d vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xf6, //0x00002262 testq        %rsi, %rsi
	0x0f, 0x85, 0x9a, 0xff, 0xff, 0xff, //0x00002265 jne          LBB0_405
	0xe9, 0x52, 0x05, 0x00, 0x00, //0x0000226b jmp          LBB0_479
	//0x00002270 LBB0_410
	0x4d, 0x29, 0xcc, //0x00002270 subq         %r9, %r12
	0xe9, 0xb0, 0xf9, 0xff, 0xff, //0x00002273 jmp          LBB0_205
	//0x00002278 LBB0_411
	0x49, 0x83, 0xfb, 0xff, //0x00002278 cmpq         $-1, %r11
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x0000227c jne          LBB0_413
	0x4c, 0x89, 0xe0, //0x00002282 movq         %r12, %rax
	0x48, 0x2b, 0x45, 0xc0, //0x00002285 subq         $-64(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xd9, //0x00002289 bsfq         %rcx, %r11
	0x49, 0x01, 0xc3, //0x0000228d addq         %rax, %r11
	//0x00002290 LBB0_413
	0x44, 0x89, 0xf8, //0x00002290 movl         %r15d, %eax
	0xf7, 0xd0, //0x00002293 notl         %eax
	0x21, 0xc8, //0x00002295 andl         %ecx, %eax
	0x8d, 0x14, 0x00, //0x00002297 leal         (%rax,%rax), %edx
	0x44, 0x09, 0xfa, //0x0000229a orl          %r15d, %edx
	0x89, 0xd7, //0x0000229d movl         %edx, %edi
	0xf7, 0xd7, //0x0000229f notl         %edi
	0x21, 0xcf, //0x000022a1 andl         %ecx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x000022a3 andl         $-1431655766, %edi
	0x45, 0x31, 0xff, //0x000022a9 xorl         %r15d, %r15d
	0x01, 0xc7, //0x000022ac addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc7, //0x000022ae setb         %r15b
	0x01, 0xff, //0x000022b2 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x000022b4 xorl         $1431655765, %edi
	0x21, 0xd7, //0x000022ba andl         %edx, %edi
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x000022bc movl         $4294967295, %eax
	0x31, 0xf8, //0x000022c1 xorl         %edi, %eax
	0x21, 0xc6, //0x000022c3 andl         %eax, %esi
	0x4c, 0x8b, 0x55, 0xd0, //0x000022c5 movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x000022c9 movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000022cd vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xf6, //0x000022d2 testq        %rsi, %rsi
	0x0f, 0x85, 0xe2, 0xf9, 0xff, 0xff, //0x000022d5 jne          LBB0_124
	//0x000022db LBB0_414
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x000022db movl         $64, %ecx
	//0x000022e0 LBB0_415
	0x49, 0x0f, 0xbc, 0xd1, //0x000022e0 bsfq         %r9, %rdx
	0x45, 0x85, 0xc9, //0x000022e4 testl        %r9d, %r9d
	0xbf, 0x40, 0x00, 0x00, 0x00, //0x000022e7 movl         $64, %edi
	0x48, 0x0f, 0x45, 0xfa, //0x000022ec cmovneq      %rdx, %rdi
	0x48, 0x85, 0xf6, //0x000022f0 testq        %rsi, %rsi
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x000022f3 je           LBB0_418
	0x4c, 0x2b, 0x65, 0xc0, //0x000022f9 subq         $-64(%rbp), %r12
	0x48, 0x39, 0xcf, //0x000022fd cmpq         %rcx, %rdi
	0x0f, 0x83, 0x1a, 0xee, 0xff, 0xff, //0x00002300 jae          LBB0_204
	0xe9, 0xd5, 0x06, 0x00, 0x00, //0x00002306 jmp          LBB0_417
	//0x0000230b LBB0_418
	0x45, 0x85, 0xc9, //0x0000230b testl        %r9d, %r9d
	0x0f, 0x85, 0xe6, 0x06, 0x00, 0x00, //0x0000230e jne          LBB0_514
	0x49, 0x83, 0xc4, 0x20, //0x00002314 addq         $32, %r12
	0x48, 0x83, 0xc3, 0xe0, //0x00002318 addq         $-32, %rbx
	//0x0000231c LBB0_420
	0x4d, 0x85, 0xff, //0x0000231c testq        %r15, %r15
	0x0f, 0x85, 0x48, 0x01, 0x00, 0x00, //0x0000231f jne          LBB0_436
	0x48, 0x8b, 0x75, 0xc0, //0x00002325 movq         $-64(%rbp), %rsi
	0x48, 0x85, 0xdb, //0x00002329 testq        %rbx, %rbx
	0x0f, 0x84, 0x90, 0x04, 0x00, 0x00, //0x0000232c je           LBB0_479
	//0x00002332 LBB0_422
	0x41, 0x0f, 0xb6, 0x0c, 0x24, //0x00002332 movzbl       (%r12), %ecx
	0x80, 0xf9, 0x22, //0x00002337 cmpb         $34, %cl
	0x0f, 0x84, 0x71, 0x00, 0x00, 0x00, //0x0000233a je           LBB0_428
	0x80, 0xf9, 0x5c, //0x00002340 cmpb         $92, %cl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00002343 je           LBB0_426
	0x80, 0xf9, 0x1f, //0x00002349 cmpb         $31, %cl
	0x0f, 0x86, 0xb4, 0x06, 0x00, 0x00, //0x0000234c jbe          LBB0_515
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00002352 movq         $-1, %rcx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00002359 movl         $1, %edx
	0x49, 0x01, 0xd4, //0x0000235e addq         %rdx, %r12
	0x48, 0x01, 0xcb, //0x00002361 addq         %rcx, %rbx
	0x0f, 0x85, 0xc8, 0xff, 0xff, 0xff, //0x00002364 jne          LBB0_422
	0xe9, 0x53, 0x04, 0x00, 0x00, //0x0000236a jmp          LBB0_479
	//0x0000236f LBB0_426
	0x48, 0x83, 0xfb, 0x01, //0x0000236f cmpq         $1, %rbx
	0x0f, 0x84, 0x95, 0x06, 0x00, 0x00, //0x00002373 je           LBB0_516
	0x4c, 0x89, 0xe0, //0x00002379 movq         %r12, %rax
	0x48, 0x29, 0xf0, //0x0000237c subq         %rsi, %rax
	0x49, 0x83, 0xfb, 0xff, //0x0000237f cmpq         $-1, %r11
	0x4c, 0x0f, 0x44, 0xd8, //0x00002383 cmoveq       %rax, %r11
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00002387 movq         $-2, %rcx
	0xba, 0x02, 0x00, 0x00, 0x00, //0x0000238e movl         $2, %edx
	0x4c, 0x8b, 0x55, 0xd0, //0x00002393 movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x00002397 movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000239b vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x49, 0x01, 0xd4, //0x000023a0 addq         %rdx, %r12
	0x48, 0x01, 0xcb, //0x000023a3 addq         %rcx, %rbx
	0x0f, 0x85, 0x86, 0xff, 0xff, 0xff, //0x000023a6 jne          LBB0_422
	0xe9, 0x11, 0x04, 0x00, 0x00, //0x000023ac jmp          LBB0_479
	//0x000023b1 LBB0_428
	0x49, 0x29, 0xf4, //0x000023b1 subq         %rsi, %r12
	0x49, 0xff, 0xc4, //0x000023b4 incq         %r12
	0xe9, 0x6c, 0xf8, 0xff, 0xff, //0x000023b7 jmp          LBB0_205
	//0x000023bc LBB0_429
	0x48, 0x85, 0xc9, //0x000023bc testq        %rcx, %rcx
	0x0f, 0x84, 0xe2, 0x05, 0x00, 0x00, //0x000023bf je           LBB0_433
	0x4c, 0x89, 0xd8, //0x000023c5 movq         %r11, %rax
	0x48, 0xf7, 0xd0, //0x000023c8 notq         %rax
	0x48, 0x01, 0xd8, //0x000023cb addq         %rbx, %rax
	0x49, 0x83, 0xf8, 0xff, //0x000023ce cmpq         $-1, %r8
	0x4c, 0x0f, 0x44, 0xc0, //0x000023d2 cmoveq       %rax, %r8
	0x48, 0xff, 0xc3, //0x000023d6 incq         %rbx
	0x48, 0xff, 0xc9, //0x000023d9 decq         %rcx
	0x4c, 0x8b, 0x55, 0xd0, //0x000023dc movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x000023e0 movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000023e4 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xc9, //0x000023e9 testq        %rcx, %rcx
	0x0f, 0x85, 0xbb, 0xfb, 0xff, 0xff, //0x000023ec jne          LBB0_372
	0xe9, 0xa1, 0x03, 0x00, 0x00, //0x000023f2 jmp          LBB0_476
	//0x000023f7 LBB0_431
	0x4d, 0x85, 0xd2, //0x000023f7 testq        %r10, %r10
	0x0f, 0x84, 0xa7, 0x05, 0x00, 0x00, //0x000023fa je           LBB0_433
	0x48, 0x8b, 0x45, 0xc0, //0x00002400 movq         $-64(%rbp), %rax
	0x48, 0x89, 0xc1, //0x00002404 movq         %rax, %rcx
	0x48, 0xf7, 0xd1, //0x00002407 notq         %rcx
	0x4c, 0x01, 0xe1, //0x0000240a addq         %r12, %rcx
	0x49, 0x83, 0xf8, 0xff, //0x0000240d cmpq         $-1, %r8
	0x4c, 0x0f, 0x44, 0xc1, //0x00002411 cmoveq       %rcx, %r8
	0x49, 0xff, 0xc4, //0x00002415 incq         %r12
	0x49, 0xff, 0xca, //0x00002418 decq         %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x0000241b movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000241f vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x4d, 0x85, 0xd2, //0x00002424 testq        %r10, %r10
	0x0f, 0x85, 0xb8, 0xfc, 0xff, 0xff, //0x00002427 jne          LBB0_390
	0xe9, 0x75, 0x05, 0x00, 0x00, //0x0000242d jmp          LBB0_433
	//0x00002432 LBB0_434
	0x48, 0x85, 0xc9, //0x00002432 testq        %rcx, %rcx
	0x0f, 0x84, 0xd3, 0x05, 0x00, 0x00, //0x00002435 je           LBB0_516
	0x4c, 0x89, 0xc8, //0x0000243b movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x0000243e notq         %rax
	0x48, 0x01, 0xd8, //0x00002441 addq         %rbx, %rax
	0x49, 0x83, 0xfb, 0xff, //0x00002444 cmpq         $-1, %r11
	0x4c, 0x0f, 0x44, 0xd8, //0x00002448 cmoveq       %rax, %r11
	0x48, 0xff, 0xc3, //0x0000244c incq         %rbx
	0x48, 0xff, 0xc9, //0x0000244f decq         %rcx
	0x4c, 0x8b, 0x55, 0xd0, //0x00002452 movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x00002456 movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000245a vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xc9, //0x0000245f testq        %rcx, %rcx
	0x0f, 0x85, 0x94, 0xfd, 0xff, 0xff, //0x00002462 jne          LBB0_404
	0xe9, 0x55, 0x03, 0x00, 0x00, //0x00002468 jmp          LBB0_479
	//0x0000246d LBB0_436
	0x48, 0x85, 0xdb, //0x0000246d testq        %rbx, %rbx
	0x0f, 0x84, 0x98, 0x05, 0x00, 0x00, //0x00002470 je           LBB0_516
	0x48, 0x8b, 0x75, 0xc0, //0x00002476 movq         $-64(%rbp), %rsi
	0x48, 0x89, 0xf0, //0x0000247a movq         %rsi, %rax
	0x48, 0xf7, 0xd0, //0x0000247d notq         %rax
	0x4c, 0x01, 0xe0, //0x00002480 addq         %r12, %rax
	0x49, 0x83, 0xfb, 0xff, //0x00002483 cmpq         $-1, %r11
	0x4c, 0x0f, 0x44, 0xd8, //0x00002487 cmoveq       %rax, %r11
	0x49, 0xff, 0xc4, //0x0000248b incq         %r12
	0x48, 0xff, 0xcb, //0x0000248e decq         %rbx
	0x4c, 0x8b, 0x55, 0xd0, //0x00002491 movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x00002495 movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002499 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xdb, //0x0000249e testq        %rbx, %rbx
	0x0f, 0x85, 0x8b, 0xfe, 0xff, 0xff, //0x000024a1 jne          LBB0_422
	0xe9, 0x16, 0x03, 0x00, 0x00, //0x000024a7 jmp          LBB0_479
	//0x000024ac LBB0_438
	0x4d, 0x89, 0xc8, //0x000024ac movq         %r9, %r8
	0x49, 0x83, 0xfb, 0xff, //0x000024af cmpq         $-1, %r11
	0x0f, 0x85, 0x15, 0x00, 0x00, 0x00, //0x000024b3 jne          LBB0_441
	0x48, 0x89, 0xd8, //0x000024b9 movq         %rbx, %rax
	0x4c, 0x29, 0xc0, //0x000024bc subq         %r8, %rax
	0x4c, 0x0f, 0xbc, 0xde, //0x000024bf bsfq         %rsi, %r11
	0x49, 0x01, 0xc3, //0x000024c3 addq         %rax, %r11
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x000024c6 jmp          LBB0_441
	//0x000024cb LBB0_440
	0x4d, 0x89, 0xc8, //0x000024cb movq         %r9, %r8
	//0x000024ce LBB0_441
	0x44, 0x89, 0xf0, //0x000024ce movl         %r14d, %eax
	0xf7, 0xd0, //0x000024d1 notl         %eax
	0x21, 0xf0, //0x000024d3 andl         %esi, %eax
	0x44, 0x8d, 0x0c, 0x00, //0x000024d5 leal         (%rax,%rax), %r9d
	0x45, 0x09, 0xf1, //0x000024d9 orl          %r14d, %r9d
	0x44, 0x89, 0xcf, //0x000024dc movl         %r9d, %edi
	0xf7, 0xd7, //0x000024df notl         %edi
	0x21, 0xf7, //0x000024e1 andl         %esi, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x000024e3 andl         $-1431655766, %edi
	0x45, 0x31, 0xf6, //0x000024e9 xorl         %r14d, %r14d
	0x01, 0xc7, //0x000024ec addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc6, //0x000024ee setb         %r14b
	0x01, 0xff, //0x000024f2 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x000024f4 xorl         $1431655765, %edi
	0x44, 0x21, 0xcf, //0x000024fa andl         %r9d, %edi
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x000024fd movl         $4294967295, %eax
	0x31, 0xc7, //0x00002502 xorl         %eax, %edi
	0x21, 0xfa, //0x00002504 andl         %edi, %edx
	0x4c, 0x8b, 0x55, 0xd0, //0x00002506 movq         $-48(%rbp), %r10
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000250a vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x4d, 0x89, 0xc1, //0x0000250f movq         %r8, %r9
	0x48, 0x85, 0xd2, //0x00002512 testq        %rdx, %rdx
	0x0f, 0x85, 0x3c, 0xf8, 0xff, 0xff, //0x00002515 jne          LBB0_226
	//0x0000251b LBB0_442
	0x48, 0x83, 0xc3, 0x20, //0x0000251b addq         $32, %rbx
	0x48, 0x83, 0xc1, 0xe0, //0x0000251f addq         $-32, %rcx
	//0x00002523 LBB0_443
	0x4d, 0x85, 0xf6, //0x00002523 testq        %r14, %r14
	0x0f, 0x85, 0xcd, 0x01, 0x00, 0x00, //0x00002526 jne          LBB0_469
	0x4c, 0x8b, 0x75, 0xc8, //0x0000252c movq         $-56(%rbp), %r14
	0x48, 0x85, 0xc9, //0x00002530 testq        %rcx, %rcx
	0x0f, 0x84, 0x89, 0x02, 0x00, 0x00, //0x00002533 je           LBB0_479
	//0x00002539 LBB0_445
	0x4d, 0x89, 0xe0, //0x00002539 movq         %r12, %r8
	0x4c, 0x89, 0xcf, //0x0000253c movq         %r9, %rdi
	0x48, 0xf7, 0xd7, //0x0000253f notq         %rdi
	//0x00002542 LBB0_446
	0x4c, 0x8d, 0x63, 0x01, //0x00002542 leaq         $1(%rbx), %r12
	0x0f, 0xb6, 0x13, //0x00002546 movzbl       (%rbx), %edx
	0x80, 0xfa, 0x22, //0x00002549 cmpb         $34, %dl
	0x0f, 0x84, 0x5b, 0x00, 0x00, 0x00, //0x0000254c je           LBB0_451
	0x48, 0x8d, 0x71, 0xff, //0x00002552 leaq         $-1(%rcx), %rsi
	0x80, 0xfa, 0x5c, //0x00002556 cmpb         $92, %dl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00002559 je           LBB0_449
	0x48, 0x89, 0xf1, //0x0000255f movq         %rsi, %rcx
	0x4c, 0x89, 0xe3, //0x00002562 movq         %r12, %rbx
	0x48, 0x85, 0xf6, //0x00002565 testq        %rsi, %rsi
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x00002568 jne          LBB0_446
	0xe9, 0x4f, 0x02, 0x00, 0x00, //0x0000256e jmp          LBB0_479
	//0x00002573 LBB0_449
	0x48, 0x85, 0xf6, //0x00002573 testq        %rsi, %rsi
	0x0f, 0x84, 0x92, 0x04, 0x00, 0x00, //0x00002576 je           LBB0_516
	0x49, 0x01, 0xfc, //0x0000257c addq         %rdi, %r12
	0x49, 0x83, 0xfb, 0xff, //0x0000257f cmpq         $-1, %r11
	0x4d, 0x0f, 0x44, 0xdc, //0x00002583 cmoveq       %r12, %r11
	0x48, 0x83, 0xc3, 0x02, //0x00002587 addq         $2, %rbx
	0x48, 0x83, 0xc1, 0xfe, //0x0000258b addq         $-2, %rcx
	0x48, 0x89, 0xce, //0x0000258f movq         %rcx, %rsi
	0x4c, 0x8b, 0x55, 0xd0, //0x00002592 movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x00002596 movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000259a vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xf6, //0x0000259f testq        %rsi, %rsi
	0x0f, 0x85, 0x9a, 0xff, 0xff, 0xff, //0x000025a2 jne          LBB0_446
	0xe9, 0x15, 0x02, 0x00, 0x00, //0x000025a8 jmp          LBB0_479
	//0x000025ad LBB0_451
	0x4d, 0x29, 0xcc, //0x000025ad subq         %r9, %r12
	0xe9, 0xb5, 0xf7, 0xff, 0xff, //0x000025b0 jmp          LBB0_323
	//0x000025b5 LBB0_452
	0x49, 0x83, 0xfb, 0xff, //0x000025b5 cmpq         $-1, %r11
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x000025b9 jne          LBB0_454
	0x4c, 0x89, 0xe0, //0x000025bf movq         %r12, %rax
	0x48, 0x2b, 0x45, 0xc0, //0x000025c2 subq         $-64(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xd9, //0x000025c6 bsfq         %rcx, %r11
	0x49, 0x01, 0xc3, //0x000025ca addq         %rax, %r11
	//0x000025cd LBB0_454
	0x44, 0x89, 0xf8, //0x000025cd movl         %r15d, %eax
	0xf7, 0xd0, //0x000025d0 notl         %eax
	0x21, 0xc8, //0x000025d2 andl         %ecx, %eax
	0x8d, 0x14, 0x00, //0x000025d4 leal         (%rax,%rax), %edx
	0x44, 0x09, 0xfa, //0x000025d7 orl          %r15d, %edx
	0x89, 0xd7, //0x000025da movl         %edx, %edi
	0xf7, 0xd7, //0x000025dc notl         %edi
	0x21, 0xcf, //0x000025de andl         %ecx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x000025e0 andl         $-1431655766, %edi
	0x45, 0x31, 0xff, //0x000025e6 xorl         %r15d, %r15d
	0x01, 0xc7, //0x000025e9 addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc7, //0x000025eb setb         %r15b
	0x01, 0xff, //0x000025ef addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x000025f1 xorl         $1431655765, %edi
	0x21, 0xd7, //0x000025f7 andl         %edx, %edi
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x000025f9 movl         $4294967295, %eax
	0x31, 0xf8, //0x000025fe xorl         %edi, %eax
	0x21, 0xc6, //0x00002600 andl         %eax, %esi
	0x4c, 0x8b, 0x55, 0xd0, //0x00002602 movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x00002606 movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000260a vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xf6, //0x0000260f testq        %rsi, %rsi
	0x0f, 0x85, 0xd4, 0xf7, 0xff, 0xff, //0x00002612 jne          LBB0_263
	//0x00002618 LBB0_455
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00002618 movl         $64, %ecx
	//0x0000261d LBB0_456
	0x49, 0x0f, 0xbc, 0xd1, //0x0000261d bsfq         %r9, %rdx
	0x45, 0x85, 0xc9, //0x00002621 testl        %r9d, %r9d
	0xbf, 0x40, 0x00, 0x00, 0x00, //0x00002624 movl         $64, %edi
	0x48, 0x0f, 0x45, 0xfa, //0x00002629 cmovneq      %rdx, %rdi
	0x48, 0x85, 0xf6, //0x0000262d testq        %rsi, %rsi
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00002630 je           LBB0_458
	0x4c, 0x2b, 0x65, 0xc0, //0x00002636 subq         $-64(%rbp), %r12
	0x48, 0x39, 0xcf, //0x0000263a cmpq         %rcx, %rdi
	0x0f, 0x83, 0x20, 0xf3, 0xff, 0xff, //0x0000263d jae          LBB0_322
	0xe9, 0x98, 0x03, 0x00, 0x00, //0x00002643 jmp          LBB0_417
	//0x00002648 LBB0_458
	0x45, 0x85, 0xc9, //0x00002648 testl        %r9d, %r9d
	0x0f, 0x85, 0xa9, 0x03, 0x00, 0x00, //0x0000264b jne          LBB0_514
	0x49, 0x83, 0xc4, 0x20, //0x00002651 addq         $32, %r12
	0x48, 0x83, 0xc3, 0xe0, //0x00002655 addq         $-32, %rbx
	//0x00002659 LBB0_460
	0x4d, 0x85, 0xff, //0x00002659 testq        %r15, %r15
	0x0f, 0x85, 0xd2, 0x00, 0x00, 0x00, //0x0000265c jne          LBB0_471
	0x48, 0x8b, 0x75, 0xc0, //0x00002662 movq         $-64(%rbp), %rsi
	0x48, 0x85, 0xdb, //0x00002666 testq        %rbx, %rbx
	0x0f, 0x84, 0x53, 0x01, 0x00, 0x00, //0x00002669 je           LBB0_479
	//0x0000266f LBB0_462
	0x41, 0x0f, 0xb6, 0x0c, 0x24, //0x0000266f movzbl       (%r12), %ecx
	0x80, 0xf9, 0x22, //0x00002674 cmpb         $34, %cl
	0x0f, 0x84, 0x71, 0x00, 0x00, 0x00, //0x00002677 je           LBB0_468
	0x80, 0xf9, 0x5c, //0x0000267d cmpb         $92, %cl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00002680 je           LBB0_466
	0x80, 0xf9, 0x1f, //0x00002686 cmpb         $31, %cl
	0x0f, 0x86, 0x77, 0x03, 0x00, 0x00, //0x00002689 jbe          LBB0_515
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000268f movq         $-1, %rcx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00002696 movl         $1, %edx
	0x49, 0x01, 0xd4, //0x0000269b addq         %rdx, %r12
	0x48, 0x01, 0xcb, //0x0000269e addq         %rcx, %rbx
	0x0f, 0x85, 0xc8, 0xff, 0xff, 0xff, //0x000026a1 jne          LBB0_462
	0xe9, 0x16, 0x01, 0x00, 0x00, //0x000026a7 jmp          LBB0_479
	//0x000026ac LBB0_466
	0x48, 0x83, 0xfb, 0x01, //0x000026ac cmpq         $1, %rbx
	0x0f, 0x84, 0x58, 0x03, 0x00, 0x00, //0x000026b0 je           LBB0_516
	0x4c, 0x89, 0xe0, //0x000026b6 movq         %r12, %rax
	0x48, 0x29, 0xf0, //0x000026b9 subq         %rsi, %rax
	0x49, 0x83, 0xfb, 0xff, //0x000026bc cmpq         $-1, %r11
	0x4c, 0x0f, 0x44, 0xd8, //0x000026c0 cmoveq       %rax, %r11
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x000026c4 movq         $-2, %rcx
	0xba, 0x02, 0x00, 0x00, 0x00, //0x000026cb movl         $2, %edx
	0x4c, 0x8b, 0x55, 0xd0, //0x000026d0 movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x000026d4 movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000026d8 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x49, 0x01, 0xd4, //0x000026dd addq         %rdx, %r12
	0x48, 0x01, 0xcb, //0x000026e0 addq         %rcx, %rbx
	0x0f, 0x85, 0x86, 0xff, 0xff, 0xff, //0x000026e3 jne          LBB0_462
	0xe9, 0xd4, 0x00, 0x00, 0x00, //0x000026e9 jmp          LBB0_479
	//0x000026ee LBB0_468
	0x49, 0x29, 0xf4, //0x000026ee subq         %rsi, %r12
	0x49, 0xff, 0xc4, //0x000026f1 incq         %r12
	0xe9, 0x71, 0xf6, 0xff, 0xff, //0x000026f4 jmp          LBB0_323
	//0x000026f9 LBB0_469
	0x48, 0x85, 0xc9, //0x000026f9 testq        %rcx, %rcx
	0x0f, 0x84, 0x0c, 0x03, 0x00, 0x00, //0x000026fc je           LBB0_516
	0x4c, 0x89, 0xc8, //0x00002702 movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x00002705 notq         %rax
	0x48, 0x01, 0xd8, //0x00002708 addq         %rbx, %rax
	0x49, 0x83, 0xfb, 0xff, //0x0000270b cmpq         $-1, %r11
	0x4c, 0x0f, 0x44, 0xd8, //0x0000270f cmoveq       %rax, %r11
	0x48, 0xff, 0xc3, //0x00002713 incq         %rbx
	0x48, 0xff, 0xc9, //0x00002716 decq         %rcx
	0x4c, 0x8b, 0x55, 0xd0, //0x00002719 movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x0000271d movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002721 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xc9, //0x00002726 testq        %rcx, %rcx
	0x0f, 0x85, 0x0a, 0xfe, 0xff, 0xff, //0x00002729 jne          LBB0_445
	0xe9, 0x8e, 0x00, 0x00, 0x00, //0x0000272f jmp          LBB0_479
	//0x00002734 LBB0_471
	0x48, 0x85, 0xdb, //0x00002734 testq        %rbx, %rbx
	0x0f, 0x84, 0xd1, 0x02, 0x00, 0x00, //0x00002737 je           LBB0_516
	0x48, 0x8b, 0x75, 0xc0, //0x0000273d movq         $-64(%rbp), %rsi
	0x48, 0x89, 0xf0, //0x00002741 movq         %rsi, %rax
	0x48, 0xf7, 0xd0, //0x00002744 notq         %rax
	0x4c, 0x01, 0xe0, //0x00002747 addq         %r12, %rax
	0x49, 0x83, 0xfb, 0xff, //0x0000274a cmpq         $-1, %r11
	0x4c, 0x0f, 0x44, 0xd8, //0x0000274e cmoveq       %rax, %r11
	0x49, 0xff, 0xc4, //0x00002752 incq         %r12
	0x48, 0xff, 0xcb, //0x00002755 decq         %rbx
	0x4c, 0x8b, 0x55, 0xd0, //0x00002758 movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x0000275c movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002760 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xdb, //0x00002765 testq        %rbx, %rbx
	0x0f, 0x85, 0x01, 0xff, 0xff, 0xff, //0x00002768 jne          LBB0_462
	0xe9, 0x4f, 0x00, 0x00, 0x00, //0x0000276e jmp          LBB0_479
	//0x00002773 LBB0_473
	0x4d, 0x89, 0x2a, //0x00002773 movq         %r13, (%r10)
	//0x00002776 LBB0_474
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00002776 movq         $-1, %rcx
	0xe9, 0x67, 0x00, 0x00, 0x00, //0x0000277d jmp          LBB0_501
	//0x00002782 LBB0_489
	0x48, 0xc7, 0xc1, 0xf9, 0xff, 0xff, 0xff, //0x00002782 movq         $-7, %rcx
	0xe9, 0x5b, 0x00, 0x00, 0x00, //0x00002789 jmp          LBB0_501
	//0x0000278e LBB0_475
	0x49, 0x83, 0xfc, 0xff, //0x0000278e cmpq         $-1, %r12
	0x0f, 0x85, 0x91, 0x00, 0x00, 0x00, //0x00002792 jne          LBB0_487
	//0x00002798 LBB0_476
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00002798 movq         $-1, %r12
	0x4c, 0x8b, 0x45, 0xb8, //0x0000279f movq         $-72(%rbp), %r8
	0xe9, 0x81, 0x00, 0x00, 0x00, //0x000027a3 jmp          LBB0_487
	//0x000027a8 LBB0_477
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000027a8 movq         $-1, %rcx
	0x48, 0x8b, 0x55, 0xd0, //0x000027af movq         $-48(%rbp), %rdx
	0xe9, 0x1d, 0x00, 0x00, 0x00, //0x000027b3 jmp          LBB0_481
	//0x000027b8 LBB0_478
	0x49, 0x83, 0xfc, 0xff, //0x000027b8 cmpq         $-1, %r12
	0x0f, 0x85, 0x91, 0x00, 0x00, 0x00, //0x000027bc jne          LBB0_506
	//0x000027c2 LBB0_479
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x000027c2 movq         $-1, %r12
	0x4c, 0x8b, 0x5d, 0xb8, //0x000027c9 movq         $-72(%rbp), %r11
	0xe9, 0x81, 0x00, 0x00, 0x00, //0x000027cd jmp          LBB0_506
	//0x000027d2 LBB0_480
	0x4c, 0x89, 0xf1, //0x000027d2 movq         %r14, %rcx
	//0x000027d5 LBB0_481
	0x48, 0x8b, 0x02, //0x000027d5 movq         (%rdx), %rax
	0x48, 0x29, 0xc8, //0x000027d8 subq         %rcx, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x000027db addq         $-2, %rax
	0x48, 0x89, 0x02, //0x000027df movq         %rax, (%rdx)
	//0x000027e2 LBB0_500
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x000027e2 movq         $-2, %rcx
	//0x000027e9 LBB0_501
	0x48, 0x89, 0xc8, //0x000027e9 movq         %rcx, %rax
	0x48, 0x83, 0xc4, 0x48, //0x000027ec addq         $72, %rsp
	0x5b, //0x000027f0 popq         %rbx
	0x41, 0x5c, //0x000027f1 popq         %r12
	0x41, 0x5d, //0x000027f3 popq         %r13
	0x41, 0x5e, //0x000027f5 popq         %r14
	0x41, 0x5f, //0x000027f7 popq         %r15
	0x5d, //0x000027f9 popq         %rbp
	0xc5, 0xf8, 0x77, //0x000027fa vzeroupper   
	0xc3, //0x000027fd retq         
	//0x000027fe LBB0_490
	0x49, 0x89, 0x12, //0x000027fe movq         %rdx, (%r10)
	0xe9, 0xe3, 0xff, 0xff, 0xff, //0x00002801 jmp          LBB0_501
	//0x00002806 LBB0_482
	0x49, 0x83, 0xf8, 0xff, //0x00002806 cmpq         $-1, %r8
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x0000280a jne          LBB0_485
	0x48, 0x0f, 0xbc, 0xc6, //0x00002810 bsfq         %rsi, %rax
	0x4c, 0x2b, 0x65, 0xc0, //0x00002814 subq         $-64(%rbp), %r12
	0x49, 0x01, 0xc4, //0x00002818 addq         %rax, %r12
	//0x0000281b LBB0_484
	0x4d, 0x89, 0xe0, //0x0000281b movq         %r12, %r8
	//0x0000281e LBB0_485
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x0000281e movq         $-2, %r12
	//0x00002825 LBB0_486
	0x4c, 0x8b, 0x55, 0xd0, //0x00002825 movq         $-48(%rbp), %r10
	//0x00002829 LBB0_487
	0x4d, 0x89, 0x02, //0x00002829 movq         %r8, (%r10)
	0x4c, 0x89, 0xe1, //0x0000282c movq         %r12, %rcx
	0xe9, 0xb5, 0xff, 0xff, 0xff, //0x0000282f jmp          LBB0_501
	//0x00002834 LBB0_502
	0x49, 0x83, 0xfb, 0xff, //0x00002834 cmpq         $-1, %r11
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x00002838 jne          LBB0_505
	0x48, 0x0f, 0xbc, 0xc6, //0x0000283e bsfq         %rsi, %rax
	0x4c, 0x2b, 0x65, 0xc0, //0x00002842 subq         $-64(%rbp), %r12
	0x49, 0x01, 0xc4, //0x00002846 addq         %rax, %r12
	//0x00002849 LBB0_504
	0x4d, 0x89, 0xe3, //0x00002849 movq         %r12, %r11
	//0x0000284c LBB0_505
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x0000284c movq         $-2, %r12
	//0x00002853 LBB0_506
	0x4d, 0x89, 0x1a, //0x00002853 movq         %r11, (%r10)
	0x4c, 0x89, 0xe1, //0x00002856 movq         %r12, %rcx
	0xe9, 0x8b, 0xff, 0xff, 0xff, //0x00002859 jmp          LBB0_501
	//0x0000285e LBB0_233
	0x4d, 0x89, 0x2a, //0x0000285e movq         %r13, (%r10)
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00002861 movq         $-2, %rcx
	0x41, 0x80, 0x3f, 0x6e, //0x00002868 cmpb         $110, (%r15)
	0x0f, 0x85, 0x77, 0xff, 0xff, 0xff, //0x0000286c jne          LBB0_501
	0x49, 0x8d, 0x45, 0x01, //0x00002872 leaq         $1(%r13), %rax
	0x49, 0x89, 0x02, //0x00002876 movq         %rax, (%r10)
	0x43, 0x80, 0x7c, 0x29, 0x01, 0x75, //0x00002879 cmpb         $117, $1(%r9,%r13)
	0x0f, 0x85, 0x64, 0xff, 0xff, 0xff, //0x0000287f jne          LBB0_501
	0x49, 0x8d, 0x45, 0x02, //0x00002885 leaq         $2(%r13), %rax
	0x49, 0x89, 0x02, //0x00002889 movq         %rax, (%r10)
	0x43, 0x80, 0x7c, 0x29, 0x02, 0x6c, //0x0000288c cmpb         $108, $2(%r9,%r13)
	0x0f, 0x85, 0x51, 0xff, 0xff, 0xff, //0x00002892 jne          LBB0_501
	0x49, 0x8d, 0x45, 0x03, //0x00002898 leaq         $3(%r13), %rax
	0x49, 0x89, 0x02, //0x0000289c movq         %rax, (%r10)
	0x43, 0x80, 0x7c, 0x29, 0x03, 0x6c, //0x0000289f cmpb         $108, $3(%r9,%r13)
	0x0f, 0x85, 0x3e, 0xff, 0xff, 0xff, //0x000028a5 jne          LBB0_501
	0xe9, 0xa2, 0x00, 0x00, 0x00, //0x000028ab jmp          LBB0_237
	//0x000028b0 LBB0_491
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x000028b0 movq         $-2, %rcx
	0x80, 0xfa, 0x61, //0x000028b7 cmpb         $97, %dl
	0x0f, 0x85, 0x29, 0xff, 0xff, 0xff, //0x000028ba jne          LBB0_501
	0x49, 0x8d, 0x45, 0x02, //0x000028c0 leaq         $2(%r13), %rax
	0x49, 0x89, 0x02, //0x000028c4 movq         %rax, (%r10)
	0x43, 0x80, 0x7c, 0x29, 0x02, 0x6c, //0x000028c7 cmpb         $108, $2(%r9,%r13)
	0x0f, 0x85, 0x16, 0xff, 0xff, 0xff, //0x000028cd jne          LBB0_501
	0x49, 0x8d, 0x45, 0x03, //0x000028d3 leaq         $3(%r13), %rax
	0x49, 0x89, 0x02, //0x000028d7 movq         %rax, (%r10)
	0x43, 0x80, 0x7c, 0x29, 0x03, 0x73, //0x000028da cmpb         $115, $3(%r9,%r13)
	0x0f, 0x85, 0x03, 0xff, 0xff, 0xff, //0x000028e0 jne          LBB0_501
	0x49, 0x8d, 0x45, 0x04, //0x000028e6 leaq         $4(%r13), %rax
	0x49, 0x89, 0x02, //0x000028ea movq         %rax, (%r10)
	0x43, 0x80, 0x7c, 0x29, 0x04, 0x65, //0x000028ed cmpb         $101, $4(%r9,%r13)
	0x0f, 0x85, 0xf0, 0xfe, 0xff, 0xff, //0x000028f3 jne          LBB0_501
	0x49, 0x83, 0xc5, 0x05, //0x000028f9 addq         $5, %r13
	0x4d, 0x89, 0x2a, //0x000028fd movq         %r13, (%r10)
	0xe9, 0xe4, 0xfe, 0xff, 0xff, //0x00002900 jmp          LBB0_501
	//0x00002905 LBB0_245
	0x4d, 0x89, 0x2a, //0x00002905 movq         %r13, (%r10)
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00002908 movq         $-2, %rcx
	0x41, 0x80, 0x3f, 0x74, //0x0000290f cmpb         $116, (%r15)
	0x0f, 0x85, 0xd0, 0xfe, 0xff, 0xff, //0x00002913 jne          LBB0_501
	0x49, 0x8d, 0x45, 0x01, //0x00002919 leaq         $1(%r13), %rax
	0x49, 0x89, 0x02, //0x0000291d movq         %rax, (%r10)
	0x43, 0x80, 0x7c, 0x29, 0x01, 0x72, //0x00002920 cmpb         $114, $1(%r9,%r13)
	0x0f, 0x85, 0xbd, 0xfe, 0xff, 0xff, //0x00002926 jne          LBB0_501
	0x49, 0x8d, 0x45, 0x02, //0x0000292c leaq         $2(%r13), %rax
	0x49, 0x89, 0x02, //0x00002930 movq         %rax, (%r10)
	0x43, 0x80, 0x7c, 0x29, 0x02, 0x75, //0x00002933 cmpb         $117, $2(%r9,%r13)
	0x0f, 0x85, 0xaa, 0xfe, 0xff, 0xff, //0x00002939 jne          LBB0_501
	0x49, 0x8d, 0x45, 0x03, //0x0000293f leaq         $3(%r13), %rax
	0x49, 0x89, 0x02, //0x00002943 movq         %rax, (%r10)
	0x43, 0x80, 0x7c, 0x29, 0x03, 0x65, //0x00002946 cmpb         $101, $3(%r9,%r13)
	0x0f, 0x85, 0x97, 0xfe, 0xff, 0xff, //0x0000294c jne          LBB0_501
	//0x00002952 LBB0_237
	0x49, 0x83, 0xc5, 0x04, //0x00002952 addq         $4, %r13
	0x4d, 0x89, 0x2a, //0x00002956 movq         %r13, (%r10)
	0xe9, 0x8b, 0xfe, 0xff, 0xff, //0x00002959 jmp          LBB0_501
	//0x0000295e LBB0_497
	0x4d, 0x8b, 0x22, //0x0000295e movq         (%r10), %r12
	//0x00002961 LBB0_498
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00002961 movq         $-1, %r15
	//0x00002968 LBB0_499
	0x49, 0xf7, 0xd7, //0x00002968 notq         %r15
	0x4d, 0x01, 0xe7, //0x0000296b addq         %r12, %r15
	0x4d, 0x89, 0x3a, //0x0000296e movq         %r15, (%r10)
	0xe9, 0x6c, 0xfe, 0xff, 0xff, //0x00002971 jmp          LBB0_500
	//0x00002976 LBB0_510
	0x4c, 0x89, 0x65, 0xb8, //0x00002976 movq         %r12, $-72(%rbp)
	0xe9, 0x43, 0xfe, 0xff, 0xff, //0x0000297a jmp          LBB0_479
	//0x0000297f LBB0_507
	0x4c, 0x89, 0x65, 0xb8, //0x0000297f movq         %r12, $-72(%rbp)
	0xe9, 0x10, 0xfe, 0xff, 0xff, //0x00002983 jmp          LBB0_476
	//0x00002988 LBB0_508
	0x4c, 0x89, 0x65, 0xb8, //0x00002988 movq         %r12, $-72(%rbp)
	0x4c, 0x8b, 0x55, 0xd0, //0x0000298c movq         $-48(%rbp), %r10
	0xe9, 0x03, 0xfe, 0xff, 0xff, //0x00002990 jmp          LBB0_476
	//0x00002995 LBB0_509
	0x4c, 0x01, 0xe2, //0x00002995 addq         %r12, %rdx
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x00002998 movq         $-2, %r12
	0x49, 0x89, 0xd0, //0x0000299f movq         %rdx, %r8
	0xe9, 0x82, 0xfe, 0xff, 0xff, //0x000029a2 jmp          LBB0_487
	//0x000029a7 LBB0_433
	0x4c, 0x8b, 0x55, 0xd0, //0x000029a7 movq         $-48(%rbp), %r10
	0xe9, 0xe8, 0xfd, 0xff, 0xff, //0x000029ab jmp          LBB0_476
	//0x000029b0 LBB0_129
	0x4c, 0x01, 0xe2, //0x000029b0 addq         %r12, %rdx
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x000029b3 movq         $-2, %r12
	0x49, 0x89, 0xd3, //0x000029ba movq         %rdx, %r11
	0xe9, 0x91, 0xfe, 0xff, 0xff, //0x000029bd jmp          LBB0_506
	//0x000029c2 LBB0_511
	0x4c, 0x01, 0xe7, //0x000029c2 addq         %r12, %rdi
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x000029c5 movq         $-2, %r12
	0x49, 0x89, 0xf8, //0x000029cc movq         %rdi, %r8
	0xe9, 0x51, 0xfe, 0xff, 0xff, //0x000029cf jmp          LBB0_486
	//0x000029d4 LBB0_512
	0x4c, 0x2b, 0x65, 0xc0, //0x000029d4 subq         $-64(%rbp), %r12
	0x49, 0x01, 0xd4, //0x000029d8 addq         %rdx, %r12
	0xe9, 0x3b, 0xfe, 0xff, 0xff, //0x000029db jmp          LBB0_484
	//0x000029e0 LBB0_417
	0x4c, 0x01, 0xe7, //0x000029e0 addq         %r12, %rdi
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x000029e3 movq         $-2, %r12
	0x49, 0x89, 0xfb, //0x000029ea movq         %rdi, %r11
	0xe9, 0x61, 0xfe, 0xff, 0xff, //0x000029ed jmp          LBB0_506
	//0x000029f2 LBB0_513
	0x49, 0x29, 0xc4, //0x000029f2 subq         %rax, %r12
	0xe9, 0x21, 0xfe, 0xff, 0xff, //0x000029f5 jmp          LBB0_484
	//0x000029fa LBB0_514
	0x4c, 0x2b, 0x65, 0xc0, //0x000029fa subq         $-64(%rbp), %r12
	0x49, 0x01, 0xd4, //0x000029fe addq         %rdx, %r12
	0xe9, 0x43, 0xfe, 0xff, 0xff, //0x00002a01 jmp          LBB0_504
	//0x00002a06 LBB0_515
	0x49, 0x29, 0xf4, //0x00002a06 subq         %rsi, %r12
	0xe9, 0x3b, 0xfe, 0xff, 0xff, //0x00002a09 jmp          LBB0_504
	//0x00002a0e LBB0_516
	0x4c, 0x8b, 0x55, 0xd0, //0x00002a0e movq         $-48(%rbp), %r10
	0xe9, 0xab, 0xfd, 0xff, 0xff, //0x00002a12 jmp          LBB0_479
	0x90, //0x00002a17 .p2align 2, 0x90
	// // .set L0_0_set_39, LBB0_39-LJTI0_0
	// // .set L0_0_set_63, LBB0_63-LJTI0_0
	// // .set L0_0_set_44, LBB0_44-LJTI0_0
	// // .set L0_0_set_61, LBB0_61-LJTI0_0
	// // .set L0_0_set_42, LBB0_42-LJTI0_0
	// // .set L0_0_set_65, LBB0_65-LJTI0_0
	//0x00002a18 LJTI0_0
	0xc4, 0xda, 0xff, 0xff, //0x00002a18 .long L0_0_set_39
	0x6c, 0xdc, 0xff, 0xff, //0x00002a1c .long L0_0_set_63
	0xfb, 0xda, 0xff, 0xff, //0x00002a20 .long L0_0_set_44
	0x55, 0xdc, 0xff, 0xff, //0x00002a24 .long L0_0_set_61
	0xdb, 0xda, 0xff, 0xff, //0x00002a28 .long L0_0_set_42
	0x97, 0xdc, 0xff, 0xff, //0x00002a2c .long L0_0_set_65
	// // .set L0_1_set_501, LBB0_501-LJTI0_1
	// // .set L0_1_set_500, LBB0_500-LJTI0_1
	// // .set L0_1_set_211, LBB0_211-LJTI0_1
	// // .set L0_1_set_227, LBB0_227-LJTI0_1
	// // .set L0_1_set_69, LBB0_69-LJTI0_1
	// // .set L0_1_set_209, LBB0_209-LJTI0_1
	// // .set L0_1_set_240, LBB0_240-LJTI0_1
	// // .set L0_1_set_231, LBB0_231-LJTI0_1
	// // .set L0_1_set_243, LBB0_243-LJTI0_1
	// // .set L0_1_set_238, LBB0_238-LJTI0_1
	//0x00002a30 LJTI0_1
	0xb9, 0xfd, 0xff, 0xff, //0x00002a30 .long L0_1_set_501
	0xb2, 0xfd, 0xff, 0xff, //0x00002a34 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a38 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a3c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a40 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a44 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a48 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a4c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a50 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a54 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a58 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a5c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a60 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a64 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a68 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a6c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a70 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a74 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a78 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a7c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a80 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a84 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a88 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a8c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a90 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a94 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a98 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a9c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002aa0 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002aa4 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002aa8 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002aac .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002ab0 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002ab4 .long L0_1_set_500
	0x1e, 0xe7, 0xff, 0xff, //0x00002ab8 .long L0_1_set_211
	0xb2, 0xfd, 0xff, 0xff, //0x00002abc .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002ac0 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002ac4 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002ac8 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002acc .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002ad0 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002ad4 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002ad8 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002adc .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002ae0 .long L0_1_set_500
	0x6d, 0xe8, 0xff, 0xff, //0x00002ae4 .long L0_1_set_227
	0xb2, 0xfd, 0xff, 0xff, //0x00002ae8 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002aec .long L0_1_set_500
	0xcb, 0xdc, 0xff, 0xff, //0x00002af0 .long L0_1_set_69
	0xcb, 0xdc, 0xff, 0xff, //0x00002af4 .long L0_1_set_69
	0xcb, 0xdc, 0xff, 0xff, //0x00002af8 .long L0_1_set_69
	0xcb, 0xdc, 0xff, 0xff, //0x00002afc .long L0_1_set_69
	0xcb, 0xdc, 0xff, 0xff, //0x00002b00 .long L0_1_set_69
	0xcb, 0xdc, 0xff, 0xff, //0x00002b04 .long L0_1_set_69
	0xcb, 0xdc, 0xff, 0xff, //0x00002b08 .long L0_1_set_69
	0xcb, 0xdc, 0xff, 0xff, //0x00002b0c .long L0_1_set_69
	0xcb, 0xdc, 0xff, 0xff, //0x00002b10 .long L0_1_set_69
	0xcb, 0xdc, 0xff, 0xff, //0x00002b14 .long L0_1_set_69
	0xb2, 0xfd, 0xff, 0xff, //0x00002b18 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b1c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b20 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b24 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b28 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b2c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b30 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b34 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b38 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b3c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b40 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b44 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b48 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b4c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b50 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b54 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b58 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b5c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b60 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b64 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b68 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b6c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b70 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b74 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b78 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b7c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b80 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b84 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b88 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b8c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b90 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b94 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b98 .long L0_1_set_500
	0xfa, 0xe6, 0xff, 0xff, //0x00002b9c .long L0_1_set_209
	0xb2, 0xfd, 0xff, 0xff, //0x00002ba0 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002ba4 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002ba8 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bac .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bb0 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bb4 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bb8 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bbc .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bc0 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bc4 .long L0_1_set_500
	0xf8, 0xe8, 0xff, 0xff, //0x00002bc8 .long L0_1_set_240
	0xb2, 0xfd, 0xff, 0xff, //0x00002bcc .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bd0 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bd4 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bd8 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bdc .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002be0 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002be4 .long L0_1_set_500
	0xad, 0xe8, 0xff, 0xff, //0x00002be8 .long L0_1_set_231
	0xb2, 0xfd, 0xff, 0xff, //0x00002bec .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bf0 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bf4 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bf8 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bfc .long L0_1_set_500
	0x38, 0xe9, 0xff, 0xff, //0x00002c00 .long L0_1_set_243
	0xb2, 0xfd, 0xff, 0xff, //0x00002c04 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002c08 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002c0c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002c10 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002c14 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002c18 .long L0_1_set_500
	0xd4, 0xe8, 0xff, 0xff, //0x00002c1c .long L0_1_set_238
	// // .set L0_2_set_311, LBB0_311-LJTI0_2
	// // .set L0_2_set_326, LBB0_326-LJTI0_2
	// // .set L0_2_set_318, LBB0_318-LJTI0_2
	// // .set L0_2_set_313, LBB0_313-LJTI0_2
	// // .set L0_2_set_316, LBB0_316-LJTI0_2
	//0x00002c20 LJTI0_2
	0x77, 0xec, 0xff, 0xff, //0x00002c20 .long L0_2_set_311
	0x54, 0xed, 0xff, 0xff, //0x00002c24 .long L0_2_set_326
	0x77, 0xec, 0xff, 0xff, //0x00002c28 .long L0_2_set_311
	0xdb, 0xec, 0xff, 0xff, //0x00002c2c .long L0_2_set_318
	0x54, 0xed, 0xff, 0xff, //0x00002c30 .long L0_2_set_326
	0x90, 0xec, 0xff, 0xff, //0x00002c34 .long L0_2_set_313
	0x90, 0xec, 0xff, 0xff, //0x00002c38 .long L0_2_set_313
	0x90, 0xec, 0xff, 0xff, //0x00002c3c .long L0_2_set_313
	0x90, 0xec, 0xff, 0xff, //0x00002c40 .long L0_2_set_313
	0x90, 0xec, 0xff, 0xff, //0x00002c44 .long L0_2_set_313
	0x90, 0xec, 0xff, 0xff, //0x00002c48 .long L0_2_set_313
	0x90, 0xec, 0xff, 0xff, //0x00002c4c .long L0_2_set_313
	0x90, 0xec, 0xff, 0xff, //0x00002c50 .long L0_2_set_313
	0x90, 0xec, 0xff, 0xff, //0x00002c54 .long L0_2_set_313
	0x90, 0xec, 0xff, 0xff, //0x00002c58 .long L0_2_set_313
	0x54, 0xed, 0xff, 0xff, //0x00002c5c .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c60 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c64 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c68 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c6c .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c70 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c74 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c78 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c7c .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c80 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c84 .long L0_2_set_326
	0xc0, 0xec, 0xff, 0xff, //0x00002c88 .long L0_2_set_316
	0x54, 0xed, 0xff, 0xff, //0x00002c8c .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c90 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c94 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c98 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c9c .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002ca0 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002ca4 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002ca8 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cac .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cb0 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cb4 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cb8 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cbc .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cc0 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cc4 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cc8 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002ccc .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cd0 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cd4 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cd8 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cdc .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002ce0 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002ce4 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002ce8 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cec .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cf0 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cf4 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cf8 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cfc .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002d00 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002d04 .long L0_2_set_326
	0xc0, 0xec, 0xff, 0xff, //0x00002d08 .long L0_2_set_316
	// // .set L0_3_set_171, LBB0_171-LJTI0_3
	// // .set L0_3_set_187, LBB0_187-LJTI0_3
	// // .set L0_3_set_178, LBB0_178-LJTI0_3
	// // .set L0_3_set_173, LBB0_173-LJTI0_3
	// // .set L0_3_set_176, LBB0_176-LJTI0_3
	//0x00002d0c LJTI0_3
	0x56, 0xdd, 0xff, 0xff, //0x00002d0c .long L0_3_set_171
	0xb1, 0xe2, 0xff, 0xff, //0x00002d10 .long L0_3_set_187
	0x56, 0xdd, 0xff, 0xff, //0x00002d14 .long L0_3_set_171
	0xbf, 0xdd, 0xff, 0xff, //0x00002d18 .long L0_3_set_178
	0xb1, 0xe2, 0xff, 0xff, //0x00002d1c .long L0_3_set_187
	0x74, 0xdd, 0xff, 0xff, //0x00002d20 .long L0_3_set_173
	0x74, 0xdd, 0xff, 0xff, //0x00002d24 .long L0_3_set_173
	0x74, 0xdd, 0xff, 0xff, //0x00002d28 .long L0_3_set_173
	0x74, 0xdd, 0xff, 0xff, //0x00002d2c .long L0_3_set_173
	0x74, 0xdd, 0xff, 0xff, //0x00002d30 .long L0_3_set_173
	0x74, 0xdd, 0xff, 0xff, //0x00002d34 .long L0_3_set_173
	0x74, 0xdd, 0xff, 0xff, //0x00002d38 .long L0_3_set_173
	0x74, 0xdd, 0xff, 0xff, //0x00002d3c .long L0_3_set_173
	0x74, 0xdd, 0xff, 0xff, //0x00002d40 .long L0_3_set_173
	0x74, 0xdd, 0xff, 0xff, //0x00002d44 .long L0_3_set_173
	0xb1, 0xe2, 0xff, 0xff, //0x00002d48 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d4c .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d50 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d54 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d58 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d5c .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d60 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d64 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d68 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d6c .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d70 .long L0_3_set_187
	0xa4, 0xdd, 0xff, 0xff, //0x00002d74 .long L0_3_set_176
	0xb1, 0xe2, 0xff, 0xff, //0x00002d78 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d7c .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d80 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d84 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d88 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d8c .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d90 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d94 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d98 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d9c .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002da0 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002da4 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002da8 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002dac .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002db0 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002db4 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002db8 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002dbc .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002dc0 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002dc4 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002dc8 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002dcc .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002dd0 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002dd4 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002dd8 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002ddc .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002de0 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002de4 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002de8 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002dec .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002df0 .long L0_3_set_187
	0xa4, 0xdd, 0xff, 0xff, //0x00002df4 .long L0_3_set_176
	//0x00002df8 .p2align 2, 0x00
	//0x00002df8 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00002df8 .long 2
}
 
