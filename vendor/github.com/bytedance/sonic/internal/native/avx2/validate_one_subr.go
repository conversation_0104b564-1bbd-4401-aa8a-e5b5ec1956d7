// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx2

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__validate_one = 432
)

const (
    _stack__validate_one = 120
)

const (
    _size__validate_one = 10328
)

var (
    _pcsp__validate_one = [][2]uint32{
        {1, 0},
        {4, 8},
        {6, 16},
        {8, 24},
        {10, 32},
        {12, 40},
        {13, 48},
        {9772, 120},
        {9776, 48},
        {9777, 40},
        {9779, 32},
        {9781, 24},
        {9783, 16},
        {9785, 8},
        {9789, 0},
        {10328, 120},
    }
)

var _cfunc_validate_one = []loader.CFunc{
    {"_validate_one_entry", 0,  _entry__validate_one, 0, nil},
    {"_validate_one", _entry__validate_one, _size__validate_one, _stack__validate_one, _pcsp__validate_one},
}
