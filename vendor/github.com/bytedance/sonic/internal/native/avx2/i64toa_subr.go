// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx2

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__i64toa = 64
)

const (
    _stack__i64toa = 8
)

const (
    _size__i64toa = 2288
)

var (
    _pcsp__i64toa = [][2]uint32{
        {1, 0},
        {170, 8},
        {171, 0},
        {505, 8},
        {506, 0},
        {637, 8},
        {638, 0},
        {1103, 8},
        {1104, 0},
        {1240, 8},
        {1241, 0},
        {1544, 8},
        {1545, 0},
        {1907, 8},
        {1908, 0},
        {2276, 8},
        {2278, 0},
    }
)

var _cfunc_i64toa = []loader.CFunc{
    {"_i64toa_entry", 0,  _entry__i64toa, 0, nil},
    {"_i64toa", _entry__i64toa, _size__i64toa, _stack__i64toa, _pcsp__i64toa},
}
