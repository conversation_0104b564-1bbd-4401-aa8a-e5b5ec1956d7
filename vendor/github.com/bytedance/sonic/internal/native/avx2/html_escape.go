// Code generated by <PERSON><PERSON><PERSON>, DO NOT EDIT.

// Code generated by <PERSON><PERSON><PERSON>, DO NOT EDIT.

/*
 * Copyright 2021 ByteDance Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package avx2

import (
    `unsafe`

    `github.com/bytedance/sonic/internal/rt`
)

var F_html_escape func(sp unsafe.Pointer, nb int, dp unsafe.Pointer, dn unsafe.Pointer) (ret int)

var S_html_escape uintptr

//go:nosplit
func html_escape(sp unsafe.Pointer, nb int, dp unsafe.Pointer, dn *int) (ret int) {
    return F_html_escape(rt.NoEscape(sp), nb, dp, rt.NoEscape(unsafe.Pointer(dn)))
}
