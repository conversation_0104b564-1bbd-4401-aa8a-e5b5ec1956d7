// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_skip_one_fast = []byte{
	// .p2align 5, 0x00
	// LCPI0_0
	0x20, // .byte 32
	0x00, //0x00000001 .byte 0
	0x00, //0x00000002 .byte 0
	0x00, //0x00000003 .byte 0
	0x00, //0x00000004 .byte 0
	0x00, //0x00000005 .byte 0
	0x00, //0x00000006 .byte 0
	0x00, //0x00000007 .byte 0
	0x00, //0x00000008 .byte 0
	0x09, //0x00000009 .byte 9
	0x0a, //0x0000000a .byte 10
	0x00, //0x0000000b .byte 0
	0x00, //0x0000000c .byte 0
	0x0d, //0x0000000d .byte 13
	0x00, //0x0000000e .byte 0
	0x00, //0x0000000f .byte 0
	0x20, //0x00000010 .byte 32
	0x00, //0x00000011 .byte 0
	0x00, //0x00000012 .byte 0
	0x00, //0x00000013 .byte 0
	0x00, //0x00000014 .byte 0
	0x00, //0x00000015 .byte 0
	0x00, //0x00000016 .byte 0
	0x00, //0x00000017 .byte 0
	0x00, //0x00000018 .byte 0
	0x09, //0x00000019 .byte 9
	0x0a, //0x0000001a .byte 10
	0x00, //0x0000001b .byte 0
	0x00, //0x0000001c .byte 0
	0x0d, //0x0000001d .byte 13
	0x00, //0x0000001e .byte 0
	0x00, //0x0000001f .byte 0
	//0x00000020 LCPI0_1
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x00000020 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x00000030 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	//0x00000040 LCPI0_2
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000040 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000050 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000060 LCPI0_3
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x00000060 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x00000070 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	//0x00000080 LCPI0_7
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000080 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000090 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x000000a0 LCPI0_8
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x000000a0 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x000000b0 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x000000c0 LCPI0_9
	0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, //0x000000c0 QUAD $0x7b7b7b7b7b7b7b7b; QUAD $0x7b7b7b7b7b7b7b7b  // .space 16, '{{{{{{{{{{{{{{{{'
	0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, //0x000000d0 QUAD $0x7b7b7b7b7b7b7b7b; QUAD $0x7b7b7b7b7b7b7b7b  // .space 16, '{{{{{{{{{{{{{{{{'
	//0x000000e0 LCPI0_10
	0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, //0x000000e0 QUAD $0x5b5b5b5b5b5b5b5b; QUAD $0x5b5b5b5b5b5b5b5b  // .space 16, '[[[[[[[[[[[[[[[['
	0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, //0x000000f0 QUAD $0x5b5b5b5b5b5b5b5b; QUAD $0x5b5b5b5b5b5b5b5b  // .space 16, '[[[[[[[[[[[[[[[['
	//0x00000100 LCPI0_11
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000100 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000110 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	//0x00000120 .p2align 4, 0x00
	//0x00000120 LCPI0_4
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x00000120 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	//0x00000130 LCPI0_5
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000130 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000140 LCPI0_6
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x00000140 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	//0x00000150 .p2align 4, 0x90
	//0x00000150 _skip_one_fast
	0x55, //0x00000150 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000151 movq         %rsp, %rbp
	0x41, 0x57, //0x00000154 pushq        %r15
	0x41, 0x56, //0x00000156 pushq        %r14
	0x41, 0x55, //0x00000158 pushq        %r13
	0x41, 0x54, //0x0000015a pushq        %r12
	0x53, //0x0000015c pushq        %rbx
	0x48, 0x81, 0xec, 0x80, 0x00, 0x00, 0x00, //0x0000015d subq         $128, %rsp
	0x4c, 0x8b, 0x37, //0x00000164 movq         (%rdi), %r14
	0x4c, 0x8b, 0x47, 0x08, //0x00000167 movq         $8(%rdi), %r8
	0x48, 0x8b, 0x06, //0x0000016b movq         (%rsi), %rax
	0x4c, 0x39, 0xc0, //0x0000016e cmpq         %r8, %rax
	0x0f, 0x83, 0x2a, 0x00, 0x00, 0x00, //0x00000171 jae          LBB0_5
	0x41, 0x8a, 0x0c, 0x06, //0x00000177 movb         (%r14,%rax), %cl
	0x80, 0xf9, 0x0d, //0x0000017b cmpb         $13, %cl
	0x0f, 0x84, 0x1d, 0x00, 0x00, 0x00, //0x0000017e je           LBB0_5
	0x80, 0xf9, 0x20, //0x00000184 cmpb         $32, %cl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00000187 je           LBB0_5
	0x80, 0xc1, 0xf7, //0x0000018d addb         $-9, %cl
	0x80, 0xf9, 0x01, //0x00000190 cmpb         $1, %cl
	0x0f, 0x86, 0x08, 0x00, 0x00, 0x00, //0x00000193 jbe          LBB0_5
	0x48, 0x89, 0xc1, //0x00000199 movq         %rax, %rcx
	0xe9, 0x89, 0x01, 0x00, 0x00, //0x0000019c jmp          LBB0_32
	//0x000001a1 LBB0_5
	0x48, 0x8d, 0x48, 0x01, //0x000001a1 leaq         $1(%rax), %rcx
	0x4c, 0x39, 0xc1, //0x000001a5 cmpq         %r8, %rcx
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x000001a8 jae          LBB0_9
	0x41, 0x8a, 0x14, 0x0e, //0x000001ae movb         (%r14,%rcx), %dl
	0x80, 0xfa, 0x0d, //0x000001b2 cmpb         $13, %dl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x000001b5 je           LBB0_9
	0x80, 0xfa, 0x20, //0x000001bb cmpb         $32, %dl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x000001be je           LBB0_9
	0x80, 0xc2, 0xf7, //0x000001c4 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x000001c7 cmpb         $1, %dl
	0x0f, 0x87, 0x5a, 0x01, 0x00, 0x00, //0x000001ca ja           LBB0_32
	//0x000001d0 LBB0_9
	0x48, 0x8d, 0x48, 0x02, //0x000001d0 leaq         $2(%rax), %rcx
	0x4c, 0x39, 0xc1, //0x000001d4 cmpq         %r8, %rcx
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x000001d7 jae          LBB0_13
	0x41, 0x8a, 0x14, 0x0e, //0x000001dd movb         (%r14,%rcx), %dl
	0x80, 0xfa, 0x0d, //0x000001e1 cmpb         $13, %dl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x000001e4 je           LBB0_13
	0x80, 0xfa, 0x20, //0x000001ea cmpb         $32, %dl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x000001ed je           LBB0_13
	0x80, 0xc2, 0xf7, //0x000001f3 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x000001f6 cmpb         $1, %dl
	0x0f, 0x87, 0x2b, 0x01, 0x00, 0x00, //0x000001f9 ja           LBB0_32
	//0x000001ff LBB0_13
	0x48, 0x8d, 0x48, 0x03, //0x000001ff leaq         $3(%rax), %rcx
	0x4c, 0x39, 0xc1, //0x00000203 cmpq         %r8, %rcx
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x00000206 jae          LBB0_17
	0x41, 0x8a, 0x14, 0x0e, //0x0000020c movb         (%r14,%rcx), %dl
	0x80, 0xfa, 0x0d, //0x00000210 cmpb         $13, %dl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000213 je           LBB0_17
	0x80, 0xfa, 0x20, //0x00000219 cmpb         $32, %dl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000021c je           LBB0_17
	0x80, 0xc2, 0xf7, //0x00000222 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00000225 cmpb         $1, %dl
	0x0f, 0x87, 0xfc, 0x00, 0x00, 0x00, //0x00000228 ja           LBB0_32
	//0x0000022e LBB0_17
	0x48, 0x8d, 0x48, 0x04, //0x0000022e leaq         $4(%rax), %rcx
	0x4c, 0x89, 0xc2, //0x00000232 movq         %r8, %rdx
	0x48, 0x29, 0xca, //0x00000235 subq         %rcx, %rdx
	0x0f, 0x86, 0xbf, 0x00, 0x00, 0x00, //0x00000238 jbe          LBB0_30
	0x4c, 0x01, 0xf1, //0x0000023e addq         %r14, %rcx
	0x48, 0x83, 0xfa, 0x20, //0x00000241 cmpq         $32, %rdx
	0x0f, 0x82, 0x57, 0x00, 0x00, 0x00, //0x00000245 jb           LBB0_23
	0x4d, 0x89, 0xc2, //0x0000024b movq         %r8, %r10
	0x49, 0x29, 0xc2, //0x0000024e subq         %rax, %r10
	0x49, 0x83, 0xc2, 0xdc, //0x00000251 addq         $-36, %r10
	0x4c, 0x89, 0xd3, //0x00000255 movq         %r10, %rbx
	0x48, 0x83, 0xe3, 0xe0, //0x00000258 andq         $-32, %rbx
	0x48, 0x01, 0xc3, //0x0000025c addq         %rax, %rbx
	0x4d, 0x8d, 0x4c, 0x1e, 0x24, //0x0000025f leaq         $36(%r14,%rbx), %r9
	0x41, 0x83, 0xe2, 0x1f, //0x00000264 andl         $31, %r10d
	0xc5, 0xfe, 0x6f, 0x05, 0x90, 0xfd, 0xff, 0xff, //0x00000268 vmovdqu      $-624(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	//0x00000270 .p2align 4, 0x90
	//0x00000270 LBB0_20
	0xc5, 0xfe, 0x6f, 0x09, //0x00000270 vmovdqu      (%rcx), %ymm1
	0xc4, 0xe2, 0x7d, 0x00, 0xd1, //0x00000274 vpshufb      %ymm1, %ymm0, %ymm2
	0xc5, 0xf5, 0x74, 0xca, //0x00000279 vpcmpeqb     %ymm2, %ymm1, %ymm1
	0xc5, 0xfd, 0xd7, 0xc1, //0x0000027d vpmovmskb    %ymm1, %eax
	0x83, 0xf8, 0xff, //0x00000281 cmpl         $-1, %eax
	0x0f, 0x85, 0x82, 0x00, 0x00, 0x00, //0x00000284 jne          LBB0_31
	0x48, 0x83, 0xc1, 0x20, //0x0000028a addq         $32, %rcx
	0x48, 0x83, 0xc2, 0xe0, //0x0000028e addq         $-32, %rdx
	0x48, 0x83, 0xfa, 0x1f, //0x00000292 cmpq         $31, %rdx
	0x0f, 0x87, 0xd4, 0xff, 0xff, 0xff, //0x00000296 ja           LBB0_20
	0x4c, 0x89, 0xd2, //0x0000029c movq         %r10, %rdx
	0x4c, 0x89, 0xc9, //0x0000029f movq         %r9, %rcx
	//0x000002a2 LBB0_23
	0x48, 0x85, 0xd2, //0x000002a2 testq        %rdx, %rdx
	0x0f, 0x84, 0x3a, 0x00, 0x00, 0x00, //0x000002a5 je           LBB0_29
	0x4c, 0x8d, 0x0c, 0x11, //0x000002ab leaq         (%rcx,%rdx), %r9
	0x48, 0xff, 0xc1, //0x000002af incq         %rcx
	0x48, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000002b2 movabsq      $4294977024, %rbx
	//0x000002bc LBB0_25
	0x0f, 0xbe, 0x41, 0xff, //0x000002bc movsbl       $-1(%rcx), %eax
	0x83, 0xf8, 0x20, //0x000002c0 cmpl         $32, %eax
	0x0f, 0x87, 0x1c, 0x09, 0x00, 0x00, //0x000002c3 ja           LBB0_128
	0x48, 0x0f, 0xa3, 0xc3, //0x000002c9 btq          %rax, %rbx
	0x0f, 0x83, 0x12, 0x09, 0x00, 0x00, //0x000002cd jae          LBB0_128
	0x48, 0xff, 0xca, //0x000002d3 decq         %rdx
	0x48, 0xff, 0xc1, //0x000002d6 incq         %rcx
	0x48, 0x85, 0xd2, //0x000002d9 testq        %rdx, %rdx
	0x0f, 0x85, 0xda, 0xff, 0xff, 0xff, //0x000002dc jne          LBB0_25
	0x4c, 0x89, 0xc9, //0x000002e2 movq         %r9, %rcx
	//0x000002e5 LBB0_29
	0x4c, 0x29, 0xf1, //0x000002e5 subq         %r14, %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000002e8 movq         $-1, %rax
	0x4c, 0x39, 0xc1, //0x000002ef cmpq         %r8, %rcx
	0x0f, 0x82, 0x32, 0x00, 0x00, 0x00, //0x000002f2 jb           LBB0_32
	0xe9, 0x9e, 0x01, 0x00, 0x00, //0x000002f8 jmp          LBB0_56
	//0x000002fd LBB0_30
	0x48, 0x89, 0x0e, //0x000002fd movq         %rcx, (%rsi)
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000300 movq         $-1, %rax
	0xe9, 0x8f, 0x01, 0x00, 0x00, //0x00000307 jmp          LBB0_56
	//0x0000030c LBB0_31
	0x4c, 0x29, 0xf1, //0x0000030c subq         %r14, %rcx
	0xf7, 0xd0, //0x0000030f notl         %eax
	0x48, 0x98, //0x00000311 cltq         
	0x48, 0x0f, 0xbc, 0xc0, //0x00000313 bsfq         %rax, %rax
	0x48, 0x01, 0xc1, //0x00000317 addq         %rax, %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000031a movq         $-1, %rax
	0x4c, 0x39, 0xc1, //0x00000321 cmpq         %r8, %rcx
	0x0f, 0x83, 0x71, 0x01, 0x00, 0x00, //0x00000324 jae          LBB0_56
	//0x0000032a LBB0_32
	0x48, 0x8d, 0x59, 0x01, //0x0000032a leaq         $1(%rcx), %rbx
	0x48, 0x89, 0x1e, //0x0000032e movq         %rbx, (%rsi)
	0x41, 0x0f, 0xbe, 0x14, 0x0e, //0x00000331 movsbl       (%r14,%rcx), %edx
	0x83, 0xfa, 0x7b, //0x00000336 cmpl         $123, %edx
	0x0f, 0x87, 0x81, 0x01, 0x00, 0x00, //0x00000339 ja           LBB0_58
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000033f movq         $-1, %rax
	0x4c, 0x8d, 0x05, 0xf7, 0x08, 0x00, 0x00, //0x00000346 leaq         $2295(%rip), %r8  /* LJTI0_0+0(%rip) */
	0x49, 0x63, 0x14, 0x90, //0x0000034d movslq       (%r8,%rdx,4), %rdx
	0x4c, 0x01, 0xc2, //0x00000351 addq         %r8, %rdx
	0xff, 0xe2, //0x00000354 jmpq         *%rdx
	//0x00000356 LBB0_34
	0x48, 0x8b, 0x57, 0x08, //0x00000356 movq         $8(%rdi), %rdx
	0x48, 0x89, 0xd0, //0x0000035a movq         %rdx, %rax
	0x48, 0x29, 0xd8, //0x0000035d subq         %rbx, %rax
	0x4c, 0x01, 0xf3, //0x00000360 addq         %r14, %rbx
	0x48, 0x83, 0xf8, 0x20, //0x00000363 cmpq         $32, %rax
	0x0f, 0x82, 0x6b, 0x00, 0x00, 0x00, //0x00000367 jb           LBB0_39
	0x48, 0x29, 0xca, //0x0000036d subq         %rcx, %rdx
	0x48, 0x83, 0xc2, 0xdf, //0x00000370 addq         $-33, %rdx
	0x48, 0x89, 0xd7, //0x00000374 movq         %rdx, %rdi
	0x48, 0x83, 0xe7, 0xe0, //0x00000377 andq         $-32, %rdi
	0x48, 0x01, 0xcf, //0x0000037b addq         %rcx, %rdi
	0x4d, 0x8d, 0x44, 0x3e, 0x21, //0x0000037e leaq         $33(%r14,%rdi), %r8
	0x83, 0xe2, 0x1f, //0x00000383 andl         $31, %edx
	0xc5, 0xfe, 0x6f, 0x05, 0x92, 0xfc, 0xff, 0xff, //0x00000386 vmovdqu      $-878(%rip), %ymm0  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0xaa, 0xfc, 0xff, 0xff, //0x0000038e vmovdqu      $-854(%rip), %ymm1  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x15, 0xc2, 0xfc, 0xff, 0xff, //0x00000396 vmovdqu      $-830(%rip), %ymm2  /* LCPI0_3+0(%rip) */
	0x90, 0x90, //0x0000039e .p2align 4, 0x90
	//0x000003a0 LBB0_36
	0xc5, 0xfe, 0x6f, 0x1b, //0x000003a0 vmovdqu      (%rbx), %ymm3
	0xc5, 0xe5, 0x74, 0xe0, //0x000003a4 vpcmpeqb     %ymm0, %ymm3, %ymm4
	0xc5, 0xe5, 0xeb, 0xd9, //0x000003a8 vpor         %ymm1, %ymm3, %ymm3
	0xc5, 0xe5, 0x74, 0xda, //0x000003ac vpcmpeqb     %ymm2, %ymm3, %ymm3
	0xc5, 0xe5, 0xeb, 0xdc, //0x000003b0 vpor         %ymm4, %ymm3, %ymm3
	0xc5, 0xfd, 0xd7, 0xfb, //0x000003b4 vpmovmskb    %ymm3, %edi
	0x85, 0xff, //0x000003b8 testl        %edi, %edi
	0x0f, 0x85, 0xc8, 0x00, 0x00, 0x00, //0x000003ba jne          LBB0_52
	0x48, 0x83, 0xc3, 0x20, //0x000003c0 addq         $32, %rbx
	0x48, 0x83, 0xc0, 0xe0, //0x000003c4 addq         $-32, %rax
	0x48, 0x83, 0xf8, 0x1f, //0x000003c8 cmpq         $31, %rax
	0x0f, 0x87, 0xce, 0xff, 0xff, 0xff, //0x000003cc ja           LBB0_36
	0x48, 0x89, 0xd0, //0x000003d2 movq         %rdx, %rax
	0x4c, 0x89, 0xc3, //0x000003d5 movq         %r8, %rbx
	//0x000003d8 LBB0_39
	0x48, 0x83, 0xf8, 0x10, //0x000003d8 cmpq         $16, %rax
	0x0f, 0x82, 0x64, 0x00, 0x00, 0x00, //0x000003dc jb           LBB0_44
	0x48, 0x8d, 0x50, 0xf0, //0x000003e2 leaq         $-16(%rax), %rdx
	0x48, 0x89, 0xd7, //0x000003e6 movq         %rdx, %rdi
	0x48, 0x83, 0xe7, 0xf0, //0x000003e9 andq         $-16, %rdi
	0x4c, 0x8d, 0x44, 0x1f, 0x10, //0x000003ed leaq         $16(%rdi,%rbx), %r8
	0x83, 0xe2, 0x0f, //0x000003f2 andl         $15, %edx
	0xc5, 0xfa, 0x6f, 0x05, 0x23, 0xfd, 0xff, 0xff, //0x000003f5 vmovdqu      $-733(%rip), %xmm0  /* LCPI0_4+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x0d, 0x2b, 0xfd, 0xff, 0xff, //0x000003fd vmovdqu      $-725(%rip), %xmm1  /* LCPI0_5+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x15, 0x33, 0xfd, 0xff, 0xff, //0x00000405 vmovdqu      $-717(%rip), %xmm2  /* LCPI0_6+0(%rip) */
	//0x0000040d LBB0_41
	0xc5, 0xfa, 0x6f, 0x1b, //0x0000040d vmovdqu      (%rbx), %xmm3
	0xc5, 0xe1, 0x74, 0xe0, //0x00000411 vpcmpeqb     %xmm0, %xmm3, %xmm4
	0xc5, 0xe1, 0xeb, 0xd9, //0x00000415 vpor         %xmm1, %xmm3, %xmm3
	0xc5, 0xe1, 0x74, 0xda, //0x00000419 vpcmpeqb     %xmm2, %xmm3, %xmm3
	0xc5, 0xe1, 0xeb, 0xdc, //0x0000041d vpor         %xmm4, %xmm3, %xmm3
	0xc5, 0xf9, 0xd7, 0xfb, //0x00000421 vpmovmskb    %xmm3, %edi
	0x66, 0x85, 0xff, //0x00000425 testw        %di, %di
	0x0f, 0x85, 0xaf, 0x07, 0x00, 0x00, //0x00000428 jne          LBB0_127
	0x48, 0x83, 0xc3, 0x10, //0x0000042e addq         $16, %rbx
	0x48, 0x83, 0xc0, 0xf0, //0x00000432 addq         $-16, %rax
	0x48, 0x83, 0xf8, 0x0f, //0x00000436 cmpq         $15, %rax
	0x0f, 0x87, 0xcd, 0xff, 0xff, 0xff, //0x0000043a ja           LBB0_41
	0x48, 0x89, 0xd0, //0x00000440 movq         %rdx, %rax
	0x4c, 0x89, 0xc3, //0x00000443 movq         %r8, %rbx
	//0x00000446 LBB0_44
	0x48, 0x85, 0xc0, //0x00000446 testq        %rax, %rax
	0x0f, 0x84, 0x31, 0x00, 0x00, 0x00, //0x00000449 je           LBB0_51
	0x48, 0x8d, 0x3c, 0x03, //0x0000044f leaq         (%rbx,%rax), %rdi
	//0x00000453 LBB0_46
	0x0f, 0xb6, 0x13, //0x00000453 movzbl       (%rbx), %edx
	0x80, 0xfa, 0x2c, //0x00000456 cmpb         $44, %dl
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x00000459 je           LBB0_51
	0x80, 0xfa, 0x7d, //0x0000045f cmpb         $125, %dl
	0x0f, 0x84, 0x18, 0x00, 0x00, 0x00, //0x00000462 je           LBB0_51
	0x80, 0xfa, 0x5d, //0x00000468 cmpb         $93, %dl
	0x0f, 0x84, 0x0f, 0x00, 0x00, 0x00, //0x0000046b je           LBB0_51
	0x48, 0xff, 0xc3, //0x00000471 incq         %rbx
	0x48, 0xff, 0xc8, //0x00000474 decq         %rax
	0x0f, 0x85, 0xd6, 0xff, 0xff, 0xff, //0x00000477 jne          LBB0_46
	0x48, 0x89, 0xfb, //0x0000047d movq         %rdi, %rbx
	//0x00000480 LBB0_51
	0x4c, 0x29, 0xf3, //0x00000480 subq         %r14, %rbx
	0xe9, 0x0d, 0x00, 0x00, 0x00, //0x00000483 jmp          LBB0_54
	//0x00000488 LBB0_52
	0x48, 0x63, 0xc7, //0x00000488 movslq       %edi, %rax
	//0x0000048b LBB0_53
	0x48, 0x0f, 0xbc, 0xc0, //0x0000048b bsfq         %rax, %rax
	0x4c, 0x29, 0xf3, //0x0000048f subq         %r14, %rbx
	0x48, 0x01, 0xc3, //0x00000492 addq         %rax, %rbx
	//0x00000495 LBB0_54
	0x48, 0x89, 0x1e, //0x00000495 movq         %rbx, (%rsi)
	//0x00000498 LBB0_55
	0x48, 0x89, 0xc8, //0x00000498 movq         %rcx, %rax
	//0x0000049b LBB0_56
	0x48, 0x8d, 0x65, 0xd8, //0x0000049b leaq         $-40(%rbp), %rsp
	0x5b, //0x0000049f popq         %rbx
	0x41, 0x5c, //0x000004a0 popq         %r12
	0x41, 0x5d, //0x000004a2 popq         %r13
	0x41, 0x5e, //0x000004a4 popq         %r14
	0x41, 0x5f, //0x000004a6 popq         %r15
	0x5d, //0x000004a8 popq         %rbp
	0xc5, 0xf8, 0x77, //0x000004a9 vzeroupper   
	0xc3, //0x000004ac retq         
	//0x000004ad LBB0_57
	0x48, 0x8d, 0x51, 0x04, //0x000004ad leaq         $4(%rcx), %rdx
	0x48, 0x3b, 0x57, 0x08, //0x000004b1 cmpq         $8(%rdi), %rdx
	0x0f, 0x87, 0xe0, 0xff, 0xff, 0xff, //0x000004b5 ja           LBB0_56
	0xe9, 0xbb, 0x03, 0x00, 0x00, //0x000004bb jmp          LBB0_95
	//0x000004c0 LBB0_58
	0x48, 0x89, 0x0e, //0x000004c0 movq         %rcx, (%rsi)
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x000004c3 movq         $-2, %rax
	0xe9, 0xcc, 0xff, 0xff, 0xff, //0x000004ca jmp          LBB0_56
	//0x000004cf LBB0_59
	0x4c, 0x8b, 0x4f, 0x08, //0x000004cf movq         $8(%rdi), %r9
	0x4d, 0x89, 0xcf, //0x000004d3 movq         %r9, %r15
	0x49, 0x29, 0xdf, //0x000004d6 subq         %rbx, %r15
	0x49, 0x83, 0xff, 0x20, //0x000004d9 cmpq         $32, %r15
	0x0f, 0x8c, 0x2b, 0x07, 0x00, 0x00, //0x000004dd jl           LBB0_130
	0x41, 0xb8, 0xff, 0xff, 0xff, 0xff, //0x000004e3 movl         $4294967295, %r8d
	0x4d, 0x8d, 0x14, 0x0e, //0x000004e9 leaq         (%r14,%rcx), %r10
	0x49, 0x29, 0xc9, //0x000004ed subq         %rcx, %r9
	0xba, 0x1f, 0x00, 0x00, 0x00, //0x000004f0 movl         $31, %edx
	0x45, 0x31, 0xff, //0x000004f5 xorl         %r15d, %r15d
	0xc5, 0xfe, 0x6f, 0x05, 0x80, 0xfb, 0xff, 0xff, //0x000004f8 vmovdqu      $-1152(%rip), %ymm0  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0x98, 0xfb, 0xff, 0xff, //0x00000500 vmovdqu      $-1128(%rip), %ymm1  /* LCPI0_8+0(%rip) */
	0x45, 0x31, 0xe4, //0x00000508 xorl         %r12d, %r12d
	0x90, 0x90, 0x90, 0x90, 0x90, //0x0000050b .p2align 4, 0x90
	//0x00000510 LBB0_61
	0xc4, 0x81, 0x7e, 0x6f, 0x54, 0x3a, 0x01, //0x00000510 vmovdqu      $1(%r10,%r15), %ymm2
	0xc5, 0xed, 0x74, 0xd8, //0x00000517 vpcmpeqb     %ymm0, %ymm2, %ymm3
	0xc5, 0x7d, 0xd7, 0xdb, //0x0000051b vpmovmskb    %ymm3, %r11d
	0xc5, 0xed, 0x74, 0xd1, //0x0000051f vpcmpeqb     %ymm1, %ymm2, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00000523 vpmovmskb    %ymm2, %edi
	0x4d, 0x85, 0xe4, //0x00000527 testq        %r12, %r12
	0x0f, 0x85, 0x10, 0x00, 0x00, 0x00, //0x0000052a jne          LBB0_64
	0x85, 0xff, //0x00000530 testl        %edi, %edi
	0x0f, 0x85, 0x08, 0x00, 0x00, 0x00, //0x00000532 jne          LBB0_64
	0x45, 0x31, 0xe4, //0x00000538 xorl         %r12d, %r12d
	0xe9, 0x3b, 0x00, 0x00, 0x00, //0x0000053b jmp          LBB0_65
	//0x00000540 .p2align 4, 0x90
	//0x00000540 LBB0_64
	0x44, 0x89, 0xe3, //0x00000540 movl         %r12d, %ebx
	0x44, 0x31, 0xc3, //0x00000543 xorl         %r8d, %ebx
	0x21, 0xfb, //0x00000546 andl         %edi, %ebx
	0x44, 0x8d, 0x2c, 0x1b, //0x00000548 leal         (%rbx,%rbx), %r13d
	0x45, 0x09, 0xe5, //0x0000054c orl          %r12d, %r13d
	0x41, 0x8d, 0xb8, 0xab, 0xaa, 0xaa, 0xaa, //0x0000054f leal         $-1431655765(%r8), %edi
	0x44, 0x31, 0xef, //0x00000556 xorl         %r13d, %edi
	0x21, 0xdf, //0x00000559 andl         %ebx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000055b andl         $-1431655766, %edi
	0x45, 0x31, 0xe4, //0x00000561 xorl         %r12d, %r12d
	0x01, 0xdf, //0x00000564 addl         %ebx, %edi
	0x41, 0x0f, 0x92, 0xc4, //0x00000566 setb         %r12b
	0x01, 0xff, //0x0000056a addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x0000056c xorl         $1431655765, %edi
	0x44, 0x21, 0xef, //0x00000572 andl         %r13d, %edi
	0x44, 0x31, 0xc7, //0x00000575 xorl         %r8d, %edi
	0x41, 0x21, 0xfb, //0x00000578 andl         %edi, %r11d
	//0x0000057b LBB0_65
	0x45, 0x85, 0xdb, //0x0000057b testl        %r11d, %r11d
	0x0f, 0x85, 0xea, 0x05, 0x00, 0x00, //0x0000057e jne          LBB0_121
	0x49, 0x83, 0xc7, 0x20, //0x00000584 addq         $32, %r15
	0x49, 0x8d, 0x7c, 0x11, 0xe0, //0x00000588 leaq         $-32(%r9,%rdx), %rdi
	0x48, 0x83, 0xc2, 0xe0, //0x0000058d addq         $-32, %rdx
	0x48, 0x83, 0xff, 0x3f, //0x00000591 cmpq         $63, %rdi
	0x0f, 0x8f, 0x75, 0xff, 0xff, 0xff, //0x00000595 jg           LBB0_61
	0x4d, 0x85, 0xe4, //0x0000059b testq        %r12, %r12
	0x0f, 0x85, 0x7e, 0x06, 0x00, 0x00, //0x0000059e jne          LBB0_132
	0x4b, 0x8d, 0x5c, 0x17, 0x01, //0x000005a4 leaq         $1(%r15,%r10), %rbx
	0x49, 0xf7, 0xd7, //0x000005a9 notq         %r15
	0x4d, 0x01, 0xcf, //0x000005ac addq         %r9, %r15
	//0x000005af LBB0_69
	0x4d, 0x85, 0xff, //0x000005af testq        %r15, %r15
	0x0f, 0x8f, 0xee, 0x05, 0x00, 0x00, //0x000005b2 jg           LBB0_124
	0xe9, 0xde, 0xfe, 0xff, 0xff, //0x000005b8 jmp          LBB0_56
	//0x000005bd LBB0_70
	0x4c, 0x8b, 0x47, 0x08, //0x000005bd movq         $8(%rdi), %r8
	0x49, 0x29, 0xd8, //0x000005c1 subq         %rbx, %r8
	0x49, 0x01, 0xde, //0x000005c4 addq         %rbx, %r14
	0x31, 0xd2, //0x000005c7 xorl         %edx, %edx
	0x48, 0x89, 0x54, 0x24, 0x18, //0x000005c9 movq         %rdx, $24(%rsp)
	0xc5, 0xfe, 0x6f, 0x05, 0xaa, 0xfa, 0xff, 0xff, //0x000005ce vmovdqu      $-1366(%rip), %ymm0  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0xc2, 0xfa, 0xff, 0xff, //0x000005d6 vmovdqu      $-1342(%rip), %ymm1  /* LCPI0_8+0(%rip) */
	0xc5, 0xe9, 0x76, 0xd2, //0x000005de vpcmpeqd     %xmm2, %xmm2, %xmm2
	0xc5, 0xfe, 0x6f, 0x1d, 0xf6, 0xfa, 0xff, 0xff, //0x000005e2 vmovdqu      $-1290(%rip), %ymm3  /* LCPI0_10+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x0e, 0xfb, 0xff, 0xff, //0x000005ea vmovdqu      $-1266(%rip), %ymm4  /* LCPI0_11+0(%rip) */
	0xc4, 0x41, 0x30, 0x57, 0xc9, //0x000005f2 vxorps       %xmm9, %xmm9, %xmm9
	0x45, 0x31, 0xe4, //0x000005f7 xorl         %r12d, %r12d
	0x45, 0x31, 0xff, //0x000005fa xorl         %r15d, %r15d
	0x45, 0x31, 0xc9, //0x000005fd xorl         %r9d, %r9d
	0x49, 0x83, 0xf8, 0x40, //0x00000600 cmpq         $64, %r8
	0x0f, 0x8c, 0x40, 0x01, 0x00, 0x00, //0x00000604 jl           LBB0_79
	//0x0000060a LBB0_73
	0xc4, 0xc1, 0x7e, 0x6f, 0x3e, //0x0000060a vmovdqu      (%r14), %ymm7
	0xc4, 0xc1, 0x7e, 0x6f, 0x76, 0x20, //0x0000060f vmovdqu      $32(%r14), %ymm6
	0xc5, 0x45, 0x74, 0xc0, //0x00000615 vpcmpeqb     %ymm0, %ymm7, %ymm8
	0xc4, 0x41, 0x7d, 0xd7, 0xe8, //0x00000619 vpmovmskb    %ymm8, %r13d
	0xc5, 0x4d, 0x74, 0xc0, //0x0000061e vpcmpeqb     %ymm0, %ymm6, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xd0, //0x00000622 vpmovmskb    %ymm8, %edx
	0x48, 0xc1, 0xe2, 0x20, //0x00000627 shlq         $32, %rdx
	0x49, 0x09, 0xd5, //0x0000062b orq          %rdx, %r13
	0xc5, 0x45, 0x74, 0xc1, //0x0000062e vpcmpeqb     %ymm1, %ymm7, %ymm8
	0xc4, 0x41, 0x7d, 0xd7, 0xd0, //0x00000632 vpmovmskb    %ymm8, %r10d
	0xc5, 0x4d, 0x74, 0xc1, //0x00000637 vpcmpeqb     %ymm1, %ymm6, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xd8, //0x0000063b vpmovmskb    %ymm8, %ebx
	0x48, 0xc1, 0xe3, 0x20, //0x00000640 shlq         $32, %rbx
	0x49, 0x09, 0xda, //0x00000644 orq          %rbx, %r10
	0x4c, 0x89, 0xd2, //0x00000647 movq         %r10, %rdx
	0x4c, 0x09, 0xe2, //0x0000064a orq          %r12, %rdx
	0x0f, 0x84, 0x4b, 0x00, 0x00, 0x00, //0x0000064d je           LBB0_75
	0x4c, 0x89, 0xe2, //0x00000653 movq         %r12, %rdx
	0x48, 0xf7, 0xd2, //0x00000656 notq         %rdx
	0x4c, 0x21, 0xd2, //0x00000659 andq         %r10, %rdx
	0x48, 0x8d, 0x1c, 0x12, //0x0000065c leaq         (%rdx,%rdx), %rbx
	0x4c, 0x09, 0xe3, //0x00000660 orq          %r12, %rbx
	0x49, 0x89, 0xdc, //0x00000663 movq         %rbx, %r12
	0x49, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000666 movabsq      $-6148914691236517206, %r11
	0x4d, 0x31, 0xdc, //0x00000670 xorq         %r11, %r12
	0x4d, 0x21, 0xda, //0x00000673 andq         %r11, %r10
	0x4d, 0x21, 0xe2, //0x00000676 andq         %r12, %r10
	0x45, 0x31, 0xe4, //0x00000679 xorl         %r12d, %r12d
	0x49, 0x01, 0xd2, //0x0000067c addq         %rdx, %r10
	0x41, 0x0f, 0x92, 0xc4, //0x0000067f setb         %r12b
	0x4d, 0x01, 0xd2, //0x00000683 addq         %r10, %r10
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000686 movabsq      $6148914691236517205, %rdx
	0x49, 0x31, 0xd2, //0x00000690 xorq         %rdx, %r10
	0x49, 0x21, 0xda, //0x00000693 andq         %rbx, %r10
	0x49, 0xf7, 0xd2, //0x00000696 notq         %r10
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00000699 jmp          LBB0_76
	//0x0000069e LBB0_75
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x0000069e movq         $-1, %r10
	0x45, 0x31, 0xe4, //0x000006a5 xorl         %r12d, %r12d
	//0x000006a8 LBB0_76
	0x4d, 0x21, 0xea, //0x000006a8 andq         %r13, %r10
	0xc4, 0xc1, 0xf9, 0x6e, 0xea, //0x000006ab vmovq        %r10, %xmm5
	0xc4, 0xe3, 0x51, 0x44, 0xea, 0x00, //0x000006b0 vpclmulqdq   $0, %xmm2, %xmm5, %xmm5
	0xc4, 0xc1, 0xf9, 0x7e, 0xed, //0x000006b6 vmovq        %xmm5, %r13
	0x4c, 0x33, 0x6c, 0x24, 0x18, //0x000006bb xorq         $24(%rsp), %r13
	0xc5, 0xc5, 0x74, 0xeb, //0x000006c0 vpcmpeqb     %ymm3, %ymm7, %ymm5
	0xc5, 0x7d, 0xd7, 0xdd, //0x000006c4 vpmovmskb    %ymm5, %r11d
	0xc5, 0xcd, 0x74, 0xeb, //0x000006c8 vpcmpeqb     %ymm3, %ymm6, %ymm5
	0xc5, 0xfd, 0xd7, 0xd5, //0x000006cc vpmovmskb    %ymm5, %edx
	0x48, 0xc1, 0xe2, 0x20, //0x000006d0 shlq         $32, %rdx
	0x49, 0x09, 0xd3, //0x000006d4 orq          %rdx, %r11
	0x4d, 0x89, 0xea, //0x000006d7 movq         %r13, %r10
	0x49, 0xf7, 0xd2, //0x000006da notq         %r10
	0x4d, 0x21, 0xd3, //0x000006dd andq         %r10, %r11
	0xc5, 0xc5, 0x74, 0xec, //0x000006e0 vpcmpeqb     %ymm4, %ymm7, %ymm5
	0xc5, 0xfd, 0xd7, 0xd5, //0x000006e4 vpmovmskb    %ymm5, %edx
	0xc5, 0xcd, 0x74, 0xec, //0x000006e8 vpcmpeqb     %ymm4, %ymm6, %ymm5
	0xc5, 0xfd, 0xd7, 0xdd, //0x000006ec vpmovmskb    %ymm5, %ebx
	0x48, 0xc1, 0xe3, 0x20, //0x000006f0 shlq         $32, %rbx
	0x48, 0x09, 0xda, //0x000006f4 orq          %rbx, %rdx
	0x4c, 0x21, 0xd2, //0x000006f7 andq         %r10, %rdx
	0x0f, 0x84, 0x27, 0x00, 0x00, 0x00, //0x000006fa je           LBB0_71
	//0x00000700 .p2align 4, 0x90
	//0x00000700 LBB0_77
	0x4c, 0x8d, 0x52, 0xff, //0x00000700 leaq         $-1(%rdx), %r10
	0x4c, 0x89, 0xd3, //0x00000704 movq         %r10, %rbx
	0x4c, 0x21, 0xdb, //0x00000707 andq         %r11, %rbx
	0xf3, 0x48, 0x0f, 0xb8, 0xdb, //0x0000070a popcntq      %rbx, %rbx
	0x4c, 0x01, 0xfb, //0x0000070f addq         %r15, %rbx
	0x4c, 0x39, 0xcb, //0x00000712 cmpq         %r9, %rbx
	0x0f, 0x86, 0x22, 0x04, 0x00, 0x00, //0x00000715 jbe          LBB0_120
	0x49, 0xff, 0xc1, //0x0000071b incq         %r9
	0x4c, 0x21, 0xd2, //0x0000071e andq         %r10, %rdx
	0x0f, 0x85, 0xd9, 0xff, 0xff, 0xff, //0x00000721 jne          LBB0_77
	//0x00000727 LBB0_71
	0x49, 0xc1, 0xfd, 0x3f, //0x00000727 sarq         $63, %r13
	0xf3, 0x49, 0x0f, 0xb8, 0xd3, //0x0000072b popcntq      %r11, %rdx
	0x49, 0x01, 0xd7, //0x00000730 addq         %rdx, %r15
	0x49, 0x83, 0xc6, 0x40, //0x00000733 addq         $64, %r14
	0x49, 0x83, 0xc0, 0xc0, //0x00000737 addq         $-64, %r8
	0x4c, 0x89, 0x6c, 0x24, 0x18, //0x0000073b movq         %r13, $24(%rsp)
	0x49, 0x83, 0xf8, 0x40, //0x00000740 cmpq         $64, %r8
	0x0f, 0x8d, 0xc0, 0xfe, 0xff, 0xff, //0x00000744 jge          LBB0_73
	//0x0000074a LBB0_79
	0x4d, 0x85, 0xc0, //0x0000074a testq        %r8, %r8
	0x0f, 0x8e, 0xc3, 0x04, 0x00, 0x00, //0x0000074d jle          LBB0_131
	0xc5, 0x7c, 0x11, 0x4c, 0x24, 0x40, //0x00000753 vmovups      %ymm9, $64(%rsp)
	0xc5, 0x7c, 0x11, 0x4c, 0x24, 0x20, //0x00000759 vmovups      %ymm9, $32(%rsp)
	0x44, 0x89, 0xf2, //0x0000075f movl         %r14d, %edx
	0x81, 0xe2, 0xff, 0x0f, 0x00, 0x00, //0x00000762 andl         $4095, %edx
	0x81, 0xfa, 0xc1, 0x0f, 0x00, 0x00, //0x00000768 cmpl         $4033, %edx
	0x0f, 0x82, 0x96, 0xfe, 0xff, 0xff, //0x0000076e jb           LBB0_73
	0x49, 0x83, 0xf8, 0x20, //0x00000774 cmpq         $32, %r8
	0x0f, 0x82, 0x1d, 0x00, 0x00, 0x00, //0x00000778 jb           LBB0_83
	0xc4, 0xc1, 0x7e, 0x6f, 0x2e, //0x0000077e vmovdqu      (%r14), %ymm5
	0xc5, 0xfe, 0x7f, 0x6c, 0x24, 0x20, //0x00000783 vmovdqu      %ymm5, $32(%rsp)
	0x49, 0x83, 0xc6, 0x20, //0x00000789 addq         $32, %r14
	0x49, 0x8d, 0x58, 0xe0, //0x0000078d leaq         $-32(%r8), %rbx
	0x4c, 0x8d, 0x54, 0x24, 0x40, //0x00000791 leaq         $64(%rsp), %r10
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x00000796 jmp          LBB0_84
	//0x0000079b LBB0_83
	0x4c, 0x8d, 0x54, 0x24, 0x20, //0x0000079b leaq         $32(%rsp), %r10
	0x4c, 0x89, 0xc3, //0x000007a0 movq         %r8, %rbx
	//0x000007a3 LBB0_84
	0x48, 0x83, 0xfb, 0x10, //0x000007a3 cmpq         $16, %rbx
	0x0f, 0x82, 0x5c, 0x00, 0x00, 0x00, //0x000007a7 jb           LBB0_85
	0xc4, 0xc1, 0x7a, 0x6f, 0x2e, //0x000007ad vmovdqu      (%r14), %xmm5
	0xc4, 0xc1, 0x7a, 0x7f, 0x2a, //0x000007b2 vmovdqu      %xmm5, (%r10)
	0x49, 0x83, 0xc6, 0x10, //0x000007b7 addq         $16, %r14
	0x49, 0x83, 0xc2, 0x10, //0x000007bb addq         $16, %r10
	0x48, 0x83, 0xc3, 0xf0, //0x000007bf addq         $-16, %rbx
	0x48, 0x83, 0xfb, 0x08, //0x000007c3 cmpq         $8, %rbx
	0x0f, 0x83, 0x46, 0x00, 0x00, 0x00, //0x000007c7 jae          LBB0_90
	//0x000007cd LBB0_86
	0x48, 0x83, 0xfb, 0x04, //0x000007cd cmpq         $4, %rbx
	0x0f, 0x8c, 0x58, 0x00, 0x00, 0x00, //0x000007d1 jl           LBB0_87
	//0x000007d7 LBB0_91
	0x41, 0x8b, 0x16, //0x000007d7 movl         (%r14), %edx
	0x41, 0x89, 0x12, //0x000007da movl         %edx, (%r10)
	0x49, 0x83, 0xc6, 0x04, //0x000007dd addq         $4, %r14
	0x49, 0x83, 0xc2, 0x04, //0x000007e1 addq         $4, %r10
	0x48, 0x83, 0xc3, 0xfc, //0x000007e5 addq         $-4, %rbx
	0x48, 0x83, 0xfb, 0x02, //0x000007e9 cmpq         $2, %rbx
	0x0f, 0x83, 0x46, 0x00, 0x00, 0x00, //0x000007ed jae          LBB0_92
	//0x000007f3 LBB0_88
	0x4c, 0x89, 0xf2, //0x000007f3 movq         %r14, %rdx
	0x4c, 0x8d, 0x74, 0x24, 0x20, //0x000007f6 leaq         $32(%rsp), %r14
	0x48, 0x85, 0xdb, //0x000007fb testq        %rbx, %rbx
	0x0f, 0x85, 0x5a, 0x00, 0x00, 0x00, //0x000007fe jne          LBB0_93
	0xe9, 0x01, 0xfe, 0xff, 0xff, //0x00000804 jmp          LBB0_73
	//0x00000809 LBB0_85
	0x48, 0x83, 0xfb, 0x08, //0x00000809 cmpq         $8, %rbx
	0x0f, 0x82, 0xba, 0xff, 0xff, 0xff, //0x0000080d jb           LBB0_86
	//0x00000813 LBB0_90
	0x49, 0x8b, 0x16, //0x00000813 movq         (%r14), %rdx
	0x49, 0x89, 0x12, //0x00000816 movq         %rdx, (%r10)
	0x49, 0x83, 0xc6, 0x08, //0x00000819 addq         $8, %r14
	0x49, 0x83, 0xc2, 0x08, //0x0000081d addq         $8, %r10
	0x48, 0x83, 0xc3, 0xf8, //0x00000821 addq         $-8, %rbx
	0x48, 0x83, 0xfb, 0x04, //0x00000825 cmpq         $4, %rbx
	0x0f, 0x8d, 0xa8, 0xff, 0xff, 0xff, //0x00000829 jge          LBB0_91
	//0x0000082f LBB0_87
	0x48, 0x83, 0xfb, 0x02, //0x0000082f cmpq         $2, %rbx
	0x0f, 0x82, 0xba, 0xff, 0xff, 0xff, //0x00000833 jb           LBB0_88
	//0x00000839 LBB0_92
	0x41, 0x0f, 0xb7, 0x16, //0x00000839 movzwl       (%r14), %edx
	0x66, 0x41, 0x89, 0x12, //0x0000083d movw         %dx, (%r10)
	0x49, 0x83, 0xc6, 0x02, //0x00000841 addq         $2, %r14
	0x49, 0x83, 0xc2, 0x02, //0x00000845 addq         $2, %r10
	0x48, 0x83, 0xc3, 0xfe, //0x00000849 addq         $-2, %rbx
	0x4c, 0x89, 0xf2, //0x0000084d movq         %r14, %rdx
	0x4c, 0x8d, 0x74, 0x24, 0x20, //0x00000850 leaq         $32(%rsp), %r14
	0x48, 0x85, 0xdb, //0x00000855 testq        %rbx, %rbx
	0x0f, 0x84, 0xac, 0xfd, 0xff, 0xff, //0x00000858 je           LBB0_73
	//0x0000085e LBB0_93
	0x8a, 0x12, //0x0000085e movb         (%rdx), %dl
	0x41, 0x88, 0x12, //0x00000860 movb         %dl, (%r10)
	0x4c, 0x8d, 0x74, 0x24, 0x20, //0x00000863 leaq         $32(%rsp), %r14
	0xe9, 0x9d, 0xfd, 0xff, 0xff, //0x00000868 jmp          LBB0_73
	//0x0000086d LBB0_94
	0x48, 0x8d, 0x51, 0x05, //0x0000086d leaq         $5(%rcx), %rdx
	0x48, 0x3b, 0x57, 0x08, //0x00000871 cmpq         $8(%rdi), %rdx
	0x0f, 0x87, 0x20, 0xfc, 0xff, 0xff, //0x00000875 ja           LBB0_56
	//0x0000087b LBB0_95
	0x48, 0x89, 0x16, //0x0000087b movq         %rdx, (%rsi)
	0xe9, 0x15, 0xfc, 0xff, 0xff, //0x0000087e jmp          LBB0_55
	//0x00000883 LBB0_96
	0x4c, 0x8b, 0x47, 0x08, //0x00000883 movq         $8(%rdi), %r8
	0x49, 0x29, 0xd8, //0x00000887 subq         %rbx, %r8
	0x49, 0x01, 0xde, //0x0000088a addq         %rbx, %r14
	0x31, 0xd2, //0x0000088d xorl         %edx, %edx
	0x48, 0x89, 0x54, 0x24, 0x18, //0x0000088f movq         %rdx, $24(%rsp)
	0xc5, 0xfe, 0x6f, 0x05, 0xe4, 0xf7, 0xff, 0xff, //0x00000894 vmovdqu      $-2076(%rip), %ymm0  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0xfc, 0xf7, 0xff, 0xff, //0x0000089c vmovdqu      $-2052(%rip), %ymm1  /* LCPI0_8+0(%rip) */
	0xc5, 0xe9, 0x76, 0xd2, //0x000008a4 vpcmpeqd     %xmm2, %xmm2, %xmm2
	0xc5, 0xfe, 0x6f, 0x1d, 0x10, 0xf8, 0xff, 0xff, //0x000008a8 vmovdqu      $-2032(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0xa8, 0xf7, 0xff, 0xff, //0x000008b0 vmovdqu      $-2136(%rip), %ymm4  /* LCPI0_3+0(%rip) */
	0xc4, 0x41, 0x30, 0x57, 0xc9, //0x000008b8 vxorps       %xmm9, %xmm9, %xmm9
	0x45, 0x31, 0xe4, //0x000008bd xorl         %r12d, %r12d
	0x45, 0x31, 0xff, //0x000008c0 xorl         %r15d, %r15d
	0x45, 0x31, 0xc9, //0x000008c3 xorl         %r9d, %r9d
	0x49, 0x83, 0xf8, 0x40, //0x000008c6 cmpq         $64, %r8
	0x0f, 0x8c, 0x4a, 0x01, 0x00, 0x00, //0x000008ca jl           LBB0_105
	//0x000008d0 LBB0_99
	0xc4, 0xc1, 0x7e, 0x6f, 0x3e, //0x000008d0 vmovdqu      (%r14), %ymm7
	0xc4, 0xc1, 0x7e, 0x6f, 0x76, 0x20, //0x000008d5 vmovdqu      $32(%r14), %ymm6
	0xc5, 0x45, 0x74, 0xc0, //0x000008db vpcmpeqb     %ymm0, %ymm7, %ymm8
	0xc4, 0x41, 0x7d, 0xd7, 0xe8, //0x000008df vpmovmskb    %ymm8, %r13d
	0xc5, 0x4d, 0x74, 0xc0, //0x000008e4 vpcmpeqb     %ymm0, %ymm6, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xd0, //0x000008e8 vpmovmskb    %ymm8, %edx
	0x48, 0xc1, 0xe2, 0x20, //0x000008ed shlq         $32, %rdx
	0x49, 0x09, 0xd5, //0x000008f1 orq          %rdx, %r13
	0xc5, 0x45, 0x74, 0xc1, //0x000008f4 vpcmpeqb     %ymm1, %ymm7, %ymm8
	0xc4, 0x41, 0x7d, 0xd7, 0xd0, //0x000008f8 vpmovmskb    %ymm8, %r10d
	0xc5, 0x4d, 0x74, 0xc1, //0x000008fd vpcmpeqb     %ymm1, %ymm6, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xd8, //0x00000901 vpmovmskb    %ymm8, %ebx
	0x48, 0xc1, 0xe3, 0x20, //0x00000906 shlq         $32, %rbx
	0x49, 0x09, 0xda, //0x0000090a orq          %rbx, %r10
	0x4c, 0x89, 0xd2, //0x0000090d movq         %r10, %rdx
	0x4c, 0x09, 0xe2, //0x00000910 orq          %r12, %rdx
	0x0f, 0x84, 0x4b, 0x00, 0x00, 0x00, //0x00000913 je           LBB0_101
	0x4c, 0x89, 0xe2, //0x00000919 movq         %r12, %rdx
	0x48, 0xf7, 0xd2, //0x0000091c notq         %rdx
	0x4c, 0x21, 0xd2, //0x0000091f andq         %r10, %rdx
	0x48, 0x8d, 0x1c, 0x12, //0x00000922 leaq         (%rdx,%rdx), %rbx
	0x4c, 0x09, 0xe3, //0x00000926 orq          %r12, %rbx
	0x49, 0x89, 0xdc, //0x00000929 movq         %rbx, %r12
	0x49, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000092c movabsq      $-6148914691236517206, %r11
	0x4d, 0x31, 0xdc, //0x00000936 xorq         %r11, %r12
	0x4d, 0x21, 0xda, //0x00000939 andq         %r11, %r10
	0x4d, 0x21, 0xe2, //0x0000093c andq         %r12, %r10
	0x45, 0x31, 0xe4, //0x0000093f xorl         %r12d, %r12d
	0x49, 0x01, 0xd2, //0x00000942 addq         %rdx, %r10
	0x41, 0x0f, 0x92, 0xc4, //0x00000945 setb         %r12b
	0x4d, 0x01, 0xd2, //0x00000949 addq         %r10, %r10
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000094c movabsq      $6148914691236517205, %rdx
	0x49, 0x31, 0xd2, //0x00000956 xorq         %rdx, %r10
	0x49, 0x21, 0xda, //0x00000959 andq         %rbx, %r10
	0x49, 0xf7, 0xd2, //0x0000095c notq         %r10
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x0000095f jmp          LBB0_102
	//0x00000964 LBB0_101
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00000964 movq         $-1, %r10
	0x45, 0x31, 0xe4, //0x0000096b xorl         %r12d, %r12d
	//0x0000096e LBB0_102
	0x4d, 0x21, 0xea, //0x0000096e andq         %r13, %r10
	0xc4, 0xc1, 0xf9, 0x6e, 0xea, //0x00000971 vmovq        %r10, %xmm5
	0xc4, 0xe3, 0x51, 0x44, 0xea, 0x00, //0x00000976 vpclmulqdq   $0, %xmm2, %xmm5, %xmm5
	0xc4, 0xc1, 0xf9, 0x7e, 0xed, //0x0000097c vmovq        %xmm5, %r13
	0x4c, 0x33, 0x6c, 0x24, 0x18, //0x00000981 xorq         $24(%rsp), %r13
	0xc5, 0xc5, 0x74, 0xeb, //0x00000986 vpcmpeqb     %ymm3, %ymm7, %ymm5
	0xc5, 0x7d, 0xd7, 0xdd, //0x0000098a vpmovmskb    %ymm5, %r11d
	0xc5, 0xcd, 0x74, 0xeb, //0x0000098e vpcmpeqb     %ymm3, %ymm6, %ymm5
	0xc5, 0xfd, 0xd7, 0xd5, //0x00000992 vpmovmskb    %ymm5, %edx
	0x48, 0xc1, 0xe2, 0x20, //0x00000996 shlq         $32, %rdx
	0x49, 0x09, 0xd3, //0x0000099a orq          %rdx, %r11
	0x4d, 0x89, 0xea, //0x0000099d movq         %r13, %r10
	0x49, 0xf7, 0xd2, //0x000009a0 notq         %r10
	0x4d, 0x21, 0xd3, //0x000009a3 andq         %r10, %r11
	0xc5, 0xc5, 0x74, 0xec, //0x000009a6 vpcmpeqb     %ymm4, %ymm7, %ymm5
	0xc5, 0xfd, 0xd7, 0xd5, //0x000009aa vpmovmskb    %ymm5, %edx
	0xc5, 0xcd, 0x74, 0xec, //0x000009ae vpcmpeqb     %ymm4, %ymm6, %ymm5
	0xc5, 0xfd, 0xd7, 0xdd, //0x000009b2 vpmovmskb    %ymm5, %ebx
	0x48, 0xc1, 0xe3, 0x20, //0x000009b6 shlq         $32, %rbx
	0x48, 0x09, 0xda, //0x000009ba orq          %rbx, %rdx
	0x4c, 0x21, 0xd2, //0x000009bd andq         %r10, %rdx
	0x0f, 0x84, 0x31, 0x00, 0x00, 0x00, //0x000009c0 je           LBB0_97
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000009c6 .p2align 4, 0x90
	//0x000009d0 LBB0_103
	0x4c, 0x8d, 0x52, 0xff, //0x000009d0 leaq         $-1(%rdx), %r10
	0x4c, 0x89, 0xd3, //0x000009d4 movq         %r10, %rbx
	0x4c, 0x21, 0xdb, //0x000009d7 andq         %r11, %rbx
	0xf3, 0x48, 0x0f, 0xb8, 0xdb, //0x000009da popcntq      %rbx, %rbx
	0x4c, 0x01, 0xfb, //0x000009df addq         %r15, %rbx
	0x4c, 0x39, 0xcb, //0x000009e2 cmpq         %r9, %rbx
	0x0f, 0x86, 0x52, 0x01, 0x00, 0x00, //0x000009e5 jbe          LBB0_120
	0x49, 0xff, 0xc1, //0x000009eb incq         %r9
	0x4c, 0x21, 0xd2, //0x000009ee andq         %r10, %rdx
	0x0f, 0x85, 0xd9, 0xff, 0xff, 0xff, //0x000009f1 jne          LBB0_103
	//0x000009f7 LBB0_97
	0x49, 0xc1, 0xfd, 0x3f, //0x000009f7 sarq         $63, %r13
	0xf3, 0x49, 0x0f, 0xb8, 0xd3, //0x000009fb popcntq      %r11, %rdx
	0x49, 0x01, 0xd7, //0x00000a00 addq         %rdx, %r15
	0x49, 0x83, 0xc6, 0x40, //0x00000a03 addq         $64, %r14
	0x49, 0x83, 0xc0, 0xc0, //0x00000a07 addq         $-64, %r8
	0x4c, 0x89, 0x6c, 0x24, 0x18, //0x00000a0b movq         %r13, $24(%rsp)
	0x49, 0x83, 0xf8, 0x40, //0x00000a10 cmpq         $64, %r8
	0x0f, 0x8d, 0xb6, 0xfe, 0xff, 0xff, //0x00000a14 jge          LBB0_99
	//0x00000a1a LBB0_105
	0x4d, 0x85, 0xc0, //0x00000a1a testq        %r8, %r8
	0x0f, 0x8e, 0xf3, 0x01, 0x00, 0x00, //0x00000a1d jle          LBB0_131
	0xc5, 0x7c, 0x11, 0x4c, 0x24, 0x40, //0x00000a23 vmovups      %ymm9, $64(%rsp)
	0xc5, 0x7c, 0x11, 0x4c, 0x24, 0x20, //0x00000a29 vmovups      %ymm9, $32(%rsp)
	0x44, 0x89, 0xf2, //0x00000a2f movl         %r14d, %edx
	0x81, 0xe2, 0xff, 0x0f, 0x00, 0x00, //0x00000a32 andl         $4095, %edx
	0x81, 0xfa, 0xc1, 0x0f, 0x00, 0x00, //0x00000a38 cmpl         $4033, %edx
	0x0f, 0x82, 0x8c, 0xfe, 0xff, 0xff, //0x00000a3e jb           LBB0_99
	0x49, 0x83, 0xf8, 0x20, //0x00000a44 cmpq         $32, %r8
	0x0f, 0x82, 0x1d, 0x00, 0x00, 0x00, //0x00000a48 jb           LBB0_109
	0xc4, 0xc1, 0x7e, 0x6f, 0x2e, //0x00000a4e vmovdqu      (%r14), %ymm5
	0xc5, 0xfe, 0x7f, 0x6c, 0x24, 0x20, //0x00000a53 vmovdqu      %ymm5, $32(%rsp)
	0x49, 0x83, 0xc6, 0x20, //0x00000a59 addq         $32, %r14
	0x49, 0x8d, 0x58, 0xe0, //0x00000a5d leaq         $-32(%r8), %rbx
	0x4c, 0x8d, 0x54, 0x24, 0x40, //0x00000a61 leaq         $64(%rsp), %r10
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x00000a66 jmp          LBB0_110
	//0x00000a6b LBB0_109
	0x4c, 0x8d, 0x54, 0x24, 0x20, //0x00000a6b leaq         $32(%rsp), %r10
	0x4c, 0x89, 0xc3, //0x00000a70 movq         %r8, %rbx
	//0x00000a73 LBB0_110
	0x48, 0x83, 0xfb, 0x10, //0x00000a73 cmpq         $16, %rbx
	0x0f, 0x82, 0x5c, 0x00, 0x00, 0x00, //0x00000a77 jb           LBB0_111
	0xc4, 0xc1, 0x7a, 0x6f, 0x2e, //0x00000a7d vmovdqu      (%r14), %xmm5
	0xc4, 0xc1, 0x7a, 0x7f, 0x2a, //0x00000a82 vmovdqu      %xmm5, (%r10)
	0x49, 0x83, 0xc6, 0x10, //0x00000a87 addq         $16, %r14
	0x49, 0x83, 0xc2, 0x10, //0x00000a8b addq         $16, %r10
	0x48, 0x83, 0xc3, 0xf0, //0x00000a8f addq         $-16, %rbx
	0x48, 0x83, 0xfb, 0x08, //0x00000a93 cmpq         $8, %rbx
	0x0f, 0x83, 0x46, 0x00, 0x00, 0x00, //0x00000a97 jae          LBB0_116
	//0x00000a9d LBB0_112
	0x48, 0x83, 0xfb, 0x04, //0x00000a9d cmpq         $4, %rbx
	0x0f, 0x8c, 0x58, 0x00, 0x00, 0x00, //0x00000aa1 jl           LBB0_113
	//0x00000aa7 LBB0_117
	0x41, 0x8b, 0x16, //0x00000aa7 movl         (%r14), %edx
	0x41, 0x89, 0x12, //0x00000aaa movl         %edx, (%r10)
	0x49, 0x83, 0xc6, 0x04, //0x00000aad addq         $4, %r14
	0x49, 0x83, 0xc2, 0x04, //0x00000ab1 addq         $4, %r10
	0x48, 0x83, 0xc3, 0xfc, //0x00000ab5 addq         $-4, %rbx
	0x48, 0x83, 0xfb, 0x02, //0x00000ab9 cmpq         $2, %rbx
	0x0f, 0x83, 0x46, 0x00, 0x00, 0x00, //0x00000abd jae          LBB0_118
	//0x00000ac3 LBB0_114
	0x4c, 0x89, 0xf2, //0x00000ac3 movq         %r14, %rdx
	0x4c, 0x8d, 0x74, 0x24, 0x20, //0x00000ac6 leaq         $32(%rsp), %r14
	0x48, 0x85, 0xdb, //0x00000acb testq        %rbx, %rbx
	0x0f, 0x85, 0x5a, 0x00, 0x00, 0x00, //0x00000ace jne          LBB0_119
	0xe9, 0xf7, 0xfd, 0xff, 0xff, //0x00000ad4 jmp          LBB0_99
	//0x00000ad9 LBB0_111
	0x48, 0x83, 0xfb, 0x08, //0x00000ad9 cmpq         $8, %rbx
	0x0f, 0x82, 0xba, 0xff, 0xff, 0xff, //0x00000add jb           LBB0_112
	//0x00000ae3 LBB0_116
	0x49, 0x8b, 0x16, //0x00000ae3 movq         (%r14), %rdx
	0x49, 0x89, 0x12, //0x00000ae6 movq         %rdx, (%r10)
	0x49, 0x83, 0xc6, 0x08, //0x00000ae9 addq         $8, %r14
	0x49, 0x83, 0xc2, 0x08, //0x00000aed addq         $8, %r10
	0x48, 0x83, 0xc3, 0xf8, //0x00000af1 addq         $-8, %rbx
	0x48, 0x83, 0xfb, 0x04, //0x00000af5 cmpq         $4, %rbx
	0x0f, 0x8d, 0xa8, 0xff, 0xff, 0xff, //0x00000af9 jge          LBB0_117
	//0x00000aff LBB0_113
	0x48, 0x83, 0xfb, 0x02, //0x00000aff cmpq         $2, %rbx
	0x0f, 0x82, 0xba, 0xff, 0xff, 0xff, //0x00000b03 jb           LBB0_114
	//0x00000b09 LBB0_118
	0x41, 0x0f, 0xb7, 0x16, //0x00000b09 movzwl       (%r14), %edx
	0x66, 0x41, 0x89, 0x12, //0x00000b0d movw         %dx, (%r10)
	0x49, 0x83, 0xc6, 0x02, //0x00000b11 addq         $2, %r14
	0x49, 0x83, 0xc2, 0x02, //0x00000b15 addq         $2, %r10
	0x48, 0x83, 0xc3, 0xfe, //0x00000b19 addq         $-2, %rbx
	0x4c, 0x89, 0xf2, //0x00000b1d movq         %r14, %rdx
	0x4c, 0x8d, 0x74, 0x24, 0x20, //0x00000b20 leaq         $32(%rsp), %r14
	0x48, 0x85, 0xdb, //0x00000b25 testq        %rbx, %rbx
	0x0f, 0x84, 0xa2, 0xfd, 0xff, 0xff, //0x00000b28 je           LBB0_99
	//0x00000b2e LBB0_119
	0x8a, 0x12, //0x00000b2e movb         (%rdx), %dl
	0x41, 0x88, 0x12, //0x00000b30 movb         %dl, (%r10)
	0x4c, 0x8d, 0x74, 0x24, 0x20, //0x00000b33 leaq         $32(%rsp), %r14
	0xe9, 0x93, 0xfd, 0xff, 0xff, //0x00000b38 jmp          LBB0_99
	//0x00000b3d LBB0_120
	0x48, 0x8b, 0x47, 0x08, //0x00000b3d movq         $8(%rdi), %rax
	0x48, 0x0f, 0xbc, 0xd2, //0x00000b41 bsfq         %rdx, %rdx
	0x4c, 0x29, 0xc2, //0x00000b45 subq         %r8, %rdx
	0x48, 0x8d, 0x44, 0x02, 0x01, //0x00000b48 leaq         $1(%rdx,%rax), %rax
	0x48, 0x89, 0x06, //0x00000b4d movq         %rax, (%rsi)
	0x48, 0x8b, 0x57, 0x08, //0x00000b50 movq         $8(%rdi), %rdx
	0x48, 0x39, 0xd0, //0x00000b54 cmpq         %rdx, %rax
	0x48, 0x0f, 0x47, 0xc2, //0x00000b57 cmovaq       %rdx, %rax
	0x48, 0x89, 0x06, //0x00000b5b movq         %rax, (%rsi)
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000b5e movq         $-1, %rax
	0x48, 0x0f, 0x47, 0xc8, //0x00000b65 cmovaq       %rax, %rcx
	0xe9, 0x2a, 0xf9, 0xff, 0xff, //0x00000b69 jmp          LBB0_55
	//0x00000b6e LBB0_121
	0x49, 0x0f, 0xbc, 0xc3, //0x00000b6e bsfq         %r11, %rax
	0x49, 0x01, 0xc2, //0x00000b72 addq         %rax, %r10
	0x4d, 0x01, 0xfa, //0x00000b75 addq         %r15, %r10
	0x4d, 0x29, 0xf2, //0x00000b78 subq         %r14, %r10
	0x49, 0x83, 0xc2, 0x02, //0x00000b7b addq         $2, %r10
	0x4c, 0x89, 0x16, //0x00000b7f movq         %r10, (%rsi)
	0xe9, 0x11, 0xf9, 0xff, 0xff, //0x00000b82 jmp          LBB0_55
	//0x00000b87 LBB0_122
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x00000b87 movq         $-2, %rdx
	0xb8, 0x02, 0x00, 0x00, 0x00, //0x00000b8e movl         $2, %eax
	0x48, 0x01, 0xc3, //0x00000b93 addq         %rax, %rbx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000b96 movq         $-1, %rax
	0x49, 0x01, 0xd7, //0x00000b9d addq         %rdx, %r15
	0x0f, 0x8e, 0xf5, 0xf8, 0xff, 0xff, //0x00000ba0 jle          LBB0_56
	//0x00000ba6 LBB0_124
	0x0f, 0xb6, 0x03, //0x00000ba6 movzbl       (%rbx), %eax
	0x3c, 0x5c, //0x00000ba9 cmpb         $92, %al
	0x0f, 0x84, 0xd6, 0xff, 0xff, 0xff, //0x00000bab je           LBB0_122
	0x3c, 0x22, //0x00000bb1 cmpb         $34, %al
	0x0f, 0x84, 0x4a, 0x00, 0x00, 0x00, //0x00000bb3 je           LBB0_129
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00000bb9 movq         $-1, %rdx
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00000bc0 movl         $1, %eax
	0x48, 0x01, 0xc3, //0x00000bc5 addq         %rax, %rbx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000bc8 movq         $-1, %rax
	0x49, 0x01, 0xd7, //0x00000bcf addq         %rdx, %r15
	0x0f, 0x8f, 0xce, 0xff, 0xff, 0xff, //0x00000bd2 jg           LBB0_124
	0xe9, 0xbe, 0xf8, 0xff, 0xff, //0x00000bd8 jmp          LBB0_56
	//0x00000bdd LBB0_127
	0x0f, 0xb7, 0xc7, //0x00000bdd movzwl       %di, %eax
	0xe9, 0xa6, 0xf8, 0xff, 0xff, //0x00000be0 jmp          LBB0_53
	//0x00000be5 LBB0_128
	0x4c, 0x89, 0xf0, //0x00000be5 movq         %r14, %rax
	0x48, 0xf7, 0xd0, //0x00000be8 notq         %rax
	0x48, 0x01, 0xc1, //0x00000beb addq         %rax, %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000bee movq         $-1, %rax
	0x4c, 0x39, 0xc1, //0x00000bf5 cmpq         %r8, %rcx
	0x0f, 0x82, 0x2c, 0xf7, 0xff, 0xff, //0x00000bf8 jb           LBB0_32
	0xe9, 0x98, 0xf8, 0xff, 0xff, //0x00000bfe jmp          LBB0_56
	//0x00000c03 LBB0_129
	0x4c, 0x29, 0xf3, //0x00000c03 subq         %r14, %rbx
	0x48, 0xff, 0xc3, //0x00000c06 incq         %rbx
	0xe9, 0x87, 0xf8, 0xff, 0xff, //0x00000c09 jmp          LBB0_54
	//0x00000c0e LBB0_130
	0x4c, 0x01, 0xf3, //0x00000c0e addq         %r14, %rbx
	0xe9, 0x99, 0xf9, 0xff, 0xff, //0x00000c11 jmp          LBB0_69
	//0x00000c16 LBB0_131
	0x48, 0x8b, 0x4f, 0x08, //0x00000c16 movq         $8(%rdi), %rcx
	0x48, 0x89, 0x0e, //0x00000c1a movq         %rcx, (%rsi)
	0xe9, 0x79, 0xf8, 0xff, 0xff, //0x00000c1d jmp          LBB0_56
	//0x00000c22 LBB0_132
	0x49, 0x8d, 0x51, 0xff, //0x00000c22 leaq         $-1(%r9), %rdx
	0x4c, 0x39, 0xfa, //0x00000c26 cmpq         %r15, %rdx
	0x0f, 0x84, 0x6c, 0xf8, 0xff, 0xff, //0x00000c29 je           LBB0_56
	0x4b, 0x8d, 0x5c, 0x17, 0x02, //0x00000c2f leaq         $2(%r15,%r10), %rbx
	0x4d, 0x29, 0xf9, //0x00000c34 subq         %r15, %r9
	0x49, 0x83, 0xc1, 0xfe, //0x00000c37 addq         $-2, %r9
	0x4d, 0x89, 0xcf, //0x00000c3b movq         %r9, %r15
	0xe9, 0x6c, 0xf9, 0xff, 0xff, //0x00000c3e jmp          LBB0_69
	0x90, //0x00000c43 .p2align 2, 0x90
	// // .set L0_0_set_56, LBB0_56-LJTI0_0
	// // .set L0_0_set_58, LBB0_58-LJTI0_0
	// // .set L0_0_set_59, LBB0_59-LJTI0_0
	// // .set L0_0_set_34, LBB0_34-LJTI0_0
	// // .set L0_0_set_70, LBB0_70-LJTI0_0
	// // .set L0_0_set_94, LBB0_94-LJTI0_0
	// // .set L0_0_set_57, LBB0_57-LJTI0_0
	// // .set L0_0_set_96, LBB0_96-LJTI0_0
	//0x00000c44 LJTI0_0
	0x57, 0xf8, 0xff, 0xff, //0x00000c44 .long L0_0_set_56
	0x7c, 0xf8, 0xff, 0xff, //0x00000c48 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000c4c .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000c50 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000c54 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000c58 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000c5c .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000c60 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000c64 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000c68 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000c6c .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000c70 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000c74 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000c78 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000c7c .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000c80 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000c84 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000c88 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000c8c .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000c90 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000c94 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000c98 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000c9c .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000ca0 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000ca4 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000ca8 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000cac .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000cb0 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000cb4 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000cb8 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000cbc .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000cc0 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000cc4 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000cc8 .long L0_0_set_58
	0x8b, 0xf8, 0xff, 0xff, //0x00000ccc .long L0_0_set_59
	0x7c, 0xf8, 0xff, 0xff, //0x00000cd0 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000cd4 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000cd8 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000cdc .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000ce0 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000ce4 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000ce8 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000cec .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000cf0 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000cf4 .long L0_0_set_58
	0x12, 0xf7, 0xff, 0xff, //0x00000cf8 .long L0_0_set_34
	0x7c, 0xf8, 0xff, 0xff, //0x00000cfc .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d00 .long L0_0_set_58
	0x12, 0xf7, 0xff, 0xff, //0x00000d04 .long L0_0_set_34
	0x12, 0xf7, 0xff, 0xff, //0x00000d08 .long L0_0_set_34
	0x12, 0xf7, 0xff, 0xff, //0x00000d0c .long L0_0_set_34
	0x12, 0xf7, 0xff, 0xff, //0x00000d10 .long L0_0_set_34
	0x12, 0xf7, 0xff, 0xff, //0x00000d14 .long L0_0_set_34
	0x12, 0xf7, 0xff, 0xff, //0x00000d18 .long L0_0_set_34
	0x12, 0xf7, 0xff, 0xff, //0x00000d1c .long L0_0_set_34
	0x12, 0xf7, 0xff, 0xff, //0x00000d20 .long L0_0_set_34
	0x12, 0xf7, 0xff, 0xff, //0x00000d24 .long L0_0_set_34
	0x12, 0xf7, 0xff, 0xff, //0x00000d28 .long L0_0_set_34
	0x7c, 0xf8, 0xff, 0xff, //0x00000d2c .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d30 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d34 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d38 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d3c .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d40 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d44 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d48 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d4c .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d50 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d54 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d58 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d5c .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d60 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d64 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d68 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d6c .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d70 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d74 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d78 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d7c .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d80 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d84 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d88 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d8c .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d90 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d94 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d98 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000d9c .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000da0 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000da4 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000da8 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000dac .long L0_0_set_58
	0x79, 0xf9, 0xff, 0xff, //0x00000db0 .long L0_0_set_70
	0x7c, 0xf8, 0xff, 0xff, //0x00000db4 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000db8 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000dbc .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000dc0 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000dc4 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000dc8 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000dcc .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000dd0 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000dd4 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000dd8 .long L0_0_set_58
	0x29, 0xfc, 0xff, 0xff, //0x00000ddc .long L0_0_set_94
	0x7c, 0xf8, 0xff, 0xff, //0x00000de0 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000de4 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000de8 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000dec .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000df0 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000df4 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000df8 .long L0_0_set_58
	0x69, 0xf8, 0xff, 0xff, //0x00000dfc .long L0_0_set_57
	0x7c, 0xf8, 0xff, 0xff, //0x00000e00 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000e04 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000e08 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000e0c .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000e10 .long L0_0_set_58
	0x69, 0xf8, 0xff, 0xff, //0x00000e14 .long L0_0_set_57
	0x7c, 0xf8, 0xff, 0xff, //0x00000e18 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000e1c .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000e20 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000e24 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000e28 .long L0_0_set_58
	0x7c, 0xf8, 0xff, 0xff, //0x00000e2c .long L0_0_set_58
	0x3f, 0xfc, 0xff, 0xff, //0x00000e30 .long L0_0_set_96
	//0x00000e34 .p2align 2, 0x00
	//0x00000e34 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00000e34 .long 2
}
 
