// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx2

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__unquote = 48
)

const (
    _stack__unquote = 72
)

const (
    _size__unquote = 2464
)

var (
    _pcsp__unquote = [][2]uint32{
        {1, 0},
        {4, 8},
        {6, 16},
        {8, 24},
        {10, 32},
        {12, 40},
        {13, 48},
        {79, 72},
        {83, 48},
        {84, 40},
        {86, 32},
        {88, 24},
        {90, 16},
        {92, 8},
        {96, 0},
        {2464, 72},
    }
)

var _cfunc_unquote = []loader.CFunc{
    {"_unquote_entry", 0,  _entry__unquote, 0, nil},
    {"_unquote", _entry__unquote, _size__unquote, _stack__unquote, _pcsp__unquote},
}
