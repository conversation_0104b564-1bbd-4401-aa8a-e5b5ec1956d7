// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_get_by_path = []byte{
	// .p2align 5, 0x00
	// LCPI0_0
	0x20, // .byte 32
	0x00, //0x00000001 .byte 0
	0x00, //0x00000002 .byte 0
	0x00, //0x00000003 .byte 0
	0x00, //0x00000004 .byte 0
	0x00, //0x00000005 .byte 0
	0x00, //0x00000006 .byte 0
	0x00, //0x00000007 .byte 0
	0x00, //0x00000008 .byte 0
	0x09, //0x00000009 .byte 9
	0x0a, //0x0000000a .byte 10
	0x00, //0x0000000b .byte 0
	0x00, //0x0000000c .byte 0
	0x0d, //0x0000000d .byte 13
	0x00, //0x0000000e .byte 0
	0x00, //0x0000000f .byte 0
	0x20, //0x00000010 .byte 32
	0x00, //0x00000011 .byte 0
	0x00, //0x00000012 .byte 0
	0x00, //0x00000013 .byte 0
	0x00, //0x00000014 .byte 0
	0x00, //0x00000015 .byte 0
	0x00, //0x00000016 .byte 0
	0x00, //0x00000017 .byte 0
	0x00, //0x00000018 .byte 0
	0x09, //0x00000019 .byte 9
	0x0a, //0x0000001a .byte 10
	0x00, //0x0000001b .byte 0
	0x00, //0x0000001c .byte 0
	0x0d, //0x0000001d .byte 13
	0x00, //0x0000001e .byte 0
	0x00, //0x0000001f .byte 0
	//0x00000020 LCPI0_1
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x00000020 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x00000030 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	//0x00000040 LCPI0_2
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000040 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000050 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000060 LCPI0_3
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x00000060 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x00000070 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	//0x00000080 LCPI0_7
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000080 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000090 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x000000a0 LCPI0_8
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x000000a0 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x000000b0 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x000000c0 LCPI0_9
	0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, //0x000000c0 QUAD $0x7b7b7b7b7b7b7b7b; QUAD $0x7b7b7b7b7b7b7b7b  // .space 16, '{{{{{{{{{{{{{{{{'
	0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, //0x000000d0 QUAD $0x7b7b7b7b7b7b7b7b; QUAD $0x7b7b7b7b7b7b7b7b  // .space 16, '{{{{{{{{{{{{{{{{'
	//0x000000e0 LCPI0_10
	0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, //0x000000e0 QUAD $0x5b5b5b5b5b5b5b5b; QUAD $0x5b5b5b5b5b5b5b5b  // .space 16, '[[[[[[[[[[[[[[[['
	0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, //0x000000f0 QUAD $0x5b5b5b5b5b5b5b5b; QUAD $0x5b5b5b5b5b5b5b5b  // .space 16, '[[[[[[[[[[[[[[[['
	//0x00000100 LCPI0_11
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000100 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000110 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	//0x00000120 LCPI0_12
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000120 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000130 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	//0x00000140 LCPI0_13
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x00000140 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x00000150 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	//0x00000160 LCPI0_14
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000160 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000170 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x00000180 LCPI0_15
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000180 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000190 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x000001a0 LCPI0_16
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x000001a0 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x000001b0 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x000001c0 LCPI0_17
	0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, //0x000001c0 QUAD $0x6565656565656565; QUAD $0x6565656565656565  // .space 16, 'eeeeeeeeeeeeeeee'
	0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, //0x000001d0 QUAD $0x6565656565656565; QUAD $0x6565656565656565  // .space 16, 'eeeeeeeeeeeeeeee'
	//0x000001e0 .p2align 4, 0x00
	//0x000001e0 LCPI0_4
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x000001e0 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	//0x000001f0 LCPI0_5
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x000001f0 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000200 LCPI0_6
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x00000200 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	//0x00000210 LCPI0_18
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000210 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	//0x00000220 LCPI0_19
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x00000220 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	//0x00000230 LCPI0_20
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000230 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x00000240 LCPI0_21
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000240 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000250 LCPI0_22
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000250 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x00000260 LCPI0_23
	0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, //0x00000260 QUAD $0x6565656565656565; QUAD $0x6565656565656565  // .space 16, 'eeeeeeeeeeeeeeee'
	//0x00000270 .p2align 4, 0x90
	//0x00000270 _get_by_path
	0x55, //0x00000270 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000271 movq         %rsp, %rbp
	0x41, 0x57, //0x00000274 pushq        %r15
	0x41, 0x56, //0x00000276 pushq        %r14
	0x41, 0x55, //0x00000278 pushq        %r13
	0x41, 0x54, //0x0000027a pushq        %r12
	0x53, //0x0000027c pushq        %rbx
	0x48, 0x81, 0xec, 0xc0, 0x00, 0x00, 0x00, //0x0000027d subq         $192, %rsp
	0x49, 0x89, 0xca, //0x00000284 movq         %rcx, %r10
	0x49, 0x89, 0xf6, //0x00000287 movq         %rsi, %r14
	0x49, 0x89, 0xff, //0x0000028a movq         %rdi, %r15
	0x48, 0x8b, 0x4a, 0x08, //0x0000028d movq         $8(%rdx), %rcx
	0x48, 0x85, 0xc9, //0x00000291 testq        %rcx, %rcx
	0x48, 0x89, 0x74, 0x24, 0x08, //0x00000294 movq         %rsi, $8(%rsp)
	0x48, 0x89, 0x7c, 0x24, 0x10, //0x00000299 movq         %rdi, $16(%rsp)
	0x0f, 0x84, 0x2f, 0x2c, 0x00, 0x00, //0x0000029e je           LBB0_525
	0x4c, 0x8b, 0x2a, //0x000002a4 movq         (%rdx), %r13
	0x48, 0xc1, 0xe1, 0x04, //0x000002a7 shlq         $4, %rcx
	0x4c, 0x01, 0xe9, //0x000002ab addq         %r13, %rcx
	0x4d, 0x8d, 0x5f, 0x08, //0x000002ae leaq         $8(%r15), %r11
	0x4d, 0x8b, 0x07, //0x000002b2 movq         (%r15), %r8
	0x49, 0x8b, 0x06, //0x000002b5 movq         (%r14), %rax
	0xc5, 0xfe, 0x6f, 0x05, 0x40, 0xfd, 0xff, 0xff, //0x000002b8 vmovdqu      $-704(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0xb8, 0xfd, 0xff, 0xff, //0x000002c0 vmovdqu      $-584(%rip), %ymm1  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x15, 0xd0, 0xfd, 0xff, 0xff, //0x000002c8 vmovdqu      $-560(%rip), %ymm2  /* LCPI0_8+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x48, 0xfd, 0xff, 0xff, //0x000002d0 vmovdqu      $-696(%rip), %ymm3  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x60, 0xfd, 0xff, 0xff, //0x000002d8 vmovdqu      $-672(%rip), %ymm4  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x2d, 0x78, 0xfd, 0xff, 0xff, //0x000002e0 vmovdqu      $-648(%rip), %ymm5  /* LCPI0_3+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x05, 0x10, 0xff, 0xff, 0xff, //0x000002e8 vmovdqu      $-240(%rip), %xmm8  /* LCPI0_6+0(%rip) */
	0xc4, 0x41, 0x31, 0x76, 0xc9, //0x000002f0 vpcmpeqd     %xmm9, %xmm9, %xmm9
	0xc5, 0x7e, 0x6f, 0x15, 0xe3, 0xfd, 0xff, 0xff, //0x000002f5 vmovdqu      $-541(%rip), %ymm10  /* LCPI0_10+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0xfb, 0xfd, 0xff, 0xff, //0x000002fd vmovdqu      $-517(%rip), %ymm11  /* LCPI0_11+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0xb3, 0xfd, 0xff, 0xff, //0x00000305 vmovdqu      $-589(%rip), %ymm13  /* LCPI0_9+0(%rip) */
	0x4c, 0x89, 0x5c, 0x24, 0x18, //0x0000030d movq         %r11, $24(%rsp)
	0x4c, 0x89, 0x54, 0x24, 0x28, //0x00000312 movq         %r10, $40(%rsp)
	0x48, 0x89, 0x8c, 0x24, 0xa0, 0x00, 0x00, 0x00, //0x00000317 movq         %rcx, $160(%rsp)
	//0x0000031f LBB0_2
	0x49, 0x8b, 0x0b, //0x0000031f movq         (%r11), %rcx
	0x48, 0x39, 0xc8, //0x00000322 cmpq         %rcx, %rax
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x00000325 jae          LBB0_7
	0x41, 0x8a, 0x14, 0x00, //0x0000032b movb         (%r8,%rax), %dl
	0x80, 0xfa, 0x0d, //0x0000032f cmpb         $13, %dl
	0x0f, 0x84, 0x28, 0x00, 0x00, 0x00, //0x00000332 je           LBB0_7
	0x80, 0xfa, 0x20, //0x00000338 cmpb         $32, %dl
	0x0f, 0x84, 0x1f, 0x00, 0x00, 0x00, //0x0000033b je           LBB0_7
	0x80, 0xc2, 0xf7, //0x00000341 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00000344 cmpb         $1, %dl
	0x0f, 0x86, 0x13, 0x00, 0x00, 0x00, //0x00000347 jbe          LBB0_7
	0x48, 0x89, 0xc2, //0x0000034d movq         %rax, %rdx
	0xe9, 0xc3, 0x01, 0x00, 0x00, //0x00000350 jmp          LBB0_33
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000355 .p2align 4, 0x90
	//0x00000360 LBB0_7
	0x48, 0x8d, 0x50, 0x01, //0x00000360 leaq         $1(%rax), %rdx
	0x48, 0x39, 0xca, //0x00000364 cmpq         %rcx, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000367 jae          LBB0_11
	0x41, 0x8a, 0x1c, 0x10, //0x0000036d movb         (%r8,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x00000371 cmpb         $13, %bl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000374 je           LBB0_11
	0x80, 0xfb, 0x20, //0x0000037a cmpb         $32, %bl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000037d je           LBB0_11
	0x80, 0xc3, 0xf7, //0x00000383 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000386 cmpb         $1, %bl
	0x0f, 0x87, 0x89, 0x01, 0x00, 0x00, //0x00000389 ja           LBB0_33
	0x90, //0x0000038f .p2align 4, 0x90
	//0x00000390 LBB0_11
	0x48, 0x8d, 0x50, 0x02, //0x00000390 leaq         $2(%rax), %rdx
	0x48, 0x39, 0xca, //0x00000394 cmpq         %rcx, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000397 jae          LBB0_15
	0x41, 0x8a, 0x1c, 0x10, //0x0000039d movb         (%r8,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x000003a1 cmpb         $13, %bl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000003a4 je           LBB0_15
	0x80, 0xfb, 0x20, //0x000003aa cmpb         $32, %bl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000003ad je           LBB0_15
	0x80, 0xc3, 0xf7, //0x000003b3 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x000003b6 cmpb         $1, %bl
	0x0f, 0x87, 0x59, 0x01, 0x00, 0x00, //0x000003b9 ja           LBB0_33
	0x90, //0x000003bf .p2align 4, 0x90
	//0x000003c0 LBB0_15
	0x48, 0x8d, 0x50, 0x03, //0x000003c0 leaq         $3(%rax), %rdx
	0x48, 0x39, 0xca, //0x000003c4 cmpq         %rcx, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000003c7 jae          LBB0_19
	0x41, 0x8a, 0x1c, 0x10, //0x000003cd movb         (%r8,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x000003d1 cmpb         $13, %bl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000003d4 je           LBB0_19
	0x80, 0xfb, 0x20, //0x000003da cmpb         $32, %bl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000003dd je           LBB0_19
	0x80, 0xc3, 0xf7, //0x000003e3 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x000003e6 cmpb         $1, %bl
	0x0f, 0x87, 0x29, 0x01, 0x00, 0x00, //0x000003e9 ja           LBB0_33
	0x90, //0x000003ef .p2align 4, 0x90
	//0x000003f0 LBB0_19
	0x4c, 0x8d, 0x50, 0x04, //0x000003f0 leaq         $4(%rax), %r10
	0x48, 0x89, 0xcf, //0x000003f4 movq         %rcx, %rdi
	0x4c, 0x29, 0xd7, //0x000003f7 subq         %r10, %rdi
	0x0f, 0x86, 0xe0, 0x00, 0x00, 0x00, //0x000003fa jbe          LBB0_627
	0x4d, 0x01, 0xc2, //0x00000400 addq         %r8, %r10
	0x48, 0x83, 0xff, 0x20, //0x00000403 cmpq         $32, %rdi
	0x0f, 0x82, 0x58, 0x00, 0x00, 0x00, //0x00000407 jb           LBB0_25
	0x48, 0x89, 0xca, //0x0000040d movq         %rcx, %rdx
	0x48, 0x29, 0xc2, //0x00000410 subq         %rax, %rdx
	0x48, 0x83, 0xc2, 0xdc, //0x00000413 addq         $-36, %rdx
	0x48, 0x89, 0xd6, //0x00000417 movq         %rdx, %rsi
	0x48, 0x83, 0xe6, 0xe0, //0x0000041a andq         $-32, %rsi
	0x48, 0x01, 0xc6, //0x0000041e addq         %rax, %rsi
	0x49, 0x8d, 0x74, 0x30, 0x24, //0x00000421 leaq         $36(%r8,%rsi), %rsi
	0x83, 0xe2, 0x1f, //0x00000426 andl         $31, %edx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000429 .p2align 4, 0x90
	//0x00000430 LBB0_22
	0xc4, 0x41, 0x7e, 0x6f, 0x32, //0x00000430 vmovdqu      (%r10), %ymm14
	0xc4, 0x42, 0x7d, 0x00, 0xfe, //0x00000435 vpshufb      %ymm14, %ymm0, %ymm15
	0xc4, 0x41, 0x0d, 0x74, 0xf7, //0x0000043a vpcmpeqb     %ymm15, %ymm14, %ymm14
	0xc4, 0xc1, 0x7d, 0xd7, 0xde, //0x0000043f vpmovmskb    %ymm14, %ebx
	0x83, 0xfb, 0xff, //0x00000444 cmpl         $-1, %ebx
	0x0f, 0x85, 0xb3, 0x00, 0x00, 0x00, //0x00000447 jne          LBB0_32
	0x49, 0x83, 0xc2, 0x20, //0x0000044d addq         $32, %r10
	0x48, 0x83, 0xc7, 0xe0, //0x00000451 addq         $-32, %rdi
	0x48, 0x83, 0xff, 0x1f, //0x00000455 cmpq         $31, %rdi
	0x0f, 0x87, 0xd1, 0xff, 0xff, 0xff, //0x00000459 ja           LBB0_22
	0x48, 0x89, 0xd7, //0x0000045f movq         %rdx, %rdi
	0x49, 0x89, 0xf2, //0x00000462 movq         %rsi, %r10
	//0x00000465 LBB0_25
	0x48, 0x85, 0xff, //0x00000465 testq        %rdi, %rdi
	0x0f, 0x84, 0x43, 0x00, 0x00, 0x00, //0x00000468 je           LBB0_31
	0x4d, 0x89, 0xc1, //0x0000046e movq         %r8, %r9
	0x4d, 0x8d, 0x04, 0x3a, //0x00000471 leaq         (%r10,%rdi), %r8
	0x49, 0xff, 0xc2, //0x00000475 incq         %r10
	0x4c, 0x89, 0xd2, //0x00000478 movq         %r10, %rdx
	//0x0000047b LBB0_27
	0x0f, 0xbe, 0x5a, 0xff, //0x0000047b movsbl       $-1(%rdx), %ebx
	0x83, 0xfb, 0x20, //0x0000047f cmpl         $32, %ebx
	0x0f, 0x87, 0x1a, 0x2a, 0x00, 0x00, //0x00000482 ja           LBB0_34
	0x48, 0xbe, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00000488 movabsq      $4294977024, %rsi
	0x48, 0x0f, 0xa3, 0xde, //0x00000492 btq          %rbx, %rsi
	0x0f, 0x83, 0x06, 0x2a, 0x00, 0x00, //0x00000496 jae          LBB0_34
	0x48, 0xff, 0xcf, //0x0000049c decq         %rdi
	0x48, 0xff, 0xc2, //0x0000049f incq         %rdx
	0x48, 0x85, 0xff, //0x000004a2 testq        %rdi, %rdi
	0x0f, 0x85, 0xd0, 0xff, 0xff, 0xff, //0x000004a5 jne          LBB0_27
	0x4d, 0x89, 0xc2, //0x000004ab movq         %r8, %r10
	0x4d, 0x89, 0xc8, //0x000004ae movq         %r9, %r8
	//0x000004b1 LBB0_31
	0x4d, 0x29, 0xc2, //0x000004b1 subq         %r8, %r10
	0x4c, 0x89, 0xd2, //0x000004b4 movq         %r10, %rdx
	0x48, 0x39, 0xca, //0x000004b7 cmpq         %rcx, %rdx
	0x0f, 0x82, 0x58, 0x00, 0x00, 0x00, //0x000004ba jb           LBB0_33
	//0x000004c0 LBB0_35
	0x31, 0xc9, //0x000004c0 xorl         %ecx, %ecx
	0x49, 0x89, 0xc2, //0x000004c2 movq         %rax, %r10
	0x49, 0x8b, 0x45, 0x00, //0x000004c5 movq         (%r13), %rax
	0x48, 0x85, 0xc0, //0x000004c9 testq        %rax, %rax
	0x0f, 0x85, 0x5e, 0x00, 0x00, 0x00, //0x000004cc jne          LBB0_36
	0xe9, 0x2e, 0x2c, 0x00, 0x00, //0x000004d2 jmp          LBB0_628
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000004d7 .p2align 4, 0x90
	//0x000004e0 LBB0_627
	0x4d, 0x89, 0x16, //0x000004e0 movq         %r10, (%r14)
	0x31, 0xc9, //0x000004e3 xorl         %ecx, %ecx
	0x49, 0x8b, 0x45, 0x00, //0x000004e5 movq         (%r13), %rax
	0x48, 0x85, 0xc0, //0x000004e9 testq        %rax, %rax
	0x0f, 0x85, 0x3e, 0x00, 0x00, 0x00, //0x000004ec jne          LBB0_36
	0xe9, 0x0e, 0x2c, 0x00, 0x00, //0x000004f2 jmp          LBB0_628
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000004f7 .p2align 4, 0x90
	//0x00000500 LBB0_32
	0x4d, 0x29, 0xc2, //0x00000500 subq         %r8, %r10
	0xf7, 0xd3, //0x00000503 notl         %ebx
	0x48, 0x63, 0xd3, //0x00000505 movslq       %ebx, %rdx
	0x48, 0x0f, 0xbc, 0xd2, //0x00000508 bsfq         %rdx, %rdx
	0x4c, 0x01, 0xd2, //0x0000050c addq         %r10, %rdx
	0x48, 0x39, 0xca, //0x0000050f cmpq         %rcx, %rdx
	0x0f, 0x83, 0xa8, 0xff, 0xff, 0xff, //0x00000512 jae          LBB0_35
	//0x00000518 LBB0_33
	0x4c, 0x8d, 0x52, 0x01, //0x00000518 leaq         $1(%rdx), %r10
	0x4d, 0x89, 0x16, //0x0000051c movq         %r10, (%r14)
	0x41, 0x8a, 0x0c, 0x10, //0x0000051f movb         (%r8,%rdx), %cl
	0x49, 0x8b, 0x45, 0x00, //0x00000523 movq         (%r13), %rax
	0x48, 0x85, 0xc0, //0x00000527 testq        %rax, %rax
	0x0f, 0x84, 0xd5, 0x2b, 0x00, 0x00, //0x0000052a je           LBB0_628
	//0x00000530 LBB0_36
	0x8a, 0x40, 0x17, //0x00000530 movb         $23(%rax), %al
	0x24, 0x1f, //0x00000533 andb         $31, %al
	0x3c, 0x02, //0x00000535 cmpb         $2, %al
	0x0f, 0x84, 0x53, 0x1a, 0x00, 0x00, //0x00000537 je           LBB0_387
	0x3c, 0x18, //0x0000053d cmpb         $24, %al
	0x0f, 0x85, 0xc0, 0x2b, 0x00, 0x00, //0x0000053f jne          LBB0_628
	0x80, 0xf9, 0x7b, //0x00000545 cmpb         $123, %cl
	0x0f, 0x85, 0xa3, 0x2d, 0x00, 0x00, //0x00000548 jne          LBB0_657
	//0x0000054e LBB0_39
	0x49, 0x8b, 0x03, //0x0000054e movq         (%r11), %rax
	0x49, 0x39, 0xc2, //0x00000551 cmpq         %rax, %r10
	0x0f, 0x83, 0x36, 0x00, 0x00, 0x00, //0x00000554 jae          LBB0_44
	0x43, 0x8a, 0x0c, 0x10, //0x0000055a movb         (%r8,%r10), %cl
	0x80, 0xf9, 0x0d, //0x0000055e cmpb         $13, %cl
	0x0f, 0x84, 0x29, 0x00, 0x00, 0x00, //0x00000561 je           LBB0_44
	0x80, 0xf9, 0x20, //0x00000567 cmpb         $32, %cl
	0x0f, 0x84, 0x20, 0x00, 0x00, 0x00, //0x0000056a je           LBB0_44
	0x80, 0xc1, 0xf7, //0x00000570 addb         $-9, %cl
	0x80, 0xf9, 0x01, //0x00000573 cmpb         $1, %cl
	0x0f, 0x86, 0x14, 0x00, 0x00, 0x00, //0x00000576 jbe          LBB0_44
	0x4c, 0x89, 0xd1, //0x0000057c movq         %r10, %rcx
	0xe9, 0x84, 0x01, 0x00, 0x00, //0x0000057f jmp          LBB0_70
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000584 .p2align 4, 0x90
	//0x00000590 LBB0_44
	0x49, 0x8d, 0x4a, 0x01, //0x00000590 leaq         $1(%r10), %rcx
	0x48, 0x39, 0xc1, //0x00000594 cmpq         %rax, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000597 jae          LBB0_48
	0x41, 0x8a, 0x14, 0x08, //0x0000059d movb         (%r8,%rcx), %dl
	0x80, 0xfa, 0x0d, //0x000005a1 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000005a4 je           LBB0_48
	0x80, 0xfa, 0x20, //0x000005aa cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000005ad je           LBB0_48
	0x80, 0xc2, 0xf7, //0x000005b3 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x000005b6 cmpb         $1, %dl
	0x0f, 0x87, 0x49, 0x01, 0x00, 0x00, //0x000005b9 ja           LBB0_70
	0x90, //0x000005bf .p2align 4, 0x90
	//0x000005c0 LBB0_48
	0x49, 0x8d, 0x4a, 0x02, //0x000005c0 leaq         $2(%r10), %rcx
	0x48, 0x39, 0xc1, //0x000005c4 cmpq         %rax, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000005c7 jae          LBB0_52
	0x41, 0x8a, 0x14, 0x08, //0x000005cd movb         (%r8,%rcx), %dl
	0x80, 0xfa, 0x0d, //0x000005d1 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000005d4 je           LBB0_52
	0x80, 0xfa, 0x20, //0x000005da cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000005dd je           LBB0_52
	0x80, 0xc2, 0xf7, //0x000005e3 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x000005e6 cmpb         $1, %dl
	0x0f, 0x87, 0x19, 0x01, 0x00, 0x00, //0x000005e9 ja           LBB0_70
	0x90, //0x000005ef .p2align 4, 0x90
	//0x000005f0 LBB0_52
	0x49, 0x8d, 0x4a, 0x03, //0x000005f0 leaq         $3(%r10), %rcx
	0x48, 0x39, 0xc1, //0x000005f4 cmpq         %rax, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000005f7 jae          LBB0_56
	0x41, 0x8a, 0x14, 0x08, //0x000005fd movb         (%r8,%rcx), %dl
	0x80, 0xfa, 0x0d, //0x00000601 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000604 je           LBB0_56
	0x80, 0xfa, 0x20, //0x0000060a cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000060d je           LBB0_56
	0x80, 0xc2, 0xf7, //0x00000613 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00000616 cmpb         $1, %dl
	0x0f, 0x87, 0xe9, 0x00, 0x00, 0x00, //0x00000619 ja           LBB0_70
	0x90, //0x0000061f .p2align 4, 0x90
	//0x00000620 LBB0_56
	0x49, 0x8d, 0x4a, 0x04, //0x00000620 leaq         $4(%r10), %rcx
	0x48, 0x89, 0xc2, //0x00000624 movq         %rax, %rdx
	0x48, 0x29, 0xca, //0x00000627 subq         %rcx, %rdx
	0x0f, 0x86, 0xbd, 0x2a, 0x00, 0x00, //0x0000062a jbe          LBB0_625
	0x4c, 0x01, 0xc1, //0x00000630 addq         %r8, %rcx
	0x48, 0x83, 0xfa, 0x20, //0x00000633 cmpq         $32, %rdx
	0x0f, 0x82, 0x57, 0x00, 0x00, 0x00, //0x00000637 jb           LBB0_62
	0x48, 0x89, 0xc6, //0x0000063d movq         %rax, %rsi
	0x4c, 0x29, 0xd6, //0x00000640 subq         %r10, %rsi
	0x48, 0x83, 0xc6, 0xdc, //0x00000643 addq         $-36, %rsi
	0x48, 0x89, 0xf7, //0x00000647 movq         %rsi, %rdi
	0x48, 0x83, 0xe7, 0xe0, //0x0000064a andq         $-32, %rdi
	0x4c, 0x01, 0xd7, //0x0000064e addq         %r10, %rdi
	0x49, 0x8d, 0x7c, 0x38, 0x24, //0x00000651 leaq         $36(%r8,%rdi), %rdi
	0x83, 0xe6, 0x1f, //0x00000656 andl         $31, %esi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000659 .p2align 4, 0x90
	//0x00000660 LBB0_59
	0xc5, 0x7e, 0x6f, 0x31, //0x00000660 vmovdqu      (%rcx), %ymm14
	0xc4, 0x42, 0x7d, 0x00, 0xfe, //0x00000664 vpshufb      %ymm14, %ymm0, %ymm15
	0xc4, 0x41, 0x0d, 0x74, 0xf7, //0x00000669 vpcmpeqb     %ymm15, %ymm14, %ymm14
	0xc4, 0xc1, 0x7d, 0xd7, 0xde, //0x0000066e vpmovmskb    %ymm14, %ebx
	0x83, 0xfb, 0xff, //0x00000673 cmpl         $-1, %ebx
	0x0f, 0x85, 0x74, 0x00, 0x00, 0x00, //0x00000676 jne          LBB0_69
	0x48, 0x83, 0xc1, 0x20, //0x0000067c addq         $32, %rcx
	0x48, 0x83, 0xc2, 0xe0, //0x00000680 addq         $-32, %rdx
	0x48, 0x83, 0xfa, 0x1f, //0x00000684 cmpq         $31, %rdx
	0x0f, 0x87, 0xd2, 0xff, 0xff, 0xff, //0x00000688 ja           LBB0_59
	0x48, 0x89, 0xf2, //0x0000068e movq         %rsi, %rdx
	0x48, 0x89, 0xf9, //0x00000691 movq         %rdi, %rcx
	//0x00000694 LBB0_62
	0x48, 0x85, 0xd2, //0x00000694 testq        %rdx, %rdx
	0x0f, 0x84, 0x3a, 0x00, 0x00, 0x00, //0x00000697 je           LBB0_68
	0x48, 0x8d, 0x34, 0x11, //0x0000069d leaq         (%rcx,%rdx), %rsi
	0x48, 0xff, 0xc1, //0x000006a1 incq         %rcx
	//0x000006a4 LBB0_64
	0x0f, 0xbe, 0x79, 0xff, //0x000006a4 movsbl       $-1(%rcx), %edi
	0x83, 0xff, 0x20, //0x000006a8 cmpl         $32, %edi
	0x0f, 0x87, 0x0d, 0x05, 0x00, 0x00, //0x000006ab ja           LBB0_125
	0x48, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000006b1 movabsq      $4294977024, %rbx
	0x48, 0x0f, 0xa3, 0xfb, //0x000006bb btq          %rdi, %rbx
	0x0f, 0x83, 0xf9, 0x04, 0x00, 0x00, //0x000006bf jae          LBB0_125
	0x48, 0xff, 0xca, //0x000006c5 decq         %rdx
	0x48, 0xff, 0xc1, //0x000006c8 incq         %rcx
	0x48, 0x85, 0xd2, //0x000006cb testq        %rdx, %rdx
	0x0f, 0x85, 0xd0, 0xff, 0xff, 0xff, //0x000006ce jne          LBB0_64
	0x48, 0x89, 0xf1, //0x000006d4 movq         %rsi, %rcx
	//0x000006d7 LBB0_68
	0x4c, 0x29, 0xc1, //0x000006d7 subq         %r8, %rcx
	0x48, 0x39, 0xc1, //0x000006da cmpq         %rax, %rcx
	0x0f, 0x82, 0x25, 0x00, 0x00, 0x00, //0x000006dd jb           LBB0_70
	0xe9, 0x09, 0x2c, 0x00, 0x00, //0x000006e3 jmp          LBB0_657
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000006e8 .p2align 4, 0x90
	//0x000006f0 LBB0_69
	0x4c, 0x29, 0xc1, //0x000006f0 subq         %r8, %rcx
	0xf7, 0xd3, //0x000006f3 notl         %ebx
	0x48, 0x63, 0xd3, //0x000006f5 movslq       %ebx, %rdx
	0x48, 0x0f, 0xbc, 0xd2, //0x000006f8 bsfq         %rdx, %rdx
	0x48, 0x01, 0xd1, //0x000006fc addq         %rdx, %rcx
	0x48, 0x39, 0xc1, //0x000006ff cmpq         %rax, %rcx
	0x0f, 0x83, 0xe9, 0x2b, 0x00, 0x00, //0x00000702 jae          LBB0_657
	//0x00000708 LBB0_70
	0x4c, 0x8d, 0x49, 0x01, //0x00000708 leaq         $1(%rcx), %r9
	0x4d, 0x89, 0x0e, //0x0000070c movq         %r9, (%r14)
	0x41, 0x8a, 0x04, 0x08, //0x0000070f movb         (%r8,%rcx), %al
	0x3c, 0x22, //0x00000713 cmpb         $34, %al
	0x0f, 0x85, 0xfc, 0x29, 0x00, 0x00, //0x00000715 jne          LBB0_629
	0x49, 0x8b, 0x33, //0x0000071b movq         (%r11), %rsi
	0x48, 0x89, 0xf0, //0x0000071e movq         %rsi, %rax
	0x4c, 0x29, 0xc8, //0x00000721 subq         %r9, %rax
	0x0f, 0x84, 0x08, 0x4e, 0x00, 0x00, //0x00000724 je           LBB0_1093
	0x49, 0x8b, 0x55, 0x08, //0x0000072a movq         $8(%r13), %rdx
	0x4c, 0x8b, 0x22, //0x0000072e movq         (%rdx), %r12
	0x48, 0x8b, 0x52, 0x08, //0x00000731 movq         $8(%rdx), %rdx
	0x4d, 0x01, 0xc1, //0x00000735 addq         %r8, %r9
	0x48, 0x83, 0xf8, 0x40, //0x00000738 cmpq         $64, %rax
	0x4c, 0x89, 0x04, 0x24, //0x0000073c movq         %r8, (%rsp)
	0x48, 0x89, 0xb4, 0x24, 0x98, 0x00, 0x00, 0x00, //0x00000740 movq         %rsi, $152(%rsp)
	0x0f, 0x82, 0x87, 0x04, 0x00, 0x00, //0x00000748 jb           LBB0_126
	0x4c, 0x89, 0x64, 0x24, 0x20, //0x0000074e movq         %r12, $32(%rsp)
	0x4d, 0x89, 0xee, //0x00000753 movq         %r13, %r14
	0x41, 0x89, 0xc2, //0x00000756 movl         %eax, %r10d
	0x41, 0x83, 0xe2, 0x3f, //0x00000759 andl         $63, %r10d
	0x48, 0x29, 0xce, //0x0000075d subq         %rcx, %rsi
	0x48, 0x83, 0xc6, 0xbf, //0x00000760 addq         $-65, %rsi
	0x48, 0x83, 0xe6, 0xc0, //0x00000764 andq         $-64, %rsi
	0x48, 0x01, 0xce, //0x00000768 addq         %rcx, %rsi
	0x49, 0x8d, 0x74, 0x30, 0x41, //0x0000076b leaq         $65(%r8,%rsi), %rsi
	0x48, 0x89, 0x74, 0x24, 0x30, //0x00000770 movq         %rsi, $48(%rsp)
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00000775 movq         $-1, %r15
	0x4d, 0x89, 0xc8, //0x0000077c movq         %r9, %r8
	0x31, 0xdb, //0x0000077f xorl         %ebx, %ebx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000781 .p2align 4, 0x90
	//0x00000790 LBB0_74
	0xc4, 0x41, 0x7e, 0x6f, 0x30, //0x00000790 vmovdqu      (%r8), %ymm14
	0xc4, 0x41, 0x7e, 0x6f, 0x78, 0x20, //0x00000795 vmovdqu      $32(%r8), %ymm15
	0xc5, 0x0d, 0x74, 0xe1, //0x0000079b vpcmpeqb     %ymm1, %ymm14, %ymm12
	0xc4, 0x41, 0x7d, 0xd7, 0xdc, //0x0000079f vpmovmskb    %ymm12, %r11d
	0xc5, 0x05, 0x74, 0xe1, //0x000007a4 vpcmpeqb     %ymm1, %ymm15, %ymm12
	0xc4, 0xc1, 0x7d, 0xd7, 0xfc, //0x000007a8 vpmovmskb    %ymm12, %edi
	0xc5, 0x0d, 0x74, 0xe2, //0x000007ad vpcmpeqb     %ymm2, %ymm14, %ymm12
	0xc4, 0x41, 0x7d, 0xd7, 0xec, //0x000007b1 vpmovmskb    %ymm12, %r13d
	0xc5, 0x05, 0x74, 0xe2, //0x000007b6 vpcmpeqb     %ymm2, %ymm15, %ymm12
	0xc4, 0x41, 0x7d, 0xd7, 0xe4, //0x000007ba vpmovmskb    %ymm12, %r12d
	0x48, 0xc1, 0xe7, 0x20, //0x000007bf shlq         $32, %rdi
	0x49, 0xc1, 0xe4, 0x20, //0x000007c3 shlq         $32, %r12
	0x4d, 0x09, 0xe5, //0x000007c7 orq          %r12, %r13
	0x49, 0x83, 0xff, 0xff, //0x000007ca cmpq         $-1, %r15
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x000007ce jne          LBB0_76
	0x4d, 0x85, 0xed, //0x000007d4 testq        %r13, %r13
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x000007d7 jne          LBB0_84
	//0x000007dd LBB0_76
	0x4c, 0x09, 0xdf, //0x000007dd orq          %r11, %rdi
	0x4c, 0x89, 0xee, //0x000007e0 movq         %r13, %rsi
	0x48, 0x09, 0xde, //0x000007e3 orq          %rbx, %rsi
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x000007e6 jne          LBB0_85
	//0x000007ec LBB0_77
	0x48, 0x85, 0xff, //0x000007ec testq        %rdi, %rdi
	0x0f, 0x85, 0x8b, 0x00, 0x00, 0x00, //0x000007ef jne          LBB0_86
	//0x000007f5 LBB0_78
	0x48, 0x83, 0xc0, 0xc0, //0x000007f5 addq         $-64, %rax
	0x49, 0x83, 0xc0, 0x40, //0x000007f9 addq         $64, %r8
	0x48, 0x83, 0xf8, 0x3f, //0x000007fd cmpq         $63, %rax
	0x0f, 0x87, 0x89, 0xff, 0xff, 0xff, //0x00000801 ja           LBB0_74
	0xe9, 0x4a, 0x03, 0x00, 0x00, //0x00000807 jmp          LBB0_79
	//0x0000080c LBB0_84
	0x4d, 0x89, 0xc4, //0x0000080c movq         %r8, %r12
	0x4c, 0x2b, 0x24, 0x24, //0x0000080f subq         (%rsp), %r12
	0x4d, 0x0f, 0xbc, 0xfd, //0x00000813 bsfq         %r13, %r15
	0x4d, 0x01, 0xe7, //0x00000817 addq         %r12, %r15
	0x4c, 0x09, 0xdf, //0x0000081a orq          %r11, %rdi
	0x4c, 0x89, 0xee, //0x0000081d movq         %r13, %rsi
	0x48, 0x09, 0xde, //0x00000820 orq          %rbx, %rsi
	0x0f, 0x84, 0xc3, 0xff, 0xff, 0xff, //0x00000823 je           LBB0_77
	//0x00000829 LBB0_85
	0x49, 0x89, 0xdc, //0x00000829 movq         %rbx, %r12
	0x49, 0xf7, 0xd4, //0x0000082c notq         %r12
	0x4d, 0x21, 0xec, //0x0000082f andq         %r13, %r12
	0x4f, 0x8d, 0x1c, 0x24, //0x00000832 leaq         (%r12,%r12), %r11
	0x49, 0x09, 0xdb, //0x00000836 orq          %rbx, %r11
	0x4c, 0x89, 0x5c, 0x24, 0x38, //0x00000839 movq         %r11, $56(%rsp)
	0x49, 0xf7, 0xd3, //0x0000083e notq         %r11
	0x4d, 0x21, 0xeb, //0x00000841 andq         %r13, %r11
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000844 movabsq      $-6148914691236517206, %rbx
	0x49, 0x21, 0xdb, //0x0000084e andq         %rbx, %r11
	0x31, 0xdb, //0x00000851 xorl         %ebx, %ebx
	0x4d, 0x01, 0xe3, //0x00000853 addq         %r12, %r11
	0x0f, 0x92, 0xc3, //0x00000856 setb         %bl
	0x4d, 0x01, 0xdb, //0x00000859 addq         %r11, %r11
	0x48, 0xbe, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000085c movabsq      $6148914691236517205, %rsi
	0x49, 0x31, 0xf3, //0x00000866 xorq         %rsi, %r11
	0x4c, 0x23, 0x5c, 0x24, 0x38, //0x00000869 andq         $56(%rsp), %r11
	0x49, 0xf7, 0xd3, //0x0000086e notq         %r11
	0x4c, 0x21, 0xdf, //0x00000871 andq         %r11, %rdi
	0x48, 0x85, 0xff, //0x00000874 testq        %rdi, %rdi
	0x0f, 0x84, 0x78, 0xff, 0xff, 0xff, //0x00000877 je           LBB0_78
	0x90, 0x90, 0x90, //0x0000087d .p2align 4, 0x90
	//0x00000880 LBB0_86
	0x48, 0x0f, 0xbc, 0xc7, //0x00000880 bsfq         %rdi, %rax
	0x4c, 0x2b, 0x04, 0x24, //0x00000884 subq         (%rsp), %r8
	0x4d, 0x8d, 0x54, 0x00, 0x01, //0x00000888 leaq         $1(%r8,%rax), %r10
	0x4d, 0x89, 0xf5, //0x0000088d movq         %r14, %r13
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00000890 movq         $24(%rsp), %r11
	0x4c, 0x8b, 0x64, 0x24, 0x20, //0x00000895 movq         $32(%rsp), %r12
	//0x0000089a LBB0_87
	0x4d, 0x85, 0xd2, //0x0000089a testq        %r10, %r10
	0x0f, 0x88, 0x97, 0x4c, 0x00, 0x00, //0x0000089d js           LBB0_1094
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x000008a3 movq         $8(%rsp), %r14
	0x4d, 0x89, 0x16, //0x000008a8 movq         %r10, (%r14)
	0x49, 0x83, 0xff, 0xff, //0x000008ab cmpq         $-1, %r15
	0x0f, 0x84, 0x09, 0x00, 0x00, 0x00, //0x000008af je           LBB0_90
	0x4d, 0x39, 0xd7, //0x000008b5 cmpq         %r10, %r15
	0x0f, 0x8e, 0x3b, 0x03, 0x00, 0x00, //0x000008b8 jle          LBB0_127
	//0x000008be LBB0_90
	0x4c, 0x89, 0xd0, //0x000008be movq         %r10, %rax
	0x48, 0x29, 0xc8, //0x000008c1 subq         %rcx, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x000008c4 addq         $-2, %rax
	0x48, 0x89, 0xc1, //0x000008c8 movq         %rax, %rcx
	0x48, 0x09, 0xd1, //0x000008cb orq          %rdx, %rcx
	0x4c, 0x8b, 0x04, 0x24, //0x000008ce movq         (%rsp), %r8
	0x0f, 0x84, 0xd8, 0x00, 0x00, 0x00, //0x000008d2 je           LBB0_101
	0x48, 0x39, 0xd0, //0x000008d8 cmpq         %rdx, %rax
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x000008db movq         $16(%rsp), %r15
	0x0f, 0x85, 0xea, 0x00, 0x00, 0x00, //0x000008e0 jne          LBB0_102
	0x48, 0x83, 0xfa, 0x20, //0x000008e6 cmpq         $32, %rdx
	0x0f, 0x82, 0x55, 0x00, 0x00, 0x00, //0x000008ea jb           LBB0_97
	0x48, 0x8d, 0x42, 0xe0, //0x000008f0 leaq         $-32(%rdx), %rax
	0x48, 0x89, 0xc6, //0x000008f4 movq         %rax, %rsi
	0x48, 0x83, 0xe6, 0xe0, //0x000008f7 andq         $-32, %rsi
	0x49, 0x8d, 0x4c, 0x31, 0x20, //0x000008fb leaq         $32(%r9,%rsi), %rcx
	0x49, 0x8d, 0x74, 0x34, 0x20, //0x00000900 leaq         $32(%r12,%rsi), %rsi
	0x83, 0xe0, 0x1f, //0x00000905 andl         $31, %eax
	0x31, 0xff, //0x00000908 xorl         %edi, %edi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000090a .p2align 4, 0x90
	//0x00000910 LBB0_94
	0xc4, 0x41, 0x7e, 0x6f, 0x24, 0x39, //0x00000910 vmovdqu      (%r9,%rdi), %ymm12
	0xc4, 0x41, 0x1d, 0x74, 0x24, 0x3c, //0x00000916 vpcmpeqb     (%r12,%rdi), %ymm12, %ymm12
	0xc4, 0xc1, 0x7d, 0xd7, 0xdc, //0x0000091c vpmovmskb    %ymm12, %ebx
	0x83, 0xfb, 0xff, //0x00000921 cmpl         $-1, %ebx
	0x0f, 0x85, 0x34, 0x01, 0x00, 0x00, //0x00000924 jne          LBB0_112
	0x48, 0x83, 0xc2, 0xe0, //0x0000092a addq         $-32, %rdx
	0x48, 0x83, 0xc7, 0x20, //0x0000092e addq         $32, %rdi
	0x48, 0x83, 0xfa, 0x1f, //0x00000932 cmpq         $31, %rdx
	0x0f, 0x87, 0xd4, 0xff, 0xff, 0xff, //0x00000936 ja           LBB0_94
	0x48, 0x89, 0xc2, //0x0000093c movq         %rax, %rdx
	0x49, 0x89, 0xf4, //0x0000093f movq         %rsi, %r12
	0x49, 0x89, 0xc9, //0x00000942 movq         %rcx, %r9
	//0x00000945 LBB0_97
	0x44, 0x89, 0xce, //0x00000945 movl         %r9d, %esi
	0x81, 0xe6, 0xff, 0x0f, 0x00, 0x00, //0x00000948 andl         $4095, %esi
	0x44, 0x89, 0xe1, //0x0000094e movl         %r12d, %ecx
	0x81, 0xe1, 0xff, 0x0f, 0x00, 0x00, //0x00000951 andl         $4095, %ecx
	0x48, 0x81, 0xf9, 0xe0, 0x0f, 0x00, 0x00, //0x00000957 cmpq         $4064, %rcx
	0x0f, 0x87, 0x80, 0x00, 0x00, 0x00, //0x0000095e ja           LBB0_103
	0x81, 0xfe, 0xe1, 0x0f, 0x00, 0x00, //0x00000964 cmpl         $4065, %esi
	0x0f, 0x83, 0x74, 0x00, 0x00, 0x00, //0x0000096a jae          LBB0_103
	0xc4, 0x41, 0x7e, 0x6f, 0x21, //0x00000970 vmovdqu      (%r9), %ymm12
	0xc4, 0x41, 0x1d, 0x74, 0x24, 0x24, //0x00000975 vpcmpeqb     (%r12), %ymm12, %ymm12
	0xc4, 0xc1, 0x7d, 0xd7, 0xc4, //0x0000097b vpmovmskb    %ymm12, %eax
	0x83, 0xf8, 0xff, //0x00000980 cmpl         $-1, %eax
	0x0f, 0x84, 0x38, 0x01, 0x00, 0x00, //0x00000983 je           LBB0_117
	0xf7, 0xd0, //0x00000989 notl         %eax
	0x48, 0x0f, 0xbc, 0xc0, //0x0000098b bsfq         %rax, %rax
	0x48, 0x39, 0xd0, //0x0000098f cmpq         %rdx, %rax
	0x0f, 0x93, 0xc2, //0x00000992 setae        %dl
	0x44, 0x0f, 0xb6, 0xca, //0x00000995 movzbl       %dl, %r9d
	0x49, 0x8b, 0x13, //0x00000999 movq         (%r11), %rdx
	0x49, 0x39, 0xd2, //0x0000099c cmpq         %rdx, %r10
	0x0f, 0x82, 0x8b, 0x01, 0x00, 0x00, //0x0000099f jb           LBB0_121
	0xe9, 0xf6, 0x05, 0x00, 0x00, //0x000009a5 jmp          LBB0_171
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000009aa .p2align 4, 0x90
	//0x000009b0 LBB0_101
	0x41, 0xb9, 0x01, 0x00, 0x00, 0x00, //0x000009b0 movl         $1, %r9d
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x000009b6 movq         $16(%rsp), %r15
	0x49, 0x8b, 0x13, //0x000009bb movq         (%r11), %rdx
	0x49, 0x39, 0xd2, //0x000009be cmpq         %rdx, %r10
	0x0f, 0x82, 0x69, 0x01, 0x00, 0x00, //0x000009c1 jb           LBB0_121
	0xe9, 0xd4, 0x05, 0x00, 0x00, //0x000009c7 jmp          LBB0_171
	0x90, 0x90, 0x90, 0x90, //0x000009cc .p2align 4, 0x90
	//0x000009d0 LBB0_102
	0x45, 0x31, 0xc9, //0x000009d0 xorl         %r9d, %r9d
	0x49, 0x8b, 0x13, //0x000009d3 movq         (%r11), %rdx
	0x49, 0x39, 0xd2, //0x000009d6 cmpq         %rdx, %r10
	0x0f, 0x82, 0x51, 0x01, 0x00, 0x00, //0x000009d9 jb           LBB0_121
	0xe9, 0xbc, 0x05, 0x00, 0x00, //0x000009df jmp          LBB0_171
	//0x000009e4 LBB0_103
	0x48, 0x83, 0xfa, 0x10, //0x000009e4 cmpq         $16, %rdx
	0x0f, 0x82, 0x87, 0x00, 0x00, 0x00, //0x000009e8 jb           LBB0_113
	0x48, 0x8d, 0x42, 0xf0, //0x000009ee leaq         $-16(%rdx), %rax
	0x48, 0x89, 0xc1, //0x000009f2 movq         %rax, %rcx
	0x48, 0x83, 0xe1, 0xf0, //0x000009f5 andq         $-16, %rcx
	0x49, 0x8d, 0x7c, 0x09, 0x10, //0x000009f9 leaq         $16(%r9,%rcx), %rdi
	0x4d, 0x8d, 0x44, 0x0c, 0x10, //0x000009fe leaq         $16(%r12,%rcx), %r8
	0x83, 0xe0, 0x0f, //0x00000a03 andl         $15, %eax
	0x31, 0xc9, //0x00000a06 xorl         %ecx, %ecx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000a08 .p2align 4, 0x90
	//0x00000a10 LBB0_105
	0xc4, 0xc1, 0x7a, 0x6f, 0x34, 0x09, //0x00000a10 vmovdqu      (%r9,%rcx), %xmm6
	0xc4, 0xc1, 0x49, 0x74, 0x34, 0x0c, //0x00000a16 vpcmpeqb     (%r12,%rcx), %xmm6, %xmm6
	0xc5, 0xf9, 0xd7, 0xf6, //0x00000a1c vpmovmskb    %xmm6, %esi
	0x66, 0x83, 0xfe, 0xff, //0x00000a20 cmpw         $-1, %si
	0x0f, 0x85, 0xe5, 0x00, 0x00, 0x00, //0x00000a24 jne          LBB0_118
	0x48, 0x83, 0xc2, 0xf0, //0x00000a2a addq         $-16, %rdx
	0x48, 0x83, 0xc1, 0x10, //0x00000a2e addq         $16, %rcx
	0x48, 0x83, 0xfa, 0x0f, //0x00000a32 cmpq         $15, %rdx
	0x0f, 0x87, 0xd4, 0xff, 0xff, 0xff, //0x00000a36 ja           LBB0_105
	0x89, 0xfe, //0x00000a3c movl         %edi, %esi
	0x81, 0xe6, 0xff, 0x0f, 0x00, 0x00, //0x00000a3e andl         $4095, %esi
	0x44, 0x89, 0xc1, //0x00000a44 movl         %r8d, %ecx
	0x81, 0xe1, 0xff, 0x0f, 0x00, 0x00, //0x00000a47 andl         $4095, %ecx
	0x81, 0xf9, 0xf0, 0x0f, 0x00, 0x00, //0x00000a4d cmpl         $4080, %ecx
	0x0f, 0x86, 0x31, 0x00, 0x00, 0x00, //0x00000a53 jbe          LBB0_114
	0xe9, 0x7a, 0x00, 0x00, 0x00, //0x00000a59 jmp          LBB0_108
	//0x00000a5e LBB0_112
	0x31, 0xd2, //0x00000a5e xorl         %edx, %edx
	0x44, 0x0f, 0xb6, 0xca, //0x00000a60 movzbl       %dl, %r9d
	0x49, 0x8b, 0x13, //0x00000a64 movq         (%r11), %rdx
	0x49, 0x39, 0xd2, //0x00000a67 cmpq         %rdx, %r10
	0x0f, 0x82, 0xc0, 0x00, 0x00, 0x00, //0x00000a6a jb           LBB0_121
	0xe9, 0x2b, 0x05, 0x00, 0x00, //0x00000a70 jmp          LBB0_171
	//0x00000a75 LBB0_113
	0x48, 0x89, 0xd0, //0x00000a75 movq         %rdx, %rax
	0x4d, 0x89, 0xe0, //0x00000a78 movq         %r12, %r8
	0x4c, 0x89, 0xcf, //0x00000a7b movq         %r9, %rdi
	0x81, 0xf9, 0xf0, 0x0f, 0x00, 0x00, //0x00000a7e cmpl         $4080, %ecx
	0x0f, 0x87, 0x4e, 0x00, 0x00, 0x00, //0x00000a84 ja           LBB0_108
	//0x00000a8a LBB0_114
	0x81, 0xfe, 0xf1, 0x0f, 0x00, 0x00, //0x00000a8a cmpl         $4081, %esi
	0x0f, 0x83, 0x42, 0x00, 0x00, 0x00, //0x00000a90 jae          LBB0_108
	0xc5, 0xfa, 0x6f, 0x37, //0x00000a96 vmovdqu      (%rdi), %xmm6
	0xc4, 0xc1, 0x49, 0x74, 0x30, //0x00000a9a vpcmpeqb     (%r8), %xmm6, %xmm6
	0xc5, 0xf9, 0xd7, 0xce, //0x00000a9f vpmovmskb    %xmm6, %ecx
	0x66, 0x83, 0xf9, 0xff, //0x00000aa3 cmpw         $-1, %cx
	0x0f, 0x84, 0x69, 0x00, 0x00, 0x00, //0x00000aa7 je           LBB0_119
	0xf7, 0xd1, //0x00000aad notl         %ecx
	0x0f, 0xb7, 0xc9, //0x00000aaf movzwl       %cx, %ecx
	0x48, 0x0f, 0xbc, 0xc9, //0x00000ab2 bsfq         %rcx, %rcx
	0x48, 0x39, 0xc1, //0x00000ab6 cmpq         %rax, %rcx
	0x0f, 0x93, 0xc2, //0x00000ab9 setae        %dl
	0xe9, 0x57, 0x00, 0x00, 0x00, //0x00000abc jmp          LBB0_120
	//0x00000ac1 LBB0_117
	0xb2, 0x01, //0x00000ac1 movb         $1, %dl
	0x44, 0x0f, 0xb6, 0xca, //0x00000ac3 movzbl       %dl, %r9d
	0x49, 0x8b, 0x13, //0x00000ac7 movq         (%r11), %rdx
	0x49, 0x39, 0xd2, //0x00000aca cmpq         %rdx, %r10
	0x0f, 0x82, 0x5d, 0x00, 0x00, 0x00, //0x00000acd jb           LBB0_121
	0xe9, 0xc8, 0x04, 0x00, 0x00, //0x00000ad3 jmp          LBB0_171
	//0x00000ad8 LBB0_108
	0xb2, 0x01, //0x00000ad8 movb         $1, %dl
	0x48, 0x85, 0xc0, //0x00000ada testq        %rax, %rax
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x00000add je           LBB0_120
	0x31, 0xc9, //0x00000ae3 xorl         %ecx, %ecx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000ae5 .p2align 4, 0x90
	//0x00000af0 LBB0_110
	0x0f, 0xb6, 0x1c, 0x0f, //0x00000af0 movzbl       (%rdi,%rcx), %ebx
	0x41, 0x3a, 0x1c, 0x08, //0x00000af4 cmpb         (%r8,%rcx), %bl
	0x0f, 0x85, 0x11, 0x00, 0x00, 0x00, //0x00000af8 jne          LBB0_118
	0x48, 0xff, 0xc1, //0x00000afe incq         %rcx
	0x48, 0x39, 0xc8, //0x00000b01 cmpq         %rcx, %rax
	0x0f, 0x85, 0xe6, 0xff, 0xff, 0xff, //0x00000b04 jne          LBB0_110
	0xe9, 0x09, 0x00, 0x00, 0x00, //0x00000b0a jmp          LBB0_120
	//0x00000b0f LBB0_118
	0x31, 0xd2, //0x00000b0f xorl         %edx, %edx
	0xe9, 0x02, 0x00, 0x00, 0x00, //0x00000b11 jmp          LBB0_120
	//0x00000b16 LBB0_119
	0xb2, 0x01, //0x00000b16 movb         $1, %dl
	//0x00000b18 LBB0_120
	0x4c, 0x8b, 0x04, 0x24, //0x00000b18 movq         (%rsp), %r8
	0x44, 0x0f, 0xb6, 0xca, //0x00000b1c movzbl       %dl, %r9d
	0x49, 0x8b, 0x13, //0x00000b20 movq         (%r11), %rdx
	0x49, 0x39, 0xd2, //0x00000b23 cmpq         %rdx, %r10
	0x0f, 0x83, 0x74, 0x04, 0x00, 0x00, //0x00000b26 jae          LBB0_171
	0x90, 0x90, 0x90, 0x90, //0x00000b2c .p2align 4, 0x90
	//0x00000b30 LBB0_121
	0x43, 0x8a, 0x04, 0x10, //0x00000b30 movb         (%r8,%r10), %al
	0x3c, 0x0d, //0x00000b34 cmpb         $13, %al
	0x0f, 0x84, 0x64, 0x04, 0x00, 0x00, //0x00000b36 je           LBB0_171
	0x3c, 0x20, //0x00000b3c cmpb         $32, %al
	0x0f, 0x84, 0x5c, 0x04, 0x00, 0x00, //0x00000b3e je           LBB0_171
	0x04, 0xf7, //0x00000b44 addb         $-9, %al
	0x3c, 0x01, //0x00000b46 cmpb         $1, %al
	0x0f, 0x86, 0x52, 0x04, 0x00, 0x00, //0x00000b48 jbe          LBB0_171
	0x4c, 0x89, 0xd0, //0x00000b4e movq         %r10, %rax
	0xe9, 0xc2, 0x05, 0x00, 0x00, //0x00000b51 jmp          LBB0_197
	//0x00000b56 LBB0_79
	0x4c, 0x89, 0x7c, 0x24, 0x38, //0x00000b56 movq         %r15, $56(%rsp)
	0x4c, 0x89, 0xd0, //0x00000b5b movq         %r10, %rax
	0x4d, 0x89, 0xf5, //0x00000b5e movq         %r14, %r13
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00000b61 movq         $24(%rsp), %r11
	0x4c, 0x8b, 0x64, 0x24, 0x20, //0x00000b66 movq         $32(%rsp), %r12
	0x48, 0x8b, 0x7c, 0x24, 0x30, //0x00000b6b movq         $48(%rsp), %rdi
	0x48, 0x83, 0xf8, 0x20, //0x00000b70 cmpq         $32, %rax
	0x0f, 0x82, 0xe1, 0x12, 0x00, 0x00, //0x00000b74 jb           LBB0_371
	//0x00000b7a LBB0_80
	0xc5, 0x7e, 0x6f, 0x27, //0x00000b7a vmovdqu      (%rdi), %ymm12
	0xc5, 0x1d, 0x74, 0xf1, //0x00000b7e vpcmpeqb     %ymm1, %ymm12, %ymm14
	0xc4, 0x41, 0x7d, 0xd7, 0xc6, //0x00000b82 vpmovmskb    %ymm14, %r8d
	0xc5, 0x1d, 0x74, 0xe2, //0x00000b87 vpcmpeqb     %ymm2, %ymm12, %ymm12
	0xc4, 0xc1, 0x7d, 0xd7, 0xf4, //0x00000b8b vpmovmskb    %ymm12, %esi
	0x85, 0xf6, //0x00000b90 testl        %esi, %esi
	0x0f, 0x85, 0x46, 0x12, 0x00, 0x00, //0x00000b92 jne          LBB0_366
	0x48, 0x85, 0xdb, //0x00000b98 testq        %rbx, %rbx
	0x0f, 0x85, 0x64, 0x12, 0x00, 0x00, //0x00000b9b jne          LBB0_368
	0x31, 0xdb, //0x00000ba1 xorl         %ebx, %ebx
	0x4d, 0x85, 0xc0, //0x00000ba3 testq        %r8, %r8
	0x0f, 0x84, 0xa7, 0x12, 0x00, 0x00, //0x00000ba6 je           LBB0_370
	//0x00000bac LBB0_83
	0x49, 0x0f, 0xbc, 0xc0, //0x00000bac bsfq         %r8, %rax
	0x48, 0x2b, 0x3c, 0x24, //0x00000bb0 subq         (%rsp), %rdi
	0x4c, 0x8d, 0x54, 0x07, 0x01, //0x00000bb4 leaq         $1(%rdi,%rax), %r10
	0xe9, 0xdc, 0xfc, 0xff, 0xff, //0x00000bb9 jmp          LBB0_87
	//0x00000bbe LBB0_125
	0x4c, 0x89, 0xc2, //0x00000bbe movq         %r8, %rdx
	0x48, 0xf7, 0xd2, //0x00000bc1 notq         %rdx
	0x48, 0x01, 0xd1, //0x00000bc4 addq         %rdx, %rcx
	0x48, 0x39, 0xc1, //0x00000bc7 cmpq         %rax, %rcx
	0x0f, 0x82, 0x38, 0xfb, 0xff, 0xff, //0x00000bca jb           LBB0_70
	0xe9, 0x1c, 0x27, 0x00, 0x00, //0x00000bd0 jmp          LBB0_657
	//0x00000bd5 LBB0_126
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00000bd5 movq         $-1, %r15
	0x48, 0xc7, 0x44, 0x24, 0x38, 0xff, 0xff, 0xff, 0xff, //0x00000bdc movq         $-1, $56(%rsp)
	0x31, 0xdb, //0x00000be5 xorl         %ebx, %ebx
	0x4c, 0x89, 0xcf, //0x00000be7 movq         %r9, %rdi
	0x48, 0x83, 0xf8, 0x20, //0x00000bea cmpq         $32, %rax
	0x0f, 0x83, 0x86, 0xff, 0xff, 0xff, //0x00000bee jae          LBB0_80
	0xe9, 0x62, 0x12, 0x00, 0x00, //0x00000bf4 jmp          LBB0_371
	//0x00000bf9 LBB0_127
	0x48, 0xc7, 0x44, 0x24, 0x40, 0x00, 0x00, 0x00, 0x00, //0x00000bf9 movq         $0, $64(%rsp)
	0x48, 0x8b, 0x04, 0x24, //0x00000c02 movq         (%rsp), %rax
	0x4e, 0x8d, 0x44, 0x10, 0xff, //0x00000c06 leaq         $-1(%rax,%r10), %r8
	0x4d, 0x8d, 0x34, 0x14, //0x00000c0b leaq         (%r12,%rdx), %r14
	0x48, 0x85, 0xd2, //0x00000c0f testq        %rdx, %rdx
	0x0f, 0x8e, 0x5c, 0x03, 0x00, 0x00, //0x00000c12 jle          LBB0_169
	0x4d, 0x39, 0xc8, //0x00000c18 cmpq         %r9, %r8
	0x0f, 0x86, 0x53, 0x03, 0x00, 0x00, //0x00000c1b jbe          LBB0_169
	0x4c, 0x8d, 0x7c, 0x24, 0x40, //0x00000c21 leaq         $64(%rsp), %r15
	//0x00000c26 LBB0_130
	0x41, 0x8a, 0x01, //0x00000c26 movb         (%r9), %al
	0x3c, 0x5c, //0x00000c29 cmpb         $92, %al
	0x0f, 0x85, 0x4f, 0x00, 0x00, 0x00, //0x00000c2b jne          LBB0_135
	0x4c, 0x89, 0xc0, //0x00000c31 movq         %r8, %rax
	0x4c, 0x29, 0xc8, //0x00000c34 subq         %r9, %rax
	0x48, 0x85, 0xc0, //0x00000c37 testq        %rax, %rax
	0x0f, 0x8e, 0xa5, 0x4a, 0x00, 0x00, //0x00000c3a jle          LBB0_1114
	0x41, 0x0f, 0xb6, 0x51, 0x01, //0x00000c40 movzbl       $1(%r9), %edx
	0x48, 0x8d, 0x0d, 0xd4, 0x54, 0x00, 0x00, //0x00000c45 leaq         $21716(%rip), %rcx  /* __UnquoteTab+0(%rip) */
	0x8a, 0x1c, 0x0a, //0x00000c4c movb         (%rdx,%rcx), %bl
	0x80, 0xfb, 0xff, //0x00000c4f cmpb         $-1, %bl
	0x0f, 0x84, 0x3d, 0x00, 0x00, 0x00, //0x00000c52 je           LBB0_137
	0x84, 0xdb, //0x00000c58 testb        %bl, %bl
	0x0f, 0x84, 0x73, 0x4a, 0x00, 0x00, //0x00000c5a je           LBB0_1112
	0x88, 0x5c, 0x24, 0x40, //0x00000c60 movb         %bl, $64(%rsp)
	0x49, 0x83, 0xc1, 0x02, //0x00000c64 addq         $2, %r9
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00000c68 movl         $1, %eax
	0x48, 0x8d, 0x44, 0x04, 0x40, //0x00000c6d leaq         $64(%rsp,%rax), %rax
	0x4d, 0x39, 0xf4, //0x00000c72 cmpq         %r14, %r12
	0x0f, 0x82, 0x28, 0x01, 0x00, 0x00, //0x00000c75 jb           LBB0_147
	0xe9, 0xd6, 0x02, 0x00, 0x00, //0x00000c7b jmp          LBB0_165
	//0x00000c80 LBB0_135
	0x41, 0x3a, 0x04, 0x24, //0x00000c80 cmpb         (%r12), %al
	0x0f, 0x85, 0x3b, 0x11, 0x00, 0x00, //0x00000c84 jne          LBB0_364
	0x49, 0xff, 0xc1, //0x00000c8a incq         %r9
	0x49, 0xff, 0xc4, //0x00000c8d incq         %r12
	0xe9, 0xcd, 0x02, 0x00, 0x00, //0x00000c90 jmp          LBB0_167
	//0x00000c95 LBB0_137
	0x48, 0x83, 0xf8, 0x03, //0x00000c95 cmpq         $3, %rax
	0x0f, 0x8e, 0x43, 0x4a, 0x00, 0x00, //0x00000c99 jle          LBB0_1113
	0x41, 0x8b, 0x51, 0x02, //0x00000c9f movl         $2(%r9), %edx
	0x89, 0xd6, //0x00000ca3 movl         %edx, %esi
	0xf7, 0xd6, //0x00000ca5 notl         %esi
	0x8d, 0xba, 0xd0, 0xcf, 0xcf, 0xcf, //0x00000ca7 leal         $-808464432(%rdx), %edi
	0x81, 0xe6, 0x80, 0x80, 0x80, 0x80, //0x00000cad andl         $-2139062144, %esi
	0x85, 0xfe, //0x00000cb3 testl        %edi, %esi
	0x0f, 0x85, 0xef, 0x49, 0x00, 0x00, //0x00000cb5 jne          LBB0_1110
	0x8d, 0xba, 0x19, 0x19, 0x19, 0x19, //0x00000cbb leal         $421075225(%rdx), %edi
	0x09, 0xd7, //0x00000cc1 orl          %edx, %edi
	0xf7, 0xc7, 0x80, 0x80, 0x80, 0x80, //0x00000cc3 testl        $-2139062144, %edi
	0x0f, 0x85, 0xdb, 0x49, 0x00, 0x00, //0x00000cc9 jne          LBB0_1110
	0x89, 0xd7, //0x00000ccf movl         %edx, %edi
	0x81, 0xe7, 0x7f, 0x7f, 0x7f, 0x7f, //0x00000cd1 andl         $2139062143, %edi
	0xbb, 0xc0, 0xc0, 0xc0, 0xc0, //0x00000cd7 movl         $-1061109568, %ebx
	0x29, 0xfb, //0x00000cdc subl         %edi, %ebx
	0x8d, 0x8f, 0x46, 0x46, 0x46, 0x46, //0x00000cde leal         $1179010630(%rdi), %ecx
	0x21, 0xf3, //0x00000ce4 andl         %esi, %ebx
	0x85, 0xcb, //0x00000ce6 testl        %ecx, %ebx
	0x0f, 0x85, 0xbc, 0x49, 0x00, 0x00, //0x00000ce8 jne          LBB0_1110
	0xb9, 0xe0, 0xe0, 0xe0, 0xe0, //0x00000cee movl         $-522133280, %ecx
	0x29, 0xf9, //0x00000cf3 subl         %edi, %ecx
	0x81, 0xc7, 0x39, 0x39, 0x39, 0x39, //0x00000cf5 addl         $960051513, %edi
	0x21, 0xce, //0x00000cfb andl         %ecx, %esi
	0x85, 0xfe, //0x00000cfd testl        %edi, %esi
	0x0f, 0x85, 0xa5, 0x49, 0x00, 0x00, //0x00000cff jne          LBB0_1110
	0x0f, 0xca, //0x00000d05 bswapl       %edx
	0x89, 0xd1, //0x00000d07 movl         %edx, %ecx
	0xc1, 0xe9, 0x04, //0x00000d09 shrl         $4, %ecx
	0xf7, 0xd1, //0x00000d0c notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x00000d0e andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x00000d14 leal         (%rcx,%rcx,8), %ecx
	0x81, 0xe2, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000d17 andl         $252645135, %edx
	0x01, 0xca, //0x00000d1d addl         %ecx, %edx
	0x89, 0xd3, //0x00000d1f movl         %edx, %ebx
	0xc1, 0xeb, 0x04, //0x00000d21 shrl         $4, %ebx
	0x09, 0xd3, //0x00000d24 orl          %edx, %ebx
	0x89, 0xde, //0x00000d26 movl         %ebx, %esi
	0xc1, 0xee, 0x08, //0x00000d28 shrl         $8, %esi
	0x81, 0xe6, 0x00, 0xff, 0x00, 0x00, //0x00000d2b andl         $65280, %esi
	0x0f, 0xb6, 0xd3, //0x00000d31 movzbl       %bl, %edx
	0x09, 0xf2, //0x00000d34 orl          %esi, %edx
	0x4d, 0x8d, 0x59, 0x06, //0x00000d36 leaq         $6(%r9), %r11
	0x83, 0xfa, 0x7f, //0x00000d3a cmpl         $127, %edx
	0x0f, 0x86, 0xb1, 0x00, 0x00, 0x00, //0x00000d3d jbe          LBB0_153
	0x81, 0xfa, 0xff, 0x07, 0x00, 0x00, //0x00000d43 cmpl         $2047, %edx
	0x0f, 0x86, 0xb3, 0x00, 0x00, 0x00, //0x00000d49 jbe          LBB0_154
	0x89, 0xd9, //0x00000d4f movl         %ebx, %ecx
	0x81, 0xe1, 0x00, 0x00, 0xf8, 0x00, //0x00000d51 andl         $16252928, %ecx
	0x81, 0xf9, 0x00, 0x00, 0xd8, 0x00, //0x00000d57 cmpl         $14155776, %ecx
	0x0f, 0x84, 0xbf, 0x00, 0x00, 0x00, //0x00000d5d je           LBB0_155
	0xc1, 0xee, 0x0c, //0x00000d63 shrl         $12, %esi
	0x40, 0x80, 0xce, 0xe0, //0x00000d66 orb          $-32, %sil
	0x40, 0x88, 0x74, 0x24, 0x40, //0x00000d6a movb         %sil, $64(%rsp)
	0xc1, 0xea, 0x06, //0x00000d6f shrl         $6, %edx
	0x80, 0xe2, 0x3f, //0x00000d72 andb         $63, %dl
	0x80, 0xca, 0x80, //0x00000d75 orb          $-128, %dl
	0x88, 0x54, 0x24, 0x41, //0x00000d78 movb         %dl, $65(%rsp)
	0x80, 0xe3, 0x3f, //0x00000d7c andb         $63, %bl
	0x80, 0xcb, 0x80, //0x00000d7f orb          $-128, %bl
	0x88, 0x5c, 0x24, 0x42, //0x00000d82 movb         %bl, $66(%rsp)
	0xb8, 0x03, 0x00, 0x00, 0x00, //0x00000d86 movl         $3, %eax
	0x89, 0xf3, //0x00000d8b movl         %esi, %ebx
	//0x00000d8d LBB0_146
	0x4d, 0x89, 0xd9, //0x00000d8d movq         %r11, %r9
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00000d90 movq         $24(%rsp), %r11
	0x48, 0x8d, 0x44, 0x04, 0x40, //0x00000d95 leaq         $64(%rsp,%rax), %rax
	0x4d, 0x39, 0xf4, //0x00000d9a cmpq         %r14, %r12
	0x0f, 0x83, 0xb3, 0x01, 0x00, 0x00, //0x00000d9d jae          LBB0_165
	//0x00000da3 LBB0_147
	0x4c, 0x39, 0xf8, //0x00000da3 cmpq         %r15, %rax
	0x0f, 0x86, 0xaa, 0x01, 0x00, 0x00, //0x00000da6 jbe          LBB0_165
	0x41, 0x38, 0x1c, 0x24, //0x00000dac cmpb         %bl, (%r12)
	0x0f, 0x85, 0xa0, 0x01, 0x00, 0x00, //0x00000db0 jne          LBB0_165
	0x49, 0xff, 0xc4, //0x00000db6 incq         %r12
	0x48, 0x8d, 0x74, 0x24, 0x41, //0x00000db9 leaq         $65(%rsp), %rsi
	0x4c, 0x89, 0xe7, //0x00000dbe movq         %r12, %rdi
	//0x00000dc1 LBB0_150
	0x49, 0x89, 0xfc, //0x00000dc1 movq         %rdi, %r12
	0x48, 0x89, 0xf2, //0x00000dc4 movq         %rsi, %rdx
	0x48, 0x39, 0xc6, //0x00000dc7 cmpq         %rax, %rsi
	0x0f, 0x83, 0x89, 0x01, 0x00, 0x00, //0x00000dca jae          LBB0_166
	0x4d, 0x39, 0xf4, //0x00000dd0 cmpq         %r14, %r12
	0x0f, 0x83, 0x80, 0x01, 0x00, 0x00, //0x00000dd3 jae          LBB0_166
	0x41, 0x0f, 0xb6, 0x0c, 0x24, //0x00000dd9 movzbl       (%r12), %ecx
	0x49, 0x8d, 0x7c, 0x24, 0x01, //0x00000dde leaq         $1(%r12), %rdi
	0x48, 0x8d, 0x72, 0x01, //0x00000de3 leaq         $1(%rdx), %rsi
	0x3a, 0x0a, //0x00000de7 cmpb         (%rdx), %cl
	0x0f, 0x84, 0xd2, 0xff, 0xff, 0xff, //0x00000de9 je           LBB0_150
	0xe9, 0x65, 0x01, 0x00, 0x00, //0x00000def jmp          LBB0_166
	//0x00000df4 LBB0_153
	0x88, 0x5c, 0x24, 0x40, //0x00000df4 movb         %bl, $64(%rsp)
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00000df8 movl         $1, %eax
	0xe9, 0x8b, 0xff, 0xff, 0xff, //0x00000dfd jmp          LBB0_146
	//0x00000e02 LBB0_154
	0xc1, 0xea, 0x06, //0x00000e02 shrl         $6, %edx
	0x80, 0xca, 0xc0, //0x00000e05 orb          $-64, %dl
	0x88, 0x54, 0x24, 0x40, //0x00000e08 movb         %dl, $64(%rsp)
	0x80, 0xe3, 0x3f, //0x00000e0c andb         $63, %bl
	0x80, 0xcb, 0x80, //0x00000e0f orb          $-128, %bl
	0x88, 0x5c, 0x24, 0x41, //0x00000e12 movb         %bl, $65(%rsp)
	0xb8, 0x02, 0x00, 0x00, 0x00, //0x00000e16 movl         $2, %eax
	0x89, 0xd3, //0x00000e1b movl         %edx, %ebx
	0xe9, 0x6b, 0xff, 0xff, 0xff, //0x00000e1d jmp          LBB0_146
	//0x00000e22 LBB0_155
	0x48, 0x83, 0xf8, 0x06, //0x00000e22 cmpq         $6, %rax
	0x0f, 0x8c, 0xf2, 0x48, 0x00, 0x00, //0x00000e26 jl           LBB0_1119
	0x81, 0xfa, 0xff, 0xdb, 0x00, 0x00, //0x00000e2c cmpl         $56319, %edx
	0x0f, 0x87, 0xe6, 0x48, 0x00, 0x00, //0x00000e32 ja           LBB0_1119
	0x41, 0x80, 0x3b, 0x5c, //0x00000e38 cmpb         $92, (%r11)
	0x0f, 0x85, 0xdc, 0x48, 0x00, 0x00, //0x00000e3c jne          LBB0_1119
	0x41, 0x80, 0x79, 0x07, 0x75, //0x00000e42 cmpb         $117, $7(%r9)
	0x0f, 0x85, 0xd1, 0x48, 0x00, 0x00, //0x00000e47 jne          LBB0_1119
	0x4d, 0x8d, 0x59, 0x08, //0x00000e4d leaq         $8(%r9), %r11
	0x41, 0x8b, 0x59, 0x08, //0x00000e51 movl         $8(%r9), %ebx
	0x89, 0xdf, //0x00000e55 movl         %ebx, %edi
	0xf7, 0xd7, //0x00000e57 notl         %edi
	0x8d, 0x8b, 0xd0, 0xcf, 0xcf, 0xcf, //0x00000e59 leal         $-808464432(%rbx), %ecx
	0x81, 0xe7, 0x80, 0x80, 0x80, 0x80, //0x00000e5f andl         $-2139062144, %edi
	0x85, 0xcf, //0x00000e65 testl        %ecx, %edi
	0x0f, 0x85, 0xc0, 0x48, 0x00, 0x00, //0x00000e67 jne          LBB0_1120
	0x8d, 0x8b, 0x19, 0x19, 0x19, 0x19, //0x00000e6d leal         $421075225(%rbx), %ecx
	0x09, 0xd9, //0x00000e73 orl          %ebx, %ecx
	0xf7, 0xc1, 0x80, 0x80, 0x80, 0x80, //0x00000e75 testl        $-2139062144, %ecx
	0x0f, 0x85, 0xac, 0x48, 0x00, 0x00, //0x00000e7b jne          LBB0_1120
	0x89, 0xde, //0x00000e81 movl         %ebx, %esi
	0x81, 0xe6, 0x7f, 0x7f, 0x7f, 0x7f, //0x00000e83 andl         $2139062143, %esi
	0xb9, 0xc0, 0xc0, 0xc0, 0xc0, //0x00000e89 movl         $-1061109568, %ecx
	0x29, 0xf1, //0x00000e8e subl         %esi, %ecx
	0x8d, 0x86, 0x46, 0x46, 0x46, 0x46, //0x00000e90 leal         $1179010630(%rsi), %eax
	0x21, 0xf9, //0x00000e96 andl         %edi, %ecx
	0x85, 0xc1, //0x00000e98 testl        %eax, %ecx
	0x0f, 0x85, 0x8d, 0x48, 0x00, 0x00, //0x00000e9a jne          LBB0_1120
	0xb8, 0xe0, 0xe0, 0xe0, 0xe0, //0x00000ea0 movl         $-522133280, %eax
	0x29, 0xf0, //0x00000ea5 subl         %esi, %eax
	0x81, 0xc6, 0x39, 0x39, 0x39, 0x39, //0x00000ea7 addl         $960051513, %esi
	0x21, 0xc7, //0x00000ead andl         %eax, %edi
	0x85, 0xf7, //0x00000eaf testl        %esi, %edi
	0x0f, 0x85, 0x76, 0x48, 0x00, 0x00, //0x00000eb1 jne          LBB0_1120
	0x0f, 0xcb, //0x00000eb7 bswapl       %ebx
	0x89, 0xd8, //0x00000eb9 movl         %ebx, %eax
	0xc1, 0xe8, 0x04, //0x00000ebb shrl         $4, %eax
	0xf7, 0xd0, //0x00000ebe notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00000ec0 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00000ec5 leal         (%rax,%rax,8), %eax
	0x81, 0xe3, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000ec8 andl         $252645135, %ebx
	0x01, 0xc3, //0x00000ece addl         %eax, %ebx
	0x89, 0xde, //0x00000ed0 movl         %ebx, %esi
	0xc1, 0xee, 0x04, //0x00000ed2 shrl         $4, %esi
	0x09, 0xde, //0x00000ed5 orl          %ebx, %esi
	0x89, 0xf0, //0x00000ed7 movl         %esi, %eax
	0x25, 0x00, 0x00, 0xfc, 0x00, //0x00000ed9 andl         $16515072, %eax
	0x3d, 0x00, 0x00, 0xdc, 0x00, //0x00000ede cmpl         $14417920, %eax
	0x0f, 0x85, 0x35, 0x48, 0x00, 0x00, //0x00000ee3 jne          LBB0_1119
	0x89, 0xf0, //0x00000ee9 movl         %esi, %eax
	0xc1, 0xe8, 0x08, //0x00000eeb shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x00000eee andl         $65280, %eax
	0x40, 0x0f, 0xb6, 0xce, //0x00000ef3 movzbl       %sil, %ecx
	0x09, 0xc1, //0x00000ef7 orl          %eax, %ecx
	0xc1, 0xe2, 0x0a, //0x00000ef9 shll         $10, %edx
	0x8d, 0x84, 0x0a, 0x00, 0x24, 0xa0, 0xfc, //0x00000efc leal         $-56613888(%rdx,%rcx), %eax
	0x89, 0xc3, //0x00000f03 movl         %eax, %ebx
	0xc1, 0xeb, 0x12, //0x00000f05 shrl         $18, %ebx
	0x80, 0xcb, 0xf0, //0x00000f08 orb          $-16, %bl
	0x88, 0x5c, 0x24, 0x40, //0x00000f0b movb         %bl, $64(%rsp)
	0x89, 0xc1, //0x00000f0f movl         %eax, %ecx
	0xc1, 0xe9, 0x0c, //0x00000f11 shrl         $12, %ecx
	0x80, 0xe1, 0x3f, //0x00000f14 andb         $63, %cl
	0x80, 0xc9, 0x80, //0x00000f17 orb          $-128, %cl
	0x88, 0x4c, 0x24, 0x41, //0x00000f1a movb         %cl, $65(%rsp)
	0x89, 0xc1, //0x00000f1e movl         %eax, %ecx
	0xc1, 0xe9, 0x06, //0x00000f20 shrl         $6, %ecx
	0x80, 0xe1, 0x3f, //0x00000f23 andb         $63, %cl
	0x80, 0xc9, 0x80, //0x00000f26 orb          $-128, %cl
	0x88, 0x4c, 0x24, 0x42, //0x00000f29 movb         %cl, $66(%rsp)
	0x24, 0x3f, //0x00000f2d andb         $63, %al
	0x0c, 0x80, //0x00000f2f orb          $-128, %al
	0x88, 0x44, 0x24, 0x43, //0x00000f31 movb         %al, $67(%rsp)
	0x49, 0x83, 0xc1, 0x0c, //0x00000f35 addq         $12, %r9
	0xb8, 0x04, 0x00, 0x00, 0x00, //0x00000f39 movl         $4, %eax
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00000f3e movq         $24(%rsp), %r11
	0x4c, 0x8d, 0x7c, 0x24, 0x40, //0x00000f43 leaq         $64(%rsp), %r15
	0x48, 0x8d, 0x44, 0x04, 0x40, //0x00000f48 leaq         $64(%rsp,%rax), %rax
	0x4d, 0x39, 0xf4, //0x00000f4d cmpq         %r14, %r12
	0x0f, 0x82, 0x4d, 0xfe, 0xff, 0xff, //0x00000f50 jb           LBB0_147
	//0x00000f56 LBB0_165
	0x4c, 0x89, 0xfa, //0x00000f56 movq         %r15, %rdx
	//0x00000f59 LBB0_166
	0x48, 0x39, 0xc2, //0x00000f59 cmpq         %rax, %rdx
	0x0f, 0x85, 0x63, 0x0e, 0x00, 0x00, //0x00000f5c jne          LBB0_364
	//0x00000f62 LBB0_167
	0x4d, 0x39, 0xc8, //0x00000f62 cmpq         %r9, %r8
	0x0f, 0x86, 0x09, 0x00, 0x00, 0x00, //0x00000f65 jbe          LBB0_169
	0x4d, 0x39, 0xf4, //0x00000f6b cmpq         %r14, %r12
	0x0f, 0x82, 0xb2, 0xfc, 0xff, 0xff, //0x00000f6e jb           LBB0_130
	//0x00000f74 LBB0_169
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x00000f74 movq         $16(%rsp), %r15
	0x4d, 0x31, 0xc8, //0x00000f79 xorq         %r9, %r8
	0x4d, 0x31, 0xf4, //0x00000f7c xorq         %r14, %r12
	0x45, 0x31, 0xc9, //0x00000f7f xorl         %r9d, %r9d
	0x4d, 0x09, 0xc4, //0x00000f82 orq          %r8, %r12
	0x41, 0x0f, 0x94, 0xc1, //0x00000f85 sete         %r9b
	//0x00000f89 LBB0_170
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x00000f89 movq         $8(%rsp), %r14
	0x4c, 0x8b, 0x04, 0x24, //0x00000f8e movq         (%rsp), %r8
	0x49, 0x8b, 0x13, //0x00000f92 movq         (%r11), %rdx
	0x49, 0x39, 0xd2, //0x00000f95 cmpq         %rdx, %r10
	0x0f, 0x82, 0x92, 0xfb, 0xff, 0xff, //0x00000f98 jb           LBB0_121
	0x90, 0x90, //0x00000f9e .p2align 4, 0x90
	//0x00000fa0 LBB0_171
	0x49, 0x8d, 0x42, 0x01, //0x00000fa0 leaq         $1(%r10), %rax
	0x48, 0x39, 0xd0, //0x00000fa4 cmpq         %rdx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000fa7 jae          LBB0_175
	0x41, 0x8a, 0x1c, 0x00, //0x00000fad movb         (%r8,%rax), %bl
	0x80, 0xfb, 0x0d, //0x00000fb1 cmpb         $13, %bl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000fb4 je           LBB0_175
	0x80, 0xfb, 0x20, //0x00000fba cmpb         $32, %bl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x00000fbd je           LBB0_175
	0x80, 0xc3, 0xf7, //0x00000fc3 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000fc6 cmpb         $1, %bl
	0x0f, 0x87, 0x49, 0x01, 0x00, 0x00, //0x00000fc9 ja           LBB0_197
	0x90, //0x00000fcf .p2align 4, 0x90
	//0x00000fd0 LBB0_175
	0x49, 0x8d, 0x42, 0x02, //0x00000fd0 leaq         $2(%r10), %rax
	0x48, 0x39, 0xd0, //0x00000fd4 cmpq         %rdx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000fd7 jae          LBB0_179
	0x41, 0x8a, 0x1c, 0x00, //0x00000fdd movb         (%r8,%rax), %bl
	0x80, 0xfb, 0x0d, //0x00000fe1 cmpb         $13, %bl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000fe4 je           LBB0_179
	0x80, 0xfb, 0x20, //0x00000fea cmpb         $32, %bl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x00000fed je           LBB0_179
	0x80, 0xc3, 0xf7, //0x00000ff3 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000ff6 cmpb         $1, %bl
	0x0f, 0x87, 0x19, 0x01, 0x00, 0x00, //0x00000ff9 ja           LBB0_197
	0x90, //0x00000fff .p2align 4, 0x90
	//0x00001000 LBB0_179
	0x49, 0x8d, 0x42, 0x03, //0x00001000 leaq         $3(%r10), %rax
	0x48, 0x39, 0xd0, //0x00001004 cmpq         %rdx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00001007 jae          LBB0_183
	0x41, 0x8a, 0x1c, 0x00, //0x0000100d movb         (%r8,%rax), %bl
	0x80, 0xfb, 0x0d, //0x00001011 cmpb         $13, %bl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001014 je           LBB0_183
	0x80, 0xfb, 0x20, //0x0000101a cmpb         $32, %bl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000101d je           LBB0_183
	0x80, 0xc3, 0xf7, //0x00001023 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00001026 cmpb         $1, %bl
	0x0f, 0x87, 0xe9, 0x00, 0x00, 0x00, //0x00001029 ja           LBB0_197
	0x90, //0x0000102f .p2align 4, 0x90
	//0x00001030 LBB0_183
	0x49, 0x8d, 0x42, 0x04, //0x00001030 leaq         $4(%r10), %rax
	0x48, 0x89, 0xd7, //0x00001034 movq         %rdx, %rdi
	0x48, 0x29, 0xc7, //0x00001037 subq         %rax, %rdi
	0x0f, 0x86, 0xab, 0x22, 0x00, 0x00, //0x0000103a jbe          LBB0_656
	0x4c, 0x01, 0xc0, //0x00001040 addq         %r8, %rax
	0x48, 0x83, 0xff, 0x20, //0x00001043 cmpq         $32, %rdi
	0x0f, 0x82, 0x55, 0x00, 0x00, 0x00, //0x00001047 jb           LBB0_189
	0x48, 0x89, 0xd6, //0x0000104d movq         %rdx, %rsi
	0x4c, 0x29, 0xd6, //0x00001050 subq         %r10, %rsi
	0x48, 0x83, 0xc6, 0xdc, //0x00001053 addq         $-36, %rsi
	0x48, 0x89, 0xf1, //0x00001057 movq         %rsi, %rcx
	0x48, 0x83, 0xe1, 0xe0, //0x0000105a andq         $-32, %rcx
	0x4c, 0x01, 0xd1, //0x0000105e addq         %r10, %rcx
	0x49, 0x8d, 0x5c, 0x08, 0x24, //0x00001061 leaq         $36(%r8,%rcx), %rbx
	0x83, 0xe6, 0x1f, //0x00001066 andl         $31, %esi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001069 .p2align 4, 0x90
	//0x00001070 LBB0_186
	0xc5, 0xfe, 0x6f, 0x30, //0x00001070 vmovdqu      (%rax), %ymm6
	0xc4, 0x62, 0x7d, 0x00, 0xe6, //0x00001074 vpshufb      %ymm6, %ymm0, %ymm12
	0xc5, 0x9d, 0x74, 0xf6, //0x00001079 vpcmpeqb     %ymm6, %ymm12, %ymm6
	0xc5, 0xfd, 0xd7, 0xce, //0x0000107d vpmovmskb    %ymm6, %ecx
	0x83, 0xf9, 0xff, //0x00001081 cmpl         $-1, %ecx
	0x0f, 0x85, 0x76, 0x00, 0x00, 0x00, //0x00001084 jne          LBB0_196
	0x48, 0x83, 0xc0, 0x20, //0x0000108a addq         $32, %rax
	0x48, 0x83, 0xc7, 0xe0, //0x0000108e addq         $-32, %rdi
	0x48, 0x83, 0xff, 0x1f, //0x00001092 cmpq         $31, %rdi
	0x0f, 0x87, 0xd4, 0xff, 0xff, 0xff, //0x00001096 ja           LBB0_186
	0x48, 0x89, 0xf7, //0x0000109c movq         %rsi, %rdi
	0x48, 0x89, 0xd8, //0x0000109f movq         %rbx, %rax
	//0x000010a2 LBB0_189
	0x48, 0x85, 0xff, //0x000010a2 testq        %rdi, %rdi
	0x0f, 0x84, 0x3a, 0x00, 0x00, 0x00, //0x000010a5 je           LBB0_195
	0x48, 0x8d, 0x34, 0x38, //0x000010ab leaq         (%rax,%rdi), %rsi
	0x48, 0xff, 0xc0, //0x000010af incq         %rax
	//0x000010b2 LBB0_191
	0x0f, 0xbe, 0x48, 0xff, //0x000010b2 movsbl       $-1(%rax), %ecx
	0x83, 0xf9, 0x20, //0x000010b6 cmpl         $32, %ecx
	0x0f, 0x87, 0xb5, 0x0c, 0x00, 0x00, //0x000010b9 ja           LBB0_360
	0x48, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000010bf movabsq      $4294977024, %rbx
	0x48, 0x0f, 0xa3, 0xcb, //0x000010c9 btq          %rcx, %rbx
	0x0f, 0x83, 0xa1, 0x0c, 0x00, 0x00, //0x000010cd jae          LBB0_360
	0x48, 0xff, 0xcf, //0x000010d3 decq         %rdi
	0x48, 0xff, 0xc0, //0x000010d6 incq         %rax
	0x48, 0x85, 0xff, //0x000010d9 testq        %rdi, %rdi
	0x0f, 0x85, 0xd0, 0xff, 0xff, 0xff, //0x000010dc jne          LBB0_191
	0x48, 0x89, 0xf0, //0x000010e2 movq         %rsi, %rax
	//0x000010e5 LBB0_195
	0x4c, 0x29, 0xc0, //0x000010e5 subq         %r8, %rax
	0x48, 0x39, 0xd0, //0x000010e8 cmpq         %rdx, %rax
	0x0f, 0x82, 0x27, 0x00, 0x00, 0x00, //0x000010eb jb           LBB0_197
	0xe9, 0xfb, 0x21, 0x00, 0x00, //0x000010f1 jmp          LBB0_657
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000010f6 .p2align 4, 0x90
	//0x00001100 LBB0_196
	0x4c, 0x29, 0xc0, //0x00001100 subq         %r8, %rax
	0xf7, 0xd1, //0x00001103 notl         %ecx
	0x48, 0x63, 0xc9, //0x00001105 movslq       %ecx, %rcx
	0x48, 0x0f, 0xbc, 0xc9, //0x00001108 bsfq         %rcx, %rcx
	0x48, 0x01, 0xc8, //0x0000110c addq         %rcx, %rax
	0x48, 0x39, 0xd0, //0x0000110f cmpq         %rdx, %rax
	0x0f, 0x83, 0xd9, 0x21, 0x00, 0x00, //0x00001112 jae          LBB0_657
	//0x00001118 LBB0_197
	0x4c, 0x8d, 0x50, 0x01, //0x00001118 leaq         $1(%rax), %r10
	0x4d, 0x89, 0x16, //0x0000111c movq         %r10, (%r14)
	0x41, 0x80, 0x3c, 0x00, 0x3a, //0x0000111f cmpb         $58, (%r8,%rax)
	0x0f, 0x85, 0xc7, 0x21, 0x00, 0x00, //0x00001124 jne          LBB0_657
	0x4d, 0x85, 0xc9, //0x0000112a testq        %r9, %r9
	0x0f, 0x85, 0x4d, 0x1d, 0x00, 0x00, //0x0000112d jne          LBB0_524
	0x4d, 0x8b, 0x03, //0x00001133 movq         (%r11), %r8
	0x4d, 0x39, 0xc2, //0x00001136 cmpq         %r8, %r10
	0x0f, 0x83, 0x31, 0x00, 0x00, 0x00, //0x00001139 jae          LBB0_204
	0x48, 0x8b, 0x0c, 0x24, //0x0000113f movq         (%rsp), %rcx
	0x42, 0x8a, 0x0c, 0x11, //0x00001143 movb         (%rcx,%r10), %cl
	0x80, 0xf9, 0x0d, //0x00001147 cmpb         $13, %cl
	0x0f, 0x84, 0x20, 0x00, 0x00, 0x00, //0x0000114a je           LBB0_204
	0x80, 0xf9, 0x20, //0x00001150 cmpb         $32, %cl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00001153 je           LBB0_204
	0x80, 0xc1, 0xf7, //0x00001159 addb         $-9, %cl
	0x80, 0xf9, 0x01, //0x0000115c cmpb         $1, %cl
	0x0f, 0x86, 0x0b, 0x00, 0x00, 0x00, //0x0000115f jbe          LBB0_204
	0x4c, 0x89, 0xd1, //0x00001165 movq         %r10, %rcx
	0xe9, 0xac, 0x01, 0x00, 0x00, //0x00001168 jmp          LBB0_230
	0x90, 0x90, 0x90, //0x0000116d .p2align 4, 0x90
	//0x00001170 LBB0_204
	0x48, 0x8d, 0x48, 0x02, //0x00001170 leaq         $2(%rax), %rcx
	0x4c, 0x39, 0xc1, //0x00001174 cmpq         %r8, %rcx
	0x0f, 0x83, 0x33, 0x00, 0x00, 0x00, //0x00001177 jae          LBB0_208
	0x48, 0x8b, 0x14, 0x24, //0x0000117d movq         (%rsp), %rdx
	0x8a, 0x1c, 0x0a, //0x00001181 movb         (%rdx,%rcx), %bl
	0x80, 0xfb, 0x0d, //0x00001184 cmpb         $13, %bl
	0x0f, 0x84, 0x23, 0x00, 0x00, 0x00, //0x00001187 je           LBB0_208
	0x80, 0xfb, 0x20, //0x0000118d cmpb         $32, %bl
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00001190 je           LBB0_208
	0x80, 0xc3, 0xf7, //0x00001196 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00001199 cmpb         $1, %bl
	0x0f, 0x87, 0x77, 0x01, 0x00, 0x00, //0x0000119c ja           LBB0_230
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000011a2 .p2align 4, 0x90
	//0x000011b0 LBB0_208
	0x48, 0x8d, 0x48, 0x03, //0x000011b0 leaq         $3(%rax), %rcx
	0x4c, 0x39, 0xc1, //0x000011b4 cmpq         %r8, %rcx
	0x0f, 0x83, 0x33, 0x00, 0x00, 0x00, //0x000011b7 jae          LBB0_212
	0x48, 0x8b, 0x14, 0x24, //0x000011bd movq         (%rsp), %rdx
	0x8a, 0x1c, 0x0a, //0x000011c1 movb         (%rdx,%rcx), %bl
	0x80, 0xfb, 0x0d, //0x000011c4 cmpb         $13, %bl
	0x0f, 0x84, 0x23, 0x00, 0x00, 0x00, //0x000011c7 je           LBB0_212
	0x80, 0xfb, 0x20, //0x000011cd cmpb         $32, %bl
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x000011d0 je           LBB0_212
	0x80, 0xc3, 0xf7, //0x000011d6 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x000011d9 cmpb         $1, %bl
	0x0f, 0x87, 0x37, 0x01, 0x00, 0x00, //0x000011dc ja           LBB0_230
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000011e2 .p2align 4, 0x90
	//0x000011f0 LBB0_212
	0x48, 0x8d, 0x48, 0x04, //0x000011f0 leaq         $4(%rax), %rcx
	0x4c, 0x39, 0xc1, //0x000011f4 cmpq         %r8, %rcx
	0x0f, 0x83, 0x33, 0x00, 0x00, 0x00, //0x000011f7 jae          LBB0_216
	0x48, 0x8b, 0x14, 0x24, //0x000011fd movq         (%rsp), %rdx
	0x8a, 0x1c, 0x0a, //0x00001201 movb         (%rdx,%rcx), %bl
	0x80, 0xfb, 0x0d, //0x00001204 cmpb         $13, %bl
	0x0f, 0x84, 0x23, 0x00, 0x00, 0x00, //0x00001207 je           LBB0_216
	0x80, 0xfb, 0x20, //0x0000120d cmpb         $32, %bl
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00001210 je           LBB0_216
	0x80, 0xc3, 0xf7, //0x00001216 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00001219 cmpb         $1, %bl
	0x0f, 0x87, 0xf7, 0x00, 0x00, 0x00, //0x0000121c ja           LBB0_230
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001222 .p2align 4, 0x90
	//0x00001230 LBB0_216
	0x48, 0x8d, 0x48, 0x05, //0x00001230 leaq         $5(%rax), %rcx
	0x4c, 0x89, 0xc7, //0x00001234 movq         %r8, %rdi
	0x48, 0x29, 0xcf, //0x00001237 subq         %rcx, %rdi
	0x0f, 0x86, 0x00, 0x06, 0x00, 0x00, //0x0000123a jbe          LBB0_293
	0x48, 0x8b, 0x1c, 0x24, //0x00001240 movq         (%rsp), %rbx
	0x48, 0x01, 0xd9, //0x00001244 addq         %rbx, %rcx
	0x48, 0x83, 0xff, 0x20, //0x00001247 cmpq         $32, %rdi
	0x0f, 0x82, 0x51, 0x00, 0x00, 0x00, //0x0000124b jb           LBB0_222
	0x4c, 0x89, 0xc2, //0x00001251 movq         %r8, %rdx
	0x48, 0x29, 0xc2, //0x00001254 subq         %rax, %rdx
	0x48, 0x83, 0xc2, 0xdb, //0x00001257 addq         $-37, %rdx
	0x48, 0x89, 0xd6, //0x0000125b movq         %rdx, %rsi
	0x48, 0x83, 0xe6, 0xe0, //0x0000125e andq         $-32, %rsi
	0x48, 0x01, 0xc6, //0x00001262 addq         %rax, %rsi
	0x48, 0x8d, 0x44, 0x33, 0x25, //0x00001265 leaq         $37(%rbx,%rsi), %rax
	0x83, 0xe2, 0x1f, //0x0000126a andl         $31, %edx
	0x90, 0x90, 0x90, //0x0000126d .p2align 4, 0x90
	//0x00001270 LBB0_219
	0xc5, 0xfe, 0x6f, 0x31, //0x00001270 vmovdqu      (%rcx), %ymm6
	0xc4, 0x62, 0x7d, 0x00, 0xe6, //0x00001274 vpshufb      %ymm6, %ymm0, %ymm12
	0xc5, 0x9d, 0x74, 0xf6, //0x00001279 vpcmpeqb     %ymm6, %ymm12, %ymm6
	0xc5, 0xfd, 0xd7, 0xde, //0x0000127d vpmovmskb    %ymm6, %ebx
	0x83, 0xfb, 0xff, //0x00001281 cmpl         $-1, %ebx
	0x0f, 0x85, 0x76, 0x00, 0x00, 0x00, //0x00001284 jne          LBB0_229
	0x48, 0x83, 0xc1, 0x20, //0x0000128a addq         $32, %rcx
	0x48, 0x83, 0xc7, 0xe0, //0x0000128e addq         $-32, %rdi
	0x48, 0x83, 0xff, 0x1f, //0x00001292 cmpq         $31, %rdi
	0x0f, 0x87, 0xd4, 0xff, 0xff, 0xff, //0x00001296 ja           LBB0_219
	0x48, 0x89, 0xd7, //0x0000129c movq         %rdx, %rdi
	0x48, 0x89, 0xc1, //0x0000129f movq         %rax, %rcx
	//0x000012a2 LBB0_222
	0x48, 0x85, 0xff, //0x000012a2 testq        %rdi, %rdi
	0x0f, 0x84, 0x3a, 0x00, 0x00, 0x00, //0x000012a5 je           LBB0_228
	0x48, 0x8d, 0x04, 0x39, //0x000012ab leaq         (%rcx,%rdi), %rax
	0x48, 0xff, 0xc1, //0x000012af incq         %rcx
	//0x000012b2 LBB0_224
	0x0f, 0xbe, 0x71, 0xff, //0x000012b2 movsbl       $-1(%rcx), %esi
	0x83, 0xfe, 0x20, //0x000012b6 cmpl         $32, %esi
	0x0f, 0x87, 0xe3, 0x0a, 0x00, 0x00, //0x000012b9 ja           LBB0_362
	0x48, 0xba, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000012bf movabsq      $4294977024, %rdx
	0x48, 0x0f, 0xa3, 0xf2, //0x000012c9 btq          %rsi, %rdx
	0x0f, 0x83, 0xcf, 0x0a, 0x00, 0x00, //0x000012cd jae          LBB0_362
	0x48, 0xff, 0xcf, //0x000012d3 decq         %rdi
	0x48, 0xff, 0xc1, //0x000012d6 incq         %rcx
	0x48, 0x85, 0xff, //0x000012d9 testq        %rdi, %rdi
	0x0f, 0x85, 0xd0, 0xff, 0xff, 0xff, //0x000012dc jne          LBB0_224
	0x48, 0x89, 0xc1, //0x000012e2 movq         %rax, %rcx
	//0x000012e5 LBB0_228
	0x48, 0x2b, 0x0c, 0x24, //0x000012e5 subq         (%rsp), %rcx
	0x4c, 0x39, 0xc1, //0x000012e9 cmpq         %r8, %rcx
	0x0f, 0x82, 0x27, 0x00, 0x00, 0x00, //0x000012ec jb           LBB0_230
	0xe9, 0x4f, 0x05, 0x00, 0x00, //0x000012f2 jmp          LBB0_294
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000012f7 .p2align 4, 0x90
	//0x00001300 LBB0_229
	0x48, 0x2b, 0x0c, 0x24, //0x00001300 subq         (%rsp), %rcx
	0xf7, 0xd3, //0x00001304 notl         %ebx
	0x48, 0x63, 0xc3, //0x00001306 movslq       %ebx, %rax
	0x48, 0x0f, 0xbc, 0xc0, //0x00001309 bsfq         %rax, %rax
	0x48, 0x01, 0xc1, //0x0000130d addq         %rax, %rcx
	0x4c, 0x39, 0xc1, //0x00001310 cmpq         %r8, %rcx
	0x0f, 0x83, 0x2d, 0x05, 0x00, 0x00, //0x00001313 jae          LBB0_294
	//0x00001319 LBB0_230
	0x4c, 0x8d, 0x51, 0x01, //0x00001319 leaq         $1(%rcx), %r10
	0x4d, 0x89, 0x16, //0x0000131d movq         %r10, (%r14)
	0x48, 0x8b, 0x04, 0x24, //0x00001320 movq         (%rsp), %rax
	0x0f, 0xbe, 0x04, 0x08, //0x00001324 movsbl       (%rax,%rcx), %eax
	0x83, 0xf8, 0x7b, //0x00001328 cmpl         $123, %eax
	0x0f, 0x87, 0x0f, 0x05, 0x00, 0x00, //0x0000132b ja           LBB0_293
	0x48, 0x8d, 0x15, 0x20, 0x46, 0x00, 0x00, //0x00001331 leaq         $17952(%rip), %rdx  /* LJTI0_1+0(%rip) */
	0x48, 0x63, 0x04, 0x82, //0x00001338 movslq       (%rdx,%rax,4), %rax
	0x48, 0x01, 0xd0, //0x0000133c addq         %rdx, %rax
	0xff, 0xe0, //0x0000133f jmpq         *%rax
	//0x00001341 LBB0_232
	0x49, 0x8b, 0x13, //0x00001341 movq         (%r11), %rdx
	0x48, 0x89, 0xd0, //0x00001344 movq         %rdx, %rax
	0x4c, 0x29, 0xd0, //0x00001347 subq         %r10, %rax
	0x48, 0x8b, 0x3c, 0x24, //0x0000134a movq         (%rsp), %rdi
	0x49, 0x01, 0xfa, //0x0000134e addq         %rdi, %r10
	0x48, 0x83, 0xf8, 0x20, //0x00001351 cmpq         $32, %rax
	0x0f, 0x82, 0x5e, 0x00, 0x00, 0x00, //0x00001355 jb           LBB0_237
	0x48, 0x29, 0xca, //0x0000135b subq         %rcx, %rdx
	0x48, 0x83, 0xc2, 0xdf, //0x0000135e addq         $-33, %rdx
	0x48, 0x89, 0xd6, //0x00001362 movq         %rdx, %rsi
	0x48, 0x83, 0xe6, 0xe0, //0x00001365 andq         $-32, %rsi
	0x48, 0x01, 0xce, //0x00001369 addq         %rcx, %rsi
	0x48, 0x8d, 0x4c, 0x37, 0x21, //0x0000136c leaq         $33(%rdi,%rsi), %rcx
	0x83, 0xe2, 0x1f, //0x00001371 andl         $31, %edx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001374 .p2align 4, 0x90
	//0x00001380 LBB0_234
	0xc4, 0xc1, 0x7e, 0x6f, 0x32, //0x00001380 vmovdqu      (%r10), %ymm6
	0xc5, 0x4d, 0x74, 0xe3, //0x00001385 vpcmpeqb     %ymm3, %ymm6, %ymm12
	0xc5, 0xcd, 0xeb, 0xf4, //0x00001389 vpor         %ymm4, %ymm6, %ymm6
	0xc5, 0xcd, 0x74, 0xf5, //0x0000138d vpcmpeqb     %ymm5, %ymm6, %ymm6
	0xc5, 0x9d, 0xeb, 0xf6, //0x00001391 vpor         %ymm6, %ymm12, %ymm6
	0xc5, 0xfd, 0xd7, 0xf6, //0x00001395 vpmovmskb    %ymm6, %esi
	0x85, 0xf6, //0x00001399 testl        %esi, %esi
	0x0f, 0x85, 0xcf, 0x00, 0x00, 0x00, //0x0000139b jne          LBB0_251
	0x49, 0x83, 0xc2, 0x20, //0x000013a1 addq         $32, %r10
	0x48, 0x83, 0xc0, 0xe0, //0x000013a5 addq         $-32, %rax
	0x48, 0x83, 0xf8, 0x1f, //0x000013a9 cmpq         $31, %rax
	0x0f, 0x87, 0xcd, 0xff, 0xff, 0xff, //0x000013ad ja           LBB0_234
	0x48, 0x89, 0xd0, //0x000013b3 movq         %rdx, %rax
	0x49, 0x89, 0xca, //0x000013b6 movq         %rcx, %r10
	//0x000013b9 LBB0_237
	0x48, 0x83, 0xf8, 0x10, //0x000013b9 cmpq         $16, %rax
	0x0f, 0x82, 0x55, 0x00, 0x00, 0x00, //0x000013bd jb           LBB0_242
	0x48, 0x8d, 0x48, 0xf0, //0x000013c3 leaq         $-16(%rax), %rcx
	0x48, 0x89, 0xca, //0x000013c7 movq         %rcx, %rdx
	0x48, 0x83, 0xe2, 0xf0, //0x000013ca andq         $-16, %rdx
	0x4a, 0x8d, 0x54, 0x12, 0x10, //0x000013ce leaq         $16(%rdx,%r10), %rdx
	0x83, 0xe1, 0x0f, //0x000013d3 andl         $15, %ecx
	//0x000013d6 LBB0_239
	0xc4, 0xc1, 0x7a, 0x6f, 0x32, //0x000013d6 vmovdqu      (%r10), %xmm6
	0xc5, 0xc9, 0x74, 0x3d, 0xfd, 0xed, 0xff, 0xff, //0x000013db vpcmpeqb     $-4611(%rip), %xmm6, %xmm7  /* LCPI0_4+0(%rip) */
	0xc5, 0xc9, 0xeb, 0x35, 0x05, 0xee, 0xff, 0xff, //0x000013e3 vpor         $-4603(%rip), %xmm6, %xmm6  /* LCPI0_5+0(%rip) */
	0xc5, 0xb9, 0x74, 0xf6, //0x000013eb vpcmpeqb     %xmm6, %xmm8, %xmm6
	0xc5, 0xc9, 0xeb, 0xf7, //0x000013ef vpor         %xmm7, %xmm6, %xmm6
	0xc5, 0xf9, 0xd7, 0xf6, //0x000013f3 vpmovmskb    %xmm6, %esi
	0x66, 0x85, 0xf6, //0x000013f7 testw        %si, %si
	0x0f, 0x85, 0x6c, 0x09, 0x00, 0x00, //0x000013fa jne          LBB0_359
	0x49, 0x83, 0xc2, 0x10, //0x00001400 addq         $16, %r10
	0x48, 0x83, 0xc0, 0xf0, //0x00001404 addq         $-16, %rax
	0x48, 0x83, 0xf8, 0x0f, //0x00001408 cmpq         $15, %rax
	0x0f, 0x87, 0xc4, 0xff, 0xff, 0xff, //0x0000140c ja           LBB0_239
	0x48, 0x89, 0xc8, //0x00001412 movq         %rcx, %rax
	0x49, 0x89, 0xd2, //0x00001415 movq         %rdx, %r10
	//0x00001418 LBB0_242
	0x48, 0x85, 0xc0, //0x00001418 testq        %rax, %rax
	0x0f, 0x84, 0x32, 0x00, 0x00, 0x00, //0x0000141b je           LBB0_249
	0x49, 0x8d, 0x0c, 0x02, //0x00001421 leaq         (%r10,%rax), %rcx
	//0x00001425 LBB0_244
	0x41, 0x0f, 0xb6, 0x12, //0x00001425 movzbl       (%r10), %edx
	0x80, 0xfa, 0x2c, //0x00001429 cmpb         $44, %dl
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x0000142c je           LBB0_249
	0x80, 0xfa, 0x7d, //0x00001432 cmpb         $125, %dl
	0x0f, 0x84, 0x18, 0x00, 0x00, 0x00, //0x00001435 je           LBB0_249
	0x80, 0xfa, 0x5d, //0x0000143b cmpb         $93, %dl
	0x0f, 0x84, 0x0f, 0x00, 0x00, 0x00, //0x0000143e je           LBB0_249
	0x49, 0xff, 0xc2, //0x00001444 incq         %r10
	0x48, 0xff, 0xc8, //0x00001447 decq         %rax
	0x0f, 0x85, 0xd5, 0xff, 0xff, 0xff, //0x0000144a jne          LBB0_244
	0x49, 0x89, 0xca, //0x00001450 movq         %rcx, %r10
	//0x00001453 LBB0_249
	0x4c, 0x2b, 0x14, 0x24, //0x00001453 subq         (%rsp), %r10
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x00001457 movq         $8(%rsp), %r14
	0x4d, 0x89, 0x16, //0x0000145c movq         %r10, (%r14)
	//0x0000145f LBB0_250
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x0000145f movq         $16(%rsp), %r15
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00001464 movq         $24(%rsp), %r11
	0xe9, 0xd8, 0x03, 0x00, 0x00, //0x00001469 jmp          LBB0_294
	0x90, 0x90, //0x0000146e .p2align 4, 0x90
	//0x00001470 LBB0_251
	0x48, 0x63, 0xc6, //0x00001470 movslq       %esi, %rax
	//0x00001473 LBB0_252
	0x48, 0x0f, 0xbc, 0xc0, //0x00001473 bsfq         %rax, %rax
	0x49, 0x29, 0xfa, //0x00001477 subq         %rdi, %r10
	0x49, 0x01, 0xc2, //0x0000147a addq         %rax, %r10
	0x4d, 0x89, 0x16, //0x0000147d movq         %r10, (%r14)
	0xe9, 0xc1, 0x03, 0x00, 0x00, //0x00001480 jmp          LBB0_294
	//0x00001485 LBB0_253
	0x48, 0x83, 0xc1, 0x04, //0x00001485 addq         $4, %rcx
	0x49, 0x3b, 0x0b, //0x00001489 cmpq         (%r11), %rcx
	0x0f, 0x86, 0xae, 0x03, 0x00, 0x00, //0x0000148c jbe          LBB0_293
	0xe9, 0xaf, 0x03, 0x00, 0x00, //0x00001492 jmp          LBB0_294
	//0x00001497 LBB0_254
	0x4d, 0x8b, 0x03, //0x00001497 movq         (%r11), %r8
	0x4c, 0x89, 0xc0, //0x0000149a movq         %r8, %rax
	0x4c, 0x29, 0xd0, //0x0000149d subq         %r10, %rax
	0x48, 0x83, 0xf8, 0x20, //0x000014a0 cmpq         $32, %rax
	0x0f, 0x8c, 0x28, 0x09, 0x00, 0x00, //0x000014a4 jl           LBB0_365
	0x48, 0x8b, 0x04, 0x24, //0x000014aa movq         (%rsp), %rax
	0x4c, 0x8d, 0x0c, 0x08, //0x000014ae leaq         (%rax,%rcx), %r9
	0x49, 0x29, 0xc8, //0x000014b2 subq         %rcx, %r8
	0xb9, 0x1f, 0x00, 0x00, 0x00, //0x000014b5 movl         $31, %ecx
	0x31, 0xc0, //0x000014ba xorl         %eax, %eax
	0x31, 0xdb, //0x000014bc xorl         %ebx, %ebx
	0x90, 0x90, //0x000014be .p2align 4, 0x90
	//0x000014c0 LBB0_256
	0xc4, 0xc1, 0x7e, 0x6f, 0x74, 0x01, 0x01, //0x000014c0 vmovdqu      $1(%r9,%rax), %ymm6
	0xc5, 0x4d, 0x74, 0xe1, //0x000014c7 vpcmpeqb     %ymm1, %ymm6, %ymm12
	0xc4, 0x41, 0x7d, 0xd7, 0xdc, //0x000014cb vpmovmskb    %ymm12, %r11d
	0xc5, 0xcd, 0x74, 0xf2, //0x000014d0 vpcmpeqb     %ymm2, %ymm6, %ymm6
	0xc5, 0xfd, 0xd7, 0xd6, //0x000014d4 vpmovmskb    %ymm6, %edx
	0x48, 0x85, 0xdb, //0x000014d8 testq        %rbx, %rbx
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x000014db jne          LBB0_259
	0x85, 0xd2, //0x000014e1 testl        %edx, %edx
	0x0f, 0x85, 0x07, 0x00, 0x00, 0x00, //0x000014e3 jne          LBB0_259
	0x31, 0xdb, //0x000014e9 xorl         %ebx, %ebx
	0xe9, 0x3b, 0x00, 0x00, 0x00, //0x000014eb jmp          LBB0_260
	//0x000014f0 .p2align 4, 0x90
	//0x000014f0 LBB0_259
	0x89, 0xdf, //0x000014f0 movl         %ebx, %edi
	0x41, 0xbc, 0xff, 0xff, 0xff, 0xff, //0x000014f2 movl         $4294967295, %r12d
	0x44, 0x31, 0xe7, //0x000014f8 xorl         %r12d, %edi
	0x21, 0xd7, //0x000014fb andl         %edx, %edi
	0x8d, 0x14, 0x3f, //0x000014fd leal         (%rdi,%rdi), %edx
	0x09, 0xda, //0x00001500 orl          %ebx, %edx
	0x41, 0x8d, 0xb4, 0x24, 0xab, 0xaa, 0xaa, 0xaa, //0x00001502 leal         $-1431655765(%r12), %esi
	0x31, 0xd6, //0x0000150a xorl         %edx, %esi
	0x21, 0xfe, //0x0000150c andl         %edi, %esi
	0x81, 0xe6, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000150e andl         $-1431655766, %esi
	0x31, 0xdb, //0x00001514 xorl         %ebx, %ebx
	0x01, 0xfe, //0x00001516 addl         %edi, %esi
	0x0f, 0x92, 0xc3, //0x00001518 setb         %bl
	0x01, 0xf6, //0x0000151b addl         %esi, %esi
	0x81, 0xf6, 0x55, 0x55, 0x55, 0x55, //0x0000151d xorl         $1431655765, %esi
	0x21, 0xd6, //0x00001523 andl         %edx, %esi
	0x44, 0x31, 0xe6, //0x00001525 xorl         %r12d, %esi
	0x41, 0x21, 0xf3, //0x00001528 andl         %esi, %r11d
	//0x0000152b LBB0_260
	0x45, 0x85, 0xdb, //0x0000152b testl        %r11d, %r11d
	0x0f, 0x85, 0xcc, 0x07, 0x00, 0x00, //0x0000152e jne          LBB0_354
	0x48, 0x83, 0xc0, 0x20, //0x00001534 addq         $32, %rax
	0x49, 0x8d, 0x54, 0x08, 0xe0, //0x00001538 leaq         $-32(%r8,%rcx), %rdx
	0x48, 0x83, 0xc1, 0xe0, //0x0000153d addq         $-32, %rcx
	0x48, 0x83, 0xfa, 0x3f, //0x00001541 cmpq         $63, %rdx
	0x0f, 0x8f, 0x75, 0xff, 0xff, 0xff, //0x00001545 jg           LBB0_256
	0x48, 0x85, 0xdb, //0x0000154b testq        %rbx, %rbx
	0x0f, 0x85, 0x01, 0x0a, 0x00, 0x00, //0x0000154e jne          LBB0_384
	0x4a, 0x8d, 0x4c, 0x08, 0x01, //0x00001554 leaq         $1(%rax,%r9), %rcx
	0x48, 0xf7, 0xd0, //0x00001559 notq         %rax
	0x4c, 0x01, 0xc0, //0x0000155c addq         %r8, %rax
	//0x0000155f LBB0_264
	0x48, 0x85, 0xc0, //0x0000155f testq        %rax, %rax
	0x48, 0x8b, 0x3c, 0x24, //0x00001562 movq         (%rsp), %rdi
	0x0f, 0x8e, 0xdf, 0x09, 0x00, 0x00, //0x00001566 jle          LBB0_383
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x0000156c movq         $24(%rsp), %r11
	0xe9, 0xc4, 0x07, 0x00, 0x00, //0x00001571 jmp          LBB0_356
	//0x00001576 LBB0_266
	0x4d, 0x8b, 0x1b, //0x00001576 movq         (%r11), %r11
	0x4d, 0x29, 0xd3, //0x00001579 subq         %r10, %r11
	0x4c, 0x01, 0x14, 0x24, //0x0000157c addq         %r10, (%rsp)
	0x45, 0x31, 0xc0, //0x00001580 xorl         %r8d, %r8d
	0x45, 0x31, 0xf6, //0x00001583 xorl         %r14d, %r14d
	0x45, 0x31, 0xff, //0x00001586 xorl         %r15d, %r15d
	0x31, 0xd2, //0x00001589 xorl         %edx, %edx
	0x49, 0x83, 0xfb, 0x40, //0x0000158b cmpq         $64, %r11
	0x0f, 0x8d, 0x3c, 0x01, 0x00, 0x00, //0x0000158f jge          LBB0_267
	//0x00001595 LBB0_276
	0x4d, 0x85, 0xdb, //0x00001595 testq        %r11, %r11
	0x0f, 0x8e, 0x9b, 0x09, 0x00, 0x00, //0x00001598 jle          LBB0_382
	0xc5, 0xc9, 0xef, 0xf6, //0x0000159e vpxor        %xmm6, %xmm6, %xmm6
	0xc5, 0xfe, 0x7f, 0x74, 0x24, 0x60, //0x000015a2 vmovdqu      %ymm6, $96(%rsp)
	0xc5, 0xfe, 0x7f, 0x74, 0x24, 0x40, //0x000015a8 vmovdqu      %ymm6, $64(%rsp)
	0x48, 0x8b, 0x04, 0x24, //0x000015ae movq         (%rsp), %rax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x000015b2 andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x000015b7 cmpl         $4033, %eax
	0x0f, 0x82, 0x0f, 0x01, 0x00, 0x00, //0x000015bc jb           LBB0_267
	0x49, 0x83, 0xfb, 0x20, //0x000015c2 cmpq         $32, %r11
	0x0f, 0x82, 0x24, 0x00, 0x00, 0x00, //0x000015c6 jb           LBB0_280
	0x48, 0x8b, 0x04, 0x24, //0x000015cc movq         (%rsp), %rax
	0xc5, 0xfe, 0x6f, 0x30, //0x000015d0 vmovdqu      (%rax), %ymm6
	0xc5, 0xfe, 0x7f, 0x74, 0x24, 0x40, //0x000015d4 vmovdqu      %ymm6, $64(%rsp)
	0x48, 0x83, 0xc0, 0x20, //0x000015da addq         $32, %rax
	0x48, 0x89, 0x04, 0x24, //0x000015de movq         %rax, (%rsp)
	0x4d, 0x8d, 0x53, 0xe0, //0x000015e2 leaq         $-32(%r11), %r10
	0x4c, 0x8d, 0x4c, 0x24, 0x60, //0x000015e6 leaq         $96(%rsp), %r9
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x000015eb jmp          LBB0_281
	//0x000015f0 LBB0_280
	0x4c, 0x8d, 0x4c, 0x24, 0x40, //0x000015f0 leaq         $64(%rsp), %r9
	0x4d, 0x89, 0xda, //0x000015f5 movq         %r11, %r10
	//0x000015f8 LBB0_281
	0x49, 0x83, 0xfa, 0x10, //0x000015f8 cmpq         $16, %r10
	0x0f, 0x82, 0x5d, 0x00, 0x00, 0x00, //0x000015fc jb           LBB0_282
	0x48, 0x8b, 0x04, 0x24, //0x00001602 movq         (%rsp), %rax
	0xc5, 0xfa, 0x6f, 0x30, //0x00001606 vmovdqu      (%rax), %xmm6
	0xc4, 0xc1, 0x7a, 0x7f, 0x31, //0x0000160a vmovdqu      %xmm6, (%r9)
	0x48, 0x83, 0xc0, 0x10, //0x0000160f addq         $16, %rax
	0x48, 0x89, 0x04, 0x24, //0x00001613 movq         %rax, (%rsp)
	0x49, 0x83, 0xc1, 0x10, //0x00001617 addq         $16, %r9
	0x49, 0x83, 0xc2, 0xf0, //0x0000161b addq         $-16, %r10
	0x49, 0x83, 0xfa, 0x08, //0x0000161f cmpq         $8, %r10
	0x0f, 0x83, 0x40, 0x00, 0x00, 0x00, //0x00001623 jae          LBB0_287
	//0x00001629 LBB0_283
	0x49, 0x83, 0xfa, 0x04, //0x00001629 cmpq         $4, %r10
	0x0f, 0x8c, 0x5a, 0x00, 0x00, 0x00, //0x0000162d jl           LBB0_284
	//0x00001633 LBB0_288
	0x48, 0x8b, 0x0c, 0x24, //0x00001633 movq         (%rsp), %rcx
	0x8b, 0x01, //0x00001637 movl         (%rcx), %eax
	0x41, 0x89, 0x01, //0x00001639 movl         %eax, (%r9)
	0x48, 0x83, 0xc1, 0x04, //0x0000163c addq         $4, %rcx
	0x48, 0x89, 0x0c, 0x24, //0x00001640 movq         %rcx, (%rsp)
	0x49, 0x83, 0xc1, 0x04, //0x00001644 addq         $4, %r9
	0x49, 0x83, 0xc2, 0xfc, //0x00001648 addq         $-4, %r10
	0x49, 0x83, 0xfa, 0x02, //0x0000164c cmpq         $2, %r10
	0x0f, 0x83, 0x41, 0x00, 0x00, 0x00, //0x00001650 jae          LBB0_285
	//0x00001656 LBB0_289
	0x48, 0x8b, 0x04, 0x24, //0x00001656 movq         (%rsp), %rax
	0xe9, 0x52, 0x00, 0x00, 0x00, //0x0000165a jmp          LBB0_290
	//0x0000165f LBB0_282
	0x49, 0x83, 0xfa, 0x08, //0x0000165f cmpq         $8, %r10
	0x0f, 0x82, 0xc0, 0xff, 0xff, 0xff, //0x00001663 jb           LBB0_283
	//0x00001669 LBB0_287
	0x48, 0x8b, 0x0c, 0x24, //0x00001669 movq         (%rsp), %rcx
	0x48, 0x8b, 0x01, //0x0000166d movq         (%rcx), %rax
	0x49, 0x89, 0x01, //0x00001670 movq         %rax, (%r9)
	0x48, 0x83, 0xc1, 0x08, //0x00001673 addq         $8, %rcx
	0x48, 0x89, 0x0c, 0x24, //0x00001677 movq         %rcx, (%rsp)
	0x49, 0x83, 0xc1, 0x08, //0x0000167b addq         $8, %r9
	0x49, 0x83, 0xc2, 0xf8, //0x0000167f addq         $-8, %r10
	0x49, 0x83, 0xfa, 0x04, //0x00001683 cmpq         $4, %r10
	0x0f, 0x8d, 0xa6, 0xff, 0xff, 0xff, //0x00001687 jge          LBB0_288
	//0x0000168d LBB0_284
	0x49, 0x83, 0xfa, 0x02, //0x0000168d cmpq         $2, %r10
	0x0f, 0x82, 0xbf, 0xff, 0xff, 0xff, //0x00001691 jb           LBB0_289
	//0x00001697 LBB0_285
	0x48, 0x8b, 0x0c, 0x24, //0x00001697 movq         (%rsp), %rcx
	0x0f, 0xb7, 0x01, //0x0000169b movzwl       (%rcx), %eax
	0x66, 0x41, 0x89, 0x01, //0x0000169e movw         %ax, (%r9)
	0x48, 0x83, 0xc1, 0x02, //0x000016a2 addq         $2, %rcx
	0x49, 0x83, 0xc1, 0x02, //0x000016a6 addq         $2, %r9
	0x49, 0x83, 0xc2, 0xfe, //0x000016aa addq         $-2, %r10
	0x48, 0x89, 0xc8, //0x000016ae movq         %rcx, %rax
	//0x000016b1 LBB0_290
	0x48, 0x8d, 0x4c, 0x24, 0x40, //0x000016b1 leaq         $64(%rsp), %rcx
	0x48, 0x89, 0x0c, 0x24, //0x000016b6 movq         %rcx, (%rsp)
	0x4d, 0x85, 0xd2, //0x000016ba testq        %r10, %r10
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x000016bd je           LBB0_267
	0x8a, 0x00, //0x000016c3 movb         (%rax), %al
	0x41, 0x88, 0x01, //0x000016c5 movb         %al, (%r9)
	0x48, 0x8d, 0x44, 0x24, 0x40, //0x000016c8 leaq         $64(%rsp), %rax
	0x48, 0x89, 0x04, 0x24, //0x000016cd movq         %rax, (%rsp)
	//0x000016d1 LBB0_267
	0x48, 0x8b, 0x04, 0x24, //0x000016d1 movq         (%rsp), %rax
	0xc5, 0x7e, 0x6f, 0x38, //0x000016d5 vmovdqu      (%rax), %ymm15
	0xc5, 0x7e, 0x6f, 0x70, 0x20, //0x000016d9 vmovdqu      $32(%rax), %ymm14
	0xc5, 0x85, 0x74, 0xf1, //0x000016de vpcmpeqb     %ymm1, %ymm15, %ymm6
	0xc5, 0x7d, 0xd7, 0xce, //0x000016e2 vpmovmskb    %ymm6, %r9d
	0xc5, 0x8d, 0x74, 0xf1, //0x000016e6 vpcmpeqb     %ymm1, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x000016ea vpmovmskb    %ymm6, %eax
	0x48, 0xc1, 0xe0, 0x20, //0x000016ee shlq         $32, %rax
	0x49, 0x09, 0xc1, //0x000016f2 orq          %rax, %r9
	0xc5, 0x85, 0x74, 0xf2, //0x000016f5 vpcmpeqb     %ymm2, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xfe, //0x000016f9 vpmovmskb    %ymm6, %edi
	0xc5, 0x8d, 0x74, 0xf2, //0x000016fd vpcmpeqb     %ymm2, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x00001701 vpmovmskb    %ymm6, %eax
	0x48, 0xc1, 0xe0, 0x20, //0x00001705 shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x00001709 orq          %rax, %rdi
	0x48, 0x89, 0xf8, //0x0000170c movq         %rdi, %rax
	0x4c, 0x09, 0xf0, //0x0000170f orq          %r14, %rax
	0x0f, 0x84, 0x4b, 0x00, 0x00, 0x00, //0x00001712 je           LBB0_269
	0x4c, 0x89, 0xf0, //0x00001718 movq         %r14, %rax
	0x48, 0xf7, 0xd0, //0x0000171b notq         %rax
	0x48, 0x21, 0xf8, //0x0000171e andq         %rdi, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x00001721 leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xf1, //0x00001725 orq          %r14, %rcx
	0x48, 0x89, 0xcb, //0x00001728 movq         %rcx, %rbx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000172b movabsq      $-6148914691236517206, %rsi
	0x48, 0x31, 0xf3, //0x00001735 xorq         %rsi, %rbx
	0x48, 0x21, 0xf7, //0x00001738 andq         %rsi, %rdi
	0x48, 0x21, 0xdf, //0x0000173b andq         %rbx, %rdi
	0x45, 0x31, 0xf6, //0x0000173e xorl         %r14d, %r14d
	0x48, 0x01, 0xc7, //0x00001741 addq         %rax, %rdi
	0x41, 0x0f, 0x92, 0xc6, //0x00001744 setb         %r14b
	0x48, 0x01, 0xff, //0x00001748 addq         %rdi, %rdi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000174b movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc7, //0x00001755 xorq         %rax, %rdi
	0x48, 0x21, 0xcf, //0x00001758 andq         %rcx, %rdi
	0x48, 0xf7, 0xd7, //0x0000175b notq         %rdi
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x0000175e jmp          LBB0_270
	//0x00001763 LBB0_269
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00001763 movq         $-1, %rdi
	0x45, 0x31, 0xf6, //0x0000176a xorl         %r14d, %r14d
	//0x0000176d LBB0_270
	0x4c, 0x21, 0xcf, //0x0000176d andq         %r9, %rdi
	0xc4, 0xe1, 0xf9, 0x6e, 0xf7, //0x00001770 vmovq        %rdi, %xmm6
	0xc4, 0xc3, 0x49, 0x44, 0xf1, 0x00, //0x00001775 vpclmulqdq   $0, %xmm9, %xmm6, %xmm6
	0xc4, 0xe1, 0xf9, 0x7e, 0xf7, //0x0000177b vmovq        %xmm6, %rdi
	0x4c, 0x31, 0xc7, //0x00001780 xorq         %r8, %rdi
	0xc4, 0xc1, 0x05, 0x74, 0xf2, //0x00001783 vpcmpeqb     %ymm10, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xf6, //0x00001788 vpmovmskb    %ymm6, %esi
	0xc4, 0xc1, 0x0d, 0x74, 0xf2, //0x0000178c vpcmpeqb     %ymm10, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x00001791 vpmovmskb    %ymm6, %eax
	0x48, 0xc1, 0xe0, 0x20, //0x00001795 shlq         $32, %rax
	0x48, 0x09, 0xc6, //0x00001799 orq          %rax, %rsi
	0x48, 0x89, 0xf9, //0x0000179c movq         %rdi, %rcx
	0x48, 0xf7, 0xd1, //0x0000179f notq         %rcx
	0x48, 0x21, 0xce, //0x000017a2 andq         %rcx, %rsi
	0xc4, 0xc1, 0x05, 0x74, 0xf3, //0x000017a5 vpcmpeqb     %ymm11, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x000017aa vpmovmskb    %ymm6, %eax
	0xc4, 0xc1, 0x0d, 0x74, 0xf3, //0x000017ae vpcmpeqb     %ymm11, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xde, //0x000017b3 vpmovmskb    %ymm6, %ebx
	0x48, 0xc1, 0xe3, 0x20, //0x000017b7 shlq         $32, %rbx
	0x48, 0x09, 0xd8, //0x000017bb orq          %rbx, %rax
	0x48, 0x21, 0xc8, //0x000017be andq         %rcx, %rax
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x000017c1 je           LBB0_274
	0x4c, 0x8b, 0x04, 0x24, //0x000017c7 movq         (%rsp), %r8
	0x90, 0x90, 0x90, 0x90, 0x90, //0x000017cb .p2align 4, 0x90
	//0x000017d0 LBB0_272
	0x48, 0x8d, 0x58, 0xff, //0x000017d0 leaq         $-1(%rax), %rbx
	0x48, 0x89, 0xd9, //0x000017d4 movq         %rbx, %rcx
	0x48, 0x21, 0xf1, //0x000017d7 andq         %rsi, %rcx
	0xf3, 0x48, 0x0f, 0xb8, 0xc9, //0x000017da popcntq      %rcx, %rcx
	0x4c, 0x01, 0xf9, //0x000017df addq         %r15, %rcx
	0x48, 0x39, 0xd1, //0x000017e2 cmpq         %rdx, %rcx
	0x0f, 0x86, 0xdf, 0x04, 0x00, 0x00, //0x000017e5 jbe          LBB0_352
	0x48, 0xff, 0xc2, //0x000017eb incq         %rdx
	0x48, 0x21, 0xd8, //0x000017ee andq         %rbx, %rax
	0x0f, 0x85, 0xd9, 0xff, 0xff, 0xff, //0x000017f1 jne          LBB0_272
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x000017f7 jmp          LBB0_275
	//0x000017fc LBB0_274
	0x4c, 0x8b, 0x04, 0x24, //0x000017fc movq         (%rsp), %r8
	//0x00001800 LBB0_275
	0x48, 0xc1, 0xff, 0x3f, //0x00001800 sarq         $63, %rdi
	0xf3, 0x48, 0x0f, 0xb8, 0xc6, //0x00001804 popcntq      %rsi, %rax
	0x49, 0x01, 0xc7, //0x00001809 addq         %rax, %r15
	0x49, 0x83, 0xc0, 0x40, //0x0000180c addq         $64, %r8
	0x4c, 0x89, 0x04, 0x24, //0x00001810 movq         %r8, (%rsp)
	0x49, 0x83, 0xc3, 0xc0, //0x00001814 addq         $-64, %r11
	0x49, 0x89, 0xf8, //0x00001818 movq         %rdi, %r8
	0x49, 0x83, 0xfb, 0x40, //0x0000181b cmpq         $64, %r11
	0x0f, 0x8d, 0xac, 0xfe, 0xff, 0xff, //0x0000181f jge          LBB0_267
	0xe9, 0x6b, 0xfd, 0xff, 0xff, //0x00001825 jmp          LBB0_276
	//0x0000182a LBB0_292
	0x48, 0x83, 0xc1, 0x05, //0x0000182a addq         $5, %rcx
	0x49, 0x3b, 0x0b, //0x0000182e cmpq         (%r11), %rcx
	0x0f, 0x87, 0x0f, 0x00, 0x00, 0x00, //0x00001831 ja           LBB0_294
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001837 .p2align 4, 0x90
	//0x00001840 LBB0_293
	0x49, 0x89, 0x0e, //0x00001840 movq         %rcx, (%r14)
	0x49, 0x89, 0xca, //0x00001843 movq         %rcx, %r10
	//0x00001846 LBB0_294
	0x4d, 0x8b, 0x07, //0x00001846 movq         (%r15), %r8
	0x49, 0x8b, 0x47, 0x08, //0x00001849 movq         $8(%r15), %rax
	0x49, 0x39, 0xc2, //0x0000184d cmpq         %rax, %r10
	0x0f, 0x83, 0x2a, 0x00, 0x00, 0x00, //0x00001850 jae          LBB0_299
	0x43, 0x8a, 0x0c, 0x10, //0x00001856 movb         (%r8,%r10), %cl
	0x80, 0xf9, 0x0d, //0x0000185a cmpb         $13, %cl
	0x0f, 0x84, 0x1d, 0x00, 0x00, 0x00, //0x0000185d je           LBB0_299
	0x80, 0xf9, 0x20, //0x00001863 cmpb         $32, %cl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00001866 je           LBB0_299
	0x80, 0xc1, 0xf7, //0x0000186c addb         $-9, %cl
	0x80, 0xf9, 0x01, //0x0000186f cmpb         $1, %cl
	0x0f, 0x86, 0x08, 0x00, 0x00, 0x00, //0x00001872 jbe          LBB0_299
	0x4c, 0x89, 0xd1, //0x00001878 movq         %r10, %rcx
	0xe9, 0x78, 0x01, 0x00, 0x00, //0x0000187b jmp          LBB0_325
	//0x00001880 .p2align 4, 0x90
	//0x00001880 LBB0_299
	0x49, 0x8d, 0x4a, 0x01, //0x00001880 leaq         $1(%r10), %rcx
	0x48, 0x39, 0xc1, //0x00001884 cmpq         %rax, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00001887 jae          LBB0_303
	0x41, 0x8a, 0x14, 0x08, //0x0000188d movb         (%r8,%rcx), %dl
	0x80, 0xfa, 0x0d, //0x00001891 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001894 je           LBB0_303
	0x80, 0xfa, 0x20, //0x0000189a cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000189d je           LBB0_303
	0x80, 0xc2, 0xf7, //0x000018a3 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x000018a6 cmpb         $1, %dl
	0x0f, 0x87, 0x49, 0x01, 0x00, 0x00, //0x000018a9 ja           LBB0_325
	0x90, //0x000018af .p2align 4, 0x90
	//0x000018b0 LBB0_303
	0x49, 0x8d, 0x4a, 0x02, //0x000018b0 leaq         $2(%r10), %rcx
	0x48, 0x39, 0xc1, //0x000018b4 cmpq         %rax, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000018b7 jae          LBB0_307
	0x41, 0x8a, 0x14, 0x08, //0x000018bd movb         (%r8,%rcx), %dl
	0x80, 0xfa, 0x0d, //0x000018c1 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000018c4 je           LBB0_307
	0x80, 0xfa, 0x20, //0x000018ca cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000018cd je           LBB0_307
	0x80, 0xc2, 0xf7, //0x000018d3 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x000018d6 cmpb         $1, %dl
	0x0f, 0x87, 0x19, 0x01, 0x00, 0x00, //0x000018d9 ja           LBB0_325
	0x90, //0x000018df .p2align 4, 0x90
	//0x000018e0 LBB0_307
	0x49, 0x8d, 0x4a, 0x03, //0x000018e0 leaq         $3(%r10), %rcx
	0x48, 0x39, 0xc1, //0x000018e4 cmpq         %rax, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000018e7 jae          LBB0_311
	0x41, 0x8a, 0x14, 0x08, //0x000018ed movb         (%r8,%rcx), %dl
	0x80, 0xfa, 0x0d, //0x000018f1 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000018f4 je           LBB0_311
	0x80, 0xfa, 0x20, //0x000018fa cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000018fd je           LBB0_311
	0x80, 0xc2, 0xf7, //0x00001903 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00001906 cmpb         $1, %dl
	0x0f, 0x87, 0xe9, 0x00, 0x00, 0x00, //0x00001909 ja           LBB0_325
	0x90, //0x0000190f .p2align 4, 0x90
	//0x00001910 LBB0_311
	0x49, 0x8d, 0x4a, 0x04, //0x00001910 leaq         $4(%r10), %rcx
	0x48, 0x89, 0xc2, //0x00001914 movq         %rax, %rdx
	0x48, 0x29, 0xca, //0x00001917 subq         %rcx, %rdx
	0x0f, 0x86, 0xcd, 0x17, 0x00, 0x00, //0x0000191a jbe          LBB0_625
	0x4c, 0x01, 0xc1, //0x00001920 addq         %r8, %rcx
	0x48, 0x83, 0xfa, 0x20, //0x00001923 cmpq         $32, %rdx
	0x0f, 0x82, 0x55, 0x00, 0x00, 0x00, //0x00001927 jb           LBB0_317
	0x48, 0x89, 0xc6, //0x0000192d movq         %rax, %rsi
	0x4c, 0x29, 0xd6, //0x00001930 subq         %r10, %rsi
	0x48, 0x83, 0xc6, 0xdc, //0x00001933 addq         $-36, %rsi
	0x48, 0x89, 0xf7, //0x00001937 movq         %rsi, %rdi
	0x48, 0x83, 0xe7, 0xe0, //0x0000193a andq         $-32, %rdi
	0x4c, 0x01, 0xd7, //0x0000193e addq         %r10, %rdi
	0x49, 0x8d, 0x7c, 0x38, 0x24, //0x00001941 leaq         $36(%r8,%rdi), %rdi
	0x83, 0xe6, 0x1f, //0x00001946 andl         $31, %esi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001949 .p2align 4, 0x90
	//0x00001950 LBB0_314
	0xc5, 0xfe, 0x6f, 0x31, //0x00001950 vmovdqu      (%rcx), %ymm6
	0xc4, 0xe2, 0x7d, 0x00, 0xfe, //0x00001954 vpshufb      %ymm6, %ymm0, %ymm7
	0xc5, 0xcd, 0x74, 0xf7, //0x00001959 vpcmpeqb     %ymm7, %ymm6, %ymm6
	0xc5, 0xfd, 0xd7, 0xde, //0x0000195d vpmovmskb    %ymm6, %ebx
	0x83, 0xfb, 0xff, //0x00001961 cmpl         $-1, %ebx
	0x0f, 0x85, 0x76, 0x00, 0x00, 0x00, //0x00001964 jne          LBB0_324
	0x48, 0x83, 0xc1, 0x20, //0x0000196a addq         $32, %rcx
	0x48, 0x83, 0xc2, 0xe0, //0x0000196e addq         $-32, %rdx
	0x48, 0x83, 0xfa, 0x1f, //0x00001972 cmpq         $31, %rdx
	0x0f, 0x87, 0xd4, 0xff, 0xff, 0xff, //0x00001976 ja           LBB0_314
	0x48, 0x89, 0xf2, //0x0000197c movq         %rsi, %rdx
	0x48, 0x89, 0xf9, //0x0000197f movq         %rdi, %rcx
	//0x00001982 LBB0_317
	0x48, 0x85, 0xd2, //0x00001982 testq        %rdx, %rdx
	0x0f, 0x84, 0x3a, 0x00, 0x00, 0x00, //0x00001985 je           LBB0_323
	0x48, 0x8d, 0x34, 0x11, //0x0000198b leaq         (%rcx,%rdx), %rsi
	0x48, 0xff, 0xc1, //0x0000198f incq         %rcx
	//0x00001992 LBB0_319
	0x0f, 0xbe, 0x79, 0xff, //0x00001992 movsbl       $-1(%rcx), %edi
	0x83, 0xff, 0x20, //0x00001996 cmpl         $32, %edi
	0x0f, 0x87, 0xec, 0x03, 0x00, 0x00, //0x00001999 ja           LBB0_361
	0x48, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000199f movabsq      $4294977024, %rbx
	0x48, 0x0f, 0xa3, 0xfb, //0x000019a9 btq          %rdi, %rbx
	0x0f, 0x83, 0xd8, 0x03, 0x00, 0x00, //0x000019ad jae          LBB0_361
	0x48, 0xff, 0xca, //0x000019b3 decq         %rdx
	0x48, 0xff, 0xc1, //0x000019b6 incq         %rcx
	0x48, 0x85, 0xd2, //0x000019b9 testq        %rdx, %rdx
	0x0f, 0x85, 0xd0, 0xff, 0xff, 0xff, //0x000019bc jne          LBB0_319
	0x48, 0x89, 0xf1, //0x000019c2 movq         %rsi, %rcx
	//0x000019c5 LBB0_323
	0x4c, 0x29, 0xc1, //0x000019c5 subq         %r8, %rcx
	0x48, 0x39, 0xc1, //0x000019c8 cmpq         %rax, %rcx
	0x0f, 0x82, 0x27, 0x00, 0x00, 0x00, //0x000019cb jb           LBB0_325
	0xe9, 0x1b, 0x19, 0x00, 0x00, //0x000019d1 jmp          LBB0_657
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000019d6 .p2align 4, 0x90
	//0x000019e0 LBB0_324
	0x4c, 0x29, 0xc1, //0x000019e0 subq         %r8, %rcx
	0xf7, 0xd3, //0x000019e3 notl         %ebx
	0x48, 0x63, 0xd3, //0x000019e5 movslq       %ebx, %rdx
	0x48, 0x0f, 0xbc, 0xd2, //0x000019e8 bsfq         %rdx, %rdx
	0x48, 0x01, 0xd1, //0x000019ec addq         %rdx, %rcx
	0x48, 0x39, 0xc1, //0x000019ef cmpq         %rax, %rcx
	0x0f, 0x83, 0xf9, 0x18, 0x00, 0x00, //0x000019f2 jae          LBB0_657
	//0x000019f8 LBB0_325
	0x4c, 0x8d, 0x51, 0x01, //0x000019f8 leaq         $1(%rcx), %r10
	0x4d, 0x89, 0x16, //0x000019fc movq         %r10, (%r14)
	0x41, 0x8a, 0x04, 0x08, //0x000019ff movb         (%r8,%rcx), %al
	0x3c, 0x2c, //0x00001a03 cmpb         $44, %al
	0x0f, 0x84, 0x43, 0xeb, 0xff, 0xff, //0x00001a05 je           LBB0_39
	0xe9, 0x0a, 0x17, 0x00, 0x00, //0x00001a0b jmp          LBB0_630
	//0x00001a10 LBB0_326
	0x4d, 0x8b, 0x1b, //0x00001a10 movq         (%r11), %r11
	0x4d, 0x29, 0xd3, //0x00001a13 subq         %r10, %r11
	0x4c, 0x01, 0x14, 0x24, //0x00001a16 addq         %r10, (%rsp)
	0x45, 0x31, 0xc0, //0x00001a1a xorl         %r8d, %r8d
	0x45, 0x31, 0xf6, //0x00001a1d xorl         %r14d, %r14d
	0x45, 0x31, 0xff, //0x00001a20 xorl         %r15d, %r15d
	0x31, 0xd2, //0x00001a23 xorl         %edx, %edx
	0x49, 0x83, 0xfb, 0x40, //0x00001a25 cmpq         $64, %r11
	0x0f, 0x8d, 0x3c, 0x01, 0x00, 0x00, //0x00001a29 jge          LBB0_327
	//0x00001a2f LBB0_336
	0x4d, 0x85, 0xdb, //0x00001a2f testq        %r11, %r11
	0x0f, 0x8e, 0x01, 0x05, 0x00, 0x00, //0x00001a32 jle          LBB0_382
	0xc5, 0xc9, 0xef, 0xf6, //0x00001a38 vpxor        %xmm6, %xmm6, %xmm6
	0xc5, 0xfe, 0x7f, 0x74, 0x24, 0x60, //0x00001a3c vmovdqu      %ymm6, $96(%rsp)
	0xc5, 0xfe, 0x7f, 0x74, 0x24, 0x40, //0x00001a42 vmovdqu      %ymm6, $64(%rsp)
	0x48, 0x8b, 0x04, 0x24, //0x00001a48 movq         (%rsp), %rax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x00001a4c andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x00001a51 cmpl         $4033, %eax
	0x0f, 0x82, 0x0f, 0x01, 0x00, 0x00, //0x00001a56 jb           LBB0_327
	0x49, 0x83, 0xfb, 0x20, //0x00001a5c cmpq         $32, %r11
	0x0f, 0x82, 0x24, 0x00, 0x00, 0x00, //0x00001a60 jb           LBB0_340
	0x48, 0x8b, 0x04, 0x24, //0x00001a66 movq         (%rsp), %rax
	0xc5, 0xfe, 0x6f, 0x30, //0x00001a6a vmovdqu      (%rax), %ymm6
	0xc5, 0xfe, 0x7f, 0x74, 0x24, 0x40, //0x00001a6e vmovdqu      %ymm6, $64(%rsp)
	0x48, 0x83, 0xc0, 0x20, //0x00001a74 addq         $32, %rax
	0x48, 0x89, 0x04, 0x24, //0x00001a78 movq         %rax, (%rsp)
	0x4d, 0x8d, 0x53, 0xe0, //0x00001a7c leaq         $-32(%r11), %r10
	0x4c, 0x8d, 0x4c, 0x24, 0x60, //0x00001a80 leaq         $96(%rsp), %r9
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x00001a85 jmp          LBB0_341
	//0x00001a8a LBB0_340
	0x4c, 0x8d, 0x4c, 0x24, 0x40, //0x00001a8a leaq         $64(%rsp), %r9
	0x4d, 0x89, 0xda, //0x00001a8f movq         %r11, %r10
	//0x00001a92 LBB0_341
	0x49, 0x83, 0xfa, 0x10, //0x00001a92 cmpq         $16, %r10
	0x0f, 0x82, 0x5d, 0x00, 0x00, 0x00, //0x00001a96 jb           LBB0_342
	0x48, 0x8b, 0x04, 0x24, //0x00001a9c movq         (%rsp), %rax
	0xc5, 0xfa, 0x6f, 0x30, //0x00001aa0 vmovdqu      (%rax), %xmm6
	0xc4, 0xc1, 0x7a, 0x7f, 0x31, //0x00001aa4 vmovdqu      %xmm6, (%r9)
	0x48, 0x83, 0xc0, 0x10, //0x00001aa9 addq         $16, %rax
	0x48, 0x89, 0x04, 0x24, //0x00001aad movq         %rax, (%rsp)
	0x49, 0x83, 0xc1, 0x10, //0x00001ab1 addq         $16, %r9
	0x49, 0x83, 0xc2, 0xf0, //0x00001ab5 addq         $-16, %r10
	0x49, 0x83, 0xfa, 0x08, //0x00001ab9 cmpq         $8, %r10
	0x0f, 0x83, 0x40, 0x00, 0x00, 0x00, //0x00001abd jae          LBB0_347
	//0x00001ac3 LBB0_343
	0x49, 0x83, 0xfa, 0x04, //0x00001ac3 cmpq         $4, %r10
	0x0f, 0x8c, 0x5a, 0x00, 0x00, 0x00, //0x00001ac7 jl           LBB0_344
	//0x00001acd LBB0_348
	0x48, 0x8b, 0x0c, 0x24, //0x00001acd movq         (%rsp), %rcx
	0x8b, 0x01, //0x00001ad1 movl         (%rcx), %eax
	0x41, 0x89, 0x01, //0x00001ad3 movl         %eax, (%r9)
	0x48, 0x83, 0xc1, 0x04, //0x00001ad6 addq         $4, %rcx
	0x48, 0x89, 0x0c, 0x24, //0x00001ada movq         %rcx, (%rsp)
	0x49, 0x83, 0xc1, 0x04, //0x00001ade addq         $4, %r9
	0x49, 0x83, 0xc2, 0xfc, //0x00001ae2 addq         $-4, %r10
	0x49, 0x83, 0xfa, 0x02, //0x00001ae6 cmpq         $2, %r10
	0x0f, 0x83, 0x41, 0x00, 0x00, 0x00, //0x00001aea jae          LBB0_345
	//0x00001af0 LBB0_349
	0x48, 0x8b, 0x04, 0x24, //0x00001af0 movq         (%rsp), %rax
	0xe9, 0x52, 0x00, 0x00, 0x00, //0x00001af4 jmp          LBB0_350
	//0x00001af9 LBB0_342
	0x49, 0x83, 0xfa, 0x08, //0x00001af9 cmpq         $8, %r10
	0x0f, 0x82, 0xc0, 0xff, 0xff, 0xff, //0x00001afd jb           LBB0_343
	//0x00001b03 LBB0_347
	0x48, 0x8b, 0x0c, 0x24, //0x00001b03 movq         (%rsp), %rcx
	0x48, 0x8b, 0x01, //0x00001b07 movq         (%rcx), %rax
	0x49, 0x89, 0x01, //0x00001b0a movq         %rax, (%r9)
	0x48, 0x83, 0xc1, 0x08, //0x00001b0d addq         $8, %rcx
	0x48, 0x89, 0x0c, 0x24, //0x00001b11 movq         %rcx, (%rsp)
	0x49, 0x83, 0xc1, 0x08, //0x00001b15 addq         $8, %r9
	0x49, 0x83, 0xc2, 0xf8, //0x00001b19 addq         $-8, %r10
	0x49, 0x83, 0xfa, 0x04, //0x00001b1d cmpq         $4, %r10
	0x0f, 0x8d, 0xa6, 0xff, 0xff, 0xff, //0x00001b21 jge          LBB0_348
	//0x00001b27 LBB0_344
	0x49, 0x83, 0xfa, 0x02, //0x00001b27 cmpq         $2, %r10
	0x0f, 0x82, 0xbf, 0xff, 0xff, 0xff, //0x00001b2b jb           LBB0_349
	//0x00001b31 LBB0_345
	0x48, 0x8b, 0x0c, 0x24, //0x00001b31 movq         (%rsp), %rcx
	0x0f, 0xb7, 0x01, //0x00001b35 movzwl       (%rcx), %eax
	0x66, 0x41, 0x89, 0x01, //0x00001b38 movw         %ax, (%r9)
	0x48, 0x83, 0xc1, 0x02, //0x00001b3c addq         $2, %rcx
	0x49, 0x83, 0xc1, 0x02, //0x00001b40 addq         $2, %r9
	0x49, 0x83, 0xc2, 0xfe, //0x00001b44 addq         $-2, %r10
	0x48, 0x89, 0xc8, //0x00001b48 movq         %rcx, %rax
	//0x00001b4b LBB0_350
	0x48, 0x8d, 0x4c, 0x24, 0x40, //0x00001b4b leaq         $64(%rsp), %rcx
	0x48, 0x89, 0x0c, 0x24, //0x00001b50 movq         %rcx, (%rsp)
	0x4d, 0x85, 0xd2, //0x00001b54 testq        %r10, %r10
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00001b57 je           LBB0_327
	0x8a, 0x00, //0x00001b5d movb         (%rax), %al
	0x41, 0x88, 0x01, //0x00001b5f movb         %al, (%r9)
	0x48, 0x8d, 0x44, 0x24, 0x40, //0x00001b62 leaq         $64(%rsp), %rax
	0x48, 0x89, 0x04, 0x24, //0x00001b67 movq         %rax, (%rsp)
	//0x00001b6b LBB0_327
	0x48, 0x8b, 0x04, 0x24, //0x00001b6b movq         (%rsp), %rax
	0xc5, 0x7e, 0x6f, 0x38, //0x00001b6f vmovdqu      (%rax), %ymm15
	0xc5, 0x7e, 0x6f, 0x70, 0x20, //0x00001b73 vmovdqu      $32(%rax), %ymm14
	0xc5, 0x85, 0x74, 0xf1, //0x00001b78 vpcmpeqb     %ymm1, %ymm15, %ymm6
	0xc5, 0x7d, 0xd7, 0xce, //0x00001b7c vpmovmskb    %ymm6, %r9d
	0xc5, 0x8d, 0x74, 0xf1, //0x00001b80 vpcmpeqb     %ymm1, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x00001b84 vpmovmskb    %ymm6, %eax
	0x48, 0xc1, 0xe0, 0x20, //0x00001b88 shlq         $32, %rax
	0x49, 0x09, 0xc1, //0x00001b8c orq          %rax, %r9
	0xc5, 0x85, 0x74, 0xf2, //0x00001b8f vpcmpeqb     %ymm2, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xfe, //0x00001b93 vpmovmskb    %ymm6, %edi
	0xc5, 0x8d, 0x74, 0xf2, //0x00001b97 vpcmpeqb     %ymm2, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x00001b9b vpmovmskb    %ymm6, %eax
	0x48, 0xc1, 0xe0, 0x20, //0x00001b9f shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x00001ba3 orq          %rax, %rdi
	0x48, 0x89, 0xf8, //0x00001ba6 movq         %rdi, %rax
	0x4c, 0x09, 0xf0, //0x00001ba9 orq          %r14, %rax
	0x0f, 0x84, 0x4b, 0x00, 0x00, 0x00, //0x00001bac je           LBB0_329
	0x4c, 0x89, 0xf0, //0x00001bb2 movq         %r14, %rax
	0x48, 0xf7, 0xd0, //0x00001bb5 notq         %rax
	0x48, 0x21, 0xf8, //0x00001bb8 andq         %rdi, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x00001bbb leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xf1, //0x00001bbf orq          %r14, %rcx
	0x48, 0x89, 0xcb, //0x00001bc2 movq         %rcx, %rbx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001bc5 movabsq      $-6148914691236517206, %rsi
	0x48, 0x31, 0xf3, //0x00001bcf xorq         %rsi, %rbx
	0x48, 0x21, 0xf7, //0x00001bd2 andq         %rsi, %rdi
	0x48, 0x21, 0xdf, //0x00001bd5 andq         %rbx, %rdi
	0x45, 0x31, 0xf6, //0x00001bd8 xorl         %r14d, %r14d
	0x48, 0x01, 0xc7, //0x00001bdb addq         %rax, %rdi
	0x41, 0x0f, 0x92, 0xc6, //0x00001bde setb         %r14b
	0x48, 0x01, 0xff, //0x00001be2 addq         %rdi, %rdi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00001be5 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc7, //0x00001bef xorq         %rax, %rdi
	0x48, 0x21, 0xcf, //0x00001bf2 andq         %rcx, %rdi
	0x48, 0xf7, 0xd7, //0x00001bf5 notq         %rdi
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00001bf8 jmp          LBB0_330
	//0x00001bfd LBB0_329
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00001bfd movq         $-1, %rdi
	0x45, 0x31, 0xf6, //0x00001c04 xorl         %r14d, %r14d
	//0x00001c07 LBB0_330
	0x4c, 0x21, 0xcf, //0x00001c07 andq         %r9, %rdi
	0xc4, 0xe1, 0xf9, 0x6e, 0xf7, //0x00001c0a vmovq        %rdi, %xmm6
	0xc4, 0xc3, 0x49, 0x44, 0xf1, 0x00, //0x00001c0f vpclmulqdq   $0, %xmm9, %xmm6, %xmm6
	0xc4, 0xe1, 0xf9, 0x7e, 0xf7, //0x00001c15 vmovq        %xmm6, %rdi
	0x4c, 0x31, 0xc7, //0x00001c1a xorq         %r8, %rdi
	0xc4, 0xc1, 0x05, 0x74, 0xf5, //0x00001c1d vpcmpeqb     %ymm13, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xf6, //0x00001c22 vpmovmskb    %ymm6, %esi
	0xc4, 0xc1, 0x0d, 0x74, 0xf5, //0x00001c26 vpcmpeqb     %ymm13, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x00001c2b vpmovmskb    %ymm6, %eax
	0x48, 0xc1, 0xe0, 0x20, //0x00001c2f shlq         $32, %rax
	0x48, 0x09, 0xc6, //0x00001c33 orq          %rax, %rsi
	0x48, 0x89, 0xf9, //0x00001c36 movq         %rdi, %rcx
	0x48, 0xf7, 0xd1, //0x00001c39 notq         %rcx
	0x48, 0x21, 0xce, //0x00001c3c andq         %rcx, %rsi
	0xc5, 0x85, 0x74, 0xf5, //0x00001c3f vpcmpeqb     %ymm5, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x00001c43 vpmovmskb    %ymm6, %eax
	0xc5, 0x8d, 0x74, 0xf5, //0x00001c47 vpcmpeqb     %ymm5, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xde, //0x00001c4b vpmovmskb    %ymm6, %ebx
	0x48, 0xc1, 0xe3, 0x20, //0x00001c4f shlq         $32, %rbx
	0x48, 0x09, 0xd8, //0x00001c53 orq          %rbx, %rax
	0x48, 0x21, 0xc8, //0x00001c56 andq         %rcx, %rax
	0x0f, 0x84, 0x3d, 0x00, 0x00, 0x00, //0x00001c59 je           LBB0_334
	0x4c, 0x8b, 0x04, 0x24, //0x00001c5f movq         (%rsp), %r8
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001c63 .p2align 4, 0x90
	//0x00001c70 LBB0_332
	0x48, 0x8d, 0x58, 0xff, //0x00001c70 leaq         $-1(%rax), %rbx
	0x48, 0x89, 0xd9, //0x00001c74 movq         %rbx, %rcx
	0x48, 0x21, 0xf1, //0x00001c77 andq         %rsi, %rcx
	0xf3, 0x48, 0x0f, 0xb8, 0xc9, //0x00001c7a popcntq      %rcx, %rcx
	0x4c, 0x01, 0xf9, //0x00001c7f addq         %r15, %rcx
	0x48, 0x39, 0xd1, //0x00001c82 cmpq         %rdx, %rcx
	0x0f, 0x86, 0x3f, 0x00, 0x00, 0x00, //0x00001c85 jbe          LBB0_352
	0x48, 0xff, 0xc2, //0x00001c8b incq         %rdx
	0x48, 0x21, 0xd8, //0x00001c8e andq         %rbx, %rax
	0x0f, 0x85, 0xd9, 0xff, 0xff, 0xff, //0x00001c91 jne          LBB0_332
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x00001c97 jmp          LBB0_335
	//0x00001c9c LBB0_334
	0x4c, 0x8b, 0x04, 0x24, //0x00001c9c movq         (%rsp), %r8
	//0x00001ca0 LBB0_335
	0x48, 0xc1, 0xff, 0x3f, //0x00001ca0 sarq         $63, %rdi
	0xf3, 0x48, 0x0f, 0xb8, 0xc6, //0x00001ca4 popcntq      %rsi, %rax
	0x49, 0x01, 0xc7, //0x00001ca9 addq         %rax, %r15
	0x49, 0x83, 0xc0, 0x40, //0x00001cac addq         $64, %r8
	0x4c, 0x89, 0x04, 0x24, //0x00001cb0 movq         %r8, (%rsp)
	0x49, 0x83, 0xc3, 0xc0, //0x00001cb4 addq         $-64, %r11
	0x49, 0x89, 0xf8, //0x00001cb8 movq         %rdi, %r8
	0x49, 0x83, 0xfb, 0x40, //0x00001cbb cmpq         $64, %r11
	0x0f, 0x8d, 0xa6, 0xfe, 0xff, 0xff, //0x00001cbf jge          LBB0_327
	0xe9, 0x65, 0xfd, 0xff, 0xff, //0x00001cc5 jmp          LBB0_336
	//0x00001cca LBB0_352
	0x48, 0x8b, 0x54, 0x24, 0x18, //0x00001cca movq         $24(%rsp), %rdx
	0x48, 0x8b, 0x0a, //0x00001ccf movq         (%rdx), %rcx
	0x48, 0x0f, 0xbc, 0xc0, //0x00001cd2 bsfq         %rax, %rax
	0x4c, 0x29, 0xd8, //0x00001cd6 subq         %r11, %rax
	0x49, 0x89, 0xd3, //0x00001cd9 movq         %rdx, %r11
	0x4c, 0x8d, 0x54, 0x08, 0x01, //0x00001cdc leaq         $1(%rax,%rcx), %r10
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x00001ce1 movq         $8(%rsp), %r14
	0x4d, 0x89, 0x16, //0x00001ce6 movq         %r10, (%r14)
	0x48, 0x8b, 0x02, //0x00001ce9 movq         (%rdx), %rax
	0x49, 0x39, 0xc2, //0x00001cec cmpq         %rax, %r10
	0x4c, 0x0f, 0x47, 0xd0, //0x00001cef cmovaq       %rax, %r10
	//0x00001cf3 LBB0_353
	0x4d, 0x89, 0x16, //0x00001cf3 movq         %r10, (%r14)
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x00001cf6 movq         $16(%rsp), %r15
	0xe9, 0x46, 0xfb, 0xff, 0xff, //0x00001cfb jmp          LBB0_294
	//0x00001d00 LBB0_354
	0x49, 0x0f, 0xbc, 0xcb, //0x00001d00 bsfq         %r11, %rcx
	0x49, 0x01, 0xc9, //0x00001d04 addq         %rcx, %r9
	0x49, 0x01, 0xc1, //0x00001d07 addq         %rax, %r9
	0x4c, 0x2b, 0x0c, 0x24, //0x00001d0a subq         (%rsp), %r9
	0x49, 0x83, 0xc1, 0x02, //0x00001d0e addq         $2, %r9
	0x4d, 0x89, 0x0e, //0x00001d12 movq         %r9, (%r14)
	0x4d, 0x89, 0xca, //0x00001d15 movq         %r9, %r10
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00001d18 movq         $24(%rsp), %r11
	0xe9, 0x24, 0xfb, 0xff, 0xff, //0x00001d1d jmp          LBB0_294
	//0x00001d22 LBB0_355
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x00001d22 movq         $-2, %rdx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x00001d29 movl         $2, %esi
	0x48, 0x01, 0xf1, //0x00001d2e addq         %rsi, %rcx
	0x48, 0x01, 0xd0, //0x00001d31 addq         %rdx, %rax
	0x0f, 0x8e, 0x0c, 0xfb, 0xff, 0xff, //0x00001d34 jle          LBB0_294
	//0x00001d3a LBB0_356
	0x0f, 0xb6, 0x11, //0x00001d3a movzbl       (%rcx), %edx
	0x80, 0xfa, 0x5c, //0x00001d3d cmpb         $92, %dl
	0x0f, 0x84, 0xdc, 0xff, 0xff, 0xff, //0x00001d40 je           LBB0_355
	0x80, 0xfa, 0x22, //0x00001d46 cmpb         $34, %dl
	0x0f, 0x84, 0x6b, 0x00, 0x00, 0x00, //0x00001d49 je           LBB0_363
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00001d4f movq         $-1, %rdx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00001d56 movl         $1, %esi
	0x48, 0x01, 0xf1, //0x00001d5b addq         %rsi, %rcx
	0x48, 0x01, 0xd0, //0x00001d5e addq         %rdx, %rax
	0x0f, 0x8f, 0xd3, 0xff, 0xff, 0xff, //0x00001d61 jg           LBB0_356
	0xe9, 0xda, 0xfa, 0xff, 0xff, //0x00001d67 jmp          LBB0_294
	//0x00001d6c LBB0_359
	0x0f, 0xb7, 0xc6, //0x00001d6c movzwl       %si, %eax
	0xe9, 0xff, 0xf6, 0xff, 0xff, //0x00001d6f jmp          LBB0_252
	//0x00001d74 LBB0_360
	0x4c, 0x89, 0xc1, //0x00001d74 movq         %r8, %rcx
	0x48, 0xf7, 0xd1, //0x00001d77 notq         %rcx
	0x48, 0x01, 0xc8, //0x00001d7a addq         %rcx, %rax
	0x48, 0x39, 0xd0, //0x00001d7d cmpq         %rdx, %rax
	0x0f, 0x82, 0x92, 0xf3, 0xff, 0xff, //0x00001d80 jb           LBB0_197
	0xe9, 0x66, 0x15, 0x00, 0x00, //0x00001d86 jmp          LBB0_657
	//0x00001d8b LBB0_361
	0x4c, 0x89, 0xc2, //0x00001d8b movq         %r8, %rdx
	0x48, 0xf7, 0xd2, //0x00001d8e notq         %rdx
	0x48, 0x01, 0xd1, //0x00001d91 addq         %rdx, %rcx
	0x48, 0x39, 0xc1, //0x00001d94 cmpq         %rax, %rcx
	0x0f, 0x82, 0x5b, 0xfc, 0xff, 0xff, //0x00001d97 jb           LBB0_325
	0xe9, 0x4f, 0x15, 0x00, 0x00, //0x00001d9d jmp          LBB0_657
	//0x00001da2 LBB0_362
	0x48, 0x8b, 0x04, 0x24, //0x00001da2 movq         (%rsp), %rax
	0x48, 0xf7, 0xd0, //0x00001da6 notq         %rax
	0x48, 0x01, 0xc1, //0x00001da9 addq         %rax, %rcx
	0x4c, 0x39, 0xc1, //0x00001dac cmpq         %r8, %rcx
	0x0f, 0x82, 0x64, 0xf5, 0xff, 0xff, //0x00001daf jb           LBB0_230
	0xe9, 0x8c, 0xfa, 0xff, 0xff, //0x00001db5 jmp          LBB0_294
	//0x00001dba LBB0_363
	0x48, 0x29, 0xf9, //0x00001dba subq         %rdi, %rcx
	0x48, 0xff, 0xc1, //0x00001dbd incq         %rcx
	0xe9, 0x7b, 0xfa, 0xff, 0xff, //0x00001dc0 jmp          LBB0_293
	//0x00001dc5 LBB0_364
	0x45, 0x31, 0xc9, //0x00001dc5 xorl         %r9d, %r9d
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x00001dc8 movq         $16(%rsp), %r15
	0xe9, 0xb7, 0xf1, 0xff, 0xff, //0x00001dcd jmp          LBB0_170
	//0x00001dd2 LBB0_365
	0x48, 0x8b, 0x0c, 0x24, //0x00001dd2 movq         (%rsp), %rcx
	0x4c, 0x01, 0xd1, //0x00001dd6 addq         %r10, %rcx
	0xe9, 0x81, 0xf7, 0xff, 0xff, //0x00001dd9 jmp          LBB0_264
	//0x00001dde LBB0_366
	0x49, 0x89, 0xfe, //0x00001dde movq         %rdi, %r14
	0x48, 0x83, 0x7c, 0x24, 0x38, 0xff, //0x00001de1 cmpq         $-1, $56(%rsp)
	0x0f, 0x85, 0x1b, 0x00, 0x00, 0x00, //0x00001de7 jne          LBB0_369
	0x4c, 0x89, 0xf7, //0x00001ded movq         %r14, %rdi
	0x48, 0x2b, 0x3c, 0x24, //0x00001df0 subq         (%rsp), %rdi
	0x4c, 0x0f, 0xbc, 0xfe, //0x00001df4 bsfq         %rsi, %r15
	0x49, 0x01, 0xff, //0x00001df8 addq         %rdi, %r15
	0x4c, 0x89, 0x7c, 0x24, 0x38, //0x00001dfb movq         %r15, $56(%rsp)
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x00001e00 jmp          LBB0_369
	//0x00001e05 LBB0_368
	0x49, 0x89, 0xfe, //0x00001e05 movq         %rdi, %r14
	//0x00001e08 LBB0_369
	0x41, 0x89, 0xdb, //0x00001e08 movl         %ebx, %r11d
	0x41, 0xf7, 0xd3, //0x00001e0b notl         %r11d
	0x41, 0x21, 0xf3, //0x00001e0e andl         %esi, %r11d
	0x47, 0x8d, 0x14, 0x1b, //0x00001e11 leal         (%r11,%r11), %r10d
	0x41, 0x09, 0xda, //0x00001e15 orl          %ebx, %r10d
	0x44, 0x89, 0xd7, //0x00001e18 movl         %r10d, %edi
	0xf7, 0xd7, //0x00001e1b notl         %edi
	0x21, 0xf7, //0x00001e1d andl         %esi, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001e1f andl         $-1431655766, %edi
	0x31, 0xdb, //0x00001e25 xorl         %ebx, %ebx
	0x44, 0x01, 0xdf, //0x00001e27 addl         %r11d, %edi
	0x0f, 0x92, 0xc3, //0x00001e2a setb         %bl
	0x01, 0xff, //0x00001e2d addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00001e2f xorl         $1431655765, %edi
	0x44, 0x21, 0xd7, //0x00001e35 andl         %r10d, %edi
	0xbe, 0xff, 0xff, 0xff, 0xff, //0x00001e38 movl         $4294967295, %esi
	0x31, 0xfe, //0x00001e3d xorl         %edi, %esi
	0x41, 0x21, 0xf0, //0x00001e3f andl         %esi, %r8d
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00001e42 movq         $24(%rsp), %r11
	0x4c, 0x89, 0xf7, //0x00001e47 movq         %r14, %rdi
	0x4d, 0x85, 0xc0, //0x00001e4a testq        %r8, %r8
	0x0f, 0x85, 0x59, 0xed, 0xff, 0xff, //0x00001e4d jne          LBB0_83
	//0x00001e53 LBB0_370
	0x48, 0x83, 0xc7, 0x20, //0x00001e53 addq         $32, %rdi
	0x48, 0x83, 0xc0, 0xe0, //0x00001e57 addq         $-32, %rax
	//0x00001e5b LBB0_371
	0x48, 0x85, 0xdb, //0x00001e5b testq        %rbx, %rbx
	0x4c, 0x8b, 0x04, 0x24, //0x00001e5e movq         (%rsp), %r8
	0x0f, 0x85, 0x8c, 0x00, 0x00, 0x00, //0x00001e62 jne          LBB0_380
	0x48, 0x85, 0xc0, //0x00001e68 testq        %rax, %rax
	0x0f, 0x84, 0xc9, 0x36, 0x00, 0x00, //0x00001e6b je           LBB0_1094
	//0x00001e71 LBB0_373
	0x4d, 0x89, 0xc6, //0x00001e71 movq         %r8, %r14
	0x49, 0xf7, 0xd6, //0x00001e74 notq         %r14
	//0x00001e77 LBB0_374
	0x4c, 0x8d, 0x57, 0x01, //0x00001e77 leaq         $1(%rdi), %r10
	0x0f, 0xb6, 0x1f, //0x00001e7b movzbl       (%rdi), %ebx
	0x80, 0xfb, 0x22, //0x00001e7e cmpb         $34, %bl
	0x0f, 0x84, 0x65, 0x00, 0x00, 0x00, //0x00001e81 je           LBB0_379
	0x48, 0x8d, 0x70, 0xff, //0x00001e87 leaq         $-1(%rax), %rsi
	0x80, 0xfb, 0x5c, //0x00001e8b cmpb         $92, %bl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00001e8e je           LBB0_377
	0x48, 0x89, 0xf0, //0x00001e94 movq         %rsi, %rax
	0x4c, 0x89, 0xd7, //0x00001e97 movq         %r10, %rdi
	0x48, 0x85, 0xf6, //0x00001e9a testq        %rsi, %rsi
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x00001e9d jne          LBB0_374
	0xe9, 0x92, 0x36, 0x00, 0x00, //0x00001ea3 jmp          LBB0_1094
	//0x00001ea8 LBB0_377
	0x48, 0x85, 0xf6, //0x00001ea8 testq        %rsi, %rsi
	0x0f, 0x84, 0x89, 0x36, 0x00, 0x00, //0x00001eab je           LBB0_1094
	0x4d, 0x01, 0xf2, //0x00001eb1 addq         %r14, %r10
	0x48, 0x8b, 0x74, 0x24, 0x38, //0x00001eb4 movq         $56(%rsp), %rsi
	0x48, 0x83, 0xfe, 0xff, //0x00001eb9 cmpq         $-1, %rsi
	0x4d, 0x0f, 0x44, 0xfa, //0x00001ebd cmoveq       %r10, %r15
	0x49, 0x0f, 0x44, 0xf2, //0x00001ec1 cmoveq       %r10, %rsi
	0x48, 0x89, 0x74, 0x24, 0x38, //0x00001ec5 movq         %rsi, $56(%rsp)
	0x48, 0x83, 0xc7, 0x02, //0x00001eca addq         $2, %rdi
	0x48, 0x83, 0xc0, 0xfe, //0x00001ece addq         $-2, %rax
	0x48, 0x89, 0xc6, //0x00001ed2 movq         %rax, %rsi
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00001ed5 movq         $24(%rsp), %r11
	0x4c, 0x8b, 0x04, 0x24, //0x00001eda movq         (%rsp), %r8
	0x48, 0x85, 0xf6, //0x00001ede testq        %rsi, %rsi
	0x0f, 0x85, 0x90, 0xff, 0xff, 0xff, //0x00001ee1 jne          LBB0_374
	0xe9, 0x4e, 0x36, 0x00, 0x00, //0x00001ee7 jmp          LBB0_1094
	//0x00001eec LBB0_379
	0x4d, 0x29, 0xc2, //0x00001eec subq         %r8, %r10
	0xe9, 0xa6, 0xe9, 0xff, 0xff, //0x00001eef jmp          LBB0_87
	//0x00001ef4 LBB0_380
	0x48, 0x85, 0xc0, //0x00001ef4 testq        %rax, %rax
	0x0f, 0x84, 0x3d, 0x36, 0x00, 0x00, //0x00001ef7 je           LBB0_1094
	0x4c, 0x8b, 0x04, 0x24, //0x00001efd movq         (%rsp), %r8
	0x4c, 0x89, 0xc6, //0x00001f01 movq         %r8, %rsi
	0x48, 0xf7, 0xd6, //0x00001f04 notq         %rsi
	0x48, 0x01, 0xfe, //0x00001f07 addq         %rdi, %rsi
	0x48, 0x8b, 0x5c, 0x24, 0x38, //0x00001f0a movq         $56(%rsp), %rbx
	0x48, 0x83, 0xfb, 0xff, //0x00001f0f cmpq         $-1, %rbx
	0x4c, 0x0f, 0x44, 0xfe, //0x00001f13 cmoveq       %rsi, %r15
	0x48, 0x0f, 0x44, 0xde, //0x00001f17 cmoveq       %rsi, %rbx
	0x48, 0x89, 0x5c, 0x24, 0x38, //0x00001f1b movq         %rbx, $56(%rsp)
	0x48, 0xff, 0xc7, //0x00001f20 incq         %rdi
	0x48, 0xff, 0xc8, //0x00001f23 decq         %rax
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00001f26 movq         $24(%rsp), %r11
	0x48, 0x85, 0xc0, //0x00001f2b testq        %rax, %rax
	0x0f, 0x85, 0x3d, 0xff, 0xff, 0xff, //0x00001f2e jne          LBB0_373
	0xe9, 0x01, 0x36, 0x00, 0x00, //0x00001f34 jmp          LBB0_1094
	//0x00001f39 LBB0_382
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00001f39 movq         $24(%rsp), %r11
	0x4d, 0x8b, 0x13, //0x00001f3e movq         (%r11), %r10
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x00001f41 movq         $8(%rsp), %r14
	0xe9, 0xa8, 0xfd, 0xff, 0xff, //0x00001f46 jmp          LBB0_353
	//0x00001f4b LBB0_383
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00001f4b movq         $24(%rsp), %r11
	0xe9, 0xf1, 0xf8, 0xff, 0xff, //0x00001f50 jmp          LBB0_294
	//0x00001f55 LBB0_384
	0x49, 0x8d, 0x48, 0xff, //0x00001f55 leaq         $-1(%r8), %rcx
	0x48, 0x39, 0xc1, //0x00001f59 cmpq         %rax, %rcx
	0x0f, 0x85, 0x0a, 0x00, 0x00, 0x00, //0x00001f5c jne          LBB0_386
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x00001f62 movq         $8(%rsp), %r14
	0xe9, 0xf3, 0xf4, 0xff, 0xff, //0x00001f67 jmp          LBB0_250
	//0x00001f6c LBB0_386
	0x4a, 0x8d, 0x4c, 0x08, 0x02, //0x00001f6c leaq         $2(%rax,%r9), %rcx
	0x49, 0x29, 0xc0, //0x00001f71 subq         %rax, %r8
	0x49, 0x83, 0xc0, 0xfe, //0x00001f74 addq         $-2, %r8
	0x4c, 0x89, 0xc0, //0x00001f78 movq         %r8, %rax
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x00001f7b movq         $8(%rsp), %r14
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x00001f80 movq         $16(%rsp), %r15
	0xe9, 0xd5, 0xf5, 0xff, 0xff, //0x00001f85 jmp          LBB0_264
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001f8a .p2align 4, 0x90
	//0x00001f90 LBB0_387
	0x80, 0xf9, 0x5b, //0x00001f90 cmpb         $91, %cl
	0x0f, 0x85, 0x58, 0x13, 0x00, 0x00, //0x00001f93 jne          LBB0_657
	0x49, 0x8b, 0x45, 0x08, //0x00001f99 movq         $8(%r13), %rax
	0x4c, 0x8b, 0x08, //0x00001f9d movq         (%rax), %r9
	0x4d, 0x85, 0xc9, //0x00001fa0 testq        %r9, %r9
	0x0f, 0x88, 0x5c, 0x11, 0x00, 0x00, //0x00001fa3 js           LBB0_628
	0x49, 0x8b, 0x03, //0x00001fa9 movq         (%r11), %rax
	0x49, 0x39, 0xc2, //0x00001fac cmpq         %rax, %r10
	0x0f, 0x83, 0x2b, 0x00, 0x00, 0x00, //0x00001faf jae          LBB0_394
	0x43, 0x8a, 0x0c, 0x10, //0x00001fb5 movb         (%r8,%r10), %cl
	0x80, 0xf9, 0x0d, //0x00001fb9 cmpb         $13, %cl
	0x0f, 0x84, 0x1e, 0x00, 0x00, 0x00, //0x00001fbc je           LBB0_394
	0x80, 0xf9, 0x20, //0x00001fc2 cmpb         $32, %cl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00001fc5 je           LBB0_394
	0x80, 0xc1, 0xf7, //0x00001fcb addb         $-9, %cl
	0x80, 0xf9, 0x01, //0x00001fce cmpb         $1, %cl
	0x0f, 0x86, 0x09, 0x00, 0x00, 0x00, //0x00001fd1 jbe          LBB0_394
	0x4c, 0x89, 0xd1, //0x00001fd7 movq         %r10, %rcx
	0xe9, 0x7a, 0x01, 0x00, 0x00, //0x00001fda jmp          LBB0_421
	0x90, //0x00001fdf .p2align 4, 0x90
	//0x00001fe0 LBB0_394
	0x49, 0x8d, 0x4a, 0x01, //0x00001fe0 leaq         $1(%r10), %rcx
	0x48, 0x39, 0xc1, //0x00001fe4 cmpq         %rax, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00001fe7 jae          LBB0_398
	0x41, 0x8a, 0x14, 0x08, //0x00001fed movb         (%r8,%rcx), %dl
	0x80, 0xfa, 0x0d, //0x00001ff1 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001ff4 je           LBB0_398
	0x80, 0xfa, 0x20, //0x00001ffa cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x00001ffd je           LBB0_398
	0x80, 0xc2, 0xf7, //0x00002003 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00002006 cmpb         $1, %dl
	0x0f, 0x87, 0x4a, 0x01, 0x00, 0x00, //0x00002009 ja           LBB0_421
	0x90, //0x0000200f .p2align 4, 0x90
	//0x00002010 LBB0_398
	0x49, 0x8d, 0x4a, 0x02, //0x00002010 leaq         $2(%r10), %rcx
	0x48, 0x39, 0xc1, //0x00002014 cmpq         %rax, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00002017 jae          LBB0_402
	0x41, 0x8a, 0x14, 0x08, //0x0000201d movb         (%r8,%rcx), %dl
	0x80, 0xfa, 0x0d, //0x00002021 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00002024 je           LBB0_402
	0x80, 0xfa, 0x20, //0x0000202a cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000202d je           LBB0_402
	0x80, 0xc2, 0xf7, //0x00002033 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00002036 cmpb         $1, %dl
	0x0f, 0x87, 0x1a, 0x01, 0x00, 0x00, //0x00002039 ja           LBB0_421
	0x90, //0x0000203f .p2align 4, 0x90
	//0x00002040 LBB0_402
	0x49, 0x8d, 0x4a, 0x03, //0x00002040 leaq         $3(%r10), %rcx
	0x48, 0x39, 0xc1, //0x00002044 cmpq         %rax, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00002047 jae          LBB0_406
	0x41, 0x8a, 0x14, 0x08, //0x0000204d movb         (%r8,%rcx), %dl
	0x80, 0xfa, 0x0d, //0x00002051 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00002054 je           LBB0_406
	0x80, 0xfa, 0x20, //0x0000205a cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000205d je           LBB0_406
	0x80, 0xc2, 0xf7, //0x00002063 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00002066 cmpb         $1, %dl
	0x0f, 0x87, 0xea, 0x00, 0x00, 0x00, //0x00002069 ja           LBB0_421
	0x90, //0x0000206f .p2align 4, 0x90
	//0x00002070 LBB0_406
	0x49, 0x8d, 0x4a, 0x04, //0x00002070 leaq         $4(%r10), %rcx
	0x48, 0x89, 0xc2, //0x00002074 movq         %rax, %rdx
	0x48, 0x29, 0xca, //0x00002077 subq         %rcx, %rdx
	0x0f, 0x86, 0xb6, 0x00, 0x00, 0x00, //0x0000207a jbe          LBB0_419
	0x4c, 0x01, 0xc1, //0x00002080 addq         %r8, %rcx
	0x48, 0x83, 0xfa, 0x20, //0x00002083 cmpq         $32, %rdx
	0x0f, 0x82, 0x55, 0x00, 0x00, 0x00, //0x00002087 jb           LBB0_412
	0x48, 0x89, 0xc6, //0x0000208d movq         %rax, %rsi
	0x4c, 0x29, 0xd6, //0x00002090 subq         %r10, %rsi
	0x48, 0x83, 0xc6, 0xdc, //0x00002093 addq         $-36, %rsi
	0x48, 0x89, 0xf7, //0x00002097 movq         %rsi, %rdi
	0x48, 0x83, 0xe7, 0xe0, //0x0000209a andq         $-32, %rdi
	0x4c, 0x01, 0xd7, //0x0000209e addq         %r10, %rdi
	0x49, 0x8d, 0x7c, 0x38, 0x24, //0x000020a1 leaq         $36(%r8,%rdi), %rdi
	0x83, 0xe6, 0x1f, //0x000020a6 andl         $31, %esi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000020a9 .p2align 4, 0x90
	//0x000020b0 LBB0_409
	0xc5, 0xfe, 0x6f, 0x31, //0x000020b0 vmovdqu      (%rcx), %ymm6
	0xc4, 0xe2, 0x7d, 0x00, 0xfe, //0x000020b4 vpshufb      %ymm6, %ymm0, %ymm7
	0xc5, 0xcd, 0x74, 0xf7, //0x000020b9 vpcmpeqb     %ymm7, %ymm6, %ymm6
	0xc5, 0xfd, 0xd7, 0xde, //0x000020bd vpmovmskb    %ymm6, %ebx
	0x83, 0xfb, 0xff, //0x000020c1 cmpl         $-1, %ebx
	0x0f, 0x85, 0x77, 0x00, 0x00, 0x00, //0x000020c4 jne          LBB0_420
	0x48, 0x83, 0xc1, 0x20, //0x000020ca addq         $32, %rcx
	0x48, 0x83, 0xc2, 0xe0, //0x000020ce addq         $-32, %rdx
	0x48, 0x83, 0xfa, 0x1f, //0x000020d2 cmpq         $31, %rdx
	0x0f, 0x87, 0xd4, 0xff, 0xff, 0xff, //0x000020d6 ja           LBB0_409
	0x48, 0x89, 0xf2, //0x000020dc movq         %rsi, %rdx
	0x48, 0x89, 0xf9, //0x000020df movq         %rdi, %rcx
	//0x000020e2 LBB0_412
	0x48, 0x85, 0xd2, //0x000020e2 testq        %rdx, %rdx
	0x0f, 0x84, 0x3a, 0x00, 0x00, 0x00, //0x000020e5 je           LBB0_418
	0x48, 0x8d, 0x34, 0x11, //0x000020eb leaq         (%rcx,%rdx), %rsi
	0x48, 0xff, 0xc1, //0x000020ef incq         %rcx
	//0x000020f2 LBB0_414
	0x0f, 0xbe, 0x79, 0xff, //0x000020f2 movsbl       $-1(%rcx), %edi
	0x83, 0xff, 0x20, //0x000020f6 cmpl         $32, %edi
	0x0f, 0x87, 0xbd, 0x0d, 0x00, 0x00, //0x000020f9 ja           LBB0_594
	0x48, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000020ff movabsq      $4294977024, %rbx
	0x48, 0x0f, 0xa3, 0xfb, //0x00002109 btq          %rdi, %rbx
	0x0f, 0x83, 0xa9, 0x0d, 0x00, 0x00, //0x0000210d jae          LBB0_594
	0x48, 0xff, 0xca, //0x00002113 decq         %rdx
	0x48, 0xff, 0xc1, //0x00002116 incq         %rcx
	0x48, 0x85, 0xd2, //0x00002119 testq        %rdx, %rdx
	0x0f, 0x85, 0xd0, 0xff, 0xff, 0xff, //0x0000211c jne          LBB0_414
	0x48, 0x89, 0xf1, //0x00002122 movq         %rsi, %rcx
	//0x00002125 LBB0_418
	0x4c, 0x29, 0xc1, //0x00002125 subq         %r8, %rcx
	0x48, 0x39, 0xc1, //0x00002128 cmpq         %rax, %rcx
	0x0f, 0x82, 0x28, 0x00, 0x00, 0x00, //0x0000212b jb           LBB0_421
	0xe9, 0x35, 0x00, 0x00, 0x00, //0x00002131 jmp          LBB0_422
	//0x00002136 LBB0_419
	0x49, 0x89, 0x0e, //0x00002136 movq         %rcx, (%r14)
	0x49, 0x89, 0xca, //0x00002139 movq         %rcx, %r10
	0xe9, 0x2a, 0x00, 0x00, 0x00, //0x0000213c jmp          LBB0_422
	//0x00002141 LBB0_420
	0x4c, 0x29, 0xc1, //0x00002141 subq         %r8, %rcx
	0xf7, 0xd3, //0x00002144 notl         %ebx
	0x48, 0x63, 0xd3, //0x00002146 movslq       %ebx, %rdx
	0x48, 0x0f, 0xbc, 0xd2, //0x00002149 bsfq         %rdx, %rdx
	0x48, 0x01, 0xd1, //0x0000214d addq         %rdx, %rcx
	0x48, 0x39, 0xc1, //0x00002150 cmpq         %rax, %rcx
	0x0f, 0x83, 0x12, 0x00, 0x00, 0x00, //0x00002153 jae          LBB0_422
	//0x00002159 LBB0_421
	0x4c, 0x8d, 0x51, 0x01, //0x00002159 leaq         $1(%rcx), %r10
	0x4d, 0x89, 0x16, //0x0000215d movq         %r10, (%r14)
	0x41, 0x80, 0x3c, 0x08, 0x5d, //0x00002160 cmpb         $93, (%r8,%rcx)
	0x0f, 0x84, 0xb7, 0x0f, 0x00, 0x00, //0x00002165 je           LBB0_631
	//0x0000216b LBB0_422
	0x49, 0xff, 0xca, //0x0000216b decq         %r10
	0x4d, 0x89, 0x16, //0x0000216e movq         %r10, (%r14)
	0x4d, 0x85, 0xc9, //0x00002171 testq        %r9, %r9
	0x0f, 0x8e, 0x06, 0x0d, 0x00, 0x00, //0x00002174 jle          LBB0_524
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000217a .p2align 4, 0x90
	//0x00002180 LBB0_423
	0x49, 0x8b, 0x0b, //0x00002180 movq         (%r11), %rcx
	0x49, 0x39, 0xca, //0x00002183 cmpq         %rcx, %r10
	0x0f, 0x83, 0x34, 0x00, 0x00, 0x00, //0x00002186 jae          LBB0_428
	0x43, 0x8a, 0x04, 0x10, //0x0000218c movb         (%r8,%r10), %al
	0x3c, 0x0d, //0x00002190 cmpb         $13, %al
	0x0f, 0x84, 0x28, 0x00, 0x00, 0x00, //0x00002192 je           LBB0_428
	0x3c, 0x20, //0x00002198 cmpb         $32, %al
	0x0f, 0x84, 0x20, 0x00, 0x00, 0x00, //0x0000219a je           LBB0_428
	0x04, 0xf7, //0x000021a0 addb         $-9, %al
	0x3c, 0x01, //0x000021a2 cmpb         $1, %al
	0x0f, 0x86, 0x16, 0x00, 0x00, 0x00, //0x000021a4 jbe          LBB0_428
	0x4c, 0x89, 0xd0, //0x000021aa movq         %r10, %rax
	0xe9, 0x86, 0x01, 0x00, 0x00, //0x000021ad jmp          LBB0_454
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000021b2 .p2align 4, 0x90
	//0x000021c0 LBB0_428
	0x49, 0x8d, 0x42, 0x01, //0x000021c0 leaq         $1(%r10), %rax
	0x48, 0x39, 0xc8, //0x000021c4 cmpq         %rcx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000021c7 jae          LBB0_432
	0x41, 0x8a, 0x14, 0x00, //0x000021cd movb         (%r8,%rax), %dl
	0x80, 0xfa, 0x0d, //0x000021d1 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000021d4 je           LBB0_432
	0x80, 0xfa, 0x20, //0x000021da cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000021dd je           LBB0_432
	0x80, 0xc2, 0xf7, //0x000021e3 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x000021e6 cmpb         $1, %dl
	0x0f, 0x87, 0x49, 0x01, 0x00, 0x00, //0x000021e9 ja           LBB0_454
	0x90, //0x000021ef .p2align 4, 0x90
	//0x000021f0 LBB0_432
	0x49, 0x8d, 0x42, 0x02, //0x000021f0 leaq         $2(%r10), %rax
	0x48, 0x39, 0xc8, //0x000021f4 cmpq         %rcx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000021f7 jae          LBB0_436
	0x41, 0x8a, 0x14, 0x00, //0x000021fd movb         (%r8,%rax), %dl
	0x80, 0xfa, 0x0d, //0x00002201 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00002204 je           LBB0_436
	0x80, 0xfa, 0x20, //0x0000220a cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000220d je           LBB0_436
	0x80, 0xc2, 0xf7, //0x00002213 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00002216 cmpb         $1, %dl
	0x0f, 0x87, 0x19, 0x01, 0x00, 0x00, //0x00002219 ja           LBB0_454
	0x90, //0x0000221f .p2align 4, 0x90
	//0x00002220 LBB0_436
	0x49, 0x8d, 0x42, 0x03, //0x00002220 leaq         $3(%r10), %rax
	0x48, 0x39, 0xc8, //0x00002224 cmpq         %rcx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00002227 jae          LBB0_440
	0x41, 0x8a, 0x14, 0x00, //0x0000222d movb         (%r8,%rax), %dl
	0x80, 0xfa, 0x0d, //0x00002231 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00002234 je           LBB0_440
	0x80, 0xfa, 0x20, //0x0000223a cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000223d je           LBB0_440
	0x80, 0xc2, 0xf7, //0x00002243 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00002246 cmpb         $1, %dl
	0x0f, 0x87, 0xe9, 0x00, 0x00, 0x00, //0x00002249 ja           LBB0_454
	0x90, //0x0000224f .p2align 4, 0x90
	//0x00002250 LBB0_440
	0x49, 0x8d, 0x42, 0x04, //0x00002250 leaq         $4(%r10), %rax
	0x48, 0x89, 0xca, //0x00002254 movq         %rcx, %rdx
	0x48, 0x29, 0xc2, //0x00002257 subq         %rax, %rdx
	0x0f, 0x86, 0x70, 0x03, 0x00, 0x00, //0x0000225a jbe          LBB0_490
	0x4c, 0x01, 0xc0, //0x00002260 addq         %r8, %rax
	0x48, 0x83, 0xfa, 0x20, //0x00002263 cmpq         $32, %rdx
	0x0f, 0x82, 0x55, 0x00, 0x00, 0x00, //0x00002267 jb           LBB0_446
	0x48, 0x89, 0xce, //0x0000226d movq         %rcx, %rsi
	0x4c, 0x29, 0xd6, //0x00002270 subq         %r10, %rsi
	0x48, 0x83, 0xc6, 0xdc, //0x00002273 addq         $-36, %rsi
	0x48, 0x89, 0xf7, //0x00002277 movq         %rsi, %rdi
	0x48, 0x83, 0xe7, 0xe0, //0x0000227a andq         $-32, %rdi
	0x4c, 0x01, 0xd7, //0x0000227e addq         %r10, %rdi
	0x49, 0x8d, 0x7c, 0x38, 0x24, //0x00002281 leaq         $36(%r8,%rdi), %rdi
	0x83, 0xe6, 0x1f, //0x00002286 andl         $31, %esi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002289 .p2align 4, 0x90
	//0x00002290 LBB0_443
	0xc5, 0xfe, 0x6f, 0x30, //0x00002290 vmovdqu      (%rax), %ymm6
	0xc4, 0xe2, 0x7d, 0x00, 0xfe, //0x00002294 vpshufb      %ymm6, %ymm0, %ymm7
	0xc5, 0xcd, 0x74, 0xf7, //0x00002299 vpcmpeqb     %ymm7, %ymm6, %ymm6
	0xc5, 0xfd, 0xd7, 0xde, //0x0000229d vpmovmskb    %ymm6, %ebx
	0x83, 0xfb, 0xff, //0x000022a1 cmpl         $-1, %ebx
	0x0f, 0x85, 0x76, 0x00, 0x00, 0x00, //0x000022a4 jne          LBB0_453
	0x48, 0x83, 0xc0, 0x20, //0x000022aa addq         $32, %rax
	0x48, 0x83, 0xc2, 0xe0, //0x000022ae addq         $-32, %rdx
	0x48, 0x83, 0xfa, 0x1f, //0x000022b2 cmpq         $31, %rdx
	0x0f, 0x87, 0xd4, 0xff, 0xff, 0xff, //0x000022b6 ja           LBB0_443
	0x48, 0x89, 0xf2, //0x000022bc movq         %rsi, %rdx
	0x48, 0x89, 0xf8, //0x000022bf movq         %rdi, %rax
	//0x000022c2 LBB0_446
	0x48, 0x85, 0xd2, //0x000022c2 testq        %rdx, %rdx
	0x0f, 0x84, 0x3a, 0x00, 0x00, 0x00, //0x000022c5 je           LBB0_452
	0x48, 0x8d, 0x34, 0x10, //0x000022cb leaq         (%rax,%rdx), %rsi
	0x48, 0xff, 0xc0, //0x000022cf incq         %rax
	//0x000022d2 LBB0_448
	0x0f, 0xbe, 0x78, 0xff, //0x000022d2 movsbl       $-1(%rax), %edi
	0x83, 0xff, 0x20, //0x000022d6 cmpl         $32, %edi
	0x0f, 0x87, 0x7e, 0x0a, 0x00, 0x00, //0x000022d9 ja           LBB0_584
	0x48, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000022df movabsq      $4294977024, %rbx
	0x48, 0x0f, 0xa3, 0xfb, //0x000022e9 btq          %rdi, %rbx
	0x0f, 0x83, 0x6a, 0x0a, 0x00, 0x00, //0x000022ed jae          LBB0_584
	0x48, 0xff, 0xca, //0x000022f3 decq         %rdx
	0x48, 0xff, 0xc0, //0x000022f6 incq         %rax
	0x48, 0x85, 0xd2, //0x000022f9 testq        %rdx, %rdx
	0x0f, 0x85, 0xd0, 0xff, 0xff, 0xff, //0x000022fc jne          LBB0_448
	0x48, 0x89, 0xf0, //0x00002302 movq         %rsi, %rax
	//0x00002305 LBB0_452
	0x4c, 0x29, 0xc0, //0x00002305 subq         %r8, %rax
	0x48, 0x39, 0xc8, //0x00002308 cmpq         %rcx, %rax
	0x0f, 0x82, 0x27, 0x00, 0x00, 0x00, //0x0000230b jb           LBB0_454
	0xe9, 0xc0, 0x02, 0x00, 0x00, //0x00002311 jmp          LBB0_491
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002316 .p2align 4, 0x90
	//0x00002320 LBB0_453
	0x4c, 0x29, 0xc0, //0x00002320 subq         %r8, %rax
	0xf7, 0xd3, //0x00002323 notl         %ebx
	0x48, 0x63, 0xd3, //0x00002325 movslq       %ebx, %rdx
	0x48, 0x0f, 0xbc, 0xd2, //0x00002328 bsfq         %rdx, %rdx
	0x48, 0x01, 0xd0, //0x0000232c addq         %rdx, %rax
	0x48, 0x39, 0xc8, //0x0000232f cmpq         %rcx, %rax
	0x0f, 0x83, 0x9e, 0x02, 0x00, 0x00, //0x00002332 jae          LBB0_491
	//0x00002338 LBB0_454
	0x4c, 0x8d, 0x50, 0x01, //0x00002338 leaq         $1(%rax), %r10
	0x4d, 0x89, 0x16, //0x0000233c movq         %r10, (%r14)
	0x41, 0x0f, 0xbe, 0x0c, 0x00, //0x0000233f movsbl       (%r8,%rax), %ecx
	0x83, 0xf9, 0x7b, //0x00002344 cmpl         $123, %ecx
	0x0f, 0x87, 0x83, 0x02, 0x00, 0x00, //0x00002347 ja           LBB0_490
	0x48, 0x8d, 0x15, 0x14, 0x34, 0x00, 0x00, //0x0000234d leaq         $13332(%rip), %rdx  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x0c, 0x8a, //0x00002354 movslq       (%rdx,%rcx,4), %rcx
	0x48, 0x01, 0xd1, //0x00002358 addq         %rdx, %rcx
	0xff, 0xe1, //0x0000235b jmpq         *%rcx
	//0x0000235d LBB0_456
	0x49, 0x8b, 0x13, //0x0000235d movq         (%r11), %rdx
	0x48, 0x89, 0xd1, //0x00002360 movq         %rdx, %rcx
	0x4c, 0x29, 0xd1, //0x00002363 subq         %r10, %rcx
	0x4d, 0x01, 0xc2, //0x00002366 addq         %r8, %r10
	0x48, 0x83, 0xf9, 0x20, //0x00002369 cmpq         $32, %rcx
	0x0f, 0x82, 0x56, 0x00, 0x00, 0x00, //0x0000236d jb           LBB0_461
	0x48, 0x29, 0xc2, //0x00002373 subq         %rax, %rdx
	0x48, 0x83, 0xc2, 0xdf, //0x00002376 addq         $-33, %rdx
	0x48, 0x89, 0xd6, //0x0000237a movq         %rdx, %rsi
	0x48, 0x83, 0xe6, 0xe0, //0x0000237d andq         $-32, %rsi
	0x48, 0x01, 0xc6, //0x00002381 addq         %rax, %rsi
	0x49, 0x8d, 0x44, 0x30, 0x21, //0x00002384 leaq         $33(%r8,%rsi), %rax
	0x83, 0xe2, 0x1f, //0x00002389 andl         $31, %edx
	0x90, 0x90, 0x90, 0x90, //0x0000238c .p2align 4, 0x90
	//0x00002390 LBB0_458
	0xc4, 0xc1, 0x7e, 0x6f, 0x32, //0x00002390 vmovdqu      (%r10), %ymm6
	0xc5, 0xcd, 0x74, 0xfb, //0x00002395 vpcmpeqb     %ymm3, %ymm6, %ymm7
	0xc5, 0xcd, 0xeb, 0xf4, //0x00002399 vpor         %ymm4, %ymm6, %ymm6
	0xc5, 0xcd, 0x74, 0xf5, //0x0000239d vpcmpeqb     %ymm5, %ymm6, %ymm6
	0xc5, 0xcd, 0xeb, 0xf7, //0x000023a1 vpor         %ymm7, %ymm6, %ymm6
	0xc5, 0xfd, 0xd7, 0xf6, //0x000023a5 vpmovmskb    %ymm6, %esi
	0x85, 0xf6, //0x000023a9 testl        %esi, %esi
	0x0f, 0x85, 0xdf, 0x00, 0x00, 0x00, //0x000023ab jne          LBB0_474
	0x49, 0x83, 0xc2, 0x20, //0x000023b1 addq         $32, %r10
	0x48, 0x83, 0xc1, 0xe0, //0x000023b5 addq         $-32, %rcx
	0x48, 0x83, 0xf9, 0x1f, //0x000023b9 cmpq         $31, %rcx
	0x0f, 0x87, 0xcd, 0xff, 0xff, 0xff, //0x000023bd ja           LBB0_458
	0x48, 0x89, 0xd1, //0x000023c3 movq         %rdx, %rcx
	0x49, 0x89, 0xc2, //0x000023c6 movq         %rax, %r10
	//0x000023c9 LBB0_461
	0x48, 0x83, 0xf9, 0x10, //0x000023c9 cmpq         $16, %rcx
	0x0f, 0x82, 0x5f, 0x00, 0x00, 0x00, //0x000023cd jb           LBB0_466
	0x48, 0x8d, 0x41, 0xf0, //0x000023d3 leaq         $-16(%rcx), %rax
	0x48, 0x89, 0xc2, //0x000023d7 movq         %rax, %rdx
	0x48, 0x83, 0xe2, 0xf0, //0x000023da andq         $-16, %rdx
	0x4a, 0x8d, 0x54, 0x12, 0x10, //0x000023de leaq         $16(%rdx,%r10), %rdx
	0x83, 0xe0, 0x0f, //0x000023e3 andl         $15, %eax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000023e6 .p2align 4, 0x90
	//0x000023f0 LBB0_463
	0xc4, 0xc1, 0x7a, 0x6f, 0x32, //0x000023f0 vmovdqu      (%r10), %xmm6
	0xc5, 0xc9, 0x74, 0x3d, 0xe3, 0xdd, 0xff, 0xff, //0x000023f5 vpcmpeqb     $-8733(%rip), %xmm6, %xmm7  /* LCPI0_4+0(%rip) */
	0xc5, 0xc9, 0xeb, 0x35, 0xeb, 0xdd, 0xff, 0xff, //0x000023fd vpor         $-8725(%rip), %xmm6, %xmm6  /* LCPI0_5+0(%rip) */
	0xc5, 0xb9, 0x74, 0xf6, //0x00002405 vpcmpeqb     %xmm6, %xmm8, %xmm6
	0xc5, 0xc9, 0xeb, 0xf7, //0x00002409 vpor         %xmm7, %xmm6, %xmm6
	0xc5, 0xf9, 0xd7, 0xf6, //0x0000240d vpmovmskb    %xmm6, %esi
	0x66, 0x85, 0xf6, //0x00002411 testw        %si, %si
	0x0f, 0x85, 0x24, 0x09, 0x00, 0x00, //0x00002414 jne          LBB0_582
	0x49, 0x83, 0xc2, 0x10, //0x0000241a addq         $16, %r10
	0x48, 0x83, 0xc1, 0xf0, //0x0000241e addq         $-16, %rcx
	0x48, 0x83, 0xf9, 0x0f, //0x00002422 cmpq         $15, %rcx
	0x0f, 0x87, 0xc4, 0xff, 0xff, 0xff, //0x00002426 ja           LBB0_463
	0x48, 0x89, 0xc1, //0x0000242c movq         %rax, %rcx
	0x49, 0x89, 0xd2, //0x0000242f movq         %rdx, %r10
	//0x00002432 LBB0_466
	0x48, 0x85, 0xc9, //0x00002432 testq        %rcx, %rcx
	0x0f, 0x84, 0x32, 0x00, 0x00, 0x00, //0x00002435 je           LBB0_473
	0x49, 0x8d, 0x04, 0x0a, //0x0000243b leaq         (%r10,%rcx), %rax
	//0x0000243f LBB0_468
	0x41, 0x0f, 0xb6, 0x12, //0x0000243f movzbl       (%r10), %edx
	0x80, 0xfa, 0x2c, //0x00002443 cmpb         $44, %dl
	0x0f, 0x84, 0xad, 0x09, 0x00, 0x00, //0x00002446 je           LBB0_586
	0x80, 0xfa, 0x7d, //0x0000244c cmpb         $125, %dl
	0x0f, 0x84, 0xa4, 0x09, 0x00, 0x00, //0x0000244f je           LBB0_586
	0x80, 0xfa, 0x5d, //0x00002455 cmpb         $93, %dl
	0x0f, 0x84, 0x9b, 0x09, 0x00, 0x00, //0x00002458 je           LBB0_586
	0x49, 0xff, 0xc2, //0x0000245e incq         %r10
	0x48, 0xff, 0xc9, //0x00002461 decq         %rcx
	0x0f, 0x85, 0xd5, 0xff, 0xff, 0xff, //0x00002464 jne          LBB0_468
	0x49, 0x89, 0xc2, //0x0000246a movq         %rax, %r10
	//0x0000246d LBB0_473
	0x4d, 0x29, 0xc2, //0x0000246d subq         %r8, %r10
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x00002470 movq         $8(%rsp), %r14
	0x4d, 0x89, 0x16, //0x00002475 movq         %r10, (%r14)
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x00002478 movq         $16(%rsp), %r15
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x0000247d movq         $24(%rsp), %r11
	0xe9, 0x4f, 0x01, 0x00, 0x00, //0x00002482 jmp          LBB0_491
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002487 .p2align 4, 0x90
	//0x00002490 LBB0_474
	0x48, 0x63, 0xc6, //0x00002490 movslq       %esi, %rax
	//0x00002493 LBB0_475
	0x48, 0x0f, 0xbc, 0xc0, //0x00002493 bsfq         %rax, %rax
	0x4d, 0x29, 0xc2, //0x00002497 subq         %r8, %r10
	0x49, 0x01, 0xc2, //0x0000249a addq         %rax, %r10
	0x4d, 0x89, 0x16, //0x0000249d movq         %r10, (%r14)
	0xe9, 0x31, 0x01, 0x00, 0x00, //0x000024a0 jmp          LBB0_491
	//0x000024a5 LBB0_476
	0x48, 0x83, 0xc0, 0x04, //0x000024a5 addq         $4, %rax
	0x49, 0x3b, 0x03, //0x000024a9 cmpq         (%r11), %rax
	0x0f, 0x86, 0x1e, 0x01, 0x00, 0x00, //0x000024ac jbe          LBB0_490
	0xe9, 0x1f, 0x01, 0x00, 0x00, //0x000024b2 jmp          LBB0_491
	//0x000024b7 LBB0_477
	0x4c, 0x89, 0x4c, 0x24, 0x20, //0x000024b7 movq         %r9, $32(%rsp)
	0x4c, 0x89, 0x04, 0x24, //0x000024bc movq         %r8, (%rsp)
	0x4d, 0x8b, 0x03, //0x000024c0 movq         (%r11), %r8
	0x4c, 0x89, 0xc1, //0x000024c3 movq         %r8, %rcx
	0x4c, 0x29, 0xd1, //0x000024c6 subq         %r10, %rcx
	0x48, 0x83, 0xf9, 0x20, //0x000024c9 cmpq         $32, %rcx
	0x0f, 0x8c, 0x31, 0x09, 0x00, 0x00, //0x000024cd jl           LBB0_587
	0x48, 0x8b, 0x0c, 0x24, //0x000024d3 movq         (%rsp), %rcx
	0x4c, 0x8d, 0x0c, 0x01, //0x000024d7 leaq         (%rcx,%rax), %r9
	0x49, 0x29, 0xc0, //0x000024db subq         %rax, %r8
	0xb8, 0x1f, 0x00, 0x00, 0x00, //0x000024de movl         $31, %eax
	0x31, 0xc9, //0x000024e3 xorl         %ecx, %ecx
	0x31, 0xdb, //0x000024e5 xorl         %ebx, %ebx
	0xe9, 0x70, 0x00, 0x00, 0x00, //0x000024e7 jmp          LBB0_479
	0x90, 0x90, 0x90, 0x90, //0x000024ec .p2align 4, 0x90
	//0x000024f0 LBB0_483
	0x89, 0xdf, //0x000024f0 movl         %ebx, %edi
	0x4d, 0x89, 0xf4, //0x000024f2 movq         %r14, %r12
	0x4d, 0x89, 0xfe, //0x000024f5 movq         %r15, %r14
	0x4d, 0x89, 0xef, //0x000024f8 movq         %r13, %r15
	0x41, 0xbd, 0xff, 0xff, 0xff, 0xff, //0x000024fb movl         $4294967295, %r13d
	0x44, 0x31, 0xef, //0x00002501 xorl         %r13d, %edi
	0x21, 0xd7, //0x00002504 andl         %edx, %edi
	0x8d, 0x14, 0x3f, //0x00002506 leal         (%rdi,%rdi), %edx
	0x09, 0xda, //0x00002509 orl          %ebx, %edx
	0x41, 0x8d, 0xb5, 0xab, 0xaa, 0xaa, 0xaa, //0x0000250b leal         $-1431655765(%r13), %esi
	0x31, 0xd6, //0x00002512 xorl         %edx, %esi
	0x21, 0xfe, //0x00002514 andl         %edi, %esi
	0x81, 0xe6, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002516 andl         $-1431655766, %esi
	0x31, 0xdb, //0x0000251c xorl         %ebx, %ebx
	0x01, 0xfe, //0x0000251e addl         %edi, %esi
	0x0f, 0x92, 0xc3, //0x00002520 setb         %bl
	0x01, 0xf6, //0x00002523 addl         %esi, %esi
	0x81, 0xf6, 0x55, 0x55, 0x55, 0x55, //0x00002525 xorl         $1431655765, %esi
	0x21, 0xd6, //0x0000252b andl         %edx, %esi
	0x44, 0x31, 0xee, //0x0000252d xorl         %r13d, %esi
	0x4d, 0x89, 0xfd, //0x00002530 movq         %r15, %r13
	0x4d, 0x89, 0xf7, //0x00002533 movq         %r14, %r15
	0x4d, 0x89, 0xe6, //0x00002536 movq         %r12, %r14
	0x41, 0x21, 0xf3, //0x00002539 andl         %esi, %r11d
	0x45, 0x85, 0xdb, //0x0000253c testl        %r11d, %r11d
	0x0f, 0x85, 0x4a, 0x00, 0x00, 0x00, //0x0000253f jne          LBB0_482
	//0x00002545 LBB0_484
	0x48, 0x83, 0xc1, 0x20, //0x00002545 addq         $32, %rcx
	0x49, 0x8d, 0x54, 0x00, 0xe0, //0x00002549 leaq         $-32(%r8,%rax), %rdx
	0x48, 0x83, 0xc0, 0xe0, //0x0000254e addq         $-32, %rax
	0x48, 0x83, 0xfa, 0x3f, //0x00002552 cmpq         $63, %rdx
	0x0f, 0x8e, 0x18, 0x08, 0x00, 0x00, //0x00002556 jle          LBB0_485
	//0x0000255c LBB0_479
	0xc4, 0xc1, 0x7e, 0x6f, 0x74, 0x09, 0x01, //0x0000255c vmovdqu      $1(%r9,%rcx), %ymm6
	0xc5, 0xcd, 0x74, 0xf9, //0x00002563 vpcmpeqb     %ymm1, %ymm6, %ymm7
	0xc5, 0x7d, 0xd7, 0xdf, //0x00002567 vpmovmskb    %ymm7, %r11d
	0xc5, 0xcd, 0x74, 0xf2, //0x0000256b vpcmpeqb     %ymm2, %ymm6, %ymm6
	0xc5, 0xfd, 0xd7, 0xd6, //0x0000256f vpmovmskb    %ymm6, %edx
	0x48, 0x85, 0xdb, //0x00002573 testq        %rbx, %rbx
	0x0f, 0x85, 0x74, 0xff, 0xff, 0xff, //0x00002576 jne          LBB0_483
	0x85, 0xd2, //0x0000257c testl        %edx, %edx
	0x0f, 0x85, 0x6c, 0xff, 0xff, 0xff, //0x0000257e jne          LBB0_483
	0x31, 0xdb, //0x00002584 xorl         %ebx, %ebx
	0x45, 0x85, 0xdb, //0x00002586 testl        %r11d, %r11d
	0x0f, 0x84, 0xb6, 0xff, 0xff, 0xff, //0x00002589 je           LBB0_484
	//0x0000258f LBB0_482
	0x49, 0x0f, 0xbc, 0xc3, //0x0000258f bsfq         %r11, %rax
	0x49, 0x01, 0xc1, //0x00002593 addq         %rax, %r9
	0x49, 0x01, 0xc9, //0x00002596 addq         %rcx, %r9
	0x4c, 0x2b, 0x0c, 0x24, //0x00002599 subq         (%rsp), %r9
	0x49, 0x83, 0xc1, 0x02, //0x0000259d addq         $2, %r9
	0x4d, 0x89, 0x0e, //0x000025a1 movq         %r9, (%r14)
	0x4d, 0x89, 0xca, //0x000025a4 movq         %r9, %r10
	//0x000025a7 LBB0_592
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x000025a7 movq         $24(%rsp), %r11
	0x4c, 0x8b, 0x4c, 0x24, 0x20, //0x000025ac movq         $32(%rsp), %r9
	0xe9, 0x20, 0x00, 0x00, 0x00, //0x000025b1 jmp          LBB0_491
	//0x000025b6 LBB0_489
	0x48, 0x83, 0xc0, 0x05, //0x000025b6 addq         $5, %rax
	0x49, 0x3b, 0x03, //0x000025ba cmpq         (%r11), %rax
	0x0f, 0x87, 0x13, 0x00, 0x00, 0x00, //0x000025bd ja           LBB0_491
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000025c3 .p2align 4, 0x90
	//0x000025d0 LBB0_490
	0x49, 0x89, 0x06, //0x000025d0 movq         %rax, (%r14)
	0x49, 0x89, 0xc2, //0x000025d3 movq         %rax, %r10
	//0x000025d6 LBB0_491
	0x4d, 0x8b, 0x07, //0x000025d6 movq         (%r15), %r8
	0x49, 0x8b, 0x47, 0x08, //0x000025d9 movq         $8(%r15), %rax
	0x49, 0x39, 0xc2, //0x000025dd cmpq         %rax, %r10
	0x0f, 0x83, 0x2a, 0x00, 0x00, 0x00, //0x000025e0 jae          LBB0_496
	0x43, 0x8a, 0x0c, 0x10, //0x000025e6 movb         (%r8,%r10), %cl
	0x80, 0xf9, 0x0d, //0x000025ea cmpb         $13, %cl
	0x0f, 0x84, 0x1d, 0x00, 0x00, 0x00, //0x000025ed je           LBB0_496
	0x80, 0xf9, 0x20, //0x000025f3 cmpb         $32, %cl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x000025f6 je           LBB0_496
	0x80, 0xc1, 0xf7, //0x000025fc addb         $-9, %cl
	0x80, 0xf9, 0x01, //0x000025ff cmpb         $1, %cl
	0x0f, 0x86, 0x08, 0x00, 0x00, 0x00, //0x00002602 jbe          LBB0_496
	0x4c, 0x89, 0xd1, //0x00002608 movq         %r10, %rcx
	0xe9, 0x88, 0x01, 0x00, 0x00, //0x0000260b jmp          LBB0_522
	//0x00002610 .p2align 4, 0x90
	//0x00002610 LBB0_496
	0x49, 0x8d, 0x4a, 0x01, //0x00002610 leaq         $1(%r10), %rcx
	0x48, 0x39, 0xc1, //0x00002614 cmpq         %rax, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00002617 jae          LBB0_500
	0x41, 0x8a, 0x14, 0x08, //0x0000261d movb         (%r8,%rcx), %dl
	0x80, 0xfa, 0x0d, //0x00002621 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00002624 je           LBB0_500
	0x80, 0xfa, 0x20, //0x0000262a cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000262d je           LBB0_500
	0x80, 0xc2, 0xf7, //0x00002633 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00002636 cmpb         $1, %dl
	0x0f, 0x87, 0x59, 0x01, 0x00, 0x00, //0x00002639 ja           LBB0_522
	0x90, //0x0000263f .p2align 4, 0x90
	//0x00002640 LBB0_500
	0x49, 0x8d, 0x4a, 0x02, //0x00002640 leaq         $2(%r10), %rcx
	0x48, 0x39, 0xc1, //0x00002644 cmpq         %rax, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00002647 jae          LBB0_504
	0x41, 0x8a, 0x14, 0x08, //0x0000264d movb         (%r8,%rcx), %dl
	0x80, 0xfa, 0x0d, //0x00002651 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00002654 je           LBB0_504
	0x80, 0xfa, 0x20, //0x0000265a cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000265d je           LBB0_504
	0x80, 0xc2, 0xf7, //0x00002663 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00002666 cmpb         $1, %dl
	0x0f, 0x87, 0x29, 0x01, 0x00, 0x00, //0x00002669 ja           LBB0_522
	0x90, //0x0000266f .p2align 4, 0x90
	//0x00002670 LBB0_504
	0x49, 0x8d, 0x4a, 0x03, //0x00002670 leaq         $3(%r10), %rcx
	0x48, 0x39, 0xc1, //0x00002674 cmpq         %rax, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00002677 jae          LBB0_508
	0x41, 0x8a, 0x14, 0x08, //0x0000267d movb         (%r8,%rcx), %dl
	0x80, 0xfa, 0x0d, //0x00002681 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00002684 je           LBB0_508
	0x80, 0xfa, 0x20, //0x0000268a cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000268d je           LBB0_508
	0x80, 0xc2, 0xf7, //0x00002693 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00002696 cmpb         $1, %dl
	0x0f, 0x87, 0xf9, 0x00, 0x00, 0x00, //0x00002699 ja           LBB0_522
	0x90, //0x0000269f .p2align 4, 0x90
	//0x000026a0 LBB0_508
	0x49, 0x8d, 0x4a, 0x04, //0x000026a0 leaq         $4(%r10), %rcx
	0x48, 0x89, 0xc2, //0x000026a4 movq         %rax, %rdx
	0x48, 0x29, 0xca, //0x000026a7 subq         %rcx, %rdx
	0x0f, 0x86, 0x3d, 0x0a, 0x00, 0x00, //0x000026aa jbe          LBB0_625
	0x4c, 0x01, 0xc1, //0x000026b0 addq         %r8, %rcx
	0x48, 0x83, 0xfa, 0x20, //0x000026b3 cmpq         $32, %rdx
	0x0f, 0x82, 0x55, 0x00, 0x00, 0x00, //0x000026b7 jb           LBB0_514
	0x48, 0x89, 0xc6, //0x000026bd movq         %rax, %rsi
	0x4c, 0x29, 0xd6, //0x000026c0 subq         %r10, %rsi
	0x48, 0x83, 0xc6, 0xdc, //0x000026c3 addq         $-36, %rsi
	0x48, 0x89, 0xf7, //0x000026c7 movq         %rsi, %rdi
	0x48, 0x83, 0xe7, 0xe0, //0x000026ca andq         $-32, %rdi
	0x4c, 0x01, 0xd7, //0x000026ce addq         %r10, %rdi
	0x49, 0x8d, 0x7c, 0x38, 0x24, //0x000026d1 leaq         $36(%r8,%rdi), %rdi
	0x83, 0xe6, 0x1f, //0x000026d6 andl         $31, %esi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000026d9 .p2align 4, 0x90
	//0x000026e0 LBB0_511
	0xc5, 0xfe, 0x6f, 0x31, //0x000026e0 vmovdqu      (%rcx), %ymm6
	0xc4, 0xe2, 0x7d, 0x00, 0xfe, //0x000026e4 vpshufb      %ymm6, %ymm0, %ymm7
	0xc5, 0xcd, 0x74, 0xf7, //0x000026e9 vpcmpeqb     %ymm7, %ymm6, %ymm6
	0xc5, 0xfd, 0xd7, 0xde, //0x000026ed vpmovmskb    %ymm6, %ebx
	0x83, 0xfb, 0xff, //0x000026f1 cmpl         $-1, %ebx
	0x0f, 0x85, 0x86, 0x00, 0x00, 0x00, //0x000026f4 jne          LBB0_521
	0x48, 0x83, 0xc1, 0x20, //0x000026fa addq         $32, %rcx
	0x48, 0x83, 0xc2, 0xe0, //0x000026fe addq         $-32, %rdx
	0x48, 0x83, 0xfa, 0x1f, //0x00002702 cmpq         $31, %rdx
	0x0f, 0x87, 0xd4, 0xff, 0xff, 0xff, //0x00002706 ja           LBB0_511
	0x48, 0x89, 0xf2, //0x0000270c movq         %rsi, %rdx
	0x48, 0x89, 0xf9, //0x0000270f movq         %rdi, %rcx
	//0x00002712 LBB0_514
	0x48, 0x85, 0xd2, //0x00002712 testq        %rdx, %rdx
	0x0f, 0x84, 0x48, 0x00, 0x00, 0x00, //0x00002715 je           LBB0_520
	0x48, 0x8d, 0x34, 0x11, //0x0000271b leaq         (%rcx,%rdx), %rsi
	0x48, 0xff, 0xc1, //0x0000271f incq         %rcx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002722 .p2align 4, 0x90
	//0x00002730 LBB0_516
	0x0f, 0xbe, 0x79, 0xff, //0x00002730 movsbl       $-1(%rcx), %edi
	0x83, 0xff, 0x20, //0x00002734 cmpl         $32, %edi
	0x0f, 0x87, 0x09, 0x06, 0x00, 0x00, //0x00002737 ja           LBB0_583
	0x48, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000273d movabsq      $4294977024, %rbx
	0x48, 0x0f, 0xa3, 0xfb, //0x00002747 btq          %rdi, %rbx
	0x0f, 0x83, 0xf5, 0x05, 0x00, 0x00, //0x0000274b jae          LBB0_583
	0x48, 0xff, 0xca, //0x00002751 decq         %rdx
	0x48, 0xff, 0xc1, //0x00002754 incq         %rcx
	0x48, 0x85, 0xd2, //0x00002757 testq        %rdx, %rdx
	0x0f, 0x85, 0xd0, 0xff, 0xff, 0xff, //0x0000275a jne          LBB0_516
	0x48, 0x89, 0xf1, //0x00002760 movq         %rsi, %rcx
	//0x00002763 LBB0_520
	0x4c, 0x29, 0xc1, //0x00002763 subq         %r8, %rcx
	0x48, 0x39, 0xc1, //0x00002766 cmpq         %rax, %rcx
	0x0f, 0x82, 0x29, 0x00, 0x00, 0x00, //0x00002769 jb           LBB0_522
	0xe9, 0x7d, 0x0b, 0x00, 0x00, //0x0000276f jmp          LBB0_657
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002774 .p2align 4, 0x90
	//0x00002780 LBB0_521
	0x4c, 0x29, 0xc1, //0x00002780 subq         %r8, %rcx
	0xf7, 0xd3, //0x00002783 notl         %ebx
	0x48, 0x63, 0xd3, //0x00002785 movslq       %ebx, %rdx
	0x48, 0x0f, 0xbc, 0xd2, //0x00002788 bsfq         %rdx, %rdx
	0x48, 0x01, 0xd1, //0x0000278c addq         %rdx, %rcx
	0x48, 0x39, 0xc1, //0x0000278f cmpq         %rax, %rcx
	0x0f, 0x83, 0x59, 0x0b, 0x00, 0x00, //0x00002792 jae          LBB0_657
	//0x00002798 LBB0_522
	0x4c, 0x8d, 0x51, 0x01, //0x00002798 leaq         $1(%rcx), %r10
	0x4d, 0x89, 0x16, //0x0000279c movq         %r10, (%r14)
	0x41, 0x8a, 0x04, 0x08, //0x0000279f movb         (%r8,%rcx), %al
	0x3c, 0x2c, //0x000027a3 cmpb         $44, %al
	0x0f, 0x85, 0x4d, 0x09, 0x00, 0x00, //0x000027a5 jne          LBB0_626
	0x49, 0x83, 0xf9, 0x02, //0x000027ab cmpq         $2, %r9
	0x4d, 0x8d, 0x49, 0xff, //0x000027af leaq         $-1(%r9), %r9
	0x0f, 0x8d, 0xc7, 0xf9, 0xff, 0xff, //0x000027b3 jge          LBB0_423
	0xe9, 0xc2, 0x06, 0x00, 0x00, //0x000027b9 jmp          LBB0_524
	//0x000027be LBB0_527
	0x4c, 0x89, 0x2c, 0x24, //0x000027be movq         %r13, (%rsp)
	0x4d, 0x8b, 0x1b, //0x000027c2 movq         (%r11), %r11
	0x4d, 0x29, 0xd3, //0x000027c5 subq         %r10, %r11
	0x4d, 0x01, 0xd0, //0x000027c8 addq         %r10, %r8
	0x45, 0x31, 0xed, //0x000027cb xorl         %r13d, %r13d
	0x45, 0x31, 0xf6, //0x000027ce xorl         %r14d, %r14d
	0x45, 0x31, 0xff, //0x000027d1 xorl         %r15d, %r15d
	0x31, 0xd2, //0x000027d4 xorl         %edx, %edx
	0x4d, 0x89, 0xcc, //0x000027d6 movq         %r9, %r12
	0x49, 0x83, 0xfb, 0x40, //0x000027d9 cmpq         $64, %r11
	0x0f, 0x8d, 0x24, 0x01, 0x00, 0x00, //0x000027dd jge          LBB0_528
	//0x000027e3 LBB0_537
	0x48, 0x8b, 0x04, 0x24, //0x000027e3 movq         (%rsp), %rax
	0x4d, 0x85, 0xdb, //0x000027e7 testq        %r11, %r11
	0x0f, 0x8e, 0x20, 0x06, 0x00, 0x00, //0x000027ea jle          LBB0_588
	0xc5, 0xc9, 0xef, 0xf6, //0x000027f0 vpxor        %xmm6, %xmm6, %xmm6
	0xc5, 0xfe, 0x7f, 0x74, 0x24, 0x60, //0x000027f4 vmovdqu      %ymm6, $96(%rsp)
	0xc5, 0xfe, 0x7f, 0x74, 0x24, 0x40, //0x000027fa vmovdqu      %ymm6, $64(%rsp)
	0x44, 0x89, 0xc0, //0x00002800 movl         %r8d, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x00002803 andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x00002808 cmpl         $4033, %eax
	0x0f, 0x82, 0xf4, 0x00, 0x00, 0x00, //0x0000280d jb           LBB0_528
	0x49, 0x83, 0xfb, 0x20, //0x00002813 cmpq         $32, %r11
	0x0f, 0x82, 0x1d, 0x00, 0x00, 0x00, //0x00002817 jb           LBB0_541
	0xc4, 0xc1, 0x7e, 0x6f, 0x30, //0x0000281d vmovdqu      (%r8), %ymm6
	0xc5, 0xfe, 0x7f, 0x74, 0x24, 0x40, //0x00002822 vmovdqu      %ymm6, $64(%rsp)
	0x49, 0x83, 0xc0, 0x20, //0x00002828 addq         $32, %r8
	0x4d, 0x8d, 0x53, 0xe0, //0x0000282c leaq         $-32(%r11), %r10
	0x4c, 0x8d, 0x4c, 0x24, 0x60, //0x00002830 leaq         $96(%rsp), %r9
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x00002835 jmp          LBB0_542
	//0x0000283a LBB0_541
	0x4c, 0x8d, 0x4c, 0x24, 0x40, //0x0000283a leaq         $64(%rsp), %r9
	0x4d, 0x89, 0xda, //0x0000283f movq         %r11, %r10
	//0x00002842 LBB0_542
	0x49, 0x83, 0xfa, 0x10, //0x00002842 cmpq         $16, %r10
	0x0f, 0x82, 0x5c, 0x00, 0x00, 0x00, //0x00002846 jb           LBB0_543
	0xc4, 0xc1, 0x7a, 0x6f, 0x30, //0x0000284c vmovdqu      (%r8), %xmm6
	0xc4, 0xc1, 0x7a, 0x7f, 0x31, //0x00002851 vmovdqu      %xmm6, (%r9)
	0x49, 0x83, 0xc0, 0x10, //0x00002856 addq         $16, %r8
	0x49, 0x83, 0xc1, 0x10, //0x0000285a addq         $16, %r9
	0x49, 0x83, 0xc2, 0xf0, //0x0000285e addq         $-16, %r10
	0x49, 0x83, 0xfa, 0x08, //0x00002862 cmpq         $8, %r10
	0x0f, 0x83, 0x46, 0x00, 0x00, 0x00, //0x00002866 jae          LBB0_548
	//0x0000286c LBB0_544
	0x49, 0x83, 0xfa, 0x04, //0x0000286c cmpq         $4, %r10
	0x0f, 0x8c, 0x58, 0x00, 0x00, 0x00, //0x00002870 jl           LBB0_545
	//0x00002876 LBB0_549
	0x41, 0x8b, 0x00, //0x00002876 movl         (%r8), %eax
	0x41, 0x89, 0x01, //0x00002879 movl         %eax, (%r9)
	0x49, 0x83, 0xc0, 0x04, //0x0000287c addq         $4, %r8
	0x49, 0x83, 0xc1, 0x04, //0x00002880 addq         $4, %r9
	0x49, 0x83, 0xc2, 0xfc, //0x00002884 addq         $-4, %r10
	0x49, 0x83, 0xfa, 0x02, //0x00002888 cmpq         $2, %r10
	0x0f, 0x83, 0x46, 0x00, 0x00, 0x00, //0x0000288c jae          LBB0_550
	//0x00002892 LBB0_546
	0x4c, 0x89, 0xc0, //0x00002892 movq         %r8, %rax
	0x4c, 0x8d, 0x44, 0x24, 0x40, //0x00002895 leaq         $64(%rsp), %r8
	0x4d, 0x85, 0xd2, //0x0000289a testq        %r10, %r10
	0x0f, 0x85, 0x5a, 0x00, 0x00, 0x00, //0x0000289d jne          LBB0_551
	0xe9, 0x5f, 0x00, 0x00, 0x00, //0x000028a3 jmp          LBB0_528
	//0x000028a8 LBB0_543
	0x49, 0x83, 0xfa, 0x08, //0x000028a8 cmpq         $8, %r10
	0x0f, 0x82, 0xba, 0xff, 0xff, 0xff, //0x000028ac jb           LBB0_544
	//0x000028b2 LBB0_548
	0x49, 0x8b, 0x00, //0x000028b2 movq         (%r8), %rax
	0x49, 0x89, 0x01, //0x000028b5 movq         %rax, (%r9)
	0x49, 0x83, 0xc0, 0x08, //0x000028b8 addq         $8, %r8
	0x49, 0x83, 0xc1, 0x08, //0x000028bc addq         $8, %r9
	0x49, 0x83, 0xc2, 0xf8, //0x000028c0 addq         $-8, %r10
	0x49, 0x83, 0xfa, 0x04, //0x000028c4 cmpq         $4, %r10
	0x0f, 0x8d, 0xa8, 0xff, 0xff, 0xff, //0x000028c8 jge          LBB0_549
	//0x000028ce LBB0_545
	0x49, 0x83, 0xfa, 0x02, //0x000028ce cmpq         $2, %r10
	0x0f, 0x82, 0xba, 0xff, 0xff, 0xff, //0x000028d2 jb           LBB0_546
	//0x000028d8 LBB0_550
	0x41, 0x0f, 0xb7, 0x00, //0x000028d8 movzwl       (%r8), %eax
	0x66, 0x41, 0x89, 0x01, //0x000028dc movw         %ax, (%r9)
	0x49, 0x83, 0xc0, 0x02, //0x000028e0 addq         $2, %r8
	0x49, 0x83, 0xc1, 0x02, //0x000028e4 addq         $2, %r9
	0x49, 0x83, 0xc2, 0xfe, //0x000028e8 addq         $-2, %r10
	0x4c, 0x89, 0xc0, //0x000028ec movq         %r8, %rax
	0x4c, 0x8d, 0x44, 0x24, 0x40, //0x000028ef leaq         $64(%rsp), %r8
	0x4d, 0x85, 0xd2, //0x000028f4 testq        %r10, %r10
	0x0f, 0x84, 0x0a, 0x00, 0x00, 0x00, //0x000028f7 je           LBB0_528
	//0x000028fd LBB0_551
	0x8a, 0x00, //0x000028fd movb         (%rax), %al
	0x41, 0x88, 0x01, //0x000028ff movb         %al, (%r9)
	0x4c, 0x8d, 0x44, 0x24, 0x40, //0x00002902 leaq         $64(%rsp), %r8
	//0x00002907 LBB0_528
	0x4c, 0x89, 0xc0, //0x00002907 movq         %r8, %rax
	0xc4, 0x41, 0x7e, 0x6f, 0x38, //0x0000290a vmovdqu      (%r8), %ymm15
	0xc4, 0x41, 0x7e, 0x6f, 0x70, 0x20, //0x0000290f vmovdqu      $32(%r8), %ymm14
	0xc5, 0x85, 0x74, 0xf1, //0x00002915 vpcmpeqb     %ymm1, %ymm15, %ymm6
	0xc5, 0x7d, 0xd7, 0xce, //0x00002919 vpmovmskb    %ymm6, %r9d
	0xc5, 0x8d, 0x74, 0xf1, //0x0000291d vpcmpeqb     %ymm1, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x00002921 vpmovmskb    %ymm6, %eax
	0x48, 0xc1, 0xe0, 0x20, //0x00002925 shlq         $32, %rax
	0x49, 0x09, 0xc1, //0x00002929 orq          %rax, %r9
	0xc5, 0x85, 0x74, 0xf2, //0x0000292c vpcmpeqb     %ymm2, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xfe, //0x00002930 vpmovmskb    %ymm6, %edi
	0xc5, 0x8d, 0x74, 0xf2, //0x00002934 vpcmpeqb     %ymm2, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x00002938 vpmovmskb    %ymm6, %eax
	0x48, 0xc1, 0xe0, 0x20, //0x0000293c shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x00002940 orq          %rax, %rdi
	0x48, 0x89, 0xf8, //0x00002943 movq         %rdi, %rax
	0x4c, 0x09, 0xf0, //0x00002946 orq          %r14, %rax
	0x0f, 0x84, 0x4b, 0x00, 0x00, 0x00, //0x00002949 je           LBB0_530
	0x4c, 0x89, 0xf0, //0x0000294f movq         %r14, %rax
	0x48, 0xf7, 0xd0, //0x00002952 notq         %rax
	0x48, 0x21, 0xf8, //0x00002955 andq         %rdi, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x00002958 leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xf1, //0x0000295c orq          %r14, %rcx
	0x48, 0x89, 0xcb, //0x0000295f movq         %rcx, %rbx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002962 movabsq      $-6148914691236517206, %rsi
	0x48, 0x31, 0xf3, //0x0000296c xorq         %rsi, %rbx
	0x48, 0x21, 0xf7, //0x0000296f andq         %rsi, %rdi
	0x48, 0x21, 0xdf, //0x00002972 andq         %rbx, %rdi
	0x45, 0x31, 0xf6, //0x00002975 xorl         %r14d, %r14d
	0x48, 0x01, 0xc7, //0x00002978 addq         %rax, %rdi
	0x41, 0x0f, 0x92, 0xc6, //0x0000297b setb         %r14b
	0x48, 0x01, 0xff, //0x0000297f addq         %rdi, %rdi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002982 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc7, //0x0000298c xorq         %rax, %rdi
	0x48, 0x21, 0xcf, //0x0000298f andq         %rcx, %rdi
	0x48, 0xf7, 0xd7, //0x00002992 notq         %rdi
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00002995 jmp          LBB0_531
	//0x0000299a LBB0_530
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x0000299a movq         $-1, %rdi
	0x45, 0x31, 0xf6, //0x000029a1 xorl         %r14d, %r14d
	//0x000029a4 LBB0_531
	0x4c, 0x21, 0xcf, //0x000029a4 andq         %r9, %rdi
	0xc4, 0xe1, 0xf9, 0x6e, 0xf7, //0x000029a7 vmovq        %rdi, %xmm6
	0xc4, 0xc3, 0x49, 0x44, 0xf1, 0x00, //0x000029ac vpclmulqdq   $0, %xmm9, %xmm6, %xmm6
	0xc4, 0xe1, 0xf9, 0x7e, 0xf7, //0x000029b2 vmovq        %xmm6, %rdi
	0x4c, 0x31, 0xef, //0x000029b7 xorq         %r13, %rdi
	0xc4, 0xc1, 0x05, 0x74, 0xf2, //0x000029ba vpcmpeqb     %ymm10, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xf6, //0x000029bf vpmovmskb    %ymm6, %esi
	0xc4, 0xc1, 0x0d, 0x74, 0xf2, //0x000029c3 vpcmpeqb     %ymm10, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x000029c8 vpmovmskb    %ymm6, %eax
	0x48, 0xc1, 0xe0, 0x20, //0x000029cc shlq         $32, %rax
	0x48, 0x09, 0xc6, //0x000029d0 orq          %rax, %rsi
	0x48, 0x89, 0xf9, //0x000029d3 movq         %rdi, %rcx
	0x48, 0xf7, 0xd1, //0x000029d6 notq         %rcx
	0x48, 0x21, 0xce, //0x000029d9 andq         %rcx, %rsi
	0xc4, 0xc1, 0x05, 0x74, 0xf3, //0x000029dc vpcmpeqb     %ymm11, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x000029e1 vpmovmskb    %ymm6, %eax
	0xc4, 0xc1, 0x0d, 0x74, 0xf3, //0x000029e5 vpcmpeqb     %ymm11, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xde, //0x000029ea vpmovmskb    %ymm6, %ebx
	0x48, 0xc1, 0xe3, 0x20, //0x000029ee shlq         $32, %rbx
	0x48, 0x09, 0xd8, //0x000029f2 orq          %rbx, %rax
	0x48, 0x21, 0xc8, //0x000029f5 andq         %rcx, %rax
	0x0f, 0x84, 0x3e, 0x00, 0x00, 0x00, //0x000029f8 je           LBB0_535
	0x4c, 0x8b, 0x2c, 0x24, //0x000029fe movq         (%rsp), %r13
	0x4d, 0x89, 0xe1, //0x00002a02 movq         %r12, %r9
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002a05 .p2align 4, 0x90
	//0x00002a10 LBB0_533
	0x48, 0x8d, 0x58, 0xff, //0x00002a10 leaq         $-1(%rax), %rbx
	0x48, 0x89, 0xd9, //0x00002a14 movq         %rbx, %rcx
	0x48, 0x21, 0xf1, //0x00002a17 andq         %rsi, %rcx
	0xf3, 0x48, 0x0f, 0xb8, 0xc9, //0x00002a1a popcntq      %rcx, %rcx
	0x4c, 0x01, 0xf9, //0x00002a1f addq         %r15, %rcx
	0x48, 0x39, 0xd1, //0x00002a22 cmpq         %rdx, %rcx
	0x0f, 0x86, 0xdd, 0x02, 0x00, 0x00, //0x00002a25 jbe          LBB0_577
	0x48, 0xff, 0xc2, //0x00002a2b incq         %rdx
	0x48, 0x21, 0xd8, //0x00002a2e andq         %rbx, %rax
	0x0f, 0x85, 0xd9, 0xff, 0xff, 0xff, //0x00002a31 jne          LBB0_533
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x00002a37 jmp          LBB0_536
	//0x00002a3c LBB0_535
	0x4d, 0x89, 0xe1, //0x00002a3c movq         %r12, %r9
	//0x00002a3f LBB0_536
	0x48, 0xc1, 0xff, 0x3f, //0x00002a3f sarq         $63, %rdi
	0xf3, 0x48, 0x0f, 0xb8, 0xc6, //0x00002a43 popcntq      %rsi, %rax
	0x49, 0x01, 0xc7, //0x00002a48 addq         %rax, %r15
	0x49, 0x83, 0xc0, 0x40, //0x00002a4b addq         $64, %r8
	0x49, 0x83, 0xc3, 0xc0, //0x00002a4f addq         $-64, %r11
	0x49, 0x89, 0xfd, //0x00002a53 movq         %rdi, %r13
	0x4d, 0x89, 0xcc, //0x00002a56 movq         %r9, %r12
	0x49, 0x83, 0xfb, 0x40, //0x00002a59 cmpq         $64, %r11
	0x0f, 0x8d, 0xa4, 0xfe, 0xff, 0xff, //0x00002a5d jge          LBB0_528
	0xe9, 0x7b, 0xfd, 0xff, 0xff, //0x00002a63 jmp          LBB0_537
	//0x00002a68 LBB0_552
	0x4c, 0x89, 0x2c, 0x24, //0x00002a68 movq         %r13, (%rsp)
	0x4d, 0x8b, 0x1b, //0x00002a6c movq         (%r11), %r11
	0x4d, 0x29, 0xd3, //0x00002a6f subq         %r10, %r11
	0x4d, 0x01, 0xd0, //0x00002a72 addq         %r10, %r8
	0x45, 0x31, 0xed, //0x00002a75 xorl         %r13d, %r13d
	0x45, 0x31, 0xf6, //0x00002a78 xorl         %r14d, %r14d
	0x45, 0x31, 0xff, //0x00002a7b xorl         %r15d, %r15d
	0x31, 0xd2, //0x00002a7e xorl         %edx, %edx
	0x4d, 0x89, 0xcc, //0x00002a80 movq         %r9, %r12
	0x49, 0x83, 0xfb, 0x40, //0x00002a83 cmpq         $64, %r11
	0x0f, 0x8d, 0x24, 0x01, 0x00, 0x00, //0x00002a87 jge          LBB0_553
	//0x00002a8d LBB0_562
	0x48, 0x8b, 0x04, 0x24, //0x00002a8d movq         (%rsp), %rax
	0x4d, 0x85, 0xdb, //0x00002a91 testq        %r11, %r11
	0x0f, 0x8e, 0x76, 0x03, 0x00, 0x00, //0x00002a94 jle          LBB0_588
	0xc5, 0xc9, 0xef, 0xf6, //0x00002a9a vpxor        %xmm6, %xmm6, %xmm6
	0xc5, 0xfe, 0x7f, 0x74, 0x24, 0x60, //0x00002a9e vmovdqu      %ymm6, $96(%rsp)
	0xc5, 0xfe, 0x7f, 0x74, 0x24, 0x40, //0x00002aa4 vmovdqu      %ymm6, $64(%rsp)
	0x44, 0x89, 0xc0, //0x00002aaa movl         %r8d, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x00002aad andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x00002ab2 cmpl         $4033, %eax
	0x0f, 0x82, 0xf4, 0x00, 0x00, 0x00, //0x00002ab7 jb           LBB0_553
	0x49, 0x83, 0xfb, 0x20, //0x00002abd cmpq         $32, %r11
	0x0f, 0x82, 0x1d, 0x00, 0x00, 0x00, //0x00002ac1 jb           LBB0_566
	0xc4, 0xc1, 0x7e, 0x6f, 0x30, //0x00002ac7 vmovdqu      (%r8), %ymm6
	0xc5, 0xfe, 0x7f, 0x74, 0x24, 0x40, //0x00002acc vmovdqu      %ymm6, $64(%rsp)
	0x49, 0x83, 0xc0, 0x20, //0x00002ad2 addq         $32, %r8
	0x4d, 0x8d, 0x53, 0xe0, //0x00002ad6 leaq         $-32(%r11), %r10
	0x4c, 0x8d, 0x4c, 0x24, 0x60, //0x00002ada leaq         $96(%rsp), %r9
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x00002adf jmp          LBB0_567
	//0x00002ae4 LBB0_566
	0x4c, 0x8d, 0x4c, 0x24, 0x40, //0x00002ae4 leaq         $64(%rsp), %r9
	0x4d, 0x89, 0xda, //0x00002ae9 movq         %r11, %r10
	//0x00002aec LBB0_567
	0x49, 0x83, 0xfa, 0x10, //0x00002aec cmpq         $16, %r10
	0x0f, 0x82, 0x5c, 0x00, 0x00, 0x00, //0x00002af0 jb           LBB0_568
	0xc4, 0xc1, 0x7a, 0x6f, 0x30, //0x00002af6 vmovdqu      (%r8), %xmm6
	0xc4, 0xc1, 0x7a, 0x7f, 0x31, //0x00002afb vmovdqu      %xmm6, (%r9)
	0x49, 0x83, 0xc0, 0x10, //0x00002b00 addq         $16, %r8
	0x49, 0x83, 0xc1, 0x10, //0x00002b04 addq         $16, %r9
	0x49, 0x83, 0xc2, 0xf0, //0x00002b08 addq         $-16, %r10
	0x49, 0x83, 0xfa, 0x08, //0x00002b0c cmpq         $8, %r10
	0x0f, 0x83, 0x46, 0x00, 0x00, 0x00, //0x00002b10 jae          LBB0_573
	//0x00002b16 LBB0_569
	0x49, 0x83, 0xfa, 0x04, //0x00002b16 cmpq         $4, %r10
	0x0f, 0x8c, 0x58, 0x00, 0x00, 0x00, //0x00002b1a jl           LBB0_570
	//0x00002b20 LBB0_574
	0x41, 0x8b, 0x00, //0x00002b20 movl         (%r8), %eax
	0x41, 0x89, 0x01, //0x00002b23 movl         %eax, (%r9)
	0x49, 0x83, 0xc0, 0x04, //0x00002b26 addq         $4, %r8
	0x49, 0x83, 0xc1, 0x04, //0x00002b2a addq         $4, %r9
	0x49, 0x83, 0xc2, 0xfc, //0x00002b2e addq         $-4, %r10
	0x49, 0x83, 0xfa, 0x02, //0x00002b32 cmpq         $2, %r10
	0x0f, 0x83, 0x46, 0x00, 0x00, 0x00, //0x00002b36 jae          LBB0_575
	//0x00002b3c LBB0_571
	0x4c, 0x89, 0xc0, //0x00002b3c movq         %r8, %rax
	0x4c, 0x8d, 0x44, 0x24, 0x40, //0x00002b3f leaq         $64(%rsp), %r8
	0x4d, 0x85, 0xd2, //0x00002b44 testq        %r10, %r10
	0x0f, 0x85, 0x5a, 0x00, 0x00, 0x00, //0x00002b47 jne          LBB0_576
	0xe9, 0x5f, 0x00, 0x00, 0x00, //0x00002b4d jmp          LBB0_553
	//0x00002b52 LBB0_568
	0x49, 0x83, 0xfa, 0x08, //0x00002b52 cmpq         $8, %r10
	0x0f, 0x82, 0xba, 0xff, 0xff, 0xff, //0x00002b56 jb           LBB0_569
	//0x00002b5c LBB0_573
	0x49, 0x8b, 0x00, //0x00002b5c movq         (%r8), %rax
	0x49, 0x89, 0x01, //0x00002b5f movq         %rax, (%r9)
	0x49, 0x83, 0xc0, 0x08, //0x00002b62 addq         $8, %r8
	0x49, 0x83, 0xc1, 0x08, //0x00002b66 addq         $8, %r9
	0x49, 0x83, 0xc2, 0xf8, //0x00002b6a addq         $-8, %r10
	0x49, 0x83, 0xfa, 0x04, //0x00002b6e cmpq         $4, %r10
	0x0f, 0x8d, 0xa8, 0xff, 0xff, 0xff, //0x00002b72 jge          LBB0_574
	//0x00002b78 LBB0_570
	0x49, 0x83, 0xfa, 0x02, //0x00002b78 cmpq         $2, %r10
	0x0f, 0x82, 0xba, 0xff, 0xff, 0xff, //0x00002b7c jb           LBB0_571
	//0x00002b82 LBB0_575
	0x41, 0x0f, 0xb7, 0x00, //0x00002b82 movzwl       (%r8), %eax
	0x66, 0x41, 0x89, 0x01, //0x00002b86 movw         %ax, (%r9)
	0x49, 0x83, 0xc0, 0x02, //0x00002b8a addq         $2, %r8
	0x49, 0x83, 0xc1, 0x02, //0x00002b8e addq         $2, %r9
	0x49, 0x83, 0xc2, 0xfe, //0x00002b92 addq         $-2, %r10
	0x4c, 0x89, 0xc0, //0x00002b96 movq         %r8, %rax
	0x4c, 0x8d, 0x44, 0x24, 0x40, //0x00002b99 leaq         $64(%rsp), %r8
	0x4d, 0x85, 0xd2, //0x00002b9e testq        %r10, %r10
	0x0f, 0x84, 0x0a, 0x00, 0x00, 0x00, //0x00002ba1 je           LBB0_553
	//0x00002ba7 LBB0_576
	0x8a, 0x00, //0x00002ba7 movb         (%rax), %al
	0x41, 0x88, 0x01, //0x00002ba9 movb         %al, (%r9)
	0x4c, 0x8d, 0x44, 0x24, 0x40, //0x00002bac leaq         $64(%rsp), %r8
	//0x00002bb1 LBB0_553
	0x4c, 0x89, 0xc0, //0x00002bb1 movq         %r8, %rax
	0xc4, 0x41, 0x7e, 0x6f, 0x38, //0x00002bb4 vmovdqu      (%r8), %ymm15
	0xc4, 0x41, 0x7e, 0x6f, 0x70, 0x20, //0x00002bb9 vmovdqu      $32(%r8), %ymm14
	0xc5, 0x85, 0x74, 0xf1, //0x00002bbf vpcmpeqb     %ymm1, %ymm15, %ymm6
	0xc5, 0x7d, 0xd7, 0xce, //0x00002bc3 vpmovmskb    %ymm6, %r9d
	0xc5, 0x8d, 0x74, 0xf1, //0x00002bc7 vpcmpeqb     %ymm1, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x00002bcb vpmovmskb    %ymm6, %eax
	0x48, 0xc1, 0xe0, 0x20, //0x00002bcf shlq         $32, %rax
	0x49, 0x09, 0xc1, //0x00002bd3 orq          %rax, %r9
	0xc5, 0x85, 0x74, 0xf2, //0x00002bd6 vpcmpeqb     %ymm2, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xfe, //0x00002bda vpmovmskb    %ymm6, %edi
	0xc5, 0x8d, 0x74, 0xf2, //0x00002bde vpcmpeqb     %ymm2, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x00002be2 vpmovmskb    %ymm6, %eax
	0x48, 0xc1, 0xe0, 0x20, //0x00002be6 shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x00002bea orq          %rax, %rdi
	0x48, 0x89, 0xf8, //0x00002bed movq         %rdi, %rax
	0x4c, 0x09, 0xf0, //0x00002bf0 orq          %r14, %rax
	0x0f, 0x84, 0x4b, 0x00, 0x00, 0x00, //0x00002bf3 je           LBB0_555
	0x4c, 0x89, 0xf0, //0x00002bf9 movq         %r14, %rax
	0x48, 0xf7, 0xd0, //0x00002bfc notq         %rax
	0x48, 0x21, 0xf8, //0x00002bff andq         %rdi, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x00002c02 leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xf1, //0x00002c06 orq          %r14, %rcx
	0x48, 0x89, 0xcb, //0x00002c09 movq         %rcx, %rbx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002c0c movabsq      $-6148914691236517206, %rsi
	0x48, 0x31, 0xf3, //0x00002c16 xorq         %rsi, %rbx
	0x48, 0x21, 0xf7, //0x00002c19 andq         %rsi, %rdi
	0x48, 0x21, 0xdf, //0x00002c1c andq         %rbx, %rdi
	0x45, 0x31, 0xf6, //0x00002c1f xorl         %r14d, %r14d
	0x48, 0x01, 0xc7, //0x00002c22 addq         %rax, %rdi
	0x41, 0x0f, 0x92, 0xc6, //0x00002c25 setb         %r14b
	0x48, 0x01, 0xff, //0x00002c29 addq         %rdi, %rdi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002c2c movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc7, //0x00002c36 xorq         %rax, %rdi
	0x48, 0x21, 0xcf, //0x00002c39 andq         %rcx, %rdi
	0x48, 0xf7, 0xd7, //0x00002c3c notq         %rdi
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00002c3f jmp          LBB0_556
	//0x00002c44 LBB0_555
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00002c44 movq         $-1, %rdi
	0x45, 0x31, 0xf6, //0x00002c4b xorl         %r14d, %r14d
	//0x00002c4e LBB0_556
	0x4c, 0x21, 0xcf, //0x00002c4e andq         %r9, %rdi
	0xc4, 0xe1, 0xf9, 0x6e, 0xf7, //0x00002c51 vmovq        %rdi, %xmm6
	0xc4, 0xc3, 0x49, 0x44, 0xf1, 0x00, //0x00002c56 vpclmulqdq   $0, %xmm9, %xmm6, %xmm6
	0xc4, 0xe1, 0xf9, 0x7e, 0xf7, //0x00002c5c vmovq        %xmm6, %rdi
	0x4c, 0x31, 0xef, //0x00002c61 xorq         %r13, %rdi
	0xc4, 0xc1, 0x05, 0x74, 0xf5, //0x00002c64 vpcmpeqb     %ymm13, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xf6, //0x00002c69 vpmovmskb    %ymm6, %esi
	0xc4, 0xc1, 0x0d, 0x74, 0xf5, //0x00002c6d vpcmpeqb     %ymm13, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x00002c72 vpmovmskb    %ymm6, %eax
	0x48, 0xc1, 0xe0, 0x20, //0x00002c76 shlq         $32, %rax
	0x48, 0x09, 0xc6, //0x00002c7a orq          %rax, %rsi
	0x48, 0x89, 0xf9, //0x00002c7d movq         %rdi, %rcx
	0x48, 0xf7, 0xd1, //0x00002c80 notq         %rcx
	0x48, 0x21, 0xce, //0x00002c83 andq         %rcx, %rsi
	0xc5, 0x85, 0x74, 0xf5, //0x00002c86 vpcmpeqb     %ymm5, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x00002c8a vpmovmskb    %ymm6, %eax
	0xc5, 0x8d, 0x74, 0xf5, //0x00002c8e vpcmpeqb     %ymm5, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xde, //0x00002c92 vpmovmskb    %ymm6, %ebx
	0x48, 0xc1, 0xe3, 0x20, //0x00002c96 shlq         $32, %rbx
	0x48, 0x09, 0xd8, //0x00002c9a orq          %rbx, %rax
	0x48, 0x21, 0xc8, //0x00002c9d andq         %rcx, %rax
	0x0f, 0x84, 0x36, 0x00, 0x00, 0x00, //0x00002ca0 je           LBB0_560
	0x4c, 0x8b, 0x2c, 0x24, //0x00002ca6 movq         (%rsp), %r13
	0x4d, 0x89, 0xe1, //0x00002caa movq         %r12, %r9
	0x90, 0x90, 0x90, //0x00002cad .p2align 4, 0x90
	//0x00002cb0 LBB0_558
	0x48, 0x8d, 0x58, 0xff, //0x00002cb0 leaq         $-1(%rax), %rbx
	0x48, 0x89, 0xd9, //0x00002cb4 movq         %rbx, %rcx
	0x48, 0x21, 0xf1, //0x00002cb7 andq         %rsi, %rcx
	0xf3, 0x48, 0x0f, 0xb8, 0xc9, //0x00002cba popcntq      %rcx, %rcx
	0x4c, 0x01, 0xf9, //0x00002cbf addq         %r15, %rcx
	0x48, 0x39, 0xd1, //0x00002cc2 cmpq         %rdx, %rcx
	0x0f, 0x86, 0x3d, 0x00, 0x00, 0x00, //0x00002cc5 jbe          LBB0_577
	0x48, 0xff, 0xc2, //0x00002ccb incq         %rdx
	0x48, 0x21, 0xd8, //0x00002cce andq         %rbx, %rax
	0x0f, 0x85, 0xd9, 0xff, 0xff, 0xff, //0x00002cd1 jne          LBB0_558
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x00002cd7 jmp          LBB0_561
	//0x00002cdc LBB0_560
	0x4d, 0x89, 0xe1, //0x00002cdc movq         %r12, %r9
	//0x00002cdf LBB0_561
	0x48, 0xc1, 0xff, 0x3f, //0x00002cdf sarq         $63, %rdi
	0xf3, 0x48, 0x0f, 0xb8, 0xc6, //0x00002ce3 popcntq      %rsi, %rax
	0x49, 0x01, 0xc7, //0x00002ce8 addq         %rax, %r15
	0x49, 0x83, 0xc0, 0x40, //0x00002ceb addq         $64, %r8
	0x49, 0x83, 0xc3, 0xc0, //0x00002cef addq         $-64, %r11
	0x49, 0x89, 0xfd, //0x00002cf3 movq         %rdi, %r13
	0x4d, 0x89, 0xcc, //0x00002cf6 movq         %r9, %r12
	0x49, 0x83, 0xfb, 0x40, //0x00002cf9 cmpq         $64, %r11
	0x0f, 0x8d, 0xae, 0xfe, 0xff, 0xff, //0x00002cfd jge          LBB0_553
	0xe9, 0x85, 0xfd, 0xff, 0xff, //0x00002d03 jmp          LBB0_562
	//0x00002d08 LBB0_577
	0x48, 0x8b, 0x54, 0x24, 0x18, //0x00002d08 movq         $24(%rsp), %rdx
	0x48, 0x8b, 0x0a, //0x00002d0d movq         (%rdx), %rcx
	0x48, 0x0f, 0xbc, 0xc0, //0x00002d10 bsfq         %rax, %rax
	0x4c, 0x29, 0xd8, //0x00002d14 subq         %r11, %rax
	0x49, 0x89, 0xd3, //0x00002d17 movq         %rdx, %r11
	0x4c, 0x8d, 0x54, 0x08, 0x01, //0x00002d1a leaq         $1(%rax,%rcx), %r10
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x00002d1f movq         $8(%rsp), %r14
	0x4d, 0x89, 0x16, //0x00002d24 movq         %r10, (%r14)
	0x48, 0x8b, 0x02, //0x00002d27 movq         (%rdx), %rax
	0x49, 0x39, 0xc2, //0x00002d2a cmpq         %rax, %r10
	0x4c, 0x0f, 0x47, 0xd0, //0x00002d2d cmovaq       %rax, %r10
	0x4d, 0x89, 0x16, //0x00002d31 movq         %r10, (%r14)
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x00002d34 movq         $16(%rsp), %r15
	0xe9, 0x98, 0xf8, 0xff, 0xff, //0x00002d39 jmp          LBB0_491
	//0x00002d3e LBB0_582
	0x0f, 0xb7, 0xc6, //0x00002d3e movzwl       %si, %eax
	0xe9, 0x4d, 0xf7, 0xff, 0xff, //0x00002d41 jmp          LBB0_475
	//0x00002d46 LBB0_583
	0x4c, 0x89, 0xc2, //0x00002d46 movq         %r8, %rdx
	0x48, 0xf7, 0xd2, //0x00002d49 notq         %rdx
	0x48, 0x01, 0xd1, //0x00002d4c addq         %rdx, %rcx
	0x48, 0x39, 0xc1, //0x00002d4f cmpq         %rax, %rcx
	0x0f, 0x82, 0x40, 0xfa, 0xff, 0xff, //0x00002d52 jb           LBB0_522
	0xe9, 0x94, 0x05, 0x00, 0x00, //0x00002d58 jmp          LBB0_657
	//0x00002d5d LBB0_584
	0x4c, 0x89, 0xc2, //0x00002d5d movq         %r8, %rdx
	0x48, 0xf7, 0xd2, //0x00002d60 notq         %rdx
	0x48, 0x01, 0xd0, //0x00002d63 addq         %rdx, %rax
	0x48, 0x39, 0xc8, //0x00002d66 cmpq         %rcx, %rax
	0x0f, 0x82, 0xc9, 0xf5, 0xff, 0xff, //0x00002d69 jb           LBB0_454
	0xe9, 0x62, 0xf8, 0xff, 0xff, //0x00002d6f jmp          LBB0_491
	//0x00002d74 LBB0_485
	0x48, 0x85, 0xdb, //0x00002d74 testq        %rbx, %rbx
	0x0f, 0x85, 0xbd, 0x00, 0x00, 0x00, //0x00002d77 jne          LBB0_590
	0x4a, 0x8d, 0x44, 0x09, 0x01, //0x00002d7d leaq         $1(%rcx,%r9), %rax
	0x48, 0xf7, 0xd1, //0x00002d82 notq         %rcx
	0x4c, 0x01, 0xc1, //0x00002d85 addq         %r8, %rcx
	//0x00002d88 LBB0_487
	0x48, 0x85, 0xc9, //0x00002d88 testq        %rcx, %rcx
	0x48, 0x8b, 0x3c, 0x24, //0x00002d8b movq         (%rsp), %rdi
	0x4c, 0x8b, 0x4c, 0x24, 0x20, //0x00002d8f movq         $32(%rsp), %r9
	0x0f, 0x8e, 0x96, 0x00, 0x00, 0x00, //0x00002d94 jle          LBB0_589
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00002d9a movq         $24(%rsp), %r11
	0xe9, 0x18, 0x00, 0x00, 0x00, //0x00002d9f jmp          LBB0_579
	//0x00002da4 LBB0_578
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x00002da4 movq         $-2, %rdx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x00002dab movl         $2, %esi
	0x48, 0x01, 0xf0, //0x00002db0 addq         %rsi, %rax
	0x48, 0x01, 0xd1, //0x00002db3 addq         %rdx, %rcx
	0x0f, 0x8e, 0x1a, 0xf8, 0xff, 0xff, //0x00002db6 jle          LBB0_491
	//0x00002dbc LBB0_579
	0x0f, 0xb6, 0x10, //0x00002dbc movzbl       (%rax), %edx
	0x80, 0xfa, 0x5c, //0x00002dbf cmpb         $92, %dl
	0x0f, 0x84, 0xdc, 0xff, 0xff, 0xff, //0x00002dc2 je           LBB0_578
	0x80, 0xfa, 0x22, //0x00002dc8 cmpb         $34, %dl
	0x0f, 0x84, 0x1d, 0x00, 0x00, 0x00, //0x00002dcb je           LBB0_585
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00002dd1 movq         $-1, %rdx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00002dd8 movl         $1, %esi
	0x48, 0x01, 0xf0, //0x00002ddd addq         %rsi, %rax
	0x48, 0x01, 0xd1, //0x00002de0 addq         %rdx, %rcx
	0x0f, 0x8f, 0xd3, 0xff, 0xff, 0xff, //0x00002de3 jg           LBB0_579
	0xe9, 0xe8, 0xf7, 0xff, 0xff, //0x00002de9 jmp          LBB0_491
	//0x00002dee LBB0_585
	0x48, 0x29, 0xf8, //0x00002dee subq         %rdi, %rax
	0x48, 0xff, 0xc0, //0x00002df1 incq         %rax
	0xe9, 0xd7, 0xf7, 0xff, 0xff, //0x00002df4 jmp          LBB0_490
	//0x00002df9 LBB0_586
	0x4d, 0x29, 0xc2, //0x00002df9 subq         %r8, %r10
	0x4d, 0x89, 0x16, //0x00002dfc movq         %r10, (%r14)
	0xe9, 0xd2, 0xf7, 0xff, 0xff, //0x00002dff jmp          LBB0_491
	//0x00002e04 LBB0_587
	0x48, 0x8b, 0x04, 0x24, //0x00002e04 movq         (%rsp), %rax
	0x4c, 0x01, 0xd0, //0x00002e08 addq         %r10, %rax
	0xe9, 0x78, 0xff, 0xff, 0xff, //0x00002e0b jmp          LBB0_487
	//0x00002e10 LBB0_588
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00002e10 movq         $24(%rsp), %r11
	0x4d, 0x8b, 0x13, //0x00002e15 movq         (%r11), %r10
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x00002e18 movq         $8(%rsp), %r14
	0x4d, 0x89, 0x16, //0x00002e1d movq         %r10, (%r14)
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x00002e20 movq         $16(%rsp), %r15
	0x49, 0x89, 0xc5, //0x00002e25 movq         %rax, %r13
	0x4d, 0x89, 0xe1, //0x00002e28 movq         %r12, %r9
	0xe9, 0xa6, 0xf7, 0xff, 0xff, //0x00002e2b jmp          LBB0_491
	//0x00002e30 LBB0_589
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00002e30 movq         $24(%rsp), %r11
	0xe9, 0x9c, 0xf7, 0xff, 0xff, //0x00002e35 jmp          LBB0_491
	//0x00002e3a LBB0_590
	0x49, 0x8d, 0x40, 0xff, //0x00002e3a leaq         $-1(%r8), %rax
	0x48, 0x39, 0xc8, //0x00002e3e cmpq         %rcx, %rax
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x00002e41 jne          LBB0_593
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x00002e47 movq         $8(%rsp), %r14
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x00002e4c movq         $16(%rsp), %r15
	0xe9, 0x51, 0xf7, 0xff, 0xff, //0x00002e51 jmp          LBB0_592
	//0x00002e56 LBB0_593
	0x4a, 0x8d, 0x44, 0x09, 0x02, //0x00002e56 leaq         $2(%rcx,%r9), %rax
	0x49, 0x29, 0xc8, //0x00002e5b subq         %rcx, %r8
	0x49, 0x83, 0xc0, 0xfe, //0x00002e5e addq         $-2, %r8
	0x4c, 0x89, 0xc1, //0x00002e62 movq         %r8, %rcx
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x00002e65 movq         $8(%rsp), %r14
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x00002e6a movq         $16(%rsp), %r15
	0xe9, 0x14, 0xff, 0xff, 0xff, //0x00002e6f jmp          LBB0_487
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002e74 .p2align 4, 0x90
	//0x00002e80 LBB0_524
	0x49, 0x83, 0xc5, 0x10, //0x00002e80 addq         $16, %r13
	0x4c, 0x89, 0xd0, //0x00002e84 movq         %r10, %rax
	0x48, 0x8b, 0x8c, 0x24, 0xa0, 0x00, 0x00, 0x00, //0x00002e87 movq         $160(%rsp), %rcx
	0x49, 0x39, 0xcd, //0x00002e8f cmpq         %rcx, %r13
	0x4c, 0x8b, 0x54, 0x24, 0x28, //0x00002e92 movq         $40(%rsp), %r10
	0x0f, 0x85, 0x82, 0xd4, 0xff, 0xff, //0x00002e97 jne          LBB0_2
	0xe9, 0x31, 0x00, 0x00, 0x00, //0x00002e9d jmp          LBB0_525
	//0x00002ea2 LBB0_34
	0x4d, 0x89, 0xc8, //0x00002ea2 movq         %r9, %r8
	0x4c, 0x89, 0xce, //0x00002ea5 movq         %r9, %rsi
	0x48, 0xf7, 0xd6, //0x00002ea8 notq         %rsi
	0x48, 0x01, 0xf2, //0x00002eab addq         %rsi, %rdx
	0x48, 0x39, 0xca, //0x00002eae cmpq         %rcx, %rdx
	0x0f, 0x82, 0x61, 0xd6, 0xff, 0xff, //0x00002eb1 jb           LBB0_33
	0xe9, 0x04, 0xd6, 0xff, 0xff, //0x00002eb7 jmp          LBB0_35
	//0x00002ebc LBB0_594
	0x4c, 0x89, 0xc2, //0x00002ebc movq         %r8, %rdx
	0x48, 0xf7, 0xd2, //0x00002ebf notq         %rdx
	0x48, 0x01, 0xd1, //0x00002ec2 addq         %rdx, %rcx
	0x48, 0x39, 0xc1, //0x00002ec5 cmpq         %rax, %rcx
	0x0f, 0x82, 0x8b, 0xf2, 0xff, 0xff, //0x00002ec8 jb           LBB0_421
	0xe9, 0x98, 0xf2, 0xff, 0xff, //0x00002ece jmp          LBB0_422
	//0x00002ed3 LBB0_525
	0x4d, 0x85, 0xd2, //0x00002ed3 testq        %r10, %r10
	0x0f, 0x84, 0x7c, 0x00, 0x00, 0x00, //0x00002ed6 je           LBB0_595
	0x41, 0xb9, 0x01, 0x00, 0x00, 0x00, //0x00002edc movl         $1, %r9d
	0xc4, 0xc1, 0xf9, 0x6e, 0xc1, //0x00002ee2 vmovq        %r9, %xmm0
	0xc4, 0xc1, 0x7a, 0x7f, 0x02, //0x00002ee7 vmovdqu      %xmm0, (%r10)
	0x4d, 0x8b, 0x2e, //0x00002eec movq         (%r14), %r13
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002eef movq         $-1, %r8
	0xc5, 0xfe, 0x6f, 0x2d, 0x02, 0xd1, 0xff, 0xff, //0x00002ef6 vmovdqu      $-12030(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x7a, 0xd1, 0xff, 0xff, //0x00002efe vmovdqu      $-11910(%rip), %ymm6  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x92, 0xd1, 0xff, 0xff, //0x00002f06 vmovdqu      $-11886(%rip), %ymm7  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x0a, 0xd2, 0xff, 0xff, //0x00002f0e vmovdqu      $-11766(%rip), %ymm8  /* LCPI0_12+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0x22, 0xd2, 0xff, 0xff, //0x00002f16 vmovdqu      $-11742(%rip), %ymm9  /* LCPI0_13+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0x3a, 0xd2, 0xff, 0xff, //0x00002f1e vmovdqu      $-11718(%rip), %ymm10  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x52, 0xd2, 0xff, 0xff, //0x00002f26 vmovdqu      $-11694(%rip), %ymm11  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x0a, 0xd1, 0xff, 0xff, //0x00002f2e vmovdqu      $-12022(%rip), %ymm12  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x62, 0xd2, 0xff, 0xff, //0x00002f36 vmovdqu      $-11678(%rip), %ymm13  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x7a, 0xd2, 0xff, 0xff, //0x00002f3e vmovdqu      $-11654(%rip), %ymm14  /* LCPI0_17+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x3d, 0xc2, 0xd2, 0xff, 0xff, //0x00002f46 vmovdqu      $-11582(%rip), %xmm15  /* LCPI0_18+0(%rip) */
	0x4c, 0x89, 0x54, 0x24, 0x28, //0x00002f4e movq         %r10, $40(%rsp)
	0xe9, 0xea, 0x03, 0x00, 0x00, //0x00002f53 jmp          LBB0_664
	//0x00002f58 LBB0_595
	0x4d, 0x8b, 0x1f, //0x00002f58 movq         (%r15), %r11
	0x49, 0x8b, 0x5f, 0x08, //0x00002f5b movq         $8(%r15), %rbx
	0x49, 0x8b, 0x0e, //0x00002f5f movq         (%r14), %rcx
	0x48, 0x39, 0xd9, //0x00002f62 cmpq         %rbx, %rcx
	0x0f, 0x83, 0x26, 0x00, 0x00, 0x00, //0x00002f65 jae          LBB0_600
	0x41, 0x8a, 0x04, 0x0b, //0x00002f6b movb         (%r11,%rcx), %al
	0x3c, 0x0d, //0x00002f6f cmpb         $13, %al
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00002f71 je           LBB0_600
	0x3c, 0x20, //0x00002f77 cmpb         $32, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00002f79 je           LBB0_600
	0x04, 0xf7, //0x00002f7f addb         $-9, %al
	0x3c, 0x01, //0x00002f81 cmpb         $1, %al
	0x0f, 0x86, 0x08, 0x00, 0x00, 0x00, //0x00002f83 jbe          LBB0_600
	0x48, 0x89, 0xc8, //0x00002f89 movq         %rcx, %rax
	0xe9, 0xd1, 0x01, 0x00, 0x00, //0x00002f8c jmp          LBB0_634
	//0x00002f91 LBB0_600
	0x48, 0x8d, 0x41, 0x01, //0x00002f91 leaq         $1(%rcx), %rax
	0x48, 0x39, 0xd8, //0x00002f95 cmpq         %rbx, %rax
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x00002f98 jae          LBB0_604
	0x41, 0x8a, 0x14, 0x03, //0x00002f9e movb         (%r11,%rax), %dl
	0x80, 0xfa, 0x0d, //0x00002fa2 cmpb         $13, %dl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00002fa5 je           LBB0_604
	0x80, 0xfa, 0x20, //0x00002fab cmpb         $32, %dl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x00002fae je           LBB0_604
	0x80, 0xc2, 0xf7, //0x00002fb4 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00002fb7 cmpb         $1, %dl
	0x0f, 0x87, 0xa2, 0x01, 0x00, 0x00, //0x00002fba ja           LBB0_634
	//0x00002fc0 LBB0_604
	0x48, 0x8d, 0x41, 0x02, //0x00002fc0 leaq         $2(%rcx), %rax
	0x48, 0x39, 0xd8, //0x00002fc4 cmpq         %rbx, %rax
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x00002fc7 jae          LBB0_608
	0x41, 0x8a, 0x14, 0x03, //0x00002fcd movb         (%r11,%rax), %dl
	0x80, 0xfa, 0x0d, //0x00002fd1 cmpb         $13, %dl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00002fd4 je           LBB0_608
	0x80, 0xfa, 0x20, //0x00002fda cmpb         $32, %dl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x00002fdd je           LBB0_608
	0x80, 0xc2, 0xf7, //0x00002fe3 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00002fe6 cmpb         $1, %dl
	0x0f, 0x87, 0x73, 0x01, 0x00, 0x00, //0x00002fe9 ja           LBB0_634
	//0x00002fef LBB0_608
	0x48, 0x8d, 0x41, 0x03, //0x00002fef leaq         $3(%rcx), %rax
	0x48, 0x39, 0xd8, //0x00002ff3 cmpq         %rbx, %rax
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x00002ff6 jae          LBB0_612
	0x41, 0x8a, 0x14, 0x03, //0x00002ffc movb         (%r11,%rax), %dl
	0x80, 0xfa, 0x0d, //0x00003000 cmpb         $13, %dl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00003003 je           LBB0_612
	0x80, 0xfa, 0x20, //0x00003009 cmpb         $32, %dl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000300c je           LBB0_612
	0x80, 0xc2, 0xf7, //0x00003012 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00003015 cmpb         $1, %dl
	0x0f, 0x87, 0x44, 0x01, 0x00, 0x00, //0x00003018 ja           LBB0_634
	//0x0000301e LBB0_612
	0x48, 0x8d, 0x41, 0x04, //0x0000301e leaq         $4(%rcx), %rax
	0x48, 0x89, 0xda, //0x00003022 movq         %rbx, %rdx
	0x48, 0x29, 0xc2, //0x00003025 subq         %rax, %rdx
	0x0f, 0x86, 0x06, 0x01, 0x00, 0x00, //0x00003028 jbe          LBB0_632
	0x4c, 0x01, 0xd8, //0x0000302e addq         %r11, %rax
	0x48, 0x83, 0xfa, 0x20, //0x00003031 cmpq         $32, %rdx
	0x0f, 0x82, 0x57, 0x00, 0x00, 0x00, //0x00003035 jb           LBB0_618
	0x48, 0x89, 0xde, //0x0000303b movq         %rbx, %rsi
	0x48, 0x29, 0xce, //0x0000303e subq         %rcx, %rsi
	0x48, 0x83, 0xc6, 0xdc, //0x00003041 addq         $-36, %rsi
	0x48, 0x89, 0xf7, //0x00003045 movq         %rsi, %rdi
	0x48, 0x83, 0xe7, 0xe0, //0x00003048 andq         $-32, %rdi
	0x48, 0x01, 0xcf, //0x0000304c addq         %rcx, %rdi
	0x49, 0x8d, 0x4c, 0x3b, 0x24, //0x0000304f leaq         $36(%r11,%rdi), %rcx
	0x83, 0xe6, 0x1f, //0x00003054 andl         $31, %esi
	0xc5, 0xfe, 0x6f, 0x05, 0xa1, 0xcf, 0xff, 0xff, //0x00003057 vmovdqu      $-12383(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	0x90, //0x0000305f .p2align 4, 0x90
	//0x00003060 LBB0_615
	0xc5, 0xfe, 0x6f, 0x08, //0x00003060 vmovdqu      (%rax), %ymm1
	0xc4, 0xe2, 0x7d, 0x00, 0xd1, //0x00003064 vpshufb      %ymm1, %ymm0, %ymm2
	0xc5, 0xf5, 0x74, 0xca, //0x00003069 vpcmpeqb     %ymm2, %ymm1, %ymm1
	0xc5, 0xfd, 0xd7, 0xf9, //0x0000306d vpmovmskb    %ymm1, %edi
	0x83, 0xff, 0xff, //0x00003071 cmpl         $-1, %edi
	0x0f, 0x85, 0xc9, 0x00, 0x00, 0x00, //0x00003074 jne          LBB0_633
	0x48, 0x83, 0xc0, 0x20, //0x0000307a addq         $32, %rax
	0x48, 0x83, 0xc2, 0xe0, //0x0000307e addq         $-32, %rdx
	0x48, 0x83, 0xfa, 0x1f, //0x00003082 cmpq         $31, %rdx
	0x0f, 0x87, 0xd4, 0xff, 0xff, 0xff, //0x00003086 ja           LBB0_615
	0x48, 0x89, 0xf2, //0x0000308c movq         %rsi, %rdx
	0x48, 0x89, 0xc8, //0x0000308f movq         %rcx, %rax
	//0x00003092 LBB0_618
	0x48, 0x85, 0xd2, //0x00003092 testq        %rdx, %rdx
	0x0f, 0x84, 0x3a, 0x00, 0x00, 0x00, //0x00003095 je           LBB0_624
	0x48, 0x8d, 0x0c, 0x10, //0x0000309b leaq         (%rax,%rdx), %rcx
	0x48, 0xff, 0xc0, //0x0000309f incq         %rax
	0x48, 0xbe, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000030a2 movabsq      $4294977024, %rsi
	//0x000030ac LBB0_620
	0x0f, 0xbe, 0x78, 0xff, //0x000030ac movsbl       $-1(%rax), %edi
	0x83, 0xff, 0x20, //0x000030b0 cmpl         $32, %edi
	0x0f, 0x87, 0xc9, 0x25, 0x00, 0x00, //0x000030b3 ja           LBB0_1108
	0x48, 0x0f, 0xa3, 0xfe, //0x000030b9 btq          %rdi, %rsi
	0x0f, 0x83, 0xbf, 0x25, 0x00, 0x00, //0x000030bd jae          LBB0_1108
	0x48, 0xff, 0xca, //0x000030c3 decq         %rdx
	0x48, 0xff, 0xc0, //0x000030c6 incq         %rax
	0x48, 0x85, 0xd2, //0x000030c9 testq        %rdx, %rdx
	0x0f, 0x85, 0xda, 0xff, 0xff, 0xff, //0x000030cc jne          LBB0_620
	0x48, 0x89, 0xc8, //0x000030d2 movq         %rcx, %rax
	//0x000030d5 LBB0_624
	0x4c, 0x29, 0xd8, //0x000030d5 subq         %r11, %rax
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000030d8 movq         $-1, %rcx
	0x48, 0x39, 0xd8, //0x000030df cmpq         %rbx, %rax
	0x0f, 0x82, 0x7a, 0x00, 0x00, 0x00, //0x000030e2 jb           LBB0_634
	0xe9, 0x11, 0x02, 0x00, 0x00, //0x000030e8 jmp          LBB0_659
	//0x000030ed LBB0_625
	0x49, 0x89, 0x0e, //0x000030ed movq         %rcx, (%r14)
	0x49, 0x89, 0xca, //0x000030f0 movq         %rcx, %r10
	0xe9, 0xf9, 0x01, 0x00, 0x00, //0x000030f3 jmp          LBB0_657
	//0x000030f8 LBB0_626
	0x3c, 0x5d, //0x000030f8 cmpb         $93, %al
	0x0f, 0x84, 0x22, 0x00, 0x00, 0x00, //0x000030fa je           LBB0_631
	0xe9, 0xec, 0x01, 0x00, 0x00, //0x00003100 jmp          LBB0_657
	//0x00003105 LBB0_628
	0x49, 0xff, 0xca, //0x00003105 decq         %r10
	0x4d, 0x89, 0x16, //0x00003108 movq         %r10, (%r14)
	0x48, 0xc7, 0xc1, 0xde, 0xff, 0xff, 0xff, //0x0000310b movq         $-34, %rcx
	0xe9, 0xe7, 0x01, 0x00, 0x00, //0x00003112 jmp          LBB0_659
	//0x00003117 LBB0_629
	0x4d, 0x89, 0xca, //0x00003117 movq         %r9, %r10
	//0x0000311a LBB0_630
	0x3c, 0x7d, //0x0000311a cmpb         $125, %al
	0x0f, 0x85, 0xcf, 0x01, 0x00, 0x00, //0x0000311c jne          LBB0_657
	//0x00003122 LBB0_631
	0x49, 0xff, 0xca, //0x00003122 decq         %r10
	0x4d, 0x89, 0x16, //0x00003125 movq         %r10, (%r14)
	0x48, 0xc7, 0xc1, 0xdf, 0xff, 0xff, 0xff, //0x00003128 movq         $-33, %rcx
	0xe9, 0xca, 0x01, 0x00, 0x00, //0x0000312f jmp          LBB0_659
	//0x00003134 LBB0_632
	0x49, 0x89, 0x06, //0x00003134 movq         %rax, (%r14)
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00003137 movq         $-1, %rcx
	0xe9, 0xbb, 0x01, 0x00, 0x00, //0x0000313e jmp          LBB0_659
	//0x00003143 LBB0_633
	0x4c, 0x29, 0xd8, //0x00003143 subq         %r11, %rax
	0xf7, 0xd7, //0x00003146 notl         %edi
	0x48, 0x63, 0xcf, //0x00003148 movslq       %edi, %rcx
	0x48, 0x0f, 0xbc, 0xc9, //0x0000314b bsfq         %rcx, %rcx
	0x48, 0x01, 0xc8, //0x0000314f addq         %rcx, %rax
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00003152 movq         $-1, %rcx
	0x48, 0x39, 0xd8, //0x00003159 cmpq         %rbx, %rax
	0x0f, 0x83, 0x9c, 0x01, 0x00, 0x00, //0x0000315c jae          LBB0_659
	//0x00003162 LBB0_634
	0x48, 0x8d, 0x58, 0x01, //0x00003162 leaq         $1(%rax), %rbx
	0x49, 0x89, 0x1e, //0x00003166 movq         %rbx, (%r14)
	0x41, 0x0f, 0xbe, 0x14, 0x03, //0x00003169 movsbl       (%r11,%rax), %edx
	0x83, 0xfa, 0x7b, //0x0000316e cmpl         $123, %edx
	0x0f, 0x87, 0x53, 0x23, 0x00, 0x00, //0x00003171 ja           LBB0_1087
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00003177 movq         $-1, %rcx
	0x48, 0x8d, 0x35, 0xa3, 0x2d, 0x00, 0x00, //0x0000317e leaq         $11683(%rip), %rsi  /* LJTI0_6+0(%rip) */
	0x48, 0x63, 0x14, 0x96, //0x00003185 movslq       (%rsi,%rdx,4), %rdx
	0x48, 0x01, 0xf2, //0x00003189 addq         %rsi, %rdx
	0xff, 0xe2, //0x0000318c jmpq         *%rdx
	//0x0000318e LBB0_636
	0x49, 0x8b, 0x57, 0x08, //0x0000318e movq         $8(%r15), %rdx
	0x48, 0x89, 0xd1, //0x00003192 movq         %rdx, %rcx
	0x48, 0x29, 0xd9, //0x00003195 subq         %rbx, %rcx
	0x4c, 0x01, 0xdb, //0x00003198 addq         %r11, %rbx
	0x48, 0x83, 0xf9, 0x20, //0x0000319b cmpq         $32, %rcx
	0x0f, 0x82, 0x73, 0x00, 0x00, 0x00, //0x0000319f jb           LBB0_641
	0x48, 0x29, 0xc2, //0x000031a5 subq         %rax, %rdx
	0x48, 0x83, 0xc2, 0xdf, //0x000031a8 addq         $-33, %rdx
	0x48, 0x89, 0xd6, //0x000031ac movq         %rdx, %rsi
	0x48, 0x83, 0xe6, 0xe0, //0x000031af andq         $-32, %rsi
	0x48, 0x01, 0xc6, //0x000031b3 addq         %rax, %rsi
	0x49, 0x8d, 0x74, 0x33, 0x21, //0x000031b6 leaq         $33(%r11,%rsi), %rsi
	0x83, 0xe2, 0x1f, //0x000031bb andl         $31, %edx
	0xc5, 0xfe, 0x6f, 0x05, 0x5a, 0xce, 0xff, 0xff, //0x000031be vmovdqu      $-12710(%rip), %ymm0  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0x72, 0xce, 0xff, 0xff, //0x000031c6 vmovdqu      $-12686(%rip), %ymm1  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x15, 0x8a, 0xce, 0xff, 0xff, //0x000031ce vmovdqu      $-12662(%rip), %ymm2  /* LCPI0_3+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000031d6 .p2align 4, 0x90
	//0x000031e0 LBB0_638
	0xc5, 0xfe, 0x6f, 0x1b, //0x000031e0 vmovdqu      (%rbx), %ymm3
	0xc5, 0xe5, 0x74, 0xe0, //0x000031e4 vpcmpeqb     %ymm0, %ymm3, %ymm4
	0xc5, 0xe5, 0xeb, 0xd9, //0x000031e8 vpor         %ymm1, %ymm3, %ymm3
	0xc5, 0xe5, 0x74, 0xda, //0x000031ec vpcmpeqb     %ymm2, %ymm3, %ymm3
	0xc5, 0xe5, 0xeb, 0xdc, //0x000031f0 vpor         %ymm4, %ymm3, %ymm3
	0xc5, 0xfd, 0xd7, 0xfb, //0x000031f4 vpmovmskb    %ymm3, %edi
	0x85, 0xff, //0x000031f8 testl        %edi, %edi
	0x0f, 0x85, 0xd3, 0x00, 0x00, 0x00, //0x000031fa jne          LBB0_654
	0x48, 0x83, 0xc3, 0x20, //0x00003200 addq         $32, %rbx
	0x48, 0x83, 0xc1, 0xe0, //0x00003204 addq         $-32, %rcx
	0x48, 0x83, 0xf9, 0x1f, //0x00003208 cmpq         $31, %rcx
	0x0f, 0x87, 0xce, 0xff, 0xff, 0xff, //0x0000320c ja           LBB0_638
	0x48, 0x89, 0xd1, //0x00003212 movq         %rdx, %rcx
	0x48, 0x89, 0xf3, //0x00003215 movq         %rsi, %rbx
	//0x00003218 LBB0_641
	0x48, 0x83, 0xf9, 0x10, //0x00003218 cmpq         $16, %rcx
	0x0f, 0x82, 0x64, 0x00, 0x00, 0x00, //0x0000321c jb           LBB0_646
	0x48, 0x8d, 0x51, 0xf0, //0x00003222 leaq         $-16(%rcx), %rdx
	0x48, 0x89, 0xd6, //0x00003226 movq         %rdx, %rsi
	0x48, 0x83, 0xe6, 0xf0, //0x00003229 andq         $-16, %rsi
	0x48, 0x8d, 0x74, 0x1e, 0x10, //0x0000322d leaq         $16(%rsi,%rbx), %rsi
	0x83, 0xe2, 0x0f, //0x00003232 andl         $15, %edx
	0xc5, 0xfa, 0x6f, 0x05, 0xa3, 0xcf, 0xff, 0xff, //0x00003235 vmovdqu      $-12381(%rip), %xmm0  /* LCPI0_4+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x0d, 0xab, 0xcf, 0xff, 0xff, //0x0000323d vmovdqu      $-12373(%rip), %xmm1  /* LCPI0_5+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x15, 0xb3, 0xcf, 0xff, 0xff, //0x00003245 vmovdqu      $-12365(%rip), %xmm2  /* LCPI0_6+0(%rip) */
	//0x0000324d LBB0_643
	0xc5, 0xfa, 0x6f, 0x1b, //0x0000324d vmovdqu      (%rbx), %xmm3
	0xc5, 0xe1, 0x74, 0xe0, //0x00003251 vpcmpeqb     %xmm0, %xmm3, %xmm4
	0xc5, 0xe1, 0xeb, 0xd9, //0x00003255 vpor         %xmm1, %xmm3, %xmm3
	0xc5, 0xe1, 0x74, 0xda, //0x00003259 vpcmpeqb     %xmm2, %xmm3, %xmm3
	0xc5, 0xe1, 0xeb, 0xdc, //0x0000325d vpor         %xmm4, %xmm3, %xmm3
	0xc5, 0xf9, 0xd7, 0xfb, //0x00003261 vpmovmskb    %xmm3, %edi
	0x66, 0x85, 0xff, //0x00003265 testw        %di, %di
	0x0f, 0x85, 0xe8, 0x22, 0x00, 0x00, //0x00003268 jne          LBB0_1095
	0x48, 0x83, 0xc3, 0x10, //0x0000326e addq         $16, %rbx
	0x48, 0x83, 0xc1, 0xf0, //0x00003272 addq         $-16, %rcx
	0x48, 0x83, 0xf9, 0x0f, //0x00003276 cmpq         $15, %rcx
	0x0f, 0x87, 0xcd, 0xff, 0xff, 0xff, //0x0000327a ja           LBB0_643
	0x48, 0x89, 0xd1, //0x00003280 movq         %rdx, %rcx
	0x48, 0x89, 0xf3, //0x00003283 movq         %rsi, %rbx
	//0x00003286 LBB0_646
	0x48, 0x85, 0xc9, //0x00003286 testq        %rcx, %rcx
	0x0f, 0x84, 0x31, 0x00, 0x00, 0x00, //0x00003289 je           LBB0_653
	0x48, 0x8d, 0x34, 0x0b, //0x0000328f leaq         (%rbx,%rcx), %rsi
	//0x00003293 LBB0_648
	0x0f, 0xb6, 0x13, //0x00003293 movzbl       (%rbx), %edx
	0x80, 0xfa, 0x2c, //0x00003296 cmpb         $44, %dl
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x00003299 je           LBB0_653
	0x80, 0xfa, 0x7d, //0x0000329f cmpb         $125, %dl
	0x0f, 0x84, 0x18, 0x00, 0x00, 0x00, //0x000032a2 je           LBB0_653
	0x80, 0xfa, 0x5d, //0x000032a8 cmpb         $93, %dl
	0x0f, 0x84, 0x0f, 0x00, 0x00, 0x00, //0x000032ab je           LBB0_653
	0x48, 0xff, 0xc3, //0x000032b1 incq         %rbx
	0x48, 0xff, 0xc9, //0x000032b4 decq         %rcx
	0x0f, 0x85, 0xd6, 0xff, 0xff, 0xff, //0x000032b7 jne          LBB0_648
	0x48, 0x89, 0xf3, //0x000032bd movq         %rsi, %rbx
	//0x000032c0 LBB0_653
	0x4c, 0x29, 0xdb, //0x000032c0 subq         %r11, %rbx
	0x48, 0x8b, 0x4c, 0x24, 0x08, //0x000032c3 movq         $8(%rsp), %rcx
	0x48, 0x89, 0x19, //0x000032c8 movq         %rbx, (%rcx)
	0x48, 0x89, 0xc1, //0x000032cb movq         %rax, %rcx
	0xe9, 0x2b, 0x00, 0x00, 0x00, //0x000032ce jmp          LBB0_659
	//0x000032d3 LBB0_654
	0x48, 0x63, 0xcf, //0x000032d3 movslq       %edi, %rcx
	//0x000032d6 LBB0_655
	0x48, 0x0f, 0xbc, 0xc9, //0x000032d6 bsfq         %rcx, %rcx
	0x4c, 0x29, 0xdb, //0x000032da subq         %r11, %rbx
	0x48, 0x01, 0xcb, //0x000032dd addq         %rcx, %rbx
	0x49, 0x89, 0x1e, //0x000032e0 movq         %rbx, (%r14)
	0x48, 0x89, 0xc1, //0x000032e3 movq         %rax, %rcx
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x000032e6 jmp          LBB0_659
	//0x000032eb LBB0_656
	0x49, 0x89, 0x06, //0x000032eb movq         %rax, (%r14)
	0x49, 0x89, 0xc2, //0x000032ee movq         %rax, %r10
	//0x000032f1 LBB0_657
	0x49, 0xff, 0xca, //0x000032f1 decq         %r10
	0x4d, 0x89, 0x16, //0x000032f4 movq         %r10, (%r14)
	//0x000032f7 LBB0_658
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x000032f7 movq         $-2, %rcx
	//0x000032fe LBB0_659
	0x48, 0x89, 0xc8, //0x000032fe movq         %rcx, %rax
	0x48, 0x8d, 0x65, 0xd8, //0x00003301 leaq         $-40(%rbp), %rsp
	0x5b, //0x00003305 popq         %rbx
	0x41, 0x5c, //0x00003306 popq         %r12
	0x41, 0x5d, //0x00003308 popq         %r13
	0x41, 0x5e, //0x0000330a popq         %r14
	0x41, 0x5f, //0x0000330c popq         %r15
	0x5d, //0x0000330e popq         %rbp
	0xc5, 0xf8, 0x77, //0x0000330f vzeroupper   
	0xc3, //0x00003312 retq         
	//0x00003313 LBB0_660
	0x4c, 0x89, 0xe8, //0x00003313 movq         %r13, %rax
	0x4d, 0x8d, 0x6b, 0x04, //0x00003316 leaq         $4(%r11), %r13
	0x4d, 0x89, 0x2e, //0x0000331a movq         %r13, (%r14)
	0x4c, 0x89, 0xd9, //0x0000331d movq         %r11, %rcx
	0x48, 0x85, 0xc0, //0x00003320 testq        %rax, %rax
	0x0f, 0x8e, 0xd5, 0xff, 0xff, 0xff, //0x00003323 jle          LBB0_659
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003329 .p2align 4, 0x90
	//0x00003330 LBB0_662
	0x49, 0x8b, 0x32, //0x00003330 movq         (%r10), %rsi
	0x49, 0x89, 0xf1, //0x00003333 movq         %rsi, %r9
	0x4c, 0x89, 0xc1, //0x00003336 movq         %r8, %rcx
	0x48, 0x85, 0xf6, //0x00003339 testq        %rsi, %rsi
	0x0f, 0x84, 0xbc, 0xff, 0xff, 0xff, //0x0000333c je           LBB0_659
	//0x00003342 LBB0_664
	0x4d, 0x8b, 0x27, //0x00003342 movq         (%r15), %r12
	0x49, 0x8b, 0x57, 0x08, //0x00003345 movq         $8(%r15), %rdx
	0x49, 0x39, 0xd5, //0x00003349 cmpq         %rdx, %r13
	0x0f, 0x83, 0x2e, 0x00, 0x00, 0x00, //0x0000334c jae          LBB0_669
	0x43, 0x8a, 0x04, 0x2c, //0x00003352 movb         (%r12,%r13), %al
	0x3c, 0x0d, //0x00003356 cmpb         $13, %al
	0x0f, 0x84, 0x22, 0x00, 0x00, 0x00, //0x00003358 je           LBB0_669
	0x3c, 0x20, //0x0000335e cmpb         $32, %al
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00003360 je           LBB0_669
	0x04, 0xf7, //0x00003366 addb         $-9, %al
	0x3c, 0x01, //0x00003368 cmpb         $1, %al
	0x0f, 0x86, 0x10, 0x00, 0x00, 0x00, //0x0000336a jbe          LBB0_669
	0x4d, 0x89, 0xeb, //0x00003370 movq         %r13, %r11
	0xe9, 0x83, 0x01, 0x00, 0x00, //0x00003373 jmp          LBB0_695
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003378 .p2align 4, 0x90
	//0x00003380 LBB0_669
	0x4d, 0x8d, 0x5d, 0x01, //0x00003380 leaq         $1(%r13), %r11
	0x49, 0x39, 0xd3, //0x00003384 cmpq         %rdx, %r11
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00003387 jae          LBB0_673
	0x43, 0x8a, 0x1c, 0x1c, //0x0000338d movb         (%r12,%r11), %bl
	0x80, 0xfb, 0x0d, //0x00003391 cmpb         $13, %bl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00003394 je           LBB0_673
	0x80, 0xfb, 0x20, //0x0000339a cmpb         $32, %bl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000339d je           LBB0_673
	0x80, 0xc3, 0xf7, //0x000033a3 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x000033a6 cmpb         $1, %bl
	0x0f, 0x87, 0x4c, 0x01, 0x00, 0x00, //0x000033a9 ja           LBB0_695
	0x90, //0x000033af .p2align 4, 0x90
	//0x000033b0 LBB0_673
	0x4d, 0x8d, 0x5d, 0x02, //0x000033b0 leaq         $2(%r13), %r11
	0x49, 0x39, 0xd3, //0x000033b4 cmpq         %rdx, %r11
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000033b7 jae          LBB0_677
	0x43, 0x8a, 0x1c, 0x1c, //0x000033bd movb         (%r12,%r11), %bl
	0x80, 0xfb, 0x0d, //0x000033c1 cmpb         $13, %bl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000033c4 je           LBB0_677
	0x80, 0xfb, 0x20, //0x000033ca cmpb         $32, %bl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000033cd je           LBB0_677
	0x80, 0xc3, 0xf7, //0x000033d3 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x000033d6 cmpb         $1, %bl
	0x0f, 0x87, 0x1c, 0x01, 0x00, 0x00, //0x000033d9 ja           LBB0_695
	0x90, //0x000033df .p2align 4, 0x90
	//0x000033e0 LBB0_677
	0x4d, 0x8d, 0x5d, 0x03, //0x000033e0 leaq         $3(%r13), %r11
	0x49, 0x39, 0xd3, //0x000033e4 cmpq         %rdx, %r11
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000033e7 jae          LBB0_681
	0x43, 0x8a, 0x1c, 0x1c, //0x000033ed movb         (%r12,%r11), %bl
	0x80, 0xfb, 0x0d, //0x000033f1 cmpb         $13, %bl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000033f4 je           LBB0_681
	0x80, 0xfb, 0x20, //0x000033fa cmpb         $32, %bl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000033fd je           LBB0_681
	0x80, 0xc3, 0xf7, //0x00003403 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00003406 cmpb         $1, %bl
	0x0f, 0x87, 0xec, 0x00, 0x00, 0x00, //0x00003409 ja           LBB0_695
	0x90, //0x0000340f .p2align 4, 0x90
	//0x00003410 LBB0_681
	0x4d, 0x8d, 0x5d, 0x04, //0x00003410 leaq         $4(%r13), %r11
	0x48, 0x89, 0xd6, //0x00003414 movq         %rdx, %rsi
	0x4c, 0x29, 0xde, //0x00003417 subq         %r11, %rsi
	0x0f, 0x86, 0x82, 0x19, 0x00, 0x00, //0x0000341a jbe          LBB0_1013
	0x4d, 0x01, 0xe3, //0x00003420 addq         %r12, %r11
	0x48, 0x83, 0xfe, 0x20, //0x00003423 cmpq         $32, %rsi
	0x0f, 0x82, 0x59, 0x00, 0x00, 0x00, //0x00003427 jb           LBB0_687
	0x4c, 0x89, 0xc0, //0x0000342d movq         %r8, %rax
	0x48, 0x89, 0xd1, //0x00003430 movq         %rdx, %rcx
	0x4c, 0x29, 0xe9, //0x00003433 subq         %r13, %rcx
	0x48, 0x83, 0xc1, 0xdc, //0x00003436 addq         $-36, %rcx
	0x48, 0x89, 0xcf, //0x0000343a movq         %rcx, %rdi
	0x48, 0x83, 0xe7, 0xe0, //0x0000343d andq         $-32, %rdi
	0x4c, 0x01, 0xef, //0x00003441 addq         %r13, %rdi
	0x4d, 0x8d, 0x44, 0x3c, 0x24, //0x00003444 leaq         $36(%r12,%rdi), %r8
	0x83, 0xe1, 0x1f, //0x00003449 andl         $31, %ecx
	0x90, 0x90, 0x90, 0x90, //0x0000344c .p2align 4, 0x90
	//0x00003450 LBB0_684
	0xc4, 0xc1, 0x7e, 0x6f, 0x03, //0x00003450 vmovdqu      (%r11), %ymm0
	0xc4, 0xe2, 0x55, 0x00, 0xc8, //0x00003455 vpshufb      %ymm0, %ymm5, %ymm1
	0xc5, 0xfd, 0x74, 0xc1, //0x0000345a vpcmpeqb     %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd8, //0x0000345e vpmovmskb    %ymm0, %ebx
	0x83, 0xfb, 0xff, //0x00003462 cmpl         $-1, %ebx
	0x0f, 0x85, 0x75, 0x00, 0x00, 0x00, //0x00003465 jne          LBB0_694
	0x49, 0x83, 0xc3, 0x20, //0x0000346b addq         $32, %r11
	0x48, 0x83, 0xc6, 0xe0, //0x0000346f addq         $-32, %rsi
	0x48, 0x83, 0xfe, 0x1f, //0x00003473 cmpq         $31, %rsi
	0x0f, 0x87, 0xd3, 0xff, 0xff, 0xff, //0x00003477 ja           LBB0_684
	0x48, 0x89, 0xce, //0x0000347d movq         %rcx, %rsi
	0x4d, 0x89, 0xc3, //0x00003480 movq         %r8, %r11
	0x49, 0x89, 0xc0, //0x00003483 movq         %rax, %r8
	//0x00003486 LBB0_687
	0x48, 0x85, 0xf6, //0x00003486 testq        %rsi, %rsi
	0x48, 0xb8, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00003489 movabsq      $4294977024, %rax
	0x0f, 0x84, 0x31, 0x00, 0x00, 0x00, //0x00003493 je           LBB0_693
	0x49, 0x8d, 0x3c, 0x33, //0x00003499 leaq         (%r11,%rsi), %rdi
	0x49, 0xff, 0xc3, //0x0000349d incq         %r11
	//0x000034a0 LBB0_689
	0x41, 0x0f, 0xbe, 0x5b, 0xff, //0x000034a0 movsbl       $-1(%r11), %ebx
	0x83, 0xfb, 0x20, //0x000034a5 cmpl         $32, %ebx
	0x0f, 0x87, 0xcf, 0x0c, 0x00, 0x00, //0x000034a8 ja           LBB0_869
	0x48, 0x0f, 0xa3, 0xd8, //0x000034ae btq          %rbx, %rax
	0x0f, 0x83, 0xc5, 0x0c, 0x00, 0x00, //0x000034b2 jae          LBB0_869
	0x48, 0xff, 0xce, //0x000034b8 decq         %rsi
	0x49, 0xff, 0xc3, //0x000034bb incq         %r11
	0x48, 0x85, 0xf6, //0x000034be testq        %rsi, %rsi
	0x0f, 0x85, 0xd9, 0xff, 0xff, 0xff, //0x000034c1 jne          LBB0_689
	0x49, 0x89, 0xfb, //0x000034c7 movq         %rdi, %r11
	//0x000034ca LBB0_693
	0x4d, 0x29, 0xe3, //0x000034ca subq         %r12, %r11
	0x49, 0x39, 0xd3, //0x000034cd cmpq         %rdx, %r11
	0x0f, 0x82, 0x25, 0x00, 0x00, 0x00, //0x000034d0 jb           LBB0_695
	0xe9, 0xca, 0x18, 0x00, 0x00, //0x000034d6 jmp          LBB0_1014
	0x90, 0x90, 0x90, 0x90, 0x90, //0x000034db .p2align 4, 0x90
	//0x000034e0 LBB0_694
	0x4d, 0x29, 0xe3, //0x000034e0 subq         %r12, %r11
	0xf7, 0xd3, //0x000034e3 notl         %ebx
	0x48, 0x63, 0xcb, //0x000034e5 movslq       %ebx, %rcx
	0x48, 0x0f, 0xbc, 0xc9, //0x000034e8 bsfq         %rcx, %rcx
	0x49, 0x01, 0xcb, //0x000034ec addq         %rcx, %r11
	0x49, 0x89, 0xc0, //0x000034ef movq         %rax, %r8
	0x49, 0x39, 0xd3, //0x000034f2 cmpq         %rdx, %r11
	0x0f, 0x83, 0xaa, 0x18, 0x00, 0x00, //0x000034f5 jae          LBB0_1014
	//0x000034fb LBB0_695
	0x4d, 0x8d, 0x6b, 0x01, //0x000034fb leaq         $1(%r11), %r13
	0x4d, 0x89, 0x2e, //0x000034ff movq         %r13, (%r14)
	0x43, 0x0f, 0xbe, 0x3c, 0x1c, //0x00003502 movsbl       (%r12,%r11), %edi
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00003507 movq         $-1, %rcx
	0x85, 0xff, //0x0000350e testl        %edi, %edi
	0x0f, 0x84, 0xe8, 0xfd, 0xff, 0xff, //0x00003510 je           LBB0_659
	0x49, 0x8d, 0x71, 0xff, //0x00003516 leaq         $-1(%r9), %rsi
	0x43, 0x8b, 0x1c, 0xca, //0x0000351a movl         (%r10,%r9,8), %ebx
	0x49, 0x83, 0xf8, 0xff, //0x0000351e cmpq         $-1, %r8
	0x4d, 0x0f, 0x44, 0xc3, //0x00003522 cmoveq       %r11, %r8
	0xff, 0xcb, //0x00003526 decl         %ebx
	0x83, 0xfb, 0x05, //0x00003528 cmpl         $5, %ebx
	0x0f, 0x87, 0xe7, 0x01, 0x00, 0x00, //0x0000352b ja           LBB0_724
	0x48, 0x8d, 0x05, 0x10, 0x26, 0x00, 0x00, //0x00003531 leaq         $9744(%rip), %rax  /* LJTI0_2+0(%rip) */
	0x48, 0x63, 0x14, 0x98, //0x00003538 movslq       (%rax,%rbx,4), %rdx
	0x48, 0x01, 0xc2, //0x0000353c addq         %rax, %rdx
	0xff, 0xe2, //0x0000353f jmpq         *%rdx
	//0x00003541 LBB0_698
	0x83, 0xff, 0x2c, //0x00003541 cmpl         $44, %edi
	0x0f, 0x84, 0x77, 0x02, 0x00, 0x00, //0x00003544 je           LBB0_733
	0x83, 0xff, 0x5d, //0x0000354a cmpl         $93, %edi
	0x0f, 0x84, 0x57, 0x02, 0x00, 0x00, //0x0000354d je           LBB0_700
	0xe9, 0x9f, 0xfd, 0xff, 0xff, //0x00003553 jmp          LBB0_658
	//0x00003558 LBB0_701
	0x40, 0x80, 0xff, 0x5d, //0x00003558 cmpb         $93, %dil
	0x0f, 0x84, 0x48, 0x02, 0x00, 0x00, //0x0000355c je           LBB0_700
	0x4b, 0xc7, 0x04, 0xca, 0x01, 0x00, 0x00, 0x00, //0x00003562 movq         $1, (%r10,%r9,8)
	0x83, 0xff, 0x7b, //0x0000356a cmpl         $123, %edi
	0x0f, 0x86, 0xb1, 0x01, 0x00, 0x00, //0x0000356d jbe          LBB0_703
	0xe9, 0x7f, 0xfd, 0xff, 0xff, //0x00003573 jmp          LBB0_658
	//0x00003578 LBB0_704
	0x40, 0x80, 0xff, 0x22, //0x00003578 cmpb         $34, %dil
	0x0f, 0x85, 0x75, 0xfd, 0xff, 0xff, //0x0000357c jne          LBB0_658
	0x4b, 0xc7, 0x04, 0xca, 0x04, 0x00, 0x00, 0x00, //0x00003582 movq         $4, (%r10,%r9,8)
	0x49, 0x8b, 0x57, 0x08, //0x0000358a movq         $8(%r15), %rdx
	0x48, 0x89, 0xd1, //0x0000358e movq         %rdx, %rcx
	0x4c, 0x29, 0xe9, //0x00003591 subq         %r13, %rcx
	0x0f, 0x84, 0x06, 0x21, 0x00, 0x00, //0x00003594 je           LBB0_1109
	0x4c, 0x89, 0x6c, 0x24, 0x20, //0x0000359a movq         %r13, $32(%rsp)
	0x4b, 0x8d, 0x1c, 0x2c, //0x0000359f leaq         (%r12,%r13), %rbx
	0x48, 0x83, 0xf9, 0x40, //0x000035a3 cmpq         $64, %rcx
	0x48, 0x89, 0x54, 0x24, 0x18, //0x000035a7 movq         %rdx, $24(%rsp)
	0x0f, 0x82, 0xf1, 0x12, 0x00, 0x00, //0x000035ac jb           LBB0_949
	0x4c, 0x89, 0x04, 0x24, //0x000035b2 movq         %r8, (%rsp)
	0x4d, 0x89, 0xd0, //0x000035b6 movq         %r10, %r8
	0x41, 0x89, 0xca, //0x000035b9 movl         %ecx, %r10d
	0x41, 0x83, 0xe2, 0x3f, //0x000035bc andl         $63, %r10d
	0x4c, 0x29, 0xda, //0x000035c0 subq         %r11, %rdx
	0x48, 0x83, 0xc2, 0xbf, //0x000035c3 addq         $-65, %rdx
	0x48, 0x83, 0xe2, 0xc0, //0x000035c7 andq         $-64, %rdx
	0x4c, 0x01, 0xda, //0x000035cb addq         %r11, %rdx
	0x4d, 0x8d, 0x6c, 0x14, 0x41, //0x000035ce leaq         $65(%r12,%rdx), %r13
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000035d3 movq         $-1, %r9
	0x45, 0x31, 0xf6, //0x000035da xorl         %r14d, %r14d
	0x90, 0x90, 0x90, //0x000035dd .p2align 4, 0x90
	//0x000035e0 LBB0_708
	0xc5, 0xfe, 0x6f, 0x03, //0x000035e0 vmovdqu      (%rbx), %ymm0
	0xc5, 0xfe, 0x6f, 0x4b, 0x20, //0x000035e4 vmovdqu      $32(%rbx), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x000035e9 vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x000035ed vpmovmskb    %ymm2, %edx
	0xc5, 0xf5, 0x74, 0xd6, //0x000035f1 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x000035f5 vpmovmskb    %ymm2, %edi
	0xc5, 0xfd, 0x74, 0xc7, //0x000035f9 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x000035fd vpmovmskb    %ymm0, %esi
	0xc5, 0xf5, 0x74, 0xc7, //0x00003601 vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x00003605 vpmovmskb    %ymm0, %eax
	0x48, 0xc1, 0xe7, 0x20, //0x00003609 shlq         $32, %rdi
	0x48, 0xc1, 0xe0, 0x20, //0x0000360d shlq         $32, %rax
	0x48, 0x09, 0xc6, //0x00003611 orq          %rax, %rsi
	0x49, 0x83, 0xf9, 0xff, //0x00003614 cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00003618 jne          LBB0_710
	0x48, 0x85, 0xf6, //0x0000361e testq        %rsi, %rsi
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x00003621 jne          LBB0_718
	//0x00003627 LBB0_710
	0x48, 0x09, 0xd7, //0x00003627 orq          %rdx, %rdi
	0x48, 0x89, 0xf0, //0x0000362a movq         %rsi, %rax
	0x4c, 0x09, 0xf0, //0x0000362d orq          %r14, %rax
	0x0f, 0x85, 0x3c, 0x00, 0x00, 0x00, //0x00003630 jne          LBB0_719
	//0x00003636 LBB0_711
	0x48, 0x85, 0xff, //0x00003636 testq        %rdi, %rdi
	0x0f, 0x85, 0x8a, 0x00, 0x00, 0x00, //0x00003639 jne          LBB0_720
	//0x0000363f LBB0_712
	0x48, 0x83, 0xc1, 0xc0, //0x0000363f addq         $-64, %rcx
	0x48, 0x83, 0xc3, 0x40, //0x00003643 addq         $64, %rbx
	0x48, 0x83, 0xf9, 0x3f, //0x00003647 cmpq         $63, %rcx
	0x0f, 0x87, 0x8f, 0xff, 0xff, 0xff, //0x0000364b ja           LBB0_708
	0xe9, 0x2e, 0x0f, 0x00, 0x00, //0x00003651 jmp          LBB0_713
	//0x00003656 LBB0_718
	0x48, 0x89, 0xd8, //0x00003656 movq         %rbx, %rax
	0x4c, 0x29, 0xe0, //0x00003659 subq         %r12, %rax
	0x4c, 0x0f, 0xbc, 0xce, //0x0000365c bsfq         %rsi, %r9
	0x49, 0x01, 0xc1, //0x00003660 addq         %rax, %r9
	0x48, 0x09, 0xd7, //0x00003663 orq          %rdx, %rdi
	0x48, 0x89, 0xf0, //0x00003666 movq         %rsi, %rax
	0x4c, 0x09, 0xf0, //0x00003669 orq          %r14, %rax
	0x0f, 0x84, 0xc4, 0xff, 0xff, 0xff, //0x0000366c je           LBB0_711
	//0x00003672 LBB0_719
	0x4c, 0x89, 0xf0, //0x00003672 movq         %r14, %rax
	0x48, 0xf7, 0xd0, //0x00003675 notq         %rax
	0x48, 0x21, 0xf0, //0x00003678 andq         %rsi, %rax
	0x4c, 0x8d, 0x3c, 0x00, //0x0000367b leaq         (%rax,%rax), %r15
	0x4d, 0x09, 0xf7, //0x0000367f orq          %r14, %r15
	0x4c, 0x89, 0xfa, //0x00003682 movq         %r15, %rdx
	0x48, 0xf7, 0xd2, //0x00003685 notq         %rdx
	0x48, 0x21, 0xf2, //0x00003688 andq         %rsi, %rdx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000368b movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf2, //0x00003695 andq         %rsi, %rdx
	0x45, 0x31, 0xf6, //0x00003698 xorl         %r14d, %r14d
	0x48, 0x01, 0xc2, //0x0000369b addq         %rax, %rdx
	0x41, 0x0f, 0x92, 0xc6, //0x0000369e setb         %r14b
	0x48, 0x01, 0xd2, //0x000036a2 addq         %rdx, %rdx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000036a5 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc2, //0x000036af xorq         %rax, %rdx
	0x4c, 0x21, 0xfa, //0x000036b2 andq         %r15, %rdx
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x000036b5 movq         $16(%rsp), %r15
	0x48, 0xf7, 0xd2, //0x000036ba notq         %rdx
	0x48, 0x21, 0xd7, //0x000036bd andq         %rdx, %rdi
	0x48, 0x85, 0xff, //0x000036c0 testq        %rdi, %rdi
	0x0f, 0x84, 0x76, 0xff, 0xff, 0xff, //0x000036c3 je           LBB0_712
	//0x000036c9 LBB0_720
	0x48, 0x0f, 0xbc, 0xc7, //0x000036c9 bsfq         %rdi, %rax
	0x4c, 0x29, 0xe3, //0x000036cd subq         %r12, %rbx
	0x4c, 0x8d, 0x6c, 0x03, 0x01, //0x000036d0 leaq         $1(%rbx,%rax), %r13
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x000036d5 movq         $8(%rsp), %r14
	0x4d, 0x89, 0xc2, //0x000036da movq         %r8, %r10
	0x4c, 0x8b, 0x04, 0x24, //0x000036dd movq         (%rsp), %r8
	0x4d, 0x85, 0xed, //0x000036e1 testq        %r13, %r13
	0x0f, 0x88, 0xdc, 0x16, 0x00, 0x00, //0x000036e4 js           LBB0_978
	//0x000036ea LBB0_721
	0x4d, 0x89, 0x2e, //0x000036ea movq         %r13, (%r14)
	0x4c, 0x89, 0xd9, //0x000036ed movq         %r11, %rcx
	0x48, 0x83, 0x7c, 0x24, 0x20, 0x00, //0x000036f0 cmpq         $0, $32(%rsp)
	0x0f, 0x8f, 0x34, 0xfc, 0xff, 0xff, //0x000036f6 jg           LBB0_662
	0xe9, 0xfd, 0xfb, 0xff, 0xff, //0x000036fc jmp          LBB0_659
	//0x00003701 LBB0_722
	0x40, 0x80, 0xff, 0x3a, //0x00003701 cmpb         $58, %dil
	0x0f, 0x85, 0xec, 0xfb, 0xff, 0xff, //0x00003705 jne          LBB0_658
	0x4b, 0xc7, 0x04, 0xca, 0x00, 0x00, 0x00, 0x00, //0x0000370b movq         $0, (%r10,%r9,8)
	0xe9, 0x18, 0xfc, 0xff, 0xff, //0x00003713 jmp          LBB0_662
	//0x00003718 LBB0_724
	0x49, 0x89, 0x32, //0x00003718 movq         %rsi, (%r10)
	0x83, 0xff, 0x7b, //0x0000371b cmpl         $123, %edi
	0x0f, 0x87, 0xd3, 0xfb, 0xff, 0xff, //0x0000371e ja           LBB0_658
	//0x00003724 LBB0_703
	0x4f, 0x8d, 0x0c, 0x1c, //0x00003724 leaq         (%r12,%r11), %r9
	0x89, 0xf8, //0x00003728 movl         %edi, %eax
	0x48, 0x8d, 0x15, 0x2f, 0x24, 0x00, 0x00, //0x0000372a leaq         $9263(%rip), %rdx  /* LJTI0_3+0(%rip) */
	0x48, 0x63, 0x04, 0x82, //0x00003731 movslq       (%rdx,%rax,4), %rax
	0x48, 0x01, 0xd0, //0x00003735 addq         %rdx, %rax
	0xff, 0xe0, //0x00003738 jmpq         *%rax
	//0x0000373a LBB0_729
	0x4d, 0x8b, 0x57, 0x08, //0x0000373a movq         $8(%r15), %r10
	0x4d, 0x29, 0xda, //0x0000373e subq         %r11, %r10
	0x0f, 0x84, 0x9a, 0x16, 0x00, 0x00, //0x00003741 je           LBB0_1016
	0x4c, 0x89, 0x04, 0x24, //0x00003747 movq         %r8, (%rsp)
	0x41, 0x80, 0x39, 0x30, //0x0000374b cmpb         $48, (%r9)
	0x0f, 0x85, 0x57, 0x02, 0x00, 0x00, //0x0000374f jne          LBB0_758
	0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x00003755 movl         $1, %r8d
	0x49, 0x83, 0xfa, 0x01, //0x0000375b cmpq         $1, %r10
	0x0f, 0x85, 0x1d, 0x02, 0x00, 0x00, //0x0000375f jne          LBB0_756
	//0x00003765 LBB0_732
	0x4c, 0x89, 0xe9, //0x00003765 movq         %r13, %rcx
	0xe9, 0xeb, 0x09, 0x00, 0x00, //0x00003768 jmp          LBB0_867
	//0x0000376d LBB0_725
	0x83, 0xff, 0x2c, //0x0000376d cmpl         $44, %edi
	0x0f, 0x85, 0x2b, 0x00, 0x00, 0x00, //0x00003770 jne          LBB0_726
	0x49, 0x81, 0xf9, 0xff, 0x0f, 0x00, 0x00, //0x00003776 cmpq         $4095, %r9
	0x0f, 0x8f, 0x2e, 0x16, 0x00, 0x00, //0x0000377d jg           LBB0_1097
	0x49, 0x8d, 0x41, 0x01, //0x00003783 leaq         $1(%r9), %rax
	0x49, 0x89, 0x02, //0x00003787 movq         %rax, (%r10)
	0x4b, 0xc7, 0x44, 0xca, 0x08, 0x03, 0x00, 0x00, 0x00, //0x0000378a movq         $3, $8(%r10,%r9,8)
	0xe9, 0x98, 0xfb, 0xff, 0xff, //0x00003793 jmp          LBB0_662
	//0x00003798 LBB0_727
	0x83, 0xff, 0x22, //0x00003798 cmpl         $34, %edi
	0x0f, 0x84, 0x42, 0x00, 0x00, 0x00, //0x0000379b je           LBB0_737
	//0x000037a1 LBB0_726
	0x83, 0xff, 0x7d, //0x000037a1 cmpl         $125, %edi
	0x0f, 0x85, 0x4d, 0xfb, 0xff, 0xff, //0x000037a4 jne          LBB0_658
	//0x000037aa LBB0_700
	0x49, 0x89, 0x32, //0x000037aa movq         %rsi, (%r10)
	0x49, 0x89, 0xf1, //0x000037ad movq         %rsi, %r9
	0x4c, 0x89, 0xc1, //0x000037b0 movq         %r8, %rcx
	0x48, 0x85, 0xf6, //0x000037b3 testq        %rsi, %rsi
	0x0f, 0x85, 0x86, 0xfb, 0xff, 0xff, //0x000037b6 jne          LBB0_664
	0xe9, 0x3d, 0xfb, 0xff, 0xff, //0x000037bc jmp          LBB0_659
	//0x000037c1 LBB0_733
	0x49, 0x81, 0xf9, 0xff, 0x0f, 0x00, 0x00, //0x000037c1 cmpq         $4095, %r9
	0x0f, 0x8f, 0xe3, 0x15, 0x00, 0x00, //0x000037c8 jg           LBB0_1097
	0x49, 0x8d, 0x41, 0x01, //0x000037ce leaq         $1(%r9), %rax
	0x49, 0x89, 0x02, //0x000037d2 movq         %rax, (%r10)
	0x4b, 0xc7, 0x44, 0xca, 0x08, 0x00, 0x00, 0x00, 0x00, //0x000037d5 movq         $0, $8(%r10,%r9,8)
	0xe9, 0x4d, 0xfb, 0xff, 0xff, //0x000037de jmp          LBB0_662
	//0x000037e3 LBB0_737
	0x4c, 0x89, 0x04, 0x24, //0x000037e3 movq         %r8, (%rsp)
	0x4b, 0xc7, 0x04, 0xca, 0x02, 0x00, 0x00, 0x00, //0x000037e7 movq         $2, (%r10,%r9,8)
	0x4d, 0x8b, 0x47, 0x08, //0x000037ef movq         $8(%r15), %r8
	0x4c, 0x89, 0xc1, //0x000037f3 movq         %r8, %rcx
	0x4c, 0x29, 0xe9, //0x000037f6 subq         %r13, %rcx
	0x0f, 0x84, 0xbb, 0x1e, 0x00, 0x00, //0x000037f9 je           LBB0_1116
	0x4c, 0x89, 0x6c, 0x24, 0x20, //0x000037ff movq         %r13, $32(%rsp)
	0x4b, 0x8d, 0x1c, 0x2c, //0x00003804 leaq         (%r12,%r13), %rbx
	0x48, 0x83, 0xf9, 0x40, //0x00003808 cmpq         $64, %rcx
	0x0f, 0x82, 0x2e, 0x11, 0x00, 0x00, //0x0000380c jb           LBB0_957
	0x41, 0x89, 0xca, //0x00003812 movl         %ecx, %r10d
	0x41, 0x83, 0xe2, 0x3f, //0x00003815 andl         $63, %r10d
	0x4c, 0x89, 0xc0, //0x00003819 movq         %r8, %rax
	0x4c, 0x29, 0xd8, //0x0000381c subq         %r11, %rax
	0x48, 0x83, 0xc0, 0xbf, //0x0000381f addq         $-65, %rax
	0x48, 0x83, 0xe0, 0xc0, //0x00003823 andq         $-64, %rax
	0x4c, 0x01, 0xd8, //0x00003827 addq         %r11, %rax
	0x4d, 0x8d, 0x6c, 0x04, 0x41, //0x0000382a leaq         $65(%r12,%rax), %r13
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000382f movq         $-1, %r9
	0x45, 0x31, 0xf6, //0x00003836 xorl         %r14d, %r14d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003839 .p2align 4, 0x90
	//0x00003840 LBB0_740
	0xc5, 0xfe, 0x6f, 0x03, //0x00003840 vmovdqu      (%rbx), %ymm0
	0xc5, 0xfe, 0x6f, 0x4b, 0x20, //0x00003844 vmovdqu      $32(%rbx), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x00003849 vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x0000384d vpmovmskb    %ymm2, %edx
	0xc5, 0xf5, 0x74, 0xd6, //0x00003851 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00003855 vpmovmskb    %ymm2, %edi
	0xc5, 0xfd, 0x74, 0xc7, //0x00003859 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x0000385d vpmovmskb    %ymm0, %esi
	0xc5, 0xf5, 0x74, 0xc7, //0x00003861 vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x00003865 vpmovmskb    %ymm0, %eax
	0x48, 0xc1, 0xe7, 0x20, //0x00003869 shlq         $32, %rdi
	0x48, 0xc1, 0xe0, 0x20, //0x0000386d shlq         $32, %rax
	0x48, 0x09, 0xc6, //0x00003871 orq          %rax, %rsi
	0x49, 0x83, 0xf9, 0xff, //0x00003874 cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00003878 jne          LBB0_742
	0x48, 0x85, 0xf6, //0x0000387e testq        %rsi, %rsi
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x00003881 jne          LBB0_750
	//0x00003887 LBB0_742
	0x48, 0x09, 0xd7, //0x00003887 orq          %rdx, %rdi
	0x48, 0x89, 0xf0, //0x0000388a movq         %rsi, %rax
	0x4c, 0x09, 0xf0, //0x0000388d orq          %r14, %rax
	0x0f, 0x85, 0x3c, 0x00, 0x00, 0x00, //0x00003890 jne          LBB0_751
	//0x00003896 LBB0_743
	0x48, 0x85, 0xff, //0x00003896 testq        %rdi, %rdi
	0x0f, 0x85, 0x8a, 0x00, 0x00, 0x00, //0x00003899 jne          LBB0_752
	//0x0000389f LBB0_744
	0x48, 0x83, 0xc1, 0xc0, //0x0000389f addq         $-64, %rcx
	0x48, 0x83, 0xc3, 0x40, //0x000038a3 addq         $64, %rbx
	0x48, 0x83, 0xf9, 0x3f, //0x000038a7 cmpq         $63, %rcx
	0x0f, 0x87, 0x8f, 0xff, 0xff, 0xff, //0x000038ab ja           LBB0_740
	0xe9, 0x6f, 0x0e, 0x00, 0x00, //0x000038b1 jmp          LBB0_745
	//0x000038b6 LBB0_750
	0x48, 0x89, 0xd8, //0x000038b6 movq         %rbx, %rax
	0x4c, 0x29, 0xe0, //0x000038b9 subq         %r12, %rax
	0x4c, 0x0f, 0xbc, 0xce, //0x000038bc bsfq         %rsi, %r9
	0x49, 0x01, 0xc1, //0x000038c0 addq         %rax, %r9
	0x48, 0x09, 0xd7, //0x000038c3 orq          %rdx, %rdi
	0x48, 0x89, 0xf0, //0x000038c6 movq         %rsi, %rax
	0x4c, 0x09, 0xf0, //0x000038c9 orq          %r14, %rax
	0x0f, 0x84, 0xc4, 0xff, 0xff, 0xff, //0x000038cc je           LBB0_743
	//0x000038d2 LBB0_751
	0x4c, 0x89, 0xf0, //0x000038d2 movq         %r14, %rax
	0x48, 0xf7, 0xd0, //0x000038d5 notq         %rax
	0x48, 0x21, 0xf0, //0x000038d8 andq         %rsi, %rax
	0x4c, 0x8d, 0x3c, 0x00, //0x000038db leaq         (%rax,%rax), %r15
	0x4d, 0x09, 0xf7, //0x000038df orq          %r14, %r15
	0x4c, 0x89, 0xfa, //0x000038e2 movq         %r15, %rdx
	0x48, 0xf7, 0xd2, //0x000038e5 notq         %rdx
	0x48, 0x21, 0xf2, //0x000038e8 andq         %rsi, %rdx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000038eb movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf2, //0x000038f5 andq         %rsi, %rdx
	0x45, 0x31, 0xf6, //0x000038f8 xorl         %r14d, %r14d
	0x48, 0x01, 0xc2, //0x000038fb addq         %rax, %rdx
	0x41, 0x0f, 0x92, 0xc6, //0x000038fe setb         %r14b
	0x48, 0x01, 0xd2, //0x00003902 addq         %rdx, %rdx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00003905 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc2, //0x0000390f xorq         %rax, %rdx
	0x4c, 0x21, 0xfa, //0x00003912 andq         %r15, %rdx
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x00003915 movq         $16(%rsp), %r15
	0x48, 0xf7, 0xd2, //0x0000391a notq         %rdx
	0x48, 0x21, 0xd7, //0x0000391d andq         %rdx, %rdi
	0x48, 0x85, 0xff, //0x00003920 testq        %rdi, %rdi
	0x0f, 0x84, 0x76, 0xff, 0xff, 0xff, //0x00003923 je           LBB0_744
	//0x00003929 LBB0_752
	0x48, 0x0f, 0xbc, 0xc7, //0x00003929 bsfq         %rdi, %rax
	0x4c, 0x29, 0xe3, //0x0000392d subq         %r12, %rbx
	0x4c, 0x8d, 0x6c, 0x03, 0x01, //0x00003930 leaq         $1(%rbx,%rax), %r13
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x00003935 movq         $8(%rsp), %r14
	0x4c, 0x8b, 0x54, 0x24, 0x28, //0x0000393a movq         $40(%rsp), %r10
	0x4d, 0x85, 0xed, //0x0000393f testq        %r13, %r13
	0x0f, 0x88, 0x56, 0x1b, 0x00, 0x00, //0x00003942 js           LBB0_1082
	//0x00003948 LBB0_753
	0x4d, 0x89, 0x2e, //0x00003948 movq         %r13, (%r14)
	0x4c, 0x89, 0xd9, //0x0000394b movq         %r11, %rcx
	0x48, 0x83, 0x7c, 0x24, 0x20, 0x00, //0x0000394e cmpq         $0, $32(%rsp)
	0x4c, 0x8b, 0x04, 0x24, //0x00003954 movq         (%rsp), %r8
	0x0f, 0x8e, 0xa0, 0xf9, 0xff, 0xff, //0x00003958 jle          LBB0_659
	0x49, 0x8b, 0x02, //0x0000395e movq         (%r10), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00003961 cmpq         $4095, %rax
	0x0f, 0x8f, 0x44, 0x14, 0x00, 0x00, //0x00003967 jg           LBB0_1097
	0x48, 0x8d, 0x48, 0x01, //0x0000396d leaq         $1(%rax), %rcx
	0x49, 0x89, 0x0a, //0x00003971 movq         %rcx, (%r10)
	0x49, 0xc7, 0x44, 0xc2, 0x08, 0x04, 0x00, 0x00, 0x00, //0x00003974 movq         $4, $8(%r10,%rax,8)
	0xe9, 0xae, 0xf9, 0xff, 0xff, //0x0000397d jmp          LBB0_662
	//0x00003982 LBB0_756
	0x43, 0x8a, 0x0c, 0x2c, //0x00003982 movb         (%r12,%r13), %cl
	0x80, 0xc1, 0xd2, //0x00003986 addb         $-46, %cl
	0x80, 0xf9, 0x37, //0x00003989 cmpb         $55, %cl
	0x0f, 0x87, 0xd3, 0xfd, 0xff, 0xff, //0x0000398c ja           LBB0_732
	0x0f, 0xb6, 0xc1, //0x00003992 movzbl       %cl, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00003995 movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x0000399f btq          %rax, %rcx
	0x4c, 0x89, 0xe9, //0x000039a3 movq         %r13, %rcx
	0x0f, 0x83, 0xac, 0x07, 0x00, 0x00, //0x000039a6 jae          LBB0_867
	//0x000039ac LBB0_758
	0x4c, 0x89, 0x6c, 0x24, 0x20, //0x000039ac movq         %r13, $32(%rsp)
	0x49, 0x83, 0xfa, 0x20, //0x000039b1 cmpq         $32, %r10
	0x0f, 0x82, 0x4d, 0x0f, 0x00, 0x00, //0x000039b5 jb           LBB0_954
	0x49, 0x8d, 0x4a, 0xe0, //0x000039bb leaq         $-32(%r10), %rcx
	0x48, 0x89, 0xc8, //0x000039bf movq         %rcx, %rax
	0x48, 0x83, 0xe0, 0xe0, //0x000039c2 andq         $-32, %rax
	0x4e, 0x8d, 0x44, 0x08, 0x20, //0x000039c6 leaq         $32(%rax,%r9), %r8
	0x83, 0xe1, 0x1f, //0x000039cb andl         $31, %ecx
	0x48, 0x89, 0x4c, 0x24, 0x18, //0x000039ce movq         %rcx, $24(%rsp)
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x000039d3 movq         $-1, %r13
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x000039da movq         $-1, %r15
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x000039e1 movq         $-1, %r12
	0x4c, 0x89, 0xcb, //0x000039e8 movq         %r9, %rbx
	0x90, 0x90, 0x90, 0x90, 0x90, //0x000039eb .p2align 4, 0x90
	//0x000039f0 LBB0_760
	0xc5, 0xfe, 0x6f, 0x03, //0x000039f0 vmovdqu      (%rbx), %ymm0
	0xc4, 0xc1, 0x7d, 0x64, 0xc8, //0x000039f4 vpcmpgtb     %ymm8, %ymm0, %ymm1
	0xc5, 0xb5, 0x64, 0xd0, //0x000039f9 vpcmpgtb     %ymm0, %ymm9, %ymm2
	0xc5, 0xf5, 0xdb, 0xca, //0x000039fd vpand        %ymm2, %ymm1, %ymm1
	0xc5, 0xad, 0x74, 0xd0, //0x00003a01 vpcmpeqb     %ymm0, %ymm10, %ymm2
	0xc5, 0xa5, 0x74, 0xd8, //0x00003a05 vpcmpeqb     %ymm0, %ymm11, %ymm3
	0xc5, 0xe5, 0xeb, 0xd2, //0x00003a09 vpor         %ymm2, %ymm3, %ymm2
	0xc5, 0x9d, 0xeb, 0xd8, //0x00003a0d vpor         %ymm0, %ymm12, %ymm3
	0xc5, 0x95, 0x74, 0xc0, //0x00003a11 vpcmpeqb     %ymm0, %ymm13, %ymm0
	0xc5, 0x7d, 0xd7, 0xf0, //0x00003a15 vpmovmskb    %ymm0, %r14d
	0xc5, 0x8d, 0x74, 0xdb, //0x00003a19 vpcmpeqb     %ymm3, %ymm14, %ymm3
	0xc5, 0xfd, 0xd7, 0xf3, //0x00003a1d vpmovmskb    %ymm3, %esi
	0xc5, 0xfd, 0xd7, 0xfa, //0x00003a21 vpmovmskb    %ymm2, %edi
	0xc5, 0xe5, 0xeb, 0xc0, //0x00003a25 vpor         %ymm0, %ymm3, %ymm0
	0xc5, 0xed, 0xeb, 0xc9, //0x00003a29 vpor         %ymm1, %ymm2, %ymm1
	0xc5, 0xfd, 0xeb, 0xc1, //0x00003a2d vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x00003a31 vpmovmskb    %ymm0, %eax
	0x48, 0xf7, 0xd0, //0x00003a35 notq         %rax
	0x48, 0x0f, 0xbc, 0xc8, //0x00003a38 bsfq         %rax, %rcx
	0x83, 0xf9, 0x20, //0x00003a3c cmpl         $32, %ecx
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00003a3f je           LBB0_762
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00003a45 movl         $-1, %eax
	0xd3, 0xe0, //0x00003a4a shll         %cl, %eax
	0xf7, 0xd0, //0x00003a4c notl         %eax
	0x41, 0x21, 0xc6, //0x00003a4e andl         %eax, %r14d
	0x21, 0xc6, //0x00003a51 andl         %eax, %esi
	0x21, 0xf8, //0x00003a53 andl         %edi, %eax
	0x89, 0xc7, //0x00003a55 movl         %eax, %edi
	//0x00003a57 LBB0_762
	0x41, 0x8d, 0x56, 0xff, //0x00003a57 leal         $-1(%r14), %edx
	0x44, 0x21, 0xf2, //0x00003a5b andl         %r14d, %edx
	0x0f, 0x85, 0x81, 0x0b, 0x00, 0x00, //0x00003a5e jne          LBB0_921
	0x8d, 0x56, 0xff, //0x00003a64 leal         $-1(%rsi), %edx
	0x21, 0xf2, //0x00003a67 andl         %esi, %edx
	0x0f, 0x85, 0x76, 0x0b, 0x00, 0x00, //0x00003a69 jne          LBB0_921
	0x8d, 0x57, 0xff, //0x00003a6f leal         $-1(%rdi), %edx
	0x21, 0xfa, //0x00003a72 andl         %edi, %edx
	0x0f, 0x85, 0x6b, 0x0b, 0x00, 0x00, //0x00003a74 jne          LBB0_921
	0x45, 0x85, 0xf6, //0x00003a7a testl        %r14d, %r14d
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00003a7d je           LBB0_768
	0x48, 0x89, 0xd8, //0x00003a83 movq         %rbx, %rax
	0x4c, 0x29, 0xc8, //0x00003a86 subq         %r9, %rax
	0x41, 0x0f, 0xbc, 0xd6, //0x00003a89 bsfl         %r14d, %edx
	0x48, 0x01, 0xc2, //0x00003a8d addq         %rax, %rdx
	0x49, 0x83, 0xfc, 0xff, //0x00003a90 cmpq         $-1, %r12
	0x0f, 0x85, 0x28, 0x0d, 0x00, 0x00, //0x00003a94 jne          LBB0_942
	0x49, 0x89, 0xd4, //0x00003a9a movq         %rdx, %r12
	//0x00003a9d LBB0_768
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x00003a9d movq         $8(%rsp), %r14
	0x85, 0xf6, //0x00003aa2 testl        %esi, %esi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x00003aa4 je           LBB0_771
	0x48, 0x89, 0xd8, //0x00003aaa movq         %rbx, %rax
	0x4c, 0x29, 0xc8, //0x00003aad subq         %r9, %rax
	0x0f, 0xbc, 0xd6, //0x00003ab0 bsfl         %esi, %edx
	0x48, 0x01, 0xc2, //0x00003ab3 addq         %rax, %rdx
	0x49, 0x83, 0xff, 0xff, //0x00003ab6 cmpq         $-1, %r15
	0x0f, 0x85, 0xf7, 0x0c, 0x00, 0x00, //0x00003aba jne          LBB0_941
	0x49, 0x89, 0xd7, //0x00003ac0 movq         %rdx, %r15
	//0x00003ac3 LBB0_771
	0x85, 0xff, //0x00003ac3 testl        %edi, %edi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x00003ac5 je           LBB0_774
	0x48, 0x89, 0xd8, //0x00003acb movq         %rbx, %rax
	0x4c, 0x29, 0xc8, //0x00003ace subq         %r9, %rax
	0x0f, 0xbc, 0xd7, //0x00003ad1 bsfl         %edi, %edx
	0x48, 0x01, 0xc2, //0x00003ad4 addq         %rax, %rdx
	0x49, 0x83, 0xfd, 0xff, //0x00003ad7 cmpq         $-1, %r13
	0x0f, 0x85, 0xd6, 0x0c, 0x00, 0x00, //0x00003adb jne          LBB0_941
	0x49, 0x89, 0xd5, //0x00003ae1 movq         %rdx, %r13
	//0x00003ae4 LBB0_774
	0x83, 0xf9, 0x20, //0x00003ae4 cmpl         $32, %ecx
	0x0f, 0x85, 0x69, 0x02, 0x00, 0x00, //0x00003ae7 jne          LBB0_807
	0x48, 0x83, 0xc3, 0x20, //0x00003aed addq         $32, %rbx
	0x49, 0x83, 0xc2, 0xe0, //0x00003af1 addq         $-32, %r10
	0x49, 0x83, 0xfa, 0x1f, //0x00003af5 cmpq         $31, %r10
	0x0f, 0x87, 0xf1, 0xfe, 0xff, 0xff, //0x00003af9 ja           LBB0_760
	0xc5, 0xf8, 0x77, //0x00003aff vzeroupper   
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00003b02 movq         $24(%rsp), %r10
	//0x00003b07 LBB0_777
	0x49, 0x83, 0xfa, 0x10, //0x00003b07 cmpq         $16, %r10
	0xc5, 0xfe, 0x6f, 0x2d, 0xed, 0xc4, 0xff, 0xff, //0x00003b0b vmovdqu      $-15123(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x65, 0xc5, 0xff, 0xff, //0x00003b13 vmovdqu      $-15003(%rip), %ymm6  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x7d, 0xc5, 0xff, 0xff, //0x00003b1b vmovdqu      $-14979(%rip), %ymm7  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0xf5, 0xc5, 0xff, 0xff, //0x00003b23 vmovdqu      $-14859(%rip), %ymm8  /* LCPI0_12+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0x0d, 0xc6, 0xff, 0xff, //0x00003b2b vmovdqu      $-14835(%rip), %ymm9  /* LCPI0_13+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0x25, 0xc6, 0xff, 0xff, //0x00003b33 vmovdqu      $-14811(%rip), %ymm10  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x3d, 0xc6, 0xff, 0xff, //0x00003b3b vmovdqu      $-14787(%rip), %ymm11  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0xf5, 0xc4, 0xff, 0xff, //0x00003b43 vmovdqu      $-15115(%rip), %ymm12  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x4d, 0xc6, 0xff, 0xff, //0x00003b4b vmovdqu      $-14771(%rip), %ymm13  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x65, 0xc6, 0xff, 0xff, //0x00003b53 vmovdqu      $-14747(%rip), %ymm14  /* LCPI0_17+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x3d, 0xad, 0xc6, 0xff, 0xff, //0x00003b5b vmovdqu      $-14675(%rip), %xmm15  /* LCPI0_18+0(%rip) */
	0x0f, 0x82, 0x56, 0x01, 0x00, 0x00, //0x00003b63 jb           LBB0_796
	0x4d, 0x8d, 0x72, 0xf0, //0x00003b69 leaq         $-16(%r10), %r14
	0x4c, 0x89, 0xf0, //0x00003b6d movq         %r14, %rax
	0x48, 0x83, 0xe0, 0xf0, //0x00003b70 andq         $-16, %rax
	0x4a, 0x8d, 0x44, 0x00, 0x10, //0x00003b74 leaq         $16(%rax,%r8), %rax
	0x48, 0x89, 0x44, 0x24, 0x18, //0x00003b79 movq         %rax, $24(%rsp)
	0x41, 0x83, 0xe6, 0x0f, //0x00003b7e andl         $15, %r14d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003b82 .p2align 4, 0x90
	//0x00003b90 LBB0_779
	0xc4, 0xc1, 0x7a, 0x6f, 0x00, //0x00003b90 vmovdqu      (%r8), %xmm0
	0xc4, 0xc1, 0x79, 0x64, 0xcf, //0x00003b95 vpcmpgtb     %xmm15, %xmm0, %xmm1
	0xc5, 0xfa, 0x6f, 0x15, 0x7e, 0xc6, 0xff, 0xff, //0x00003b9a vmovdqu      $-14722(%rip), %xmm2  /* LCPI0_19+0(%rip) */
	0xc5, 0xe9, 0x64, 0xd0, //0x00003ba2 vpcmpgtb     %xmm0, %xmm2, %xmm2
	0xc5, 0xf1, 0xdb, 0xca, //0x00003ba6 vpand        %xmm2, %xmm1, %xmm1
	0xc5, 0xf9, 0x74, 0x15, 0x7e, 0xc6, 0xff, 0xff, //0x00003baa vpcmpeqb     $-14722(%rip), %xmm0, %xmm2  /* LCPI0_20+0(%rip) */
	0xc5, 0xf9, 0x74, 0x1d, 0x86, 0xc6, 0xff, 0xff, //0x00003bb2 vpcmpeqb     $-14714(%rip), %xmm0, %xmm3  /* LCPI0_21+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xd2, //0x00003bba vpor         %xmm2, %xmm3, %xmm2
	0xc5, 0xf9, 0xeb, 0x1d, 0x2a, 0xc6, 0xff, 0xff, //0x00003bbe vpor         $-14806(%rip), %xmm0, %xmm3  /* LCPI0_5+0(%rip) */
	0xc5, 0xf9, 0x74, 0x05, 0x82, 0xc6, 0xff, 0xff, //0x00003bc6 vpcmpeqb     $-14718(%rip), %xmm0, %xmm0  /* LCPI0_22+0(%rip) */
	0xc5, 0xe1, 0x74, 0x1d, 0x8a, 0xc6, 0xff, 0xff, //0x00003bce vpcmpeqb     $-14710(%rip), %xmm3, %xmm3  /* LCPI0_23+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xe0, //0x00003bd6 vpor         %xmm0, %xmm3, %xmm4
	0xc5, 0xe9, 0xeb, 0xc9, //0x00003bda vpor         %xmm1, %xmm2, %xmm1
	0xc5, 0xd9, 0xeb, 0xc9, //0x00003bde vpor         %xmm1, %xmm4, %xmm1
	0xc5, 0xf9, 0xd7, 0xd8, //0x00003be2 vpmovmskb    %xmm0, %ebx
	0xc5, 0xf9, 0xd7, 0xf3, //0x00003be6 vpmovmskb    %xmm3, %esi
	0xc5, 0xf9, 0xd7, 0xfa, //0x00003bea vpmovmskb    %xmm2, %edi
	0xc5, 0xf9, 0xd7, 0xc1, //0x00003bee vpmovmskb    %xmm1, %eax
	0xb9, 0xff, 0xff, 0xff, 0xff, //0x00003bf2 movl         $4294967295, %ecx
	0x48, 0x31, 0xc8, //0x00003bf7 xorq         %rcx, %rax
	0x48, 0x0f, 0xbc, 0xc8, //0x00003bfa bsfq         %rax, %rcx
	0x83, 0xf9, 0x10, //0x00003bfe cmpl         $16, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x00003c01 je           LBB0_781
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00003c07 movl         $-1, %eax
	0xd3, 0xe0, //0x00003c0c shll         %cl, %eax
	0xf7, 0xd0, //0x00003c0e notl         %eax
	0x21, 0xc3, //0x00003c10 andl         %eax, %ebx
	0x21, 0xc6, //0x00003c12 andl         %eax, %esi
	0x21, 0xf8, //0x00003c14 andl         %edi, %eax
	0x89, 0xc7, //0x00003c16 movl         %eax, %edi
	//0x00003c18 LBB0_781
	0x8d, 0x53, 0xff, //0x00003c18 leal         $-1(%rbx), %edx
	0x21, 0xda, //0x00003c1b andl         %ebx, %edx
	0x0f, 0x85, 0x7b, 0x0b, 0x00, 0x00, //0x00003c1d jne          LBB0_940
	0x8d, 0x56, 0xff, //0x00003c23 leal         $-1(%rsi), %edx
	0x21, 0xf2, //0x00003c26 andl         %esi, %edx
	0x0f, 0x85, 0x70, 0x0b, 0x00, 0x00, //0x00003c28 jne          LBB0_940
	0x8d, 0x57, 0xff, //0x00003c2e leal         $-1(%rdi), %edx
	0x21, 0xfa, //0x00003c31 andl         %edi, %edx
	0x0f, 0x85, 0x65, 0x0b, 0x00, 0x00, //0x00003c33 jne          LBB0_940
	0x85, 0xdb, //0x00003c39 testl        %ebx, %ebx
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x00003c3b je           LBB0_787
	0x4c, 0x89, 0xc0, //0x00003c41 movq         %r8, %rax
	0x4c, 0x29, 0xc8, //0x00003c44 subq         %r9, %rax
	0x0f, 0xbc, 0xdb, //0x00003c47 bsfl         %ebx, %ebx
	0x48, 0x01, 0xc3, //0x00003c4a addq         %rax, %rbx
	0x49, 0x83, 0xfc, 0xff, //0x00003c4d cmpq         $-1, %r12
	0x0f, 0x85, 0xa3, 0x0b, 0x00, 0x00, //0x00003c51 jne          LBB0_945
	0x49, 0x89, 0xdc, //0x00003c57 movq         %rbx, %r12
	//0x00003c5a LBB0_787
	0x85, 0xf6, //0x00003c5a testl        %esi, %esi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x00003c5c je           LBB0_790
	0x4c, 0x89, 0xc0, //0x00003c62 movq         %r8, %rax
	0x4c, 0x29, 0xc8, //0x00003c65 subq         %r9, %rax
	0x0f, 0xbc, 0xf6, //0x00003c68 bsfl         %esi, %esi
	0x48, 0x01, 0xc6, //0x00003c6b addq         %rax, %rsi
	0x49, 0x83, 0xff, 0xff, //0x00003c6e cmpq         $-1, %r15
	0x0f, 0x85, 0x5a, 0x0b, 0x00, 0x00, //0x00003c72 jne          LBB0_943
	0x49, 0x89, 0xf7, //0x00003c78 movq         %rsi, %r15
	//0x00003c7b LBB0_790
	0x85, 0xff, //0x00003c7b testl        %edi, %edi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x00003c7d je           LBB0_793
	0x4c, 0x89, 0xc0, //0x00003c83 movq         %r8, %rax
	0x4c, 0x29, 0xc8, //0x00003c86 subq         %r9, %rax
	0x0f, 0xbc, 0xf7, //0x00003c89 bsfl         %edi, %esi
	0x48, 0x01, 0xc6, //0x00003c8c addq         %rax, %rsi
	0x49, 0x83, 0xfd, 0xff, //0x00003c8f cmpq         $-1, %r13
	0x0f, 0x85, 0x39, 0x0b, 0x00, 0x00, //0x00003c93 jne          LBB0_943
	0x49, 0x89, 0xf5, //0x00003c99 movq         %rsi, %r13
	//0x00003c9c LBB0_793
	0x83, 0xf9, 0x10, //0x00003c9c cmpl         $16, %ecx
	0x0f, 0x85, 0xd7, 0x00, 0x00, 0x00, //0x00003c9f jne          LBB0_808
	0x49, 0x83, 0xc0, 0x10, //0x00003ca5 addq         $16, %r8
	0x49, 0x83, 0xc2, 0xf0, //0x00003ca9 addq         $-16, %r10
	0x49, 0x83, 0xfa, 0x0f, //0x00003cad cmpq         $15, %r10
	0x0f, 0x87, 0xd9, 0xfe, 0xff, 0xff, //0x00003cb1 ja           LBB0_779
	0x4d, 0x89, 0xf2, //0x00003cb7 movq         %r14, %r10
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00003cba movq         $24(%rsp), %r8
	//0x00003cbf LBB0_796
	0x4d, 0x85, 0xd2, //0x00003cbf testq        %r10, %r10
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x00003cc2 movq         $8(%rsp), %r14
	0x0f, 0x84, 0xb7, 0x00, 0x00, 0x00, //0x00003cc7 je           LBB0_809
	0x4b, 0x8d, 0x0c, 0x10, //0x00003ccd leaq         (%r8,%r10), %rcx
	0xe9, 0x26, 0x00, 0x00, 0x00, //0x00003cd1 jmp          LBB0_801
	//0x00003cd6 LBB0_798
	0x49, 0x89, 0xf0, //0x00003cd6 movq         %rsi, %r8
	0x4d, 0x29, 0xc8, //0x00003cd9 subq         %r9, %r8
	0x49, 0x83, 0xfd, 0xff, //0x00003cdc cmpq         $-1, %r13
	0x0f, 0x85, 0x91, 0x0b, 0x00, 0x00, //0x00003ce0 jne          LBB0_946
	0x49, 0xff, 0xc8, //0x00003ce6 decq         %r8
	0x4d, 0x89, 0xc5, //0x00003ce9 movq         %r8, %r13
	0x90, 0x90, 0x90, 0x90, //0x00003cec .p2align 4, 0x90
	//0x00003cf0 LBB0_800
	0x49, 0x89, 0xf0, //0x00003cf0 movq         %rsi, %r8
	0x49, 0xff, 0xca, //0x00003cf3 decq         %r10
	0x0f, 0x84, 0xe6, 0x0a, 0x00, 0x00, //0x00003cf6 je           LBB0_944
	//0x00003cfc LBB0_801
	0x41, 0x0f, 0xbe, 0x10, //0x00003cfc movsbl       (%r8), %edx
	0x83, 0xc2, 0xd5, //0x00003d00 addl         $-43, %edx
	0x83, 0xfa, 0x3a, //0x00003d03 cmpl         $58, %edx
	0x0f, 0x87, 0x78, 0x00, 0x00, 0x00, //0x00003d06 ja           LBB0_809
	0x49, 0x8d, 0x70, 0x01, //0x00003d0c leaq         $1(%r8), %rsi
	0x48, 0x8d, 0x3d, 0x25, 0x21, 0x00, 0x00, //0x00003d10 leaq         $8485(%rip), %rdi  /* LJTI0_5+0(%rip) */
	0x48, 0x63, 0x04, 0x97, //0x00003d17 movslq       (%rdi,%rdx,4), %rax
	0x48, 0x01, 0xf8, //0x00003d1b addq         %rdi, %rax
	0xff, 0xe0, //0x00003d1e jmpq         *%rax
	//0x00003d20 LBB0_803
	0x49, 0x89, 0xf0, //0x00003d20 movq         %rsi, %r8
	0x4d, 0x29, 0xc8, //0x00003d23 subq         %r9, %r8
	0x49, 0x83, 0xff, 0xff, //0x00003d26 cmpq         $-1, %r15
	0x0f, 0x85, 0x47, 0x0b, 0x00, 0x00, //0x00003d2a jne          LBB0_946
	0x49, 0xff, 0xc8, //0x00003d30 decq         %r8
	0x4d, 0x89, 0xc7, //0x00003d33 movq         %r8, %r15
	0xe9, 0xb5, 0xff, 0xff, 0xff, //0x00003d36 jmp          LBB0_800
	//0x00003d3b LBB0_805
	0x49, 0x89, 0xf0, //0x00003d3b movq         %rsi, %r8
	0x4d, 0x29, 0xc8, //0x00003d3e subq         %r9, %r8
	0x49, 0x83, 0xfc, 0xff, //0x00003d41 cmpq         $-1, %r12
	0x0f, 0x85, 0x2c, 0x0b, 0x00, 0x00, //0x00003d45 jne          LBB0_946
	0x49, 0xff, 0xc8, //0x00003d4b decq         %r8
	0x4d, 0x89, 0xc4, //0x00003d4e movq         %r8, %r12
	0xe9, 0x9a, 0xff, 0xff, 0xff, //0x00003d51 jmp          LBB0_800
	//0x00003d56 LBB0_807
	0x48, 0x01, 0xcb, //0x00003d56 addq         %rcx, %rbx
	0xc5, 0xf8, 0x77, //0x00003d59 vzeroupper   
	0xc5, 0x7a, 0x6f, 0x3d, 0xac, 0xc4, 0xff, 0xff, //0x00003d5c vmovdqu      $-15188(%rip), %xmm15  /* LCPI0_18+0(%rip) */
	0x49, 0x89, 0xd8, //0x00003d64 movq         %rbx, %r8
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00003d67 movq         $-1, %rcx
	0x4d, 0x85, 0xff, //0x00003d6e testq        %r15, %r15
	0x0f, 0x85, 0x1d, 0x00, 0x00, 0x00, //0x00003d71 jne          LBB0_810
	0xe9, 0x44, 0x17, 0x00, 0x00, //0x00003d77 jmp          LBB0_1086
	//0x00003d7c LBB0_808
	0x49, 0x01, 0xc8, //0x00003d7c addq         %rcx, %r8
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x00003d7f movq         $8(%rsp), %r14
	//0x00003d84 LBB0_809
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00003d84 movq         $-1, %rcx
	0x4d, 0x85, 0xff, //0x00003d8b testq        %r15, %r15
	0x0f, 0x84, 0x2c, 0x17, 0x00, 0x00, //0x00003d8e je           LBB0_1086
	//0x00003d94 LBB0_810
	0x4d, 0x85, 0xed, //0x00003d94 testq        %r13, %r13
	0x0f, 0x84, 0x23, 0x17, 0x00, 0x00, //0x00003d97 je           LBB0_1086
	0x4d, 0x85, 0xe4, //0x00003d9d testq        %r12, %r12
	0x0f, 0x84, 0x1a, 0x17, 0x00, 0x00, //0x00003da0 je           LBB0_1086
	0x4d, 0x29, 0xc8, //0x00003da6 subq         %r9, %r8
	0x49, 0x8d, 0x48, 0xff, //0x00003da9 leaq         $-1(%r8), %rcx
	0x49, 0x39, 0xcf, //0x00003dad cmpq         %rcx, %r15
	0x0f, 0x84, 0x83, 0x00, 0x00, 0x00, //0x00003db0 je           LBB0_818
	0x49, 0x39, 0xcc, //0x00003db6 cmpq         %rcx, %r12
	0x0f, 0x84, 0x7a, 0x00, 0x00, 0x00, //0x00003db9 je           LBB0_818
	0x49, 0x39, 0xcd, //0x00003dbf cmpq         %rcx, %r13
	0x0f, 0x84, 0x71, 0x00, 0x00, 0x00, //0x00003dc2 je           LBB0_818
	0x4d, 0x85, 0xed, //0x00003dc8 testq        %r13, %r13
	0xc5, 0xfe, 0x6f, 0x2d, 0x2d, 0xc2, 0xff, 0xff, //0x00003dcb vmovdqu      $-15827(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0xa5, 0xc2, 0xff, 0xff, //0x00003dd3 vmovdqu      $-15707(%rip), %ymm6  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0xbd, 0xc2, 0xff, 0xff, //0x00003ddb vmovdqu      $-15683(%rip), %ymm7  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x35, 0xc3, 0xff, 0xff, //0x00003de3 vmovdqu      $-15563(%rip), %ymm8  /* LCPI0_12+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0x4d, 0xc3, 0xff, 0xff, //0x00003deb vmovdqu      $-15539(%rip), %ymm9  /* LCPI0_13+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0x65, 0xc3, 0xff, 0xff, //0x00003df3 vmovdqu      $-15515(%rip), %ymm10  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x7d, 0xc3, 0xff, 0xff, //0x00003dfb vmovdqu      $-15491(%rip), %ymm11  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x35, 0xc2, 0xff, 0xff, //0x00003e03 vmovdqu      $-15819(%rip), %ymm12  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x8d, 0xc3, 0xff, 0xff, //0x00003e0b vmovdqu      $-15475(%rip), %ymm13  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0xa5, 0xc3, 0xff, 0xff, //0x00003e13 vmovdqu      $-15451(%rip), %ymm14  /* LCPI0_17+0(%rip) */
	0x0f, 0x8e, 0x75, 0x00, 0x00, 0x00, //0x00003e1b jle          LBB0_819
	0x49, 0x8d, 0x45, 0xff, //0x00003e21 leaq         $-1(%r13), %rax
	0x49, 0x39, 0xc7, //0x00003e25 cmpq         %rax, %r15
	0x0f, 0x84, 0x68, 0x00, 0x00, 0x00, //0x00003e28 je           LBB0_819
	0x49, 0xf7, 0xd5, //0x00003e2e notq         %r13
	0x4d, 0x89, 0xe8, //0x00003e31 movq         %r13, %r8
	0xe9, 0x09, 0x03, 0x00, 0x00, //0x00003e34 jmp          LBB0_864
	//0x00003e39 LBB0_818
	0x49, 0xf7, 0xd8, //0x00003e39 negq         %r8
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x00003e3c movq         $16(%rsp), %r15
	0xc5, 0xfe, 0x6f, 0x2d, 0xb7, 0xc1, 0xff, 0xff, //0x00003e41 vmovdqu      $-15945(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x2f, 0xc2, 0xff, 0xff, //0x00003e49 vmovdqu      $-15825(%rip), %ymm6  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x47, 0xc2, 0xff, 0xff, //0x00003e51 vmovdqu      $-15801(%rip), %ymm7  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0xbf, 0xc2, 0xff, 0xff, //0x00003e59 vmovdqu      $-15681(%rip), %ymm8  /* LCPI0_12+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0xd7, 0xc2, 0xff, 0xff, //0x00003e61 vmovdqu      $-15657(%rip), %ymm9  /* LCPI0_13+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0xef, 0xc2, 0xff, 0xff, //0x00003e69 vmovdqu      $-15633(%rip), %ymm10  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x07, 0xc3, 0xff, 0xff, //0x00003e71 vmovdqu      $-15609(%rip), %ymm11  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0xbf, 0xc1, 0xff, 0xff, //0x00003e79 vmovdqu      $-15937(%rip), %ymm12  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x17, 0xc3, 0xff, 0xff, //0x00003e81 vmovdqu      $-15593(%rip), %ymm13  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x2f, 0xc3, 0xff, 0xff, //0x00003e89 vmovdqu      $-15569(%rip), %ymm14  /* LCPI0_17+0(%rip) */
	0xe9, 0xb1, 0x02, 0x00, 0x00, //0x00003e91 jmp          LBB0_865
	//0x00003e96 LBB0_819
	0x4c, 0x89, 0xe1, //0x00003e96 movq         %r12, %rcx
	0x4c, 0x09, 0xf9, //0x00003e99 orq          %r15, %rcx
	0x4d, 0x39, 0xfc, //0x00003e9c cmpq         %r15, %r12
	0x0f, 0x8c, 0x88, 0x02, 0x00, 0x00, //0x00003e9f jl           LBB0_863
	0x48, 0x85, 0xc9, //0x00003ea5 testq        %rcx, %rcx
	0x0f, 0x88, 0x7f, 0x02, 0x00, 0x00, //0x00003ea8 js           LBB0_863
	0x49, 0xf7, 0xd4, //0x00003eae notq         %r12
	0x4d, 0x89, 0xe0, //0x00003eb1 movq         %r12, %r8
	0xe9, 0x89, 0x02, 0x00, 0x00, //0x00003eb4 jmp          LBB0_864
	//0x00003eb9 LBB0_822
	0x4c, 0x89, 0x04, 0x24, //0x00003eb9 movq         %r8, (%rsp)
	0x4d, 0x8b, 0x47, 0x08, //0x00003ebd movq         $8(%r15), %r8
	0x4c, 0x89, 0xc1, //0x00003ec1 movq         %r8, %rcx
	0x4c, 0x29, 0xe9, //0x00003ec4 subq         %r13, %rcx
	0x0f, 0x84, 0xed, 0x17, 0x00, 0x00, //0x00003ec7 je           LBB0_1116
	0x4c, 0x89, 0x6c, 0x24, 0x20, //0x00003ecd movq         %r13, $32(%rsp)
	0x4b, 0x8d, 0x1c, 0x2c, //0x00003ed2 leaq         (%r12,%r13), %rbx
	0x48, 0x83, 0xf9, 0x40, //0x00003ed6 cmpq         $64, %rcx
	0x0f, 0x82, 0xb5, 0x0a, 0x00, 0x00, //0x00003eda jb           LBB0_961
	0x41, 0x89, 0xca, //0x00003ee0 movl         %ecx, %r10d
	0x41, 0x83, 0xe2, 0x3f, //0x00003ee3 andl         $63, %r10d
	0x4c, 0x89, 0xc0, //0x00003ee7 movq         %r8, %rax
	0x4c, 0x29, 0xd8, //0x00003eea subq         %r11, %rax
	0x48, 0x83, 0xc0, 0xbf, //0x00003eed addq         $-65, %rax
	0x48, 0x83, 0xe0, 0xc0, //0x00003ef1 andq         $-64, %rax
	0x4c, 0x01, 0xd8, //0x00003ef5 addq         %r11, %rax
	0x4d, 0x8d, 0x6c, 0x04, 0x41, //0x00003ef8 leaq         $65(%r12,%rax), %r13
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00003efd movq         $-1, %r9
	0x45, 0x31, 0xf6, //0x00003f04 xorl         %r14d, %r14d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003f07 .p2align 4, 0x90
	//0x00003f10 LBB0_825
	0xc5, 0xfe, 0x6f, 0x03, //0x00003f10 vmovdqu      (%rbx), %ymm0
	0xc5, 0xfe, 0x6f, 0x4b, 0x20, //0x00003f14 vmovdqu      $32(%rbx), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x00003f19 vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00003f1d vpmovmskb    %ymm2, %edx
	0xc5, 0xf5, 0x74, 0xd6, //0x00003f21 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00003f25 vpmovmskb    %ymm2, %edi
	0xc5, 0xfd, 0x74, 0xc7, //0x00003f29 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00003f2d vpmovmskb    %ymm0, %esi
	0xc5, 0xf5, 0x74, 0xc7, //0x00003f31 vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x00003f35 vpmovmskb    %ymm0, %eax
	0x48, 0xc1, 0xe7, 0x20, //0x00003f39 shlq         $32, %rdi
	0x48, 0xc1, 0xe0, 0x20, //0x00003f3d shlq         $32, %rax
	0x48, 0x09, 0xc6, //0x00003f41 orq          %rax, %rsi
	0x49, 0x83, 0xf9, 0xff, //0x00003f44 cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00003f48 jne          LBB0_827
	0x48, 0x85, 0xf6, //0x00003f4e testq        %rsi, %rsi
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x00003f51 jne          LBB0_835
	//0x00003f57 LBB0_827
	0x48, 0x09, 0xd7, //0x00003f57 orq          %rdx, %rdi
	0x48, 0x89, 0xf0, //0x00003f5a movq         %rsi, %rax
	0x4c, 0x09, 0xf0, //0x00003f5d orq          %r14, %rax
	0x0f, 0x85, 0x3c, 0x00, 0x00, 0x00, //0x00003f60 jne          LBB0_836
	//0x00003f66 LBB0_828
	0x48, 0x85, 0xff, //0x00003f66 testq        %rdi, %rdi
	0x0f, 0x85, 0x8a, 0x00, 0x00, 0x00, //0x00003f69 jne          LBB0_837
	//0x00003f6f LBB0_829
	0x48, 0x83, 0xc1, 0xc0, //0x00003f6f addq         $-64, %rcx
	0x48, 0x83, 0xc3, 0x40, //0x00003f73 addq         $64, %rbx
	0x48, 0x83, 0xf9, 0x3f, //0x00003f77 cmpq         $63, %rcx
	0x0f, 0x87, 0x8f, 0xff, 0xff, 0xff, //0x00003f7b ja           LBB0_825
	0xe9, 0x84, 0x08, 0x00, 0x00, //0x00003f81 jmp          LBB0_830
	//0x00003f86 LBB0_835
	0x48, 0x89, 0xd8, //0x00003f86 movq         %rbx, %rax
	0x4c, 0x29, 0xe0, //0x00003f89 subq         %r12, %rax
	0x4c, 0x0f, 0xbc, 0xce, //0x00003f8c bsfq         %rsi, %r9
	0x49, 0x01, 0xc1, //0x00003f90 addq         %rax, %r9
	0x48, 0x09, 0xd7, //0x00003f93 orq          %rdx, %rdi
	0x48, 0x89, 0xf0, //0x00003f96 movq         %rsi, %rax
	0x4c, 0x09, 0xf0, //0x00003f99 orq          %r14, %rax
	0x0f, 0x84, 0xc4, 0xff, 0xff, 0xff, //0x00003f9c je           LBB0_828
	//0x00003fa2 LBB0_836
	0x4c, 0x89, 0xf0, //0x00003fa2 movq         %r14, %rax
	0x48, 0xf7, 0xd0, //0x00003fa5 notq         %rax
	0x48, 0x21, 0xf0, //0x00003fa8 andq         %rsi, %rax
	0x4c, 0x8d, 0x3c, 0x00, //0x00003fab leaq         (%rax,%rax), %r15
	0x4d, 0x09, 0xf7, //0x00003faf orq          %r14, %r15
	0x4c, 0x89, 0xfa, //0x00003fb2 movq         %r15, %rdx
	0x48, 0xf7, 0xd2, //0x00003fb5 notq         %rdx
	0x48, 0x21, 0xf2, //0x00003fb8 andq         %rsi, %rdx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00003fbb movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf2, //0x00003fc5 andq         %rsi, %rdx
	0x45, 0x31, 0xf6, //0x00003fc8 xorl         %r14d, %r14d
	0x48, 0x01, 0xc2, //0x00003fcb addq         %rax, %rdx
	0x41, 0x0f, 0x92, 0xc6, //0x00003fce setb         %r14b
	0x48, 0x01, 0xd2, //0x00003fd2 addq         %rdx, %rdx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00003fd5 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc2, //0x00003fdf xorq         %rax, %rdx
	0x4c, 0x21, 0xfa, //0x00003fe2 andq         %r15, %rdx
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x00003fe5 movq         $16(%rsp), %r15
	0x48, 0xf7, 0xd2, //0x00003fea notq         %rdx
	0x48, 0x21, 0xd7, //0x00003fed andq         %rdx, %rdi
	0x48, 0x85, 0xff, //0x00003ff0 testq        %rdi, %rdi
	0x0f, 0x84, 0x76, 0xff, 0xff, 0xff, //0x00003ff3 je           LBB0_829
	//0x00003ff9 LBB0_837
	0x48, 0x0f, 0xbc, 0xc7, //0x00003ff9 bsfq         %rdi, %rax
	0x4c, 0x29, 0xe3, //0x00003ffd subq         %r12, %rbx
	0x4c, 0x8d, 0x6c, 0x03, 0x01, //0x00004000 leaq         $1(%rbx,%rax), %r13
	0x4c, 0x8b, 0x54, 0x24, 0x28, //0x00004005 movq         $40(%rsp), %r10
	0x4d, 0x85, 0xed, //0x0000400a testq        %r13, %r13
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x0000400d movq         $8(%rsp), %r14
	0x0f, 0x88, 0x86, 0x14, 0x00, 0x00, //0x00004012 js           LBB0_1082
	//0x00004018 LBB0_838
	0x4d, 0x89, 0x2e, //0x00004018 movq         %r13, (%r14)
	0x4c, 0x89, 0xd9, //0x0000401b movq         %r11, %rcx
	0x48, 0x83, 0x7c, 0x24, 0x20, 0x00, //0x0000401e cmpq         $0, $32(%rsp)
	0xe9, 0x45, 0x01, 0x00, 0x00, //0x00004024 jmp          LBB0_868
	//0x00004029 LBB0_839
	0x4d, 0x8b, 0x4f, 0x08, //0x00004029 movq         $8(%r15), %r9
	0x4d, 0x29, 0xe9, //0x0000402d subq         %r13, %r9
	0x0f, 0x84, 0x32, 0x16, 0x00, 0x00, //0x00004030 je           LBB0_1106
	0x4c, 0x89, 0x6c, 0x24, 0x20, //0x00004036 movq         %r13, $32(%rsp)
	0x4d, 0x01, 0xec, //0x0000403b addq         %r13, %r12
	0x41, 0x80, 0x3c, 0x24, 0x30, //0x0000403e cmpb         $48, (%r12)
	0x0f, 0x85, 0x78, 0x01, 0x00, 0x00, //0x00004043 jne          LBB0_872
	0x41, 0xbe, 0x01, 0x00, 0x00, 0x00, //0x00004049 movl         $1, %r14d
	0x49, 0x83, 0xf9, 0x01, //0x0000404f cmpq         $1, %r9
	0x0f, 0x85, 0x3b, 0x01, 0x00, 0x00, //0x00004053 jne          LBB0_870
	0x4c, 0x8b, 0x6c, 0x24, 0x20, //0x00004059 movq         $32(%rsp), %r13
	0xe9, 0x92, 0x06, 0x00, 0x00, //0x0000405e jmp          LBB0_935
	//0x00004063 LBB0_843
	0x49, 0x8b, 0x02, //0x00004063 movq         (%r10), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00004066 cmpq         $4095, %rax
	0x0f, 0x8f, 0x3f, 0x0d, 0x00, 0x00, //0x0000406c jg           LBB0_1097
	0x48, 0x8d, 0x48, 0x01, //0x00004072 leaq         $1(%rax), %rcx
	0x49, 0x89, 0x0a, //0x00004076 movq         %rcx, (%r10)
	0x49, 0xc7, 0x44, 0xc2, 0x08, 0x05, 0x00, 0x00, 0x00, //0x00004079 movq         $5, $8(%r10,%rax,8)
	0xe9, 0xa9, 0xf2, 0xff, 0xff, //0x00004082 jmp          LBB0_662
	//0x00004087 LBB0_845
	0x49, 0x8b, 0x57, 0x08, //0x00004087 movq         $8(%r15), %rdx
	0x48, 0x8d, 0x42, 0xfc, //0x0000408b leaq         $-4(%rdx), %rax
	0x49, 0x39, 0xc3, //0x0000408f cmpq         %rax, %r11
	0x0f, 0x83, 0x3a, 0x14, 0x00, 0x00, //0x00004092 jae          LBB0_1096
	0x43, 0x8b, 0x14, 0x2c, //0x00004098 movl         (%r12,%r13), %edx
	0x81, 0xfa, 0x61, 0x6c, 0x73, 0x65, //0x0000409c cmpl         $1702063201, %edx
	0x0f, 0x85, 0xb6, 0x14, 0x00, 0x00, //0x000040a2 jne          LBB0_1098
	0x4c, 0x89, 0xe8, //0x000040a8 movq         %r13, %rax
	0x4d, 0x8d, 0x6b, 0x05, //0x000040ab leaq         $5(%r11), %r13
	0x4d, 0x89, 0x2e, //0x000040af movq         %r13, (%r14)
	0x4c, 0x89, 0xd9, //0x000040b2 movq         %r11, %rcx
	0x48, 0x85, 0xc0, //0x000040b5 testq        %rax, %rax
	0x0f, 0x8f, 0x72, 0xf2, 0xff, 0xff, //0x000040b8 jg           LBB0_662
	0xe9, 0x3b, 0xf2, 0xff, 0xff, //0x000040be jmp          LBB0_659
	//0x000040c3 LBB0_848
	0x49, 0x8b, 0x57, 0x08, //0x000040c3 movq         $8(%r15), %rdx
	0x48, 0x8d, 0x42, 0xfd, //0x000040c7 leaq         $-3(%rdx), %rax
	0x49, 0x39, 0xc3, //0x000040cb cmpq         %rax, %r11
	0x0f, 0x83, 0xfe, 0x13, 0x00, 0x00, //0x000040ce jae          LBB0_1096
	0x41, 0x81, 0x39, 0x6e, 0x75, 0x6c, 0x6c, //0x000040d4 cmpl         $1819047278, (%r9)
	0x0f, 0x84, 0x32, 0xf2, 0xff, 0xff, //0x000040db je           LBB0_660
	0xe9, 0xca, 0x14, 0x00, 0x00, //0x000040e1 jmp          LBB0_850
	//0x000040e6 LBB0_855
	0x49, 0x8b, 0x57, 0x08, //0x000040e6 movq         $8(%r15), %rdx
	0x48, 0x8d, 0x42, 0xfd, //0x000040ea leaq         $-3(%rdx), %rax
	0x49, 0x39, 0xc3, //0x000040ee cmpq         %rax, %r11
	0x0f, 0x83, 0xdb, 0x13, 0x00, 0x00, //0x000040f1 jae          LBB0_1096
	0x41, 0x81, 0x39, 0x74, 0x72, 0x75, 0x65, //0x000040f7 cmpl         $1702195828, (%r9)
	0x0f, 0x84, 0x0f, 0xf2, 0xff, 0xff, //0x000040fe je           LBB0_660
	0xe9, 0xf9, 0x14, 0x00, 0x00, //0x00004104 jmp          LBB0_857
	//0x00004109 LBB0_861
	0x49, 0x8b, 0x02, //0x00004109 movq         (%r10), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x0000410c cmpq         $4095, %rax
	0x0f, 0x8f, 0x99, 0x0c, 0x00, 0x00, //0x00004112 jg           LBB0_1097
	0x48, 0x8d, 0x48, 0x01, //0x00004118 leaq         $1(%rax), %rcx
	0x49, 0x89, 0x0a, //0x0000411c movq         %rcx, (%r10)
	0x49, 0xc7, 0x44, 0xc2, 0x08, 0x06, 0x00, 0x00, 0x00, //0x0000411f movq         $6, $8(%r10,%rax,8)
	0xe9, 0x03, 0xf2, 0xff, 0xff, //0x00004128 jmp          LBB0_662
	//0x0000412d LBB0_863
	0x48, 0x85, 0xc9, //0x0000412d testq        %rcx, %rcx
	0x49, 0x8d, 0x47, 0xff, //0x00004130 leaq         $-1(%r15), %rax
	0x49, 0xf7, 0xd7, //0x00004134 notq         %r15
	0x4d, 0x0f, 0x48, 0xf8, //0x00004137 cmovsq       %r8, %r15
	0x49, 0x39, 0xc4, //0x0000413b cmpq         %rax, %r12
	0x4d, 0x0f, 0x44, 0xc7, //0x0000413e cmoveq       %r15, %r8
	//0x00004142 LBB0_864
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x00004142 movq         $16(%rsp), %r15
	//0x00004147 LBB0_865
	0x4c, 0x8b, 0x6c, 0x24, 0x20, //0x00004147 movq         $32(%rsp), %r13
	0x4d, 0x85, 0xc0, //0x0000414c testq        %r8, %r8
	0x0f, 0x88, 0x68, 0x13, 0x00, 0x00, //0x0000414f js           LBB0_1085
	0x49, 0x8b, 0x0e, //0x00004155 movq         (%r14), %rcx
	//0x00004158 LBB0_867
	0x4c, 0x89, 0xe8, //0x00004158 movq         %r13, %rax
	0x4e, 0x8d, 0x6c, 0x01, 0xff, //0x0000415b leaq         $-1(%rcx,%r8), %r13
	0x4d, 0x89, 0x2e, //0x00004160 movq         %r13, (%r14)
	0x4c, 0x89, 0xd9, //0x00004163 movq         %r11, %rcx
	0x48, 0x85, 0xc0, //0x00004166 testq        %rax, %rax
	0x4c, 0x8b, 0x54, 0x24, 0x28, //0x00004169 movq         $40(%rsp), %r10
	//0x0000416e LBB0_868
	0x4c, 0x8b, 0x04, 0x24, //0x0000416e movq         (%rsp), %r8
	0x0f, 0x8f, 0xb8, 0xf1, 0xff, 0xff, //0x00004172 jg           LBB0_662
	0xe9, 0x81, 0xf1, 0xff, 0xff, //0x00004178 jmp          LBB0_659
	//0x0000417d LBB0_869
	0x4c, 0x89, 0xe1, //0x0000417d movq         %r12, %rcx
	0x48, 0xf7, 0xd1, //0x00004180 notq         %rcx
	0x49, 0x01, 0xcb, //0x00004183 addq         %rcx, %r11
	0x49, 0x39, 0xd3, //0x00004186 cmpq         %rdx, %r11
	0x0f, 0x82, 0x6c, 0xf3, 0xff, 0xff, //0x00004189 jb           LBB0_695
	0xe9, 0x11, 0x0c, 0x00, 0x00, //0x0000418f jmp          LBB0_1014
	//0x00004194 LBB0_870
	0x41, 0x8a, 0x4c, 0x24, 0x01, //0x00004194 movb         $1(%r12), %cl
	0x80, 0xc1, 0xd2, //0x00004199 addb         $-46, %cl
	0x80, 0xf9, 0x37, //0x0000419c cmpb         $55, %cl
	0x4c, 0x8b, 0x6c, 0x24, 0x20, //0x0000419f movq         $32(%rsp), %r13
	0x0f, 0x87, 0x4b, 0x05, 0x00, 0x00, //0x000041a4 ja           LBB0_935
	0x0f, 0xb6, 0xc1, //0x000041aa movzbl       %cl, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x000041ad movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x000041b7 btq          %rax, %rcx
	0x0f, 0x83, 0x34, 0x05, 0x00, 0x00, //0x000041bb jae          LBB0_935
	//0x000041c1 LBB0_872
	0x49, 0x83, 0xf9, 0x20, //0x000041c1 cmpq         $32, %r9
	0x0f, 0x82, 0xef, 0x07, 0x00, 0x00, //0x000041c5 jb           LBB0_962
	0x49, 0x8d, 0x49, 0xe0, //0x000041cb leaq         $-32(%r9), %rcx
	0x48, 0x89, 0xc8, //0x000041cf movq         %rcx, %rax
	0x48, 0x83, 0xe0, 0xe0, //0x000041d2 andq         $-32, %rax
	0x4e, 0x8d, 0x74, 0x20, 0x20, //0x000041d6 leaq         $32(%rax,%r12), %r14
	0x83, 0xe1, 0x1f, //0x000041db andl         $31, %ecx
	0x48, 0x89, 0x8c, 0x24, 0x98, 0x00, 0x00, 0x00, //0x000041de movq         %rcx, $152(%rsp)
	0x48, 0xc7, 0x44, 0x24, 0x18, 0xff, 0xff, 0xff, 0xff, //0x000041e6 movq         $-1, $24(%rsp)
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x000041ef movq         $-1, %r13
	0x48, 0xc7, 0x44, 0x24, 0x30, 0xff, 0xff, 0xff, 0xff, //0x000041f6 movq         $-1, $48(%rsp)
	0x4d, 0x89, 0xe7, //0x000041ff movq         %r12, %r15
	0x4c, 0x89, 0x04, 0x24, //0x00004202 movq         %r8, (%rsp)
	//0x00004206 LBB0_874
	0xc4, 0xc1, 0x7e, 0x6f, 0x07, //0x00004206 vmovdqu      (%r15), %ymm0
	0xc4, 0xc1, 0x7d, 0x64, 0xc8, //0x0000420b vpcmpgtb     %ymm8, %ymm0, %ymm1
	0xc5, 0xb5, 0x64, 0xd0, //0x00004210 vpcmpgtb     %ymm0, %ymm9, %ymm2
	0xc5, 0xf5, 0xdb, 0xca, //0x00004214 vpand        %ymm2, %ymm1, %ymm1
	0xc5, 0xad, 0x74, 0xd0, //0x00004218 vpcmpeqb     %ymm0, %ymm10, %ymm2
	0xc5, 0xa5, 0x74, 0xd8, //0x0000421c vpcmpeqb     %ymm0, %ymm11, %ymm3
	0xc5, 0xe5, 0xeb, 0xd2, //0x00004220 vpor         %ymm2, %ymm3, %ymm2
	0xc5, 0x9d, 0xeb, 0xd8, //0x00004224 vpor         %ymm0, %ymm12, %ymm3
	0xc5, 0x95, 0x74, 0xc0, //0x00004228 vpcmpeqb     %ymm0, %ymm13, %ymm0
	0xc5, 0xfd, 0xd7, 0xd8, //0x0000422c vpmovmskb    %ymm0, %ebx
	0xc5, 0x8d, 0x74, 0xdb, //0x00004230 vpcmpeqb     %ymm3, %ymm14, %ymm3
	0xc5, 0xfd, 0xd7, 0xf3, //0x00004234 vpmovmskb    %ymm3, %esi
	0xc5, 0xfd, 0xd7, 0xfa, //0x00004238 vpmovmskb    %ymm2, %edi
	0xc5, 0xe5, 0xeb, 0xc0, //0x0000423c vpor         %ymm0, %ymm3, %ymm0
	0xc5, 0xed, 0xeb, 0xc9, //0x00004240 vpor         %ymm1, %ymm2, %ymm1
	0xc5, 0xfd, 0xeb, 0xc1, //0x00004244 vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x00004248 vpmovmskb    %ymm0, %eax
	0x48, 0xf7, 0xd0, //0x0000424c notq         %rax
	0x48, 0x0f, 0xbc, 0xc8, //0x0000424f bsfq         %rax, %rcx
	0x4d, 0x89, 0xd0, //0x00004253 movq         %r10, %r8
	0x83, 0xf9, 0x20, //0x00004256 cmpl         $32, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x00004259 je           LBB0_876
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x0000425f movl         $-1, %eax
	0xd3, 0xe0, //0x00004264 shll         %cl, %eax
	0xf7, 0xd0, //0x00004266 notl         %eax
	0x21, 0xc3, //0x00004268 andl         %eax, %ebx
	0x21, 0xc6, //0x0000426a andl         %eax, %esi
	0x21, 0xf8, //0x0000426c andl         %edi, %eax
	0x89, 0xc7, //0x0000426e movl         %eax, %edi
	//0x00004270 LBB0_876
	0x44, 0x8d, 0x53, 0xff, //0x00004270 leal         $-1(%rbx), %r10d
	0x41, 0x21, 0xda, //0x00004274 andl         %ebx, %r10d
	0x0f, 0x85, 0x46, 0x06, 0x00, 0x00, //0x00004277 jne          LBB0_950
	0x8d, 0x56, 0xff, //0x0000427d leal         $-1(%rsi), %edx
	0x21, 0xf2, //0x00004280 andl         %esi, %edx
	0x0f, 0x85, 0x47, 0x06, 0x00, 0x00, //0x00004282 jne          LBB0_951
	0x8d, 0x57, 0xff, //0x00004288 leal         $-1(%rdi), %edx
	0x21, 0xfa, //0x0000428b andl         %edi, %edx
	0x4d, 0x89, 0xc2, //0x0000428d movq         %r8, %r10
	0x0f, 0x85, 0x57, 0x06, 0x00, 0x00, //0x00004290 jne          LBB0_953
	0x85, 0xdb, //0x00004296 testl        %ebx, %ebx
	0x4c, 0x8b, 0x04, 0x24, //0x00004298 movq         (%rsp), %r8
	0x0f, 0x84, 0x1d, 0x00, 0x00, 0x00, //0x0000429c je           LBB0_882
	0x4c, 0x89, 0xf8, //0x000042a2 movq         %r15, %rax
	0x4c, 0x29, 0xe0, //0x000042a5 subq         %r12, %rax
	0x0f, 0xbc, 0xdb, //0x000042a8 bsfl         %ebx, %ebx
	0x48, 0x01, 0xc3, //0x000042ab addq         %rax, %rbx
	0x48, 0x83, 0x7c, 0x24, 0x30, 0xff, //0x000042ae cmpq         $-1, $48(%rsp)
	0x0f, 0x85, 0x6b, 0x06, 0x00, 0x00, //0x000042b4 jne          LBB0_955
	0x48, 0x89, 0x5c, 0x24, 0x30, //0x000042ba movq         %rbx, $48(%rsp)
	//0x000042bf LBB0_882
	0x85, 0xf6, //0x000042bf testl        %esi, %esi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x000042c1 je           LBB0_885
	0x4c, 0x89, 0xf8, //0x000042c7 movq         %r15, %rax
	0x4c, 0x29, 0xe0, //0x000042ca subq         %r12, %rax
	0x0f, 0xbc, 0xf6, //0x000042cd bsfl         %esi, %esi
	0x48, 0x01, 0xc6, //0x000042d0 addq         %rax, %rsi
	0x49, 0x83, 0xfd, 0xff, //0x000042d3 cmpq         $-1, %r13
	0x0f, 0x85, 0xb6, 0x05, 0x00, 0x00, //0x000042d7 jne          LBB0_948
	0x49, 0x89, 0xf5, //0x000042dd movq         %rsi, %r13
	//0x000042e0 LBB0_885
	0x85, 0xff, //0x000042e0 testl        %edi, %edi
	0x0f, 0x84, 0x1d, 0x00, 0x00, 0x00, //0x000042e2 je           LBB0_888
	0x4c, 0x89, 0xf8, //0x000042e8 movq         %r15, %rax
	0x4c, 0x29, 0xe0, //0x000042eb subq         %r12, %rax
	0x0f, 0xbc, 0xf7, //0x000042ee bsfl         %edi, %esi
	0x48, 0x01, 0xc6, //0x000042f1 addq         %rax, %rsi
	0x48, 0x83, 0x7c, 0x24, 0x18, 0xff, //0x000042f4 cmpq         $-1, $24(%rsp)
	0x0f, 0x85, 0x93, 0x05, 0x00, 0x00, //0x000042fa jne          LBB0_948
	0x48, 0x89, 0x74, 0x24, 0x18, //0x00004300 movq         %rsi, $24(%rsp)
	//0x00004305 LBB0_888
	0x83, 0xf9, 0x20, //0x00004305 cmpl         $32, %ecx
	0x0f, 0x85, 0x30, 0x02, 0x00, 0x00, //0x00004308 jne          LBB0_1104
	0x49, 0x83, 0xc7, 0x20, //0x0000430e addq         $32, %r15
	0x49, 0x83, 0xc1, 0xe0, //0x00004312 addq         $-32, %r9
	0x49, 0x83, 0xf9, 0x1f, //0x00004316 cmpq         $31, %r9
	0x0f, 0x87, 0xe6, 0xfe, 0xff, 0xff, //0x0000431a ja           LBB0_874
	0xc5, 0xf8, 0x77, //0x00004320 vzeroupper   
	0x4c, 0x8b, 0x8c, 0x24, 0x98, 0x00, 0x00, 0x00, //0x00004323 movq         $152(%rsp), %r9
	//0x0000432b LBB0_891
	0x49, 0x83, 0xf9, 0x10, //0x0000432b cmpq         $16, %r9
	0xc5, 0xfe, 0x6f, 0x2d, 0xc9, 0xbc, 0xff, 0xff, //0x0000432f vmovdqu      $-17207(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x41, 0xbd, 0xff, 0xff, //0x00004337 vmovdqu      $-17087(%rip), %ymm6  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x59, 0xbd, 0xff, 0xff, //0x0000433f vmovdqu      $-17063(%rip), %ymm7  /* LCPI0_8+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x3d, 0xc1, 0xbe, 0xff, 0xff, //0x00004347 vmovdqu      $-16703(%rip), %xmm15  /* LCPI0_18+0(%rip) */
	0x0f, 0x82, 0x49, 0x01, 0x00, 0x00, //0x0000434f jb           LBB0_910
	0x4d, 0x8d, 0x79, 0xf0, //0x00004355 leaq         $-16(%r9), %r15
	0x4c, 0x89, 0xf8, //0x00004359 movq         %r15, %rax
	0x48, 0x83, 0xe0, 0xf0, //0x0000435c andq         $-16, %rax
	0x4e, 0x8d, 0x54, 0x30, 0x10, //0x00004360 leaq         $16(%rax,%r14), %r10
	0x41, 0x83, 0xe7, 0x0f, //0x00004365 andl         $15, %r15d
	//0x00004369 LBB0_893
	0xc4, 0xc1, 0x7a, 0x6f, 0x06, //0x00004369 vmovdqu      (%r14), %xmm0
	0xc4, 0xc1, 0x79, 0x64, 0xcf, //0x0000436e vpcmpgtb     %xmm15, %xmm0, %xmm1
	0xc5, 0xfa, 0x6f, 0x15, 0xa5, 0xbe, 0xff, 0xff, //0x00004373 vmovdqu      $-16731(%rip), %xmm2  /* LCPI0_19+0(%rip) */
	0xc5, 0xe9, 0x64, 0xd0, //0x0000437b vpcmpgtb     %xmm0, %xmm2, %xmm2
	0xc5, 0xf1, 0xdb, 0xca, //0x0000437f vpand        %xmm2, %xmm1, %xmm1
	0xc5, 0xf9, 0x74, 0x15, 0xa5, 0xbe, 0xff, 0xff, //0x00004383 vpcmpeqb     $-16731(%rip), %xmm0, %xmm2  /* LCPI0_20+0(%rip) */
	0xc5, 0xf9, 0x74, 0x1d, 0xad, 0xbe, 0xff, 0xff, //0x0000438b vpcmpeqb     $-16723(%rip), %xmm0, %xmm3  /* LCPI0_21+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xd2, //0x00004393 vpor         %xmm2, %xmm3, %xmm2
	0xc5, 0xf9, 0xeb, 0x1d, 0x51, 0xbe, 0xff, 0xff, //0x00004397 vpor         $-16815(%rip), %xmm0, %xmm3  /* LCPI0_5+0(%rip) */
	0xc5, 0xf9, 0x74, 0x05, 0xa9, 0xbe, 0xff, 0xff, //0x0000439f vpcmpeqb     $-16727(%rip), %xmm0, %xmm0  /* LCPI0_22+0(%rip) */
	0xc5, 0xe1, 0x74, 0x1d, 0xb1, 0xbe, 0xff, 0xff, //0x000043a7 vpcmpeqb     $-16719(%rip), %xmm3, %xmm3  /* LCPI0_23+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xe0, //0x000043af vpor         %xmm0, %xmm3, %xmm4
	0xc5, 0xe9, 0xeb, 0xc9, //0x000043b3 vpor         %xmm1, %xmm2, %xmm1
	0xc5, 0xd9, 0xeb, 0xc9, //0x000043b7 vpor         %xmm1, %xmm4, %xmm1
	0xc5, 0xf9, 0xd7, 0xd8, //0x000043bb vpmovmskb    %xmm0, %ebx
	0xc5, 0xf9, 0xd7, 0xf3, //0x000043bf vpmovmskb    %xmm3, %esi
	0xc5, 0xf9, 0xd7, 0xfa, //0x000043c3 vpmovmskb    %xmm2, %edi
	0xc5, 0xf9, 0xd7, 0xc1, //0x000043c7 vpmovmskb    %xmm1, %eax
	0xb9, 0xff, 0xff, 0xff, 0xff, //0x000043cb movl         $4294967295, %ecx
	0x48, 0x31, 0xc8, //0x000043d0 xorq         %rcx, %rax
	0x48, 0x0f, 0xbc, 0xc8, //0x000043d3 bsfq         %rax, %rcx
	0x83, 0xf9, 0x10, //0x000043d7 cmpl         $16, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x000043da je           LBB0_895
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x000043e0 movl         $-1, %eax
	0xd3, 0xe0, //0x000043e5 shll         %cl, %eax
	0xf7, 0xd0, //0x000043e7 notl         %eax
	0x21, 0xc3, //0x000043e9 andl         %eax, %ebx
	0x21, 0xc6, //0x000043eb andl         %eax, %esi
	0x21, 0xf8, //0x000043ed andl         %edi, %eax
	0x89, 0xc7, //0x000043ef movl         %eax, %edi
	//0x000043f1 LBB0_895
	0x8d, 0x53, 0xff, //0x000043f1 leal         $-1(%rbx), %edx
	0x21, 0xda, //0x000043f4 andl         %ebx, %edx
	0x0f, 0x85, 0x83, 0x04, 0x00, 0x00, //0x000043f6 jne          LBB0_947
	0x8d, 0x56, 0xff, //0x000043fc leal         $-1(%rsi), %edx
	0x21, 0xf2, //0x000043ff andl         %esi, %edx
	0x0f, 0x85, 0x78, 0x04, 0x00, 0x00, //0x00004401 jne          LBB0_947
	0x8d, 0x57, 0xff, //0x00004407 leal         $-1(%rdi), %edx
	0x21, 0xfa, //0x0000440a andl         %edi, %edx
	0x0f, 0x85, 0x6d, 0x04, 0x00, 0x00, //0x0000440c jne          LBB0_947
	0x85, 0xdb, //0x00004412 testl        %ebx, %ebx
	0x0f, 0x84, 0x1d, 0x00, 0x00, 0x00, //0x00004414 je           LBB0_901
	0x4c, 0x89, 0xf0, //0x0000441a movq         %r14, %rax
	0x4c, 0x29, 0xe0, //0x0000441d subq         %r12, %rax
	0x0f, 0xbc, 0xdb, //0x00004420 bsfl         %ebx, %ebx
	0x48, 0x01, 0xc3, //0x00004423 addq         %rax, %rbx
	0x48, 0x83, 0x7c, 0x24, 0x30, 0xff, //0x00004426 cmpq         $-1, $48(%rsp)
	0x0f, 0x85, 0x4e, 0x05, 0x00, 0x00, //0x0000442c jne          LBB0_959
	0x48, 0x89, 0x5c, 0x24, 0x30, //0x00004432 movq         %rbx, $48(%rsp)
	//0x00004437 LBB0_901
	0x85, 0xf6, //0x00004437 testl        %esi, %esi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x00004439 je           LBB0_904
	0x4c, 0x89, 0xf0, //0x0000443f movq         %r14, %rax
	0x4c, 0x29, 0xe0, //0x00004442 subq         %r12, %rax
	0x0f, 0xbc, 0xf6, //0x00004445 bsfl         %esi, %esi
	0x48, 0x01, 0xc6, //0x00004448 addq         %rax, %rsi
	0x49, 0x83, 0xfd, 0xff, //0x0000444b cmpq         $-1, %r13
	0x0f, 0x85, 0xe0, 0x04, 0x00, 0x00, //0x0000444f jne          LBB0_956
	0x49, 0x89, 0xf5, //0x00004455 movq         %rsi, %r13
	//0x00004458 LBB0_904
	0x85, 0xff, //0x00004458 testl        %edi, %edi
	0x0f, 0x84, 0x1d, 0x00, 0x00, 0x00, //0x0000445a je           LBB0_907
	0x4c, 0x89, 0xf0, //0x00004460 movq         %r14, %rax
	0x4c, 0x29, 0xe0, //0x00004463 subq         %r12, %rax
	0x0f, 0xbc, 0xf7, //0x00004466 bsfl         %edi, %esi
	0x48, 0x01, 0xc6, //0x00004469 addq         %rax, %rsi
	0x48, 0x83, 0x7c, 0x24, 0x18, 0xff, //0x0000446c cmpq         $-1, $24(%rsp)
	0x0f, 0x85, 0xbd, 0x04, 0x00, 0x00, //0x00004472 jne          LBB0_956
	0x48, 0x89, 0x74, 0x24, 0x18, //0x00004478 movq         %rsi, $24(%rsp)
	//0x0000447d LBB0_907
	0x83, 0xf9, 0x10, //0x0000447d cmpl         $16, %ecx
	0x0f, 0x85, 0x76, 0x01, 0x00, 0x00, //0x00004480 jne          LBB0_922
	0x49, 0x83, 0xc6, 0x10, //0x00004486 addq         $16, %r14
	0x49, 0x83, 0xc1, 0xf0, //0x0000448a addq         $-16, %r9
	0x49, 0x83, 0xf9, 0x0f, //0x0000448e cmpq         $15, %r9
	0x0f, 0x87, 0xd1, 0xfe, 0xff, 0xff, //0x00004492 ja           LBB0_893
	0x4d, 0x89, 0xf9, //0x00004498 movq         %r15, %r9
	0x4d, 0x89, 0xd6, //0x0000449b movq         %r10, %r14
	//0x0000449e LBB0_910
	0x4d, 0x85, 0xc9, //0x0000449e testq        %r9, %r9
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x000044a1 movq         $16(%rsp), %r15
	0x4c, 0x8b, 0x54, 0x24, 0x28, //0x000044a6 movq         $40(%rsp), %r10
	0x0f, 0x84, 0x58, 0x01, 0x00, 0x00, //0x000044ab je           LBB0_923
	0x4b, 0x8d, 0x0c, 0x0e, //0x000044b1 leaq         (%r14,%r9), %rcx
	0xe9, 0x26, 0x00, 0x00, 0x00, //0x000044b5 jmp          LBB0_915
	//0x000044ba LBB0_912
	0x49, 0x89, 0xd6, //0x000044ba movq         %rdx, %r14
	0x4d, 0x29, 0xe6, //0x000044bd subq         %r12, %r14
	0x48, 0x83, 0x7c, 0x24, 0x18, 0xff, //0x000044c0 cmpq         $-1, $24(%rsp)
	0x0f, 0x85, 0xdd, 0x01, 0x00, 0x00, //0x000044c6 jne          LBB0_932
	0x49, 0xff, 0xce, //0x000044cc decq         %r14
	0x4c, 0x89, 0x74, 0x24, 0x18, //0x000044cf movq         %r14, $24(%rsp)
	//0x000044d4 LBB0_914
	0x49, 0x89, 0xd6, //0x000044d4 movq         %rdx, %r14
	0x49, 0xff, 0xc9, //0x000044d7 decq         %r9
	0x0f, 0x84, 0x85, 0x04, 0x00, 0x00, //0x000044da je           LBB0_958
	//0x000044e0 LBB0_915
	0x41, 0x0f, 0xbe, 0x36, //0x000044e0 movsbl       (%r14), %esi
	0x83, 0xc6, 0xd5, //0x000044e4 addl         $-43, %esi
	0x83, 0xfe, 0x3a, //0x000044e7 cmpl         $58, %esi
	0x0f, 0x87, 0x19, 0x01, 0x00, 0x00, //0x000044ea ja           LBB0_923
	0x49, 0x8d, 0x56, 0x01, //0x000044f0 leaq         $1(%r14), %rdx
	0x48, 0x8d, 0x3d, 0x55, 0x18, 0x00, 0x00, //0x000044f4 leaq         $6229(%rip), %rdi  /* LJTI0_4+0(%rip) */
	0x48, 0x63, 0x04, 0xb7, //0x000044fb movslq       (%rdi,%rsi,4), %rax
	0x48, 0x01, 0xf8, //0x000044ff addq         %rdi, %rax
	0xff, 0xe0, //0x00004502 jmpq         *%rax
	//0x00004504 LBB0_917
	0x49, 0x89, 0xd6, //0x00004504 movq         %rdx, %r14
	0x4d, 0x29, 0xe6, //0x00004507 subq         %r12, %r14
	0x49, 0x83, 0xfd, 0xff, //0x0000450a cmpq         $-1, %r13
	0x0f, 0x85, 0x95, 0x01, 0x00, 0x00, //0x0000450e jne          LBB0_932
	0x49, 0xff, 0xce, //0x00004514 decq         %r14
	0x4d, 0x89, 0xf5, //0x00004517 movq         %r14, %r13
	0xe9, 0xb5, 0xff, 0xff, 0xff, //0x0000451a jmp          LBB0_914
	//0x0000451f LBB0_919
	0x49, 0x89, 0xd6, //0x0000451f movq         %rdx, %r14
	0x4d, 0x29, 0xe6, //0x00004522 subq         %r12, %r14
	0x48, 0x83, 0x7c, 0x24, 0x30, 0xff, //0x00004525 cmpq         $-1, $48(%rsp)
	0x0f, 0x85, 0x78, 0x01, 0x00, 0x00, //0x0000452b jne          LBB0_932
	0x49, 0xff, 0xce, //0x00004531 decq         %r14
	0x4c, 0x89, 0x74, 0x24, 0x30, //0x00004534 movq         %r14, $48(%rsp)
	0xe9, 0x96, 0xff, 0xff, 0xff, //0x00004539 jmp          LBB0_914
	//0x0000453e LBB0_1104
	0x49, 0x01, 0xcf, //0x0000453e addq         %rcx, %r15
	0xc5, 0xf8, 0x77, //0x00004541 vzeroupper   
	0xc5, 0x7a, 0x6f, 0x3d, 0xc4, 0xbc, 0xff, 0xff, //0x00004544 vmovdqu      $-17212(%rip), %xmm15  /* LCPI0_18+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x4c, 0xbb, 0xff, 0xff, //0x0000454c vmovdqu      $-17588(%rip), %ymm7  /* LCPI0_8+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x24, 0xbb, 0xff, 0xff, //0x00004554 vmovdqu      $-17628(%rip), %ymm6  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x2d, 0x9c, 0xba, 0xff, 0xff, //0x0000455c vmovdqu      $-17764(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0x4d, 0x89, 0xfe, //0x00004564 movq         %r15, %r14
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x00004567 movq         $16(%rsp), %r15
	0x4d, 0x85, 0xed, //0x0000456c testq        %r13, %r13
	0x48, 0x8b, 0x54, 0x24, 0x30, //0x0000456f movq         $48(%rsp), %rdx
	0x48, 0x8b, 0x74, 0x24, 0x18, //0x00004574 movq         $24(%rsp), %rsi
	0x0f, 0x85, 0x9d, 0x00, 0x00, 0x00, //0x00004579 jne          LBB0_924
	0xe9, 0xdc, 0x10, 0x00, 0x00, //0x0000457f jmp          LBB0_1105
	//0x00004584 LBB0_713
	0x4d, 0x89, 0xcf, //0x00004584 movq         %r9, %r15
	0x4c, 0x89, 0xd1, //0x00004587 movq         %r10, %rcx
	0x4c, 0x89, 0xeb, //0x0000458a movq         %r13, %rbx
	0x4d, 0x89, 0xc2, //0x0000458d movq         %r8, %r10
	0x4c, 0x8b, 0x04, 0x24, //0x00004590 movq         (%rsp), %r8
	0x48, 0x83, 0xf9, 0x20, //0x00004594 cmpq         $32, %rcx
	0x0f, 0x82, 0xb7, 0x04, 0x00, 0x00, //0x00004598 jb           LBB0_968
	//0x0000459e LBB0_714
	0xc5, 0xfe, 0x6f, 0x03, //0x0000459e vmovdqu      (%rbx), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x000045a2 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf9, //0x000045a6 vpmovmskb    %ymm1, %edi
	0xc5, 0xfd, 0x74, 0xc7, //0x000045aa vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x000045ae vpmovmskb    %ymm0, %esi
	0x85, 0xf6, //0x000045b2 testl        %esi, %esi
	0x0f, 0x85, 0x21, 0x04, 0x00, 0x00, //0x000045b4 jne          LBB0_963
	0x4d, 0x85, 0xf6, //0x000045ba testq        %r14, %r14
	0x0f, 0x85, 0x3d, 0x04, 0x00, 0x00, //0x000045bd jne          LBB0_965
	0x45, 0x31, 0xf6, //0x000045c3 xorl         %r14d, %r14d
	0x48, 0x85, 0xff, //0x000045c6 testq        %rdi, %rdi
	0x0f, 0x84, 0x7e, 0x04, 0x00, 0x00, //0x000045c9 je           LBB0_967
	//0x000045cf LBB0_717
	0x48, 0x0f, 0xbc, 0xc7, //0x000045cf bsfq         %rdi, %rax
	0x4c, 0x29, 0xe3, //0x000045d3 subq         %r12, %rbx
	0x4c, 0x8d, 0x6c, 0x03, 0x01, //0x000045d6 leaq         $1(%rbx,%rax), %r13
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x000045db movq         $8(%rsp), %r14
	0xe9, 0xf7, 0x04, 0x00, 0x00, //0x000045e0 jmp          LBB0_977
	//0x000045e5 LBB0_921
	0x4c, 0x29, 0xcb, //0x000045e5 subq         %r9, %rbx
	0x44, 0x0f, 0xbc, 0xc2, //0x000045e8 bsfl         %edx, %r8d
	0x49, 0x01, 0xd8, //0x000045ec addq         %rbx, %r8
	0x49, 0xf7, 0xd0, //0x000045ef notq         %r8
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x000045f2 movq         $8(%rsp), %r14
	0xe9, 0x46, 0xfb, 0xff, 0xff, //0x000045f7 jmp          LBB0_864
	//0x000045fc LBB0_922
	0x49, 0x01, 0xce, //0x000045fc addq         %rcx, %r14
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x000045ff movq         $16(%rsp), %r15
	0x4c, 0x8b, 0x54, 0x24, 0x28, //0x00004604 movq         $40(%rsp), %r10
	//0x00004609 LBB0_923
	0x4d, 0x85, 0xed, //0x00004609 testq        %r13, %r13
	0x48, 0x8b, 0x54, 0x24, 0x30, //0x0000460c movq         $48(%rsp), %rdx
	0x48, 0x8b, 0x74, 0x24, 0x18, //0x00004611 movq         $24(%rsp), %rsi
	0x0f, 0x84, 0x44, 0x10, 0x00, 0x00, //0x00004616 je           LBB0_1105
	//0x0000461c LBB0_924
	0x48, 0x85, 0xf6, //0x0000461c testq        %rsi, %rsi
	0x0f, 0x84, 0x3b, 0x10, 0x00, 0x00, //0x0000461f je           LBB0_1105
	0x48, 0x85, 0xd2, //0x00004625 testq        %rdx, %rdx
	0x0f, 0x84, 0x32, 0x10, 0x00, 0x00, //0x00004628 je           LBB0_1105
	0x4d, 0x29, 0xe6, //0x0000462e subq         %r12, %r14
	0x49, 0x8d, 0x4e, 0xff, //0x00004631 leaq         $-1(%r14), %rcx
	0x49, 0x39, 0xcd, //0x00004635 cmpq         %rcx, %r13
	0x0f, 0x84, 0x6b, 0x00, 0x00, 0x00, //0x00004638 je           LBB0_932
	0x48, 0x39, 0xca, //0x0000463e cmpq         %rcx, %rdx
	0x0f, 0x84, 0x62, 0x00, 0x00, 0x00, //0x00004641 je           LBB0_932
	0x48, 0x39, 0xce, //0x00004647 cmpq         %rcx, %rsi
	0x0f, 0x84, 0x59, 0x00, 0x00, 0x00, //0x0000464a je           LBB0_932
	0x48, 0x85, 0xf6, //0x00004650 testq        %rsi, %rsi
	0xc5, 0x7e, 0x6f, 0x05, 0xc5, 0xba, 0xff, 0xff, //0x00004653 vmovdqu      $-17723(%rip), %ymm8  /* LCPI0_12+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0xdd, 0xba, 0xff, 0xff, //0x0000465b vmovdqu      $-17699(%rip), %ymm9  /* LCPI0_13+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0xf5, 0xba, 0xff, 0xff, //0x00004663 vmovdqu      $-17675(%rip), %ymm10  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x0d, 0xbb, 0xff, 0xff, //0x0000466b vmovdqu      $-17651(%rip), %ymm11  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0xc5, 0xb9, 0xff, 0xff, //0x00004673 vmovdqu      $-17979(%rip), %ymm12  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x1d, 0xbb, 0xff, 0xff, //0x0000467b vmovdqu      $-17635(%rip), %ymm13  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x35, 0xbb, 0xff, 0xff, //0x00004683 vmovdqu      $-17611(%rip), %ymm14  /* LCPI0_17+0(%rip) */
	0x0f, 0x8e, 0x71, 0x00, 0x00, 0x00, //0x0000468b jle          LBB0_936
	0x48, 0x8d, 0x46, 0xff, //0x00004691 leaq         $-1(%rsi), %rax
	0x49, 0x39, 0xc5, //0x00004695 cmpq         %rax, %r13
	0x0f, 0x84, 0x64, 0x00, 0x00, 0x00, //0x00004698 je           LBB0_936
	0x48, 0xf7, 0xd6, //0x0000469e notq         %rsi
	0x49, 0x89, 0xf6, //0x000046a1 movq         %rsi, %r14
	0xe9, 0x3b, 0x00, 0x00, 0x00, //0x000046a4 jmp          LBB0_934
	//0x000046a9 LBB0_932
	0x49, 0xf7, 0xde, //0x000046a9 negq         %r14
	//0x000046ac LBB0_933
	0xc5, 0x7e, 0x6f, 0x05, 0x6c, 0xba, 0xff, 0xff, //0x000046ac vmovdqu      $-17812(%rip), %ymm8  /* LCPI0_12+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0x84, 0xba, 0xff, 0xff, //0x000046b4 vmovdqu      $-17788(%rip), %ymm9  /* LCPI0_13+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0x9c, 0xba, 0xff, 0xff, //0x000046bc vmovdqu      $-17764(%rip), %ymm10  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0xb4, 0xba, 0xff, 0xff, //0x000046c4 vmovdqu      $-17740(%rip), %ymm11  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x6c, 0xb9, 0xff, 0xff, //0x000046cc vmovdqu      $-18068(%rip), %ymm12  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0xc4, 0xba, 0xff, 0xff, //0x000046d4 vmovdqu      $-17724(%rip), %ymm13  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0xdc, 0xba, 0xff, 0xff, //0x000046dc vmovdqu      $-17700(%rip), %ymm14  /* LCPI0_17+0(%rip) */
	//0x000046e4 LBB0_934
	0x48, 0x8b, 0x44, 0x24, 0x08, //0x000046e4 movq         $8(%rsp), %rax
	0x4c, 0x8b, 0x28, //0x000046e9 movq         (%rax), %r13
	0x4d, 0x85, 0xf6, //0x000046ec testq        %r14, %r14
	0x0f, 0x88, 0x7a, 0x0f, 0x00, 0x00, //0x000046ef js           LBB0_1107
	//0x000046f5 LBB0_935
	0x4d, 0x01, 0xf5, //0x000046f5 addq         %r14, %r13
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x000046f8 movq         $8(%rsp), %r14
	0xe9, 0xe8, 0xef, 0xff, 0xff, //0x000046fd jmp          LBB0_721
	//0x00004702 LBB0_936
	0x48, 0x89, 0xd1, //0x00004702 movq         %rdx, %rcx
	0x4c, 0x09, 0xe9, //0x00004705 orq          %r13, %rcx
	0x4c, 0x39, 0xea, //0x00004708 cmpq         %r13, %rdx
	0x0f, 0x8c, 0x73, 0x00, 0x00, 0x00, //0x0000470b jl           LBB0_939
	0x48, 0x85, 0xc9, //0x00004711 testq        %rcx, %rcx
	0x0f, 0x88, 0x6a, 0x00, 0x00, 0x00, //0x00004714 js           LBB0_939
	0x48, 0xf7, 0xd2, //0x0000471a notq         %rdx
	0x49, 0x89, 0xd6, //0x0000471d movq         %rdx, %r14
	0xe9, 0xbf, 0xff, 0xff, 0xff, //0x00004720 jmp          LBB0_934
	//0x00004725 LBB0_745
	0x4d, 0x89, 0xcf, //0x00004725 movq         %r9, %r15
	0x4c, 0x89, 0xd1, //0x00004728 movq         %r10, %rcx
	0x4c, 0x89, 0xeb, //0x0000472b movq         %r13, %rbx
	0x48, 0x83, 0xf9, 0x20, //0x0000472e cmpq         $32, %rcx
	0x4c, 0x8b, 0x54, 0x24, 0x28, //0x00004732 movq         $40(%rsp), %r10
	0x0f, 0x82, 0x1a, 0x04, 0x00, 0x00, //0x00004737 jb           LBB0_984
	//0x0000473d LBB0_746
	0xc5, 0xfe, 0x6f, 0x03, //0x0000473d vmovdqu      (%rbx), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00004741 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf9, //0x00004745 vpmovmskb    %ymm1, %edi
	0xc5, 0xfd, 0x74, 0xc7, //0x00004749 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x0000474d vpmovmskb    %ymm0, %esi
	0x85, 0xf6, //0x00004751 testl        %esi, %esi
	0x0f, 0x85, 0x96, 0x03, 0x00, 0x00, //0x00004753 jne          LBB0_980
	0x4d, 0x85, 0xf6, //0x00004759 testq        %r14, %r14
	0x0f, 0x85, 0xa7, 0x03, 0x00, 0x00, //0x0000475c jne          LBB0_982
	0x45, 0x31, 0xf6, //0x00004762 xorl         %r14d, %r14d
	0x48, 0x85, 0xff, //0x00004765 testq        %rdi, %rdi
	0x0f, 0x84, 0xe1, 0x03, 0x00, 0x00, //0x00004768 je           LBB0_983
	//0x0000476e LBB0_749
	0x48, 0x0f, 0xbc, 0xc7, //0x0000476e bsfq         %rdi, %rax
	0x4c, 0x29, 0xe3, //0x00004772 subq         %r12, %rbx
	0x4c, 0x8d, 0x6c, 0x03, 0x01, //0x00004775 leaq         $1(%rbx,%rax), %r13
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x0000477a movq         $8(%rsp), %r14
	0xe9, 0x5f, 0x04, 0x00, 0x00, //0x0000477f jmp          LBB0_993
	//0x00004784 LBB0_939
	0x48, 0x85, 0xc9, //0x00004784 testq        %rcx, %rcx
	0x49, 0x8d, 0x45, 0xff, //0x00004787 leaq         $-1(%r13), %rax
	0x49, 0xf7, 0xd5, //0x0000478b notq         %r13
	0x4d, 0x0f, 0x48, 0xee, //0x0000478e cmovsq       %r14, %r13
	0x48, 0x39, 0xc2, //0x00004792 cmpq         %rax, %rdx
	0x4d, 0x0f, 0x44, 0xf5, //0x00004795 cmoveq       %r13, %r14
	0xe9, 0x46, 0xff, 0xff, 0xff, //0x00004799 jmp          LBB0_934
	//0x0000479e LBB0_940
	0x4d, 0x29, 0xc8, //0x0000479e subq         %r9, %r8
	0x0f, 0xbc, 0xc2, //0x000047a1 bsfl         %edx, %eax
	0x4c, 0x01, 0xc0, //0x000047a4 addq         %r8, %rax
	0x48, 0xf7, 0xd0, //0x000047a7 notq         %rax
	0x49, 0x89, 0xc0, //0x000047aa movq         %rax, %r8
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x000047ad movq         $8(%rsp), %r14
	0xe9, 0x8b, 0xf9, 0xff, 0xff, //0x000047b2 jmp          LBB0_864
	//0x000047b7 LBB0_941
	0x48, 0xf7, 0xd2, //0x000047b7 notq         %rdx
	0x49, 0x89, 0xd0, //0x000047ba movq         %rdx, %r8
	0xe9, 0x80, 0xf9, 0xff, 0xff, //0x000047bd jmp          LBB0_864
	//0x000047c2 LBB0_942
	0x48, 0xf7, 0xd2, //0x000047c2 notq         %rdx
	0x49, 0x89, 0xd0, //0x000047c5 movq         %rdx, %r8
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x000047c8 movq         $8(%rsp), %r14
	0xe9, 0x70, 0xf9, 0xff, 0xff, //0x000047cd jmp          LBB0_864
	//0x000047d2 LBB0_943
	0x48, 0xf7, 0xd6, //0x000047d2 notq         %rsi
	0x49, 0x89, 0xf0, //0x000047d5 movq         %rsi, %r8
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x000047d8 movq         $8(%rsp), %r14
	0xe9, 0x60, 0xf9, 0xff, 0xff, //0x000047dd jmp          LBB0_864
	//0x000047e2 LBB0_944
	0x49, 0x89, 0xc8, //0x000047e2 movq         %rcx, %r8
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000047e5 movq         $-1, %rcx
	0x4d, 0x85, 0xff, //0x000047ec testq        %r15, %r15
	0x0f, 0x85, 0x9f, 0xf5, 0xff, 0xff, //0x000047ef jne          LBB0_810
	0xe9, 0xc6, 0x0c, 0x00, 0x00, //0x000047f5 jmp          LBB0_1086
	//0x000047fa LBB0_945
	0x48, 0xf7, 0xd3, //0x000047fa notq         %rbx
	0x49, 0x89, 0xd8, //0x000047fd movq         %rbx, %r8
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x00004800 movq         $8(%rsp), %r14
	0xe9, 0x38, 0xf9, 0xff, 0xff, //0x00004805 jmp          LBB0_864
	//0x0000480a LBB0_830
	0x4d, 0x89, 0xcf, //0x0000480a movq         %r9, %r15
	0x4c, 0x89, 0xd1, //0x0000480d movq         %r10, %rcx
	0x4c, 0x89, 0xeb, //0x00004810 movq         %r13, %rbx
	0x48, 0x83, 0xf9, 0x20, //0x00004813 cmpq         $32, %rcx
	0x4c, 0x8b, 0x54, 0x24, 0x28, //0x00004817 movq         $40(%rsp), %r10
	0x0f, 0x82, 0xaf, 0x04, 0x00, 0x00, //0x0000481c jb           LBB0_1002
	//0x00004822 LBB0_831
	0xc5, 0xfe, 0x6f, 0x03, //0x00004822 vmovdqu      (%rbx), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00004826 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf9, //0x0000482a vpmovmskb    %ymm1, %edi
	0xc5, 0xfd, 0x74, 0xc7, //0x0000482e vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00004832 vpmovmskb    %ymm0, %esi
	0x85, 0xf6, //0x00004836 testl        %esi, %esi
	0x0f, 0x85, 0x2b, 0x04, 0x00, 0x00, //0x00004838 jne          LBB0_998
	0x4d, 0x85, 0xf6, //0x0000483e testq        %r14, %r14
	0x0f, 0x85, 0x3c, 0x04, 0x00, 0x00, //0x00004841 jne          LBB0_1000
	0x45, 0x31, 0xf6, //0x00004847 xorl         %r14d, %r14d
	0x48, 0x85, 0xff, //0x0000484a testq        %rdi, %rdi
	0x0f, 0x84, 0x76, 0x04, 0x00, 0x00, //0x0000484d je           LBB0_1001
	//0x00004853 LBB0_834
	0x48, 0x0f, 0xbc, 0xc7, //0x00004853 bsfq         %rdi, %rax
	0x4c, 0x29, 0xe3, //0x00004857 subq         %r12, %rbx
	0x4c, 0x8d, 0x6c, 0x03, 0x01, //0x0000485a leaq         $1(%rbx,%rax), %r13
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x0000485f movq         $16(%rsp), %r15
	0x4d, 0x85, 0xed, //0x00004864 testq        %r13, %r13
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x00004867 movq         $8(%rsp), %r14
	0x0f, 0x89, 0xa6, 0xf7, 0xff, 0xff, //0x0000486c jns          LBB0_838
	0xe9, 0x27, 0x0c, 0x00, 0x00, //0x00004872 jmp          LBB0_1082
	//0x00004877 LBB0_946
	0x49, 0xf7, 0xd8, //0x00004877 negq         %r8
	0xe9, 0xc3, 0xf8, 0xff, 0xff, //0x0000487a jmp          LBB0_864
	//0x0000487f LBB0_947
	0x4d, 0x29, 0xe6, //0x0000487f subq         %r12, %r14
	0x0f, 0xbc, 0xc2, //0x00004882 bsfl         %edx, %eax
	0x4c, 0x01, 0xf0, //0x00004885 addq         %r14, %rax
	0x48, 0xf7, 0xd0, //0x00004888 notq         %rax
	0x49, 0x89, 0xc6, //0x0000488b movq         %rax, %r14
	0xe9, 0xf3, 0x00, 0x00, 0x00, //0x0000488e jmp          LBB0_960
	//0x00004893 LBB0_948
	0x48, 0xf7, 0xd6, //0x00004893 notq         %rsi
	0x49, 0x89, 0xf6, //0x00004896 movq         %rsi, %r14
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x00004899 movq         $16(%rsp), %r15
	0xe9, 0x41, 0xfe, 0xff, 0xff, //0x0000489e jmp          LBB0_934
	//0x000048a3 LBB0_949
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000048a3 movq         $-1, %r9
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x000048aa movq         $-1, %r15
	0x45, 0x31, 0xf6, //0x000048b1 xorl         %r14d, %r14d
	0x48, 0x83, 0xf9, 0x20, //0x000048b4 cmpq         $32, %rcx
	0x0f, 0x83, 0xe0, 0xfc, 0xff, 0xff, //0x000048b8 jae          LBB0_714
	0xe9, 0x92, 0x01, 0x00, 0x00, //0x000048be jmp          LBB0_968
	//0x000048c3 LBB0_950
	0x4d, 0x29, 0xe7, //0x000048c3 subq         %r12, %r15
	0x45, 0x0f, 0xbc, 0xf2, //0x000048c6 bsfl         %r10d, %r14d
	0xe9, 0x07, 0x00, 0x00, 0x00, //0x000048ca jmp          LBB0_952
	//0x000048cf LBB0_951
	0x4d, 0x29, 0xe7, //0x000048cf subq         %r12, %r15
	0x44, 0x0f, 0xbc, 0xf2, //0x000048d2 bsfl         %edx, %r14d
	//0x000048d6 LBB0_952
	0x4d, 0x01, 0xfe, //0x000048d6 addq         %r15, %r14
	0x49, 0xf7, 0xd6, //0x000048d9 notq         %r14
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x000048dc movq         $16(%rsp), %r15
	0x4d, 0x89, 0xc2, //0x000048e1 movq         %r8, %r10
	0x4c, 0x8b, 0x04, 0x24, //0x000048e4 movq         (%rsp), %r8
	0xe9, 0xf7, 0xfd, 0xff, 0xff, //0x000048e8 jmp          LBB0_934
	//0x000048ed LBB0_953
	0x4d, 0x29, 0xe7, //0x000048ed subq         %r12, %r15
	0x44, 0x0f, 0xbc, 0xf2, //0x000048f0 bsfl         %edx, %r14d
	0x4d, 0x01, 0xfe, //0x000048f4 addq         %r15, %r14
	0x49, 0xf7, 0xd6, //0x000048f7 notq         %r14
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x000048fa movq         $16(%rsp), %r15
	0x4c, 0x8b, 0x04, 0x24, //0x000048ff movq         (%rsp), %r8
	0xe9, 0xdc, 0xfd, 0xff, 0xff, //0x00004903 jmp          LBB0_934
	//0x00004908 LBB0_954
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00004908 movq         $-1, %r13
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x0000490f movq         $-1, %r15
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00004916 movq         $-1, %r12
	0x4d, 0x89, 0xc8, //0x0000491d movq         %r9, %r8
	0xe9, 0xe2, 0xf1, 0xff, 0xff, //0x00004920 jmp          LBB0_777
	//0x00004925 LBB0_955
	0x48, 0xf7, 0xd3, //0x00004925 notq         %rbx
	0x49, 0x89, 0xde, //0x00004928 movq         %rbx, %r14
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x0000492b movq         $16(%rsp), %r15
	0xe9, 0xaf, 0xfd, 0xff, 0xff, //0x00004930 jmp          LBB0_934
	//0x00004935 LBB0_956
	0x48, 0xf7, 0xd6, //0x00004935 notq         %rsi
	0x49, 0x89, 0xf6, //0x00004938 movq         %rsi, %r14
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x0000493b jmp          LBB0_960
	//0x00004940 LBB0_957
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00004940 movq         $-1, %r9
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00004947 movq         $-1, %r15
	0x45, 0x31, 0xf6, //0x0000494e xorl         %r14d, %r14d
	0x48, 0x83, 0xf9, 0x20, //0x00004951 cmpq         $32, %rcx
	0x4c, 0x8b, 0x54, 0x24, 0x28, //0x00004955 movq         $40(%rsp), %r10
	0x0f, 0x83, 0xdd, 0xfd, 0xff, 0xff, //0x0000495a jae          LBB0_746
	0xe9, 0xf2, 0x01, 0x00, 0x00, //0x00004960 jmp          LBB0_984
	//0x00004965 LBB0_958
	0x49, 0x89, 0xce, //0x00004965 movq         %rcx, %r14
	0x4d, 0x85, 0xed, //0x00004968 testq        %r13, %r13
	0x48, 0x8b, 0x54, 0x24, 0x30, //0x0000496b movq         $48(%rsp), %rdx
	0x48, 0x8b, 0x74, 0x24, 0x18, //0x00004970 movq         $24(%rsp), %rsi
	0x0f, 0x85, 0xa1, 0xfc, 0xff, 0xff, //0x00004975 jne          LBB0_924
	0xe9, 0xe0, 0x0c, 0x00, 0x00, //0x0000497b jmp          LBB0_1105
	//0x00004980 LBB0_959
	0x48, 0xf7, 0xd3, //0x00004980 notq         %rbx
	0x49, 0x89, 0xde, //0x00004983 movq         %rbx, %r14
	//0x00004986 LBB0_960
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x00004986 movq         $16(%rsp), %r15
	0x4c, 0x8b, 0x54, 0x24, 0x28, //0x0000498b movq         $40(%rsp), %r10
	0xe9, 0x17, 0xfd, 0xff, 0xff, //0x00004990 jmp          LBB0_933
	//0x00004995 LBB0_961
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00004995 movq         $-1, %r9
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x0000499c movq         $-1, %r15
	0x45, 0x31, 0xf6, //0x000049a3 xorl         %r14d, %r14d
	0x48, 0x83, 0xf9, 0x20, //0x000049a6 cmpq         $32, %rcx
	0x4c, 0x8b, 0x54, 0x24, 0x28, //0x000049aa movq         $40(%rsp), %r10
	0x0f, 0x83, 0x6d, 0xfe, 0xff, 0xff, //0x000049af jae          LBB0_831
	0xe9, 0x17, 0x03, 0x00, 0x00, //0x000049b5 jmp          LBB0_1002
	//0x000049ba LBB0_962
	0x48, 0xc7, 0x44, 0x24, 0x18, 0xff, 0xff, 0xff, 0xff, //0x000049ba movq         $-1, $24(%rsp)
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x000049c3 movq         $-1, %r13
	0x48, 0xc7, 0x44, 0x24, 0x30, 0xff, 0xff, 0xff, 0xff, //0x000049ca movq         $-1, $48(%rsp)
	0x4d, 0x89, 0xe6, //0x000049d3 movq         %r12, %r14
	0xe9, 0x50, 0xf9, 0xff, 0xff, //0x000049d6 jmp          LBB0_891
	//0x000049db LBB0_963
	0x4d, 0x89, 0xc5, //0x000049db movq         %r8, %r13
	0x4d, 0x89, 0xd0, //0x000049de movq         %r10, %r8
	0x49, 0x83, 0xff, 0xff, //0x000049e1 cmpq         $-1, %r15
	0x0f, 0x85, 0x1b, 0x00, 0x00, 0x00, //0x000049e5 jne          LBB0_966
	0x48, 0x89, 0xd8, //0x000049eb movq         %rbx, %rax
	0x4c, 0x29, 0xe0, //0x000049ee subq         %r12, %rax
	0x4c, 0x0f, 0xbc, 0xce, //0x000049f1 bsfq         %rsi, %r9
	0x49, 0x01, 0xc1, //0x000049f5 addq         %rax, %r9
	0x4d, 0x89, 0xcf, //0x000049f8 movq         %r9, %r15
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x000049fb jmp          LBB0_966
	//0x00004a00 LBB0_965
	0x4d, 0x89, 0xc5, //0x00004a00 movq         %r8, %r13
	0x4d, 0x89, 0xd0, //0x00004a03 movq         %r10, %r8
	//0x00004a06 LBB0_966
	0x44, 0x89, 0xf0, //0x00004a06 movl         %r14d, %eax
	0xf7, 0xd0, //0x00004a09 notl         %eax
	0x21, 0xf0, //0x00004a0b andl         %esi, %eax
	0x44, 0x8d, 0x14, 0x00, //0x00004a0d leal         (%rax,%rax), %r10d
	0x45, 0x09, 0xf2, //0x00004a11 orl          %r14d, %r10d
	0x44, 0x89, 0xd2, //0x00004a14 movl         %r10d, %edx
	0xf7, 0xd2, //0x00004a17 notl         %edx
	0x21, 0xf2, //0x00004a19 andl         %esi, %edx
	0x81, 0xe2, 0xaa, 0xaa, 0xaa, 0xaa, //0x00004a1b andl         $-1431655766, %edx
	0x45, 0x31, 0xf6, //0x00004a21 xorl         %r14d, %r14d
	0x01, 0xc2, //0x00004a24 addl         %eax, %edx
	0x41, 0x0f, 0x92, 0xc6, //0x00004a26 setb         %r14b
	0x01, 0xd2, //0x00004a2a addl         %edx, %edx
	0x81, 0xf2, 0x55, 0x55, 0x55, 0x55, //0x00004a2c xorl         $1431655765, %edx
	0x44, 0x21, 0xd2, //0x00004a32 andl         %r10d, %edx
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00004a35 movl         $4294967295, %eax
	0x31, 0xd0, //0x00004a3a xorl         %edx, %eax
	0x21, 0xc7, //0x00004a3c andl         %eax, %edi
	0x4d, 0x89, 0xc2, //0x00004a3e movq         %r8, %r10
	0x4d, 0x89, 0xe8, //0x00004a41 movq         %r13, %r8
	0x48, 0x85, 0xff, //0x00004a44 testq        %rdi, %rdi
	0x0f, 0x85, 0x82, 0xfb, 0xff, 0xff, //0x00004a47 jne          LBB0_717
	//0x00004a4d LBB0_967
	0x48, 0x83, 0xc3, 0x20, //0x00004a4d addq         $32, %rbx
	0x48, 0x83, 0xc1, 0xe0, //0x00004a51 addq         $-32, %rcx
	//0x00004a55 LBB0_968
	0x4d, 0x85, 0xf6, //0x00004a55 testq        %r14, %r14
	0x0f, 0x85, 0x98, 0x01, 0x00, 0x00, //0x00004a58 jne          LBB0_994
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x00004a5e movq         $8(%rsp), %r14
	0x48, 0x85, 0xc9, //0x00004a63 testq        %rcx, %rcx
	0x0f, 0x84, 0x64, 0x03, 0x00, 0x00, //0x00004a66 je           LBB0_979
	//0x00004a6c LBB0_970
	0x4c, 0x89, 0xe7, //0x00004a6c movq         %r12, %rdi
	0x48, 0xf7, 0xd7, //0x00004a6f notq         %rdi
	//0x00004a72 LBB0_971
	0x4c, 0x8d, 0x6b, 0x01, //0x00004a72 leaq         $1(%rbx), %r13
	0x0f, 0xb6, 0x13, //0x00004a76 movzbl       (%rbx), %edx
	0x80, 0xfa, 0x22, //0x00004a79 cmpb         $34, %dl
	0x0f, 0x84, 0x57, 0x00, 0x00, 0x00, //0x00004a7c je           LBB0_976
	0x48, 0x8d, 0x71, 0xff, //0x00004a82 leaq         $-1(%rcx), %rsi
	0x80, 0xfa, 0x5c, //0x00004a86 cmpb         $92, %dl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00004a89 je           LBB0_974
	0x48, 0x89, 0xf1, //0x00004a8f movq         %rsi, %rcx
	0x4c, 0x89, 0xeb, //0x00004a92 movq         %r13, %rbx
	0x48, 0x85, 0xf6, //0x00004a95 testq        %rsi, %rsi
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x00004a98 jne          LBB0_971
	0xe9, 0x2d, 0x03, 0x00, 0x00, //0x00004a9e jmp          LBB0_979
	//0x00004aa3 LBB0_974
	0x48, 0x85, 0xf6, //0x00004aa3 testq        %rsi, %rsi
	0x0f, 0x84, 0x90, 0x0c, 0x00, 0x00, //0x00004aa6 je           LBB0_1123
	0x49, 0x01, 0xfd, //0x00004aac addq         %rdi, %r13
	0x49, 0x83, 0xff, 0xff, //0x00004aaf cmpq         $-1, %r15
	0x4d, 0x0f, 0x44, 0xcd, //0x00004ab3 cmoveq       %r13, %r9
	0x4d, 0x0f, 0x44, 0xfd, //0x00004ab7 cmoveq       %r13, %r15
	0x48, 0x83, 0xc3, 0x02, //0x00004abb addq         $2, %rbx
	0x48, 0x83, 0xc1, 0xfe, //0x00004abf addq         $-2, %rcx
	0x48, 0x89, 0xce, //0x00004ac3 movq         %rcx, %rsi
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x00004ac6 movq         $8(%rsp), %r14
	0x48, 0x85, 0xf6, //0x00004acb testq        %rsi, %rsi
	0x0f, 0x85, 0x9e, 0xff, 0xff, 0xff, //0x00004ace jne          LBB0_971
	0xe9, 0xf7, 0x02, 0x00, 0x00, //0x00004ad4 jmp          LBB0_979
	//0x00004ad9 LBB0_976
	0x4d, 0x29, 0xe5, //0x00004ad9 subq         %r12, %r13
	//0x00004adc LBB0_977
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x00004adc movq         $16(%rsp), %r15
	0x4d, 0x85, 0xed, //0x00004ae1 testq        %r13, %r13
	0x0f, 0x89, 0x00, 0xec, 0xff, 0xff, //0x00004ae4 jns          LBB0_721
	0xe9, 0xd7, 0x02, 0x00, 0x00, //0x00004aea jmp          LBB0_978
	//0x00004aef LBB0_980
	0x49, 0x83, 0xff, 0xff, //0x00004aef cmpq         $-1, %r15
	0x0f, 0x85, 0x10, 0x00, 0x00, 0x00, //0x00004af3 jne          LBB0_982
	0x48, 0x89, 0xd8, //0x00004af9 movq         %rbx, %rax
	0x4c, 0x29, 0xe0, //0x00004afc subq         %r12, %rax
	0x4c, 0x0f, 0xbc, 0xce, //0x00004aff bsfq         %rsi, %r9
	0x49, 0x01, 0xc1, //0x00004b03 addq         %rax, %r9
	0x4d, 0x89, 0xcf, //0x00004b06 movq         %r9, %r15
	//0x00004b09 LBB0_982
	0x44, 0x89, 0xf0, //0x00004b09 movl         %r14d, %eax
	0xf7, 0xd0, //0x00004b0c notl         %eax
	0x21, 0xf0, //0x00004b0e andl         %esi, %eax
	0x44, 0x8d, 0x14, 0x00, //0x00004b10 leal         (%rax,%rax), %r10d
	0x45, 0x09, 0xf2, //0x00004b14 orl          %r14d, %r10d
	0x44, 0x89, 0xd2, //0x00004b17 movl         %r10d, %edx
	0xf7, 0xd2, //0x00004b1a notl         %edx
	0x21, 0xf2, //0x00004b1c andl         %esi, %edx
	0x81, 0xe2, 0xaa, 0xaa, 0xaa, 0xaa, //0x00004b1e andl         $-1431655766, %edx
	0x45, 0x31, 0xf6, //0x00004b24 xorl         %r14d, %r14d
	0x01, 0xc2, //0x00004b27 addl         %eax, %edx
	0x41, 0x0f, 0x92, 0xc6, //0x00004b29 setb         %r14b
	0x01, 0xd2, //0x00004b2d addl         %edx, %edx
	0x81, 0xf2, 0x55, 0x55, 0x55, 0x55, //0x00004b2f xorl         $1431655765, %edx
	0x44, 0x21, 0xd2, //0x00004b35 andl         %r10d, %edx
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00004b38 movl         $4294967295, %eax
	0x31, 0xd0, //0x00004b3d xorl         %edx, %eax
	0x21, 0xc7, //0x00004b3f andl         %eax, %edi
	0x4c, 0x8b, 0x54, 0x24, 0x28, //0x00004b41 movq         $40(%rsp), %r10
	0x48, 0x85, 0xff, //0x00004b46 testq        %rdi, %rdi
	0x0f, 0x85, 0x1f, 0xfc, 0xff, 0xff, //0x00004b49 jne          LBB0_749
	//0x00004b4f LBB0_983
	0x48, 0x83, 0xc3, 0x20, //0x00004b4f addq         $32, %rbx
	0x48, 0x83, 0xc1, 0xe0, //0x00004b53 addq         $-32, %rcx
	//0x00004b57 LBB0_984
	0x4d, 0x85, 0xf6, //0x00004b57 testq        %r14, %r14
	0x0f, 0x85, 0xcd, 0x00, 0x00, 0x00, //0x00004b5a jne          LBB0_996
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x00004b60 movq         $8(%rsp), %r14
	0x48, 0x85, 0xc9, //0x00004b65 testq        %rcx, %rcx
	0x0f, 0x84, 0x3a, 0x09, 0x00, 0x00, //0x00004b68 je           LBB0_1083
	//0x00004b6e LBB0_986
	0x4c, 0x89, 0xe7, //0x00004b6e movq         %r12, %rdi
	0x48, 0xf7, 0xd7, //0x00004b71 notq         %rdi
	//0x00004b74 LBB0_987
	0x4c, 0x8d, 0x6b, 0x01, //0x00004b74 leaq         $1(%rbx), %r13
	0x0f, 0xb6, 0x13, //0x00004b78 movzbl       (%rbx), %edx
	0x80, 0xfa, 0x22, //0x00004b7b cmpb         $34, %dl
	0x0f, 0x84, 0x5c, 0x00, 0x00, 0x00, //0x00004b7e je           LBB0_992
	0x48, 0x8d, 0x71, 0xff, //0x00004b84 leaq         $-1(%rcx), %rsi
	0x80, 0xfa, 0x5c, //0x00004b88 cmpb         $92, %dl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00004b8b je           LBB0_990
	0x48, 0x89, 0xf1, //0x00004b91 movq         %rsi, %rcx
	0x4c, 0x89, 0xeb, //0x00004b94 movq         %r13, %rbx
	0x48, 0x85, 0xf6, //0x00004b97 testq        %rsi, %rsi
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x00004b9a jne          LBB0_987
	0xe9, 0x03, 0x09, 0x00, 0x00, //0x00004ba0 jmp          LBB0_1083
	//0x00004ba5 LBB0_990
	0x48, 0x85, 0xf6, //0x00004ba5 testq        %rsi, %rsi
	0x0f, 0x84, 0xea, 0x01, 0x00, 0x00, //0x00004ba8 je           LBB0_1012
	0x49, 0x01, 0xfd, //0x00004bae addq         %rdi, %r13
	0x49, 0x83, 0xff, 0xff, //0x00004bb1 cmpq         $-1, %r15
	0x4d, 0x0f, 0x44, 0xcd, //0x00004bb5 cmoveq       %r13, %r9
	0x4d, 0x0f, 0x44, 0xfd, //0x00004bb9 cmoveq       %r13, %r15
	0x48, 0x83, 0xc3, 0x02, //0x00004bbd addq         $2, %rbx
	0x48, 0x83, 0xc1, 0xfe, //0x00004bc1 addq         $-2, %rcx
	0x48, 0x89, 0xce, //0x00004bc5 movq         %rcx, %rsi
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x00004bc8 movq         $8(%rsp), %r14
	0x4c, 0x8b, 0x54, 0x24, 0x28, //0x00004bcd movq         $40(%rsp), %r10
	0x48, 0x85, 0xf6, //0x00004bd2 testq        %rsi, %rsi
	0x0f, 0x85, 0x99, 0xff, 0xff, 0xff, //0x00004bd5 jne          LBB0_987
	0xe9, 0xc8, 0x08, 0x00, 0x00, //0x00004bdb jmp          LBB0_1083
	//0x00004be0 LBB0_992
	0x4d, 0x29, 0xe5, //0x00004be0 subq         %r12, %r13
	//0x00004be3 LBB0_993
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x00004be3 movq         $16(%rsp), %r15
	0x4d, 0x85, 0xed, //0x00004be8 testq        %r13, %r13
	0x0f, 0x89, 0x57, 0xed, 0xff, 0xff, //0x00004beb jns          LBB0_753
	0xe9, 0xa8, 0x08, 0x00, 0x00, //0x00004bf1 jmp          LBB0_1082
	//0x00004bf6 LBB0_994
	0x48, 0x85, 0xc9, //0x00004bf6 testq        %rcx, %rcx
	0x0f, 0x84, 0x3d, 0x0b, 0x00, 0x00, //0x00004bf9 je           LBB0_1123
	0x4c, 0x89, 0xe0, //0x00004bff movq         %r12, %rax
	0x48, 0xf7, 0xd0, //0x00004c02 notq         %rax
	0x48, 0x01, 0xd8, //0x00004c05 addq         %rbx, %rax
	0x49, 0x83, 0xff, 0xff, //0x00004c08 cmpq         $-1, %r15
	0x4c, 0x0f, 0x44, 0xc8, //0x00004c0c cmoveq       %rax, %r9
	0x4c, 0x0f, 0x44, 0xf8, //0x00004c10 cmoveq       %rax, %r15
	0x48, 0xff, 0xc3, //0x00004c14 incq         %rbx
	0x48, 0xff, 0xc9, //0x00004c17 decq         %rcx
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x00004c1a movq         $8(%rsp), %r14
	0x48, 0x85, 0xc9, //0x00004c1f testq        %rcx, %rcx
	0x0f, 0x85, 0x44, 0xfe, 0xff, 0xff, //0x00004c22 jne          LBB0_970
	0xe9, 0xa3, 0x01, 0x00, 0x00, //0x00004c28 jmp          LBB0_979
	//0x00004c2d LBB0_996
	0x48, 0x85, 0xc9, //0x00004c2d testq        %rcx, %rcx
	0x0f, 0x84, 0x62, 0x01, 0x00, 0x00, //0x00004c30 je           LBB0_1012
	0x4c, 0x89, 0xe0, //0x00004c36 movq         %r12, %rax
	0x48, 0xf7, 0xd0, //0x00004c39 notq         %rax
	0x48, 0x01, 0xd8, //0x00004c3c addq         %rbx, %rax
	0x49, 0x83, 0xff, 0xff, //0x00004c3f cmpq         $-1, %r15
	0x4c, 0x0f, 0x44, 0xc8, //0x00004c43 cmoveq       %rax, %r9
	0x4c, 0x0f, 0x44, 0xf8, //0x00004c47 cmoveq       %rax, %r15
	0x48, 0xff, 0xc3, //0x00004c4b incq         %rbx
	0x48, 0xff, 0xc9, //0x00004c4e decq         %rcx
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x00004c51 movq         $8(%rsp), %r14
	0x4c, 0x8b, 0x54, 0x24, 0x28, //0x00004c56 movq         $40(%rsp), %r10
	0x48, 0x85, 0xc9, //0x00004c5b testq        %rcx, %rcx
	0x0f, 0x85, 0x0a, 0xff, 0xff, 0xff, //0x00004c5e jne          LBB0_986
	0xe9, 0x3f, 0x08, 0x00, 0x00, //0x00004c64 jmp          LBB0_1083
	//0x00004c69 LBB0_998
	0x49, 0x83, 0xff, 0xff, //0x00004c69 cmpq         $-1, %r15
	0x0f, 0x85, 0x10, 0x00, 0x00, 0x00, //0x00004c6d jne          LBB0_1000
	0x48, 0x89, 0xd8, //0x00004c73 movq         %rbx, %rax
	0x4c, 0x29, 0xe0, //0x00004c76 subq         %r12, %rax
	0x4c, 0x0f, 0xbc, 0xce, //0x00004c79 bsfq         %rsi, %r9
	0x49, 0x01, 0xc1, //0x00004c7d addq         %rax, %r9
	0x4d, 0x89, 0xcf, //0x00004c80 movq         %r9, %r15
	//0x00004c83 LBB0_1000
	0x44, 0x89, 0xf0, //0x00004c83 movl         %r14d, %eax
	0xf7, 0xd0, //0x00004c86 notl         %eax
	0x21, 0xf0, //0x00004c88 andl         %esi, %eax
	0x44, 0x8d, 0x14, 0x00, //0x00004c8a leal         (%rax,%rax), %r10d
	0x45, 0x09, 0xf2, //0x00004c8e orl          %r14d, %r10d
	0x44, 0x89, 0xd2, //0x00004c91 movl         %r10d, %edx
	0xf7, 0xd2, //0x00004c94 notl         %edx
	0x21, 0xf2, //0x00004c96 andl         %esi, %edx
	0x81, 0xe2, 0xaa, 0xaa, 0xaa, 0xaa, //0x00004c98 andl         $-1431655766, %edx
	0x45, 0x31, 0xf6, //0x00004c9e xorl         %r14d, %r14d
	0x01, 0xc2, //0x00004ca1 addl         %eax, %edx
	0x41, 0x0f, 0x92, 0xc6, //0x00004ca3 setb         %r14b
	0x01, 0xd2, //0x00004ca7 addl         %edx, %edx
	0x81, 0xf2, 0x55, 0x55, 0x55, 0x55, //0x00004ca9 xorl         $1431655765, %edx
	0x44, 0x21, 0xd2, //0x00004caf andl         %r10d, %edx
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00004cb2 movl         $4294967295, %eax
	0x31, 0xc2, //0x00004cb7 xorl         %eax, %edx
	0x21, 0xd7, //0x00004cb9 andl         %edx, %edi
	0x4c, 0x8b, 0x54, 0x24, 0x28, //0x00004cbb movq         $40(%rsp), %r10
	0x48, 0x85, 0xff, //0x00004cc0 testq        %rdi, %rdi
	0x0f, 0x85, 0x8a, 0xfb, 0xff, 0xff, //0x00004cc3 jne          LBB0_834
	//0x00004cc9 LBB0_1001
	0x48, 0x83, 0xc3, 0x20, //0x00004cc9 addq         $32, %rbx
	0x48, 0x83, 0xc1, 0xe0, //0x00004ccd addq         $-32, %rcx
	//0x00004cd1 LBB0_1002
	0x4d, 0x85, 0xf6, //0x00004cd1 testq        %r14, %r14
	0x0f, 0x85, 0x91, 0x00, 0x00, 0x00, //0x00004cd4 jne          LBB0_1010
	0x48, 0x85, 0xc9, //0x00004cda testq        %rcx, %rcx
	0x0f, 0x84, 0xb5, 0x00, 0x00, 0x00, //0x00004cdd je           LBB0_1012
	//0x00004ce3 LBB0_1004
	0x4c, 0x89, 0xe7, //0x00004ce3 movq         %r12, %rdi
	0x48, 0xf7, 0xd7, //0x00004ce6 notq         %rdi
	//0x00004ce9 LBB0_1005
	0x4c, 0x8d, 0x6b, 0x01, //0x00004ce9 leaq         $1(%rbx), %r13
	0x0f, 0xb6, 0x13, //0x00004ced movzbl       (%rbx), %edx
	0x80, 0xfa, 0x22, //0x00004cf0 cmpb         $34, %dl
	0x0f, 0x84, 0x52, 0x00, 0x00, 0x00, //0x00004cf3 je           LBB0_1081
	0x48, 0x8d, 0x71, 0xff, //0x00004cf9 leaq         $-1(%rcx), %rsi
	0x80, 0xfa, 0x5c, //0x00004cfd cmpb         $92, %dl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00004d00 je           LBB0_1008
	0x48, 0x89, 0xf1, //0x00004d06 movq         %rsi, %rcx
	0x4c, 0x89, 0xeb, //0x00004d09 movq         %r13, %rbx
	0x48, 0x85, 0xf6, //0x00004d0c testq        %rsi, %rsi
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x00004d0f jne          LBB0_1005
	0xe9, 0x7e, 0x00, 0x00, 0x00, //0x00004d15 jmp          LBB0_1012
	//0x00004d1a LBB0_1008
	0x48, 0x85, 0xf6, //0x00004d1a testq        %rsi, %rsi
	0x0f, 0x84, 0x75, 0x00, 0x00, 0x00, //0x00004d1d je           LBB0_1012
	0x49, 0x01, 0xfd, //0x00004d23 addq         %rdi, %r13
	0x49, 0x83, 0xff, 0xff, //0x00004d26 cmpq         $-1, %r15
	0x4d, 0x0f, 0x44, 0xcd, //0x00004d2a cmoveq       %r13, %r9
	0x4d, 0x0f, 0x44, 0xfd, //0x00004d2e cmoveq       %r13, %r15
	0x48, 0x83, 0xc3, 0x02, //0x00004d32 addq         $2, %rbx
	0x48, 0x83, 0xc1, 0xfe, //0x00004d36 addq         $-2, %rcx
	0x48, 0x89, 0xce, //0x00004d3a movq         %rcx, %rsi
	0x48, 0x85, 0xf6, //0x00004d3d testq        %rsi, %rsi
	0x0f, 0x85, 0xa3, 0xff, 0xff, 0xff, //0x00004d40 jne          LBB0_1005
	0xe9, 0x4d, 0x00, 0x00, 0x00, //0x00004d46 jmp          LBB0_1012
	//0x00004d4b LBB0_1081
	0x4d, 0x29, 0xe5, //0x00004d4b subq         %r12, %r13
	0x4c, 0x8b, 0x7c, 0x24, 0x10, //0x00004d4e movq         $16(%rsp), %r15
	0x4c, 0x8b, 0x54, 0x24, 0x28, //0x00004d53 movq         $40(%rsp), %r10
	0x4d, 0x85, 0xed, //0x00004d58 testq        %r13, %r13
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x00004d5b movq         $8(%rsp), %r14
	0x0f, 0x89, 0xb2, 0xf2, 0xff, 0xff, //0x00004d60 jns          LBB0_838
	0xe9, 0x33, 0x07, 0x00, 0x00, //0x00004d66 jmp          LBB0_1082
	//0x00004d6b LBB0_1010
	0x48, 0x85, 0xc9, //0x00004d6b testq        %rcx, %rcx
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x00004d6e je           LBB0_1012
	0x4c, 0x89, 0xe0, //0x00004d74 movq         %r12, %rax
	0x48, 0xf7, 0xd0, //0x00004d77 notq         %rax
	0x48, 0x01, 0xd8, //0x00004d7a addq         %rbx, %rax
	0x49, 0x83, 0xff, 0xff, //0x00004d7d cmpq         $-1, %r15
	0x4c, 0x0f, 0x44, 0xc8, //0x00004d81 cmoveq       %rax, %r9
	0x4c, 0x0f, 0x44, 0xf8, //0x00004d85 cmoveq       %rax, %r15
	0x48, 0xff, 0xc3, //0x00004d89 incq         %rbx
	0x48, 0xff, 0xc9, //0x00004d8c decq         %rcx
	0x48, 0x85, 0xc9, //0x00004d8f testq        %rcx, %rcx
	0x0f, 0x85, 0x4b, 0xff, 0xff, 0xff, //0x00004d92 jne          LBB0_1004
	//0x00004d98 LBB0_1012
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x00004d98 movq         $8(%rsp), %r14
	0xe9, 0x06, 0x07, 0x00, 0x00, //0x00004d9d jmp          LBB0_1083
	//0x00004da2 LBB0_1013
	0x4d, 0x89, 0x1e, //0x00004da2 movq         %r11, (%r14)
	//0x00004da5 LBB0_1014
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00004da5 movq         $-1, %rcx
	0xe9, 0x4d, 0xe5, 0xff, 0xff, //0x00004dac jmp          LBB0_659
	//0x00004db1 LBB0_1097
	0x48, 0xc7, 0xc1, 0xf9, 0xff, 0xff, 0xff, //0x00004db1 movq         $-7, %rcx
	0xe9, 0x41, 0xe5, 0xff, 0xff, //0x00004db8 jmp          LBB0_659
	//0x00004dbd LBB0_1015
	0x48, 0x8d, 0x50, 0x04, //0x00004dbd leaq         $4(%rax), %rdx
	0xe9, 0xbd, 0x03, 0x00, 0x00, //0x00004dc1 jmp          LBB0_1053
	//0x00004dc6 LBB0_978
	0x49, 0x83, 0xfd, 0xff, //0x00004dc6 cmpq         $-1, %r13
	0x0f, 0x85, 0xe2, 0x06, 0x00, 0x00, //0x00004dca jne          LBB0_1084
	//0x00004dd0 LBB0_979
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00004dd0 movq         $-1, %r13
	0x4c, 0x8b, 0x4c, 0x24, 0x18, //0x00004dd7 movq         $24(%rsp), %r9
	0xe9, 0xd1, 0x06, 0x00, 0x00, //0x00004ddc jmp          LBB0_1084
	//0x00004de1 LBB0_1016
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00004de1 movq         $-1, %rcx
	0xe9, 0xd3, 0x06, 0x00, 0x00, //0x00004de8 jmp          LBB0_1086
	//0x00004ded LBB0_1017
	0x4d, 0x8b, 0x4f, 0x08, //0x00004ded movq         $8(%r15), %r9
	0x4d, 0x89, 0xcf, //0x00004df1 movq         %r9, %r15
	0x49, 0x29, 0xdf, //0x00004df4 subq         %rbx, %r15
	0x49, 0x83, 0xff, 0x20, //0x00004df7 cmpq         $32, %r15
	0x0f, 0x8c, 0xff, 0x08, 0x00, 0x00, //0x00004dfb jl           LBB0_1117
	0x41, 0xb8, 0xff, 0xff, 0xff, 0xff, //0x00004e01 movl         $4294967295, %r8d
	0x4d, 0x8d, 0x14, 0x03, //0x00004e07 leaq         (%r11,%rax), %r10
	0x49, 0x29, 0xc1, //0x00004e0b subq         %rax, %r9
	0xba, 0x1f, 0x00, 0x00, 0x00, //0x00004e0e movl         $31, %edx
	0x45, 0x31, 0xff, //0x00004e13 xorl         %r15d, %r15d
	0xc5, 0xfe, 0x6f, 0x05, 0x62, 0xb2, 0xff, 0xff, //0x00004e16 vmovdqu      $-19870(%rip), %ymm0  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0x7a, 0xb2, 0xff, 0xff, //0x00004e1e vmovdqu      $-19846(%rip), %ymm1  /* LCPI0_8+0(%rip) */
	0x45, 0x31, 0xe4, //0x00004e26 xorl         %r12d, %r12d
	//0x00004e29 LBB0_1019
	0xc4, 0x81, 0x7e, 0x6f, 0x54, 0x3a, 0x01, //0x00004e29 vmovdqu      $1(%r10,%r15), %ymm2
	0xc5, 0xed, 0x74, 0xd8, //0x00004e30 vpcmpeqb     %ymm0, %ymm2, %ymm3
	0xc5, 0x7d, 0xd7, 0xf3, //0x00004e34 vpmovmskb    %ymm3, %r14d
	0xc5, 0xed, 0x74, 0xd1, //0x00004e38 vpcmpeqb     %ymm1, %ymm2, %ymm2
	0xc5, 0xfd, 0xd7, 0xda, //0x00004e3c vpmovmskb    %ymm2, %ebx
	0x4d, 0x85, 0xe4, //0x00004e40 testq        %r12, %r12
	0x0f, 0x85, 0x10, 0x00, 0x00, 0x00, //0x00004e43 jne          LBB0_1022
	0x85, 0xdb, //0x00004e49 testl        %ebx, %ebx
	0x0f, 0x85, 0x08, 0x00, 0x00, 0x00, //0x00004e4b jne          LBB0_1022
	0x45, 0x31, 0xe4, //0x00004e51 xorl         %r12d, %r12d
	0xe9, 0x38, 0x00, 0x00, 0x00, //0x00004e54 jmp          LBB0_1023
	//0x00004e59 LBB0_1022
	0x44, 0x89, 0xe6, //0x00004e59 movl         %r12d, %esi
	0x44, 0x31, 0xc6, //0x00004e5c xorl         %r8d, %esi
	0x21, 0xde, //0x00004e5f andl         %ebx, %esi
	0x8d, 0x1c, 0x36, //0x00004e61 leal         (%rsi,%rsi), %ebx
	0x44, 0x09, 0xe3, //0x00004e64 orl          %r12d, %ebx
	0x41, 0x8d, 0xb8, 0xab, 0xaa, 0xaa, 0xaa, //0x00004e67 leal         $-1431655765(%r8), %edi
	0x31, 0xdf, //0x00004e6e xorl         %ebx, %edi
	0x21, 0xf7, //0x00004e70 andl         %esi, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00004e72 andl         $-1431655766, %edi
	0x45, 0x31, 0xe4, //0x00004e78 xorl         %r12d, %r12d
	0x01, 0xf7, //0x00004e7b addl         %esi, %edi
	0x41, 0x0f, 0x92, 0xc4, //0x00004e7d setb         %r12b
	0x01, 0xff, //0x00004e81 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00004e83 xorl         $1431655765, %edi
	0x21, 0xdf, //0x00004e89 andl         %ebx, %edi
	0x44, 0x31, 0xc7, //0x00004e8b xorl         %r8d, %edi
	0x41, 0x21, 0xfe, //0x00004e8e andl         %edi, %r14d
	//0x00004e91 LBB0_1023
	0x45, 0x85, 0xf6, //0x00004e91 testl        %r14d, %r14d
	0x0f, 0x85, 0xe3, 0x05, 0x00, 0x00, //0x00004e94 jne          LBB0_1080
	0x49, 0x83, 0xc7, 0x20, //0x00004e9a addq         $32, %r15
	0x49, 0x8d, 0x74, 0x11, 0xe0, //0x00004e9e leaq         $-32(%r9,%rdx), %rsi
	0x48, 0x83, 0xc2, 0xe0, //0x00004ea3 addq         $-32, %rdx
	0x48, 0x83, 0xfe, 0x3f, //0x00004ea7 cmpq         $63, %rsi
	0x0f, 0x8f, 0x78, 0xff, 0xff, 0xff, //0x00004eab jg           LBB0_1019
	0x4d, 0x85, 0xe4, //0x00004eb1 testq        %r12, %r12
	0x0f, 0x85, 0x8c, 0x08, 0x00, 0x00, //0x00004eb4 jne          LBB0_1121
	0x4b, 0x8d, 0x5c, 0x17, 0x01, //0x00004eba leaq         $1(%r15,%r10), %rbx
	0x49, 0xf7, 0xd7, //0x00004ebf notq         %r15
	0x4d, 0x01, 0xcf, //0x00004ec2 addq         %r9, %r15
	//0x00004ec5 LBB0_1027
	0x4d, 0x85, 0xff, //0x00004ec5 testq        %r15, %r15
	0x48, 0x8b, 0x74, 0x24, 0x08, //0x00004ec8 movq         $8(%rsp), %rsi
	0x0f, 0x8f, 0x26, 0x06, 0x00, 0x00, //0x00004ecd jg           LBB0_1090
	0xe9, 0x26, 0xe4, 0xff, 0xff, //0x00004ed3 jmp          LBB0_659
	//0x00004ed8 LBB0_1028
	0x49, 0xb8, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00004ed8 movabsq      $-6148914691236517206, %r8
	0x4d, 0x8b, 0x77, 0x08, //0x00004ee2 movq         $8(%r15), %r14
	0x49, 0x29, 0xde, //0x00004ee6 subq         %rbx, %r14
	0x49, 0x01, 0xdb, //0x00004ee9 addq         %rbx, %r11
	0x45, 0x31, 0xd2, //0x00004eec xorl         %r10d, %r10d
	0xc5, 0xfe, 0x6f, 0x05, 0x89, 0xb1, 0xff, 0xff, //0x00004eef vmovdqu      $-20087(%rip), %ymm0  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0xa1, 0xb1, 0xff, 0xff, //0x00004ef7 vmovdqu      $-20063(%rip), %ymm1  /* LCPI0_8+0(%rip) */
	0xc5, 0xe9, 0x76, 0xd2, //0x00004eff vpcmpeqd     %xmm2, %xmm2, %xmm2
	0xc5, 0xfe, 0x6f, 0x1d, 0xd5, 0xb1, 0xff, 0xff, //0x00004f03 vmovdqu      $-20011(%rip), %ymm3  /* LCPI0_10+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0xed, 0xb1, 0xff, 0xff, //0x00004f0b vmovdqu      $-19987(%rip), %ymm4  /* LCPI0_11+0(%rip) */
	0xc4, 0x41, 0x31, 0xef, 0xc9, //0x00004f13 vpxor        %xmm9, %xmm9, %xmm9
	0x45, 0x31, 0xe4, //0x00004f18 xorl         %r12d, %r12d
	0x45, 0x31, 0xff, //0x00004f1b xorl         %r15d, %r15d
	0x31, 0xdb, //0x00004f1e xorl         %ebx, %ebx
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00004f20 jmp          LBB0_1030
	//0x00004f25 LBB0_1029
	0x49, 0xc1, 0xfd, 0x3f, //0x00004f25 sarq         $63, %r13
	0xf3, 0x49, 0x0f, 0xb8, 0xd1, //0x00004f29 popcntq      %r9, %rdx
	0x49, 0x01, 0xd7, //0x00004f2e addq         %rdx, %r15
	0x49, 0x83, 0xc3, 0x40, //0x00004f31 addq         $64, %r11
	0x49, 0x83, 0xc6, 0xc0, //0x00004f35 addq         $-64, %r14
	0x4d, 0x89, 0xea, //0x00004f39 movq         %r13, %r10
	//0x00004f3c LBB0_1030
	0x49, 0x83, 0xfe, 0x40, //0x00004f3c cmpq         $64, %r14
	0x0f, 0x8c, 0x16, 0x01, 0x00, 0x00, //0x00004f40 jl           LBB0_1037
	//0x00004f46 LBB0_1031
	0xc4, 0xc1, 0x7e, 0x6f, 0x3b, //0x00004f46 vmovdqu      (%r11), %ymm7
	0xc4, 0xc1, 0x7e, 0x6f, 0x73, 0x20, //0x00004f4b vmovdqu      $32(%r11), %ymm6
	0xc5, 0x45, 0x74, 0xc0, //0x00004f51 vpcmpeqb     %ymm0, %ymm7, %ymm8
	0xc4, 0x41, 0x7d, 0xd7, 0xe8, //0x00004f55 vpmovmskb    %ymm8, %r13d
	0xc5, 0x4d, 0x74, 0xc0, //0x00004f5a vpcmpeqb     %ymm0, %ymm6, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xf8, //0x00004f5e vpmovmskb    %ymm8, %edi
	0x48, 0xc1, 0xe7, 0x20, //0x00004f63 shlq         $32, %rdi
	0x49, 0x09, 0xfd, //0x00004f67 orq          %rdi, %r13
	0xc5, 0x45, 0x74, 0xc1, //0x00004f6a vpcmpeqb     %ymm1, %ymm7, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xf8, //0x00004f6e vpmovmskb    %ymm8, %edi
	0xc5, 0x4d, 0x74, 0xc1, //0x00004f73 vpcmpeqb     %ymm1, %ymm6, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xd0, //0x00004f77 vpmovmskb    %ymm8, %edx
	0x48, 0xc1, 0xe2, 0x20, //0x00004f7c shlq         $32, %rdx
	0x48, 0x09, 0xd7, //0x00004f80 orq          %rdx, %rdi
	0x48, 0x89, 0xfa, //0x00004f83 movq         %rdi, %rdx
	0x4c, 0x09, 0xe2, //0x00004f86 orq          %r12, %rdx
	0x0f, 0x84, 0x41, 0x00, 0x00, 0x00, //0x00004f89 je           LBB0_1033
	0x4c, 0x89, 0xe2, //0x00004f8f movq         %r12, %rdx
	0x48, 0xf7, 0xd2, //0x00004f92 notq         %rdx
	0x48, 0x21, 0xfa, //0x00004f95 andq         %rdi, %rdx
	0x48, 0x8d, 0x34, 0x12, //0x00004f98 leaq         (%rdx,%rdx), %rsi
	0x4c, 0x09, 0xe6, //0x00004f9c orq          %r12, %rsi
	0x49, 0x89, 0xf4, //0x00004f9f movq         %rsi, %r12
	0x4d, 0x31, 0xc4, //0x00004fa2 xorq         %r8, %r12
	0x4c, 0x21, 0xc7, //0x00004fa5 andq         %r8, %rdi
	0x4c, 0x21, 0xe7, //0x00004fa8 andq         %r12, %rdi
	0x45, 0x31, 0xe4, //0x00004fab xorl         %r12d, %r12d
	0x48, 0x01, 0xd7, //0x00004fae addq         %rdx, %rdi
	0x41, 0x0f, 0x92, 0xc4, //0x00004fb1 setb         %r12b
	0x48, 0x01, 0xff, //0x00004fb5 addq         %rdi, %rdi
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00004fb8 movabsq      $6148914691236517205, %rdx
	0x48, 0x31, 0xd7, //0x00004fc2 xorq         %rdx, %rdi
	0x48, 0x21, 0xf7, //0x00004fc5 andq         %rsi, %rdi
	0x48, 0xf7, 0xd7, //0x00004fc8 notq         %rdi
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00004fcb jmp          LBB0_1034
	//0x00004fd0 LBB0_1033
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00004fd0 movq         $-1, %rdi
	0x45, 0x31, 0xe4, //0x00004fd7 xorl         %r12d, %r12d
	//0x00004fda LBB0_1034
	0x4c, 0x21, 0xef, //0x00004fda andq         %r13, %rdi
	0xc4, 0xe1, 0xf9, 0x6e, 0xef, //0x00004fdd vmovq        %rdi, %xmm5
	0xc4, 0xe3, 0x51, 0x44, 0xea, 0x00, //0x00004fe2 vpclmulqdq   $0, %xmm2, %xmm5, %xmm5
	0xc4, 0xc1, 0xf9, 0x7e, 0xed, //0x00004fe8 vmovq        %xmm5, %r13
	0x4d, 0x31, 0xd5, //0x00004fed xorq         %r10, %r13
	0xc5, 0xc5, 0x74, 0xeb, //0x00004ff0 vpcmpeqb     %ymm3, %ymm7, %ymm5
	0xc5, 0x7d, 0xd7, 0xcd, //0x00004ff4 vpmovmskb    %ymm5, %r9d
	0xc5, 0xcd, 0x74, 0xeb, //0x00004ff8 vpcmpeqb     %ymm3, %ymm6, %ymm5
	0xc5, 0xfd, 0xd7, 0xd5, //0x00004ffc vpmovmskb    %ymm5, %edx
	0x48, 0xc1, 0xe2, 0x20, //0x00005000 shlq         $32, %rdx
	0x49, 0x09, 0xd1, //0x00005004 orq          %rdx, %r9
	0x4d, 0x89, 0xea, //0x00005007 movq         %r13, %r10
	0x49, 0xf7, 0xd2, //0x0000500a notq         %r10
	0x4d, 0x21, 0xd1, //0x0000500d andq         %r10, %r9
	0xc5, 0xc5, 0x74, 0xec, //0x00005010 vpcmpeqb     %ymm4, %ymm7, %ymm5
	0xc5, 0xfd, 0xd7, 0xd5, //0x00005014 vpmovmskb    %ymm5, %edx
	0xc5, 0xcd, 0x74, 0xec, //0x00005018 vpcmpeqb     %ymm4, %ymm6, %ymm5
	0xc5, 0xfd, 0xd7, 0xf5, //0x0000501c vpmovmskb    %ymm5, %esi
	0x48, 0xc1, 0xe6, 0x20, //0x00005020 shlq         $32, %rsi
	0x48, 0x09, 0xf2, //0x00005024 orq          %rsi, %rdx
	0x4c, 0x21, 0xd2, //0x00005027 andq         %r10, %rdx
	0x0f, 0x84, 0xf5, 0xfe, 0xff, 0xff, //0x0000502a je           LBB0_1029
	//0x00005030 LBB0_1035
	0x48, 0x8d, 0x7a, 0xff, //0x00005030 leaq         $-1(%rdx), %rdi
	0x48, 0x89, 0xfe, //0x00005034 movq         %rdi, %rsi
	0x4c, 0x21, 0xce, //0x00005037 andq         %r9, %rsi
	0xf3, 0x48, 0x0f, 0xb8, 0xf6, //0x0000503a popcntq      %rsi, %rsi
	0x4c, 0x01, 0xfe, //0x0000503f addq         %r15, %rsi
	0x48, 0x39, 0xde, //0x00005042 cmpq         %rbx, %rsi
	0x0f, 0x86, 0xf4, 0x03, 0x00, 0x00, //0x00005045 jbe          LBB0_1079
	0x48, 0xff, 0xc3, //0x0000504b incq         %rbx
	0x48, 0x21, 0xfa, //0x0000504e andq         %rdi, %rdx
	0x0f, 0x85, 0xd9, 0xff, 0xff, 0xff, //0x00005051 jne          LBB0_1035
	0xe9, 0xc9, 0xfe, 0xff, 0xff, //0x00005057 jmp          LBB0_1029
	//0x0000505c LBB0_1037
	0x4d, 0x85, 0xf6, //0x0000505c testq        %r14, %r14
	0x0f, 0x8e, 0xa3, 0x06, 0x00, 0x00, //0x0000505f jle          LBB0_1118
	0xc5, 0x7e, 0x7f, 0x4c, 0x24, 0x60, //0x00005065 vmovdqu      %ymm9, $96(%rsp)
	0xc5, 0x7e, 0x7f, 0x4c, 0x24, 0x40, //0x0000506b vmovdqu      %ymm9, $64(%rsp)
	0x44, 0x89, 0xda, //0x00005071 movl         %r11d, %edx
	0x81, 0xe2, 0xff, 0x0f, 0x00, 0x00, //0x00005074 andl         $4095, %edx
	0x81, 0xfa, 0xc1, 0x0f, 0x00, 0x00, //0x0000507a cmpl         $4033, %edx
	0x0f, 0x82, 0xc0, 0xfe, 0xff, 0xff, //0x00005080 jb           LBB0_1031
	0x49, 0x83, 0xfe, 0x20, //0x00005086 cmpq         $32, %r14
	0x0f, 0x82, 0x1d, 0x00, 0x00, 0x00, //0x0000508a jb           LBB0_1041
	0xc4, 0xc1, 0x7e, 0x6f, 0x2b, //0x00005090 vmovdqu      (%r11), %ymm5
	0xc5, 0xfe, 0x7f, 0x6c, 0x24, 0x40, //0x00005095 vmovdqu      %ymm5, $64(%rsp)
	0x49, 0x83, 0xc3, 0x20, //0x0000509b addq         $32, %r11
	0x49, 0x8d, 0x7e, 0xe0, //0x0000509f leaq         $-32(%r14), %rdi
	0x4c, 0x8d, 0x4c, 0x24, 0x60, //0x000050a3 leaq         $96(%rsp), %r9
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x000050a8 jmp          LBB0_1042
	//0x000050ad LBB0_1041
	0x4c, 0x8d, 0x4c, 0x24, 0x40, //0x000050ad leaq         $64(%rsp), %r9
	0x4c, 0x89, 0xf7, //0x000050b2 movq         %r14, %rdi
	//0x000050b5 LBB0_1042
	0x48, 0x83, 0xff, 0x10, //0x000050b5 cmpq         $16, %rdi
	0x0f, 0x82, 0x5c, 0x00, 0x00, 0x00, //0x000050b9 jb           LBB0_1043
	0xc4, 0xc1, 0x7a, 0x6f, 0x2b, //0x000050bf vmovdqu      (%r11), %xmm5
	0xc4, 0xc1, 0x7a, 0x7f, 0x29, //0x000050c4 vmovdqu      %xmm5, (%r9)
	0x49, 0x83, 0xc3, 0x10, //0x000050c9 addq         $16, %r11
	0x49, 0x83, 0xc1, 0x10, //0x000050cd addq         $16, %r9
	0x48, 0x83, 0xc7, 0xf0, //0x000050d1 addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x000050d5 cmpq         $8, %rdi
	0x0f, 0x83, 0x46, 0x00, 0x00, 0x00, //0x000050d9 jae          LBB0_1048
	//0x000050df LBB0_1044
	0x48, 0x83, 0xff, 0x04, //0x000050df cmpq         $4, %rdi
	0x0f, 0x8c, 0x58, 0x00, 0x00, 0x00, //0x000050e3 jl           LBB0_1045
	//0x000050e9 LBB0_1049
	0x41, 0x8b, 0x13, //0x000050e9 movl         (%r11), %edx
	0x41, 0x89, 0x11, //0x000050ec movl         %edx, (%r9)
	0x49, 0x83, 0xc3, 0x04, //0x000050ef addq         $4, %r11
	0x49, 0x83, 0xc1, 0x04, //0x000050f3 addq         $4, %r9
	0x48, 0x83, 0xc7, 0xfc, //0x000050f7 addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x000050fb cmpq         $2, %rdi
	0x0f, 0x83, 0x46, 0x00, 0x00, 0x00, //0x000050ff jae          LBB0_1050
	//0x00005105 LBB0_1046
	0x4c, 0x89, 0xde, //0x00005105 movq         %r11, %rsi
	0x4c, 0x8d, 0x5c, 0x24, 0x40, //0x00005108 leaq         $64(%rsp), %r11
	0x48, 0x85, 0xff, //0x0000510d testq        %rdi, %rdi
	0x0f, 0x85, 0x5a, 0x00, 0x00, 0x00, //0x00005110 jne          LBB0_1051
	0xe9, 0x2b, 0xfe, 0xff, 0xff, //0x00005116 jmp          LBB0_1031
	//0x0000511b LBB0_1043
	0x48, 0x83, 0xff, 0x08, //0x0000511b cmpq         $8, %rdi
	0x0f, 0x82, 0xba, 0xff, 0xff, 0xff, //0x0000511f jb           LBB0_1044
	//0x00005125 LBB0_1048
	0x49, 0x8b, 0x13, //0x00005125 movq         (%r11), %rdx
	0x49, 0x89, 0x11, //0x00005128 movq         %rdx, (%r9)
	0x49, 0x83, 0xc3, 0x08, //0x0000512b addq         $8, %r11
	0x49, 0x83, 0xc1, 0x08, //0x0000512f addq         $8, %r9
	0x48, 0x83, 0xc7, 0xf8, //0x00005133 addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x00005137 cmpq         $4, %rdi
	0x0f, 0x8d, 0xa8, 0xff, 0xff, 0xff, //0x0000513b jge          LBB0_1049
	//0x00005141 LBB0_1045
	0x48, 0x83, 0xff, 0x02, //0x00005141 cmpq         $2, %rdi
	0x0f, 0x82, 0xba, 0xff, 0xff, 0xff, //0x00005145 jb           LBB0_1046
	//0x0000514b LBB0_1050
	0x41, 0x0f, 0xb7, 0x13, //0x0000514b movzwl       (%r11), %edx
	0x66, 0x41, 0x89, 0x11, //0x0000514f movw         %dx, (%r9)
	0x49, 0x83, 0xc3, 0x02, //0x00005153 addq         $2, %r11
	0x49, 0x83, 0xc1, 0x02, //0x00005157 addq         $2, %r9
	0x48, 0x83, 0xc7, 0xfe, //0x0000515b addq         $-2, %rdi
	0x4c, 0x89, 0xde, //0x0000515f movq         %r11, %rsi
	0x4c, 0x8d, 0x5c, 0x24, 0x40, //0x00005162 leaq         $64(%rsp), %r11
	0x48, 0x85, 0xff, //0x00005167 testq        %rdi, %rdi
	0x0f, 0x84, 0xd6, 0xfd, 0xff, 0xff, //0x0000516a je           LBB0_1031
	//0x00005170 LBB0_1051
	0x8a, 0x16, //0x00005170 movb         (%rsi), %dl
	0x41, 0x88, 0x11, //0x00005172 movb         %dl, (%r9)
	0x4c, 0x8d, 0x5c, 0x24, 0x40, //0x00005175 leaq         $64(%rsp), %r11
	0xe9, 0xc7, 0xfd, 0xff, 0xff, //0x0000517a jmp          LBB0_1031
	//0x0000517f LBB0_1052
	0x48, 0x8d, 0x50, 0x05, //0x0000517f leaq         $5(%rax), %rdx
	//0x00005183 LBB0_1053
	0x49, 0x3b, 0x57, 0x08, //0x00005183 cmpq         $8(%r15), %rdx
	0x0f, 0x87, 0x71, 0xe1, 0xff, 0xff, //0x00005187 ja           LBB0_659
	0x49, 0x89, 0x16, //0x0000518d movq         %rdx, (%r14)
	0x48, 0x89, 0xc1, //0x00005190 movq         %rax, %rcx
	0xe9, 0x66, 0xe1, 0xff, 0xff, //0x00005193 jmp          LBB0_659
	//0x00005198 LBB0_1055
	0x49, 0xb8, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00005198 movabsq      $-6148914691236517206, %r8
	0x4d, 0x8b, 0x77, 0x08, //0x000051a2 movq         $8(%r15), %r14
	0x49, 0x29, 0xde, //0x000051a6 subq         %rbx, %r14
	0x49, 0x01, 0xdb, //0x000051a9 addq         %rbx, %r11
	0x45, 0x31, 0xd2, //0x000051ac xorl         %r10d, %r10d
	0xc5, 0xfe, 0x6f, 0x05, 0xc9, 0xae, 0xff, 0xff, //0x000051af vmovdqu      $-20791(%rip), %ymm0  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0xe1, 0xae, 0xff, 0xff, //0x000051b7 vmovdqu      $-20767(%rip), %ymm1  /* LCPI0_8+0(%rip) */
	0xc5, 0xe9, 0x76, 0xd2, //0x000051bf vpcmpeqd     %xmm2, %xmm2, %xmm2
	0xc5, 0xfe, 0x6f, 0x1d, 0xf5, 0xae, 0xff, 0xff, //0x000051c3 vmovdqu      $-20747(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x8d, 0xae, 0xff, 0xff, //0x000051cb vmovdqu      $-20851(%rip), %ymm4  /* LCPI0_3+0(%rip) */
	0xc4, 0x41, 0x31, 0xef, 0xc9, //0x000051d3 vpxor        %xmm9, %xmm9, %xmm9
	0x45, 0x31, 0xe4, //0x000051d8 xorl         %r12d, %r12d
	0x45, 0x31, 0xff, //0x000051db xorl         %r15d, %r15d
	0x31, 0xdb, //0x000051de xorl         %ebx, %ebx
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x000051e0 jmp          LBB0_1057
	//0x000051e5 LBB0_1056
	0x49, 0xc1, 0xfd, 0x3f, //0x000051e5 sarq         $63, %r13
	0xf3, 0x49, 0x0f, 0xb8, 0xd1, //0x000051e9 popcntq      %r9, %rdx
	0x49, 0x01, 0xd7, //0x000051ee addq         %rdx, %r15
	0x49, 0x83, 0xc3, 0x40, //0x000051f1 addq         $64, %r11
	0x49, 0x83, 0xc6, 0xc0, //0x000051f5 addq         $-64, %r14
	0x4d, 0x89, 0xea, //0x000051f9 movq         %r13, %r10
	//0x000051fc LBB0_1057
	0x49, 0x83, 0xfe, 0x40, //0x000051fc cmpq         $64, %r14
	0x0f, 0x8c, 0x16, 0x01, 0x00, 0x00, //0x00005200 jl           LBB0_1064
	//0x00005206 LBB0_1058
	0xc4, 0xc1, 0x7e, 0x6f, 0x3b, //0x00005206 vmovdqu      (%r11), %ymm7
	0xc4, 0xc1, 0x7e, 0x6f, 0x73, 0x20, //0x0000520b vmovdqu      $32(%r11), %ymm6
	0xc5, 0x45, 0x74, 0xc0, //0x00005211 vpcmpeqb     %ymm0, %ymm7, %ymm8
	0xc4, 0x41, 0x7d, 0xd7, 0xe8, //0x00005215 vpmovmskb    %ymm8, %r13d
	0xc5, 0x4d, 0x74, 0xc0, //0x0000521a vpcmpeqb     %ymm0, %ymm6, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xf8, //0x0000521e vpmovmskb    %ymm8, %edi
	0x48, 0xc1, 0xe7, 0x20, //0x00005223 shlq         $32, %rdi
	0x49, 0x09, 0xfd, //0x00005227 orq          %rdi, %r13
	0xc5, 0x45, 0x74, 0xc1, //0x0000522a vpcmpeqb     %ymm1, %ymm7, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xf8, //0x0000522e vpmovmskb    %ymm8, %edi
	0xc5, 0x4d, 0x74, 0xc1, //0x00005233 vpcmpeqb     %ymm1, %ymm6, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xd0, //0x00005237 vpmovmskb    %ymm8, %edx
	0x48, 0xc1, 0xe2, 0x20, //0x0000523c shlq         $32, %rdx
	0x48, 0x09, 0xd7, //0x00005240 orq          %rdx, %rdi
	0x48, 0x89, 0xfa, //0x00005243 movq         %rdi, %rdx
	0x4c, 0x09, 0xe2, //0x00005246 orq          %r12, %rdx
	0x0f, 0x84, 0x41, 0x00, 0x00, 0x00, //0x00005249 je           LBB0_1060
	0x4c, 0x89, 0xe2, //0x0000524f movq         %r12, %rdx
	0x48, 0xf7, 0xd2, //0x00005252 notq         %rdx
	0x48, 0x21, 0xfa, //0x00005255 andq         %rdi, %rdx
	0x48, 0x8d, 0x34, 0x12, //0x00005258 leaq         (%rdx,%rdx), %rsi
	0x4c, 0x09, 0xe6, //0x0000525c orq          %r12, %rsi
	0x49, 0x89, 0xf4, //0x0000525f movq         %rsi, %r12
	0x4d, 0x31, 0xc4, //0x00005262 xorq         %r8, %r12
	0x4c, 0x21, 0xc7, //0x00005265 andq         %r8, %rdi
	0x4c, 0x21, 0xe7, //0x00005268 andq         %r12, %rdi
	0x45, 0x31, 0xe4, //0x0000526b xorl         %r12d, %r12d
	0x48, 0x01, 0xd7, //0x0000526e addq         %rdx, %rdi
	0x41, 0x0f, 0x92, 0xc4, //0x00005271 setb         %r12b
	0x48, 0x01, 0xff, //0x00005275 addq         %rdi, %rdi
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00005278 movabsq      $6148914691236517205, %rdx
	0x48, 0x31, 0xd7, //0x00005282 xorq         %rdx, %rdi
	0x48, 0x21, 0xf7, //0x00005285 andq         %rsi, %rdi
	0x48, 0xf7, 0xd7, //0x00005288 notq         %rdi
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x0000528b jmp          LBB0_1061
	//0x00005290 LBB0_1060
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00005290 movq         $-1, %rdi
	0x45, 0x31, 0xe4, //0x00005297 xorl         %r12d, %r12d
	//0x0000529a LBB0_1061
	0x4c, 0x21, 0xef, //0x0000529a andq         %r13, %rdi
	0xc4, 0xe1, 0xf9, 0x6e, 0xef, //0x0000529d vmovq        %rdi, %xmm5
	0xc4, 0xe3, 0x51, 0x44, 0xea, 0x00, //0x000052a2 vpclmulqdq   $0, %xmm2, %xmm5, %xmm5
	0xc4, 0xc1, 0xf9, 0x7e, 0xed, //0x000052a8 vmovq        %xmm5, %r13
	0x4d, 0x31, 0xd5, //0x000052ad xorq         %r10, %r13
	0xc5, 0xc5, 0x74, 0xeb, //0x000052b0 vpcmpeqb     %ymm3, %ymm7, %ymm5
	0xc5, 0x7d, 0xd7, 0xcd, //0x000052b4 vpmovmskb    %ymm5, %r9d
	0xc5, 0xcd, 0x74, 0xeb, //0x000052b8 vpcmpeqb     %ymm3, %ymm6, %ymm5
	0xc5, 0xfd, 0xd7, 0xd5, //0x000052bc vpmovmskb    %ymm5, %edx
	0x48, 0xc1, 0xe2, 0x20, //0x000052c0 shlq         $32, %rdx
	0x49, 0x09, 0xd1, //0x000052c4 orq          %rdx, %r9
	0x4d, 0x89, 0xea, //0x000052c7 movq         %r13, %r10
	0x49, 0xf7, 0xd2, //0x000052ca notq         %r10
	0x4d, 0x21, 0xd1, //0x000052cd andq         %r10, %r9
	0xc5, 0xc5, 0x74, 0xec, //0x000052d0 vpcmpeqb     %ymm4, %ymm7, %ymm5
	0xc5, 0xfd, 0xd7, 0xd5, //0x000052d4 vpmovmskb    %ymm5, %edx
	0xc5, 0xcd, 0x74, 0xec, //0x000052d8 vpcmpeqb     %ymm4, %ymm6, %ymm5
	0xc5, 0xfd, 0xd7, 0xf5, //0x000052dc vpmovmskb    %ymm5, %esi
	0x48, 0xc1, 0xe6, 0x20, //0x000052e0 shlq         $32, %rsi
	0x48, 0x09, 0xf2, //0x000052e4 orq          %rsi, %rdx
	0x4c, 0x21, 0xd2, //0x000052e7 andq         %r10, %rdx
	0x0f, 0x84, 0xf5, 0xfe, 0xff, 0xff, //0x000052ea je           LBB0_1056
	//0x000052f0 LBB0_1062
	0x48, 0x8d, 0x7a, 0xff, //0x000052f0 leaq         $-1(%rdx), %rdi
	0x48, 0x89, 0xfe, //0x000052f4 movq         %rdi, %rsi
	0x4c, 0x21, 0xce, //0x000052f7 andq         %r9, %rsi
	0xf3, 0x48, 0x0f, 0xb8, 0xf6, //0x000052fa popcntq      %rsi, %rsi
	0x4c, 0x01, 0xfe, //0x000052ff addq         %r15, %rsi
	0x48, 0x39, 0xde, //0x00005302 cmpq         %rbx, %rsi
	0x0f, 0x86, 0x34, 0x01, 0x00, 0x00, //0x00005305 jbe          LBB0_1079
	0x48, 0xff, 0xc3, //0x0000530b incq         %rbx
	0x48, 0x21, 0xfa, //0x0000530e andq         %rdi, %rdx
	0x0f, 0x85, 0xd9, 0xff, 0xff, 0xff, //0x00005311 jne          LBB0_1062
	0xe9, 0xc9, 0xfe, 0xff, 0xff, //0x00005317 jmp          LBB0_1056
	//0x0000531c LBB0_1064
	0x4d, 0x85, 0xf6, //0x0000531c testq        %r14, %r14
	0x0f, 0x8e, 0xe3, 0x03, 0x00, 0x00, //0x0000531f jle          LBB0_1118
	0xc5, 0x7e, 0x7f, 0x4c, 0x24, 0x60, //0x00005325 vmovdqu      %ymm9, $96(%rsp)
	0xc5, 0x7e, 0x7f, 0x4c, 0x24, 0x40, //0x0000532b vmovdqu      %ymm9, $64(%rsp)
	0x44, 0x89, 0xda, //0x00005331 movl         %r11d, %edx
	0x81, 0xe2, 0xff, 0x0f, 0x00, 0x00, //0x00005334 andl         $4095, %edx
	0x81, 0xfa, 0xc1, 0x0f, 0x00, 0x00, //0x0000533a cmpl         $4033, %edx
	0x0f, 0x82, 0xc0, 0xfe, 0xff, 0xff, //0x00005340 jb           LBB0_1058
	0x49, 0x83, 0xfe, 0x20, //0x00005346 cmpq         $32, %r14
	0x0f, 0x82, 0x1d, 0x00, 0x00, 0x00, //0x0000534a jb           LBB0_1068
	0xc4, 0xc1, 0x7e, 0x6f, 0x2b, //0x00005350 vmovdqu      (%r11), %ymm5
	0xc5, 0xfe, 0x7f, 0x6c, 0x24, 0x40, //0x00005355 vmovdqu      %ymm5, $64(%rsp)
	0x49, 0x83, 0xc3, 0x20, //0x0000535b addq         $32, %r11
	0x49, 0x8d, 0x7e, 0xe0, //0x0000535f leaq         $-32(%r14), %rdi
	0x4c, 0x8d, 0x4c, 0x24, 0x60, //0x00005363 leaq         $96(%rsp), %r9
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x00005368 jmp          LBB0_1069
	//0x0000536d LBB0_1068
	0x4c, 0x8d, 0x4c, 0x24, 0x40, //0x0000536d leaq         $64(%rsp), %r9
	0x4c, 0x89, 0xf7, //0x00005372 movq         %r14, %rdi
	//0x00005375 LBB0_1069
	0x48, 0x83, 0xff, 0x10, //0x00005375 cmpq         $16, %rdi
	0x0f, 0x82, 0x5c, 0x00, 0x00, 0x00, //0x00005379 jb           LBB0_1070
	0xc4, 0xc1, 0x7a, 0x6f, 0x2b, //0x0000537f vmovdqu      (%r11), %xmm5
	0xc4, 0xc1, 0x7a, 0x7f, 0x29, //0x00005384 vmovdqu      %xmm5, (%r9)
	0x49, 0x83, 0xc3, 0x10, //0x00005389 addq         $16, %r11
	0x49, 0x83, 0xc1, 0x10, //0x0000538d addq         $16, %r9
	0x48, 0x83, 0xc7, 0xf0, //0x00005391 addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x00005395 cmpq         $8, %rdi
	0x0f, 0x83, 0x46, 0x00, 0x00, 0x00, //0x00005399 jae          LBB0_1075
	//0x0000539f LBB0_1071
	0x48, 0x83, 0xff, 0x04, //0x0000539f cmpq         $4, %rdi
	0x0f, 0x8c, 0x58, 0x00, 0x00, 0x00, //0x000053a3 jl           LBB0_1072
	//0x000053a9 LBB0_1076
	0x41, 0x8b, 0x13, //0x000053a9 movl         (%r11), %edx
	0x41, 0x89, 0x11, //0x000053ac movl         %edx, (%r9)
	0x49, 0x83, 0xc3, 0x04, //0x000053af addq         $4, %r11
	0x49, 0x83, 0xc1, 0x04, //0x000053b3 addq         $4, %r9
	0x48, 0x83, 0xc7, 0xfc, //0x000053b7 addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x000053bb cmpq         $2, %rdi
	0x0f, 0x83, 0x46, 0x00, 0x00, 0x00, //0x000053bf jae          LBB0_1077
	//0x000053c5 LBB0_1073
	0x4c, 0x89, 0xde, //0x000053c5 movq         %r11, %rsi
	0x4c, 0x8d, 0x5c, 0x24, 0x40, //0x000053c8 leaq         $64(%rsp), %r11
	0x48, 0x85, 0xff, //0x000053cd testq        %rdi, %rdi
	0x0f, 0x85, 0x5a, 0x00, 0x00, 0x00, //0x000053d0 jne          LBB0_1078
	0xe9, 0x2b, 0xfe, 0xff, 0xff, //0x000053d6 jmp          LBB0_1058
	//0x000053db LBB0_1070
	0x48, 0x83, 0xff, 0x08, //0x000053db cmpq         $8, %rdi
	0x0f, 0x82, 0xba, 0xff, 0xff, 0xff, //0x000053df jb           LBB0_1071
	//0x000053e5 LBB0_1075
	0x49, 0x8b, 0x13, //0x000053e5 movq         (%r11), %rdx
	0x49, 0x89, 0x11, //0x000053e8 movq         %rdx, (%r9)
	0x49, 0x83, 0xc3, 0x08, //0x000053eb addq         $8, %r11
	0x49, 0x83, 0xc1, 0x08, //0x000053ef addq         $8, %r9
	0x48, 0x83, 0xc7, 0xf8, //0x000053f3 addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x000053f7 cmpq         $4, %rdi
	0x0f, 0x8d, 0xa8, 0xff, 0xff, 0xff, //0x000053fb jge          LBB0_1076
	//0x00005401 LBB0_1072
	0x48, 0x83, 0xff, 0x02, //0x00005401 cmpq         $2, %rdi
	0x0f, 0x82, 0xba, 0xff, 0xff, 0xff, //0x00005405 jb           LBB0_1073
	//0x0000540b LBB0_1077
	0x41, 0x0f, 0xb7, 0x13, //0x0000540b movzwl       (%r11), %edx
	0x66, 0x41, 0x89, 0x11, //0x0000540f movw         %dx, (%r9)
	0x49, 0x83, 0xc3, 0x02, //0x00005413 addq         $2, %r11
	0x49, 0x83, 0xc1, 0x02, //0x00005417 addq         $2, %r9
	0x48, 0x83, 0xc7, 0xfe, //0x0000541b addq         $-2, %rdi
	0x4c, 0x89, 0xde, //0x0000541f movq         %r11, %rsi
	0x4c, 0x8d, 0x5c, 0x24, 0x40, //0x00005422 leaq         $64(%rsp), %r11
	0x48, 0x85, 0xff, //0x00005427 testq        %rdi, %rdi
	0x0f, 0x84, 0xd6, 0xfd, 0xff, 0xff, //0x0000542a je           LBB0_1058
	//0x00005430 LBB0_1078
	0x8a, 0x16, //0x00005430 movb         (%rsi), %dl
	0x41, 0x88, 0x11, //0x00005432 movb         %dl, (%r9)
	0x4c, 0x8d, 0x5c, 0x24, 0x40, //0x00005435 leaq         $64(%rsp), %r11
	0xe9, 0xc7, 0xfd, 0xff, 0xff, //0x0000543a jmp          LBB0_1058
	//0x0000543f LBB0_1079
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x0000543f movq         $16(%rsp), %rdi
	0x48, 0x8b, 0x4f, 0x08, //0x00005444 movq         $8(%rdi), %rcx
	0x48, 0x0f, 0xbc, 0xd2, //0x00005448 bsfq         %rdx, %rdx
	0x4c, 0x29, 0xf2, //0x0000544c subq         %r14, %rdx
	0x48, 0x8d, 0x4c, 0x0a, 0x01, //0x0000544f leaq         $1(%rdx,%rcx), %rcx
	0x48, 0x8b, 0x74, 0x24, 0x08, //0x00005454 movq         $8(%rsp), %rsi
	0x48, 0x89, 0x0e, //0x00005459 movq         %rcx, (%rsi)
	0x48, 0x8b, 0x57, 0x08, //0x0000545c movq         $8(%rdi), %rdx
	0x48, 0x39, 0xd1, //0x00005460 cmpq         %rdx, %rcx
	0x48, 0x0f, 0x47, 0xca, //0x00005463 cmovaq       %rdx, %rcx
	0x48, 0x89, 0x0e, //0x00005467 movq         %rcx, (%rsi)
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000546a movq         $-1, %rcx
	0x48, 0x0f, 0x47, 0xc1, //0x00005471 cmovaq       %rcx, %rax
	0x48, 0x89, 0xc1, //0x00005475 movq         %rax, %rcx
	0xe9, 0x81, 0xde, 0xff, 0xff, //0x00005478 jmp          LBB0_659
	//0x0000547d LBB0_1080
	0x49, 0x0f, 0xbc, 0xce, //0x0000547d bsfq         %r14, %rcx
	0x49, 0x01, 0xca, //0x00005481 addq         %rcx, %r10
	0x4d, 0x01, 0xfa, //0x00005484 addq         %r15, %r10
	0x4d, 0x29, 0xda, //0x00005487 subq         %r11, %r10
	0x49, 0x83, 0xc2, 0x02, //0x0000548a addq         $2, %r10
	0x48, 0x8b, 0x4c, 0x24, 0x08, //0x0000548e movq         $8(%rsp), %rcx
	0x4c, 0x89, 0x11, //0x00005493 movq         %r10, (%rcx)
	0x48, 0x89, 0xc1, //0x00005496 movq         %rax, %rcx
	0xe9, 0x60, 0xde, 0xff, 0xff, //0x00005499 jmp          LBB0_659
	//0x0000549e LBB0_1082
	0x49, 0x83, 0xfd, 0xff, //0x0000549e cmpq         $-1, %r13
	0x0f, 0x85, 0x0a, 0x00, 0x00, 0x00, //0x000054a2 jne          LBB0_1084
	//0x000054a8 LBB0_1083
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x000054a8 movq         $-1, %r13
	0x4d, 0x89, 0xc1, //0x000054af movq         %r8, %r9
	//0x000054b2 LBB0_1084
	0x4d, 0x89, 0x0e, //0x000054b2 movq         %r9, (%r14)
	0x4c, 0x89, 0xe9, //0x000054b5 movq         %r13, %rcx
	0xe9, 0x41, 0xde, 0xff, 0xff, //0x000054b8 jmp          LBB0_659
	//0x000054bd LBB0_1085
	0x4c, 0x89, 0xc1, //0x000054bd movq         %r8, %rcx
	//0x000054c0 LBB0_1086
	0x49, 0x8b, 0x06, //0x000054c0 movq         (%r14), %rax
	0x48, 0x29, 0xc8, //0x000054c3 subq         %rcx, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x000054c6 addq         $-2, %rax
	//0x000054ca LBB0_1087
	0x49, 0x89, 0x06, //0x000054ca movq         %rax, (%r14)
	0xe9, 0x25, 0xde, 0xff, 0xff, //0x000054cd jmp          LBB0_658
	//0x000054d2 LBB0_1096
	0x49, 0x89, 0x16, //0x000054d2 movq         %rdx, (%r14)
	0xe9, 0x24, 0xde, 0xff, 0xff, //0x000054d5 jmp          LBB0_659
	//0x000054da LBB0_1088
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x000054da movq         $-2, %rdx
	0xb9, 0x02, 0x00, 0x00, 0x00, //0x000054e1 movl         $2, %ecx
	0x48, 0x01, 0xcb, //0x000054e6 addq         %rcx, %rbx
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000054e9 movq         $-1, %rcx
	0x49, 0x01, 0xd7, //0x000054f0 addq         %rdx, %r15
	0x0f, 0x8e, 0x05, 0xde, 0xff, 0xff, //0x000054f3 jle          LBB0_659
	//0x000054f9 LBB0_1090
	0x0f, 0xb6, 0x0b, //0x000054f9 movzbl       (%rbx), %ecx
	0x80, 0xf9, 0x5c, //0x000054fc cmpb         $92, %cl
	0x0f, 0x84, 0xd5, 0xff, 0xff, 0xff, //0x000054ff je           LBB0_1088
	0x80, 0xf9, 0x22, //0x00005505 cmpb         $34, %cl
	0x0f, 0x84, 0xb4, 0x01, 0x00, 0x00, //0x00005508 je           LBB0_1111
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x0000550e movq         $-1, %rdx
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x00005515 movl         $1, %ecx
	0x48, 0x01, 0xcb, //0x0000551a addq         %rcx, %rbx
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000551d movq         $-1, %rcx
	0x49, 0x01, 0xd7, //0x00005524 addq         %rdx, %r15
	0x0f, 0x8f, 0xcc, 0xff, 0xff, 0xff, //0x00005527 jg           LBB0_1090
	0xe9, 0xcc, 0xdd, 0xff, 0xff, //0x0000552d jmp          LBB0_659
	//0x00005532 LBB0_1093
	0x4c, 0x89, 0x8c, 0x24, 0x98, 0x00, 0x00, 0x00, //0x00005532 movq         %r9, $152(%rsp)
	//0x0000553a LBB0_1094
	0x48, 0x8b, 0x44, 0x24, 0x08, //0x0000553a movq         $8(%rsp), %rax
	0x48, 0x8b, 0x8c, 0x24, 0x98, 0x00, 0x00, 0x00, //0x0000553f movq         $152(%rsp), %rcx
	0x48, 0x89, 0x08, //0x00005547 movq         %rcx, (%rax)
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000554a movq         $-1, %rcx
	0xe9, 0xa8, 0xdd, 0xff, 0xff, //0x00005551 jmp          LBB0_659
	//0x00005556 LBB0_1095
	0x0f, 0xb7, 0xcf, //0x00005556 movzwl       %di, %ecx
	0xe9, 0x78, 0xdd, 0xff, 0xff, //0x00005559 jmp          LBB0_655
	//0x0000555e LBB0_1098
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x0000555e movq         $-2, %rcx
	0x80, 0xfa, 0x61, //0x00005565 cmpb         $97, %dl
	0x0f, 0x85, 0x90, 0xdd, 0xff, 0xff, //0x00005568 jne          LBB0_659
	0x49, 0x8d, 0x43, 0x02, //0x0000556e leaq         $2(%r11), %rax
	0x49, 0x89, 0x06, //0x00005572 movq         %rax, (%r14)
	0x43, 0x80, 0x7c, 0x1c, 0x02, 0x6c, //0x00005575 cmpb         $108, $2(%r12,%r11)
	0x0f, 0x85, 0x7d, 0xdd, 0xff, 0xff, //0x0000557b jne          LBB0_659
	0x49, 0x8d, 0x43, 0x03, //0x00005581 leaq         $3(%r11), %rax
	0x49, 0x89, 0x06, //0x00005585 movq         %rax, (%r14)
	0x43, 0x80, 0x7c, 0x1c, 0x03, 0x73, //0x00005588 cmpb         $115, $3(%r12,%r11)
	0x0f, 0x85, 0x6a, 0xdd, 0xff, 0xff, //0x0000558e jne          LBB0_659
	0x49, 0x8d, 0x43, 0x04, //0x00005594 leaq         $4(%r11), %rax
	0x49, 0x89, 0x06, //0x00005598 movq         %rax, (%r14)
	0x43, 0x80, 0x7c, 0x1c, 0x04, 0x65, //0x0000559b cmpb         $101, $4(%r12,%r11)
	0x0f, 0x85, 0x57, 0xdd, 0xff, 0xff, //0x000055a1 jne          LBB0_659
	0x49, 0x83, 0xc3, 0x05, //0x000055a7 addq         $5, %r11
	0xe9, 0xa3, 0x00, 0x00, 0x00, //0x000055ab jmp          LBB0_1103
	//0x000055b0 LBB0_850
	0x4d, 0x89, 0x1e, //0x000055b0 movq         %r11, (%r14)
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x000055b3 movq         $-2, %rcx
	0x41, 0x80, 0x39, 0x6e, //0x000055ba cmpb         $110, (%r9)
	0x0f, 0x85, 0x3a, 0xdd, 0xff, 0xff, //0x000055be jne          LBB0_659
	0x49, 0x8d, 0x43, 0x01, //0x000055c4 leaq         $1(%r11), %rax
	0x49, 0x89, 0x06, //0x000055c8 movq         %rax, (%r14)
	0x43, 0x80, 0x7c, 0x1c, 0x01, 0x75, //0x000055cb cmpb         $117, $1(%r12,%r11)
	0x0f, 0x85, 0x27, 0xdd, 0xff, 0xff, //0x000055d1 jne          LBB0_659
	0x49, 0x8d, 0x43, 0x02, //0x000055d7 leaq         $2(%r11), %rax
	0x49, 0x89, 0x06, //0x000055db movq         %rax, (%r14)
	0x43, 0x80, 0x7c, 0x1c, 0x02, 0x6c, //0x000055de cmpb         $108, $2(%r12,%r11)
	0x0f, 0x85, 0x14, 0xdd, 0xff, 0xff, //0x000055e4 jne          LBB0_659
	0x49, 0x8d, 0x43, 0x03, //0x000055ea leaq         $3(%r11), %rax
	0x49, 0x89, 0x06, //0x000055ee movq         %rax, (%r14)
	0x43, 0x80, 0x7c, 0x1c, 0x03, 0x6c, //0x000055f1 cmpb         $108, $3(%r12,%r11)
	0x0f, 0x85, 0x01, 0xdd, 0xff, 0xff, //0x000055f7 jne          LBB0_659
	0xe9, 0x4d, 0x00, 0x00, 0x00, //0x000055fd jmp          LBB0_854
	//0x00005602 LBB0_857
	0x4d, 0x89, 0x1e, //0x00005602 movq         %r11, (%r14)
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00005605 movq         $-2, %rcx
	0x41, 0x80, 0x39, 0x74, //0x0000560c cmpb         $116, (%r9)
	0x0f, 0x85, 0xe8, 0xdc, 0xff, 0xff, //0x00005610 jne          LBB0_659
	0x49, 0x8d, 0x43, 0x01, //0x00005616 leaq         $1(%r11), %rax
	0x49, 0x89, 0x06, //0x0000561a movq         %rax, (%r14)
	0x43, 0x80, 0x7c, 0x1c, 0x01, 0x72, //0x0000561d cmpb         $114, $1(%r12,%r11)
	0x0f, 0x85, 0xd5, 0xdc, 0xff, 0xff, //0x00005623 jne          LBB0_659
	0x49, 0x8d, 0x43, 0x02, //0x00005629 leaq         $2(%r11), %rax
	0x49, 0x89, 0x06, //0x0000562d movq         %rax, (%r14)
	0x43, 0x80, 0x7c, 0x1c, 0x02, 0x75, //0x00005630 cmpb         $117, $2(%r12,%r11)
	0x0f, 0x85, 0xc2, 0xdc, 0xff, 0xff, //0x00005636 jne          LBB0_659
	0x49, 0x8d, 0x43, 0x03, //0x0000563c leaq         $3(%r11), %rax
	0x49, 0x89, 0x06, //0x00005640 movq         %rax, (%r14)
	0x43, 0x80, 0x7c, 0x1c, 0x03, 0x65, //0x00005643 cmpb         $101, $3(%r12,%r11)
	0x0f, 0x85, 0xaf, 0xdc, 0xff, 0xff, //0x00005649 jne          LBB0_659
	//0x0000564f LBB0_854
	0x49, 0x83, 0xc3, 0x04, //0x0000564f addq         $4, %r11
	//0x00005653 LBB0_1103
	0x48, 0x8b, 0x44, 0x24, 0x08, //0x00005653 movq         $8(%rsp), %rax
	0x4c, 0x89, 0x18, //0x00005658 movq         %r11, (%rax)
	0xe9, 0x9e, 0xdc, 0xff, 0xff, //0x0000565b jmp          LBB0_659
	//0x00005660 LBB0_1105
	0x48, 0x8b, 0x44, 0x24, 0x08, //0x00005660 movq         $8(%rsp), %rax
	0x4c, 0x8b, 0x28, //0x00005665 movq         (%rax), %r13
	//0x00005668 LBB0_1106
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00005668 movq         $-1, %r14
	//0x0000566f LBB0_1107
	0x49, 0xf7, 0xd6, //0x0000566f notq         %r14
	0x4d, 0x01, 0xee, //0x00005672 addq         %r13, %r14
	0x48, 0x8b, 0x44, 0x24, 0x08, //0x00005675 movq         $8(%rsp), %rax
	0x4c, 0x89, 0x30, //0x0000567a movq         %r14, (%rax)
	0xe9, 0x75, 0xdc, 0xff, 0xff, //0x0000567d jmp          LBB0_658
	//0x00005682 LBB0_1108
	0x4c, 0x89, 0xd9, //0x00005682 movq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x00005685 notq         %rcx
	0x48, 0x01, 0xc8, //0x00005688 addq         %rcx, %rax
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000568b movq         $-1, %rcx
	0x48, 0x39, 0xd8, //0x00005692 cmpq         %rbx, %rax
	0x0f, 0x82, 0xc7, 0xda, 0xff, 0xff, //0x00005695 jb           LBB0_634
	0xe9, 0x5e, 0xdc, 0xff, 0xff, //0x0000569b jmp          LBB0_659
	//0x000056a0 LBB0_1109
	0x4c, 0x89, 0x6c, 0x24, 0x18, //0x000056a0 movq         %r13, $24(%rsp)
	0xe9, 0x26, 0xf7, 0xff, 0xff, //0x000056a5 jmp          LBB0_979
	//0x000056aa LBB0_1110
	0x49, 0x83, 0xc1, 0x02, //0x000056aa addq         $2, %r9
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x000056ae movq         $-2, %rcx
	0xe9, 0x32, 0x00, 0x00, 0x00, //0x000056b5 jmp          LBB0_1115
	//0x000056ba LBB0_1116
	0x4d, 0x89, 0xe8, //0x000056ba movq         %r13, %r8
	0xe9, 0xe6, 0xfd, 0xff, 0xff, //0x000056bd jmp          LBB0_1083
	//0x000056c2 LBB0_1111
	0x4c, 0x29, 0xdb, //0x000056c2 subq         %r11, %rbx
	0x48, 0xff, 0xc3, //0x000056c5 incq         %rbx
	0x48, 0x89, 0x1e, //0x000056c8 movq         %rbx, (%rsi)
	0x48, 0x89, 0xc1, //0x000056cb movq         %rax, %rcx
	0xe9, 0x2b, 0xdc, 0xff, 0xff, //0x000056ce jmp          LBB0_659
	//0x000056d3 LBB0_1112
	0x49, 0xff, 0xc1, //0x000056d3 incq         %r9
	0x48, 0xc7, 0xc1, 0xfd, 0xff, 0xff, 0xff, //0x000056d6 movq         $-3, %rcx
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x000056dd jmp          LBB0_1115
	//0x000056e2 LBB0_1113
	0x49, 0xff, 0xc1, //0x000056e2 incq         %r9
	//0x000056e5 LBB0_1114
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000056e5 movq         $-1, %rcx
	//0x000056ec LBB0_1115
	0x48, 0x8b, 0x44, 0x24, 0x08, //0x000056ec movq         $8(%rsp), %rax
	0x48, 0x8b, 0x14, 0x24, //0x000056f1 movq         (%rsp), %rdx
	0x49, 0x29, 0xd1, //0x000056f5 subq         %rdx, %r9
	0x4c, 0x89, 0x08, //0x000056f8 movq         %r9, (%rax)
	0xe9, 0xfe, 0xdb, 0xff, 0xff, //0x000056fb jmp          LBB0_659
	//0x00005700 LBB0_1117
	0x4c, 0x01, 0xdb, //0x00005700 addq         %r11, %rbx
	0xe9, 0xbd, 0xf7, 0xff, 0xff, //0x00005703 jmp          LBB0_1027
	//0x00005708 LBB0_1118
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x00005708 movq         $16(%rsp), %rax
	0x48, 0x8b, 0x40, 0x08, //0x0000570d movq         $8(%rax), %rax
	0x48, 0x8b, 0x54, 0x24, 0x08, //0x00005711 movq         $8(%rsp), %rdx
	0x48, 0x89, 0x02, //0x00005716 movq         %rax, (%rdx)
	0xe9, 0xe0, 0xdb, 0xff, 0xff, //0x00005719 jmp          LBB0_659
	//0x0000571e LBB0_1119
	0x48, 0xc7, 0xc1, 0xfc, 0xff, 0xff, 0xff, //0x0000571e movq         $-4, %rcx
	0x4d, 0x89, 0xd9, //0x00005725 movq         %r11, %r9
	0xe9, 0xbf, 0xff, 0xff, 0xff, //0x00005728 jmp          LBB0_1115
	//0x0000572d LBB0_1120
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x0000572d movq         $-2, %rcx
	0x4d, 0x89, 0xd9, //0x00005734 movq         %r11, %r9
	0xe9, 0xb0, 0xff, 0xff, 0xff, //0x00005737 jmp          LBB0_1115
	//0x0000573c LBB0_1123
	0x4c, 0x8b, 0x74, 0x24, 0x08, //0x0000573c movq         $8(%rsp), %r14
	0xe9, 0x8a, 0xf6, 0xff, 0xff, //0x00005741 jmp          LBB0_979
	//0x00005746 LBB0_1121
	0x49, 0x8d, 0x51, 0xff, //0x00005746 leaq         $-1(%r9), %rdx
	0x4c, 0x39, 0xfa, //0x0000574a cmpq         %r15, %rdx
	0x0f, 0x84, 0xab, 0xdb, 0xff, 0xff, //0x0000574d je           LBB0_659
	0x4b, 0x8d, 0x5c, 0x17, 0x02, //0x00005753 leaq         $2(%r15,%r10), %rbx
	0x4d, 0x29, 0xf9, //0x00005758 subq         %r15, %r9
	0x49, 0x83, 0xc1, 0xfe, //0x0000575b addq         $-2, %r9
	0x4d, 0x89, 0xcf, //0x0000575f movq         %r9, %r15
	0xe9, 0x5e, 0xf7, 0xff, 0xff, //0x00005762 jmp          LBB0_1027
	0x90, //0x00005767 .p2align 2, 0x90
	// // .set L0_0_set_491, LBB0_491-LJTI0_0
	// // .set L0_0_set_490, LBB0_490-LJTI0_0
	// // .set L0_0_set_477, LBB0_477-LJTI0_0
	// // .set L0_0_set_456, LBB0_456-LJTI0_0
	// // .set L0_0_set_527, LBB0_527-LJTI0_0
	// // .set L0_0_set_489, LBB0_489-LJTI0_0
	// // .set L0_0_set_476, LBB0_476-LJTI0_0
	// // .set L0_0_set_552, LBB0_552-LJTI0_0
	//0x00005768 LJTI0_0
	0x6e, 0xce, 0xff, 0xff, //0x00005768 .long L0_0_set_491
	0x68, 0xce, 0xff, 0xff, //0x0000576c .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005770 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005774 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005778 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x0000577c .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005780 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005784 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005788 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x0000578c .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005790 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005794 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005798 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x0000579c .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000057a0 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000057a4 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000057a8 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000057ac .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000057b0 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000057b4 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000057b8 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000057bc .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000057c0 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000057c4 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000057c8 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000057cc .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000057d0 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000057d4 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000057d8 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000057dc .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000057e0 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000057e4 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000057e8 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000057ec .long L0_0_set_490
	0x4f, 0xcd, 0xff, 0xff, //0x000057f0 .long L0_0_set_477
	0x68, 0xce, 0xff, 0xff, //0x000057f4 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000057f8 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000057fc .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005800 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005804 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005808 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x0000580c .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005810 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005814 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005818 .long L0_0_set_490
	0xf5, 0xcb, 0xff, 0xff, //0x0000581c .long L0_0_set_456
	0x68, 0xce, 0xff, 0xff, //0x00005820 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005824 .long L0_0_set_490
	0xf5, 0xcb, 0xff, 0xff, //0x00005828 .long L0_0_set_456
	0xf5, 0xcb, 0xff, 0xff, //0x0000582c .long L0_0_set_456
	0xf5, 0xcb, 0xff, 0xff, //0x00005830 .long L0_0_set_456
	0xf5, 0xcb, 0xff, 0xff, //0x00005834 .long L0_0_set_456
	0xf5, 0xcb, 0xff, 0xff, //0x00005838 .long L0_0_set_456
	0xf5, 0xcb, 0xff, 0xff, //0x0000583c .long L0_0_set_456
	0xf5, 0xcb, 0xff, 0xff, //0x00005840 .long L0_0_set_456
	0xf5, 0xcb, 0xff, 0xff, //0x00005844 .long L0_0_set_456
	0xf5, 0xcb, 0xff, 0xff, //0x00005848 .long L0_0_set_456
	0xf5, 0xcb, 0xff, 0xff, //0x0000584c .long L0_0_set_456
	0x68, 0xce, 0xff, 0xff, //0x00005850 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005854 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005858 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x0000585c .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005860 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005864 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005868 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x0000586c .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005870 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005874 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005878 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x0000587c .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005880 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005884 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005888 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x0000588c .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005890 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005894 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005898 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x0000589c .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000058a0 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000058a4 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000058a8 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000058ac .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000058b0 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000058b4 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000058b8 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000058bc .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000058c0 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000058c4 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000058c8 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000058cc .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000058d0 .long L0_0_set_490
	0x56, 0xd0, 0xff, 0xff, //0x000058d4 .long L0_0_set_527
	0x68, 0xce, 0xff, 0xff, //0x000058d8 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000058dc .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000058e0 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000058e4 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000058e8 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000058ec .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000058f0 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000058f4 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000058f8 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x000058fc .long L0_0_set_490
	0x4e, 0xce, 0xff, 0xff, //0x00005900 .long L0_0_set_489
	0x68, 0xce, 0xff, 0xff, //0x00005904 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005908 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x0000590c .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005910 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005914 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005918 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x0000591c .long L0_0_set_490
	0x3d, 0xcd, 0xff, 0xff, //0x00005920 .long L0_0_set_476
	0x68, 0xce, 0xff, 0xff, //0x00005924 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005928 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x0000592c .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005930 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005934 .long L0_0_set_490
	0x3d, 0xcd, 0xff, 0xff, //0x00005938 .long L0_0_set_476
	0x68, 0xce, 0xff, 0xff, //0x0000593c .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005940 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005944 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005948 .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x0000594c .long L0_0_set_490
	0x68, 0xce, 0xff, 0xff, //0x00005950 .long L0_0_set_490
	0x00, 0xd3, 0xff, 0xff, //0x00005954 .long L0_0_set_552
	// // .set L0_1_set_294, LBB0_294-LJTI0_1
	// // .set L0_1_set_293, LBB0_293-LJTI0_1
	// // .set L0_1_set_254, LBB0_254-LJTI0_1
	// // .set L0_1_set_232, LBB0_232-LJTI0_1
	// // .set L0_1_set_266, LBB0_266-LJTI0_1
	// // .set L0_1_set_292, LBB0_292-LJTI0_1
	// // .set L0_1_set_253, LBB0_253-LJTI0_1
	// // .set L0_1_set_326, LBB0_326-LJTI0_1
	//0x00005958 LJTI0_1
	0xee, 0xbe, 0xff, 0xff, //0x00005958 .long L0_1_set_294
	0xe8, 0xbe, 0xff, 0xff, //0x0000595c .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005960 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005964 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005968 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x0000596c .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005970 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005974 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005978 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x0000597c .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005980 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005984 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005988 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x0000598c .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005990 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005994 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005998 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x0000599c .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x000059a0 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x000059a4 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x000059a8 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x000059ac .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x000059b0 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x000059b4 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x000059b8 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x000059bc .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x000059c0 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x000059c4 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x000059c8 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x000059cc .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x000059d0 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x000059d4 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x000059d8 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x000059dc .long L0_1_set_293
	0x3f, 0xbb, 0xff, 0xff, //0x000059e0 .long L0_1_set_254
	0xe8, 0xbe, 0xff, 0xff, //0x000059e4 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x000059e8 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x000059ec .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x000059f0 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x000059f4 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x000059f8 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x000059fc .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a00 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a04 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a08 .long L0_1_set_293
	0xe9, 0xb9, 0xff, 0xff, //0x00005a0c .long L0_1_set_232
	0xe8, 0xbe, 0xff, 0xff, //0x00005a10 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a14 .long L0_1_set_293
	0xe9, 0xb9, 0xff, 0xff, //0x00005a18 .long L0_1_set_232
	0xe9, 0xb9, 0xff, 0xff, //0x00005a1c .long L0_1_set_232
	0xe9, 0xb9, 0xff, 0xff, //0x00005a20 .long L0_1_set_232
	0xe9, 0xb9, 0xff, 0xff, //0x00005a24 .long L0_1_set_232
	0xe9, 0xb9, 0xff, 0xff, //0x00005a28 .long L0_1_set_232
	0xe9, 0xb9, 0xff, 0xff, //0x00005a2c .long L0_1_set_232
	0xe9, 0xb9, 0xff, 0xff, //0x00005a30 .long L0_1_set_232
	0xe9, 0xb9, 0xff, 0xff, //0x00005a34 .long L0_1_set_232
	0xe9, 0xb9, 0xff, 0xff, //0x00005a38 .long L0_1_set_232
	0xe9, 0xb9, 0xff, 0xff, //0x00005a3c .long L0_1_set_232
	0xe8, 0xbe, 0xff, 0xff, //0x00005a40 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a44 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a48 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a4c .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a50 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a54 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a58 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a5c .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a60 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a64 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a68 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a6c .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a70 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a74 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a78 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a7c .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a80 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a84 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a88 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a8c .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a90 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a94 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a98 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005a9c .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005aa0 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005aa4 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005aa8 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005aac .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005ab0 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005ab4 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005ab8 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005abc .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005ac0 .long L0_1_set_293
	0x1e, 0xbc, 0xff, 0xff, //0x00005ac4 .long L0_1_set_266
	0xe8, 0xbe, 0xff, 0xff, //0x00005ac8 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005acc .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005ad0 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005ad4 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005ad8 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005adc .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005ae0 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005ae4 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005ae8 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005aec .long L0_1_set_293
	0xd2, 0xbe, 0xff, 0xff, //0x00005af0 .long L0_1_set_292
	0xe8, 0xbe, 0xff, 0xff, //0x00005af4 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005af8 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005afc .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005b00 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005b04 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005b08 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005b0c .long L0_1_set_293
	0x2d, 0xbb, 0xff, 0xff, //0x00005b10 .long L0_1_set_253
	0xe8, 0xbe, 0xff, 0xff, //0x00005b14 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005b18 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005b1c .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005b20 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005b24 .long L0_1_set_293
	0x2d, 0xbb, 0xff, 0xff, //0x00005b28 .long L0_1_set_253
	0xe8, 0xbe, 0xff, 0xff, //0x00005b2c .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005b30 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005b34 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005b38 .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005b3c .long L0_1_set_293
	0xe8, 0xbe, 0xff, 0xff, //0x00005b40 .long L0_1_set_293
	0xb8, 0xc0, 0xff, 0xff, //0x00005b44 .long L0_1_set_326
	// // .set L0_2_set_698, LBB0_698-LJTI0_2
	// // .set L0_2_set_725, LBB0_725-LJTI0_2
	// // .set L0_2_set_704, LBB0_704-LJTI0_2
	// // .set L0_2_set_722, LBB0_722-LJTI0_2
	// // .set L0_2_set_701, LBB0_701-LJTI0_2
	// // .set L0_2_set_727, LBB0_727-LJTI0_2
	//0x00005b48 LJTI0_2
	0xf9, 0xd9, 0xff, 0xff, //0x00005b48 .long L0_2_set_698
	0x25, 0xdc, 0xff, 0xff, //0x00005b4c .long L0_2_set_725
	0x30, 0xda, 0xff, 0xff, //0x00005b50 .long L0_2_set_704
	0xb9, 0xdb, 0xff, 0xff, //0x00005b54 .long L0_2_set_722
	0x10, 0xda, 0xff, 0xff, //0x00005b58 .long L0_2_set_701
	0x50, 0xdc, 0xff, 0xff, //0x00005b5c .long L0_2_set_727
	// // .set L0_3_set_659, LBB0_659-LJTI0_3
	// // .set L0_3_set_658, LBB0_658-LJTI0_3
	// // .set L0_3_set_822, LBB0_822-LJTI0_3
	// // .set L0_3_set_839, LBB0_839-LJTI0_3
	// // .set L0_3_set_729, LBB0_729-LJTI0_3
	// // .set L0_3_set_843, LBB0_843-LJTI0_3
	// // .set L0_3_set_845, LBB0_845-LJTI0_3
	// // .set L0_3_set_848, LBB0_848-LJTI0_3
	// // .set L0_3_set_855, LBB0_855-LJTI0_3
	// // .set L0_3_set_861, LBB0_861-LJTI0_3
	//0x00005b60 LJTI0_3
	0x9e, 0xd7, 0xff, 0xff, //0x00005b60 .long L0_3_set_659
	0x97, 0xd7, 0xff, 0xff, //0x00005b64 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005b68 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005b6c .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005b70 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005b74 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005b78 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005b7c .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005b80 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005b84 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005b88 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005b8c .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005b90 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005b94 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005b98 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005b9c .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005ba0 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005ba4 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005ba8 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005bac .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005bb0 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005bb4 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005bb8 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005bbc .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005bc0 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005bc4 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005bc8 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005bcc .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005bd0 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005bd4 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005bd8 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005bdc .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005be0 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005be4 .long L0_3_set_658
	0x59, 0xe3, 0xff, 0xff, //0x00005be8 .long L0_3_set_822
	0x97, 0xd7, 0xff, 0xff, //0x00005bec .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005bf0 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005bf4 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005bf8 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005bfc .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c00 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c04 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c08 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c0c .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c10 .long L0_3_set_658
	0xc9, 0xe4, 0xff, 0xff, //0x00005c14 .long L0_3_set_839
	0x97, 0xd7, 0xff, 0xff, //0x00005c18 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c1c .long L0_3_set_658
	0xda, 0xdb, 0xff, 0xff, //0x00005c20 .long L0_3_set_729
	0xda, 0xdb, 0xff, 0xff, //0x00005c24 .long L0_3_set_729
	0xda, 0xdb, 0xff, 0xff, //0x00005c28 .long L0_3_set_729
	0xda, 0xdb, 0xff, 0xff, //0x00005c2c .long L0_3_set_729
	0xda, 0xdb, 0xff, 0xff, //0x00005c30 .long L0_3_set_729
	0xda, 0xdb, 0xff, 0xff, //0x00005c34 .long L0_3_set_729
	0xda, 0xdb, 0xff, 0xff, //0x00005c38 .long L0_3_set_729
	0xda, 0xdb, 0xff, 0xff, //0x00005c3c .long L0_3_set_729
	0xda, 0xdb, 0xff, 0xff, //0x00005c40 .long L0_3_set_729
	0xda, 0xdb, 0xff, 0xff, //0x00005c44 .long L0_3_set_729
	0x97, 0xd7, 0xff, 0xff, //0x00005c48 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c4c .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c50 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c54 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c58 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c5c .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c60 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c64 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c68 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c6c .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c70 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c74 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c78 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c7c .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c80 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c84 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c88 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c8c .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c90 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c94 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c98 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005c9c .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005ca0 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005ca4 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005ca8 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005cac .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005cb0 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005cb4 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005cb8 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005cbc .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005cc0 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005cc4 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005cc8 .long L0_3_set_658
	0x03, 0xe5, 0xff, 0xff, //0x00005ccc .long L0_3_set_843
	0x97, 0xd7, 0xff, 0xff, //0x00005cd0 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005cd4 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005cd8 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005cdc .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005ce0 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005ce4 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005ce8 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005cec .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005cf0 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005cf4 .long L0_3_set_658
	0x27, 0xe5, 0xff, 0xff, //0x00005cf8 .long L0_3_set_845
	0x97, 0xd7, 0xff, 0xff, //0x00005cfc .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005d00 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005d04 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005d08 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005d0c .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005d10 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005d14 .long L0_3_set_658
	0x63, 0xe5, 0xff, 0xff, //0x00005d18 .long L0_3_set_848
	0x97, 0xd7, 0xff, 0xff, //0x00005d1c .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005d20 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005d24 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005d28 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005d2c .long L0_3_set_658
	0x86, 0xe5, 0xff, 0xff, //0x00005d30 .long L0_3_set_855
	0x97, 0xd7, 0xff, 0xff, //0x00005d34 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005d38 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005d3c .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005d40 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005d44 .long L0_3_set_658
	0x97, 0xd7, 0xff, 0xff, //0x00005d48 .long L0_3_set_658
	0xa9, 0xe5, 0xff, 0xff, //0x00005d4c .long L0_3_set_861
	// // .set L0_4_set_912, LBB0_912-LJTI0_4
	// // .set L0_4_set_923, LBB0_923-LJTI0_4
	// // .set L0_4_set_919, LBB0_919-LJTI0_4
	// // .set L0_4_set_914, LBB0_914-LJTI0_4
	// // .set L0_4_set_917, LBB0_917-LJTI0_4
	//0x00005d50 LJTI0_4
	0x6a, 0xe7, 0xff, 0xff, //0x00005d50 .long L0_4_set_912
	0xb9, 0xe8, 0xff, 0xff, //0x00005d54 .long L0_4_set_923
	0x6a, 0xe7, 0xff, 0xff, //0x00005d58 .long L0_4_set_912
	0xcf, 0xe7, 0xff, 0xff, //0x00005d5c .long L0_4_set_919
	0xb9, 0xe8, 0xff, 0xff, //0x00005d60 .long L0_4_set_923
	0x84, 0xe7, 0xff, 0xff, //0x00005d64 .long L0_4_set_914
	0x84, 0xe7, 0xff, 0xff, //0x00005d68 .long L0_4_set_914
	0x84, 0xe7, 0xff, 0xff, //0x00005d6c .long L0_4_set_914
	0x84, 0xe7, 0xff, 0xff, //0x00005d70 .long L0_4_set_914
	0x84, 0xe7, 0xff, 0xff, //0x00005d74 .long L0_4_set_914
	0x84, 0xe7, 0xff, 0xff, //0x00005d78 .long L0_4_set_914
	0x84, 0xe7, 0xff, 0xff, //0x00005d7c .long L0_4_set_914
	0x84, 0xe7, 0xff, 0xff, //0x00005d80 .long L0_4_set_914
	0x84, 0xe7, 0xff, 0xff, //0x00005d84 .long L0_4_set_914
	0x84, 0xe7, 0xff, 0xff, //0x00005d88 .long L0_4_set_914
	0xb9, 0xe8, 0xff, 0xff, //0x00005d8c .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005d90 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005d94 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005d98 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005d9c .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005da0 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005da4 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005da8 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005dac .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005db0 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005db4 .long L0_4_set_923
	0xb4, 0xe7, 0xff, 0xff, //0x00005db8 .long L0_4_set_917
	0xb9, 0xe8, 0xff, 0xff, //0x00005dbc .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005dc0 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005dc4 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005dc8 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005dcc .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005dd0 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005dd4 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005dd8 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005ddc .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005de0 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005de4 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005de8 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005dec .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005df0 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005df4 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005df8 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005dfc .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005e00 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005e04 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005e08 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005e0c .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005e10 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005e14 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005e18 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005e1c .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005e20 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005e24 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005e28 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005e2c .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005e30 .long L0_4_set_923
	0xb9, 0xe8, 0xff, 0xff, //0x00005e34 .long L0_4_set_923
	0xb4, 0xe7, 0xff, 0xff, //0x00005e38 .long L0_4_set_917
	// // .set L0_5_set_798, LBB0_798-LJTI0_5
	// // .set L0_5_set_809, LBB0_809-LJTI0_5
	// // .set L0_5_set_805, LBB0_805-LJTI0_5
	// // .set L0_5_set_800, LBB0_800-LJTI0_5
	// // .set L0_5_set_803, LBB0_803-LJTI0_5
	//0x00005e3c LJTI0_5
	0x9a, 0xde, 0xff, 0xff, //0x00005e3c .long L0_5_set_798
	0x48, 0xdf, 0xff, 0xff, //0x00005e40 .long L0_5_set_809
	0x9a, 0xde, 0xff, 0xff, //0x00005e44 .long L0_5_set_798
	0xff, 0xde, 0xff, 0xff, //0x00005e48 .long L0_5_set_805
	0x48, 0xdf, 0xff, 0xff, //0x00005e4c .long L0_5_set_809
	0xb4, 0xde, 0xff, 0xff, //0x00005e50 .long L0_5_set_800
	0xb4, 0xde, 0xff, 0xff, //0x00005e54 .long L0_5_set_800
	0xb4, 0xde, 0xff, 0xff, //0x00005e58 .long L0_5_set_800
	0xb4, 0xde, 0xff, 0xff, //0x00005e5c .long L0_5_set_800
	0xb4, 0xde, 0xff, 0xff, //0x00005e60 .long L0_5_set_800
	0xb4, 0xde, 0xff, 0xff, //0x00005e64 .long L0_5_set_800
	0xb4, 0xde, 0xff, 0xff, //0x00005e68 .long L0_5_set_800
	0xb4, 0xde, 0xff, 0xff, //0x00005e6c .long L0_5_set_800
	0xb4, 0xde, 0xff, 0xff, //0x00005e70 .long L0_5_set_800
	0xb4, 0xde, 0xff, 0xff, //0x00005e74 .long L0_5_set_800
	0x48, 0xdf, 0xff, 0xff, //0x00005e78 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005e7c .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005e80 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005e84 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005e88 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005e8c .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005e90 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005e94 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005e98 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005e9c .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005ea0 .long L0_5_set_809
	0xe4, 0xde, 0xff, 0xff, //0x00005ea4 .long L0_5_set_803
	0x48, 0xdf, 0xff, 0xff, //0x00005ea8 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005eac .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005eb0 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005eb4 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005eb8 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005ebc .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005ec0 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005ec4 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005ec8 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005ecc .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005ed0 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005ed4 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005ed8 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005edc .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005ee0 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005ee4 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005ee8 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005eec .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005ef0 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005ef4 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005ef8 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005efc .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005f00 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005f04 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005f08 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005f0c .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005f10 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005f14 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005f18 .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005f1c .long L0_5_set_809
	0x48, 0xdf, 0xff, 0xff, //0x00005f20 .long L0_5_set_809
	0xe4, 0xde, 0xff, 0xff, //0x00005f24 .long L0_5_set_803
	// // .set L0_6_set_659, LBB0_659-LJTI0_6
	// // .set L0_6_set_1087, LBB0_1087-LJTI0_6
	// // .set L0_6_set_1017, LBB0_1017-LJTI0_6
	// // .set L0_6_set_636, LBB0_636-LJTI0_6
	// // .set L0_6_set_1028, LBB0_1028-LJTI0_6
	// // .set L0_6_set_1052, LBB0_1052-LJTI0_6
	// // .set L0_6_set_1015, LBB0_1015-LJTI0_6
	// // .set L0_6_set_1055, LBB0_1055-LJTI0_6
	//0x00005f28 LJTI0_6
	0xd6, 0xd3, 0xff, 0xff, //0x00005f28 .long L0_6_set_659
	0xa2, 0xf5, 0xff, 0xff, //0x00005f2c .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f30 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f34 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f38 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f3c .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f40 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f44 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f48 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f4c .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f50 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f54 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f58 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f5c .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f60 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f64 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f68 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f6c .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f70 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f74 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f78 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f7c .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f80 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f84 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f88 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f8c .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f90 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f94 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f98 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005f9c .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005fa0 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005fa4 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005fa8 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005fac .long L0_6_set_1087
	0xc5, 0xee, 0xff, 0xff, //0x00005fb0 .long L0_6_set_1017
	0xa2, 0xf5, 0xff, 0xff, //0x00005fb4 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005fb8 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005fbc .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005fc0 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005fc4 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005fc8 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005fcc .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005fd0 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005fd4 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005fd8 .long L0_6_set_1087
	0x66, 0xd2, 0xff, 0xff, //0x00005fdc .long L0_6_set_636
	0xa2, 0xf5, 0xff, 0xff, //0x00005fe0 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00005fe4 .long L0_6_set_1087
	0x66, 0xd2, 0xff, 0xff, //0x00005fe8 .long L0_6_set_636
	0x66, 0xd2, 0xff, 0xff, //0x00005fec .long L0_6_set_636
	0x66, 0xd2, 0xff, 0xff, //0x00005ff0 .long L0_6_set_636
	0x66, 0xd2, 0xff, 0xff, //0x00005ff4 .long L0_6_set_636
	0x66, 0xd2, 0xff, 0xff, //0x00005ff8 .long L0_6_set_636
	0x66, 0xd2, 0xff, 0xff, //0x00005ffc .long L0_6_set_636
	0x66, 0xd2, 0xff, 0xff, //0x00006000 .long L0_6_set_636
	0x66, 0xd2, 0xff, 0xff, //0x00006004 .long L0_6_set_636
	0x66, 0xd2, 0xff, 0xff, //0x00006008 .long L0_6_set_636
	0x66, 0xd2, 0xff, 0xff, //0x0000600c .long L0_6_set_636
	0xa2, 0xf5, 0xff, 0xff, //0x00006010 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006014 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006018 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x0000601c .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006020 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006024 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006028 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x0000602c .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006030 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006034 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006038 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x0000603c .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006040 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006044 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006048 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x0000604c .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006050 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006054 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006058 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x0000605c .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006060 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006064 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006068 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x0000606c .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006070 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006074 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006078 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x0000607c .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006080 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006084 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006088 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x0000608c .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006090 .long L0_6_set_1087
	0xb0, 0xef, 0xff, 0xff, //0x00006094 .long L0_6_set_1028
	0xa2, 0xf5, 0xff, 0xff, //0x00006098 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x0000609c .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x000060a0 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x000060a4 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x000060a8 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x000060ac .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x000060b0 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x000060b4 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x000060b8 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x000060bc .long L0_6_set_1087
	0x57, 0xf2, 0xff, 0xff, //0x000060c0 .long L0_6_set_1052
	0xa2, 0xf5, 0xff, 0xff, //0x000060c4 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x000060c8 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x000060cc .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x000060d0 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x000060d4 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x000060d8 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x000060dc .long L0_6_set_1087
	0x95, 0xee, 0xff, 0xff, //0x000060e0 .long L0_6_set_1015
	0xa2, 0xf5, 0xff, 0xff, //0x000060e4 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x000060e8 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x000060ec .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x000060f0 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x000060f4 .long L0_6_set_1087
	0x95, 0xee, 0xff, 0xff, //0x000060f8 .long L0_6_set_1015
	0xa2, 0xf5, 0xff, 0xff, //0x000060fc .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006100 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006104 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006108 .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x0000610c .long L0_6_set_1087
	0xa2, 0xf5, 0xff, 0xff, //0x00006110 .long L0_6_set_1087
	0x70, 0xf2, 0xff, 0xff, //0x00006114 .long L0_6_set_1055
	//0x00006118 .p2align 2, 0x00
	//0x00006118 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00006118 .long 2
	0x00, 0x00, 0x00, 0x00, //0x0000611c .p2align 4, 0x00
	//0x00006120 __UnquoteTab
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006120 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006130 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2f, //0x00006140 QUAD $0x0000000000220000; QUAD $0x2f00000000000000  // .ascii 16, '\x00\x00"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00/'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006150 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006160 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5c, 0x00, 0x00, 0x00, //0x00006170 QUAD $0x0000000000000000; QUAD $0x0000005c00000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\\\x00\x00\x00'
	0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x00, //0x00006180 QUAD $0x000c000000080000; QUAD $0x000a000000000000  // .ascii 16, '\x00\x00\x08\x00\x00\x00\x0c\x00\x00\x00\x00\x00\x00\x00\n\x00'
	0x00, 0x00, 0x0d, 0x00, 0x09, 0xff, //0x00006190 LONG $0x000d0000; WORD $0xff09  // .ascii 6, '\x00\x00\r\x00\t\xff'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006196 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061a6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061b6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061c6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061d6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061e6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061f6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006206 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006216 QUAD $0x0000000000000000; WORD $0x0000  // .space 10, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
}
 
