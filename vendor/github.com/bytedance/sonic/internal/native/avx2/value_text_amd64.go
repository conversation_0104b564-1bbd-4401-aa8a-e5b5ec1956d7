// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_value = []byte{
	// .p2align 5, 0x00
	// LCPI0_0
	0x20, // .byte 32
	0x00, //0x00000001 .byte 0
	0x00, //0x00000002 .byte 0
	0x00, //0x00000003 .byte 0
	0x00, //0x00000004 .byte 0
	0x00, //0x00000005 .byte 0
	0x00, //0x00000006 .byte 0
	0x00, //0x00000007 .byte 0
	0x00, //0x00000008 .byte 0
	0x09, //0x00000009 .byte 9
	0x0a, //0x0000000a .byte 10
	0x00, //0x0000000b .byte 0
	0x00, //0x0000000c .byte 0
	0x0d, //0x0000000d .byte 13
	0x00, //0x0000000e .byte 0
	0x00, //0x0000000f .byte 0
	0x20, //0x00000010 .byte 32
	0x00, //0x00000011 .byte 0
	0x00, //0x00000012 .byte 0
	0x00, //0x00000013 .byte 0
	0x00, //0x00000014 .byte 0
	0x00, //0x00000015 .byte 0
	0x00, //0x00000016 .byte 0
	0x00, //0x00000017 .byte 0
	0x00, //0x00000018 .byte 0
	0x09, //0x00000019 .byte 9
	0x0a, //0x0000001a .byte 10
	0x00, //0x0000001b .byte 0
	0x00, //0x0000001c .byte 0
	0x0d, //0x0000001d .byte 13
	0x00, //0x0000001e .byte 0
	0x00, //0x0000001f .byte 0
	//0x00000020 LCPI0_1
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000020 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000030 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x00000040 LCPI0_2
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000040 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000050 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000060 LCPI0_3
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000060 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000070 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000080 LCPI0_4
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000080 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000090 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	//0x000000a0 LCPI0_5
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x000000a0 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x000000b0 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	//0x000000c0 LCPI0_6
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x000000c0 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x000000d0 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x000000e0 LCPI0_7
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x000000e0 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x000000f0 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000100 LCPI0_8
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000100 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000110 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x00000120 LCPI0_9
	0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, //0x00000120 QUAD $0x6565656565656565; QUAD $0x6565656565656565  // .space 16, 'eeeeeeeeeeeeeeee'
	0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, //0x00000130 QUAD $0x6565656565656565; QUAD $0x6565656565656565  // .space 16, 'eeeeeeeeeeeeeeee'
	//0x00000140 LCPI0_21
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000140 QUAD $0x0000000000000000  // .space 8, '\x00\x00\x00\x00\x00\x00\x00\x00'
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000148 .quad 1
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000150 .quad 1
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000158 .quad 1
	//0x00000160 .p2align 4, 0x00
	//0x00000160 LCPI0_10
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000160 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	//0x00000170 LCPI0_11
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x00000170 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	//0x00000180 LCPI0_12
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000180 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x00000190 LCPI0_13
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000190 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x000001a0 LCPI0_14
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x000001a0 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x000001b0 LCPI0_15
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x000001b0 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x000001c0 LCPI0_16
	0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, //0x000001c0 QUAD $0x6565656565656565; QUAD $0x6565656565656565  // .space 16, 'eeeeeeeeeeeeeeee'
	//0x000001d0 LCPI0_17
	0x00, 0x00, 0x30, 0x43, //0x000001d0 .long 1127219200
	0x00, 0x00, 0x30, 0x45, //0x000001d4 .long 1160773632
	0x00, 0x00, 0x00, 0x00, //0x000001d8 .long 0
	0x00, 0x00, 0x00, 0x00, //0x000001dc .long 0
	//0x000001e0 LCPI0_18
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x43, //0x000001e0 .quad 4841369599423283200
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x45, //0x000001e8 .quad 4985484787499139072
	//0x000001f0 .p2align 3, 0x00
	//0x000001f0 LCPI0_19
	0x00, 0x00, 0x34, 0x26, 0xf5, 0x6b, 0x0c, 0x43, //0x000001f0 .quad 4831355200913801216
	//0x000001f8 LCPI0_20
	0x00, 0x00, 0x34, 0x26, 0xf5, 0x6b, 0x0c, 0xc3, //0x000001f8 .quad -4392016835940974592
	//0x00000200 LCPI0_22
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000200 .quad 1
	//0x00000208 LCPI0_23
	0x10, 0x27, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000208 .quad 10000
	//0x00000210 LCPI0_24
	0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000210 .quad 10
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000218 .p2align 4, 0x90
	//0x00000220 _value
	0x55, //0x00000220 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000221 movq         %rsp, %rbp
	0x41, 0x57, //0x00000224 pushq        %r15
	0x41, 0x56, //0x00000226 pushq        %r14
	0x41, 0x55, //0x00000228 pushq        %r13
	0x41, 0x54, //0x0000022a pushq        %r12
	0x53, //0x0000022c pushq        %rbx
	0x48, 0x83, 0xec, 0x38, //0x0000022d subq         $56, %rsp
	0x49, 0x89, 0xce, //0x00000231 movq         %rcx, %r14
	0x49, 0x89, 0xf3, //0x00000234 movq         %rsi, %r11
	0x48, 0x39, 0xf2, //0x00000237 cmpq         %rsi, %rdx
	0x0f, 0x83, 0x27, 0x00, 0x00, 0x00, //0x0000023a jae          LBB0_5
	0x8a, 0x04, 0x17, //0x00000240 movb         (%rdi,%rdx), %al
	0x3c, 0x0d, //0x00000243 cmpb         $13, %al
	0x0f, 0x84, 0x1c, 0x00, 0x00, 0x00, //0x00000245 je           LBB0_5
	0x3c, 0x20, //0x0000024b cmpb         $32, %al
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x0000024d je           LBB0_5
	0x8d, 0x48, 0xf7, //0x00000253 leal         $-9(%rax), %ecx
	0x80, 0xf9, 0x01, //0x00000256 cmpb         $1, %cl
	0x0f, 0x86, 0x08, 0x00, 0x00, 0x00, //0x00000259 jbe          LBB0_5
	0x49, 0x89, 0xd7, //0x0000025f movq         %rdx, %r15
	0xe9, 0x85, 0x01, 0x00, 0x00, //0x00000262 jmp          LBB0_33
	//0x00000267 LBB0_5
	0x4c, 0x8d, 0x7a, 0x01, //0x00000267 leaq         $1(%rdx), %r15
	0x4d, 0x39, 0xdf, //0x0000026b cmpq         %r11, %r15
	0x0f, 0x83, 0x20, 0x00, 0x00, 0x00, //0x0000026e jae          LBB0_9
	0x42, 0x8a, 0x04, 0x3f, //0x00000274 movb         (%rdi,%r15), %al
	0x3c, 0x0d, //0x00000278 cmpb         $13, %al
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x0000027a je           LBB0_9
	0x3c, 0x20, //0x00000280 cmpb         $32, %al
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x00000282 je           LBB0_9
	0x8d, 0x48, 0xf7, //0x00000288 leal         $-9(%rax), %ecx
	0x80, 0xf9, 0x01, //0x0000028b cmpb         $1, %cl
	0x0f, 0x87, 0x58, 0x01, 0x00, 0x00, //0x0000028e ja           LBB0_33
	//0x00000294 LBB0_9
	0x4c, 0x8d, 0x7a, 0x02, //0x00000294 leaq         $2(%rdx), %r15
	0x4d, 0x39, 0xdf, //0x00000298 cmpq         %r11, %r15
	0x0f, 0x83, 0x20, 0x00, 0x00, 0x00, //0x0000029b jae          LBB0_13
	0x42, 0x8a, 0x04, 0x3f, //0x000002a1 movb         (%rdi,%r15), %al
	0x3c, 0x0d, //0x000002a5 cmpb         $13, %al
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x000002a7 je           LBB0_13
	0x3c, 0x20, //0x000002ad cmpb         $32, %al
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x000002af je           LBB0_13
	0x8d, 0x48, 0xf7, //0x000002b5 leal         $-9(%rax), %ecx
	0x80, 0xf9, 0x01, //0x000002b8 cmpb         $1, %cl
	0x0f, 0x87, 0x2b, 0x01, 0x00, 0x00, //0x000002bb ja           LBB0_33
	//0x000002c1 LBB0_13
	0x4c, 0x8d, 0x7a, 0x03, //0x000002c1 leaq         $3(%rdx), %r15
	0x4d, 0x39, 0xdf, //0x000002c5 cmpq         %r11, %r15
	0x0f, 0x83, 0x20, 0x00, 0x00, 0x00, //0x000002c8 jae          LBB0_17
	0x42, 0x8a, 0x04, 0x3f, //0x000002ce movb         (%rdi,%r15), %al
	0x3c, 0x0d, //0x000002d2 cmpb         $13, %al
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x000002d4 je           LBB0_17
	0x3c, 0x20, //0x000002da cmpb         $32, %al
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x000002dc je           LBB0_17
	0x8d, 0x48, 0xf7, //0x000002e2 leal         $-9(%rax), %ecx
	0x80, 0xf9, 0x01, //0x000002e5 cmpb         $1, %cl
	0x0f, 0x87, 0xfe, 0x00, 0x00, 0x00, //0x000002e8 ja           LBB0_33
	//0x000002ee LBB0_17
	0x48, 0x8d, 0x4a, 0x04, //0x000002ee leaq         $4(%rdx), %rcx
	0x4c, 0x39, 0xd9, //0x000002f2 cmpq         %r11, %rcx
	0x0f, 0x83, 0xcd, 0x00, 0x00, 0x00, //0x000002f5 jae          LBB0_30
	0x4c, 0x8d, 0x3c, 0x0f, //0x000002fb leaq         (%rdi,%rcx), %r15
	0x4c, 0x89, 0xd8, //0x000002ff movq         %r11, %rax
	0x48, 0x29, 0xc8, //0x00000302 subq         %rcx, %rax
	0x48, 0x83, 0xf8, 0x20, //0x00000305 cmpq         $32, %rax
	0x0f, 0x82, 0x64, 0x00, 0x00, 0x00, //0x00000309 jb           LBB0_23
	0x4c, 0x89, 0xd9, //0x0000030f movq         %r11, %rcx
	0x48, 0x29, 0xd1, //0x00000312 subq         %rdx, %rcx
	0x48, 0x83, 0xc1, 0xdc, //0x00000315 addq         $-36, %rcx
	0x48, 0x89, 0xce, //0x00000319 movq         %rcx, %rsi
	0x48, 0x83, 0xe6, 0xe0, //0x0000031c andq         $-32, %rsi
	0x48, 0x01, 0xd6, //0x00000320 addq         %rdx, %rsi
	0x48, 0x8d, 0x74, 0x37, 0x24, //0x00000323 leaq         $36(%rdi,%rsi), %rsi
	0x83, 0xe1, 0x1f, //0x00000328 andl         $31, %ecx
	0xc5, 0xfe, 0x6f, 0x05, 0xcd, 0xfc, 0xff, 0xff, //0x0000032b vmovdqu      $-819(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000333 .p2align 4, 0x90
	//0x00000340 LBB0_20
	0xc4, 0xc1, 0x7e, 0x6f, 0x0f, //0x00000340 vmovdqu      (%r15), %ymm1
	0xc4, 0xe2, 0x7d, 0x00, 0xd1, //0x00000345 vpshufb      %ymm1, %ymm0, %ymm2
	0xc5, 0xf5, 0x74, 0xca, //0x0000034a vpcmpeqb     %ymm2, %ymm1, %ymm1
	0xc5, 0xfd, 0xd7, 0xd9, //0x0000034e vpmovmskb    %ymm1, %ebx
	0x83, 0xfb, 0xff, //0x00000352 cmpl         $-1, %ebx
	0x0f, 0x85, 0x75, 0x00, 0x00, 0x00, //0x00000355 jne          LBB0_31
	0x49, 0x83, 0xc7, 0x20, //0x0000035b addq         $32, %r15
	0x48, 0x83, 0xc0, 0xe0, //0x0000035f addq         $-32, %rax
	0x48, 0x83, 0xf8, 0x1f, //0x00000363 cmpq         $31, %rax
	0x0f, 0x87, 0xd3, 0xff, 0xff, 0xff, //0x00000367 ja           LBB0_20
	0x48, 0x89, 0xc8, //0x0000036d movq         %rcx, %rax
	0x49, 0x89, 0xf7, //0x00000370 movq         %rsi, %r15
	//0x00000373 LBB0_23
	0x48, 0x85, 0xc0, //0x00000373 testq        %rax, %rax
	0x0f, 0x84, 0x3b, 0x00, 0x00, 0x00, //0x00000376 je           LBB0_29
	0x49, 0x8d, 0x0c, 0x07, //0x0000037c leaq         (%r15,%rax), %rcx
	0x49, 0xff, 0xc7, //0x00000380 incq         %r15
	0x48, 0xbe, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00000383 movabsq      $4294977024, %rsi
	//0x0000038d LBB0_25
	0x41, 0x0f, 0xbe, 0x5f, 0xff, //0x0000038d movsbl       $-1(%r15), %ebx
	0x83, 0xfb, 0x20, //0x00000392 cmpl         $32, %ebx
	0x0f, 0x87, 0x77, 0x0c, 0x00, 0x00, //0x00000395 ja           LBB0_199
	0x48, 0x0f, 0xa3, 0xde, //0x0000039b btq          %rbx, %rsi
	0x0f, 0x83, 0x6d, 0x0c, 0x00, 0x00, //0x0000039f jae          LBB0_199
	0x48, 0xff, 0xc8, //0x000003a5 decq         %rax
	0x49, 0xff, 0xc7, //0x000003a8 incq         %r15
	0x48, 0x85, 0xc0, //0x000003ab testq        %rax, %rax
	0x0f, 0x85, 0xd9, 0xff, 0xff, 0xff, //0x000003ae jne          LBB0_25
	0x49, 0x89, 0xcf, //0x000003b4 movq         %rcx, %r15
	//0x000003b7 LBB0_29
	0x49, 0x29, 0xff, //0x000003b7 subq         %rdi, %r15
	0x4d, 0x39, 0xdf, //0x000003ba cmpq         %r11, %r15
	0x0f, 0x82, 0x25, 0x00, 0x00, 0x00, //0x000003bd jb           LBB0_32
	0xe9, 0x5c, 0x0c, 0x00, 0x00, //0x000003c3 jmp          LBB0_200
	//0x000003c8 LBB0_30
	0x48, 0x89, 0xca, //0x000003c8 movq         %rcx, %rdx
	0xe9, 0x54, 0x0c, 0x00, 0x00, //0x000003cb jmp          LBB0_200
	//0x000003d0 LBB0_31
	0x49, 0x29, 0xff, //0x000003d0 subq         %rdi, %r15
	0xf7, 0xd3, //0x000003d3 notl         %ebx
	0x48, 0x63, 0xc3, //0x000003d5 movslq       %ebx, %rax
	0x48, 0x0f, 0xbc, 0xc0, //0x000003d8 bsfq         %rax, %rax
	0x49, 0x01, 0xc7, //0x000003dc addq         %rax, %r15
	0x4d, 0x39, 0xdf, //0x000003df cmpq         %r11, %r15
	0x0f, 0x83, 0x3c, 0x0c, 0x00, 0x00, //0x000003e2 jae          LBB0_200
	//0x000003e8 LBB0_32
	0x42, 0x8a, 0x04, 0x3f, //0x000003e8 movb         (%rdi,%r15), %al
	//0x000003ec LBB0_33
	0x0f, 0xbe, 0xc8, //0x000003ec movsbl       %al, %ecx
	0x83, 0xf9, 0x7d, //0x000003ef cmpl         $125, %ecx
	0x0f, 0x87, 0x48, 0x07, 0x00, 0x00, //0x000003f2 ja           LBB0_129
	0x49, 0x8d, 0x57, 0x01, //0x000003f8 leaq         $1(%r15), %rdx
	0x4e, 0x8d, 0x0c, 0x3f, //0x000003fc leaq         (%rdi,%r15), %r9
	0x48, 0x8d, 0x35, 0xa9, 0x32, 0x00, 0x00, //0x00000400 leaq         $12969(%rip), %rsi  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x0c, 0x8e, //0x00000407 movslq       (%rsi,%rcx,4), %rcx
	0x48, 0x01, 0xf1, //0x0000040b addq         %rsi, %rcx
	0xff, 0xe1, //0x0000040e jmpq         *%rcx
	//0x00000410 LBB0_35
	0x41, 0xf6, 0xc0, 0x02, //0x00000410 testb        $2, %r8b
	0x48, 0x89, 0x7d, 0xc8, //0x00000414 movq         %rdi, $-56(%rbp)
	0x0f, 0x85, 0x70, 0x00, 0x00, 0x00, //0x00000418 jne          LBB0_42
	0x4d, 0x8b, 0x66, 0x20, //0x0000041e movq         $32(%r14), %r12
	0x4d, 0x8b, 0x6e, 0x28, //0x00000422 movq         $40(%r14), %r13
	0x49, 0xc7, 0x06, 0x09, 0x00, 0x00, 0x00, //0x00000426 movq         $9, (%r14)
	0xc5, 0xf9, 0xef, 0xc0, //0x0000042d vpxor        %xmm0, %xmm0, %xmm0
	0xc4, 0xc1, 0x7a, 0x7f, 0x46, 0x08, //0x00000431 vmovdqu      %xmm0, $8(%r14)
	0x4d, 0x89, 0x7e, 0x18, //0x00000437 movq         %r15, $24(%r14)
	0x4d, 0x39, 0xdf, //0x0000043b cmpq         %r11, %r15
	0x0f, 0x83, 0x00, 0x0d, 0x00, 0x00, //0x0000043e jae          LBB0_221
	0x41, 0x8a, 0x31, //0x00000444 movb         (%r9), %sil
	0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x00000447 movl         $1, %r8d
	0x4c, 0x89, 0xff, //0x0000044d movq         %r15, %rdi
	0x40, 0x80, 0xfe, 0x2d, //0x00000450 cmpb         $45, %sil
	0x0f, 0x85, 0x1a, 0x00, 0x00, 0x00, //0x00000454 jne          LBB0_40
	0x4c, 0x39, 0xda, //0x0000045a cmpq         %r11, %rdx
	0x0f, 0x83, 0xe1, 0x0c, 0x00, 0x00, //0x0000045d jae          LBB0_221
	0x48, 0x8b, 0x45, 0xc8, //0x00000463 movq         $-56(%rbp), %rax
	0x40, 0x8a, 0x34, 0x10, //0x00000467 movb         (%rax,%rdx), %sil
	0x41, 0xb8, 0xff, 0xff, 0xff, 0xff, //0x0000046b movl         $-1, %r8d
	0x48, 0x89, 0xd7, //0x00000471 movq         %rdx, %rdi
	//0x00000474 LBB0_40
	0x8d, 0x46, 0xd0, //0x00000474 leal         $-48(%rsi), %eax
	0x3c, 0x0a, //0x00000477 cmpb         $10, %al
	0x0f, 0x82, 0x31, 0x04, 0x00, 0x00, //0x00000479 jb           LBB0_98
	0x49, 0xc7, 0x06, 0xfe, 0xff, 0xff, 0xff, //0x0000047f movq         $-2, (%r14)
	0x48, 0x89, 0xfb, //0x00000486 movq         %rdi, %rbx
	0xe9, 0xa0, 0x0b, 0x00, 0x00, //0x00000489 jmp          LBB0_202
	//0x0000048e LBB0_42
	0x4d, 0x29, 0xfb, //0x0000048e subq         %r15, %r11
	0x31, 0xc9, //0x00000491 xorl         %ecx, %ecx
	0x3c, 0x2d, //0x00000493 cmpb         $45, %al
	0x0f, 0x94, 0xc1, //0x00000495 sete         %cl
	0x49, 0x01, 0xc9, //0x00000498 addq         %rcx, %r9
	0x49, 0x29, 0xcb, //0x0000049b subq         %rcx, %r11
	0x0f, 0x84, 0x7f, 0x29, 0x00, 0x00, //0x0000049e je           LBB0_655
	0x4c, 0x89, 0x75, 0xb8, //0x000004a4 movq         %r14, $-72(%rbp)
	0x41, 0x8a, 0x01, //0x000004a8 movb         (%r9), %al
	0x8d, 0x48, 0xd0, //0x000004ab leal         $-48(%rax), %ecx
	0x80, 0xf9, 0x09, //0x000004ae cmpb         $9, %cl
	0x0f, 0x87, 0xc0, 0x27, 0x00, 0x00, //0x000004b1 ja           LBB0_249
	0x3c, 0x30, //0x000004b7 cmpb         $48, %al
	0x0f, 0x85, 0x34, 0x00, 0x00, 0x00, //0x000004b9 jne          LBB0_48
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x000004bf movl         $1, %ebx
	0x49, 0x83, 0xfb, 0x01, //0x000004c4 cmpq         $1, %r11
	0x0f, 0x84, 0x6d, 0x0d, 0x00, 0x00, //0x000004c8 je           LBB0_246
	0x41, 0x8a, 0x41, 0x01, //0x000004ce movb         $1(%r9), %al
	0x04, 0xd2, //0x000004d2 addb         $-46, %al
	0x3c, 0x37, //0x000004d4 cmpb         $55, %al
	0x0f, 0x87, 0x5f, 0x0d, 0x00, 0x00, //0x000004d6 ja           LBB0_246
	0x0f, 0xb6, 0xc0, //0x000004dc movzbl       %al, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x000004df movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x000004e9 btq          %rax, %rcx
	0x0f, 0x83, 0x48, 0x0d, 0x00, 0x00, //0x000004ed jae          LBB0_246
	//0x000004f3 LBB0_48
	0x49, 0x83, 0xfb, 0x20, //0x000004f3 cmpq         $32, %r11
	0x0f, 0x82, 0x32, 0x29, 0x00, 0x00, //0x000004f7 jb           LBB0_656
	0x49, 0x8d, 0x4b, 0xe0, //0x000004fd leaq         $-32(%r11), %rcx
	0x48, 0x89, 0xc8, //0x00000501 movq         %rcx, %rax
	0x48, 0x83, 0xe0, 0xe0, //0x00000504 andq         $-32, %rax
	0x4e, 0x8d, 0x6c, 0x08, 0x20, //0x00000508 leaq         $32(%rax,%r9), %r13
	0x83, 0xe1, 0x1f, //0x0000050d andl         $31, %ecx
	0x48, 0x89, 0x4d, 0xc0, //0x00000510 movq         %rcx, $-64(%rbp)
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000514 movq         $-1, %r8
	0xc5, 0xfe, 0x6f, 0x05, 0x5d, 0xfb, 0xff, 0xff, //0x0000051b vmovdqu      $-1187(%rip), %ymm0  /* LCPI0_4+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0x75, 0xfb, 0xff, 0xff, //0x00000523 vmovdqu      $-1163(%rip), %ymm1  /* LCPI0_5+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x15, 0x8d, 0xfb, 0xff, 0xff, //0x0000052b vmovdqu      $-1139(%rip), %ymm2  /* LCPI0_6+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0xa5, 0xfb, 0xff, 0xff, //0x00000533 vmovdqu      $-1115(%rip), %ymm3  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x1d, 0xfb, 0xff, 0xff, //0x0000053b vmovdqu      $-1251(%rip), %ymm4  /* LCPI0_3+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x2d, 0xb5, 0xfb, 0xff, 0xff, //0x00000543 vmovdqu      $-1099(%rip), %ymm5  /* LCPI0_8+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0xcd, 0xfb, 0xff, 0xff, //0x0000054b vmovdqu      $-1075(%rip), %ymm6  /* LCPI0_9+0(%rip) */
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00000553 movq         $-1, %r14
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x0000055a movq         $-1, %r10
	0x4c, 0x89, 0xc8, //0x00000561 movq         %r9, %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000564 .p2align 4, 0x90
	//0x00000570 LBB0_50
	0xc5, 0xfe, 0x6f, 0x38, //0x00000570 vmovdqu      (%rax), %ymm7
	0xc5, 0x45, 0x64, 0xc0, //0x00000574 vpcmpgtb     %ymm0, %ymm7, %ymm8
	0xc5, 0x75, 0x64, 0xcf, //0x00000578 vpcmpgtb     %ymm7, %ymm1, %ymm9
	0xc4, 0x41, 0x3d, 0xdb, 0xc1, //0x0000057c vpand        %ymm9, %ymm8, %ymm8
	0xc5, 0x45, 0x74, 0xca, //0x00000581 vpcmpeqb     %ymm2, %ymm7, %ymm9
	0xc5, 0x45, 0x74, 0xd3, //0x00000585 vpcmpeqb     %ymm3, %ymm7, %ymm10
	0xc4, 0x41, 0x2d, 0xeb, 0xc9, //0x00000589 vpor         %ymm9, %ymm10, %ymm9
	0xc5, 0x45, 0xeb, 0xd4, //0x0000058e vpor         %ymm4, %ymm7, %ymm10
	0xc5, 0x2d, 0x74, 0xd6, //0x00000592 vpcmpeqb     %ymm6, %ymm10, %ymm10
	0xc5, 0xc5, 0x74, 0xfd, //0x00000596 vpcmpeqb     %ymm5, %ymm7, %ymm7
	0xc5, 0xfd, 0xd7, 0xdf, //0x0000059a vpmovmskb    %ymm7, %ebx
	0xc4, 0x41, 0x7d, 0xd7, 0xe2, //0x0000059e vpmovmskb    %ymm10, %r12d
	0xc4, 0xc1, 0x7d, 0xd7, 0xf1, //0x000005a3 vpmovmskb    %ymm9, %esi
	0xc5, 0xad, 0xeb, 0xff, //0x000005a8 vpor         %ymm7, %ymm10, %ymm7
	0xc4, 0x41, 0x35, 0xeb, 0xc0, //0x000005ac vpor         %ymm8, %ymm9, %ymm8
	0xc5, 0xbd, 0xeb, 0xff, //0x000005b1 vpor         %ymm7, %ymm8, %ymm7
	0xc5, 0xfd, 0xd7, 0xcf, //0x000005b5 vpmovmskb    %ymm7, %ecx
	0x48, 0xf7, 0xd1, //0x000005b9 notq         %rcx
	0x48, 0x0f, 0xbc, 0xc9, //0x000005bc bsfq         %rcx, %rcx
	0x83, 0xf9, 0x20, //0x000005c0 cmpl         $32, %ecx
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x000005c3 je           LBB0_52
	0xbf, 0xff, 0xff, 0xff, 0xff, //0x000005c9 movl         $-1, %edi
	0xd3, 0xe7, //0x000005ce shll         %cl, %edi
	0xf7, 0xd7, //0x000005d0 notl         %edi
	0x21, 0xfb, //0x000005d2 andl         %edi, %ebx
	0x41, 0x21, 0xfc, //0x000005d4 andl         %edi, %r12d
	0x21, 0xf7, //0x000005d7 andl         %esi, %edi
	0x89, 0xfe, //0x000005d9 movl         %edi, %esi
	//0x000005db LBB0_52
	0x8d, 0x7b, 0xff, //0x000005db leal         $-1(%rbx), %edi
	0x21, 0xdf, //0x000005de andl         %ebx, %edi
	0x0f, 0x85, 0x02, 0x0a, 0x00, 0x00, //0x000005e0 jne          LBB0_197
	0x41, 0x8d, 0x7c, 0x24, 0xff, //0x000005e6 leal         $-1(%r12), %edi
	0x44, 0x21, 0xe7, //0x000005eb andl         %r12d, %edi
	0x0f, 0x85, 0xf4, 0x09, 0x00, 0x00, //0x000005ee jne          LBB0_197
	0x8d, 0x7e, 0xff, //0x000005f4 leal         $-1(%rsi), %edi
	0x21, 0xf7, //0x000005f7 andl         %esi, %edi
	0x0f, 0x85, 0xe9, 0x09, 0x00, 0x00, //0x000005f9 jne          LBB0_197
	0x85, 0xdb, //0x000005ff testl        %ebx, %ebx
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x00000601 je           LBB0_58
	0x48, 0x89, 0xc7, //0x00000607 movq         %rax, %rdi
	0x4c, 0x29, 0xcf, //0x0000060a subq         %r9, %rdi
	0x0f, 0xbc, 0xdb, //0x0000060d bsfl         %ebx, %ebx
	0x48, 0x01, 0xfb, //0x00000610 addq         %rdi, %rbx
	0x49, 0x83, 0xfa, 0xff, //0x00000613 cmpq         $-1, %r10
	0x0f, 0x85, 0x12, 0x0c, 0x00, 0x00, //0x00000617 jne          LBB0_244
	0x49, 0x89, 0xda, //0x0000061d movq         %rbx, %r10
	//0x00000620 LBB0_58
	0x45, 0x85, 0xe4, //0x00000620 testl        %r12d, %r12d
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00000623 je           LBB0_61
	0x48, 0x89, 0xc7, //0x00000629 movq         %rax, %rdi
	0x4c, 0x29, 0xcf, //0x0000062c subq         %r9, %rdi
	0x41, 0x0f, 0xbc, 0xdc, //0x0000062f bsfl         %r12d, %ebx
	0x48, 0x01, 0xfb, //0x00000633 addq         %rdi, %rbx
	0x49, 0x83, 0xfe, 0xff, //0x00000636 cmpq         $-1, %r14
	0x0f, 0x85, 0xef, 0x0b, 0x00, 0x00, //0x0000063a jne          LBB0_244
	0x49, 0x89, 0xde, //0x00000640 movq         %rbx, %r14
	//0x00000643 LBB0_61
	0x85, 0xf6, //0x00000643 testl        %esi, %esi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x00000645 je           LBB0_64
	0x48, 0x89, 0xc7, //0x0000064b movq         %rax, %rdi
	0x4c, 0x29, 0xcf, //0x0000064e subq         %r9, %rdi
	0x0f, 0xbc, 0xde, //0x00000651 bsfl         %esi, %ebx
	0x48, 0x01, 0xfb, //0x00000654 addq         %rdi, %rbx
	0x49, 0x83, 0xf8, 0xff, //0x00000657 cmpq         $-1, %r8
	0x0f, 0x85, 0xce, 0x0b, 0x00, 0x00, //0x0000065b jne          LBB0_244
	0x49, 0x89, 0xd8, //0x00000661 movq         %rbx, %r8
	//0x00000664 LBB0_64
	0x83, 0xf9, 0x20, //0x00000664 cmpl         $32, %ecx
	0x0f, 0x85, 0xb3, 0x01, 0x00, 0x00, //0x00000667 jne          LBB0_87
	0x48, 0x83, 0xc0, 0x20, //0x0000066d addq         $32, %rax
	0x49, 0x83, 0xc3, 0xe0, //0x00000671 addq         $-32, %r11
	0x49, 0x83, 0xfb, 0x1f, //0x00000675 cmpq         $31, %r11
	0x0f, 0x87, 0xf1, 0xfe, 0xff, 0xff, //0x00000679 ja           LBB0_50
	0xc5, 0xf8, 0x77, //0x0000067f vzeroupper   
	0x4c, 0x8b, 0x5d, 0xc0, //0x00000682 movq         $-64(%rbp), %r11
	0x49, 0x83, 0xfb, 0x10, //0x00000686 cmpq         $16, %r11
	0x0f, 0x82, 0x76, 0x01, 0x00, 0x00, //0x0000068a jb           LBB0_85
	//0x00000690 LBB0_67
	0xbe, 0xff, 0xff, 0xff, 0xff, //0x00000690 movl         $4294967295, %esi
	0x49, 0x8d, 0x4b, 0xf0, //0x00000695 leaq         $-16(%r11), %rcx
	0x48, 0x89, 0xc8, //0x00000699 movq         %rcx, %rax
	0x48, 0x83, 0xe0, 0xf0, //0x0000069c andq         $-16, %rax
	0x4a, 0x8d, 0x44, 0x28, 0x10, //0x000006a0 leaq         $16(%rax,%r13), %rax
	0x48, 0x89, 0x45, 0xb0, //0x000006a5 movq         %rax, $-80(%rbp)
	0x83, 0xe1, 0x0f, //0x000006a9 andl         $15, %ecx
	0x48, 0x89, 0x4d, 0xc0, //0x000006ac movq         %rcx, $-64(%rbp)
	0xc5, 0x7a, 0x6f, 0x05, 0xa8, 0xfa, 0xff, 0xff, //0x000006b0 vmovdqu      $-1368(%rip), %xmm8  /* LCPI0_10+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x0d, 0xb0, 0xfa, 0xff, 0xff, //0x000006b8 vmovdqu      $-1360(%rip), %xmm9  /* LCPI0_11+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x15, 0xb8, 0xfa, 0xff, 0xff, //0x000006c0 vmovdqu      $-1352(%rip), %xmm10  /* LCPI0_12+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x1d, 0xc0, 0xfa, 0xff, 0xff, //0x000006c8 vmovdqu      $-1344(%rip), %xmm11  /* LCPI0_13+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x25, 0xc8, 0xfa, 0xff, 0xff, //0x000006d0 vmovdqu      $-1336(%rip), %xmm4  /* LCPI0_14+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x2d, 0xd0, 0xfa, 0xff, 0xff, //0x000006d8 vmovdqu      $-1328(%rip), %xmm5  /* LCPI0_15+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x35, 0xd8, 0xfa, 0xff, 0xff, //0x000006e0 vmovdqu      $-1320(%rip), %xmm6  /* LCPI0_16+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000006e8 .p2align 4, 0x90
	//0x000006f0 LBB0_68
	0xc4, 0xc1, 0x7a, 0x6f, 0x7d, 0x00, //0x000006f0 vmovdqu      (%r13), %xmm7
	0xc4, 0xc1, 0x41, 0x64, 0xc0, //0x000006f6 vpcmpgtb     %xmm8, %xmm7, %xmm0
	0xc5, 0xb1, 0x64, 0xcf, //0x000006fb vpcmpgtb     %xmm7, %xmm9, %xmm1
	0xc5, 0xf9, 0xdb, 0xc1, //0x000006ff vpand        %xmm1, %xmm0, %xmm0
	0xc5, 0xa9, 0x74, 0xcf, //0x00000703 vpcmpeqb     %xmm7, %xmm10, %xmm1
	0xc5, 0xa1, 0x74, 0xd7, //0x00000707 vpcmpeqb     %xmm7, %xmm11, %xmm2
	0xc5, 0xe9, 0xeb, 0xc9, //0x0000070b vpor         %xmm1, %xmm2, %xmm1
	0xc5, 0xc1, 0xeb, 0xd4, //0x0000070f vpor         %xmm4, %xmm7, %xmm2
	0xc5, 0xe9, 0x74, 0xd6, //0x00000713 vpcmpeqb     %xmm6, %xmm2, %xmm2
	0xc5, 0xc1, 0x74, 0xfd, //0x00000717 vpcmpeqb     %xmm5, %xmm7, %xmm7
	0xc5, 0xe9, 0xeb, 0xdf, //0x0000071b vpor         %xmm7, %xmm2, %xmm3
	0xc5, 0xf1, 0xeb, 0xc0, //0x0000071f vpor         %xmm0, %xmm1, %xmm0
	0xc5, 0xe1, 0xeb, 0xc0, //0x00000723 vpor         %xmm0, %xmm3, %xmm0
	0xc5, 0xf9, 0xd7, 0xdf, //0x00000727 vpmovmskb    %xmm7, %ebx
	0xc5, 0xf9, 0xd7, 0xfa, //0x0000072b vpmovmskb    %xmm2, %edi
	0xc5, 0x79, 0xd7, 0xe1, //0x0000072f vpmovmskb    %xmm1, %r12d
	0xc5, 0xf9, 0xd7, 0xc0, //0x00000733 vpmovmskb    %xmm0, %eax
	0x48, 0x31, 0xf0, //0x00000737 xorq         %rsi, %rax
	0x48, 0x0f, 0xbc, 0xc8, //0x0000073a bsfq         %rax, %rcx
	0x83, 0xf9, 0x10, //0x0000073e cmpl         $16, %ecx
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00000741 je           LBB0_70
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00000747 movl         $-1, %eax
	0xd3, 0xe0, //0x0000074c shll         %cl, %eax
	0xf7, 0xd0, //0x0000074e notl         %eax
	0x21, 0xc3, //0x00000750 andl         %eax, %ebx
	0x21, 0xc7, //0x00000752 andl         %eax, %edi
	0x44, 0x21, 0xe0, //0x00000754 andl         %r12d, %eax
	0x41, 0x89, 0xc4, //0x00000757 movl         %eax, %r12d
	//0x0000075a LBB0_70
	0x8d, 0x43, 0xff, //0x0000075a leal         $-1(%rbx), %eax
	0x21, 0xd8, //0x0000075d andl         %ebx, %eax
	0x0f, 0x85, 0xc1, 0x0a, 0x00, 0x00, //0x0000075f jne          LBB0_238
	0x8d, 0x47, 0xff, //0x00000765 leal         $-1(%rdi), %eax
	0x21, 0xf8, //0x00000768 andl         %edi, %eax
	0x0f, 0x85, 0xb6, 0x0a, 0x00, 0x00, //0x0000076a jne          LBB0_238
	0x41, 0x8d, 0x44, 0x24, 0xff, //0x00000770 leal         $-1(%r12), %eax
	0x44, 0x21, 0xe0, //0x00000775 andl         %r12d, %eax
	0x0f, 0x85, 0xa8, 0x0a, 0x00, 0x00, //0x00000778 jne          LBB0_238
	0x85, 0xdb, //0x0000077e testl        %ebx, %ebx
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x00000780 je           LBB0_76
	0x4c, 0x89, 0xe8, //0x00000786 movq         %r13, %rax
	0x4c, 0x29, 0xc8, //0x00000789 subq         %r9, %rax
	0x0f, 0xbc, 0xdb, //0x0000078c bsfl         %ebx, %ebx
	0x48, 0x01, 0xc3, //0x0000078f addq         %rax, %rbx
	0x49, 0x83, 0xfa, 0xff, //0x00000792 cmpq         $-1, %r10
	0x0f, 0x85, 0x93, 0x0a, 0x00, 0x00, //0x00000796 jne          LBB0_244
	0x49, 0x89, 0xda, //0x0000079c movq         %rbx, %r10
	//0x0000079f LBB0_76
	0x85, 0xff, //0x0000079f testl        %edi, %edi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x000007a1 je           LBB0_79
	0x4c, 0x89, 0xe8, //0x000007a7 movq         %r13, %rax
	0x4c, 0x29, 0xc8, //0x000007aa subq         %r9, %rax
	0x0f, 0xbc, 0xdf, //0x000007ad bsfl         %edi, %ebx
	0x48, 0x01, 0xc3, //0x000007b0 addq         %rax, %rbx
	0x49, 0x83, 0xfe, 0xff, //0x000007b3 cmpq         $-1, %r14
	0x0f, 0x85, 0x72, 0x0a, 0x00, 0x00, //0x000007b7 jne          LBB0_244
	0x49, 0x89, 0xde, //0x000007bd movq         %rbx, %r14
	//0x000007c0 LBB0_79
	0x45, 0x85, 0xe4, //0x000007c0 testl        %r12d, %r12d
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x000007c3 je           LBB0_82
	0x4c, 0x89, 0xe8, //0x000007c9 movq         %r13, %rax
	0x4c, 0x29, 0xc8, //0x000007cc subq         %r9, %rax
	0x41, 0x0f, 0xbc, 0xdc, //0x000007cf bsfl         %r12d, %ebx
	0x48, 0x01, 0xc3, //0x000007d3 addq         %rax, %rbx
	0x49, 0x83, 0xf8, 0xff, //0x000007d6 cmpq         $-1, %r8
	0x0f, 0x85, 0x4f, 0x0a, 0x00, 0x00, //0x000007da jne          LBB0_244
	0x49, 0x89, 0xd8, //0x000007e0 movq         %rbx, %r8
	//0x000007e3 LBB0_82
	0x83, 0xf9, 0x10, //0x000007e3 cmpl         $16, %ecx
	0x0f, 0x85, 0x12, 0x04, 0x00, 0x00, //0x000007e6 jne          LBB0_141
	0x49, 0x83, 0xc5, 0x10, //0x000007ec addq         $16, %r13
	0x49, 0x83, 0xc3, 0xf0, //0x000007f0 addq         $-16, %r11
	0x49, 0x83, 0xfb, 0x0f, //0x000007f4 cmpq         $15, %r11
	0x0f, 0x87, 0xf2, 0xfe, 0xff, 0xff, //0x000007f8 ja           LBB0_68
	0x4c, 0x8b, 0x5d, 0xc0, //0x000007fe movq         $-64(%rbp), %r11
	0x4c, 0x8b, 0x6d, 0xb0, //0x00000802 movq         $-80(%rbp), %r13
	//0x00000806 LBB0_85
	0x4d, 0x85, 0xdb, //0x00000806 testq        %r11, %r11
	0x0f, 0x84, 0xf2, 0x03, 0x00, 0x00, //0x00000809 je           LBB0_142
	0x4b, 0x8d, 0x44, 0x1d, 0x00, //0x0000080f leaq         (%r13,%r11), %rax
	0x48, 0x8d, 0x0d, 0x8d, 0x30, 0x00, 0x00, //0x00000814 leaq         $12429(%rip), %rcx  /* LJTI0_1+0(%rip) */
	0xe9, 0x3c, 0x00, 0x00, 0x00, //0x0000081b jmp          LBB0_92
	//0x00000820 LBB0_87
	0x48, 0x01, 0xc8, //0x00000820 addq         %rcx, %rax
	0xc5, 0xf8, 0x77, //0x00000823 vzeroupper   
	//0x00000826 LBB0_88
	0x49, 0x89, 0xc5, //0x00000826 movq         %rax, %r13
	0xe9, 0xd3, 0x03, 0x00, 0x00, //0x00000829 jmp          LBB0_142
	//0x0000082e LBB0_89
	0x48, 0x89, 0xf3, //0x0000082e movq         %rsi, %rbx
	0x4c, 0x29, 0xcb, //0x00000831 subq         %r9, %rbx
	0x49, 0x83, 0xfa, 0xff, //0x00000834 cmpq         $-1, %r10
	0x0f, 0x85, 0x27, 0x24, 0x00, 0x00, //0x00000838 jne          LBB0_660
	0x48, 0xff, 0xcb, //0x0000083e decq         %rbx
	0x49, 0x89, 0xda, //0x00000841 movq         %rbx, %r10
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000844 .p2align 4, 0x90
	//0x00000850 LBB0_91
	0x49, 0x89, 0xf5, //0x00000850 movq         %rsi, %r13
	0x49, 0xff, 0xcb, //0x00000853 decq         %r11
	0x0f, 0x84, 0xca, 0xff, 0xff, 0xff, //0x00000856 je           LBB0_88
	//0x0000085c LBB0_92
	0x41, 0x0f, 0xbe, 0x7d, 0x00, //0x0000085c movsbl       (%r13), %edi
	0x83, 0xc7, 0xd5, //0x00000861 addl         $-43, %edi
	0x83, 0xff, 0x3a, //0x00000864 cmpl         $58, %edi
	0x0f, 0x87, 0x94, 0x03, 0x00, 0x00, //0x00000867 ja           LBB0_142
	0x49, 0x8d, 0x75, 0x01, //0x0000086d leaq         $1(%r13), %rsi
	0x48, 0x63, 0x3c, 0xb9, //0x00000871 movslq       (%rcx,%rdi,4), %rdi
	0x48, 0x01, 0xcf, //0x00000875 addq         %rcx, %rdi
	0xff, 0xe7, //0x00000878 jmpq         *%rdi
	//0x0000087a LBB0_94
	0x48, 0x89, 0xf3, //0x0000087a movq         %rsi, %rbx
	0x4c, 0x29, 0xcb, //0x0000087d subq         %r9, %rbx
	0x49, 0x83, 0xf8, 0xff, //0x00000880 cmpq         $-1, %r8
	0x0f, 0x85, 0xdb, 0x23, 0x00, 0x00, //0x00000884 jne          LBB0_660
	0x48, 0xff, 0xcb, //0x0000088a decq         %rbx
	0x49, 0x89, 0xd8, //0x0000088d movq         %rbx, %r8
	0xe9, 0xbb, 0xff, 0xff, 0xff, //0x00000890 jmp          LBB0_91
	//0x00000895 LBB0_96
	0x48, 0x89, 0xf3, //0x00000895 movq         %rsi, %rbx
	0x4c, 0x29, 0xcb, //0x00000898 subq         %r9, %rbx
	0x49, 0x83, 0xfe, 0xff, //0x0000089b cmpq         $-1, %r14
	0x0f, 0x85, 0xc0, 0x23, 0x00, 0x00, //0x0000089f jne          LBB0_660
	0x48, 0xff, 0xcb, //0x000008a5 decq         %rbx
	0x49, 0x89, 0xde, //0x000008a8 movq         %rbx, %r14
	0xe9, 0xa0, 0xff, 0xff, 0xff, //0x000008ab jmp          LBB0_91
	//0x000008b0 LBB0_98
	0x40, 0x80, 0xfe, 0x30, //0x000008b0 cmpb         $48, %sil
	0x0f, 0x85, 0x35, 0x00, 0x00, 0x00, //0x000008b4 jne          LBB0_102
	0x48, 0x8d, 0x5f, 0x01, //0x000008ba leaq         $1(%rdi), %rbx
	0x4c, 0x39, 0xdf, //0x000008be cmpq         %r11, %rdi
	0x0f, 0x83, 0x67, 0x07, 0x00, 0x00, //0x000008c1 jae          LBB0_202
	0x48, 0x8b, 0x45, 0xc8, //0x000008c7 movq         $-56(%rbp), %rax
	0x8a, 0x04, 0x18, //0x000008cb movb         (%rax,%rbx), %al
	0x04, 0xd2, //0x000008ce addb         $-46, %al
	0x3c, 0x37, //0x000008d0 cmpb         $55, %al
	0x0f, 0x87, 0x56, 0x07, 0x00, 0x00, //0x000008d2 ja           LBB0_202
	0x0f, 0xb6, 0xc0, //0x000008d8 movzbl       %al, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x000008db movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x000008e5 btq          %rax, %rcx
	0x0f, 0x83, 0x3f, 0x07, 0x00, 0x00, //0x000008e9 jae          LBB0_202
	//0x000008ef LBB0_102
	0x4c, 0x39, 0xdf, //0x000008ef cmpq         %r11, %rdi
	0x0f, 0x83, 0x4f, 0x05, 0x00, 0x00, //0x000008f2 jae          LBB0_168
	0x48, 0xff, 0xc7, //0x000008f8 incq         %rdi
	0x31, 0xc9, //0x000008fb xorl         %ecx, %ecx
	0x48, 0x89, 0xfb, //0x000008fd movq         %rdi, %rbx
	0x31, 0xc0, //0x00000900 xorl         %eax, %eax
	0x45, 0x31, 0xd2, //0x00000902 xorl         %r10d, %r10d
	//0x00000905 LBB0_104
	0x83, 0xf8, 0x12, //0x00000905 cmpl         $18, %eax
	0x0f, 0x8f, 0x14, 0x00, 0x00, 0x00, //0x00000908 jg           LBB0_106
	0x40, 0x0f, 0xb6, 0xd6, //0x0000090e movzbl       %sil, %edx
	0x4b, 0x8d, 0x34, 0x92, //0x00000912 leaq         (%r10,%r10,4), %rsi
	0x4c, 0x8d, 0x54, 0x72, 0xd0, //0x00000916 leaq         $-48(%rdx,%rsi,2), %r10
	0xff, 0xc0, //0x0000091b incl         %eax
	0xe9, 0x02, 0x00, 0x00, 0x00, //0x0000091d jmp          LBB0_107
	//0x00000922 LBB0_106
	0xff, 0xc1, //0x00000922 incl         %ecx
	//0x00000924 LBB0_107
	0x49, 0x39, 0xdb, //0x00000924 cmpq         %rbx, %r11
	0x0f, 0x84, 0x2c, 0x06, 0x00, 0x00, //0x00000927 je           LBB0_185
	0x48, 0x8b, 0x55, 0xc8, //0x0000092d movq         $-56(%rbp), %rdx
	0x0f, 0xb6, 0x34, 0x1a, //0x00000931 movzbl       (%rdx,%rbx), %esi
	0x8d, 0x56, 0xd0, //0x00000935 leal         $-48(%rsi), %edx
	0x48, 0xff, 0xc3, //0x00000938 incq         %rbx
	0x80, 0xfa, 0x0a, //0x0000093b cmpb         $10, %dl
	0x0f, 0x82, 0xc1, 0xff, 0xff, 0xff, //0x0000093e jb           LBB0_104
	0x31, 0xd2, //0x00000944 xorl         %edx, %edx
	0x85, 0xc9, //0x00000946 testl        %ecx, %ecx
	0x0f, 0x9f, 0xc2, //0x00000948 setg         %dl
	0x40, 0x80, 0xfe, 0x2e, //0x0000094b cmpb         $46, %sil
	0x0f, 0x85, 0xad, 0x06, 0x00, 0x00, //0x0000094f jne          LBB0_198
	0x49, 0xc7, 0x06, 0x08, 0x00, 0x00, 0x00, //0x00000955 movq         $8, (%r14)
	0x4c, 0x39, 0xdb, //0x0000095c cmpq         %r11, %rbx
	0x0f, 0x83, 0xdf, 0x07, 0x00, 0x00, //0x0000095f jae          LBB0_221
	0x89, 0x55, 0xc0, //0x00000965 movl         %edx, $-64(%rbp)
	0x48, 0x89, 0xde, //0x00000968 movq         %rbx, %rsi
	0x48, 0x8b, 0x55, 0xc8, //0x0000096b movq         $-56(%rbp), %rdx
	0x8a, 0x1c, 0x1a, //0x0000096f movb         (%rdx,%rbx), %bl
	0x80, 0xc3, 0xd0, //0x00000972 addb         $-48, %bl
	0xba, 0x08, 0x00, 0x00, 0x00, //0x00000975 movl         $8, %edx
	0x80, 0xfb, 0x0a, //0x0000097a cmpb         $10, %bl
	0x0f, 0x82, 0x69, 0x09, 0x00, 0x00, //0x0000097d jb           LBB0_259
	0x49, 0xc7, 0x06, 0xfe, 0xff, 0xff, 0xff, //0x00000983 movq         $-2, (%r14)
	0x48, 0x89, 0xf3, //0x0000098a movq         %rsi, %rbx
	0xe9, 0x9c, 0x06, 0x00, 0x00, //0x0000098d jmp          LBB0_202
	//0x00000992 LBB0_113
	0x4c, 0x89, 0xf8, //0x00000992 movq         %r15, %rax
	0x48, 0xf7, 0xd0, //0x00000995 notq         %rax
	0x41, 0xf6, 0xc0, 0x20, //0x00000998 testb        $32, %r8b
	0x0f, 0x85, 0xe1, 0x02, 0x00, 0x00, //0x0000099c jne          LBB0_152
	0x4c, 0x39, 0xda, //0x000009a2 cmpq         %r11, %rdx
	0x0f, 0x84, 0xad, 0x2c, 0x00, 0x00, //0x000009a5 je           LBB0_722
	0x49, 0x89, 0xfa, //0x000009ab movq         %rdi, %r10
	0x4d, 0x89, 0xdd, //0x000009ae movq         %r11, %r13
	0x49, 0x29, 0xd5, //0x000009b1 subq         %rdx, %r13
	0x49, 0x8d, 0x0c, 0x12, //0x000009b4 leaq         (%r10,%rdx), %rcx
	0x49, 0x83, 0xfd, 0x40, //0x000009b8 cmpq         $64, %r13
	0x0f, 0x82, 0xd9, 0x29, 0x00, 0x00, //0x000009bc jb           LBB0_723
	0x4c, 0x89, 0xd6, //0x000009c2 movq         %r10, %rsi
	0x45, 0x89, 0xec, //0x000009c5 movl         %r13d, %r12d
	0x41, 0x83, 0xe4, 0x3f, //0x000009c8 andl         $63, %r12d
	0x49, 0x8d, 0x44, 0x03, 0xc0, //0x000009cc leaq         $-64(%r11,%rax), %rax
	0x48, 0x83, 0xe0, 0xc0, //0x000009d1 andq         $-64, %rax
	0x49, 0x01, 0xc7, //0x000009d5 addq         %rax, %r15
	0x4f, 0x8d, 0x4c, 0x3a, 0x41, //0x000009d8 leaq         $65(%r10,%r15), %r9
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000009dd movq         $-1, %r8
	0x45, 0x31, 0xff, //0x000009e4 xorl         %r15d, %r15d
	0xc5, 0xfe, 0x6f, 0x05, 0x31, 0xf6, 0xff, 0xff, //0x000009e7 vmovdqu      $-2511(%rip), %ymm0  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0x49, 0xf6, 0xff, 0xff, //0x000009ef vmovdqu      $-2487(%rip), %ymm1  /* LCPI0_2+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000009f7 .p2align 4, 0x90
	//0x00000a00 LBB0_117
	0xc5, 0xfe, 0x6f, 0x11, //0x00000a00 vmovdqu      (%rcx), %ymm2
	0xc5, 0xfe, 0x6f, 0x59, 0x20, //0x00000a04 vmovdqu      $32(%rcx), %ymm3
	0xc5, 0xed, 0x74, 0xe0, //0x00000a09 vpcmpeqb     %ymm0, %ymm2, %ymm4
	0xc5, 0xfd, 0xd7, 0xf4, //0x00000a0d vpmovmskb    %ymm4, %esi
	0xc5, 0xe5, 0x74, 0xe0, //0x00000a11 vpcmpeqb     %ymm0, %ymm3, %ymm4
	0xc5, 0xfd, 0xd7, 0xc4, //0x00000a15 vpmovmskb    %ymm4, %eax
	0xc5, 0xed, 0x74, 0xd1, //0x00000a19 vpcmpeqb     %ymm1, %ymm2, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00000a1d vpmovmskb    %ymm2, %edi
	0xc5, 0xe5, 0x74, 0xd1, //0x00000a21 vpcmpeqb     %ymm1, %ymm3, %ymm2
	0xc5, 0xfd, 0xd7, 0xda, //0x00000a25 vpmovmskb    %ymm2, %ebx
	0x48, 0xc1, 0xe0, 0x20, //0x00000a29 shlq         $32, %rax
	0x48, 0xc1, 0xe3, 0x20, //0x00000a2d shlq         $32, %rbx
	0x48, 0x09, 0xdf, //0x00000a31 orq          %rbx, %rdi
	0x49, 0x83, 0xf8, 0xff, //0x00000a34 cmpq         $-1, %r8
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00000a38 jne          LBB0_119
	0x48, 0x85, 0xff, //0x00000a3e testq        %rdi, %rdi
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x00000a41 jne          LBB0_122
	//0x00000a47 LBB0_119
	0x48, 0x09, 0xf0, //0x00000a47 orq          %rsi, %rax
	0x48, 0x89, 0xfe, //0x00000a4a movq         %rdi, %rsi
	0x4c, 0x09, 0xfe, //0x00000a4d orq          %r15, %rsi
	0x0f, 0x85, 0x3c, 0x00, 0x00, 0x00, //0x00000a50 jne          LBB0_123
	//0x00000a56 LBB0_120
	0x48, 0x85, 0xc0, //0x00000a56 testq        %rax, %rax
	0x0f, 0x85, 0xe1, 0x08, 0x00, 0x00, //0x00000a59 jne          LBB0_264
	//0x00000a5f LBB0_121
	0x49, 0x83, 0xc5, 0xc0, //0x00000a5f addq         $-64, %r13
	0x48, 0x83, 0xc1, 0x40, //0x00000a63 addq         $64, %rcx
	0x49, 0x83, 0xfd, 0x3f, //0x00000a67 cmpq         $63, %r13
	0x0f, 0x87, 0x8f, 0xff, 0xff, 0xff, //0x00000a6b ja           LBB0_117
	0xe9, 0x7e, 0x08, 0x00, 0x00, //0x00000a71 jmp          LBB0_260
	//0x00000a76 LBB0_122
	0x48, 0x89, 0xcb, //0x00000a76 movq         %rcx, %rbx
	0x4c, 0x29, 0xd3, //0x00000a79 subq         %r10, %rbx
	0x4c, 0x0f, 0xbc, 0xc7, //0x00000a7c bsfq         %rdi, %r8
	0x49, 0x01, 0xd8, //0x00000a80 addq         %rbx, %r8
	0x48, 0x09, 0xf0, //0x00000a83 orq          %rsi, %rax
	0x48, 0x89, 0xfe, //0x00000a86 movq         %rdi, %rsi
	0x4c, 0x09, 0xfe, //0x00000a89 orq          %r15, %rsi
	0x0f, 0x84, 0xc4, 0xff, 0xff, 0xff, //0x00000a8c je           LBB0_120
	//0x00000a92 LBB0_123
	0x4c, 0x89, 0xfe, //0x00000a92 movq         %r15, %rsi
	0x48, 0xf7, 0xd6, //0x00000a95 notq         %rsi
	0x48, 0x21, 0xfe, //0x00000a98 andq         %rdi, %rsi
	0x48, 0x8d, 0x1c, 0x36, //0x00000a9b leaq         (%rsi,%rsi), %rbx
	0x4c, 0x09, 0xfb, //0x00000a9f orq          %r15, %rbx
	0x48, 0x89, 0x5d, 0xc0, //0x00000aa2 movq         %rbx, $-64(%rbp)
	0x48, 0xf7, 0xd3, //0x00000aa6 notq         %rbx
	0x48, 0x21, 0xfb, //0x00000aa9 andq         %rdi, %rbx
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000aac movabsq      $-6148914691236517206, %rdi
	0x48, 0x21, 0xfb, //0x00000ab6 andq         %rdi, %rbx
	0x45, 0x31, 0xff, //0x00000ab9 xorl         %r15d, %r15d
	0x48, 0x01, 0xf3, //0x00000abc addq         %rsi, %rbx
	0x41, 0x0f, 0x92, 0xc7, //0x00000abf setb         %r15b
	0x48, 0x01, 0xdb, //0x00000ac3 addq         %rbx, %rbx
	0x48, 0xbe, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000ac6 movabsq      $6148914691236517205, %rsi
	0x48, 0x31, 0xf3, //0x00000ad0 xorq         %rsi, %rbx
	0x48, 0x23, 0x5d, 0xc0, //0x00000ad3 andq         $-64(%rbp), %rbx
	0x48, 0xf7, 0xd3, //0x00000ad7 notq         %rbx
	0x48, 0x21, 0xd8, //0x00000ada andq         %rbx, %rax
	0x48, 0x85, 0xc0, //0x00000add testq        %rax, %rax
	0x0f, 0x84, 0x79, 0xff, 0xff, 0xff, //0x00000ae0 je           LBB0_121
	0xe9, 0x55, 0x08, 0x00, 0x00, //0x00000ae6 jmp          LBB0_264
	//0x00000aeb LBB0_124
	0x31, 0xc0, //0x00000aeb xorl         %eax, %eax
	0x45, 0x85, 0xc0, //0x00000aed testl        %r8d, %r8d
	0x0f, 0x99, 0xc0, //0x00000af0 setns        %al
	0xb9, 0x0b, 0x00, 0x00, 0x00, //0x00000af3 movl         $11, %ecx
	0xe9, 0xdf, 0x00, 0x00, 0x00, //0x00000af8 jmp          LBB0_139
	//0x00000afd LBB0_125
	0x49, 0x8d, 0x4b, 0xfd, //0x00000afd leaq         $-3(%r11), %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000b01 movq         $-1, %rax
	0x49, 0x39, 0xcf, //0x00000b08 cmpq         %rcx, %r15
	0x0f, 0x83, 0xcc, 0x04, 0x00, 0x00, //0x00000b0b jae          LBB0_243
	0x41, 0x8b, 0x09, //0x00000b11 movl         (%r9), %ecx
	0x81, 0xf9, 0x6e, 0x75, 0x6c, 0x6c, //0x00000b14 cmpl         $1819047278, %ecx
	0x0f, 0x85, 0x33, 0x03, 0x00, 0x00, //0x00000b1a jne          LBB0_169
	0x49, 0x83, 0xc7, 0x04, //0x00000b20 addq         $4, %r15
	0xb8, 0x02, 0x00, 0x00, 0x00, //0x00000b24 movl         $2, %eax
	0xe9, 0xac, 0x04, 0x00, 0x00, //0x00000b29 jmp          LBB0_242
	//0x00000b2e LBB0_128
	0x31, 0xc0, //0x00000b2e xorl         %eax, %eax
	0x45, 0x85, 0xc0, //0x00000b30 testl        %r8d, %r8d
	0x0f, 0x99, 0xc0, //0x00000b33 setns        %al
	0xb9, 0x0d, 0x00, 0x00, 0x00, //0x00000b36 movl         $13, %ecx
	0xe9, 0x9c, 0x00, 0x00, 0x00, //0x00000b3b jmp          LBB0_139
	//0x00000b40 LBB0_129
	0x49, 0xc7, 0x06, 0xfe, 0xff, 0xff, 0xff, //0x00000b40 movq         $-2, (%r14)
	0x4c, 0x89, 0xfb, //0x00000b47 movq         %r15, %rbx
	0xe9, 0xdf, 0x04, 0x00, 0x00, //0x00000b4a jmp          LBB0_202
	//0x00000b4f LBB0_130
	0x31, 0xc0, //0x00000b4f xorl         %eax, %eax
	0x45, 0x85, 0xc0, //0x00000b51 testl        %r8d, %r8d
	0x0f, 0x99, 0xc0, //0x00000b54 setns        %al
	0xb9, 0x0a, 0x00, 0x00, 0x00, //0x00000b57 movl         $10, %ecx
	0xe9, 0x7b, 0x00, 0x00, 0x00, //0x00000b5c jmp          LBB0_139
	//0x00000b61 LBB0_131
	0x49, 0xc7, 0x06, 0x05, 0x00, 0x00, 0x00, //0x00000b61 movq         $5, (%r14)
	0xe9, 0xbe, 0x04, 0x00, 0x00, //0x00000b68 jmp          LBB0_201
	//0x00000b6d LBB0_132
	0x49, 0x8d, 0x4b, 0xfc, //0x00000b6d leaq         $-4(%r11), %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000b71 movq         $-1, %rax
	0x49, 0x39, 0xcf, //0x00000b78 cmpq         %rcx, %r15
	0x0f, 0x83, 0x5c, 0x04, 0x00, 0x00, //0x00000b7b jae          LBB0_243
	0x8b, 0x0c, 0x17, //0x00000b81 movl         (%rdi,%rdx), %ecx
	0x81, 0xf9, 0x61, 0x6c, 0x73, 0x65, //0x00000b84 cmpl         $1702063201, %ecx
	0x0f, 0x85, 0x02, 0x03, 0x00, 0x00, //0x00000b8a jne          LBB0_173
	0x49, 0x83, 0xc7, 0x05, //0x00000b90 addq         $5, %r15
	0xb8, 0x04, 0x00, 0x00, 0x00, //0x00000b94 movl         $4, %eax
	0xe9, 0x3c, 0x04, 0x00, 0x00, //0x00000b99 jmp          LBB0_242
	//0x00000b9e LBB0_135
	0x49, 0x8d, 0x4b, 0xfd, //0x00000b9e leaq         $-3(%r11), %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000ba2 movq         $-1, %rax
	0x49, 0x39, 0xcf, //0x00000ba9 cmpq         %rcx, %r15
	0x0f, 0x83, 0x2b, 0x04, 0x00, 0x00, //0x00000bac jae          LBB0_243
	0x41, 0x8b, 0x09, //0x00000bb2 movl         (%r9), %ecx
	0x81, 0xf9, 0x74, 0x72, 0x75, 0x65, //0x00000bb5 cmpl         $1702195828, %ecx
	0x0f, 0x85, 0x10, 0x03, 0x00, 0x00, //0x00000bbb jne          LBB0_177
	0x49, 0x83, 0xc7, 0x04, //0x00000bc1 addq         $4, %r15
	0xb8, 0x03, 0x00, 0x00, 0x00, //0x00000bc5 movl         $3, %eax
	0xe9, 0x0b, 0x04, 0x00, 0x00, //0x00000bca jmp          LBB0_242
	//0x00000bcf LBB0_138
	0x31, 0xc0, //0x00000bcf xorl         %eax, %eax
	0x45, 0x85, 0xc0, //0x00000bd1 testl        %r8d, %r8d
	0x0f, 0x99, 0xc0, //0x00000bd4 setns        %al
	0xb9, 0x0c, 0x00, 0x00, 0x00, //0x00000bd7 movl         $12, %ecx
	//0x00000bdc LBB0_139
	0x48, 0xc7, 0xc6, 0xfe, 0xff, 0xff, 0xff, //0x00000bdc movq         $-2, %rsi
	0x48, 0x0f, 0x48, 0xf1, //0x00000be3 cmovsq       %rcx, %rsi
	0x49, 0x89, 0x36, //0x00000be7 movq         %rsi, (%r14)
	0x48, 0x29, 0xc2, //0x00000bea subq         %rax, %rdx
	0xe9, 0x39, 0x04, 0x00, 0x00, //0x00000bed jmp          LBB0_201
	//0x00000bf2 LBB0_140
	0x49, 0xc7, 0x06, 0x06, 0x00, 0x00, 0x00, //0x00000bf2 movq         $6, (%r14)
	0xe9, 0x2d, 0x04, 0x00, 0x00, //0x00000bf9 jmp          LBB0_201
	//0x00000bfe LBB0_141
	0x49, 0x01, 0xcd, //0x00000bfe addq         %rcx, %r13
	//0x00000c01 LBB0_142
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00000c01 movq         $-1, %rbx
	0x4d, 0x85, 0xf6, //0x00000c08 testq        %r14, %r14
	0x0f, 0x84, 0x60, 0x20, 0x00, 0x00, //0x00000c0b je           LBB0_248
	0x4d, 0x85, 0xc0, //0x00000c11 testq        %r8, %r8
	0x0f, 0x84, 0x57, 0x20, 0x00, 0x00, //0x00000c14 je           LBB0_248
	0x4d, 0x85, 0xd2, //0x00000c1a testq        %r10, %r10
	0x0f, 0x84, 0x4e, 0x20, 0x00, 0x00, //0x00000c1d je           LBB0_248
	0x4d, 0x29, 0xcd, //0x00000c23 subq         %r9, %r13
	0x49, 0x8d, 0x45, 0xff, //0x00000c26 leaq         $-1(%r13), %rax
	0x49, 0x39, 0xc6, //0x00000c2a cmpq         %rax, %r14
	0x0f, 0x84, 0x3c, 0x00, 0x00, 0x00, //0x00000c2d je           LBB0_151
	0x49, 0x39, 0xc2, //0x00000c33 cmpq         %rax, %r10
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x00000c36 je           LBB0_151
	0x49, 0x39, 0xc0, //0x00000c3c cmpq         %rax, %r8
	0x0f, 0x84, 0x2a, 0x00, 0x00, 0x00, //0x00000c3f je           LBB0_151
	0x4d, 0x85, 0xc0, //0x00000c45 testq        %r8, %r8
	0x0f, 0x8e, 0xcd, 0x01, 0x00, 0x00, //0x00000c48 jle          LBB0_165
	0x49, 0x8d, 0x40, 0xff, //0x00000c4e leaq         $-1(%r8), %rax
	0x49, 0x39, 0xc6, //0x00000c52 cmpq         %rax, %r14
	0x0f, 0x84, 0xc0, 0x01, 0x00, 0x00, //0x00000c55 je           LBB0_165
	0x49, 0xf7, 0xd0, //0x00000c5b notq         %r8
	0x4c, 0x89, 0xc3, //0x00000c5e movq         %r8, %rbx
	0x48, 0x85, 0xdb, //0x00000c61 testq        %rbx, %rbx
	0x0f, 0x89, 0xd1, 0x05, 0x00, 0x00, //0x00000c64 jns          LBB0_246
	0xe9, 0x02, 0x20, 0x00, 0x00, //0x00000c6a jmp          LBB0_248
	//0x00000c6f LBB0_151
	0x49, 0xf7, 0xdd, //0x00000c6f negq         %r13
	0x4c, 0x89, 0xeb, //0x00000c72 movq         %r13, %rbx
	0x48, 0x85, 0xdb, //0x00000c75 testq        %rbx, %rbx
	0x0f, 0x89, 0xbd, 0x05, 0x00, 0x00, //0x00000c78 jns          LBB0_246
	0xe9, 0xee, 0x1f, 0x00, 0x00, //0x00000c7e jmp          LBB0_248
	//0x00000c83 LBB0_152
	0x48, 0x89, 0x7d, 0xc8, //0x00000c83 movq         %rdi, $-56(%rbp)
	0x4c, 0x39, 0xda, //0x00000c87 cmpq         %r11, %rdx
	0x0f, 0x84, 0xc8, 0x29, 0x00, 0x00, //0x00000c8a je           LBB0_722
	0x4c, 0x89, 0x75, 0xb8, //0x00000c90 movq         %r14, $-72(%rbp)
	0x4d, 0x89, 0xdd, //0x00000c94 movq         %r11, %r13
	0x49, 0x29, 0xd5, //0x00000c97 subq         %rdx, %r13
	0x48, 0x8b, 0x4d, 0xc8, //0x00000c9a movq         $-56(%rbp), %rcx
	0x48, 0x8d, 0x1c, 0x11, //0x00000c9e leaq         (%rcx,%rdx), %rbx
	0x49, 0x83, 0xfd, 0x40, //0x00000ca2 cmpq         $64, %r13
	0x0f, 0x82, 0x0f, 0x27, 0x00, 0x00, //0x00000ca6 jb           LBB0_724
	0x45, 0x89, 0xee, //0x00000cac movl         %r13d, %r14d
	0x41, 0x83, 0xe6, 0x3f, //0x00000caf andl         $63, %r14d
	0x49, 0x8d, 0x44, 0x03, 0xc0, //0x00000cb3 leaq         $-64(%r11,%rax), %rax
	0x48, 0x83, 0xe0, 0xc0, //0x00000cb8 andq         $-64, %rax
	0x49, 0x01, 0xc7, //0x00000cbc addq         %rax, %r15
	0x4a, 0x8d, 0x44, 0x39, 0x41, //0x00000cbf leaq         $65(%rcx,%r15), %rax
	0x48, 0x89, 0x45, 0xc0, //0x00000cc4 movq         %rax, $-64(%rbp)
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000cc8 movq         $-1, %r8
	0x45, 0x31, 0xe4, //0x00000ccf xorl         %r12d, %r12d
	0xc5, 0xfe, 0x6f, 0x05, 0x46, 0xf3, 0xff, 0xff, //0x00000cd2 vmovdqu      $-3258(%rip), %ymm0  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0x5e, 0xf3, 0xff, 0xff, //0x00000cda vmovdqu      $-3234(%rip), %ymm1  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x15, 0x76, 0xf3, 0xff, 0xff, //0x00000ce2 vmovdqu      $-3210(%rip), %ymm2  /* LCPI0_3+0(%rip) */
	0xc5, 0xe5, 0x76, 0xdb, //0x00000cea vpcmpeqd     %ymm3, %ymm3, %ymm3
	0x90, 0x90, //0x00000cee .p2align 4, 0x90
	//0x00000cf0 LBB0_155
	0xc5, 0xfe, 0x6f, 0x23, //0x00000cf0 vmovdqu      (%rbx), %ymm4
	0xc5, 0xfe, 0x6f, 0x6b, 0x20, //0x00000cf4 vmovdqu      $32(%rbx), %ymm5
	0xc5, 0xdd, 0x74, 0xf0, //0x00000cf9 vpcmpeqb     %ymm0, %ymm4, %ymm6
	0xc5, 0x7d, 0xd7, 0xce, //0x00000cfd vpmovmskb    %ymm6, %r9d
	0xc5, 0xd5, 0x74, 0xf0, //0x00000d01 vpcmpeqb     %ymm0, %ymm5, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x00000d05 vpmovmskb    %ymm6, %eax
	0xc5, 0xdd, 0x74, 0xf1, //0x00000d09 vpcmpeqb     %ymm1, %ymm4, %ymm6
	0xc5, 0x7d, 0xd7, 0xd6, //0x00000d0d vpmovmskb    %ymm6, %r10d
	0xc5, 0xd5, 0x74, 0xf1, //0x00000d11 vpcmpeqb     %ymm1, %ymm5, %ymm6
	0xc5, 0x7d, 0xd7, 0xfe, //0x00000d15 vpmovmskb    %ymm6, %r15d
	0xc5, 0xed, 0x64, 0xf4, //0x00000d19 vpcmpgtb     %ymm4, %ymm2, %ymm6
	0xc5, 0xdd, 0x64, 0xe3, //0x00000d1d vpcmpgtb     %ymm3, %ymm4, %ymm4
	0xc5, 0xdd, 0xdb, 0xe6, //0x00000d21 vpand        %ymm6, %ymm4, %ymm4
	0xc5, 0xfd, 0xd7, 0xcc, //0x00000d25 vpmovmskb    %ymm4, %ecx
	0xc5, 0xed, 0x64, 0xe5, //0x00000d29 vpcmpgtb     %ymm5, %ymm2, %ymm4
	0xc5, 0xd5, 0x64, 0xeb, //0x00000d2d vpcmpgtb     %ymm3, %ymm5, %ymm5
	0xc5, 0xd5, 0xdb, 0xe4, //0x00000d31 vpand        %ymm4, %ymm5, %ymm4
	0xc5, 0xfd, 0xd7, 0xf4, //0x00000d35 vpmovmskb    %ymm4, %esi
	0x48, 0xc1, 0xe0, 0x20, //0x00000d39 shlq         $32, %rax
	0x49, 0xc1, 0xe7, 0x20, //0x00000d3d shlq         $32, %r15
	0x4d, 0x09, 0xfa, //0x00000d41 orq          %r15, %r10
	0x49, 0x83, 0xf8, 0xff, //0x00000d44 cmpq         $-1, %r8
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00000d48 jne          LBB0_157
	0x4d, 0x85, 0xd2, //0x00000d4e testq        %r10, %r10
	0x0f, 0x85, 0x9b, 0x00, 0x00, 0x00, //0x00000d51 jne          LBB0_162
	//0x00000d57 LBB0_157
	0x48, 0xc1, 0xe6, 0x20, //0x00000d57 shlq         $32, %rsi
	0x4c, 0x09, 0xc8, //0x00000d5b orq          %r9, %rax
	0x4c, 0x89, 0xd7, //0x00000d5e movq         %r10, %rdi
	0x4c, 0x09, 0xe7, //0x00000d61 orq          %r12, %rdi
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x00000d64 jne          LBB0_161
	0x48, 0x09, 0xce, //0x00000d6a orq          %rcx, %rsi
	0x48, 0x85, 0xc0, //0x00000d6d testq        %rax, %rax
	0x0f, 0x85, 0x8f, 0x00, 0x00, 0x00, //0x00000d70 jne          LBB0_163
	//0x00000d76 LBB0_159
	0x48, 0x85, 0xf6, //0x00000d76 testq        %rsi, %rsi
	0x0f, 0x85, 0x86, 0x28, 0x00, 0x00, //0x00000d79 jne          LBB0_760
	0x49, 0x83, 0xc5, 0xc0, //0x00000d7f addq         $-64, %r13
	0x48, 0x83, 0xc3, 0x40, //0x00000d83 addq         $64, %rbx
	0x49, 0x83, 0xfd, 0x3f, //0x00000d87 cmpq         $63, %r13
	0x0f, 0x87, 0x5f, 0xff, 0xff, 0xff, //0x00000d8b ja           LBB0_155
	0xe9, 0xe1, 0x05, 0x00, 0x00, //0x00000d91 jmp          LBB0_267
	//0x00000d96 LBB0_161
	0x4d, 0x89, 0xe7, //0x00000d96 movq         %r12, %r15
	0x49, 0xf7, 0xd7, //0x00000d99 notq         %r15
	0x4d, 0x21, 0xd7, //0x00000d9c andq         %r10, %r15
	0x4f, 0x8d, 0x0c, 0x3f, //0x00000d9f leaq         (%r15,%r15), %r9
	0x4d, 0x09, 0xe1, //0x00000da3 orq          %r12, %r9
	0x4c, 0x89, 0x4d, 0xb0, //0x00000da6 movq         %r9, $-80(%rbp)
	0x49, 0xf7, 0xd1, //0x00000daa notq         %r9
	0x4d, 0x21, 0xd1, //0x00000dad andq         %r10, %r9
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000db0 movabsq      $-6148914691236517206, %rdi
	0x49, 0x21, 0xf9, //0x00000dba andq         %rdi, %r9
	0x45, 0x31, 0xe4, //0x00000dbd xorl         %r12d, %r12d
	0x4d, 0x01, 0xf9, //0x00000dc0 addq         %r15, %r9
	0x41, 0x0f, 0x92, 0xc4, //0x00000dc3 setb         %r12b
	0x4d, 0x01, 0xc9, //0x00000dc7 addq         %r9, %r9
	0x48, 0xbf, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000dca movabsq      $6148914691236517205, %rdi
	0x49, 0x31, 0xf9, //0x00000dd4 xorq         %rdi, %r9
	0x4c, 0x23, 0x4d, 0xb0, //0x00000dd7 andq         $-80(%rbp), %r9
	0x49, 0xf7, 0xd1, //0x00000ddb notq         %r9
	0x4c, 0x21, 0xc8, //0x00000dde andq         %r9, %rax
	0x48, 0x09, 0xce, //0x00000de1 orq          %rcx, %rsi
	0x48, 0x85, 0xc0, //0x00000de4 testq        %rax, %rax
	0x0f, 0x84, 0x89, 0xff, 0xff, 0xff, //0x00000de7 je           LBB0_159
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x00000ded jmp          LBB0_163
	//0x00000df2 LBB0_162
	0x49, 0x89, 0xdf, //0x00000df2 movq         %rbx, %r15
	0x4c, 0x2b, 0x7d, 0xc8, //0x00000df5 subq         $-56(%rbp), %r15
	0x4d, 0x0f, 0xbc, 0xc2, //0x00000df9 bsfq         %r10, %r8
	0x4d, 0x01, 0xf8, //0x00000dfd addq         %r15, %r8
	0xe9, 0x52, 0xff, 0xff, 0xff, //0x00000e00 jmp          LBB0_157
	//0x00000e05 LBB0_163
	0x48, 0x0f, 0xbc, 0xc0, //0x00000e05 bsfq         %rax, %rax
	0x48, 0x85, 0xf6, //0x00000e09 testq        %rsi, %rsi
	0x0f, 0x84, 0x24, 0x01, 0x00, 0x00, //0x00000e0c je           LBB0_182
	0x48, 0x0f, 0xbc, 0xce, //0x00000e12 bsfq         %rsi, %rcx
	0xe9, 0x20, 0x01, 0x00, 0x00, //0x00000e16 jmp          LBB0_183
	//0x00000e1b LBB0_165
	0x4c, 0x89, 0xd0, //0x00000e1b movq         %r10, %rax
	0x4c, 0x09, 0xf0, //0x00000e1e orq          %r14, %rax
	0x4d, 0x39, 0xf2, //0x00000e21 cmpq         %r14, %r10
	0x0f, 0x8c, 0xe6, 0x00, 0x00, 0x00, //0x00000e24 jl           LBB0_181
	0x48, 0x85, 0xc0, //0x00000e2a testq        %rax, %rax
	0x0f, 0x88, 0xdd, 0x00, 0x00, 0x00, //0x00000e2d js           LBB0_181
	0x49, 0xf7, 0xd2, //0x00000e33 notq         %r10
	0x4c, 0x89, 0xd3, //0x00000e36 movq         %r10, %rbx
	0x48, 0x85, 0xdb, //0x00000e39 testq        %rbx, %rbx
	0x0f, 0x89, 0xf9, 0x03, 0x00, 0x00, //0x00000e3c jns          LBB0_246
	0xe9, 0x2a, 0x1e, 0x00, 0x00, //0x00000e42 jmp          LBB0_248
	//0x00000e47 LBB0_168
	0x31, 0xc9, //0x00000e47 xorl         %ecx, %ecx
	0x31, 0xc0, //0x00000e49 xorl         %eax, %eax
	0x45, 0x31, 0xd2, //0x00000e4b xorl         %r10d, %r10d
	0xe9, 0x09, 0x01, 0x00, 0x00, //0x00000e4e jmp          LBB0_186
	//0x00000e53 LBB0_169
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000e53 movq         $-2, %rax
	0x80, 0xf9, 0x6e, //0x00000e5a cmpb         $110, %cl
	0x0f, 0x85, 0x77, 0x01, 0x00, 0x00, //0x00000e5d jne          LBB0_242
	0x42, 0x80, 0x7c, 0x3f, 0x01, 0x75, //0x00000e63 cmpb         $117, $1(%rdi,%r15)
	0x0f, 0x85, 0x5f, 0x01, 0x00, 0x00, //0x00000e69 jne          LBB0_196
	0x42, 0x80, 0x7c, 0x3f, 0x02, 0x6c, //0x00000e6f cmpb         $108, $2(%rdi,%r15)
	0x0f, 0x85, 0x5b, 0x01, 0x00, 0x00, //0x00000e75 jne          LBB0_241
	0x4d, 0x8d, 0x5f, 0x03, //0x00000e7b leaq         $3(%r15), %r11
	0x49, 0x8d, 0x4f, 0x04, //0x00000e7f leaq         $4(%r15), %rcx
	0x42, 0x80, 0x7c, 0x3f, 0x03, 0x6c, //0x00000e83 cmpb         $108, $3(%rdi,%r15)
	0x4c, 0x0f, 0x44, 0xd9, //0x00000e89 cmoveq       %rcx, %r11
	0xe9, 0x4b, 0x01, 0x00, 0x00, //0x00000e8d jmp          LBB0_243
	//0x00000e92 LBB0_173
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000e92 movq         $-2, %rax
	0x80, 0xf9, 0x61, //0x00000e99 cmpb         $97, %cl
	0x0f, 0x85, 0x24, 0x01, 0x00, 0x00, //0x00000e9c jne          LBB0_195
	0x42, 0x80, 0x7c, 0x3f, 0x02, 0x6c, //0x00000ea2 cmpb         $108, $2(%rdi,%r15)
	0x0f, 0x85, 0x28, 0x01, 0x00, 0x00, //0x00000ea8 jne          LBB0_241
	0x42, 0x80, 0x7c, 0x3f, 0x03, 0x73, //0x00000eae cmpb         $115, $3(%rdi,%r15)
	0x0f, 0x85, 0xb4, 0x03, 0x00, 0x00, //0x00000eb4 jne          LBB0_240
	0x4d, 0x8d, 0x5f, 0x04, //0x00000eba leaq         $4(%r15), %r11
	0x49, 0x8d, 0x4f, 0x05, //0x00000ebe leaq         $5(%r15), %rcx
	0x42, 0x80, 0x7c, 0x3f, 0x04, 0x65, //0x00000ec2 cmpb         $101, $4(%rdi,%r15)
	0x4c, 0x0f, 0x44, 0xd9, //0x00000ec8 cmoveq       %rcx, %r11
	0xe9, 0x0c, 0x01, 0x00, 0x00, //0x00000ecc jmp          LBB0_243
	//0x00000ed1 LBB0_177
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000ed1 movq         $-2, %rax
	0x80, 0xf9, 0x74, //0x00000ed8 cmpb         $116, %cl
	0x0f, 0x85, 0xf9, 0x00, 0x00, 0x00, //0x00000edb jne          LBB0_242
	0x42, 0x80, 0x7c, 0x3f, 0x01, 0x72, //0x00000ee1 cmpb         $114, $1(%rdi,%r15)
	0x0f, 0x85, 0xe1, 0x00, 0x00, 0x00, //0x00000ee7 jne          LBB0_196
	0x42, 0x80, 0x7c, 0x3f, 0x02, 0x75, //0x00000eed cmpb         $117, $2(%rdi,%r15)
	0x0f, 0x85, 0xdd, 0x00, 0x00, 0x00, //0x00000ef3 jne          LBB0_241
	0x4d, 0x8d, 0x5f, 0x03, //0x00000ef9 leaq         $3(%r15), %r11
	0x49, 0x8d, 0x4f, 0x04, //0x00000efd leaq         $4(%r15), %rcx
	0x42, 0x80, 0x7c, 0x3f, 0x03, 0x65, //0x00000f01 cmpb         $101, $3(%rdi,%r15)
	0x4c, 0x0f, 0x44, 0xd9, //0x00000f07 cmoveq       %rcx, %r11
	0xe9, 0xcd, 0x00, 0x00, 0x00, //0x00000f0b jmp          LBB0_243
	//0x00000f10 LBB0_181
	0x48, 0x85, 0xc0, //0x00000f10 testq        %rax, %rax
	0x49, 0x8d, 0x46, 0xff, //0x00000f13 leaq         $-1(%r14), %rax
	0x49, 0xf7, 0xd6, //0x00000f17 notq         %r14
	0x4d, 0x0f, 0x48, 0xf5, //0x00000f1a cmovsq       %r13, %r14
	0x49, 0x39, 0xc2, //0x00000f1e cmpq         %rax, %r10
	0x4d, 0x0f, 0x45, 0xf5, //0x00000f21 cmovneq      %r13, %r14
	0x4c, 0x89, 0xf3, //0x00000f25 movq         %r14, %rbx
	0x48, 0x85, 0xdb, //0x00000f28 testq        %rbx, %rbx
	0x0f, 0x89, 0x0a, 0x03, 0x00, 0x00, //0x00000f2b jns          LBB0_246
	0xe9, 0x3b, 0x1d, 0x00, 0x00, //0x00000f31 jmp          LBB0_248
	//0x00000f36 LBB0_182
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00000f36 movl         $64, %ecx
	//0x00000f3b LBB0_183
	0x4c, 0x8b, 0x75, 0xb8, //0x00000f3b movq         $-72(%rbp), %r14
	0x48, 0x8b, 0x75, 0xc8, //0x00000f3f movq         $-56(%rbp), %rsi
	0x48, 0x39, 0xc1, //0x00000f43 cmpq         %rax, %rcx
	0x0f, 0x82, 0x8f, 0x24, 0x00, 0x00, //0x00000f46 jb           LBB0_725
	0x48, 0x29, 0xf3, //0x00000f4c subq         %rsi, %rbx
	0x48, 0x8d, 0x5c, 0x03, 0x01, //0x00000f4f leaq         $1(%rbx,%rax), %rbx
	0xe9, 0xf3, 0x03, 0x00, 0x00, //0x00000f54 jmp          LBB0_265
	//0x00000f59 LBB0_185
	0x4c, 0x89, 0xdf, //0x00000f59 movq         %r11, %rdi
	//0x00000f5c LBB0_186
	0x31, 0xd2, //0x00000f5c xorl         %edx, %edx
	0x85, 0xc9, //0x00000f5e testl        %ecx, %ecx
	0x0f, 0x9f, 0xc2, //0x00000f60 setg         %dl
	0x89, 0x55, 0xc0, //0x00000f63 movl         %edx, $-64(%rbp)
	0xba, 0x09, 0x00, 0x00, 0x00, //0x00000f66 movl         $9, %edx
	0x48, 0x89, 0xfb, //0x00000f6b movq         %rdi, %rbx
	//0x00000f6e LBB0_187
	0x85, 0xc9, //0x00000f6e testl        %ecx, %ecx
	0x4c, 0x89, 0x75, 0xb8, //0x00000f70 movq         %r14, $-72(%rbp)
	0x0f, 0x85, 0xd0, 0x00, 0x00, 0x00, //0x00000f74 jne          LBB0_204
	0x4d, 0x85, 0xd2, //0x00000f7a testq        %r10, %r10
	0x0f, 0x85, 0xc7, 0x00, 0x00, 0x00, //0x00000f7d jne          LBB0_204
	0x4c, 0x39, 0xdb, //0x00000f83 cmpq         %r11, %rbx
	0x0f, 0x83, 0x2e, 0x00, 0x00, 0x00, //0x00000f86 jae          LBB0_194
	0x41, 0x89, 0xde, //0x00000f8c movl         %ebx, %r14d
	0x45, 0x29, 0xde, //0x00000f8f subl         %r11d, %r14d
	0x31, 0xc0, //0x00000f92 xorl         %eax, %eax
	0x31, 0xc9, //0x00000f94 xorl         %ecx, %ecx
	0x48, 0x8b, 0x75, 0xc8, //0x00000f96 movq         $-56(%rbp), %rsi
	//0x00000f9a LBB0_191
	0x80, 0x3c, 0x1e, 0x30, //0x00000f9a cmpb         $48, (%rsi,%rbx)
	0x0f, 0x85, 0x9f, 0x00, 0x00, 0x00, //0x00000f9e jne          LBB0_203
	0x48, 0xff, 0xc3, //0x00000fa4 incq         %rbx
	0xff, 0xc9, //0x00000fa7 decl         %ecx
	0x49, 0x39, 0xdb, //0x00000fa9 cmpq         %rbx, %r11
	0x0f, 0x85, 0xe8, 0xff, 0xff, 0xff, //0x00000fac jne          LBB0_191
	0x45, 0x31, 0xd2, //0x00000fb2 xorl         %r10d, %r10d
	0xe9, 0xab, 0x01, 0x00, 0x00, //0x00000fb5 jmp          LBB0_224
	//0x00000fba LBB0_194
	0x31, 0xc9, //0x00000fba xorl         %ecx, %ecx
	0x31, 0xc0, //0x00000fbc xorl         %eax, %eax
	0x45, 0x31, 0xd2, //0x00000fbe xorl         %r10d, %r10d
	0xe9, 0x84, 0x00, 0x00, 0x00, //0x00000fc1 jmp          LBB0_204
	//0x00000fc6 LBB0_195
	0x49, 0x89, 0xd3, //0x00000fc6 movq         %rdx, %r11
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x00000fc9 jmp          LBB0_243
	//0x00000fce LBB0_196
	0x49, 0xff, 0xc7, //0x00000fce incq         %r15
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x00000fd1 jmp          LBB0_242
	//0x00000fd6 LBB0_241
	0x49, 0x83, 0xc7, 0x02, //0x00000fd6 addq         $2, %r15
	//0x00000fda LBB0_242
	0x4d, 0x89, 0xfb, //0x00000fda movq         %r15, %r11
	//0x00000fdd LBB0_243
	0x49, 0x89, 0x06, //0x00000fdd movq         %rax, (%r14)
	0x4c, 0x89, 0xdb, //0x00000fe0 movq         %r11, %rbx
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x00000fe3 jmp          LBB0_202
	//0x00000fe8 LBB0_197
	0x4c, 0x29, 0xc8, //0x00000fe8 subq         %r9, %rax
	0x0f, 0xbc, 0xdf, //0x00000feb bsfl         %edi, %ebx
	0x48, 0x01, 0xc3, //0x00000fee addq         %rax, %rbx
	0x48, 0xf7, 0xd3, //0x00000ff1 notq         %rbx
	0x48, 0x85, 0xdb, //0x00000ff4 testq        %rbx, %rbx
	0x0f, 0x89, 0x3e, 0x02, 0x00, 0x00, //0x00000ff7 jns          LBB0_246
	0xe9, 0x6f, 0x1c, 0x00, 0x00, //0x00000ffd jmp          LBB0_248
	//0x00001002 LBB0_198
	0x89, 0x55, 0xc0, //0x00001002 movl         %edx, $-64(%rbp)
	0x48, 0xff, 0xcb, //0x00001005 decq         %rbx
	0xba, 0x09, 0x00, 0x00, 0x00, //0x00001008 movl         $9, %edx
	0xe9, 0x5c, 0xff, 0xff, 0xff, //0x0000100d jmp          LBB0_187
	//0x00001012 LBB0_199
	0x48, 0x89, 0xf8, //0x00001012 movq         %rdi, %rax
	0x48, 0xf7, 0xd0, //0x00001015 notq         %rax
	0x49, 0x01, 0xc7, //0x00001018 addq         %rax, %r15
	0x4d, 0x39, 0xdf, //0x0000101b cmpq         %r11, %r15
	0x0f, 0x82, 0xc4, 0xf3, 0xff, 0xff, //0x0000101e jb           LBB0_32
	//0x00001024 LBB0_200
	0x49, 0xc7, 0x06, 0x01, 0x00, 0x00, 0x00, //0x00001024 movq         $1, (%r14)
	//0x0000102b LBB0_201
	0x48, 0x89, 0xd3, //0x0000102b movq         %rdx, %rbx
	//0x0000102e LBB0_202
	0x48, 0x89, 0xd8, //0x0000102e movq         %rbx, %rax
	0x48, 0x83, 0xc4, 0x38, //0x00001031 addq         $56, %rsp
	0x5b, //0x00001035 popq         %rbx
	0x41, 0x5c, //0x00001036 popq         %r12
	0x41, 0x5d, //0x00001038 popq         %r13
	0x41, 0x5e, //0x0000103a popq         %r14
	0x41, 0x5f, //0x0000103c popq         %r15
	0x5d, //0x0000103e popq         %rbp
	0xc5, 0xf8, 0x77, //0x0000103f vzeroupper   
	0xc3, //0x00001042 retq         
	//0x00001043 LBB0_203
	0x45, 0x31, 0xd2, //0x00001043 xorl         %r10d, %r10d
	0x4c, 0x8b, 0x75, 0xb8, //0x00001046 movq         $-72(%rbp), %r14
	//0x0000104a LBB0_204
	0x4c, 0x39, 0xdb, //0x0000104a cmpq         %r11, %rbx
	0x0f, 0x83, 0x40, 0x00, 0x00, 0x00, //0x0000104d jae          LBB0_209
	0x83, 0xf8, 0x12, //0x00001053 cmpl         $18, %eax
	0x0f, 0x8f, 0x37, 0x00, 0x00, 0x00, //0x00001056 jg           LBB0_209
	//0x0000105c LBB0_206
	0x48, 0x8b, 0x75, 0xc8, //0x0000105c movq         $-56(%rbp), %rsi
	0x0f, 0xb6, 0x34, 0x1e, //0x00001060 movzbl       (%rsi,%rbx), %esi
	0x8d, 0x7e, 0xd0, //0x00001064 leal         $-48(%rsi), %edi
	0x40, 0x80, 0xff, 0x09, //0x00001067 cmpb         $9, %dil
	0x0f, 0x87, 0x22, 0x00, 0x00, 0x00, //0x0000106b ja           LBB0_209
	0x4b, 0x8d, 0x3c, 0x92, //0x00001071 leaq         (%r10,%r10,4), %rdi
	0x4c, 0x8d, 0x54, 0x7e, 0xd0, //0x00001075 leaq         $-48(%rsi,%rdi,2), %r10
	0xff, 0xc9, //0x0000107a decl         %ecx
	0x48, 0xff, 0xc3, //0x0000107c incq         %rbx
	0x83, 0xf8, 0x11, //0x0000107f cmpl         $17, %eax
	0x0f, 0x8f, 0x0b, 0x00, 0x00, 0x00, //0x00001082 jg           LBB0_209
	0xff, 0xc0, //0x00001088 incl         %eax
	0x4c, 0x39, 0xdb, //0x0000108a cmpq         %r11, %rbx
	0x0f, 0x82, 0xc9, 0xff, 0xff, 0xff, //0x0000108d jb           LBB0_206
	//0x00001093 LBB0_209
	0x4c, 0x39, 0xdb, //0x00001093 cmpq         %r11, %rbx
	0x0f, 0x83, 0xb7, 0x00, 0x00, 0x00, //0x00001096 jae          LBB0_225
	0x48, 0x8b, 0x45, 0xc8, //0x0000109c movq         $-56(%rbp), %rax
	0x8a, 0x04, 0x18, //0x000010a0 movb         (%rax,%rbx), %al
	0x8d, 0x70, 0xd0, //0x000010a3 leal         $-48(%rax), %esi
	0x40, 0x80, 0xfe, 0x09, //0x000010a6 cmpb         $9, %sil
	0x0f, 0x87, 0x2d, 0x00, 0x00, 0x00, //0x000010aa ja           LBB0_215
	0x49, 0x8d, 0x73, 0xff, //0x000010b0 leaq         $-1(%r11), %rsi
	//0x000010b4 LBB0_212
	0x48, 0x39, 0xde, //0x000010b4 cmpq         %rbx, %rsi
	0x0f, 0x84, 0x9e, 0x00, 0x00, 0x00, //0x000010b7 je           LBB0_223
	0x48, 0x8b, 0x45, 0xc8, //0x000010bd movq         $-56(%rbp), %rax
	0x0f, 0xb6, 0x44, 0x18, 0x01, //0x000010c1 movzbl       $1(%rax,%rbx), %eax
	0x48, 0xff, 0xc3, //0x000010c6 incq         %rbx
	0x8d, 0x78, 0xd0, //0x000010c9 leal         $-48(%rax), %edi
	0x40, 0x80, 0xff, 0x09, //0x000010cc cmpb         $9, %dil
	0x0f, 0x86, 0xde, 0xff, 0xff, 0xff, //0x000010d0 jbe          LBB0_212
	0xc7, 0x45, 0xc0, 0x01, 0x00, 0x00, 0x00, //0x000010d6 movl         $1, $-64(%rbp)
	//0x000010dd LBB0_215
	0x0c, 0x20, //0x000010dd orb          $32, %al
	0x3c, 0x65, //0x000010df cmpb         $101, %al
	0x0f, 0x85, 0x6c, 0x00, 0x00, 0x00, //0x000010e1 jne          LBB0_225
	0x48, 0x8d, 0x73, 0x01, //0x000010e7 leaq         $1(%rbx), %rsi
	0x49, 0xc7, 0x06, 0x08, 0x00, 0x00, 0x00, //0x000010eb movq         $8, (%r14)
	0x4c, 0x39, 0xde, //0x000010f2 cmpq         %r11, %rsi
	0x0f, 0x83, 0x49, 0x00, 0x00, 0x00, //0x000010f5 jae          LBB0_221
	0x44, 0x89, 0x45, 0xb0, //0x000010fb movl         %r8d, $-80(%rbp)
	0x48, 0x8b, 0x45, 0xc8, //0x000010ff movq         $-56(%rbp), %rax
	0x8a, 0x14, 0x30, //0x00001103 movb         (%rax,%rsi), %dl
	0x80, 0xfa, 0x2d, //0x00001106 cmpb         $45, %dl
	0x0f, 0x84, 0x0f, 0x00, 0x00, 0x00, //0x00001109 je           LBB0_219
	0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x0000110f movl         $1, %r8d
	0x80, 0xfa, 0x2b, //0x00001115 cmpb         $43, %dl
	0x0f, 0x85, 0x59, 0x01, 0x00, 0x00, //0x00001118 jne          LBB0_252
	//0x0000111e LBB0_219
	0x48, 0x83, 0xc3, 0x02, //0x0000111e addq         $2, %rbx
	0x4c, 0x39, 0xdb, //0x00001122 cmpq         %r11, %rbx
	0x0f, 0x83, 0x19, 0x00, 0x00, 0x00, //0x00001125 jae          LBB0_221
	0x31, 0xc0, //0x0000112b xorl         %eax, %eax
	0x80, 0xfa, 0x2b, //0x0000112d cmpb         $43, %dl
	0x0f, 0x94, 0xc0, //0x00001130 sete         %al
	0x44, 0x8d, 0x44, 0x00, 0xff, //0x00001133 leal         $-1(%rax,%rax), %r8d
	0x48, 0x8b, 0x45, 0xc8, //0x00001138 movq         $-56(%rbp), %rax
	0x8a, 0x14, 0x18, //0x0000113c movb         (%rax,%rbx), %dl
	0xe9, 0x36, 0x01, 0x00, 0x00, //0x0000113f jmp          LBB0_253
	//0x00001144 LBB0_221
	0x49, 0xc7, 0x06, 0xff, 0xff, 0xff, 0xff, //0x00001144 movq         $-1, (%r14)
	0x4c, 0x89, 0xdb, //0x0000114b movq         %r11, %rbx
	0xe9, 0xdb, 0xfe, 0xff, 0xff, //0x0000114e jmp          LBB0_202
	//0x00001153 LBB0_225
	0x41, 0x89, 0xce, //0x00001153 movl         %ecx, %r14d
	0xe9, 0x0d, 0x00, 0x00, 0x00, //0x00001156 jmp          LBB0_226
	//0x0000115b LBB0_223
	0xc7, 0x45, 0xc0, 0x01, 0x00, 0x00, 0x00, //0x0000115b movl         $1, $-64(%rbp)
	0x41, 0x89, 0xce, //0x00001162 movl         %ecx, %r14d
	//0x00001165 LBB0_224
	0x4c, 0x89, 0xdb, //0x00001165 movq         %r11, %rbx
	//0x00001168 LBB0_226
	0x83, 0xfa, 0x09, //0x00001168 cmpl         $9, %edx
	0x48, 0x89, 0x5d, 0xa0, //0x0000116b movq         %rbx, $-96(%rbp)
	0x0f, 0x85, 0x39, 0x00, 0x00, 0x00, //0x0000116f jne          LBB0_231
	0x45, 0x85, 0xf6, //0x00001175 testl        %r14d, %r14d
	0x0f, 0x85, 0x25, 0x00, 0x00, 0x00, //0x00001178 jne          LBB0_230
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x0000117e movabsq      $-9223372036854775808, %rax
	0x49, 0x63, 0xc8, //0x00001188 movslq       %r8d, %rcx
	0x4d, 0x85, 0xd2, //0x0000118b testq        %r10, %r10
	0x0f, 0x89, 0x53, 0x00, 0x00, 0x00, //0x0000118e jns          LBB0_237
	0x4c, 0x89, 0xd2, //0x00001194 movq         %r10, %rdx
	0x48, 0x21, 0xca, //0x00001197 andq         %rcx, %rdx
	0x48, 0x39, 0xc2, //0x0000119a cmpq         %rax, %rdx
	0x0f, 0x84, 0x44, 0x00, 0x00, 0x00, //0x0000119d je           LBB0_237
	//0x000011a3 LBB0_230
	0x48, 0x8b, 0x45, 0xb8, //0x000011a3 movq         $-72(%rbp), %rax
	0x48, 0xc7, 0x00, 0x08, 0x00, 0x00, 0x00, //0x000011a7 movq         $8, (%rax)
	//0x000011ae LBB0_231
	0x4c, 0x89, 0xd0, //0x000011ae movq         %r10, %rax
	0x48, 0xc1, 0xe8, 0x34, //0x000011b1 shrq         $52, %rax
	0x0f, 0x84, 0x4d, 0x02, 0x00, 0x00, //0x000011b5 je           LBB0_274
	//0x000011bb LBB0_232
	0x44, 0x89, 0x45, 0xb0, //0x000011bb movl         %r8d, $-80(%rbp)
	0x41, 0x8d, 0x86, 0x5c, 0x01, 0x00, 0x00, //0x000011bf leal         $348(%r14), %eax
	0x3d, 0xb7, 0x02, 0x00, 0x00, //0x000011c6 cmpl         $695, %eax
	0x0f, 0x87, 0x28, 0x04, 0x00, 0x00, //0x000011cb ja           LBB0_295
	0x4d, 0x85, 0xd2, //0x000011d1 testq        %r10, %r10
	0x0f, 0x84, 0x1f, 0x03, 0x00, 0x00, //0x000011d4 je           LBB0_286
	//0x000011da LBB0_234
	0x49, 0x0f, 0xbd, 0xca, //0x000011da bsrq         %r10, %rcx
	0x48, 0x83, 0xf1, 0x3f, //0x000011de xorq         $63, %rcx
	0xe9, 0x17, 0x03, 0x00, 0x00, //0x000011e2 jmp          LBB0_287
	//0x000011e7 LBB0_237
	0xc4, 0xc1, 0xf9, 0x6e, 0xc2, //0x000011e7 vmovq        %r10, %xmm0
	0x4c, 0x0f, 0xaf, 0xd1, //0x000011ec imulq        %rcx, %r10
	0x48, 0x8b, 0x55, 0xb8, //0x000011f0 movq         $-72(%rbp), %rdx
	0x4c, 0x89, 0x52, 0x10, //0x000011f4 movq         %r10, $16(%rdx)
	0xc5, 0xf9, 0x62, 0x05, 0xd0, 0xef, 0xff, 0xff, //0x000011f8 vpunpckldq   $-4144(%rip), %xmm0, %xmm0  /* LCPI0_17+0(%rip) */
	0xc5, 0xf9, 0x5c, 0x05, 0xd8, 0xef, 0xff, 0xff, //0x00001200 vsubpd       $-4136(%rip), %xmm0, %xmm0  /* LCPI0_18+0(%rip) */
	0xc4, 0xe3, 0x79, 0x05, 0xc8, 0x01, //0x00001208 vpermilpd    $1, %xmm0, %xmm1
	0xc5, 0xf3, 0x58, 0xc0, //0x0000120e vaddsd       %xmm0, %xmm1, %xmm0
	0x48, 0x21, 0xc8, //0x00001212 andq         %rcx, %rax
	0xc4, 0xe1, 0xf9, 0x7e, 0xc1, //0x00001215 vmovq        %xmm0, %rcx
	0x48, 0x09, 0xc1, //0x0000121a orq          %rax, %rcx
	0x48, 0x89, 0x4a, 0x08, //0x0000121d movq         %rcx, $8(%rdx)
	0xe9, 0x08, 0xfe, 0xff, 0xff, //0x00001221 jmp          LBB0_202
	//0x00001226 LBB0_238
	0x4d, 0x29, 0xcd, //0x00001226 subq         %r9, %r13
	0x0f, 0xbc, 0xd8, //0x00001229 bsfl         %eax, %ebx
	0x4c, 0x01, 0xeb, //0x0000122c addq         %r13, %rbx
	//0x0000122f LBB0_244
	0x48, 0xf7, 0xd3, //0x0000122f notq         %rbx
	0x48, 0x85, 0xdb, //0x00001232 testq        %rbx, %rbx
	0x0f, 0x88, 0x36, 0x1a, 0x00, 0x00, //0x00001235 js           LBB0_248
	//0x0000123b LBB0_246
	0x49, 0x01, 0xd9, //0x0000123b addq         %rbx, %r9
	0x4c, 0x89, 0xcb, //0x0000123e movq         %r9, %rbx
	0x48, 0x2b, 0x5d, 0xc8, //0x00001241 subq         $-56(%rbp), %rbx
	0x48, 0x85, 0xd2, //0x00001245 testq        %rdx, %rdx
	0x0f, 0x8e, 0x14, 0x00, 0x00, 0x00, //0x00001248 jle          LBB0_251
	0x48, 0x8b, 0x45, 0xb8, //0x0000124e movq         $-72(%rbp), %rax
	0x48, 0xc7, 0x00, 0x08, 0x00, 0x00, 0x00, //0x00001252 movq         $8, (%rax)
	0x4c, 0x89, 0x78, 0x18, //0x00001259 movq         %r15, $24(%rax)
	0xe9, 0xcc, 0xfd, 0xff, 0xff, //0x0000125d jmp          LBB0_202
	//0x00001262 LBB0_251
	0x4c, 0x8b, 0x75, 0xb8, //0x00001262 movq         $-72(%rbp), %r14
	0x4d, 0x89, 0x3e, //0x00001266 movq         %r15, (%r14)
	0xe9, 0xc0, 0xfd, 0xff, 0xff, //0x00001269 jmp          LBB0_202
	//0x0000126e LBB0_240
	0x49, 0x83, 0xc7, 0x03, //0x0000126e addq         $3, %r15
	0xe9, 0x63, 0xfd, 0xff, 0xff, //0x00001272 jmp          LBB0_242
	//0x00001277 LBB0_252
	0x48, 0x89, 0xf3, //0x00001277 movq         %rsi, %rbx
	//0x0000127a LBB0_253
	0x8d, 0x72, 0xd0, //0x0000127a leal         $-48(%rdx), %esi
	0x40, 0x80, 0xfe, 0x09, //0x0000127d cmpb         $9, %sil
	0x0f, 0x86, 0x0c, 0x00, 0x00, 0x00, //0x00001281 jbe          LBB0_255
	0x49, 0xc7, 0x06, 0xfe, 0xff, 0xff, 0xff, //0x00001287 movq         $-2, (%r14)
	0xe9, 0x9b, 0xfd, 0xff, 0xff, //0x0000128e jmp          LBB0_202
	//0x00001293 LBB0_255
	0x45, 0x31, 0xf6, //0x00001293 xorl         %r14d, %r14d
	0x4c, 0x39, 0xdb, //0x00001296 cmpq         %r11, %rbx
	0x0f, 0x83, 0x4d, 0x01, 0x00, 0x00, //0x00001299 jae          LBB0_273
	0x49, 0x8d, 0x73, 0xff, //0x0000129f leaq         $-1(%r11), %rsi
	0x45, 0x31, 0xf6, //0x000012a3 xorl         %r14d, %r14d
	//0x000012a6 LBB0_257
	0x44, 0x89, 0xf7, //0x000012a6 movl         %r14d, %edi
	0x41, 0x81, 0xfe, 0x10, 0x27, 0x00, 0x00, //0x000012a9 cmpl         $10000, %r14d
	0x48, 0x89, 0xd8, //0x000012b0 movq         %rbx, %rax
	0x8d, 0x1c, 0xbf, //0x000012b3 leal         (%rdi,%rdi,4), %ebx
	0x0f, 0xb6, 0xd2, //0x000012b6 movzbl       %dl, %edx
	0x44, 0x8d, 0x74, 0x5a, 0xd0, //0x000012b9 leal         $-48(%rdx,%rbx,2), %r14d
	0x44, 0x0f, 0x4d, 0xf7, //0x000012be cmovgel      %edi, %r14d
	0x48, 0x39, 0xc6, //0x000012c2 cmpq         %rax, %rsi
	0x0f, 0x84, 0x1e, 0x01, 0x00, 0x00, //0x000012c5 je           LBB0_272
	0x48, 0x89, 0xc3, //0x000012cb movq         %rax, %rbx
	0x48, 0x8b, 0x45, 0xc8, //0x000012ce movq         $-56(%rbp), %rax
	0x0f, 0xb6, 0x54, 0x18, 0x01, //0x000012d2 movzbl       $1(%rax,%rbx), %edx
	0x48, 0xff, 0xc3, //0x000012d7 incq         %rbx
	0x8d, 0x7a, 0xd0, //0x000012da leal         $-48(%rdx), %edi
	0x40, 0x80, 0xff, 0x0a, //0x000012dd cmpb         $10, %dil
	0x0f, 0x82, 0xbf, 0xff, 0xff, 0xff, //0x000012e1 jb           LBB0_257
	0xe9, 0x00, 0x01, 0x00, 0x00, //0x000012e7 jmp          LBB0_273
	//0x000012ec LBB0_259
	0x48, 0x89, 0xf3, //0x000012ec movq         %rsi, %rbx
	0xe9, 0x7a, 0xfc, 0xff, 0xff, //0x000012ef jmp          LBB0_187
	//0x000012f4 LBB0_260
	0x4c, 0x89, 0xc3, //0x000012f4 movq         %r8, %rbx
	0x4d, 0x89, 0xe5, //0x000012f7 movq         %r12, %r13
	0x4c, 0x89, 0xc9, //0x000012fa movq         %r9, %rcx
	0x49, 0x83, 0xfd, 0x20, //0x000012fd cmpq         $32, %r13
	0x0f, 0x82, 0x4a, 0x21, 0x00, 0x00, //0x00001301 jb           LBB0_730
	//0x00001307 LBB0_261
	0xc5, 0xfe, 0x6f, 0x01, //0x00001307 vmovdqu      (%rcx), %ymm0
	0xc5, 0xfd, 0x74, 0x0d, 0x0d, 0xed, 0xff, 0xff, //0x0000130b vpcmpeqb     $-4851(%rip), %ymm0, %ymm1  /* LCPI0_1+0(%rip) */
	0xc5, 0xfd, 0xd7, 0xc1, //0x00001313 vpmovmskb    %ymm1, %eax
	0xc5, 0xfd, 0x74, 0x05, 0x21, 0xed, 0xff, 0xff, //0x00001317 vpcmpeqb     $-4831(%rip), %ymm0, %ymm0  /* LCPI0_2+0(%rip) */
	0xc5, 0xfd, 0xd7, 0xf8, //0x0000131f vpmovmskb    %ymm0, %edi
	0x85, 0xff, //0x00001323 testl        %edi, %edi
	0x0f, 0x85, 0xbc, 0x20, 0x00, 0x00, //0x00001325 jne          LBB0_726
	0x4d, 0x85, 0xff, //0x0000132b testq        %r15, %r15
	0x0f, 0x85, 0xcd, 0x20, 0x00, 0x00, //0x0000132e jne          LBB0_728
	0x45, 0x31, 0xff, //0x00001334 xorl         %r15d, %r15d
	0x48, 0x85, 0xc0, //0x00001337 testq        %rax, %rax
	0x0f, 0x84, 0x09, 0x21, 0x00, 0x00, //0x0000133a je           LBB0_729
	//0x00001340 LBB0_264
	0x48, 0x0f, 0xbc, 0xc0, //0x00001340 bsfq         %rax, %rax
	0x4c, 0x29, 0xd1, //0x00001344 subq         %r10, %rcx
	0x48, 0x8d, 0x5c, 0x01, 0x01, //0x00001347 leaq         $1(%rcx,%rax), %rbx
	//0x0000134c LBB0_265
	0x48, 0x85, 0xdb, //0x0000134c testq        %rbx, %rbx
	0x0f, 0x88, 0xbb, 0x22, 0x00, 0x00, //0x0000134f js           LBB0_762
	0x49, 0x89, 0x56, 0x10, //0x00001355 movq         %rdx, $16(%r14)
	0x49, 0xc7, 0x06, 0x07, 0x00, 0x00, 0x00, //0x00001359 movq         $7, (%r14)
	0x49, 0x39, 0xd8, //0x00001360 cmpq         %rbx, %r8
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00001363 movq         $-1, %rax
	0x49, 0x0f, 0x4c, 0xc0, //0x0000136a cmovlq       %r8, %rax
	0x49, 0x89, 0x46, 0x18, //0x0000136e movq         %rax, $24(%r14)
	0xe9, 0xb7, 0xfc, 0xff, 0xff, //0x00001372 jmp          LBB0_202
	//0x00001377 LBB0_267
	0x4d, 0x89, 0xc1, //0x00001377 movq         %r8, %r9
	0x4d, 0x89, 0xf5, //0x0000137a movq         %r14, %r13
	0x48, 0x8b, 0x5d, 0xc0, //0x0000137d movq         $-64(%rbp), %rbx
	0x49, 0x83, 0xfd, 0x20, //0x00001381 cmpq         $32, %r13
	0x0f, 0x82, 0xd6, 0x21, 0x00, 0x00, //0x00001385 jb           LBB0_749
	//0x0000138b LBB0_268
	0xc5, 0xfe, 0x6f, 0x03, //0x0000138b vmovdqu      (%rbx), %ymm0
	0xc5, 0xfd, 0x74, 0x0d, 0x89, 0xec, 0xff, 0xff, //0x0000138f vpcmpeqb     $-4983(%rip), %ymm0, %ymm1  /* LCPI0_1+0(%rip) */
	0xc5, 0xfd, 0xd7, 0xc1, //0x00001397 vpmovmskb    %ymm1, %eax
	0xc5, 0xfd, 0x74, 0x0d, 0x9d, 0xec, 0xff, 0xff, //0x0000139b vpcmpeqb     $-4963(%rip), %ymm0, %ymm1  /* LCPI0_2+0(%rip) */
	0xc5, 0xfd, 0xd7, 0xc9, //0x000013a3 vpmovmskb    %ymm1, %ecx
	0xc5, 0xfe, 0x6f, 0x0d, 0xb1, 0xec, 0xff, 0xff, //0x000013a7 vmovdqu      $-4943(%rip), %ymm1  /* LCPI0_3+0(%rip) */
	0xc5, 0xf5, 0x64, 0xc8, //0x000013af vpcmpgtb     %ymm0, %ymm1, %ymm1
	0xc5, 0xed, 0x76, 0xd2, //0x000013b3 vpcmpeqd     %ymm2, %ymm2, %ymm2
	0xc5, 0xfd, 0x64, 0xc2, //0x000013b7 vpcmpgtb     %ymm2, %ymm0, %ymm0
	0xc5, 0xfd, 0xdb, 0xc1, //0x000013bb vpand        %ymm1, %ymm0, %ymm0
	0xc5, 0x7d, 0xd7, 0xd0, //0x000013bf vpmovmskb    %ymm0, %r10d
	0x85, 0xc9, //0x000013c3 testl        %ecx, %ecx
	0x0f, 0x85, 0x09, 0x21, 0x00, 0x00, //0x000013c5 jne          LBB0_740
	0x4d, 0x85, 0xe4, //0x000013cb testq        %r12, %r12
	0x0f, 0x85, 0x1b, 0x21, 0x00, 0x00, //0x000013ce jne          LBB0_742
	0x45, 0x31, 0xe4, //0x000013d4 xorl         %r12d, %r12d
	0x48, 0x85, 0xc0, //0x000013d7 testq        %rax, %rax
	0x0f, 0x84, 0x50, 0x21, 0x00, 0x00, //0x000013da je           LBB0_743
	//0x000013e0 LBB0_271
	0x48, 0x0f, 0xbc, 0xc8, //0x000013e0 bsfq         %rax, %rcx
	0xe9, 0x4c, 0x21, 0x00, 0x00, //0x000013e4 jmp          LBB0_744
	//0x000013e9 LBB0_272
	0x4c, 0x89, 0xdb, //0x000013e9 movq         %r11, %rbx
	//0x000013ec LBB0_273
	0x48, 0x89, 0x5d, 0xa0, //0x000013ec movq         %rbx, $-96(%rbp)
	0x45, 0x0f, 0xaf, 0xf0, //0x000013f0 imull        %r8d, %r14d
	0x41, 0x01, 0xce, //0x000013f4 addl         %ecx, %r14d
	0x44, 0x8b, 0x45, 0xb0, //0x000013f7 movl         $-80(%rbp), %r8d
	0x4c, 0x89, 0xd0, //0x000013fb movq         %r10, %rax
	0x48, 0xc1, 0xe8, 0x34, //0x000013fe shrq         $52, %rax
	0x0f, 0x85, 0xb3, 0xfd, 0xff, 0xff, //0x00001402 jne          LBB0_232
	//0x00001408 LBB0_274
	0xc4, 0xc1, 0xf9, 0x6e, 0xc2, //0x00001408 vmovq        %r10, %xmm0
	0xc5, 0xf9, 0x62, 0x05, 0xbb, 0xed, 0xff, 0xff, //0x0000140d vpunpckldq   $-4677(%rip), %xmm0, %xmm0  /* LCPI0_17+0(%rip) */
	0xc5, 0xf9, 0x5c, 0x05, 0xc3, 0xed, 0xff, 0xff, //0x00001415 vsubpd       $-4669(%rip), %xmm0, %xmm0  /* LCPI0_18+0(%rip) */
	0xc4, 0xe3, 0x79, 0x05, 0xc8, 0x01, //0x0000141d vpermilpd    $1, %xmm0, %xmm1
	0xc5, 0xf3, 0x58, 0xc0, //0x00001423 vaddsd       %xmm0, %xmm1, %xmm0
	0xc4, 0xe1, 0xf9, 0x7e, 0xc0, //0x00001427 vmovq        %xmm0, %rax
	0x44, 0x89, 0xc3, //0x0000142c movl         %r8d, %ebx
	0xc1, 0xeb, 0x1f, //0x0000142f shrl         $31, %ebx
	0x48, 0xc1, 0xe3, 0x3f, //0x00001432 shlq         $63, %rbx
	0x48, 0x09, 0xc3, //0x00001436 orq          %rax, %rbx
	0x4d, 0x85, 0xd2, //0x00001439 testq        %r10, %r10
	0x0f, 0x84, 0x9a, 0x06, 0x00, 0x00, //0x0000143c je           LBB0_359
	0x45, 0x85, 0xf6, //0x00001442 testl        %r14d, %r14d
	0x0f, 0x84, 0x91, 0x06, 0x00, 0x00, //0x00001445 je           LBB0_359
	0xc4, 0xe1, 0xf9, 0x6e, 0xc3, //0x0000144b vmovq        %rbx, %xmm0
	0x41, 0x8d, 0x46, 0xff, //0x00001450 leal         $-1(%r14), %eax
	0x83, 0xf8, 0x24, //0x00001454 cmpl         $36, %eax
	0x0f, 0x87, 0x2b, 0x00, 0x00, 0x00, //0x00001457 ja           LBB0_279
	0x41, 0x83, 0xfe, 0x17, //0x0000145d cmpl         $23, %r14d
	0x44, 0x89, 0x45, 0xb0, //0x00001461 movl         %r8d, $-80(%rbp)
	0x0f, 0x8c, 0x43, 0x00, 0x00, 0x00, //0x00001465 jl           LBB0_281
	0x49, 0x63, 0xc6, //0x0000146b movslq       %r14d, %rax
	0x48, 0x8d, 0x0d, 0x2b, 0x25, 0x00, 0x00, //0x0000146e leaq         $9515(%rip), %rcx  /* _P10_TAB+0(%rip) */
	0xc5, 0xfb, 0x59, 0x84, 0xc1, 0x50, 0xff, 0xff, 0xff, //0x00001475 vmulsd       $-176(%rcx,%rax,8), %xmm0, %xmm0
	0xb8, 0x16, 0x00, 0x00, 0x00, //0x0000147e movl         $22, %eax
	0xe9, 0x29, 0x00, 0x00, 0x00, //0x00001483 jmp          LBB0_282
	//0x00001488 LBB0_279
	0x41, 0x83, 0xfe, 0xea, //0x00001488 cmpl         $-22, %r14d
	0x0f, 0x82, 0x29, 0xfd, 0xff, 0xff, //0x0000148c jb           LBB0_232
	0x41, 0xf7, 0xde, //0x00001492 negl         %r14d
	0x49, 0x63, 0xc6, //0x00001495 movslq       %r14d, %rax
	0x48, 0x8d, 0x0d, 0x01, 0x25, 0x00, 0x00, //0x00001498 leaq         $9473(%rip), %rcx  /* _P10_TAB+0(%rip) */
	0xc5, 0xfb, 0x5e, 0x04, 0xc1, //0x0000149f vdivsd       (%rcx,%rax,8), %xmm0, %xmm0
	0xc4, 0xe1, 0xf9, 0x7e, 0xc3, //0x000014a4 vmovq        %xmm0, %rbx
	0xe9, 0x2e, 0x06, 0x00, 0x00, //0x000014a9 jmp          LBB0_359
	//0x000014ae LBB0_281
	0x44, 0x89, 0xf0, //0x000014ae movl         %r14d, %eax
	//0x000014b1 LBB0_282
	0xc5, 0xf9, 0x2e, 0x05, 0x37, 0xed, 0xff, 0xff, //0x000014b1 vucomisd     $-4809(%rip), %xmm0  /* LCPI0_19+0(%rip) */
	0x0f, 0x87, 0x2a, 0x00, 0x00, 0x00, //0x000014b9 ja           LBB0_285
	0xc5, 0xfb, 0x10, 0x0d, 0x31, 0xed, 0xff, 0xff, //0x000014bf vmovsd       $-4815(%rip), %xmm1  /* LCPI0_20+0(%rip) */
	0xc5, 0xf9, 0x2e, 0xc8, //0x000014c7 vucomisd     %xmm0, %xmm1
	0x0f, 0x87, 0x18, 0x00, 0x00, 0x00, //0x000014cb ja           LBB0_285
	0x89, 0xc0, //0x000014d1 movl         %eax, %eax
	0x48, 0x8d, 0x0d, 0xc6, 0x24, 0x00, 0x00, //0x000014d3 leaq         $9414(%rip), %rcx  /* _P10_TAB+0(%rip) */
	0xc5, 0xfb, 0x59, 0x04, 0xc1, //0x000014da vmulsd       (%rcx,%rax,8), %xmm0, %xmm0
	0xc4, 0xe1, 0xf9, 0x7e, 0xc3, //0x000014df vmovq        %xmm0, %rbx
	0xe9, 0xf3, 0x05, 0x00, 0x00, //0x000014e4 jmp          LBB0_359
	//0x000014e9 LBB0_285
	0x41, 0x8d, 0x86, 0x5c, 0x01, 0x00, 0x00, //0x000014e9 leal         $348(%r14), %eax
	0x4d, 0x85, 0xd2, //0x000014f0 testq        %r10, %r10
	0x0f, 0x85, 0xe1, 0xfc, 0xff, 0xff, //0x000014f3 jne          LBB0_234
	//0x000014f9 LBB0_286
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x000014f9 movl         $64, %ecx
	//0x000014fe LBB0_287
	0x4d, 0x89, 0xd0, //0x000014fe movq         %r10, %r8
	0x48, 0x89, 0xcf, //0x00001501 movq         %rcx, %rdi
	0x49, 0xd3, 0xe0, //0x00001504 shlq         %cl, %r8
	0x41, 0x89, 0xc3, //0x00001507 movl         %eax, %r11d
	0x49, 0xc1, 0xe3, 0x04, //0x0000150a shlq         $4, %r11
	0x48, 0x8d, 0x05, 0x4b, 0x25, 0x00, 0x00, //0x0000150e leaq         $9547(%rip), %rax  /* _POW10_M128_TAB+0(%rip) */
	0x49, 0x8b, 0x44, 0x03, 0x08, //0x00001515 movq         $8(%r11,%rax), %rax
	0x48, 0x89, 0x45, 0xa8, //0x0000151a movq         %rax, $-88(%rbp)
	0x49, 0xf7, 0xe0, //0x0000151e mulq         %r8
	0x48, 0x89, 0xc6, //0x00001521 movq         %rax, %rsi
	0x48, 0x89, 0xd3, //0x00001524 movq         %rdx, %rbx
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x00001527 andl         $511, %edx
	0x4c, 0x89, 0xc1, //0x0000152d movq         %r8, %rcx
	0x48, 0xf7, 0xd1, //0x00001530 notq         %rcx
	0x48, 0x39, 0xc8, //0x00001533 cmpq         %rcx, %rax
	0x0f, 0x86, 0x48, 0x00, 0x00, 0x00, //0x00001536 jbe          LBB0_292
	0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x0000153c cmpl         $511, %edx
	0x0f, 0x85, 0x3c, 0x00, 0x00, 0x00, //0x00001542 jne          LBB0_292
	0x4c, 0x89, 0xc0, //0x00001548 movq         %r8, %rax
	0x48, 0x8d, 0x15, 0x0e, 0x25, 0x00, 0x00, //0x0000154b leaq         $9486(%rip), %rdx  /* _POW10_M128_TAB+0(%rip) */
	0x49, 0xf7, 0x24, 0x13, //0x00001552 mulq         (%r11,%rdx)
	0x48, 0x01, 0xd6, //0x00001556 addq         %rdx, %rsi
	0x48, 0x83, 0xd3, 0x00, //0x00001559 adcq         $0, %rbx
	0x89, 0xda, //0x0000155d movl         %ebx, %edx
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x0000155f andl         $511, %edx
	0x48, 0x39, 0xc8, //0x00001565 cmpq         %rcx, %rax
	0x0f, 0x86, 0x16, 0x00, 0x00, 0x00, //0x00001568 jbe          LBB0_292
	0x48, 0x83, 0xfe, 0xff, //0x0000156e cmpq         $-1, %rsi
	0x0f, 0x85, 0x0c, 0x00, 0x00, 0x00, //0x00001572 jne          LBB0_292
	0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x00001578 cmpl         $511, %edx
	0x0f, 0x84, 0x75, 0x00, 0x00, 0x00, //0x0000157e je           LBB0_295
	//0x00001584 LBB0_292
	0x48, 0x89, 0xd8, //0x00001584 movq         %rbx, %rax
	0x48, 0xc1, 0xe8, 0x3f, //0x00001587 shrq         $63, %rax
	0x8d, 0x48, 0x09, //0x0000158b leal         $9(%rax), %ecx
	0x48, 0xd3, 0xeb, //0x0000158e shrq         %cl, %rbx
	0x48, 0x09, 0xf2, //0x00001591 orq          %rsi, %rdx
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x00001594 jne          LBB0_294
	0x89, 0xd9, //0x0000159a movl         %ebx, %ecx
	0x83, 0xe1, 0x03, //0x0000159c andl         $3, %ecx
	0x83, 0xf9, 0x01, //0x0000159f cmpl         $1, %ecx
	0x0f, 0x84, 0x51, 0x00, 0x00, 0x00, //0x000015a2 je           LBB0_295
	//0x000015a8 LBB0_294
	0x41, 0x69, 0xce, 0x6a, 0x52, 0x03, 0x00, //0x000015a8 imull        $217706, %r14d, %ecx
	0xc1, 0xf9, 0x10, //0x000015af sarl         $16, %ecx
	0x81, 0xc1, 0x3f, 0x04, 0x00, 0x00, //0x000015b2 addl         $1087, %ecx
	0x4c, 0x63, 0xf1, //0x000015b8 movslq       %ecx, %r14
	0x4c, 0x89, 0xf2, //0x000015bb movq         %r14, %rdx
	0x48, 0x29, 0xfa, //0x000015be subq         %rdi, %rdx
	0x48, 0xbe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x01, //0x000015c1 movabsq      $126100789566373888, %rsi
	0x48, 0x83, 0xf0, 0x01, //0x000015cb xorq         $1, %rax
	0x48, 0x29, 0xc2, //0x000015cf subq         %rax, %rdx
	0x89, 0xd8, //0x000015d2 movl         %ebx, %eax
	0x83, 0xe0, 0x01, //0x000015d4 andl         $1, %eax
	0x48, 0x01, 0xd8, //0x000015d7 addq         %rbx, %rax
	0x48, 0x89, 0xc1, //0x000015da movq         %rax, %rcx
	0x48, 0x21, 0xf1, //0x000015dd andq         %rsi, %rcx
	0x48, 0x83, 0xf9, 0x01, //0x000015e0 cmpq         $1, %rcx
	0x48, 0x83, 0xda, 0xff, //0x000015e4 sbbq         $-1, %rdx
	0x48, 0x8d, 0x72, 0xff, //0x000015e8 leaq         $-1(%rdx), %rsi
	0x48, 0x81, 0xfe, 0xfd, 0x07, 0x00, 0x00, //0x000015ec cmpq         $2045, %rsi
	0x0f, 0x86, 0x63, 0x00, 0x00, 0x00, //0x000015f3 jbe          LBB0_300
	//0x000015f9 LBB0_295
	0x48, 0x8b, 0x45, 0xa0, //0x000015f9 movq         $-96(%rbp), %rax
	0x4c, 0x29, 0xf8, //0x000015fd subq         %r15, %rax
	0x4d, 0x85, 0xed, //0x00001600 testq        %r13, %r13
	0x0f, 0x84, 0xe4, 0x02, 0x00, 0x00, //0x00001603 je           LBB0_320
	0x41, 0xc6, 0x04, 0x24, 0x00, //0x00001609 movb         $0, (%r12)
	0x49, 0x83, 0xfd, 0x01, //0x0000160e cmpq         $1, %r13
	0x0f, 0x84, 0xd5, 0x02, 0x00, 0x00, //0x00001612 je           LBB0_320
	0x49, 0x8d, 0x55, 0xff, //0x00001618 leaq         $-1(%r13), %rdx
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x0000161c movl         $1, %ecx
	0x48, 0x81, 0xfa, 0x80, 0x00, 0x00, 0x00, //0x00001621 cmpq         $128, %rdx
	0x0f, 0x82, 0xae, 0x02, 0x00, 0x00, //0x00001628 jb           LBB0_319
	0x48, 0x89, 0xd1, //0x0000162e movq         %rdx, %rcx
	0x48, 0x83, 0xe1, 0x80, //0x00001631 andq         $-128, %rcx
	0x48, 0x8d, 0x71, 0x80, //0x00001635 leaq         $-128(%rcx), %rsi
	0x48, 0x89, 0xf7, //0x00001639 movq         %rsi, %rdi
	0x48, 0xc1, 0xef, 0x07, //0x0000163c shrq         $7, %rdi
	0x48, 0xff, 0xc7, //0x00001640 incq         %rdi
	0x89, 0xfb, //0x00001643 movl         %edi, %ebx
	0x83, 0xe3, 0x03, //0x00001645 andl         $3, %ebx
	0x48, 0x81, 0xfe, 0x80, 0x01, 0x00, 0x00, //0x00001648 cmpq         $384, %rsi
	0x0f, 0x83, 0x8a, 0x01, 0x00, 0x00, //0x0000164f jae          LBB0_312
	0x31, 0xff, //0x00001655 xorl         %edi, %edi
	0xe9, 0x34, 0x02, 0x00, 0x00, //0x00001657 jmp          LBB0_314
	//0x0000165c LBB0_300
	0x48, 0x83, 0xf9, 0x01, //0x0000165c cmpq         $1, %rcx
	0xb1, 0x02, //0x00001660 movb         $2, %cl
	0x80, 0xd9, 0x00, //0x00001662 sbbb         $0, %cl
	0x48, 0xd3, 0xe8, //0x00001665 shrq         %cl, %rax
	0x48, 0xc1, 0xe2, 0x34, //0x00001668 shlq         $52, %rdx
	0x48, 0xb9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x0000166c movabsq      $4503599627370495, %rcx
	0x48, 0x21, 0xc8, //0x00001676 andq         %rcx, %rax
	0x48, 0x09, 0xd0, //0x00001679 orq          %rdx, %rax
	0x48, 0x89, 0xc3, //0x0000167c movq         %rax, %rbx
	0x48, 0xb9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x0000167f movabsq      $-9223372036854775808, %rcx
	0x48, 0x09, 0xcb, //0x00001689 orq          %rcx, %rbx
	0x8b, 0x7d, 0xb0, //0x0000168c movl         $-80(%rbp), %edi
	0x83, 0xff, 0xff, //0x0000168f cmpl         $-1, %edi
	0x48, 0x0f, 0x45, 0xd8, //0x00001692 cmovneq      %rax, %rbx
	0x83, 0x7d, 0xc0, 0x00, //0x00001696 cmpl         $0, $-64(%rbp)
	0x0f, 0x84, 0x3c, 0x04, 0x00, 0x00, //0x0000169a je           LBB0_359
	0x89, 0x7d, 0xb0, //0x000016a0 movl         %edi, $-80(%rbp)
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x000016a3 movl         $64, %ecx
	0x49, 0xff, 0xc2, //0x000016a8 incq         %r10
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x000016ab je           LBB0_303
	0x49, 0x0f, 0xbd, 0xca, //0x000016b1 bsrq         %r10, %rcx
	0x48, 0x83, 0xf1, 0x3f, //0x000016b5 xorq         $63, %rcx
	//0x000016b9 LBB0_303
	0x48, 0x89, 0xcf, //0x000016b9 movq         %rcx, %rdi
	0x49, 0xd3, 0xe2, //0x000016bc shlq         %cl, %r10
	0x48, 0x8b, 0x45, 0xa8, //0x000016bf movq         $-88(%rbp), %rax
	0x49, 0xf7, 0xe2, //0x000016c3 mulq         %r10
	0x49, 0x89, 0xc0, //0x000016c6 movq         %rax, %r8
	0x48, 0x89, 0xd6, //0x000016c9 movq         %rdx, %rsi
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x000016cc andl         $511, %edx
	0x4c, 0x89, 0xd1, //0x000016d2 movq         %r10, %rcx
	0x48, 0xf7, 0xd1, //0x000016d5 notq         %rcx
	0x48, 0x39, 0xc8, //0x000016d8 cmpq         %rcx, %rax
	0x0f, 0x86, 0x48, 0x00, 0x00, 0x00, //0x000016db jbe          LBB0_308
	0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x000016e1 cmpl         $511, %edx
	0x0f, 0x85, 0x3c, 0x00, 0x00, 0x00, //0x000016e7 jne          LBB0_308
	0x4c, 0x89, 0xd0, //0x000016ed movq         %r10, %rax
	0x48, 0x8d, 0x15, 0x69, 0x23, 0x00, 0x00, //0x000016f0 leaq         $9065(%rip), %rdx  /* _POW10_M128_TAB+0(%rip) */
	0x49, 0xf7, 0x24, 0x13, //0x000016f7 mulq         (%r11,%rdx)
	0x49, 0x01, 0xd0, //0x000016fb addq         %rdx, %r8
	0x48, 0x83, 0xd6, 0x00, //0x000016fe adcq         $0, %rsi
	0x89, 0xf2, //0x00001702 movl         %esi, %edx
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x00001704 andl         $511, %edx
	0x48, 0x39, 0xc8, //0x0000170a cmpq         %rcx, %rax
	0x0f, 0x86, 0x16, 0x00, 0x00, 0x00, //0x0000170d jbe          LBB0_308
	0x49, 0x83, 0xf8, 0xff, //0x00001713 cmpq         $-1, %r8
	0x0f, 0x85, 0x0c, 0x00, 0x00, 0x00, //0x00001717 jne          LBB0_308
	0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x0000171d cmpl         $511, %edx
	0x0f, 0x84, 0xd0, 0xfe, 0xff, 0xff, //0x00001723 je           LBB0_295
	//0x00001729 LBB0_308
	0x48, 0x89, 0xf0, //0x00001729 movq         %rsi, %rax
	0x48, 0xc1, 0xe8, 0x3f, //0x0000172c shrq         $63, %rax
	0x8d, 0x48, 0x09, //0x00001730 leal         $9(%rax), %ecx
	0x48, 0xd3, 0xee, //0x00001733 shrq         %cl, %rsi
	0x4c, 0x09, 0xc2, //0x00001736 orq          %r8, %rdx
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x00001739 jne          LBB0_310
	0x89, 0xf1, //0x0000173f movl         %esi, %ecx
	0x83, 0xe1, 0x03, //0x00001741 andl         $3, %ecx
	0x83, 0xf9, 0x01, //0x00001744 cmpl         $1, %ecx
	0x0f, 0x84, 0xac, 0xfe, 0xff, 0xff, //0x00001747 je           LBB0_295
	//0x0000174d LBB0_310
	0x49, 0x29, 0xfe, //0x0000174d subq         %rdi, %r14
	0x48, 0x83, 0xf0, 0x01, //0x00001750 xorq         $1, %rax
	0x49, 0x29, 0xc6, //0x00001754 subq         %rax, %r14
	0x89, 0xf0, //0x00001757 movl         %esi, %eax
	0x83, 0xe0, 0x01, //0x00001759 andl         $1, %eax
	0x48, 0x01, 0xf0, //0x0000175c addq         %rsi, %rax
	0x48, 0xb9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x01, //0x0000175f movabsq      $126100789566373888, %rcx
	0x48, 0x21, 0xc1, //0x00001769 andq         %rax, %rcx
	0x48, 0x89, 0xca, //0x0000176c movq         %rcx, %rdx
	0x48, 0x83, 0xf9, 0x01, //0x0000176f cmpq         $1, %rcx
	0x49, 0x83, 0xde, 0xff, //0x00001773 sbbq         $-1, %r14
	0x49, 0x8d, 0x4e, 0xff, //0x00001777 leaq         $-1(%r14), %rcx
	0x48, 0x81, 0xf9, 0xfd, 0x07, 0x00, 0x00, //0x0000177b cmpq         $2045, %rcx
	0x0f, 0x87, 0x71, 0xfe, 0xff, 0xff, //0x00001782 ja           LBB0_295
	0x48, 0x83, 0xfa, 0x01, //0x00001788 cmpq         $1, %rdx
	0xb1, 0x02, //0x0000178c movb         $2, %cl
	0x80, 0xd9, 0x00, //0x0000178e sbbb         $0, %cl
	0x48, 0xd3, 0xe8, //0x00001791 shrq         %cl, %rax
	0x49, 0xc1, 0xe6, 0x34, //0x00001794 shlq         $52, %r14
	0x48, 0xb9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00001798 movabsq      $4503599627370495, %rcx
	0x48, 0x21, 0xc8, //0x000017a2 andq         %rcx, %rax
	0x4c, 0x09, 0xf0, //0x000017a5 orq          %r14, %rax
	0x48, 0x89, 0xc1, //0x000017a8 movq         %rax, %rcx
	0x48, 0xba, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x000017ab movabsq      $-9223372036854775808, %rdx
	0x48, 0x09, 0xd1, //0x000017b5 orq          %rdx, %rcx
	0x83, 0x7d, 0xb0, 0xff, //0x000017b8 cmpl         $-1, $-80(%rbp)
	0x48, 0x0f, 0x45, 0xc8, //0x000017bc cmovneq      %rax, %rcx
	0xc4, 0xe1, 0xf9, 0x6e, 0xc3, //0x000017c0 vmovq        %rbx, %xmm0
	0xc4, 0xe1, 0xf9, 0x6e, 0xc9, //0x000017c5 vmovq        %rcx, %xmm1
	0xc5, 0xf9, 0x2e, 0xc1, //0x000017ca vucomisd     %xmm1, %xmm0
	0x0f, 0x85, 0x25, 0xfe, 0xff, 0xff, //0x000017ce jne          LBB0_295
	0x0f, 0x8b, 0x02, 0x03, 0x00, 0x00, //0x000017d4 jnp          LBB0_359
	0xe9, 0x1a, 0xfe, 0xff, 0xff, //0x000017da jmp          LBB0_295
	//0x000017df LBB0_312
	0x48, 0x89, 0xde, //0x000017df movq         %rbx, %rsi
	0x48, 0x29, 0xfe, //0x000017e2 subq         %rdi, %rsi
	0x31, 0xff, //0x000017e5 xorl         %edi, %edi
	0xc5, 0xf9, 0xef, 0xc0, //0x000017e7 vpxor        %xmm0, %xmm0, %xmm0
	//0x000017eb LBB0_313
	0xc4, 0xc1, 0x7e, 0x7f, 0x44, 0x3c, 0x01, //0x000017eb vmovdqu      %ymm0, $1(%r12,%rdi)
	0xc4, 0xc1, 0x7e, 0x7f, 0x44, 0x3c, 0x21, //0x000017f2 vmovdqu      %ymm0, $33(%r12,%rdi)
	0xc4, 0xc1, 0x7e, 0x7f, 0x44, 0x3c, 0x41, //0x000017f9 vmovdqu      %ymm0, $65(%r12,%rdi)
	0xc4, 0xc1, 0x7e, 0x7f, 0x44, 0x3c, 0x61, //0x00001800 vmovdqu      %ymm0, $97(%r12,%rdi)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x3c, 0x81, 0x00, 0x00, 0x00, //0x00001807 vmovdqu      %ymm0, $129(%r12,%rdi)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x3c, 0xa1, 0x00, 0x00, 0x00, //0x00001811 vmovdqu      %ymm0, $161(%r12,%rdi)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x3c, 0xc1, 0x00, 0x00, 0x00, //0x0000181b vmovdqu      %ymm0, $193(%r12,%rdi)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x3c, 0xe1, 0x00, 0x00, 0x00, //0x00001825 vmovdqu      %ymm0, $225(%r12,%rdi)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x3c, 0x01, 0x01, 0x00, 0x00, //0x0000182f vmovdqu      %ymm0, $257(%r12,%rdi)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x3c, 0x21, 0x01, 0x00, 0x00, //0x00001839 vmovdqu      %ymm0, $289(%r12,%rdi)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x3c, 0x41, 0x01, 0x00, 0x00, //0x00001843 vmovdqu      %ymm0, $321(%r12,%rdi)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x3c, 0x61, 0x01, 0x00, 0x00, //0x0000184d vmovdqu      %ymm0, $353(%r12,%rdi)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x3c, 0x81, 0x01, 0x00, 0x00, //0x00001857 vmovdqu      %ymm0, $385(%r12,%rdi)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x3c, 0xa1, 0x01, 0x00, 0x00, //0x00001861 vmovdqu      %ymm0, $417(%r12,%rdi)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x3c, 0xc1, 0x01, 0x00, 0x00, //0x0000186b vmovdqu      %ymm0, $449(%r12,%rdi)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x3c, 0xe1, 0x01, 0x00, 0x00, //0x00001875 vmovdqu      %ymm0, $481(%r12,%rdi)
	0x48, 0x81, 0xc7, 0x00, 0x02, 0x00, 0x00, //0x0000187f addq         $512, %rdi
	0x48, 0x83, 0xc6, 0x04, //0x00001886 addq         $4, %rsi
	0x0f, 0x85, 0x5b, 0xff, 0xff, 0xff, //0x0000188a jne          LBB0_313
	//0x00001890 LBB0_314
	0x48, 0x85, 0xdb, //0x00001890 testq        %rbx, %rbx
	0x0f, 0x84, 0x36, 0x00, 0x00, 0x00, //0x00001893 je           LBB0_317
	0x48, 0xf7, 0xdb, //0x00001899 negq         %rbx
	0xc5, 0xf9, 0xef, 0xc0, //0x0000189c vpxor        %xmm0, %xmm0, %xmm0
	//0x000018a0 LBB0_316
	0x48, 0x89, 0xfe, //0x000018a0 movq         %rdi, %rsi
	0x48, 0x83, 0xce, 0x01, //0x000018a3 orq          $1, %rsi
	0xc4, 0xc1, 0x7e, 0x7f, 0x04, 0x34, //0x000018a7 vmovdqu      %ymm0, (%r12,%rsi)
	0xc4, 0xc1, 0x7e, 0x7f, 0x44, 0x34, 0x20, //0x000018ad vmovdqu      %ymm0, $32(%r12,%rsi)
	0xc4, 0xc1, 0x7e, 0x7f, 0x44, 0x34, 0x40, //0x000018b4 vmovdqu      %ymm0, $64(%r12,%rsi)
	0xc4, 0xc1, 0x7e, 0x7f, 0x44, 0x34, 0x60, //0x000018bb vmovdqu      %ymm0, $96(%r12,%rsi)
	0x48, 0x83, 0xef, 0x80, //0x000018c2 subq         $-128, %rdi
	0x48, 0xff, 0xc3, //0x000018c6 incq         %rbx
	0x0f, 0x85, 0xd1, 0xff, 0xff, 0xff, //0x000018c9 jne          LBB0_316
	//0x000018cf LBB0_317
	0x48, 0x39, 0xca, //0x000018cf cmpq         %rcx, %rdx
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x000018d2 je           LBB0_320
	0x48, 0x83, 0xc9, 0x01, //0x000018d8 orq          $1, %rcx
	//0x000018dc LBB0_319
	0x41, 0xc6, 0x04, 0x0c, 0x00, //0x000018dc movb         $0, (%r12,%rcx)
	0x48, 0xff, 0xc1, //0x000018e1 incq         %rcx
	0x49, 0x39, 0xcd, //0x000018e4 cmpq         %rcx, %r13
	0x0f, 0x85, 0xef, 0xff, 0xff, 0xff, //0x000018e7 jne          LBB0_319
	//0x000018ed LBB0_320
	0x41, 0x8a, 0x11, //0x000018ed movb         (%r9), %dl
	0x31, 0xc9, //0x000018f0 xorl         %ecx, %ecx
	0x80, 0xfa, 0x2d, //0x000018f2 cmpb         $45, %dl
	0x0f, 0x94, 0xc1, //0x000018f5 sete         %cl
	0x45, 0x31, 0xd2, //0x000018f8 xorl         %r10d, %r10d
	0x48, 0x39, 0xc8, //0x000018fb cmpq         %rcx, %rax
	0x0f, 0x8e, 0x92, 0x00, 0x00, 0x00, //0x000018fe jle          LBB0_333
	0x88, 0x55, 0xd7, //0x00001904 movb         %dl, $-41(%rbp)
	0xb3, 0x01, //0x00001907 movb         $1, %bl
	0x45, 0x31, 0xc0, //0x00001909 xorl         %r8d, %r8d
	0x45, 0x31, 0xf6, //0x0000190c xorl         %r14d, %r14d
	0x31, 0xff, //0x0000190f xorl         %edi, %edi
	0x45, 0x31, 0xdb, //0x00001911 xorl         %r11d, %r11d
	0xe9, 0x21, 0x00, 0x00, 0x00, //0x00001914 jmp          LBB0_325
	//0x00001919 LBB0_322
	0x80, 0xfa, 0x30, //0x00001919 cmpb         $48, %dl
	0xba, 0x01, 0x00, 0x00, 0x00, //0x0000191c movl         $1, %edx
	0x44, 0x0f, 0x45, 0xf2, //0x00001921 cmovnel      %edx, %r14d
	//0x00001925 LBB0_323
	0x44, 0x89, 0xc7, //0x00001925 movl         %r8d, %edi
	//0x00001928 LBB0_324
	0x48, 0xff, 0xc1, //0x00001928 incq         %rcx
	0x48, 0x39, 0xc1, //0x0000192b cmpq         %rax, %rcx
	0x0f, 0x9c, 0xc3, //0x0000192e setl         %bl
	0x48, 0x39, 0xc8, //0x00001931 cmpq         %rcx, %rax
	0x0f, 0x84, 0x70, 0x00, 0x00, 0x00, //0x00001934 je           LBB0_334
	//0x0000193a LBB0_325
	0x41, 0x0f, 0xb6, 0x14, 0x09, //0x0000193a movzbl       (%r9,%rcx), %edx
	0x8d, 0x72, 0xd0, //0x0000193f leal         $-48(%rdx), %esi
	0x40, 0x80, 0xfe, 0x09, //0x00001942 cmpb         $9, %sil
	0x0f, 0x87, 0x1b, 0x00, 0x00, 0x00, //0x00001946 ja           LBB0_329
	0x85, 0xff, //0x0000194c testl        %edi, %edi
	0x0f, 0x85, 0x2a, 0x00, 0x00, 0x00, //0x0000194e jne          LBB0_331
	0x80, 0xfa, 0x30, //0x00001954 cmpb         $48, %dl
	0x0f, 0x85, 0x21, 0x00, 0x00, 0x00, //0x00001957 jne          LBB0_331
	0x41, 0xff, 0xca, //0x0000195d decl         %r10d
	0x31, 0xff, //0x00001960 xorl         %edi, %edi
	0xe9, 0xc1, 0xff, 0xff, 0xff, //0x00001962 jmp          LBB0_324
	//0x00001967 LBB0_329
	0x80, 0xfa, 0x2e, //0x00001967 cmpb         $46, %dl
	0x0f, 0x85, 0x49, 0x00, 0x00, 0x00, //0x0000196a jne          LBB0_335
	0x41, 0x89, 0xfa, //0x00001970 movl         %edi, %r10d
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00001973 movl         $1, %r11d
	0xe9, 0xaa, 0xff, 0xff, 0xff, //0x00001979 jmp          LBB0_324
	//0x0000197e LBB0_331
	0x49, 0x63, 0xf8, //0x0000197e movslq       %r8d, %rdi
	0x49, 0x39, 0xfd, //0x00001981 cmpq         %rdi, %r13
	0x0f, 0x86, 0x8f, 0xff, 0xff, 0xff, //0x00001984 jbe          LBB0_322
	0x41, 0x88, 0x14, 0x3c, //0x0000198a movb         %dl, (%r12,%rdi)
	0x41, 0xff, 0xc0, //0x0000198e incl         %r8d
	0xe9, 0x8f, 0xff, 0xff, 0xff, //0x00001991 jmp          LBB0_323
	//0x00001996 LBB0_333
	0x45, 0x31, 0xff, //0x00001996 xorl         %r15d, %r15d
	0x31, 0xf6, //0x00001999 xorl         %esi, %esi
	0x48, 0xb9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x0000199b movabsq      $4503599627370495, %rcx
	0xe9, 0x15, 0x01, 0x00, 0x00, //0x000019a5 jmp          LBB0_358
	//0x000019aa LBB0_334
	0x45, 0x85, 0xdb, //0x000019aa testl        %r11d, %r11d
	0x4d, 0x89, 0xd7, //0x000019ad movq         %r10, %r15
	0x45, 0x0f, 0x44, 0xf8, //0x000019b0 cmovel       %r8d, %r15d
	0xe9, 0xb6, 0x00, 0x00, 0x00, //0x000019b4 jmp          LBB0_351
	//0x000019b9 LBB0_335
	0x45, 0x85, 0xdb, //0x000019b9 testl        %r11d, %r11d
	0x45, 0x0f, 0x44, 0xd0, //0x000019bc cmovel       %r8d, %r10d
	0xf6, 0xc3, 0x01, //0x000019c0 testb        $1, %bl
	0x0f, 0x84, 0x39, 0x00, 0x00, 0x00, //0x000019c3 je           LBB0_341
	0x80, 0xca, 0x20, //0x000019c9 orb          $32, %dl
	0x80, 0xfa, 0x65, //0x000019cc cmpb         $101, %dl
	0x48, 0x8b, 0x7d, 0xa0, //0x000019cf movq         $-96(%rbp), %rdi
	0x0f, 0x85, 0x29, 0x00, 0x00, 0x00, //0x000019d3 jne          LBB0_341
	0x48, 0x8d, 0x71, 0x01, //0x000019d9 leaq         $1(%rcx), %rsi
	0x89, 0xf2, //0x000019dd movl         %esi, %edx
	0x41, 0x8a, 0x14, 0x11, //0x000019df movb         (%r9,%rdx), %dl
	0x80, 0xfa, 0x2b, //0x000019e3 cmpb         $43, %dl
	0x0f, 0x84, 0x1e, 0x00, 0x00, 0x00, //0x000019e6 je           LBB0_342
	0x80, 0xfa, 0x2d, //0x000019ec cmpb         $45, %dl
	0x0f, 0x85, 0x24, 0x00, 0x00, 0x00, //0x000019ef jne          LBB0_344
	0x83, 0xc1, 0x02, //0x000019f5 addl         $2, %ecx
	0xba, 0xff, 0xff, 0xff, 0xff, //0x000019f8 movl         $-1, %edx
	0xe9, 0x10, 0x00, 0x00, 0x00, //0x000019fd jmp          LBB0_343
	//0x00001a02 LBB0_341
	0x4d, 0x89, 0xd7, //0x00001a02 movq         %r10, %r15
	0xe9, 0x65, 0x00, 0x00, 0x00, //0x00001a05 jmp          LBB0_351
	//0x00001a0a LBB0_342
	0x83, 0xc1, 0x02, //0x00001a0a addl         $2, %ecx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00001a0d movl         $1, %edx
	//0x00001a12 LBB0_343
	0x89, 0xce, //0x00001a12 movl         %ecx, %esi
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x00001a14 jmp          LBB0_345
	//0x00001a19 LBB0_344
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00001a19 movl         $1, %edx
	//0x00001a1e LBB0_345
	0x48, 0x63, 0xf6, //0x00001a1e movslq       %esi, %rsi
	0x31, 0xc9, //0x00001a21 xorl         %ecx, %ecx
	0x48, 0x39, 0xf0, //0x00001a23 cmpq         %rsi, %rax
	0x0f, 0x8e, 0x3a, 0x00, 0x00, 0x00, //0x00001a26 jle          LBB0_350
	0x49, 0x01, 0xf7, //0x00001a2c addq         %rsi, %r15
	0x31, 0xc9, //0x00001a2f xorl         %ecx, %ecx
	//0x00001a31 LBB0_347
	0x81, 0xf9, 0x0f, 0x27, 0x00, 0x00, //0x00001a31 cmpl         $9999, %ecx
	0x0f, 0x8f, 0x29, 0x00, 0x00, 0x00, //0x00001a37 jg           LBB0_350
	0x48, 0x8b, 0x45, 0xc8, //0x00001a3d movq         $-56(%rbp), %rax
	0x42, 0x0f, 0xb6, 0x04, 0x38, //0x00001a41 movzbl       (%rax,%r15), %eax
	0x8d, 0x70, 0xd0, //0x00001a46 leal         $-48(%rax), %esi
	0x40, 0x80, 0xfe, 0x09, //0x00001a49 cmpb         $9, %sil
	0x0f, 0x87, 0x13, 0x00, 0x00, 0x00, //0x00001a4d ja           LBB0_350
	0x8d, 0x0c, 0x89, //0x00001a53 leal         (%rcx,%rcx,4), %ecx
	0x8d, 0x4c, 0x48, 0xd0, //0x00001a56 leal         $-48(%rax,%rcx,2), %ecx
	0x49, 0xff, 0xc7, //0x00001a5a incq         %r15
	0x4c, 0x39, 0xff, //0x00001a5d cmpq         %r15, %rdi
	0x0f, 0x85, 0xcb, 0xff, 0xff, 0xff, //0x00001a60 jne          LBB0_347
	//0x00001a66 LBB0_350
	0x0f, 0xaf, 0xca, //0x00001a66 imull        %edx, %ecx
	0x44, 0x01, 0xd1, //0x00001a69 addl         %r10d, %ecx
	0x41, 0x89, 0xcf, //0x00001a6c movl         %ecx, %r15d
	//0x00001a6f LBB0_351
	0x45, 0x85, 0xc0, //0x00001a6f testl        %r8d, %r8d
	0x0f, 0x84, 0x1e, 0x00, 0x00, 0x00, //0x00001a72 je           LBB0_354
	0x41, 0x81, 0xff, 0x36, 0x01, 0x00, 0x00, //0x00001a78 cmpl         $310, %r15d
	0x0f, 0x8e, 0x1b, 0x00, 0x00, 0x00, //0x00001a7f jle          LBB0_355
	//0x00001a85 LBB0_353
	0x31, 0xf6, //0x00001a85 xorl         %esi, %esi
	0x49, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, //0x00001a87 movabsq      $9218868437227405312, %r15
	0xe9, 0x1c, 0x00, 0x00, 0x00, //0x00001a91 jmp          LBB0_357
	//0x00001a96 LBB0_354
	0x45, 0x31, 0xff, //0x00001a96 xorl         %r15d, %r15d
	0x31, 0xf6, //0x00001a99 xorl         %esi, %esi
	0xe9, 0x12, 0x00, 0x00, 0x00, //0x00001a9b jmp          LBB0_357
	//0x00001aa0 LBB0_355
	0x31, 0xf6, //0x00001aa0 xorl         %esi, %esi
	0x41, 0x81, 0xff, 0xb6, 0xfe, 0xff, 0xff, //0x00001aa2 cmpl         $-330, %r15d
	0x0f, 0x8d, 0x68, 0x00, 0x00, 0x00, //0x00001aa9 jge          LBB0_362
	0x45, 0x31, 0xff, //0x00001aaf xorl         %r15d, %r15d
	//0x00001ab2 LBB0_357
	0x48, 0xb9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00001ab2 movabsq      $4503599627370495, %rcx
	0x8a, 0x55, 0xd7, //0x00001abc movb         $-41(%rbp), %dl
	//0x00001abf LBB0_358
	0x48, 0x21, 0xce, //0x00001abf andq         %rcx, %rsi
	0x4c, 0x09, 0xfe, //0x00001ac2 orq          %r15, %rsi
	0x48, 0x89, 0xf3, //0x00001ac5 movq         %rsi, %rbx
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00001ac8 movabsq      $-9223372036854775808, %rax
	0x48, 0x09, 0xc3, //0x00001ad2 orq          %rax, %rbx
	0x80, 0xfa, 0x2d, //0x00001ad5 cmpb         $45, %dl
	0x48, 0x0f, 0x45, 0xde, //0x00001ad8 cmovneq      %rsi, %rbx
	//0x00001adc LBB0_359
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00001adc movabsq      $-9223372036854775808, %rax
	0x48, 0xff, 0xc8, //0x00001ae6 decq         %rax
	0x48, 0x21, 0xd8, //0x00001ae9 andq         %rbx, %rax
	0x48, 0xb9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, //0x00001aec movabsq      $9218868437227405312, %rcx
	0x48, 0x39, 0xc8, //0x00001af6 cmpq         %rcx, %rax
	0x48, 0x8b, 0x45, 0xb8, //0x00001af9 movq         $-72(%rbp), %rax
	0x0f, 0x85, 0x07, 0x00, 0x00, 0x00, //0x00001afd jne          LBB0_361
	0x48, 0xc7, 0x00, 0xf8, 0xff, 0xff, 0xff, //0x00001b03 movq         $-8, (%rax)
	//0x00001b0a LBB0_361
	0x48, 0x89, 0x58, 0x08, //0x00001b0a movq         %rbx, $8(%rax)
	0x48, 0x8b, 0x5d, 0xa0, //0x00001b0e movq         $-96(%rbp), %rbx
	0xe9, 0x17, 0xf5, 0xff, 0xff, //0x00001b12 jmp          LBB0_202
	//0x00001b17 LBB0_362
	0x49, 0xba, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, //0x00001b17 movabsq      $1152921504606846975, %r10
	0x45, 0x85, 0xff, //0x00001b21 testl        %r15d, %r15d
	0x0f, 0x8e, 0x82, 0x07, 0x00, 0x00, //0x00001b24 jle          LBB0_479
	0x31, 0xf6, //0x00001b2a xorl         %esi, %esi
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00001b2c movl         $1, %r11d
	0x44, 0x89, 0xc0, //0x00001b32 movl         %r8d, %eax
	0xe9, 0x14, 0x00, 0x00, 0x00, //0x00001b35 jmp          LBB0_366
	//0x00001b3a LBB0_364
	0x89, 0xc3, //0x00001b3a movl         %eax, %ebx
	//0x00001b3c LBB0_365
	0x48, 0x8b, 0x75, 0xb0, //0x00001b3c movq         $-80(%rbp), %rsi
	0x03, 0x75, 0xa8, //0x00001b40 addl         $-88(%rbp), %esi
	0x89, 0xd8, //0x00001b43 movl         %ebx, %eax
	0x45, 0x85, 0xff, //0x00001b45 testl        %r15d, %r15d
	0x0f, 0x8e, 0x61, 0x07, 0x00, 0x00, //0x00001b48 jle          LBB0_480
	//0x00001b4e LBB0_366
	0xb9, 0x1b, 0x00, 0x00, 0x00, //0x00001b4e movl         $27, %ecx
	0x41, 0x83, 0xff, 0x08, //0x00001b53 cmpl         $8, %r15d
	0x0f, 0x8f, 0x0d, 0x00, 0x00, 0x00, //0x00001b57 jg           LBB0_368
	0x44, 0x89, 0xf9, //0x00001b5d movl         %r15d, %ecx
	0x48, 0x8d, 0x15, 0x89, 0x4a, 0x00, 0x00, //0x00001b60 leaq         $19081(%rip), %rdx  /* _POW_TAB+0(%rip) */
	0x8b, 0x0c, 0x8a, //0x00001b67 movl         (%rdx,%rcx,4), %ecx
	//0x00001b6a LBB0_368
	0x48, 0x89, 0x75, 0xb0, //0x00001b6a movq         %rsi, $-80(%rbp)
	0x85, 0xc0, //0x00001b6e testl        %eax, %eax
	0x89, 0x4d, 0xa8, //0x00001b70 movl         %ecx, $-88(%rbp)
	0x0f, 0x84, 0xc1, 0xff, 0xff, 0xff, //0x00001b73 je           LBB0_364
	0x41, 0x89, 0xc9, //0x00001b79 movl         %ecx, %r9d
	0x41, 0xf7, 0xd9, //0x00001b7c negl         %r9d
	0x85, 0xc9, //0x00001b7f testl        %ecx, %ecx
	0x0f, 0x84, 0xb3, 0xff, 0xff, 0xff, //0x00001b81 je           LBB0_364
	0x0f, 0x88, 0x90, 0x01, 0x00, 0x00, //0x00001b87 js           LBB0_395
	//0x00001b8d LBB0_371
	0x41, 0x83, 0xf9, 0xc3, //0x00001b8d cmpl         $-61, %r9d
	0x0f, 0x8e, 0x21, 0x00, 0x00, 0x00, //0x00001b91 jle          LBB0_375
	0xe9, 0x9f, 0x03, 0x00, 0x00, //0x00001b97 jmp          LBB0_426
	//0x00001b9c LBB0_372
	0xff, 0xc8, //0x00001b9c decl         %eax
	0x41, 0x89, 0xc0, //0x00001b9e movl         %eax, %r8d
	//0x00001ba1 LBB0_373
	0x45, 0x85, 0xc0, //0x00001ba1 testl        %r8d, %r8d
	0x45, 0x0f, 0x44, 0xf8, //0x00001ba4 cmovel       %r8d, %r15d
	//0x00001ba8 LBB0_374
	0x44, 0x8d, 0x49, 0x3c, //0x00001ba8 leal         $60(%rcx), %r9d
	0x44, 0x89, 0xc0, //0x00001bac movl         %r8d, %eax
	0x83, 0xf9, 0x88, //0x00001baf cmpl         $-120, %ecx
	0x0f, 0x8d, 0x74, 0x03, 0x00, 0x00, //0x00001bb2 jge          LBB0_425
	//0x00001bb8 LBB0_375
	0x44, 0x89, 0xc9, //0x00001bb8 movl         %r9d, %ecx
	0x48, 0x63, 0xf8, //0x00001bbb movslq       %eax, %rdi
	0x31, 0xf6, //0x00001bbe xorl         %esi, %esi
	0x31, 0xd2, //0x00001bc0 xorl         %edx, %edx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001bc2 .p2align 4, 0x90
	//0x00001bd0 LBB0_376
	0x48, 0x39, 0xfe, //0x00001bd0 cmpq         %rdi, %rsi
	0x0f, 0x8d, 0x23, 0x00, 0x00, 0x00, //0x00001bd3 jge          LBB0_378
	0x48, 0x8d, 0x14, 0x92, //0x00001bd9 leaq         (%rdx,%rdx,4), %rdx
	0x49, 0x0f, 0xbe, 0x1c, 0x34, //0x00001bdd movsbq       (%r12,%rsi), %rbx
	0x48, 0x8d, 0x54, 0x53, 0xd0, //0x00001be2 leaq         $-48(%rbx,%rdx,2), %rdx
	0x48, 0xff, 0xc6, //0x00001be7 incq         %rsi
	0x49, 0x8d, 0x5a, 0x01, //0x00001bea leaq         $1(%r10), %rbx
	0x48, 0x39, 0xda, //0x00001bee cmpq         %rbx, %rdx
	0x0f, 0x82, 0xd9, 0xff, 0xff, 0xff, //0x00001bf1 jb           LBB0_376
	0xe9, 0x2a, 0x00, 0x00, 0x00, //0x00001bf7 jmp          LBB0_380
	//0x00001bfc LBB0_378
	0x48, 0x85, 0xd2, //0x00001bfc testq        %rdx, %rdx
	0x0f, 0x84, 0x10, 0x01, 0x00, 0x00, //0x00001bff je           LBB0_393
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001c05 .p2align 4, 0x90
	//0x00001c10 LBB0_379
	0x48, 0x01, 0xd2, //0x00001c10 addq         %rdx, %rdx
	0x48, 0x8d, 0x14, 0x92, //0x00001c13 leaq         (%rdx,%rdx,4), %rdx
	0xff, 0xc6, //0x00001c17 incl         %esi
	0x49, 0x8d, 0x7a, 0x01, //0x00001c19 leaq         $1(%r10), %rdi
	0x48, 0x39, 0xfa, //0x00001c1d cmpq         %rdi, %rdx
	0x0f, 0x82, 0xea, 0xff, 0xff, 0xff, //0x00001c20 jb           LBB0_379
	//0x00001c26 LBB0_380
	0x41, 0x29, 0xf7, //0x00001c26 subl         %esi, %r15d
	0x31, 0xff, //0x00001c29 xorl         %edi, %edi
	0x39, 0xc6, //0x00001c2b cmpl         %eax, %esi
	0x0f, 0x8d, 0x49, 0x00, 0x00, 0x00, //0x00001c2d jge          LBB0_385
	0x48, 0x63, 0xc6, //0x00001c33 movslq       %esi, %rax
	0x49, 0x63, 0xf0, //0x00001c36 movslq       %r8d, %rsi
	0x49, 0x8d, 0x3c, 0x04, //0x00001c39 leaq         (%r12,%rax), %rdi
	0x45, 0x31, 0xc0, //0x00001c3d xorl         %r8d, %r8d
	//0x00001c40 .p2align 4, 0x90
	//0x00001c40 LBB0_382
	0x48, 0x89, 0xd3, //0x00001c40 movq         %rdx, %rbx
	0x48, 0xc1, 0xeb, 0x3c, //0x00001c43 shrq         $60, %rbx
	0x4c, 0x21, 0xd2, //0x00001c47 andq         %r10, %rdx
	0x80, 0xcb, 0x30, //0x00001c4a orb          $48, %bl
	0x43, 0x88, 0x1c, 0x04, //0x00001c4d movb         %bl, (%r12,%r8)
	0x48, 0x8d, 0x14, 0x92, //0x00001c51 leaq         (%rdx,%rdx,4), %rdx
	0x4a, 0x0f, 0xbe, 0x1c, 0x07, //0x00001c55 movsbq       (%rdi,%r8), %rbx
	0x48, 0x8d, 0x54, 0x53, 0xd0, //0x00001c5a leaq         $-48(%rbx,%rdx,2), %rdx
	0x4a, 0x8d, 0x5c, 0x00, 0x01, //0x00001c5f leaq         $1(%rax,%r8), %rbx
	0x49, 0xff, 0xc0, //0x00001c64 incq         %r8
	0x48, 0x39, 0xf3, //0x00001c67 cmpq         %rsi, %rbx
	0x0f, 0x8c, 0xd0, 0xff, 0xff, 0xff, //0x00001c6a jl           LBB0_382
	0x48, 0x85, 0xd2, //0x00001c70 testq        %rdx, %rdx
	0x0f, 0x84, 0x5f, 0x00, 0x00, 0x00, //0x00001c73 je           LBB0_389
	0x44, 0x89, 0xc7, //0x00001c79 movl         %r8d, %edi
	//0x00001c7c LBB0_385
	0x41, 0x89, 0xf8, //0x00001c7c movl         %edi, %r8d
	0xe9, 0x23, 0x00, 0x00, 0x00, //0x00001c7f jmp          LBB0_387
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001c84 .p2align 4, 0x90
	//0x00001c90 LBB0_386
	0x48, 0x85, 0xc0, //0x00001c90 testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xf3, //0x00001c93 cmovnel      %r11d, %r14d
	0x48, 0x01, 0xd2, //0x00001c97 addq         %rdx, %rdx
	0x48, 0x8d, 0x14, 0x92, //0x00001c9a leaq         (%rdx,%rdx,4), %rdx
	0x48, 0x85, 0xd2, //0x00001c9e testq        %rdx, %rdx
	0x0f, 0x84, 0x31, 0x00, 0x00, 0x00, //0x00001ca1 je           LBB0_389
	//0x00001ca7 LBB0_387
	0x48, 0x89, 0xd0, //0x00001ca7 movq         %rdx, %rax
	0x48, 0xc1, 0xe8, 0x3c, //0x00001caa shrq         $60, %rax
	0x4c, 0x21, 0xd2, //0x00001cae andq         %r10, %rdx
	0x49, 0x63, 0xf0, //0x00001cb1 movslq       %r8d, %rsi
	0x49, 0x39, 0xf5, //0x00001cb4 cmpq         %rsi, %r13
	0x0f, 0x86, 0xd3, 0xff, 0xff, 0xff, //0x00001cb7 jbe          LBB0_386
	0x0c, 0x30, //0x00001cbd orb          $48, %al
	0x41, 0x88, 0x04, 0x34, //0x00001cbf movb         %al, (%r12,%rsi)
	0xff, 0xc6, //0x00001cc3 incl         %esi
	0x41, 0x89, 0xf0, //0x00001cc5 movl         %esi, %r8d
	0x48, 0x01, 0xd2, //0x00001cc8 addq         %rdx, %rdx
	0x48, 0x8d, 0x14, 0x92, //0x00001ccb leaq         (%rdx,%rdx,4), %rdx
	0x48, 0x85, 0xd2, //0x00001ccf testq        %rdx, %rdx
	0x0f, 0x85, 0xcf, 0xff, 0xff, 0xff, //0x00001cd2 jne          LBB0_387
	//0x00001cd8 LBB0_389
	0x41, 0xff, 0xc7, //0x00001cd8 incl         %r15d
	0x45, 0x85, 0xc0, //0x00001cdb testl        %r8d, %r8d
	0x0f, 0x8e, 0xbd, 0xfe, 0xff, 0xff, //0x00001cde jle          LBB0_373
	0x44, 0x89, 0xc0, //0x00001ce4 movl         %r8d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x00001ce7 cmpb         $48, $-1(%rax,%r12)
	0x0f, 0x85, 0xb5, 0xfe, 0xff, 0xff, //0x00001ced jne          LBB0_374
	//0x00001cf3 LBB0_391
	0x48, 0x83, 0xf8, 0x01, //0x00001cf3 cmpq         $1, %rax
	0x0f, 0x8e, 0x9f, 0xfe, 0xff, 0xff, //0x00001cf7 jle          LBB0_372
	0x4c, 0x8d, 0x40, 0xff, //0x00001cfd leaq         $-1(%rax), %r8
	0x41, 0x80, 0x7c, 0x04, 0xfe, 0x30, //0x00001d01 cmpb         $48, $-2(%r12,%rax)
	0x4c, 0x89, 0xc0, //0x00001d07 movq         %r8, %rax
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00001d0a je           LBB0_391
	0xe9, 0x93, 0xfe, 0xff, 0xff, //0x00001d10 jmp          LBB0_374
	//0x00001d15 LBB0_393
	0x45, 0x31, 0xc0, //0x00001d15 xorl         %r8d, %r8d
	0xe9, 0x8b, 0xfe, 0xff, 0xff, //0x00001d18 jmp          LBB0_374
	//0x00001d1d LBB0_395
	0x83, 0xf9, 0xc3, //0x00001d1d cmpl         $-61, %ecx
	0x0f, 0x8f, 0x6f, 0x03, 0x00, 0x00, //0x00001d20 jg           LBB0_447
	0x48, 0x8d, 0x1d, 0xf3, 0x48, 0x00, 0x00, //0x00001d26 leaq         $18675(%rip), %rbx  /* _LSHIFT_TAB+0(%rip) */
	0xe9, 0x20, 0x00, 0x00, 0x00, //0x00001d2d jmp          LBB0_400
	//0x00001d32 LBB0_397
	0xff, 0xc8, //0x00001d32 decl         %eax
	0x41, 0x89, 0xc0, //0x00001d34 movl         %eax, %r8d
	//0x00001d37 LBB0_398
	0x45, 0x85, 0xc0, //0x00001d37 testl        %r8d, %r8d
	0x45, 0x0f, 0x44, 0xf8, //0x00001d3a cmovel       %r8d, %r15d
	//0x00001d3e LBB0_399
	0x48, 0x8b, 0x4d, 0xc0, //0x00001d3e movq         $-64(%rbp), %rcx
	0x44, 0x8d, 0x49, 0xc4, //0x00001d42 leal         $-60(%rcx), %r9d
	0x44, 0x89, 0xc0, //0x00001d46 movl         %r8d, %eax
	0x83, 0xf9, 0x78, //0x00001d49 cmpl         $120, %ecx
	0x0f, 0x8e, 0x34, 0x03, 0x00, 0x00, //0x00001d4c jle          LBB0_446
	//0x00001d52 LBB0_400
	0x4c, 0x89, 0x7d, 0xc8, //0x00001d52 movq         %r15, $-56(%rbp)
	0x4c, 0x89, 0x4d, 0xc0, //0x00001d56 movq         %r9, $-64(%rbp)
	0x48, 0x63, 0xf8, //0x00001d5a movslq       %eax, %rdi
	0x85, 0xff, //0x00001d5d testl        %edi, %edi
	0x0f, 0x84, 0x3a, 0x00, 0x00, 0x00, //0x00001d5f je           LBB0_406
	0xb2, 0x38, //0x00001d65 movb         $56, %dl
	0x31, 0xc9, //0x00001d67 xorl         %ecx, %ecx
	//0x00001d69 LBB0_402
	0x41, 0xb9, 0x13, 0x00, 0x00, 0x00, //0x00001d69 movl         $19, %r9d
	0x48, 0x83, 0xf9, 0x2a, //0x00001d6f cmpq         $42, %rcx
	0x0f, 0x84, 0x2c, 0x00, 0x00, 0x00, //0x00001d73 je           LBB0_407
	0x41, 0x38, 0x14, 0x0c, //0x00001d79 cmpb         %dl, (%r12,%rcx)
	0x0f, 0x85, 0x9e, 0x01, 0x00, 0x00, //0x00001d7d jne          LBB0_424
	0x0f, 0xb6, 0x94, 0x19, 0x65, 0x18, 0x00, 0x00, //0x00001d83 movzbl       $6245(%rcx,%rbx), %edx
	0x48, 0xff, 0xc1, //0x00001d8b incq         %rcx
	0x48, 0x39, 0xcf, //0x00001d8e cmpq         %rcx, %rdi
	0x0f, 0x85, 0xd2, 0xff, 0xff, 0xff, //0x00001d91 jne          LBB0_402
	0x84, 0xd2, //0x00001d97 testb        %dl, %dl
	0x0f, 0x84, 0x06, 0x00, 0x00, 0x00, //0x00001d99 je           LBB0_407
	//0x00001d9f LBB0_406
	0x41, 0xb9, 0x12, 0x00, 0x00, 0x00, //0x00001d9f movl         $18, %r9d
	//0x00001da5 LBB0_407
	0x85, 0xc0, //0x00001da5 testl        %eax, %eax
	0x0f, 0x8e, 0xb1, 0x00, 0x00, 0x00, //0x00001da7 jle          LBB0_415
	0x41, 0xbf, 0x01, 0x00, 0x00, 0x00, //0x00001dad movl         $1, %r15d
	0x44, 0x01, 0xc8, //0x00001db3 addl         %r9d, %eax
	0x48, 0x98, //0x00001db6 cltq         
	0x48, 0x89, 0xc3, //0x00001db8 movq         %rax, %rbx
	0x48, 0xc1, 0xe3, 0x20, //0x00001dbb shlq         $32, %rbx
	0x48, 0xff, 0xc8, //0x00001dbf decq         %rax
	0x48, 0xff, 0xc7, //0x00001dc2 incq         %rdi
	0x31, 0xc9, //0x00001dc5 xorl         %ecx, %ecx
	0xe9, 0x29, 0x00, 0x00, 0x00, //0x00001dc7 jmp          LBB0_411
	0x90, 0x90, 0x90, 0x90, //0x00001dcc .p2align 4, 0x90
	//0x00001dd0 LBB0_409
	0x48, 0x85, 0xc0, //0x00001dd0 testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xf7, //0x00001dd3 cmovnel      %r15d, %r14d
	//0x00001dd7 LBB0_410
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00001dd7 movabsq      $-4294967296, %rax
	0x48, 0x01, 0xc3, //0x00001de1 addq         %rax, %rbx
	0x49, 0x8d, 0x43, 0xff, //0x00001de4 leaq         $-1(%r11), %rax
	0x48, 0xff, 0xcf, //0x00001de8 decq         %rdi
	0x48, 0x83, 0xff, 0x01, //0x00001deb cmpq         $1, %rdi
	0x0f, 0x8e, 0x49, 0x00, 0x00, 0x00, //0x00001def jle          LBB0_413
	//0x00001df5 LBB0_411
	0x49, 0x89, 0xc3, //0x00001df5 movq         %rax, %r11
	0x41, 0x0f, 0xb6, 0x74, 0x3c, 0xfe, //0x00001df8 movzbl       $-2(%r12,%rdi), %esi
	0x48, 0xc1, 0xe6, 0x3c, //0x00001dfe shlq         $60, %rsi
	0x48, 0x01, 0xce, //0x00001e02 addq         %rcx, %rsi
	0x48, 0x89, 0xf0, //0x00001e05 movq         %rsi, %rax
	0x48, 0xb9, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00001e08 movabsq      $-3689348814741910323, %rcx
	0x48, 0xf7, 0xe1, //0x00001e12 mulq         %rcx
	0x48, 0x89, 0xd1, //0x00001e15 movq         %rdx, %rcx
	0x48, 0xc1, 0xe9, 0x03, //0x00001e18 shrq         $3, %rcx
	0x48, 0x8d, 0x04, 0x09, //0x00001e1c leaq         (%rcx,%rcx), %rax
	0x48, 0x8d, 0x14, 0x80, //0x00001e20 leaq         (%rax,%rax,4), %rdx
	0x48, 0x89, 0xf0, //0x00001e24 movq         %rsi, %rax
	0x48, 0x29, 0xd0, //0x00001e27 subq         %rdx, %rax
	0x4d, 0x39, 0xeb, //0x00001e2a cmpq         %r13, %r11
	0x0f, 0x83, 0x9d, 0xff, 0xff, 0xff, //0x00001e2d jae          LBB0_409
	0x04, 0x30, //0x00001e33 addb         $48, %al
	0x43, 0x88, 0x04, 0x1c, //0x00001e35 movb         %al, (%r12,%r11)
	0xe9, 0x99, 0xff, 0xff, 0xff, //0x00001e39 jmp          LBB0_410
	//0x00001e3e LBB0_413
	0x48, 0x83, 0xfe, 0x0a, //0x00001e3e cmpq         $10, %rsi
	0x4c, 0x8b, 0x7d, 0xc8, //0x00001e42 movq         $-56(%rbp), %r15
	0x0f, 0x83, 0x1b, 0x00, 0x00, 0x00, //0x00001e46 jae          LBB0_416
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00001e4c movl         $1, %r11d
	0x48, 0x8d, 0x1d, 0xc7, 0x47, 0x00, 0x00, //0x00001e52 leaq         $18375(%rip), %rbx  /* _LSHIFT_TAB+0(%rip) */
	0xe9, 0x79, 0x00, 0x00, 0x00, //0x00001e59 jmp          LBB0_420
	//0x00001e5e LBB0_415
	0x4c, 0x8b, 0x7d, 0xc8, //0x00001e5e movq         $-56(%rbp), %r15
	0xe9, 0x70, 0x00, 0x00, 0x00, //0x00001e62 jmp          LBB0_420
	//0x00001e67 LBB0_416
	0x49, 0x63, 0xf3, //0x00001e67 movslq       %r11d, %rsi
	0x48, 0xff, 0xce, //0x00001e6a decq         %rsi
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00001e6d movl         $1, %r11d
	0x48, 0x8d, 0x1d, 0xa6, 0x47, 0x00, 0x00, //0x00001e73 leaq         $18342(%rip), %rbx  /* _LSHIFT_TAB+0(%rip) */
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00001e7a jmp          LBB0_418
	//0x00001e7f LBB0_417
	0x48, 0x85, 0xc0, //0x00001e7f testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xf3, //0x00001e82 cmovnel      %r11d, %r14d
	0x48, 0xff, 0xce, //0x00001e86 decq         %rsi
	0x48, 0x83, 0xf9, 0x09, //0x00001e89 cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x00001e8d movq         %rdx, %rcx
	0x0f, 0x86, 0x41, 0x00, 0x00, 0x00, //0x00001e90 jbe          LBB0_420
	//0x00001e96 LBB0_418
	0x48, 0x89, 0xc8, //0x00001e96 movq         %rcx, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00001e99 movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x00001ea3 mulq         %rdx
	0x48, 0xc1, 0xea, 0x03, //0x00001ea6 shrq         $3, %rdx
	0x48, 0x8d, 0x04, 0x12, //0x00001eaa leaq         (%rdx,%rdx), %rax
	0x48, 0x8d, 0x3c, 0x80, //0x00001eae leaq         (%rax,%rax,4), %rdi
	0x48, 0x89, 0xc8, //0x00001eb2 movq         %rcx, %rax
	0x48, 0x29, 0xf8, //0x00001eb5 subq         %rdi, %rax
	0x4c, 0x39, 0xee, //0x00001eb8 cmpq         %r13, %rsi
	0x0f, 0x83, 0xbe, 0xff, 0xff, 0xff, //0x00001ebb jae          LBB0_417
	0x04, 0x30, //0x00001ec1 addb         $48, %al
	0x41, 0x88, 0x04, 0x34, //0x00001ec3 movb         %al, (%r12,%rsi)
	0x48, 0xff, 0xce, //0x00001ec7 decq         %rsi
	0x48, 0x83, 0xf9, 0x09, //0x00001eca cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x00001ece movq         %rdx, %rcx
	0x0f, 0x87, 0xbf, 0xff, 0xff, 0xff, //0x00001ed1 ja           LBB0_418
	//0x00001ed7 LBB0_420
	0x45, 0x01, 0xc8, //0x00001ed7 addl         %r9d, %r8d
	0x4d, 0x63, 0xc0, //0x00001eda movslq       %r8d, %r8
	0x4d, 0x39, 0xc5, //0x00001edd cmpq         %r8, %r13
	0x45, 0x0f, 0x46, 0xc5, //0x00001ee0 cmovbel      %r13d, %r8d
	0x45, 0x01, 0xcf, //0x00001ee4 addl         %r9d, %r15d
	0x45, 0x85, 0xc0, //0x00001ee7 testl        %r8d, %r8d
	0x0f, 0x8e, 0x47, 0xfe, 0xff, 0xff, //0x00001eea jle          LBB0_398
	0x44, 0x89, 0xc0, //0x00001ef0 movl         %r8d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x00001ef3 cmpb         $48, $-1(%rax,%r12)
	0x0f, 0x85, 0x3f, 0xfe, 0xff, 0xff, //0x00001ef9 jne          LBB0_399
	//0x00001eff LBB0_422
	0x48, 0x83, 0xf8, 0x01, //0x00001eff cmpq         $1, %rax
	0x0f, 0x8e, 0x29, 0xfe, 0xff, 0xff, //0x00001f03 jle          LBB0_397
	0x4c, 0x8d, 0x40, 0xff, //0x00001f09 leaq         $-1(%rax), %r8
	0x41, 0x80, 0x7c, 0x04, 0xfe, 0x30, //0x00001f0d cmpb         $48, $-2(%r12,%rax)
	0x4c, 0x89, 0xc0, //0x00001f13 movq         %r8, %rax
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00001f16 je           LBB0_422
	0xe9, 0x1d, 0xfe, 0xff, 0xff, //0x00001f1c jmp          LBB0_399
	//0x00001f21 LBB0_424
	0x0f, 0x8c, 0x78, 0xfe, 0xff, 0xff, //0x00001f21 jl           LBB0_406
	0xe9, 0x79, 0xfe, 0xff, 0xff, //0x00001f27 jmp          LBB0_407
	//0x00001f2c LBB0_425
	0x44, 0x89, 0xc0, //0x00001f2c movl         %r8d, %eax
	0x44, 0x89, 0xc3, //0x00001f2f movl         %r8d, %ebx
	0x45, 0x85, 0xc9, //0x00001f32 testl        %r9d, %r9d
	0x0f, 0x84, 0x01, 0xfc, 0xff, 0xff, //0x00001f35 je           LBB0_365
	//0x00001f3b LBB0_426
	0x41, 0xf7, 0xd9, //0x00001f3b negl         %r9d
	0x48, 0x63, 0xf8, //0x00001f3e movslq       %eax, %rdi
	0x31, 0xf6, //0x00001f41 xorl         %esi, %esi
	0x31, 0xd2, //0x00001f43 xorl         %edx, %edx
	//0x00001f45 LBB0_427
	0x48, 0x39, 0xfe, //0x00001f45 cmpq         %rdi, %rsi
	0x0f, 0x8d, 0x0f, 0x01, 0x00, 0x00, //0x00001f48 jge          LBB0_443
	0x48, 0x8d, 0x0c, 0x92, //0x00001f4e leaq         (%rdx,%rdx,4), %rcx
	0x49, 0x0f, 0xbe, 0x14, 0x34, //0x00001f52 movsbq       (%r12,%rsi), %rdx
	0x48, 0x8d, 0x54, 0x4a, 0xd0, //0x00001f57 leaq         $-48(%rdx,%rcx,2), %rdx
	0x48, 0xff, 0xc6, //0x00001f5c incq         %rsi
	0x48, 0x89, 0xd3, //0x00001f5f movq         %rdx, %rbx
	0x44, 0x89, 0xc9, //0x00001f62 movl         %r9d, %ecx
	0x48, 0xd3, 0xeb, //0x00001f65 shrq         %cl, %rbx
	0x48, 0x85, 0xdb, //0x00001f68 testq        %rbx, %rbx
	0x0f, 0x84, 0xd4, 0xff, 0xff, 0xff, //0x00001f6b je           LBB0_427
	//0x00001f71 LBB0_429
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00001f71 movq         $-1, %rdi
	0x44, 0x89, 0xc9, //0x00001f78 movl         %r9d, %ecx
	0x48, 0xd3, 0xe7, //0x00001f7b shlq         %cl, %rdi
	0x48, 0xf7, 0xd7, //0x00001f7e notq         %rdi
	0x31, 0xdb, //0x00001f81 xorl         %ebx, %ebx
	0x39, 0xc6, //0x00001f83 cmpl         %eax, %esi
	0x0f, 0x8d, 0x4b, 0x00, 0x00, 0x00, //0x00001f85 jge          LBB0_433
	0x4c, 0x89, 0x7d, 0xc8, //0x00001f8b movq         %r15, $-56(%rbp)
	0x4c, 0x63, 0xde, //0x00001f8f movslq       %esi, %r11
	0x4d, 0x63, 0xc0, //0x00001f92 movslq       %r8d, %r8
	0x4f, 0x8d, 0x3c, 0x1c, //0x00001f95 leaq         (%r12,%r11), %r15
	0x31, 0xdb, //0x00001f99 xorl         %ebx, %ebx
	//0x00001f9b LBB0_431
	0x48, 0x89, 0xd0, //0x00001f9b movq         %rdx, %rax
	0x44, 0x89, 0xc9, //0x00001f9e movl         %r9d, %ecx
	0x48, 0xd3, 0xe8, //0x00001fa1 shrq         %cl, %rax
	0x48, 0x21, 0xfa, //0x00001fa4 andq         %rdi, %rdx
	0x04, 0x30, //0x00001fa7 addb         $48, %al
	0x41, 0x88, 0x04, 0x1c, //0x00001fa9 movb         %al, (%r12,%rbx)
	0x48, 0x8d, 0x04, 0x92, //0x00001fad leaq         (%rdx,%rdx,4), %rax
	0x49, 0x0f, 0xbe, 0x0c, 0x1f, //0x00001fb1 movsbq       (%r15,%rbx), %rcx
	0x48, 0x8d, 0x54, 0x41, 0xd0, //0x00001fb6 leaq         $-48(%rcx,%rax,2), %rdx
	0x49, 0x8d, 0x44, 0x1b, 0x01, //0x00001fbb leaq         $1(%r11,%rbx), %rax
	0x48, 0xff, 0xc3, //0x00001fc0 incq         %rbx
	0x4c, 0x39, 0xc0, //0x00001fc3 cmpq         %r8, %rax
	0x0f, 0x8c, 0xcf, 0xff, 0xff, 0xff, //0x00001fc6 jl           LBB0_431
	0x4c, 0x8b, 0x7d, 0xc8, //0x00001fcc movq         $-56(%rbp), %r15
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00001fd0 movl         $1, %r11d
	//0x00001fd6 LBB0_433
	0x41, 0x29, 0xf7, //0x00001fd6 subl         %esi, %r15d
	0x41, 0x89, 0xd8, //0x00001fd9 movl         %ebx, %r8d
	0xe9, 0x0e, 0x00, 0x00, 0x00, //0x00001fdc jmp          LBB0_436
	//0x00001fe1 LBB0_434
	0x48, 0x85, 0xc0, //0x00001fe1 testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xf3, //0x00001fe4 cmovnel      %r11d, %r14d
	//0x00001fe8 LBB0_435
	0x48, 0x01, 0xd2, //0x00001fe8 addq         %rdx, %rdx
	0x48, 0x8d, 0x14, 0x92, //0x00001feb leaq         (%rdx,%rdx,4), %rdx
	//0x00001fef LBB0_436
	0x48, 0x85, 0xd2, //0x00001fef testq        %rdx, %rdx
	0x0f, 0x84, 0x28, 0x00, 0x00, 0x00, //0x00001ff2 je           LBB0_439
	0x48, 0x89, 0xd0, //0x00001ff8 movq         %rdx, %rax
	0x44, 0x89, 0xc9, //0x00001ffb movl         %r9d, %ecx
	0x48, 0xd3, 0xe8, //0x00001ffe shrq         %cl, %rax
	0x48, 0x21, 0xfa, //0x00002001 andq         %rdi, %rdx
	0x49, 0x63, 0xc8, //0x00002004 movslq       %r8d, %rcx
	0x49, 0x39, 0xcd, //0x00002007 cmpq         %rcx, %r13
	0x0f, 0x86, 0xd1, 0xff, 0xff, 0xff, //0x0000200a jbe          LBB0_434
	0x04, 0x30, //0x00002010 addb         $48, %al
	0x41, 0x88, 0x04, 0x0c, //0x00002012 movb         %al, (%r12,%rcx)
	0xff, 0xc1, //0x00002016 incl         %ecx
	0x41, 0x89, 0xc8, //0x00002018 movl         %ecx, %r8d
	0xe9, 0xc8, 0xff, 0xff, 0xff, //0x0000201b jmp          LBB0_435
	//0x00002020 LBB0_439
	0x41, 0xff, 0xc7, //0x00002020 incl         %r15d
	0x45, 0x85, 0xc0, //0x00002023 testl        %r8d, %r8d
	0x0f, 0x8e, 0x44, 0x02, 0x00, 0x00, //0x00002026 jle          LBB0_473
	0x44, 0x89, 0xc0, //0x0000202c movl         %r8d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x0000202f cmpb         $48, $-1(%rax,%r12)
	0x0f, 0x85, 0x3e, 0x02, 0x00, 0x00, //0x00002035 jne          LBB0_474
	//0x0000203b LBB0_441
	0x48, 0x83, 0xf8, 0x01, //0x0000203b cmpq         $1, %rax
	0x0f, 0x8e, 0x26, 0x02, 0x00, 0x00, //0x0000203f jle          LBB0_472
	0x4c, 0x8d, 0x40, 0xff, //0x00002045 leaq         $-1(%rax), %r8
	0x41, 0x80, 0x7c, 0x04, 0xfe, 0x30, //0x00002049 cmpb         $48, $-2(%r12,%rax)
	0x4c, 0x89, 0xc0, //0x0000204f movq         %r8, %rax
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00002052 je           LBB0_441
	0xe9, 0x1c, 0x02, 0x00, 0x00, //0x00002058 jmp          LBB0_474
	//0x0000205d LBB0_443
	0x48, 0x85, 0xd2, //0x0000205d testq        %rdx, %rdx
	0x0f, 0x84, 0x3c, 0x01, 0x00, 0x00, //0x00002060 je           LBB0_462
	//0x00002066 LBB0_444
	0x48, 0x89, 0xd7, //0x00002066 movq         %rdx, %rdi
	0x44, 0x89, 0xc9, //0x00002069 movl         %r9d, %ecx
	0x48, 0xd3, 0xef, //0x0000206c shrq         %cl, %rdi
	0x48, 0x85, 0xff, //0x0000206f testq        %rdi, %rdi
	0x0f, 0x85, 0xf9, 0xfe, 0xff, 0xff, //0x00002072 jne          LBB0_429
	0x48, 0x01, 0xd2, //0x00002078 addq         %rdx, %rdx
	0x48, 0x8d, 0x14, 0x92, //0x0000207b leaq         (%rdx,%rdx,4), %rdx
	0xff, 0xc6, //0x0000207f incl         %esi
	0xe9, 0xe0, 0xff, 0xff, 0xff, //0x00002081 jmp          LBB0_444
	//0x00002086 LBB0_446
	0x44, 0x89, 0xc0, //0x00002086 movl         %r8d, %eax
	0x44, 0x89, 0xc3, //0x00002089 movl         %r8d, %ebx
	0x45, 0x85, 0xc9, //0x0000208c testl        %r9d, %r9d
	0x0f, 0x84, 0xa7, 0xfa, 0xff, 0xff, //0x0000208f je           LBB0_365
	//0x00002095 LBB0_447
	0x44, 0x89, 0xc9, //0x00002095 movl         %r9d, %ecx
	0x48, 0x6b, 0xf1, 0x68, //0x00002098 imulq        $104, %rcx, %rsi
	0x48, 0x8d, 0x3d, 0x7d, 0x45, 0x00, 0x00, //0x0000209c leaq         $17789(%rip), %rdi  /* _LSHIFT_TAB+0(%rip) */
	0x8b, 0x1c, 0x3e, //0x000020a3 movl         (%rsi,%rdi), %ebx
	0x4c, 0x63, 0xd8, //0x000020a6 movslq       %eax, %r11
	0x8a, 0x54, 0x3e, 0x04, //0x000020a9 movb         $4(%rsi,%rdi), %dl
	0x45, 0x85, 0xdb, //0x000020ad testl        %r11d, %r11d
	0x0f, 0x84, 0x29, 0x00, 0x00, 0x00, //0x000020b0 je           LBB0_452
	0x48, 0x8d, 0x74, 0x3e, 0x05, //0x000020b6 leaq         $5(%rsi,%rdi), %rsi
	0x31, 0xff, //0x000020bb xorl         %edi, %edi
	//0x000020bd LBB0_449
	0x84, 0xd2, //0x000020bd testb        %dl, %dl
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x000020bf je           LBB0_454
	0x41, 0x38, 0x14, 0x3c, //0x000020c5 cmpb         %dl, (%r12,%rdi)
	0x0f, 0x85, 0xb2, 0x01, 0x00, 0x00, //0x000020c9 jne          LBB0_475
	0x0f, 0xb6, 0x14, 0x3e, //0x000020cf movzbl       (%rsi,%rdi), %edx
	0x48, 0xff, 0xc7, //0x000020d3 incq         %rdi
	0x49, 0x39, 0xfb, //0x000020d6 cmpq         %rdi, %r11
	0x0f, 0x85, 0xde, 0xff, 0xff, 0xff, //0x000020d9 jne          LBB0_449
	//0x000020df LBB0_452
	0x84, 0xd2, //0x000020df testb        %dl, %dl
	0x0f, 0x84, 0x02, 0x00, 0x00, 0x00, //0x000020e1 je           LBB0_454
	//0x000020e7 LBB0_453
	0xff, 0xcb, //0x000020e7 decl         %ebx
	//0x000020e9 LBB0_454
	0x85, 0xc0, //0x000020e9 testl        %eax, %eax
	0x0f, 0x8e, 0xbb, 0x00, 0x00, 0x00, //0x000020eb jle          LBB0_463
	0x4c, 0x89, 0x7d, 0xc8, //0x000020f1 movq         %r15, $-56(%rbp)
	0x89, 0x5d, 0xc0, //0x000020f5 movl         %ebx, $-64(%rbp)
	0x01, 0xd8, //0x000020f8 addl         %ebx, %eax
	0x48, 0x98, //0x000020fa cltq         
	0x48, 0x89, 0xc6, //0x000020fc movq         %rax, %rsi
	0x48, 0xc1, 0xe6, 0x20, //0x000020ff shlq         $32, %rsi
	0x48, 0xff, 0xc8, //0x00002103 decq         %rax
	0x49, 0xff, 0xc3, //0x00002106 incq         %r11
	0x31, 0xff, //0x00002109 xorl         %edi, %edi
	0xe9, 0x2a, 0x00, 0x00, 0x00, //0x0000210b jmp          LBB0_458
	//0x00002110 LBB0_456
	0x48, 0x85, 0xc0, //0x00002110 testq        %rax, %rax
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00002113 movl         $1, %eax
	0x44, 0x0f, 0x45, 0xf0, //0x00002118 cmovnel      %eax, %r14d
	//0x0000211c LBB0_457
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x0000211c movabsq      $-4294967296, %rax
	0x48, 0x01, 0xc6, //0x00002126 addq         %rax, %rsi
	0x49, 0x8d, 0x47, 0xff, //0x00002129 leaq         $-1(%r15), %rax
	0x49, 0xff, 0xcb, //0x0000212d decq         %r11
	0x49, 0x83, 0xfb, 0x01, //0x00002130 cmpq         $1, %r11
	0x0f, 0x8e, 0x4c, 0x00, 0x00, 0x00, //0x00002134 jle          LBB0_460
	//0x0000213a LBB0_458
	0x49, 0x89, 0xc7, //0x0000213a movq         %rax, %r15
	0x4b, 0x0f, 0xbe, 0x5c, 0x1c, 0xfe, //0x0000213d movsbq       $-2(%r12,%r11), %rbx
	0x48, 0x83, 0xc3, 0xd0, //0x00002143 addq         $-48, %rbx
	0x48, 0xd3, 0xe3, //0x00002147 shlq         %cl, %rbx
	0x48, 0x01, 0xfb, //0x0000214a addq         %rdi, %rbx
	0x48, 0x89, 0xd8, //0x0000214d movq         %rbx, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00002150 movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x0000215a mulq         %rdx
	0x48, 0x89, 0xd7, //0x0000215d movq         %rdx, %rdi
	0x48, 0xc1, 0xef, 0x03, //0x00002160 shrq         $3, %rdi
	0x48, 0x8d, 0x04, 0x3f, //0x00002164 leaq         (%rdi,%rdi), %rax
	0x48, 0x8d, 0x14, 0x80, //0x00002168 leaq         (%rax,%rax,4), %rdx
	0x48, 0x89, 0xd8, //0x0000216c movq         %rbx, %rax
	0x48, 0x29, 0xd0, //0x0000216f subq         %rdx, %rax
	0x4d, 0x39, 0xef, //0x00002172 cmpq         %r13, %r15
	0x0f, 0x83, 0x95, 0xff, 0xff, 0xff, //0x00002175 jae          LBB0_456
	0x04, 0x30, //0x0000217b addb         $48, %al
	0x43, 0x88, 0x04, 0x3c, //0x0000217d movb         %al, (%r12,%r15)
	0xe9, 0x96, 0xff, 0xff, 0xff, //0x00002181 jmp          LBB0_457
	//0x00002186 LBB0_460
	0x48, 0x83, 0xfb, 0x0a, //0x00002186 cmpq         $10, %rbx
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x0000218a movl         $1, %r11d
	0x0f, 0x83, 0x21, 0x00, 0x00, 0x00, //0x00002190 jae          LBB0_464
	0x4c, 0x8b, 0x7d, 0xc8, //0x00002196 movq         $-56(%rbp), %r15
	0x8b, 0x5d, 0xc0, //0x0000219a movl         $-64(%rbp), %ebx
	0xe9, 0x7f, 0x00, 0x00, 0x00, //0x0000219d jmp          LBB0_468
	//0x000021a2 LBB0_462
	0x45, 0x31, 0xc0, //0x000021a2 xorl         %r8d, %r8d
	0x31, 0xdb, //0x000021a5 xorl         %ebx, %ebx
	0xe9, 0x90, 0xf9, 0xff, 0xff, //0x000021a7 jmp          LBB0_365
	//0x000021ac LBB0_463
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x000021ac movl         $1, %r11d
	0xe9, 0x6a, 0x00, 0x00, 0x00, //0x000021b2 jmp          LBB0_468
	//0x000021b7 LBB0_464
	0x49, 0x63, 0xcf, //0x000021b7 movslq       %r15d, %rcx
	0x48, 0xff, 0xc9, //0x000021ba decq         %rcx
	0x4c, 0x8b, 0x7d, 0xc8, //0x000021bd movq         $-56(%rbp), %r15
	0x8b, 0x5d, 0xc0, //0x000021c1 movl         $-64(%rbp), %ebx
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x000021c4 jmp          LBB0_466
	//0x000021c9 LBB0_465
	0x48, 0x85, 0xc0, //0x000021c9 testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xf3, //0x000021cc cmovnel      %r11d, %r14d
	0x48, 0xff, 0xc9, //0x000021d0 decq         %rcx
	0x48, 0x83, 0xff, 0x09, //0x000021d3 cmpq         $9, %rdi
	0x48, 0x89, 0xd7, //0x000021d7 movq         %rdx, %rdi
	0x0f, 0x86, 0x41, 0x00, 0x00, 0x00, //0x000021da jbe          LBB0_468
	//0x000021e0 LBB0_466
	0x48, 0x89, 0xf8, //0x000021e0 movq         %rdi, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x000021e3 movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x000021ed mulq         %rdx
	0x48, 0xc1, 0xea, 0x03, //0x000021f0 shrq         $3, %rdx
	0x48, 0x8d, 0x04, 0x12, //0x000021f4 leaq         (%rdx,%rdx), %rax
	0x48, 0x8d, 0x34, 0x80, //0x000021f8 leaq         (%rax,%rax,4), %rsi
	0x48, 0x89, 0xf8, //0x000021fc movq         %rdi, %rax
	0x48, 0x29, 0xf0, //0x000021ff subq         %rsi, %rax
	0x4c, 0x39, 0xe9, //0x00002202 cmpq         %r13, %rcx
	0x0f, 0x83, 0xbe, 0xff, 0xff, 0xff, //0x00002205 jae          LBB0_465
	0x04, 0x30, //0x0000220b addb         $48, %al
	0x41, 0x88, 0x04, 0x0c, //0x0000220d movb         %al, (%r12,%rcx)
	0x48, 0xff, 0xc9, //0x00002211 decq         %rcx
	0x48, 0x83, 0xff, 0x09, //0x00002214 cmpq         $9, %rdi
	0x48, 0x89, 0xd7, //0x00002218 movq         %rdx, %rdi
	0x0f, 0x87, 0xbf, 0xff, 0xff, 0xff, //0x0000221b ja           LBB0_466
	//0x00002221 LBB0_468
	0x41, 0x01, 0xd8, //0x00002221 addl         %ebx, %r8d
	0x4d, 0x63, 0xc0, //0x00002224 movslq       %r8d, %r8
	0x4d, 0x39, 0xc5, //0x00002227 cmpq         %r8, %r13
	0x45, 0x0f, 0x46, 0xc5, //0x0000222a cmovbel      %r13d, %r8d
	0x41, 0x01, 0xdf, //0x0000222e addl         %ebx, %r15d
	0x45, 0x85, 0xc0, //0x00002231 testl        %r8d, %r8d
	0x0f, 0x8e, 0x57, 0x00, 0x00, 0x00, //0x00002234 jle          LBB0_477
	0x44, 0x89, 0xc0, //0x0000223a movl         %r8d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x0000223d cmpb         $48, $-1(%rax,%r12)
	0x0f, 0x85, 0x4f, 0x00, 0x00, 0x00, //0x00002243 jne          LBB0_478
	//0x00002249 LBB0_470
	0x48, 0x83, 0xf8, 0x01, //0x00002249 cmpq         $1, %rax
	0x0f, 0x8e, 0x39, 0x00, 0x00, 0x00, //0x0000224d jle          LBB0_476
	0x4c, 0x8d, 0x40, 0xff, //0x00002253 leaq         $-1(%rax), %r8
	0x41, 0x80, 0x7c, 0x04, 0xfe, 0x30, //0x00002257 cmpb         $48, $-2(%r12,%rax)
	0x4c, 0x89, 0xc0, //0x0000225d movq         %r8, %rax
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00002260 je           LBB0_470
	0xe9, 0x2d, 0x00, 0x00, 0x00, //0x00002266 jmp          LBB0_478
	//0x0000226b LBB0_472
	0xff, 0xc8, //0x0000226b decl         %eax
	0x41, 0x89, 0xc0, //0x0000226d movl         %eax, %r8d
	//0x00002270 LBB0_473
	0x45, 0x85, 0xc0, //0x00002270 testl        %r8d, %r8d
	0x0f, 0x84, 0xdd, 0x0b, 0x00, 0x00, //0x00002273 je           LBB0_659
	//0x00002279 LBB0_474
	0x44, 0x89, 0xc3, //0x00002279 movl         %r8d, %ebx
	0xe9, 0xbb, 0xf8, 0xff, 0xff, //0x0000227c jmp          LBB0_365
	//0x00002281 LBB0_475
	0x0f, 0x8c, 0x60, 0xfe, 0xff, 0xff, //0x00002281 jl           LBB0_453
	0xe9, 0x5d, 0xfe, 0xff, 0xff, //0x00002287 jmp          LBB0_454
	//0x0000228c LBB0_476
	0xff, 0xc8, //0x0000228c decl         %eax
	0x41, 0x89, 0xc0, //0x0000228e movl         %eax, %r8d
	//0x00002291 LBB0_477
	0x45, 0x85, 0xc0, //0x00002291 testl        %r8d, %r8d
	0x45, 0x0f, 0x44, 0xf8, //0x00002294 cmovel       %r8d, %r15d
	//0x00002298 LBB0_478
	0x44, 0x89, 0xc0, //0x00002298 movl         %r8d, %eax
	0x44, 0x89, 0xc3, //0x0000229b movl         %r8d, %ebx
	0x45, 0x85, 0xc9, //0x0000229e testl        %r9d, %r9d
	0x0f, 0x89, 0x95, 0xf8, 0xff, 0xff, //0x000022a1 jns          LBB0_365
	0xe9, 0xe1, 0xf8, 0xff, 0xff, //0x000022a7 jmp          LBB0_371
	//0x000022ac LBB0_479
	0x44, 0x89, 0xc3, //0x000022ac movl         %r8d, %ebx
	//0x000022af LBB0_480
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x000022af movl         $1, %r11d
	0x48, 0x8d, 0x15, 0x64, 0x43, 0x00, 0x00, //0x000022b5 leaq         $17252(%rip), %rdx  /* _LSHIFT_TAB+0(%rip) */
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x000022bc jmp          LBB0_483
	//0x000022c1 LBB0_481
	0x89, 0xd8, //0x000022c1 movl         %ebx, %eax
	//0x000022c3 LBB0_482
	0x48, 0x8b, 0x75, 0xb0, //0x000022c3 movq         $-80(%rbp), %rsi
	0x2b, 0x75, 0xa8, //0x000022c7 subl         $-88(%rbp), %esi
	0x89, 0xc3, //0x000022ca movl         %eax, %ebx
	//0x000022cc LBB0_483
	0x45, 0x85, 0xff, //0x000022cc testl        %r15d, %r15d
	0x0f, 0x88, 0x16, 0x00, 0x00, 0x00, //0x000022cf js           LBB0_486
	0x0f, 0x85, 0xc8, 0x07, 0x00, 0x00, //0x000022d5 jne          LBB0_603
	0x41, 0x80, 0x3c, 0x24, 0x35, //0x000022db cmpb         $53, (%r12)
	0x0f, 0x8c, 0x14, 0x00, 0x00, 0x00, //0x000022e0 jl           LBB0_487
	0xe9, 0xb8, 0x07, 0x00, 0x00, //0x000022e6 jmp          LBB0_603
	//0x000022eb LBB0_486
	0xb8, 0x1b, 0x00, 0x00, 0x00, //0x000022eb movl         $27, %eax
	0x41, 0x83, 0xff, 0xf8, //0x000022f0 cmpl         $-8, %r15d
	0x0f, 0x8c, 0x11, 0x00, 0x00, 0x00, //0x000022f4 jl           LBB0_488
	//0x000022fa LBB0_487
	0x44, 0x89, 0xf8, //0x000022fa movl         %r15d, %eax
	0xf7, 0xd8, //0x000022fd negl         %eax
	0x48, 0x98, //0x000022ff cltq         
	0x48, 0x8d, 0x0d, 0xe8, 0x42, 0x00, 0x00, //0x00002301 leaq         $17128(%rip), %rcx  /* _POW_TAB+0(%rip) */
	0x8b, 0x04, 0x81, //0x00002308 movl         (%rcx,%rax,4), %eax
	//0x0000230b LBB0_488
	0x85, 0xdb, //0x0000230b testl        %ebx, %ebx
	0x48, 0x89, 0x75, 0xb0, //0x0000230d movq         %rsi, $-80(%rbp)
	0x89, 0x45, 0xa8, //0x00002311 movl         %eax, $-88(%rbp)
	0x0f, 0x84, 0xa7, 0xff, 0xff, 0xff, //0x00002314 je           LBB0_481
	0x85, 0xc0, //0x0000231a testl        %eax, %eax
	0x0f, 0x84, 0x9f, 0xff, 0xff, 0xff, //0x0000231c je           LBB0_481
	0x0f, 0x8e, 0x2f, 0x02, 0x00, 0x00, //0x00002322 jle          LBB0_522
	0x41, 0x89, 0xc1, //0x00002328 movl         %eax, %r9d
	0x83, 0xf8, 0x3d, //0x0000232b cmpl         $61, %eax
	0x0f, 0x8d, 0x27, 0x00, 0x00, 0x00, //0x0000232e jge          LBB0_495
	0xe9, 0x35, 0x02, 0x00, 0x00, //0x00002334 jmp          LBB0_524
	//0x00002339 LBB0_492
	0x48, 0x8d, 0x15, 0xe0, 0x42, 0x00, 0x00, //0x00002339 leaq         $17120(%rip), %rdx  /* _LSHIFT_TAB+0(%rip) */
	//0x00002340 LBB0_493
	0x45, 0x85, 0xc0, //0x00002340 testl        %r8d, %r8d
	0x45, 0x0f, 0x44, 0xf8, //0x00002343 cmovel       %r8d, %r15d
	//0x00002347 LBB0_494
	0x48, 0x8b, 0x45, 0xc0, //0x00002347 movq         $-64(%rbp), %rax
	0x44, 0x8d, 0x48, 0xc4, //0x0000234b leal         $-60(%rax), %r9d
	0x44, 0x89, 0xc3, //0x0000234f movl         %r8d, %ebx
	0x83, 0xf8, 0x78, //0x00002352 cmpl         $120, %eax
	0x0f, 0x8e, 0x04, 0x02, 0x00, 0x00, //0x00002355 jle          LBB0_523
	//0x0000235b LBB0_495
	0x4c, 0x89, 0x7d, 0xc8, //0x0000235b movq         %r15, $-56(%rbp)
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x0000235f movl         $1, %r11d
	0x4c, 0x89, 0x4d, 0xc0, //0x00002365 movq         %r9, $-64(%rbp)
	0x4c, 0x63, 0xfb, //0x00002369 movslq       %ebx, %r15
	0x45, 0x85, 0xff, //0x0000236c testl        %r15d, %r15d
	0x0f, 0x84, 0x41, 0x00, 0x00, 0x00, //0x0000236f je           LBB0_501
	0xb1, 0x38, //0x00002375 movb         $56, %cl
	0x31, 0xc0, //0x00002377 xorl         %eax, %eax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002379 .p2align 4, 0x90
	//0x00002380 LBB0_497
	0x41, 0xb9, 0x13, 0x00, 0x00, 0x00, //0x00002380 movl         $19, %r9d
	0x48, 0x83, 0xf8, 0x2a, //0x00002386 cmpq         $42, %rax
	0x0f, 0x84, 0x2c, 0x00, 0x00, 0x00, //0x0000238a je           LBB0_502
	0x41, 0x38, 0x0c, 0x04, //0x00002390 cmpb         %cl, (%r12,%rax)
	0x0f, 0x85, 0xa8, 0x01, 0x00, 0x00, //0x00002394 jne          LBB0_519
	0x0f, 0xb6, 0x8c, 0x10, 0x65, 0x18, 0x00, 0x00, //0x0000239a movzbl       $6245(%rax,%rdx), %ecx
	0x48, 0xff, 0xc0, //0x000023a2 incq         %rax
	0x49, 0x39, 0xc7, //0x000023a5 cmpq         %rax, %r15
	0x0f, 0x85, 0xd2, 0xff, 0xff, 0xff, //0x000023a8 jne          LBB0_497
	0x84, 0xc9, //0x000023ae testb        %cl, %cl
	0x0f, 0x84, 0x06, 0x00, 0x00, 0x00, //0x000023b0 je           LBB0_502
	//0x000023b6 LBB0_501
	0x41, 0xb9, 0x12, 0x00, 0x00, 0x00, //0x000023b6 movl         $18, %r9d
	//0x000023bc LBB0_502
	0x85, 0xdb, //0x000023bc testl        %ebx, %ebx
	0x0f, 0x8e, 0xa3, 0x00, 0x00, 0x00, //0x000023be jle          LBB0_510
	0x44, 0x01, 0xcb, //0x000023c4 addl         %r9d, %ebx
	0x48, 0x63, 0xc3, //0x000023c7 movslq       %ebx, %rax
	0x48, 0x89, 0xc3, //0x000023ca movq         %rax, %rbx
	0x48, 0xc1, 0xe3, 0x20, //0x000023cd shlq         $32, %rbx
	0x48, 0xff, 0xc8, //0x000023d1 decq         %rax
	0x49, 0xff, 0xc7, //0x000023d4 incq         %r15
	0x31, 0xc9, //0x000023d7 xorl         %ecx, %ecx
	0xe9, 0x27, 0x00, 0x00, 0x00, //0x000023d9 jmp          LBB0_506
	0x90, 0x90, //0x000023de .p2align 4, 0x90
	//0x000023e0 LBB0_504
	0x48, 0x85, 0xc0, //0x000023e0 testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xf3, //0x000023e3 cmovnel      %r11d, %r14d
	//0x000023e7 LBB0_505
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x000023e7 movabsq      $-4294967296, %rax
	0x48, 0x01, 0xc3, //0x000023f1 addq         %rax, %rbx
	0x48, 0x8d, 0x47, 0xff, //0x000023f4 leaq         $-1(%rdi), %rax
	0x49, 0xff, 0xcf, //0x000023f8 decq         %r15
	0x49, 0x83, 0xff, 0x01, //0x000023fb cmpq         $1, %r15
	0x0f, 0x8e, 0x49, 0x00, 0x00, 0x00, //0x000023ff jle          LBB0_508
	//0x00002405 LBB0_506
	0x48, 0x89, 0xc7, //0x00002405 movq         %rax, %rdi
	0x43, 0x0f, 0xb6, 0x74, 0x3c, 0xfe, //0x00002408 movzbl       $-2(%r12,%r15), %esi
	0x48, 0xc1, 0xe6, 0x3c, //0x0000240e shlq         $60, %rsi
	0x48, 0x01, 0xce, //0x00002412 addq         %rcx, %rsi
	0x48, 0x89, 0xf0, //0x00002415 movq         %rsi, %rax
	0x48, 0xb9, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00002418 movabsq      $-3689348814741910323, %rcx
	0x48, 0xf7, 0xe1, //0x00002422 mulq         %rcx
	0x48, 0x89, 0xd1, //0x00002425 movq         %rdx, %rcx
	0x48, 0xc1, 0xe9, 0x03, //0x00002428 shrq         $3, %rcx
	0x48, 0x8d, 0x04, 0x09, //0x0000242c leaq         (%rcx,%rcx), %rax
	0x48, 0x8d, 0x14, 0x80, //0x00002430 leaq         (%rax,%rax,4), %rdx
	0x48, 0x89, 0xf0, //0x00002434 movq         %rsi, %rax
	0x48, 0x29, 0xd0, //0x00002437 subq         %rdx, %rax
	0x4c, 0x39, 0xef, //0x0000243a cmpq         %r13, %rdi
	0x0f, 0x83, 0x9d, 0xff, 0xff, 0xff, //0x0000243d jae          LBB0_504
	0x04, 0x30, //0x00002443 addb         $48, %al
	0x41, 0x88, 0x04, 0x3c, //0x00002445 movb         %al, (%r12,%rdi)
	0xe9, 0x99, 0xff, 0xff, 0xff, //0x00002449 jmp          LBB0_505
	//0x0000244e LBB0_508
	0x48, 0x83, 0xfe, 0x0a, //0x0000244e cmpq         $10, %rsi
	0x4c, 0x8b, 0x7d, 0xc8, //0x00002452 movq         $-56(%rbp), %r15
	0x0f, 0x83, 0x1a, 0x00, 0x00, 0x00, //0x00002456 jae          LBB0_511
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x0000245c movl         $1, %r11d
	0xe9, 0x81, 0x00, 0x00, 0x00, //0x00002462 jmp          LBB0_515
	//0x00002467 LBB0_510
	0x4c, 0x8b, 0x7d, 0xc8, //0x00002467 movq         $-56(%rbp), %r15
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x0000246b movl         $1, %r11d
	0xe9, 0x72, 0x00, 0x00, 0x00, //0x00002471 jmp          LBB0_515
	//0x00002476 LBB0_511
	0x48, 0x63, 0xf7, //0x00002476 movslq       %edi, %rsi
	0x48, 0xff, 0xce, //0x00002479 decq         %rsi
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x0000247c movl         $1, %r11d
	0xe9, 0x20, 0x00, 0x00, 0x00, //0x00002482 jmp          LBB0_513
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002487 .p2align 4, 0x90
	//0x00002490 LBB0_512
	0x48, 0x85, 0xc0, //0x00002490 testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xf3, //0x00002493 cmovnel      %r11d, %r14d
	0x48, 0xff, 0xce, //0x00002497 decq         %rsi
	0x48, 0x83, 0xf9, 0x09, //0x0000249a cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x0000249e movq         %rdx, %rcx
	0x0f, 0x86, 0x41, 0x00, 0x00, 0x00, //0x000024a1 jbe          LBB0_515
	//0x000024a7 LBB0_513
	0x48, 0x89, 0xc8, //0x000024a7 movq         %rcx, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x000024aa movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x000024b4 mulq         %rdx
	0x48, 0xc1, 0xea, 0x03, //0x000024b7 shrq         $3, %rdx
	0x48, 0x8d, 0x04, 0x12, //0x000024bb leaq         (%rdx,%rdx), %rax
	0x48, 0x8d, 0x3c, 0x80, //0x000024bf leaq         (%rax,%rax,4), %rdi
	0x48, 0x89, 0xc8, //0x000024c3 movq         %rcx, %rax
	0x48, 0x29, 0xf8, //0x000024c6 subq         %rdi, %rax
	0x4c, 0x39, 0xee, //0x000024c9 cmpq         %r13, %rsi
	0x0f, 0x83, 0xbe, 0xff, 0xff, 0xff, //0x000024cc jae          LBB0_512
	0x04, 0x30, //0x000024d2 addb         $48, %al
	0x41, 0x88, 0x04, 0x34, //0x000024d4 movb         %al, (%r12,%rsi)
	0x48, 0xff, 0xce, //0x000024d8 decq         %rsi
	0x48, 0x83, 0xf9, 0x09, //0x000024db cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x000024df movq         %rdx, %rcx
	0x0f, 0x87, 0xbf, 0xff, 0xff, 0xff, //0x000024e2 ja           LBB0_513
	//0x000024e8 LBB0_515
	0x45, 0x01, 0xc8, //0x000024e8 addl         %r9d, %r8d
	0x4d, 0x63, 0xc0, //0x000024eb movslq       %r8d, %r8
	0x4d, 0x39, 0xc5, //0x000024ee cmpq         %r8, %r13
	0x45, 0x0f, 0x46, 0xc5, //0x000024f1 cmovbel      %r13d, %r8d
	0x45, 0x01, 0xcf, //0x000024f5 addl         %r9d, %r15d
	0x45, 0x85, 0xc0, //0x000024f8 testl        %r8d, %r8d
	0x0f, 0x8e, 0x38, 0xfe, 0xff, 0xff, //0x000024fb jle          LBB0_492
	0x44, 0x89, 0xc0, //0x00002501 movl         %r8d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x00002504 cmpb         $48, $-1(%rax,%r12)
	0x48, 0x8d, 0x15, 0x0f, 0x41, 0x00, 0x00, //0x0000250a leaq         $16655(%rip), %rdx  /* _LSHIFT_TAB+0(%rip) */
	0x0f, 0x85, 0x30, 0xfe, 0xff, 0xff, //0x00002511 jne          LBB0_494
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002517 .p2align 4, 0x90
	//0x00002520 LBB0_517
	0x48, 0x83, 0xf8, 0x01, //0x00002520 cmpq         $1, %rax
	0x0f, 0x8e, 0x23, 0x00, 0x00, 0x00, //0x00002524 jle          LBB0_520
	0x4c, 0x8d, 0x40, 0xff, //0x0000252a leaq         $-1(%rax), %r8
	0x41, 0x80, 0x7c, 0x04, 0xfe, 0x30, //0x0000252e cmpb         $48, $-2(%r12,%rax)
	0x4c, 0x89, 0xc0, //0x00002534 movq         %r8, %rax
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00002537 je           LBB0_517
	0xe9, 0x05, 0xfe, 0xff, 0xff, //0x0000253d jmp          LBB0_494
	//0x00002542 LBB0_519
	0x0f, 0x8c, 0x6e, 0xfe, 0xff, 0xff, //0x00002542 jl           LBB0_501
	0xe9, 0x6f, 0xfe, 0xff, 0xff, //0x00002548 jmp          LBB0_502
	//0x0000254d LBB0_520
	0xff, 0xc8, //0x0000254d decl         %eax
	0x41, 0x89, 0xc0, //0x0000254f movl         %eax, %r8d
	0xe9, 0xe9, 0xfd, 0xff, 0xff, //0x00002552 jmp          LBB0_493
	//0x00002557 LBB0_522
	0x41, 0x89, 0xc1, //0x00002557 movl         %eax, %r9d
	0xe9, 0x0e, 0x02, 0x00, 0x00, //0x0000255a jmp          LBB0_553
	//0x0000255f LBB0_523
	0x44, 0x89, 0xc3, //0x0000255f movl         %r8d, %ebx
	0x44, 0x89, 0xc0, //0x00002562 movl         %r8d, %eax
	0x45, 0x85, 0xc9, //0x00002565 testl        %r9d, %r9d
	0x0f, 0x84, 0x55, 0xfd, 0xff, 0xff, //0x00002568 je           LBB0_482
	//0x0000256e LBB0_524
	0x44, 0x89, 0xc9, //0x0000256e movl         %r9d, %ecx
	0x48, 0x89, 0xd6, //0x00002571 movq         %rdx, %rsi
	0x48, 0x6b, 0xd1, 0x68, //0x00002574 imulq        $104, %rcx, %rdx
	0x8b, 0x3c, 0x32, //0x00002578 movl         (%rdx,%rsi), %edi
	0x4c, 0x63, 0xdb, //0x0000257b movslq       %ebx, %r11
	0x8a, 0x44, 0x32, 0x04, //0x0000257e movb         $4(%rdx,%rsi), %al
	0x45, 0x85, 0xdb, //0x00002582 testl        %r11d, %r11d
	0x0f, 0x84, 0x29, 0x00, 0x00, 0x00, //0x00002585 je           LBB0_529
	0x48, 0x8d, 0x54, 0x32, 0x05, //0x0000258b leaq         $5(%rdx,%rsi), %rdx
	0x31, 0xf6, //0x00002590 xorl         %esi, %esi
	//0x00002592 LBB0_526
	0x84, 0xc0, //0x00002592 testb        %al, %al
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x00002594 je           LBB0_531
	0x41, 0x38, 0x04, 0x34, //0x0000259a cmpb         %al, (%r12,%rsi)
	0x0f, 0x85, 0xa3, 0x01, 0x00, 0x00, //0x0000259e jne          LBB0_549
	0x0f, 0xb6, 0x04, 0x32, //0x000025a4 movzbl       (%rdx,%rsi), %eax
	0x48, 0xff, 0xc6, //0x000025a8 incq         %rsi
	0x49, 0x39, 0xf3, //0x000025ab cmpq         %rsi, %r11
	0x0f, 0x85, 0xde, 0xff, 0xff, 0xff, //0x000025ae jne          LBB0_526
	//0x000025b4 LBB0_529
	0x84, 0xc0, //0x000025b4 testb        %al, %al
	0x0f, 0x84, 0x02, 0x00, 0x00, 0x00, //0x000025b6 je           LBB0_531
	//0x000025bc LBB0_530
	0xff, 0xcf, //0x000025bc decl         %edi
	//0x000025be LBB0_531
	0x85, 0xdb, //0x000025be testl        %ebx, %ebx
	0x89, 0x7d, 0xc0, //0x000025c0 movl         %edi, $-64(%rbp)
	0x0f, 0x8e, 0xac, 0x00, 0x00, 0x00, //0x000025c3 jle          LBB0_539
	0x4c, 0x89, 0x7d, 0xc8, //0x000025c9 movq         %r15, $-56(%rbp)
	0x01, 0xfb, //0x000025cd addl         %edi, %ebx
	0x48, 0x63, 0xc3, //0x000025cf movslq       %ebx, %rax
	0x48, 0x89, 0xc6, //0x000025d2 movq         %rax, %rsi
	0x48, 0xc1, 0xe6, 0x20, //0x000025d5 shlq         $32, %rsi
	0x48, 0xff, 0xc8, //0x000025d9 decq         %rax
	0x49, 0xff, 0xc3, //0x000025dc incq         %r11
	0x31, 0xff, //0x000025df xorl         %edi, %edi
	0xe9, 0x2a, 0x00, 0x00, 0x00, //0x000025e1 jmp          LBB0_535
	//0x000025e6 LBB0_533
	0x48, 0x85, 0xc0, //0x000025e6 testq        %rax, %rax
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x000025e9 movl         $1, %eax
	0x44, 0x0f, 0x45, 0xf0, //0x000025ee cmovnel      %eax, %r14d
	//0x000025f2 LBB0_534
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x000025f2 movabsq      $-4294967296, %rax
	0x48, 0x01, 0xc6, //0x000025fc addq         %rax, %rsi
	0x49, 0x8d, 0x47, 0xff, //0x000025ff leaq         $-1(%r15), %rax
	0x49, 0xff, 0xcb, //0x00002603 decq         %r11
	0x49, 0x83, 0xfb, 0x01, //0x00002606 cmpq         $1, %r11
	0x0f, 0x8e, 0x4c, 0x00, 0x00, 0x00, //0x0000260a jle          LBB0_537
	//0x00002610 LBB0_535
	0x49, 0x89, 0xc7, //0x00002610 movq         %rax, %r15
	0x4b, 0x0f, 0xbe, 0x5c, 0x1c, 0xfe, //0x00002613 movsbq       $-2(%r12,%r11), %rbx
	0x48, 0x83, 0xc3, 0xd0, //0x00002619 addq         $-48, %rbx
	0x48, 0xd3, 0xe3, //0x0000261d shlq         %cl, %rbx
	0x48, 0x01, 0xfb, //0x00002620 addq         %rdi, %rbx
	0x48, 0x89, 0xd8, //0x00002623 movq         %rbx, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00002626 movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x00002630 mulq         %rdx
	0x48, 0x89, 0xd7, //0x00002633 movq         %rdx, %rdi
	0x48, 0xc1, 0xef, 0x03, //0x00002636 shrq         $3, %rdi
	0x48, 0x8d, 0x04, 0x3f, //0x0000263a leaq         (%rdi,%rdi), %rax
	0x48, 0x8d, 0x14, 0x80, //0x0000263e leaq         (%rax,%rax,4), %rdx
	0x48, 0x89, 0xd8, //0x00002642 movq         %rbx, %rax
	0x48, 0x29, 0xd0, //0x00002645 subq         %rdx, %rax
	0x4d, 0x39, 0xef, //0x00002648 cmpq         %r13, %r15
	0x0f, 0x83, 0x95, 0xff, 0xff, 0xff, //0x0000264b jae          LBB0_533
	0x04, 0x30, //0x00002651 addb         $48, %al
	0x43, 0x88, 0x04, 0x3c, //0x00002653 movb         %al, (%r12,%r15)
	0xe9, 0x96, 0xff, 0xff, 0xff, //0x00002657 jmp          LBB0_534
	//0x0000265c LBB0_537
	0x48, 0x83, 0xfb, 0x0a, //0x0000265c cmpq         $10, %rbx
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00002660 movl         $1, %r11d
	0x0f, 0x83, 0x14, 0x00, 0x00, 0x00, //0x00002666 jae          LBB0_540
	0x4c, 0x8b, 0x7d, 0xc8, //0x0000266c movq         $-56(%rbp), %r15
	0xe9, 0x72, 0x00, 0x00, 0x00, //0x00002670 jmp          LBB0_544
	//0x00002675 LBB0_539
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00002675 movl         $1, %r11d
	0xe9, 0x67, 0x00, 0x00, 0x00, //0x0000267b jmp          LBB0_544
	//0x00002680 LBB0_540
	0x49, 0x63, 0xcf, //0x00002680 movslq       %r15d, %rcx
	0x48, 0xff, 0xc9, //0x00002683 decq         %rcx
	0x4c, 0x8b, 0x7d, 0xc8, //0x00002686 movq         $-56(%rbp), %r15
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x0000268a jmp          LBB0_542
	//0x0000268f LBB0_541
	0x48, 0x85, 0xc0, //0x0000268f testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xf3, //0x00002692 cmovnel      %r11d, %r14d
	0x48, 0xff, 0xc9, //0x00002696 decq         %rcx
	0x48, 0x83, 0xff, 0x09, //0x00002699 cmpq         $9, %rdi
	0x48, 0x89, 0xd7, //0x0000269d movq         %rdx, %rdi
	0x0f, 0x86, 0x41, 0x00, 0x00, 0x00, //0x000026a0 jbe          LBB0_544
	//0x000026a6 LBB0_542
	0x48, 0x89, 0xf8, //0x000026a6 movq         %rdi, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x000026a9 movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x000026b3 mulq         %rdx
	0x48, 0xc1, 0xea, 0x03, //0x000026b6 shrq         $3, %rdx
	0x48, 0x8d, 0x04, 0x12, //0x000026ba leaq         (%rdx,%rdx), %rax
	0x48, 0x8d, 0x34, 0x80, //0x000026be leaq         (%rax,%rax,4), %rsi
	0x48, 0x89, 0xf8, //0x000026c2 movq         %rdi, %rax
	0x48, 0x29, 0xf0, //0x000026c5 subq         %rsi, %rax
	0x4c, 0x39, 0xe9, //0x000026c8 cmpq         %r13, %rcx
	0x0f, 0x83, 0xbe, 0xff, 0xff, 0xff, //0x000026cb jae          LBB0_541
	0x04, 0x30, //0x000026d1 addb         $48, %al
	0x41, 0x88, 0x04, 0x0c, //0x000026d3 movb         %al, (%r12,%rcx)
	0x48, 0xff, 0xc9, //0x000026d7 decq         %rcx
	0x48, 0x83, 0xff, 0x09, //0x000026da cmpq         $9, %rdi
	0x48, 0x89, 0xd7, //0x000026de movq         %rdx, %rdi
	0x0f, 0x87, 0xbf, 0xff, 0xff, 0xff, //0x000026e1 ja           LBB0_542
	//0x000026e7 LBB0_544
	0x8b, 0x45, 0xc0, //0x000026e7 movl         $-64(%rbp), %eax
	0x41, 0x01, 0xc0, //0x000026ea addl         %eax, %r8d
	0x4d, 0x63, 0xc0, //0x000026ed movslq       %r8d, %r8
	0x4d, 0x39, 0xc5, //0x000026f0 cmpq         %r8, %r13
	0x45, 0x0f, 0x46, 0xc5, //0x000026f3 cmovbel      %r13d, %r8d
	0x41, 0x01, 0xc7, //0x000026f7 addl         %eax, %r15d
	0x45, 0x85, 0xc0, //0x000026fa testl        %r8d, %r8d
	0x0f, 0x8e, 0x38, 0x00, 0x00, 0x00, //0x000026fd jle          LBB0_548
	0x44, 0x89, 0xc0, //0x00002703 movl         %r8d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x00002706 cmpb         $48, $-1(%rax,%r12)
	0x48, 0x8d, 0x15, 0x0d, 0x3f, 0x00, 0x00, //0x0000270c leaq         $16141(%rip), %rdx  /* _LSHIFT_TAB+0(%rip) */
	0x0f, 0x85, 0x45, 0x00, 0x00, 0x00, //0x00002713 jne          LBB0_552
	//0x00002719 LBB0_546
	0x48, 0x83, 0xf8, 0x01, //0x00002719 cmpq         $1, %rax
	0x0f, 0x8e, 0x2f, 0x00, 0x00, 0x00, //0x0000271d jle          LBB0_550
	0x4c, 0x8d, 0x40, 0xff, //0x00002723 leaq         $-1(%rax), %r8
	0x41, 0x80, 0x7c, 0x04, 0xfe, 0x30, //0x00002727 cmpb         $48, $-2(%r12,%rax)
	0x4c, 0x89, 0xc0, //0x0000272d movq         %r8, %rax
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00002730 je           LBB0_546
	0xe9, 0x23, 0x00, 0x00, 0x00, //0x00002736 jmp          LBB0_552
	//0x0000273b LBB0_548
	0x48, 0x8d, 0x15, 0xde, 0x3e, 0x00, 0x00, //0x0000273b leaq         $16094(%rip), %rdx  /* _LSHIFT_TAB+0(%rip) */
	0xe9, 0x10, 0x00, 0x00, 0x00, //0x00002742 jmp          LBB0_551
	//0x00002747 LBB0_549
	0x0f, 0x8c, 0x6f, 0xfe, 0xff, 0xff, //0x00002747 jl           LBB0_530
	0xe9, 0x6c, 0xfe, 0xff, 0xff, //0x0000274d jmp          LBB0_531
	//0x00002752 LBB0_550
	0xff, 0xc8, //0x00002752 decl         %eax
	0x41, 0x89, 0xc0, //0x00002754 movl         %eax, %r8d
	//0x00002757 LBB0_551
	0x45, 0x85, 0xc0, //0x00002757 testl        %r8d, %r8d
	0x45, 0x0f, 0x44, 0xf8, //0x0000275a cmovel       %r8d, %r15d
	//0x0000275e LBB0_552
	0x44, 0x89, 0xc3, //0x0000275e movl         %r8d, %ebx
	0x44, 0x89, 0xc0, //0x00002761 movl         %r8d, %eax
	0x45, 0x85, 0xc9, //0x00002764 testl        %r9d, %r9d
	0x0f, 0x89, 0x56, 0xfb, 0xff, 0xff, //0x00002767 jns          LBB0_482
	//0x0000276d LBB0_553
	0x41, 0x83, 0xf9, 0xc3, //0x0000276d cmpl         $-61, %r9d
	0x0f, 0x8e, 0x1f, 0x00, 0x00, 0x00, //0x00002771 jle          LBB0_556
	0xe9, 0xa2, 0x01, 0x00, 0x00, //0x00002777 jmp          LBB0_578
	//0x0000277c LBB0_554
	0x45, 0x31, 0xc0, //0x0000277c xorl         %r8d, %r8d
	0x48, 0x8d, 0x15, 0x9a, 0x3e, 0x00, 0x00, //0x0000277f leaq         $16026(%rip), %rdx  /* _LSHIFT_TAB+0(%rip) */
	//0x00002786 LBB0_555
	0x44, 0x8d, 0x48, 0x3c, //0x00002786 leal         $60(%rax), %r9d
	0x44, 0x89, 0xc3, //0x0000278a movl         %r8d, %ebx
	0x83, 0xf8, 0x88, //0x0000278d cmpl         $-120, %eax
	0x0f, 0x8d, 0x79, 0x01, 0x00, 0x00, //0x00002790 jge          LBB0_577
	//0x00002796 LBB0_556
	0x44, 0x89, 0xc8, //0x00002796 movl         %r9d, %eax
	0x48, 0x63, 0xf3, //0x00002799 movslq       %ebx, %rsi
	0x31, 0xd2, //0x0000279c xorl         %edx, %edx
	0x31, 0xc9, //0x0000279e xorl         %ecx, %ecx
	//0x000027a0 .p2align 4, 0x90
	//0x000027a0 LBB0_557
	0x48, 0x39, 0xf2, //0x000027a0 cmpq         %rsi, %rdx
	0x0f, 0x8d, 0x23, 0x00, 0x00, 0x00, //0x000027a3 jge          LBB0_559
	0x48, 0x8d, 0x0c, 0x89, //0x000027a9 leaq         (%rcx,%rcx,4), %rcx
	0x49, 0x0f, 0xbe, 0x3c, 0x14, //0x000027ad movsbq       (%r12,%rdx), %rdi
	0x48, 0x8d, 0x4c, 0x4f, 0xd0, //0x000027b2 leaq         $-48(%rdi,%rcx,2), %rcx
	0x48, 0xff, 0xc2, //0x000027b7 incq         %rdx
	0x49, 0x8d, 0x7a, 0x01, //0x000027ba leaq         $1(%r10), %rdi
	0x48, 0x39, 0xf9, //0x000027be cmpq         %rdi, %rcx
	0x0f, 0x82, 0xd9, 0xff, 0xff, 0xff, //0x000027c1 jb           LBB0_557
	0xe9, 0x2a, 0x00, 0x00, 0x00, //0x000027c7 jmp          LBB0_561
	//0x000027cc LBB0_559
	0x48, 0x85, 0xc9, //0x000027cc testq        %rcx, %rcx
	0x0f, 0x84, 0xa7, 0xff, 0xff, 0xff, //0x000027cf je           LBB0_554
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000027d5 .p2align 4, 0x90
	//0x000027e0 LBB0_560
	0x48, 0x01, 0xc9, //0x000027e0 addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x000027e3 leaq         (%rcx,%rcx,4), %rcx
	0xff, 0xc2, //0x000027e7 incl         %edx
	0x49, 0x8d, 0x72, 0x01, //0x000027e9 leaq         $1(%r10), %rsi
	0x48, 0x39, 0xf1, //0x000027ed cmpq         %rsi, %rcx
	0x0f, 0x82, 0xea, 0xff, 0xff, 0xff, //0x000027f0 jb           LBB0_560
	//0x000027f6 LBB0_561
	0x41, 0x29, 0xd7, //0x000027f6 subl         %edx, %r15d
	0x31, 0xf6, //0x000027f9 xorl         %esi, %esi
	0x39, 0xda, //0x000027fb cmpl         %ebx, %edx
	0x0f, 0x8d, 0x49, 0x00, 0x00, 0x00, //0x000027fd jge          LBB0_566
	0x48, 0x63, 0xd2, //0x00002803 movslq       %edx, %rdx
	0x49, 0x63, 0xf0, //0x00002806 movslq       %r8d, %rsi
	0x49, 0x8d, 0x3c, 0x14, //0x00002809 leaq         (%r12,%rdx), %rdi
	0x45, 0x31, 0xc0, //0x0000280d xorl         %r8d, %r8d
	//0x00002810 .p2align 4, 0x90
	//0x00002810 LBB0_563
	0x48, 0x89, 0xcb, //0x00002810 movq         %rcx, %rbx
	0x48, 0xc1, 0xeb, 0x3c, //0x00002813 shrq         $60, %rbx
	0x4c, 0x21, 0xd1, //0x00002817 andq         %r10, %rcx
	0x80, 0xcb, 0x30, //0x0000281a orb          $48, %bl
	0x43, 0x88, 0x1c, 0x04, //0x0000281d movb         %bl, (%r12,%r8)
	0x48, 0x8d, 0x0c, 0x89, //0x00002821 leaq         (%rcx,%rcx,4), %rcx
	0x4a, 0x0f, 0xbe, 0x1c, 0x07, //0x00002825 movsbq       (%rdi,%r8), %rbx
	0x48, 0x8d, 0x4c, 0x4b, 0xd0, //0x0000282a leaq         $-48(%rbx,%rcx,2), %rcx
	0x4a, 0x8d, 0x5c, 0x02, 0x01, //0x0000282f leaq         $1(%rdx,%r8), %rbx
	0x49, 0xff, 0xc0, //0x00002834 incq         %r8
	0x48, 0x39, 0xf3, //0x00002837 cmpq         %rsi, %rbx
	0x0f, 0x8c, 0xd0, 0xff, 0xff, 0xff, //0x0000283a jl           LBB0_563
	0x48, 0x85, 0xc9, //0x00002840 testq        %rcx, %rcx
	0x0f, 0x84, 0x60, 0x00, 0x00, 0x00, //0x00002843 je           LBB0_570
	0x44, 0x89, 0xc6, //0x00002849 movl         %r8d, %esi
	//0x0000284c LBB0_566
	0x41, 0x89, 0xf0, //0x0000284c movl         %esi, %r8d
	0xe9, 0x23, 0x00, 0x00, 0x00, //0x0000284f jmp          LBB0_568
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002854 .p2align 4, 0x90
	//0x00002860 LBB0_567
	0x48, 0x85, 0xd2, //0x00002860 testq        %rdx, %rdx
	0x45, 0x0f, 0x45, 0xf3, //0x00002863 cmovnel      %r11d, %r14d
	0x48, 0x01, 0xc9, //0x00002867 addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x0000286a leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xc9, //0x0000286e testq        %rcx, %rcx
	0x0f, 0x84, 0x32, 0x00, 0x00, 0x00, //0x00002871 je           LBB0_570
	//0x00002877 LBB0_568
	0x48, 0x89, 0xca, //0x00002877 movq         %rcx, %rdx
	0x48, 0xc1, 0xea, 0x3c, //0x0000287a shrq         $60, %rdx
	0x4c, 0x21, 0xd1, //0x0000287e andq         %r10, %rcx
	0x49, 0x63, 0xf0, //0x00002881 movslq       %r8d, %rsi
	0x49, 0x39, 0xf5, //0x00002884 cmpq         %rsi, %r13
	0x0f, 0x86, 0xd3, 0xff, 0xff, 0xff, //0x00002887 jbe          LBB0_567
	0x80, 0xca, 0x30, //0x0000288d orb          $48, %dl
	0x41, 0x88, 0x14, 0x34, //0x00002890 movb         %dl, (%r12,%rsi)
	0xff, 0xc6, //0x00002894 incl         %esi
	0x41, 0x89, 0xf0, //0x00002896 movl         %esi, %r8d
	0x48, 0x01, 0xc9, //0x00002899 addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x0000289c leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xc9, //0x000028a0 testq        %rcx, %rcx
	0x0f, 0x85, 0xce, 0xff, 0xff, 0xff, //0x000028a3 jne          LBB0_568
	//0x000028a9 LBB0_570
	0x41, 0xff, 0xc7, //0x000028a9 incl         %r15d
	0x45, 0x85, 0xc0, //0x000028ac testl        %r8d, %r8d
	0x0f, 0x8e, 0x3d, 0x00, 0x00, 0x00, //0x000028af jle          LBB0_574
	0x44, 0x89, 0xc1, //0x000028b5 movl         %r8d, %ecx
	0x42, 0x80, 0x7c, 0x21, 0xff, 0x30, //0x000028b8 cmpb         $48, $-1(%rcx,%r12)
	0x48, 0x8d, 0x15, 0x5b, 0x3d, 0x00, 0x00, //0x000028be leaq         $15707(%rip), %rdx  /* _LSHIFT_TAB+0(%rip) */
	0x0f, 0x85, 0xbb, 0xfe, 0xff, 0xff, //0x000028c5 jne          LBB0_555
	0x90, 0x90, 0x90, 0x90, 0x90, //0x000028cb .p2align 4, 0x90
	//0x000028d0 LBB0_572
	0x48, 0x83, 0xf9, 0x01, //0x000028d0 cmpq         $1, %rcx
	0x0f, 0x8e, 0x24, 0x00, 0x00, 0x00, //0x000028d4 jle          LBB0_575
	0x4c, 0x8d, 0x41, 0xff, //0x000028da leaq         $-1(%rcx), %r8
	0x41, 0x80, 0x7c, 0x0c, 0xfe, 0x30, //0x000028de cmpb         $48, $-2(%r12,%rcx)
	0x4c, 0x89, 0xc1, //0x000028e4 movq         %r8, %rcx
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x000028e7 je           LBB0_572
	0xe9, 0x94, 0xfe, 0xff, 0xff, //0x000028ed jmp          LBB0_555
	//0x000028f2 LBB0_574
	0x48, 0x8d, 0x15, 0x27, 0x3d, 0x00, 0x00, //0x000028f2 leaq         $15655(%rip), %rdx  /* _LSHIFT_TAB+0(%rip) */
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x000028f9 jmp          LBB0_576
	//0x000028fe LBB0_575
	0xff, 0xc9, //0x000028fe decl         %ecx
	0x41, 0x89, 0xc8, //0x00002900 movl         %ecx, %r8d
	//0x00002903 LBB0_576
	0x45, 0x85, 0xc0, //0x00002903 testl        %r8d, %r8d
	0x45, 0x0f, 0x44, 0xf8, //0x00002906 cmovel       %r8d, %r15d
	0xe9, 0x77, 0xfe, 0xff, 0xff, //0x0000290a jmp          LBB0_555
	//0x0000290f LBB0_577
	0x44, 0x89, 0xc3, //0x0000290f movl         %r8d, %ebx
	0x44, 0x89, 0xc0, //0x00002912 movl         %r8d, %eax
	0x45, 0x85, 0xc9, //0x00002915 testl        %r9d, %r9d
	0x0f, 0x84, 0xa5, 0xf9, 0xff, 0xff, //0x00002918 je           LBB0_482
	//0x0000291e LBB0_578
	0x41, 0xf7, 0xd9, //0x0000291e negl         %r9d
	0x48, 0x63, 0xf3, //0x00002921 movslq       %ebx, %rsi
	0x31, 0xd2, //0x00002924 xorl         %edx, %edx
	0x31, 0xc0, //0x00002926 xorl         %eax, %eax
	//0x00002928 LBB0_579
	0x48, 0x39, 0xf2, //0x00002928 cmpq         %rsi, %rdx
	0x0f, 0x8d, 0x18, 0x01, 0x00, 0x00, //0x0000292b jge          LBB0_595
	0x48, 0x8d, 0x04, 0x80, //0x00002931 leaq         (%rax,%rax,4), %rax
	0x49, 0x0f, 0xbe, 0x0c, 0x14, //0x00002935 movsbq       (%r12,%rdx), %rcx
	0x48, 0x8d, 0x44, 0x41, 0xd0, //0x0000293a leaq         $-48(%rcx,%rax,2), %rax
	0x48, 0xff, 0xc2, //0x0000293f incq         %rdx
	0x48, 0x89, 0xc7, //0x00002942 movq         %rax, %rdi
	0x44, 0x89, 0xc9, //0x00002945 movl         %r9d, %ecx
	0x48, 0xd3, 0xef, //0x00002948 shrq         %cl, %rdi
	0x48, 0x85, 0xff, //0x0000294b testq        %rdi, %rdi
	0x0f, 0x84, 0xd4, 0xff, 0xff, 0xff, //0x0000294e je           LBB0_579
	//0x00002954 LBB0_581
	0x48, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00002954 movq         $-1, %rsi
	0x44, 0x89, 0xc9, //0x0000295b movl         %r9d, %ecx
	0x48, 0xd3, 0xe6, //0x0000295e shlq         %cl, %rsi
	0x48, 0xf7, 0xd6, //0x00002961 notq         %rsi
	0x31, 0xff, //0x00002964 xorl         %edi, %edi
	0x39, 0xda, //0x00002966 cmpl         %ebx, %edx
	0x0f, 0x8d, 0x4c, 0x00, 0x00, 0x00, //0x00002968 jge          LBB0_585
	0x4c, 0x89, 0x7d, 0xc8, //0x0000296e movq         %r15, $-56(%rbp)
	0x4c, 0x63, 0xda, //0x00002972 movslq       %edx, %r11
	0x4d, 0x63, 0xc0, //0x00002975 movslq       %r8d, %r8
	0x4f, 0x8d, 0x3c, 0x1c, //0x00002978 leaq         (%r12,%r11), %r15
	0x31, 0xff, //0x0000297c xorl         %edi, %edi
	//0x0000297e LBB0_583
	0x48, 0x89, 0xc3, //0x0000297e movq         %rax, %rbx
	0x44, 0x89, 0xc9, //0x00002981 movl         %r9d, %ecx
	0x48, 0xd3, 0xeb, //0x00002984 shrq         %cl, %rbx
	0x48, 0x21, 0xf0, //0x00002987 andq         %rsi, %rax
	0x80, 0xc3, 0x30, //0x0000298a addb         $48, %bl
	0x41, 0x88, 0x1c, 0x3c, //0x0000298d movb         %bl, (%r12,%rdi)
	0x48, 0x8d, 0x04, 0x80, //0x00002991 leaq         (%rax,%rax,4), %rax
	0x49, 0x0f, 0xbe, 0x0c, 0x3f, //0x00002995 movsbq       (%r15,%rdi), %rcx
	0x48, 0x8d, 0x44, 0x41, 0xd0, //0x0000299a leaq         $-48(%rcx,%rax,2), %rax
	0x49, 0x8d, 0x4c, 0x3b, 0x01, //0x0000299f leaq         $1(%r11,%rdi), %rcx
	0x48, 0xff, 0xc7, //0x000029a4 incq         %rdi
	0x4c, 0x39, 0xc1, //0x000029a7 cmpq         %r8, %rcx
	0x0f, 0x8c, 0xce, 0xff, 0xff, 0xff, //0x000029aa jl           LBB0_583
	0x4c, 0x8b, 0x7d, 0xc8, //0x000029b0 movq         $-56(%rbp), %r15
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x000029b4 movl         $1, %r11d
	//0x000029ba LBB0_585
	0x41, 0x29, 0xd7, //0x000029ba subl         %edx, %r15d
	0x41, 0x89, 0xf8, //0x000029bd movl         %edi, %r8d
	0xe9, 0x0e, 0x00, 0x00, 0x00, //0x000029c0 jmp          LBB0_588
	//0x000029c5 LBB0_586
	0x48, 0x85, 0xd2, //0x000029c5 testq        %rdx, %rdx
	0x45, 0x0f, 0x45, 0xf3, //0x000029c8 cmovnel      %r11d, %r14d
	//0x000029cc LBB0_587
	0x48, 0x01, 0xc0, //0x000029cc addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x000029cf leaq         (%rax,%rax,4), %rax
	//0x000029d3 LBB0_588
	0x48, 0x85, 0xc0, //0x000029d3 testq        %rax, %rax
	0x0f, 0x84, 0x29, 0x00, 0x00, 0x00, //0x000029d6 je           LBB0_591
	0x48, 0x89, 0xc2, //0x000029dc movq         %rax, %rdx
	0x44, 0x89, 0xc9, //0x000029df movl         %r9d, %ecx
	0x48, 0xd3, 0xea, //0x000029e2 shrq         %cl, %rdx
	0x48, 0x21, 0xf0, //0x000029e5 andq         %rsi, %rax
	0x49, 0x63, 0xc8, //0x000029e8 movslq       %r8d, %rcx
	0x49, 0x39, 0xcd, //0x000029eb cmpq         %rcx, %r13
	0x0f, 0x86, 0xd1, 0xff, 0xff, 0xff, //0x000029ee jbe          LBB0_586
	0x80, 0xc2, 0x30, //0x000029f4 addb         $48, %dl
	0x41, 0x88, 0x14, 0x0c, //0x000029f7 movb         %dl, (%r12,%rcx)
	0xff, 0xc1, //0x000029fb incl         %ecx
	0x41, 0x89, 0xc8, //0x000029fd movl         %ecx, %r8d
	0xe9, 0xc7, 0xff, 0xff, 0xff, //0x00002a00 jmp          LBB0_587
	//0x00002a05 LBB0_591
	0x41, 0xff, 0xc7, //0x00002a05 incl         %r15d
	0x45, 0x85, 0xc0, //0x00002a08 testl        %r8d, %r8d
	0x0f, 0x8e, 0x61, 0x00, 0x00, 0x00, //0x00002a0b jle          LBB0_598
	0x44, 0x89, 0xc0, //0x00002a11 movl         %r8d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x00002a14 cmpb         $48, $-1(%rax,%r12)
	0x48, 0x8d, 0x15, 0xff, 0x3b, 0x00, 0x00, //0x00002a1a leaq         $15359(%rip), %rdx  /* _LSHIFT_TAB+0(%rip) */
	0x0f, 0x85, 0x74, 0x00, 0x00, 0x00, //0x00002a21 jne          LBB0_602
	//0x00002a27 LBB0_593
	0x48, 0x83, 0xf8, 0x01, //0x00002a27 cmpq         $1, %rax
	0x0f, 0x8e, 0x5e, 0x00, 0x00, 0x00, //0x00002a2b jle          LBB0_600
	0x4c, 0x8d, 0x40, 0xff, //0x00002a31 leaq         $-1(%rax), %r8
	0x41, 0x80, 0x7c, 0x04, 0xfe, 0x30, //0x00002a35 cmpb         $48, $-2(%r12,%rax)
	0x4c, 0x89, 0xc0, //0x00002a3b movq         %r8, %rax
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00002a3e je           LBB0_593
	0xe9, 0x52, 0x00, 0x00, 0x00, //0x00002a44 jmp          LBB0_602
	//0x00002a49 LBB0_595
	0x48, 0x85, 0xc0, //0x00002a49 testq        %rax, %rax
	0x0f, 0x84, 0x2c, 0x00, 0x00, 0x00, //0x00002a4c je           LBB0_599
	//0x00002a52 LBB0_596
	0x48, 0x89, 0xc6, //0x00002a52 movq         %rax, %rsi
	0x44, 0x89, 0xc9, //0x00002a55 movl         %r9d, %ecx
	0x48, 0xd3, 0xee, //0x00002a58 shrq         %cl, %rsi
	0x48, 0x85, 0xf6, //0x00002a5b testq        %rsi, %rsi
	0x0f, 0x85, 0xf0, 0xfe, 0xff, 0xff, //0x00002a5e jne          LBB0_581
	0x48, 0x01, 0xc0, //0x00002a64 addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x00002a67 leaq         (%rax,%rax,4), %rax
	0xff, 0xc2, //0x00002a6b incl         %edx
	0xe9, 0xe0, 0xff, 0xff, 0xff, //0x00002a6d jmp          LBB0_596
	//0x00002a72 LBB0_598
	0x48, 0x8d, 0x15, 0xa7, 0x3b, 0x00, 0x00, //0x00002a72 leaq         $15271(%rip), %rdx  /* _LSHIFT_TAB+0(%rip) */
	0xe9, 0x16, 0x00, 0x00, 0x00, //0x00002a79 jmp          LBB0_601
	//0x00002a7e LBB0_599
	0x45, 0x31, 0xc0, //0x00002a7e xorl         %r8d, %r8d
	0x31, 0xc0, //0x00002a81 xorl         %eax, %eax
	0x48, 0x8d, 0x15, 0x96, 0x3b, 0x00, 0x00, //0x00002a83 leaq         $15254(%rip), %rdx  /* _LSHIFT_TAB+0(%rip) */
	0xe9, 0x34, 0xf8, 0xff, 0xff, //0x00002a8a jmp          LBB0_482
	//0x00002a8f LBB0_600
	0xff, 0xc8, //0x00002a8f decl         %eax
	0x41, 0x89, 0xc0, //0x00002a91 movl         %eax, %r8d
	//0x00002a94 LBB0_601
	0x45, 0x85, 0xc0, //0x00002a94 testl        %r8d, %r8d
	0x45, 0x0f, 0x44, 0xf8, //0x00002a97 cmovel       %r8d, %r15d
	//0x00002a9b LBB0_602
	0x44, 0x89, 0xc0, //0x00002a9b movl         %r8d, %eax
	0xe9, 0x20, 0xf8, 0xff, 0xff, //0x00002a9e jmp          LBB0_482
	//0x00002aa3 LBB0_603
	0x81, 0xfe, 0x02, 0xfc, 0xff, 0xff, //0x00002aa3 cmpl         $-1022, %esi
	0x4c, 0x89, 0x7d, 0xc8, //0x00002aa9 movq         %r15, $-56(%rbp)
	0x0f, 0x8f, 0x9c, 0x01, 0x00, 0x00, //0x00002aad jg           LBB0_629
	0x41, 0xbf, 0x02, 0xfc, 0xff, 0xff, //0x00002ab3 movl         $-1022, %r15d
	0x85, 0xdb, //0x00002ab9 testl        %ebx, %ebx
	0x0f, 0x84, 0x5b, 0x03, 0x00, 0x00, //0x00002abb je           LBB0_654
	0x8d, 0x8e, 0xfd, 0x03, 0x00, 0x00, //0x00002ac1 leal         $1021(%rsi), %ecx
	0x81, 0xfe, 0xc6, 0xfb, 0xff, 0xff, //0x00002ac7 cmpl         $-1082, %esi
	0x0f, 0x8f, 0xc9, 0x01, 0x00, 0x00, //0x00002acd jg           LBB0_633
	0x49, 0x8d, 0x42, 0x01, //0x00002ad3 leaq         $1(%r10), %rax
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00002ad7 movl         $1, %r11d
	0xe9, 0x25, 0x00, 0x00, 0x00, //0x00002add jmp          LBB0_610
	//0x00002ae2 LBB0_607
	0xff, 0xc9, //0x00002ae2 decl         %ecx
	0x41, 0x89, 0xc8, //0x00002ae4 movl         %ecx, %r8d
	//0x00002ae7 LBB0_608
	0x45, 0x85, 0xc0, //0x00002ae7 testl        %r8d, %r8d
	0x48, 0x8b, 0x4d, 0xc8, //0x00002aea movq         $-56(%rbp), %rcx
	0x41, 0x0f, 0x44, 0xc8, //0x00002aee cmovel       %r8d, %ecx
	0x48, 0x89, 0x4d, 0xc8, //0x00002af2 movq         %rcx, $-56(%rbp)
	//0x00002af6 LBB0_609
	0x41, 0x8d, 0x49, 0x3c, //0x00002af6 leal         $60(%r9), %ecx
	0x44, 0x89, 0xc3, //0x00002afa movl         %r8d, %ebx
	0x41, 0x83, 0xf9, 0x88, //0x00002afd cmpl         $-120, %r9d
	0x0f, 0x8d, 0x8a, 0x01, 0x00, 0x00, //0x00002b01 jge          LBB0_631
	//0x00002b07 LBB0_610
	0x41, 0x89, 0xc9, //0x00002b07 movl         %ecx, %r9d
	0x48, 0x63, 0xf3, //0x00002b0a movslq       %ebx, %rsi
	0x31, 0xff, //0x00002b0d xorl         %edi, %edi
	0x31, 0xc9, //0x00002b0f xorl         %ecx, %ecx
	//0x00002b11 LBB0_611
	0x48, 0x39, 0xf7, //0x00002b11 cmpq         %rsi, %rdi
	0x0f, 0x8d, 0x1f, 0x00, 0x00, 0x00, //0x00002b14 jge          LBB0_613
	0x48, 0x8d, 0x0c, 0x89, //0x00002b1a leaq         (%rcx,%rcx,4), %rcx
	0x49, 0x0f, 0xbe, 0x14, 0x3c, //0x00002b1e movsbq       (%r12,%rdi), %rdx
	0x48, 0x8d, 0x4c, 0x4a, 0xd0, //0x00002b23 leaq         $-48(%rdx,%rcx,2), %rcx
	0x48, 0xff, 0xc7, //0x00002b28 incq         %rdi
	0x48, 0x39, 0xc1, //0x00002b2b cmpq         %rax, %rcx
	0x0f, 0x82, 0xdd, 0xff, 0xff, 0xff, //0x00002b2e jb           LBB0_611
	0xe9, 0x1b, 0x00, 0x00, 0x00, //0x00002b34 jmp          LBB0_615
	//0x00002b39 LBB0_613
	0x48, 0x85, 0xc9, //0x00002b39 testq        %rcx, %rcx
	0x0f, 0x84, 0x05, 0x01, 0x00, 0x00, //0x00002b3c je           LBB0_628
	//0x00002b42 LBB0_614
	0x48, 0x01, 0xc9, //0x00002b42 addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x00002b45 leaq         (%rcx,%rcx,4), %rcx
	0xff, 0xc7, //0x00002b49 incl         %edi
	0x48, 0x39, 0xc1, //0x00002b4b cmpq         %rax, %rcx
	0x0f, 0x82, 0xee, 0xff, 0xff, 0xff, //0x00002b4e jb           LBB0_614
	//0x00002b54 LBB0_615
	0x48, 0x8b, 0x55, 0xc8, //0x00002b54 movq         $-56(%rbp), %rdx
	0x29, 0xfa, //0x00002b58 subl         %edi, %edx
	0x48, 0x89, 0x55, 0xc8, //0x00002b5a movq         %rdx, $-56(%rbp)
	0x31, 0xf6, //0x00002b5e xorl         %esi, %esi
	0x39, 0xdf, //0x00002b60 cmpl         %ebx, %edi
	0x0f, 0x8d, 0x49, 0x00, 0x00, 0x00, //0x00002b62 jge          LBB0_620
	0x48, 0x63, 0xff, //0x00002b68 movslq       %edi, %rdi
	0x49, 0x63, 0xd8, //0x00002b6b movslq       %r8d, %rbx
	0x49, 0x8d, 0x34, 0x3c, //0x00002b6e leaq         (%r12,%rdi), %rsi
	0x45, 0x31, 0xc0, //0x00002b72 xorl         %r8d, %r8d
	//0x00002b75 LBB0_617
	0x48, 0x89, 0xca, //0x00002b75 movq         %rcx, %rdx
	0x48, 0xc1, 0xea, 0x3c, //0x00002b78 shrq         $60, %rdx
	0x4c, 0x21, 0xd1, //0x00002b7c andq         %r10, %rcx
	0x80, 0xca, 0x30, //0x00002b7f orb          $48, %dl
	0x43, 0x88, 0x14, 0x04, //0x00002b82 movb         %dl, (%r12,%r8)
	0x48, 0x8d, 0x0c, 0x89, //0x00002b86 leaq         (%rcx,%rcx,4), %rcx
	0x4a, 0x0f, 0xbe, 0x14, 0x06, //0x00002b8a movsbq       (%rsi,%r8), %rdx
	0x48, 0x8d, 0x4c, 0x4a, 0xd0, //0x00002b8f leaq         $-48(%rdx,%rcx,2), %rcx
	0x4a, 0x8d, 0x54, 0x07, 0x01, //0x00002b94 leaq         $1(%rdi,%r8), %rdx
	0x49, 0xff, 0xc0, //0x00002b99 incq         %r8
	0x48, 0x39, 0xda, //0x00002b9c cmpq         %rbx, %rdx
	0x0f, 0x8c, 0xd0, 0xff, 0xff, 0xff, //0x00002b9f jl           LBB0_617
	0x48, 0x85, 0xc9, //0x00002ba5 testq        %rcx, %rcx
	0x0f, 0x84, 0x55, 0x00, 0x00, 0x00, //0x00002ba8 je           LBB0_624
	0x44, 0x89, 0xc6, //0x00002bae movl         %r8d, %esi
	//0x00002bb1 LBB0_620
	0x41, 0x89, 0xf0, //0x00002bb1 movl         %esi, %r8d
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00002bb4 jmp          LBB0_622
	//0x00002bb9 LBB0_621
	0x48, 0x85, 0xf6, //0x00002bb9 testq        %rsi, %rsi
	0x45, 0x0f, 0x45, 0xf3, //0x00002bbc cmovnel      %r11d, %r14d
	0x48, 0x01, 0xc9, //0x00002bc0 addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x00002bc3 leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xc9, //0x00002bc7 testq        %rcx, %rcx
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x00002bca je           LBB0_624
	//0x00002bd0 LBB0_622
	0x48, 0x89, 0xce, //0x00002bd0 movq         %rcx, %rsi
	0x48, 0xc1, 0xee, 0x3c, //0x00002bd3 shrq         $60, %rsi
	0x4c, 0x21, 0xd1, //0x00002bd7 andq         %r10, %rcx
	0x49, 0x63, 0xf8, //0x00002bda movslq       %r8d, %rdi
	0x49, 0x39, 0xfd, //0x00002bdd cmpq         %rdi, %r13
	0x0f, 0x86, 0xd3, 0xff, 0xff, 0xff, //0x00002be0 jbe          LBB0_621
	0x40, 0x80, 0xce, 0x30, //0x00002be6 orb          $48, %sil
	0x41, 0x88, 0x34, 0x3c, //0x00002bea movb         %sil, (%r12,%rdi)
	0xff, 0xc7, //0x00002bee incl         %edi
	0x41, 0x89, 0xf8, //0x00002bf0 movl         %edi, %r8d
	0x48, 0x01, 0xc9, //0x00002bf3 addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x00002bf6 leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xc9, //0x00002bfa testq        %rcx, %rcx
	0x0f, 0x85, 0xcd, 0xff, 0xff, 0xff, //0x00002bfd jne          LBB0_622
	//0x00002c03 LBB0_624
	0x48, 0x8b, 0x4d, 0xc8, //0x00002c03 movq         $-56(%rbp), %rcx
	0xff, 0xc1, //0x00002c07 incl         %ecx
	0x48, 0x89, 0x4d, 0xc8, //0x00002c09 movq         %rcx, $-56(%rbp)
	0x45, 0x85, 0xc0, //0x00002c0d testl        %r8d, %r8d
	0x0f, 0x8e, 0xd1, 0xfe, 0xff, 0xff, //0x00002c10 jle          LBB0_608
	0x44, 0x89, 0xc1, //0x00002c16 movl         %r8d, %ecx
	0x42, 0x80, 0x7c, 0x21, 0xff, 0x30, //0x00002c19 cmpb         $48, $-1(%rcx,%r12)
	0x0f, 0x85, 0xd1, 0xfe, 0xff, 0xff, //0x00002c1f jne          LBB0_609
	//0x00002c25 LBB0_626
	0x48, 0x83, 0xf9, 0x01, //0x00002c25 cmpq         $1, %rcx
	0x0f, 0x8e, 0xb3, 0xfe, 0xff, 0xff, //0x00002c29 jle          LBB0_607
	0x4c, 0x8d, 0x41, 0xff, //0x00002c2f leaq         $-1(%rcx), %r8
	0x41, 0x80, 0x7c, 0x0c, 0xfe, 0x30, //0x00002c33 cmpb         $48, $-2(%r12,%rcx)
	0x4c, 0x89, 0xc1, //0x00002c39 movq         %r8, %rcx
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00002c3c je           LBB0_626
	0xe9, 0xaf, 0xfe, 0xff, 0xff, //0x00002c42 jmp          LBB0_609
	//0x00002c47 LBB0_628
	0x45, 0x31, 0xc0, //0x00002c47 xorl         %r8d, %r8d
	0xe9, 0xa7, 0xfe, 0xff, 0xff, //0x00002c4a jmp          LBB0_609
	//0x00002c4f LBB0_629
	0x81, 0xfe, 0x00, 0x04, 0x00, 0x00, //0x00002c4f cmpl         $1024, %esi
	0x0f, 0x8f, 0x2a, 0xee, 0xff, 0xff, //0x00002c55 jg           LBB0_353
	0xff, 0xce, //0x00002c5b decl         %esi
	0x41, 0x89, 0xf7, //0x00002c5d movl         %esi, %r15d
	0xe9, 0xa7, 0x01, 0x00, 0x00, //0x00002c60 jmp          LBB0_649
	//0x00002c65 LBB0_660
	0x48, 0xf7, 0xdb, //0x00002c65 negq         %rbx
	0x48, 0x85, 0xdb, //0x00002c68 testq        %rbx, %rbx
	0x0f, 0x89, 0xca, 0xe5, 0xff, 0xff, //0x00002c6b jns          LBB0_246
	//0x00002c71 LBB0_248
	0x48, 0xf7, 0xd3, //0x00002c71 notq         %rbx
	0x49, 0x01, 0xd9, //0x00002c74 addq         %rbx, %r9
	//0x00002c77 LBB0_249
	0x4c, 0x8b, 0x75, 0xb8, //0x00002c77 movq         $-72(%rbp), %r14
	0x49, 0xc7, 0xc7, 0xfe, 0xff, 0xff, 0xff, //0x00002c7b movq         $-2, %r15
	//0x00002c82 LBB0_250
	0x4c, 0x2b, 0x4d, 0xc8, //0x00002c82 subq         $-56(%rbp), %r9
	0x4c, 0x89, 0xcb, //0x00002c86 movq         %r9, %rbx
	0x4d, 0x89, 0x3e, //0x00002c89 movq         %r15, (%r14)
	0xe9, 0x9d, 0xe3, 0xff, 0xff, //0x00002c8c jmp          LBB0_202
	//0x00002c91 LBB0_631
	0x85, 0xc9, //0x00002c91 testl        %ecx, %ecx
	0x0f, 0x84, 0x6a, 0x01, 0x00, 0x00, //0x00002c93 je           LBB0_648
	0x44, 0x89, 0xc3, //0x00002c99 movl         %r8d, %ebx
	//0x00002c9c LBB0_633
	0xf7, 0xd9, //0x00002c9c negl         %ecx
	0x48, 0x63, 0xf3, //0x00002c9e movslq       %ebx, %rsi
	0x31, 0xd2, //0x00002ca1 xorl         %edx, %edx
	0x31, 0xc0, //0x00002ca3 xorl         %eax, %eax
	//0x00002ca5 LBB0_634
	0x48, 0x39, 0xf2, //0x00002ca5 cmpq         %rsi, %rdx
	0x0f, 0x8d, 0x25, 0x00, 0x00, 0x00, //0x00002ca8 jge          LBB0_651
	0x48, 0x8d, 0x04, 0x80, //0x00002cae leaq         (%rax,%rax,4), %rax
	0x49, 0x0f, 0xbe, 0x3c, 0x14, //0x00002cb2 movsbq       (%r12,%rdx), %rdi
	0x48, 0x8d, 0x44, 0x47, 0xd0, //0x00002cb7 leaq         $-48(%rdi,%rax,2), %rax
	0x48, 0xff, 0xc2, //0x00002cbc incq         %rdx
	0x48, 0x89, 0xc7, //0x00002cbf movq         %rax, %rdi
	0x48, 0xd3, 0xef, //0x00002cc2 shrq         %cl, %rdi
	0x48, 0x85, 0xff, //0x00002cc5 testq        %rdi, %rdi
	0x0f, 0x84, 0xd7, 0xff, 0xff, 0xff, //0x00002cc8 je           LBB0_634
	0xe9, 0x30, 0x00, 0x00, 0x00, //0x00002cce jmp          LBB0_636
	//0x00002cd3 LBB0_651
	0x48, 0x85, 0xc0, //0x00002cd3 testq        %rax, %rax
	0x0f, 0x84, 0x40, 0x01, 0x00, 0x00, //0x00002cd6 je           LBB0_654
	0x48, 0x89, 0xc6, //0x00002cdc movq         %rax, %rsi
	0x48, 0xd3, 0xee, //0x00002cdf shrq         %cl, %rsi
	0x48, 0x85, 0xf6, //0x00002ce2 testq        %rsi, %rsi
	0x0f, 0x85, 0x18, 0x00, 0x00, 0x00, //0x00002ce5 jne          LBB0_636
	//0x00002ceb LBB0_653
	0x48, 0x01, 0xc0, //0x00002ceb addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x00002cee leaq         (%rax,%rax,4), %rax
	0xff, 0xc2, //0x00002cf2 incl         %edx
	0x48, 0x89, 0xc6, //0x00002cf4 movq         %rax, %rsi
	0x48, 0xd3, 0xee, //0x00002cf7 shrq         %cl, %rsi
	0x48, 0x85, 0xf6, //0x00002cfa testq        %rsi, %rsi
	0x0f, 0x84, 0xe8, 0xff, 0xff, 0xff, //0x00002cfd je           LBB0_653
	//0x00002d03 LBB0_636
	0x48, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00002d03 movq         $-1, %rsi
	0x48, 0xd3, 0xe6, //0x00002d0a shlq         %cl, %rsi
	0x48, 0xf7, 0xd6, //0x00002d0d notq         %rsi
	0x45, 0x31, 0xc9, //0x00002d10 xorl         %r9d, %r9d
	0x39, 0xda, //0x00002d13 cmpl         %ebx, %edx
	0x0f, 0x8d, 0x3d, 0x00, 0x00, 0x00, //0x00002d15 jge          LBB0_639
	0x4c, 0x63, 0xd2, //0x00002d1b movslq       %edx, %r10
	0x4d, 0x63, 0xc0, //0x00002d1e movslq       %r8d, %r8
	0x4b, 0x8d, 0x1c, 0x14, //0x00002d21 leaq         (%r12,%r10), %rbx
	0x45, 0x31, 0xc9, //0x00002d25 xorl         %r9d, %r9d
	//0x00002d28 LBB0_638
	0x48, 0x89, 0xc7, //0x00002d28 movq         %rax, %rdi
	0x48, 0xd3, 0xef, //0x00002d2b shrq         %cl, %rdi
	0x48, 0x21, 0xf0, //0x00002d2e andq         %rsi, %rax
	0x40, 0x80, 0xc7, 0x30, //0x00002d31 addb         $48, %dil
	0x43, 0x88, 0x3c, 0x0c, //0x00002d35 movb         %dil, (%r12,%r9)
	0x48, 0x8d, 0x04, 0x80, //0x00002d39 leaq         (%rax,%rax,4), %rax
	0x4a, 0x0f, 0xbe, 0x3c, 0x0b, //0x00002d3d movsbq       (%rbx,%r9), %rdi
	0x48, 0x8d, 0x44, 0x47, 0xd0, //0x00002d42 leaq         $-48(%rdi,%rax,2), %rax
	0x4b, 0x8d, 0x7c, 0x0a, 0x01, //0x00002d47 leaq         $1(%r10,%r9), %rdi
	0x49, 0xff, 0xc1, //0x00002d4c incq         %r9
	0x4c, 0x39, 0xc7, //0x00002d4f cmpq         %r8, %rdi
	0x0f, 0x8c, 0xd0, 0xff, 0xff, 0xff, //0x00002d52 jl           LBB0_638
	//0x00002d58 LBB0_639
	0x48, 0x8b, 0x7d, 0xc8, //0x00002d58 movq         $-56(%rbp), %rdi
	0x29, 0xd7, //0x00002d5c subl         %edx, %edi
	0x48, 0x89, 0x7d, 0xc8, //0x00002d5e movq         %rdi, $-56(%rbp)
	0x48, 0x85, 0xc0, //0x00002d62 testq        %rax, %rax
	0x0f, 0x84, 0x53, 0x00, 0x00, 0x00, //0x00002d65 je           LBB0_644
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00002d6b movl         $1, %edx
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00002d70 jmp          LBB0_642
	//0x00002d75 LBB0_641
	0x48, 0x85, 0xff, //0x00002d75 testq        %rdi, %rdi
	0x44, 0x0f, 0x45, 0xf2, //0x00002d78 cmovnel      %edx, %r14d
	0x48, 0x01, 0xc0, //0x00002d7c addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x00002d7f leaq         (%rax,%rax,4), %rax
	0x48, 0x85, 0xc0, //0x00002d83 testq        %rax, %rax
	0x0f, 0x84, 0x32, 0x00, 0x00, 0x00, //0x00002d86 je           LBB0_644
	//0x00002d8c LBB0_642
	0x48, 0x89, 0xc7, //0x00002d8c movq         %rax, %rdi
	0x48, 0xd3, 0xef, //0x00002d8f shrq         %cl, %rdi
	0x48, 0x21, 0xf0, //0x00002d92 andq         %rsi, %rax
	0x49, 0x63, 0xd9, //0x00002d95 movslq       %r9d, %rbx
	0x49, 0x39, 0xdd, //0x00002d98 cmpq         %rbx, %r13
	0x0f, 0x86, 0xd4, 0xff, 0xff, 0xff, //0x00002d9b jbe          LBB0_641
	0x40, 0x80, 0xc7, 0x30, //0x00002da1 addb         $48, %dil
	0x41, 0x88, 0x3c, 0x1c, //0x00002da5 movb         %dil, (%r12,%rbx)
	0xff, 0xc3, //0x00002da9 incl         %ebx
	0x41, 0x89, 0xd9, //0x00002dab movl         %ebx, %r9d
	0x48, 0x01, 0xc0, //0x00002dae addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x00002db1 leaq         (%rax,%rax,4), %rax
	0x48, 0x85, 0xc0, //0x00002db5 testq        %rax, %rax
	0x0f, 0x85, 0xce, 0xff, 0xff, 0xff, //0x00002db8 jne          LBB0_642
	//0x00002dbe LBB0_644
	0x48, 0x8b, 0x45, 0xc8, //0x00002dbe movq         $-56(%rbp), %rax
	0xff, 0xc0, //0x00002dc2 incl         %eax
	0x48, 0x89, 0x45, 0xc8, //0x00002dc4 movq         %rax, $-56(%rbp)
	0x45, 0x85, 0xc9, //0x00002dc8 testl        %r9d, %r9d
	0x0f, 0x8e, 0x9f, 0x00, 0x00, 0x00, //0x00002dcb jle          LBB0_662
	0x44, 0x89, 0xc8, //0x00002dd1 movl         %r9d, %eax
	0x41, 0xbf, 0x02, 0xfc, 0xff, 0xff, //0x00002dd4 movl         $-1022, %r15d
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x00002dda cmpb         $48, $-1(%rax,%r12)
	0x0f, 0x85, 0x99, 0x00, 0x00, 0x00, //0x00002de0 jne          LBB0_663
	//0x00002de6 LBB0_646
	0x48, 0x83, 0xf8, 0x01, //0x00002de6 cmpq         $1, %rax
	0x0f, 0x8e, 0x7b, 0x00, 0x00, 0x00, //0x00002dea jle          LBB0_661
	0x4c, 0x8d, 0x40, 0xff, //0x00002df0 leaq         $-1(%rax), %r8
	0x41, 0x80, 0x7c, 0x04, 0xfe, 0x30, //0x00002df4 cmpb         $48, $-2(%r12,%rax)
	0x4c, 0x89, 0xc0, //0x00002dfa movq         %r8, %rax
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00002dfd je           LBB0_646
	//0x00002e03 LBB0_648
	0x41, 0xbf, 0x02, 0xfc, 0xff, 0xff, //0x00002e03 movl         $-1022, %r15d
	0x44, 0x89, 0xc3, //0x00002e09 movl         %r8d, %ebx
	//0x00002e0c LBB0_649
	0x85, 0xdb, //0x00002e0c testl        %ebx, %ebx
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x00002e0e je           LBB0_654
	0x45, 0x89, 0xc1, //0x00002e14 movl         %r8d, %r9d
	0xe9, 0x66, 0x00, 0x00, 0x00, //0x00002e17 jmp          LBB0_664
	//0x00002e1c LBB0_654
	0x31, 0xc0, //0x00002e1c xorl         %eax, %eax
	0xe9, 0x0e, 0x02, 0x00, 0x00, //0x00002e1e jmp          LBB0_688
	//0x00002e23 LBB0_655
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00002e23 movq         $-1, %r15
	0xe9, 0x53, 0xfe, 0xff, 0xff, //0x00002e2a jmp          LBB0_250
	//0x00002e2f LBB0_656
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002e2f movq         $-1, %r8
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00002e36 movq         $-1, %r14
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00002e3d movq         $-1, %r10
	0x4d, 0x89, 0xcd, //0x00002e44 movq         %r9, %r13
	0x49, 0x83, 0xfb, 0x10, //0x00002e47 cmpq         $16, %r11
	0x0f, 0x83, 0x3f, 0xd8, 0xff, 0xff, //0x00002e4b jae          LBB0_67
	0xe9, 0xb0, 0xd9, 0xff, 0xff, //0x00002e51 jmp          LBB0_85
	//0x00002e56 LBB0_659
	0x8b, 0x45, 0xa8, //0x00002e56 movl         $-88(%rbp), %eax
	0x03, 0x45, 0xb0, //0x00002e59 addl         $-80(%rbp), %eax
	0x45, 0x31, 0xff, //0x00002e5c xorl         %r15d, %r15d
	0x45, 0x31, 0xc0, //0x00002e5f xorl         %r8d, %r8d
	0x31, 0xdb, //0x00002e62 xorl         %ebx, %ebx
	0x89, 0xc6, //0x00002e64 movl         %eax, %esi
	0xe9, 0x44, 0xf4, 0xff, 0xff, //0x00002e66 jmp          LBB0_480
	//0x00002e6b LBB0_661
	0xff, 0xc8, //0x00002e6b decl         %eax
	0x41, 0x89, 0xc1, //0x00002e6d movl         %eax, %r9d
	//0x00002e70 LBB0_662
	0x41, 0xbf, 0x02, 0xfc, 0xff, 0xff, //0x00002e70 movl         $-1022, %r15d
	0x45, 0x85, 0xc9, //0x00002e76 testl        %r9d, %r9d
	0x0f, 0x84, 0x7b, 0x02, 0x00, 0x00, //0x00002e79 je           LBB0_699
	//0x00002e7f LBB0_663
	0x44, 0x89, 0xcb, //0x00002e7f movl         %r9d, %ebx
	//0x00002e82 LBB0_664
	0x48, 0x8d, 0x15, 0x97, 0x37, 0x00, 0x00, //0x00002e82 leaq         $14231(%rip), %rdx  /* _LSHIFT_TAB+0(%rip) */
	0x4c, 0x63, 0xd3, //0x00002e89 movslq       %ebx, %r10
	0xb1, 0x31, //0x00002e8c movb         $49, %cl
	0x31, 0xc0, //0x00002e8e xorl         %eax, %eax
	0x41, 0xb8, 0x10, 0x00, 0x00, 0x00, //0x00002e90 movl         $16, %r8d
	//0x00002e96 LBB0_665
	0x48, 0x83, 0xf8, 0x26, //0x00002e96 cmpq         $38, %rax
	0x0f, 0x84, 0x2c, 0x00, 0x00, 0x00, //0x00002e9a je           LBB0_670
	0x41, 0x38, 0x0c, 0x04, //0x00002ea0 cmpb         %cl, (%r12,%rax)
	0x0f, 0x85, 0x70, 0x01, 0x00, 0x00, //0x00002ea4 jne          LBB0_685
	0x0f, 0xb6, 0x8c, 0x10, 0x8d, 0x15, 0x00, 0x00, //0x00002eaa movzbl       $5517(%rax,%rdx), %ecx
	0x48, 0xff, 0xc0, //0x00002eb2 incq         %rax
	0x49, 0x39, 0xc2, //0x00002eb5 cmpq         %rax, %r10
	0x0f, 0x85, 0xd8, 0xff, 0xff, 0xff, //0x00002eb8 jne          LBB0_665
	0x84, 0xc9, //0x00002ebe testb        %cl, %cl
	0x0f, 0x84, 0x06, 0x00, 0x00, 0x00, //0x00002ec0 je           LBB0_670
	//0x00002ec6 LBB0_669
	0x41, 0xb8, 0x0f, 0x00, 0x00, 0x00, //0x00002ec6 movl         $15, %r8d
	//0x00002ecc LBB0_670
	0x85, 0xdb, //0x00002ecc testl        %ebx, %ebx
	0x0f, 0x8e, 0xf6, 0x00, 0x00, 0x00, //0x00002ece jle          LBB0_681
	0x44, 0x01, 0xc3, //0x00002ed4 addl         %r8d, %ebx
	0x48, 0x63, 0xfb, //0x00002ed7 movslq       %ebx, %rdi
	0x48, 0xff, 0xcf, //0x00002eda decq         %rdi
	0x49, 0xff, 0xc2, //0x00002edd incq         %r10
	0x31, 0xc9, //0x00002ee0 xorl         %ecx, %ecx
	0x49, 0xbb, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfa, //0x00002ee2 movabsq      $-432345564227567616, %r11
	0xe9, 0x1e, 0x00, 0x00, 0x00, //0x00002eec jmp          LBB0_674
	//0x00002ef1 LBB0_672
	0x48, 0x85, 0xc0, //0x00002ef1 testq        %rax, %rax
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00002ef4 movl         $1, %eax
	0x44, 0x0f, 0x45, 0xf0, //0x00002ef9 cmovnel      %eax, %r14d
	//0x00002efd LBB0_673
	0xff, 0xcb, //0x00002efd decl         %ebx
	0x48, 0xff, 0xcf, //0x00002eff decq         %rdi
	0x49, 0xff, 0xca, //0x00002f02 decq         %r10
	0x49, 0x83, 0xfa, 0x01, //0x00002f05 cmpq         $1, %r10
	0x0f, 0x8e, 0x49, 0x00, 0x00, 0x00, //0x00002f09 jle          LBB0_676
	//0x00002f0f LBB0_674
	0x4b, 0x0f, 0xbe, 0x74, 0x14, 0xfe, //0x00002f0f movsbq       $-2(%r12,%r10), %rsi
	0x48, 0xc1, 0xe6, 0x35, //0x00002f15 shlq         $53, %rsi
	0x48, 0x01, 0xce, //0x00002f19 addq         %rcx, %rsi
	0x4c, 0x01, 0xde, //0x00002f1c addq         %r11, %rsi
	0x48, 0x89, 0xf0, //0x00002f1f movq         %rsi, %rax
	0x48, 0xb9, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00002f22 movabsq      $-3689348814741910323, %rcx
	0x48, 0xf7, 0xe1, //0x00002f2c mulq         %rcx
	0x48, 0x89, 0xd1, //0x00002f2f movq         %rdx, %rcx
	0x48, 0xc1, 0xe9, 0x03, //0x00002f32 shrq         $3, %rcx
	0x48, 0x8d, 0x04, 0x09, //0x00002f36 leaq         (%rcx,%rcx), %rax
	0x48, 0x8d, 0x14, 0x80, //0x00002f3a leaq         (%rax,%rax,4), %rdx
	0x48, 0x89, 0xf0, //0x00002f3e movq         %rsi, %rax
	0x48, 0x29, 0xd0, //0x00002f41 subq         %rdx, %rax
	0x4c, 0x39, 0xef, //0x00002f44 cmpq         %r13, %rdi
	0x0f, 0x83, 0xa4, 0xff, 0xff, 0xff, //0x00002f47 jae          LBB0_672
	0x04, 0x30, //0x00002f4d addb         $48, %al
	0x41, 0x88, 0x04, 0x3c, //0x00002f4f movb         %al, (%r12,%rdi)
	0xe9, 0xa5, 0xff, 0xff, 0xff, //0x00002f53 jmp          LBB0_673
	//0x00002f58 LBB0_676
	0x48, 0x83, 0xfe, 0x0a, //0x00002f58 cmpq         $10, %rsi
	0x0f, 0x82, 0x68, 0x00, 0x00, 0x00, //0x00002f5c jb           LBB0_681
	0x48, 0x63, 0xf3, //0x00002f62 movslq       %ebx, %rsi
	0x48, 0xff, 0xce, //0x00002f65 decq         %rsi
	0xbf, 0x01, 0x00, 0x00, 0x00, //0x00002f68 movl         $1, %edi
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00002f6d jmp          LBB0_679
	//0x00002f72 LBB0_678
	0x48, 0x85, 0xc0, //0x00002f72 testq        %rax, %rax
	0x44, 0x0f, 0x45, 0xf7, //0x00002f75 cmovnel      %edi, %r14d
	0x48, 0xff, 0xce, //0x00002f79 decq         %rsi
	0x48, 0x83, 0xf9, 0x09, //0x00002f7c cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x00002f80 movq         %rdx, %rcx
	0x0f, 0x86, 0x41, 0x00, 0x00, 0x00, //0x00002f83 jbe          LBB0_681
	//0x00002f89 LBB0_679
	0x48, 0x89, 0xc8, //0x00002f89 movq         %rcx, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00002f8c movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x00002f96 mulq         %rdx
	0x48, 0xc1, 0xea, 0x03, //0x00002f99 shrq         $3, %rdx
	0x48, 0x8d, 0x04, 0x12, //0x00002f9d leaq         (%rdx,%rdx), %rax
	0x48, 0x8d, 0x1c, 0x80, //0x00002fa1 leaq         (%rax,%rax,4), %rbx
	0x48, 0x89, 0xc8, //0x00002fa5 movq         %rcx, %rax
	0x48, 0x29, 0xd8, //0x00002fa8 subq         %rbx, %rax
	0x4c, 0x39, 0xee, //0x00002fab cmpq         %r13, %rsi
	0x0f, 0x83, 0xbe, 0xff, 0xff, 0xff, //0x00002fae jae          LBB0_678
	0x04, 0x30, //0x00002fb4 addb         $48, %al
	0x41, 0x88, 0x04, 0x34, //0x00002fb6 movb         %al, (%r12,%rsi)
	0x48, 0xff, 0xce, //0x00002fba decq         %rsi
	0x48, 0x83, 0xf9, 0x09, //0x00002fbd cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x00002fc1 movq         %rdx, %rcx
	0x0f, 0x87, 0xbf, 0xff, 0xff, 0xff, //0x00002fc4 ja           LBB0_679
	//0x00002fca LBB0_681
	0x45, 0x01, 0xc1, //0x00002fca addl         %r8d, %r9d
	0x49, 0x63, 0xc1, //0x00002fcd movslq       %r9d, %rax
	0x49, 0x39, 0xc5, //0x00002fd0 cmpq         %rax, %r13
	0x41, 0x0f, 0x46, 0xc5, //0x00002fd3 cmovbel      %r13d, %eax
	0x48, 0x8b, 0x4d, 0xc8, //0x00002fd7 movq         $-56(%rbp), %rcx
	0x44, 0x01, 0xc1, //0x00002fdb addl         %r8d, %ecx
	0x48, 0x89, 0x4d, 0xc8, //0x00002fde movq         %rcx, $-56(%rbp)
	0x85, 0xc0, //0x00002fe2 testl        %eax, %eax
	0x0f, 0x8e, 0x3f, 0x00, 0x00, 0x00, //0x00002fe4 jle          LBB0_687
	0x89, 0xc1, //0x00002fea movl         %eax, %ecx
	0x42, 0x80, 0x7c, 0x21, 0xff, 0x30, //0x00002fec cmpb         $48, $-1(%rcx,%r12)
	0x0f, 0x85, 0x39, 0x00, 0x00, 0x00, //0x00002ff2 jne          LBB0_688
	//0x00002ff8 LBB0_683
	0x48, 0x83, 0xf9, 0x01, //0x00002ff8 cmpq         $1, %rcx
	0x0f, 0x8e, 0x23, 0x00, 0x00, 0x00, //0x00002ffc jle          LBB0_686
	0x48, 0x8d, 0x41, 0xff, //0x00003002 leaq         $-1(%rcx), %rax
	0x41, 0x80, 0x7c, 0x0c, 0xfe, 0x30, //0x00003006 cmpb         $48, $-2(%r12,%rcx)
	0x48, 0x89, 0xc1, //0x0000300c movq         %rax, %rcx
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x0000300f je           LBB0_683
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00003015 jmp          LBB0_688
	//0x0000301a LBB0_685
	0x0f, 0x8c, 0xa6, 0xfe, 0xff, 0xff, //0x0000301a jl           LBB0_669
	0xe9, 0xa7, 0xfe, 0xff, 0xff, //0x00003020 jmp          LBB0_670
	//0x00003025 LBB0_686
	0xff, 0xc9, //0x00003025 decl         %ecx
	0x89, 0xc8, //0x00003027 movl         %ecx, %eax
	//0x00003029 LBB0_687
	0x85, 0xc0, //0x00003029 testl        %eax, %eax
	0x0f, 0x84, 0xc9, 0x00, 0x00, 0x00, //0x0000302b je           LBB0_699
	//0x00003031 LBB0_688
	0x48, 0xb9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00003031 movabsq      $4503599627370495, %rcx
	0x48, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x0000303b movq         $-1, %rsi
	0x48, 0x8b, 0x55, 0xc8, //0x00003042 movq         $-56(%rbp), %rdx
	0x83, 0xfa, 0x14, //0x00003046 cmpl         $20, %edx
	0x0f, 0x8f, 0x24, 0x03, 0x00, 0x00, //0x00003049 jg           LBB0_720
	0x89, 0xd1, //0x0000304f movl         %edx, %ecx
	0x85, 0xd2, //0x00003051 testl        %edx, %edx
	0x0f, 0x8e, 0x32, 0x00, 0x00, 0x00, //0x00003053 jle          LBB0_694
	0x48, 0x63, 0xf8, //0x00003059 movslq       %eax, %rdi
	0x31, 0xf6, //0x0000305c xorl         %esi, %esi
	0x31, 0xd2, //0x0000305e xorl         %edx, %edx
	//0x00003060 LBB0_691
	0x48, 0x39, 0xfe, //0x00003060 cmpq         %rdi, %rsi
	0x0f, 0x8d, 0x1a, 0x00, 0x00, 0x00, //0x00003063 jge          LBB0_693
	0x48, 0x8d, 0x14, 0x92, //0x00003069 leaq         (%rdx,%rdx,4), %rdx
	0x49, 0x0f, 0xbe, 0x1c, 0x34, //0x0000306d movsbq       (%r12,%rsi), %rbx
	0x48, 0x8d, 0x54, 0x53, 0xd0, //0x00003072 leaq         $-48(%rbx,%rdx,2), %rdx
	0x48, 0xff, 0xc6, //0x00003077 incq         %rsi
	0x48, 0x39, 0xf1, //0x0000307a cmpq         %rsi, %rcx
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x0000307d jne          LBB0_691
	//0x00003083 LBB0_693
	0x45, 0x31, 0xc0, //0x00003083 xorl         %r8d, %r8d
	0xe9, 0x07, 0x00, 0x00, 0x00, //0x00003086 jmp          LBB0_695
	//0x0000308b LBB0_694
	0x31, 0xf6, //0x0000308b xorl         %esi, %esi
	0x41, 0xb0, 0x01, //0x0000308d movb         $1, %r8b
	0x31, 0xd2, //0x00003090 xorl         %edx, %edx
	//0x00003092 LBB0_695
	0x48, 0x8b, 0x7d, 0xc8, //0x00003092 movq         $-56(%rbp), %rdi
	0x41, 0x89, 0xfa, //0x00003096 movl         %edi, %r10d
	0x41, 0x29, 0xf2, //0x00003099 subl         %esi, %r10d
	0x0f, 0x8e, 0x33, 0x02, 0x00, 0x00, //0x0000309c jle          LBB0_709
	0x41, 0x83, 0xfa, 0x10, //0x000030a2 cmpl         $16, %r10d
	0x0f, 0x82, 0x14, 0x02, 0x00, 0x00, //0x000030a6 jb           LBB0_707
	0x45, 0x89, 0xd1, //0x000030ac movl         %r10d, %r9d
	0xc5, 0xfa, 0x6f, 0x05, 0x89, 0xd0, 0xff, 0xff, //0x000030af vmovdqu      $-12151(%rip), %xmm0  /* LCPI0_21+0(%rip) */
	0xc4, 0xe3, 0xf9, 0x22, 0xc2, 0x00, //0x000030b7 vpinsrq      $0, %rdx, %xmm0, %xmm0
	0x41, 0x83, 0xe1, 0xf0, //0x000030bd andl         $-16, %r9d
	0xc4, 0xe3, 0x7d, 0x02, 0x05, 0x75, 0xd0, 0xff, 0xff, 0xf0, //0x000030c1 vpblendd     $240, $-12171(%rip), %ymm0, %ymm0  /* LCPI0_21+0(%rip) */
	0x41, 0x8d, 0x51, 0xf0, //0x000030cb leal         $-16(%r9), %edx
	0x89, 0xd7, //0x000030cf movl         %edx, %edi
	0xc1, 0xef, 0x04, //0x000030d1 shrl         $4, %edi
	0xff, 0xc7, //0x000030d4 incl         %edi
	0x89, 0xfb, //0x000030d6 movl         %edi, %ebx
	0x83, 0xe3, 0x03, //0x000030d8 andl         $3, %ebx
	0x83, 0xfa, 0x30, //0x000030db cmpl         $48, %edx
	0x0f, 0x83, 0x1f, 0x00, 0x00, 0x00, //0x000030de jae          LBB0_700
	0xc4, 0xe2, 0x7d, 0x59, 0x15, 0x13, 0xd1, 0xff, 0xff, //0x000030e4 vpbroadcastq $-12013(%rip), %ymm2  /* LCPI0_22+0(%rip) */
	0xc5, 0xfd, 0x6f, 0xda, //0x000030ed vmovdqa      %ymm2, %ymm3
	0xc5, 0xfd, 0x6f, 0xca, //0x000030f1 vmovdqa      %ymm2, %ymm1
	0xe9, 0x88, 0x00, 0x00, 0x00, //0x000030f5 jmp          LBB0_702
	//0x000030fa LBB0_699
	0x31, 0xd2, //0x000030fa xorl         %edx, %edx
	0x31, 0xf6, //0x000030fc xorl         %esi, %esi
	0xe9, 0x29, 0x02, 0x00, 0x00, //0x000030fe jmp          LBB0_716
	//0x00003103 LBB0_700
	0x89, 0xda, //0x00003103 movl         %ebx, %edx
	0x29, 0xfa, //0x00003105 subl         %edi, %edx
	0xc4, 0xe2, 0x7d, 0x59, 0x15, 0xf0, 0xd0, 0xff, 0xff, //0x00003107 vpbroadcastq $-12048(%rip), %ymm2  /* LCPI0_22+0(%rip) */
	0xc4, 0xe2, 0x7d, 0x59, 0x25, 0xef, 0xd0, 0xff, 0xff, //0x00003110 vpbroadcastq $-12049(%rip), %ymm4  /* LCPI0_23+0(%rip) */
	0xc5, 0xfd, 0x6f, 0xda, //0x00003119 vmovdqa      %ymm2, %ymm3
	0xc5, 0xfd, 0x6f, 0xca, //0x0000311d vmovdqa      %ymm2, %ymm1
	//0x00003121 LBB0_701
	0xc5, 0xfd, 0xf4, 0xec, //0x00003121 vpmuludq     %ymm4, %ymm0, %ymm5
	0xc5, 0xfd, 0x73, 0xd0, 0x20, //0x00003125 vpsrlq       $32, %ymm0, %ymm0
	0xc5, 0xfd, 0xf4, 0xc4, //0x0000312a vpmuludq     %ymm4, %ymm0, %ymm0
	0xc5, 0xfd, 0x73, 0xf0, 0x20, //0x0000312e vpsllq       $32, %ymm0, %ymm0
	0xc5, 0xd5, 0xd4, 0xc0, //0x00003133 vpaddq       %ymm0, %ymm5, %ymm0
	0xc5, 0xed, 0xf4, 0xec, //0x00003137 vpmuludq     %ymm4, %ymm2, %ymm5
	0xc5, 0xed, 0x73, 0xd2, 0x20, //0x0000313b vpsrlq       $32, %ymm2, %ymm2
	0xc5, 0xed, 0xf4, 0xd4, //0x00003140 vpmuludq     %ymm4, %ymm2, %ymm2
	0xc5, 0xed, 0x73, 0xf2, 0x20, //0x00003144 vpsllq       $32, %ymm2, %ymm2
	0xc5, 0xd5, 0xd4, 0xd2, //0x00003149 vpaddq       %ymm2, %ymm5, %ymm2
	0xc5, 0xe5, 0xf4, 0xec, //0x0000314d vpmuludq     %ymm4, %ymm3, %ymm5
	0xc5, 0xe5, 0x73, 0xd3, 0x20, //0x00003151 vpsrlq       $32, %ymm3, %ymm3
	0xc5, 0xe5, 0xf4, 0xdc, //0x00003156 vpmuludq     %ymm4, %ymm3, %ymm3
	0xc5, 0xe5, 0x73, 0xf3, 0x20, //0x0000315a vpsllq       $32, %ymm3, %ymm3
	0xc5, 0xd5, 0xd4, 0xdb, //0x0000315f vpaddq       %ymm3, %ymm5, %ymm3
	0xc5, 0xf5, 0xf4, 0xec, //0x00003163 vpmuludq     %ymm4, %ymm1, %ymm5
	0xc5, 0xf5, 0x73, 0xd1, 0x20, //0x00003167 vpsrlq       $32, %ymm1, %ymm1
	0xc5, 0xf5, 0xf4, 0xcc, //0x0000316c vpmuludq     %ymm4, %ymm1, %ymm1
	0xc5, 0xf5, 0x73, 0xf1, 0x20, //0x00003170 vpsllq       $32, %ymm1, %ymm1
	0xc5, 0xd5, 0xd4, 0xc9, //0x00003175 vpaddq       %ymm1, %ymm5, %ymm1
	0x83, 0xc2, 0x04, //0x00003179 addl         $4, %edx
	0x0f, 0x85, 0x9f, 0xff, 0xff, 0xff, //0x0000317c jne          LBB0_701
	//0x00003182 LBB0_702
	0x85, 0xdb, //0x00003182 testl        %ebx, %ebx
	0x0f, 0x84, 0x6b, 0x00, 0x00, 0x00, //0x00003184 je           LBB0_705
	0xf7, 0xdb, //0x0000318a negl         %ebx
	0xc4, 0xe2, 0x7d, 0x59, 0x25, 0x7b, 0xd0, 0xff, 0xff, //0x0000318c vpbroadcastq $-12165(%rip), %ymm4  /* LCPI0_24+0(%rip) */
	//0x00003195 LBB0_704
	0xc5, 0xfd, 0xf4, 0xec, //0x00003195 vpmuludq     %ymm4, %ymm0, %ymm5
	0xc5, 0xfd, 0x73, 0xd0, 0x20, //0x00003199 vpsrlq       $32, %ymm0, %ymm0
	0xc5, 0xfd, 0xf4, 0xc4, //0x0000319e vpmuludq     %ymm4, %ymm0, %ymm0
	0xc5, 0xfd, 0x73, 0xf0, 0x20, //0x000031a2 vpsllq       $32, %ymm0, %ymm0
	0xc5, 0xd5, 0xd4, 0xc0, //0x000031a7 vpaddq       %ymm0, %ymm5, %ymm0
	0xc5, 0xed, 0xf4, 0xec, //0x000031ab vpmuludq     %ymm4, %ymm2, %ymm5
	0xc5, 0xed, 0x73, 0xd2, 0x20, //0x000031af vpsrlq       $32, %ymm2, %ymm2
	0xc5, 0xed, 0xf4, 0xd4, //0x000031b4 vpmuludq     %ymm4, %ymm2, %ymm2
	0xc5, 0xed, 0x73, 0xf2, 0x20, //0x000031b8 vpsllq       $32, %ymm2, %ymm2
	0xc5, 0xd5, 0xd4, 0xd2, //0x000031bd vpaddq       %ymm2, %ymm5, %ymm2
	0xc5, 0xe5, 0xf4, 0xec, //0x000031c1 vpmuludq     %ymm4, %ymm3, %ymm5
	0xc5, 0xe5, 0x73, 0xd3, 0x20, //0x000031c5 vpsrlq       $32, %ymm3, %ymm3
	0xc5, 0xe5, 0xf4, 0xdc, //0x000031ca vpmuludq     %ymm4, %ymm3, %ymm3
	0xc5, 0xe5, 0x73, 0xf3, 0x20, //0x000031ce vpsllq       $32, %ymm3, %ymm3
	0xc5, 0xd5, 0xd4, 0xdb, //0x000031d3 vpaddq       %ymm3, %ymm5, %ymm3
	0xc5, 0xf5, 0xf4, 0xec, //0x000031d7 vpmuludq     %ymm4, %ymm1, %ymm5
	0xc5, 0xf5, 0x73, 0xd1, 0x20, //0x000031db vpsrlq       $32, %ymm1, %ymm1
	0xc5, 0xf5, 0xf4, 0xcc, //0x000031e0 vpmuludq     %ymm4, %ymm1, %ymm1
	0xc5, 0xf5, 0x73, 0xf1, 0x20, //0x000031e4 vpsllq       $32, %ymm1, %ymm1
	0xc5, 0xd5, 0xd4, 0xc9, //0x000031e9 vpaddq       %ymm1, %ymm5, %ymm1
	0xff, 0xc3, //0x000031ed incl         %ebx
	0x0f, 0x85, 0xa0, 0xff, 0xff, 0xff, //0x000031ef jne          LBB0_704
	//0x000031f5 LBB0_705
	0xc5, 0xdd, 0x73, 0xd2, 0x20, //0x000031f5 vpsrlq       $32, %ymm2, %ymm4
	0xc5, 0xdd, 0xf4, 0xe0, //0x000031fa vpmuludq     %ymm0, %ymm4, %ymm4
	0xc5, 0xd5, 0x73, 0xd0, 0x20, //0x000031fe vpsrlq       $32, %ymm0, %ymm5
	0xc5, 0xed, 0xf4, 0xed, //0x00003203 vpmuludq     %ymm5, %ymm2, %ymm5
	0xc5, 0xd5, 0xd4, 0xe4, //0x00003207 vpaddq       %ymm4, %ymm5, %ymm4
	0xc5, 0xdd, 0x73, 0xf4, 0x20, //0x0000320b vpsllq       $32, %ymm4, %ymm4
	0xc5, 0xed, 0xf4, 0xc0, //0x00003210 vpmuludq     %ymm0, %ymm2, %ymm0
	0xc5, 0xfd, 0xd4, 0xc4, //0x00003214 vpaddq       %ymm4, %ymm0, %ymm0
	0xc5, 0xed, 0x73, 0xd3, 0x20, //0x00003218 vpsrlq       $32, %ymm3, %ymm2
	0xc5, 0xed, 0xf4, 0xd0, //0x0000321d vpmuludq     %ymm0, %ymm2, %ymm2
	0xc5, 0xdd, 0x73, 0xd0, 0x20, //0x00003221 vpsrlq       $32, %ymm0, %ymm4
	0xc5, 0xe5, 0xf4, 0xe4, //0x00003226 vpmuludq     %ymm4, %ymm3, %ymm4
	0xc5, 0xdd, 0xd4, 0xd2, //0x0000322a vpaddq       %ymm2, %ymm4, %ymm2
	0xc5, 0xed, 0x73, 0xf2, 0x20, //0x0000322e vpsllq       $32, %ymm2, %ymm2
	0xc5, 0xe5, 0xf4, 0xc0, //0x00003233 vpmuludq     %ymm0, %ymm3, %ymm0
	0xc5, 0xfd, 0xd4, 0xc2, //0x00003237 vpaddq       %ymm2, %ymm0, %ymm0
	0xc5, 0xed, 0x73, 0xd1, 0x20, //0x0000323b vpsrlq       $32, %ymm1, %ymm2
	0xc5, 0xed, 0xf4, 0xd0, //0x00003240 vpmuludq     %ymm0, %ymm2, %ymm2
	0xc5, 0xe5, 0x73, 0xd0, 0x20, //0x00003244 vpsrlq       $32, %ymm0, %ymm3
	0xc5, 0xf5, 0xf4, 0xdb, //0x00003249 vpmuludq     %ymm3, %ymm1, %ymm3
	0xc5, 0xe5, 0xd4, 0xd2, //0x0000324d vpaddq       %ymm2, %ymm3, %ymm2
	0xc5, 0xed, 0x73, 0xf2, 0x20, //0x00003251 vpsllq       $32, %ymm2, %ymm2
	0xc5, 0xf5, 0xf4, 0xc0, //0x00003256 vpmuludq     %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xd4, 0xc2, //0x0000325a vpaddq       %ymm2, %ymm0, %ymm0
	0xc4, 0xe3, 0x7d, 0x39, 0xc1, 0x01, //0x0000325e vextracti128 $1, %ymm0, %xmm1
	0xc5, 0xe9, 0x73, 0xd0, 0x20, //0x00003264 vpsrlq       $32, %xmm0, %xmm2
	0xc5, 0xe9, 0xf4, 0xd1, //0x00003269 vpmuludq     %xmm1, %xmm2, %xmm2
	0xc5, 0xe1, 0x73, 0xd1, 0x20, //0x0000326d vpsrlq       $32, %xmm1, %xmm3
	0xc5, 0xf9, 0xf4, 0xdb, //0x00003272 vpmuludq     %xmm3, %xmm0, %xmm3
	0xc5, 0xe1, 0xd4, 0xd2, //0x00003276 vpaddq       %xmm2, %xmm3, %xmm2
	0xc5, 0xe9, 0x73, 0xf2, 0x20, //0x0000327a vpsllq       $32, %xmm2, %xmm2
	0xc5, 0xf9, 0xf4, 0xc1, //0x0000327f vpmuludq     %xmm1, %xmm0, %xmm0
	0xc5, 0xf9, 0xd4, 0xc2, //0x00003283 vpaddq       %xmm2, %xmm0, %xmm0
	0xc5, 0xf9, 0x70, 0xc8, 0x4e, //0x00003287 vpshufd      $78, %xmm0, %xmm1
	0xc5, 0xe9, 0x73, 0xd0, 0x20, //0x0000328c vpsrlq       $32, %xmm0, %xmm2
	0xc5, 0xe9, 0xf4, 0xd1, //0x00003291 vpmuludq     %xmm1, %xmm2, %xmm2
	0xc5, 0xe1, 0x73, 0xd8, 0x0c, //0x00003295 vpsrldq      $12, %xmm0, %xmm3
	0xc5, 0xf9, 0xf4, 0xdb, //0x0000329a vpmuludq     %xmm3, %xmm0, %xmm3
	0xc5, 0xe1, 0xd4, 0xd2, //0x0000329e vpaddq       %xmm2, %xmm3, %xmm2
	0xc5, 0xe9, 0x73, 0xf2, 0x20, //0x000032a2 vpsllq       $32, %xmm2, %xmm2
	0xc5, 0xf9, 0xf4, 0xc1, //0x000032a7 vpmuludq     %xmm1, %xmm0, %xmm0
	0xc5, 0xf9, 0xd4, 0xc2, //0x000032ab vpaddq       %xmm2, %xmm0, %xmm0
	0xc4, 0xe1, 0xf9, 0x7e, 0xc2, //0x000032af vmovq        %xmm0, %rdx
	0x45, 0x39, 0xca, //0x000032b4 cmpl         %r9d, %r10d
	0x0f, 0x84, 0x18, 0x00, 0x00, 0x00, //0x000032b7 je           LBB0_709
	0x44, 0x01, 0xce, //0x000032bd addl         %r9d, %esi
	//0x000032c0 LBB0_707
	0x48, 0x8b, 0x7d, 0xc8, //0x000032c0 movq         $-56(%rbp), %rdi
	0x29, 0xf7, //0x000032c4 subl         %esi, %edi
	//0x000032c6 LBB0_708
	0x48, 0x01, 0xd2, //0x000032c6 addq         %rdx, %rdx
	0x48, 0x8d, 0x14, 0x92, //0x000032c9 leaq         (%rdx,%rdx,4), %rdx
	0xff, 0xcf, //0x000032cd decl         %edi
	0x0f, 0x85, 0xf1, 0xff, 0xff, 0xff, //0x000032cf jne          LBB0_708
	//0x000032d5 LBB0_709
	0x31, 0xf6, //0x000032d5 xorl         %esi, %esi
	0x48, 0x8b, 0x7d, 0xc8, //0x000032d7 movq         $-56(%rbp), %rdi
	0x85, 0xff, //0x000032db testl        %edi, %edi
	0x0f, 0x88, 0x49, 0x00, 0x00, 0x00, //0x000032dd js           LBB0_716
	0x39, 0xf8, //0x000032e3 cmpl         %edi, %eax
	0x0f, 0x8e, 0x41, 0x00, 0x00, 0x00, //0x000032e5 jle          LBB0_716
	0x41, 0x8a, 0x0c, 0x0c, //0x000032eb movb         (%r12,%rcx), %cl
	0x8d, 0x77, 0x01, //0x000032ef leal         $1(%rdi), %esi
	0x39, 0xc6, //0x000032f2 cmpl         %eax, %esi
	0x0f, 0x85, 0x2b, 0x00, 0x00, 0x00, //0x000032f4 jne          LBB0_715
	0x80, 0xf9, 0x35, //0x000032fa cmpb         $53, %cl
	0x0f, 0x85, 0x22, 0x00, 0x00, 0x00, //0x000032fd jne          LBB0_715
	0x45, 0x85, 0xf6, //0x00003303 testl        %r14d, %r14d
	0x40, 0x0f, 0x95, 0xc6, //0x00003306 setne        %sil
	0x41, 0x08, 0xf0, //0x0000330a orb          %sil, %r8b
	0x0f, 0x85, 0x19, 0x00, 0x00, 0x00, //0x0000330d jne          LBB0_716
	0x48, 0x63, 0x45, 0xc8, //0x00003313 movslq       $-56(%rbp), %rax
	0x42, 0x8a, 0x74, 0x20, 0xff, //0x00003317 movb         $-1(%rax,%r12), %sil
	0x40, 0x80, 0xe6, 0x01, //0x0000331c andb         $1, %sil
	0xe9, 0x07, 0x00, 0x00, 0x00, //0x00003320 jmp          LBB0_716
	//0x00003325 LBB0_715
	0x80, 0xf9, 0x34, //0x00003325 cmpb         $52, %cl
	0x40, 0x0f, 0x9f, 0xc6, //0x00003328 setg         %sil
	//0x0000332c LBB0_716
	0x40, 0x0f, 0xb6, 0xf6, //0x0000332c movzbl       %sil, %esi
	0x48, 0x01, 0xd6, //0x00003330 addq         %rdx, %rsi
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, //0x00003333 movabsq      $9007199254740992, %rax
	0x48, 0x39, 0xc6, //0x0000333d cmpq         %rax, %rsi
	0x0f, 0x85, 0x23, 0x00, 0x00, 0x00, //0x00003340 jne          LBB0_719
	0x41, 0x81, 0xff, 0xfe, 0x03, 0x00, 0x00, //0x00003346 cmpl         $1022, %r15d
	0x0f, 0x8f, 0x32, 0xe7, 0xff, 0xff, //0x0000334d jg           LBB0_353
	0x41, 0xff, 0xc7, //0x00003353 incl         %r15d
	0x48, 0xb9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00003356 movabsq      $4503599627370495, %rcx
	0x48, 0x8d, 0x71, 0x01, //0x00003360 leaq         $1(%rcx), %rsi
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00003364 jmp          LBB0_720
	//0x00003369 LBB0_719
	0x48, 0xb9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00003369 movabsq      $4503599627370495, %rcx
	//0x00003373 LBB0_720
	0x8a, 0x55, 0xd7, //0x00003373 movb         $-41(%rbp), %dl
	0x48, 0x8d, 0x41, 0x01, //0x00003376 leaq         $1(%rcx), %rax
	0x48, 0x21, 0xf0, //0x0000337a andq         %rsi, %rax
	0x41, 0x81, 0xc7, 0xff, 0x03, 0x00, 0x00, //0x0000337d addl         $1023, %r15d
	0x41, 0x81, 0xe7, 0xff, 0x07, 0x00, 0x00, //0x00003384 andl         $2047, %r15d
	0x49, 0xc1, 0xe7, 0x34, //0x0000338b shlq         $52, %r15
	0x48, 0x85, 0xc0, //0x0000338f testq        %rax, %rax
	0x4c, 0x0f, 0x44, 0xf8, //0x00003392 cmoveq       %rax, %r15
	0xe9, 0x24, 0xe7, 0xff, 0xff, //0x00003396 jmp          LBB0_358
	//0x0000339b LBB0_723
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000339b movq         $-1, %r8
	0x45, 0x31, 0xff, //0x000033a2 xorl         %r15d, %r15d
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000033a5 movq         $-1, %rbx
	0x49, 0x83, 0xfd, 0x20, //0x000033ac cmpq         $32, %r13
	0x0f, 0x83, 0x51, 0xdf, 0xff, 0xff, //0x000033b0 jae          LBB0_261
	0xe9, 0x96, 0x00, 0x00, 0x00, //0x000033b6 jmp          LBB0_730
	//0x000033bb LBB0_724
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000033bb movq         $-1, %r8
	0x45, 0x31, 0xe4, //0x000033c2 xorl         %r12d, %r12d
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000033c5 movq         $-1, %r9
	0x49, 0x83, 0xfd, 0x20, //0x000033cc cmpq         $32, %r13
	0x0f, 0x83, 0xb5, 0xdf, 0xff, 0xff, //0x000033d0 jae          LBB0_268
	0xe9, 0x86, 0x01, 0x00, 0x00, //0x000033d6 jmp          LBB0_749
	//0x000033db LBB0_725
	0x48, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x000033db movq         $-2, %rbx
	0xe9, 0x29, 0x02, 0x00, 0x00, //0x000033e2 jmp          LBB0_762
	//0x000033e7 LBB0_726
	0x48, 0x83, 0xfb, 0xff, //0x000033e7 cmpq         $-1, %rbx
	0x0f, 0x85, 0x10, 0x00, 0x00, 0x00, //0x000033eb jne          LBB0_728
	0x48, 0x89, 0xce, //0x000033f1 movq         %rcx, %rsi
	0x4c, 0x29, 0xd6, //0x000033f4 subq         %r10, %rsi
	0x4c, 0x0f, 0xbc, 0xc7, //0x000033f7 bsfq         %rdi, %r8
	0x49, 0x01, 0xf0, //0x000033fb addq         %rsi, %r8
	0x4c, 0x89, 0xc3, //0x000033fe movq         %r8, %rbx
	//0x00003401 LBB0_728
	0x44, 0x89, 0xfe, //0x00003401 movl         %r15d, %esi
	0xf7, 0xd6, //0x00003404 notl         %esi
	0x21, 0xfe, //0x00003406 andl         %edi, %esi
	0x44, 0x8d, 0x0c, 0x36, //0x00003408 leal         (%rsi,%rsi), %r9d
	0x45, 0x09, 0xf9, //0x0000340c orl          %r15d, %r9d
	0x45, 0x89, 0xcc, //0x0000340f movl         %r9d, %r12d
	0x41, 0xf7, 0xd4, //0x00003412 notl         %r12d
	0x41, 0x21, 0xfc, //0x00003415 andl         %edi, %r12d
	0x41, 0x81, 0xe4, 0xaa, 0xaa, 0xaa, 0xaa, //0x00003418 andl         $-1431655766, %r12d
	0x45, 0x31, 0xff, //0x0000341f xorl         %r15d, %r15d
	0x41, 0x01, 0xf4, //0x00003422 addl         %esi, %r12d
	0x41, 0x0f, 0x92, 0xc7, //0x00003425 setb         %r15b
	0x45, 0x01, 0xe4, //0x00003429 addl         %r12d, %r12d
	0x41, 0x81, 0xf4, 0x55, 0x55, 0x55, 0x55, //0x0000342c xorl         $1431655765, %r12d
	0x45, 0x21, 0xcc, //0x00003433 andl         %r9d, %r12d
	0xbe, 0xff, 0xff, 0xff, 0xff, //0x00003436 movl         $4294967295, %esi
	0x44, 0x31, 0xe6, //0x0000343b xorl         %r12d, %esi
	0x21, 0xf0, //0x0000343e andl         %esi, %eax
	0x48, 0x85, 0xc0, //0x00003440 testq        %rax, %rax
	0x0f, 0x85, 0xf7, 0xde, 0xff, 0xff, //0x00003443 jne          LBB0_264
	//0x00003449 LBB0_729
	0x48, 0x83, 0xc1, 0x20, //0x00003449 addq         $32, %rcx
	0x49, 0x83, 0xc5, 0xe0, //0x0000344d addq         $-32, %r13
	//0x00003451 LBB0_730
	0x4d, 0x85, 0xff, //0x00003451 testq        %r15, %r15
	0x0f, 0x85, 0xd1, 0x01, 0x00, 0x00, //0x00003454 jne          LBB0_764
	0x4d, 0x85, 0xed, //0x0000345a testq        %r13, %r13
	0x0f, 0x84, 0xf5, 0x01, 0x00, 0x00, //0x0000345d je           LBB0_722
	//0x00003463 LBB0_732
	0x4d, 0x89, 0xd1, //0x00003463 movq         %r10, %r9
	0x49, 0xf7, 0xd1, //0x00003466 notq         %r9
	//0x00003469 LBB0_733
	0x48, 0x8d, 0x71, 0x01, //0x00003469 leaq         $1(%rcx), %rsi
	0x0f, 0xb6, 0x01, //0x0000346d movzbl       (%rcx), %eax
	0x3c, 0x22, //0x00003470 cmpb         $34, %al
	0x0f, 0x84, 0x51, 0x00, 0x00, 0x00, //0x00003472 je           LBB0_739
	0x49, 0x8d, 0x7d, 0xff, //0x00003478 leaq         $-1(%r13), %rdi
	0x3c, 0x5c, //0x0000347c cmpb         $92, %al
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x0000347e je           LBB0_736
	0x49, 0x89, 0xfd, //0x00003484 movq         %rdi, %r13
	0x48, 0x89, 0xf1, //0x00003487 movq         %rsi, %rcx
	0x48, 0x85, 0xff, //0x0000348a testq        %rdi, %rdi
	0x0f, 0x85, 0xd6, 0xff, 0xff, 0xff, //0x0000348d jne          LBB0_733
	0xe9, 0xc0, 0x01, 0x00, 0x00, //0x00003493 jmp          LBB0_722
	//0x00003498 LBB0_736
	0x48, 0x85, 0xff, //0x00003498 testq        %rdi, %rdi
	0x0f, 0x84, 0xb7, 0x01, 0x00, 0x00, //0x0000349b je           LBB0_722
	0x4c, 0x01, 0xce, //0x000034a1 addq         %r9, %rsi
	0x48, 0x83, 0xfb, 0xff, //0x000034a4 cmpq         $-1, %rbx
	0x4c, 0x0f, 0x44, 0xc6, //0x000034a8 cmoveq       %rsi, %r8
	0x48, 0x0f, 0x44, 0xde, //0x000034ac cmoveq       %rsi, %rbx
	0x48, 0x83, 0xc1, 0x02, //0x000034b0 addq         $2, %rcx
	0x49, 0x83, 0xc5, 0xfe, //0x000034b4 addq         $-2, %r13
	0x4c, 0x89, 0xef, //0x000034b8 movq         %r13, %rdi
	0x48, 0x85, 0xff, //0x000034bb testq        %rdi, %rdi
	0x0f, 0x85, 0xa5, 0xff, 0xff, 0xff, //0x000034be jne          LBB0_733
	0xe9, 0x8f, 0x01, 0x00, 0x00, //0x000034c4 jmp          LBB0_722
	//0x000034c9 LBB0_739
	0x4c, 0x29, 0xd6, //0x000034c9 subq         %r10, %rsi
	0x48, 0x89, 0xf3, //0x000034cc movq         %rsi, %rbx
	0xe9, 0x78, 0xde, 0xff, 0xff, //0x000034cf jmp          LBB0_265
	//0x000034d4 LBB0_740
	0x49, 0x83, 0xf9, 0xff, //0x000034d4 cmpq         $-1, %r9
	0x0f, 0x85, 0x11, 0x00, 0x00, 0x00, //0x000034d8 jne          LBB0_742
	0x48, 0x89, 0xde, //0x000034de movq         %rbx, %rsi
	0x48, 0x2b, 0x75, 0xc8, //0x000034e1 subq         $-56(%rbp), %rsi
	0x4c, 0x0f, 0xbc, 0xc1, //0x000034e5 bsfq         %rcx, %r8
	0x49, 0x01, 0xf0, //0x000034e9 addq         %rsi, %r8
	0x4d, 0x89, 0xc1, //0x000034ec movq         %r8, %r9
	//0x000034ef LBB0_742
	0x44, 0x89, 0xe6, //0x000034ef movl         %r12d, %esi
	0xf7, 0xd6, //0x000034f2 notl         %esi
	0x21, 0xce, //0x000034f4 andl         %ecx, %esi
	0x44, 0x8d, 0x34, 0x36, //0x000034f6 leal         (%rsi,%rsi), %r14d
	0x45, 0x09, 0xe6, //0x000034fa orl          %r12d, %r14d
	0x44, 0x89, 0xf7, //0x000034fd movl         %r14d, %edi
	0xf7, 0xd7, //0x00003500 notl         %edi
	0x21, 0xcf, //0x00003502 andl         %ecx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00003504 andl         $-1431655766, %edi
	0x45, 0x31, 0xe4, //0x0000350a xorl         %r12d, %r12d
	0x01, 0xf7, //0x0000350d addl         %esi, %edi
	0x41, 0x0f, 0x92, 0xc4, //0x0000350f setb         %r12b
	0x01, 0xff, //0x00003513 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00003515 xorl         $1431655765, %edi
	0x44, 0x21, 0xf7, //0x0000351b andl         %r14d, %edi
	0xb9, 0xff, 0xff, 0xff, 0xff, //0x0000351e movl         $4294967295, %ecx
	0x31, 0xf9, //0x00003523 xorl         %edi, %ecx
	0x21, 0xc8, //0x00003525 andl         %ecx, %eax
	0x48, 0x85, 0xc0, //0x00003527 testq        %rax, %rax
	0x0f, 0x85, 0xb0, 0xde, 0xff, 0xff, //0x0000352a jne          LBB0_271
	//0x00003530 LBB0_743
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00003530 movl         $64, %ecx
	//0x00003535 LBB0_744
	0x48, 0x85, 0xc0, //0x00003535 testq        %rax, %rax
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00003538 je           LBB0_747
	0x45, 0x85, 0xd2, //0x0000353e testl        %r10d, %r10d
	0x0f, 0x84, 0x9e, 0x00, 0x00, 0x00, //0x00003541 je           LBB0_757
	0x49, 0x0f, 0xbc, 0xc2, //0x00003547 bsfq         %r10, %rax
	0xe9, 0x9a, 0x00, 0x00, 0x00, //0x0000354b jmp          LBB0_758
	//0x00003550 LBB0_747
	0x45, 0x85, 0xd2, //0x00003550 testl        %r10d, %r10d
	0x0f, 0x85, 0xac, 0x00, 0x00, 0x00, //0x00003553 jne          LBB0_760
	0x48, 0x83, 0xc3, 0x20, //0x00003559 addq         $32, %rbx
	0x49, 0x83, 0xc5, 0xe0, //0x0000355d addq         $-32, %r13
	//0x00003561 LBB0_749
	0x4d, 0x85, 0xe4, //0x00003561 testq        %r12, %r12
	0x0f, 0x85, 0xfa, 0x00, 0x00, 0x00, //0x00003564 jne          LBB0_767
	0x4d, 0x85, 0xed, //0x0000356a testq        %r13, %r13
	0x0f, 0x84, 0x1f, 0x01, 0x00, 0x00, //0x0000356d je           LBB0_769
	//0x00003573 LBB0_751
	0x0f, 0xb6, 0x03, //0x00003573 movzbl       (%rbx), %eax
	0x3c, 0x22, //0x00003576 cmpb         $34, %al
	0x0f, 0x84, 0x9d, 0x00, 0x00, 0x00, //0x00003578 je           LBB0_763
	0x3c, 0x5c, //0x0000357e cmpb         $92, %al
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x00003580 je           LBB0_755
	0x3c, 0x20, //0x00003586 cmpb         $32, %al
	0x0f, 0x82, 0x77, 0x00, 0x00, 0x00, //0x00003588 jb           LBB0_760
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000358e movq         $-1, %rax
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x00003595 movl         $1, %ecx
	0x48, 0x01, 0xcb, //0x0000359a addq         %rcx, %rbx
	0x49, 0x01, 0xc5, //0x0000359d addq         %rax, %r13
	0x0f, 0x85, 0xcd, 0xff, 0xff, 0xff, //0x000035a0 jne          LBB0_751
	0xe9, 0xe7, 0x00, 0x00, 0x00, //0x000035a6 jmp          LBB0_769
	//0x000035ab LBB0_755
	0x49, 0x83, 0xfd, 0x01, //0x000035ab cmpq         $1, %r13
	0x0f, 0x84, 0xdd, 0x00, 0x00, 0x00, //0x000035af je           LBB0_769
	0x48, 0x89, 0xd8, //0x000035b5 movq         %rbx, %rax
	0x48, 0x2b, 0x45, 0xc8, //0x000035b8 subq         $-56(%rbp), %rax
	0x49, 0x83, 0xf9, 0xff, //0x000035bc cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc0, //0x000035c0 cmoveq       %rax, %r8
	0x4c, 0x0f, 0x44, 0xc8, //0x000035c4 cmoveq       %rax, %r9
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x000035c8 movq         $-2, %rax
	0xb9, 0x02, 0x00, 0x00, 0x00, //0x000035cf movl         $2, %ecx
	0x48, 0x01, 0xcb, //0x000035d4 addq         %rcx, %rbx
	0x49, 0x01, 0xc5, //0x000035d7 addq         %rax, %r13
	0x0f, 0x85, 0x93, 0xff, 0xff, 0xff, //0x000035da jne          LBB0_751
	0xe9, 0xad, 0x00, 0x00, 0x00, //0x000035e0 jmp          LBB0_769
	//0x000035e5 LBB0_757
	0xb8, 0x40, 0x00, 0x00, 0x00, //0x000035e5 movl         $64, %eax
	//0x000035ea LBB0_758
	0x48, 0x39, 0xc8, //0x000035ea cmpq         %rcx, %rax
	0x0f, 0x82, 0x12, 0x00, 0x00, 0x00, //0x000035ed jb           LBB0_760
	0x48, 0x2b, 0x5d, 0xc8, //0x000035f3 subq         $-56(%rbp), %rbx
	0x48, 0x8d, 0x5c, 0x0b, 0x01, //0x000035f7 leaq         $1(%rbx,%rcx), %rbx
	0x4c, 0x8b, 0x75, 0xb8, //0x000035fc movq         $-72(%rbp), %r14
	0xe9, 0x47, 0xdd, 0xff, 0xff, //0x00003600 jmp          LBB0_265
	//0x00003605 LBB0_760
	0x48, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x00003605 movq         $-2, %rbx
	//0x0000360c LBB0_761
	0x4c, 0x8b, 0x75, 0xb8, //0x0000360c movq         $-72(%rbp), %r14
	//0x00003610 LBB0_762
	0x49, 0x89, 0x1e, //0x00003610 movq         %rbx, (%r14)
	0x4c, 0x89, 0xdb, //0x00003613 movq         %r11, %rbx
	0xe9, 0x13, 0xda, 0xff, 0xff, //0x00003616 jmp          LBB0_202
	//0x0000361b LBB0_763
	0x48, 0x2b, 0x5d, 0xc8, //0x0000361b subq         $-56(%rbp), %rbx
	0x48, 0xff, 0xc3, //0x0000361f incq         %rbx
	0x4c, 0x8b, 0x75, 0xb8, //0x00003622 movq         $-72(%rbp), %r14
	0xe9, 0x21, 0xdd, 0xff, 0xff, //0x00003626 jmp          LBB0_265
	//0x0000362b LBB0_764
	0x4d, 0x85, 0xed, //0x0000362b testq        %r13, %r13
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x0000362e je           LBB0_722
	0x4c, 0x89, 0xd0, //0x00003634 movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x00003637 notq         %rax
	0x48, 0x01, 0xc8, //0x0000363a addq         %rcx, %rax
	0x48, 0x83, 0xfb, 0xff, //0x0000363d cmpq         $-1, %rbx
	0x4c, 0x0f, 0x44, 0xc0, //0x00003641 cmoveq       %rax, %r8
	0x48, 0x0f, 0x44, 0xd8, //0x00003645 cmoveq       %rax, %rbx
	0x48, 0xff, 0xc1, //0x00003649 incq         %rcx
	0x49, 0xff, 0xcd, //0x0000364c decq         %r13
	0x4d, 0x85, 0xed, //0x0000364f testq        %r13, %r13
	0x0f, 0x85, 0x0b, 0xfe, 0xff, 0xff, //0x00003652 jne          LBB0_732
	//0x00003658 LBB0_722
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00003658 movq         $-1, %rbx
	0xe9, 0xac, 0xff, 0xff, 0xff, //0x0000365f jmp          LBB0_762
	//0x00003664 LBB0_767
	0x4d, 0x85, 0xed, //0x00003664 testq        %r13, %r13
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x00003667 je           LBB0_771
	0x48, 0x8b, 0x45, 0xc8, //0x0000366d movq         $-56(%rbp), %rax
	0x48, 0xf7, 0xd0, //0x00003671 notq         %rax
	0x48, 0x01, 0xd8, //0x00003674 addq         %rbx, %rax
	0x49, 0x83, 0xf9, 0xff, //0x00003677 cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc0, //0x0000367b cmoveq       %rax, %r8
	0x4c, 0x0f, 0x44, 0xc8, //0x0000367f cmoveq       %rax, %r9
	0x48, 0xff, 0xc3, //0x00003683 incq         %rbx
	0x49, 0xff, 0xcd, //0x00003686 decq         %r13
	0x4d, 0x85, 0xed, //0x00003689 testq        %r13, %r13
	0x0f, 0x85, 0xe1, 0xfe, 0xff, 0xff, //0x0000368c jne          LBB0_751
	//0x00003692 LBB0_769
	0x4c, 0x8b, 0x75, 0xb8, //0x00003692 movq         $-72(%rbp), %r14
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00003696 movq         $-1, %rbx
	0xe9, 0x6e, 0xff, 0xff, 0xff, //0x0000369d jmp          LBB0_762
	//0x000036a2 LBB0_771
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000036a2 movq         $-1, %rbx
	0xe9, 0x5e, 0xff, 0xff, 0xff, //0x000036a9 jmp          LBB0_761
	0x90, 0x90, //0x000036ae .p2align 2, 0x90
	// // .set L0_0_set_200, LBB0_200-LJTI0_0
	// // .set L0_0_set_129, LBB0_129-LJTI0_0
	// // .set L0_0_set_113, LBB0_113-LJTI0_0
	// // .set L0_0_set_124, LBB0_124-LJTI0_0
	// // .set L0_0_set_35, LBB0_35-LJTI0_0
	// // .set L0_0_set_130, LBB0_130-LJTI0_0
	// // .set L0_0_set_131, LBB0_131-LJTI0_0
	// // .set L0_0_set_138, LBB0_138-LJTI0_0
	// // .set L0_0_set_132, LBB0_132-LJTI0_0
	// // .set L0_0_set_125, LBB0_125-LJTI0_0
	// // .set L0_0_set_135, LBB0_135-LJTI0_0
	// // .set L0_0_set_140, LBB0_140-LJTI0_0
	// // .set L0_0_set_128, LBB0_128-LJTI0_0
	//0x000036b0 LJTI0_0
	0x74, 0xd9, 0xff, 0xff, //0x000036b0 .long L0_0_set_200
	0x90, 0xd4, 0xff, 0xff, //0x000036b4 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000036b8 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000036bc .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000036c0 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000036c4 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000036c8 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000036cc .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000036d0 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000036d4 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000036d8 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000036dc .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000036e0 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000036e4 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000036e8 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000036ec .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000036f0 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000036f4 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000036f8 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000036fc .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003700 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003704 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003708 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x0000370c .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003710 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003714 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003718 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x0000371c .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003720 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003724 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003728 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x0000372c .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003730 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003734 .long L0_0_set_129
	0xe2, 0xd2, 0xff, 0xff, //0x00003738 .long L0_0_set_113
	0x90, 0xd4, 0xff, 0xff, //0x0000373c .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003740 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003744 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003748 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x0000374c .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003750 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003754 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003758 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x0000375c .long L0_0_set_129
	0x3b, 0xd4, 0xff, 0xff, //0x00003760 .long L0_0_set_124
	0x60, 0xcd, 0xff, 0xff, //0x00003764 .long L0_0_set_35
	0x90, 0xd4, 0xff, 0xff, //0x00003768 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x0000376c .long L0_0_set_129
	0x60, 0xcd, 0xff, 0xff, //0x00003770 .long L0_0_set_35
	0x60, 0xcd, 0xff, 0xff, //0x00003774 .long L0_0_set_35
	0x60, 0xcd, 0xff, 0xff, //0x00003778 .long L0_0_set_35
	0x60, 0xcd, 0xff, 0xff, //0x0000377c .long L0_0_set_35
	0x60, 0xcd, 0xff, 0xff, //0x00003780 .long L0_0_set_35
	0x60, 0xcd, 0xff, 0xff, //0x00003784 .long L0_0_set_35
	0x60, 0xcd, 0xff, 0xff, //0x00003788 .long L0_0_set_35
	0x60, 0xcd, 0xff, 0xff, //0x0000378c .long L0_0_set_35
	0x60, 0xcd, 0xff, 0xff, //0x00003790 .long L0_0_set_35
	0x60, 0xcd, 0xff, 0xff, //0x00003794 .long L0_0_set_35
	0x9f, 0xd4, 0xff, 0xff, //0x00003798 .long L0_0_set_130
	0x90, 0xd4, 0xff, 0xff, //0x0000379c .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000037a0 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000037a4 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000037a8 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000037ac .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000037b0 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000037b4 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000037b8 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000037bc .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000037c0 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000037c4 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000037c8 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000037cc .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000037d0 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000037d4 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000037d8 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000037dc .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000037e0 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000037e4 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000037e8 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000037ec .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000037f0 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000037f4 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000037f8 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x000037fc .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003800 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003804 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003808 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x0000380c .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003810 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003814 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003818 .long L0_0_set_129
	0xb1, 0xd4, 0xff, 0xff, //0x0000381c .long L0_0_set_131
	0x90, 0xd4, 0xff, 0xff, //0x00003820 .long L0_0_set_129
	0x1f, 0xd5, 0xff, 0xff, //0x00003824 .long L0_0_set_138
	0x90, 0xd4, 0xff, 0xff, //0x00003828 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x0000382c .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003830 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003834 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003838 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x0000383c .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003840 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003844 .long L0_0_set_129
	0xbd, 0xd4, 0xff, 0xff, //0x00003848 .long L0_0_set_132
	0x90, 0xd4, 0xff, 0xff, //0x0000384c .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003850 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003854 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003858 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x0000385c .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003860 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003864 .long L0_0_set_129
	0x4d, 0xd4, 0xff, 0xff, //0x00003868 .long L0_0_set_125
	0x90, 0xd4, 0xff, 0xff, //0x0000386c .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003870 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003874 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003878 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x0000387c .long L0_0_set_129
	0xee, 0xd4, 0xff, 0xff, //0x00003880 .long L0_0_set_135
	0x90, 0xd4, 0xff, 0xff, //0x00003884 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003888 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x0000388c .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003890 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003894 .long L0_0_set_129
	0x90, 0xd4, 0xff, 0xff, //0x00003898 .long L0_0_set_129
	0x42, 0xd5, 0xff, 0xff, //0x0000389c .long L0_0_set_140
	0x90, 0xd4, 0xff, 0xff, //0x000038a0 .long L0_0_set_129
	0x7e, 0xd4, 0xff, 0xff, //0x000038a4 .long L0_0_set_128
	// // .set L0_1_set_94, LBB0_94-LJTI0_1
	// // .set L0_1_set_142, LBB0_142-LJTI0_1
	// // .set L0_1_set_89, LBB0_89-LJTI0_1
	// // .set L0_1_set_91, LBB0_91-LJTI0_1
	// // .set L0_1_set_96, LBB0_96-LJTI0_1
	//0x000038a8 LJTI0_1
	0xd2, 0xcf, 0xff, 0xff, //0x000038a8 .long L0_1_set_94
	0x59, 0xd3, 0xff, 0xff, //0x000038ac .long L0_1_set_142
	0xd2, 0xcf, 0xff, 0xff, //0x000038b0 .long L0_1_set_94
	0x86, 0xcf, 0xff, 0xff, //0x000038b4 .long L0_1_set_89
	0x59, 0xd3, 0xff, 0xff, //0x000038b8 .long L0_1_set_142
	0xa8, 0xcf, 0xff, 0xff, //0x000038bc .long L0_1_set_91
	0xa8, 0xcf, 0xff, 0xff, //0x000038c0 .long L0_1_set_91
	0xa8, 0xcf, 0xff, 0xff, //0x000038c4 .long L0_1_set_91
	0xa8, 0xcf, 0xff, 0xff, //0x000038c8 .long L0_1_set_91
	0xa8, 0xcf, 0xff, 0xff, //0x000038cc .long L0_1_set_91
	0xa8, 0xcf, 0xff, 0xff, //0x000038d0 .long L0_1_set_91
	0xa8, 0xcf, 0xff, 0xff, //0x000038d4 .long L0_1_set_91
	0xa8, 0xcf, 0xff, 0xff, //0x000038d8 .long L0_1_set_91
	0xa8, 0xcf, 0xff, 0xff, //0x000038dc .long L0_1_set_91
	0xa8, 0xcf, 0xff, 0xff, //0x000038e0 .long L0_1_set_91
	0x59, 0xd3, 0xff, 0xff, //0x000038e4 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x000038e8 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x000038ec .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x000038f0 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x000038f4 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x000038f8 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x000038fc .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x00003900 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x00003904 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x00003908 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x0000390c .long L0_1_set_142
	0xed, 0xcf, 0xff, 0xff, //0x00003910 .long L0_1_set_96
	0x59, 0xd3, 0xff, 0xff, //0x00003914 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x00003918 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x0000391c .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x00003920 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x00003924 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x00003928 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x0000392c .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x00003930 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x00003934 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x00003938 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x0000393c .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x00003940 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x00003944 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x00003948 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x0000394c .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x00003950 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x00003954 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x00003958 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x0000395c .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x00003960 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x00003964 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x00003968 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x0000396c .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x00003970 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x00003974 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x00003978 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x0000397c .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x00003980 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x00003984 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x00003988 .long L0_1_set_142
	0x59, 0xd3, 0xff, 0xff, //0x0000398c .long L0_1_set_142
	0xed, 0xcf, 0xff, 0xff, //0x00003990 .long L0_1_set_96
	//0x00003994 .p2align 2, 0x00
	//0x00003994 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00003994 .long 2
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003998 .p2align 4, 0x00
	//0x000039a0 _P10_TAB
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x3f, //0x000039a0 .quad 4607182418800017408
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0x40, //0x000039a8 .quad 4621819117588971520
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x59, 0x40, //0x000039b0 .quad 4636737291354636288
	0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x8f, 0x40, //0x000039b8 .quad 4652007308841189376
	0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 0xc3, 0x40, //0x000039c0 .quad 4666723172467343360
	0x00, 0x00, 0x00, 0x00, 0x00, 0x6a, 0xf8, 0x40, //0x000039c8 .quad 4681608360884174848
	0x00, 0x00, 0x00, 0x00, 0x80, 0x84, 0x2e, 0x41, //0x000039d0 .quad 4696837146684686336
	0x00, 0x00, 0x00, 0x00, 0xd0, 0x12, 0x63, 0x41, //0x000039d8 .quad 4711630319722168320
	0x00, 0x00, 0x00, 0x00, 0x84, 0xd7, 0x97, 0x41, //0x000039e0 .quad 4726483295884279808
	0x00, 0x00, 0x00, 0x00, 0x65, 0xcd, 0xcd, 0x41, //0x000039e8 .quad 4741671816366391296
	0x00, 0x00, 0x00, 0x20, 0x5f, 0xa0, 0x02, 0x42, //0x000039f0 .quad 4756540486875873280
	0x00, 0x00, 0x00, 0xe8, 0x76, 0x48, 0x37, 0x42, //0x000039f8 .quad 4771362005757984768
	0x00, 0x00, 0x00, 0xa2, 0x94, 0x1a, 0x6d, 0x42, //0x00003a00 .quad 4786511204640096256
	0x00, 0x00, 0x40, 0xe5, 0x9c, 0x30, 0xa2, 0x42, //0x00003a08 .quad 4801453603149578240
	0x00, 0x00, 0x90, 0x1e, 0xc4, 0xbc, 0xd6, 0x42, //0x00003a10 .quad 4816244402031689728
	0x00, 0x00, 0x34, 0x26, 0xf5, 0x6b, 0x0c, 0x43, //0x00003a18 .quad 4831355200913801216
	0x00, 0x80, 0xe0, 0x37, 0x79, 0xc3, 0x41, 0x43, //0x00003a20 .quad 4846369599423283200
	0x00, 0xa0, 0xd8, 0x85, 0x57, 0x34, 0x76, 0x43, //0x00003a28 .quad 4861130398305394688
	0x00, 0xc8, 0x4e, 0x67, 0x6d, 0xc1, 0xab, 0x43, //0x00003a30 .quad 4876203697187506176
	0x00, 0x3d, 0x91, 0x60, 0xe4, 0x58, 0xe1, 0x43, //0x00003a38 .quad 4891288408196988160
	0x40, 0x8c, 0xb5, 0x78, 0x1d, 0xaf, 0x15, 0x44, //0x00003a40 .quad 4906019910204099648
	0x50, 0xef, 0xe2, 0xd6, 0xe4, 0x1a, 0x4b, 0x44, //0x00003a48 .quad 4921056587992461136
	0x92, 0xd5, 0x4d, 0x06, 0xcf, 0xf0, 0x80, 0x44, //0x00003a50 .quad 4936209963552724370
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003a58 .p2align 4, 0x00
	//0x00003a60 _POW10_M128_TAB
	0x53, 0xe4, 0x60, 0xcd, 0x69, 0xc8, 0x32, 0x17, //0x00003a60 .quad 1671618768450675795
	0x88, 0x02, 0x1c, 0x08, 0xa0, 0xd5, 0x8f, 0xfa, //0x00003a68 .quad -391859759250406776
	0xb4, 0x8e, 0x5c, 0x20, 0x42, 0xbd, 0x7f, 0x0e, //0x00003a70 .quad 1044761730281672372
	0x95, 0x81, 0x11, 0x05, 0x84, 0xe5, 0x99, 0x9c, //0x00003a78 .quad -7162441377172586091
	0x61, 0xb2, 0x73, 0xa8, 0x92, 0xac, 0x1f, 0x52, //0x00003a80 .quad 5917638181279478369
	0xfa, 0xe1, 0x55, 0x06, 0xe5, 0x5e, 0xc0, 0xc3, //0x00003a88 .quad -4341365703038344710
	0xf9, 0x9e, 0x90, 0x52, 0xb7, 0x97, 0xa7, 0xe6, //0x00003a90 .quad -1826324310255427847
	0x78, 0x5a, 0xeb, 0x47, 0x9e, 0x76, 0xb0, 0xf4, //0x00003a98 .quad -815021110370542984
	0x5c, 0x63, 0x9a, 0x93, 0xd2, 0xbe, 0x28, 0x90, //0x00003aa0 .quad -8058981721550724260
	0x8b, 0x18, 0xf3, 0xec, 0x22, 0x4a, 0xee, 0x98, //0x00003aa8 .quad -7426917221622671221
	0x33, 0xfc, 0x80, 0x38, 0x87, 0xee, 0x32, 0x74, //0x00003ab0 .quad 8373016921771146291
	0xae, 0xde, 0x2f, 0xa8, 0xab, 0xdc, 0x29, 0xbf, //0x00003ab8 .quad -4671960508600951122
	0x3f, 0x3b, 0xa1, 0x06, 0x29, 0xaa, 0x3f, 0x11, //0x00003ac0 .quad 1242899115359157055
	0x5a, 0xd6, 0x3b, 0x92, 0xd6, 0x53, 0xf4, 0xee, //0x00003ac8 .quad -1228264617323800998
	0x07, 0xc5, 0x24, 0xa4, 0x59, 0xca, 0xc7, 0x4a, //0x00003ad0 .quad 5388497965526861063
	0xf8, 0x65, 0x65, 0x1b, 0x66, 0xb4, 0x58, 0x95, //0x00003ad8 .quad -7685194413468457480
	0x49, 0xf6, 0x2d, 0x0d, 0xf0, 0xbc, 0x79, 0x5d, //0x00003ae0 .quad 6735622456908576329
	0x76, 0xbf, 0x3e, 0xa2, 0x7f, 0xe1, 0xae, 0xba, //0x00003ae8 .quad -4994806998408183946
	0xdc, 0x73, 0x79, 0x10, 0x2c, 0x2c, 0xd8, 0xf4, //0x00003af0 .quad -803843965719055396
	0x53, 0x6f, 0xce, 0x8a, 0xdf, 0x99, 0x5a, 0xe9, //0x00003af8 .quad -1631822729582842029
	0x69, 0xe8, 0x4b, 0x8a, 0x9b, 0x1b, 0x07, 0x79, //0x00003b00 .quad 8720969558280366185
	0x94, 0x05, 0xc1, 0xb6, 0x2b, 0xa0, 0xd8, 0x91, //0x00003b08 .quad -7937418233630358124
	0x84, 0xe2, 0xde, 0x6c, 0x82, 0xe2, 0x48, 0x97, //0x00003b10 .quad -7545532125859093884
	0xf9, 0x46, 0x71, 0xa4, 0x36, 0xc8, 0x4e, 0xb6, //0x00003b18 .quad -5310086773610559751
	0x25, 0x9b, 0x16, 0x08, 0x23, 0x1b, 0x1b, 0xfd, //0x00003b20 .quad -208543120469091547
	0xb7, 0x98, 0x8d, 0x4d, 0x44, 0x7a, 0xe2, 0xe3, //0x00003b28 .quad -2025922448585811785
	0xf7, 0x20, 0x0e, 0xe5, 0xf5, 0xf0, 0x30, 0xfe, //0x00003b30 .quad -130339450293182217
	0x72, 0x7f, 0x78, 0xb0, 0x6a, 0x8c, 0x6d, 0x8e, //0x00003b38 .quad -8183730558007214222
	0x35, 0xa9, 0x51, 0x5e, 0x33, 0x2d, 0xbd, 0xbd, //0x00003b40 .quad -4774610331293865675
	0x4f, 0x9f, 0x96, 0x5c, 0x85, 0xef, 0x08, 0xb2, //0x00003b48 .quad -5617977179081629873
	0x82, 0x13, 0xe6, 0x35, 0x80, 0x78, 0x2c, 0xad, //0x00003b50 .quad -5968262914117332094
	0x23, 0x47, 0xbc, 0xb3, 0x66, 0x2b, 0x8b, 0xde, //0x00003b58 .quad -2410785455424649437
	0x31, 0xcc, 0xaf, 0x21, 0x50, 0xcb, 0x3b, 0x4c, //0x00003b60 .quad 5493207715531443249
	0x76, 0xac, 0x55, 0x30, 0x20, 0xfb, 0x16, 0x8b, //0x00003b68 .quad -8424269937281487754
	0x3d, 0xbf, 0x1b, 0x2a, 0x24, 0xbe, 0x4a, 0xdf, //0x00003b70 .quad -2356862392440471747
	0x93, 0x17, 0x6b, 0x3c, 0xe8, 0xb9, 0xdc, 0xad, //0x00003b78 .quad -5918651403174471789
	0x0d, 0xaf, 0xa2, 0x34, 0xad, 0x6d, 0x1d, 0xd7, //0x00003b80 .quad -2946077990550589683
	0x78, 0xdd, 0x85, 0x4b, 0x62, 0xe8, 0x53, 0xd9, //0x00003b88 .quad -2786628235540701832
	0x68, 0xad, 0xe5, 0x40, 0x8c, 0x64, 0x72, 0x86, //0x00003b90 .quad -8758827771735200408
	0x6b, 0xaa, 0x33, 0x6f, 0x3d, 0x71, 0xd4, 0x87, //0x00003b98 .quad -8659171674854020501
	0xc2, 0x18, 0x1f, 0x51, 0xaf, 0xfd, 0x0e, 0x68, //0x00003ba0 .quad 7498209359040551106
	0x06, 0x95, 0x00, 0xcb, 0x8c, 0x8d, 0xc9, 0xa9, //0x00003ba8 .quad -6212278575140137722
	0xf2, 0xde, 0x66, 0x25, 0x1b, 0xbd, 0x12, 0x02, //0x00003bb0 .quad 149389661945913074
	0x48, 0xba, 0xc0, 0xfd, 0xef, 0xf0, 0x3b, 0xd4, //0x00003bb8 .quad -3153662200497784248
	0x57, 0x4b, 0x60, 0xf7, 0x30, 0xb6, 0x4b, 0x01, //0x00003bc0 .quad 93368538716195671
	0x6d, 0x74, 0x98, 0xfe, 0x95, 0x76, 0xa5, 0x84, //0x00003bc8 .quad -8888567902952197011
	0x2d, 0x5e, 0x38, 0x35, 0xbd, 0xa3, 0x9e, 0x41, //0x00003bd0 .quad 4728396691822632493
	0x88, 0x91, 0x3e, 0x7e, 0x3b, 0xd4, 0xce, 0xa5, //0x00003bd8 .quad -6499023860262858360
	0xb9, 0x75, 0x86, 0x82, 0xac, 0x4c, 0x06, 0x52, //0x00003be0 .quad 5910495864778290617
	0xea, 0x35, 0xce, 0x5d, 0x4a, 0x89, 0x42, 0xcf, //0x00003be8 .quad -3512093806901185046
	0x93, 0x09, 0x94, 0xd1, 0xeb, 0xef, 0x43, 0x73, //0x00003bf0 .quad 8305745933913819539
	0xb2, 0xe1, 0xa0, 0x7a, 0xce, 0x95, 0x89, 0x81, //0x00003bf8 .quad -9112587656954322510
	0xf8, 0x0b, 0xf9, 0xc5, 0xe6, 0xeb, 0x14, 0x10, //0x00003c00 .quad 1158810380537498616
	0x1f, 0x1a, 0x49, 0x19, 0x42, 0xfb, 0xeb, 0xa1, //0x00003c08 .quad -6779048552765515233
	0xf6, 0x4e, 0x77, 0x77, 0xe0, 0x26, 0x1a, 0xd4, //0x00003c10 .quad -3163173042755514634
	0xa6, 0x60, 0x9b, 0x9f, 0x12, 0xfa, 0x66, 0xca, //0x00003c18 .quad -3862124672529506138
	0xb4, 0x22, 0x55, 0x95, 0x98, 0xb0, 0x20, 0x89, //0x00003c20 .quad -8565652321871781196
	0xd0, 0x38, 0x82, 0x47, 0x97, 0xb8, 0x00, 0xfd, //0x00003c28 .quad -215969822234494768
	0xb0, 0x35, 0x55, 0x5d, 0x5f, 0x6e, 0xb4, 0x55, //0x00003c30 .quad 6175682344898606512
	0x82, 0x63, 0xb1, 0x8c, 0x5e, 0x73, 0x20, 0x9e, //0x00003c38 .quad -7052510166537641086
	0x1d, 0x83, 0xaa, 0x34, 0xf7, 0x89, 0x21, 0xeb, //0x00003c40 .quad -1503769105731517667
	0x62, 0xbc, 0xdd, 0x2f, 0x36, 0x90, 0xa8, 0xc5, //0x00003c48 .quad -4203951689744663454
	0xe4, 0x23, 0xd5, 0x01, 0x75, 0xec, 0xe9, 0xa5, //0x00003c50 .quad -6491397400591784988
	0x7b, 0x2b, 0xd5, 0xbb, 0x43, 0xb4, 0x12, 0xf7, //0x00003c58 .quad -643253593753441413
	0x6e, 0x36, 0x25, 0x21, 0xc9, 0x33, 0xb2, 0x47, //0x00003c60 .quad 5166248661484910190
	0x2d, 0x3b, 0x65, 0x55, 0xaa, 0xb0, 0x6b, 0x9a, //0x00003c68 .quad -7319562523736982739
	0x0a, 0x84, 0x6e, 0x69, 0xbb, 0xc0, 0x9e, 0x99, //0x00003c70 .quad -7377247228426025974
	0xf8, 0x89, 0xbe, 0xea, 0xd4, 0x9c, 0x06, 0xc1, //0x00003c78 .quad -4537767136243840520
	0x0d, 0x25, 0xca, 0x43, 0xea, 0x70, 0x06, 0xc0, //0x00003c80 .quad -4609873017105144563
	0x76, 0x2c, 0x6e, 0x25, 0x0a, 0x44, 0x48, 0xf1, //0x00003c88 .quad -1060522901877412746
	0x28, 0x57, 0x5e, 0x6a, 0x92, 0x06, 0x04, 0x38, //0x00003c90 .quad 4036358391950366504
	0xca, 0xdb, 0x64, 0x57, 0x86, 0x2a, 0xcd, 0x96, //0x00003c98 .quad -7580355841314464822
	0xf2, 0xec, 0xf5, 0x04, 0x37, 0x08, 0x05, 0xc6, //0x00003ca0 .quad -4177924046916817678
	0xbc, 0x12, 0x3e, 0xed, 0x27, 0x75, 0x80, 0xbc, //0x00003ca8 .quad -4863758783215693124
	0x2e, 0x68, 0x33, 0xc6, 0x44, 0x4a, 0x86, 0xf7, //0x00003cb0 .quad -610719040218634194
	0x6b, 0x97, 0x8d, 0xe8, 0x71, 0x92, 0xa0, 0xeb, //0x00003cb8 .quad -1468012460592228501
	0x1d, 0x21, 0xe0, 0xfb, 0x6a, 0xee, 0xb3, 0x7a, //0x00003cc0 .quad 8841672636718129437
	0xa3, 0x7e, 0x58, 0x31, 0x87, 0x5b, 0x44, 0x93, //0x00003cc8 .quad -7835036815511224669
	0x64, 0x29, 0xd8, 0xba, 0x05, 0xea, 0x60, 0x59, //0x00003cd0 .quad 6440404777470273892
	0x4c, 0x9e, 0xae, 0xfd, 0x68, 0x72, 0x15, 0xb8, //0x00003cd8 .quad -5182110000961642932
	0xbd, 0x33, 0x8e, 0x29, 0x87, 0x24, 0xb9, 0x6f, //0x00003ce0 .quad 8050505971837842365
	0xdf, 0x45, 0x1a, 0x3d, 0x03, 0xcf, 0x1a, 0xe6, //0x00003ce8 .quad -1865951482774665761
	0x56, 0xe0, 0xf8, 0x79, 0xd4, 0xb6, 0xd3, 0xa5, //0x00003cf0 .quad -6497648813669818282
	0xab, 0x6b, 0x30, 0x06, 0x62, 0xc1, 0xd0, 0x8f, //0x00003cf8 .quad -8083748704375247957
	0x6c, 0x18, 0x77, 0x98, 0x89, 0xa4, 0x48, 0x8f, //0x00003d00 .quad -8122061017087272852
	0x96, 0x86, 0xbc, 0x87, 0xba, 0xf1, 0xc4, 0xb3, //0x00003d08 .quad -5492999862041672042
	0x87, 0xde, 0x94, 0xfe, 0xab, 0xcd, 0x1a, 0x33, //0x00003d10 .quad 3682481783923072647
	0x3c, 0xa8, 0xab, 0x29, 0x29, 0x2e, 0xb6, 0xe0, //0x00003d18 .quad -2254563809124702148
	0x14, 0x0b, 0x1d, 0x7f, 0x8b, 0xc0, 0xf0, 0x9f, //0x00003d20 .quad -6921820921902855404
	0x25, 0x49, 0x0b, 0xba, 0xd9, 0xdc, 0x71, 0x8c, //0x00003d28 .quad -8326631408344020699
	0xd9, 0x4d, 0xe4, 0x5e, 0xae, 0xf0, 0xec, 0x07, //0x00003d30 .quad 571095884476206553
	0x6f, 0x1b, 0x8e, 0x28, 0x10, 0x54, 0x8e, 0xaf, //0x00003d38 .quad -5796603242002637969
	0x50, 0x61, 0x9d, 0xf6, 0xd9, 0x2c, 0xe8, 0xc9, //0x00003d40 .quad -3897816162832129712
	0x4a, 0xa2, 0xb1, 0x32, 0x14, 0xe9, 0x71, 0xdb, //0x00003d48 .quad -2634068034075909558
	0xd2, 0x5c, 0x22, 0x3a, 0x08, 0x1c, 0x31, 0xbe, //0x00003d50 .quad -4741978110983775022
	0x6e, 0x05, 0xaf, 0x9f, 0xac, 0x31, 0x27, 0x89, //0x00003d58 .quad -8563821548938525330
	0x06, 0xf4, 0xaa, 0x48, 0x0a, 0x63, 0xbd, 0x6d, //0x00003d60 .quad 7907585416552444934
	0xca, 0xc6, 0x9a, 0xc7, 0x17, 0xfe, 0x70, 0xab, //0x00003d68 .quad -6093090917745768758
	0x08, 0xb1, 0xd5, 0xda, 0xcc, 0xbb, 0x2c, 0x09, //0x00003d70 .quad 661109733835780360
	0x7d, 0x78, 0x81, 0xb9, 0x9d, 0x3d, 0x4d, 0xd6, //0x00003d78 .quad -3004677628754823043
	0xa5, 0x8e, 0xc5, 0x08, 0x60, 0xf5, 0xbb, 0x25, //0x00003d80 .quad 2719036592861056677
	0x4e, 0xeb, 0xf0, 0x93, 0x82, 0x46, 0xf0, 0x85, //0x00003d88 .quad -8795452545612846258
	0x4e, 0xf2, 0xf6, 0x0a, 0xb8, 0xf2, 0x2a, 0xaf, //0x00003d90 .quad -5824576295778454962
	0x21, 0x26, 0xed, 0x38, 0x23, 0x58, 0x6c, 0xa7, //0x00003d98 .quad -6382629663588669919
	0xe1, 0xae, 0xb4, 0x0d, 0x66, 0xaf, 0xf5, 0x1a, //0x00003da0 .quad 1942651667131707105
	0xaa, 0x6f, 0x28, 0x07, 0x2c, 0x6e, 0x47, 0xd1, //0x00003da8 .quad -3366601061058449494
	0x4d, 0xed, 0x90, 0xc8, 0x9f, 0x8d, 0xd9, 0x50, //0x00003db0 .quad 5825843310384704845
	0xca, 0x45, 0x79, 0x84, 0xdb, 0xa4, 0xcc, 0x82, //0x00003db8 .quad -9021654690802612790
	0xa0, 0x28, 0xb5, 0xba, 0x07, 0xf1, 0x0f, 0xe5, //0x00003dc0 .quad -1941067898873894752
	0x3c, 0x97, 0x97, 0x65, 0x12, 0xce, 0x7f, 0xa3, //0x00003dc8 .quad -6665382345075878084
	0xc8, 0x72, 0x62, 0xa9, 0x49, 0xed, 0x53, 0x1e, //0x00003dd0 .quad 2185351144835019464
	0x0c, 0x7d, 0xfd, 0xfe, 0x96, 0xc1, 0x5f, 0xcc, //0x00003dd8 .quad -3720041912917459700
	0x7a, 0x0f, 0xbb, 0x13, 0x9c, 0xe8, 0xe8, 0x25, //0x00003de0 .quad 2731688931043774330
	0x4f, 0xdc, 0xbc, 0xbe, 0xfc, 0xb1, 0x77, 0xff, //0x00003de8 .quad -38366372719436721
	0xac, 0xe9, 0x54, 0x8c, 0x61, 0x91, 0xb1, 0x77, //0x00003df0 .quad 8624834609543440812
	0xb1, 0x09, 0x36, 0xf7, 0x3d, 0xcf, 0xaa, 0x9f, //0x00003df8 .quad -6941508010590729807
	0x17, 0x24, 0x6a, 0xef, 0xb9, 0xf5, 0x9d, 0xd5, //0x00003e00 .quad -3054014793352862697
	0x1d, 0x8c, 0x03, 0x75, 0x0d, 0x83, 0x95, 0xc7, //0x00003e08 .quad -4065198994811024355
	0x1d, 0xad, 0x44, 0x6b, 0x28, 0x73, 0x05, 0x4b, //0x00003e10 .quad 5405853545163697437
	0x25, 0x6f, 0x44, 0xd2, 0xd0, 0xe3, 0x7a, 0xf9, //0x00003e18 .quad -469812725086392539
	0x32, 0xec, 0x0a, 0x43, 0xf9, 0x67, 0xe3, 0x4e, //0x00003e20 .quad 5684501474941004850
	0x77, 0xc5, 0x6a, 0x83, 0x62, 0xce, 0xec, 0x9b, //0x00003e28 .quad -7211161980820077193
	0x3f, 0xa7, 0xcd, 0x93, 0xf7, 0x41, 0x9c, 0x22, //0x00003e30 .quad 2493940825248868159
	0xd5, 0x76, 0x45, 0x24, 0xfb, 0x01, 0xe8, 0xc2, //0x00003e38 .quad -4402266457597708587
	0x0f, 0x11, 0xc1, 0x78, 0x75, 0x52, 0x43, 0x6b, //0x00003e40 .quad 7729112049988473103
	0x8a, 0xd4, 0x56, 0xed, 0x79, 0x02, 0xa2, 0xf3, //0x00003e48 .quad -891147053569747830
	0xa9, 0xaa, 0x78, 0x6b, 0x89, 0x13, 0x0a, 0x83, //0x00003e50 .quad -9004363024039368023
	0xd6, 0x44, 0x56, 0x34, 0x8c, 0x41, 0x45, 0x98, //0x00003e58 .quad -7474495936122174250
	0x53, 0xd5, 0x56, 0xc6, 0x6b, 0x98, 0xcc, 0x23, //0x00003e60 .quad 2579604275232953683
	0x0c, 0xd6, 0x6b, 0x41, 0xef, 0x91, 0x56, 0xbe, //0x00003e68 .quad -4731433901725329908
	0xa8, 0x8a, 0xec, 0xb7, 0x86, 0xbe, 0xbf, 0x2c, //0x00003e70 .quad 3224505344041192104
	0x8f, 0xcb, 0xc6, 0x11, 0x6b, 0x36, 0xec, 0xed, //0x00003e78 .quad -1302606358729274481
	0xa9, 0xd6, 0xf3, 0x32, 0x14, 0xd7, 0xf7, 0x7b, //0x00003e80 .quad 8932844867666826921
	0x39, 0x3f, 0x1c, 0xeb, 0x02, 0xa2, 0xb3, 0x94, //0x00003e88 .quad -7731658001846878407
	0x53, 0xcc, 0xb0, 0x3f, 0xd9, 0xcc, 0xf5, 0xda, //0x00003e90 .quad -2669001970698630061
	0x07, 0x4f, 0xe3, 0xa5, 0x83, 0x8a, 0xe0, 0xb9, //0x00003e98 .quad -5052886483881210105
	0x68, 0xff, 0x9c, 0x8f, 0x0f, 0x40, 0xb3, 0xd1, //0x00003ea0 .quad -3336252463373287576
	0xc9, 0x22, 0x5c, 0x8f, 0x24, 0xad, 0x58, 0xe8, //0x00003ea8 .quad -1704422086424124727
	0xa1, 0x1f, 0xc2, 0xb9, 0x09, 0x08, 0x10, 0x23, //0x00003eb0 .quad 2526528228819083169
	0xbe, 0x95, 0x99, 0xd9, 0x36, 0x6c, 0x37, 0x91, //0x00003eb8 .quad -7982792831656159810
	0x8a, 0xa7, 0x32, 0x28, 0x0c, 0x0a, 0xd4, 0xab, //0x00003ec0 .quad -6065211750830921846
	0x2d, 0xfb, 0xff, 0x8f, 0x44, 0x47, 0x85, 0xb5, //0x00003ec8 .quad -5366805021142811859
	0x6c, 0x51, 0x3f, 0x32, 0x8f, 0x0c, 0xc9, 0x16, //0x00003ed0 .quad 1641857348316123500
	0xf9, 0xf9, 0xff, 0xb3, 0x15, 0x99, 0xe6, 0xe2, //0x00003ed8 .quad -2096820258001126919
	0xe3, 0x92, 0x67, 0x7f, 0xd9, 0xa7, 0x3d, 0xae, //0x00003ee0 .quad -5891368184943504669
	0x3b, 0xfc, 0x7f, 0x90, 0xad, 0x1f, 0xd0, 0x8d, //0x00003ee8 .quad -8228041688891786181
	0x9c, 0x77, 0x41, 0xdf, 0xcf, 0x11, 0xcd, 0x99, //0x00003ef0 .quad -7364210231179380836
	0x4a, 0xfb, 0x9f, 0xf4, 0x98, 0x27, 0x44, 0xb1, //0x00003ef8 .quad -5673366092687344822
	0x83, 0xd5, 0x11, 0xd7, 0x43, 0x56, 0x40, 0x40, //0x00003f00 .quad 4629795266307937667
	0x1d, 0xfa, 0xc7, 0x31, 0x7f, 0x31, 0x95, 0xdd, //0x00003f08 .quad -2480021597431793123
	0x72, 0x25, 0x6b, 0x66, 0xea, 0x35, 0x28, 0x48, //0x00003f10 .quad 5199465050656154994
	0x52, 0xfc, 0x1c, 0x7f, 0xef, 0x3e, 0x7d, 0x8a, //0x00003f18 .quad -8467542526035952558
	0xcf, 0xee, 0x05, 0x00, 0x65, 0x43, 0x32, 0xda, //0x00003f20 .quad -2724040723534582065
	0x66, 0x3b, 0xe4, 0x5e, 0xab, 0x8e, 0x1c, 0xad, //0x00003f28 .quad -5972742139117552794
	0x82, 0x6a, 0x07, 0x40, 0x3e, 0xd4, 0xbe, 0x90, //0x00003f30 .quad -8016736922845615486
	0x40, 0x4a, 0x9d, 0x36, 0x56, 0xb2, 0x63, 0xd8, //0x00003f38 .quad -2854241655469553088
	0x91, 0xa2, 0x04, 0xe8, 0xa6, 0x44, 0x77, 0x5a, //0x00003f40 .quad 6518754469289960081
	0x68, 0x4e, 0x22, 0xe2, 0x75, 0x4f, 0x3e, 0x87, //0x00003f48 .quad -8701430062309552536
	0x36, 0xcb, 0x05, 0xa2, 0xd0, 0x15, 0x15, 0x71, //0x00003f50 .quad 8148443086612450102
	0x02, 0xe2, 0xaa, 0x5a, 0x53, 0xe3, 0x0d, 0xa9, //0x00003f58 .quad -6265101559459552766
	0x03, 0x3e, 0x87, 0xca, 0x44, 0x5b, 0x5a, 0x0d, //0x00003f60 .quad 962181821410786819
	0x83, 0x9a, 0x55, 0x31, 0x28, 0x5c, 0x51, 0xd3, //0x00003f68 .quad -3219690930897053053
	0xc2, 0x86, 0x94, 0xfe, 0x0a, 0x79, 0x58, 0xe8, //0x00003f70 .quad -1704479370831952190
	0x91, 0x80, 0xd5, 0x1e, 0x99, 0xd9, 0x12, 0x84, //0x00003f78 .quad -8929835859451740015
	0x72, 0xa8, 0x39, 0xbe, 0x4d, 0x97, 0x6e, 0x62, //0x00003f80 .quad 7092772823314835570
	0xb6, 0xe0, 0x8a, 0x66, 0xff, 0x8f, 0x17, 0xa5, //0x00003f88 .quad -6550608805887287114
	0x8f, 0x12, 0xc8, 0x2d, 0x21, 0x3d, 0x0a, 0xfb, //0x00003f90 .quad -357406007711231345
	0xe3, 0x98, 0x2d, 0x40, 0xff, 0x73, 0x5d, 0xce, //0x00003f98 .quad -3576574988931720989
	0x99, 0x0b, 0x9d, 0xbc, 0x34, 0x66, 0xe6, 0x7c, //0x00003fa0 .quad 8999993282035256217
	0x8e, 0x7f, 0x1c, 0x88, 0x7f, 0x68, 0xfa, 0x80, //0x00003fa8 .quad -9152888395723407474
	0x80, 0x4e, 0xc4, 0xeb, 0xc1, 0xff, 0x1f, 0x1c, //0x00003fb0 .quad 2026619565689294464
	0x72, 0x9f, 0x23, 0x6a, 0x9f, 0x02, 0x39, 0xa1, //0x00003fb8 .quad -6829424476226871438
	0x20, 0x62, 0xb5, 0x66, 0xb2, 0xff, 0x27, 0xa3, //0x00003fc0 .quad -6690097579743157728
	0x4e, 0x87, 0xac, 0x44, 0x47, 0x43, 0x87, 0xc9, //0x00003fc8 .quad -3925094576856201394
	0xa8, 0xba, 0x62, 0x00, 0x9f, 0xff, 0xf1, 0x4b, //0x00003fd0 .quad 5472436080603216552
	0x22, 0xa9, 0xd7, 0x15, 0x19, 0x14, 0xe9, 0xfb, //0x00003fd8 .quad -294682202642863838
	0xa9, 0xb4, 0x3d, 0x60, 0xc3, 0x3f, 0x77, 0x6f, //0x00003fe0 .quad 8031958568804398249
	0xb5, 0xc9, 0xa6, 0xad, 0x8f, 0xac, 0x71, 0x9d, //0x00003fe8 .quad -7101705404292871755
	0xd3, 0x21, 0x4d, 0x38, 0xb4, 0x0f, 0x55, 0xcb, //0x00003ff0 .quad -3795109844276665901
	0x22, 0x7c, 0x10, 0x99, 0xb3, 0x17, 0xce, 0xc4, //0x00003ff8 .quad -4265445736938701790
	0x48, 0x6a, 0x60, 0x46, 0xa1, 0x53, 0x2a, 0x7e, //0x00004000 .quad 9091170749936331336
	0x2b, 0x9b, 0x54, 0x7f, 0xa0, 0x9d, 0x01, 0xf6, //0x00004008 .quad -720121152745989333
	0x6d, 0x42, 0xfc, 0xcb, 0x44, 0x74, 0xda, 0x2e, //0x00004010 .quad 3376138709496513133
	0xfb, 0xe0, 0x94, 0x4f, 0x84, 0x02, 0xc1, 0x99, //0x00004018 .quad -7367604748107325189
	0x08, 0x53, 0xfb, 0xfe, 0x55, 0x11, 0x91, 0xfa, //0x00004020 .quad -391512631556746488
	0x39, 0x19, 0x7a, 0x63, 0x25, 0x43, 0x31, 0xc0, //0x00004028 .quad -4597819916706768583
	0xca, 0x27, 0xba, 0x7e, 0xab, 0x55, 0x35, 0x79, //0x00004030 .quad 8733981247408842698
	0x88, 0x9f, 0x58, 0xbc, 0xee, 0x93, 0x3d, 0xf0, //0x00004038 .quad -1135588877456072824
	0xde, 0x58, 0x34, 0x2f, 0x8b, 0x55, 0xc1, 0x4b, //0x00004040 .quad 5458738279630526686
	0xb5, 0x63, 0xb7, 0x35, 0x75, 0x7c, 0x26, 0x96, //0x00004048 .quad -7627272076051127371
	0x16, 0x6f, 0x01, 0xfb, 0xed, 0xaa, 0xb1, 0x9e, //0x00004050 .quad -7011635205744005354
	0xa2, 0x3c, 0x25, 0x83, 0x92, 0x1b, 0xb0, 0xbb, //0x00004058 .quad -4922404076636521310
	0xdc, 0xca, 0xc1, 0x79, 0xa9, 0x15, 0x5e, 0x46, //0x00004060 .quad 5070514048102157020
	0xcb, 0x8b, 0xee, 0x23, 0x77, 0x22, 0x9c, 0xea, //0x00004068 .quad -1541319077368263733
	0xc9, 0x1e, 0x19, 0xec, 0x89, 0xcd, 0xfa, 0x0b, //0x00004070 .quad 863228270850154185
	0x5f, 0x17, 0x75, 0x76, 0x8a, 0x95, 0xa1, 0x92, //0x00004078 .quad -7880853450996246689
	0x7b, 0x66, 0x1f, 0x67, 0xec, 0x80, 0xf9, 0xce, //0x00004080 .quad -3532650679864695173
	0x36, 0x5d, 0x12, 0x14, 0xed, 0xfa, 0x49, 0xb7, //0x00004088 .quad -5239380795317920458
	0x1a, 0x40, 0xe7, 0x80, 0x27, 0xe1, 0xb7, 0x82, //0x00004090 .quad -9027499368258256870
	0x84, 0xf4, 0x16, 0x59, 0xa8, 0x79, 0x1c, 0xe5, //0x00004098 .quad -1937539975720012668
	0x10, 0x88, 0x90, 0xb0, 0xb8, 0xec, 0xb2, 0xd1, //0x000040a0 .quad -3336344095947716592
	0xd2, 0x58, 0xae, 0x37, 0x09, 0xcc, 0x31, 0x8f, //0x000040a8 .quad -8128491512466089774
	0x15, 0xaa, 0xb4, 0xdc, 0xe6, 0xa7, 0x1f, 0x86, //0x000040b0 .quad -8782116138362033643
	0x07, 0xef, 0x99, 0x85, 0x0b, 0x3f, 0xfe, 0xb2, //0x000040b8 .quad -5548928372155224313
	0x9a, 0xd4, 0xe1, 0x93, 0xe0, 0x91, 0xa7, 0x67, //0x000040c0 .quad 7469098900757009562
	0xc9, 0x6a, 0x00, 0x67, 0xce, 0xce, 0xbd, 0xdf, //0x000040c8 .quad -2324474446766642487
	0xe0, 0x24, 0x6d, 0x5c, 0x2c, 0xbb, 0xc8, 0xe0, //0x000040d0 .quad -2249342214667950880
	0xbd, 0x42, 0x60, 0x00, 0x41, 0xa1, 0xd6, 0x8b, //0x000040d8 .quad -8370325556870233411
	0x18, 0x6e, 0x88, 0x73, 0xf7, 0xe9, 0xfa, 0x58, //0x000040e0 .quad 6411694268519837208
	0x6d, 0x53, 0x78, 0x40, 0x91, 0x49, 0xcc, 0xae, //0x000040e8 .quad -5851220927660403859
	0x9e, 0x89, 0x6a, 0x50, 0x75, 0xa4, 0x39, 0xaf, //0x000040f0 .quad -5820440219632367202
	0x48, 0x68, 0x96, 0x90, 0xf5, 0x5b, 0x7f, 0xda, //0x000040f8 .quad -2702340141148116920
	0x03, 0x96, 0x42, 0x52, 0xc9, 0x06, 0x84, 0x6d, //0x00004100 .quad 7891439908798240259
	0x2d, 0x01, 0x5e, 0x7a, 0x79, 0x99, 0x8f, 0x88, //0x00004108 .quad -8606491615858654931
	0x83, 0x3b, 0xd3, 0xa6, 0x7b, 0x08, 0xe5, 0xc8, //0x00004110 .quad -3970758169284363389
	0x78, 0x81, 0xf5, 0xd8, 0xd7, 0x7f, 0xb3, 0xaa, //0x00004118 .quad -6146428501395930760
	0x64, 0x0a, 0x88, 0x90, 0x9a, 0x4a, 0x1e, 0xfb, //0x00004120 .quad -351761693178066332
	0xd6, 0xe1, 0x32, 0xcf, 0xcd, 0x5f, 0x60, 0xd5, //0x00004128 .quad -3071349608317525546
	0x7f, 0x06, 0x55, 0x9a, 0xa0, 0xee, 0xf2, 0x5c, //0x00004130 .quad 6697677969404790399
	0x26, 0xcd, 0x7f, 0xa1, 0xe0, 0x3b, 0x5c, 0x85, //0x00004138 .quad -8837122532839535322
	0x1e, 0x48, 0xea, 0xc0, 0x48, 0xaa, 0x2f, 0xf4, //0x00004140 .quad -851274575098787810
	0x6f, 0xc0, 0xdf, 0xc9, 0xd8, 0x4a, 0xb3, 0xa6, //0x00004148 .quad -6434717147622031249
	0x26, 0xda, 0x24, 0xf1, 0xda, 0x94, 0x3b, 0xf1, //0x00004150 .quad -1064093218873484762
	0x8b, 0xb0, 0x57, 0xfc, 0x8e, 0x1d, 0x60, 0xd0, //0x00004158 .quad -3431710416100151157
	0x58, 0x08, 0xb7, 0xd6, 0x08, 0x3d, 0xc5, 0x76, //0x00004160 .quad 8558313775058847832
	0x57, 0xce, 0xb6, 0x5d, 0x79, 0x12, 0x3c, 0x82, //0x00004168 .quad -9062348037703676329
	0x6e, 0xca, 0x64, 0x0c, 0x4b, 0x8c, 0x76, 0x54, //0x00004170 .quad 6086206200396171886
	0xed, 0x81, 0x24, 0xb5, 0x17, 0x17, 0xcb, 0xa2, //0x00004178 .quad -6716249028702207507
	0x09, 0xfd, 0x7d, 0xcf, 0x5d, 0x2f, 0x94, 0xa9, //0x00004180 .quad -6227300304786948855
	0x68, 0xa2, 0x6d, 0xa2, 0xdd, 0xdc, 0x7d, 0xcb, //0x00004188 .quad -3783625267450371480
	0x4c, 0x7c, 0x5d, 0x43, 0x35, 0x3b, 0xf9, 0xd3, //0x00004190 .quad -3172439362556298164
	0x02, 0x0b, 0x09, 0x0b, 0x15, 0x54, 0x5d, 0xfe, //0x00004198 .quad -117845565885576446
	0xaf, 0x6d, 0x1a, 0x4a, 0x01, 0xc5, 0x7b, 0xc4, //0x000041a0 .quad -4288617610811380305
	0xe1, 0xa6, 0xe5, 0x26, 0x8d, 0x54, 0xfa, 0x9e, //0x000041a8 .quad -6991182506319567135
	0x1b, 0x09, 0xa1, 0x9c, 0x41, 0xb6, 0x9a, 0x35, //0x000041b0 .quad 3862600023340550427
	0x9a, 0x10, 0x9f, 0x70, 0xb0, 0xe9, 0xb8, 0xc6, //0x000041b8 .quad -4127292114472071014
	0x62, 0x4b, 0xc9, 0x03, 0xd2, 0x63, 0x01, 0xc3, //0x000041c0 .quad -4395122007679087774
	0xc0, 0xd4, 0xc6, 0x8c, 0x1c, 0x24, 0x67, 0xf8, //0x000041c8 .quad -547429124662700864
	0x1d, 0xcf, 0x5d, 0x42, 0x63, 0xde, 0xe0, 0x79, //0x000041d0 .quad 8782263791269039901
	0xf8, 0x44, 0xfc, 0xd7, 0x91, 0x76, 0x40, 0x9b, //0x000041d8 .quad -7259672230555269896
	0xe4, 0x42, 0xf5, 0x12, 0xfc, 0x15, 0x59, 0x98, //0x000041e0 .quad -7468914334623251740
	0x36, 0x56, 0xfb, 0x4d, 0x36, 0x94, 0x10, 0xc2, //0x000041e8 .quad -4462904269766699466
	0x9d, 0x93, 0xb2, 0x17, 0x7b, 0x5b, 0x6f, 0x3e, //0x000041f0 .quad 4498915137003099037
	0xc4, 0x2b, 0x7a, 0xe1, 0x43, 0xb9, 0x94, 0xf2, //0x000041f8 .quad -966944318780986428
	0x42, 0x9c, 0xcf, 0xee, 0x2c, 0x99, 0x05, 0xa7, //0x00004200 .quad -6411550076227838910
	0x5a, 0x5b, 0xec, 0x6c, 0xca, 0xf3, 0x9c, 0x97, //0x00004208 .quad -7521869226879198374
	0x53, 0x83, 0x83, 0x2a, 0x78, 0xff, 0xc6, 0x50, //0x00004210 .quad 5820620459997365075
	0x31, 0x72, 0x27, 0x08, 0xbd, 0x30, 0x84, 0xbd, //0x00004218 .quad -4790650515171610063
	0x28, 0x64, 0x24, 0x35, 0x56, 0xbf, 0xf8, 0xa4, //0x00004220 .quad -6559282480285457368
	0xbd, 0x4e, 0x31, 0x4a, 0xec, 0x3c, 0xe5, 0xec, //0x00004228 .quad -1376627125537124675
	0x99, 0xbe, 0x36, 0xe1, 0x95, 0x77, 0x1b, 0x87, //0x00004230 .quad -8711237568605798759
	0x36, 0xd1, 0x5e, 0xae, 0x13, 0x46, 0x0f, 0x94, //0x00004238 .quad -7777920981101784778
	0x3f, 0x6e, 0x84, 0x59, 0x7b, 0x55, 0xe2, 0x28, //0x00004240 .quad 2946011094524915263
	0x84, 0x85, 0xf6, 0x99, 0x98, 0x17, 0x13, 0xb9, //0x00004248 .quad -5110715207949843068
	0xcf, 0x89, 0xe5, 0x2f, 0xda, 0xea, 0x1a, 0x33, //0x00004250 .quad 3682513868156144079
	0xe5, 0x26, 0x74, 0xc0, 0x7e, 0xdd, 0x57, 0xe7, //0x00004258 .quad -1776707991509915931
	0x21, 0x76, 0xef, 0x5d, 0xc8, 0xd2, 0xf0, 0x3f, //0x00004260 .quad 4607414176811284001
	0x4f, 0x98, 0x48, 0x38, 0x6f, 0xea, 0x96, 0x90, //0x00004268 .quad -8027971522334779313
	0xa9, 0x53, 0x6b, 0x75, 0x7a, 0x07, 0xed, 0x0f, //0x00004270 .quad 1147581702586717097
	0x63, 0xbe, 0x5a, 0x06, 0x0b, 0xa5, 0xbc, 0xb4, //0x00004278 .quad -5423278384491086237
	0x94, 0x28, 0xc6, 0x12, 0x59, 0x49, 0xe8, 0xd3, //0x00004280 .quad -3177208890193991532
	0xfb, 0x6d, 0xf1, 0xc7, 0x4d, 0xce, 0xeb, 0xe1, //0x00004288 .quad -2167411962186469893
	0x5c, 0xd9, 0xbb, 0xab, 0xd7, 0x2d, 0x71, 0x64, //0x00004290 .quad 7237616480483531100
	0xbd, 0xe4, 0xf6, 0x9c, 0xf0, 0x60, 0x33, 0x8d, //0x00004298 .quad -8272161504007625539
	0xb3, 0xcf, 0xaa, 0x96, 0x4d, 0x79, 0x8d, 0xbd, //0x000042a0 .quad -4788037454677749837
	0xec, 0x9d, 0x34, 0xc4, 0x2c, 0x39, 0x80, 0xb0, //0x000042a8 .quad -5728515861582144020
	0xa0, 0x83, 0x55, 0xfc, 0xa0, 0xd7, 0xf0, 0xec, //0x000042b0 .quad -1373360799919799392
	0x67, 0xc5, 0x41, 0xf5, 0x77, 0x47, 0xa0, 0xdc, //0x000042b8 .quad -2548958808550292121
	0x44, 0x72, 0xb5, 0x9d, 0xc4, 0x86, 0x16, 0xf4, //0x000042c0 .quad -858350499949874620
	0x60, 0x1b, 0x49, 0xf9, 0xaa, 0x2c, 0xe4, 0x89, //0x000042c8 .quad -8510628282985014432
	0xd5, 0xce, 0x22, 0xc5, 0x75, 0x28, 0x1c, 0x31, //0x000042d0 .quad 3538747893490044629
	0x39, 0x62, 0x9b, 0xb7, 0xd5, 0x37, 0x5d, 0xac, //0x000042d8 .quad -6026599335303880135
	0x8b, 0x82, 0x6b, 0x36, 0x93, 0x32, 0x63, 0x7d, //0x000042e0 .quad 9035120885289943691
	0xc7, 0x3a, 0x82, 0x25, 0xcb, 0x85, 0x74, 0xd7, //0x000042e8 .quad -2921563150702462265
	0x97, 0x31, 0x03, 0x02, 0x9c, 0xff, 0x5d, 0xae, //0x000042f0 .quad -5882264492762254953
	0xbc, 0x64, 0x71, 0xf7, 0x9e, 0xd3, 0xa8, 0x86, //0x000042f8 .quad -8743505996830120772
	0xfc, 0xfd, 0x83, 0x02, 0x83, 0x7f, 0xf5, 0xd9, //0x00004300 .quad -2741144597525430788
	0xeb, 0xbd, 0x4d, 0xb5, 0x86, 0x08, 0x53, 0xa8, //0x00004308 .quad -6317696477610263061
	0x7b, 0xfd, 0x24, 0xc3, 0x63, 0xdf, 0x72, 0xd0, //0x00004310 .quad -3426430746906788485
	0x66, 0x2d, 0xa1, 0x62, 0xa8, 0xca, 0x67, 0xd2, //0x00004318 .quad -3285434578585440922
	0x6d, 0x1e, 0xf7, 0x59, 0x9e, 0xcb, 0x47, 0x42, //0x00004320 .quad 4776009810824339053
	0x60, 0xbc, 0xa4, 0x3d, 0xa9, 0xde, 0x80, 0x83, //0x00004328 .quad -8970925639256982432
	0x08, 0xe6, 0x74, 0xf0, 0x85, 0xbe, 0xd9, 0x52, //0x00004330 .quad 5970012263530423816
	0x78, 0xeb, 0x0d, 0x8d, 0x53, 0x16, 0x61, 0xa4, //0x00004338 .quad -6601971030643840136
	0x8b, 0x1f, 0x92, 0x6c, 0x27, 0x2e, 0x90, 0x67, //0x00004340 .quad 7462515329413029771
	0x56, 0x66, 0x51, 0x70, 0xe8, 0x5b, 0x79, 0xcd, //0x00004348 .quad -3640777769877412266
	0xb6, 0x53, 0xdb, 0xa3, 0xd8, 0x1c, 0xba, 0x00, //0x00004350 .quad 52386062455755702
	0xf6, 0xdf, 0x32, 0x46, 0x71, 0xd9, 0x6b, 0x80, //0x00004358 .quad -9193015133814464522
	0xa4, 0x28, 0xd2, 0xcc, 0x0e, 0xa4, 0xe8, 0x80, //0x00004360 .quad -9157889458785081180
	0xf3, 0x97, 0xbf, 0x97, 0xcd, 0xcf, 0x86, 0xa0, //0x00004368 .quad -6879582898840692749
	0xcd, 0xb2, 0x06, 0x80, 0x12, 0xcd, 0x22, 0x61, //0x00004370 .quad 6999382250228200141
	0xf0, 0x7d, 0xaf, 0xfd, 0xc0, 0x83, 0xa8, 0xc8, //0x00004378 .quad -3987792605123478032
	0x81, 0x5f, 0x08, 0x20, 0x57, 0x80, 0x6b, 0x79, //0x00004380 .quad 8749227812785250177
	0x6c, 0x5d, 0x1b, 0x3d, 0xb1, 0xa4, 0xd2, 0xfa, //0x00004388 .quad -373054737976959636
	0xb0, 0x3b, 0x05, 0x74, 0x36, 0x30, 0xe3, 0xcb, //0x00004390 .quad -3755104653863994448
	0x63, 0x1a, 0x31, 0xc6, 0xee, 0xa6, 0xc3, 0x9c, //0x00004398 .quad -7150688238876681629
	0x9c, 0x8a, 0x06, 0x11, 0x44, 0xfc, 0xdb, 0xbe, //0x000043a0 .quad -4693880817329993060
	0xfc, 0x60, 0xbd, 0x77, 0xaa, 0x90, 0xf4, 0xc3, //0x000043a8 .quad -4326674280168464132
	0x44, 0x2d, 0x48, 0x15, 0x55, 0xfb, 0x92, 0xee, //0x000043b0 .quad -1255665003235103420
	0x3b, 0xb9, 0xac, 0x15, 0xd5, 0xb4, 0xf1, 0xf4, //0x000043b8 .quad -796656831783192261
	0x4a, 0x1c, 0x4d, 0x2d, 0x15, 0xdd, 0x1b, 0x75, //0x000043c0 .quad 8438581409832836170
	0xc5, 0xf3, 0x8b, 0x2d, 0x05, 0x11, 0x17, 0x99, //0x000043c8 .quad -7415439547505577019
	0x5d, 0x63, 0xa0, 0x78, 0x5a, 0xd4, 0x62, 0xd2, //0x000043d0 .quad -3286831292991118499
	0xb6, 0xf0, 0xee, 0x78, 0x46, 0xd5, 0x5c, 0xbf, //0x000043d8 .quad -4657613415954583370
	0x34, 0x7c, 0xc8, 0x16, 0x71, 0x89, 0xfb, 0x86, //0x000043e0 .quad -8720225134666286028
	0xe4, 0xac, 0x2a, 0x17, 0x98, 0x0a, 0x34, 0xef, //0x000043e8 .quad -1210330751515841308
	0xa0, 0x4d, 0x3d, 0xae, 0xe6, 0x35, 0x5d, 0xd4, //0x000043f0 .quad -3144297699952734816
	0x0e, 0xac, 0x7a, 0x0e, 0x9f, 0x86, 0x80, 0x95, //0x000043f8 .quad -7673985747338482674
	0x09, 0xa1, 0xcc, 0x59, 0x60, 0x83, 0x74, 0x89, //0x00004400 .quad -8542058143368306423
	0x12, 0x57, 0x19, 0xd2, 0x46, 0xa8, 0xe0, 0xba, //0x00004408 .quad -4980796165745715438
	0x4b, 0xc9, 0x3f, 0x70, 0x38, 0xa4, 0xd1, 0x2b, //0x00004410 .quad 3157485376071780683
	0xd7, 0xac, 0x9f, 0x86, 0x58, 0xd2, 0x98, 0xe9, //0x00004418 .quad -1614309188754756393
	0xcf, 0xdd, 0x27, 0x46, 0xa3, 0x06, 0x63, 0x7b, //0x00004420 .quad 8890957387685944783
	0x06, 0xcc, 0x23, 0x54, 0x77, 0x83, 0xff, 0x91, //0x00004428 .quad -7926472270612804602
	0x42, 0xd5, 0xb1, 0x17, 0x4c, 0xc8, 0x3b, 0x1a, //0x00004430 .quad 1890324697752655170
	0x08, 0xbf, 0x2c, 0x29, 0x55, 0x64, 0x7f, 0xb6, //0x00004438 .quad -5296404319838617848
	0x93, 0x4a, 0x9e, 0x1d, 0x5f, 0xba, 0xca, 0x20, //0x00004440 .quad 2362905872190818963
	0xca, 0xee, 0x77, 0x73, 0x6a, 0x3d, 0x1f, 0xe4, //0x00004448 .quad -2008819381370884406
	0x9c, 0xee, 0x82, 0x72, 0x7b, 0xb4, 0x7e, 0x54, //0x00004450 .quad 6088502188546649756
	0x3e, 0xf5, 0x2a, 0x88, 0x62, 0x86, 0x93, 0x8e, //0x00004458 .quad -8173041140997884610
	0x43, 0xaa, 0x23, 0x4f, 0x9a, 0x61, 0x9e, 0xe9, //0x00004460 .quad -1612744301171463613
	0x8d, 0xb2, 0x35, 0x2a, 0xfb, 0x67, 0x38, 0xb2, //0x00004468 .quad -5604615407819967859
	0xd4, 0x94, 0xec, 0xe2, 0x00, 0xfa, 0x05, 0x64, //0x00004470 .quad 7207441660390446292
	0x31, 0x1f, 0xc3, 0xf4, 0xf9, 0x81, 0xc6, 0xde, //0x00004478 .quad -2394083241347571919
	0x04, 0xdd, 0xd3, 0x8d, 0x40, 0xbc, 0x83, 0xde, //0x00004480 .quad -2412877989897052924
	0x7e, 0xf3, 0xf9, 0x38, 0x3c, 0x11, 0x3c, 0x8b, //0x00004488 .quad -8413831053483314306
	0x45, 0xd4, 0x48, 0xb1, 0x50, 0xab, 0x24, 0x96, //0x00004490 .quad -7627783505798704059
	0x5e, 0x70, 0x38, 0x47, 0x8b, 0x15, 0x0b, 0xae, //0x00004498 .quad -5905602798426754978
	0x57, 0x09, 0x9b, 0xdd, 0x24, 0xd6, 0xad, 0x3b, //0x000044a0 .quad 4300328673033783639
	0x76, 0x8c, 0x06, 0x19, 0xee, 0xda, 0x8d, 0xd9, //0x000044a8 .quad -2770317479606055818
	0xd6, 0xe5, 0x80, 0x0a, 0xd7, 0xa5, 0x4c, 0xe5, //0x000044b0 .quad -1923980597781273130
	0xc9, 0x17, 0xa4, 0xcf, 0xd4, 0xa8, 0xf8, 0x87, //0x000044b8 .quad -8648977452394866743
	0x4c, 0x1f, 0x21, 0xcd, 0x4c, 0xcf, 0x9f, 0x5e, //0x000044c0 .quad 6818396289628184396
	0xbc, 0x1d, 0x8d, 0x03, 0x0a, 0xd3, 0xf6, 0xa9, //0x000044c8 .quad -6199535797066195524
	0x1f, 0x67, 0x69, 0x00, 0x20, 0xc3, 0x47, 0x76, //0x000044d0 .quad 8522995362035230495
	0x2b, 0x65, 0x70, 0x84, 0xcc, 0x87, 0x74, 0xd4, //0x000044d8 .quad -3137733727905356501
	0x73, 0xe0, 0x41, 0x00, 0xf4, 0xd9, 0xec, 0x29, //0x000044e0 .quad 3021029092058325107
	0x3b, 0x3f, 0xc6, 0xd2, 0xdf, 0xd4, 0xc8, 0x84, //0x000044e8 .quad -8878612607581929669
	0x90, 0x58, 0x52, 0x00, 0x71, 0x10, 0x68, 0xf4, //0x000044f0 .quad -835399653354481520
	0x09, 0xcf, 0x77, 0xc7, 0x17, 0x0a, 0xfb, 0xa5, //0x000044f8 .quad -6486579741050024183
	0xb4, 0xee, 0x66, 0x40, 0x8d, 0x14, 0x82, 0x71, //0x00004500 .quad 8179122470161673908
	0xcc, 0xc2, 0x55, 0xb9, 0x9d, 0xcc, 0x79, 0xcf, //0x00004508 .quad -3496538657885142324
	0x30, 0x55, 0x40, 0x48, 0xd8, 0x4c, 0xf1, 0xc6, //0x00004510 .quad -4111420493003729616
	0xbf, 0x99, 0xd5, 0x93, 0xe2, 0x1f, 0xac, 0x81, //0x00004518 .quad -9102865688819295809
	0x7c, 0x6a, 0x50, 0x5a, 0x0e, 0xa0, 0xad, 0xb8, //0x00004520 .quad -5139275616254662020
	0x2f, 0x00, 0xcb, 0x38, 0xdb, 0x27, 0x17, 0xa2, //0x00004528 .quad -6766896092596731857
	0x1c, 0x85, 0xe4, 0xf0, 0x11, 0x08, 0xd9, 0xa6, //0x00004530 .quad -6424094520318327524
	0x3b, 0xc0, 0xfd, 0x06, 0xd2, 0xf1, 0x9c, 0xca, //0x00004538 .quad -3846934097318526917
	0x63, 0xa6, 0x1d, 0x6d, 0x16, 0x4a, 0x8f, 0x90, //0x00004540 .quad -8030118150397909405
	0x4a, 0x30, 0xbd, 0x88, 0x46, 0x2e, 0x44, 0xfd, //0x00004548 .quad -196981603220770742
	0xfe, 0x87, 0x32, 0x04, 0x4e, 0x8e, 0x59, 0x9a, //0x00004550 .quad -7324666853212387330
	0x2e, 0x3e, 0x76, 0x15, 0xec, 0x9c, 0x4a, 0x9e, //0x00004558 .quad -7040642529654063570
	0xfd, 0x29, 0x3f, 0x85, 0xe1, 0xf1, 0xef, 0x40, //0x00004560 .quad 4679224488766679549
	0xba, 0xcd, 0xd3, 0x1a, 0x27, 0x44, 0xdd, 0xc5, //0x00004568 .quad -4189117143640191558
	0x7c, 0xf4, 0x8e, 0xe6, 0x59, 0xee, 0x2b, 0xd1, //0x00004570 .quad -3374341425896426372
	0x28, 0xc1, 0x88, 0xe1, 0x30, 0x95, 0x54, 0xf7, //0x00004578 .quad -624710411122851544
	0xce, 0x58, 0x19, 0x30, 0xf8, 0x74, 0xbb, 0x82, //0x00004580 .quad -9026492418826348338
	0xb9, 0x78, 0xf5, 0x8c, 0x3e, 0xdd, 0x94, 0x9a, //0x00004588 .quad -7307973034592864071
	0x01, 0xaf, 0x1f, 0x3c, 0x36, 0x52, 0x6a, 0xe3, //0x00004590 .quad -2059743486678159615
	0xe7, 0xd6, 0x32, 0x30, 0x8e, 0x14, 0x3a, 0xc1, //0x00004598 .quad -4523280274813692185
	0xc1, 0x9a, 0x27, 0xcb, 0xc3, 0xe6, 0x44, 0xdc, //0x000045a0 .quad -2574679358347699519
	0xa1, 0x8c, 0x3f, 0xbc, 0xb1, 0x99, 0x88, 0xf1, //0x000045a8 .quad -1042414325089727327
	0xb9, 0xc0, 0xf8, 0x5e, 0x3a, 0x10, 0xab, 0x29, //0x000045b0 .quad 3002511419460075705
	0xe5, 0xb7, 0xa7, 0x15, 0x0f, 0x60, 0xf5, 0x96, //0x000045b8 .quad -7569037980822161435
	0xe7, 0xf0, 0xb6, 0xf6, 0x48, 0xd4, 0x15, 0x74, //0x000045c0 .quad 8364825292752482535
	0xde, 0xa5, 0x11, 0xdb, 0x12, 0xb8, 0xb2, 0xbc, //0x000045c8 .quad -4849611457600313890
	0x21, 0xad, 0x64, 0x34, 0x5b, 0x49, 0x1b, 0x11, //0x000045d0 .quad 1232659579085827361
	0x56, 0x0f, 0xd6, 0x91, 0x17, 0x66, 0xdf, 0xeb, //0x000045d8 .quad -1450328303573004458
	0x34, 0xec, 0xbe, 0x00, 0xd9, 0x0d, 0xb1, 0xca, //0x000045e0 .quad -3841273781498745804
	0x95, 0xc9, 0x25, 0xbb, 0xce, 0x9f, 0x6b, 0x93, //0x000045e8 .quad -7823984217374209643
	0x42, 0xa7, 0xee, 0x40, 0x4f, 0x51, 0x5d, 0x3d, //0x000045f0 .quad 4421779809981343554
	0xfb, 0x3b, 0xef, 0x69, 0xc2, 0x87, 0x46, 0xb8, //0x000045f8 .quad -5168294253290374149
	0x12, 0x51, 0x2a, 0x11, 0xa3, 0xa5, 0xb4, 0x0c, //0x00004600 .quad 915538744049291538
	0xfa, 0x0a, 0x6b, 0x04, 0xb3, 0x29, 0x58, 0xe6, //0x00004608 .quad -1848681798185579782
	0xab, 0x72, 0xba, 0xea, 0x85, 0xe7, 0xf0, 0x47, //0x00004610 .quad 5183897733458195115
	0xdc, 0xe6, 0xc2, 0xe2, 0x0f, 0x1a, 0xf7, 0x8f, //0x00004618 .quad -8072955151507069220
	0x56, 0x0f, 0x69, 0x65, 0x67, 0x21, 0xed, 0x59, //0x00004620 .quad 6479872166822743894
	0x93, 0xa0, 0x73, 0xdb, 0x93, 0xe0, 0xf4, 0xb3, //0x00004628 .quad -5479507920956448621
	0x2c, 0x53, 0xc3, 0x3e, 0xc1, 0x69, 0x68, 0x30, //0x00004630 .quad 3488154190101041964
	0xb8, 0x88, 0x50, 0xd2, 0xb8, 0x18, 0xf2, 0xe0, //0x00004638 .quad -2237698882768172872
	0xfb, 0x13, 0x3a, 0xc7, 0x18, 0x42, 0x41, 0x1e, //0x00004640 .quad 2180096368813151227
	0x73, 0x55, 0x72, 0x83, 0x73, 0x4f, 0x97, 0x8c, //0x00004648 .quad -8316090829371189901
	0xfa, 0x98, 0x08, 0xf9, 0x9e, 0x92, 0xd1, 0xe5, //0x00004650 .quad -1886565557410948870
	0xcf, 0xea, 0x4e, 0x64, 0x50, 0x23, 0xbd, 0xaf, //0x00004658 .quad -5783427518286599473
	0x39, 0xbf, 0x4a, 0xb7, 0x46, 0xf7, 0x45, 0xdf, //0x00004660 .quad -2358206946763686087
	0x83, 0xa5, 0x62, 0x7d, 0x24, 0x6c, 0xac, 0xdb, //0x00004668 .quad -2617598379430861437
	0x83, 0xb7, 0x8e, 0x32, 0x8c, 0xba, 0x8b, 0x6b, //0x00004670 .quad 7749492695127472003
	0x72, 0xa7, 0x5d, 0xce, 0x96, 0xc3, 0x4b, 0x89, //0x00004678 .quad -8553528014785370254
	0x64, 0x65, 0x32, 0x3f, 0x2f, 0xa9, 0x6e, 0x06, //0x00004680 .quad 463493832054564196
	0x4f, 0x11, 0xf5, 0x81, 0x7c, 0xb4, 0x9e, 0xab, //0x00004688 .quad -6080224000054324913
	0xbd, 0xfe, 0xfe, 0x0e, 0x7b, 0x53, 0x0a, 0xc8, //0x00004690 .quad -4032318728359182659
	0xa2, 0x55, 0x72, 0xa2, 0x9b, 0x61, 0x86, 0xd6, //0x00004698 .quad -2988593981640518238
	0x36, 0x5f, 0x5f, 0xe9, 0x2c, 0x74, 0x06, 0xbd, //0x000046a0 .quad -4826042214438183114
	0x85, 0x75, 0x87, 0x45, 0x01, 0xfd, 0x13, 0x86, //0x000046a8 .quad -8785400266166405755
	0x04, 0x37, 0xb7, 0x23, 0x38, 0x11, 0x48, 0x2c, //0x000046b0 .quad 3190819268807046916
	0xe7, 0x52, 0xe9, 0x96, 0x41, 0xfc, 0x98, 0xa7, //0x000046b8 .quad -6370064314280619289
	0xc5, 0x04, 0xa5, 0x2c, 0x86, 0x15, 0x5a, 0xf7, //0x000046c0 .quad -623161932418579259
	0xa0, 0xa7, 0xa3, 0xfc, 0x51, 0x3b, 0x7f, 0xd1, //0x000046c8 .quad -3350894374423386208
	0xfb, 0x22, 0xe7, 0xdb, 0x73, 0x4d, 0x98, 0x9a, //0x000046d0 .quad -7307005235402693893
	0xc4, 0x48, 0xe6, 0x3d, 0x13, 0x85, 0xef, 0x82, //0x000046d8 .quad -9011838011655698236
	0xba, 0xeb, 0xe0, 0xd2, 0xd0, 0x60, 0x3e, 0xc1, //0x000046e0 .quad -4522070525825979462
	0xf5, 0xda, 0x5f, 0x0d, 0x58, 0x66, 0xab, 0xa3, //0x000046e8 .quad -6653111496142234891
	0xa8, 0x26, 0x99, 0x07, 0x05, 0xf9, 0x8d, 0x31, //0x000046f0 .quad 3570783879572301480
	0xb3, 0xd1, 0xb7, 0x10, 0xee, 0x3f, 0x96, 0xcc, //0x000046f8 .quad -3704703351750405709
	0x52, 0x70, 0x7f, 0x49, 0x46, 0x77, 0xf1, 0xfd, //0x00004700 .quad -148206168962011054
	0x1f, 0xc6, 0xe5, 0x94, 0xe9, 0xcf, 0xbb, 0xff, //0x00004708 .quad -19193171260619233
	0x33, 0xa6, 0xef, 0xed, 0x8b, 0xea, 0xb6, 0xfe, //0x00004710 .quad -92628855601256909
	0xd3, 0x9b, 0x0f, 0xfd, 0xf1, 0x61, 0xd5, 0x9f, //0x00004718 .quad -6929524759678968877
	0xc0, 0x8f, 0x6b, 0xe9, 0x2e, 0xa5, 0x64, 0xfe, //0x00004720 .quad -115786069501571136
	0xc8, 0x82, 0x53, 0x7c, 0x6e, 0xba, 0xca, 0xc7, //0x00004728 .quad -4050219931171323192
	0xb0, 0x73, 0xc6, 0xa3, 0x7a, 0xce, 0xfd, 0x3d, //0x00004730 .quad 4466953431550423984
	0x7b, 0x63, 0x68, 0x1b, 0x0a, 0x69, 0xbd, 0xf9, //0x00004738 .quad -451088895536766085
	0x4e, 0x08, 0x5c, 0xa6, 0x0c, 0xa1, 0xbe, 0x06, //0x00004740 .quad 486002885505321038
	0x2d, 0x3e, 0x21, 0x51, 0xa6, 0x61, 0x16, 0x9c, //0x00004748 .quad -7199459587351560659
	0x62, 0x0a, 0xf3, 0xcf, 0x4f, 0x49, 0x6e, 0x48, //0x00004750 .quad 5219189625309039202
	0xb8, 0x8d, 0x69, 0xe5, 0x0f, 0xfa, 0x1b, 0xc3, //0x00004758 .quad -4387638465762062920
	0xfa, 0xcc, 0xef, 0xc3, 0xa3, 0xdb, 0x89, 0x5a, //0x00004760 .quad 6523987031636299002
	0x26, 0xf1, 0xc3, 0xde, 0x93, 0xf8, 0xe2, 0xf3, //0x00004768 .quad -872862063775190746
	0x1c, 0xe0, 0x75, 0x5a, 0x46, 0x29, 0x96, 0xf8, //0x00004770 .quad -534194123654701028
	0xb7, 0x76, 0x3a, 0x6b, 0x5c, 0xdb, 0x6d, 0x98, //0x00004778 .quad -7463067817500576073
	0x23, 0x58, 0x13, 0xf1, 0x97, 0xb3, 0xbb, 0xf6, //0x00004780 .quad -667742654568376285
	0x65, 0x14, 0x09, 0x86, 0x33, 0x52, 0x89, 0xbe, //0x00004788 .quad -4717148753448332187
	0x2c, 0x2e, 0x58, 0xed, 0x7d, 0xa0, 0x6a, 0x74, //0x00004790 .quad 8388693718644305452
	0x7f, 0x59, 0x8b, 0x67, 0xc0, 0xa6, 0x2b, 0xee, //0x00004798 .quad -1284749923383027329
	0xdc, 0x1c, 0x57, 0xb4, 0x4e, 0xa4, 0xc2, 0xa8, //0x000047a0 .quad -6286281471915778852
	0xef, 0x17, 0xb7, 0x40, 0x38, 0x48, 0xdb, 0x94, //0x000047a8 .quad -7720497729755473937
	0x13, 0xe4, 0x6c, 0x61, 0x62, 0x4d, 0xf3, 0x92, //0x000047b0 .quad -7857851839894723565
	0xeb, 0xdd, 0xe4, 0x50, 0x46, 0x1a, 0x12, 0xba, //0x000047b8 .quad -5038936143766954517
	0x17, 0x1d, 0xc8, 0xf9, 0xba, 0x20, 0xb0, 0x77, //0x000047c0 .quad 8624429273841147159
	0x66, 0x15, 0x1e, 0xe5, 0xd7, 0xa0, 0x96, 0xe8, //0x000047c8 .quad -1686984161281305242
	0x2e, 0x12, 0x1d, 0xdc, 0x74, 0x14, 0xce, 0x0a, //0x000047d0 .quad 778582277723329070
	0x60, 0xcd, 0x32, 0xef, 0x86, 0x24, 0x5e, 0x91, //0x000047d8 .quad -7971894128441897632
	0xba, 0x56, 0x24, 0x13, 0x92, 0x99, 0x81, 0x0d, //0x000047e0 .quad 973227847154161338
	0xb8, 0x80, 0xff, 0xaa, 0xa8, 0xad, 0xb5, 0xb5, //0x000047e8 .quad -5353181642124984136
	0x69, 0x6c, 0xed, 0x97, 0xf6, 0xff, 0xe1, 0x10, //0x000047f0 .quad 1216534808942701673
	0xe6, 0x60, 0xbf, 0xd5, 0x12, 0x19, 0x23, 0xe3, //0x000047f8 .quad -2079791034228842266
	0xc1, 0x63, 0xf4, 0x1e, 0xfa, 0x3f, 0x8d, 0xca, //0x00004800 .quad -3851351762838199359
	0x8f, 0x9c, 0x97, 0xc5, 0xab, 0xef, 0xf5, 0x8d, //0x00004808 .quad -8217398424034108273
	0xb2, 0x7c, 0xb1, 0xa6, 0xf8, 0x8f, 0x30, 0xbd, //0x00004810 .quad -4814189703547749198
	0xb3, 0x83, 0xfd, 0xb6, 0x96, 0x6b, 0x73, 0xb1, //0x00004818 .quad -5660062011615247437
	0xde, 0xdb, 0x5d, 0xd0, 0xf6, 0xb3, 0x7c, 0xac, //0x00004820 .quad -6017737129434686498
	0xa0, 0xe4, 0xbc, 0x64, 0x7c, 0x46, 0xd0, 0xdd, //0x00004828 .quad -2463391496091671392
	0x6b, 0xa9, 0x3a, 0x42, 0x7a, 0xf0, 0xcd, 0x6b, //0x00004830 .quad 7768129340171790699
	0xe4, 0x0e, 0xf6, 0xbe, 0x0d, 0x2c, 0xa2, 0x8a, //0x00004838 .quad -8457148712698376476
	0xc6, 0x53, 0xc9, 0xd2, 0x98, 0x6c, 0xc1, 0x86, //0x00004840 .quad -8736582398494813242
	0x9d, 0x92, 0xb3, 0x2e, 0x11, 0xb7, 0x4a, 0xad, //0x00004848 .quad -5959749872445582691
	0xb7, 0xa8, 0x7b, 0x07, 0xbf, 0xc7, 0x71, 0xe8, //0x00004850 .quad -1697355961263740745
	0x44, 0x77, 0x60, 0x7a, 0xd5, 0x64, 0x9d, 0xd8, //0x00004858 .quad -2838001322129590460
	0x72, 0x49, 0xad, 0x64, 0xd7, 0x1c, 0x47, 0x11, //0x00004860 .quad 1244995533423855986
	0x8b, 0x4a, 0x7c, 0x6c, 0x05, 0x5f, 0x62, 0x87, //0x00004868 .quad -8691279853972075893
	0xcf, 0x9b, 0xd8, 0x3d, 0x0d, 0xe4, 0x98, 0xd5, //0x00004870 .quad -3055441601647567921
	0x2d, 0x5d, 0x9b, 0xc7, 0xc6, 0xf6, 0x3a, 0xa9, //0x00004878 .quad -6252413799037706963
	0xc3, 0xc2, 0x4e, 0x8d, 0x10, 0x1d, 0xff, 0x4a, //0x00004880 .quad 5404070034795315907
	0x79, 0x34, 0x82, 0x79, 0x78, 0xb4, 0x89, 0xd3, //0x00004888 .quad -3203831230369745799
	0xba, 0x39, 0x51, 0x58, 0x2a, 0x72, 0xdf, 0xce, //0x00004890 .quad -3539985255894009414
	0xcb, 0x60, 0xf1, 0x4b, 0xcb, 0x10, 0x36, 0x84, //0x00004898 .quad -8919923546622172981
	0x28, 0x88, 0x65, 0xee, 0xb4, 0x4e, 0x97, 0xc2, //0x000048a0 .quad -4424981569867511768
	0xfe, 0xb8, 0xed, 0x1e, 0xfe, 0x94, 0x43, 0xa5, //0x000048a8 .quad -6538218414850328322
	0x32, 0xea, 0xfe, 0x29, 0x62, 0x22, 0x3d, 0x73, //0x000048b0 .quad 8303831092947774002
	0x3e, 0x27, 0xa9, 0xa6, 0x3d, 0x7a, 0x94, 0xce, //0x000048b8 .quad -3561087000135522498
	0x5f, 0x52, 0x3f, 0x5a, 0x7d, 0x35, 0x06, 0x08, //0x000048c0 .quad 578208414664970847
	0x87, 0xb8, 0x29, 0x88, 0x66, 0xcc, 0x1c, 0x81, //0x000048c8 .quad -9143208402725783417
	0xf7, 0x26, 0xcf, 0xb0, 0xdc, 0xc2, 0x07, 0xca, //0x000048d0 .quad -3888925500096174345
	0xa8, 0x26, 0x34, 0x2a, 0x80, 0xff, 0x63, 0xa1, //0x000048d8 .quad -6817324484979841368
	0xb5, 0xf0, 0x02, 0xdd, 0x93, 0xb3, 0x89, 0xfc, //0x000048e0 .quad -249470856692830027
	0x52, 0x30, 0xc1, 0x34, 0x60, 0xff, 0xbc, 0xc9, //0x000048e8 .quad -3909969587797413806
	0xe2, 0xac, 0x43, 0xd4, 0x78, 0x20, 0xac, 0xbb, //0x000048f0 .quad -4923524589293425438
	0x67, 0x7c, 0xf1, 0x41, 0x38, 0x3f, 0x2c, 0xfc, //0x000048f8 .quad -275775966319379353
	0x0d, 0x4c, 0xaa, 0x84, 0x4b, 0x94, 0x4b, 0xd5, //0x00004900 .quad -3077202868308390899
	0xc0, 0xed, 0x36, 0x29, 0x83, 0xa7, 0x9b, 0x9d, //0x00004908 .quad -7089889006590693952
	0x11, 0xdf, 0xd4, 0x65, 0x5e, 0x79, 0x9e, 0x0a, //0x00004910 .quad 765182433041899281
	0x31, 0xa9, 0x84, 0xf3, 0x63, 0x91, 0x02, 0xc5, //0x00004918 .quad -4250675239810979535
	0xd5, 0x16, 0x4a, 0xff, 0xb5, 0x17, 0x46, 0x4d, //0x00004920 .quad 5568164059729762005
	0x7d, 0xd3, 0x65, 0xf0, 0xbc, 0x35, 0x43, 0xf6, //0x00004928 .quad -701658031336336515
	0x45, 0x4e, 0x8e, 0xbf, 0xd1, 0xce, 0x4b, 0x50, //0x00004930 .quad 5785945546544795205
	0x2e, 0xa4, 0x3f, 0x16, 0x96, 0x01, 0xea, 0x99, //0x00004938 .quad -7356065297226292178
	0xd6, 0xe1, 0x71, 0x2f, 0x86, 0xc2, 0x5e, 0xe4, //0x00004940 .quad -1990940103673781802
	0x39, 0x8d, 0xcf, 0x9b, 0xfb, 0x81, 0x64, 0xc0, //0x00004948 .quad -4583395603105477319
	0x4c, 0x5a, 0x4e, 0xbb, 0x27, 0x73, 0x76, 0x5d, //0x00004950 .quad 6734696907262548556
	0x88, 0x70, 0xc3, 0x82, 0x7a, 0xa2, 0x7d, 0xf0, //0x00004958 .quad -1117558485454458744
	0x6f, 0xf8, 0x10, 0xd5, 0xf8, 0x07, 0x6a, 0x3a, //0x00004960 .quad 4209185567039092847
	0x55, 0x26, 0xba, 0x91, 0x8c, 0x85, 0x4e, 0x96, //0x00004968 .quad -7616003081050118571
	0x8b, 0x36, 0x55, 0x0a, 0xf7, 0x89, 0x04, 0x89, //0x00004970 .quad -8573576096483297653
	0xea, 0xaf, 0x28, 0xb6, 0xef, 0x26, 0xe2, 0xbb, //0x00004978 .quad -4908317832885260310
	0x2e, 0x84, 0xea, 0xcc, 0x74, 0xac, 0x45, 0x2b, //0x00004980 .quad 3118087934678041646
	0xe5, 0xdb, 0xb2, 0xa3, 0xab, 0xb0, 0xda, 0xea, //0x00004988 .quad -1523711272679187483
	0x9d, 0x92, 0x12, 0x00, 0xc9, 0x8b, 0x0b, 0x3b, //0x00004990 .quad 4254647968387469981
	0x6f, 0xc9, 0x4f, 0x46, 0x6b, 0xae, 0xc8, 0x92, //0x00004998 .quad -7869848573065574033
	0x44, 0x37, 0x17, 0x40, 0xbb, 0x6e, 0xce, 0x09, //0x000049a0 .quad 706623942056949572
	0xcb, 0xbb, 0xe3, 0x17, 0x06, 0xda, 0x7a, 0xb7, //0x000049a8 .quad -5225624697904579637
	0x15, 0x05, 0x1d, 0x10, 0x6a, 0x0a, 0x42, 0xcc, //0x000049b0 .quad -3728406090856200939
	0xbd, 0xaa, 0xdc, 0x9d, 0x87, 0x90, 0x59, 0xe5, //0x000049b8 .quad -1920344853953336643
	0x2d, 0x23, 0x12, 0x4a, 0x82, 0x46, 0xa9, 0x9f, //0x000049c0 .quad -6941939825212513491
	0xb6, 0xea, 0xa9, 0xc2, 0x54, 0xfa, 0x57, 0x8f, //0x000049c8 .quad -8117744561361917258
	0xf9, 0xab, 0x96, 0xdc, 0x22, 0x98, 0x93, 0x47, //0x000049d0 .quad 5157633273766521849
	0x64, 0x65, 0x54, 0xf3, 0xe9, 0xf8, 0x2d, 0xb3, //0x000049d8 .quad -5535494683275008668
	0xf7, 0x56, 0xbc, 0x93, 0x2b, 0x7e, 0x78, 0x59, //0x000049e0 .quad 6447041592208152311
	0xbd, 0x7e, 0x29, 0x70, 0x24, 0x77, 0xf9, 0xdf, //0x000049e8 .quad -2307682335666372931
	0x5a, 0xb6, 0x55, 0x3c, 0xdb, 0x4e, 0xeb, 0x57, //0x000049f0 .quad 6335244004343789146
	0x36, 0xef, 0x19, 0xc6, 0x76, 0xea, 0xfb, 0x8b, //0x000049f8 .quad -8359830487432564938
	0xf1, 0x23, 0x6b, 0x0b, 0x92, 0x22, 0xe6, 0xed, //0x00004a00 .quad -1304317031425039375
	0x03, 0x6b, 0xa0, 0x77, 0x14, 0xe5, 0xfa, 0xae, //0x00004a08 .quad -5838102090863318269
	0xed, 0xec, 0x45, 0x8e, 0x36, 0xab, 0x5f, 0xe9, //0x00004a10 .quad -1630396289281299219
	0xc4, 0x85, 0x88, 0x95, 0x59, 0x9e, 0xb9, 0xda, //0x00004a18 .quad -2685941595151759932
	0x14, 0xb4, 0xeb, 0x18, 0x02, 0xcb, 0xdb, 0x11, //0x00004a20 .quad 1286845328412881940
	0x9b, 0x53, 0x75, 0xfd, 0xf7, 0x02, 0xb4, 0x88, //0x00004a28 .quad -8596242524610931813
	0x19, 0xa1, 0x26, 0x9f, 0xc2, 0xbd, 0x52, 0xd6, //0x00004a30 .quad -3003129357911285479
	0x81, 0xa8, 0xd2, 0xfc, 0xb5, 0x03, 0xe1, 0xaa, //0x00004a38 .quad -6133617137336276863
	0x5f, 0x49, 0xf0, 0x46, 0x33, 0x6d, 0xe7, 0x4b, //0x00004a40 .quad 5469460339465668959
	0xa2, 0x52, 0x07, 0x7c, 0xa3, 0x44, 0x99, 0xd5, //0x00004a48 .quad -3055335403242958174
	0xdb, 0x2d, 0x56, 0x0c, 0x40, 0xa4, 0x70, 0x6f, //0x00004a50 .quad 8030098730593431003
	0xa5, 0x93, 0x84, 0x2d, 0xe6, 0xca, 0x7f, 0x85, //0x00004a58 .quad -8827113654667930715
	0x52, 0xb9, 0x6b, 0x0f, 0x50, 0xcd, 0x4c, 0xcb, //0x00004a60 .quad -3797434642040374958
	0x8e, 0xb8, 0xe5, 0xb8, 0x9f, 0xbd, 0xdf, 0xa6, //0x00004a68 .quad -6422206049907525490
	0xa7, 0xa7, 0x46, 0x13, 0xa4, 0x00, 0x20, 0x7e, //0x00004a70 .quad 9088264752731695015
	0xb2, 0x26, 0x1f, 0xa7, 0x07, 0xad, 0x97, 0xd0, //0x00004a78 .quad -3416071543957018958
	0xc8, 0x28, 0x0c, 0x8c, 0x66, 0x00, 0xd4, 0x8e, //0x00004a80 .quad -8154892584824854328
	0x2f, 0x78, 0x73, 0xc8, 0x24, 0xcc, 0x5e, 0x82, //0x00004a88 .quad -9052573742614218705
	0xfa, 0x32, 0x0f, 0x2f, 0x80, 0x00, 0x89, 0x72, //0x00004a90 .quad 8253128342678483706
	0x3b, 0x56, 0x90, 0xfa, 0x2d, 0x7f, 0xf6, 0xa2, //0x00004a98 .quad -6704031159840385477
	0xb9, 0xff, 0xd2, 0x3a, 0xa0, 0x40, 0x2b, 0x4f, //0x00004aa0 .quad 5704724409920716729
	0xca, 0x6b, 0x34, 0x79, 0xf9, 0x1e, 0xb4, 0xcb, //0x00004aa8 .quad -3768352931373093942
	0xa8, 0xbf, 0x87, 0x49, 0xc8, 0x10, 0xf6, 0xe2, //0x00004ab0 .quad -2092466524453879896
	0xbc, 0x86, 0x81, 0xd7, 0xb7, 0x26, 0xa1, 0xfe, //0x00004ab8 .quad -98755145788979524
	0xc9, 0xd7, 0xf4, 0x2d, 0x7d, 0xca, 0xd9, 0x0d, //0x00004ac0 .quad 998051431430019017
	0x36, 0xf4, 0xb0, 0xe6, 0x32, 0xb8, 0x24, 0x9f, //0x00004ac8 .quad -6979250993759194058
	0xbb, 0x0d, 0x72, 0x79, 0x1c, 0x3d, 0x50, 0x91, //0x00004ad0 .quad -7975807747567252037
	0x43, 0x31, 0x5d, 0xa0, 0x3f, 0xe6, 0xed, 0xc6, //0x00004ad8 .quad -4112377723771604669
	0x2a, 0x91, 0xce, 0x97, 0x63, 0x4c, 0xa4, 0x75, //0x00004ae0 .quad 8476984389250486570
	0x94, 0x7d, 0x74, 0x88, 0xcf, 0x5f, 0xa9, 0xf8, //0x00004ae8 .quad -528786136287117932
	0xba, 0x1a, 0xe1, 0x3e, 0xbe, 0xaf, 0x86, 0xc9, //0x00004af0 .quad -3925256793573221702
	0x7c, 0xce, 0x48, 0xb5, 0xe1, 0xdb, 0x69, 0x9b, //0x00004af8 .quad -7248020362820530564
	0x68, 0x61, 0x99, 0xce, 0xad, 0x5b, 0xe8, 0xfb, //0x00004b00 .quad -294884973539139224
	0x1b, 0x02, 0x9b, 0x22, 0xda, 0x52, 0x44, 0xc2, //0x00004b08 .quad -4448339435098275301
	0xc3, 0xb9, 0x3f, 0x42, 0x99, 0x72, 0xe2, 0xfa, //0x00004b10 .quad -368606216923924029
	0xa2, 0xc2, 0x41, 0xab, 0x90, 0x67, 0xd5, 0xf2, //0x00004b18 .quad -948738275445456222
	0x1a, 0xd4, 0x67, 0xc9, 0x9f, 0x87, 0xcd, 0xdc, //0x00004b20 .quad -2536221894791146470
	0xa5, 0x19, 0x09, 0x6b, 0xba, 0x60, 0xc5, 0x97, //0x00004b28 .quad -7510490449794491995
	0x20, 0xc9, 0xc1, 0xbb, 0x87, 0xe9, 0x00, 0x54, //0x00004b30 .quad 6053094668365842720
	0x0f, 0x60, 0xcb, 0x05, 0xe9, 0xb8, 0xb6, 0xbd, //0x00004b38 .quad -4776427043815727089
	0x68, 0x3b, 0xb2, 0xaa, 0xe9, 0x23, 0x01, 0x29, //0x00004b40 .quad 2954682317029915496
	0x13, 0x38, 0x3e, 0x47, 0x23, 0x67, 0x24, 0xed, //0x00004b48 .quad -1358847786342270957
	0x21, 0x65, 0xaf, 0x0a, 0x72, 0xb6, 0xa0, 0xf9, //0x00004b50 .quad -459166561069996767
	0x0b, 0xe3, 0x86, 0x0c, 0x76, 0xc0, 0x36, 0x94, //0x00004b58 .quad -7766808894105001205
	0x69, 0x3e, 0x5b, 0x8d, 0x0e, 0xe4, 0x08, 0xf8, //0x00004b60 .quad -573958201337495959
	0xce, 0x9b, 0xa8, 0x8f, 0x93, 0x70, 0x44, 0xb9, //0x00004b68 .quad -5096825099203863602
	0x04, 0x0e, 0xb2, 0x30, 0x12, 0x1d, 0x0b, 0xb6, //0x00004b70 .quad -5329133770099257852
	0xc2, 0xc2, 0x92, 0x73, 0xb8, 0x8c, 0x95, 0xe7, //0x00004b78 .quad -1759345355577441598
	0xc2, 0x48, 0x6f, 0x5e, 0x2b, 0xf2, 0xc6, 0xb1, //0x00004b80 .quad -5636551615525730110
	0xb9, 0xb9, 0x3b, 0x48, 0xf3, 0x77, 0xbd, 0x90, //0x00004b88 .quad -8017119874876982855
	0xf3, 0x1a, 0x0b, 0x36, 0xb6, 0xae, 0x38, 0x1e, //0x00004b90 .quad 2177682517447613171
	0x28, 0xa8, 0x4a, 0x1a, 0xf0, 0xd5, 0xec, 0xb4, //0x00004b98 .quad -5409713825168840664
	0xb0, 0xe1, 0x8d, 0xc3, 0x63, 0xda, 0xc6, 0x25, //0x00004ba0 .quad 2722103146809516464
	0x32, 0x52, 0xdd, 0x20, 0x6c, 0x0b, 0x28, 0xe2, //0x00004ba8 .quad -2150456263033662926
	0x0e, 0xad, 0x38, 0x5a, 0x7e, 0x48, 0x9c, 0x57, //0x00004bb0 .quad 6313000485183335694
	0x5f, 0x53, 0x8a, 0x94, 0x23, 0x07, 0x59, 0x8d, //0x00004bb8 .quad -8261564192037121185
	0x51, 0xd8, 0xc6, 0xf0, 0x9d, 0x5a, 0x83, 0x2d, //0x00004bc0 .quad 3279564588051781713
	0x37, 0xe8, 0xac, 0x79, 0xec, 0x48, 0xaf, 0xb0, //0x00004bc8 .quad -5715269221619013577
	0x65, 0x8e, 0xf8, 0x6c, 0x45, 0x31, 0xe4, 0xf8, //0x00004bd0 .quad -512230283362660763
	0x44, 0x22, 0x18, 0x98, 0x27, 0x1b, 0xdb, 0xdc, //0x00004bd8 .quad -2532400508596379068
	0xff, 0x58, 0x1b, 0x64, 0xcb, 0x9e, 0x8e, 0x1b, //0x00004be0 .quad 1985699082112030975
	0x6b, 0x15, 0x0f, 0xbf, 0xf8, 0xf0, 0x08, 0x8a, //0x00004be8 .quad -8500279345513818773
	0x3f, 0x2f, 0x22, 0x3d, 0x7e, 0x46, 0x72, 0xe2, //0x00004bf0 .quad -2129562165787349185
	0xc5, 0xda, 0xd2, 0xee, 0x36, 0x2d, 0x8b, 0xac, //0x00004bf8 .quad -6013663163464885563
	0x0f, 0xbb, 0x6a, 0xcc, 0x1d, 0xd8, 0x0e, 0x5b, //0x00004c00 .quad 6561419329620589327
	0x77, 0x91, 0x87, 0xaa, 0x84, 0xf8, 0xad, 0xd7, //0x00004c08 .quad -2905392935903719049
	0xe9, 0xb4, 0xc2, 0x9f, 0x12, 0x47, 0xe9, 0x98, //0x00004c10 .quad -7428327965055601431
	0xea, 0xba, 0x94, 0xea, 0x52, 0xbb, 0xcc, 0x86, //0x00004c18 .quad -8733399612580906262
	0x24, 0x62, 0xb3, 0x47, 0xd7, 0x98, 0x23, 0x3f, //0x00004c20 .quad 4549648098962661924
	0xa5, 0xe9, 0x39, 0xa5, 0x27, 0xea, 0x7f, 0xa8, //0x00004c28 .quad -6305063497298744923
	0xad, 0x3a, 0xa0, 0x19, 0x0d, 0x7f, 0xec, 0x8e, //0x00004c30 .quad -8147997931578836307
	0x0e, 0x64, 0x88, 0x8e, 0xb1, 0xe4, 0x9f, 0xd2, //0x00004c38 .quad -3269643353196043250
	0xac, 0x24, 0x04, 0x30, 0x68, 0xcf, 0x53, 0x19, //0x00004c40 .quad 1825030320404309164
	0x89, 0x3e, 0x15, 0xf9, 0xee, 0xee, 0xa3, 0x83, //0x00004c48 .quad -8961056123388608887
	0xd7, 0x2d, 0x05, 0x3c, 0x42, 0xc3, 0xa8, 0x5f, //0x00004c50 .quad 6892973918932774359
	0x2b, 0x8e, 0x5a, 0xb7, 0xaa, 0xea, 0x8c, 0xa4, //0x00004c58 .quad -6589634135808373205
	0x4d, 0x79, 0x06, 0xcb, 0x12, 0xf4, 0x92, 0x37, //0x00004c60 .quad 4004531380238580045
	0xb6, 0x31, 0x31, 0x65, 0x55, 0x25, 0xb0, 0xcd, //0x00004c68 .quad -3625356651333078602
	0xd0, 0x0b, 0xe4, 0xbe, 0x8b, 0xd8, 0xbb, 0xe2, //0x00004c70 .quad -2108853905778275376
	0x11, 0xbf, 0x3e, 0x5f, 0x55, 0x17, 0x8e, 0x80, //0x00004c78 .quad -9183376934724255983
	0xc4, 0x0e, 0x9d, 0xae, 0xae, 0xce, 0x6a, 0x5b, //0x00004c80 .quad 6587304654631931588
	0xd6, 0x6e, 0x0e, 0xb7, 0x2a, 0x9d, 0xb1, 0xa0, //0x00004c88 .quad -6867535149977932074
	0x75, 0x52, 0x44, 0x5a, 0x5a, 0x82, 0x45, 0xf2, //0x00004c90 .quad -989241218564861323
	0x8b, 0x0a, 0xd2, 0x64, 0x75, 0x04, 0xde, 0xc8, //0x00004c98 .quad -3972732919045027189
	0x12, 0x67, 0xd5, 0xf0, 0xf0, 0xe2, 0xd6, 0xee, //0x00004ca0 .quad -1236551523206076654
	0x2e, 0x8d, 0x06, 0xbe, 0x92, 0x85, 0x15, 0xfb, //0x00004ca8 .quad -354230130378896082
	0x6b, 0x60, 0x85, 0x96, 0xd6, 0x4d, 0x46, 0x55, //0x00004cb0 .quad 6144684325637283947
	0x3d, 0x18, 0xc4, 0xb6, 0x7b, 0x73, 0xed, 0x9c, //0x00004cb8 .quad -7138922859127891907
	0x86, 0xb8, 0x26, 0x3c, 0x4c, 0xe1, 0x97, 0xaa, //0x00004cc0 .quad -6154202648235558778
	0x4c, 0x1e, 0x75, 0xa4, 0x5a, 0xd0, 0x28, 0xc4, //0x00004cc8 .quad -4311967555482476980
	0xa8, 0x66, 0x30, 0x4b, 0x9f, 0xd9, 0x3d, 0xd5, //0x00004cd0 .quad -3081067291867060568
	0xdf, 0x65, 0x92, 0x4d, 0x71, 0x04, 0x33, 0xf5, //0x00004cd8 .quad -778273425925708321
	0x29, 0x40, 0xfe, 0x8e, 0x03, 0xa8, 0x46, 0xe5, //0x00004ce0 .quad -1925667057416912855
	0xab, 0x7f, 0x7b, 0xd0, 0xc6, 0xe2, 0x3f, 0x99, //0x00004ce8 .quad -7403949918844649557
	0x33, 0xd0, 0xbd, 0x72, 0x04, 0x52, 0x98, 0xde, //0x00004cf0 .quad -2407083821771141069
	0x96, 0x5f, 0x9a, 0x84, 0x78, 0xdb, 0x8f, 0xbf, //0x00004cf8 .quad -4643251380128424042
	0x40, 0x44, 0x6d, 0x8f, 0x85, 0x66, 0x3e, 0x96, //0x00004d00 .quad -7620540795641314240
	0x7c, 0xf7, 0xc0, 0xa5, 0x56, 0xd2, 0x73, 0xef, //0x00004d08 .quad -1192378206733142148
	0xa8, 0x4a, 0xa4, 0x79, 0x13, 0x00, 0xe7, 0xdd, //0x00004d10 .quad -2456994988062127448
	0xad, 0x9a, 0x98, 0x27, 0x76, 0x63, 0xa8, 0x95, //0x00004d18 .quad -7662765406849295699
	0x52, 0x5d, 0x0d, 0x58, 0x18, 0xc0, 0x60, 0x55, //0x00004d20 .quad 6152128301777116498
	0x59, 0xc1, 0x7e, 0xb1, 0x53, 0x7c, 0x12, 0xbb, //0x00004d28 .quad -4966770740134231719
	0xa6, 0xb4, 0x10, 0x6e, 0x1e, 0xf0, 0xb8, 0xaa, //0x00004d30 .quad -6144897678060768090
	0xaf, 0x71, 0xde, 0x9d, 0x68, 0x1b, 0xd7, 0xe9, //0x00004d38 .quad -1596777406740401745
	0xe8, 0x70, 0xca, 0x04, 0x13, 0x96, 0xb3, 0xca, //0x00004d40 .quad -3840561048787980056
	0x0d, 0x07, 0xab, 0x62, 0x21, 0x71, 0x26, 0x92, //0x00004d48 .quad -7915514906853832947
	0x22, 0x0d, 0xfd, 0xc5, 0x97, 0x7b, 0x60, 0x3d, //0x00004d50 .quad 4422670725869800738
	0xd1, 0xc8, 0x55, 0xbb, 0x69, 0x0d, 0xb0, 0xb6, //0x00004d58 .quad -5282707615139903279
	0x6a, 0x50, 0x7c, 0xb7, 0x7d, 0x9a, 0xb8, 0x8c, //0x00004d60 .quad -8306719647944912790
	0x05, 0x3b, 0x2b, 0x2a, 0xc4, 0x10, 0x5c, 0xe4, //0x00004d68 .quad -1991698500497491195
	0x42, 0xb2, 0xad, 0x92, 0x8e, 0x60, 0xf3, 0x77, //0x00004d70 .quad 8643358275316593218
	0xe3, 0x04, 0x5b, 0x9a, 0x7a, 0x8a, 0xb9, 0x8e, //0x00004d78 .quad -8162340590452013853
	0xd3, 0x1e, 0x59, 0x37, 0xb2, 0x38, 0xf0, 0x55, //0x00004d80 .quad 6192511825718353619
	0x1c, 0xc6, 0xf1, 0x40, 0x19, 0xed, 0x67, 0xb2, //0x00004d88 .quad -5591239719637629412
	0x88, 0x66, 0x2f, 0xc5, 0xde, 0x46, 0x6c, 0x6b, //0x00004d90 .quad 7740639782147942024
	0xa3, 0x37, 0x2e, 0x91, 0x5f, 0xe8, 0x01, 0xdf, //0x00004d98 .quad -2377363631119648861
	0x15, 0xa0, 0x3d, 0x3b, 0x4b, 0xac, 0x23, 0x23, //0x00004da0 .quad 2532056854628769813
	0xc6, 0xe2, 0xbc, 0xba, 0x3b, 0x31, 0x61, 0x8b, //0x00004da8 .quad -8403381297090862394
	0x1a, 0x08, 0x0d, 0x0a, 0x5e, 0x97, 0xec, 0xab, //0x00004db0 .quad -6058300968568813542
	0x77, 0x1b, 0x6c, 0xa9, 0x8a, 0x7d, 0x39, 0xae, //0x00004db8 .quad -5892540602936190089
	0x21, 0x4a, 0x90, 0x8c, 0x35, 0xbd, 0xe7, 0x96, //0x00004dc0 .quad -7572876210711016927
	0x55, 0x22, 0xc7, 0x53, 0xed, 0xdc, 0xc7, 0xd9, //0x00004dc8 .quad -2753989735242849707
	0x54, 0x2e, 0xda, 0x77, 0x41, 0xd6, 0x50, 0x7e, //0x00004dd0 .quad 9102010423587778132
	0x75, 0x75, 0x5c, 0x54, 0x14, 0xea, 0x1c, 0x88, //0x00004dd8 .quad -8638772612167862923
	0xe9, 0xb9, 0xd0, 0xd5, 0xd1, 0x0b, 0xe5, 0xdd, //0x00004de0 .quad -2457545025797441047
	0xd2, 0x92, 0x73, 0x69, 0x99, 0x24, 0x24, 0xaa, //0x00004de8 .quad -6186779746782440750
	0x64, 0xe8, 0x44, 0x4b, 0xc6, 0x4e, 0x5e, 0x95, //0x00004df0 .quad -7683617300674189212
	0x87, 0x77, 0xd0, 0xc3, 0xbf, 0x2d, 0xad, 0xd4, //0x00004df8 .quad -3121788665050663033
	0x3e, 0x11, 0x0b, 0xef, 0x3b, 0xf1, 0x5a, 0xbd, //0x00004e00 .quad -4802260812921368258
	0xb4, 0x4a, 0x62, 0xda, 0x97, 0x3c, 0xec, 0x84, //0x00004e08 .quad -8868646943297746252
	0x8e, 0xd5, 0xcd, 0xea, 0x8a, 0xad, 0xb1, 0xec, //0x00004e10 .quad -1391139997724322418
	0x61, 0xdd, 0xfa, 0xd0, 0xbd, 0x4b, 0x27, 0xa6, //0x00004e18 .quad -6474122660694794911
	0xf2, 0x4a, 0x81, 0xa5, 0xed, 0x18, 0xde, 0x67, //0x00004e20 .quad 7484447039699372786
	0xba, 0x94, 0x39, 0x45, 0xad, 0x1e, 0xb1, 0xcf, //0x00004e28 .quad -3480967307441105734
	0xd7, 0xce, 0x70, 0x87, 0x94, 0xcf, 0xea, 0x80, //0x00004e30 .quad -9157278655470055721
	0xf4, 0xfc, 0x43, 0x4b, 0x2c, 0xb3, 0xce, 0x81, //0x00004e38 .quad -9093133594791772940
	0x8d, 0x02, 0x4d, 0xa9, 0x79, 0x83, 0x25, 0xa1, //0x00004e40 .quad -6834912300910181747
	0x31, 0xfc, 0x14, 0x5e, 0xf7, 0x5f, 0x42, 0xa2, //0x00004e48 .quad -6754730975062328271
	0x30, 0x43, 0xa0, 0x13, 0x58, 0xe4, 0x6e, 0x09, //0x00004e50 .quad 679731660717048624
	0x3e, 0x3b, 0x9a, 0x35, 0xf5, 0xf7, 0xd2, 0xca, //0x00004e58 .quad -3831727700400522434
	0xfc, 0x53, 0x88, 0x18, 0x6e, 0x9d, 0xca, 0x8b, //0x00004e60 .quad -8373707460958465028
	0x0d, 0xca, 0x00, 0x83, 0xf2, 0xb5, 0x87, 0xfd, //0x00004e68 .quad -177973607073265139
	0x7d, 0x34, 0x55, 0xcf, 0x64, 0xa2, 0x5e, 0x77, //0x00004e70 .quad 8601490892183123069
	0x48, 0x7e, 0xe0, 0x91, 0xb7, 0xd1, 0x74, 0x9e, //0x00004e78 .quad -7028762532061872568
	0x9d, 0x81, 0x2a, 0x03, 0xfe, 0x4a, 0x36, 0x95, //0x00004e80 .quad -7694880458480647779
	0xda, 0x9d, 0x58, 0x76, 0x25, 0x06, 0x12, 0xc6, //0x00004e88 .quad -4174267146649952806
	0x04, 0x22, 0xf5, 0x83, 0xbd, 0xdd, 0x83, 0x3a, //0x00004e90 .quad 4216457482181353988
	0x51, 0xc5, 0xee, 0xd3, 0xae, 0x87, 0x96, 0xf7, //0x00004e98 .quad -606147914885053103
	0x42, 0x35, 0x79, 0x72, 0x96, 0x6a, 0x92, 0xc4, //0x00004ea0 .quad -4282243101277735614
	0x52, 0x3b, 0x75, 0x44, 0xcd, 0x14, 0xbe, 0x9a, //0x00004ea8 .quad -7296371474444240046
	0x93, 0x82, 0x17, 0x0f, 0x3c, 0x05, 0xb7, 0x75, //0x00004eb0 .quad 8482254178684994195
	0x27, 0x8a, 0x92, 0x95, 0x00, 0x9a, 0x6d, 0xc1, //0x00004eb8 .quad -4508778324627912153
	0x38, 0x63, 0xdd, 0x12, 0x8b, 0xc6, 0x24, 0x53, //0x00004ec0 .quad 5991131704928854840
	0xb1, 0x2c, 0xf7, 0xba, 0x80, 0x00, 0xc9, 0xf1, //0x00004ec8 .quad -1024286887357502287
	0x03, 0x5e, 0xca, 0xeb, 0x16, 0xfc, 0xf6, 0xd3, //0x00004ed0 .quad -3173071712060547581
	0xee, 0x7b, 0xda, 0x74, 0x50, 0xa0, 0x1d, 0x97, //0x00004ed8 .quad -7557708332239520786
	0x84, 0xf5, 0xbc, 0xa6, 0x1c, 0xbb, 0xf4, 0x88, //0x00004ee0 .quad -8578025658503072380
	0xea, 0x1a, 0x11, 0x92, 0x64, 0x08, 0xe5, 0xbc, //0x00004ee8 .quad -4835449396872013078
	0xe5, 0x32, 0x6c, 0xd0, 0xe3, 0xe9, 0x31, 0x2b, //0x00004ef0 .quad 3112525982153323237
	0xa5, 0x61, 0x95, 0xb6, 0x7d, 0x4a, 0x1e, 0xec, //0x00004ef8 .quad -1432625727662628443
	0xcf, 0x9f, 0x43, 0x62, 0x2e, 0x32, 0xff, 0x3a, //0x00004f00 .quad 4251171748059520975
	0x07, 0x5d, 0x1d, 0x92, 0x8e, 0xee, 0x92, 0x93, //0x00004f08 .quad -7812920107430224633
	0xc2, 0x87, 0xd4, 0xfa, 0xb9, 0xfe, 0xbe, 0x09, //0x00004f10 .quad 702278666647013314
	0x49, 0xb4, 0xa4, 0x36, 0x32, 0xaa, 0x77, 0xb8, //0x00004f18 .quad -5154464115860392887
	0xb3, 0xa9, 0x89, 0x79, 0x68, 0xbe, 0x2e, 0x4c, //0x00004f20 .quad 5489534351736154547
	0x5b, 0xe1, 0x4d, 0xc4, 0xbe, 0x94, 0x95, 0xe6, //0x00004f28 .quad -1831394126398103205
	0x10, 0x0a, 0xf6, 0x4b, 0x01, 0x37, 0x9d, 0x0f, //0x00004f30 .quad 1125115960621402640
	0xd9, 0xac, 0xb0, 0x3a, 0xf7, 0x7c, 0x1d, 0x90, //0x00004f38 .quad -8062150356639896359
	0x94, 0x8c, 0xf3, 0x9e, 0xc1, 0x84, 0x84, 0x53, //0x00004f40 .quad 6018080969204141204
	0x0f, 0xd8, 0x5c, 0x09, 0x35, 0xdc, 0x24, 0xb4, //0x00004f48 .quad -5466001927372482545
	0xb9, 0x6f, 0xb0, 0x06, 0xf2, 0xa5, 0x65, 0x28, //0x00004f50 .quad 2910915193077788601
	0x13, 0x0e, 0xb4, 0x4b, 0x42, 0x13, 0x2e, 0xe1, //0x00004f58 .quad -2220816390788215277
	0xd3, 0x45, 0x2e, 0x44, 0xb7, 0x87, 0x3f, 0xf9, //0x00004f60 .quad -486521013540076077
	0xcb, 0x88, 0x50, 0x6f, 0x09, 0xcc, 0xbc, 0x8c, //0x00004f68 .quad -8305539271883716405
	0x48, 0xd7, 0x39, 0x15, 0xa5, 0x69, 0x8f, 0xf7, //0x00004f70 .quad -608151266925095096
	0xfe, 0xaa, 0x24, 0xcb, 0x0b, 0xff, 0xeb, 0xaf, //0x00004f78 .quad -5770238071427257602
	0x1b, 0x4d, 0x88, 0x5a, 0x0e, 0x44, 0x73, 0xb5, //0x00004f80 .quad -5371875102083756773
	0xbe, 0xd5, 0xed, 0xbd, 0xce, 0xfe, 0xe6, 0xdb, //0x00004f88 .quad -2601111570856684098
	0x30, 0x30, 0x95, 0xf8, 0x88, 0x0a, 0x68, 0x31, //0x00004f90 .quad 3560107088838733872
	0x97, 0xa5, 0xb4, 0x36, 0x41, 0x5f, 0x70, 0x89, //0x00004f98 .quad -8543223759426509417
	0x3d, 0x7c, 0xba, 0x36, 0x2b, 0x0d, 0xc2, 0xfd, //0x00004fa0 .quad -161552157378970563
	0xfc, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x00004fa8 .quad -6067343680855748868
	0x4c, 0x1b, 0x69, 0x04, 0x76, 0x90, 0x32, 0x3d, //0x00004fb0 .quad 4409745821703674700
	0xbc, 0x42, 0x7a, 0xe5, 0xd5, 0x94, 0xbf, 0xd6, //0x00004fb8 .quad -2972493582642298180
	0x0f, 0xb1, 0xc1, 0xc2, 0x49, 0x9a, 0x3f, 0xa6, //0x00004fc0 .quad -6467280898289979121
	0xb5, 0x69, 0x6c, 0xaf, 0x05, 0xbd, 0x37, 0x86, //0x00004fc8 .quad -8775337516792518219
	0x53, 0x1d, 0x72, 0x33, 0xdc, 0x80, 0xcf, 0x0f, //0x00004fd0 .quad 1139270913992301907
	0x23, 0x84, 0x47, 0x1b, 0x47, 0xac, 0xc5, 0xa7, //0x00004fd8 .quad -6357485877563259869
	0xa8, 0xa4, 0x4e, 0x40, 0x13, 0x61, 0xc3, 0xd3, //0x00004fe0 .quad -3187597375937010520
	0x2b, 0x65, 0x19, 0xe2, 0x58, 0x17, 0xb7, 0xd1, //0x00004fe8 .quad -3335171328526686933
	0xe9, 0x26, 0x31, 0x08, 0xac, 0x1c, 0x5a, 0x64, //0x00004ff0 .quad 7231123676894144233
	0x3b, 0xdf, 0x4f, 0x8d, 0x97, 0x6e, 0x12, 0x83, //0x00004ff8 .quad -9002011107970261189
	0xa3, 0x70, 0x3d, 0x0a, 0xd7, 0xa3, 0x70, 0x3d, //0x00005000 .quad 4427218577690292387
	0x0a, 0xd7, 0xa3, 0x70, 0x3d, 0x0a, 0xd7, 0xa3, //0x00005008 .quad -6640827866535438582
	0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00005010 QUAD $0xcccccccccccccccc; QUAD $0xcccccccccccccccc  // .space 16, '\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005020 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00005028 .quad -9223372036854775808
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005030 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa0, //0x00005038 .quad -6917529027641081856
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005040 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc8, //0x00005048 .quad -4035225266123964416
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005050 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfa, //0x00005058 .quad -432345564227567616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005060 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x9c, //0x00005068 .quad -7187745005283311616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005070 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xc3, //0x00005078 .quad -4372995238176751616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005080 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0xf4, //0x00005088 .quad -854558029293551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005090 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x96, 0x98, //0x00005098 .quad -7451627795949551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000050a0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xbc, 0xbe, //0x000050a8 .quad -4702848726509551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000050b0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x6b, 0xee, //0x000050b8 .quad -1266874889709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000050c0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0xf9, 0x02, 0x95, //0x000050c8 .quad -7709325833709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000050d0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x40, 0xb7, 0x43, 0xba, //0x000050d8 .quad -5024971273709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000050e0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x10, 0xa5, 0xd4, 0xe8, //0x000050e8 .quad -1669528073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000050f0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x2a, 0xe7, 0x84, 0x91, //0x000050f8 .quad -7960984073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005100 .quad 0
	0x00, 0x00, 0x00, 0x80, 0xf4, 0x20, 0xe6, 0xb5, //0x00005108 .quad -5339544073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005110 .quad 0
	0x00, 0x00, 0x00, 0xa0, 0x31, 0xa9, 0x5f, 0xe3, //0x00005118 .quad -2062744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005120 .quad 0
	0x00, 0x00, 0x00, 0x04, 0xbf, 0xc9, 0x1b, 0x8e, //0x00005128 .quad -8206744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005130 .quad 0
	0x00, 0x00, 0x00, 0xc5, 0x2e, 0xbc, 0xa2, 0xb1, //0x00005138 .quad -5646744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005140 .quad 0
	0x00, 0x00, 0x40, 0x76, 0x3a, 0x6b, 0x0b, 0xde, //0x00005148 .quad -2446744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005150 .quad 0
	0x00, 0x00, 0xe8, 0x89, 0x04, 0x23, 0xc7, 0x8a, //0x00005158 .quad -8446744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005160 .quad 0
	0x00, 0x00, 0x62, 0xac, 0xc5, 0xeb, 0x78, 0xad, //0x00005168 .quad -5946744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005170 .quad 0
	0x00, 0x80, 0x7a, 0x17, 0xb7, 0x26, 0xd7, 0xd8, //0x00005178 .quad -2821744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005180 .quad 0
	0x00, 0x90, 0xac, 0x6e, 0x32, 0x78, 0x86, 0x87, //0x00005188 .quad -8681119073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005190 .quad 0
	0x00, 0xb4, 0x57, 0x0a, 0x3f, 0x16, 0x68, 0xa9, //0x00005198 .quad -6239712823709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000051a0 .quad 0
	0x00, 0xa1, 0xed, 0xcc, 0xce, 0x1b, 0xc2, 0xd3, //0x000051a8 .quad -3187955011209551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000051b0 .quad 0
	0xa0, 0x84, 0x14, 0x40, 0x61, 0x51, 0x59, 0x84, //0x000051b8 .quad -8910000909647051616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000051c0 .quad 0
	0xc8, 0xa5, 0x19, 0x90, 0xb9, 0xa5, 0x6f, 0xa5, //0x000051c8 .quad -6525815118631426616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000051d0 .quad 0
	0x3a, 0x0f, 0x20, 0xf4, 0x27, 0x8f, 0xcb, 0xce, //0x000051d8 .quad -3545582879861895366
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, //0x000051e0 .quad 4611686018427387904
	0x84, 0x09, 0x94, 0xf8, 0x78, 0x39, 0x3f, 0x81, //0x000051e8 .quad -9133518327554766460
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, //0x000051f0 .quad 5764607523034234880
	0xe5, 0x0b, 0xb9, 0x36, 0xd7, 0x07, 0x8f, 0xa1, //0x000051f8 .quad -6805211891016070171
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa4, //0x00005200 .quad -6629298651489370112
	0xde, 0x4e, 0x67, 0x04, 0xcd, 0xc9, 0xf2, 0xc9, //0x00005208 .quad -3894828845342699810
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4d, //0x00005210 .quad 5548434740920451072
	0x96, 0x22, 0x81, 0x45, 0x40, 0x7c, 0x6f, 0xfc, //0x00005218 .quad -256850038250986858
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xf0, //0x00005220 .quad -1143914305352105984
	0x9d, 0xb5, 0x70, 0x2b, 0xa8, 0xad, 0xc5, 0x9d, //0x00005228 .quad -7078060301547948643
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x6c, //0x00005230 .quad 7793479155164643328
	0x05, 0xe3, 0x4c, 0x36, 0x12, 0x19, 0x37, 0xc5, //0x00005238 .quad -4235889358507547899
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x32, 0xc7, //0x00005240 .quad -4093209111326359552
	0xc6, 0x1b, 0xe0, 0xc3, 0x56, 0xdf, 0x84, 0xf6, //0x00005248 .quad -683175679707046970
	0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x7f, 0x3c, //0x00005250 .quad 4359273333062107136
	0x5c, 0x11, 0x6c, 0x3a, 0x96, 0x0b, 0x13, 0x9a, //0x00005258 .quad -7344513827457986212
	0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x9f, 0x4b, //0x00005260 .quad 5449091666327633920
	0xb3, 0x15, 0x07, 0xc9, 0x7b, 0xce, 0x97, 0xc0, //0x00005268 .quad -4568956265895094861
	0x00, 0x00, 0x00, 0x00, 0x00, 0xd4, 0x86, 0x1e, //0x00005270 .quad 2199678564482154496
	0x20, 0xdb, 0x48, 0xbb, 0x1a, 0xc2, 0xbd, 0xf0, //0x00005278 .quad -1099509313941480672
	0x00, 0x00, 0x00, 0x00, 0x80, 0x44, 0x14, 0x13, //0x00005280 .quad 1374799102801346560
	0xf4, 0x88, 0x0d, 0xb5, 0x50, 0x99, 0x76, 0x96, //0x00005288 .quad -7604722348854507276
	0x00, 0x00, 0x00, 0x00, 0xa0, 0x55, 0xd9, 0x17, //0x00005290 .quad 1718498878501683200
	0x31, 0xeb, 0x50, 0xe2, 0xa4, 0x3f, 0x14, 0xbc, //0x00005298 .quad -4894216917640746191
	0x00, 0x00, 0x00, 0x00, 0x08, 0xab, 0xcf, 0x5d, //0x000052a0 .quad 6759809616554491904
	0xfd, 0x25, 0xe5, 0x1a, 0x8e, 0x4f, 0x19, 0xeb, //0x000052a8 .quad -1506085128623544835
	0x00, 0x00, 0x00, 0x00, 0xe5, 0xca, 0xa1, 0x5a, //0x000052b0 .quad 6530724019560251392
	0xbe, 0x37, 0xcf, 0xd0, 0xb8, 0xd1, 0xef, 0x92, //0x000052b8 .quad -7858832233030797378
	0x00, 0x00, 0x00, 0x40, 0x9e, 0x3d, 0x4a, 0xf1, //0x000052c0 .quad -1059967012404461568
	0xad, 0x05, 0x03, 0x05, 0x27, 0xc6, 0xab, 0xb7, //0x000052c8 .quad -5211854272861108819
	0x00, 0x00, 0x00, 0xd0, 0x05, 0xcd, 0x9c, 0x6d, //0x000052d0 .quad 7898413271349198848
	0x19, 0xc7, 0x43, 0xc6, 0xb0, 0xb7, 0x96, 0xe5, //0x000052d8 .quad -1903131822648998119
	0x00, 0x00, 0x00, 0xa2, 0x23, 0x00, 0x82, 0xe4, //0x000052e0 .quad -1981020733047832576
	0x6f, 0x5c, 0xea, 0x7b, 0xce, 0x32, 0x7e, 0x8f, //0x000052e8 .quad -8106986416796705681
	0x00, 0x00, 0x80, 0x8a, 0x2c, 0x80, 0xa2, 0xdd, //0x000052f0 .quad -2476275916309790720
	0x8b, 0xf3, 0xe4, 0x1a, 0x82, 0xbf, 0x5d, 0xb3, //0x000052f8 .quad -5522047002568494197
	0x00, 0x00, 0x20, 0xad, 0x37, 0x20, 0x0b, 0xd5, //0x00005300 .quad -3095344895387238400
	0x6e, 0x30, 0x9e, 0xa1, 0x62, 0x2f, 0x35, 0xe0, //0x00005308 .quad -2290872734783229842
	0x00, 0x00, 0x34, 0xcc, 0x22, 0xf4, 0x26, 0x45, //0x00005310 .quad 4982938468024057856
	0x45, 0xde, 0x02, 0xa5, 0x9d, 0x3d, 0x21, 0x8c, //0x00005318 .quad -8349324486880600507
	0x00, 0x00, 0x41, 0x7f, 0x2b, 0xb1, 0x70, 0x96, //0x00005320 .quad -7606384970252091392
	0xd6, 0x95, 0x43, 0x0e, 0x05, 0x8d, 0x29, 0xaf, //0x00005328 .quad -5824969590173362730
	0x00, 0x40, 0x11, 0x5f, 0x76, 0xdd, 0x0c, 0x3c, //0x00005330 .quad 4327076842467049472
	0x4c, 0x7b, 0xd4, 0x51, 0x46, 0xf0, 0xf3, 0xda, //0x00005338 .quad -2669525969289315508
	0x00, 0xc8, 0x6a, 0xfb, 0x69, 0x0a, 0x88, 0xa5, //0x00005340 .quad -6518949010312869888
	0x0f, 0xcd, 0x24, 0xf3, 0x2b, 0x76, 0xd8, 0x88, //0x00005348 .quad -8585982758446904049
	0x00, 0x7a, 0x45, 0x7a, 0x04, 0x0d, 0xea, 0x8e, //0x00005350 .quad -8148686262891087360
	0x53, 0x00, 0xee, 0xef, 0xb6, 0x93, 0x0e, 0xab, //0x00005358 .quad -6120792429631242157
	0x80, 0xd8, 0xd6, 0x98, 0x45, 0x90, 0xa4, 0x72, //0x00005360 .quad 8260886245095692416
	0x68, 0x80, 0xe9, 0xab, 0xa4, 0x38, 0xd2, 0xd5, //0x00005368 .quad -3039304518611664792
	0x50, 0x47, 0x86, 0x7f, 0x2b, 0xda, 0xa6, 0x47, //0x00005370 .quad 5163053903184807760
	0x41, 0xf0, 0x71, 0xeb, 0x66, 0x63, 0xa3, 0x85, //0x00005378 .quad -8817094351773372351
	0x24, 0xd9, 0x67, 0x5f, 0xb6, 0x90, 0x90, 0x99, //0x00005380 .quad -7381240676301154012
	0x51, 0x6c, 0x4e, 0xa6, 0x40, 0x3c, 0x0c, 0xa7, //0x00005388 .quad -6409681921289327535
	0x6d, 0xcf, 0x41, 0xf7, 0xe3, 0xb4, 0xf4, 0xff, //0x00005390 .quad -3178808521666707
	0x65, 0x07, 0xe2, 0xcf, 0x50, 0x4b, 0xcf, 0xd0, //0x00005398 .quad -3400416383184271515
	0xa4, 0x21, 0x89, 0x7a, 0x0e, 0xf1, 0xf8, 0xbf, //0x000053a0 .quad -4613672773753429596
	0x9f, 0x44, 0xed, 0x81, 0x12, 0x8f, 0x81, 0x82, //0x000053a8 .quad -9042789267131251553
	0x0d, 0x6a, 0x2b, 0x19, 0x52, 0x2d, 0xf7, 0xaf, //0x000053b0 .quad -5767090967191786995
	0xc7, 0x95, 0x68, 0x22, 0xd7, 0xf2, 0x21, 0xa3, //0x000053b8 .quad -6691800565486676537
	0x90, 0x44, 0x76, 0x9f, 0xa6, 0xf8, 0xf4, 0x9b, //0x000053c0 .quad -7208863708989733744
	0x39, 0xbb, 0x02, 0xeb, 0x8c, 0x6f, 0xea, 0xcb, //0x000053c8 .quad -3753064688430957767
	0xb4, 0xd5, 0x53, 0x47, 0xd0, 0x36, 0xf2, 0x02, //0x000053d0 .quad 212292400617608628
	0x08, 0x6a, 0xc3, 0x25, 0x70, 0x0b, 0xe5, 0xfe, //0x000053d8 .quad -79644842111309304
	0x90, 0x65, 0x94, 0x2c, 0x42, 0x62, 0xd7, 0x01, //0x000053e0 .quad 132682750386005392
	0x45, 0x22, 0x9a, 0x17, 0x26, 0x27, 0x4f, 0x9f, //0x000053e8 .quad -6967307053960650171
	0xf5, 0x7e, 0xb9, 0xb7, 0xd2, 0x3a, 0x4d, 0x42, //0x000053f0 .quad 4777539456409894645
	0xd6, 0xaa, 0x80, 0x9d, 0xef, 0xf0, 0x22, 0xc7, //0x000053f8 .quad -4097447799023424810
	0xb2, 0xde, 0xa7, 0x65, 0x87, 0x89, 0xe0, 0xd2, //0x00005400 .quad -3251447716342407502
	0x8b, 0xd5, 0xe0, 0x84, 0x2b, 0xad, 0xeb, 0xf8, //0x00005408 .quad -510123730351893109
	0x2f, 0xeb, 0x88, 0x9f, 0xf4, 0x55, 0xcc, 0x63, //0x00005410 .quad 7191217214140771119
	0x77, 0x85, 0x0c, 0x33, 0x3b, 0x4c, 0x93, 0x9b, //0x00005418 .quad -7236356359111015049
	0xfb, 0x25, 0x6b, 0xc7, 0x71, 0x6b, 0xbf, 0x3c, //0x00005420 .quad 4377335499248575995
	0xd5, 0xa6, 0xcf, 0xff, 0x49, 0x1f, 0x78, 0xc2, //0x00005428 .quad -4433759430461380907
	0x7a, 0xef, 0x45, 0x39, 0x4e, 0x46, 0xef, 0x8b, //0x00005430 .quad -8363388681221443718
	0x8a, 0x90, 0xc3, 0x7f, 0x1c, 0x27, 0x16, 0xf3, //0x00005438 .quad -930513269649338230
	0xac, 0xb5, 0xcb, 0xe3, 0xf0, 0x8b, 0x75, 0x97, //0x00005440 .quad -7532960934977096276
	0x56, 0x3a, 0xda, 0xcf, 0x71, 0xd8, 0xed, 0x97, //0x00005448 .quad -7499099821171918250
	0x17, 0xa3, 0xbe, 0x1c, 0xed, 0xee, 0x52, 0x3d, //0x00005450 .quad 4418856886560793367
	0xec, 0xc8, 0xd0, 0x43, 0x8e, 0x4e, 0xe9, 0xbd, //0x00005458 .quad -4762188758037509908
	0xdd, 0x4b, 0xee, 0x63, 0xa8, 0xaa, 0xa7, 0x4c, //0x00005460 .quad 5523571108200991709
	0x27, 0xfb, 0xc4, 0xd4, 0x31, 0xa2, 0x63, 0xed, //0x00005468 .quad -1341049929119499481
	0x6a, 0xef, 0x74, 0x3e, 0xa9, 0xca, 0xe8, 0x8f, //0x00005470 .quad -8076983103442849942
	0xf8, 0x1c, 0xfb, 0x24, 0x5f, 0x45, 0x5e, 0x94, //0x00005478 .quad -7755685233340769032
	0x44, 0x2b, 0x12, 0x8e, 0x53, 0xfd, 0xe2, 0xb3, //0x00005480 .quad -5484542860876174524
	0x36, 0xe4, 0x39, 0xee, 0xb6, 0xd6, 0x75, 0xb9, //0x00005488 .quad -5082920523248573386
	0x16, 0xb6, 0x96, 0x71, 0xa8, 0xbc, 0xdb, 0x60, //0x00005490 .quad 6979379479186945558
	0x44, 0x5d, 0xc8, 0xa9, 0x64, 0x4c, 0xd3, 0xe7, //0x00005498 .quad -1741964635633328828
	0xcd, 0x31, 0xfe, 0x46, 0xe9, 0x55, 0x89, 0xbc, //0x000054a0 .quad -4861259862362934835
	0x4a, 0x3a, 0x1d, 0xea, 0xbe, 0x0f, 0xe4, 0x90, //0x000054a8 .quad -8006256924911912374
	0x41, 0xbe, 0xbd, 0x98, 0x63, 0xab, 0xab, 0x6b, //0x000054b0 .quad 7758483227328495169
	0xdd, 0x88, 0xa4, 0xa4, 0xae, 0x13, 0x1d, 0xb5, //0x000054b8 .quad -5396135137712502563
	0xd1, 0x2d, 0xed, 0x7e, 0x3c, 0x96, 0x96, 0xc6, //0x000054c0 .quad -4136954021121544751
	0x14, 0xab, 0xcd, 0x4d, 0x9a, 0x58, 0x64, 0xe2, //0x000054c8 .quad -2133482903713240300
	0xa2, 0x3c, 0x54, 0xcf, 0xe5, 0x1d, 0x1e, 0xfc, //0x000054d0 .quad -279753253987271518
	0xec, 0x8a, 0xa0, 0x70, 0x60, 0xb7, 0x7e, 0x8d, //0x000054d8 .quad -8250955842461857044
	0xcb, 0x4b, 0x29, 0x43, 0x5f, 0xa5, 0x25, 0x3b, //0x000054e0 .quad 4261994450943298507
	0xa8, 0xad, 0xc8, 0x8c, 0x38, 0x65, 0xde, 0xb0, //0x000054e8 .quad -5702008784649933400
	0xbe, 0x9e, 0xf3, 0x13, 0xb7, 0x0e, 0xef, 0x49, //0x000054f0 .quad 5327493063679123134
	0x12, 0xd9, 0xfa, 0xaf, 0x86, 0xfe, 0x15, 0xdd, //0x000054f8 .quad -2515824962385028846
	0x37, 0x43, 0x78, 0x6c, 0x32, 0x69, 0x35, 0x6e, //0x00005500 .quad 7941369183226839863
	0xab, 0xc7, 0xfc, 0x2d, 0x14, 0xbf, 0x2d, 0x8a, //0x00005508 .quad -8489919629131724885
	0x04, 0x54, 0x96, 0x07, 0x7f, 0xc3, 0xc2, 0x49, //0x00005510 .quad 5315025460606161924
	0x96, 0xf9, 0x7b, 0x39, 0xd9, 0x2e, 0xb9, 0xac, //0x00005518 .quad -6000713517987268202
	0x06, 0xe9, 0x7b, 0xc9, 0x5e, 0x74, 0x33, 0xdc, //0x00005520 .quad -2579590211097073402
	0xfb, 0xf7, 0xda, 0x87, 0x8f, 0x7a, 0xe7, 0xd7, //0x00005528 .quad -2889205879056697349
	0xa3, 0x71, 0xed, 0x3d, 0xbb, 0x28, 0xa0, 0x69, //0x00005530 .quad 7611128154919104931
	0xfd, 0xda, 0xe8, 0xb4, 0x99, 0xac, 0xf0, 0x86, //0x00005538 .quad -8723282702051517699
	0x0c, 0xce, 0x68, 0x0d, 0xea, 0x32, 0x08, 0xc4, //0x00005540 .quad -4321147861633282548
	0xbc, 0x11, 0x23, 0x22, 0xc0, 0xd7, 0xac, 0xa8, //0x00005548 .quad -6292417359137009220
	0x90, 0x01, 0xc3, 0x90, 0xa4, 0x3f, 0x0a, 0xf5, //0x00005550 .quad -789748808614215280
	0x2b, 0xd6, 0xab, 0x2a, 0xb0, 0x0d, 0xd8, 0xd2, //0x00005558 .quad -3253835680493873621
	0xfa, 0xe0, 0x79, 0xda, 0xc6, 0x67, 0x26, 0x79, //0x00005560 .quad 8729779031470891258
	0xdb, 0x65, 0xab, 0x1a, 0x8e, 0x08, 0xc7, 0x83, //0x00005568 .quad -8951176327949752869
	0x38, 0x59, 0x18, 0x91, 0xb8, 0x01, 0x70, 0x57, //0x00005570 .quad 6300537770911226168
	0x52, 0x3f, 0x56, 0xa1, 0xb1, 0xca, 0xb8, 0xa4, //0x00005578 .quad -6577284391509803182
	0x86, 0x6f, 0x5e, 0xb5, 0x26, 0x02, 0x4c, 0xed, //0x00005580 .quad -1347699823215743098
	0x26, 0xcf, 0xab, 0x09, 0x5e, 0xfd, 0xe6, 0xcd, //0x00005588 .quad -3609919470959866074
	0xb4, 0x05, 0x5b, 0x31, 0x58, 0x81, 0x4f, 0x54, //0x00005590 .quad 6075216638131242420
	0x78, 0x61, 0x0b, 0xc6, 0x5a, 0x5e, 0xb0, 0x80, //0x00005598 .quad -9173728696990998152
	0x21, 0xc7, 0xb1, 0x3d, 0xae, 0x61, 0x63, 0x69, //0x000055a0 .quad 7594020797664053025
	0xd6, 0x39, 0x8e, 0x77, 0xf1, 0x75, 0xdc, 0xa0, //0x000055a8 .quad -6855474852811359786
	0xe9, 0x38, 0x1e, 0xcd, 0x19, 0x3a, 0xbc, 0x03, //0x000055b0 .quad 269153960225290473
	0x4c, 0xc8, 0x71, 0xd5, 0x6d, 0x93, 0x13, 0xc9, //0x000055b8 .quad -3957657547586811828
	0x23, 0xc7, 0x65, 0x40, 0xa0, 0x48, 0xab, 0x04, //0x000055c0 .quad 336442450281613091
	0x5f, 0x3a, 0xce, 0x4a, 0x49, 0x78, 0x58, 0xfb, //0x000055c8 .quad -335385916056126881
	0x76, 0x9c, 0x3f, 0x28, 0x64, 0x0d, 0xeb, 0x62, //0x000055d0 .quad 7127805559067090038
	0x7b, 0xe4, 0xc0, 0xce, 0x2d, 0x4b, 0x17, 0x9d, //0x000055d8 .quad -7127145225176161157
	0x94, 0x83, 0x4f, 0x32, 0xbd, 0xd0, 0xa5, 0x3b, //0x000055e0 .quad 4298070930406474644
	0x9a, 0x1d, 0x71, 0x42, 0xf9, 0x1d, 0x5d, 0xc4, //0x000055e8 .quad -4297245513042813542
	0x79, 0x64, 0xe3, 0x7e, 0xec, 0x44, 0x8f, 0xca, //0x000055f0 .quad -3850783373846682503
	0x00, 0x65, 0x0d, 0x93, 0x77, 0x65, 0x74, 0xf5, //0x000055f8 .quad -759870872876129024
	0xcb, 0x1e, 0x4e, 0xcf, 0x13, 0x8b, 0x99, 0x7e, //0x00005600 .quad 9122475437414293195
	0x20, 0x5f, 0xe8, 0xbb, 0x6a, 0xbf, 0x68, 0x99, //0x00005608 .quad -7392448323188662496
	0x7e, 0xa6, 0x21, 0xc3, 0xd8, 0xed, 0x3f, 0x9e, //0x00005610 .quad -7043649776941685122
	0xe8, 0x76, 0xe2, 0x6a, 0x45, 0xef, 0xc2, 0xbf, //0x00005618 .quad -4628874385558440216
	0x1e, 0x10, 0xea, 0xf3, 0x4e, 0xe9, 0xcf, 0xc5, //0x00005620 .quad -4192876202749718498
	0xa2, 0x14, 0x9b, 0xc5, 0x16, 0xab, 0xb3, 0xef, //0x00005628 .quad -1174406963520662366
	0x12, 0x4a, 0x72, 0x58, 0xd1, 0xf1, 0xa1, 0xbb, //0x00005630 .quad -4926390635932268014
	0xe5, 0xec, 0x80, 0x3b, 0xee, 0x4a, 0xd0, 0x95, //0x00005638 .quad -7651533379841495835
	0x97, 0xdc, 0x8e, 0xae, 0x45, 0x6e, 0x8a, 0x2a, //0x00005640 .quad 3065383741939440791
	0x1f, 0x28, 0x61, 0xca, 0xa9, 0x5d, 0x44, 0xbb, //0x00005648 .quad -4952730706374481889
	0xbd, 0x93, 0x32, 0x1a, 0xd7, 0x09, 0x2d, 0xf5, //0x00005650 .quad -779956341003086915
	0x26, 0x72, 0xf9, 0x3c, 0x14, 0x75, 0x15, 0xea, //0x00005658 .quad -1579227364540714458
	0x56, 0x9c, 0x5f, 0x70, 0x26, 0x26, 0x3c, 0x59, //0x00005660 .quad 6430056314514152534
	0x58, 0xe7, 0x1b, 0xa6, 0x2c, 0x69, 0x4d, 0x92, //0x00005668 .quad -7904546130479028392
	0x6c, 0x83, 0x77, 0x0c, 0xb0, 0x2f, 0x8b, 0x6f, //0x00005670 .quad 8037570393142690668
	0x2e, 0xe1, 0xa2, 0xcf, 0x77, 0xc3, 0xe0, 0xb6, //0x00005678 .quad -5268996644671397586
	0x47, 0x64, 0x95, 0x0f, 0x9c, 0xfb, 0x6d, 0x0b, //0x00005680 .quad 823590954573587527
	0x7a, 0x99, 0x8b, 0xc3, 0x55, 0xf4, 0x98, 0xe4, //0x00005688 .quad -1974559787411859078
	0xac, 0x5e, 0xbd, 0x89, 0x41, 0xbd, 0x24, 0x47, //0x00005690 .quad 5126430365035880108
	0xec, 0x3f, 0x37, 0x9a, 0xb5, 0x98, 0xdf, 0x8e, //0x00005698 .quad -8151628894773493780
	0x57, 0xb6, 0x2c, 0xec, 0x91, 0xec, 0xed, 0x58, //0x000056a0 .quad 6408037956294850135
	0xe7, 0x0f, 0xc5, 0x00, 0xe3, 0x7e, 0x97, 0xb2, //0x000056a8 .quad -5577850100039479321
	0xed, 0xe3, 0x37, 0x67, 0xb6, 0x67, 0x29, 0x2f, //0x000056b0 .quad 3398361426941174765
	0xe1, 0x53, 0xf6, 0xc0, 0x9b, 0x5e, 0x3d, 0xdf, //0x000056b8 .quad -2360626606621961247
	0x74, 0xee, 0x82, 0x00, 0xd2, 0xe0, 0x79, 0xbd, //0x000056c0 .quad -4793553135802847628
	0x6c, 0xf4, 0x99, 0x58, 0x21, 0x5b, 0x86, 0x8b, //0x000056c8 .quad -8392920656779807636
	0x11, 0xaa, 0xa3, 0x80, 0x06, 0x59, 0xd8, 0xec, //0x000056d0 .quad -1380255401326171631
	0x87, 0x71, 0xc0, 0xae, 0xe9, 0xf1, 0x67, 0xae, //0x000056d8 .quad -5879464802547371641
	0x95, 0x94, 0xcc, 0x20, 0x48, 0x6f, 0x0e, 0xe8, //0x000056e0 .quad -1725319251657714539
	0xe9, 0x8d, 0x70, 0x1a, 0x64, 0xee, 0x01, 0xda, //0x000056e8 .quad -2737644984756826647
	0xdd, 0xdc, 0x7f, 0x14, 0x8d, 0x05, 0x09, 0x31, //0x000056f0 .quad 3533361486141316317
	0xb2, 0x58, 0x86, 0x90, 0xfe, 0x34, 0x41, 0x88, //0x000056f8 .quad -8628557143114098510
	0x15, 0xd4, 0x9f, 0x59, 0xf0, 0x46, 0x4b, 0xbd, //0x00005700 .quad -4806670179178130411
	0xde, 0xee, 0xa7, 0x34, 0x3e, 0x82, 0x51, 0xaa, //0x00005708 .quad -6174010410465235234
	0x1a, 0xc9, 0x07, 0x70, 0xac, 0x18, 0x9e, 0x6c, //0x00005710 .quad 7826720331309500698
	0x96, 0xea, 0xd1, 0xc1, 0xcd, 0xe2, 0xe5, 0xd4, //0x00005718 .quad -3105826994654156138
	0xb0, 0xdd, 0x04, 0xc6, 0x6b, 0xcf, 0xe2, 0x03, //0x00005720 .quad 280014188641050032
	0x9e, 0x32, 0x23, 0x99, 0xc0, 0xad, 0x0f, 0x85, //0x00005728 .quad -8858670899299929442
	0x1c, 0x15, 0x86, 0xb7, 0x46, 0x83, 0xdb, 0x84, //0x00005730 .quad -8873354301053463268
	0x45, 0xff, 0x6b, 0xbf, 0x30, 0x99, 0x53, 0xa6, //0x00005738 .quad -6461652605697523899
	0x63, 0x9a, 0x67, 0x65, 0x18, 0x64, 0x12, 0xe6, //0x00005740 .quad -1868320839462053277
	0x16, 0xff, 0x46, 0xef, 0x7c, 0x7f, 0xe8, 0xcf, //0x00005748 .quad -3465379738694516970
	0x7e, 0xc0, 0x60, 0x3f, 0x8f, 0x7e, 0xcb, 0x4f, //0x00005750 .quad 5749828502977298558
	0x6e, 0x5f, 0x8c, 0x15, 0xae, 0x4f, 0xf1, 0x81, //0x00005758 .quad -9083391364325154962
	0x9d, 0xf0, 0x38, 0x0f, 0x33, 0x5e, 0xbe, 0xe3, //0x00005760 .quad -2036086408133152611
	0x49, 0x77, 0xef, 0x9a, 0x99, 0xa3, 0x6d, 0xa2, //0x00005768 .quad -6742553186979055799
	0xc5, 0x2c, 0x07, 0xd3, 0xbf, 0xf5, 0xad, 0x5c, //0x00005770 .quad 6678264026688335045
	0x1c, 0x55, 0xab, 0x01, 0x80, 0x0c, 0x09, 0xcb, //0x00005778 .quad -3816505465296431844
	0xf6, 0xf7, 0xc8, 0xc7, 0x2f, 0x73, 0xd9, 0x73, //0x00005780 .quad 8347830033360418806
	0x63, 0x2a, 0x16, 0x02, 0xa0, 0x4f, 0xcb, 0xfd, //0x00005788 .quad -158945813193151901
	0xfa, 0x9a, 0xdd, 0xdc, 0xfd, 0xe7, 0x67, 0x28, //0x00005790 .quad 2911550761636567802
	0x7e, 0xda, 0x4d, 0x01, 0xc4, 0x11, 0x9f, 0x9e, //0x00005798 .quad -7016870160886801794
	0xb8, 0x01, 0x15, 0x54, 0xfd, 0xe1, 0x81, 0xb2, //0x000057a0 .quad -5583933584809066056
	0x1d, 0x51, 0xa1, 0x01, 0x35, 0xd6, 0x46, 0xc6, //0x000057a8 .quad -4159401682681114339
	0x26, 0x42, 0x1a, 0xa9, 0x7c, 0x5a, 0x22, 0x1f, //0x000057b0 .quad 2243455055843443238
	0x65, 0xa5, 0x09, 0x42, 0xc2, 0x8b, 0xd8, 0xf7, //0x000057b8 .quad -587566084924005019
	0x58, 0x69, 0xb0, 0xe9, 0x8d, 0x78, 0x75, 0x33, //0x000057c0 .quad 3708002419115845976
	0x5f, 0x07, 0x46, 0x69, 0x59, 0x57, 0xe7, 0x9a, //0x000057c8 .quad -7284757830718584993
	0xae, 0x83, 0x1c, 0x64, 0xb1, 0xd6, 0x52, 0x00, //0x000057d0 .quad 23317005467419566
	0x37, 0x89, 0x97, 0xc3, 0x2f, 0x2d, 0xa1, 0xc1, //0x000057d8 .quad -4494261269970843337
	0x9a, 0xa4, 0x23, 0xbd, 0x5d, 0x8c, 0x67, 0xc0, //0x000057e0 .quad -4582539761593113446
	0x84, 0x6b, 0x7d, 0xb4, 0x7b, 0x78, 0x09, 0xf2, //0x000057e8 .quad -1006140569036166268
	0xe0, 0x46, 0x36, 0x96, 0xba, 0xb7, 0x40, 0xf8, //0x000057f0 .quad -558244341782001952
	0x32, 0x63, 0xce, 0x50, 0x4d, 0xeb, 0x45, 0x97, //0x000057f8 .quad -7546366883288685774
	0x98, 0xd8, 0xc3, 0x3b, 0xa9, 0xe5, 0x50, 0xb6, //0x00005800 .quad -5309491445654890344
	0xff, 0xfb, 0x01, 0xa5, 0x20, 0x66, 0x17, 0xbd, //0x00005808 .quad -4821272585683469313
	0xbe, 0xce, 0xb4, 0x8a, 0x13, 0x1f, 0xe5, 0xa3, //0x00005810 .quad -6636864307068612930
	0xff, 0x7a, 0x42, 0xce, 0xa8, 0x3f, 0x5d, 0xec, //0x00005818 .quad -1414904713676948737
	0x37, 0x01, 0xb1, 0x36, 0x6c, 0x33, 0x6f, 0xc6, //0x00005820 .quad -4148040191917883081
	0xdf, 0x8c, 0xe9, 0x80, 0xc9, 0x47, 0xba, 0x93, //0x00005828 .quad -7801844473689174817
	0x84, 0x41, 0x5d, 0x44, 0x47, 0x00, 0x0b, 0xb8, //0x00005830 .quad -5185050239897353852
	0x17, 0xf0, 0x23, 0xe1, 0xbb, 0xd9, 0xa8, 0xb8, //0x00005838 .quad -5140619573684080617
	0xe5, 0x91, 0x74, 0x15, 0x59, 0xc0, 0x0d, 0xa6, //0x00005840 .quad -6481312799871692315
	0x1d, 0xec, 0x6c, 0xd9, 0x2a, 0x10, 0xd3, 0xe6, //0x00005848 .quad -1814088448677712867
	0x2f, 0xdb, 0x68, 0xad, 0x37, 0x98, 0xc8, 0x87, //0x00005850 .quad -8662506518347195601
	0x92, 0x13, 0xe4, 0xc7, 0x1a, 0xea, 0x43, 0x90, //0x00005858 .quad -8051334308064652398
	0xfb, 0x11, 0xc3, 0x98, 0x45, 0xbe, 0xba, 0x29, //0x00005860 .quad 3006924907348169211
	0x77, 0x18, 0xdd, 0x79, 0xa1, 0xe4, 0x54, 0xb4, //0x00005868 .quad -5452481866653427593
	0x7a, 0xd6, 0xf3, 0xfe, 0xd6, 0x6d, 0x29, 0xf4, //0x00005870 .quad -853029884242176390
	0x94, 0x5e, 0x54, 0xd8, 0xc9, 0x1d, 0x6a, 0xe1, //0x00005878 .quad -2203916314889396588
	0x0c, 0x66, 0x58, 0x5f, 0xa6, 0xe4, 0x99, 0x18, //0x00005880 .quad 1772699331562333708
	0x1d, 0xbb, 0x34, 0x27, 0x9e, 0x52, 0xe2, 0x8c, //0x00005888 .quad -8294976724446954723
	0x8f, 0x7f, 0x2e, 0xf7, 0xcf, 0x5d, 0xc0, 0x5e, //0x00005890 .quad 6827560182880305039
	0xe4, 0xe9, 0x01, 0xb1, 0x45, 0xe7, 0x1a, 0xb0, //0x00005898 .quad -5757034887131305500
	0x73, 0x1f, 0xfa, 0xf4, 0x43, 0x75, 0x70, 0x76, //0x000058a0 .quad 8534450228600381299
	0x5d, 0x64, 0x42, 0x1d, 0x17, 0xa1, 0x21, 0xdc, //0x000058a8 .quad -2584607590486743971
	0xa8, 0x53, 0x1c, 0x79, 0x4a, 0x49, 0x06, 0x6a, //0x000058b0 .quad 7639874402088932264
	0xba, 0x7e, 0x49, 0x72, 0xae, 0x04, 0x95, 0x89, //0x000058b8 .quad -8532908771695296838
	0x92, 0x68, 0x63, 0x17, 0x9d, 0xdb, 0x87, 0x04, //0x000058c0 .quad 326470965756389522
	0x69, 0xde, 0xdb, 0x0e, 0xda, 0x45, 0xfa, 0xab, //0x000058c8 .quad -6054449946191733143
	0xb6, 0x42, 0x3c, 0x5d, 0x84, 0xd2, 0xa9, 0x45, //0x000058d0 .quad 5019774725622874806
	0x03, 0xd6, 0x92, 0x92, 0x50, 0xd7, 0xf8, 0xd6, //0x000058d8 .quad -2956376414312278525
	0xb2, 0xa9, 0x45, 0xba, 0x92, 0x23, 0x8a, 0x0b, //0x000058e0 .quad 831516194300602802
	0xc2, 0xc5, 0x9b, 0x5b, 0x92, 0x86, 0x5b, 0x86, //0x000058e8 .quad -8765264286586255934
	0x1e, 0x14, 0xd7, 0x68, 0x77, 0xac, 0x6c, 0x8e, //0x000058f0 .quad -8183976793979022306
	0x32, 0xb7, 0x82, 0xf2, 0x36, 0x68, 0xf2, 0xa7, //0x000058f8 .quad -6344894339805432014
	0x26, 0xd9, 0x0c, 0x43, 0x95, 0xd7, 0x07, 0x32, //0x00005900 .quad 3605087062808385830
	0xff, 0x64, 0x23, 0xaf, 0x44, 0x02, 0xef, 0xd1, //0x00005908 .quad -3319431906329402113
	0xb8, 0x07, 0xe8, 0x49, 0xbd, 0xe6, 0x44, 0x7f, //0x00005910 .quad 9170708441896323000
	0x1f, 0x1f, 0x76, 0xed, 0x6a, 0x61, 0x35, 0x83, //0x00005918 .quad -8992173969096958177
	0xa6, 0x09, 0x62, 0x9c, 0x6c, 0x20, 0x16, 0x5f, //0x00005920 .quad 6851699533943015846
	0xe7, 0xa6, 0xd3, 0xa8, 0xc5, 0xb9, 0x02, 0xa4, //0x00005928 .quad -6628531442943809817
	0x0f, 0x8c, 0x7a, 0xc3, 0x87, 0xa8, 0xdb, 0x36, //0x00005930 .quad 3952938399001381903
	0xa1, 0x90, 0x08, 0x13, 0x37, 0x68, 0x03, 0xcd, //0x00005938 .quad -3673978285252374367
	0x89, 0x97, 0x2c, 0xda, 0x54, 0x49, 0x49, 0xc2, //0x00005940 .quad -4446942528265218167
	0x64, 0x5a, 0xe5, 0x6b, 0x22, 0x21, 0x22, 0x80, //0x00005948 .quad -9213765455923815836
	0x6c, 0xbd, 0xb7, 0x10, 0xaa, 0x9b, 0xdb, 0xf2, //0x00005950 .quad -946992141904134804
	0xfd, 0xb0, 0xde, 0x06, 0x6b, 0xa9, 0x2a, 0xa0, //0x00005958 .quad -6905520801477381891
	0xc7, 0xac, 0xe5, 0x94, 0x94, 0x82, 0x92, 0x6f, //0x00005960 .quad 8039631859474607303
	0x3d, 0x5d, 0x96, 0xc8, 0xc5, 0x53, 0x35, 0xc8, //0x00005968 .quad -4020214983419339459
	0xf9, 0x17, 0x1f, 0xba, 0x39, 0x23, 0x77, 0xcb, //0x00005970 .quad -3785518230938904583
	0x8c, 0xf4, 0xbb, 0x3a, 0xb7, 0xa8, 0x42, 0xfa, //0x00005978 .quad -413582710846786420
	0xfb, 0x6e, 0x53, 0x14, 0x04, 0x76, 0x2a, 0xff, //0x00005980 .quad -60105885123121413
	0xd7, 0x78, 0xb5, 0x84, 0x72, 0xa9, 0x69, 0x9c, //0x00005988 .quad -7176018221920323369
	0xba, 0x4a, 0x68, 0x19, 0x85, 0x13, 0xf5, 0xfe, //0x00005990 .quad -75132356403901766
	0x0d, 0xd7, 0xe2, 0x25, 0xcf, 0x13, 0x84, 0xc3, //0x00005998 .quad -4358336758973016307
	0x69, 0x5d, 0xc2, 0x5f, 0x66, 0x58, 0xb2, 0x7e, //0x000059a0 .quad 9129456591349898601
	0xd1, 0x8c, 0x5b, 0xef, 0xc2, 0x18, 0x65, 0xf4, //0x000059a8 .quad -836234930288882479
	0x61, 0x7a, 0xd9, 0xfb, 0x3f, 0x77, 0x2f, 0xef, //0x000059b0 .quad -1211618658047395231
	0x02, 0x38, 0x99, 0xd5, 0x79, 0x2f, 0xbf, 0x98, //0x000059b8 .quad -7440175859071633406
	0xfa, 0xd8, 0xcf, 0xfa, 0x0f, 0x55, 0xfb, 0xaa, //0x000059c0 .quad -6126209340986631942
	0x03, 0x86, 0xff, 0x4a, 0x58, 0xfb, 0xee, 0xbe, //0x000059c8 .quad -4688533805412153853
	0x38, 0xcf, 0x83, 0xf9, 0x53, 0x2a, 0xba, 0x95, //0x000059d0 .quad -7657761676233289928
	0x84, 0x67, 0xbf, 0x5d, 0x2e, 0xba, 0xaa, 0xee, //0x000059d8 .quad -1248981238337804412
	0x83, 0x61, 0xf2, 0x7b, 0x74, 0x5a, 0x94, 0xdd, //0x000059e0 .quad -2480258038432112253
	0xb2, 0xa0, 0x97, 0xfa, 0x5c, 0xb4, 0x2a, 0x95, //0x000059e8 .quad -7698142301602209614
	0xe4, 0xf9, 0xee, 0x9a, 0x11, 0x71, 0xf9, 0x94, //0x000059f0 .quad -7712008566467528220
	0xdf, 0x88, 0x3d, 0x39, 0x74, 0x61, 0x75, 0xba, //0x000059f8 .quad -5010991858575374113
	0x5d, 0xb8, 0xaa, 0x01, 0x56, 0xcd, 0x37, 0x7a, //0x00005a00 .quad 8806733365625141341
	0x17, 0xeb, 0x8c, 0x47, 0xd1, 0xb9, 0x12, 0xe9, //0x00005a08 .quad -1652053804791829737
	0x3a, 0xb3, 0x0a, 0xc1, 0x55, 0xe0, 0x62, 0xac, //0x00005a10 .quad -6025006692552756422
	0xee, 0x12, 0xb8, 0xcc, 0x22, 0xb4, 0xab, 0x91, //0x00005a18 .quad -7950062655635975442
	0x09, 0x60, 0x4d, 0x31, 0x6b, 0x98, 0x7b, 0x57, //0x00005a20 .quad 6303799689591218185
	0xaa, 0x17, 0xe6, 0x7f, 0x2b, 0xa1, 0x16, 0xb6, //0x00005a28 .quad -5325892301117581398
	0x0b, 0xb8, 0xa0, 0xfd, 0x85, 0x7e, 0x5a, 0xed, //0x00005a30 .quad -1343622424865753077
	0x94, 0x9d, 0xdf, 0x5f, 0x76, 0x49, 0x9c, 0xe3, //0x00005a38 .quad -2045679357969588844
	0x07, 0x73, 0x84, 0xbe, 0x13, 0x8f, 0x58, 0x14, //0x00005a40 .quad 1466078993672598279
	0x7d, 0xc2, 0xeb, 0xfb, 0xe9, 0xad, 0x41, 0x8e, //0x00005a48 .quad -8196078626372074883
	0xc8, 0x8f, 0x25, 0xae, 0xd8, 0xb2, 0x6e, 0x59, //0x00005a50 .quad 6444284760518135752
	0x1c, 0xb3, 0xe6, 0x7a, 0x64, 0x19, 0xd2, 0xb1, //0x00005a58 .quad -5633412264537705700
	0xbb, 0xf3, 0xae, 0xd9, 0x8e, 0x5f, 0xca, 0x6f, //0x00005a60 .quad 8055355950647669691
	0xe3, 0x5f, 0xa0, 0x99, 0xbd, 0x9f, 0x46, 0xde, //0x00005a68 .quad -2430079312244744221
	0x54, 0x58, 0x0d, 0x48, 0xb9, 0x7b, 0xde, 0x25, //0x00005a70 .quad 2728754459941099604
	0xee, 0x3b, 0x04, 0x80, 0xd6, 0x23, 0xec, 0x8a, //0x00005a78 .quad -8436328597794046994
	0x6a, 0xae, 0x10, 0x9a, 0xa7, 0x1a, 0x56, 0xaf, //0x00005a80 .quad -5812428961928401302
	0xe9, 0x4a, 0x05, 0x20, 0xcc, 0x2c, 0xa7, 0xad, //0x00005a88 .quad -5933724728815170839
	0x04, 0xda, 0x94, 0x80, 0x51, 0xa1, 0x2b, 0x1b, //0x00005a90 .quad 1957835834444274180
	0xa4, 0x9d, 0x06, 0x28, 0xff, 0xf7, 0x10, 0xd9, //0x00005a98 .quad -2805469892591575644
	0x42, 0x08, 0x5d, 0xf0, 0xd2, 0x44, 0xfb, 0x90, //0x00005aa0 .quad -7999724640327104446
	0x86, 0x22, 0x04, 0x79, 0xff, 0x9a, 0xaa, 0x87, //0x00005aa8 .quad -8670947710510816634
	0x53, 0x4a, 0x74, 0xac, 0x07, 0x16, 0x3a, 0x35, //0x00005ab0 .quad 3835402254873283155
	0x28, 0x2b, 0x45, 0x57, 0xbf, 0x41, 0x95, 0xa9, //0x00005ab8 .quad -6226998619711132888
	0xe8, 0x5c, 0x91, 0x97, 0x89, 0x9b, 0x88, 0x42, //0x00005ac0 .quad 4794252818591603944
	0xf2, 0x75, 0x16, 0x2d, 0x2f, 0x92, 0xfa, 0xd3, //0x00005ac8 .quad -3172062256211528206
	0x11, 0xda, 0xba, 0xfe, 0x35, 0x61, 0x95, 0x69, //0x00005ad0 .quad 7608094030047140369
	0xb7, 0x09, 0x2e, 0x7c, 0x5d, 0x9b, 0x7c, 0x84, //0x00005ad8 .quad -8900067937773286985
	0x95, 0x90, 0x69, 0x7e, 0x83, 0xb9, 0xfa, 0x43, //0x00005ae0 .quad 4898431519131537557
	0x25, 0x8c, 0x39, 0xdb, 0x34, 0xc2, 0x9b, 0xa5, //0x00005ae8 .quad -6513398903789220827
	0xbb, 0xf4, 0x03, 0x5e, 0xe4, 0x67, 0xf9, 0x94, //0x00005af0 .quad -7712018656367741765
	0x2e, 0xef, 0x07, 0x12, 0xc2, 0xb2, 0x02, 0xcf, //0x00005af8 .quad -3530062611309138130
	0xf5, 0x78, 0xc2, 0xba, 0xee, 0xe0, 0x1b, 0x1d, //0x00005b00 .quad 2097517367411243253
	0x7d, 0xf5, 0x44, 0x4b, 0xb9, 0xaf, 0x61, 0x81, //0x00005b08 .quad -9123818159709293187
	0x32, 0x17, 0x73, 0x69, 0x2a, 0xd9, 0x62, 0x64, //0x00005b10 .quad 7233582727691441970
	0xdc, 0x32, 0x16, 0x9e, 0xa7, 0x1b, 0xba, 0xa1, //0x00005b18 .quad -6793086681209228580
	0xfe, 0xdc, 0xcf, 0x03, 0x75, 0x8f, 0x7b, 0x7d, //0x00005b20 .quad 9041978409614302462
	0x93, 0xbf, 0x9b, 0x85, 0x91, 0xa2, 0x28, 0xca, //0x00005b28 .quad -3879672333084147821
	0x3e, 0xd4, 0xc3, 0x44, 0x52, 0x73, 0xda, 0x5c, //0x00005b30 .quad 6690786993590490174
	0x78, 0xaf, 0x02, 0xe7, 0x35, 0xcb, 0xb2, 0xfc, //0x00005b38 .quad -237904397927796872
	0xa7, 0x64, 0xfa, 0x6a, 0x13, 0x88, 0x08, 0x3a, //0x00005b40 .quad 4181741870994056359
	0xab, 0xad, 0x61, 0xb0, 0x01, 0xbf, 0xef, 0x9d, //0x00005b48 .quad -7066219276345954901
	0xd0, 0xfd, 0xb8, 0x45, 0x18, 0xaa, 0x8a, 0x08, //0x00005b50 .quad 615491320315182544
	0x16, 0x19, 0x7a, 0x1c, 0xc2, 0xae, 0x6b, 0xc5, //0x00005b58 .quad -4221088077005055722
	0x45, 0x3d, 0x27, 0x57, 0x9e, 0x54, 0xad, 0x8a, //0x00005b60 .quad -8454007886460797627
	0x5b, 0x9f, 0x98, 0xa3, 0x72, 0x9a, 0xc6, 0xf6, //0x00005b68 .quad -664674077828931749
	0x4b, 0x86, 0x78, 0xf6, 0xe2, 0x54, 0xac, 0x36, //0x00005b70 .quad 3939617107816777291
	0x99, 0x63, 0x3f, 0xa6, 0x87, 0x20, 0x3c, 0x9a, //0x00005b78 .quad -7332950326284164199
	0xdd, 0xa7, 0x16, 0xb4, 0x1b, 0x6a, 0x57, 0x84, //0x00005b80 .quad -8910536670511192099
	0x7f, 0x3c, 0xcf, 0x8f, 0xa9, 0x28, 0xcb, 0xc0, //0x00005b88 .quad -4554501889427817345
	0xd5, 0x51, 0x1c, 0xa1, 0xa2, 0x44, 0x6d, 0x65, //0x00005b90 .quad 7308573235570561493
	0x9f, 0x0b, 0xc3, 0xf3, 0xd3, 0xf2, 0xfd, 0xf0, //0x00005b98 .quad -1081441343357383777
	0x25, 0xb3, 0xb1, 0xa4, 0xe5, 0x4a, 0x64, 0x9f, //0x00005ba0 .quad -6961356773836868827
	0x43, 0xe7, 0x59, 0x78, 0xc4, 0xb7, 0x9e, 0x96, //0x00005ba8 .quad -7593429867239446717
	0xee, 0x1f, 0xde, 0x0d, 0x9f, 0x5d, 0x3d, 0x87, //0x00005bb0 .quad -8701695967296086034
	0x14, 0x61, 0x70, 0x96, 0xb5, 0x65, 0x46, 0xbc, //0x00005bb8 .quad -4880101315621920492
	0xea, 0xa7, 0x55, 0xd1, 0x06, 0xb5, 0x0c, 0xa9, //0x00005bc0 .quad -6265433940692719638
	0x59, 0x79, 0x0c, 0xfc, 0x22, 0xff, 0x57, 0xeb, //0x00005bc8 .quad -1488440626100012711
	0xf2, 0x88, 0xd5, 0x42, 0x24, 0xf1, 0xa7, 0x09, //0x00005bd0 .quad 695789805494438130
	0xd8, 0xcb, 0x87, 0xdd, 0x75, 0xff, 0x16, 0x93, //0x00005bd8 .quad -7847804418953589800
	0x2f, 0xeb, 0x8a, 0x53, 0x6d, 0xed, 0x11, 0x0c, //0x00005be0 .quad 869737256868047663
	0xce, 0xbe, 0xe9, 0x54, 0x53, 0xbf, 0xdc, 0xb7, //0x00005be8 .quad -5198069505264599346
	0xfa, 0xa5, 0x6d, 0xa8, 0xc8, 0x68, 0x16, 0x8f, //0x00005bf0 .quad -8136200465769716230
	0x81, 0x2e, 0x24, 0x2a, 0x28, 0xef, 0xd3, 0xe5, //0x00005bf8 .quad -1885900863153361279
	0xbc, 0x87, 0x44, 0x69, 0x7d, 0x01, 0x6e, 0xf9, //0x00005c00 .quad -473439272678684740
	0x10, 0x9d, 0x56, 0x1a, 0x79, 0x75, 0xa4, 0x8f, //0x00005c08 .quad -8096217067111932656
	0xac, 0xa9, 0x95, 0xc3, 0xdc, 0x81, 0xc9, 0x37, //0x00005c10 .quad 4019886927579031980
	0x55, 0x44, 0xec, 0x60, 0xd7, 0x92, 0x8d, 0xb3, //0x00005c18 .quad -5508585315462527915
	0x17, 0x14, 0x7b, 0xf4, 0x53, 0xe2, 0xbb, 0x85, //0x00005c20 .quad -8810199395808373737
	0x6a, 0x55, 0x27, 0x39, 0x8d, 0xf7, 0x70, 0xe0, //0x00005c28 .quad -2274045625900771990
	0x8e, 0xec, 0xcc, 0x78, 0x74, 0x6d, 0x95, 0x93, //0x00005c30 .quad -7812217631593927538
	0x62, 0x95, 0xb8, 0x43, 0xb8, 0x9a, 0x46, 0x8c, //0x00005c38 .quad -8338807543829064350
	0xb2, 0x27, 0x00, 0x97, 0xd1, 0xc8, 0x7a, 0x38, //0x00005c40 .quad 4069786015789754290
	0xbb, 0xba, 0xa6, 0x54, 0x66, 0x41, 0x58, 0xaf, //0x00005c48 .quad -5811823411358942533
	0x9e, 0x31, 0xc0, 0xfc, 0x05, 0x7b, 0x99, 0x06, //0x00005c50 .quad 475546501309804958
	0x6a, 0x69, 0xd0, 0xe9, 0xbf, 0x51, 0x2e, 0xdb, //0x00005c58 .quad -2653093245771290262
	0x03, 0x1f, 0xf8, 0xbd, 0xe3, 0xec, 0x1f, 0x44, //0x00005c60 .quad 4908902581746016003
	0xe2, 0x41, 0x22, 0xf2, 0x17, 0xf3, 0xfc, 0x88, //0x00005c68 .quad -8575712306248138270
	0xc3, 0x26, 0x76, 0xad, 0x1c, 0xe8, 0x27, 0xd5, //0x00005c70 .quad -3087243809672255805
	0x5a, 0xd2, 0xaa, 0xee, 0xdd, 0x2f, 0x3c, 0xab, //0x00005c78 .quad -6107954364382784934
	0x74, 0xb0, 0xd3, 0xd8, 0x23, 0xe2, 0x71, 0x8a, //0x00005c80 .quad -8470740780517707660
	0xf1, 0x86, 0x55, 0x6a, 0xd5, 0x3b, 0x0b, 0xd6, //0x00005c88 .quad -3023256937051093263
	0x49, 0x4e, 0x84, 0x67, 0x56, 0x2d, 0x87, 0xf6, //0x00005c90 .quad -682526969396179383
	0x56, 0x74, 0x75, 0x62, 0x65, 0x05, 0xc7, 0x85, //0x00005c98 .quad -8807064613298015146
	0xdb, 0x61, 0x65, 0x01, 0xac, 0xf8, 0x28, 0xb4, //0x00005ca0 .quad -5464844730172612133
	0x6c, 0xd1, 0x12, 0xbb, 0xbe, 0xc6, 0x38, 0xa7, //0x00005ca8 .quad -6397144748195131028
	0x52, 0xba, 0xbe, 0x01, 0xd7, 0x36, 0x33, 0xe1, //0x00005cb0 .quad -2219369894288377262
	0xc7, 0x85, 0xd7, 0x69, 0x6e, 0xf8, 0x06, 0xd1, //0x00005cb8 .quad -3384744916816525881
	0x73, 0x34, 0x17, 0x61, 0x46, 0x02, 0xc0, 0xec, //0x00005cc0 .quad -1387106183930235789
	0x9c, 0xb3, 0x26, 0x02, 0x45, 0x5b, 0xa4, 0x82, //0x00005cc8 .quad -9032994600651410532
	0x90, 0x01, 0x5d, 0xf9, 0xd7, 0x02, 0xf0, 0x27, //0x00005cd0 .quad 2877803288514593168
	0x84, 0x60, 0xb0, 0x42, 0x16, 0x72, 0x4d, 0xa3, //0x00005cd8 .quad -6679557232386875260
	0xf4, 0x41, 0xb4, 0xf7, 0x8d, 0x03, 0xec, 0x31, //0x00005ce0 .quad 3597254110643241460
	0xa5, 0x78, 0x5c, 0xd3, 0x9b, 0xce, 0x20, 0xcc, //0x00005ce8 .quad -3737760522056206171
	0x71, 0x52, 0xa1, 0x75, 0x71, 0x04, 0x67, 0x7e, //0x00005cf0 .quad 9108253656731439729
	0xce, 0x96, 0x33, 0xc8, 0x42, 0x02, 0x29, 0xff, //0x00005cf8 .quad -60514634142869810
	0x86, 0xd3, 0x84, 0xe9, 0xc6, 0x62, 0x00, 0x0f, //0x00005d00 .quad 1080972517029761926
	0x41, 0x3e, 0x20, 0xbd, 0x69, 0xa1, 0x79, 0x9f, //0x00005d08 .quad -6955350673980375487
	0x68, 0x08, 0xe6, 0xa3, 0x78, 0x7b, 0xc0, 0x52, //0x00005d10 .quad 5962901664714590312
	0xd1, 0x4d, 0x68, 0x2c, 0xc4, 0x09, 0x58, 0xc7, //0x00005d18 .quad -4082502324048081455
	0x82, 0x8a, 0xdf, 0xcc, 0x56, 0x9a, 0x70, 0xa7, //0x00005d20 .quad -6381430974388925822
	0x45, 0x61, 0x82, 0x37, 0x35, 0x0c, 0x2e, 0xf9, //0x00005d28 .quad -491441886632713915
	0x91, 0xb6, 0x0b, 0x40, 0x76, 0x60, 0xa6, 0x88, //0x00005d30 .quad -8600080377420466543
	0xcb, 0x7c, 0xb1, 0x42, 0xa1, 0xc7, 0xbc, 0x9b, //0x00005d38 .quad -7224680206786528053
	0x35, 0xa4, 0x0e, 0xd0, 0x93, 0xf8, 0xcf, 0x6a, //0x00005d40 .quad 7696643601933968437
	0xfe, 0xdb, 0x5d, 0x93, 0x89, 0xf9, 0xab, 0xc2, //0x00005d48 .quad -4419164240055772162
	0x43, 0x4d, 0x12, 0xc4, 0xb8, 0xf6, 0x83, 0x05, //0x00005d50 .quad 397432465562684739
	0xfe, 0x52, 0x35, 0xf8, 0xeb, 0xf7, 0x56, 0xf3, //0x00005d58 .quad -912269281642327298
	0x4a, 0x70, 0x8b, 0x7a, 0x33, 0x7a, 0x72, 0xc3, //0x00005d60 .quad -4363290727450709942
	0xde, 0x53, 0x21, 0x7b, 0xf3, 0x5a, 0x16, 0x98, //0x00005d68 .quad -7487697328667536418
	0x5c, 0x4c, 0x2e, 0x59, 0xc0, 0x18, 0x4f, 0x74, //0x00005d70 .quad 8380944645968776284
	0xd6, 0xa8, 0xe9, 0x59, 0xb0, 0xf1, 0x1b, 0xbe, //0x00005d78 .quad -4747935642407032618
	0x73, 0xdf, 0x79, 0x6f, 0xf0, 0xde, 0x62, 0x11, //0x00005d80 .quad 1252808770606194547
	0x0c, 0x13, 0x64, 0x70, 0x1c, 0xee, 0xa2, 0xed, //0x00005d88 .quad -1323233534581402868
	0xa8, 0x2b, 0xac, 0x45, 0x56, 0xcb, 0xdd, 0x8a, //0x00005d90 .quad -8440366555225904216
	0xe7, 0x8b, 0x3e, 0xc6, 0xd1, 0xd4, 0x85, 0x94, //0x00005d98 .quad -7744549986754458649
	0x92, 0x36, 0x17, 0xd7, 0x2b, 0x3e, 0x95, 0x6d, //0x00005da0 .quad 7896285879677171346
	0xe1, 0x2e, 0xce, 0x37, 0x06, 0x4a, 0xa7, 0xb9, //0x00005da8 .quad -5069001465015685407
	0x37, 0x04, 0xdd, 0xcc, 0xb6, 0x8d, 0xfa, 0xc8, //0x00005db0 .quad -3964700705685699529
	0x99, 0xba, 0xc1, 0xc5, 0x87, 0x1c, 0x11, 0xe8, //0x00005db8 .quad -1724565812842218855
	0xa2, 0x22, 0x0a, 0x40, 0x92, 0x98, 0x9c, 0x1d, //0x00005dc0 .quad 2133748077373825698
	0xa0, 0x14, 0x99, 0xdb, 0xd4, 0xb1, 0x0a, 0x91, //0x00005dc8 .quad -7995382660667468640
	0x4b, 0xab, 0x0c, 0xd0, 0xb6, 0xbe, 0x03, 0x25, //0x00005dd0 .quad 2667185096717282123
	0xc8, 0x59, 0x7f, 0x12, 0x4a, 0x5e, 0x4d, 0xb5, //0x00005dd8 .quad -5382542307406947896
	0x1d, 0xd6, 0x0f, 0x84, 0x64, 0xae, 0x44, 0x2e, //0x00005de0 .quad 3333981370896602653
	0x3a, 0x30, 0x1f, 0x97, 0xdc, 0xb5, 0xa0, 0xe2, //0x00005de8 .quad -2116491865831296966
	0xd2, 0xe5, 0x89, 0xd2, 0xfe, 0xec, 0xea, 0x5c, //0x00005df0 .quad 6695424375237764562
	0x24, 0x7e, 0x73, 0xde, 0xa9, 0x71, 0xa4, 0x8d, //0x00005df8 .quad -8240336443785642460
	0x47, 0x5f, 0x2c, 0x87, 0x3e, 0xa8, 0x25, 0x74, //0x00005e00 .quad 8369280469047205703
	0xad, 0x5d, 0x10, 0x56, 0x14, 0x8e, 0x0d, 0xb1, //0x00005e08 .quad -5688734536304665171
	0x19, 0x77, 0xf7, 0x28, 0x4e, 0x12, 0x2f, 0xd1, //0x00005e10 .quad -3373457468973156583
	0x18, 0x75, 0x94, 0x6b, 0x99, 0xf1, 0x50, 0xdd, //0x00005e18 .quad -2499232151953443560
	0x6f, 0xaa, 0x9a, 0xd9, 0x70, 0x6b, 0xbd, 0x82, //0x00005e20 .quad -9025939945749304721
	0x2f, 0xc9, 0x3c, 0xe3, 0xff, 0x96, 0x52, 0x8a, //0x00005e28 .quad -8479549122611984081
	0x0b, 0x55, 0x01, 0x10, 0x4d, 0xc6, 0x6c, 0x63, //0x00005e30 .quad 7164319141522920715
	0x7b, 0xfb, 0x0b, 0xdc, 0xbf, 0x3c, 0xe7, 0xac, //0x00005e38 .quad -5987750384837592197
	0x4e, 0xaa, 0x01, 0x54, 0xe0, 0xf7, 0x47, 0x3c, //0x00005e40 .quad 4343712908476262990
	0x5a, 0xfa, 0x0e, 0xd3, 0xef, 0x0b, 0x21, 0xd8, //0x00005e48 .quad -2873001962619602342
	0x71, 0x0a, 0x81, 0x34, 0xec, 0xfa, 0xac, 0x65, //0x00005e50 .quad 7326506586225052273
	0x78, 0x5c, 0xe9, 0xe3, 0x75, 0xa7, 0x14, 0x87, //0x00005e58 .quad -8713155254278333320
	0x0d, 0x4d, 0xa1, 0x41, 0xa7, 0x39, 0x18, 0x7f, //0x00005e60 .quad 9158133232781315341
	0x96, 0xb3, 0xe3, 0x5c, 0x53, 0xd1, 0xd9, 0xa8, //0x00005e68 .quad -6279758049420528746
	0x50, 0xa0, 0x09, 0x12, 0x11, 0x48, 0xde, 0x1e, //0x00005e70 .quad 2224294504121868368
	0x7c, 0xa0, 0x1c, 0x34, 0xa8, 0x45, 0x10, 0xd3, //0x00005e78 .quad -3238011543348273028
	0x32, 0x04, 0x46, 0xab, 0x0a, 0xed, 0x4a, 0x93, //0x00005e80 .quad -7833187971778608078
	0x4d, 0xe4, 0x91, 0x20, 0x89, 0x2b, 0xea, 0x83, //0x00005e88 .quad -8941286242233752499
	0x3f, 0x85, 0x17, 0x56, 0x4d, 0xa8, 0x1d, 0xf8, //0x00005e90 .quad -568112927868484289
	0x60, 0x5d, 0xb6, 0x68, 0x6b, 0xb6, 0xe4, 0xa4, //0x00005e98 .quad -6564921784364802720
	0x8e, 0x66, 0x9d, 0xab, 0x60, 0x12, 0x25, 0x36, //0x00005ea0 .quad 3901544858591782542
	0xb9, 0xf4, 0xe3, 0x42, 0x06, 0xe4, 0x1d, 0xce, //0x00005ea8 .quad -3594466212028615495
	0x19, 0x60, 0x42, 0x6b, 0x7c, 0x2b, 0xd7, 0xc1, //0x00005eb0 .quad -4479063491021217767
	0xf3, 0x78, 0xce, 0xe9, 0x83, 0xae, 0xd2, 0x80, //0x00005eb8 .quad -9164070410158966541
	0x1f, 0xf8, 0x12, 0x86, 0x5b, 0xf6, 0x4c, 0xb2, //0x00005ec0 .quad -5598829363776522209
	0x30, 0x17, 0x42, 0xe4, 0x24, 0x5a, 0x07, 0xa1, //0x00005ec8 .quad -6843401994271320272
	0x27, 0xb6, 0x97, 0x67, 0xf2, 0x33, 0xe0, 0xde, //0x00005ed0 .quad -2386850686293264857
	0xfc, 0x9c, 0x52, 0x1d, 0xae, 0x30, 0x49, 0xc9, //0x00005ed8 .quad -3942566474411762436
	0xb1, 0xa3, 0x7d, 0x01, 0xef, 0x40, 0x98, 0x16, //0x00005ee0 .quad 1628122660560806833
	0x3c, 0x44, 0xa7, 0xa4, 0xd9, 0x7c, 0x9b, 0xfb, //0x00005ee8 .quad -316522074587315140
	0x4e, 0x86, 0xee, 0x60, 0x95, 0x28, 0x1f, 0x8e, //0x00005ef0 .quad -8205795374004271538
	0xa5, 0x8a, 0xe8, 0x06, 0x08, 0x2e, 0x41, 0x9d, //0x00005ef8 .quad -7115355324258153819
	0xe2, 0x27, 0x2a, 0xb9, 0xba, 0xf2, 0xa6, 0xf1, //0x00005f00 .quad -1033872180650563614
	0x4e, 0xad, 0xa2, 0x08, 0x8a, 0x79, 0x91, 0xc4, //0x00005f08 .quad -4282508136895304370
	0xdb, 0xb1, 0x74, 0x67, 0x69, 0xaf, 0x10, 0xae, //0x00005f10 .quad -5904026244240592421
	0xa2, 0x58, 0xcb, 0x8a, 0xec, 0xd7, 0xb5, 0xf5, //0x00005f18 .quad -741449152691742558
	0x29, 0xef, 0xa8, 0xe0, 0xa1, 0x6d, 0xca, 0xac, //0x00005f20 .quad -5995859411864064215
	0x65, 0x17, 0xbf, 0xd6, 0xf3, 0xa6, 0x91, 0x99, //0x00005f28 .quad -7380934748073420955
	0xf3, 0x2a, 0xd3, 0x58, 0x0a, 0x09, 0xfd, 0x17, //0x00005f30 .quad 1728547772024695539
	0x3f, 0xdd, 0x6e, 0xcc, 0xb0, 0x10, 0xf6, 0xbf, //0x00005f38 .quad -4614482416664388289
	0xb0, 0xf5, 0x07, 0xef, 0x4c, 0x4b, 0xfc, 0xdd, //0x00005f40 .quad -2451001303396518480
	0x8e, 0x94, 0x8a, 0xff, 0xdc, 0x94, 0xf3, 0xef, //0x00005f48 .quad -1156417002403097458
	0x8e, 0xf9, 0x64, 0x15, 0x10, 0xaf, 0xbd, 0x4a, //0x00005f50 .quad 5385653213018257806
	0xd9, 0x9c, 0xb6, 0x1f, 0x0a, 0x3d, 0xf8, 0x95, //0x00005f58 .quad -7640289654143017767
	0xf1, 0x37, 0xbe, 0x1a, 0xd4, 0x1a, 0x6d, 0x9d, //0x00005f60 .quad -7102991539009341455
	0x0f, 0x44, 0xa4, 0xa7, 0x4c, 0x4c, 0x76, 0xbb, //0x00005f68 .quad -4938676049251384305
	0xed, 0xc5, 0x6d, 0x21, 0x89, 0x61, 0xc8, 0x84, //0x00005f70 .quad -8878739423761676819
	0x13, 0x55, 0x8d, 0xd1, 0x5f, 0xdf, 0x53, 0xea, //0x00005f78 .quad -1561659043136842477
	0xb4, 0x9b, 0xe4, 0xb4, 0xf5, 0x3c, 0xfd, 0x32, //0x00005f80 .quad 3674159897003727796
	0x2c, 0x55, 0xf8, 0xe2, 0x9b, 0x6b, 0x74, 0x92, //0x00005f88 .quad -7893565929601608404
	0xa1, 0xc2, 0x1d, 0x22, 0x33, 0x8c, 0xbc, 0x3f, //0x00005f90 .quad 4592699871254659745
	0x77, 0x6a, 0xb6, 0xdb, 0x82, 0x86, 0x11, 0xb7, //0x00005f98 .quad -5255271393574622601
	0x4a, 0x33, 0xa5, 0xea, 0x3f, 0xaf, 0xab, 0x0f, //0x00005fa0 .quad 1129188820640936778
	0x15, 0x05, 0xa4, 0x92, 0x23, 0xe8, 0xd5, 0xe4, //0x00005fa8 .quad -1957403223540890347
	0x0e, 0x40, 0xa7, 0xf2, 0x87, 0x4d, 0xcb, 0x29, //0x00005fb0 .quad 3011586022114279438
	0x2d, 0x83, 0xa6, 0x3b, 0x16, 0xb1, 0x05, 0x8f, //0x00005fb8 .quad -8140906042354138323
	0x12, 0x10, 0x51, 0xef, 0xe9, 0x20, 0x3e, 0x74, //0x00005fc0 .quad 8376168546070237202
	0xf8, 0x23, 0x90, 0xca, 0x5b, 0x1d, 0xc7, 0xb2, //0x00005fc8 .quad -5564446534515285000
	0x16, 0x54, 0x25, 0x6b, 0x24, 0xa9, 0x4d, 0x91, //0x00005fd0 .quad -7976533391121755114
	0xf6, 0x2c, 0x34, 0xbd, 0xb2, 0xe4, 0x78, 0xdf, //0x00005fd8 .quad -2343872149716718346
	0x8e, 0x54, 0xf7, 0xc2, 0xb6, 0x89, 0xd0, 0x1a, //0x00005fe0 .quad 1932195658189984910
	0x1a, 0x9c, 0x40, 0xb6, 0xef, 0x8e, 0xab, 0x8b, //0x00005fe8 .quad -8382449121214030822
	0xb1, 0x29, 0xb5, 0x73, 0x24, 0xac, 0x84, 0xa1, //0x00005ff0 .quad -6808127464117294671
	0x20, 0xc3, 0xd0, 0xa3, 0xab, 0x72, 0x96, 0xae, //0x00005ff8 .quad -5866375383090150624
	0x1e, 0x74, 0xa2, 0x90, 0x2d, 0xd7, 0xe5, 0xc9, //0x00006000 .quad -3898473311719230434
	0xe8, 0xf3, 0xc4, 0x8c, 0x56, 0x0f, 0x3c, 0xda, //0x00006008 .quad -2721283210435300376
	0x92, 0x88, 0x65, 0x7a, 0x7c, 0xa6, 0x2f, 0x7e, //0x00006010 .quad 9092669226243950738
	0x71, 0x18, 0xfb, 0x17, 0x96, 0x89, 0x65, 0x88, //0x00006018 .quad -8618331034163144591
	0xb7, 0xea, 0xfe, 0x98, 0x1b, 0x90, 0xbb, 0xdd, //0x00006020 .quad -2469221522477225289
	0x8d, 0xde, 0xf9, 0x9d, 0xfb, 0xeb, 0x7e, 0xaa, //0x00006028 .quad -6161227774276542835
	0x65, 0xa5, 0x3e, 0x7f, 0x22, 0x74, 0x2a, 0x55, //0x00006030 .quad 6136845133758244197
	0x31, 0x56, 0x78, 0x85, 0xfa, 0xa6, 0x1e, 0xd5, //0x00006038 .quad -3089848699418290639
	0x5f, 0x27, 0x87, 0x8f, 0x95, 0x88, 0x3a, 0xd5, //0x00006040 .quad -3082000819042179233
	0xde, 0x35, 0x6b, 0x93, 0x5c, 0x28, 0x33, 0x85, //0x00006048 .quad -8848684464777513506
	0x37, 0xf1, 0x68, 0xf3, 0xba, 0x2a, 0x89, 0x8a, //0x00006050 .quad -8464187042230111945
	0x56, 0x03, 0x46, 0xb8, 0x73, 0xf2, 0x7f, 0xa6, //0x00006058 .quad -6449169562544503978
	0x85, 0x2d, 0x43, 0xb0, 0x69, 0x75, 0x2b, 0x2d, //0x00006060 .quad 3254824252494523781
	0x2c, 0x84, 0x57, 0xa6, 0x10, 0xef, 0x1f, 0xd0, //0x00006068 .quad -3449775934753242068
	0x73, 0xfc, 0x29, 0x0e, 0x62, 0x29, 0x3b, 0x9c, //0x00006070 .quad -7189106879045698445
	0x9b, 0xb2, 0xf6, 0x67, 0x6a, 0xf5, 0x13, 0x82, //0x00006078 .quad -9073638986861858149
	0x8f, 0x7b, 0xb4, 0x91, 0xba, 0xf3, 0x49, 0x83, //0x00006080 .quad -8986383598807123057
	0x42, 0x5f, 0xf4, 0x01, 0xc5, 0xf2, 0x98, 0xa2, //0x00006088 .quad -6730362715149934782
	0x73, 0x9a, 0x21, 0x36, 0xa9, 0x70, 0x1c, 0x24, //0x00006090 .quad 2602078556773259891
	0x13, 0x77, 0x71, 0x42, 0x76, 0x2f, 0x3f, 0xcb, //0x00006098 .quad -3801267375510030573
	0x10, 0x01, 0xaa, 0x83, 0xd3, 0x8c, 0x23, 0xed, //0x000060a0 .quad -1359087822460813040
	0xd7, 0xd4, 0x0d, 0xd3, 0x53, 0xfb, 0x0e, 0xfe, //0x000060a8 .quad -139898200960150313
	0xaa, 0x40, 0x4a, 0x32, 0x04, 0x38, 0x36, 0xf4, //0x000060b0 .quad -849429889038008150
	0x06, 0xa5, 0xe8, 0x63, 0x14, 0x5d, 0xc9, 0x9e, //0x000060b8 .quad -7004965403241175802
	0xd5, 0xd0, 0xdc, 0x3e, 0x05, 0xc6, 0x43, 0xb1, //0x000060c0 .quad -5673473379724898091
	0x48, 0xce, 0xe2, 0x7c, 0x59, 0xb4, 0x7b, 0xc6, //0x000060c8 .quad -4144520735624081848
	0x0a, 0x05, 0x94, 0x8e, 0x86, 0xb7, 0x94, 0xdd, //0x000060d0 .quad -2480155706228734710
	0xda, 0x81, 0x1b, 0xdc, 0x6f, 0xa1, 0x1a, 0xf8, //0x000060d8 .quad -568964901102714406
	0x26, 0x83, 0x1c, 0x19, 0xb4, 0xf2, 0x7c, 0xca, //0x000060e0 .quad -3855940325606653146
	0x28, 0x31, 0x91, 0xe9, 0xe5, 0xa4, 0x10, 0x9b, //0x000060e8 .quad -7273132090830278360
	0xf0, 0xa3, 0x63, 0x1f, 0x61, 0x2f, 0x1c, 0xfd, //0x000060f0 .quad -208239388580928528
	0x72, 0x7d, 0xf5, 0x63, 0x1f, 0xce, 0xd4, 0xc1, //0x000060f8 .quad -4479729095110460046
	0xec, 0x8c, 0x3c, 0x67, 0x39, 0x3b, 0x63, 0xbc, //0x00006100 .quad -4871985254153548564
	0xcf, 0xdc, 0xf2, 0x3c, 0xa7, 0x01, 0x4a, 0xf2, //0x00006108 .quad -987975350460687153
	0x13, 0xd8, 0x85, 0xe0, 0x03, 0x05, 0xbe, 0xd5, //0x00006110 .quad -3044990783845967853
	0x01, 0xca, 0x17, 0x86, 0x08, 0x41, 0x6e, 0x97, //0x00006118 .quad -7535013621679011327
	0x18, 0x4e, 0xa7, 0xd8, 0x44, 0x86, 0x2d, 0x4b, //0x00006120 .quad 5417133557047315992
	0x82, 0xbc, 0x9d, 0xa7, 0x4a, 0xd1, 0x49, 0xbd, //0x00006128 .quad -4807081008671376254
	0x9e, 0x21, 0xd1, 0x0e, 0xd6, 0xe7, 0xf8, 0xdd, //0x00006130 .quad -2451955090545630818
	0xa2, 0x2b, 0x85, 0x51, 0x9d, 0x45, 0x9c, 0xec, //0x00006138 .quad -1397165242411832414
	0x03, 0xb5, 0x42, 0xc9, 0xe5, 0x90, 0xbb, 0xca, //0x00006140 .quad -3838314940804713213
	0x45, 0x3b, 0xf3, 0x52, 0x82, 0xab, 0xe1, 0x93, //0x00006148 .quad -7790757304148477115
	0x43, 0x62, 0x93, 0x3b, 0x1f, 0x75, 0x6a, 0x3d, //0x00006150 .quad 4425478360848884291
	0x17, 0x0a, 0xb0, 0xe7, 0x62, 0x16, 0xda, 0xb8, //0x00006158 .quad -5126760611758208489
	0xd4, 0x3a, 0x78, 0x0a, 0x67, 0x12, 0xc5, 0x0c, //0x00006160 .quad 920161932633717460
	0x9d, 0x0c, 0x9c, 0xa1, 0xfb, 0x9b, 0x10, 0xe7, //0x00006168 .quad -1796764746270372707
	0xc5, 0x24, 0x8b, 0x66, 0x80, 0x2b, 0xfb, 0x27, //0x00006170 .quad 2880944217109767365
	0xe2, 0x87, 0x01, 0x45, 0x7d, 0x61, 0x6a, 0x90, //0x00006178 .quad -8040506994060064798
	0xf6, 0xed, 0x2d, 0x80, 0x60, 0xf6, 0xf9, 0xb1, //0x00006180 .quad -5622191765467566602
	0xda, 0xe9, 0x41, 0x96, 0xdc, 0xf9, 0x84, 0xb4, //0x00006188 .quad -5438947724147693094
	0x73, 0x69, 0x39, 0xa0, 0xf8, 0x73, 0x78, 0x5e, //0x00006190 .quad 6807318348447705459
	0x51, 0x64, 0xd2, 0xbb, 0x53, 0x38, 0xa6, 0xe1, //0x00006198 .quad -2186998636757228463
	0xe8, 0xe1, 0x23, 0x64, 0x7b, 0x48, 0x0b, 0xdb, //0x000061a0 .quad -2662955059861265944
	0xb2, 0x7e, 0x63, 0x55, 0x34, 0xe3, 0x07, 0x8d, //0x000061a8 .quad -8284403175614349646
	0x62, 0xda, 0x2c, 0x3d, 0x9a, 0x1a, 0xce, 0x91, //0x000061b0 .quad -7940379843253970334
	0x5f, 0x5e, 0xbc, 0x6a, 0x01, 0xdc, 0x49, 0xb0, //0x000061b8 .quad -5743817951090549153
	0xfb, 0x10, 0x78, 0xcc, 0x40, 0xa1, 0x41, 0x76, //0x000061c0 .quad 8521269269642088699
	0xf7, 0x75, 0x6b, 0xc5, 0x01, 0x53, 0x5c, 0xdc, //0x000061c8 .quad -2568086420435798537
	0x9d, 0x0a, 0xcb, 0x7f, 0xc8, 0x04, 0xe9, 0xa9, //0x000061d0 .quad -6203421752542164323
	0xba, 0x29, 0x63, 0x1b, 0xe1, 0xb3, 0xb9, 0x89, //0x000061d8 .quad -8522583040413455942
	0x44, 0xcd, 0xbd, 0x9f, 0xfa, 0x45, 0x63, 0x54, //0x000061e0 .quad 6080780864604458308
	0x29, 0xf4, 0x3b, 0x62, 0xd9, 0x20, 0x28, 0xac, //0x000061e8 .quad -6041542782089432023
	0x95, 0x40, 0xad, 0x47, 0x79, 0x17, 0x7c, 0xa9, //0x000061f0 .quad -6234081974526590827
	0x33, 0xf1, 0xca, 0xba, 0x0f, 0x29, 0x32, 0xd7, //0x000061f8 .quad -2940242459184402125
	0x5d, 0x48, 0xcc, 0xcc, 0xab, 0x8e, 0xed, 0x49, //0x00006200 .quad 5327070802775656541
	0xc0, 0xd6, 0xbe, 0xd4, 0xa9, 0x59, 0x7f, 0x86, //0x00006208 .quad -8755180564631333184
	0x74, 0x5a, 0xff, 0xbf, 0x56, 0xf2, 0x68, 0x5c, //0x00006210 .quad 6658838503469570676
	0x70, 0x8c, 0xee, 0x49, 0x14, 0x30, 0x1f, 0xa8, //0x00006218 .quad -6332289687361778576
	0x11, 0x31, 0xff, 0x6f, 0xec, 0x2e, 0x83, 0x73, //0x00006220 .quad 8323548129336963345
	0x8c, 0x2f, 0x6a, 0x5c, 0x19, 0xfc, 0x26, 0xd2, //0x00006228 .quad -3303676090774835316
	0xab, 0x7e, 0xff, 0xc5, 0x53, 0xfd, 0x31, 0xc8, //0x00006230 .quad -4021154456019173717
	0xb7, 0x5d, 0xc2, 0xd9, 0x8f, 0x5d, 0x58, 0x83, //0x00006238 .quad -8982326584375353929
	0x55, 0x5e, 0x7f, 0xb7, 0xa8, 0x7c, 0x3e, 0xba, //0x00006240 .quad -5026443070023967147
	0x25, 0xf5, 0x32, 0xd0, 0xf3, 0x74, 0x2e, 0xa4, //0x00006248 .quad -6616222212041804507
	0xeb, 0x35, 0x5f, 0xe5, 0xd2, 0x1b, 0xce, 0x28, //0x00006250 .quad 2940318199324816875
	0x6f, 0xb2, 0x3f, 0xc4, 0x30, 0x12, 0x3a, 0xcd, //0x00006258 .quad -3658591746624867729
	0xb3, 0x81, 0x5b, 0xcf, 0x63, 0xd1, 0x80, 0x79, //0x00006260 .quad 8755227902219092403
	0x85, 0xcf, 0xa7, 0x7a, 0x5e, 0x4b, 0x44, 0x80, //0x00006268 .quad -9204148869281624187
	0x1f, 0x62, 0x32, 0xc3, 0xbc, 0x05, 0xe1, 0xd7, //0x00006270 .quad -2891023177508298209
	0x66, 0xc3, 0x51, 0x19, 0x36, 0x5e, 0x55, 0xa0, //0x00006278 .quad -6893500068174642330
	0xa7, 0xfa, 0xfe, 0xf3, 0x2b, 0x47, 0xd9, 0x8d, //0x00006280 .quad -8225464990312760665
	0x40, 0x34, 0xa6, 0x9f, 0xc3, 0xb5, 0x6a, 0xc8, //0x00006288 .quad -4005189066790915008
	0x51, 0xb9, 0xfe, 0xf0, 0xf6, 0x98, 0x4f, 0xb1, //0x00006290 .quad -5670145219463562927
	0x50, 0xc1, 0x8f, 0x87, 0x34, 0x63, 0x85, 0xfa, //0x00006298 .quad -394800315061255856
	0xd3, 0x33, 0x9f, 0x56, 0x9a, 0xbf, 0xd1, 0x6e, //0x000062a0 .quad 7985374283903742931
	0xd2, 0xd8, 0xb9, 0xd4, 0x00, 0x5e, 0x93, 0x9c, //0x000062a8 .quad -7164279224554366766
	0xc8, 0x00, 0x47, 0xec, 0x80, 0x2f, 0x86, 0x0a, //0x000062b0 .quad 758345818024902856
	0x07, 0x4f, 0xe8, 0x09, 0x81, 0x35, 0xb8, 0xc3, //0x000062b8 .quad -4343663012265570553
	0xfa, 0xc0, 0x58, 0x27, 0x61, 0xbb, 0x27, 0xcd, //0x000062c0 .quad -3663753745896259334
	0xc8, 0x62, 0x62, 0x4c, 0xe1, 0x42, 0xa6, 0xf4, //0x000062c8 .quad -817892746904575288
	0x9c, 0x78, 0x97, 0xb8, 0x1c, 0xd5, 0x38, 0x80, //0x000062d0 .quad -9207375118826243940
	0xbd, 0x7d, 0xbd, 0xcf, 0xcc, 0xe9, 0xe7, 0x98, //0x000062d8 .quad -7428711994456441411
	0xc3, 0x56, 0xbd, 0xe6, 0x63, 0x0a, 0x47, 0xe0, //0x000062e0 .quad -2285846861678029117
	0x2c, 0xdd, 0xac, 0x03, 0x40, 0xe4, 0x21, 0xbf, //0x000062e8 .quad -4674203974643163860
	0x74, 0xac, 0x6c, 0xe0, 0xfc, 0xcc, 0x58, 0x18, //0x000062f0 .quad 1754377441329851508
	0x78, 0x14, 0x98, 0x04, 0x50, 0x5d, 0xea, 0xee, //0x000062f8 .quad -1231068949876566920
	0xc8, 0xeb, 0x43, 0x0c, 0x1e, 0x80, 0x37, 0x0f, //0x00006300 .quad 1096485900831157192
	0xcb, 0x0c, 0xdf, 0x02, 0x52, 0x7a, 0x52, 0x95, //0x00006308 .quad -7686947121313936181
	0xba, 0xe6, 0x54, 0x8f, 0x25, 0x60, 0x05, 0xd3, //0x00006310 .quad -3241078642388441414
	0xfd, 0xcf, 0x96, 0x83, 0xe6, 0x18, 0xa7, 0xba, //0x00006318 .quad -4996997883215032323
	0x69, 0x20, 0x2a, 0xf3, 0x2e, 0xb8, 0xc6, 0x47, //0x00006320 .quad 5172023733869224041
	0xfd, 0x83, 0x7c, 0x24, 0x20, 0xdf, 0x50, 0xe9, //0x00006328 .quad -1634561335591402499
	0x41, 0x54, 0xfa, 0x57, 0x1d, 0x33, 0xdc, 0x4c, //0x00006330 .quad 5538357842881958977
	0x7e, 0xd2, 0xcd, 0x16, 0x74, 0x8b, 0xd2, 0x91, //0x00006338 .quad -7939129862385708418
	0x52, 0xe9, 0xf8, 0xad, 0xe4, 0x3f, 0x13, 0xe0, //0x00006340 .quad -2300424733252327086
	0x1d, 0x47, 0x81, 0x1c, 0x51, 0x2e, 0x47, 0xb6, //0x00006348 .quad -5312226309554747619
	0xa6, 0x23, 0x77, 0xd9, 0xdd, 0x0f, 0x18, 0x58, //0x00006350 .quad 6347841120289366950
	0xe5, 0x98, 0xa1, 0x63, 0xe5, 0xf9, 0xd8, 0xe3, //0x00006358 .quad -2028596868516046619
	0x48, 0x76, 0xea, 0xa7, 0xea, 0x09, 0x0f, 0x57, //0x00006360 .quad 6273243709394548296
	0x8f, 0xff, 0x44, 0x5e, 0x2f, 0x9c, 0x67, 0x8e, //0x00006368 .quad -8185402070463610993
	0xda, 0x13, 0xe5, 0x51, 0x65, 0xcc, 0xd2, 0x2c, //0x00006370 .quad 3229868618315797466
	0x73, 0x3f, 0xd6, 0x35, 0x3b, 0x83, 0x01, 0xb2, //0x00006378 .quad -5620066569652125837
	0xd1, 0x58, 0x5e, 0xa6, 0x7e, 0x7f, 0x07, 0xf8, //0x00006380 .quad -574350245532641071
	0x4f, 0xcf, 0x4b, 0x03, 0x0a, 0xe4, 0x81, 0xde, //0x00006388 .quad -2413397193637769393
	0x82, 0xf7, 0xfa, 0x27, 0xaf, 0xaf, 0x04, 0xfb, //0x00006390 .quad -358968903457900670
	0x91, 0x61, 0x0f, 0x42, 0x86, 0x2e, 0x11, 0x8b, //0x00006398 .quad -8425902273664687727
	0x63, 0xb5, 0xf9, 0xf1, 0x9a, 0xdb, 0xc5, 0x79, //0x000063a0 .quad 8774660907532399971
	0xf6, 0x39, 0x93, 0xd2, 0x27, 0x7a, 0xd5, 0xad, //0x000063a8 .quad -5920691823653471754
	0xbc, 0x22, 0x78, 0xae, 0x81, 0x52, 0x37, 0x18, //0x000063b0 .quad 1744954097560724156
	0x74, 0x08, 0x38, 0xc7, 0xb1, 0xd8, 0x4a, 0xd9, //0x000063b8 .quad -2789178761139451788
	0xb5, 0x15, 0x0b, 0x0d, 0x91, 0x93, 0x22, 0x8f, //0x000063c0 .quad -8132775725879323211
	0x48, 0x05, 0x83, 0x1c, 0x6f, 0xc7, 0xce, 0x87, //0x000063c8 .quad -8660765753353239224
	0x22, 0xdb, 0x4d, 0x50, 0x75, 0x38, 0xeb, 0xb2, //0x000063d0 .quad -5554283638921766110
	0x9a, 0xc6, 0xa3, 0xe3, 0x4a, 0x79, 0xc2, 0xa9, //0x000063d8 .quad -6214271173264161126
	0xeb, 0x51, 0x61, 0xa4, 0x92, 0x06, 0xa6, 0x5f, //0x000063e0 .quad 6892203506629956075
	0x41, 0xb8, 0x8c, 0x9c, 0x9d, 0x17, 0x33, 0xd4, //0x000063e8 .quad -3156152948152813503
	0x33, 0xd3, 0xbc, 0xa6, 0x1b, 0xc4, 0xc7, 0xdb, //0x000063f0 .quad -2609901835997359309
	0x28, 0xf3, 0xd7, 0x81, 0xc2, 0xee, 0x9f, 0x84, //0x000063f8 .quad -8890124620236590296
	0x00, 0x08, 0x6c, 0x90, 0x22, 0xb5, 0xb9, 0x12, //0x00006400 .quad 1349308723430688768
	0xf3, 0xef, 0x4d, 0x22, 0x73, 0xea, 0xc7, 0xa5, //0x00006408 .quad -6500969756868349965
	0x00, 0x0a, 0x87, 0x34, 0x6b, 0x22, 0x68, 0xd7, //0x00006410 .quad -2925050114139026944
	0xef, 0x6b, 0xe1, 0xea, 0x0f, 0xe5, 0x39, 0xcf, //0x00006418 .quad -3514526177658049553
	0x40, 0x66, 0xd4, 0x00, 0x83, 0x15, 0xa1, 0xe6, //0x00006420 .quad -1828156321336891840
	0x75, 0xe3, 0xcc, 0xf2, 0x29, 0x2f, 0x84, 0x81, //0x00006428 .quad -9114107888677362827
	0xd0, 0x7f, 0x09, 0xc1, 0xe3, 0x5a, 0x49, 0x60, //0x00006430 .quad 6938176635183661008
	0x53, 0x1c, 0x80, 0x6f, 0xf4, 0x3a, 0xe5, 0xa1, //0x00006438 .quad -6780948842419315629
	0xc4, 0xdf, 0x4b, 0xb1, 0x9c, 0xb1, 0x5b, 0x38, //0x00006440 .quad 4061034775552188356
	0x68, 0x23, 0x60, 0x8b, 0xb1, 0x89, 0x5e, 0xca, //0x00006448 .quad -3864500034596756632
	0xb5, 0xd7, 0x9e, 0xdd, 0x03, 0x9e, 0x72, 0x46, //0x00006450 .quad 5076293469440235445
	0x42, 0x2c, 0x38, 0xee, 0x1d, 0x2c, 0xf6, 0xfc, //0x00006458 .quad -218939024818557886
	0xd1, 0x46, 0x83, 0x6a, 0xc2, 0xa2, 0x07, 0x6c, //0x00006460 .quad 7784369436827535057
	0xa9, 0x1b, 0xe3, 0xb4, 0x92, 0xdb, 0x19, 0x9e, //0x00006468 .quad -7054365918152680535
	0x85, 0x18, 0x24, 0x05, 0x73, 0x8b, 0x09, 0xc7, //0x00006470 .quad -4104596259247744891
	0x93, 0xe2, 0x1b, 0x62, 0x77, 0x52, 0xa0, 0xc5, //0x00006478 .quad -4206271379263462765
	0xa7, 0x1e, 0x6d, 0xc6, 0x4f, 0xee, 0xcb, 0xb8, //0x00006480 .quad -5130745324059681113
	0x38, 0xdb, 0xa2, 0x3a, 0x15, 0x67, 0x08, 0xf7, //0x00006488 .quad -646153205651940552
	0x28, 0x33, 0x04, 0xdc, 0xf1, 0x74, 0x7f, 0x73, //0x00006490 .quad 8322499218531169064
	0x03, 0xc9, 0xa5, 0x44, 0x6d, 0x40, 0x65, 0x9a, //0x00006498 .quad -7321374781173544701
	0xf2, 0x3f, 0x05, 0x53, 0x2e, 0x52, 0x5f, 0x50, //0x000064a0 .quad 5791438004736573426
	0x44, 0x3b, 0xcf, 0x95, 0x88, 0x90, 0xfe, 0xc0, //0x000064a8 .quad -4540032458039542972
	0xef, 0x8f, 0xc6, 0xe7, 0xb9, 0x26, 0x77, 0x64, //0x000064b0 .quad 7239297505920716783
	0x15, 0x0a, 0x43, 0xbb, 0xaa, 0x34, 0x3e, 0xf1, //0x000064b8 .quad -1063354554122040811
	0xf5, 0x19, 0xdc, 0x30, 0x34, 0x78, 0xca, 0x5e, //0x000064c0 .quad 6830403950414141941
	0x4d, 0xe6, 0x09, 0xb5, 0xea, 0xe0, 0xc6, 0x96, //0x000064c8 .quad -7582125623967357363
	0x72, 0x20, 0x13, 0x3d, 0x41, 0x16, 0x7d, 0xb6, //0x000064d0 .quad -5297053117264486286
	0xe0, 0x5f, 0x4c, 0x62, 0x25, 0x99, 0x78, 0xbc, //0x000064d8 .quad -4865971011531808800
	0x8f, 0xe8, 0x57, 0x8c, 0xd1, 0x5b, 0x1c, 0xe4, //0x000064e0 .quad -2009630378153219953
	0xd8, 0x77, 0xdf, 0xba, 0x6e, 0xbf, 0x96, 0xeb, //0x000064e8 .quad -1470777745987373096
	0x59, 0xf1, 0xb6, 0xf7, 0x62, 0xb9, 0x91, 0x8e, //0x000064f0 .quad -8173548013986844327
	0xe7, 0xaa, 0xcb, 0x34, 0xa5, 0x37, 0x3e, 0x93, //0x000064f8 .quad -7836765118883190041
	0xb0, 0xad, 0xa4, 0xb5, 0xbb, 0x27, 0x36, 0x72, //0x00006500 .quad 8229809056225996208
	0xa1, 0x95, 0xfe, 0x81, 0x8e, 0xc5, 0x0d, 0xb8, //0x00006508 .quad -5184270380176599647
	0x1c, 0xd9, 0x0d, 0xa3, 0xaa, 0xb1, 0xc3, 0xce, //0x00006510 .quad -3547796734999668452
	0x09, 0x3b, 0x7e, 0x22, 0xf2, 0x36, 0x11, 0xe6, //0x00006518 .quad -1868651956793361655
	0xb1, 0xa7, 0xe8, 0xa5, 0x0a, 0x4f, 0x3a, 0x21, //0x00006520 .quad 2394313059052595121
	0xe6, 0xe4, 0x8e, 0x55, 0x57, 0xc2, 0xca, 0x8f, //0x00006528 .quad -8085436500636932890
	0x9d, 0xd1, 0x62, 0x4f, 0xcd, 0xe2, 0x88, 0xa9, //0x00006530 .quad -6230480713039031907
	0x1f, 0x9e, 0xf2, 0x2a, 0xed, 0x72, 0xbd, 0xb3, //0x00006538 .quad -5495109607368778209
	0x05, 0x86, 0x3b, 0xa3, 0x80, 0x1b, 0xeb, 0x93, //0x00006540 .quad -7788100891298789883
	0xa7, 0x45, 0xaf, 0x75, 0xa8, 0xcf, 0xac, 0xe0, //0x00006548 .quad -2257200990783584857
	0xc3, 0x33, 0x05, 0x66, 0x30, 0xf1, 0x72, 0xbc, //0x00006550 .quad -4867563057061743677
	0x88, 0x8b, 0x8d, 0x49, 0xc9, 0x01, 0x6c, 0x8c, //0x00006558 .quad -8328279646880822392
	0xb4, 0x80, 0x86, 0x7f, 0x7c, 0xad, 0x8f, 0xeb, //0x00006560 .quad -1472767802899791692
	0x6a, 0xee, 0xf0, 0x9b, 0x3b, 0x02, 0x87, 0xaf, //0x00006568 .quad -5798663540173640086
	0xe1, 0x20, 0x68, 0x9f, 0xdb, 0x98, 0x73, 0xa6, //0x00006570 .quad -6452645772052127519
	0x05, 0x2a, 0xed, 0x82, 0xca, 0xc2, 0x68, 0xdb, //0x00006578 .quad -2636643406789662203
	0x8c, 0x14, 0xa1, 0x43, 0x89, 0x3f, 0x08, 0x88, //0x00006580 .quad -8644589625959967604
	0x43, 0x3a, 0xd4, 0x91, 0xbe, 0x79, 0x21, 0x89, //0x00006588 .quad -8565431156884620733
	0xb0, 0x59, 0x89, 0x94, 0x6b, 0x4f, 0x0a, 0x6a, //0x00006590 .quad 7641007041259592112
	0xd4, 0x48, 0x49, 0x36, 0x2e, 0xd8, 0x69, 0xab, //0x00006598 .quad -6095102927678388012
	0x1c, 0xb0, 0xab, 0x79, 0x46, 0xe3, 0x8c, 0x84, //0x000065a0 .quad -8895485272135061476
	0x09, 0x9b, 0xdb, 0xc3, 0x39, 0x4e, 0x44, 0xd6, //0x000065a8 .quad -3007192641170597111
	0x11, 0x4e, 0x0b, 0x0c, 0x0c, 0x0e, 0xd8, 0xf2, //0x000065b0 .quad -947992276657025519
	0xe5, 0x40, 0x69, 0x1a, 0xe4, 0xb0, 0xea, 0x85, //0x000065b8 .quad -8797024428372705051
	0x95, 0x21, 0x0e, 0x0f, 0x8f, 0x11, 0x8e, 0x6f, //0x000065c0 .quad 8038381691033493909
	0x1f, 0x91, 0x03, 0x21, 0x1d, 0x5d, 0x65, 0xa7, //0x000065c8 .quad -6384594517038493409
	0xfb, 0xa9, 0xd1, 0xd2, 0xf2, 0x95, 0x71, 0x4b, //0x000065d0 .quad 5436291095364479483
	0x67, 0x75, 0x44, 0x69, 0x64, 0xb4, 0x3e, 0xd1, //0x000065d8 .quad -3369057127870728857
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065e0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	//0x000065f0 .p2align 4, 0x00
	//0x000065f0 _POW_TAB
	0x01, 0x00, 0x00, 0x00, //0x000065f0 .long 1
	0x03, 0x00, 0x00, 0x00, //0x000065f4 .long 3
	0x06, 0x00, 0x00, 0x00, //0x000065f8 .long 6
	0x09, 0x00, 0x00, 0x00, //0x000065fc .long 9
	0x0d, 0x00, 0x00, 0x00, //0x00006600 .long 13
	0x10, 0x00, 0x00, 0x00, //0x00006604 .long 16
	0x13, 0x00, 0x00, 0x00, //0x00006608 .long 19
	0x17, 0x00, 0x00, 0x00, //0x0000660c .long 23
	0x1a, 0x00, 0x00, 0x00, //0x00006610 .long 26
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006614 .p2align 4, 0x00
	//0x00006620 _LSHIFT_TAB
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006620 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006630 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006640 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006650 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006660 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006670 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006680 QUAD $0x0000000000000000  // .space 8, '\x00\x00\x00\x00\x00\x00\x00\x00'
	0x01, 0x00, 0x00, 0x00, //0x00006688 .long 1
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000668c QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000669c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000066ec LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x01, 0x00, 0x00, 0x00, //0x000066f0 .long 1
	0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066f4 QUAD $0x0000000000003532; QUAD $0x0000000000000000  // .asciz 16, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006704 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006714 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006724 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006734 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006744 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006754 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x01, 0x00, 0x00, 0x00, //0x00006758 .long 1
	0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000675c QUAD $0x0000000000353231; QUAD $0x0000000000000000  // .asciz 16, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000676c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000677c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000678c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000679c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000067bc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x02, 0x00, 0x00, 0x00, //0x000067c0 .long 2
	0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067c4 QUAD $0x0000000000353236; QUAD $0x0000000000000000  // .asciz 16, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006804 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006814 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006824 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x02, 0x00, 0x00, 0x00, //0x00006828 .long 2
	0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000682c QUAD $0x0000000035323133; QUAD $0x0000000000000000  // .asciz 16, '3125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000683c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000684c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000685c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000686c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000687c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000688c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x02, 0x00, 0x00, 0x00, //0x00006890 .long 2
	0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006894 QUAD $0x0000003532363531; QUAD $0x0000000000000000  // .asciz 16, '15625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000068a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000068b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000068c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000068d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000068e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000068f4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x03, 0x00, 0x00, 0x00, //0x000068f8 .long 3
	0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000068fc QUAD $0x0000003532313837; QUAD $0x0000000000000000  // .asciz 16, '78125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000690c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000691c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000692c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000693c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000694c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000695c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x03, 0x00, 0x00, 0x00, //0x00006960 .long 3
	0x33, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006964 QUAD $0x0000353236303933; QUAD $0x0000000000000000  // .asciz 16, '390625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006974 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006984 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006994 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000069a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000069b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000069c4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x03, 0x00, 0x00, 0x00, //0x000069c8 .long 3
	0x31, 0x39, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000069cc QUAD $0x0035323133353931; QUAD $0x0000000000000000  // .asciz 16, '1953125\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000069dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000069ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000069fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a0c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a1c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006a2c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x00006a30 .long 4
	0x39, 0x37, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a34 QUAD $0x0035323635363739; QUAD $0x0000000000000000  // .asciz 16, '9765625\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a44 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a54 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a64 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a74 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a84 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006a94 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x00006a98 .long 4
	0x34, 0x38, 0x38, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a9c QUAD $0x3532313832383834; QUAD $0x0000000000000000  // .asciz 16, '48828125\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006aac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006abc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006acc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006adc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006aec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006afc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x00006b00 .long 4
	0x32, 0x34, 0x34, 0x31, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b04 QUAD $0x3236303431343432; QUAD $0x0000000000000035  // .asciz 16, '244140625\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b14 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b24 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b34 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b44 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b54 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006b64 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x00006b68 .long 4
	0x31, 0x32, 0x32, 0x30, 0x37, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b6c QUAD $0x3133303730323231; QUAD $0x0000000000003532  // .asciz 16, '1220703125\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b7c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b8c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b9c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006bac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006bbc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006bcc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x05, 0x00, 0x00, 0x00, //0x00006bd0 .long 5
	0x36, 0x31, 0x30, 0x33, 0x35, 0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006bd4 QUAD $0x3635313533303136; QUAD $0x0000000000003532  // .asciz 16, '6103515625\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006be4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006bf4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c04 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c14 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c24 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006c34 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x05, 0x00, 0x00, 0x00, //0x00006c38 .long 5
	0x33, 0x30, 0x35, 0x31, 0x37, 0x35, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c3c QUAD $0x3837353731353033; QUAD $0x0000000000353231  // .asciz 16, '30517578125\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c4c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c5c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c6c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c7c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c8c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006c9c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x05, 0x00, 0x00, 0x00, //0x00006ca0 .long 5
	0x31, 0x35, 0x32, 0x35, 0x38, 0x37, 0x38, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x00006ca4 QUAD $0x3938373835323531; QUAD $0x0000000035323630  // .asciz 16, '152587890625\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006cb4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006cc4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006cd4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ce4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006cf4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006d04 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x06, 0x00, 0x00, 0x00, //0x00006d08 .long 6
	0x37, 0x36, 0x32, 0x39, 0x33, 0x39, 0x34, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x00006d0c QUAD $0x3534393339323637; QUAD $0x0000000035323133  // .asciz 16, '762939453125\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d1c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d2c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d3c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d4c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d5c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006d6c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x06, 0x00, 0x00, 0x00, //0x00006d70 .long 6
	0x33, 0x38, 0x31, 0x34, 0x36, 0x39, 0x37, 0x32, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, //0x00006d74 QUAD $0x3237393634313833; QUAD $0x0000003532363536  // .asciz 16, '3814697265625\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d84 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d94 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006da4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006db4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006dc4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006dd4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x06, 0x00, 0x00, 0x00, //0x00006dd8 .long 6
	0x31, 0x39, 0x30, 0x37, 0x33, 0x34, 0x38, 0x36, 0x33, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, //0x00006ddc QUAD $0x3638343337303931; QUAD $0x0000353231383233  // .asciz 16, '19073486328125\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006dec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006dfc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e0c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e1c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e2c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006e3c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x00006e40 .long 7
	0x39, 0x35, 0x33, 0x36, 0x37, 0x34, 0x33, 0x31, 0x36, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, //0x00006e44 QUAD $0x3133343736333539; QUAD $0x0000353236303436  // .asciz 16, '95367431640625\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e54 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e64 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e74 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e84 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e94 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006ea4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x00006ea8 .long 7
	0x34, 0x37, 0x36, 0x38, 0x33, 0x37, 0x31, 0x35, 0x38, 0x32, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, //0x00006eac QUAD $0x3531373338363734; QUAD $0x0035323133303238  // .asciz 16, '476837158203125\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ebc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ecc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006edc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006eec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006efc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006f0c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x00006f10 .long 7
	0x32, 0x33, 0x38, 0x34, 0x31, 0x38, 0x35, 0x37, 0x39, 0x31, 0x30, 0x31, 0x35, 0x36, 0x32, 0x35, //0x00006f14 QUAD $0x3735383134383332; QUAD $0x3532363531303139  // .asciz 16, '2384185791015625'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f24 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f34 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f44 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f54 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f64 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006f74 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x00006f78 .long 7
	0x31, 0x31, 0x39, 0x32, 0x30, 0x39, 0x32, 0x38, 0x39, 0x35, 0x35, 0x30, 0x37, 0x38, 0x31, 0x32, //0x00006f7c QUAD $0x3832393032393131; QUAD $0x3231383730353539  // .asciz 16, '1192092895507812'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f8c QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f9c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006fac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006fbc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006fcc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006fdc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x08, 0x00, 0x00, 0x00, //0x00006fe0 .long 8
	0x35, 0x39, 0x36, 0x30, 0x34, 0x36, 0x34, 0x34, 0x37, 0x37, 0x35, 0x33, 0x39, 0x30, 0x36, 0x32, //0x00006fe4 QUAD $0x3434363430363935; QUAD $0x3236303933353737  // .asciz 16, '5960464477539062'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ff4 QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007004 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007014 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007024 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007034 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007044 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x08, 0x00, 0x00, 0x00, //0x00007048 .long 8
	0x32, 0x39, 0x38, 0x30, 0x32, 0x33, 0x32, 0x32, 0x33, 0x38, 0x37, 0x36, 0x39, 0x35, 0x33, 0x31, //0x0000704c QUAD $0x3232333230383932; QUAD $0x3133353936373833  // .asciz 16, '2980232238769531'
	0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000705c QUAD $0x0000000000003532; QUAD $0x0000000000000000  // .asciz 16, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000706c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000707c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000708c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000709c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000070ac LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x08, 0x00, 0x00, 0x00, //0x000070b0 .long 8
	0x31, 0x34, 0x39, 0x30, 0x31, 0x31, 0x36, 0x31, 0x31, 0x39, 0x33, 0x38, 0x34, 0x37, 0x36, 0x35, //0x000070b4 QUAD $0x3136313130393431; QUAD $0x3536373438333931  // .asciz 16, '1490116119384765'
	0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000070c4 QUAD $0x0000000000353236; QUAD $0x0000000000000000  // .asciz 16, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000070d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000070e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000070f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007104 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007114 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x09, 0x00, 0x00, 0x00, //0x00007118 .long 9
	0x37, 0x34, 0x35, 0x30, 0x35, 0x38, 0x30, 0x35, 0x39, 0x36, 0x39, 0x32, 0x33, 0x38, 0x32, 0x38, //0x0000711c QUAD $0x3530383530353437; QUAD $0x3832383332393639  // .asciz 16, '7450580596923828'
	0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000712c QUAD $0x0000000000353231; QUAD $0x0000000000000000  // .asciz 16, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000713c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000714c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000715c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000716c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000717c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x09, 0x00, 0x00, 0x00, //0x00007180 .long 9
	0x33, 0x37, 0x32, 0x35, 0x32, 0x39, 0x30, 0x32, 0x39, 0x38, 0x34, 0x36, 0x31, 0x39, 0x31, 0x34, //0x00007184 QUAD $0x3230393235323733; QUAD $0x3431393136343839  // .asciz 16, '3725290298461914'
	0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007194 QUAD $0x0000000035323630; QUAD $0x0000000000000000  // .asciz 16, '0625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000071a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000071b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000071c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000071d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000071e4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x09, 0x00, 0x00, 0x00, //0x000071e8 .long 9
	0x31, 0x38, 0x36, 0x32, 0x36, 0x34, 0x35, 0x31, 0x34, 0x39, 0x32, 0x33, 0x30, 0x39, 0x35, 0x37, //0x000071ec QUAD $0x3135343632363831; QUAD $0x3735393033323934  // .asciz 16, '1862645149230957'
	0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000071fc QUAD $0x0000003532313330; QUAD $0x0000000000000000  // .asciz 16, '03125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000720c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000721c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000722c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000723c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000724c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00007250 .long 10
	0x39, 0x33, 0x31, 0x33, 0x32, 0x32, 0x35, 0x37, 0x34, 0x36, 0x31, 0x35, 0x34, 0x37, 0x38, 0x35, //0x00007254 QUAD $0x3735323233313339; QUAD $0x3538373435313634  // .asciz 16, '9313225746154785'
	0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007264 QUAD $0x0000003532363531; QUAD $0x0000000000000000  // .asciz 16, '15625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007274 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007284 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007294 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000072a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000072b4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x000072b8 .long 10
	0x34, 0x36, 0x35, 0x36, 0x36, 0x31, 0x32, 0x38, 0x37, 0x33, 0x30, 0x37, 0x37, 0x33, 0x39, 0x32, //0x000072bc QUAD $0x3832313636353634; QUAD $0x3239333737303337  // .asciz 16, '4656612873077392'
	0x35, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000072cc QUAD $0x0000353231383735; QUAD $0x0000000000000000  // .asciz 16, '578125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000072dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000072ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000072fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000730c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000731c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00007320 .long 10
	0x32, 0x33, 0x32, 0x38, 0x33, 0x30, 0x36, 0x34, 0x33, 0x36, 0x35, 0x33, 0x38, 0x36, 0x39, 0x36, //0x00007324 QUAD $0x3436303338323332; QUAD $0x3639363833353633  // .asciz 16, '2328306436538696'
	0x32, 0x38, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007334 QUAD $0x0035323630393832; QUAD $0x0000000000000000  // .asciz 16, '2890625\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007344 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007354 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007364 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007374 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007384 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00007388 .long 10
	0x31, 0x31, 0x36, 0x34, 0x31, 0x35, 0x33, 0x32, 0x31, 0x38, 0x32, 0x36, 0x39, 0x33, 0x34, 0x38, //0x0000738c QUAD $0x3233353134363131; QUAD $0x3834333936323831  // .asciz 16, '1164153218269348'
	0x31, 0x34, 0x34, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000739c QUAD $0x3532313335343431; QUAD $0x0000000000000000  // .asciz 16, '14453125\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000073ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000073bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000073cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000073dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000073ec LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0b, 0x00, 0x00, 0x00, //0x000073f0 .long 11
	0x35, 0x38, 0x32, 0x30, 0x37, 0x36, 0x36, 0x30, 0x39, 0x31, 0x33, 0x34, 0x36, 0x37, 0x34, 0x30, //0x000073f4 QUAD $0x3036363730323835; QUAD $0x3034373634333139  // .asciz 16, '5820766091346740'
	0x37, 0x32, 0x32, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007404 QUAD $0x3532363536323237; QUAD $0x0000000000000000  // .asciz 16, '72265625\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007414 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007424 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007434 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007444 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007454 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0b, 0x00, 0x00, 0x00, //0x00007458 .long 11
	0x32, 0x39, 0x31, 0x30, 0x33, 0x38, 0x33, 0x30, 0x34, 0x35, 0x36, 0x37, 0x33, 0x33, 0x37, 0x30, //0x0000745c QUAD $0x3033383330313932; QUAD $0x3037333337363534  // .asciz 16, '2910383045673370'
	0x33, 0x36, 0x31, 0x33, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000746c QUAD $0x3231383233313633; QUAD $0x0000000000000035  // .asciz 16, '361328125\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000747c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000748c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000749c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000074ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000074bc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0b, 0x00, 0x00, 0x00, //0x000074c0 .long 11
	0x31, 0x34, 0x35, 0x35, 0x31, 0x39, 0x31, 0x35, 0x32, 0x32, 0x38, 0x33, 0x36, 0x36, 0x38, 0x35, //0x000074c4 QUAD $0x3531393135353431; QUAD $0x3538363633383232  // .asciz 16, '1455191522836685'
	0x31, 0x38, 0x30, 0x36, 0x36, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000074d4 QUAD $0x3630343636303831; QUAD $0x0000000000003532  // .asciz 16, '1806640625\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000074e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000074f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007504 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007514 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007524 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0c, 0x00, 0x00, 0x00, //0x00007528 .long 12
	0x37, 0x32, 0x37, 0x35, 0x39, 0x35, 0x37, 0x36, 0x31, 0x34, 0x31, 0x38, 0x33, 0x34, 0x32, 0x35, //0x0000752c QUAD $0x3637353935373237; QUAD $0x3532343338313431  // .asciz 16, '7275957614183425'
	0x39, 0x30, 0x33, 0x33, 0x32, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000753c QUAD $0x3133303233333039; QUAD $0x0000000000003532  // .asciz 16, '9033203125\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000754c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000755c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000756c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000757c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000758c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0c, 0x00, 0x00, 0x00, //0x00007590 .long 12
	0x33, 0x36, 0x33, 0x37, 0x39, 0x37, 0x38, 0x38, 0x30, 0x37, 0x30, 0x39, 0x31, 0x37, 0x31, 0x32, //0x00007594 QUAD $0x3838373937333633; QUAD $0x3231373139303730  // .asciz 16, '3637978807091712'
	0x39, 0x35, 0x31, 0x36, 0x36, 0x30, 0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000075a4 QUAD $0x3531303636313539; QUAD $0x0000000000353236  // .asciz 16, '95166015625\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000075b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000075c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000075d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000075e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000075f4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0c, 0x00, 0x00, 0x00, //0x000075f8 .long 12
	0x31, 0x38, 0x31, 0x38, 0x39, 0x38, 0x39, 0x34, 0x30, 0x33, 0x35, 0x34, 0x35, 0x38, 0x35, 0x36, //0x000075fc QUAD $0x3439383938313831; QUAD $0x3635383534353330  // .asciz 16, '1818989403545856'
	0x34, 0x37, 0x35, 0x38, 0x33, 0x30, 0x30, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x0000760c QUAD $0x3730303338353734; QUAD $0x0000000035323138  // .asciz 16, '475830078125\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000761c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000762c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000763c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000764c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000765c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x00007660 .long 13
	0x39, 0x30, 0x39, 0x34, 0x39, 0x34, 0x37, 0x30, 0x31, 0x37, 0x37, 0x32, 0x39, 0x32, 0x38, 0x32, //0x00007664 QUAD $0x3037343934393039; QUAD $0x3238323932373731  // .asciz 16, '9094947017729282'
	0x33, 0x37, 0x39, 0x31, 0x35, 0x30, 0x33, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x00007674 QUAD $0x3933303531393733; QUAD $0x0000000035323630  // .asciz 16, '379150390625\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007684 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007694 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000076a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000076b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000076c4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x000076c8 .long 13
	0x34, 0x35, 0x34, 0x37, 0x34, 0x37, 0x33, 0x35, 0x30, 0x38, 0x38, 0x36, 0x34, 0x36, 0x34, 0x31, //0x000076cc QUAD $0x3533373437343534; QUAD $0x3134363436383830  // .asciz 16, '4547473508864641'
	0x31, 0x38, 0x39, 0x35, 0x37, 0x35, 0x31, 0x39, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, //0x000076dc QUAD $0x3931353735393831; QUAD $0x0000003532313335  // .asciz 16, '1895751953125\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000076ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000076fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000770c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000771c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000772c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x00007730 .long 13
	0x32, 0x32, 0x37, 0x33, 0x37, 0x33, 0x36, 0x37, 0x35, 0x34, 0x34, 0x33, 0x32, 0x33, 0x32, 0x30, //0x00007734 QUAD $0x3736333733373232; QUAD $0x3032333233343435  // .asciz 16, '2273736754432320'
	0x35, 0x39, 0x34, 0x37, 0x38, 0x37, 0x35, 0x39, 0x37, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, //0x00007744 QUAD $0x3935373837343935; QUAD $0x0000353236353637  // .asciz 16, '59478759765625\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007754 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007764 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007774 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007784 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007794 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x00007798 .long 13
	0x31, 0x31, 0x33, 0x36, 0x38, 0x36, 0x38, 0x33, 0x37, 0x37, 0x32, 0x31, 0x36, 0x31, 0x36, 0x30, //0x0000779c QUAD $0x3338363836333131; QUAD $0x3036313631323737  // .asciz 16, '1136868377216160'
	0x32, 0x39, 0x37, 0x33, 0x39, 0x33, 0x37, 0x39, 0x38, 0x38, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, //0x000077ac QUAD $0x3937333933373932; QUAD $0x0035323138323838  // .asciz 16, '297393798828125\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000077bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000077cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000077dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000077ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000077fc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0e, 0x00, 0x00, 0x00, //0x00007800 .long 14
	0x35, 0x36, 0x38, 0x34, 0x33, 0x34, 0x31, 0x38, 0x38, 0x36, 0x30, 0x38, 0x30, 0x38, 0x30, 0x31, //0x00007804 QUAD $0x3831343334383635; QUAD $0x3130383038303638  // .asciz 16, '5684341886080801'
	0x34, 0x38, 0x36, 0x39, 0x36, 0x38, 0x39, 0x39, 0x34, 0x31, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, //0x00007814 QUAD $0x3939383639363834; QUAD $0x0035323630343134  // .asciz 16, '486968994140625\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007824 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007834 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007844 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007854 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007864 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0e, 0x00, 0x00, 0x00, //0x00007868 .long 14
	0x32, 0x38, 0x34, 0x32, 0x31, 0x37, 0x30, 0x39, 0x34, 0x33, 0x30, 0x34, 0x30, 0x34, 0x30, 0x30, //0x0000786c QUAD $0x3930373132343832; QUAD $0x3030343034303334  // .asciz 16, '2842170943040400'
	0x37, 0x34, 0x33, 0x34, 0x38, 0x34, 0x34, 0x39, 0x37, 0x30, 0x37, 0x30, 0x33, 0x31, 0x32, 0x35, //0x0000787c QUAD $0x3934343834333437; QUAD $0x3532313330373037  // .asciz 16, '7434844970703125'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000788c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000789c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000078ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000078bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000078cc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0e, 0x00, 0x00, 0x00, //0x000078d0 .long 14
	0x31, 0x34, 0x32, 0x31, 0x30, 0x38, 0x35, 0x34, 0x37, 0x31, 0x35, 0x32, 0x30, 0x32, 0x30, 0x30, //0x000078d4 QUAD $0x3435383031323431; QUAD $0x3030323032353137  // .asciz 16, '1421085471520200'
	0x33, 0x37, 0x31, 0x37, 0x34, 0x32, 0x32, 0x34, 0x38, 0x35, 0x33, 0x35, 0x31, 0x35, 0x36, 0x32, //0x000078e4 QUAD $0x3432323437313733; QUAD $0x3236353135333538  // .asciz 16, '3717422485351562'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000078f4 QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007904 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007914 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007924 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007934 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0f, 0x00, 0x00, 0x00, //0x00007938 .long 15
	0x37, 0x31, 0x30, 0x35, 0x34, 0x32, 0x37, 0x33, 0x35, 0x37, 0x36, 0x30, 0x31, 0x30, 0x30, 0x31, //0x0000793c QUAD $0x3337323435303137; QUAD $0x3130303130363735  // .asciz 16, '7105427357601001'
	0x38, 0x35, 0x38, 0x37, 0x31, 0x31, 0x32, 0x34, 0x32, 0x36, 0x37, 0x35, 0x37, 0x38, 0x31, 0x32, //0x0000794c QUAD $0x3432313137383538; QUAD $0x3231383735373632  // .asciz 16, '8587112426757812'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000795c QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000796c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000797c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000798c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000799c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0f, 0x00, 0x00, 0x00, //0x000079a0 .long 15
	0x33, 0x35, 0x35, 0x32, 0x37, 0x31, 0x33, 0x36, 0x37, 0x38, 0x38, 0x30, 0x30, 0x35, 0x30, 0x30, //0x000079a4 QUAD $0x3633313732353533; QUAD $0x3030353030383837  // .asciz 16, '3552713678800500'
	0x39, 0x32, 0x39, 0x33, 0x35, 0x35, 0x36, 0x32, 0x31, 0x33, 0x33, 0x37, 0x38, 0x39, 0x30, 0x36, //0x000079b4 QUAD $0x3236353533393239; QUAD $0x3630393837333331  // .asciz 16, '9293556213378906'
	0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000079c4 QUAD $0x0000000000003532; QUAD $0x0000000000000000  // .asciz 16, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000079d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000079e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000079f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007a04 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0f, 0x00, 0x00, 0x00, //0x00007a08 .long 15
	0x31, 0x37, 0x37, 0x36, 0x33, 0x35, 0x36, 0x38, 0x33, 0x39, 0x34, 0x30, 0x30, 0x32, 0x35, 0x30, //0x00007a0c QUAD $0x3836353336373731; QUAD $0x3035323030343933  // .asciz 16, '1776356839400250'
	0x34, 0x36, 0x34, 0x36, 0x37, 0x37, 0x38, 0x31, 0x30, 0x36, 0x36, 0x38, 0x39, 0x34, 0x35, 0x33, //0x00007a1c QUAD $0x3138373736343634; QUAD $0x3335343938363630  // .asciz 16, '4646778106689453'
	0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007a2c QUAD $0x0000000000353231; QUAD $0x0000000000000000  // .asciz 16, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007a3c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007a4c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007a5c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007a6c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x00007a70 .long 16
	0x38, 0x38, 0x38, 0x31, 0x37, 0x38, 0x34, 0x31, 0x39, 0x37, 0x30, 0x30, 0x31, 0x32, 0x35, 0x32, //0x00007a74 QUAD $0x3134383731383838; QUAD $0x3235323130303739  // .asciz 16, '8881784197001252'
	0x33, 0x32, 0x33, 0x33, 0x38, 0x39, 0x30, 0x35, 0x33, 0x33, 0x34, 0x34, 0x37, 0x32, 0x36, 0x35, //0x00007a84 QUAD $0x3530393833333233; QUAD $0x3536323734343333  // .asciz 16, '3233890533447265'
	0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007a94 QUAD $0x0000000000353236; QUAD $0x0000000000000000  // .asciz 16, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007aa4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007ab4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007ac4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007ad4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x00007ad8 .long 16
	0x34, 0x34, 0x34, 0x30, 0x38, 0x39, 0x32, 0x30, 0x39, 0x38, 0x35, 0x30, 0x30, 0x36, 0x32, 0x36, //0x00007adc QUAD $0x3032393830343434; QUAD $0x3632363030353839  // .asciz 16, '4440892098500626'
	0x31, 0x36, 0x31, 0x36, 0x39, 0x34, 0x35, 0x32, 0x36, 0x36, 0x37, 0x32, 0x33, 0x36, 0x33, 0x32, //0x00007aec QUAD $0x3235343936313631; QUAD $0x3233363332373636  // .asciz 16, '1616945266723632'
	0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007afc QUAD $0x0000000035323138; QUAD $0x0000000000000000  // .asciz 16, '8125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007b0c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007b1c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007b2c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007b3c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x00007b40 .long 16
	0x32, 0x32, 0x32, 0x30, 0x34, 0x34, 0x36, 0x30, 0x34, 0x39, 0x32, 0x35, 0x30, 0x33, 0x31, 0x33, //0x00007b44 QUAD $0x3036343430323232; QUAD $0x3331333035323934  // .asciz 16, '2220446049250313'
	0x30, 0x38, 0x30, 0x38, 0x34, 0x37, 0x32, 0x36, 0x33, 0x33, 0x33, 0x36, 0x31, 0x38, 0x31, 0x36, //0x00007b54 QUAD $0x3632373438303830; QUAD $0x3631383136333333  // .asciz 16, '0808472633361816'
	0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007b64 QUAD $0x0000003532363034; QUAD $0x0000000000000000  // .asciz 16, '40625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007b74 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007b84 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007b94 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007ba4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x00007ba8 .long 16
	0x31, 0x31, 0x31, 0x30, 0x32, 0x32, 0x33, 0x30, 0x32, 0x34, 0x36, 0x32, 0x35, 0x31, 0x35, 0x36, //0x00007bac QUAD $0x3033323230313131; QUAD $0x3635313532363432  // .asciz 16, '1110223024625156'
	0x35, 0x34, 0x30, 0x34, 0x32, 0x33, 0x36, 0x33, 0x31, 0x36, 0x36, 0x38, 0x30, 0x39, 0x30, 0x38, //0x00007bbc QUAD $0x3336333234303435; QUAD $0x3830393038363631  // .asciz 16, '5404236316680908'
	0x32, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007bcc QUAD $0x0000353231333032; QUAD $0x0000000000000000  // .asciz 16, '203125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007bdc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007bec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007bfc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007c0c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x11, 0x00, 0x00, 0x00, //0x00007c10 .long 17
	0x35, 0x35, 0x35, 0x31, 0x31, 0x31, 0x35, 0x31, 0x32, 0x33, 0x31, 0x32, 0x35, 0x37, 0x38, 0x32, //0x00007c14 QUAD $0x3135313131353535; QUAD $0x3238373532313332  // .asciz 16, '5551115123125782'
	0x37, 0x30, 0x32, 0x31, 0x31, 0x38, 0x31, 0x35, 0x38, 0x33, 0x34, 0x30, 0x34, 0x35, 0x34, 0x31, //0x00007c24 QUAD $0x3531383131323037; QUAD $0x3134353430343338  // .asciz 16, '7021181583404541'
	0x30, 0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007c34 QUAD $0x0000353236353130; QUAD $0x0000000000000000  // .asciz 16, '015625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007c44 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007c54 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007c64 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007c74 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x11, 0x00, 0x00, 0x00, //0x00007c78 .long 17
	0x32, 0x37, 0x37, 0x35, 0x35, 0x35, 0x37, 0x35, 0x36, 0x31, 0x35, 0x36, 0x32, 0x38, 0x39, 0x31, //0x00007c7c QUAD $0x3537353535373732; QUAD $0x3139383236353136  // .asciz 16, '2775557561562891'
	0x33, 0x35, 0x31, 0x30, 0x35, 0x39, 0x30, 0x37, 0x39, 0x31, 0x37, 0x30, 0x32, 0x32, 0x37, 0x30, //0x00007c8c QUAD $0x3730393530313533; QUAD $0x3037323230373139  // .asciz 16, '3510590791702270'
	0x35, 0x30, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007c9c QUAD $0x0035323138373035; QUAD $0x0000000000000000  // .asciz 16, '5078125\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007cac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007cbc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007ccc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007cdc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x11, 0x00, 0x00, 0x00, //0x00007ce0 .long 17
	0x31, 0x33, 0x38, 0x37, 0x37, 0x37, 0x38, 0x37, 0x38, 0x30, 0x37, 0x38, 0x31, 0x34, 0x34, 0x35, //0x00007ce4 QUAD $0x3738373737383331; QUAD $0x3534343138373038  // .asciz 16, '1387778780781445'
	0x36, 0x37, 0x35, 0x35, 0x32, 0x39, 0x35, 0x33, 0x39, 0x35, 0x38, 0x35, 0x31, 0x31, 0x33, 0x35, //0x00007cf4 QUAD $0x3335393235353736; QUAD $0x3533313135383539  // .asciz 16, '6755295395851135'
	0x32, 0x35, 0x33, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007d04 QUAD $0x3532363039333532; QUAD $0x0000000000000000  // .asciz 16, '25390625\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007d14 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007d24 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007d34 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007d44 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x12, 0x00, 0x00, 0x00, //0x00007d48 .long 18
	0x36, 0x39, 0x33, 0x38, 0x38, 0x39, 0x33, 0x39, 0x30, 0x33, 0x39, 0x30, 0x37, 0x32, 0x32, 0x38, //0x00007d4c QUAD $0x3933393838333936; QUAD $0x3832323730393330  // .asciz 16, '6938893903907228'
	0x33, 0x37, 0x37, 0x36, 0x34, 0x37, 0x36, 0x39, 0x37, 0x39, 0x32, 0x35, 0x35, 0x36, 0x37, 0x36, //0x00007d5c QUAD $0x3936373436373733; QUAD $0x3637363535323937  // .asciz 16, '3776476979255676'
	0x32, 0x36, 0x39, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007d6c QUAD $0x3532313335393632; QUAD $0x0000000000000000  // .asciz 16, '26953125\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007d7c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007d8c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007d9c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007dac LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x12, 0x00, 0x00, 0x00, //0x00007db0 .long 18
	0x33, 0x34, 0x36, 0x39, 0x34, 0x34, 0x36, 0x39, 0x35, 0x31, 0x39, 0x35, 0x33, 0x36, 0x31, 0x34, //0x00007db4 QUAD $0x3936343439363433; QUAD $0x3431363335393135  // .asciz 16, '3469446951953614'
	0x31, 0x38, 0x38, 0x38, 0x32, 0x33, 0x38, 0x34, 0x38, 0x39, 0x36, 0x32, 0x37, 0x38, 0x33, 0x38, //0x00007dc4 QUAD $0x3438333238383831; QUAD $0x3833383732363938  // .asciz 16, '1888238489627838'
	0x31, 0x33, 0x34, 0x37, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007dd4 QUAD $0x3236353637343331; QUAD $0x0000000000000035  // .asciz 16, '134765625\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007de4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007df4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007e04 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007e14 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x12, 0x00, 0x00, 0x00, //0x00007e18 .long 18
	0x31, 0x37, 0x33, 0x34, 0x37, 0x32, 0x33, 0x34, 0x37, 0x35, 0x39, 0x37, 0x36, 0x38, 0x30, 0x37, //0x00007e1c QUAD $0x3433323734333731; QUAD $0x3730383637393537  // .asciz 16, '1734723475976807'
	0x30, 0x39, 0x34, 0x34, 0x31, 0x31, 0x39, 0x32, 0x34, 0x34, 0x38, 0x31, 0x33, 0x39, 0x31, 0x39, //0x00007e2c QUAD $0x3239313134343930; QUAD $0x3931393331383434  // .asciz 16, '0944119244813919'
	0x30, 0x36, 0x37, 0x33, 0x38, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007e3c QUAD $0x3138323833373630; QUAD $0x0000000000003532  // .asciz 16, '0673828125\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007e4c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007e5c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007e6c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007e7c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x13, 0x00, 0x00, 0x00, //0x00007e80 .long 19
	0x38, 0x36, 0x37, 0x33, 0x36, 0x31, 0x37, 0x33, 0x37, 0x39, 0x38, 0x38, 0x34, 0x30, 0x33, 0x35, //0x00007e84 QUAD $0x3337313633373638; QUAD $0x3533303438383937  // .asciz 16, '8673617379884035'
	0x34, 0x37, 0x32, 0x30, 0x35, 0x39, 0x36, 0x32, 0x32, 0x34, 0x30, 0x36, 0x39, 0x35, 0x39, 0x35, //0x00007e94 QUAD $0x3236393530323734; QUAD $0x3539353936303432  // .asciz 16, '4720596224069595'
	0x33, 0x33, 0x36, 0x39, 0x31, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007ea4 QUAD $0x3630343139363333; QUAD $0x0000000000003532  // .asciz 16, '3369140625\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007eb4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007ec4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007ed4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007ee4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
}
 
