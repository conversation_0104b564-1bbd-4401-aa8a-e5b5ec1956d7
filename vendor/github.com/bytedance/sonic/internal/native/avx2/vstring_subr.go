// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx2

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__vstring = 96
)

const (
    _stack__vstring = 88
)

const (
    _size__vstring = 2056
)

var (
    _pcsp__vstring = [][2]uint32{
        {1, 0},
        {4, 8},
        {6, 16},
        {8, 24},
        {10, 32},
        {12, 40},
        {13, 48},
        {1899, 88},
        {1903, 48},
        {1904, 40},
        {1906, 32},
        {1908, 24},
        {1910, 16},
        {1912, 8},
        {1916, 0},
        {2055, 88},
    }
)

var _cfunc_vstring = []loader.CFunc{
    {"_vstring_entry", 0,  _entry__vstring, 0, nil},
    {"_vstring", _entry__vstring, _size__vstring, _stack__vstring, _pcsp__vstring},
}
