// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_validate_utf8_fast = []byte{
	// .p2align 5, 0x00
	// LCPI0_0
	0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, // QUAD $0x0f0f0f0f0f0f0f0f; QUAD $0x0f0f0f0f0f0f0f0f  // .space 16, '\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f'
	0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000010 QUAD $0x0f0f0f0f0f0f0f0f; QUAD $0x0f0f0f0f0f0f0f0f  // .space 16, '\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f'
	//0x00000020 LCPI0_1
	0x02, //0x00000020 .byte 2
	0x02, //0x00000021 .byte 2
	0x02, //0x00000022 .byte 2
	0x02, //0x00000023 .byte 2
	0x02, //0x00000024 .byte 2
	0x02, //0x00000025 .byte 2
	0x02, //0x00000026 .byte 2
	0x02, //0x00000027 .byte 2
	0x80, //0x00000028 .byte 128
	0x80, //0x00000029 .byte 128
	0x80, //0x0000002a .byte 128
	0x80, //0x0000002b .byte 128
	0x21, //0x0000002c .byte 33
	0x01, //0x0000002d .byte 1
	0x15, //0x0000002e .byte 21
	0x49, //0x0000002f .byte 73
	0x02, //0x00000030 .byte 2
	0x02, //0x00000031 .byte 2
	0x02, //0x00000032 .byte 2
	0x02, //0x00000033 .byte 2
	0x02, //0x00000034 .byte 2
	0x02, //0x00000035 .byte 2
	0x02, //0x00000036 .byte 2
	0x02, //0x00000037 .byte 2
	0x80, //0x00000038 .byte 128
	0x80, //0x00000039 .byte 128
	0x80, //0x0000003a .byte 128
	0x80, //0x0000003b .byte 128
	0x21, //0x0000003c .byte 33
	0x01, //0x0000003d .byte 1
	0x15, //0x0000003e .byte 21
	0x49, //0x0000003f .byte 73
	//0x00000040 LCPI0_2
	0xe7, //0x00000040 .byte 231
	0xa3, //0x00000041 .byte 163
	0x83, //0x00000042 .byte 131
	0x83, //0x00000043 .byte 131
	0x8b, //0x00000044 .byte 139
	0xcb, //0x00000045 .byte 203
	0xcb, //0x00000046 .byte 203
	0xcb, //0x00000047 .byte 203
	0xcb, //0x00000048 .byte 203
	0xcb, //0x00000049 .byte 203
	0xcb, //0x0000004a .byte 203
	0xcb, //0x0000004b .byte 203
	0xcb, //0x0000004c .byte 203
	0xdb, //0x0000004d .byte 219
	0xcb, //0x0000004e .byte 203
	0xcb, //0x0000004f .byte 203
	0xe7, //0x00000050 .byte 231
	0xa3, //0x00000051 .byte 163
	0x83, //0x00000052 .byte 131
	0x83, //0x00000053 .byte 131
	0x8b, //0x00000054 .byte 139
	0xcb, //0x00000055 .byte 203
	0xcb, //0x00000056 .byte 203
	0xcb, //0x00000057 .byte 203
	0xcb, //0x00000058 .byte 203
	0xcb, //0x00000059 .byte 203
	0xcb, //0x0000005a .byte 203
	0xcb, //0x0000005b .byte 203
	0xcb, //0x0000005c .byte 203
	0xdb, //0x0000005d .byte 219
	0xcb, //0x0000005e .byte 203
	0xcb, //0x0000005f .byte 203
	//0x00000060 LCPI0_3
	0x01, //0x00000060 .byte 1
	0x01, //0x00000061 .byte 1
	0x01, //0x00000062 .byte 1
	0x01, //0x00000063 .byte 1
	0x01, //0x00000064 .byte 1
	0x01, //0x00000065 .byte 1
	0x01, //0x00000066 .byte 1
	0x01, //0x00000067 .byte 1
	0xe6, //0x00000068 .byte 230
	0xae, //0x00000069 .byte 174
	0xba, //0x0000006a .byte 186
	0xba, //0x0000006b .byte 186
	0x01, //0x0000006c .byte 1
	0x01, //0x0000006d .byte 1
	0x01, //0x0000006e .byte 1
	0x01, //0x0000006f .byte 1
	0x01, //0x00000070 .byte 1
	0x01, //0x00000071 .byte 1
	0x01, //0x00000072 .byte 1
	0x01, //0x00000073 .byte 1
	0x01, //0x00000074 .byte 1
	0x01, //0x00000075 .byte 1
	0x01, //0x00000076 .byte 1
	0x01, //0x00000077 .byte 1
	0xe6, //0x00000078 .byte 230
	0xae, //0x00000079 .byte 174
	0xba, //0x0000007a .byte 186
	0xba, //0x0000007b .byte 186
	0x01, //0x0000007c .byte 1
	0x01, //0x0000007d .byte 1
	0x01, //0x0000007e .byte 1
	0x01, //0x0000007f .byte 1
	//0x00000080 LCPI0_4
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000080 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000090 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x000000a0 LCPI0_5
	0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, //0x000000a0 QUAD $0xefefefefefefefef; QUAD $0xefefefefefefefef  // .space 16, '\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef'
	0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, //0x000000b0 QUAD $0xefefefefefefefef; QUAD $0xefefefefefefefef  // .space 16, '\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef'
	//0x000000c0 LCPI0_7
	0xff, //0x000000c0 .byte 255
	0xff, //0x000000c1 .byte 255
	0xff, //0x000000c2 .byte 255
	0xff, //0x000000c3 .byte 255
	0xff, //0x000000c4 .byte 255
	0xff, //0x000000c5 .byte 255
	0xff, //0x000000c6 .byte 255
	0xff, //0x000000c7 .byte 255
	0xff, //0x000000c8 .byte 255
	0xff, //0x000000c9 .byte 255
	0xff, //0x000000ca .byte 255
	0xff, //0x000000cb .byte 255
	0xff, //0x000000cc .byte 255
	0xff, //0x000000cd .byte 255
	0xff, //0x000000ce .byte 255
	0xff, //0x000000cf .byte 255
	0xff, //0x000000d0 .byte 255
	0xff, //0x000000d1 .byte 255
	0xff, //0x000000d2 .byte 255
	0xff, //0x000000d3 .byte 255
	0xff, //0x000000d4 .byte 255
	0xff, //0x000000d5 .byte 255
	0xff, //0x000000d6 .byte 255
	0xff, //0x000000d7 .byte 255
	0xff, //0x000000d8 .byte 255
	0xff, //0x000000d9 .byte 255
	0xff, //0x000000da .byte 255
	0xff, //0x000000db .byte 255
	0xff, //0x000000dc .byte 255
	0xef, //0x000000dd .byte 239
	0xdf, //0x000000de .byte 223
	0xbf, //0x000000df .byte 191
	//0x000000e0 LCPI0_8
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000000e0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000000f0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	//0x00000100 .p2align 3, 0x00
	//0x00000100 LCPI0_6
	0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, //0x00000100 .quad -9187201950435737472
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000108 .p2align 4, 0x90
	//0x00000110 _validate_utf8_fast
	0x55, //0x00000110 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000111 movq         %rsp, %rbp
	0x53, //0x00000114 pushq        %rbx
	0x48, 0x81, 0xec, 0xa0, 0x00, 0x00, 0x00, //0x00000115 subq         $160, %rsp
	0x48, 0x8b, 0x47, 0x08, //0x0000011c movq         $8(%rdi), %rax
	0x48, 0x85, 0xc0, //0x00000120 testq        %rax, %rax
	0x0f, 0x84, 0xc3, 0x07, 0x00, 0x00, //0x00000123 je           LBB0_12
	0x4c, 0x8b, 0x07, //0x00000129 movq         (%rdi), %r8
	0x4d, 0x8d, 0x0c, 0x00, //0x0000012c leaq         (%r8,%rax), %r9
	0x49, 0x8d, 0x79, 0x80, //0x00000130 leaq         $-128(%r9), %rdi
	0xc5, 0xf1, 0xef, 0xc9, //0x00000134 vpxor        %xmm1, %xmm1, %xmm1
	0xc5, 0xe9, 0xef, 0xd2, //0x00000138 vpxor        %xmm2, %xmm2, %xmm2
	0xc5, 0xf9, 0xef, 0xc0, //0x0000013c vpxor        %xmm0, %xmm0, %xmm0
	0x4c, 0x89, 0xc2, //0x00000140 movq         %r8, %rdx
	0x4c, 0x39, 0xc7, //0x00000143 cmpq         %r8, %rdi
	0x0f, 0x86, 0x70, 0x03, 0x00, 0x00, //0x00000146 jbe          LBB0_14
	0x48, 0x8d, 0x50, 0xff, //0x0000014c leaq         $-1(%rax), %rdx
	0xc5, 0xfe, 0x6f, 0x25, 0xa8, 0xfe, 0xff, 0xff, //0x00000150 vmovdqu      $-344(%rip), %ymm4  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0xe0, 0xfe, 0xff, 0xff, //0x00000158 vmovdqu      $-288(%rip), %ymm6  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0xf8, 0xfe, 0xff, 0xff, //0x00000160 vmovdqu      $-264(%rip), %ymm7  /* LCPI0_3+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x10, 0xff, 0xff, 0xff, //0x00000168 vmovdqu      $-240(%rip), %ymm8  /* LCPI0_4+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0x28, 0xff, 0xff, 0xff, //0x00000170 vmovdqu      $-216(%rip), %ymm9  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0x40, 0xff, 0xff, 0xff, //0x00000178 vmovdqu      $-192(%rip), %ymm10  /* LCPI0_7+0(%rip) */
	0x4c, 0x89, 0xc1, //0x00000180 movq         %r8, %rcx
	0xc5, 0xf9, 0xef, 0xc0, //0x00000183 vpxor        %xmm0, %xmm0, %xmm0
	0xc5, 0xe9, 0xef, 0xd2, //0x00000187 vpxor        %xmm2, %xmm2, %xmm2
	0xc5, 0xf1, 0xef, 0xc9, //0x0000018b vpxor        %xmm1, %xmm1, %xmm1
	0x90, //0x0000018f .p2align 4, 0x90
	//0x00000190 LBB0_3
	0xc5, 0x7e, 0x6f, 0x39, //0x00000190 vmovdqu      (%rcx), %ymm15
	0xc5, 0x7e, 0x6f, 0x69, 0x20, //0x00000194 vmovdqu      $32(%rcx), %ymm13
	0xc5, 0x7e, 0x6f, 0x61, 0x40, //0x00000199 vmovdqu      $64(%rcx), %ymm12
	0xc5, 0x7e, 0x6f, 0x59, 0x60, //0x0000019e vmovdqu      $96(%rcx), %ymm11
	0xc4, 0xc1, 0x15, 0xeb, 0xdf, //0x000001a3 vpor         %ymm15, %ymm13, %ymm3
	0xc4, 0x41, 0x25, 0xeb, 0xf4, //0x000001a8 vpor         %ymm12, %ymm11, %ymm14
	0xc5, 0x8d, 0xeb, 0xeb, //0x000001ad vpor         %ymm3, %ymm14, %ymm5
	0xc5, 0xfd, 0xd7, 0xf5, //0x000001b1 vpmovmskb    %ymm5, %esi
	0x85, 0xf6, //0x000001b5 testl        %esi, %esi
	0x0f, 0x85, 0x16, 0x00, 0x00, 0x00, //0x000001b7 jne          LBB0_6
	0xc5, 0xf5, 0xeb, 0xc0, //0x000001bd vpor         %ymm0, %ymm1, %ymm0
	//0x000001c1 LBB0_5
	0x48, 0x83, 0xe9, 0x80, //0x000001c1 subq         $-128, %rcx
	0x48, 0x39, 0xf9, //0x000001c5 cmpq         %rdi, %rcx
	0x0f, 0x82, 0xc2, 0xff, 0xff, 0xff, //0x000001c8 jb           LBB0_3
	0xe9, 0xe2, 0x02, 0x00, 0x00, //0x000001ce jmp          LBB0_13
	//0x000001d3 LBB0_6
	0xc5, 0xfd, 0xd7, 0xf3, //0x000001d3 vpmovmskb    %ymm3, %esi
	0x85, 0xf6, //0x000001d7 testl        %esi, %esi
	0x0f, 0x85, 0xf0, 0x00, 0x00, 0x00, //0x000001d9 jne          LBB0_9
	0xc5, 0xf5, 0xeb, 0xc0, //0x000001df vpor         %ymm0, %ymm1, %ymm0
	0xc4, 0xc3, 0x6d, 0x46, 0xcc, 0x21, //0x000001e3 vperm2i128   $33, %ymm12, %ymm2, %ymm1
	0xc4, 0xe3, 0x1d, 0x0f, 0xd1, 0x0f, //0x000001e9 vpalignr     $15, %ymm1, %ymm12, %ymm2
	0xc5, 0xe5, 0x71, 0xd2, 0x04, //0x000001ef vpsrlw       $4, %ymm2, %ymm3
	0xc5, 0xe5, 0xdb, 0xdc, //0x000001f4 vpand        %ymm4, %ymm3, %ymm3
	0xc5, 0x7e, 0x6f, 0x35, 0x20, 0xfe, 0xff, 0xff, //0x000001f8 vmovdqu      $-480(%rip), %ymm14  /* LCPI0_1+0(%rip) */
	0xc4, 0xe2, 0x0d, 0x00, 0xdb, //0x00000200 vpshufb      %ymm3, %ymm14, %ymm3
	0xc5, 0xed, 0xdb, 0xd4, //0x00000205 vpand        %ymm4, %ymm2, %ymm2
	0xc4, 0xe2, 0x4d, 0x00, 0xd2, //0x00000209 vpshufb      %ymm2, %ymm6, %ymm2
	0xc4, 0xc1, 0x55, 0x71, 0xd4, 0x04, //0x0000020e vpsrlw       $4, %ymm12, %ymm5
	0xc5, 0xd5, 0xdb, 0xec, //0x00000214 vpand        %ymm4, %ymm5, %ymm5
	0xc4, 0xe2, 0x45, 0x00, 0xed, //0x00000218 vpshufb      %ymm5, %ymm7, %ymm5
	0xc5, 0xed, 0xdb, 0xd5, //0x0000021d vpand        %ymm5, %ymm2, %ymm2
	0xc5, 0xe5, 0xdb, 0xd2, //0x00000221 vpand        %ymm2, %ymm3, %ymm2
	0xc4, 0xe3, 0x1d, 0x0f, 0xd9, 0x0e, //0x00000225 vpalignr     $14, %ymm1, %ymm12, %ymm3
	0xc4, 0xe3, 0x1d, 0x0f, 0xc9, 0x0d, //0x0000022b vpalignr     $13, %ymm1, %ymm12, %ymm1
	0xc4, 0xc1, 0x65, 0xd8, 0xd8, //0x00000231 vpsubusb     %ymm8, %ymm3, %ymm3
	0xc4, 0xc1, 0x75, 0xd8, 0xc9, //0x00000236 vpsubusb     %ymm9, %ymm1, %ymm1
	0xc5, 0xf5, 0xeb, 0xcb, //0x0000023b vpor         %ymm3, %ymm1, %ymm1
	0xc4, 0x41, 0x11, 0xef, 0xed, //0x0000023f vpxor        %xmm13, %xmm13, %xmm13
	0xc5, 0x95, 0x74, 0xc9, //0x00000244 vpcmpeqb     %ymm1, %ymm13, %ymm1
	0xc4, 0xe2, 0x7d, 0x59, 0x1d, 0xaf, 0xfe, 0xff, 0xff, //0x00000248 vpbroadcastq $-337(%rip), %ymm3  /* LCPI0_6+0(%rip) */
	0xc5, 0xf5, 0xdf, 0xcb, //0x00000251 vpandn       %ymm3, %ymm1, %ymm1
	0xc5, 0xf5, 0xef, 0xca, //0x00000255 vpxor        %ymm2, %ymm1, %ymm1
	0xc5, 0xfd, 0xeb, 0xc1, //0x00000259 vpor         %ymm1, %ymm0, %ymm0
	0xc4, 0xc3, 0x1d, 0x46, 0xcb, 0x21, //0x0000025d vperm2i128   $33, %ymm11, %ymm12, %ymm1
	0xc4, 0xe3, 0x25, 0x0f, 0xd1, 0x0f, //0x00000263 vpalignr     $15, %ymm1, %ymm11, %ymm2
	0xc5, 0xd5, 0x71, 0xd2, 0x04, //0x00000269 vpsrlw       $4, %ymm2, %ymm5
	0xc5, 0xd5, 0xdb, 0xec, //0x0000026e vpand        %ymm4, %ymm5, %ymm5
	0xc4, 0xe2, 0x0d, 0x00, 0xed, //0x00000272 vpshufb      %ymm5, %ymm14, %ymm5
	0xc5, 0xed, 0xdb, 0xd4, //0x00000277 vpand        %ymm4, %ymm2, %ymm2
	0xc4, 0xe2, 0x4d, 0x00, 0xd2, //0x0000027b vpshufb      %ymm2, %ymm6, %ymm2
	0xc4, 0xc1, 0x1d, 0x71, 0xd3, 0x04, //0x00000280 vpsrlw       $4, %ymm11, %ymm12
	0xc5, 0x1d, 0xdb, 0xe4, //0x00000286 vpand        %ymm4, %ymm12, %ymm12
	0xc4, 0x42, 0x45, 0x00, 0xe4, //0x0000028a vpshufb      %ymm12, %ymm7, %ymm12
	0xc5, 0x9d, 0xdb, 0xd2, //0x0000028f vpand        %ymm2, %ymm12, %ymm2
	0xc5, 0xd5, 0xdb, 0xd2, //0x00000293 vpand        %ymm2, %ymm5, %ymm2
	0xc4, 0xe3, 0x25, 0x0f, 0xe9, 0x0e, //0x00000297 vpalignr     $14, %ymm1, %ymm11, %ymm5
	0xc4, 0xe3, 0x25, 0x0f, 0xc9, 0x0d, //0x0000029d vpalignr     $13, %ymm1, %ymm11, %ymm1
	0xc4, 0xc1, 0x55, 0xd8, 0xe8, //0x000002a3 vpsubusb     %ymm8, %ymm5, %ymm5
	0xc4, 0xc1, 0x75, 0xd8, 0xc9, //0x000002a8 vpsubusb     %ymm9, %ymm1, %ymm1
	0xc5, 0xf5, 0xeb, 0xcd, //0x000002ad vpor         %ymm5, %ymm1, %ymm1
	0xc5, 0x95, 0x74, 0xc9, //0x000002b1 vpcmpeqb     %ymm1, %ymm13, %ymm1
	0xc5, 0xf5, 0xdf, 0xcb, //0x000002b5 vpandn       %ymm3, %ymm1, %ymm1
	0xc5, 0xf5, 0xef, 0xca, //0x000002b9 vpxor        %ymm2, %ymm1, %ymm1
	//0x000002bd LBB0_8
	0xc5, 0xfd, 0xeb, 0xc1, //0x000002bd vpor         %ymm1, %ymm0, %ymm0
	0xc4, 0xc1, 0x25, 0xd8, 0xca, //0x000002c1 vpsubusb     %ymm10, %ymm11, %ymm1
	0xc5, 0x7d, 0x7f, 0xda, //0x000002c6 vmovdqa      %ymm11, %ymm2
	0xe9, 0xf2, 0xfe, 0xff, 0xff, //0x000002ca jmp          LBB0_5
	//0x000002cf LBB0_9
	0xc4, 0xc3, 0x6d, 0x46, 0xcf, 0x21, //0x000002cf vperm2i128   $33, %ymm15, %ymm2, %ymm1
	0xc4, 0xe3, 0x05, 0x0f, 0xd1, 0x0f, //0x000002d5 vpalignr     $15, %ymm1, %ymm15, %ymm2
	0xc5, 0xe5, 0x71, 0xd2, 0x04, //0x000002db vpsrlw       $4, %ymm2, %ymm3
	0xc5, 0xe5, 0xdb, 0xdc, //0x000002e0 vpand        %ymm4, %ymm3, %ymm3
	0xc5, 0xfe, 0x6f, 0x2d, 0x34, 0xfd, 0xff, 0xff, //0x000002e4 vmovdqu      $-716(%rip), %ymm5  /* LCPI0_1+0(%rip) */
	0xc4, 0xe2, 0x55, 0x00, 0xdb, //0x000002ec vpshufb      %ymm3, %ymm5, %ymm3
	0xc5, 0xed, 0xdb, 0xd4, //0x000002f1 vpand        %ymm4, %ymm2, %ymm2
	0xc4, 0xe2, 0x4d, 0x00, 0xd2, //0x000002f5 vpshufb      %ymm2, %ymm6, %ymm2
	0xc4, 0xc1, 0x55, 0x71, 0xd7, 0x04, //0x000002fa vpsrlw       $4, %ymm15, %ymm5
	0xc5, 0xd5, 0xdb, 0xec, //0x00000300 vpand        %ymm4, %ymm5, %ymm5
	0xc4, 0xe2, 0x45, 0x00, 0xed, //0x00000304 vpshufb      %ymm5, %ymm7, %ymm5
	0xc5, 0xed, 0xdb, 0xd5, //0x00000309 vpand        %ymm5, %ymm2, %ymm2
	0xc5, 0xe5, 0xdb, 0xd2, //0x0000030d vpand        %ymm2, %ymm3, %ymm2
	0xc4, 0xe3, 0x05, 0x0f, 0xd9, 0x0e, //0x00000311 vpalignr     $14, %ymm1, %ymm15, %ymm3
	0xc4, 0xe3, 0x05, 0x0f, 0xc9, 0x0d, //0x00000317 vpalignr     $13, %ymm1, %ymm15, %ymm1
	0xc4, 0xc1, 0x65, 0xd8, 0xd8, //0x0000031d vpsubusb     %ymm8, %ymm3, %ymm3
	0xc4, 0xc1, 0x75, 0xd8, 0xc9, //0x00000322 vpsubusb     %ymm9, %ymm1, %ymm1
	0xc5, 0xf5, 0xeb, 0xcb, //0x00000327 vpor         %ymm3, %ymm1, %ymm1
	0xc5, 0xe1, 0xef, 0xdb, //0x0000032b vpxor        %xmm3, %xmm3, %xmm3
	0xc5, 0xf5, 0x74, 0xdb, //0x0000032f vpcmpeqb     %ymm3, %ymm1, %ymm3
	0xc4, 0xe2, 0x7d, 0x59, 0x0d, 0xc4, 0xfd, 0xff, 0xff, //0x00000333 vpbroadcastq $-572(%rip), %ymm1  /* LCPI0_6+0(%rip) */
	0xc5, 0xe5, 0xdf, 0xd9, //0x0000033c vpandn       %ymm1, %ymm3, %ymm3
	0xc5, 0xe5, 0xef, 0xd2, //0x00000340 vpxor        %ymm2, %ymm3, %ymm2
	0xc5, 0xfe, 0x7f, 0x54, 0x24, 0x60, //0x00000344 vmovdqu      %ymm2, $96(%rsp)
	0xc4, 0xc3, 0x05, 0x46, 0xdd, 0x21, //0x0000034a vperm2i128   $33, %ymm13, %ymm15, %ymm3
	0xc4, 0xe3, 0x15, 0x0f, 0xeb, 0x0f, //0x00000350 vpalignr     $15, %ymm3, %ymm13, %ymm5
	0xc5, 0x85, 0x71, 0xd5, 0x04, //0x00000356 vpsrlw       $4, %ymm5, %ymm15
	0xc5, 0x05, 0xdb, 0xfc, //0x0000035b vpand        %ymm4, %ymm15, %ymm15
	0xc5, 0xfe, 0x6f, 0x15, 0xb9, 0xfc, 0xff, 0xff, //0x0000035f vmovdqu      $-839(%rip), %ymm2  /* LCPI0_1+0(%rip) */
	0xc4, 0x42, 0x6d, 0x00, 0xff, //0x00000367 vpshufb      %ymm15, %ymm2, %ymm15
	0xc5, 0xd5, 0xdb, 0xec, //0x0000036c vpand        %ymm4, %ymm5, %ymm5
	0xc4, 0xe2, 0x4d, 0x00, 0xed, //0x00000370 vpshufb      %ymm5, %ymm6, %ymm5
	0xc4, 0xc1, 0x6d, 0x71, 0xd5, 0x04, //0x00000375 vpsrlw       $4, %ymm13, %ymm2
	0xc5, 0xed, 0xdb, 0xd4, //0x0000037b vpand        %ymm4, %ymm2, %ymm2
	0xc4, 0xe2, 0x45, 0x00, 0xd2, //0x0000037f vpshufb      %ymm2, %ymm7, %ymm2
	0xc5, 0xd5, 0xdb, 0xd2, //0x00000384 vpand        %ymm2, %ymm5, %ymm2
	0xc5, 0x85, 0xdb, 0xd2, //0x00000388 vpand        %ymm2, %ymm15, %ymm2
	0xc4, 0xe3, 0x15, 0x0f, 0xeb, 0x0e, //0x0000038c vpalignr     $14, %ymm3, %ymm13, %ymm5
	0xc4, 0xe3, 0x15, 0x0f, 0xdb, 0x0d, //0x00000392 vpalignr     $13, %ymm3, %ymm13, %ymm3
	0xc4, 0xc1, 0x55, 0xd8, 0xe8, //0x00000398 vpsubusb     %ymm8, %ymm5, %ymm5
	0xc4, 0xc1, 0x65, 0xd8, 0xd9, //0x0000039d vpsubusb     %ymm9, %ymm3, %ymm3
	0xc5, 0xe5, 0xeb, 0xdd, //0x000003a2 vpor         %ymm5, %ymm3, %ymm3
	0xc5, 0xe5, 0x74, 0x1d, 0x32, 0xfd, 0xff, 0xff, //0x000003a6 vpcmpeqb     $-718(%rip), %ymm3, %ymm3  /* LCPI0_8+0(%rip) */
	0xc5, 0xe5, 0xdf, 0xd9, //0x000003ae vpandn       %ymm1, %ymm3, %ymm3
	0xc5, 0xe5, 0xef, 0xd2, //0x000003b2 vpxor        %ymm2, %ymm3, %ymm2
	0xc5, 0xfd, 0xeb, 0x44, 0x24, 0x60, //0x000003b6 vpor         $96(%rsp), %ymm0, %ymm0
	0xc5, 0xfd, 0xeb, 0xc2, //0x000003bc vpor         %ymm2, %ymm0, %ymm0
	0xc4, 0xc1, 0x7d, 0xd7, 0xf6, //0x000003c0 vpmovmskb    %ymm14, %esi
	0x85, 0xf6, //0x000003c5 testl        %esi, %esi
	0x0f, 0x84, 0xd6, 0x00, 0x00, 0x00, //0x000003c7 je           LBB0_11
	0xc4, 0xc3, 0x15, 0x46, 0xd4, 0x21, //0x000003cd vperm2i128   $33, %ymm12, %ymm13, %ymm2
	0xc4, 0xe3, 0x1d, 0x0f, 0xda, 0x0f, //0x000003d3 vpalignr     $15, %ymm2, %ymm12, %ymm3
	0xc5, 0xd5, 0x71, 0xd3, 0x04, //0x000003d9 vpsrlw       $4, %ymm3, %ymm5
	0xc5, 0xd5, 0xdb, 0xec, //0x000003de vpand        %ymm4, %ymm5, %ymm5
	0xc5, 0x7e, 0x6f, 0x3d, 0x36, 0xfc, 0xff, 0xff, //0x000003e2 vmovdqu      $-970(%rip), %ymm15  /* LCPI0_1+0(%rip) */
	0xc4, 0xe2, 0x05, 0x00, 0xed, //0x000003ea vpshufb      %ymm5, %ymm15, %ymm5
	0xc5, 0xe5, 0xdb, 0xdc, //0x000003ef vpand        %ymm4, %ymm3, %ymm3
	0xc4, 0xe2, 0x4d, 0x00, 0xdb, //0x000003f3 vpshufb      %ymm3, %ymm6, %ymm3
	0xc4, 0xc1, 0x15, 0x71, 0xd4, 0x04, //0x000003f8 vpsrlw       $4, %ymm12, %ymm13
	0xc5, 0x15, 0xdb, 0xec, //0x000003fe vpand        %ymm4, %ymm13, %ymm13
	0xc4, 0x42, 0x45, 0x00, 0xed, //0x00000402 vpshufb      %ymm13, %ymm7, %ymm13
	0xc5, 0x95, 0xdb, 0xdb, //0x00000407 vpand        %ymm3, %ymm13, %ymm3
	0xc5, 0xd5, 0xdb, 0xdb, //0x0000040b vpand        %ymm3, %ymm5, %ymm3
	0xc4, 0xe3, 0x1d, 0x0f, 0xea, 0x0e, //0x0000040f vpalignr     $14, %ymm2, %ymm12, %ymm5
	0xc4, 0xe3, 0x1d, 0x0f, 0xd2, 0x0d, //0x00000415 vpalignr     $13, %ymm2, %ymm12, %ymm2
	0xc4, 0xc1, 0x55, 0xd8, 0xe8, //0x0000041b vpsubusb     %ymm8, %ymm5, %ymm5
	0xc4, 0xc1, 0x6d, 0xd8, 0xd1, //0x00000420 vpsubusb     %ymm9, %ymm2, %ymm2
	0xc5, 0xed, 0xeb, 0xd5, //0x00000425 vpor         %ymm5, %ymm2, %ymm2
	0xc4, 0x41, 0x09, 0xef, 0xf6, //0x00000429 vpxor        %xmm14, %xmm14, %xmm14
	0xc5, 0x8d, 0x74, 0xd2, //0x0000042e vpcmpeqb     %ymm2, %ymm14, %ymm2
	0xc5, 0xed, 0xdf, 0xd1, //0x00000432 vpandn       %ymm1, %ymm2, %ymm2
	0xc5, 0xed, 0xef, 0xd3, //0x00000436 vpxor        %ymm3, %ymm2, %ymm2
	0xc4, 0xc3, 0x1d, 0x46, 0xdb, 0x21, //0x0000043a vperm2i128   $33, %ymm11, %ymm12, %ymm3
	0xc4, 0xe3, 0x25, 0x0f, 0xeb, 0x0f, //0x00000440 vpalignr     $15, %ymm3, %ymm11, %ymm5
	0xc5, 0x9d, 0x71, 0xd5, 0x04, //0x00000446 vpsrlw       $4, %ymm5, %ymm12
	0xc5, 0x1d, 0xdb, 0xe4, //0x0000044b vpand        %ymm4, %ymm12, %ymm12
	0xc4, 0x42, 0x05, 0x00, 0xe4, //0x0000044f vpshufb      %ymm12, %ymm15, %ymm12
	0xc5, 0xd5, 0xdb, 0xec, //0x00000454 vpand        %ymm4, %ymm5, %ymm5
	0xc4, 0xe2, 0x4d, 0x00, 0xed, //0x00000458 vpshufb      %ymm5, %ymm6, %ymm5
	0xc4, 0xc1, 0x15, 0x71, 0xd3, 0x04, //0x0000045d vpsrlw       $4, %ymm11, %ymm13
	0xc5, 0x15, 0xdb, 0xec, //0x00000463 vpand        %ymm4, %ymm13, %ymm13
	0xc4, 0x42, 0x45, 0x00, 0xed, //0x00000467 vpshufb      %ymm13, %ymm7, %ymm13
	0xc5, 0x95, 0xdb, 0xed, //0x0000046c vpand        %ymm5, %ymm13, %ymm5
	0xc5, 0x9d, 0xdb, 0xed, //0x00000470 vpand        %ymm5, %ymm12, %ymm5
	0xc4, 0x63, 0x25, 0x0f, 0xe3, 0x0e, //0x00000474 vpalignr     $14, %ymm3, %ymm11, %ymm12
	0xc4, 0xe3, 0x25, 0x0f, 0xdb, 0x0d, //0x0000047a vpalignr     $13, %ymm3, %ymm11, %ymm3
	0xc4, 0x41, 0x1d, 0xd8, 0xe0, //0x00000480 vpsubusb     %ymm8, %ymm12, %ymm12
	0xc4, 0xc1, 0x65, 0xd8, 0xd9, //0x00000485 vpsubusb     %ymm9, %ymm3, %ymm3
	0xc5, 0x9d, 0xeb, 0xdb, //0x0000048a vpor         %ymm3, %ymm12, %ymm3
	0xc5, 0x8d, 0x74, 0xdb, //0x0000048e vpcmpeqb     %ymm3, %ymm14, %ymm3
	0xc5, 0xe5, 0xdf, 0xc9, //0x00000492 vpandn       %ymm1, %ymm3, %ymm1
	0xc5, 0xf5, 0xef, 0xcd, //0x00000496 vpxor        %ymm5, %ymm1, %ymm1
	0xc5, 0xed, 0xeb, 0xc0, //0x0000049a vpor         %ymm0, %ymm2, %ymm0
	0xe9, 0x1a, 0xfe, 0xff, 0xff, //0x0000049e jmp          LBB0_8
	//0x000004a3 LBB0_11
	0xc4, 0xc1, 0x15, 0xd8, 0xca, //0x000004a3 vpsubusb     %ymm10, %ymm13, %ymm1
	0xc5, 0xfd, 0xeb, 0xc1, //0x000004a8 vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0x7d, 0x7f, 0xea, //0x000004ac vmovdqa      %ymm13, %ymm2
	0xe9, 0x0c, 0xfd, 0xff, 0xff, //0x000004b0 jmp          LBB0_5
	//0x000004b5 LBB0_13
	0x48, 0x83, 0xe2, 0x80, //0x000004b5 andq         $-128, %rdx
	0x4c, 0x01, 0xc2, //0x000004b9 addq         %r8, %rdx
	//0x000004bc LBB0_14
	0x49, 0x8d, 0x49, 0xc0, //0x000004bc leaq         $-64(%r9), %rcx
	0x48, 0x39, 0xca, //0x000004c0 cmpq         %rcx, %rdx
	0x0f, 0x83, 0x79, 0x01, 0x00, 0x00, //0x000004c3 jae          LBB0_20
	0x48, 0x89, 0xc6, //0x000004c9 movq         %rax, %rsi
	0x48, 0x29, 0xd6, //0x000004cc subq         %rdx, %rsi
	0x4a, 0x8d, 0x7c, 0x06, 0xff, //0x000004cf leaq         $-1(%rsi,%r8), %rdi
	0xc5, 0xfe, 0x6f, 0x1d, 0x24, 0xfb, 0xff, 0xff, //0x000004d4 vmovdqu      $-1244(%rip), %ymm3  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x3c, 0xfb, 0xff, 0xff, //0x000004dc vmovdqu      $-1220(%rip), %ymm4  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x2d, 0x54, 0xfb, 0xff, 0xff, //0x000004e4 vmovdqu      $-1196(%rip), %ymm5  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x6c, 0xfb, 0xff, 0xff, //0x000004ec vmovdqu      $-1172(%rip), %ymm6  /* LCPI0_3+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x84, 0xfb, 0xff, 0xff, //0x000004f4 vmovdqu      $-1148(%rip), %ymm7  /* LCPI0_4+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x9c, 0xfb, 0xff, 0xff, //0x000004fc vmovdqu      $-1124(%rip), %ymm8  /* LCPI0_5+0(%rip) */
	0xc4, 0x41, 0x31, 0xef, 0xc9, //0x00000504 vpxor        %xmm9, %xmm9, %xmm9
	0xc5, 0x7e, 0x6f, 0x15, 0xaf, 0xfb, 0xff, 0xff, //0x00000509 vmovdqu      $-1105(%rip), %ymm10  /* LCPI0_7+0(%rip) */
	0x48, 0x89, 0xd6, //0x00000511 movq         %rdx, %rsi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000514 .p2align 4, 0x90
	//0x00000520 LBB0_16
	0xc5, 0x7e, 0x6f, 0x26, //0x00000520 vmovdqu      (%rsi), %ymm12
	0xc5, 0x7e, 0x6f, 0x5e, 0x20, //0x00000524 vmovdqu      $32(%rsi), %ymm11
	0xc4, 0x41, 0x25, 0xeb, 0xec, //0x00000529 vpor         %ymm12, %ymm11, %ymm13
	0xc4, 0xc1, 0x7d, 0xd7, 0xdd, //0x0000052e vpmovmskb    %ymm13, %ebx
	0x85, 0xdb, //0x00000533 testl        %ebx, %ebx
	0x0f, 0x85, 0x16, 0x00, 0x00, 0x00, //0x00000535 jne          LBB0_18
	0xc5, 0xf5, 0xeb, 0xc0, //0x0000053b vpor         %ymm0, %ymm1, %ymm0
	0x48, 0x83, 0xc6, 0x40, //0x0000053f addq         $64, %rsi
	0x48, 0x39, 0xce, //0x00000543 cmpq         %rcx, %rsi
	0x0f, 0x82, 0xd4, 0xff, 0xff, 0xff, //0x00000546 jb           LBB0_16
	0xe9, 0xea, 0x00, 0x00, 0x00, //0x0000054c jmp          LBB0_19
	//0x00000551 LBB0_18
	0xc4, 0xc3, 0x6d, 0x46, 0xcc, 0x21, //0x00000551 vperm2i128   $33, %ymm12, %ymm2, %ymm1
	0xc4, 0xe3, 0x1d, 0x0f, 0xd1, 0x0f, //0x00000557 vpalignr     $15, %ymm1, %ymm12, %ymm2
	0xc5, 0x95, 0x71, 0xd2, 0x04, //0x0000055d vpsrlw       $4, %ymm2, %ymm13
	0xc5, 0x15, 0xdb, 0xeb, //0x00000562 vpand        %ymm3, %ymm13, %ymm13
	0xc4, 0x42, 0x5d, 0x00, 0xed, //0x00000566 vpshufb      %ymm13, %ymm4, %ymm13
	0xc5, 0xed, 0xdb, 0xd3, //0x0000056b vpand        %ymm3, %ymm2, %ymm2
	0xc4, 0xe2, 0x55, 0x00, 0xd2, //0x0000056f vpshufb      %ymm2, %ymm5, %ymm2
	0xc4, 0xc1, 0x0d, 0x71, 0xd4, 0x04, //0x00000574 vpsrlw       $4, %ymm12, %ymm14
	0xc5, 0x0d, 0xdb, 0xf3, //0x0000057a vpand        %ymm3, %ymm14, %ymm14
	0xc4, 0x42, 0x4d, 0x00, 0xf6, //0x0000057e vpshufb      %ymm14, %ymm6, %ymm14
	0xc5, 0x8d, 0xdb, 0xd2, //0x00000583 vpand        %ymm2, %ymm14, %ymm2
	0xc5, 0x95, 0xdb, 0xd2, //0x00000587 vpand        %ymm2, %ymm13, %ymm2
	0xc4, 0x63, 0x1d, 0x0f, 0xe9, 0x0e, //0x0000058b vpalignr     $14, %ymm1, %ymm12, %ymm13
	0xc4, 0xe3, 0x1d, 0x0f, 0xc9, 0x0d, //0x00000591 vpalignr     $13, %ymm1, %ymm12, %ymm1
	0xc5, 0x15, 0xd8, 0xef, //0x00000597 vpsubusb     %ymm7, %ymm13, %ymm13
	0xc4, 0xc1, 0x75, 0xd8, 0xc8, //0x0000059b vpsubusb     %ymm8, %ymm1, %ymm1
	0xc5, 0x95, 0xeb, 0xc9, //0x000005a0 vpor         %ymm1, %ymm13, %ymm1
	0xc5, 0xb5, 0x74, 0xc9, //0x000005a4 vpcmpeqb     %ymm1, %ymm9, %ymm1
	0xc4, 0x62, 0x7d, 0x59, 0x2d, 0x4f, 0xfb, 0xff, 0xff, //0x000005a8 vpbroadcastq $-1201(%rip), %ymm13  /* LCPI0_6+0(%rip) */
	0xc4, 0xc1, 0x75, 0xdf, 0xcd, //0x000005b1 vpandn       %ymm13, %ymm1, %ymm1
	0xc5, 0xf5, 0xef, 0xca, //0x000005b6 vpxor        %ymm2, %ymm1, %ymm1
	0xc4, 0xc3, 0x1d, 0x46, 0xd3, 0x21, //0x000005ba vperm2i128   $33, %ymm11, %ymm12, %ymm2
	0xc4, 0x63, 0x25, 0x0f, 0xe2, 0x0f, //0x000005c0 vpalignr     $15, %ymm2, %ymm11, %ymm12
	0xc4, 0xc1, 0x0d, 0x71, 0xd4, 0x04, //0x000005c6 vpsrlw       $4, %ymm12, %ymm14
	0xc5, 0x0d, 0xdb, 0xf3, //0x000005cc vpand        %ymm3, %ymm14, %ymm14
	0xc4, 0x42, 0x5d, 0x00, 0xf6, //0x000005d0 vpshufb      %ymm14, %ymm4, %ymm14
	0xc5, 0x1d, 0xdb, 0xe3, //0x000005d5 vpand        %ymm3, %ymm12, %ymm12
	0xc4, 0x42, 0x55, 0x00, 0xe4, //0x000005d9 vpshufb      %ymm12, %ymm5, %ymm12
	0xc4, 0xc1, 0x05, 0x71, 0xd3, 0x04, //0x000005de vpsrlw       $4, %ymm11, %ymm15
	0xc5, 0x05, 0xdb, 0xfb, //0x000005e4 vpand        %ymm3, %ymm15, %ymm15
	0xc4, 0x42, 0x4d, 0x00, 0xff, //0x000005e8 vpshufb      %ymm15, %ymm6, %ymm15
	0xc4, 0x41, 0x1d, 0xdb, 0xe7, //0x000005ed vpand        %ymm15, %ymm12, %ymm12
	0xc4, 0x41, 0x0d, 0xdb, 0xe4, //0x000005f2 vpand        %ymm12, %ymm14, %ymm12
	0xc4, 0x63, 0x25, 0x0f, 0xf2, 0x0e, //0x000005f7 vpalignr     $14, %ymm2, %ymm11, %ymm14
	0xc4, 0xe3, 0x25, 0x0f, 0xd2, 0x0d, //0x000005fd vpalignr     $13, %ymm2, %ymm11, %ymm2
	0xc5, 0x0d, 0xd8, 0xf7, //0x00000603 vpsubusb     %ymm7, %ymm14, %ymm14
	0xc4, 0xc1, 0x6d, 0xd8, 0xd0, //0x00000607 vpsubusb     %ymm8, %ymm2, %ymm2
	0xc5, 0x8d, 0xeb, 0xd2, //0x0000060c vpor         %ymm2, %ymm14, %ymm2
	0xc5, 0xb5, 0x74, 0xd2, //0x00000610 vpcmpeqb     %ymm2, %ymm9, %ymm2
	0xc4, 0xc1, 0x6d, 0xdf, 0xd5, //0x00000614 vpandn       %ymm13, %ymm2, %ymm2
	0xc5, 0x9d, 0xef, 0xd2, //0x00000619 vpxor        %ymm2, %ymm12, %ymm2
	0xc5, 0xf5, 0xeb, 0xc0, //0x0000061d vpor         %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xeb, 0xc2, //0x00000621 vpor         %ymm2, %ymm0, %ymm0
	0xc4, 0xc1, 0x25, 0xd8, 0xca, //0x00000625 vpsubusb     %ymm10, %ymm11, %ymm1
	0xc5, 0x7d, 0x7f, 0xda, //0x0000062a vmovdqa      %ymm11, %ymm2
	0x48, 0x83, 0xc6, 0x40, //0x0000062e addq         $64, %rsi
	0x48, 0x39, 0xce, //0x00000632 cmpq         %rcx, %rsi
	0x0f, 0x82, 0xe5, 0xfe, 0xff, 0xff, //0x00000635 jb           LBB0_16
	//0x0000063b LBB0_19
	0x48, 0x83, 0xe7, 0xc0, //0x0000063b andq         $-64, %rdi
	0x48, 0x01, 0xfa, //0x0000063f addq         %rdi, %rdx
	//0x00000642 LBB0_20
	0xc5, 0xe1, 0xef, 0xdb, //0x00000642 vpxor        %xmm3, %xmm3, %xmm3
	0xc5, 0xfe, 0x7f, 0x5c, 0x24, 0x40, //0x00000646 vmovdqu      %ymm3, $64(%rsp)
	0xc5, 0xfe, 0x7f, 0x5c, 0x24, 0x20, //0x0000064c vmovdqu      %ymm3, $32(%rsp)
	0xc5, 0xd9, 0xef, 0xe4, //0x00000652 vpxor        %xmm4, %xmm4, %xmm4
	0x4c, 0x39, 0xca, //0x00000656 cmpq         %r9, %rdx
	0x0f, 0x83, 0x70, 0x00, 0x00, 0x00, //0x00000659 jae          LBB0_36
	0x48, 0x89, 0xc1, //0x0000065f movq         %rax, %rcx
	0x48, 0x29, 0xd1, //0x00000662 subq         %rdx, %rcx
	0x4d, 0x8d, 0x1c, 0x08, //0x00000665 leaq         (%r8,%rcx), %r11
	0x49, 0x81, 0xfb, 0x80, 0x00, 0x00, 0x00, //0x00000669 cmpq         $128, %r11
	0x0f, 0x82, 0x1f, 0x00, 0x00, 0x00, //0x00000670 jb           LBB0_22
	0x48, 0x8d, 0x74, 0x24, 0x20, //0x00000676 leaq         $32(%rsp), %rsi
	0x4c, 0x39, 0xce, //0x0000067b cmpq         %r9, %rsi
	0x0f, 0x83, 0x74, 0x02, 0x00, 0x00, //0x0000067e jae          LBB0_26
	0x48, 0x8d, 0x4c, 0x0c, 0x20, //0x00000684 leaq         $32(%rsp,%rcx), %rcx
	0x4c, 0x01, 0xc1, //0x00000689 addq         %r8, %rcx
	0x48, 0x39, 0xca, //0x0000068c cmpq         %rcx, %rdx
	0x0f, 0x83, 0x63, 0x02, 0x00, 0x00, //0x0000068f jae          LBB0_26
	//0x00000695 LBB0_22
	0x31, 0xc9, //0x00000695 xorl         %ecx, %ecx
	//0x00000697 LBB0_33
	0x48, 0x8d, 0x4c, 0x0c, 0x20, //0x00000697 leaq         $32(%rsp,%rcx), %rcx
	0x48, 0x29, 0xd0, //0x0000069c subq         %rdx, %rax
	0x4c, 0x01, 0xc0, //0x0000069f addq         %r8, %rax
	0x31, 0xf6, //0x000006a2 xorl         %esi, %esi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000006a4 .p2align 4, 0x90
	//0x000006b0 LBB0_34
	0x0f, 0xb6, 0x1c, 0x32, //0x000006b0 movzbl       (%rdx,%rsi), %ebx
	0x88, 0x1c, 0x31, //0x000006b4 movb         %bl, (%rcx,%rsi)
	0x48, 0xff, 0xc6, //0x000006b7 incq         %rsi
	0x48, 0x39, 0xf0, //0x000006ba cmpq         %rsi, %rax
	0x0f, 0x85, 0xed, 0xff, 0xff, 0xff, //0x000006bd jne          LBB0_34
	//0x000006c3 LBB0_35
	0xc5, 0xfe, 0x6f, 0x64, 0x24, 0x20, //0x000006c3 vmovdqu      $32(%rsp), %ymm4
	0xc5, 0xfe, 0x6f, 0x5c, 0x24, 0x40, //0x000006c9 vmovdqu      $64(%rsp), %ymm3
	//0x000006cf LBB0_36
	0xc5, 0xdd, 0xeb, 0xeb, //0x000006cf vpor         %ymm3, %ymm4, %ymm5
	0xc5, 0xfd, 0xd7, 0xc5, //0x000006d3 vpmovmskb    %ymm5, %eax
	0x85, 0xc0, //0x000006d7 testl        %eax, %eax
	0x0f, 0x85, 0x38, 0x03, 0x00, 0x00, //0x000006d9 jne          LBB0_40
	0xc5, 0xfd, 0xeb, 0xc1, //0x000006df vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xeb, 0xc1, //0x000006e3 vpor         %ymm1, %ymm0, %ymm0
	0xc4, 0xe2, 0x7d, 0x17, 0xc0, //0x000006e7 vptest       %ymm0, %ymm0
	0x0f, 0x84, 0xfa, 0x01, 0x00, 0x00, //0x000006ec je           LBB0_12
	//0x000006f2 LBB0_41
	0x49, 0x8d, 0x49, 0xfd, //0x000006f2 leaq         $-3(%r9), %rcx
	0x4c, 0x89, 0xc0, //0x000006f6 movq         %r8, %rax
	0x49, 0x39, 0xc8, //0x000006f9 cmpq         %rcx, %r8
	0x0f, 0x83, 0xde, 0x00, 0x00, 0x00, //0x000006fc jae          LBB0_55
	0x4c, 0x89, 0xc0, //0x00000702 movq         %r8, %rax
	0xe9, 0x12, 0x00, 0x00, 0x00, //0x00000705 jmp          LBB0_44
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000070a .p2align 4, 0x90
	//0x00000710 LBB0_43
	0x48, 0x01, 0xf0, //0x00000710 addq         %rsi, %rax
	0x48, 0x39, 0xc8, //0x00000713 cmpq         %rcx, %rax
	0x0f, 0x83, 0xc4, 0x00, 0x00, 0x00, //0x00000716 jae          LBB0_55
	//0x0000071c LBB0_44
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x0000071c movl         $1, %esi
	0x80, 0x38, 0x00, //0x00000721 cmpb         $0, (%rax)
	0x0f, 0x89, 0xe6, 0xff, 0xff, 0xff, //0x00000724 jns          LBB0_43
	0x8b, 0x10, //0x0000072a movl         (%rax), %edx
	0x89, 0xd6, //0x0000072c movl         %edx, %esi
	0x81, 0xe6, 0xf0, 0xc0, 0xc0, 0x00, //0x0000072e andl         $12632304, %esi
	0x81, 0xfe, 0xe0, 0x80, 0x80, 0x00, //0x00000734 cmpl         $8421600, %esi
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x0000073a jne          LBB0_48
	0x89, 0xd7, //0x00000740 movl         %edx, %edi
	0x81, 0xe7, 0x0f, 0x20, 0x00, 0x00, //0x00000742 andl         $8207, %edi
	0x81, 0xff, 0x0d, 0x20, 0x00, 0x00, //0x00000748 cmpl         $8205, %edi
	0x0f, 0x84, 0x1c, 0x00, 0x00, 0x00, //0x0000074e je           LBB0_48
	0xbe, 0x03, 0x00, 0x00, 0x00, //0x00000754 movl         $3, %esi
	0x85, 0xff, //0x00000759 testl        %edi, %edi
	0x0f, 0x85, 0xaf, 0xff, 0xff, 0xff, //0x0000075b jne          LBB0_43
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000761 .p2align 4, 0x90
	//0x00000770 LBB0_48
	0x89, 0xd6, //0x00000770 movl         %edx, %esi
	0x81, 0xe6, 0xe0, 0xc0, 0x00, 0x00, //0x00000772 andl         $49376, %esi
	0x81, 0xfe, 0xc0, 0x80, 0x00, 0x00, //0x00000778 cmpl         $32960, %esi
	0x0f, 0x85, 0x10, 0x00, 0x00, 0x00, //0x0000077e jne          LBB0_50
	0x89, 0xd7, //0x00000784 movl         %edx, %edi
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x00000786 movl         $2, %esi
	0x83, 0xe7, 0x1e, //0x0000078b andl         $30, %edi
	0x0f, 0x85, 0x7c, 0xff, 0xff, 0xff, //0x0000078e jne          LBB0_43
	//0x00000794 LBB0_50
	0x89, 0xd6, //0x00000794 movl         %edx, %esi
	0x81, 0xe6, 0xf8, 0xc0, 0xc0, 0xc0, //0x00000796 andl         $-1061109512, %esi
	0x81, 0xfe, 0xf0, 0x80, 0x80, 0x80, //0x0000079c cmpl         $-2139062032, %esi
	0x0f, 0x85, 0x28, 0x00, 0x00, 0x00, //0x000007a2 jne          LBB0_54
	0x89, 0xd6, //0x000007a8 movl         %edx, %esi
	0x81, 0xe6, 0x07, 0x30, 0x00, 0x00, //0x000007aa andl         $12295, %esi
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x000007b0 je           LBB0_54
	0xbe, 0x04, 0x00, 0x00, 0x00, //0x000007b6 movl         $4, %esi
	0xf6, 0xc2, 0x04, //0x000007bb testb        $4, %dl
	0x0f, 0x84, 0x4c, 0xff, 0xff, 0xff, //0x000007be je           LBB0_43
	0x81, 0xe2, 0x03, 0x30, 0x00, 0x00, //0x000007c4 andl         $12291, %edx
	0x0f, 0x84, 0x40, 0xff, 0xff, 0xff, //0x000007ca je           LBB0_43
	//0x000007d0 LBB0_54
	0x48, 0xf7, 0xd0, //0x000007d0 notq         %rax
	0x4c, 0x01, 0xc0, //0x000007d3 addq         %r8, %rax
	0x48, 0x8d, 0x65, 0xf8, //0x000007d6 leaq         $-8(%rbp), %rsp
	0x5b, //0x000007da popq         %rbx
	0x5d, //0x000007db popq         %rbp
	0xc5, 0xf8, 0x77, //0x000007dc vzeroupper   
	0xc3, //0x000007df retq         
	//0x000007e0 LBB0_55
	0x4c, 0x39, 0xc8, //0x000007e0 cmpq         %r9, %rax
	0x0f, 0x83, 0x03, 0x01, 0x00, 0x00, //0x000007e3 jae          LBB0_12
	0x4c, 0x8d, 0x54, 0x24, 0x20, //0x000007e9 leaq         $32(%rsp), %r10
	0x4c, 0x8d, 0x5c, 0x24, 0x1e, //0x000007ee leaq         $30(%rsp), %r11
	0xe9, 0x14, 0x00, 0x00, 0x00, //0x000007f3 jmp          LBB0_58
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000007f8 .p2align 4, 0x90
	//0x00000800 LBB0_57
	0x48, 0xff, 0xc0, //0x00000800 incq         %rax
	0x4c, 0x39, 0xc8, //0x00000803 cmpq         %r9, %rax
	0x0f, 0x83, 0xe0, 0x00, 0x00, 0x00, //0x00000806 jae          LBB0_12
	//0x0000080c LBB0_58
	0x80, 0x38, 0x00, //0x0000080c cmpb         $0, (%rax)
	0x0f, 0x89, 0xeb, 0xff, 0xff, 0xff, //0x0000080f jns          LBB0_57
	0xc6, 0x44, 0x24, 0x20, 0x00, //0x00000815 movb         $0, $32(%rsp)
	0xc6, 0x44, 0x24, 0x1e, 0x00, //0x0000081a movb         $0, $30(%rsp)
	0x4c, 0x89, 0xc9, //0x0000081f movq         %r9, %rcx
	0x48, 0x29, 0xc1, //0x00000822 subq         %rax, %rcx
	0x48, 0x83, 0xf9, 0x02, //0x00000825 cmpq         $2, %rcx
	0x0f, 0x82, 0x33, 0x00, 0x00, 0x00, //0x00000829 jb           LBB0_62
	0x0f, 0xb6, 0x10, //0x0000082f movzbl       (%rax), %edx
	0x0f, 0xb6, 0x78, 0x01, //0x00000832 movzbl       $1(%rax), %edi
	0x88, 0x54, 0x24, 0x20, //0x00000836 movb         %dl, $32(%rsp)
	0x48, 0x8d, 0x70, 0x02, //0x0000083a leaq         $2(%rax), %rsi
	0x48, 0x83, 0xc1, 0xfe, //0x0000083e addq         $-2, %rcx
	0x4c, 0x89, 0xdb, //0x00000842 movq         %r11, %rbx
	0x48, 0x85, 0xc9, //0x00000845 testq        %rcx, %rcx
	0x0f, 0x84, 0x27, 0x00, 0x00, 0x00, //0x00000848 je           LBB0_63
	//0x0000084e LBB0_61
	0x0f, 0xb6, 0x0e, //0x0000084e movzbl       (%rsi), %ecx
	0x88, 0x0b, //0x00000851 movb         %cl, (%rbx)
	0x0f, 0xb6, 0x54, 0x24, 0x20, //0x00000853 movzbl       $32(%rsp), %edx
	0x0f, 0xb6, 0x4c, 0x24, 0x1e, //0x00000858 movzbl       $30(%rsp), %ecx
	0xe9, 0x15, 0x00, 0x00, 0x00, //0x0000085d jmp          LBB0_64
	//0x00000862 LBB0_62
	0x31, 0xd2, //0x00000862 xorl         %edx, %edx
	0x31, 0xff, //0x00000864 xorl         %edi, %edi
	0x4c, 0x89, 0xd3, //0x00000866 movq         %r10, %rbx
	0x48, 0x89, 0xc6, //0x00000869 movq         %rax, %rsi
	0x48, 0x85, 0xc9, //0x0000086c testq        %rcx, %rcx
	0x0f, 0x85, 0xd9, 0xff, 0xff, 0xff, //0x0000086f jne          LBB0_61
	//0x00000875 LBB0_63
	0x31, 0xc9, //0x00000875 xorl         %ecx, %ecx
	//0x00000877 LBB0_64
	0x0f, 0xb6, 0xf1, //0x00000877 movzbl       %cl, %esi
	0xc1, 0xe6, 0x10, //0x0000087a shll         $16, %esi
	0x40, 0x0f, 0xb6, 0xff, //0x0000087d movzbl       %dil, %edi
	0xc1, 0xe7, 0x08, //0x00000881 shll         $8, %edi
	0x0f, 0xb6, 0xca, //0x00000884 movzbl       %dl, %ecx
	0x09, 0xf9, //0x00000887 orl          %edi, %ecx
	0x09, 0xce, //0x00000889 orl          %ecx, %esi
	0x81, 0xe6, 0xf0, 0xc0, 0xc0, 0x00, //0x0000088b andl         $12632304, %esi
	0x81, 0xfe, 0xe0, 0x80, 0x80, 0x00, //0x00000891 cmpl         $8421600, %esi
	0x0f, 0x85, 0x23, 0x00, 0x00, 0x00, //0x00000897 jne          LBB0_67
	0x89, 0xcf, //0x0000089d movl         %ecx, %edi
	0x81, 0xe7, 0x0f, 0x20, 0x00, 0x00, //0x0000089f andl         $8207, %edi
	0x81, 0xff, 0x0d, 0x20, 0x00, 0x00, //0x000008a5 cmpl         $8205, %edi
	0x0f, 0x84, 0x0f, 0x00, 0x00, 0x00, //0x000008ab je           LBB0_67
	0xbe, 0x03, 0x00, 0x00, 0x00, //0x000008b1 movl         $3, %esi
	0x85, 0xff, //0x000008b6 testl        %edi, %edi
	0x0f, 0x85, 0x22, 0x00, 0x00, 0x00, //0x000008b8 jne          LBB0_69
	0x90, 0x90, //0x000008be .p2align 4, 0x90
	//0x000008c0 LBB0_67
	0xf6, 0xc2, 0x1e, //0x000008c0 testb        $30, %dl
	0x0f, 0x84, 0x07, 0xff, 0xff, 0xff, //0x000008c3 je           LBB0_54
	0x81, 0xe1, 0xe0, 0xc0, 0x00, 0x00, //0x000008c9 andl         $49376, %ecx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x000008cf movl         $2, %esi
	0x81, 0xf9, 0xc0, 0x80, 0x00, 0x00, //0x000008d4 cmpl         $32960, %ecx
	0x0f, 0x85, 0xf0, 0xfe, 0xff, 0xff, //0x000008da jne          LBB0_54
	//0x000008e0 LBB0_69
	0x48, 0x01, 0xf0, //0x000008e0 addq         %rsi, %rax
	0x4c, 0x39, 0xc8, //0x000008e3 cmpq         %r9, %rax
	0x0f, 0x82, 0x20, 0xff, 0xff, 0xff, //0x000008e6 jb           LBB0_58
	//0x000008ec LBB0_12
	0x31, 0xc0, //0x000008ec xorl         %eax, %eax
	0x48, 0x8d, 0x65, 0xf8, //0x000008ee leaq         $-8(%rbp), %rsp
	0x5b, //0x000008f2 popq         %rbx
	0x5d, //0x000008f3 popq         %rbp
	0xc5, 0xf8, 0x77, //0x000008f4 vzeroupper   
	0xc3, //0x000008f7 retq         
	//0x000008f8 LBB0_26
	0x4c, 0x89, 0xd9, //0x000008f8 movq         %r11, %rcx
	0x48, 0x83, 0xe1, 0x80, //0x000008fb andq         $-128, %rcx
	0x48, 0x8d, 0x79, 0x80, //0x000008ff leaq         $-128(%rcx), %rdi
	0x48, 0x89, 0xfe, //0x00000903 movq         %rdi, %rsi
	0x48, 0xc1, 0xee, 0x07, //0x00000906 shrq         $7, %rsi
	0x48, 0xff, 0xc6, //0x0000090a incq         %rsi
	0x41, 0x89, 0xf2, //0x0000090d movl         %esi, %r10d
	0x41, 0x83, 0xe2, 0x01, //0x00000910 andl         $1, %r10d
	0x48, 0x85, 0xff, //0x00000914 testq        %rdi, %rdi
	0x0f, 0x84, 0xea, 0x00, 0x00, 0x00, //0x00000917 je           LBB0_39
	0x4c, 0x29, 0xd6, //0x0000091d subq         %r10, %rsi
	0x31, 0xff, //0x00000920 xorl         %edi, %edi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000922 .p2align 4, 0x90
	//0x00000930 LBB0_28
	0xc5, 0xfc, 0x10, 0x1c, 0x3a, //0x00000930 vmovups      (%rdx,%rdi), %ymm3
	0xc5, 0xfc, 0x10, 0x64, 0x3a, 0x20, //0x00000935 vmovups      $32(%rdx,%rdi), %ymm4
	0xc5, 0xfc, 0x10, 0x6c, 0x3a, 0x40, //0x0000093b vmovups      $64(%rdx,%rdi), %ymm5
	0xc5, 0xfc, 0x10, 0x74, 0x3a, 0x60, //0x00000941 vmovups      $96(%rdx,%rdi), %ymm6
	0xc5, 0xfc, 0x11, 0x5c, 0x3c, 0x20, //0x00000947 vmovups      %ymm3, $32(%rsp,%rdi)
	0xc5, 0xfc, 0x11, 0x64, 0x3c, 0x40, //0x0000094d vmovups      %ymm4, $64(%rsp,%rdi)
	0xc5, 0xfc, 0x11, 0x6c, 0x3c, 0x60, //0x00000953 vmovups      %ymm5, $96(%rsp,%rdi)
	0xc5, 0xfc, 0x11, 0xb4, 0x3c, 0x80, 0x00, 0x00, 0x00, //0x00000959 vmovups      %ymm6, $128(%rsp,%rdi)
	0xc5, 0xfe, 0x6f, 0x9c, 0x3a, 0x80, 0x00, 0x00, 0x00, //0x00000962 vmovdqu      $128(%rdx,%rdi), %ymm3
	0xc5, 0xfe, 0x6f, 0xa4, 0x3a, 0xa0, 0x00, 0x00, 0x00, //0x0000096b vmovdqu      $160(%rdx,%rdi), %ymm4
	0xc5, 0xfe, 0x6f, 0xac, 0x3a, 0xc0, 0x00, 0x00, 0x00, //0x00000974 vmovdqu      $192(%rdx,%rdi), %ymm5
	0xc5, 0xfe, 0x6f, 0xb4, 0x3a, 0xe0, 0x00, 0x00, 0x00, //0x0000097d vmovdqu      $224(%rdx,%rdi), %ymm6
	0xc5, 0xfe, 0x7f, 0x9c, 0x3c, 0xa0, 0x00, 0x00, 0x00, //0x00000986 vmovdqu      %ymm3, $160(%rsp,%rdi)
	0xc5, 0xfe, 0x7f, 0xa4, 0x3c, 0xc0, 0x00, 0x00, 0x00, //0x0000098f vmovdqu      %ymm4, $192(%rsp,%rdi)
	0xc5, 0xfe, 0x7f, 0xac, 0x3c, 0xe0, 0x00, 0x00, 0x00, //0x00000998 vmovdqu      %ymm5, $224(%rsp,%rdi)
	0xc5, 0xfe, 0x7f, 0xb4, 0x3c, 0x00, 0x01, 0x00, 0x00, //0x000009a1 vmovdqu      %ymm6, $256(%rsp,%rdi)
	0x48, 0x81, 0xc7, 0x00, 0x01, 0x00, 0x00, //0x000009aa addq         $256, %rdi
	0x48, 0x83, 0xc6, 0xfe, //0x000009b1 addq         $-2, %rsi
	0x0f, 0x85, 0x75, 0xff, 0xff, 0xff, //0x000009b5 jne          LBB0_28
	0x4d, 0x85, 0xd2, //0x000009bb testq        %r10, %r10
	0x0f, 0x84, 0x32, 0x00, 0x00, 0x00, //0x000009be je           LBB0_31
	//0x000009c4 LBB0_30
	0xc5, 0xfe, 0x6f, 0x1c, 0x3a, //0x000009c4 vmovdqu      (%rdx,%rdi), %ymm3
	0xc5, 0xfe, 0x6f, 0x64, 0x3a, 0x20, //0x000009c9 vmovdqu      $32(%rdx,%rdi), %ymm4
	0xc5, 0xfe, 0x6f, 0x6c, 0x3a, 0x40, //0x000009cf vmovdqu      $64(%rdx,%rdi), %ymm5
	0xc5, 0xfe, 0x6f, 0x74, 0x3a, 0x60, //0x000009d5 vmovdqu      $96(%rdx,%rdi), %ymm6
	0xc5, 0xfe, 0x7f, 0x5c, 0x3c, 0x20, //0x000009db vmovdqu      %ymm3, $32(%rsp,%rdi)
	0xc5, 0xfe, 0x7f, 0x64, 0x3c, 0x40, //0x000009e1 vmovdqu      %ymm4, $64(%rsp,%rdi)
	0xc5, 0xfe, 0x7f, 0x6c, 0x3c, 0x60, //0x000009e7 vmovdqu      %ymm5, $96(%rsp,%rdi)
	0xc5, 0xfe, 0x7f, 0xb4, 0x3c, 0x80, 0x00, 0x00, 0x00, //0x000009ed vmovdqu      %ymm6, $128(%rsp,%rdi)
	//0x000009f6 LBB0_31
	0x4c, 0x39, 0xd9, //0x000009f6 cmpq         %r11, %rcx
	0x0f, 0x84, 0xc4, 0xfc, 0xff, 0xff, //0x000009f9 je           LBB0_35
	0x48, 0x01, 0xca, //0x000009ff addq         %rcx, %rdx
	0xe9, 0x90, 0xfc, 0xff, 0xff, //0x00000a02 jmp          LBB0_33
	//0x00000a07 LBB0_39
	0x31, 0xff, //0x00000a07 xorl         %edi, %edi
	0x4d, 0x85, 0xd2, //0x00000a09 testq        %r10, %r10
	0x0f, 0x85, 0xb2, 0xff, 0xff, 0xff, //0x00000a0c jne          LBB0_30
	0xe9, 0xdf, 0xff, 0xff, 0xff, //0x00000a12 jmp          LBB0_31
	//0x00000a17 LBB0_40
	0xc4, 0xe3, 0x6d, 0x46, 0xd4, 0x21, //0x00000a17 vperm2i128   $33, %ymm4, %ymm2, %ymm2
	0xc4, 0xe3, 0x5d, 0x0f, 0xea, 0x0f, //0x00000a1d vpalignr     $15, %ymm2, %ymm4, %ymm5
	0xc5, 0xcd, 0x71, 0xd5, 0x04, //0x00000a23 vpsrlw       $4, %ymm5, %ymm6
	0xc5, 0xfe, 0x6f, 0x0d, 0xd0, 0xf5, 0xff, 0xff, //0x00000a28 vmovdqu      $-2608(%rip), %ymm1  /* LCPI0_0+0(%rip) */
	0xc5, 0xcd, 0xdb, 0xf1, //0x00000a30 vpand        %ymm1, %ymm6, %ymm6
	0xc5, 0xfe, 0x6f, 0x3d, 0xe4, 0xf5, 0xff, 0xff, //0x00000a34 vmovdqu      $-2588(%rip), %ymm7  /* LCPI0_1+0(%rip) */
	0xc4, 0xe2, 0x45, 0x00, 0xf6, //0x00000a3c vpshufb      %ymm6, %ymm7, %ymm6
	0xc5, 0xd5, 0xdb, 0xe9, //0x00000a41 vpand        %ymm1, %ymm5, %ymm5
	0xc5, 0x7e, 0x6f, 0x05, 0xf3, 0xf5, 0xff, 0xff, //0x00000a45 vmovdqu      $-2573(%rip), %ymm8  /* LCPI0_2+0(%rip) */
	0xc4, 0xe2, 0x3d, 0x00, 0xed, //0x00000a4d vpshufb      %ymm5, %ymm8, %ymm5
	0xc5, 0xb5, 0x71, 0xd4, 0x04, //0x00000a52 vpsrlw       $4, %ymm4, %ymm9
	0xc5, 0x35, 0xdb, 0xc9, //0x00000a57 vpand        %ymm1, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x15, 0xfd, 0xf5, 0xff, 0xff, //0x00000a5b vmovdqu      $-2563(%rip), %ymm10  /* LCPI0_3+0(%rip) */
	0xc4, 0x42, 0x2d, 0x00, 0xc9, //0x00000a63 vpshufb      %ymm9, %ymm10, %ymm9
	0xc5, 0xb5, 0xdb, 0xed, //0x00000a68 vpand        %ymm5, %ymm9, %ymm5
	0xc5, 0xcd, 0xdb, 0xed, //0x00000a6c vpand        %ymm5, %ymm6, %ymm5
	0xc4, 0xe3, 0x5d, 0x0f, 0xf2, 0x0e, //0x00000a70 vpalignr     $14, %ymm2, %ymm4, %ymm6
	0xc4, 0xe3, 0x5d, 0x0f, 0xd2, 0x0d, //0x00000a76 vpalignr     $13, %ymm2, %ymm4, %ymm2
	0xc5, 0x7e, 0x6f, 0x0d, 0xfc, 0xf5, 0xff, 0xff, //0x00000a7c vmovdqu      $-2564(%rip), %ymm9  /* LCPI0_4+0(%rip) */
	0xc4, 0xc1, 0x4d, 0xd8, 0xf1, //0x00000a84 vpsubusb     %ymm9, %ymm6, %ymm6
	0xc5, 0x7e, 0x6f, 0x1d, 0x0f, 0xf6, 0xff, 0xff, //0x00000a89 vmovdqu      $-2545(%rip), %ymm11  /* LCPI0_5+0(%rip) */
	0xc4, 0xc1, 0x6d, 0xd8, 0xd3, //0x00000a91 vpsubusb     %ymm11, %ymm2, %ymm2
	0xc5, 0xed, 0xeb, 0xd6, //0x00000a96 vpor         %ymm6, %ymm2, %ymm2
	0xc5, 0xc9, 0xef, 0xf6, //0x00000a9a vpxor        %xmm6, %xmm6, %xmm6
	0xc5, 0xed, 0x74, 0xd6, //0x00000a9e vpcmpeqb     %ymm6, %ymm2, %ymm2
	0xc4, 0x62, 0x7d, 0x59, 0x25, 0x55, 0xf6, 0xff, 0xff, //0x00000aa2 vpbroadcastq $-2475(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0xc4, 0xc1, 0x6d, 0xdf, 0xd4, //0x00000aab vpandn       %ymm12, %ymm2, %ymm2
	0xc5, 0xed, 0xef, 0xd5, //0x00000ab0 vpxor        %ymm5, %ymm2, %ymm2
	0xc4, 0xe3, 0x5d, 0x46, 0xe3, 0x21, //0x00000ab4 vperm2i128   $33, %ymm3, %ymm4, %ymm4
	0xc4, 0xe3, 0x65, 0x0f, 0xec, 0x0f, //0x00000aba vpalignr     $15, %ymm4, %ymm3, %ymm5
	0xc5, 0x95, 0x71, 0xd5, 0x04, //0x00000ac0 vpsrlw       $4, %ymm5, %ymm13
	0xc5, 0x15, 0xdb, 0xe9, //0x00000ac5 vpand        %ymm1, %ymm13, %ymm13
	0xc4, 0xc2, 0x45, 0x00, 0xfd, //0x00000ac9 vpshufb      %ymm13, %ymm7, %ymm7
	0xc5, 0xd5, 0xdb, 0xe9, //0x00000ace vpand        %ymm1, %ymm5, %ymm5
	0xc4, 0xe2, 0x3d, 0x00, 0xed, //0x00000ad2 vpshufb      %ymm5, %ymm8, %ymm5
	0xc5, 0xbd, 0x71, 0xd3, 0x04, //0x00000ad7 vpsrlw       $4, %ymm3, %ymm8
	0xc5, 0xbd, 0xdb, 0xc9, //0x00000adc vpand        %ymm1, %ymm8, %ymm1
	0xc4, 0xe2, 0x2d, 0x00, 0xc9, //0x00000ae0 vpshufb      %ymm1, %ymm10, %ymm1
	0xc5, 0xd5, 0xdb, 0xc9, //0x00000ae5 vpand        %ymm1, %ymm5, %ymm1
	0xc5, 0xc5, 0xdb, 0xc9, //0x00000ae9 vpand        %ymm1, %ymm7, %ymm1
	0xc4, 0xe3, 0x65, 0x0f, 0xec, 0x0e, //0x00000aed vpalignr     $14, %ymm4, %ymm3, %ymm5
	0xc4, 0xe3, 0x65, 0x0f, 0xe4, 0x0d, //0x00000af3 vpalignr     $13, %ymm4, %ymm3, %ymm4
	0xc4, 0xc1, 0x55, 0xd8, 0xe9, //0x00000af9 vpsubusb     %ymm9, %ymm5, %ymm5
	0xc4, 0xc1, 0x5d, 0xd8, 0xe3, //0x00000afe vpsubusb     %ymm11, %ymm4, %ymm4
	0xc5, 0xdd, 0xeb, 0xe5, //0x00000b03 vpor         %ymm5, %ymm4, %ymm4
	0xc5, 0xdd, 0x74, 0xe6, //0x00000b07 vpcmpeqb     %ymm6, %ymm4, %ymm4
	0xc4, 0xc1, 0x5d, 0xdf, 0xe4, //0x00000b0b vpandn       %ymm12, %ymm4, %ymm4
	0xc5, 0xdd, 0xef, 0xc9, //0x00000b10 vpxor        %ymm1, %ymm4, %ymm1
	0xc5, 0xed, 0xeb, 0xc0, //0x00000b14 vpor         %ymm0, %ymm2, %ymm0
	0xc5, 0xfd, 0xeb, 0xc1, //0x00000b18 vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xe5, 0xd8, 0x0d, 0x9c, 0xf5, 0xff, 0xff, //0x00000b1c vpsubusb     $-2660(%rip), %ymm3, %ymm1  /* LCPI0_7+0(%rip) */
	0xc5, 0xfd, 0xeb, 0xc1, //0x00000b24 vpor         %ymm1, %ymm0, %ymm0
	0xc4, 0xe2, 0x7d, 0x17, 0xc0, //0x00000b28 vptest       %ymm0, %ymm0
	0x0f, 0x84, 0xb9, 0xfd, 0xff, 0xff, //0x00000b2d je           LBB0_12
	0xe9, 0xba, 0xfb, 0xff, 0xff, //0x00000b33 jmp          LBB0_41
	//0x00000b38 .p2align 2, 0x00
	//0x00000b38 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00000b38 .long 2
}
 
