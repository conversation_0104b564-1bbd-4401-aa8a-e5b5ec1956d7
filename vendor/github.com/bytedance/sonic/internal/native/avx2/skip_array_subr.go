// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx2

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__skip_array = 448
)

const (
    _stack__skip_array = 120
)

const (
    _size__skip_array = 10328
)

var (
    _pcsp__skip_array = [][2]uint32{
        {1, 0},
        {4, 8},
        {6, 16},
        {8, 24},
        {10, 32},
        {12, 40},
        {13, 48},
        {9772, 120},
        {9776, 48},
        {9777, 40},
        {9779, 32},
        {9781, 24},
        {9783, 16},
        {9785, 8},
        {9789, 0},
        {10328, 120},
    }
)

var _cfunc_skip_array = []loader.CFunc{
    {"_skip_array_entry", 0,  _entry__skip_array, 0, nil},
    {"_skip_array", _entry__skip_array, _size__skip_array, _stack__skip_array, _pcsp__skip_array},
}
