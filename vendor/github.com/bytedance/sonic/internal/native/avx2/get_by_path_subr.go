// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx2

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__get_by_path = 624
)

const (
    _stack__get_by_path = 240
)

const (
    _size__get_by_path = 21752
)

var (
    _pcsp__get_by_path = [][2]uint32{
        {1, 0},
        {4, 8},
        {6, 16},
        {8, 24},
        {10, 32},
        {12, 40},
        {13, 48},
        {12437, 240},
        {12438, 232},
        {12440, 224},
        {12442, 216},
        {12444, 208},
        {12446, 200},
        {12450, 192},
        {21752, 240},
    }
)

var _cfunc_get_by_path = []loader.CFunc{
    {"_get_by_path_entry", 0,  _entry__get_by_path, 0, nil},
    {"_get_by_path", _entry__get_by_path, _size__get_by_path, _stack__get_by_path, _pcsp__get_by_path},
}
