// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_unquote = []byte{
	// .p2align 5, 0x00
	// LCPI0_0
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, // QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000010 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000020 .p2align 4, 0x00
	//0x00000020 LCPI0_1
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000020 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000030 .p2align 4, 0x90
	//0x00000030 _unquote
	0x55, //0x00000030 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000031 movq         %rsp, %rbp
	0x41, 0x57, //0x00000034 pushq        %r15
	0x41, 0x56, //0x00000036 pushq        %r14
	0x41, 0x55, //0x00000038 pushq        %r13
	0x41, 0x54, //0x0000003a pushq        %r12
	0x53, //0x0000003c pushq        %rbx
	0x48, 0x83, 0xec, 0x18, //0x0000003d subq         $24, %rsp
	0x48, 0x85, 0xf6, //0x00000041 testq        %rsi, %rsi
	0x0f, 0x84, 0x29, 0x00, 0x00, 0x00, //0x00000044 je           LBB0_2
	0x48, 0x89, 0x4d, 0xd0, //0x0000004a movq         %rcx, $-48(%rbp)
	0x45, 0x89, 0xc2, //0x0000004e movl         %r8d, %r10d
	0x41, 0x83, 0xe2, 0x01, //0x00000051 andl         $1, %r10d
	0xc5, 0xfe, 0x6f, 0x0d, 0xa3, 0xff, 0xff, 0xff, //0x00000055 vmovdqu      $-93(%rip), %ymm1  /* LCPI0_0+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x15, 0xbb, 0xff, 0xff, 0xff, //0x0000005d vmovdqu      $-69(%rip), %xmm2  /* LCPI0_1+0(%rip) */
	0x49, 0x89, 0xf9, //0x00000065 movq         %rdi, %r9
	0x49, 0x89, 0xf5, //0x00000068 movq         %rsi, %r13
	0x48, 0x89, 0xd0, //0x0000006b movq         %rdx, %rax
	0xe9, 0x59, 0x00, 0x00, 0x00, //0x0000006e jmp          LBB0_8
	//0x00000073 LBB0_2
	0x45, 0x31, 0xed, //0x00000073 xorl         %r13d, %r13d
	0x48, 0x89, 0xd0, //0x00000076 movq         %rdx, %rax
	//0x00000079 LBB0_3
	0x4c, 0x01, 0xe8, //0x00000079 addq         %r13, %rax
	0x48, 0x29, 0xd0, //0x0000007c subq         %rdx, %rax
	//0x0000007f LBB0_4
	0x48, 0x83, 0xc4, 0x18, //0x0000007f addq         $24, %rsp
	0x5b, //0x00000083 popq         %rbx
	0x41, 0x5c, //0x00000084 popq         %r12
	0x41, 0x5d, //0x00000086 popq         %r13
	0x41, 0x5e, //0x00000088 popq         %r14
	0x41, 0x5f, //0x0000008a popq         %r15
	0x5d, //0x0000008c popq         %rbp
	0xc5, 0xf8, 0x77, //0x0000008d vzeroupper   
	0xc3, //0x00000090 retq         
	//0x00000091 LBB0_5
	0x4f, 0x8d, 0x4c, 0x21, 0x04, //0x00000091 leaq         $4(%r9,%r12), %r9
	0x44, 0x89, 0xf9, //0x00000096 movl         %r15d, %ecx
	0xc1, 0xe9, 0x06, //0x00000099 shrl         $6, %ecx
	0x80, 0xc9, 0xc0, //0x0000009c orb          $-64, %cl
	0x88, 0x08, //0x0000009f movb         %cl, (%rax)
	0x41, 0x80, 0xe7, 0x3f, //0x000000a1 andb         $63, %r15b
	0x41, 0x80, 0xcf, 0x80, //0x000000a5 orb          $-128, %r15b
	0x44, 0x88, 0x78, 0x01, //0x000000a9 movb         %r15b, $1(%rax)
	0x48, 0x83, 0xc0, 0x02, //0x000000ad addq         $2, %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000000b1 .p2align 4, 0x90
	//0x000000c0 LBB0_6
	0x4d, 0x89, 0xf5, //0x000000c0 movq         %r14, %r13
	//0x000000c3 LBB0_7
	0x4d, 0x85, 0xed, //0x000000c3 testq        %r13, %r13
	0x0f, 0x84, 0x9d, 0x07, 0x00, 0x00, //0x000000c6 je           LBB0_101
	//0x000000cc LBB0_8
	0x41, 0x80, 0x39, 0x5c, //0x000000cc cmpb         $92, (%r9)
	0x0f, 0x85, 0x0a, 0x00, 0x00, 0x00, //0x000000d0 jne          LBB0_10
	0x31, 0xdb, //0x000000d6 xorl         %ebx, %ebx
	0xe9, 0x43, 0x01, 0x00, 0x00, //0x000000d8 jmp          LBB0_24
	0x90, 0x90, 0x90, //0x000000dd .p2align 4, 0x90
	//0x000000e0 LBB0_10
	0x4d, 0x89, 0xec, //0x000000e0 movq         %r13, %r12
	0x49, 0x89, 0xc7, //0x000000e3 movq         %rax, %r15
	0x4d, 0x89, 0xce, //0x000000e6 movq         %r9, %r14
	0x49, 0x83, 0xfd, 0x20, //0x000000e9 cmpq         $32, %r13
	0x0f, 0x8c, 0x3e, 0x00, 0x00, 0x00, //0x000000ed jl           LBB0_14
	0x4d, 0x89, 0xce, //0x000000f3 movq         %r9, %r14
	0x49, 0x89, 0xc7, //0x000000f6 movq         %rax, %r15
	0x4d, 0x89, 0xec, //0x000000f9 movq         %r13, %r12
	0x90, 0x90, 0x90, 0x90, //0x000000fc .p2align 4, 0x90
	//0x00000100 LBB0_12
	0xc4, 0xc1, 0x7e, 0x6f, 0x06, //0x00000100 vmovdqu      (%r14), %ymm0
	0xc4, 0xc1, 0x7e, 0x7f, 0x07, //0x00000105 vmovdqu      %ymm0, (%r15)
	0xc5, 0xfd, 0x74, 0xc1, //0x0000010a vpcmpeqb     %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd8, //0x0000010e vpmovmskb    %ymm0, %ebx
	0x85, 0xdb, //0x00000112 testl        %ebx, %ebx
	0x0f, 0x85, 0xc2, 0x00, 0x00, 0x00, //0x00000114 jne          LBB0_22
	0x49, 0x83, 0xc6, 0x20, //0x0000011a addq         $32, %r14
	0x49, 0x83, 0xc7, 0x20, //0x0000011e addq         $32, %r15
	0x49, 0x83, 0xfc, 0x3f, //0x00000122 cmpq         $63, %r12
	0x4d, 0x8d, 0x64, 0x24, 0xe0, //0x00000126 leaq         $-32(%r12), %r12
	0x0f, 0x8f, 0xcf, 0xff, 0xff, 0xff, //0x0000012b jg           LBB0_12
	//0x00000131 LBB0_14
	0xc5, 0xf8, 0x77, //0x00000131 vzeroupper   
	0xc5, 0xfa, 0x6f, 0x15, 0xe4, 0xfe, 0xff, 0xff, //0x00000134 vmovdqu      $-284(%rip), %xmm2  /* LCPI0_1+0(%rip) */
	0x49, 0x83, 0xfc, 0x10, //0x0000013c cmpq         $16, %r12
	0x0f, 0x8c, 0x3c, 0x00, 0x00, 0x00, //0x00000140 jl           LBB0_17
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000146 .p2align 4, 0x90
	//0x00000150 LBB0_15
	0xc4, 0xc1, 0x7a, 0x6f, 0x06, //0x00000150 vmovdqu      (%r14), %xmm0
	0xc4, 0xc1, 0x7a, 0x7f, 0x07, //0x00000155 vmovdqu      %xmm0, (%r15)
	0xc5, 0xf9, 0x74, 0xc2, //0x0000015a vpcmpeqb     %xmm2, %xmm0, %xmm0
	0xc5, 0xf9, 0xd7, 0xd8, //0x0000015e vpmovmskb    %xmm0, %ebx
	0x66, 0x85, 0xdb, //0x00000162 testw        %bx, %bx
	0x0f, 0x85, 0x8d, 0x00, 0x00, 0x00, //0x00000165 jne          LBB0_23
	0x49, 0x83, 0xc6, 0x10, //0x0000016b addq         $16, %r14
	0x49, 0x83, 0xc7, 0x10, //0x0000016f addq         $16, %r15
	0x49, 0x83, 0xfc, 0x1f, //0x00000173 cmpq         $31, %r12
	0x4d, 0x8d, 0x64, 0x24, 0xf0, //0x00000177 leaq         $-16(%r12), %r12
	0x0f, 0x8f, 0xce, 0xff, 0xff, 0xff, //0x0000017c jg           LBB0_15
	//0x00000182 LBB0_17
	0x4d, 0x85, 0xe4, //0x00000182 testq        %r12, %r12
	0x0f, 0x84, 0xee, 0xfe, 0xff, 0xff, //0x00000185 je           LBB0_3
	0x31, 0xdb, //0x0000018b xorl         %ebx, %ebx
	0xc5, 0xfe, 0x6f, 0x0d, 0x6b, 0xfe, 0xff, 0xff, //0x0000018d vmovdqu      $-405(%rip), %ymm1  /* LCPI0_0+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000195 .p2align 4, 0x90
	//0x000001a0 LBB0_19
	0x45, 0x0f, 0xb6, 0x1c, 0x1e, //0x000001a0 movzbl       (%r14,%rbx), %r11d
	0x41, 0x80, 0xfb, 0x5c, //0x000001a5 cmpb         $92, %r11b
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x000001a9 je           LBB0_21
	0x45, 0x88, 0x1c, 0x1f, //0x000001af movb         %r11b, (%r15,%rbx)
	0x48, 0xff, 0xc3, //0x000001b3 incq         %rbx
	0x49, 0x39, 0xdc, //0x000001b6 cmpq         %rbx, %r12
	0x0f, 0x85, 0xe1, 0xff, 0xff, 0xff, //0x000001b9 jne          LBB0_19
	0xe9, 0xb5, 0xfe, 0xff, 0xff, //0x000001bf jmp          LBB0_3
	//0x000001c4 LBB0_21
	0x49, 0x01, 0xde, //0x000001c4 addq         %rbx, %r14
	0x4d, 0x29, 0xce, //0x000001c7 subq         %r9, %r14
	0x4c, 0x89, 0xf3, //0x000001ca movq         %r14, %rbx
	0x48, 0x83, 0xfb, 0xff, //0x000001cd cmpq         $-1, %rbx
	0x0f, 0x85, 0x49, 0x00, 0x00, 0x00, //0x000001d1 jne          LBB0_24
	0xe9, 0x9d, 0xfe, 0xff, 0xff, //0x000001d7 jmp          LBB0_3
	//0x000001dc LBB0_22
	0x48, 0x63, 0xdb, //0x000001dc movslq       %ebx, %rbx
	0x4d, 0x29, 0xce, //0x000001df subq         %r9, %r14
	0x48, 0x0f, 0xbc, 0xdb, //0x000001e2 bsfq         %rbx, %rbx
	0x4c, 0x01, 0xf3, //0x000001e6 addq         %r14, %rbx
	0x48, 0x83, 0xfb, 0xff, //0x000001e9 cmpq         $-1, %rbx
	0x0f, 0x85, 0x2d, 0x00, 0x00, 0x00, //0x000001ed jne          LBB0_24
	0xe9, 0x81, 0xfe, 0xff, 0xff, //0x000001f3 jmp          LBB0_3
	//0x000001f8 LBB0_23
	0x0f, 0xb7, 0xdb, //0x000001f8 movzwl       %bx, %ebx
	0x4d, 0x29, 0xce, //0x000001fb subq         %r9, %r14
	0x48, 0x0f, 0xbc, 0xdb, //0x000001fe bsfq         %rbx, %rbx
	0x4c, 0x01, 0xf3, //0x00000202 addq         %r14, %rbx
	0xc5, 0xfe, 0x6f, 0x0d, 0xf3, 0xfd, 0xff, 0xff, //0x00000205 vmovdqu      $-525(%rip), %ymm1  /* LCPI0_0+0(%rip) */
	0x48, 0x83, 0xfb, 0xff, //0x0000020d cmpq         $-1, %rbx
	0x0f, 0x84, 0x62, 0xfe, 0xff, 0xff, //0x00000211 je           LBB0_3
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000217 .p2align 4, 0x90
	//0x00000220 LBB0_24
	0x48, 0x8d, 0x4b, 0x02, //0x00000220 leaq         $2(%rbx), %rcx
	0x49, 0x29, 0xcd, //0x00000224 subq         %rcx, %r13
	0x0f, 0x88, 0x10, 0x06, 0x00, 0x00, //0x00000227 js           LBB0_99
	0x4d, 0x8d, 0x4c, 0x19, 0x02, //0x0000022d leaq         $2(%r9,%rbx), %r9
	0x4d, 0x85, 0xd2, //0x00000232 testq        %r10, %r10
	0x0f, 0x85, 0x09, 0x04, 0x00, 0x00, //0x00000235 jne          LBB0_67
	//0x0000023b LBB0_26
	0x48, 0x01, 0xd8, //0x0000023b addq         %rbx, %rax
	0x41, 0x0f, 0xb6, 0x49, 0xff, //0x0000023e movzbl       $-1(%r9), %ecx
	0x48, 0x8d, 0x1d, 0x86, 0x07, 0x00, 0x00, //0x00000243 leaq         $1926(%rip), %rbx  /* __UnquoteTab+0(%rip) */
	0x8a, 0x1c, 0x19, //0x0000024a movb         (%rcx,%rbx), %bl
	0x80, 0xfb, 0xff, //0x0000024d cmpb         $-1, %bl
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00000250 je           LBB0_29
	0x84, 0xdb, //0x00000256 testb        %bl, %bl
	0x0f, 0x84, 0xf2, 0x05, 0x00, 0x00, //0x00000258 je           LBB0_100
	0x88, 0x18, //0x0000025e movb         %bl, (%rax)
	0x48, 0xff, 0xc0, //0x00000260 incq         %rax
	0xe9, 0x5b, 0xfe, 0xff, 0xff, //0x00000263 jmp          LBB0_7
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000268 .p2align 4, 0x90
	//0x00000270 LBB0_29
	0x49, 0x83, 0xfd, 0x03, //0x00000270 cmpq         $3, %r13
	0x0f, 0x8e, 0xc3, 0x05, 0x00, 0x00, //0x00000274 jle          LBB0_99
	0x45, 0x8b, 0x31, //0x0000027a movl         (%r9), %r14d
	0x45, 0x89, 0xf7, //0x0000027d movl         %r14d, %r15d
	0x41, 0xf7, 0xd7, //0x00000280 notl         %r15d
	0x41, 0x8d, 0x8e, 0xd0, 0xcf, 0xcf, 0xcf, //0x00000283 leal         $-808464432(%r14), %ecx
	0x41, 0x81, 0xe7, 0x80, 0x80, 0x80, 0x80, //0x0000028a andl         $-2139062144, %r15d
	0x41, 0x85, 0xcf, //0x00000291 testl        %ecx, %r15d
	0x0f, 0x85, 0xe7, 0x04, 0x00, 0x00, //0x00000294 jne          LBB0_90
	0x41, 0x8d, 0x8e, 0x19, 0x19, 0x19, 0x19, //0x0000029a leal         $421075225(%r14), %ecx
	0x44, 0x09, 0xf1, //0x000002a1 orl          %r14d, %ecx
	0xf7, 0xc1, 0x80, 0x80, 0x80, 0x80, //0x000002a4 testl        $-2139062144, %ecx
	0x0f, 0x85, 0xd1, 0x04, 0x00, 0x00, //0x000002aa jne          LBB0_90
	0x44, 0x89, 0xf3, //0x000002b0 movl         %r14d, %ebx
	0x81, 0xe3, 0x7f, 0x7f, 0x7f, 0x7f, //0x000002b3 andl         $2139062143, %ebx
	0xb9, 0xc0, 0xc0, 0xc0, 0xc0, //0x000002b9 movl         $-1061109568, %ecx
	0x29, 0xd9, //0x000002be subl         %ebx, %ecx
	0x44, 0x8d, 0x9b, 0x46, 0x46, 0x46, 0x46, //0x000002c0 leal         $1179010630(%rbx), %r11d
	0x44, 0x21, 0xf9, //0x000002c7 andl         %r15d, %ecx
	0x44, 0x85, 0xd9, //0x000002ca testl        %r11d, %ecx
	0x0f, 0x85, 0xae, 0x04, 0x00, 0x00, //0x000002cd jne          LBB0_90
	0xb9, 0xe0, 0xe0, 0xe0, 0xe0, //0x000002d3 movl         $-522133280, %ecx
	0x29, 0xd9, //0x000002d8 subl         %ebx, %ecx
	0x81, 0xc3, 0x39, 0x39, 0x39, 0x39, //0x000002da addl         $960051513, %ebx
	0x41, 0x21, 0xcf, //0x000002e0 andl         %ecx, %r15d
	0x41, 0x85, 0xdf, //0x000002e3 testl        %ebx, %r15d
	0x0f, 0x85, 0x95, 0x04, 0x00, 0x00, //0x000002e6 jne          LBB0_90
	0x41, 0x0f, 0xce, //0x000002ec bswapl       %r14d
	0x44, 0x89, 0xf1, //0x000002ef movl         %r14d, %ecx
	0xc1, 0xe9, 0x04, //0x000002f2 shrl         $4, %ecx
	0xf7, 0xd1, //0x000002f5 notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x000002f7 andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x000002fd leal         (%rcx,%rcx,8), %ecx
	0x41, 0x81, 0xe6, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000300 andl         $252645135, %r14d
	0x41, 0x01, 0xce, //0x00000307 addl         %ecx, %r14d
	0x44, 0x89, 0xf1, //0x0000030a movl         %r14d, %ecx
	0xc1, 0xe9, 0x04, //0x0000030d shrl         $4, %ecx
	0x44, 0x09, 0xf1, //0x00000310 orl          %r14d, %ecx
	0x44, 0x0f, 0xb6, 0xf9, //0x00000313 movzbl       %cl, %r15d
	0xc1, 0xe9, 0x08, //0x00000317 shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x0000031a andl         $65280, %ecx
	0x41, 0x09, 0xcf, //0x00000320 orl          %ecx, %r15d
	0x4d, 0x8d, 0x75, 0xfc, //0x00000323 leaq         $-4(%r13), %r14
	0x41, 0x81, 0xff, 0x80, 0x00, 0x00, 0x00, //0x00000327 cmpl         $128, %r15d
	0x0f, 0x82, 0x61, 0x03, 0x00, 0x00, //0x0000032e jb           LBB0_75
	0x45, 0x31, 0xe4, //0x00000334 xorl         %r12d, %r12d
	0x4d, 0x85, 0xd2, //0x00000337 testq        %r10, %r10
	0x0f, 0x84, 0x70, 0x01, 0x00, 0x00, //0x0000033a je           LBB0_51
	//0x00000340 LBB0_36
	0x41, 0x81, 0xff, 0x00, 0x08, 0x00, 0x00, //0x00000340 cmpl         $2048, %r15d
	0x0f, 0x82, 0x44, 0xfd, 0xff, 0xff, //0x00000347 jb           LBB0_5
	0x44, 0x89, 0xf9, //0x0000034d movl         %r15d, %ecx
	0x81, 0xe1, 0x00, 0xf8, 0xff, 0xff, //0x00000350 andl         $-2048, %ecx
	0x81, 0xf9, 0x00, 0xd8, 0x00, 0x00, //0x00000356 cmpl         $55296, %ecx
	0x0f, 0x85, 0xae, 0x02, 0x00, 0x00, //0x0000035c jne          LBB0_65
	0x4d, 0x85, 0xf6, //0x00000362 testq        %r14, %r14
	0x0f, 0x8e, 0x61, 0x03, 0x00, 0x00, //0x00000365 jle          LBB0_80
	0x43, 0x80, 0x7c, 0x21, 0x04, 0x5c, //0x0000036b cmpb         $92, $4(%r9,%r12)
	0x0f, 0x85, 0x64, 0x03, 0x00, 0x00, //0x00000371 jne          LBB0_81
	0x41, 0x81, 0xff, 0xff, 0xdb, 0x00, 0x00, //0x00000377 cmpl         $56319, %r15d
	0x0f, 0x87, 0x2a, 0x03, 0x00, 0x00, //0x0000037e ja           LBB0_78
	0x49, 0x83, 0xfe, 0x07, //0x00000384 cmpq         $7, %r14
	0x0f, 0x8c, 0x20, 0x03, 0x00, 0x00, //0x00000388 jl           LBB0_78
	0x43, 0x80, 0x7c, 0x21, 0x05, 0x5c, //0x0000038e cmpb         $92, $5(%r9,%r12)
	0x0f, 0x85, 0x14, 0x03, 0x00, 0x00, //0x00000394 jne          LBB0_78
	0x43, 0x80, 0x7c, 0x21, 0x06, 0x75, //0x0000039a cmpb         $117, $6(%r9,%r12)
	0x0f, 0x85, 0x08, 0x03, 0x00, 0x00, //0x000003a0 jne          LBB0_78
	0x47, 0x8b, 0x5c, 0x21, 0x07, //0x000003a6 movl         $7(%r9,%r12), %r11d
	0x44, 0x89, 0xdb, //0x000003ab movl         %r11d, %ebx
	0xf7, 0xd3, //0x000003ae notl         %ebx
	0x41, 0x8d, 0x8b, 0xd0, 0xcf, 0xcf, 0xcf, //0x000003b0 leal         $-808464432(%r11), %ecx
	0x81, 0xe3, 0x80, 0x80, 0x80, 0x80, //0x000003b7 andl         $-2139062144, %ebx
	0x89, 0x5d, 0xcc, //0x000003bd movl         %ebx, $-52(%rbp)
	0x85, 0xcb, //0x000003c0 testl        %ecx, %ebx
	0x0f, 0x85, 0xbe, 0x04, 0x00, 0x00, //0x000003c2 jne          LBB0_104
	0x41, 0x8d, 0x8b, 0x19, 0x19, 0x19, 0x19, //0x000003c8 leal         $421075225(%r11), %ecx
	0x44, 0x09, 0xd9, //0x000003cf orl          %r11d, %ecx
	0xf7, 0xc1, 0x80, 0x80, 0x80, 0x80, //0x000003d2 testl        $-2139062144, %ecx
	0x0f, 0x85, 0xa8, 0x04, 0x00, 0x00, //0x000003d8 jne          LBB0_104
	0x44, 0x89, 0xdb, //0x000003de movl         %r11d, %ebx
	0x81, 0xe3, 0x7f, 0x7f, 0x7f, 0x7f, //0x000003e1 andl         $2139062143, %ebx
	0xb9, 0xc0, 0xc0, 0xc0, 0xc0, //0x000003e7 movl         $-1061109568, %ecx
	0x29, 0xd9, //0x000003ec subl         %ebx, %ecx
	0x4c, 0x89, 0x5d, 0xc0, //0x000003ee movq         %r11, $-64(%rbp)
	0x44, 0x8d, 0x9b, 0x46, 0x46, 0x46, 0x46, //0x000003f2 leal         $1179010630(%rbx), %r11d
	0x23, 0x4d, 0xcc, //0x000003f9 andl         $-52(%rbp), %ecx
	0x44, 0x85, 0xd9, //0x000003fc testl        %r11d, %ecx
	0x4c, 0x8b, 0x5d, 0xc0, //0x000003ff movq         $-64(%rbp), %r11
	0x0f, 0x85, 0x7d, 0x04, 0x00, 0x00, //0x00000403 jne          LBB0_104
	0xb9, 0xe0, 0xe0, 0xe0, 0xe0, //0x00000409 movl         $-522133280, %ecx
	0x29, 0xd9, //0x0000040e subl         %ebx, %ecx
	0x89, 0x4d, 0xc8, //0x00000410 movl         %ecx, $-56(%rbp)
	0x81, 0xc3, 0x39, 0x39, 0x39, 0x39, //0x00000413 addl         $960051513, %ebx
	0x8b, 0x4d, 0xcc, //0x00000419 movl         $-52(%rbp), %ecx
	0x23, 0x4d, 0xc8, //0x0000041c andl         $-56(%rbp), %ecx
	0x85, 0xd9, //0x0000041f testl        %ebx, %ecx
	0x0f, 0x85, 0x5f, 0x04, 0x00, 0x00, //0x00000421 jne          LBB0_104
	0x41, 0x0f, 0xcb, //0x00000427 bswapl       %r11d
	0x44, 0x89, 0xd9, //0x0000042a movl         %r11d, %ecx
	0xc1, 0xe9, 0x04, //0x0000042d shrl         $4, %ecx
	0xf7, 0xd1, //0x00000430 notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x00000432 andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x00000438 leal         (%rcx,%rcx,8), %ecx
	0x41, 0x81, 0xe3, 0x0f, 0x0f, 0x0f, 0x0f, //0x0000043b andl         $252645135, %r11d
	0x41, 0x01, 0xcb, //0x00000442 addl         %ecx, %r11d
	0x44, 0x89, 0xd9, //0x00000445 movl         %r11d, %ecx
	0xc1, 0xe9, 0x04, //0x00000448 shrl         $4, %ecx
	0x44, 0x09, 0xd9, //0x0000044b orl          %r11d, %ecx
	0x89, 0xcb, //0x0000044e movl         %ecx, %ebx
	0xc1, 0xeb, 0x08, //0x00000450 shrl         $8, %ebx
	0x81, 0xe3, 0x00, 0xff, 0x00, 0x00, //0x00000453 andl         $65280, %ebx
	0x44, 0x0f, 0xb6, 0xd9, //0x00000459 movzbl       %cl, %r11d
	0x41, 0x09, 0xdb, //0x0000045d orl          %ebx, %r11d
	0x81, 0xe1, 0x00, 0x00, 0xfc, 0x00, //0x00000460 andl         $16515072, %ecx
	0x81, 0xf9, 0x00, 0x00, 0xdc, 0x00, //0x00000466 cmpl         $14417920, %ecx
	0x0f, 0x84, 0x97, 0x02, 0x00, 0x00, //0x0000046c je           LBB0_85
	0x41, 0xf6, 0xc0, 0x02, //0x00000472 testb        $2, %r8b
	0x0f, 0x84, 0x1d, 0x05, 0x00, 0x00, //0x00000476 je           LBB0_119
	0x49, 0x83, 0xc6, 0xf9, //0x0000047c addq         $-7, %r14
	0x66, 0xc7, 0x00, 0xef, 0xbf, //0x00000480 movw         $-16401, (%rax)
	0xc6, 0x40, 0x02, 0xbd, //0x00000485 movb         $-67, $2(%rax)
	0x48, 0x83, 0xc0, 0x03, //0x00000489 addq         $3, %rax
	0x49, 0x83, 0xc4, 0x07, //0x0000048d addq         $7, %r12
	0x45, 0x89, 0xdf, //0x00000491 movl         %r11d, %r15d
	0x41, 0x83, 0xfb, 0x7f, //0x00000494 cmpl         $127, %r11d
	0x0f, 0x87, 0xa2, 0xfe, 0xff, 0xff, //0x00000498 ja           LBB0_36
	0xe9, 0x59, 0x01, 0x00, 0x00, //0x0000049e jmp          LBB0_64
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000004a3 .p2align 4, 0x90
	//0x000004b0 LBB0_51
	0x41, 0x81, 0xff, 0x00, 0x08, 0x00, 0x00, //0x000004b0 cmpl         $2048, %r15d
	0x0f, 0x82, 0xd4, 0xfb, 0xff, 0xff, //0x000004b7 jb           LBB0_5
	0x44, 0x89, 0xf9, //0x000004bd movl         %r15d, %ecx
	0x81, 0xe1, 0x00, 0xf8, 0xff, 0xff, //0x000004c0 andl         $-2048, %ecx
	0x81, 0xf9, 0x00, 0xd8, 0x00, 0x00, //0x000004c6 cmpl         $55296, %ecx
	0x0f, 0x85, 0x3e, 0x01, 0x00, 0x00, //0x000004cc jne          LBB0_65
	0x41, 0x81, 0xff, 0xff, 0xdb, 0x00, 0x00, //0x000004d2 cmpl         $56319, %r15d
	0x0f, 0x87, 0xc5, 0x01, 0x00, 0x00, //0x000004d9 ja           LBB0_77
	0x49, 0x83, 0xfe, 0x06, //0x000004df cmpq         $6, %r14
	0x0f, 0x8c, 0xbb, 0x01, 0x00, 0x00, //0x000004e3 jl           LBB0_77
	0x43, 0x80, 0x7c, 0x21, 0x04, 0x5c, //0x000004e9 cmpb         $92, $4(%r9,%r12)
	0x0f, 0x85, 0xaf, 0x01, 0x00, 0x00, //0x000004ef jne          LBB0_77
	0x43, 0x80, 0x7c, 0x21, 0x05, 0x75, //0x000004f5 cmpb         $117, $5(%r9,%r12)
	0x0f, 0x85, 0xa3, 0x01, 0x00, 0x00, //0x000004fb jne          LBB0_77
	0x47, 0x8b, 0x5c, 0x21, 0x06, //0x00000501 movl         $6(%r9,%r12), %r11d
	0x44, 0x89, 0xdb, //0x00000506 movl         %r11d, %ebx
	0xf7, 0xd3, //0x00000509 notl         %ebx
	0x41, 0x8d, 0x8b, 0xd0, 0xcf, 0xcf, 0xcf, //0x0000050b leal         $-808464432(%r11), %ecx
	0x81, 0xe3, 0x80, 0x80, 0x80, 0x80, //0x00000512 andl         $-2139062144, %ebx
	0x89, 0x5d, 0xcc, //0x00000518 movl         %ebx, $-52(%rbp)
	0x85, 0xcb, //0x0000051b testl        %ecx, %ebx
	0x0f, 0x85, 0x59, 0x03, 0x00, 0x00, //0x0000051d jne          LBB0_103
	0x41, 0x8d, 0x8b, 0x19, 0x19, 0x19, 0x19, //0x00000523 leal         $421075225(%r11), %ecx
	0x44, 0x09, 0xd9, //0x0000052a orl          %r11d, %ecx
	0xf7, 0xc1, 0x80, 0x80, 0x80, 0x80, //0x0000052d testl        $-2139062144, %ecx
	0x0f, 0x85, 0x43, 0x03, 0x00, 0x00, //0x00000533 jne          LBB0_103
	0x44, 0x89, 0xdb, //0x00000539 movl         %r11d, %ebx
	0x81, 0xe3, 0x7f, 0x7f, 0x7f, 0x7f, //0x0000053c andl         $2139062143, %ebx
	0xb9, 0xc0, 0xc0, 0xc0, 0xc0, //0x00000542 movl         $-1061109568, %ecx
	0x29, 0xd9, //0x00000547 subl         %ebx, %ecx
	0x4c, 0x89, 0x5d, 0xc0, //0x00000549 movq         %r11, $-64(%rbp)
	0x44, 0x8d, 0x9b, 0x46, 0x46, 0x46, 0x46, //0x0000054d leal         $1179010630(%rbx), %r11d
	0x23, 0x4d, 0xcc, //0x00000554 andl         $-52(%rbp), %ecx
	0x44, 0x85, 0xd9, //0x00000557 testl        %r11d, %ecx
	0x4c, 0x8b, 0x5d, 0xc0, //0x0000055a movq         $-64(%rbp), %r11
	0x0f, 0x85, 0x18, 0x03, 0x00, 0x00, //0x0000055e jne          LBB0_103
	0xb9, 0xe0, 0xe0, 0xe0, 0xe0, //0x00000564 movl         $-522133280, %ecx
	0x29, 0xd9, //0x00000569 subl         %ebx, %ecx
	0x89, 0x4d, 0xc8, //0x0000056b movl         %ecx, $-56(%rbp)
	0x81, 0xc3, 0x39, 0x39, 0x39, 0x39, //0x0000056e addl         $960051513, %ebx
	0x8b, 0x4d, 0xcc, //0x00000574 movl         $-52(%rbp), %ecx
	0x23, 0x4d, 0xc8, //0x00000577 andl         $-56(%rbp), %ecx
	0x85, 0xd9, //0x0000057a testl        %ebx, %ecx
	0x0f, 0x85, 0xfa, 0x02, 0x00, 0x00, //0x0000057c jne          LBB0_103
	0x41, 0x0f, 0xcb, //0x00000582 bswapl       %r11d
	0x44, 0x89, 0xd9, //0x00000585 movl         %r11d, %ecx
	0xc1, 0xe9, 0x04, //0x00000588 shrl         $4, %ecx
	0xf7, 0xd1, //0x0000058b notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x0000058d andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x00000593 leal         (%rcx,%rcx,8), %ecx
	0x41, 0x81, 0xe3, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000596 andl         $252645135, %r11d
	0x41, 0x01, 0xcb, //0x0000059d addl         %ecx, %r11d
	0x44, 0x89, 0xd9, //0x000005a0 movl         %r11d, %ecx
	0xc1, 0xe9, 0x04, //0x000005a3 shrl         $4, %ecx
	0x44, 0x09, 0xd9, //0x000005a6 orl          %r11d, %ecx
	0x89, 0xcb, //0x000005a9 movl         %ecx, %ebx
	0xc1, 0xeb, 0x08, //0x000005ab shrl         $8, %ebx
	0x81, 0xe3, 0x00, 0xff, 0x00, 0x00, //0x000005ae andl         $65280, %ebx
	0x44, 0x0f, 0xb6, 0xd9, //0x000005b4 movzbl       %cl, %r11d
	0x41, 0x09, 0xdb, //0x000005b8 orl          %ebx, %r11d
	0x81, 0xe1, 0x00, 0x00, 0xfc, 0x00, //0x000005bb andl         $16515072, %ecx
	0x81, 0xf9, 0x00, 0x00, 0xdc, 0x00, //0x000005c1 cmpl         $14417920, %ecx
	0x0f, 0x84, 0x2b, 0x01, 0x00, 0x00, //0x000005c7 je           LBB0_84
	0x41, 0xf6, 0xc0, 0x02, //0x000005cd testb        $2, %r8b
	0x0f, 0x84, 0xb8, 0x03, 0x00, 0x00, //0x000005d1 je           LBB0_118
	0x49, 0x83, 0xc6, 0xfa, //0x000005d7 addq         $-6, %r14
	0x66, 0xc7, 0x00, 0xef, 0xbf, //0x000005db movw         $-16401, (%rax)
	0xc6, 0x40, 0x02, 0xbd, //0x000005e0 movb         $-67, $2(%rax)
	0x48, 0x83, 0xc0, 0x03, //0x000005e4 addq         $3, %rax
	0x49, 0x83, 0xc4, 0x06, //0x000005e8 addq         $6, %r12
	0x45, 0x89, 0xdf, //0x000005ec movl         %r11d, %r15d
	0x41, 0x81, 0xfb, 0x80, 0x00, 0x00, 0x00, //0x000005ef cmpl         $128, %r11d
	0x0f, 0x83, 0xb4, 0xfe, 0xff, 0xff, //0x000005f6 jae          LBB0_51
	//0x000005fc LBB0_64
	0x4f, 0x8d, 0x4c, 0x21, 0x04, //0x000005fc leaq         $4(%r9,%r12), %r9
	0x45, 0x89, 0xdf, //0x00000601 movl         %r11d, %r15d
	0xe9, 0x90, 0x00, 0x00, 0x00, //0x00000604 jmp          LBB0_76
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000609 .p2align 4, 0x90
	//0x00000610 LBB0_65
	0x4f, 0x8d, 0x4c, 0x21, 0x04, //0x00000610 leaq         $4(%r9,%r12), %r9
	0x44, 0x89, 0xf9, //0x00000615 movl         %r15d, %ecx
	0xc1, 0xe9, 0x0c, //0x00000618 shrl         $12, %ecx
	0x80, 0xc9, 0xe0, //0x0000061b orb          $-32, %cl
	0x88, 0x08, //0x0000061e movb         %cl, (%rax)
	0x44, 0x89, 0xf9, //0x00000620 movl         %r15d, %ecx
	0xc1, 0xe9, 0x06, //0x00000623 shrl         $6, %ecx
	0x80, 0xe1, 0x3f, //0x00000626 andb         $63, %cl
	0x80, 0xc9, 0x80, //0x00000629 orb          $-128, %cl
	0x88, 0x48, 0x01, //0x0000062c movb         %cl, $1(%rax)
	0x41, 0x80, 0xe7, 0x3f, //0x0000062f andb         $63, %r15b
	0x41, 0x80, 0xcf, 0x80, //0x00000633 orb          $-128, %r15b
	0x44, 0x88, 0x78, 0x02, //0x00000637 movb         %r15b, $2(%rax)
	//0x0000063b LBB0_66
	0x48, 0x83, 0xc0, 0x03, //0x0000063b addq         $3, %rax
	0xe9, 0x7c, 0xfa, 0xff, 0xff, //0x0000063f jmp          LBB0_6
	//0x00000644 LBB0_67
	0x45, 0x85, 0xed, //0x00000644 testl        %r13d, %r13d
	0x0f, 0x84, 0xf0, 0x01, 0x00, 0x00, //0x00000647 je           LBB0_99
	0x41, 0x80, 0x79, 0xff, 0x5c, //0x0000064d cmpb         $92, $-1(%r9)
	0x0f, 0x85, 0x19, 0x02, 0x00, 0x00, //0x00000652 jne          LBB0_102
	0x41, 0x80, 0x39, 0x5c, //0x00000658 cmpb         $92, (%r9)
	0x0f, 0x85, 0x28, 0x00, 0x00, 0x00, //0x0000065c jne          LBB0_74
	0x41, 0x83, 0xfd, 0x01, //0x00000662 cmpl         $1, %r13d
	0x0f, 0x8e, 0xd1, 0x01, 0x00, 0x00, //0x00000666 jle          LBB0_99
	0x45, 0x8a, 0x59, 0x01, //0x0000066c movb         $1(%r9), %r11b
	0x41, 0x80, 0xfb, 0x22, //0x00000670 cmpb         $34, %r11b
	0x0f, 0x84, 0x0a, 0x00, 0x00, 0x00, //0x00000674 je           LBB0_73
	0x41, 0x80, 0xfb, 0x5c, //0x0000067a cmpb         $92, %r11b
	0x0f, 0x85, 0xd8, 0x02, 0x00, 0x00, //0x0000067e jne          LBB0_114
	//0x00000684 LBB0_73
	0x49, 0xff, 0xc1, //0x00000684 incq         %r9
	0x49, 0xff, 0xcd, //0x00000687 decq         %r13
	//0x0000068a LBB0_74
	0x49, 0xff, 0xc1, //0x0000068a incq         %r9
	0x49, 0xff, 0xcd, //0x0000068d decq         %r13
	0xe9, 0xa6, 0xfb, 0xff, 0xff, //0x00000690 jmp          LBB0_26
	//0x00000695 LBB0_75
	0x49, 0x83, 0xc1, 0x04, //0x00000695 addq         $4, %r9
	//0x00000699 LBB0_76
	0x44, 0x88, 0x38, //0x00000699 movb         %r15b, (%rax)
	0x48, 0xff, 0xc0, //0x0000069c incq         %rax
	0xe9, 0x1c, 0xfa, 0xff, 0xff, //0x0000069f jmp          LBB0_6
	//0x000006a4 LBB0_77
	0x4f, 0x8d, 0x4c, 0x21, 0x04, //0x000006a4 leaq         $4(%r9,%r12), %r9
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x000006a9 jmp          LBB0_79
	//0x000006ae LBB0_78
	0x4f, 0x8d, 0x4c, 0x21, 0x05, //0x000006ae leaq         $5(%r9,%r12), %r9
	0x4d, 0x29, 0xe5, //0x000006b3 subq         %r12, %r13
	0x49, 0x83, 0xc5, 0xfb, //0x000006b6 addq         $-5, %r13
	0x4d, 0x89, 0xee, //0x000006ba movq         %r13, %r14
	//0x000006bd LBB0_79
	0x41, 0xf6, 0xc0, 0x02, //0x000006bd testb        $2, %r8b
	0x0f, 0x85, 0x23, 0x00, 0x00, 0x00, //0x000006c1 jne          LBB0_83
	0xe9, 0xec, 0x02, 0x00, 0x00, //0x000006c7 jmp          LBB0_121
	//0x000006cc LBB0_80
	0x41, 0xf6, 0xc0, 0x02, //0x000006cc testb        $2, %r8b
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x000006d0 jne          LBB0_82
	0xe9, 0x62, 0x01, 0x00, 0x00, //0x000006d6 jmp          LBB0_99
	//0x000006db LBB0_81
	0x41, 0xf6, 0xc0, 0x02, //0x000006db testb        $2, %r8b
	0x0f, 0x84, 0xe0, 0x02, 0x00, 0x00, //0x000006df je           LBB0_122
	//0x000006e5 LBB0_82
	0x4f, 0x8d, 0x4c, 0x21, 0x04, //0x000006e5 leaq         $4(%r9,%r12), %r9
	//0x000006ea LBB0_83
	0x66, 0xc7, 0x00, 0xef, 0xbf, //0x000006ea movw         $-16401, (%rax)
	0xc6, 0x40, 0x02, 0xbd, //0x000006ef movb         $-67, $2(%rax)
	0xe9, 0x43, 0xff, 0xff, 0xff, //0x000006f3 jmp          LBB0_66
	//0x000006f8 LBB0_84
	0x4f, 0x8d, 0x4c, 0x21, 0x0a, //0x000006f8 leaq         $10(%r9,%r12), %r9
	0x4d, 0x29, 0xe5, //0x000006fd subq         %r12, %r13
	0x49, 0x83, 0xc5, 0xf6, //0x00000700 addq         $-10, %r13
	0xe9, 0x0c, 0x00, 0x00, 0x00, //0x00000704 jmp          LBB0_86
	//0x00000709 LBB0_85
	0x4f, 0x8d, 0x4c, 0x21, 0x0b, //0x00000709 leaq         $11(%r9,%r12), %r9
	0x4d, 0x29, 0xe5, //0x0000070e subq         %r12, %r13
	0x49, 0x83, 0xc5, 0xf5, //0x00000711 addq         $-11, %r13
	//0x00000715 LBB0_86
	0x41, 0xc1, 0xe7, 0x0a, //0x00000715 shll         $10, %r15d
	0x43, 0x8d, 0x9c, 0x1f, 0x00, 0x24, 0xa0, 0xfc, //0x00000719 leal         $-56613888(%r15,%r11), %ebx
	0x81, 0xfb, 0x00, 0x00, 0x11, 0x00, //0x00000721 cmpl         $1114112, %ebx
	0x0f, 0x82, 0x1c, 0x00, 0x00, 0x00, //0x00000727 jb           LBB0_89
	0x41, 0xf6, 0xc0, 0x02, //0x0000072d testb        $2, %r8b
	0x0f, 0x84, 0x3e, 0x02, 0x00, 0x00, //0x00000731 je           LBB0_116
	0x66, 0xc7, 0x00, 0xef, 0xbf, //0x00000737 movw         $-16401, (%rax)
	0xc6, 0x40, 0x02, 0xbd, //0x0000073c movb         $-67, $2(%rax)
	0x48, 0x83, 0xc0, 0x03, //0x00000740 addq         $3, %rax
	0xe9, 0x7a, 0xf9, 0xff, 0xff, //0x00000744 jmp          LBB0_7
	//0x00000749 LBB0_89
	0x89, 0xd9, //0x00000749 movl         %ebx, %ecx
	0xc1, 0xe9, 0x12, //0x0000074b shrl         $18, %ecx
	0x80, 0xc9, 0xf0, //0x0000074e orb          $-16, %cl
	0x88, 0x08, //0x00000751 movb         %cl, (%rax)
	0x89, 0xd9, //0x00000753 movl         %ebx, %ecx
	0xc1, 0xe9, 0x0c, //0x00000755 shrl         $12, %ecx
	0x80, 0xe1, 0x3f, //0x00000758 andb         $63, %cl
	0x80, 0xc9, 0x80, //0x0000075b orb          $-128, %cl
	0x88, 0x48, 0x01, //0x0000075e movb         %cl, $1(%rax)
	0x89, 0xd9, //0x00000761 movl         %ebx, %ecx
	0xc1, 0xe9, 0x06, //0x00000763 shrl         $6, %ecx
	0x80, 0xe1, 0x3f, //0x00000766 andb         $63, %cl
	0x80, 0xc9, 0x80, //0x00000769 orb          $-128, %cl
	0x88, 0x48, 0x02, //0x0000076c movb         %cl, $2(%rax)
	0x80, 0xe3, 0x3f, //0x0000076f andb         $63, %bl
	0x80, 0xcb, 0x80, //0x00000772 orb          $-128, %bl
	0x88, 0x58, 0x03, //0x00000775 movb         %bl, $3(%rax)
	0x48, 0x83, 0xc0, 0x04, //0x00000778 addq         $4, %rax
	0xe9, 0x42, 0xf9, 0xff, 0xff, //0x0000077c jmp          LBB0_7
	//0x00000781 LBB0_90
	0x4c, 0x89, 0xca, //0x00000781 movq         %r9, %rdx
	0x48, 0x29, 0xfa, //0x00000784 subq         %rdi, %rdx
	0x48, 0x8b, 0x7d, 0xd0, //0x00000787 movq         $-48(%rbp), %rdi
	0x48, 0x89, 0x17, //0x0000078b movq         %rdx, (%rdi)
	0x41, 0x8a, 0x31, //0x0000078e movb         (%r9), %sil
	0x8d, 0x4e, 0xd0, //0x00000791 leal         $-48(%rsi), %ecx
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000794 movq         $-2, %rax
	0x80, 0xf9, 0x0a, //0x0000079b cmpb         $10, %cl
	0x0f, 0x82, 0x12, 0x00, 0x00, 0x00, //0x0000079e jb           LBB0_92
	0x40, 0x80, 0xe6, 0xdf, //0x000007a4 andb         $-33, %sil
	0x40, 0x80, 0xc6, 0xbf, //0x000007a8 addb         $-65, %sil
	0x40, 0x80, 0xfe, 0x05, //0x000007ac cmpb         $5, %sil
	0x0f, 0x87, 0xc9, 0xf8, 0xff, 0xff, //0x000007b0 ja           LBB0_4
	//0x000007b6 LBB0_92
	0x48, 0x8d, 0x4a, 0x01, //0x000007b6 leaq         $1(%rdx), %rcx
	0x48, 0x89, 0x0f, //0x000007ba movq         %rcx, (%rdi)
	0x41, 0x8a, 0x71, 0x01, //0x000007bd movb         $1(%r9), %sil
	0x8d, 0x4e, 0xd0, //0x000007c1 leal         $-48(%rsi), %ecx
	0x80, 0xf9, 0x0a, //0x000007c4 cmpb         $10, %cl
	0x0f, 0x82, 0x12, 0x00, 0x00, 0x00, //0x000007c7 jb           LBB0_94
	0x40, 0x80, 0xe6, 0xdf, //0x000007cd andb         $-33, %sil
	0x40, 0x80, 0xc6, 0xbf, //0x000007d1 addb         $-65, %sil
	0x40, 0x80, 0xfe, 0x05, //0x000007d5 cmpb         $5, %sil
	0x0f, 0x87, 0xa0, 0xf8, 0xff, 0xff, //0x000007d9 ja           LBB0_4
	//0x000007df LBB0_94
	0x48, 0x8d, 0x4a, 0x02, //0x000007df leaq         $2(%rdx), %rcx
	0x48, 0x89, 0x0f, //0x000007e3 movq         %rcx, (%rdi)
	0x41, 0x8a, 0x71, 0x02, //0x000007e6 movb         $2(%r9), %sil
	0x8d, 0x4e, 0xd0, //0x000007ea leal         $-48(%rsi), %ecx
	0x80, 0xf9, 0x0a, //0x000007ed cmpb         $10, %cl
	0x0f, 0x82, 0x12, 0x00, 0x00, 0x00, //0x000007f0 jb           LBB0_96
	0x40, 0x80, 0xe6, 0xdf, //0x000007f6 andb         $-33, %sil
	0x40, 0x80, 0xc6, 0xbf, //0x000007fa addb         $-65, %sil
	0x40, 0x80, 0xfe, 0x05, //0x000007fe cmpb         $5, %sil
	0x0f, 0x87, 0x77, 0xf8, 0xff, 0xff, //0x00000802 ja           LBB0_4
	//0x00000808 LBB0_96
	0x48, 0x8d, 0x4a, 0x03, //0x00000808 leaq         $3(%rdx), %rcx
	0x48, 0x89, 0x0f, //0x0000080c movq         %rcx, (%rdi)
	0x41, 0x8a, 0x71, 0x03, //0x0000080f movb         $3(%r9), %sil
	0x8d, 0x4e, 0xd0, //0x00000813 leal         $-48(%rsi), %ecx
	0x80, 0xf9, 0x0a, //0x00000816 cmpb         $10, %cl
	0x0f, 0x82, 0x12, 0x00, 0x00, 0x00, //0x00000819 jb           LBB0_98
	0x40, 0x80, 0xe6, 0xdf, //0x0000081f andb         $-33, %sil
	0x40, 0x80, 0xc6, 0xbf, //0x00000823 addb         $-65, %sil
	0x40, 0x80, 0xfe, 0x05, //0x00000827 cmpb         $5, %sil
	0x0f, 0x87, 0x4e, 0xf8, 0xff, 0xff, //0x0000082b ja           LBB0_4
	//0x00000831 LBB0_98
	0x48, 0x83, 0xc2, 0x04, //0x00000831 addq         $4, %rdx
	0x48, 0x89, 0x17, //0x00000835 movq         %rdx, (%rdi)
	0xe9, 0x42, 0xf8, 0xff, 0xff, //0x00000838 jmp          LBB0_4
	//0x0000083d LBB0_99
	0x48, 0x8b, 0x45, 0xd0, //0x0000083d movq         $-48(%rbp), %rax
	0x48, 0x89, 0x30, //0x00000841 movq         %rsi, (%rax)
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000844 movq         $-1, %rax
	0xe9, 0x2f, 0xf8, 0xff, 0xff, //0x0000084b jmp          LBB0_4
	//0x00000850 LBB0_100
	0x48, 0xf7, 0xd7, //0x00000850 notq         %rdi
	0x49, 0x01, 0xf9, //0x00000853 addq         %rdi, %r9
	0x48, 0x8b, 0x45, 0xd0, //0x00000856 movq         $-48(%rbp), %rax
	0x4c, 0x89, 0x08, //0x0000085a movq         %r9, (%rax)
	0x48, 0xc7, 0xc0, 0xfd, 0xff, 0xff, 0xff, //0x0000085d movq         $-3, %rax
	0xe9, 0x16, 0xf8, 0xff, 0xff, //0x00000864 jmp          LBB0_4
	//0x00000869 LBB0_101
	0x45, 0x31, 0xed, //0x00000869 xorl         %r13d, %r13d
	0xe9, 0x08, 0xf8, 0xff, 0xff, //0x0000086c jmp          LBB0_3
	//0x00000871 LBB0_102
	0x48, 0xf7, 0xd7, //0x00000871 notq         %rdi
	0x49, 0x01, 0xf9, //0x00000874 addq         %rdi, %r9
	0xe9, 0xe6, 0x00, 0x00, 0x00, //0x00000877 jmp          LBB0_115
	//0x0000087c LBB0_103
	0x4b, 0x8d, 0x74, 0x21, 0x04, //0x0000087c leaq         $4(%r9,%r12), %rsi
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x00000881 jmp          LBB0_105
	//0x00000886 LBB0_104
	0x4b, 0x8d, 0x74, 0x21, 0x05, //0x00000886 leaq         $5(%r9,%r12), %rsi
	//0x0000088b LBB0_105
	0x48, 0x89, 0xf2, //0x0000088b movq         %rsi, %rdx
	0x48, 0x29, 0xfa, //0x0000088e subq         %rdi, %rdx
	0x48, 0x83, 0xc2, 0x02, //0x00000891 addq         $2, %rdx
	0x48, 0x8b, 0x45, 0xd0, //0x00000895 movq         $-48(%rbp), %rax
	0x48, 0x89, 0x10, //0x00000899 movq         %rdx, (%rax)
	0x40, 0x8a, 0x7e, 0x02, //0x0000089c movb         $2(%rsi), %dil
	0x8d, 0x4f, 0xd0, //0x000008a0 leal         $-48(%rdi), %ecx
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x000008a3 movq         $-2, %rax
	0x80, 0xf9, 0x0a, //0x000008aa cmpb         $10, %cl
	0x0f, 0x82, 0x12, 0x00, 0x00, 0x00, //0x000008ad jb           LBB0_107
	0x40, 0x80, 0xe7, 0xdf, //0x000008b3 andb         $-33, %dil
	0x40, 0x80, 0xc7, 0xbf, //0x000008b7 addb         $-65, %dil
	0x40, 0x80, 0xff, 0x05, //0x000008bb cmpb         $5, %dil
	0x0f, 0x87, 0xba, 0xf7, 0xff, 0xff, //0x000008bf ja           LBB0_4
	//0x000008c5 LBB0_107
	0x48, 0x8d, 0x4a, 0x01, //0x000008c5 leaq         $1(%rdx), %rcx
	0x48, 0x8b, 0x7d, 0xd0, //0x000008c9 movq         $-48(%rbp), %rdi
	0x48, 0x89, 0x0f, //0x000008cd movq         %rcx, (%rdi)
	0x40, 0x8a, 0x7e, 0x03, //0x000008d0 movb         $3(%rsi), %dil
	0x8d, 0x4f, 0xd0, //0x000008d4 leal         $-48(%rdi), %ecx
	0x80, 0xf9, 0x0a, //0x000008d7 cmpb         $10, %cl
	0x0f, 0x82, 0x12, 0x00, 0x00, 0x00, //0x000008da jb           LBB0_109
	0x40, 0x80, 0xe7, 0xdf, //0x000008e0 andb         $-33, %dil
	0x40, 0x80, 0xc7, 0xbf, //0x000008e4 addb         $-65, %dil
	0x40, 0x80, 0xff, 0x05, //0x000008e8 cmpb         $5, %dil
	0x0f, 0x87, 0x8d, 0xf7, 0xff, 0xff, //0x000008ec ja           LBB0_4
	//0x000008f2 LBB0_109
	0x48, 0x8d, 0x4a, 0x02, //0x000008f2 leaq         $2(%rdx), %rcx
	0x48, 0x8b, 0x7d, 0xd0, //0x000008f6 movq         $-48(%rbp), %rdi
	0x48, 0x89, 0x0f, //0x000008fa movq         %rcx, (%rdi)
	0x40, 0x8a, 0x7e, 0x04, //0x000008fd movb         $4(%rsi), %dil
	0x8d, 0x4f, 0xd0, //0x00000901 leal         $-48(%rdi), %ecx
	0x80, 0xf9, 0x0a, //0x00000904 cmpb         $10, %cl
	0x0f, 0x82, 0x12, 0x00, 0x00, 0x00, //0x00000907 jb           LBB0_111
	0x40, 0x80, 0xe7, 0xdf, //0x0000090d andb         $-33, %dil
	0x40, 0x80, 0xc7, 0xbf, //0x00000911 addb         $-65, %dil
	0x40, 0x80, 0xff, 0x05, //0x00000915 cmpb         $5, %dil
	0x0f, 0x87, 0x60, 0xf7, 0xff, 0xff, //0x00000919 ja           LBB0_4
	//0x0000091f LBB0_111
	0x48, 0x8d, 0x4a, 0x03, //0x0000091f leaq         $3(%rdx), %rcx
	0x48, 0x8b, 0x7d, 0xd0, //0x00000923 movq         $-48(%rbp), %rdi
	0x48, 0x89, 0x0f, //0x00000927 movq         %rcx, (%rdi)
	0x40, 0x8a, 0x76, 0x05, //0x0000092a movb         $5(%rsi), %sil
	0x8d, 0x4e, 0xd0, //0x0000092e leal         $-48(%rsi), %ecx
	0x80, 0xf9, 0x0a, //0x00000931 cmpb         $10, %cl
	0x0f, 0x82, 0x12, 0x00, 0x00, 0x00, //0x00000934 jb           LBB0_113
	0x40, 0x80, 0xe6, 0xdf, //0x0000093a andb         $-33, %sil
	0x40, 0x80, 0xc6, 0xbf, //0x0000093e addb         $-65, %sil
	0x40, 0x80, 0xfe, 0x05, //0x00000942 cmpb         $5, %sil
	0x0f, 0x87, 0x33, 0xf7, 0xff, 0xff, //0x00000946 ja           LBB0_4
	//0x0000094c LBB0_113
	0x48, 0x83, 0xc2, 0x04, //0x0000094c addq         $4, %rdx
	0x48, 0x8b, 0x4d, 0xd0, //0x00000950 movq         $-48(%rbp), %rcx
	0x48, 0x89, 0x11, //0x00000954 movq         %rdx, (%rcx)
	0xe9, 0x23, 0xf7, 0xff, 0xff, //0x00000957 jmp          LBB0_4
	//0x0000095c LBB0_114
	0x49, 0x29, 0xf9, //0x0000095c subq         %rdi, %r9
	0x49, 0xff, 0xc1, //0x0000095f incq         %r9
	//0x00000962 LBB0_115
	0x48, 0x8b, 0x45, 0xd0, //0x00000962 movq         $-48(%rbp), %rax
	0x4c, 0x89, 0x08, //0x00000966 movq         %r9, (%rax)
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000969 movq         $-2, %rax
	0xe9, 0x0a, 0xf7, 0xff, 0xff, //0x00000970 jmp          LBB0_4
	//0x00000975 LBB0_116
	0x49, 0x29, 0xf9, //0x00000975 subq         %rdi, %r9
	0x49, 0x83, 0xc1, 0xfc, //0x00000978 addq         $-4, %r9
	//0x0000097c LBB0_117
	0x48, 0x8b, 0x45, 0xd0, //0x0000097c movq         $-48(%rbp), %rax
	0x4c, 0x89, 0x08, //0x00000980 movq         %r9, (%rax)
	0x48, 0xc7, 0xc0, 0xfc, 0xff, 0xff, 0xff, //0x00000983 movq         $-4, %rax
	0xe9, 0xf0, 0xf6, 0xff, 0xff, //0x0000098a jmp          LBB0_4
	//0x0000098f LBB0_118
	0x4b, 0x8d, 0x44, 0x21, 0x0a, //0x0000098f leaq         $10(%r9,%r12), %rax
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x00000994 jmp          LBB0_120
	//0x00000999 LBB0_119
	0x4b, 0x8d, 0x44, 0x21, 0x0b, //0x00000999 leaq         $11(%r9,%r12), %rax
	//0x0000099e LBB0_120
	0x48, 0x29, 0xf8, //0x0000099e subq         %rdi, %rax
	0x48, 0x83, 0xc0, 0xfc, //0x000009a1 addq         $-4, %rax
	0x48, 0x8b, 0x4d, 0xd0, //0x000009a5 movq         $-48(%rbp), %rcx
	0x48, 0x89, 0x01, //0x000009a9 movq         %rax, (%rcx)
	0x48, 0xc7, 0xc0, 0xfc, 0xff, 0xff, 0xff, //0x000009ac movq         $-4, %rax
	0xe9, 0xc7, 0xf6, 0xff, 0xff, //0x000009b3 jmp          LBB0_4
	//0x000009b8 LBB0_121
	0x49, 0x8d, 0x44, 0x3a, 0x04, //0x000009b8 leaq         $4(%r10,%rdi), %rax
	0x49, 0x29, 0xc1, //0x000009bd subq         %rax, %r9
	0xe9, 0xb7, 0xff, 0xff, 0xff, //0x000009c0 jmp          LBB0_117
	//0x000009c5 LBB0_122
	0x4d, 0x01, 0xe1, //0x000009c5 addq         %r12, %r9
	0x49, 0x29, 0xf9, //0x000009c8 subq         %rdi, %r9
	0xe9, 0xac, 0xff, 0xff, 0xff, //0x000009cb jmp          LBB0_117
	//0x000009d0 .p2align 4, 0x00
	//0x000009d0 __UnquoteTab
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000009d0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000009e0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2f, //0x000009f0 QUAD $0x0000000000220000; QUAD $0x2f00000000000000  // .ascii 16, '\x00\x00"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00/'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000a00 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000a10 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5c, 0x00, 0x00, 0x00, //0x00000a20 QUAD $0x0000000000000000; QUAD $0x0000005c00000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\\\x00\x00\x00'
	0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x00, //0x00000a30 QUAD $0x000c000000080000; QUAD $0x000a000000000000  // .ascii 16, '\x00\x00\x08\x00\x00\x00\x0c\x00\x00\x00\x00\x00\x00\x00\n\x00'
	0x00, 0x00, 0x0d, 0x00, 0x09, 0xff, //0x00000a40 LONG $0x000d0000; WORD $0xff09  // .ascii 6, '\x00\x00\r\x00\t\xff'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000a46 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000a56 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000a66 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000a76 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000a86 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000a96 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000aa6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000ab6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000ac6 QUAD $0x0000000000000000; WORD $0x0000  // .space 10, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
}
 
