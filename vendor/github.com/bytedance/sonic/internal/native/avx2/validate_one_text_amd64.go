// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_validate_one = []byte{
	// .p2align 5, 0x00
	// LCPI0_0
	0x20, // .byte 32
	0x00, //0x00000001 .byte 0
	0x00, //0x00000002 .byte 0
	0x00, //0x00000003 .byte 0
	0x00, //0x00000004 .byte 0
	0x00, //0x00000005 .byte 0
	0x00, //0x00000006 .byte 0
	0x00, //0x00000007 .byte 0
	0x00, //0x00000008 .byte 0
	0x09, //0x00000009 .byte 9
	0x0a, //0x0000000a .byte 10
	0x00, //0x0000000b .byte 0
	0x00, //0x0000000c .byte 0
	0x0d, //0x0000000d .byte 13
	0x00, //0x0000000e .byte 0
	0x00, //0x0000000f .byte 0
	0x20, //0x00000010 .byte 32
	0x00, //0x00000011 .byte 0
	0x00, //0x00000012 .byte 0
	0x00, //0x00000013 .byte 0
	0x00, //0x00000014 .byte 0
	0x00, //0x00000015 .byte 0
	0x00, //0x00000016 .byte 0
	0x00, //0x00000017 .byte 0
	0x00, //0x00000018 .byte 0
	0x09, //0x00000019 .byte 9
	0x0a, //0x0000001a .byte 10
	0x00, //0x0000001b .byte 0
	0x00, //0x0000001c .byte 0
	0x0d, //0x0000001d .byte 13
	0x00, //0x0000001e .byte 0
	0x00, //0x0000001f .byte 0
	//0x00000020 LCPI0_1
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000020 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000030 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x00000040 LCPI0_2
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000040 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000050 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000060 LCPI0_3
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000060 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000070 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000080 LCPI0_4
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000080 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000090 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	//0x000000a0 LCPI0_5
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x000000a0 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x000000b0 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	//0x000000c0 LCPI0_6
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x000000c0 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x000000d0 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x000000e0 LCPI0_7
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x000000e0 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x000000f0 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000100 LCPI0_8
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000100 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000110 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x00000120 LCPI0_9
	0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, //0x00000120 QUAD $0x6565656565656565; QUAD $0x6565656565656565  // .space 16, 'eeeeeeeeeeeeeeee'
	0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, //0x00000130 QUAD $0x6565656565656565; QUAD $0x6565656565656565  // .space 16, 'eeeeeeeeeeeeeeee'
	//0x00000140 .p2align 4, 0x00
	//0x00000140 LCPI0_10
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000140 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	//0x00000150 LCPI0_11
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x00000150 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	//0x00000160 LCPI0_12
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000160 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x00000170 LCPI0_13
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000170 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000180 LCPI0_14
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000180 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000190 LCPI0_15
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000190 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x000001a0 LCPI0_16
	0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, //0x000001a0 QUAD $0x6565656565656565; QUAD $0x6565656565656565  // .space 16, 'eeeeeeeeeeeeeeee'
	//0x000001b0 .p2align 4, 0x90
	//0x000001b0 _validate_one
	0x55, //0x000001b0 pushq        %rbp
	0x48, 0x89, 0xe5, //0x000001b1 movq         %rsp, %rbp
	0x41, 0x57, //0x000001b4 pushq        %r15
	0x41, 0x56, //0x000001b6 pushq        %r14
	0x41, 0x55, //0x000001b8 pushq        %r13
	0x41, 0x54, //0x000001ba pushq        %r12
	0x53, //0x000001bc pushq        %rbx
	0x48, 0x83, 0xec, 0x48, //0x000001bd subq         $72, %rsp
	0x48, 0x89, 0x4d, 0x98, //0x000001c1 movq         %rcx, $-104(%rbp)
	0x49, 0x89, 0xd6, //0x000001c5 movq         %rdx, %r14
	0x49, 0x89, 0xf2, //0x000001c8 movq         %rsi, %r10
	0x48, 0x89, 0x7d, 0xb0, //0x000001cb movq         %rdi, $-80(%rbp)
	0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x000001cf movl         $1, %r8d
	0xc4, 0xc1, 0xf9, 0x6e, 0xc0, //0x000001d5 vmovq        %r8, %xmm0
	0xc5, 0xfa, 0x7f, 0x02, //0x000001da vmovdqu      %xmm0, (%rdx)
	0x4c, 0x8b, 0x26, //0x000001de movq         (%rsi), %r12
	0x48, 0xc7, 0x45, 0x90, 0xff, 0xff, 0xff, 0xff, //0x000001e1 movq         $-1, $-112(%rbp)
	0xc5, 0xfe, 0x6f, 0x2d, 0x0f, 0xfe, 0xff, 0xff, //0x000001e9 vmovdqu      $-497(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x27, 0xfe, 0xff, 0xff, //0x000001f1 vmovdqu      $-473(%rip), %ymm6  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x3f, 0xfe, 0xff, 0xff, //0x000001f9 vmovdqu      $-449(%rip), %ymm7  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x57, 0xfe, 0xff, 0xff, //0x00000201 vmovdqu      $-425(%rip), %ymm8  /* LCPI0_3+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00000209 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x15, 0x6a, 0xfe, 0xff, 0xff, //0x0000020e vmovdqu      $-406(%rip), %ymm10  /* LCPI0_4+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x82, 0xfe, 0xff, 0xff, //0x00000216 vmovdqu      $-382(%rip), %ymm11  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x9a, 0xfe, 0xff, 0xff, //0x0000021e vmovdqu      $-358(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0xb2, 0xfe, 0xff, 0xff, //0x00000226 vmovdqu      $-334(%rip), %ymm13  /* LCPI0_7+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0xca, 0xfe, 0xff, 0xff, //0x0000022e vmovdqu      $-310(%rip), %ymm14  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0xe2, 0xfe, 0xff, 0xff, //0x00000236 vmovdqu      $-286(%rip), %ymm15  /* LCPI0_9+0(%rip) */
	0x48, 0x89, 0x75, 0xd0, //0x0000023e movq         %rsi, $-48(%rbp)
	0x48, 0x89, 0x55, 0xc8, //0x00000242 movq         %rdx, $-56(%rbp)
	0xe9, 0x65, 0x00, 0x00, 0x00, //0x00000246 jmp          LBB0_5
	//0x0000024b LBB0_72
	0x4c, 0x89, 0xe1, //0x0000024b movq         %r12, %rcx
	//0x0000024e LBB0_199
	0x4c, 0x89, 0xe0, //0x0000024e movq         %r12, %rax
	0x4e, 0x8d, 0x64, 0x31, 0xff, //0x00000251 leaq         $-1(%rcx,%r14), %r12
	0x4c, 0x8b, 0x55, 0xd0, //0x00000256 movq         $-48(%rbp), %r10
	0x4d, 0x89, 0x22, //0x0000025a movq         %r12, (%r10)
	0x4c, 0x89, 0xe9, //0x0000025d movq         %r13, %rcx
	0x48, 0x85, 0xc0, //0x00000260 testq        %rax, %rax
	0x4c, 0x8b, 0x75, 0xc8, //0x00000263 movq         $-56(%rbp), %r14
	0x0f, 0x8e, 0x6c, 0x25, 0x00, 0x00, //0x00000267 jle          LBB0_501
	0x90, 0x90, 0x90, //0x0000026d .p2align 4, 0x90
	//0x00000270 LBB0_3
	0x49, 0x8b, 0x16, //0x00000270 movq         (%r14), %rdx
	0x49, 0x89, 0xd0, //0x00000273 movq         %rdx, %r8
	0x48, 0x8b, 0x4d, 0x90, //0x00000276 movq         $-112(%rbp), %rcx
	0x48, 0x85, 0xd2, //0x0000027a testq        %rdx, %rdx
	0x0f, 0x85, 0x2d, 0x00, 0x00, 0x00, //0x0000027d jne          LBB0_5
	0xe9, 0x51, 0x25, 0x00, 0x00, //0x00000283 jmp          LBB0_501
	//0x00000288 LBB0_1
	0x4c, 0x89, 0xe0, //0x00000288 movq         %r12, %rax
	0x4d, 0x8d, 0x65, 0x04, //0x0000028b leaq         $4(%r13), %r12
	0x4d, 0x89, 0x22, //0x0000028f movq         %r12, (%r10)
	0x4c, 0x89, 0xe9, //0x00000292 movq         %r13, %rcx
	0x48, 0x85, 0xc0, //0x00000295 testq        %rax, %rax
	0x0f, 0x8f, 0xd2, 0xff, 0xff, 0xff, //0x00000298 jg           LBB0_3
	0xe9, 0x36, 0x25, 0x00, 0x00, //0x0000029e jmp          LBB0_501
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000002a3 .p2align 4, 0x90
	//0x000002b0 LBB0_5
	0x48, 0x8b, 0x45, 0xb0, //0x000002b0 movq         $-80(%rbp), %rax
	0x4c, 0x8b, 0x08, //0x000002b4 movq         (%rax), %r9
	0x48, 0x8b, 0x48, 0x08, //0x000002b7 movq         $8(%rax), %rcx
	0x49, 0x39, 0xcc, //0x000002bb cmpq         %rcx, %r12
	0x0f, 0x83, 0x2c, 0x00, 0x00, 0x00, //0x000002be jae          LBB0_10
	0x43, 0x8a, 0x04, 0x21, //0x000002c4 movb         (%r9,%r12), %al
	0x3c, 0x0d, //0x000002c8 cmpb         $13, %al
	0x0f, 0x84, 0x20, 0x00, 0x00, 0x00, //0x000002ca je           LBB0_10
	0x3c, 0x20, //0x000002d0 cmpb         $32, %al
	0x0f, 0x84, 0x18, 0x00, 0x00, 0x00, //0x000002d2 je           LBB0_10
	0x04, 0xf7, //0x000002d8 addb         $-9, %al
	0x3c, 0x01, //0x000002da cmpb         $1, %al
	0x0f, 0x86, 0x0e, 0x00, 0x00, 0x00, //0x000002dc jbe          LBB0_10
	0x4d, 0x89, 0xe5, //0x000002e2 movq         %r12, %r13
	0xe9, 0x8e, 0x01, 0x00, 0x00, //0x000002e5 jmp          LBB0_36
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000002ea .p2align 4, 0x90
	//0x000002f0 LBB0_10
	0x4d, 0x8d, 0x6c, 0x24, 0x01, //0x000002f0 leaq         $1(%r12), %r13
	0x49, 0x39, 0xcd, //0x000002f5 cmpq         %rcx, %r13
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x000002f8 jae          LBB0_14
	0x43, 0x8a, 0x14, 0x29, //0x000002fe movb         (%r9,%r13), %dl
	0x80, 0xfa, 0x0d, //0x00000302 cmpb         $13, %dl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000305 je           LBB0_14
	0x80, 0xfa, 0x20, //0x0000030b cmpb         $32, %dl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000030e je           LBB0_14
	0x80, 0xc2, 0xf7, //0x00000314 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00000317 cmpb         $1, %dl
	0x0f, 0x87, 0x58, 0x01, 0x00, 0x00, //0x0000031a ja           LBB0_36
	//0x00000320 .p2align 4, 0x90
	//0x00000320 LBB0_14
	0x4d, 0x8d, 0x6c, 0x24, 0x02, //0x00000320 leaq         $2(%r12), %r13
	0x49, 0x39, 0xcd, //0x00000325 cmpq         %rcx, %r13
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x00000328 jae          LBB0_18
	0x43, 0x8a, 0x14, 0x29, //0x0000032e movb         (%r9,%r13), %dl
	0x80, 0xfa, 0x0d, //0x00000332 cmpb         $13, %dl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000335 je           LBB0_18
	0x80, 0xfa, 0x20, //0x0000033b cmpb         $32, %dl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000033e je           LBB0_18
	0x80, 0xc2, 0xf7, //0x00000344 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00000347 cmpb         $1, %dl
	0x0f, 0x87, 0x28, 0x01, 0x00, 0x00, //0x0000034a ja           LBB0_36
	//0x00000350 .p2align 4, 0x90
	//0x00000350 LBB0_18
	0x4d, 0x8d, 0x6c, 0x24, 0x03, //0x00000350 leaq         $3(%r12), %r13
	0x49, 0x39, 0xcd, //0x00000355 cmpq         %rcx, %r13
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x00000358 jae          LBB0_22
	0x43, 0x8a, 0x14, 0x29, //0x0000035e movb         (%r9,%r13), %dl
	0x80, 0xfa, 0x0d, //0x00000362 cmpb         $13, %dl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000365 je           LBB0_22
	0x80, 0xfa, 0x20, //0x0000036b cmpb         $32, %dl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000036e je           LBB0_22
	0x80, 0xc2, 0xf7, //0x00000374 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00000377 cmpb         $1, %dl
	0x0f, 0x87, 0xf8, 0x00, 0x00, 0x00, //0x0000037a ja           LBB0_36
	//0x00000380 .p2align 4, 0x90
	//0x00000380 LBB0_22
	0x4d, 0x8d, 0x6c, 0x24, 0x04, //0x00000380 leaq         $4(%r12), %r13
	0x48, 0x89, 0xca, //0x00000385 movq         %rcx, %rdx
	0x4c, 0x29, 0xea, //0x00000388 subq         %r13, %rdx
	0x0f, 0x86, 0xd2, 0x23, 0x00, 0x00, //0x0000038b jbe          LBB0_473
	0x4d, 0x01, 0xcd, //0x00000391 addq         %r9, %r13
	0x48, 0x83, 0xfa, 0x20, //0x00000394 cmpq         $32, %rdx
	0x0f, 0x82, 0x56, 0x00, 0x00, 0x00, //0x00000398 jb           LBB0_28
	0x48, 0x89, 0xce, //0x0000039e movq         %rcx, %rsi
	0x4c, 0x29, 0xe6, //0x000003a1 subq         %r12, %rsi
	0x48, 0x83, 0xc6, 0xdc, //0x000003a4 addq         $-36, %rsi
	0x48, 0x89, 0xf7, //0x000003a8 movq         %rsi, %rdi
	0x48, 0x83, 0xe7, 0xe0, //0x000003ab andq         $-32, %rdi
	0x4c, 0x01, 0xe7, //0x000003af addq         %r12, %rdi
	0x49, 0x8d, 0x7c, 0x39, 0x24, //0x000003b2 leaq         $36(%r9,%rdi), %rdi
	0x83, 0xe6, 0x1f, //0x000003b7 andl         $31, %esi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000003ba .p2align 4, 0x90
	//0x000003c0 LBB0_25
	0xc4, 0xc1, 0x7e, 0x6f, 0x45, 0x00, //0x000003c0 vmovdqu      (%r13), %ymm0
	0xc4, 0xe2, 0x55, 0x00, 0xc8, //0x000003c6 vpshufb      %ymm0, %ymm5, %ymm1
	0xc5, 0xfd, 0x74, 0xc1, //0x000003cb vpcmpeqb     %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd8, //0x000003cf vpmovmskb    %ymm0, %ebx
	0x83, 0xfb, 0xff, //0x000003d3 cmpl         $-1, %ebx
	0x0f, 0x85, 0x84, 0x00, 0x00, 0x00, //0x000003d6 jne          LBB0_35
	0x49, 0x83, 0xc5, 0x20, //0x000003dc addq         $32, %r13
	0x48, 0x83, 0xc2, 0xe0, //0x000003e0 addq         $-32, %rdx
	0x48, 0x83, 0xfa, 0x1f, //0x000003e4 cmpq         $31, %rdx
	0x0f, 0x87, 0xd2, 0xff, 0xff, 0xff, //0x000003e8 ja           LBB0_25
	0x48, 0x89, 0xf2, //0x000003ee movq         %rsi, %rdx
	0x49, 0x89, 0xfd, //0x000003f1 movq         %rdi, %r13
	//0x000003f4 LBB0_28
	0x48, 0x85, 0xd2, //0x000003f4 testq        %rdx, %rdx
	0x0f, 0x84, 0x47, 0x00, 0x00, 0x00, //0x000003f7 je           LBB0_34
	0x49, 0x8d, 0x74, 0x15, 0x00, //0x000003fd leaq         (%r13,%rdx), %rsi
	0x49, 0xff, 0xc5, //0x00000402 incq         %r13
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000405 .p2align 4, 0x90
	//0x00000410 LBB0_30
	0x41, 0x0f, 0xbe, 0x7d, 0xff, //0x00000410 movsbl       $-1(%r13), %edi
	0x83, 0xff, 0x20, //0x00000415 cmpl         $32, %edi
	0x0f, 0x87, 0x09, 0x11, 0x00, 0x00, //0x00000418 ja           LBB0_268
	0x48, 0xb8, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000041e movabsq      $4294977024, %rax
	0x48, 0x0f, 0xa3, 0xf8, //0x00000428 btq          %rdi, %rax
	0x0f, 0x83, 0xf5, 0x10, 0x00, 0x00, //0x0000042c jae          LBB0_268
	0x48, 0xff, 0xca, //0x00000432 decq         %rdx
	0x49, 0xff, 0xc5, //0x00000435 incq         %r13
	0x48, 0x85, 0xd2, //0x00000438 testq        %rdx, %rdx
	0x0f, 0x85, 0xcf, 0xff, 0xff, 0xff, //0x0000043b jne          LBB0_30
	0x49, 0x89, 0xf5, //0x00000441 movq         %rsi, %r13
	//0x00000444 LBB0_34
	0x4d, 0x29, 0xcd, //0x00000444 subq         %r9, %r13
	0x49, 0x39, 0xcd, //0x00000447 cmpq         %rcx, %r13
	0x0f, 0x82, 0x28, 0x00, 0x00, 0x00, //0x0000044a jb           LBB0_36
	0xe9, 0x11, 0x23, 0x00, 0x00, //0x00000450 jmp          LBB0_474
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000455 .p2align 4, 0x90
	//0x00000460 LBB0_35
	0x4d, 0x29, 0xcd, //0x00000460 subq         %r9, %r13
	0xf7, 0xd3, //0x00000463 notl         %ebx
	0x48, 0x63, 0xd3, //0x00000465 movslq       %ebx, %rdx
	0x48, 0x0f, 0xbc, 0xd2, //0x00000468 bsfq         %rdx, %rdx
	0x49, 0x01, 0xd5, //0x0000046c addq         %rdx, %r13
	0x49, 0x39, 0xcd, //0x0000046f cmpq         %rcx, %r13
	0x0f, 0x83, 0xee, 0x22, 0x00, 0x00, //0x00000472 jae          LBB0_474
	//0x00000478 LBB0_36
	0x4d, 0x8d, 0x65, 0x01, //0x00000478 leaq         $1(%r13), %r12
	0x4d, 0x89, 0x22, //0x0000047c movq         %r12, (%r10)
	0x43, 0x0f, 0xbe, 0x3c, 0x29, //0x0000047f movsbl       (%r9,%r13), %edi
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000484 movq         $-1, %rcx
	0x85, 0xff, //0x0000048b testl        %edi, %edi
	0x0f, 0x84, 0x46, 0x23, 0x00, 0x00, //0x0000048d je           LBB0_501
	0x4c, 0x89, 0xee, //0x00000493 movq         %r13, %rsi
	0x48, 0xf7, 0xd6, //0x00000496 notq         %rsi
	0x49, 0x8d, 0x50, 0xff, //0x00000499 leaq         $-1(%r8), %rdx
	0x43, 0x8b, 0x1c, 0xc6, //0x0000049d movl         (%r14,%r8,8), %ebx
	0x48, 0x8b, 0x45, 0x90, //0x000004a1 movq         $-112(%rbp), %rax
	0x48, 0x83, 0xf8, 0xff, //0x000004a5 cmpq         $-1, %rax
	0x49, 0x0f, 0x44, 0xc5, //0x000004a9 cmoveq       %r13, %rax
	0x48, 0x89, 0x45, 0x90, //0x000004ad movq         %rax, $-112(%rbp)
	0xff, 0xcb, //0x000004b1 decl         %ebx
	0x83, 0xfb, 0x05, //0x000004b3 cmpl         $5, %ebx
	0x0f, 0x87, 0x0d, 0x02, 0x00, 0x00, //0x000004b6 ja           LBB0_67
	0x48, 0x8d, 0x05, 0x45, 0x25, 0x00, 0x00, //0x000004bc leaq         $9541(%rip), %rax  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x1c, 0x98, //0x000004c3 movslq       (%rax,%rbx,4), %rbx
	0x48, 0x01, 0xc3, //0x000004c7 addq         %rax, %rbx
	0xff, 0xe3, //0x000004ca jmpq         *%rbx
	//0x000004cc LBB0_39
	0x83, 0xff, 0x2c, //0x000004cc cmpl         $44, %edi
	0x0f, 0x84, 0x01, 0x06, 0x00, 0x00, //0x000004cf je           LBB0_73
	0x83, 0xff, 0x5d, //0x000004d5 cmpl         $93, %edi
	0x0f, 0x84, 0xd3, 0x01, 0x00, 0x00, //0x000004d8 je           LBB0_41
	0xe9, 0xef, 0x22, 0x00, 0x00, //0x000004de jmp          LBB0_500
	//0x000004e3 LBB0_42
	0x40, 0x80, 0xff, 0x5d, //0x000004e3 cmpb         $93, %dil
	0x0f, 0x84, 0xc4, 0x01, 0x00, 0x00, //0x000004e7 je           LBB0_41
	0x4b, 0xc7, 0x04, 0xc6, 0x01, 0x00, 0x00, 0x00, //0x000004ed movq         $1, (%r14,%r8,8)
	0x83, 0xff, 0x7b, //0x000004f5 cmpl         $123, %edi
	0x0f, 0x86, 0xd7, 0x01, 0x00, 0x00, //0x000004f8 jbe          LBB0_68
	0xe9, 0xcf, 0x22, 0x00, 0x00, //0x000004fe jmp          LBB0_500
	//0x00000503 LBB0_44
	0x40, 0x80, 0xff, 0x22, //0x00000503 cmpb         $34, %dil
	0x0f, 0x85, 0xc5, 0x22, 0x00, 0x00, //0x00000507 jne          LBB0_500
	0x4b, 0xc7, 0x04, 0xc6, 0x04, 0x00, 0x00, 0x00, //0x0000050d movq         $4, (%r14,%r8,8)
	0x48, 0x8b, 0x45, 0xb0, //0x00000515 movq         $-80(%rbp), %rax
	0x48, 0x8b, 0x50, 0x08, //0x00000519 movq         $8(%rax), %rdx
	0xf6, 0x45, 0x98, 0x20, //0x0000051d testb        $32, $-104(%rbp)
	0x4c, 0x89, 0x65, 0xa8, //0x00000521 movq         %r12, $-88(%rbp)
	0x48, 0x89, 0x55, 0xb8, //0x00000525 movq         %rdx, $-72(%rbp)
	0x0f, 0x85, 0xc9, 0x05, 0x00, 0x00, //0x00000529 jne          LBB0_75
	0x48, 0x89, 0xd1, //0x0000052f movq         %rdx, %rcx
	0x4c, 0x29, 0xe1, //0x00000532 subq         %r12, %rcx
	0x0f, 0x84, 0x34, 0x24, 0x00, 0x00, //0x00000535 je           LBB0_507
	0x4b, 0x8d, 0x1c, 0x21, //0x0000053b leaq         (%r9,%r12), %rbx
	0x48, 0x83, 0xf9, 0x40, //0x0000053f cmpq         $64, %rcx
	0x0f, 0x82, 0xea, 0x18, 0x00, 0x00, //0x00000543 jb           LBB0_356
	0x41, 0x89, 0xcf, //0x00000549 movl         %ecx, %r15d
	0x41, 0x83, 0xe7, 0x3f, //0x0000054c andl         $63, %r15d
	0x48, 0x8d, 0x54, 0x32, 0xc0, //0x00000550 leaq         $-64(%rdx,%rsi), %rdx
	0x48, 0x83, 0xe2, 0xc0, //0x00000555 andq         $-64, %rdx
	0x4c, 0x01, 0xe2, //0x00000559 addq         %r12, %rdx
	0x4d, 0x89, 0xcb, //0x0000055c movq         %r9, %r11
	0x4d, 0x8d, 0x4c, 0x11, 0x40, //0x0000055f leaq         $64(%r9,%rdx), %r9
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000564 movq         $-1, %r8
	0x45, 0x31, 0xf6, //0x0000056b xorl         %r14d, %r14d
	0x90, 0x90, //0x0000056e .p2align 4, 0x90
	//0x00000570 LBB0_49
	0xc5, 0xfe, 0x6f, 0x03, //0x00000570 vmovdqu      (%rbx), %ymm0
	0xc5, 0xfe, 0x6f, 0x4b, 0x20, //0x00000574 vmovdqu      $32(%rbx), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x00000579 vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x0000057d vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x00000581 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00000585 vpmovmskb    %ymm2, %edx
	0xc5, 0xfd, 0x74, 0xc7, //0x00000589 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x0000058d vpmovmskb    %ymm0, %esi
	0xc5, 0xf5, 0x74, 0xc7, //0x00000591 vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x00000595 vpmovmskb    %ymm0, %eax
	0x48, 0xc1, 0xe2, 0x20, //0x00000599 shlq         $32, %rdx
	0x48, 0xc1, 0xe0, 0x20, //0x0000059d shlq         $32, %rax
	0x48, 0x09, 0xc6, //0x000005a1 orq          %rax, %rsi
	0x49, 0x83, 0xf8, 0xff, //0x000005a4 cmpq         $-1, %r8
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x000005a8 jne          LBB0_51
	0x48, 0x85, 0xf6, //0x000005ae testq        %rsi, %rsi
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x000005b1 jne          LBB0_58
	//0x000005b7 LBB0_51
	0x48, 0x09, 0xfa, //0x000005b7 orq          %rdi, %rdx
	0x48, 0x89, 0xf0, //0x000005ba movq         %rsi, %rax
	0x4c, 0x09, 0xf0, //0x000005bd orq          %r14, %rax
	0x0f, 0x85, 0x3c, 0x00, 0x00, 0x00, //0x000005c0 jne          LBB0_59
	//0x000005c6 LBB0_52
	0x48, 0x85, 0xd2, //0x000005c6 testq        %rdx, %rdx
	0x0f, 0x85, 0xdb, 0x14, 0x00, 0x00, //0x000005c9 jne          LBB0_60
	//0x000005cf LBB0_53
	0x48, 0x83, 0xc1, 0xc0, //0x000005cf addq         $-64, %rcx
	0x48, 0x83, 0xc3, 0x40, //0x000005d3 addq         $64, %rbx
	0x48, 0x83, 0xf9, 0x3f, //0x000005d7 cmpq         $63, %rcx
	0x0f, 0x87, 0x8f, 0xff, 0xff, 0xff, //0x000005db ja           LBB0_49
	0xe9, 0x83, 0x14, 0x00, 0x00, //0x000005e1 jmp          LBB0_54
	//0x000005e6 LBB0_58
	0x48, 0x89, 0xd8, //0x000005e6 movq         %rbx, %rax
	0x4c, 0x29, 0xd8, //0x000005e9 subq         %r11, %rax
	0x4c, 0x0f, 0xbc, 0xc6, //0x000005ec bsfq         %rsi, %r8
	0x49, 0x01, 0xc0, //0x000005f0 addq         %rax, %r8
	0x48, 0x09, 0xfa, //0x000005f3 orq          %rdi, %rdx
	0x48, 0x89, 0xf0, //0x000005f6 movq         %rsi, %rax
	0x4c, 0x09, 0xf0, //0x000005f9 orq          %r14, %rax
	0x0f, 0x84, 0xc4, 0xff, 0xff, 0xff, //0x000005fc je           LBB0_52
	//0x00000602 LBB0_59
	0x4c, 0x89, 0xf0, //0x00000602 movq         %r14, %rax
	0x48, 0xf7, 0xd0, //0x00000605 notq         %rax
	0x48, 0x21, 0xf0, //0x00000608 andq         %rsi, %rax
	0x4c, 0x8d, 0x14, 0x00, //0x0000060b leaq         (%rax,%rax), %r10
	0x4d, 0x09, 0xf2, //0x0000060f orq          %r14, %r10
	0x4c, 0x89, 0xd7, //0x00000612 movq         %r10, %rdi
	0x48, 0xf7, 0xd7, //0x00000615 notq         %rdi
	0x48, 0x21, 0xf7, //0x00000618 andq         %rsi, %rdi
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000061b movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf7, //0x00000625 andq         %rsi, %rdi
	0x45, 0x31, 0xf6, //0x00000628 xorl         %r14d, %r14d
	0x48, 0x01, 0xc7, //0x0000062b addq         %rax, %rdi
	0x41, 0x0f, 0x92, 0xc6, //0x0000062e setb         %r14b
	0x48, 0x01, 0xff, //0x00000632 addq         %rdi, %rdi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000635 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc7, //0x0000063f xorq         %rax, %rdi
	0x4c, 0x21, 0xd7, //0x00000642 andq         %r10, %rdi
	0x4c, 0x8b, 0x55, 0xd0, //0x00000645 movq         $-48(%rbp), %r10
	0x48, 0xf7, 0xd7, //0x00000649 notq         %rdi
	0x48, 0x21, 0xfa, //0x0000064c andq         %rdi, %rdx
	0x48, 0x85, 0xd2, //0x0000064f testq        %rdx, %rdx
	0x0f, 0x84, 0x77, 0xff, 0xff, 0xff, //0x00000652 je           LBB0_53
	0xe9, 0x4d, 0x14, 0x00, 0x00, //0x00000658 jmp          LBB0_60
	//0x0000065d LBB0_61
	0x40, 0x80, 0xff, 0x3a, //0x0000065d cmpb         $58, %dil
	0x0f, 0x85, 0x6b, 0x21, 0x00, 0x00, //0x00000661 jne          LBB0_500
	0x4b, 0xc7, 0x04, 0xc6, 0x00, 0x00, 0x00, 0x00, //0x00000667 movq         $0, (%r14,%r8,8)
	0xe9, 0xfc, 0xfb, 0xff, 0xff, //0x0000066f jmp          LBB0_3
	//0x00000674 LBB0_63
	0x83, 0xff, 0x2c, //0x00000674 cmpl         $44, %edi
	0x0f, 0x85, 0x2b, 0x00, 0x00, 0x00, //0x00000677 jne          LBB0_64
	0x49, 0x81, 0xf8, 0xff, 0x0f, 0x00, 0x00, //0x0000067d cmpq         $4095, %r8
	0x0f, 0x8f, 0xe8, 0x20, 0x00, 0x00, //0x00000684 jg           LBB0_489
	0x49, 0x8d, 0x40, 0x01, //0x0000068a leaq         $1(%r8), %rax
	0x49, 0x89, 0x06, //0x0000068e movq         %rax, (%r14)
	0x4b, 0xc7, 0x44, 0xc6, 0x08, 0x03, 0x00, 0x00, 0x00, //0x00000691 movq         $3, $8(%r14,%r8,8)
	0xe9, 0xd1, 0xfb, 0xff, 0xff, //0x0000069a jmp          LBB0_3
	//0x0000069f LBB0_65
	0x83, 0xff, 0x22, //0x0000069f cmpl         $34, %edi
	0x0f, 0x84, 0xb7, 0x05, 0x00, 0x00, //0x000006a2 je           LBB0_92
	//0x000006a8 LBB0_64
	0x83, 0xff, 0x7d, //0x000006a8 cmpl         $125, %edi
	0x0f, 0x85, 0x21, 0x21, 0x00, 0x00, //0x000006ab jne          LBB0_500
	//0x000006b1 LBB0_41
	0x49, 0x89, 0x16, //0x000006b1 movq         %rdx, (%r14)
	0x49, 0x89, 0xd0, //0x000006b4 movq         %rdx, %r8
	0x48, 0x8b, 0x4d, 0x90, //0x000006b7 movq         $-112(%rbp), %rcx
	0x48, 0x85, 0xd2, //0x000006bb testq        %rdx, %rdx
	0x0f, 0x85, 0xec, 0xfb, 0xff, 0xff, //0x000006be jne          LBB0_5
	0xe9, 0x10, 0x21, 0x00, 0x00, //0x000006c4 jmp          LBB0_501
	//0x000006c9 LBB0_67
	0x49, 0x89, 0x16, //0x000006c9 movq         %rdx, (%r14)
	0x83, 0xff, 0x7b, //0x000006cc cmpl         $123, %edi
	0x0f, 0x87, 0xfd, 0x20, 0x00, 0x00, //0x000006cf ja           LBB0_500
	//0x000006d5 LBB0_68
	0x4f, 0x8d, 0x3c, 0x29, //0x000006d5 leaq         (%r9,%r13), %r15
	0x89, 0xf8, //0x000006d9 movl         %edi, %eax
	0x48, 0x8d, 0x15, 0x3e, 0x23, 0x00, 0x00, //0x000006db leaq         $9022(%rip), %rdx  /* LJTI0_1+0(%rip) */
	0x48, 0x63, 0x04, 0x82, //0x000006e2 movslq       (%rdx,%rax,4), %rax
	0x48, 0x01, 0xd0, //0x000006e6 addq         %rdx, %rax
	0xff, 0xe0, //0x000006e9 jmpq         *%rax
	//0x000006eb LBB0_69
	0x48, 0x8b, 0x45, 0xb0, //0x000006eb movq         $-80(%rbp), %rax
	0x4c, 0x8b, 0x50, 0x08, //0x000006ef movq         $8(%rax), %r10
	0x4d, 0x29, 0xea, //0x000006f3 subq         %r13, %r10
	0x0f, 0x84, 0x9c, 0x20, 0x00, 0x00, //0x000006f6 je           LBB0_477
	0x41, 0x80, 0x3f, 0x30, //0x000006fc cmpb         $48, (%r15)
	0x0f, 0x85, 0x3a, 0x00, 0x00, 0x00, //0x00000700 jne          LBB0_132
	0x41, 0xbe, 0x01, 0x00, 0x00, 0x00, //0x00000706 movl         $1, %r14d
	0x49, 0x83, 0xfa, 0x01, //0x0000070c cmpq         $1, %r10
	0x0f, 0x84, 0x35, 0xfb, 0xff, 0xff, //0x00000710 je           LBB0_72
	0x43, 0x8a, 0x0c, 0x21, //0x00000716 movb         (%r9,%r12), %cl
	0x80, 0xc1, 0xd2, //0x0000071a addb         $-46, %cl
	0x80, 0xf9, 0x37, //0x0000071d cmpb         $55, %cl
	0x0f, 0x87, 0x25, 0xfb, 0xff, 0xff, //0x00000720 ja           LBB0_72
	0x0f, 0xb6, 0xc1, //0x00000726 movzbl       %cl, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00000729 movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x00000733 btq          %rax, %rcx
	0x4c, 0x89, 0xe1, //0x00000737 movq         %r12, %rcx
	0x0f, 0x83, 0x0e, 0xfb, 0xff, 0xff, //0x0000073a jae          LBB0_199
	//0x00000740 LBB0_132
	0x4c, 0x89, 0x65, 0xa8, //0x00000740 movq         %r12, $-88(%rbp)
	0x49, 0x83, 0xfa, 0x20, //0x00000744 cmpq         $32, %r10
	0x0f, 0x82, 0xbe, 0x16, 0x00, 0x00, //0x00000748 jb           LBB0_355
	0x49, 0x8d, 0x4a, 0xe0, //0x0000074e leaq         $-32(%r10), %rcx
	0x48, 0x89, 0xc8, //0x00000752 movq         %rcx, %rax
	0x48, 0x83, 0xe0, 0xe0, //0x00000755 andq         $-32, %rax
	0x4e, 0x8d, 0x74, 0x38, 0x20, //0x00000759 leaq         $32(%rax,%r15), %r14
	0x83, 0xe1, 0x1f, //0x0000075e andl         $31, %ecx
	0x48, 0x89, 0x4d, 0xc0, //0x00000761 movq         %rcx, $-64(%rbp)
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000765 movq         $-1, %r8
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x0000076c movq         $-1, %r12
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000773 movq         $-1, %rax
	0x4d, 0x89, 0xfb, //0x0000077a movq         %r15, %r11
	0x90, 0x90, 0x90, //0x0000077d .p2align 4, 0x90
	//0x00000780 LBB0_134
	0xc4, 0xc1, 0x7e, 0x6f, 0x03, //0x00000780 vmovdqu      (%r11), %ymm0
	0xc4, 0xc1, 0x7d, 0x64, 0xca, //0x00000785 vpcmpgtb     %ymm10, %ymm0, %ymm1
	0xc5, 0xa5, 0x64, 0xd0, //0x0000078a vpcmpgtb     %ymm0, %ymm11, %ymm2
	0xc5, 0xf5, 0xdb, 0xca, //0x0000078e vpand        %ymm2, %ymm1, %ymm1
	0xc5, 0x9d, 0x74, 0xd0, //0x00000792 vpcmpeqb     %ymm0, %ymm12, %ymm2
	0xc5, 0x95, 0x74, 0xd8, //0x00000796 vpcmpeqb     %ymm0, %ymm13, %ymm3
	0xc5, 0xe5, 0xeb, 0xd2, //0x0000079a vpor         %ymm2, %ymm3, %ymm2
	0xc5, 0xbd, 0xeb, 0xd8, //0x0000079e vpor         %ymm0, %ymm8, %ymm3
	0xc5, 0x8d, 0x74, 0xc0, //0x000007a2 vpcmpeqb     %ymm0, %ymm14, %ymm0
	0xc5, 0xfd, 0xd7, 0xf8, //0x000007a6 vpmovmskb    %ymm0, %edi
	0xc5, 0x85, 0x74, 0xdb, //0x000007aa vpcmpeqb     %ymm3, %ymm15, %ymm3
	0xc5, 0xfd, 0xd7, 0xd3, //0x000007ae vpmovmskb    %ymm3, %edx
	0xc5, 0xfd, 0xd7, 0xf2, //0x000007b2 vpmovmskb    %ymm2, %esi
	0xc5, 0xe5, 0xeb, 0xc0, //0x000007b6 vpor         %ymm0, %ymm3, %ymm0
	0xc5, 0xed, 0xeb, 0xc9, //0x000007ba vpor         %ymm1, %ymm2, %ymm1
	0xc5, 0xfd, 0xeb, 0xc1, //0x000007be vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc8, //0x000007c2 vpmovmskb    %ymm0, %ecx
	0x48, 0xf7, 0xd1, //0x000007c6 notq         %rcx
	0x48, 0x0f, 0xbc, 0xc9, //0x000007c9 bsfq         %rcx, %rcx
	0x83, 0xf9, 0x20, //0x000007cd cmpl         $32, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x000007d0 je           LBB0_136
	0xbb, 0xff, 0xff, 0xff, 0xff, //0x000007d6 movl         $-1, %ebx
	0xd3, 0xe3, //0x000007db shll         %cl, %ebx
	0xf7, 0xd3, //0x000007dd notl         %ebx
	0x21, 0xdf, //0x000007df andl         %ebx, %edi
	0x21, 0xda, //0x000007e1 andl         %ebx, %edx
	0x21, 0xf3, //0x000007e3 andl         %esi, %ebx
	0x89, 0xde, //0x000007e5 movl         %ebx, %esi
	//0x000007e7 LBB0_136
	0x44, 0x8d, 0x4f, 0xff, //0x000007e7 leal         $-1(%rdi), %r9d
	0x41, 0x21, 0xf9, //0x000007eb andl         %edi, %r9d
	0x0f, 0x85, 0xab, 0x13, 0x00, 0x00, //0x000007ee jne          LBB0_345
	0x8d, 0x5a, 0xff, //0x000007f4 leal         $-1(%rdx), %ebx
	0x21, 0xd3, //0x000007f7 andl         %edx, %ebx
	0x0f, 0x85, 0x5e, 0x12, 0x00, 0x00, //0x000007f9 jne          LBB0_339
	0x8d, 0x5e, 0xff, //0x000007ff leal         $-1(%rsi), %ebx
	0x21, 0xf3, //0x00000802 andl         %esi, %ebx
	0x0f, 0x85, 0x53, 0x12, 0x00, 0x00, //0x00000804 jne          LBB0_339
	0x85, 0xff, //0x0000080a testl        %edi, %edi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000080c je           LBB0_142
	0x4c, 0x89, 0xdb, //0x00000812 movq         %r11, %rbx
	0x4c, 0x29, 0xfb, //0x00000815 subq         %r15, %rbx
	0x0f, 0xbc, 0xff, //0x00000818 bsfl         %edi, %edi
	0x48, 0x01, 0xdf, //0x0000081b addq         %rbx, %rdi
	0x48, 0x83, 0xf8, 0xff, //0x0000081e cmpq         $-1, %rax
	0x0f, 0x85, 0x8d, 0x13, 0x00, 0x00, //0x00000822 jne          LBB0_347
	0x48, 0x89, 0xf8, //0x00000828 movq         %rdi, %rax
	//0x0000082b LBB0_142
	0x85, 0xd2, //0x0000082b testl        %edx, %edx
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000082d je           LBB0_145
	0x4c, 0x89, 0xdf, //0x00000833 movq         %r11, %rdi
	0x4c, 0x29, 0xff, //0x00000836 subq         %r15, %rdi
	0x0f, 0xbc, 0xd2, //0x00000839 bsfl         %edx, %edx
	0x48, 0x01, 0xfa, //0x0000083c addq         %rdi, %rdx
	0x49, 0x83, 0xfc, 0xff, //0x0000083f cmpq         $-1, %r12
	0x0f, 0x85, 0xf1, 0x12, 0x00, 0x00, //0x00000843 jne          LBB0_340
	0x49, 0x89, 0xd4, //0x00000849 movq         %rdx, %r12
	//0x0000084c LBB0_145
	0x85, 0xf6, //0x0000084c testl        %esi, %esi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000084e je           LBB0_148
	0x4c, 0x89, 0xdf, //0x00000854 movq         %r11, %rdi
	0x4c, 0x29, 0xff, //0x00000857 subq         %r15, %rdi
	0x0f, 0xbc, 0xd6, //0x0000085a bsfl         %esi, %edx
	0x48, 0x01, 0xfa, //0x0000085d addq         %rdi, %rdx
	0x49, 0x83, 0xf8, 0xff, //0x00000860 cmpq         $-1, %r8
	0x0f, 0x85, 0xd0, 0x12, 0x00, 0x00, //0x00000864 jne          LBB0_340
	0x49, 0x89, 0xd0, //0x0000086a movq         %rdx, %r8
	//0x0000086d LBB0_148
	0x83, 0xf9, 0x20, //0x0000086d cmpl         $32, %ecx
	0x0f, 0x85, 0x51, 0x05, 0x00, 0x00, //0x00000870 jne          LBB0_180
	0x49, 0x83, 0xc3, 0x20, //0x00000876 addq         $32, %r11
	0x49, 0x83, 0xc2, 0xe0, //0x0000087a addq         $-32, %r10
	0x49, 0x83, 0xfa, 0x1f, //0x0000087e cmpq         $31, %r10
	0x0f, 0x87, 0xf8, 0xfe, 0xff, 0xff, //0x00000882 ja           LBB0_134
	0xc5, 0xf8, 0x77, //0x00000888 vzeroupper   
	0xc5, 0x7e, 0x6f, 0x3d, 0x8d, 0xf8, 0xff, 0xff, //0x0000088b vmovdqu      $-1907(%rip), %ymm15  /* LCPI0_9+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x65, 0xf8, 0xff, 0xff, //0x00000893 vmovdqu      $-1947(%rip), %ymm14  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x3d, 0xf8, 0xff, 0xff, //0x0000089b vmovdqu      $-1987(%rip), %ymm13  /* LCPI0_7+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x15, 0xf8, 0xff, 0xff, //0x000008a3 vmovdqu      $-2027(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0xed, 0xf7, 0xff, 0xff, //0x000008ab vmovdqu      $-2067(%rip), %ymm11  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0xc5, 0xf7, 0xff, 0xff, //0x000008b3 vmovdqu      $-2107(%rip), %ymm10  /* LCPI0_4+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000008bb vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x05, 0x98, 0xf7, 0xff, 0xff, //0x000008c0 vmovdqu      $-2152(%rip), %ymm8  /* LCPI0_3+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x70, 0xf7, 0xff, 0xff, //0x000008c8 vmovdqu      $-2192(%rip), %ymm7  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x48, 0xf7, 0xff, 0xff, //0x000008d0 vmovdqu      $-2232(%rip), %ymm6  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x2d, 0x20, 0xf7, 0xff, 0xff, //0x000008d8 vmovdqu      $-2272(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0x4c, 0x8b, 0x55, 0xc0, //0x000008e0 movq         $-64(%rbp), %r10
	0x49, 0x83, 0xfa, 0x10, //0x000008e4 cmpq         $16, %r10
	0x0f, 0x82, 0x52, 0x01, 0x00, 0x00, //0x000008e8 jb           LBB0_169
	//0x000008ee LBB0_151
	0x4d, 0x8d, 0x4a, 0xf0, //0x000008ee leaq         $-16(%r10), %r9
	0x4c, 0x89, 0xc9, //0x000008f2 movq         %r9, %rcx
	0x48, 0x83, 0xe1, 0xf0, //0x000008f5 andq         $-16, %rcx
	0x4e, 0x8d, 0x5c, 0x31, 0x10, //0x000008f9 leaq         $16(%rcx,%r14), %r11
	0x41, 0x83, 0xe1, 0x0f, //0x000008fe andl         $15, %r9d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000902 .p2align 4, 0x90
	//0x00000910 LBB0_152
	0xc4, 0xc1, 0x7a, 0x6f, 0x06, //0x00000910 vmovdqu      (%r14), %xmm0
	0xc5, 0xf9, 0x64, 0x0d, 0x23, 0xf8, 0xff, 0xff, //0x00000915 vpcmpgtb     $-2013(%rip), %xmm0, %xmm1  /* LCPI0_10+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x15, 0x2b, 0xf8, 0xff, 0xff, //0x0000091d vmovdqu      $-2005(%rip), %xmm2  /* LCPI0_11+0(%rip) */
	0xc5, 0xe9, 0x64, 0xd0, //0x00000925 vpcmpgtb     %xmm0, %xmm2, %xmm2
	0xc5, 0xf1, 0xdb, 0xca, //0x00000929 vpand        %xmm2, %xmm1, %xmm1
	0xc5, 0xf9, 0x74, 0x15, 0x2b, 0xf8, 0xff, 0xff, //0x0000092d vpcmpeqb     $-2005(%rip), %xmm0, %xmm2  /* LCPI0_12+0(%rip) */
	0xc5, 0xf9, 0x74, 0x1d, 0x33, 0xf8, 0xff, 0xff, //0x00000935 vpcmpeqb     $-1997(%rip), %xmm0, %xmm3  /* LCPI0_13+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xd2, //0x0000093d vpor         %xmm2, %xmm3, %xmm2
	0xc5, 0xf9, 0xeb, 0x1d, 0x37, 0xf8, 0xff, 0xff, //0x00000941 vpor         $-1993(%rip), %xmm0, %xmm3  /* LCPI0_14+0(%rip) */
	0xc5, 0xf9, 0x74, 0x05, 0x3f, 0xf8, 0xff, 0xff, //0x00000949 vpcmpeqb     $-1985(%rip), %xmm0, %xmm0  /* LCPI0_15+0(%rip) */
	0xc5, 0xe1, 0x74, 0x1d, 0x47, 0xf8, 0xff, 0xff, //0x00000951 vpcmpeqb     $-1977(%rip), %xmm3, %xmm3  /* LCPI0_16+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xe0, //0x00000959 vpor         %xmm0, %xmm3, %xmm4
	0xc5, 0xe9, 0xeb, 0xc9, //0x0000095d vpor         %xmm1, %xmm2, %xmm1
	0xc5, 0xd9, 0xeb, 0xc9, //0x00000961 vpor         %xmm1, %xmm4, %xmm1
	0xc5, 0xf9, 0xd7, 0xf8, //0x00000965 vpmovmskb    %xmm0, %edi
	0xc5, 0xf9, 0xd7, 0xf3, //0x00000969 vpmovmskb    %xmm3, %esi
	0xc5, 0xf9, 0xd7, 0xd2, //0x0000096d vpmovmskb    %xmm2, %edx
	0xc5, 0xf9, 0xd7, 0xc9, //0x00000971 vpmovmskb    %xmm1, %ecx
	0xbb, 0xff, 0xff, 0xff, 0xff, //0x00000975 movl         $4294967295, %ebx
	0x48, 0x31, 0xd9, //0x0000097a xorq         %rbx, %rcx
	0x48, 0x0f, 0xbc, 0xc9, //0x0000097d bsfq         %rcx, %rcx
	0x83, 0xf9, 0x10, //0x00000981 cmpl         $16, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x00000984 je           LBB0_154
	0xbb, 0xff, 0xff, 0xff, 0xff, //0x0000098a movl         $-1, %ebx
	0xd3, 0xe3, //0x0000098f shll         %cl, %ebx
	0xf7, 0xd3, //0x00000991 notl         %ebx
	0x21, 0xdf, //0x00000993 andl         %ebx, %edi
	0x21, 0xde, //0x00000995 andl         %ebx, %esi
	0x21, 0xd3, //0x00000997 andl         %edx, %ebx
	0x89, 0xda, //0x00000999 movl         %ebx, %edx
	//0x0000099b LBB0_154
	0x8d, 0x5f, 0xff, //0x0000099b leal         $-1(%rdi), %ebx
	0x21, 0xfb, //0x0000099e andl         %edi, %ebx
	0x0f, 0x85, 0xe1, 0x11, 0x00, 0x00, //0x000009a0 jne          LBB0_344
	0x8d, 0x5e, 0xff, //0x000009a6 leal         $-1(%rsi), %ebx
	0x21, 0xf3, //0x000009a9 andl         %esi, %ebx
	0x0f, 0x85, 0xd6, 0x11, 0x00, 0x00, //0x000009ab jne          LBB0_344
	0x8d, 0x5a, 0xff, //0x000009b1 leal         $-1(%rdx), %ebx
	0x21, 0xd3, //0x000009b4 andl         %edx, %ebx
	0x0f, 0x85, 0xcb, 0x11, 0x00, 0x00, //0x000009b6 jne          LBB0_344
	0x85, 0xff, //0x000009bc testl        %edi, %edi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x000009be je           LBB0_160
	0x4c, 0x89, 0xf3, //0x000009c4 movq         %r14, %rbx
	0x4c, 0x29, 0xfb, //0x000009c7 subq         %r15, %rbx
	0x0f, 0xbc, 0xff, //0x000009ca bsfl         %edi, %edi
	0x48, 0x01, 0xdf, //0x000009cd addq         %rbx, %rdi
	0x48, 0x83, 0xf8, 0xff, //0x000009d0 cmpq         $-1, %rax
	0x0f, 0x85, 0xdb, 0x11, 0x00, 0x00, //0x000009d4 jne          LBB0_347
	0x48, 0x89, 0xf8, //0x000009da movq         %rdi, %rax
	//0x000009dd LBB0_160
	0x85, 0xf6, //0x000009dd testl        %esi, %esi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x000009df je           LBB0_163
	0x4c, 0x89, 0xf7, //0x000009e5 movq         %r14, %rdi
	0x4c, 0x29, 0xff, //0x000009e8 subq         %r15, %rdi
	0x0f, 0xbc, 0xf6, //0x000009eb bsfl         %esi, %esi
	0x48, 0x01, 0xfe, //0x000009ee addq         %rdi, %rsi
	0x49, 0x83, 0xfc, 0xff, //0x000009f1 cmpq         $-1, %r12
	0x0f, 0x85, 0xed, 0x12, 0x00, 0x00, //0x000009f5 jne          LBB0_350
	0x49, 0x89, 0xf4, //0x000009fb movq         %rsi, %r12
	//0x000009fe LBB0_163
	0x85, 0xd2, //0x000009fe testl        %edx, %edx
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x00000a00 je           LBB0_166
	0x4c, 0x89, 0xf6, //0x00000a06 movq         %r14, %rsi
	0x4c, 0x29, 0xfe, //0x00000a09 subq         %r15, %rsi
	0x0f, 0xbc, 0xd2, //0x00000a0c bsfl         %edx, %edx
	0x48, 0x01, 0xf2, //0x00000a0f addq         %rsi, %rdx
	0x49, 0x83, 0xf8, 0xff, //0x00000a12 cmpq         $-1, %r8
	0x0f, 0x85, 0x1e, 0x11, 0x00, 0x00, //0x00000a16 jne          LBB0_340
	0x49, 0x89, 0xd0, //0x00000a1c movq         %rdx, %r8
	//0x00000a1f LBB0_166
	0x83, 0xf9, 0x10, //0x00000a1f cmpl         $16, %ecx
	0x0f, 0x85, 0x82, 0x05, 0x00, 0x00, //0x00000a22 jne          LBB0_186
	0x49, 0x83, 0xc6, 0x10, //0x00000a28 addq         $16, %r14
	0x49, 0x83, 0xc2, 0xf0, //0x00000a2c addq         $-16, %r10
	0x49, 0x83, 0xfa, 0x0f, //0x00000a30 cmpq         $15, %r10
	0x0f, 0x87, 0xd6, 0xfe, 0xff, 0xff, //0x00000a34 ja           LBB0_152
	0x4d, 0x89, 0xca, //0x00000a3a movq         %r9, %r10
	0x4d, 0x89, 0xde, //0x00000a3d movq         %r11, %r14
	//0x00000a40 LBB0_169
	0x4d, 0x85, 0xd2, //0x00000a40 testq        %r10, %r10
	0x0f, 0x84, 0x64, 0x05, 0x00, 0x00, //0x00000a43 je           LBB0_187
	0x4b, 0x8d, 0x0c, 0x16, //0x00000a49 leaq         (%r14,%r10), %rcx
	0xe9, 0x2a, 0x00, 0x00, 0x00, //0x00000a4d jmp          LBB0_174
	//0x00000a52 LBB0_171
	0x49, 0x89, 0xd6, //0x00000a52 movq         %rdx, %r14
	0x4d, 0x29, 0xfe, //0x00000a55 subq         %r15, %r14
	0x49, 0x83, 0xf8, 0xff, //0x00000a58 cmpq         $-1, %r8
	0x0f, 0x85, 0x15, 0x13, 0x00, 0x00, //0x00000a5c jne          LBB0_352
	0x49, 0xff, 0xce, //0x00000a62 decq         %r14
	0x4d, 0x89, 0xf0, //0x00000a65 movq         %r14, %r8
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000a68 .p2align 4, 0x90
	//0x00000a70 LBB0_173
	0x49, 0x89, 0xd6, //0x00000a70 movq         %rdx, %r14
	0x49, 0xff, 0xca, //0x00000a73 decq         %r10
	0x0f, 0x84, 0x3a, 0x12, 0x00, 0x00, //0x00000a76 je           LBB0_348
	//0x00000a7c LBB0_174
	0x41, 0x0f, 0xbe, 0x36, //0x00000a7c movsbl       (%r14), %esi
	0x83, 0xc6, 0xd5, //0x00000a80 addl         $-43, %esi
	0x83, 0xfe, 0x3a, //0x00000a83 cmpl         $58, %esi
	0x0f, 0x87, 0x21, 0x05, 0x00, 0x00, //0x00000a86 ja           LBB0_187
	0x49, 0x8d, 0x56, 0x01, //0x00000a8c leaq         $1(%r14), %rdx
	0x48, 0x8d, 0x3d, 0x65, 0x22, 0x00, 0x00, //0x00000a90 leaq         $8805(%rip), %rdi  /* LJTI0_3+0(%rip) */
	0x48, 0x63, 0x34, 0xb7, //0x00000a97 movslq       (%rdi,%rsi,4), %rsi
	0x48, 0x01, 0xfe, //0x00000a9b addq         %rdi, %rsi
	0xff, 0xe6, //0x00000a9e jmpq         *%rsi
	//0x00000aa0 LBB0_176
	0x49, 0x89, 0xd6, //0x00000aa0 movq         %rdx, %r14
	0x4d, 0x29, 0xfe, //0x00000aa3 subq         %r15, %r14
	0x49, 0x83, 0xfc, 0xff, //0x00000aa6 cmpq         $-1, %r12
	0x0f, 0x85, 0xc7, 0x12, 0x00, 0x00, //0x00000aaa jne          LBB0_352
	0x49, 0xff, 0xce, //0x00000ab0 decq         %r14
	0x4d, 0x89, 0xf4, //0x00000ab3 movq         %r14, %r12
	0xe9, 0xb5, 0xff, 0xff, 0xff, //0x00000ab6 jmp          LBB0_173
	//0x00000abb LBB0_178
	0x49, 0x89, 0xd6, //0x00000abb movq         %rdx, %r14
	0x4d, 0x29, 0xfe, //0x00000abe subq         %r15, %r14
	0x48, 0x83, 0xf8, 0xff, //0x00000ac1 cmpq         $-1, %rax
	0x0f, 0x85, 0xac, 0x12, 0x00, 0x00, //0x00000ac5 jne          LBB0_352
	0x49, 0xff, 0xce, //0x00000acb decq         %r14
	0x4c, 0x89, 0xf0, //0x00000ace movq         %r14, %rax
	0xe9, 0x9a, 0xff, 0xff, 0xff, //0x00000ad1 jmp          LBB0_173
	//0x00000ad6 LBB0_73
	0x49, 0x81, 0xf8, 0xff, 0x0f, 0x00, 0x00, //0x00000ad6 cmpq         $4095, %r8
	0x0f, 0x8f, 0x8f, 0x1c, 0x00, 0x00, //0x00000add jg           LBB0_489
	0x49, 0x8d, 0x40, 0x01, //0x00000ae3 leaq         $1(%r8), %rax
	0x49, 0x89, 0x06, //0x00000ae7 movq         %rax, (%r14)
	0x4b, 0xc7, 0x44, 0xc6, 0x08, 0x00, 0x00, 0x00, 0x00, //0x00000aea movq         $0, $8(%r14,%r8,8)
	0xe9, 0x78, 0xf7, 0xff, 0xff, //0x00000af3 jmp          LBB0_3
	//0x00000af8 LBB0_75
	0x49, 0x89, 0xd2, //0x00000af8 movq         %rdx, %r10
	0x4d, 0x29, 0xe2, //0x00000afb subq         %r12, %r10
	0x0f, 0x84, 0x74, 0x1e, 0x00, 0x00, //0x00000afe je           LBB0_508
	0x4c, 0x89, 0xe0, //0x00000b04 movq         %r12, %rax
	0x4d, 0x01, 0xcc, //0x00000b07 addq         %r9, %r12
	0x49, 0x83, 0xfa, 0x40, //0x00000b0a cmpq         $64, %r10
	0x4c, 0x89, 0x4d, 0xc0, //0x00000b0e movq         %r9, $-64(%rbp)
	0x0f, 0x82, 0x37, 0x13, 0x00, 0x00, //0x00000b12 jb           LBB0_357
	0x45, 0x89, 0xd6, //0x00000b18 movl         %r10d, %r14d
	0x41, 0x83, 0xe6, 0x3f, //0x00000b1b andl         $63, %r14d
	0x48, 0x8d, 0x4c, 0x32, 0xc0, //0x00000b1f leaq         $-64(%rdx,%rsi), %rcx
	0x48, 0x83, 0xe1, 0xc0, //0x00000b24 andq         $-64, %rcx
	0x48, 0x01, 0xc1, //0x00000b28 addq         %rax, %rcx
	0x49, 0x8d, 0x44, 0x09, 0x40, //0x00000b2b leaq         $64(%r9,%rcx), %rax
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000b30 movq         $-1, %r8
	0x45, 0x31, 0xff, //0x00000b37 xorl         %r15d, %r15d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000b3a .p2align 4, 0x90
	//0x00000b40 LBB0_78
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x00000b40 vmovdqu      (%r12), %ymm0
	0xc4, 0xc1, 0x7e, 0x6f, 0x4c, 0x24, 0x20, //0x00000b46 vmovdqu      $32(%r12), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x00000b4d vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0x7d, 0xd7, 0xca, //0x00000b51 vpmovmskb    %ymm2, %r9d
	0xc5, 0xf5, 0x74, 0xd6, //0x00000b55 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00000b59 vpmovmskb    %ymm2, %edx
	0xc5, 0xfd, 0x74, 0xd7, //0x00000b5d vpcmpeqb     %ymm7, %ymm0, %ymm2
	0xc5, 0x7d, 0xd7, 0xda, //0x00000b61 vpmovmskb    %ymm2, %r11d
	0xc5, 0xf5, 0x74, 0xd7, //0x00000b65 vpcmpeqb     %ymm7, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xda, //0x00000b69 vpmovmskb    %ymm2, %ebx
	0xc5, 0xbd, 0x64, 0xd0, //0x00000b6d vpcmpgtb     %ymm0, %ymm8, %ymm2
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x00000b71 vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xfd, 0xdb, 0xc2, //0x00000b76 vpand        %ymm2, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf8, //0x00000b7a vpmovmskb    %ymm0, %edi
	0xc5, 0xbd, 0x64, 0xc1, //0x00000b7e vpcmpgtb     %ymm1, %ymm8, %ymm0
	0xc4, 0xc1, 0x75, 0x64, 0xc9, //0x00000b82 vpcmpgtb     %ymm9, %ymm1, %ymm1
	0xc5, 0xf5, 0xdb, 0xc0, //0x00000b87 vpand        %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00000b8b vpmovmskb    %ymm0, %esi
	0x48, 0xc1, 0xe2, 0x20, //0x00000b8f shlq         $32, %rdx
	0x48, 0xc1, 0xe3, 0x20, //0x00000b93 shlq         $32, %rbx
	0x49, 0x09, 0xdb, //0x00000b97 orq          %rbx, %r11
	0x49, 0x83, 0xf8, 0xff, //0x00000b9a cmpq         $-1, %r8
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00000b9e jne          LBB0_80
	0x4d, 0x85, 0xdb, //0x00000ba4 testq        %r11, %r11
	0x0f, 0x85, 0x9f, 0x00, 0x00, 0x00, //0x00000ba7 jne          LBB0_89
	//0x00000bad LBB0_80
	0x48, 0xc1, 0xe6, 0x20, //0x00000bad shlq         $32, %rsi
	0x4c, 0x09, 0xca, //0x00000bb1 orq          %r9, %rdx
	0x4c, 0x89, 0xd9, //0x00000bb4 movq         %r11, %rcx
	0x4c, 0x09, 0xf9, //0x00000bb7 orq          %r15, %rcx
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x00000bba jne          LBB0_108
	0x48, 0x09, 0xfe, //0x00000bc0 orq          %rdi, %rsi
	0x48, 0x85, 0xd2, //0x00000bc3 testq        %rdx, %rdx
	0x0f, 0x85, 0xe1, 0x01, 0x00, 0x00, //0x00000bc6 jne          LBB0_109
	//0x00000bcc LBB0_82
	0x48, 0x85, 0xf6, //0x00000bcc testq        %rsi, %rsi
	0x0f, 0x85, 0x21, 0x1c, 0x00, 0x00, //0x00000bcf jne          LBB0_482
	0x49, 0x83, 0xc2, 0xc0, //0x00000bd5 addq         $-64, %r10
	0x49, 0x83, 0xc4, 0x40, //0x00000bd9 addq         $64, %r12
	0x49, 0x83, 0xfa, 0x3f, //0x00000bdd cmpq         $63, %r10
	0x0f, 0x87, 0x59, 0xff, 0xff, 0xff, //0x00000be1 ja           LBB0_78
	0xe9, 0xed, 0x0e, 0x00, 0x00, //0x00000be7 jmp          LBB0_84
	//0x00000bec LBB0_108
	0x4c, 0x89, 0xf9, //0x00000bec movq         %r15, %rcx
	0x48, 0xf7, 0xd1, //0x00000bef notq         %rcx
	0x4c, 0x21, 0xd9, //0x00000bf2 andq         %r11, %rcx
	0x4c, 0x8d, 0x0c, 0x09, //0x00000bf5 leaq         (%rcx,%rcx), %r9
	0x4d, 0x09, 0xf9, //0x00000bf9 orq          %r15, %r9
	0x4c, 0x89, 0xcb, //0x00000bfc movq         %r9, %rbx
	0x48, 0xf7, 0xd3, //0x00000bff notq         %rbx
	0x4c, 0x21, 0xdb, //0x00000c02 andq         %r11, %rbx
	0x49, 0x89, 0xc3, //0x00000c05 movq         %rax, %r11
	0x48, 0xb8, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000c08 movabsq      $-6148914691236517206, %rax
	0x48, 0x21, 0xc3, //0x00000c12 andq         %rax, %rbx
	0x45, 0x31, 0xff, //0x00000c15 xorl         %r15d, %r15d
	0x48, 0x01, 0xcb, //0x00000c18 addq         %rcx, %rbx
	0x41, 0x0f, 0x92, 0xc7, //0x00000c1b setb         %r15b
	0x48, 0x01, 0xdb, //0x00000c1f addq         %rbx, %rbx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000c22 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc3, //0x00000c2c xorq         %rax, %rbx
	0x4c, 0x89, 0xd8, //0x00000c2f movq         %r11, %rax
	0x4c, 0x21, 0xcb, //0x00000c32 andq         %r9, %rbx
	0x48, 0xf7, 0xd3, //0x00000c35 notq         %rbx
	0x48, 0x21, 0xda, //0x00000c38 andq         %rbx, %rdx
	0x48, 0x09, 0xfe, //0x00000c3b orq          %rdi, %rsi
	0x48, 0x85, 0xd2, //0x00000c3e testq        %rdx, %rdx
	0x0f, 0x84, 0x85, 0xff, 0xff, 0xff, //0x00000c41 je           LBB0_82
	0xe9, 0x61, 0x01, 0x00, 0x00, //0x00000c47 jmp          LBB0_109
	//0x00000c4c LBB0_89
	0x4c, 0x89, 0xe3, //0x00000c4c movq         %r12, %rbx
	0x48, 0x2b, 0x5d, 0xc0, //0x00000c4f subq         $-64(%rbp), %rbx
	0x4d, 0x0f, 0xbc, 0xc3, //0x00000c53 bsfq         %r11, %r8
	0x49, 0x01, 0xd8, //0x00000c57 addq         %rbx, %r8
	0xe9, 0x4e, 0xff, 0xff, 0xff, //0x00000c5a jmp          LBB0_80
	//0x00000c5f LBB0_92
	0x4b, 0xc7, 0x04, 0xc6, 0x02, 0x00, 0x00, 0x00, //0x00000c5f movq         $2, (%r14,%r8,8)
	0x48, 0x8b, 0x45, 0xb0, //0x00000c67 movq         $-80(%rbp), %rax
	0x48, 0x8b, 0x40, 0x08, //0x00000c6b movq         $8(%rax), %rax
	0xf6, 0x45, 0x98, 0x20, //0x00000c6f testb        $32, $-104(%rbp)
	0x48, 0x89, 0x45, 0xb8, //0x00000c73 movq         %rax, $-72(%rbp)
	0x0f, 0x85, 0x6c, 0x01, 0x00, 0x00, //0x00000c77 jne          LBB0_111
	0x48, 0x89, 0xc1, //0x00000c7d movq         %rax, %rcx
	0x4c, 0x29, 0xe1, //0x00000c80 subq         %r12, %rcx
	0x0f, 0x84, 0xdd, 0x1c, 0x00, 0x00, //0x00000c83 je           LBB0_510
	0x4b, 0x8d, 0x1c, 0x21, //0x00000c89 leaq         (%r9,%r12), %rbx
	0x48, 0x83, 0xf9, 0x40, //0x00000c8d cmpq         $64, %rcx
	0x0f, 0x82, 0xf1, 0x11, 0x00, 0x00, //0x00000c91 jb           LBB0_360
	0x4c, 0x89, 0xca, //0x00000c97 movq         %r9, %rdx
	0x41, 0x89, 0xcf, //0x00000c9a movl         %ecx, %r15d
	0x41, 0x83, 0xe7, 0x3f, //0x00000c9d andl         $63, %r15d
	0x48, 0x8d, 0x44, 0x30, 0xc0, //0x00000ca1 leaq         $-64(%rax,%rsi), %rax
	0x48, 0x83, 0xe0, 0xc0, //0x00000ca6 andq         $-64, %rax
	0x4c, 0x01, 0xe0, //0x00000caa addq         %r12, %rax
	0x4d, 0x8d, 0x44, 0x01, 0x40, //0x00000cad leaq         $64(%r9,%rax), %r8
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00000cb2 movq         $-1, %r11
	0x45, 0x31, 0xf6, //0x00000cb9 xorl         %r14d, %r14d
	0x90, 0x90, 0x90, 0x90, //0x00000cbc .p2align 4, 0x90
	//0x00000cc0 LBB0_96
	0xc5, 0xfe, 0x6f, 0x03, //0x00000cc0 vmovdqu      (%rbx), %ymm0
	0xc5, 0xfe, 0x6f, 0x4b, 0x20, //0x00000cc4 vmovdqu      $32(%rbx), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x00000cc9 vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00000ccd vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x00000cd1 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00000cd5 vpmovmskb    %ymm2, %edx
	0xc5, 0xfd, 0x74, 0xc7, //0x00000cd9 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00000cdd vpmovmskb    %ymm0, %esi
	0xc5, 0xf5, 0x74, 0xc7, //0x00000ce1 vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x00000ce5 vpmovmskb    %ymm0, %eax
	0x48, 0xc1, 0xe2, 0x20, //0x00000ce9 shlq         $32, %rdx
	0x48, 0xc1, 0xe0, 0x20, //0x00000ced shlq         $32, %rax
	0x48, 0x09, 0xc6, //0x00000cf1 orq          %rax, %rsi
	0x49, 0x83, 0xfb, 0xff, //0x00000cf4 cmpq         $-1, %r11
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00000cf8 jne          LBB0_98
	0x48, 0x85, 0xf6, //0x00000cfe testq        %rsi, %rsi
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x00000d01 jne          LBB0_105
	//0x00000d07 LBB0_98
	0x48, 0x09, 0xfa, //0x00000d07 orq          %rdi, %rdx
	0x48, 0x89, 0xf0, //0x00000d0a movq         %rsi, %rax
	0x4c, 0x09, 0xf0, //0x00000d0d orq          %r14, %rax
	0x0f, 0x85, 0x3c, 0x00, 0x00, 0x00, //0x00000d10 jne          LBB0_106
	//0x00000d16 LBB0_99
	0x48, 0x85, 0xd2, //0x00000d16 testq        %rdx, %rdx
	0x0f, 0x85, 0xe6, 0x0e, 0x00, 0x00, //0x00000d19 jne          LBB0_107
	//0x00000d1f LBB0_100
	0x48, 0x83, 0xc1, 0xc0, //0x00000d1f addq         $-64, %rcx
	0x48, 0x83, 0xc3, 0x40, //0x00000d23 addq         $64, %rbx
	0x48, 0x83, 0xf9, 0x3f, //0x00000d27 cmpq         $63, %rcx
	0x0f, 0x87, 0x8f, 0xff, 0xff, 0xff, //0x00000d2b ja           LBB0_96
	0xe9, 0x8e, 0x0e, 0x00, 0x00, //0x00000d31 jmp          LBB0_101
	//0x00000d36 LBB0_105
	0x48, 0x89, 0xd8, //0x00000d36 movq         %rbx, %rax
	0x4c, 0x29, 0xc8, //0x00000d39 subq         %r9, %rax
	0x4c, 0x0f, 0xbc, 0xde, //0x00000d3c bsfq         %rsi, %r11
	0x49, 0x01, 0xc3, //0x00000d40 addq         %rax, %r11
	0x48, 0x09, 0xfa, //0x00000d43 orq          %rdi, %rdx
	0x48, 0x89, 0xf0, //0x00000d46 movq         %rsi, %rax
	0x4c, 0x09, 0xf0, //0x00000d49 orq          %r14, %rax
	0x0f, 0x84, 0xc4, 0xff, 0xff, 0xff, //0x00000d4c je           LBB0_99
	//0x00000d52 LBB0_106
	0x4c, 0x89, 0xf0, //0x00000d52 movq         %r14, %rax
	0x48, 0xf7, 0xd0, //0x00000d55 notq         %rax
	0x48, 0x21, 0xf0, //0x00000d58 andq         %rsi, %rax
	0x4c, 0x8d, 0x14, 0x00, //0x00000d5b leaq         (%rax,%rax), %r10
	0x4d, 0x09, 0xf2, //0x00000d5f orq          %r14, %r10
	0x4c, 0x89, 0xd7, //0x00000d62 movq         %r10, %rdi
	0x48, 0xf7, 0xd7, //0x00000d65 notq         %rdi
	0x48, 0x21, 0xf7, //0x00000d68 andq         %rsi, %rdi
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000d6b movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf7, //0x00000d75 andq         %rsi, %rdi
	0x45, 0x31, 0xf6, //0x00000d78 xorl         %r14d, %r14d
	0x48, 0x01, 0xc7, //0x00000d7b addq         %rax, %rdi
	0x41, 0x0f, 0x92, 0xc6, //0x00000d7e setb         %r14b
	0x48, 0x01, 0xff, //0x00000d82 addq         %rdi, %rdi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000d85 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc7, //0x00000d8f xorq         %rax, %rdi
	0x4c, 0x21, 0xd7, //0x00000d92 andq         %r10, %rdi
	0x4c, 0x8b, 0x55, 0xd0, //0x00000d95 movq         $-48(%rbp), %r10
	0x48, 0xf7, 0xd7, //0x00000d99 notq         %rdi
	0x48, 0x21, 0xfa, //0x00000d9c andq         %rdi, %rdx
	0x48, 0x85, 0xd2, //0x00000d9f testq        %rdx, %rdx
	0x0f, 0x84, 0x77, 0xff, 0xff, 0xff, //0x00000da2 je           LBB0_100
	0xe9, 0x58, 0x0e, 0x00, 0x00, //0x00000da8 jmp          LBB0_107
	//0x00000dad LBB0_109
	0x48, 0x0f, 0xbc, 0xca, //0x00000dad bsfq         %rdx, %rcx
	0x48, 0x85, 0xf6, //0x00000db1 testq        %rsi, %rsi
	0x48, 0x8b, 0x45, 0xc0, //0x00000db4 movq         $-64(%rbp), %rax
	0x0f, 0x84, 0xc9, 0x01, 0x00, 0x00, //0x00000db8 je           LBB0_181
	0x48, 0x0f, 0xbc, 0xd6, //0x00000dbe bsfq         %rsi, %rdx
	0xe9, 0xc5, 0x01, 0x00, 0x00, //0x00000dc2 jmp          LBB0_182
	//0x00000dc7 LBB0_180
	0x49, 0x01, 0xcb, //0x00000dc7 addq         %rcx, %r11
	0xc5, 0xf8, 0x77, //0x00000dca vzeroupper   
	0x4d, 0x89, 0xde, //0x00000dcd movq         %r11, %r14
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000dd0 movq         $-1, %rcx
	0x4d, 0x85, 0xe4, //0x00000dd7 testq        %r12, %r12
	0x48, 0x8b, 0x55, 0xd0, //0x00000dda movq         $-48(%rbp), %rdx
	0x0f, 0x85, 0xdd, 0x01, 0x00, 0x00, //0x00000dde jne          LBB0_188
	0xe9, 0xdc, 0x19, 0x00, 0x00, //0x00000de4 jmp          LBB0_481
	//0x00000de9 LBB0_111
	0x48, 0x89, 0xc3, //0x00000de9 movq         %rax, %rbx
	0x4c, 0x29, 0xe3, //0x00000dec subq         %r12, %rbx
	0x0f, 0x84, 0x71, 0x1b, 0x00, 0x00, //0x00000def je           LBB0_510
	0x4c, 0x89, 0xe1, //0x00000df5 movq         %r12, %rcx
	0x4d, 0x01, 0xcc, //0x00000df8 addq         %r9, %r12
	0x48, 0x83, 0xfb, 0x40, //0x00000dfb cmpq         $64, %rbx
	0x4c, 0x89, 0x4d, 0xc0, //0x00000dff movq         %r9, $-64(%rbp)
	0x0f, 0x82, 0x98, 0x10, 0x00, 0x00, //0x00000e03 jb           LBB0_361
	0x41, 0x89, 0xde, //0x00000e09 movl         %ebx, %r14d
	0x41, 0x83, 0xe6, 0x3f, //0x00000e0c andl         $63, %r14d
	0x48, 0x8d, 0x44, 0x30, 0xc0, //0x00000e10 leaq         $-64(%rax,%rsi), %rax
	0x48, 0x83, 0xe0, 0xc0, //0x00000e15 andq         $-64, %rax
	0x49, 0x89, 0xc8, //0x00000e19 movq         %rcx, %r8
	0x48, 0x01, 0xc8, //0x00000e1c addq         %rcx, %rax
	0x49, 0x8d, 0x44, 0x01, 0x40, //0x00000e1f leaq         $64(%r9,%rax), %rax
	0x48, 0x89, 0x45, 0xa0, //0x00000e24 movq         %rax, $-96(%rbp)
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00000e28 movq         $-1, %r11
	0x45, 0x31, 0xff, //0x00000e2f xorl         %r15d, %r15d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000e32 .p2align 4, 0x90
	//0x00000e40 LBB0_114
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x00000e40 vmovdqu      (%r12), %ymm0
	0xc4, 0xc1, 0x7e, 0x6f, 0x4c, 0x24, 0x20, //0x00000e46 vmovdqu      $32(%r12), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x00000e4d vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0x7d, 0xd7, 0xca, //0x00000e51 vpmovmskb    %ymm2, %r9d
	0xc5, 0xf5, 0x74, 0xd6, //0x00000e55 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00000e59 vpmovmskb    %ymm2, %edx
	0xc5, 0xfd, 0x74, 0xd7, //0x00000e5d vpcmpeqb     %ymm7, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x00000e61 vpmovmskb    %ymm2, %ecx
	0xc5, 0xf5, 0x74, 0xd7, //0x00000e65 vpcmpeqb     %ymm7, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x00000e69 vpmovmskb    %ymm2, %eax
	0xc5, 0xbd, 0x64, 0xd0, //0x00000e6d vpcmpgtb     %ymm0, %ymm8, %ymm2
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x00000e71 vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xfd, 0xdb, 0xc2, //0x00000e76 vpand        %ymm2, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf8, //0x00000e7a vpmovmskb    %ymm0, %edi
	0xc5, 0xbd, 0x64, 0xc1, //0x00000e7e vpcmpgtb     %ymm1, %ymm8, %ymm0
	0xc4, 0xc1, 0x75, 0x64, 0xc9, //0x00000e82 vpcmpgtb     %ymm9, %ymm1, %ymm1
	0xc5, 0xf5, 0xdb, 0xc0, //0x00000e87 vpand        %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00000e8b vpmovmskb    %ymm0, %esi
	0x48, 0xc1, 0xe2, 0x20, //0x00000e8f shlq         $32, %rdx
	0x48, 0xc1, 0xe0, 0x20, //0x00000e93 shlq         $32, %rax
	0x48, 0x09, 0xc1, //0x00000e97 orq          %rax, %rcx
	0x49, 0x83, 0xfb, 0xff, //0x00000e9a cmpq         $-1, %r11
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00000e9e jne          LBB0_116
	0x48, 0x85, 0xc9, //0x00000ea4 testq        %rcx, %rcx
	0x0f, 0x85, 0x9d, 0x00, 0x00, 0x00, //0x00000ea7 jne          LBB0_125
	//0x00000ead LBB0_116
	0x48, 0xc1, 0xe6, 0x20, //0x00000ead shlq         $32, %rsi
	0x4c, 0x09, 0xca, //0x00000eb1 orq          %r9, %rdx
	0x48, 0x89, 0xc8, //0x00000eb4 movq         %rcx, %rax
	0x4c, 0x09, 0xf8, //0x00000eb7 orq          %r15, %rax
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x00000eba jne          LBB0_126
	0x48, 0x09, 0xfe, //0x00000ec0 orq          %rdi, %rsi
	0x48, 0x85, 0xd2, //0x00000ec3 testq        %rdx, %rdx
	0x0f, 0x85, 0x91, 0x00, 0x00, 0x00, //0x00000ec6 jne          LBB0_127
	//0x00000ecc LBB0_118
	0x48, 0x85, 0xf6, //0x00000ecc testq        %rsi, %rsi
	0x0f, 0x85, 0x4f, 0x19, 0x00, 0x00, //0x00000ecf jne          LBB0_502
	0x48, 0x83, 0xc3, 0xc0, //0x00000ed5 addq         $-64, %rbx
	0x49, 0x83, 0xc4, 0x40, //0x00000ed9 addq         $64, %r12
	0x48, 0x83, 0xfb, 0x3f, //0x00000edd cmpq         $63, %rbx
	0x0f, 0x87, 0x59, 0xff, 0xff, 0xff, //0x00000ee1 ja           LBB0_114
	0xe9, 0x68, 0x0d, 0x00, 0x00, //0x00000ee7 jmp          LBB0_120
	//0x00000eec LBB0_126
	0x4d, 0x89, 0xfa, //0x00000eec movq         %r15, %r10
	0x49, 0xf7, 0xd2, //0x00000eef notq         %r10
	0x49, 0x21, 0xca, //0x00000ef2 andq         %rcx, %r10
	0x4f, 0x8d, 0x0c, 0x12, //0x00000ef5 leaq         (%r10,%r10), %r9
	0x4d, 0x09, 0xf9, //0x00000ef9 orq          %r15, %r9
	0x4c, 0x89, 0xc8, //0x00000efc movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x00000eff notq         %rax
	0x48, 0x21, 0xc8, //0x00000f02 andq         %rcx, %rax
	0x48, 0xb9, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000f05 movabsq      $-6148914691236517206, %rcx
	0x48, 0x21, 0xc8, //0x00000f0f andq         %rcx, %rax
	0x45, 0x31, 0xff, //0x00000f12 xorl         %r15d, %r15d
	0x4c, 0x01, 0xd0, //0x00000f15 addq         %r10, %rax
	0x4c, 0x8b, 0x55, 0xd0, //0x00000f18 movq         $-48(%rbp), %r10
	0x41, 0x0f, 0x92, 0xc7, //0x00000f1c setb         %r15b
	0x48, 0x01, 0xc0, //0x00000f20 addq         %rax, %rax
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000f23 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xc8, //0x00000f2d xorq         %rcx, %rax
	0x4c, 0x21, 0xc8, //0x00000f30 andq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x00000f33 notq         %rax
	0x48, 0x21, 0xc2, //0x00000f36 andq         %rax, %rdx
	0x48, 0x09, 0xfe, //0x00000f39 orq          %rdi, %rsi
	0x48, 0x85, 0xd2, //0x00000f3c testq        %rdx, %rdx
	0x0f, 0x84, 0x87, 0xff, 0xff, 0xff, //0x00000f3f je           LBB0_118
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x00000f45 jmp          LBB0_127
	//0x00000f4a LBB0_125
	0x4c, 0x89, 0xe0, //0x00000f4a movq         %r12, %rax
	0x48, 0x2b, 0x45, 0xc0, //0x00000f4d subq         $-64(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xd9, //0x00000f51 bsfq         %rcx, %r11
	0x49, 0x01, 0xc3, //0x00000f55 addq         %rax, %r11
	0xe9, 0x50, 0xff, 0xff, 0xff, //0x00000f58 jmp          LBB0_116
	//0x00000f5d LBB0_127
	0x48, 0x0f, 0xbc, 0xca, //0x00000f5d bsfq         %rdx, %rcx
	0x48, 0x85, 0xf6, //0x00000f61 testq        %rsi, %rsi
	0x48, 0x8b, 0x45, 0xc0, //0x00000f64 movq         $-64(%rbp), %rax
	0x0f, 0x84, 0x8d, 0x01, 0x00, 0x00, //0x00000f68 je           LBB0_203
	0x48, 0x0f, 0xbc, 0xd6, //0x00000f6e bsfq         %rsi, %rdx
	0x4c, 0x8b, 0x75, 0xc8, //0x00000f72 movq         $-56(%rbp), %r14
	0x49, 0x29, 0xc4, //0x00000f76 subq         %rax, %r12
	0x48, 0x39, 0xca, //0x00000f79 cmpq         %rcx, %rdx
	0x0f, 0x83, 0x8e, 0x01, 0x00, 0x00, //0x00000f7c jae          LBB0_204
	0xe9, 0x19, 0x1a, 0x00, 0x00, //0x00000f82 jmp          LBB0_129
	//0x00000f87 LBB0_181
	0xba, 0x40, 0x00, 0x00, 0x00, //0x00000f87 movl         $64, %edx
	//0x00000f8c LBB0_182
	0x4c, 0x8b, 0x55, 0xd0, //0x00000f8c movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x00000f90 movq         $-56(%rbp), %r14
	0x49, 0x29, 0xc4, //0x00000f94 subq         %rax, %r12
	0x48, 0x39, 0xca, //0x00000f97 cmpq         %rcx, %rdx
	0x0f, 0x82, 0xe5, 0x19, 0x00, 0x00, //0x00000f9a jb           LBB0_509
	0x4d, 0x8d, 0x64, 0x0c, 0x01, //0x00000fa0 leaq         $1(%r12,%rcx), %r12
	0xe9, 0x10, 0x0b, 0x00, 0x00, //0x00000fa5 jmp          LBB0_184
	//0x00000faa LBB0_186
	0x49, 0x01, 0xce, //0x00000faa addq         %rcx, %r14
	//0x00000fad LBB0_187
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000fad movq         $-1, %rcx
	0x4d, 0x85, 0xe4, //0x00000fb4 testq        %r12, %r12
	0x48, 0x8b, 0x55, 0xd0, //0x00000fb7 movq         $-48(%rbp), %rdx
	0x0f, 0x84, 0x04, 0x18, 0x00, 0x00, //0x00000fbb je           LBB0_481
	//0x00000fc1 LBB0_188
	0x4d, 0x85, 0xc0, //0x00000fc1 testq        %r8, %r8
	0x0f, 0x84, 0xfb, 0x17, 0x00, 0x00, //0x00000fc4 je           LBB0_481
	0x48, 0x85, 0xc0, //0x00000fca testq        %rax, %rax
	0x0f, 0x84, 0xf2, 0x17, 0x00, 0x00, //0x00000fcd je           LBB0_481
	0x4d, 0x29, 0xfe, //0x00000fd3 subq         %r15, %r14
	0x49, 0x8d, 0x4e, 0xff, //0x00000fd6 leaq         $-1(%r14), %rcx
	0x49, 0x39, 0xcc, //0x00000fda cmpq         %rcx, %r12
	0x0f, 0x84, 0x88, 0x00, 0x00, 0x00, //0x00000fdd je           LBB0_196
	0x48, 0x39, 0xc8, //0x00000fe3 cmpq         %rcx, %rax
	0x0f, 0x84, 0x7f, 0x00, 0x00, 0x00, //0x00000fe6 je           LBB0_196
	0x49, 0x39, 0xc8, //0x00000fec cmpq         %rcx, %r8
	0x0f, 0x84, 0x76, 0x00, 0x00, 0x00, //0x00000fef je           LBB0_196
	0x4d, 0x85, 0xc0, //0x00000ff5 testq        %r8, %r8
	0xc5, 0xfe, 0x6f, 0x2d, 0x00, 0xf0, 0xff, 0xff, //0x00000ff8 vmovdqu      $-4096(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x18, 0xf0, 0xff, 0xff, //0x00001000 vmovdqu      $-4072(%rip), %ymm6  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x30, 0xf0, 0xff, 0xff, //0x00001008 vmovdqu      $-4048(%rip), %ymm7  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x48, 0xf0, 0xff, 0xff, //0x00001010 vmovdqu      $-4024(%rip), %ymm8  /* LCPI0_3+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00001018 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x15, 0x5b, 0xf0, 0xff, 0xff, //0x0000101d vmovdqu      $-4005(%rip), %ymm10  /* LCPI0_4+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x73, 0xf0, 0xff, 0xff, //0x00001025 vmovdqu      $-3981(%rip), %ymm11  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x8b, 0xf0, 0xff, 0xff, //0x0000102d vmovdqu      $-3957(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0xa3, 0xf0, 0xff, 0xff, //0x00001035 vmovdqu      $-3933(%rip), %ymm13  /* LCPI0_7+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0xbb, 0xf0, 0xff, 0xff, //0x0000103d vmovdqu      $-3909(%rip), %ymm14  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0xd3, 0xf0, 0xff, 0xff, //0x00001045 vmovdqu      $-3885(%rip), %ymm15  /* LCPI0_9+0(%rip) */
	0x0f, 0x8e, 0x85, 0x00, 0x00, 0x00, //0x0000104d jle          LBB0_200
	0x49, 0x8d, 0x48, 0xff, //0x00001053 leaq         $-1(%r8), %rcx
	0x49, 0x39, 0xcc, //0x00001057 cmpq         %rcx, %r12
	0x0f, 0x84, 0x78, 0x00, 0x00, 0x00, //0x0000105a je           LBB0_200
	0x49, 0xf7, 0xd0, //0x00001060 notq         %r8
	0x4d, 0x89, 0xc6, //0x00001063 movq         %r8, %r14
	0xe9, 0x58, 0x00, 0x00, 0x00, //0x00001066 jmp          LBB0_197
	//0x0000106b LBB0_196
	0x49, 0xf7, 0xde, //0x0000106b negq         %r14
	0xc5, 0xfe, 0x6f, 0x2d, 0x8a, 0xef, 0xff, 0xff, //0x0000106e vmovdqu      $-4214(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0xa2, 0xef, 0xff, 0xff, //0x00001076 vmovdqu      $-4190(%rip), %ymm6  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0xba, 0xef, 0xff, 0xff, //0x0000107e vmovdqu      $-4166(%rip), %ymm7  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0xd2, 0xef, 0xff, 0xff, //0x00001086 vmovdqu      $-4142(%rip), %ymm8  /* LCPI0_3+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000108e vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x15, 0xe5, 0xef, 0xff, 0xff, //0x00001093 vmovdqu      $-4123(%rip), %ymm10  /* LCPI0_4+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0xfd, 0xef, 0xff, 0xff, //0x0000109b vmovdqu      $-4099(%rip), %ymm11  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x15, 0xf0, 0xff, 0xff, //0x000010a3 vmovdqu      $-4075(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x2d, 0xf0, 0xff, 0xff, //0x000010ab vmovdqu      $-4051(%rip), %ymm13  /* LCPI0_7+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x45, 0xf0, 0xff, 0xff, //0x000010b3 vmovdqu      $-4027(%rip), %ymm14  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0x5d, 0xf0, 0xff, 0xff, //0x000010bb vmovdqu      $-4003(%rip), %ymm15  /* LCPI0_9+0(%rip) */
	//0x000010c3 LBB0_197
	0x4d, 0x85, 0xf6, //0x000010c3 testq        %r14, %r14
	0x4c, 0x8b, 0x65, 0xa8, //0x000010c6 movq         $-88(%rbp), %r12
	0x0f, 0x88, 0xf2, 0x16, 0x00, 0x00, //0x000010ca js           LBB0_480
	0x48, 0x8b, 0x0a, //0x000010d0 movq         (%rdx), %rcx
	0xe9, 0x76, 0xf1, 0xff, 0xff, //0x000010d3 jmp          LBB0_199
	//0x000010d8 LBB0_200
	0x48, 0x89, 0xc1, //0x000010d8 movq         %rax, %rcx
	0x4c, 0x09, 0xe1, //0x000010db orq          %r12, %rcx
	0x4c, 0x39, 0xe0, //0x000010de cmpq         %r12, %rax
	0x0f, 0x8c, 0x98, 0x02, 0x00, 0x00, //0x000010e1 jl           LBB0_249
	0x48, 0x85, 0xc9, //0x000010e7 testq        %rcx, %rcx
	0x0f, 0x88, 0x8f, 0x02, 0x00, 0x00, //0x000010ea js           LBB0_249
	0x48, 0xf7, 0xd0, //0x000010f0 notq         %rax
	0x49, 0x89, 0xc6, //0x000010f3 movq         %rax, %r14
	0xe9, 0xc8, 0xff, 0xff, 0xff, //0x000010f6 jmp          LBB0_197
	//0x000010fb LBB0_203
	0xba, 0x40, 0x00, 0x00, 0x00, //0x000010fb movl         $64, %edx
	0x4c, 0x8b, 0x75, 0xc8, //0x00001100 movq         $-56(%rbp), %r14
	0x49, 0x29, 0xc4, //0x00001104 subq         %rax, %r12
	0x48, 0x39, 0xca, //0x00001107 cmpq         %rcx, %rdx
	0x0f, 0x82, 0x90, 0x18, 0x00, 0x00, //0x0000110a jb           LBB0_129
	//0x00001110 LBB0_204
	0x4d, 0x8d, 0x64, 0x0c, 0x01, //0x00001110 leaq         $1(%r12,%rcx), %r12
	0xe9, 0xfe, 0x0a, 0x00, 0x00, //0x00001115 jmp          LBB0_205
	//0x0000111a LBB0_209
	0x49, 0x8b, 0x06, //0x0000111a movq         (%r14), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x0000111d cmpq         $4095, %rax
	0x0f, 0x8f, 0x49, 0x16, 0x00, 0x00, //0x00001123 jg           LBB0_489
	0x48, 0x8d, 0x48, 0x01, //0x00001129 leaq         $1(%rax), %rcx
	0x49, 0x89, 0x0e, //0x0000112d movq         %rcx, (%r14)
	0x49, 0xc7, 0x44, 0xc6, 0x08, 0x05, 0x00, 0x00, 0x00, //0x00001130 movq         $5, $8(%r14,%rax,8)
	0xe9, 0x32, 0xf1, 0xff, 0xff, //0x00001139 jmp          LBB0_3
	//0x0000113e LBB0_211
	0x48, 0x8b, 0x45, 0xb0, //0x0000113e movq         $-80(%rbp), %rax
	0x48, 0x8b, 0x40, 0x08, //0x00001142 movq         $8(%rax), %rax
	0xf6, 0x45, 0x98, 0x20, //0x00001146 testb        $32, $-104(%rbp)
	0x48, 0x89, 0x45, 0xb8, //0x0000114a movq         %rax, $-72(%rbp)
	0x0f, 0x85, 0x46, 0x02, 0x00, 0x00, //0x0000114e jne          LBB0_250
	0x48, 0x89, 0xc1, //0x00001154 movq         %rax, %rcx
	0x4c, 0x29, 0xe1, //0x00001157 subq         %r12, %rcx
	0x0f, 0x84, 0x06, 0x18, 0x00, 0x00, //0x0000115a je           LBB0_510
	0x4b, 0x8d, 0x1c, 0x21, //0x00001160 leaq         (%r9,%r12), %rbx
	0x48, 0x83, 0xf9, 0x40, //0x00001164 cmpq         $64, %rcx
	0x4c, 0x89, 0xca, //0x00001168 movq         %r9, %rdx
	0x0f, 0x82, 0x71, 0x0d, 0x00, 0x00, //0x0000116b jb           LBB0_363
	0x41, 0x89, 0xcf, //0x00001171 movl         %ecx, %r15d
	0x41, 0x83, 0xe7, 0x3f, //0x00001174 andl         $63, %r15d
	0x48, 0x8d, 0x44, 0x30, 0xc0, //0x00001178 leaq         $-64(%rax,%rsi), %rax
	0x48, 0x83, 0xe0, 0xc0, //0x0000117d andq         $-64, %rax
	0x4c, 0x01, 0xe0, //0x00001181 addq         %r12, %rax
	0x49, 0x89, 0xd1, //0x00001184 movq         %rdx, %r9
	0x4c, 0x8d, 0x44, 0x02, 0x40, //0x00001187 leaq         $64(%rdx,%rax), %r8
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x0000118c movq         $-1, %r11
	0x45, 0x31, 0xf6, //0x00001193 xorl         %r14d, %r14d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001196 .p2align 4, 0x90
	//0x000011a0 LBB0_215
	0xc5, 0xfe, 0x6f, 0x03, //0x000011a0 vmovdqu      (%rbx), %ymm0
	0xc5, 0xfe, 0x6f, 0x4b, 0x20, //0x000011a4 vmovdqu      $32(%rbx), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x000011a9 vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x000011ad vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x000011b1 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x000011b5 vpmovmskb    %ymm2, %edx
	0xc5, 0xfd, 0x74, 0xc7, //0x000011b9 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x000011bd vpmovmskb    %ymm0, %esi
	0xc5, 0xf5, 0x74, 0xc7, //0x000011c1 vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x000011c5 vpmovmskb    %ymm0, %eax
	0x48, 0xc1, 0xe2, 0x20, //0x000011c9 shlq         $32, %rdx
	0x48, 0xc1, 0xe0, 0x20, //0x000011cd shlq         $32, %rax
	0x48, 0x09, 0xc6, //0x000011d1 orq          %rax, %rsi
	0x49, 0x83, 0xfb, 0xff, //0x000011d4 cmpq         $-1, %r11
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x000011d8 jne          LBB0_217
	0x48, 0x85, 0xf6, //0x000011de testq        %rsi, %rsi
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x000011e1 jne          LBB0_224
	//0x000011e7 LBB0_217
	0x48, 0x09, 0xfa, //0x000011e7 orq          %rdi, %rdx
	0x48, 0x89, 0xf0, //0x000011ea movq         %rsi, %rax
	0x4c, 0x09, 0xf0, //0x000011ed orq          %r14, %rax
	0x0f, 0x85, 0x3c, 0x00, 0x00, 0x00, //0x000011f0 jne          LBB0_225
	//0x000011f6 LBB0_218
	0x48, 0x85, 0xd2, //0x000011f6 testq        %rdx, %rdx
	0x0f, 0x85, 0x48, 0x0b, 0x00, 0x00, //0x000011f9 jne          LBB0_226
	//0x000011ff LBB0_219
	0x48, 0x83, 0xc1, 0xc0, //0x000011ff addq         $-64, %rcx
	0x48, 0x83, 0xc3, 0x40, //0x00001203 addq         $64, %rbx
	0x48, 0x83, 0xf9, 0x3f, //0x00001207 cmpq         $63, %rcx
	0x0f, 0x87, 0x8f, 0xff, 0xff, 0xff, //0x0000120b ja           LBB0_215
	0xe9, 0xf0, 0x0a, 0x00, 0x00, //0x00001211 jmp          LBB0_220
	//0x00001216 LBB0_224
	0x48, 0x89, 0xd8, //0x00001216 movq         %rbx, %rax
	0x4c, 0x29, 0xc8, //0x00001219 subq         %r9, %rax
	0x4c, 0x0f, 0xbc, 0xde, //0x0000121c bsfq         %rsi, %r11
	0x49, 0x01, 0xc3, //0x00001220 addq         %rax, %r11
	0x48, 0x09, 0xfa, //0x00001223 orq          %rdi, %rdx
	0x48, 0x89, 0xf0, //0x00001226 movq         %rsi, %rax
	0x4c, 0x09, 0xf0, //0x00001229 orq          %r14, %rax
	0x0f, 0x84, 0xc4, 0xff, 0xff, 0xff, //0x0000122c je           LBB0_218
	//0x00001232 LBB0_225
	0x4c, 0x89, 0xf0, //0x00001232 movq         %r14, %rax
	0x48, 0xf7, 0xd0, //0x00001235 notq         %rax
	0x48, 0x21, 0xf0, //0x00001238 andq         %rsi, %rax
	0x4c, 0x8d, 0x14, 0x00, //0x0000123b leaq         (%rax,%rax), %r10
	0x4d, 0x09, 0xf2, //0x0000123f orq          %r14, %r10
	0x4c, 0x89, 0xd7, //0x00001242 movq         %r10, %rdi
	0x48, 0xf7, 0xd7, //0x00001245 notq         %rdi
	0x48, 0x21, 0xf7, //0x00001248 andq         %rsi, %rdi
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000124b movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf7, //0x00001255 andq         %rsi, %rdi
	0x45, 0x31, 0xf6, //0x00001258 xorl         %r14d, %r14d
	0x48, 0x01, 0xc7, //0x0000125b addq         %rax, %rdi
	0x41, 0x0f, 0x92, 0xc6, //0x0000125e setb         %r14b
	0x48, 0x01, 0xff, //0x00001262 addq         %rdi, %rdi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00001265 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc7, //0x0000126f xorq         %rax, %rdi
	0x4c, 0x21, 0xd7, //0x00001272 andq         %r10, %rdi
	0x4c, 0x8b, 0x55, 0xd0, //0x00001275 movq         $-48(%rbp), %r10
	0x48, 0xf7, 0xd7, //0x00001279 notq         %rdi
	0x48, 0x21, 0xfa, //0x0000127c andq         %rdi, %rdx
	0x48, 0x85, 0xd2, //0x0000127f testq        %rdx, %rdx
	0x0f, 0x84, 0x77, 0xff, 0xff, 0xff, //0x00001282 je           LBB0_219
	0xe9, 0xba, 0x0a, 0x00, 0x00, //0x00001288 jmp          LBB0_226
	//0x0000128d LBB0_227
	0x48, 0x8b, 0x45, 0xb0, //0x0000128d movq         $-80(%rbp), %rax
	0x4c, 0x8b, 0x70, 0x08, //0x00001291 movq         $8(%rax), %r14
	0x4d, 0x29, 0xe6, //0x00001295 subq         %r12, %r14
	0x0f, 0x84, 0xb3, 0x16, 0x00, 0x00, //0x00001298 je           LBB0_498
	0x4c, 0x89, 0x65, 0xa8, //0x0000129e movq         %r12, $-88(%rbp)
	0x4c, 0x89, 0xc8, //0x000012a2 movq         %r9, %rax
	0x4c, 0x01, 0xe0, //0x000012a5 addq         %r12, %rax
	0x49, 0x89, 0xc1, //0x000012a8 movq         %rax, %r9
	0x80, 0x38, 0x30, //0x000012ab cmpb         $48, (%rax)
	0x0f, 0x85, 0xb5, 0x02, 0x00, 0x00, //0x000012ae jne          LBB0_271
	0x41, 0xbf, 0x01, 0x00, 0x00, 0x00, //0x000012b4 movl         $1, %r15d
	0x49, 0x83, 0xfe, 0x01, //0x000012ba cmpq         $1, %r14
	0x0f, 0x85, 0x7a, 0x02, 0x00, 0x00, //0x000012be jne          LBB0_269
	0x4c, 0x8b, 0x65, 0xa8, //0x000012c4 movq         $-88(%rbp), %r12
	0xe9, 0x9d, 0x08, 0x00, 0x00, //0x000012c8 jmp          LBB0_343
	//0x000012cd LBB0_231
	0x48, 0x8b, 0x45, 0xb0, //0x000012cd movq         $-80(%rbp), %rax
	0x48, 0x8b, 0x50, 0x08, //0x000012d1 movq         $8(%rax), %rdx
	0x48, 0x8d, 0x42, 0xfd, //0x000012d5 leaq         $-3(%rdx), %rax
	0x49, 0x39, 0xc5, //0x000012d9 cmpq         %rax, %r13
	0x0f, 0x83, 0x0c, 0x15, 0x00, 0x00, //0x000012dc jae          LBB0_490
	0x41, 0x81, 0x3f, 0x6e, 0x75, 0x6c, 0x6c, //0x000012e2 cmpl         $1819047278, (%r15)
	0x0f, 0x84, 0x99, 0xef, 0xff, 0xff, //0x000012e9 je           LBB0_1
	0xe9, 0x5a, 0x15, 0x00, 0x00, //0x000012ef jmp          LBB0_233
	//0x000012f4 LBB0_238
	0x49, 0x8b, 0x06, //0x000012f4 movq         (%r14), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x000012f7 cmpq         $4095, %rax
	0x0f, 0x8f, 0x6f, 0x14, 0x00, 0x00, //0x000012fd jg           LBB0_489
	0x48, 0x8d, 0x48, 0x01, //0x00001303 leaq         $1(%rax), %rcx
	0x49, 0x89, 0x0e, //0x00001307 movq         %rcx, (%r14)
	0x49, 0xc7, 0x44, 0xc6, 0x08, 0x06, 0x00, 0x00, 0x00, //0x0000130a movq         $6, $8(%r14,%rax,8)
	0xe9, 0x58, 0xef, 0xff, 0xff, //0x00001313 jmp          LBB0_3
	//0x00001318 LBB0_240
	0x48, 0x8b, 0x45, 0xb0, //0x00001318 movq         $-80(%rbp), %rax
	0x48, 0x8b, 0x50, 0x08, //0x0000131c movq         $8(%rax), %rdx
	0x48, 0x8d, 0x42, 0xfc, //0x00001320 leaq         $-4(%rdx), %rax
	0x49, 0x39, 0xc5, //0x00001324 cmpq         %rax, %r13
	0x0f, 0x83, 0xc1, 0x14, 0x00, 0x00, //0x00001327 jae          LBB0_490
	0x43, 0x8b, 0x14, 0x21, //0x0000132d movl         (%r9,%r12), %edx
	0x81, 0xfa, 0x61, 0x6c, 0x73, 0x65, //0x00001331 cmpl         $1702063201, %edx
	0x0f, 0x85, 0x63, 0x15, 0x00, 0x00, //0x00001337 jne          LBB0_491
	0x4c, 0x89, 0xe0, //0x0000133d movq         %r12, %rax
	0x4d, 0x8d, 0x65, 0x05, //0x00001340 leaq         $5(%r13), %r12
	0x4d, 0x89, 0x22, //0x00001344 movq         %r12, (%r10)
	0x4c, 0x89, 0xe9, //0x00001347 movq         %r13, %rcx
	0x48, 0x85, 0xc0, //0x0000134a testq        %rax, %rax
	0x0f, 0x8f, 0x1d, 0xef, 0xff, 0xff, //0x0000134d jg           LBB0_3
	0xe9, 0x81, 0x14, 0x00, 0x00, //0x00001353 jmp          LBB0_501
	//0x00001358 LBB0_243
	0x48, 0x8b, 0x45, 0xb0, //0x00001358 movq         $-80(%rbp), %rax
	0x48, 0x8b, 0x50, 0x08, //0x0000135c movq         $8(%rax), %rdx
	0x48, 0x8d, 0x42, 0xfd, //0x00001360 leaq         $-3(%rdx), %rax
	0x49, 0x39, 0xc5, //0x00001364 cmpq         %rax, %r13
	0x0f, 0x83, 0x81, 0x14, 0x00, 0x00, //0x00001367 jae          LBB0_490
	0x41, 0x81, 0x3f, 0x74, 0x72, 0x75, 0x65, //0x0000136d cmpl         $1702195828, (%r15)
	0x0f, 0x84, 0x0e, 0xef, 0xff, 0xff, //0x00001374 je           LBB0_1
	0xe9, 0x76, 0x15, 0x00, 0x00, //0x0000137a jmp          LBB0_245
	//0x0000137f LBB0_249
	0x48, 0x85, 0xc9, //0x0000137f testq        %rcx, %rcx
	0x49, 0x8d, 0x4c, 0x24, 0xff, //0x00001382 leaq         $-1(%r12), %rcx
	0x49, 0xf7, 0xd4, //0x00001387 notq         %r12
	0x4d, 0x0f, 0x48, 0xe6, //0x0000138a cmovsq       %r14, %r12
	0x48, 0x39, 0xc8, //0x0000138e cmpq         %rcx, %rax
	0x4d, 0x0f, 0x44, 0xf4, //0x00001391 cmoveq       %r12, %r14
	0xe9, 0x29, 0xfd, 0xff, 0xff, //0x00001395 jmp          LBB0_197
	//0x0000139a LBB0_250
	0x48, 0x89, 0xc3, //0x0000139a movq         %rax, %rbx
	0x4c, 0x29, 0xe3, //0x0000139d subq         %r12, %rbx
	0x0f, 0x84, 0xc0, 0x15, 0x00, 0x00, //0x000013a0 je           LBB0_510
	0x4c, 0x89, 0xe1, //0x000013a6 movq         %r12, %rcx
	0x4d, 0x01, 0xcc, //0x000013a9 addq         %r9, %r12
	0x48, 0x83, 0xfb, 0x40, //0x000013ac cmpq         $64, %rbx
	0x4c, 0x89, 0x4d, 0xc0, //0x000013b0 movq         %r9, $-64(%rbp)
	0x0f, 0x82, 0x44, 0x0b, 0x00, 0x00, //0x000013b4 jb           LBB0_364
	0x41, 0x89, 0xde, //0x000013ba movl         %ebx, %r14d
	0x41, 0x83, 0xe6, 0x3f, //0x000013bd andl         $63, %r14d
	0x48, 0x8d, 0x44, 0x30, 0xc0, //0x000013c1 leaq         $-64(%rax,%rsi), %rax
	0x48, 0x83, 0xe0, 0xc0, //0x000013c6 andq         $-64, %rax
	0x49, 0x89, 0xc8, //0x000013ca movq         %rcx, %r8
	0x48, 0x01, 0xc8, //0x000013cd addq         %rcx, %rax
	0x49, 0x8d, 0x44, 0x01, 0x40, //0x000013d0 leaq         $64(%r9,%rax), %rax
	0x48, 0x89, 0x45, 0xa0, //0x000013d5 movq         %rax, $-96(%rbp)
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000013d9 movq         $-1, %r11
	0x45, 0x31, 0xff, //0x000013e0 xorl         %r15d, %r15d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000013e3 .p2align 4, 0x90
	//0x000013f0 LBB0_253
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x000013f0 vmovdqu      (%r12), %ymm0
	0xc4, 0xc1, 0x7e, 0x6f, 0x4c, 0x24, 0x20, //0x000013f6 vmovdqu      $32(%r12), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x000013fd vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0x7d, 0xd7, 0xca, //0x00001401 vpmovmskb    %ymm2, %r9d
	0xc5, 0xf5, 0x74, 0xd6, //0x00001405 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00001409 vpmovmskb    %ymm2, %edx
	0xc5, 0xfd, 0x74, 0xd7, //0x0000140d vpcmpeqb     %ymm7, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x00001411 vpmovmskb    %ymm2, %ecx
	0xc5, 0xf5, 0x74, 0xd7, //0x00001415 vpcmpeqb     %ymm7, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x00001419 vpmovmskb    %ymm2, %eax
	0xc5, 0xbd, 0x64, 0xd0, //0x0000141d vpcmpgtb     %ymm0, %ymm8, %ymm2
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x00001421 vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xfd, 0xdb, 0xc2, //0x00001426 vpand        %ymm2, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf8, //0x0000142a vpmovmskb    %ymm0, %edi
	0xc5, 0xbd, 0x64, 0xc1, //0x0000142e vpcmpgtb     %ymm1, %ymm8, %ymm0
	0xc4, 0xc1, 0x75, 0x64, 0xc9, //0x00001432 vpcmpgtb     %ymm9, %ymm1, %ymm1
	0xc5, 0xf5, 0xdb, 0xc0, //0x00001437 vpand        %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x0000143b vpmovmskb    %ymm0, %esi
	0x48, 0xc1, 0xe2, 0x20, //0x0000143f shlq         $32, %rdx
	0x48, 0xc1, 0xe0, 0x20, //0x00001443 shlq         $32, %rax
	0x48, 0x09, 0xc1, //0x00001447 orq          %rax, %rcx
	0x49, 0x83, 0xfb, 0xff, //0x0000144a cmpq         $-1, %r11
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x0000144e jne          LBB0_255
	0x48, 0x85, 0xc9, //0x00001454 testq        %rcx, %rcx
	0x0f, 0x85, 0x9d, 0x00, 0x00, 0x00, //0x00001457 jne          LBB0_264
	//0x0000145d LBB0_255
	0x48, 0xc1, 0xe6, 0x20, //0x0000145d shlq         $32, %rsi
	0x4c, 0x09, 0xca, //0x00001461 orq          %r9, %rdx
	0x48, 0x89, 0xc8, //0x00001464 movq         %rcx, %rax
	0x4c, 0x09, 0xf8, //0x00001467 orq          %r15, %rax
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x0000146a jne          LBB0_265
	0x48, 0x09, 0xfe, //0x00001470 orq          %rdi, %rsi
	0x48, 0x85, 0xd2, //0x00001473 testq        %rdx, %rdx
	0x0f, 0x85, 0x91, 0x00, 0x00, 0x00, //0x00001476 jne          LBB0_266
	//0x0000147c LBB0_257
	0x48, 0x85, 0xf6, //0x0000147c testq        %rsi, %rsi
	0x0f, 0x85, 0x9f, 0x13, 0x00, 0x00, //0x0000147f jne          LBB0_502
	0x48, 0x83, 0xc3, 0xc0, //0x00001485 addq         $-64, %rbx
	0x49, 0x83, 0xc4, 0x40, //0x00001489 addq         $64, %r12
	0x48, 0x83, 0xfb, 0x3f, //0x0000148d cmpq         $63, %rbx
	0x0f, 0x87, 0x59, 0xff, 0xff, 0xff, //0x00001491 ja           LBB0_253
	0xe9, 0xe7, 0x08, 0x00, 0x00, //0x00001497 jmp          LBB0_259
	//0x0000149c LBB0_265
	0x4d, 0x89, 0xfa, //0x0000149c movq         %r15, %r10
	0x49, 0xf7, 0xd2, //0x0000149f notq         %r10
	0x49, 0x21, 0xca, //0x000014a2 andq         %rcx, %r10
	0x4f, 0x8d, 0x0c, 0x12, //0x000014a5 leaq         (%r10,%r10), %r9
	0x4d, 0x09, 0xf9, //0x000014a9 orq          %r15, %r9
	0x4c, 0x89, 0xc8, //0x000014ac movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x000014af notq         %rax
	0x48, 0x21, 0xc8, //0x000014b2 andq         %rcx, %rax
	0x48, 0xb9, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000014b5 movabsq      $-6148914691236517206, %rcx
	0x48, 0x21, 0xc8, //0x000014bf andq         %rcx, %rax
	0x45, 0x31, 0xff, //0x000014c2 xorl         %r15d, %r15d
	0x4c, 0x01, 0xd0, //0x000014c5 addq         %r10, %rax
	0x4c, 0x8b, 0x55, 0xd0, //0x000014c8 movq         $-48(%rbp), %r10
	0x41, 0x0f, 0x92, 0xc7, //0x000014cc setb         %r15b
	0x48, 0x01, 0xc0, //0x000014d0 addq         %rax, %rax
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000014d3 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xc8, //0x000014dd xorq         %rcx, %rax
	0x4c, 0x21, 0xc8, //0x000014e0 andq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x000014e3 notq         %rax
	0x48, 0x21, 0xc2, //0x000014e6 andq         %rax, %rdx
	0x48, 0x09, 0xfe, //0x000014e9 orq          %rdi, %rsi
	0x48, 0x85, 0xd2, //0x000014ec testq        %rdx, %rdx
	0x0f, 0x84, 0x87, 0xff, 0xff, 0xff, //0x000014ef je           LBB0_257
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x000014f5 jmp          LBB0_266
	//0x000014fa LBB0_264
	0x4c, 0x89, 0xe0, //0x000014fa movq         %r12, %rax
	0x48, 0x2b, 0x45, 0xc0, //0x000014fd subq         $-64(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xd9, //0x00001501 bsfq         %rcx, %r11
	0x49, 0x01, 0xc3, //0x00001505 addq         %rax, %r11
	0xe9, 0x50, 0xff, 0xff, 0xff, //0x00001508 jmp          LBB0_255
	//0x0000150d LBB0_266
	0x48, 0x0f, 0xbc, 0xca, //0x0000150d bsfq         %rdx, %rcx
	0x48, 0x85, 0xf6, //0x00001511 testq        %rsi, %rsi
	0x48, 0x8b, 0x45, 0xc0, //0x00001514 movq         $-64(%rbp), %rax
	0x0f, 0x84, 0x20, 0x04, 0x00, 0x00, //0x00001518 je           LBB0_320
	0x48, 0x0f, 0xbc, 0xd6, //0x0000151e bsfq         %rsi, %rdx
	0xe9, 0x1c, 0x04, 0x00, 0x00, //0x00001522 jmp          LBB0_321
	//0x00001527 LBB0_268
	0x4c, 0x89, 0xca, //0x00001527 movq         %r9, %rdx
	0x48, 0xf7, 0xd2, //0x0000152a notq         %rdx
	0x49, 0x01, 0xd5, //0x0000152d addq         %rdx, %r13
	0x49, 0x39, 0xcd, //0x00001530 cmpq         %rcx, %r13
	0x0f, 0x82, 0x3f, 0xef, 0xff, 0xff, //0x00001533 jb           LBB0_36
	0xe9, 0x28, 0x12, 0x00, 0x00, //0x00001539 jmp          LBB0_474
	//0x0000153e LBB0_269
	0x41, 0x8a, 0x49, 0x01, //0x0000153e movb         $1(%r9), %cl
	0x80, 0xc1, 0xd2, //0x00001542 addb         $-46, %cl
	0x80, 0xf9, 0x37, //0x00001545 cmpb         $55, %cl
	0x4c, 0x8b, 0x65, 0xa8, //0x00001548 movq         $-88(%rbp), %r12
	0x0f, 0x87, 0x18, 0x06, 0x00, 0x00, //0x0000154c ja           LBB0_343
	0x0f, 0xb6, 0xc1, //0x00001552 movzbl       %cl, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00001555 movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x0000155f btq          %rax, %rcx
	0x0f, 0x83, 0x01, 0x06, 0x00, 0x00, //0x00001563 jae          LBB0_343
	//0x00001569 LBB0_271
	0x49, 0x83, 0xfe, 0x20, //0x00001569 cmpq         $32, %r14
	0x0f, 0x82, 0x52, 0x09, 0x00, 0x00, //0x0000156d jb           LBB0_362
	0x49, 0x8d, 0x4e, 0xe0, //0x00001573 leaq         $-32(%r14), %rcx
	0x48, 0x89, 0xc8, //0x00001577 movq         %rcx, %rax
	0x48, 0x83, 0xe0, 0xe0, //0x0000157a andq         $-32, %rax
	0x4d, 0x89, 0xca, //0x0000157e movq         %r9, %r10
	0x4e, 0x8d, 0x7c, 0x08, 0x20, //0x00001581 leaq         $32(%rax,%r9), %r15
	0x83, 0xe1, 0x1f, //0x00001586 andl         $31, %ecx
	0x48, 0x89, 0x4d, 0xc0, //0x00001589 movq         %rcx, $-64(%rbp)
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000158d movq         $-1, %r8
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001594 movq         $-1, %r11
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x0000159b movq         $-1, %r12
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000015a2 .p2align 4, 0x90
	//0x000015b0 LBB0_273
	0xc4, 0xc1, 0x7e, 0x6f, 0x02, //0x000015b0 vmovdqu      (%r10), %ymm0
	0xc4, 0xc1, 0x7d, 0x64, 0xca, //0x000015b5 vpcmpgtb     %ymm10, %ymm0, %ymm1
	0xc5, 0xa5, 0x64, 0xd0, //0x000015ba vpcmpgtb     %ymm0, %ymm11, %ymm2
	0xc5, 0xf5, 0xdb, 0xca, //0x000015be vpand        %ymm2, %ymm1, %ymm1
	0xc5, 0x9d, 0x74, 0xd0, //0x000015c2 vpcmpeqb     %ymm0, %ymm12, %ymm2
	0xc5, 0x95, 0x74, 0xd8, //0x000015c6 vpcmpeqb     %ymm0, %ymm13, %ymm3
	0xc5, 0xe5, 0xeb, 0xd2, //0x000015ca vpor         %ymm2, %ymm3, %ymm2
	0xc5, 0xbd, 0xeb, 0xd8, //0x000015ce vpor         %ymm0, %ymm8, %ymm3
	0xc5, 0x8d, 0x74, 0xc0, //0x000015d2 vpcmpeqb     %ymm0, %ymm14, %ymm0
	0xc5, 0xfd, 0xd7, 0xf8, //0x000015d6 vpmovmskb    %ymm0, %edi
	0xc5, 0x85, 0x74, 0xdb, //0x000015da vpcmpeqb     %ymm3, %ymm15, %ymm3
	0xc5, 0xfd, 0xd7, 0xd3, //0x000015de vpmovmskb    %ymm3, %edx
	0xc5, 0xfd, 0xd7, 0xf2, //0x000015e2 vpmovmskb    %ymm2, %esi
	0xc5, 0xe5, 0xeb, 0xc0, //0x000015e6 vpor         %ymm0, %ymm3, %ymm0
	0xc5, 0xed, 0xeb, 0xc9, //0x000015ea vpor         %ymm1, %ymm2, %ymm1
	0xc5, 0xfd, 0xeb, 0xc1, //0x000015ee vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x000015f2 vpmovmskb    %ymm0, %eax
	0x48, 0xf7, 0xd0, //0x000015f6 notq         %rax
	0x48, 0x0f, 0xbc, 0xc8, //0x000015f9 bsfq         %rax, %rcx
	0x83, 0xf9, 0x20, //0x000015fd cmpl         $32, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x00001600 je           LBB0_275
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00001606 movl         $-1, %eax
	0xd3, 0xe0, //0x0000160b shll         %cl, %eax
	0xf7, 0xd0, //0x0000160d notl         %eax
	0x21, 0xc7, //0x0000160f andl         %eax, %edi
	0x21, 0xc2, //0x00001611 andl         %eax, %edx
	0x21, 0xf0, //0x00001613 andl         %esi, %eax
	0x89, 0xc6, //0x00001615 movl         %eax, %esi
	//0x00001617 LBB0_275
	0x8d, 0x5f, 0xff, //0x00001617 leal         $-1(%rdi), %ebx
	0x21, 0xfb, //0x0000161a andl         %edi, %ebx
	0x0f, 0x85, 0xb0, 0x06, 0x00, 0x00, //0x0000161c jne          LBB0_349
	0x8d, 0x5a, 0xff, //0x00001622 leal         $-1(%rdx), %ebx
	0x21, 0xd3, //0x00001625 andl         %edx, %ebx
	0x0f, 0x85, 0xa5, 0x06, 0x00, 0x00, //0x00001627 jne          LBB0_349
	0x8d, 0x5e, 0xff, //0x0000162d leal         $-1(%rsi), %ebx
	0x21, 0xf3, //0x00001630 andl         %esi, %ebx
	0x0f, 0x85, 0x9a, 0x06, 0x00, 0x00, //0x00001632 jne          LBB0_349
	0x85, 0xff, //0x00001638 testl        %edi, %edi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000163a je           LBB0_281
	0x4c, 0x89, 0xd0, //0x00001640 movq         %r10, %rax
	0x4c, 0x29, 0xc8, //0x00001643 subq         %r9, %rax
	0x0f, 0xbc, 0xff, //0x00001646 bsfl         %edi, %edi
	0x48, 0x01, 0xc7, //0x00001649 addq         %rax, %rdi
	0x49, 0x83, 0xfc, 0xff, //0x0000164c cmpq         $-1, %r12
	0x0f, 0x85, 0xa7, 0x07, 0x00, 0x00, //0x00001650 jne          LBB0_354
	0x49, 0x89, 0xfc, //0x00001656 movq         %rdi, %r12
	//0x00001659 LBB0_281
	0x85, 0xd2, //0x00001659 testl        %edx, %edx
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000165b je           LBB0_284
	0x4c, 0x89, 0xd0, //0x00001661 movq         %r10, %rax
	0x4c, 0x29, 0xc8, //0x00001664 subq         %r9, %rax
	0x0f, 0xbc, 0xd2, //0x00001667 bsfl         %edx, %edx
	0x48, 0x01, 0xc2, //0x0000166a addq         %rax, %rdx
	0x49, 0x83, 0xfb, 0xff, //0x0000166d cmpq         $-1, %r11
	0x0f, 0x85, 0x80, 0x06, 0x00, 0x00, //0x00001671 jne          LBB0_351
	0x49, 0x89, 0xd3, //0x00001677 movq         %rdx, %r11
	//0x0000167a LBB0_284
	0x85, 0xf6, //0x0000167a testl        %esi, %esi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000167c je           LBB0_287
	0x4c, 0x89, 0xd0, //0x00001682 movq         %r10, %rax
	0x4c, 0x29, 0xc8, //0x00001685 subq         %r9, %rax
	0x0f, 0xbc, 0xd6, //0x00001688 bsfl         %esi, %edx
	0x48, 0x01, 0xc2, //0x0000168b addq         %rax, %rdx
	0x49, 0x83, 0xf8, 0xff, //0x0000168e cmpq         $-1, %r8
	0x0f, 0x85, 0x5f, 0x06, 0x00, 0x00, //0x00001692 jne          LBB0_351
	0x49, 0x89, 0xd0, //0x00001698 movq         %rdx, %r8
	//0x0000169b LBB0_287
	0x83, 0xf9, 0x20, //0x0000169b cmpl         $32, %ecx
	0x0f, 0x85, 0x62, 0x02, 0x00, 0x00, //0x0000169e jne          LBB0_496
	0x49, 0x83, 0xc2, 0x20, //0x000016a4 addq         $32, %r10
	0x49, 0x83, 0xc6, 0xe0, //0x000016a8 addq         $-32, %r14
	0x49, 0x83, 0xfe, 0x1f, //0x000016ac cmpq         $31, %r14
	0x0f, 0x87, 0xfa, 0xfe, 0xff, 0xff, //0x000016b0 ja           LBB0_273
	0xc5, 0xf8, 0x77, //0x000016b6 vzeroupper   
	0x4c, 0x8b, 0x75, 0xc0, //0x000016b9 movq         $-64(%rbp), %r14
	//0x000016bd LBB0_290
	0x49, 0x83, 0xfe, 0x10, //0x000016bd cmpq         $16, %r14
	0xc5, 0xfe, 0x6f, 0x2d, 0x37, 0xe9, 0xff, 0xff, //0x000016c1 vmovdqu      $-5833(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x4f, 0xe9, 0xff, 0xff, //0x000016c9 vmovdqu      $-5809(%rip), %ymm6  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x67, 0xe9, 0xff, 0xff, //0x000016d1 vmovdqu      $-5785(%rip), %ymm7  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x7f, 0xe9, 0xff, 0xff, //0x000016d9 vmovdqu      $-5761(%rip), %ymm8  /* LCPI0_3+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000016e1 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x15, 0x92, 0xe9, 0xff, 0xff, //0x000016e6 vmovdqu      $-5742(%rip), %ymm10  /* LCPI0_4+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0xaa, 0xe9, 0xff, 0xff, //0x000016ee vmovdqu      $-5718(%rip), %ymm11  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0xc2, 0xe9, 0xff, 0xff, //0x000016f6 vmovdqu      $-5694(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0xda, 0xe9, 0xff, 0xff, //0x000016fe vmovdqu      $-5670(%rip), %ymm13  /* LCPI0_7+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0xf2, 0xe9, 0xff, 0xff, //0x00001706 vmovdqu      $-5646(%rip), %ymm14  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0x0a, 0xea, 0xff, 0xff, //0x0000170e vmovdqu      $-5622(%rip), %ymm15  /* LCPI0_9+0(%rip) */
	0x0f, 0x82, 0x55, 0x01, 0x00, 0x00, //0x00001716 jb           LBB0_309
	0x4d, 0x8d, 0x56, 0xf0, //0x0000171c leaq         $-16(%r14), %r10
	0x4c, 0x89, 0xd0, //0x00001720 movq         %r10, %rax
	0x48, 0x83, 0xe0, 0xf0, //0x00001723 andq         $-16, %rax
	0x4a, 0x8d, 0x44, 0x38, 0x10, //0x00001727 leaq         $16(%rax,%r15), %rax
	0x48, 0x89, 0x45, 0xc0, //0x0000172c movq         %rax, $-64(%rbp)
	0x41, 0x83, 0xe2, 0x0f, //0x00001730 andl         $15, %r10d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001734 .p2align 4, 0x90
	//0x00001740 LBB0_292
	0xc4, 0xc1, 0x7a, 0x6f, 0x07, //0x00001740 vmovdqu      (%r15), %xmm0
	0xc5, 0xf9, 0x64, 0x0d, 0xf3, 0xe9, 0xff, 0xff, //0x00001745 vpcmpgtb     $-5645(%rip), %xmm0, %xmm1  /* LCPI0_10+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x15, 0xfb, 0xe9, 0xff, 0xff, //0x0000174d vmovdqu      $-5637(%rip), %xmm2  /* LCPI0_11+0(%rip) */
	0xc5, 0xe9, 0x64, 0xd0, //0x00001755 vpcmpgtb     %xmm0, %xmm2, %xmm2
	0xc5, 0xf1, 0xdb, 0xca, //0x00001759 vpand        %xmm2, %xmm1, %xmm1
	0xc5, 0xf9, 0x74, 0x15, 0xfb, 0xe9, 0xff, 0xff, //0x0000175d vpcmpeqb     $-5637(%rip), %xmm0, %xmm2  /* LCPI0_12+0(%rip) */
	0xc5, 0xf9, 0x74, 0x1d, 0x03, 0xea, 0xff, 0xff, //0x00001765 vpcmpeqb     $-5629(%rip), %xmm0, %xmm3  /* LCPI0_13+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xd2, //0x0000176d vpor         %xmm2, %xmm3, %xmm2
	0xc5, 0xf9, 0xeb, 0x1d, 0x07, 0xea, 0xff, 0xff, //0x00001771 vpor         $-5625(%rip), %xmm0, %xmm3  /* LCPI0_14+0(%rip) */
	0xc5, 0xf9, 0x74, 0x05, 0x0f, 0xea, 0xff, 0xff, //0x00001779 vpcmpeqb     $-5617(%rip), %xmm0, %xmm0  /* LCPI0_15+0(%rip) */
	0xc5, 0xe1, 0x74, 0x1d, 0x17, 0xea, 0xff, 0xff, //0x00001781 vpcmpeqb     $-5609(%rip), %xmm3, %xmm3  /* LCPI0_16+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xe0, //0x00001789 vpor         %xmm0, %xmm3, %xmm4
	0xc5, 0xe9, 0xeb, 0xc9, //0x0000178d vpor         %xmm1, %xmm2, %xmm1
	0xc5, 0xd9, 0xeb, 0xc9, //0x00001791 vpor         %xmm1, %xmm4, %xmm1
	0xc5, 0xf9, 0xd7, 0xf8, //0x00001795 vpmovmskb    %xmm0, %edi
	0xc5, 0xf9, 0xd7, 0xf3, //0x00001799 vpmovmskb    %xmm3, %esi
	0xc5, 0xf9, 0xd7, 0xd2, //0x0000179d vpmovmskb    %xmm2, %edx
	0xc5, 0xf9, 0xd7, 0xc1, //0x000017a1 vpmovmskb    %xmm1, %eax
	0xb9, 0xff, 0xff, 0xff, 0xff, //0x000017a5 movl         $4294967295, %ecx
	0x48, 0x31, 0xc8, //0x000017aa xorq         %rcx, %rax
	0x48, 0x0f, 0xbc, 0xc8, //0x000017ad bsfq         %rax, %rcx
	0x83, 0xf9, 0x10, //0x000017b1 cmpl         $16, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x000017b4 je           LBB0_294
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x000017ba movl         $-1, %eax
	0xd3, 0xe0, //0x000017bf shll         %cl, %eax
	0xf7, 0xd0, //0x000017c1 notl         %eax
	0x21, 0xc7, //0x000017c3 andl         %eax, %edi
	0x21, 0xc6, //0x000017c5 andl         %eax, %esi
	0x21, 0xd0, //0x000017c7 andl         %edx, %eax
	0x89, 0xc2, //0x000017c9 movl         %eax, %edx
	//0x000017cb LBB0_294
	0x8d, 0x5f, 0xff, //0x000017cb leal         $-1(%rdi), %ebx
	0x21, 0xfb, //0x000017ce andl         %edi, %ebx
	0x0f, 0x85, 0x0f, 0x06, 0x00, 0x00, //0x000017d0 jne          LBB0_353
	0x8d, 0x5e, 0xff, //0x000017d6 leal         $-1(%rsi), %ebx
	0x21, 0xf3, //0x000017d9 andl         %esi, %ebx
	0x0f, 0x85, 0x04, 0x06, 0x00, 0x00, //0x000017db jne          LBB0_353
	0x8d, 0x5a, 0xff, //0x000017e1 leal         $-1(%rdx), %ebx
	0x21, 0xd3, //0x000017e4 andl         %edx, %ebx
	0x0f, 0x85, 0xf9, 0x05, 0x00, 0x00, //0x000017e6 jne          LBB0_353
	0x85, 0xff, //0x000017ec testl        %edi, %edi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x000017ee je           LBB0_300
	0x4c, 0x89, 0xf8, //0x000017f4 movq         %r15, %rax
	0x4c, 0x29, 0xc8, //0x000017f7 subq         %r9, %rax
	0x0f, 0xbc, 0xff, //0x000017fa bsfl         %edi, %edi
	0x48, 0x01, 0xc7, //0x000017fd addq         %rax, %rdi
	0x49, 0x83, 0xfc, 0xff, //0x00001800 cmpq         $-1, %r12
	0x0f, 0x85, 0xf3, 0x05, 0x00, 0x00, //0x00001804 jne          LBB0_354
	0x49, 0x89, 0xfc, //0x0000180a movq         %rdi, %r12
	//0x0000180d LBB0_300
	0x85, 0xf6, //0x0000180d testl        %esi, %esi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000180f je           LBB0_303
	0x4c, 0x89, 0xf8, //0x00001815 movq         %r15, %rax
	0x4c, 0x29, 0xc8, //0x00001818 subq         %r9, %rax
	0x0f, 0xbc, 0xf6, //0x0000181b bsfl         %esi, %esi
	0x48, 0x01, 0xc6, //0x0000181e addq         %rax, %rsi
	0x49, 0x83, 0xfb, 0xff, //0x00001821 cmpq         $-1, %r11
	0x0f, 0x85, 0x4e, 0x06, 0x00, 0x00, //0x00001825 jne          LBB0_359
	0x49, 0x89, 0xf3, //0x0000182b movq         %rsi, %r11
	//0x0000182e LBB0_303
	0x85, 0xd2, //0x0000182e testl        %edx, %edx
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x00001830 je           LBB0_306
	0x4c, 0x89, 0xf8, //0x00001836 movq         %r15, %rax
	0x4c, 0x29, 0xc8, //0x00001839 subq         %r9, %rax
	0x0f, 0xbc, 0xd2, //0x0000183c bsfl         %edx, %edx
	0x48, 0x01, 0xc2, //0x0000183f addq         %rax, %rdx
	0x49, 0x83, 0xf8, 0xff, //0x00001842 cmpq         $-1, %r8
	0x0f, 0x85, 0xab, 0x04, 0x00, 0x00, //0x00001846 jne          LBB0_351
	0x49, 0x89, 0xd0, //0x0000184c movq         %rdx, %r8
	//0x0000184f LBB0_306
	0x83, 0xf9, 0x10, //0x0000184f cmpl         $16, %ecx
	0x0f, 0x85, 0x05, 0x01, 0x00, 0x00, //0x00001852 jne          LBB0_325
	0x49, 0x83, 0xc7, 0x10, //0x00001858 addq         $16, %r15
	0x49, 0x83, 0xc6, 0xf0, //0x0000185c addq         $-16, %r14
	0x49, 0x83, 0xfe, 0x0f, //0x00001860 cmpq         $15, %r14
	0x0f, 0x87, 0xd6, 0xfe, 0xff, 0xff, //0x00001864 ja           LBB0_292
	0x4d, 0x89, 0xd6, //0x0000186a movq         %r10, %r14
	0x4c, 0x8b, 0x7d, 0xc0, //0x0000186d movq         $-64(%rbp), %r15
	//0x00001871 LBB0_309
	0x4d, 0x85, 0xf6, //0x00001871 testq        %r14, %r14
	0x4c, 0x8b, 0x55, 0xd0, //0x00001874 movq         $-48(%rbp), %r10
	0x0f, 0x84, 0xe6, 0x00, 0x00, 0x00, //0x00001878 je           LBB0_326
	0x4b, 0x8d, 0x0c, 0x37, //0x0000187e leaq         (%r15,%r14), %rcx
	0xe9, 0x25, 0x00, 0x00, 0x00, //0x00001882 jmp          LBB0_314
	//0x00001887 LBB0_311
	0x49, 0x89, 0xd7, //0x00001887 movq         %rdx, %r15
	0x4d, 0x29, 0xcf, //0x0000188a subq         %r9, %r15
	0x49, 0x83, 0xf8, 0xff, //0x0000188d cmpq         $-1, %r8
	0x0f, 0x85, 0x26, 0x06, 0x00, 0x00, //0x00001891 jne          LBB0_365
	0x49, 0xff, 0xcf, //0x00001897 decq         %r15
	0x4d, 0x89, 0xf8, //0x0000189a movq         %r15, %r8
	0x90, 0x90, 0x90, //0x0000189d .p2align 4, 0x90
	//0x000018a0 LBB0_313
	0x49, 0x89, 0xd7, //0x000018a0 movq         %rdx, %r15
	0x49, 0xff, 0xce, //0x000018a3 decq         %r14
	0x0f, 0x84, 0xbc, 0x05, 0x00, 0x00, //0x000018a6 je           LBB0_358
	//0x000018ac LBB0_314
	0x41, 0x0f, 0xbe, 0x37, //0x000018ac movsbl       (%r15), %esi
	0x83, 0xc6, 0xd5, //0x000018b0 addl         $-43, %esi
	0x83, 0xfe, 0x3a, //0x000018b3 cmpl         $58, %esi
	0x0f, 0x87, 0xa8, 0x00, 0x00, 0x00, //0x000018b6 ja           LBB0_326
	0x49, 0x8d, 0x57, 0x01, //0x000018bc leaq         $1(%r15), %rdx
	0x48, 0x8d, 0x3d, 0x49, 0x13, 0x00, 0x00, //0x000018c0 leaq         $4937(%rip), %rdi  /* LJTI0_2+0(%rip) */
	0x48, 0x63, 0x04, 0xb7, //0x000018c7 movslq       (%rdi,%rsi,4), %rax
	0x48, 0x01, 0xf8, //0x000018cb addq         %rdi, %rax
	0xff, 0xe0, //0x000018ce jmpq         *%rax
	//0x000018d0 LBB0_316
	0x49, 0x89, 0xd7, //0x000018d0 movq         %rdx, %r15
	0x4d, 0x29, 0xcf, //0x000018d3 subq         %r9, %r15
	0x49, 0x83, 0xfb, 0xff, //0x000018d6 cmpq         $-1, %r11
	0x0f, 0x85, 0xdd, 0x05, 0x00, 0x00, //0x000018da jne          LBB0_365
	0x49, 0xff, 0xcf, //0x000018e0 decq         %r15
	0x4d, 0x89, 0xfb, //0x000018e3 movq         %r15, %r11
	0xe9, 0xb5, 0xff, 0xff, 0xff, //0x000018e6 jmp          LBB0_313
	//0x000018eb LBB0_318
	0x49, 0x89, 0xd7, //0x000018eb movq         %rdx, %r15
	0x4d, 0x29, 0xcf, //0x000018ee subq         %r9, %r15
	0x49, 0x83, 0xfc, 0xff, //0x000018f1 cmpq         $-1, %r12
	0x0f, 0x85, 0xc2, 0x05, 0x00, 0x00, //0x000018f5 jne          LBB0_365
	0x49, 0xff, 0xcf, //0x000018fb decq         %r15
	0x4d, 0x89, 0xfc, //0x000018fe movq         %r15, %r12
	0xe9, 0x9a, 0xff, 0xff, 0xff, //0x00001901 jmp          LBB0_313
	//0x00001906 LBB0_496
	0x49, 0x01, 0xca, //0x00001906 addq         %rcx, %r10
	0xc5, 0xf8, 0x77, //0x00001909 vzeroupper   
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000190c vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x3d, 0x27, 0xe7, 0xff, 0xff, //0x00001911 vmovdqu      $-6361(%rip), %ymm7  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0xff, 0xe6, 0xff, 0xff, //0x00001919 vmovdqu      $-6401(%rip), %ymm6  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x2d, 0xd7, 0xe6, 0xff, 0xff, //0x00001921 vmovdqu      $-6441(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0x4d, 0x89, 0xd7, //0x00001929 movq         %r10, %r15
	0x4c, 0x8b, 0x55, 0xd0, //0x0000192c movq         $-48(%rbp), %r10
	0x4d, 0x85, 0xdb, //0x00001930 testq        %r11, %r11
	0x0f, 0x85, 0x34, 0x00, 0x00, 0x00, //0x00001933 jne          LBB0_327
	0xe9, 0x10, 0x10, 0x00, 0x00, //0x00001939 jmp          LBB0_497
	//0x0000193e LBB0_320
	0xba, 0x40, 0x00, 0x00, 0x00, //0x0000193e movl         $64, %edx
	//0x00001943 LBB0_321
	0x4c, 0x8b, 0x75, 0xc8, //0x00001943 movq         $-56(%rbp), %r14
	0x49, 0x29, 0xc4, //0x00001947 subq         %rax, %r12
	0x48, 0x39, 0xca, //0x0000194a cmpq         %rcx, %rdx
	0x0f, 0x82, 0x4d, 0x10, 0x00, 0x00, //0x0000194d jb           LBB0_129
	//0x00001953 LBB0_322
	0x4d, 0x8d, 0x64, 0x0c, 0x01, //0x00001953 leaq         $1(%r12,%rcx), %r12
	0xe9, 0xfd, 0x03, 0x00, 0x00, //0x00001958 jmp          LBB0_323
	//0x0000195d LBB0_325
	0x49, 0x01, 0xcf, //0x0000195d addq         %rcx, %r15
	0x4c, 0x8b, 0x55, 0xd0, //0x00001960 movq         $-48(%rbp), %r10
	//0x00001964 LBB0_326
	0x4d, 0x85, 0xdb, //0x00001964 testq        %r11, %r11
	0x0f, 0x84, 0xe1, 0x0f, 0x00, 0x00, //0x00001967 je           LBB0_497
	//0x0000196d LBB0_327
	0x4d, 0x85, 0xc0, //0x0000196d testq        %r8, %r8
	0x0f, 0x84, 0xd8, 0x0f, 0x00, 0x00, //0x00001970 je           LBB0_497
	0x4d, 0x85, 0xe4, //0x00001976 testq        %r12, %r12
	0x0f, 0x84, 0xcf, 0x0f, 0x00, 0x00, //0x00001979 je           LBB0_497
	0x4d, 0x29, 0xcf, //0x0000197f subq         %r9, %r15
	0x49, 0x8d, 0x4f, 0xff, //0x00001982 leaq         $-1(%r15), %rcx
	0x49, 0x39, 0xcb, //0x00001986 cmpq         %rcx, %r11
	0x0f, 0x84, 0x6b, 0x00, 0x00, 0x00, //0x00001989 je           LBB0_335
	0x49, 0x39, 0xcc, //0x0000198f cmpq         %rcx, %r12
	0x0f, 0x84, 0x62, 0x00, 0x00, 0x00, //0x00001992 je           LBB0_335
	0x49, 0x39, 0xc8, //0x00001998 cmpq         %rcx, %r8
	0x0f, 0x84, 0x59, 0x00, 0x00, 0x00, //0x0000199b je           LBB0_335
	0x4d, 0x85, 0xc0, //0x000019a1 testq        %r8, %r8
	0xc5, 0x7e, 0x6f, 0x05, 0xb4, 0xe6, 0xff, 0xff, //0x000019a4 vmovdqu      $-6476(%rip), %ymm8  /* LCPI0_3+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0xcc, 0xe6, 0xff, 0xff, //0x000019ac vmovdqu      $-6452(%rip), %ymm10  /* LCPI0_4+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0xe4, 0xe6, 0xff, 0xff, //0x000019b4 vmovdqu      $-6428(%rip), %ymm11  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0xfc, 0xe6, 0xff, 0xff, //0x000019bc vmovdqu      $-6404(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x14, 0xe7, 0xff, 0xff, //0x000019c4 vmovdqu      $-6380(%rip), %ymm13  /* LCPI0_7+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x2c, 0xe7, 0xff, 0xff, //0x000019cc vmovdqu      $-6356(%rip), %ymm14  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0x44, 0xe7, 0xff, 0xff, //0x000019d4 vmovdqu      $-6332(%rip), %ymm15  /* LCPI0_9+0(%rip) */
	0x0f, 0x8e, 0x58, 0x00, 0x00, 0x00, //0x000019dc jle          LBB0_336
	0x49, 0x8d, 0x40, 0xff, //0x000019e2 leaq         $-1(%r8), %rax
	0x49, 0x39, 0xc3, //0x000019e6 cmpq         %rax, %r11
	0x0f, 0x84, 0x4b, 0x00, 0x00, 0x00, //0x000019e9 je           LBB0_336
	0x49, 0xf7, 0xd0, //0x000019ef notq         %r8
	0x4d, 0x89, 0xc7, //0x000019f2 movq         %r8, %r15
	0xe9, 0x64, 0x01, 0x00, 0x00, //0x000019f5 jmp          LBB0_342
	//0x000019fa LBB0_335
	0x49, 0xf7, 0xdf, //0x000019fa negq         %r15
	0xc5, 0x7e, 0x6f, 0x05, 0x5b, 0xe6, 0xff, 0xff, //0x000019fd vmovdqu      $-6565(%rip), %ymm8  /* LCPI0_3+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0x73, 0xe6, 0xff, 0xff, //0x00001a05 vmovdqu      $-6541(%rip), %ymm10  /* LCPI0_4+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x8b, 0xe6, 0xff, 0xff, //0x00001a0d vmovdqu      $-6517(%rip), %ymm11  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0xa3, 0xe6, 0xff, 0xff, //0x00001a15 vmovdqu      $-6493(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0xbb, 0xe6, 0xff, 0xff, //0x00001a1d vmovdqu      $-6469(%rip), %ymm13  /* LCPI0_7+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0xd3, 0xe6, 0xff, 0xff, //0x00001a25 vmovdqu      $-6445(%rip), %ymm14  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0xeb, 0xe6, 0xff, 0xff, //0x00001a2d vmovdqu      $-6421(%rip), %ymm15  /* LCPI0_9+0(%rip) */
	0xe9, 0x24, 0x01, 0x00, 0x00, //0x00001a35 jmp          LBB0_342
	//0x00001a3a LBB0_336
	0x4c, 0x89, 0xe1, //0x00001a3a movq         %r12, %rcx
	0x4c, 0x09, 0xd9, //0x00001a3d orq          %r11, %rcx
	0x4d, 0x39, 0xdc, //0x00001a40 cmpq         %r11, %r12
	0x0f, 0x8c, 0x00, 0x01, 0x00, 0x00, //0x00001a43 jl           LBB0_341
	0x48, 0x85, 0xc9, //0x00001a49 testq        %rcx, %rcx
	0x0f, 0x88, 0xf7, 0x00, 0x00, 0x00, //0x00001a4c js           LBB0_341
	0x49, 0xf7, 0xd4, //0x00001a52 notq         %r12
	0x4d, 0x89, 0xe7, //0x00001a55 movq         %r12, %r15
	0xe9, 0x01, 0x01, 0x00, 0x00, //0x00001a58 jmp          LBB0_342
	//0x00001a5d LBB0_339
	0x4d, 0x29, 0xfb, //0x00001a5d subq         %r15, %r11
	0x44, 0x0f, 0xbc, 0xf3, //0x00001a60 bsfl         %ebx, %r14d
	0xe9, 0x3d, 0x01, 0x00, 0x00, //0x00001a64 jmp          LBB0_346
	//0x00001a69 LBB0_54
	0x4c, 0x89, 0xf9, //0x00001a69 movq         %r15, %rcx
	0x4c, 0x89, 0xcb, //0x00001a6c movq         %r9, %rbx
	0x48, 0x83, 0xf9, 0x20, //0x00001a6f cmpq         $32, %rcx
	0x0f, 0x82, 0x0e, 0x05, 0x00, 0x00, //0x00001a73 jb           LBB0_370
	//0x00001a79 LBB0_55
	0xc5, 0xfe, 0x6f, 0x03, //0x00001a79 vmovdqu      (%rbx), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001a7d vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xd1, //0x00001a81 vpmovmskb    %ymm1, %edx
	0xc5, 0xfd, 0x74, 0xc7, //0x00001a85 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00001a89 vpmovmskb    %ymm0, %esi
	0x85, 0xf6, //0x00001a8d testl        %esi, %esi
	0x0f, 0x85, 0x89, 0x04, 0x00, 0x00, //0x00001a8f jne          LBB0_366
	0x4d, 0x85, 0xf6, //0x00001a95 testq        %r14, %r14
	0x0f, 0x85, 0x97, 0x04, 0x00, 0x00, //0x00001a98 jne          LBB0_368
	0x45, 0x31, 0xf6, //0x00001a9e xorl         %r14d, %r14d
	0x48, 0x85, 0xd2, //0x00001aa1 testq        %rdx, %rdx
	0x0f, 0x84, 0xd5, 0x04, 0x00, 0x00, //0x00001aa4 je           LBB0_369
	//0x00001aaa LBB0_60
	0x48, 0x0f, 0xbc, 0xc2, //0x00001aaa bsfq         %rdx, %rax
	0x4c, 0x29, 0xdb, //0x00001aae subq         %r11, %rbx
	0x4c, 0x8d, 0x64, 0x03, 0x01, //0x00001ab1 leaq         $1(%rbx,%rax), %r12
	0x4c, 0x8b, 0x75, 0xc8, //0x00001ab6 movq         $-56(%rbp), %r14
	//0x00001aba LBB0_184
	0x4d, 0x85, 0xe4, //0x00001aba testq        %r12, %r12
	0x0f, 0x88, 0xbb, 0x0c, 0x00, 0x00, //0x00001abd js           LBB0_475
	0x4d, 0x89, 0x22, //0x00001ac3 movq         %r12, (%r10)
	0x4c, 0x89, 0xe9, //0x00001ac6 movq         %r13, %rcx
	0x48, 0x83, 0x7d, 0xa8, 0x00, //0x00001ac9 cmpq         $0, $-88(%rbp)
	0x0f, 0x8f, 0x9c, 0xe7, 0xff, 0xff, //0x00001ace jg           LBB0_3
	0xe9, 0x00, 0x0d, 0x00, 0x00, //0x00001ad4 jmp          LBB0_501
	//0x00001ad9 LBB0_84
	0x4d, 0x89, 0xf2, //0x00001ad9 movq         %r14, %r10
	0x49, 0x89, 0xc4, //0x00001adc movq         %rax, %r12
	0x4c, 0x8b, 0x75, 0xc8, //0x00001adf movq         $-56(%rbp), %r14
	0x49, 0x83, 0xfa, 0x20, //0x00001ae3 cmpq         $32, %r10
	0x0f, 0x82, 0xd2, 0x05, 0x00, 0x00, //0x00001ae7 jb           LBB0_388
	//0x00001aed LBB0_85
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x00001aed vmovdqu      (%r12), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001af3 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x00001af7 vpmovmskb    %ymm1, %esi
	0xc5, 0xfd, 0x74, 0xcf, //0x00001afb vpcmpeqb     %ymm7, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xc9, //0x00001aff vpmovmskb    %ymm1, %ecx
	0xc5, 0xbd, 0x64, 0xc8, //0x00001b03 vpcmpgtb     %ymm0, %ymm8, %ymm1
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x00001b07 vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xfd, 0xdb, 0xc1, //0x00001b0c vpand        %ymm1, %ymm0, %ymm0
	0xc5, 0x7d, 0xd7, 0xc8, //0x00001b10 vpmovmskb    %ymm0, %r9d
	0x85, 0xc9, //0x00001b14 testl        %ecx, %ecx
	0x0f, 0x85, 0xfa, 0x04, 0x00, 0x00, //0x00001b16 jne          LBB0_379
	0x4d, 0x85, 0xff, //0x00001b1c testq        %r15, %r15
	0x0f, 0x85, 0x09, 0x05, 0x00, 0x00, //0x00001b1f jne          LBB0_381
	0x45, 0x31, 0xff, //0x00001b25 xorl         %r15d, %r15d
	0x48, 0x85, 0xf6, //0x00001b28 testq        %rsi, %rsi
	0x0f, 0x84, 0x44, 0x05, 0x00, 0x00, //0x00001b2b je           LBB0_382
	//0x00001b31 LBB0_88
	0x48, 0x0f, 0xbc, 0xce, //0x00001b31 bsfq         %rsi, %rcx
	0xe9, 0x40, 0x05, 0x00, 0x00, //0x00001b35 jmp          LBB0_383
	//0x00001b3a LBB0_340
	0x48, 0xf7, 0xd2, //0x00001b3a notq         %rdx
	0x49, 0x89, 0xd6, //0x00001b3d movq         %rdx, %r14
	0x48, 0x8b, 0x55, 0xd0, //0x00001b40 movq         $-48(%rbp), %rdx
	0xe9, 0x7a, 0xf5, 0xff, 0xff, //0x00001b44 jmp          LBB0_197
	//0x00001b49 LBB0_341
	0x48, 0x85, 0xc9, //0x00001b49 testq        %rcx, %rcx
	0x49, 0x8d, 0x43, 0xff, //0x00001b4c leaq         $-1(%r11), %rax
	0x49, 0xf7, 0xd3, //0x00001b50 notq         %r11
	0x4d, 0x0f, 0x48, 0xdf, //0x00001b53 cmovsq       %r15, %r11
	0x49, 0x39, 0xc4, //0x00001b57 cmpq         %rax, %r12
	0x4d, 0x0f, 0x44, 0xfb, //0x00001b5a cmoveq       %r11, %r15
	//0x00001b5e LBB0_342
	0x4d, 0x8b, 0x22, //0x00001b5e movq         (%r10), %r12
	0x4d, 0x85, 0xff, //0x00001b61 testq        %r15, %r15
	0x0f, 0x88, 0xee, 0x0d, 0x00, 0x00, //0x00001b64 js           LBB0_499
	//0x00001b6a LBB0_343
	0x4d, 0x01, 0xfc, //0x00001b6a addq         %r15, %r12
	0x4d, 0x89, 0x22, //0x00001b6d movq         %r12, (%r10)
	0x4c, 0x89, 0xe9, //0x00001b70 movq         %r13, %rcx
	0x48, 0x83, 0x7d, 0xa8, 0x00, //0x00001b73 cmpq         $0, $-88(%rbp)
	0x4c, 0x8b, 0x75, 0xc8, //0x00001b78 movq         $-56(%rbp), %r14
	0x0f, 0x8f, 0xee, 0xe6, 0xff, 0xff, //0x00001b7c jg           LBB0_3
	0xe9, 0x52, 0x0c, 0x00, 0x00, //0x00001b82 jmp          LBB0_501
	//0x00001b87 LBB0_344
	0x4d, 0x29, 0xfe, //0x00001b87 subq         %r15, %r14
	0x0f, 0xbc, 0xc3, //0x00001b8a bsfl         %ebx, %eax
	0x4c, 0x01, 0xf0, //0x00001b8d addq         %r14, %rax
	0x48, 0xf7, 0xd0, //0x00001b90 notq         %rax
	0x49, 0x89, 0xc6, //0x00001b93 movq         %rax, %r14
	0x48, 0x8b, 0x55, 0xd0, //0x00001b96 movq         $-48(%rbp), %rdx
	0xe9, 0x24, 0xf5, 0xff, 0xff, //0x00001b9a jmp          LBB0_197
	//0x00001b9f LBB0_345
	0x4d, 0x29, 0xfb, //0x00001b9f subq         %r15, %r11
	0x45, 0x0f, 0xbc, 0xf1, //0x00001ba2 bsfl         %r9d, %r14d
	//0x00001ba6 LBB0_346
	0x4d, 0x01, 0xde, //0x00001ba6 addq         %r11, %r14
	0x49, 0xf7, 0xd6, //0x00001ba9 notq         %r14
	0x48, 0x8b, 0x55, 0xd0, //0x00001bac movq         $-48(%rbp), %rdx
	0xe9, 0x0e, 0xf5, 0xff, 0xff, //0x00001bb0 jmp          LBB0_197
	//0x00001bb5 LBB0_347
	0x48, 0xf7, 0xd7, //0x00001bb5 notq         %rdi
	0x49, 0x89, 0xfe, //0x00001bb8 movq         %rdi, %r14
	0x48, 0x8b, 0x55, 0xd0, //0x00001bbb movq         $-48(%rbp), %rdx
	0xe9, 0xff, 0xf4, 0xff, 0xff, //0x00001bbf jmp          LBB0_197
	//0x00001bc4 LBB0_101
	0x4c, 0x89, 0xf9, //0x00001bc4 movq         %r15, %rcx
	0x4c, 0x89, 0xc3, //0x00001bc7 movq         %r8, %rbx
	0x48, 0x83, 0xf9, 0x20, //0x00001bca cmpq         $32, %rcx
	0x0f, 0x82, 0x02, 0x06, 0x00, 0x00, //0x00001bce jb           LBB0_402
	//0x00001bd4 LBB0_102
	0xc5, 0xfe, 0x6f, 0x03, //0x00001bd4 vmovdqu      (%rbx), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001bd8 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xd1, //0x00001bdc vpmovmskb    %ymm1, %edx
	0xc5, 0xfd, 0x74, 0xc7, //0x00001be0 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00001be4 vpmovmskb    %ymm0, %esi
	0x85, 0xf6, //0x00001be8 testl        %esi, %esi
	0x0f, 0x85, 0x6f, 0x05, 0x00, 0x00, //0x00001bea jne          LBB0_397
	0x4d, 0x85, 0xf6, //0x00001bf0 testq        %r14, %r14
	0x0f, 0x85, 0x85, 0x05, 0x00, 0x00, //0x00001bf3 jne          LBB0_399
	0x45, 0x31, 0xf6, //0x00001bf9 xorl         %r14d, %r14d
	0x48, 0x85, 0xd2, //0x00001bfc testq        %rdx, %rdx
	0x0f, 0x84, 0xc9, 0x05, 0x00, 0x00, //0x00001bff je           LBB0_401
	//0x00001c05 LBB0_107
	0x4d, 0x89, 0xe0, //0x00001c05 movq         %r12, %r8
	0x48, 0x0f, 0xbc, 0xc2, //0x00001c08 bsfq         %rdx, %rax
	0x4c, 0x29, 0xcb, //0x00001c0c subq         %r9, %rbx
	0x4c, 0x8d, 0x64, 0x03, 0x01, //0x00001c0f leaq         $1(%rbx,%rax), %r12
	0x4c, 0x8b, 0x75, 0xc8, //0x00001c14 movq         $-56(%rbp), %r14
	//0x00001c18 LBB0_205
	0x4d, 0x85, 0xe4, //0x00001c18 testq        %r12, %r12
	0x0f, 0x88, 0x87, 0x0b, 0x00, 0x00, //0x00001c1b js           LBB0_478
	0x4d, 0x89, 0x22, //0x00001c21 movq         %r12, (%r10)
	0x4c, 0x89, 0xe9, //0x00001c24 movq         %r13, %rcx
	0x4d, 0x85, 0xc0, //0x00001c27 testq        %r8, %r8
	0x0f, 0x8e, 0xa9, 0x0b, 0x00, 0x00, //0x00001c2a jle          LBB0_501
	0x49, 0x8b, 0x06, //0x00001c30 movq         (%r14), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00001c33 cmpq         $4095, %rax
	0x0f, 0x8f, 0x33, 0x0b, 0x00, 0x00, //0x00001c39 jg           LBB0_489
	0x48, 0x8d, 0x48, 0x01, //0x00001c3f leaq         $1(%rax), %rcx
	0x49, 0x89, 0x0e, //0x00001c43 movq         %rcx, (%r14)
	0x49, 0xc7, 0x44, 0xc6, 0x08, 0x04, 0x00, 0x00, 0x00, //0x00001c46 movq         $4, $8(%r14,%rax,8)
	0xe9, 0x1c, 0xe6, 0xff, 0xff, //0x00001c4f jmp          LBB0_3
	//0x00001c54 LBB0_120
	0x4c, 0x89, 0xf3, //0x00001c54 movq         %r14, %rbx
	0x4c, 0x8b, 0x65, 0xa0, //0x00001c57 movq         $-96(%rbp), %r12
	0x4c, 0x8b, 0x75, 0xc8, //0x00001c5b movq         $-56(%rbp), %r14
	0x48, 0x83, 0xfb, 0x20, //0x00001c5f cmpq         $32, %rbx
	0x0f, 0x82, 0xa3, 0x06, 0x00, 0x00, //0x00001c63 jb           LBB0_420
	//0x00001c69 LBB0_121
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x00001c69 vmovdqu      (%r12), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001c6f vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x00001c73 vpmovmskb    %ymm1, %esi
	0xc5, 0xfd, 0x74, 0xcf, //0x00001c77 vpcmpeqb     %ymm7, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xc9, //0x00001c7b vpmovmskb    %ymm1, %ecx
	0xc5, 0xbd, 0x64, 0xc8, //0x00001c7f vpcmpgtb     %ymm0, %ymm8, %ymm1
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x00001c83 vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xfd, 0xdb, 0xc1, //0x00001c88 vpand        %ymm1, %ymm0, %ymm0
	0xc5, 0x7d, 0xd7, 0xc8, //0x00001c8c vpmovmskb    %ymm0, %r9d
	0x85, 0xc9, //0x00001c90 testl        %ecx, %ecx
	0x0f, 0x85, 0xd0, 0x05, 0x00, 0x00, //0x00001c92 jne          LBB0_411
	0x4d, 0x85, 0xff, //0x00001c98 testq        %r15, %r15
	0x0f, 0x85, 0xdf, 0x05, 0x00, 0x00, //0x00001c9b jne          LBB0_413
	0x45, 0x31, 0xff, //0x00001ca1 xorl         %r15d, %r15d
	0x48, 0x85, 0xf6, //0x00001ca4 testq        %rsi, %rsi
	0x0f, 0x84, 0x1e, 0x06, 0x00, 0x00, //0x00001ca7 je           LBB0_414
	//0x00001cad LBB0_124
	0x48, 0x0f, 0xbc, 0xce, //0x00001cad bsfq         %rsi, %rcx
	0xe9, 0x1a, 0x06, 0x00, 0x00, //0x00001cb1 jmp          LBB0_415
	//0x00001cb6 LBB0_348
	0x49, 0x89, 0xce, //0x00001cb6 movq         %rcx, %r14
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001cb9 movq         $-1, %rcx
	0x4d, 0x85, 0xe4, //0x00001cc0 testq        %r12, %r12
	0x48, 0x8b, 0x55, 0xd0, //0x00001cc3 movq         $-48(%rbp), %rdx
	0x0f, 0x85, 0xf4, 0xf2, 0xff, 0xff, //0x00001cc7 jne          LBB0_188
	0xe9, 0xf3, 0x0a, 0x00, 0x00, //0x00001ccd jmp          LBB0_481
	//0x00001cd2 LBB0_349
	0x4d, 0x29, 0xca, //0x00001cd2 subq         %r9, %r10
	0x44, 0x0f, 0xbc, 0xfb, //0x00001cd5 bsfl         %ebx, %r15d
	0x4d, 0x01, 0xd7, //0x00001cd9 addq         %r10, %r15
	0x49, 0xf7, 0xd7, //0x00001cdc notq         %r15
	0x4c, 0x8b, 0x55, 0xd0, //0x00001cdf movq         $-48(%rbp), %r10
	0xe9, 0x76, 0xfe, 0xff, 0xff, //0x00001ce3 jmp          LBB0_342
	//0x00001ce8 LBB0_350
	0x48, 0xf7, 0xd6, //0x00001ce8 notq         %rsi
	0x49, 0x89, 0xf6, //0x00001ceb movq         %rsi, %r14
	0x48, 0x8b, 0x55, 0xd0, //0x00001cee movq         $-48(%rbp), %rdx
	0xe9, 0xcc, 0xf3, 0xff, 0xff, //0x00001cf2 jmp          LBB0_197
	//0x00001cf7 LBB0_351
	0x48, 0xf7, 0xd2, //0x00001cf7 notq         %rdx
	0x49, 0x89, 0xd7, //0x00001cfa movq         %rdx, %r15
	0x4c, 0x8b, 0x55, 0xd0, //0x00001cfd movq         $-48(%rbp), %r10
	0xe9, 0x58, 0xfe, 0xff, 0xff, //0x00001d01 jmp          LBB0_342
	//0x00001d06 LBB0_220
	0x4c, 0x89, 0xf9, //0x00001d06 movq         %r15, %rcx
	0x4c, 0x89, 0xc3, //0x00001d09 movq         %r8, %rbx
	0x48, 0x83, 0xf9, 0x20, //0x00001d0c cmpq         $32, %rcx
	0x0f, 0x82, 0xfd, 0x07, 0x00, 0x00, //0x00001d10 jb           LBB0_443
	//0x00001d16 LBB0_221
	0xc5, 0xfe, 0x6f, 0x03, //0x00001d16 vmovdqu      (%rbx), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001d1a vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xd1, //0x00001d1e vpmovmskb    %ymm1, %edx
	0xc5, 0xfd, 0x74, 0xc7, //0x00001d22 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00001d26 vpmovmskb    %ymm0, %esi
	0x85, 0xf6, //0x00001d2a testl        %esi, %esi
	0x0f, 0x85, 0x6a, 0x07, 0x00, 0x00, //0x00001d2c jne          LBB0_438
	0x4d, 0x85, 0xf6, //0x00001d32 testq        %r14, %r14
	0x0f, 0x85, 0x80, 0x07, 0x00, 0x00, //0x00001d35 jne          LBB0_440
	0x45, 0x31, 0xf6, //0x00001d3b xorl         %r14d, %r14d
	0x48, 0x85, 0xd2, //0x00001d3e testq        %rdx, %rdx
	0x0f, 0x84, 0xc4, 0x07, 0x00, 0x00, //0x00001d41 je           LBB0_442
	//0x00001d47 LBB0_226
	0x4d, 0x89, 0xe0, //0x00001d47 movq         %r12, %r8
	0x48, 0x0f, 0xbc, 0xc2, //0x00001d4a bsfq         %rdx, %rax
	0x4c, 0x29, 0xcb, //0x00001d4e subq         %r9, %rbx
	0x4c, 0x8d, 0x64, 0x03, 0x01, //0x00001d51 leaq         $1(%rbx,%rax), %r12
	0x4c, 0x8b, 0x75, 0xc8, //0x00001d56 movq         $-56(%rbp), %r14
	//0x00001d5a LBB0_323
	0x4d, 0x85, 0xe4, //0x00001d5a testq        %r12, %r12
	0x0f, 0x88, 0x45, 0x0a, 0x00, 0x00, //0x00001d5d js           LBB0_478
	0x4d, 0x89, 0x22, //0x00001d63 movq         %r12, (%r10)
	0x4c, 0x89, 0xe9, //0x00001d66 movq         %r13, %rcx
	0x4d, 0x85, 0xc0, //0x00001d69 testq        %r8, %r8
	0x0f, 0x8f, 0xfe, 0xe4, 0xff, 0xff, //0x00001d6c jg           LBB0_3
	0xe9, 0x62, 0x0a, 0x00, 0x00, //0x00001d72 jmp          LBB0_501
	//0x00001d77 LBB0_352
	0x49, 0xf7, 0xde, //0x00001d77 negq         %r14
	0x48, 0x8b, 0x55, 0xd0, //0x00001d7a movq         $-48(%rbp), %rdx
	0xe9, 0x40, 0xf3, 0xff, 0xff, //0x00001d7e jmp          LBB0_197
	//0x00001d83 LBB0_259
	0x4c, 0x89, 0xf3, //0x00001d83 movq         %r14, %rbx
	0x4c, 0x8b, 0x65, 0xa0, //0x00001d86 movq         $-96(%rbp), %r12
	0x48, 0x83, 0xfb, 0x20, //0x00001d8a cmpq         $32, %rbx
	0x4c, 0x8b, 0x75, 0xc8, //0x00001d8e movq         $-56(%rbp), %r14
	0x0f, 0x82, 0xb1, 0x08, 0x00, 0x00, //0x00001d92 jb           LBB0_460
	//0x00001d98 LBB0_260
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x00001d98 vmovdqu      (%r12), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001d9e vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x00001da2 vpmovmskb    %ymm1, %esi
	0xc5, 0xfd, 0x74, 0xcf, //0x00001da6 vpcmpeqb     %ymm7, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xc9, //0x00001daa vpmovmskb    %ymm1, %ecx
	0xc5, 0xbd, 0x64, 0xc8, //0x00001dae vpcmpgtb     %ymm0, %ymm8, %ymm1
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x00001db2 vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xfd, 0xdb, 0xc1, //0x00001db7 vpand        %ymm1, %ymm0, %ymm0
	0xc5, 0x7d, 0xd7, 0xc8, //0x00001dbb vpmovmskb    %ymm0, %r9d
	0x85, 0xc9, //0x00001dbf testl        %ecx, %ecx
	0x0f, 0x85, 0xde, 0x07, 0x00, 0x00, //0x00001dc1 jne          LBB0_452
	0x4d, 0x85, 0xff, //0x00001dc7 testq        %r15, %r15
	0x0f, 0x85, 0xed, 0x07, 0x00, 0x00, //0x00001dca jne          LBB0_454
	0x45, 0x31, 0xff, //0x00001dd0 xorl         %r15d, %r15d
	0x48, 0x85, 0xf6, //0x00001dd3 testq        %rsi, %rsi
	0x0f, 0x84, 0x2c, 0x08, 0x00, 0x00, //0x00001dd6 je           LBB0_455
	//0x00001ddc LBB0_263
	0x48, 0x0f, 0xbc, 0xce, //0x00001ddc bsfq         %rsi, %rcx
	0xe9, 0x28, 0x08, 0x00, 0x00, //0x00001de0 jmp          LBB0_456
	//0x00001de5 LBB0_353
	0x4d, 0x29, 0xcf, //0x00001de5 subq         %r9, %r15
	0x0f, 0xbc, 0xc3, //0x00001de8 bsfl         %ebx, %eax
	0x4c, 0x01, 0xf8, //0x00001deb addq         %r15, %rax
	0x48, 0xf7, 0xd0, //0x00001dee notq         %rax
	0x49, 0x89, 0xc7, //0x00001df1 movq         %rax, %r15
	0x4c, 0x8b, 0x55, 0xd0, //0x00001df4 movq         $-48(%rbp), %r10
	0xe9, 0x61, 0xfd, 0xff, 0xff, //0x00001df8 jmp          LBB0_342
	//0x00001dfd LBB0_354
	0x48, 0xf7, 0xd7, //0x00001dfd notq         %rdi
	0x49, 0x89, 0xff, //0x00001e00 movq         %rdi, %r15
	0x4c, 0x8b, 0x55, 0xd0, //0x00001e03 movq         $-48(%rbp), %r10
	0xe9, 0x52, 0xfd, 0xff, 0xff, //0x00001e07 jmp          LBB0_342
	//0x00001e0c LBB0_355
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00001e0c movq         $-1, %r8
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00001e13 movq         $-1, %r12
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00001e1a movq         $-1, %rax
	0x4d, 0x89, 0xfe, //0x00001e21 movq         %r15, %r14
	0x49, 0x83, 0xfa, 0x10, //0x00001e24 cmpq         $16, %r10
	0x0f, 0x83, 0xc0, 0xea, 0xff, 0xff, //0x00001e28 jae          LBB0_151
	0xe9, 0x0d, 0xec, 0xff, 0xff, //0x00001e2e jmp          LBB0_169
	//0x00001e33 LBB0_356
	0x4d, 0x89, 0xcb, //0x00001e33 movq         %r9, %r11
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00001e36 movq         $-1, %r8
	0x45, 0x31, 0xf6, //0x00001e3d xorl         %r14d, %r14d
	0x48, 0x83, 0xf9, 0x20, //0x00001e40 cmpq         $32, %rcx
	0x0f, 0x83, 0x2f, 0xfc, 0xff, 0xff, //0x00001e44 jae          LBB0_55
	0xe9, 0x38, 0x01, 0x00, 0x00, //0x00001e4a jmp          LBB0_370
	//0x00001e4f LBB0_357
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00001e4f movq         $-1, %r8
	0x45, 0x31, 0xff, //0x00001e56 xorl         %r15d, %r15d
	0x49, 0x83, 0xfa, 0x20, //0x00001e59 cmpq         $32, %r10
	0x0f, 0x83, 0x8a, 0xfc, 0xff, 0xff, //0x00001e5d jae          LBB0_85
	0xe9, 0x57, 0x02, 0x00, 0x00, //0x00001e63 jmp          LBB0_388
	//0x00001e68 LBB0_358
	0x49, 0x89, 0xcf, //0x00001e68 movq         %rcx, %r15
	0x4d, 0x85, 0xdb, //0x00001e6b testq        %r11, %r11
	0x0f, 0x85, 0xf9, 0xfa, 0xff, 0xff, //0x00001e6e jne          LBB0_327
	0xe9, 0xd5, 0x0a, 0x00, 0x00, //0x00001e74 jmp          LBB0_497
	//0x00001e79 LBB0_359
	0x48, 0xf7, 0xd6, //0x00001e79 notq         %rsi
	0x49, 0x89, 0xf7, //0x00001e7c movq         %rsi, %r15
	0x4c, 0x8b, 0x55, 0xd0, //0x00001e7f movq         $-48(%rbp), %r10
	0xe9, 0xd6, 0xfc, 0xff, 0xff, //0x00001e83 jmp          LBB0_342
	//0x00001e88 LBB0_360
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001e88 movq         $-1, %r11
	0x45, 0x31, 0xf6, //0x00001e8f xorl         %r14d, %r14d
	0x48, 0x83, 0xf9, 0x20, //0x00001e92 cmpq         $32, %rcx
	0x0f, 0x83, 0x38, 0xfd, 0xff, 0xff, //0x00001e96 jae          LBB0_102
	0xe9, 0x35, 0x03, 0x00, 0x00, //0x00001e9c jmp          LBB0_402
	//0x00001ea1 LBB0_361
	0x49, 0x89, 0xc8, //0x00001ea1 movq         %rcx, %r8
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001ea4 movq         $-1, %r11
	0x45, 0x31, 0xff, //0x00001eab xorl         %r15d, %r15d
	0x48, 0x83, 0xfb, 0x20, //0x00001eae cmpq         $32, %rbx
	0x0f, 0x83, 0xb1, 0xfd, 0xff, 0xff, //0x00001eb2 jae          LBB0_121
	0xe9, 0x4f, 0x04, 0x00, 0x00, //0x00001eb8 jmp          LBB0_420
	//0x00001ebd LBB0_365
	0x49, 0xf7, 0xdf, //0x00001ebd negq         %r15
	0xe9, 0x99, 0xfc, 0xff, 0xff, //0x00001ec0 jmp          LBB0_342
	//0x00001ec5 LBB0_362
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00001ec5 movq         $-1, %r8
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001ecc movq         $-1, %r11
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00001ed3 movq         $-1, %r12
	0x4d, 0x89, 0xcf, //0x00001eda movq         %r9, %r15
	0xe9, 0xdb, 0xf7, 0xff, 0xff, //0x00001edd jmp          LBB0_290
	//0x00001ee2 LBB0_363
	0x49, 0x89, 0xd1, //0x00001ee2 movq         %rdx, %r9
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001ee5 movq         $-1, %r11
	0x45, 0x31, 0xf6, //0x00001eec xorl         %r14d, %r14d
	0x48, 0x83, 0xf9, 0x20, //0x00001eef cmpq         $32, %rcx
	0x0f, 0x83, 0x1d, 0xfe, 0xff, 0xff, //0x00001ef3 jae          LBB0_221
	0xe9, 0x15, 0x06, 0x00, 0x00, //0x00001ef9 jmp          LBB0_443
	//0x00001efe LBB0_364
	0x49, 0x89, 0xc8, //0x00001efe movq         %rcx, %r8
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001f01 movq         $-1, %r11
	0x45, 0x31, 0xff, //0x00001f08 xorl         %r15d, %r15d
	0x48, 0x83, 0xfb, 0x20, //0x00001f0b cmpq         $32, %rbx
	0x4c, 0x8b, 0x75, 0xc8, //0x00001f0f movq         $-56(%rbp), %r14
	0x0f, 0x83, 0x7f, 0xfe, 0xff, 0xff, //0x00001f13 jae          LBB0_260
	0xe9, 0x2b, 0x07, 0x00, 0x00, //0x00001f19 jmp          LBB0_460
	//0x00001f1e LBB0_366
	0x49, 0x83, 0xf8, 0xff, //0x00001f1e cmpq         $-1, %r8
	0x0f, 0x85, 0x0d, 0x00, 0x00, 0x00, //0x00001f22 jne          LBB0_368
	0x48, 0x89, 0xd8, //0x00001f28 movq         %rbx, %rax
	0x4c, 0x29, 0xd8, //0x00001f2b subq         %r11, %rax
	0x4c, 0x0f, 0xbc, 0xc6, //0x00001f2e bsfq         %rsi, %r8
	0x49, 0x01, 0xc0, //0x00001f32 addq         %rax, %r8
	//0x00001f35 LBB0_368
	0x44, 0x89, 0xf0, //0x00001f35 movl         %r14d, %eax
	0xf7, 0xd0, //0x00001f38 notl         %eax
	0x21, 0xf0, //0x00001f3a andl         %esi, %eax
	0x44, 0x8d, 0x0c, 0x00, //0x00001f3c leal         (%rax,%rax), %r9d
	0x45, 0x09, 0xf1, //0x00001f40 orl          %r14d, %r9d
	0x44, 0x89, 0xcf, //0x00001f43 movl         %r9d, %edi
	0xf7, 0xd7, //0x00001f46 notl         %edi
	0x21, 0xf7, //0x00001f48 andl         %esi, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001f4a andl         $-1431655766, %edi
	0x45, 0x31, 0xf6, //0x00001f50 xorl         %r14d, %r14d
	0x01, 0xc7, //0x00001f53 addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc6, //0x00001f55 setb         %r14b
	0x01, 0xff, //0x00001f59 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00001f5b xorl         $1431655765, %edi
	0x44, 0x21, 0xcf, //0x00001f61 andl         %r9d, %edi
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00001f64 movl         $4294967295, %eax
	0x31, 0xf8, //0x00001f69 xorl         %edi, %eax
	0x21, 0xc2, //0x00001f6b andl         %eax, %edx
	0x4c, 0x8b, 0x55, 0xd0, //0x00001f6d movq         $-48(%rbp), %r10
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00001f71 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xd2, //0x00001f76 testq        %rdx, %rdx
	0x0f, 0x85, 0x2b, 0xfb, 0xff, 0xff, //0x00001f79 jne          LBB0_60
	//0x00001f7f LBB0_369
	0x48, 0x83, 0xc3, 0x20, //0x00001f7f addq         $32, %rbx
	0x48, 0x83, 0xc1, 0xe0, //0x00001f83 addq         $-32, %rcx
	//0x00001f87 LBB0_370
	0x4d, 0x85, 0xf6, //0x00001f87 testq        %r14, %r14
	0x0f, 0x85, 0x1c, 0x04, 0x00, 0x00, //0x00001f8a jne          LBB0_429
	0x4c, 0x8b, 0x75, 0xc8, //0x00001f90 movq         $-56(%rbp), %r14
	0x48, 0x85, 0xc9, //0x00001f94 testq        %rcx, %rcx
	0x0f, 0x84, 0xeb, 0x07, 0x00, 0x00, //0x00001f97 je           LBB0_476
	//0x00001f9d LBB0_372
	0x4c, 0x89, 0xdf, //0x00001f9d movq         %r11, %rdi
	0x48, 0xf7, 0xd7, //0x00001fa0 notq         %rdi
	//0x00001fa3 LBB0_373
	0x4c, 0x8d, 0x63, 0x01, //0x00001fa3 leaq         $1(%rbx), %r12
	0x0f, 0xb6, 0x13, //0x00001fa7 movzbl       (%rbx), %edx
	0x80, 0xfa, 0x22, //0x00001faa cmpb         $34, %dl
	0x0f, 0x84, 0x5b, 0x00, 0x00, 0x00, //0x00001fad je           LBB0_378
	0x48, 0x8d, 0x71, 0xff, //0x00001fb3 leaq         $-1(%rcx), %rsi
	0x80, 0xfa, 0x5c, //0x00001fb7 cmpb         $92, %dl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00001fba je           LBB0_376
	0x48, 0x89, 0xf1, //0x00001fc0 movq         %rsi, %rcx
	0x4c, 0x89, 0xe3, //0x00001fc3 movq         %r12, %rbx
	0x48, 0x85, 0xf6, //0x00001fc6 testq        %rsi, %rsi
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x00001fc9 jne          LBB0_373
	0xe9, 0xb4, 0x07, 0x00, 0x00, //0x00001fcf jmp          LBB0_476
	//0x00001fd4 LBB0_376
	0x48, 0x85, 0xf6, //0x00001fd4 testq        %rsi, %rsi
	0x0f, 0x84, 0xba, 0x09, 0x00, 0x00, //0x00001fd7 je           LBB0_433
	0x49, 0x01, 0xfc, //0x00001fdd addq         %rdi, %r12
	0x49, 0x83, 0xf8, 0xff, //0x00001fe0 cmpq         $-1, %r8
	0x4d, 0x0f, 0x44, 0xc4, //0x00001fe4 cmoveq       %r12, %r8
	0x48, 0x83, 0xc3, 0x02, //0x00001fe8 addq         $2, %rbx
	0x48, 0x83, 0xc1, 0xfe, //0x00001fec addq         $-2, %rcx
	0x48, 0x89, 0xce, //0x00001ff0 movq         %rcx, %rsi
	0x4c, 0x8b, 0x55, 0xd0, //0x00001ff3 movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x00001ff7 movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00001ffb vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xf6, //0x00002000 testq        %rsi, %rsi
	0x0f, 0x85, 0x9a, 0xff, 0xff, 0xff, //0x00002003 jne          LBB0_373
	0xe9, 0x7a, 0x07, 0x00, 0x00, //0x00002009 jmp          LBB0_476
	//0x0000200e LBB0_378
	0x4d, 0x29, 0xdc, //0x0000200e subq         %r11, %r12
	0xe9, 0xa4, 0xfa, 0xff, 0xff, //0x00002011 jmp          LBB0_184
	//0x00002016 LBB0_379
	0x49, 0x83, 0xf8, 0xff, //0x00002016 cmpq         $-1, %r8
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x0000201a jne          LBB0_381
	0x4c, 0x89, 0xe2, //0x00002020 movq         %r12, %rdx
	0x48, 0x2b, 0x55, 0xc0, //0x00002023 subq         $-64(%rbp), %rdx
	0x4c, 0x0f, 0xbc, 0xc1, //0x00002027 bsfq         %rcx, %r8
	0x49, 0x01, 0xd0, //0x0000202b addq         %rdx, %r8
	//0x0000202e LBB0_381
	0x44, 0x89, 0xfa, //0x0000202e movl         %r15d, %edx
	0xf7, 0xd2, //0x00002031 notl         %edx
	0x21, 0xca, //0x00002033 andl         %ecx, %edx
	0x8d, 0x1c, 0x12, //0x00002035 leal         (%rdx,%rdx), %ebx
	0x44, 0x09, 0xfb, //0x00002038 orl          %r15d, %ebx
	0x89, 0xdf, //0x0000203b movl         %ebx, %edi
	0xf7, 0xd7, //0x0000203d notl         %edi
	0x21, 0xcf, //0x0000203f andl         %ecx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002041 andl         $-1431655766, %edi
	0x45, 0x31, 0xff, //0x00002047 xorl         %r15d, %r15d
	0x01, 0xd7, //0x0000204a addl         %edx, %edi
	0x41, 0x0f, 0x92, 0xc7, //0x0000204c setb         %r15b
	0x01, 0xff, //0x00002050 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00002052 xorl         $1431655765, %edi
	0x21, 0xdf, //0x00002058 andl         %ebx, %edi
	0xb9, 0xff, 0xff, 0xff, 0xff, //0x0000205a movl         $4294967295, %ecx
	0x31, 0xf9, //0x0000205f xorl         %edi, %ecx
	0x21, 0xce, //0x00002061 andl         %ecx, %esi
	0x4c, 0x8b, 0x75, 0xc8, //0x00002063 movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002067 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xf6, //0x0000206c testq        %rsi, %rsi
	0x0f, 0x85, 0xbc, 0xfa, 0xff, 0xff, //0x0000206f jne          LBB0_88
	//0x00002075 LBB0_382
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00002075 movl         $64, %ecx
	//0x0000207a LBB0_383
	0x49, 0x0f, 0xbc, 0xd1, //0x0000207a bsfq         %r9, %rdx
	0x45, 0x85, 0xc9, //0x0000207e testl        %r9d, %r9d
	0xbf, 0x40, 0x00, 0x00, 0x00, //0x00002081 movl         $64, %edi
	0x48, 0x0f, 0x45, 0xfa, //0x00002086 cmovneq      %rdx, %rdi
	0x48, 0x85, 0xf6, //0x0000208a testq        %rsi, %rsi
	0x0f, 0x84, 0x1b, 0x00, 0x00, 0x00, //0x0000208d je           LBB0_386
	0x4c, 0x2b, 0x65, 0xc0, //0x00002093 subq         $-64(%rbp), %r12
	0x48, 0x39, 0xcf, //0x00002097 cmpq         %rcx, %rdi
	0x0f, 0x82, 0x12, 0x09, 0x00, 0x00, //0x0000209a jb           LBB0_511
	0x4d, 0x8d, 0x64, 0x0c, 0x01, //0x000020a0 leaq         $1(%r12,%rcx), %r12
	0x4c, 0x8b, 0x55, 0xd0, //0x000020a5 movq         $-48(%rbp), %r10
	0xe9, 0x0c, 0xfa, 0xff, 0xff, //0x000020a9 jmp          LBB0_184
	//0x000020ae LBB0_386
	0x45, 0x85, 0xc9, //0x000020ae testl        %r9d, %r9d
	0x0f, 0x85, 0x0d, 0x09, 0x00, 0x00, //0x000020b1 jne          LBB0_512
	0x49, 0x83, 0xc4, 0x20, //0x000020b7 addq         $32, %r12
	0x49, 0x83, 0xc2, 0xe0, //0x000020bb addq         $-32, %r10
	//0x000020bf LBB0_388
	0x4d, 0x85, 0xff, //0x000020bf testq        %r15, %r15
	0x0f, 0x85, 0x1f, 0x03, 0x00, 0x00, //0x000020c2 jne          LBB0_431
	0x48, 0x8b, 0x45, 0xc0, //0x000020c8 movq         $-64(%rbp), %rax
	0x4d, 0x85, 0xd2, //0x000020cc testq        %r10, %r10
	0x0f, 0x84, 0xc2, 0x08, 0x00, 0x00, //0x000020cf je           LBB0_433
	//0x000020d5 LBB0_390
	0x41, 0x0f, 0xb6, 0x0c, 0x24, //0x000020d5 movzbl       (%r12), %ecx
	0x80, 0xf9, 0x22, //0x000020da cmpb         $34, %cl
	0x0f, 0x84, 0x6d, 0x00, 0x00, 0x00, //0x000020dd je           LBB0_396
	0x80, 0xf9, 0x5c, //0x000020e3 cmpb         $92, %cl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x000020e6 je           LBB0_394
	0x80, 0xf9, 0x1f, //0x000020ec cmpb         $31, %cl
	0x0f, 0x86, 0xed, 0x08, 0x00, 0x00, //0x000020ef jbe          LBB0_513
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000020f5 movq         $-1, %rcx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x000020fc movl         $1, %edx
	0x49, 0x01, 0xd4, //0x00002101 addq         %rdx, %r12
	0x49, 0x01, 0xca, //0x00002104 addq         %rcx, %r10
	0x0f, 0x85, 0xc8, 0xff, 0xff, 0xff, //0x00002107 jne          LBB0_390
	0xe9, 0x85, 0x08, 0x00, 0x00, //0x0000210d jmp          LBB0_433
	//0x00002112 LBB0_394
	0x49, 0x83, 0xfa, 0x01, //0x00002112 cmpq         $1, %r10
	0x0f, 0x84, 0x7b, 0x08, 0x00, 0x00, //0x00002116 je           LBB0_433
	0x4c, 0x89, 0xe1, //0x0000211c movq         %r12, %rcx
	0x48, 0x29, 0xc1, //0x0000211f subq         %rax, %rcx
	0x49, 0x83, 0xf8, 0xff, //0x00002122 cmpq         $-1, %r8
	0x4c, 0x0f, 0x44, 0xc1, //0x00002126 cmoveq       %rcx, %r8
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x0000212a movq         $-2, %rcx
	0xba, 0x02, 0x00, 0x00, 0x00, //0x00002131 movl         $2, %edx
	0x4c, 0x8b, 0x75, 0xc8, //0x00002136 movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000213a vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x49, 0x01, 0xd4, //0x0000213f addq         %rdx, %r12
	0x49, 0x01, 0xca, //0x00002142 addq         %rcx, %r10
	0x0f, 0x85, 0x8a, 0xff, 0xff, 0xff, //0x00002145 jne          LBB0_390
	0xe9, 0x47, 0x08, 0x00, 0x00, //0x0000214b jmp          LBB0_433
	//0x00002150 LBB0_396
	0x49, 0x29, 0xc4, //0x00002150 subq         %rax, %r12
	0x49, 0xff, 0xc4, //0x00002153 incq         %r12
	0x4c, 0x8b, 0x55, 0xd0, //0x00002156 movq         $-48(%rbp), %r10
	0xe9, 0x5b, 0xf9, 0xff, 0xff, //0x0000215a jmp          LBB0_184
	//0x0000215f LBB0_397
	0x4d, 0x89, 0xc8, //0x0000215f movq         %r9, %r8
	0x49, 0x83, 0xfb, 0xff, //0x00002162 cmpq         $-1, %r11
	0x0f, 0x85, 0x15, 0x00, 0x00, 0x00, //0x00002166 jne          LBB0_400
	0x48, 0x89, 0xd8, //0x0000216c movq         %rbx, %rax
	0x4c, 0x29, 0xc0, //0x0000216f subq         %r8, %rax
	0x4c, 0x0f, 0xbc, 0xde, //0x00002172 bsfq         %rsi, %r11
	0x49, 0x01, 0xc3, //0x00002176 addq         %rax, %r11
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x00002179 jmp          LBB0_400
	//0x0000217e LBB0_399
	0x4d, 0x89, 0xc8, //0x0000217e movq         %r9, %r8
	//0x00002181 LBB0_400
	0x44, 0x89, 0xf0, //0x00002181 movl         %r14d, %eax
	0xf7, 0xd0, //0x00002184 notl         %eax
	0x21, 0xf0, //0x00002186 andl         %esi, %eax
	0x44, 0x8d, 0x0c, 0x00, //0x00002188 leal         (%rax,%rax), %r9d
	0x45, 0x09, 0xf1, //0x0000218c orl          %r14d, %r9d
	0x44, 0x89, 0xcf, //0x0000218f movl         %r9d, %edi
	0xf7, 0xd7, //0x00002192 notl         %edi
	0x21, 0xf7, //0x00002194 andl         %esi, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002196 andl         $-1431655766, %edi
	0x45, 0x31, 0xf6, //0x0000219c xorl         %r14d, %r14d
	0x01, 0xc7, //0x0000219f addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc6, //0x000021a1 setb         %r14b
	0x01, 0xff, //0x000021a5 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x000021a7 xorl         $1431655765, %edi
	0x44, 0x21, 0xcf, //0x000021ad andl         %r9d, %edi
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x000021b0 movl         $4294967295, %eax
	0x31, 0xf8, //0x000021b5 xorl         %edi, %eax
	0x21, 0xc2, //0x000021b7 andl         %eax, %edx
	0x4c, 0x8b, 0x55, 0xd0, //0x000021b9 movq         $-48(%rbp), %r10
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000021bd vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x4d, 0x89, 0xc1, //0x000021c2 movq         %r8, %r9
	0x48, 0x85, 0xd2, //0x000021c5 testq        %rdx, %rdx
	0x0f, 0x85, 0x37, 0xfa, 0xff, 0xff, //0x000021c8 jne          LBB0_107
	//0x000021ce LBB0_401
	0x48, 0x83, 0xc3, 0x20, //0x000021ce addq         $32, %rbx
	0x48, 0x83, 0xc1, 0xe0, //0x000021d2 addq         $-32, %rcx
	//0x000021d6 LBB0_402
	0x4d, 0x85, 0xf6, //0x000021d6 testq        %r14, %r14
	0x0f, 0x85, 0x43, 0x02, 0x00, 0x00, //0x000021d9 jne          LBB0_434
	0x4c, 0x8b, 0x75, 0xc8, //0x000021df movq         $-56(%rbp), %r14
	0x48, 0x85, 0xc9, //0x000021e3 testq        %rcx, %rcx
	0x0f, 0x84, 0xc6, 0x05, 0x00, 0x00, //0x000021e6 je           LBB0_479
	//0x000021ec LBB0_404
	0x4d, 0x89, 0xe0, //0x000021ec movq         %r12, %r8
	0x4c, 0x89, 0xcf, //0x000021ef movq         %r9, %rdi
	0x48, 0xf7, 0xd7, //0x000021f2 notq         %rdi
	//0x000021f5 LBB0_405
	0x4c, 0x8d, 0x63, 0x01, //0x000021f5 leaq         $1(%rbx), %r12
	0x0f, 0xb6, 0x13, //0x000021f9 movzbl       (%rbx), %edx
	0x80, 0xfa, 0x22, //0x000021fc cmpb         $34, %dl
	0x0f, 0x84, 0x5b, 0x00, 0x00, 0x00, //0x000021ff je           LBB0_410
	0x48, 0x8d, 0x71, 0xff, //0x00002205 leaq         $-1(%rcx), %rsi
	0x80, 0xfa, 0x5c, //0x00002209 cmpb         $92, %dl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x0000220c je           LBB0_408
	0x48, 0x89, 0xf1, //0x00002212 movq         %rsi, %rcx
	0x4c, 0x89, 0xe3, //0x00002215 movq         %r12, %rbx
	0x48, 0x85, 0xf6, //0x00002218 testq        %rsi, %rsi
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x0000221b jne          LBB0_405
	0xe9, 0x8c, 0x05, 0x00, 0x00, //0x00002221 jmp          LBB0_479
	//0x00002226 LBB0_408
	0x48, 0x85, 0xf6, //0x00002226 testq        %rsi, %rsi
	0x0f, 0x84, 0xcf, 0x07, 0x00, 0x00, //0x00002229 je           LBB0_516
	0x49, 0x01, 0xfc, //0x0000222f addq         %rdi, %r12
	0x49, 0x83, 0xfb, 0xff, //0x00002232 cmpq         $-1, %r11
	0x4d, 0x0f, 0x44, 0xdc, //0x00002236 cmoveq       %r12, %r11
	0x48, 0x83, 0xc3, 0x02, //0x0000223a addq         $2, %rbx
	0x48, 0x83, 0xc1, 0xfe, //0x0000223e addq         $-2, %rcx
	0x48, 0x89, 0xce, //0x00002242 movq         %rcx, %rsi
	0x4c, 0x8b, 0x55, 0xd0, //0x00002245 movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x00002249 movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000224d vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xf6, //0x00002252 testq        %rsi, %rsi
	0x0f, 0x85, 0x9a, 0xff, 0xff, 0xff, //0x00002255 jne          LBB0_405
	0xe9, 0x52, 0x05, 0x00, 0x00, //0x0000225b jmp          LBB0_479
	//0x00002260 LBB0_410
	0x4d, 0x29, 0xcc, //0x00002260 subq         %r9, %r12
	0xe9, 0xb0, 0xf9, 0xff, 0xff, //0x00002263 jmp          LBB0_205
	//0x00002268 LBB0_411
	0x49, 0x83, 0xfb, 0xff, //0x00002268 cmpq         $-1, %r11
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x0000226c jne          LBB0_413
	0x4c, 0x89, 0xe0, //0x00002272 movq         %r12, %rax
	0x48, 0x2b, 0x45, 0xc0, //0x00002275 subq         $-64(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xd9, //0x00002279 bsfq         %rcx, %r11
	0x49, 0x01, 0xc3, //0x0000227d addq         %rax, %r11
	//0x00002280 LBB0_413
	0x44, 0x89, 0xf8, //0x00002280 movl         %r15d, %eax
	0xf7, 0xd0, //0x00002283 notl         %eax
	0x21, 0xc8, //0x00002285 andl         %ecx, %eax
	0x8d, 0x14, 0x00, //0x00002287 leal         (%rax,%rax), %edx
	0x44, 0x09, 0xfa, //0x0000228a orl          %r15d, %edx
	0x89, 0xd7, //0x0000228d movl         %edx, %edi
	0xf7, 0xd7, //0x0000228f notl         %edi
	0x21, 0xcf, //0x00002291 andl         %ecx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002293 andl         $-1431655766, %edi
	0x45, 0x31, 0xff, //0x00002299 xorl         %r15d, %r15d
	0x01, 0xc7, //0x0000229c addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc7, //0x0000229e setb         %r15b
	0x01, 0xff, //0x000022a2 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x000022a4 xorl         $1431655765, %edi
	0x21, 0xd7, //0x000022aa andl         %edx, %edi
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x000022ac movl         $4294967295, %eax
	0x31, 0xf8, //0x000022b1 xorl         %edi, %eax
	0x21, 0xc6, //0x000022b3 andl         %eax, %esi
	0x4c, 0x8b, 0x55, 0xd0, //0x000022b5 movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x000022b9 movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000022bd vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xf6, //0x000022c2 testq        %rsi, %rsi
	0x0f, 0x85, 0xe2, 0xf9, 0xff, 0xff, //0x000022c5 jne          LBB0_124
	//0x000022cb LBB0_414
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x000022cb movl         $64, %ecx
	//0x000022d0 LBB0_415
	0x49, 0x0f, 0xbc, 0xd1, //0x000022d0 bsfq         %r9, %rdx
	0x45, 0x85, 0xc9, //0x000022d4 testl        %r9d, %r9d
	0xbf, 0x40, 0x00, 0x00, 0x00, //0x000022d7 movl         $64, %edi
	0x48, 0x0f, 0x45, 0xfa, //0x000022dc cmovneq      %rdx, %rdi
	0x48, 0x85, 0xf6, //0x000022e0 testq        %rsi, %rsi
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x000022e3 je           LBB0_418
	0x4c, 0x2b, 0x65, 0xc0, //0x000022e9 subq         $-64(%rbp), %r12
	0x48, 0x39, 0xcf, //0x000022ed cmpq         %rcx, %rdi
	0x0f, 0x83, 0x1a, 0xee, 0xff, 0xff, //0x000022f0 jae          LBB0_204
	0xe9, 0xd5, 0x06, 0x00, 0x00, //0x000022f6 jmp          LBB0_417
	//0x000022fb LBB0_418
	0x45, 0x85, 0xc9, //0x000022fb testl        %r9d, %r9d
	0x0f, 0x85, 0xe6, 0x06, 0x00, 0x00, //0x000022fe jne          LBB0_514
	0x49, 0x83, 0xc4, 0x20, //0x00002304 addq         $32, %r12
	0x48, 0x83, 0xc3, 0xe0, //0x00002308 addq         $-32, %rbx
	//0x0000230c LBB0_420
	0x4d, 0x85, 0xff, //0x0000230c testq        %r15, %r15
	0x0f, 0x85, 0x48, 0x01, 0x00, 0x00, //0x0000230f jne          LBB0_436
	0x48, 0x8b, 0x75, 0xc0, //0x00002315 movq         $-64(%rbp), %rsi
	0x48, 0x85, 0xdb, //0x00002319 testq        %rbx, %rbx
	0x0f, 0x84, 0x90, 0x04, 0x00, 0x00, //0x0000231c je           LBB0_479
	//0x00002322 LBB0_422
	0x41, 0x0f, 0xb6, 0x0c, 0x24, //0x00002322 movzbl       (%r12), %ecx
	0x80, 0xf9, 0x22, //0x00002327 cmpb         $34, %cl
	0x0f, 0x84, 0x71, 0x00, 0x00, 0x00, //0x0000232a je           LBB0_428
	0x80, 0xf9, 0x5c, //0x00002330 cmpb         $92, %cl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00002333 je           LBB0_426
	0x80, 0xf9, 0x1f, //0x00002339 cmpb         $31, %cl
	0x0f, 0x86, 0xb4, 0x06, 0x00, 0x00, //0x0000233c jbe          LBB0_515
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00002342 movq         $-1, %rcx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00002349 movl         $1, %edx
	0x49, 0x01, 0xd4, //0x0000234e addq         %rdx, %r12
	0x48, 0x01, 0xcb, //0x00002351 addq         %rcx, %rbx
	0x0f, 0x85, 0xc8, 0xff, 0xff, 0xff, //0x00002354 jne          LBB0_422
	0xe9, 0x53, 0x04, 0x00, 0x00, //0x0000235a jmp          LBB0_479
	//0x0000235f LBB0_426
	0x48, 0x83, 0xfb, 0x01, //0x0000235f cmpq         $1, %rbx
	0x0f, 0x84, 0x95, 0x06, 0x00, 0x00, //0x00002363 je           LBB0_516
	0x4c, 0x89, 0xe0, //0x00002369 movq         %r12, %rax
	0x48, 0x29, 0xf0, //0x0000236c subq         %rsi, %rax
	0x49, 0x83, 0xfb, 0xff, //0x0000236f cmpq         $-1, %r11
	0x4c, 0x0f, 0x44, 0xd8, //0x00002373 cmoveq       %rax, %r11
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00002377 movq         $-2, %rcx
	0xba, 0x02, 0x00, 0x00, 0x00, //0x0000237e movl         $2, %edx
	0x4c, 0x8b, 0x55, 0xd0, //0x00002383 movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x00002387 movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000238b vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x49, 0x01, 0xd4, //0x00002390 addq         %rdx, %r12
	0x48, 0x01, 0xcb, //0x00002393 addq         %rcx, %rbx
	0x0f, 0x85, 0x86, 0xff, 0xff, 0xff, //0x00002396 jne          LBB0_422
	0xe9, 0x11, 0x04, 0x00, 0x00, //0x0000239c jmp          LBB0_479
	//0x000023a1 LBB0_428
	0x49, 0x29, 0xf4, //0x000023a1 subq         %rsi, %r12
	0x49, 0xff, 0xc4, //0x000023a4 incq         %r12
	0xe9, 0x6c, 0xf8, 0xff, 0xff, //0x000023a7 jmp          LBB0_205
	//0x000023ac LBB0_429
	0x48, 0x85, 0xc9, //0x000023ac testq        %rcx, %rcx
	0x0f, 0x84, 0xe2, 0x05, 0x00, 0x00, //0x000023af je           LBB0_433
	0x4c, 0x89, 0xd8, //0x000023b5 movq         %r11, %rax
	0x48, 0xf7, 0xd0, //0x000023b8 notq         %rax
	0x48, 0x01, 0xd8, //0x000023bb addq         %rbx, %rax
	0x49, 0x83, 0xf8, 0xff, //0x000023be cmpq         $-1, %r8
	0x4c, 0x0f, 0x44, 0xc0, //0x000023c2 cmoveq       %rax, %r8
	0x48, 0xff, 0xc3, //0x000023c6 incq         %rbx
	0x48, 0xff, 0xc9, //0x000023c9 decq         %rcx
	0x4c, 0x8b, 0x55, 0xd0, //0x000023cc movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x000023d0 movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000023d4 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xc9, //0x000023d9 testq        %rcx, %rcx
	0x0f, 0x85, 0xbb, 0xfb, 0xff, 0xff, //0x000023dc jne          LBB0_372
	0xe9, 0xa1, 0x03, 0x00, 0x00, //0x000023e2 jmp          LBB0_476
	//0x000023e7 LBB0_431
	0x4d, 0x85, 0xd2, //0x000023e7 testq        %r10, %r10
	0x0f, 0x84, 0xa7, 0x05, 0x00, 0x00, //0x000023ea je           LBB0_433
	0x48, 0x8b, 0x45, 0xc0, //0x000023f0 movq         $-64(%rbp), %rax
	0x48, 0x89, 0xc1, //0x000023f4 movq         %rax, %rcx
	0x48, 0xf7, 0xd1, //0x000023f7 notq         %rcx
	0x4c, 0x01, 0xe1, //0x000023fa addq         %r12, %rcx
	0x49, 0x83, 0xf8, 0xff, //0x000023fd cmpq         $-1, %r8
	0x4c, 0x0f, 0x44, 0xc1, //0x00002401 cmoveq       %rcx, %r8
	0x49, 0xff, 0xc4, //0x00002405 incq         %r12
	0x49, 0xff, 0xca, //0x00002408 decq         %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x0000240b movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000240f vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x4d, 0x85, 0xd2, //0x00002414 testq        %r10, %r10
	0x0f, 0x85, 0xb8, 0xfc, 0xff, 0xff, //0x00002417 jne          LBB0_390
	0xe9, 0x75, 0x05, 0x00, 0x00, //0x0000241d jmp          LBB0_433
	//0x00002422 LBB0_434
	0x48, 0x85, 0xc9, //0x00002422 testq        %rcx, %rcx
	0x0f, 0x84, 0xd3, 0x05, 0x00, 0x00, //0x00002425 je           LBB0_516
	0x4c, 0x89, 0xc8, //0x0000242b movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x0000242e notq         %rax
	0x48, 0x01, 0xd8, //0x00002431 addq         %rbx, %rax
	0x49, 0x83, 0xfb, 0xff, //0x00002434 cmpq         $-1, %r11
	0x4c, 0x0f, 0x44, 0xd8, //0x00002438 cmoveq       %rax, %r11
	0x48, 0xff, 0xc3, //0x0000243c incq         %rbx
	0x48, 0xff, 0xc9, //0x0000243f decq         %rcx
	0x4c, 0x8b, 0x55, 0xd0, //0x00002442 movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x00002446 movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000244a vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xc9, //0x0000244f testq        %rcx, %rcx
	0x0f, 0x85, 0x94, 0xfd, 0xff, 0xff, //0x00002452 jne          LBB0_404
	0xe9, 0x55, 0x03, 0x00, 0x00, //0x00002458 jmp          LBB0_479
	//0x0000245d LBB0_436
	0x48, 0x85, 0xdb, //0x0000245d testq        %rbx, %rbx
	0x0f, 0x84, 0x98, 0x05, 0x00, 0x00, //0x00002460 je           LBB0_516
	0x48, 0x8b, 0x75, 0xc0, //0x00002466 movq         $-64(%rbp), %rsi
	0x48, 0x89, 0xf0, //0x0000246a movq         %rsi, %rax
	0x48, 0xf7, 0xd0, //0x0000246d notq         %rax
	0x4c, 0x01, 0xe0, //0x00002470 addq         %r12, %rax
	0x49, 0x83, 0xfb, 0xff, //0x00002473 cmpq         $-1, %r11
	0x4c, 0x0f, 0x44, 0xd8, //0x00002477 cmoveq       %rax, %r11
	0x49, 0xff, 0xc4, //0x0000247b incq         %r12
	0x48, 0xff, 0xcb, //0x0000247e decq         %rbx
	0x4c, 0x8b, 0x55, 0xd0, //0x00002481 movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x00002485 movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002489 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xdb, //0x0000248e testq        %rbx, %rbx
	0x0f, 0x85, 0x8b, 0xfe, 0xff, 0xff, //0x00002491 jne          LBB0_422
	0xe9, 0x16, 0x03, 0x00, 0x00, //0x00002497 jmp          LBB0_479
	//0x0000249c LBB0_438
	0x4d, 0x89, 0xc8, //0x0000249c movq         %r9, %r8
	0x49, 0x83, 0xfb, 0xff, //0x0000249f cmpq         $-1, %r11
	0x0f, 0x85, 0x15, 0x00, 0x00, 0x00, //0x000024a3 jne          LBB0_441
	0x48, 0x89, 0xd8, //0x000024a9 movq         %rbx, %rax
	0x4c, 0x29, 0xc0, //0x000024ac subq         %r8, %rax
	0x4c, 0x0f, 0xbc, 0xde, //0x000024af bsfq         %rsi, %r11
	0x49, 0x01, 0xc3, //0x000024b3 addq         %rax, %r11
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x000024b6 jmp          LBB0_441
	//0x000024bb LBB0_440
	0x4d, 0x89, 0xc8, //0x000024bb movq         %r9, %r8
	//0x000024be LBB0_441
	0x44, 0x89, 0xf0, //0x000024be movl         %r14d, %eax
	0xf7, 0xd0, //0x000024c1 notl         %eax
	0x21, 0xf0, //0x000024c3 andl         %esi, %eax
	0x44, 0x8d, 0x0c, 0x00, //0x000024c5 leal         (%rax,%rax), %r9d
	0x45, 0x09, 0xf1, //0x000024c9 orl          %r14d, %r9d
	0x44, 0x89, 0xcf, //0x000024cc movl         %r9d, %edi
	0xf7, 0xd7, //0x000024cf notl         %edi
	0x21, 0xf7, //0x000024d1 andl         %esi, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x000024d3 andl         $-1431655766, %edi
	0x45, 0x31, 0xf6, //0x000024d9 xorl         %r14d, %r14d
	0x01, 0xc7, //0x000024dc addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc6, //0x000024de setb         %r14b
	0x01, 0xff, //0x000024e2 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x000024e4 xorl         $1431655765, %edi
	0x44, 0x21, 0xcf, //0x000024ea andl         %r9d, %edi
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x000024ed movl         $4294967295, %eax
	0x31, 0xc7, //0x000024f2 xorl         %eax, %edi
	0x21, 0xfa, //0x000024f4 andl         %edi, %edx
	0x4c, 0x8b, 0x55, 0xd0, //0x000024f6 movq         $-48(%rbp), %r10
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000024fa vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x4d, 0x89, 0xc1, //0x000024ff movq         %r8, %r9
	0x48, 0x85, 0xd2, //0x00002502 testq        %rdx, %rdx
	0x0f, 0x85, 0x3c, 0xf8, 0xff, 0xff, //0x00002505 jne          LBB0_226
	//0x0000250b LBB0_442
	0x48, 0x83, 0xc3, 0x20, //0x0000250b addq         $32, %rbx
	0x48, 0x83, 0xc1, 0xe0, //0x0000250f addq         $-32, %rcx
	//0x00002513 LBB0_443
	0x4d, 0x85, 0xf6, //0x00002513 testq        %r14, %r14
	0x0f, 0x85, 0xcd, 0x01, 0x00, 0x00, //0x00002516 jne          LBB0_469
	0x4c, 0x8b, 0x75, 0xc8, //0x0000251c movq         $-56(%rbp), %r14
	0x48, 0x85, 0xc9, //0x00002520 testq        %rcx, %rcx
	0x0f, 0x84, 0x89, 0x02, 0x00, 0x00, //0x00002523 je           LBB0_479
	//0x00002529 LBB0_445
	0x4d, 0x89, 0xe0, //0x00002529 movq         %r12, %r8
	0x4c, 0x89, 0xcf, //0x0000252c movq         %r9, %rdi
	0x48, 0xf7, 0xd7, //0x0000252f notq         %rdi
	//0x00002532 LBB0_446
	0x4c, 0x8d, 0x63, 0x01, //0x00002532 leaq         $1(%rbx), %r12
	0x0f, 0xb6, 0x13, //0x00002536 movzbl       (%rbx), %edx
	0x80, 0xfa, 0x22, //0x00002539 cmpb         $34, %dl
	0x0f, 0x84, 0x5b, 0x00, 0x00, 0x00, //0x0000253c je           LBB0_451
	0x48, 0x8d, 0x71, 0xff, //0x00002542 leaq         $-1(%rcx), %rsi
	0x80, 0xfa, 0x5c, //0x00002546 cmpb         $92, %dl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00002549 je           LBB0_449
	0x48, 0x89, 0xf1, //0x0000254f movq         %rsi, %rcx
	0x4c, 0x89, 0xe3, //0x00002552 movq         %r12, %rbx
	0x48, 0x85, 0xf6, //0x00002555 testq        %rsi, %rsi
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x00002558 jne          LBB0_446
	0xe9, 0x4f, 0x02, 0x00, 0x00, //0x0000255e jmp          LBB0_479
	//0x00002563 LBB0_449
	0x48, 0x85, 0xf6, //0x00002563 testq        %rsi, %rsi
	0x0f, 0x84, 0x92, 0x04, 0x00, 0x00, //0x00002566 je           LBB0_516
	0x49, 0x01, 0xfc, //0x0000256c addq         %rdi, %r12
	0x49, 0x83, 0xfb, 0xff, //0x0000256f cmpq         $-1, %r11
	0x4d, 0x0f, 0x44, 0xdc, //0x00002573 cmoveq       %r12, %r11
	0x48, 0x83, 0xc3, 0x02, //0x00002577 addq         $2, %rbx
	0x48, 0x83, 0xc1, 0xfe, //0x0000257b addq         $-2, %rcx
	0x48, 0x89, 0xce, //0x0000257f movq         %rcx, %rsi
	0x4c, 0x8b, 0x55, 0xd0, //0x00002582 movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x00002586 movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000258a vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xf6, //0x0000258f testq        %rsi, %rsi
	0x0f, 0x85, 0x9a, 0xff, 0xff, 0xff, //0x00002592 jne          LBB0_446
	0xe9, 0x15, 0x02, 0x00, 0x00, //0x00002598 jmp          LBB0_479
	//0x0000259d LBB0_451
	0x4d, 0x29, 0xcc, //0x0000259d subq         %r9, %r12
	0xe9, 0xb5, 0xf7, 0xff, 0xff, //0x000025a0 jmp          LBB0_323
	//0x000025a5 LBB0_452
	0x49, 0x83, 0xfb, 0xff, //0x000025a5 cmpq         $-1, %r11
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x000025a9 jne          LBB0_454
	0x4c, 0x89, 0xe0, //0x000025af movq         %r12, %rax
	0x48, 0x2b, 0x45, 0xc0, //0x000025b2 subq         $-64(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xd9, //0x000025b6 bsfq         %rcx, %r11
	0x49, 0x01, 0xc3, //0x000025ba addq         %rax, %r11
	//0x000025bd LBB0_454
	0x44, 0x89, 0xf8, //0x000025bd movl         %r15d, %eax
	0xf7, 0xd0, //0x000025c0 notl         %eax
	0x21, 0xc8, //0x000025c2 andl         %ecx, %eax
	0x8d, 0x14, 0x00, //0x000025c4 leal         (%rax,%rax), %edx
	0x44, 0x09, 0xfa, //0x000025c7 orl          %r15d, %edx
	0x89, 0xd7, //0x000025ca movl         %edx, %edi
	0xf7, 0xd7, //0x000025cc notl         %edi
	0x21, 0xcf, //0x000025ce andl         %ecx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x000025d0 andl         $-1431655766, %edi
	0x45, 0x31, 0xff, //0x000025d6 xorl         %r15d, %r15d
	0x01, 0xc7, //0x000025d9 addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc7, //0x000025db setb         %r15b
	0x01, 0xff, //0x000025df addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x000025e1 xorl         $1431655765, %edi
	0x21, 0xd7, //0x000025e7 andl         %edx, %edi
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x000025e9 movl         $4294967295, %eax
	0x31, 0xf8, //0x000025ee xorl         %edi, %eax
	0x21, 0xc6, //0x000025f0 andl         %eax, %esi
	0x4c, 0x8b, 0x55, 0xd0, //0x000025f2 movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x000025f6 movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000025fa vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xf6, //0x000025ff testq        %rsi, %rsi
	0x0f, 0x85, 0xd4, 0xf7, 0xff, 0xff, //0x00002602 jne          LBB0_263
	//0x00002608 LBB0_455
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00002608 movl         $64, %ecx
	//0x0000260d LBB0_456
	0x49, 0x0f, 0xbc, 0xd1, //0x0000260d bsfq         %r9, %rdx
	0x45, 0x85, 0xc9, //0x00002611 testl        %r9d, %r9d
	0xbf, 0x40, 0x00, 0x00, 0x00, //0x00002614 movl         $64, %edi
	0x48, 0x0f, 0x45, 0xfa, //0x00002619 cmovneq      %rdx, %rdi
	0x48, 0x85, 0xf6, //0x0000261d testq        %rsi, %rsi
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00002620 je           LBB0_458
	0x4c, 0x2b, 0x65, 0xc0, //0x00002626 subq         $-64(%rbp), %r12
	0x48, 0x39, 0xcf, //0x0000262a cmpq         %rcx, %rdi
	0x0f, 0x83, 0x20, 0xf3, 0xff, 0xff, //0x0000262d jae          LBB0_322
	0xe9, 0x98, 0x03, 0x00, 0x00, //0x00002633 jmp          LBB0_417
	//0x00002638 LBB0_458
	0x45, 0x85, 0xc9, //0x00002638 testl        %r9d, %r9d
	0x0f, 0x85, 0xa9, 0x03, 0x00, 0x00, //0x0000263b jne          LBB0_514
	0x49, 0x83, 0xc4, 0x20, //0x00002641 addq         $32, %r12
	0x48, 0x83, 0xc3, 0xe0, //0x00002645 addq         $-32, %rbx
	//0x00002649 LBB0_460
	0x4d, 0x85, 0xff, //0x00002649 testq        %r15, %r15
	0x0f, 0x85, 0xd2, 0x00, 0x00, 0x00, //0x0000264c jne          LBB0_471
	0x48, 0x8b, 0x75, 0xc0, //0x00002652 movq         $-64(%rbp), %rsi
	0x48, 0x85, 0xdb, //0x00002656 testq        %rbx, %rbx
	0x0f, 0x84, 0x53, 0x01, 0x00, 0x00, //0x00002659 je           LBB0_479
	//0x0000265f LBB0_462
	0x41, 0x0f, 0xb6, 0x0c, 0x24, //0x0000265f movzbl       (%r12), %ecx
	0x80, 0xf9, 0x22, //0x00002664 cmpb         $34, %cl
	0x0f, 0x84, 0x71, 0x00, 0x00, 0x00, //0x00002667 je           LBB0_468
	0x80, 0xf9, 0x5c, //0x0000266d cmpb         $92, %cl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00002670 je           LBB0_466
	0x80, 0xf9, 0x1f, //0x00002676 cmpb         $31, %cl
	0x0f, 0x86, 0x77, 0x03, 0x00, 0x00, //0x00002679 jbe          LBB0_515
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000267f movq         $-1, %rcx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00002686 movl         $1, %edx
	0x49, 0x01, 0xd4, //0x0000268b addq         %rdx, %r12
	0x48, 0x01, 0xcb, //0x0000268e addq         %rcx, %rbx
	0x0f, 0x85, 0xc8, 0xff, 0xff, 0xff, //0x00002691 jne          LBB0_462
	0xe9, 0x16, 0x01, 0x00, 0x00, //0x00002697 jmp          LBB0_479
	//0x0000269c LBB0_466
	0x48, 0x83, 0xfb, 0x01, //0x0000269c cmpq         $1, %rbx
	0x0f, 0x84, 0x58, 0x03, 0x00, 0x00, //0x000026a0 je           LBB0_516
	0x4c, 0x89, 0xe0, //0x000026a6 movq         %r12, %rax
	0x48, 0x29, 0xf0, //0x000026a9 subq         %rsi, %rax
	0x49, 0x83, 0xfb, 0xff, //0x000026ac cmpq         $-1, %r11
	0x4c, 0x0f, 0x44, 0xd8, //0x000026b0 cmoveq       %rax, %r11
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x000026b4 movq         $-2, %rcx
	0xba, 0x02, 0x00, 0x00, 0x00, //0x000026bb movl         $2, %edx
	0x4c, 0x8b, 0x55, 0xd0, //0x000026c0 movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x000026c4 movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000026c8 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x49, 0x01, 0xd4, //0x000026cd addq         %rdx, %r12
	0x48, 0x01, 0xcb, //0x000026d0 addq         %rcx, %rbx
	0x0f, 0x85, 0x86, 0xff, 0xff, 0xff, //0x000026d3 jne          LBB0_462
	0xe9, 0xd4, 0x00, 0x00, 0x00, //0x000026d9 jmp          LBB0_479
	//0x000026de LBB0_468
	0x49, 0x29, 0xf4, //0x000026de subq         %rsi, %r12
	0x49, 0xff, 0xc4, //0x000026e1 incq         %r12
	0xe9, 0x71, 0xf6, 0xff, 0xff, //0x000026e4 jmp          LBB0_323
	//0x000026e9 LBB0_469
	0x48, 0x85, 0xc9, //0x000026e9 testq        %rcx, %rcx
	0x0f, 0x84, 0x0c, 0x03, 0x00, 0x00, //0x000026ec je           LBB0_516
	0x4c, 0x89, 0xc8, //0x000026f2 movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x000026f5 notq         %rax
	0x48, 0x01, 0xd8, //0x000026f8 addq         %rbx, %rax
	0x49, 0x83, 0xfb, 0xff, //0x000026fb cmpq         $-1, %r11
	0x4c, 0x0f, 0x44, 0xd8, //0x000026ff cmoveq       %rax, %r11
	0x48, 0xff, 0xc3, //0x00002703 incq         %rbx
	0x48, 0xff, 0xc9, //0x00002706 decq         %rcx
	0x4c, 0x8b, 0x55, 0xd0, //0x00002709 movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x0000270d movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002711 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xc9, //0x00002716 testq        %rcx, %rcx
	0x0f, 0x85, 0x0a, 0xfe, 0xff, 0xff, //0x00002719 jne          LBB0_445
	0xe9, 0x8e, 0x00, 0x00, 0x00, //0x0000271f jmp          LBB0_479
	//0x00002724 LBB0_471
	0x48, 0x85, 0xdb, //0x00002724 testq        %rbx, %rbx
	0x0f, 0x84, 0xd1, 0x02, 0x00, 0x00, //0x00002727 je           LBB0_516
	0x48, 0x8b, 0x75, 0xc0, //0x0000272d movq         $-64(%rbp), %rsi
	0x48, 0x89, 0xf0, //0x00002731 movq         %rsi, %rax
	0x48, 0xf7, 0xd0, //0x00002734 notq         %rax
	0x4c, 0x01, 0xe0, //0x00002737 addq         %r12, %rax
	0x49, 0x83, 0xfb, 0xff, //0x0000273a cmpq         $-1, %r11
	0x4c, 0x0f, 0x44, 0xd8, //0x0000273e cmoveq       %rax, %r11
	0x49, 0xff, 0xc4, //0x00002742 incq         %r12
	0x48, 0xff, 0xcb, //0x00002745 decq         %rbx
	0x4c, 0x8b, 0x55, 0xd0, //0x00002748 movq         $-48(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x0000274c movq         $-56(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002750 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xdb, //0x00002755 testq        %rbx, %rbx
	0x0f, 0x85, 0x01, 0xff, 0xff, 0xff, //0x00002758 jne          LBB0_462
	0xe9, 0x4f, 0x00, 0x00, 0x00, //0x0000275e jmp          LBB0_479
	//0x00002763 LBB0_473
	0x4d, 0x89, 0x2a, //0x00002763 movq         %r13, (%r10)
	//0x00002766 LBB0_474
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00002766 movq         $-1, %rcx
	0xe9, 0x67, 0x00, 0x00, 0x00, //0x0000276d jmp          LBB0_501
	//0x00002772 LBB0_489
	0x48, 0xc7, 0xc1, 0xf9, 0xff, 0xff, 0xff, //0x00002772 movq         $-7, %rcx
	0xe9, 0x5b, 0x00, 0x00, 0x00, //0x00002779 jmp          LBB0_501
	//0x0000277e LBB0_475
	0x49, 0x83, 0xfc, 0xff, //0x0000277e cmpq         $-1, %r12
	0x0f, 0x85, 0x91, 0x00, 0x00, 0x00, //0x00002782 jne          LBB0_487
	//0x00002788 LBB0_476
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00002788 movq         $-1, %r12
	0x4c, 0x8b, 0x45, 0xb8, //0x0000278f movq         $-72(%rbp), %r8
	0xe9, 0x81, 0x00, 0x00, 0x00, //0x00002793 jmp          LBB0_487
	//0x00002798 LBB0_477
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00002798 movq         $-1, %rcx
	0x48, 0x8b, 0x55, 0xd0, //0x0000279f movq         $-48(%rbp), %rdx
	0xe9, 0x1d, 0x00, 0x00, 0x00, //0x000027a3 jmp          LBB0_481
	//0x000027a8 LBB0_478
	0x49, 0x83, 0xfc, 0xff, //0x000027a8 cmpq         $-1, %r12
	0x0f, 0x85, 0x91, 0x00, 0x00, 0x00, //0x000027ac jne          LBB0_506
	//0x000027b2 LBB0_479
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x000027b2 movq         $-1, %r12
	0x4c, 0x8b, 0x5d, 0xb8, //0x000027b9 movq         $-72(%rbp), %r11
	0xe9, 0x81, 0x00, 0x00, 0x00, //0x000027bd jmp          LBB0_506
	//0x000027c2 LBB0_480
	0x4c, 0x89, 0xf1, //0x000027c2 movq         %r14, %rcx
	//0x000027c5 LBB0_481
	0x48, 0x8b, 0x02, //0x000027c5 movq         (%rdx), %rax
	0x48, 0x29, 0xc8, //0x000027c8 subq         %rcx, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x000027cb addq         $-2, %rax
	0x48, 0x89, 0x02, //0x000027cf movq         %rax, (%rdx)
	//0x000027d2 LBB0_500
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x000027d2 movq         $-2, %rcx
	//0x000027d9 LBB0_501
	0x48, 0x89, 0xc8, //0x000027d9 movq         %rcx, %rax
	0x48, 0x83, 0xc4, 0x48, //0x000027dc addq         $72, %rsp
	0x5b, //0x000027e0 popq         %rbx
	0x41, 0x5c, //0x000027e1 popq         %r12
	0x41, 0x5d, //0x000027e3 popq         %r13
	0x41, 0x5e, //0x000027e5 popq         %r14
	0x41, 0x5f, //0x000027e7 popq         %r15
	0x5d, //0x000027e9 popq         %rbp
	0xc5, 0xf8, 0x77, //0x000027ea vzeroupper   
	0xc3, //0x000027ed retq         
	//0x000027ee LBB0_490
	0x49, 0x89, 0x12, //0x000027ee movq         %rdx, (%r10)
	0xe9, 0xe3, 0xff, 0xff, 0xff, //0x000027f1 jmp          LBB0_501
	//0x000027f6 LBB0_482
	0x49, 0x83, 0xf8, 0xff, //0x000027f6 cmpq         $-1, %r8
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x000027fa jne          LBB0_485
	0x48, 0x0f, 0xbc, 0xc6, //0x00002800 bsfq         %rsi, %rax
	0x4c, 0x2b, 0x65, 0xc0, //0x00002804 subq         $-64(%rbp), %r12
	0x49, 0x01, 0xc4, //0x00002808 addq         %rax, %r12
	//0x0000280b LBB0_484
	0x4d, 0x89, 0xe0, //0x0000280b movq         %r12, %r8
	//0x0000280e LBB0_485
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x0000280e movq         $-2, %r12
	//0x00002815 LBB0_486
	0x4c, 0x8b, 0x55, 0xd0, //0x00002815 movq         $-48(%rbp), %r10
	//0x00002819 LBB0_487
	0x4d, 0x89, 0x02, //0x00002819 movq         %r8, (%r10)
	0x4c, 0x89, 0xe1, //0x0000281c movq         %r12, %rcx
	0xe9, 0xb5, 0xff, 0xff, 0xff, //0x0000281f jmp          LBB0_501
	//0x00002824 LBB0_502
	0x49, 0x83, 0xfb, 0xff, //0x00002824 cmpq         $-1, %r11
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x00002828 jne          LBB0_505
	0x48, 0x0f, 0xbc, 0xc6, //0x0000282e bsfq         %rsi, %rax
	0x4c, 0x2b, 0x65, 0xc0, //0x00002832 subq         $-64(%rbp), %r12
	0x49, 0x01, 0xc4, //0x00002836 addq         %rax, %r12
	//0x00002839 LBB0_504
	0x4d, 0x89, 0xe3, //0x00002839 movq         %r12, %r11
	//0x0000283c LBB0_505
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x0000283c movq         $-2, %r12
	//0x00002843 LBB0_506
	0x4d, 0x89, 0x1a, //0x00002843 movq         %r11, (%r10)
	0x4c, 0x89, 0xe1, //0x00002846 movq         %r12, %rcx
	0xe9, 0x8b, 0xff, 0xff, 0xff, //0x00002849 jmp          LBB0_501
	//0x0000284e LBB0_233
	0x4d, 0x89, 0x2a, //0x0000284e movq         %r13, (%r10)
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00002851 movq         $-2, %rcx
	0x41, 0x80, 0x3f, 0x6e, //0x00002858 cmpb         $110, (%r15)
	0x0f, 0x85, 0x77, 0xff, 0xff, 0xff, //0x0000285c jne          LBB0_501
	0x49, 0x8d, 0x45, 0x01, //0x00002862 leaq         $1(%r13), %rax
	0x49, 0x89, 0x02, //0x00002866 movq         %rax, (%r10)
	0x43, 0x80, 0x7c, 0x29, 0x01, 0x75, //0x00002869 cmpb         $117, $1(%r9,%r13)
	0x0f, 0x85, 0x64, 0xff, 0xff, 0xff, //0x0000286f jne          LBB0_501
	0x49, 0x8d, 0x45, 0x02, //0x00002875 leaq         $2(%r13), %rax
	0x49, 0x89, 0x02, //0x00002879 movq         %rax, (%r10)
	0x43, 0x80, 0x7c, 0x29, 0x02, 0x6c, //0x0000287c cmpb         $108, $2(%r9,%r13)
	0x0f, 0x85, 0x51, 0xff, 0xff, 0xff, //0x00002882 jne          LBB0_501
	0x49, 0x8d, 0x45, 0x03, //0x00002888 leaq         $3(%r13), %rax
	0x49, 0x89, 0x02, //0x0000288c movq         %rax, (%r10)
	0x43, 0x80, 0x7c, 0x29, 0x03, 0x6c, //0x0000288f cmpb         $108, $3(%r9,%r13)
	0x0f, 0x85, 0x3e, 0xff, 0xff, 0xff, //0x00002895 jne          LBB0_501
	0xe9, 0xa2, 0x00, 0x00, 0x00, //0x0000289b jmp          LBB0_237
	//0x000028a0 LBB0_491
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x000028a0 movq         $-2, %rcx
	0x80, 0xfa, 0x61, //0x000028a7 cmpb         $97, %dl
	0x0f, 0x85, 0x29, 0xff, 0xff, 0xff, //0x000028aa jne          LBB0_501
	0x49, 0x8d, 0x45, 0x02, //0x000028b0 leaq         $2(%r13), %rax
	0x49, 0x89, 0x02, //0x000028b4 movq         %rax, (%r10)
	0x43, 0x80, 0x7c, 0x29, 0x02, 0x6c, //0x000028b7 cmpb         $108, $2(%r9,%r13)
	0x0f, 0x85, 0x16, 0xff, 0xff, 0xff, //0x000028bd jne          LBB0_501
	0x49, 0x8d, 0x45, 0x03, //0x000028c3 leaq         $3(%r13), %rax
	0x49, 0x89, 0x02, //0x000028c7 movq         %rax, (%r10)
	0x43, 0x80, 0x7c, 0x29, 0x03, 0x73, //0x000028ca cmpb         $115, $3(%r9,%r13)
	0x0f, 0x85, 0x03, 0xff, 0xff, 0xff, //0x000028d0 jne          LBB0_501
	0x49, 0x8d, 0x45, 0x04, //0x000028d6 leaq         $4(%r13), %rax
	0x49, 0x89, 0x02, //0x000028da movq         %rax, (%r10)
	0x43, 0x80, 0x7c, 0x29, 0x04, 0x65, //0x000028dd cmpb         $101, $4(%r9,%r13)
	0x0f, 0x85, 0xf0, 0xfe, 0xff, 0xff, //0x000028e3 jne          LBB0_501
	0x49, 0x83, 0xc5, 0x05, //0x000028e9 addq         $5, %r13
	0x4d, 0x89, 0x2a, //0x000028ed movq         %r13, (%r10)
	0xe9, 0xe4, 0xfe, 0xff, 0xff, //0x000028f0 jmp          LBB0_501
	//0x000028f5 LBB0_245
	0x4d, 0x89, 0x2a, //0x000028f5 movq         %r13, (%r10)
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x000028f8 movq         $-2, %rcx
	0x41, 0x80, 0x3f, 0x74, //0x000028ff cmpb         $116, (%r15)
	0x0f, 0x85, 0xd0, 0xfe, 0xff, 0xff, //0x00002903 jne          LBB0_501
	0x49, 0x8d, 0x45, 0x01, //0x00002909 leaq         $1(%r13), %rax
	0x49, 0x89, 0x02, //0x0000290d movq         %rax, (%r10)
	0x43, 0x80, 0x7c, 0x29, 0x01, 0x72, //0x00002910 cmpb         $114, $1(%r9,%r13)
	0x0f, 0x85, 0xbd, 0xfe, 0xff, 0xff, //0x00002916 jne          LBB0_501
	0x49, 0x8d, 0x45, 0x02, //0x0000291c leaq         $2(%r13), %rax
	0x49, 0x89, 0x02, //0x00002920 movq         %rax, (%r10)
	0x43, 0x80, 0x7c, 0x29, 0x02, 0x75, //0x00002923 cmpb         $117, $2(%r9,%r13)
	0x0f, 0x85, 0xaa, 0xfe, 0xff, 0xff, //0x00002929 jne          LBB0_501
	0x49, 0x8d, 0x45, 0x03, //0x0000292f leaq         $3(%r13), %rax
	0x49, 0x89, 0x02, //0x00002933 movq         %rax, (%r10)
	0x43, 0x80, 0x7c, 0x29, 0x03, 0x65, //0x00002936 cmpb         $101, $3(%r9,%r13)
	0x0f, 0x85, 0x97, 0xfe, 0xff, 0xff, //0x0000293c jne          LBB0_501
	//0x00002942 LBB0_237
	0x49, 0x83, 0xc5, 0x04, //0x00002942 addq         $4, %r13
	0x4d, 0x89, 0x2a, //0x00002946 movq         %r13, (%r10)
	0xe9, 0x8b, 0xfe, 0xff, 0xff, //0x00002949 jmp          LBB0_501
	//0x0000294e LBB0_497
	0x4d, 0x8b, 0x22, //0x0000294e movq         (%r10), %r12
	//0x00002951 LBB0_498
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00002951 movq         $-1, %r15
	//0x00002958 LBB0_499
	0x49, 0xf7, 0xd7, //0x00002958 notq         %r15
	0x4d, 0x01, 0xe7, //0x0000295b addq         %r12, %r15
	0x4d, 0x89, 0x3a, //0x0000295e movq         %r15, (%r10)
	0xe9, 0x6c, 0xfe, 0xff, 0xff, //0x00002961 jmp          LBB0_500
	//0x00002966 LBB0_510
	0x4c, 0x89, 0x65, 0xb8, //0x00002966 movq         %r12, $-72(%rbp)
	0xe9, 0x43, 0xfe, 0xff, 0xff, //0x0000296a jmp          LBB0_479
	//0x0000296f LBB0_507
	0x4c, 0x89, 0x65, 0xb8, //0x0000296f movq         %r12, $-72(%rbp)
	0xe9, 0x10, 0xfe, 0xff, 0xff, //0x00002973 jmp          LBB0_476
	//0x00002978 LBB0_508
	0x4c, 0x89, 0x65, 0xb8, //0x00002978 movq         %r12, $-72(%rbp)
	0x4c, 0x8b, 0x55, 0xd0, //0x0000297c movq         $-48(%rbp), %r10
	0xe9, 0x03, 0xfe, 0xff, 0xff, //0x00002980 jmp          LBB0_476
	//0x00002985 LBB0_509
	0x4c, 0x01, 0xe2, //0x00002985 addq         %r12, %rdx
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x00002988 movq         $-2, %r12
	0x49, 0x89, 0xd0, //0x0000298f movq         %rdx, %r8
	0xe9, 0x82, 0xfe, 0xff, 0xff, //0x00002992 jmp          LBB0_487
	//0x00002997 LBB0_433
	0x4c, 0x8b, 0x55, 0xd0, //0x00002997 movq         $-48(%rbp), %r10
	0xe9, 0xe8, 0xfd, 0xff, 0xff, //0x0000299b jmp          LBB0_476
	//0x000029a0 LBB0_129
	0x4c, 0x01, 0xe2, //0x000029a0 addq         %r12, %rdx
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x000029a3 movq         $-2, %r12
	0x49, 0x89, 0xd3, //0x000029aa movq         %rdx, %r11
	0xe9, 0x91, 0xfe, 0xff, 0xff, //0x000029ad jmp          LBB0_506
	//0x000029b2 LBB0_511
	0x4c, 0x01, 0xe7, //0x000029b2 addq         %r12, %rdi
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x000029b5 movq         $-2, %r12
	0x49, 0x89, 0xf8, //0x000029bc movq         %rdi, %r8
	0xe9, 0x51, 0xfe, 0xff, 0xff, //0x000029bf jmp          LBB0_486
	//0x000029c4 LBB0_512
	0x4c, 0x2b, 0x65, 0xc0, //0x000029c4 subq         $-64(%rbp), %r12
	0x49, 0x01, 0xd4, //0x000029c8 addq         %rdx, %r12
	0xe9, 0x3b, 0xfe, 0xff, 0xff, //0x000029cb jmp          LBB0_484
	//0x000029d0 LBB0_417
	0x4c, 0x01, 0xe7, //0x000029d0 addq         %r12, %rdi
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x000029d3 movq         $-2, %r12
	0x49, 0x89, 0xfb, //0x000029da movq         %rdi, %r11
	0xe9, 0x61, 0xfe, 0xff, 0xff, //0x000029dd jmp          LBB0_506
	//0x000029e2 LBB0_513
	0x49, 0x29, 0xc4, //0x000029e2 subq         %rax, %r12
	0xe9, 0x21, 0xfe, 0xff, 0xff, //0x000029e5 jmp          LBB0_484
	//0x000029ea LBB0_514
	0x4c, 0x2b, 0x65, 0xc0, //0x000029ea subq         $-64(%rbp), %r12
	0x49, 0x01, 0xd4, //0x000029ee addq         %rdx, %r12
	0xe9, 0x43, 0xfe, 0xff, 0xff, //0x000029f1 jmp          LBB0_504
	//0x000029f6 LBB0_515
	0x49, 0x29, 0xf4, //0x000029f6 subq         %rsi, %r12
	0xe9, 0x3b, 0xfe, 0xff, 0xff, //0x000029f9 jmp          LBB0_504
	//0x000029fe LBB0_516
	0x4c, 0x8b, 0x55, 0xd0, //0x000029fe movq         $-48(%rbp), %r10
	0xe9, 0xab, 0xfd, 0xff, 0xff, //0x00002a02 jmp          LBB0_479
	0x90, //0x00002a07 .p2align 2, 0x90
	// // .set L0_0_set_39, LBB0_39-LJTI0_0
	// // .set L0_0_set_63, LBB0_63-LJTI0_0
	// // .set L0_0_set_44, LBB0_44-LJTI0_0
	// // .set L0_0_set_61, LBB0_61-LJTI0_0
	// // .set L0_0_set_42, LBB0_42-LJTI0_0
	// // .set L0_0_set_65, LBB0_65-LJTI0_0
	//0x00002a08 LJTI0_0
	0xc4, 0xda, 0xff, 0xff, //0x00002a08 .long L0_0_set_39
	0x6c, 0xdc, 0xff, 0xff, //0x00002a0c .long L0_0_set_63
	0xfb, 0xda, 0xff, 0xff, //0x00002a10 .long L0_0_set_44
	0x55, 0xdc, 0xff, 0xff, //0x00002a14 .long L0_0_set_61
	0xdb, 0xda, 0xff, 0xff, //0x00002a18 .long L0_0_set_42
	0x97, 0xdc, 0xff, 0xff, //0x00002a1c .long L0_0_set_65
	// // .set L0_1_set_501, LBB0_501-LJTI0_1
	// // .set L0_1_set_500, LBB0_500-LJTI0_1
	// // .set L0_1_set_211, LBB0_211-LJTI0_1
	// // .set L0_1_set_227, LBB0_227-LJTI0_1
	// // .set L0_1_set_69, LBB0_69-LJTI0_1
	// // .set L0_1_set_209, LBB0_209-LJTI0_1
	// // .set L0_1_set_240, LBB0_240-LJTI0_1
	// // .set L0_1_set_231, LBB0_231-LJTI0_1
	// // .set L0_1_set_243, LBB0_243-LJTI0_1
	// // .set L0_1_set_238, LBB0_238-LJTI0_1
	//0x00002a20 LJTI0_1
	0xb9, 0xfd, 0xff, 0xff, //0x00002a20 .long L0_1_set_501
	0xb2, 0xfd, 0xff, 0xff, //0x00002a24 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a28 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a2c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a30 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a34 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a38 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a3c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a40 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a44 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a48 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a4c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a50 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a54 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a58 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a5c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a60 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a64 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a68 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a6c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a70 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a74 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a78 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a7c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a80 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a84 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a88 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a8c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a90 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a94 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a98 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002a9c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002aa0 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002aa4 .long L0_1_set_500
	0x1e, 0xe7, 0xff, 0xff, //0x00002aa8 .long L0_1_set_211
	0xb2, 0xfd, 0xff, 0xff, //0x00002aac .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002ab0 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002ab4 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002ab8 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002abc .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002ac0 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002ac4 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002ac8 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002acc .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002ad0 .long L0_1_set_500
	0x6d, 0xe8, 0xff, 0xff, //0x00002ad4 .long L0_1_set_227
	0xb2, 0xfd, 0xff, 0xff, //0x00002ad8 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002adc .long L0_1_set_500
	0xcb, 0xdc, 0xff, 0xff, //0x00002ae0 .long L0_1_set_69
	0xcb, 0xdc, 0xff, 0xff, //0x00002ae4 .long L0_1_set_69
	0xcb, 0xdc, 0xff, 0xff, //0x00002ae8 .long L0_1_set_69
	0xcb, 0xdc, 0xff, 0xff, //0x00002aec .long L0_1_set_69
	0xcb, 0xdc, 0xff, 0xff, //0x00002af0 .long L0_1_set_69
	0xcb, 0xdc, 0xff, 0xff, //0x00002af4 .long L0_1_set_69
	0xcb, 0xdc, 0xff, 0xff, //0x00002af8 .long L0_1_set_69
	0xcb, 0xdc, 0xff, 0xff, //0x00002afc .long L0_1_set_69
	0xcb, 0xdc, 0xff, 0xff, //0x00002b00 .long L0_1_set_69
	0xcb, 0xdc, 0xff, 0xff, //0x00002b04 .long L0_1_set_69
	0xb2, 0xfd, 0xff, 0xff, //0x00002b08 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b0c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b10 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b14 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b18 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b1c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b20 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b24 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b28 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b2c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b30 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b34 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b38 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b3c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b40 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b44 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b48 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b4c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b50 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b54 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b58 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b5c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b60 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b64 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b68 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b6c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b70 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b74 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b78 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b7c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b80 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b84 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b88 .long L0_1_set_500
	0xfa, 0xe6, 0xff, 0xff, //0x00002b8c .long L0_1_set_209
	0xb2, 0xfd, 0xff, 0xff, //0x00002b90 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b94 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b98 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002b9c .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002ba0 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002ba4 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002ba8 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bac .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bb0 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bb4 .long L0_1_set_500
	0xf8, 0xe8, 0xff, 0xff, //0x00002bb8 .long L0_1_set_240
	0xb2, 0xfd, 0xff, 0xff, //0x00002bbc .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bc0 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bc4 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bc8 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bcc .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bd0 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bd4 .long L0_1_set_500
	0xad, 0xe8, 0xff, 0xff, //0x00002bd8 .long L0_1_set_231
	0xb2, 0xfd, 0xff, 0xff, //0x00002bdc .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002be0 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002be4 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002be8 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bec .long L0_1_set_500
	0x38, 0xe9, 0xff, 0xff, //0x00002bf0 .long L0_1_set_243
	0xb2, 0xfd, 0xff, 0xff, //0x00002bf4 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bf8 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002bfc .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002c00 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002c04 .long L0_1_set_500
	0xb2, 0xfd, 0xff, 0xff, //0x00002c08 .long L0_1_set_500
	0xd4, 0xe8, 0xff, 0xff, //0x00002c0c .long L0_1_set_238
	// // .set L0_2_set_311, LBB0_311-LJTI0_2
	// // .set L0_2_set_326, LBB0_326-LJTI0_2
	// // .set L0_2_set_318, LBB0_318-LJTI0_2
	// // .set L0_2_set_313, LBB0_313-LJTI0_2
	// // .set L0_2_set_316, LBB0_316-LJTI0_2
	//0x00002c10 LJTI0_2
	0x77, 0xec, 0xff, 0xff, //0x00002c10 .long L0_2_set_311
	0x54, 0xed, 0xff, 0xff, //0x00002c14 .long L0_2_set_326
	0x77, 0xec, 0xff, 0xff, //0x00002c18 .long L0_2_set_311
	0xdb, 0xec, 0xff, 0xff, //0x00002c1c .long L0_2_set_318
	0x54, 0xed, 0xff, 0xff, //0x00002c20 .long L0_2_set_326
	0x90, 0xec, 0xff, 0xff, //0x00002c24 .long L0_2_set_313
	0x90, 0xec, 0xff, 0xff, //0x00002c28 .long L0_2_set_313
	0x90, 0xec, 0xff, 0xff, //0x00002c2c .long L0_2_set_313
	0x90, 0xec, 0xff, 0xff, //0x00002c30 .long L0_2_set_313
	0x90, 0xec, 0xff, 0xff, //0x00002c34 .long L0_2_set_313
	0x90, 0xec, 0xff, 0xff, //0x00002c38 .long L0_2_set_313
	0x90, 0xec, 0xff, 0xff, //0x00002c3c .long L0_2_set_313
	0x90, 0xec, 0xff, 0xff, //0x00002c40 .long L0_2_set_313
	0x90, 0xec, 0xff, 0xff, //0x00002c44 .long L0_2_set_313
	0x90, 0xec, 0xff, 0xff, //0x00002c48 .long L0_2_set_313
	0x54, 0xed, 0xff, 0xff, //0x00002c4c .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c50 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c54 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c58 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c5c .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c60 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c64 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c68 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c6c .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c70 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c74 .long L0_2_set_326
	0xc0, 0xec, 0xff, 0xff, //0x00002c78 .long L0_2_set_316
	0x54, 0xed, 0xff, 0xff, //0x00002c7c .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c80 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c84 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c88 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c8c .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c90 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c94 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c98 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002c9c .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002ca0 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002ca4 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002ca8 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cac .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cb0 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cb4 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cb8 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cbc .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cc0 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cc4 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cc8 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002ccc .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cd0 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cd4 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cd8 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cdc .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002ce0 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002ce4 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002ce8 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cec .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cf0 .long L0_2_set_326
	0x54, 0xed, 0xff, 0xff, //0x00002cf4 .long L0_2_set_326
	0xc0, 0xec, 0xff, 0xff, //0x00002cf8 .long L0_2_set_316
	// // .set L0_3_set_171, LBB0_171-LJTI0_3
	// // .set L0_3_set_187, LBB0_187-LJTI0_3
	// // .set L0_3_set_178, LBB0_178-LJTI0_3
	// // .set L0_3_set_173, LBB0_173-LJTI0_3
	// // .set L0_3_set_176, LBB0_176-LJTI0_3
	//0x00002cfc LJTI0_3
	0x56, 0xdd, 0xff, 0xff, //0x00002cfc .long L0_3_set_171
	0xb1, 0xe2, 0xff, 0xff, //0x00002d00 .long L0_3_set_187
	0x56, 0xdd, 0xff, 0xff, //0x00002d04 .long L0_3_set_171
	0xbf, 0xdd, 0xff, 0xff, //0x00002d08 .long L0_3_set_178
	0xb1, 0xe2, 0xff, 0xff, //0x00002d0c .long L0_3_set_187
	0x74, 0xdd, 0xff, 0xff, //0x00002d10 .long L0_3_set_173
	0x74, 0xdd, 0xff, 0xff, //0x00002d14 .long L0_3_set_173
	0x74, 0xdd, 0xff, 0xff, //0x00002d18 .long L0_3_set_173
	0x74, 0xdd, 0xff, 0xff, //0x00002d1c .long L0_3_set_173
	0x74, 0xdd, 0xff, 0xff, //0x00002d20 .long L0_3_set_173
	0x74, 0xdd, 0xff, 0xff, //0x00002d24 .long L0_3_set_173
	0x74, 0xdd, 0xff, 0xff, //0x00002d28 .long L0_3_set_173
	0x74, 0xdd, 0xff, 0xff, //0x00002d2c .long L0_3_set_173
	0x74, 0xdd, 0xff, 0xff, //0x00002d30 .long L0_3_set_173
	0x74, 0xdd, 0xff, 0xff, //0x00002d34 .long L0_3_set_173
	0xb1, 0xe2, 0xff, 0xff, //0x00002d38 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d3c .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d40 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d44 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d48 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d4c .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d50 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d54 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d58 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d5c .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d60 .long L0_3_set_187
	0xa4, 0xdd, 0xff, 0xff, //0x00002d64 .long L0_3_set_176
	0xb1, 0xe2, 0xff, 0xff, //0x00002d68 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d6c .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d70 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d74 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d78 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d7c .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d80 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d84 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d88 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d8c .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d90 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d94 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d98 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002d9c .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002da0 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002da4 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002da8 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002dac .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002db0 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002db4 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002db8 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002dbc .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002dc0 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002dc4 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002dc8 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002dcc .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002dd0 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002dd4 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002dd8 .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002ddc .long L0_3_set_187
	0xb1, 0xe2, 0xff, 0xff, //0x00002de0 .long L0_3_set_187
	0xa4, 0xdd, 0xff, 0xff, //0x00002de4 .long L0_3_set_176
	//0x00002de8 .p2align 2, 0x00
	//0x00002de8 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00002de8 .long 2
}
 
