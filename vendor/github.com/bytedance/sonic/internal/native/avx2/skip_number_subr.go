// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx2

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__skip_number = 336
)

const (
    _stack__skip_number = 72
)

const (
    _size__skip_number = 1528
)

var (
    _pcsp__skip_number = [][2]uint32{
        {1, 0},
        {4, 8},
        {6, 16},
        {8, 24},
        {10, 32},
        {12, 40},
        {13, 48},
        {1441, 72},
        {1445, 48},
        {1446, 40},
        {1448, 32},
        {1450, 24},
        {1452, 16},
        {1454, 8},
        {1458, 0},
        {1528, 72},
    }
)

var _cfunc_skip_number = []loader.CFunc{
    {"_skip_number_entry", 0,  _entry__skip_number, 0, nil},
    {"_skip_number", _entry__skip_number, _size__skip_number, _stack__skip_number, _pcsp__skip_number},
}
