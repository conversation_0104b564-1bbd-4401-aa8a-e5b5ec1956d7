// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_skip_one = []byte{
	// .p2align 5, 0x00
	// LCPI0_0
	0x20, // .byte 32
	0x00, //0x00000001 .byte 0
	0x00, //0x00000002 .byte 0
	0x00, //0x00000003 .byte 0
	0x00, //0x00000004 .byte 0
	0x00, //0x00000005 .byte 0
	0x00, //0x00000006 .byte 0
	0x00, //0x00000007 .byte 0
	0x00, //0x00000008 .byte 0
	0x09, //0x00000009 .byte 9
	0x0a, //0x0000000a .byte 10
	0x00, //0x0000000b .byte 0
	0x00, //0x0000000c .byte 0
	0x0d, //0x0000000d .byte 13
	0x00, //0x0000000e .byte 0
	0x00, //0x0000000f .byte 0
	0x20, //0x00000010 .byte 32
	0x00, //0x00000011 .byte 0
	0x00, //0x00000012 .byte 0
	0x00, //0x00000013 .byte 0
	0x00, //0x00000014 .byte 0
	0x00, //0x00000015 .byte 0
	0x00, //0x00000016 .byte 0
	0x00, //0x00000017 .byte 0
	0x00, //0x00000018 .byte 0
	0x09, //0x00000019 .byte 9
	0x0a, //0x0000001a .byte 10
	0x00, //0x0000001b .byte 0
	0x00, //0x0000001c .byte 0
	0x0d, //0x0000001d .byte 13
	0x00, //0x0000001e .byte 0
	0x00, //0x0000001f .byte 0
	//0x00000020 LCPI0_1
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000020 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000030 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x00000040 LCPI0_2
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000040 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000050 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000060 LCPI0_3
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000060 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000070 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000080 LCPI0_4
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000080 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000090 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	//0x000000a0 LCPI0_5
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x000000a0 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x000000b0 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	//0x000000c0 LCPI0_6
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x000000c0 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x000000d0 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x000000e0 LCPI0_7
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x000000e0 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x000000f0 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000100 LCPI0_8
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000100 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000110 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x00000120 LCPI0_9
	0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, //0x00000120 QUAD $0x6565656565656565; QUAD $0x6565656565656565  // .space 16, 'eeeeeeeeeeeeeeee'
	0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, //0x00000130 QUAD $0x6565656565656565; QUAD $0x6565656565656565  // .space 16, 'eeeeeeeeeeeeeeee'
	//0x00000140 .p2align 4, 0x00
	//0x00000140 LCPI0_10
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000140 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	//0x00000150 LCPI0_11
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x00000150 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	//0x00000160 LCPI0_12
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000160 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x00000170 LCPI0_13
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000170 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000180 LCPI0_14
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000180 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000190 LCPI0_15
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000190 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x000001a0 LCPI0_16
	0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, //0x000001a0 QUAD $0x6565656565656565; QUAD $0x6565656565656565  // .space 16, 'eeeeeeeeeeeeeeee'
	//0x000001b0 .p2align 4, 0x90
	//0x000001b0 _skip_one
	0x55, //0x000001b0 pushq        %rbp
	0x48, 0x89, 0xe5, //0x000001b1 movq         %rsp, %rbp
	0x41, 0x57, //0x000001b4 pushq        %r15
	0x41, 0x56, //0x000001b6 pushq        %r14
	0x41, 0x55, //0x000001b8 pushq        %r13
	0x41, 0x54, //0x000001ba pushq        %r12
	0x53, //0x000001bc pushq        %rbx
	0x48, 0x83, 0xec, 0x48, //0x000001bd subq         $72, %rsp
	0x48, 0x89, 0x4d, 0x98, //0x000001c1 movq         %rcx, $-104(%rbp)
	0x49, 0x89, 0xd2, //0x000001c5 movq         %rdx, %r10
	0x49, 0x89, 0xf5, //0x000001c8 movq         %rsi, %r13
	0x49, 0x89, 0xfe, //0x000001cb movq         %rdi, %r14
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x000001ce movl         $1, %ebx
	0xc4, 0xe1, 0xf9, 0x6e, 0xc3, //0x000001d3 vmovq        %rbx, %xmm0
	0xc5, 0xfa, 0x7f, 0x02, //0x000001d8 vmovdqu      %xmm0, (%rdx)
	0x4c, 0x8b, 0x26, //0x000001dc movq         (%rsi), %r12
	0x48, 0xc7, 0x45, 0x90, 0xff, 0xff, 0xff, 0xff, //0x000001df movq         $-1, $-112(%rbp)
	0xc5, 0xfe, 0x6f, 0x2d, 0x11, 0xfe, 0xff, 0xff, //0x000001e7 vmovdqu      $-495(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x29, 0xfe, 0xff, 0xff, //0x000001ef vmovdqu      $-471(%rip), %ymm6  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x41, 0xfe, 0xff, 0xff, //0x000001f7 vmovdqu      $-447(%rip), %ymm7  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x59, 0xfe, 0xff, 0xff, //0x000001ff vmovdqu      $-423(%rip), %ymm8  /* LCPI0_3+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00000207 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x15, 0x6c, 0xfe, 0xff, 0xff, //0x0000020c vmovdqu      $-404(%rip), %ymm10  /* LCPI0_4+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x84, 0xfe, 0xff, 0xff, //0x00000214 vmovdqu      $-380(%rip), %ymm11  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x9c, 0xfe, 0xff, 0xff, //0x0000021c vmovdqu      $-356(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0xb4, 0xfe, 0xff, 0xff, //0x00000224 vmovdqu      $-332(%rip), %ymm13  /* LCPI0_7+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0xcc, 0xfe, 0xff, 0xff, //0x0000022c vmovdqu      $-308(%rip), %ymm14  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0xe4, 0xfe, 0xff, 0xff, //0x00000234 vmovdqu      $-284(%rip), %ymm15  /* LCPI0_9+0(%rip) */
	0x48, 0x89, 0x75, 0xc0, //0x0000023c movq         %rsi, $-64(%rbp)
	0x48, 0x89, 0x55, 0xb8, //0x00000240 movq         %rdx, $-72(%rbp)
	0x48, 0x89, 0x7d, 0xd0, //0x00000244 movq         %rdi, $-48(%rbp)
	0xe9, 0x63, 0x00, 0x00, 0x00, //0x00000248 jmp          LBB0_5
	//0x0000024d LBB0_72
	0x4c, 0x89, 0xe1, //0x0000024d movq         %r12, %rcx
	//0x00000250 LBB0_199
	0x4c, 0x89, 0xe0, //0x00000250 movq         %r12, %rax
	0x4e, 0x8d, 0x64, 0x31, 0xff, //0x00000253 leaq         $-1(%rcx,%r14), %r12
	0x4d, 0x89, 0x65, 0x00, //0x00000258 movq         %r12, (%r13)
	0x4c, 0x89, 0xc1, //0x0000025c movq         %r8, %rcx
	0x48, 0x85, 0xc0, //0x0000025f testq        %rax, %rax
	0x4c, 0x8b, 0x55, 0xb8, //0x00000262 movq         $-72(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xd0, //0x00000266 movq         $-48(%rbp), %r14
	0x0f, 0x8e, 0x49, 0x25, 0x00, 0x00, //0x0000026a jle          LBB0_498
	//0x00000270 .p2align 4, 0x90
	//0x00000270 LBB0_3
	0x49, 0x8b, 0x12, //0x00000270 movq         (%r10), %rdx
	0x48, 0x89, 0xd3, //0x00000273 movq         %rdx, %rbx
	0x48, 0x8b, 0x4d, 0x90, //0x00000276 movq         $-112(%rbp), %rcx
	0x48, 0x85, 0xd2, //0x0000027a testq        %rdx, %rdx
	0x0f, 0x85, 0x2d, 0x00, 0x00, 0x00, //0x0000027d jne          LBB0_5
	0xe9, 0x31, 0x25, 0x00, 0x00, //0x00000283 jmp          LBB0_498
	//0x00000288 LBB0_1
	0x4c, 0x89, 0xe0, //0x00000288 movq         %r12, %rax
	0x4d, 0x8d, 0x60, 0x04, //0x0000028b leaq         $4(%r8), %r12
	0x4d, 0x89, 0x65, 0x00, //0x0000028f movq         %r12, (%r13)
	0x4c, 0x89, 0xc1, //0x00000293 movq         %r8, %rcx
	0x48, 0x85, 0xc0, //0x00000296 testq        %rax, %rax
	0x0f, 0x8f, 0xd1, 0xff, 0xff, 0xff, //0x00000299 jg           LBB0_3
	0xe9, 0x15, 0x25, 0x00, 0x00, //0x0000029f jmp          LBB0_498
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000002a4 .p2align 4, 0x90
	//0x000002b0 LBB0_5
	0x4d, 0x8b, 0x0e, //0x000002b0 movq         (%r14), %r9
	0x49, 0x8b, 0x4e, 0x08, //0x000002b3 movq         $8(%r14), %rcx
	0x49, 0x39, 0xcc, //0x000002b7 cmpq         %rcx, %r12
	0x0f, 0x83, 0x30, 0x00, 0x00, 0x00, //0x000002ba jae          LBB0_10
	0x43, 0x8a, 0x04, 0x21, //0x000002c0 movb         (%r9,%r12), %al
	0x3c, 0x0d, //0x000002c4 cmpb         $13, %al
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x000002c6 je           LBB0_10
	0x3c, 0x20, //0x000002cc cmpb         $32, %al
	0x0f, 0x84, 0x1c, 0x00, 0x00, 0x00, //0x000002ce je           LBB0_10
	0x04, 0xf7, //0x000002d4 addb         $-9, %al
	0x3c, 0x01, //0x000002d6 cmpb         $1, %al
	0x0f, 0x86, 0x12, 0x00, 0x00, 0x00, //0x000002d8 jbe          LBB0_10
	0x4d, 0x89, 0xe0, //0x000002de movq         %r12, %r8
	0xe9, 0x82, 0x01, 0x00, 0x00, //0x000002e1 jmp          LBB0_36
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000002e6 .p2align 4, 0x90
	//0x000002f0 LBB0_10
	0x4d, 0x8d, 0x44, 0x24, 0x01, //0x000002f0 leaq         $1(%r12), %r8
	0x49, 0x39, 0xc8, //0x000002f5 cmpq         %rcx, %r8
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x000002f8 jae          LBB0_14
	0x43, 0x8a, 0x14, 0x01, //0x000002fe movb         (%r9,%r8), %dl
	0x80, 0xfa, 0x0d, //0x00000302 cmpb         $13, %dl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000305 je           LBB0_14
	0x80, 0xfa, 0x20, //0x0000030b cmpb         $32, %dl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000030e je           LBB0_14
	0x80, 0xc2, 0xf7, //0x00000314 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00000317 cmpb         $1, %dl
	0x0f, 0x87, 0x48, 0x01, 0x00, 0x00, //0x0000031a ja           LBB0_36
	//0x00000320 .p2align 4, 0x90
	//0x00000320 LBB0_14
	0x4d, 0x8d, 0x44, 0x24, 0x02, //0x00000320 leaq         $2(%r12), %r8
	0x49, 0x39, 0xc8, //0x00000325 cmpq         %rcx, %r8
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x00000328 jae          LBB0_18
	0x43, 0x8a, 0x14, 0x01, //0x0000032e movb         (%r9,%r8), %dl
	0x80, 0xfa, 0x0d, //0x00000332 cmpb         $13, %dl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000335 je           LBB0_18
	0x80, 0xfa, 0x20, //0x0000033b cmpb         $32, %dl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000033e je           LBB0_18
	0x80, 0xc2, 0xf7, //0x00000344 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00000347 cmpb         $1, %dl
	0x0f, 0x87, 0x18, 0x01, 0x00, 0x00, //0x0000034a ja           LBB0_36
	//0x00000350 .p2align 4, 0x90
	//0x00000350 LBB0_18
	0x4d, 0x8d, 0x44, 0x24, 0x03, //0x00000350 leaq         $3(%r12), %r8
	0x49, 0x39, 0xc8, //0x00000355 cmpq         %rcx, %r8
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x00000358 jae          LBB0_22
	0x43, 0x8a, 0x14, 0x01, //0x0000035e movb         (%r9,%r8), %dl
	0x80, 0xfa, 0x0d, //0x00000362 cmpb         $13, %dl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000365 je           LBB0_22
	0x80, 0xfa, 0x20, //0x0000036b cmpb         $32, %dl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000036e je           LBB0_22
	0x80, 0xc2, 0xf7, //0x00000374 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00000377 cmpb         $1, %dl
	0x0f, 0x87, 0xe8, 0x00, 0x00, 0x00, //0x0000037a ja           LBB0_36
	//0x00000380 .p2align 4, 0x90
	//0x00000380 LBB0_22
	0x4d, 0x8d, 0x44, 0x24, 0x04, //0x00000380 leaq         $4(%r12), %r8
	0x48, 0x89, 0xca, //0x00000385 movq         %rcx, %rdx
	0x4c, 0x29, 0xc2, //0x00000388 subq         %r8, %rdx
	0x0f, 0x86, 0xb1, 0x23, 0x00, 0x00, //0x0000038b jbe          LBB0_472
	0x4d, 0x01, 0xc8, //0x00000391 addq         %r9, %r8
	0x48, 0x83, 0xfa, 0x20, //0x00000394 cmpq         $32, %rdx
	0x0f, 0x82, 0x55, 0x00, 0x00, 0x00, //0x00000398 jb           LBB0_28
	0x48, 0x89, 0xce, //0x0000039e movq         %rcx, %rsi
	0x4c, 0x29, 0xe6, //0x000003a1 subq         %r12, %rsi
	0x48, 0x83, 0xc6, 0xdc, //0x000003a4 addq         $-36, %rsi
	0x48, 0x89, 0xf7, //0x000003a8 movq         %rsi, %rdi
	0x48, 0x83, 0xe7, 0xe0, //0x000003ab andq         $-32, %rdi
	0x4c, 0x01, 0xe7, //0x000003af addq         %r12, %rdi
	0x49, 0x8d, 0x44, 0x39, 0x24, //0x000003b2 leaq         $36(%r9,%rdi), %rax
	0x83, 0xe6, 0x1f, //0x000003b7 andl         $31, %esi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000003ba .p2align 4, 0x90
	//0x000003c0 LBB0_25
	0xc4, 0xc1, 0x7e, 0x6f, 0x00, //0x000003c0 vmovdqu      (%r8), %ymm0
	0xc4, 0xe2, 0x55, 0x00, 0xc8, //0x000003c5 vpshufb      %ymm0, %ymm5, %ymm1
	0xc5, 0xfd, 0x74, 0xc1, //0x000003ca vpcmpeqb     %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf8, //0x000003ce vpmovmskb    %ymm0, %edi
	0x83, 0xff, 0xff, //0x000003d2 cmpl         $-1, %edi
	0x0f, 0x85, 0x75, 0x00, 0x00, 0x00, //0x000003d5 jne          LBB0_35
	0x49, 0x83, 0xc0, 0x20, //0x000003db addq         $32, %r8
	0x48, 0x83, 0xc2, 0xe0, //0x000003df addq         $-32, %rdx
	0x48, 0x83, 0xfa, 0x1f, //0x000003e3 cmpq         $31, %rdx
	0x0f, 0x87, 0xd3, 0xff, 0xff, 0xff, //0x000003e7 ja           LBB0_25
	0x48, 0x89, 0xf2, //0x000003ed movq         %rsi, %rdx
	0x49, 0x89, 0xc0, //0x000003f0 movq         %rax, %r8
	//0x000003f3 LBB0_28
	0x48, 0x85, 0xd2, //0x000003f3 testq        %rdx, %rdx
	0x48, 0xb8, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000003f6 movabsq      $4294977024, %rax
	0x0f, 0x84, 0x34, 0x00, 0x00, 0x00, //0x00000400 je           LBB0_34
	0x49, 0x8d, 0x34, 0x10, //0x00000406 leaq         (%r8,%rdx), %rsi
	0x49, 0xff, 0xc0, //0x0000040a incq         %r8
	0x90, 0x90, 0x90, //0x0000040d .p2align 4, 0x90
	//0x00000410 LBB0_30
	0x41, 0x0f, 0xbe, 0x78, 0xff, //0x00000410 movsbl       $-1(%r8), %edi
	0x83, 0xff, 0x20, //0x00000415 cmpl         $32, %edi
	0x0f, 0x87, 0xfa, 0x10, 0x00, 0x00, //0x00000418 ja           LBB0_268
	0x48, 0x0f, 0xa3, 0xf8, //0x0000041e btq          %rdi, %rax
	0x0f, 0x83, 0xf0, 0x10, 0x00, 0x00, //0x00000422 jae          LBB0_268
	0x48, 0xff, 0xca, //0x00000428 decq         %rdx
	0x49, 0xff, 0xc0, //0x0000042b incq         %r8
	0x48, 0x85, 0xd2, //0x0000042e testq        %rdx, %rdx
	0x0f, 0x85, 0xd9, 0xff, 0xff, 0xff, //0x00000431 jne          LBB0_30
	0x49, 0x89, 0xf0, //0x00000437 movq         %rsi, %r8
	//0x0000043a LBB0_34
	0x4d, 0x29, 0xc8, //0x0000043a subq         %r9, %r8
	0x49, 0x39, 0xc8, //0x0000043d cmpq         %rcx, %r8
	0x0f, 0x82, 0x22, 0x00, 0x00, 0x00, //0x00000440 jb           LBB0_36
	0xe9, 0xfb, 0x22, 0x00, 0x00, //0x00000446 jmp          LBB0_473
	0x90, 0x90, 0x90, 0x90, 0x90, //0x0000044b .p2align 4, 0x90
	//0x00000450 LBB0_35
	0x4d, 0x29, 0xc8, //0x00000450 subq         %r9, %r8
	0xf7, 0xd7, //0x00000453 notl         %edi
	0x48, 0x63, 0xd7, //0x00000455 movslq       %edi, %rdx
	0x48, 0x0f, 0xbc, 0xd2, //0x00000458 bsfq         %rdx, %rdx
	0x49, 0x01, 0xd0, //0x0000045c addq         %rdx, %r8
	0x49, 0x39, 0xc8, //0x0000045f cmpq         %rcx, %r8
	0x0f, 0x83, 0xde, 0x22, 0x00, 0x00, //0x00000462 jae          LBB0_473
	//0x00000468 LBB0_36
	0x4d, 0x8d, 0x60, 0x01, //0x00000468 leaq         $1(%r8), %r12
	0x4d, 0x89, 0x65, 0x00, //0x0000046c movq         %r12, (%r13)
	0x43, 0x0f, 0xbe, 0x34, 0x01, //0x00000470 movsbl       (%r9,%r8), %esi
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000475 movq         $-1, %rcx
	0x85, 0xf6, //0x0000047c testl        %esi, %esi
	0x0f, 0x84, 0x35, 0x23, 0x00, 0x00, //0x0000047e je           LBB0_498
	0x48, 0x8d, 0x53, 0xff, //0x00000484 leaq         $-1(%rbx), %rdx
	0x41, 0x8b, 0x3c, 0xda, //0x00000488 movl         (%r10,%rbx,8), %edi
	0x48, 0x8b, 0x45, 0x90, //0x0000048c movq         $-112(%rbp), %rax
	0x48, 0x83, 0xf8, 0xff, //0x00000490 cmpq         $-1, %rax
	0x49, 0x0f, 0x44, 0xc0, //0x00000494 cmoveq       %r8, %rax
	0x48, 0x89, 0x45, 0x90, //0x00000498 movq         %rax, $-112(%rbp)
	0xff, 0xcf, //0x0000049c decl         %edi
	0x83, 0xff, 0x05, //0x0000049e cmpl         $5, %edi
	0x0f, 0x87, 0xe8, 0x01, 0x00, 0x00, //0x000004a1 ja           LBB0_66
	0x48, 0x8d, 0x05, 0x36, 0x25, 0x00, 0x00, //0x000004a7 leaq         $9526(%rip), %rax  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x3c, 0xb8, //0x000004ae movslq       (%rax,%rdi,4), %rdi
	0x48, 0x01, 0xc7, //0x000004b2 addq         %rax, %rdi
	0xff, 0xe7, //0x000004b5 jmpq         *%rdi
	//0x000004b7 LBB0_39
	0x83, 0xfe, 0x2c, //0x000004b7 cmpl         $44, %esi
	0x0f, 0x84, 0x6f, 0x07, 0x00, 0x00, //0x000004ba je           LBB0_88
	0x83, 0xfe, 0x5d, //0x000004c0 cmpl         $93, %esi
	0x0f, 0x84, 0x5b, 0x05, 0x00, 0x00, //0x000004c3 je           LBB0_41
	0xe9, 0xe4, 0x22, 0x00, 0x00, //0x000004c9 jmp          LBB0_497
	//0x000004ce LBB0_42
	0x40, 0x80, 0xfe, 0x5d, //0x000004ce cmpb         $93, %sil
	0x0f, 0x84, 0x4c, 0x05, 0x00, 0x00, //0x000004d2 je           LBB0_41
	0x49, 0xc7, 0x04, 0xda, 0x01, 0x00, 0x00, 0x00, //0x000004d8 movq         $1, (%r10,%rbx,8)
	0x83, 0xfe, 0x7b, //0x000004e0 cmpl         $123, %esi
	0x0f, 0x86, 0xb2, 0x01, 0x00, 0x00, //0x000004e3 jbe          LBB0_44
	0xe9, 0xc4, 0x22, 0x00, 0x00, //0x000004e9 jmp          LBB0_497
	//0x000004ee LBB0_45
	0x40, 0x80, 0xfe, 0x22, //0x000004ee cmpb         $34, %sil
	0x0f, 0x85, 0xba, 0x22, 0x00, 0x00, //0x000004f2 jne          LBB0_497
	0x49, 0xc7, 0x04, 0xda, 0x04, 0x00, 0x00, 0x00, //0x000004f8 movq         $4, (%r10,%rbx,8)
	0x49, 0x8b, 0x56, 0x08, //0x00000500 movq         $8(%r14), %rdx
	0xf6, 0x45, 0x98, 0x20, //0x00000504 testb        $32, $-104(%rbp)
	0x4c, 0x89, 0x65, 0xa8, //0x00000508 movq         %r12, $-88(%rbp)
	0x48, 0x89, 0x55, 0xb0, //0x0000050c movq         %rdx, $-80(%rbp)
	0x0f, 0x85, 0xb0, 0x05, 0x00, 0x00, //0x00000510 jne          LBB0_73
	0x48, 0x89, 0xd1, //0x00000516 movq         %rdx, %rcx
	0x4c, 0x29, 0xe1, //0x00000519 subq         %r12, %rcx
	0x0f, 0x84, 0x35, 0x24, 0x00, 0x00, //0x0000051c je           LBB0_504
	0x4b, 0x8d, 0x1c, 0x21, //0x00000522 leaq         (%r9,%r12), %rbx
	0x48, 0x83, 0xf9, 0x40, //0x00000526 cmpq         $64, %rcx
	0x0f, 0x82, 0xe2, 0x18, 0x00, 0x00, //0x0000052a jb           LBB0_355
	0x41, 0x89, 0xcf, //0x00000530 movl         %ecx, %r15d
	0x41, 0x83, 0xe7, 0x3f, //0x00000533 andl         $63, %r15d
	0x4c, 0x29, 0xc2, //0x00000537 subq         %r8, %rdx
	0x48, 0x83, 0xc2, 0xbf, //0x0000053a addq         $-65, %rdx
	0x48, 0x83, 0xe2, 0xc0, //0x0000053e andq         $-64, %rdx
	0x4c, 0x01, 0xe2, //0x00000542 addq         %r12, %rdx
	0x4d, 0x89, 0xcb, //0x00000545 movq         %r9, %r11
	0x4d, 0x8d, 0x4c, 0x11, 0x40, //0x00000548 leaq         $64(%r9,%rdx), %r9
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x0000054d movq         $-1, %r13
	0x45, 0x31, 0xf6, //0x00000554 xorl         %r14d, %r14d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000557 .p2align 4, 0x90
	//0x00000560 LBB0_50
	0xc5, 0xfe, 0x6f, 0x03, //0x00000560 vmovdqu      (%rbx), %ymm0
	0xc5, 0xfe, 0x6f, 0x4b, 0x20, //0x00000564 vmovdqu      $32(%rbx), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x00000569 vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xf2, //0x0000056d vpmovmskb    %ymm2, %esi
	0xc5, 0xf5, 0x74, 0xd6, //0x00000571 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00000575 vpmovmskb    %ymm2, %edx
	0xc5, 0xfd, 0x74, 0xc7, //0x00000579 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf8, //0x0000057d vpmovmskb    %ymm0, %edi
	0xc5, 0xf5, 0x74, 0xc7, //0x00000581 vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x00000585 vpmovmskb    %ymm0, %eax
	0x48, 0xc1, 0xe2, 0x20, //0x00000589 shlq         $32, %rdx
	0x48, 0xc1, 0xe0, 0x20, //0x0000058d shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x00000591 orq          %rax, %rdi
	0x49, 0x83, 0xfd, 0xff, //0x00000594 cmpq         $-1, %r13
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00000598 jne          LBB0_52
	0x48, 0x85, 0xff, //0x0000059e testq        %rdi, %rdi
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x000005a1 jne          LBB0_59
	//0x000005a7 LBB0_52
	0x48, 0x09, 0xf2, //0x000005a7 orq          %rsi, %rdx
	0x48, 0x89, 0xf8, //0x000005aa movq         %rdi, %rax
	0x4c, 0x09, 0xf0, //0x000005ad orq          %r14, %rax
	0x0f, 0x85, 0x3c, 0x00, 0x00, 0x00, //0x000005b0 jne          LBB0_60
	//0x000005b6 LBB0_53
	0x48, 0x85, 0xd2, //0x000005b6 testq        %rdx, %rdx
	0x0f, 0x85, 0xdb, 0x14, 0x00, 0x00, //0x000005b9 jne          LBB0_61
	//0x000005bf LBB0_54
	0x48, 0x83, 0xc1, 0xc0, //0x000005bf addq         $-64, %rcx
	0x48, 0x83, 0xc3, 0x40, //0x000005c3 addq         $64, %rbx
	0x48, 0x83, 0xf9, 0x3f, //0x000005c7 cmpq         $63, %rcx
	0x0f, 0x87, 0x8f, 0xff, 0xff, 0xff, //0x000005cb ja           LBB0_50
	0xe9, 0x83, 0x14, 0x00, 0x00, //0x000005d1 jmp          LBB0_55
	//0x000005d6 LBB0_59
	0x48, 0x89, 0xd8, //0x000005d6 movq         %rbx, %rax
	0x4c, 0x29, 0xd8, //0x000005d9 subq         %r11, %rax
	0x4c, 0x0f, 0xbc, 0xef, //0x000005dc bsfq         %rdi, %r13
	0x49, 0x01, 0xc5, //0x000005e0 addq         %rax, %r13
	0x48, 0x09, 0xf2, //0x000005e3 orq          %rsi, %rdx
	0x48, 0x89, 0xf8, //0x000005e6 movq         %rdi, %rax
	0x4c, 0x09, 0xf0, //0x000005e9 orq          %r14, %rax
	0x0f, 0x84, 0xc4, 0xff, 0xff, 0xff, //0x000005ec je           LBB0_53
	//0x000005f2 LBB0_60
	0x4c, 0x89, 0xf0, //0x000005f2 movq         %r14, %rax
	0x48, 0xf7, 0xd0, //0x000005f5 notq         %rax
	0x48, 0x21, 0xf8, //0x000005f8 andq         %rdi, %rax
	0x4c, 0x8d, 0x14, 0x00, //0x000005fb leaq         (%rax,%rax), %r10
	0x4d, 0x09, 0xf2, //0x000005ff orq          %r14, %r10
	0x4c, 0x89, 0xd6, //0x00000602 movq         %r10, %rsi
	0x48, 0xf7, 0xd6, //0x00000605 notq         %rsi
	0x48, 0x21, 0xfe, //0x00000608 andq         %rdi, %rsi
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000060b movabsq      $-6148914691236517206, %rdi
	0x48, 0x21, 0xfe, //0x00000615 andq         %rdi, %rsi
	0x45, 0x31, 0xf6, //0x00000618 xorl         %r14d, %r14d
	0x48, 0x01, 0xc6, //0x0000061b addq         %rax, %rsi
	0x41, 0x0f, 0x92, 0xc6, //0x0000061e setb         %r14b
	0x48, 0x01, 0xf6, //0x00000622 addq         %rsi, %rsi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000625 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc6, //0x0000062f xorq         %rax, %rsi
	0x4c, 0x21, 0xd6, //0x00000632 andq         %r10, %rsi
	0x4c, 0x8b, 0x55, 0xb8, //0x00000635 movq         $-72(%rbp), %r10
	0x48, 0xf7, 0xd6, //0x00000639 notq         %rsi
	0x48, 0x21, 0xf2, //0x0000063c andq         %rsi, %rdx
	0x48, 0x85, 0xd2, //0x0000063f testq        %rdx, %rdx
	0x0f, 0x84, 0x77, 0xff, 0xff, 0xff, //0x00000642 je           LBB0_54
	0xe9, 0x4d, 0x14, 0x00, 0x00, //0x00000648 jmp          LBB0_61
	//0x0000064d LBB0_62
	0x83, 0xfe, 0x2c, //0x0000064d cmpl         $44, %esi
	0x0f, 0x85, 0xc5, 0x03, 0x00, 0x00, //0x00000650 jne          LBB0_63
	0x48, 0x81, 0xfb, 0xff, 0x0f, 0x00, 0x00, //0x00000656 cmpq         $4095, %rbx
	0x0f, 0x8f, 0xef, 0x20, 0x00, 0x00, //0x0000065d jg           LBB0_486
	0x48, 0x8d, 0x43, 0x01, //0x00000663 leaq         $1(%rbx), %rax
	0x49, 0x89, 0x02, //0x00000667 movq         %rax, (%r10)
	0x49, 0xc7, 0x44, 0xda, 0x08, 0x03, 0x00, 0x00, 0x00, //0x0000066a movq         $3, $8(%r10,%rbx,8)
	0xe9, 0xf8, 0xfb, 0xff, 0xff, //0x00000673 jmp          LBB0_3
	//0x00000678 LBB0_64
	0x40, 0x80, 0xfe, 0x3a, //0x00000678 cmpb         $58, %sil
	0x0f, 0x85, 0x30, 0x21, 0x00, 0x00, //0x0000067c jne          LBB0_497
	0x49, 0xc7, 0x04, 0xda, 0x00, 0x00, 0x00, 0x00, //0x00000682 movq         $0, (%r10,%rbx,8)
	0xe9, 0xe1, 0xfb, 0xff, 0xff, //0x0000068a jmp          LBB0_3
	//0x0000068f LBB0_66
	0x49, 0x89, 0x12, //0x0000068f movq         %rdx, (%r10)
	0x83, 0xfe, 0x7b, //0x00000692 cmpl         $123, %esi
	0x0f, 0x87, 0x17, 0x21, 0x00, 0x00, //0x00000695 ja           LBB0_497
	//0x0000069b LBB0_44
	0x4f, 0x8d, 0x3c, 0x01, //0x0000069b leaq         (%r9,%r8), %r15
	0x89, 0xf0, //0x0000069f movl         %esi, %eax
	0x48, 0x8d, 0x15, 0x54, 0x23, 0x00, 0x00, //0x000006a1 leaq         $9044(%rip), %rdx  /* LJTI0_1+0(%rip) */
	0x48, 0x63, 0x04, 0x82, //0x000006a8 movslq       (%rdx,%rax,4), %rax
	0x48, 0x01, 0xd0, //0x000006ac addq         %rdx, %rax
	0xff, 0xe0, //0x000006af jmpq         *%rax
	//0x000006b1 LBB0_69
	0x4d, 0x8b, 0x56, 0x08, //0x000006b1 movq         $8(%r14), %r10
	0x4d, 0x29, 0xc2, //0x000006b5 subq         %r8, %r10
	0x0f, 0x84, 0xba, 0x20, 0x00, 0x00, //0x000006b8 je           LBB0_476
	0x41, 0x80, 0x3f, 0x30, //0x000006be cmpb         $48, (%r15)
	0x0f, 0x85, 0x3a, 0x00, 0x00, 0x00, //0x000006c2 jne          LBB0_132
	0x41, 0xbe, 0x01, 0x00, 0x00, 0x00, //0x000006c8 movl         $1, %r14d
	0x49, 0x83, 0xfa, 0x01, //0x000006ce cmpq         $1, %r10
	0x0f, 0x84, 0x75, 0xfb, 0xff, 0xff, //0x000006d2 je           LBB0_72
	0x43, 0x8a, 0x0c, 0x21, //0x000006d8 movb         (%r9,%r12), %cl
	0x80, 0xc1, 0xd2, //0x000006dc addb         $-46, %cl
	0x80, 0xf9, 0x37, //0x000006df cmpb         $55, %cl
	0x0f, 0x87, 0x65, 0xfb, 0xff, 0xff, //0x000006e2 ja           LBB0_72
	0x0f, 0xb6, 0xc1, //0x000006e8 movzbl       %cl, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x000006eb movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x000006f5 btq          %rax, %rcx
	0x4c, 0x89, 0xe1, //0x000006f9 movq         %r12, %rcx
	0x0f, 0x83, 0x4e, 0xfb, 0xff, 0xff, //0x000006fc jae          LBB0_199
	//0x00000702 LBB0_132
	0x4c, 0x89, 0x65, 0xa8, //0x00000702 movq         %r12, $-88(%rbp)
	0x49, 0x83, 0xfa, 0x20, //0x00000706 cmpq         $32, %r10
	0x0f, 0x82, 0xdb, 0x16, 0x00, 0x00, //0x0000070a jb           LBB0_354
	0x49, 0x8d, 0x4a, 0xe0, //0x00000710 leaq         $-32(%r10), %rcx
	0x48, 0x89, 0xc8, //0x00000714 movq         %rcx, %rax
	0x48, 0x83, 0xe0, 0xe0, //0x00000717 andq         $-32, %rax
	0x4e, 0x8d, 0x74, 0x38, 0x20, //0x0000071b leaq         $32(%rax,%r15), %r14
	0x83, 0xe1, 0x1f, //0x00000720 andl         $31, %ecx
	0x48, 0x89, 0x4d, 0xc8, //0x00000723 movq         %rcx, $-56(%rbp)
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000727 movq         $-1, %rax
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x0000072e movq         $-1, %r12
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00000735 movq         $-1, %r13
	0x4d, 0x89, 0xfb, //0x0000073c movq         %r15, %r11
	0x90, //0x0000073f .p2align 4, 0x90
	//0x00000740 LBB0_134
	0xc4, 0xc1, 0x7e, 0x6f, 0x03, //0x00000740 vmovdqu      (%r11), %ymm0
	0xc4, 0xc1, 0x7d, 0x64, 0xca, //0x00000745 vpcmpgtb     %ymm10, %ymm0, %ymm1
	0xc5, 0xa5, 0x64, 0xd0, //0x0000074a vpcmpgtb     %ymm0, %ymm11, %ymm2
	0xc5, 0xf5, 0xdb, 0xca, //0x0000074e vpand        %ymm2, %ymm1, %ymm1
	0xc5, 0x9d, 0x74, 0xd0, //0x00000752 vpcmpeqb     %ymm0, %ymm12, %ymm2
	0xc5, 0x95, 0x74, 0xd8, //0x00000756 vpcmpeqb     %ymm0, %ymm13, %ymm3
	0xc5, 0xe5, 0xeb, 0xd2, //0x0000075a vpor         %ymm2, %ymm3, %ymm2
	0xc5, 0xbd, 0xeb, 0xd8, //0x0000075e vpor         %ymm0, %ymm8, %ymm3
	0xc5, 0x8d, 0x74, 0xc0, //0x00000762 vpcmpeqb     %ymm0, %ymm14, %ymm0
	0xc5, 0xfd, 0xd7, 0xf8, //0x00000766 vpmovmskb    %ymm0, %edi
	0xc5, 0x85, 0x74, 0xdb, //0x0000076a vpcmpeqb     %ymm3, %ymm15, %ymm3
	0xc5, 0xfd, 0xd7, 0xd3, //0x0000076e vpmovmskb    %ymm3, %edx
	0xc5, 0xfd, 0xd7, 0xf2, //0x00000772 vpmovmskb    %ymm2, %esi
	0xc5, 0xe5, 0xeb, 0xc0, //0x00000776 vpor         %ymm0, %ymm3, %ymm0
	0xc5, 0xed, 0xeb, 0xc9, //0x0000077a vpor         %ymm1, %ymm2, %ymm1
	0xc5, 0xfd, 0xeb, 0xc1, //0x0000077e vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc8, //0x00000782 vpmovmskb    %ymm0, %ecx
	0x48, 0xf7, 0xd1, //0x00000786 notq         %rcx
	0x48, 0x0f, 0xbc, 0xc9, //0x00000789 bsfq         %rcx, %rcx
	0x83, 0xf9, 0x20, //0x0000078d cmpl         $32, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x00000790 je           LBB0_136
	0xbb, 0xff, 0xff, 0xff, 0xff, //0x00000796 movl         $-1, %ebx
	0xd3, 0xe3, //0x0000079b shll         %cl, %ebx
	0xf7, 0xd3, //0x0000079d notl         %ebx
	0x21, 0xdf, //0x0000079f andl         %ebx, %edi
	0x21, 0xda, //0x000007a1 andl         %ebx, %edx
	0x21, 0xf3, //0x000007a3 andl         %esi, %ebx
	0x89, 0xde, //0x000007a5 movl         %ebx, %esi
	//0x000007a7 LBB0_136
	0x44, 0x8d, 0x4f, 0xff, //0x000007a7 leal         $-1(%rdi), %r9d
	0x41, 0x21, 0xf9, //0x000007ab andl         %edi, %r9d
	0x0f, 0x85, 0xd4, 0x13, 0x00, 0x00, //0x000007ae jne          LBB0_345
	0x8d, 0x5a, 0xff, //0x000007b4 leal         $-1(%rdx), %ebx
	0x21, 0xd3, //0x000007b7 andl         %edx, %ebx
	0x0f, 0x85, 0x8e, 0x12, 0x00, 0x00, //0x000007b9 jne          LBB0_339
	0x8d, 0x5e, 0xff, //0x000007bf leal         $-1(%rsi), %ebx
	0x21, 0xf3, //0x000007c2 andl         %esi, %ebx
	0x0f, 0x85, 0x83, 0x12, 0x00, 0x00, //0x000007c4 jne          LBB0_339
	0x85, 0xff, //0x000007ca testl        %edi, %edi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x000007cc je           LBB0_142
	0x4c, 0x89, 0xdb, //0x000007d2 movq         %r11, %rbx
	0x4c, 0x29, 0xfb, //0x000007d5 subq         %r15, %rbx
	0x0f, 0xbc, 0xff, //0x000007d8 bsfl         %edi, %edi
	0x48, 0x01, 0xdf, //0x000007db addq         %rbx, %rdi
	0x49, 0x83, 0xfd, 0xff, //0x000007de cmpq         $-1, %r13
	0x0f, 0x85, 0xb2, 0x13, 0x00, 0x00, //0x000007e2 jne          LBB0_347
	0x49, 0x89, 0xfd, //0x000007e8 movq         %rdi, %r13
	//0x000007eb LBB0_142
	0x85, 0xd2, //0x000007eb testl        %edx, %edx
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x000007ed je           LBB0_145
	0x4c, 0x89, 0xdf, //0x000007f3 movq         %r11, %rdi
	0x4c, 0x29, 0xff, //0x000007f6 subq         %r15, %rdi
	0x0f, 0xbc, 0xd2, //0x000007f9 bsfl         %edx, %edx
	0x48, 0x01, 0xfa, //0x000007fc addq         %rdi, %rdx
	0x49, 0x83, 0xfc, 0xff, //0x000007ff cmpq         $-1, %r12
	0x0f, 0x85, 0x26, 0x13, 0x00, 0x00, //0x00000803 jne          LBB0_340
	0x49, 0x89, 0xd4, //0x00000809 movq         %rdx, %r12
	//0x0000080c LBB0_145
	0x85, 0xf6, //0x0000080c testl        %esi, %esi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000080e je           LBB0_148
	0x4c, 0x89, 0xdf, //0x00000814 movq         %r11, %rdi
	0x4c, 0x29, 0xff, //0x00000817 subq         %r15, %rdi
	0x0f, 0xbc, 0xd6, //0x0000081a bsfl         %esi, %edx
	0x48, 0x01, 0xfa, //0x0000081d addq         %rdi, %rdx
	0x48, 0x83, 0xf8, 0xff, //0x00000820 cmpq         $-1, %rax
	0x0f, 0x85, 0x05, 0x13, 0x00, 0x00, //0x00000824 jne          LBB0_340
	0x48, 0x89, 0xd0, //0x0000082a movq         %rdx, %rax
	//0x0000082d LBB0_148
	0x83, 0xf9, 0x20, //0x0000082d cmpl         $32, %ecx
	0x0f, 0x85, 0x85, 0x05, 0x00, 0x00, //0x00000830 jne          LBB0_180
	0x49, 0x83, 0xc3, 0x20, //0x00000836 addq         $32, %r11
	0x49, 0x83, 0xc2, 0xe0, //0x0000083a addq         $-32, %r10
	0x49, 0x83, 0xfa, 0x1f, //0x0000083e cmpq         $31, %r10
	0x0f, 0x87, 0xf8, 0xfe, 0xff, 0xff, //0x00000842 ja           LBB0_134
	0xc5, 0xf8, 0x77, //0x00000848 vzeroupper   
	0xc5, 0x7e, 0x6f, 0x3d, 0xcd, 0xf8, 0xff, 0xff, //0x0000084b vmovdqu      $-1843(%rip), %ymm15  /* LCPI0_9+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0xa5, 0xf8, 0xff, 0xff, //0x00000853 vmovdqu      $-1883(%rip), %ymm14  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x7d, 0xf8, 0xff, 0xff, //0x0000085b vmovdqu      $-1923(%rip), %ymm13  /* LCPI0_7+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x55, 0xf8, 0xff, 0xff, //0x00000863 vmovdqu      $-1963(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x2d, 0xf8, 0xff, 0xff, //0x0000086b vmovdqu      $-2003(%rip), %ymm11  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0x05, 0xf8, 0xff, 0xff, //0x00000873 vmovdqu      $-2043(%rip), %ymm10  /* LCPI0_4+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000087b vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x05, 0xd8, 0xf7, 0xff, 0xff, //0x00000880 vmovdqu      $-2088(%rip), %ymm8  /* LCPI0_3+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0xb0, 0xf7, 0xff, 0xff, //0x00000888 vmovdqu      $-2128(%rip), %ymm7  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x88, 0xf7, 0xff, 0xff, //0x00000890 vmovdqu      $-2168(%rip), %ymm6  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x2d, 0x60, 0xf7, 0xff, 0xff, //0x00000898 vmovdqu      $-2208(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0x4c, 0x8b, 0x55, 0xc8, //0x000008a0 movq         $-56(%rbp), %r10
	0x49, 0x83, 0xfa, 0x10, //0x000008a4 cmpq         $16, %r10
	0x0f, 0x82, 0x52, 0x01, 0x00, 0x00, //0x000008a8 jb           LBB0_169
	//0x000008ae LBB0_151
	0x4d, 0x8d, 0x4a, 0xf0, //0x000008ae leaq         $-16(%r10), %r9
	0x4c, 0x89, 0xc9, //0x000008b2 movq         %r9, %rcx
	0x48, 0x83, 0xe1, 0xf0, //0x000008b5 andq         $-16, %rcx
	0x4e, 0x8d, 0x5c, 0x31, 0x10, //0x000008b9 leaq         $16(%rcx,%r14), %r11
	0x41, 0x83, 0xe1, 0x0f, //0x000008be andl         $15, %r9d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000008c2 .p2align 4, 0x90
	//0x000008d0 LBB0_152
	0xc4, 0xc1, 0x7a, 0x6f, 0x06, //0x000008d0 vmovdqu      (%r14), %xmm0
	0xc5, 0xf9, 0x64, 0x0d, 0x63, 0xf8, 0xff, 0xff, //0x000008d5 vpcmpgtb     $-1949(%rip), %xmm0, %xmm1  /* LCPI0_10+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x15, 0x6b, 0xf8, 0xff, 0xff, //0x000008dd vmovdqu      $-1941(%rip), %xmm2  /* LCPI0_11+0(%rip) */
	0xc5, 0xe9, 0x64, 0xd0, //0x000008e5 vpcmpgtb     %xmm0, %xmm2, %xmm2
	0xc5, 0xf1, 0xdb, 0xca, //0x000008e9 vpand        %xmm2, %xmm1, %xmm1
	0xc5, 0xf9, 0x74, 0x15, 0x6b, 0xf8, 0xff, 0xff, //0x000008ed vpcmpeqb     $-1941(%rip), %xmm0, %xmm2  /* LCPI0_12+0(%rip) */
	0xc5, 0xf9, 0x74, 0x1d, 0x73, 0xf8, 0xff, 0xff, //0x000008f5 vpcmpeqb     $-1933(%rip), %xmm0, %xmm3  /* LCPI0_13+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xd2, //0x000008fd vpor         %xmm2, %xmm3, %xmm2
	0xc5, 0xf9, 0xeb, 0x1d, 0x77, 0xf8, 0xff, 0xff, //0x00000901 vpor         $-1929(%rip), %xmm0, %xmm3  /* LCPI0_14+0(%rip) */
	0xc5, 0xf9, 0x74, 0x05, 0x7f, 0xf8, 0xff, 0xff, //0x00000909 vpcmpeqb     $-1921(%rip), %xmm0, %xmm0  /* LCPI0_15+0(%rip) */
	0xc5, 0xe1, 0x74, 0x1d, 0x87, 0xf8, 0xff, 0xff, //0x00000911 vpcmpeqb     $-1913(%rip), %xmm3, %xmm3  /* LCPI0_16+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xe0, //0x00000919 vpor         %xmm0, %xmm3, %xmm4
	0xc5, 0xe9, 0xeb, 0xc9, //0x0000091d vpor         %xmm1, %xmm2, %xmm1
	0xc5, 0xd9, 0xeb, 0xc9, //0x00000921 vpor         %xmm1, %xmm4, %xmm1
	0xc5, 0xf9, 0xd7, 0xf8, //0x00000925 vpmovmskb    %xmm0, %edi
	0xc5, 0xf9, 0xd7, 0xf3, //0x00000929 vpmovmskb    %xmm3, %esi
	0xc5, 0xf9, 0xd7, 0xd2, //0x0000092d vpmovmskb    %xmm2, %edx
	0xc5, 0xf9, 0xd7, 0xc9, //0x00000931 vpmovmskb    %xmm1, %ecx
	0xbb, 0xff, 0xff, 0xff, 0xff, //0x00000935 movl         $4294967295, %ebx
	0x48, 0x31, 0xd9, //0x0000093a xorq         %rbx, %rcx
	0x48, 0x0f, 0xbc, 0xc9, //0x0000093d bsfq         %rcx, %rcx
	0x83, 0xf9, 0x10, //0x00000941 cmpl         $16, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x00000944 je           LBB0_154
	0xbb, 0xff, 0xff, 0xff, 0xff, //0x0000094a movl         $-1, %ebx
	0xd3, 0xe3, //0x0000094f shll         %cl, %ebx
	0xf7, 0xd3, //0x00000951 notl         %ebx
	0x21, 0xdf, //0x00000953 andl         %ebx, %edi
	0x21, 0xde, //0x00000955 andl         %ebx, %esi
	0x21, 0xd3, //0x00000957 andl         %edx, %ebx
	0x89, 0xda, //0x00000959 movl         %ebx, %edx
	//0x0000095b LBB0_154
	0x8d, 0x5f, 0xff, //0x0000095b leal         $-1(%rdi), %ebx
	0x21, 0xfb, //0x0000095e andl         %edi, %ebx
	0x0f, 0x85, 0x14, 0x12, 0x00, 0x00, //0x00000960 jne          LBB0_344
	0x8d, 0x5e, 0xff, //0x00000966 leal         $-1(%rsi), %ebx
	0x21, 0xf3, //0x00000969 andl         %esi, %ebx
	0x0f, 0x85, 0x09, 0x12, 0x00, 0x00, //0x0000096b jne          LBB0_344
	0x8d, 0x5a, 0xff, //0x00000971 leal         $-1(%rdx), %ebx
	0x21, 0xd3, //0x00000974 andl         %edx, %ebx
	0x0f, 0x85, 0xfe, 0x11, 0x00, 0x00, //0x00000976 jne          LBB0_344
	0x85, 0xff, //0x0000097c testl        %edi, %edi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000097e je           LBB0_160
	0x4c, 0x89, 0xf3, //0x00000984 movq         %r14, %rbx
	0x4c, 0x29, 0xfb, //0x00000987 subq         %r15, %rbx
	0x0f, 0xbc, 0xff, //0x0000098a bsfl         %edi, %edi
	0x48, 0x01, 0xdf, //0x0000098d addq         %rbx, %rdi
	0x49, 0x83, 0xfd, 0xff, //0x00000990 cmpq         $-1, %r13
	0x0f, 0x85, 0x00, 0x12, 0x00, 0x00, //0x00000994 jne          LBB0_347
	0x49, 0x89, 0xfd, //0x0000099a movq         %rdi, %r13
	//0x0000099d LBB0_160
	0x85, 0xf6, //0x0000099d testl        %esi, %esi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000099f je           LBB0_163
	0x4c, 0x89, 0xf7, //0x000009a5 movq         %r14, %rdi
	0x4c, 0x29, 0xff, //0x000009a8 subq         %r15, %rdi
	0x0f, 0xbc, 0xf6, //0x000009ab bsfl         %esi, %esi
	0x48, 0x01, 0xfe, //0x000009ae addq         %rdi, %rsi
	0x49, 0x83, 0xfc, 0xff, //0x000009b1 cmpq         $-1, %r12
	0x0f, 0x85, 0x0f, 0x13, 0x00, 0x00, //0x000009b5 jne          LBB0_350
	0x49, 0x89, 0xf4, //0x000009bb movq         %rsi, %r12
	//0x000009be LBB0_163
	0x85, 0xd2, //0x000009be testl        %edx, %edx
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x000009c0 je           LBB0_166
	0x4c, 0x89, 0xf6, //0x000009c6 movq         %r14, %rsi
	0x4c, 0x29, 0xfe, //0x000009c9 subq         %r15, %rsi
	0x0f, 0xbc, 0xd2, //0x000009cc bsfl         %edx, %edx
	0x48, 0x01, 0xf2, //0x000009cf addq         %rsi, %rdx
	0x48, 0x83, 0xf8, 0xff, //0x000009d2 cmpq         $-1, %rax
	0x0f, 0x85, 0x53, 0x11, 0x00, 0x00, //0x000009d6 jne          LBB0_340
	0x48, 0x89, 0xd0, //0x000009dc movq         %rdx, %rax
	//0x000009df LBB0_166
	0x83, 0xf9, 0x10, //0x000009df cmpl         $16, %ecx
	0x0f, 0x85, 0xb3, 0x05, 0x00, 0x00, //0x000009e2 jne          LBB0_186
	0x49, 0x83, 0xc6, 0x10, //0x000009e8 addq         $16, %r14
	0x49, 0x83, 0xc2, 0xf0, //0x000009ec addq         $-16, %r10
	0x49, 0x83, 0xfa, 0x0f, //0x000009f0 cmpq         $15, %r10
	0x0f, 0x87, 0xd6, 0xfe, 0xff, 0xff, //0x000009f4 ja           LBB0_152
	0x4d, 0x89, 0xca, //0x000009fa movq         %r9, %r10
	0x4d, 0x89, 0xde, //0x000009fd movq         %r11, %r14
	//0x00000a00 LBB0_169
	0x4d, 0x85, 0xd2, //0x00000a00 testq        %r10, %r10
	0x0f, 0x84, 0x95, 0x05, 0x00, 0x00, //0x00000a03 je           LBB0_187
	0x4b, 0x8d, 0x0c, 0x16, //0x00000a09 leaq         (%r14,%r10), %rcx
	0xe9, 0x5a, 0x00, 0x00, 0x00, //0x00000a0d jmp          LBB0_174
	//0x00000a12 LBB0_67
	0x83, 0xfe, 0x22, //0x00000a12 cmpl         $34, %esi
	0x0f, 0x84, 0x36, 0x02, 0x00, 0x00, //0x00000a15 je           LBB0_92
	//0x00000a1b LBB0_63
	0x83, 0xfe, 0x7d, //0x00000a1b cmpl         $125, %esi
	0x0f, 0x85, 0x8e, 0x1d, 0x00, 0x00, //0x00000a1e jne          LBB0_497
	//0x00000a24 LBB0_41
	0x49, 0x89, 0x12, //0x00000a24 movq         %rdx, (%r10)
	0x48, 0x89, 0xd3, //0x00000a27 movq         %rdx, %rbx
	0x48, 0x8b, 0x4d, 0x90, //0x00000a2a movq         $-112(%rbp), %rcx
	0x48, 0x85, 0xd2, //0x00000a2e testq        %rdx, %rdx
	0x0f, 0x85, 0x79, 0xf8, 0xff, 0xff, //0x00000a31 jne          LBB0_5
	0xe9, 0x7d, 0x1d, 0x00, 0x00, //0x00000a37 jmp          LBB0_498
	//0x00000a3c LBB0_171
	0x49, 0x89, 0xd6, //0x00000a3c movq         %rdx, %r14
	0x4d, 0x29, 0xfe, //0x00000a3f subq         %r15, %r14
	0x49, 0x83, 0xfc, 0xff, //0x00000a42 cmpq         $-1, %r12
	0x0f, 0x85, 0x0e, 0x13, 0x00, 0x00, //0x00000a46 jne          LBB0_359
	0x49, 0xff, 0xce, //0x00000a4c decq         %r14
	0x4d, 0x89, 0xf4, //0x00000a4f movq         %r14, %r12
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000a52 .p2align 4, 0x90
	//0x00000a60 LBB0_173
	0x49, 0x89, 0xd6, //0x00000a60 movq         %rdx, %r14
	0x49, 0xff, 0xca, //0x00000a63 decq         %r10
	0x0f, 0x84, 0x30, 0x12, 0x00, 0x00, //0x00000a66 je           LBB0_348
	//0x00000a6c LBB0_174
	0x41, 0x0f, 0xbe, 0x36, //0x00000a6c movsbl       (%r14), %esi
	0x83, 0xc6, 0xd5, //0x00000a70 addl         $-43, %esi
	0x83, 0xfe, 0x3a, //0x00000a73 cmpl         $58, %esi
	0x0f, 0x87, 0x22, 0x05, 0x00, 0x00, //0x00000a76 ja           LBB0_187
	0x49, 0x8d, 0x56, 0x01, //0x00000a7c leaq         $1(%r14), %rdx
	0x48, 0x8d, 0x3d, 0x51, 0x22, 0x00, 0x00, //0x00000a80 leaq         $8785(%rip), %rdi  /* LJTI0_3+0(%rip) */
	0x48, 0x63, 0x34, 0xb7, //0x00000a87 movslq       (%rdi,%rsi,4), %rsi
	0x48, 0x01, 0xfe, //0x00000a8b addq         %rdi, %rsi
	0xff, 0xe6, //0x00000a8e jmpq         *%rsi
	//0x00000a90 LBB0_176
	0x49, 0x89, 0xd6, //0x00000a90 movq         %rdx, %r14
	0x4d, 0x29, 0xfe, //0x00000a93 subq         %r15, %r14
	0x48, 0x83, 0xf8, 0xff, //0x00000a96 cmpq         $-1, %rax
	0x0f, 0x85, 0xba, 0x12, 0x00, 0x00, //0x00000a9a jne          LBB0_359
	0x49, 0xff, 0xce, //0x00000aa0 decq         %r14
	0x4c, 0x89, 0xf0, //0x00000aa3 movq         %r14, %rax
	0xe9, 0xb5, 0xff, 0xff, 0xff, //0x00000aa6 jmp          LBB0_173
	//0x00000aab LBB0_178
	0x49, 0x89, 0xd6, //0x00000aab movq         %rdx, %r14
	0x4d, 0x29, 0xfe, //0x00000aae subq         %r15, %r14
	0x49, 0x83, 0xfd, 0xff, //0x00000ab1 cmpq         $-1, %r13
	0x0f, 0x85, 0x9f, 0x12, 0x00, 0x00, //0x00000ab5 jne          LBB0_359
	0x49, 0xff, 0xce, //0x00000abb decq         %r14
	0x4d, 0x89, 0xf5, //0x00000abe movq         %r14, %r13
	0xe9, 0x9a, 0xff, 0xff, 0xff, //0x00000ac1 jmp          LBB0_173
	//0x00000ac6 LBB0_73
	0x49, 0x89, 0xd2, //0x00000ac6 movq         %rdx, %r10
	0x4d, 0x29, 0xe2, //0x00000ac9 subq         %r12, %r10
	0x0f, 0x84, 0x85, 0x1e, 0x00, 0x00, //0x00000acc je           LBB0_504
	0x4c, 0x89, 0xe0, //0x00000ad2 movq         %r12, %rax
	0x4d, 0x01, 0xcc, //0x00000ad5 addq         %r9, %r12
	0x49, 0x83, 0xfa, 0x40, //0x00000ad8 cmpq         $64, %r10
	0x4c, 0x89, 0x4d, 0xc8, //0x00000adc movq         %r9, $-56(%rbp)
	0x0f, 0x82, 0x48, 0x13, 0x00, 0x00, //0x00000ae0 jb           LBB0_356
	0x45, 0x89, 0xd6, //0x00000ae6 movl         %r10d, %r14d
	0x41, 0x83, 0xe6, 0x3f, //0x00000ae9 andl         $63, %r14d
	0x48, 0x89, 0xd1, //0x00000aed movq         %rdx, %rcx
	0x4c, 0x29, 0xc1, //0x00000af0 subq         %r8, %rcx
	0x48, 0x83, 0xc1, 0xbf, //0x00000af3 addq         $-65, %rcx
	0x48, 0x83, 0xe1, 0xc0, //0x00000af7 andq         $-64, %rcx
	0x48, 0x01, 0xc1, //0x00000afb addq         %rax, %rcx
	0x49, 0x8d, 0x44, 0x09, 0x40, //0x00000afe leaq         $64(%r9,%rcx), %rax
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00000b03 movq         $-1, %r13
	0x45, 0x31, 0xff, //0x00000b0a xorl         %r15d, %r15d
	0x90, 0x90, 0x90, //0x00000b0d .p2align 4, 0x90
	//0x00000b10 LBB0_76
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x00000b10 vmovdqu      (%r12), %ymm0
	0xc4, 0xc1, 0x7e, 0x6f, 0x4c, 0x24, 0x20, //0x00000b16 vmovdqu      $32(%r12), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x00000b1d vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0x7d, 0xd7, 0xca, //0x00000b21 vpmovmskb    %ymm2, %r9d
	0xc5, 0xf5, 0x74, 0xd6, //0x00000b25 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00000b29 vpmovmskb    %ymm2, %edx
	0xc5, 0xfd, 0x74, 0xd7, //0x00000b2d vpcmpeqb     %ymm7, %ymm0, %ymm2
	0xc5, 0x7d, 0xd7, 0xda, //0x00000b31 vpmovmskb    %ymm2, %r11d
	0xc5, 0xf5, 0x74, 0xd7, //0x00000b35 vpcmpeqb     %ymm7, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xda, //0x00000b39 vpmovmskb    %ymm2, %ebx
	0xc5, 0xbd, 0x64, 0xd0, //0x00000b3d vpcmpgtb     %ymm0, %ymm8, %ymm2
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x00000b41 vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xfd, 0xdb, 0xc2, //0x00000b46 vpand        %ymm2, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00000b4a vpmovmskb    %ymm0, %esi
	0xc5, 0xbd, 0x64, 0xc1, //0x00000b4e vpcmpgtb     %ymm1, %ymm8, %ymm0
	0xc4, 0xc1, 0x75, 0x64, 0xc9, //0x00000b52 vpcmpgtb     %ymm9, %ymm1, %ymm1
	0xc5, 0xf5, 0xdb, 0xc0, //0x00000b57 vpand        %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xf8, //0x00000b5b vpmovmskb    %ymm0, %edi
	0x48, 0xc1, 0xe2, 0x20, //0x00000b5f shlq         $32, %rdx
	0x48, 0xc1, 0xe3, 0x20, //0x00000b63 shlq         $32, %rbx
	0x49, 0x09, 0xdb, //0x00000b67 orq          %rbx, %r11
	0x49, 0x83, 0xfd, 0xff, //0x00000b6a cmpq         $-1, %r13
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00000b6e jne          LBB0_78
	0x4d, 0x85, 0xdb, //0x00000b74 testq        %r11, %r11
	0x0f, 0x85, 0x9f, 0x00, 0x00, 0x00, //0x00000b77 jne          LBB0_87
	//0x00000b7d LBB0_78
	0x48, 0xc1, 0xe7, 0x20, //0x00000b7d shlq         $32, %rdi
	0x4c, 0x09, 0xca, //0x00000b81 orq          %r9, %rdx
	0x4c, 0x89, 0xd9, //0x00000b84 movq         %r11, %rcx
	0x4c, 0x09, 0xf9, //0x00000b87 orq          %r15, %rcx
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x00000b8a jne          LBB0_108
	0x48, 0x09, 0xf7, //0x00000b90 orq          %rsi, %rdi
	0x48, 0x85, 0xd2, //0x00000b93 testq        %rdx, %rdx
	0x0f, 0x85, 0x05, 0x02, 0x00, 0x00, //0x00000b96 jne          LBB0_109
	//0x00000b9c LBB0_80
	0x48, 0x85, 0xff, //0x00000b9c testq        %rdi, %rdi
	0x0f, 0x85, 0x32, 0x1c, 0x00, 0x00, //0x00000b9f jne          LBB0_481
	0x49, 0x83, 0xc2, 0xc0, //0x00000ba5 addq         $-64, %r10
	0x49, 0x83, 0xc4, 0x40, //0x00000ba9 addq         $64, %r12
	0x49, 0x83, 0xfa, 0x3f, //0x00000bad cmpq         $63, %r10
	0x0f, 0x87, 0x59, 0xff, 0xff, 0xff, //0x00000bb1 ja           LBB0_76
	0xe9, 0x12, 0x0f, 0x00, 0x00, //0x00000bb7 jmp          LBB0_82
	//0x00000bbc LBB0_108
	0x4c, 0x89, 0xf9, //0x00000bbc movq         %r15, %rcx
	0x48, 0xf7, 0xd1, //0x00000bbf notq         %rcx
	0x4c, 0x21, 0xd9, //0x00000bc2 andq         %r11, %rcx
	0x4c, 0x8d, 0x0c, 0x09, //0x00000bc5 leaq         (%rcx,%rcx), %r9
	0x4d, 0x09, 0xf9, //0x00000bc9 orq          %r15, %r9
	0x4c, 0x89, 0xcb, //0x00000bcc movq         %r9, %rbx
	0x48, 0xf7, 0xd3, //0x00000bcf notq         %rbx
	0x4c, 0x21, 0xdb, //0x00000bd2 andq         %r11, %rbx
	0x49, 0x89, 0xc3, //0x00000bd5 movq         %rax, %r11
	0x48, 0xb8, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000bd8 movabsq      $-6148914691236517206, %rax
	0x48, 0x21, 0xc3, //0x00000be2 andq         %rax, %rbx
	0x45, 0x31, 0xff, //0x00000be5 xorl         %r15d, %r15d
	0x48, 0x01, 0xcb, //0x00000be8 addq         %rcx, %rbx
	0x41, 0x0f, 0x92, 0xc7, //0x00000beb setb         %r15b
	0x48, 0x01, 0xdb, //0x00000bef addq         %rbx, %rbx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000bf2 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc3, //0x00000bfc xorq         %rax, %rbx
	0x4c, 0x89, 0xd8, //0x00000bff movq         %r11, %rax
	0x4c, 0x21, 0xcb, //0x00000c02 andq         %r9, %rbx
	0x48, 0xf7, 0xd3, //0x00000c05 notq         %rbx
	0x48, 0x21, 0xda, //0x00000c08 andq         %rbx, %rdx
	0x48, 0x09, 0xf7, //0x00000c0b orq          %rsi, %rdi
	0x48, 0x85, 0xd2, //0x00000c0e testq        %rdx, %rdx
	0x0f, 0x84, 0x85, 0xff, 0xff, 0xff, //0x00000c11 je           LBB0_80
	0xe9, 0x85, 0x01, 0x00, 0x00, //0x00000c17 jmp          LBB0_109
	//0x00000c1c LBB0_87
	0x4c, 0x89, 0xe3, //0x00000c1c movq         %r12, %rbx
	0x48, 0x2b, 0x5d, 0xc8, //0x00000c1f subq         $-56(%rbp), %rbx
	0x4d, 0x0f, 0xbc, 0xeb, //0x00000c23 bsfq         %r11, %r13
	0x49, 0x01, 0xdd, //0x00000c27 addq         %rbx, %r13
	0xe9, 0x4e, 0xff, 0xff, 0xff, //0x00000c2a jmp          LBB0_78
	//0x00000c2f LBB0_88
	0x48, 0x81, 0xfb, 0xff, 0x0f, 0x00, 0x00, //0x00000c2f cmpq         $4095, %rbx
	0x0f, 0x8f, 0x16, 0x1b, 0x00, 0x00, //0x00000c36 jg           LBB0_486
	0x48, 0x8d, 0x43, 0x01, //0x00000c3c leaq         $1(%rbx), %rax
	0x49, 0x89, 0x02, //0x00000c40 movq         %rax, (%r10)
	0x49, 0xc7, 0x44, 0xda, 0x08, 0x00, 0x00, 0x00, 0x00, //0x00000c43 movq         $0, $8(%r10,%rbx,8)
	0xe9, 0x1f, 0xf6, 0xff, 0xff, //0x00000c4c jmp          LBB0_3
	//0x00000c51 LBB0_92
	0x49, 0xc7, 0x04, 0xda, 0x02, 0x00, 0x00, 0x00, //0x00000c51 movq         $2, (%r10,%rbx,8)
	0x49, 0x8b, 0x76, 0x08, //0x00000c59 movq         $8(%r14), %rsi
	0xf6, 0x45, 0x98, 0x20, //0x00000c5d testb        $32, $-104(%rbp)
	0x48, 0x89, 0x75, 0xb0, //0x00000c61 movq         %rsi, $-80(%rbp)
	0x0f, 0x85, 0x6e, 0x01, 0x00, 0x00, //0x00000c65 jne          LBB0_111
	0x48, 0x89, 0xf1, //0x00000c6b movq         %rsi, %rcx
	0x4c, 0x29, 0xe1, //0x00000c6e subq         %r12, %rcx
	0x0f, 0x84, 0xe9, 0x1c, 0x00, 0x00, //0x00000c71 je           LBB0_507
	0x4b, 0x8d, 0x1c, 0x21, //0x00000c77 leaq         (%r9,%r12), %rbx
	0x48, 0x83, 0xf9, 0x40, //0x00000c7b cmpq         $64, %rcx
	0x0f, 0x82, 0xe2, 0x11, 0x00, 0x00, //0x00000c7f jb           LBB0_360
	0x4c, 0x89, 0xca, //0x00000c85 movq         %r9, %rdx
	0x41, 0x89, 0xcf, //0x00000c88 movl         %ecx, %r15d
	0x41, 0x83, 0xe7, 0x3f, //0x00000c8b andl         $63, %r15d
	0x48, 0x89, 0xf0, //0x00000c8f movq         %rsi, %rax
	0x4c, 0x29, 0xc0, //0x00000c92 subq         %r8, %rax
	0x48, 0x83, 0xc0, 0xbf, //0x00000c95 addq         $-65, %rax
	0x48, 0x83, 0xe0, 0xc0, //0x00000c99 andq         $-64, %rax
	0x4c, 0x01, 0xe0, //0x00000c9d addq         %r12, %rax
	0x4d, 0x8d, 0x54, 0x01, 0x40, //0x00000ca0 leaq         $64(%r9,%rax), %r10
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00000ca5 movq         $-1, %r11
	0x45, 0x31, 0xf6, //0x00000cac xorl         %r14d, %r14d
	0x90, //0x00000caf .p2align 4, 0x90
	//0x00000cb0 LBB0_96
	0xc5, 0xfe, 0x6f, 0x03, //0x00000cb0 vmovdqu      (%rbx), %ymm0
	0xc5, 0xfe, 0x6f, 0x4b, 0x20, //0x00000cb4 vmovdqu      $32(%rbx), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x00000cb9 vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xf2, //0x00000cbd vpmovmskb    %ymm2, %esi
	0xc5, 0xf5, 0x74, 0xd6, //0x00000cc1 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00000cc5 vpmovmskb    %ymm2, %edx
	0xc5, 0xfd, 0x74, 0xc7, //0x00000cc9 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf8, //0x00000ccd vpmovmskb    %ymm0, %edi
	0xc5, 0xf5, 0x74, 0xc7, //0x00000cd1 vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x00000cd5 vpmovmskb    %ymm0, %eax
	0x48, 0xc1, 0xe2, 0x20, //0x00000cd9 shlq         $32, %rdx
	0x48, 0xc1, 0xe0, 0x20, //0x00000cdd shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x00000ce1 orq          %rax, %rdi
	0x49, 0x83, 0xfb, 0xff, //0x00000ce4 cmpq         $-1, %r11
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00000ce8 jne          LBB0_98
	0x48, 0x85, 0xff, //0x00000cee testq        %rdi, %rdi
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x00000cf1 jne          LBB0_105
	//0x00000cf7 LBB0_98
	0x48, 0x09, 0xf2, //0x00000cf7 orq          %rsi, %rdx
	0x48, 0x89, 0xf8, //0x00000cfa movq         %rdi, %rax
	0x4c, 0x09, 0xf0, //0x00000cfd orq          %r14, %rax
	0x0f, 0x85, 0x3c, 0x00, 0x00, 0x00, //0x00000d00 jne          LBB0_106
	//0x00000d06 LBB0_99
	0x48, 0x85, 0xd2, //0x00000d06 testq        %rdx, %rdx
	0x0f, 0x85, 0xd7, 0x0e, 0x00, 0x00, //0x00000d09 jne          LBB0_107
	//0x00000d0f LBB0_100
	0x48, 0x83, 0xc1, 0xc0, //0x00000d0f addq         $-64, %rcx
	0x48, 0x83, 0xc3, 0x40, //0x00000d13 addq         $64, %rbx
	0x48, 0x83, 0xf9, 0x3f, //0x00000d17 cmpq         $63, %rcx
	0x0f, 0x87, 0x8f, 0xff, 0xff, 0xff, //0x00000d1b ja           LBB0_96
	0xe9, 0x7f, 0x0e, 0x00, 0x00, //0x00000d21 jmp          LBB0_101
	//0x00000d26 LBB0_105
	0x48, 0x89, 0xd8, //0x00000d26 movq         %rbx, %rax
	0x4c, 0x29, 0xc8, //0x00000d29 subq         %r9, %rax
	0x4c, 0x0f, 0xbc, 0xdf, //0x00000d2c bsfq         %rdi, %r11
	0x49, 0x01, 0xc3, //0x00000d30 addq         %rax, %r11
	0x48, 0x09, 0xf2, //0x00000d33 orq          %rsi, %rdx
	0x48, 0x89, 0xf8, //0x00000d36 movq         %rdi, %rax
	0x4c, 0x09, 0xf0, //0x00000d39 orq          %r14, %rax
	0x0f, 0x84, 0xc4, 0xff, 0xff, 0xff, //0x00000d3c je           LBB0_99
	//0x00000d42 LBB0_106
	0x4c, 0x89, 0xf0, //0x00000d42 movq         %r14, %rax
	0x48, 0xf7, 0xd0, //0x00000d45 notq         %rax
	0x48, 0x21, 0xf8, //0x00000d48 andq         %rdi, %rax
	0x4c, 0x89, 0x65, 0xa8, //0x00000d4b movq         %r12, $-88(%rbp)
	0x4c, 0x8d, 0x24, 0x00, //0x00000d4f leaq         (%rax,%rax), %r12
	0x4d, 0x09, 0xf4, //0x00000d53 orq          %r14, %r12
	0x4c, 0x89, 0xe6, //0x00000d56 movq         %r12, %rsi
	0x48, 0xf7, 0xd6, //0x00000d59 notq         %rsi
	0x48, 0x21, 0xfe, //0x00000d5c andq         %rdi, %rsi
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000d5f movabsq      $-6148914691236517206, %rdi
	0x48, 0x21, 0xfe, //0x00000d69 andq         %rdi, %rsi
	0x45, 0x31, 0xf6, //0x00000d6c xorl         %r14d, %r14d
	0x48, 0x01, 0xc6, //0x00000d6f addq         %rax, %rsi
	0x41, 0x0f, 0x92, 0xc6, //0x00000d72 setb         %r14b
	0x48, 0x01, 0xf6, //0x00000d76 addq         %rsi, %rsi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000d79 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc6, //0x00000d83 xorq         %rax, %rsi
	0x4c, 0x21, 0xe6, //0x00000d86 andq         %r12, %rsi
	0x4c, 0x8b, 0x65, 0xa8, //0x00000d89 movq         $-88(%rbp), %r12
	0x48, 0xf7, 0xd6, //0x00000d8d notq         %rsi
	0x48, 0x21, 0xf2, //0x00000d90 andq         %rsi, %rdx
	0x48, 0x85, 0xd2, //0x00000d93 testq        %rdx, %rdx
	0x0f, 0x84, 0x73, 0xff, 0xff, 0xff, //0x00000d96 je           LBB0_100
	0xe9, 0x45, 0x0e, 0x00, 0x00, //0x00000d9c jmp          LBB0_107
	//0x00000da1 LBB0_109
	0x48, 0x0f, 0xbc, 0xca, //0x00000da1 bsfq         %rdx, %rcx
	0x48, 0x85, 0xff, //0x00000da5 testq        %rdi, %rdi
	0x48, 0x8b, 0x45, 0xc8, //0x00000da8 movq         $-56(%rbp), %rax
	0x0f, 0x84, 0xc6, 0x01, 0x00, 0x00, //0x00000dac je           LBB0_181
	0x48, 0x0f, 0xbc, 0xd7, //0x00000db2 bsfq         %rdi, %rdx
	0xe9, 0xc2, 0x01, 0x00, 0x00, //0x00000db6 jmp          LBB0_182
	//0x00000dbb LBB0_180
	0x49, 0x01, 0xcb, //0x00000dbb addq         %rcx, %r11
	0xc5, 0xf8, 0x77, //0x00000dbe vzeroupper   
	0x4d, 0x89, 0xde, //0x00000dc1 movq         %r11, %r14
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000dc4 movq         $-1, %rcx
	0x4d, 0x85, 0xe4, //0x00000dcb testq        %r12, %r12
	0x0f, 0x85, 0xda, 0x01, 0x00, 0x00, //0x00000dce jne          LBB0_188
	0xe9, 0xc8, 0x19, 0x00, 0x00, //0x00000dd4 jmp          LBB0_480
	//0x00000dd9 LBB0_111
	0x48, 0x89, 0xf3, //0x00000dd9 movq         %rsi, %rbx
	0x4c, 0x29, 0xe3, //0x00000ddc subq         %r12, %rbx
	0x0f, 0x84, 0x7b, 0x1b, 0x00, 0x00, //0x00000ddf je           LBB0_507
	0x4c, 0x89, 0xe0, //0x00000de5 movq         %r12, %rax
	0x4d, 0x01, 0xcc, //0x00000de8 addq         %r9, %r12
	0x48, 0x83, 0xfb, 0x40, //0x00000deb cmpq         $64, %rbx
	0x4c, 0x89, 0x4d, 0xc8, //0x00000def movq         %r9, $-56(%rbp)
	0x0f, 0x82, 0x87, 0x10, 0x00, 0x00, //0x00000df3 jb           LBB0_361
	0x41, 0x89, 0xde, //0x00000df9 movl         %ebx, %r14d
	0x41, 0x83, 0xe6, 0x3f, //0x00000dfc andl         $63, %r14d
	0x48, 0x89, 0xf1, //0x00000e00 movq         %rsi, %rcx
	0x4c, 0x29, 0xc1, //0x00000e03 subq         %r8, %rcx
	0x48, 0x83, 0xc1, 0xbf, //0x00000e06 addq         $-65, %rcx
	0x48, 0x83, 0xe1, 0xc0, //0x00000e0a andq         $-64, %rcx
	0x49, 0x89, 0xc2, //0x00000e0e movq         %rax, %r10
	0x48, 0x01, 0xc1, //0x00000e11 addq         %rax, %rcx
	0x49, 0x8d, 0x44, 0x09, 0x40, //0x00000e14 leaq         $64(%r9,%rcx), %rax
	0x48, 0x89, 0x45, 0xa0, //0x00000e19 movq         %rax, $-96(%rbp)
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00000e1d movq         $-1, %r11
	0x45, 0x31, 0xff, //0x00000e24 xorl         %r15d, %r15d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000e27 .p2align 4, 0x90
	//0x00000e30 LBB0_114
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x00000e30 vmovdqu      (%r12), %ymm0
	0xc4, 0xc1, 0x7e, 0x6f, 0x4c, 0x24, 0x20, //0x00000e36 vmovdqu      $32(%r12), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x00000e3d vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0x7d, 0xd7, 0xca, //0x00000e41 vpmovmskb    %ymm2, %r9d
	0xc5, 0xf5, 0x74, 0xd6, //0x00000e45 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00000e49 vpmovmskb    %ymm2, %edx
	0xc5, 0xfd, 0x74, 0xd7, //0x00000e4d vpcmpeqb     %ymm7, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x00000e51 vpmovmskb    %ymm2, %ecx
	0xc5, 0xf5, 0x74, 0xd7, //0x00000e55 vpcmpeqb     %ymm7, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x00000e59 vpmovmskb    %ymm2, %eax
	0xc5, 0xbd, 0x64, 0xd0, //0x00000e5d vpcmpgtb     %ymm0, %ymm8, %ymm2
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x00000e61 vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xfd, 0xdb, 0xc2, //0x00000e66 vpand        %ymm2, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00000e6a vpmovmskb    %ymm0, %esi
	0xc5, 0xbd, 0x64, 0xc1, //0x00000e6e vpcmpgtb     %ymm1, %ymm8, %ymm0
	0xc4, 0xc1, 0x75, 0x64, 0xc9, //0x00000e72 vpcmpgtb     %ymm9, %ymm1, %ymm1
	0xc5, 0xf5, 0xdb, 0xc0, //0x00000e77 vpand        %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xf8, //0x00000e7b vpmovmskb    %ymm0, %edi
	0x48, 0xc1, 0xe2, 0x20, //0x00000e7f shlq         $32, %rdx
	0x48, 0xc1, 0xe0, 0x20, //0x00000e83 shlq         $32, %rax
	0x48, 0x09, 0xc1, //0x00000e87 orq          %rax, %rcx
	0x49, 0x83, 0xfb, 0xff, //0x00000e8a cmpq         $-1, %r11
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00000e8e jne          LBB0_116
	0x48, 0x85, 0xc9, //0x00000e94 testq        %rcx, %rcx
	0x0f, 0x85, 0x9e, 0x00, 0x00, 0x00, //0x00000e97 jne          LBB0_125
	//0x00000e9d LBB0_116
	0x48, 0xc1, 0xe7, 0x20, //0x00000e9d shlq         $32, %rdi
	0x4c, 0x09, 0xca, //0x00000ea1 orq          %r9, %rdx
	0x48, 0x89, 0xc8, //0x00000ea4 movq         %rcx, %rax
	0x4c, 0x09, 0xf8, //0x00000ea7 orq          %r15, %rax
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x00000eaa jne          LBB0_126
	0x48, 0x09, 0xf7, //0x00000eb0 orq          %rsi, %rdi
	0x48, 0x85, 0xd2, //0x00000eb3 testq        %rdx, %rdx
	0x0f, 0x85, 0x92, 0x00, 0x00, 0x00, //0x00000eb6 jne          LBB0_127
	//0x00000ebc LBB0_118
	0x48, 0x85, 0xff, //0x00000ebc testq        %rdi, %rdi
	0x0f, 0x85, 0x40, 0x19, 0x00, 0x00, //0x00000ebf jne          LBB0_499
	0x48, 0x83, 0xc3, 0xc0, //0x00000ec5 addq         $-64, %rbx
	0x49, 0x83, 0xc4, 0x40, //0x00000ec9 addq         $64, %r12
	0x48, 0x83, 0xfb, 0x3f, //0x00000ecd cmpq         $63, %rbx
	0x0f, 0x87, 0x59, 0xff, 0xff, 0xff, //0x00000ed1 ja           LBB0_114
	0xe9, 0x5e, 0x0d, 0x00, 0x00, //0x00000ed7 jmp          LBB0_120
	//0x00000edc LBB0_126
	0x4d, 0x89, 0xfd, //0x00000edc movq         %r15, %r13
	0x49, 0xf7, 0xd5, //0x00000edf notq         %r13
	0x49, 0x21, 0xcd, //0x00000ee2 andq         %rcx, %r13
	0x4f, 0x8d, 0x4c, 0x2d, 0x00, //0x00000ee5 leaq         (%r13,%r13), %r9
	0x4d, 0x09, 0xf9, //0x00000eea orq          %r15, %r9
	0x4c, 0x89, 0xc8, //0x00000eed movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x00000ef0 notq         %rax
	0x48, 0x21, 0xc8, //0x00000ef3 andq         %rcx, %rax
	0x48, 0xb9, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000ef6 movabsq      $-6148914691236517206, %rcx
	0x48, 0x21, 0xc8, //0x00000f00 andq         %rcx, %rax
	0x45, 0x31, 0xff, //0x00000f03 xorl         %r15d, %r15d
	0x4c, 0x01, 0xe8, //0x00000f06 addq         %r13, %rax
	0x4c, 0x8b, 0x6d, 0xc0, //0x00000f09 movq         $-64(%rbp), %r13
	0x41, 0x0f, 0x92, 0xc7, //0x00000f0d setb         %r15b
	0x48, 0x01, 0xc0, //0x00000f11 addq         %rax, %rax
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000f14 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xc8, //0x00000f1e xorq         %rcx, %rax
	0x4c, 0x21, 0xc8, //0x00000f21 andq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x00000f24 notq         %rax
	0x48, 0x21, 0xc2, //0x00000f27 andq         %rax, %rdx
	0x48, 0x09, 0xf7, //0x00000f2a orq          %rsi, %rdi
	0x48, 0x85, 0xd2, //0x00000f2d testq        %rdx, %rdx
	0x0f, 0x84, 0x86, 0xff, 0xff, 0xff, //0x00000f30 je           LBB0_118
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x00000f36 jmp          LBB0_127
	//0x00000f3b LBB0_125
	0x4c, 0x89, 0xe0, //0x00000f3b movq         %r12, %rax
	0x48, 0x2b, 0x45, 0xc8, //0x00000f3e subq         $-56(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xd9, //0x00000f42 bsfq         %rcx, %r11
	0x49, 0x01, 0xc3, //0x00000f46 addq         %rax, %r11
	0xe9, 0x4f, 0xff, 0xff, 0xff, //0x00000f49 jmp          LBB0_116
	//0x00000f4e LBB0_127
	0x48, 0x0f, 0xbc, 0xca, //0x00000f4e bsfq         %rdx, %rcx
	0x48, 0x85, 0xff, //0x00000f52 testq        %rdi, %rdi
	0x48, 0x8b, 0x45, 0xc8, //0x00000f55 movq         $-56(%rbp), %rax
	0x0f, 0x84, 0x8e, 0x01, 0x00, 0x00, //0x00000f59 je           LBB0_203
	0x48, 0x0f, 0xbc, 0xd7, //0x00000f5f bsfq         %rdi, %rdx
	0x4c, 0x8b, 0x75, 0xd0, //0x00000f63 movq         $-48(%rbp), %r14
	0x49, 0x29, 0xc4, //0x00000f67 subq         %rax, %r12
	0x48, 0x39, 0xca, //0x00000f6a cmpq         %rcx, %rdx
	0x0f, 0x83, 0x8f, 0x01, 0x00, 0x00, //0x00000f6d jae          LBB0_204
	0xe9, 0x03, 0x1a, 0x00, 0x00, //0x00000f73 jmp          LBB0_129
	//0x00000f78 LBB0_181
	0xba, 0x40, 0x00, 0x00, 0x00, //0x00000f78 movl         $64, %edx
	//0x00000f7d LBB0_182
	0x4c, 0x8b, 0x55, 0xb8, //0x00000f7d movq         $-72(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xd0, //0x00000f81 movq         $-48(%rbp), %r14
	0x49, 0x29, 0xc4, //0x00000f85 subq         %rax, %r12
	0x48, 0x39, 0xca, //0x00000f88 cmpq         %rcx, %rdx
	0x0f, 0x82, 0xd8, 0x19, 0x00, 0x00, //0x00000f8b jb           LBB0_505
	0x4d, 0x8d, 0x64, 0x0c, 0x01, //0x00000f91 leaq         $1(%r12,%rcx), %r12
	0xe9, 0x0f, 0x0b, 0x00, 0x00, //0x00000f96 jmp          LBB0_184
	//0x00000f9b LBB0_186
	0x49, 0x01, 0xce, //0x00000f9b addq         %rcx, %r14
	//0x00000f9e LBB0_187
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000f9e movq         $-1, %rcx
	0x4d, 0x85, 0xe4, //0x00000fa5 testq        %r12, %r12
	0x0f, 0x84, 0xf3, 0x17, 0x00, 0x00, //0x00000fa8 je           LBB0_480
	//0x00000fae LBB0_188
	0x48, 0x85, 0xc0, //0x00000fae testq        %rax, %rax
	0x0f, 0x84, 0xea, 0x17, 0x00, 0x00, //0x00000fb1 je           LBB0_480
	0x4d, 0x85, 0xed, //0x00000fb7 testq        %r13, %r13
	0x0f, 0x84, 0xe1, 0x17, 0x00, 0x00, //0x00000fba je           LBB0_480
	0x4d, 0x29, 0xfe, //0x00000fc0 subq         %r15, %r14
	0x49, 0x8d, 0x4e, 0xff, //0x00000fc3 leaq         $-1(%r14), %rcx
	0x49, 0x39, 0xcc, //0x00000fc7 cmpq         %rcx, %r12
	0x0f, 0x84, 0x88, 0x00, 0x00, 0x00, //0x00000fca je           LBB0_196
	0x49, 0x39, 0xcd, //0x00000fd0 cmpq         %rcx, %r13
	0x0f, 0x84, 0x7f, 0x00, 0x00, 0x00, //0x00000fd3 je           LBB0_196
	0x48, 0x39, 0xc8, //0x00000fd9 cmpq         %rcx, %rax
	0x0f, 0x84, 0x76, 0x00, 0x00, 0x00, //0x00000fdc je           LBB0_196
	0x48, 0x85, 0xc0, //0x00000fe2 testq        %rax, %rax
	0xc5, 0xfe, 0x6f, 0x2d, 0x13, 0xf0, 0xff, 0xff, //0x00000fe5 vmovdqu      $-4077(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x2b, 0xf0, 0xff, 0xff, //0x00000fed vmovdqu      $-4053(%rip), %ymm6  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x43, 0xf0, 0xff, 0xff, //0x00000ff5 vmovdqu      $-4029(%rip), %ymm7  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x5b, 0xf0, 0xff, 0xff, //0x00000ffd vmovdqu      $-4005(%rip), %ymm8  /* LCPI0_3+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00001005 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x15, 0x6e, 0xf0, 0xff, 0xff, //0x0000100a vmovdqu      $-3986(%rip), %ymm10  /* LCPI0_4+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x86, 0xf0, 0xff, 0xff, //0x00001012 vmovdqu      $-3962(%rip), %ymm11  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x9e, 0xf0, 0xff, 0xff, //0x0000101a vmovdqu      $-3938(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0xb6, 0xf0, 0xff, 0xff, //0x00001022 vmovdqu      $-3914(%rip), %ymm13  /* LCPI0_7+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0xce, 0xf0, 0xff, 0xff, //0x0000102a vmovdqu      $-3890(%rip), %ymm14  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0xe6, 0xf0, 0xff, 0xff, //0x00001032 vmovdqu      $-3866(%rip), %ymm15  /* LCPI0_9+0(%rip) */
	0x0f, 0x8e, 0x8a, 0x00, 0x00, 0x00, //0x0000103a jle          LBB0_200
	0x48, 0x8d, 0x48, 0xff, //0x00001040 leaq         $-1(%rax), %rcx
	0x49, 0x39, 0xcc, //0x00001044 cmpq         %rcx, %r12
	0x0f, 0x84, 0x7d, 0x00, 0x00, 0x00, //0x00001047 je           LBB0_200
	//0x0000104d LBB0_195
	0x48, 0xf7, 0xd0, //0x0000104d notq         %rax
	0x49, 0x89, 0xc6, //0x00001050 movq         %rax, %r14
	0xe9, 0x58, 0x00, 0x00, 0x00, //0x00001053 jmp          LBB0_197
	//0x00001058 LBB0_196
	0x49, 0xf7, 0xde, //0x00001058 negq         %r14
	0xc5, 0xfe, 0x6f, 0x2d, 0x9d, 0xef, 0xff, 0xff, //0x0000105b vmovdqu      $-4195(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0xb5, 0xef, 0xff, 0xff, //0x00001063 vmovdqu      $-4171(%rip), %ymm6  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0xcd, 0xef, 0xff, 0xff, //0x0000106b vmovdqu      $-4147(%rip), %ymm7  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0xe5, 0xef, 0xff, 0xff, //0x00001073 vmovdqu      $-4123(%rip), %ymm8  /* LCPI0_3+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000107b vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x15, 0xf8, 0xef, 0xff, 0xff, //0x00001080 vmovdqu      $-4104(%rip), %ymm10  /* LCPI0_4+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x10, 0xf0, 0xff, 0xff, //0x00001088 vmovdqu      $-4080(%rip), %ymm11  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x28, 0xf0, 0xff, 0xff, //0x00001090 vmovdqu      $-4056(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x40, 0xf0, 0xff, 0xff, //0x00001098 vmovdqu      $-4032(%rip), %ymm13  /* LCPI0_7+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x58, 0xf0, 0xff, 0xff, //0x000010a0 vmovdqu      $-4008(%rip), %ymm14  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0x70, 0xf0, 0xff, 0xff, //0x000010a8 vmovdqu      $-3984(%rip), %ymm15  /* LCPI0_9+0(%rip) */
	//0x000010b0 LBB0_197
	0x4d, 0x85, 0xf6, //0x000010b0 testq        %r14, %r14
	0x4c, 0x8b, 0x6d, 0xc0, //0x000010b3 movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x65, 0xa8, //0x000010b7 movq         $-88(%rbp), %r12
	0x0f, 0x88, 0xdd, 0x16, 0x00, 0x00, //0x000010bb js           LBB0_479
	0x49, 0x8b, 0x4d, 0x00, //0x000010c1 movq         (%r13), %rcx
	0xe9, 0x86, 0xf1, 0xff, 0xff, //0x000010c5 jmp          LBB0_199
	//0x000010ca LBB0_200
	0x4c, 0x89, 0xe9, //0x000010ca movq         %r13, %rcx
	0x4c, 0x09, 0xe1, //0x000010cd orq          %r12, %rcx
	0x4d, 0x39, 0xe5, //0x000010d0 cmpq         %r12, %r13
	0x0f, 0x8c, 0x8f, 0x02, 0x00, 0x00, //0x000010d3 jl           LBB0_249
	0x48, 0x85, 0xc9, //0x000010d9 testq        %rcx, %rcx
	0x0f, 0x88, 0x86, 0x02, 0x00, 0x00, //0x000010dc js           LBB0_249
	0x49, 0xf7, 0xd5, //0x000010e2 notq         %r13
	0x4d, 0x89, 0xee, //0x000010e5 movq         %r13, %r14
	0xe9, 0xc3, 0xff, 0xff, 0xff, //0x000010e8 jmp          LBB0_197
	//0x000010ed LBB0_203
	0xba, 0x40, 0x00, 0x00, 0x00, //0x000010ed movl         $64, %edx
	0x4c, 0x8b, 0x75, 0xd0, //0x000010f2 movq         $-48(%rbp), %r14
	0x49, 0x29, 0xc4, //0x000010f6 subq         %rax, %r12
	0x48, 0x39, 0xca, //0x000010f9 cmpq         %rcx, %rdx
	0x0f, 0x82, 0x79, 0x18, 0x00, 0x00, //0x000010fc jb           LBB0_129
	//0x00001102 LBB0_204
	0x4d, 0x8d, 0x64, 0x0c, 0x01, //0x00001102 leaq         $1(%r12,%rcx), %r12
	0xe9, 0xed, 0x0a, 0x00, 0x00, //0x00001107 jmp          LBB0_205
	//0x0000110c LBB0_209
	0x49, 0x8b, 0x76, 0x08, //0x0000110c movq         $8(%r14), %rsi
	0xf6, 0x45, 0x98, 0x20, //0x00001110 testb        $32, $-104(%rbp)
	0x48, 0x89, 0x75, 0xb0, //0x00001114 movq         %rsi, $-80(%rbp)
	0x0f, 0x85, 0x65, 0x02, 0x00, 0x00, //0x00001118 jne          LBB0_250
	0x48, 0x89, 0xf1, //0x0000111e movq         %rsi, %rcx
	0x4c, 0x29, 0xe1, //0x00001121 subq         %r12, %rcx
	0x0f, 0x84, 0x36, 0x18, 0x00, 0x00, //0x00001124 je           LBB0_507
	0x4b, 0x8d, 0x1c, 0x21, //0x0000112a leaq         (%r9,%r12), %rbx
	0x48, 0x83, 0xf9, 0x40, //0x0000112e cmpq         $64, %rcx
	0x4c, 0x89, 0xca, //0x00001132 movq         %r9, %rdx
	0x0f, 0x82, 0x86, 0x0d, 0x00, 0x00, //0x00001135 jb           LBB0_363
	0x41, 0x89, 0xcf, //0x0000113b movl         %ecx, %r15d
	0x41, 0x83, 0xe7, 0x3f, //0x0000113e andl         $63, %r15d
	0x48, 0x89, 0xf0, //0x00001142 movq         %rsi, %rax
	0x4c, 0x29, 0xc0, //0x00001145 subq         %r8, %rax
	0x48, 0x83, 0xc0, 0xbf, //0x00001148 addq         $-65, %rax
	0x48, 0x83, 0xe0, 0xc0, //0x0000114c andq         $-64, %rax
	0x4c, 0x01, 0xe0, //0x00001150 addq         %r12, %rax
	0x49, 0x89, 0xd1, //0x00001153 movq         %rdx, %r9
	0x4c, 0x8d, 0x54, 0x02, 0x40, //0x00001156 leaq         $64(%rdx,%rax), %r10
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x0000115b movq         $-1, %r11
	0x45, 0x31, 0xf6, //0x00001162 xorl         %r14d, %r14d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001165 .p2align 4, 0x90
	//0x00001170 LBB0_213
	0xc5, 0xfe, 0x6f, 0x03, //0x00001170 vmovdqu      (%rbx), %ymm0
	0xc5, 0xfe, 0x6f, 0x4b, 0x20, //0x00001174 vmovdqu      $32(%rbx), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x00001179 vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xf2, //0x0000117d vpmovmskb    %ymm2, %esi
	0xc5, 0xf5, 0x74, 0xd6, //0x00001181 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00001185 vpmovmskb    %ymm2, %edx
	0xc5, 0xfd, 0x74, 0xc7, //0x00001189 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf8, //0x0000118d vpmovmskb    %ymm0, %edi
	0xc5, 0xf5, 0x74, 0xc7, //0x00001191 vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x00001195 vpmovmskb    %ymm0, %eax
	0x48, 0xc1, 0xe2, 0x20, //0x00001199 shlq         $32, %rdx
	0x48, 0xc1, 0xe0, 0x20, //0x0000119d shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x000011a1 orq          %rax, %rdi
	0x49, 0x83, 0xfb, 0xff, //0x000011a4 cmpq         $-1, %r11
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x000011a8 jne          LBB0_215
	0x48, 0x85, 0xff, //0x000011ae testq        %rdi, %rdi
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x000011b1 jne          LBB0_222
	//0x000011b7 LBB0_215
	0x48, 0x09, 0xf2, //0x000011b7 orq          %rsi, %rdx
	0x48, 0x89, 0xf8, //0x000011ba movq         %rdi, %rax
	0x4c, 0x09, 0xf0, //0x000011bd orq          %r14, %rax
	0x0f, 0x85, 0x3c, 0x00, 0x00, 0x00, //0x000011c0 jne          LBB0_223
	//0x000011c6 LBB0_216
	0x48, 0x85, 0xd2, //0x000011c6 testq        %rdx, %rdx
	0x0f, 0x85, 0x56, 0x0b, 0x00, 0x00, //0x000011c9 jne          LBB0_224
	//0x000011cf LBB0_217
	0x48, 0x83, 0xc1, 0xc0, //0x000011cf addq         $-64, %rcx
	0x48, 0x83, 0xc3, 0x40, //0x000011d3 addq         $64, %rbx
	0x48, 0x83, 0xf9, 0x3f, //0x000011d7 cmpq         $63, %rcx
	0x0f, 0x87, 0x8f, 0xff, 0xff, 0xff, //0x000011db ja           LBB0_213
	0xe9, 0xfe, 0x0a, 0x00, 0x00, //0x000011e1 jmp          LBB0_218
	//0x000011e6 LBB0_222
	0x48, 0x89, 0xd8, //0x000011e6 movq         %rbx, %rax
	0x4c, 0x29, 0xc8, //0x000011e9 subq         %r9, %rax
	0x4c, 0x0f, 0xbc, 0xdf, //0x000011ec bsfq         %rdi, %r11
	0x49, 0x01, 0xc3, //0x000011f0 addq         %rax, %r11
	0x48, 0x09, 0xf2, //0x000011f3 orq          %rsi, %rdx
	0x48, 0x89, 0xf8, //0x000011f6 movq         %rdi, %rax
	0x4c, 0x09, 0xf0, //0x000011f9 orq          %r14, %rax
	0x0f, 0x84, 0xc4, 0xff, 0xff, 0xff, //0x000011fc je           LBB0_216
	//0x00001202 LBB0_223
	0x4c, 0x89, 0xf0, //0x00001202 movq         %r14, %rax
	0x48, 0xf7, 0xd0, //0x00001205 notq         %rax
	0x48, 0x21, 0xf8, //0x00001208 andq         %rdi, %rax
	0x4c, 0x89, 0x65, 0xa8, //0x0000120b movq         %r12, $-88(%rbp)
	0x4c, 0x8d, 0x24, 0x00, //0x0000120f leaq         (%rax,%rax), %r12
	0x4d, 0x09, 0xf4, //0x00001213 orq          %r14, %r12
	0x4c, 0x89, 0xe6, //0x00001216 movq         %r12, %rsi
	0x48, 0xf7, 0xd6, //0x00001219 notq         %rsi
	0x48, 0x21, 0xfe, //0x0000121c andq         %rdi, %rsi
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000121f movabsq      $-6148914691236517206, %rdi
	0x48, 0x21, 0xfe, //0x00001229 andq         %rdi, %rsi
	0x45, 0x31, 0xf6, //0x0000122c xorl         %r14d, %r14d
	0x48, 0x01, 0xc6, //0x0000122f addq         %rax, %rsi
	0x41, 0x0f, 0x92, 0xc6, //0x00001232 setb         %r14b
	0x48, 0x01, 0xf6, //0x00001236 addq         %rsi, %rsi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00001239 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc6, //0x00001243 xorq         %rax, %rsi
	0x4c, 0x21, 0xe6, //0x00001246 andq         %r12, %rsi
	0x4c, 0x8b, 0x65, 0xa8, //0x00001249 movq         $-88(%rbp), %r12
	0x48, 0xf7, 0xd6, //0x0000124d notq         %rsi
	0x48, 0x21, 0xf2, //0x00001250 andq         %rsi, %rdx
	0x48, 0x85, 0xd2, //0x00001253 testq        %rdx, %rdx
	0x0f, 0x84, 0x73, 0xff, 0xff, 0xff, //0x00001256 je           LBB0_217
	0xe9, 0xc4, 0x0a, 0x00, 0x00, //0x0000125c jmp          LBB0_224
	//0x00001261 LBB0_225
	0x4d, 0x8b, 0x76, 0x08, //0x00001261 movq         $8(%r14), %r14
	0x4d, 0x29, 0xe6, //0x00001265 subq         %r12, %r14
	0x0f, 0x84, 0xd3, 0x16, 0x00, 0x00, //0x00001268 je           LBB0_495
	0x4c, 0x89, 0x65, 0xa8, //0x0000126e movq         %r12, $-88(%rbp)
	0x4c, 0x89, 0xc8, //0x00001272 movq         %r9, %rax
	0x4c, 0x01, 0xe0, //0x00001275 addq         %r12, %rax
	0x49, 0x89, 0xc1, //0x00001278 movq         %rax, %r9
	0x80, 0x38, 0x30, //0x0000127b cmpb         $48, (%rax)
	0x0f, 0x85, 0xd6, 0x02, 0x00, 0x00, //0x0000127e jne          LBB0_271
	0x41, 0xbf, 0x01, 0x00, 0x00, 0x00, //0x00001284 movl         $1, %r15d
	0x49, 0x83, 0xfe, 0x01, //0x0000128a cmpq         $1, %r14
	0x0f, 0x85, 0x9b, 0x02, 0x00, 0x00, //0x0000128e jne          LBB0_269
	0x4c, 0x8b, 0x65, 0xa8, //0x00001294 movq         $-88(%rbp), %r12
	0xe9, 0xbf, 0x08, 0x00, 0x00, //0x00001298 jmp          LBB0_343
	//0x0000129d LBB0_229
	0x49, 0x8b, 0x02, //0x0000129d movq         (%r10), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x000012a0 cmpq         $4095, %rax
	0x0f, 0x8f, 0xa6, 0x14, 0x00, 0x00, //0x000012a6 jg           LBB0_486
	0x48, 0x8d, 0x48, 0x01, //0x000012ac leaq         $1(%rax), %rcx
	0x49, 0x89, 0x0a, //0x000012b0 movq         %rcx, (%r10)
	0x49, 0xc7, 0x44, 0xc2, 0x08, 0x05, 0x00, 0x00, 0x00, //0x000012b3 movq         $5, $8(%r10,%rax,8)
	0xe9, 0xaf, 0xef, 0xff, 0xff, //0x000012bc jmp          LBB0_3
	//0x000012c1 LBB0_231
	0x49, 0x8b, 0x56, 0x08, //0x000012c1 movq         $8(%r14), %rdx
	0x48, 0x8d, 0x42, 0xfd, //0x000012c5 leaq         $-3(%rdx), %rax
	0x49, 0x39, 0xc0, //0x000012c9 cmpq         %rax, %r8
	0x0f, 0x83, 0xfc, 0x14, 0x00, 0x00, //0x000012cc jae          LBB0_487
	0x41, 0x81, 0x3f, 0x6e, 0x75, 0x6c, 0x6c, //0x000012d2 cmpl         $1819047278, (%r15)
	0x0f, 0x84, 0xa9, 0xef, 0xff, 0xff, //0x000012d9 je           LBB0_1
	0xe9, 0x4c, 0x15, 0x00, 0x00, //0x000012df jmp          LBB0_233
	//0x000012e4 LBB0_238
	0x49, 0x8b, 0x02, //0x000012e4 movq         (%r10), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x000012e7 cmpq         $4095, %rax
	0x0f, 0x8f, 0x5f, 0x14, 0x00, 0x00, //0x000012ed jg           LBB0_486
	0x48, 0x8d, 0x48, 0x01, //0x000012f3 leaq         $1(%rax), %rcx
	0x49, 0x89, 0x0a, //0x000012f7 movq         %rcx, (%r10)
	0x49, 0xc7, 0x44, 0xc2, 0x08, 0x06, 0x00, 0x00, 0x00, //0x000012fa movq         $6, $8(%r10,%rax,8)
	0xe9, 0x68, 0xef, 0xff, 0xff, //0x00001303 jmp          LBB0_3
	//0x00001308 LBB0_240
	0x49, 0x8b, 0x56, 0x08, //0x00001308 movq         $8(%r14), %rdx
	0x48, 0x8d, 0x42, 0xfc, //0x0000130c leaq         $-4(%rdx), %rax
	0x49, 0x39, 0xc0, //0x00001310 cmpq         %rax, %r8
	0x0f, 0x83, 0xb5, 0x14, 0x00, 0x00, //0x00001313 jae          LBB0_487
	0x43, 0x8b, 0x14, 0x21, //0x00001319 movl         (%r9,%r12), %edx
	0x81, 0xfa, 0x61, 0x6c, 0x73, 0x65, //0x0000131d cmpl         $1702063201, %edx
	0x0f, 0x85, 0x5d, 0x15, 0x00, 0x00, //0x00001323 jne          LBB0_488
	0x4c, 0x89, 0xe0, //0x00001329 movq         %r12, %rax
	0x4d, 0x8d, 0x60, 0x05, //0x0000132c leaq         $5(%r8), %r12
	0x4d, 0x89, 0x65, 0x00, //0x00001330 movq         %r12, (%r13)
	0x4c, 0x89, 0xc1, //0x00001334 movq         %r8, %rcx
	0x48, 0x85, 0xc0, //0x00001337 testq        %rax, %rax
	0x0f, 0x8f, 0x30, 0xef, 0xff, 0xff, //0x0000133a jg           LBB0_3
	0xe9, 0x74, 0x14, 0x00, 0x00, //0x00001340 jmp          LBB0_498
	//0x00001345 LBB0_243
	0x49, 0x8b, 0x56, 0x08, //0x00001345 movq         $8(%r14), %rdx
	0x48, 0x8d, 0x42, 0xfd, //0x00001349 leaq         $-3(%rdx), %rax
	0x49, 0x39, 0xc0, //0x0000134d cmpq         %rax, %r8
	0x0f, 0x83, 0x78, 0x14, 0x00, 0x00, //0x00001350 jae          LBB0_487
	0x41, 0x81, 0x3f, 0x74, 0x72, 0x75, 0x65, //0x00001356 cmpl         $1702195828, (%r15)
	0x0f, 0x84, 0x25, 0xef, 0xff, 0xff, //0x0000135d je           LBB0_1
	0xe9, 0x77, 0x15, 0x00, 0x00, //0x00001363 jmp          LBB0_245
	//0x00001368 LBB0_249
	0x48, 0x85, 0xc9, //0x00001368 testq        %rcx, %rcx
	0x49, 0x8d, 0x44, 0x24, 0xff, //0x0000136b leaq         $-1(%r12), %rax
	0x49, 0xf7, 0xd4, //0x00001370 notq         %r12
	0x4d, 0x0f, 0x48, 0xe6, //0x00001373 cmovsq       %r14, %r12
	0x49, 0x39, 0xc5, //0x00001377 cmpq         %rax, %r13
	0x4d, 0x0f, 0x44, 0xf4, //0x0000137a cmoveq       %r12, %r14
	0xe9, 0x2d, 0xfd, 0xff, 0xff, //0x0000137e jmp          LBB0_197
	//0x00001383 LBB0_250
	0x48, 0x89, 0xf3, //0x00001383 movq         %rsi, %rbx
	0x4c, 0x29, 0xe3, //0x00001386 subq         %r12, %rbx
	0x0f, 0x84, 0xd1, 0x15, 0x00, 0x00, //0x00001389 je           LBB0_507
	0x4c, 0x89, 0xe0, //0x0000138f movq         %r12, %rax
	0x4d, 0x01, 0xcc, //0x00001392 addq         %r9, %r12
	0x48, 0x83, 0xfb, 0x40, //0x00001395 cmpq         $64, %rbx
	0x4c, 0x89, 0x4d, 0xc8, //0x00001399 movq         %r9, $-56(%rbp)
	0x0f, 0x82, 0x3a, 0x0b, 0x00, 0x00, //0x0000139d jb           LBB0_364
	0x41, 0x89, 0xde, //0x000013a3 movl         %ebx, %r14d
	0x41, 0x83, 0xe6, 0x3f, //0x000013a6 andl         $63, %r14d
	0x48, 0x89, 0xf1, //0x000013aa movq         %rsi, %rcx
	0x4c, 0x29, 0xc1, //0x000013ad subq         %r8, %rcx
	0x48, 0x83, 0xc1, 0xbf, //0x000013b0 addq         $-65, %rcx
	0x48, 0x83, 0xe1, 0xc0, //0x000013b4 andq         $-64, %rcx
	0x49, 0x89, 0xc2, //0x000013b8 movq         %rax, %r10
	0x48, 0x01, 0xc1, //0x000013bb addq         %rax, %rcx
	0x49, 0x8d, 0x44, 0x09, 0x40, //0x000013be leaq         $64(%r9,%rcx), %rax
	0x48, 0x89, 0x45, 0xa0, //0x000013c3 movq         %rax, $-96(%rbp)
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000013c7 movq         $-1, %r11
	0x45, 0x31, 0xff, //0x000013ce xorl         %r15d, %r15d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000013d1 .p2align 4, 0x90
	//0x000013e0 LBB0_253
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x000013e0 vmovdqu      (%r12), %ymm0
	0xc4, 0xc1, 0x7e, 0x6f, 0x4c, 0x24, 0x20, //0x000013e6 vmovdqu      $32(%r12), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x000013ed vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0x7d, 0xd7, 0xca, //0x000013f1 vpmovmskb    %ymm2, %r9d
	0xc5, 0xf5, 0x74, 0xd6, //0x000013f5 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x000013f9 vpmovmskb    %ymm2, %edx
	0xc5, 0xfd, 0x74, 0xd7, //0x000013fd vpcmpeqb     %ymm7, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x00001401 vpmovmskb    %ymm2, %ecx
	0xc5, 0xf5, 0x74, 0xd7, //0x00001405 vpcmpeqb     %ymm7, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x00001409 vpmovmskb    %ymm2, %eax
	0xc5, 0xbd, 0x64, 0xd0, //0x0000140d vpcmpgtb     %ymm0, %ymm8, %ymm2
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x00001411 vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xfd, 0xdb, 0xc2, //0x00001416 vpand        %ymm2, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x0000141a vpmovmskb    %ymm0, %esi
	0xc5, 0xbd, 0x64, 0xc1, //0x0000141e vpcmpgtb     %ymm1, %ymm8, %ymm0
	0xc4, 0xc1, 0x75, 0x64, 0xc9, //0x00001422 vpcmpgtb     %ymm9, %ymm1, %ymm1
	0xc5, 0xf5, 0xdb, 0xc0, //0x00001427 vpand        %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xf8, //0x0000142b vpmovmskb    %ymm0, %edi
	0x48, 0xc1, 0xe2, 0x20, //0x0000142f shlq         $32, %rdx
	0x48, 0xc1, 0xe0, 0x20, //0x00001433 shlq         $32, %rax
	0x48, 0x09, 0xc1, //0x00001437 orq          %rax, %rcx
	0x49, 0x83, 0xfb, 0xff, //0x0000143a cmpq         $-1, %r11
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x0000143e jne          LBB0_255
	0x48, 0x85, 0xc9, //0x00001444 testq        %rcx, %rcx
	0x0f, 0x85, 0x9e, 0x00, 0x00, 0x00, //0x00001447 jne          LBB0_264
	//0x0000144d LBB0_255
	0x48, 0xc1, 0xe7, 0x20, //0x0000144d shlq         $32, %rdi
	0x4c, 0x09, 0xca, //0x00001451 orq          %r9, %rdx
	0x48, 0x89, 0xc8, //0x00001454 movq         %rcx, %rax
	0x4c, 0x09, 0xf8, //0x00001457 orq          %r15, %rax
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x0000145a jne          LBB0_265
	0x48, 0x09, 0xf7, //0x00001460 orq          %rsi, %rdi
	0x48, 0x85, 0xd2, //0x00001463 testq        %rdx, %rdx
	0x0f, 0x85, 0x92, 0x00, 0x00, 0x00, //0x00001466 jne          LBB0_266
	//0x0000146c LBB0_257
	0x48, 0x85, 0xff, //0x0000146c testq        %rdi, %rdi
	0x0f, 0x85, 0x90, 0x13, 0x00, 0x00, //0x0000146f jne          LBB0_499
	0x48, 0x83, 0xc3, 0xc0, //0x00001475 addq         $-64, %rbx
	0x49, 0x83, 0xc4, 0x40, //0x00001479 addq         $64, %r12
	0x48, 0x83, 0xfb, 0x3f, //0x0000147d cmpq         $63, %rbx
	0x0f, 0x87, 0x59, 0xff, 0xff, 0xff, //0x00001481 ja           LBB0_253
	0xe9, 0xd6, 0x08, 0x00, 0x00, //0x00001487 jmp          LBB0_259
	//0x0000148c LBB0_265
	0x4d, 0x89, 0xfd, //0x0000148c movq         %r15, %r13
	0x49, 0xf7, 0xd5, //0x0000148f notq         %r13
	0x49, 0x21, 0xcd, //0x00001492 andq         %rcx, %r13
	0x4f, 0x8d, 0x4c, 0x2d, 0x00, //0x00001495 leaq         (%r13,%r13), %r9
	0x4d, 0x09, 0xf9, //0x0000149a orq          %r15, %r9
	0x4c, 0x89, 0xc8, //0x0000149d movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x000014a0 notq         %rax
	0x48, 0x21, 0xc8, //0x000014a3 andq         %rcx, %rax
	0x48, 0xb9, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000014a6 movabsq      $-6148914691236517206, %rcx
	0x48, 0x21, 0xc8, //0x000014b0 andq         %rcx, %rax
	0x45, 0x31, 0xff, //0x000014b3 xorl         %r15d, %r15d
	0x4c, 0x01, 0xe8, //0x000014b6 addq         %r13, %rax
	0x4c, 0x8b, 0x6d, 0xc0, //0x000014b9 movq         $-64(%rbp), %r13
	0x41, 0x0f, 0x92, 0xc7, //0x000014bd setb         %r15b
	0x48, 0x01, 0xc0, //0x000014c1 addq         %rax, %rax
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000014c4 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xc8, //0x000014ce xorq         %rcx, %rax
	0x4c, 0x21, 0xc8, //0x000014d1 andq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x000014d4 notq         %rax
	0x48, 0x21, 0xc2, //0x000014d7 andq         %rax, %rdx
	0x48, 0x09, 0xf7, //0x000014da orq          %rsi, %rdi
	0x48, 0x85, 0xd2, //0x000014dd testq        %rdx, %rdx
	0x0f, 0x84, 0x86, 0xff, 0xff, 0xff, //0x000014e0 je           LBB0_257
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x000014e6 jmp          LBB0_266
	//0x000014eb LBB0_264
	0x4c, 0x89, 0xe0, //0x000014eb movq         %r12, %rax
	0x48, 0x2b, 0x45, 0xc8, //0x000014ee subq         $-56(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xd9, //0x000014f2 bsfq         %rcx, %r11
	0x49, 0x01, 0xc3, //0x000014f6 addq         %rax, %r11
	0xe9, 0x4f, 0xff, 0xff, 0xff, //0x000014f9 jmp          LBB0_255
	//0x000014fe LBB0_266
	0x48, 0x0f, 0xbc, 0xca, //0x000014fe bsfq         %rdx, %rcx
	0x48, 0x85, 0xff, //0x00001502 testq        %rdi, %rdi
	0x48, 0x8b, 0x45, 0xc8, //0x00001505 movq         $-56(%rbp), %rax
	0x0f, 0x84, 0x1f, 0x04, 0x00, 0x00, //0x00001509 je           LBB0_320
	0x48, 0x0f, 0xbc, 0xd7, //0x0000150f bsfq         %rdi, %rdx
	0xe9, 0x1b, 0x04, 0x00, 0x00, //0x00001513 jmp          LBB0_321
	//0x00001518 LBB0_268
	0x4c, 0x89, 0xca, //0x00001518 movq         %r9, %rdx
	0x48, 0xf7, 0xd2, //0x0000151b notq         %rdx
	0x49, 0x01, 0xd0, //0x0000151e addq         %rdx, %r8
	0x49, 0x39, 0xc8, //0x00001521 cmpq         %rcx, %r8
	0x0f, 0x82, 0x3e, 0xef, 0xff, 0xff, //0x00001524 jb           LBB0_36
	0xe9, 0x17, 0x12, 0x00, 0x00, //0x0000152a jmp          LBB0_473
	//0x0000152f LBB0_269
	0x41, 0x8a, 0x49, 0x01, //0x0000152f movb         $1(%r9), %cl
	0x80, 0xc1, 0xd2, //0x00001533 addb         $-46, %cl
	0x80, 0xf9, 0x37, //0x00001536 cmpb         $55, %cl
	0x4c, 0x8b, 0x65, 0xa8, //0x00001539 movq         $-88(%rbp), %r12
	0x0f, 0x87, 0x19, 0x06, 0x00, 0x00, //0x0000153d ja           LBB0_343
	0x0f, 0xb6, 0xc1, //0x00001543 movzbl       %cl, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00001546 movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x00001550 btq          %rax, %rcx
	0x0f, 0x83, 0x02, 0x06, 0x00, 0x00, //0x00001554 jae          LBB0_343
	//0x0000155a LBB0_271
	0x49, 0x83, 0xfe, 0x20, //0x0000155a cmpq         $32, %r14
	0x0f, 0x82, 0x40, 0x09, 0x00, 0x00, //0x0000155e jb           LBB0_362
	0x49, 0x8d, 0x4e, 0xe0, //0x00001564 leaq         $-32(%r14), %rcx
	0x48, 0x89, 0xc8, //0x00001568 movq         %rcx, %rax
	0x48, 0x83, 0xe0, 0xe0, //0x0000156b andq         $-32, %rax
	0x4d, 0x89, 0xca, //0x0000156f movq         %r9, %r10
	0x4e, 0x8d, 0x7c, 0x08, 0x20, //0x00001572 leaq         $32(%rax,%r9), %r15
	0x83, 0xe1, 0x1f, //0x00001577 andl         $31, %ecx
	0x48, 0x89, 0x4d, 0xc8, //0x0000157a movq         %rcx, $-56(%rbp)
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000157e movq         $-1, %rax
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001585 movq         $-1, %r11
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x0000158c movq         $-1, %r12
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001593 .p2align 4, 0x90
	//0x000015a0 LBB0_273
	0xc4, 0xc1, 0x7e, 0x6f, 0x02, //0x000015a0 vmovdqu      (%r10), %ymm0
	0xc4, 0xc1, 0x7d, 0x64, 0xca, //0x000015a5 vpcmpgtb     %ymm10, %ymm0, %ymm1
	0xc5, 0xa5, 0x64, 0xd0, //0x000015aa vpcmpgtb     %ymm0, %ymm11, %ymm2
	0xc5, 0xf5, 0xdb, 0xca, //0x000015ae vpand        %ymm2, %ymm1, %ymm1
	0xc5, 0x9d, 0x74, 0xd0, //0x000015b2 vpcmpeqb     %ymm0, %ymm12, %ymm2
	0xc5, 0x95, 0x74, 0xd8, //0x000015b6 vpcmpeqb     %ymm0, %ymm13, %ymm3
	0xc5, 0xe5, 0xeb, 0xd2, //0x000015ba vpor         %ymm2, %ymm3, %ymm2
	0xc5, 0xbd, 0xeb, 0xd8, //0x000015be vpor         %ymm0, %ymm8, %ymm3
	0xc5, 0x8d, 0x74, 0xc0, //0x000015c2 vpcmpeqb     %ymm0, %ymm14, %ymm0
	0xc5, 0xfd, 0xd7, 0xf8, //0x000015c6 vpmovmskb    %ymm0, %edi
	0xc5, 0x85, 0x74, 0xdb, //0x000015ca vpcmpeqb     %ymm3, %ymm15, %ymm3
	0xc5, 0xfd, 0xd7, 0xd3, //0x000015ce vpmovmskb    %ymm3, %edx
	0xc5, 0xfd, 0xd7, 0xf2, //0x000015d2 vpmovmskb    %ymm2, %esi
	0xc5, 0xe5, 0xeb, 0xc0, //0x000015d6 vpor         %ymm0, %ymm3, %ymm0
	0xc5, 0xed, 0xeb, 0xc9, //0x000015da vpor         %ymm1, %ymm2, %ymm1
	0xc5, 0xfd, 0xeb, 0xc1, //0x000015de vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc8, //0x000015e2 vpmovmskb    %ymm0, %ecx
	0x48, 0xf7, 0xd1, //0x000015e6 notq         %rcx
	0x48, 0x0f, 0xbc, 0xc9, //0x000015e9 bsfq         %rcx, %rcx
	0x83, 0xf9, 0x20, //0x000015ed cmpl         $32, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x000015f0 je           LBB0_275
	0xbb, 0xff, 0xff, 0xff, 0xff, //0x000015f6 movl         $-1, %ebx
	0xd3, 0xe3, //0x000015fb shll         %cl, %ebx
	0xf7, 0xd3, //0x000015fd notl         %ebx
	0x21, 0xdf, //0x000015ff andl         %ebx, %edi
	0x21, 0xda, //0x00001601 andl         %ebx, %edx
	0x21, 0xf3, //0x00001603 andl         %esi, %ebx
	0x89, 0xde, //0x00001605 movl         %ebx, %esi
	//0x00001607 LBB0_275
	0x8d, 0x5f, 0xff, //0x00001607 leal         $-1(%rdi), %ebx
	0x21, 0xfb, //0x0000160a andl         %edi, %ebx
	0x0f, 0x85, 0xa2, 0x06, 0x00, 0x00, //0x0000160c jne          LBB0_349
	0x8d, 0x5a, 0xff, //0x00001612 leal         $-1(%rdx), %ebx
	0x21, 0xd3, //0x00001615 andl         %edx, %ebx
	0x0f, 0x85, 0x97, 0x06, 0x00, 0x00, //0x00001617 jne          LBB0_349
	0x8d, 0x5e, 0xff, //0x0000161d leal         $-1(%rsi), %ebx
	0x21, 0xf3, //0x00001620 andl         %esi, %ebx
	0x0f, 0x85, 0x8c, 0x06, 0x00, 0x00, //0x00001622 jne          LBB0_349
	0x85, 0xff, //0x00001628 testl        %edi, %edi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000162a je           LBB0_281
	0x4c, 0x89, 0xd3, //0x00001630 movq         %r10, %rbx
	0x4c, 0x29, 0xcb, //0x00001633 subq         %r9, %rbx
	0x0f, 0xbc, 0xff, //0x00001636 bsfl         %edi, %edi
	0x48, 0x01, 0xdf, //0x00001639 addq         %rbx, %rdi
	0x49, 0x83, 0xfc, 0xff, //0x0000163c cmpq         $-1, %r12
	0x0f, 0x85, 0x96, 0x07, 0x00, 0x00, //0x00001640 jne          LBB0_353
	0x49, 0x89, 0xfc, //0x00001646 movq         %rdi, %r12
	//0x00001649 LBB0_281
	0x85, 0xd2, //0x00001649 testl        %edx, %edx
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000164b je           LBB0_284
	0x4c, 0x89, 0xd7, //0x00001651 movq         %r10, %rdi
	0x4c, 0x29, 0xcf, //0x00001654 subq         %r9, %rdi
	0x0f, 0xbc, 0xd2, //0x00001657 bsfl         %edx, %edx
	0x48, 0x01, 0xfa, //0x0000165a addq         %rdi, %rdx
	0x49, 0x83, 0xfb, 0xff, //0x0000165d cmpq         $-1, %r11
	0x0f, 0x85, 0x6e, 0x06, 0x00, 0x00, //0x00001661 jne          LBB0_351
	0x49, 0x89, 0xd3, //0x00001667 movq         %rdx, %r11
	//0x0000166a LBB0_284
	0x85, 0xf6, //0x0000166a testl        %esi, %esi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000166c je           LBB0_287
	0x4c, 0x89, 0xd7, //0x00001672 movq         %r10, %rdi
	0x4c, 0x29, 0xcf, //0x00001675 subq         %r9, %rdi
	0x0f, 0xbc, 0xd6, //0x00001678 bsfl         %esi, %edx
	0x48, 0x01, 0xfa, //0x0000167b addq         %rdi, %rdx
	0x48, 0x83, 0xf8, 0xff, //0x0000167e cmpq         $-1, %rax
	0x0f, 0x85, 0x4d, 0x06, 0x00, 0x00, //0x00001682 jne          LBB0_351
	0x48, 0x89, 0xd0, //0x00001688 movq         %rdx, %rax
	//0x0000168b LBB0_287
	0x83, 0xf9, 0x20, //0x0000168b cmpl         $32, %ecx
	0x0f, 0x85, 0x62, 0x02, 0x00, 0x00, //0x0000168e jne          LBB0_493
	0x49, 0x83, 0xc2, 0x20, //0x00001694 addq         $32, %r10
	0x49, 0x83, 0xc6, 0xe0, //0x00001698 addq         $-32, %r14
	0x49, 0x83, 0xfe, 0x1f, //0x0000169c cmpq         $31, %r14
	0x0f, 0x87, 0xfa, 0xfe, 0xff, 0xff, //0x000016a0 ja           LBB0_273
	0xc5, 0xf8, 0x77, //0x000016a6 vzeroupper   
	0x4c, 0x8b, 0x75, 0xc8, //0x000016a9 movq         $-56(%rbp), %r14
	//0x000016ad LBB0_290
	0x49, 0x83, 0xfe, 0x10, //0x000016ad cmpq         $16, %r14
	0xc5, 0xfe, 0x6f, 0x2d, 0x47, 0xe9, 0xff, 0xff, //0x000016b1 vmovdqu      $-5817(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x5f, 0xe9, 0xff, 0xff, //0x000016b9 vmovdqu      $-5793(%rip), %ymm6  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x77, 0xe9, 0xff, 0xff, //0x000016c1 vmovdqu      $-5769(%rip), %ymm7  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x8f, 0xe9, 0xff, 0xff, //0x000016c9 vmovdqu      $-5745(%rip), %ymm8  /* LCPI0_3+0(%rip) */
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000016d1 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x15, 0xa2, 0xe9, 0xff, 0xff, //0x000016d6 vmovdqu      $-5726(%rip), %ymm10  /* LCPI0_4+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0xba, 0xe9, 0xff, 0xff, //0x000016de vmovdqu      $-5702(%rip), %ymm11  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0xd2, 0xe9, 0xff, 0xff, //0x000016e6 vmovdqu      $-5678(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0xea, 0xe9, 0xff, 0xff, //0x000016ee vmovdqu      $-5654(%rip), %ymm13  /* LCPI0_7+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x02, 0xea, 0xff, 0xff, //0x000016f6 vmovdqu      $-5630(%rip), %ymm14  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0x1a, 0xea, 0xff, 0xff, //0x000016fe vmovdqu      $-5606(%rip), %ymm15  /* LCPI0_9+0(%rip) */
	0x0f, 0x82, 0x55, 0x01, 0x00, 0x00, //0x00001706 jb           LBB0_309
	0x4d, 0x8d, 0x56, 0xf0, //0x0000170c leaq         $-16(%r14), %r10
	0x4c, 0x89, 0xd1, //0x00001710 movq         %r10, %rcx
	0x48, 0x83, 0xe1, 0xf0, //0x00001713 andq         $-16, %rcx
	0x4a, 0x8d, 0x4c, 0x39, 0x10, //0x00001717 leaq         $16(%rcx,%r15), %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x0000171c movq         %rcx, $-56(%rbp)
	0x41, 0x83, 0xe2, 0x0f, //0x00001720 andl         $15, %r10d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001724 .p2align 4, 0x90
	//0x00001730 LBB0_292
	0xc4, 0xc1, 0x7a, 0x6f, 0x07, //0x00001730 vmovdqu      (%r15), %xmm0
	0xc5, 0xf9, 0x64, 0x0d, 0x03, 0xea, 0xff, 0xff, //0x00001735 vpcmpgtb     $-5629(%rip), %xmm0, %xmm1  /* LCPI0_10+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x15, 0x0b, 0xea, 0xff, 0xff, //0x0000173d vmovdqu      $-5621(%rip), %xmm2  /* LCPI0_11+0(%rip) */
	0xc5, 0xe9, 0x64, 0xd0, //0x00001745 vpcmpgtb     %xmm0, %xmm2, %xmm2
	0xc5, 0xf1, 0xdb, 0xca, //0x00001749 vpand        %xmm2, %xmm1, %xmm1
	0xc5, 0xf9, 0x74, 0x15, 0x0b, 0xea, 0xff, 0xff, //0x0000174d vpcmpeqb     $-5621(%rip), %xmm0, %xmm2  /* LCPI0_12+0(%rip) */
	0xc5, 0xf9, 0x74, 0x1d, 0x13, 0xea, 0xff, 0xff, //0x00001755 vpcmpeqb     $-5613(%rip), %xmm0, %xmm3  /* LCPI0_13+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xd2, //0x0000175d vpor         %xmm2, %xmm3, %xmm2
	0xc5, 0xf9, 0xeb, 0x1d, 0x17, 0xea, 0xff, 0xff, //0x00001761 vpor         $-5609(%rip), %xmm0, %xmm3  /* LCPI0_14+0(%rip) */
	0xc5, 0xf9, 0x74, 0x05, 0x1f, 0xea, 0xff, 0xff, //0x00001769 vpcmpeqb     $-5601(%rip), %xmm0, %xmm0  /* LCPI0_15+0(%rip) */
	0xc5, 0xe1, 0x74, 0x1d, 0x27, 0xea, 0xff, 0xff, //0x00001771 vpcmpeqb     $-5593(%rip), %xmm3, %xmm3  /* LCPI0_16+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xe0, //0x00001779 vpor         %xmm0, %xmm3, %xmm4
	0xc5, 0xe9, 0xeb, 0xc9, //0x0000177d vpor         %xmm1, %xmm2, %xmm1
	0xc5, 0xd9, 0xeb, 0xc9, //0x00001781 vpor         %xmm1, %xmm4, %xmm1
	0xc5, 0xf9, 0xd7, 0xf8, //0x00001785 vpmovmskb    %xmm0, %edi
	0xc5, 0xf9, 0xd7, 0xf3, //0x00001789 vpmovmskb    %xmm3, %esi
	0xc5, 0xf9, 0xd7, 0xd2, //0x0000178d vpmovmskb    %xmm2, %edx
	0xc5, 0xf9, 0xd7, 0xc9, //0x00001791 vpmovmskb    %xmm1, %ecx
	0xbb, 0xff, 0xff, 0xff, 0xff, //0x00001795 movl         $4294967295, %ebx
	0x48, 0x31, 0xd9, //0x0000179a xorq         %rbx, %rcx
	0x48, 0x0f, 0xbc, 0xc9, //0x0000179d bsfq         %rcx, %rcx
	0x83, 0xf9, 0x10, //0x000017a1 cmpl         $16, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x000017a4 je           LBB0_294
	0xbb, 0xff, 0xff, 0xff, 0xff, //0x000017aa movl         $-1, %ebx
	0xd3, 0xe3, //0x000017af shll         %cl, %ebx
	0xf7, 0xd3, //0x000017b1 notl         %ebx
	0x21, 0xdf, //0x000017b3 andl         %ebx, %edi
	0x21, 0xde, //0x000017b5 andl         %ebx, %esi
	0x21, 0xd3, //0x000017b7 andl         %edx, %ebx
	0x89, 0xda, //0x000017b9 movl         %ebx, %edx
	//0x000017bb LBB0_294
	0x8d, 0x5f, 0xff, //0x000017bb leal         $-1(%rdi), %ebx
	0x21, 0xfb, //0x000017be andl         %edi, %ebx
	0x0f, 0x85, 0xfe, 0x05, 0x00, 0x00, //0x000017c0 jne          LBB0_352
	0x8d, 0x5e, 0xff, //0x000017c6 leal         $-1(%rsi), %ebx
	0x21, 0xf3, //0x000017c9 andl         %esi, %ebx
	0x0f, 0x85, 0xf3, 0x05, 0x00, 0x00, //0x000017cb jne          LBB0_352
	0x8d, 0x5a, 0xff, //0x000017d1 leal         $-1(%rdx), %ebx
	0x21, 0xd3, //0x000017d4 andl         %edx, %ebx
	0x0f, 0x85, 0xe8, 0x05, 0x00, 0x00, //0x000017d6 jne          LBB0_352
	0x85, 0xff, //0x000017dc testl        %edi, %edi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x000017de je           LBB0_300
	0x4c, 0x89, 0xfb, //0x000017e4 movq         %r15, %rbx
	0x4c, 0x29, 0xcb, //0x000017e7 subq         %r9, %rbx
	0x0f, 0xbc, 0xff, //0x000017ea bsfl         %edi, %edi
	0x48, 0x01, 0xdf, //0x000017ed addq         %rbx, %rdi
	0x49, 0x83, 0xfc, 0xff, //0x000017f0 cmpq         $-1, %r12
	0x0f, 0x85, 0xe2, 0x05, 0x00, 0x00, //0x000017f4 jne          LBB0_353
	0x49, 0x89, 0xfc, //0x000017fa movq         %rdi, %r12
	//0x000017fd LBB0_300
	0x85, 0xf6, //0x000017fd testl        %esi, %esi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x000017ff je           LBB0_303
	0x4c, 0x89, 0xff, //0x00001805 movq         %r15, %rdi
	0x4c, 0x29, 0xcf, //0x00001808 subq         %r9, %rdi
	0x0f, 0xbc, 0xf6, //0x0000180b bsfl         %esi, %esi
	0x48, 0x01, 0xfe, //0x0000180e addq         %rdi, %rsi
	0x49, 0x83, 0xfb, 0xff, //0x00001811 cmpq         $-1, %r11
	0x0f, 0x85, 0x3d, 0x06, 0x00, 0x00, //0x00001815 jne          LBB0_358
	0x49, 0x89, 0xf3, //0x0000181b movq         %rsi, %r11
	//0x0000181e LBB0_303
	0x85, 0xd2, //0x0000181e testl        %edx, %edx
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x00001820 je           LBB0_306
	0x4c, 0x89, 0xfe, //0x00001826 movq         %r15, %rsi
	0x4c, 0x29, 0xce, //0x00001829 subq         %r9, %rsi
	0x0f, 0xbc, 0xd2, //0x0000182c bsfl         %edx, %edx
	0x48, 0x01, 0xf2, //0x0000182f addq         %rsi, %rdx
	0x48, 0x83, 0xf8, 0xff, //0x00001832 cmpq         $-1, %rax
	0x0f, 0x85, 0x99, 0x04, 0x00, 0x00, //0x00001836 jne          LBB0_351
	0x48, 0x89, 0xd0, //0x0000183c movq         %rdx, %rax
	//0x0000183f LBB0_306
	0x83, 0xf9, 0x10, //0x0000183f cmpl         $16, %ecx
	0x0f, 0x85, 0x05, 0x01, 0x00, 0x00, //0x00001842 jne          LBB0_325
	0x49, 0x83, 0xc7, 0x10, //0x00001848 addq         $16, %r15
	0x49, 0x83, 0xc6, 0xf0, //0x0000184c addq         $-16, %r14
	0x49, 0x83, 0xfe, 0x0f, //0x00001850 cmpq         $15, %r14
	0x0f, 0x87, 0xd6, 0xfe, 0xff, 0xff, //0x00001854 ja           LBB0_292
	0x4d, 0x89, 0xd6, //0x0000185a movq         %r10, %r14
	0x4c, 0x8b, 0x7d, 0xc8, //0x0000185d movq         $-56(%rbp), %r15
	//0x00001861 LBB0_309
	0x4d, 0x85, 0xf6, //0x00001861 testq        %r14, %r14
	0x4c, 0x8b, 0x55, 0xb8, //0x00001864 movq         $-72(%rbp), %r10
	0x0f, 0x84, 0xe6, 0x00, 0x00, 0x00, //0x00001868 je           LBB0_326
	0x4b, 0x8d, 0x0c, 0x37, //0x0000186e leaq         (%r15,%r14), %rcx
	0xe9, 0x25, 0x00, 0x00, 0x00, //0x00001872 jmp          LBB0_314
	//0x00001877 LBB0_311
	0x49, 0x89, 0xd7, //0x00001877 movq         %rdx, %r15
	0x4d, 0x29, 0xcf, //0x0000187a subq         %r9, %r15
	0x48, 0x83, 0xf8, 0xff, //0x0000187d cmpq         $-1, %rax
	0x0f, 0x85, 0x15, 0x06, 0x00, 0x00, //0x00001881 jne          LBB0_365
	0x49, 0xff, 0xcf, //0x00001887 decq         %r15
	0x4c, 0x89, 0xf8, //0x0000188a movq         %r15, %rax
	0x90, 0x90, 0x90, //0x0000188d .p2align 4, 0x90
	//0x00001890 LBB0_313
	0x49, 0x89, 0xd7, //0x00001890 movq         %rdx, %r15
	0x49, 0xff, 0xce, //0x00001893 decq         %r14
	0x0f, 0x84, 0xab, 0x05, 0x00, 0x00, //0x00001896 je           LBB0_357
	//0x0000189c LBB0_314
	0x41, 0x0f, 0xbe, 0x37, //0x0000189c movsbl       (%r15), %esi
	0x83, 0xc6, 0xd5, //0x000018a0 addl         $-43, %esi
	0x83, 0xfe, 0x3a, //0x000018a3 cmpl         $58, %esi
	0x0f, 0x87, 0xa8, 0x00, 0x00, 0x00, //0x000018a6 ja           LBB0_326
	0x49, 0x8d, 0x57, 0x01, //0x000018ac leaq         $1(%r15), %rdx
	0x48, 0x8d, 0x3d, 0x35, 0x13, 0x00, 0x00, //0x000018b0 leaq         $4917(%rip), %rdi  /* LJTI0_2+0(%rip) */
	0x48, 0x63, 0x34, 0xb7, //0x000018b7 movslq       (%rdi,%rsi,4), %rsi
	0x48, 0x01, 0xfe, //0x000018bb addq         %rdi, %rsi
	0xff, 0xe6, //0x000018be jmpq         *%rsi
	//0x000018c0 LBB0_316
	0x49, 0x89, 0xd7, //0x000018c0 movq         %rdx, %r15
	0x4d, 0x29, 0xcf, //0x000018c3 subq         %r9, %r15
	0x49, 0x83, 0xfb, 0xff, //0x000018c6 cmpq         $-1, %r11
	0x0f, 0x85, 0xcc, 0x05, 0x00, 0x00, //0x000018ca jne          LBB0_365
	0x49, 0xff, 0xcf, //0x000018d0 decq         %r15
	0x4d, 0x89, 0xfb, //0x000018d3 movq         %r15, %r11
	0xe9, 0xb5, 0xff, 0xff, 0xff, //0x000018d6 jmp          LBB0_313
	//0x000018db LBB0_318
	0x49, 0x89, 0xd7, //0x000018db movq         %rdx, %r15
	0x4d, 0x29, 0xcf, //0x000018de subq         %r9, %r15
	0x49, 0x83, 0xfc, 0xff, //0x000018e1 cmpq         $-1, %r12
	0x0f, 0x85, 0xb1, 0x05, 0x00, 0x00, //0x000018e5 jne          LBB0_365
	0x49, 0xff, 0xcf, //0x000018eb decq         %r15
	0x4d, 0x89, 0xfc, //0x000018ee movq         %r15, %r12
	0xe9, 0x9a, 0xff, 0xff, 0xff, //0x000018f1 jmp          LBB0_313
	//0x000018f6 LBB0_493
	0x49, 0x01, 0xca, //0x000018f6 addq         %rcx, %r10
	0xc5, 0xf8, 0x77, //0x000018f9 vzeroupper   
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000018fc vpcmpeqd     %ymm9, %ymm9, %ymm9
	0xc5, 0xfe, 0x6f, 0x3d, 0x37, 0xe7, 0xff, 0xff, //0x00001901 vmovdqu      $-6345(%rip), %ymm7  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x0f, 0xe7, 0xff, 0xff, //0x00001909 vmovdqu      $-6385(%rip), %ymm6  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x2d, 0xe7, 0xe6, 0xff, 0xff, //0x00001911 vmovdqu      $-6425(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0x4d, 0x89, 0xd7, //0x00001919 movq         %r10, %r15
	0x4c, 0x8b, 0x55, 0xb8, //0x0000191c movq         $-72(%rbp), %r10
	0x4d, 0x85, 0xdb, //0x00001920 testq        %r11, %r11
	0x0f, 0x85, 0x34, 0x00, 0x00, 0x00, //0x00001923 jne          LBB0_327
	0xe9, 0x0f, 0x10, 0x00, 0x00, //0x00001929 jmp          LBB0_494
	//0x0000192e LBB0_320
	0xba, 0x40, 0x00, 0x00, 0x00, //0x0000192e movl         $64, %edx
	//0x00001933 LBB0_321
	0x4c, 0x8b, 0x75, 0xd0, //0x00001933 movq         $-48(%rbp), %r14
	0x49, 0x29, 0xc4, //0x00001937 subq         %rax, %r12
	0x48, 0x39, 0xca, //0x0000193a cmpq         %rcx, %rdx
	0x0f, 0x82, 0x38, 0x10, 0x00, 0x00, //0x0000193d jb           LBB0_129
	//0x00001943 LBB0_322
	0x4d, 0x8d, 0x64, 0x0c, 0x01, //0x00001943 leaq         $1(%r12,%rcx), %r12
	0xe9, 0xeb, 0x03, 0x00, 0x00, //0x00001948 jmp          LBB0_323
	//0x0000194d LBB0_325
	0x49, 0x01, 0xcf, //0x0000194d addq         %rcx, %r15
	0x4c, 0x8b, 0x55, 0xb8, //0x00001950 movq         $-72(%rbp), %r10
	//0x00001954 LBB0_326
	0x4d, 0x85, 0xdb, //0x00001954 testq        %r11, %r11
	0x0f, 0x84, 0xe0, 0x0f, 0x00, 0x00, //0x00001957 je           LBB0_494
	//0x0000195d LBB0_327
	0x48, 0x85, 0xc0, //0x0000195d testq        %rax, %rax
	0x0f, 0x84, 0xd7, 0x0f, 0x00, 0x00, //0x00001960 je           LBB0_494
	0x4d, 0x85, 0xe4, //0x00001966 testq        %r12, %r12
	0x0f, 0x84, 0xce, 0x0f, 0x00, 0x00, //0x00001969 je           LBB0_494
	0x4d, 0x29, 0xcf, //0x0000196f subq         %r9, %r15
	0x49, 0x8d, 0x4f, 0xff, //0x00001972 leaq         $-1(%r15), %rcx
	0x49, 0x39, 0xcb, //0x00001976 cmpq         %rcx, %r11
	0x0f, 0x84, 0x6b, 0x00, 0x00, 0x00, //0x00001979 je           LBB0_335
	0x49, 0x39, 0xcc, //0x0000197f cmpq         %rcx, %r12
	0x0f, 0x84, 0x62, 0x00, 0x00, 0x00, //0x00001982 je           LBB0_335
	0x48, 0x39, 0xc8, //0x00001988 cmpq         %rcx, %rax
	0x0f, 0x84, 0x59, 0x00, 0x00, 0x00, //0x0000198b je           LBB0_335
	0x48, 0x85, 0xc0, //0x00001991 testq        %rax, %rax
	0xc5, 0x7e, 0x6f, 0x05, 0xc4, 0xe6, 0xff, 0xff, //0x00001994 vmovdqu      $-6460(%rip), %ymm8  /* LCPI0_3+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0xdc, 0xe6, 0xff, 0xff, //0x0000199c vmovdqu      $-6436(%rip), %ymm10  /* LCPI0_4+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0xf4, 0xe6, 0xff, 0xff, //0x000019a4 vmovdqu      $-6412(%rip), %ymm11  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x0c, 0xe7, 0xff, 0xff, //0x000019ac vmovdqu      $-6388(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x24, 0xe7, 0xff, 0xff, //0x000019b4 vmovdqu      $-6364(%rip), %ymm13  /* LCPI0_7+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x3c, 0xe7, 0xff, 0xff, //0x000019bc vmovdqu      $-6340(%rip), %ymm14  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0x54, 0xe7, 0xff, 0xff, //0x000019c4 vmovdqu      $-6316(%rip), %ymm15  /* LCPI0_9+0(%rip) */
	0x0f, 0x8e, 0x58, 0x00, 0x00, 0x00, //0x000019cc jle          LBB0_336
	0x48, 0x8d, 0x48, 0xff, //0x000019d2 leaq         $-1(%rax), %rcx
	0x49, 0x39, 0xcb, //0x000019d6 cmpq         %rcx, %r11
	0x0f, 0x84, 0x4b, 0x00, 0x00, 0x00, //0x000019d9 je           LBB0_336
	0x48, 0xf7, 0xd0, //0x000019df notq         %rax
	0x49, 0x89, 0xc7, //0x000019e2 movq         %rax, %r15
	0xe9, 0x65, 0x01, 0x00, 0x00, //0x000019e5 jmp          LBB0_342
	//0x000019ea LBB0_335
	0x49, 0xf7, 0xdf, //0x000019ea negq         %r15
	0xc5, 0x7e, 0x6f, 0x05, 0x6b, 0xe6, 0xff, 0xff, //0x000019ed vmovdqu      $-6549(%rip), %ymm8  /* LCPI0_3+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0x83, 0xe6, 0xff, 0xff, //0x000019f5 vmovdqu      $-6525(%rip), %ymm10  /* LCPI0_4+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x9b, 0xe6, 0xff, 0xff, //0x000019fd vmovdqu      $-6501(%rip), %ymm11  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0xb3, 0xe6, 0xff, 0xff, //0x00001a05 vmovdqu      $-6477(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0xcb, 0xe6, 0xff, 0xff, //0x00001a0d vmovdqu      $-6453(%rip), %ymm13  /* LCPI0_7+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0xe3, 0xe6, 0xff, 0xff, //0x00001a15 vmovdqu      $-6429(%rip), %ymm14  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0xfb, 0xe6, 0xff, 0xff, //0x00001a1d vmovdqu      $-6405(%rip), %ymm15  /* LCPI0_9+0(%rip) */
	0xe9, 0x25, 0x01, 0x00, 0x00, //0x00001a25 jmp          LBB0_342
	//0x00001a2a LBB0_336
	0x4c, 0x89, 0xe1, //0x00001a2a movq         %r12, %rcx
	0x4c, 0x09, 0xd9, //0x00001a2d orq          %r11, %rcx
	0x4d, 0x39, 0xdc, //0x00001a30 cmpq         %r11, %r12
	0x0f, 0x8c, 0x01, 0x01, 0x00, 0x00, //0x00001a33 jl           LBB0_341
	0x48, 0x85, 0xc9, //0x00001a39 testq        %rcx, %rcx
	0x0f, 0x88, 0xf8, 0x00, 0x00, 0x00, //0x00001a3c js           LBB0_341
	0x49, 0xf7, 0xd4, //0x00001a42 notq         %r12
	0x4d, 0x89, 0xe7, //0x00001a45 movq         %r12, %r15
	0xe9, 0x02, 0x01, 0x00, 0x00, //0x00001a48 jmp          LBB0_342
	//0x00001a4d LBB0_339
	0x4d, 0x29, 0xfb, //0x00001a4d subq         %r15, %r11
	0x44, 0x0f, 0xbc, 0xf3, //0x00001a50 bsfl         %ebx, %r14d
	0xe9, 0x36, 0x01, 0x00, 0x00, //0x00001a54 jmp          LBB0_346
	//0x00001a59 LBB0_55
	0x4c, 0x89, 0xf9, //0x00001a59 movq         %r15, %rcx
	0x4c, 0x89, 0xcb, //0x00001a5c movq         %r9, %rbx
	0x48, 0x83, 0xf9, 0x20, //0x00001a5f cmpq         $32, %rcx
	0x0f, 0x82, 0xfd, 0x04, 0x00, 0x00, //0x00001a63 jb           LBB0_370
	//0x00001a69 LBB0_56
	0xc5, 0xfe, 0x6f, 0x03, //0x00001a69 vmovdqu      (%rbx), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001a6d vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xd1, //0x00001a71 vpmovmskb    %ymm1, %edx
	0xc5, 0xfd, 0x74, 0xc7, //0x00001a75 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00001a79 vpmovmskb    %ymm0, %esi
	0x85, 0xf6, //0x00001a7d testl        %esi, %esi
	0x0f, 0x85, 0x78, 0x04, 0x00, 0x00, //0x00001a7f jne          LBB0_366
	0x4d, 0x85, 0xf6, //0x00001a85 testq        %r14, %r14
	0x0f, 0x85, 0x86, 0x04, 0x00, 0x00, //0x00001a88 jne          LBB0_368
	0x45, 0x31, 0xf6, //0x00001a8e xorl         %r14d, %r14d
	0x48, 0x85, 0xd2, //0x00001a91 testq        %rdx, %rdx
	0x0f, 0x84, 0xc4, 0x04, 0x00, 0x00, //0x00001a94 je           LBB0_369
	//0x00001a9a LBB0_61
	0x48, 0x0f, 0xbc, 0xc2, //0x00001a9a bsfq         %rdx, %rax
	0x4c, 0x29, 0xdb, //0x00001a9e subq         %r11, %rbx
	0x4c, 0x8d, 0x64, 0x03, 0x01, //0x00001aa1 leaq         $1(%rbx,%rax), %r12
	0x4c, 0x8b, 0x75, 0xd0, //0x00001aa6 movq         $-48(%rbp), %r14
	//0x00001aaa LBB0_184
	0x4d, 0x85, 0xe4, //0x00001aaa testq        %r12, %r12
	0x0f, 0x88, 0xab, 0x0c, 0x00, 0x00, //0x00001aad js           LBB0_474
	0x4c, 0x8b, 0x6d, 0xc0, //0x00001ab3 movq         $-64(%rbp), %r13
	0x4d, 0x89, 0x65, 0x00, //0x00001ab7 movq         %r12, (%r13)
	0x4c, 0x89, 0xc1, //0x00001abb movq         %r8, %rcx
	0x48, 0x83, 0x7d, 0xa8, 0x00, //0x00001abe cmpq         $0, $-88(%rbp)
	0x0f, 0x8f, 0xa7, 0xe7, 0xff, 0xff, //0x00001ac3 jg           LBB0_3
	0xe9, 0xeb, 0x0c, 0x00, 0x00, //0x00001ac9 jmp          LBB0_498
	//0x00001ace LBB0_82
	0x4d, 0x89, 0xf2, //0x00001ace movq         %r14, %r10
	0x49, 0x89, 0xc4, //0x00001ad1 movq         %rax, %r12
	0x4c, 0x8b, 0x75, 0xd0, //0x00001ad4 movq         $-48(%rbp), %r14
	0x49, 0x83, 0xfa, 0x20, //0x00001ad8 cmpq         $32, %r10
	0x0f, 0x82, 0xbc, 0x05, 0x00, 0x00, //0x00001adc jb           LBB0_388
	//0x00001ae2 LBB0_83
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x00001ae2 vmovdqu      (%r12), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001ae8 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x00001aec vpmovmskb    %ymm1, %esi
	0xc5, 0xfd, 0x74, 0xcf, //0x00001af0 vpcmpeqb     %ymm7, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xc9, //0x00001af4 vpmovmskb    %ymm1, %ecx
	0xc5, 0xbd, 0x64, 0xc8, //0x00001af8 vpcmpgtb     %ymm0, %ymm8, %ymm1
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x00001afc vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xfd, 0xdb, 0xc1, //0x00001b01 vpand        %ymm1, %ymm0, %ymm0
	0xc5, 0x7d, 0xd7, 0xc8, //0x00001b05 vpmovmskb    %ymm0, %r9d
	0x85, 0xc9, //0x00001b09 testl        %ecx, %ecx
	0x0f, 0x85, 0xe4, 0x04, 0x00, 0x00, //0x00001b0b jne          LBB0_379
	0x4d, 0x85, 0xff, //0x00001b11 testq        %r15, %r15
	0x0f, 0x85, 0xf3, 0x04, 0x00, 0x00, //0x00001b14 jne          LBB0_381
	0x45, 0x31, 0xff, //0x00001b1a xorl         %r15d, %r15d
	0x48, 0x85, 0xf6, //0x00001b1d testq        %rsi, %rsi
	0x0f, 0x84, 0x2e, 0x05, 0x00, 0x00, //0x00001b20 je           LBB0_382
	//0x00001b26 LBB0_86
	0x48, 0x0f, 0xbc, 0xce, //0x00001b26 bsfq         %rsi, %rcx
	0xe9, 0x2a, 0x05, 0x00, 0x00, //0x00001b2a jmp          LBB0_383
	//0x00001b2f LBB0_340
	0x48, 0xf7, 0xd2, //0x00001b2f notq         %rdx
	0x49, 0x89, 0xd6, //0x00001b32 movq         %rdx, %r14
	0xe9, 0x76, 0xf5, 0xff, 0xff, //0x00001b35 jmp          LBB0_197
	//0x00001b3a LBB0_341
	0x48, 0x85, 0xc9, //0x00001b3a testq        %rcx, %rcx
	0x49, 0x8d, 0x43, 0xff, //0x00001b3d leaq         $-1(%r11), %rax
	0x49, 0xf7, 0xd3, //0x00001b41 notq         %r11
	0x4d, 0x0f, 0x48, 0xdf, //0x00001b44 cmovsq       %r15, %r11
	0x49, 0x39, 0xc4, //0x00001b48 cmpq         %rax, %r12
	0x4d, 0x0f, 0x44, 0xfb, //0x00001b4b cmoveq       %r11, %r15
	//0x00001b4f LBB0_342
	0x4d, 0x8b, 0x65, 0x00, //0x00001b4f movq         (%r13), %r12
	0x4d, 0x85, 0xff, //0x00001b53 testq        %r15, %r15
	0x0f, 0x88, 0xec, 0x0d, 0x00, 0x00, //0x00001b56 js           LBB0_496
	//0x00001b5c LBB0_343
	0x4d, 0x01, 0xfc, //0x00001b5c addq         %r15, %r12
	0x4d, 0x89, 0x65, 0x00, //0x00001b5f movq         %r12, (%r13)
	0x4c, 0x89, 0xc1, //0x00001b63 movq         %r8, %rcx
	0x48, 0x83, 0x7d, 0xa8, 0x00, //0x00001b66 cmpq         $0, $-88(%rbp)
	0x4c, 0x8b, 0x75, 0xd0, //0x00001b6b movq         $-48(%rbp), %r14
	0x0f, 0x8f, 0xfb, 0xe6, 0xff, 0xff, //0x00001b6f jg           LBB0_3
	0xe9, 0x3f, 0x0c, 0x00, 0x00, //0x00001b75 jmp          LBB0_498
	//0x00001b7a LBB0_344
	0x4d, 0x29, 0xfe, //0x00001b7a subq         %r15, %r14
	0x0f, 0xbc, 0xc3, //0x00001b7d bsfl         %ebx, %eax
	0x4c, 0x01, 0xf0, //0x00001b80 addq         %r14, %rax
	0xe9, 0xc5, 0xf4, 0xff, 0xff, //0x00001b83 jmp          LBB0_195
	//0x00001b88 LBB0_345
	0x4d, 0x29, 0xfb, //0x00001b88 subq         %r15, %r11
	0x45, 0x0f, 0xbc, 0xf1, //0x00001b8b bsfl         %r9d, %r14d
	//0x00001b8f LBB0_346
	0x4d, 0x01, 0xde, //0x00001b8f addq         %r11, %r14
	0x49, 0xf7, 0xd6, //0x00001b92 notq         %r14
	0xe9, 0x16, 0xf5, 0xff, 0xff, //0x00001b95 jmp          LBB0_197
	//0x00001b9a LBB0_347
	0x48, 0xf7, 0xd7, //0x00001b9a notq         %rdi
	0x49, 0x89, 0xfe, //0x00001b9d movq         %rdi, %r14
	0xe9, 0x0b, 0xf5, 0xff, 0xff, //0x00001ba0 jmp          LBB0_197
	//0x00001ba5 LBB0_101
	0x4c, 0x89, 0xf9, //0x00001ba5 movq         %r15, %rcx
	0x4c, 0x89, 0xd3, //0x00001ba8 movq         %r10, %rbx
	0x48, 0x83, 0xf9, 0x20, //0x00001bab cmpq         $32, %rcx
	0x0f, 0x82, 0x00, 0x06, 0x00, 0x00, //0x00001baf jb           LBB0_402
	//0x00001bb5 LBB0_102
	0xc5, 0xfe, 0x6f, 0x03, //0x00001bb5 vmovdqu      (%rbx), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001bb9 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xd1, //0x00001bbd vpmovmskb    %ymm1, %edx
	0xc5, 0xfd, 0x74, 0xc7, //0x00001bc1 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00001bc5 vpmovmskb    %ymm0, %esi
	0x85, 0xf6, //0x00001bc9 testl        %esi, %esi
	0x0f, 0x85, 0x6d, 0x05, 0x00, 0x00, //0x00001bcb jne          LBB0_397
	0x4d, 0x85, 0xf6, //0x00001bd1 testq        %r14, %r14
	0x0f, 0x85, 0x83, 0x05, 0x00, 0x00, //0x00001bd4 jne          LBB0_399
	0x45, 0x31, 0xf6, //0x00001bda xorl         %r14d, %r14d
	0x48, 0x85, 0xd2, //0x00001bdd testq        %rdx, %rdx
	0x0f, 0x84, 0xc7, 0x05, 0x00, 0x00, //0x00001be0 je           LBB0_401
	//0x00001be6 LBB0_107
	0x4d, 0x89, 0xe2, //0x00001be6 movq         %r12, %r10
	0x48, 0x0f, 0xbc, 0xc2, //0x00001be9 bsfq         %rdx, %rax
	0x4c, 0x29, 0xcb, //0x00001bed subq         %r9, %rbx
	0x4c, 0x8d, 0x64, 0x03, 0x01, //0x00001bf0 leaq         $1(%rbx,%rax), %r12
	0x4c, 0x8b, 0x75, 0xd0, //0x00001bf5 movq         $-48(%rbp), %r14
	//0x00001bf9 LBB0_205
	0x4d, 0x85, 0xe4, //0x00001bf9 testq        %r12, %r12
	0x0f, 0x88, 0x82, 0x0b, 0x00, 0x00, //0x00001bfc js           LBB0_477
	0x4d, 0x89, 0x65, 0x00, //0x00001c02 movq         %r12, (%r13)
	0x4c, 0x89, 0xc1, //0x00001c06 movq         %r8, %rcx
	0x4d, 0x85, 0xd2, //0x00001c09 testq        %r10, %r10
	0x4c, 0x8b, 0x55, 0xb8, //0x00001c0c movq         $-72(%rbp), %r10
	0x0f, 0x8e, 0xa3, 0x0b, 0x00, 0x00, //0x00001c10 jle          LBB0_498
	0x49, 0x8b, 0x02, //0x00001c16 movq         (%r10), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00001c19 cmpq         $4095, %rax
	0x0f, 0x8f, 0x2d, 0x0b, 0x00, 0x00, //0x00001c1f jg           LBB0_486
	0x48, 0x8d, 0x48, 0x01, //0x00001c25 leaq         $1(%rax), %rcx
	0x49, 0x89, 0x0a, //0x00001c29 movq         %rcx, (%r10)
	0x49, 0xc7, 0x44, 0xc2, 0x08, 0x04, 0x00, 0x00, 0x00, //0x00001c2c movq         $4, $8(%r10,%rax,8)
	0xe9, 0x36, 0xe6, 0xff, 0xff, //0x00001c35 jmp          LBB0_3
	//0x00001c3a LBB0_120
	0x4c, 0x89, 0xf3, //0x00001c3a movq         %r14, %rbx
	0x4c, 0x8b, 0x65, 0xa0, //0x00001c3d movq         $-96(%rbp), %r12
	0x4c, 0x8b, 0x75, 0xd0, //0x00001c41 movq         $-48(%rbp), %r14
	0x48, 0x83, 0xfb, 0x20, //0x00001c45 cmpq         $32, %rbx
	0x0f, 0x82, 0x9c, 0x06, 0x00, 0x00, //0x00001c49 jb           LBB0_420
	//0x00001c4f LBB0_121
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x00001c4f vmovdqu      (%r12), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001c55 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x00001c59 vpmovmskb    %ymm1, %esi
	0xc5, 0xfd, 0x74, 0xcf, //0x00001c5d vpcmpeqb     %ymm7, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xc9, //0x00001c61 vpmovmskb    %ymm1, %ecx
	0xc5, 0xbd, 0x64, 0xc8, //0x00001c65 vpcmpgtb     %ymm0, %ymm8, %ymm1
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x00001c69 vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xfd, 0xdb, 0xc1, //0x00001c6e vpand        %ymm1, %ymm0, %ymm0
	0xc5, 0x7d, 0xd7, 0xc8, //0x00001c72 vpmovmskb    %ymm0, %r9d
	0x85, 0xc9, //0x00001c76 testl        %ecx, %ecx
	0x0f, 0x85, 0xc9, 0x05, 0x00, 0x00, //0x00001c78 jne          LBB0_411
	0x4d, 0x85, 0xff, //0x00001c7e testq        %r15, %r15
	0x0f, 0x85, 0xd8, 0x05, 0x00, 0x00, //0x00001c81 jne          LBB0_413
	0x45, 0x31, 0xff, //0x00001c87 xorl         %r15d, %r15d
	0x48, 0x85, 0xf6, //0x00001c8a testq        %rsi, %rsi
	0x0f, 0x84, 0x17, 0x06, 0x00, 0x00, //0x00001c8d je           LBB0_414
	//0x00001c93 LBB0_124
	0x48, 0x0f, 0xbc, 0xce, //0x00001c93 bsfq         %rsi, %rcx
	0xe9, 0x13, 0x06, 0x00, 0x00, //0x00001c97 jmp          LBB0_415
	//0x00001c9c LBB0_348
	0x49, 0x89, 0xce, //0x00001c9c movq         %rcx, %r14
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001c9f movq         $-1, %rcx
	0x4d, 0x85, 0xe4, //0x00001ca6 testq        %r12, %r12
	0x0f, 0x85, 0xff, 0xf2, 0xff, 0xff, //0x00001ca9 jne          LBB0_188
	0xe9, 0xed, 0x0a, 0x00, 0x00, //0x00001caf jmp          LBB0_480
	//0x00001cb4 LBB0_349
	0x4d, 0x29, 0xca, //0x00001cb4 subq         %r9, %r10
	0x44, 0x0f, 0xbc, 0xfb, //0x00001cb7 bsfl         %ebx, %r15d
	0x4d, 0x01, 0xd7, //0x00001cbb addq         %r10, %r15
	0x49, 0xf7, 0xd7, //0x00001cbe notq         %r15
	0x4c, 0x8b, 0x55, 0xb8, //0x00001cc1 movq         $-72(%rbp), %r10
	0xe9, 0x85, 0xfe, 0xff, 0xff, //0x00001cc5 jmp          LBB0_342
	//0x00001cca LBB0_350
	0x48, 0xf7, 0xd6, //0x00001cca notq         %rsi
	0x49, 0x89, 0xf6, //0x00001ccd movq         %rsi, %r14
	0xe9, 0xdb, 0xf3, 0xff, 0xff, //0x00001cd0 jmp          LBB0_197
	//0x00001cd5 LBB0_351
	0x48, 0xf7, 0xd2, //0x00001cd5 notq         %rdx
	0x49, 0x89, 0xd7, //0x00001cd8 movq         %rdx, %r15
	0x4c, 0x8b, 0x55, 0xb8, //0x00001cdb movq         $-72(%rbp), %r10
	0xe9, 0x6b, 0xfe, 0xff, 0xff, //0x00001cdf jmp          LBB0_342
	//0x00001ce4 LBB0_218
	0x4c, 0x89, 0xf9, //0x00001ce4 movq         %r15, %rcx
	0x4c, 0x89, 0xd3, //0x00001ce7 movq         %r10, %rbx
	0x48, 0x83, 0xf9, 0x20, //0x00001cea cmpq         $32, %rcx
	0x0f, 0x82, 0xfe, 0x07, 0x00, 0x00, //0x00001cee jb           LBB0_442
	//0x00001cf4 LBB0_219
	0xc5, 0xfe, 0x6f, 0x03, //0x00001cf4 vmovdqu      (%rbx), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001cf8 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xd1, //0x00001cfc vpmovmskb    %ymm1, %edx
	0xc5, 0xfd, 0x74, 0xc7, //0x00001d00 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00001d04 vpmovmskb    %ymm0, %esi
	0x85, 0xf6, //0x00001d08 testl        %esi, %esi
	0x0f, 0x85, 0x6b, 0x07, 0x00, 0x00, //0x00001d0a jne          LBB0_437
	0x4d, 0x85, 0xf6, //0x00001d10 testq        %r14, %r14
	0x0f, 0x85, 0x81, 0x07, 0x00, 0x00, //0x00001d13 jne          LBB0_439
	0x45, 0x31, 0xf6, //0x00001d19 xorl         %r14d, %r14d
	0x48, 0x85, 0xd2, //0x00001d1c testq        %rdx, %rdx
	0x0f, 0x84, 0xc5, 0x07, 0x00, 0x00, //0x00001d1f je           LBB0_441
	//0x00001d25 LBB0_224
	0x4d, 0x89, 0xe2, //0x00001d25 movq         %r12, %r10
	0x48, 0x0f, 0xbc, 0xc2, //0x00001d28 bsfq         %rdx, %rax
	0x4c, 0x29, 0xcb, //0x00001d2c subq         %r9, %rbx
	0x4c, 0x8d, 0x64, 0x03, 0x01, //0x00001d2f leaq         $1(%rbx,%rax), %r12
	0x4c, 0x8b, 0x75, 0xd0, //0x00001d34 movq         $-48(%rbp), %r14
	//0x00001d38 LBB0_323
	0x4d, 0x85, 0xe4, //0x00001d38 testq        %r12, %r12
	0x0f, 0x88, 0x43, 0x0a, 0x00, 0x00, //0x00001d3b js           LBB0_477
	0x4d, 0x89, 0x65, 0x00, //0x00001d41 movq         %r12, (%r13)
	0x4c, 0x89, 0xc1, //0x00001d45 movq         %r8, %rcx
	0x4d, 0x85, 0xd2, //0x00001d48 testq        %r10, %r10
	0x4c, 0x8b, 0x55, 0xb8, //0x00001d4b movq         $-72(%rbp), %r10
	0x0f, 0x8f, 0x1b, 0xe5, 0xff, 0xff, //0x00001d4f jg           LBB0_3
	0xe9, 0x5f, 0x0a, 0x00, 0x00, //0x00001d55 jmp          LBB0_498
	//0x00001d5a LBB0_359
	0x49, 0xf7, 0xde, //0x00001d5a negq         %r14
	0xe9, 0x4e, 0xf3, 0xff, 0xff, //0x00001d5d jmp          LBB0_197
	//0x00001d62 LBB0_259
	0x4c, 0x89, 0xf3, //0x00001d62 movq         %r14, %rbx
	0x4c, 0x8b, 0x65, 0xa0, //0x00001d65 movq         $-96(%rbp), %r12
	0x48, 0x83, 0xfb, 0x20, //0x00001d69 cmpq         $32, %rbx
	0x4c, 0x8b, 0x75, 0xd0, //0x00001d6d movq         $-48(%rbp), %r14
	0x0f, 0x82, 0xb1, 0x08, 0x00, 0x00, //0x00001d71 jb           LBB0_459
	//0x00001d77 LBB0_260
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x00001d77 vmovdqu      (%r12), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001d7d vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x00001d81 vpmovmskb    %ymm1, %esi
	0xc5, 0xfd, 0x74, 0xcf, //0x00001d85 vpcmpeqb     %ymm7, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xc9, //0x00001d89 vpmovmskb    %ymm1, %ecx
	0xc5, 0xbd, 0x64, 0xc8, //0x00001d8d vpcmpgtb     %ymm0, %ymm8, %ymm1
	0xc4, 0xc1, 0x7d, 0x64, 0xc1, //0x00001d91 vpcmpgtb     %ymm9, %ymm0, %ymm0
	0xc5, 0xfd, 0xdb, 0xc1, //0x00001d96 vpand        %ymm1, %ymm0, %ymm0
	0xc5, 0x7d, 0xd7, 0xc8, //0x00001d9a vpmovmskb    %ymm0, %r9d
	0x85, 0xc9, //0x00001d9e testl        %ecx, %ecx
	0x0f, 0x85, 0xde, 0x07, 0x00, 0x00, //0x00001da0 jne          LBB0_451
	0x4d, 0x85, 0xff, //0x00001da6 testq        %r15, %r15
	0x0f, 0x85, 0xed, 0x07, 0x00, 0x00, //0x00001da9 jne          LBB0_453
	0x45, 0x31, 0xff, //0x00001daf xorl         %r15d, %r15d
	0x48, 0x85, 0xf6, //0x00001db2 testq        %rsi, %rsi
	0x0f, 0x84, 0x2c, 0x08, 0x00, 0x00, //0x00001db5 je           LBB0_454
	//0x00001dbb LBB0_263
	0x48, 0x0f, 0xbc, 0xce, //0x00001dbb bsfq         %rsi, %rcx
	0xe9, 0x28, 0x08, 0x00, 0x00, //0x00001dbf jmp          LBB0_455
	//0x00001dc4 LBB0_352
	0x4d, 0x29, 0xcf, //0x00001dc4 subq         %r9, %r15
	0x0f, 0xbc, 0xc3, //0x00001dc7 bsfl         %ebx, %eax
	0x4c, 0x01, 0xf8, //0x00001dca addq         %r15, %rax
	0x48, 0xf7, 0xd0, //0x00001dcd notq         %rax
	0x49, 0x89, 0xc7, //0x00001dd0 movq         %rax, %r15
	0x4c, 0x8b, 0x55, 0xb8, //0x00001dd3 movq         $-72(%rbp), %r10
	0xe9, 0x73, 0xfd, 0xff, 0xff, //0x00001dd7 jmp          LBB0_342
	//0x00001ddc LBB0_353
	0x48, 0xf7, 0xd7, //0x00001ddc notq         %rdi
	0x49, 0x89, 0xff, //0x00001ddf movq         %rdi, %r15
	0x4c, 0x8b, 0x55, 0xb8, //0x00001de2 movq         $-72(%rbp), %r10
	0xe9, 0x64, 0xfd, 0xff, 0xff, //0x00001de6 jmp          LBB0_342
	//0x00001deb LBB0_354
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00001deb movq         $-1, %rax
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00001df2 movq         $-1, %r12
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00001df9 movq         $-1, %r13
	0x4d, 0x89, 0xfe, //0x00001e00 movq         %r15, %r14
	0x49, 0x83, 0xfa, 0x10, //0x00001e03 cmpq         $16, %r10
	0x0f, 0x83, 0xa1, 0xea, 0xff, 0xff, //0x00001e07 jae          LBB0_151
	0xe9, 0xee, 0xeb, 0xff, 0xff, //0x00001e0d jmp          LBB0_169
	//0x00001e12 LBB0_355
	0x4d, 0x89, 0xcb, //0x00001e12 movq         %r9, %r11
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00001e15 movq         $-1, %r13
	0x45, 0x31, 0xf6, //0x00001e1c xorl         %r14d, %r14d
	0x48, 0x83, 0xf9, 0x20, //0x00001e1f cmpq         $32, %rcx
	0x0f, 0x83, 0x40, 0xfc, 0xff, 0xff, //0x00001e23 jae          LBB0_56
	0xe9, 0x38, 0x01, 0x00, 0x00, //0x00001e29 jmp          LBB0_370
	//0x00001e2e LBB0_356
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00001e2e movq         $-1, %r13
	0x45, 0x31, 0xff, //0x00001e35 xorl         %r15d, %r15d
	0x49, 0x83, 0xfa, 0x20, //0x00001e38 cmpq         $32, %r10
	0x0f, 0x83, 0xa0, 0xfc, 0xff, 0xff, //0x00001e3c jae          LBB0_83
	0xe9, 0x57, 0x02, 0x00, 0x00, //0x00001e42 jmp          LBB0_388
	//0x00001e47 LBB0_357
	0x49, 0x89, 0xcf, //0x00001e47 movq         %rcx, %r15
	0x4d, 0x85, 0xdb, //0x00001e4a testq        %r11, %r11
	0x0f, 0x85, 0x0a, 0xfb, 0xff, 0xff, //0x00001e4d jne          LBB0_327
	0xe9, 0xe5, 0x0a, 0x00, 0x00, //0x00001e53 jmp          LBB0_494
	//0x00001e58 LBB0_358
	0x48, 0xf7, 0xd6, //0x00001e58 notq         %rsi
	0x49, 0x89, 0xf7, //0x00001e5b movq         %rsi, %r15
	0x4c, 0x8b, 0x55, 0xb8, //0x00001e5e movq         $-72(%rbp), %r10
	0xe9, 0xe8, 0xfc, 0xff, 0xff, //0x00001e62 jmp          LBB0_342
	//0x00001e67 LBB0_360
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001e67 movq         $-1, %r11
	0x45, 0x31, 0xf6, //0x00001e6e xorl         %r14d, %r14d
	0x48, 0x83, 0xf9, 0x20, //0x00001e71 cmpq         $32, %rcx
	0x0f, 0x83, 0x3a, 0xfd, 0xff, 0xff, //0x00001e75 jae          LBB0_102
	0xe9, 0x35, 0x03, 0x00, 0x00, //0x00001e7b jmp          LBB0_402
	//0x00001e80 LBB0_361
	0x49, 0x89, 0xc2, //0x00001e80 movq         %rax, %r10
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001e83 movq         $-1, %r11
	0x45, 0x31, 0xff, //0x00001e8a xorl         %r15d, %r15d
	0x48, 0x83, 0xfb, 0x20, //0x00001e8d cmpq         $32, %rbx
	0x0f, 0x83, 0xb8, 0xfd, 0xff, 0xff, //0x00001e91 jae          LBB0_121
	0xe9, 0x4f, 0x04, 0x00, 0x00, //0x00001e97 jmp          LBB0_420
	//0x00001e9c LBB0_365
	0x49, 0xf7, 0xdf, //0x00001e9c negq         %r15
	0xe9, 0xab, 0xfc, 0xff, 0xff, //0x00001e9f jmp          LBB0_342
	//0x00001ea4 LBB0_362
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00001ea4 movq         $-1, %rax
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001eab movq         $-1, %r11
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00001eb2 movq         $-1, %r12
	0x4d, 0x89, 0xcf, //0x00001eb9 movq         %r9, %r15
	0xe9, 0xec, 0xf7, 0xff, 0xff, //0x00001ebc jmp          LBB0_290
	//0x00001ec1 LBB0_363
	0x49, 0x89, 0xd1, //0x00001ec1 movq         %rdx, %r9
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001ec4 movq         $-1, %r11
	0x45, 0x31, 0xf6, //0x00001ecb xorl         %r14d, %r14d
	0x48, 0x83, 0xf9, 0x20, //0x00001ece cmpq         $32, %rcx
	0x0f, 0x83, 0x1c, 0xfe, 0xff, 0xff, //0x00001ed2 jae          LBB0_219
	0xe9, 0x15, 0x06, 0x00, 0x00, //0x00001ed8 jmp          LBB0_442
	//0x00001edd LBB0_364
	0x49, 0x89, 0xc2, //0x00001edd movq         %rax, %r10
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001ee0 movq         $-1, %r11
	0x45, 0x31, 0xff, //0x00001ee7 xorl         %r15d, %r15d
	0x48, 0x83, 0xfb, 0x20, //0x00001eea cmpq         $32, %rbx
	0x4c, 0x8b, 0x75, 0xd0, //0x00001eee movq         $-48(%rbp), %r14
	0x0f, 0x83, 0x7f, 0xfe, 0xff, 0xff, //0x00001ef2 jae          LBB0_260
	0xe9, 0x2b, 0x07, 0x00, 0x00, //0x00001ef8 jmp          LBB0_459
	//0x00001efd LBB0_366
	0x49, 0x83, 0xfd, 0xff, //0x00001efd cmpq         $-1, %r13
	0x0f, 0x85, 0x0d, 0x00, 0x00, 0x00, //0x00001f01 jne          LBB0_368
	0x48, 0x89, 0xd8, //0x00001f07 movq         %rbx, %rax
	0x4c, 0x29, 0xd8, //0x00001f0a subq         %r11, %rax
	0x4c, 0x0f, 0xbc, 0xee, //0x00001f0d bsfq         %rsi, %r13
	0x49, 0x01, 0xc5, //0x00001f11 addq         %rax, %r13
	//0x00001f14 LBB0_368
	0x44, 0x89, 0xf0, //0x00001f14 movl         %r14d, %eax
	0xf7, 0xd0, //0x00001f17 notl         %eax
	0x21, 0xf0, //0x00001f19 andl         %esi, %eax
	0x44, 0x8d, 0x0c, 0x00, //0x00001f1b leal         (%rax,%rax), %r9d
	0x45, 0x09, 0xf1, //0x00001f1f orl          %r14d, %r9d
	0x44, 0x89, 0xcf, //0x00001f22 movl         %r9d, %edi
	0xf7, 0xd7, //0x00001f25 notl         %edi
	0x21, 0xf7, //0x00001f27 andl         %esi, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001f29 andl         $-1431655766, %edi
	0x45, 0x31, 0xf6, //0x00001f2f xorl         %r14d, %r14d
	0x01, 0xc7, //0x00001f32 addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc6, //0x00001f34 setb         %r14b
	0x01, 0xff, //0x00001f38 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00001f3a xorl         $1431655765, %edi
	0x44, 0x21, 0xcf, //0x00001f40 andl         %r9d, %edi
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00001f43 movl         $4294967295, %eax
	0x31, 0xf8, //0x00001f48 xorl         %edi, %eax
	0x21, 0xc2, //0x00001f4a andl         %eax, %edx
	0x4c, 0x8b, 0x55, 0xb8, //0x00001f4c movq         $-72(%rbp), %r10
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00001f50 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xd2, //0x00001f55 testq        %rdx, %rdx
	0x0f, 0x85, 0x3c, 0xfb, 0xff, 0xff, //0x00001f58 jne          LBB0_61
	//0x00001f5e LBB0_369
	0x48, 0x83, 0xc3, 0x20, //0x00001f5e addq         $32, %rbx
	0x48, 0x83, 0xc1, 0xe0, //0x00001f62 addq         $-32, %rcx
	//0x00001f66 LBB0_370
	0x4d, 0x85, 0xf6, //0x00001f66 testq        %r14, %r14
	0x0f, 0x85, 0x1c, 0x04, 0x00, 0x00, //0x00001f69 jne          LBB0_429
	0x4c, 0x8b, 0x75, 0xd0, //0x00001f6f movq         $-48(%rbp), %r14
	0x48, 0x85, 0xc9, //0x00001f73 testq        %rcx, %rcx
	0x0f, 0x84, 0xec, 0x07, 0x00, 0x00, //0x00001f76 je           LBB0_475
	//0x00001f7c LBB0_372
	0x4c, 0x89, 0xdf, //0x00001f7c movq         %r11, %rdi
	0x48, 0xf7, 0xd7, //0x00001f7f notq         %rdi
	//0x00001f82 LBB0_373
	0x4c, 0x8d, 0x63, 0x01, //0x00001f82 leaq         $1(%rbx), %r12
	0x0f, 0xb6, 0x13, //0x00001f86 movzbl       (%rbx), %edx
	0x80, 0xfa, 0x22, //0x00001f89 cmpb         $34, %dl
	0x0f, 0x84, 0x5b, 0x00, 0x00, 0x00, //0x00001f8c je           LBB0_378
	0x48, 0x8d, 0x71, 0xff, //0x00001f92 leaq         $-1(%rcx), %rsi
	0x80, 0xfa, 0x5c, //0x00001f96 cmpb         $92, %dl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00001f99 je           LBB0_376
	0x48, 0x89, 0xf1, //0x00001f9f movq         %rsi, %rcx
	0x4c, 0x89, 0xe3, //0x00001fa2 movq         %r12, %rbx
	0x48, 0x85, 0xf6, //0x00001fa5 testq        %rsi, %rsi
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x00001fa8 jne          LBB0_373
	0xe9, 0xb5, 0x07, 0x00, 0x00, //0x00001fae jmp          LBB0_475
	//0x00001fb3 LBB0_376
	0x48, 0x85, 0xf6, //0x00001fb3 testq        %rsi, %rsi
	0x0f, 0x84, 0xac, 0x07, 0x00, 0x00, //0x00001fb6 je           LBB0_475
	0x49, 0x01, 0xfc, //0x00001fbc addq         %rdi, %r12
	0x49, 0x83, 0xfd, 0xff, //0x00001fbf cmpq         $-1, %r13
	0x4d, 0x0f, 0x44, 0xec, //0x00001fc3 cmoveq       %r12, %r13
	0x48, 0x83, 0xc3, 0x02, //0x00001fc7 addq         $2, %rbx
	0x48, 0x83, 0xc1, 0xfe, //0x00001fcb addq         $-2, %rcx
	0x48, 0x89, 0xce, //0x00001fcf movq         %rcx, %rsi
	0x4c, 0x8b, 0x55, 0xb8, //0x00001fd2 movq         $-72(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xd0, //0x00001fd6 movq         $-48(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00001fda vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xf6, //0x00001fdf testq        %rsi, %rsi
	0x0f, 0x85, 0x9a, 0xff, 0xff, 0xff, //0x00001fe2 jne          LBB0_373
	0xe9, 0x7b, 0x07, 0x00, 0x00, //0x00001fe8 jmp          LBB0_475
	//0x00001fed LBB0_378
	0x4d, 0x29, 0xdc, //0x00001fed subq         %r11, %r12
	0xe9, 0xb5, 0xfa, 0xff, 0xff, //0x00001ff0 jmp          LBB0_184
	//0x00001ff5 LBB0_379
	0x49, 0x83, 0xfd, 0xff, //0x00001ff5 cmpq         $-1, %r13
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x00001ff9 jne          LBB0_381
	0x4c, 0x89, 0xe2, //0x00001fff movq         %r12, %rdx
	0x48, 0x2b, 0x55, 0xc8, //0x00002002 subq         $-56(%rbp), %rdx
	0x4c, 0x0f, 0xbc, 0xe9, //0x00002006 bsfq         %rcx, %r13
	0x49, 0x01, 0xd5, //0x0000200a addq         %rdx, %r13
	//0x0000200d LBB0_381
	0x44, 0x89, 0xfa, //0x0000200d movl         %r15d, %edx
	0xf7, 0xd2, //0x00002010 notl         %edx
	0x21, 0xca, //0x00002012 andl         %ecx, %edx
	0x8d, 0x1c, 0x12, //0x00002014 leal         (%rdx,%rdx), %ebx
	0x44, 0x09, 0xfb, //0x00002017 orl          %r15d, %ebx
	0x89, 0xdf, //0x0000201a movl         %ebx, %edi
	0xf7, 0xd7, //0x0000201c notl         %edi
	0x21, 0xcf, //0x0000201e andl         %ecx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002020 andl         $-1431655766, %edi
	0x45, 0x31, 0xff, //0x00002026 xorl         %r15d, %r15d
	0x01, 0xd7, //0x00002029 addl         %edx, %edi
	0x41, 0x0f, 0x92, 0xc7, //0x0000202b setb         %r15b
	0x01, 0xff, //0x0000202f addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00002031 xorl         $1431655765, %edi
	0x21, 0xdf, //0x00002037 andl         %ebx, %edi
	0xb9, 0xff, 0xff, 0xff, 0xff, //0x00002039 movl         $4294967295, %ecx
	0x31, 0xf9, //0x0000203e xorl         %edi, %ecx
	0x21, 0xce, //0x00002040 andl         %ecx, %esi
	0x4c, 0x8b, 0x75, 0xd0, //0x00002042 movq         $-48(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002046 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xf6, //0x0000204b testq        %rsi, %rsi
	0x0f, 0x85, 0xd2, 0xfa, 0xff, 0xff, //0x0000204e jne          LBB0_86
	//0x00002054 LBB0_382
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00002054 movl         $64, %ecx
	//0x00002059 LBB0_383
	0x49, 0x0f, 0xbc, 0xd1, //0x00002059 bsfq         %r9, %rdx
	0x45, 0x85, 0xc9, //0x0000205d testl        %r9d, %r9d
	0xbf, 0x40, 0x00, 0x00, 0x00, //0x00002060 movl         $64, %edi
	0x48, 0x0f, 0x45, 0xfa, //0x00002065 cmovneq      %rdx, %rdi
	0x48, 0x85, 0xf6, //0x00002069 testq        %rsi, %rsi
	0x0f, 0x84, 0x1b, 0x00, 0x00, 0x00, //0x0000206c je           LBB0_386
	0x4c, 0x2b, 0x65, 0xc8, //0x00002072 subq         $-56(%rbp), %r12
	0x48, 0x39, 0xcf, //0x00002076 cmpq         %rcx, %rdi
	0x0f, 0x82, 0x0e, 0x09, 0x00, 0x00, //0x00002079 jb           LBB0_508
	0x4d, 0x8d, 0x64, 0x0c, 0x01, //0x0000207f leaq         $1(%r12,%rcx), %r12
	0x4c, 0x8b, 0x55, 0xb8, //0x00002084 movq         $-72(%rbp), %r10
	0xe9, 0x1d, 0xfa, 0xff, 0xff, //0x00002088 jmp          LBB0_184
	//0x0000208d LBB0_386
	0x45, 0x85, 0xc9, //0x0000208d testl        %r9d, %r9d
	0x0f, 0x85, 0x09, 0x09, 0x00, 0x00, //0x00002090 jne          LBB0_509
	0x49, 0x83, 0xc4, 0x20, //0x00002096 addq         $32, %r12
	0x49, 0x83, 0xc2, 0xe0, //0x0000209a addq         $-32, %r10
	//0x0000209e LBB0_388
	0x4d, 0x85, 0xff, //0x0000209e testq        %r15, %r15
	0x0f, 0x85, 0x1f, 0x03, 0x00, 0x00, //0x000020a1 jne          LBB0_431
	0x48, 0x8b, 0x45, 0xc8, //0x000020a7 movq         $-56(%rbp), %rax
	0x4d, 0x85, 0xd2, //0x000020ab testq        %r10, %r10
	0x0f, 0x84, 0xb4, 0x06, 0x00, 0x00, //0x000020ae je           LBB0_475
	//0x000020b4 LBB0_390
	0x41, 0x0f, 0xb6, 0x0c, 0x24, //0x000020b4 movzbl       (%r12), %ecx
	0x80, 0xf9, 0x22, //0x000020b9 cmpb         $34, %cl
	0x0f, 0x84, 0x6d, 0x00, 0x00, 0x00, //0x000020bc je           LBB0_396
	0x80, 0xf9, 0x5c, //0x000020c2 cmpb         $92, %cl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x000020c5 je           LBB0_394
	0x80, 0xf9, 0x1f, //0x000020cb cmpb         $31, %cl
	0x0f, 0x86, 0xe9, 0x08, 0x00, 0x00, //0x000020ce jbe          LBB0_510
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000020d4 movq         $-1, %rcx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x000020db movl         $1, %edx
	0x49, 0x01, 0xd4, //0x000020e0 addq         %rdx, %r12
	0x49, 0x01, 0xca, //0x000020e3 addq         %rcx, %r10
	0x0f, 0x85, 0xc8, 0xff, 0xff, 0xff, //0x000020e6 jne          LBB0_390
	0xe9, 0x77, 0x06, 0x00, 0x00, //0x000020ec jmp          LBB0_475
	//0x000020f1 LBB0_394
	0x49, 0x83, 0xfa, 0x01, //0x000020f1 cmpq         $1, %r10
	0x0f, 0x84, 0x6d, 0x06, 0x00, 0x00, //0x000020f5 je           LBB0_475
	0x4c, 0x89, 0xe1, //0x000020fb movq         %r12, %rcx
	0x48, 0x29, 0xc1, //0x000020fe subq         %rax, %rcx
	0x49, 0x83, 0xfd, 0xff, //0x00002101 cmpq         $-1, %r13
	0x4c, 0x0f, 0x44, 0xe9, //0x00002105 cmoveq       %rcx, %r13
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00002109 movq         $-2, %rcx
	0xba, 0x02, 0x00, 0x00, 0x00, //0x00002110 movl         $2, %edx
	0x4c, 0x8b, 0x75, 0xd0, //0x00002115 movq         $-48(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002119 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x49, 0x01, 0xd4, //0x0000211e addq         %rdx, %r12
	0x49, 0x01, 0xca, //0x00002121 addq         %rcx, %r10
	0x0f, 0x85, 0x8a, 0xff, 0xff, 0xff, //0x00002124 jne          LBB0_390
	0xe9, 0x39, 0x06, 0x00, 0x00, //0x0000212a jmp          LBB0_475
	//0x0000212f LBB0_396
	0x49, 0x29, 0xc4, //0x0000212f subq         %rax, %r12
	0x49, 0xff, 0xc4, //0x00002132 incq         %r12
	0x4c, 0x8b, 0x55, 0xb8, //0x00002135 movq         $-72(%rbp), %r10
	0xe9, 0x6c, 0xf9, 0xff, 0xff, //0x00002139 jmp          LBB0_184
	//0x0000213e LBB0_397
	0x4d, 0x89, 0xca, //0x0000213e movq         %r9, %r10
	0x49, 0x83, 0xfb, 0xff, //0x00002141 cmpq         $-1, %r11
	0x0f, 0x85, 0x15, 0x00, 0x00, 0x00, //0x00002145 jne          LBB0_400
	0x48, 0x89, 0xd8, //0x0000214b movq         %rbx, %rax
	0x4c, 0x29, 0xd0, //0x0000214e subq         %r10, %rax
	0x4c, 0x0f, 0xbc, 0xde, //0x00002151 bsfq         %rsi, %r11
	0x49, 0x01, 0xc3, //0x00002155 addq         %rax, %r11
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x00002158 jmp          LBB0_400
	//0x0000215d LBB0_399
	0x4d, 0x89, 0xca, //0x0000215d movq         %r9, %r10
	//0x00002160 LBB0_400
	0x44, 0x89, 0xf0, //0x00002160 movl         %r14d, %eax
	0xf7, 0xd0, //0x00002163 notl         %eax
	0x21, 0xf0, //0x00002165 andl         %esi, %eax
	0x44, 0x8d, 0x0c, 0x00, //0x00002167 leal         (%rax,%rax), %r9d
	0x45, 0x09, 0xf1, //0x0000216b orl          %r14d, %r9d
	0x44, 0x89, 0xcf, //0x0000216e movl         %r9d, %edi
	0xf7, 0xd7, //0x00002171 notl         %edi
	0x21, 0xf7, //0x00002173 andl         %esi, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002175 andl         $-1431655766, %edi
	0x45, 0x31, 0xf6, //0x0000217b xorl         %r14d, %r14d
	0x01, 0xc7, //0x0000217e addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc6, //0x00002180 setb         %r14b
	0x01, 0xff, //0x00002184 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00002186 xorl         $1431655765, %edi
	0x44, 0x21, 0xcf, //0x0000218c andl         %r9d, %edi
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x0000218f movl         $4294967295, %eax
	0x31, 0xf8, //0x00002194 xorl         %edi, %eax
	0x21, 0xc2, //0x00002196 andl         %eax, %edx
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002198 movq         $-64(%rbp), %r13
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000219c vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x4d, 0x89, 0xd1, //0x000021a1 movq         %r10, %r9
	0x48, 0x85, 0xd2, //0x000021a4 testq        %rdx, %rdx
	0x0f, 0x85, 0x39, 0xfa, 0xff, 0xff, //0x000021a7 jne          LBB0_107
	//0x000021ad LBB0_401
	0x48, 0x83, 0xc3, 0x20, //0x000021ad addq         $32, %rbx
	0x48, 0x83, 0xc1, 0xe0, //0x000021b1 addq         $-32, %rcx
	//0x000021b5 LBB0_402
	0x4d, 0x85, 0xf6, //0x000021b5 testq        %r14, %r14
	0x0f, 0x85, 0x43, 0x02, 0x00, 0x00, //0x000021b8 jne          LBB0_433
	0x4c, 0x8b, 0x75, 0xd0, //0x000021be movq         $-48(%rbp), %r14
	0x48, 0x85, 0xc9, //0x000021c2 testq        %rcx, %rcx
	0x0f, 0x84, 0xc3, 0x05, 0x00, 0x00, //0x000021c5 je           LBB0_478
	//0x000021cb LBB0_404
	0x4d, 0x89, 0xe2, //0x000021cb movq         %r12, %r10
	0x4c, 0x89, 0xcf, //0x000021ce movq         %r9, %rdi
	0x48, 0xf7, 0xd7, //0x000021d1 notq         %rdi
	//0x000021d4 LBB0_405
	0x4c, 0x8d, 0x63, 0x01, //0x000021d4 leaq         $1(%rbx), %r12
	0x0f, 0xb6, 0x13, //0x000021d8 movzbl       (%rbx), %edx
	0x80, 0xfa, 0x22, //0x000021db cmpb         $34, %dl
	0x0f, 0x84, 0x5b, 0x00, 0x00, 0x00, //0x000021de je           LBB0_410
	0x48, 0x8d, 0x71, 0xff, //0x000021e4 leaq         $-1(%rcx), %rsi
	0x80, 0xfa, 0x5c, //0x000021e8 cmpb         $92, %dl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x000021eb je           LBB0_408
	0x48, 0x89, 0xf1, //0x000021f1 movq         %rsi, %rcx
	0x4c, 0x89, 0xe3, //0x000021f4 movq         %r12, %rbx
	0x48, 0x85, 0xf6, //0x000021f7 testq        %rsi, %rsi
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x000021fa jne          LBB0_405
	0xe9, 0x89, 0x05, 0x00, 0x00, //0x00002200 jmp          LBB0_478
	//0x00002205 LBB0_408
	0x48, 0x85, 0xf6, //0x00002205 testq        %rsi, %rsi
	0x0f, 0x84, 0xcb, 0x07, 0x00, 0x00, //0x00002208 je           LBB0_513
	0x49, 0x01, 0xfc, //0x0000220e addq         %rdi, %r12
	0x49, 0x83, 0xfb, 0xff, //0x00002211 cmpq         $-1, %r11
	0x4d, 0x0f, 0x44, 0xdc, //0x00002215 cmoveq       %r12, %r11
	0x48, 0x83, 0xc3, 0x02, //0x00002219 addq         $2, %rbx
	0x48, 0x83, 0xc1, 0xfe, //0x0000221d addq         $-2, %rcx
	0x48, 0x89, 0xce, //0x00002221 movq         %rcx, %rsi
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002224 movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x75, 0xd0, //0x00002228 movq         $-48(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000222c vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xf6, //0x00002231 testq        %rsi, %rsi
	0x0f, 0x85, 0x9a, 0xff, 0xff, 0xff, //0x00002234 jne          LBB0_405
	0xe9, 0x4f, 0x05, 0x00, 0x00, //0x0000223a jmp          LBB0_478
	//0x0000223f LBB0_410
	0x4d, 0x29, 0xcc, //0x0000223f subq         %r9, %r12
	0xe9, 0xb2, 0xf9, 0xff, 0xff, //0x00002242 jmp          LBB0_205
	//0x00002247 LBB0_411
	0x49, 0x83, 0xfb, 0xff, //0x00002247 cmpq         $-1, %r11
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x0000224b jne          LBB0_413
	0x4c, 0x89, 0xe0, //0x00002251 movq         %r12, %rax
	0x48, 0x2b, 0x45, 0xc8, //0x00002254 subq         $-56(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xd9, //0x00002258 bsfq         %rcx, %r11
	0x49, 0x01, 0xc3, //0x0000225c addq         %rax, %r11
	//0x0000225f LBB0_413
	0x44, 0x89, 0xf8, //0x0000225f movl         %r15d, %eax
	0xf7, 0xd0, //0x00002262 notl         %eax
	0x21, 0xc8, //0x00002264 andl         %ecx, %eax
	0x8d, 0x14, 0x00, //0x00002266 leal         (%rax,%rax), %edx
	0x44, 0x09, 0xfa, //0x00002269 orl          %r15d, %edx
	0x89, 0xd7, //0x0000226c movl         %edx, %edi
	0xf7, 0xd7, //0x0000226e notl         %edi
	0x21, 0xcf, //0x00002270 andl         %ecx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002272 andl         $-1431655766, %edi
	0x45, 0x31, 0xff, //0x00002278 xorl         %r15d, %r15d
	0x01, 0xc7, //0x0000227b addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc7, //0x0000227d setb         %r15b
	0x01, 0xff, //0x00002281 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00002283 xorl         $1431655765, %edi
	0x21, 0xd7, //0x00002289 andl         %edx, %edi
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x0000228b movl         $4294967295, %eax
	0x31, 0xf8, //0x00002290 xorl         %edi, %eax
	0x21, 0xc6, //0x00002292 andl         %eax, %esi
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002294 movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x75, 0xd0, //0x00002298 movq         $-48(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000229c vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xf6, //0x000022a1 testq        %rsi, %rsi
	0x0f, 0x85, 0xe9, 0xf9, 0xff, 0xff, //0x000022a4 jne          LBB0_124
	//0x000022aa LBB0_414
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x000022aa movl         $64, %ecx
	//0x000022af LBB0_415
	0x49, 0x0f, 0xbc, 0xd1, //0x000022af bsfq         %r9, %rdx
	0x45, 0x85, 0xc9, //0x000022b3 testl        %r9d, %r9d
	0xbf, 0x40, 0x00, 0x00, 0x00, //0x000022b6 movl         $64, %edi
	0x48, 0x0f, 0x45, 0xfa, //0x000022bb cmovneq      %rdx, %rdi
	0x48, 0x85, 0xf6, //0x000022bf testq        %rsi, %rsi
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x000022c2 je           LBB0_418
	0x4c, 0x2b, 0x65, 0xc8, //0x000022c8 subq         $-56(%rbp), %r12
	0x48, 0x39, 0xcf, //0x000022cc cmpq         %rcx, %rdi
	0x0f, 0x83, 0x2d, 0xee, 0xff, 0xff, //0x000022cf jae          LBB0_204
	0xe9, 0xd1, 0x06, 0x00, 0x00, //0x000022d5 jmp          LBB0_417
	//0x000022da LBB0_418
	0x45, 0x85, 0xc9, //0x000022da testl        %r9d, %r9d
	0x0f, 0x85, 0xe2, 0x06, 0x00, 0x00, //0x000022dd jne          LBB0_511
	0x49, 0x83, 0xc4, 0x20, //0x000022e3 addq         $32, %r12
	0x48, 0x83, 0xc3, 0xe0, //0x000022e7 addq         $-32, %rbx
	//0x000022eb LBB0_420
	0x4d, 0x85, 0xff, //0x000022eb testq        %r15, %r15
	0x0f, 0x85, 0x48, 0x01, 0x00, 0x00, //0x000022ee jne          LBB0_435
	0x48, 0x8b, 0x75, 0xc8, //0x000022f4 movq         $-56(%rbp), %rsi
	0x48, 0x85, 0xdb, //0x000022f8 testq        %rbx, %rbx
	0x0f, 0x84, 0x8d, 0x04, 0x00, 0x00, //0x000022fb je           LBB0_478
	//0x00002301 LBB0_422
	0x41, 0x0f, 0xb6, 0x0c, 0x24, //0x00002301 movzbl       (%r12), %ecx
	0x80, 0xf9, 0x22, //0x00002306 cmpb         $34, %cl
	0x0f, 0x84, 0x71, 0x00, 0x00, 0x00, //0x00002309 je           LBB0_428
	0x80, 0xf9, 0x5c, //0x0000230f cmpb         $92, %cl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00002312 je           LBB0_426
	0x80, 0xf9, 0x1f, //0x00002318 cmpb         $31, %cl
	0x0f, 0x86, 0xb0, 0x06, 0x00, 0x00, //0x0000231b jbe          LBB0_512
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00002321 movq         $-1, %rcx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00002328 movl         $1, %edx
	0x49, 0x01, 0xd4, //0x0000232d addq         %rdx, %r12
	0x48, 0x01, 0xcb, //0x00002330 addq         %rcx, %rbx
	0x0f, 0x85, 0xc8, 0xff, 0xff, 0xff, //0x00002333 jne          LBB0_422
	0xe9, 0x50, 0x04, 0x00, 0x00, //0x00002339 jmp          LBB0_478
	//0x0000233e LBB0_426
	0x48, 0x83, 0xfb, 0x01, //0x0000233e cmpq         $1, %rbx
	0x0f, 0x84, 0x91, 0x06, 0x00, 0x00, //0x00002342 je           LBB0_513
	0x4c, 0x89, 0xe0, //0x00002348 movq         %r12, %rax
	0x48, 0x29, 0xf0, //0x0000234b subq         %rsi, %rax
	0x49, 0x83, 0xfb, 0xff, //0x0000234e cmpq         $-1, %r11
	0x4c, 0x0f, 0x44, 0xd8, //0x00002352 cmoveq       %rax, %r11
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00002356 movq         $-2, %rcx
	0xba, 0x02, 0x00, 0x00, 0x00, //0x0000235d movl         $2, %edx
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002362 movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x75, 0xd0, //0x00002366 movq         $-48(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000236a vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x49, 0x01, 0xd4, //0x0000236f addq         %rdx, %r12
	0x48, 0x01, 0xcb, //0x00002372 addq         %rcx, %rbx
	0x0f, 0x85, 0x86, 0xff, 0xff, 0xff, //0x00002375 jne          LBB0_422
	0xe9, 0x0e, 0x04, 0x00, 0x00, //0x0000237b jmp          LBB0_478
	//0x00002380 LBB0_428
	0x49, 0x29, 0xf4, //0x00002380 subq         %rsi, %r12
	0x49, 0xff, 0xc4, //0x00002383 incq         %r12
	0xe9, 0x6e, 0xf8, 0xff, 0xff, //0x00002386 jmp          LBB0_205
	//0x0000238b LBB0_429
	0x48, 0x85, 0xc9, //0x0000238b testq        %rcx, %rcx
	0x0f, 0x84, 0xd4, 0x03, 0x00, 0x00, //0x0000238e je           LBB0_475
	0x4c, 0x89, 0xd8, //0x00002394 movq         %r11, %rax
	0x48, 0xf7, 0xd0, //0x00002397 notq         %rax
	0x48, 0x01, 0xd8, //0x0000239a addq         %rbx, %rax
	0x49, 0x83, 0xfd, 0xff, //0x0000239d cmpq         $-1, %r13
	0x4c, 0x0f, 0x44, 0xe8, //0x000023a1 cmoveq       %rax, %r13
	0x48, 0xff, 0xc3, //0x000023a5 incq         %rbx
	0x48, 0xff, 0xc9, //0x000023a8 decq         %rcx
	0x4c, 0x8b, 0x55, 0xb8, //0x000023ab movq         $-72(%rbp), %r10
	0x4c, 0x8b, 0x75, 0xd0, //0x000023af movq         $-48(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000023b3 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xc9, //0x000023b8 testq        %rcx, %rcx
	0x0f, 0x85, 0xbb, 0xfb, 0xff, 0xff, //0x000023bb jne          LBB0_372
	0xe9, 0xa2, 0x03, 0x00, 0x00, //0x000023c1 jmp          LBB0_475
	//0x000023c6 LBB0_431
	0x4d, 0x85, 0xd2, //0x000023c6 testq        %r10, %r10
	0x0f, 0x84, 0x99, 0x03, 0x00, 0x00, //0x000023c9 je           LBB0_475
	0x48, 0x8b, 0x45, 0xc8, //0x000023cf movq         $-56(%rbp), %rax
	0x48, 0x89, 0xc1, //0x000023d3 movq         %rax, %rcx
	0x48, 0xf7, 0xd1, //0x000023d6 notq         %rcx
	0x4c, 0x01, 0xe1, //0x000023d9 addq         %r12, %rcx
	0x49, 0x83, 0xfd, 0xff, //0x000023dc cmpq         $-1, %r13
	0x4c, 0x0f, 0x44, 0xe9, //0x000023e0 cmoveq       %rcx, %r13
	0x49, 0xff, 0xc4, //0x000023e4 incq         %r12
	0x49, 0xff, 0xca, //0x000023e7 decq         %r10
	0x4c, 0x8b, 0x75, 0xd0, //0x000023ea movq         $-48(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000023ee vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x4d, 0x85, 0xd2, //0x000023f3 testq        %r10, %r10
	0x0f, 0x85, 0xb8, 0xfc, 0xff, 0xff, //0x000023f6 jne          LBB0_390
	0xe9, 0x67, 0x03, 0x00, 0x00, //0x000023fc jmp          LBB0_475
	//0x00002401 LBB0_433
	0x48, 0x85, 0xc9, //0x00002401 testq        %rcx, %rcx
	0x0f, 0x84, 0xcf, 0x05, 0x00, 0x00, //0x00002404 je           LBB0_513
	0x4c, 0x89, 0xc8, //0x0000240a movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x0000240d notq         %rax
	0x48, 0x01, 0xd8, //0x00002410 addq         %rbx, %rax
	0x49, 0x83, 0xfb, 0xff, //0x00002413 cmpq         $-1, %r11
	0x4c, 0x0f, 0x44, 0xd8, //0x00002417 cmoveq       %rax, %r11
	0x48, 0xff, 0xc3, //0x0000241b incq         %rbx
	0x48, 0xff, 0xc9, //0x0000241e decq         %rcx
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002421 movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x75, 0xd0, //0x00002425 movq         $-48(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002429 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xc9, //0x0000242e testq        %rcx, %rcx
	0x0f, 0x85, 0x94, 0xfd, 0xff, 0xff, //0x00002431 jne          LBB0_404
	0xe9, 0x52, 0x03, 0x00, 0x00, //0x00002437 jmp          LBB0_478
	//0x0000243c LBB0_435
	0x48, 0x85, 0xdb, //0x0000243c testq        %rbx, %rbx
	0x0f, 0x84, 0x94, 0x05, 0x00, 0x00, //0x0000243f je           LBB0_513
	0x48, 0x8b, 0x75, 0xc8, //0x00002445 movq         $-56(%rbp), %rsi
	0x48, 0x89, 0xf0, //0x00002449 movq         %rsi, %rax
	0x48, 0xf7, 0xd0, //0x0000244c notq         %rax
	0x4c, 0x01, 0xe0, //0x0000244f addq         %r12, %rax
	0x49, 0x83, 0xfb, 0xff, //0x00002452 cmpq         $-1, %r11
	0x4c, 0x0f, 0x44, 0xd8, //0x00002456 cmoveq       %rax, %r11
	0x49, 0xff, 0xc4, //0x0000245a incq         %r12
	0x48, 0xff, 0xcb, //0x0000245d decq         %rbx
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002460 movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x75, 0xd0, //0x00002464 movq         $-48(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002468 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xdb, //0x0000246d testq        %rbx, %rbx
	0x0f, 0x85, 0x8b, 0xfe, 0xff, 0xff, //0x00002470 jne          LBB0_422
	0xe9, 0x13, 0x03, 0x00, 0x00, //0x00002476 jmp          LBB0_478
	//0x0000247b LBB0_437
	0x4d, 0x89, 0xca, //0x0000247b movq         %r9, %r10
	0x49, 0x83, 0xfb, 0xff, //0x0000247e cmpq         $-1, %r11
	0x0f, 0x85, 0x15, 0x00, 0x00, 0x00, //0x00002482 jne          LBB0_440
	0x48, 0x89, 0xd8, //0x00002488 movq         %rbx, %rax
	0x4c, 0x29, 0xd0, //0x0000248b subq         %r10, %rax
	0x4c, 0x0f, 0xbc, 0xde, //0x0000248e bsfq         %rsi, %r11
	0x49, 0x01, 0xc3, //0x00002492 addq         %rax, %r11
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x00002495 jmp          LBB0_440
	//0x0000249a LBB0_439
	0x4d, 0x89, 0xca, //0x0000249a movq         %r9, %r10
	//0x0000249d LBB0_440
	0x44, 0x89, 0xf0, //0x0000249d movl         %r14d, %eax
	0xf7, 0xd0, //0x000024a0 notl         %eax
	0x21, 0xf0, //0x000024a2 andl         %esi, %eax
	0x44, 0x8d, 0x0c, 0x00, //0x000024a4 leal         (%rax,%rax), %r9d
	0x45, 0x09, 0xf1, //0x000024a8 orl          %r14d, %r9d
	0x44, 0x89, 0xcf, //0x000024ab movl         %r9d, %edi
	0xf7, 0xd7, //0x000024ae notl         %edi
	0x21, 0xf7, //0x000024b0 andl         %esi, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x000024b2 andl         $-1431655766, %edi
	0x45, 0x31, 0xf6, //0x000024b8 xorl         %r14d, %r14d
	0x01, 0xc7, //0x000024bb addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc6, //0x000024bd setb         %r14b
	0x01, 0xff, //0x000024c1 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x000024c3 xorl         $1431655765, %edi
	0x44, 0x21, 0xcf, //0x000024c9 andl         %r9d, %edi
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x000024cc movl         $4294967295, %eax
	0x31, 0xc7, //0x000024d1 xorl         %eax, %edi
	0x21, 0xfa, //0x000024d3 andl         %edi, %edx
	0x4c, 0x8b, 0x6d, 0xc0, //0x000024d5 movq         $-64(%rbp), %r13
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000024d9 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x4d, 0x89, 0xd1, //0x000024de movq         %r10, %r9
	0x48, 0x85, 0xd2, //0x000024e1 testq        %rdx, %rdx
	0x0f, 0x85, 0x3b, 0xf8, 0xff, 0xff, //0x000024e4 jne          LBB0_224
	//0x000024ea LBB0_441
	0x48, 0x83, 0xc3, 0x20, //0x000024ea addq         $32, %rbx
	0x48, 0x83, 0xc1, 0xe0, //0x000024ee addq         $-32, %rcx
	//0x000024f2 LBB0_442
	0x4d, 0x85, 0xf6, //0x000024f2 testq        %r14, %r14
	0x0f, 0x85, 0xcd, 0x01, 0x00, 0x00, //0x000024f5 jne          LBB0_468
	0x4c, 0x8b, 0x75, 0xd0, //0x000024fb movq         $-48(%rbp), %r14
	0x48, 0x85, 0xc9, //0x000024ff testq        %rcx, %rcx
	0x0f, 0x84, 0x86, 0x02, 0x00, 0x00, //0x00002502 je           LBB0_478
	//0x00002508 LBB0_444
	0x4d, 0x89, 0xe2, //0x00002508 movq         %r12, %r10
	0x4c, 0x89, 0xcf, //0x0000250b movq         %r9, %rdi
	0x48, 0xf7, 0xd7, //0x0000250e notq         %rdi
	//0x00002511 LBB0_445
	0x4c, 0x8d, 0x63, 0x01, //0x00002511 leaq         $1(%rbx), %r12
	0x0f, 0xb6, 0x13, //0x00002515 movzbl       (%rbx), %edx
	0x80, 0xfa, 0x22, //0x00002518 cmpb         $34, %dl
	0x0f, 0x84, 0x5b, 0x00, 0x00, 0x00, //0x0000251b je           LBB0_450
	0x48, 0x8d, 0x71, 0xff, //0x00002521 leaq         $-1(%rcx), %rsi
	0x80, 0xfa, 0x5c, //0x00002525 cmpb         $92, %dl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00002528 je           LBB0_448
	0x48, 0x89, 0xf1, //0x0000252e movq         %rsi, %rcx
	0x4c, 0x89, 0xe3, //0x00002531 movq         %r12, %rbx
	0x48, 0x85, 0xf6, //0x00002534 testq        %rsi, %rsi
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x00002537 jne          LBB0_445
	0xe9, 0x4c, 0x02, 0x00, 0x00, //0x0000253d jmp          LBB0_478
	//0x00002542 LBB0_448
	0x48, 0x85, 0xf6, //0x00002542 testq        %rsi, %rsi
	0x0f, 0x84, 0x8e, 0x04, 0x00, 0x00, //0x00002545 je           LBB0_513
	0x49, 0x01, 0xfc, //0x0000254b addq         %rdi, %r12
	0x49, 0x83, 0xfb, 0xff, //0x0000254e cmpq         $-1, %r11
	0x4d, 0x0f, 0x44, 0xdc, //0x00002552 cmoveq       %r12, %r11
	0x48, 0x83, 0xc3, 0x02, //0x00002556 addq         $2, %rbx
	0x48, 0x83, 0xc1, 0xfe, //0x0000255a addq         $-2, %rcx
	0x48, 0x89, 0xce, //0x0000255e movq         %rcx, %rsi
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002561 movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x75, 0xd0, //0x00002565 movq         $-48(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x00002569 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xf6, //0x0000256e testq        %rsi, %rsi
	0x0f, 0x85, 0x9a, 0xff, 0xff, 0xff, //0x00002571 jne          LBB0_445
	0xe9, 0x12, 0x02, 0x00, 0x00, //0x00002577 jmp          LBB0_478
	//0x0000257c LBB0_450
	0x4d, 0x29, 0xcc, //0x0000257c subq         %r9, %r12
	0xe9, 0xb4, 0xf7, 0xff, 0xff, //0x0000257f jmp          LBB0_323
	//0x00002584 LBB0_451
	0x49, 0x83, 0xfb, 0xff, //0x00002584 cmpq         $-1, %r11
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x00002588 jne          LBB0_453
	0x4c, 0x89, 0xe0, //0x0000258e movq         %r12, %rax
	0x48, 0x2b, 0x45, 0xc8, //0x00002591 subq         $-56(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xd9, //0x00002595 bsfq         %rcx, %r11
	0x49, 0x01, 0xc3, //0x00002599 addq         %rax, %r11
	//0x0000259c LBB0_453
	0x44, 0x89, 0xf8, //0x0000259c movl         %r15d, %eax
	0xf7, 0xd0, //0x0000259f notl         %eax
	0x21, 0xc8, //0x000025a1 andl         %ecx, %eax
	0x8d, 0x14, 0x00, //0x000025a3 leal         (%rax,%rax), %edx
	0x44, 0x09, 0xfa, //0x000025a6 orl          %r15d, %edx
	0x89, 0xd7, //0x000025a9 movl         %edx, %edi
	0xf7, 0xd7, //0x000025ab notl         %edi
	0x21, 0xcf, //0x000025ad andl         %ecx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x000025af andl         $-1431655766, %edi
	0x45, 0x31, 0xff, //0x000025b5 xorl         %r15d, %r15d
	0x01, 0xc7, //0x000025b8 addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc7, //0x000025ba setb         %r15b
	0x01, 0xff, //0x000025be addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x000025c0 xorl         $1431655765, %edi
	0x21, 0xd7, //0x000025c6 andl         %edx, %edi
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x000025c8 movl         $4294967295, %eax
	0x31, 0xf8, //0x000025cd xorl         %edi, %eax
	0x21, 0xc6, //0x000025cf andl         %eax, %esi
	0x4c, 0x8b, 0x6d, 0xc0, //0x000025d1 movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x75, 0xd0, //0x000025d5 movq         $-48(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000025d9 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xf6, //0x000025de testq        %rsi, %rsi
	0x0f, 0x85, 0xd4, 0xf7, 0xff, 0xff, //0x000025e1 jne          LBB0_263
	//0x000025e7 LBB0_454
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x000025e7 movl         $64, %ecx
	//0x000025ec LBB0_455
	0x49, 0x0f, 0xbc, 0xd1, //0x000025ec bsfq         %r9, %rdx
	0x45, 0x85, 0xc9, //0x000025f0 testl        %r9d, %r9d
	0xbf, 0x40, 0x00, 0x00, 0x00, //0x000025f3 movl         $64, %edi
	0x48, 0x0f, 0x45, 0xfa, //0x000025f8 cmovneq      %rdx, %rdi
	0x48, 0x85, 0xf6, //0x000025fc testq        %rsi, %rsi
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x000025ff je           LBB0_457
	0x4c, 0x2b, 0x65, 0xc8, //0x00002605 subq         $-56(%rbp), %r12
	0x48, 0x39, 0xcf, //0x00002609 cmpq         %rcx, %rdi
	0x0f, 0x83, 0x31, 0xf3, 0xff, 0xff, //0x0000260c jae          LBB0_322
	0xe9, 0x94, 0x03, 0x00, 0x00, //0x00002612 jmp          LBB0_417
	//0x00002617 LBB0_457
	0x45, 0x85, 0xc9, //0x00002617 testl        %r9d, %r9d
	0x0f, 0x85, 0xa5, 0x03, 0x00, 0x00, //0x0000261a jne          LBB0_511
	0x49, 0x83, 0xc4, 0x20, //0x00002620 addq         $32, %r12
	0x48, 0x83, 0xc3, 0xe0, //0x00002624 addq         $-32, %rbx
	//0x00002628 LBB0_459
	0x4d, 0x85, 0xff, //0x00002628 testq        %r15, %r15
	0x0f, 0x85, 0xd2, 0x00, 0x00, 0x00, //0x0000262b jne          LBB0_470
	0x48, 0x8b, 0x75, 0xc8, //0x00002631 movq         $-56(%rbp), %rsi
	0x48, 0x85, 0xdb, //0x00002635 testq        %rbx, %rbx
	0x0f, 0x84, 0x50, 0x01, 0x00, 0x00, //0x00002638 je           LBB0_478
	//0x0000263e LBB0_461
	0x41, 0x0f, 0xb6, 0x0c, 0x24, //0x0000263e movzbl       (%r12), %ecx
	0x80, 0xf9, 0x22, //0x00002643 cmpb         $34, %cl
	0x0f, 0x84, 0x71, 0x00, 0x00, 0x00, //0x00002646 je           LBB0_467
	0x80, 0xf9, 0x5c, //0x0000264c cmpb         $92, %cl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x0000264f je           LBB0_465
	0x80, 0xf9, 0x1f, //0x00002655 cmpb         $31, %cl
	0x0f, 0x86, 0x73, 0x03, 0x00, 0x00, //0x00002658 jbe          LBB0_512
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000265e movq         $-1, %rcx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00002665 movl         $1, %edx
	0x49, 0x01, 0xd4, //0x0000266a addq         %rdx, %r12
	0x48, 0x01, 0xcb, //0x0000266d addq         %rcx, %rbx
	0x0f, 0x85, 0xc8, 0xff, 0xff, 0xff, //0x00002670 jne          LBB0_461
	0xe9, 0x13, 0x01, 0x00, 0x00, //0x00002676 jmp          LBB0_478
	//0x0000267b LBB0_465
	0x48, 0x83, 0xfb, 0x01, //0x0000267b cmpq         $1, %rbx
	0x0f, 0x84, 0x54, 0x03, 0x00, 0x00, //0x0000267f je           LBB0_513
	0x4c, 0x89, 0xe0, //0x00002685 movq         %r12, %rax
	0x48, 0x29, 0xf0, //0x00002688 subq         %rsi, %rax
	0x49, 0x83, 0xfb, 0xff, //0x0000268b cmpq         $-1, %r11
	0x4c, 0x0f, 0x44, 0xd8, //0x0000268f cmoveq       %rax, %r11
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00002693 movq         $-2, %rcx
	0xba, 0x02, 0x00, 0x00, 0x00, //0x0000269a movl         $2, %edx
	0x4c, 0x8b, 0x6d, 0xc0, //0x0000269f movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x75, 0xd0, //0x000026a3 movq         $-48(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000026a7 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x49, 0x01, 0xd4, //0x000026ac addq         %rdx, %r12
	0x48, 0x01, 0xcb, //0x000026af addq         %rcx, %rbx
	0x0f, 0x85, 0x86, 0xff, 0xff, 0xff, //0x000026b2 jne          LBB0_461
	0xe9, 0xd1, 0x00, 0x00, 0x00, //0x000026b8 jmp          LBB0_478
	//0x000026bd LBB0_467
	0x49, 0x29, 0xf4, //0x000026bd subq         %rsi, %r12
	0x49, 0xff, 0xc4, //0x000026c0 incq         %r12
	0xe9, 0x70, 0xf6, 0xff, 0xff, //0x000026c3 jmp          LBB0_323
	//0x000026c8 LBB0_468
	0x48, 0x85, 0xc9, //0x000026c8 testq        %rcx, %rcx
	0x0f, 0x84, 0x08, 0x03, 0x00, 0x00, //0x000026cb je           LBB0_513
	0x4c, 0x89, 0xc8, //0x000026d1 movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x000026d4 notq         %rax
	0x48, 0x01, 0xd8, //0x000026d7 addq         %rbx, %rax
	0x49, 0x83, 0xfb, 0xff, //0x000026da cmpq         $-1, %r11
	0x4c, 0x0f, 0x44, 0xd8, //0x000026de cmoveq       %rax, %r11
	0x48, 0xff, 0xc3, //0x000026e2 incq         %rbx
	0x48, 0xff, 0xc9, //0x000026e5 decq         %rcx
	0x4c, 0x8b, 0x6d, 0xc0, //0x000026e8 movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x75, 0xd0, //0x000026ec movq         $-48(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x000026f0 vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xc9, //0x000026f5 testq        %rcx, %rcx
	0x0f, 0x85, 0x0a, 0xfe, 0xff, 0xff, //0x000026f8 jne          LBB0_444
	0xe9, 0x8b, 0x00, 0x00, 0x00, //0x000026fe jmp          LBB0_478
	//0x00002703 LBB0_470
	0x48, 0x85, 0xdb, //0x00002703 testq        %rbx, %rbx
	0x0f, 0x84, 0xcd, 0x02, 0x00, 0x00, //0x00002706 je           LBB0_513
	0x48, 0x8b, 0x75, 0xc8, //0x0000270c movq         $-56(%rbp), %rsi
	0x48, 0x89, 0xf0, //0x00002710 movq         %rsi, %rax
	0x48, 0xf7, 0xd0, //0x00002713 notq         %rax
	0x4c, 0x01, 0xe0, //0x00002716 addq         %r12, %rax
	0x49, 0x83, 0xfb, 0xff, //0x00002719 cmpq         $-1, %r11
	0x4c, 0x0f, 0x44, 0xd8, //0x0000271d cmoveq       %rax, %r11
	0x49, 0xff, 0xc4, //0x00002721 incq         %r12
	0x48, 0xff, 0xcb, //0x00002724 decq         %rbx
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002727 movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x75, 0xd0, //0x0000272b movq         $-48(%rbp), %r14
	0xc4, 0x41, 0x35, 0x76, 0xc9, //0x0000272f vpcmpeqd     %ymm9, %ymm9, %ymm9
	0x48, 0x85, 0xdb, //0x00002734 testq        %rbx, %rbx
	0x0f, 0x85, 0x01, 0xff, 0xff, 0xff, //0x00002737 jne          LBB0_461
	0xe9, 0x4c, 0x00, 0x00, 0x00, //0x0000273d jmp          LBB0_478
	//0x00002742 LBB0_472
	0x4d, 0x89, 0x45, 0x00, //0x00002742 movq         %r8, (%r13)
	//0x00002746 LBB0_473
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00002746 movq         $-1, %rcx
	0xe9, 0x67, 0x00, 0x00, 0x00, //0x0000274d jmp          LBB0_498
	//0x00002752 LBB0_486
	0x48, 0xc7, 0xc1, 0xf9, 0xff, 0xff, 0xff, //0x00002752 movq         $-7, %rcx
	0xe9, 0x5b, 0x00, 0x00, 0x00, //0x00002759 jmp          LBB0_498
	//0x0000275e LBB0_474
	0x49, 0x83, 0xfc, 0xff, //0x0000275e cmpq         $-1, %r12
	0x0f, 0x85, 0x8e, 0x00, 0x00, 0x00, //0x00002762 jne          LBB0_506
	//0x00002768 LBB0_475
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00002768 movq         $-1, %r12
	0x4c, 0x8b, 0x6d, 0xb0, //0x0000276f movq         $-80(%rbp), %r13
	0xe9, 0x7e, 0x00, 0x00, 0x00, //0x00002773 jmp          LBB0_506
	//0x00002778 LBB0_476
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00002778 movq         $-1, %rcx
	0xe9, 0x1d, 0x00, 0x00, 0x00, //0x0000277f jmp          LBB0_480
	//0x00002784 LBB0_477
	0x49, 0x83, 0xfc, 0xff, //0x00002784 cmpq         $-1, %r12
	0x0f, 0x85, 0x96, 0x00, 0x00, 0x00, //0x00002788 jne          LBB0_503
	//0x0000278e LBB0_478
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x0000278e movq         $-1, %r12
	0x4c, 0x8b, 0x5d, 0xb0, //0x00002795 movq         $-80(%rbp), %r11
	0xe9, 0x86, 0x00, 0x00, 0x00, //0x00002799 jmp          LBB0_503
	//0x0000279e LBB0_479
	0x4c, 0x89, 0xf1, //0x0000279e movq         %r14, %rcx
	//0x000027a1 LBB0_480
	0x48, 0x8b, 0x55, 0xc0, //0x000027a1 movq         $-64(%rbp), %rdx
	0x48, 0x8b, 0x02, //0x000027a5 movq         (%rdx), %rax
	0x48, 0x29, 0xc8, //0x000027a8 subq         %rcx, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x000027ab addq         $-2, %rax
	0x48, 0x89, 0x02, //0x000027af movq         %rax, (%rdx)
	//0x000027b2 LBB0_497
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x000027b2 movq         $-2, %rcx
	//0x000027b9 LBB0_498
	0x48, 0x89, 0xc8, //0x000027b9 movq         %rcx, %rax
	0x48, 0x83, 0xc4, 0x48, //0x000027bc addq         $72, %rsp
	0x5b, //0x000027c0 popq         %rbx
	0x41, 0x5c, //0x000027c1 popq         %r12
	0x41, 0x5d, //0x000027c3 popq         %r13
	0x41, 0x5e, //0x000027c5 popq         %r14
	0x41, 0x5f, //0x000027c7 popq         %r15
	0x5d, //0x000027c9 popq         %rbp
	0xc5, 0xf8, 0x77, //0x000027ca vzeroupper   
	0xc3, //0x000027cd retq         
	//0x000027ce LBB0_487
	0x49, 0x89, 0x55, 0x00, //0x000027ce movq         %rdx, (%r13)
	0xe9, 0xe2, 0xff, 0xff, 0xff, //0x000027d2 jmp          LBB0_498
	//0x000027d7 LBB0_481
	0x49, 0x83, 0xfd, 0xff, //0x000027d7 cmpq         $-1, %r13
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x000027db jne          LBB0_484
	0x48, 0x0f, 0xbc, 0xc7, //0x000027e1 bsfq         %rdi, %rax
	0x4c, 0x2b, 0x65, 0xc8, //0x000027e5 subq         $-56(%rbp), %r12
	0x49, 0x01, 0xc4, //0x000027e9 addq         %rax, %r12
	//0x000027ec LBB0_483
	0x4d, 0x89, 0xe5, //0x000027ec movq         %r12, %r13
	//0x000027ef LBB0_484
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x000027ef movq         $-2, %r12
	//0x000027f6 LBB0_506
	0x48, 0x8b, 0x45, 0xc0, //0x000027f6 movq         $-64(%rbp), %rax
	0x4c, 0x89, 0x28, //0x000027fa movq         %r13, (%rax)
	0x4c, 0x89, 0xe1, //0x000027fd movq         %r12, %rcx
	0xe9, 0xb4, 0xff, 0xff, 0xff, //0x00002800 jmp          LBB0_498
	//0x00002805 LBB0_499
	0x49, 0x83, 0xfb, 0xff, //0x00002805 cmpq         $-1, %r11
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x00002809 jne          LBB0_502
	0x48, 0x0f, 0xbc, 0xc7, //0x0000280f bsfq         %rdi, %rax
	0x4c, 0x2b, 0x65, 0xc8, //0x00002813 subq         $-56(%rbp), %r12
	0x49, 0x01, 0xc4, //0x00002817 addq         %rax, %r12
	//0x0000281a LBB0_501
	0x4d, 0x89, 0xe3, //0x0000281a movq         %r12, %r11
	//0x0000281d LBB0_502
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x0000281d movq         $-2, %r12
	//0x00002824 LBB0_503
	0x4d, 0x89, 0x5d, 0x00, //0x00002824 movq         %r11, (%r13)
	0x4c, 0x89, 0xe1, //0x00002828 movq         %r12, %rcx
	0xe9, 0x89, 0xff, 0xff, 0xff, //0x0000282b jmp          LBB0_498
	//0x00002830 LBB0_233
	0x4d, 0x89, 0x45, 0x00, //0x00002830 movq         %r8, (%r13)
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00002834 movq         $-2, %rcx
	0x41, 0x80, 0x3f, 0x6e, //0x0000283b cmpb         $110, (%r15)
	0x0f, 0x85, 0x74, 0xff, 0xff, 0xff, //0x0000283f jne          LBB0_498
	0x49, 0x8d, 0x40, 0x01, //0x00002845 leaq         $1(%r8), %rax
	0x49, 0x89, 0x45, 0x00, //0x00002849 movq         %rax, (%r13)
	0x43, 0x80, 0x7c, 0x01, 0x01, 0x75, //0x0000284d cmpb         $117, $1(%r9,%r8)
	0x0f, 0x85, 0x60, 0xff, 0xff, 0xff, //0x00002853 jne          LBB0_498
	0x49, 0x8d, 0x40, 0x02, //0x00002859 leaq         $2(%r8), %rax
	0x49, 0x89, 0x45, 0x00, //0x0000285d movq         %rax, (%r13)
	0x43, 0x80, 0x7c, 0x01, 0x02, 0x6c, //0x00002861 cmpb         $108, $2(%r9,%r8)
	0x0f, 0x85, 0x4c, 0xff, 0xff, 0xff, //0x00002867 jne          LBB0_498
	0x49, 0x8d, 0x40, 0x03, //0x0000286d leaq         $3(%r8), %rax
	0x49, 0x89, 0x45, 0x00, //0x00002871 movq         %rax, (%r13)
	0x43, 0x80, 0x7c, 0x01, 0x03, 0x6c, //0x00002875 cmpb         $108, $3(%r9,%r8)
	0x0f, 0x85, 0x38, 0xff, 0xff, 0xff, //0x0000287b jne          LBB0_498
	0xe9, 0xaa, 0x00, 0x00, 0x00, //0x00002881 jmp          LBB0_237
	//0x00002886 LBB0_488
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00002886 movq         $-2, %rcx
	0x80, 0xfa, 0x61, //0x0000288d cmpb         $97, %dl
	0x0f, 0x85, 0x23, 0xff, 0xff, 0xff, //0x00002890 jne          LBB0_498
	0x49, 0x8d, 0x40, 0x02, //0x00002896 leaq         $2(%r8), %rax
	0x49, 0x89, 0x45, 0x00, //0x0000289a movq         %rax, (%r13)
	0x43, 0x80, 0x7c, 0x01, 0x02, 0x6c, //0x0000289e cmpb         $108, $2(%r9,%r8)
	0x0f, 0x85, 0x0f, 0xff, 0xff, 0xff, //0x000028a4 jne          LBB0_498
	0x49, 0x8d, 0x40, 0x03, //0x000028aa leaq         $3(%r8), %rax
	0x49, 0x89, 0x45, 0x00, //0x000028ae movq         %rax, (%r13)
	0x43, 0x80, 0x7c, 0x01, 0x03, 0x73, //0x000028b2 cmpb         $115, $3(%r9,%r8)
	0x0f, 0x85, 0xfb, 0xfe, 0xff, 0xff, //0x000028b8 jne          LBB0_498
	0x49, 0x8d, 0x40, 0x04, //0x000028be leaq         $4(%r8), %rax
	0x49, 0x89, 0x45, 0x00, //0x000028c2 movq         %rax, (%r13)
	0x43, 0x80, 0x7c, 0x01, 0x04, 0x65, //0x000028c6 cmpb         $101, $4(%r9,%r8)
	0x0f, 0x85, 0xe7, 0xfe, 0xff, 0xff, //0x000028cc jne          LBB0_498
	0x49, 0x83, 0xc0, 0x05, //0x000028d2 addq         $5, %r8
	0x4d, 0x89, 0x45, 0x00, //0x000028d6 movq         %r8, (%r13)
	0xe9, 0xda, 0xfe, 0xff, 0xff, //0x000028da jmp          LBB0_498
	//0x000028df LBB0_245
	0x4d, 0x89, 0x45, 0x00, //0x000028df movq         %r8, (%r13)
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x000028e3 movq         $-2, %rcx
	0x41, 0x80, 0x3f, 0x74, //0x000028ea cmpb         $116, (%r15)
	0x0f, 0x85, 0xc5, 0xfe, 0xff, 0xff, //0x000028ee jne          LBB0_498
	0x49, 0x8d, 0x40, 0x01, //0x000028f4 leaq         $1(%r8), %rax
	0x49, 0x89, 0x45, 0x00, //0x000028f8 movq         %rax, (%r13)
	0x43, 0x80, 0x7c, 0x01, 0x01, 0x72, //0x000028fc cmpb         $114, $1(%r9,%r8)
	0x0f, 0x85, 0xb1, 0xfe, 0xff, 0xff, //0x00002902 jne          LBB0_498
	0x49, 0x8d, 0x40, 0x02, //0x00002908 leaq         $2(%r8), %rax
	0x49, 0x89, 0x45, 0x00, //0x0000290c movq         %rax, (%r13)
	0x43, 0x80, 0x7c, 0x01, 0x02, 0x75, //0x00002910 cmpb         $117, $2(%r9,%r8)
	0x0f, 0x85, 0x9d, 0xfe, 0xff, 0xff, //0x00002916 jne          LBB0_498
	0x49, 0x8d, 0x40, 0x03, //0x0000291c leaq         $3(%r8), %rax
	0x49, 0x89, 0x45, 0x00, //0x00002920 movq         %rax, (%r13)
	0x43, 0x80, 0x7c, 0x01, 0x03, 0x65, //0x00002924 cmpb         $101, $3(%r9,%r8)
	0x0f, 0x85, 0x89, 0xfe, 0xff, 0xff, //0x0000292a jne          LBB0_498
	//0x00002930 LBB0_237
	0x49, 0x83, 0xc0, 0x04, //0x00002930 addq         $4, %r8
	0x4d, 0x89, 0x45, 0x00, //0x00002934 movq         %r8, (%r13)
	0xe9, 0x7c, 0xfe, 0xff, 0xff, //0x00002938 jmp          LBB0_498
	//0x0000293d LBB0_494
	0x4d, 0x8b, 0x65, 0x00, //0x0000293d movq         (%r13), %r12
	//0x00002941 LBB0_495
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00002941 movq         $-1, %r15
	//0x00002948 LBB0_496
	0x49, 0xf7, 0xd7, //0x00002948 notq         %r15
	0x4d, 0x01, 0xe7, //0x0000294b addq         %r12, %r15
	0x4d, 0x89, 0x7d, 0x00, //0x0000294e movq         %r15, (%r13)
	0xe9, 0x5b, 0xfe, 0xff, 0xff, //0x00002952 jmp          LBB0_497
	//0x00002957 LBB0_504
	0x4c, 0x89, 0x65, 0xb0, //0x00002957 movq         %r12, $-80(%rbp)
	0xe9, 0x08, 0xfe, 0xff, 0xff, //0x0000295b jmp          LBB0_475
	//0x00002960 LBB0_507
	0x4c, 0x89, 0x65, 0xb0, //0x00002960 movq         %r12, $-80(%rbp)
	0xe9, 0x25, 0xfe, 0xff, 0xff, //0x00002964 jmp          LBB0_478
	//0x00002969 LBB0_505
	0x4c, 0x01, 0xe2, //0x00002969 addq         %r12, %rdx
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x0000296c movq         $-2, %r12
	0x49, 0x89, 0xd5, //0x00002973 movq         %rdx, %r13
	0xe9, 0x7b, 0xfe, 0xff, 0xff, //0x00002976 jmp          LBB0_506
	//0x0000297b LBB0_129
	0x4c, 0x01, 0xe2, //0x0000297b addq         %r12, %rdx
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x0000297e movq         $-2, %r12
	0x49, 0x89, 0xd3, //0x00002985 movq         %rdx, %r11
	0xe9, 0x97, 0xfe, 0xff, 0xff, //0x00002988 jmp          LBB0_503
	//0x0000298d LBB0_508
	0x4c, 0x01, 0xe7, //0x0000298d addq         %r12, %rdi
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x00002990 movq         $-2, %r12
	0x49, 0x89, 0xfd, //0x00002997 movq         %rdi, %r13
	0xe9, 0x57, 0xfe, 0xff, 0xff, //0x0000299a jmp          LBB0_506
	//0x0000299f LBB0_509
	0x4c, 0x2b, 0x65, 0xc8, //0x0000299f subq         $-56(%rbp), %r12
	0x49, 0x01, 0xd4, //0x000029a3 addq         %rdx, %r12
	0xe9, 0x41, 0xfe, 0xff, 0xff, //0x000029a6 jmp          LBB0_483
	//0x000029ab LBB0_417
	0x4c, 0x01, 0xe7, //0x000029ab addq         %r12, %rdi
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x000029ae movq         $-2, %r12
	0x49, 0x89, 0xfb, //0x000029b5 movq         %rdi, %r11
	0xe9, 0x67, 0xfe, 0xff, 0xff, //0x000029b8 jmp          LBB0_503
	//0x000029bd LBB0_510
	0x49, 0x29, 0xc4, //0x000029bd subq         %rax, %r12
	0xe9, 0x27, 0xfe, 0xff, 0xff, //0x000029c0 jmp          LBB0_483
	//0x000029c5 LBB0_511
	0x4c, 0x2b, 0x65, 0xc8, //0x000029c5 subq         $-56(%rbp), %r12
	0x49, 0x01, 0xd4, //0x000029c9 addq         %rdx, %r12
	0xe9, 0x49, 0xfe, 0xff, 0xff, //0x000029cc jmp          LBB0_501
	//0x000029d1 LBB0_512
	0x49, 0x29, 0xf4, //0x000029d1 subq         %rsi, %r12
	0xe9, 0x41, 0xfe, 0xff, 0xff, //0x000029d4 jmp          LBB0_501
	//0x000029d9 LBB0_513
	0x4c, 0x8b, 0x6d, 0xc0, //0x000029d9 movq         $-64(%rbp), %r13
	0xe9, 0xac, 0xfd, 0xff, 0xff, //0x000029dd jmp          LBB0_478
	0x90, 0x90, //0x000029e2 .p2align 2, 0x90
	// // .set L0_0_set_39, LBB0_39-LJTI0_0
	// // .set L0_0_set_62, LBB0_62-LJTI0_0
	// // .set L0_0_set_45, LBB0_45-LJTI0_0
	// // .set L0_0_set_64, LBB0_64-LJTI0_0
	// // .set L0_0_set_42, LBB0_42-LJTI0_0
	// // .set L0_0_set_67, LBB0_67-LJTI0_0
	//0x000029e4 LJTI0_0
	0xd3, 0xda, 0xff, 0xff, //0x000029e4 .long L0_0_set_39
	0x69, 0xdc, 0xff, 0xff, //0x000029e8 .long L0_0_set_62
	0x0a, 0xdb, 0xff, 0xff, //0x000029ec .long L0_0_set_45
	0x94, 0xdc, 0xff, 0xff, //0x000029f0 .long L0_0_set_64
	0xea, 0xda, 0xff, 0xff, //0x000029f4 .long L0_0_set_42
	0x2e, 0xe0, 0xff, 0xff, //0x000029f8 .long L0_0_set_67
	// // .set L0_1_set_498, LBB0_498-LJTI0_1
	// // .set L0_1_set_497, LBB0_497-LJTI0_1
	// // .set L0_1_set_209, LBB0_209-LJTI0_1
	// // .set L0_1_set_225, LBB0_225-LJTI0_1
	// // .set L0_1_set_69, LBB0_69-LJTI0_1
	// // .set L0_1_set_229, LBB0_229-LJTI0_1
	// // .set L0_1_set_240, LBB0_240-LJTI0_1
	// // .set L0_1_set_231, LBB0_231-LJTI0_1
	// // .set L0_1_set_243, LBB0_243-LJTI0_1
	// // .set L0_1_set_238, LBB0_238-LJTI0_1
	//0x000029fc LJTI0_1
	0xbd, 0xfd, 0xff, 0xff, //0x000029fc .long L0_1_set_498
	0xb6, 0xfd, 0xff, 0xff, //0x00002a00 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a04 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a08 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a0c .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a10 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a14 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a18 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a1c .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a20 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a24 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a28 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a2c .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a30 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a34 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a38 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a3c .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a40 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a44 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a48 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a4c .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a50 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a54 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a58 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a5c .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a60 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a64 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a68 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a6c .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a70 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a74 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a78 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a7c .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a80 .long L0_1_set_497
	0x10, 0xe7, 0xff, 0xff, //0x00002a84 .long L0_1_set_209
	0xb6, 0xfd, 0xff, 0xff, //0x00002a88 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a8c .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a90 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a94 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a98 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002a9c .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002aa0 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002aa4 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002aa8 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002aac .long L0_1_set_497
	0x65, 0xe8, 0xff, 0xff, //0x00002ab0 .long L0_1_set_225
	0xb6, 0xfd, 0xff, 0xff, //0x00002ab4 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002ab8 .long L0_1_set_497
	0xb5, 0xdc, 0xff, 0xff, //0x00002abc .long L0_1_set_69
	0xb5, 0xdc, 0xff, 0xff, //0x00002ac0 .long L0_1_set_69
	0xb5, 0xdc, 0xff, 0xff, //0x00002ac4 .long L0_1_set_69
	0xb5, 0xdc, 0xff, 0xff, //0x00002ac8 .long L0_1_set_69
	0xb5, 0xdc, 0xff, 0xff, //0x00002acc .long L0_1_set_69
	0xb5, 0xdc, 0xff, 0xff, //0x00002ad0 .long L0_1_set_69
	0xb5, 0xdc, 0xff, 0xff, //0x00002ad4 .long L0_1_set_69
	0xb5, 0xdc, 0xff, 0xff, //0x00002ad8 .long L0_1_set_69
	0xb5, 0xdc, 0xff, 0xff, //0x00002adc .long L0_1_set_69
	0xb5, 0xdc, 0xff, 0xff, //0x00002ae0 .long L0_1_set_69
	0xb6, 0xfd, 0xff, 0xff, //0x00002ae4 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002ae8 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002aec .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002af0 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002af4 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002af8 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002afc .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b00 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b04 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b08 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b0c .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b10 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b14 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b18 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b1c .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b20 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b24 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b28 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b2c .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b30 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b34 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b38 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b3c .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b40 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b44 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b48 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b4c .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b50 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b54 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b58 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b5c .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b60 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b64 .long L0_1_set_497
	0xa1, 0xe8, 0xff, 0xff, //0x00002b68 .long L0_1_set_229
	0xb6, 0xfd, 0xff, 0xff, //0x00002b6c .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b70 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b74 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b78 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b7c .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b80 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b84 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b88 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b8c .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b90 .long L0_1_set_497
	0x0c, 0xe9, 0xff, 0xff, //0x00002b94 .long L0_1_set_240
	0xb6, 0xfd, 0xff, 0xff, //0x00002b98 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002b9c .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002ba0 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002ba4 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002ba8 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002bac .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002bb0 .long L0_1_set_497
	0xc5, 0xe8, 0xff, 0xff, //0x00002bb4 .long L0_1_set_231
	0xb6, 0xfd, 0xff, 0xff, //0x00002bb8 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002bbc .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002bc0 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002bc4 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002bc8 .long L0_1_set_497
	0x49, 0xe9, 0xff, 0xff, //0x00002bcc .long L0_1_set_243
	0xb6, 0xfd, 0xff, 0xff, //0x00002bd0 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002bd4 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002bd8 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002bdc .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002be0 .long L0_1_set_497
	0xb6, 0xfd, 0xff, 0xff, //0x00002be4 .long L0_1_set_497
	0xe8, 0xe8, 0xff, 0xff, //0x00002be8 .long L0_1_set_238
	// // .set L0_2_set_311, LBB0_311-LJTI0_2
	// // .set L0_2_set_326, LBB0_326-LJTI0_2
	// // .set L0_2_set_318, LBB0_318-LJTI0_2
	// // .set L0_2_set_313, LBB0_313-LJTI0_2
	// // .set L0_2_set_316, LBB0_316-LJTI0_2
	//0x00002bec LJTI0_2
	0x8b, 0xec, 0xff, 0xff, //0x00002bec .long L0_2_set_311
	0x68, 0xed, 0xff, 0xff, //0x00002bf0 .long L0_2_set_326
	0x8b, 0xec, 0xff, 0xff, //0x00002bf4 .long L0_2_set_311
	0xef, 0xec, 0xff, 0xff, //0x00002bf8 .long L0_2_set_318
	0x68, 0xed, 0xff, 0xff, //0x00002bfc .long L0_2_set_326
	0xa4, 0xec, 0xff, 0xff, //0x00002c00 .long L0_2_set_313
	0xa4, 0xec, 0xff, 0xff, //0x00002c04 .long L0_2_set_313
	0xa4, 0xec, 0xff, 0xff, //0x00002c08 .long L0_2_set_313
	0xa4, 0xec, 0xff, 0xff, //0x00002c0c .long L0_2_set_313
	0xa4, 0xec, 0xff, 0xff, //0x00002c10 .long L0_2_set_313
	0xa4, 0xec, 0xff, 0xff, //0x00002c14 .long L0_2_set_313
	0xa4, 0xec, 0xff, 0xff, //0x00002c18 .long L0_2_set_313
	0xa4, 0xec, 0xff, 0xff, //0x00002c1c .long L0_2_set_313
	0xa4, 0xec, 0xff, 0xff, //0x00002c20 .long L0_2_set_313
	0xa4, 0xec, 0xff, 0xff, //0x00002c24 .long L0_2_set_313
	0x68, 0xed, 0xff, 0xff, //0x00002c28 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c2c .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c30 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c34 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c38 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c3c .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c40 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c44 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c48 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c4c .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c50 .long L0_2_set_326
	0xd4, 0xec, 0xff, 0xff, //0x00002c54 .long L0_2_set_316
	0x68, 0xed, 0xff, 0xff, //0x00002c58 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c5c .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c60 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c64 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c68 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c6c .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c70 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c74 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c78 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c7c .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c80 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c84 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c88 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c8c .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c90 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c94 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c98 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002c9c .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002ca0 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002ca4 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002ca8 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002cac .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002cb0 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002cb4 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002cb8 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002cbc .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002cc0 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002cc4 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002cc8 .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002ccc .long L0_2_set_326
	0x68, 0xed, 0xff, 0xff, //0x00002cd0 .long L0_2_set_326
	0xd4, 0xec, 0xff, 0xff, //0x00002cd4 .long L0_2_set_316
	// // .set L0_3_set_176, LBB0_176-LJTI0_3
	// // .set L0_3_set_187, LBB0_187-LJTI0_3
	// // .set L0_3_set_178, LBB0_178-LJTI0_3
	// // .set L0_3_set_173, LBB0_173-LJTI0_3
	// // .set L0_3_set_171, LBB0_171-LJTI0_3
	//0x00002cd8 LJTI0_3
	0xb8, 0xdd, 0xff, 0xff, //0x00002cd8 .long L0_3_set_176
	0xc6, 0xe2, 0xff, 0xff, //0x00002cdc .long L0_3_set_187
	0xb8, 0xdd, 0xff, 0xff, //0x00002ce0 .long L0_3_set_176
	0xd3, 0xdd, 0xff, 0xff, //0x00002ce4 .long L0_3_set_178
	0xc6, 0xe2, 0xff, 0xff, //0x00002ce8 .long L0_3_set_187
	0x88, 0xdd, 0xff, 0xff, //0x00002cec .long L0_3_set_173
	0x88, 0xdd, 0xff, 0xff, //0x00002cf0 .long L0_3_set_173
	0x88, 0xdd, 0xff, 0xff, //0x00002cf4 .long L0_3_set_173
	0x88, 0xdd, 0xff, 0xff, //0x00002cf8 .long L0_3_set_173
	0x88, 0xdd, 0xff, 0xff, //0x00002cfc .long L0_3_set_173
	0x88, 0xdd, 0xff, 0xff, //0x00002d00 .long L0_3_set_173
	0x88, 0xdd, 0xff, 0xff, //0x00002d04 .long L0_3_set_173
	0x88, 0xdd, 0xff, 0xff, //0x00002d08 .long L0_3_set_173
	0x88, 0xdd, 0xff, 0xff, //0x00002d0c .long L0_3_set_173
	0x88, 0xdd, 0xff, 0xff, //0x00002d10 .long L0_3_set_173
	0xc6, 0xe2, 0xff, 0xff, //0x00002d14 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d18 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d1c .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d20 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d24 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d28 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d2c .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d30 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d34 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d38 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d3c .long L0_3_set_187
	0x64, 0xdd, 0xff, 0xff, //0x00002d40 .long L0_3_set_171
	0xc6, 0xe2, 0xff, 0xff, //0x00002d44 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d48 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d4c .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d50 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d54 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d58 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d5c .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d60 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d64 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d68 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d6c .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d70 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d74 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d78 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d7c .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d80 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d84 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d88 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d8c .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d90 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d94 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d98 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002d9c .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002da0 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002da4 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002da8 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002dac .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002db0 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002db4 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002db8 .long L0_3_set_187
	0xc6, 0xe2, 0xff, 0xff, //0x00002dbc .long L0_3_set_187
	0x64, 0xdd, 0xff, 0xff, //0x00002dc0 .long L0_3_set_171
	//0x00002dc4 .p2align 2, 0x00
	//0x00002dc4 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00002dc4 .long 2
}
 
