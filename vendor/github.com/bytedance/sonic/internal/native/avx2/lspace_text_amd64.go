// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_lspace = []byte{
	// .p2align 5, 0x00
	// LCPI0_0
	0x20, // .byte 32
	0x00, //0x00000001 .byte 0
	0x00, //0x00000002 .byte 0
	0x00, //0x00000003 .byte 0
	0x00, //0x00000004 .byte 0
	0x00, //0x00000005 .byte 0
	0x00, //0x00000006 .byte 0
	0x00, //0x00000007 .byte 0
	0x00, //0x00000008 .byte 0
	0x09, //0x00000009 .byte 9
	0x0a, //0x0000000a .byte 10
	0x00, //0x0000000b .byte 0
	0x00, //0x0000000c .byte 0
	0x0d, //0x0000000d .byte 13
	0x00, //0x0000000e .byte 0
	0x00, //0x0000000f .byte 0
	0x20, //0x00000010 .byte 32
	0x00, //0x00000011 .byte 0
	0x00, //0x00000012 .byte 0
	0x00, //0x00000013 .byte 0
	0x00, //0x00000014 .byte 0
	0x00, //0x00000015 .byte 0
	0x00, //0x00000016 .byte 0
	0x00, //0x00000017 .byte 0
	0x00, //0x00000018 .byte 0
	0x09, //0x00000019 .byte 9
	0x0a, //0x0000001a .byte 10
	0x00, //0x0000001b .byte 0
	0x00, //0x0000001c .byte 0
	0x0d, //0x0000001d .byte 13
	0x00, //0x0000001e .byte 0
	0x00, //0x0000001f .byte 0
	//0x00000020 .p2align 4, 0x90
	//0x00000020 _lspace
	0x55, //0x00000020 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000021 movq         %rsp, %rbp
	0x48, 0x8d, 0x04, 0x17, //0x00000024 leaq         (%rdi,%rdx), %rax
	0x49, 0x89, 0xf1, //0x00000028 movq         %rsi, %r9
	0x49, 0x29, 0xd1, //0x0000002b subq         %rdx, %r9
	0x49, 0x83, 0xf9, 0x20, //0x0000002e cmpq         $32, %r9
	0x0f, 0x82, 0x5a, 0x00, 0x00, 0x00, //0x00000032 jb           LBB0_4
	0x48, 0x29, 0xd6, //0x00000038 subq         %rdx, %rsi
	0x48, 0x83, 0xc6, 0xe0, //0x0000003b addq         $-32, %rsi
	0x48, 0x89, 0xf1, //0x0000003f movq         %rsi, %rcx
	0x48, 0x83, 0xe1, 0xe0, //0x00000042 andq         $-32, %rcx
	0x48, 0x01, 0xd1, //0x00000046 addq         %rdx, %rcx
	0x4c, 0x8d, 0x44, 0x0f, 0x20, //0x00000049 leaq         $32(%rdi,%rcx), %r8
	0x83, 0xe6, 0x1f, //0x0000004e andl         $31, %esi
	0xc5, 0xfe, 0x6f, 0x05, 0xa7, 0xff, 0xff, 0xff, //0x00000051 vmovdqu      $-89(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000059 .p2align 4, 0x90
	//0x00000060 LBB0_2
	0xc5, 0xfe, 0x6f, 0x08, //0x00000060 vmovdqu      (%rax), %ymm1
	0xc4, 0xe2, 0x7d, 0x00, 0xd1, //0x00000064 vpshufb      %ymm1, %ymm0, %ymm2
	0xc5, 0xf5, 0x74, 0xca, //0x00000069 vpcmpeqb     %ymm2, %ymm1, %ymm1
	0xc5, 0xfd, 0xd7, 0xd1, //0x0000006d vpmovmskb    %ymm1, %edx
	0x83, 0xfa, 0xff, //0x00000071 cmpl         $-1, %edx
	0x0f, 0x85, 0x63, 0x00, 0x00, 0x00, //0x00000074 jne          LBB0_3
	0x48, 0x83, 0xc0, 0x20, //0x0000007a addq         $32, %rax
	0x49, 0x83, 0xc1, 0xe0, //0x0000007e addq         $-32, %r9
	0x49, 0x83, 0xf9, 0x1f, //0x00000082 cmpq         $31, %r9
	0x0f, 0x87, 0xd4, 0xff, 0xff, 0xff, //0x00000086 ja           LBB0_2
	0x49, 0x89, 0xf1, //0x0000008c movq         %rsi, %r9
	0x4c, 0x89, 0xc0, //0x0000008f movq         %r8, %rax
	//0x00000092 LBB0_4
	0x4d, 0x85, 0xc9, //0x00000092 testq        %r9, %r9
	0x0f, 0x84, 0x3a, 0x00, 0x00, 0x00, //0x00000095 je           LBB0_13
	0x4e, 0x8d, 0x04, 0x08, //0x0000009b leaq         (%rax,%r9), %r8
	0x48, 0xff, 0xc0, //0x0000009f incq         %rax
	0x48, 0xbe, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000000a2 movabsq      $4294977024, %rsi
	//0x000000ac LBB0_6
	0x0f, 0xbe, 0x50, 0xff, //0x000000ac movsbl       $-1(%rax), %edx
	0x83, 0xfa, 0x20, //0x000000b0 cmpl         $32, %edx
	0x0f, 0x87, 0x38, 0x00, 0x00, 0x00, //0x000000b3 ja           LBB0_8
	0x48, 0x0f, 0xa3, 0xd6, //0x000000b9 btq          %rdx, %rsi
	0x0f, 0x83, 0x2e, 0x00, 0x00, 0x00, //0x000000bd jae          LBB0_8
	0x49, 0xff, 0xc9, //0x000000c3 decq         %r9
	0x48, 0xff, 0xc0, //0x000000c6 incq         %rax
	0x4d, 0x85, 0xc9, //0x000000c9 testq        %r9, %r9
	0x0f, 0x85, 0xda, 0xff, 0xff, 0xff, //0x000000cc jne          LBB0_6
	0x4c, 0x89, 0xc0, //0x000000d2 movq         %r8, %rax
	//0x000000d5 LBB0_13
	0x48, 0x29, 0xf8, //0x000000d5 subq         %rdi, %rax
	0x5d, //0x000000d8 popq         %rbp
	0xc5, 0xf8, 0x77, //0x000000d9 vzeroupper   
	0xc3, //0x000000dc retq         
	//0x000000dd LBB0_3
	0x48, 0x29, 0xf8, //0x000000dd subq         %rdi, %rax
	0xf7, 0xd2, //0x000000e0 notl         %edx
	0x48, 0x63, 0xca, //0x000000e2 movslq       %edx, %rcx
	0x48, 0x0f, 0xbc, 0xc9, //0x000000e5 bsfq         %rcx, %rcx
	0x48, 0x01, 0xc8, //0x000000e9 addq         %rcx, %rax
	0x5d, //0x000000ec popq         %rbp
	0xc5, 0xf8, 0x77, //0x000000ed vzeroupper   
	0xc3, //0x000000f0 retq         
	//0x000000f1 LBB0_8
	0x48, 0xf7, 0xd7, //0x000000f1 notq         %rdi
	0x48, 0x01, 0xf8, //0x000000f4 addq         %rdi, %rax
	0x5d, //0x000000f7 popq         %rbp
	0xc5, 0xf8, 0x77, //0x000000f8 vzeroupper   
	0xc3, //0x000000fb retq         
}
 
