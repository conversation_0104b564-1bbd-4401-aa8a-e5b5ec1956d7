// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx2

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__validate_utf8_fast = 272
)

const (
    _stack__validate_utf8_fast = 176
)

const (
    _size__validate_utf8_fast = 2600
)

var (
    _pcsp__validate_utf8_fast = [][2]uint32{
        {1, 0},
        {4, 8},
        {5, 16},
        {1738, 176},
        {1739, 168},
        {1743, 160},
        {2018, 176},
        {2019, 168},
        {2023, 160},
        {2600, 176},
    }
)

var _cfunc_validate_utf8_fast = []loader.CFunc{
    {"_validate_utf8_fast_entry", 0,  _entry__validate_utf8_fast, 0, nil},
    {"_validate_utf8_fast", _entry__validate_utf8_fast, _size__validate_utf8_fast, _stack__validate_utf8_fast, _pcsp__validate_utf8_fast},
}
