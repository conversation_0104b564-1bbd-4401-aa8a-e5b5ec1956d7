// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx

var _text_validate_one = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, // QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x00000010 LCPI0_1
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000010 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000020 LCPI0_2
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000020 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000030 LCPI0_3
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000030 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	//0x00000040 LCPI0_4
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x00000040 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	//0x00000050 LCPI0_5
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000050 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x00000060 LCPI0_6
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000060 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000070 LCPI0_7
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000070 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x00000080 LCPI0_8
	0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, //0x00000080 QUAD $0x6565656565656565; QUAD $0x6565656565656565  // .space 16, 'eeeeeeeeeeeeeeee'
	//0x00000090 .p2align 4, 0x90
	//0x00000090 _validate_one
	0x55, //0x00000090 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000091 movq         %rsp, %rbp
	0x41, 0x57, //0x00000094 pushq        %r15
	0x41, 0x56, //0x00000096 pushq        %r14
	0x41, 0x55, //0x00000098 pushq        %r13
	0x41, 0x54, //0x0000009a pushq        %r12
	0x53, //0x0000009c pushq        %rbx
	0x48, 0x83, 0xec, 0x68, //0x0000009d subq         $104, %rsp
	0x48, 0x89, 0x4d, 0x80, //0x000000a1 movq         %rcx, $-128(%rbp)
	0x49, 0x89, 0xd1, //0x000000a5 movq         %rdx, %r9
	0x41, 0xba, 0x01, 0x00, 0x00, 0x00, //0x000000a8 movl         $1, %r10d
	0xc4, 0xc1, 0xf9, 0x6e, 0xc2, //0x000000ae vmovq        %r10, %xmm0
	0xc5, 0xfa, 0x7f, 0x02, //0x000000b3 vmovdqu      %xmm0, (%rdx)
	0x48, 0x89, 0x7d, 0x90, //0x000000b7 movq         %rdi, $-112(%rbp)
	0x4c, 0x8b, 0x27, //0x000000bb movq         (%rdi), %r12
	0x4c, 0x89, 0xe0, //0x000000be movq         %r12, %rax
	0x48, 0xf7, 0xd0, //0x000000c1 notq         %rax
	0x48, 0x89, 0x45, 0xa0, //0x000000c4 movq         %rax, $-96(%rbp)
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x000000c8 movl         $1, %eax
	0x4c, 0x29, 0xe0, //0x000000cd subq         %r12, %rax
	0x48, 0x89, 0x45, 0x98, //0x000000d0 movq         %rax, $-104(%rbp)
	0x49, 0x8d, 0x44, 0x24, 0x40, //0x000000d4 leaq         $64(%r12), %rax
	0x48, 0x89, 0x45, 0x88, //0x000000d9 movq         %rax, $-120(%rbp)
	0x4c, 0x8b, 0x1e, //0x000000dd movq         (%rsi), %r11
	0x49, 0x8d, 0x44, 0x24, 0x05, //0x000000e0 leaq         $5(%r12), %rax
	0x48, 0x89, 0x85, 0x70, 0xff, 0xff, 0xff, //0x000000e5 movq         %rax, $-144(%rbp)
	0x48, 0xc7, 0x85, 0x78, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x000000ec movq         $-1, $-136(%rbp)
	0xc5, 0xfa, 0x6f, 0x05, 0x01, 0xff, 0xff, 0xff, //0x000000f7 vmovdqu      $-255(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x0d, 0x09, 0xff, 0xff, 0xff, //0x000000ff vmovdqu      $-247(%rip), %xmm1  /* LCPI0_1+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x3d, 0x11, 0xff, 0xff, 0xff, //0x00000107 vmovdqu      $-239(%rip), %xmm15  /* LCPI0_2+0(%rip) */
	0xc5, 0xe1, 0x76, 0xdb, //0x0000010f vpcmpeqd     %xmm3, %xmm3, %xmm3
	0xc5, 0x7a, 0x6f, 0x05, 0x15, 0xff, 0xff, 0xff, //0x00000113 vmovdqu      $-235(%rip), %xmm8  /* LCPI0_3+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x0d, 0x1d, 0xff, 0xff, 0xff, //0x0000011b vmovdqu      $-227(%rip), %xmm9  /* LCPI0_4+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x15, 0x25, 0xff, 0xff, 0xff, //0x00000123 vmovdqu      $-219(%rip), %xmm10  /* LCPI0_5+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x1d, 0x2d, 0xff, 0xff, 0xff, //0x0000012b vmovdqu      $-211(%rip), %xmm11  /* LCPI0_6+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x25, 0xe5, 0xfe, 0xff, 0xff, //0x00000133 vmovdqu      $-283(%rip), %xmm12  /* LCPI0_2+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x2d, 0x2d, 0xff, 0xff, 0xff, //0x0000013b vmovdqu      $-211(%rip), %xmm13  /* LCPI0_7+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x35, 0x35, 0xff, 0xff, 0xff, //0x00000143 vmovdqu      $-203(%rip), %xmm14  /* LCPI0_8+0(%rip) */
	0x48, 0x89, 0x75, 0xc8, //0x0000014b movq         %rsi, $-56(%rbp)
	0x4c, 0x89, 0x65, 0xd0, //0x0000014f movq         %r12, $-48(%rbp)
	0x48, 0x89, 0x55, 0xc0, //0x00000153 movq         %rdx, $-64(%rbp)
	0xe9, 0x5a, 0x00, 0x00, 0x00, //0x00000157 jmp          LBB0_4
	//0x0000015c LBB0_257
	0x48, 0x85, 0xc0, //0x0000015c testq        %rax, %rax
	0x49, 0x8d, 0x45, 0xff, //0x0000015f leaq         $-1(%r13), %rax
	0x49, 0xf7, 0xd5, //0x00000163 notq         %r13
	0x4c, 0x0f, 0x48, 0xe9, //0x00000166 cmovsq       %rcx, %r13
	0x49, 0x39, 0xc3, //0x0000016a cmpq         %rax, %r11
	0x49, 0x89, 0xcb, //0x0000016d movq         %rcx, %r11
	0x4d, 0x0f, 0x44, 0xdd, //0x00000170 cmoveq       %r13, %r11
	0x4c, 0x8b, 0x65, 0xd0, //0x00000174 movq         $-48(%rbp), %r12
	0x4d, 0x85, 0xdb, //0x00000178 testq        %r11, %r11
	0x0f, 0x88, 0xfe, 0x21, 0x00, 0x00, //0x0000017b js           LBB0_419
	//0x00000181 LBB0_258
	0x4d, 0x01, 0xfb, //0x00000181 addq         %r15, %r11
	//0x00000184 LBB0_1
	0x4c, 0x89, 0x1e, //0x00000184 movq         %r11, (%rsi)
	0x4c, 0x89, 0xf8, //0x00000187 movq         %r15, %rax
	0x4d, 0x85, 0xff, //0x0000018a testq        %r15, %r15
	0x0f, 0x88, 0xff, 0x21, 0x00, 0x00, //0x0000018d js           LBB0_423
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000193 .p2align 4, 0x90
	//0x000001a0 LBB0_2
	0x49, 0x8b, 0x11, //0x000001a0 movq         (%r9), %rdx
	0x49, 0x89, 0xd2, //0x000001a3 movq         %rdx, %r10
	0x48, 0x8b, 0x85, 0x78, 0xff, 0xff, 0xff, //0x000001a6 movq         $-136(%rbp), %rax
	0x48, 0x85, 0xd2, //0x000001ad testq        %rdx, %rdx
	0x0f, 0x84, 0xdc, 0x21, 0x00, 0x00, //0x000001b0 je           LBB0_423
	//0x000001b6 LBB0_4
	0x48, 0x8b, 0x45, 0x90, //0x000001b6 movq         $-112(%rbp), %rax
	0x48, 0x8b, 0x40, 0x08, //0x000001ba movq         $8(%rax), %rax
	0x4c, 0x89, 0xdb, //0x000001be movq         %r11, %rbx
	0x48, 0x29, 0xc3, //0x000001c1 subq         %rax, %rbx
	0x0f, 0x83, 0x36, 0x00, 0x00, 0x00, //0x000001c4 jae          LBB0_9
	0x43, 0x8a, 0x14, 0x1c, //0x000001ca movb         (%r12,%r11), %dl
	0x80, 0xfa, 0x0d, //0x000001ce cmpb         $13, %dl
	0x0f, 0x84, 0x29, 0x00, 0x00, 0x00, //0x000001d1 je           LBB0_9
	0x80, 0xfa, 0x20, //0x000001d7 cmpb         $32, %dl
	0x0f, 0x84, 0x20, 0x00, 0x00, 0x00, //0x000001da je           LBB0_9
	0x80, 0xc2, 0xf7, //0x000001e0 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x000001e3 cmpb         $1, %dl
	0x0f, 0x86, 0x14, 0x00, 0x00, 0x00, //0x000001e6 jbe          LBB0_9
	0x4d, 0x89, 0xdf, //0x000001ec movq         %r11, %r15
	0xe9, 0x39, 0x01, 0x00, 0x00, //0x000001ef jmp          LBB0_30
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000001f4 .p2align 4, 0x90
	//0x00000200 LBB0_9
	0x4d, 0x8d, 0x7b, 0x01, //0x00000200 leaq         $1(%r11), %r15
	0x49, 0x39, 0xc7, //0x00000204 cmpq         %rax, %r15
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000207 jae          LBB0_13
	0x43, 0x8a, 0x14, 0x3c, //0x0000020d movb         (%r12,%r15), %dl
	0x80, 0xfa, 0x0d, //0x00000211 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000214 je           LBB0_13
	0x80, 0xfa, 0x20, //0x0000021a cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000021d je           LBB0_13
	0x80, 0xc2, 0xf7, //0x00000223 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00000226 cmpb         $1, %dl
	0x0f, 0x87, 0xfe, 0x00, 0x00, 0x00, //0x00000229 ja           LBB0_30
	0x90, //0x0000022f .p2align 4, 0x90
	//0x00000230 LBB0_13
	0x4d, 0x8d, 0x7b, 0x02, //0x00000230 leaq         $2(%r11), %r15
	0x49, 0x39, 0xc7, //0x00000234 cmpq         %rax, %r15
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000237 jae          LBB0_17
	0x43, 0x8a, 0x14, 0x3c, //0x0000023d movb         (%r12,%r15), %dl
	0x80, 0xfa, 0x0d, //0x00000241 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000244 je           LBB0_17
	0x80, 0xfa, 0x20, //0x0000024a cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000024d je           LBB0_17
	0x80, 0xc2, 0xf7, //0x00000253 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00000256 cmpb         $1, %dl
	0x0f, 0x87, 0xce, 0x00, 0x00, 0x00, //0x00000259 ja           LBB0_30
	0x90, //0x0000025f .p2align 4, 0x90
	//0x00000260 LBB0_17
	0x4d, 0x8d, 0x7b, 0x03, //0x00000260 leaq         $3(%r11), %r15
	0x49, 0x39, 0xc7, //0x00000264 cmpq         %rax, %r15
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000267 jae          LBB0_21
	0x43, 0x8a, 0x14, 0x3c, //0x0000026d movb         (%r12,%r15), %dl
	0x80, 0xfa, 0x0d, //0x00000271 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000274 je           LBB0_21
	0x80, 0xfa, 0x20, //0x0000027a cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000027d je           LBB0_21
	0x80, 0xc2, 0xf7, //0x00000283 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00000286 cmpb         $1, %dl
	0x0f, 0x87, 0x9e, 0x00, 0x00, 0x00, //0x00000289 ja           LBB0_30
	0x90, //0x0000028f .p2align 4, 0x90
	//0x00000290 LBB0_21
	0x49, 0x8d, 0x53, 0x04, //0x00000290 leaq         $4(%r11), %rdx
	0x48, 0x39, 0xd0, //0x00000294 cmpq         %rdx, %rax
	0x0f, 0x86, 0xa1, 0x20, 0x00, 0x00, //0x00000297 jbe          LBB0_413
	0x48, 0x39, 0xd0, //0x0000029d cmpq         %rdx, %rax
	0x0f, 0x84, 0x5a, 0x00, 0x00, 0x00, //0x000002a0 je           LBB0_27
	0x49, 0x8d, 0x14, 0x04, //0x000002a6 leaq         (%r12,%rax), %rdx
	0x48, 0x83, 0xc3, 0x04, //0x000002aa addq         $4, %rbx
	0x4c, 0x03, 0x9d, 0x70, 0xff, 0xff, 0xff, //0x000002ae addq         $-144(%rbp), %r11
	0x4d, 0x89, 0xdf, //0x000002b5 movq         %r11, %r15
	0x48, 0xb9, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000002b8 movabsq      $4294977024, %rcx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000002c2 .p2align 4, 0x90
	//0x000002d0 LBB0_24
	0x41, 0x0f, 0xbe, 0x7f, 0xff, //0x000002d0 movsbl       $-1(%r15), %edi
	0x83, 0xff, 0x20, //0x000002d5 cmpl         $32, %edi
	0x0f, 0x87, 0x42, 0x00, 0x00, 0x00, //0x000002d8 ja           LBB0_29
	0x48, 0x0f, 0xa3, 0xf9, //0x000002de btq          %rdi, %rcx
	0x0f, 0x83, 0x38, 0x00, 0x00, 0x00, //0x000002e2 jae          LBB0_29
	0x49, 0xff, 0xc7, //0x000002e8 incq         %r15
	0x48, 0xff, 0xc3, //0x000002eb incq         %rbx
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x000002ee jne          LBB0_24
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x000002f4 jmp          LBB0_28
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000002f9 .p2align 4, 0x90
	//0x00000300 LBB0_27
	0x4c, 0x01, 0xe2, //0x00000300 addq         %r12, %rdx
	//0x00000303 LBB0_28
	0x4c, 0x29, 0xe2, //0x00000303 subq         %r12, %rdx
	0x49, 0x89, 0xd7, //0x00000306 movq         %rdx, %r15
	0x49, 0x39, 0xc7, //0x00000309 cmpq         %rax, %r15
	0x0f, 0x82, 0x1b, 0x00, 0x00, 0x00, //0x0000030c jb           LBB0_30
	0xe9, 0x2a, 0x20, 0x00, 0x00, //0x00000312 jmp          LBB0_414
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000317 .p2align 4, 0x90
	//0x00000320 LBB0_29
	0x4c, 0x03, 0x7d, 0xa0, //0x00000320 addq         $-96(%rbp), %r15
	0x49, 0x39, 0xc7, //0x00000324 cmpq         %rax, %r15
	0x0f, 0x83, 0x14, 0x20, 0x00, 0x00, //0x00000327 jae          LBB0_414
	//0x0000032d LBB0_30
	0x4d, 0x8d, 0x5f, 0x01, //0x0000032d leaq         $1(%r15), %r11
	0x4c, 0x89, 0x1e, //0x00000331 movq         %r11, (%rsi)
	0x43, 0x0f, 0xbe, 0x1c, 0x3c, //0x00000334 movsbl       (%r12,%r15), %ebx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000339 movq         $-1, %rax
	0x85, 0xdb, //0x00000340 testl        %ebx, %ebx
	0x0f, 0x84, 0x4a, 0x20, 0x00, 0x00, //0x00000342 je           LBB0_423
	0x4d, 0x89, 0xf8, //0x00000348 movq         %r15, %r8
	0x49, 0xf7, 0xd0, //0x0000034b notq         %r8
	0x49, 0x8d, 0x52, 0xff, //0x0000034e leaq         $-1(%r10), %rdx
	0x43, 0x8b, 0x3c, 0xd1, //0x00000352 movl         (%r9,%r10,8), %edi
	0x48, 0x8b, 0x8d, 0x78, 0xff, 0xff, 0xff, //0x00000356 movq         $-136(%rbp), %rcx
	0x48, 0x83, 0xf9, 0xff, //0x0000035d cmpq         $-1, %rcx
	0x49, 0x0f, 0x44, 0xcf, //0x00000361 cmoveq       %r15, %rcx
	0x48, 0x89, 0x8d, 0x78, 0xff, 0xff, 0xff, //0x00000365 movq         %rcx, $-136(%rbp)
	0xff, 0xcf, //0x0000036c decl         %edi
	0x83, 0xff, 0x05, //0x0000036e cmpl         $5, %edi
	0x0f, 0x87, 0x27, 0x00, 0x00, 0x00, //0x00000371 ja           LBB0_36
	0x48, 0x8d, 0x0d, 0x46, 0x22, 0x00, 0x00, //0x00000377 leaq         $8774(%rip), %rcx  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x3c, 0xb9, //0x0000037e movslq       (%rcx,%rdi,4), %rdi
	0x48, 0x01, 0xcf, //0x00000382 addq         %rcx, %rdi
	0xff, 0xe7, //0x00000385 jmpq         *%rdi
	//0x00000387 LBB0_33
	0x83, 0xfb, 0x2c, //0x00000387 cmpl         $44, %ebx
	0x0f, 0x84, 0xb6, 0x04, 0x00, 0x00, //0x0000038a je           LBB0_100
	0x83, 0xfb, 0x5d, //0x00000390 cmpl         $93, %ebx
	0x0f, 0x84, 0x92, 0x04, 0x00, 0x00, //0x00000393 je           LBB0_35
	0xe9, 0xed, 0x1f, 0x00, 0x00, //0x00000399 jmp          LBB0_422
	//0x0000039e LBB0_36
	0x49, 0x89, 0x11, //0x0000039e movq         %rdx, (%r9)
	0x83, 0xfb, 0x7b, //0x000003a1 cmpl         $123, %ebx
	0x0f, 0x86, 0x19, 0x02, 0x00, 0x00, //0x000003a4 jbe          LBB0_63
	0xe9, 0xdc, 0x1f, 0x00, 0x00, //0x000003aa jmp          LBB0_422
	//0x000003af LBB0_37
	0x83, 0xfb, 0x2c, //0x000003af cmpl         $44, %ebx
	0x0f, 0x85, 0x6a, 0x04, 0x00, 0x00, //0x000003b2 jne          LBB0_38
	0x49, 0x81, 0xfa, 0xff, 0x0f, 0x00, 0x00, //0x000003b8 cmpq         $4095, %r10
	0x0f, 0x8f, 0x88, 0x1f, 0x00, 0x00, //0x000003bf jg           LBB0_441
	0x49, 0x8d, 0x42, 0x01, //0x000003c5 leaq         $1(%r10), %rax
	0x49, 0x89, 0x01, //0x000003c9 movq         %rax, (%r9)
	0x4b, 0xc7, 0x44, 0xd1, 0x08, 0x03, 0x00, 0x00, 0x00, //0x000003cc movq         $3, $8(%r9,%r10,8)
	0xe9, 0xc6, 0xfd, 0xff, 0xff, //0x000003d5 jmp          LBB0_2
	//0x000003da LBB0_39
	0x80, 0xfb, 0x22, //0x000003da cmpb         $34, %bl
	0x0f, 0x85, 0xa8, 0x1f, 0x00, 0x00, //0x000003dd jne          LBB0_422
	0x4b, 0xc7, 0x04, 0xd1, 0x04, 0x00, 0x00, 0x00, //0x000003e3 movq         $4, (%r9,%r10,8)
	0x48, 0x8b, 0x45, 0x90, //0x000003eb movq         $-112(%rbp), %rax
	0x48, 0x8b, 0x50, 0x08, //0x000003ef movq         $8(%rax), %rdx
	0xf6, 0x45, 0x80, 0x20, //0x000003f3 testb        $32, $-128(%rbp)
	0x4c, 0x89, 0x5d, 0xa8, //0x000003f7 movq         %r11, $-88(%rbp)
	0x48, 0x89, 0x55, 0xb0, //0x000003fb movq         %rdx, $-80(%rbp)
	0x0f, 0x85, 0x63, 0x04, 0x00, 0x00, //0x000003ff jne          LBB0_104
	0x49, 0x89, 0xd5, //0x00000405 movq         %rdx, %r13
	0x4d, 0x29, 0xdd, //0x00000408 subq         %r11, %r13
	0x0f, 0x84, 0x30, 0x21, 0x00, 0x00, //0x0000040b je           LBB0_456
	0x4c, 0x89, 0xd8, //0x00000411 movq         %r11, %rax
	0x4d, 0x01, 0xe3, //0x00000414 addq         %r12, %r11
	0x49, 0x83, 0xfd, 0x40, //0x00000417 cmpq         $64, %r13
	0x0f, 0x82, 0x0b, 0x1a, 0x00, 0x00, //0x0000041b jb           LBB0_353
	0x45, 0x89, 0xea, //0x00000421 movl         %r13d, %r10d
	0x41, 0x83, 0xe2, 0x3f, //0x00000424 andl         $63, %r10d
	0x4a, 0x8d, 0x4c, 0x02, 0xc0, //0x00000428 leaq         $-64(%rdx,%r8), %rcx
	0x48, 0x83, 0xe1, 0xc0, //0x0000042d andq         $-64, %rcx
	0x48, 0x01, 0xc1, //0x00000431 addq         %rax, %rcx
	0x48, 0x03, 0x4d, 0x88, //0x00000434 addq         $-120(%rbp), %rcx
	0x48, 0x89, 0x4d, 0xb8, //0x00000438 movq         %rcx, $-72(%rbp)
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000043c movq         $-1, %r9
	0x45, 0x31, 0xe4, //0x00000443 xorl         %r12d, %r12d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000446 .p2align 4, 0x90
	//0x00000450 LBB0_44
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x00000450 vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x00000455 vmovdqu      $16(%r11), %xmm4
	0xc4, 0xc1, 0x7a, 0x6f, 0x6b, 0x20, //0x0000045b vmovdqu      $32(%r11), %xmm5
	0xc4, 0xc1, 0x7a, 0x6f, 0x73, 0x30, //0x00000461 vmovdqu      $48(%r11), %xmm6
	0xc5, 0xe9, 0x74, 0xf8, //0x00000467 vpcmpeqb     %xmm0, %xmm2, %xmm7
	0xc5, 0xf9, 0xd7, 0xff, //0x0000046b vpmovmskb    %xmm7, %edi
	0xc5, 0xd9, 0x74, 0xf8, //0x0000046f vpcmpeqb     %xmm0, %xmm4, %xmm7
	0xc5, 0xf9, 0xd7, 0xcf, //0x00000473 vpmovmskb    %xmm7, %ecx
	0xc5, 0xd1, 0x74, 0xf8, //0x00000477 vpcmpeqb     %xmm0, %xmm5, %xmm7
	0xc5, 0xf9, 0xd7, 0xf7, //0x0000047b vpmovmskb    %xmm7, %esi
	0xc5, 0xc9, 0x74, 0xf8, //0x0000047f vpcmpeqb     %xmm0, %xmm6, %xmm7
	0xc5, 0xf9, 0xd7, 0xdf, //0x00000483 vpmovmskb    %xmm7, %ebx
	0xc5, 0xe9, 0x74, 0xd1, //0x00000487 vpcmpeqb     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xd2, //0x0000048b vpmovmskb    %xmm2, %edx
	0xc5, 0xd9, 0x74, 0xd1, //0x0000048f vpcmpeqb     %xmm1, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xc2, //0x00000493 vpmovmskb    %xmm2, %eax
	0xc5, 0xd1, 0x74, 0xd1, //0x00000497 vpcmpeqb     %xmm1, %xmm5, %xmm2
	0xc5, 0x79, 0xd7, 0xf2, //0x0000049b vpmovmskb    %xmm2, %r14d
	0xc5, 0xc9, 0x74, 0xd1, //0x0000049f vpcmpeqb     %xmm1, %xmm6, %xmm2
	0xc5, 0x79, 0xd7, 0xc2, //0x000004a3 vpmovmskb    %xmm2, %r8d
	0x48, 0xc1, 0xe3, 0x30, //0x000004a7 shlq         $48, %rbx
	0x48, 0xc1, 0xe6, 0x20, //0x000004ab shlq         $32, %rsi
	0x48, 0xc1, 0xe1, 0x10, //0x000004af shlq         $16, %rcx
	0x48, 0x09, 0xcf, //0x000004b3 orq          %rcx, %rdi
	0x48, 0x09, 0xf7, //0x000004b6 orq          %rsi, %rdi
	0x49, 0xc1, 0xe0, 0x30, //0x000004b9 shlq         $48, %r8
	0x49, 0xc1, 0xe6, 0x20, //0x000004bd shlq         $32, %r14
	0x48, 0xc1, 0xe0, 0x10, //0x000004c1 shlq         $16, %rax
	0x48, 0x09, 0xc2, //0x000004c5 orq          %rax, %rdx
	0x4c, 0x09, 0xf2, //0x000004c8 orq          %r14, %rdx
	0x4c, 0x09, 0xc2, //0x000004cb orq          %r8, %rdx
	0x49, 0x83, 0xf9, 0xff, //0x000004ce cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x000004d2 jne          LBB0_46
	0x48, 0x85, 0xd2, //0x000004d8 testq        %rdx, %rdx
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x000004db jne          LBB0_55
	//0x000004e1 LBB0_46
	0x48, 0x09, 0xdf, //0x000004e1 orq          %rbx, %rdi
	0x48, 0x89, 0xd0, //0x000004e4 movq         %rdx, %rax
	0x4c, 0x09, 0xe0, //0x000004e7 orq          %r12, %rax
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x000004ea jne          LBB0_56
	//0x000004f0 LBB0_47
	0x48, 0x85, 0xff, //0x000004f0 testq        %rdi, %rdi
	0x0f, 0x85, 0x86, 0x00, 0x00, 0x00, //0x000004f3 jne          LBB0_57
	//0x000004f9 LBB0_48
	0x49, 0x83, 0xc5, 0xc0, //0x000004f9 addq         $-64, %r13
	0x49, 0x83, 0xc3, 0x40, //0x000004fd addq         $64, %r11
	0x49, 0x83, 0xfd, 0x3f, //0x00000501 cmpq         $63, %r13
	0x0f, 0x87, 0x45, 0xff, 0xff, 0xff, //0x00000505 ja           LBB0_44
	0xe9, 0x1d, 0x12, 0x00, 0x00, //0x0000050b jmp          LBB0_49
	//0x00000510 LBB0_55
	0x4c, 0x89, 0xd8, //0x00000510 movq         %r11, %rax
	0x48, 0x2b, 0x45, 0xd0, //0x00000513 subq         $-48(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xca, //0x00000517 bsfq         %rdx, %r9
	0x49, 0x01, 0xc1, //0x0000051b addq         %rax, %r9
	0x48, 0x09, 0xdf, //0x0000051e orq          %rbx, %rdi
	0x48, 0x89, 0xd0, //0x00000521 movq         %rdx, %rax
	0x4c, 0x09, 0xe0, //0x00000524 orq          %r12, %rax
	0x0f, 0x84, 0xc3, 0xff, 0xff, 0xff, //0x00000527 je           LBB0_47
	//0x0000052d LBB0_56
	0x4c, 0x89, 0xe0, //0x0000052d movq         %r12, %rax
	0x48, 0xf7, 0xd0, //0x00000530 notq         %rax
	0x48, 0x21, 0xd0, //0x00000533 andq         %rdx, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x00000536 leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xe1, //0x0000053a orq          %r12, %rcx
	0x48, 0x89, 0xce, //0x0000053d movq         %rcx, %rsi
	0x48, 0xf7, 0xd6, //0x00000540 notq         %rsi
	0x48, 0x21, 0xd6, //0x00000543 andq         %rdx, %rsi
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000546 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd6, //0x00000550 andq         %rdx, %rsi
	0x45, 0x31, 0xe4, //0x00000553 xorl         %r12d, %r12d
	0x48, 0x01, 0xc6, //0x00000556 addq         %rax, %rsi
	0x41, 0x0f, 0x92, 0xc4, //0x00000559 setb         %r12b
	0x48, 0x01, 0xf6, //0x0000055d addq         %rsi, %rsi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000560 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc6, //0x0000056a xorq         %rax, %rsi
	0x48, 0x21, 0xce, //0x0000056d andq         %rcx, %rsi
	0x48, 0xf7, 0xd6, //0x00000570 notq         %rsi
	0x48, 0x21, 0xf7, //0x00000573 andq         %rsi, %rdi
	0x48, 0x85, 0xff, //0x00000576 testq        %rdi, %rdi
	0x0f, 0x84, 0x7a, 0xff, 0xff, 0xff, //0x00000579 je           LBB0_48
	//0x0000057f LBB0_57
	0x48, 0x0f, 0xbc, 0xc7, //0x0000057f bsfq         %rdi, %rax
	//0x00000583 LBB0_58
	0x4c, 0x03, 0x5d, 0x98, //0x00000583 addq         $-104(%rbp), %r11
	0x49, 0x01, 0xc3, //0x00000587 addq         %rax, %r11
	0x4c, 0x8b, 0x65, 0xd0, //0x0000058a movq         $-48(%rbp), %r12
	0xe9, 0x3a, 0x09, 0x00, 0x00, //0x0000058e jmp          LBB0_187
	//0x00000593 LBB0_59
	0x80, 0xfb, 0x3a, //0x00000593 cmpb         $58, %bl
	0x0f, 0x85, 0xef, 0x1d, 0x00, 0x00, //0x00000596 jne          LBB0_422
	0x4b, 0xc7, 0x04, 0xd1, 0x00, 0x00, 0x00, 0x00, //0x0000059c movq         $0, (%r9,%r10,8)
	0xe9, 0xf7, 0xfb, 0xff, 0xff, //0x000005a4 jmp          LBB0_2
	//0x000005a9 LBB0_61
	0x80, 0xfb, 0x5d, //0x000005a9 cmpb         $93, %bl
	0x0f, 0x84, 0x79, 0x02, 0x00, 0x00, //0x000005ac je           LBB0_35
	0x4b, 0xc7, 0x04, 0xd1, 0x01, 0x00, 0x00, 0x00, //0x000005b2 movq         $1, (%r9,%r10,8)
	0x83, 0xfb, 0x7b, //0x000005ba cmpl         $123, %ebx
	0x0f, 0x87, 0xc8, 0x1d, 0x00, 0x00, //0x000005bd ja           LBB0_422
	//0x000005c3 LBB0_63
	0x4f, 0x8d, 0x14, 0x3c, //0x000005c3 leaq         (%r12,%r15), %r10
	0x89, 0xd9, //0x000005c7 movl         %ebx, %ecx
	0x48, 0x8d, 0x15, 0x0c, 0x20, 0x00, 0x00, //0x000005c9 leaq         $8204(%rip), %rdx  /* LJTI0_1+0(%rip) */
	0x48, 0x63, 0x0c, 0x8a, //0x000005d0 movslq       (%rdx,%rcx,4), %rcx
	0x48, 0x01, 0xd1, //0x000005d4 addq         %rdx, %rcx
	0xff, 0xe1, //0x000005d7 jmpq         *%rcx
	//0x000005d9 LBB0_66
	0x48, 0x8b, 0x45, 0x90, //0x000005d9 movq         $-112(%rbp), %rax
	0x48, 0x8b, 0x78, 0x08, //0x000005dd movq         $8(%rax), %rdi
	0x4c, 0x29, 0xff, //0x000005e1 subq         %r15, %rdi
	0x0f, 0x84, 0x89, 0x1d, 0x00, 0x00, //0x000005e4 je           LBB0_417
	0x41, 0x80, 0x3a, 0x30, //0x000005ea cmpb         $48, (%r10)
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x000005ee jne          LBB0_71
	0x48, 0x83, 0xff, 0x01, //0x000005f4 cmpq         $1, %rdi
	0x0f, 0x84, 0x86, 0xfb, 0xff, 0xff, //0x000005f8 je           LBB0_1
	0x43, 0x8a, 0x04, 0x1c, //0x000005fe movb         (%r12,%r11), %al
	0x04, 0xd2, //0x00000602 addb         $-46, %al
	0x3c, 0x37, //0x00000604 cmpb         $55, %al
	0x0f, 0x87, 0x78, 0xfb, 0xff, 0xff, //0x00000606 ja           LBB0_1
	0x0f, 0xb6, 0xc0, //0x0000060c movzbl       %al, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x0000060f movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x00000619 btq          %rax, %rcx
	0x0f, 0x83, 0x61, 0xfb, 0xff, 0xff, //0x0000061d jae          LBB0_1
	//0x00000623 LBB0_71
	0x48, 0x83, 0xff, 0x10, //0x00000623 cmpq         $16, %rdi
	0x0f, 0x82, 0xde, 0x17, 0x00, 0x00, //0x00000627 jb           LBB0_342
	0x4c, 0x8d, 0x4f, 0xf0, //0x0000062d leaq         $-16(%rdi), %r9
	0x4c, 0x89, 0xc8, //0x00000631 movq         %r9, %rax
	0x48, 0x83, 0xe0, 0xf0, //0x00000634 andq         $-16, %rax
	0x4a, 0x8d, 0x44, 0x10, 0x10, //0x00000638 leaq         $16(%rax,%r10), %rax
	0x48, 0x89, 0x45, 0xa8, //0x0000063d movq         %rax, $-88(%rbp)
	0x41, 0x83, 0xe1, 0x0f, //0x00000641 andl         $15, %r9d
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00000645 movq         $-1, %r12
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x0000064c movq         $-1, %r13
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00000653 movq         $-1, %r11
	0x4d, 0x89, 0xd6, //0x0000065a movq         %r10, %r14
	0x90, 0x90, 0x90, //0x0000065d .p2align 4, 0x90
	//0x00000660 LBB0_73
	0xc4, 0xc1, 0x7a, 0x6f, 0x16, //0x00000660 vmovdqu      (%r14), %xmm2
	0xc4, 0xc1, 0x69, 0x64, 0xe0, //0x00000665 vpcmpgtb     %xmm8, %xmm2, %xmm4
	0xc5, 0xb1, 0x64, 0xea, //0x0000066a vpcmpgtb     %xmm2, %xmm9, %xmm5
	0xc5, 0xd9, 0xdb, 0xe5, //0x0000066e vpand        %xmm5, %xmm4, %xmm4
	0xc5, 0xa9, 0x74, 0xea, //0x00000672 vpcmpeqb     %xmm2, %xmm10, %xmm5
	0xc5, 0xa1, 0x74, 0xf2, //0x00000676 vpcmpeqb     %xmm2, %xmm11, %xmm6
	0xc5, 0xc9, 0xeb, 0xed, //0x0000067a vpor         %xmm5, %xmm6, %xmm5
	0xc5, 0x99, 0xeb, 0xf2, //0x0000067e vpor         %xmm2, %xmm12, %xmm6
	0xc5, 0x91, 0x74, 0xd2, //0x00000682 vpcmpeqb     %xmm2, %xmm13, %xmm2
	0xc5, 0x89, 0x74, 0xf6, //0x00000686 vpcmpeqb     %xmm6, %xmm14, %xmm6
	0xc5, 0xc9, 0xeb, 0xfa, //0x0000068a vpor         %xmm2, %xmm6, %xmm7
	0xc5, 0xd1, 0xeb, 0xe4, //0x0000068e vpor         %xmm4, %xmm5, %xmm4
	0xc5, 0xc1, 0xeb, 0xe4, //0x00000692 vpor         %xmm4, %xmm7, %xmm4
	0xc5, 0x79, 0xd7, 0xc2, //0x00000696 vpmovmskb    %xmm2, %r8d
	0xc5, 0xf9, 0xd7, 0xc6, //0x0000069a vpmovmskb    %xmm6, %eax
	0xc5, 0xf9, 0xd7, 0xd5, //0x0000069e vpmovmskb    %xmm5, %edx
	0xc5, 0xf9, 0xd7, 0xcc, //0x000006a2 vpmovmskb    %xmm4, %ecx
	0xbe, 0xaa, 0xaa, 0xaa, 0xaa, //0x000006a6 movl         $2863311530, %esi
	0x48, 0x81, 0xc6, 0x55, 0x55, 0x55, 0x55, //0x000006ab addq         $1431655765, %rsi
	0x48, 0x31, 0xce, //0x000006b2 xorq         %rcx, %rsi
	0x48, 0x0f, 0xbc, 0xce, //0x000006b5 bsfq         %rsi, %rcx
	0x83, 0xf9, 0x10, //0x000006b9 cmpl         $16, %ecx
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x000006bc je           LBB0_75
	0xbe, 0xff, 0xff, 0xff, 0xff, //0x000006c2 movl         $-1, %esi
	0xd3, 0xe6, //0x000006c7 shll         %cl, %esi
	0xf7, 0xd6, //0x000006c9 notl         %esi
	0x41, 0x21, 0xf0, //0x000006cb andl         %esi, %r8d
	0x21, 0xf0, //0x000006ce andl         %esi, %eax
	0x21, 0xd6, //0x000006d0 andl         %edx, %esi
	0x89, 0xf2, //0x000006d2 movl         %esi, %edx
	//0x000006d4 LBB0_75
	0x41, 0x8d, 0x70, 0xff, //0x000006d4 leal         $-1(%r8), %esi
	0x44, 0x21, 0xc6, //0x000006d8 andl         %r8d, %esi
	0x0f, 0x85, 0x17, 0x10, 0x00, 0x00, //0x000006db jne          LBB0_308
	0x8d, 0x70, 0xff, //0x000006e1 leal         $-1(%rax), %esi
	0x21, 0xc6, //0x000006e4 andl         %eax, %esi
	0x0f, 0x85, 0x0c, 0x10, 0x00, 0x00, //0x000006e6 jne          LBB0_308
	0x8d, 0x72, 0xff, //0x000006ec leal         $-1(%rdx), %esi
	0x21, 0xd6, //0x000006ef andl         %edx, %esi
	0x0f, 0x85, 0x01, 0x10, 0x00, 0x00, //0x000006f1 jne          LBB0_308
	0x45, 0x85, 0xc0, //0x000006f7 testl        %r8d, %r8d
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x000006fa je           LBB0_81
	0x4c, 0x89, 0xf3, //0x00000700 movq         %r14, %rbx
	0x4c, 0x29, 0xd3, //0x00000703 subq         %r10, %rbx
	0x41, 0x0f, 0xbc, 0xf0, //0x00000706 bsfl         %r8d, %esi
	0x48, 0x01, 0xde, //0x0000070a addq         %rbx, %rsi
	0x49, 0x83, 0xfb, 0xff, //0x0000070d cmpq         $-1, %r11
	0x0f, 0x85, 0x93, 0x13, 0x00, 0x00, //0x00000711 jne          LBB0_325
	0x49, 0x89, 0xf3, //0x00000717 movq         %rsi, %r11
	//0x0000071a LBB0_81
	0x85, 0xc0, //0x0000071a testl        %eax, %eax
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000071c je           LBB0_84
	0x4c, 0x89, 0xf6, //0x00000722 movq         %r14, %rsi
	0x4c, 0x29, 0xd6, //0x00000725 subq         %r10, %rsi
	0x0f, 0xbc, 0xc0, //0x00000728 bsfl         %eax, %eax
	0x48, 0x01, 0xf0, //0x0000072b addq         %rsi, %rax
	0x49, 0x83, 0xfd, 0xff, //0x0000072e cmpq         $-1, %r13
	0x0f, 0x85, 0x64, 0x11, 0x00, 0x00, //0x00000732 jne          LBB0_313
	0x49, 0x89, 0xc5, //0x00000738 movq         %rax, %r13
	//0x0000073b LBB0_84
	0x85, 0xd2, //0x0000073b testl        %edx, %edx
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000073d je           LBB0_87
	0x4c, 0x89, 0xf6, //0x00000743 movq         %r14, %rsi
	0x4c, 0x29, 0xd6, //0x00000746 subq         %r10, %rsi
	0x0f, 0xbc, 0xc2, //0x00000749 bsfl         %edx, %eax
	0x48, 0x01, 0xf0, //0x0000074c addq         %rsi, %rax
	0x49, 0x83, 0xfc, 0xff, //0x0000074f cmpq         $-1, %r12
	0x0f, 0x85, 0x43, 0x11, 0x00, 0x00, //0x00000753 jne          LBB0_313
	0x49, 0x89, 0xc4, //0x00000759 movq         %rax, %r12
	//0x0000075c LBB0_87
	0x83, 0xf9, 0x10, //0x0000075c cmpl         $16, %ecx
	0x0f, 0x85, 0xa8, 0x04, 0x00, 0x00, //0x0000075f jne          LBB0_148
	0x49, 0x83, 0xc6, 0x10, //0x00000765 addq         $16, %r14
	0x48, 0x83, 0xc7, 0xf0, //0x00000769 addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x0f, //0x0000076d cmpq         $15, %rdi
	0x0f, 0x87, 0xe9, 0xfe, 0xff, 0xff, //0x00000771 ja           LBB0_73
	0x4d, 0x85, 0xc9, //0x00000777 testq        %r9, %r9
	0x0f, 0x84, 0xb1, 0x04, 0x00, 0x00, //0x0000077a je           LBB0_150
	//0x00000780 LBB0_90
	0x48, 0x8b, 0x7d, 0xa8, //0x00000780 movq         $-88(%rbp), %rdi
	0x4a, 0x8d, 0x0c, 0x0f, //0x00000784 leaq         (%rdi,%r9), %rcx
	0x48, 0x8d, 0x35, 0x29, 0x21, 0x00, 0x00, //0x00000788 leaq         $8489(%rip), %rsi  /* LJTI0_3+0(%rip) */
	0xe9, 0x18, 0x00, 0x00, 0x00, //0x0000078f jmp          LBB0_92
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000794 .p2align 4, 0x90
	//0x000007a0 LBB0_91
	0x48, 0x89, 0xc7, //0x000007a0 movq         %rax, %rdi
	0x49, 0xff, 0xc9, //0x000007a3 decq         %r9
	0x0f, 0x84, 0xfb, 0x10, 0x00, 0x00, //0x000007a6 je           LBB0_314
	//0x000007ac LBB0_92
	0x0f, 0xbe, 0x17, //0x000007ac movsbl       (%rdi), %edx
	0x83, 0xc2, 0xd5, //0x000007af addl         $-43, %edx
	0x83, 0xfa, 0x3a, //0x000007b2 cmpl         $58, %edx
	0x0f, 0x87, 0x72, 0x04, 0x00, 0x00, //0x000007b5 ja           LBB0_149
	0x48, 0x8d, 0x47, 0x01, //0x000007bb leaq         $1(%rdi), %rax
	0x48, 0x63, 0x14, 0x96, //0x000007bf movslq       (%rsi,%rdx,4), %rdx
	0x48, 0x01, 0xf2, //0x000007c3 addq         %rsi, %rdx
	0xff, 0xe2, //0x000007c6 jmpq         *%rdx
	//0x000007c8 LBB0_94
	0x48, 0x89, 0xc2, //0x000007c8 movq         %rax, %rdx
	0x4c, 0x29, 0xd2, //0x000007cb subq         %r10, %rdx
	0x49, 0x83, 0xfc, 0xff, //0x000007ce cmpq         $-1, %r12
	0x0f, 0x85, 0x86, 0x13, 0x00, 0x00, //0x000007d2 jne          LBB0_418
	0x48, 0xff, 0xca, //0x000007d8 decq         %rdx
	0x49, 0x89, 0xd4, //0x000007db movq         %rdx, %r12
	0xe9, 0xbd, 0xff, 0xff, 0xff, //0x000007de jmp          LBB0_91
	//0x000007e3 LBB0_96
	0x48, 0x89, 0xc2, //0x000007e3 movq         %rax, %rdx
	0x4c, 0x29, 0xd2, //0x000007e6 subq         %r10, %rdx
	0x49, 0x83, 0xfd, 0xff, //0x000007e9 cmpq         $-1, %r13
	0x0f, 0x85, 0x6b, 0x13, 0x00, 0x00, //0x000007ed jne          LBB0_418
	0x48, 0xff, 0xca, //0x000007f3 decq         %rdx
	0x49, 0x89, 0xd5, //0x000007f6 movq         %rdx, %r13
	0xe9, 0xa2, 0xff, 0xff, 0xff, //0x000007f9 jmp          LBB0_91
	//0x000007fe LBB0_98
	0x48, 0x89, 0xc2, //0x000007fe movq         %rax, %rdx
	0x4c, 0x29, 0xd2, //0x00000801 subq         %r10, %rdx
	0x49, 0x83, 0xfb, 0xff, //0x00000804 cmpq         $-1, %r11
	0x0f, 0x85, 0x50, 0x13, 0x00, 0x00, //0x00000808 jne          LBB0_418
	0x48, 0xff, 0xca, //0x0000080e decq         %rdx
	0x49, 0x89, 0xd3, //0x00000811 movq         %rdx, %r11
	0xe9, 0x87, 0xff, 0xff, 0xff, //0x00000814 jmp          LBB0_91
	//0x00000819 LBB0_64
	0x83, 0xfb, 0x22, //0x00000819 cmpl         $34, %ebx
	0x0f, 0x84, 0x19, 0x02, 0x00, 0x00, //0x0000081c je           LBB0_125
	//0x00000822 LBB0_38
	0x83, 0xfb, 0x7d, //0x00000822 cmpl         $125, %ebx
	0x0f, 0x85, 0x60, 0x1b, 0x00, 0x00, //0x00000825 jne          LBB0_422
	//0x0000082b LBB0_35
	0x49, 0x89, 0x11, //0x0000082b movq         %rdx, (%r9)
	0x49, 0x89, 0xd2, //0x0000082e movq         %rdx, %r10
	0x48, 0x8b, 0x85, 0x78, 0xff, 0xff, 0xff, //0x00000831 movq         $-136(%rbp), %rax
	0x48, 0x85, 0xd2, //0x00000838 testq        %rdx, %rdx
	0x0f, 0x85, 0x75, 0xf9, 0xff, 0xff, //0x0000083b jne          LBB0_4
	0xe9, 0x4c, 0x1b, 0x00, 0x00, //0x00000841 jmp          LBB0_423
	//0x00000846 LBB0_100
	0x49, 0x81, 0xfa, 0xff, 0x0f, 0x00, 0x00, //0x00000846 cmpq         $4095, %r10
	0x0f, 0x8f, 0xfa, 0x1a, 0x00, 0x00, //0x0000084d jg           LBB0_441
	0x49, 0x8d, 0x42, 0x01, //0x00000853 leaq         $1(%r10), %rax
	0x49, 0x89, 0x01, //0x00000857 movq         %rax, (%r9)
	0x4b, 0xc7, 0x44, 0xd1, 0x08, 0x00, 0x00, 0x00, 0x00, //0x0000085a movq         $0, $8(%r9,%r10,8)
	0xe9, 0x38, 0xf9, 0xff, 0xff, //0x00000863 jmp          LBB0_2
	//0x00000868 LBB0_104
	0x48, 0x89, 0xd0, //0x00000868 movq         %rdx, %rax
	0x4c, 0x29, 0xd8, //0x0000086b subq         %r11, %rax
	0x0f, 0x84, 0xcd, 0x1c, 0x00, 0x00, //0x0000086e je           LBB0_456
	0x4c, 0x89, 0xd9, //0x00000874 movq         %r11, %rcx
	0x4d, 0x01, 0xe3, //0x00000877 addq         %r12, %r11
	0x48, 0x83, 0xf8, 0x40, //0x0000087a cmpq         $64, %rax
	0x0f, 0x82, 0xc1, 0x15, 0x00, 0x00, //0x0000087e jb           LBB0_354
	0x89, 0xc6, //0x00000884 movl         %eax, %esi
	0x83, 0xe6, 0x3f, //0x00000886 andl         $63, %esi
	0x48, 0x89, 0x75, 0xb8, //0x00000889 movq         %rsi, $-72(%rbp)
	0x4e, 0x8d, 0x54, 0x02, 0xc0, //0x0000088d leaq         $-64(%rdx,%r8), %r10
	0x49, 0x83, 0xe2, 0xc0, //0x00000892 andq         $-64, %r10
	0x49, 0x01, 0xca, //0x00000896 addq         %rcx, %r10
	0x4c, 0x03, 0x55, 0x88, //0x00000899 addq         $-120(%rbp), %r10
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000089d movq         $-1, %r9
	0x45, 0x31, 0xc0, //0x000008a4 xorl         %r8d, %r8d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000008a7 .p2align 4, 0x90
	//0x000008b0 LBB0_107
	0xc4, 0xc1, 0x7a, 0x6f, 0x33, //0x000008b0 vmovdqu      (%r11), %xmm6
	0xc4, 0xc1, 0x7a, 0x6f, 0x6b, 0x10, //0x000008b5 vmovdqu      $16(%r11), %xmm5
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x20, //0x000008bb vmovdqu      $32(%r11), %xmm4
	0xc4, 0xc1, 0x7a, 0x6f, 0x7b, 0x30, //0x000008c1 vmovdqu      $48(%r11), %xmm7
	0xc5, 0xc9, 0x74, 0xd0, //0x000008c7 vpcmpeqb     %xmm0, %xmm6, %xmm2
	0xc5, 0xf9, 0xd7, 0xf2, //0x000008cb vpmovmskb    %xmm2, %esi
	0xc5, 0xd1, 0x74, 0xd0, //0x000008cf vpcmpeqb     %xmm0, %xmm5, %xmm2
	0xc5, 0xf9, 0xd7, 0xda, //0x000008d3 vpmovmskb    %xmm2, %ebx
	0xc5, 0xd9, 0x74, 0xd0, //0x000008d7 vpcmpeqb     %xmm0, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xca, //0x000008db vpmovmskb    %xmm2, %ecx
	0xc5, 0xc1, 0x74, 0xd0, //0x000008df vpcmpeqb     %xmm0, %xmm7, %xmm2
	0xc5, 0xf9, 0xd7, 0xd2, //0x000008e3 vpmovmskb    %xmm2, %edx
	0xc5, 0xc9, 0x74, 0xd1, //0x000008e7 vpcmpeqb     %xmm1, %xmm6, %xmm2
	0xc5, 0x79, 0xd7, 0xea, //0x000008eb vpmovmskb    %xmm2, %r13d
	0xc5, 0xd1, 0x74, 0xd1, //0x000008ef vpcmpeqb     %xmm1, %xmm5, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x000008f3 vpmovmskb    %xmm2, %edi
	0xc5, 0xd9, 0x74, 0xd1, //0x000008f7 vpcmpeqb     %xmm1, %xmm4, %xmm2
	0x48, 0xc1, 0xe3, 0x10, //0x000008fb shlq         $16, %rbx
	0x48, 0x09, 0xde, //0x000008ff orq          %rbx, %rsi
	0xc5, 0xf9, 0xd7, 0xda, //0x00000902 vpmovmskb    %xmm2, %ebx
	0xc5, 0xc1, 0x74, 0xd1, //0x00000906 vpcmpeqb     %xmm1, %xmm7, %xmm2
	0x48, 0xc1, 0xe1, 0x20, //0x0000090a shlq         $32, %rcx
	0x48, 0x09, 0xce, //0x0000090e orq          %rcx, %rsi
	0xc5, 0xf9, 0xd7, 0xca, //0x00000911 vpmovmskb    %xmm2, %ecx
	0xc5, 0x81, 0x64, 0xd6, //0x00000915 vpcmpgtb     %xmm6, %xmm15, %xmm2
	0xc5, 0xc9, 0x64, 0xf3, //0x00000919 vpcmpgtb     %xmm3, %xmm6, %xmm6
	0xc5, 0xc9, 0xdb, 0xd2, //0x0000091d vpand        %xmm2, %xmm6, %xmm2
	0x48, 0xc1, 0xe7, 0x10, //0x00000921 shlq         $16, %rdi
	0x49, 0x09, 0xfd, //0x00000925 orq          %rdi, %r13
	0xc5, 0x79, 0xd7, 0xe2, //0x00000928 vpmovmskb    %xmm2, %r12d
	0xc5, 0x81, 0x64, 0xd5, //0x0000092c vpcmpgtb     %xmm5, %xmm15, %xmm2
	0xc5, 0xd1, 0x64, 0xeb, //0x00000930 vpcmpgtb     %xmm3, %xmm5, %xmm5
	0xc5, 0xd1, 0xdb, 0xd2, //0x00000934 vpand        %xmm2, %xmm5, %xmm2
	0x48, 0xc1, 0xe3, 0x20, //0x00000938 shlq         $32, %rbx
	0x49, 0x09, 0xdd, //0x0000093c orq          %rbx, %r13
	0xc5, 0xf9, 0xd7, 0xfa, //0x0000093f vpmovmskb    %xmm2, %edi
	0xc5, 0x81, 0x64, 0xd4, //0x00000943 vpcmpgtb     %xmm4, %xmm15, %xmm2
	0xc5, 0xd9, 0x64, 0xe3, //0x00000947 vpcmpgtb     %xmm3, %xmm4, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x0000094b vpand        %xmm2, %xmm4, %xmm2
	0x48, 0xc1, 0xe1, 0x30, //0x0000094f shlq         $48, %rcx
	0x49, 0x09, 0xcd, //0x00000953 orq          %rcx, %r13
	0xc5, 0xf9, 0xd7, 0xca, //0x00000956 vpmovmskb    %xmm2, %ecx
	0xc5, 0x81, 0x64, 0xd7, //0x0000095a vpcmpgtb     %xmm7, %xmm15, %xmm2
	0xc5, 0xc1, 0x64, 0xe3, //0x0000095e vpcmpgtb     %xmm3, %xmm7, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x00000962 vpand        %xmm2, %xmm4, %xmm2
	0x48, 0xc1, 0xe7, 0x10, //0x00000966 shlq         $16, %rdi
	0x49, 0x09, 0xfc, //0x0000096a orq          %rdi, %r12
	0xc5, 0x79, 0xd7, 0xf2, //0x0000096d vpmovmskb    %xmm2, %r14d
	0x48, 0xc1, 0xe2, 0x30, //0x00000971 shlq         $48, %rdx
	0x48, 0xc1, 0xe1, 0x20, //0x00000975 shlq         $32, %rcx
	0x49, 0x83, 0xf9, 0xff, //0x00000979 cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x0000097d jne          LBB0_109
	0x4d, 0x85, 0xed, //0x00000983 testq        %r13, %r13
	0x0f, 0x85, 0x9c, 0x00, 0x00, 0x00, //0x00000986 jne          LBB0_124
	//0x0000098c LBB0_109
	0x49, 0xc1, 0xe6, 0x30, //0x0000098c shlq         $48, %r14
	0x49, 0x09, 0xcc, //0x00000990 orq          %rcx, %r12
	0x48, 0x09, 0xd6, //0x00000993 orq          %rdx, %rsi
	0x4c, 0x89, 0xe9, //0x00000996 movq         %r13, %rcx
	0x4c, 0x09, 0xc1, //0x00000999 orq          %r8, %rcx
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x0000099c jne          LBB0_145
	0x4d, 0x09, 0xf4, //0x000009a2 orq          %r14, %r12
	0x48, 0x85, 0xf6, //0x000009a5 testq        %rsi, %rsi
	0x0f, 0x85, 0x39, 0x02, 0x00, 0x00, //0x000009a8 jne          LBB0_146
	//0x000009ae LBB0_111
	0x4d, 0x85, 0xe4, //0x000009ae testq        %r12, %r12
	0x0f, 0x85, 0x0c, 0x1a, 0x00, 0x00, //0x000009b1 jne          LBB0_426
	0x48, 0x83, 0xc0, 0xc0, //0x000009b7 addq         $-64, %rax
	0x49, 0x83, 0xc3, 0x40, //0x000009bb addq         $64, %r11
	0x48, 0x83, 0xf8, 0x3f, //0x000009bf cmpq         $63, %rax
	0x0f, 0x87, 0xe7, 0xfe, 0xff, 0xff, //0x000009c3 ja           LBB0_107
	0xe9, 0xda, 0x0d, 0x00, 0x00, //0x000009c9 jmp          LBB0_113
	//0x000009ce LBB0_145
	0x4c, 0x89, 0xc1, //0x000009ce movq         %r8, %rcx
	0x48, 0xf7, 0xd1, //0x000009d1 notq         %rcx
	0x4c, 0x21, 0xe9, //0x000009d4 andq         %r13, %rcx
	0x48, 0x8d, 0x14, 0x09, //0x000009d7 leaq         (%rcx,%rcx), %rdx
	0x4c, 0x09, 0xc2, //0x000009db orq          %r8, %rdx
	0x48, 0x89, 0xd7, //0x000009de movq         %rdx, %rdi
	0x48, 0xf7, 0xd7, //0x000009e1 notq         %rdi
	0x4c, 0x21, 0xef, //0x000009e4 andq         %r13, %rdi
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000009e7 movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xdf, //0x000009f1 andq         %rbx, %rdi
	0x45, 0x31, 0xc0, //0x000009f4 xorl         %r8d, %r8d
	0x48, 0x01, 0xcf, //0x000009f7 addq         %rcx, %rdi
	0x41, 0x0f, 0x92, 0xc0, //0x000009fa setb         %r8b
	0x48, 0x01, 0xff, //0x000009fe addq         %rdi, %rdi
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000a01 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xcf, //0x00000a0b xorq         %rcx, %rdi
	0x48, 0x21, 0xd7, //0x00000a0e andq         %rdx, %rdi
	0x48, 0xf7, 0xd7, //0x00000a11 notq         %rdi
	0x48, 0x21, 0xfe, //0x00000a14 andq         %rdi, %rsi
	0x4d, 0x09, 0xf4, //0x00000a17 orq          %r14, %r12
	0x48, 0x85, 0xf6, //0x00000a1a testq        %rsi, %rsi
	0x0f, 0x84, 0x8b, 0xff, 0xff, 0xff, //0x00000a1d je           LBB0_111
	0xe9, 0xbf, 0x01, 0x00, 0x00, //0x00000a23 jmp          LBB0_146
	//0x00000a28 LBB0_124
	0x4c, 0x89, 0xdf, //0x00000a28 movq         %r11, %rdi
	0x48, 0x2b, 0x7d, 0xd0, //0x00000a2b subq         $-48(%rbp), %rdi
	0x4d, 0x0f, 0xbc, 0xcd, //0x00000a2f bsfq         %r13, %r9
	0x49, 0x01, 0xf9, //0x00000a33 addq         %rdi, %r9
	0xe9, 0x51, 0xff, 0xff, 0xff, //0x00000a36 jmp          LBB0_109
	//0x00000a3b LBB0_125
	0x4b, 0xc7, 0x04, 0xd1, 0x02, 0x00, 0x00, 0x00, //0x00000a3b movq         $2, (%r9,%r10,8)
	0x48, 0x8b, 0x45, 0x90, //0x00000a43 movq         $-112(%rbp), %rax
	0x48, 0x8b, 0x50, 0x08, //0x00000a47 movq         $8(%rax), %rdx
	0xf6, 0x45, 0x80, 0x20, //0x00000a4b testb        $32, $-128(%rbp)
	0x4c, 0x89, 0x5d, 0xa8, //0x00000a4f movq         %r11, $-88(%rbp)
	0x48, 0x89, 0x55, 0xb0, //0x00000a53 movq         %rdx, $-80(%rbp)
	0x0f, 0x85, 0x71, 0x02, 0x00, 0x00, //0x00000a57 jne          LBB0_161
	0x49, 0x89, 0xd5, //0x00000a5d movq         %rdx, %r13
	0x4d, 0x29, 0xdd, //0x00000a60 subq         %r11, %r13
	0x0f, 0x84, 0xf3, 0x1a, 0x00, 0x00, //0x00000a63 je           LBB0_454
	0x4c, 0x89, 0xd8, //0x00000a69 movq         %r11, %rax
	0x4d, 0x01, 0xe3, //0x00000a6c addq         %r12, %r11
	0x49, 0x83, 0xfd, 0x40, //0x00000a6f cmpq         $64, %r13
	0x0f, 0x82, 0xfc, 0x13, 0x00, 0x00, //0x00000a73 jb           LBB0_357
	0x45, 0x89, 0xea, //0x00000a79 movl         %r13d, %r10d
	0x41, 0x83, 0xe2, 0x3f, //0x00000a7c andl         $63, %r10d
	0x4a, 0x8d, 0x4c, 0x02, 0xc0, //0x00000a80 leaq         $-64(%rdx,%r8), %rcx
	0x48, 0x83, 0xe1, 0xc0, //0x00000a85 andq         $-64, %rcx
	0x48, 0x01, 0xc1, //0x00000a89 addq         %rax, %rcx
	0x48, 0x03, 0x4d, 0x88, //0x00000a8c addq         $-120(%rbp), %rcx
	0x48, 0x89, 0x4d, 0xb8, //0x00000a90 movq         %rcx, $-72(%rbp)
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00000a94 movq         $-1, %r12
	0x45, 0x31, 0xc9, //0x00000a9b xorl         %r9d, %r9d
	0x90, 0x90, //0x00000a9e .p2align 4, 0x90
	//0x00000aa0 LBB0_129
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x00000aa0 vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x00000aa5 vmovdqu      $16(%r11), %xmm4
	0xc4, 0xc1, 0x7a, 0x6f, 0x6b, 0x20, //0x00000aab vmovdqu      $32(%r11), %xmm5
	0xc4, 0xc1, 0x7a, 0x6f, 0x73, 0x30, //0x00000ab1 vmovdqu      $48(%r11), %xmm6
	0xc5, 0xe9, 0x74, 0xf8, //0x00000ab7 vpcmpeqb     %xmm0, %xmm2, %xmm7
	0xc5, 0xf9, 0xd7, 0xff, //0x00000abb vpmovmskb    %xmm7, %edi
	0xc5, 0xd9, 0x74, 0xf8, //0x00000abf vpcmpeqb     %xmm0, %xmm4, %xmm7
	0xc5, 0xf9, 0xd7, 0xcf, //0x00000ac3 vpmovmskb    %xmm7, %ecx
	0xc5, 0xd1, 0x74, 0xf8, //0x00000ac7 vpcmpeqb     %xmm0, %xmm5, %xmm7
	0xc5, 0xf9, 0xd7, 0xf7, //0x00000acb vpmovmskb    %xmm7, %esi
	0xc5, 0xc9, 0x74, 0xf8, //0x00000acf vpcmpeqb     %xmm0, %xmm6, %xmm7
	0xc5, 0xf9, 0xd7, 0xdf, //0x00000ad3 vpmovmskb    %xmm7, %ebx
	0xc5, 0xe9, 0x74, 0xd1, //0x00000ad7 vpcmpeqb     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xd2, //0x00000adb vpmovmskb    %xmm2, %edx
	0xc5, 0xd9, 0x74, 0xd1, //0x00000adf vpcmpeqb     %xmm1, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xc2, //0x00000ae3 vpmovmskb    %xmm2, %eax
	0xc5, 0xd1, 0x74, 0xd1, //0x00000ae7 vpcmpeqb     %xmm1, %xmm5, %xmm2
	0xc5, 0x79, 0xd7, 0xf2, //0x00000aeb vpmovmskb    %xmm2, %r14d
	0xc5, 0xc9, 0x74, 0xd1, //0x00000aef vpcmpeqb     %xmm1, %xmm6, %xmm2
	0xc5, 0x79, 0xd7, 0xc2, //0x00000af3 vpmovmskb    %xmm2, %r8d
	0x48, 0xc1, 0xe3, 0x30, //0x00000af7 shlq         $48, %rbx
	0x48, 0xc1, 0xe6, 0x20, //0x00000afb shlq         $32, %rsi
	0x48, 0xc1, 0xe1, 0x10, //0x00000aff shlq         $16, %rcx
	0x48, 0x09, 0xcf, //0x00000b03 orq          %rcx, %rdi
	0x48, 0x09, 0xf7, //0x00000b06 orq          %rsi, %rdi
	0x49, 0xc1, 0xe0, 0x30, //0x00000b09 shlq         $48, %r8
	0x49, 0xc1, 0xe6, 0x20, //0x00000b0d shlq         $32, %r14
	0x48, 0xc1, 0xe0, 0x10, //0x00000b11 shlq         $16, %rax
	0x48, 0x09, 0xc2, //0x00000b15 orq          %rax, %rdx
	0x4c, 0x09, 0xf2, //0x00000b18 orq          %r14, %rdx
	0x4c, 0x09, 0xc2, //0x00000b1b orq          %r8, %rdx
	0x49, 0x83, 0xfc, 0xff, //0x00000b1e cmpq         $-1, %r12
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00000b22 jne          LBB0_131
	0x48, 0x85, 0xd2, //0x00000b28 testq        %rdx, %rdx
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x00000b2b jne          LBB0_140
	//0x00000b31 LBB0_131
	0x48, 0x09, 0xdf, //0x00000b31 orq          %rbx, %rdi
	0x48, 0x89, 0xd0, //0x00000b34 movq         %rdx, %rax
	0x4c, 0x09, 0xc8, //0x00000b37 orq          %r9, %rax
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x00000b3a jne          LBB0_141
	//0x00000b40 LBB0_132
	0x48, 0x85, 0xff, //0x00000b40 testq        %rdi, %rdi
	0x0f, 0x85, 0x86, 0x00, 0x00, 0x00, //0x00000b43 jne          LBB0_142
	//0x00000b49 LBB0_133
	0x49, 0x83, 0xc5, 0xc0, //0x00000b49 addq         $-64, %r13
	0x49, 0x83, 0xc3, 0x40, //0x00000b4d addq         $64, %r11
	0x49, 0x83, 0xfd, 0x3f, //0x00000b51 cmpq         $63, %r13
	0x0f, 0x87, 0x45, 0xff, 0xff, 0xff, //0x00000b55 ja           LBB0_129
	0xe9, 0x64, 0x0d, 0x00, 0x00, //0x00000b5b jmp          LBB0_134
	//0x00000b60 LBB0_140
	0x4c, 0x89, 0xd8, //0x00000b60 movq         %r11, %rax
	0x48, 0x2b, 0x45, 0xd0, //0x00000b63 subq         $-48(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xe2, //0x00000b67 bsfq         %rdx, %r12
	0x49, 0x01, 0xc4, //0x00000b6b addq         %rax, %r12
	0x48, 0x09, 0xdf, //0x00000b6e orq          %rbx, %rdi
	0x48, 0x89, 0xd0, //0x00000b71 movq         %rdx, %rax
	0x4c, 0x09, 0xc8, //0x00000b74 orq          %r9, %rax
	0x0f, 0x84, 0xc3, 0xff, 0xff, 0xff, //0x00000b77 je           LBB0_132
	//0x00000b7d LBB0_141
	0x4c, 0x89, 0xc8, //0x00000b7d movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x00000b80 notq         %rax
	0x48, 0x21, 0xd0, //0x00000b83 andq         %rdx, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x00000b86 leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xc9, //0x00000b8a orq          %r9, %rcx
	0x48, 0x89, 0xce, //0x00000b8d movq         %rcx, %rsi
	0x48, 0xf7, 0xd6, //0x00000b90 notq         %rsi
	0x48, 0x21, 0xd6, //0x00000b93 andq         %rdx, %rsi
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000b96 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd6, //0x00000ba0 andq         %rdx, %rsi
	0x45, 0x31, 0xc9, //0x00000ba3 xorl         %r9d, %r9d
	0x48, 0x01, 0xc6, //0x00000ba6 addq         %rax, %rsi
	0x41, 0x0f, 0x92, 0xc1, //0x00000ba9 setb         %r9b
	0x48, 0x01, 0xf6, //0x00000bad addq         %rsi, %rsi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000bb0 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc6, //0x00000bba xorq         %rax, %rsi
	0x48, 0x21, 0xce, //0x00000bbd andq         %rcx, %rsi
	0x48, 0xf7, 0xd6, //0x00000bc0 notq         %rsi
	0x48, 0x21, 0xf7, //0x00000bc3 andq         %rsi, %rdi
	0x48, 0x85, 0xff, //0x00000bc6 testq        %rdi, %rdi
	0x0f, 0x84, 0x7a, 0xff, 0xff, 0xff, //0x00000bc9 je           LBB0_133
	//0x00000bcf LBB0_142
	0x48, 0x0f, 0xbc, 0xc7, //0x00000bcf bsfq         %rdi, %rax
	//0x00000bd3 LBB0_143
	0x4c, 0x03, 0x5d, 0x98, //0x00000bd3 addq         $-104(%rbp), %r11
	0x49, 0x01, 0xc3, //0x00000bd7 addq         %rax, %r11
	//0x00000bda LBB0_144
	0x48, 0x8b, 0x75, 0xc8, //0x00000bda movq         $-56(%rbp), %rsi
	0x4c, 0x8b, 0x4d, 0xc0, //0x00000bde movq         $-64(%rbp), %r9
	0xe9, 0x59, 0x03, 0x00, 0x00, //0x00000be2 jmp          LBB0_195
	//0x00000be7 LBB0_146
	0x48, 0x0f, 0xbc, 0xc6, //0x00000be7 bsfq         %rsi, %rax
	0x4d, 0x85, 0xe4, //0x00000beb testq        %r12, %r12
	0x0f, 0x84, 0xbf, 0x02, 0x00, 0x00, //0x00000bee je           LBB0_185
	0x49, 0x0f, 0xbc, 0xcc, //0x00000bf4 bsfq         %r12, %rcx
	0x4c, 0x8b, 0x65, 0xd0, //0x00000bf8 movq         $-48(%rbp), %r12
	0x4d, 0x29, 0xe3, //0x00000bfc subq         %r12, %r11
	0x48, 0x39, 0xc1, //0x00000bff cmpq         %rax, %rcx
	0x0f, 0x83, 0xc0, 0x02, 0x00, 0x00, //0x00000c02 jae          LBB0_186
	0xe9, 0x3d, 0x19, 0x00, 0x00, //0x00000c08 jmp          LBB0_306
	//0x00000c0d LBB0_148
	0x49, 0x01, 0xce, //0x00000c0d addq         %rcx, %r14
	0x4c, 0x89, 0x75, 0xa8, //0x00000c10 movq         %r14, $-88(%rbp)
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000c14 movq         $-1, %rax
	0x4d, 0x85, 0xed, //0x00000c1b testq        %r13, %r13
	0x48, 0x8b, 0x75, 0xc8, //0x00000c1e movq         $-56(%rbp), %rsi
	0x0f, 0x85, 0x1d, 0x00, 0x00, 0x00, //0x00000c22 jne          LBB0_151
	0xe9, 0x55, 0x17, 0x00, 0x00, //0x00000c28 jmp          LBB0_420
	//0x00000c2d LBB0_149
	0x48, 0x89, 0x7d, 0xa8, //0x00000c2d movq         %rdi, $-88(%rbp)
	//0x00000c31 LBB0_150
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000c31 movq         $-1, %rax
	0x4d, 0x85, 0xed, //0x00000c38 testq        %r13, %r13
	0x48, 0x8b, 0x75, 0xc8, //0x00000c3b movq         $-56(%rbp), %rsi
	0x0f, 0x84, 0x3d, 0x17, 0x00, 0x00, //0x00000c3f je           LBB0_420
	//0x00000c45 LBB0_151
	0x4d, 0x85, 0xe4, //0x00000c45 testq        %r12, %r12
	0x0f, 0x84, 0x34, 0x17, 0x00, 0x00, //0x00000c48 je           LBB0_420
	0x4d, 0x85, 0xdb, //0x00000c4e testq        %r11, %r11
	0x0f, 0x84, 0x2b, 0x17, 0x00, 0x00, //0x00000c51 je           LBB0_420
	0x48, 0x8b, 0x45, 0xa8, //0x00000c57 movq         $-88(%rbp), %rax
	0x4c, 0x29, 0xd0, //0x00000c5b subq         %r10, %rax
	0x48, 0x89, 0xc1, //0x00000c5e movq         %rax, %rcx
	0x48, 0xff, 0xc8, //0x00000c61 decq         %rax
	0x49, 0x39, 0xc5, //0x00000c64 cmpq         %rax, %r13
	0x0f, 0x84, 0x45, 0x00, 0x00, 0x00, //0x00000c67 je           LBB0_159
	0x49, 0x39, 0xc3, //0x00000c6d cmpq         %rax, %r11
	0x0f, 0x84, 0x3c, 0x00, 0x00, 0x00, //0x00000c70 je           LBB0_159
	0x49, 0x39, 0xc4, //0x00000c76 cmpq         %rax, %r12
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x00000c79 je           LBB0_159
	0x4d, 0x85, 0xe4, //0x00000c7f testq        %r12, %r12
	0x4c, 0x8b, 0x4d, 0xc0, //0x00000c82 movq         $-64(%rbp), %r9
	0x0f, 0x8e, 0x68, 0x02, 0x00, 0x00, //0x00000c86 jle          LBB0_189
	0x49, 0x8d, 0x44, 0x24, 0xff, //0x00000c8c leaq         $-1(%r12), %rax
	0x49, 0x39, 0xc5, //0x00000c91 cmpq         %rax, %r13
	0x0f, 0x84, 0x5a, 0x02, 0x00, 0x00, //0x00000c94 je           LBB0_189
	0x49, 0xf7, 0xd4, //0x00000c9a notq         %r12
	0x4d, 0x89, 0xe3, //0x00000c9d movq         %r12, %r11
	0x4c, 0x8b, 0x65, 0xd0, //0x00000ca0 movq         $-48(%rbp), %r12
	0x4d, 0x85, 0xdb, //0x00000ca4 testq        %r11, %r11
	0x0f, 0x89, 0xd4, 0xf4, 0xff, 0xff, //0x00000ca7 jns          LBB0_258
	0xe9, 0xcd, 0x16, 0x00, 0x00, //0x00000cad jmp          LBB0_419
	//0x00000cb2 LBB0_159
	0x49, 0x89, 0xcb, //0x00000cb2 movq         %rcx, %r11
	0x49, 0xf7, 0xdb, //0x00000cb5 negq         %r11
	//0x00000cb8 LBB0_160
	0x4c, 0x8b, 0x65, 0xd0, //0x00000cb8 movq         $-48(%rbp), %r12
	0x4c, 0x8b, 0x4d, 0xc0, //0x00000cbc movq         $-64(%rbp), %r9
	0x4d, 0x85, 0xdb, //0x00000cc0 testq        %r11, %r11
	0x0f, 0x89, 0xb8, 0xf4, 0xff, 0xff, //0x00000cc3 jns          LBB0_258
	0xe9, 0xb1, 0x16, 0x00, 0x00, //0x00000cc9 jmp          LBB0_419
	//0x00000cce LBB0_161
	0x48, 0x89, 0xd0, //0x00000cce movq         %rdx, %rax
	0x4c, 0x29, 0xd8, //0x00000cd1 subq         %r11, %rax
	0x0f, 0x84, 0x82, 0x18, 0x00, 0x00, //0x00000cd4 je           LBB0_454
	0x4c, 0x89, 0xd9, //0x00000cda movq         %r11, %rcx
	0x4d, 0x01, 0xe3, //0x00000cdd addq         %r12, %r11
	0x48, 0x83, 0xf8, 0x40, //0x00000ce0 cmpq         $64, %rax
	0x0f, 0x82, 0xa4, 0x11, 0x00, 0x00, //0x00000ce4 jb           LBB0_358
	0x41, 0x89, 0xc2, //0x00000cea movl         %eax, %r10d
	0x41, 0x83, 0xe2, 0x3f, //0x00000ced andl         $63, %r10d
	0x4e, 0x8d, 0x44, 0x02, 0xc0, //0x00000cf1 leaq         $-64(%rdx,%r8), %r8
	0x49, 0x83, 0xe0, 0xc0, //0x00000cf6 andq         $-64, %r8
	0x49, 0x01, 0xc8, //0x00000cfa addq         %rcx, %r8
	0x4c, 0x03, 0x45, 0x88, //0x00000cfd addq         $-120(%rbp), %r8
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00000d01 movq         $-1, %r12
	0x45, 0x31, 0xc9, //0x00000d08 xorl         %r9d, %r9d
	0x90, 0x90, 0x90, 0x90, 0x90, //0x00000d0b .p2align 4, 0x90
	//0x00000d10 LBB0_164
	0xc4, 0xc1, 0x7a, 0x6f, 0x33, //0x00000d10 vmovdqu      (%r11), %xmm6
	0xc4, 0xc1, 0x7a, 0x6f, 0x6b, 0x10, //0x00000d15 vmovdqu      $16(%r11), %xmm5
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x20, //0x00000d1b vmovdqu      $32(%r11), %xmm4
	0xc4, 0xc1, 0x7a, 0x6f, 0x7b, 0x30, //0x00000d21 vmovdqu      $48(%r11), %xmm7
	0xc5, 0xc9, 0x74, 0xd0, //0x00000d27 vpcmpeqb     %xmm0, %xmm6, %xmm2
	0xc5, 0xf9, 0xd7, 0xf2, //0x00000d2b vpmovmskb    %xmm2, %esi
	0xc5, 0xd1, 0x74, 0xd0, //0x00000d2f vpcmpeqb     %xmm0, %xmm5, %xmm2
	0xc5, 0xf9, 0xd7, 0xda, //0x00000d33 vpmovmskb    %xmm2, %ebx
	0xc5, 0xd9, 0x74, 0xd0, //0x00000d37 vpcmpeqb     %xmm0, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xca, //0x00000d3b vpmovmskb    %xmm2, %ecx
	0xc5, 0xc1, 0x74, 0xd0, //0x00000d3f vpcmpeqb     %xmm0, %xmm7, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x00000d43 vpmovmskb    %xmm2, %edi
	0xc5, 0xc9, 0x74, 0xd1, //0x00000d47 vpcmpeqb     %xmm1, %xmm6, %xmm2
	0xc5, 0x79, 0xd7, 0xea, //0x00000d4b vpmovmskb    %xmm2, %r13d
	0xc5, 0xd1, 0x74, 0xd1, //0x00000d4f vpcmpeqb     %xmm1, %xmm5, %xmm2
	0xc5, 0xf9, 0xd7, 0xd2, //0x00000d53 vpmovmskb    %xmm2, %edx
	0xc5, 0xd9, 0x74, 0xd1, //0x00000d57 vpcmpeqb     %xmm1, %xmm4, %xmm2
	0x48, 0xc1, 0xe3, 0x10, //0x00000d5b shlq         $16, %rbx
	0x48, 0x09, 0xde, //0x00000d5f orq          %rbx, %rsi
	0xc5, 0xf9, 0xd7, 0xda, //0x00000d62 vpmovmskb    %xmm2, %ebx
	0xc5, 0xc1, 0x74, 0xd1, //0x00000d66 vpcmpeqb     %xmm1, %xmm7, %xmm2
	0x48, 0xc1, 0xe1, 0x20, //0x00000d6a shlq         $32, %rcx
	0x48, 0x09, 0xce, //0x00000d6e orq          %rcx, %rsi
	0xc5, 0xf9, 0xd7, 0xca, //0x00000d71 vpmovmskb    %xmm2, %ecx
	0xc5, 0x81, 0x64, 0xd6, //0x00000d75 vpcmpgtb     %xmm6, %xmm15, %xmm2
	0xc5, 0xc9, 0x64, 0xf3, //0x00000d79 vpcmpgtb     %xmm3, %xmm6, %xmm6
	0xc5, 0xc9, 0xdb, 0xd2, //0x00000d7d vpand        %xmm2, %xmm6, %xmm2
	0x48, 0xc1, 0xe2, 0x10, //0x00000d81 shlq         $16, %rdx
	0x49, 0x09, 0xd5, //0x00000d85 orq          %rdx, %r13
	0xc5, 0xf9, 0xd7, 0xd2, //0x00000d88 vpmovmskb    %xmm2, %edx
	0xc5, 0x81, 0x64, 0xd5, //0x00000d8c vpcmpgtb     %xmm5, %xmm15, %xmm2
	0xc5, 0xd1, 0x64, 0xeb, //0x00000d90 vpcmpgtb     %xmm3, %xmm5, %xmm5
	0xc5, 0xd1, 0xdb, 0xd2, //0x00000d94 vpand        %xmm2, %xmm5, %xmm2
	0x48, 0xc1, 0xe3, 0x20, //0x00000d98 shlq         $32, %rbx
	0x49, 0x09, 0xdd, //0x00000d9c orq          %rbx, %r13
	0xc5, 0xf9, 0xd7, 0xda, //0x00000d9f vpmovmskb    %xmm2, %ebx
	0xc5, 0x81, 0x64, 0xd4, //0x00000da3 vpcmpgtb     %xmm4, %xmm15, %xmm2
	0xc5, 0xd9, 0x64, 0xe3, //0x00000da7 vpcmpgtb     %xmm3, %xmm4, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x00000dab vpand        %xmm2, %xmm4, %xmm2
	0x48, 0xc1, 0xe1, 0x30, //0x00000daf shlq         $48, %rcx
	0x49, 0x09, 0xcd, //0x00000db3 orq          %rcx, %r13
	0xc5, 0xf9, 0xd7, 0xca, //0x00000db6 vpmovmskb    %xmm2, %ecx
	0xc5, 0x81, 0x64, 0xd7, //0x00000dba vpcmpgtb     %xmm7, %xmm15, %xmm2
	0xc5, 0xc1, 0x64, 0xe3, //0x00000dbe vpcmpgtb     %xmm3, %xmm7, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x00000dc2 vpand        %xmm2, %xmm4, %xmm2
	0x48, 0xc1, 0xe3, 0x10, //0x00000dc6 shlq         $16, %rbx
	0x48, 0x09, 0xda, //0x00000dca orq          %rbx, %rdx
	0xc5, 0x79, 0xd7, 0xf2, //0x00000dcd vpmovmskb    %xmm2, %r14d
	0x48, 0xc1, 0xe7, 0x30, //0x00000dd1 shlq         $48, %rdi
	0x48, 0xc1, 0xe1, 0x20, //0x00000dd5 shlq         $32, %rcx
	0x49, 0x83, 0xfc, 0xff, //0x00000dd9 cmpq         $-1, %r12
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00000ddd jne          LBB0_166
	0x4d, 0x85, 0xed, //0x00000de3 testq        %r13, %r13
	0x0f, 0x85, 0x9e, 0x00, 0x00, 0x00, //0x00000de6 jne          LBB0_181
	//0x00000dec LBB0_166
	0x49, 0xc1, 0xe6, 0x30, //0x00000dec shlq         $48, %r14
	0x48, 0x09, 0xca, //0x00000df0 orq          %rcx, %rdx
	0x48, 0x09, 0xfe, //0x00000df3 orq          %rdi, %rsi
	0x4c, 0x89, 0xe9, //0x00000df6 movq         %r13, %rcx
	0x4c, 0x09, 0xc9, //0x00000df9 orq          %r9, %rcx
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x00000dfc jne          LBB0_182
	0x4c, 0x09, 0xf2, //0x00000e02 orq          %r14, %rdx
	0x48, 0x85, 0xf6, //0x00000e05 testq        %rsi, %rsi
	0x0f, 0x85, 0x8f, 0x00, 0x00, 0x00, //0x00000e08 jne          LBB0_183
	//0x00000e0e LBB0_168
	0x48, 0x85, 0xd2, //0x00000e0e testq        %rdx, %rdx
	0x0f, 0x85, 0xda, 0x15, 0x00, 0x00, //0x00000e11 jne          LBB0_432
	0x48, 0x83, 0xc0, 0xc0, //0x00000e17 addq         $-64, %rax
	0x49, 0x83, 0xc3, 0x40, //0x00000e1b addq         $64, %r11
	0x48, 0x83, 0xf8, 0x3f, //0x00000e1f cmpq         $63, %rax
	0x0f, 0x87, 0xe7, 0xfe, 0xff, 0xff, //0x00000e23 ja           LBB0_164
	0xe9, 0x11, 0x0b, 0x00, 0x00, //0x00000e29 jmp          LBB0_170
	//0x00000e2e LBB0_182
	0x4c, 0x89, 0xc9, //0x00000e2e movq         %r9, %rcx
	0x48, 0xf7, 0xd1, //0x00000e31 notq         %rcx
	0x4c, 0x21, 0xe9, //0x00000e34 andq         %r13, %rcx
	0x48, 0x8d, 0x1c, 0x09, //0x00000e37 leaq         (%rcx,%rcx), %rbx
	0x4c, 0x09, 0xcb, //0x00000e3b orq          %r9, %rbx
	0x48, 0x89, 0x5d, 0xb8, //0x00000e3e movq         %rbx, $-72(%rbp)
	0x48, 0xf7, 0xd3, //0x00000e42 notq         %rbx
	0x4c, 0x21, 0xeb, //0x00000e45 andq         %r13, %rbx
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000e48 movabsq      $-6148914691236517206, %rdi
	0x48, 0x21, 0xfb, //0x00000e52 andq         %rdi, %rbx
	0x45, 0x31, 0xc9, //0x00000e55 xorl         %r9d, %r9d
	0x48, 0x01, 0xcb, //0x00000e58 addq         %rcx, %rbx
	0x41, 0x0f, 0x92, 0xc1, //0x00000e5b setb         %r9b
	0x48, 0x01, 0xdb, //0x00000e5f addq         %rbx, %rbx
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000e62 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xcb, //0x00000e6c xorq         %rcx, %rbx
	0x48, 0x23, 0x5d, 0xb8, //0x00000e6f andq         $-72(%rbp), %rbx
	0x48, 0xf7, 0xd3, //0x00000e73 notq         %rbx
	0x48, 0x21, 0xde, //0x00000e76 andq         %rbx, %rsi
	0x4c, 0x09, 0xf2, //0x00000e79 orq          %r14, %rdx
	0x48, 0x85, 0xf6, //0x00000e7c testq        %rsi, %rsi
	0x0f, 0x84, 0x89, 0xff, 0xff, 0xff, //0x00000e7f je           LBB0_168
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x00000e85 jmp          LBB0_183
	//0x00000e8a LBB0_181
	0x4c, 0x89, 0xdb, //0x00000e8a movq         %r11, %rbx
	0x48, 0x2b, 0x5d, 0xd0, //0x00000e8d subq         $-48(%rbp), %rbx
	0x4d, 0x0f, 0xbc, 0xe5, //0x00000e91 bsfq         %r13, %r12
	0x49, 0x01, 0xdc, //0x00000e95 addq         %rbx, %r12
	0xe9, 0x4f, 0xff, 0xff, 0xff, //0x00000e98 jmp          LBB0_166
	//0x00000e9d LBB0_183
	0x48, 0x0f, 0xbc, 0xc6, //0x00000e9d bsfq         %rsi, %rax
	0x48, 0x85, 0xd2, //0x00000ea1 testq        %rdx, %rdx
	0x0f, 0x84, 0x77, 0x00, 0x00, 0x00, //0x00000ea4 je           LBB0_192
	0x48, 0x0f, 0xbc, 0xca, //0x00000eaa bsfq         %rdx, %rcx
	0xe9, 0x73, 0x00, 0x00, 0x00, //0x00000eae jmp          LBB0_193
	//0x00000eb3 LBB0_185
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00000eb3 movl         $64, %ecx
	0x4c, 0x8b, 0x65, 0xd0, //0x00000eb8 movq         $-48(%rbp), %r12
	0x4d, 0x29, 0xe3, //0x00000ebc subq         %r12, %r11
	0x48, 0x39, 0xc1, //0x00000ebf cmpq         %rax, %rcx
	0x0f, 0x82, 0x82, 0x16, 0x00, 0x00, //0x00000ec2 jb           LBB0_306
	//0x00000ec8 LBB0_186
	0x4d, 0x8d, 0x5c, 0x03, 0x01, //0x00000ec8 leaq         $1(%r11,%rax), %r11
	//0x00000ecd LBB0_187
	0x4d, 0x85, 0xdb, //0x00000ecd testq        %r11, %r11
	0x0f, 0x88, 0x83, 0x14, 0x00, 0x00, //0x00000ed0 js           LBB0_415
	0x48, 0x8b, 0x75, 0xc8, //0x00000ed6 movq         $-56(%rbp), %rsi
	0x4c, 0x89, 0x1e, //0x00000eda movq         %r11, (%rsi)
	0x4c, 0x89, 0xf8, //0x00000edd movq         %r15, %rax
	0x48, 0x83, 0x7d, 0xa8, 0x00, //0x00000ee0 cmpq         $0, $-88(%rbp)
	0x4c, 0x8b, 0x4d, 0xc0, //0x00000ee5 movq         $-64(%rbp), %r9
	0x0f, 0x8f, 0xb1, 0xf2, 0xff, 0xff, //0x00000ee9 jg           LBB0_2
	0xe9, 0x9e, 0x14, 0x00, 0x00, //0x00000eef jmp          LBB0_423
	//0x00000ef4 LBB0_189
	0x4c, 0x89, 0xd8, //0x00000ef4 movq         %r11, %rax
	0x4c, 0x09, 0xe8, //0x00000ef7 orq          %r13, %rax
	0x4d, 0x39, 0xeb, //0x00000efa cmpq         %r13, %r11
	0x0f, 0x8c, 0x59, 0xf2, 0xff, 0xff, //0x00000efd jl           LBB0_257
	0x48, 0x85, 0xc0, //0x00000f03 testq        %rax, %rax
	0x0f, 0x88, 0x50, 0xf2, 0xff, 0xff, //0x00000f06 js           LBB0_257
	0x49, 0xf7, 0xd3, //0x00000f0c notq         %r11
	0x4c, 0x8b, 0x65, 0xd0, //0x00000f0f movq         $-48(%rbp), %r12
	0x4d, 0x85, 0xdb, //0x00000f13 testq        %r11, %r11
	0x0f, 0x89, 0x65, 0xf2, 0xff, 0xff, //0x00000f16 jns          LBB0_258
	0xe9, 0x5e, 0x14, 0x00, 0x00, //0x00000f1c jmp          LBB0_419
	//0x00000f21 LBB0_192
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00000f21 movl         $64, %ecx
	//0x00000f26 LBB0_193
	0x48, 0x8b, 0x75, 0xc8, //0x00000f26 movq         $-56(%rbp), %rsi
	0x4c, 0x8b, 0x4d, 0xc0, //0x00000f2a movq         $-64(%rbp), %r9
	0x4c, 0x2b, 0x5d, 0xd0, //0x00000f2e subq         $-48(%rbp), %r11
	0x48, 0x39, 0xc1, //0x00000f32 cmpq         %rax, %rcx
	0x0f, 0x82, 0x2a, 0x16, 0x00, 0x00, //0x00000f35 jb           LBB0_455
	0x4d, 0x8d, 0x5c, 0x03, 0x01, //0x00000f3b leaq         $1(%r11,%rax), %r11
	//0x00000f40 LBB0_195
	0x4d, 0x85, 0xdb, //0x00000f40 testq        %r11, %r11
	0x0f, 0x88, 0x58, 0x14, 0x00, 0x00, //0x00000f43 js           LBB0_424
	0x4c, 0x89, 0x1e, //0x00000f49 movq         %r11, (%rsi)
	0x4c, 0x89, 0xf8, //0x00000f4c movq         %r15, %rax
	0x48, 0x83, 0x7d, 0xa8, 0x00, //0x00000f4f cmpq         $0, $-88(%rbp)
	0x4c, 0x8b, 0x65, 0xd0, //0x00000f54 movq         $-48(%rbp), %r12
	0x0f, 0x8e, 0x34, 0x14, 0x00, 0x00, //0x00000f58 jle          LBB0_423
	0x49, 0x8b, 0x01, //0x00000f5e movq         (%r9), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00000f61 cmpq         $4095, %rax
	0x0f, 0x8f, 0xe0, 0x13, 0x00, 0x00, //0x00000f67 jg           LBB0_441
	0x48, 0x8d, 0x48, 0x01, //0x00000f6d leaq         $1(%rax), %rcx
	0x49, 0x89, 0x09, //0x00000f71 movq         %rcx, (%r9)
	0x49, 0xc7, 0x44, 0xc1, 0x08, 0x04, 0x00, 0x00, 0x00, //0x00000f74 movq         $4, $8(%r9,%rax,8)
	0xe9, 0x1e, 0xf2, 0xff, 0xff, //0x00000f7d jmp          LBB0_2
	//0x00000f82 LBB0_199
	0x48, 0x8b, 0x45, 0x90, //0x00000f82 movq         $-112(%rbp), %rax
	0x48, 0x8b, 0x50, 0x08, //0x00000f86 movq         $8(%rax), %rdx
	0xf6, 0x45, 0x80, 0x20, //0x00000f8a testb        $32, $-128(%rbp)
	0x4c, 0x89, 0x5d, 0xa8, //0x00000f8e movq         %r11, $-88(%rbp)
	0x48, 0x89, 0x55, 0xb0, //0x00000f92 movq         %rdx, $-80(%rbp)
	0x0f, 0x85, 0xa3, 0x04, 0x00, 0x00, //0x00000f96 jne          LBB0_268
	0x49, 0x89, 0xd4, //0x00000f9c movq         %rdx, %r12
	0x4d, 0x29, 0xdc, //0x00000f9f subq         %r11, %r12
	0x0f, 0x84, 0x99, 0x15, 0x00, 0x00, //0x00000fa2 je           LBB0_456
	0x48, 0x8b, 0x45, 0xd0, //0x00000fa8 movq         $-48(%rbp), %rax
	0x4c, 0x89, 0xd9, //0x00000fac movq         %r11, %rcx
	0x49, 0x01, 0xc3, //0x00000faf addq         %rax, %r11
	0x49, 0x83, 0xfc, 0x40, //0x00000fb2 cmpq         $64, %r12
	0x0f, 0x82, 0x12, 0x0f, 0x00, 0x00, //0x00000fb6 jb           LBB0_360
	0x44, 0x89, 0xe0, //0x00000fbc movl         %r12d, %eax
	0x83, 0xe0, 0x3f, //0x00000fbf andl         $63, %eax
	0x48, 0x89, 0x45, 0xb8, //0x00000fc2 movq         %rax, $-72(%rbp)
	0x4e, 0x8d, 0x44, 0x02, 0xc0, //0x00000fc6 leaq         $-64(%rdx,%r8), %r8
	0x49, 0x83, 0xe0, 0xc0, //0x00000fcb andq         $-64, %r8
	0x49, 0x01, 0xc8, //0x00000fcf addq         %rcx, %r8
	0x4c, 0x03, 0x45, 0x88, //0x00000fd2 addq         $-120(%rbp), %r8
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000fd6 movq         $-1, %r9
	0x45, 0x31, 0xd2, //0x00000fdd xorl         %r10d, %r10d
	//0x00000fe0 .p2align 4, 0x90
	//0x00000fe0 LBB0_203
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x00000fe0 vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x00000fe5 vmovdqu      $16(%r11), %xmm4
	0xc4, 0xc1, 0x7a, 0x6f, 0x6b, 0x20, //0x00000feb vmovdqu      $32(%r11), %xmm5
	0xc4, 0xc1, 0x7a, 0x6f, 0x73, 0x30, //0x00000ff1 vmovdqu      $48(%r11), %xmm6
	0xc5, 0xe9, 0x74, 0xf8, //0x00000ff7 vpcmpeqb     %xmm0, %xmm2, %xmm7
	0xc5, 0xf9, 0xd7, 0xff, //0x00000ffb vpmovmskb    %xmm7, %edi
	0xc5, 0xd9, 0x74, 0xf8, //0x00000fff vpcmpeqb     %xmm0, %xmm4, %xmm7
	0xc5, 0xf9, 0xd7, 0xcf, //0x00001003 vpmovmskb    %xmm7, %ecx
	0xc5, 0xd1, 0x74, 0xf8, //0x00001007 vpcmpeqb     %xmm0, %xmm5, %xmm7
	0xc5, 0xf9, 0xd7, 0xf7, //0x0000100b vpmovmskb    %xmm7, %esi
	0xc5, 0xc9, 0x74, 0xf8, //0x0000100f vpcmpeqb     %xmm0, %xmm6, %xmm7
	0xc5, 0x79, 0xd7, 0xef, //0x00001013 vpmovmskb    %xmm7, %r13d
	0xc5, 0xe9, 0x74, 0xd1, //0x00001017 vpcmpeqb     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xd2, //0x0000101b vpmovmskb    %xmm2, %edx
	0xc5, 0xd9, 0x74, 0xd1, //0x0000101f vpcmpeqb     %xmm1, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xc2, //0x00001023 vpmovmskb    %xmm2, %eax
	0xc5, 0xd1, 0x74, 0xd1, //0x00001027 vpcmpeqb     %xmm1, %xmm5, %xmm2
	0xc5, 0xf9, 0xd7, 0xda, //0x0000102b vpmovmskb    %xmm2, %ebx
	0xc5, 0xc9, 0x74, 0xd1, //0x0000102f vpcmpeqb     %xmm1, %xmm6, %xmm2
	0xc5, 0x79, 0xd7, 0xf2, //0x00001033 vpmovmskb    %xmm2, %r14d
	0x49, 0xc1, 0xe5, 0x30, //0x00001037 shlq         $48, %r13
	0x48, 0xc1, 0xe6, 0x20, //0x0000103b shlq         $32, %rsi
	0x48, 0xc1, 0xe1, 0x10, //0x0000103f shlq         $16, %rcx
	0x48, 0x09, 0xcf, //0x00001043 orq          %rcx, %rdi
	0x48, 0x09, 0xf7, //0x00001046 orq          %rsi, %rdi
	0x49, 0xc1, 0xe6, 0x30, //0x00001049 shlq         $48, %r14
	0x48, 0xc1, 0xe3, 0x20, //0x0000104d shlq         $32, %rbx
	0x48, 0xc1, 0xe0, 0x10, //0x00001051 shlq         $16, %rax
	0x48, 0x09, 0xc2, //0x00001055 orq          %rax, %rdx
	0x48, 0x09, 0xda, //0x00001058 orq          %rbx, %rdx
	0x4c, 0x09, 0xf2, //0x0000105b orq          %r14, %rdx
	0x49, 0x83, 0xf9, 0xff, //0x0000105e cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00001062 jne          LBB0_205
	0x48, 0x85, 0xd2, //0x00001068 testq        %rdx, %rdx
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x0000106b jne          LBB0_213
	//0x00001071 LBB0_205
	0x4c, 0x09, 0xef, //0x00001071 orq          %r13, %rdi
	0x48, 0x89, 0xd0, //0x00001074 movq         %rdx, %rax
	0x4c, 0x09, 0xd0, //0x00001077 orq          %r10, %rax
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x0000107a jne          LBB0_214
	//0x00001080 LBB0_206
	0x48, 0x85, 0xff, //0x00001080 testq        %rdi, %rdi
	0x0f, 0x85, 0xf6, 0xf4, 0xff, 0xff, //0x00001083 jne          LBB0_57
	//0x00001089 LBB0_207
	0x49, 0x83, 0xc4, 0xc0, //0x00001089 addq         $-64, %r12
	0x49, 0x83, 0xc3, 0x40, //0x0000108d addq         $64, %r11
	0x49, 0x83, 0xfc, 0x3f, //0x00001091 cmpq         $63, %r12
	0x0f, 0x87, 0x45, 0xff, 0xff, 0xff, //0x00001095 ja           LBB0_203
	0xe9, 0x01, 0x0b, 0x00, 0x00, //0x0000109b jmp          LBB0_208
	//0x000010a0 LBB0_213
	0x4c, 0x89, 0xd8, //0x000010a0 movq         %r11, %rax
	0x48, 0x2b, 0x45, 0xd0, //0x000010a3 subq         $-48(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xca, //0x000010a7 bsfq         %rdx, %r9
	0x49, 0x01, 0xc1, //0x000010ab addq         %rax, %r9
	0x4c, 0x09, 0xef, //0x000010ae orq          %r13, %rdi
	0x48, 0x89, 0xd0, //0x000010b1 movq         %rdx, %rax
	0x4c, 0x09, 0xd0, //0x000010b4 orq          %r10, %rax
	0x0f, 0x84, 0xc3, 0xff, 0xff, 0xff, //0x000010b7 je           LBB0_206
	//0x000010bd LBB0_214
	0x4c, 0x89, 0xd0, //0x000010bd movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x000010c0 notq         %rax
	0x48, 0x21, 0xd0, //0x000010c3 andq         %rdx, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x000010c6 leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xd1, //0x000010ca orq          %r10, %rcx
	0x48, 0x89, 0xce, //0x000010cd movq         %rcx, %rsi
	0x48, 0xf7, 0xd6, //0x000010d0 notq         %rsi
	0x48, 0x21, 0xd6, //0x000010d3 andq         %rdx, %rsi
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000010d6 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd6, //0x000010e0 andq         %rdx, %rsi
	0x45, 0x31, 0xd2, //0x000010e3 xorl         %r10d, %r10d
	0x48, 0x01, 0xc6, //0x000010e6 addq         %rax, %rsi
	0x41, 0x0f, 0x92, 0xc2, //0x000010e9 setb         %r10b
	0x48, 0x01, 0xf6, //0x000010ed addq         %rsi, %rsi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000010f0 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc6, //0x000010fa xorq         %rax, %rsi
	0x48, 0x21, 0xce, //0x000010fd andq         %rcx, %rsi
	0x48, 0xf7, 0xd6, //0x00001100 notq         %rsi
	0x48, 0x21, 0xf7, //0x00001103 andq         %rsi, %rdi
	0x48, 0x85, 0xff, //0x00001106 testq        %rdi, %rdi
	0x0f, 0x84, 0x7a, 0xff, 0xff, 0xff, //0x00001109 je           LBB0_207
	0xe9, 0x6b, 0xf4, 0xff, 0xff, //0x0000110f jmp          LBB0_57
	//0x00001114 LBB0_215
	0x48, 0x8b, 0x45, 0x90, //0x00001114 movq         $-112(%rbp), %rax
	0x4c, 0x8b, 0x50, 0x08, //0x00001118 movq         $8(%rax), %r10
	0x4d, 0x29, 0xda, //0x0000111c subq         %r11, %r10
	0x0f, 0x84, 0xfa, 0x12, 0x00, 0x00, //0x0000111f je           LBB0_438
	0x4c, 0x89, 0x5d, 0xa8, //0x00001125 movq         %r11, $-88(%rbp)
	0x4d, 0x01, 0xdc, //0x00001129 addq         %r11, %r12
	0x41, 0x80, 0x3c, 0x24, 0x30, //0x0000112c cmpb         $48, (%r12)
	0x0f, 0x85, 0x36, 0x00, 0x00, 0x00, //0x00001131 jne          LBB0_220
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00001137 movl         $1, %r11d
	0x49, 0x83, 0xfa, 0x01, //0x0000113d cmpq         $1, %r10
	0x0f, 0x84, 0x7c, 0x05, 0x00, 0x00, //0x00001141 je           LBB0_303
	0x41, 0x8a, 0x44, 0x24, 0x01, //0x00001147 movb         $1(%r12), %al
	0x04, 0xd2, //0x0000114c addb         $-46, %al
	0x3c, 0x37, //0x0000114e cmpb         $55, %al
	0x0f, 0x87, 0x6d, 0x05, 0x00, 0x00, //0x00001150 ja           LBB0_303
	0x0f, 0xb6, 0xc0, //0x00001156 movzbl       %al, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00001159 movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x00001163 btq          %rax, %rcx
	0x0f, 0x83, 0x56, 0x05, 0x00, 0x00, //0x00001167 jae          LBB0_303
	//0x0000116d LBB0_220
	0x49, 0x83, 0xfa, 0x10, //0x0000116d cmpq         $16, %r10
	0x0f, 0x82, 0x30, 0x0d, 0x00, 0x00, //0x00001171 jb           LBB0_359
	0x4d, 0x8d, 0x4a, 0xf0, //0x00001177 leaq         $-16(%r10), %r9
	0x4c, 0x89, 0xc8, //0x0000117b movq         %r9, %rax
	0x48, 0x83, 0xe0, 0xf0, //0x0000117e andq         $-16, %rax
	0x4e, 0x8d, 0x44, 0x20, 0x10, //0x00001182 leaq         $16(%rax,%r12), %r8
	0x41, 0x83, 0xe1, 0x0f, //0x00001187 andl         $15, %r9d
	0x48, 0xc7, 0x45, 0xb8, 0xff, 0xff, 0xff, 0xff, //0x0000118b movq         $-1, $-72(%rbp)
	0x48, 0xc7, 0x45, 0xb0, 0xff, 0xff, 0xff, 0xff, //0x00001193 movq         $-1, $-80(%rbp)
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x0000119b movq         $-1, %r14
	0x4d, 0x89, 0xe5, //0x000011a2 movq         %r12, %r13
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000011a5 .p2align 4, 0x90
	//0x000011b0 LBB0_222
	0xc4, 0xc1, 0x7a, 0x6f, 0x55, 0x00, //0x000011b0 vmovdqu      (%r13), %xmm2
	0xc4, 0xc1, 0x69, 0x64, 0xe0, //0x000011b6 vpcmpgtb     %xmm8, %xmm2, %xmm4
	0xc5, 0xb1, 0x64, 0xea, //0x000011bb vpcmpgtb     %xmm2, %xmm9, %xmm5
	0xc5, 0xd9, 0xdb, 0xe5, //0x000011bf vpand        %xmm5, %xmm4, %xmm4
	0xc5, 0xa9, 0x74, 0xea, //0x000011c3 vpcmpeqb     %xmm2, %xmm10, %xmm5
	0xc5, 0xa1, 0x74, 0xf2, //0x000011c7 vpcmpeqb     %xmm2, %xmm11, %xmm6
	0xc5, 0xc9, 0xeb, 0xed, //0x000011cb vpor         %xmm5, %xmm6, %xmm5
	0xc5, 0x99, 0xeb, 0xf2, //0x000011cf vpor         %xmm2, %xmm12, %xmm6
	0xc5, 0x91, 0x74, 0xd2, //0x000011d3 vpcmpeqb     %xmm2, %xmm13, %xmm2
	0xc5, 0x89, 0x74, 0xf6, //0x000011d7 vpcmpeqb     %xmm6, %xmm14, %xmm6
	0xc5, 0xc9, 0xeb, 0xfa, //0x000011db vpor         %xmm2, %xmm6, %xmm7
	0xc5, 0xd1, 0xeb, 0xe4, //0x000011df vpor         %xmm4, %xmm5, %xmm4
	0xc5, 0xc1, 0xeb, 0xe4, //0x000011e3 vpor         %xmm4, %xmm7, %xmm4
	0xc5, 0xf9, 0xd7, 0xc2, //0x000011e7 vpmovmskb    %xmm2, %eax
	0xc5, 0xf9, 0xd7, 0xde, //0x000011eb vpmovmskb    %xmm6, %ebx
	0xc5, 0xf9, 0xd7, 0xd5, //0x000011ef vpmovmskb    %xmm5, %edx
	0xc5, 0xf9, 0xd7, 0xcc, //0x000011f3 vpmovmskb    %xmm4, %ecx
	0xbe, 0xaa, 0xaa, 0xaa, 0xaa, //0x000011f7 movl         $2863311530, %esi
	0x48, 0x8d, 0xbe, 0x55, 0x55, 0x55, 0x55, //0x000011fc leaq         $1431655765(%rsi), %rdi
	0x48, 0x31, 0xcf, //0x00001203 xorq         %rcx, %rdi
	0x48, 0x0f, 0xbc, 0xcf, //0x00001206 bsfq         %rdi, %rcx
	0x83, 0xf9, 0x10, //0x0000120a cmpl         $16, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x0000120d je           LBB0_224
	0xbf, 0xff, 0xff, 0xff, 0xff, //0x00001213 movl         $-1, %edi
	0xd3, 0xe7, //0x00001218 shll         %cl, %edi
	0xf7, 0xd7, //0x0000121a notl         %edi
	0x21, 0xf8, //0x0000121c andl         %edi, %eax
	0x21, 0xfb, //0x0000121e andl         %edi, %ebx
	0x21, 0xd7, //0x00001220 andl         %edx, %edi
	0x89, 0xfa, //0x00001222 movl         %edi, %edx
	//0x00001224 LBB0_224
	0x8d, 0x78, 0xff, //0x00001224 leal         $-1(%rax), %edi
	0x21, 0xc7, //0x00001227 andl         %eax, %edi
	0x0f, 0x85, 0x4f, 0x09, 0x00, 0x00, //0x00001229 jne          LBB0_337
	0x8d, 0x7b, 0xff, //0x0000122f leal         $-1(%rbx), %edi
	0x21, 0xdf, //0x00001232 andl         %ebx, %edi
	0x0f, 0x85, 0x44, 0x09, 0x00, 0x00, //0x00001234 jne          LBB0_337
	0x8d, 0x7a, 0xff, //0x0000123a leal         $-1(%rdx), %edi
	0x21, 0xd7, //0x0000123d andl         %edx, %edi
	0x0f, 0x85, 0x39, 0x09, 0x00, 0x00, //0x0000123f jne          LBB0_337
	0x85, 0xc0, //0x00001245 testl        %eax, %eax
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00001247 je           LBB0_230
	0x4c, 0x89, 0xef, //0x0000124d movq         %r13, %rdi
	0x4c, 0x29, 0xe7, //0x00001250 subq         %r12, %rdi
	0x44, 0x0f, 0xbc, 0xd8, //0x00001253 bsfl         %eax, %r11d
	0x49, 0x01, 0xfb, //0x00001257 addq         %rdi, %r11
	0x49, 0x83, 0xfe, 0xff, //0x0000125a cmpq         $-1, %r14
	0x0f, 0x85, 0x24, 0x09, 0x00, 0x00, //0x0000125e jne          LBB0_338
	0x4d, 0x89, 0xde, //0x00001264 movq         %r11, %r14
	//0x00001267 LBB0_230
	0x85, 0xdb, //0x00001267 testl        %ebx, %ebx
	0x0f, 0x84, 0x1c, 0x00, 0x00, 0x00, //0x00001269 je           LBB0_233
	0x4c, 0x89, 0xe8, //0x0000126f movq         %r13, %rax
	0x4c, 0x29, 0xe0, //0x00001272 subq         %r12, %rax
	0x44, 0x0f, 0xbc, 0xdb, //0x00001275 bsfl         %ebx, %r11d
	0x49, 0x01, 0xc3, //0x00001279 addq         %rax, %r11
	0x48, 0x83, 0x7d, 0xb0, 0xff, //0x0000127c cmpq         $-1, $-80(%rbp)
	0x0f, 0x85, 0x01, 0x09, 0x00, 0x00, //0x00001281 jne          LBB0_338
	0x4c, 0x89, 0x5d, 0xb0, //0x00001287 movq         %r11, $-80(%rbp)
	//0x0000128b LBB0_233
	0x85, 0xd2, //0x0000128b testl        %edx, %edx
	0x0f, 0x84, 0x1c, 0x00, 0x00, 0x00, //0x0000128d je           LBB0_236
	0x4c, 0x89, 0xe8, //0x00001293 movq         %r13, %rax
	0x4c, 0x29, 0xe0, //0x00001296 subq         %r12, %rax
	0x44, 0x0f, 0xbc, 0xda, //0x00001299 bsfl         %edx, %r11d
	0x49, 0x01, 0xc3, //0x0000129d addq         %rax, %r11
	0x48, 0x83, 0x7d, 0xb8, 0xff, //0x000012a0 cmpq         $-1, $-72(%rbp)
	0x0f, 0x85, 0xdd, 0x08, 0x00, 0x00, //0x000012a5 jne          LBB0_338
	0x4c, 0x89, 0x5d, 0xb8, //0x000012ab movq         %r11, $-72(%rbp)
	//0x000012af LBB0_236
	0x83, 0xf9, 0x10, //0x000012af cmpl         $16, %ecx
	0x0f, 0x85, 0x81, 0x03, 0x00, 0x00, //0x000012b2 jne          LBB0_291
	0x49, 0x83, 0xc5, 0x10, //0x000012b8 addq         $16, %r13
	0x49, 0x83, 0xc2, 0xf0, //0x000012bc addq         $-16, %r10
	0x49, 0x83, 0xfa, 0x0f, //0x000012c0 cmpq         $15, %r10
	0x0f, 0x87, 0xe6, 0xfe, 0xff, 0xff, //0x000012c4 ja           LBB0_222
	0x4d, 0x85, 0xc9, //0x000012ca testq        %r9, %r9
	0x48, 0x8d, 0x35, 0xf8, 0x14, 0x00, 0x00, //0x000012cd leaq         $5368(%rip), %rsi  /* LJTI0_2+0(%rip) */
	0x48, 0x8b, 0x7d, 0xb0, //0x000012d4 movq         $-80(%rbp), %rdi
	0x48, 0x8b, 0x5d, 0xb8, //0x000012d8 movq         $-72(%rbp), %rbx
	0x0f, 0x84, 0x65, 0x03, 0x00, 0x00, //0x000012dc je           LBB0_292
	//0x000012e2 LBB0_239
	0x4b, 0x8d, 0x0c, 0x08, //0x000012e2 leaq         (%r8,%r9), %rcx
	0xe9, 0x01, 0x01, 0x00, 0x00, //0x000012e6 jmp          LBB0_262
	//0x000012eb LBB0_240
	0x49, 0x8b, 0x01, //0x000012eb movq         (%r9), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x000012ee cmpq         $4095, %rax
	0x0f, 0x8f, 0x53, 0x10, 0x00, 0x00, //0x000012f4 jg           LBB0_441
	0x48, 0x8d, 0x48, 0x01, //0x000012fa leaq         $1(%rax), %rcx
	0x49, 0x89, 0x09, //0x000012fe movq         %rcx, (%r9)
	0x49, 0xc7, 0x44, 0xc1, 0x08, 0x05, 0x00, 0x00, 0x00, //0x00001301 movq         $5, $8(%r9,%rax,8)
	0xe9, 0x91, 0xee, 0xff, 0xff, //0x0000130a jmp          LBB0_2
	//0x0000130f LBB0_242
	0x48, 0x8b, 0x4d, 0x90, //0x0000130f movq         $-112(%rbp), %rcx
	0x48, 0x8b, 0x49, 0x08, //0x00001313 movq         $8(%rcx), %rcx
	0x48, 0x8d, 0x51, 0xfc, //0x00001317 leaq         $-4(%rcx), %rdx
	0x49, 0x39, 0xd7, //0x0000131b cmpq         %rdx, %r15
	0x0f, 0x83, 0x97, 0x10, 0x00, 0x00, //0x0000131e jae          LBB0_440
	0x43, 0x8b, 0x0c, 0x1c, //0x00001324 movl         (%r12,%r11), %ecx
	0x81, 0xf9, 0x61, 0x6c, 0x73, 0x65, //0x00001328 cmpl         $1702063201, %ecx
	0x0f, 0x85, 0xfa, 0x10, 0x00, 0x00, //0x0000132e jne          LBB0_442
	0x4c, 0x89, 0xd9, //0x00001334 movq         %r11, %rcx
	0x4d, 0x8d, 0x5f, 0x05, //0x00001337 leaq         $5(%r15), %r11
	0xe9, 0x50, 0x00, 0x00, 0x00, //0x0000133b jmp          LBB0_254
	//0x00001340 LBB0_245
	0x48, 0x8b, 0x4d, 0x90, //0x00001340 movq         $-112(%rbp), %rcx
	0x48, 0x8b, 0x49, 0x08, //0x00001344 movq         $8(%rcx), %rcx
	0x48, 0x8d, 0x51, 0xfd, //0x00001348 leaq         $-3(%rcx), %rdx
	0x49, 0x39, 0xd7, //0x0000134c cmpq         %rdx, %r15
	0x0f, 0x83, 0x66, 0x10, 0x00, 0x00, //0x0000134f jae          LBB0_440
	0x41, 0x81, 0x3a, 0x6e, 0x75, 0x6c, 0x6c, //0x00001355 cmpl         $1819047278, (%r10)
	0x0f, 0x84, 0x27, 0x00, 0x00, 0x00, //0x0000135c je           LBB0_253
	0xe9, 0x1c, 0x11, 0x00, 0x00, //0x00001362 jmp          LBB0_247
	//0x00001367 LBB0_251
	0x48, 0x8b, 0x4d, 0x90, //0x00001367 movq         $-112(%rbp), %rcx
	0x48, 0x8b, 0x49, 0x08, //0x0000136b movq         $8(%rcx), %rcx
	0x48, 0x8d, 0x51, 0xfd, //0x0000136f leaq         $-3(%rcx), %rdx
	0x49, 0x39, 0xd7, //0x00001373 cmpq         %rdx, %r15
	0x0f, 0x83, 0x3f, 0x10, 0x00, 0x00, //0x00001376 jae          LBB0_440
	0x41, 0x81, 0x3a, 0x74, 0x72, 0x75, 0x65, //0x0000137c cmpl         $1702195828, (%r10)
	0x0f, 0x85, 0x4c, 0x11, 0x00, 0x00, //0x00001383 jne          LBB0_447
	//0x00001389 LBB0_253
	0x4c, 0x89, 0xd9, //0x00001389 movq         %r11, %rcx
	0x4d, 0x8d, 0x5f, 0x04, //0x0000138c leaq         $4(%r15), %r11
	//0x00001390 LBB0_254
	0x4c, 0x89, 0x1e, //0x00001390 movq         %r11, (%rsi)
	0x4c, 0x89, 0xf8, //0x00001393 movq         %r15, %rax
	0x48, 0x85, 0xc9, //0x00001396 testq        %rcx, %rcx
	0x0f, 0x8f, 0x01, 0xee, 0xff, 0xff, //0x00001399 jg           LBB0_2
	0xe9, 0xee, 0x0f, 0x00, 0x00, //0x0000139f jmp          LBB0_423
	//0x000013a4 LBB0_255
	0x49, 0x8b, 0x01, //0x000013a4 movq         (%r9), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x000013a7 cmpq         $4095, %rax
	0x0f, 0x8f, 0x9a, 0x0f, 0x00, 0x00, //0x000013ad jg           LBB0_441
	0x48, 0x8d, 0x48, 0x01, //0x000013b3 leaq         $1(%rax), %rcx
	0x49, 0x89, 0x09, //0x000013b7 movq         %rcx, (%r9)
	0x49, 0xc7, 0x44, 0xc1, 0x08, 0x06, 0x00, 0x00, 0x00, //0x000013ba movq         $6, $8(%r9,%rax,8)
	0xe9, 0xd8, 0xed, 0xff, 0xff, //0x000013c3 jmp          LBB0_2
	//0x000013c8 LBB0_259
	0x49, 0x89, 0xc3, //0x000013c8 movq         %rax, %r11
	0x4d, 0x29, 0xe3, //0x000013cb subq         %r12, %r11
	0x49, 0x83, 0xfe, 0xff, //0x000013ce cmpq         $-1, %r14
	0x0f, 0x85, 0x95, 0x0a, 0x00, 0x00, //0x000013d2 jne          LBB0_356
	0x49, 0xff, 0xcb, //0x000013d8 decq         %r11
	0x4d, 0x89, 0xde, //0x000013db movq         %r11, %r14
	0x90, 0x90, //0x000013de .p2align 4, 0x90
	//0x000013e0 LBB0_261
	0x49, 0x89, 0xc0, //0x000013e0 movq         %rax, %r8
	0x49, 0xff, 0xc9, //0x000013e3 decq         %r9
	0x0f, 0x84, 0x03, 0x0a, 0x00, 0x00, //0x000013e6 je           LBB0_341
	//0x000013ec LBB0_262
	0x41, 0x0f, 0xbe, 0x10, //0x000013ec movsbl       (%r8), %edx
	0x83, 0xc2, 0xd5, //0x000013f0 addl         $-43, %edx
	0x83, 0xfa, 0x3a, //0x000013f3 cmpl         $58, %edx
	0x0f, 0x87, 0x4b, 0x02, 0x00, 0x00, //0x000013f6 ja           LBB0_292
	0x49, 0x8d, 0x40, 0x01, //0x000013fc leaq         $1(%r8), %rax
	0x48, 0x63, 0x14, 0x96, //0x00001400 movslq       (%rsi,%rdx,4), %rdx
	0x48, 0x01, 0xf2, //0x00001404 addq         %rsi, %rdx
	0xff, 0xe2, //0x00001407 jmpq         *%rdx
	//0x00001409 LBB0_264
	0x49, 0x89, 0xc3, //0x00001409 movq         %rax, %r11
	0x4d, 0x29, 0xe3, //0x0000140c subq         %r12, %r11
	0x48, 0x83, 0xfb, 0xff, //0x0000140f cmpq         $-1, %rbx
	0x0f, 0x85, 0x54, 0x0a, 0x00, 0x00, //0x00001413 jne          LBB0_356
	0x49, 0xff, 0xcb, //0x00001419 decq         %r11
	0x4c, 0x89, 0xdb, //0x0000141c movq         %r11, %rbx
	0xe9, 0xbc, 0xff, 0xff, 0xff, //0x0000141f jmp          LBB0_261
	//0x00001424 LBB0_266
	0x49, 0x89, 0xc3, //0x00001424 movq         %rax, %r11
	0x4d, 0x29, 0xe3, //0x00001427 subq         %r12, %r11
	0x48, 0x83, 0xff, 0xff, //0x0000142a cmpq         $-1, %rdi
	0x0f, 0x85, 0x39, 0x0a, 0x00, 0x00, //0x0000142e jne          LBB0_356
	0x49, 0xff, 0xcb, //0x00001434 decq         %r11
	0x4c, 0x89, 0xdf, //0x00001437 movq         %r11, %rdi
	0xe9, 0xa1, 0xff, 0xff, 0xff, //0x0000143a jmp          LBB0_261
	//0x0000143f LBB0_268
	0x48, 0x89, 0xd0, //0x0000143f movq         %rdx, %rax
	0x4c, 0x29, 0xd8, //0x00001442 subq         %r11, %rax
	0x0f, 0x84, 0xf6, 0x10, 0x00, 0x00, //0x00001445 je           LBB0_456
	0x4c, 0x89, 0xd9, //0x0000144b movq         %r11, %rcx
	0x4d, 0x01, 0xe3, //0x0000144e addq         %r12, %r11
	0x48, 0x83, 0xf8, 0x40, //0x00001451 cmpq         $64, %rax
	0x0f, 0x82, 0x8c, 0x0a, 0x00, 0x00, //0x00001455 jb           LBB0_361
	0x41, 0x89, 0xc2, //0x0000145b movl         %eax, %r10d
	0x41, 0x83, 0xe2, 0x3f, //0x0000145e andl         $63, %r10d
	0x4e, 0x8d, 0x64, 0x02, 0xc0, //0x00001462 leaq         $-64(%rdx,%r8), %r12
	0x49, 0x83, 0xe4, 0xc0, //0x00001467 andq         $-64, %r12
	0x49, 0x01, 0xcc, //0x0000146b addq         %rcx, %r12
	0x4c, 0x03, 0x65, 0x88, //0x0000146e addq         $-120(%rbp), %r12
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001472 movq         $-1, %r9
	0x45, 0x31, 0xc0, //0x00001479 xorl         %r8d, %r8d
	0x90, 0x90, 0x90, 0x90, //0x0000147c .p2align 4, 0x90
	//0x00001480 LBB0_271
	0xc4, 0xc1, 0x7a, 0x6f, 0x33, //0x00001480 vmovdqu      (%r11), %xmm6
	0xc4, 0xc1, 0x7a, 0x6f, 0x6b, 0x10, //0x00001485 vmovdqu      $16(%r11), %xmm5
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x20, //0x0000148b vmovdqu      $32(%r11), %xmm4
	0xc4, 0xc1, 0x7a, 0x6f, 0x7b, 0x30, //0x00001491 vmovdqu      $48(%r11), %xmm7
	0xc5, 0xc9, 0x74, 0xd0, //0x00001497 vpcmpeqb     %xmm0, %xmm6, %xmm2
	0xc5, 0xf9, 0xd7, 0xf2, //0x0000149b vpmovmskb    %xmm2, %esi
	0xc5, 0xd1, 0x74, 0xd0, //0x0000149f vpcmpeqb     %xmm0, %xmm5, %xmm2
	0xc5, 0xf9, 0xd7, 0xda, //0x000014a3 vpmovmskb    %xmm2, %ebx
	0xc5, 0xd9, 0x74, 0xd0, //0x000014a7 vpcmpeqb     %xmm0, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xca, //0x000014ab vpmovmskb    %xmm2, %ecx
	0xc5, 0xc1, 0x74, 0xd0, //0x000014af vpcmpeqb     %xmm0, %xmm7, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x000014b3 vpmovmskb    %xmm2, %edi
	0xc5, 0xc9, 0x74, 0xd1, //0x000014b7 vpcmpeqb     %xmm1, %xmm6, %xmm2
	0xc5, 0x79, 0xd7, 0xea, //0x000014bb vpmovmskb    %xmm2, %r13d
	0xc5, 0xd1, 0x74, 0xd1, //0x000014bf vpcmpeqb     %xmm1, %xmm5, %xmm2
	0xc5, 0xf9, 0xd7, 0xd2, //0x000014c3 vpmovmskb    %xmm2, %edx
	0xc5, 0xd9, 0x74, 0xd1, //0x000014c7 vpcmpeqb     %xmm1, %xmm4, %xmm2
	0x48, 0xc1, 0xe3, 0x10, //0x000014cb shlq         $16, %rbx
	0x48, 0x09, 0xde, //0x000014cf orq          %rbx, %rsi
	0xc5, 0xf9, 0xd7, 0xda, //0x000014d2 vpmovmskb    %xmm2, %ebx
	0xc5, 0xc1, 0x74, 0xd1, //0x000014d6 vpcmpeqb     %xmm1, %xmm7, %xmm2
	0x48, 0xc1, 0xe1, 0x20, //0x000014da shlq         $32, %rcx
	0x48, 0x09, 0xce, //0x000014de orq          %rcx, %rsi
	0xc5, 0xf9, 0xd7, 0xca, //0x000014e1 vpmovmskb    %xmm2, %ecx
	0xc5, 0x81, 0x64, 0xd6, //0x000014e5 vpcmpgtb     %xmm6, %xmm15, %xmm2
	0xc5, 0xc9, 0x64, 0xf3, //0x000014e9 vpcmpgtb     %xmm3, %xmm6, %xmm6
	0xc5, 0xc9, 0xdb, 0xd2, //0x000014ed vpand        %xmm2, %xmm6, %xmm2
	0x48, 0xc1, 0xe2, 0x10, //0x000014f1 shlq         $16, %rdx
	0x49, 0x09, 0xd5, //0x000014f5 orq          %rdx, %r13
	0xc5, 0xf9, 0xd7, 0xd2, //0x000014f8 vpmovmskb    %xmm2, %edx
	0xc5, 0x81, 0x64, 0xd5, //0x000014fc vpcmpgtb     %xmm5, %xmm15, %xmm2
	0xc5, 0xd1, 0x64, 0xeb, //0x00001500 vpcmpgtb     %xmm3, %xmm5, %xmm5
	0xc5, 0xd1, 0xdb, 0xd2, //0x00001504 vpand        %xmm2, %xmm5, %xmm2
	0x48, 0xc1, 0xe3, 0x20, //0x00001508 shlq         $32, %rbx
	0x49, 0x09, 0xdd, //0x0000150c orq          %rbx, %r13
	0xc5, 0xf9, 0xd7, 0xda, //0x0000150f vpmovmskb    %xmm2, %ebx
	0xc5, 0x81, 0x64, 0xd4, //0x00001513 vpcmpgtb     %xmm4, %xmm15, %xmm2
	0xc5, 0xd9, 0x64, 0xe3, //0x00001517 vpcmpgtb     %xmm3, %xmm4, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x0000151b vpand        %xmm2, %xmm4, %xmm2
	0x48, 0xc1, 0xe1, 0x30, //0x0000151f shlq         $48, %rcx
	0x49, 0x09, 0xcd, //0x00001523 orq          %rcx, %r13
	0xc5, 0xf9, 0xd7, 0xca, //0x00001526 vpmovmskb    %xmm2, %ecx
	0xc5, 0x81, 0x64, 0xd7, //0x0000152a vpcmpgtb     %xmm7, %xmm15, %xmm2
	0xc5, 0xc1, 0x64, 0xe3, //0x0000152e vpcmpgtb     %xmm3, %xmm7, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x00001532 vpand        %xmm2, %xmm4, %xmm2
	0x48, 0xc1, 0xe3, 0x10, //0x00001536 shlq         $16, %rbx
	0x48, 0x09, 0xda, //0x0000153a orq          %rbx, %rdx
	0xc5, 0x79, 0xd7, 0xf2, //0x0000153d vpmovmskb    %xmm2, %r14d
	0x48, 0xc1, 0xe7, 0x30, //0x00001541 shlq         $48, %rdi
	0x48, 0xc1, 0xe1, 0x20, //0x00001545 shlq         $32, %rcx
	0x49, 0x83, 0xf9, 0xff, //0x00001549 cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x0000154d jne          LBB0_273
	0x4d, 0x85, 0xed, //0x00001553 testq        %r13, %r13
	0x0f, 0x85, 0xa4, 0x00, 0x00, 0x00, //0x00001556 jne          LBB0_287
	//0x0000155c LBB0_273
	0x49, 0xc1, 0xe6, 0x30, //0x0000155c shlq         $48, %r14
	0x48, 0x09, 0xca, //0x00001560 orq          %rcx, %rdx
	0x48, 0x09, 0xfe, //0x00001563 orq          %rdi, %rsi
	0x4c, 0x89, 0xe9, //0x00001566 movq         %r13, %rcx
	0x4c, 0x09, 0xc1, //0x00001569 orq          %r8, %rcx
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x0000156c jne          LBB0_288
	0x4c, 0x09, 0xf2, //0x00001572 orq          %r14, %rdx
	0x48, 0x85, 0xf6, //0x00001575 testq        %rsi, %rsi
	0x0f, 0x85, 0x95, 0x00, 0x00, 0x00, //0x00001578 jne          LBB0_289
	//0x0000157e LBB0_275
	0x48, 0x85, 0xd2, //0x0000157e testq        %rdx, %rdx
	0x0f, 0x85, 0xa7, 0x0f, 0x00, 0x00, //0x00001581 jne          LBB0_452
	0x48, 0x83, 0xc0, 0xc0, //0x00001587 addq         $-64, %rax
	0x49, 0x83, 0xc3, 0x40, //0x0000158b addq         $64, %r11
	0x48, 0x83, 0xf8, 0x3f, //0x0000158f cmpq         $63, %rax
	0x0f, 0x87, 0xe7, 0xfe, 0xff, 0xff, //0x00001593 ja           LBB0_271
	0xe9, 0xf0, 0x06, 0x00, 0x00, //0x00001599 jmp          LBB0_277
	//0x0000159e LBB0_288
	0x4c, 0x89, 0xc1, //0x0000159e movq         %r8, %rcx
	0x48, 0xf7, 0xd1, //0x000015a1 notq         %rcx
	0x4c, 0x21, 0xe9, //0x000015a4 andq         %r13, %rcx
	0x4c, 0x89, 0x55, 0xb8, //0x000015a7 movq         %r10, $-72(%rbp)
	0x4c, 0x8d, 0x14, 0x09, //0x000015ab leaq         (%rcx,%rcx), %r10
	0x4d, 0x09, 0xc2, //0x000015af orq          %r8, %r10
	0x4c, 0x89, 0xd7, //0x000015b2 movq         %r10, %rdi
	0x48, 0xf7, 0xd7, //0x000015b5 notq         %rdi
	0x4c, 0x21, 0xef, //0x000015b8 andq         %r13, %rdi
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000015bb movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xdf, //0x000015c5 andq         %rbx, %rdi
	0x45, 0x31, 0xc0, //0x000015c8 xorl         %r8d, %r8d
	0x48, 0x01, 0xcf, //0x000015cb addq         %rcx, %rdi
	0x41, 0x0f, 0x92, 0xc0, //0x000015ce setb         %r8b
	0x48, 0x01, 0xff, //0x000015d2 addq         %rdi, %rdi
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000015d5 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xcf, //0x000015df xorq         %rcx, %rdi
	0x4c, 0x21, 0xd7, //0x000015e2 andq         %r10, %rdi
	0x4c, 0x8b, 0x55, 0xb8, //0x000015e5 movq         $-72(%rbp), %r10
	0x48, 0xf7, 0xd7, //0x000015e9 notq         %rdi
	0x48, 0x21, 0xfe, //0x000015ec andq         %rdi, %rsi
	0x4c, 0x09, 0xf2, //0x000015ef orq          %r14, %rdx
	0x48, 0x85, 0xf6, //0x000015f2 testq        %rsi, %rsi
	0x0f, 0x84, 0x83, 0xff, 0xff, 0xff, //0x000015f5 je           LBB0_275
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x000015fb jmp          LBB0_289
	//0x00001600 LBB0_287
	0x4c, 0x89, 0xdb, //0x00001600 movq         %r11, %rbx
	0x48, 0x2b, 0x5d, 0xd0, //0x00001603 subq         $-48(%rbp), %rbx
	0x4d, 0x0f, 0xbc, 0xcd, //0x00001607 bsfq         %r13, %r9
	0x49, 0x01, 0xd9, //0x0000160b addq         %rbx, %r9
	0xe9, 0x49, 0xff, 0xff, 0xff, //0x0000160e jmp          LBB0_273
	//0x00001613 LBB0_289
	0x48, 0x0f, 0xbc, 0xc6, //0x00001613 bsfq         %rsi, %rax
	0x48, 0x85, 0xd2, //0x00001617 testq        %rdx, %rdx
	0x4c, 0x8b, 0x65, 0xd0, //0x0000161a movq         $-48(%rbp), %r12
	0x0f, 0x84, 0xbe, 0x00, 0x00, 0x00, //0x0000161e je           LBB0_304
	0x48, 0x0f, 0xbc, 0xca, //0x00001624 bsfq         %rdx, %rcx
	0x4d, 0x29, 0xe3, //0x00001628 subq         %r12, %r11
	0x48, 0x39, 0xc1, //0x0000162b cmpq         %rax, %rcx
	0x0f, 0x83, 0x94, 0xf8, 0xff, 0xff, //0x0000162e jae          LBB0_186
	0xe9, 0x11, 0x0f, 0x00, 0x00, //0x00001634 jmp          LBB0_306
	//0x00001639 LBB0_291
	0x49, 0x01, 0xcd, //0x00001639 addq         %rcx, %r13
	0x4d, 0x89, 0xe8, //0x0000163c movq         %r13, %r8
	0x48, 0x8b, 0x7d, 0xb0, //0x0000163f movq         $-80(%rbp), %rdi
	0x48, 0x8b, 0x5d, 0xb8, //0x00001643 movq         $-72(%rbp), %rbx
	//0x00001647 LBB0_292
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001647 movq         $-1, %r11
	0x48, 0x85, 0xff, //0x0000164e testq        %rdi, %rdi
	0x48, 0x8b, 0x75, 0xc8, //0x00001651 movq         $-56(%rbp), %rsi
	0x0f, 0x84, 0xcb, 0x0d, 0x00, 0x00, //0x00001655 je           LBB0_439
	//0x0000165b LBB0_293
	0x48, 0x85, 0xdb, //0x0000165b testq        %rbx, %rbx
	0x0f, 0x84, 0xc2, 0x0d, 0x00, 0x00, //0x0000165e je           LBB0_439
	0x4d, 0x85, 0xf6, //0x00001664 testq        %r14, %r14
	0x4c, 0x8b, 0x4d, 0xc0, //0x00001667 movq         $-64(%rbp), %r9
	0x0f, 0x84, 0xb5, 0x0d, 0x00, 0x00, //0x0000166b je           LBB0_439
	0x4d, 0x29, 0xe0, //0x00001671 subq         %r12, %r8
	0x49, 0x8d, 0x40, 0xff, //0x00001674 leaq         $-1(%r8), %rax
	0x48, 0x39, 0xc7, //0x00001678 cmpq         %rax, %rdi
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x0000167b je           LBB0_301
	0x49, 0x39, 0xc6, //0x00001681 cmpq         %rax, %r14
	0x0f, 0x84, 0x2a, 0x00, 0x00, 0x00, //0x00001684 je           LBB0_301
	0x48, 0x39, 0xc3, //0x0000168a cmpq         %rax, %rbx
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x0000168d je           LBB0_301
	0x48, 0x85, 0xdb, //0x00001693 testq        %rbx, %rbx
	0x0f, 0x8e, 0x6e, 0x00, 0x00, 0x00, //0x00001696 jle          LBB0_309
	0x48, 0x8d, 0x43, 0xff, //0x0000169c leaq         $-1(%rbx), %rax
	0x48, 0x39, 0xc7, //0x000016a0 cmpq         %rax, %rdi
	0x0f, 0x84, 0x61, 0x00, 0x00, 0x00, //0x000016a3 je           LBB0_309
	0x48, 0xf7, 0xd3, //0x000016a9 notq         %rbx
	0x49, 0x89, 0xdb, //0x000016ac movq         %rbx, %r11
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x000016af jmp          LBB0_302
	//0x000016b4 LBB0_301
	0x49, 0xf7, 0xd8, //0x000016b4 negq         %r8
	0x4d, 0x89, 0xc3, //0x000016b7 movq         %r8, %r11
	//0x000016ba LBB0_302
	0x4d, 0x85, 0xdb, //0x000016ba testq        %r11, %r11
	0x0f, 0x88, 0x63, 0x0d, 0x00, 0x00, //0x000016bd js           LBB0_439
	//0x000016c3 LBB0_303
	0x48, 0x8b, 0x4d, 0xa8, //0x000016c3 movq         $-88(%rbp), %rcx
	0x49, 0x01, 0xcb, //0x000016c7 addq         %rcx, %r11
	0x4c, 0x89, 0x1e, //0x000016ca movq         %r11, (%rsi)
	0x4c, 0x89, 0xf8, //0x000016cd movq         %r15, %rax
	0x48, 0x85, 0xc9, //0x000016d0 testq        %rcx, %rcx
	0x4c, 0x8b, 0x65, 0xd0, //0x000016d3 movq         $-48(%rbp), %r12
	0x0f, 0x8f, 0xc3, 0xea, 0xff, 0xff, //0x000016d7 jg           LBB0_2
	0xe9, 0xb0, 0x0c, 0x00, 0x00, //0x000016dd jmp          LBB0_423
	//0x000016e2 LBB0_304
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x000016e2 movl         $64, %ecx
	0x4d, 0x29, 0xe3, //0x000016e7 subq         %r12, %r11
	0x48, 0x39, 0xc1, //0x000016ea cmpq         %rax, %rcx
	0x0f, 0x83, 0xd5, 0xf7, 0xff, 0xff, //0x000016ed jae          LBB0_186
	0xe9, 0x52, 0x0e, 0x00, 0x00, //0x000016f3 jmp          LBB0_306
	//0x000016f8 LBB0_308
	0x4d, 0x29, 0xd6, //0x000016f8 subq         %r10, %r14
	0x44, 0x0f, 0xbc, 0xde, //0x000016fb bsfl         %esi, %r11d
	0x4d, 0x01, 0xf3, //0x000016ff addq         %r14, %r11
	0x49, 0xf7, 0xd3, //0x00001702 notq         %r11
	0xe9, 0xa6, 0x03, 0x00, 0x00, //0x00001705 jmp          LBB0_326
	//0x0000170a LBB0_309
	0x4c, 0x89, 0xf0, //0x0000170a movq         %r14, %rax
	0x48, 0x09, 0xf8, //0x0000170d orq          %rdi, %rax
	0x49, 0x39, 0xfe, //0x00001710 cmpq         %rdi, %r14
	0x0f, 0x8c, 0x66, 0x01, 0x00, 0x00, //0x00001713 jl           LBB0_312
	0x48, 0x85, 0xc0, //0x00001719 testq        %rax, %rax
	0x0f, 0x88, 0x5d, 0x01, 0x00, 0x00, //0x0000171c js           LBB0_312
	0x49, 0xf7, 0xd6, //0x00001722 notq         %r14
	0x4d, 0x89, 0xf3, //0x00001725 movq         %r14, %r11
	0xe9, 0x8d, 0xff, 0xff, 0xff, //0x00001728 jmp          LBB0_302
	//0x0000172d LBB0_49
	0x4c, 0x8b, 0x5d, 0xb8, //0x0000172d movq         $-72(%rbp), %r11
	0x4d, 0x89, 0xd5, //0x00001731 movq         %r10, %r13
	0x49, 0x83, 0xfd, 0x20, //0x00001734 cmpq         $32, %r13
	0x0f, 0x82, 0x66, 0x08, 0x00, 0x00, //0x00001738 jb           LBB0_366
	//0x0000173e LBB0_50
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x0000173e vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x00001743 vmovdqu      $16(%r11), %xmm4
	0xc5, 0xe9, 0x74, 0xe8, //0x00001749 vpcmpeqb     %xmm0, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xcd, //0x0000174d vpmovmskb    %xmm5, %ecx
	0xc5, 0xd9, 0x74, 0xe8, //0x00001751 vpcmpeqb     %xmm0, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xd5, //0x00001755 vpmovmskb    %xmm5, %edx
	0xc5, 0xe9, 0x74, 0xd1, //0x00001759 vpcmpeqb     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x0000175d vpmovmskb    %xmm2, %edi
	0xc5, 0xd9, 0x74, 0xd1, //0x00001761 vpcmpeqb     %xmm1, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xc2, //0x00001765 vpmovmskb    %xmm2, %eax
	0x48, 0xc1, 0xe2, 0x10, //0x00001769 shlq         $16, %rdx
	0x48, 0xc1, 0xe0, 0x10, //0x0000176d shlq         $16, %rax
	0x48, 0x09, 0xc7, //0x00001771 orq          %rax, %rdi
	0x49, 0x83, 0xf9, 0xff, //0x00001774 cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00001778 jne          LBB0_52
	0x48, 0x85, 0xff, //0x0000177e testq        %rdi, %rdi
	0x0f, 0x85, 0xbc, 0x07, 0x00, 0x00, //0x00001781 jne          LBB0_363
	//0x00001787 LBB0_52
	0x48, 0x09, 0xca, //0x00001787 orq          %rcx, %rdx
	0x48, 0x89, 0xf8, //0x0000178a movq         %rdi, %rax
	0x4c, 0x09, 0xe0, //0x0000178d orq          %r12, %rax
	0x0f, 0x85, 0xca, 0x07, 0x00, 0x00, //0x00001790 jne          LBB0_364
	//0x00001796 LBB0_53
	0x48, 0x85, 0xd2, //0x00001796 testq        %rdx, %rdx
	0x0f, 0x84, 0xfd, 0x07, 0x00, 0x00, //0x00001799 je           LBB0_365
	//0x0000179f LBB0_54
	0x48, 0x0f, 0xbc, 0xc2, //0x0000179f bsfq         %rdx, %rax
	0xe9, 0xdb, 0xed, 0xff, 0xff, //0x000017a3 jmp          LBB0_58
	//0x000017a8 LBB0_113
	0x4d, 0x89, 0xd3, //0x000017a8 movq         %r10, %r11
	0x48, 0x8b, 0x45, 0xb8, //0x000017ab movq         $-72(%rbp), %rax
	0x4c, 0x8b, 0x65, 0xd0, //0x000017af movq         $-48(%rbp), %r12
	0x48, 0x83, 0xf8, 0x20, //0x000017b3 cmpq         $32, %rax
	0x0f, 0x82, 0x66, 0x02, 0x00, 0x00, //0x000017b7 jb           LBB0_317
	//0x000017bd LBB0_114
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x000017bd vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x000017c2 vmovdqu      $16(%r11), %xmm4
	0xc5, 0xe9, 0x74, 0xe8, //0x000017c8 vpcmpeqb     %xmm0, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xcd, //0x000017cc vpmovmskb    %xmm5, %ecx
	0xc5, 0xd9, 0x74, 0xe8, //0x000017d0 vpcmpeqb     %xmm0, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xf5, //0x000017d4 vpmovmskb    %xmm5, %esi
	0xc5, 0xe9, 0x74, 0xe9, //0x000017d8 vpcmpeqb     %xmm1, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xd5, //0x000017dc vpmovmskb    %xmm5, %edx
	0xc5, 0xd9, 0x74, 0xe9, //0x000017e0 vpcmpeqb     %xmm1, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xdd, //0x000017e4 vpmovmskb    %xmm5, %ebx
	0xc5, 0x81, 0x64, 0xea, //0x000017e8 vpcmpgtb     %xmm2, %xmm15, %xmm5
	0xc5, 0xe9, 0x64, 0xd3, //0x000017ec vpcmpgtb     %xmm3, %xmm2, %xmm2
	0xc5, 0xe9, 0xdb, 0xd5, //0x000017f0 vpand        %xmm5, %xmm2, %xmm2
	0xc5, 0x79, 0xd7, 0xf2, //0x000017f4 vpmovmskb    %xmm2, %r14d
	0xc5, 0x81, 0x64, 0xd4, //0x000017f8 vpcmpgtb     %xmm4, %xmm15, %xmm2
	0xc5, 0xd9, 0x64, 0xe3, //0x000017fc vpcmpgtb     %xmm3, %xmm4, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x00001800 vpand        %xmm2, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x00001804 vpmovmskb    %xmm2, %edi
	0x48, 0xc1, 0xe6, 0x10, //0x00001808 shlq         $16, %rsi
	0x48, 0xc1, 0xe3, 0x10, //0x0000180c shlq         $16, %rbx
	0x48, 0x09, 0xda, //0x00001810 orq          %rbx, %rdx
	0x49, 0x83, 0xf9, 0xff, //0x00001813 cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00001817 jne          LBB0_116
	0x48, 0x85, 0xd2, //0x0000181d testq        %rdx, %rdx
	0x0f, 0x85, 0xf8, 0x07, 0x00, 0x00, //0x00001820 jne          LBB0_373
	//0x00001826 LBB0_116
	0x48, 0xc1, 0xe7, 0x10, //0x00001826 shlq         $16, %rdi
	0x48, 0x09, 0xce, //0x0000182a orq          %rcx, %rsi
	0x48, 0x89, 0xd1, //0x0000182d movq         %rdx, %rcx
	0x4c, 0x09, 0xc1, //0x00001830 orq          %r8, %rcx
	0x0f, 0x85, 0xcb, 0x06, 0x00, 0x00, //0x00001833 jne          LBB0_362
	//0x00001839 LBB0_117
	0x4c, 0x09, 0xf7, //0x00001839 orq          %r14, %rdi
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x0000183c movl         $64, %ecx
	0xba, 0x40, 0x00, 0x00, 0x00, //0x00001841 movl         $64, %edx
	0x48, 0x85, 0xf6, //0x00001846 testq        %rsi, %rsi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00001849 je           LBB0_119
	0x48, 0x0f, 0xbc, 0xd6, //0x0000184f bsfq         %rsi, %rdx
	//0x00001853 LBB0_119
	0x48, 0x85, 0xff, //0x00001853 testq        %rdi, %rdi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00001856 je           LBB0_121
	0x48, 0x0f, 0xbc, 0xcf, //0x0000185c bsfq         %rdi, %rcx
	//0x00001860 LBB0_121
	0x48, 0x85, 0xf6, //0x00001860 testq        %rsi, %rsi
	0x0f, 0x84, 0xa9, 0x01, 0x00, 0x00, //0x00001863 je           LBB0_315
	//0x00001869 LBB0_122
	0x4d, 0x29, 0xe3, //0x00001869 subq         %r12, %r11
	0x48, 0x39, 0xd1, //0x0000186c cmpq         %rdx, %rcx
	0x0f, 0x82, 0xd5, 0x0c, 0x00, 0x00, //0x0000186f jb           LBB0_306
	0x4d, 0x8d, 0x5c, 0x13, 0x01, //0x00001875 leaq         $1(%r11,%rdx), %r11
	0xe9, 0x4e, 0xf6, 0xff, 0xff, //0x0000187a jmp          LBB0_187
	//0x0000187f LBB0_312
	0x48, 0x85, 0xc0, //0x0000187f testq        %rax, %rax
	0x48, 0x8d, 0x47, 0xff, //0x00001882 leaq         $-1(%rdi), %rax
	0x48, 0xf7, 0xd7, //0x00001886 notq         %rdi
	0x49, 0x0f, 0x48, 0xf8, //0x00001889 cmovsq       %r8, %rdi
	0x49, 0x39, 0xc6, //0x0000188d cmpq         %rax, %r14
	0x49, 0x0f, 0x45, 0xf8, //0x00001890 cmovneq      %r8, %rdi
	0x49, 0x89, 0xfb, //0x00001894 movq         %rdi, %r11
	0xe9, 0x1e, 0xfe, 0xff, 0xff, //0x00001897 jmp          LBB0_302
	//0x0000189c LBB0_313
	0x48, 0xf7, 0xd0, //0x0000189c notq         %rax
	0x49, 0x89, 0xc3, //0x0000189f movq         %rax, %r11
	0xe9, 0x09, 0x02, 0x00, 0x00, //0x000018a2 jmp          LBB0_326
	//0x000018a7 LBB0_314
	0x48, 0x89, 0x4d, 0xa8, //0x000018a7 movq         %rcx, $-88(%rbp)
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000018ab movq         $-1, %rax
	0x4d, 0x85, 0xed, //0x000018b2 testq        %r13, %r13
	0x48, 0x8b, 0x75, 0xc8, //0x000018b5 movq         $-56(%rbp), %rsi
	0x0f, 0x85, 0x86, 0xf3, 0xff, 0xff, //0x000018b9 jne          LBB0_151
	0xe9, 0xbe, 0x0a, 0x00, 0x00, //0x000018bf jmp          LBB0_420
	//0x000018c4 LBB0_134
	0x4c, 0x8b, 0x5d, 0xb8, //0x000018c4 movq         $-72(%rbp), %r11
	0x4d, 0x89, 0xd5, //0x000018c8 movq         %r10, %r13
	0x49, 0x83, 0xfd, 0x20, //0x000018cb cmpq         $32, %r13
	0x0f, 0x82, 0x57, 0x08, 0x00, 0x00, //0x000018cf jb           LBB0_382
	//0x000018d5 LBB0_135
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x000018d5 vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x000018da vmovdqu      $16(%r11), %xmm4
	0xc5, 0xe9, 0x74, 0xe8, //0x000018e0 vpcmpeqb     %xmm0, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xcd, //0x000018e4 vpmovmskb    %xmm5, %ecx
	0xc5, 0xd9, 0x74, 0xe8, //0x000018e8 vpcmpeqb     %xmm0, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xd5, //0x000018ec vpmovmskb    %xmm5, %edx
	0xc5, 0xe9, 0x74, 0xd1, //0x000018f0 vpcmpeqb     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x000018f4 vpmovmskb    %xmm2, %edi
	0xc5, 0xd9, 0x74, 0xd1, //0x000018f8 vpcmpeqb     %xmm1, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xc2, //0x000018fc vpmovmskb    %xmm2, %eax
	0x48, 0xc1, 0xe2, 0x10, //0x00001900 shlq         $16, %rdx
	0x48, 0xc1, 0xe0, 0x10, //0x00001904 shlq         $16, %rax
	0x48, 0x09, 0xc7, //0x00001908 orq          %rax, %rdi
	0x49, 0x83, 0xfc, 0xff, //0x0000190b cmpq         $-1, %r12
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x0000190f jne          LBB0_137
	0x48, 0x85, 0xff, //0x00001915 testq        %rdi, %rdi
	0x0f, 0x85, 0xad, 0x07, 0x00, 0x00, //0x00001918 jne          LBB0_379
	//0x0000191e LBB0_137
	0x48, 0x09, 0xca, //0x0000191e orq          %rcx, %rdx
	0x48, 0x89, 0xf8, //0x00001921 movq         %rdi, %rax
	0x4c, 0x09, 0xc8, //0x00001924 orq          %r9, %rax
	0x0f, 0x85, 0xbb, 0x07, 0x00, 0x00, //0x00001927 jne          LBB0_380
	//0x0000192d LBB0_138
	0x48, 0x85, 0xd2, //0x0000192d testq        %rdx, %rdx
	0x0f, 0x84, 0xee, 0x07, 0x00, 0x00, //0x00001930 je           LBB0_381
	//0x00001936 LBB0_139
	0x48, 0x0f, 0xbc, 0xc2, //0x00001936 bsfq         %rdx, %rax
	0xe9, 0x94, 0xf2, 0xff, 0xff, //0x0000193a jmp          LBB0_143
	//0x0000193f LBB0_170
	0x4d, 0x89, 0xc3, //0x0000193f movq         %r8, %r11
	0x4c, 0x89, 0xd0, //0x00001942 movq         %r10, %rax
	0x48, 0x83, 0xf8, 0x20, //0x00001945 cmpq         $32, %rax
	0x0f, 0x82, 0x7b, 0x01, 0x00, 0x00, //0x00001949 jb           LBB0_329
	//0x0000194f LBB0_171
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x0000194f vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x00001954 vmovdqu      $16(%r11), %xmm4
	0xc5, 0xe9, 0x74, 0xe8, //0x0000195a vpcmpeqb     %xmm0, %xmm2, %xmm5
	0xc5, 0x79, 0xd7, 0xc5, //0x0000195e vpmovmskb    %xmm5, %r8d
	0xc5, 0xd9, 0x74, 0xe8, //0x00001962 vpcmpeqb     %xmm0, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xf5, //0x00001966 vpmovmskb    %xmm5, %esi
	0xc5, 0xe9, 0x74, 0xe9, //0x0000196a vpcmpeqb     %xmm1, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xd5, //0x0000196e vpmovmskb    %xmm5, %edx
	0xc5, 0xd9, 0x74, 0xe9, //0x00001972 vpcmpeqb     %xmm1, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xcd, //0x00001976 vpmovmskb    %xmm5, %ecx
	0xc5, 0x81, 0x64, 0xea, //0x0000197a vpcmpgtb     %xmm2, %xmm15, %xmm5
	0xc5, 0xe9, 0x64, 0xd3, //0x0000197e vpcmpgtb     %xmm3, %xmm2, %xmm2
	0xc5, 0xe9, 0xdb, 0xd5, //0x00001982 vpand        %xmm5, %xmm2, %xmm2
	0xc5, 0x79, 0xd7, 0xf2, //0x00001986 vpmovmskb    %xmm2, %r14d
	0xc5, 0x81, 0x64, 0xd4, //0x0000198a vpcmpgtb     %xmm4, %xmm15, %xmm2
	0xc5, 0xd9, 0x64, 0xe3, //0x0000198e vpcmpgtb     %xmm3, %xmm4, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x00001992 vpand        %xmm2, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x00001996 vpmovmskb    %xmm2, %edi
	0x48, 0xc1, 0xe6, 0x10, //0x0000199a shlq         $16, %rsi
	0x48, 0xc1, 0xe1, 0x10, //0x0000199e shlq         $16, %rcx
	0x48, 0x09, 0xca, //0x000019a2 orq          %rcx, %rdx
	0x49, 0x83, 0xfc, 0xff, //0x000019a5 cmpq         $-1, %r12
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x000019a9 jne          LBB0_173
	0x48, 0x85, 0xd2, //0x000019af testq        %rdx, %rdx
	0x0f, 0x85, 0x02, 0x08, 0x00, 0x00, //0x000019b2 jne          LBB0_390
	//0x000019b8 LBB0_173
	0x48, 0xc1, 0xe7, 0x10, //0x000019b8 shlq         $16, %rdi
	0x4c, 0x09, 0xc6, //0x000019bc orq          %r8, %rsi
	0x48, 0x89, 0xd1, //0x000019bf movq         %rdx, %rcx
	0x4c, 0x09, 0xc9, //0x000019c2 orq          %r9, %rcx
	0x0f, 0x85, 0x65, 0x06, 0x00, 0x00, //0x000019c5 jne          LBB0_374
	//0x000019cb LBB0_174
	0x4c, 0x09, 0xf7, //0x000019cb orq          %r14, %rdi
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x000019ce movl         $64, %ecx
	0xba, 0x40, 0x00, 0x00, 0x00, //0x000019d3 movl         $64, %edx
	0x48, 0x85, 0xf6, //0x000019d8 testq        %rsi, %rsi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x000019db je           LBB0_176
	0x48, 0x0f, 0xbc, 0xd6, //0x000019e1 bsfq         %rsi, %rdx
	//0x000019e5 LBB0_176
	0x48, 0x85, 0xff, //0x000019e5 testq        %rdi, %rdi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x000019e8 je           LBB0_178
	0x48, 0x0f, 0xbc, 0xcf, //0x000019ee bsfq         %rdi, %rcx
	//0x000019f2 LBB0_178
	0x48, 0x85, 0xf6, //0x000019f2 testq        %rsi, %rsi
	0x0f, 0x84, 0xbe, 0x00, 0x00, 0x00, //0x000019f5 je           LBB0_327
	0x4c, 0x2b, 0x5d, 0xd0, //0x000019fb subq         $-48(%rbp), %r11
	0x48, 0x39, 0xd1, //0x000019ff cmpq         %rdx, %rcx
	0x0f, 0x82, 0x83, 0x0b, 0x00, 0x00, //0x00001a02 jb           LBB0_458
	0x4d, 0x8d, 0x5c, 0x13, 0x01, //0x00001a08 leaq         $1(%r11,%rdx), %r11
	0xe9, 0xc8, 0xf1, 0xff, 0xff, //0x00001a0d jmp          LBB0_144
	//0x00001a12 LBB0_315
	0x48, 0x85, 0xff, //0x00001a12 testq        %rdi, %rdi
	0x0f, 0x85, 0x5c, 0x0b, 0x00, 0x00, //0x00001a15 jne          LBB0_457
	0x49, 0x83, 0xc3, 0x20, //0x00001a1b addq         $32, %r11
	0x48, 0x83, 0xc0, 0xe0, //0x00001a1f addq         $-32, %rax
	//0x00001a23 LBB0_317
	0x4d, 0x85, 0xc0, //0x00001a23 testq        %r8, %r8
	0x0f, 0x85, 0x6f, 0x06, 0x00, 0x00, //0x00001a26 jne          LBB0_377
	0x48, 0x85, 0xc0, //0x00001a2c testq        %rax, %rax
	0x0f, 0x84, 0x2e, 0x09, 0x00, 0x00, //0x00001a2f je           LBB0_416
	//0x00001a35 LBB0_319
	0x41, 0x0f, 0xb6, 0x0b, //0x00001a35 movzbl       (%r11), %ecx
	0x80, 0xf9, 0x22, //0x00001a39 cmpb         $34, %cl
	0x0f, 0x84, 0xa4, 0x03, 0x00, 0x00, //0x00001a3c je           LBB0_355
	0x80, 0xf9, 0x5c, //0x00001a42 cmpb         $92, %cl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00001a45 je           LBB0_323
	0x80, 0xf9, 0x1f, //0x00001a4b cmpb         $31, %cl
	0x0f, 0x86, 0x2f, 0x0b, 0x00, 0x00, //0x00001a4e jbe          LBB0_461
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001a54 movq         $-1, %rcx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00001a5b movl         $1, %edx
	0x49, 0x01, 0xd3, //0x00001a60 addq         %rdx, %r11
	0x48, 0x01, 0xc8, //0x00001a63 addq         %rcx, %rax
	0x0f, 0x85, 0xc9, 0xff, 0xff, 0xff, //0x00001a66 jne          LBB0_319
	0xe9, 0xf2, 0x08, 0x00, 0x00, //0x00001a6c jmp          LBB0_416
	//0x00001a71 LBB0_323
	0x48, 0x83, 0xf8, 0x01, //0x00001a71 cmpq         $1, %rax
	0x0f, 0x84, 0xe8, 0x08, 0x00, 0x00, //0x00001a75 je           LBB0_416
	0x4c, 0x89, 0xd9, //0x00001a7b movq         %r11, %rcx
	0x4c, 0x8b, 0x65, 0xd0, //0x00001a7e movq         $-48(%rbp), %r12
	0x4c, 0x29, 0xe1, //0x00001a82 subq         %r12, %rcx
	0x49, 0x83, 0xf9, 0xff, //0x00001a85 cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc9, //0x00001a89 cmoveq       %rcx, %r9
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00001a8d movq         $-2, %rcx
	0xba, 0x02, 0x00, 0x00, 0x00, //0x00001a94 movl         $2, %edx
	0x49, 0x01, 0xd3, //0x00001a99 addq         %rdx, %r11
	0x48, 0x01, 0xc8, //0x00001a9c addq         %rcx, %rax
	0x0f, 0x85, 0x90, 0xff, 0xff, 0xff, //0x00001a9f jne          LBB0_319
	0xe9, 0xb9, 0x08, 0x00, 0x00, //0x00001aa5 jmp          LBB0_416
	//0x00001aaa LBB0_325
	0x48, 0xf7, 0xd6, //0x00001aaa notq         %rsi
	0x49, 0x89, 0xf3, //0x00001aad movq         %rsi, %r11
	//0x00001ab0 LBB0_326
	0x48, 0x8b, 0x75, 0xc8, //0x00001ab0 movq         $-56(%rbp), %rsi
	0xe9, 0xff, 0xf1, 0xff, 0xff, //0x00001ab4 jmp          LBB0_160
	//0x00001ab9 LBB0_327
	0x48, 0x85, 0xff, //0x00001ab9 testq        %rdi, %rdi
	0x0f, 0x85, 0xdb, 0x0a, 0x00, 0x00, //0x00001abc jne          LBB0_459
	0x49, 0x83, 0xc3, 0x20, //0x00001ac2 addq         $32, %r11
	0x48, 0x83, 0xc0, 0xe0, //0x00001ac6 addq         $-32, %rax
	//0x00001aca LBB0_329
	0x4d, 0x85, 0xc9, //0x00001aca testq        %r9, %r9
	0x0f, 0x85, 0x2e, 0x07, 0x00, 0x00, //0x00001acd jne          LBB0_393
	0x48, 0x8b, 0x75, 0xc8, //0x00001ad3 movq         $-56(%rbp), %rsi
	0x4c, 0x8b, 0x4d, 0xc0, //0x00001ad7 movq         $-64(%rbp), %r9
	0x48, 0x85, 0xc0, //0x00001adb testq        %rax, %rax
	0x0f, 0x84, 0xc7, 0x08, 0x00, 0x00, //0x00001ade je           LBB0_425
	//0x00001ae4 LBB0_331
	0x41, 0x0f, 0xb6, 0x0b, //0x00001ae4 movzbl       (%r11), %ecx
	0x80, 0xf9, 0x22, //0x00001ae8 cmpb         $34, %cl
	0x0f, 0x84, 0xa7, 0x00, 0x00, 0x00, //0x00001aeb je           LBB0_340
	0x80, 0xf9, 0x5c, //0x00001af1 cmpb         $92, %cl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00001af4 je           LBB0_335
	0x80, 0xf9, 0x1f, //0x00001afa cmpb         $31, %cl
	0x0f, 0x86, 0xa3, 0x0a, 0x00, 0x00, //0x00001afd jbe          LBB0_460
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001b03 movq         $-1, %rcx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00001b0a movl         $1, %edx
	0x49, 0x01, 0xd3, //0x00001b0f addq         %rdx, %r11
	0x48, 0x01, 0xc8, //0x00001b12 addq         %rcx, %rax
	0x0f, 0x85, 0xc9, 0xff, 0xff, 0xff, //0x00001b15 jne          LBB0_331
	0xe9, 0x8b, 0x08, 0x00, 0x00, //0x00001b1b jmp          LBB0_425
	//0x00001b20 LBB0_335
	0x48, 0x83, 0xf8, 0x01, //0x00001b20 cmpq         $1, %rax
	0x0f, 0x84, 0x8f, 0x0a, 0x00, 0x00, //0x00001b24 je           LBB0_462
	0x4c, 0x89, 0xd9, //0x00001b2a movq         %r11, %rcx
	0x48, 0x2b, 0x4d, 0xd0, //0x00001b2d subq         $-48(%rbp), %rcx
	0x49, 0x83, 0xfc, 0xff, //0x00001b31 cmpq         $-1, %r12
	0x4c, 0x0f, 0x44, 0xe1, //0x00001b35 cmoveq       %rcx, %r12
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00001b39 movq         $-2, %rcx
	0xba, 0x02, 0x00, 0x00, 0x00, //0x00001b40 movl         $2, %edx
	0x48, 0x8b, 0x75, 0xc8, //0x00001b45 movq         $-56(%rbp), %rsi
	0x4c, 0x8b, 0x4d, 0xc0, //0x00001b49 movq         $-64(%rbp), %r9
	0x49, 0x01, 0xd3, //0x00001b4d addq         %rdx, %r11
	0x48, 0x01, 0xc8, //0x00001b50 addq         %rcx, %rax
	0x0f, 0x85, 0x8b, 0xff, 0xff, 0xff, //0x00001b53 jne          LBB0_331
	0xe9, 0x4d, 0x08, 0x00, 0x00, //0x00001b59 jmp          LBB0_425
	//0x00001b5e LBB0_418
	0x48, 0xf7, 0xda, //0x00001b5e negq         %rdx
	0x48, 0x8b, 0x75, 0xc8, //0x00001b61 movq         $-56(%rbp), %rsi
	0x4c, 0x8b, 0x65, 0xd0, //0x00001b65 movq         $-48(%rbp), %r12
	0x4c, 0x8b, 0x4d, 0xc0, //0x00001b69 movq         $-64(%rbp), %r9
	0x49, 0x89, 0xd3, //0x00001b6d movq         %rdx, %r11
	0x4d, 0x85, 0xdb, //0x00001b70 testq        %r11, %r11
	0x0f, 0x89, 0x08, 0xe6, 0xff, 0xff, //0x00001b73 jns          LBB0_258
	0xe9, 0x01, 0x08, 0x00, 0x00, //0x00001b79 jmp          LBB0_419
	//0x00001b7e LBB0_337
	0x4d, 0x29, 0xe5, //0x00001b7e subq         %r12, %r13
	0x44, 0x0f, 0xbc, 0xdf, //0x00001b81 bsfl         %edi, %r11d
	0x4d, 0x01, 0xeb, //0x00001b85 addq         %r13, %r11
	//0x00001b88 LBB0_338
	0x49, 0xf7, 0xd3, //0x00001b88 notq         %r11
	//0x00001b8b LBB0_339
	0x48, 0x8b, 0x75, 0xc8, //0x00001b8b movq         $-56(%rbp), %rsi
	0x4c, 0x8b, 0x4d, 0xc0, //0x00001b8f movq         $-64(%rbp), %r9
	0xe9, 0x22, 0xfb, 0xff, 0xff, //0x00001b93 jmp          LBB0_302
	//0x00001b98 LBB0_340
	0x4c, 0x03, 0x5d, 0x98, //0x00001b98 addq         $-104(%rbp), %r11
	0xe9, 0x9f, 0xf3, 0xff, 0xff, //0x00001b9c jmp          LBB0_195
	//0x00001ba1 LBB0_208
	0x4d, 0x89, 0xc3, //0x00001ba1 movq         %r8, %r11
	0x4c, 0x8b, 0x65, 0xb8, //0x00001ba4 movq         $-72(%rbp), %r12
	0x49, 0x83, 0xfc, 0x20, //0x00001ba8 cmpq         $32, %r12
	0x0f, 0x82, 0x69, 0x00, 0x00, 0x00, //0x00001bac jb           LBB0_399
	//0x00001bb2 LBB0_209
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x00001bb2 vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x00001bb7 vmovdqu      $16(%r11), %xmm4
	0xc5, 0xe9, 0x74, 0xe8, //0x00001bbd vpcmpeqb     %xmm0, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xcd, //0x00001bc1 vpmovmskb    %xmm5, %ecx
	0xc5, 0xd9, 0x74, 0xe8, //0x00001bc5 vpcmpeqb     %xmm0, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xd5, //0x00001bc9 vpmovmskb    %xmm5, %edx
	0xc5, 0xe9, 0x74, 0xd1, //0x00001bcd vpcmpeqb     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x00001bd1 vpmovmskb    %xmm2, %edi
	0xc5, 0xd9, 0x74, 0xd1, //0x00001bd5 vpcmpeqb     %xmm1, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xc2, //0x00001bd9 vpmovmskb    %xmm2, %eax
	0x48, 0xc1, 0xe2, 0x10, //0x00001bdd shlq         $16, %rdx
	0x48, 0xc1, 0xe0, 0x10, //0x00001be1 shlq         $16, %rax
	0x48, 0x09, 0xc7, //0x00001be5 orq          %rax, %rdi
	0x49, 0x83, 0xf9, 0xff, //0x00001be8 cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00001bec jne          LBB0_211
	0x48, 0x85, 0xff, //0x00001bf2 testq        %rdi, %rdi
	0x0f, 0x85, 0x75, 0x06, 0x00, 0x00, //0x00001bf5 jne          LBB0_396
	//0x00001bfb LBB0_211
	0x48, 0x09, 0xca, //0x00001bfb orq          %rcx, %rdx
	0x48, 0x89, 0xf8, //0x00001bfe movq         %rdi, %rax
	0x4c, 0x09, 0xd0, //0x00001c01 orq          %r10, %rax
	0x0f, 0x85, 0x83, 0x06, 0x00, 0x00, //0x00001c04 jne          LBB0_397
	//0x00001c0a LBB0_212
	0x48, 0x85, 0xd2, //0x00001c0a testq        %rdx, %rdx
	0x0f, 0x85, 0x8c, 0xfb, 0xff, 0xff, //0x00001c0d jne          LBB0_54
	//0x00001c13 LBB0_398
	0x49, 0x83, 0xc3, 0x20, //0x00001c13 addq         $32, %r11
	0x49, 0x83, 0xc4, 0xe0, //0x00001c17 addq         $-32, %r12
	//0x00001c1b LBB0_399
	0x4d, 0x85, 0xd2, //0x00001c1b testq        %r10, %r10
	0x0f, 0x85, 0xbe, 0x06, 0x00, 0x00, //0x00001c1e jne          LBB0_411
	0x4d, 0x85, 0xe4, //0x00001c24 testq        %r12, %r12
	0x0f, 0x84, 0x36, 0x07, 0x00, 0x00, //0x00001c27 je           LBB0_416
	//0x00001c2d LBB0_401
	0x49, 0x8d, 0x4b, 0x01, //0x00001c2d leaq         $1(%r11), %rcx
	0x41, 0x0f, 0xb6, 0x1b, //0x00001c31 movzbl       (%r11), %ebx
	0x80, 0xfb, 0x22, //0x00001c35 cmpb         $34, %bl
	0x0f, 0x84, 0x20, 0x02, 0x00, 0x00, //0x00001c38 je           LBB0_406
	0x49, 0x8d, 0x54, 0x24, 0xff, //0x00001c3e leaq         $-1(%r12), %rdx
	0x80, 0xfb, 0x5c, //0x00001c43 cmpb         $92, %bl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00001c46 je           LBB0_404
	0x49, 0x89, 0xd4, //0x00001c4c movq         %rdx, %r12
	0x49, 0x89, 0xcb, //0x00001c4f movq         %rcx, %r11
	0x48, 0x85, 0xd2, //0x00001c52 testq        %rdx, %rdx
	0x0f, 0x85, 0xd2, 0xff, 0xff, 0xff, //0x00001c55 jne          LBB0_401
	0xe9, 0x03, 0x07, 0x00, 0x00, //0x00001c5b jmp          LBB0_416
	//0x00001c60 LBB0_404
	0x48, 0x85, 0xd2, //0x00001c60 testq        %rdx, %rdx
	0x0f, 0x84, 0xfa, 0x06, 0x00, 0x00, //0x00001c63 je           LBB0_416
	0x48, 0x03, 0x4d, 0xa0, //0x00001c69 addq         $-96(%rbp), %rcx
	0x49, 0x83, 0xf9, 0xff, //0x00001c6d cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc9, //0x00001c71 cmoveq       %rcx, %r9
	0x49, 0x83, 0xc3, 0x02, //0x00001c75 addq         $2, %r11
	0x49, 0x83, 0xc4, 0xfe, //0x00001c79 addq         $-2, %r12
	0x4c, 0x89, 0xe2, //0x00001c7d movq         %r12, %rdx
	0x48, 0x85, 0xd2, //0x00001c80 testq        %rdx, %rdx
	0x0f, 0x85, 0xa4, 0xff, 0xff, 0xff, //0x00001c83 jne          LBB0_401
	0xe9, 0xd5, 0x06, 0x00, 0x00, //0x00001c89 jmp          LBB0_416
	//0x00001c8e LBB0_277
	0x4d, 0x89, 0xe3, //0x00001c8e movq         %r12, %r11
	0x4c, 0x89, 0xd0, //0x00001c91 movq         %r10, %rax
	0x48, 0x83, 0xf8, 0x20, //0x00001c94 cmpq         $32, %rax
	0x4c, 0x8b, 0x65, 0xd0, //0x00001c98 movq         $-48(%rbp), %r12
	0x0f, 0x82, 0xbd, 0x00, 0x00, 0x00, //0x00001c9c jb           LBB0_345
	//0x00001ca2 LBB0_278
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x00001ca2 vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x00001ca7 vmovdqu      $16(%r11), %xmm4
	0xc5, 0xe9, 0x74, 0xe8, //0x00001cad vpcmpeqb     %xmm0, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xcd, //0x00001cb1 vpmovmskb    %xmm5, %ecx
	0xc5, 0xd9, 0x74, 0xe8, //0x00001cb5 vpcmpeqb     %xmm0, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xf5, //0x00001cb9 vpmovmskb    %xmm5, %esi
	0xc5, 0xe9, 0x74, 0xe9, //0x00001cbd vpcmpeqb     %xmm1, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xd5, //0x00001cc1 vpmovmskb    %xmm5, %edx
	0xc5, 0xd9, 0x74, 0xe9, //0x00001cc5 vpcmpeqb     %xmm1, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xdd, //0x00001cc9 vpmovmskb    %xmm5, %ebx
	0xc5, 0x81, 0x64, 0xea, //0x00001ccd vpcmpgtb     %xmm2, %xmm15, %xmm5
	0xc5, 0xe9, 0x64, 0xd3, //0x00001cd1 vpcmpgtb     %xmm3, %xmm2, %xmm2
	0xc5, 0xe9, 0xdb, 0xd5, //0x00001cd5 vpand        %xmm5, %xmm2, %xmm2
	0xc5, 0x79, 0xd7, 0xf2, //0x00001cd9 vpmovmskb    %xmm2, %r14d
	0xc5, 0x81, 0x64, 0xd4, //0x00001cdd vpcmpgtb     %xmm4, %xmm15, %xmm2
	0xc5, 0xd9, 0x64, 0xe3, //0x00001ce1 vpcmpgtb     %xmm3, %xmm4, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x00001ce5 vpand        %xmm2, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x00001ce9 vpmovmskb    %xmm2, %edi
	0x48, 0xc1, 0xe6, 0x10, //0x00001ced shlq         $16, %rsi
	0x48, 0xc1, 0xe3, 0x10, //0x00001cf1 shlq         $16, %rbx
	0x48, 0x09, 0xda, //0x00001cf5 orq          %rbx, %rdx
	0x49, 0x83, 0xf9, 0xff, //0x00001cf8 cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00001cfc jne          LBB0_280
	0x48, 0x85, 0xd2, //0x00001d02 testq        %rdx, %rdx
	0x0f, 0x85, 0xc5, 0x05, 0x00, 0x00, //0x00001d05 jne          LBB0_408
	//0x00001d0b LBB0_280
	0x48, 0xc1, 0xe7, 0x10, //0x00001d0b shlq         $16, %rdi
	0x48, 0x09, 0xce, //0x00001d0f orq          %rcx, %rsi
	0x48, 0x89, 0xd1, //0x00001d12 movq         %rdx, %rcx
	0x4c, 0x09, 0xc1, //0x00001d15 orq          %r8, %rcx
	0x0f, 0x85, 0x17, 0x05, 0x00, 0x00, //0x00001d18 jne          LBB0_395
	//0x00001d1e LBB0_281
	0x4c, 0x09, 0xf7, //0x00001d1e orq          %r14, %rdi
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00001d21 movl         $64, %ecx
	0xba, 0x40, 0x00, 0x00, 0x00, //0x00001d26 movl         $64, %edx
	0x48, 0x85, 0xf6, //0x00001d2b testq        %rsi, %rsi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00001d2e je           LBB0_283
	0x48, 0x0f, 0xbc, 0xd6, //0x00001d34 bsfq         %rsi, %rdx
	//0x00001d38 LBB0_283
	0x48, 0x85, 0xff, //0x00001d38 testq        %rdi, %rdi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00001d3b je           LBB0_285
	0x48, 0x0f, 0xbc, 0xcf, //0x00001d41 bsfq         %rdi, %rcx
	//0x00001d45 LBB0_285
	0x48, 0x85, 0xf6, //0x00001d45 testq        %rsi, %rsi
	0x0f, 0x85, 0x1b, 0xfb, 0xff, 0xff, //0x00001d48 jne          LBB0_122
	0x48, 0x85, 0xff, //0x00001d4e testq        %rdi, %rdi
	0x0f, 0x85, 0x20, 0x08, 0x00, 0x00, //0x00001d51 jne          LBB0_457
	0x49, 0x83, 0xc3, 0x20, //0x00001d57 addq         $32, %r11
	0x48, 0x83, 0xc0, 0xe0, //0x00001d5b addq         $-32, %rax
	//0x00001d5f LBB0_345
	0x4d, 0x85, 0xc0, //0x00001d5f testq        %r8, %r8
	0x0f, 0x85, 0xa6, 0x05, 0x00, 0x00, //0x00001d62 jne          LBB0_409
	0x48, 0x85, 0xc0, //0x00001d68 testq        %rax, %rax
	0x0f, 0x84, 0xf2, 0x05, 0x00, 0x00, //0x00001d6b je           LBB0_416
	//0x00001d71 LBB0_347
	0x41, 0x0f, 0xb6, 0x0b, //0x00001d71 movzbl       (%r11), %ecx
	0x80, 0xf9, 0x22, //0x00001d75 cmpb         $34, %cl
	0x0f, 0x84, 0x68, 0x00, 0x00, 0x00, //0x00001d78 je           LBB0_355
	0x80, 0xf9, 0x5c, //0x00001d7e cmpb         $92, %cl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00001d81 je           LBB0_351
	0x80, 0xf9, 0x1f, //0x00001d87 cmpb         $31, %cl
	0x0f, 0x86, 0xf3, 0x07, 0x00, 0x00, //0x00001d8a jbe          LBB0_461
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001d90 movq         $-1, %rcx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00001d97 movl         $1, %edx
	0x49, 0x01, 0xd3, //0x00001d9c addq         %rdx, %r11
	0x48, 0x01, 0xc8, //0x00001d9f addq         %rcx, %rax
	0x0f, 0x85, 0xc9, 0xff, 0xff, 0xff, //0x00001da2 jne          LBB0_347
	0xe9, 0xb6, 0x05, 0x00, 0x00, //0x00001da8 jmp          LBB0_416
	//0x00001dad LBB0_351
	0x48, 0x83, 0xf8, 0x01, //0x00001dad cmpq         $1, %rax
	0x0f, 0x84, 0xac, 0x05, 0x00, 0x00, //0x00001db1 je           LBB0_416
	0x4c, 0x89, 0xd9, //0x00001db7 movq         %r11, %rcx
	0x4c, 0x8b, 0x65, 0xd0, //0x00001dba movq         $-48(%rbp), %r12
	0x4c, 0x29, 0xe1, //0x00001dbe subq         %r12, %rcx
	0x49, 0x83, 0xf9, 0xff, //0x00001dc1 cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc9, //0x00001dc5 cmoveq       %rcx, %r9
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00001dc9 movq         $-2, %rcx
	0xba, 0x02, 0x00, 0x00, 0x00, //0x00001dd0 movl         $2, %edx
	0x49, 0x01, 0xd3, //0x00001dd5 addq         %rdx, %r11
	0x48, 0x01, 0xc8, //0x00001dd8 addq         %rcx, %rax
	0x0f, 0x85, 0x90, 0xff, 0xff, 0xff, //0x00001ddb jne          LBB0_347
	0xe9, 0x7d, 0x05, 0x00, 0x00, //0x00001de1 jmp          LBB0_416
	//0x00001de6 LBB0_355
	0x4c, 0x03, 0x5d, 0x98, //0x00001de6 addq         $-104(%rbp), %r11
	0xe9, 0xde, 0xf0, 0xff, 0xff, //0x00001dea jmp          LBB0_187
	//0x00001def LBB0_341
	0x49, 0x89, 0xc8, //0x00001def movq         %rcx, %r8
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001df2 movq         $-1, %r11
	0x48, 0x85, 0xff, //0x00001df9 testq        %rdi, %rdi
	0x48, 0x8b, 0x75, 0xc8, //0x00001dfc movq         $-56(%rbp), %rsi
	0x0f, 0x85, 0x55, 0xf8, 0xff, 0xff, //0x00001e00 jne          LBB0_293
	0xe9, 0x1b, 0x06, 0x00, 0x00, //0x00001e06 jmp          LBB0_439
	//0x00001e0b LBB0_342
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001e0b movq         $-1, %r11
	0x4c, 0x89, 0x55, 0xa8, //0x00001e12 movq         %r10, $-88(%rbp)
	0x49, 0x89, 0xf9, //0x00001e16 movq         %rdi, %r9
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00001e19 movq         $-1, %r13
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00001e20 movq         $-1, %r12
	0xe9, 0x54, 0xe9, 0xff, 0xff, //0x00001e27 jmp          LBB0_90
	//0x00001e2c LBB0_353
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001e2c movq         $-1, %r9
	0x45, 0x31, 0xe4, //0x00001e33 xorl         %r12d, %r12d
	0x49, 0x83, 0xfd, 0x20, //0x00001e36 cmpq         $32, %r13
	0x0f, 0x83, 0xfe, 0xf8, 0xff, 0xff, //0x00001e3a jae          LBB0_50
	0xe9, 0x5f, 0x01, 0x00, 0x00, //0x00001e40 jmp          LBB0_366
	//0x00001e45 LBB0_354
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001e45 movq         $-1, %r9
	0x45, 0x31, 0xc0, //0x00001e4c xorl         %r8d, %r8d
	0x48, 0x83, 0xf8, 0x20, //0x00001e4f cmpq         $32, %rax
	0x0f, 0x83, 0x64, 0xf9, 0xff, 0xff, //0x00001e53 jae          LBB0_114
	0xe9, 0xc5, 0xfb, 0xff, 0xff, //0x00001e59 jmp          LBB0_317
	//0x00001e5e LBB0_406
	0x4c, 0x8b, 0x65, 0xd0, //0x00001e5e movq         $-48(%rbp), %r12
	//0x00001e62 LBB0_407
	0x4c, 0x29, 0xe1, //0x00001e62 subq         %r12, %rcx
	0x49, 0x89, 0xcb, //0x00001e65 movq         %rcx, %r11
	0xe9, 0x60, 0xf0, 0xff, 0xff, //0x00001e68 jmp          LBB0_187
	//0x00001e6d LBB0_356
	0x49, 0xf7, 0xdb, //0x00001e6d negq         %r11
	0xe9, 0x16, 0xfd, 0xff, 0xff, //0x00001e70 jmp          LBB0_339
	//0x00001e75 LBB0_357
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00001e75 movq         $-1, %r12
	0x45, 0x31, 0xc9, //0x00001e7c xorl         %r9d, %r9d
	0x49, 0x83, 0xfd, 0x20, //0x00001e7f cmpq         $32, %r13
	0x0f, 0x83, 0x4c, 0xfa, 0xff, 0xff, //0x00001e83 jae          LBB0_135
	0xe9, 0x9e, 0x02, 0x00, 0x00, //0x00001e89 jmp          LBB0_382
	//0x00001e8e LBB0_358
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00001e8e movq         $-1, %r12
	0x45, 0x31, 0xc9, //0x00001e95 xorl         %r9d, %r9d
	0x48, 0x83, 0xf8, 0x20, //0x00001e98 cmpq         $32, %rax
	0x0f, 0x83, 0xad, 0xfa, 0xff, 0xff, //0x00001e9c jae          LBB0_171
	0xe9, 0x23, 0xfc, 0xff, 0xff, //0x00001ea2 jmp          LBB0_329
	//0x00001ea7 LBB0_359
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00001ea7 movq         $-1, %r14
	0x4d, 0x89, 0xe0, //0x00001eae movq         %r12, %r8
	0x4d, 0x89, 0xd1, //0x00001eb1 movq         %r10, %r9
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00001eb4 movq         $-1, %rdi
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001ebb movq         $-1, %rbx
	0x48, 0x8d, 0x35, 0x03, 0x09, 0x00, 0x00, //0x00001ec2 leaq         $2307(%rip), %rsi  /* LJTI0_2+0(%rip) */
	0xe9, 0x14, 0xf4, 0xff, 0xff, //0x00001ec9 jmp          LBB0_239
	//0x00001ece LBB0_360
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001ece movq         $-1, %r9
	0x45, 0x31, 0xd2, //0x00001ed5 xorl         %r10d, %r10d
	0x49, 0x83, 0xfc, 0x20, //0x00001ed8 cmpq         $32, %r12
	0x0f, 0x83, 0xd0, 0xfc, 0xff, 0xff, //0x00001edc jae          LBB0_209
	0xe9, 0x34, 0xfd, 0xff, 0xff, //0x00001ee2 jmp          LBB0_399
	//0x00001ee7 LBB0_361
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001ee7 movq         $-1, %r9
	0x45, 0x31, 0xc0, //0x00001eee xorl         %r8d, %r8d
	0x48, 0x83, 0xf8, 0x20, //0x00001ef1 cmpq         $32, %rax
	0x4c, 0x8b, 0x65, 0xd0, //0x00001ef5 movq         $-48(%rbp), %r12
	0x0f, 0x83, 0xa3, 0xfd, 0xff, 0xff, //0x00001ef9 jae          LBB0_278
	0xe9, 0x5b, 0xfe, 0xff, 0xff, //0x00001eff jmp          LBB0_345
	//0x00001f04 LBB0_362
	0x44, 0x89, 0xc1, //0x00001f04 movl         %r8d, %ecx
	0xf7, 0xd1, //0x00001f07 notl         %ecx
	0x21, 0xd1, //0x00001f09 andl         %edx, %ecx
	0x44, 0x8d, 0x24, 0x09, //0x00001f0b leal         (%rcx,%rcx), %r12d
	0x45, 0x09, 0xc4, //0x00001f0f orl          %r8d, %r12d
	0xbb, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001f12 movl         $2863311530, %ebx
	0x44, 0x31, 0xe3, //0x00001f17 xorl         %r12d, %ebx
	0x21, 0xd3, //0x00001f1a andl         %edx, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001f1c andl         $-1431655766, %ebx
	0x45, 0x31, 0xc0, //0x00001f22 xorl         %r8d, %r8d
	0x01, 0xcb, //0x00001f25 addl         %ecx, %ebx
	0x41, 0x0f, 0x92, 0xc0, //0x00001f27 setb         %r8b
	0x01, 0xdb, //0x00001f2b addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x00001f2d xorl         $1431655765, %ebx
	0x44, 0x21, 0xe3, //0x00001f33 andl         %r12d, %ebx
	0x4c, 0x8b, 0x65, 0xd0, //0x00001f36 movq         $-48(%rbp), %r12
	0xf7, 0xd3, //0x00001f3a notl         %ebx
	0x21, 0xde, //0x00001f3c andl         %ebx, %esi
	0xe9, 0xf6, 0xf8, 0xff, 0xff, //0x00001f3e jmp          LBB0_117
	//0x00001f43 LBB0_363
	0x4c, 0x89, 0xd8, //0x00001f43 movq         %r11, %rax
	0x48, 0x2b, 0x45, 0xd0, //0x00001f46 subq         $-48(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xcf, //0x00001f4a bsfq         %rdi, %r9
	0x49, 0x01, 0xc1, //0x00001f4e addq         %rax, %r9
	0x48, 0x09, 0xca, //0x00001f51 orq          %rcx, %rdx
	0x48, 0x89, 0xf8, //0x00001f54 movq         %rdi, %rax
	0x4c, 0x09, 0xe0, //0x00001f57 orq          %r12, %rax
	0x0f, 0x84, 0x36, 0xf8, 0xff, 0xff, //0x00001f5a je           LBB0_53
	//0x00001f60 LBB0_364
	0x44, 0x89, 0xe0, //0x00001f60 movl         %r12d, %eax
	0xf7, 0xd0, //0x00001f63 notl         %eax
	0x21, 0xf8, //0x00001f65 andl         %edi, %eax
	0x8d, 0x0c, 0x00, //0x00001f67 leal         (%rax,%rax), %ecx
	0x44, 0x09, 0xe1, //0x00001f6a orl          %r12d, %ecx
	0xbe, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001f6d movl         $2863311530, %esi
	0x31, 0xce, //0x00001f72 xorl         %ecx, %esi
	0x21, 0xfe, //0x00001f74 andl         %edi, %esi
	0x81, 0xe6, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001f76 andl         $-1431655766, %esi
	0x45, 0x31, 0xe4, //0x00001f7c xorl         %r12d, %r12d
	0x01, 0xc6, //0x00001f7f addl         %eax, %esi
	0x41, 0x0f, 0x92, 0xc4, //0x00001f81 setb         %r12b
	0x01, 0xf6, //0x00001f85 addl         %esi, %esi
	0x81, 0xf6, 0x55, 0x55, 0x55, 0x55, //0x00001f87 xorl         $1431655765, %esi
	0x21, 0xce, //0x00001f8d andl         %ecx, %esi
	0xf7, 0xd6, //0x00001f8f notl         %esi
	0x21, 0xf2, //0x00001f91 andl         %esi, %edx
	0x48, 0x85, 0xd2, //0x00001f93 testq        %rdx, %rdx
	0x0f, 0x85, 0x03, 0xf8, 0xff, 0xff, //0x00001f96 jne          LBB0_54
	//0x00001f9c LBB0_365
	0x49, 0x83, 0xc3, 0x20, //0x00001f9c addq         $32, %r11
	0x49, 0x83, 0xc5, 0xe0, //0x00001fa0 addq         $-32, %r13
	//0x00001fa4 LBB0_366
	0x4d, 0x85, 0xe4, //0x00001fa4 testq        %r12, %r12
	0x0f, 0x85, 0xbe, 0x00, 0x00, 0x00, //0x00001fa7 jne          LBB0_375
	0x4c, 0x8b, 0x65, 0xd0, //0x00001fad movq         $-48(%rbp), %r12
	0x4d, 0x85, 0xed, //0x00001fb1 testq        %r13, %r13
	0x0f, 0x84, 0xa9, 0x03, 0x00, 0x00, //0x00001fb4 je           LBB0_416
	//0x00001fba LBB0_368
	0x49, 0x8d, 0x4b, 0x01, //0x00001fba leaq         $1(%r11), %rcx
	0x41, 0x0f, 0xb6, 0x1b, //0x00001fbe movzbl       (%r11), %ebx
	0x80, 0xfb, 0x22, //0x00001fc2 cmpb         $34, %bl
	0x0f, 0x84, 0x97, 0xfe, 0xff, 0xff, //0x00001fc5 je           LBB0_407
	0x49, 0x8d, 0x55, 0xff, //0x00001fcb leaq         $-1(%r13), %rdx
	0x80, 0xfb, 0x5c, //0x00001fcf cmpb         $92, %bl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00001fd2 je           LBB0_371
	0x49, 0x89, 0xd5, //0x00001fd8 movq         %rdx, %r13
	0x49, 0x89, 0xcb, //0x00001fdb movq         %rcx, %r11
	0x48, 0x85, 0xd2, //0x00001fde testq        %rdx, %rdx
	0x0f, 0x85, 0xd3, 0xff, 0xff, 0xff, //0x00001fe1 jne          LBB0_368
	0xe9, 0x77, 0x03, 0x00, 0x00, //0x00001fe7 jmp          LBB0_416
	//0x00001fec LBB0_371
	0x48, 0x85, 0xd2, //0x00001fec testq        %rdx, %rdx
	0x0f, 0x84, 0x6e, 0x03, 0x00, 0x00, //0x00001fef je           LBB0_416
	0x48, 0x03, 0x4d, 0xa0, //0x00001ff5 addq         $-96(%rbp), %rcx
	0x49, 0x83, 0xf9, 0xff, //0x00001ff9 cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc9, //0x00001ffd cmoveq       %rcx, %r9
	0x49, 0x83, 0xc3, 0x02, //0x00002001 addq         $2, %r11
	0x49, 0x83, 0xc5, 0xfe, //0x00002005 addq         $-2, %r13
	0x4c, 0x89, 0xea, //0x00002009 movq         %r13, %rdx
	0x4c, 0x8b, 0x65, 0xd0, //0x0000200c movq         $-48(%rbp), %r12
	0x48, 0x85, 0xd2, //0x00002010 testq        %rdx, %rdx
	0x0f, 0x85, 0xa1, 0xff, 0xff, 0xff, //0x00002013 jne          LBB0_368
	0xe9, 0x45, 0x03, 0x00, 0x00, //0x00002019 jmp          LBB0_416
	//0x0000201e LBB0_373
	0x4c, 0x89, 0xdb, //0x0000201e movq         %r11, %rbx
	0x4c, 0x29, 0xe3, //0x00002021 subq         %r12, %rbx
	0x4c, 0x0f, 0xbc, 0xca, //0x00002024 bsfq         %rdx, %r9
	0x49, 0x01, 0xd9, //0x00002028 addq         %rbx, %r9
	0xe9, 0xf6, 0xf7, 0xff, 0xff, //0x0000202b jmp          LBB0_116
	//0x00002030 LBB0_374
	0x44, 0x89, 0xc9, //0x00002030 movl         %r9d, %ecx
	0xf7, 0xd1, //0x00002033 notl         %ecx
	0x21, 0xd1, //0x00002035 andl         %edx, %ecx
	0x44, 0x8d, 0x04, 0x09, //0x00002037 leal         (%rcx,%rcx), %r8d
	0x45, 0x09, 0xc8, //0x0000203b orl          %r9d, %r8d
	0xbb, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000203e movl         $2863311530, %ebx
	0x44, 0x31, 0xc3, //0x00002043 xorl         %r8d, %ebx
	0x21, 0xd3, //0x00002046 andl         %edx, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002048 andl         $-1431655766, %ebx
	0x45, 0x31, 0xc9, //0x0000204e xorl         %r9d, %r9d
	0x01, 0xcb, //0x00002051 addl         %ecx, %ebx
	0x41, 0x0f, 0x92, 0xc1, //0x00002053 setb         %r9b
	0x01, 0xdb, //0x00002057 addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x00002059 xorl         $1431655765, %ebx
	0x44, 0x21, 0xc3, //0x0000205f andl         %r8d, %ebx
	0xf7, 0xd3, //0x00002062 notl         %ebx
	0x21, 0xde, //0x00002064 andl         %ebx, %esi
	0xe9, 0x60, 0xf9, 0xff, 0xff, //0x00002066 jmp          LBB0_174
	//0x0000206b LBB0_375
	0x4d, 0x85, 0xed, //0x0000206b testq        %r13, %r13
	0x0f, 0x84, 0xef, 0x02, 0x00, 0x00, //0x0000206e je           LBB0_416
	0x48, 0x8b, 0x45, 0xa0, //0x00002074 movq         $-96(%rbp), %rax
	0x4c, 0x01, 0xd8, //0x00002078 addq         %r11, %rax
	0x49, 0x83, 0xf9, 0xff, //0x0000207b cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc8, //0x0000207f cmoveq       %rax, %r9
	0x49, 0xff, 0xc3, //0x00002083 incq         %r11
	0x49, 0xff, 0xcd, //0x00002086 decq         %r13
	0x4c, 0x8b, 0x65, 0xd0, //0x00002089 movq         $-48(%rbp), %r12
	0x4d, 0x85, 0xed, //0x0000208d testq        %r13, %r13
	0x0f, 0x85, 0x24, 0xff, 0xff, 0xff, //0x00002090 jne          LBB0_368
	0xe9, 0xc8, 0x02, 0x00, 0x00, //0x00002096 jmp          LBB0_416
	//0x0000209b LBB0_377
	0x48, 0x85, 0xc0, //0x0000209b testq        %rax, %rax
	0x0f, 0x84, 0xbf, 0x02, 0x00, 0x00, //0x0000209e je           LBB0_416
	0x48, 0x8b, 0x4d, 0xa0, //0x000020a4 movq         $-96(%rbp), %rcx
	0x4c, 0x01, 0xd9, //0x000020a8 addq         %r11, %rcx
	0x49, 0x83, 0xf9, 0xff, //0x000020ab cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc9, //0x000020af cmoveq       %rcx, %r9
	0x49, 0xff, 0xc3, //0x000020b3 incq         %r11
	0x48, 0xff, 0xc8, //0x000020b6 decq         %rax
	0x4c, 0x8b, 0x65, 0xd0, //0x000020b9 movq         $-48(%rbp), %r12
	0x48, 0x85, 0xc0, //0x000020bd testq        %rax, %rax
	0x0f, 0x85, 0x6f, 0xf9, 0xff, 0xff, //0x000020c0 jne          LBB0_319
	0xe9, 0x98, 0x02, 0x00, 0x00, //0x000020c6 jmp          LBB0_416
	//0x000020cb LBB0_379
	0x4c, 0x89, 0xd8, //0x000020cb movq         %r11, %rax
	0x48, 0x2b, 0x45, 0xd0, //0x000020ce subq         $-48(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xe7, //0x000020d2 bsfq         %rdi, %r12
	0x49, 0x01, 0xc4, //0x000020d6 addq         %rax, %r12
	0x48, 0x09, 0xca, //0x000020d9 orq          %rcx, %rdx
	0x48, 0x89, 0xf8, //0x000020dc movq         %rdi, %rax
	0x4c, 0x09, 0xc8, //0x000020df orq          %r9, %rax
	0x0f, 0x84, 0x45, 0xf8, 0xff, 0xff, //0x000020e2 je           LBB0_138
	//0x000020e8 LBB0_380
	0x44, 0x89, 0xc8, //0x000020e8 movl         %r9d, %eax
	0xf7, 0xd0, //0x000020eb notl         %eax
	0x21, 0xf8, //0x000020ed andl         %edi, %eax
	0x8d, 0x0c, 0x00, //0x000020ef leal         (%rax,%rax), %ecx
	0x44, 0x09, 0xc9, //0x000020f2 orl          %r9d, %ecx
	0xbe, 0xaa, 0xaa, 0xaa, 0xaa, //0x000020f5 movl         $2863311530, %esi
	0x31, 0xce, //0x000020fa xorl         %ecx, %esi
	0x21, 0xfe, //0x000020fc andl         %edi, %esi
	0x81, 0xe6, 0xaa, 0xaa, 0xaa, 0xaa, //0x000020fe andl         $-1431655766, %esi
	0x45, 0x31, 0xc9, //0x00002104 xorl         %r9d, %r9d
	0x01, 0xc6, //0x00002107 addl         %eax, %esi
	0x41, 0x0f, 0x92, 0xc1, //0x00002109 setb         %r9b
	0x01, 0xf6, //0x0000210d addl         %esi, %esi
	0x81, 0xf6, 0x55, 0x55, 0x55, 0x55, //0x0000210f xorl         $1431655765, %esi
	0x21, 0xce, //0x00002115 andl         %ecx, %esi
	0xf7, 0xd6, //0x00002117 notl         %esi
	0x21, 0xf2, //0x00002119 andl         %esi, %edx
	0x48, 0x85, 0xd2, //0x0000211b testq        %rdx, %rdx
	0x0f, 0x85, 0x12, 0xf8, 0xff, 0xff, //0x0000211e jne          LBB0_139
	//0x00002124 LBB0_381
	0x49, 0x83, 0xc3, 0x20, //0x00002124 addq         $32, %r11
	0x49, 0x83, 0xc5, 0xe0, //0x00002128 addq         $-32, %r13
	//0x0000212c LBB0_382
	0x4d, 0x85, 0xc9, //0x0000212c testq        %r9, %r9
	0x0f, 0x85, 0x98, 0x00, 0x00, 0x00, //0x0000212f jne          LBB0_391
	0x48, 0x8b, 0x75, 0xc8, //0x00002135 movq         $-56(%rbp), %rsi
	0x4c, 0x8b, 0x4d, 0xc0, //0x00002139 movq         $-64(%rbp), %r9
	0x4d, 0x85, 0xed, //0x0000213d testq        %r13, %r13
	0x0f, 0x84, 0x65, 0x02, 0x00, 0x00, //0x00002140 je           LBB0_425
	//0x00002146 LBB0_384
	0x49, 0x8d, 0x4b, 0x01, //0x00002146 leaq         $1(%r11), %rcx
	0x41, 0x0f, 0xb6, 0x1b, //0x0000214a movzbl       (%r11), %ebx
	0x80, 0xfb, 0x22, //0x0000214e cmpb         $34, %bl
	0x0f, 0x84, 0x57, 0x00, 0x00, 0x00, //0x00002151 je           LBB0_389
	0x49, 0x8d, 0x55, 0xff, //0x00002157 leaq         $-1(%r13), %rdx
	0x80, 0xfb, 0x5c, //0x0000215b cmpb         $92, %bl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x0000215e je           LBB0_387
	0x49, 0x89, 0xd5, //0x00002164 movq         %rdx, %r13
	0x49, 0x89, 0xcb, //0x00002167 movq         %rcx, %r11
	0x48, 0x85, 0xd2, //0x0000216a testq        %rdx, %rdx
	0x0f, 0x85, 0xd3, 0xff, 0xff, 0xff, //0x0000216d jne          LBB0_384
	0xe9, 0x33, 0x02, 0x00, 0x00, //0x00002173 jmp          LBB0_425
	//0x00002178 LBB0_387
	0x48, 0x85, 0xd2, //0x00002178 testq        %rdx, %rdx
	0x0f, 0x84, 0x38, 0x04, 0x00, 0x00, //0x0000217b je           LBB0_462
	0x48, 0x03, 0x4d, 0xa0, //0x00002181 addq         $-96(%rbp), %rcx
	0x49, 0x83, 0xfc, 0xff, //0x00002185 cmpq         $-1, %r12
	0x4c, 0x0f, 0x44, 0xe1, //0x00002189 cmoveq       %rcx, %r12
	0x49, 0x83, 0xc3, 0x02, //0x0000218d addq         $2, %r11
	0x49, 0x83, 0xc5, 0xfe, //0x00002191 addq         $-2, %r13
	0x4c, 0x89, 0xea, //0x00002195 movq         %r13, %rdx
	0x48, 0x8b, 0x75, 0xc8, //0x00002198 movq         $-56(%rbp), %rsi
	0x4c, 0x8b, 0x4d, 0xc0, //0x0000219c movq         $-64(%rbp), %r9
	0x48, 0x85, 0xd2, //0x000021a0 testq        %rdx, %rdx
	0x0f, 0x85, 0x9d, 0xff, 0xff, 0xff, //0x000021a3 jne          LBB0_384
	0xe9, 0xfd, 0x01, 0x00, 0x00, //0x000021a9 jmp          LBB0_425
	//0x000021ae LBB0_389
	0x48, 0x2b, 0x4d, 0xd0, //0x000021ae subq         $-48(%rbp), %rcx
	0x49, 0x89, 0xcb, //0x000021b2 movq         %rcx, %r11
	0xe9, 0x86, 0xed, 0xff, 0xff, //0x000021b5 jmp          LBB0_195
	//0x000021ba LBB0_390
	0x4c, 0x89, 0xd9, //0x000021ba movq         %r11, %rcx
	0x48, 0x2b, 0x4d, 0xd0, //0x000021bd subq         $-48(%rbp), %rcx
	0x4c, 0x0f, 0xbc, 0xe2, //0x000021c1 bsfq         %rdx, %r12
	0x49, 0x01, 0xcc, //0x000021c5 addq         %rcx, %r12
	0xe9, 0xeb, 0xf7, 0xff, 0xff, //0x000021c8 jmp          LBB0_173
	//0x000021cd LBB0_391
	0x4d, 0x85, 0xed, //0x000021cd testq        %r13, %r13
	0x0f, 0x84, 0xe3, 0x03, 0x00, 0x00, //0x000021d0 je           LBB0_462
	0x48, 0x8b, 0x45, 0xa0, //0x000021d6 movq         $-96(%rbp), %rax
	0x4c, 0x01, 0xd8, //0x000021da addq         %r11, %rax
	0x49, 0x83, 0xfc, 0xff, //0x000021dd cmpq         $-1, %r12
	0x4c, 0x0f, 0x44, 0xe0, //0x000021e1 cmoveq       %rax, %r12
	0x49, 0xff, 0xc3, //0x000021e5 incq         %r11
	0x49, 0xff, 0xcd, //0x000021e8 decq         %r13
	0x48, 0x8b, 0x75, 0xc8, //0x000021eb movq         $-56(%rbp), %rsi
	0x4c, 0x8b, 0x4d, 0xc0, //0x000021ef movq         $-64(%rbp), %r9
	0x4d, 0x85, 0xed, //0x000021f3 testq        %r13, %r13
	0x0f, 0x85, 0x4a, 0xff, 0xff, 0xff, //0x000021f6 jne          LBB0_384
	0xe9, 0xaa, 0x01, 0x00, 0x00, //0x000021fc jmp          LBB0_425
	//0x00002201 LBB0_393
	0x48, 0x85, 0xc0, //0x00002201 testq        %rax, %rax
	0x0f, 0x84, 0xaf, 0x03, 0x00, 0x00, //0x00002204 je           LBB0_462
	0x48, 0x8b, 0x4d, 0xa0, //0x0000220a movq         $-96(%rbp), %rcx
	0x4c, 0x01, 0xd9, //0x0000220e addq         %r11, %rcx
	0x49, 0x83, 0xfc, 0xff, //0x00002211 cmpq         $-1, %r12
	0x4c, 0x0f, 0x44, 0xe1, //0x00002215 cmoveq       %rcx, %r12
	0x49, 0xff, 0xc3, //0x00002219 incq         %r11
	0x48, 0xff, 0xc8, //0x0000221c decq         %rax
	0x48, 0x8b, 0x75, 0xc8, //0x0000221f movq         $-56(%rbp), %rsi
	0x4c, 0x8b, 0x4d, 0xc0, //0x00002223 movq         $-64(%rbp), %r9
	0x48, 0x85, 0xc0, //0x00002227 testq        %rax, %rax
	0x0f, 0x85, 0xb4, 0xf8, 0xff, 0xff, //0x0000222a jne          LBB0_331
	0xe9, 0x76, 0x01, 0x00, 0x00, //0x00002230 jmp          LBB0_425
	//0x00002235 LBB0_395
	0x44, 0x89, 0xc1, //0x00002235 movl         %r8d, %ecx
	0xf7, 0xd1, //0x00002238 notl         %ecx
	0x21, 0xd1, //0x0000223a andl         %edx, %ecx
	0x44, 0x8d, 0x14, 0x09, //0x0000223c leal         (%rcx,%rcx), %r10d
	0x45, 0x09, 0xc2, //0x00002240 orl          %r8d, %r10d
	0xbb, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002243 movl         $2863311530, %ebx
	0x44, 0x31, 0xd3, //0x00002248 xorl         %r10d, %ebx
	0x21, 0xd3, //0x0000224b andl         %edx, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000224d andl         $-1431655766, %ebx
	0x45, 0x31, 0xc0, //0x00002253 xorl         %r8d, %r8d
	0x01, 0xcb, //0x00002256 addl         %ecx, %ebx
	0x41, 0x0f, 0x92, 0xc0, //0x00002258 setb         %r8b
	0x01, 0xdb, //0x0000225c addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x0000225e xorl         $1431655765, %ebx
	0x44, 0x21, 0xd3, //0x00002264 andl         %r10d, %ebx
	0xf7, 0xd3, //0x00002267 notl         %ebx
	0x21, 0xde, //0x00002269 andl         %ebx, %esi
	0xe9, 0xae, 0xfa, 0xff, 0xff, //0x0000226b jmp          LBB0_281
	//0x00002270 LBB0_396
	0x4c, 0x89, 0xd8, //0x00002270 movq         %r11, %rax
	0x48, 0x2b, 0x45, 0xd0, //0x00002273 subq         $-48(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xcf, //0x00002277 bsfq         %rdi, %r9
	0x49, 0x01, 0xc1, //0x0000227b addq         %rax, %r9
	0x48, 0x09, 0xca, //0x0000227e orq          %rcx, %rdx
	0x48, 0x89, 0xf8, //0x00002281 movq         %rdi, %rax
	0x4c, 0x09, 0xd0, //0x00002284 orq          %r10, %rax
	0x0f, 0x84, 0x7d, 0xf9, 0xff, 0xff, //0x00002287 je           LBB0_212
	//0x0000228d LBB0_397
	0x44, 0x89, 0xd0, //0x0000228d movl         %r10d, %eax
	0xf7, 0xd0, //0x00002290 notl         %eax
	0x21, 0xf8, //0x00002292 andl         %edi, %eax
	0x8d, 0x0c, 0x00, //0x00002294 leal         (%rax,%rax), %ecx
	0x44, 0x09, 0xd1, //0x00002297 orl          %r10d, %ecx
	0x89, 0xce, //0x0000229a movl         %ecx, %esi
	0xbb, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000229c movl         $2863311530, %ebx
	0x31, 0xde, //0x000022a1 xorl         %ebx, %esi
	0x21, 0xfe, //0x000022a3 andl         %edi, %esi
	0x81, 0xe6, 0xaa, 0xaa, 0xaa, 0xaa, //0x000022a5 andl         $-1431655766, %esi
	0x45, 0x31, 0xd2, //0x000022ab xorl         %r10d, %r10d
	0x01, 0xc6, //0x000022ae addl         %eax, %esi
	0x41, 0x0f, 0x92, 0xc2, //0x000022b0 setb         %r10b
	0x01, 0xf6, //0x000022b4 addl         %esi, %esi
	0x81, 0xf6, 0x55, 0x55, 0x55, 0x55, //0x000022b6 xorl         $1431655765, %esi
	0x21, 0xce, //0x000022bc andl         %ecx, %esi
	0xf7, 0xd6, //0x000022be notl         %esi
	0x21, 0xf2, //0x000022c0 andl         %esi, %edx
	0x48, 0x85, 0xd2, //0x000022c2 testq        %rdx, %rdx
	0x0f, 0x85, 0xd4, 0xf4, 0xff, 0xff, //0x000022c5 jne          LBB0_54
	0xe9, 0x43, 0xf9, 0xff, 0xff, //0x000022cb jmp          LBB0_398
	//0x000022d0 LBB0_408
	0x4c, 0x89, 0xdb, //0x000022d0 movq         %r11, %rbx
	0x4c, 0x29, 0xe3, //0x000022d3 subq         %r12, %rbx
	0x4c, 0x0f, 0xbc, 0xca, //0x000022d6 bsfq         %rdx, %r9
	0x49, 0x01, 0xd9, //0x000022da addq         %rbx, %r9
	0xe9, 0x29, 0xfa, 0xff, 0xff, //0x000022dd jmp          LBB0_280
	//0x000022e2 LBB0_411
	0x4d, 0x85, 0xe4, //0x000022e2 testq        %r12, %r12
	0x0f, 0x84, 0x78, 0x00, 0x00, 0x00, //0x000022e5 je           LBB0_416
	0x48, 0x8b, 0x45, 0xa0, //0x000022eb movq         $-96(%rbp), %rax
	0x4c, 0x01, 0xd8, //0x000022ef addq         %r11, %rax
	0x49, 0x83, 0xf9, 0xff, //0x000022f2 cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc8, //0x000022f6 cmoveq       %rax, %r9
	0x49, 0xff, 0xc3, //0x000022fa incq         %r11
	0x49, 0xff, 0xcc, //0x000022fd decq         %r12
	0x4d, 0x85, 0xe4, //0x00002300 testq        %r12, %r12
	0x0f, 0x85, 0x24, 0xf9, 0xff, 0xff, //0x00002303 jne          LBB0_401
	0xe9, 0x55, 0x00, 0x00, 0x00, //0x00002309 jmp          LBB0_416
	//0x0000230e LBB0_409
	0x48, 0x85, 0xc0, //0x0000230e testq        %rax, %rax
	0x0f, 0x84, 0x4c, 0x00, 0x00, 0x00, //0x00002311 je           LBB0_416
	0x48, 0x8b, 0x4d, 0xa0, //0x00002317 movq         $-96(%rbp), %rcx
	0x4c, 0x01, 0xd9, //0x0000231b addq         %r11, %rcx
	0x49, 0x83, 0xf9, 0xff, //0x0000231e cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc9, //0x00002322 cmoveq       %rcx, %r9
	0x49, 0xff, 0xc3, //0x00002326 incq         %r11
	0x48, 0xff, 0xc8, //0x00002329 decq         %rax
	0x4c, 0x8b, 0x65, 0xd0, //0x0000232c movq         $-48(%rbp), %r12
	0x48, 0x85, 0xc0, //0x00002330 testq        %rax, %rax
	0x0f, 0x85, 0x38, 0xfa, 0xff, 0xff, //0x00002333 jne          LBB0_347
	0xe9, 0x25, 0x00, 0x00, 0x00, //0x00002339 jmp          LBB0_416
	//0x0000233e LBB0_413
	0x48, 0x89, 0x16, //0x0000233e movq         %rdx, (%rsi)
	//0x00002341 LBB0_414
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002341 movq         $-1, %rax
	0xe9, 0x45, 0x00, 0x00, 0x00, //0x00002348 jmp          LBB0_423
	//0x0000234d LBB0_441
	0x48, 0xc7, 0xc0, 0xf9, 0xff, 0xff, 0xff, //0x0000234d movq         $-7, %rax
	0xe9, 0x39, 0x00, 0x00, 0x00, //0x00002354 jmp          LBB0_423
	//0x00002359 LBB0_415
	0x49, 0x83, 0xfb, 0xff, //0x00002359 cmpq         $-1, %r11
	0x0f, 0x85, 0x7f, 0x00, 0x00, 0x00, //0x0000235d jne          LBB0_307
	//0x00002363 LBB0_416
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00002363 movq         $-1, %r11
	0x4c, 0x8b, 0x4d, 0xb0, //0x0000236a movq         $-80(%rbp), %r9
	0xe9, 0x6f, 0x00, 0x00, 0x00, //0x0000236e jmp          LBB0_307
	//0x00002373 LBB0_417
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002373 movq         $-1, %rax
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x0000237a jmp          LBB0_420
	//0x0000237f LBB0_419
	0x4c, 0x89, 0xd8, //0x0000237f movq         %r11, %rax
	//0x00002382 LBB0_420
	0x48, 0xf7, 0xd0, //0x00002382 notq         %rax
	0x49, 0x01, 0xc7, //0x00002385 addq         %rax, %r15
	//0x00002388 LBB0_421
	0x4c, 0x89, 0x3e, //0x00002388 movq         %r15, (%rsi)
	//0x0000238b LBB0_422
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x0000238b movq         $-2, %rax
	//0x00002392 LBB0_423
	0x48, 0x83, 0xc4, 0x68, //0x00002392 addq         $104, %rsp
	0x5b, //0x00002396 popq         %rbx
	0x41, 0x5c, //0x00002397 popq         %r12
	0x41, 0x5d, //0x00002399 popq         %r13
	0x41, 0x5e, //0x0000239b popq         %r14
	0x41, 0x5f, //0x0000239d popq         %r15
	0x5d, //0x0000239f popq         %rbp
	0xc3, //0x000023a0 retq         
	//0x000023a1 LBB0_424
	0x49, 0x83, 0xfb, 0xff, //0x000023a1 cmpq         $-1, %r11
	0x0f, 0x85, 0x69, 0x00, 0x00, 0x00, //0x000023a5 jne          LBB0_437
	//0x000023ab LBB0_425
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000023ab movq         $-1, %r11
	0x4c, 0x8b, 0x65, 0xb0, //0x000023b2 movq         $-80(%rbp), %r12
	0xe9, 0x59, 0x00, 0x00, 0x00, //0x000023b6 jmp          LBB0_437
	//0x000023bb LBB0_440
	0x48, 0x89, 0x0e, //0x000023bb movq         %rcx, (%rsi)
	0xe9, 0xcf, 0xff, 0xff, 0xff, //0x000023be jmp          LBB0_423
	//0x000023c3 LBB0_426
	0x49, 0x83, 0xf9, 0xff, //0x000023c3 cmpq         $-1, %r9
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x000023c7 jne          LBB0_431
	0x49, 0x0f, 0xbc, 0xc4, //0x000023cd bsfq         %r12, %rax
	//0x000023d1 LBB0_428
	0x4c, 0x2b, 0x5d, 0xd0, //0x000023d1 subq         $-48(%rbp), %r11
	//0x000023d5 LBB0_429
	0x49, 0x01, 0xc3, //0x000023d5 addq         %rax, %r11
	//0x000023d8 LBB0_430
	0x4d, 0x89, 0xd9, //0x000023d8 movq         %r11, %r9
	//0x000023db LBB0_431
	0x49, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x000023db movq         $-2, %r11
	//0x000023e2 LBB0_307
	0x48, 0x8b, 0x45, 0xc8, //0x000023e2 movq         $-56(%rbp), %rax
	0x4c, 0x89, 0x08, //0x000023e6 movq         %r9, (%rax)
	0x4c, 0x89, 0xd8, //0x000023e9 movq         %r11, %rax
	0xe9, 0xa1, 0xff, 0xff, 0xff, //0x000023ec jmp          LBB0_423
	//0x000023f1 LBB0_432
	0x49, 0x83, 0xfc, 0xff, //0x000023f1 cmpq         $-1, %r12
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x000023f5 jne          LBB0_435
	0x48, 0x0f, 0xbc, 0xc2, //0x000023fb bsfq         %rdx, %rax
	//0x000023ff LBB0_434
	0x4c, 0x2b, 0x5d, 0xd0, //0x000023ff subq         $-48(%rbp), %r11
	0x49, 0x01, 0xc3, //0x00002403 addq         %rax, %r11
	0x4d, 0x89, 0xdc, //0x00002406 movq         %r11, %r12
	//0x00002409 LBB0_435
	0x49, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x00002409 movq         $-2, %r11
	//0x00002410 LBB0_436
	0x48, 0x8b, 0x75, 0xc8, //0x00002410 movq         $-56(%rbp), %rsi
	//0x00002414 LBB0_437
	0x4c, 0x89, 0x26, //0x00002414 movq         %r12, (%rsi)
	0x4c, 0x89, 0xd8, //0x00002417 movq         %r11, %rax
	0xe9, 0x73, 0xff, 0xff, 0xff, //0x0000241a jmp          LBB0_423
	//0x0000241f LBB0_438
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x0000241f movq         $-1, %r11
	//0x00002426 LBB0_439
	0x4d, 0x29, 0xdf, //0x00002426 subq         %r11, %r15
	0xe9, 0x5a, 0xff, 0xff, 0xff, //0x00002429 jmp          LBB0_421
	//0x0000242e LBB0_442
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x0000242e movq         $-2, %rax
	0x80, 0xf9, 0x61, //0x00002435 cmpb         $97, %cl
	0x0f, 0x85, 0x54, 0xff, 0xff, 0xff, //0x00002438 jne          LBB0_423
	0x49, 0x8d, 0x4f, 0x02, //0x0000243e leaq         $2(%r15), %rcx
	0x48, 0x89, 0x0e, //0x00002442 movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x02, 0x6c, //0x00002445 cmpb         $108, $2(%r12,%r15)
	0x0f, 0x85, 0x41, 0xff, 0xff, 0xff, //0x0000244b jne          LBB0_423
	0x49, 0x8d, 0x4f, 0x03, //0x00002451 leaq         $3(%r15), %rcx
	0x48, 0x89, 0x0e, //0x00002455 movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x03, 0x73, //0x00002458 cmpb         $115, $3(%r12,%r15)
	0x0f, 0x85, 0x2e, 0xff, 0xff, 0xff, //0x0000245e jne          LBB0_423
	0x49, 0x8d, 0x4f, 0x04, //0x00002464 leaq         $4(%r15), %rcx
	0x48, 0x89, 0x0e, //0x00002468 movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x04, 0x65, //0x0000246b cmpb         $101, $4(%r12,%r15)
	0x0f, 0x85, 0x1b, 0xff, 0xff, 0xff, //0x00002471 jne          LBB0_423
	0x49, 0x83, 0xc7, 0x05, //0x00002477 addq         $5, %r15
	0x4c, 0x89, 0x3e, //0x0000247b movq         %r15, (%rsi)
	0xe9, 0x0f, 0xff, 0xff, 0xff, //0x0000247e jmp          LBB0_423
	//0x00002483 LBB0_247
	0x4c, 0x89, 0x3e, //0x00002483 movq         %r15, (%rsi)
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00002486 movq         $-2, %rax
	0x41, 0x80, 0x3a, 0x6e, //0x0000248d cmpb         $110, (%r10)
	0x0f, 0x85, 0xfb, 0xfe, 0xff, 0xff, //0x00002491 jne          LBB0_423
	0x49, 0x8d, 0x4f, 0x01, //0x00002497 leaq         $1(%r15), %rcx
	0x48, 0x89, 0x0e, //0x0000249b movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x01, 0x75, //0x0000249e cmpb         $117, $1(%r12,%r15)
	0x0f, 0x85, 0xe8, 0xfe, 0xff, 0xff, //0x000024a4 jne          LBB0_423
	0x49, 0x8d, 0x4f, 0x02, //0x000024aa leaq         $2(%r15), %rcx
	0x48, 0x89, 0x0e, //0x000024ae movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x02, 0x6c, //0x000024b1 cmpb         $108, $2(%r12,%r15)
	0x0f, 0x85, 0xd5, 0xfe, 0xff, 0xff, //0x000024b7 jne          LBB0_423
	0x49, 0x8d, 0x4f, 0x03, //0x000024bd leaq         $3(%r15), %rcx
	0x48, 0x89, 0x0e, //0x000024c1 movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x03, 0x6c, //0x000024c4 cmpb         $108, $3(%r12,%r15)
	0x0f, 0x85, 0xc2, 0xfe, 0xff, 0xff, //0x000024ca jne          LBB0_423
	0xe9, 0x4d, 0x00, 0x00, 0x00, //0x000024d0 jmp          LBB0_451
	//0x000024d5 LBB0_447
	0x4c, 0x89, 0x3e, //0x000024d5 movq         %r15, (%rsi)
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x000024d8 movq         $-2, %rax
	0x41, 0x80, 0x3a, 0x74, //0x000024df cmpb         $116, (%r10)
	0x0f, 0x85, 0xa9, 0xfe, 0xff, 0xff, //0x000024e3 jne          LBB0_423
	0x49, 0x8d, 0x4f, 0x01, //0x000024e9 leaq         $1(%r15), %rcx
	0x48, 0x89, 0x0e, //0x000024ed movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x01, 0x72, //0x000024f0 cmpb         $114, $1(%r12,%r15)
	0x0f, 0x85, 0x96, 0xfe, 0xff, 0xff, //0x000024f6 jne          LBB0_423
	0x49, 0x8d, 0x4f, 0x02, //0x000024fc leaq         $2(%r15), %rcx
	0x48, 0x89, 0x0e, //0x00002500 movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x02, 0x75, //0x00002503 cmpb         $117, $2(%r12,%r15)
	0x0f, 0x85, 0x83, 0xfe, 0xff, 0xff, //0x00002509 jne          LBB0_423
	0x49, 0x8d, 0x4f, 0x03, //0x0000250f leaq         $3(%r15), %rcx
	0x48, 0x89, 0x0e, //0x00002513 movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x03, 0x65, //0x00002516 cmpb         $101, $3(%r12,%r15)
	0x0f, 0x85, 0x70, 0xfe, 0xff, 0xff, //0x0000251c jne          LBB0_423
	//0x00002522 LBB0_451
	0x49, 0x83, 0xc7, 0x04, //0x00002522 addq         $4, %r15
	0x4c, 0x89, 0x3e, //0x00002526 movq         %r15, (%rsi)
	0xe9, 0x64, 0xfe, 0xff, 0xff, //0x00002529 jmp          LBB0_423
	//0x0000252e LBB0_452
	0x49, 0x83, 0xf9, 0xff, //0x0000252e cmpq         $-1, %r9
	0x0f, 0x85, 0xa3, 0xfe, 0xff, 0xff, //0x00002532 jne          LBB0_431
	0x48, 0x0f, 0xbc, 0xc2, //0x00002538 bsfq         %rdx, %rax
	0xe9, 0x90, 0xfe, 0xff, 0xff, //0x0000253c jmp          LBB0_428
	//0x00002541 LBB0_456
	0x4c, 0x89, 0x5d, 0xb0, //0x00002541 movq         %r11, $-80(%rbp)
	0xe9, 0x19, 0xfe, 0xff, 0xff, //0x00002545 jmp          LBB0_416
	//0x0000254a LBB0_306
	0x4c, 0x01, 0xd9, //0x0000254a addq         %r11, %rcx
	0x49, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x0000254d movq         $-2, %r11
	0x49, 0x89, 0xc9, //0x00002554 movq         %rcx, %r9
	0xe9, 0x86, 0xfe, 0xff, 0xff, //0x00002557 jmp          LBB0_307
	//0x0000255c LBB0_454
	0x4c, 0x89, 0x5d, 0xb0, //0x0000255c movq         %r11, $-80(%rbp)
	0xe9, 0x46, 0xfe, 0xff, 0xff, //0x00002560 jmp          LBB0_425
	//0x00002565 LBB0_455
	0x4c, 0x01, 0xd9, //0x00002565 addq         %r11, %rcx
	0x49, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x00002568 movq         $-2, %r11
	0x49, 0x89, 0xcc, //0x0000256f movq         %rcx, %r12
	0xe9, 0x9d, 0xfe, 0xff, 0xff, //0x00002572 jmp          LBB0_437
	//0x00002577 LBB0_457
	0x48, 0x0f, 0xbc, 0xc7, //0x00002577 bsfq         %rdi, %rax
	0x4d, 0x29, 0xe3, //0x0000257b subq         %r12, %r11
	0xe9, 0x52, 0xfe, 0xff, 0xff, //0x0000257e jmp          LBB0_429
	//0x00002583 LBB0_461
	0x4d, 0x29, 0xe3, //0x00002583 subq         %r12, %r11
	0xe9, 0x4d, 0xfe, 0xff, 0xff, //0x00002586 jmp          LBB0_430
	//0x0000258b LBB0_458
	0x4c, 0x01, 0xd9, //0x0000258b addq         %r11, %rcx
	0x49, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x0000258e movq         $-2, %r11
	0x49, 0x89, 0xcc, //0x00002595 movq         %rcx, %r12
	0xe9, 0x73, 0xfe, 0xff, 0xff, //0x00002598 jmp          LBB0_436
	//0x0000259d LBB0_459
	0x48, 0x0f, 0xbc, 0xc7, //0x0000259d bsfq         %rdi, %rax
	0xe9, 0x59, 0xfe, 0xff, 0xff, //0x000025a1 jmp          LBB0_434
	//0x000025a6 LBB0_460
	0x4c, 0x2b, 0x5d, 0xd0, //0x000025a6 subq         $-48(%rbp), %r11
	0x4d, 0x89, 0xdc, //0x000025aa movq         %r11, %r12
	0x49, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x000025ad movq         $-2, %r11
	0xe9, 0x5b, 0xfe, 0xff, 0xff, //0x000025b4 jmp          LBB0_437
	//0x000025b9 LBB0_462
	0x48, 0x8b, 0x75, 0xc8, //0x000025b9 movq         $-56(%rbp), %rsi
	0xe9, 0xe9, 0xfd, 0xff, 0xff, //0x000025bd jmp          LBB0_425
	0x90, 0x90, //0x000025c2 .p2align 2, 0x90
	// // .set L0_0_set_33, LBB0_33-LJTI0_0
	// // .set L0_0_set_37, LBB0_37-LJTI0_0
	// // .set L0_0_set_39, LBB0_39-LJTI0_0
	// // .set L0_0_set_59, LBB0_59-LJTI0_0
	// // .set L0_0_set_61, LBB0_61-LJTI0_0
	// // .set L0_0_set_64, LBB0_64-LJTI0_0
	//0x000025c4 LJTI0_0
	0xc3, 0xdd, 0xff, 0xff, //0x000025c4 .long L0_0_set_33
	0xeb, 0xdd, 0xff, 0xff, //0x000025c8 .long L0_0_set_37
	0x16, 0xde, 0xff, 0xff, //0x000025cc .long L0_0_set_39
	0xcf, 0xdf, 0xff, 0xff, //0x000025d0 .long L0_0_set_59
	0xe5, 0xdf, 0xff, 0xff, //0x000025d4 .long L0_0_set_61
	0x55, 0xe2, 0xff, 0xff, //0x000025d8 .long L0_0_set_64
	// // .set L0_1_set_423, LBB0_423-LJTI0_1
	// // .set L0_1_set_422, LBB0_422-LJTI0_1
	// // .set L0_1_set_199, LBB0_199-LJTI0_1
	// // .set L0_1_set_215, LBB0_215-LJTI0_1
	// // .set L0_1_set_66, LBB0_66-LJTI0_1
	// // .set L0_1_set_240, LBB0_240-LJTI0_1
	// // .set L0_1_set_242, LBB0_242-LJTI0_1
	// // .set L0_1_set_245, LBB0_245-LJTI0_1
	// // .set L0_1_set_251, LBB0_251-LJTI0_1
	// // .set L0_1_set_255, LBB0_255-LJTI0_1
	//0x000025dc LJTI0_1
	0xb6, 0xfd, 0xff, 0xff, //0x000025dc .long L0_1_set_423
	0xaf, 0xfd, 0xff, 0xff, //0x000025e0 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000025e4 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000025e8 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000025ec .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000025f0 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000025f4 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000025f8 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000025fc .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002600 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002604 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002608 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000260c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002610 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002614 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002618 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000261c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002620 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002624 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002628 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000262c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002630 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002634 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002638 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000263c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002640 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002644 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002648 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000264c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002650 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002654 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002658 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000265c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002660 .long L0_1_set_422
	0xa6, 0xe9, 0xff, 0xff, //0x00002664 .long L0_1_set_199
	0xaf, 0xfd, 0xff, 0xff, //0x00002668 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000266c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002670 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002674 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002678 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000267c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002680 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002684 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002688 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000268c .long L0_1_set_422
	0x38, 0xeb, 0xff, 0xff, //0x00002690 .long L0_1_set_215
	0xaf, 0xfd, 0xff, 0xff, //0x00002694 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002698 .long L0_1_set_422
	0xfd, 0xdf, 0xff, 0xff, //0x0000269c .long L0_1_set_66
	0xfd, 0xdf, 0xff, 0xff, //0x000026a0 .long L0_1_set_66
	0xfd, 0xdf, 0xff, 0xff, //0x000026a4 .long L0_1_set_66
	0xfd, 0xdf, 0xff, 0xff, //0x000026a8 .long L0_1_set_66
	0xfd, 0xdf, 0xff, 0xff, //0x000026ac .long L0_1_set_66
	0xfd, 0xdf, 0xff, 0xff, //0x000026b0 .long L0_1_set_66
	0xfd, 0xdf, 0xff, 0xff, //0x000026b4 .long L0_1_set_66
	0xfd, 0xdf, 0xff, 0xff, //0x000026b8 .long L0_1_set_66
	0xfd, 0xdf, 0xff, 0xff, //0x000026bc .long L0_1_set_66
	0xfd, 0xdf, 0xff, 0xff, //0x000026c0 .long L0_1_set_66
	0xaf, 0xfd, 0xff, 0xff, //0x000026c4 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000026c8 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000026cc .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000026d0 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000026d4 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000026d8 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000026dc .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000026e0 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000026e4 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000026e8 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000026ec .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000026f0 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000026f4 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000026f8 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000026fc .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002700 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002704 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002708 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000270c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002710 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002714 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002718 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000271c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002720 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002724 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002728 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000272c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002730 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002734 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002738 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000273c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002740 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002744 .long L0_1_set_422
	0x0f, 0xed, 0xff, 0xff, //0x00002748 .long L0_1_set_240
	0xaf, 0xfd, 0xff, 0xff, //0x0000274c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002750 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002754 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002758 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000275c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002760 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002764 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002768 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000276c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002770 .long L0_1_set_422
	0x33, 0xed, 0xff, 0xff, //0x00002774 .long L0_1_set_242
	0xaf, 0xfd, 0xff, 0xff, //0x00002778 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000277c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002780 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002784 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002788 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000278c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002790 .long L0_1_set_422
	0x64, 0xed, 0xff, 0xff, //0x00002794 .long L0_1_set_245
	0xaf, 0xfd, 0xff, 0xff, //0x00002798 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000279c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000027a0 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000027a4 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000027a8 .long L0_1_set_422
	0x8b, 0xed, 0xff, 0xff, //0x000027ac .long L0_1_set_251
	0xaf, 0xfd, 0xff, 0xff, //0x000027b0 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000027b4 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000027b8 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000027bc .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000027c0 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000027c4 .long L0_1_set_422
	0xc8, 0xed, 0xff, 0xff, //0x000027c8 .long L0_1_set_255
	// // .set L0_2_set_264, LBB0_264-LJTI0_2
	// // .set L0_2_set_292, LBB0_292-LJTI0_2
	// // .set L0_2_set_259, LBB0_259-LJTI0_2
	// // .set L0_2_set_261, LBB0_261-LJTI0_2
	// // .set L0_2_set_266, LBB0_266-LJTI0_2
	//0x000027cc LJTI0_2
	0x3d, 0xec, 0xff, 0xff, //0x000027cc .long L0_2_set_264
	0x7b, 0xee, 0xff, 0xff, //0x000027d0 .long L0_2_set_292
	0x3d, 0xec, 0xff, 0xff, //0x000027d4 .long L0_2_set_264
	0xfc, 0xeb, 0xff, 0xff, //0x000027d8 .long L0_2_set_259
	0x7b, 0xee, 0xff, 0xff, //0x000027dc .long L0_2_set_292
	0x14, 0xec, 0xff, 0xff, //0x000027e0 .long L0_2_set_261
	0x14, 0xec, 0xff, 0xff, //0x000027e4 .long L0_2_set_261
	0x14, 0xec, 0xff, 0xff, //0x000027e8 .long L0_2_set_261
	0x14, 0xec, 0xff, 0xff, //0x000027ec .long L0_2_set_261
	0x14, 0xec, 0xff, 0xff, //0x000027f0 .long L0_2_set_261
	0x14, 0xec, 0xff, 0xff, //0x000027f4 .long L0_2_set_261
	0x14, 0xec, 0xff, 0xff, //0x000027f8 .long L0_2_set_261
	0x14, 0xec, 0xff, 0xff, //0x000027fc .long L0_2_set_261
	0x14, 0xec, 0xff, 0xff, //0x00002800 .long L0_2_set_261
	0x14, 0xec, 0xff, 0xff, //0x00002804 .long L0_2_set_261
	0x7b, 0xee, 0xff, 0xff, //0x00002808 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x0000280c .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002810 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002814 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002818 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x0000281c .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002820 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002824 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002828 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x0000282c .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002830 .long L0_2_set_292
	0x58, 0xec, 0xff, 0xff, //0x00002834 .long L0_2_set_266
	0x7b, 0xee, 0xff, 0xff, //0x00002838 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x0000283c .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002840 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002844 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002848 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x0000284c .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002850 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002854 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002858 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x0000285c .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002860 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002864 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002868 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x0000286c .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002870 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002874 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002878 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x0000287c .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002880 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002884 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002888 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x0000288c .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002890 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002894 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002898 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x0000289c .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x000028a0 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x000028a4 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x000028a8 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x000028ac .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x000028b0 .long L0_2_set_292
	0x58, 0xec, 0xff, 0xff, //0x000028b4 .long L0_2_set_266
	// // .set L0_3_set_94, LBB0_94-LJTI0_3
	// // .set L0_3_set_149, LBB0_149-LJTI0_3
	// // .set L0_3_set_98, LBB0_98-LJTI0_3
	// // .set L0_3_set_91, LBB0_91-LJTI0_3
	// // .set L0_3_set_96, LBB0_96-LJTI0_3
	//0x000028b8 LJTI0_3
	0x10, 0xdf, 0xff, 0xff, //0x000028b8 .long L0_3_set_94
	0x75, 0xe3, 0xff, 0xff, //0x000028bc .long L0_3_set_149
	0x10, 0xdf, 0xff, 0xff, //0x000028c0 .long L0_3_set_94
	0x46, 0xdf, 0xff, 0xff, //0x000028c4 .long L0_3_set_98
	0x75, 0xe3, 0xff, 0xff, //0x000028c8 .long L0_3_set_149
	0xe8, 0xde, 0xff, 0xff, //0x000028cc .long L0_3_set_91
	0xe8, 0xde, 0xff, 0xff, //0x000028d0 .long L0_3_set_91
	0xe8, 0xde, 0xff, 0xff, //0x000028d4 .long L0_3_set_91
	0xe8, 0xde, 0xff, 0xff, //0x000028d8 .long L0_3_set_91
	0xe8, 0xde, 0xff, 0xff, //0x000028dc .long L0_3_set_91
	0xe8, 0xde, 0xff, 0xff, //0x000028e0 .long L0_3_set_91
	0xe8, 0xde, 0xff, 0xff, //0x000028e4 .long L0_3_set_91
	0xe8, 0xde, 0xff, 0xff, //0x000028e8 .long L0_3_set_91
	0xe8, 0xde, 0xff, 0xff, //0x000028ec .long L0_3_set_91
	0xe8, 0xde, 0xff, 0xff, //0x000028f0 .long L0_3_set_91
	0x75, 0xe3, 0xff, 0xff, //0x000028f4 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x000028f8 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x000028fc .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002900 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002904 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002908 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x0000290c .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002910 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002914 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002918 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x0000291c .long L0_3_set_149
	0x2b, 0xdf, 0xff, 0xff, //0x00002920 .long L0_3_set_96
	0x75, 0xe3, 0xff, 0xff, //0x00002924 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002928 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x0000292c .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002930 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002934 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002938 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x0000293c .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002940 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002944 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002948 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x0000294c .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002950 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002954 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002958 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x0000295c .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002960 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002964 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002968 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x0000296c .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002970 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002974 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002978 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x0000297c .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002980 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002984 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002988 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x0000298c .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002990 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002994 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002998 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x0000299c .long L0_3_set_149
	0x2b, 0xdf, 0xff, 0xff, //0x000029a0 .long L0_3_set_96
	//0x000029a4 .p2align 2, 0x00
	//0x000029a4 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x000029a4 .long 2
}
 
