// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx

var _text_vunsigned = []byte{
	// .p2align 4, 0x90
	// _vunsigned
	0x55, // pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000001 movq         %rsp, %rbp
	0x49, 0x89, 0xd0, //0x00000004 movq         %rdx, %r8
	0x48, 0x8b, 0x0e, //0x00000007 movq         (%rsi), %rcx
	0x4c, 0x8b, 0x0f, //0x0000000a movq         (%rdi), %r9
	0x4c, 0x8b, 0x5f, 0x08, //0x0000000d movq         $8(%rdi), %r11
	0x48, 0xc7, 0x02, 0x09, 0x00, 0x00, 0x00, //0x00000011 movq         $9, (%rdx)
	0xc5, 0xf8, 0x57, 0xc0, //0x00000018 vxorps       %xmm0, %xmm0, %xmm0
	0xc5, 0xf8, 0x11, 0x42, 0x08, //0x0000001c vmovups      %xmm0, $8(%rdx)
	0x48, 0x8b, 0x06, //0x00000021 movq         (%rsi), %rax
	0x48, 0x89, 0x42, 0x18, //0x00000024 movq         %rax, $24(%rdx)
	0x4c, 0x39, 0xd9, //0x00000028 cmpq         %r11, %rcx
	0x0f, 0x83, 0x18, 0x00, 0x00, 0x00, //0x0000002b jae          LBB0_1
	0x41, 0x8a, 0x04, 0x09, //0x00000031 movb         (%r9,%rcx), %al
	0x3c, 0x2d, //0x00000035 cmpb         $45, %al
	0x0f, 0x85, 0x18, 0x00, 0x00, 0x00, //0x00000037 jne          LBB0_4
	//0x0000003d LBB0_3
	0x48, 0x89, 0x0e, //0x0000003d movq         %rcx, (%rsi)
	0x49, 0xc7, 0x00, 0xfa, 0xff, 0xff, 0xff, //0x00000040 movq         $-6, (%r8)
	0x5d, //0x00000047 popq         %rbp
	0xc3, //0x00000048 retq         
	//0x00000049 LBB0_1
	0x4c, 0x89, 0x1e, //0x00000049 movq         %r11, (%rsi)
	0x49, 0xc7, 0x00, 0xff, 0xff, 0xff, 0xff, //0x0000004c movq         $-1, (%r8)
	0x5d, //0x00000053 popq         %rbp
	0xc3, //0x00000054 retq         
	//0x00000055 LBB0_4
	0x8d, 0x50, 0xd0, //0x00000055 leal         $-48(%rax), %edx
	0x80, 0xfa, 0x0a, //0x00000058 cmpb         $10, %dl
	0x0f, 0x82, 0x0c, 0x00, 0x00, 0x00, //0x0000005b jb           LBB0_6
	0x48, 0x89, 0x0e, //0x00000061 movq         %rcx, (%rsi)
	0x49, 0xc7, 0x00, 0xfe, 0xff, 0xff, 0xff, //0x00000064 movq         $-2, (%r8)
	0x5d, //0x0000006b popq         %rbp
	0xc3, //0x0000006c retq         
	//0x0000006d LBB0_6
	0x3c, 0x30, //0x0000006d cmpb         $48, %al
	0x0f, 0x85, 0x26, 0x00, 0x00, 0x00, //0x0000006f jne          LBB0_7
	0x41, 0x8a, 0x44, 0x09, 0x01, //0x00000075 movb         $1(%r9,%rcx), %al
	0x04, 0xd2, //0x0000007a addb         $-46, %al
	0x3c, 0x37, //0x0000007c cmpb         $55, %al
	0x0f, 0x87, 0xb6, 0x00, 0x00, 0x00, //0x0000007e ja           LBB0_16
	0x0f, 0xb6, 0xc0, //0x00000084 movzbl       %al, %eax
	0x48, 0xba, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00000087 movabsq      $36028797027352577, %rdx
	0x48, 0x0f, 0xa3, 0xc2, //0x00000091 btq          %rax, %rdx
	0x0f, 0x83, 0x9f, 0x00, 0x00, 0x00, //0x00000095 jae          LBB0_16
	//0x0000009b LBB0_7
	0x31, 0xc0, //0x0000009b xorl         %eax, %eax
	0x41, 0xba, 0x0a, 0x00, 0x00, 0x00, //0x0000009d movl         $10, %r10d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000000a3 .p2align 4, 0x90
	//0x000000b0 LBB0_8
	0x4c, 0x39, 0xd9, //0x000000b0 cmpq         %r11, %rcx
	0x0f, 0x83, 0x78, 0x00, 0x00, 0x00, //0x000000b3 jae          LBB0_20
	0x41, 0x0f, 0xbe, 0x3c, 0x09, //0x000000b9 movsbl       (%r9,%rcx), %edi
	0x8d, 0x57, 0xd0, //0x000000be leal         $-48(%rdi), %edx
	0x80, 0xfa, 0x09, //0x000000c1 cmpb         $9, %dl
	0x0f, 0x87, 0x49, 0x00, 0x00, 0x00, //0x000000c4 ja           LBB0_17
	0x49, 0xf7, 0xe2, //0x000000ca mulq         %r10
	0x0f, 0x80, 0x31, 0x00, 0x00, 0x00, //0x000000cd jo           LBB0_13
	0x48, 0xff, 0xc1, //0x000000d3 incq         %rcx
	0x83, 0xc7, 0xd0, //0x000000d6 addl         $-48, %edi
	0x48, 0x63, 0xd7, //0x000000d9 movslq       %edi, %rdx
	0x48, 0x89, 0xd7, //0x000000dc movq         %rdx, %rdi
	0x48, 0xc1, 0xff, 0x3f, //0x000000df sarq         $63, %rdi
	0x48, 0x01, 0xd0, //0x000000e3 addq         %rdx, %rax
	0x48, 0x83, 0xd7, 0x00, //0x000000e6 adcq         $0, %rdi
	0x89, 0xfa, //0x000000ea movl         %edi, %edx
	0x83, 0xe2, 0x01, //0x000000ec andl         $1, %edx
	0x48, 0xf7, 0xda, //0x000000ef negq         %rdx
	0x48, 0x31, 0xd7, //0x000000f2 xorq         %rdx, %rdi
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x000000f5 jne          LBB0_13
	0x48, 0x85, 0xd2, //0x000000fb testq        %rdx, %rdx
	0x0f, 0x89, 0xac, 0xff, 0xff, 0xff, //0x000000fe jns          LBB0_8
	//0x00000104 LBB0_13
	0x48, 0xff, 0xc9, //0x00000104 decq         %rcx
	0x48, 0x89, 0x0e, //0x00000107 movq         %rcx, (%rsi)
	0x49, 0xc7, 0x00, 0xfb, 0xff, 0xff, 0xff, //0x0000010a movq         $-5, (%r8)
	0x5d, //0x00000111 popq         %rbp
	0xc3, //0x00000112 retq         
	//0x00000113 LBB0_17
	0x40, 0x80, 0xff, 0x65, //0x00000113 cmpb         $101, %dil
	0x0f, 0x84, 0x20, 0xff, 0xff, 0xff, //0x00000117 je           LBB0_3
	0x40, 0x80, 0xff, 0x45, //0x0000011d cmpb         $69, %dil
	0x0f, 0x84, 0x16, 0xff, 0xff, 0xff, //0x00000121 je           LBB0_3
	0x40, 0x80, 0xff, 0x2e, //0x00000127 cmpb         $46, %dil
	0x0f, 0x84, 0x0c, 0xff, 0xff, 0xff, //0x0000012b je           LBB0_3
	//0x00000131 LBB0_20
	0x48, 0x89, 0x0e, //0x00000131 movq         %rcx, (%rsi)
	0x49, 0x89, 0x40, 0x10, //0x00000134 movq         %rax, $16(%r8)
	0x5d, //0x00000138 popq         %rbp
	0xc3, //0x00000139 retq         
	//0x0000013a LBB0_16
	0x48, 0xff, 0xc1, //0x0000013a incq         %rcx
	0x48, 0x89, 0x0e, //0x0000013d movq         %rcx, (%rsi)
	0x5d, //0x00000140 popq         %rbp
	0xc3, //0x00000141 retq         
	0x00, 0x00, //0x00000142 .p2align 2, 0x00
	//0x00000144 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00000144 .long 2
}
 
