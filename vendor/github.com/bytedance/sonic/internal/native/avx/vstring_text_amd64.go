// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx

var _text_vstring = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, // QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x00000010 LCPI0_1
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000010 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000020 LCPI0_2
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000020 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000030 .p2align 4, 0x90
	//0x00000030 _vstring
	0x55, //0x00000030 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000031 movq         %rsp, %rbp
	0x41, 0x57, //0x00000034 pushq        %r15
	0x41, 0x56, //0x00000036 pushq        %r14
	0x41, 0x55, //0x00000038 pushq        %r13
	0x41, 0x54, //0x0000003a pushq        %r12
	0x53, //0x0000003c pushq        %rbx
	0x48, 0x83, 0xec, 0x28, //0x0000003d subq         $40, %rsp
	0x48, 0x89, 0xd3, //0x00000041 movq         %rdx, %rbx
	0x4c, 0x8b, 0x16, //0x00000044 movq         (%rsi), %r10
	0xf6, 0xc1, 0x20, //0x00000047 testb        $32, %cl
	0x48, 0x89, 0x55, 0xb8, //0x0000004a movq         %rdx, $-72(%rbp)
	0x48, 0x89, 0x75, 0xc0, //0x0000004e movq         %rsi, $-64(%rbp)
	0x0f, 0x85, 0xad, 0x01, 0x00, 0x00, //0x00000052 jne          LBB0_12
	0x48, 0x8b, 0x4f, 0x08, //0x00000058 movq         $8(%rdi), %rcx
	0x48, 0x89, 0xc8, //0x0000005c movq         %rcx, %rax
	0x4c, 0x29, 0xd0, //0x0000005f subq         %r10, %rax
	0x0f, 0x84, 0x68, 0x06, 0x00, 0x00, //0x00000062 je           LBB0_55
	0x48, 0x89, 0x4d, 0xc8, //0x00000068 movq         %rcx, $-56(%rbp)
	0x48, 0x8b, 0x37, //0x0000006c movq         (%rdi), %rsi
	0x4a, 0x8d, 0x0c, 0x16, //0x0000006f leaq         (%rsi,%r10), %rcx
	0x48, 0x83, 0xf8, 0x40, //0x00000073 cmpq         $64, %rax
	0x48, 0x89, 0x75, 0xd0, //0x00000077 movq         %rsi, $-48(%rbp)
	0x0f, 0x82, 0x5b, 0x06, 0x00, 0x00, //0x0000007b jb           LBB0_56
	0x41, 0x89, 0xc5, //0x00000081 movl         %eax, %r13d
	0x41, 0x83, 0xe5, 0x3f, //0x00000084 andl         $63, %r13d
	0x48, 0x8d, 0x50, 0xc0, //0x00000088 leaq         $-64(%rax), %rdx
	0x48, 0x83, 0xe2, 0xc0, //0x0000008c andq         $-64, %rdx
	0x4c, 0x01, 0xd2, //0x00000090 addq         %r10, %rdx
	0x48, 0x8d, 0x54, 0x16, 0x40, //0x00000093 leaq         $64(%rsi,%rdx), %rdx
	0x48, 0x89, 0x55, 0xb0, //0x00000098 movq         %rdx, $-80(%rbp)
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x0000009c movq         $-1, %r11
	0x45, 0x31, 0xc0, //0x000000a3 xorl         %r8d, %r8d
	0xc5, 0xfa, 0x6f, 0x05, 0x52, 0xff, 0xff, 0xff, //0x000000a6 vmovdqu      $-174(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x0d, 0x5a, 0xff, 0xff, 0xff, //0x000000ae vmovdqu      $-166(%rip), %xmm1  /* LCPI0_1+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000000b6 .p2align 4, 0x90
	//0x000000c0 LBB0_4
	0xc5, 0xfa, 0x6f, 0x11, //0x000000c0 vmovdqu      (%rcx), %xmm2
	0xc5, 0xfa, 0x6f, 0x59, 0x10, //0x000000c4 vmovdqu      $16(%rcx), %xmm3
	0xc5, 0xfa, 0x6f, 0x61, 0x20, //0x000000c9 vmovdqu      $32(%rcx), %xmm4
	0xc5, 0xfa, 0x6f, 0x69, 0x30, //0x000000ce vmovdqu      $48(%rcx), %xmm5
	0xc5, 0xe9, 0x74, 0xf0, //0x000000d3 vpcmpeqb     %xmm0, %xmm2, %xmm6
	0xc5, 0xf9, 0xd7, 0xde, //0x000000d7 vpmovmskb    %xmm6, %ebx
	0xc5, 0xe1, 0x74, 0xf0, //0x000000db vpcmpeqb     %xmm0, %xmm3, %xmm6
	0xc5, 0xf9, 0xd7, 0xd6, //0x000000df vpmovmskb    %xmm6, %edx
	0xc5, 0xd9, 0x74, 0xf0, //0x000000e3 vpcmpeqb     %xmm0, %xmm4, %xmm6
	0xc5, 0xf9, 0xd7, 0xf6, //0x000000e7 vpmovmskb    %xmm6, %esi
	0xc5, 0xd1, 0x74, 0xf0, //0x000000eb vpcmpeqb     %xmm0, %xmm5, %xmm6
	0xc5, 0x79, 0xd7, 0xfe, //0x000000ef vpmovmskb    %xmm6, %r15d
	0xc5, 0xe9, 0x74, 0xd1, //0x000000f3 vpcmpeqb     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x000000f7 vpmovmskb    %xmm2, %edi
	0xc5, 0xe1, 0x74, 0xd1, //0x000000fb vpcmpeqb     %xmm1, %xmm3, %xmm2
	0xc5, 0x79, 0xd7, 0xe2, //0x000000ff vpmovmskb    %xmm2, %r12d
	0xc5, 0xd9, 0x74, 0xd1, //0x00000103 vpcmpeqb     %xmm1, %xmm4, %xmm2
	0xc5, 0x79, 0xd7, 0xf2, //0x00000107 vpmovmskb    %xmm2, %r14d
	0xc5, 0xd1, 0x74, 0xd1, //0x0000010b vpcmpeqb     %xmm1, %xmm5, %xmm2
	0xc5, 0x79, 0xd7, 0xca, //0x0000010f vpmovmskb    %xmm2, %r9d
	0x49, 0xc1, 0xe7, 0x30, //0x00000113 shlq         $48, %r15
	0x48, 0xc1, 0xe6, 0x20, //0x00000117 shlq         $32, %rsi
	0x48, 0xc1, 0xe2, 0x10, //0x0000011b shlq         $16, %rdx
	0x48, 0x09, 0xd3, //0x0000011f orq          %rdx, %rbx
	0x48, 0x09, 0xf3, //0x00000122 orq          %rsi, %rbx
	0x49, 0xc1, 0xe1, 0x30, //0x00000125 shlq         $48, %r9
	0x49, 0xc1, 0xe6, 0x20, //0x00000129 shlq         $32, %r14
	0x49, 0xc1, 0xe4, 0x10, //0x0000012d shlq         $16, %r12
	0x4c, 0x09, 0xe7, //0x00000131 orq          %r12, %rdi
	0x4c, 0x09, 0xf7, //0x00000134 orq          %r14, %rdi
	0x4c, 0x09, 0xcf, //0x00000137 orq          %r9, %rdi
	0x49, 0x83, 0xfb, 0xff, //0x0000013a cmpq         $-1, %r11
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x0000013e jne          LBB0_6
	0x48, 0x85, 0xff, //0x00000144 testq        %rdi, %rdi
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x00000147 jne          LBB0_9
	//0x0000014d LBB0_6
	0x4c, 0x09, 0xfb, //0x0000014d orq          %r15, %rbx
	0x48, 0x89, 0xfa, //0x00000150 movq         %rdi, %rdx
	0x4c, 0x09, 0xc2, //0x00000153 orq          %r8, %rdx
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x00000156 jne          LBB0_10
	//0x0000015c LBB0_7
	0x48, 0x85, 0xdb, //0x0000015c testq        %rbx, %rbx
	0x0f, 0x85, 0x86, 0x00, 0x00, 0x00, //0x0000015f jne          LBB0_11
	//0x00000165 LBB0_8
	0x48, 0x83, 0xc0, 0xc0, //0x00000165 addq         $-64, %rax
	0x48, 0x83, 0xc1, 0x40, //0x00000169 addq         $64, %rcx
	0x48, 0x83, 0xf8, 0x3f, //0x0000016d cmpq         $63, %rax
	0x0f, 0x87, 0x49, 0xff, 0xff, 0xff, //0x00000171 ja           LBB0_4
	0xe9, 0xef, 0x02, 0x00, 0x00, //0x00000177 jmp          LBB0_29
	//0x0000017c LBB0_9
	0x48, 0x89, 0xca, //0x0000017c movq         %rcx, %rdx
	0x48, 0x2b, 0x55, 0xd0, //0x0000017f subq         $-48(%rbp), %rdx
	0x4c, 0x0f, 0xbc, 0xdf, //0x00000183 bsfq         %rdi, %r11
	0x49, 0x01, 0xd3, //0x00000187 addq         %rdx, %r11
	0x4c, 0x09, 0xfb, //0x0000018a orq          %r15, %rbx
	0x48, 0x89, 0xfa, //0x0000018d movq         %rdi, %rdx
	0x4c, 0x09, 0xc2, //0x00000190 orq          %r8, %rdx
	0x0f, 0x84, 0xc3, 0xff, 0xff, 0xff, //0x00000193 je           LBB0_7
	//0x00000199 LBB0_10
	0x4c, 0x89, 0xc2, //0x00000199 movq         %r8, %rdx
	0x48, 0xf7, 0xd2, //0x0000019c notq         %rdx
	0x48, 0x21, 0xfa, //0x0000019f andq         %rdi, %rdx
	0x4c, 0x8d, 0x0c, 0x12, //0x000001a2 leaq         (%rdx,%rdx), %r9
	0x4d, 0x09, 0xc1, //0x000001a6 orq          %r8, %r9
	0x4c, 0x89, 0xce, //0x000001a9 movq         %r9, %rsi
	0x48, 0xf7, 0xd6, //0x000001ac notq         %rsi
	0x48, 0x21, 0xfe, //0x000001af andq         %rdi, %rsi
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000001b2 movabsq      $-6148914691236517206, %rdi
	0x48, 0x21, 0xfe, //0x000001bc andq         %rdi, %rsi
	0x45, 0x31, 0xc0, //0x000001bf xorl         %r8d, %r8d
	0x48, 0x01, 0xd6, //0x000001c2 addq         %rdx, %rsi
	0x41, 0x0f, 0x92, 0xc0, //0x000001c5 setb         %r8b
	0x48, 0x01, 0xf6, //0x000001c9 addq         %rsi, %rsi
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000001cc movabsq      $6148914691236517205, %rdx
	0x48, 0x31, 0xd6, //0x000001d6 xorq         %rdx, %rsi
	0x4c, 0x21, 0xce, //0x000001d9 andq         %r9, %rsi
	0x48, 0xf7, 0xd6, //0x000001dc notq         %rsi
	0x48, 0x21, 0xf3, //0x000001df andq         %rsi, %rbx
	0x48, 0x85, 0xdb, //0x000001e2 testq        %rbx, %rbx
	0x0f, 0x84, 0x7a, 0xff, 0xff, 0xff, //0x000001e5 je           LBB0_8
	//0x000001eb LBB0_11
	0x48, 0x0f, 0xbc, 0xc3, //0x000001eb bsfq         %rbx, %rax
	0x48, 0x2b, 0x4d, 0xd0, //0x000001ef subq         $-48(%rbp), %rcx
	0x48, 0x8d, 0x7c, 0x01, 0x01, //0x000001f3 leaq         $1(%rcx,%rax), %rdi
	0x48, 0x8b, 0x5d, 0xb8, //0x000001f8 movq         $-72(%rbp), %rbx
	0x48, 0x8b, 0x75, 0xc0, //0x000001fc movq         $-64(%rbp), %rsi
	0xe9, 0x38, 0x02, 0x00, 0x00, //0x00000200 jmp          LBB0_27
	//0x00000205 LBB0_12
	0x48, 0x8b, 0x4f, 0x08, //0x00000205 movq         $8(%rdi), %rcx
	0x48, 0x89, 0xc8, //0x00000209 movq         %rcx, %rax
	0x4c, 0x29, 0xd0, //0x0000020c subq         %r10, %rax
	0x0f, 0x84, 0xbb, 0x04, 0x00, 0x00, //0x0000020f je           LBB0_55
	0x48, 0x89, 0x4d, 0xc8, //0x00000215 movq         %rcx, $-56(%rbp)
	0x48, 0x8b, 0x3f, //0x00000219 movq         (%rdi), %rdi
	0x4a, 0x8d, 0x0c, 0x17, //0x0000021c leaq         (%rdi,%r10), %rcx
	0x48, 0x83, 0xf8, 0x40, //0x00000220 cmpq         $64, %rax
	0x48, 0x89, 0x7d, 0xd0, //0x00000224 movq         %rdi, $-48(%rbp)
	0x0f, 0x82, 0x1b, 0x05, 0x00, 0x00, //0x00000228 jb           LBB0_60
	0x41, 0x89, 0xc5, //0x0000022e movl         %eax, %r13d
	0x41, 0x83, 0xe5, 0x3f, //0x00000231 andl         $63, %r13d
	0x48, 0x8d, 0x50, 0xc0, //0x00000235 leaq         $-64(%rax), %rdx
	0x48, 0x83, 0xe2, 0xc0, //0x00000239 andq         $-64, %rdx
	0x4c, 0x01, 0xd2, //0x0000023d addq         %r10, %rdx
	0x48, 0x8d, 0x54, 0x17, 0x40, //0x00000240 leaq         $64(%rdi,%rdx), %rdx
	0x48, 0x89, 0x55, 0xb0, //0x00000245 movq         %rdx, $-80(%rbp)
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00000249 movq         $-1, %r11
	0x45, 0x31, 0xc9, //0x00000250 xorl         %r9d, %r9d
	0xc5, 0x7a, 0x6f, 0x05, 0xa5, 0xfd, 0xff, 0xff, //0x00000253 vmovdqu      $-603(%rip), %xmm8  /* LCPI0_0+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x0d, 0xad, 0xfd, 0xff, 0xff, //0x0000025b vmovdqu      $-595(%rip), %xmm1  /* LCPI0_1+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x15, 0xb5, 0xfd, 0xff, 0xff, //0x00000263 vmovdqu      $-587(%rip), %xmm2  /* LCPI0_2+0(%rip) */
	0xc5, 0xe1, 0x76, 0xdb, //0x0000026b vpcmpeqd     %xmm3, %xmm3, %xmm3
	0x90, //0x0000026f .p2align 4, 0x90
	//0x00000270 LBB0_15
	0xc5, 0xfa, 0x6f, 0x39, //0x00000270 vmovdqu      (%rcx), %xmm7
	0xc5, 0xfa, 0x6f, 0x71, 0x10, //0x00000274 vmovdqu      $16(%rcx), %xmm6
	0xc5, 0xfa, 0x6f, 0x69, 0x20, //0x00000279 vmovdqu      $32(%rcx), %xmm5
	0xc5, 0xfa, 0x6f, 0x61, 0x30, //0x0000027e vmovdqu      $48(%rcx), %xmm4
	0xc5, 0xb9, 0x74, 0xc7, //0x00000283 vpcmpeqb     %xmm7, %xmm8, %xmm0
	0xc5, 0xf9, 0xd7, 0xd8, //0x00000287 vpmovmskb    %xmm0, %ebx
	0xc5, 0xb9, 0x74, 0xc6, //0x0000028b vpcmpeqb     %xmm6, %xmm8, %xmm0
	0xc5, 0xf9, 0xd7, 0xd0, //0x0000028f vpmovmskb    %xmm0, %edx
	0xc5, 0xb9, 0x74, 0xc5, //0x00000293 vpcmpeqb     %xmm5, %xmm8, %xmm0
	0xc5, 0xf9, 0xd7, 0xf0, //0x00000297 vpmovmskb    %xmm0, %esi
	0xc5, 0xb9, 0x74, 0xc4, //0x0000029b vpcmpeqb     %xmm4, %xmm8, %xmm0
	0xc5, 0x79, 0xd7, 0xc0, //0x0000029f vpmovmskb    %xmm0, %r8d
	0xc5, 0xc1, 0x74, 0xc1, //0x000002a3 vpcmpeqb     %xmm1, %xmm7, %xmm0
	0xc5, 0x79, 0xd7, 0xf8, //0x000002a7 vpmovmskb    %xmm0, %r15d
	0xc5, 0xc9, 0x74, 0xc1, //0x000002ab vpcmpeqb     %xmm1, %xmm6, %xmm0
	0xc5, 0xf9, 0xd7, 0xf8, //0x000002af vpmovmskb    %xmm0, %edi
	0xc5, 0xd1, 0x74, 0xc1, //0x000002b3 vpcmpeqb     %xmm1, %xmm5, %xmm0
	0x48, 0xc1, 0xe2, 0x10, //0x000002b7 shlq         $16, %rdx
	0x48, 0x09, 0xd3, //0x000002bb orq          %rdx, %rbx
	0xc5, 0xf9, 0xd7, 0xd0, //0x000002be vpmovmskb    %xmm0, %edx
	0xc5, 0xd9, 0x74, 0xc1, //0x000002c2 vpcmpeqb     %xmm1, %xmm4, %xmm0
	0x48, 0xc1, 0xe6, 0x20, //0x000002c6 shlq         $32, %rsi
	0x48, 0x09, 0xf3, //0x000002ca orq          %rsi, %rbx
	0xc5, 0xf9, 0xd7, 0xf0, //0x000002cd vpmovmskb    %xmm0, %esi
	0xc5, 0xe9, 0x64, 0xc7, //0x000002d1 vpcmpgtb     %xmm7, %xmm2, %xmm0
	0xc5, 0xc1, 0x64, 0xfb, //0x000002d5 vpcmpgtb     %xmm3, %xmm7, %xmm7
	0xc5, 0xc1, 0xdb, 0xc0, //0x000002d9 vpand        %xmm0, %xmm7, %xmm0
	0x48, 0xc1, 0xe7, 0x10, //0x000002dd shlq         $16, %rdi
	0x49, 0x09, 0xff, //0x000002e1 orq          %rdi, %r15
	0xc5, 0x79, 0xd7, 0xe0, //0x000002e4 vpmovmskb    %xmm0, %r12d
	0xc5, 0xe9, 0x64, 0xc6, //0x000002e8 vpcmpgtb     %xmm6, %xmm2, %xmm0
	0xc5, 0xc9, 0x64, 0xf3, //0x000002ec vpcmpgtb     %xmm3, %xmm6, %xmm6
	0xc5, 0xc9, 0xdb, 0xc0, //0x000002f0 vpand        %xmm0, %xmm6, %xmm0
	0x48, 0xc1, 0xe2, 0x20, //0x000002f4 shlq         $32, %rdx
	0x49, 0x09, 0xd7, //0x000002f8 orq          %rdx, %r15
	0xc5, 0xf9, 0xd7, 0xf8, //0x000002fb vpmovmskb    %xmm0, %edi
	0xc5, 0xe9, 0x64, 0xc5, //0x000002ff vpcmpgtb     %xmm5, %xmm2, %xmm0
	0xc5, 0xd1, 0x64, 0xeb, //0x00000303 vpcmpgtb     %xmm3, %xmm5, %xmm5
	0xc5, 0xd1, 0xdb, 0xc0, //0x00000307 vpand        %xmm0, %xmm5, %xmm0
	0x48, 0xc1, 0xe6, 0x30, //0x0000030b shlq         $48, %rsi
	0x49, 0x09, 0xf7, //0x0000030f orq          %rsi, %r15
	0xc5, 0xf9, 0xd7, 0xd0, //0x00000312 vpmovmskb    %xmm0, %edx
	0xc5, 0xe9, 0x64, 0xc4, //0x00000316 vpcmpgtb     %xmm4, %xmm2, %xmm0
	0xc5, 0xd9, 0x64, 0xe3, //0x0000031a vpcmpgtb     %xmm3, %xmm4, %xmm4
	0xc5, 0xd9, 0xdb, 0xc0, //0x0000031e vpand        %xmm0, %xmm4, %xmm0
	0x48, 0xc1, 0xe7, 0x10, //0x00000322 shlq         $16, %rdi
	0x49, 0x09, 0xfc, //0x00000326 orq          %rdi, %r12
	0xc5, 0x79, 0xd7, 0xf0, //0x00000329 vpmovmskb    %xmm0, %r14d
	0x49, 0xc1, 0xe0, 0x30, //0x0000032d shlq         $48, %r8
	0x48, 0xc1, 0xe2, 0x20, //0x00000331 shlq         $32, %rdx
	0x49, 0x83, 0xfb, 0xff, //0x00000335 cmpq         $-1, %r11
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00000339 jne          LBB0_17
	0x4d, 0x85, 0xff, //0x0000033f testq        %r15, %r15
	0x0f, 0x85, 0x9c, 0x00, 0x00, 0x00, //0x00000342 jne          LBB0_22
	//0x00000348 LBB0_17
	0x49, 0xc1, 0xe6, 0x30, //0x00000348 shlq         $48, %r14
	0x49, 0x09, 0xd4, //0x0000034c orq          %rdx, %r12
	0x4c, 0x09, 0xc3, //0x0000034f orq          %r8, %rbx
	0x4c, 0x89, 0xfa, //0x00000352 movq         %r15, %rdx
	0x4c, 0x09, 0xca, //0x00000355 orq          %r9, %rdx
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x00000358 jne          LBB0_21
	0x4d, 0x09, 0xf4, //0x0000035e orq          %r14, %r12
	0x48, 0x85, 0xdb, //0x00000361 testq        %rbx, %rbx
	0x0f, 0x85, 0x8d, 0x00, 0x00, 0x00, //0x00000364 jne          LBB0_23
	//0x0000036a LBB0_19
	0x4d, 0x85, 0xe4, //0x0000036a testq        %r12, %r12
	0x0f, 0x85, 0x89, 0x01, 0x00, 0x00, //0x0000036d jne          LBB0_35
	0x48, 0x83, 0xc0, 0xc0, //0x00000373 addq         $-64, %rax
	0x48, 0x83, 0xc1, 0x40, //0x00000377 addq         $64, %rcx
	0x48, 0x83, 0xf8, 0x3f, //0x0000037b cmpq         $63, %rax
	0x0f, 0x87, 0xeb, 0xfe, 0xff, 0xff, //0x0000037f ja           LBB0_15
	0xe9, 0x8a, 0x01, 0x00, 0x00, //0x00000385 jmp          LBB0_37
	//0x0000038a LBB0_21
	0x4c, 0x89, 0xca, //0x0000038a movq         %r9, %rdx
	0x48, 0xf7, 0xd2, //0x0000038d notq         %rdx
	0x4c, 0x21, 0xfa, //0x00000390 andq         %r15, %rdx
	0x4c, 0x8d, 0x04, 0x12, //0x00000393 leaq         (%rdx,%rdx), %r8
	0x4d, 0x09, 0xc8, //0x00000397 orq          %r9, %r8
	0x4c, 0x89, 0xc7, //0x0000039a movq         %r8, %rdi
	0x48, 0xf7, 0xd7, //0x0000039d notq         %rdi
	0x4c, 0x21, 0xff, //0x000003a0 andq         %r15, %rdi
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000003a3 movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf7, //0x000003ad andq         %rsi, %rdi
	0x45, 0x31, 0xc9, //0x000003b0 xorl         %r9d, %r9d
	0x48, 0x01, 0xd7, //0x000003b3 addq         %rdx, %rdi
	0x41, 0x0f, 0x92, 0xc1, //0x000003b6 setb         %r9b
	0x48, 0x01, 0xff, //0x000003ba addq         %rdi, %rdi
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000003bd movabsq      $6148914691236517205, %rdx
	0x48, 0x31, 0xd7, //0x000003c7 xorq         %rdx, %rdi
	0x4c, 0x21, 0xc7, //0x000003ca andq         %r8, %rdi
	0x48, 0xf7, 0xd7, //0x000003cd notq         %rdi
	0x48, 0x21, 0xfb, //0x000003d0 andq         %rdi, %rbx
	0x4d, 0x09, 0xf4, //0x000003d3 orq          %r14, %r12
	0x48, 0x85, 0xdb, //0x000003d6 testq        %rbx, %rbx
	0x0f, 0x84, 0x8b, 0xff, 0xff, 0xff, //0x000003d9 je           LBB0_19
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x000003df jmp          LBB0_23
	//0x000003e4 LBB0_22
	0x48, 0x89, 0xce, //0x000003e4 movq         %rcx, %rsi
	0x48, 0x2b, 0x75, 0xd0, //0x000003e7 subq         $-48(%rbp), %rsi
	0x4d, 0x0f, 0xbc, 0xdf, //0x000003eb bsfq         %r15, %r11
	0x49, 0x01, 0xf3, //0x000003ef addq         %rsi, %r11
	0xe9, 0x51, 0xff, 0xff, 0xff, //0x000003f2 jmp          LBB0_17
	//0x000003f7 LBB0_23
	0x48, 0x0f, 0xbc, 0xc3, //0x000003f7 bsfq         %rbx, %rax
	0x4d, 0x85, 0xe4, //0x000003fb testq        %r12, %r12
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x000003fe je           LBB0_25
	0x49, 0x0f, 0xbc, 0xd4, //0x00000404 bsfq         %r12, %rdx
	0x48, 0x8b, 0x5d, 0xb8, //0x00000408 movq         $-72(%rbp), %rbx
	0x48, 0x8b, 0x75, 0xc0, //0x0000040c movq         $-64(%rbp), %rsi
	0x48, 0x39, 0xc2, //0x00000410 cmpq         %rax, %rdx
	0x0f, 0x83, 0x1b, 0x00, 0x00, 0x00, //0x00000413 jae          LBB0_26
	0xe9, 0x7c, 0x04, 0x00, 0x00, //0x00000419 jmp          LBB0_80
	//0x0000041e LBB0_25
	0xba, 0x40, 0x00, 0x00, 0x00, //0x0000041e movl         $64, %edx
	0x48, 0x8b, 0x5d, 0xb8, //0x00000423 movq         $-72(%rbp), %rbx
	0x48, 0x8b, 0x75, 0xc0, //0x00000427 movq         $-64(%rbp), %rsi
	0x48, 0x39, 0xc2, //0x0000042b cmpq         %rax, %rdx
	0x0f, 0x82, 0x66, 0x04, 0x00, 0x00, //0x0000042e jb           LBB0_80
	//0x00000434 LBB0_26
	0x48, 0x2b, 0x4d, 0xd0, //0x00000434 subq         $-48(%rbp), %rcx
	0x48, 0x8d, 0x7c, 0x01, 0x01, //0x00000438 leaq         $1(%rcx,%rax), %rdi
	//0x0000043d LBB0_27
	0x48, 0x85, 0xff, //0x0000043d testq        %rdi, %rdi
	0x0f, 0x88, 0x5b, 0x04, 0x00, 0x00, //0x00000440 js           LBB0_81
	0x48, 0x89, 0x3e, //0x00000446 movq         %rdi, (%rsi)
	0x4c, 0x89, 0x53, 0x10, //0x00000449 movq         %r10, $16(%rbx)
	0x48, 0xc7, 0x03, 0x07, 0x00, 0x00, 0x00, //0x0000044d movq         $7, (%rbx)
	0x49, 0x39, 0xfb, //0x00000454 cmpq         %rdi, %r11
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000457 movq         $-1, %rax
	0x49, 0x0f, 0x4c, 0xc3, //0x0000045e cmovlq       %r11, %rax
	0x48, 0x89, 0x43, 0x18, //0x00000462 movq         %rax, $24(%rbx)
	0xe9, 0x40, 0x04, 0x00, 0x00, //0x00000466 jmp          LBB0_83
	//0x0000046b LBB0_29
	0x48, 0x8b, 0x4d, 0xb0, //0x0000046b movq         $-80(%rbp), %rcx
	0x4c, 0x89, 0xe8, //0x0000046f movq         %r13, %rax
	0x48, 0x83, 0xf8, 0x20, //0x00000472 cmpq         $32, %rax
	0x0f, 0x82, 0x74, 0x02, 0x00, 0x00, //0x00000476 jb           LBB0_57
	//0x0000047c LBB0_30
	0xc5, 0xfa, 0x6f, 0x01, //0x0000047c vmovdqu      (%rcx), %xmm0
	0xc5, 0xfa, 0x6f, 0x49, 0x10, //0x00000480 vmovdqu      $16(%rcx), %xmm1
	0xc5, 0xfa, 0x6f, 0x15, 0x73, 0xfb, 0xff, 0xff, //0x00000485 vmovdqu      $-1165(%rip), %xmm2  /* LCPI0_0+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x1d, 0x7b, 0xfb, 0xff, 0xff, //0x0000048d vmovdqu      $-1157(%rip), %xmm3  /* LCPI0_1+0(%rip) */
	0xc5, 0xf9, 0x74, 0xe2, //0x00000495 vpcmpeqb     %xmm2, %xmm0, %xmm4
	0xc5, 0x79, 0xd7, 0xcc, //0x00000499 vpmovmskb    %xmm4, %r9d
	0xc5, 0xf1, 0x74, 0xd2, //0x0000049d vpcmpeqb     %xmm2, %xmm1, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x000004a1 vpmovmskb    %xmm2, %edi
	0xc5, 0xf9, 0x74, 0xc3, //0x000004a5 vpcmpeqb     %xmm3, %xmm0, %xmm0
	0xc5, 0x79, 0xd7, 0xf8, //0x000004a9 vpmovmskb    %xmm0, %r15d
	0xc5, 0xf1, 0x74, 0xc3, //0x000004ad vpcmpeqb     %xmm3, %xmm1, %xmm0
	0xc5, 0xf9, 0xd7, 0xd0, //0x000004b1 vpmovmskb    %xmm0, %edx
	0x48, 0xc1, 0xe7, 0x10, //0x000004b5 shlq         $16, %rdi
	0x48, 0xc1, 0xe2, 0x10, //0x000004b9 shlq         $16, %rdx
	0x49, 0x09, 0xd7, //0x000004bd orq          %rdx, %r15
	0x49, 0x83, 0xfb, 0xff, //0x000004c0 cmpq         $-1, %r11
	0x0f, 0x85, 0x0b, 0x01, 0x00, 0x00, //0x000004c4 jne          LBB0_43
	0x4d, 0x85, 0xff, //0x000004ca testq        %r15, %r15
	0x48, 0x8b, 0x5d, 0xb8, //0x000004cd movq         $-72(%rbp), %rbx
	0x48, 0x8b, 0x75, 0xc0, //0x000004d1 movq         $-64(%rbp), %rsi
	0x0f, 0x85, 0xdf, 0x03, 0x00, 0x00, //0x000004d5 jne          LBB0_84
	0x4c, 0x09, 0xcf, //0x000004db orq          %r9, %rdi
	0x4c, 0x89, 0xfa, //0x000004de movq         %r15, %rdx
	0x4c, 0x09, 0xc2, //0x000004e1 orq          %r8, %rdx
	0x0f, 0x85, 0x02, 0x01, 0x00, 0x00, //0x000004e4 jne          LBB0_44
	//0x000004ea LBB0_33
	0x48, 0x85, 0xff, //0x000004ea testq        %rdi, %rdi
	0x0f, 0x84, 0x3c, 0x01, 0x00, 0x00, //0x000004ed je           LBB0_45
	//0x000004f3 LBB0_34
	0x48, 0x0f, 0xbc, 0xc7, //0x000004f3 bsfq         %rdi, %rax
	0xe9, 0x38, 0xff, 0xff, 0xff, //0x000004f7 jmp          LBB0_26
	//0x000004fc LBB0_35
	0x48, 0xc7, 0xc7, 0xfe, 0xff, 0xff, 0xff, //0x000004fc movq         $-2, %rdi
	//0x00000503 LBB0_36
	0x4c, 0x8b, 0x55, 0xc8, //0x00000503 movq         $-56(%rbp), %r10
	0x48, 0x8b, 0x5d, 0xb8, //0x00000507 movq         $-72(%rbp), %rbx
	0x48, 0x8b, 0x75, 0xc0, //0x0000050b movq         $-64(%rbp), %rsi
	0xe9, 0x91, 0x03, 0x00, 0x00, //0x0000050f jmp          LBB0_82
	//0x00000514 LBB0_37
	0x48, 0x8b, 0x4d, 0xb0, //0x00000514 movq         $-80(%rbp), %rcx
	0x4c, 0x89, 0xe8, //0x00000518 movq         %r13, %rax
	0x48, 0x8b, 0x5d, 0xb8, //0x0000051b movq         $-72(%rbp), %rbx
	0x48, 0x8b, 0x75, 0xc0, //0x0000051f movq         $-64(%rbp), %rsi
	0x48, 0x83, 0xf8, 0x20, //0x00000523 cmpq         $32, %rax
	0x0f, 0x82, 0xb1, 0x02, 0x00, 0x00, //0x00000527 jb           LBB0_68
	//0x0000052d LBB0_38
	0xc5, 0xfa, 0x6f, 0x01, //0x0000052d vmovdqu      (%rcx), %xmm0
	0xc5, 0xfa, 0x6f, 0x49, 0x10, //0x00000531 vmovdqu      $16(%rcx), %xmm1
	0xc5, 0xfa, 0x6f, 0x15, 0xc2, 0xfa, 0xff, 0xff, //0x00000536 vmovdqu      $-1342(%rip), %xmm2  /* LCPI0_0+0(%rip) */
	0xc5, 0xf9, 0x74, 0xda, //0x0000053e vpcmpeqb     %xmm2, %xmm0, %xmm3
	0xc5, 0x79, 0xd7, 0xe3, //0x00000542 vpmovmskb    %xmm3, %r12d
	0xc5, 0xf1, 0x74, 0xd2, //0x00000546 vpcmpeqb     %xmm2, %xmm1, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x0000054a vpmovmskb    %xmm2, %edi
	0xc5, 0xfa, 0x6f, 0x15, 0xba, 0xfa, 0xff, 0xff, //0x0000054e vmovdqu      $-1350(%rip), %xmm2  /* LCPI0_1+0(%rip) */
	0xc5, 0xf9, 0x74, 0xda, //0x00000556 vpcmpeqb     %xmm2, %xmm0, %xmm3
	0xc5, 0x79, 0xd7, 0xfb, //0x0000055a vpmovmskb    %xmm3, %r15d
	0xc5, 0xf1, 0x74, 0xd2, //0x0000055e vpcmpeqb     %xmm2, %xmm1, %xmm2
	0xc5, 0xf9, 0xd7, 0xd2, //0x00000562 vpmovmskb    %xmm2, %edx
	0xc5, 0xfa, 0x6f, 0x15, 0xb2, 0xfa, 0xff, 0xff, //0x00000566 vmovdqu      $-1358(%rip), %xmm2  /* LCPI0_2+0(%rip) */
	0xc5, 0xe9, 0x64, 0xd8, //0x0000056e vpcmpgtb     %xmm0, %xmm2, %xmm3
	0xc5, 0xd9, 0x76, 0xe4, //0x00000572 vpcmpeqd     %xmm4, %xmm4, %xmm4
	0xc5, 0xf9, 0x64, 0xc4, //0x00000576 vpcmpgtb     %xmm4, %xmm0, %xmm0
	0xc5, 0xf9, 0xdb, 0xc3, //0x0000057a vpand        %xmm3, %xmm0, %xmm0
	0xc5, 0xe9, 0x64, 0xd1, //0x0000057e vpcmpgtb     %xmm1, %xmm2, %xmm2
	0xc5, 0xf1, 0x64, 0xcc, //0x00000582 vpcmpgtb     %xmm4, %xmm1, %xmm1
	0xc5, 0xf1, 0xdb, 0xca, //0x00000586 vpand        %xmm2, %xmm1, %xmm1
	0xc5, 0x79, 0xd7, 0xf1, //0x0000058a vpmovmskb    %xmm1, %r14d
	0x48, 0xc1, 0xe7, 0x10, //0x0000058e shlq         $16, %rdi
	0x48, 0xc1, 0xe2, 0x10, //0x00000592 shlq         $16, %rdx
	0x49, 0x09, 0xd7, //0x00000596 orq          %rdx, %r15
	0x49, 0x83, 0xfb, 0xff, //0x00000599 cmpq         $-1, %r11
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x0000059d jne          LBB0_40
	0x4d, 0x85, 0xff, //0x000005a3 testq        %r15, %r15
	0x0f, 0x85, 0x30, 0x03, 0x00, 0x00, //0x000005a6 jne          LBB0_85
	//0x000005ac LBB0_40
	0xc5, 0x79, 0xd7, 0xc0, //0x000005ac vpmovmskb    %xmm0, %r8d
	0x4c, 0x09, 0xe7, //0x000005b0 orq          %r12, %rdi
	0x4c, 0x89, 0xfa, //0x000005b3 movq         %r15, %rdx
	0x4c, 0x09, 0xca, //0x000005b6 orq          %r9, %rdx
	0x0f, 0x85, 0xa3, 0x01, 0x00, 0x00, //0x000005b9 jne          LBB0_61
	0x49, 0xc1, 0xe6, 0x10, //0x000005bf shlq         $16, %r14
	0x48, 0x85, 0xff, //0x000005c3 testq        %rdi, %rdi
	0x0f, 0x84, 0xde, 0x01, 0x00, 0x00, //0x000005c6 je           LBB0_62
	//0x000005cc LBB0_42
	0x48, 0x0f, 0xbc, 0xd7, //0x000005cc bsfq         %rdi, %rdx
	0xe9, 0xda, 0x01, 0x00, 0x00, //0x000005d0 jmp          LBB0_63
	//0x000005d5 LBB0_43
	0x48, 0x8b, 0x5d, 0xb8, //0x000005d5 movq         $-72(%rbp), %rbx
	0x48, 0x8b, 0x75, 0xc0, //0x000005d9 movq         $-64(%rbp), %rsi
	0x4c, 0x09, 0xcf, //0x000005dd orq          %r9, %rdi
	0x4c, 0x89, 0xfa, //0x000005e0 movq         %r15, %rdx
	0x4c, 0x09, 0xc2, //0x000005e3 orq          %r8, %rdx
	0x0f, 0x84, 0xfe, 0xfe, 0xff, 0xff, //0x000005e6 je           LBB0_33
	//0x000005ec LBB0_44
	0x45, 0x89, 0xc6, //0x000005ec movl         %r8d, %r14d
	0x41, 0xf7, 0xd6, //0x000005ef notl         %r14d
	0x45, 0x21, 0xfe, //0x000005f2 andl         %r15d, %r14d
	0x47, 0x8d, 0x0c, 0x36, //0x000005f5 leal         (%r14,%r14), %r9d
	0x45, 0x09, 0xc1, //0x000005f9 orl          %r8d, %r9d
	0xba, 0xaa, 0xaa, 0xaa, 0xaa, //0x000005fc movl         $2863311530, %edx
	0x44, 0x31, 0xca, //0x00000601 xorl         %r9d, %edx
	0x44, 0x21, 0xfa, //0x00000604 andl         %r15d, %edx
	0x81, 0xe2, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000607 andl         $-1431655766, %edx
	0x45, 0x31, 0xc0, //0x0000060d xorl         %r8d, %r8d
	0x44, 0x01, 0xf2, //0x00000610 addl         %r14d, %edx
	0x41, 0x0f, 0x92, 0xc0, //0x00000613 setb         %r8b
	0x01, 0xd2, //0x00000617 addl         %edx, %edx
	0x81, 0xf2, 0x55, 0x55, 0x55, 0x55, //0x00000619 xorl         $1431655765, %edx
	0x44, 0x21, 0xca, //0x0000061f andl         %r9d, %edx
	0xf7, 0xd2, //0x00000622 notl         %edx
	0x21, 0xd7, //0x00000624 andl         %edx, %edi
	0x48, 0x85, 0xff, //0x00000626 testq        %rdi, %rdi
	0x0f, 0x85, 0xc4, 0xfe, 0xff, 0xff, //0x00000629 jne          LBB0_34
	//0x0000062f LBB0_45
	0x48, 0x83, 0xc1, 0x20, //0x0000062f addq         $32, %rcx
	0x48, 0x83, 0xc0, 0xe0, //0x00000633 addq         $-32, %rax
	0x4d, 0x85, 0xc0, //0x00000637 testq        %r8, %r8
	0x0f, 0x85, 0xc1, 0x00, 0x00, 0x00, //0x0000063a jne          LBB0_58
	//0x00000640 LBB0_46
	0x4d, 0x89, 0xd8, //0x00000640 movq         %r11, %r8
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00000643 movq         $-1, %rdi
	0x48, 0x85, 0xc0, //0x0000064a testq        %rax, %rax
	0x0f, 0x84, 0x4e, 0x02, 0x00, 0x00, //0x0000064d je           LBB0_81
	//0x00000653 LBB0_47
	0x4c, 0x8b, 0x75, 0xd0, //0x00000653 movq         $-48(%rbp), %r14
	0x49, 0xf7, 0xd6, //0x00000657 notq         %r14
	//0x0000065a LBB0_48
	0x4c, 0x8d, 0x79, 0x01, //0x0000065a leaq         $1(%rcx), %r15
	0x0f, 0xb6, 0x11, //0x0000065e movzbl       (%rcx), %edx
	0x80, 0xfa, 0x22, //0x00000661 cmpb         $34, %dl
	0x0f, 0x84, 0x5a, 0x00, 0x00, 0x00, //0x00000664 je           LBB0_53
	0x4c, 0x8d, 0x48, 0xff, //0x0000066a leaq         $-1(%rax), %r9
	0x80, 0xfa, 0x5c, //0x0000066e cmpb         $92, %dl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00000671 je           LBB0_51
	0x4c, 0x89, 0xc8, //0x00000677 movq         %r9, %rax
	0x4c, 0x89, 0xf9, //0x0000067a movq         %r15, %rcx
	0x4d, 0x85, 0xc9, //0x0000067d testq        %r9, %r9
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x00000680 jne          LBB0_48
	0xe9, 0x16, 0x02, 0x00, 0x00, //0x00000686 jmp          LBB0_81
	//0x0000068b LBB0_51
	0x4d, 0x85, 0xc9, //0x0000068b testq        %r9, %r9
	0x0f, 0x84, 0x6f, 0xfe, 0xff, 0xff, //0x0000068e je           LBB0_36
	0x4d, 0x01, 0xf7, //0x00000694 addq         %r14, %r15
	0x49, 0x83, 0xf8, 0xff, //0x00000697 cmpq         $-1, %r8
	0x4d, 0x0f, 0x44, 0xdf, //0x0000069b cmoveq       %r15, %r11
	0x4d, 0x0f, 0x44, 0xc7, //0x0000069f cmoveq       %r15, %r8
	0x48, 0x83, 0xc1, 0x02, //0x000006a3 addq         $2, %rcx
	0x48, 0x83, 0xc0, 0xfe, //0x000006a7 addq         $-2, %rax
	0x49, 0x89, 0xc1, //0x000006ab movq         %rax, %r9
	0x48, 0x8b, 0x5d, 0xb8, //0x000006ae movq         $-72(%rbp), %rbx
	0x48, 0x8b, 0x75, 0xc0, //0x000006b2 movq         $-64(%rbp), %rsi
	0x4d, 0x85, 0xc9, //0x000006b6 testq        %r9, %r9
	0x0f, 0x85, 0x9b, 0xff, 0xff, 0xff, //0x000006b9 jne          LBB0_48
	0xe9, 0xdd, 0x01, 0x00, 0x00, //0x000006bf jmp          LBB0_81
	//0x000006c4 LBB0_53
	0x4c, 0x2b, 0x7d, 0xd0, //0x000006c4 subq         $-48(%rbp), %r15
	0x4c, 0x89, 0xff, //0x000006c8 movq         %r15, %rdi
	0xe9, 0x6d, 0xfd, 0xff, 0xff, //0x000006cb jmp          LBB0_27
	//0x000006d0 LBB0_55
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x000006d0 movq         $-1, %rdi
	0xe9, 0xc9, 0x01, 0x00, 0x00, //0x000006d7 jmp          LBB0_82
	//0x000006dc LBB0_56
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000006dc movq         $-1, %r11
	0x45, 0x31, 0xc0, //0x000006e3 xorl         %r8d, %r8d
	0x48, 0x83, 0xf8, 0x20, //0x000006e6 cmpq         $32, %rax
	0x0f, 0x83, 0x8c, 0xfd, 0xff, 0xff, //0x000006ea jae          LBB0_30
	//0x000006f0 LBB0_57
	0x48, 0x8b, 0x5d, 0xb8, //0x000006f0 movq         $-72(%rbp), %rbx
	0x48, 0x8b, 0x75, 0xc0, //0x000006f4 movq         $-64(%rbp), %rsi
	0x4d, 0x85, 0xc0, //0x000006f8 testq        %r8, %r8
	0x0f, 0x84, 0x3f, 0xff, 0xff, 0xff, //0x000006fb je           LBB0_46
	//0x00000701 LBB0_58
	0x48, 0x85, 0xc0, //0x00000701 testq        %rax, %rax
	0x0f, 0x84, 0x25, 0x02, 0x00, 0x00, //0x00000704 je           LBB0_88
	0x4c, 0x8b, 0x45, 0xd0, //0x0000070a movq         $-48(%rbp), %r8
	0x49, 0xf7, 0xd0, //0x0000070e notq         %r8
	0x49, 0x01, 0xc8, //0x00000711 addq         %rcx, %r8
	0x49, 0x83, 0xfb, 0xff, //0x00000714 cmpq         $-1, %r11
	0x4c, 0x89, 0xda, //0x00000718 movq         %r11, %rdx
	0x49, 0x0f, 0x44, 0xd0, //0x0000071b cmoveq       %r8, %rdx
	0x4d, 0x0f, 0x45, 0xc3, //0x0000071f cmovneq      %r11, %r8
	0x48, 0xff, 0xc1, //0x00000723 incq         %rcx
	0x48, 0xff, 0xc8, //0x00000726 decq         %rax
	0x49, 0x89, 0xd3, //0x00000729 movq         %rdx, %r11
	0x48, 0x8b, 0x5d, 0xb8, //0x0000072c movq         $-72(%rbp), %rbx
	0x48, 0x8b, 0x75, 0xc0, //0x00000730 movq         $-64(%rbp), %rsi
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00000734 movq         $-1, %rdi
	0x48, 0x85, 0xc0, //0x0000073b testq        %rax, %rax
	0x0f, 0x85, 0x0f, 0xff, 0xff, 0xff, //0x0000073e jne          LBB0_47
	0xe9, 0x58, 0x01, 0x00, 0x00, //0x00000744 jmp          LBB0_81
	//0x00000749 LBB0_60
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00000749 movq         $-1, %r11
	0x45, 0x31, 0xc9, //0x00000750 xorl         %r9d, %r9d
	0x48, 0x83, 0xf8, 0x20, //0x00000753 cmpq         $32, %rax
	0x0f, 0x83, 0xd0, 0xfd, 0xff, 0xff, //0x00000757 jae          LBB0_38
	0xe9, 0x7c, 0x00, 0x00, 0x00, //0x0000075d jmp          LBB0_68
	//0x00000762 LBB0_61
	0x45, 0x89, 0xcd, //0x00000762 movl         %r9d, %r13d
	0x41, 0xf7, 0xd5, //0x00000765 notl         %r13d
	0x45, 0x21, 0xfd, //0x00000768 andl         %r15d, %r13d
	0x47, 0x8d, 0x64, 0x2d, 0x00, //0x0000076b leal         (%r13,%r13), %r12d
	0x45, 0x09, 0xcc, //0x00000770 orl          %r9d, %r12d
	0xba, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000773 movl         $2863311530, %edx
	0x44, 0x31, 0xe2, //0x00000778 xorl         %r12d, %edx
	0x44, 0x21, 0xfa, //0x0000077b andl         %r15d, %edx
	0x81, 0xe2, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000077e andl         $-1431655766, %edx
	0x45, 0x31, 0xc9, //0x00000784 xorl         %r9d, %r9d
	0x44, 0x01, 0xea, //0x00000787 addl         %r13d, %edx
	0x41, 0x0f, 0x92, 0xc1, //0x0000078a setb         %r9b
	0x01, 0xd2, //0x0000078e addl         %edx, %edx
	0x81, 0xf2, 0x55, 0x55, 0x55, 0x55, //0x00000790 xorl         $1431655765, %edx
	0x44, 0x21, 0xe2, //0x00000796 andl         %r12d, %edx
	0xf7, 0xd2, //0x00000799 notl         %edx
	0x21, 0xd7, //0x0000079b andl         %edx, %edi
	0x49, 0xc1, 0xe6, 0x10, //0x0000079d shlq         $16, %r14
	0x48, 0x85, 0xff, //0x000007a1 testq        %rdi, %rdi
	0x0f, 0x85, 0x22, 0xfe, 0xff, 0xff, //0x000007a4 jne          LBB0_42
	//0x000007aa LBB0_62
	0xba, 0x40, 0x00, 0x00, 0x00, //0x000007aa movl         $64, %edx
	//0x000007af LBB0_63
	0x4d, 0x09, 0xc6, //0x000007af orq          %r8, %r14
	0x48, 0x85, 0xff, //0x000007b2 testq        %rdi, %rdi
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x000007b5 je           LBB0_66
	0x4d, 0x85, 0xf6, //0x000007bb testq        %r14, %r14
	0x0f, 0x84, 0xab, 0x00, 0x00, 0x00, //0x000007be je           LBB0_76
	0x49, 0x0f, 0xbc, 0xc6, //0x000007c4 bsfq         %r14, %rax
	0xe9, 0xa7, 0x00, 0x00, 0x00, //0x000007c8 jmp          LBB0_77
	//0x000007cd LBB0_66
	0x4d, 0x85, 0xf6, //0x000007cd testq        %r14, %r14
	0x0f, 0x85, 0xc4, 0x00, 0x00, 0x00, //0x000007d0 jne          LBB0_80
	0x48, 0x83, 0xc1, 0x20, //0x000007d6 addq         $32, %rcx
	0x48, 0x83, 0xc0, 0xe0, //0x000007da addq         $-32, %rax
	//0x000007de LBB0_68
	0x4d, 0x85, 0xc9, //0x000007de testq        %r9, %r9
	0x0f, 0x85, 0x08, 0x01, 0x00, 0x00, //0x000007e1 jne          LBB0_86
	0x4d, 0x89, 0xd8, //0x000007e7 movq         %r11, %r8
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x000007ea movq         $-1, %rdi
	0x48, 0x85, 0xc0, //0x000007f1 testq        %rax, %rax
	0x0f, 0x84, 0xa7, 0x00, 0x00, 0x00, //0x000007f4 je           LBB0_81
	//0x000007fa LBB0_70
	0x0f, 0xb6, 0x11, //0x000007fa movzbl       (%rcx), %edx
	0x80, 0xfa, 0x22, //0x000007fd cmpb         $34, %dl
	0x0f, 0x84, 0x85, 0x00, 0x00, 0x00, //0x00000800 je           LBB0_79
	0x80, 0xfa, 0x5c, //0x00000806 cmpb         $92, %dl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00000809 je           LBB0_74
	0x80, 0xfa, 0x20, //0x0000080f cmpb         $32, %dl
	0x0f, 0x82, 0x82, 0x00, 0x00, 0x00, //0x00000812 jb           LBB0_80
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000818 movq         $-1, %r9
	0xba, 0x01, 0x00, 0x00, 0x00, //0x0000081f movl         $1, %edx
	0x48, 0x01, 0xd1, //0x00000824 addq         %rdx, %rcx
	0x4c, 0x01, 0xc8, //0x00000827 addq         %r9, %rax
	0x0f, 0x85, 0xca, 0xff, 0xff, 0xff, //0x0000082a jne          LBB0_70
	0xe9, 0x6c, 0x00, 0x00, 0x00, //0x00000830 jmp          LBB0_81
	//0x00000835 LBB0_74
	0x48, 0x83, 0xf8, 0x01, //0x00000835 cmpq         $1, %rax
	0x0f, 0x84, 0x62, 0x00, 0x00, 0x00, //0x00000839 je           LBB0_81
	0x48, 0x89, 0xca, //0x0000083f movq         %rcx, %rdx
	0x48, 0x2b, 0x55, 0xd0, //0x00000842 subq         $-48(%rbp), %rdx
	0x49, 0x83, 0xf8, 0xff, //0x00000846 cmpq         $-1, %r8
	0x4c, 0x0f, 0x44, 0xda, //0x0000084a cmoveq       %rdx, %r11
	0x4c, 0x0f, 0x44, 0xc2, //0x0000084e cmoveq       %rdx, %r8
	0x49, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00000852 movq         $-2, %r9
	0xba, 0x02, 0x00, 0x00, 0x00, //0x00000859 movl         $2, %edx
	0x48, 0x01, 0xd1, //0x0000085e addq         %rdx, %rcx
	0x4c, 0x01, 0xc8, //0x00000861 addq         %r9, %rax
	0x0f, 0x85, 0x90, 0xff, 0xff, 0xff, //0x00000864 jne          LBB0_70
	0xe9, 0x32, 0x00, 0x00, 0x00, //0x0000086a jmp          LBB0_81
	//0x0000086f LBB0_76
	0xb8, 0x40, 0x00, 0x00, 0x00, //0x0000086f movl         $64, %eax
	//0x00000874 LBB0_77
	0x48, 0x39, 0xd0, //0x00000874 cmpq         %rdx, %rax
	0x0f, 0x82, 0x1d, 0x00, 0x00, 0x00, //0x00000877 jb           LBB0_80
	0x48, 0x2b, 0x4d, 0xd0, //0x0000087d subq         $-48(%rbp), %rcx
	0x48, 0x8d, 0x7c, 0x11, 0x01, //0x00000881 leaq         $1(%rcx,%rdx), %rdi
	0xe9, 0xb2, 0xfb, 0xff, 0xff, //0x00000886 jmp          LBB0_27
	//0x0000088b LBB0_79
	0x48, 0x2b, 0x4d, 0xd0, //0x0000088b subq         $-48(%rbp), %rcx
	0x48, 0xff, 0xc1, //0x0000088f incq         %rcx
	0x48, 0x89, 0xcf, //0x00000892 movq         %rcx, %rdi
	0xe9, 0xa3, 0xfb, 0xff, 0xff, //0x00000895 jmp          LBB0_27
	//0x0000089a LBB0_80
	0x48, 0xc7, 0xc7, 0xfe, 0xff, 0xff, 0xff, //0x0000089a movq         $-2, %rdi
	//0x000008a1 LBB0_81
	0x4c, 0x8b, 0x55, 0xc8, //0x000008a1 movq         $-56(%rbp), %r10
	//0x000008a5 LBB0_82
	0x4c, 0x89, 0x16, //0x000008a5 movq         %r10, (%rsi)
	0x48, 0x89, 0x3b, //0x000008a8 movq         %rdi, (%rbx)
	//0x000008ab LBB0_83
	0x48, 0x83, 0xc4, 0x28, //0x000008ab addq         $40, %rsp
	0x5b, //0x000008af popq         %rbx
	0x41, 0x5c, //0x000008b0 popq         %r12
	0x41, 0x5d, //0x000008b2 popq         %r13
	0x41, 0x5e, //0x000008b4 popq         %r14
	0x41, 0x5f, //0x000008b6 popq         %r15
	0x5d, //0x000008b8 popq         %rbp
	0xc3, //0x000008b9 retq         
	//0x000008ba LBB0_84
	0x48, 0x89, 0xca, //0x000008ba movq         %rcx, %rdx
	0x48, 0x2b, 0x55, 0xd0, //0x000008bd subq         $-48(%rbp), %rdx
	0x4d, 0x0f, 0xbc, 0xdf, //0x000008c1 bsfq         %r15, %r11
	0x49, 0x01, 0xd3, //0x000008c5 addq         %rdx, %r11
	0x4c, 0x09, 0xcf, //0x000008c8 orq          %r9, %rdi
	0x4c, 0x89, 0xfa, //0x000008cb movq         %r15, %rdx
	0x4c, 0x09, 0xc2, //0x000008ce orq          %r8, %rdx
	0x0f, 0x84, 0x13, 0xfc, 0xff, 0xff, //0x000008d1 je           LBB0_33
	0xe9, 0x10, 0xfd, 0xff, 0xff, //0x000008d7 jmp          LBB0_44
	//0x000008dc LBB0_85
	0x48, 0x89, 0xca, //0x000008dc movq         %rcx, %rdx
	0x48, 0x2b, 0x55, 0xd0, //0x000008df subq         $-48(%rbp), %rdx
	0x4d, 0x0f, 0xbc, 0xdf, //0x000008e3 bsfq         %r15, %r11
	0x49, 0x01, 0xd3, //0x000008e7 addq         %rdx, %r11
	0xe9, 0xbd, 0xfc, 0xff, 0xff, //0x000008ea jmp          LBB0_40
	//0x000008ef LBB0_86
	0x48, 0x85, 0xc0, //0x000008ef testq        %rax, %rax
	0x0f, 0x84, 0x43, 0x00, 0x00, 0x00, //0x000008f2 je           LBB0_89
	0x4c, 0x8b, 0x45, 0xd0, //0x000008f8 movq         $-48(%rbp), %r8
	0x49, 0xf7, 0xd0, //0x000008fc notq         %r8
	0x49, 0x01, 0xc8, //0x000008ff addq         %rcx, %r8
	0x49, 0x83, 0xfb, 0xff, //0x00000902 cmpq         $-1, %r11
	0x4c, 0x89, 0xda, //0x00000906 movq         %r11, %rdx
	0x49, 0x0f, 0x44, 0xd0, //0x00000909 cmoveq       %r8, %rdx
	0x4d, 0x0f, 0x45, 0xc3, //0x0000090d cmovneq      %r11, %r8
	0x48, 0xff, 0xc1, //0x00000911 incq         %rcx
	0x48, 0xff, 0xc8, //0x00000914 decq         %rax
	0x49, 0x89, 0xd3, //0x00000917 movq         %rdx, %r11
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x0000091a movq         $-1, %rdi
	0x48, 0x85, 0xc0, //0x00000921 testq        %rax, %rax
	0x0f, 0x85, 0xd0, 0xfe, 0xff, 0xff, //0x00000924 jne          LBB0_70
	0xe9, 0x72, 0xff, 0xff, 0xff, //0x0000092a jmp          LBB0_81
	//0x0000092f LBB0_88
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x0000092f movq         $-1, %rdi
	0xe9, 0xc8, 0xfb, 0xff, 0xff, //0x00000936 jmp          LBB0_36
	//0x0000093b LBB0_89
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x0000093b movq         $-1, %rdi
	0xe9, 0x5a, 0xff, 0xff, 0xff, //0x00000942 jmp          LBB0_81
	0x00, //0x00000947 .p2align 2, 0x00
	//0x00000948 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00000948 .long 2
}
 
