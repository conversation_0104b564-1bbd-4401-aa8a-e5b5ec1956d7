// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__vstring = 48
)

const (
    _stack__vstring = 88
)

const (
    _size__vstring = 2328
)

var (
    _pcsp__vstring = [][2]uint32{
        {1, 0},
        {4, 8},
        {6, 16},
        {8, 24},
        {10, 32},
        {12, 40},
        {13, 48},
        {2171, 88},
        {2175, 48},
        {2176, 40},
        {2178, 32},
        {2180, 24},
        {2182, 16},
        {2184, 8},
        {2185, 0},
        {2327, 88},
    }
)

var _cfunc_vstring = []loader.CFunc{
    {"_vstring_entry", 0,  _entry__vstring, 0, nil},
    {"_vstring", _entry__vstring, _size__vstring, _stack__vstring, _pcsp__vstring},
}
