// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx

var _text_vsigned = []byte{
	// .p2align 4, 0x90
	// _vsigned
	0x55, // pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000001 movq         %rsp, %rbp
	0x53, //0x00000004 pushq        %rbx
	0x48, 0x8b, 0x1e, //0x00000005 movq         (%rsi), %rbx
	0x4c, 0x8b, 0x07, //0x00000008 movq         (%rdi), %r8
	0x4c, 0x8b, 0x57, 0x08, //0x0000000b movq         $8(%rdi), %r10
	0x48, 0xc7, 0x02, 0x09, 0x00, 0x00, 0x00, //0x0000000f movq         $9, (%rdx)
	0xc5, 0xf8, 0x57, 0xc0, //0x00000016 vxorps       %xmm0, %xmm0, %xmm0
	0xc5, 0xf8, 0x11, 0x42, 0x08, //0x0000001a vmovups      %xmm0, $8(%rdx)
	0x48, 0x8b, 0x0e, //0x0000001f movq         (%rsi), %rcx
	0x48, 0x89, 0x4a, 0x18, //0x00000022 movq         %rcx, $24(%rdx)
	0x4c, 0x39, 0xd3, //0x00000026 cmpq         %r10, %rbx
	0x0f, 0x83, 0x44, 0x00, 0x00, 0x00, //0x00000029 jae          LBB0_1
	0x41, 0x8a, 0x0c, 0x18, //0x0000002f movb         (%r8,%rbx), %cl
	0x41, 0xb9, 0x01, 0x00, 0x00, 0x00, //0x00000033 movl         $1, %r9d
	0x80, 0xf9, 0x2d, //0x00000039 cmpb         $45, %cl
	0x0f, 0x85, 0x17, 0x00, 0x00, 0x00, //0x0000003c jne          LBB0_5
	0x48, 0xff, 0xc3, //0x00000042 incq         %rbx
	0x4c, 0x39, 0xd3, //0x00000045 cmpq         %r10, %rbx
	0x0f, 0x83, 0x25, 0x00, 0x00, 0x00, //0x00000048 jae          LBB0_1
	0x41, 0x8a, 0x0c, 0x18, //0x0000004e movb         (%r8,%rbx), %cl
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000052 movq         $-1, %r9
	//0x00000059 LBB0_5
	0x8d, 0x79, 0xd0, //0x00000059 leal         $-48(%rcx), %edi
	0x40, 0x80, 0xff, 0x0a, //0x0000005c cmpb         $10, %dil
	0x0f, 0x82, 0x1a, 0x00, 0x00, 0x00, //0x00000060 jb           LBB0_7
	0x48, 0x89, 0x1e, //0x00000066 movq         %rbx, (%rsi)
	0x48, 0xc7, 0x02, 0xfe, 0xff, 0xff, 0xff, //0x00000069 movq         $-2, (%rdx)
	0x5b, //0x00000070 popq         %rbx
	0x5d, //0x00000071 popq         %rbp
	0xc3, //0x00000072 retq         
	//0x00000073 LBB0_1
	0x4c, 0x89, 0x16, //0x00000073 movq         %r10, (%rsi)
	0x48, 0xc7, 0x02, 0xff, 0xff, 0xff, 0xff, //0x00000076 movq         $-1, (%rdx)
	0x5b, //0x0000007d popq         %rbx
	0x5d, //0x0000007e popq         %rbp
	0xc3, //0x0000007f retq         
	//0x00000080 LBB0_7
	0x80, 0xf9, 0x30, //0x00000080 cmpb         $48, %cl
	0x0f, 0x85, 0x35, 0x00, 0x00, 0x00, //0x00000083 jne          LBB0_8
	0x48, 0x8d, 0x7b, 0x01, //0x00000089 leaq         $1(%rbx), %rdi
	0x4c, 0x39, 0xd3, //0x0000008d cmpq         %r10, %rbx
	0x0f, 0x83, 0x71, 0x00, 0x00, 0x00, //0x00000090 jae          LBB0_17
	0x41, 0x8a, 0x0c, 0x38, //0x00000096 movb         (%r8,%rdi), %cl
	0x80, 0xc1, 0xd2, //0x0000009a addb         $-46, %cl
	0x80, 0xf9, 0x37, //0x0000009d cmpb         $55, %cl
	0x0f, 0x87, 0x61, 0x00, 0x00, 0x00, //0x000000a0 ja           LBB0_17
	0x44, 0x0f, 0xb6, 0xd9, //0x000000a6 movzbl       %cl, %r11d
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x000000aa movabsq      $36028797027352577, %rcx
	0x4c, 0x0f, 0xa3, 0xd9, //0x000000b4 btq          %r11, %rcx
	0x0f, 0x83, 0x49, 0x00, 0x00, 0x00, //0x000000b8 jae          LBB0_17
	//0x000000be LBB0_8
	0x31, 0xff, //0x000000be xorl         %edi, %edi
	//0x000000c0 .p2align 4, 0x90
	//0x000000c0 LBB0_9
	0x4c, 0x39, 0xd3, //0x000000c0 cmpq         %r10, %rbx
	0x0f, 0x83, 0x6c, 0x00, 0x00, 0x00, //0x000000c3 jae          LBB0_22
	0x49, 0x0f, 0xbe, 0x0c, 0x18, //0x000000c9 movsbq       (%r8,%rbx), %rcx
	0x8d, 0x41, 0xd0, //0x000000ce leal         $-48(%rcx), %eax
	0x3c, 0x09, //0x000000d1 cmpb         $9, %al
	0x0f, 0x87, 0x34, 0x00, 0x00, 0x00, //0x000000d3 ja           LBB0_18
	0x48, 0x6b, 0xff, 0x0a, //0x000000d9 imulq        $10, %rdi, %rdi
	0x0f, 0x80, 0x14, 0x00, 0x00, 0x00, //0x000000dd jo           LBB0_13
	0x48, 0xff, 0xc3, //0x000000e3 incq         %rbx
	0x48, 0x83, 0xc1, 0xd0, //0x000000e6 addq         $-48, %rcx
	0x49, 0x0f, 0xaf, 0xc9, //0x000000ea imulq        %r9, %rcx
	0x48, 0x01, 0xcf, //0x000000ee addq         %rcx, %rdi
	0x0f, 0x81, 0xc9, 0xff, 0xff, 0xff, //0x000000f1 jno          LBB0_9
	//0x000000f7 LBB0_13
	0x48, 0xff, 0xcb, //0x000000f7 decq         %rbx
	0x48, 0x89, 0x1e, //0x000000fa movq         %rbx, (%rsi)
	0x48, 0xc7, 0x02, 0xfb, 0xff, 0xff, 0xff, //0x000000fd movq         $-5, (%rdx)
	0x5b, //0x00000104 popq         %rbx
	0x5d, //0x00000105 popq         %rbp
	0xc3, //0x00000106 retq         
	//0x00000107 LBB0_17
	0x48, 0x89, 0x3e, //0x00000107 movq         %rdi, (%rsi)
	0x5b, //0x0000010a popq         %rbx
	0x5d, //0x0000010b popq         %rbp
	0xc3, //0x0000010c retq         
	//0x0000010d LBB0_18
	0x80, 0xf9, 0x65, //0x0000010d cmpb         $101, %cl
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00000110 je           LBB0_21
	0x80, 0xf9, 0x45, //0x00000116 cmpb         $69, %cl
	0x0f, 0x84, 0x09, 0x00, 0x00, 0x00, //0x00000119 je           LBB0_21
	0x80, 0xf9, 0x2e, //0x0000011f cmpb         $46, %cl
	0x0f, 0x85, 0x0d, 0x00, 0x00, 0x00, //0x00000122 jne          LBB0_22
	//0x00000128 LBB0_21
	0x48, 0x89, 0x1e, //0x00000128 movq         %rbx, (%rsi)
	0x48, 0xc7, 0x02, 0xfa, 0xff, 0xff, 0xff, //0x0000012b movq         $-6, (%rdx)
	0x5b, //0x00000132 popq         %rbx
	0x5d, //0x00000133 popq         %rbp
	0xc3, //0x00000134 retq         
	//0x00000135 LBB0_22
	0x48, 0x89, 0x1e, //0x00000135 movq         %rbx, (%rsi)
	0x48, 0x89, 0x7a, 0x10, //0x00000138 movq         %rdi, $16(%rdx)
	0x5b, //0x0000013c popq         %rbx
	0x5d, //0x0000013d popq         %rbp
	0xc3, //0x0000013e retq         
	0x00, //0x0000013f .p2align 2, 0x00
	//0x00000140 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00000140 .long 2
}
 
