// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx

var _text_skip_object = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // .quad 1
	0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000008 .quad 6
	//0x00000010 LCPI0_1
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000010 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x00000020 LCPI0_2
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000020 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000030 LCPI0_3
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000030 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000040 LCPI0_4
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000040 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	//0x00000050 LCPI0_5
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x00000050 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	//0x00000060 LCPI0_6
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000060 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x00000070 LCPI0_7
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000070 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000080 LCPI0_8
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000080 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x00000090 LCPI0_9
	0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, //0x00000090 QUAD $0x6565656565656565; QUAD $0x6565656565656565  // .space 16, 'eeeeeeeeeeeeeeee'
	//0x000000a0 .p2align 4, 0x90
	//0x000000a0 _skip_object
	0x55, //0x000000a0 pushq        %rbp
	0x48, 0x89, 0xe5, //0x000000a1 movq         %rsp, %rbp
	0x41, 0x57, //0x000000a4 pushq        %r15
	0x41, 0x56, //0x000000a6 pushq        %r14
	0x41, 0x55, //0x000000a8 pushq        %r13
	0x41, 0x54, //0x000000aa pushq        %r12
	0x53, //0x000000ac pushq        %rbx
	0x48, 0x83, 0xec, 0x68, //0x000000ad subq         $104, %rsp
	0x48, 0x89, 0x4d, 0x80, //0x000000b1 movq         %rcx, $-128(%rbp)
	0x49, 0x89, 0xd1, //0x000000b5 movq         %rdx, %r9
	0xc5, 0xf8, 0x10, 0x05, 0x40, 0xff, 0xff, 0xff, //0x000000b8 vmovups      $-192(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0xc5, 0xf8, 0x11, 0x02, //0x000000c0 vmovups      %xmm0, (%rdx)
	0x48, 0x89, 0x7d, 0x90, //0x000000c4 movq         %rdi, $-112(%rbp)
	0x4c, 0x8b, 0x27, //0x000000c8 movq         (%rdi), %r12
	0x4c, 0x89, 0xe0, //0x000000cb movq         %r12, %rax
	0x48, 0xf7, 0xd0, //0x000000ce notq         %rax
	0x48, 0x89, 0x45, 0xa0, //0x000000d1 movq         %rax, $-96(%rbp)
	0x41, 0xba, 0x01, 0x00, 0x00, 0x00, //0x000000d5 movl         $1, %r10d
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x000000db movl         $1, %eax
	0x4c, 0x29, 0xe0, //0x000000e0 subq         %r12, %rax
	0x48, 0x89, 0x45, 0x98, //0x000000e3 movq         %rax, $-104(%rbp)
	0x49, 0x8d, 0x44, 0x24, 0x40, //0x000000e7 leaq         $64(%r12), %rax
	0x48, 0x89, 0x45, 0x88, //0x000000ec movq         %rax, $-120(%rbp)
	0x4c, 0x8b, 0x1e, //0x000000f0 movq         (%rsi), %r11
	0x49, 0x8d, 0x44, 0x24, 0x05, //0x000000f3 leaq         $5(%r12), %rax
	0x48, 0x89, 0x85, 0x70, 0xff, 0xff, 0xff, //0x000000f8 movq         %rax, $-144(%rbp)
	0x48, 0xc7, 0x85, 0x78, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x000000ff movq         $-1, $-136(%rbp)
	0xc5, 0xfa, 0x6f, 0x05, 0xfe, 0xfe, 0xff, 0xff, //0x0000010a vmovdqu      $-258(%rip), %xmm0  /* LCPI0_1+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x0d, 0x06, 0xff, 0xff, 0xff, //0x00000112 vmovdqu      $-250(%rip), %xmm1  /* LCPI0_2+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x3d, 0x0e, 0xff, 0xff, 0xff, //0x0000011a vmovdqu      $-242(%rip), %xmm15  /* LCPI0_3+0(%rip) */
	0xc5, 0xe1, 0x76, 0xdb, //0x00000122 vpcmpeqd     %xmm3, %xmm3, %xmm3
	0xc5, 0x7a, 0x6f, 0x05, 0x12, 0xff, 0xff, 0xff, //0x00000126 vmovdqu      $-238(%rip), %xmm8  /* LCPI0_4+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x0d, 0x1a, 0xff, 0xff, 0xff, //0x0000012e vmovdqu      $-230(%rip), %xmm9  /* LCPI0_5+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x15, 0x22, 0xff, 0xff, 0xff, //0x00000136 vmovdqu      $-222(%rip), %xmm10  /* LCPI0_6+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x1d, 0x2a, 0xff, 0xff, 0xff, //0x0000013e vmovdqu      $-214(%rip), %xmm11  /* LCPI0_7+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x25, 0xe2, 0xfe, 0xff, 0xff, //0x00000146 vmovdqu      $-286(%rip), %xmm12  /* LCPI0_3+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x2d, 0x2a, 0xff, 0xff, 0xff, //0x0000014e vmovdqu      $-214(%rip), %xmm13  /* LCPI0_8+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x35, 0x32, 0xff, 0xff, 0xff, //0x00000156 vmovdqu      $-206(%rip), %xmm14  /* LCPI0_9+0(%rip) */
	0x48, 0x89, 0x75, 0xc8, //0x0000015e movq         %rsi, $-56(%rbp)
	0x4c, 0x89, 0x65, 0xd0, //0x00000162 movq         %r12, $-48(%rbp)
	0x48, 0x89, 0x55, 0xc0, //0x00000166 movq         %rdx, $-64(%rbp)
	0xe9, 0x57, 0x00, 0x00, 0x00, //0x0000016a jmp          LBB0_4
	//0x0000016f LBB0_257
	0x48, 0x85, 0xc0, //0x0000016f testq        %rax, %rax
	0x49, 0x8d, 0x45, 0xff, //0x00000172 leaq         $-1(%r13), %rax
	0x49, 0xf7, 0xd5, //0x00000176 notq         %r13
	0x4c, 0x0f, 0x48, 0xe9, //0x00000179 cmovsq       %rcx, %r13
	0x49, 0x39, 0xc3, //0x0000017d cmpq         %rax, %r11
	0x49, 0x89, 0xcb, //0x00000180 movq         %rcx, %r11
	0x4d, 0x0f, 0x44, 0xdd, //0x00000183 cmoveq       %r13, %r11
	0x4c, 0x8b, 0x65, 0xd0, //0x00000187 movq         $-48(%rbp), %r12
	0x4d, 0x85, 0xdb, //0x0000018b testq        %r11, %r11
	0x0f, 0x88, 0xfb, 0x21, 0x00, 0x00, //0x0000018e js           LBB0_419
	//0x00000194 LBB0_258
	0x4d, 0x01, 0xfb, //0x00000194 addq         %r15, %r11
	//0x00000197 LBB0_1
	0x4c, 0x89, 0x1e, //0x00000197 movq         %r11, (%rsi)
	0x4c, 0x89, 0xf8, //0x0000019a movq         %r15, %rax
	0x4d, 0x85, 0xff, //0x0000019d testq        %r15, %r15
	0x0f, 0x88, 0xfc, 0x21, 0x00, 0x00, //0x000001a0 js           LBB0_423
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000001a6 .p2align 4, 0x90
	//0x000001b0 LBB0_2
	0x49, 0x8b, 0x11, //0x000001b0 movq         (%r9), %rdx
	0x49, 0x89, 0xd2, //0x000001b3 movq         %rdx, %r10
	0x48, 0x8b, 0x85, 0x78, 0xff, 0xff, 0xff, //0x000001b6 movq         $-136(%rbp), %rax
	0x48, 0x85, 0xd2, //0x000001bd testq        %rdx, %rdx
	0x0f, 0x84, 0xdc, 0x21, 0x00, 0x00, //0x000001c0 je           LBB0_423
	//0x000001c6 LBB0_4
	0x48, 0x8b, 0x45, 0x90, //0x000001c6 movq         $-112(%rbp), %rax
	0x48, 0x8b, 0x40, 0x08, //0x000001ca movq         $8(%rax), %rax
	0x4c, 0x89, 0xdb, //0x000001ce movq         %r11, %rbx
	0x48, 0x29, 0xc3, //0x000001d1 subq         %rax, %rbx
	0x0f, 0x83, 0x36, 0x00, 0x00, 0x00, //0x000001d4 jae          LBB0_9
	0x43, 0x8a, 0x14, 0x1c, //0x000001da movb         (%r12,%r11), %dl
	0x80, 0xfa, 0x0d, //0x000001de cmpb         $13, %dl
	0x0f, 0x84, 0x29, 0x00, 0x00, 0x00, //0x000001e1 je           LBB0_9
	0x80, 0xfa, 0x20, //0x000001e7 cmpb         $32, %dl
	0x0f, 0x84, 0x20, 0x00, 0x00, 0x00, //0x000001ea je           LBB0_9
	0x80, 0xc2, 0xf7, //0x000001f0 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x000001f3 cmpb         $1, %dl
	0x0f, 0x86, 0x14, 0x00, 0x00, 0x00, //0x000001f6 jbe          LBB0_9
	0x4d, 0x89, 0xdf, //0x000001fc movq         %r11, %r15
	0xe9, 0x39, 0x01, 0x00, 0x00, //0x000001ff jmp          LBB0_30
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000204 .p2align 4, 0x90
	//0x00000210 LBB0_9
	0x4d, 0x8d, 0x7b, 0x01, //0x00000210 leaq         $1(%r11), %r15
	0x49, 0x39, 0xc7, //0x00000214 cmpq         %rax, %r15
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000217 jae          LBB0_13
	0x43, 0x8a, 0x14, 0x3c, //0x0000021d movb         (%r12,%r15), %dl
	0x80, 0xfa, 0x0d, //0x00000221 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000224 je           LBB0_13
	0x80, 0xfa, 0x20, //0x0000022a cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000022d je           LBB0_13
	0x80, 0xc2, 0xf7, //0x00000233 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00000236 cmpb         $1, %dl
	0x0f, 0x87, 0xfe, 0x00, 0x00, 0x00, //0x00000239 ja           LBB0_30
	0x90, //0x0000023f .p2align 4, 0x90
	//0x00000240 LBB0_13
	0x4d, 0x8d, 0x7b, 0x02, //0x00000240 leaq         $2(%r11), %r15
	0x49, 0x39, 0xc7, //0x00000244 cmpq         %rax, %r15
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000247 jae          LBB0_17
	0x43, 0x8a, 0x14, 0x3c, //0x0000024d movb         (%r12,%r15), %dl
	0x80, 0xfa, 0x0d, //0x00000251 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000254 je           LBB0_17
	0x80, 0xfa, 0x20, //0x0000025a cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000025d je           LBB0_17
	0x80, 0xc2, 0xf7, //0x00000263 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00000266 cmpb         $1, %dl
	0x0f, 0x87, 0xce, 0x00, 0x00, 0x00, //0x00000269 ja           LBB0_30
	0x90, //0x0000026f .p2align 4, 0x90
	//0x00000270 LBB0_17
	0x4d, 0x8d, 0x7b, 0x03, //0x00000270 leaq         $3(%r11), %r15
	0x49, 0x39, 0xc7, //0x00000274 cmpq         %rax, %r15
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000277 jae          LBB0_21
	0x43, 0x8a, 0x14, 0x3c, //0x0000027d movb         (%r12,%r15), %dl
	0x80, 0xfa, 0x0d, //0x00000281 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000284 je           LBB0_21
	0x80, 0xfa, 0x20, //0x0000028a cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000028d je           LBB0_21
	0x80, 0xc2, 0xf7, //0x00000293 addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00000296 cmpb         $1, %dl
	0x0f, 0x87, 0x9e, 0x00, 0x00, 0x00, //0x00000299 ja           LBB0_30
	0x90, //0x0000029f .p2align 4, 0x90
	//0x000002a0 LBB0_21
	0x49, 0x8d, 0x53, 0x04, //0x000002a0 leaq         $4(%r11), %rdx
	0x48, 0x39, 0xd0, //0x000002a4 cmpq         %rdx, %rax
	0x0f, 0x86, 0xa1, 0x20, 0x00, 0x00, //0x000002a7 jbe          LBB0_413
	0x48, 0x39, 0xd0, //0x000002ad cmpq         %rdx, %rax
	0x0f, 0x84, 0x5a, 0x00, 0x00, 0x00, //0x000002b0 je           LBB0_27
	0x49, 0x8d, 0x14, 0x04, //0x000002b6 leaq         (%r12,%rax), %rdx
	0x48, 0x83, 0xc3, 0x04, //0x000002ba addq         $4, %rbx
	0x4c, 0x03, 0x9d, 0x70, 0xff, 0xff, 0xff, //0x000002be addq         $-144(%rbp), %r11
	0x4d, 0x89, 0xdf, //0x000002c5 movq         %r11, %r15
	0x48, 0xb9, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000002c8 movabsq      $4294977024, %rcx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000002d2 .p2align 4, 0x90
	//0x000002e0 LBB0_24
	0x41, 0x0f, 0xbe, 0x7f, 0xff, //0x000002e0 movsbl       $-1(%r15), %edi
	0x83, 0xff, 0x20, //0x000002e5 cmpl         $32, %edi
	0x0f, 0x87, 0x42, 0x00, 0x00, 0x00, //0x000002e8 ja           LBB0_29
	0x48, 0x0f, 0xa3, 0xf9, //0x000002ee btq          %rdi, %rcx
	0x0f, 0x83, 0x38, 0x00, 0x00, 0x00, //0x000002f2 jae          LBB0_29
	0x49, 0xff, 0xc7, //0x000002f8 incq         %r15
	0x48, 0xff, 0xc3, //0x000002fb incq         %rbx
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x000002fe jne          LBB0_24
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00000304 jmp          LBB0_28
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000309 .p2align 4, 0x90
	//0x00000310 LBB0_27
	0x4c, 0x01, 0xe2, //0x00000310 addq         %r12, %rdx
	//0x00000313 LBB0_28
	0x4c, 0x29, 0xe2, //0x00000313 subq         %r12, %rdx
	0x49, 0x89, 0xd7, //0x00000316 movq         %rdx, %r15
	0x49, 0x39, 0xc7, //0x00000319 cmpq         %rax, %r15
	0x0f, 0x82, 0x1b, 0x00, 0x00, 0x00, //0x0000031c jb           LBB0_30
	0xe9, 0x2a, 0x20, 0x00, 0x00, //0x00000322 jmp          LBB0_414
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000327 .p2align 4, 0x90
	//0x00000330 LBB0_29
	0x4c, 0x03, 0x7d, 0xa0, //0x00000330 addq         $-96(%rbp), %r15
	0x49, 0x39, 0xc7, //0x00000334 cmpq         %rax, %r15
	0x0f, 0x83, 0x14, 0x20, 0x00, 0x00, //0x00000337 jae          LBB0_414
	//0x0000033d LBB0_30
	0x4d, 0x8d, 0x5f, 0x01, //0x0000033d leaq         $1(%r15), %r11
	0x4c, 0x89, 0x1e, //0x00000341 movq         %r11, (%rsi)
	0x43, 0x0f, 0xbe, 0x1c, 0x3c, //0x00000344 movsbl       (%r12,%r15), %ebx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000349 movq         $-1, %rax
	0x85, 0xdb, //0x00000350 testl        %ebx, %ebx
	0x0f, 0x84, 0x4a, 0x20, 0x00, 0x00, //0x00000352 je           LBB0_423
	0x4d, 0x89, 0xf8, //0x00000358 movq         %r15, %r8
	0x49, 0xf7, 0xd0, //0x0000035b notq         %r8
	0x49, 0x8d, 0x52, 0xff, //0x0000035e leaq         $-1(%r10), %rdx
	0x43, 0x8b, 0x3c, 0xd1, //0x00000362 movl         (%r9,%r10,8), %edi
	0x48, 0x8b, 0x8d, 0x78, 0xff, 0xff, 0xff, //0x00000366 movq         $-136(%rbp), %rcx
	0x48, 0x83, 0xf9, 0xff, //0x0000036d cmpq         $-1, %rcx
	0x49, 0x0f, 0x44, 0xcf, //0x00000371 cmoveq       %r15, %rcx
	0x48, 0x89, 0x8d, 0x78, 0xff, 0xff, 0xff, //0x00000375 movq         %rcx, $-136(%rbp)
	0xff, 0xcf, //0x0000037c decl         %edi
	0x83, 0xff, 0x05, //0x0000037e cmpl         $5, %edi
	0x0f, 0x87, 0x27, 0x00, 0x00, 0x00, //0x00000381 ja           LBB0_36
	0x48, 0x8d, 0x0d, 0x46, 0x22, 0x00, 0x00, //0x00000387 leaq         $8774(%rip), %rcx  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x3c, 0xb9, //0x0000038e movslq       (%rcx,%rdi,4), %rdi
	0x48, 0x01, 0xcf, //0x00000392 addq         %rcx, %rdi
	0xff, 0xe7, //0x00000395 jmpq         *%rdi
	//0x00000397 LBB0_33
	0x83, 0xfb, 0x2c, //0x00000397 cmpl         $44, %ebx
	0x0f, 0x84, 0xb6, 0x04, 0x00, 0x00, //0x0000039a je           LBB0_100
	0x83, 0xfb, 0x5d, //0x000003a0 cmpl         $93, %ebx
	0x0f, 0x84, 0x92, 0x04, 0x00, 0x00, //0x000003a3 je           LBB0_35
	0xe9, 0xed, 0x1f, 0x00, 0x00, //0x000003a9 jmp          LBB0_422
	//0x000003ae LBB0_36
	0x49, 0x89, 0x11, //0x000003ae movq         %rdx, (%r9)
	0x83, 0xfb, 0x7b, //0x000003b1 cmpl         $123, %ebx
	0x0f, 0x86, 0x19, 0x02, 0x00, 0x00, //0x000003b4 jbe          LBB0_63
	0xe9, 0xdc, 0x1f, 0x00, 0x00, //0x000003ba jmp          LBB0_422
	//0x000003bf LBB0_37
	0x83, 0xfb, 0x2c, //0x000003bf cmpl         $44, %ebx
	0x0f, 0x85, 0x6a, 0x04, 0x00, 0x00, //0x000003c2 jne          LBB0_38
	0x49, 0x81, 0xfa, 0xff, 0x0f, 0x00, 0x00, //0x000003c8 cmpq         $4095, %r10
	0x0f, 0x8f, 0x88, 0x1f, 0x00, 0x00, //0x000003cf jg           LBB0_441
	0x49, 0x8d, 0x42, 0x01, //0x000003d5 leaq         $1(%r10), %rax
	0x49, 0x89, 0x01, //0x000003d9 movq         %rax, (%r9)
	0x4b, 0xc7, 0x44, 0xd1, 0x08, 0x03, 0x00, 0x00, 0x00, //0x000003dc movq         $3, $8(%r9,%r10,8)
	0xe9, 0xc6, 0xfd, 0xff, 0xff, //0x000003e5 jmp          LBB0_2
	//0x000003ea LBB0_39
	0x80, 0xfb, 0x22, //0x000003ea cmpb         $34, %bl
	0x0f, 0x85, 0xa8, 0x1f, 0x00, 0x00, //0x000003ed jne          LBB0_422
	0x4b, 0xc7, 0x04, 0xd1, 0x04, 0x00, 0x00, 0x00, //0x000003f3 movq         $4, (%r9,%r10,8)
	0x48, 0x8b, 0x45, 0x90, //0x000003fb movq         $-112(%rbp), %rax
	0x48, 0x8b, 0x50, 0x08, //0x000003ff movq         $8(%rax), %rdx
	0xf6, 0x45, 0x80, 0x20, //0x00000403 testb        $32, $-128(%rbp)
	0x4c, 0x89, 0x5d, 0xa8, //0x00000407 movq         %r11, $-88(%rbp)
	0x48, 0x89, 0x55, 0xb0, //0x0000040b movq         %rdx, $-80(%rbp)
	0x0f, 0x85, 0x63, 0x04, 0x00, 0x00, //0x0000040f jne          LBB0_104
	0x49, 0x89, 0xd5, //0x00000415 movq         %rdx, %r13
	0x4d, 0x29, 0xdd, //0x00000418 subq         %r11, %r13
	0x0f, 0x84, 0x30, 0x21, 0x00, 0x00, //0x0000041b je           LBB0_456
	0x4c, 0x89, 0xd8, //0x00000421 movq         %r11, %rax
	0x4d, 0x01, 0xe3, //0x00000424 addq         %r12, %r11
	0x49, 0x83, 0xfd, 0x40, //0x00000427 cmpq         $64, %r13
	0x0f, 0x82, 0x0b, 0x1a, 0x00, 0x00, //0x0000042b jb           LBB0_353
	0x45, 0x89, 0xea, //0x00000431 movl         %r13d, %r10d
	0x41, 0x83, 0xe2, 0x3f, //0x00000434 andl         $63, %r10d
	0x4a, 0x8d, 0x4c, 0x02, 0xc0, //0x00000438 leaq         $-64(%rdx,%r8), %rcx
	0x48, 0x83, 0xe1, 0xc0, //0x0000043d andq         $-64, %rcx
	0x48, 0x01, 0xc1, //0x00000441 addq         %rax, %rcx
	0x48, 0x03, 0x4d, 0x88, //0x00000444 addq         $-120(%rbp), %rcx
	0x48, 0x89, 0x4d, 0xb8, //0x00000448 movq         %rcx, $-72(%rbp)
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000044c movq         $-1, %r9
	0x45, 0x31, 0xe4, //0x00000453 xorl         %r12d, %r12d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000456 .p2align 4, 0x90
	//0x00000460 LBB0_44
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x00000460 vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x00000465 vmovdqu      $16(%r11), %xmm4
	0xc4, 0xc1, 0x7a, 0x6f, 0x6b, 0x20, //0x0000046b vmovdqu      $32(%r11), %xmm5
	0xc4, 0xc1, 0x7a, 0x6f, 0x73, 0x30, //0x00000471 vmovdqu      $48(%r11), %xmm6
	0xc5, 0xe9, 0x74, 0xf8, //0x00000477 vpcmpeqb     %xmm0, %xmm2, %xmm7
	0xc5, 0xf9, 0xd7, 0xff, //0x0000047b vpmovmskb    %xmm7, %edi
	0xc5, 0xd9, 0x74, 0xf8, //0x0000047f vpcmpeqb     %xmm0, %xmm4, %xmm7
	0xc5, 0xf9, 0xd7, 0xcf, //0x00000483 vpmovmskb    %xmm7, %ecx
	0xc5, 0xd1, 0x74, 0xf8, //0x00000487 vpcmpeqb     %xmm0, %xmm5, %xmm7
	0xc5, 0xf9, 0xd7, 0xf7, //0x0000048b vpmovmskb    %xmm7, %esi
	0xc5, 0xc9, 0x74, 0xf8, //0x0000048f vpcmpeqb     %xmm0, %xmm6, %xmm7
	0xc5, 0xf9, 0xd7, 0xdf, //0x00000493 vpmovmskb    %xmm7, %ebx
	0xc5, 0xe9, 0x74, 0xd1, //0x00000497 vpcmpeqb     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xd2, //0x0000049b vpmovmskb    %xmm2, %edx
	0xc5, 0xd9, 0x74, 0xd1, //0x0000049f vpcmpeqb     %xmm1, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xc2, //0x000004a3 vpmovmskb    %xmm2, %eax
	0xc5, 0xd1, 0x74, 0xd1, //0x000004a7 vpcmpeqb     %xmm1, %xmm5, %xmm2
	0xc5, 0x79, 0xd7, 0xf2, //0x000004ab vpmovmskb    %xmm2, %r14d
	0xc5, 0xc9, 0x74, 0xd1, //0x000004af vpcmpeqb     %xmm1, %xmm6, %xmm2
	0xc5, 0x79, 0xd7, 0xc2, //0x000004b3 vpmovmskb    %xmm2, %r8d
	0x48, 0xc1, 0xe3, 0x30, //0x000004b7 shlq         $48, %rbx
	0x48, 0xc1, 0xe6, 0x20, //0x000004bb shlq         $32, %rsi
	0x48, 0xc1, 0xe1, 0x10, //0x000004bf shlq         $16, %rcx
	0x48, 0x09, 0xcf, //0x000004c3 orq          %rcx, %rdi
	0x48, 0x09, 0xf7, //0x000004c6 orq          %rsi, %rdi
	0x49, 0xc1, 0xe0, 0x30, //0x000004c9 shlq         $48, %r8
	0x49, 0xc1, 0xe6, 0x20, //0x000004cd shlq         $32, %r14
	0x48, 0xc1, 0xe0, 0x10, //0x000004d1 shlq         $16, %rax
	0x48, 0x09, 0xc2, //0x000004d5 orq          %rax, %rdx
	0x4c, 0x09, 0xf2, //0x000004d8 orq          %r14, %rdx
	0x4c, 0x09, 0xc2, //0x000004db orq          %r8, %rdx
	0x49, 0x83, 0xf9, 0xff, //0x000004de cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x000004e2 jne          LBB0_46
	0x48, 0x85, 0xd2, //0x000004e8 testq        %rdx, %rdx
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x000004eb jne          LBB0_55
	//0x000004f1 LBB0_46
	0x48, 0x09, 0xdf, //0x000004f1 orq          %rbx, %rdi
	0x48, 0x89, 0xd0, //0x000004f4 movq         %rdx, %rax
	0x4c, 0x09, 0xe0, //0x000004f7 orq          %r12, %rax
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x000004fa jne          LBB0_56
	//0x00000500 LBB0_47
	0x48, 0x85, 0xff, //0x00000500 testq        %rdi, %rdi
	0x0f, 0x85, 0x86, 0x00, 0x00, 0x00, //0x00000503 jne          LBB0_57
	//0x00000509 LBB0_48
	0x49, 0x83, 0xc5, 0xc0, //0x00000509 addq         $-64, %r13
	0x49, 0x83, 0xc3, 0x40, //0x0000050d addq         $64, %r11
	0x49, 0x83, 0xfd, 0x3f, //0x00000511 cmpq         $63, %r13
	0x0f, 0x87, 0x45, 0xff, 0xff, 0xff, //0x00000515 ja           LBB0_44
	0xe9, 0x1d, 0x12, 0x00, 0x00, //0x0000051b jmp          LBB0_49
	//0x00000520 LBB0_55
	0x4c, 0x89, 0xd8, //0x00000520 movq         %r11, %rax
	0x48, 0x2b, 0x45, 0xd0, //0x00000523 subq         $-48(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xca, //0x00000527 bsfq         %rdx, %r9
	0x49, 0x01, 0xc1, //0x0000052b addq         %rax, %r9
	0x48, 0x09, 0xdf, //0x0000052e orq          %rbx, %rdi
	0x48, 0x89, 0xd0, //0x00000531 movq         %rdx, %rax
	0x4c, 0x09, 0xe0, //0x00000534 orq          %r12, %rax
	0x0f, 0x84, 0xc3, 0xff, 0xff, 0xff, //0x00000537 je           LBB0_47
	//0x0000053d LBB0_56
	0x4c, 0x89, 0xe0, //0x0000053d movq         %r12, %rax
	0x48, 0xf7, 0xd0, //0x00000540 notq         %rax
	0x48, 0x21, 0xd0, //0x00000543 andq         %rdx, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x00000546 leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xe1, //0x0000054a orq          %r12, %rcx
	0x48, 0x89, 0xce, //0x0000054d movq         %rcx, %rsi
	0x48, 0xf7, 0xd6, //0x00000550 notq         %rsi
	0x48, 0x21, 0xd6, //0x00000553 andq         %rdx, %rsi
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000556 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd6, //0x00000560 andq         %rdx, %rsi
	0x45, 0x31, 0xe4, //0x00000563 xorl         %r12d, %r12d
	0x48, 0x01, 0xc6, //0x00000566 addq         %rax, %rsi
	0x41, 0x0f, 0x92, 0xc4, //0x00000569 setb         %r12b
	0x48, 0x01, 0xf6, //0x0000056d addq         %rsi, %rsi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000570 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc6, //0x0000057a xorq         %rax, %rsi
	0x48, 0x21, 0xce, //0x0000057d andq         %rcx, %rsi
	0x48, 0xf7, 0xd6, //0x00000580 notq         %rsi
	0x48, 0x21, 0xf7, //0x00000583 andq         %rsi, %rdi
	0x48, 0x85, 0xff, //0x00000586 testq        %rdi, %rdi
	0x0f, 0x84, 0x7a, 0xff, 0xff, 0xff, //0x00000589 je           LBB0_48
	//0x0000058f LBB0_57
	0x48, 0x0f, 0xbc, 0xc7, //0x0000058f bsfq         %rdi, %rax
	//0x00000593 LBB0_58
	0x4c, 0x03, 0x5d, 0x98, //0x00000593 addq         $-104(%rbp), %r11
	0x49, 0x01, 0xc3, //0x00000597 addq         %rax, %r11
	0x4c, 0x8b, 0x65, 0xd0, //0x0000059a movq         $-48(%rbp), %r12
	0xe9, 0x3a, 0x09, 0x00, 0x00, //0x0000059e jmp          LBB0_187
	//0x000005a3 LBB0_59
	0x80, 0xfb, 0x3a, //0x000005a3 cmpb         $58, %bl
	0x0f, 0x85, 0xef, 0x1d, 0x00, 0x00, //0x000005a6 jne          LBB0_422
	0x4b, 0xc7, 0x04, 0xd1, 0x00, 0x00, 0x00, 0x00, //0x000005ac movq         $0, (%r9,%r10,8)
	0xe9, 0xf7, 0xfb, 0xff, 0xff, //0x000005b4 jmp          LBB0_2
	//0x000005b9 LBB0_61
	0x80, 0xfb, 0x5d, //0x000005b9 cmpb         $93, %bl
	0x0f, 0x84, 0x79, 0x02, 0x00, 0x00, //0x000005bc je           LBB0_35
	0x4b, 0xc7, 0x04, 0xd1, 0x01, 0x00, 0x00, 0x00, //0x000005c2 movq         $1, (%r9,%r10,8)
	0x83, 0xfb, 0x7b, //0x000005ca cmpl         $123, %ebx
	0x0f, 0x87, 0xc8, 0x1d, 0x00, 0x00, //0x000005cd ja           LBB0_422
	//0x000005d3 LBB0_63
	0x4f, 0x8d, 0x14, 0x3c, //0x000005d3 leaq         (%r12,%r15), %r10
	0x89, 0xd9, //0x000005d7 movl         %ebx, %ecx
	0x48, 0x8d, 0x15, 0x0c, 0x20, 0x00, 0x00, //0x000005d9 leaq         $8204(%rip), %rdx  /* LJTI0_1+0(%rip) */
	0x48, 0x63, 0x0c, 0x8a, //0x000005e0 movslq       (%rdx,%rcx,4), %rcx
	0x48, 0x01, 0xd1, //0x000005e4 addq         %rdx, %rcx
	0xff, 0xe1, //0x000005e7 jmpq         *%rcx
	//0x000005e9 LBB0_66
	0x48, 0x8b, 0x45, 0x90, //0x000005e9 movq         $-112(%rbp), %rax
	0x48, 0x8b, 0x78, 0x08, //0x000005ed movq         $8(%rax), %rdi
	0x4c, 0x29, 0xff, //0x000005f1 subq         %r15, %rdi
	0x0f, 0x84, 0x89, 0x1d, 0x00, 0x00, //0x000005f4 je           LBB0_417
	0x41, 0x80, 0x3a, 0x30, //0x000005fa cmpb         $48, (%r10)
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x000005fe jne          LBB0_71
	0x48, 0x83, 0xff, 0x01, //0x00000604 cmpq         $1, %rdi
	0x0f, 0x84, 0x89, 0xfb, 0xff, 0xff, //0x00000608 je           LBB0_1
	0x43, 0x8a, 0x04, 0x1c, //0x0000060e movb         (%r12,%r11), %al
	0x04, 0xd2, //0x00000612 addb         $-46, %al
	0x3c, 0x37, //0x00000614 cmpb         $55, %al
	0x0f, 0x87, 0x7b, 0xfb, 0xff, 0xff, //0x00000616 ja           LBB0_1
	0x0f, 0xb6, 0xc0, //0x0000061c movzbl       %al, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x0000061f movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x00000629 btq          %rax, %rcx
	0x0f, 0x83, 0x64, 0xfb, 0xff, 0xff, //0x0000062d jae          LBB0_1
	//0x00000633 LBB0_71
	0x48, 0x83, 0xff, 0x10, //0x00000633 cmpq         $16, %rdi
	0x0f, 0x82, 0xde, 0x17, 0x00, 0x00, //0x00000637 jb           LBB0_342
	0x4c, 0x8d, 0x4f, 0xf0, //0x0000063d leaq         $-16(%rdi), %r9
	0x4c, 0x89, 0xc8, //0x00000641 movq         %r9, %rax
	0x48, 0x83, 0xe0, 0xf0, //0x00000644 andq         $-16, %rax
	0x4a, 0x8d, 0x44, 0x10, 0x10, //0x00000648 leaq         $16(%rax,%r10), %rax
	0x48, 0x89, 0x45, 0xa8, //0x0000064d movq         %rax, $-88(%rbp)
	0x41, 0x83, 0xe1, 0x0f, //0x00000651 andl         $15, %r9d
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00000655 movq         $-1, %r12
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x0000065c movq         $-1, %r13
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00000663 movq         $-1, %r11
	0x4d, 0x89, 0xd6, //0x0000066a movq         %r10, %r14
	0x90, 0x90, 0x90, //0x0000066d .p2align 4, 0x90
	//0x00000670 LBB0_73
	0xc4, 0xc1, 0x7a, 0x6f, 0x16, //0x00000670 vmovdqu      (%r14), %xmm2
	0xc4, 0xc1, 0x69, 0x64, 0xe0, //0x00000675 vpcmpgtb     %xmm8, %xmm2, %xmm4
	0xc5, 0xb1, 0x64, 0xea, //0x0000067a vpcmpgtb     %xmm2, %xmm9, %xmm5
	0xc5, 0xd9, 0xdb, 0xe5, //0x0000067e vpand        %xmm5, %xmm4, %xmm4
	0xc5, 0xa9, 0x74, 0xea, //0x00000682 vpcmpeqb     %xmm2, %xmm10, %xmm5
	0xc5, 0xa1, 0x74, 0xf2, //0x00000686 vpcmpeqb     %xmm2, %xmm11, %xmm6
	0xc5, 0xc9, 0xeb, 0xed, //0x0000068a vpor         %xmm5, %xmm6, %xmm5
	0xc5, 0x99, 0xeb, 0xf2, //0x0000068e vpor         %xmm2, %xmm12, %xmm6
	0xc5, 0x91, 0x74, 0xd2, //0x00000692 vpcmpeqb     %xmm2, %xmm13, %xmm2
	0xc5, 0x89, 0x74, 0xf6, //0x00000696 vpcmpeqb     %xmm6, %xmm14, %xmm6
	0xc5, 0xc9, 0xeb, 0xfa, //0x0000069a vpor         %xmm2, %xmm6, %xmm7
	0xc5, 0xd1, 0xeb, 0xe4, //0x0000069e vpor         %xmm4, %xmm5, %xmm4
	0xc5, 0xc1, 0xeb, 0xe4, //0x000006a2 vpor         %xmm4, %xmm7, %xmm4
	0xc5, 0x79, 0xd7, 0xc2, //0x000006a6 vpmovmskb    %xmm2, %r8d
	0xc5, 0xf9, 0xd7, 0xc6, //0x000006aa vpmovmskb    %xmm6, %eax
	0xc5, 0xf9, 0xd7, 0xd5, //0x000006ae vpmovmskb    %xmm5, %edx
	0xc5, 0xf9, 0xd7, 0xcc, //0x000006b2 vpmovmskb    %xmm4, %ecx
	0xbe, 0xaa, 0xaa, 0xaa, 0xaa, //0x000006b6 movl         $2863311530, %esi
	0x48, 0x81, 0xc6, 0x55, 0x55, 0x55, 0x55, //0x000006bb addq         $1431655765, %rsi
	0x48, 0x31, 0xce, //0x000006c2 xorq         %rcx, %rsi
	0x48, 0x0f, 0xbc, 0xce, //0x000006c5 bsfq         %rsi, %rcx
	0x83, 0xf9, 0x10, //0x000006c9 cmpl         $16, %ecx
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x000006cc je           LBB0_75
	0xbe, 0xff, 0xff, 0xff, 0xff, //0x000006d2 movl         $-1, %esi
	0xd3, 0xe6, //0x000006d7 shll         %cl, %esi
	0xf7, 0xd6, //0x000006d9 notl         %esi
	0x41, 0x21, 0xf0, //0x000006db andl         %esi, %r8d
	0x21, 0xf0, //0x000006de andl         %esi, %eax
	0x21, 0xd6, //0x000006e0 andl         %edx, %esi
	0x89, 0xf2, //0x000006e2 movl         %esi, %edx
	//0x000006e4 LBB0_75
	0x41, 0x8d, 0x70, 0xff, //0x000006e4 leal         $-1(%r8), %esi
	0x44, 0x21, 0xc6, //0x000006e8 andl         %r8d, %esi
	0x0f, 0x85, 0x17, 0x10, 0x00, 0x00, //0x000006eb jne          LBB0_308
	0x8d, 0x70, 0xff, //0x000006f1 leal         $-1(%rax), %esi
	0x21, 0xc6, //0x000006f4 andl         %eax, %esi
	0x0f, 0x85, 0x0c, 0x10, 0x00, 0x00, //0x000006f6 jne          LBB0_308
	0x8d, 0x72, 0xff, //0x000006fc leal         $-1(%rdx), %esi
	0x21, 0xd6, //0x000006ff andl         %edx, %esi
	0x0f, 0x85, 0x01, 0x10, 0x00, 0x00, //0x00000701 jne          LBB0_308
	0x45, 0x85, 0xc0, //0x00000707 testl        %r8d, %r8d
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x0000070a je           LBB0_81
	0x4c, 0x89, 0xf3, //0x00000710 movq         %r14, %rbx
	0x4c, 0x29, 0xd3, //0x00000713 subq         %r10, %rbx
	0x41, 0x0f, 0xbc, 0xf0, //0x00000716 bsfl         %r8d, %esi
	0x48, 0x01, 0xde, //0x0000071a addq         %rbx, %rsi
	0x49, 0x83, 0xfb, 0xff, //0x0000071d cmpq         $-1, %r11
	0x0f, 0x85, 0x93, 0x13, 0x00, 0x00, //0x00000721 jne          LBB0_325
	0x49, 0x89, 0xf3, //0x00000727 movq         %rsi, %r11
	//0x0000072a LBB0_81
	0x85, 0xc0, //0x0000072a testl        %eax, %eax
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000072c je           LBB0_84
	0x4c, 0x89, 0xf6, //0x00000732 movq         %r14, %rsi
	0x4c, 0x29, 0xd6, //0x00000735 subq         %r10, %rsi
	0x0f, 0xbc, 0xc0, //0x00000738 bsfl         %eax, %eax
	0x48, 0x01, 0xf0, //0x0000073b addq         %rsi, %rax
	0x49, 0x83, 0xfd, 0xff, //0x0000073e cmpq         $-1, %r13
	0x0f, 0x85, 0x64, 0x11, 0x00, 0x00, //0x00000742 jne          LBB0_313
	0x49, 0x89, 0xc5, //0x00000748 movq         %rax, %r13
	//0x0000074b LBB0_84
	0x85, 0xd2, //0x0000074b testl        %edx, %edx
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x0000074d je           LBB0_87
	0x4c, 0x89, 0xf6, //0x00000753 movq         %r14, %rsi
	0x4c, 0x29, 0xd6, //0x00000756 subq         %r10, %rsi
	0x0f, 0xbc, 0xc2, //0x00000759 bsfl         %edx, %eax
	0x48, 0x01, 0xf0, //0x0000075c addq         %rsi, %rax
	0x49, 0x83, 0xfc, 0xff, //0x0000075f cmpq         $-1, %r12
	0x0f, 0x85, 0x43, 0x11, 0x00, 0x00, //0x00000763 jne          LBB0_313
	0x49, 0x89, 0xc4, //0x00000769 movq         %rax, %r12
	//0x0000076c LBB0_87
	0x83, 0xf9, 0x10, //0x0000076c cmpl         $16, %ecx
	0x0f, 0x85, 0xa8, 0x04, 0x00, 0x00, //0x0000076f jne          LBB0_148
	0x49, 0x83, 0xc6, 0x10, //0x00000775 addq         $16, %r14
	0x48, 0x83, 0xc7, 0xf0, //0x00000779 addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x0f, //0x0000077d cmpq         $15, %rdi
	0x0f, 0x87, 0xe9, 0xfe, 0xff, 0xff, //0x00000781 ja           LBB0_73
	0x4d, 0x85, 0xc9, //0x00000787 testq        %r9, %r9
	0x0f, 0x84, 0xb1, 0x04, 0x00, 0x00, //0x0000078a je           LBB0_150
	//0x00000790 LBB0_90
	0x48, 0x8b, 0x7d, 0xa8, //0x00000790 movq         $-88(%rbp), %rdi
	0x4a, 0x8d, 0x0c, 0x0f, //0x00000794 leaq         (%rdi,%r9), %rcx
	0x48, 0x8d, 0x35, 0x29, 0x21, 0x00, 0x00, //0x00000798 leaq         $8489(%rip), %rsi  /* LJTI0_3+0(%rip) */
	0xe9, 0x18, 0x00, 0x00, 0x00, //0x0000079f jmp          LBB0_92
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000007a4 .p2align 4, 0x90
	//0x000007b0 LBB0_91
	0x48, 0x89, 0xc7, //0x000007b0 movq         %rax, %rdi
	0x49, 0xff, 0xc9, //0x000007b3 decq         %r9
	0x0f, 0x84, 0xfb, 0x10, 0x00, 0x00, //0x000007b6 je           LBB0_314
	//0x000007bc LBB0_92
	0x0f, 0xbe, 0x17, //0x000007bc movsbl       (%rdi), %edx
	0x83, 0xc2, 0xd5, //0x000007bf addl         $-43, %edx
	0x83, 0xfa, 0x3a, //0x000007c2 cmpl         $58, %edx
	0x0f, 0x87, 0x72, 0x04, 0x00, 0x00, //0x000007c5 ja           LBB0_149
	0x48, 0x8d, 0x47, 0x01, //0x000007cb leaq         $1(%rdi), %rax
	0x48, 0x63, 0x14, 0x96, //0x000007cf movslq       (%rsi,%rdx,4), %rdx
	0x48, 0x01, 0xf2, //0x000007d3 addq         %rsi, %rdx
	0xff, 0xe2, //0x000007d6 jmpq         *%rdx
	//0x000007d8 LBB0_94
	0x48, 0x89, 0xc2, //0x000007d8 movq         %rax, %rdx
	0x4c, 0x29, 0xd2, //0x000007db subq         %r10, %rdx
	0x49, 0x83, 0xfc, 0xff, //0x000007de cmpq         $-1, %r12
	0x0f, 0x85, 0x86, 0x13, 0x00, 0x00, //0x000007e2 jne          LBB0_418
	0x48, 0xff, 0xca, //0x000007e8 decq         %rdx
	0x49, 0x89, 0xd4, //0x000007eb movq         %rdx, %r12
	0xe9, 0xbd, 0xff, 0xff, 0xff, //0x000007ee jmp          LBB0_91
	//0x000007f3 LBB0_96
	0x48, 0x89, 0xc2, //0x000007f3 movq         %rax, %rdx
	0x4c, 0x29, 0xd2, //0x000007f6 subq         %r10, %rdx
	0x49, 0x83, 0xfd, 0xff, //0x000007f9 cmpq         $-1, %r13
	0x0f, 0x85, 0x6b, 0x13, 0x00, 0x00, //0x000007fd jne          LBB0_418
	0x48, 0xff, 0xca, //0x00000803 decq         %rdx
	0x49, 0x89, 0xd5, //0x00000806 movq         %rdx, %r13
	0xe9, 0xa2, 0xff, 0xff, 0xff, //0x00000809 jmp          LBB0_91
	//0x0000080e LBB0_98
	0x48, 0x89, 0xc2, //0x0000080e movq         %rax, %rdx
	0x4c, 0x29, 0xd2, //0x00000811 subq         %r10, %rdx
	0x49, 0x83, 0xfb, 0xff, //0x00000814 cmpq         $-1, %r11
	0x0f, 0x85, 0x50, 0x13, 0x00, 0x00, //0x00000818 jne          LBB0_418
	0x48, 0xff, 0xca, //0x0000081e decq         %rdx
	0x49, 0x89, 0xd3, //0x00000821 movq         %rdx, %r11
	0xe9, 0x87, 0xff, 0xff, 0xff, //0x00000824 jmp          LBB0_91
	//0x00000829 LBB0_64
	0x83, 0xfb, 0x22, //0x00000829 cmpl         $34, %ebx
	0x0f, 0x84, 0x19, 0x02, 0x00, 0x00, //0x0000082c je           LBB0_125
	//0x00000832 LBB0_38
	0x83, 0xfb, 0x7d, //0x00000832 cmpl         $125, %ebx
	0x0f, 0x85, 0x60, 0x1b, 0x00, 0x00, //0x00000835 jne          LBB0_422
	//0x0000083b LBB0_35
	0x49, 0x89, 0x11, //0x0000083b movq         %rdx, (%r9)
	0x49, 0x89, 0xd2, //0x0000083e movq         %rdx, %r10
	0x48, 0x8b, 0x85, 0x78, 0xff, 0xff, 0xff, //0x00000841 movq         $-136(%rbp), %rax
	0x48, 0x85, 0xd2, //0x00000848 testq        %rdx, %rdx
	0x0f, 0x85, 0x75, 0xf9, 0xff, 0xff, //0x0000084b jne          LBB0_4
	0xe9, 0x4c, 0x1b, 0x00, 0x00, //0x00000851 jmp          LBB0_423
	//0x00000856 LBB0_100
	0x49, 0x81, 0xfa, 0xff, 0x0f, 0x00, 0x00, //0x00000856 cmpq         $4095, %r10
	0x0f, 0x8f, 0xfa, 0x1a, 0x00, 0x00, //0x0000085d jg           LBB0_441
	0x49, 0x8d, 0x42, 0x01, //0x00000863 leaq         $1(%r10), %rax
	0x49, 0x89, 0x01, //0x00000867 movq         %rax, (%r9)
	0x4b, 0xc7, 0x44, 0xd1, 0x08, 0x00, 0x00, 0x00, 0x00, //0x0000086a movq         $0, $8(%r9,%r10,8)
	0xe9, 0x38, 0xf9, 0xff, 0xff, //0x00000873 jmp          LBB0_2
	//0x00000878 LBB0_104
	0x48, 0x89, 0xd0, //0x00000878 movq         %rdx, %rax
	0x4c, 0x29, 0xd8, //0x0000087b subq         %r11, %rax
	0x0f, 0x84, 0xcd, 0x1c, 0x00, 0x00, //0x0000087e je           LBB0_456
	0x4c, 0x89, 0xd9, //0x00000884 movq         %r11, %rcx
	0x4d, 0x01, 0xe3, //0x00000887 addq         %r12, %r11
	0x48, 0x83, 0xf8, 0x40, //0x0000088a cmpq         $64, %rax
	0x0f, 0x82, 0xc1, 0x15, 0x00, 0x00, //0x0000088e jb           LBB0_354
	0x89, 0xc6, //0x00000894 movl         %eax, %esi
	0x83, 0xe6, 0x3f, //0x00000896 andl         $63, %esi
	0x48, 0x89, 0x75, 0xb8, //0x00000899 movq         %rsi, $-72(%rbp)
	0x4e, 0x8d, 0x54, 0x02, 0xc0, //0x0000089d leaq         $-64(%rdx,%r8), %r10
	0x49, 0x83, 0xe2, 0xc0, //0x000008a2 andq         $-64, %r10
	0x49, 0x01, 0xca, //0x000008a6 addq         %rcx, %r10
	0x4c, 0x03, 0x55, 0x88, //0x000008a9 addq         $-120(%rbp), %r10
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000008ad movq         $-1, %r9
	0x45, 0x31, 0xc0, //0x000008b4 xorl         %r8d, %r8d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000008b7 .p2align 4, 0x90
	//0x000008c0 LBB0_107
	0xc4, 0xc1, 0x7a, 0x6f, 0x33, //0x000008c0 vmovdqu      (%r11), %xmm6
	0xc4, 0xc1, 0x7a, 0x6f, 0x6b, 0x10, //0x000008c5 vmovdqu      $16(%r11), %xmm5
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x20, //0x000008cb vmovdqu      $32(%r11), %xmm4
	0xc4, 0xc1, 0x7a, 0x6f, 0x7b, 0x30, //0x000008d1 vmovdqu      $48(%r11), %xmm7
	0xc5, 0xc9, 0x74, 0xd0, //0x000008d7 vpcmpeqb     %xmm0, %xmm6, %xmm2
	0xc5, 0xf9, 0xd7, 0xf2, //0x000008db vpmovmskb    %xmm2, %esi
	0xc5, 0xd1, 0x74, 0xd0, //0x000008df vpcmpeqb     %xmm0, %xmm5, %xmm2
	0xc5, 0xf9, 0xd7, 0xda, //0x000008e3 vpmovmskb    %xmm2, %ebx
	0xc5, 0xd9, 0x74, 0xd0, //0x000008e7 vpcmpeqb     %xmm0, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xca, //0x000008eb vpmovmskb    %xmm2, %ecx
	0xc5, 0xc1, 0x74, 0xd0, //0x000008ef vpcmpeqb     %xmm0, %xmm7, %xmm2
	0xc5, 0xf9, 0xd7, 0xd2, //0x000008f3 vpmovmskb    %xmm2, %edx
	0xc5, 0xc9, 0x74, 0xd1, //0x000008f7 vpcmpeqb     %xmm1, %xmm6, %xmm2
	0xc5, 0x79, 0xd7, 0xea, //0x000008fb vpmovmskb    %xmm2, %r13d
	0xc5, 0xd1, 0x74, 0xd1, //0x000008ff vpcmpeqb     %xmm1, %xmm5, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x00000903 vpmovmskb    %xmm2, %edi
	0xc5, 0xd9, 0x74, 0xd1, //0x00000907 vpcmpeqb     %xmm1, %xmm4, %xmm2
	0x48, 0xc1, 0xe3, 0x10, //0x0000090b shlq         $16, %rbx
	0x48, 0x09, 0xde, //0x0000090f orq          %rbx, %rsi
	0xc5, 0xf9, 0xd7, 0xda, //0x00000912 vpmovmskb    %xmm2, %ebx
	0xc5, 0xc1, 0x74, 0xd1, //0x00000916 vpcmpeqb     %xmm1, %xmm7, %xmm2
	0x48, 0xc1, 0xe1, 0x20, //0x0000091a shlq         $32, %rcx
	0x48, 0x09, 0xce, //0x0000091e orq          %rcx, %rsi
	0xc5, 0xf9, 0xd7, 0xca, //0x00000921 vpmovmskb    %xmm2, %ecx
	0xc5, 0x81, 0x64, 0xd6, //0x00000925 vpcmpgtb     %xmm6, %xmm15, %xmm2
	0xc5, 0xc9, 0x64, 0xf3, //0x00000929 vpcmpgtb     %xmm3, %xmm6, %xmm6
	0xc5, 0xc9, 0xdb, 0xd2, //0x0000092d vpand        %xmm2, %xmm6, %xmm2
	0x48, 0xc1, 0xe7, 0x10, //0x00000931 shlq         $16, %rdi
	0x49, 0x09, 0xfd, //0x00000935 orq          %rdi, %r13
	0xc5, 0x79, 0xd7, 0xe2, //0x00000938 vpmovmskb    %xmm2, %r12d
	0xc5, 0x81, 0x64, 0xd5, //0x0000093c vpcmpgtb     %xmm5, %xmm15, %xmm2
	0xc5, 0xd1, 0x64, 0xeb, //0x00000940 vpcmpgtb     %xmm3, %xmm5, %xmm5
	0xc5, 0xd1, 0xdb, 0xd2, //0x00000944 vpand        %xmm2, %xmm5, %xmm2
	0x48, 0xc1, 0xe3, 0x20, //0x00000948 shlq         $32, %rbx
	0x49, 0x09, 0xdd, //0x0000094c orq          %rbx, %r13
	0xc5, 0xf9, 0xd7, 0xfa, //0x0000094f vpmovmskb    %xmm2, %edi
	0xc5, 0x81, 0x64, 0xd4, //0x00000953 vpcmpgtb     %xmm4, %xmm15, %xmm2
	0xc5, 0xd9, 0x64, 0xe3, //0x00000957 vpcmpgtb     %xmm3, %xmm4, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x0000095b vpand        %xmm2, %xmm4, %xmm2
	0x48, 0xc1, 0xe1, 0x30, //0x0000095f shlq         $48, %rcx
	0x49, 0x09, 0xcd, //0x00000963 orq          %rcx, %r13
	0xc5, 0xf9, 0xd7, 0xca, //0x00000966 vpmovmskb    %xmm2, %ecx
	0xc5, 0x81, 0x64, 0xd7, //0x0000096a vpcmpgtb     %xmm7, %xmm15, %xmm2
	0xc5, 0xc1, 0x64, 0xe3, //0x0000096e vpcmpgtb     %xmm3, %xmm7, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x00000972 vpand        %xmm2, %xmm4, %xmm2
	0x48, 0xc1, 0xe7, 0x10, //0x00000976 shlq         $16, %rdi
	0x49, 0x09, 0xfc, //0x0000097a orq          %rdi, %r12
	0xc5, 0x79, 0xd7, 0xf2, //0x0000097d vpmovmskb    %xmm2, %r14d
	0x48, 0xc1, 0xe2, 0x30, //0x00000981 shlq         $48, %rdx
	0x48, 0xc1, 0xe1, 0x20, //0x00000985 shlq         $32, %rcx
	0x49, 0x83, 0xf9, 0xff, //0x00000989 cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x0000098d jne          LBB0_109
	0x4d, 0x85, 0xed, //0x00000993 testq        %r13, %r13
	0x0f, 0x85, 0x9c, 0x00, 0x00, 0x00, //0x00000996 jne          LBB0_124
	//0x0000099c LBB0_109
	0x49, 0xc1, 0xe6, 0x30, //0x0000099c shlq         $48, %r14
	0x49, 0x09, 0xcc, //0x000009a0 orq          %rcx, %r12
	0x48, 0x09, 0xd6, //0x000009a3 orq          %rdx, %rsi
	0x4c, 0x89, 0xe9, //0x000009a6 movq         %r13, %rcx
	0x4c, 0x09, 0xc1, //0x000009a9 orq          %r8, %rcx
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x000009ac jne          LBB0_145
	0x4d, 0x09, 0xf4, //0x000009b2 orq          %r14, %r12
	0x48, 0x85, 0xf6, //0x000009b5 testq        %rsi, %rsi
	0x0f, 0x85, 0x39, 0x02, 0x00, 0x00, //0x000009b8 jne          LBB0_146
	//0x000009be LBB0_111
	0x4d, 0x85, 0xe4, //0x000009be testq        %r12, %r12
	0x0f, 0x85, 0x0c, 0x1a, 0x00, 0x00, //0x000009c1 jne          LBB0_426
	0x48, 0x83, 0xc0, 0xc0, //0x000009c7 addq         $-64, %rax
	0x49, 0x83, 0xc3, 0x40, //0x000009cb addq         $64, %r11
	0x48, 0x83, 0xf8, 0x3f, //0x000009cf cmpq         $63, %rax
	0x0f, 0x87, 0xe7, 0xfe, 0xff, 0xff, //0x000009d3 ja           LBB0_107
	0xe9, 0xda, 0x0d, 0x00, 0x00, //0x000009d9 jmp          LBB0_113
	//0x000009de LBB0_145
	0x4c, 0x89, 0xc1, //0x000009de movq         %r8, %rcx
	0x48, 0xf7, 0xd1, //0x000009e1 notq         %rcx
	0x4c, 0x21, 0xe9, //0x000009e4 andq         %r13, %rcx
	0x48, 0x8d, 0x14, 0x09, //0x000009e7 leaq         (%rcx,%rcx), %rdx
	0x4c, 0x09, 0xc2, //0x000009eb orq          %r8, %rdx
	0x48, 0x89, 0xd7, //0x000009ee movq         %rdx, %rdi
	0x48, 0xf7, 0xd7, //0x000009f1 notq         %rdi
	0x4c, 0x21, 0xef, //0x000009f4 andq         %r13, %rdi
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000009f7 movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xdf, //0x00000a01 andq         %rbx, %rdi
	0x45, 0x31, 0xc0, //0x00000a04 xorl         %r8d, %r8d
	0x48, 0x01, 0xcf, //0x00000a07 addq         %rcx, %rdi
	0x41, 0x0f, 0x92, 0xc0, //0x00000a0a setb         %r8b
	0x48, 0x01, 0xff, //0x00000a0e addq         %rdi, %rdi
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000a11 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xcf, //0x00000a1b xorq         %rcx, %rdi
	0x48, 0x21, 0xd7, //0x00000a1e andq         %rdx, %rdi
	0x48, 0xf7, 0xd7, //0x00000a21 notq         %rdi
	0x48, 0x21, 0xfe, //0x00000a24 andq         %rdi, %rsi
	0x4d, 0x09, 0xf4, //0x00000a27 orq          %r14, %r12
	0x48, 0x85, 0xf6, //0x00000a2a testq        %rsi, %rsi
	0x0f, 0x84, 0x8b, 0xff, 0xff, 0xff, //0x00000a2d je           LBB0_111
	0xe9, 0xbf, 0x01, 0x00, 0x00, //0x00000a33 jmp          LBB0_146
	//0x00000a38 LBB0_124
	0x4c, 0x89, 0xdf, //0x00000a38 movq         %r11, %rdi
	0x48, 0x2b, 0x7d, 0xd0, //0x00000a3b subq         $-48(%rbp), %rdi
	0x4d, 0x0f, 0xbc, 0xcd, //0x00000a3f bsfq         %r13, %r9
	0x49, 0x01, 0xf9, //0x00000a43 addq         %rdi, %r9
	0xe9, 0x51, 0xff, 0xff, 0xff, //0x00000a46 jmp          LBB0_109
	//0x00000a4b LBB0_125
	0x4b, 0xc7, 0x04, 0xd1, 0x02, 0x00, 0x00, 0x00, //0x00000a4b movq         $2, (%r9,%r10,8)
	0x48, 0x8b, 0x45, 0x90, //0x00000a53 movq         $-112(%rbp), %rax
	0x48, 0x8b, 0x50, 0x08, //0x00000a57 movq         $8(%rax), %rdx
	0xf6, 0x45, 0x80, 0x20, //0x00000a5b testb        $32, $-128(%rbp)
	0x4c, 0x89, 0x5d, 0xa8, //0x00000a5f movq         %r11, $-88(%rbp)
	0x48, 0x89, 0x55, 0xb0, //0x00000a63 movq         %rdx, $-80(%rbp)
	0x0f, 0x85, 0x71, 0x02, 0x00, 0x00, //0x00000a67 jne          LBB0_161
	0x49, 0x89, 0xd5, //0x00000a6d movq         %rdx, %r13
	0x4d, 0x29, 0xdd, //0x00000a70 subq         %r11, %r13
	0x0f, 0x84, 0xf3, 0x1a, 0x00, 0x00, //0x00000a73 je           LBB0_454
	0x4c, 0x89, 0xd8, //0x00000a79 movq         %r11, %rax
	0x4d, 0x01, 0xe3, //0x00000a7c addq         %r12, %r11
	0x49, 0x83, 0xfd, 0x40, //0x00000a7f cmpq         $64, %r13
	0x0f, 0x82, 0xfc, 0x13, 0x00, 0x00, //0x00000a83 jb           LBB0_357
	0x45, 0x89, 0xea, //0x00000a89 movl         %r13d, %r10d
	0x41, 0x83, 0xe2, 0x3f, //0x00000a8c andl         $63, %r10d
	0x4a, 0x8d, 0x4c, 0x02, 0xc0, //0x00000a90 leaq         $-64(%rdx,%r8), %rcx
	0x48, 0x83, 0xe1, 0xc0, //0x00000a95 andq         $-64, %rcx
	0x48, 0x01, 0xc1, //0x00000a99 addq         %rax, %rcx
	0x48, 0x03, 0x4d, 0x88, //0x00000a9c addq         $-120(%rbp), %rcx
	0x48, 0x89, 0x4d, 0xb8, //0x00000aa0 movq         %rcx, $-72(%rbp)
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00000aa4 movq         $-1, %r12
	0x45, 0x31, 0xc9, //0x00000aab xorl         %r9d, %r9d
	0x90, 0x90, //0x00000aae .p2align 4, 0x90
	//0x00000ab0 LBB0_129
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x00000ab0 vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x00000ab5 vmovdqu      $16(%r11), %xmm4
	0xc4, 0xc1, 0x7a, 0x6f, 0x6b, 0x20, //0x00000abb vmovdqu      $32(%r11), %xmm5
	0xc4, 0xc1, 0x7a, 0x6f, 0x73, 0x30, //0x00000ac1 vmovdqu      $48(%r11), %xmm6
	0xc5, 0xe9, 0x74, 0xf8, //0x00000ac7 vpcmpeqb     %xmm0, %xmm2, %xmm7
	0xc5, 0xf9, 0xd7, 0xff, //0x00000acb vpmovmskb    %xmm7, %edi
	0xc5, 0xd9, 0x74, 0xf8, //0x00000acf vpcmpeqb     %xmm0, %xmm4, %xmm7
	0xc5, 0xf9, 0xd7, 0xcf, //0x00000ad3 vpmovmskb    %xmm7, %ecx
	0xc5, 0xd1, 0x74, 0xf8, //0x00000ad7 vpcmpeqb     %xmm0, %xmm5, %xmm7
	0xc5, 0xf9, 0xd7, 0xf7, //0x00000adb vpmovmskb    %xmm7, %esi
	0xc5, 0xc9, 0x74, 0xf8, //0x00000adf vpcmpeqb     %xmm0, %xmm6, %xmm7
	0xc5, 0xf9, 0xd7, 0xdf, //0x00000ae3 vpmovmskb    %xmm7, %ebx
	0xc5, 0xe9, 0x74, 0xd1, //0x00000ae7 vpcmpeqb     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xd2, //0x00000aeb vpmovmskb    %xmm2, %edx
	0xc5, 0xd9, 0x74, 0xd1, //0x00000aef vpcmpeqb     %xmm1, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xc2, //0x00000af3 vpmovmskb    %xmm2, %eax
	0xc5, 0xd1, 0x74, 0xd1, //0x00000af7 vpcmpeqb     %xmm1, %xmm5, %xmm2
	0xc5, 0x79, 0xd7, 0xf2, //0x00000afb vpmovmskb    %xmm2, %r14d
	0xc5, 0xc9, 0x74, 0xd1, //0x00000aff vpcmpeqb     %xmm1, %xmm6, %xmm2
	0xc5, 0x79, 0xd7, 0xc2, //0x00000b03 vpmovmskb    %xmm2, %r8d
	0x48, 0xc1, 0xe3, 0x30, //0x00000b07 shlq         $48, %rbx
	0x48, 0xc1, 0xe6, 0x20, //0x00000b0b shlq         $32, %rsi
	0x48, 0xc1, 0xe1, 0x10, //0x00000b0f shlq         $16, %rcx
	0x48, 0x09, 0xcf, //0x00000b13 orq          %rcx, %rdi
	0x48, 0x09, 0xf7, //0x00000b16 orq          %rsi, %rdi
	0x49, 0xc1, 0xe0, 0x30, //0x00000b19 shlq         $48, %r8
	0x49, 0xc1, 0xe6, 0x20, //0x00000b1d shlq         $32, %r14
	0x48, 0xc1, 0xe0, 0x10, //0x00000b21 shlq         $16, %rax
	0x48, 0x09, 0xc2, //0x00000b25 orq          %rax, %rdx
	0x4c, 0x09, 0xf2, //0x00000b28 orq          %r14, %rdx
	0x4c, 0x09, 0xc2, //0x00000b2b orq          %r8, %rdx
	0x49, 0x83, 0xfc, 0xff, //0x00000b2e cmpq         $-1, %r12
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00000b32 jne          LBB0_131
	0x48, 0x85, 0xd2, //0x00000b38 testq        %rdx, %rdx
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x00000b3b jne          LBB0_140
	//0x00000b41 LBB0_131
	0x48, 0x09, 0xdf, //0x00000b41 orq          %rbx, %rdi
	0x48, 0x89, 0xd0, //0x00000b44 movq         %rdx, %rax
	0x4c, 0x09, 0xc8, //0x00000b47 orq          %r9, %rax
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x00000b4a jne          LBB0_141
	//0x00000b50 LBB0_132
	0x48, 0x85, 0xff, //0x00000b50 testq        %rdi, %rdi
	0x0f, 0x85, 0x86, 0x00, 0x00, 0x00, //0x00000b53 jne          LBB0_142
	//0x00000b59 LBB0_133
	0x49, 0x83, 0xc5, 0xc0, //0x00000b59 addq         $-64, %r13
	0x49, 0x83, 0xc3, 0x40, //0x00000b5d addq         $64, %r11
	0x49, 0x83, 0xfd, 0x3f, //0x00000b61 cmpq         $63, %r13
	0x0f, 0x87, 0x45, 0xff, 0xff, 0xff, //0x00000b65 ja           LBB0_129
	0xe9, 0x64, 0x0d, 0x00, 0x00, //0x00000b6b jmp          LBB0_134
	//0x00000b70 LBB0_140
	0x4c, 0x89, 0xd8, //0x00000b70 movq         %r11, %rax
	0x48, 0x2b, 0x45, 0xd0, //0x00000b73 subq         $-48(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xe2, //0x00000b77 bsfq         %rdx, %r12
	0x49, 0x01, 0xc4, //0x00000b7b addq         %rax, %r12
	0x48, 0x09, 0xdf, //0x00000b7e orq          %rbx, %rdi
	0x48, 0x89, 0xd0, //0x00000b81 movq         %rdx, %rax
	0x4c, 0x09, 0xc8, //0x00000b84 orq          %r9, %rax
	0x0f, 0x84, 0xc3, 0xff, 0xff, 0xff, //0x00000b87 je           LBB0_132
	//0x00000b8d LBB0_141
	0x4c, 0x89, 0xc8, //0x00000b8d movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x00000b90 notq         %rax
	0x48, 0x21, 0xd0, //0x00000b93 andq         %rdx, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x00000b96 leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xc9, //0x00000b9a orq          %r9, %rcx
	0x48, 0x89, 0xce, //0x00000b9d movq         %rcx, %rsi
	0x48, 0xf7, 0xd6, //0x00000ba0 notq         %rsi
	0x48, 0x21, 0xd6, //0x00000ba3 andq         %rdx, %rsi
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000ba6 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd6, //0x00000bb0 andq         %rdx, %rsi
	0x45, 0x31, 0xc9, //0x00000bb3 xorl         %r9d, %r9d
	0x48, 0x01, 0xc6, //0x00000bb6 addq         %rax, %rsi
	0x41, 0x0f, 0x92, 0xc1, //0x00000bb9 setb         %r9b
	0x48, 0x01, 0xf6, //0x00000bbd addq         %rsi, %rsi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000bc0 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc6, //0x00000bca xorq         %rax, %rsi
	0x48, 0x21, 0xce, //0x00000bcd andq         %rcx, %rsi
	0x48, 0xf7, 0xd6, //0x00000bd0 notq         %rsi
	0x48, 0x21, 0xf7, //0x00000bd3 andq         %rsi, %rdi
	0x48, 0x85, 0xff, //0x00000bd6 testq        %rdi, %rdi
	0x0f, 0x84, 0x7a, 0xff, 0xff, 0xff, //0x00000bd9 je           LBB0_133
	//0x00000bdf LBB0_142
	0x48, 0x0f, 0xbc, 0xc7, //0x00000bdf bsfq         %rdi, %rax
	//0x00000be3 LBB0_143
	0x4c, 0x03, 0x5d, 0x98, //0x00000be3 addq         $-104(%rbp), %r11
	0x49, 0x01, 0xc3, //0x00000be7 addq         %rax, %r11
	//0x00000bea LBB0_144
	0x48, 0x8b, 0x75, 0xc8, //0x00000bea movq         $-56(%rbp), %rsi
	0x4c, 0x8b, 0x4d, 0xc0, //0x00000bee movq         $-64(%rbp), %r9
	0xe9, 0x59, 0x03, 0x00, 0x00, //0x00000bf2 jmp          LBB0_195
	//0x00000bf7 LBB0_146
	0x48, 0x0f, 0xbc, 0xc6, //0x00000bf7 bsfq         %rsi, %rax
	0x4d, 0x85, 0xe4, //0x00000bfb testq        %r12, %r12
	0x0f, 0x84, 0xbf, 0x02, 0x00, 0x00, //0x00000bfe je           LBB0_185
	0x49, 0x0f, 0xbc, 0xcc, //0x00000c04 bsfq         %r12, %rcx
	0x4c, 0x8b, 0x65, 0xd0, //0x00000c08 movq         $-48(%rbp), %r12
	0x4d, 0x29, 0xe3, //0x00000c0c subq         %r12, %r11
	0x48, 0x39, 0xc1, //0x00000c0f cmpq         %rax, %rcx
	0x0f, 0x83, 0xc0, 0x02, 0x00, 0x00, //0x00000c12 jae          LBB0_186
	0xe9, 0x3d, 0x19, 0x00, 0x00, //0x00000c18 jmp          LBB0_306
	//0x00000c1d LBB0_148
	0x49, 0x01, 0xce, //0x00000c1d addq         %rcx, %r14
	0x4c, 0x89, 0x75, 0xa8, //0x00000c20 movq         %r14, $-88(%rbp)
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000c24 movq         $-1, %rax
	0x4d, 0x85, 0xed, //0x00000c2b testq        %r13, %r13
	0x48, 0x8b, 0x75, 0xc8, //0x00000c2e movq         $-56(%rbp), %rsi
	0x0f, 0x85, 0x1d, 0x00, 0x00, 0x00, //0x00000c32 jne          LBB0_151
	0xe9, 0x55, 0x17, 0x00, 0x00, //0x00000c38 jmp          LBB0_420
	//0x00000c3d LBB0_149
	0x48, 0x89, 0x7d, 0xa8, //0x00000c3d movq         %rdi, $-88(%rbp)
	//0x00000c41 LBB0_150
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000c41 movq         $-1, %rax
	0x4d, 0x85, 0xed, //0x00000c48 testq        %r13, %r13
	0x48, 0x8b, 0x75, 0xc8, //0x00000c4b movq         $-56(%rbp), %rsi
	0x0f, 0x84, 0x3d, 0x17, 0x00, 0x00, //0x00000c4f je           LBB0_420
	//0x00000c55 LBB0_151
	0x4d, 0x85, 0xe4, //0x00000c55 testq        %r12, %r12
	0x0f, 0x84, 0x34, 0x17, 0x00, 0x00, //0x00000c58 je           LBB0_420
	0x4d, 0x85, 0xdb, //0x00000c5e testq        %r11, %r11
	0x0f, 0x84, 0x2b, 0x17, 0x00, 0x00, //0x00000c61 je           LBB0_420
	0x48, 0x8b, 0x45, 0xa8, //0x00000c67 movq         $-88(%rbp), %rax
	0x4c, 0x29, 0xd0, //0x00000c6b subq         %r10, %rax
	0x48, 0x89, 0xc1, //0x00000c6e movq         %rax, %rcx
	0x48, 0xff, 0xc8, //0x00000c71 decq         %rax
	0x49, 0x39, 0xc5, //0x00000c74 cmpq         %rax, %r13
	0x0f, 0x84, 0x45, 0x00, 0x00, 0x00, //0x00000c77 je           LBB0_159
	0x49, 0x39, 0xc3, //0x00000c7d cmpq         %rax, %r11
	0x0f, 0x84, 0x3c, 0x00, 0x00, 0x00, //0x00000c80 je           LBB0_159
	0x49, 0x39, 0xc4, //0x00000c86 cmpq         %rax, %r12
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x00000c89 je           LBB0_159
	0x4d, 0x85, 0xe4, //0x00000c8f testq        %r12, %r12
	0x4c, 0x8b, 0x4d, 0xc0, //0x00000c92 movq         $-64(%rbp), %r9
	0x0f, 0x8e, 0x68, 0x02, 0x00, 0x00, //0x00000c96 jle          LBB0_189
	0x49, 0x8d, 0x44, 0x24, 0xff, //0x00000c9c leaq         $-1(%r12), %rax
	0x49, 0x39, 0xc5, //0x00000ca1 cmpq         %rax, %r13
	0x0f, 0x84, 0x5a, 0x02, 0x00, 0x00, //0x00000ca4 je           LBB0_189
	0x49, 0xf7, 0xd4, //0x00000caa notq         %r12
	0x4d, 0x89, 0xe3, //0x00000cad movq         %r12, %r11
	0x4c, 0x8b, 0x65, 0xd0, //0x00000cb0 movq         $-48(%rbp), %r12
	0x4d, 0x85, 0xdb, //0x00000cb4 testq        %r11, %r11
	0x0f, 0x89, 0xd7, 0xf4, 0xff, 0xff, //0x00000cb7 jns          LBB0_258
	0xe9, 0xcd, 0x16, 0x00, 0x00, //0x00000cbd jmp          LBB0_419
	//0x00000cc2 LBB0_159
	0x49, 0x89, 0xcb, //0x00000cc2 movq         %rcx, %r11
	0x49, 0xf7, 0xdb, //0x00000cc5 negq         %r11
	//0x00000cc8 LBB0_160
	0x4c, 0x8b, 0x65, 0xd0, //0x00000cc8 movq         $-48(%rbp), %r12
	0x4c, 0x8b, 0x4d, 0xc0, //0x00000ccc movq         $-64(%rbp), %r9
	0x4d, 0x85, 0xdb, //0x00000cd0 testq        %r11, %r11
	0x0f, 0x89, 0xbb, 0xf4, 0xff, 0xff, //0x00000cd3 jns          LBB0_258
	0xe9, 0xb1, 0x16, 0x00, 0x00, //0x00000cd9 jmp          LBB0_419
	//0x00000cde LBB0_161
	0x48, 0x89, 0xd0, //0x00000cde movq         %rdx, %rax
	0x4c, 0x29, 0xd8, //0x00000ce1 subq         %r11, %rax
	0x0f, 0x84, 0x82, 0x18, 0x00, 0x00, //0x00000ce4 je           LBB0_454
	0x4c, 0x89, 0xd9, //0x00000cea movq         %r11, %rcx
	0x4d, 0x01, 0xe3, //0x00000ced addq         %r12, %r11
	0x48, 0x83, 0xf8, 0x40, //0x00000cf0 cmpq         $64, %rax
	0x0f, 0x82, 0xa4, 0x11, 0x00, 0x00, //0x00000cf4 jb           LBB0_358
	0x41, 0x89, 0xc2, //0x00000cfa movl         %eax, %r10d
	0x41, 0x83, 0xe2, 0x3f, //0x00000cfd andl         $63, %r10d
	0x4e, 0x8d, 0x44, 0x02, 0xc0, //0x00000d01 leaq         $-64(%rdx,%r8), %r8
	0x49, 0x83, 0xe0, 0xc0, //0x00000d06 andq         $-64, %r8
	0x49, 0x01, 0xc8, //0x00000d0a addq         %rcx, %r8
	0x4c, 0x03, 0x45, 0x88, //0x00000d0d addq         $-120(%rbp), %r8
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00000d11 movq         $-1, %r12
	0x45, 0x31, 0xc9, //0x00000d18 xorl         %r9d, %r9d
	0x90, 0x90, 0x90, 0x90, 0x90, //0x00000d1b .p2align 4, 0x90
	//0x00000d20 LBB0_164
	0xc4, 0xc1, 0x7a, 0x6f, 0x33, //0x00000d20 vmovdqu      (%r11), %xmm6
	0xc4, 0xc1, 0x7a, 0x6f, 0x6b, 0x10, //0x00000d25 vmovdqu      $16(%r11), %xmm5
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x20, //0x00000d2b vmovdqu      $32(%r11), %xmm4
	0xc4, 0xc1, 0x7a, 0x6f, 0x7b, 0x30, //0x00000d31 vmovdqu      $48(%r11), %xmm7
	0xc5, 0xc9, 0x74, 0xd0, //0x00000d37 vpcmpeqb     %xmm0, %xmm6, %xmm2
	0xc5, 0xf9, 0xd7, 0xf2, //0x00000d3b vpmovmskb    %xmm2, %esi
	0xc5, 0xd1, 0x74, 0xd0, //0x00000d3f vpcmpeqb     %xmm0, %xmm5, %xmm2
	0xc5, 0xf9, 0xd7, 0xda, //0x00000d43 vpmovmskb    %xmm2, %ebx
	0xc5, 0xd9, 0x74, 0xd0, //0x00000d47 vpcmpeqb     %xmm0, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xca, //0x00000d4b vpmovmskb    %xmm2, %ecx
	0xc5, 0xc1, 0x74, 0xd0, //0x00000d4f vpcmpeqb     %xmm0, %xmm7, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x00000d53 vpmovmskb    %xmm2, %edi
	0xc5, 0xc9, 0x74, 0xd1, //0x00000d57 vpcmpeqb     %xmm1, %xmm6, %xmm2
	0xc5, 0x79, 0xd7, 0xea, //0x00000d5b vpmovmskb    %xmm2, %r13d
	0xc5, 0xd1, 0x74, 0xd1, //0x00000d5f vpcmpeqb     %xmm1, %xmm5, %xmm2
	0xc5, 0xf9, 0xd7, 0xd2, //0x00000d63 vpmovmskb    %xmm2, %edx
	0xc5, 0xd9, 0x74, 0xd1, //0x00000d67 vpcmpeqb     %xmm1, %xmm4, %xmm2
	0x48, 0xc1, 0xe3, 0x10, //0x00000d6b shlq         $16, %rbx
	0x48, 0x09, 0xde, //0x00000d6f orq          %rbx, %rsi
	0xc5, 0xf9, 0xd7, 0xda, //0x00000d72 vpmovmskb    %xmm2, %ebx
	0xc5, 0xc1, 0x74, 0xd1, //0x00000d76 vpcmpeqb     %xmm1, %xmm7, %xmm2
	0x48, 0xc1, 0xe1, 0x20, //0x00000d7a shlq         $32, %rcx
	0x48, 0x09, 0xce, //0x00000d7e orq          %rcx, %rsi
	0xc5, 0xf9, 0xd7, 0xca, //0x00000d81 vpmovmskb    %xmm2, %ecx
	0xc5, 0x81, 0x64, 0xd6, //0x00000d85 vpcmpgtb     %xmm6, %xmm15, %xmm2
	0xc5, 0xc9, 0x64, 0xf3, //0x00000d89 vpcmpgtb     %xmm3, %xmm6, %xmm6
	0xc5, 0xc9, 0xdb, 0xd2, //0x00000d8d vpand        %xmm2, %xmm6, %xmm2
	0x48, 0xc1, 0xe2, 0x10, //0x00000d91 shlq         $16, %rdx
	0x49, 0x09, 0xd5, //0x00000d95 orq          %rdx, %r13
	0xc5, 0xf9, 0xd7, 0xd2, //0x00000d98 vpmovmskb    %xmm2, %edx
	0xc5, 0x81, 0x64, 0xd5, //0x00000d9c vpcmpgtb     %xmm5, %xmm15, %xmm2
	0xc5, 0xd1, 0x64, 0xeb, //0x00000da0 vpcmpgtb     %xmm3, %xmm5, %xmm5
	0xc5, 0xd1, 0xdb, 0xd2, //0x00000da4 vpand        %xmm2, %xmm5, %xmm2
	0x48, 0xc1, 0xe3, 0x20, //0x00000da8 shlq         $32, %rbx
	0x49, 0x09, 0xdd, //0x00000dac orq          %rbx, %r13
	0xc5, 0xf9, 0xd7, 0xda, //0x00000daf vpmovmskb    %xmm2, %ebx
	0xc5, 0x81, 0x64, 0xd4, //0x00000db3 vpcmpgtb     %xmm4, %xmm15, %xmm2
	0xc5, 0xd9, 0x64, 0xe3, //0x00000db7 vpcmpgtb     %xmm3, %xmm4, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x00000dbb vpand        %xmm2, %xmm4, %xmm2
	0x48, 0xc1, 0xe1, 0x30, //0x00000dbf shlq         $48, %rcx
	0x49, 0x09, 0xcd, //0x00000dc3 orq          %rcx, %r13
	0xc5, 0xf9, 0xd7, 0xca, //0x00000dc6 vpmovmskb    %xmm2, %ecx
	0xc5, 0x81, 0x64, 0xd7, //0x00000dca vpcmpgtb     %xmm7, %xmm15, %xmm2
	0xc5, 0xc1, 0x64, 0xe3, //0x00000dce vpcmpgtb     %xmm3, %xmm7, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x00000dd2 vpand        %xmm2, %xmm4, %xmm2
	0x48, 0xc1, 0xe3, 0x10, //0x00000dd6 shlq         $16, %rbx
	0x48, 0x09, 0xda, //0x00000dda orq          %rbx, %rdx
	0xc5, 0x79, 0xd7, 0xf2, //0x00000ddd vpmovmskb    %xmm2, %r14d
	0x48, 0xc1, 0xe7, 0x30, //0x00000de1 shlq         $48, %rdi
	0x48, 0xc1, 0xe1, 0x20, //0x00000de5 shlq         $32, %rcx
	0x49, 0x83, 0xfc, 0xff, //0x00000de9 cmpq         $-1, %r12
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00000ded jne          LBB0_166
	0x4d, 0x85, 0xed, //0x00000df3 testq        %r13, %r13
	0x0f, 0x85, 0x9e, 0x00, 0x00, 0x00, //0x00000df6 jne          LBB0_181
	//0x00000dfc LBB0_166
	0x49, 0xc1, 0xe6, 0x30, //0x00000dfc shlq         $48, %r14
	0x48, 0x09, 0xca, //0x00000e00 orq          %rcx, %rdx
	0x48, 0x09, 0xfe, //0x00000e03 orq          %rdi, %rsi
	0x4c, 0x89, 0xe9, //0x00000e06 movq         %r13, %rcx
	0x4c, 0x09, 0xc9, //0x00000e09 orq          %r9, %rcx
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x00000e0c jne          LBB0_182
	0x4c, 0x09, 0xf2, //0x00000e12 orq          %r14, %rdx
	0x48, 0x85, 0xf6, //0x00000e15 testq        %rsi, %rsi
	0x0f, 0x85, 0x8f, 0x00, 0x00, 0x00, //0x00000e18 jne          LBB0_183
	//0x00000e1e LBB0_168
	0x48, 0x85, 0xd2, //0x00000e1e testq        %rdx, %rdx
	0x0f, 0x85, 0xda, 0x15, 0x00, 0x00, //0x00000e21 jne          LBB0_432
	0x48, 0x83, 0xc0, 0xc0, //0x00000e27 addq         $-64, %rax
	0x49, 0x83, 0xc3, 0x40, //0x00000e2b addq         $64, %r11
	0x48, 0x83, 0xf8, 0x3f, //0x00000e2f cmpq         $63, %rax
	0x0f, 0x87, 0xe7, 0xfe, 0xff, 0xff, //0x00000e33 ja           LBB0_164
	0xe9, 0x11, 0x0b, 0x00, 0x00, //0x00000e39 jmp          LBB0_170
	//0x00000e3e LBB0_182
	0x4c, 0x89, 0xc9, //0x00000e3e movq         %r9, %rcx
	0x48, 0xf7, 0xd1, //0x00000e41 notq         %rcx
	0x4c, 0x21, 0xe9, //0x00000e44 andq         %r13, %rcx
	0x48, 0x8d, 0x1c, 0x09, //0x00000e47 leaq         (%rcx,%rcx), %rbx
	0x4c, 0x09, 0xcb, //0x00000e4b orq          %r9, %rbx
	0x48, 0x89, 0x5d, 0xb8, //0x00000e4e movq         %rbx, $-72(%rbp)
	0x48, 0xf7, 0xd3, //0x00000e52 notq         %rbx
	0x4c, 0x21, 0xeb, //0x00000e55 andq         %r13, %rbx
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000e58 movabsq      $-6148914691236517206, %rdi
	0x48, 0x21, 0xfb, //0x00000e62 andq         %rdi, %rbx
	0x45, 0x31, 0xc9, //0x00000e65 xorl         %r9d, %r9d
	0x48, 0x01, 0xcb, //0x00000e68 addq         %rcx, %rbx
	0x41, 0x0f, 0x92, 0xc1, //0x00000e6b setb         %r9b
	0x48, 0x01, 0xdb, //0x00000e6f addq         %rbx, %rbx
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000e72 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xcb, //0x00000e7c xorq         %rcx, %rbx
	0x48, 0x23, 0x5d, 0xb8, //0x00000e7f andq         $-72(%rbp), %rbx
	0x48, 0xf7, 0xd3, //0x00000e83 notq         %rbx
	0x48, 0x21, 0xde, //0x00000e86 andq         %rbx, %rsi
	0x4c, 0x09, 0xf2, //0x00000e89 orq          %r14, %rdx
	0x48, 0x85, 0xf6, //0x00000e8c testq        %rsi, %rsi
	0x0f, 0x84, 0x89, 0xff, 0xff, 0xff, //0x00000e8f je           LBB0_168
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x00000e95 jmp          LBB0_183
	//0x00000e9a LBB0_181
	0x4c, 0x89, 0xdb, //0x00000e9a movq         %r11, %rbx
	0x48, 0x2b, 0x5d, 0xd0, //0x00000e9d subq         $-48(%rbp), %rbx
	0x4d, 0x0f, 0xbc, 0xe5, //0x00000ea1 bsfq         %r13, %r12
	0x49, 0x01, 0xdc, //0x00000ea5 addq         %rbx, %r12
	0xe9, 0x4f, 0xff, 0xff, 0xff, //0x00000ea8 jmp          LBB0_166
	//0x00000ead LBB0_183
	0x48, 0x0f, 0xbc, 0xc6, //0x00000ead bsfq         %rsi, %rax
	0x48, 0x85, 0xd2, //0x00000eb1 testq        %rdx, %rdx
	0x0f, 0x84, 0x77, 0x00, 0x00, 0x00, //0x00000eb4 je           LBB0_192
	0x48, 0x0f, 0xbc, 0xca, //0x00000eba bsfq         %rdx, %rcx
	0xe9, 0x73, 0x00, 0x00, 0x00, //0x00000ebe jmp          LBB0_193
	//0x00000ec3 LBB0_185
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00000ec3 movl         $64, %ecx
	0x4c, 0x8b, 0x65, 0xd0, //0x00000ec8 movq         $-48(%rbp), %r12
	0x4d, 0x29, 0xe3, //0x00000ecc subq         %r12, %r11
	0x48, 0x39, 0xc1, //0x00000ecf cmpq         %rax, %rcx
	0x0f, 0x82, 0x82, 0x16, 0x00, 0x00, //0x00000ed2 jb           LBB0_306
	//0x00000ed8 LBB0_186
	0x4d, 0x8d, 0x5c, 0x03, 0x01, //0x00000ed8 leaq         $1(%r11,%rax), %r11
	//0x00000edd LBB0_187
	0x4d, 0x85, 0xdb, //0x00000edd testq        %r11, %r11
	0x0f, 0x88, 0x83, 0x14, 0x00, 0x00, //0x00000ee0 js           LBB0_415
	0x48, 0x8b, 0x75, 0xc8, //0x00000ee6 movq         $-56(%rbp), %rsi
	0x4c, 0x89, 0x1e, //0x00000eea movq         %r11, (%rsi)
	0x4c, 0x89, 0xf8, //0x00000eed movq         %r15, %rax
	0x48, 0x83, 0x7d, 0xa8, 0x00, //0x00000ef0 cmpq         $0, $-88(%rbp)
	0x4c, 0x8b, 0x4d, 0xc0, //0x00000ef5 movq         $-64(%rbp), %r9
	0x0f, 0x8f, 0xb1, 0xf2, 0xff, 0xff, //0x00000ef9 jg           LBB0_2
	0xe9, 0x9e, 0x14, 0x00, 0x00, //0x00000eff jmp          LBB0_423
	//0x00000f04 LBB0_189
	0x4c, 0x89, 0xd8, //0x00000f04 movq         %r11, %rax
	0x4c, 0x09, 0xe8, //0x00000f07 orq          %r13, %rax
	0x4d, 0x39, 0xeb, //0x00000f0a cmpq         %r13, %r11
	0x0f, 0x8c, 0x5c, 0xf2, 0xff, 0xff, //0x00000f0d jl           LBB0_257
	0x48, 0x85, 0xc0, //0x00000f13 testq        %rax, %rax
	0x0f, 0x88, 0x53, 0xf2, 0xff, 0xff, //0x00000f16 js           LBB0_257
	0x49, 0xf7, 0xd3, //0x00000f1c notq         %r11
	0x4c, 0x8b, 0x65, 0xd0, //0x00000f1f movq         $-48(%rbp), %r12
	0x4d, 0x85, 0xdb, //0x00000f23 testq        %r11, %r11
	0x0f, 0x89, 0x68, 0xf2, 0xff, 0xff, //0x00000f26 jns          LBB0_258
	0xe9, 0x5e, 0x14, 0x00, 0x00, //0x00000f2c jmp          LBB0_419
	//0x00000f31 LBB0_192
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00000f31 movl         $64, %ecx
	//0x00000f36 LBB0_193
	0x48, 0x8b, 0x75, 0xc8, //0x00000f36 movq         $-56(%rbp), %rsi
	0x4c, 0x8b, 0x4d, 0xc0, //0x00000f3a movq         $-64(%rbp), %r9
	0x4c, 0x2b, 0x5d, 0xd0, //0x00000f3e subq         $-48(%rbp), %r11
	0x48, 0x39, 0xc1, //0x00000f42 cmpq         %rax, %rcx
	0x0f, 0x82, 0x2a, 0x16, 0x00, 0x00, //0x00000f45 jb           LBB0_455
	0x4d, 0x8d, 0x5c, 0x03, 0x01, //0x00000f4b leaq         $1(%r11,%rax), %r11
	//0x00000f50 LBB0_195
	0x4d, 0x85, 0xdb, //0x00000f50 testq        %r11, %r11
	0x0f, 0x88, 0x58, 0x14, 0x00, 0x00, //0x00000f53 js           LBB0_424
	0x4c, 0x89, 0x1e, //0x00000f59 movq         %r11, (%rsi)
	0x4c, 0x89, 0xf8, //0x00000f5c movq         %r15, %rax
	0x48, 0x83, 0x7d, 0xa8, 0x00, //0x00000f5f cmpq         $0, $-88(%rbp)
	0x4c, 0x8b, 0x65, 0xd0, //0x00000f64 movq         $-48(%rbp), %r12
	0x0f, 0x8e, 0x34, 0x14, 0x00, 0x00, //0x00000f68 jle          LBB0_423
	0x49, 0x8b, 0x01, //0x00000f6e movq         (%r9), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00000f71 cmpq         $4095, %rax
	0x0f, 0x8f, 0xe0, 0x13, 0x00, 0x00, //0x00000f77 jg           LBB0_441
	0x48, 0x8d, 0x48, 0x01, //0x00000f7d leaq         $1(%rax), %rcx
	0x49, 0x89, 0x09, //0x00000f81 movq         %rcx, (%r9)
	0x49, 0xc7, 0x44, 0xc1, 0x08, 0x04, 0x00, 0x00, 0x00, //0x00000f84 movq         $4, $8(%r9,%rax,8)
	0xe9, 0x1e, 0xf2, 0xff, 0xff, //0x00000f8d jmp          LBB0_2
	//0x00000f92 LBB0_199
	0x48, 0x8b, 0x45, 0x90, //0x00000f92 movq         $-112(%rbp), %rax
	0x48, 0x8b, 0x50, 0x08, //0x00000f96 movq         $8(%rax), %rdx
	0xf6, 0x45, 0x80, 0x20, //0x00000f9a testb        $32, $-128(%rbp)
	0x4c, 0x89, 0x5d, 0xa8, //0x00000f9e movq         %r11, $-88(%rbp)
	0x48, 0x89, 0x55, 0xb0, //0x00000fa2 movq         %rdx, $-80(%rbp)
	0x0f, 0x85, 0xa3, 0x04, 0x00, 0x00, //0x00000fa6 jne          LBB0_268
	0x49, 0x89, 0xd4, //0x00000fac movq         %rdx, %r12
	0x4d, 0x29, 0xdc, //0x00000faf subq         %r11, %r12
	0x0f, 0x84, 0x99, 0x15, 0x00, 0x00, //0x00000fb2 je           LBB0_456
	0x48, 0x8b, 0x45, 0xd0, //0x00000fb8 movq         $-48(%rbp), %rax
	0x4c, 0x89, 0xd9, //0x00000fbc movq         %r11, %rcx
	0x49, 0x01, 0xc3, //0x00000fbf addq         %rax, %r11
	0x49, 0x83, 0xfc, 0x40, //0x00000fc2 cmpq         $64, %r12
	0x0f, 0x82, 0x12, 0x0f, 0x00, 0x00, //0x00000fc6 jb           LBB0_360
	0x44, 0x89, 0xe0, //0x00000fcc movl         %r12d, %eax
	0x83, 0xe0, 0x3f, //0x00000fcf andl         $63, %eax
	0x48, 0x89, 0x45, 0xb8, //0x00000fd2 movq         %rax, $-72(%rbp)
	0x4e, 0x8d, 0x44, 0x02, 0xc0, //0x00000fd6 leaq         $-64(%rdx,%r8), %r8
	0x49, 0x83, 0xe0, 0xc0, //0x00000fdb andq         $-64, %r8
	0x49, 0x01, 0xc8, //0x00000fdf addq         %rcx, %r8
	0x4c, 0x03, 0x45, 0x88, //0x00000fe2 addq         $-120(%rbp), %r8
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000fe6 movq         $-1, %r9
	0x45, 0x31, 0xd2, //0x00000fed xorl         %r10d, %r10d
	//0x00000ff0 .p2align 4, 0x90
	//0x00000ff0 LBB0_203
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x00000ff0 vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x00000ff5 vmovdqu      $16(%r11), %xmm4
	0xc4, 0xc1, 0x7a, 0x6f, 0x6b, 0x20, //0x00000ffb vmovdqu      $32(%r11), %xmm5
	0xc4, 0xc1, 0x7a, 0x6f, 0x73, 0x30, //0x00001001 vmovdqu      $48(%r11), %xmm6
	0xc5, 0xe9, 0x74, 0xf8, //0x00001007 vpcmpeqb     %xmm0, %xmm2, %xmm7
	0xc5, 0xf9, 0xd7, 0xff, //0x0000100b vpmovmskb    %xmm7, %edi
	0xc5, 0xd9, 0x74, 0xf8, //0x0000100f vpcmpeqb     %xmm0, %xmm4, %xmm7
	0xc5, 0xf9, 0xd7, 0xcf, //0x00001013 vpmovmskb    %xmm7, %ecx
	0xc5, 0xd1, 0x74, 0xf8, //0x00001017 vpcmpeqb     %xmm0, %xmm5, %xmm7
	0xc5, 0xf9, 0xd7, 0xf7, //0x0000101b vpmovmskb    %xmm7, %esi
	0xc5, 0xc9, 0x74, 0xf8, //0x0000101f vpcmpeqb     %xmm0, %xmm6, %xmm7
	0xc5, 0x79, 0xd7, 0xef, //0x00001023 vpmovmskb    %xmm7, %r13d
	0xc5, 0xe9, 0x74, 0xd1, //0x00001027 vpcmpeqb     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xd2, //0x0000102b vpmovmskb    %xmm2, %edx
	0xc5, 0xd9, 0x74, 0xd1, //0x0000102f vpcmpeqb     %xmm1, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xc2, //0x00001033 vpmovmskb    %xmm2, %eax
	0xc5, 0xd1, 0x74, 0xd1, //0x00001037 vpcmpeqb     %xmm1, %xmm5, %xmm2
	0xc5, 0xf9, 0xd7, 0xda, //0x0000103b vpmovmskb    %xmm2, %ebx
	0xc5, 0xc9, 0x74, 0xd1, //0x0000103f vpcmpeqb     %xmm1, %xmm6, %xmm2
	0xc5, 0x79, 0xd7, 0xf2, //0x00001043 vpmovmskb    %xmm2, %r14d
	0x49, 0xc1, 0xe5, 0x30, //0x00001047 shlq         $48, %r13
	0x48, 0xc1, 0xe6, 0x20, //0x0000104b shlq         $32, %rsi
	0x48, 0xc1, 0xe1, 0x10, //0x0000104f shlq         $16, %rcx
	0x48, 0x09, 0xcf, //0x00001053 orq          %rcx, %rdi
	0x48, 0x09, 0xf7, //0x00001056 orq          %rsi, %rdi
	0x49, 0xc1, 0xe6, 0x30, //0x00001059 shlq         $48, %r14
	0x48, 0xc1, 0xe3, 0x20, //0x0000105d shlq         $32, %rbx
	0x48, 0xc1, 0xe0, 0x10, //0x00001061 shlq         $16, %rax
	0x48, 0x09, 0xc2, //0x00001065 orq          %rax, %rdx
	0x48, 0x09, 0xda, //0x00001068 orq          %rbx, %rdx
	0x4c, 0x09, 0xf2, //0x0000106b orq          %r14, %rdx
	0x49, 0x83, 0xf9, 0xff, //0x0000106e cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00001072 jne          LBB0_205
	0x48, 0x85, 0xd2, //0x00001078 testq        %rdx, %rdx
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x0000107b jne          LBB0_213
	//0x00001081 LBB0_205
	0x4c, 0x09, 0xef, //0x00001081 orq          %r13, %rdi
	0x48, 0x89, 0xd0, //0x00001084 movq         %rdx, %rax
	0x4c, 0x09, 0xd0, //0x00001087 orq          %r10, %rax
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x0000108a jne          LBB0_214
	//0x00001090 LBB0_206
	0x48, 0x85, 0xff, //0x00001090 testq        %rdi, %rdi
	0x0f, 0x85, 0xf6, 0xf4, 0xff, 0xff, //0x00001093 jne          LBB0_57
	//0x00001099 LBB0_207
	0x49, 0x83, 0xc4, 0xc0, //0x00001099 addq         $-64, %r12
	0x49, 0x83, 0xc3, 0x40, //0x0000109d addq         $64, %r11
	0x49, 0x83, 0xfc, 0x3f, //0x000010a1 cmpq         $63, %r12
	0x0f, 0x87, 0x45, 0xff, 0xff, 0xff, //0x000010a5 ja           LBB0_203
	0xe9, 0x01, 0x0b, 0x00, 0x00, //0x000010ab jmp          LBB0_208
	//0x000010b0 LBB0_213
	0x4c, 0x89, 0xd8, //0x000010b0 movq         %r11, %rax
	0x48, 0x2b, 0x45, 0xd0, //0x000010b3 subq         $-48(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xca, //0x000010b7 bsfq         %rdx, %r9
	0x49, 0x01, 0xc1, //0x000010bb addq         %rax, %r9
	0x4c, 0x09, 0xef, //0x000010be orq          %r13, %rdi
	0x48, 0x89, 0xd0, //0x000010c1 movq         %rdx, %rax
	0x4c, 0x09, 0xd0, //0x000010c4 orq          %r10, %rax
	0x0f, 0x84, 0xc3, 0xff, 0xff, 0xff, //0x000010c7 je           LBB0_206
	//0x000010cd LBB0_214
	0x4c, 0x89, 0xd0, //0x000010cd movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x000010d0 notq         %rax
	0x48, 0x21, 0xd0, //0x000010d3 andq         %rdx, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x000010d6 leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xd1, //0x000010da orq          %r10, %rcx
	0x48, 0x89, 0xce, //0x000010dd movq         %rcx, %rsi
	0x48, 0xf7, 0xd6, //0x000010e0 notq         %rsi
	0x48, 0x21, 0xd6, //0x000010e3 andq         %rdx, %rsi
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000010e6 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd6, //0x000010f0 andq         %rdx, %rsi
	0x45, 0x31, 0xd2, //0x000010f3 xorl         %r10d, %r10d
	0x48, 0x01, 0xc6, //0x000010f6 addq         %rax, %rsi
	0x41, 0x0f, 0x92, 0xc2, //0x000010f9 setb         %r10b
	0x48, 0x01, 0xf6, //0x000010fd addq         %rsi, %rsi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00001100 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc6, //0x0000110a xorq         %rax, %rsi
	0x48, 0x21, 0xce, //0x0000110d andq         %rcx, %rsi
	0x48, 0xf7, 0xd6, //0x00001110 notq         %rsi
	0x48, 0x21, 0xf7, //0x00001113 andq         %rsi, %rdi
	0x48, 0x85, 0xff, //0x00001116 testq        %rdi, %rdi
	0x0f, 0x84, 0x7a, 0xff, 0xff, 0xff, //0x00001119 je           LBB0_207
	0xe9, 0x6b, 0xf4, 0xff, 0xff, //0x0000111f jmp          LBB0_57
	//0x00001124 LBB0_215
	0x48, 0x8b, 0x45, 0x90, //0x00001124 movq         $-112(%rbp), %rax
	0x4c, 0x8b, 0x50, 0x08, //0x00001128 movq         $8(%rax), %r10
	0x4d, 0x29, 0xda, //0x0000112c subq         %r11, %r10
	0x0f, 0x84, 0xfa, 0x12, 0x00, 0x00, //0x0000112f je           LBB0_438
	0x4c, 0x89, 0x5d, 0xa8, //0x00001135 movq         %r11, $-88(%rbp)
	0x4d, 0x01, 0xdc, //0x00001139 addq         %r11, %r12
	0x41, 0x80, 0x3c, 0x24, 0x30, //0x0000113c cmpb         $48, (%r12)
	0x0f, 0x85, 0x36, 0x00, 0x00, 0x00, //0x00001141 jne          LBB0_220
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00001147 movl         $1, %r11d
	0x49, 0x83, 0xfa, 0x01, //0x0000114d cmpq         $1, %r10
	0x0f, 0x84, 0x7c, 0x05, 0x00, 0x00, //0x00001151 je           LBB0_303
	0x41, 0x8a, 0x44, 0x24, 0x01, //0x00001157 movb         $1(%r12), %al
	0x04, 0xd2, //0x0000115c addb         $-46, %al
	0x3c, 0x37, //0x0000115e cmpb         $55, %al
	0x0f, 0x87, 0x6d, 0x05, 0x00, 0x00, //0x00001160 ja           LBB0_303
	0x0f, 0xb6, 0xc0, //0x00001166 movzbl       %al, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00001169 movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x00001173 btq          %rax, %rcx
	0x0f, 0x83, 0x56, 0x05, 0x00, 0x00, //0x00001177 jae          LBB0_303
	//0x0000117d LBB0_220
	0x49, 0x83, 0xfa, 0x10, //0x0000117d cmpq         $16, %r10
	0x0f, 0x82, 0x30, 0x0d, 0x00, 0x00, //0x00001181 jb           LBB0_359
	0x4d, 0x8d, 0x4a, 0xf0, //0x00001187 leaq         $-16(%r10), %r9
	0x4c, 0x89, 0xc8, //0x0000118b movq         %r9, %rax
	0x48, 0x83, 0xe0, 0xf0, //0x0000118e andq         $-16, %rax
	0x4e, 0x8d, 0x44, 0x20, 0x10, //0x00001192 leaq         $16(%rax,%r12), %r8
	0x41, 0x83, 0xe1, 0x0f, //0x00001197 andl         $15, %r9d
	0x48, 0xc7, 0x45, 0xb8, 0xff, 0xff, 0xff, 0xff, //0x0000119b movq         $-1, $-72(%rbp)
	0x48, 0xc7, 0x45, 0xb0, 0xff, 0xff, 0xff, 0xff, //0x000011a3 movq         $-1, $-80(%rbp)
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x000011ab movq         $-1, %r14
	0x4d, 0x89, 0xe5, //0x000011b2 movq         %r12, %r13
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000011b5 .p2align 4, 0x90
	//0x000011c0 LBB0_222
	0xc4, 0xc1, 0x7a, 0x6f, 0x55, 0x00, //0x000011c0 vmovdqu      (%r13), %xmm2
	0xc4, 0xc1, 0x69, 0x64, 0xe0, //0x000011c6 vpcmpgtb     %xmm8, %xmm2, %xmm4
	0xc5, 0xb1, 0x64, 0xea, //0x000011cb vpcmpgtb     %xmm2, %xmm9, %xmm5
	0xc5, 0xd9, 0xdb, 0xe5, //0x000011cf vpand        %xmm5, %xmm4, %xmm4
	0xc5, 0xa9, 0x74, 0xea, //0x000011d3 vpcmpeqb     %xmm2, %xmm10, %xmm5
	0xc5, 0xa1, 0x74, 0xf2, //0x000011d7 vpcmpeqb     %xmm2, %xmm11, %xmm6
	0xc5, 0xc9, 0xeb, 0xed, //0x000011db vpor         %xmm5, %xmm6, %xmm5
	0xc5, 0x99, 0xeb, 0xf2, //0x000011df vpor         %xmm2, %xmm12, %xmm6
	0xc5, 0x91, 0x74, 0xd2, //0x000011e3 vpcmpeqb     %xmm2, %xmm13, %xmm2
	0xc5, 0x89, 0x74, 0xf6, //0x000011e7 vpcmpeqb     %xmm6, %xmm14, %xmm6
	0xc5, 0xc9, 0xeb, 0xfa, //0x000011eb vpor         %xmm2, %xmm6, %xmm7
	0xc5, 0xd1, 0xeb, 0xe4, //0x000011ef vpor         %xmm4, %xmm5, %xmm4
	0xc5, 0xc1, 0xeb, 0xe4, //0x000011f3 vpor         %xmm4, %xmm7, %xmm4
	0xc5, 0xf9, 0xd7, 0xc2, //0x000011f7 vpmovmskb    %xmm2, %eax
	0xc5, 0xf9, 0xd7, 0xde, //0x000011fb vpmovmskb    %xmm6, %ebx
	0xc5, 0xf9, 0xd7, 0xd5, //0x000011ff vpmovmskb    %xmm5, %edx
	0xc5, 0xf9, 0xd7, 0xcc, //0x00001203 vpmovmskb    %xmm4, %ecx
	0xbe, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001207 movl         $2863311530, %esi
	0x48, 0x8d, 0xbe, 0x55, 0x55, 0x55, 0x55, //0x0000120c leaq         $1431655765(%rsi), %rdi
	0x48, 0x31, 0xcf, //0x00001213 xorq         %rcx, %rdi
	0x48, 0x0f, 0xbc, 0xcf, //0x00001216 bsfq         %rdi, %rcx
	0x83, 0xf9, 0x10, //0x0000121a cmpl         $16, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x0000121d je           LBB0_224
	0xbf, 0xff, 0xff, 0xff, 0xff, //0x00001223 movl         $-1, %edi
	0xd3, 0xe7, //0x00001228 shll         %cl, %edi
	0xf7, 0xd7, //0x0000122a notl         %edi
	0x21, 0xf8, //0x0000122c andl         %edi, %eax
	0x21, 0xfb, //0x0000122e andl         %edi, %ebx
	0x21, 0xd7, //0x00001230 andl         %edx, %edi
	0x89, 0xfa, //0x00001232 movl         %edi, %edx
	//0x00001234 LBB0_224
	0x8d, 0x78, 0xff, //0x00001234 leal         $-1(%rax), %edi
	0x21, 0xc7, //0x00001237 andl         %eax, %edi
	0x0f, 0x85, 0x4f, 0x09, 0x00, 0x00, //0x00001239 jne          LBB0_337
	0x8d, 0x7b, 0xff, //0x0000123f leal         $-1(%rbx), %edi
	0x21, 0xdf, //0x00001242 andl         %ebx, %edi
	0x0f, 0x85, 0x44, 0x09, 0x00, 0x00, //0x00001244 jne          LBB0_337
	0x8d, 0x7a, 0xff, //0x0000124a leal         $-1(%rdx), %edi
	0x21, 0xd7, //0x0000124d andl         %edx, %edi
	0x0f, 0x85, 0x39, 0x09, 0x00, 0x00, //0x0000124f jne          LBB0_337
	0x85, 0xc0, //0x00001255 testl        %eax, %eax
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00001257 je           LBB0_230
	0x4c, 0x89, 0xef, //0x0000125d movq         %r13, %rdi
	0x4c, 0x29, 0xe7, //0x00001260 subq         %r12, %rdi
	0x44, 0x0f, 0xbc, 0xd8, //0x00001263 bsfl         %eax, %r11d
	0x49, 0x01, 0xfb, //0x00001267 addq         %rdi, %r11
	0x49, 0x83, 0xfe, 0xff, //0x0000126a cmpq         $-1, %r14
	0x0f, 0x85, 0x24, 0x09, 0x00, 0x00, //0x0000126e jne          LBB0_338
	0x4d, 0x89, 0xde, //0x00001274 movq         %r11, %r14
	//0x00001277 LBB0_230
	0x85, 0xdb, //0x00001277 testl        %ebx, %ebx
	0x0f, 0x84, 0x1c, 0x00, 0x00, 0x00, //0x00001279 je           LBB0_233
	0x4c, 0x89, 0xe8, //0x0000127f movq         %r13, %rax
	0x4c, 0x29, 0xe0, //0x00001282 subq         %r12, %rax
	0x44, 0x0f, 0xbc, 0xdb, //0x00001285 bsfl         %ebx, %r11d
	0x49, 0x01, 0xc3, //0x00001289 addq         %rax, %r11
	0x48, 0x83, 0x7d, 0xb0, 0xff, //0x0000128c cmpq         $-1, $-80(%rbp)
	0x0f, 0x85, 0x01, 0x09, 0x00, 0x00, //0x00001291 jne          LBB0_338
	0x4c, 0x89, 0x5d, 0xb0, //0x00001297 movq         %r11, $-80(%rbp)
	//0x0000129b LBB0_233
	0x85, 0xd2, //0x0000129b testl        %edx, %edx
	0x0f, 0x84, 0x1c, 0x00, 0x00, 0x00, //0x0000129d je           LBB0_236
	0x4c, 0x89, 0xe8, //0x000012a3 movq         %r13, %rax
	0x4c, 0x29, 0xe0, //0x000012a6 subq         %r12, %rax
	0x44, 0x0f, 0xbc, 0xda, //0x000012a9 bsfl         %edx, %r11d
	0x49, 0x01, 0xc3, //0x000012ad addq         %rax, %r11
	0x48, 0x83, 0x7d, 0xb8, 0xff, //0x000012b0 cmpq         $-1, $-72(%rbp)
	0x0f, 0x85, 0xdd, 0x08, 0x00, 0x00, //0x000012b5 jne          LBB0_338
	0x4c, 0x89, 0x5d, 0xb8, //0x000012bb movq         %r11, $-72(%rbp)
	//0x000012bf LBB0_236
	0x83, 0xf9, 0x10, //0x000012bf cmpl         $16, %ecx
	0x0f, 0x85, 0x81, 0x03, 0x00, 0x00, //0x000012c2 jne          LBB0_291
	0x49, 0x83, 0xc5, 0x10, //0x000012c8 addq         $16, %r13
	0x49, 0x83, 0xc2, 0xf0, //0x000012cc addq         $-16, %r10
	0x49, 0x83, 0xfa, 0x0f, //0x000012d0 cmpq         $15, %r10
	0x0f, 0x87, 0xe6, 0xfe, 0xff, 0xff, //0x000012d4 ja           LBB0_222
	0x4d, 0x85, 0xc9, //0x000012da testq        %r9, %r9
	0x48, 0x8d, 0x35, 0xf8, 0x14, 0x00, 0x00, //0x000012dd leaq         $5368(%rip), %rsi  /* LJTI0_2+0(%rip) */
	0x48, 0x8b, 0x7d, 0xb0, //0x000012e4 movq         $-80(%rbp), %rdi
	0x48, 0x8b, 0x5d, 0xb8, //0x000012e8 movq         $-72(%rbp), %rbx
	0x0f, 0x84, 0x65, 0x03, 0x00, 0x00, //0x000012ec je           LBB0_292
	//0x000012f2 LBB0_239
	0x4b, 0x8d, 0x0c, 0x08, //0x000012f2 leaq         (%r8,%r9), %rcx
	0xe9, 0x01, 0x01, 0x00, 0x00, //0x000012f6 jmp          LBB0_262
	//0x000012fb LBB0_240
	0x49, 0x8b, 0x01, //0x000012fb movq         (%r9), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x000012fe cmpq         $4095, %rax
	0x0f, 0x8f, 0x53, 0x10, 0x00, 0x00, //0x00001304 jg           LBB0_441
	0x48, 0x8d, 0x48, 0x01, //0x0000130a leaq         $1(%rax), %rcx
	0x49, 0x89, 0x09, //0x0000130e movq         %rcx, (%r9)
	0x49, 0xc7, 0x44, 0xc1, 0x08, 0x05, 0x00, 0x00, 0x00, //0x00001311 movq         $5, $8(%r9,%rax,8)
	0xe9, 0x91, 0xee, 0xff, 0xff, //0x0000131a jmp          LBB0_2
	//0x0000131f LBB0_242
	0x48, 0x8b, 0x4d, 0x90, //0x0000131f movq         $-112(%rbp), %rcx
	0x48, 0x8b, 0x49, 0x08, //0x00001323 movq         $8(%rcx), %rcx
	0x48, 0x8d, 0x51, 0xfc, //0x00001327 leaq         $-4(%rcx), %rdx
	0x49, 0x39, 0xd7, //0x0000132b cmpq         %rdx, %r15
	0x0f, 0x83, 0x97, 0x10, 0x00, 0x00, //0x0000132e jae          LBB0_440
	0x43, 0x8b, 0x0c, 0x1c, //0x00001334 movl         (%r12,%r11), %ecx
	0x81, 0xf9, 0x61, 0x6c, 0x73, 0x65, //0x00001338 cmpl         $1702063201, %ecx
	0x0f, 0x85, 0xfa, 0x10, 0x00, 0x00, //0x0000133e jne          LBB0_442
	0x4c, 0x89, 0xd9, //0x00001344 movq         %r11, %rcx
	0x4d, 0x8d, 0x5f, 0x05, //0x00001347 leaq         $5(%r15), %r11
	0xe9, 0x50, 0x00, 0x00, 0x00, //0x0000134b jmp          LBB0_254
	//0x00001350 LBB0_245
	0x48, 0x8b, 0x4d, 0x90, //0x00001350 movq         $-112(%rbp), %rcx
	0x48, 0x8b, 0x49, 0x08, //0x00001354 movq         $8(%rcx), %rcx
	0x48, 0x8d, 0x51, 0xfd, //0x00001358 leaq         $-3(%rcx), %rdx
	0x49, 0x39, 0xd7, //0x0000135c cmpq         %rdx, %r15
	0x0f, 0x83, 0x66, 0x10, 0x00, 0x00, //0x0000135f jae          LBB0_440
	0x41, 0x81, 0x3a, 0x6e, 0x75, 0x6c, 0x6c, //0x00001365 cmpl         $1819047278, (%r10)
	0x0f, 0x84, 0x27, 0x00, 0x00, 0x00, //0x0000136c je           LBB0_253
	0xe9, 0x1c, 0x11, 0x00, 0x00, //0x00001372 jmp          LBB0_247
	//0x00001377 LBB0_251
	0x48, 0x8b, 0x4d, 0x90, //0x00001377 movq         $-112(%rbp), %rcx
	0x48, 0x8b, 0x49, 0x08, //0x0000137b movq         $8(%rcx), %rcx
	0x48, 0x8d, 0x51, 0xfd, //0x0000137f leaq         $-3(%rcx), %rdx
	0x49, 0x39, 0xd7, //0x00001383 cmpq         %rdx, %r15
	0x0f, 0x83, 0x3f, 0x10, 0x00, 0x00, //0x00001386 jae          LBB0_440
	0x41, 0x81, 0x3a, 0x74, 0x72, 0x75, 0x65, //0x0000138c cmpl         $1702195828, (%r10)
	0x0f, 0x85, 0x4c, 0x11, 0x00, 0x00, //0x00001393 jne          LBB0_447
	//0x00001399 LBB0_253
	0x4c, 0x89, 0xd9, //0x00001399 movq         %r11, %rcx
	0x4d, 0x8d, 0x5f, 0x04, //0x0000139c leaq         $4(%r15), %r11
	//0x000013a0 LBB0_254
	0x4c, 0x89, 0x1e, //0x000013a0 movq         %r11, (%rsi)
	0x4c, 0x89, 0xf8, //0x000013a3 movq         %r15, %rax
	0x48, 0x85, 0xc9, //0x000013a6 testq        %rcx, %rcx
	0x0f, 0x8f, 0x01, 0xee, 0xff, 0xff, //0x000013a9 jg           LBB0_2
	0xe9, 0xee, 0x0f, 0x00, 0x00, //0x000013af jmp          LBB0_423
	//0x000013b4 LBB0_255
	0x49, 0x8b, 0x01, //0x000013b4 movq         (%r9), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x000013b7 cmpq         $4095, %rax
	0x0f, 0x8f, 0x9a, 0x0f, 0x00, 0x00, //0x000013bd jg           LBB0_441
	0x48, 0x8d, 0x48, 0x01, //0x000013c3 leaq         $1(%rax), %rcx
	0x49, 0x89, 0x09, //0x000013c7 movq         %rcx, (%r9)
	0x49, 0xc7, 0x44, 0xc1, 0x08, 0x06, 0x00, 0x00, 0x00, //0x000013ca movq         $6, $8(%r9,%rax,8)
	0xe9, 0xd8, 0xed, 0xff, 0xff, //0x000013d3 jmp          LBB0_2
	//0x000013d8 LBB0_259
	0x49, 0x89, 0xc3, //0x000013d8 movq         %rax, %r11
	0x4d, 0x29, 0xe3, //0x000013db subq         %r12, %r11
	0x49, 0x83, 0xfe, 0xff, //0x000013de cmpq         $-1, %r14
	0x0f, 0x85, 0x95, 0x0a, 0x00, 0x00, //0x000013e2 jne          LBB0_356
	0x49, 0xff, 0xcb, //0x000013e8 decq         %r11
	0x4d, 0x89, 0xde, //0x000013eb movq         %r11, %r14
	0x90, 0x90, //0x000013ee .p2align 4, 0x90
	//0x000013f0 LBB0_261
	0x49, 0x89, 0xc0, //0x000013f0 movq         %rax, %r8
	0x49, 0xff, 0xc9, //0x000013f3 decq         %r9
	0x0f, 0x84, 0x03, 0x0a, 0x00, 0x00, //0x000013f6 je           LBB0_341
	//0x000013fc LBB0_262
	0x41, 0x0f, 0xbe, 0x10, //0x000013fc movsbl       (%r8), %edx
	0x83, 0xc2, 0xd5, //0x00001400 addl         $-43, %edx
	0x83, 0xfa, 0x3a, //0x00001403 cmpl         $58, %edx
	0x0f, 0x87, 0x4b, 0x02, 0x00, 0x00, //0x00001406 ja           LBB0_292
	0x49, 0x8d, 0x40, 0x01, //0x0000140c leaq         $1(%r8), %rax
	0x48, 0x63, 0x14, 0x96, //0x00001410 movslq       (%rsi,%rdx,4), %rdx
	0x48, 0x01, 0xf2, //0x00001414 addq         %rsi, %rdx
	0xff, 0xe2, //0x00001417 jmpq         *%rdx
	//0x00001419 LBB0_264
	0x49, 0x89, 0xc3, //0x00001419 movq         %rax, %r11
	0x4d, 0x29, 0xe3, //0x0000141c subq         %r12, %r11
	0x48, 0x83, 0xfb, 0xff, //0x0000141f cmpq         $-1, %rbx
	0x0f, 0x85, 0x54, 0x0a, 0x00, 0x00, //0x00001423 jne          LBB0_356
	0x49, 0xff, 0xcb, //0x00001429 decq         %r11
	0x4c, 0x89, 0xdb, //0x0000142c movq         %r11, %rbx
	0xe9, 0xbc, 0xff, 0xff, 0xff, //0x0000142f jmp          LBB0_261
	//0x00001434 LBB0_266
	0x49, 0x89, 0xc3, //0x00001434 movq         %rax, %r11
	0x4d, 0x29, 0xe3, //0x00001437 subq         %r12, %r11
	0x48, 0x83, 0xff, 0xff, //0x0000143a cmpq         $-1, %rdi
	0x0f, 0x85, 0x39, 0x0a, 0x00, 0x00, //0x0000143e jne          LBB0_356
	0x49, 0xff, 0xcb, //0x00001444 decq         %r11
	0x4c, 0x89, 0xdf, //0x00001447 movq         %r11, %rdi
	0xe9, 0xa1, 0xff, 0xff, 0xff, //0x0000144a jmp          LBB0_261
	//0x0000144f LBB0_268
	0x48, 0x89, 0xd0, //0x0000144f movq         %rdx, %rax
	0x4c, 0x29, 0xd8, //0x00001452 subq         %r11, %rax
	0x0f, 0x84, 0xf6, 0x10, 0x00, 0x00, //0x00001455 je           LBB0_456
	0x4c, 0x89, 0xd9, //0x0000145b movq         %r11, %rcx
	0x4d, 0x01, 0xe3, //0x0000145e addq         %r12, %r11
	0x48, 0x83, 0xf8, 0x40, //0x00001461 cmpq         $64, %rax
	0x0f, 0x82, 0x8c, 0x0a, 0x00, 0x00, //0x00001465 jb           LBB0_361
	0x41, 0x89, 0xc2, //0x0000146b movl         %eax, %r10d
	0x41, 0x83, 0xe2, 0x3f, //0x0000146e andl         $63, %r10d
	0x4e, 0x8d, 0x64, 0x02, 0xc0, //0x00001472 leaq         $-64(%rdx,%r8), %r12
	0x49, 0x83, 0xe4, 0xc0, //0x00001477 andq         $-64, %r12
	0x49, 0x01, 0xcc, //0x0000147b addq         %rcx, %r12
	0x4c, 0x03, 0x65, 0x88, //0x0000147e addq         $-120(%rbp), %r12
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001482 movq         $-1, %r9
	0x45, 0x31, 0xc0, //0x00001489 xorl         %r8d, %r8d
	0x90, 0x90, 0x90, 0x90, //0x0000148c .p2align 4, 0x90
	//0x00001490 LBB0_271
	0xc4, 0xc1, 0x7a, 0x6f, 0x33, //0x00001490 vmovdqu      (%r11), %xmm6
	0xc4, 0xc1, 0x7a, 0x6f, 0x6b, 0x10, //0x00001495 vmovdqu      $16(%r11), %xmm5
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x20, //0x0000149b vmovdqu      $32(%r11), %xmm4
	0xc4, 0xc1, 0x7a, 0x6f, 0x7b, 0x30, //0x000014a1 vmovdqu      $48(%r11), %xmm7
	0xc5, 0xc9, 0x74, 0xd0, //0x000014a7 vpcmpeqb     %xmm0, %xmm6, %xmm2
	0xc5, 0xf9, 0xd7, 0xf2, //0x000014ab vpmovmskb    %xmm2, %esi
	0xc5, 0xd1, 0x74, 0xd0, //0x000014af vpcmpeqb     %xmm0, %xmm5, %xmm2
	0xc5, 0xf9, 0xd7, 0xda, //0x000014b3 vpmovmskb    %xmm2, %ebx
	0xc5, 0xd9, 0x74, 0xd0, //0x000014b7 vpcmpeqb     %xmm0, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xca, //0x000014bb vpmovmskb    %xmm2, %ecx
	0xc5, 0xc1, 0x74, 0xd0, //0x000014bf vpcmpeqb     %xmm0, %xmm7, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x000014c3 vpmovmskb    %xmm2, %edi
	0xc5, 0xc9, 0x74, 0xd1, //0x000014c7 vpcmpeqb     %xmm1, %xmm6, %xmm2
	0xc5, 0x79, 0xd7, 0xea, //0x000014cb vpmovmskb    %xmm2, %r13d
	0xc5, 0xd1, 0x74, 0xd1, //0x000014cf vpcmpeqb     %xmm1, %xmm5, %xmm2
	0xc5, 0xf9, 0xd7, 0xd2, //0x000014d3 vpmovmskb    %xmm2, %edx
	0xc5, 0xd9, 0x74, 0xd1, //0x000014d7 vpcmpeqb     %xmm1, %xmm4, %xmm2
	0x48, 0xc1, 0xe3, 0x10, //0x000014db shlq         $16, %rbx
	0x48, 0x09, 0xde, //0x000014df orq          %rbx, %rsi
	0xc5, 0xf9, 0xd7, 0xda, //0x000014e2 vpmovmskb    %xmm2, %ebx
	0xc5, 0xc1, 0x74, 0xd1, //0x000014e6 vpcmpeqb     %xmm1, %xmm7, %xmm2
	0x48, 0xc1, 0xe1, 0x20, //0x000014ea shlq         $32, %rcx
	0x48, 0x09, 0xce, //0x000014ee orq          %rcx, %rsi
	0xc5, 0xf9, 0xd7, 0xca, //0x000014f1 vpmovmskb    %xmm2, %ecx
	0xc5, 0x81, 0x64, 0xd6, //0x000014f5 vpcmpgtb     %xmm6, %xmm15, %xmm2
	0xc5, 0xc9, 0x64, 0xf3, //0x000014f9 vpcmpgtb     %xmm3, %xmm6, %xmm6
	0xc5, 0xc9, 0xdb, 0xd2, //0x000014fd vpand        %xmm2, %xmm6, %xmm2
	0x48, 0xc1, 0xe2, 0x10, //0x00001501 shlq         $16, %rdx
	0x49, 0x09, 0xd5, //0x00001505 orq          %rdx, %r13
	0xc5, 0xf9, 0xd7, 0xd2, //0x00001508 vpmovmskb    %xmm2, %edx
	0xc5, 0x81, 0x64, 0xd5, //0x0000150c vpcmpgtb     %xmm5, %xmm15, %xmm2
	0xc5, 0xd1, 0x64, 0xeb, //0x00001510 vpcmpgtb     %xmm3, %xmm5, %xmm5
	0xc5, 0xd1, 0xdb, 0xd2, //0x00001514 vpand        %xmm2, %xmm5, %xmm2
	0x48, 0xc1, 0xe3, 0x20, //0x00001518 shlq         $32, %rbx
	0x49, 0x09, 0xdd, //0x0000151c orq          %rbx, %r13
	0xc5, 0xf9, 0xd7, 0xda, //0x0000151f vpmovmskb    %xmm2, %ebx
	0xc5, 0x81, 0x64, 0xd4, //0x00001523 vpcmpgtb     %xmm4, %xmm15, %xmm2
	0xc5, 0xd9, 0x64, 0xe3, //0x00001527 vpcmpgtb     %xmm3, %xmm4, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x0000152b vpand        %xmm2, %xmm4, %xmm2
	0x48, 0xc1, 0xe1, 0x30, //0x0000152f shlq         $48, %rcx
	0x49, 0x09, 0xcd, //0x00001533 orq          %rcx, %r13
	0xc5, 0xf9, 0xd7, 0xca, //0x00001536 vpmovmskb    %xmm2, %ecx
	0xc5, 0x81, 0x64, 0xd7, //0x0000153a vpcmpgtb     %xmm7, %xmm15, %xmm2
	0xc5, 0xc1, 0x64, 0xe3, //0x0000153e vpcmpgtb     %xmm3, %xmm7, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x00001542 vpand        %xmm2, %xmm4, %xmm2
	0x48, 0xc1, 0xe3, 0x10, //0x00001546 shlq         $16, %rbx
	0x48, 0x09, 0xda, //0x0000154a orq          %rbx, %rdx
	0xc5, 0x79, 0xd7, 0xf2, //0x0000154d vpmovmskb    %xmm2, %r14d
	0x48, 0xc1, 0xe7, 0x30, //0x00001551 shlq         $48, %rdi
	0x48, 0xc1, 0xe1, 0x20, //0x00001555 shlq         $32, %rcx
	0x49, 0x83, 0xf9, 0xff, //0x00001559 cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x0000155d jne          LBB0_273
	0x4d, 0x85, 0xed, //0x00001563 testq        %r13, %r13
	0x0f, 0x85, 0xa4, 0x00, 0x00, 0x00, //0x00001566 jne          LBB0_287
	//0x0000156c LBB0_273
	0x49, 0xc1, 0xe6, 0x30, //0x0000156c shlq         $48, %r14
	0x48, 0x09, 0xca, //0x00001570 orq          %rcx, %rdx
	0x48, 0x09, 0xfe, //0x00001573 orq          %rdi, %rsi
	0x4c, 0x89, 0xe9, //0x00001576 movq         %r13, %rcx
	0x4c, 0x09, 0xc1, //0x00001579 orq          %r8, %rcx
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x0000157c jne          LBB0_288
	0x4c, 0x09, 0xf2, //0x00001582 orq          %r14, %rdx
	0x48, 0x85, 0xf6, //0x00001585 testq        %rsi, %rsi
	0x0f, 0x85, 0x95, 0x00, 0x00, 0x00, //0x00001588 jne          LBB0_289
	//0x0000158e LBB0_275
	0x48, 0x85, 0xd2, //0x0000158e testq        %rdx, %rdx
	0x0f, 0x85, 0xa7, 0x0f, 0x00, 0x00, //0x00001591 jne          LBB0_452
	0x48, 0x83, 0xc0, 0xc0, //0x00001597 addq         $-64, %rax
	0x49, 0x83, 0xc3, 0x40, //0x0000159b addq         $64, %r11
	0x48, 0x83, 0xf8, 0x3f, //0x0000159f cmpq         $63, %rax
	0x0f, 0x87, 0xe7, 0xfe, 0xff, 0xff, //0x000015a3 ja           LBB0_271
	0xe9, 0xf0, 0x06, 0x00, 0x00, //0x000015a9 jmp          LBB0_277
	//0x000015ae LBB0_288
	0x4c, 0x89, 0xc1, //0x000015ae movq         %r8, %rcx
	0x48, 0xf7, 0xd1, //0x000015b1 notq         %rcx
	0x4c, 0x21, 0xe9, //0x000015b4 andq         %r13, %rcx
	0x4c, 0x89, 0x55, 0xb8, //0x000015b7 movq         %r10, $-72(%rbp)
	0x4c, 0x8d, 0x14, 0x09, //0x000015bb leaq         (%rcx,%rcx), %r10
	0x4d, 0x09, 0xc2, //0x000015bf orq          %r8, %r10
	0x4c, 0x89, 0xd7, //0x000015c2 movq         %r10, %rdi
	0x48, 0xf7, 0xd7, //0x000015c5 notq         %rdi
	0x4c, 0x21, 0xef, //0x000015c8 andq         %r13, %rdi
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000015cb movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xdf, //0x000015d5 andq         %rbx, %rdi
	0x45, 0x31, 0xc0, //0x000015d8 xorl         %r8d, %r8d
	0x48, 0x01, 0xcf, //0x000015db addq         %rcx, %rdi
	0x41, 0x0f, 0x92, 0xc0, //0x000015de setb         %r8b
	0x48, 0x01, 0xff, //0x000015e2 addq         %rdi, %rdi
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000015e5 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xcf, //0x000015ef xorq         %rcx, %rdi
	0x4c, 0x21, 0xd7, //0x000015f2 andq         %r10, %rdi
	0x4c, 0x8b, 0x55, 0xb8, //0x000015f5 movq         $-72(%rbp), %r10
	0x48, 0xf7, 0xd7, //0x000015f9 notq         %rdi
	0x48, 0x21, 0xfe, //0x000015fc andq         %rdi, %rsi
	0x4c, 0x09, 0xf2, //0x000015ff orq          %r14, %rdx
	0x48, 0x85, 0xf6, //0x00001602 testq        %rsi, %rsi
	0x0f, 0x84, 0x83, 0xff, 0xff, 0xff, //0x00001605 je           LBB0_275
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x0000160b jmp          LBB0_289
	//0x00001610 LBB0_287
	0x4c, 0x89, 0xdb, //0x00001610 movq         %r11, %rbx
	0x48, 0x2b, 0x5d, 0xd0, //0x00001613 subq         $-48(%rbp), %rbx
	0x4d, 0x0f, 0xbc, 0xcd, //0x00001617 bsfq         %r13, %r9
	0x49, 0x01, 0xd9, //0x0000161b addq         %rbx, %r9
	0xe9, 0x49, 0xff, 0xff, 0xff, //0x0000161e jmp          LBB0_273
	//0x00001623 LBB0_289
	0x48, 0x0f, 0xbc, 0xc6, //0x00001623 bsfq         %rsi, %rax
	0x48, 0x85, 0xd2, //0x00001627 testq        %rdx, %rdx
	0x4c, 0x8b, 0x65, 0xd0, //0x0000162a movq         $-48(%rbp), %r12
	0x0f, 0x84, 0xbe, 0x00, 0x00, 0x00, //0x0000162e je           LBB0_304
	0x48, 0x0f, 0xbc, 0xca, //0x00001634 bsfq         %rdx, %rcx
	0x4d, 0x29, 0xe3, //0x00001638 subq         %r12, %r11
	0x48, 0x39, 0xc1, //0x0000163b cmpq         %rax, %rcx
	0x0f, 0x83, 0x94, 0xf8, 0xff, 0xff, //0x0000163e jae          LBB0_186
	0xe9, 0x11, 0x0f, 0x00, 0x00, //0x00001644 jmp          LBB0_306
	//0x00001649 LBB0_291
	0x49, 0x01, 0xcd, //0x00001649 addq         %rcx, %r13
	0x4d, 0x89, 0xe8, //0x0000164c movq         %r13, %r8
	0x48, 0x8b, 0x7d, 0xb0, //0x0000164f movq         $-80(%rbp), %rdi
	0x48, 0x8b, 0x5d, 0xb8, //0x00001653 movq         $-72(%rbp), %rbx
	//0x00001657 LBB0_292
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001657 movq         $-1, %r11
	0x48, 0x85, 0xff, //0x0000165e testq        %rdi, %rdi
	0x48, 0x8b, 0x75, 0xc8, //0x00001661 movq         $-56(%rbp), %rsi
	0x0f, 0x84, 0xcb, 0x0d, 0x00, 0x00, //0x00001665 je           LBB0_439
	//0x0000166b LBB0_293
	0x48, 0x85, 0xdb, //0x0000166b testq        %rbx, %rbx
	0x0f, 0x84, 0xc2, 0x0d, 0x00, 0x00, //0x0000166e je           LBB0_439
	0x4d, 0x85, 0xf6, //0x00001674 testq        %r14, %r14
	0x4c, 0x8b, 0x4d, 0xc0, //0x00001677 movq         $-64(%rbp), %r9
	0x0f, 0x84, 0xb5, 0x0d, 0x00, 0x00, //0x0000167b je           LBB0_439
	0x4d, 0x29, 0xe0, //0x00001681 subq         %r12, %r8
	0x49, 0x8d, 0x40, 0xff, //0x00001684 leaq         $-1(%r8), %rax
	0x48, 0x39, 0xc7, //0x00001688 cmpq         %rax, %rdi
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x0000168b je           LBB0_301
	0x49, 0x39, 0xc6, //0x00001691 cmpq         %rax, %r14
	0x0f, 0x84, 0x2a, 0x00, 0x00, 0x00, //0x00001694 je           LBB0_301
	0x48, 0x39, 0xc3, //0x0000169a cmpq         %rax, %rbx
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x0000169d je           LBB0_301
	0x48, 0x85, 0xdb, //0x000016a3 testq        %rbx, %rbx
	0x0f, 0x8e, 0x6e, 0x00, 0x00, 0x00, //0x000016a6 jle          LBB0_309
	0x48, 0x8d, 0x43, 0xff, //0x000016ac leaq         $-1(%rbx), %rax
	0x48, 0x39, 0xc7, //0x000016b0 cmpq         %rax, %rdi
	0x0f, 0x84, 0x61, 0x00, 0x00, 0x00, //0x000016b3 je           LBB0_309
	0x48, 0xf7, 0xd3, //0x000016b9 notq         %rbx
	0x49, 0x89, 0xdb, //0x000016bc movq         %rbx, %r11
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x000016bf jmp          LBB0_302
	//0x000016c4 LBB0_301
	0x49, 0xf7, 0xd8, //0x000016c4 negq         %r8
	0x4d, 0x89, 0xc3, //0x000016c7 movq         %r8, %r11
	//0x000016ca LBB0_302
	0x4d, 0x85, 0xdb, //0x000016ca testq        %r11, %r11
	0x0f, 0x88, 0x63, 0x0d, 0x00, 0x00, //0x000016cd js           LBB0_439
	//0x000016d3 LBB0_303
	0x48, 0x8b, 0x4d, 0xa8, //0x000016d3 movq         $-88(%rbp), %rcx
	0x49, 0x01, 0xcb, //0x000016d7 addq         %rcx, %r11
	0x4c, 0x89, 0x1e, //0x000016da movq         %r11, (%rsi)
	0x4c, 0x89, 0xf8, //0x000016dd movq         %r15, %rax
	0x48, 0x85, 0xc9, //0x000016e0 testq        %rcx, %rcx
	0x4c, 0x8b, 0x65, 0xd0, //0x000016e3 movq         $-48(%rbp), %r12
	0x0f, 0x8f, 0xc3, 0xea, 0xff, 0xff, //0x000016e7 jg           LBB0_2
	0xe9, 0xb0, 0x0c, 0x00, 0x00, //0x000016ed jmp          LBB0_423
	//0x000016f2 LBB0_304
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x000016f2 movl         $64, %ecx
	0x4d, 0x29, 0xe3, //0x000016f7 subq         %r12, %r11
	0x48, 0x39, 0xc1, //0x000016fa cmpq         %rax, %rcx
	0x0f, 0x83, 0xd5, 0xf7, 0xff, 0xff, //0x000016fd jae          LBB0_186
	0xe9, 0x52, 0x0e, 0x00, 0x00, //0x00001703 jmp          LBB0_306
	//0x00001708 LBB0_308
	0x4d, 0x29, 0xd6, //0x00001708 subq         %r10, %r14
	0x44, 0x0f, 0xbc, 0xde, //0x0000170b bsfl         %esi, %r11d
	0x4d, 0x01, 0xf3, //0x0000170f addq         %r14, %r11
	0x49, 0xf7, 0xd3, //0x00001712 notq         %r11
	0xe9, 0xa6, 0x03, 0x00, 0x00, //0x00001715 jmp          LBB0_326
	//0x0000171a LBB0_309
	0x4c, 0x89, 0xf0, //0x0000171a movq         %r14, %rax
	0x48, 0x09, 0xf8, //0x0000171d orq          %rdi, %rax
	0x49, 0x39, 0xfe, //0x00001720 cmpq         %rdi, %r14
	0x0f, 0x8c, 0x66, 0x01, 0x00, 0x00, //0x00001723 jl           LBB0_312
	0x48, 0x85, 0xc0, //0x00001729 testq        %rax, %rax
	0x0f, 0x88, 0x5d, 0x01, 0x00, 0x00, //0x0000172c js           LBB0_312
	0x49, 0xf7, 0xd6, //0x00001732 notq         %r14
	0x4d, 0x89, 0xf3, //0x00001735 movq         %r14, %r11
	0xe9, 0x8d, 0xff, 0xff, 0xff, //0x00001738 jmp          LBB0_302
	//0x0000173d LBB0_49
	0x4c, 0x8b, 0x5d, 0xb8, //0x0000173d movq         $-72(%rbp), %r11
	0x4d, 0x89, 0xd5, //0x00001741 movq         %r10, %r13
	0x49, 0x83, 0xfd, 0x20, //0x00001744 cmpq         $32, %r13
	0x0f, 0x82, 0x66, 0x08, 0x00, 0x00, //0x00001748 jb           LBB0_366
	//0x0000174e LBB0_50
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x0000174e vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x00001753 vmovdqu      $16(%r11), %xmm4
	0xc5, 0xe9, 0x74, 0xe8, //0x00001759 vpcmpeqb     %xmm0, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xcd, //0x0000175d vpmovmskb    %xmm5, %ecx
	0xc5, 0xd9, 0x74, 0xe8, //0x00001761 vpcmpeqb     %xmm0, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xd5, //0x00001765 vpmovmskb    %xmm5, %edx
	0xc5, 0xe9, 0x74, 0xd1, //0x00001769 vpcmpeqb     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x0000176d vpmovmskb    %xmm2, %edi
	0xc5, 0xd9, 0x74, 0xd1, //0x00001771 vpcmpeqb     %xmm1, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xc2, //0x00001775 vpmovmskb    %xmm2, %eax
	0x48, 0xc1, 0xe2, 0x10, //0x00001779 shlq         $16, %rdx
	0x48, 0xc1, 0xe0, 0x10, //0x0000177d shlq         $16, %rax
	0x48, 0x09, 0xc7, //0x00001781 orq          %rax, %rdi
	0x49, 0x83, 0xf9, 0xff, //0x00001784 cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00001788 jne          LBB0_52
	0x48, 0x85, 0xff, //0x0000178e testq        %rdi, %rdi
	0x0f, 0x85, 0xbc, 0x07, 0x00, 0x00, //0x00001791 jne          LBB0_363
	//0x00001797 LBB0_52
	0x48, 0x09, 0xca, //0x00001797 orq          %rcx, %rdx
	0x48, 0x89, 0xf8, //0x0000179a movq         %rdi, %rax
	0x4c, 0x09, 0xe0, //0x0000179d orq          %r12, %rax
	0x0f, 0x85, 0xca, 0x07, 0x00, 0x00, //0x000017a0 jne          LBB0_364
	//0x000017a6 LBB0_53
	0x48, 0x85, 0xd2, //0x000017a6 testq        %rdx, %rdx
	0x0f, 0x84, 0xfd, 0x07, 0x00, 0x00, //0x000017a9 je           LBB0_365
	//0x000017af LBB0_54
	0x48, 0x0f, 0xbc, 0xc2, //0x000017af bsfq         %rdx, %rax
	0xe9, 0xdb, 0xed, 0xff, 0xff, //0x000017b3 jmp          LBB0_58
	//0x000017b8 LBB0_113
	0x4d, 0x89, 0xd3, //0x000017b8 movq         %r10, %r11
	0x48, 0x8b, 0x45, 0xb8, //0x000017bb movq         $-72(%rbp), %rax
	0x4c, 0x8b, 0x65, 0xd0, //0x000017bf movq         $-48(%rbp), %r12
	0x48, 0x83, 0xf8, 0x20, //0x000017c3 cmpq         $32, %rax
	0x0f, 0x82, 0x66, 0x02, 0x00, 0x00, //0x000017c7 jb           LBB0_317
	//0x000017cd LBB0_114
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x000017cd vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x000017d2 vmovdqu      $16(%r11), %xmm4
	0xc5, 0xe9, 0x74, 0xe8, //0x000017d8 vpcmpeqb     %xmm0, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xcd, //0x000017dc vpmovmskb    %xmm5, %ecx
	0xc5, 0xd9, 0x74, 0xe8, //0x000017e0 vpcmpeqb     %xmm0, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xf5, //0x000017e4 vpmovmskb    %xmm5, %esi
	0xc5, 0xe9, 0x74, 0xe9, //0x000017e8 vpcmpeqb     %xmm1, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xd5, //0x000017ec vpmovmskb    %xmm5, %edx
	0xc5, 0xd9, 0x74, 0xe9, //0x000017f0 vpcmpeqb     %xmm1, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xdd, //0x000017f4 vpmovmskb    %xmm5, %ebx
	0xc5, 0x81, 0x64, 0xea, //0x000017f8 vpcmpgtb     %xmm2, %xmm15, %xmm5
	0xc5, 0xe9, 0x64, 0xd3, //0x000017fc vpcmpgtb     %xmm3, %xmm2, %xmm2
	0xc5, 0xe9, 0xdb, 0xd5, //0x00001800 vpand        %xmm5, %xmm2, %xmm2
	0xc5, 0x79, 0xd7, 0xf2, //0x00001804 vpmovmskb    %xmm2, %r14d
	0xc5, 0x81, 0x64, 0xd4, //0x00001808 vpcmpgtb     %xmm4, %xmm15, %xmm2
	0xc5, 0xd9, 0x64, 0xe3, //0x0000180c vpcmpgtb     %xmm3, %xmm4, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x00001810 vpand        %xmm2, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x00001814 vpmovmskb    %xmm2, %edi
	0x48, 0xc1, 0xe6, 0x10, //0x00001818 shlq         $16, %rsi
	0x48, 0xc1, 0xe3, 0x10, //0x0000181c shlq         $16, %rbx
	0x48, 0x09, 0xda, //0x00001820 orq          %rbx, %rdx
	0x49, 0x83, 0xf9, 0xff, //0x00001823 cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00001827 jne          LBB0_116
	0x48, 0x85, 0xd2, //0x0000182d testq        %rdx, %rdx
	0x0f, 0x85, 0xf8, 0x07, 0x00, 0x00, //0x00001830 jne          LBB0_373
	//0x00001836 LBB0_116
	0x48, 0xc1, 0xe7, 0x10, //0x00001836 shlq         $16, %rdi
	0x48, 0x09, 0xce, //0x0000183a orq          %rcx, %rsi
	0x48, 0x89, 0xd1, //0x0000183d movq         %rdx, %rcx
	0x4c, 0x09, 0xc1, //0x00001840 orq          %r8, %rcx
	0x0f, 0x85, 0xcb, 0x06, 0x00, 0x00, //0x00001843 jne          LBB0_362
	//0x00001849 LBB0_117
	0x4c, 0x09, 0xf7, //0x00001849 orq          %r14, %rdi
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x0000184c movl         $64, %ecx
	0xba, 0x40, 0x00, 0x00, 0x00, //0x00001851 movl         $64, %edx
	0x48, 0x85, 0xf6, //0x00001856 testq        %rsi, %rsi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00001859 je           LBB0_119
	0x48, 0x0f, 0xbc, 0xd6, //0x0000185f bsfq         %rsi, %rdx
	//0x00001863 LBB0_119
	0x48, 0x85, 0xff, //0x00001863 testq        %rdi, %rdi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00001866 je           LBB0_121
	0x48, 0x0f, 0xbc, 0xcf, //0x0000186c bsfq         %rdi, %rcx
	//0x00001870 LBB0_121
	0x48, 0x85, 0xf6, //0x00001870 testq        %rsi, %rsi
	0x0f, 0x84, 0xa9, 0x01, 0x00, 0x00, //0x00001873 je           LBB0_315
	//0x00001879 LBB0_122
	0x4d, 0x29, 0xe3, //0x00001879 subq         %r12, %r11
	0x48, 0x39, 0xd1, //0x0000187c cmpq         %rdx, %rcx
	0x0f, 0x82, 0xd5, 0x0c, 0x00, 0x00, //0x0000187f jb           LBB0_306
	0x4d, 0x8d, 0x5c, 0x13, 0x01, //0x00001885 leaq         $1(%r11,%rdx), %r11
	0xe9, 0x4e, 0xf6, 0xff, 0xff, //0x0000188a jmp          LBB0_187
	//0x0000188f LBB0_312
	0x48, 0x85, 0xc0, //0x0000188f testq        %rax, %rax
	0x48, 0x8d, 0x47, 0xff, //0x00001892 leaq         $-1(%rdi), %rax
	0x48, 0xf7, 0xd7, //0x00001896 notq         %rdi
	0x49, 0x0f, 0x48, 0xf8, //0x00001899 cmovsq       %r8, %rdi
	0x49, 0x39, 0xc6, //0x0000189d cmpq         %rax, %r14
	0x49, 0x0f, 0x45, 0xf8, //0x000018a0 cmovneq      %r8, %rdi
	0x49, 0x89, 0xfb, //0x000018a4 movq         %rdi, %r11
	0xe9, 0x1e, 0xfe, 0xff, 0xff, //0x000018a7 jmp          LBB0_302
	//0x000018ac LBB0_313
	0x48, 0xf7, 0xd0, //0x000018ac notq         %rax
	0x49, 0x89, 0xc3, //0x000018af movq         %rax, %r11
	0xe9, 0x09, 0x02, 0x00, 0x00, //0x000018b2 jmp          LBB0_326
	//0x000018b7 LBB0_314
	0x48, 0x89, 0x4d, 0xa8, //0x000018b7 movq         %rcx, $-88(%rbp)
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000018bb movq         $-1, %rax
	0x4d, 0x85, 0xed, //0x000018c2 testq        %r13, %r13
	0x48, 0x8b, 0x75, 0xc8, //0x000018c5 movq         $-56(%rbp), %rsi
	0x0f, 0x85, 0x86, 0xf3, 0xff, 0xff, //0x000018c9 jne          LBB0_151
	0xe9, 0xbe, 0x0a, 0x00, 0x00, //0x000018cf jmp          LBB0_420
	//0x000018d4 LBB0_134
	0x4c, 0x8b, 0x5d, 0xb8, //0x000018d4 movq         $-72(%rbp), %r11
	0x4d, 0x89, 0xd5, //0x000018d8 movq         %r10, %r13
	0x49, 0x83, 0xfd, 0x20, //0x000018db cmpq         $32, %r13
	0x0f, 0x82, 0x57, 0x08, 0x00, 0x00, //0x000018df jb           LBB0_382
	//0x000018e5 LBB0_135
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x000018e5 vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x000018ea vmovdqu      $16(%r11), %xmm4
	0xc5, 0xe9, 0x74, 0xe8, //0x000018f0 vpcmpeqb     %xmm0, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xcd, //0x000018f4 vpmovmskb    %xmm5, %ecx
	0xc5, 0xd9, 0x74, 0xe8, //0x000018f8 vpcmpeqb     %xmm0, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xd5, //0x000018fc vpmovmskb    %xmm5, %edx
	0xc5, 0xe9, 0x74, 0xd1, //0x00001900 vpcmpeqb     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x00001904 vpmovmskb    %xmm2, %edi
	0xc5, 0xd9, 0x74, 0xd1, //0x00001908 vpcmpeqb     %xmm1, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xc2, //0x0000190c vpmovmskb    %xmm2, %eax
	0x48, 0xc1, 0xe2, 0x10, //0x00001910 shlq         $16, %rdx
	0x48, 0xc1, 0xe0, 0x10, //0x00001914 shlq         $16, %rax
	0x48, 0x09, 0xc7, //0x00001918 orq          %rax, %rdi
	0x49, 0x83, 0xfc, 0xff, //0x0000191b cmpq         $-1, %r12
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x0000191f jne          LBB0_137
	0x48, 0x85, 0xff, //0x00001925 testq        %rdi, %rdi
	0x0f, 0x85, 0xad, 0x07, 0x00, 0x00, //0x00001928 jne          LBB0_379
	//0x0000192e LBB0_137
	0x48, 0x09, 0xca, //0x0000192e orq          %rcx, %rdx
	0x48, 0x89, 0xf8, //0x00001931 movq         %rdi, %rax
	0x4c, 0x09, 0xc8, //0x00001934 orq          %r9, %rax
	0x0f, 0x85, 0xbb, 0x07, 0x00, 0x00, //0x00001937 jne          LBB0_380
	//0x0000193d LBB0_138
	0x48, 0x85, 0xd2, //0x0000193d testq        %rdx, %rdx
	0x0f, 0x84, 0xee, 0x07, 0x00, 0x00, //0x00001940 je           LBB0_381
	//0x00001946 LBB0_139
	0x48, 0x0f, 0xbc, 0xc2, //0x00001946 bsfq         %rdx, %rax
	0xe9, 0x94, 0xf2, 0xff, 0xff, //0x0000194a jmp          LBB0_143
	//0x0000194f LBB0_170
	0x4d, 0x89, 0xc3, //0x0000194f movq         %r8, %r11
	0x4c, 0x89, 0xd0, //0x00001952 movq         %r10, %rax
	0x48, 0x83, 0xf8, 0x20, //0x00001955 cmpq         $32, %rax
	0x0f, 0x82, 0x7b, 0x01, 0x00, 0x00, //0x00001959 jb           LBB0_329
	//0x0000195f LBB0_171
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x0000195f vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x00001964 vmovdqu      $16(%r11), %xmm4
	0xc5, 0xe9, 0x74, 0xe8, //0x0000196a vpcmpeqb     %xmm0, %xmm2, %xmm5
	0xc5, 0x79, 0xd7, 0xc5, //0x0000196e vpmovmskb    %xmm5, %r8d
	0xc5, 0xd9, 0x74, 0xe8, //0x00001972 vpcmpeqb     %xmm0, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xf5, //0x00001976 vpmovmskb    %xmm5, %esi
	0xc5, 0xe9, 0x74, 0xe9, //0x0000197a vpcmpeqb     %xmm1, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xd5, //0x0000197e vpmovmskb    %xmm5, %edx
	0xc5, 0xd9, 0x74, 0xe9, //0x00001982 vpcmpeqb     %xmm1, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xcd, //0x00001986 vpmovmskb    %xmm5, %ecx
	0xc5, 0x81, 0x64, 0xea, //0x0000198a vpcmpgtb     %xmm2, %xmm15, %xmm5
	0xc5, 0xe9, 0x64, 0xd3, //0x0000198e vpcmpgtb     %xmm3, %xmm2, %xmm2
	0xc5, 0xe9, 0xdb, 0xd5, //0x00001992 vpand        %xmm5, %xmm2, %xmm2
	0xc5, 0x79, 0xd7, 0xf2, //0x00001996 vpmovmskb    %xmm2, %r14d
	0xc5, 0x81, 0x64, 0xd4, //0x0000199a vpcmpgtb     %xmm4, %xmm15, %xmm2
	0xc5, 0xd9, 0x64, 0xe3, //0x0000199e vpcmpgtb     %xmm3, %xmm4, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x000019a2 vpand        %xmm2, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x000019a6 vpmovmskb    %xmm2, %edi
	0x48, 0xc1, 0xe6, 0x10, //0x000019aa shlq         $16, %rsi
	0x48, 0xc1, 0xe1, 0x10, //0x000019ae shlq         $16, %rcx
	0x48, 0x09, 0xca, //0x000019b2 orq          %rcx, %rdx
	0x49, 0x83, 0xfc, 0xff, //0x000019b5 cmpq         $-1, %r12
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x000019b9 jne          LBB0_173
	0x48, 0x85, 0xd2, //0x000019bf testq        %rdx, %rdx
	0x0f, 0x85, 0x02, 0x08, 0x00, 0x00, //0x000019c2 jne          LBB0_390
	//0x000019c8 LBB0_173
	0x48, 0xc1, 0xe7, 0x10, //0x000019c8 shlq         $16, %rdi
	0x4c, 0x09, 0xc6, //0x000019cc orq          %r8, %rsi
	0x48, 0x89, 0xd1, //0x000019cf movq         %rdx, %rcx
	0x4c, 0x09, 0xc9, //0x000019d2 orq          %r9, %rcx
	0x0f, 0x85, 0x65, 0x06, 0x00, 0x00, //0x000019d5 jne          LBB0_374
	//0x000019db LBB0_174
	0x4c, 0x09, 0xf7, //0x000019db orq          %r14, %rdi
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x000019de movl         $64, %ecx
	0xba, 0x40, 0x00, 0x00, 0x00, //0x000019e3 movl         $64, %edx
	0x48, 0x85, 0xf6, //0x000019e8 testq        %rsi, %rsi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x000019eb je           LBB0_176
	0x48, 0x0f, 0xbc, 0xd6, //0x000019f1 bsfq         %rsi, %rdx
	//0x000019f5 LBB0_176
	0x48, 0x85, 0xff, //0x000019f5 testq        %rdi, %rdi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x000019f8 je           LBB0_178
	0x48, 0x0f, 0xbc, 0xcf, //0x000019fe bsfq         %rdi, %rcx
	//0x00001a02 LBB0_178
	0x48, 0x85, 0xf6, //0x00001a02 testq        %rsi, %rsi
	0x0f, 0x84, 0xbe, 0x00, 0x00, 0x00, //0x00001a05 je           LBB0_327
	0x4c, 0x2b, 0x5d, 0xd0, //0x00001a0b subq         $-48(%rbp), %r11
	0x48, 0x39, 0xd1, //0x00001a0f cmpq         %rdx, %rcx
	0x0f, 0x82, 0x83, 0x0b, 0x00, 0x00, //0x00001a12 jb           LBB0_458
	0x4d, 0x8d, 0x5c, 0x13, 0x01, //0x00001a18 leaq         $1(%r11,%rdx), %r11
	0xe9, 0xc8, 0xf1, 0xff, 0xff, //0x00001a1d jmp          LBB0_144
	//0x00001a22 LBB0_315
	0x48, 0x85, 0xff, //0x00001a22 testq        %rdi, %rdi
	0x0f, 0x85, 0x5c, 0x0b, 0x00, 0x00, //0x00001a25 jne          LBB0_457
	0x49, 0x83, 0xc3, 0x20, //0x00001a2b addq         $32, %r11
	0x48, 0x83, 0xc0, 0xe0, //0x00001a2f addq         $-32, %rax
	//0x00001a33 LBB0_317
	0x4d, 0x85, 0xc0, //0x00001a33 testq        %r8, %r8
	0x0f, 0x85, 0x6f, 0x06, 0x00, 0x00, //0x00001a36 jne          LBB0_377
	0x48, 0x85, 0xc0, //0x00001a3c testq        %rax, %rax
	0x0f, 0x84, 0x2e, 0x09, 0x00, 0x00, //0x00001a3f je           LBB0_416
	//0x00001a45 LBB0_319
	0x41, 0x0f, 0xb6, 0x0b, //0x00001a45 movzbl       (%r11), %ecx
	0x80, 0xf9, 0x22, //0x00001a49 cmpb         $34, %cl
	0x0f, 0x84, 0xa4, 0x03, 0x00, 0x00, //0x00001a4c je           LBB0_355
	0x80, 0xf9, 0x5c, //0x00001a52 cmpb         $92, %cl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00001a55 je           LBB0_323
	0x80, 0xf9, 0x1f, //0x00001a5b cmpb         $31, %cl
	0x0f, 0x86, 0x2f, 0x0b, 0x00, 0x00, //0x00001a5e jbe          LBB0_461
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001a64 movq         $-1, %rcx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00001a6b movl         $1, %edx
	0x49, 0x01, 0xd3, //0x00001a70 addq         %rdx, %r11
	0x48, 0x01, 0xc8, //0x00001a73 addq         %rcx, %rax
	0x0f, 0x85, 0xc9, 0xff, 0xff, 0xff, //0x00001a76 jne          LBB0_319
	0xe9, 0xf2, 0x08, 0x00, 0x00, //0x00001a7c jmp          LBB0_416
	//0x00001a81 LBB0_323
	0x48, 0x83, 0xf8, 0x01, //0x00001a81 cmpq         $1, %rax
	0x0f, 0x84, 0xe8, 0x08, 0x00, 0x00, //0x00001a85 je           LBB0_416
	0x4c, 0x89, 0xd9, //0x00001a8b movq         %r11, %rcx
	0x4c, 0x8b, 0x65, 0xd0, //0x00001a8e movq         $-48(%rbp), %r12
	0x4c, 0x29, 0xe1, //0x00001a92 subq         %r12, %rcx
	0x49, 0x83, 0xf9, 0xff, //0x00001a95 cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc9, //0x00001a99 cmoveq       %rcx, %r9
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00001a9d movq         $-2, %rcx
	0xba, 0x02, 0x00, 0x00, 0x00, //0x00001aa4 movl         $2, %edx
	0x49, 0x01, 0xd3, //0x00001aa9 addq         %rdx, %r11
	0x48, 0x01, 0xc8, //0x00001aac addq         %rcx, %rax
	0x0f, 0x85, 0x90, 0xff, 0xff, 0xff, //0x00001aaf jne          LBB0_319
	0xe9, 0xb9, 0x08, 0x00, 0x00, //0x00001ab5 jmp          LBB0_416
	//0x00001aba LBB0_325
	0x48, 0xf7, 0xd6, //0x00001aba notq         %rsi
	0x49, 0x89, 0xf3, //0x00001abd movq         %rsi, %r11
	//0x00001ac0 LBB0_326
	0x48, 0x8b, 0x75, 0xc8, //0x00001ac0 movq         $-56(%rbp), %rsi
	0xe9, 0xff, 0xf1, 0xff, 0xff, //0x00001ac4 jmp          LBB0_160
	//0x00001ac9 LBB0_327
	0x48, 0x85, 0xff, //0x00001ac9 testq        %rdi, %rdi
	0x0f, 0x85, 0xdb, 0x0a, 0x00, 0x00, //0x00001acc jne          LBB0_459
	0x49, 0x83, 0xc3, 0x20, //0x00001ad2 addq         $32, %r11
	0x48, 0x83, 0xc0, 0xe0, //0x00001ad6 addq         $-32, %rax
	//0x00001ada LBB0_329
	0x4d, 0x85, 0xc9, //0x00001ada testq        %r9, %r9
	0x0f, 0x85, 0x2e, 0x07, 0x00, 0x00, //0x00001add jne          LBB0_393
	0x48, 0x8b, 0x75, 0xc8, //0x00001ae3 movq         $-56(%rbp), %rsi
	0x4c, 0x8b, 0x4d, 0xc0, //0x00001ae7 movq         $-64(%rbp), %r9
	0x48, 0x85, 0xc0, //0x00001aeb testq        %rax, %rax
	0x0f, 0x84, 0xc7, 0x08, 0x00, 0x00, //0x00001aee je           LBB0_425
	//0x00001af4 LBB0_331
	0x41, 0x0f, 0xb6, 0x0b, //0x00001af4 movzbl       (%r11), %ecx
	0x80, 0xf9, 0x22, //0x00001af8 cmpb         $34, %cl
	0x0f, 0x84, 0xa7, 0x00, 0x00, 0x00, //0x00001afb je           LBB0_340
	0x80, 0xf9, 0x5c, //0x00001b01 cmpb         $92, %cl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00001b04 je           LBB0_335
	0x80, 0xf9, 0x1f, //0x00001b0a cmpb         $31, %cl
	0x0f, 0x86, 0xa3, 0x0a, 0x00, 0x00, //0x00001b0d jbe          LBB0_460
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001b13 movq         $-1, %rcx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00001b1a movl         $1, %edx
	0x49, 0x01, 0xd3, //0x00001b1f addq         %rdx, %r11
	0x48, 0x01, 0xc8, //0x00001b22 addq         %rcx, %rax
	0x0f, 0x85, 0xc9, 0xff, 0xff, 0xff, //0x00001b25 jne          LBB0_331
	0xe9, 0x8b, 0x08, 0x00, 0x00, //0x00001b2b jmp          LBB0_425
	//0x00001b30 LBB0_335
	0x48, 0x83, 0xf8, 0x01, //0x00001b30 cmpq         $1, %rax
	0x0f, 0x84, 0x8f, 0x0a, 0x00, 0x00, //0x00001b34 je           LBB0_462
	0x4c, 0x89, 0xd9, //0x00001b3a movq         %r11, %rcx
	0x48, 0x2b, 0x4d, 0xd0, //0x00001b3d subq         $-48(%rbp), %rcx
	0x49, 0x83, 0xfc, 0xff, //0x00001b41 cmpq         $-1, %r12
	0x4c, 0x0f, 0x44, 0xe1, //0x00001b45 cmoveq       %rcx, %r12
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00001b49 movq         $-2, %rcx
	0xba, 0x02, 0x00, 0x00, 0x00, //0x00001b50 movl         $2, %edx
	0x48, 0x8b, 0x75, 0xc8, //0x00001b55 movq         $-56(%rbp), %rsi
	0x4c, 0x8b, 0x4d, 0xc0, //0x00001b59 movq         $-64(%rbp), %r9
	0x49, 0x01, 0xd3, //0x00001b5d addq         %rdx, %r11
	0x48, 0x01, 0xc8, //0x00001b60 addq         %rcx, %rax
	0x0f, 0x85, 0x8b, 0xff, 0xff, 0xff, //0x00001b63 jne          LBB0_331
	0xe9, 0x4d, 0x08, 0x00, 0x00, //0x00001b69 jmp          LBB0_425
	//0x00001b6e LBB0_418
	0x48, 0xf7, 0xda, //0x00001b6e negq         %rdx
	0x48, 0x8b, 0x75, 0xc8, //0x00001b71 movq         $-56(%rbp), %rsi
	0x4c, 0x8b, 0x65, 0xd0, //0x00001b75 movq         $-48(%rbp), %r12
	0x4c, 0x8b, 0x4d, 0xc0, //0x00001b79 movq         $-64(%rbp), %r9
	0x49, 0x89, 0xd3, //0x00001b7d movq         %rdx, %r11
	0x4d, 0x85, 0xdb, //0x00001b80 testq        %r11, %r11
	0x0f, 0x89, 0x0b, 0xe6, 0xff, 0xff, //0x00001b83 jns          LBB0_258
	0xe9, 0x01, 0x08, 0x00, 0x00, //0x00001b89 jmp          LBB0_419
	//0x00001b8e LBB0_337
	0x4d, 0x29, 0xe5, //0x00001b8e subq         %r12, %r13
	0x44, 0x0f, 0xbc, 0xdf, //0x00001b91 bsfl         %edi, %r11d
	0x4d, 0x01, 0xeb, //0x00001b95 addq         %r13, %r11
	//0x00001b98 LBB0_338
	0x49, 0xf7, 0xd3, //0x00001b98 notq         %r11
	//0x00001b9b LBB0_339
	0x48, 0x8b, 0x75, 0xc8, //0x00001b9b movq         $-56(%rbp), %rsi
	0x4c, 0x8b, 0x4d, 0xc0, //0x00001b9f movq         $-64(%rbp), %r9
	0xe9, 0x22, 0xfb, 0xff, 0xff, //0x00001ba3 jmp          LBB0_302
	//0x00001ba8 LBB0_340
	0x4c, 0x03, 0x5d, 0x98, //0x00001ba8 addq         $-104(%rbp), %r11
	0xe9, 0x9f, 0xf3, 0xff, 0xff, //0x00001bac jmp          LBB0_195
	//0x00001bb1 LBB0_208
	0x4d, 0x89, 0xc3, //0x00001bb1 movq         %r8, %r11
	0x4c, 0x8b, 0x65, 0xb8, //0x00001bb4 movq         $-72(%rbp), %r12
	0x49, 0x83, 0xfc, 0x20, //0x00001bb8 cmpq         $32, %r12
	0x0f, 0x82, 0x69, 0x00, 0x00, 0x00, //0x00001bbc jb           LBB0_399
	//0x00001bc2 LBB0_209
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x00001bc2 vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x00001bc7 vmovdqu      $16(%r11), %xmm4
	0xc5, 0xe9, 0x74, 0xe8, //0x00001bcd vpcmpeqb     %xmm0, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xcd, //0x00001bd1 vpmovmskb    %xmm5, %ecx
	0xc5, 0xd9, 0x74, 0xe8, //0x00001bd5 vpcmpeqb     %xmm0, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xd5, //0x00001bd9 vpmovmskb    %xmm5, %edx
	0xc5, 0xe9, 0x74, 0xd1, //0x00001bdd vpcmpeqb     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x00001be1 vpmovmskb    %xmm2, %edi
	0xc5, 0xd9, 0x74, 0xd1, //0x00001be5 vpcmpeqb     %xmm1, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xc2, //0x00001be9 vpmovmskb    %xmm2, %eax
	0x48, 0xc1, 0xe2, 0x10, //0x00001bed shlq         $16, %rdx
	0x48, 0xc1, 0xe0, 0x10, //0x00001bf1 shlq         $16, %rax
	0x48, 0x09, 0xc7, //0x00001bf5 orq          %rax, %rdi
	0x49, 0x83, 0xf9, 0xff, //0x00001bf8 cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00001bfc jne          LBB0_211
	0x48, 0x85, 0xff, //0x00001c02 testq        %rdi, %rdi
	0x0f, 0x85, 0x75, 0x06, 0x00, 0x00, //0x00001c05 jne          LBB0_396
	//0x00001c0b LBB0_211
	0x48, 0x09, 0xca, //0x00001c0b orq          %rcx, %rdx
	0x48, 0x89, 0xf8, //0x00001c0e movq         %rdi, %rax
	0x4c, 0x09, 0xd0, //0x00001c11 orq          %r10, %rax
	0x0f, 0x85, 0x83, 0x06, 0x00, 0x00, //0x00001c14 jne          LBB0_397
	//0x00001c1a LBB0_212
	0x48, 0x85, 0xd2, //0x00001c1a testq        %rdx, %rdx
	0x0f, 0x85, 0x8c, 0xfb, 0xff, 0xff, //0x00001c1d jne          LBB0_54
	//0x00001c23 LBB0_398
	0x49, 0x83, 0xc3, 0x20, //0x00001c23 addq         $32, %r11
	0x49, 0x83, 0xc4, 0xe0, //0x00001c27 addq         $-32, %r12
	//0x00001c2b LBB0_399
	0x4d, 0x85, 0xd2, //0x00001c2b testq        %r10, %r10
	0x0f, 0x85, 0xbe, 0x06, 0x00, 0x00, //0x00001c2e jne          LBB0_411
	0x4d, 0x85, 0xe4, //0x00001c34 testq        %r12, %r12
	0x0f, 0x84, 0x36, 0x07, 0x00, 0x00, //0x00001c37 je           LBB0_416
	//0x00001c3d LBB0_401
	0x49, 0x8d, 0x4b, 0x01, //0x00001c3d leaq         $1(%r11), %rcx
	0x41, 0x0f, 0xb6, 0x1b, //0x00001c41 movzbl       (%r11), %ebx
	0x80, 0xfb, 0x22, //0x00001c45 cmpb         $34, %bl
	0x0f, 0x84, 0x20, 0x02, 0x00, 0x00, //0x00001c48 je           LBB0_406
	0x49, 0x8d, 0x54, 0x24, 0xff, //0x00001c4e leaq         $-1(%r12), %rdx
	0x80, 0xfb, 0x5c, //0x00001c53 cmpb         $92, %bl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00001c56 je           LBB0_404
	0x49, 0x89, 0xd4, //0x00001c5c movq         %rdx, %r12
	0x49, 0x89, 0xcb, //0x00001c5f movq         %rcx, %r11
	0x48, 0x85, 0xd2, //0x00001c62 testq        %rdx, %rdx
	0x0f, 0x85, 0xd2, 0xff, 0xff, 0xff, //0x00001c65 jne          LBB0_401
	0xe9, 0x03, 0x07, 0x00, 0x00, //0x00001c6b jmp          LBB0_416
	//0x00001c70 LBB0_404
	0x48, 0x85, 0xd2, //0x00001c70 testq        %rdx, %rdx
	0x0f, 0x84, 0xfa, 0x06, 0x00, 0x00, //0x00001c73 je           LBB0_416
	0x48, 0x03, 0x4d, 0xa0, //0x00001c79 addq         $-96(%rbp), %rcx
	0x49, 0x83, 0xf9, 0xff, //0x00001c7d cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc9, //0x00001c81 cmoveq       %rcx, %r9
	0x49, 0x83, 0xc3, 0x02, //0x00001c85 addq         $2, %r11
	0x49, 0x83, 0xc4, 0xfe, //0x00001c89 addq         $-2, %r12
	0x4c, 0x89, 0xe2, //0x00001c8d movq         %r12, %rdx
	0x48, 0x85, 0xd2, //0x00001c90 testq        %rdx, %rdx
	0x0f, 0x85, 0xa4, 0xff, 0xff, 0xff, //0x00001c93 jne          LBB0_401
	0xe9, 0xd5, 0x06, 0x00, 0x00, //0x00001c99 jmp          LBB0_416
	//0x00001c9e LBB0_277
	0x4d, 0x89, 0xe3, //0x00001c9e movq         %r12, %r11
	0x4c, 0x89, 0xd0, //0x00001ca1 movq         %r10, %rax
	0x48, 0x83, 0xf8, 0x20, //0x00001ca4 cmpq         $32, %rax
	0x4c, 0x8b, 0x65, 0xd0, //0x00001ca8 movq         $-48(%rbp), %r12
	0x0f, 0x82, 0xbd, 0x00, 0x00, 0x00, //0x00001cac jb           LBB0_345
	//0x00001cb2 LBB0_278
	0xc4, 0xc1, 0x7a, 0x6f, 0x13, //0x00001cb2 vmovdqu      (%r11), %xmm2
	0xc4, 0xc1, 0x7a, 0x6f, 0x63, 0x10, //0x00001cb7 vmovdqu      $16(%r11), %xmm4
	0xc5, 0xe9, 0x74, 0xe8, //0x00001cbd vpcmpeqb     %xmm0, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xcd, //0x00001cc1 vpmovmskb    %xmm5, %ecx
	0xc5, 0xd9, 0x74, 0xe8, //0x00001cc5 vpcmpeqb     %xmm0, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xf5, //0x00001cc9 vpmovmskb    %xmm5, %esi
	0xc5, 0xe9, 0x74, 0xe9, //0x00001ccd vpcmpeqb     %xmm1, %xmm2, %xmm5
	0xc5, 0xf9, 0xd7, 0xd5, //0x00001cd1 vpmovmskb    %xmm5, %edx
	0xc5, 0xd9, 0x74, 0xe9, //0x00001cd5 vpcmpeqb     %xmm1, %xmm4, %xmm5
	0xc5, 0xf9, 0xd7, 0xdd, //0x00001cd9 vpmovmskb    %xmm5, %ebx
	0xc5, 0x81, 0x64, 0xea, //0x00001cdd vpcmpgtb     %xmm2, %xmm15, %xmm5
	0xc5, 0xe9, 0x64, 0xd3, //0x00001ce1 vpcmpgtb     %xmm3, %xmm2, %xmm2
	0xc5, 0xe9, 0xdb, 0xd5, //0x00001ce5 vpand        %xmm5, %xmm2, %xmm2
	0xc5, 0x79, 0xd7, 0xf2, //0x00001ce9 vpmovmskb    %xmm2, %r14d
	0xc5, 0x81, 0x64, 0xd4, //0x00001ced vpcmpgtb     %xmm4, %xmm15, %xmm2
	0xc5, 0xd9, 0x64, 0xe3, //0x00001cf1 vpcmpgtb     %xmm3, %xmm4, %xmm4
	0xc5, 0xd9, 0xdb, 0xd2, //0x00001cf5 vpand        %xmm2, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xfa, //0x00001cf9 vpmovmskb    %xmm2, %edi
	0x48, 0xc1, 0xe6, 0x10, //0x00001cfd shlq         $16, %rsi
	0x48, 0xc1, 0xe3, 0x10, //0x00001d01 shlq         $16, %rbx
	0x48, 0x09, 0xda, //0x00001d05 orq          %rbx, %rdx
	0x49, 0x83, 0xf9, 0xff, //0x00001d08 cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00001d0c jne          LBB0_280
	0x48, 0x85, 0xd2, //0x00001d12 testq        %rdx, %rdx
	0x0f, 0x85, 0xc5, 0x05, 0x00, 0x00, //0x00001d15 jne          LBB0_408
	//0x00001d1b LBB0_280
	0x48, 0xc1, 0xe7, 0x10, //0x00001d1b shlq         $16, %rdi
	0x48, 0x09, 0xce, //0x00001d1f orq          %rcx, %rsi
	0x48, 0x89, 0xd1, //0x00001d22 movq         %rdx, %rcx
	0x4c, 0x09, 0xc1, //0x00001d25 orq          %r8, %rcx
	0x0f, 0x85, 0x17, 0x05, 0x00, 0x00, //0x00001d28 jne          LBB0_395
	//0x00001d2e LBB0_281
	0x4c, 0x09, 0xf7, //0x00001d2e orq          %r14, %rdi
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00001d31 movl         $64, %ecx
	0xba, 0x40, 0x00, 0x00, 0x00, //0x00001d36 movl         $64, %edx
	0x48, 0x85, 0xf6, //0x00001d3b testq        %rsi, %rsi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00001d3e je           LBB0_283
	0x48, 0x0f, 0xbc, 0xd6, //0x00001d44 bsfq         %rsi, %rdx
	//0x00001d48 LBB0_283
	0x48, 0x85, 0xff, //0x00001d48 testq        %rdi, %rdi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00001d4b je           LBB0_285
	0x48, 0x0f, 0xbc, 0xcf, //0x00001d51 bsfq         %rdi, %rcx
	//0x00001d55 LBB0_285
	0x48, 0x85, 0xf6, //0x00001d55 testq        %rsi, %rsi
	0x0f, 0x85, 0x1b, 0xfb, 0xff, 0xff, //0x00001d58 jne          LBB0_122
	0x48, 0x85, 0xff, //0x00001d5e testq        %rdi, %rdi
	0x0f, 0x85, 0x20, 0x08, 0x00, 0x00, //0x00001d61 jne          LBB0_457
	0x49, 0x83, 0xc3, 0x20, //0x00001d67 addq         $32, %r11
	0x48, 0x83, 0xc0, 0xe0, //0x00001d6b addq         $-32, %rax
	//0x00001d6f LBB0_345
	0x4d, 0x85, 0xc0, //0x00001d6f testq        %r8, %r8
	0x0f, 0x85, 0xa6, 0x05, 0x00, 0x00, //0x00001d72 jne          LBB0_409
	0x48, 0x85, 0xc0, //0x00001d78 testq        %rax, %rax
	0x0f, 0x84, 0xf2, 0x05, 0x00, 0x00, //0x00001d7b je           LBB0_416
	//0x00001d81 LBB0_347
	0x41, 0x0f, 0xb6, 0x0b, //0x00001d81 movzbl       (%r11), %ecx
	0x80, 0xf9, 0x22, //0x00001d85 cmpb         $34, %cl
	0x0f, 0x84, 0x68, 0x00, 0x00, 0x00, //0x00001d88 je           LBB0_355
	0x80, 0xf9, 0x5c, //0x00001d8e cmpb         $92, %cl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00001d91 je           LBB0_351
	0x80, 0xf9, 0x1f, //0x00001d97 cmpb         $31, %cl
	0x0f, 0x86, 0xf3, 0x07, 0x00, 0x00, //0x00001d9a jbe          LBB0_461
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001da0 movq         $-1, %rcx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00001da7 movl         $1, %edx
	0x49, 0x01, 0xd3, //0x00001dac addq         %rdx, %r11
	0x48, 0x01, 0xc8, //0x00001daf addq         %rcx, %rax
	0x0f, 0x85, 0xc9, 0xff, 0xff, 0xff, //0x00001db2 jne          LBB0_347
	0xe9, 0xb6, 0x05, 0x00, 0x00, //0x00001db8 jmp          LBB0_416
	//0x00001dbd LBB0_351
	0x48, 0x83, 0xf8, 0x01, //0x00001dbd cmpq         $1, %rax
	0x0f, 0x84, 0xac, 0x05, 0x00, 0x00, //0x00001dc1 je           LBB0_416
	0x4c, 0x89, 0xd9, //0x00001dc7 movq         %r11, %rcx
	0x4c, 0x8b, 0x65, 0xd0, //0x00001dca movq         $-48(%rbp), %r12
	0x4c, 0x29, 0xe1, //0x00001dce subq         %r12, %rcx
	0x49, 0x83, 0xf9, 0xff, //0x00001dd1 cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc9, //0x00001dd5 cmoveq       %rcx, %r9
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00001dd9 movq         $-2, %rcx
	0xba, 0x02, 0x00, 0x00, 0x00, //0x00001de0 movl         $2, %edx
	0x49, 0x01, 0xd3, //0x00001de5 addq         %rdx, %r11
	0x48, 0x01, 0xc8, //0x00001de8 addq         %rcx, %rax
	0x0f, 0x85, 0x90, 0xff, 0xff, 0xff, //0x00001deb jne          LBB0_347
	0xe9, 0x7d, 0x05, 0x00, 0x00, //0x00001df1 jmp          LBB0_416
	//0x00001df6 LBB0_355
	0x4c, 0x03, 0x5d, 0x98, //0x00001df6 addq         $-104(%rbp), %r11
	0xe9, 0xde, 0xf0, 0xff, 0xff, //0x00001dfa jmp          LBB0_187
	//0x00001dff LBB0_341
	0x49, 0x89, 0xc8, //0x00001dff movq         %rcx, %r8
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001e02 movq         $-1, %r11
	0x48, 0x85, 0xff, //0x00001e09 testq        %rdi, %rdi
	0x48, 0x8b, 0x75, 0xc8, //0x00001e0c movq         $-56(%rbp), %rsi
	0x0f, 0x85, 0x55, 0xf8, 0xff, 0xff, //0x00001e10 jne          LBB0_293
	0xe9, 0x1b, 0x06, 0x00, 0x00, //0x00001e16 jmp          LBB0_439
	//0x00001e1b LBB0_342
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001e1b movq         $-1, %r11
	0x4c, 0x89, 0x55, 0xa8, //0x00001e22 movq         %r10, $-88(%rbp)
	0x49, 0x89, 0xf9, //0x00001e26 movq         %rdi, %r9
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00001e29 movq         $-1, %r13
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00001e30 movq         $-1, %r12
	0xe9, 0x54, 0xe9, 0xff, 0xff, //0x00001e37 jmp          LBB0_90
	//0x00001e3c LBB0_353
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001e3c movq         $-1, %r9
	0x45, 0x31, 0xe4, //0x00001e43 xorl         %r12d, %r12d
	0x49, 0x83, 0xfd, 0x20, //0x00001e46 cmpq         $32, %r13
	0x0f, 0x83, 0xfe, 0xf8, 0xff, 0xff, //0x00001e4a jae          LBB0_50
	0xe9, 0x5f, 0x01, 0x00, 0x00, //0x00001e50 jmp          LBB0_366
	//0x00001e55 LBB0_354
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001e55 movq         $-1, %r9
	0x45, 0x31, 0xc0, //0x00001e5c xorl         %r8d, %r8d
	0x48, 0x83, 0xf8, 0x20, //0x00001e5f cmpq         $32, %rax
	0x0f, 0x83, 0x64, 0xf9, 0xff, 0xff, //0x00001e63 jae          LBB0_114
	0xe9, 0xc5, 0xfb, 0xff, 0xff, //0x00001e69 jmp          LBB0_317
	//0x00001e6e LBB0_406
	0x4c, 0x8b, 0x65, 0xd0, //0x00001e6e movq         $-48(%rbp), %r12
	//0x00001e72 LBB0_407
	0x4c, 0x29, 0xe1, //0x00001e72 subq         %r12, %rcx
	0x49, 0x89, 0xcb, //0x00001e75 movq         %rcx, %r11
	0xe9, 0x60, 0xf0, 0xff, 0xff, //0x00001e78 jmp          LBB0_187
	//0x00001e7d LBB0_356
	0x49, 0xf7, 0xdb, //0x00001e7d negq         %r11
	0xe9, 0x16, 0xfd, 0xff, 0xff, //0x00001e80 jmp          LBB0_339
	//0x00001e85 LBB0_357
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00001e85 movq         $-1, %r12
	0x45, 0x31, 0xc9, //0x00001e8c xorl         %r9d, %r9d
	0x49, 0x83, 0xfd, 0x20, //0x00001e8f cmpq         $32, %r13
	0x0f, 0x83, 0x4c, 0xfa, 0xff, 0xff, //0x00001e93 jae          LBB0_135
	0xe9, 0x9e, 0x02, 0x00, 0x00, //0x00001e99 jmp          LBB0_382
	//0x00001e9e LBB0_358
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00001e9e movq         $-1, %r12
	0x45, 0x31, 0xc9, //0x00001ea5 xorl         %r9d, %r9d
	0x48, 0x83, 0xf8, 0x20, //0x00001ea8 cmpq         $32, %rax
	0x0f, 0x83, 0xad, 0xfa, 0xff, 0xff, //0x00001eac jae          LBB0_171
	0xe9, 0x23, 0xfc, 0xff, 0xff, //0x00001eb2 jmp          LBB0_329
	//0x00001eb7 LBB0_359
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00001eb7 movq         $-1, %r14
	0x4d, 0x89, 0xe0, //0x00001ebe movq         %r12, %r8
	0x4d, 0x89, 0xd1, //0x00001ec1 movq         %r10, %r9
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00001ec4 movq         $-1, %rdi
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001ecb movq         $-1, %rbx
	0x48, 0x8d, 0x35, 0x03, 0x09, 0x00, 0x00, //0x00001ed2 leaq         $2307(%rip), %rsi  /* LJTI0_2+0(%rip) */
	0xe9, 0x14, 0xf4, 0xff, 0xff, //0x00001ed9 jmp          LBB0_239
	//0x00001ede LBB0_360
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001ede movq         $-1, %r9
	0x45, 0x31, 0xd2, //0x00001ee5 xorl         %r10d, %r10d
	0x49, 0x83, 0xfc, 0x20, //0x00001ee8 cmpq         $32, %r12
	0x0f, 0x83, 0xd0, 0xfc, 0xff, 0xff, //0x00001eec jae          LBB0_209
	0xe9, 0x34, 0xfd, 0xff, 0xff, //0x00001ef2 jmp          LBB0_399
	//0x00001ef7 LBB0_361
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001ef7 movq         $-1, %r9
	0x45, 0x31, 0xc0, //0x00001efe xorl         %r8d, %r8d
	0x48, 0x83, 0xf8, 0x20, //0x00001f01 cmpq         $32, %rax
	0x4c, 0x8b, 0x65, 0xd0, //0x00001f05 movq         $-48(%rbp), %r12
	0x0f, 0x83, 0xa3, 0xfd, 0xff, 0xff, //0x00001f09 jae          LBB0_278
	0xe9, 0x5b, 0xfe, 0xff, 0xff, //0x00001f0f jmp          LBB0_345
	//0x00001f14 LBB0_362
	0x44, 0x89, 0xc1, //0x00001f14 movl         %r8d, %ecx
	0xf7, 0xd1, //0x00001f17 notl         %ecx
	0x21, 0xd1, //0x00001f19 andl         %edx, %ecx
	0x44, 0x8d, 0x24, 0x09, //0x00001f1b leal         (%rcx,%rcx), %r12d
	0x45, 0x09, 0xc4, //0x00001f1f orl          %r8d, %r12d
	0xbb, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001f22 movl         $2863311530, %ebx
	0x44, 0x31, 0xe3, //0x00001f27 xorl         %r12d, %ebx
	0x21, 0xd3, //0x00001f2a andl         %edx, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001f2c andl         $-1431655766, %ebx
	0x45, 0x31, 0xc0, //0x00001f32 xorl         %r8d, %r8d
	0x01, 0xcb, //0x00001f35 addl         %ecx, %ebx
	0x41, 0x0f, 0x92, 0xc0, //0x00001f37 setb         %r8b
	0x01, 0xdb, //0x00001f3b addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x00001f3d xorl         $1431655765, %ebx
	0x44, 0x21, 0xe3, //0x00001f43 andl         %r12d, %ebx
	0x4c, 0x8b, 0x65, 0xd0, //0x00001f46 movq         $-48(%rbp), %r12
	0xf7, 0xd3, //0x00001f4a notl         %ebx
	0x21, 0xde, //0x00001f4c andl         %ebx, %esi
	0xe9, 0xf6, 0xf8, 0xff, 0xff, //0x00001f4e jmp          LBB0_117
	//0x00001f53 LBB0_363
	0x4c, 0x89, 0xd8, //0x00001f53 movq         %r11, %rax
	0x48, 0x2b, 0x45, 0xd0, //0x00001f56 subq         $-48(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xcf, //0x00001f5a bsfq         %rdi, %r9
	0x49, 0x01, 0xc1, //0x00001f5e addq         %rax, %r9
	0x48, 0x09, 0xca, //0x00001f61 orq          %rcx, %rdx
	0x48, 0x89, 0xf8, //0x00001f64 movq         %rdi, %rax
	0x4c, 0x09, 0xe0, //0x00001f67 orq          %r12, %rax
	0x0f, 0x84, 0x36, 0xf8, 0xff, 0xff, //0x00001f6a je           LBB0_53
	//0x00001f70 LBB0_364
	0x44, 0x89, 0xe0, //0x00001f70 movl         %r12d, %eax
	0xf7, 0xd0, //0x00001f73 notl         %eax
	0x21, 0xf8, //0x00001f75 andl         %edi, %eax
	0x8d, 0x0c, 0x00, //0x00001f77 leal         (%rax,%rax), %ecx
	0x44, 0x09, 0xe1, //0x00001f7a orl          %r12d, %ecx
	0xbe, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001f7d movl         $2863311530, %esi
	0x31, 0xce, //0x00001f82 xorl         %ecx, %esi
	0x21, 0xfe, //0x00001f84 andl         %edi, %esi
	0x81, 0xe6, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001f86 andl         $-1431655766, %esi
	0x45, 0x31, 0xe4, //0x00001f8c xorl         %r12d, %r12d
	0x01, 0xc6, //0x00001f8f addl         %eax, %esi
	0x41, 0x0f, 0x92, 0xc4, //0x00001f91 setb         %r12b
	0x01, 0xf6, //0x00001f95 addl         %esi, %esi
	0x81, 0xf6, 0x55, 0x55, 0x55, 0x55, //0x00001f97 xorl         $1431655765, %esi
	0x21, 0xce, //0x00001f9d andl         %ecx, %esi
	0xf7, 0xd6, //0x00001f9f notl         %esi
	0x21, 0xf2, //0x00001fa1 andl         %esi, %edx
	0x48, 0x85, 0xd2, //0x00001fa3 testq        %rdx, %rdx
	0x0f, 0x85, 0x03, 0xf8, 0xff, 0xff, //0x00001fa6 jne          LBB0_54
	//0x00001fac LBB0_365
	0x49, 0x83, 0xc3, 0x20, //0x00001fac addq         $32, %r11
	0x49, 0x83, 0xc5, 0xe0, //0x00001fb0 addq         $-32, %r13
	//0x00001fb4 LBB0_366
	0x4d, 0x85, 0xe4, //0x00001fb4 testq        %r12, %r12
	0x0f, 0x85, 0xbe, 0x00, 0x00, 0x00, //0x00001fb7 jne          LBB0_375
	0x4c, 0x8b, 0x65, 0xd0, //0x00001fbd movq         $-48(%rbp), %r12
	0x4d, 0x85, 0xed, //0x00001fc1 testq        %r13, %r13
	0x0f, 0x84, 0xa9, 0x03, 0x00, 0x00, //0x00001fc4 je           LBB0_416
	//0x00001fca LBB0_368
	0x49, 0x8d, 0x4b, 0x01, //0x00001fca leaq         $1(%r11), %rcx
	0x41, 0x0f, 0xb6, 0x1b, //0x00001fce movzbl       (%r11), %ebx
	0x80, 0xfb, 0x22, //0x00001fd2 cmpb         $34, %bl
	0x0f, 0x84, 0x97, 0xfe, 0xff, 0xff, //0x00001fd5 je           LBB0_407
	0x49, 0x8d, 0x55, 0xff, //0x00001fdb leaq         $-1(%r13), %rdx
	0x80, 0xfb, 0x5c, //0x00001fdf cmpb         $92, %bl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00001fe2 je           LBB0_371
	0x49, 0x89, 0xd5, //0x00001fe8 movq         %rdx, %r13
	0x49, 0x89, 0xcb, //0x00001feb movq         %rcx, %r11
	0x48, 0x85, 0xd2, //0x00001fee testq        %rdx, %rdx
	0x0f, 0x85, 0xd3, 0xff, 0xff, 0xff, //0x00001ff1 jne          LBB0_368
	0xe9, 0x77, 0x03, 0x00, 0x00, //0x00001ff7 jmp          LBB0_416
	//0x00001ffc LBB0_371
	0x48, 0x85, 0xd2, //0x00001ffc testq        %rdx, %rdx
	0x0f, 0x84, 0x6e, 0x03, 0x00, 0x00, //0x00001fff je           LBB0_416
	0x48, 0x03, 0x4d, 0xa0, //0x00002005 addq         $-96(%rbp), %rcx
	0x49, 0x83, 0xf9, 0xff, //0x00002009 cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc9, //0x0000200d cmoveq       %rcx, %r9
	0x49, 0x83, 0xc3, 0x02, //0x00002011 addq         $2, %r11
	0x49, 0x83, 0xc5, 0xfe, //0x00002015 addq         $-2, %r13
	0x4c, 0x89, 0xea, //0x00002019 movq         %r13, %rdx
	0x4c, 0x8b, 0x65, 0xd0, //0x0000201c movq         $-48(%rbp), %r12
	0x48, 0x85, 0xd2, //0x00002020 testq        %rdx, %rdx
	0x0f, 0x85, 0xa1, 0xff, 0xff, 0xff, //0x00002023 jne          LBB0_368
	0xe9, 0x45, 0x03, 0x00, 0x00, //0x00002029 jmp          LBB0_416
	//0x0000202e LBB0_373
	0x4c, 0x89, 0xdb, //0x0000202e movq         %r11, %rbx
	0x4c, 0x29, 0xe3, //0x00002031 subq         %r12, %rbx
	0x4c, 0x0f, 0xbc, 0xca, //0x00002034 bsfq         %rdx, %r9
	0x49, 0x01, 0xd9, //0x00002038 addq         %rbx, %r9
	0xe9, 0xf6, 0xf7, 0xff, 0xff, //0x0000203b jmp          LBB0_116
	//0x00002040 LBB0_374
	0x44, 0x89, 0xc9, //0x00002040 movl         %r9d, %ecx
	0xf7, 0xd1, //0x00002043 notl         %ecx
	0x21, 0xd1, //0x00002045 andl         %edx, %ecx
	0x44, 0x8d, 0x04, 0x09, //0x00002047 leal         (%rcx,%rcx), %r8d
	0x45, 0x09, 0xc8, //0x0000204b orl          %r9d, %r8d
	0xbb, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000204e movl         $2863311530, %ebx
	0x44, 0x31, 0xc3, //0x00002053 xorl         %r8d, %ebx
	0x21, 0xd3, //0x00002056 andl         %edx, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002058 andl         $-1431655766, %ebx
	0x45, 0x31, 0xc9, //0x0000205e xorl         %r9d, %r9d
	0x01, 0xcb, //0x00002061 addl         %ecx, %ebx
	0x41, 0x0f, 0x92, 0xc1, //0x00002063 setb         %r9b
	0x01, 0xdb, //0x00002067 addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x00002069 xorl         $1431655765, %ebx
	0x44, 0x21, 0xc3, //0x0000206f andl         %r8d, %ebx
	0xf7, 0xd3, //0x00002072 notl         %ebx
	0x21, 0xde, //0x00002074 andl         %ebx, %esi
	0xe9, 0x60, 0xf9, 0xff, 0xff, //0x00002076 jmp          LBB0_174
	//0x0000207b LBB0_375
	0x4d, 0x85, 0xed, //0x0000207b testq        %r13, %r13
	0x0f, 0x84, 0xef, 0x02, 0x00, 0x00, //0x0000207e je           LBB0_416
	0x48, 0x8b, 0x45, 0xa0, //0x00002084 movq         $-96(%rbp), %rax
	0x4c, 0x01, 0xd8, //0x00002088 addq         %r11, %rax
	0x49, 0x83, 0xf9, 0xff, //0x0000208b cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc8, //0x0000208f cmoveq       %rax, %r9
	0x49, 0xff, 0xc3, //0x00002093 incq         %r11
	0x49, 0xff, 0xcd, //0x00002096 decq         %r13
	0x4c, 0x8b, 0x65, 0xd0, //0x00002099 movq         $-48(%rbp), %r12
	0x4d, 0x85, 0xed, //0x0000209d testq        %r13, %r13
	0x0f, 0x85, 0x24, 0xff, 0xff, 0xff, //0x000020a0 jne          LBB0_368
	0xe9, 0xc8, 0x02, 0x00, 0x00, //0x000020a6 jmp          LBB0_416
	//0x000020ab LBB0_377
	0x48, 0x85, 0xc0, //0x000020ab testq        %rax, %rax
	0x0f, 0x84, 0xbf, 0x02, 0x00, 0x00, //0x000020ae je           LBB0_416
	0x48, 0x8b, 0x4d, 0xa0, //0x000020b4 movq         $-96(%rbp), %rcx
	0x4c, 0x01, 0xd9, //0x000020b8 addq         %r11, %rcx
	0x49, 0x83, 0xf9, 0xff, //0x000020bb cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc9, //0x000020bf cmoveq       %rcx, %r9
	0x49, 0xff, 0xc3, //0x000020c3 incq         %r11
	0x48, 0xff, 0xc8, //0x000020c6 decq         %rax
	0x4c, 0x8b, 0x65, 0xd0, //0x000020c9 movq         $-48(%rbp), %r12
	0x48, 0x85, 0xc0, //0x000020cd testq        %rax, %rax
	0x0f, 0x85, 0x6f, 0xf9, 0xff, 0xff, //0x000020d0 jne          LBB0_319
	0xe9, 0x98, 0x02, 0x00, 0x00, //0x000020d6 jmp          LBB0_416
	//0x000020db LBB0_379
	0x4c, 0x89, 0xd8, //0x000020db movq         %r11, %rax
	0x48, 0x2b, 0x45, 0xd0, //0x000020de subq         $-48(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xe7, //0x000020e2 bsfq         %rdi, %r12
	0x49, 0x01, 0xc4, //0x000020e6 addq         %rax, %r12
	0x48, 0x09, 0xca, //0x000020e9 orq          %rcx, %rdx
	0x48, 0x89, 0xf8, //0x000020ec movq         %rdi, %rax
	0x4c, 0x09, 0xc8, //0x000020ef orq          %r9, %rax
	0x0f, 0x84, 0x45, 0xf8, 0xff, 0xff, //0x000020f2 je           LBB0_138
	//0x000020f8 LBB0_380
	0x44, 0x89, 0xc8, //0x000020f8 movl         %r9d, %eax
	0xf7, 0xd0, //0x000020fb notl         %eax
	0x21, 0xf8, //0x000020fd andl         %edi, %eax
	0x8d, 0x0c, 0x00, //0x000020ff leal         (%rax,%rax), %ecx
	0x44, 0x09, 0xc9, //0x00002102 orl          %r9d, %ecx
	0xbe, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002105 movl         $2863311530, %esi
	0x31, 0xce, //0x0000210a xorl         %ecx, %esi
	0x21, 0xfe, //0x0000210c andl         %edi, %esi
	0x81, 0xe6, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000210e andl         $-1431655766, %esi
	0x45, 0x31, 0xc9, //0x00002114 xorl         %r9d, %r9d
	0x01, 0xc6, //0x00002117 addl         %eax, %esi
	0x41, 0x0f, 0x92, 0xc1, //0x00002119 setb         %r9b
	0x01, 0xf6, //0x0000211d addl         %esi, %esi
	0x81, 0xf6, 0x55, 0x55, 0x55, 0x55, //0x0000211f xorl         $1431655765, %esi
	0x21, 0xce, //0x00002125 andl         %ecx, %esi
	0xf7, 0xd6, //0x00002127 notl         %esi
	0x21, 0xf2, //0x00002129 andl         %esi, %edx
	0x48, 0x85, 0xd2, //0x0000212b testq        %rdx, %rdx
	0x0f, 0x85, 0x12, 0xf8, 0xff, 0xff, //0x0000212e jne          LBB0_139
	//0x00002134 LBB0_381
	0x49, 0x83, 0xc3, 0x20, //0x00002134 addq         $32, %r11
	0x49, 0x83, 0xc5, 0xe0, //0x00002138 addq         $-32, %r13
	//0x0000213c LBB0_382
	0x4d, 0x85, 0xc9, //0x0000213c testq        %r9, %r9
	0x0f, 0x85, 0x98, 0x00, 0x00, 0x00, //0x0000213f jne          LBB0_391
	0x48, 0x8b, 0x75, 0xc8, //0x00002145 movq         $-56(%rbp), %rsi
	0x4c, 0x8b, 0x4d, 0xc0, //0x00002149 movq         $-64(%rbp), %r9
	0x4d, 0x85, 0xed, //0x0000214d testq        %r13, %r13
	0x0f, 0x84, 0x65, 0x02, 0x00, 0x00, //0x00002150 je           LBB0_425
	//0x00002156 LBB0_384
	0x49, 0x8d, 0x4b, 0x01, //0x00002156 leaq         $1(%r11), %rcx
	0x41, 0x0f, 0xb6, 0x1b, //0x0000215a movzbl       (%r11), %ebx
	0x80, 0xfb, 0x22, //0x0000215e cmpb         $34, %bl
	0x0f, 0x84, 0x57, 0x00, 0x00, 0x00, //0x00002161 je           LBB0_389
	0x49, 0x8d, 0x55, 0xff, //0x00002167 leaq         $-1(%r13), %rdx
	0x80, 0xfb, 0x5c, //0x0000216b cmpb         $92, %bl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x0000216e je           LBB0_387
	0x49, 0x89, 0xd5, //0x00002174 movq         %rdx, %r13
	0x49, 0x89, 0xcb, //0x00002177 movq         %rcx, %r11
	0x48, 0x85, 0xd2, //0x0000217a testq        %rdx, %rdx
	0x0f, 0x85, 0xd3, 0xff, 0xff, 0xff, //0x0000217d jne          LBB0_384
	0xe9, 0x33, 0x02, 0x00, 0x00, //0x00002183 jmp          LBB0_425
	//0x00002188 LBB0_387
	0x48, 0x85, 0xd2, //0x00002188 testq        %rdx, %rdx
	0x0f, 0x84, 0x38, 0x04, 0x00, 0x00, //0x0000218b je           LBB0_462
	0x48, 0x03, 0x4d, 0xa0, //0x00002191 addq         $-96(%rbp), %rcx
	0x49, 0x83, 0xfc, 0xff, //0x00002195 cmpq         $-1, %r12
	0x4c, 0x0f, 0x44, 0xe1, //0x00002199 cmoveq       %rcx, %r12
	0x49, 0x83, 0xc3, 0x02, //0x0000219d addq         $2, %r11
	0x49, 0x83, 0xc5, 0xfe, //0x000021a1 addq         $-2, %r13
	0x4c, 0x89, 0xea, //0x000021a5 movq         %r13, %rdx
	0x48, 0x8b, 0x75, 0xc8, //0x000021a8 movq         $-56(%rbp), %rsi
	0x4c, 0x8b, 0x4d, 0xc0, //0x000021ac movq         $-64(%rbp), %r9
	0x48, 0x85, 0xd2, //0x000021b0 testq        %rdx, %rdx
	0x0f, 0x85, 0x9d, 0xff, 0xff, 0xff, //0x000021b3 jne          LBB0_384
	0xe9, 0xfd, 0x01, 0x00, 0x00, //0x000021b9 jmp          LBB0_425
	//0x000021be LBB0_389
	0x48, 0x2b, 0x4d, 0xd0, //0x000021be subq         $-48(%rbp), %rcx
	0x49, 0x89, 0xcb, //0x000021c2 movq         %rcx, %r11
	0xe9, 0x86, 0xed, 0xff, 0xff, //0x000021c5 jmp          LBB0_195
	//0x000021ca LBB0_390
	0x4c, 0x89, 0xd9, //0x000021ca movq         %r11, %rcx
	0x48, 0x2b, 0x4d, 0xd0, //0x000021cd subq         $-48(%rbp), %rcx
	0x4c, 0x0f, 0xbc, 0xe2, //0x000021d1 bsfq         %rdx, %r12
	0x49, 0x01, 0xcc, //0x000021d5 addq         %rcx, %r12
	0xe9, 0xeb, 0xf7, 0xff, 0xff, //0x000021d8 jmp          LBB0_173
	//0x000021dd LBB0_391
	0x4d, 0x85, 0xed, //0x000021dd testq        %r13, %r13
	0x0f, 0x84, 0xe3, 0x03, 0x00, 0x00, //0x000021e0 je           LBB0_462
	0x48, 0x8b, 0x45, 0xa0, //0x000021e6 movq         $-96(%rbp), %rax
	0x4c, 0x01, 0xd8, //0x000021ea addq         %r11, %rax
	0x49, 0x83, 0xfc, 0xff, //0x000021ed cmpq         $-1, %r12
	0x4c, 0x0f, 0x44, 0xe0, //0x000021f1 cmoveq       %rax, %r12
	0x49, 0xff, 0xc3, //0x000021f5 incq         %r11
	0x49, 0xff, 0xcd, //0x000021f8 decq         %r13
	0x48, 0x8b, 0x75, 0xc8, //0x000021fb movq         $-56(%rbp), %rsi
	0x4c, 0x8b, 0x4d, 0xc0, //0x000021ff movq         $-64(%rbp), %r9
	0x4d, 0x85, 0xed, //0x00002203 testq        %r13, %r13
	0x0f, 0x85, 0x4a, 0xff, 0xff, 0xff, //0x00002206 jne          LBB0_384
	0xe9, 0xaa, 0x01, 0x00, 0x00, //0x0000220c jmp          LBB0_425
	//0x00002211 LBB0_393
	0x48, 0x85, 0xc0, //0x00002211 testq        %rax, %rax
	0x0f, 0x84, 0xaf, 0x03, 0x00, 0x00, //0x00002214 je           LBB0_462
	0x48, 0x8b, 0x4d, 0xa0, //0x0000221a movq         $-96(%rbp), %rcx
	0x4c, 0x01, 0xd9, //0x0000221e addq         %r11, %rcx
	0x49, 0x83, 0xfc, 0xff, //0x00002221 cmpq         $-1, %r12
	0x4c, 0x0f, 0x44, 0xe1, //0x00002225 cmoveq       %rcx, %r12
	0x49, 0xff, 0xc3, //0x00002229 incq         %r11
	0x48, 0xff, 0xc8, //0x0000222c decq         %rax
	0x48, 0x8b, 0x75, 0xc8, //0x0000222f movq         $-56(%rbp), %rsi
	0x4c, 0x8b, 0x4d, 0xc0, //0x00002233 movq         $-64(%rbp), %r9
	0x48, 0x85, 0xc0, //0x00002237 testq        %rax, %rax
	0x0f, 0x85, 0xb4, 0xf8, 0xff, 0xff, //0x0000223a jne          LBB0_331
	0xe9, 0x76, 0x01, 0x00, 0x00, //0x00002240 jmp          LBB0_425
	//0x00002245 LBB0_395
	0x44, 0x89, 0xc1, //0x00002245 movl         %r8d, %ecx
	0xf7, 0xd1, //0x00002248 notl         %ecx
	0x21, 0xd1, //0x0000224a andl         %edx, %ecx
	0x44, 0x8d, 0x14, 0x09, //0x0000224c leal         (%rcx,%rcx), %r10d
	0x45, 0x09, 0xc2, //0x00002250 orl          %r8d, %r10d
	0xbb, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002253 movl         $2863311530, %ebx
	0x44, 0x31, 0xd3, //0x00002258 xorl         %r10d, %ebx
	0x21, 0xd3, //0x0000225b andl         %edx, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000225d andl         $-1431655766, %ebx
	0x45, 0x31, 0xc0, //0x00002263 xorl         %r8d, %r8d
	0x01, 0xcb, //0x00002266 addl         %ecx, %ebx
	0x41, 0x0f, 0x92, 0xc0, //0x00002268 setb         %r8b
	0x01, 0xdb, //0x0000226c addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x0000226e xorl         $1431655765, %ebx
	0x44, 0x21, 0xd3, //0x00002274 andl         %r10d, %ebx
	0xf7, 0xd3, //0x00002277 notl         %ebx
	0x21, 0xde, //0x00002279 andl         %ebx, %esi
	0xe9, 0xae, 0xfa, 0xff, 0xff, //0x0000227b jmp          LBB0_281
	//0x00002280 LBB0_396
	0x4c, 0x89, 0xd8, //0x00002280 movq         %r11, %rax
	0x48, 0x2b, 0x45, 0xd0, //0x00002283 subq         $-48(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xcf, //0x00002287 bsfq         %rdi, %r9
	0x49, 0x01, 0xc1, //0x0000228b addq         %rax, %r9
	0x48, 0x09, 0xca, //0x0000228e orq          %rcx, %rdx
	0x48, 0x89, 0xf8, //0x00002291 movq         %rdi, %rax
	0x4c, 0x09, 0xd0, //0x00002294 orq          %r10, %rax
	0x0f, 0x84, 0x7d, 0xf9, 0xff, 0xff, //0x00002297 je           LBB0_212
	//0x0000229d LBB0_397
	0x44, 0x89, 0xd0, //0x0000229d movl         %r10d, %eax
	0xf7, 0xd0, //0x000022a0 notl         %eax
	0x21, 0xf8, //0x000022a2 andl         %edi, %eax
	0x8d, 0x0c, 0x00, //0x000022a4 leal         (%rax,%rax), %ecx
	0x44, 0x09, 0xd1, //0x000022a7 orl          %r10d, %ecx
	0x89, 0xce, //0x000022aa movl         %ecx, %esi
	0xbb, 0xaa, 0xaa, 0xaa, 0xaa, //0x000022ac movl         $2863311530, %ebx
	0x31, 0xde, //0x000022b1 xorl         %ebx, %esi
	0x21, 0xfe, //0x000022b3 andl         %edi, %esi
	0x81, 0xe6, 0xaa, 0xaa, 0xaa, 0xaa, //0x000022b5 andl         $-1431655766, %esi
	0x45, 0x31, 0xd2, //0x000022bb xorl         %r10d, %r10d
	0x01, 0xc6, //0x000022be addl         %eax, %esi
	0x41, 0x0f, 0x92, 0xc2, //0x000022c0 setb         %r10b
	0x01, 0xf6, //0x000022c4 addl         %esi, %esi
	0x81, 0xf6, 0x55, 0x55, 0x55, 0x55, //0x000022c6 xorl         $1431655765, %esi
	0x21, 0xce, //0x000022cc andl         %ecx, %esi
	0xf7, 0xd6, //0x000022ce notl         %esi
	0x21, 0xf2, //0x000022d0 andl         %esi, %edx
	0x48, 0x85, 0xd2, //0x000022d2 testq        %rdx, %rdx
	0x0f, 0x85, 0xd4, 0xf4, 0xff, 0xff, //0x000022d5 jne          LBB0_54
	0xe9, 0x43, 0xf9, 0xff, 0xff, //0x000022db jmp          LBB0_398
	//0x000022e0 LBB0_408
	0x4c, 0x89, 0xdb, //0x000022e0 movq         %r11, %rbx
	0x4c, 0x29, 0xe3, //0x000022e3 subq         %r12, %rbx
	0x4c, 0x0f, 0xbc, 0xca, //0x000022e6 bsfq         %rdx, %r9
	0x49, 0x01, 0xd9, //0x000022ea addq         %rbx, %r9
	0xe9, 0x29, 0xfa, 0xff, 0xff, //0x000022ed jmp          LBB0_280
	//0x000022f2 LBB0_411
	0x4d, 0x85, 0xe4, //0x000022f2 testq        %r12, %r12
	0x0f, 0x84, 0x78, 0x00, 0x00, 0x00, //0x000022f5 je           LBB0_416
	0x48, 0x8b, 0x45, 0xa0, //0x000022fb movq         $-96(%rbp), %rax
	0x4c, 0x01, 0xd8, //0x000022ff addq         %r11, %rax
	0x49, 0x83, 0xf9, 0xff, //0x00002302 cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc8, //0x00002306 cmoveq       %rax, %r9
	0x49, 0xff, 0xc3, //0x0000230a incq         %r11
	0x49, 0xff, 0xcc, //0x0000230d decq         %r12
	0x4d, 0x85, 0xe4, //0x00002310 testq        %r12, %r12
	0x0f, 0x85, 0x24, 0xf9, 0xff, 0xff, //0x00002313 jne          LBB0_401
	0xe9, 0x55, 0x00, 0x00, 0x00, //0x00002319 jmp          LBB0_416
	//0x0000231e LBB0_409
	0x48, 0x85, 0xc0, //0x0000231e testq        %rax, %rax
	0x0f, 0x84, 0x4c, 0x00, 0x00, 0x00, //0x00002321 je           LBB0_416
	0x48, 0x8b, 0x4d, 0xa0, //0x00002327 movq         $-96(%rbp), %rcx
	0x4c, 0x01, 0xd9, //0x0000232b addq         %r11, %rcx
	0x49, 0x83, 0xf9, 0xff, //0x0000232e cmpq         $-1, %r9
	0x4c, 0x0f, 0x44, 0xc9, //0x00002332 cmoveq       %rcx, %r9
	0x49, 0xff, 0xc3, //0x00002336 incq         %r11
	0x48, 0xff, 0xc8, //0x00002339 decq         %rax
	0x4c, 0x8b, 0x65, 0xd0, //0x0000233c movq         $-48(%rbp), %r12
	0x48, 0x85, 0xc0, //0x00002340 testq        %rax, %rax
	0x0f, 0x85, 0x38, 0xfa, 0xff, 0xff, //0x00002343 jne          LBB0_347
	0xe9, 0x25, 0x00, 0x00, 0x00, //0x00002349 jmp          LBB0_416
	//0x0000234e LBB0_413
	0x48, 0x89, 0x16, //0x0000234e movq         %rdx, (%rsi)
	//0x00002351 LBB0_414
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002351 movq         $-1, %rax
	0xe9, 0x45, 0x00, 0x00, 0x00, //0x00002358 jmp          LBB0_423
	//0x0000235d LBB0_441
	0x48, 0xc7, 0xc0, 0xf9, 0xff, 0xff, 0xff, //0x0000235d movq         $-7, %rax
	0xe9, 0x39, 0x00, 0x00, 0x00, //0x00002364 jmp          LBB0_423
	//0x00002369 LBB0_415
	0x49, 0x83, 0xfb, 0xff, //0x00002369 cmpq         $-1, %r11
	0x0f, 0x85, 0x7f, 0x00, 0x00, 0x00, //0x0000236d jne          LBB0_307
	//0x00002373 LBB0_416
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00002373 movq         $-1, %r11
	0x4c, 0x8b, 0x4d, 0xb0, //0x0000237a movq         $-80(%rbp), %r9
	0xe9, 0x6f, 0x00, 0x00, 0x00, //0x0000237e jmp          LBB0_307
	//0x00002383 LBB0_417
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002383 movq         $-1, %rax
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x0000238a jmp          LBB0_420
	//0x0000238f LBB0_419
	0x4c, 0x89, 0xd8, //0x0000238f movq         %r11, %rax
	//0x00002392 LBB0_420
	0x48, 0xf7, 0xd0, //0x00002392 notq         %rax
	0x49, 0x01, 0xc7, //0x00002395 addq         %rax, %r15
	//0x00002398 LBB0_421
	0x4c, 0x89, 0x3e, //0x00002398 movq         %r15, (%rsi)
	//0x0000239b LBB0_422
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x0000239b movq         $-2, %rax
	//0x000023a2 LBB0_423
	0x48, 0x83, 0xc4, 0x68, //0x000023a2 addq         $104, %rsp
	0x5b, //0x000023a6 popq         %rbx
	0x41, 0x5c, //0x000023a7 popq         %r12
	0x41, 0x5d, //0x000023a9 popq         %r13
	0x41, 0x5e, //0x000023ab popq         %r14
	0x41, 0x5f, //0x000023ad popq         %r15
	0x5d, //0x000023af popq         %rbp
	0xc3, //0x000023b0 retq         
	//0x000023b1 LBB0_424
	0x49, 0x83, 0xfb, 0xff, //0x000023b1 cmpq         $-1, %r11
	0x0f, 0x85, 0x69, 0x00, 0x00, 0x00, //0x000023b5 jne          LBB0_437
	//0x000023bb LBB0_425
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000023bb movq         $-1, %r11
	0x4c, 0x8b, 0x65, 0xb0, //0x000023c2 movq         $-80(%rbp), %r12
	0xe9, 0x59, 0x00, 0x00, 0x00, //0x000023c6 jmp          LBB0_437
	//0x000023cb LBB0_440
	0x48, 0x89, 0x0e, //0x000023cb movq         %rcx, (%rsi)
	0xe9, 0xcf, 0xff, 0xff, 0xff, //0x000023ce jmp          LBB0_423
	//0x000023d3 LBB0_426
	0x49, 0x83, 0xf9, 0xff, //0x000023d3 cmpq         $-1, %r9
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x000023d7 jne          LBB0_431
	0x49, 0x0f, 0xbc, 0xc4, //0x000023dd bsfq         %r12, %rax
	//0x000023e1 LBB0_428
	0x4c, 0x2b, 0x5d, 0xd0, //0x000023e1 subq         $-48(%rbp), %r11
	//0x000023e5 LBB0_429
	0x49, 0x01, 0xc3, //0x000023e5 addq         %rax, %r11
	//0x000023e8 LBB0_430
	0x4d, 0x89, 0xd9, //0x000023e8 movq         %r11, %r9
	//0x000023eb LBB0_431
	0x49, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x000023eb movq         $-2, %r11
	//0x000023f2 LBB0_307
	0x48, 0x8b, 0x45, 0xc8, //0x000023f2 movq         $-56(%rbp), %rax
	0x4c, 0x89, 0x08, //0x000023f6 movq         %r9, (%rax)
	0x4c, 0x89, 0xd8, //0x000023f9 movq         %r11, %rax
	0xe9, 0xa1, 0xff, 0xff, 0xff, //0x000023fc jmp          LBB0_423
	//0x00002401 LBB0_432
	0x49, 0x83, 0xfc, 0xff, //0x00002401 cmpq         $-1, %r12
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x00002405 jne          LBB0_435
	0x48, 0x0f, 0xbc, 0xc2, //0x0000240b bsfq         %rdx, %rax
	//0x0000240f LBB0_434
	0x4c, 0x2b, 0x5d, 0xd0, //0x0000240f subq         $-48(%rbp), %r11
	0x49, 0x01, 0xc3, //0x00002413 addq         %rax, %r11
	0x4d, 0x89, 0xdc, //0x00002416 movq         %r11, %r12
	//0x00002419 LBB0_435
	0x49, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x00002419 movq         $-2, %r11
	//0x00002420 LBB0_436
	0x48, 0x8b, 0x75, 0xc8, //0x00002420 movq         $-56(%rbp), %rsi
	//0x00002424 LBB0_437
	0x4c, 0x89, 0x26, //0x00002424 movq         %r12, (%rsi)
	0x4c, 0x89, 0xd8, //0x00002427 movq         %r11, %rax
	0xe9, 0x73, 0xff, 0xff, 0xff, //0x0000242a jmp          LBB0_423
	//0x0000242f LBB0_438
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x0000242f movq         $-1, %r11
	//0x00002436 LBB0_439
	0x4d, 0x29, 0xdf, //0x00002436 subq         %r11, %r15
	0xe9, 0x5a, 0xff, 0xff, 0xff, //0x00002439 jmp          LBB0_421
	//0x0000243e LBB0_442
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x0000243e movq         $-2, %rax
	0x80, 0xf9, 0x61, //0x00002445 cmpb         $97, %cl
	0x0f, 0x85, 0x54, 0xff, 0xff, 0xff, //0x00002448 jne          LBB0_423
	0x49, 0x8d, 0x4f, 0x02, //0x0000244e leaq         $2(%r15), %rcx
	0x48, 0x89, 0x0e, //0x00002452 movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x02, 0x6c, //0x00002455 cmpb         $108, $2(%r12,%r15)
	0x0f, 0x85, 0x41, 0xff, 0xff, 0xff, //0x0000245b jne          LBB0_423
	0x49, 0x8d, 0x4f, 0x03, //0x00002461 leaq         $3(%r15), %rcx
	0x48, 0x89, 0x0e, //0x00002465 movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x03, 0x73, //0x00002468 cmpb         $115, $3(%r12,%r15)
	0x0f, 0x85, 0x2e, 0xff, 0xff, 0xff, //0x0000246e jne          LBB0_423
	0x49, 0x8d, 0x4f, 0x04, //0x00002474 leaq         $4(%r15), %rcx
	0x48, 0x89, 0x0e, //0x00002478 movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x04, 0x65, //0x0000247b cmpb         $101, $4(%r12,%r15)
	0x0f, 0x85, 0x1b, 0xff, 0xff, 0xff, //0x00002481 jne          LBB0_423
	0x49, 0x83, 0xc7, 0x05, //0x00002487 addq         $5, %r15
	0x4c, 0x89, 0x3e, //0x0000248b movq         %r15, (%rsi)
	0xe9, 0x0f, 0xff, 0xff, 0xff, //0x0000248e jmp          LBB0_423
	//0x00002493 LBB0_247
	0x4c, 0x89, 0x3e, //0x00002493 movq         %r15, (%rsi)
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00002496 movq         $-2, %rax
	0x41, 0x80, 0x3a, 0x6e, //0x0000249d cmpb         $110, (%r10)
	0x0f, 0x85, 0xfb, 0xfe, 0xff, 0xff, //0x000024a1 jne          LBB0_423
	0x49, 0x8d, 0x4f, 0x01, //0x000024a7 leaq         $1(%r15), %rcx
	0x48, 0x89, 0x0e, //0x000024ab movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x01, 0x75, //0x000024ae cmpb         $117, $1(%r12,%r15)
	0x0f, 0x85, 0xe8, 0xfe, 0xff, 0xff, //0x000024b4 jne          LBB0_423
	0x49, 0x8d, 0x4f, 0x02, //0x000024ba leaq         $2(%r15), %rcx
	0x48, 0x89, 0x0e, //0x000024be movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x02, 0x6c, //0x000024c1 cmpb         $108, $2(%r12,%r15)
	0x0f, 0x85, 0xd5, 0xfe, 0xff, 0xff, //0x000024c7 jne          LBB0_423
	0x49, 0x8d, 0x4f, 0x03, //0x000024cd leaq         $3(%r15), %rcx
	0x48, 0x89, 0x0e, //0x000024d1 movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x03, 0x6c, //0x000024d4 cmpb         $108, $3(%r12,%r15)
	0x0f, 0x85, 0xc2, 0xfe, 0xff, 0xff, //0x000024da jne          LBB0_423
	0xe9, 0x4d, 0x00, 0x00, 0x00, //0x000024e0 jmp          LBB0_451
	//0x000024e5 LBB0_447
	0x4c, 0x89, 0x3e, //0x000024e5 movq         %r15, (%rsi)
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x000024e8 movq         $-2, %rax
	0x41, 0x80, 0x3a, 0x74, //0x000024ef cmpb         $116, (%r10)
	0x0f, 0x85, 0xa9, 0xfe, 0xff, 0xff, //0x000024f3 jne          LBB0_423
	0x49, 0x8d, 0x4f, 0x01, //0x000024f9 leaq         $1(%r15), %rcx
	0x48, 0x89, 0x0e, //0x000024fd movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x01, 0x72, //0x00002500 cmpb         $114, $1(%r12,%r15)
	0x0f, 0x85, 0x96, 0xfe, 0xff, 0xff, //0x00002506 jne          LBB0_423
	0x49, 0x8d, 0x4f, 0x02, //0x0000250c leaq         $2(%r15), %rcx
	0x48, 0x89, 0x0e, //0x00002510 movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x02, 0x75, //0x00002513 cmpb         $117, $2(%r12,%r15)
	0x0f, 0x85, 0x83, 0xfe, 0xff, 0xff, //0x00002519 jne          LBB0_423
	0x49, 0x8d, 0x4f, 0x03, //0x0000251f leaq         $3(%r15), %rcx
	0x48, 0x89, 0x0e, //0x00002523 movq         %rcx, (%rsi)
	0x43, 0x80, 0x7c, 0x3c, 0x03, 0x65, //0x00002526 cmpb         $101, $3(%r12,%r15)
	0x0f, 0x85, 0x70, 0xfe, 0xff, 0xff, //0x0000252c jne          LBB0_423
	//0x00002532 LBB0_451
	0x49, 0x83, 0xc7, 0x04, //0x00002532 addq         $4, %r15
	0x4c, 0x89, 0x3e, //0x00002536 movq         %r15, (%rsi)
	0xe9, 0x64, 0xfe, 0xff, 0xff, //0x00002539 jmp          LBB0_423
	//0x0000253e LBB0_452
	0x49, 0x83, 0xf9, 0xff, //0x0000253e cmpq         $-1, %r9
	0x0f, 0x85, 0xa3, 0xfe, 0xff, 0xff, //0x00002542 jne          LBB0_431
	0x48, 0x0f, 0xbc, 0xc2, //0x00002548 bsfq         %rdx, %rax
	0xe9, 0x90, 0xfe, 0xff, 0xff, //0x0000254c jmp          LBB0_428
	//0x00002551 LBB0_456
	0x4c, 0x89, 0x5d, 0xb0, //0x00002551 movq         %r11, $-80(%rbp)
	0xe9, 0x19, 0xfe, 0xff, 0xff, //0x00002555 jmp          LBB0_416
	//0x0000255a LBB0_306
	0x4c, 0x01, 0xd9, //0x0000255a addq         %r11, %rcx
	0x49, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x0000255d movq         $-2, %r11
	0x49, 0x89, 0xc9, //0x00002564 movq         %rcx, %r9
	0xe9, 0x86, 0xfe, 0xff, 0xff, //0x00002567 jmp          LBB0_307
	//0x0000256c LBB0_454
	0x4c, 0x89, 0x5d, 0xb0, //0x0000256c movq         %r11, $-80(%rbp)
	0xe9, 0x46, 0xfe, 0xff, 0xff, //0x00002570 jmp          LBB0_425
	//0x00002575 LBB0_455
	0x4c, 0x01, 0xd9, //0x00002575 addq         %r11, %rcx
	0x49, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x00002578 movq         $-2, %r11
	0x49, 0x89, 0xcc, //0x0000257f movq         %rcx, %r12
	0xe9, 0x9d, 0xfe, 0xff, 0xff, //0x00002582 jmp          LBB0_437
	//0x00002587 LBB0_457
	0x48, 0x0f, 0xbc, 0xc7, //0x00002587 bsfq         %rdi, %rax
	0x4d, 0x29, 0xe3, //0x0000258b subq         %r12, %r11
	0xe9, 0x52, 0xfe, 0xff, 0xff, //0x0000258e jmp          LBB0_429
	//0x00002593 LBB0_461
	0x4d, 0x29, 0xe3, //0x00002593 subq         %r12, %r11
	0xe9, 0x4d, 0xfe, 0xff, 0xff, //0x00002596 jmp          LBB0_430
	//0x0000259b LBB0_458
	0x4c, 0x01, 0xd9, //0x0000259b addq         %r11, %rcx
	0x49, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x0000259e movq         $-2, %r11
	0x49, 0x89, 0xcc, //0x000025a5 movq         %rcx, %r12
	0xe9, 0x73, 0xfe, 0xff, 0xff, //0x000025a8 jmp          LBB0_436
	//0x000025ad LBB0_459
	0x48, 0x0f, 0xbc, 0xc7, //0x000025ad bsfq         %rdi, %rax
	0xe9, 0x59, 0xfe, 0xff, 0xff, //0x000025b1 jmp          LBB0_434
	//0x000025b6 LBB0_460
	0x4c, 0x2b, 0x5d, 0xd0, //0x000025b6 subq         $-48(%rbp), %r11
	0x4d, 0x89, 0xdc, //0x000025ba movq         %r11, %r12
	0x49, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x000025bd movq         $-2, %r11
	0xe9, 0x5b, 0xfe, 0xff, 0xff, //0x000025c4 jmp          LBB0_437
	//0x000025c9 LBB0_462
	0x48, 0x8b, 0x75, 0xc8, //0x000025c9 movq         $-56(%rbp), %rsi
	0xe9, 0xe9, 0xfd, 0xff, 0xff, //0x000025cd jmp          LBB0_425
	0x90, 0x90, //0x000025d2 .p2align 2, 0x90
	// // .set L0_0_set_33, LBB0_33-LJTI0_0
	// // .set L0_0_set_37, LBB0_37-LJTI0_0
	// // .set L0_0_set_39, LBB0_39-LJTI0_0
	// // .set L0_0_set_59, LBB0_59-LJTI0_0
	// // .set L0_0_set_61, LBB0_61-LJTI0_0
	// // .set L0_0_set_64, LBB0_64-LJTI0_0
	//0x000025d4 LJTI0_0
	0xc3, 0xdd, 0xff, 0xff, //0x000025d4 .long L0_0_set_33
	0xeb, 0xdd, 0xff, 0xff, //0x000025d8 .long L0_0_set_37
	0x16, 0xde, 0xff, 0xff, //0x000025dc .long L0_0_set_39
	0xcf, 0xdf, 0xff, 0xff, //0x000025e0 .long L0_0_set_59
	0xe5, 0xdf, 0xff, 0xff, //0x000025e4 .long L0_0_set_61
	0x55, 0xe2, 0xff, 0xff, //0x000025e8 .long L0_0_set_64
	// // .set L0_1_set_423, LBB0_423-LJTI0_1
	// // .set L0_1_set_422, LBB0_422-LJTI0_1
	// // .set L0_1_set_199, LBB0_199-LJTI0_1
	// // .set L0_1_set_215, LBB0_215-LJTI0_1
	// // .set L0_1_set_66, LBB0_66-LJTI0_1
	// // .set L0_1_set_240, LBB0_240-LJTI0_1
	// // .set L0_1_set_242, LBB0_242-LJTI0_1
	// // .set L0_1_set_245, LBB0_245-LJTI0_1
	// // .set L0_1_set_251, LBB0_251-LJTI0_1
	// // .set L0_1_set_255, LBB0_255-LJTI0_1
	//0x000025ec LJTI0_1
	0xb6, 0xfd, 0xff, 0xff, //0x000025ec .long L0_1_set_423
	0xaf, 0xfd, 0xff, 0xff, //0x000025f0 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000025f4 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000025f8 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000025fc .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002600 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002604 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002608 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000260c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002610 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002614 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002618 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000261c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002620 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002624 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002628 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000262c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002630 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002634 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002638 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000263c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002640 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002644 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002648 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000264c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002650 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002654 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002658 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000265c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002660 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002664 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002668 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000266c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002670 .long L0_1_set_422
	0xa6, 0xe9, 0xff, 0xff, //0x00002674 .long L0_1_set_199
	0xaf, 0xfd, 0xff, 0xff, //0x00002678 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000267c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002680 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002684 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002688 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000268c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002690 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002694 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002698 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000269c .long L0_1_set_422
	0x38, 0xeb, 0xff, 0xff, //0x000026a0 .long L0_1_set_215
	0xaf, 0xfd, 0xff, 0xff, //0x000026a4 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000026a8 .long L0_1_set_422
	0xfd, 0xdf, 0xff, 0xff, //0x000026ac .long L0_1_set_66
	0xfd, 0xdf, 0xff, 0xff, //0x000026b0 .long L0_1_set_66
	0xfd, 0xdf, 0xff, 0xff, //0x000026b4 .long L0_1_set_66
	0xfd, 0xdf, 0xff, 0xff, //0x000026b8 .long L0_1_set_66
	0xfd, 0xdf, 0xff, 0xff, //0x000026bc .long L0_1_set_66
	0xfd, 0xdf, 0xff, 0xff, //0x000026c0 .long L0_1_set_66
	0xfd, 0xdf, 0xff, 0xff, //0x000026c4 .long L0_1_set_66
	0xfd, 0xdf, 0xff, 0xff, //0x000026c8 .long L0_1_set_66
	0xfd, 0xdf, 0xff, 0xff, //0x000026cc .long L0_1_set_66
	0xfd, 0xdf, 0xff, 0xff, //0x000026d0 .long L0_1_set_66
	0xaf, 0xfd, 0xff, 0xff, //0x000026d4 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000026d8 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000026dc .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000026e0 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000026e4 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000026e8 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000026ec .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000026f0 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000026f4 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000026f8 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000026fc .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002700 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002704 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002708 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000270c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002710 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002714 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002718 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000271c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002720 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002724 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002728 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000272c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002730 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002734 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002738 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000273c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002740 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002744 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002748 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000274c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002750 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002754 .long L0_1_set_422
	0x0f, 0xed, 0xff, 0xff, //0x00002758 .long L0_1_set_240
	0xaf, 0xfd, 0xff, 0xff, //0x0000275c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002760 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002764 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002768 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000276c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002770 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002774 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002778 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000277c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002780 .long L0_1_set_422
	0x33, 0xed, 0xff, 0xff, //0x00002784 .long L0_1_set_242
	0xaf, 0xfd, 0xff, 0xff, //0x00002788 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000278c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002790 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002794 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x00002798 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x0000279c .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000027a0 .long L0_1_set_422
	0x64, 0xed, 0xff, 0xff, //0x000027a4 .long L0_1_set_245
	0xaf, 0xfd, 0xff, 0xff, //0x000027a8 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000027ac .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000027b0 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000027b4 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000027b8 .long L0_1_set_422
	0x8b, 0xed, 0xff, 0xff, //0x000027bc .long L0_1_set_251
	0xaf, 0xfd, 0xff, 0xff, //0x000027c0 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000027c4 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000027c8 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000027cc .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000027d0 .long L0_1_set_422
	0xaf, 0xfd, 0xff, 0xff, //0x000027d4 .long L0_1_set_422
	0xc8, 0xed, 0xff, 0xff, //0x000027d8 .long L0_1_set_255
	// // .set L0_2_set_264, LBB0_264-LJTI0_2
	// // .set L0_2_set_292, LBB0_292-LJTI0_2
	// // .set L0_2_set_259, LBB0_259-LJTI0_2
	// // .set L0_2_set_261, LBB0_261-LJTI0_2
	// // .set L0_2_set_266, LBB0_266-LJTI0_2
	//0x000027dc LJTI0_2
	0x3d, 0xec, 0xff, 0xff, //0x000027dc .long L0_2_set_264
	0x7b, 0xee, 0xff, 0xff, //0x000027e0 .long L0_2_set_292
	0x3d, 0xec, 0xff, 0xff, //0x000027e4 .long L0_2_set_264
	0xfc, 0xeb, 0xff, 0xff, //0x000027e8 .long L0_2_set_259
	0x7b, 0xee, 0xff, 0xff, //0x000027ec .long L0_2_set_292
	0x14, 0xec, 0xff, 0xff, //0x000027f0 .long L0_2_set_261
	0x14, 0xec, 0xff, 0xff, //0x000027f4 .long L0_2_set_261
	0x14, 0xec, 0xff, 0xff, //0x000027f8 .long L0_2_set_261
	0x14, 0xec, 0xff, 0xff, //0x000027fc .long L0_2_set_261
	0x14, 0xec, 0xff, 0xff, //0x00002800 .long L0_2_set_261
	0x14, 0xec, 0xff, 0xff, //0x00002804 .long L0_2_set_261
	0x14, 0xec, 0xff, 0xff, //0x00002808 .long L0_2_set_261
	0x14, 0xec, 0xff, 0xff, //0x0000280c .long L0_2_set_261
	0x14, 0xec, 0xff, 0xff, //0x00002810 .long L0_2_set_261
	0x14, 0xec, 0xff, 0xff, //0x00002814 .long L0_2_set_261
	0x7b, 0xee, 0xff, 0xff, //0x00002818 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x0000281c .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002820 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002824 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002828 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x0000282c .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002830 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002834 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002838 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x0000283c .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002840 .long L0_2_set_292
	0x58, 0xec, 0xff, 0xff, //0x00002844 .long L0_2_set_266
	0x7b, 0xee, 0xff, 0xff, //0x00002848 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x0000284c .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002850 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002854 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002858 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x0000285c .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002860 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002864 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002868 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x0000286c .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002870 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002874 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002878 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x0000287c .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002880 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002884 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002888 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x0000288c .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002890 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002894 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x00002898 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x0000289c .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x000028a0 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x000028a4 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x000028a8 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x000028ac .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x000028b0 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x000028b4 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x000028b8 .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x000028bc .long L0_2_set_292
	0x7b, 0xee, 0xff, 0xff, //0x000028c0 .long L0_2_set_292
	0x58, 0xec, 0xff, 0xff, //0x000028c4 .long L0_2_set_266
	// // .set L0_3_set_94, LBB0_94-LJTI0_3
	// // .set L0_3_set_149, LBB0_149-LJTI0_3
	// // .set L0_3_set_98, LBB0_98-LJTI0_3
	// // .set L0_3_set_91, LBB0_91-LJTI0_3
	// // .set L0_3_set_96, LBB0_96-LJTI0_3
	//0x000028c8 LJTI0_3
	0x10, 0xdf, 0xff, 0xff, //0x000028c8 .long L0_3_set_94
	0x75, 0xe3, 0xff, 0xff, //0x000028cc .long L0_3_set_149
	0x10, 0xdf, 0xff, 0xff, //0x000028d0 .long L0_3_set_94
	0x46, 0xdf, 0xff, 0xff, //0x000028d4 .long L0_3_set_98
	0x75, 0xe3, 0xff, 0xff, //0x000028d8 .long L0_3_set_149
	0xe8, 0xde, 0xff, 0xff, //0x000028dc .long L0_3_set_91
	0xe8, 0xde, 0xff, 0xff, //0x000028e0 .long L0_3_set_91
	0xe8, 0xde, 0xff, 0xff, //0x000028e4 .long L0_3_set_91
	0xe8, 0xde, 0xff, 0xff, //0x000028e8 .long L0_3_set_91
	0xe8, 0xde, 0xff, 0xff, //0x000028ec .long L0_3_set_91
	0xe8, 0xde, 0xff, 0xff, //0x000028f0 .long L0_3_set_91
	0xe8, 0xde, 0xff, 0xff, //0x000028f4 .long L0_3_set_91
	0xe8, 0xde, 0xff, 0xff, //0x000028f8 .long L0_3_set_91
	0xe8, 0xde, 0xff, 0xff, //0x000028fc .long L0_3_set_91
	0xe8, 0xde, 0xff, 0xff, //0x00002900 .long L0_3_set_91
	0x75, 0xe3, 0xff, 0xff, //0x00002904 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002908 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x0000290c .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002910 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002914 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002918 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x0000291c .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002920 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002924 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002928 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x0000292c .long L0_3_set_149
	0x2b, 0xdf, 0xff, 0xff, //0x00002930 .long L0_3_set_96
	0x75, 0xe3, 0xff, 0xff, //0x00002934 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002938 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x0000293c .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002940 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002944 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002948 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x0000294c .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002950 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002954 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002958 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x0000295c .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002960 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002964 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002968 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x0000296c .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002970 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002974 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002978 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x0000297c .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002980 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002984 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002988 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x0000298c .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002990 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002994 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x00002998 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x0000299c .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x000029a0 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x000029a4 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x000029a8 .long L0_3_set_149
	0x75, 0xe3, 0xff, 0xff, //0x000029ac .long L0_3_set_149
	0x2b, 0xdf, 0xff, 0xff, //0x000029b0 .long L0_3_set_96
	//0x000029b4 .p2align 2, 0x00
	//0x000029b4 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x000029b4 .long 2
}
 
