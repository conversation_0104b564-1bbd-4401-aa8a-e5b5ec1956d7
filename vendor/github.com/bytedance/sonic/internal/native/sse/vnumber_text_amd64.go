// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package sse

var _text_vnumber = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x00, 0x00, 0x30, 0x43, // .long 1127219200
	0x00, 0x00, 0x30, 0x45, //0x00000004 .long 1160773632
	0x00, 0x00, 0x00, 0x00, //0x00000008 .long 0
	0x00, 0x00, 0x00, 0x00, //0x0000000c .long 0
	//0x00000010 LCPI0_1
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x43, //0x00000010 .quad 4841369599423283200
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x45, //0x00000018 .quad 4985484787499139072
	//0x00000020 .p2align 3, 0x00
	//0x00000020 LCPI0_2
	0x00, 0x00, 0x34, 0x26, 0xf5, 0x6b, 0x0c, 0x43, //0x00000020 .quad 4831355200913801216
	//0x00000028 LCPI0_3
	0x00, 0x00, 0x34, 0x26, 0xf5, 0x6b, 0x0c, 0xc3, //0x00000028 .quad -4392016835940974592
	//0x00000030 .p2align 4, 0x90
	//0x00000030 _vnumber
	0x55, //0x00000030 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000031 movq         %rsp, %rbp
	0x41, 0x57, //0x00000034 pushq        %r15
	0x41, 0x56, //0x00000036 pushq        %r14
	0x41, 0x55, //0x00000038 pushq        %r13
	0x41, 0x54, //0x0000003a pushq        %r12
	0x53, //0x0000003c pushq        %rbx
	0x48, 0x83, 0xec, 0x40, //0x0000003d subq         $64, %rsp
	0x49, 0x89, 0xd6, //0x00000041 movq         %rdx, %r14
	0x4c, 0x8b, 0x07, //0x00000044 movq         (%rdi), %r8
	0x4c, 0x8b, 0x5f, 0x08, //0x00000047 movq         $8(%rdi), %r11
	0x48, 0x8b, 0x06, //0x0000004b movq         (%rsi), %rax
	0x4c, 0x8b, 0x62, 0x20, //0x0000004e movq         $32(%rdx), %r12
	0x4c, 0x8b, 0x6a, 0x28, //0x00000052 movq         $40(%rdx), %r13
	0x48, 0xc7, 0x02, 0x09, 0x00, 0x00, 0x00, //0x00000056 movq         $9, (%rdx)
	0x48, 0xc7, 0x42, 0x08, 0x00, 0x00, 0x00, 0x00, //0x0000005d movq         $0, $8(%rdx)
	0x48, 0xc7, 0x42, 0x10, 0x00, 0x00, 0x00, 0x00, //0x00000065 movq         $0, $16(%rdx)
	0x48, 0x8b, 0x0e, //0x0000006d movq         (%rsi), %rcx
	0x48, 0x89, 0x4a, 0x18, //0x00000070 movq         %rcx, $24(%rdx)
	0x4c, 0x39, 0xd8, //0x00000074 cmpq         %r11, %rax
	0x0f, 0x83, 0xa8, 0x02, 0x00, 0x00, //0x00000077 jae          LBB0_50
	0x41, 0x8a, 0x14, 0x00, //0x0000007d movb         (%r8,%rax), %dl
	0x41, 0xb9, 0x01, 0x00, 0x00, 0x00, //0x00000081 movl         $1, %r9d
	0x80, 0xfa, 0x2d, //0x00000087 cmpb         $45, %dl
	0x0f, 0x85, 0x16, 0x00, 0x00, 0x00, //0x0000008a jne          LBB0_4
	0x48, 0xff, 0xc0, //0x00000090 incq         %rax
	0x4c, 0x39, 0xd8, //0x00000093 cmpq         %r11, %rax
	0x0f, 0x83, 0x89, 0x02, 0x00, 0x00, //0x00000096 jae          LBB0_50
	0x41, 0x8a, 0x14, 0x00, //0x0000009c movb         (%r8,%rax), %dl
	0x41, 0xb9, 0xff, 0xff, 0xff, 0xff, //0x000000a0 movl         $-1, %r9d
	//0x000000a6 LBB0_4
	0x8d, 0x4a, 0xd0, //0x000000a6 leal         $-48(%rdx), %ecx
	0x80, 0xf9, 0x0a, //0x000000a9 cmpb         $10, %cl
	0x0f, 0x82, 0x0f, 0x00, 0x00, 0x00, //0x000000ac jb           LBB0_6
	//0x000000b2 LBB0_5
	0x48, 0x89, 0x06, //0x000000b2 movq         %rax, (%rsi)
	0x49, 0xc7, 0x06, 0xfe, 0xff, 0xff, 0xff, //0x000000b5 movq         $-2, (%r14)
	0xe9, 0x6e, 0x02, 0x00, 0x00, //0x000000bc jmp          LBB0_51
	//0x000000c1 LBB0_6
	0x80, 0xfa, 0x30, //0x000000c1 cmpb         $48, %dl
	0x0f, 0x85, 0x34, 0x00, 0x00, 0x00, //0x000000c4 jne          LBB0_10
	0x48, 0x8d, 0x48, 0x01, //0x000000ca leaq         $1(%rax), %rcx
	0x4c, 0x39, 0xd8, //0x000000ce cmpq         %r11, %rax
	0x0f, 0x83, 0xd4, 0x00, 0x00, 0x00, //0x000000d1 jae          LBB0_21
	0x41, 0x8a, 0x1c, 0x08, //0x000000d7 movb         (%r8,%rcx), %bl
	0x80, 0xc3, 0xd2, //0x000000db addb         $-46, %bl
	0x80, 0xfb, 0x37, //0x000000de cmpb         $55, %bl
	0x0f, 0x87, 0xc4, 0x00, 0x00, 0x00, //0x000000e1 ja           LBB0_21
	0x0f, 0xb6, 0xfb, //0x000000e7 movzbl       %bl, %edi
	0x48, 0xbb, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x000000ea movabsq      $36028797027352577, %rbx
	0x48, 0x0f, 0xa3, 0xfb, //0x000000f4 btq          %rdi, %rbx
	0x0f, 0x83, 0xad, 0x00, 0x00, 0x00, //0x000000f8 jae          LBB0_21
	//0x000000fe LBB0_10
	0x4c, 0x39, 0xd8, //0x000000fe cmpq         %r11, %rax
	0x0f, 0x83, 0x98, 0x00, 0x00, 0x00, //0x00000101 jae          LBB0_20
	0x48, 0xff, 0xc0, //0x00000107 incq         %rax
	0x31, 0xc9, //0x0000010a xorl         %ecx, %ecx
	0x31, 0xdb, //0x0000010c xorl         %ebx, %ebx
	0x45, 0x31, 0xff, //0x0000010e xorl         %r15d, %r15d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000111 .p2align 4, 0x90
	//0x00000120 LBB0_12
	0x83, 0xfb, 0x12, //0x00000120 cmpl         $18, %ebx
	0x0f, 0x8f, 0x17, 0x00, 0x00, 0x00, //0x00000123 jg           LBB0_14
	0x0f, 0xb6, 0xd2, //0x00000129 movzbl       %dl, %edx
	0x4b, 0x8d, 0x3c, 0xbf, //0x0000012c leaq         (%r15,%r15,4), %rdi
	0x4c, 0x8d, 0x7c, 0x7a, 0xd0, //0x00000130 leaq         $-48(%rdx,%rdi,2), %r15
	0xff, 0xc3, //0x00000135 incl         %ebx
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x00000137 jmp          LBB0_15
	0x90, 0x90, 0x90, 0x90, //0x0000013c .p2align 4, 0x90
	//0x00000140 LBB0_14
	0xff, 0xc1, //0x00000140 incl         %ecx
	//0x00000142 LBB0_15
	0x49, 0x39, 0xc3, //0x00000142 cmpq         %rax, %r11
	0x0f, 0x84, 0x68, 0x00, 0x00, 0x00, //0x00000145 je           LBB0_22
	0x41, 0x0f, 0xb6, 0x14, 0x00, //0x0000014b movzbl       (%r8,%rax), %edx
	0x8d, 0x7a, 0xd0, //0x00000150 leal         $-48(%rdx), %edi
	0x48, 0xff, 0xc0, //0x00000153 incq         %rax
	0x40, 0x80, 0xff, 0x0a, //0x00000156 cmpb         $10, %dil
	0x0f, 0x82, 0xc0, 0xff, 0xff, 0xff, //0x0000015a jb           LBB0_12
	0x31, 0xff, //0x00000160 xorl         %edi, %edi
	0x85, 0xc9, //0x00000162 testl        %ecx, %ecx
	0x40, 0x0f, 0x9f, 0xc7, //0x00000164 setg         %dil
	0x80, 0xfa, 0x2e, //0x00000168 cmpb         $46, %dl
	0x0f, 0x85, 0xde, 0x02, 0x00, 0x00, //0x0000016b jne          LBB0_66
	0x49, 0xc7, 0x06, 0x08, 0x00, 0x00, 0x00, //0x00000171 movq         $8, (%r14)
	0x4c, 0x39, 0xd8, //0x00000178 cmpq         %r11, %rax
	0x0f, 0x83, 0xa4, 0x01, 0x00, 0x00, //0x0000017b jae          LBB0_50
	0x89, 0x7d, 0xc0, //0x00000181 movl         %edi, $-64(%rbp)
	0x41, 0x8a, 0x14, 0x00, //0x00000184 movb         (%r8,%rax), %dl
	0x80, 0xc2, 0xd0, //0x00000188 addb         $-48, %dl
	0x41, 0xba, 0x08, 0x00, 0x00, 0x00, //0x0000018b movl         $8, %r10d
	0x80, 0xfa, 0x0a, //0x00000191 cmpb         $10, %dl
	0x0f, 0x83, 0x18, 0xff, 0xff, 0xff, //0x00000194 jae          LBB0_5
	0xe9, 0x27, 0x00, 0x00, 0x00, //0x0000019a jmp          LBB0_24
	//0x0000019f LBB0_20
	0x31, 0xc9, //0x0000019f xorl         %ecx, %ecx
	0x31, 0xdb, //0x000001a1 xorl         %ebx, %ebx
	0x45, 0x31, 0xff, //0x000001a3 xorl         %r15d, %r15d
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x000001a6 jmp          LBB0_23
	//0x000001ab LBB0_21
	0x48, 0x89, 0x0e, //0x000001ab movq         %rcx, (%rsi)
	0xe9, 0x7c, 0x01, 0x00, 0x00, //0x000001ae jmp          LBB0_51
	//0x000001b3 LBB0_22
	0x4c, 0x89, 0xd8, //0x000001b3 movq         %r11, %rax
	//0x000001b6 LBB0_23
	0x31, 0xd2, //0x000001b6 xorl         %edx, %edx
	0x85, 0xc9, //0x000001b8 testl        %ecx, %ecx
	0x0f, 0x9f, 0xc2, //0x000001ba setg         %dl
	0x89, 0x55, 0xc0, //0x000001bd movl         %edx, $-64(%rbp)
	0x41, 0xba, 0x09, 0x00, 0x00, 0x00, //0x000001c0 movl         $9, %r10d
	//0x000001c6 LBB0_24
	0x85, 0xc9, //0x000001c6 testl        %ecx, %ecx
	0x44, 0x89, 0x4d, 0xc8, //0x000001c8 movl         %r9d, $-56(%rbp)
	0x0f, 0x85, 0x58, 0x00, 0x00, 0x00, //0x000001cc jne          LBB0_33
	//0x000001d2 LBB0_25
	0x4d, 0x85, 0xff, //0x000001d2 testq        %r15, %r15
	0x0f, 0x85, 0x4f, 0x00, 0x00, 0x00, //0x000001d5 jne          LBB0_33
	0x4c, 0x39, 0xd8, //0x000001db cmpq         %r11, %rax
	0x0f, 0x83, 0x3f, 0x00, 0x00, 0x00, //0x000001de jae          LBB0_31
	0x41, 0x89, 0xc1, //0x000001e4 movl         %eax, %r9d
	0x45, 0x29, 0xd9, //0x000001e7 subl         %r11d, %r9d
	0x31, 0xdb, //0x000001ea xorl         %ebx, %ebx
	0x31, 0xc9, //0x000001ec xorl         %ecx, %ecx
	0x90, 0x90, //0x000001ee .p2align 4, 0x90
	//0x000001f0 LBB0_28
	0x41, 0x80, 0x3c, 0x00, 0x30, //0x000001f0 cmpb         $48, (%r8,%rax)
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x000001f5 jne          LBB0_32
	0x48, 0xff, 0xc0, //0x000001fb incq         %rax
	0xff, 0xc9, //0x000001fe decl         %ecx
	0x49, 0x39, 0xc3, //0x00000200 cmpq         %rax, %r11
	0x0f, 0x85, 0xe7, 0xff, 0xff, 0xff, //0x00000203 jne          LBB0_28
	0x45, 0x31, 0xff, //0x00000209 xorl         %r15d, %r15d
	0x41, 0x83, 0xfa, 0x09, //0x0000020c cmpl         $9, %r10d
	0x48, 0x89, 0x75, 0xa0, //0x00000210 movq         %rsi, $-96(%rbp)
	0x4c, 0x89, 0x5d, 0xa8, //0x00000214 movq         %r11, $-88(%rbp)
	0x0f, 0x84, 0x38, 0x01, 0x00, 0x00, //0x00000218 je           LBB0_53
	0xe9, 0x69, 0x01, 0x00, 0x00, //0x0000021e jmp          LBB0_57
	//0x00000223 LBB0_31
	0x31, 0xc9, //0x00000223 xorl         %ecx, %ecx
	0x31, 0xdb, //0x00000225 xorl         %ebx, %ebx
	//0x00000227 LBB0_32
	0x45, 0x31, 0xff, //0x00000227 xorl         %r15d, %r15d
	//0x0000022a LBB0_33
	0x4c, 0x39, 0xd8, //0x0000022a cmpq         %r11, %rax
	0x0f, 0x83, 0x41, 0x00, 0x00, 0x00, //0x0000022d jae          LBB0_38
	0x83, 0xfb, 0x12, //0x00000233 cmpl         $18, %ebx
	0x0f, 0x8f, 0x38, 0x00, 0x00, 0x00, //0x00000236 jg           LBB0_38
	0x90, 0x90, 0x90, 0x90, //0x0000023c .p2align 4, 0x90
	//0x00000240 LBB0_35
	0x41, 0x0f, 0xb6, 0x14, 0x00, //0x00000240 movzbl       (%r8,%rax), %edx
	0x8d, 0x7a, 0xd0, //0x00000245 leal         $-48(%rdx), %edi
	0x40, 0x80, 0xff, 0x09, //0x00000248 cmpb         $9, %dil
	0x0f, 0x87, 0x22, 0x00, 0x00, 0x00, //0x0000024c ja           LBB0_38
	0x4b, 0x8d, 0x3c, 0xbf, //0x00000252 leaq         (%r15,%r15,4), %rdi
	0x4c, 0x8d, 0x7c, 0x7a, 0xd0, //0x00000256 leaq         $-48(%rdx,%rdi,2), %r15
	0xff, 0xc9, //0x0000025b decl         %ecx
	0x48, 0xff, 0xc0, //0x0000025d incq         %rax
	0x83, 0xfb, 0x11, //0x00000260 cmpl         $17, %ebx
	0x0f, 0x8f, 0x0b, 0x00, 0x00, 0x00, //0x00000263 jg           LBB0_38
	0xff, 0xc3, //0x00000269 incl         %ebx
	0x4c, 0x39, 0xd8, //0x0000026b cmpq         %r11, %rax
	0x0f, 0x82, 0xcc, 0xff, 0xff, 0xff, //0x0000026e jb           LBB0_35
	//0x00000274 LBB0_38
	0x4c, 0x39, 0xd8, //0x00000274 cmpq         %r11, %rax
	0x0f, 0x83, 0xc1, 0x00, 0x00, 0x00, //0x00000277 jae          LBB0_52
	0x41, 0x8a, 0x1c, 0x00, //0x0000027d movb         (%r8,%rax), %bl
	0x8d, 0x53, 0xd0, //0x00000281 leal         $-48(%rbx), %edx
	0x80, 0xfa, 0x09, //0x00000284 cmpb         $9, %dl
	0x0f, 0x87, 0x39, 0x00, 0x00, 0x00, //0x00000287 ja           LBB0_44
	0x49, 0x8d, 0x53, 0xff, //0x0000028d leaq         $-1(%r11), %rdx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000291 .p2align 4, 0x90
	//0x000002a0 LBB0_41
	0x48, 0x39, 0xc2, //0x000002a0 cmpq         %rax, %rdx
	0x0f, 0x84, 0xc3, 0x01, 0x00, 0x00, //0x000002a3 je           LBB0_67
	0x41, 0x0f, 0xb6, 0x5c, 0x00, 0x01, //0x000002a9 movzbl       $1(%r8,%rax), %ebx
	0x48, 0xff, 0xc0, //0x000002af incq         %rax
	0x8d, 0x7b, 0xd0, //0x000002b2 leal         $-48(%rbx), %edi
	0x40, 0x80, 0xff, 0x09, //0x000002b5 cmpb         $9, %dil
	0x0f, 0x86, 0xe1, 0xff, 0xff, 0xff, //0x000002b9 jbe          LBB0_41
	0xc7, 0x45, 0xc0, 0x01, 0x00, 0x00, 0x00, //0x000002bf movl         $1, $-64(%rbp)
	//0x000002c6 LBB0_44
	0x80, 0xcb, 0x20, //0x000002c6 orb          $32, %bl
	0x80, 0xfb, 0x65, //0x000002c9 cmpb         $101, %bl
	0x0f, 0x85, 0x6c, 0x00, 0x00, 0x00, //0x000002cc jne          LBB0_52
	0x48, 0x8d, 0x50, 0x01, //0x000002d2 leaq         $1(%rax), %rdx
	0x49, 0xc7, 0x06, 0x08, 0x00, 0x00, 0x00, //0x000002d6 movq         $8, (%r14)
	0x4c, 0x39, 0xda, //0x000002dd cmpq         %r11, %rdx
	0x0f, 0x83, 0x3f, 0x00, 0x00, 0x00, //0x000002e0 jae          LBB0_50
	0x41, 0x8a, 0x1c, 0x10, //0x000002e6 movb         (%r8,%rdx), %bl
	0x80, 0xfb, 0x2d, //0x000002ea cmpb         $45, %bl
	0x0f, 0x84, 0x0f, 0x00, 0x00, 0x00, //0x000002ed je           LBB0_48
	0x41, 0xba, 0x01, 0x00, 0x00, 0x00, //0x000002f3 movl         $1, %r10d
	0x80, 0xfb, 0x2b, //0x000002f9 cmpb         $43, %bl
	0x0f, 0x85, 0x0b, 0x05, 0x00, 0x00, //0x000002fc jne          LBB0_102
	//0x00000302 LBB0_48
	0x48, 0x83, 0xc0, 0x02, //0x00000302 addq         $2, %rax
	0x4c, 0x39, 0xd8, //0x00000306 cmpq         %r11, %rax
	0x0f, 0x83, 0x16, 0x00, 0x00, 0x00, //0x00000309 jae          LBB0_50
	0x31, 0xd2, //0x0000030f xorl         %edx, %edx
	0x80, 0xfb, 0x2b, //0x00000311 cmpb         $43, %bl
	0x0f, 0x94, 0xc2, //0x00000314 sete         %dl
	0x44, 0x8d, 0x54, 0x12, 0xff, //0x00000317 leal         $-1(%rdx,%rdx), %r10d
	0x41, 0x8a, 0x1c, 0x00, //0x0000031c movb         (%r8,%rax), %bl
	0xe9, 0xeb, 0x04, 0x00, 0x00, //0x00000320 jmp          LBB0_103
	//0x00000325 LBB0_50
	0x4c, 0x89, 0x1e, //0x00000325 movq         %r11, (%rsi)
	0x49, 0xc7, 0x06, 0xff, 0xff, 0xff, 0xff, //0x00000328 movq         $-1, (%r14)
	//0x0000032f LBB0_51
	0x48, 0x83, 0xc4, 0x40, //0x0000032f addq         $64, %rsp
	0x5b, //0x00000333 popq         %rbx
	0x41, 0x5c, //0x00000334 popq         %r12
	0x41, 0x5d, //0x00000336 popq         %r13
	0x41, 0x5e, //0x00000338 popq         %r14
	0x41, 0x5f, //0x0000033a popq         %r15
	0x5d, //0x0000033c popq         %rbp
	0xc3, //0x0000033d retq         
	//0x0000033e LBB0_52
	0x41, 0x89, 0xc9, //0x0000033e movl         %ecx, %r9d
	0x49, 0x89, 0xc3, //0x00000341 movq         %rax, %r11
	0x41, 0x83, 0xfa, 0x09, //0x00000344 cmpl         $9, %r10d
	0x48, 0x89, 0x75, 0xa0, //0x00000348 movq         %rsi, $-96(%rbp)
	0x4c, 0x89, 0x5d, 0xa8, //0x0000034c movq         %r11, $-88(%rbp)
	0x0f, 0x85, 0x36, 0x00, 0x00, 0x00, //0x00000350 jne          LBB0_57
	//0x00000356 LBB0_53
	0x45, 0x85, 0xc9, //0x00000356 testl        %r9d, %r9d
	0x0f, 0x85, 0x26, 0x00, 0x00, 0x00, //0x00000359 jne          LBB0_56
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x0000035f movabsq      $-9223372036854775808, %rax
	0x48, 0x63, 0x4d, 0xc8, //0x00000369 movslq       $-56(%rbp), %rcx
	0x4d, 0x85, 0xff, //0x0000036d testq        %r15, %r15
	0x0f, 0x89, 0x3d, 0x01, 0x00, 0x00, //0x00000370 jns          LBB0_70
	0x4c, 0x89, 0xfa, //0x00000376 movq         %r15, %rdx
	0x48, 0x21, 0xca, //0x00000379 andq         %rcx, %rdx
	0x48, 0x39, 0xc2, //0x0000037c cmpq         %rax, %rdx
	0x0f, 0x84, 0x2e, 0x01, 0x00, 0x00, //0x0000037f je           LBB0_70
	//0x00000385 LBB0_56
	0x49, 0xc7, 0x06, 0x08, 0x00, 0x00, 0x00, //0x00000385 movq         $8, (%r14)
	//0x0000038c LBB0_57
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x0000038c movabsq      $-9223372036854775808, %rdi
	0x4c, 0x89, 0xf8, //0x00000396 movq         %r15, %rax
	0x48, 0xc1, 0xe8, 0x34, //0x00000399 shrq         $52, %rax
	0x4c, 0x89, 0x75, 0x98, //0x0000039d movq         %r14, $-104(%rbp)
	0x0f, 0x84, 0x28, 0x00, 0x00, 0x00, //0x000003a1 je           LBB0_61
	//0x000003a7 LBB0_58
	0x41, 0x8d, 0x81, 0x5c, 0x01, 0x00, 0x00, //0x000003a7 leal         $348(%r9), %eax
	0x3d, 0xb7, 0x02, 0x00, 0x00, //0x000003ae cmpl         $695, %eax
	0x0f, 0x87, 0x88, 0x02, 0x00, 0x00, //0x000003b3 ja           LBB0_85
	0x4d, 0x85, 0xff, //0x000003b9 testq        %r15, %r15
	0x0f, 0x84, 0x7c, 0x01, 0x00, 0x00, //0x000003bc je           LBB0_76
	//0x000003c2 LBB0_60
	0x4d, 0x0f, 0xbd, 0xd7, //0x000003c2 bsrq         %r15, %r10
	0x49, 0x83, 0xf2, 0x3f, //0x000003c6 xorq         $63, %r10
	0xe9, 0x75, 0x01, 0x00, 0x00, //0x000003ca jmp          LBB0_77
	//0x000003cf LBB0_61
	0x66, 0x49, 0x0f, 0x6e, 0xc7, //0x000003cf movq         %r15, %xmm0
	0x66, 0x0f, 0x62, 0x05, 0x24, 0xfc, 0xff, 0xff, //0x000003d4 punpckldq    $-988(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0x66, 0x0f, 0x5c, 0x05, 0x2c, 0xfc, 0xff, 0xff, //0x000003dc subpd        $-980(%rip), %xmm0  /* LCPI0_1+0(%rip) */
	0x66, 0x0f, 0x28, 0xc8, //0x000003e4 movapd       %xmm0, %xmm1
	0x66, 0x0f, 0x15, 0xc8, //0x000003e8 unpckhpd     %xmm0, %xmm1
	0xf2, 0x0f, 0x58, 0xc8, //0x000003ec addsd        %xmm0, %xmm1
	0x66, 0x48, 0x0f, 0x7e, 0xc8, //0x000003f0 movq         %xmm1, %rax
	0x8b, 0x4d, 0xc8, //0x000003f5 movl         $-56(%rbp), %ecx
	0x89, 0xcb, //0x000003f8 movl         %ecx, %ebx
	0xc1, 0xeb, 0x1f, //0x000003fa shrl         $31, %ebx
	0x48, 0xc1, 0xe3, 0x3f, //0x000003fd shlq         $63, %rbx
	0x48, 0x09, 0xc3, //0x00000401 orq          %rax, %rbx
	0x4d, 0x85, 0xff, //0x00000404 testq        %r15, %r15
	0x0f, 0x84, 0xe0, 0x1e, 0x00, 0x00, //0x00000407 je           LBB0_507
	0x45, 0x85, 0xc9, //0x0000040d testl        %r9d, %r9d
	0x0f, 0x84, 0xd7, 0x1e, 0x00, 0x00, //0x00000410 je           LBB0_507
	0x66, 0x48, 0x0f, 0x6e, 0xc3, //0x00000416 movq         %rbx, %xmm0
	0x41, 0x8d, 0x41, 0xff, //0x0000041b leal         $-1(%r9), %eax
	0x83, 0xf8, 0x24, //0x0000041f cmpl         $36, %eax
	0x0f, 0x87, 0x65, 0x00, 0x00, 0x00, //0x00000422 ja           LBB0_68
	0x41, 0x83, 0xf9, 0x17, //0x00000428 cmpl         $23, %r9d
	0x0f, 0x8c, 0xc1, 0x00, 0x00, 0x00, //0x0000042c jl           LBB0_71
	0x49, 0x63, 0xc1, //0x00000432 movslq       %r9d, %rax
	0x48, 0x8d, 0x0d, 0xf4, 0x1e, 0x00, 0x00, //0x00000435 leaq         $7924(%rip), %rcx  /* _P10_TAB+0(%rip) */
	0xf2, 0x0f, 0x59, 0x84, 0xc1, 0x50, 0xff, 0xff, 0xff, //0x0000043c mulsd        $-176(%rcx,%rax,8), %xmm0
	0xb8, 0x16, 0x00, 0x00, 0x00, //0x00000445 movl         $22, %eax
	0xe9, 0xa7, 0x00, 0x00, 0x00, //0x0000044a jmp          LBB0_72
	//0x0000044f LBB0_66
	0x89, 0x7d, 0xc0, //0x0000044f movl         %edi, $-64(%rbp)
	0x48, 0xff, 0xc8, //0x00000452 decq         %rax
	0x41, 0xba, 0x09, 0x00, 0x00, 0x00, //0x00000455 movl         $9, %r10d
	0x85, 0xc9, //0x0000045b testl        %ecx, %ecx
	0x44, 0x89, 0x4d, 0xc8, //0x0000045d movl         %r9d, $-56(%rbp)
	0x0f, 0x84, 0x6b, 0xfd, 0xff, 0xff, //0x00000461 je           LBB0_25
	0xe9, 0xbe, 0xfd, 0xff, 0xff, //0x00000467 jmp          LBB0_33
	//0x0000046c LBB0_67
	0xc7, 0x45, 0xc0, 0x01, 0x00, 0x00, 0x00, //0x0000046c movl         $1, $-64(%rbp)
	0x41, 0x89, 0xc9, //0x00000473 movl         %ecx, %r9d
	0x41, 0x83, 0xfa, 0x09, //0x00000476 cmpl         $9, %r10d
	0x48, 0x89, 0x75, 0xa0, //0x0000047a movq         %rsi, $-96(%rbp)
	0x4c, 0x89, 0x5d, 0xa8, //0x0000047e movq         %r11, $-88(%rbp)
	0x0f, 0x84, 0xce, 0xfe, 0xff, 0xff, //0x00000482 je           LBB0_53
	0xe9, 0xff, 0xfe, 0xff, 0xff, //0x00000488 jmp          LBB0_57
	//0x0000048d LBB0_68
	0x41, 0x83, 0xf9, 0xea, //0x0000048d cmpl         $-22, %r9d
	0x0f, 0x82, 0x10, 0xff, 0xff, 0xff, //0x00000491 jb           LBB0_58
	0x41, 0xf7, 0xd9, //0x00000497 negl         %r9d
	0x49, 0x63, 0xc1, //0x0000049a movslq       %r9d, %rax
	0x48, 0x8d, 0x0d, 0x8c, 0x1e, 0x00, 0x00, //0x0000049d leaq         $7820(%rip), %rcx  /* _P10_TAB+0(%rip) */
	0xf2, 0x0f, 0x5e, 0x04, 0xc1, //0x000004a4 divsd        (%rcx,%rax,8), %xmm0
	0x66, 0x48, 0x0f, 0x7e, 0xc3, //0x000004a9 movq         %xmm0, %rbx
	0xe9, 0x3a, 0x1e, 0x00, 0x00, //0x000004ae jmp          LBB0_507
	//0x000004b3 LBB0_70
	0x66, 0x49, 0x0f, 0x6e, 0xc7, //0x000004b3 movq         %r15, %xmm0
	0x4c, 0x0f, 0xaf, 0xf9, //0x000004b8 imulq        %rcx, %r15
	0x66, 0x0f, 0x62, 0x05, 0x3c, 0xfb, 0xff, 0xff, //0x000004bc punpckldq    $-1220(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0x66, 0x0f, 0x5c, 0x05, 0x44, 0xfb, 0xff, 0xff, //0x000004c4 subpd        $-1212(%rip), %xmm0  /* LCPI0_1+0(%rip) */
	0x4d, 0x89, 0x7e, 0x10, //0x000004cc movq         %r15, $16(%r14)
	0x66, 0x0f, 0x28, 0xc8, //0x000004d0 movapd       %xmm0, %xmm1
	0x66, 0x0f, 0x15, 0xc8, //0x000004d4 unpckhpd     %xmm0, %xmm1
	0xf2, 0x0f, 0x58, 0xc8, //0x000004d8 addsd        %xmm0, %xmm1
	0x48, 0x21, 0xc8, //0x000004dc andq         %rcx, %rax
	0x66, 0x48, 0x0f, 0x7e, 0xc9, //0x000004df movq         %xmm1, %rcx
	0x48, 0x09, 0xc1, //0x000004e4 orq          %rax, %rcx
	0x49, 0x89, 0x4e, 0x08, //0x000004e7 movq         %rcx, $8(%r14)
	0x4c, 0x89, 0x1e, //0x000004eb movq         %r11, (%rsi)
	0xe9, 0x3c, 0xfe, 0xff, 0xff, //0x000004ee jmp          LBB0_51
	//0x000004f3 LBB0_71
	0x44, 0x89, 0xc8, //0x000004f3 movl         %r9d, %eax
	//0x000004f6 LBB0_72
	0x66, 0x0f, 0x2e, 0x05, 0x22, 0xfb, 0xff, 0xff, //0x000004f6 ucomisd      $-1246(%rip), %xmm0  /* LCPI0_2+0(%rip) */
	0x0f, 0x87, 0x2a, 0x00, 0x00, 0x00, //0x000004fe ja           LBB0_75
	0xf2, 0x0f, 0x10, 0x0d, 0x1c, 0xfb, 0xff, 0xff, //0x00000504 movsd        $-1252(%rip), %xmm1  /* LCPI0_3+0(%rip) */
	0x66, 0x0f, 0x2e, 0xc8, //0x0000050c ucomisd      %xmm0, %xmm1
	0x0f, 0x87, 0x18, 0x00, 0x00, 0x00, //0x00000510 ja           LBB0_75
	0x89, 0xc0, //0x00000516 movl         %eax, %eax
	0x48, 0x8d, 0x0d, 0x11, 0x1e, 0x00, 0x00, //0x00000518 leaq         $7697(%rip), %rcx  /* _P10_TAB+0(%rip) */
	0xf2, 0x0f, 0x59, 0x04, 0xc1, //0x0000051f mulsd        (%rcx,%rax,8), %xmm0
	0x66, 0x48, 0x0f, 0x7e, 0xc3, //0x00000524 movq         %xmm0, %rbx
	0xe9, 0xbf, 0x1d, 0x00, 0x00, //0x00000529 jmp          LBB0_507
	//0x0000052e LBB0_75
	0x41, 0x8d, 0x81, 0x5c, 0x01, 0x00, 0x00, //0x0000052e leal         $348(%r9), %eax
	0x4d, 0x85, 0xff, //0x00000535 testq        %r15, %r15
	0x0f, 0x85, 0x84, 0xfe, 0xff, 0xff, //0x00000538 jne          LBB0_60
	//0x0000053e LBB0_76
	0x41, 0xba, 0x40, 0x00, 0x00, 0x00, //0x0000053e movl         $64, %r10d
	//0x00000544 LBB0_77
	0x4c, 0x89, 0xfb, //0x00000544 movq         %r15, %rbx
	0x44, 0x89, 0xd1, //0x00000547 movl         %r10d, %ecx
	0x48, 0xd3, 0xe3, //0x0000054a shlq         %cl, %rbx
	0x89, 0xc6, //0x0000054d movl         %eax, %esi
	0x48, 0xc1, 0xe6, 0x04, //0x0000054f shlq         $4, %rsi
	0x48, 0x8d, 0x05, 0x96, 0x1e, 0x00, 0x00, //0x00000553 leaq         $7830(%rip), %rax  /* _POW10_M128_TAB+0(%rip) */
	0x48, 0x8b, 0x44, 0x06, 0x08, //0x0000055a movq         $8(%rsi,%rax), %rax
	0x48, 0x89, 0x45, 0xb0, //0x0000055f movq         %rax, $-80(%rbp)
	0x48, 0xf7, 0xe3, //0x00000563 mulq         %rbx
	0x49, 0x89, 0xc6, //0x00000566 movq         %rax, %r14
	0x49, 0x89, 0xd3, //0x00000569 movq         %rdx, %r11
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x0000056c andl         $511, %edx
	0x48, 0x89, 0xd9, //0x00000572 movq         %rbx, %rcx
	0x48, 0xf7, 0xd1, //0x00000575 notq         %rcx
	0x48, 0x39, 0xc8, //0x00000578 cmpq         %rcx, %rax
	0x0f, 0x86, 0x49, 0x00, 0x00, 0x00, //0x0000057b jbe          LBB0_82
	0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x00000581 cmpl         $511, %edx
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x00000587 jne          LBB0_82
	0x48, 0x89, 0xd8, //0x0000058d movq         %rbx, %rax
	0x48, 0x8d, 0x15, 0x59, 0x1e, 0x00, 0x00, //0x00000590 leaq         $7769(%rip), %rdx  /* _POW10_M128_TAB+0(%rip) */
	0x48, 0xf7, 0x24, 0x16, //0x00000597 mulq         (%rsi,%rdx)
	0x49, 0x01, 0xd6, //0x0000059b addq         %rdx, %r14
	0x49, 0x83, 0xd3, 0x00, //0x0000059e adcq         $0, %r11
	0x44, 0x89, 0xda, //0x000005a2 movl         %r11d, %edx
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x000005a5 andl         $511, %edx
	0x48, 0x39, 0xc8, //0x000005ab cmpq         %rcx, %rax
	0x0f, 0x86, 0x16, 0x00, 0x00, 0x00, //0x000005ae jbe          LBB0_82
	0x49, 0x83, 0xfe, 0xff, //0x000005b4 cmpq         $-1, %r14
	0x0f, 0x85, 0x0c, 0x00, 0x00, 0x00, //0x000005b8 jne          LBB0_82
	0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x000005be cmpl         $511, %edx
	0x0f, 0x84, 0x77, 0x00, 0x00, 0x00, //0x000005c4 je           LBB0_85
	//0x000005ca LBB0_82
	0x4c, 0x89, 0xd8, //0x000005ca movq         %r11, %rax
	0x48, 0xc1, 0xe8, 0x3f, //0x000005cd shrq         $63, %rax
	0x8d, 0x48, 0x09, //0x000005d1 leal         $9(%rax), %ecx
	0x49, 0xd3, 0xeb, //0x000005d4 shrq         %cl, %r11
	0x4c, 0x09, 0xf2, //0x000005d7 orq          %r14, %rdx
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x000005da jne          LBB0_84
	0x44, 0x89, 0xd9, //0x000005e0 movl         %r11d, %ecx
	0x83, 0xe1, 0x03, //0x000005e3 andl         $3, %ecx
	0x83, 0xf9, 0x01, //0x000005e6 cmpl         $1, %ecx
	0x0f, 0x84, 0x52, 0x00, 0x00, 0x00, //0x000005e9 je           LBB0_85
	//0x000005ef LBB0_84
	0x41, 0x69, 0xc9, 0x6a, 0x52, 0x03, 0x00, //0x000005ef imull        $217706, %r9d, %ecx
	0xc1, 0xf9, 0x10, //0x000005f6 sarl         $16, %ecx
	0x81, 0xc1, 0x3f, 0x04, 0x00, 0x00, //0x000005f9 addl         $1087, %ecx
	0x4c, 0x63, 0xf1, //0x000005ff movslq       %ecx, %r14
	0x4c, 0x89, 0xf2, //0x00000602 movq         %r14, %rdx
	0x4c, 0x29, 0xd2, //0x00000605 subq         %r10, %rdx
	0x49, 0xba, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x01, //0x00000608 movabsq      $126100789566373888, %r10
	0x48, 0x83, 0xf0, 0x01, //0x00000612 xorq         $1, %rax
	0x48, 0x29, 0xc2, //0x00000616 subq         %rax, %rdx
	0x44, 0x89, 0xd8, //0x00000619 movl         %r11d, %eax
	0x83, 0xe0, 0x01, //0x0000061c andl         $1, %eax
	0x4c, 0x01, 0xd8, //0x0000061f addq         %r11, %rax
	0x48, 0x89, 0xc1, //0x00000622 movq         %rax, %rcx
	0x4c, 0x21, 0xd1, //0x00000625 andq         %r10, %rcx
	0x48, 0x83, 0xf9, 0x01, //0x00000628 cmpq         $1, %rcx
	0x48, 0x83, 0xda, 0xff, //0x0000062c sbbq         $-1, %rdx
	0x48, 0x8d, 0x5a, 0xff, //0x00000630 leaq         $-1(%rdx), %rbx
	0x48, 0x81, 0xfb, 0xfd, 0x07, 0x00, 0x00, //0x00000634 cmpq         $2045, %rbx
	0x0f, 0x86, 0x68, 0x00, 0x00, 0x00, //0x0000063b jbe          LBB0_90
	//0x00000641 LBB0_85
	0x48, 0x8b, 0x45, 0xa0, //0x00000641 movq         $-96(%rbp), %rax
	0x48, 0x8b, 0x30, //0x00000645 movq         (%rax), %rsi
	0x4d, 0x8d, 0x14, 0x30, //0x00000648 leaq         (%r8,%rsi), %r10
	0x48, 0x8b, 0x4d, 0xa8, //0x0000064c movq         $-88(%rbp), %rcx
	0x48, 0x29, 0xf1, //0x00000650 subq         %rsi, %rcx
	0x4d, 0x85, 0xed, //0x00000653 testq        %r13, %r13
	0x0f, 0x84, 0xc5, 0x02, 0x00, 0x00, //0x00000656 je           LBB0_116
	0x41, 0xc6, 0x04, 0x24, 0x00, //0x0000065c movb         $0, (%r12)
	0x49, 0x83, 0xfd, 0x01, //0x00000661 cmpq         $1, %r13
	0x0f, 0x84, 0xb6, 0x02, 0x00, 0x00, //0x00000665 je           LBB0_116
	0x4d, 0x8d, 0x4d, 0xff, //0x0000066b leaq         $-1(%r13), %r9
	0xbf, 0x01, 0x00, 0x00, 0x00, //0x0000066f movl         $1, %edi
	0x49, 0x83, 0xf9, 0x20, //0x00000674 cmpq         $32, %r9
	0x0f, 0x82, 0x92, 0x02, 0x00, 0x00, //0x00000678 jb           LBB0_115
	0x4c, 0x89, 0xcf, //0x0000067e movq         %r9, %rdi
	0x48, 0x83, 0xe7, 0xe0, //0x00000681 andq         $-32, %rdi
	0x48, 0x8d, 0x57, 0xe0, //0x00000685 leaq         $-32(%rdi), %rdx
	0x48, 0x89, 0xd3, //0x00000689 movq         %rdx, %rbx
	0x48, 0xc1, 0xeb, 0x05, //0x0000068c shrq         $5, %rbx
	0x48, 0xff, 0xc3, //0x00000690 incq         %rbx
	0x89, 0xd8, //0x00000693 movl         %ebx, %eax
	0x83, 0xe0, 0x03, //0x00000695 andl         $3, %eax
	0x48, 0x83, 0xfa, 0x60, //0x00000698 cmpq         $96, %rdx
	0x0f, 0x83, 0xcf, 0x01, 0x00, 0x00, //0x0000069c jae          LBB0_108
	0x31, 0xdb, //0x000006a2 xorl         %ebx, %ebx
	0xe9, 0x1a, 0x02, 0x00, 0x00, //0x000006a4 jmp          LBB0_110
	//0x000006a9 LBB0_90
	0x48, 0x83, 0xf9, 0x01, //0x000006a9 cmpq         $1, %rcx
	0xb1, 0x02, //0x000006ad movb         $2, %cl
	0x80, 0xd9, 0x00, //0x000006af sbbb         $0, %cl
	0x48, 0xd3, 0xe8, //0x000006b2 shrq         %cl, %rax
	0x48, 0xc1, 0xe2, 0x34, //0x000006b5 shlq         $52, %rdx
	0x48, 0xb9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x000006b9 movabsq      $4503599627370495, %rcx
	0x48, 0x21, 0xc8, //0x000006c3 andq         %rcx, %rax
	0x48, 0x09, 0xd0, //0x000006c6 orq          %rdx, %rax
	0x48, 0x89, 0xc3, //0x000006c9 movq         %rax, %rbx
	0x48, 0x09, 0xfb, //0x000006cc orq          %rdi, %rbx
	0x8b, 0x4d, 0xc8, //0x000006cf movl         $-56(%rbp), %ecx
	0x83, 0xf9, 0xff, //0x000006d2 cmpl         $-1, %ecx
	0x48, 0x0f, 0x45, 0xd8, //0x000006d5 cmovneq      %rax, %rbx
	0x83, 0x7d, 0xc0, 0x00, //0x000006d9 cmpl         $0, $-64(%rbp)
	0x0f, 0x84, 0x0a, 0x1c, 0x00, 0x00, //0x000006dd je           LBB0_507
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x000006e3 movl         $64, %ecx
	0x49, 0xff, 0xc7, //0x000006e8 incq         %r15
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x000006eb je           LBB0_93
	0x49, 0x0f, 0xbd, 0xcf, //0x000006f1 bsrq         %r15, %rcx
	0x48, 0x83, 0xf1, 0x3f, //0x000006f5 xorq         $63, %rcx
	//0x000006f9 LBB0_93
	0x48, 0x89, 0x4d, 0xc0, //0x000006f9 movq         %rcx, $-64(%rbp)
	0x49, 0xd3, 0xe7, //0x000006fd shlq         %cl, %r15
	0x48, 0x8b, 0x45, 0xb0, //0x00000700 movq         $-80(%rbp), %rax
	0x49, 0xf7, 0xe7, //0x00000704 mulq         %r15
	0x49, 0x89, 0xc3, //0x00000707 movq         %rax, %r11
	0x49, 0x89, 0xd1, //0x0000070a movq         %rdx, %r9
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x0000070d andl         $511, %edx
	0x4c, 0x89, 0xf9, //0x00000713 movq         %r15, %rcx
	0x48, 0xf7, 0xd1, //0x00000716 notq         %rcx
	0x48, 0x39, 0xc8, //0x00000719 cmpq         %rcx, %rax
	0x0f, 0x86, 0x49, 0x00, 0x00, 0x00, //0x0000071c jbe          LBB0_98
	0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x00000722 cmpl         $511, %edx
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x00000728 jne          LBB0_98
	0x4c, 0x89, 0xf8, //0x0000072e movq         %r15, %rax
	0x48, 0x8d, 0x15, 0xb8, 0x1c, 0x00, 0x00, //0x00000731 leaq         $7352(%rip), %rdx  /* _POW10_M128_TAB+0(%rip) */
	0x48, 0xf7, 0x24, 0x16, //0x00000738 mulq         (%rsi,%rdx)
	0x49, 0x01, 0xd3, //0x0000073c addq         %rdx, %r11
	0x49, 0x83, 0xd1, 0x00, //0x0000073f adcq         $0, %r9
	0x44, 0x89, 0xca, //0x00000743 movl         %r9d, %edx
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x00000746 andl         $511, %edx
	0x48, 0x39, 0xc8, //0x0000074c cmpq         %rcx, %rax
	0x0f, 0x86, 0x16, 0x00, 0x00, 0x00, //0x0000074f jbe          LBB0_98
	0x49, 0x83, 0xfb, 0xff, //0x00000755 cmpq         $-1, %r11
	0x0f, 0x85, 0x0c, 0x00, 0x00, 0x00, //0x00000759 jne          LBB0_98
	0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x0000075f cmpl         $511, %edx
	0x0f, 0x84, 0xd6, 0xfe, 0xff, 0xff, //0x00000765 je           LBB0_85
	//0x0000076b LBB0_98
	0x4c, 0x89, 0xc8, //0x0000076b movq         %r9, %rax
	0x48, 0xc1, 0xe8, 0x3f, //0x0000076e shrq         $63, %rax
	0x8d, 0x48, 0x09, //0x00000772 leal         $9(%rax), %ecx
	0x49, 0xd3, 0xe9, //0x00000775 shrq         %cl, %r9
	0x4c, 0x09, 0xda, //0x00000778 orq          %r11, %rdx
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x0000077b jne          LBB0_100
	0x44, 0x89, 0xc9, //0x00000781 movl         %r9d, %ecx
	0x83, 0xe1, 0x03, //0x00000784 andl         $3, %ecx
	0x83, 0xf9, 0x01, //0x00000787 cmpl         $1, %ecx
	0x0f, 0x84, 0xb1, 0xfe, 0xff, 0xff, //0x0000078a je           LBB0_85
	//0x00000790 LBB0_100
	0x4c, 0x2b, 0x75, 0xc0, //0x00000790 subq         $-64(%rbp), %r14
	0x48, 0x83, 0xf0, 0x01, //0x00000794 xorq         $1, %rax
	0x49, 0x29, 0xc6, //0x00000798 subq         %rax, %r14
	0x44, 0x89, 0xc8, //0x0000079b movl         %r9d, %eax
	0x83, 0xe0, 0x01, //0x0000079e andl         $1, %eax
	0x4c, 0x01, 0xc8, //0x000007a1 addq         %r9, %rax
	0x49, 0x21, 0xc2, //0x000007a4 andq         %rax, %r10
	0x49, 0x83, 0xfa, 0x01, //0x000007a7 cmpq         $1, %r10
	0x49, 0x83, 0xde, 0xff, //0x000007ab sbbq         $-1, %r14
	0x49, 0x8d, 0x4e, 0xff, //0x000007af leaq         $-1(%r14), %rcx
	0x48, 0x81, 0xf9, 0xfd, 0x07, 0x00, 0x00, //0x000007b3 cmpq         $2045, %rcx
	0x0f, 0x87, 0x81, 0xfe, 0xff, 0xff, //0x000007ba ja           LBB0_85
	0x49, 0x83, 0xfa, 0x01, //0x000007c0 cmpq         $1, %r10
	0xb1, 0x02, //0x000007c4 movb         $2, %cl
	0x80, 0xd9, 0x00, //0x000007c6 sbbb         $0, %cl
	0x48, 0xd3, 0xe8, //0x000007c9 shrq         %cl, %rax
	0x49, 0xc1, 0xe6, 0x34, //0x000007cc shlq         $52, %r14
	0x48, 0xb9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x000007d0 movabsq      $4503599627370495, %rcx
	0x48, 0x21, 0xc8, //0x000007da andq         %rcx, %rax
	0x4c, 0x09, 0xf0, //0x000007dd orq          %r14, %rax
	0x48, 0x89, 0xc1, //0x000007e0 movq         %rax, %rcx
	0x48, 0x09, 0xf9, //0x000007e3 orq          %rdi, %rcx
	0x83, 0x7d, 0xc8, 0xff, //0x000007e6 cmpl         $-1, $-56(%rbp)
	0x48, 0x0f, 0x45, 0xc8, //0x000007ea cmovneq      %rax, %rcx
	0x66, 0x48, 0x0f, 0x6e, 0xc3, //0x000007ee movq         %rbx, %xmm0
	0x66, 0x48, 0x0f, 0x6e, 0xc9, //0x000007f3 movq         %rcx, %xmm1
	0x66, 0x0f, 0x2e, 0xc1, //0x000007f8 ucomisd      %xmm1, %xmm0
	0x0f, 0x85, 0x3f, 0xfe, 0xff, 0xff, //0x000007fc jne          LBB0_85
	0x0f, 0x8b, 0xe5, 0x1a, 0x00, 0x00, //0x00000802 jnp          LBB0_507
	0xe9, 0x34, 0xfe, 0xff, 0xff, //0x00000808 jmp          LBB0_85
	//0x0000080d LBB0_102
	0x48, 0x89, 0xd0, //0x0000080d movq         %rdx, %rax
	//0x00000810 LBB0_103
	0x8d, 0x53, 0xd0, //0x00000810 leal         $-48(%rbx), %edx
	0x80, 0xfa, 0x09, //0x00000813 cmpb         $9, %dl
	0x0f, 0x87, 0x96, 0xf8, 0xff, 0xff, //0x00000816 ja           LBB0_5
	0x45, 0x31, 0xc9, //0x0000081c xorl         %r9d, %r9d
	0x4c, 0x39, 0xd8, //0x0000081f cmpq         %r11, %rax
	0x48, 0x89, 0x75, 0xa0, //0x00000822 movq         %rsi, $-96(%rbp)
	0x0f, 0x83, 0x43, 0x02, 0x00, 0x00, //0x00000826 jae          LBB0_138
	0x4c, 0x89, 0xdf, //0x0000082c movq         %r11, %rdi
	0x49, 0xff, 0xcb, //0x0000082f decq         %r11
	0x45, 0x31, 0xc9, //0x00000832 xorl         %r9d, %r9d
	//0x00000835 LBB0_106
	0x44, 0x89, 0xce, //0x00000835 movl         %r9d, %esi
	0x41, 0x81, 0xf9, 0x10, 0x27, 0x00, 0x00, //0x00000838 cmpl         $10000, %r9d
	0x8d, 0x14, 0xb6, //0x0000083f leal         (%rsi,%rsi,4), %edx
	0x0f, 0xb6, 0xdb, //0x00000842 movzbl       %bl, %ebx
	0x44, 0x8d, 0x4c, 0x53, 0xd0, //0x00000845 leal         $-48(%rbx,%rdx,2), %r9d
	0x44, 0x0f, 0x4d, 0xce, //0x0000084a cmovgel      %esi, %r9d
	0x49, 0x39, 0xc3, //0x0000084e cmpq         %rax, %r11
	0x0f, 0x84, 0x15, 0x02, 0x00, 0x00, //0x00000851 je           LBB0_137
	0x41, 0x0f, 0xb6, 0x5c, 0x00, 0x01, //0x00000857 movzbl       $1(%r8,%rax), %ebx
	0x48, 0xff, 0xc0, //0x0000085d incq         %rax
	0x8d, 0x53, 0xd0, //0x00000860 leal         $-48(%rbx), %edx
	0x80, 0xfa, 0x0a, //0x00000863 cmpb         $10, %dl
	0x0f, 0x82, 0xc9, 0xff, 0xff, 0xff, //0x00000866 jb           LBB0_106
	0xe9, 0xfe, 0x01, 0x00, 0x00, //0x0000086c jmp          LBB0_138
	//0x00000871 LBB0_108
	0x48, 0x89, 0xc2, //0x00000871 movq         %rax, %rdx
	0x48, 0x29, 0xda, //0x00000874 subq         %rbx, %rdx
	0x31, 0xdb, //0x00000877 xorl         %ebx, %ebx
	0x66, 0x0f, 0x57, 0xc0, //0x00000879 xorpd        %xmm0, %xmm0
	//0x0000087d LBB0_109
	0x66, 0x41, 0x0f, 0x11, 0x44, 0x1c, 0x01, //0x0000087d movupd       %xmm0, $1(%r12,%rbx)
	0x66, 0x41, 0x0f, 0x11, 0x44, 0x1c, 0x11, //0x00000884 movupd       %xmm0, $17(%r12,%rbx)
	0x66, 0x41, 0x0f, 0x11, 0x44, 0x1c, 0x21, //0x0000088b movupd       %xmm0, $33(%r12,%rbx)
	0x66, 0x41, 0x0f, 0x11, 0x44, 0x1c, 0x31, //0x00000892 movupd       %xmm0, $49(%r12,%rbx)
	0x66, 0x41, 0x0f, 0x11, 0x44, 0x1c, 0x41, //0x00000899 movupd       %xmm0, $65(%r12,%rbx)
	0x66, 0x41, 0x0f, 0x11, 0x44, 0x1c, 0x51, //0x000008a0 movupd       %xmm0, $81(%r12,%rbx)
	0x66, 0x41, 0x0f, 0x11, 0x44, 0x1c, 0x61, //0x000008a7 movupd       %xmm0, $97(%r12,%rbx)
	0x66, 0x41, 0x0f, 0x11, 0x44, 0x1c, 0x71, //0x000008ae movupd       %xmm0, $113(%r12,%rbx)
	0x48, 0x83, 0xeb, 0x80, //0x000008b5 subq         $-128, %rbx
	0x48, 0x83, 0xc2, 0x04, //0x000008b9 addq         $4, %rdx
	0x0f, 0x85, 0xba, 0xff, 0xff, 0xff, //0x000008bd jne          LBB0_109
	//0x000008c3 LBB0_110
	0x48, 0x85, 0xc0, //0x000008c3 testq        %rax, %rax
	0x0f, 0x84, 0x28, 0x00, 0x00, 0x00, //0x000008c6 je           LBB0_113
	0x48, 0xf7, 0xd8, //0x000008cc negq         %rax
	0x66, 0x0f, 0x57, 0xc0, //0x000008cf xorpd        %xmm0, %xmm0
	//0x000008d3 LBB0_112
	0x48, 0x89, 0xda, //0x000008d3 movq         %rbx, %rdx
	0x48, 0x83, 0xca, 0x01, //0x000008d6 orq          $1, %rdx
	0x66, 0x41, 0x0f, 0x11, 0x04, 0x14, //0x000008da movupd       %xmm0, (%r12,%rdx)
	0x66, 0x41, 0x0f, 0x11, 0x44, 0x14, 0x10, //0x000008e0 movupd       %xmm0, $16(%r12,%rdx)
	0x48, 0x83, 0xc3, 0x20, //0x000008e7 addq         $32, %rbx
	0x48, 0xff, 0xc0, //0x000008eb incq         %rax
	0x0f, 0x85, 0xdf, 0xff, 0xff, 0xff, //0x000008ee jne          LBB0_112
	//0x000008f4 LBB0_113
	0x49, 0x39, 0xf9, //0x000008f4 cmpq         %rdi, %r9
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x000008f7 je           LBB0_116
	0x48, 0x83, 0xcf, 0x01, //0x000008fd orq          $1, %rdi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000901 .p2align 4, 0x90
	//0x00000910 LBB0_115
	0x41, 0xc6, 0x04, 0x3c, 0x00, //0x00000910 movb         $0, (%r12,%rdi)
	0x48, 0xff, 0xc7, //0x00000915 incq         %rdi
	0x49, 0x39, 0xfd, //0x00000918 cmpq         %rdi, %r13
	0x0f, 0x85, 0xef, 0xff, 0xff, 0xff, //0x0000091b jne          LBB0_115
	//0x00000921 LBB0_116
	0x41, 0x8a, 0x12, //0x00000921 movb         (%r10), %dl
	0x31, 0xff, //0x00000924 xorl         %edi, %edi
	0x80, 0xfa, 0x2d, //0x00000926 cmpb         $45, %dl
	0x40, 0x0f, 0x94, 0xc7, //0x00000929 sete         %dil
	0x48, 0x39, 0xf9, //0x0000092d cmpq         %rdi, %rcx
	0x0f, 0x8e, 0xa5, 0x00, 0x00, 0x00, //0x00000930 jle          LBB0_128
	0x48, 0x89, 0x75, 0xc8, //0x00000936 movq         %rsi, $-56(%rbp)
	0x45, 0x31, 0xdb, //0x0000093a xorl         %r11d, %r11d
	0x88, 0x55, 0xd7, //0x0000093d movb         %dl, $-41(%rbp)
	0xb2, 0x01, //0x00000940 movb         $1, %dl
	0x45, 0x31, 0xff, //0x00000942 xorl         %r15d, %r15d
	0x45, 0x31, 0xf6, //0x00000945 xorl         %r14d, %r14d
	0x31, 0xc0, //0x00000948 xorl         %eax, %eax
	0x45, 0x31, 0xc9, //0x0000094a xorl         %r9d, %r9d
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x0000094d jmp          LBB0_120
	//0x00000952 LBB0_118
	0x41, 0xff, 0xcb, //0x00000952 decl         %r11d
	0x31, 0xc0, //0x00000955 xorl         %eax, %eax
	//0x00000957 LBB0_119
	0x48, 0xff, 0xc7, //0x00000957 incq         %rdi
	0x48, 0x39, 0xcf, //0x0000095a cmpq         %rcx, %rdi
	0x0f, 0x9c, 0xc2, //0x0000095d setl         %dl
	0x48, 0x39, 0xf9, //0x00000960 cmpq         %rdi, %rcx
	0x0f, 0x84, 0x90, 0x00, 0x00, 0x00, //0x00000963 je           LBB0_129
	//0x00000969 LBB0_120
	0x41, 0x0f, 0xb6, 0x1c, 0x3a, //0x00000969 movzbl       (%r10,%rdi), %ebx
	0x8d, 0x73, 0xd0, //0x0000096e leal         $-48(%rbx), %esi
	0x40, 0x80, 0xfe, 0x09, //0x00000971 cmpb         $9, %sil
	0x0f, 0x87, 0x35, 0x00, 0x00, 0x00, //0x00000975 ja           LBB0_125
	0x85, 0xc0, //0x0000097b testl        %eax, %eax
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x0000097d jne          LBB0_123
	0x80, 0xfb, 0x30, //0x00000983 cmpb         $48, %bl
	0x0f, 0x84, 0xc6, 0xff, 0xff, 0xff, //0x00000986 je           LBB0_118
	//0x0000098c LBB0_123
	0x49, 0x63, 0xc7, //0x0000098c movslq       %r15d, %rax
	0x49, 0x39, 0xc5, //0x0000098f cmpq         %rax, %r13
	0x0f, 0x86, 0x2f, 0x00, 0x00, 0x00, //0x00000992 jbe          LBB0_127
	0x41, 0x88, 0x1c, 0x04, //0x00000998 movb         %bl, (%r12,%rax)
	0x41, 0xff, 0xc7, //0x0000099c incl         %r15d
	0x44, 0x89, 0xf8, //0x0000099f movl         %r15d, %eax
	0xe9, 0xb0, 0xff, 0xff, 0xff, //0x000009a2 jmp          LBB0_119
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000009a7 .p2align 4, 0x90
	//0x000009b0 LBB0_125
	0x80, 0xfb, 0x2e, //0x000009b0 cmpb         $46, %bl
	0x0f, 0x85, 0x56, 0x00, 0x00, 0x00, //0x000009b3 jne          LBB0_130
	0x41, 0x89, 0xc3, //0x000009b9 movl         %eax, %r11d
	0x41, 0xb9, 0x01, 0x00, 0x00, 0x00, //0x000009bc movl         $1, %r9d
	0xe9, 0x90, 0xff, 0xff, 0xff, //0x000009c2 jmp          LBB0_119
	//0x000009c7 LBB0_127
	0x80, 0xfb, 0x30, //0x000009c7 cmpb         $48, %bl
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x000009ca movl         $1, %eax
	0x44, 0x0f, 0x45, 0xf0, //0x000009cf cmovnel      %eax, %r14d
	0x44, 0x89, 0xf8, //0x000009d3 movl         %r15d, %eax
	0xe9, 0x7c, 0xff, 0xff, 0xff, //0x000009d6 jmp          LBB0_119
	//0x000009db LBB0_128
	0x45, 0x31, 0xc0, //0x000009db xorl         %r8d, %r8d
	0x31, 0xf6, //0x000009de xorl         %esi, %esi
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x000009e0 movabsq      $-9223372036854775808, %rdi
	0x49, 0xba, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x000009ea movabsq      $4503599627370495, %r10
	0xe9, 0xe1, 0x18, 0x00, 0x00, //0x000009f4 jmp          LBB0_506
	//0x000009f9 LBB0_129
	0x45, 0x85, 0xc9, //0x000009f9 testl        %r9d, %r9d
	0x45, 0x0f, 0x44, 0xdf, //0x000009fc cmovel       %r15d, %r11d
	0x49, 0xba, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00000a00 movabsq      $4503599627370495, %r10
	0xe9, 0xec, 0x00, 0x00, 0x00, //0x00000a0a jmp          LBB0_148
	//0x00000a0f LBB0_130
	0x45, 0x85, 0xc9, //0x00000a0f testl        %r9d, %r9d
	0x45, 0x0f, 0x44, 0xdf, //0x00000a12 cmovel       %r15d, %r11d
	0xf6, 0xc2, 0x01, //0x00000a16 testb        $1, %dl
	0x0f, 0x84, 0x3e, 0x00, 0x00, 0x00, //0x00000a19 je           LBB0_136
	0x80, 0xcb, 0x20, //0x00000a1f orb          $32, %bl
	0x80, 0xfb, 0x65, //0x00000a22 cmpb         $101, %bl
	0x48, 0x8b, 0x5d, 0xa8, //0x00000a25 movq         $-88(%rbp), %rbx
	0x0f, 0x85, 0x2e, 0x00, 0x00, 0x00, //0x00000a29 jne          LBB0_136
	0x48, 0x8d, 0x57, 0x01, //0x00000a2f leaq         $1(%rdi), %rdx
	0x89, 0xd0, //0x00000a33 movl         %edx, %eax
	0x41, 0x8a, 0x04, 0x02, //0x00000a35 movb         (%r10,%rax), %al
	0x3c, 0x2b, //0x00000a39 cmpb         $43, %al
	0x0f, 0x84, 0x3e, 0x00, 0x00, 0x00, //0x00000a3b je           LBB0_139
	0x3c, 0x2d, //0x00000a41 cmpb         $45, %al
	0x48, 0x8b, 0x45, 0xc8, //0x00000a43 movq         $-56(%rbp), %rax
	0x0f, 0x85, 0x50, 0x00, 0x00, 0x00, //0x00000a47 jne          LBB0_140
	0x83, 0xc7, 0x02, //0x00000a4d addl         $2, %edi
	0x41, 0xb9, 0xff, 0xff, 0xff, 0xff, //0x00000a50 movl         $-1, %r9d
	0x89, 0xfa, //0x00000a56 movl         %edi, %edx
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x00000a58 jmp          LBB0_141
	//0x00000a5d LBB0_136
	0x49, 0xba, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00000a5d movabsq      $4503599627370495, %r10
	0xe9, 0x8f, 0x00, 0x00, 0x00, //0x00000a67 jmp          LBB0_148
	//0x00000a6c LBB0_137
	0x48, 0x89, 0xf8, //0x00000a6c movq         %rdi, %rax
	//0x00000a6f LBB0_138
	0x45, 0x0f, 0xaf, 0xca, //0x00000a6f imull        %r10d, %r9d
	0x41, 0x01, 0xc9, //0x00000a73 addl         %ecx, %r9d
	0x48, 0x89, 0x45, 0xa8, //0x00000a76 movq         %rax, $-88(%rbp)
	0xe9, 0x0d, 0xf9, 0xff, 0xff, //0x00000a7a jmp          LBB0_57
	//0x00000a7f LBB0_139
	0x83, 0xc7, 0x02, //0x00000a7f addl         $2, %edi
	0x41, 0xb9, 0x01, 0x00, 0x00, 0x00, //0x00000a82 movl         $1, %r9d
	0x89, 0xfa, //0x00000a88 movl         %edi, %edx
	0x49, 0xba, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00000a8a movabsq      $4503599627370495, %r10
	0x48, 0x8b, 0x45, 0xc8, //0x00000a94 movq         $-56(%rbp), %rax
	0xe9, 0x10, 0x00, 0x00, 0x00, //0x00000a98 jmp          LBB0_142
	//0x00000a9d LBB0_140
	0x41, 0xb9, 0x01, 0x00, 0x00, 0x00, //0x00000a9d movl         $1, %r9d
	//0x00000aa3 LBB0_141
	0x49, 0xba, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00000aa3 movabsq      $4503599627370495, %r10
	//0x00000aad LBB0_142
	0x48, 0x63, 0xfa, //0x00000aad movslq       %edx, %rdi
	0x31, 0xd2, //0x00000ab0 xorl         %edx, %edx
	0x48, 0x39, 0xf9, //0x00000ab2 cmpq         %rdi, %rcx
	0x0f, 0x8e, 0x36, 0x00, 0x00, 0x00, //0x00000ab5 jle          LBB0_147
	0x48, 0x01, 0xf8, //0x00000abb addq         %rdi, %rax
	0x31, 0xd2, //0x00000abe xorl         %edx, %edx
	//0x00000ac0 LBB0_144
	0x81, 0xfa, 0x0f, 0x27, 0x00, 0x00, //0x00000ac0 cmpl         $9999, %edx
	0x0f, 0x8f, 0x25, 0x00, 0x00, 0x00, //0x00000ac6 jg           LBB0_147
	0x41, 0x0f, 0xb6, 0x0c, 0x00, //0x00000acc movzbl       (%r8,%rax), %ecx
	0x8d, 0x71, 0xd0, //0x00000ad1 leal         $-48(%rcx), %esi
	0x40, 0x80, 0xfe, 0x09, //0x00000ad4 cmpb         $9, %sil
	0x0f, 0x87, 0x13, 0x00, 0x00, 0x00, //0x00000ad8 ja           LBB0_147
	0x8d, 0x14, 0x92, //0x00000ade leal         (%rdx,%rdx,4), %edx
	0x8d, 0x54, 0x51, 0xd0, //0x00000ae1 leal         $-48(%rcx,%rdx,2), %edx
	0x48, 0xff, 0xc0, //0x00000ae5 incq         %rax
	0x48, 0x39, 0xc3, //0x00000ae8 cmpq         %rax, %rbx
	0x0f, 0x85, 0xcf, 0xff, 0xff, 0xff, //0x00000aeb jne          LBB0_144
	//0x00000af1 LBB0_147
	0x41, 0x0f, 0xaf, 0xd1, //0x00000af1 imull        %r9d, %edx
	0x44, 0x01, 0xda, //0x00000af5 addl         %r11d, %edx
	0x41, 0x89, 0xd3, //0x00000af8 movl         %edx, %r11d
	//0x00000afb LBB0_148
	0x45, 0x85, 0xff, //0x00000afb testl        %r15d, %r15d
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00000afe movabsq      $-9223372036854775808, %rdi
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x00000b08 je           LBB0_151
	0x31, 0xf6, //0x00000b0e xorl         %esi, %esi
	0x41, 0x81, 0xfb, 0x36, 0x01, 0x00, 0x00, //0x00000b10 cmpl         $310, %r11d
	0x0f, 0x8e, 0x1f, 0x00, 0x00, 0x00, //0x00000b17 jle          LBB0_152
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, //0x00000b1d movabsq      $9218868437227405312, %r8
	0x8a, 0x55, 0xd7, //0x00000b27 movb         $-41(%rbp), %dl
	0xe9, 0xab, 0x17, 0x00, 0x00, //0x00000b2a jmp          LBB0_506
	//0x00000b2f LBB0_151
	0x45, 0x31, 0xc0, //0x00000b2f xorl         %r8d, %r8d
	0x31, 0xf6, //0x00000b32 xorl         %esi, %esi
	0x8a, 0x55, 0xd7, //0x00000b34 movb         $-41(%rbp), %dl
	0xe9, 0x9e, 0x17, 0x00, 0x00, //0x00000b37 jmp          LBB0_506
	//0x00000b3c LBB0_152
	0x41, 0x81, 0xfb, 0xb6, 0xfe, 0xff, 0xff, //0x00000b3c cmpl         $-330, %r11d
	0x0f, 0x8d, 0x0b, 0x00, 0x00, 0x00, //0x00000b43 jge          LBB0_154
	0x45, 0x31, 0xc0, //0x00000b49 xorl         %r8d, %r8d
	0x8a, 0x55, 0xd7, //0x00000b4c movb         $-41(%rbp), %dl
	0xe9, 0x86, 0x17, 0x00, 0x00, //0x00000b4f jmp          LBB0_506
	//0x00000b54 LBB0_154
	0x49, 0xba, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, //0x00000b54 movabsq      $1152921504606846975, %r10
	0x45, 0x85, 0xdb, //0x00000b5e testl        %r11d, %r11d
	0x0f, 0x8e, 0xd5, 0x07, 0x00, 0x00, //0x00000b61 jle          LBB0_270
	0x31, 0xf6, //0x00000b67 xorl         %esi, %esi
	0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x00000b69 movl         $1, %r8d
	0x44, 0x89, 0xf8, //0x00000b6f movl         %r15d, %eax
	0xe9, 0x14, 0x00, 0x00, 0x00, //0x00000b72 jmp          LBB0_158
	//0x00000b77 LBB0_156
	0x89, 0xc7, //0x00000b77 movl         %eax, %edi
	//0x00000b79 LBB0_157
	0x48, 0x8b, 0x75, 0xc0, //0x00000b79 movq         $-64(%rbp), %rsi
	0x03, 0x75, 0xbc, //0x00000b7d addl         $-68(%rbp), %esi
	0x89, 0xf8, //0x00000b80 movl         %edi, %eax
	0x45, 0x85, 0xdb, //0x00000b82 testl        %r11d, %r11d
	0x0f, 0x8e, 0xb4, 0x07, 0x00, 0x00, //0x00000b85 jle          LBB0_271
	//0x00000b8b LBB0_158
	0x48, 0x89, 0x75, 0xc0, //0x00000b8b movq         %rsi, $-64(%rbp)
	0xb9, 0x1b, 0x00, 0x00, 0x00, //0x00000b8f movl         $27, %ecx
	0x41, 0x83, 0xfb, 0x08, //0x00000b94 cmpl         $8, %r11d
	0x0f, 0x8f, 0x0d, 0x00, 0x00, 0x00, //0x00000b98 jg           LBB0_160
	0x44, 0x89, 0xd9, //0x00000b9e movl         %r11d, %ecx
	0x48, 0x8d, 0x15, 0xd8, 0x43, 0x00, 0x00, //0x00000ba1 leaq         $17368(%rip), %rdx  /* _POW_TAB+0(%rip) */
	0x8b, 0x0c, 0x8a, //0x00000ba8 movl         (%rdx,%rcx,4), %ecx
	//0x00000bab LBB0_160
	0x85, 0xc0, //0x00000bab testl        %eax, %eax
	0x89, 0x4d, 0xbc, //0x00000bad movl         %ecx, $-68(%rbp)
	0x0f, 0x84, 0xc1, 0xff, 0xff, 0xff, //0x00000bb0 je           LBB0_156
	0x41, 0x89, 0xc9, //0x00000bb6 movl         %ecx, %r9d
	0x41, 0xf7, 0xd9, //0x00000bb9 negl         %r9d
	0x85, 0xc9, //0x00000bbc testl        %ecx, %ecx
	0x0f, 0x84, 0xb3, 0xff, 0xff, 0xff, //0x00000bbe je           LBB0_156
	0x0f, 0x88, 0x90, 0x01, 0x00, 0x00, //0x00000bc4 js           LBB0_187
	//0x00000bca LBB0_163
	0x41, 0x83, 0xf9, 0xc3, //0x00000bca cmpl         $-61, %r9d
	0x0f, 0x8e, 0x21, 0x00, 0x00, 0x00, //0x00000bce jle          LBB0_167
	0xe9, 0xcd, 0x03, 0x00, 0x00, //0x00000bd4 jmp          LBB0_219
	//0x00000bd9 LBB0_164
	0xff, 0xc8, //0x00000bd9 decl         %eax
	0x41, 0x89, 0xc7, //0x00000bdb movl         %eax, %r15d
	//0x00000bde LBB0_165
	0x45, 0x85, 0xff, //0x00000bde testl        %r15d, %r15d
	0x45, 0x0f, 0x44, 0xdf, //0x00000be1 cmovel       %r15d, %r11d
	//0x00000be5 LBB0_166
	0x44, 0x8d, 0x49, 0x3c, //0x00000be5 leal         $60(%rcx), %r9d
	0x44, 0x89, 0xf8, //0x00000be9 movl         %r15d, %eax
	0x83, 0xf9, 0x88, //0x00000bec cmpl         $-120, %ecx
	0x0f, 0x8d, 0xa2, 0x03, 0x00, 0x00, //0x00000bef jge          LBB0_218
	//0x00000bf5 LBB0_167
	0x44, 0x89, 0xc9, //0x00000bf5 movl         %r9d, %ecx
	0x48, 0x63, 0xf8, //0x00000bf8 movslq       %eax, %rdi
	0x31, 0xf6, //0x00000bfb xorl         %esi, %esi
	0x31, 0xd2, //0x00000bfd xorl         %edx, %edx
	0x90, //0x00000bff .p2align 4, 0x90
	//0x00000c00 LBB0_168
	0x48, 0x39, 0xfe, //0x00000c00 cmpq         %rdi, %rsi
	0x0f, 0x8d, 0x27, 0x00, 0x00, 0x00, //0x00000c03 jge          LBB0_170
	0x48, 0x8d, 0x14, 0x92, //0x00000c09 leaq         (%rdx,%rdx,4), %rdx
	0x49, 0x0f, 0xbe, 0x1c, 0x34, //0x00000c0d movsbq       (%r12,%rsi), %rbx
	0x48, 0x8d, 0x54, 0x53, 0xd0, //0x00000c12 leaq         $-48(%rbx,%rdx,2), %rdx
	0x48, 0xff, 0xc6, //0x00000c17 incq         %rsi
	0x49, 0x8d, 0x5a, 0x01, //0x00000c1a leaq         $1(%r10), %rbx
	0x48, 0x39, 0xda, //0x00000c1e cmpq         %rbx, %rdx
	0x0f, 0x82, 0xd9, 0xff, 0xff, 0xff, //0x00000c21 jb           LBB0_168
	0xe9, 0x2a, 0x00, 0x00, 0x00, //0x00000c27 jmp          LBB0_172
	0x90, 0x90, 0x90, 0x90, //0x00000c2c .p2align 4, 0x90
	//0x00000c30 LBB0_170
	0x48, 0x85, 0xd2, //0x00000c30 testq        %rdx, %rdx
	0x0f, 0x84, 0x19, 0x01, 0x00, 0x00, //0x00000c33 je           LBB0_185
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000c39 .p2align 4, 0x90
	//0x00000c40 LBB0_171
	0x48, 0x01, 0xd2, //0x00000c40 addq         %rdx, %rdx
	0x48, 0x8d, 0x14, 0x92, //0x00000c43 leaq         (%rdx,%rdx,4), %rdx
	0xff, 0xc6, //0x00000c47 incl         %esi
	0x49, 0x8d, 0x7a, 0x01, //0x00000c49 leaq         $1(%r10), %rdi
	0x48, 0x39, 0xfa, //0x00000c4d cmpq         %rdi, %rdx
	0x0f, 0x82, 0xea, 0xff, 0xff, 0xff, //0x00000c50 jb           LBB0_171
	//0x00000c56 LBB0_172
	0x41, 0x29, 0xf3, //0x00000c56 subl         %esi, %r11d
	0x31, 0xff, //0x00000c59 xorl         %edi, %edi
	0x39, 0xc6, //0x00000c5b cmpl         %eax, %esi
	0x0f, 0x8d, 0x49, 0x00, 0x00, 0x00, //0x00000c5d jge          LBB0_177
	0x48, 0x63, 0xc6, //0x00000c63 movslq       %esi, %rax
	0x49, 0x63, 0xf7, //0x00000c66 movslq       %r15d, %rsi
	0x49, 0x8d, 0x3c, 0x04, //0x00000c69 leaq         (%r12,%rax), %rdi
	0x45, 0x31, 0xff, //0x00000c6d xorl         %r15d, %r15d
	//0x00000c70 .p2align 4, 0x90
	//0x00000c70 LBB0_174
	0x48, 0x89, 0xd3, //0x00000c70 movq         %rdx, %rbx
	0x48, 0xc1, 0xeb, 0x3c, //0x00000c73 shrq         $60, %rbx
	0x4c, 0x21, 0xd2, //0x00000c77 andq         %r10, %rdx
	0x80, 0xcb, 0x30, //0x00000c7a orb          $48, %bl
	0x43, 0x88, 0x1c, 0x3c, //0x00000c7d movb         %bl, (%r12,%r15)
	0x48, 0x8d, 0x14, 0x92, //0x00000c81 leaq         (%rdx,%rdx,4), %rdx
	0x4a, 0x0f, 0xbe, 0x1c, 0x3f, //0x00000c85 movsbq       (%rdi,%r15), %rbx
	0x48, 0x8d, 0x54, 0x53, 0xd0, //0x00000c8a leaq         $-48(%rbx,%rdx,2), %rdx
	0x4a, 0x8d, 0x5c, 0x38, 0x01, //0x00000c8f leaq         $1(%rax,%r15), %rbx
	0x49, 0xff, 0xc7, //0x00000c94 incq         %r15
	0x48, 0x39, 0xf3, //0x00000c97 cmpq         %rsi, %rbx
	0x0f, 0x8c, 0xd0, 0xff, 0xff, 0xff, //0x00000c9a jl           LBB0_174
	0x48, 0x85, 0xd2, //0x00000ca0 testq        %rdx, %rdx
	0x0f, 0x84, 0x5f, 0x00, 0x00, 0x00, //0x00000ca3 je           LBB0_181
	0x44, 0x89, 0xff, //0x00000ca9 movl         %r15d, %edi
	//0x00000cac LBB0_177
	0x41, 0x89, 0xff, //0x00000cac movl         %edi, %r15d
	0xe9, 0x23, 0x00, 0x00, 0x00, //0x00000caf jmp          LBB0_179
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000cb4 .p2align 4, 0x90
	//0x00000cc0 LBB0_178
	0x48, 0x85, 0xc0, //0x00000cc0 testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xf0, //0x00000cc3 cmovnel      %r8d, %r14d
	0x48, 0x01, 0xd2, //0x00000cc7 addq         %rdx, %rdx
	0x48, 0x8d, 0x14, 0x92, //0x00000cca leaq         (%rdx,%rdx,4), %rdx
	0x48, 0x85, 0xd2, //0x00000cce testq        %rdx, %rdx
	0x0f, 0x84, 0x31, 0x00, 0x00, 0x00, //0x00000cd1 je           LBB0_181
	//0x00000cd7 LBB0_179
	0x48, 0x89, 0xd0, //0x00000cd7 movq         %rdx, %rax
	0x48, 0xc1, 0xe8, 0x3c, //0x00000cda shrq         $60, %rax
	0x4c, 0x21, 0xd2, //0x00000cde andq         %r10, %rdx
	0x49, 0x63, 0xf7, //0x00000ce1 movslq       %r15d, %rsi
	0x49, 0x39, 0xf5, //0x00000ce4 cmpq         %rsi, %r13
	0x0f, 0x86, 0xd3, 0xff, 0xff, 0xff, //0x00000ce7 jbe          LBB0_178
	0x0c, 0x30, //0x00000ced orb          $48, %al
	0x41, 0x88, 0x04, 0x34, //0x00000cef movb         %al, (%r12,%rsi)
	0xff, 0xc6, //0x00000cf3 incl         %esi
	0x41, 0x89, 0xf7, //0x00000cf5 movl         %esi, %r15d
	0x48, 0x01, 0xd2, //0x00000cf8 addq         %rdx, %rdx
	0x48, 0x8d, 0x14, 0x92, //0x00000cfb leaq         (%rdx,%rdx,4), %rdx
	0x48, 0x85, 0xd2, //0x00000cff testq        %rdx, %rdx
	0x0f, 0x85, 0xcf, 0xff, 0xff, 0xff, //0x00000d02 jne          LBB0_179
	//0x00000d08 LBB0_181
	0x41, 0xff, 0xc3, //0x00000d08 incl         %r11d
	0x45, 0x85, 0xff, //0x00000d0b testl        %r15d, %r15d
	0x0f, 0x8e, 0xca, 0xfe, 0xff, 0xff, //0x00000d0e jle          LBB0_165
	0x44, 0x89, 0xf8, //0x00000d14 movl         %r15d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x00000d17 cmpb         $48, $-1(%rax,%r12)
	0x0f, 0x85, 0xc2, 0xfe, 0xff, 0xff, //0x00000d1d jne          LBB0_166
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000d23 .p2align 4, 0x90
	//0x00000d30 LBB0_183
	0x48, 0x83, 0xf8, 0x01, //0x00000d30 cmpq         $1, %rax
	0x0f, 0x8e, 0x9f, 0xfe, 0xff, 0xff, //0x00000d34 jle          LBB0_164
	0x4c, 0x8d, 0x78, 0xff, //0x00000d3a leaq         $-1(%rax), %r15
	0x41, 0x80, 0x7c, 0x04, 0xfe, 0x30, //0x00000d3e cmpb         $48, $-2(%r12,%rax)
	0x4c, 0x89, 0xf8, //0x00000d44 movq         %r15, %rax
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00000d47 je           LBB0_183
	0xe9, 0x93, 0xfe, 0xff, 0xff, //0x00000d4d jmp          LBB0_166
	//0x00000d52 LBB0_185
	0x45, 0x31, 0xff, //0x00000d52 xorl         %r15d, %r15d
	0xe9, 0x8b, 0xfe, 0xff, 0xff, //0x00000d55 jmp          LBB0_166
	//0x00000d5a LBB0_187
	0x83, 0xf9, 0xc3, //0x00000d5a cmpl         $-61, %ecx
	0x0f, 0x8f, 0xbe, 0x03, 0x00, 0x00, //0x00000d5d jg           LBB0_238
	0x48, 0x8d, 0x3d, 0x46, 0x42, 0x00, 0x00, //0x00000d63 leaq         $16966(%rip), %rdi  /* _LSHIFT_TAB+0(%rip) */
	0xe9, 0x1e, 0x00, 0x00, 0x00, //0x00000d6a jmp          LBB0_192
	//0x00000d6f LBB0_189
	0x48, 0x8d, 0x3d, 0x3a, 0x42, 0x00, 0x00, //0x00000d6f leaq         $16954(%rip), %rdi  /* _LSHIFT_TAB+0(%rip) */
	//0x00000d76 LBB0_190
	0x45, 0x85, 0xff, //0x00000d76 testl        %r15d, %r15d
	0x45, 0x0f, 0x44, 0xdf, //0x00000d79 cmovel       %r15d, %r11d
	//0x00000d7d LBB0_191
	0x44, 0x8d, 0x4b, 0xc4, //0x00000d7d leal         $-60(%rbx), %r9d
	0x44, 0x89, 0xf8, //0x00000d81 movl         %r15d, %eax
	0x83, 0xfb, 0x78, //0x00000d84 cmpl         $120, %ebx
	0x0f, 0x8e, 0x85, 0x03, 0x00, 0x00, //0x00000d87 jle          LBB0_237
	//0x00000d8d LBB0_192
	0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x00000d8d movl         $1, %r8d
	0x44, 0x89, 0xcb, //0x00000d93 movl         %r9d, %ebx
	0x48, 0x63, 0xf0, //0x00000d96 movslq       %eax, %rsi
	0x85, 0xf6, //0x00000d99 testl        %esi, %esi
	0x0f, 0x84, 0x45, 0x00, 0x00, 0x00, //0x00000d9b je           LBB0_198
	0xb2, 0x38, //0x00000da1 movb         $56, %dl
	0x31, 0xc9, //0x00000da3 xorl         %ecx, %ecx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000da5 .p2align 4, 0x90
	//0x00000db0 LBB0_194
	0x41, 0xb9, 0x13, 0x00, 0x00, 0x00, //0x00000db0 movl         $19, %r9d
	0x48, 0x83, 0xf9, 0x2a, //0x00000db6 cmpq         $42, %rcx
	0x0f, 0x84, 0x2c, 0x00, 0x00, 0x00, //0x00000dba je           LBB0_199
	0x41, 0x38, 0x14, 0x0c, //0x00000dc0 cmpb         %dl, (%r12,%rcx)
	0x0f, 0x85, 0xb8, 0x01, 0x00, 0x00, //0x00000dc4 jne          LBB0_216
	0x0f, 0xb6, 0x94, 0x39, 0x65, 0x18, 0x00, 0x00, //0x00000dca movzbl       $6245(%rcx,%rdi), %edx
	0x48, 0xff, 0xc1, //0x00000dd2 incq         %rcx
	0x48, 0x39, 0xce, //0x00000dd5 cmpq         %rcx, %rsi
	0x0f, 0x85, 0xd2, 0xff, 0xff, 0xff, //0x00000dd8 jne          LBB0_194
	0x84, 0xd2, //0x00000dde testb        %dl, %dl
	0x0f, 0x84, 0x06, 0x00, 0x00, 0x00, //0x00000de0 je           LBB0_199
	//0x00000de6 LBB0_198
	0x41, 0xb9, 0x12, 0x00, 0x00, 0x00, //0x00000de6 movl         $18, %r9d
	//0x00000dec LBB0_199
	0x85, 0xc0, //0x00000dec testl        %eax, %eax
	0x0f, 0x8e, 0xb7, 0x00, 0x00, 0x00, //0x00000dee jle          LBB0_207
	0x48, 0x89, 0x5d, 0xb0, //0x00000df4 movq         %rbx, $-80(%rbp)
	0x4c, 0x89, 0x5d, 0xc8, //0x00000df8 movq         %r11, $-56(%rbp)
	0x44, 0x01, 0xc8, //0x00000dfc addl         %r9d, %eax
	0x48, 0x98, //0x00000dff cltq         
	0x48, 0x89, 0xc3, //0x00000e01 movq         %rax, %rbx
	0x48, 0xc1, 0xe3, 0x20, //0x00000e04 shlq         $32, %rbx
	0x48, 0xff, 0xc8, //0x00000e08 decq         %rax
	0x48, 0xff, 0xc6, //0x00000e0b incq         %rsi
	0x31, 0xc9, //0x00000e0e xorl         %ecx, %ecx
	0xe9, 0x30, 0x00, 0x00, 0x00, //0x00000e10 jmp          LBB0_203
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000e15 .p2align 4, 0x90
	//0x00000e20 LBB0_201
	0x48, 0x85, 0xc0, //0x00000e20 testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xf0, //0x00000e23 cmovnel      %r8d, %r14d
	//0x00000e27 LBB0_202
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00000e27 movabsq      $-4294967296, %rax
	0x48, 0x01, 0xc3, //0x00000e31 addq         %rax, %rbx
	0x49, 0x8d, 0x43, 0xff, //0x00000e34 leaq         $-1(%r11), %rax
	0x48, 0xff, 0xce, //0x00000e38 decq         %rsi
	0x48, 0x83, 0xfe, 0x01, //0x00000e3b cmpq         $1, %rsi
	0x0f, 0x8e, 0x49, 0x00, 0x00, 0x00, //0x00000e3f jle          LBB0_205
	//0x00000e45 LBB0_203
	0x49, 0x89, 0xc3, //0x00000e45 movq         %rax, %r11
	0x41, 0x0f, 0xb6, 0x7c, 0x34, 0xfe, //0x00000e48 movzbl       $-2(%r12,%rsi), %edi
	0x48, 0xc1, 0xe7, 0x3c, //0x00000e4e shlq         $60, %rdi
	0x48, 0x01, 0xcf, //0x00000e52 addq         %rcx, %rdi
	0x48, 0x89, 0xf8, //0x00000e55 movq         %rdi, %rax
	0x48, 0xb9, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00000e58 movabsq      $-3689348814741910323, %rcx
	0x48, 0xf7, 0xe1, //0x00000e62 mulq         %rcx
	0x48, 0x89, 0xd1, //0x00000e65 movq         %rdx, %rcx
	0x48, 0xc1, 0xe9, 0x03, //0x00000e68 shrq         $3, %rcx
	0x48, 0x8d, 0x04, 0x09, //0x00000e6c leaq         (%rcx,%rcx), %rax
	0x48, 0x8d, 0x14, 0x80, //0x00000e70 leaq         (%rax,%rax,4), %rdx
	0x48, 0x89, 0xf8, //0x00000e74 movq         %rdi, %rax
	0x48, 0x29, 0xd0, //0x00000e77 subq         %rdx, %rax
	0x4d, 0x39, 0xeb, //0x00000e7a cmpq         %r13, %r11
	0x0f, 0x83, 0x9d, 0xff, 0xff, 0xff, //0x00000e7d jae          LBB0_201
	0x04, 0x30, //0x00000e83 addb         $48, %al
	0x43, 0x88, 0x04, 0x1c, //0x00000e85 movb         %al, (%r12,%r11)
	0xe9, 0x99, 0xff, 0xff, 0xff, //0x00000e89 jmp          LBB0_202
	//0x00000e8e LBB0_205
	0x48, 0x83, 0xff, 0x0a, //0x00000e8e cmpq         $10, %rdi
	0x0f, 0x83, 0x1e, 0x00, 0x00, 0x00, //0x00000e92 jae          LBB0_208
	0x4c, 0x8b, 0x5d, 0xc8, //0x00000e98 movq         $-56(%rbp), %r11
	0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x00000e9c movl         $1, %r8d
	0x48, 0x8b, 0x5d, 0xb0, //0x00000ea2 movq         $-80(%rbp), %rbx
	0xe9, 0x85, 0x00, 0x00, 0x00, //0x00000ea6 jmp          LBB0_212
	//0x00000eab LBB0_207
	0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x00000eab movl         $1, %r8d
	0xe9, 0x7a, 0x00, 0x00, 0x00, //0x00000eb1 jmp          LBB0_212
	//0x00000eb6 LBB0_208
	0x49, 0x63, 0xf3, //0x00000eb6 movslq       %r11d, %rsi
	0x48, 0xff, 0xce, //0x00000eb9 decq         %rsi
	0x4c, 0x8b, 0x5d, 0xc8, //0x00000ebc movq         $-56(%rbp), %r11
	0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x00000ec0 movl         $1, %r8d
	0x48, 0x8b, 0x5d, 0xb0, //0x00000ec6 movq         $-80(%rbp), %rbx
	0xe9, 0x18, 0x00, 0x00, 0x00, //0x00000eca jmp          LBB0_210
	0x90, //0x00000ecf .p2align 4, 0x90
	//0x00000ed0 LBB0_209
	0x48, 0x85, 0xc0, //0x00000ed0 testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xf0, //0x00000ed3 cmovnel      %r8d, %r14d
	0x48, 0xff, 0xce, //0x00000ed7 decq         %rsi
	0x48, 0x83, 0xf9, 0x09, //0x00000eda cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x00000ede movq         %rdx, %rcx
	0x0f, 0x86, 0x49, 0x00, 0x00, 0x00, //0x00000ee1 jbe          LBB0_212
	//0x00000ee7 LBB0_210
	0x48, 0x89, 0xc8, //0x00000ee7 movq         %rcx, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00000eea movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x00000ef4 mulq         %rdx
	0x48, 0xc1, 0xea, 0x03, //0x00000ef7 shrq         $3, %rdx
	0x48, 0x8d, 0x04, 0x12, //0x00000efb leaq         (%rdx,%rdx), %rax
	0x48, 0x8d, 0x3c, 0x80, //0x00000eff leaq         (%rax,%rax,4), %rdi
	0x48, 0x89, 0xc8, //0x00000f03 movq         %rcx, %rax
	0x48, 0x29, 0xf8, //0x00000f06 subq         %rdi, %rax
	0x4c, 0x39, 0xee, //0x00000f09 cmpq         %r13, %rsi
	0x0f, 0x83, 0xbe, 0xff, 0xff, 0xff, //0x00000f0c jae          LBB0_209
	0x04, 0x30, //0x00000f12 addb         $48, %al
	0x41, 0x88, 0x04, 0x34, //0x00000f14 movb         %al, (%r12,%rsi)
	0x48, 0xff, 0xce, //0x00000f18 decq         %rsi
	0x48, 0x83, 0xf9, 0x09, //0x00000f1b cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x00000f1f movq         %rdx, %rcx
	0x0f, 0x87, 0xbf, 0xff, 0xff, 0xff, //0x00000f22 ja           LBB0_210
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000f28 .p2align 4, 0x90
	//0x00000f30 LBB0_212
	0x45, 0x01, 0xcf, //0x00000f30 addl         %r9d, %r15d
	0x4d, 0x63, 0xff, //0x00000f33 movslq       %r15d, %r15
	0x4d, 0x39, 0xfd, //0x00000f36 cmpq         %r15, %r13
	0x45, 0x0f, 0x46, 0xfd, //0x00000f39 cmovbel      %r13d, %r15d
	0x45, 0x01, 0xcb, //0x00000f3d addl         %r9d, %r11d
	0x45, 0x85, 0xff, //0x00000f40 testl        %r15d, %r15d
	0x0f, 0x8e, 0x26, 0xfe, 0xff, 0xff, //0x00000f43 jle          LBB0_189
	0x44, 0x89, 0xf8, //0x00000f49 movl         %r15d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x00000f4c cmpb         $48, $-1(%rax,%r12)
	0x48, 0x8d, 0x3d, 0x57, 0x40, 0x00, 0x00, //0x00000f52 leaq         $16471(%rip), %rdi  /* _LSHIFT_TAB+0(%rip) */
	0x0f, 0x85, 0x1e, 0xfe, 0xff, 0xff, //0x00000f59 jne          LBB0_191
	0x90, //0x00000f5f .p2align 4, 0x90
	//0x00000f60 LBB0_214
	0x48, 0x83, 0xf8, 0x01, //0x00000f60 cmpq         $1, %rax
	0x0f, 0x8e, 0x23, 0x00, 0x00, 0x00, //0x00000f64 jle          LBB0_217
	0x4c, 0x8d, 0x78, 0xff, //0x00000f6a leaq         $-1(%rax), %r15
	0x41, 0x80, 0x7c, 0x04, 0xfe, 0x30, //0x00000f6e cmpb         $48, $-2(%r12,%rax)
	0x4c, 0x89, 0xf8, //0x00000f74 movq         %r15, %rax
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00000f77 je           LBB0_214
	0xe9, 0xfb, 0xfd, 0xff, 0xff, //0x00000f7d jmp          LBB0_191
	//0x00000f82 LBB0_216
	0x0f, 0x8c, 0x5e, 0xfe, 0xff, 0xff, //0x00000f82 jl           LBB0_198
	0xe9, 0x5f, 0xfe, 0xff, 0xff, //0x00000f88 jmp          LBB0_199
	//0x00000f8d LBB0_217
	0xff, 0xc8, //0x00000f8d decl         %eax
	0x41, 0x89, 0xc7, //0x00000f8f movl         %eax, %r15d
	0xe9, 0xdf, 0xfd, 0xff, 0xff, //0x00000f92 jmp          LBB0_190
	//0x00000f97 LBB0_218
	0x44, 0x89, 0xf8, //0x00000f97 movl         %r15d, %eax
	0x44, 0x89, 0xff, //0x00000f9a movl         %r15d, %edi
	0x45, 0x85, 0xc9, //0x00000f9d testl        %r9d, %r9d
	0x0f, 0x84, 0xd3, 0xfb, 0xff, 0xff, //0x00000fa0 je           LBB0_157
	//0x00000fa6 LBB0_219
	0x41, 0xf7, 0xd9, //0x00000fa6 negl         %r9d
	0x48, 0x63, 0xf0, //0x00000fa9 movslq       %eax, %rsi
	0x31, 0xff, //0x00000fac xorl         %edi, %edi
	0x31, 0xd2, //0x00000fae xorl         %edx, %edx
	//0x00000fb0 .p2align 4, 0x90
	//0x00000fb0 LBB0_220
	0x48, 0x39, 0xf7, //0x00000fb0 cmpq         %rsi, %rdi
	0x0f, 0x8d, 0x30, 0x01, 0x00, 0x00, //0x00000fb3 jge          LBB0_234
	0x48, 0x8d, 0x0c, 0x92, //0x00000fb9 leaq         (%rdx,%rdx,4), %rcx
	0x49, 0x0f, 0xbe, 0x14, 0x3c, //0x00000fbd movsbq       (%r12,%rdi), %rdx
	0x48, 0x8d, 0x54, 0x4a, 0xd0, //0x00000fc2 leaq         $-48(%rdx,%rcx,2), %rdx
	0x48, 0xff, 0xc7, //0x00000fc7 incq         %rdi
	0x48, 0x89, 0xd3, //0x00000fca movq         %rdx, %rbx
	0x44, 0x89, 0xc9, //0x00000fcd movl         %r9d, %ecx
	0x48, 0xd3, 0xeb, //0x00000fd0 shrq         %cl, %rbx
	0x48, 0x85, 0xdb, //0x00000fd3 testq        %rbx, %rbx
	0x0f, 0x84, 0xd4, 0xff, 0xff, 0xff, //0x00000fd6 je           LBB0_220
	//0x00000fdc LBB0_222
	0x48, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00000fdc movq         $-1, %rsi
	0x44, 0x89, 0xc9, //0x00000fe3 movl         %r9d, %ecx
	0x48, 0xd3, 0xe6, //0x00000fe6 shlq         %cl, %rsi
	0x48, 0xf7, 0xd6, //0x00000fe9 notq         %rsi
	0x31, 0xdb, //0x00000fec xorl         %ebx, %ebx
	0x39, 0xc7, //0x00000fee cmpl         %eax, %edi
	0x0f, 0x8d, 0x55, 0x00, 0x00, 0x00, //0x00000ff0 jge          LBB0_226
	0x4c, 0x89, 0x5d, 0xc8, //0x00000ff6 movq         %r11, $-56(%rbp)
	0x4c, 0x63, 0xdf, //0x00000ffa movslq       %edi, %r11
	0x4d, 0x63, 0xc7, //0x00000ffd movslq       %r15d, %r8
	0x4f, 0x8d, 0x3c, 0x1c, //0x00001000 leaq         (%r12,%r11), %r15
	0x31, 0xdb, //0x00001004 xorl         %ebx, %ebx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001006 .p2align 4, 0x90
	//0x00001010 LBB0_224
	0x48, 0x89, 0xd0, //0x00001010 movq         %rdx, %rax
	0x44, 0x89, 0xc9, //0x00001013 movl         %r9d, %ecx
	0x48, 0xd3, 0xe8, //0x00001016 shrq         %cl, %rax
	0x48, 0x21, 0xf2, //0x00001019 andq         %rsi, %rdx
	0x04, 0x30, //0x0000101c addb         $48, %al
	0x41, 0x88, 0x04, 0x1c, //0x0000101e movb         %al, (%r12,%rbx)
	0x48, 0x8d, 0x04, 0x92, //0x00001022 leaq         (%rdx,%rdx,4), %rax
	0x49, 0x0f, 0xbe, 0x0c, 0x1f, //0x00001026 movsbq       (%r15,%rbx), %rcx
	0x48, 0x8d, 0x54, 0x41, 0xd0, //0x0000102b leaq         $-48(%rcx,%rax,2), %rdx
	0x49, 0x8d, 0x44, 0x1b, 0x01, //0x00001030 leaq         $1(%r11,%rbx), %rax
	0x48, 0xff, 0xc3, //0x00001035 incq         %rbx
	0x4c, 0x39, 0xc0, //0x00001038 cmpq         %r8, %rax
	0x0f, 0x8c, 0xcf, 0xff, 0xff, 0xff, //0x0000103b jl           LBB0_224
	0x4c, 0x8b, 0x5d, 0xc8, //0x00001041 movq         $-56(%rbp), %r11
	0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x00001045 movl         $1, %r8d
	//0x0000104b LBB0_226
	0x41, 0x29, 0xfb, //0x0000104b subl         %edi, %r11d
	0x41, 0x89, 0xdf, //0x0000104e movl         %ebx, %r15d
	0x48, 0x85, 0xd2, //0x00001051 testq        %rdx, %rdx
	0x8b, 0x7d, 0xbc, //0x00001054 movl         $-68(%rbp), %edi
	0x0f, 0x85, 0x1c, 0x00, 0x00, 0x00, //0x00001057 jne          LBB0_228
	0xe9, 0x4a, 0x00, 0x00, 0x00, //0x0000105d jmp          LBB0_230
	//0x00001062 LBB0_227
	0x48, 0x85, 0xc0, //0x00001062 testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xf0, //0x00001065 cmovnel      %r8d, %r14d
	0x48, 0x01, 0xd2, //0x00001069 addq         %rdx, %rdx
	0x48, 0x8d, 0x14, 0x92, //0x0000106c leaq         (%rdx,%rdx,4), %rdx
	0x48, 0x85, 0xd2, //0x00001070 testq        %rdx, %rdx
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x00001073 je           LBB0_230
	//0x00001079 LBB0_228
	0x48, 0x89, 0xd0, //0x00001079 movq         %rdx, %rax
	0x44, 0x89, 0xc9, //0x0000107c movl         %r9d, %ecx
	0x48, 0xd3, 0xe8, //0x0000107f shrq         %cl, %rax
	0x48, 0x21, 0xf2, //0x00001082 andq         %rsi, %rdx
	0x49, 0x63, 0xcf, //0x00001085 movslq       %r15d, %rcx
	0x49, 0x39, 0xcd, //0x00001088 cmpq         %rcx, %r13
	0x0f, 0x86, 0xd1, 0xff, 0xff, 0xff, //0x0000108b jbe          LBB0_227
	0x04, 0x30, //0x00001091 addb         $48, %al
	0x41, 0x88, 0x04, 0x0c, //0x00001093 movb         %al, (%r12,%rcx)
	0xff, 0xc1, //0x00001097 incl         %ecx
	0x41, 0x89, 0xcf, //0x00001099 movl         %ecx, %r15d
	0x48, 0x01, 0xd2, //0x0000109c addq         %rdx, %rdx
	0x48, 0x8d, 0x14, 0x92, //0x0000109f leaq         (%rdx,%rdx,4), %rdx
	0x48, 0x85, 0xd2, //0x000010a3 testq        %rdx, %rdx
	0x0f, 0x85, 0xcd, 0xff, 0xff, 0xff, //0x000010a6 jne          LBB0_228
	//0x000010ac LBB0_230
	0x41, 0xff, 0xc3, //0x000010ac incl         %r11d
	0x45, 0x85, 0xff, //0x000010af testl        %r15d, %r15d
	0x0f, 0x8e, 0x44, 0x02, 0x00, 0x00, //0x000010b2 jle          LBB0_264
	0x44, 0x89, 0xf8, //0x000010b8 movl         %r15d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x000010bb cmpb         $48, $-1(%rax,%r12)
	0x0f, 0x85, 0x42, 0x02, 0x00, 0x00, //0x000010c1 jne          LBB0_265
	//0x000010c7 LBB0_232
	0x48, 0x83, 0xf8, 0x01, //0x000010c7 cmpq         $1, %rax
	0x0f, 0x8e, 0x26, 0x02, 0x00, 0x00, //0x000010cb jle          LBB0_263
	0x4c, 0x8d, 0x78, 0xff, //0x000010d1 leaq         $-1(%rax), %r15
	0x41, 0x80, 0x7c, 0x04, 0xfe, 0x30, //0x000010d5 cmpb         $48, $-2(%r12,%rax)
	0x4c, 0x89, 0xf8, //0x000010db movq         %r15, %rax
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x000010de je           LBB0_232
	0xe9, 0x20, 0x02, 0x00, 0x00, //0x000010e4 jmp          LBB0_265
	//0x000010e9 LBB0_234
	0x48, 0x85, 0xd2, //0x000010e9 testq        %rdx, %rdx
	0x0f, 0x84, 0x3c, 0x01, 0x00, 0x00, //0x000010ec je           LBB0_253
	//0x000010f2 LBB0_235
	0x48, 0x89, 0xd6, //0x000010f2 movq         %rdx, %rsi
	0x44, 0x89, 0xc9, //0x000010f5 movl         %r9d, %ecx
	0x48, 0xd3, 0xee, //0x000010f8 shrq         %cl, %rsi
	0x48, 0x85, 0xf6, //0x000010fb testq        %rsi, %rsi
	0x0f, 0x85, 0xd8, 0xfe, 0xff, 0xff, //0x000010fe jne          LBB0_222
	0x48, 0x01, 0xd2, //0x00001104 addq         %rdx, %rdx
	0x48, 0x8d, 0x14, 0x92, //0x00001107 leaq         (%rdx,%rdx,4), %rdx
	0xff, 0xc7, //0x0000110b incl         %edi
	0xe9, 0xe0, 0xff, 0xff, 0xff, //0x0000110d jmp          LBB0_235
	//0x00001112 LBB0_237
	0x44, 0x89, 0xf8, //0x00001112 movl         %r15d, %eax
	0x44, 0x89, 0xff, //0x00001115 movl         %r15d, %edi
	0x45, 0x85, 0xc9, //0x00001118 testl        %r9d, %r9d
	0x0f, 0x84, 0x58, 0xfa, 0xff, 0xff, //0x0000111b je           LBB0_157
	//0x00001121 LBB0_238
	0x44, 0x89, 0xc9, //0x00001121 movl         %r9d, %ecx
	0x48, 0x6b, 0xf1, 0x68, //0x00001124 imulq        $104, %rcx, %rsi
	0x48, 0x8d, 0x3d, 0x81, 0x3e, 0x00, 0x00, //0x00001128 leaq         $16001(%rip), %rdi  /* _LSHIFT_TAB+0(%rip) */
	0x8b, 0x1c, 0x3e, //0x0000112f movl         (%rsi,%rdi), %ebx
	0x4c, 0x63, 0xc0, //0x00001132 movslq       %eax, %r8
	0x8a, 0x54, 0x3e, 0x04, //0x00001135 movb         $4(%rsi,%rdi), %dl
	0x45, 0x85, 0xc0, //0x00001139 testl        %r8d, %r8d
	0x0f, 0x84, 0x29, 0x00, 0x00, 0x00, //0x0000113c je           LBB0_243
	0x48, 0x8d, 0x74, 0x3e, 0x05, //0x00001142 leaq         $5(%rsi,%rdi), %rsi
	0x31, 0xff, //0x00001147 xorl         %edi, %edi
	//0x00001149 LBB0_240
	0x84, 0xd2, //0x00001149 testb        %dl, %dl
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x0000114b je           LBB0_245
	0x41, 0x38, 0x14, 0x3c, //0x00001151 cmpb         %dl, (%r12,%rdi)
	0x0f, 0x85, 0xb6, 0x01, 0x00, 0x00, //0x00001155 jne          LBB0_266
	0x0f, 0xb6, 0x14, 0x3e, //0x0000115b movzbl       (%rsi,%rdi), %edx
	0x48, 0xff, 0xc7, //0x0000115f incq         %rdi
	0x49, 0x39, 0xf8, //0x00001162 cmpq         %rdi, %r8
	0x0f, 0x85, 0xde, 0xff, 0xff, 0xff, //0x00001165 jne          LBB0_240
	//0x0000116b LBB0_243
	0x84, 0xd2, //0x0000116b testb        %dl, %dl
	0x0f, 0x84, 0x02, 0x00, 0x00, 0x00, //0x0000116d je           LBB0_245
	//0x00001173 LBB0_244
	0xff, 0xcb, //0x00001173 decl         %ebx
	//0x00001175 LBB0_245
	0x85, 0xc0, //0x00001175 testl        %eax, %eax
	0x0f, 0x8e, 0xbb, 0x00, 0x00, 0x00, //0x00001177 jle          LBB0_254
	0x4c, 0x89, 0x5d, 0xc8, //0x0000117d movq         %r11, $-56(%rbp)
	0x89, 0x5d, 0xb0, //0x00001181 movl         %ebx, $-80(%rbp)
	0x01, 0xd8, //0x00001184 addl         %ebx, %eax
	0x48, 0x98, //0x00001186 cltq         
	0x48, 0x89, 0xc6, //0x00001188 movq         %rax, %rsi
	0x48, 0xc1, 0xe6, 0x20, //0x0000118b shlq         $32, %rsi
	0x48, 0xff, 0xc8, //0x0000118f decq         %rax
	0x49, 0xff, 0xc0, //0x00001192 incq         %r8
	0x31, 0xff, //0x00001195 xorl         %edi, %edi
	0xe9, 0x2a, 0x00, 0x00, 0x00, //0x00001197 jmp          LBB0_249
	//0x0000119c LBB0_247
	0x48, 0x85, 0xc0, //0x0000119c testq        %rax, %rax
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x0000119f movl         $1, %eax
	0x44, 0x0f, 0x45, 0xf0, //0x000011a4 cmovnel      %eax, %r14d
	//0x000011a8 LBB0_248
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x000011a8 movabsq      $-4294967296, %rax
	0x48, 0x01, 0xc6, //0x000011b2 addq         %rax, %rsi
	0x49, 0x8d, 0x43, 0xff, //0x000011b5 leaq         $-1(%r11), %rax
	0x49, 0xff, 0xc8, //0x000011b9 decq         %r8
	0x49, 0x83, 0xf8, 0x01, //0x000011bc cmpq         $1, %r8
	0x0f, 0x8e, 0x4c, 0x00, 0x00, 0x00, //0x000011c0 jle          LBB0_251
	//0x000011c6 LBB0_249
	0x49, 0x89, 0xc3, //0x000011c6 movq         %rax, %r11
	0x4b, 0x0f, 0xbe, 0x5c, 0x04, 0xfe, //0x000011c9 movsbq       $-2(%r12,%r8), %rbx
	0x48, 0x83, 0xc3, 0xd0, //0x000011cf addq         $-48, %rbx
	0x48, 0xd3, 0xe3, //0x000011d3 shlq         %cl, %rbx
	0x48, 0x01, 0xfb, //0x000011d6 addq         %rdi, %rbx
	0x48, 0x89, 0xd8, //0x000011d9 movq         %rbx, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x000011dc movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x000011e6 mulq         %rdx
	0x48, 0x89, 0xd7, //0x000011e9 movq         %rdx, %rdi
	0x48, 0xc1, 0xef, 0x03, //0x000011ec shrq         $3, %rdi
	0x48, 0x8d, 0x04, 0x3f, //0x000011f0 leaq         (%rdi,%rdi), %rax
	0x48, 0x8d, 0x14, 0x80, //0x000011f4 leaq         (%rax,%rax,4), %rdx
	0x48, 0x89, 0xd8, //0x000011f8 movq         %rbx, %rax
	0x48, 0x29, 0xd0, //0x000011fb subq         %rdx, %rax
	0x4d, 0x39, 0xeb, //0x000011fe cmpq         %r13, %r11
	0x0f, 0x83, 0x95, 0xff, 0xff, 0xff, //0x00001201 jae          LBB0_247
	0x04, 0x30, //0x00001207 addb         $48, %al
	0x43, 0x88, 0x04, 0x1c, //0x00001209 movb         %al, (%r12,%r11)
	0xe9, 0x96, 0xff, 0xff, 0xff, //0x0000120d jmp          LBB0_248
	//0x00001212 LBB0_251
	0x48, 0x83, 0xfb, 0x0a, //0x00001212 cmpq         $10, %rbx
	0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x00001216 movl         $1, %r8d
	0x0f, 0x83, 0x21, 0x00, 0x00, 0x00, //0x0000121c jae          LBB0_255
	0x4c, 0x8b, 0x5d, 0xc8, //0x00001222 movq         $-56(%rbp), %r11
	0x8b, 0x5d, 0xb0, //0x00001226 movl         $-80(%rbp), %ebx
	0xe9, 0x7f, 0x00, 0x00, 0x00, //0x00001229 jmp          LBB0_259
	//0x0000122e LBB0_253
	0x45, 0x31, 0xff, //0x0000122e xorl         %r15d, %r15d
	0x31, 0xff, //0x00001231 xorl         %edi, %edi
	0xe9, 0x41, 0xf9, 0xff, 0xff, //0x00001233 jmp          LBB0_157
	//0x00001238 LBB0_254
	0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x00001238 movl         $1, %r8d
	0xe9, 0x6a, 0x00, 0x00, 0x00, //0x0000123e jmp          LBB0_259
	//0x00001243 LBB0_255
	0x49, 0x63, 0xcb, //0x00001243 movslq       %r11d, %rcx
	0x48, 0xff, 0xc9, //0x00001246 decq         %rcx
	0x4c, 0x8b, 0x5d, 0xc8, //0x00001249 movq         $-56(%rbp), %r11
	0x8b, 0x5d, 0xb0, //0x0000124d movl         $-80(%rbp), %ebx
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00001250 jmp          LBB0_257
	//0x00001255 LBB0_256
	0x48, 0x85, 0xc0, //0x00001255 testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xf0, //0x00001258 cmovnel      %r8d, %r14d
	0x48, 0xff, 0xc9, //0x0000125c decq         %rcx
	0x48, 0x83, 0xff, 0x09, //0x0000125f cmpq         $9, %rdi
	0x48, 0x89, 0xd7, //0x00001263 movq         %rdx, %rdi
	0x0f, 0x86, 0x41, 0x00, 0x00, 0x00, //0x00001266 jbe          LBB0_259
	//0x0000126c LBB0_257
	0x48, 0x89, 0xf8, //0x0000126c movq         %rdi, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x0000126f movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x00001279 mulq         %rdx
	0x48, 0xc1, 0xea, 0x03, //0x0000127c shrq         $3, %rdx
	0x48, 0x8d, 0x04, 0x12, //0x00001280 leaq         (%rdx,%rdx), %rax
	0x48, 0x8d, 0x34, 0x80, //0x00001284 leaq         (%rax,%rax,4), %rsi
	0x48, 0x89, 0xf8, //0x00001288 movq         %rdi, %rax
	0x48, 0x29, 0xf0, //0x0000128b subq         %rsi, %rax
	0x4c, 0x39, 0xe9, //0x0000128e cmpq         %r13, %rcx
	0x0f, 0x83, 0xbe, 0xff, 0xff, 0xff, //0x00001291 jae          LBB0_256
	0x04, 0x30, //0x00001297 addb         $48, %al
	0x41, 0x88, 0x04, 0x0c, //0x00001299 movb         %al, (%r12,%rcx)
	0x48, 0xff, 0xc9, //0x0000129d decq         %rcx
	0x48, 0x83, 0xff, 0x09, //0x000012a0 cmpq         $9, %rdi
	0x48, 0x89, 0xd7, //0x000012a4 movq         %rdx, %rdi
	0x0f, 0x87, 0xbf, 0xff, 0xff, 0xff, //0x000012a7 ja           LBB0_257
	//0x000012ad LBB0_259
	0x41, 0x01, 0xdf, //0x000012ad addl         %ebx, %r15d
	0x4d, 0x63, 0xff, //0x000012b0 movslq       %r15d, %r15
	0x4d, 0x39, 0xfd, //0x000012b3 cmpq         %r15, %r13
	0x45, 0x0f, 0x46, 0xfd, //0x000012b6 cmovbel      %r13d, %r15d
	0x41, 0x01, 0xdb, //0x000012ba addl         %ebx, %r11d
	0x45, 0x85, 0xff, //0x000012bd testl        %r15d, %r15d
	0x0f, 0x8e, 0x5b, 0x00, 0x00, 0x00, //0x000012c0 jle          LBB0_268
	0x44, 0x89, 0xf8, //0x000012c6 movl         %r15d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x000012c9 cmpb         $48, $-1(%rax,%r12)
	0x0f, 0x85, 0x53, 0x00, 0x00, 0x00, //0x000012cf jne          LBB0_269
	//0x000012d5 LBB0_261
	0x48, 0x83, 0xf8, 0x01, //0x000012d5 cmpq         $1, %rax
	0x0f, 0x8e, 0x3d, 0x00, 0x00, 0x00, //0x000012d9 jle          LBB0_267
	0x4c, 0x8d, 0x78, 0xff, //0x000012df leaq         $-1(%rax), %r15
	0x41, 0x80, 0x7c, 0x04, 0xfe, 0x30, //0x000012e3 cmpb         $48, $-2(%r12,%rax)
	0x4c, 0x89, 0xf8, //0x000012e9 movq         %r15, %rax
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x000012ec je           LBB0_261
	0xe9, 0x31, 0x00, 0x00, 0x00, //0x000012f2 jmp          LBB0_269
	//0x000012f7 LBB0_263
	0xff, 0xc8, //0x000012f7 decl         %eax
	0x41, 0x89, 0xc7, //0x000012f9 movl         %eax, %r15d
	//0x000012fc LBB0_264
	0x48, 0x8b, 0x45, 0xc0, //0x000012fc movq         $-64(%rbp), %rax
	0x45, 0x85, 0xff, //0x00001300 testl        %r15d, %r15d
	0x0f, 0x84, 0x04, 0x0c, 0x00, 0x00, //0x00001303 je           LBB0_444
	//0x00001309 LBB0_265
	0x44, 0x89, 0xff, //0x00001309 movl         %r15d, %edi
	0xe9, 0x68, 0xf8, 0xff, 0xff, //0x0000130c jmp          LBB0_157
	//0x00001311 LBB0_266
	0x0f, 0x8c, 0x5c, 0xfe, 0xff, 0xff, //0x00001311 jl           LBB0_244
	0xe9, 0x59, 0xfe, 0xff, 0xff, //0x00001317 jmp          LBB0_245
	//0x0000131c LBB0_267
	0xff, 0xc8, //0x0000131c decl         %eax
	0x41, 0x89, 0xc7, //0x0000131e movl         %eax, %r15d
	//0x00001321 LBB0_268
	0x45, 0x85, 0xff, //0x00001321 testl        %r15d, %r15d
	0x45, 0x0f, 0x44, 0xdf, //0x00001324 cmovel       %r15d, %r11d
	//0x00001328 LBB0_269
	0x44, 0x89, 0xf8, //0x00001328 movl         %r15d, %eax
	0x44, 0x89, 0xff, //0x0000132b movl         %r15d, %edi
	0x45, 0x85, 0xc9, //0x0000132e testl        %r9d, %r9d
	0x0f, 0x89, 0x42, 0xf8, 0xff, 0xff, //0x00001331 jns          LBB0_157
	0xe9, 0x8e, 0xf8, 0xff, 0xff, //0x00001337 jmp          LBB0_163
	//0x0000133c LBB0_270
	0x44, 0x89, 0xff, //0x0000133c movl         %r15d, %edi
	//0x0000133f LBB0_271
	0x4c, 0x8d, 0x05, 0x6a, 0x3c, 0x00, 0x00, //0x0000133f leaq         $15466(%rip), %r8  /* _LSHIFT_TAB+0(%rip) */
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x00001346 jmp          LBB0_274
	//0x0000134b LBB0_272
	0x89, 0xf8, //0x0000134b movl         %edi, %eax
	//0x0000134d LBB0_273
	0x48, 0x8b, 0x75, 0xc0, //0x0000134d movq         $-64(%rbp), %rsi
	0x2b, 0x75, 0xb0, //0x00001351 subl         $-80(%rbp), %esi
	0x89, 0xc7, //0x00001354 movl         %eax, %edi
	//0x00001356 LBB0_274
	0x45, 0x85, 0xdb, //0x00001356 testl        %r11d, %r11d
	0x0f, 0x88, 0x16, 0x00, 0x00, 0x00, //0x00001359 js           LBB0_277
	0x0f, 0x85, 0xcd, 0x07, 0x00, 0x00, //0x0000135f jne          LBB0_389
	0x41, 0x80, 0x3c, 0x24, 0x35, //0x00001365 cmpb         $53, (%r12)
	0x0f, 0x8c, 0x14, 0x00, 0x00, 0x00, //0x0000136a jl           LBB0_278
	0xe9, 0xbd, 0x07, 0x00, 0x00, //0x00001370 jmp          LBB0_389
	//0x00001375 LBB0_277
	0xb8, 0x1b, 0x00, 0x00, 0x00, //0x00001375 movl         $27, %eax
	0x41, 0x83, 0xfb, 0xf8, //0x0000137a cmpl         $-8, %r11d
	0x0f, 0x8c, 0x11, 0x00, 0x00, 0x00, //0x0000137e jl           LBB0_279
	//0x00001384 LBB0_278
	0x44, 0x89, 0xd8, //0x00001384 movl         %r11d, %eax
	0xf7, 0xd8, //0x00001387 negl         %eax
	0x48, 0x98, //0x00001389 cltq         
	0x48, 0x8d, 0x0d, 0xee, 0x3b, 0x00, 0x00, //0x0000138b leaq         $15342(%rip), %rcx  /* _POW_TAB+0(%rip) */
	0x8b, 0x04, 0x81, //0x00001392 movl         (%rcx,%rax,4), %eax
	//0x00001395 LBB0_279
	0x85, 0xff, //0x00001395 testl        %edi, %edi
	0x48, 0x89, 0x75, 0xc0, //0x00001397 movq         %rsi, $-64(%rbp)
	0x89, 0x45, 0xb0, //0x0000139b movl         %eax, $-80(%rbp)
	0x0f, 0x84, 0xa7, 0xff, 0xff, 0xff, //0x0000139e je           LBB0_272
	0x85, 0xc0, //0x000013a4 testl        %eax, %eax
	0x0f, 0x84, 0x9f, 0xff, 0xff, 0xff, //0x000013a6 je           LBB0_272
	0x0f, 0x8e, 0x3b, 0x02, 0x00, 0x00, //0x000013ac jle          LBB0_311
	0x41, 0x89, 0xc1, //0x000013b2 movl         %eax, %r9d
	0x83, 0xf8, 0x3d, //0x000013b5 cmpl         $61, %eax
	0x0f, 0x8d, 0x21, 0x00, 0x00, 0x00, //0x000013b8 jge          LBB0_286
	0xe9, 0x41, 0x02, 0x00, 0x00, //0x000013be jmp          LBB0_313
	//0x000013c3 LBB0_283
	0xff, 0xc8, //0x000013c3 decl         %eax
	0x41, 0x89, 0xc7, //0x000013c5 movl         %eax, %r15d
	//0x000013c8 LBB0_284
	0x45, 0x85, 0xff, //0x000013c8 testl        %r15d, %r15d
	0x45, 0x0f, 0x44, 0xdf, //0x000013cb cmovel       %r15d, %r11d
	//0x000013cf LBB0_285
	0x44, 0x8d, 0x4b, 0xc4, //0x000013cf leal         $-60(%rbx), %r9d
	0x44, 0x89, 0xff, //0x000013d3 movl         %r15d, %edi
	0x83, 0xfb, 0x78, //0x000013d6 cmpl         $120, %ebx
	0x0f, 0x8e, 0x16, 0x02, 0x00, 0x00, //0x000013d9 jle          LBB0_312
	//0x000013df LBB0_286
	0x44, 0x89, 0xcb, //0x000013df movl         %r9d, %ebx
	0x48, 0x63, 0xf7, //0x000013e2 movslq       %edi, %rsi
	0x85, 0xf6, //0x000013e5 testl        %esi, %esi
	0x0f, 0x84, 0x4a, 0x00, 0x00, 0x00, //0x000013e7 je           LBB0_292
	0xb1, 0x38, //0x000013ed movb         $56, %cl
	0x31, 0xc0, //0x000013ef xorl         %eax, %eax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000013f1 .p2align 4, 0x90
	//0x00001400 LBB0_288
	0x41, 0xb9, 0x13, 0x00, 0x00, 0x00, //0x00001400 movl         $19, %r9d
	0x48, 0x83, 0xf8, 0x2a, //0x00001406 cmpq         $42, %rax
	0x0f, 0x84, 0x2d, 0x00, 0x00, 0x00, //0x0000140a je           LBB0_293
	0x41, 0x38, 0x0c, 0x04, //0x00001410 cmpb         %cl, (%r12,%rax)
	0x0f, 0x85, 0xc8, 0x01, 0x00, 0x00, //0x00001414 jne          LBB0_309
	0x42, 0x0f, 0xb6, 0x8c, 0x00, 0x65, 0x18, 0x00, 0x00, //0x0000141a movzbl       $6245(%rax,%r8), %ecx
	0x48, 0xff, 0xc0, //0x00001423 incq         %rax
	0x48, 0x39, 0xc6, //0x00001426 cmpq         %rax, %rsi
	0x0f, 0x85, 0xd1, 0xff, 0xff, 0xff, //0x00001429 jne          LBB0_288
	0x84, 0xc9, //0x0000142f testb        %cl, %cl
	0x0f, 0x84, 0x06, 0x00, 0x00, 0x00, //0x00001431 je           LBB0_293
	//0x00001437 LBB0_292
	0x41, 0xb9, 0x12, 0x00, 0x00, 0x00, //0x00001437 movl         $18, %r9d
	//0x0000143d LBB0_293
	0x85, 0xff, //0x0000143d testl        %edi, %edi
	0x0f, 0x8e, 0x4b, 0x01, 0x00, 0x00, //0x0000143f jle          LBB0_305
	0x48, 0x89, 0x5d, 0xc8, //0x00001445 movq         %rbx, $-56(%rbp)
	0x4d, 0x89, 0xd8, //0x00001449 movq         %r11, %r8
	0x44, 0x01, 0xcf, //0x0000144c addl         %r9d, %edi
	0x48, 0x63, 0xc7, //0x0000144f movslq       %edi, %rax
	0x48, 0x89, 0xc3, //0x00001452 movq         %rax, %rbx
	0x48, 0xc1, 0xe3, 0x20, //0x00001455 shlq         $32, %rbx
	0x48, 0xff, 0xc8, //0x00001459 decq         %rax
	0x48, 0xff, 0xc6, //0x0000145c incq         %rsi
	0x31, 0xc9, //0x0000145f xorl         %ecx, %ecx
	0xe9, 0x34, 0x00, 0x00, 0x00, //0x00001461 jmp          LBB0_297
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001466 .p2align 4, 0x90
	//0x00001470 LBB0_295
	0x48, 0x85, 0xc0, //0x00001470 testq        %rax, %rax
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00001473 movl         $1, %eax
	0x44, 0x0f, 0x45, 0xf0, //0x00001478 cmovnel      %eax, %r14d
	//0x0000147c LBB0_296
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x0000147c movabsq      $-4294967296, %rax
	0x48, 0x01, 0xc3, //0x00001486 addq         %rax, %rbx
	0x49, 0x8d, 0x43, 0xff, //0x00001489 leaq         $-1(%r11), %rax
	0x48, 0xff, 0xce, //0x0000148d decq         %rsi
	0x48, 0x83, 0xfe, 0x01, //0x00001490 cmpq         $1, %rsi
	0x0f, 0x8e, 0x56, 0x00, 0x00, 0x00, //0x00001494 jle          LBB0_299
	//0x0000149a LBB0_297
	0x49, 0x89, 0xc3, //0x0000149a movq         %rax, %r11
	0x41, 0x0f, 0xb6, 0x7c, 0x34, 0xfe, //0x0000149d movzbl       $-2(%r12,%rsi), %edi
	0x48, 0xc1, 0xe7, 0x3c, //0x000014a3 shlq         $60, %rdi
	0x48, 0x01, 0xcf, //0x000014a7 addq         %rcx, %rdi
	0x48, 0x89, 0xf8, //0x000014aa movq         %rdi, %rax
	0x48, 0xb9, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x000014ad movabsq      $-3689348814741910323, %rcx
	0x48, 0xf7, 0xe1, //0x000014b7 mulq         %rcx
	0x48, 0x89, 0xd1, //0x000014ba movq         %rdx, %rcx
	0x48, 0xc1, 0xe9, 0x03, //0x000014bd shrq         $3, %rcx
	0x48, 0x8d, 0x04, 0x09, //0x000014c1 leaq         (%rcx,%rcx), %rax
	0x48, 0x8d, 0x14, 0x80, //0x000014c5 leaq         (%rax,%rax,4), %rdx
	0x48, 0x89, 0xf8, //0x000014c9 movq         %rdi, %rax
	0x48, 0x29, 0xd0, //0x000014cc subq         %rdx, %rax
	0x4d, 0x39, 0xeb, //0x000014cf cmpq         %r13, %r11
	0x0f, 0x83, 0x98, 0xff, 0xff, 0xff, //0x000014d2 jae          LBB0_295
	0x04, 0x30, //0x000014d8 addb         $48, %al
	0x43, 0x88, 0x04, 0x1c, //0x000014da movb         %al, (%r12,%r11)
	0xe9, 0x99, 0xff, 0xff, 0xff, //0x000014de jmp          LBB0_296
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000014e3 .p2align 4, 0x90
	//0x000014f0 LBB0_299
	0x48, 0x83, 0xff, 0x0a, //0x000014f0 cmpq         $10, %rdi
	0x0f, 0x83, 0x13, 0x00, 0x00, 0x00, //0x000014f4 jae          LBB0_301
	0x4d, 0x89, 0xc3, //0x000014fa movq         %r8, %r11
	0x4c, 0x8d, 0x05, 0xac, 0x3a, 0x00, 0x00, //0x000014fd leaq         $15020(%rip), %r8  /* _LSHIFT_TAB+0(%rip) */
	0x48, 0x8b, 0x5d, 0xc8, //0x00001504 movq         $-56(%rbp), %rbx
	0xe9, 0x83, 0x00, 0x00, 0x00, //0x00001508 jmp          LBB0_305
	//0x0000150d LBB0_301
	0x49, 0x63, 0xf3, //0x0000150d movslq       %r11d, %rsi
	0x48, 0xff, 0xce, //0x00001510 decq         %rsi
	0x4d, 0x89, 0xc3, //0x00001513 movq         %r8, %r11
	0x4c, 0x8d, 0x05, 0x93, 0x3a, 0x00, 0x00, //0x00001516 leaq         $14995(%rip), %r8  /* _LSHIFT_TAB+0(%rip) */
	0x48, 0x8b, 0x5d, 0xc8, //0x0000151d movq         $-56(%rbp), %rbx
	0xe9, 0x26, 0x00, 0x00, 0x00, //0x00001521 jmp          LBB0_303
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001526 .p2align 4, 0x90
	//0x00001530 LBB0_302
	0x48, 0x85, 0xc0, //0x00001530 testq        %rax, %rax
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00001533 movl         $1, %eax
	0x44, 0x0f, 0x45, 0xf0, //0x00001538 cmovnel      %eax, %r14d
	0x48, 0xff, 0xce, //0x0000153c decq         %rsi
	0x48, 0x83, 0xf9, 0x09, //0x0000153f cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x00001543 movq         %rdx, %rcx
	0x0f, 0x86, 0x44, 0x00, 0x00, 0x00, //0x00001546 jbe          LBB0_305
	//0x0000154c LBB0_303
	0x48, 0x89, 0xc8, //0x0000154c movq         %rcx, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x0000154f movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x00001559 mulq         %rdx
	0x48, 0xc1, 0xea, 0x03, //0x0000155c shrq         $3, %rdx
	0x48, 0x8d, 0x04, 0x12, //0x00001560 leaq         (%rdx,%rdx), %rax
	0x48, 0x8d, 0x3c, 0x80, //0x00001564 leaq         (%rax,%rax,4), %rdi
	0x48, 0x89, 0xc8, //0x00001568 movq         %rcx, %rax
	0x48, 0x29, 0xf8, //0x0000156b subq         %rdi, %rax
	0x4c, 0x39, 0xee, //0x0000156e cmpq         %r13, %rsi
	0x0f, 0x83, 0xb9, 0xff, 0xff, 0xff, //0x00001571 jae          LBB0_302
	0x04, 0x30, //0x00001577 addb         $48, %al
	0x41, 0x88, 0x04, 0x34, //0x00001579 movb         %al, (%r12,%rsi)
	0x48, 0xff, 0xce, //0x0000157d decq         %rsi
	0x48, 0x83, 0xf9, 0x09, //0x00001580 cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x00001584 movq         %rdx, %rcx
	0x0f, 0x87, 0xbf, 0xff, 0xff, 0xff, //0x00001587 ja           LBB0_303
	0x90, 0x90, 0x90, //0x0000158d .p2align 4, 0x90
	//0x00001590 LBB0_305
	0x45, 0x01, 0xcf, //0x00001590 addl         %r9d, %r15d
	0x4d, 0x63, 0xff, //0x00001593 movslq       %r15d, %r15
	0x4d, 0x39, 0xfd, //0x00001596 cmpq         %r15, %r13
	0x45, 0x0f, 0x46, 0xfd, //0x00001599 cmovbel      %r13d, %r15d
	0x45, 0x01, 0xcb, //0x0000159d addl         %r9d, %r11d
	0x45, 0x85, 0xff, //0x000015a0 testl        %r15d, %r15d
	0x0f, 0x8e, 0x1f, 0xfe, 0xff, 0xff, //0x000015a3 jle          LBB0_284
	0x44, 0x89, 0xf8, //0x000015a9 movl         %r15d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x000015ac cmpb         $48, $-1(%rax,%r12)
	0x0f, 0x85, 0x17, 0xfe, 0xff, 0xff, //0x000015b2 jne          LBB0_285
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000015b8 .p2align 4, 0x90
	//0x000015c0 LBB0_307
	0x48, 0x83, 0xf8, 0x01, //0x000015c0 cmpq         $1, %rax
	0x0f, 0x8e, 0xf9, 0xfd, 0xff, 0xff, //0x000015c4 jle          LBB0_283
	0x4c, 0x8d, 0x78, 0xff, //0x000015ca leaq         $-1(%rax), %r15
	0x41, 0x80, 0x7c, 0x04, 0xfe, 0x30, //0x000015ce cmpb         $48, $-2(%r12,%rax)
	0x4c, 0x89, 0xf8, //0x000015d4 movq         %r15, %rax
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x000015d7 je           LBB0_307
	0xe9, 0xed, 0xfd, 0xff, 0xff, //0x000015dd jmp          LBB0_285
	//0x000015e2 LBB0_309
	0x0f, 0x8c, 0x4f, 0xfe, 0xff, 0xff, //0x000015e2 jl           LBB0_292
	0xe9, 0x50, 0xfe, 0xff, 0xff, //0x000015e8 jmp          LBB0_293
	//0x000015ed LBB0_311
	0x41, 0x89, 0xc1, //0x000015ed movl         %eax, %r9d
	0xe9, 0x09, 0x02, 0x00, 0x00, //0x000015f0 jmp          LBB0_341
	//0x000015f5 LBB0_312
	0x44, 0x89, 0xff, //0x000015f5 movl         %r15d, %edi
	0x44, 0x89, 0xf8, //0x000015f8 movl         %r15d, %eax
	0x45, 0x85, 0xc9, //0x000015fb testl        %r9d, %r9d
	0x0f, 0x84, 0x49, 0xfd, 0xff, 0xff, //0x000015fe je           LBB0_273
	//0x00001604 LBB0_313
	0x44, 0x89, 0xc9, //0x00001604 movl         %r9d, %ecx
	0x48, 0x6b, 0xd1, 0x68, //0x00001607 imulq        $104, %rcx, %rdx
	0x42, 0x8b, 0x1c, 0x02, //0x0000160b movl         (%rdx,%r8), %ebx
	0x4c, 0x89, 0xc6, //0x0000160f movq         %r8, %rsi
	0x4c, 0x63, 0xc7, //0x00001612 movslq       %edi, %r8
	0x8a, 0x44, 0x32, 0x04, //0x00001615 movb         $4(%rdx,%rsi), %al
	0x45, 0x85, 0xc0, //0x00001619 testl        %r8d, %r8d
	0x0f, 0x84, 0x29, 0x00, 0x00, 0x00, //0x0000161c je           LBB0_318
	0x48, 0x8d, 0x54, 0x32, 0x05, //0x00001622 leaq         $5(%rdx,%rsi), %rdx
	0x31, 0xf6, //0x00001627 xorl         %esi, %esi
	//0x00001629 LBB0_315
	0x84, 0xc0, //0x00001629 testb        %al, %al
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x0000162b je           LBB0_320
	0x41, 0x38, 0x04, 0x34, //0x00001631 cmpb         %al, (%r12,%rsi)
	0x0f, 0x85, 0x9d, 0x01, 0x00, 0x00, //0x00001635 jne          LBB0_337
	0x0f, 0xb6, 0x04, 0x32, //0x0000163b movzbl       (%rdx,%rsi), %eax
	0x48, 0xff, 0xc6, //0x0000163f incq         %rsi
	0x49, 0x39, 0xf0, //0x00001642 cmpq         %rsi, %r8
	0x0f, 0x85, 0xde, 0xff, 0xff, 0xff, //0x00001645 jne          LBB0_315
	//0x0000164b LBB0_318
	0x84, 0xc0, //0x0000164b testb        %al, %al
	0x0f, 0x84, 0x02, 0x00, 0x00, 0x00, //0x0000164d je           LBB0_320
	//0x00001653 LBB0_319
	0xff, 0xcb, //0x00001653 decl         %ebx
	//0x00001655 LBB0_320
	0x85, 0xff, //0x00001655 testl        %edi, %edi
	0x0f, 0x8e, 0xb6, 0x00, 0x00, 0x00, //0x00001657 jle          LBB0_328
	0x4c, 0x89, 0x5d, 0xc8, //0x0000165d movq         %r11, $-56(%rbp)
	0x89, 0x5d, 0xbc, //0x00001661 movl         %ebx, $-68(%rbp)
	0x01, 0xdf, //0x00001664 addl         %ebx, %edi
	0x48, 0x63, 0xc7, //0x00001666 movslq       %edi, %rax
	0x48, 0x89, 0xc6, //0x00001669 movq         %rax, %rsi
	0x48, 0xc1, 0xe6, 0x20, //0x0000166c shlq         $32, %rsi
	0x48, 0xff, 0xc8, //0x00001670 decq         %rax
	0x49, 0xff, 0xc0, //0x00001673 incq         %r8
	0x31, 0xff, //0x00001676 xorl         %edi, %edi
	0xe9, 0x2d, 0x00, 0x00, 0x00, //0x00001678 jmp          LBB0_324
	0x90, 0x90, 0x90, //0x0000167d .p2align 4, 0x90
	//0x00001680 LBB0_322
	0x48, 0x85, 0xc0, //0x00001680 testq        %rax, %rax
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00001683 movl         $1, %eax
	0x44, 0x0f, 0x45, 0xf0, //0x00001688 cmovnel      %eax, %r14d
	//0x0000168c LBB0_323
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x0000168c movabsq      $-4294967296, %rax
	0x48, 0x01, 0xc6, //0x00001696 addq         %rax, %rsi
	0x49, 0x8d, 0x43, 0xff, //0x00001699 leaq         $-1(%r11), %rax
	0x49, 0xff, 0xc8, //0x0000169d decq         %r8
	0x49, 0x83, 0xf8, 0x01, //0x000016a0 cmpq         $1, %r8
	0x0f, 0x8e, 0x4c, 0x00, 0x00, 0x00, //0x000016a4 jle          LBB0_326
	//0x000016aa LBB0_324
	0x49, 0x89, 0xc3, //0x000016aa movq         %rax, %r11
	0x4b, 0x0f, 0xbe, 0x5c, 0x04, 0xfe, //0x000016ad movsbq       $-2(%r12,%r8), %rbx
	0x48, 0x83, 0xc3, 0xd0, //0x000016b3 addq         $-48, %rbx
	0x48, 0xd3, 0xe3, //0x000016b7 shlq         %cl, %rbx
	0x48, 0x01, 0xfb, //0x000016ba addq         %rdi, %rbx
	0x48, 0x89, 0xd8, //0x000016bd movq         %rbx, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x000016c0 movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x000016ca mulq         %rdx
	0x48, 0x89, 0xd7, //0x000016cd movq         %rdx, %rdi
	0x48, 0xc1, 0xef, 0x03, //0x000016d0 shrq         $3, %rdi
	0x48, 0x8d, 0x04, 0x3f, //0x000016d4 leaq         (%rdi,%rdi), %rax
	0x48, 0x8d, 0x14, 0x80, //0x000016d8 leaq         (%rax,%rax,4), %rdx
	0x48, 0x89, 0xd8, //0x000016dc movq         %rbx, %rax
	0x48, 0x29, 0xd0, //0x000016df subq         %rdx, %rax
	0x4d, 0x39, 0xeb, //0x000016e2 cmpq         %r13, %r11
	0x0f, 0x83, 0x95, 0xff, 0xff, 0xff, //0x000016e5 jae          LBB0_322
	0x04, 0x30, //0x000016eb addb         $48, %al
	0x43, 0x88, 0x04, 0x1c, //0x000016ed movb         %al, (%r12,%r11)
	0xe9, 0x96, 0xff, 0xff, 0xff, //0x000016f1 jmp          LBB0_323
	//0x000016f6 LBB0_326
	0x48, 0x83, 0xfb, 0x0a, //0x000016f6 cmpq         $10, %rbx
	0x4c, 0x8d, 0x05, 0xaf, 0x38, 0x00, 0x00, //0x000016fa leaq         $14511(%rip), %r8  /* _LSHIFT_TAB+0(%rip) */
	0x0f, 0x83, 0x18, 0x00, 0x00, 0x00, //0x00001701 jae          LBB0_329
	0x4c, 0x8b, 0x5d, 0xc8, //0x00001707 movq         $-56(%rbp), %r11
	0x8b, 0x5d, 0xbc, //0x0000170b movl         $-68(%rbp), %ebx
	0xe9, 0x7b, 0x00, 0x00, 0x00, //0x0000170e jmp          LBB0_333
	//0x00001713 LBB0_328
	0x4c, 0x8d, 0x05, 0x96, 0x38, 0x00, 0x00, //0x00001713 leaq         $14486(%rip), %r8  /* _LSHIFT_TAB+0(%rip) */
	0xe9, 0x6f, 0x00, 0x00, 0x00, //0x0000171a jmp          LBB0_333
	//0x0000171f LBB0_329
	0x49, 0x63, 0xcb, //0x0000171f movslq       %r11d, %rcx
	0x48, 0xff, 0xc9, //0x00001722 decq         %rcx
	0x4c, 0x8b, 0x5d, 0xc8, //0x00001725 movq         $-56(%rbp), %r11
	0x8b, 0x5d, 0xbc, //0x00001729 movl         $-68(%rbp), %ebx
	0xe9, 0x1c, 0x00, 0x00, 0x00, //0x0000172c jmp          LBB0_331
	//0x00001731 LBB0_330
	0x48, 0x85, 0xc0, //0x00001731 testq        %rax, %rax
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00001734 movl         $1, %eax
	0x44, 0x0f, 0x45, 0xf0, //0x00001739 cmovnel      %eax, %r14d
	0x48, 0xff, 0xc9, //0x0000173d decq         %rcx
	0x48, 0x83, 0xff, 0x09, //0x00001740 cmpq         $9, %rdi
	0x48, 0x89, 0xd7, //0x00001744 movq         %rdx, %rdi
	0x0f, 0x86, 0x41, 0x00, 0x00, 0x00, //0x00001747 jbe          LBB0_333
	//0x0000174d LBB0_331
	0x48, 0x89, 0xf8, //0x0000174d movq         %rdi, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00001750 movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x0000175a mulq         %rdx
	0x48, 0xc1, 0xea, 0x03, //0x0000175d shrq         $3, %rdx
	0x48, 0x8d, 0x04, 0x12, //0x00001761 leaq         (%rdx,%rdx), %rax
	0x48, 0x8d, 0x34, 0x80, //0x00001765 leaq         (%rax,%rax,4), %rsi
	0x48, 0x89, 0xf8, //0x00001769 movq         %rdi, %rax
	0x48, 0x29, 0xf0, //0x0000176c subq         %rsi, %rax
	0x4c, 0x39, 0xe9, //0x0000176f cmpq         %r13, %rcx
	0x0f, 0x83, 0xb9, 0xff, 0xff, 0xff, //0x00001772 jae          LBB0_330
	0x04, 0x30, //0x00001778 addb         $48, %al
	0x41, 0x88, 0x04, 0x0c, //0x0000177a movb         %al, (%r12,%rcx)
	0x48, 0xff, 0xc9, //0x0000177e decq         %rcx
	0x48, 0x83, 0xff, 0x09, //0x00001781 cmpq         $9, %rdi
	0x48, 0x89, 0xd7, //0x00001785 movq         %rdx, %rdi
	0x0f, 0x87, 0xbf, 0xff, 0xff, 0xff, //0x00001788 ja           LBB0_331
	//0x0000178e LBB0_333
	0x41, 0x01, 0xdf, //0x0000178e addl         %ebx, %r15d
	0x4d, 0x63, 0xff, //0x00001791 movslq       %r15d, %r15
	0x4d, 0x39, 0xfd, //0x00001794 cmpq         %r15, %r13
	0x45, 0x0f, 0x46, 0xfd, //0x00001797 cmovbel      %r13d, %r15d
	0x41, 0x01, 0xdb, //0x0000179b addl         %ebx, %r11d
	0x45, 0x85, 0xff, //0x0000179e testl        %r15d, %r15d
	0x0f, 0x8e, 0x41, 0x00, 0x00, 0x00, //0x000017a1 jle          LBB0_339
	0x44, 0x89, 0xf8, //0x000017a7 movl         %r15d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x000017aa cmpb         $48, $-1(%rax,%r12)
	0x0f, 0x85, 0x39, 0x00, 0x00, 0x00, //0x000017b0 jne          LBB0_340
	//0x000017b6 LBB0_335
	0x48, 0x83, 0xf8, 0x01, //0x000017b6 cmpq         $1, %rax
	0x0f, 0x8e, 0x23, 0x00, 0x00, 0x00, //0x000017ba jle          LBB0_338
	0x4c, 0x8d, 0x78, 0xff, //0x000017c0 leaq         $-1(%rax), %r15
	0x41, 0x80, 0x7c, 0x04, 0xfe, 0x30, //0x000017c4 cmpb         $48, $-2(%r12,%rax)
	0x4c, 0x89, 0xf8, //0x000017ca movq         %r15, %rax
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x000017cd je           LBB0_335
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x000017d3 jmp          LBB0_340
	//0x000017d8 LBB0_337
	0x0f, 0x8c, 0x75, 0xfe, 0xff, 0xff, //0x000017d8 jl           LBB0_319
	0xe9, 0x72, 0xfe, 0xff, 0xff, //0x000017de jmp          LBB0_320
	//0x000017e3 LBB0_338
	0xff, 0xc8, //0x000017e3 decl         %eax
	0x41, 0x89, 0xc7, //0x000017e5 movl         %eax, %r15d
	//0x000017e8 LBB0_339
	0x45, 0x85, 0xff, //0x000017e8 testl        %r15d, %r15d
	0x45, 0x0f, 0x44, 0xdf, //0x000017eb cmovel       %r15d, %r11d
	//0x000017ef LBB0_340
	0x44, 0x89, 0xff, //0x000017ef movl         %r15d, %edi
	0x44, 0x89, 0xf8, //0x000017f2 movl         %r15d, %eax
	0x45, 0x85, 0xc9, //0x000017f5 testl        %r9d, %r9d
	0x0f, 0x89, 0x4f, 0xfb, 0xff, 0xff, //0x000017f8 jns          LBB0_273
	//0x000017fe LBB0_341
	0x41, 0x83, 0xf9, 0xc3, //0x000017fe cmpl         $-61, %r9d
	0x0f, 0x8e, 0x21, 0x00, 0x00, 0x00, //0x00001802 jle          LBB0_345
	0xe9, 0x9c, 0x01, 0x00, 0x00, //0x00001808 jmp          LBB0_365
	//0x0000180d LBB0_342
	0xff, 0xc9, //0x0000180d decl         %ecx
	0x41, 0x89, 0xcf, //0x0000180f movl         %ecx, %r15d
	//0x00001812 LBB0_343
	0x45, 0x85, 0xff, //0x00001812 testl        %r15d, %r15d
	0x45, 0x0f, 0x44, 0xdf, //0x00001815 cmovel       %r15d, %r11d
	//0x00001819 LBB0_344
	0x44, 0x8d, 0x48, 0x3c, //0x00001819 leal         $60(%rax), %r9d
	0x44, 0x89, 0xff, //0x0000181d movl         %r15d, %edi
	0x83, 0xf8, 0x88, //0x00001820 cmpl         $-120, %eax
	0x0f, 0x8d, 0x71, 0x01, 0x00, 0x00, //0x00001823 jge          LBB0_364
	//0x00001829 LBB0_345
	0x44, 0x89, 0xc8, //0x00001829 movl         %r9d, %eax
	0x48, 0x63, 0xf7, //0x0000182c movslq       %edi, %rsi
	0x31, 0xd2, //0x0000182f xorl         %edx, %edx
	0x31, 0xc9, //0x00001831 xorl         %ecx, %ecx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001833 .p2align 4, 0x90
	//0x00001840 LBB0_346
	0x48, 0x39, 0xf2, //0x00001840 cmpq         %rsi, %rdx
	0x0f, 0x8d, 0x27, 0x00, 0x00, 0x00, //0x00001843 jge          LBB0_348
	0x48, 0x8d, 0x0c, 0x89, //0x00001849 leaq         (%rcx,%rcx,4), %rcx
	0x49, 0x0f, 0xbe, 0x1c, 0x14, //0x0000184d movsbq       (%r12,%rdx), %rbx
	0x48, 0x8d, 0x4c, 0x4b, 0xd0, //0x00001852 leaq         $-48(%rbx,%rcx,2), %rcx
	0x48, 0xff, 0xc2, //0x00001857 incq         %rdx
	0x49, 0x8d, 0x5a, 0x01, //0x0000185a leaq         $1(%r10), %rbx
	0x48, 0x39, 0xd9, //0x0000185e cmpq         %rbx, %rcx
	0x0f, 0x82, 0xd9, 0xff, 0xff, 0xff, //0x00001861 jb           LBB0_346
	0xe9, 0x2a, 0x00, 0x00, 0x00, //0x00001867 jmp          LBB0_350
	0x90, 0x90, 0x90, 0x90, //0x0000186c .p2align 4, 0x90
	//0x00001870 LBB0_348
	0x48, 0x85, 0xc9, //0x00001870 testq        %rcx, %rcx
	0x0f, 0x84, 0x19, 0x01, 0x00, 0x00, //0x00001873 je           LBB0_363
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001879 .p2align 4, 0x90
	//0x00001880 LBB0_349
	0x48, 0x01, 0xc9, //0x00001880 addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x00001883 leaq         (%rcx,%rcx,4), %rcx
	0xff, 0xc2, //0x00001887 incl         %edx
	0x49, 0x8d, 0x72, 0x01, //0x00001889 leaq         $1(%r10), %rsi
	0x48, 0x39, 0xf1, //0x0000188d cmpq         %rsi, %rcx
	0x0f, 0x82, 0xea, 0xff, 0xff, 0xff, //0x00001890 jb           LBB0_349
	//0x00001896 LBB0_350
	0x41, 0x29, 0xd3, //0x00001896 subl         %edx, %r11d
	0x31, 0xf6, //0x00001899 xorl         %esi, %esi
	0x39, 0xfa, //0x0000189b cmpl         %edi, %edx
	0x0f, 0x8d, 0x49, 0x00, 0x00, 0x00, //0x0000189d jge          LBB0_355
	0x48, 0x63, 0xd2, //0x000018a3 movslq       %edx, %rdx
	0x49, 0x63, 0xf7, //0x000018a6 movslq       %r15d, %rsi
	0x49, 0x8d, 0x3c, 0x14, //0x000018a9 leaq         (%r12,%rdx), %rdi
	0x45, 0x31, 0xff, //0x000018ad xorl         %r15d, %r15d
	//0x000018b0 .p2align 4, 0x90
	//0x000018b0 LBB0_352
	0x48, 0x89, 0xcb, //0x000018b0 movq         %rcx, %rbx
	0x48, 0xc1, 0xeb, 0x3c, //0x000018b3 shrq         $60, %rbx
	0x4c, 0x21, 0xd1, //0x000018b7 andq         %r10, %rcx
	0x80, 0xcb, 0x30, //0x000018ba orb          $48, %bl
	0x43, 0x88, 0x1c, 0x3c, //0x000018bd movb         %bl, (%r12,%r15)
	0x48, 0x8d, 0x0c, 0x89, //0x000018c1 leaq         (%rcx,%rcx,4), %rcx
	0x4a, 0x0f, 0xbe, 0x1c, 0x3f, //0x000018c5 movsbq       (%rdi,%r15), %rbx
	0x48, 0x8d, 0x4c, 0x4b, 0xd0, //0x000018ca leaq         $-48(%rbx,%rcx,2), %rcx
	0x4a, 0x8d, 0x5c, 0x3a, 0x01, //0x000018cf leaq         $1(%rdx,%r15), %rbx
	0x49, 0xff, 0xc7, //0x000018d4 incq         %r15
	0x48, 0x39, 0xf3, //0x000018d7 cmpq         %rsi, %rbx
	0x0f, 0x8c, 0xd0, 0xff, 0xff, 0xff, //0x000018da jl           LBB0_352
	0x48, 0x85, 0xc9, //0x000018e0 testq        %rcx, %rcx
	0x0f, 0x84, 0x65, 0x00, 0x00, 0x00, //0x000018e3 je           LBB0_359
	0x44, 0x89, 0xfe, //0x000018e9 movl         %r15d, %esi
	//0x000018ec LBB0_355
	0x41, 0x89, 0xf7, //0x000018ec movl         %esi, %r15d
	0xe9, 0x28, 0x00, 0x00, 0x00, //0x000018ef jmp          LBB0_357
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000018f4 .p2align 4, 0x90
	//0x00001900 LBB0_356
	0x48, 0x85, 0xd2, //0x00001900 testq        %rdx, %rdx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00001903 movl         $1, %edx
	0x44, 0x0f, 0x45, 0xf2, //0x00001908 cmovnel      %edx, %r14d
	0x48, 0x01, 0xc9, //0x0000190c addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x0000190f leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xc9, //0x00001913 testq        %rcx, %rcx
	0x0f, 0x84, 0x32, 0x00, 0x00, 0x00, //0x00001916 je           LBB0_359
	//0x0000191c LBB0_357
	0x48, 0x89, 0xca, //0x0000191c movq         %rcx, %rdx
	0x48, 0xc1, 0xea, 0x3c, //0x0000191f shrq         $60, %rdx
	0x4c, 0x21, 0xd1, //0x00001923 andq         %r10, %rcx
	0x49, 0x63, 0xf7, //0x00001926 movslq       %r15d, %rsi
	0x49, 0x39, 0xf5, //0x00001929 cmpq         %rsi, %r13
	0x0f, 0x86, 0xce, 0xff, 0xff, 0xff, //0x0000192c jbe          LBB0_356
	0x80, 0xca, 0x30, //0x00001932 orb          $48, %dl
	0x41, 0x88, 0x14, 0x34, //0x00001935 movb         %dl, (%r12,%rsi)
	0xff, 0xc6, //0x00001939 incl         %esi
	0x41, 0x89, 0xf7, //0x0000193b movl         %esi, %r15d
	0x48, 0x01, 0xc9, //0x0000193e addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x00001941 leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xc9, //0x00001945 testq        %rcx, %rcx
	0x0f, 0x85, 0xce, 0xff, 0xff, 0xff, //0x00001948 jne          LBB0_357
	//0x0000194e LBB0_359
	0x41, 0xff, 0xc3, //0x0000194e incl         %r11d
	0x45, 0x85, 0xff, //0x00001951 testl        %r15d, %r15d
	0x0f, 0x8e, 0xb8, 0xfe, 0xff, 0xff, //0x00001954 jle          LBB0_343
	0x44, 0x89, 0xf9, //0x0000195a movl         %r15d, %ecx
	0x42, 0x80, 0x7c, 0x21, 0xff, 0x30, //0x0000195d cmpb         $48, $-1(%rcx,%r12)
	0x0f, 0x85, 0xb0, 0xfe, 0xff, 0xff, //0x00001963 jne          LBB0_344
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001969 .p2align 4, 0x90
	//0x00001970 LBB0_361
	0x48, 0x83, 0xf9, 0x01, //0x00001970 cmpq         $1, %rcx
	0x0f, 0x8e, 0x93, 0xfe, 0xff, 0xff, //0x00001974 jle          LBB0_342
	0x4c, 0x8d, 0x79, 0xff, //0x0000197a leaq         $-1(%rcx), %r15
	0x41, 0x80, 0x7c, 0x0c, 0xfe, 0x30, //0x0000197e cmpb         $48, $-2(%r12,%rcx)
	0x4c, 0x89, 0xf9, //0x00001984 movq         %r15, %rcx
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00001987 je           LBB0_361
	0xe9, 0x87, 0xfe, 0xff, 0xff, //0x0000198d jmp          LBB0_344
	//0x00001992 LBB0_363
	0x45, 0x31, 0xff, //0x00001992 xorl         %r15d, %r15d
	0xe9, 0x7f, 0xfe, 0xff, 0xff, //0x00001995 jmp          LBB0_344
	//0x0000199a LBB0_364
	0x44, 0x89, 0xff, //0x0000199a movl         %r15d, %edi
	0x44, 0x89, 0xf8, //0x0000199d movl         %r15d, %eax
	0x45, 0x85, 0xc9, //0x000019a0 testl        %r9d, %r9d
	0x0f, 0x84, 0xa4, 0xf9, 0xff, 0xff, //0x000019a3 je           LBB0_273
	//0x000019a9 LBB0_365
	0x41, 0xf7, 0xd9, //0x000019a9 negl         %r9d
	0x48, 0x63, 0xf7, //0x000019ac movslq       %edi, %rsi
	0x31, 0xd2, //0x000019af xorl         %edx, %edx
	0x31, 0xc0, //0x000019b1 xorl         %eax, %eax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000019b3 .p2align 4, 0x90
	//0x000019c0 LBB0_366
	0x48, 0x39, 0xf2, //0x000019c0 cmpq         %rsi, %rdx
	0x0f, 0x8d, 0x22, 0x01, 0x00, 0x00, //0x000019c3 jge          LBB0_382
	0x48, 0x8d, 0x04, 0x80, //0x000019c9 leaq         (%rax,%rax,4), %rax
	0x49, 0x0f, 0xbe, 0x0c, 0x14, //0x000019cd movsbq       (%r12,%rdx), %rcx
	0x48, 0x8d, 0x44, 0x41, 0xd0, //0x000019d2 leaq         $-48(%rcx,%rax,2), %rax
	0x48, 0xff, 0xc2, //0x000019d7 incq         %rdx
	0x48, 0x89, 0xc3, //0x000019da movq         %rax, %rbx
	0x44, 0x89, 0xc9, //0x000019dd movl         %r9d, %ecx
	0x48, 0xd3, 0xeb, //0x000019e0 shrq         %cl, %rbx
	0x48, 0x85, 0xdb, //0x000019e3 testq        %rbx, %rbx
	0x0f, 0x84, 0xd4, 0xff, 0xff, 0xff, //0x000019e6 je           LBB0_366
	//0x000019ec LBB0_368
	0x48, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x000019ec movq         $-1, %rsi
	0x44, 0x89, 0xc9, //0x000019f3 movl         %r9d, %ecx
	0x48, 0xd3, 0xe6, //0x000019f6 shlq         %cl, %rsi
	0x48, 0xf7, 0xd6, //0x000019f9 notq         %rsi
	0x31, 0xdb, //0x000019fc xorl         %ebx, %ebx
	0x39, 0xfa, //0x000019fe cmpl         %edi, %edx
	0x0f, 0x8d, 0x58, 0x00, 0x00, 0x00, //0x00001a00 jge          LBB0_372
	0x4c, 0x89, 0x5d, 0xc8, //0x00001a06 movq         %r11, $-56(%rbp)
	0x4c, 0x63, 0xda, //0x00001a0a movslq       %edx, %r11
	0x4d, 0x63, 0xc7, //0x00001a0d movslq       %r15d, %r8
	0x4f, 0x8d, 0x3c, 0x1c, //0x00001a10 leaq         (%r12,%r11), %r15
	0x31, 0xdb, //0x00001a14 xorl         %ebx, %ebx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001a16 .p2align 4, 0x90
	//0x00001a20 LBB0_370
	0x48, 0x89, 0xc7, //0x00001a20 movq         %rax, %rdi
	0x44, 0x89, 0xc9, //0x00001a23 movl         %r9d, %ecx
	0x48, 0xd3, 0xef, //0x00001a26 shrq         %cl, %rdi
	0x48, 0x21, 0xf0, //0x00001a29 andq         %rsi, %rax
	0x40, 0x80, 0xc7, 0x30, //0x00001a2c addb         $48, %dil
	0x41, 0x88, 0x3c, 0x1c, //0x00001a30 movb         %dil, (%r12,%rbx)
	0x48, 0x8d, 0x04, 0x80, //0x00001a34 leaq         (%rax,%rax,4), %rax
	0x49, 0x0f, 0xbe, 0x0c, 0x1f, //0x00001a38 movsbq       (%r15,%rbx), %rcx
	0x48, 0x8d, 0x44, 0x41, 0xd0, //0x00001a3d leaq         $-48(%rcx,%rax,2), %rax
	0x49, 0x8d, 0x4c, 0x1b, 0x01, //0x00001a42 leaq         $1(%r11,%rbx), %rcx
	0x48, 0xff, 0xc3, //0x00001a47 incq         %rbx
	0x4c, 0x39, 0xc1, //0x00001a4a cmpq         %r8, %rcx
	0x0f, 0x8c, 0xcd, 0xff, 0xff, 0xff, //0x00001a4d jl           LBB0_370
	0x4c, 0x8b, 0x5d, 0xc8, //0x00001a53 movq         $-56(%rbp), %r11
	0x4c, 0x8d, 0x05, 0x52, 0x35, 0x00, 0x00, //0x00001a57 leaq         $13650(%rip), %r8  /* _LSHIFT_TAB+0(%rip) */
	//0x00001a5e LBB0_372
	0x41, 0x29, 0xd3, //0x00001a5e subl         %edx, %r11d
	0x41, 0x89, 0xdf, //0x00001a61 movl         %ebx, %r15d
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x00001a64 jmp          LBB0_375
	//0x00001a69 LBB0_373
	0x48, 0x85, 0xd2, //0x00001a69 testq        %rdx, %rdx
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x00001a6c movl         $1, %ecx
	0x44, 0x0f, 0x45, 0xf1, //0x00001a71 cmovnel      %ecx, %r14d
	//0x00001a75 LBB0_374
	0x48, 0x01, 0xc0, //0x00001a75 addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x00001a78 leaq         (%rax,%rax,4), %rax
	//0x00001a7c LBB0_375
	0x48, 0x85, 0xc0, //0x00001a7c testq        %rax, %rax
	0x0f, 0x84, 0x29, 0x00, 0x00, 0x00, //0x00001a7f je           LBB0_378
	0x48, 0x89, 0xc2, //0x00001a85 movq         %rax, %rdx
	0x44, 0x89, 0xc9, //0x00001a88 movl         %r9d, %ecx
	0x48, 0xd3, 0xea, //0x00001a8b shrq         %cl, %rdx
	0x48, 0x21, 0xf0, //0x00001a8e andq         %rsi, %rax
	0x49, 0x63, 0xcf, //0x00001a91 movslq       %r15d, %rcx
	0x49, 0x39, 0xcd, //0x00001a94 cmpq         %rcx, %r13
	0x0f, 0x86, 0xcc, 0xff, 0xff, 0xff, //0x00001a97 jbe          LBB0_373
	0x80, 0xc2, 0x30, //0x00001a9d addb         $48, %dl
	0x41, 0x88, 0x14, 0x0c, //0x00001aa0 movb         %dl, (%r12,%rcx)
	0xff, 0xc1, //0x00001aa4 incl         %ecx
	0x41, 0x89, 0xcf, //0x00001aa6 movl         %ecx, %r15d
	0xe9, 0xc7, 0xff, 0xff, 0xff, //0x00001aa9 jmp          LBB0_374
	//0x00001aae LBB0_378
	0x41, 0xff, 0xc3, //0x00001aae incl         %r11d
	0x45, 0x85, 0xff, //0x00001ab1 testl        %r15d, %r15d
	0x0f, 0x8e, 0x69, 0x00, 0x00, 0x00, //0x00001ab4 jle          LBB0_387
	0x44, 0x89, 0xf8, //0x00001aba movl         %r15d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x00001abd cmpb         $48, $-1(%rax,%r12)
	0x0f, 0x85, 0x61, 0x00, 0x00, 0x00, //0x00001ac3 jne          LBB0_388
	//0x00001ac9 LBB0_380
	0x48, 0x83, 0xf8, 0x01, //0x00001ac9 cmpq         $1, %rax
	0x0f, 0x8e, 0x4b, 0x00, 0x00, 0x00, //0x00001acd jle          LBB0_386
	0x4c, 0x8d, 0x78, 0xff, //0x00001ad3 leaq         $-1(%rax), %r15
	0x41, 0x80, 0x7c, 0x04, 0xfe, 0x30, //0x00001ad7 cmpb         $48, $-2(%r12,%rax)
	0x4c, 0x89, 0xf8, //0x00001add movq         %r15, %rax
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00001ae0 je           LBB0_380
	0xe9, 0x3f, 0x00, 0x00, 0x00, //0x00001ae6 jmp          LBB0_388
	//0x00001aeb LBB0_382
	0x48, 0x85, 0xc0, //0x00001aeb testq        %rax, %rax
	0x0f, 0x84, 0x20, 0x00, 0x00, 0x00, //0x00001aee je           LBB0_385
	//0x00001af4 LBB0_383
	0x48, 0x89, 0xc6, //0x00001af4 movq         %rax, %rsi
	0x44, 0x89, 0xc9, //0x00001af7 movl         %r9d, %ecx
	0x48, 0xd3, 0xee, //0x00001afa shrq         %cl, %rsi
	0x48, 0x85, 0xf6, //0x00001afd testq        %rsi, %rsi
	0x0f, 0x85, 0xe6, 0xfe, 0xff, 0xff, //0x00001b00 jne          LBB0_368
	0x48, 0x01, 0xc0, //0x00001b06 addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x00001b09 leaq         (%rax,%rax,4), %rax
	0xff, 0xc2, //0x00001b0d incl         %edx
	0xe9, 0xe0, 0xff, 0xff, 0xff, //0x00001b0f jmp          LBB0_383
	//0x00001b14 LBB0_385
	0x45, 0x31, 0xff, //0x00001b14 xorl         %r15d, %r15d
	0x31, 0xc0, //0x00001b17 xorl         %eax, %eax
	0xe9, 0x2f, 0xf8, 0xff, 0xff, //0x00001b19 jmp          LBB0_273
	//0x00001b1e LBB0_386
	0xff, 0xc8, //0x00001b1e decl         %eax
	0x41, 0x89, 0xc7, //0x00001b20 movl         %eax, %r15d
	//0x00001b23 LBB0_387
	0x45, 0x85, 0xff, //0x00001b23 testl        %r15d, %r15d
	0x45, 0x0f, 0x44, 0xdf, //0x00001b26 cmovel       %r15d, %r11d
	//0x00001b2a LBB0_388
	0x44, 0x89, 0xf8, //0x00001b2a movl         %r15d, %eax
	0xe9, 0x1b, 0xf8, 0xff, 0xff, //0x00001b2d jmp          LBB0_273
	//0x00001b32 LBB0_389
	0x81, 0xfe, 0x02, 0xfc, 0xff, 0xff, //0x00001b32 cmpl         $-1022, %esi
	0x0f, 0x8f, 0xb1, 0x01, 0x00, 0x00, //0x00001b38 jg           LBB0_415
	0x41, 0xb8, 0x02, 0xfc, 0xff, 0xff, //0x00001b3e movl         $-1022, %r8d
	0x85, 0xff, //0x00001b44 testl        %edi, %edi
	0x0f, 0x84, 0xfd, 0x01, 0x00, 0x00, //0x00001b46 je           LBB0_420
	0x8d, 0x8e, 0xfd, 0x03, 0x00, 0x00, //0x00001b4c leal         $1021(%rsi), %ecx
	0x81, 0xfe, 0xc6, 0xfb, 0xff, 0xff, //0x00001b52 cmpl         $-1082, %esi
	0x0f, 0x8f, 0x06, 0x02, 0x00, 0x00, //0x00001b58 jg           LBB0_421
	0x4d, 0x89, 0xd8, //0x00001b5e movq         %r11, %r8
	0x49, 0x8d, 0x42, 0x01, //0x00001b61 leaq         $1(%r10), %rax
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00001b65 movl         $1, %r11d
	0xe9, 0x1d, 0x00, 0x00, 0x00, //0x00001b6b jmp          LBB0_396
	//0x00001b70 LBB0_393
	0xff, 0xc9, //0x00001b70 decl         %ecx
	0x41, 0x89, 0xcf, //0x00001b72 movl         %ecx, %r15d
	//0x00001b75 LBB0_394
	0x45, 0x85, 0xff, //0x00001b75 testl        %r15d, %r15d
	0x45, 0x0f, 0x44, 0xc7, //0x00001b78 cmovel       %r15d, %r8d
	//0x00001b7c LBB0_395
	0x41, 0x8d, 0x49, 0x3c, //0x00001b7c leal         $60(%r9), %ecx
	0x44, 0x89, 0xff, //0x00001b80 movl         %r15d, %edi
	0x41, 0x83, 0xf9, 0x88, //0x00001b83 cmpl         $-120, %r9d
	0x0f, 0x8d, 0xe6, 0x01, 0x00, 0x00, //0x00001b87 jge          LBB0_422
	//0x00001b8d LBB0_396
	0x41, 0x89, 0xc9, //0x00001b8d movl         %ecx, %r9d
	0x48, 0x63, 0xdf, //0x00001b90 movslq       %edi, %rbx
	0x31, 0xf6, //0x00001b93 xorl         %esi, %esi
	0x31, 0xc9, //0x00001b95 xorl         %ecx, %ecx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001b97 .p2align 4, 0x90
	//0x00001ba0 LBB0_397
	0x48, 0x39, 0xde, //0x00001ba0 cmpq         %rbx, %rsi
	0x0f, 0x8d, 0x1f, 0x00, 0x00, 0x00, //0x00001ba3 jge          LBB0_399
	0x48, 0x8d, 0x0c, 0x89, //0x00001ba9 leaq         (%rcx,%rcx,4), %rcx
	0x49, 0x0f, 0xbe, 0x14, 0x34, //0x00001bad movsbq       (%r12,%rsi), %rdx
	0x48, 0x8d, 0x4c, 0x4a, 0xd0, //0x00001bb2 leaq         $-48(%rdx,%rcx,2), %rcx
	0x48, 0xff, 0xc6, //0x00001bb7 incq         %rsi
	0x48, 0x39, 0xc1, //0x00001bba cmpq         %rax, %rcx
	0x0f, 0x82, 0xdd, 0xff, 0xff, 0xff, //0x00001bbd jb           LBB0_397
	0xe9, 0x2a, 0x00, 0x00, 0x00, //0x00001bc3 jmp          LBB0_401
	//0x00001bc8 LBB0_399
	0x48, 0x85, 0xc9, //0x00001bc8 testq        %rcx, %rcx
	0x0f, 0x84, 0x16, 0x01, 0x00, 0x00, //0x00001bcb je           LBB0_414
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001bd1 .p2align 4, 0x90
	//0x00001be0 LBB0_400
	0x48, 0x01, 0xc9, //0x00001be0 addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x00001be3 leaq         (%rcx,%rcx,4), %rcx
	0xff, 0xc6, //0x00001be7 incl         %esi
	0x48, 0x39, 0xc1, //0x00001be9 cmpq         %rax, %rcx
	0x0f, 0x82, 0xee, 0xff, 0xff, 0xff, //0x00001bec jb           LBB0_400
	//0x00001bf2 LBB0_401
	0x41, 0x29, 0xf0, //0x00001bf2 subl         %esi, %r8d
	0x31, 0xdb, //0x00001bf5 xorl         %ebx, %ebx
	0x39, 0xfe, //0x00001bf7 cmpl         %edi, %esi
	0x0f, 0x8d, 0x4d, 0x00, 0x00, 0x00, //0x00001bf9 jge          LBB0_406
	0x48, 0x63, 0xf6, //0x00001bff movslq       %esi, %rsi
	0x49, 0x63, 0xff, //0x00001c02 movslq       %r15d, %rdi
	0x49, 0x8d, 0x1c, 0x34, //0x00001c05 leaq         (%r12,%rsi), %rbx
	0x45, 0x31, 0xff, //0x00001c09 xorl         %r15d, %r15d
	0x90, 0x90, 0x90, 0x90, //0x00001c0c .p2align 4, 0x90
	//0x00001c10 LBB0_403
	0x48, 0x89, 0xca, //0x00001c10 movq         %rcx, %rdx
	0x48, 0xc1, 0xea, 0x3c, //0x00001c13 shrq         $60, %rdx
	0x4c, 0x21, 0xd1, //0x00001c17 andq         %r10, %rcx
	0x80, 0xca, 0x30, //0x00001c1a orb          $48, %dl
	0x43, 0x88, 0x14, 0x3c, //0x00001c1d movb         %dl, (%r12,%r15)
	0x48, 0x8d, 0x0c, 0x89, //0x00001c21 leaq         (%rcx,%rcx,4), %rcx
	0x4a, 0x0f, 0xbe, 0x14, 0x3b, //0x00001c25 movsbq       (%rbx,%r15), %rdx
	0x48, 0x8d, 0x4c, 0x4a, 0xd0, //0x00001c2a leaq         $-48(%rdx,%rcx,2), %rcx
	0x4a, 0x8d, 0x54, 0x3e, 0x01, //0x00001c2f leaq         $1(%rsi,%r15), %rdx
	0x49, 0xff, 0xc7, //0x00001c34 incq         %r15
	0x48, 0x39, 0xfa, //0x00001c37 cmpq         %rdi, %rdx
	0x0f, 0x8c, 0xd0, 0xff, 0xff, 0xff, //0x00001c3a jl           LBB0_403
	0x48, 0x85, 0xc9, //0x00001c40 testq        %rcx, %rcx
	0x0f, 0x84, 0x61, 0x00, 0x00, 0x00, //0x00001c43 je           LBB0_410
	0x44, 0x89, 0xfb, //0x00001c49 movl         %r15d, %ebx
	//0x00001c4c LBB0_406
	0x41, 0x89, 0xdf, //0x00001c4c movl         %ebx, %r15d
	0xe9, 0x23, 0x00, 0x00, 0x00, //0x00001c4f jmp          LBB0_408
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001c54 .p2align 4, 0x90
	//0x00001c60 LBB0_407
	0x48, 0x85, 0xf6, //0x00001c60 testq        %rsi, %rsi
	0x45, 0x0f, 0x45, 0xf3, //0x00001c63 cmovnel      %r11d, %r14d
	0x48, 0x01, 0xc9, //0x00001c67 addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x00001c6a leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xc9, //0x00001c6e testq        %rcx, %rcx
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x00001c71 je           LBB0_410
	//0x00001c77 LBB0_408
	0x48, 0x89, 0xce, //0x00001c77 movq         %rcx, %rsi
	0x48, 0xc1, 0xee, 0x3c, //0x00001c7a shrq         $60, %rsi
	0x4c, 0x21, 0xd1, //0x00001c7e andq         %r10, %rcx
	0x49, 0x63, 0xff, //0x00001c81 movslq       %r15d, %rdi
	0x49, 0x39, 0xfd, //0x00001c84 cmpq         %rdi, %r13
	0x0f, 0x86, 0xd3, 0xff, 0xff, 0xff, //0x00001c87 jbe          LBB0_407
	0x40, 0x80, 0xce, 0x30, //0x00001c8d orb          $48, %sil
	0x41, 0x88, 0x34, 0x3c, //0x00001c91 movb         %sil, (%r12,%rdi)
	0xff, 0xc7, //0x00001c95 incl         %edi
	0x41, 0x89, 0xff, //0x00001c97 movl         %edi, %r15d
	0x48, 0x01, 0xc9, //0x00001c9a addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x00001c9d leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xc9, //0x00001ca1 testq        %rcx, %rcx
	0x0f, 0x85, 0xcd, 0xff, 0xff, 0xff, //0x00001ca4 jne          LBB0_408
	//0x00001caa LBB0_410
	0x41, 0xff, 0xc0, //0x00001caa incl         %r8d
	0x45, 0x85, 0xff, //0x00001cad testl        %r15d, %r15d
	0x0f, 0x8e, 0xbf, 0xfe, 0xff, 0xff, //0x00001cb0 jle          LBB0_394
	0x44, 0x89, 0xf9, //0x00001cb6 movl         %r15d, %ecx
	0x42, 0x80, 0x7c, 0x21, 0xff, 0x30, //0x00001cb9 cmpb         $48, $-1(%rcx,%r12)
	0x0f, 0x85, 0xb7, 0xfe, 0xff, 0xff, //0x00001cbf jne          LBB0_395
	//0x00001cc5 LBB0_412
	0x48, 0x83, 0xf9, 0x01, //0x00001cc5 cmpq         $1, %rcx
	0x0f, 0x8e, 0xa1, 0xfe, 0xff, 0xff, //0x00001cc9 jle          LBB0_393
	0x4c, 0x8d, 0x79, 0xff, //0x00001ccf leaq         $-1(%rcx), %r15
	0x41, 0x80, 0x7c, 0x0c, 0xfe, 0x30, //0x00001cd3 cmpb         $48, $-2(%r12,%rcx)
	0x4c, 0x89, 0xf9, //0x00001cd9 movq         %r15, %rcx
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00001cdc je           LBB0_412
	0xe9, 0x95, 0xfe, 0xff, 0xff, //0x00001ce2 jmp          LBB0_395
	//0x00001ce7 LBB0_414
	0x45, 0x31, 0xff, //0x00001ce7 xorl         %r15d, %r15d
	0xe9, 0x8d, 0xfe, 0xff, 0xff, //0x00001cea jmp          LBB0_395
	//0x00001cef LBB0_415
	0x81, 0xfe, 0x00, 0x04, 0x00, 0x00, //0x00001cef cmpl         $1024, %esi
	0x0f, 0x8e, 0x28, 0x00, 0x00, 0x00, //0x00001cf5 jle          LBB0_417
	0x31, 0xf6, //0x00001cfb xorl         %esi, %esi
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, //0x00001cfd movabsq      $9218868437227405312, %r8
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00001d07 movabsq      $-9223372036854775808, %rdi
	0x49, 0xba, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00001d11 movabsq      $4503599627370495, %r10
	0x8a, 0x55, 0xd7, //0x00001d1b movb         $-41(%rbp), %dl
	0xe9, 0xb7, 0x05, 0x00, 0x00, //0x00001d1e jmp          LBB0_506
	//0x00001d23 LBB0_417
	0xff, 0xce, //0x00001d23 decl         %esi
	0x41, 0x89, 0xf0, //0x00001d25 movl         %esi, %r8d
	0x49, 0xba, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00001d28 movabsq      $4503599627370495, %r10
	//0x00001d32 LBB0_418
	0x85, 0xff, //0x00001d32 testl        %edi, %edi
	0x0f, 0x84, 0xc2, 0x01, 0x00, 0x00, //0x00001d34 je           LBB0_443
	0x4c, 0x89, 0x45, 0xc8, //0x00001d3a movq         %r8, $-56(%rbp)
	0x4d, 0x89, 0xd8, //0x00001d3e movq         %r11, %r8
	0x45, 0x89, 0xfa, //0x00001d41 movl         %r15d, %r10d
	0xe9, 0xfa, 0x01, 0x00, 0x00, //0x00001d44 jmp          LBB0_448
	//0x00001d49 LBB0_420
	0x31, 0xc0, //0x00001d49 xorl         %eax, %eax
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00001d4b movabsq      $-9223372036854775808, %rdi
	0x49, 0xba, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00001d55 movabsq      $4503599627370495, %r10
	0xe9, 0xcf, 0x03, 0x00, 0x00, //0x00001d5f jmp          LBB0_475
	//0x00001d64 LBB0_421
	0x49, 0xba, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00001d64 movabsq      $4503599627370495, %r10
	0xe9, 0x1e, 0x00, 0x00, 0x00, //0x00001d6e jmp          LBB0_423
	//0x00001d73 LBB0_422
	0x44, 0x89, 0xff, //0x00001d73 movl         %r15d, %edi
	0x49, 0xba, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00001d76 movabsq      $4503599627370495, %r10
	0x4d, 0x89, 0xc3, //0x00001d80 movq         %r8, %r11
	0x41, 0xb8, 0x02, 0xfc, 0xff, 0xff, //0x00001d83 movl         $-1022, %r8d
	0x85, 0xc9, //0x00001d89 testl        %ecx, %ecx
	0x0f, 0x84, 0xa1, 0xff, 0xff, 0xff, //0x00001d8b je           LBB0_418
	//0x00001d91 LBB0_423
	0xf7, 0xd9, //0x00001d91 negl         %ecx
	0x48, 0x63, 0xf7, //0x00001d93 movslq       %edi, %rsi
	0x31, 0xd2, //0x00001d96 xorl         %edx, %edx
	0x31, 0xc0, //0x00001d98 xorl         %eax, %eax
	//0x00001d9a LBB0_424
	0x48, 0x39, 0xf2, //0x00001d9a cmpq         %rsi, %rdx
	0x0f, 0x8d, 0x25, 0x00, 0x00, 0x00, //0x00001d9d jge          LBB0_440
	0x48, 0x8d, 0x04, 0x80, //0x00001da3 leaq         (%rax,%rax,4), %rax
	0x49, 0x0f, 0xbe, 0x1c, 0x14, //0x00001da7 movsbq       (%r12,%rdx), %rbx
	0x48, 0x8d, 0x44, 0x43, 0xd0, //0x00001dac leaq         $-48(%rbx,%rax,2), %rax
	0x48, 0xff, 0xc2, //0x00001db1 incq         %rdx
	0x48, 0x89, 0xc3, //0x00001db4 movq         %rax, %rbx
	0x48, 0xd3, 0xeb, //0x00001db7 shrq         %cl, %rbx
	0x48, 0x85, 0xdb, //0x00001dba testq        %rbx, %rbx
	0x0f, 0x84, 0xd7, 0xff, 0xff, 0xff, //0x00001dbd je           LBB0_424
	0xe9, 0x30, 0x00, 0x00, 0x00, //0x00001dc3 jmp          LBB0_426
	//0x00001dc8 LBB0_440
	0x48, 0x85, 0xc0, //0x00001dc8 testq        %rax, %rax
	0x0f, 0x84, 0x2b, 0x01, 0x00, 0x00, //0x00001dcb je           LBB0_443
	0x48, 0x89, 0xc6, //0x00001dd1 movq         %rax, %rsi
	0x48, 0xd3, 0xee, //0x00001dd4 shrq         %cl, %rsi
	0x48, 0x85, 0xf6, //0x00001dd7 testq        %rsi, %rsi
	0x0f, 0x85, 0x18, 0x00, 0x00, 0x00, //0x00001dda jne          LBB0_426
	//0x00001de0 LBB0_442
	0x48, 0x01, 0xc0, //0x00001de0 addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x00001de3 leaq         (%rax,%rax,4), %rax
	0xff, 0xc2, //0x00001de7 incl         %edx
	0x48, 0x89, 0xc6, //0x00001de9 movq         %rax, %rsi
	0x48, 0xd3, 0xee, //0x00001dec shrq         %cl, %rsi
	0x48, 0x85, 0xf6, //0x00001def testq        %rsi, %rsi
	0x0f, 0x84, 0xe8, 0xff, 0xff, 0xff, //0x00001df2 je           LBB0_442
	//0x00001df8 LBB0_426
	0x48, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00001df8 movq         $-1, %rsi
	0x48, 0xd3, 0xe6, //0x00001dff shlq         %cl, %rsi
	0x48, 0xf7, 0xd6, //0x00001e02 notq         %rsi
	0x45, 0x31, 0xd2, //0x00001e05 xorl         %r10d, %r10d
	0x39, 0xfa, //0x00001e08 cmpl         %edi, %edx
	0x0f, 0x8d, 0x3d, 0x00, 0x00, 0x00, //0x00001e0a jge          LBB0_429
	0x4c, 0x63, 0xca, //0x00001e10 movslq       %edx, %r9
	0x4d, 0x63, 0xc7, //0x00001e13 movslq       %r15d, %r8
	0x4b, 0x8d, 0x1c, 0x0c, //0x00001e16 leaq         (%r12,%r9), %rbx
	0x45, 0x31, 0xd2, //0x00001e1a xorl         %r10d, %r10d
	//0x00001e1d LBB0_428
	0x48, 0x89, 0xc7, //0x00001e1d movq         %rax, %rdi
	0x48, 0xd3, 0xef, //0x00001e20 shrq         %cl, %rdi
	0x48, 0x21, 0xf0, //0x00001e23 andq         %rsi, %rax
	0x40, 0x80, 0xc7, 0x30, //0x00001e26 addb         $48, %dil
	0x43, 0x88, 0x3c, 0x14, //0x00001e2a movb         %dil, (%r12,%r10)
	0x48, 0x8d, 0x04, 0x80, //0x00001e2e leaq         (%rax,%rax,4), %rax
	0x4a, 0x0f, 0xbe, 0x3c, 0x13, //0x00001e32 movsbq       (%rbx,%r10), %rdi
	0x48, 0x8d, 0x44, 0x47, 0xd0, //0x00001e37 leaq         $-48(%rdi,%rax,2), %rax
	0x4b, 0x8d, 0x7c, 0x11, 0x01, //0x00001e3c leaq         $1(%r9,%r10), %rdi
	0x49, 0xff, 0xc2, //0x00001e41 incq         %r10
	0x4c, 0x39, 0xc7, //0x00001e44 cmpq         %r8, %rdi
	0x0f, 0x8c, 0xd0, 0xff, 0xff, 0xff, //0x00001e47 jl           LBB0_428
	//0x00001e4d LBB0_429
	0x41, 0x29, 0xd3, //0x00001e4d subl         %edx, %r11d
	0x48, 0x85, 0xc0, //0x00001e50 testq        %rax, %rax
	0x0f, 0x84, 0x53, 0x00, 0x00, 0x00, //0x00001e53 je           LBB0_434
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00001e59 movl         $1, %edx
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00001e5e jmp          LBB0_432
	//0x00001e63 LBB0_431
	0x48, 0x85, 0xff, //0x00001e63 testq        %rdi, %rdi
	0x44, 0x0f, 0x45, 0xf2, //0x00001e66 cmovnel      %edx, %r14d
	0x48, 0x01, 0xc0, //0x00001e6a addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x00001e6d leaq         (%rax,%rax,4), %rax
	0x48, 0x85, 0xc0, //0x00001e71 testq        %rax, %rax
	0x0f, 0x84, 0x32, 0x00, 0x00, 0x00, //0x00001e74 je           LBB0_434
	//0x00001e7a LBB0_432
	0x48, 0x89, 0xc7, //0x00001e7a movq         %rax, %rdi
	0x48, 0xd3, 0xef, //0x00001e7d shrq         %cl, %rdi
	0x48, 0x21, 0xf0, //0x00001e80 andq         %rsi, %rax
	0x49, 0x63, 0xda, //0x00001e83 movslq       %r10d, %rbx
	0x49, 0x39, 0xdd, //0x00001e86 cmpq         %rbx, %r13
	0x0f, 0x86, 0xd4, 0xff, 0xff, 0xff, //0x00001e89 jbe          LBB0_431
	0x40, 0x80, 0xc7, 0x30, //0x00001e8f addb         $48, %dil
	0x41, 0x88, 0x3c, 0x1c, //0x00001e93 movb         %dil, (%r12,%rbx)
	0xff, 0xc3, //0x00001e97 incl         %ebx
	0x41, 0x89, 0xda, //0x00001e99 movl         %ebx, %r10d
	0x48, 0x01, 0xc0, //0x00001e9c addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x00001e9f leaq         (%rax,%rax,4), %rax
	0x48, 0x85, 0xc0, //0x00001ea3 testq        %rax, %rax
	0x0f, 0x85, 0xce, 0xff, 0xff, 0xff, //0x00001ea6 jne          LBB0_432
	//0x00001eac LBB0_434
	0x41, 0xff, 0xc3, //0x00001eac incl         %r11d
	0x45, 0x85, 0xd2, //0x00001eaf testl        %r10d, %r10d
	0x0f, 0x8e, 0x6d, 0x00, 0x00, 0x00, //0x00001eb2 jle          LBB0_446
	0x44, 0x89, 0xd0, //0x00001eb8 movl         %r10d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x00001ebb cmpb         $48, $-1(%rax,%r12)
	0x0f, 0x85, 0x6d, 0x00, 0x00, 0x00, //0x00001ec1 jne          LBB0_447
	0x49, 0xba, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00001ec7 movabsq      $4503599627370495, %r10
	//0x00001ed1 LBB0_437
	0x48, 0x83, 0xf8, 0x01, //0x00001ed1 cmpq         $1, %rax
	0x0f, 0x8e, 0x45, 0x00, 0x00, 0x00, //0x00001ed5 jle          LBB0_445
	0x4c, 0x8d, 0x78, 0xff, //0x00001edb leaq         $-1(%rax), %r15
	0x41, 0x80, 0x7c, 0x04, 0xfe, 0x30, //0x00001edf cmpb         $48, $-2(%r12,%rax)
	0x4c, 0x89, 0xf8, //0x00001ee5 movq         %r15, %rax
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00001ee8 je           LBB0_437
	0x41, 0xb8, 0x02, 0xfc, 0xff, 0xff, //0x00001eee movl         $-1022, %r8d
	0x44, 0x89, 0xff, //0x00001ef4 movl         %r15d, %edi
	0xe9, 0x36, 0xfe, 0xff, 0xff, //0x00001ef7 jmp          LBB0_418
	//0x00001efc LBB0_443
	0x31, 0xc0, //0x00001efc xorl         %eax, %eax
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00001efe movabsq      $-9223372036854775808, %rdi
	0xe9, 0x26, 0x02, 0x00, 0x00, //0x00001f08 jmp          LBB0_475
	//0x00001f0d LBB0_444
	0x01, 0xc7, //0x00001f0d addl         %eax, %edi
	0x45, 0x31, 0xdb, //0x00001f0f xorl         %r11d, %r11d
	0x45, 0x31, 0xff, //0x00001f12 xorl         %r15d, %r15d
	0x89, 0xf8, //0x00001f15 movl         %edi, %eax
	0x31, 0xff, //0x00001f17 xorl         %edi, %edi
	0x89, 0xc6, //0x00001f19 movl         %eax, %esi
	0xe9, 0x1f, 0xf4, 0xff, 0xff, //0x00001f1b jmp          LBB0_271
	//0x00001f20 LBB0_445
	0xff, 0xc8, //0x00001f20 decl         %eax
	0x41, 0x89, 0xc2, //0x00001f22 movl         %eax, %r10d
	//0x00001f25 LBB0_446
	0x41, 0xb8, 0x02, 0xfc, 0xff, 0xff, //0x00001f25 movl         $-1022, %r8d
	0x45, 0x85, 0xd2, //0x00001f2b testl        %r10d, %r10d
	0x0f, 0x84, 0x1d, 0x03, 0x00, 0x00, //0x00001f2e je           LBB0_499
	//0x00001f34 LBB0_447
	0xb8, 0x02, 0xfc, 0xff, 0xff, //0x00001f34 movl         $-1022, %eax
	0x48, 0x89, 0x45, 0xc8, //0x00001f39 movq         %rax, $-56(%rbp)
	0x4d, 0x89, 0xd8, //0x00001f3d movq         %r11, %r8
	0x44, 0x89, 0xd7, //0x00001f40 movl         %r10d, %edi
	//0x00001f43 LBB0_448
	0x4c, 0x63, 0xdf, //0x00001f43 movslq       %edi, %r11
	0xb1, 0x31, //0x00001f46 movb         $49, %cl
	0x31, 0xc0, //0x00001f48 xorl         %eax, %eax
	0x41, 0xb9, 0x10, 0x00, 0x00, 0x00, //0x00001f4a movl         $16, %r9d
	//0x00001f50 LBB0_449
	0x48, 0x83, 0xf8, 0x26, //0x00001f50 cmpq         $38, %rax
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x00001f54 je           LBB0_454
	0x41, 0x38, 0x0c, 0x04, //0x00001f5a cmpb         %cl, (%r12,%rax)
	0x0f, 0x85, 0xd7, 0x00, 0x00, 0x00, //0x00001f5e jne          LBB0_463
	0x48, 0x8d, 0x0d, 0x45, 0x30, 0x00, 0x00, //0x00001f64 leaq         $12357(%rip), %rcx  /* _LSHIFT_TAB+0(%rip) */
	0x0f, 0xb6, 0x8c, 0x08, 0x8d, 0x15, 0x00, 0x00, //0x00001f6b movzbl       $5517(%rax,%rcx), %ecx
	0x48, 0xff, 0xc0, //0x00001f73 incq         %rax
	0x49, 0x39, 0xc3, //0x00001f76 cmpq         %rax, %r11
	0x0f, 0x85, 0xd1, 0xff, 0xff, 0xff, //0x00001f79 jne          LBB0_449
	0x84, 0xc9, //0x00001f7f testb        %cl, %cl
	0x0f, 0x84, 0x06, 0x00, 0x00, 0x00, //0x00001f81 je           LBB0_454
	//0x00001f87 LBB0_453
	0x41, 0xb9, 0x0f, 0x00, 0x00, 0x00, //0x00001f87 movl         $15, %r9d
	//0x00001f8d LBB0_454
	0x85, 0xff, //0x00001f8d testl        %edi, %edi
	0x0f, 0x8e, 0x9a, 0x00, 0x00, 0x00, //0x00001f8f jle          LBB0_462
	0x44, 0x01, 0xcf, //0x00001f95 addl         %r9d, %edi
	0x48, 0x63, 0xf7, //0x00001f98 movslq       %edi, %rsi
	0x48, 0xff, 0xce, //0x00001f9b decq         %rsi
	0x49, 0xff, 0xc3, //0x00001f9e incq         %r11
	0x31, 0xc9, //0x00001fa1 xorl         %ecx, %ecx
	0x49, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfa, //0x00001fa3 movabsq      $-432345564227567616, %r15
	0xe9, 0x1e, 0x00, 0x00, 0x00, //0x00001fad jmp          LBB0_458
	//0x00001fb2 LBB0_456
	0x48, 0x85, 0xc0, //0x00001fb2 testq        %rax, %rax
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00001fb5 movl         $1, %eax
	0x44, 0x0f, 0x45, 0xf0, //0x00001fba cmovnel      %eax, %r14d
	//0x00001fbe LBB0_457
	0xff, 0xcf, //0x00001fbe decl         %edi
	0x48, 0xff, 0xce, //0x00001fc0 decq         %rsi
	0x49, 0xff, 0xcb, //0x00001fc3 decq         %r11
	0x49, 0x83, 0xfb, 0x01, //0x00001fc6 cmpq         $1, %r11
	0x0f, 0x8e, 0x49, 0x00, 0x00, 0x00, //0x00001fca jle          LBB0_460
	//0x00001fd0 LBB0_458
	0x4b, 0x0f, 0xbe, 0x5c, 0x1c, 0xfe, //0x00001fd0 movsbq       $-2(%r12,%r11), %rbx
	0x48, 0xc1, 0xe3, 0x35, //0x00001fd6 shlq         $53, %rbx
	0x48, 0x01, 0xcb, //0x00001fda addq         %rcx, %rbx
	0x4c, 0x01, 0xfb, //0x00001fdd addq         %r15, %rbx
	0x48, 0x89, 0xd8, //0x00001fe0 movq         %rbx, %rax
	0x48, 0xb9, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00001fe3 movabsq      $-3689348814741910323, %rcx
	0x48, 0xf7, 0xe1, //0x00001fed mulq         %rcx
	0x48, 0x89, 0xd1, //0x00001ff0 movq         %rdx, %rcx
	0x48, 0xc1, 0xe9, 0x03, //0x00001ff3 shrq         $3, %rcx
	0x48, 0x8d, 0x04, 0x09, //0x00001ff7 leaq         (%rcx,%rcx), %rax
	0x48, 0x8d, 0x14, 0x80, //0x00001ffb leaq         (%rax,%rax,4), %rdx
	0x48, 0x89, 0xd8, //0x00001fff movq         %rbx, %rax
	0x48, 0x29, 0xd0, //0x00002002 subq         %rdx, %rax
	0x4c, 0x39, 0xee, //0x00002005 cmpq         %r13, %rsi
	0x0f, 0x83, 0xa4, 0xff, 0xff, 0xff, //0x00002008 jae          LBB0_456
	0x04, 0x30, //0x0000200e addb         $48, %al
	0x41, 0x88, 0x04, 0x34, //0x00002010 movb         %al, (%r12,%rsi)
	0xe9, 0xa5, 0xff, 0xff, 0xff, //0x00002014 jmp          LBB0_457
	//0x00002019 LBB0_460
	0x48, 0x83, 0xfb, 0x0a, //0x00002019 cmpq         $10, %rbx
	0x4d, 0x89, 0xc3, //0x0000201d movq         %r8, %r11
	0x0f, 0x83, 0x20, 0x00, 0x00, 0x00, //0x00002020 jae          LBB0_464
	0x4c, 0x8b, 0x45, 0xc8, //0x00002026 movq         $-56(%rbp), %r8
	0xe9, 0x83, 0x00, 0x00, 0x00, //0x0000202a jmp          LBB0_468
	//0x0000202f LBB0_462
	0x4d, 0x89, 0xc3, //0x0000202f movq         %r8, %r11
	0x4c, 0x8b, 0x45, 0xc8, //0x00002032 movq         $-56(%rbp), %r8
	0xe9, 0x77, 0x00, 0x00, 0x00, //0x00002036 jmp          LBB0_468
	//0x0000203b LBB0_463
	0x0f, 0x8c, 0x46, 0xff, 0xff, 0xff, //0x0000203b jl           LBB0_453
	0xe9, 0x47, 0xff, 0xff, 0xff, //0x00002041 jmp          LBB0_454
	//0x00002046 LBB0_464
	0x48, 0x63, 0xf7, //0x00002046 movslq       %edi, %rsi
	0x48, 0xff, 0xce, //0x00002049 decq         %rsi
	0xbf, 0x01, 0x00, 0x00, 0x00, //0x0000204c movl         $1, %edi
	0x4c, 0x8b, 0x45, 0xc8, //0x00002051 movq         $-56(%rbp), %r8
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00002055 jmp          LBB0_466
	//0x0000205a LBB0_465
	0x48, 0x85, 0xc0, //0x0000205a testq        %rax, %rax
	0x44, 0x0f, 0x45, 0xf7, //0x0000205d cmovnel      %edi, %r14d
	0x48, 0xff, 0xce, //0x00002061 decq         %rsi
	0x48, 0x83, 0xf9, 0x09, //0x00002064 cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x00002068 movq         %rdx, %rcx
	0x0f, 0x86, 0x41, 0x00, 0x00, 0x00, //0x0000206b jbe          LBB0_468
	//0x00002071 LBB0_466
	0x48, 0x89, 0xc8, //0x00002071 movq         %rcx, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00002074 movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x0000207e mulq         %rdx
	0x48, 0xc1, 0xea, 0x03, //0x00002081 shrq         $3, %rdx
	0x48, 0x8d, 0x04, 0x12, //0x00002085 leaq         (%rdx,%rdx), %rax
	0x48, 0x8d, 0x1c, 0x80, //0x00002089 leaq         (%rax,%rax,4), %rbx
	0x48, 0x89, 0xc8, //0x0000208d movq         %rcx, %rax
	0x48, 0x29, 0xd8, //0x00002090 subq         %rbx, %rax
	0x4c, 0x39, 0xee, //0x00002093 cmpq         %r13, %rsi
	0x0f, 0x83, 0xbe, 0xff, 0xff, 0xff, //0x00002096 jae          LBB0_465
	0x04, 0x30, //0x0000209c addb         $48, %al
	0x41, 0x88, 0x04, 0x34, //0x0000209e movb         %al, (%r12,%rsi)
	0x48, 0xff, 0xce, //0x000020a2 decq         %rsi
	0x48, 0x83, 0xf9, 0x09, //0x000020a5 cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x000020a9 movq         %rdx, %rcx
	0x0f, 0x87, 0xbf, 0xff, 0xff, 0xff, //0x000020ac ja           LBB0_466
	//0x000020b2 LBB0_468
	0x45, 0x01, 0xca, //0x000020b2 addl         %r9d, %r10d
	0x49, 0x63, 0xc2, //0x000020b5 movslq       %r10d, %rax
	0x49, 0x39, 0xc5, //0x000020b8 cmpq         %rax, %r13
	0x41, 0x0f, 0x46, 0xc5, //0x000020bb cmovbel      %r13d, %eax
	0x45, 0x01, 0xcb, //0x000020bf addl         %r9d, %r11d
	0x85, 0xc0, //0x000020c2 testl        %eax, %eax
	0x0f, 0x8e, 0x44, 0x00, 0x00, 0x00, //0x000020c4 jle          LBB0_472
	0x89, 0xc1, //0x000020ca movl         %eax, %ecx
	0x42, 0x80, 0x7c, 0x21, 0xff, 0x30, //0x000020cc cmpb         $48, $-1(%rcx,%r12)
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x000020d2 movabsq      $-9223372036854775808, %rdi
	0x49, 0xba, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x000020dc movabsq      $4503599627370495, %r10
	0x0f, 0x85, 0x47, 0x00, 0x00, 0x00, //0x000020e6 jne          LBB0_475
	//0x000020ec LBB0_470
	0x48, 0x83, 0xf9, 0x01, //0x000020ec cmpq         $1, %rcx
	0x0f, 0x8e, 0x31, 0x00, 0x00, 0x00, //0x000020f0 jle          LBB0_473
	0x48, 0x8d, 0x41, 0xff, //0x000020f6 leaq         $-1(%rcx), %rax
	0x41, 0x80, 0x7c, 0x0c, 0xfe, 0x30, //0x000020fa cmpb         $48, $-2(%r12,%rcx)
	0x48, 0x89, 0xc1, //0x00002100 movq         %rax, %rcx
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00002103 je           LBB0_470
	0xe9, 0x25, 0x00, 0x00, 0x00, //0x00002109 jmp          LBB0_475
	//0x0000210e LBB0_472
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x0000210e movabsq      $-9223372036854775808, %rdi
	0x49, 0xba, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00002118 movabsq      $4503599627370495, %r10
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x00002122 jmp          LBB0_474
	//0x00002127 LBB0_473
	0xff, 0xc9, //0x00002127 decl         %ecx
	0x89, 0xc8, //0x00002129 movl         %ecx, %eax
	//0x0000212b LBB0_474
	0x85, 0xc0, //0x0000212b testl        %eax, %eax
	0x0f, 0x84, 0x15, 0x01, 0x00, 0x00, //0x0000212d je           LBB0_498
	//0x00002133 LBB0_475
	0x48, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00002133 movq         $-1, %rsi
	0x41, 0x83, 0xfb, 0x14, //0x0000213a cmpl         $20, %r11d
	0x0f, 0x8e, 0x08, 0x00, 0x00, 0x00, //0x0000213e jle          LBB0_477
	0x8a, 0x55, 0xd7, //0x00002144 movb         $-41(%rbp), %dl
	0xe9, 0x6e, 0x01, 0x00, 0x00, //0x00002147 jmp          LBB0_505
	//0x0000214c LBB0_477
	0x44, 0x89, 0xda, //0x0000214c movl         %r11d, %edx
	0x45, 0x85, 0xdb, //0x0000214f testl        %r11d, %r11d
	0x0f, 0x8e, 0x32, 0x00, 0x00, 0x00, //0x00002152 jle          LBB0_482
	0x48, 0x63, 0xf0, //0x00002158 movslq       %eax, %rsi
	0x31, 0xff, //0x0000215b xorl         %edi, %edi
	0x31, 0xc9, //0x0000215d xorl         %ecx, %ecx
	//0x0000215f LBB0_479
	0x48, 0x39, 0xf7, //0x0000215f cmpq         %rsi, %rdi
	0x0f, 0x8d, 0x1a, 0x00, 0x00, 0x00, //0x00002162 jge          LBB0_481
	0x48, 0x8d, 0x0c, 0x89, //0x00002168 leaq         (%rcx,%rcx,4), %rcx
	0x49, 0x0f, 0xbe, 0x1c, 0x3c, //0x0000216c movsbq       (%r12,%rdi), %rbx
	0x48, 0x8d, 0x4c, 0x4b, 0xd0, //0x00002171 leaq         $-48(%rbx,%rcx,2), %rcx
	0x48, 0xff, 0xc7, //0x00002176 incq         %rdi
	0x48, 0x39, 0xfa, //0x00002179 cmpq         %rdi, %rdx
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x0000217c jne          LBB0_479
	//0x00002182 LBB0_481
	0x45, 0x31, 0xc9, //0x00002182 xorl         %r9d, %r9d
	0xe9, 0x07, 0x00, 0x00, 0x00, //0x00002185 jmp          LBB0_483
	//0x0000218a LBB0_482
	0x31, 0xff, //0x0000218a xorl         %edi, %edi
	0x41, 0xb1, 0x01, //0x0000218c movb         $1, %r9b
	0x31, 0xc9, //0x0000218f xorl         %ecx, %ecx
	//0x00002191 LBB0_483
	0x44, 0x89, 0xde, //0x00002191 movl         %r11d, %esi
	0x29, 0xfe, //0x00002194 subl         %edi, %esi
	0x0f, 0x8e, 0x52, 0x00, 0x00, 0x00, //0x00002196 jle          LBB0_491
	0x41, 0x89, 0xfa, //0x0000219c movl         %edi, %r10d
	0x41, 0xf7, 0xd2, //0x0000219f notl         %r10d
	0x45, 0x01, 0xda, //0x000021a2 addl         %r11d, %r10d
	0x83, 0xe6, 0x07, //0x000021a5 andl         $7, %esi
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x000021a8 je           LBB0_488
	0xf7, 0xde, //0x000021ae negl         %esi
	0x31, 0xdb, //0x000021b0 xorl         %ebx, %ebx
	//0x000021b2 LBB0_486
	0x48, 0x01, 0xc9, //0x000021b2 addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x000021b5 leaq         (%rcx,%rcx,4), %rcx
	0xff, 0xcb, //0x000021b9 decl         %ebx
	0x39, 0xde, //0x000021bb cmpl         %ebx, %esi
	0x0f, 0x85, 0xef, 0xff, 0xff, 0xff, //0x000021bd jne          LBB0_486
	0x29, 0xdf, //0x000021c3 subl         %ebx, %edi
	//0x000021c5 LBB0_488
	0x41, 0x83, 0xfa, 0x07, //0x000021c5 cmpl         $7, %r10d
	0x49, 0xba, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x000021c9 movabsq      $4503599627370495, %r10
	0x0f, 0x82, 0x15, 0x00, 0x00, 0x00, //0x000021d3 jb           LBB0_491
	0x44, 0x89, 0xde, //0x000021d9 movl         %r11d, %esi
	0x29, 0xfe, //0x000021dc subl         %edi, %esi
	//0x000021de LBB0_490
	0x48, 0x69, 0xc9, 0x00, 0xe1, 0xf5, 0x05, //0x000021de imulq        $100000000, %rcx, %rcx
	0x83, 0xc6, 0xf8, //0x000021e5 addl         $-8, %esi
	0x0f, 0x85, 0xf0, 0xff, 0xff, 0xff, //0x000021e8 jne          LBB0_490
	//0x000021ee LBB0_491
	0x31, 0xff, //0x000021ee xorl         %edi, %edi
	0x45, 0x85, 0xdb, //0x000021f0 testl        %r11d, %r11d
	0x0f, 0x88, 0x66, 0x00, 0x00, 0x00, //0x000021f3 js           LBB0_500
	0x44, 0x39, 0xd8, //0x000021f9 cmpl         %r11d, %eax
	0x0f, 0x8e, 0x5d, 0x00, 0x00, 0x00, //0x000021fc jle          LBB0_500
	0x41, 0x8a, 0x14, 0x14, //0x00002202 movb         (%r12,%rdx), %dl
	0x41, 0x8d, 0x73, 0x01, //0x00002206 leal         $1(%r11), %esi
	0x39, 0xc6, //0x0000220a cmpl         %eax, %esi
	0x0f, 0x85, 0x2a, 0x00, 0x00, 0x00, //0x0000220c jne          LBB0_497
	0x80, 0xfa, 0x35, //0x00002212 cmpb         $53, %dl
	0x0f, 0x85, 0x21, 0x00, 0x00, 0x00, //0x00002215 jne          LBB0_497
	0x45, 0x85, 0xf6, //0x0000221b testl        %r14d, %r14d
	0x40, 0x0f, 0x95, 0xc7, //0x0000221e setne        %dil
	0x41, 0x08, 0xf9, //0x00002222 orb          %dil, %r9b
	0x0f, 0x85, 0x34, 0x00, 0x00, 0x00, //0x00002225 jne          LBB0_500
	0x49, 0x63, 0xc3, //0x0000222b movslq       %r11d, %rax
	0x42, 0x8a, 0x7c, 0x20, 0xff, //0x0000222e movb         $-1(%rax,%r12), %dil
	0x40, 0x80, 0xe7, 0x01, //0x00002233 andb         $1, %dil
	0xe9, 0x23, 0x00, 0x00, 0x00, //0x00002237 jmp          LBB0_500
	//0x0000223c LBB0_497
	0x80, 0xfa, 0x34, //0x0000223c cmpb         $52, %dl
	0x40, 0x0f, 0x9f, 0xc7, //0x0000223f setg         %dil
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00002243 jmp          LBB0_500
	//0x00002248 LBB0_498
	0x31, 0xc9, //0x00002248 xorl         %ecx, %ecx
	0x31, 0xff, //0x0000224a xorl         %edi, %edi
	0xe9, 0x0e, 0x00, 0x00, 0x00, //0x0000224c jmp          LBB0_500
	//0x00002251 LBB0_499
	0x31, 0xc9, //0x00002251 xorl         %ecx, %ecx
	0x31, 0xff, //0x00002253 xorl         %edi, %edi
	0x49, 0xba, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00002255 movabsq      $4503599627370495, %r10
	//0x0000225f LBB0_500
	0x8a, 0x55, 0xd7, //0x0000225f movb         $-41(%rbp), %dl
	0x40, 0x0f, 0xb6, 0xf7, //0x00002262 movzbl       %dil, %esi
	0x48, 0x01, 0xce, //0x00002266 addq         %rcx, %rsi
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, //0x00002269 movabsq      $9007199254740992, %rax
	0x48, 0x39, 0xc6, //0x00002273 cmpq         %rax, %rsi
	0x0f, 0x85, 0x28, 0x00, 0x00, 0x00, //0x00002276 jne          LBB0_503
	0x41, 0x81, 0xf8, 0xfe, 0x03, 0x00, 0x00, //0x0000227c cmpl         $1022, %r8d
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00002283 movabsq      $-9223372036854775808, %rdi
	0x0f, 0x8e, 0x20, 0x00, 0x00, 0x00, //0x0000228d jle          LBB0_504
	0x31, 0xf6, //0x00002293 xorl         %esi, %esi
	0x49, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, //0x00002295 movabsq      $9218868437227405312, %r8
	0xe9, 0x36, 0x00, 0x00, 0x00, //0x0000229f jmp          LBB0_506
	//0x000022a4 LBB0_503
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x000022a4 movabsq      $-9223372036854775808, %rdi
	0xe9, 0x07, 0x00, 0x00, 0x00, //0x000022ae jmp          LBB0_505
	//0x000022b3 LBB0_504
	0x41, 0xff, 0xc0, //0x000022b3 incl         %r8d
	0x49, 0x8d, 0x72, 0x01, //0x000022b6 leaq         $1(%r10), %rsi
	//0x000022ba LBB0_505
	0x49, 0x8d, 0x42, 0x01, //0x000022ba leaq         $1(%r10), %rax
	0x48, 0x21, 0xf0, //0x000022be andq         %rsi, %rax
	0x41, 0x81, 0xc0, 0xff, 0x03, 0x00, 0x00, //0x000022c1 addl         $1023, %r8d
	0x41, 0x81, 0xe0, 0xff, 0x07, 0x00, 0x00, //0x000022c8 andl         $2047, %r8d
	0x49, 0xc1, 0xe0, 0x34, //0x000022cf shlq         $52, %r8
	0x48, 0x85, 0xc0, //0x000022d3 testq        %rax, %rax
	0x4c, 0x0f, 0x44, 0xc0, //0x000022d6 cmoveq       %rax, %r8
	//0x000022da LBB0_506
	0x4c, 0x21, 0xd6, //0x000022da andq         %r10, %rsi
	0x4c, 0x09, 0xc6, //0x000022dd orq          %r8, %rsi
	0x48, 0x89, 0xf3, //0x000022e0 movq         %rsi, %rbx
	0x48, 0x09, 0xfb, //0x000022e3 orq          %rdi, %rbx
	0x80, 0xfa, 0x2d, //0x000022e6 cmpb         $45, %dl
	0x48, 0x0f, 0x45, 0xde, //0x000022e9 cmovneq      %rsi, %rbx
	//0x000022ed LBB0_507
	0x48, 0xff, 0xcf, //0x000022ed decq         %rdi
	0x48, 0x21, 0xdf, //0x000022f0 andq         %rbx, %rdi
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, //0x000022f3 movabsq      $9218868437227405312, %rax
	0x48, 0x39, 0xc7, //0x000022fd cmpq         %rax, %rdi
	0x48, 0x8b, 0x45, 0x98, //0x00002300 movq         $-104(%rbp), %rax
	0x0f, 0x85, 0x07, 0x00, 0x00, 0x00, //0x00002304 jne          LBB0_509
	0x48, 0xc7, 0x00, 0xf8, 0xff, 0xff, 0xff, //0x0000230a movq         $-8, (%rax)
	//0x00002311 LBB0_509
	0x48, 0x89, 0x58, 0x08, //0x00002311 movq         %rbx, $8(%rax)
	0x48, 0x8b, 0x45, 0xa0, //0x00002315 movq         $-96(%rbp), %rax
	0x48, 0x8b, 0x4d, 0xa8, //0x00002319 movq         $-88(%rbp), %rcx
	0x48, 0x89, 0x08, //0x0000231d movq         %rcx, (%rax)
	0xe9, 0x0a, 0xe0, 0xff, 0xff, //0x00002320 jmp          LBB0_51
	0x00, 0x00, 0x00, //0x00002325 .p2align 2, 0x00
	//0x00002328 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00002328 .long 2
	0x00, 0x00, 0x00, 0x00, //0x0000232c .p2align 4, 0x00
	//0x00002330 _P10_TAB
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x3f, //0x00002330 .quad 4607182418800017408
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0x40, //0x00002338 .quad 4621819117588971520
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x59, 0x40, //0x00002340 .quad 4636737291354636288
	0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x8f, 0x40, //0x00002348 .quad 4652007308841189376
	0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 0xc3, 0x40, //0x00002350 .quad 4666723172467343360
	0x00, 0x00, 0x00, 0x00, 0x00, 0x6a, 0xf8, 0x40, //0x00002358 .quad 4681608360884174848
	0x00, 0x00, 0x00, 0x00, 0x80, 0x84, 0x2e, 0x41, //0x00002360 .quad 4696837146684686336
	0x00, 0x00, 0x00, 0x00, 0xd0, 0x12, 0x63, 0x41, //0x00002368 .quad 4711630319722168320
	0x00, 0x00, 0x00, 0x00, 0x84, 0xd7, 0x97, 0x41, //0x00002370 .quad 4726483295884279808
	0x00, 0x00, 0x00, 0x00, 0x65, 0xcd, 0xcd, 0x41, //0x00002378 .quad 4741671816366391296
	0x00, 0x00, 0x00, 0x20, 0x5f, 0xa0, 0x02, 0x42, //0x00002380 .quad 4756540486875873280
	0x00, 0x00, 0x00, 0xe8, 0x76, 0x48, 0x37, 0x42, //0x00002388 .quad 4771362005757984768
	0x00, 0x00, 0x00, 0xa2, 0x94, 0x1a, 0x6d, 0x42, //0x00002390 .quad 4786511204640096256
	0x00, 0x00, 0x40, 0xe5, 0x9c, 0x30, 0xa2, 0x42, //0x00002398 .quad 4801453603149578240
	0x00, 0x00, 0x90, 0x1e, 0xc4, 0xbc, 0xd6, 0x42, //0x000023a0 .quad 4816244402031689728
	0x00, 0x00, 0x34, 0x26, 0xf5, 0x6b, 0x0c, 0x43, //0x000023a8 .quad 4831355200913801216
	0x00, 0x80, 0xe0, 0x37, 0x79, 0xc3, 0x41, 0x43, //0x000023b0 .quad 4846369599423283200
	0x00, 0xa0, 0xd8, 0x85, 0x57, 0x34, 0x76, 0x43, //0x000023b8 .quad 4861130398305394688
	0x00, 0xc8, 0x4e, 0x67, 0x6d, 0xc1, 0xab, 0x43, //0x000023c0 .quad 4876203697187506176
	0x00, 0x3d, 0x91, 0x60, 0xe4, 0x58, 0xe1, 0x43, //0x000023c8 .quad 4891288408196988160
	0x40, 0x8c, 0xb5, 0x78, 0x1d, 0xaf, 0x15, 0x44, //0x000023d0 .quad 4906019910204099648
	0x50, 0xef, 0xe2, 0xd6, 0xe4, 0x1a, 0x4b, 0x44, //0x000023d8 .quad 4921056587992461136
	0x92, 0xd5, 0x4d, 0x06, 0xcf, 0xf0, 0x80, 0x44, //0x000023e0 .quad 4936209963552724370
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000023e8 .p2align 4, 0x00
	//0x000023f0 _POW10_M128_TAB
	0x53, 0xe4, 0x60, 0xcd, 0x69, 0xc8, 0x32, 0x17, //0x000023f0 .quad 1671618768450675795
	0x88, 0x02, 0x1c, 0x08, 0xa0, 0xd5, 0x8f, 0xfa, //0x000023f8 .quad -391859759250406776
	0xb4, 0x8e, 0x5c, 0x20, 0x42, 0xbd, 0x7f, 0x0e, //0x00002400 .quad 1044761730281672372
	0x95, 0x81, 0x11, 0x05, 0x84, 0xe5, 0x99, 0x9c, //0x00002408 .quad -7162441377172586091
	0x61, 0xb2, 0x73, 0xa8, 0x92, 0xac, 0x1f, 0x52, //0x00002410 .quad 5917638181279478369
	0xfa, 0xe1, 0x55, 0x06, 0xe5, 0x5e, 0xc0, 0xc3, //0x00002418 .quad -4341365703038344710
	0xf9, 0x9e, 0x90, 0x52, 0xb7, 0x97, 0xa7, 0xe6, //0x00002420 .quad -1826324310255427847
	0x78, 0x5a, 0xeb, 0x47, 0x9e, 0x76, 0xb0, 0xf4, //0x00002428 .quad -815021110370542984
	0x5c, 0x63, 0x9a, 0x93, 0xd2, 0xbe, 0x28, 0x90, //0x00002430 .quad -8058981721550724260
	0x8b, 0x18, 0xf3, 0xec, 0x22, 0x4a, 0xee, 0x98, //0x00002438 .quad -7426917221622671221
	0x33, 0xfc, 0x80, 0x38, 0x87, 0xee, 0x32, 0x74, //0x00002440 .quad 8373016921771146291
	0xae, 0xde, 0x2f, 0xa8, 0xab, 0xdc, 0x29, 0xbf, //0x00002448 .quad -4671960508600951122
	0x3f, 0x3b, 0xa1, 0x06, 0x29, 0xaa, 0x3f, 0x11, //0x00002450 .quad 1242899115359157055
	0x5a, 0xd6, 0x3b, 0x92, 0xd6, 0x53, 0xf4, 0xee, //0x00002458 .quad -1228264617323800998
	0x07, 0xc5, 0x24, 0xa4, 0x59, 0xca, 0xc7, 0x4a, //0x00002460 .quad 5388497965526861063
	0xf8, 0x65, 0x65, 0x1b, 0x66, 0xb4, 0x58, 0x95, //0x00002468 .quad -7685194413468457480
	0x49, 0xf6, 0x2d, 0x0d, 0xf0, 0xbc, 0x79, 0x5d, //0x00002470 .quad 6735622456908576329
	0x76, 0xbf, 0x3e, 0xa2, 0x7f, 0xe1, 0xae, 0xba, //0x00002478 .quad -4994806998408183946
	0xdc, 0x73, 0x79, 0x10, 0x2c, 0x2c, 0xd8, 0xf4, //0x00002480 .quad -803843965719055396
	0x53, 0x6f, 0xce, 0x8a, 0xdf, 0x99, 0x5a, 0xe9, //0x00002488 .quad -1631822729582842029
	0x69, 0xe8, 0x4b, 0x8a, 0x9b, 0x1b, 0x07, 0x79, //0x00002490 .quad 8720969558280366185
	0x94, 0x05, 0xc1, 0xb6, 0x2b, 0xa0, 0xd8, 0x91, //0x00002498 .quad -7937418233630358124
	0x84, 0xe2, 0xde, 0x6c, 0x82, 0xe2, 0x48, 0x97, //0x000024a0 .quad -7545532125859093884
	0xf9, 0x46, 0x71, 0xa4, 0x36, 0xc8, 0x4e, 0xb6, //0x000024a8 .quad -5310086773610559751
	0x25, 0x9b, 0x16, 0x08, 0x23, 0x1b, 0x1b, 0xfd, //0x000024b0 .quad -208543120469091547
	0xb7, 0x98, 0x8d, 0x4d, 0x44, 0x7a, 0xe2, 0xe3, //0x000024b8 .quad -2025922448585811785
	0xf7, 0x20, 0x0e, 0xe5, 0xf5, 0xf0, 0x30, 0xfe, //0x000024c0 .quad -130339450293182217
	0x72, 0x7f, 0x78, 0xb0, 0x6a, 0x8c, 0x6d, 0x8e, //0x000024c8 .quad -8183730558007214222
	0x35, 0xa9, 0x51, 0x5e, 0x33, 0x2d, 0xbd, 0xbd, //0x000024d0 .quad -4774610331293865675
	0x4f, 0x9f, 0x96, 0x5c, 0x85, 0xef, 0x08, 0xb2, //0x000024d8 .quad -5617977179081629873
	0x82, 0x13, 0xe6, 0x35, 0x80, 0x78, 0x2c, 0xad, //0x000024e0 .quad -5968262914117332094
	0x23, 0x47, 0xbc, 0xb3, 0x66, 0x2b, 0x8b, 0xde, //0x000024e8 .quad -2410785455424649437
	0x31, 0xcc, 0xaf, 0x21, 0x50, 0xcb, 0x3b, 0x4c, //0x000024f0 .quad 5493207715531443249
	0x76, 0xac, 0x55, 0x30, 0x20, 0xfb, 0x16, 0x8b, //0x000024f8 .quad -8424269937281487754
	0x3d, 0xbf, 0x1b, 0x2a, 0x24, 0xbe, 0x4a, 0xdf, //0x00002500 .quad -2356862392440471747
	0x93, 0x17, 0x6b, 0x3c, 0xe8, 0xb9, 0xdc, 0xad, //0x00002508 .quad -5918651403174471789
	0x0d, 0xaf, 0xa2, 0x34, 0xad, 0x6d, 0x1d, 0xd7, //0x00002510 .quad -2946077990550589683
	0x78, 0xdd, 0x85, 0x4b, 0x62, 0xe8, 0x53, 0xd9, //0x00002518 .quad -2786628235540701832
	0x68, 0xad, 0xe5, 0x40, 0x8c, 0x64, 0x72, 0x86, //0x00002520 .quad -8758827771735200408
	0x6b, 0xaa, 0x33, 0x6f, 0x3d, 0x71, 0xd4, 0x87, //0x00002528 .quad -8659171674854020501
	0xc2, 0x18, 0x1f, 0x51, 0xaf, 0xfd, 0x0e, 0x68, //0x00002530 .quad 7498209359040551106
	0x06, 0x95, 0x00, 0xcb, 0x8c, 0x8d, 0xc9, 0xa9, //0x00002538 .quad -6212278575140137722
	0xf2, 0xde, 0x66, 0x25, 0x1b, 0xbd, 0x12, 0x02, //0x00002540 .quad 149389661945913074
	0x48, 0xba, 0xc0, 0xfd, 0xef, 0xf0, 0x3b, 0xd4, //0x00002548 .quad -3153662200497784248
	0x57, 0x4b, 0x60, 0xf7, 0x30, 0xb6, 0x4b, 0x01, //0x00002550 .quad 93368538716195671
	0x6d, 0x74, 0x98, 0xfe, 0x95, 0x76, 0xa5, 0x84, //0x00002558 .quad -8888567902952197011
	0x2d, 0x5e, 0x38, 0x35, 0xbd, 0xa3, 0x9e, 0x41, //0x00002560 .quad 4728396691822632493
	0x88, 0x91, 0x3e, 0x7e, 0x3b, 0xd4, 0xce, 0xa5, //0x00002568 .quad -6499023860262858360
	0xb9, 0x75, 0x86, 0x82, 0xac, 0x4c, 0x06, 0x52, //0x00002570 .quad 5910495864778290617
	0xea, 0x35, 0xce, 0x5d, 0x4a, 0x89, 0x42, 0xcf, //0x00002578 .quad -3512093806901185046
	0x93, 0x09, 0x94, 0xd1, 0xeb, 0xef, 0x43, 0x73, //0x00002580 .quad 8305745933913819539
	0xb2, 0xe1, 0xa0, 0x7a, 0xce, 0x95, 0x89, 0x81, //0x00002588 .quad -9112587656954322510
	0xf8, 0x0b, 0xf9, 0xc5, 0xe6, 0xeb, 0x14, 0x10, //0x00002590 .quad 1158810380537498616
	0x1f, 0x1a, 0x49, 0x19, 0x42, 0xfb, 0xeb, 0xa1, //0x00002598 .quad -6779048552765515233
	0xf6, 0x4e, 0x77, 0x77, 0xe0, 0x26, 0x1a, 0xd4, //0x000025a0 .quad -3163173042755514634
	0xa6, 0x60, 0x9b, 0x9f, 0x12, 0xfa, 0x66, 0xca, //0x000025a8 .quad -3862124672529506138
	0xb4, 0x22, 0x55, 0x95, 0x98, 0xb0, 0x20, 0x89, //0x000025b0 .quad -8565652321871781196
	0xd0, 0x38, 0x82, 0x47, 0x97, 0xb8, 0x00, 0xfd, //0x000025b8 .quad -215969822234494768
	0xb0, 0x35, 0x55, 0x5d, 0x5f, 0x6e, 0xb4, 0x55, //0x000025c0 .quad 6175682344898606512
	0x82, 0x63, 0xb1, 0x8c, 0x5e, 0x73, 0x20, 0x9e, //0x000025c8 .quad -7052510166537641086
	0x1d, 0x83, 0xaa, 0x34, 0xf7, 0x89, 0x21, 0xeb, //0x000025d0 .quad -1503769105731517667
	0x62, 0xbc, 0xdd, 0x2f, 0x36, 0x90, 0xa8, 0xc5, //0x000025d8 .quad -4203951689744663454
	0xe4, 0x23, 0xd5, 0x01, 0x75, 0xec, 0xe9, 0xa5, //0x000025e0 .quad -6491397400591784988
	0x7b, 0x2b, 0xd5, 0xbb, 0x43, 0xb4, 0x12, 0xf7, //0x000025e8 .quad -643253593753441413
	0x6e, 0x36, 0x25, 0x21, 0xc9, 0x33, 0xb2, 0x47, //0x000025f0 .quad 5166248661484910190
	0x2d, 0x3b, 0x65, 0x55, 0xaa, 0xb0, 0x6b, 0x9a, //0x000025f8 .quad -7319562523736982739
	0x0a, 0x84, 0x6e, 0x69, 0xbb, 0xc0, 0x9e, 0x99, //0x00002600 .quad -7377247228426025974
	0xf8, 0x89, 0xbe, 0xea, 0xd4, 0x9c, 0x06, 0xc1, //0x00002608 .quad -4537767136243840520
	0x0d, 0x25, 0xca, 0x43, 0xea, 0x70, 0x06, 0xc0, //0x00002610 .quad -4609873017105144563
	0x76, 0x2c, 0x6e, 0x25, 0x0a, 0x44, 0x48, 0xf1, //0x00002618 .quad -1060522901877412746
	0x28, 0x57, 0x5e, 0x6a, 0x92, 0x06, 0x04, 0x38, //0x00002620 .quad 4036358391950366504
	0xca, 0xdb, 0x64, 0x57, 0x86, 0x2a, 0xcd, 0x96, //0x00002628 .quad -7580355841314464822
	0xf2, 0xec, 0xf5, 0x04, 0x37, 0x08, 0x05, 0xc6, //0x00002630 .quad -4177924046916817678
	0xbc, 0x12, 0x3e, 0xed, 0x27, 0x75, 0x80, 0xbc, //0x00002638 .quad -4863758783215693124
	0x2e, 0x68, 0x33, 0xc6, 0x44, 0x4a, 0x86, 0xf7, //0x00002640 .quad -610719040218634194
	0x6b, 0x97, 0x8d, 0xe8, 0x71, 0x92, 0xa0, 0xeb, //0x00002648 .quad -1468012460592228501
	0x1d, 0x21, 0xe0, 0xfb, 0x6a, 0xee, 0xb3, 0x7a, //0x00002650 .quad 8841672636718129437
	0xa3, 0x7e, 0x58, 0x31, 0x87, 0x5b, 0x44, 0x93, //0x00002658 .quad -7835036815511224669
	0x64, 0x29, 0xd8, 0xba, 0x05, 0xea, 0x60, 0x59, //0x00002660 .quad 6440404777470273892
	0x4c, 0x9e, 0xae, 0xfd, 0x68, 0x72, 0x15, 0xb8, //0x00002668 .quad -5182110000961642932
	0xbd, 0x33, 0x8e, 0x29, 0x87, 0x24, 0xb9, 0x6f, //0x00002670 .quad 8050505971837842365
	0xdf, 0x45, 0x1a, 0x3d, 0x03, 0xcf, 0x1a, 0xe6, //0x00002678 .quad -1865951482774665761
	0x56, 0xe0, 0xf8, 0x79, 0xd4, 0xb6, 0xd3, 0xa5, //0x00002680 .quad -6497648813669818282
	0xab, 0x6b, 0x30, 0x06, 0x62, 0xc1, 0xd0, 0x8f, //0x00002688 .quad -8083748704375247957
	0x6c, 0x18, 0x77, 0x98, 0x89, 0xa4, 0x48, 0x8f, //0x00002690 .quad -8122061017087272852
	0x96, 0x86, 0xbc, 0x87, 0xba, 0xf1, 0xc4, 0xb3, //0x00002698 .quad -5492999862041672042
	0x87, 0xde, 0x94, 0xfe, 0xab, 0xcd, 0x1a, 0x33, //0x000026a0 .quad 3682481783923072647
	0x3c, 0xa8, 0xab, 0x29, 0x29, 0x2e, 0xb6, 0xe0, //0x000026a8 .quad -2254563809124702148
	0x14, 0x0b, 0x1d, 0x7f, 0x8b, 0xc0, 0xf0, 0x9f, //0x000026b0 .quad -6921820921902855404
	0x25, 0x49, 0x0b, 0xba, 0xd9, 0xdc, 0x71, 0x8c, //0x000026b8 .quad -8326631408344020699
	0xd9, 0x4d, 0xe4, 0x5e, 0xae, 0xf0, 0xec, 0x07, //0x000026c0 .quad 571095884476206553
	0x6f, 0x1b, 0x8e, 0x28, 0x10, 0x54, 0x8e, 0xaf, //0x000026c8 .quad -5796603242002637969
	0x50, 0x61, 0x9d, 0xf6, 0xd9, 0x2c, 0xe8, 0xc9, //0x000026d0 .quad -3897816162832129712
	0x4a, 0xa2, 0xb1, 0x32, 0x14, 0xe9, 0x71, 0xdb, //0x000026d8 .quad -2634068034075909558
	0xd2, 0x5c, 0x22, 0x3a, 0x08, 0x1c, 0x31, 0xbe, //0x000026e0 .quad -4741978110983775022
	0x6e, 0x05, 0xaf, 0x9f, 0xac, 0x31, 0x27, 0x89, //0x000026e8 .quad -8563821548938525330
	0x06, 0xf4, 0xaa, 0x48, 0x0a, 0x63, 0xbd, 0x6d, //0x000026f0 .quad 7907585416552444934
	0xca, 0xc6, 0x9a, 0xc7, 0x17, 0xfe, 0x70, 0xab, //0x000026f8 .quad -6093090917745768758
	0x08, 0xb1, 0xd5, 0xda, 0xcc, 0xbb, 0x2c, 0x09, //0x00002700 .quad 661109733835780360
	0x7d, 0x78, 0x81, 0xb9, 0x9d, 0x3d, 0x4d, 0xd6, //0x00002708 .quad -3004677628754823043
	0xa5, 0x8e, 0xc5, 0x08, 0x60, 0xf5, 0xbb, 0x25, //0x00002710 .quad 2719036592861056677
	0x4e, 0xeb, 0xf0, 0x93, 0x82, 0x46, 0xf0, 0x85, //0x00002718 .quad -8795452545612846258
	0x4e, 0xf2, 0xf6, 0x0a, 0xb8, 0xf2, 0x2a, 0xaf, //0x00002720 .quad -5824576295778454962
	0x21, 0x26, 0xed, 0x38, 0x23, 0x58, 0x6c, 0xa7, //0x00002728 .quad -6382629663588669919
	0xe1, 0xae, 0xb4, 0x0d, 0x66, 0xaf, 0xf5, 0x1a, //0x00002730 .quad 1942651667131707105
	0xaa, 0x6f, 0x28, 0x07, 0x2c, 0x6e, 0x47, 0xd1, //0x00002738 .quad -3366601061058449494
	0x4d, 0xed, 0x90, 0xc8, 0x9f, 0x8d, 0xd9, 0x50, //0x00002740 .quad 5825843310384704845
	0xca, 0x45, 0x79, 0x84, 0xdb, 0xa4, 0xcc, 0x82, //0x00002748 .quad -9021654690802612790
	0xa0, 0x28, 0xb5, 0xba, 0x07, 0xf1, 0x0f, 0xe5, //0x00002750 .quad -1941067898873894752
	0x3c, 0x97, 0x97, 0x65, 0x12, 0xce, 0x7f, 0xa3, //0x00002758 .quad -6665382345075878084
	0xc8, 0x72, 0x62, 0xa9, 0x49, 0xed, 0x53, 0x1e, //0x00002760 .quad 2185351144835019464
	0x0c, 0x7d, 0xfd, 0xfe, 0x96, 0xc1, 0x5f, 0xcc, //0x00002768 .quad -3720041912917459700
	0x7a, 0x0f, 0xbb, 0x13, 0x9c, 0xe8, 0xe8, 0x25, //0x00002770 .quad 2731688931043774330
	0x4f, 0xdc, 0xbc, 0xbe, 0xfc, 0xb1, 0x77, 0xff, //0x00002778 .quad -38366372719436721
	0xac, 0xe9, 0x54, 0x8c, 0x61, 0x91, 0xb1, 0x77, //0x00002780 .quad 8624834609543440812
	0xb1, 0x09, 0x36, 0xf7, 0x3d, 0xcf, 0xaa, 0x9f, //0x00002788 .quad -6941508010590729807
	0x17, 0x24, 0x6a, 0xef, 0xb9, 0xf5, 0x9d, 0xd5, //0x00002790 .quad -3054014793352862697
	0x1d, 0x8c, 0x03, 0x75, 0x0d, 0x83, 0x95, 0xc7, //0x00002798 .quad -4065198994811024355
	0x1d, 0xad, 0x44, 0x6b, 0x28, 0x73, 0x05, 0x4b, //0x000027a0 .quad 5405853545163697437
	0x25, 0x6f, 0x44, 0xd2, 0xd0, 0xe3, 0x7a, 0xf9, //0x000027a8 .quad -469812725086392539
	0x32, 0xec, 0x0a, 0x43, 0xf9, 0x67, 0xe3, 0x4e, //0x000027b0 .quad 5684501474941004850
	0x77, 0xc5, 0x6a, 0x83, 0x62, 0xce, 0xec, 0x9b, //0x000027b8 .quad -7211161980820077193
	0x3f, 0xa7, 0xcd, 0x93, 0xf7, 0x41, 0x9c, 0x22, //0x000027c0 .quad 2493940825248868159
	0xd5, 0x76, 0x45, 0x24, 0xfb, 0x01, 0xe8, 0xc2, //0x000027c8 .quad -4402266457597708587
	0x0f, 0x11, 0xc1, 0x78, 0x75, 0x52, 0x43, 0x6b, //0x000027d0 .quad 7729112049988473103
	0x8a, 0xd4, 0x56, 0xed, 0x79, 0x02, 0xa2, 0xf3, //0x000027d8 .quad -891147053569747830
	0xa9, 0xaa, 0x78, 0x6b, 0x89, 0x13, 0x0a, 0x83, //0x000027e0 .quad -9004363024039368023
	0xd6, 0x44, 0x56, 0x34, 0x8c, 0x41, 0x45, 0x98, //0x000027e8 .quad -7474495936122174250
	0x53, 0xd5, 0x56, 0xc6, 0x6b, 0x98, 0xcc, 0x23, //0x000027f0 .quad 2579604275232953683
	0x0c, 0xd6, 0x6b, 0x41, 0xef, 0x91, 0x56, 0xbe, //0x000027f8 .quad -4731433901725329908
	0xa8, 0x8a, 0xec, 0xb7, 0x86, 0xbe, 0xbf, 0x2c, //0x00002800 .quad 3224505344041192104
	0x8f, 0xcb, 0xc6, 0x11, 0x6b, 0x36, 0xec, 0xed, //0x00002808 .quad -1302606358729274481
	0xa9, 0xd6, 0xf3, 0x32, 0x14, 0xd7, 0xf7, 0x7b, //0x00002810 .quad 8932844867666826921
	0x39, 0x3f, 0x1c, 0xeb, 0x02, 0xa2, 0xb3, 0x94, //0x00002818 .quad -7731658001846878407
	0x53, 0xcc, 0xb0, 0x3f, 0xd9, 0xcc, 0xf5, 0xda, //0x00002820 .quad -2669001970698630061
	0x07, 0x4f, 0xe3, 0xa5, 0x83, 0x8a, 0xe0, 0xb9, //0x00002828 .quad -5052886483881210105
	0x68, 0xff, 0x9c, 0x8f, 0x0f, 0x40, 0xb3, 0xd1, //0x00002830 .quad -3336252463373287576
	0xc9, 0x22, 0x5c, 0x8f, 0x24, 0xad, 0x58, 0xe8, //0x00002838 .quad -1704422086424124727
	0xa1, 0x1f, 0xc2, 0xb9, 0x09, 0x08, 0x10, 0x23, //0x00002840 .quad 2526528228819083169
	0xbe, 0x95, 0x99, 0xd9, 0x36, 0x6c, 0x37, 0x91, //0x00002848 .quad -7982792831656159810
	0x8a, 0xa7, 0x32, 0x28, 0x0c, 0x0a, 0xd4, 0xab, //0x00002850 .quad -6065211750830921846
	0x2d, 0xfb, 0xff, 0x8f, 0x44, 0x47, 0x85, 0xb5, //0x00002858 .quad -5366805021142811859
	0x6c, 0x51, 0x3f, 0x32, 0x8f, 0x0c, 0xc9, 0x16, //0x00002860 .quad 1641857348316123500
	0xf9, 0xf9, 0xff, 0xb3, 0x15, 0x99, 0xe6, 0xe2, //0x00002868 .quad -2096820258001126919
	0xe3, 0x92, 0x67, 0x7f, 0xd9, 0xa7, 0x3d, 0xae, //0x00002870 .quad -5891368184943504669
	0x3b, 0xfc, 0x7f, 0x90, 0xad, 0x1f, 0xd0, 0x8d, //0x00002878 .quad -8228041688891786181
	0x9c, 0x77, 0x41, 0xdf, 0xcf, 0x11, 0xcd, 0x99, //0x00002880 .quad -7364210231179380836
	0x4a, 0xfb, 0x9f, 0xf4, 0x98, 0x27, 0x44, 0xb1, //0x00002888 .quad -5673366092687344822
	0x83, 0xd5, 0x11, 0xd7, 0x43, 0x56, 0x40, 0x40, //0x00002890 .quad 4629795266307937667
	0x1d, 0xfa, 0xc7, 0x31, 0x7f, 0x31, 0x95, 0xdd, //0x00002898 .quad -2480021597431793123
	0x72, 0x25, 0x6b, 0x66, 0xea, 0x35, 0x28, 0x48, //0x000028a0 .quad 5199465050656154994
	0x52, 0xfc, 0x1c, 0x7f, 0xef, 0x3e, 0x7d, 0x8a, //0x000028a8 .quad -8467542526035952558
	0xcf, 0xee, 0x05, 0x00, 0x65, 0x43, 0x32, 0xda, //0x000028b0 .quad -2724040723534582065
	0x66, 0x3b, 0xe4, 0x5e, 0xab, 0x8e, 0x1c, 0xad, //0x000028b8 .quad -5972742139117552794
	0x82, 0x6a, 0x07, 0x40, 0x3e, 0xd4, 0xbe, 0x90, //0x000028c0 .quad -8016736922845615486
	0x40, 0x4a, 0x9d, 0x36, 0x56, 0xb2, 0x63, 0xd8, //0x000028c8 .quad -2854241655469553088
	0x91, 0xa2, 0x04, 0xe8, 0xa6, 0x44, 0x77, 0x5a, //0x000028d0 .quad 6518754469289960081
	0x68, 0x4e, 0x22, 0xe2, 0x75, 0x4f, 0x3e, 0x87, //0x000028d8 .quad -8701430062309552536
	0x36, 0xcb, 0x05, 0xa2, 0xd0, 0x15, 0x15, 0x71, //0x000028e0 .quad 8148443086612450102
	0x02, 0xe2, 0xaa, 0x5a, 0x53, 0xe3, 0x0d, 0xa9, //0x000028e8 .quad -6265101559459552766
	0x03, 0x3e, 0x87, 0xca, 0x44, 0x5b, 0x5a, 0x0d, //0x000028f0 .quad 962181821410786819
	0x83, 0x9a, 0x55, 0x31, 0x28, 0x5c, 0x51, 0xd3, //0x000028f8 .quad -3219690930897053053
	0xc2, 0x86, 0x94, 0xfe, 0x0a, 0x79, 0x58, 0xe8, //0x00002900 .quad -1704479370831952190
	0x91, 0x80, 0xd5, 0x1e, 0x99, 0xd9, 0x12, 0x84, //0x00002908 .quad -8929835859451740015
	0x72, 0xa8, 0x39, 0xbe, 0x4d, 0x97, 0x6e, 0x62, //0x00002910 .quad 7092772823314835570
	0xb6, 0xe0, 0x8a, 0x66, 0xff, 0x8f, 0x17, 0xa5, //0x00002918 .quad -6550608805887287114
	0x8f, 0x12, 0xc8, 0x2d, 0x21, 0x3d, 0x0a, 0xfb, //0x00002920 .quad -357406007711231345
	0xe3, 0x98, 0x2d, 0x40, 0xff, 0x73, 0x5d, 0xce, //0x00002928 .quad -3576574988931720989
	0x99, 0x0b, 0x9d, 0xbc, 0x34, 0x66, 0xe6, 0x7c, //0x00002930 .quad 8999993282035256217
	0x8e, 0x7f, 0x1c, 0x88, 0x7f, 0x68, 0xfa, 0x80, //0x00002938 .quad -9152888395723407474
	0x80, 0x4e, 0xc4, 0xeb, 0xc1, 0xff, 0x1f, 0x1c, //0x00002940 .quad 2026619565689294464
	0x72, 0x9f, 0x23, 0x6a, 0x9f, 0x02, 0x39, 0xa1, //0x00002948 .quad -6829424476226871438
	0x20, 0x62, 0xb5, 0x66, 0xb2, 0xff, 0x27, 0xa3, //0x00002950 .quad -6690097579743157728
	0x4e, 0x87, 0xac, 0x44, 0x47, 0x43, 0x87, 0xc9, //0x00002958 .quad -3925094576856201394
	0xa8, 0xba, 0x62, 0x00, 0x9f, 0xff, 0xf1, 0x4b, //0x00002960 .quad 5472436080603216552
	0x22, 0xa9, 0xd7, 0x15, 0x19, 0x14, 0xe9, 0xfb, //0x00002968 .quad -294682202642863838
	0xa9, 0xb4, 0x3d, 0x60, 0xc3, 0x3f, 0x77, 0x6f, //0x00002970 .quad 8031958568804398249
	0xb5, 0xc9, 0xa6, 0xad, 0x8f, 0xac, 0x71, 0x9d, //0x00002978 .quad -7101705404292871755
	0xd3, 0x21, 0x4d, 0x38, 0xb4, 0x0f, 0x55, 0xcb, //0x00002980 .quad -3795109844276665901
	0x22, 0x7c, 0x10, 0x99, 0xb3, 0x17, 0xce, 0xc4, //0x00002988 .quad -4265445736938701790
	0x48, 0x6a, 0x60, 0x46, 0xa1, 0x53, 0x2a, 0x7e, //0x00002990 .quad 9091170749936331336
	0x2b, 0x9b, 0x54, 0x7f, 0xa0, 0x9d, 0x01, 0xf6, //0x00002998 .quad -720121152745989333
	0x6d, 0x42, 0xfc, 0xcb, 0x44, 0x74, 0xda, 0x2e, //0x000029a0 .quad 3376138709496513133
	0xfb, 0xe0, 0x94, 0x4f, 0x84, 0x02, 0xc1, 0x99, //0x000029a8 .quad -7367604748107325189
	0x08, 0x53, 0xfb, 0xfe, 0x55, 0x11, 0x91, 0xfa, //0x000029b0 .quad -391512631556746488
	0x39, 0x19, 0x7a, 0x63, 0x25, 0x43, 0x31, 0xc0, //0x000029b8 .quad -4597819916706768583
	0xca, 0x27, 0xba, 0x7e, 0xab, 0x55, 0x35, 0x79, //0x000029c0 .quad 8733981247408842698
	0x88, 0x9f, 0x58, 0xbc, 0xee, 0x93, 0x3d, 0xf0, //0x000029c8 .quad -1135588877456072824
	0xde, 0x58, 0x34, 0x2f, 0x8b, 0x55, 0xc1, 0x4b, //0x000029d0 .quad 5458738279630526686
	0xb5, 0x63, 0xb7, 0x35, 0x75, 0x7c, 0x26, 0x96, //0x000029d8 .quad -7627272076051127371
	0x16, 0x6f, 0x01, 0xfb, 0xed, 0xaa, 0xb1, 0x9e, //0x000029e0 .quad -7011635205744005354
	0xa2, 0x3c, 0x25, 0x83, 0x92, 0x1b, 0xb0, 0xbb, //0x000029e8 .quad -4922404076636521310
	0xdc, 0xca, 0xc1, 0x79, 0xa9, 0x15, 0x5e, 0x46, //0x000029f0 .quad 5070514048102157020
	0xcb, 0x8b, 0xee, 0x23, 0x77, 0x22, 0x9c, 0xea, //0x000029f8 .quad -1541319077368263733
	0xc9, 0x1e, 0x19, 0xec, 0x89, 0xcd, 0xfa, 0x0b, //0x00002a00 .quad 863228270850154185
	0x5f, 0x17, 0x75, 0x76, 0x8a, 0x95, 0xa1, 0x92, //0x00002a08 .quad -7880853450996246689
	0x7b, 0x66, 0x1f, 0x67, 0xec, 0x80, 0xf9, 0xce, //0x00002a10 .quad -3532650679864695173
	0x36, 0x5d, 0x12, 0x14, 0xed, 0xfa, 0x49, 0xb7, //0x00002a18 .quad -5239380795317920458
	0x1a, 0x40, 0xe7, 0x80, 0x27, 0xe1, 0xb7, 0x82, //0x00002a20 .quad -9027499368258256870
	0x84, 0xf4, 0x16, 0x59, 0xa8, 0x79, 0x1c, 0xe5, //0x00002a28 .quad -1937539975720012668
	0x10, 0x88, 0x90, 0xb0, 0xb8, 0xec, 0xb2, 0xd1, //0x00002a30 .quad -3336344095947716592
	0xd2, 0x58, 0xae, 0x37, 0x09, 0xcc, 0x31, 0x8f, //0x00002a38 .quad -8128491512466089774
	0x15, 0xaa, 0xb4, 0xdc, 0xe6, 0xa7, 0x1f, 0x86, //0x00002a40 .quad -8782116138362033643
	0x07, 0xef, 0x99, 0x85, 0x0b, 0x3f, 0xfe, 0xb2, //0x00002a48 .quad -5548928372155224313
	0x9a, 0xd4, 0xe1, 0x93, 0xe0, 0x91, 0xa7, 0x67, //0x00002a50 .quad 7469098900757009562
	0xc9, 0x6a, 0x00, 0x67, 0xce, 0xce, 0xbd, 0xdf, //0x00002a58 .quad -2324474446766642487
	0xe0, 0x24, 0x6d, 0x5c, 0x2c, 0xbb, 0xc8, 0xe0, //0x00002a60 .quad -2249342214667950880
	0xbd, 0x42, 0x60, 0x00, 0x41, 0xa1, 0xd6, 0x8b, //0x00002a68 .quad -8370325556870233411
	0x18, 0x6e, 0x88, 0x73, 0xf7, 0xe9, 0xfa, 0x58, //0x00002a70 .quad 6411694268519837208
	0x6d, 0x53, 0x78, 0x40, 0x91, 0x49, 0xcc, 0xae, //0x00002a78 .quad -5851220927660403859
	0x9e, 0x89, 0x6a, 0x50, 0x75, 0xa4, 0x39, 0xaf, //0x00002a80 .quad -5820440219632367202
	0x48, 0x68, 0x96, 0x90, 0xf5, 0x5b, 0x7f, 0xda, //0x00002a88 .quad -2702340141148116920
	0x03, 0x96, 0x42, 0x52, 0xc9, 0x06, 0x84, 0x6d, //0x00002a90 .quad 7891439908798240259
	0x2d, 0x01, 0x5e, 0x7a, 0x79, 0x99, 0x8f, 0x88, //0x00002a98 .quad -8606491615858654931
	0x83, 0x3b, 0xd3, 0xa6, 0x7b, 0x08, 0xe5, 0xc8, //0x00002aa0 .quad -3970758169284363389
	0x78, 0x81, 0xf5, 0xd8, 0xd7, 0x7f, 0xb3, 0xaa, //0x00002aa8 .quad -6146428501395930760
	0x64, 0x0a, 0x88, 0x90, 0x9a, 0x4a, 0x1e, 0xfb, //0x00002ab0 .quad -351761693178066332
	0xd6, 0xe1, 0x32, 0xcf, 0xcd, 0x5f, 0x60, 0xd5, //0x00002ab8 .quad -3071349608317525546
	0x7f, 0x06, 0x55, 0x9a, 0xa0, 0xee, 0xf2, 0x5c, //0x00002ac0 .quad 6697677969404790399
	0x26, 0xcd, 0x7f, 0xa1, 0xe0, 0x3b, 0x5c, 0x85, //0x00002ac8 .quad -8837122532839535322
	0x1e, 0x48, 0xea, 0xc0, 0x48, 0xaa, 0x2f, 0xf4, //0x00002ad0 .quad -851274575098787810
	0x6f, 0xc0, 0xdf, 0xc9, 0xd8, 0x4a, 0xb3, 0xa6, //0x00002ad8 .quad -6434717147622031249
	0x26, 0xda, 0x24, 0xf1, 0xda, 0x94, 0x3b, 0xf1, //0x00002ae0 .quad -1064093218873484762
	0x8b, 0xb0, 0x57, 0xfc, 0x8e, 0x1d, 0x60, 0xd0, //0x00002ae8 .quad -3431710416100151157
	0x58, 0x08, 0xb7, 0xd6, 0x08, 0x3d, 0xc5, 0x76, //0x00002af0 .quad 8558313775058847832
	0x57, 0xce, 0xb6, 0x5d, 0x79, 0x12, 0x3c, 0x82, //0x00002af8 .quad -9062348037703676329
	0x6e, 0xca, 0x64, 0x0c, 0x4b, 0x8c, 0x76, 0x54, //0x00002b00 .quad 6086206200396171886
	0xed, 0x81, 0x24, 0xb5, 0x17, 0x17, 0xcb, 0xa2, //0x00002b08 .quad -6716249028702207507
	0x09, 0xfd, 0x7d, 0xcf, 0x5d, 0x2f, 0x94, 0xa9, //0x00002b10 .quad -6227300304786948855
	0x68, 0xa2, 0x6d, 0xa2, 0xdd, 0xdc, 0x7d, 0xcb, //0x00002b18 .quad -3783625267450371480
	0x4c, 0x7c, 0x5d, 0x43, 0x35, 0x3b, 0xf9, 0xd3, //0x00002b20 .quad -3172439362556298164
	0x02, 0x0b, 0x09, 0x0b, 0x15, 0x54, 0x5d, 0xfe, //0x00002b28 .quad -117845565885576446
	0xaf, 0x6d, 0x1a, 0x4a, 0x01, 0xc5, 0x7b, 0xc4, //0x00002b30 .quad -4288617610811380305
	0xe1, 0xa6, 0xe5, 0x26, 0x8d, 0x54, 0xfa, 0x9e, //0x00002b38 .quad -6991182506319567135
	0x1b, 0x09, 0xa1, 0x9c, 0x41, 0xb6, 0x9a, 0x35, //0x00002b40 .quad 3862600023340550427
	0x9a, 0x10, 0x9f, 0x70, 0xb0, 0xe9, 0xb8, 0xc6, //0x00002b48 .quad -4127292114472071014
	0x62, 0x4b, 0xc9, 0x03, 0xd2, 0x63, 0x01, 0xc3, //0x00002b50 .quad -4395122007679087774
	0xc0, 0xd4, 0xc6, 0x8c, 0x1c, 0x24, 0x67, 0xf8, //0x00002b58 .quad -547429124662700864
	0x1d, 0xcf, 0x5d, 0x42, 0x63, 0xde, 0xe0, 0x79, //0x00002b60 .quad 8782263791269039901
	0xf8, 0x44, 0xfc, 0xd7, 0x91, 0x76, 0x40, 0x9b, //0x00002b68 .quad -7259672230555269896
	0xe4, 0x42, 0xf5, 0x12, 0xfc, 0x15, 0x59, 0x98, //0x00002b70 .quad -7468914334623251740
	0x36, 0x56, 0xfb, 0x4d, 0x36, 0x94, 0x10, 0xc2, //0x00002b78 .quad -4462904269766699466
	0x9d, 0x93, 0xb2, 0x17, 0x7b, 0x5b, 0x6f, 0x3e, //0x00002b80 .quad 4498915137003099037
	0xc4, 0x2b, 0x7a, 0xe1, 0x43, 0xb9, 0x94, 0xf2, //0x00002b88 .quad -966944318780986428
	0x42, 0x9c, 0xcf, 0xee, 0x2c, 0x99, 0x05, 0xa7, //0x00002b90 .quad -6411550076227838910
	0x5a, 0x5b, 0xec, 0x6c, 0xca, 0xf3, 0x9c, 0x97, //0x00002b98 .quad -7521869226879198374
	0x53, 0x83, 0x83, 0x2a, 0x78, 0xff, 0xc6, 0x50, //0x00002ba0 .quad 5820620459997365075
	0x31, 0x72, 0x27, 0x08, 0xbd, 0x30, 0x84, 0xbd, //0x00002ba8 .quad -4790650515171610063
	0x28, 0x64, 0x24, 0x35, 0x56, 0xbf, 0xf8, 0xa4, //0x00002bb0 .quad -6559282480285457368
	0xbd, 0x4e, 0x31, 0x4a, 0xec, 0x3c, 0xe5, 0xec, //0x00002bb8 .quad -1376627125537124675
	0x99, 0xbe, 0x36, 0xe1, 0x95, 0x77, 0x1b, 0x87, //0x00002bc0 .quad -8711237568605798759
	0x36, 0xd1, 0x5e, 0xae, 0x13, 0x46, 0x0f, 0x94, //0x00002bc8 .quad -7777920981101784778
	0x3f, 0x6e, 0x84, 0x59, 0x7b, 0x55, 0xe2, 0x28, //0x00002bd0 .quad 2946011094524915263
	0x84, 0x85, 0xf6, 0x99, 0x98, 0x17, 0x13, 0xb9, //0x00002bd8 .quad -5110715207949843068
	0xcf, 0x89, 0xe5, 0x2f, 0xda, 0xea, 0x1a, 0x33, //0x00002be0 .quad 3682513868156144079
	0xe5, 0x26, 0x74, 0xc0, 0x7e, 0xdd, 0x57, 0xe7, //0x00002be8 .quad -1776707991509915931
	0x21, 0x76, 0xef, 0x5d, 0xc8, 0xd2, 0xf0, 0x3f, //0x00002bf0 .quad 4607414176811284001
	0x4f, 0x98, 0x48, 0x38, 0x6f, 0xea, 0x96, 0x90, //0x00002bf8 .quad -8027971522334779313
	0xa9, 0x53, 0x6b, 0x75, 0x7a, 0x07, 0xed, 0x0f, //0x00002c00 .quad 1147581702586717097
	0x63, 0xbe, 0x5a, 0x06, 0x0b, 0xa5, 0xbc, 0xb4, //0x00002c08 .quad -5423278384491086237
	0x94, 0x28, 0xc6, 0x12, 0x59, 0x49, 0xe8, 0xd3, //0x00002c10 .quad -3177208890193991532
	0xfb, 0x6d, 0xf1, 0xc7, 0x4d, 0xce, 0xeb, 0xe1, //0x00002c18 .quad -2167411962186469893
	0x5c, 0xd9, 0xbb, 0xab, 0xd7, 0x2d, 0x71, 0x64, //0x00002c20 .quad 7237616480483531100
	0xbd, 0xe4, 0xf6, 0x9c, 0xf0, 0x60, 0x33, 0x8d, //0x00002c28 .quad -8272161504007625539
	0xb3, 0xcf, 0xaa, 0x96, 0x4d, 0x79, 0x8d, 0xbd, //0x00002c30 .quad -4788037454677749837
	0xec, 0x9d, 0x34, 0xc4, 0x2c, 0x39, 0x80, 0xb0, //0x00002c38 .quad -5728515861582144020
	0xa0, 0x83, 0x55, 0xfc, 0xa0, 0xd7, 0xf0, 0xec, //0x00002c40 .quad -1373360799919799392
	0x67, 0xc5, 0x41, 0xf5, 0x77, 0x47, 0xa0, 0xdc, //0x00002c48 .quad -2548958808550292121
	0x44, 0x72, 0xb5, 0x9d, 0xc4, 0x86, 0x16, 0xf4, //0x00002c50 .quad -858350499949874620
	0x60, 0x1b, 0x49, 0xf9, 0xaa, 0x2c, 0xe4, 0x89, //0x00002c58 .quad -8510628282985014432
	0xd5, 0xce, 0x22, 0xc5, 0x75, 0x28, 0x1c, 0x31, //0x00002c60 .quad 3538747893490044629
	0x39, 0x62, 0x9b, 0xb7, 0xd5, 0x37, 0x5d, 0xac, //0x00002c68 .quad -6026599335303880135
	0x8b, 0x82, 0x6b, 0x36, 0x93, 0x32, 0x63, 0x7d, //0x00002c70 .quad 9035120885289943691
	0xc7, 0x3a, 0x82, 0x25, 0xcb, 0x85, 0x74, 0xd7, //0x00002c78 .quad -2921563150702462265
	0x97, 0x31, 0x03, 0x02, 0x9c, 0xff, 0x5d, 0xae, //0x00002c80 .quad -5882264492762254953
	0xbc, 0x64, 0x71, 0xf7, 0x9e, 0xd3, 0xa8, 0x86, //0x00002c88 .quad -8743505996830120772
	0xfc, 0xfd, 0x83, 0x02, 0x83, 0x7f, 0xf5, 0xd9, //0x00002c90 .quad -2741144597525430788
	0xeb, 0xbd, 0x4d, 0xb5, 0x86, 0x08, 0x53, 0xa8, //0x00002c98 .quad -6317696477610263061
	0x7b, 0xfd, 0x24, 0xc3, 0x63, 0xdf, 0x72, 0xd0, //0x00002ca0 .quad -3426430746906788485
	0x66, 0x2d, 0xa1, 0x62, 0xa8, 0xca, 0x67, 0xd2, //0x00002ca8 .quad -3285434578585440922
	0x6d, 0x1e, 0xf7, 0x59, 0x9e, 0xcb, 0x47, 0x42, //0x00002cb0 .quad 4776009810824339053
	0x60, 0xbc, 0xa4, 0x3d, 0xa9, 0xde, 0x80, 0x83, //0x00002cb8 .quad -8970925639256982432
	0x08, 0xe6, 0x74, 0xf0, 0x85, 0xbe, 0xd9, 0x52, //0x00002cc0 .quad 5970012263530423816
	0x78, 0xeb, 0x0d, 0x8d, 0x53, 0x16, 0x61, 0xa4, //0x00002cc8 .quad -6601971030643840136
	0x8b, 0x1f, 0x92, 0x6c, 0x27, 0x2e, 0x90, 0x67, //0x00002cd0 .quad 7462515329413029771
	0x56, 0x66, 0x51, 0x70, 0xe8, 0x5b, 0x79, 0xcd, //0x00002cd8 .quad -3640777769877412266
	0xb6, 0x53, 0xdb, 0xa3, 0xd8, 0x1c, 0xba, 0x00, //0x00002ce0 .quad 52386062455755702
	0xf6, 0xdf, 0x32, 0x46, 0x71, 0xd9, 0x6b, 0x80, //0x00002ce8 .quad -9193015133814464522
	0xa4, 0x28, 0xd2, 0xcc, 0x0e, 0xa4, 0xe8, 0x80, //0x00002cf0 .quad -9157889458785081180
	0xf3, 0x97, 0xbf, 0x97, 0xcd, 0xcf, 0x86, 0xa0, //0x00002cf8 .quad -6879582898840692749
	0xcd, 0xb2, 0x06, 0x80, 0x12, 0xcd, 0x22, 0x61, //0x00002d00 .quad 6999382250228200141
	0xf0, 0x7d, 0xaf, 0xfd, 0xc0, 0x83, 0xa8, 0xc8, //0x00002d08 .quad -3987792605123478032
	0x81, 0x5f, 0x08, 0x20, 0x57, 0x80, 0x6b, 0x79, //0x00002d10 .quad 8749227812785250177
	0x6c, 0x5d, 0x1b, 0x3d, 0xb1, 0xa4, 0xd2, 0xfa, //0x00002d18 .quad -373054737976959636
	0xb0, 0x3b, 0x05, 0x74, 0x36, 0x30, 0xe3, 0xcb, //0x00002d20 .quad -3755104653863994448
	0x63, 0x1a, 0x31, 0xc6, 0xee, 0xa6, 0xc3, 0x9c, //0x00002d28 .quad -7150688238876681629
	0x9c, 0x8a, 0x06, 0x11, 0x44, 0xfc, 0xdb, 0xbe, //0x00002d30 .quad -4693880817329993060
	0xfc, 0x60, 0xbd, 0x77, 0xaa, 0x90, 0xf4, 0xc3, //0x00002d38 .quad -4326674280168464132
	0x44, 0x2d, 0x48, 0x15, 0x55, 0xfb, 0x92, 0xee, //0x00002d40 .quad -1255665003235103420
	0x3b, 0xb9, 0xac, 0x15, 0xd5, 0xb4, 0xf1, 0xf4, //0x00002d48 .quad -796656831783192261
	0x4a, 0x1c, 0x4d, 0x2d, 0x15, 0xdd, 0x1b, 0x75, //0x00002d50 .quad 8438581409832836170
	0xc5, 0xf3, 0x8b, 0x2d, 0x05, 0x11, 0x17, 0x99, //0x00002d58 .quad -7415439547505577019
	0x5d, 0x63, 0xa0, 0x78, 0x5a, 0xd4, 0x62, 0xd2, //0x00002d60 .quad -3286831292991118499
	0xb6, 0xf0, 0xee, 0x78, 0x46, 0xd5, 0x5c, 0xbf, //0x00002d68 .quad -4657613415954583370
	0x34, 0x7c, 0xc8, 0x16, 0x71, 0x89, 0xfb, 0x86, //0x00002d70 .quad -8720225134666286028
	0xe4, 0xac, 0x2a, 0x17, 0x98, 0x0a, 0x34, 0xef, //0x00002d78 .quad -1210330751515841308
	0xa0, 0x4d, 0x3d, 0xae, 0xe6, 0x35, 0x5d, 0xd4, //0x00002d80 .quad -3144297699952734816
	0x0e, 0xac, 0x7a, 0x0e, 0x9f, 0x86, 0x80, 0x95, //0x00002d88 .quad -7673985747338482674
	0x09, 0xa1, 0xcc, 0x59, 0x60, 0x83, 0x74, 0x89, //0x00002d90 .quad -8542058143368306423
	0x12, 0x57, 0x19, 0xd2, 0x46, 0xa8, 0xe0, 0xba, //0x00002d98 .quad -4980796165745715438
	0x4b, 0xc9, 0x3f, 0x70, 0x38, 0xa4, 0xd1, 0x2b, //0x00002da0 .quad 3157485376071780683
	0xd7, 0xac, 0x9f, 0x86, 0x58, 0xd2, 0x98, 0xe9, //0x00002da8 .quad -1614309188754756393
	0xcf, 0xdd, 0x27, 0x46, 0xa3, 0x06, 0x63, 0x7b, //0x00002db0 .quad 8890957387685944783
	0x06, 0xcc, 0x23, 0x54, 0x77, 0x83, 0xff, 0x91, //0x00002db8 .quad -7926472270612804602
	0x42, 0xd5, 0xb1, 0x17, 0x4c, 0xc8, 0x3b, 0x1a, //0x00002dc0 .quad 1890324697752655170
	0x08, 0xbf, 0x2c, 0x29, 0x55, 0x64, 0x7f, 0xb6, //0x00002dc8 .quad -5296404319838617848
	0x93, 0x4a, 0x9e, 0x1d, 0x5f, 0xba, 0xca, 0x20, //0x00002dd0 .quad 2362905872190818963
	0xca, 0xee, 0x77, 0x73, 0x6a, 0x3d, 0x1f, 0xe4, //0x00002dd8 .quad -2008819381370884406
	0x9c, 0xee, 0x82, 0x72, 0x7b, 0xb4, 0x7e, 0x54, //0x00002de0 .quad 6088502188546649756
	0x3e, 0xf5, 0x2a, 0x88, 0x62, 0x86, 0x93, 0x8e, //0x00002de8 .quad -8173041140997884610
	0x43, 0xaa, 0x23, 0x4f, 0x9a, 0x61, 0x9e, 0xe9, //0x00002df0 .quad -1612744301171463613
	0x8d, 0xb2, 0x35, 0x2a, 0xfb, 0x67, 0x38, 0xb2, //0x00002df8 .quad -5604615407819967859
	0xd4, 0x94, 0xec, 0xe2, 0x00, 0xfa, 0x05, 0x64, //0x00002e00 .quad 7207441660390446292
	0x31, 0x1f, 0xc3, 0xf4, 0xf9, 0x81, 0xc6, 0xde, //0x00002e08 .quad -2394083241347571919
	0x04, 0xdd, 0xd3, 0x8d, 0x40, 0xbc, 0x83, 0xde, //0x00002e10 .quad -2412877989897052924
	0x7e, 0xf3, 0xf9, 0x38, 0x3c, 0x11, 0x3c, 0x8b, //0x00002e18 .quad -8413831053483314306
	0x45, 0xd4, 0x48, 0xb1, 0x50, 0xab, 0x24, 0x96, //0x00002e20 .quad -7627783505798704059
	0x5e, 0x70, 0x38, 0x47, 0x8b, 0x15, 0x0b, 0xae, //0x00002e28 .quad -5905602798426754978
	0x57, 0x09, 0x9b, 0xdd, 0x24, 0xd6, 0xad, 0x3b, //0x00002e30 .quad 4300328673033783639
	0x76, 0x8c, 0x06, 0x19, 0xee, 0xda, 0x8d, 0xd9, //0x00002e38 .quad -2770317479606055818
	0xd6, 0xe5, 0x80, 0x0a, 0xd7, 0xa5, 0x4c, 0xe5, //0x00002e40 .quad -1923980597781273130
	0xc9, 0x17, 0xa4, 0xcf, 0xd4, 0xa8, 0xf8, 0x87, //0x00002e48 .quad -8648977452394866743
	0x4c, 0x1f, 0x21, 0xcd, 0x4c, 0xcf, 0x9f, 0x5e, //0x00002e50 .quad 6818396289628184396
	0xbc, 0x1d, 0x8d, 0x03, 0x0a, 0xd3, 0xf6, 0xa9, //0x00002e58 .quad -6199535797066195524
	0x1f, 0x67, 0x69, 0x00, 0x20, 0xc3, 0x47, 0x76, //0x00002e60 .quad 8522995362035230495
	0x2b, 0x65, 0x70, 0x84, 0xcc, 0x87, 0x74, 0xd4, //0x00002e68 .quad -3137733727905356501
	0x73, 0xe0, 0x41, 0x00, 0xf4, 0xd9, 0xec, 0x29, //0x00002e70 .quad 3021029092058325107
	0x3b, 0x3f, 0xc6, 0xd2, 0xdf, 0xd4, 0xc8, 0x84, //0x00002e78 .quad -8878612607581929669
	0x90, 0x58, 0x52, 0x00, 0x71, 0x10, 0x68, 0xf4, //0x00002e80 .quad -835399653354481520
	0x09, 0xcf, 0x77, 0xc7, 0x17, 0x0a, 0xfb, 0xa5, //0x00002e88 .quad -6486579741050024183
	0xb4, 0xee, 0x66, 0x40, 0x8d, 0x14, 0x82, 0x71, //0x00002e90 .quad 8179122470161673908
	0xcc, 0xc2, 0x55, 0xb9, 0x9d, 0xcc, 0x79, 0xcf, //0x00002e98 .quad -3496538657885142324
	0x30, 0x55, 0x40, 0x48, 0xd8, 0x4c, 0xf1, 0xc6, //0x00002ea0 .quad -4111420493003729616
	0xbf, 0x99, 0xd5, 0x93, 0xe2, 0x1f, 0xac, 0x81, //0x00002ea8 .quad -9102865688819295809
	0x7c, 0x6a, 0x50, 0x5a, 0x0e, 0xa0, 0xad, 0xb8, //0x00002eb0 .quad -5139275616254662020
	0x2f, 0x00, 0xcb, 0x38, 0xdb, 0x27, 0x17, 0xa2, //0x00002eb8 .quad -6766896092596731857
	0x1c, 0x85, 0xe4, 0xf0, 0x11, 0x08, 0xd9, 0xa6, //0x00002ec0 .quad -6424094520318327524
	0x3b, 0xc0, 0xfd, 0x06, 0xd2, 0xf1, 0x9c, 0xca, //0x00002ec8 .quad -3846934097318526917
	0x63, 0xa6, 0x1d, 0x6d, 0x16, 0x4a, 0x8f, 0x90, //0x00002ed0 .quad -8030118150397909405
	0x4a, 0x30, 0xbd, 0x88, 0x46, 0x2e, 0x44, 0xfd, //0x00002ed8 .quad -196981603220770742
	0xfe, 0x87, 0x32, 0x04, 0x4e, 0x8e, 0x59, 0x9a, //0x00002ee0 .quad -7324666853212387330
	0x2e, 0x3e, 0x76, 0x15, 0xec, 0x9c, 0x4a, 0x9e, //0x00002ee8 .quad -7040642529654063570
	0xfd, 0x29, 0x3f, 0x85, 0xe1, 0xf1, 0xef, 0x40, //0x00002ef0 .quad 4679224488766679549
	0xba, 0xcd, 0xd3, 0x1a, 0x27, 0x44, 0xdd, 0xc5, //0x00002ef8 .quad -4189117143640191558
	0x7c, 0xf4, 0x8e, 0xe6, 0x59, 0xee, 0x2b, 0xd1, //0x00002f00 .quad -3374341425896426372
	0x28, 0xc1, 0x88, 0xe1, 0x30, 0x95, 0x54, 0xf7, //0x00002f08 .quad -624710411122851544
	0xce, 0x58, 0x19, 0x30, 0xf8, 0x74, 0xbb, 0x82, //0x00002f10 .quad -9026492418826348338
	0xb9, 0x78, 0xf5, 0x8c, 0x3e, 0xdd, 0x94, 0x9a, //0x00002f18 .quad -7307973034592864071
	0x01, 0xaf, 0x1f, 0x3c, 0x36, 0x52, 0x6a, 0xe3, //0x00002f20 .quad -2059743486678159615
	0xe7, 0xd6, 0x32, 0x30, 0x8e, 0x14, 0x3a, 0xc1, //0x00002f28 .quad -4523280274813692185
	0xc1, 0x9a, 0x27, 0xcb, 0xc3, 0xe6, 0x44, 0xdc, //0x00002f30 .quad -2574679358347699519
	0xa1, 0x8c, 0x3f, 0xbc, 0xb1, 0x99, 0x88, 0xf1, //0x00002f38 .quad -1042414325089727327
	0xb9, 0xc0, 0xf8, 0x5e, 0x3a, 0x10, 0xab, 0x29, //0x00002f40 .quad 3002511419460075705
	0xe5, 0xb7, 0xa7, 0x15, 0x0f, 0x60, 0xf5, 0x96, //0x00002f48 .quad -7569037980822161435
	0xe7, 0xf0, 0xb6, 0xf6, 0x48, 0xd4, 0x15, 0x74, //0x00002f50 .quad 8364825292752482535
	0xde, 0xa5, 0x11, 0xdb, 0x12, 0xb8, 0xb2, 0xbc, //0x00002f58 .quad -4849611457600313890
	0x21, 0xad, 0x64, 0x34, 0x5b, 0x49, 0x1b, 0x11, //0x00002f60 .quad 1232659579085827361
	0x56, 0x0f, 0xd6, 0x91, 0x17, 0x66, 0xdf, 0xeb, //0x00002f68 .quad -1450328303573004458
	0x34, 0xec, 0xbe, 0x00, 0xd9, 0x0d, 0xb1, 0xca, //0x00002f70 .quad -3841273781498745804
	0x95, 0xc9, 0x25, 0xbb, 0xce, 0x9f, 0x6b, 0x93, //0x00002f78 .quad -7823984217374209643
	0x42, 0xa7, 0xee, 0x40, 0x4f, 0x51, 0x5d, 0x3d, //0x00002f80 .quad 4421779809981343554
	0xfb, 0x3b, 0xef, 0x69, 0xc2, 0x87, 0x46, 0xb8, //0x00002f88 .quad -5168294253290374149
	0x12, 0x51, 0x2a, 0x11, 0xa3, 0xa5, 0xb4, 0x0c, //0x00002f90 .quad 915538744049291538
	0xfa, 0x0a, 0x6b, 0x04, 0xb3, 0x29, 0x58, 0xe6, //0x00002f98 .quad -1848681798185579782
	0xab, 0x72, 0xba, 0xea, 0x85, 0xe7, 0xf0, 0x47, //0x00002fa0 .quad 5183897733458195115
	0xdc, 0xe6, 0xc2, 0xe2, 0x0f, 0x1a, 0xf7, 0x8f, //0x00002fa8 .quad -8072955151507069220
	0x56, 0x0f, 0x69, 0x65, 0x67, 0x21, 0xed, 0x59, //0x00002fb0 .quad 6479872166822743894
	0x93, 0xa0, 0x73, 0xdb, 0x93, 0xe0, 0xf4, 0xb3, //0x00002fb8 .quad -5479507920956448621
	0x2c, 0x53, 0xc3, 0x3e, 0xc1, 0x69, 0x68, 0x30, //0x00002fc0 .quad 3488154190101041964
	0xb8, 0x88, 0x50, 0xd2, 0xb8, 0x18, 0xf2, 0xe0, //0x00002fc8 .quad -2237698882768172872
	0xfb, 0x13, 0x3a, 0xc7, 0x18, 0x42, 0x41, 0x1e, //0x00002fd0 .quad 2180096368813151227
	0x73, 0x55, 0x72, 0x83, 0x73, 0x4f, 0x97, 0x8c, //0x00002fd8 .quad -8316090829371189901
	0xfa, 0x98, 0x08, 0xf9, 0x9e, 0x92, 0xd1, 0xe5, //0x00002fe0 .quad -1886565557410948870
	0xcf, 0xea, 0x4e, 0x64, 0x50, 0x23, 0xbd, 0xaf, //0x00002fe8 .quad -5783427518286599473
	0x39, 0xbf, 0x4a, 0xb7, 0x46, 0xf7, 0x45, 0xdf, //0x00002ff0 .quad -2358206946763686087
	0x83, 0xa5, 0x62, 0x7d, 0x24, 0x6c, 0xac, 0xdb, //0x00002ff8 .quad -2617598379430861437
	0x83, 0xb7, 0x8e, 0x32, 0x8c, 0xba, 0x8b, 0x6b, //0x00003000 .quad 7749492695127472003
	0x72, 0xa7, 0x5d, 0xce, 0x96, 0xc3, 0x4b, 0x89, //0x00003008 .quad -8553528014785370254
	0x64, 0x65, 0x32, 0x3f, 0x2f, 0xa9, 0x6e, 0x06, //0x00003010 .quad 463493832054564196
	0x4f, 0x11, 0xf5, 0x81, 0x7c, 0xb4, 0x9e, 0xab, //0x00003018 .quad -6080224000054324913
	0xbd, 0xfe, 0xfe, 0x0e, 0x7b, 0x53, 0x0a, 0xc8, //0x00003020 .quad -4032318728359182659
	0xa2, 0x55, 0x72, 0xa2, 0x9b, 0x61, 0x86, 0xd6, //0x00003028 .quad -2988593981640518238
	0x36, 0x5f, 0x5f, 0xe9, 0x2c, 0x74, 0x06, 0xbd, //0x00003030 .quad -4826042214438183114
	0x85, 0x75, 0x87, 0x45, 0x01, 0xfd, 0x13, 0x86, //0x00003038 .quad -8785400266166405755
	0x04, 0x37, 0xb7, 0x23, 0x38, 0x11, 0x48, 0x2c, //0x00003040 .quad 3190819268807046916
	0xe7, 0x52, 0xe9, 0x96, 0x41, 0xfc, 0x98, 0xa7, //0x00003048 .quad -6370064314280619289
	0xc5, 0x04, 0xa5, 0x2c, 0x86, 0x15, 0x5a, 0xf7, //0x00003050 .quad -623161932418579259
	0xa0, 0xa7, 0xa3, 0xfc, 0x51, 0x3b, 0x7f, 0xd1, //0x00003058 .quad -3350894374423386208
	0xfb, 0x22, 0xe7, 0xdb, 0x73, 0x4d, 0x98, 0x9a, //0x00003060 .quad -7307005235402693893
	0xc4, 0x48, 0xe6, 0x3d, 0x13, 0x85, 0xef, 0x82, //0x00003068 .quad -9011838011655698236
	0xba, 0xeb, 0xe0, 0xd2, 0xd0, 0x60, 0x3e, 0xc1, //0x00003070 .quad -4522070525825979462
	0xf5, 0xda, 0x5f, 0x0d, 0x58, 0x66, 0xab, 0xa3, //0x00003078 .quad -6653111496142234891
	0xa8, 0x26, 0x99, 0x07, 0x05, 0xf9, 0x8d, 0x31, //0x00003080 .quad 3570783879572301480
	0xb3, 0xd1, 0xb7, 0x10, 0xee, 0x3f, 0x96, 0xcc, //0x00003088 .quad -3704703351750405709
	0x52, 0x70, 0x7f, 0x49, 0x46, 0x77, 0xf1, 0xfd, //0x00003090 .quad -148206168962011054
	0x1f, 0xc6, 0xe5, 0x94, 0xe9, 0xcf, 0xbb, 0xff, //0x00003098 .quad -19193171260619233
	0x33, 0xa6, 0xef, 0xed, 0x8b, 0xea, 0xb6, 0xfe, //0x000030a0 .quad -92628855601256909
	0xd3, 0x9b, 0x0f, 0xfd, 0xf1, 0x61, 0xd5, 0x9f, //0x000030a8 .quad -6929524759678968877
	0xc0, 0x8f, 0x6b, 0xe9, 0x2e, 0xa5, 0x64, 0xfe, //0x000030b0 .quad -115786069501571136
	0xc8, 0x82, 0x53, 0x7c, 0x6e, 0xba, 0xca, 0xc7, //0x000030b8 .quad -4050219931171323192
	0xb0, 0x73, 0xc6, 0xa3, 0x7a, 0xce, 0xfd, 0x3d, //0x000030c0 .quad 4466953431550423984
	0x7b, 0x63, 0x68, 0x1b, 0x0a, 0x69, 0xbd, 0xf9, //0x000030c8 .quad -451088895536766085
	0x4e, 0x08, 0x5c, 0xa6, 0x0c, 0xa1, 0xbe, 0x06, //0x000030d0 .quad 486002885505321038
	0x2d, 0x3e, 0x21, 0x51, 0xa6, 0x61, 0x16, 0x9c, //0x000030d8 .quad -7199459587351560659
	0x62, 0x0a, 0xf3, 0xcf, 0x4f, 0x49, 0x6e, 0x48, //0x000030e0 .quad 5219189625309039202
	0xb8, 0x8d, 0x69, 0xe5, 0x0f, 0xfa, 0x1b, 0xc3, //0x000030e8 .quad -4387638465762062920
	0xfa, 0xcc, 0xef, 0xc3, 0xa3, 0xdb, 0x89, 0x5a, //0x000030f0 .quad 6523987031636299002
	0x26, 0xf1, 0xc3, 0xde, 0x93, 0xf8, 0xe2, 0xf3, //0x000030f8 .quad -872862063775190746
	0x1c, 0xe0, 0x75, 0x5a, 0x46, 0x29, 0x96, 0xf8, //0x00003100 .quad -534194123654701028
	0xb7, 0x76, 0x3a, 0x6b, 0x5c, 0xdb, 0x6d, 0x98, //0x00003108 .quad -7463067817500576073
	0x23, 0x58, 0x13, 0xf1, 0x97, 0xb3, 0xbb, 0xf6, //0x00003110 .quad -667742654568376285
	0x65, 0x14, 0x09, 0x86, 0x33, 0x52, 0x89, 0xbe, //0x00003118 .quad -4717148753448332187
	0x2c, 0x2e, 0x58, 0xed, 0x7d, 0xa0, 0x6a, 0x74, //0x00003120 .quad 8388693718644305452
	0x7f, 0x59, 0x8b, 0x67, 0xc0, 0xa6, 0x2b, 0xee, //0x00003128 .quad -1284749923383027329
	0xdc, 0x1c, 0x57, 0xb4, 0x4e, 0xa4, 0xc2, 0xa8, //0x00003130 .quad -6286281471915778852
	0xef, 0x17, 0xb7, 0x40, 0x38, 0x48, 0xdb, 0x94, //0x00003138 .quad -7720497729755473937
	0x13, 0xe4, 0x6c, 0x61, 0x62, 0x4d, 0xf3, 0x92, //0x00003140 .quad -7857851839894723565
	0xeb, 0xdd, 0xe4, 0x50, 0x46, 0x1a, 0x12, 0xba, //0x00003148 .quad -5038936143766954517
	0x17, 0x1d, 0xc8, 0xf9, 0xba, 0x20, 0xb0, 0x77, //0x00003150 .quad 8624429273841147159
	0x66, 0x15, 0x1e, 0xe5, 0xd7, 0xa0, 0x96, 0xe8, //0x00003158 .quad -1686984161281305242
	0x2e, 0x12, 0x1d, 0xdc, 0x74, 0x14, 0xce, 0x0a, //0x00003160 .quad 778582277723329070
	0x60, 0xcd, 0x32, 0xef, 0x86, 0x24, 0x5e, 0x91, //0x00003168 .quad -7971894128441897632
	0xba, 0x56, 0x24, 0x13, 0x92, 0x99, 0x81, 0x0d, //0x00003170 .quad 973227847154161338
	0xb8, 0x80, 0xff, 0xaa, 0xa8, 0xad, 0xb5, 0xb5, //0x00003178 .quad -5353181642124984136
	0x69, 0x6c, 0xed, 0x97, 0xf6, 0xff, 0xe1, 0x10, //0x00003180 .quad 1216534808942701673
	0xe6, 0x60, 0xbf, 0xd5, 0x12, 0x19, 0x23, 0xe3, //0x00003188 .quad -2079791034228842266
	0xc1, 0x63, 0xf4, 0x1e, 0xfa, 0x3f, 0x8d, 0xca, //0x00003190 .quad -3851351762838199359
	0x8f, 0x9c, 0x97, 0xc5, 0xab, 0xef, 0xf5, 0x8d, //0x00003198 .quad -8217398424034108273
	0xb2, 0x7c, 0xb1, 0xa6, 0xf8, 0x8f, 0x30, 0xbd, //0x000031a0 .quad -4814189703547749198
	0xb3, 0x83, 0xfd, 0xb6, 0x96, 0x6b, 0x73, 0xb1, //0x000031a8 .quad -5660062011615247437
	0xde, 0xdb, 0x5d, 0xd0, 0xf6, 0xb3, 0x7c, 0xac, //0x000031b0 .quad -6017737129434686498
	0xa0, 0xe4, 0xbc, 0x64, 0x7c, 0x46, 0xd0, 0xdd, //0x000031b8 .quad -2463391496091671392
	0x6b, 0xa9, 0x3a, 0x42, 0x7a, 0xf0, 0xcd, 0x6b, //0x000031c0 .quad 7768129340171790699
	0xe4, 0x0e, 0xf6, 0xbe, 0x0d, 0x2c, 0xa2, 0x8a, //0x000031c8 .quad -8457148712698376476
	0xc6, 0x53, 0xc9, 0xd2, 0x98, 0x6c, 0xc1, 0x86, //0x000031d0 .quad -8736582398494813242
	0x9d, 0x92, 0xb3, 0x2e, 0x11, 0xb7, 0x4a, 0xad, //0x000031d8 .quad -5959749872445582691
	0xb7, 0xa8, 0x7b, 0x07, 0xbf, 0xc7, 0x71, 0xe8, //0x000031e0 .quad -1697355961263740745
	0x44, 0x77, 0x60, 0x7a, 0xd5, 0x64, 0x9d, 0xd8, //0x000031e8 .quad -2838001322129590460
	0x72, 0x49, 0xad, 0x64, 0xd7, 0x1c, 0x47, 0x11, //0x000031f0 .quad 1244995533423855986
	0x8b, 0x4a, 0x7c, 0x6c, 0x05, 0x5f, 0x62, 0x87, //0x000031f8 .quad -8691279853972075893
	0xcf, 0x9b, 0xd8, 0x3d, 0x0d, 0xe4, 0x98, 0xd5, //0x00003200 .quad -3055441601647567921
	0x2d, 0x5d, 0x9b, 0xc7, 0xc6, 0xf6, 0x3a, 0xa9, //0x00003208 .quad -6252413799037706963
	0xc3, 0xc2, 0x4e, 0x8d, 0x10, 0x1d, 0xff, 0x4a, //0x00003210 .quad 5404070034795315907
	0x79, 0x34, 0x82, 0x79, 0x78, 0xb4, 0x89, 0xd3, //0x00003218 .quad -3203831230369745799
	0xba, 0x39, 0x51, 0x58, 0x2a, 0x72, 0xdf, 0xce, //0x00003220 .quad -3539985255894009414
	0xcb, 0x60, 0xf1, 0x4b, 0xcb, 0x10, 0x36, 0x84, //0x00003228 .quad -8919923546622172981
	0x28, 0x88, 0x65, 0xee, 0xb4, 0x4e, 0x97, 0xc2, //0x00003230 .quad -4424981569867511768
	0xfe, 0xb8, 0xed, 0x1e, 0xfe, 0x94, 0x43, 0xa5, //0x00003238 .quad -6538218414850328322
	0x32, 0xea, 0xfe, 0x29, 0x62, 0x22, 0x3d, 0x73, //0x00003240 .quad 8303831092947774002
	0x3e, 0x27, 0xa9, 0xa6, 0x3d, 0x7a, 0x94, 0xce, //0x00003248 .quad -3561087000135522498
	0x5f, 0x52, 0x3f, 0x5a, 0x7d, 0x35, 0x06, 0x08, //0x00003250 .quad 578208414664970847
	0x87, 0xb8, 0x29, 0x88, 0x66, 0xcc, 0x1c, 0x81, //0x00003258 .quad -9143208402725783417
	0xf7, 0x26, 0xcf, 0xb0, 0xdc, 0xc2, 0x07, 0xca, //0x00003260 .quad -3888925500096174345
	0xa8, 0x26, 0x34, 0x2a, 0x80, 0xff, 0x63, 0xa1, //0x00003268 .quad -6817324484979841368
	0xb5, 0xf0, 0x02, 0xdd, 0x93, 0xb3, 0x89, 0xfc, //0x00003270 .quad -249470856692830027
	0x52, 0x30, 0xc1, 0x34, 0x60, 0xff, 0xbc, 0xc9, //0x00003278 .quad -3909969587797413806
	0xe2, 0xac, 0x43, 0xd4, 0x78, 0x20, 0xac, 0xbb, //0x00003280 .quad -4923524589293425438
	0x67, 0x7c, 0xf1, 0x41, 0x38, 0x3f, 0x2c, 0xfc, //0x00003288 .quad -275775966319379353
	0x0d, 0x4c, 0xaa, 0x84, 0x4b, 0x94, 0x4b, 0xd5, //0x00003290 .quad -3077202868308390899
	0xc0, 0xed, 0x36, 0x29, 0x83, 0xa7, 0x9b, 0x9d, //0x00003298 .quad -7089889006590693952
	0x11, 0xdf, 0xd4, 0x65, 0x5e, 0x79, 0x9e, 0x0a, //0x000032a0 .quad 765182433041899281
	0x31, 0xa9, 0x84, 0xf3, 0x63, 0x91, 0x02, 0xc5, //0x000032a8 .quad -4250675239810979535
	0xd5, 0x16, 0x4a, 0xff, 0xb5, 0x17, 0x46, 0x4d, //0x000032b0 .quad 5568164059729762005
	0x7d, 0xd3, 0x65, 0xf0, 0xbc, 0x35, 0x43, 0xf6, //0x000032b8 .quad -701658031336336515
	0x45, 0x4e, 0x8e, 0xbf, 0xd1, 0xce, 0x4b, 0x50, //0x000032c0 .quad 5785945546544795205
	0x2e, 0xa4, 0x3f, 0x16, 0x96, 0x01, 0xea, 0x99, //0x000032c8 .quad -7356065297226292178
	0xd6, 0xe1, 0x71, 0x2f, 0x86, 0xc2, 0x5e, 0xe4, //0x000032d0 .quad -1990940103673781802
	0x39, 0x8d, 0xcf, 0x9b, 0xfb, 0x81, 0x64, 0xc0, //0x000032d8 .quad -4583395603105477319
	0x4c, 0x5a, 0x4e, 0xbb, 0x27, 0x73, 0x76, 0x5d, //0x000032e0 .quad 6734696907262548556
	0x88, 0x70, 0xc3, 0x82, 0x7a, 0xa2, 0x7d, 0xf0, //0x000032e8 .quad -1117558485454458744
	0x6f, 0xf8, 0x10, 0xd5, 0xf8, 0x07, 0x6a, 0x3a, //0x000032f0 .quad 4209185567039092847
	0x55, 0x26, 0xba, 0x91, 0x8c, 0x85, 0x4e, 0x96, //0x000032f8 .quad -7616003081050118571
	0x8b, 0x36, 0x55, 0x0a, 0xf7, 0x89, 0x04, 0x89, //0x00003300 .quad -8573576096483297653
	0xea, 0xaf, 0x28, 0xb6, 0xef, 0x26, 0xe2, 0xbb, //0x00003308 .quad -4908317832885260310
	0x2e, 0x84, 0xea, 0xcc, 0x74, 0xac, 0x45, 0x2b, //0x00003310 .quad 3118087934678041646
	0xe5, 0xdb, 0xb2, 0xa3, 0xab, 0xb0, 0xda, 0xea, //0x00003318 .quad -1523711272679187483
	0x9d, 0x92, 0x12, 0x00, 0xc9, 0x8b, 0x0b, 0x3b, //0x00003320 .quad 4254647968387469981
	0x6f, 0xc9, 0x4f, 0x46, 0x6b, 0xae, 0xc8, 0x92, //0x00003328 .quad -7869848573065574033
	0x44, 0x37, 0x17, 0x40, 0xbb, 0x6e, 0xce, 0x09, //0x00003330 .quad 706623942056949572
	0xcb, 0xbb, 0xe3, 0x17, 0x06, 0xda, 0x7a, 0xb7, //0x00003338 .quad -5225624697904579637
	0x15, 0x05, 0x1d, 0x10, 0x6a, 0x0a, 0x42, 0xcc, //0x00003340 .quad -3728406090856200939
	0xbd, 0xaa, 0xdc, 0x9d, 0x87, 0x90, 0x59, 0xe5, //0x00003348 .quad -1920344853953336643
	0x2d, 0x23, 0x12, 0x4a, 0x82, 0x46, 0xa9, 0x9f, //0x00003350 .quad -6941939825212513491
	0xb6, 0xea, 0xa9, 0xc2, 0x54, 0xfa, 0x57, 0x8f, //0x00003358 .quad -8117744561361917258
	0xf9, 0xab, 0x96, 0xdc, 0x22, 0x98, 0x93, 0x47, //0x00003360 .quad 5157633273766521849
	0x64, 0x65, 0x54, 0xf3, 0xe9, 0xf8, 0x2d, 0xb3, //0x00003368 .quad -5535494683275008668
	0xf7, 0x56, 0xbc, 0x93, 0x2b, 0x7e, 0x78, 0x59, //0x00003370 .quad 6447041592208152311
	0xbd, 0x7e, 0x29, 0x70, 0x24, 0x77, 0xf9, 0xdf, //0x00003378 .quad -2307682335666372931
	0x5a, 0xb6, 0x55, 0x3c, 0xdb, 0x4e, 0xeb, 0x57, //0x00003380 .quad 6335244004343789146
	0x36, 0xef, 0x19, 0xc6, 0x76, 0xea, 0xfb, 0x8b, //0x00003388 .quad -8359830487432564938
	0xf1, 0x23, 0x6b, 0x0b, 0x92, 0x22, 0xe6, 0xed, //0x00003390 .quad -1304317031425039375
	0x03, 0x6b, 0xa0, 0x77, 0x14, 0xe5, 0xfa, 0xae, //0x00003398 .quad -5838102090863318269
	0xed, 0xec, 0x45, 0x8e, 0x36, 0xab, 0x5f, 0xe9, //0x000033a0 .quad -1630396289281299219
	0xc4, 0x85, 0x88, 0x95, 0x59, 0x9e, 0xb9, 0xda, //0x000033a8 .quad -2685941595151759932
	0x14, 0xb4, 0xeb, 0x18, 0x02, 0xcb, 0xdb, 0x11, //0x000033b0 .quad 1286845328412881940
	0x9b, 0x53, 0x75, 0xfd, 0xf7, 0x02, 0xb4, 0x88, //0x000033b8 .quad -8596242524610931813
	0x19, 0xa1, 0x26, 0x9f, 0xc2, 0xbd, 0x52, 0xd6, //0x000033c0 .quad -3003129357911285479
	0x81, 0xa8, 0xd2, 0xfc, 0xb5, 0x03, 0xe1, 0xaa, //0x000033c8 .quad -6133617137336276863
	0x5f, 0x49, 0xf0, 0x46, 0x33, 0x6d, 0xe7, 0x4b, //0x000033d0 .quad 5469460339465668959
	0xa2, 0x52, 0x07, 0x7c, 0xa3, 0x44, 0x99, 0xd5, //0x000033d8 .quad -3055335403242958174
	0xdb, 0x2d, 0x56, 0x0c, 0x40, 0xa4, 0x70, 0x6f, //0x000033e0 .quad 8030098730593431003
	0xa5, 0x93, 0x84, 0x2d, 0xe6, 0xca, 0x7f, 0x85, //0x000033e8 .quad -8827113654667930715
	0x52, 0xb9, 0x6b, 0x0f, 0x50, 0xcd, 0x4c, 0xcb, //0x000033f0 .quad -3797434642040374958
	0x8e, 0xb8, 0xe5, 0xb8, 0x9f, 0xbd, 0xdf, 0xa6, //0x000033f8 .quad -6422206049907525490
	0xa7, 0xa7, 0x46, 0x13, 0xa4, 0x00, 0x20, 0x7e, //0x00003400 .quad 9088264752731695015
	0xb2, 0x26, 0x1f, 0xa7, 0x07, 0xad, 0x97, 0xd0, //0x00003408 .quad -3416071543957018958
	0xc8, 0x28, 0x0c, 0x8c, 0x66, 0x00, 0xd4, 0x8e, //0x00003410 .quad -8154892584824854328
	0x2f, 0x78, 0x73, 0xc8, 0x24, 0xcc, 0x5e, 0x82, //0x00003418 .quad -9052573742614218705
	0xfa, 0x32, 0x0f, 0x2f, 0x80, 0x00, 0x89, 0x72, //0x00003420 .quad 8253128342678483706
	0x3b, 0x56, 0x90, 0xfa, 0x2d, 0x7f, 0xf6, 0xa2, //0x00003428 .quad -6704031159840385477
	0xb9, 0xff, 0xd2, 0x3a, 0xa0, 0x40, 0x2b, 0x4f, //0x00003430 .quad 5704724409920716729
	0xca, 0x6b, 0x34, 0x79, 0xf9, 0x1e, 0xb4, 0xcb, //0x00003438 .quad -3768352931373093942
	0xa8, 0xbf, 0x87, 0x49, 0xc8, 0x10, 0xf6, 0xe2, //0x00003440 .quad -2092466524453879896
	0xbc, 0x86, 0x81, 0xd7, 0xb7, 0x26, 0xa1, 0xfe, //0x00003448 .quad -98755145788979524
	0xc9, 0xd7, 0xf4, 0x2d, 0x7d, 0xca, 0xd9, 0x0d, //0x00003450 .quad 998051431430019017
	0x36, 0xf4, 0xb0, 0xe6, 0x32, 0xb8, 0x24, 0x9f, //0x00003458 .quad -6979250993759194058
	0xbb, 0x0d, 0x72, 0x79, 0x1c, 0x3d, 0x50, 0x91, //0x00003460 .quad -7975807747567252037
	0x43, 0x31, 0x5d, 0xa0, 0x3f, 0xe6, 0xed, 0xc6, //0x00003468 .quad -4112377723771604669
	0x2a, 0x91, 0xce, 0x97, 0x63, 0x4c, 0xa4, 0x75, //0x00003470 .quad 8476984389250486570
	0x94, 0x7d, 0x74, 0x88, 0xcf, 0x5f, 0xa9, 0xf8, //0x00003478 .quad -528786136287117932
	0xba, 0x1a, 0xe1, 0x3e, 0xbe, 0xaf, 0x86, 0xc9, //0x00003480 .quad -3925256793573221702
	0x7c, 0xce, 0x48, 0xb5, 0xe1, 0xdb, 0x69, 0x9b, //0x00003488 .quad -7248020362820530564
	0x68, 0x61, 0x99, 0xce, 0xad, 0x5b, 0xe8, 0xfb, //0x00003490 .quad -294884973539139224
	0x1b, 0x02, 0x9b, 0x22, 0xda, 0x52, 0x44, 0xc2, //0x00003498 .quad -4448339435098275301
	0xc3, 0xb9, 0x3f, 0x42, 0x99, 0x72, 0xe2, 0xfa, //0x000034a0 .quad -368606216923924029
	0xa2, 0xc2, 0x41, 0xab, 0x90, 0x67, 0xd5, 0xf2, //0x000034a8 .quad -948738275445456222
	0x1a, 0xd4, 0x67, 0xc9, 0x9f, 0x87, 0xcd, 0xdc, //0x000034b0 .quad -2536221894791146470
	0xa5, 0x19, 0x09, 0x6b, 0xba, 0x60, 0xc5, 0x97, //0x000034b8 .quad -7510490449794491995
	0x20, 0xc9, 0xc1, 0xbb, 0x87, 0xe9, 0x00, 0x54, //0x000034c0 .quad 6053094668365842720
	0x0f, 0x60, 0xcb, 0x05, 0xe9, 0xb8, 0xb6, 0xbd, //0x000034c8 .quad -4776427043815727089
	0x68, 0x3b, 0xb2, 0xaa, 0xe9, 0x23, 0x01, 0x29, //0x000034d0 .quad 2954682317029915496
	0x13, 0x38, 0x3e, 0x47, 0x23, 0x67, 0x24, 0xed, //0x000034d8 .quad -1358847786342270957
	0x21, 0x65, 0xaf, 0x0a, 0x72, 0xb6, 0xa0, 0xf9, //0x000034e0 .quad -459166561069996767
	0x0b, 0xe3, 0x86, 0x0c, 0x76, 0xc0, 0x36, 0x94, //0x000034e8 .quad -7766808894105001205
	0x69, 0x3e, 0x5b, 0x8d, 0x0e, 0xe4, 0x08, 0xf8, //0x000034f0 .quad -573958201337495959
	0xce, 0x9b, 0xa8, 0x8f, 0x93, 0x70, 0x44, 0xb9, //0x000034f8 .quad -5096825099203863602
	0x04, 0x0e, 0xb2, 0x30, 0x12, 0x1d, 0x0b, 0xb6, //0x00003500 .quad -5329133770099257852
	0xc2, 0xc2, 0x92, 0x73, 0xb8, 0x8c, 0x95, 0xe7, //0x00003508 .quad -1759345355577441598
	0xc2, 0x48, 0x6f, 0x5e, 0x2b, 0xf2, 0xc6, 0xb1, //0x00003510 .quad -5636551615525730110
	0xb9, 0xb9, 0x3b, 0x48, 0xf3, 0x77, 0xbd, 0x90, //0x00003518 .quad -8017119874876982855
	0xf3, 0x1a, 0x0b, 0x36, 0xb6, 0xae, 0x38, 0x1e, //0x00003520 .quad 2177682517447613171
	0x28, 0xa8, 0x4a, 0x1a, 0xf0, 0xd5, 0xec, 0xb4, //0x00003528 .quad -5409713825168840664
	0xb0, 0xe1, 0x8d, 0xc3, 0x63, 0xda, 0xc6, 0x25, //0x00003530 .quad 2722103146809516464
	0x32, 0x52, 0xdd, 0x20, 0x6c, 0x0b, 0x28, 0xe2, //0x00003538 .quad -2150456263033662926
	0x0e, 0xad, 0x38, 0x5a, 0x7e, 0x48, 0x9c, 0x57, //0x00003540 .quad 6313000485183335694
	0x5f, 0x53, 0x8a, 0x94, 0x23, 0x07, 0x59, 0x8d, //0x00003548 .quad -8261564192037121185
	0x51, 0xd8, 0xc6, 0xf0, 0x9d, 0x5a, 0x83, 0x2d, //0x00003550 .quad 3279564588051781713
	0x37, 0xe8, 0xac, 0x79, 0xec, 0x48, 0xaf, 0xb0, //0x00003558 .quad -5715269221619013577
	0x65, 0x8e, 0xf8, 0x6c, 0x45, 0x31, 0xe4, 0xf8, //0x00003560 .quad -512230283362660763
	0x44, 0x22, 0x18, 0x98, 0x27, 0x1b, 0xdb, 0xdc, //0x00003568 .quad -2532400508596379068
	0xff, 0x58, 0x1b, 0x64, 0xcb, 0x9e, 0x8e, 0x1b, //0x00003570 .quad 1985699082112030975
	0x6b, 0x15, 0x0f, 0xbf, 0xf8, 0xf0, 0x08, 0x8a, //0x00003578 .quad -8500279345513818773
	0x3f, 0x2f, 0x22, 0x3d, 0x7e, 0x46, 0x72, 0xe2, //0x00003580 .quad -2129562165787349185
	0xc5, 0xda, 0xd2, 0xee, 0x36, 0x2d, 0x8b, 0xac, //0x00003588 .quad -6013663163464885563
	0x0f, 0xbb, 0x6a, 0xcc, 0x1d, 0xd8, 0x0e, 0x5b, //0x00003590 .quad 6561419329620589327
	0x77, 0x91, 0x87, 0xaa, 0x84, 0xf8, 0xad, 0xd7, //0x00003598 .quad -2905392935903719049
	0xe9, 0xb4, 0xc2, 0x9f, 0x12, 0x47, 0xe9, 0x98, //0x000035a0 .quad -7428327965055601431
	0xea, 0xba, 0x94, 0xea, 0x52, 0xbb, 0xcc, 0x86, //0x000035a8 .quad -8733399612580906262
	0x24, 0x62, 0xb3, 0x47, 0xd7, 0x98, 0x23, 0x3f, //0x000035b0 .quad 4549648098962661924
	0xa5, 0xe9, 0x39, 0xa5, 0x27, 0xea, 0x7f, 0xa8, //0x000035b8 .quad -6305063497298744923
	0xad, 0x3a, 0xa0, 0x19, 0x0d, 0x7f, 0xec, 0x8e, //0x000035c0 .quad -8147997931578836307
	0x0e, 0x64, 0x88, 0x8e, 0xb1, 0xe4, 0x9f, 0xd2, //0x000035c8 .quad -3269643353196043250
	0xac, 0x24, 0x04, 0x30, 0x68, 0xcf, 0x53, 0x19, //0x000035d0 .quad 1825030320404309164
	0x89, 0x3e, 0x15, 0xf9, 0xee, 0xee, 0xa3, 0x83, //0x000035d8 .quad -8961056123388608887
	0xd7, 0x2d, 0x05, 0x3c, 0x42, 0xc3, 0xa8, 0x5f, //0x000035e0 .quad 6892973918932774359
	0x2b, 0x8e, 0x5a, 0xb7, 0xaa, 0xea, 0x8c, 0xa4, //0x000035e8 .quad -6589634135808373205
	0x4d, 0x79, 0x06, 0xcb, 0x12, 0xf4, 0x92, 0x37, //0x000035f0 .quad 4004531380238580045
	0xb6, 0x31, 0x31, 0x65, 0x55, 0x25, 0xb0, 0xcd, //0x000035f8 .quad -3625356651333078602
	0xd0, 0x0b, 0xe4, 0xbe, 0x8b, 0xd8, 0xbb, 0xe2, //0x00003600 .quad -2108853905778275376
	0x11, 0xbf, 0x3e, 0x5f, 0x55, 0x17, 0x8e, 0x80, //0x00003608 .quad -9183376934724255983
	0xc4, 0x0e, 0x9d, 0xae, 0xae, 0xce, 0x6a, 0x5b, //0x00003610 .quad 6587304654631931588
	0xd6, 0x6e, 0x0e, 0xb7, 0x2a, 0x9d, 0xb1, 0xa0, //0x00003618 .quad -6867535149977932074
	0x75, 0x52, 0x44, 0x5a, 0x5a, 0x82, 0x45, 0xf2, //0x00003620 .quad -989241218564861323
	0x8b, 0x0a, 0xd2, 0x64, 0x75, 0x04, 0xde, 0xc8, //0x00003628 .quad -3972732919045027189
	0x12, 0x67, 0xd5, 0xf0, 0xf0, 0xe2, 0xd6, 0xee, //0x00003630 .quad -1236551523206076654
	0x2e, 0x8d, 0x06, 0xbe, 0x92, 0x85, 0x15, 0xfb, //0x00003638 .quad -354230130378896082
	0x6b, 0x60, 0x85, 0x96, 0xd6, 0x4d, 0x46, 0x55, //0x00003640 .quad 6144684325637283947
	0x3d, 0x18, 0xc4, 0xb6, 0x7b, 0x73, 0xed, 0x9c, //0x00003648 .quad -7138922859127891907
	0x86, 0xb8, 0x26, 0x3c, 0x4c, 0xe1, 0x97, 0xaa, //0x00003650 .quad -6154202648235558778
	0x4c, 0x1e, 0x75, 0xa4, 0x5a, 0xd0, 0x28, 0xc4, //0x00003658 .quad -4311967555482476980
	0xa8, 0x66, 0x30, 0x4b, 0x9f, 0xd9, 0x3d, 0xd5, //0x00003660 .quad -3081067291867060568
	0xdf, 0x65, 0x92, 0x4d, 0x71, 0x04, 0x33, 0xf5, //0x00003668 .quad -778273425925708321
	0x29, 0x40, 0xfe, 0x8e, 0x03, 0xa8, 0x46, 0xe5, //0x00003670 .quad -1925667057416912855
	0xab, 0x7f, 0x7b, 0xd0, 0xc6, 0xe2, 0x3f, 0x99, //0x00003678 .quad -7403949918844649557
	0x33, 0xd0, 0xbd, 0x72, 0x04, 0x52, 0x98, 0xde, //0x00003680 .quad -2407083821771141069
	0x96, 0x5f, 0x9a, 0x84, 0x78, 0xdb, 0x8f, 0xbf, //0x00003688 .quad -4643251380128424042
	0x40, 0x44, 0x6d, 0x8f, 0x85, 0x66, 0x3e, 0x96, //0x00003690 .quad -7620540795641314240
	0x7c, 0xf7, 0xc0, 0xa5, 0x56, 0xd2, 0x73, 0xef, //0x00003698 .quad -1192378206733142148
	0xa8, 0x4a, 0xa4, 0x79, 0x13, 0x00, 0xe7, 0xdd, //0x000036a0 .quad -2456994988062127448
	0xad, 0x9a, 0x98, 0x27, 0x76, 0x63, 0xa8, 0x95, //0x000036a8 .quad -7662765406849295699
	0x52, 0x5d, 0x0d, 0x58, 0x18, 0xc0, 0x60, 0x55, //0x000036b0 .quad 6152128301777116498
	0x59, 0xc1, 0x7e, 0xb1, 0x53, 0x7c, 0x12, 0xbb, //0x000036b8 .quad -4966770740134231719
	0xa6, 0xb4, 0x10, 0x6e, 0x1e, 0xf0, 0xb8, 0xaa, //0x000036c0 .quad -6144897678060768090
	0xaf, 0x71, 0xde, 0x9d, 0x68, 0x1b, 0xd7, 0xe9, //0x000036c8 .quad -1596777406740401745
	0xe8, 0x70, 0xca, 0x04, 0x13, 0x96, 0xb3, 0xca, //0x000036d0 .quad -3840561048787980056
	0x0d, 0x07, 0xab, 0x62, 0x21, 0x71, 0x26, 0x92, //0x000036d8 .quad -7915514906853832947
	0x22, 0x0d, 0xfd, 0xc5, 0x97, 0x7b, 0x60, 0x3d, //0x000036e0 .quad 4422670725869800738
	0xd1, 0xc8, 0x55, 0xbb, 0x69, 0x0d, 0xb0, 0xb6, //0x000036e8 .quad -5282707615139903279
	0x6a, 0x50, 0x7c, 0xb7, 0x7d, 0x9a, 0xb8, 0x8c, //0x000036f0 .quad -8306719647944912790
	0x05, 0x3b, 0x2b, 0x2a, 0xc4, 0x10, 0x5c, 0xe4, //0x000036f8 .quad -1991698500497491195
	0x42, 0xb2, 0xad, 0x92, 0x8e, 0x60, 0xf3, 0x77, //0x00003700 .quad 8643358275316593218
	0xe3, 0x04, 0x5b, 0x9a, 0x7a, 0x8a, 0xb9, 0x8e, //0x00003708 .quad -8162340590452013853
	0xd3, 0x1e, 0x59, 0x37, 0xb2, 0x38, 0xf0, 0x55, //0x00003710 .quad 6192511825718353619
	0x1c, 0xc6, 0xf1, 0x40, 0x19, 0xed, 0x67, 0xb2, //0x00003718 .quad -5591239719637629412
	0x88, 0x66, 0x2f, 0xc5, 0xde, 0x46, 0x6c, 0x6b, //0x00003720 .quad 7740639782147942024
	0xa3, 0x37, 0x2e, 0x91, 0x5f, 0xe8, 0x01, 0xdf, //0x00003728 .quad -2377363631119648861
	0x15, 0xa0, 0x3d, 0x3b, 0x4b, 0xac, 0x23, 0x23, //0x00003730 .quad 2532056854628769813
	0xc6, 0xe2, 0xbc, 0xba, 0x3b, 0x31, 0x61, 0x8b, //0x00003738 .quad -8403381297090862394
	0x1a, 0x08, 0x0d, 0x0a, 0x5e, 0x97, 0xec, 0xab, //0x00003740 .quad -6058300968568813542
	0x77, 0x1b, 0x6c, 0xa9, 0x8a, 0x7d, 0x39, 0xae, //0x00003748 .quad -5892540602936190089
	0x21, 0x4a, 0x90, 0x8c, 0x35, 0xbd, 0xe7, 0x96, //0x00003750 .quad -7572876210711016927
	0x55, 0x22, 0xc7, 0x53, 0xed, 0xdc, 0xc7, 0xd9, //0x00003758 .quad -2753989735242849707
	0x54, 0x2e, 0xda, 0x77, 0x41, 0xd6, 0x50, 0x7e, //0x00003760 .quad 9102010423587778132
	0x75, 0x75, 0x5c, 0x54, 0x14, 0xea, 0x1c, 0x88, //0x00003768 .quad -8638772612167862923
	0xe9, 0xb9, 0xd0, 0xd5, 0xd1, 0x0b, 0xe5, 0xdd, //0x00003770 .quad -2457545025797441047
	0xd2, 0x92, 0x73, 0x69, 0x99, 0x24, 0x24, 0xaa, //0x00003778 .quad -6186779746782440750
	0x64, 0xe8, 0x44, 0x4b, 0xc6, 0x4e, 0x5e, 0x95, //0x00003780 .quad -7683617300674189212
	0x87, 0x77, 0xd0, 0xc3, 0xbf, 0x2d, 0xad, 0xd4, //0x00003788 .quad -3121788665050663033
	0x3e, 0x11, 0x0b, 0xef, 0x3b, 0xf1, 0x5a, 0xbd, //0x00003790 .quad -4802260812921368258
	0xb4, 0x4a, 0x62, 0xda, 0x97, 0x3c, 0xec, 0x84, //0x00003798 .quad -8868646943297746252
	0x8e, 0xd5, 0xcd, 0xea, 0x8a, 0xad, 0xb1, 0xec, //0x000037a0 .quad -1391139997724322418
	0x61, 0xdd, 0xfa, 0xd0, 0xbd, 0x4b, 0x27, 0xa6, //0x000037a8 .quad -6474122660694794911
	0xf2, 0x4a, 0x81, 0xa5, 0xed, 0x18, 0xde, 0x67, //0x000037b0 .quad 7484447039699372786
	0xba, 0x94, 0x39, 0x45, 0xad, 0x1e, 0xb1, 0xcf, //0x000037b8 .quad -3480967307441105734
	0xd7, 0xce, 0x70, 0x87, 0x94, 0xcf, 0xea, 0x80, //0x000037c0 .quad -9157278655470055721
	0xf4, 0xfc, 0x43, 0x4b, 0x2c, 0xb3, 0xce, 0x81, //0x000037c8 .quad -9093133594791772940
	0x8d, 0x02, 0x4d, 0xa9, 0x79, 0x83, 0x25, 0xa1, //0x000037d0 .quad -6834912300910181747
	0x31, 0xfc, 0x14, 0x5e, 0xf7, 0x5f, 0x42, 0xa2, //0x000037d8 .quad -6754730975062328271
	0x30, 0x43, 0xa0, 0x13, 0x58, 0xe4, 0x6e, 0x09, //0x000037e0 .quad 679731660717048624
	0x3e, 0x3b, 0x9a, 0x35, 0xf5, 0xf7, 0xd2, 0xca, //0x000037e8 .quad -3831727700400522434
	0xfc, 0x53, 0x88, 0x18, 0x6e, 0x9d, 0xca, 0x8b, //0x000037f0 .quad -8373707460958465028
	0x0d, 0xca, 0x00, 0x83, 0xf2, 0xb5, 0x87, 0xfd, //0x000037f8 .quad -177973607073265139
	0x7d, 0x34, 0x55, 0xcf, 0x64, 0xa2, 0x5e, 0x77, //0x00003800 .quad 8601490892183123069
	0x48, 0x7e, 0xe0, 0x91, 0xb7, 0xd1, 0x74, 0x9e, //0x00003808 .quad -7028762532061872568
	0x9d, 0x81, 0x2a, 0x03, 0xfe, 0x4a, 0x36, 0x95, //0x00003810 .quad -7694880458480647779
	0xda, 0x9d, 0x58, 0x76, 0x25, 0x06, 0x12, 0xc6, //0x00003818 .quad -4174267146649952806
	0x04, 0x22, 0xf5, 0x83, 0xbd, 0xdd, 0x83, 0x3a, //0x00003820 .quad 4216457482181353988
	0x51, 0xc5, 0xee, 0xd3, 0xae, 0x87, 0x96, 0xf7, //0x00003828 .quad -606147914885053103
	0x42, 0x35, 0x79, 0x72, 0x96, 0x6a, 0x92, 0xc4, //0x00003830 .quad -4282243101277735614
	0x52, 0x3b, 0x75, 0x44, 0xcd, 0x14, 0xbe, 0x9a, //0x00003838 .quad -7296371474444240046
	0x93, 0x82, 0x17, 0x0f, 0x3c, 0x05, 0xb7, 0x75, //0x00003840 .quad 8482254178684994195
	0x27, 0x8a, 0x92, 0x95, 0x00, 0x9a, 0x6d, 0xc1, //0x00003848 .quad -4508778324627912153
	0x38, 0x63, 0xdd, 0x12, 0x8b, 0xc6, 0x24, 0x53, //0x00003850 .quad 5991131704928854840
	0xb1, 0x2c, 0xf7, 0xba, 0x80, 0x00, 0xc9, 0xf1, //0x00003858 .quad -1024286887357502287
	0x03, 0x5e, 0xca, 0xeb, 0x16, 0xfc, 0xf6, 0xd3, //0x00003860 .quad -3173071712060547581
	0xee, 0x7b, 0xda, 0x74, 0x50, 0xa0, 0x1d, 0x97, //0x00003868 .quad -7557708332239520786
	0x84, 0xf5, 0xbc, 0xa6, 0x1c, 0xbb, 0xf4, 0x88, //0x00003870 .quad -8578025658503072380
	0xea, 0x1a, 0x11, 0x92, 0x64, 0x08, 0xe5, 0xbc, //0x00003878 .quad -4835449396872013078
	0xe5, 0x32, 0x6c, 0xd0, 0xe3, 0xe9, 0x31, 0x2b, //0x00003880 .quad 3112525982153323237
	0xa5, 0x61, 0x95, 0xb6, 0x7d, 0x4a, 0x1e, 0xec, //0x00003888 .quad -1432625727662628443
	0xcf, 0x9f, 0x43, 0x62, 0x2e, 0x32, 0xff, 0x3a, //0x00003890 .quad 4251171748059520975
	0x07, 0x5d, 0x1d, 0x92, 0x8e, 0xee, 0x92, 0x93, //0x00003898 .quad -7812920107430224633
	0xc2, 0x87, 0xd4, 0xfa, 0xb9, 0xfe, 0xbe, 0x09, //0x000038a0 .quad 702278666647013314
	0x49, 0xb4, 0xa4, 0x36, 0x32, 0xaa, 0x77, 0xb8, //0x000038a8 .quad -5154464115860392887
	0xb3, 0xa9, 0x89, 0x79, 0x68, 0xbe, 0x2e, 0x4c, //0x000038b0 .quad 5489534351736154547
	0x5b, 0xe1, 0x4d, 0xc4, 0xbe, 0x94, 0x95, 0xe6, //0x000038b8 .quad -1831394126398103205
	0x10, 0x0a, 0xf6, 0x4b, 0x01, 0x37, 0x9d, 0x0f, //0x000038c0 .quad 1125115960621402640
	0xd9, 0xac, 0xb0, 0x3a, 0xf7, 0x7c, 0x1d, 0x90, //0x000038c8 .quad -8062150356639896359
	0x94, 0x8c, 0xf3, 0x9e, 0xc1, 0x84, 0x84, 0x53, //0x000038d0 .quad 6018080969204141204
	0x0f, 0xd8, 0x5c, 0x09, 0x35, 0xdc, 0x24, 0xb4, //0x000038d8 .quad -5466001927372482545
	0xb9, 0x6f, 0xb0, 0x06, 0xf2, 0xa5, 0x65, 0x28, //0x000038e0 .quad 2910915193077788601
	0x13, 0x0e, 0xb4, 0x4b, 0x42, 0x13, 0x2e, 0xe1, //0x000038e8 .quad -2220816390788215277
	0xd3, 0x45, 0x2e, 0x44, 0xb7, 0x87, 0x3f, 0xf9, //0x000038f0 .quad -486521013540076077
	0xcb, 0x88, 0x50, 0x6f, 0x09, 0xcc, 0xbc, 0x8c, //0x000038f8 .quad -8305539271883716405
	0x48, 0xd7, 0x39, 0x15, 0xa5, 0x69, 0x8f, 0xf7, //0x00003900 .quad -608151266925095096
	0xfe, 0xaa, 0x24, 0xcb, 0x0b, 0xff, 0xeb, 0xaf, //0x00003908 .quad -5770238071427257602
	0x1b, 0x4d, 0x88, 0x5a, 0x0e, 0x44, 0x73, 0xb5, //0x00003910 .quad -5371875102083756773
	0xbe, 0xd5, 0xed, 0xbd, 0xce, 0xfe, 0xe6, 0xdb, //0x00003918 .quad -2601111570856684098
	0x30, 0x30, 0x95, 0xf8, 0x88, 0x0a, 0x68, 0x31, //0x00003920 .quad 3560107088838733872
	0x97, 0xa5, 0xb4, 0x36, 0x41, 0x5f, 0x70, 0x89, //0x00003928 .quad -8543223759426509417
	0x3d, 0x7c, 0xba, 0x36, 0x2b, 0x0d, 0xc2, 0xfd, //0x00003930 .quad -161552157378970563
	0xfc, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x00003938 .quad -6067343680855748868
	0x4c, 0x1b, 0x69, 0x04, 0x76, 0x90, 0x32, 0x3d, //0x00003940 .quad 4409745821703674700
	0xbc, 0x42, 0x7a, 0xe5, 0xd5, 0x94, 0xbf, 0xd6, //0x00003948 .quad -2972493582642298180
	0x0f, 0xb1, 0xc1, 0xc2, 0x49, 0x9a, 0x3f, 0xa6, //0x00003950 .quad -6467280898289979121
	0xb5, 0x69, 0x6c, 0xaf, 0x05, 0xbd, 0x37, 0x86, //0x00003958 .quad -8775337516792518219
	0x53, 0x1d, 0x72, 0x33, 0xdc, 0x80, 0xcf, 0x0f, //0x00003960 .quad 1139270913992301907
	0x23, 0x84, 0x47, 0x1b, 0x47, 0xac, 0xc5, 0xa7, //0x00003968 .quad -6357485877563259869
	0xa8, 0xa4, 0x4e, 0x40, 0x13, 0x61, 0xc3, 0xd3, //0x00003970 .quad -3187597375937010520
	0x2b, 0x65, 0x19, 0xe2, 0x58, 0x17, 0xb7, 0xd1, //0x00003978 .quad -3335171328526686933
	0xe9, 0x26, 0x31, 0x08, 0xac, 0x1c, 0x5a, 0x64, //0x00003980 .quad 7231123676894144233
	0x3b, 0xdf, 0x4f, 0x8d, 0x97, 0x6e, 0x12, 0x83, //0x00003988 .quad -9002011107970261189
	0xa3, 0x70, 0x3d, 0x0a, 0xd7, 0xa3, 0x70, 0x3d, //0x00003990 .quad 4427218577690292387
	0x0a, 0xd7, 0xa3, 0x70, 0x3d, 0x0a, 0xd7, 0xa3, //0x00003998 .quad -6640827866535438582
	0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x000039a0 QUAD $0xcccccccccccccccc; QUAD $0xcccccccccccccccc  // .space 16, '\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000039b0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x000039b8 .quad -9223372036854775808
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000039c0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa0, //0x000039c8 .quad -6917529027641081856
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000039d0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc8, //0x000039d8 .quad -4035225266123964416
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000039e0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfa, //0x000039e8 .quad -432345564227567616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000039f0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x9c, //0x000039f8 .quad -7187745005283311616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003a00 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xc3, //0x00003a08 .quad -4372995238176751616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003a10 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0xf4, //0x00003a18 .quad -854558029293551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003a20 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x96, 0x98, //0x00003a28 .quad -7451627795949551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003a30 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xbc, 0xbe, //0x00003a38 .quad -4702848726509551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003a40 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x6b, 0xee, //0x00003a48 .quad -1266874889709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003a50 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0xf9, 0x02, 0x95, //0x00003a58 .quad -7709325833709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003a60 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x40, 0xb7, 0x43, 0xba, //0x00003a68 .quad -5024971273709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003a70 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x10, 0xa5, 0xd4, 0xe8, //0x00003a78 .quad -1669528073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003a80 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x2a, 0xe7, 0x84, 0x91, //0x00003a88 .quad -7960984073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003a90 .quad 0
	0x00, 0x00, 0x00, 0x80, 0xf4, 0x20, 0xe6, 0xb5, //0x00003a98 .quad -5339544073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003aa0 .quad 0
	0x00, 0x00, 0x00, 0xa0, 0x31, 0xa9, 0x5f, 0xe3, //0x00003aa8 .quad -2062744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003ab0 .quad 0
	0x00, 0x00, 0x00, 0x04, 0xbf, 0xc9, 0x1b, 0x8e, //0x00003ab8 .quad -8206744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003ac0 .quad 0
	0x00, 0x00, 0x00, 0xc5, 0x2e, 0xbc, 0xa2, 0xb1, //0x00003ac8 .quad -5646744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003ad0 .quad 0
	0x00, 0x00, 0x40, 0x76, 0x3a, 0x6b, 0x0b, 0xde, //0x00003ad8 .quad -2446744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003ae0 .quad 0
	0x00, 0x00, 0xe8, 0x89, 0x04, 0x23, 0xc7, 0x8a, //0x00003ae8 .quad -8446744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003af0 .quad 0
	0x00, 0x00, 0x62, 0xac, 0xc5, 0xeb, 0x78, 0xad, //0x00003af8 .quad -5946744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003b00 .quad 0
	0x00, 0x80, 0x7a, 0x17, 0xb7, 0x26, 0xd7, 0xd8, //0x00003b08 .quad -2821744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003b10 .quad 0
	0x00, 0x90, 0xac, 0x6e, 0x32, 0x78, 0x86, 0x87, //0x00003b18 .quad -8681119073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003b20 .quad 0
	0x00, 0xb4, 0x57, 0x0a, 0x3f, 0x16, 0x68, 0xa9, //0x00003b28 .quad -6239712823709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003b30 .quad 0
	0x00, 0xa1, 0xed, 0xcc, 0xce, 0x1b, 0xc2, 0xd3, //0x00003b38 .quad -3187955011209551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003b40 .quad 0
	0xa0, 0x84, 0x14, 0x40, 0x61, 0x51, 0x59, 0x84, //0x00003b48 .quad -8910000909647051616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003b50 .quad 0
	0xc8, 0xa5, 0x19, 0x90, 0xb9, 0xa5, 0x6f, 0xa5, //0x00003b58 .quad -6525815118631426616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003b60 .quad 0
	0x3a, 0x0f, 0x20, 0xf4, 0x27, 0x8f, 0xcb, 0xce, //0x00003b68 .quad -3545582879861895366
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, //0x00003b70 .quad 4611686018427387904
	0x84, 0x09, 0x94, 0xf8, 0x78, 0x39, 0x3f, 0x81, //0x00003b78 .quad -9133518327554766460
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, //0x00003b80 .quad 5764607523034234880
	0xe5, 0x0b, 0xb9, 0x36, 0xd7, 0x07, 0x8f, 0xa1, //0x00003b88 .quad -6805211891016070171
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa4, //0x00003b90 .quad -6629298651489370112
	0xde, 0x4e, 0x67, 0x04, 0xcd, 0xc9, 0xf2, 0xc9, //0x00003b98 .quad -3894828845342699810
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4d, //0x00003ba0 .quad 5548434740920451072
	0x96, 0x22, 0x81, 0x45, 0x40, 0x7c, 0x6f, 0xfc, //0x00003ba8 .quad -256850038250986858
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xf0, //0x00003bb0 .quad -1143914305352105984
	0x9d, 0xb5, 0x70, 0x2b, 0xa8, 0xad, 0xc5, 0x9d, //0x00003bb8 .quad -7078060301547948643
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x6c, //0x00003bc0 .quad 7793479155164643328
	0x05, 0xe3, 0x4c, 0x36, 0x12, 0x19, 0x37, 0xc5, //0x00003bc8 .quad -4235889358507547899
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x32, 0xc7, //0x00003bd0 .quad -4093209111326359552
	0xc6, 0x1b, 0xe0, 0xc3, 0x56, 0xdf, 0x84, 0xf6, //0x00003bd8 .quad -683175679707046970
	0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x7f, 0x3c, //0x00003be0 .quad 4359273333062107136
	0x5c, 0x11, 0x6c, 0x3a, 0x96, 0x0b, 0x13, 0x9a, //0x00003be8 .quad -7344513827457986212
	0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x9f, 0x4b, //0x00003bf0 .quad 5449091666327633920
	0xb3, 0x15, 0x07, 0xc9, 0x7b, 0xce, 0x97, 0xc0, //0x00003bf8 .quad -4568956265895094861
	0x00, 0x00, 0x00, 0x00, 0x00, 0xd4, 0x86, 0x1e, //0x00003c00 .quad 2199678564482154496
	0x20, 0xdb, 0x48, 0xbb, 0x1a, 0xc2, 0xbd, 0xf0, //0x00003c08 .quad -1099509313941480672
	0x00, 0x00, 0x00, 0x00, 0x80, 0x44, 0x14, 0x13, //0x00003c10 .quad 1374799102801346560
	0xf4, 0x88, 0x0d, 0xb5, 0x50, 0x99, 0x76, 0x96, //0x00003c18 .quad -7604722348854507276
	0x00, 0x00, 0x00, 0x00, 0xa0, 0x55, 0xd9, 0x17, //0x00003c20 .quad 1718498878501683200
	0x31, 0xeb, 0x50, 0xe2, 0xa4, 0x3f, 0x14, 0xbc, //0x00003c28 .quad -4894216917640746191
	0x00, 0x00, 0x00, 0x00, 0x08, 0xab, 0xcf, 0x5d, //0x00003c30 .quad 6759809616554491904
	0xfd, 0x25, 0xe5, 0x1a, 0x8e, 0x4f, 0x19, 0xeb, //0x00003c38 .quad -1506085128623544835
	0x00, 0x00, 0x00, 0x00, 0xe5, 0xca, 0xa1, 0x5a, //0x00003c40 .quad 6530724019560251392
	0xbe, 0x37, 0xcf, 0xd0, 0xb8, 0xd1, 0xef, 0x92, //0x00003c48 .quad -7858832233030797378
	0x00, 0x00, 0x00, 0x40, 0x9e, 0x3d, 0x4a, 0xf1, //0x00003c50 .quad -1059967012404461568
	0xad, 0x05, 0x03, 0x05, 0x27, 0xc6, 0xab, 0xb7, //0x00003c58 .quad -5211854272861108819
	0x00, 0x00, 0x00, 0xd0, 0x05, 0xcd, 0x9c, 0x6d, //0x00003c60 .quad 7898413271349198848
	0x19, 0xc7, 0x43, 0xc6, 0xb0, 0xb7, 0x96, 0xe5, //0x00003c68 .quad -1903131822648998119
	0x00, 0x00, 0x00, 0xa2, 0x23, 0x00, 0x82, 0xe4, //0x00003c70 .quad -1981020733047832576
	0x6f, 0x5c, 0xea, 0x7b, 0xce, 0x32, 0x7e, 0x8f, //0x00003c78 .quad -8106986416796705681
	0x00, 0x00, 0x80, 0x8a, 0x2c, 0x80, 0xa2, 0xdd, //0x00003c80 .quad -2476275916309790720
	0x8b, 0xf3, 0xe4, 0x1a, 0x82, 0xbf, 0x5d, 0xb3, //0x00003c88 .quad -5522047002568494197
	0x00, 0x00, 0x20, 0xad, 0x37, 0x20, 0x0b, 0xd5, //0x00003c90 .quad -3095344895387238400
	0x6e, 0x30, 0x9e, 0xa1, 0x62, 0x2f, 0x35, 0xe0, //0x00003c98 .quad -2290872734783229842
	0x00, 0x00, 0x34, 0xcc, 0x22, 0xf4, 0x26, 0x45, //0x00003ca0 .quad 4982938468024057856
	0x45, 0xde, 0x02, 0xa5, 0x9d, 0x3d, 0x21, 0x8c, //0x00003ca8 .quad -8349324486880600507
	0x00, 0x00, 0x41, 0x7f, 0x2b, 0xb1, 0x70, 0x96, //0x00003cb0 .quad -7606384970252091392
	0xd6, 0x95, 0x43, 0x0e, 0x05, 0x8d, 0x29, 0xaf, //0x00003cb8 .quad -5824969590173362730
	0x00, 0x40, 0x11, 0x5f, 0x76, 0xdd, 0x0c, 0x3c, //0x00003cc0 .quad 4327076842467049472
	0x4c, 0x7b, 0xd4, 0x51, 0x46, 0xf0, 0xf3, 0xda, //0x00003cc8 .quad -2669525969289315508
	0x00, 0xc8, 0x6a, 0xfb, 0x69, 0x0a, 0x88, 0xa5, //0x00003cd0 .quad -6518949010312869888
	0x0f, 0xcd, 0x24, 0xf3, 0x2b, 0x76, 0xd8, 0x88, //0x00003cd8 .quad -8585982758446904049
	0x00, 0x7a, 0x45, 0x7a, 0x04, 0x0d, 0xea, 0x8e, //0x00003ce0 .quad -8148686262891087360
	0x53, 0x00, 0xee, 0xef, 0xb6, 0x93, 0x0e, 0xab, //0x00003ce8 .quad -6120792429631242157
	0x80, 0xd8, 0xd6, 0x98, 0x45, 0x90, 0xa4, 0x72, //0x00003cf0 .quad 8260886245095692416
	0x68, 0x80, 0xe9, 0xab, 0xa4, 0x38, 0xd2, 0xd5, //0x00003cf8 .quad -3039304518611664792
	0x50, 0x47, 0x86, 0x7f, 0x2b, 0xda, 0xa6, 0x47, //0x00003d00 .quad 5163053903184807760
	0x41, 0xf0, 0x71, 0xeb, 0x66, 0x63, 0xa3, 0x85, //0x00003d08 .quad -8817094351773372351
	0x24, 0xd9, 0x67, 0x5f, 0xb6, 0x90, 0x90, 0x99, //0x00003d10 .quad -7381240676301154012
	0x51, 0x6c, 0x4e, 0xa6, 0x40, 0x3c, 0x0c, 0xa7, //0x00003d18 .quad -6409681921289327535
	0x6d, 0xcf, 0x41, 0xf7, 0xe3, 0xb4, 0xf4, 0xff, //0x00003d20 .quad -3178808521666707
	0x65, 0x07, 0xe2, 0xcf, 0x50, 0x4b, 0xcf, 0xd0, //0x00003d28 .quad -3400416383184271515
	0xa4, 0x21, 0x89, 0x7a, 0x0e, 0xf1, 0xf8, 0xbf, //0x00003d30 .quad -4613672773753429596
	0x9f, 0x44, 0xed, 0x81, 0x12, 0x8f, 0x81, 0x82, //0x00003d38 .quad -9042789267131251553
	0x0d, 0x6a, 0x2b, 0x19, 0x52, 0x2d, 0xf7, 0xaf, //0x00003d40 .quad -5767090967191786995
	0xc7, 0x95, 0x68, 0x22, 0xd7, 0xf2, 0x21, 0xa3, //0x00003d48 .quad -6691800565486676537
	0x90, 0x44, 0x76, 0x9f, 0xa6, 0xf8, 0xf4, 0x9b, //0x00003d50 .quad -7208863708989733744
	0x39, 0xbb, 0x02, 0xeb, 0x8c, 0x6f, 0xea, 0xcb, //0x00003d58 .quad -3753064688430957767
	0xb4, 0xd5, 0x53, 0x47, 0xd0, 0x36, 0xf2, 0x02, //0x00003d60 .quad 212292400617608628
	0x08, 0x6a, 0xc3, 0x25, 0x70, 0x0b, 0xe5, 0xfe, //0x00003d68 .quad -79644842111309304
	0x90, 0x65, 0x94, 0x2c, 0x42, 0x62, 0xd7, 0x01, //0x00003d70 .quad 132682750386005392
	0x45, 0x22, 0x9a, 0x17, 0x26, 0x27, 0x4f, 0x9f, //0x00003d78 .quad -6967307053960650171
	0xf5, 0x7e, 0xb9, 0xb7, 0xd2, 0x3a, 0x4d, 0x42, //0x00003d80 .quad 4777539456409894645
	0xd6, 0xaa, 0x80, 0x9d, 0xef, 0xf0, 0x22, 0xc7, //0x00003d88 .quad -4097447799023424810
	0xb2, 0xde, 0xa7, 0x65, 0x87, 0x89, 0xe0, 0xd2, //0x00003d90 .quad -3251447716342407502
	0x8b, 0xd5, 0xe0, 0x84, 0x2b, 0xad, 0xeb, 0xf8, //0x00003d98 .quad -510123730351893109
	0x2f, 0xeb, 0x88, 0x9f, 0xf4, 0x55, 0xcc, 0x63, //0x00003da0 .quad 7191217214140771119
	0x77, 0x85, 0x0c, 0x33, 0x3b, 0x4c, 0x93, 0x9b, //0x00003da8 .quad -7236356359111015049
	0xfb, 0x25, 0x6b, 0xc7, 0x71, 0x6b, 0xbf, 0x3c, //0x00003db0 .quad 4377335499248575995
	0xd5, 0xa6, 0xcf, 0xff, 0x49, 0x1f, 0x78, 0xc2, //0x00003db8 .quad -4433759430461380907
	0x7a, 0xef, 0x45, 0x39, 0x4e, 0x46, 0xef, 0x8b, //0x00003dc0 .quad -8363388681221443718
	0x8a, 0x90, 0xc3, 0x7f, 0x1c, 0x27, 0x16, 0xf3, //0x00003dc8 .quad -930513269649338230
	0xac, 0xb5, 0xcb, 0xe3, 0xf0, 0x8b, 0x75, 0x97, //0x00003dd0 .quad -7532960934977096276
	0x56, 0x3a, 0xda, 0xcf, 0x71, 0xd8, 0xed, 0x97, //0x00003dd8 .quad -7499099821171918250
	0x17, 0xa3, 0xbe, 0x1c, 0xed, 0xee, 0x52, 0x3d, //0x00003de0 .quad 4418856886560793367
	0xec, 0xc8, 0xd0, 0x43, 0x8e, 0x4e, 0xe9, 0xbd, //0x00003de8 .quad -4762188758037509908
	0xdd, 0x4b, 0xee, 0x63, 0xa8, 0xaa, 0xa7, 0x4c, //0x00003df0 .quad 5523571108200991709
	0x27, 0xfb, 0xc4, 0xd4, 0x31, 0xa2, 0x63, 0xed, //0x00003df8 .quad -1341049929119499481
	0x6a, 0xef, 0x74, 0x3e, 0xa9, 0xca, 0xe8, 0x8f, //0x00003e00 .quad -8076983103442849942
	0xf8, 0x1c, 0xfb, 0x24, 0x5f, 0x45, 0x5e, 0x94, //0x00003e08 .quad -7755685233340769032
	0x44, 0x2b, 0x12, 0x8e, 0x53, 0xfd, 0xe2, 0xb3, //0x00003e10 .quad -5484542860876174524
	0x36, 0xe4, 0x39, 0xee, 0xb6, 0xd6, 0x75, 0xb9, //0x00003e18 .quad -5082920523248573386
	0x16, 0xb6, 0x96, 0x71, 0xa8, 0xbc, 0xdb, 0x60, //0x00003e20 .quad 6979379479186945558
	0x44, 0x5d, 0xc8, 0xa9, 0x64, 0x4c, 0xd3, 0xe7, //0x00003e28 .quad -1741964635633328828
	0xcd, 0x31, 0xfe, 0x46, 0xe9, 0x55, 0x89, 0xbc, //0x00003e30 .quad -4861259862362934835
	0x4a, 0x3a, 0x1d, 0xea, 0xbe, 0x0f, 0xe4, 0x90, //0x00003e38 .quad -8006256924911912374
	0x41, 0xbe, 0xbd, 0x98, 0x63, 0xab, 0xab, 0x6b, //0x00003e40 .quad 7758483227328495169
	0xdd, 0x88, 0xa4, 0xa4, 0xae, 0x13, 0x1d, 0xb5, //0x00003e48 .quad -5396135137712502563
	0xd1, 0x2d, 0xed, 0x7e, 0x3c, 0x96, 0x96, 0xc6, //0x00003e50 .quad -4136954021121544751
	0x14, 0xab, 0xcd, 0x4d, 0x9a, 0x58, 0x64, 0xe2, //0x00003e58 .quad -2133482903713240300
	0xa2, 0x3c, 0x54, 0xcf, 0xe5, 0x1d, 0x1e, 0xfc, //0x00003e60 .quad -279753253987271518
	0xec, 0x8a, 0xa0, 0x70, 0x60, 0xb7, 0x7e, 0x8d, //0x00003e68 .quad -8250955842461857044
	0xcb, 0x4b, 0x29, 0x43, 0x5f, 0xa5, 0x25, 0x3b, //0x00003e70 .quad 4261994450943298507
	0xa8, 0xad, 0xc8, 0x8c, 0x38, 0x65, 0xde, 0xb0, //0x00003e78 .quad -5702008784649933400
	0xbe, 0x9e, 0xf3, 0x13, 0xb7, 0x0e, 0xef, 0x49, //0x00003e80 .quad 5327493063679123134
	0x12, 0xd9, 0xfa, 0xaf, 0x86, 0xfe, 0x15, 0xdd, //0x00003e88 .quad -2515824962385028846
	0x37, 0x43, 0x78, 0x6c, 0x32, 0x69, 0x35, 0x6e, //0x00003e90 .quad 7941369183226839863
	0xab, 0xc7, 0xfc, 0x2d, 0x14, 0xbf, 0x2d, 0x8a, //0x00003e98 .quad -8489919629131724885
	0x04, 0x54, 0x96, 0x07, 0x7f, 0xc3, 0xc2, 0x49, //0x00003ea0 .quad 5315025460606161924
	0x96, 0xf9, 0x7b, 0x39, 0xd9, 0x2e, 0xb9, 0xac, //0x00003ea8 .quad -6000713517987268202
	0x06, 0xe9, 0x7b, 0xc9, 0x5e, 0x74, 0x33, 0xdc, //0x00003eb0 .quad -2579590211097073402
	0xfb, 0xf7, 0xda, 0x87, 0x8f, 0x7a, 0xe7, 0xd7, //0x00003eb8 .quad -2889205879056697349
	0xa3, 0x71, 0xed, 0x3d, 0xbb, 0x28, 0xa0, 0x69, //0x00003ec0 .quad 7611128154919104931
	0xfd, 0xda, 0xe8, 0xb4, 0x99, 0xac, 0xf0, 0x86, //0x00003ec8 .quad -8723282702051517699
	0x0c, 0xce, 0x68, 0x0d, 0xea, 0x32, 0x08, 0xc4, //0x00003ed0 .quad -4321147861633282548
	0xbc, 0x11, 0x23, 0x22, 0xc0, 0xd7, 0xac, 0xa8, //0x00003ed8 .quad -6292417359137009220
	0x90, 0x01, 0xc3, 0x90, 0xa4, 0x3f, 0x0a, 0xf5, //0x00003ee0 .quad -789748808614215280
	0x2b, 0xd6, 0xab, 0x2a, 0xb0, 0x0d, 0xd8, 0xd2, //0x00003ee8 .quad -3253835680493873621
	0xfa, 0xe0, 0x79, 0xda, 0xc6, 0x67, 0x26, 0x79, //0x00003ef0 .quad 8729779031470891258
	0xdb, 0x65, 0xab, 0x1a, 0x8e, 0x08, 0xc7, 0x83, //0x00003ef8 .quad -8951176327949752869
	0x38, 0x59, 0x18, 0x91, 0xb8, 0x01, 0x70, 0x57, //0x00003f00 .quad 6300537770911226168
	0x52, 0x3f, 0x56, 0xa1, 0xb1, 0xca, 0xb8, 0xa4, //0x00003f08 .quad -6577284391509803182
	0x86, 0x6f, 0x5e, 0xb5, 0x26, 0x02, 0x4c, 0xed, //0x00003f10 .quad -1347699823215743098
	0x26, 0xcf, 0xab, 0x09, 0x5e, 0xfd, 0xe6, 0xcd, //0x00003f18 .quad -3609919470959866074
	0xb4, 0x05, 0x5b, 0x31, 0x58, 0x81, 0x4f, 0x54, //0x00003f20 .quad 6075216638131242420
	0x78, 0x61, 0x0b, 0xc6, 0x5a, 0x5e, 0xb0, 0x80, //0x00003f28 .quad -9173728696990998152
	0x21, 0xc7, 0xb1, 0x3d, 0xae, 0x61, 0x63, 0x69, //0x00003f30 .quad 7594020797664053025
	0xd6, 0x39, 0x8e, 0x77, 0xf1, 0x75, 0xdc, 0xa0, //0x00003f38 .quad -6855474852811359786
	0xe9, 0x38, 0x1e, 0xcd, 0x19, 0x3a, 0xbc, 0x03, //0x00003f40 .quad 269153960225290473
	0x4c, 0xc8, 0x71, 0xd5, 0x6d, 0x93, 0x13, 0xc9, //0x00003f48 .quad -3957657547586811828
	0x23, 0xc7, 0x65, 0x40, 0xa0, 0x48, 0xab, 0x04, //0x00003f50 .quad 336442450281613091
	0x5f, 0x3a, 0xce, 0x4a, 0x49, 0x78, 0x58, 0xfb, //0x00003f58 .quad -335385916056126881
	0x76, 0x9c, 0x3f, 0x28, 0x64, 0x0d, 0xeb, 0x62, //0x00003f60 .quad 7127805559067090038
	0x7b, 0xe4, 0xc0, 0xce, 0x2d, 0x4b, 0x17, 0x9d, //0x00003f68 .quad -7127145225176161157
	0x94, 0x83, 0x4f, 0x32, 0xbd, 0xd0, 0xa5, 0x3b, //0x00003f70 .quad 4298070930406474644
	0x9a, 0x1d, 0x71, 0x42, 0xf9, 0x1d, 0x5d, 0xc4, //0x00003f78 .quad -4297245513042813542
	0x79, 0x64, 0xe3, 0x7e, 0xec, 0x44, 0x8f, 0xca, //0x00003f80 .quad -3850783373846682503
	0x00, 0x65, 0x0d, 0x93, 0x77, 0x65, 0x74, 0xf5, //0x00003f88 .quad -759870872876129024
	0xcb, 0x1e, 0x4e, 0xcf, 0x13, 0x8b, 0x99, 0x7e, //0x00003f90 .quad 9122475437414293195
	0x20, 0x5f, 0xe8, 0xbb, 0x6a, 0xbf, 0x68, 0x99, //0x00003f98 .quad -7392448323188662496
	0x7e, 0xa6, 0x21, 0xc3, 0xd8, 0xed, 0x3f, 0x9e, //0x00003fa0 .quad -7043649776941685122
	0xe8, 0x76, 0xe2, 0x6a, 0x45, 0xef, 0xc2, 0xbf, //0x00003fa8 .quad -4628874385558440216
	0x1e, 0x10, 0xea, 0xf3, 0x4e, 0xe9, 0xcf, 0xc5, //0x00003fb0 .quad -4192876202749718498
	0xa2, 0x14, 0x9b, 0xc5, 0x16, 0xab, 0xb3, 0xef, //0x00003fb8 .quad -1174406963520662366
	0x12, 0x4a, 0x72, 0x58, 0xd1, 0xf1, 0xa1, 0xbb, //0x00003fc0 .quad -4926390635932268014
	0xe5, 0xec, 0x80, 0x3b, 0xee, 0x4a, 0xd0, 0x95, //0x00003fc8 .quad -7651533379841495835
	0x97, 0xdc, 0x8e, 0xae, 0x45, 0x6e, 0x8a, 0x2a, //0x00003fd0 .quad 3065383741939440791
	0x1f, 0x28, 0x61, 0xca, 0xa9, 0x5d, 0x44, 0xbb, //0x00003fd8 .quad -4952730706374481889
	0xbd, 0x93, 0x32, 0x1a, 0xd7, 0x09, 0x2d, 0xf5, //0x00003fe0 .quad -779956341003086915
	0x26, 0x72, 0xf9, 0x3c, 0x14, 0x75, 0x15, 0xea, //0x00003fe8 .quad -1579227364540714458
	0x56, 0x9c, 0x5f, 0x70, 0x26, 0x26, 0x3c, 0x59, //0x00003ff0 .quad 6430056314514152534
	0x58, 0xe7, 0x1b, 0xa6, 0x2c, 0x69, 0x4d, 0x92, //0x00003ff8 .quad -7904546130479028392
	0x6c, 0x83, 0x77, 0x0c, 0xb0, 0x2f, 0x8b, 0x6f, //0x00004000 .quad 8037570393142690668
	0x2e, 0xe1, 0xa2, 0xcf, 0x77, 0xc3, 0xe0, 0xb6, //0x00004008 .quad -5268996644671397586
	0x47, 0x64, 0x95, 0x0f, 0x9c, 0xfb, 0x6d, 0x0b, //0x00004010 .quad 823590954573587527
	0x7a, 0x99, 0x8b, 0xc3, 0x55, 0xf4, 0x98, 0xe4, //0x00004018 .quad -1974559787411859078
	0xac, 0x5e, 0xbd, 0x89, 0x41, 0xbd, 0x24, 0x47, //0x00004020 .quad 5126430365035880108
	0xec, 0x3f, 0x37, 0x9a, 0xb5, 0x98, 0xdf, 0x8e, //0x00004028 .quad -8151628894773493780
	0x57, 0xb6, 0x2c, 0xec, 0x91, 0xec, 0xed, 0x58, //0x00004030 .quad 6408037956294850135
	0xe7, 0x0f, 0xc5, 0x00, 0xe3, 0x7e, 0x97, 0xb2, //0x00004038 .quad -5577850100039479321
	0xed, 0xe3, 0x37, 0x67, 0xb6, 0x67, 0x29, 0x2f, //0x00004040 .quad 3398361426941174765
	0xe1, 0x53, 0xf6, 0xc0, 0x9b, 0x5e, 0x3d, 0xdf, //0x00004048 .quad -2360626606621961247
	0x74, 0xee, 0x82, 0x00, 0xd2, 0xe0, 0x79, 0xbd, //0x00004050 .quad -4793553135802847628
	0x6c, 0xf4, 0x99, 0x58, 0x21, 0x5b, 0x86, 0x8b, //0x00004058 .quad -8392920656779807636
	0x11, 0xaa, 0xa3, 0x80, 0x06, 0x59, 0xd8, 0xec, //0x00004060 .quad -1380255401326171631
	0x87, 0x71, 0xc0, 0xae, 0xe9, 0xf1, 0x67, 0xae, //0x00004068 .quad -5879464802547371641
	0x95, 0x94, 0xcc, 0x20, 0x48, 0x6f, 0x0e, 0xe8, //0x00004070 .quad -1725319251657714539
	0xe9, 0x8d, 0x70, 0x1a, 0x64, 0xee, 0x01, 0xda, //0x00004078 .quad -2737644984756826647
	0xdd, 0xdc, 0x7f, 0x14, 0x8d, 0x05, 0x09, 0x31, //0x00004080 .quad 3533361486141316317
	0xb2, 0x58, 0x86, 0x90, 0xfe, 0x34, 0x41, 0x88, //0x00004088 .quad -8628557143114098510
	0x15, 0xd4, 0x9f, 0x59, 0xf0, 0x46, 0x4b, 0xbd, //0x00004090 .quad -4806670179178130411
	0xde, 0xee, 0xa7, 0x34, 0x3e, 0x82, 0x51, 0xaa, //0x00004098 .quad -6174010410465235234
	0x1a, 0xc9, 0x07, 0x70, 0xac, 0x18, 0x9e, 0x6c, //0x000040a0 .quad 7826720331309500698
	0x96, 0xea, 0xd1, 0xc1, 0xcd, 0xe2, 0xe5, 0xd4, //0x000040a8 .quad -3105826994654156138
	0xb0, 0xdd, 0x04, 0xc6, 0x6b, 0xcf, 0xe2, 0x03, //0x000040b0 .quad 280014188641050032
	0x9e, 0x32, 0x23, 0x99, 0xc0, 0xad, 0x0f, 0x85, //0x000040b8 .quad -8858670899299929442
	0x1c, 0x15, 0x86, 0xb7, 0x46, 0x83, 0xdb, 0x84, //0x000040c0 .quad -8873354301053463268
	0x45, 0xff, 0x6b, 0xbf, 0x30, 0x99, 0x53, 0xa6, //0x000040c8 .quad -6461652605697523899
	0x63, 0x9a, 0x67, 0x65, 0x18, 0x64, 0x12, 0xe6, //0x000040d0 .quad -1868320839462053277
	0x16, 0xff, 0x46, 0xef, 0x7c, 0x7f, 0xe8, 0xcf, //0x000040d8 .quad -3465379738694516970
	0x7e, 0xc0, 0x60, 0x3f, 0x8f, 0x7e, 0xcb, 0x4f, //0x000040e0 .quad 5749828502977298558
	0x6e, 0x5f, 0x8c, 0x15, 0xae, 0x4f, 0xf1, 0x81, //0x000040e8 .quad -9083391364325154962
	0x9d, 0xf0, 0x38, 0x0f, 0x33, 0x5e, 0xbe, 0xe3, //0x000040f0 .quad -2036086408133152611
	0x49, 0x77, 0xef, 0x9a, 0x99, 0xa3, 0x6d, 0xa2, //0x000040f8 .quad -6742553186979055799
	0xc5, 0x2c, 0x07, 0xd3, 0xbf, 0xf5, 0xad, 0x5c, //0x00004100 .quad 6678264026688335045
	0x1c, 0x55, 0xab, 0x01, 0x80, 0x0c, 0x09, 0xcb, //0x00004108 .quad -3816505465296431844
	0xf6, 0xf7, 0xc8, 0xc7, 0x2f, 0x73, 0xd9, 0x73, //0x00004110 .quad 8347830033360418806
	0x63, 0x2a, 0x16, 0x02, 0xa0, 0x4f, 0xcb, 0xfd, //0x00004118 .quad -158945813193151901
	0xfa, 0x9a, 0xdd, 0xdc, 0xfd, 0xe7, 0x67, 0x28, //0x00004120 .quad 2911550761636567802
	0x7e, 0xda, 0x4d, 0x01, 0xc4, 0x11, 0x9f, 0x9e, //0x00004128 .quad -7016870160886801794
	0xb8, 0x01, 0x15, 0x54, 0xfd, 0xe1, 0x81, 0xb2, //0x00004130 .quad -5583933584809066056
	0x1d, 0x51, 0xa1, 0x01, 0x35, 0xd6, 0x46, 0xc6, //0x00004138 .quad -4159401682681114339
	0x26, 0x42, 0x1a, 0xa9, 0x7c, 0x5a, 0x22, 0x1f, //0x00004140 .quad 2243455055843443238
	0x65, 0xa5, 0x09, 0x42, 0xc2, 0x8b, 0xd8, 0xf7, //0x00004148 .quad -587566084924005019
	0x58, 0x69, 0xb0, 0xe9, 0x8d, 0x78, 0x75, 0x33, //0x00004150 .quad 3708002419115845976
	0x5f, 0x07, 0x46, 0x69, 0x59, 0x57, 0xe7, 0x9a, //0x00004158 .quad -7284757830718584993
	0xae, 0x83, 0x1c, 0x64, 0xb1, 0xd6, 0x52, 0x00, //0x00004160 .quad 23317005467419566
	0x37, 0x89, 0x97, 0xc3, 0x2f, 0x2d, 0xa1, 0xc1, //0x00004168 .quad -4494261269970843337
	0x9a, 0xa4, 0x23, 0xbd, 0x5d, 0x8c, 0x67, 0xc0, //0x00004170 .quad -4582539761593113446
	0x84, 0x6b, 0x7d, 0xb4, 0x7b, 0x78, 0x09, 0xf2, //0x00004178 .quad -1006140569036166268
	0xe0, 0x46, 0x36, 0x96, 0xba, 0xb7, 0x40, 0xf8, //0x00004180 .quad -558244341782001952
	0x32, 0x63, 0xce, 0x50, 0x4d, 0xeb, 0x45, 0x97, //0x00004188 .quad -7546366883288685774
	0x98, 0xd8, 0xc3, 0x3b, 0xa9, 0xe5, 0x50, 0xb6, //0x00004190 .quad -5309491445654890344
	0xff, 0xfb, 0x01, 0xa5, 0x20, 0x66, 0x17, 0xbd, //0x00004198 .quad -4821272585683469313
	0xbe, 0xce, 0xb4, 0x8a, 0x13, 0x1f, 0xe5, 0xa3, //0x000041a0 .quad -6636864307068612930
	0xff, 0x7a, 0x42, 0xce, 0xa8, 0x3f, 0x5d, 0xec, //0x000041a8 .quad -1414904713676948737
	0x37, 0x01, 0xb1, 0x36, 0x6c, 0x33, 0x6f, 0xc6, //0x000041b0 .quad -4148040191917883081
	0xdf, 0x8c, 0xe9, 0x80, 0xc9, 0x47, 0xba, 0x93, //0x000041b8 .quad -7801844473689174817
	0x84, 0x41, 0x5d, 0x44, 0x47, 0x00, 0x0b, 0xb8, //0x000041c0 .quad -5185050239897353852
	0x17, 0xf0, 0x23, 0xe1, 0xbb, 0xd9, 0xa8, 0xb8, //0x000041c8 .quad -5140619573684080617
	0xe5, 0x91, 0x74, 0x15, 0x59, 0xc0, 0x0d, 0xa6, //0x000041d0 .quad -6481312799871692315
	0x1d, 0xec, 0x6c, 0xd9, 0x2a, 0x10, 0xd3, 0xe6, //0x000041d8 .quad -1814088448677712867
	0x2f, 0xdb, 0x68, 0xad, 0x37, 0x98, 0xc8, 0x87, //0x000041e0 .quad -8662506518347195601
	0x92, 0x13, 0xe4, 0xc7, 0x1a, 0xea, 0x43, 0x90, //0x000041e8 .quad -8051334308064652398
	0xfb, 0x11, 0xc3, 0x98, 0x45, 0xbe, 0xba, 0x29, //0x000041f0 .quad 3006924907348169211
	0x77, 0x18, 0xdd, 0x79, 0xa1, 0xe4, 0x54, 0xb4, //0x000041f8 .quad -5452481866653427593
	0x7a, 0xd6, 0xf3, 0xfe, 0xd6, 0x6d, 0x29, 0xf4, //0x00004200 .quad -853029884242176390
	0x94, 0x5e, 0x54, 0xd8, 0xc9, 0x1d, 0x6a, 0xe1, //0x00004208 .quad -2203916314889396588
	0x0c, 0x66, 0x58, 0x5f, 0xa6, 0xe4, 0x99, 0x18, //0x00004210 .quad 1772699331562333708
	0x1d, 0xbb, 0x34, 0x27, 0x9e, 0x52, 0xe2, 0x8c, //0x00004218 .quad -8294976724446954723
	0x8f, 0x7f, 0x2e, 0xf7, 0xcf, 0x5d, 0xc0, 0x5e, //0x00004220 .quad 6827560182880305039
	0xe4, 0xe9, 0x01, 0xb1, 0x45, 0xe7, 0x1a, 0xb0, //0x00004228 .quad -5757034887131305500
	0x73, 0x1f, 0xfa, 0xf4, 0x43, 0x75, 0x70, 0x76, //0x00004230 .quad 8534450228600381299
	0x5d, 0x64, 0x42, 0x1d, 0x17, 0xa1, 0x21, 0xdc, //0x00004238 .quad -2584607590486743971
	0xa8, 0x53, 0x1c, 0x79, 0x4a, 0x49, 0x06, 0x6a, //0x00004240 .quad 7639874402088932264
	0xba, 0x7e, 0x49, 0x72, 0xae, 0x04, 0x95, 0x89, //0x00004248 .quad -8532908771695296838
	0x92, 0x68, 0x63, 0x17, 0x9d, 0xdb, 0x87, 0x04, //0x00004250 .quad 326470965756389522
	0x69, 0xde, 0xdb, 0x0e, 0xda, 0x45, 0xfa, 0xab, //0x00004258 .quad -6054449946191733143
	0xb6, 0x42, 0x3c, 0x5d, 0x84, 0xd2, 0xa9, 0x45, //0x00004260 .quad 5019774725622874806
	0x03, 0xd6, 0x92, 0x92, 0x50, 0xd7, 0xf8, 0xd6, //0x00004268 .quad -2956376414312278525
	0xb2, 0xa9, 0x45, 0xba, 0x92, 0x23, 0x8a, 0x0b, //0x00004270 .quad 831516194300602802
	0xc2, 0xc5, 0x9b, 0x5b, 0x92, 0x86, 0x5b, 0x86, //0x00004278 .quad -8765264286586255934
	0x1e, 0x14, 0xd7, 0x68, 0x77, 0xac, 0x6c, 0x8e, //0x00004280 .quad -8183976793979022306
	0x32, 0xb7, 0x82, 0xf2, 0x36, 0x68, 0xf2, 0xa7, //0x00004288 .quad -6344894339805432014
	0x26, 0xd9, 0x0c, 0x43, 0x95, 0xd7, 0x07, 0x32, //0x00004290 .quad 3605087062808385830
	0xff, 0x64, 0x23, 0xaf, 0x44, 0x02, 0xef, 0xd1, //0x00004298 .quad -3319431906329402113
	0xb8, 0x07, 0xe8, 0x49, 0xbd, 0xe6, 0x44, 0x7f, //0x000042a0 .quad 9170708441896323000
	0x1f, 0x1f, 0x76, 0xed, 0x6a, 0x61, 0x35, 0x83, //0x000042a8 .quad -8992173969096958177
	0xa6, 0x09, 0x62, 0x9c, 0x6c, 0x20, 0x16, 0x5f, //0x000042b0 .quad 6851699533943015846
	0xe7, 0xa6, 0xd3, 0xa8, 0xc5, 0xb9, 0x02, 0xa4, //0x000042b8 .quad -6628531442943809817
	0x0f, 0x8c, 0x7a, 0xc3, 0x87, 0xa8, 0xdb, 0x36, //0x000042c0 .quad 3952938399001381903
	0xa1, 0x90, 0x08, 0x13, 0x37, 0x68, 0x03, 0xcd, //0x000042c8 .quad -3673978285252374367
	0x89, 0x97, 0x2c, 0xda, 0x54, 0x49, 0x49, 0xc2, //0x000042d0 .quad -4446942528265218167
	0x64, 0x5a, 0xe5, 0x6b, 0x22, 0x21, 0x22, 0x80, //0x000042d8 .quad -9213765455923815836
	0x6c, 0xbd, 0xb7, 0x10, 0xaa, 0x9b, 0xdb, 0xf2, //0x000042e0 .quad -946992141904134804
	0xfd, 0xb0, 0xde, 0x06, 0x6b, 0xa9, 0x2a, 0xa0, //0x000042e8 .quad -6905520801477381891
	0xc7, 0xac, 0xe5, 0x94, 0x94, 0x82, 0x92, 0x6f, //0x000042f0 .quad 8039631859474607303
	0x3d, 0x5d, 0x96, 0xc8, 0xc5, 0x53, 0x35, 0xc8, //0x000042f8 .quad -4020214983419339459
	0xf9, 0x17, 0x1f, 0xba, 0x39, 0x23, 0x77, 0xcb, //0x00004300 .quad -3785518230938904583
	0x8c, 0xf4, 0xbb, 0x3a, 0xb7, 0xa8, 0x42, 0xfa, //0x00004308 .quad -413582710846786420
	0xfb, 0x6e, 0x53, 0x14, 0x04, 0x76, 0x2a, 0xff, //0x00004310 .quad -60105885123121413
	0xd7, 0x78, 0xb5, 0x84, 0x72, 0xa9, 0x69, 0x9c, //0x00004318 .quad -7176018221920323369
	0xba, 0x4a, 0x68, 0x19, 0x85, 0x13, 0xf5, 0xfe, //0x00004320 .quad -75132356403901766
	0x0d, 0xd7, 0xe2, 0x25, 0xcf, 0x13, 0x84, 0xc3, //0x00004328 .quad -4358336758973016307
	0x69, 0x5d, 0xc2, 0x5f, 0x66, 0x58, 0xb2, 0x7e, //0x00004330 .quad 9129456591349898601
	0xd1, 0x8c, 0x5b, 0xef, 0xc2, 0x18, 0x65, 0xf4, //0x00004338 .quad -836234930288882479
	0x61, 0x7a, 0xd9, 0xfb, 0x3f, 0x77, 0x2f, 0xef, //0x00004340 .quad -1211618658047395231
	0x02, 0x38, 0x99, 0xd5, 0x79, 0x2f, 0xbf, 0x98, //0x00004348 .quad -7440175859071633406
	0xfa, 0xd8, 0xcf, 0xfa, 0x0f, 0x55, 0xfb, 0xaa, //0x00004350 .quad -6126209340986631942
	0x03, 0x86, 0xff, 0x4a, 0x58, 0xfb, 0xee, 0xbe, //0x00004358 .quad -4688533805412153853
	0x38, 0xcf, 0x83, 0xf9, 0x53, 0x2a, 0xba, 0x95, //0x00004360 .quad -7657761676233289928
	0x84, 0x67, 0xbf, 0x5d, 0x2e, 0xba, 0xaa, 0xee, //0x00004368 .quad -1248981238337804412
	0x83, 0x61, 0xf2, 0x7b, 0x74, 0x5a, 0x94, 0xdd, //0x00004370 .quad -2480258038432112253
	0xb2, 0xa0, 0x97, 0xfa, 0x5c, 0xb4, 0x2a, 0x95, //0x00004378 .quad -7698142301602209614
	0xe4, 0xf9, 0xee, 0x9a, 0x11, 0x71, 0xf9, 0x94, //0x00004380 .quad -7712008566467528220
	0xdf, 0x88, 0x3d, 0x39, 0x74, 0x61, 0x75, 0xba, //0x00004388 .quad -5010991858575374113
	0x5d, 0xb8, 0xaa, 0x01, 0x56, 0xcd, 0x37, 0x7a, //0x00004390 .quad 8806733365625141341
	0x17, 0xeb, 0x8c, 0x47, 0xd1, 0xb9, 0x12, 0xe9, //0x00004398 .quad -1652053804791829737
	0x3a, 0xb3, 0x0a, 0xc1, 0x55, 0xe0, 0x62, 0xac, //0x000043a0 .quad -6025006692552756422
	0xee, 0x12, 0xb8, 0xcc, 0x22, 0xb4, 0xab, 0x91, //0x000043a8 .quad -7950062655635975442
	0x09, 0x60, 0x4d, 0x31, 0x6b, 0x98, 0x7b, 0x57, //0x000043b0 .quad 6303799689591218185
	0xaa, 0x17, 0xe6, 0x7f, 0x2b, 0xa1, 0x16, 0xb6, //0x000043b8 .quad -5325892301117581398
	0x0b, 0xb8, 0xa0, 0xfd, 0x85, 0x7e, 0x5a, 0xed, //0x000043c0 .quad -1343622424865753077
	0x94, 0x9d, 0xdf, 0x5f, 0x76, 0x49, 0x9c, 0xe3, //0x000043c8 .quad -2045679357969588844
	0x07, 0x73, 0x84, 0xbe, 0x13, 0x8f, 0x58, 0x14, //0x000043d0 .quad 1466078993672598279
	0x7d, 0xc2, 0xeb, 0xfb, 0xe9, 0xad, 0x41, 0x8e, //0x000043d8 .quad -8196078626372074883
	0xc8, 0x8f, 0x25, 0xae, 0xd8, 0xb2, 0x6e, 0x59, //0x000043e0 .quad 6444284760518135752
	0x1c, 0xb3, 0xe6, 0x7a, 0x64, 0x19, 0xd2, 0xb1, //0x000043e8 .quad -5633412264537705700
	0xbb, 0xf3, 0xae, 0xd9, 0x8e, 0x5f, 0xca, 0x6f, //0x000043f0 .quad 8055355950647669691
	0xe3, 0x5f, 0xa0, 0x99, 0xbd, 0x9f, 0x46, 0xde, //0x000043f8 .quad -2430079312244744221
	0x54, 0x58, 0x0d, 0x48, 0xb9, 0x7b, 0xde, 0x25, //0x00004400 .quad 2728754459941099604
	0xee, 0x3b, 0x04, 0x80, 0xd6, 0x23, 0xec, 0x8a, //0x00004408 .quad -8436328597794046994
	0x6a, 0xae, 0x10, 0x9a, 0xa7, 0x1a, 0x56, 0xaf, //0x00004410 .quad -5812428961928401302
	0xe9, 0x4a, 0x05, 0x20, 0xcc, 0x2c, 0xa7, 0xad, //0x00004418 .quad -5933724728815170839
	0x04, 0xda, 0x94, 0x80, 0x51, 0xa1, 0x2b, 0x1b, //0x00004420 .quad 1957835834444274180
	0xa4, 0x9d, 0x06, 0x28, 0xff, 0xf7, 0x10, 0xd9, //0x00004428 .quad -2805469892591575644
	0x42, 0x08, 0x5d, 0xf0, 0xd2, 0x44, 0xfb, 0x90, //0x00004430 .quad -7999724640327104446
	0x86, 0x22, 0x04, 0x79, 0xff, 0x9a, 0xaa, 0x87, //0x00004438 .quad -8670947710510816634
	0x53, 0x4a, 0x74, 0xac, 0x07, 0x16, 0x3a, 0x35, //0x00004440 .quad 3835402254873283155
	0x28, 0x2b, 0x45, 0x57, 0xbf, 0x41, 0x95, 0xa9, //0x00004448 .quad -6226998619711132888
	0xe8, 0x5c, 0x91, 0x97, 0x89, 0x9b, 0x88, 0x42, //0x00004450 .quad 4794252818591603944
	0xf2, 0x75, 0x16, 0x2d, 0x2f, 0x92, 0xfa, 0xd3, //0x00004458 .quad -3172062256211528206
	0x11, 0xda, 0xba, 0xfe, 0x35, 0x61, 0x95, 0x69, //0x00004460 .quad 7608094030047140369
	0xb7, 0x09, 0x2e, 0x7c, 0x5d, 0x9b, 0x7c, 0x84, //0x00004468 .quad -8900067937773286985
	0x95, 0x90, 0x69, 0x7e, 0x83, 0xb9, 0xfa, 0x43, //0x00004470 .quad 4898431519131537557
	0x25, 0x8c, 0x39, 0xdb, 0x34, 0xc2, 0x9b, 0xa5, //0x00004478 .quad -6513398903789220827
	0xbb, 0xf4, 0x03, 0x5e, 0xe4, 0x67, 0xf9, 0x94, //0x00004480 .quad -7712018656367741765
	0x2e, 0xef, 0x07, 0x12, 0xc2, 0xb2, 0x02, 0xcf, //0x00004488 .quad -3530062611309138130
	0xf5, 0x78, 0xc2, 0xba, 0xee, 0xe0, 0x1b, 0x1d, //0x00004490 .quad 2097517367411243253
	0x7d, 0xf5, 0x44, 0x4b, 0xb9, 0xaf, 0x61, 0x81, //0x00004498 .quad -9123818159709293187
	0x32, 0x17, 0x73, 0x69, 0x2a, 0xd9, 0x62, 0x64, //0x000044a0 .quad 7233582727691441970
	0xdc, 0x32, 0x16, 0x9e, 0xa7, 0x1b, 0xba, 0xa1, //0x000044a8 .quad -6793086681209228580
	0xfe, 0xdc, 0xcf, 0x03, 0x75, 0x8f, 0x7b, 0x7d, //0x000044b0 .quad 9041978409614302462
	0x93, 0xbf, 0x9b, 0x85, 0x91, 0xa2, 0x28, 0xca, //0x000044b8 .quad -3879672333084147821
	0x3e, 0xd4, 0xc3, 0x44, 0x52, 0x73, 0xda, 0x5c, //0x000044c0 .quad 6690786993590490174
	0x78, 0xaf, 0x02, 0xe7, 0x35, 0xcb, 0xb2, 0xfc, //0x000044c8 .quad -237904397927796872
	0xa7, 0x64, 0xfa, 0x6a, 0x13, 0x88, 0x08, 0x3a, //0x000044d0 .quad 4181741870994056359
	0xab, 0xad, 0x61, 0xb0, 0x01, 0xbf, 0xef, 0x9d, //0x000044d8 .quad -7066219276345954901
	0xd0, 0xfd, 0xb8, 0x45, 0x18, 0xaa, 0x8a, 0x08, //0x000044e0 .quad 615491320315182544
	0x16, 0x19, 0x7a, 0x1c, 0xc2, 0xae, 0x6b, 0xc5, //0x000044e8 .quad -4221088077005055722
	0x45, 0x3d, 0x27, 0x57, 0x9e, 0x54, 0xad, 0x8a, //0x000044f0 .quad -8454007886460797627
	0x5b, 0x9f, 0x98, 0xa3, 0x72, 0x9a, 0xc6, 0xf6, //0x000044f8 .quad -664674077828931749
	0x4b, 0x86, 0x78, 0xf6, 0xe2, 0x54, 0xac, 0x36, //0x00004500 .quad 3939617107816777291
	0x99, 0x63, 0x3f, 0xa6, 0x87, 0x20, 0x3c, 0x9a, //0x00004508 .quad -7332950326284164199
	0xdd, 0xa7, 0x16, 0xb4, 0x1b, 0x6a, 0x57, 0x84, //0x00004510 .quad -8910536670511192099
	0x7f, 0x3c, 0xcf, 0x8f, 0xa9, 0x28, 0xcb, 0xc0, //0x00004518 .quad -4554501889427817345
	0xd5, 0x51, 0x1c, 0xa1, 0xa2, 0x44, 0x6d, 0x65, //0x00004520 .quad 7308573235570561493
	0x9f, 0x0b, 0xc3, 0xf3, 0xd3, 0xf2, 0xfd, 0xf0, //0x00004528 .quad -1081441343357383777
	0x25, 0xb3, 0xb1, 0xa4, 0xe5, 0x4a, 0x64, 0x9f, //0x00004530 .quad -6961356773836868827
	0x43, 0xe7, 0x59, 0x78, 0xc4, 0xb7, 0x9e, 0x96, //0x00004538 .quad -7593429867239446717
	0xee, 0x1f, 0xde, 0x0d, 0x9f, 0x5d, 0x3d, 0x87, //0x00004540 .quad -8701695967296086034
	0x14, 0x61, 0x70, 0x96, 0xb5, 0x65, 0x46, 0xbc, //0x00004548 .quad -4880101315621920492
	0xea, 0xa7, 0x55, 0xd1, 0x06, 0xb5, 0x0c, 0xa9, //0x00004550 .quad -6265433940692719638
	0x59, 0x79, 0x0c, 0xfc, 0x22, 0xff, 0x57, 0xeb, //0x00004558 .quad -1488440626100012711
	0xf2, 0x88, 0xd5, 0x42, 0x24, 0xf1, 0xa7, 0x09, //0x00004560 .quad 695789805494438130
	0xd8, 0xcb, 0x87, 0xdd, 0x75, 0xff, 0x16, 0x93, //0x00004568 .quad -7847804418953589800
	0x2f, 0xeb, 0x8a, 0x53, 0x6d, 0xed, 0x11, 0x0c, //0x00004570 .quad 869737256868047663
	0xce, 0xbe, 0xe9, 0x54, 0x53, 0xbf, 0xdc, 0xb7, //0x00004578 .quad -5198069505264599346
	0xfa, 0xa5, 0x6d, 0xa8, 0xc8, 0x68, 0x16, 0x8f, //0x00004580 .quad -8136200465769716230
	0x81, 0x2e, 0x24, 0x2a, 0x28, 0xef, 0xd3, 0xe5, //0x00004588 .quad -1885900863153361279
	0xbc, 0x87, 0x44, 0x69, 0x7d, 0x01, 0x6e, 0xf9, //0x00004590 .quad -473439272678684740
	0x10, 0x9d, 0x56, 0x1a, 0x79, 0x75, 0xa4, 0x8f, //0x00004598 .quad -8096217067111932656
	0xac, 0xa9, 0x95, 0xc3, 0xdc, 0x81, 0xc9, 0x37, //0x000045a0 .quad 4019886927579031980
	0x55, 0x44, 0xec, 0x60, 0xd7, 0x92, 0x8d, 0xb3, //0x000045a8 .quad -5508585315462527915
	0x17, 0x14, 0x7b, 0xf4, 0x53, 0xe2, 0xbb, 0x85, //0x000045b0 .quad -8810199395808373737
	0x6a, 0x55, 0x27, 0x39, 0x8d, 0xf7, 0x70, 0xe0, //0x000045b8 .quad -2274045625900771990
	0x8e, 0xec, 0xcc, 0x78, 0x74, 0x6d, 0x95, 0x93, //0x000045c0 .quad -7812217631593927538
	0x62, 0x95, 0xb8, 0x43, 0xb8, 0x9a, 0x46, 0x8c, //0x000045c8 .quad -8338807543829064350
	0xb2, 0x27, 0x00, 0x97, 0xd1, 0xc8, 0x7a, 0x38, //0x000045d0 .quad 4069786015789754290
	0xbb, 0xba, 0xa6, 0x54, 0x66, 0x41, 0x58, 0xaf, //0x000045d8 .quad -5811823411358942533
	0x9e, 0x31, 0xc0, 0xfc, 0x05, 0x7b, 0x99, 0x06, //0x000045e0 .quad 475546501309804958
	0x6a, 0x69, 0xd0, 0xe9, 0xbf, 0x51, 0x2e, 0xdb, //0x000045e8 .quad -2653093245771290262
	0x03, 0x1f, 0xf8, 0xbd, 0xe3, 0xec, 0x1f, 0x44, //0x000045f0 .quad 4908902581746016003
	0xe2, 0x41, 0x22, 0xf2, 0x17, 0xf3, 0xfc, 0x88, //0x000045f8 .quad -8575712306248138270
	0xc3, 0x26, 0x76, 0xad, 0x1c, 0xe8, 0x27, 0xd5, //0x00004600 .quad -3087243809672255805
	0x5a, 0xd2, 0xaa, 0xee, 0xdd, 0x2f, 0x3c, 0xab, //0x00004608 .quad -6107954364382784934
	0x74, 0xb0, 0xd3, 0xd8, 0x23, 0xe2, 0x71, 0x8a, //0x00004610 .quad -8470740780517707660
	0xf1, 0x86, 0x55, 0x6a, 0xd5, 0x3b, 0x0b, 0xd6, //0x00004618 .quad -3023256937051093263
	0x49, 0x4e, 0x84, 0x67, 0x56, 0x2d, 0x87, 0xf6, //0x00004620 .quad -682526969396179383
	0x56, 0x74, 0x75, 0x62, 0x65, 0x05, 0xc7, 0x85, //0x00004628 .quad -8807064613298015146
	0xdb, 0x61, 0x65, 0x01, 0xac, 0xf8, 0x28, 0xb4, //0x00004630 .quad -5464844730172612133
	0x6c, 0xd1, 0x12, 0xbb, 0xbe, 0xc6, 0x38, 0xa7, //0x00004638 .quad -6397144748195131028
	0x52, 0xba, 0xbe, 0x01, 0xd7, 0x36, 0x33, 0xe1, //0x00004640 .quad -2219369894288377262
	0xc7, 0x85, 0xd7, 0x69, 0x6e, 0xf8, 0x06, 0xd1, //0x00004648 .quad -3384744916816525881
	0x73, 0x34, 0x17, 0x61, 0x46, 0x02, 0xc0, 0xec, //0x00004650 .quad -1387106183930235789
	0x9c, 0xb3, 0x26, 0x02, 0x45, 0x5b, 0xa4, 0x82, //0x00004658 .quad -9032994600651410532
	0x90, 0x01, 0x5d, 0xf9, 0xd7, 0x02, 0xf0, 0x27, //0x00004660 .quad 2877803288514593168
	0x84, 0x60, 0xb0, 0x42, 0x16, 0x72, 0x4d, 0xa3, //0x00004668 .quad -6679557232386875260
	0xf4, 0x41, 0xb4, 0xf7, 0x8d, 0x03, 0xec, 0x31, //0x00004670 .quad 3597254110643241460
	0xa5, 0x78, 0x5c, 0xd3, 0x9b, 0xce, 0x20, 0xcc, //0x00004678 .quad -3737760522056206171
	0x71, 0x52, 0xa1, 0x75, 0x71, 0x04, 0x67, 0x7e, //0x00004680 .quad 9108253656731439729
	0xce, 0x96, 0x33, 0xc8, 0x42, 0x02, 0x29, 0xff, //0x00004688 .quad -60514634142869810
	0x86, 0xd3, 0x84, 0xe9, 0xc6, 0x62, 0x00, 0x0f, //0x00004690 .quad 1080972517029761926
	0x41, 0x3e, 0x20, 0xbd, 0x69, 0xa1, 0x79, 0x9f, //0x00004698 .quad -6955350673980375487
	0x68, 0x08, 0xe6, 0xa3, 0x78, 0x7b, 0xc0, 0x52, //0x000046a0 .quad 5962901664714590312
	0xd1, 0x4d, 0x68, 0x2c, 0xc4, 0x09, 0x58, 0xc7, //0x000046a8 .quad -4082502324048081455
	0x82, 0x8a, 0xdf, 0xcc, 0x56, 0x9a, 0x70, 0xa7, //0x000046b0 .quad -6381430974388925822
	0x45, 0x61, 0x82, 0x37, 0x35, 0x0c, 0x2e, 0xf9, //0x000046b8 .quad -491441886632713915
	0x91, 0xb6, 0x0b, 0x40, 0x76, 0x60, 0xa6, 0x88, //0x000046c0 .quad -8600080377420466543
	0xcb, 0x7c, 0xb1, 0x42, 0xa1, 0xc7, 0xbc, 0x9b, //0x000046c8 .quad -7224680206786528053
	0x35, 0xa4, 0x0e, 0xd0, 0x93, 0xf8, 0xcf, 0x6a, //0x000046d0 .quad 7696643601933968437
	0xfe, 0xdb, 0x5d, 0x93, 0x89, 0xf9, 0xab, 0xc2, //0x000046d8 .quad -4419164240055772162
	0x43, 0x4d, 0x12, 0xc4, 0xb8, 0xf6, 0x83, 0x05, //0x000046e0 .quad 397432465562684739
	0xfe, 0x52, 0x35, 0xf8, 0xeb, 0xf7, 0x56, 0xf3, //0x000046e8 .quad -912269281642327298
	0x4a, 0x70, 0x8b, 0x7a, 0x33, 0x7a, 0x72, 0xc3, //0x000046f0 .quad -4363290727450709942
	0xde, 0x53, 0x21, 0x7b, 0xf3, 0x5a, 0x16, 0x98, //0x000046f8 .quad -7487697328667536418
	0x5c, 0x4c, 0x2e, 0x59, 0xc0, 0x18, 0x4f, 0x74, //0x00004700 .quad 8380944645968776284
	0xd6, 0xa8, 0xe9, 0x59, 0xb0, 0xf1, 0x1b, 0xbe, //0x00004708 .quad -4747935642407032618
	0x73, 0xdf, 0x79, 0x6f, 0xf0, 0xde, 0x62, 0x11, //0x00004710 .quad 1252808770606194547
	0x0c, 0x13, 0x64, 0x70, 0x1c, 0xee, 0xa2, 0xed, //0x00004718 .quad -1323233534581402868
	0xa8, 0x2b, 0xac, 0x45, 0x56, 0xcb, 0xdd, 0x8a, //0x00004720 .quad -8440366555225904216
	0xe7, 0x8b, 0x3e, 0xc6, 0xd1, 0xd4, 0x85, 0x94, //0x00004728 .quad -7744549986754458649
	0x92, 0x36, 0x17, 0xd7, 0x2b, 0x3e, 0x95, 0x6d, //0x00004730 .quad 7896285879677171346
	0xe1, 0x2e, 0xce, 0x37, 0x06, 0x4a, 0xa7, 0xb9, //0x00004738 .quad -5069001465015685407
	0x37, 0x04, 0xdd, 0xcc, 0xb6, 0x8d, 0xfa, 0xc8, //0x00004740 .quad -3964700705685699529
	0x99, 0xba, 0xc1, 0xc5, 0x87, 0x1c, 0x11, 0xe8, //0x00004748 .quad -1724565812842218855
	0xa2, 0x22, 0x0a, 0x40, 0x92, 0x98, 0x9c, 0x1d, //0x00004750 .quad 2133748077373825698
	0xa0, 0x14, 0x99, 0xdb, 0xd4, 0xb1, 0x0a, 0x91, //0x00004758 .quad -7995382660667468640
	0x4b, 0xab, 0x0c, 0xd0, 0xb6, 0xbe, 0x03, 0x25, //0x00004760 .quad 2667185096717282123
	0xc8, 0x59, 0x7f, 0x12, 0x4a, 0x5e, 0x4d, 0xb5, //0x00004768 .quad -5382542307406947896
	0x1d, 0xd6, 0x0f, 0x84, 0x64, 0xae, 0x44, 0x2e, //0x00004770 .quad 3333981370896602653
	0x3a, 0x30, 0x1f, 0x97, 0xdc, 0xb5, 0xa0, 0xe2, //0x00004778 .quad -2116491865831296966
	0xd2, 0xe5, 0x89, 0xd2, 0xfe, 0xec, 0xea, 0x5c, //0x00004780 .quad 6695424375237764562
	0x24, 0x7e, 0x73, 0xde, 0xa9, 0x71, 0xa4, 0x8d, //0x00004788 .quad -8240336443785642460
	0x47, 0x5f, 0x2c, 0x87, 0x3e, 0xa8, 0x25, 0x74, //0x00004790 .quad 8369280469047205703
	0xad, 0x5d, 0x10, 0x56, 0x14, 0x8e, 0x0d, 0xb1, //0x00004798 .quad -5688734536304665171
	0x19, 0x77, 0xf7, 0x28, 0x4e, 0x12, 0x2f, 0xd1, //0x000047a0 .quad -3373457468973156583
	0x18, 0x75, 0x94, 0x6b, 0x99, 0xf1, 0x50, 0xdd, //0x000047a8 .quad -2499232151953443560
	0x6f, 0xaa, 0x9a, 0xd9, 0x70, 0x6b, 0xbd, 0x82, //0x000047b0 .quad -9025939945749304721
	0x2f, 0xc9, 0x3c, 0xe3, 0xff, 0x96, 0x52, 0x8a, //0x000047b8 .quad -8479549122611984081
	0x0b, 0x55, 0x01, 0x10, 0x4d, 0xc6, 0x6c, 0x63, //0x000047c0 .quad 7164319141522920715
	0x7b, 0xfb, 0x0b, 0xdc, 0xbf, 0x3c, 0xe7, 0xac, //0x000047c8 .quad -5987750384837592197
	0x4e, 0xaa, 0x01, 0x54, 0xe0, 0xf7, 0x47, 0x3c, //0x000047d0 .quad 4343712908476262990
	0x5a, 0xfa, 0x0e, 0xd3, 0xef, 0x0b, 0x21, 0xd8, //0x000047d8 .quad -2873001962619602342
	0x71, 0x0a, 0x81, 0x34, 0xec, 0xfa, 0xac, 0x65, //0x000047e0 .quad 7326506586225052273
	0x78, 0x5c, 0xe9, 0xe3, 0x75, 0xa7, 0x14, 0x87, //0x000047e8 .quad -8713155254278333320
	0x0d, 0x4d, 0xa1, 0x41, 0xa7, 0x39, 0x18, 0x7f, //0x000047f0 .quad 9158133232781315341
	0x96, 0xb3, 0xe3, 0x5c, 0x53, 0xd1, 0xd9, 0xa8, //0x000047f8 .quad -6279758049420528746
	0x50, 0xa0, 0x09, 0x12, 0x11, 0x48, 0xde, 0x1e, //0x00004800 .quad 2224294504121868368
	0x7c, 0xa0, 0x1c, 0x34, 0xa8, 0x45, 0x10, 0xd3, //0x00004808 .quad -3238011543348273028
	0x32, 0x04, 0x46, 0xab, 0x0a, 0xed, 0x4a, 0x93, //0x00004810 .quad -7833187971778608078
	0x4d, 0xe4, 0x91, 0x20, 0x89, 0x2b, 0xea, 0x83, //0x00004818 .quad -8941286242233752499
	0x3f, 0x85, 0x17, 0x56, 0x4d, 0xa8, 0x1d, 0xf8, //0x00004820 .quad -568112927868484289
	0x60, 0x5d, 0xb6, 0x68, 0x6b, 0xb6, 0xe4, 0xa4, //0x00004828 .quad -6564921784364802720
	0x8e, 0x66, 0x9d, 0xab, 0x60, 0x12, 0x25, 0x36, //0x00004830 .quad 3901544858591782542
	0xb9, 0xf4, 0xe3, 0x42, 0x06, 0xe4, 0x1d, 0xce, //0x00004838 .quad -3594466212028615495
	0x19, 0x60, 0x42, 0x6b, 0x7c, 0x2b, 0xd7, 0xc1, //0x00004840 .quad -4479063491021217767
	0xf3, 0x78, 0xce, 0xe9, 0x83, 0xae, 0xd2, 0x80, //0x00004848 .quad -9164070410158966541
	0x1f, 0xf8, 0x12, 0x86, 0x5b, 0xf6, 0x4c, 0xb2, //0x00004850 .quad -5598829363776522209
	0x30, 0x17, 0x42, 0xe4, 0x24, 0x5a, 0x07, 0xa1, //0x00004858 .quad -6843401994271320272
	0x27, 0xb6, 0x97, 0x67, 0xf2, 0x33, 0xe0, 0xde, //0x00004860 .quad -2386850686293264857
	0xfc, 0x9c, 0x52, 0x1d, 0xae, 0x30, 0x49, 0xc9, //0x00004868 .quad -3942566474411762436
	0xb1, 0xa3, 0x7d, 0x01, 0xef, 0x40, 0x98, 0x16, //0x00004870 .quad 1628122660560806833
	0x3c, 0x44, 0xa7, 0xa4, 0xd9, 0x7c, 0x9b, 0xfb, //0x00004878 .quad -316522074587315140
	0x4e, 0x86, 0xee, 0x60, 0x95, 0x28, 0x1f, 0x8e, //0x00004880 .quad -8205795374004271538
	0xa5, 0x8a, 0xe8, 0x06, 0x08, 0x2e, 0x41, 0x9d, //0x00004888 .quad -7115355324258153819
	0xe2, 0x27, 0x2a, 0xb9, 0xba, 0xf2, 0xa6, 0xf1, //0x00004890 .quad -1033872180650563614
	0x4e, 0xad, 0xa2, 0x08, 0x8a, 0x79, 0x91, 0xc4, //0x00004898 .quad -4282508136895304370
	0xdb, 0xb1, 0x74, 0x67, 0x69, 0xaf, 0x10, 0xae, //0x000048a0 .quad -5904026244240592421
	0xa2, 0x58, 0xcb, 0x8a, 0xec, 0xd7, 0xb5, 0xf5, //0x000048a8 .quad -741449152691742558
	0x29, 0xef, 0xa8, 0xe0, 0xa1, 0x6d, 0xca, 0xac, //0x000048b0 .quad -5995859411864064215
	0x65, 0x17, 0xbf, 0xd6, 0xf3, 0xa6, 0x91, 0x99, //0x000048b8 .quad -7380934748073420955
	0xf3, 0x2a, 0xd3, 0x58, 0x0a, 0x09, 0xfd, 0x17, //0x000048c0 .quad 1728547772024695539
	0x3f, 0xdd, 0x6e, 0xcc, 0xb0, 0x10, 0xf6, 0xbf, //0x000048c8 .quad -4614482416664388289
	0xb0, 0xf5, 0x07, 0xef, 0x4c, 0x4b, 0xfc, 0xdd, //0x000048d0 .quad -2451001303396518480
	0x8e, 0x94, 0x8a, 0xff, 0xdc, 0x94, 0xf3, 0xef, //0x000048d8 .quad -1156417002403097458
	0x8e, 0xf9, 0x64, 0x15, 0x10, 0xaf, 0xbd, 0x4a, //0x000048e0 .quad 5385653213018257806
	0xd9, 0x9c, 0xb6, 0x1f, 0x0a, 0x3d, 0xf8, 0x95, //0x000048e8 .quad -7640289654143017767
	0xf1, 0x37, 0xbe, 0x1a, 0xd4, 0x1a, 0x6d, 0x9d, //0x000048f0 .quad -7102991539009341455
	0x0f, 0x44, 0xa4, 0xa7, 0x4c, 0x4c, 0x76, 0xbb, //0x000048f8 .quad -4938676049251384305
	0xed, 0xc5, 0x6d, 0x21, 0x89, 0x61, 0xc8, 0x84, //0x00004900 .quad -8878739423761676819
	0x13, 0x55, 0x8d, 0xd1, 0x5f, 0xdf, 0x53, 0xea, //0x00004908 .quad -1561659043136842477
	0xb4, 0x9b, 0xe4, 0xb4, 0xf5, 0x3c, 0xfd, 0x32, //0x00004910 .quad 3674159897003727796
	0x2c, 0x55, 0xf8, 0xe2, 0x9b, 0x6b, 0x74, 0x92, //0x00004918 .quad -7893565929601608404
	0xa1, 0xc2, 0x1d, 0x22, 0x33, 0x8c, 0xbc, 0x3f, //0x00004920 .quad 4592699871254659745
	0x77, 0x6a, 0xb6, 0xdb, 0x82, 0x86, 0x11, 0xb7, //0x00004928 .quad -5255271393574622601
	0x4a, 0x33, 0xa5, 0xea, 0x3f, 0xaf, 0xab, 0x0f, //0x00004930 .quad 1129188820640936778
	0x15, 0x05, 0xa4, 0x92, 0x23, 0xe8, 0xd5, 0xe4, //0x00004938 .quad -1957403223540890347
	0x0e, 0x40, 0xa7, 0xf2, 0x87, 0x4d, 0xcb, 0x29, //0x00004940 .quad 3011586022114279438
	0x2d, 0x83, 0xa6, 0x3b, 0x16, 0xb1, 0x05, 0x8f, //0x00004948 .quad -8140906042354138323
	0x12, 0x10, 0x51, 0xef, 0xe9, 0x20, 0x3e, 0x74, //0x00004950 .quad 8376168546070237202
	0xf8, 0x23, 0x90, 0xca, 0x5b, 0x1d, 0xc7, 0xb2, //0x00004958 .quad -5564446534515285000
	0x16, 0x54, 0x25, 0x6b, 0x24, 0xa9, 0x4d, 0x91, //0x00004960 .quad -7976533391121755114
	0xf6, 0x2c, 0x34, 0xbd, 0xb2, 0xe4, 0x78, 0xdf, //0x00004968 .quad -2343872149716718346
	0x8e, 0x54, 0xf7, 0xc2, 0xb6, 0x89, 0xd0, 0x1a, //0x00004970 .quad 1932195658189984910
	0x1a, 0x9c, 0x40, 0xb6, 0xef, 0x8e, 0xab, 0x8b, //0x00004978 .quad -8382449121214030822
	0xb1, 0x29, 0xb5, 0x73, 0x24, 0xac, 0x84, 0xa1, //0x00004980 .quad -6808127464117294671
	0x20, 0xc3, 0xd0, 0xa3, 0xab, 0x72, 0x96, 0xae, //0x00004988 .quad -5866375383090150624
	0x1e, 0x74, 0xa2, 0x90, 0x2d, 0xd7, 0xe5, 0xc9, //0x00004990 .quad -3898473311719230434
	0xe8, 0xf3, 0xc4, 0x8c, 0x56, 0x0f, 0x3c, 0xda, //0x00004998 .quad -2721283210435300376
	0x92, 0x88, 0x65, 0x7a, 0x7c, 0xa6, 0x2f, 0x7e, //0x000049a0 .quad 9092669226243950738
	0x71, 0x18, 0xfb, 0x17, 0x96, 0x89, 0x65, 0x88, //0x000049a8 .quad -8618331034163144591
	0xb7, 0xea, 0xfe, 0x98, 0x1b, 0x90, 0xbb, 0xdd, //0x000049b0 .quad -2469221522477225289
	0x8d, 0xde, 0xf9, 0x9d, 0xfb, 0xeb, 0x7e, 0xaa, //0x000049b8 .quad -6161227774276542835
	0x65, 0xa5, 0x3e, 0x7f, 0x22, 0x74, 0x2a, 0x55, //0x000049c0 .quad 6136845133758244197
	0x31, 0x56, 0x78, 0x85, 0xfa, 0xa6, 0x1e, 0xd5, //0x000049c8 .quad -3089848699418290639
	0x5f, 0x27, 0x87, 0x8f, 0x95, 0x88, 0x3a, 0xd5, //0x000049d0 .quad -3082000819042179233
	0xde, 0x35, 0x6b, 0x93, 0x5c, 0x28, 0x33, 0x85, //0x000049d8 .quad -8848684464777513506
	0x37, 0xf1, 0x68, 0xf3, 0xba, 0x2a, 0x89, 0x8a, //0x000049e0 .quad -8464187042230111945
	0x56, 0x03, 0x46, 0xb8, 0x73, 0xf2, 0x7f, 0xa6, //0x000049e8 .quad -6449169562544503978
	0x85, 0x2d, 0x43, 0xb0, 0x69, 0x75, 0x2b, 0x2d, //0x000049f0 .quad 3254824252494523781
	0x2c, 0x84, 0x57, 0xa6, 0x10, 0xef, 0x1f, 0xd0, //0x000049f8 .quad -3449775934753242068
	0x73, 0xfc, 0x29, 0x0e, 0x62, 0x29, 0x3b, 0x9c, //0x00004a00 .quad -7189106879045698445
	0x9b, 0xb2, 0xf6, 0x67, 0x6a, 0xf5, 0x13, 0x82, //0x00004a08 .quad -9073638986861858149
	0x8f, 0x7b, 0xb4, 0x91, 0xba, 0xf3, 0x49, 0x83, //0x00004a10 .quad -8986383598807123057
	0x42, 0x5f, 0xf4, 0x01, 0xc5, 0xf2, 0x98, 0xa2, //0x00004a18 .quad -6730362715149934782
	0x73, 0x9a, 0x21, 0x36, 0xa9, 0x70, 0x1c, 0x24, //0x00004a20 .quad 2602078556773259891
	0x13, 0x77, 0x71, 0x42, 0x76, 0x2f, 0x3f, 0xcb, //0x00004a28 .quad -3801267375510030573
	0x10, 0x01, 0xaa, 0x83, 0xd3, 0x8c, 0x23, 0xed, //0x00004a30 .quad -1359087822460813040
	0xd7, 0xd4, 0x0d, 0xd3, 0x53, 0xfb, 0x0e, 0xfe, //0x00004a38 .quad -139898200960150313
	0xaa, 0x40, 0x4a, 0x32, 0x04, 0x38, 0x36, 0xf4, //0x00004a40 .quad -849429889038008150
	0x06, 0xa5, 0xe8, 0x63, 0x14, 0x5d, 0xc9, 0x9e, //0x00004a48 .quad -7004965403241175802
	0xd5, 0xd0, 0xdc, 0x3e, 0x05, 0xc6, 0x43, 0xb1, //0x00004a50 .quad -5673473379724898091
	0x48, 0xce, 0xe2, 0x7c, 0x59, 0xb4, 0x7b, 0xc6, //0x00004a58 .quad -4144520735624081848
	0x0a, 0x05, 0x94, 0x8e, 0x86, 0xb7, 0x94, 0xdd, //0x00004a60 .quad -2480155706228734710
	0xda, 0x81, 0x1b, 0xdc, 0x6f, 0xa1, 0x1a, 0xf8, //0x00004a68 .quad -568964901102714406
	0x26, 0x83, 0x1c, 0x19, 0xb4, 0xf2, 0x7c, 0xca, //0x00004a70 .quad -3855940325606653146
	0x28, 0x31, 0x91, 0xe9, 0xe5, 0xa4, 0x10, 0x9b, //0x00004a78 .quad -7273132090830278360
	0xf0, 0xa3, 0x63, 0x1f, 0x61, 0x2f, 0x1c, 0xfd, //0x00004a80 .quad -208239388580928528
	0x72, 0x7d, 0xf5, 0x63, 0x1f, 0xce, 0xd4, 0xc1, //0x00004a88 .quad -4479729095110460046
	0xec, 0x8c, 0x3c, 0x67, 0x39, 0x3b, 0x63, 0xbc, //0x00004a90 .quad -4871985254153548564
	0xcf, 0xdc, 0xf2, 0x3c, 0xa7, 0x01, 0x4a, 0xf2, //0x00004a98 .quad -987975350460687153
	0x13, 0xd8, 0x85, 0xe0, 0x03, 0x05, 0xbe, 0xd5, //0x00004aa0 .quad -3044990783845967853
	0x01, 0xca, 0x17, 0x86, 0x08, 0x41, 0x6e, 0x97, //0x00004aa8 .quad -7535013621679011327
	0x18, 0x4e, 0xa7, 0xd8, 0x44, 0x86, 0x2d, 0x4b, //0x00004ab0 .quad 5417133557047315992
	0x82, 0xbc, 0x9d, 0xa7, 0x4a, 0xd1, 0x49, 0xbd, //0x00004ab8 .quad -4807081008671376254
	0x9e, 0x21, 0xd1, 0x0e, 0xd6, 0xe7, 0xf8, 0xdd, //0x00004ac0 .quad -2451955090545630818
	0xa2, 0x2b, 0x85, 0x51, 0x9d, 0x45, 0x9c, 0xec, //0x00004ac8 .quad -1397165242411832414
	0x03, 0xb5, 0x42, 0xc9, 0xe5, 0x90, 0xbb, 0xca, //0x00004ad0 .quad -3838314940804713213
	0x45, 0x3b, 0xf3, 0x52, 0x82, 0xab, 0xe1, 0x93, //0x00004ad8 .quad -7790757304148477115
	0x43, 0x62, 0x93, 0x3b, 0x1f, 0x75, 0x6a, 0x3d, //0x00004ae0 .quad 4425478360848884291
	0x17, 0x0a, 0xb0, 0xe7, 0x62, 0x16, 0xda, 0xb8, //0x00004ae8 .quad -5126760611758208489
	0xd4, 0x3a, 0x78, 0x0a, 0x67, 0x12, 0xc5, 0x0c, //0x00004af0 .quad 920161932633717460
	0x9d, 0x0c, 0x9c, 0xa1, 0xfb, 0x9b, 0x10, 0xe7, //0x00004af8 .quad -1796764746270372707
	0xc5, 0x24, 0x8b, 0x66, 0x80, 0x2b, 0xfb, 0x27, //0x00004b00 .quad 2880944217109767365
	0xe2, 0x87, 0x01, 0x45, 0x7d, 0x61, 0x6a, 0x90, //0x00004b08 .quad -8040506994060064798
	0xf6, 0xed, 0x2d, 0x80, 0x60, 0xf6, 0xf9, 0xb1, //0x00004b10 .quad -5622191765467566602
	0xda, 0xe9, 0x41, 0x96, 0xdc, 0xf9, 0x84, 0xb4, //0x00004b18 .quad -5438947724147693094
	0x73, 0x69, 0x39, 0xa0, 0xf8, 0x73, 0x78, 0x5e, //0x00004b20 .quad 6807318348447705459
	0x51, 0x64, 0xd2, 0xbb, 0x53, 0x38, 0xa6, 0xe1, //0x00004b28 .quad -2186998636757228463
	0xe8, 0xe1, 0x23, 0x64, 0x7b, 0x48, 0x0b, 0xdb, //0x00004b30 .quad -2662955059861265944
	0xb2, 0x7e, 0x63, 0x55, 0x34, 0xe3, 0x07, 0x8d, //0x00004b38 .quad -8284403175614349646
	0x62, 0xda, 0x2c, 0x3d, 0x9a, 0x1a, 0xce, 0x91, //0x00004b40 .quad -7940379843253970334
	0x5f, 0x5e, 0xbc, 0x6a, 0x01, 0xdc, 0x49, 0xb0, //0x00004b48 .quad -5743817951090549153
	0xfb, 0x10, 0x78, 0xcc, 0x40, 0xa1, 0x41, 0x76, //0x00004b50 .quad 8521269269642088699
	0xf7, 0x75, 0x6b, 0xc5, 0x01, 0x53, 0x5c, 0xdc, //0x00004b58 .quad -2568086420435798537
	0x9d, 0x0a, 0xcb, 0x7f, 0xc8, 0x04, 0xe9, 0xa9, //0x00004b60 .quad -6203421752542164323
	0xba, 0x29, 0x63, 0x1b, 0xe1, 0xb3, 0xb9, 0x89, //0x00004b68 .quad -8522583040413455942
	0x44, 0xcd, 0xbd, 0x9f, 0xfa, 0x45, 0x63, 0x54, //0x00004b70 .quad 6080780864604458308
	0x29, 0xf4, 0x3b, 0x62, 0xd9, 0x20, 0x28, 0xac, //0x00004b78 .quad -6041542782089432023
	0x95, 0x40, 0xad, 0x47, 0x79, 0x17, 0x7c, 0xa9, //0x00004b80 .quad -6234081974526590827
	0x33, 0xf1, 0xca, 0xba, 0x0f, 0x29, 0x32, 0xd7, //0x00004b88 .quad -2940242459184402125
	0x5d, 0x48, 0xcc, 0xcc, 0xab, 0x8e, 0xed, 0x49, //0x00004b90 .quad 5327070802775656541
	0xc0, 0xd6, 0xbe, 0xd4, 0xa9, 0x59, 0x7f, 0x86, //0x00004b98 .quad -8755180564631333184
	0x74, 0x5a, 0xff, 0xbf, 0x56, 0xf2, 0x68, 0x5c, //0x00004ba0 .quad 6658838503469570676
	0x70, 0x8c, 0xee, 0x49, 0x14, 0x30, 0x1f, 0xa8, //0x00004ba8 .quad -6332289687361778576
	0x11, 0x31, 0xff, 0x6f, 0xec, 0x2e, 0x83, 0x73, //0x00004bb0 .quad 8323548129336963345
	0x8c, 0x2f, 0x6a, 0x5c, 0x19, 0xfc, 0x26, 0xd2, //0x00004bb8 .quad -3303676090774835316
	0xab, 0x7e, 0xff, 0xc5, 0x53, 0xfd, 0x31, 0xc8, //0x00004bc0 .quad -4021154456019173717
	0xb7, 0x5d, 0xc2, 0xd9, 0x8f, 0x5d, 0x58, 0x83, //0x00004bc8 .quad -8982326584375353929
	0x55, 0x5e, 0x7f, 0xb7, 0xa8, 0x7c, 0x3e, 0xba, //0x00004bd0 .quad -5026443070023967147
	0x25, 0xf5, 0x32, 0xd0, 0xf3, 0x74, 0x2e, 0xa4, //0x00004bd8 .quad -6616222212041804507
	0xeb, 0x35, 0x5f, 0xe5, 0xd2, 0x1b, 0xce, 0x28, //0x00004be0 .quad 2940318199324816875
	0x6f, 0xb2, 0x3f, 0xc4, 0x30, 0x12, 0x3a, 0xcd, //0x00004be8 .quad -3658591746624867729
	0xb3, 0x81, 0x5b, 0xcf, 0x63, 0xd1, 0x80, 0x79, //0x00004bf0 .quad 8755227902219092403
	0x85, 0xcf, 0xa7, 0x7a, 0x5e, 0x4b, 0x44, 0x80, //0x00004bf8 .quad -9204148869281624187
	0x1f, 0x62, 0x32, 0xc3, 0xbc, 0x05, 0xe1, 0xd7, //0x00004c00 .quad -2891023177508298209
	0x66, 0xc3, 0x51, 0x19, 0x36, 0x5e, 0x55, 0xa0, //0x00004c08 .quad -6893500068174642330
	0xa7, 0xfa, 0xfe, 0xf3, 0x2b, 0x47, 0xd9, 0x8d, //0x00004c10 .quad -8225464990312760665
	0x40, 0x34, 0xa6, 0x9f, 0xc3, 0xb5, 0x6a, 0xc8, //0x00004c18 .quad -4005189066790915008
	0x51, 0xb9, 0xfe, 0xf0, 0xf6, 0x98, 0x4f, 0xb1, //0x00004c20 .quad -5670145219463562927
	0x50, 0xc1, 0x8f, 0x87, 0x34, 0x63, 0x85, 0xfa, //0x00004c28 .quad -394800315061255856
	0xd3, 0x33, 0x9f, 0x56, 0x9a, 0xbf, 0xd1, 0x6e, //0x00004c30 .quad 7985374283903742931
	0xd2, 0xd8, 0xb9, 0xd4, 0x00, 0x5e, 0x93, 0x9c, //0x00004c38 .quad -7164279224554366766
	0xc8, 0x00, 0x47, 0xec, 0x80, 0x2f, 0x86, 0x0a, //0x00004c40 .quad 758345818024902856
	0x07, 0x4f, 0xe8, 0x09, 0x81, 0x35, 0xb8, 0xc3, //0x00004c48 .quad -4343663012265570553
	0xfa, 0xc0, 0x58, 0x27, 0x61, 0xbb, 0x27, 0xcd, //0x00004c50 .quad -3663753745896259334
	0xc8, 0x62, 0x62, 0x4c, 0xe1, 0x42, 0xa6, 0xf4, //0x00004c58 .quad -817892746904575288
	0x9c, 0x78, 0x97, 0xb8, 0x1c, 0xd5, 0x38, 0x80, //0x00004c60 .quad -9207375118826243940
	0xbd, 0x7d, 0xbd, 0xcf, 0xcc, 0xe9, 0xe7, 0x98, //0x00004c68 .quad -7428711994456441411
	0xc3, 0x56, 0xbd, 0xe6, 0x63, 0x0a, 0x47, 0xe0, //0x00004c70 .quad -2285846861678029117
	0x2c, 0xdd, 0xac, 0x03, 0x40, 0xe4, 0x21, 0xbf, //0x00004c78 .quad -4674203974643163860
	0x74, 0xac, 0x6c, 0xe0, 0xfc, 0xcc, 0x58, 0x18, //0x00004c80 .quad 1754377441329851508
	0x78, 0x14, 0x98, 0x04, 0x50, 0x5d, 0xea, 0xee, //0x00004c88 .quad -1231068949876566920
	0xc8, 0xeb, 0x43, 0x0c, 0x1e, 0x80, 0x37, 0x0f, //0x00004c90 .quad 1096485900831157192
	0xcb, 0x0c, 0xdf, 0x02, 0x52, 0x7a, 0x52, 0x95, //0x00004c98 .quad -7686947121313936181
	0xba, 0xe6, 0x54, 0x8f, 0x25, 0x60, 0x05, 0xd3, //0x00004ca0 .quad -3241078642388441414
	0xfd, 0xcf, 0x96, 0x83, 0xe6, 0x18, 0xa7, 0xba, //0x00004ca8 .quad -4996997883215032323
	0x69, 0x20, 0x2a, 0xf3, 0x2e, 0xb8, 0xc6, 0x47, //0x00004cb0 .quad 5172023733869224041
	0xfd, 0x83, 0x7c, 0x24, 0x20, 0xdf, 0x50, 0xe9, //0x00004cb8 .quad -1634561335591402499
	0x41, 0x54, 0xfa, 0x57, 0x1d, 0x33, 0xdc, 0x4c, //0x00004cc0 .quad 5538357842881958977
	0x7e, 0xd2, 0xcd, 0x16, 0x74, 0x8b, 0xd2, 0x91, //0x00004cc8 .quad -7939129862385708418
	0x52, 0xe9, 0xf8, 0xad, 0xe4, 0x3f, 0x13, 0xe0, //0x00004cd0 .quad -2300424733252327086
	0x1d, 0x47, 0x81, 0x1c, 0x51, 0x2e, 0x47, 0xb6, //0x00004cd8 .quad -5312226309554747619
	0xa6, 0x23, 0x77, 0xd9, 0xdd, 0x0f, 0x18, 0x58, //0x00004ce0 .quad 6347841120289366950
	0xe5, 0x98, 0xa1, 0x63, 0xe5, 0xf9, 0xd8, 0xe3, //0x00004ce8 .quad -2028596868516046619
	0x48, 0x76, 0xea, 0xa7, 0xea, 0x09, 0x0f, 0x57, //0x00004cf0 .quad 6273243709394548296
	0x8f, 0xff, 0x44, 0x5e, 0x2f, 0x9c, 0x67, 0x8e, //0x00004cf8 .quad -8185402070463610993
	0xda, 0x13, 0xe5, 0x51, 0x65, 0xcc, 0xd2, 0x2c, //0x00004d00 .quad 3229868618315797466
	0x73, 0x3f, 0xd6, 0x35, 0x3b, 0x83, 0x01, 0xb2, //0x00004d08 .quad -5620066569652125837
	0xd1, 0x58, 0x5e, 0xa6, 0x7e, 0x7f, 0x07, 0xf8, //0x00004d10 .quad -574350245532641071
	0x4f, 0xcf, 0x4b, 0x03, 0x0a, 0xe4, 0x81, 0xde, //0x00004d18 .quad -2413397193637769393
	0x82, 0xf7, 0xfa, 0x27, 0xaf, 0xaf, 0x04, 0xfb, //0x00004d20 .quad -358968903457900670
	0x91, 0x61, 0x0f, 0x42, 0x86, 0x2e, 0x11, 0x8b, //0x00004d28 .quad -8425902273664687727
	0x63, 0xb5, 0xf9, 0xf1, 0x9a, 0xdb, 0xc5, 0x79, //0x00004d30 .quad 8774660907532399971
	0xf6, 0x39, 0x93, 0xd2, 0x27, 0x7a, 0xd5, 0xad, //0x00004d38 .quad -5920691823653471754
	0xbc, 0x22, 0x78, 0xae, 0x81, 0x52, 0x37, 0x18, //0x00004d40 .quad 1744954097560724156
	0x74, 0x08, 0x38, 0xc7, 0xb1, 0xd8, 0x4a, 0xd9, //0x00004d48 .quad -2789178761139451788
	0xb5, 0x15, 0x0b, 0x0d, 0x91, 0x93, 0x22, 0x8f, //0x00004d50 .quad -8132775725879323211
	0x48, 0x05, 0x83, 0x1c, 0x6f, 0xc7, 0xce, 0x87, //0x00004d58 .quad -8660765753353239224
	0x22, 0xdb, 0x4d, 0x50, 0x75, 0x38, 0xeb, 0xb2, //0x00004d60 .quad -5554283638921766110
	0x9a, 0xc6, 0xa3, 0xe3, 0x4a, 0x79, 0xc2, 0xa9, //0x00004d68 .quad -6214271173264161126
	0xeb, 0x51, 0x61, 0xa4, 0x92, 0x06, 0xa6, 0x5f, //0x00004d70 .quad 6892203506629956075
	0x41, 0xb8, 0x8c, 0x9c, 0x9d, 0x17, 0x33, 0xd4, //0x00004d78 .quad -3156152948152813503
	0x33, 0xd3, 0xbc, 0xa6, 0x1b, 0xc4, 0xc7, 0xdb, //0x00004d80 .quad -2609901835997359309
	0x28, 0xf3, 0xd7, 0x81, 0xc2, 0xee, 0x9f, 0x84, //0x00004d88 .quad -8890124620236590296
	0x00, 0x08, 0x6c, 0x90, 0x22, 0xb5, 0xb9, 0x12, //0x00004d90 .quad 1349308723430688768
	0xf3, 0xef, 0x4d, 0x22, 0x73, 0xea, 0xc7, 0xa5, //0x00004d98 .quad -6500969756868349965
	0x00, 0x0a, 0x87, 0x34, 0x6b, 0x22, 0x68, 0xd7, //0x00004da0 .quad -2925050114139026944
	0xef, 0x6b, 0xe1, 0xea, 0x0f, 0xe5, 0x39, 0xcf, //0x00004da8 .quad -3514526177658049553
	0x40, 0x66, 0xd4, 0x00, 0x83, 0x15, 0xa1, 0xe6, //0x00004db0 .quad -1828156321336891840
	0x75, 0xe3, 0xcc, 0xf2, 0x29, 0x2f, 0x84, 0x81, //0x00004db8 .quad -9114107888677362827
	0xd0, 0x7f, 0x09, 0xc1, 0xe3, 0x5a, 0x49, 0x60, //0x00004dc0 .quad 6938176635183661008
	0x53, 0x1c, 0x80, 0x6f, 0xf4, 0x3a, 0xe5, 0xa1, //0x00004dc8 .quad -6780948842419315629
	0xc4, 0xdf, 0x4b, 0xb1, 0x9c, 0xb1, 0x5b, 0x38, //0x00004dd0 .quad 4061034775552188356
	0x68, 0x23, 0x60, 0x8b, 0xb1, 0x89, 0x5e, 0xca, //0x00004dd8 .quad -3864500034596756632
	0xb5, 0xd7, 0x9e, 0xdd, 0x03, 0x9e, 0x72, 0x46, //0x00004de0 .quad 5076293469440235445
	0x42, 0x2c, 0x38, 0xee, 0x1d, 0x2c, 0xf6, 0xfc, //0x00004de8 .quad -218939024818557886
	0xd1, 0x46, 0x83, 0x6a, 0xc2, 0xa2, 0x07, 0x6c, //0x00004df0 .quad 7784369436827535057
	0xa9, 0x1b, 0xe3, 0xb4, 0x92, 0xdb, 0x19, 0x9e, //0x00004df8 .quad -7054365918152680535
	0x85, 0x18, 0x24, 0x05, 0x73, 0x8b, 0x09, 0xc7, //0x00004e00 .quad -4104596259247744891
	0x93, 0xe2, 0x1b, 0x62, 0x77, 0x52, 0xa0, 0xc5, //0x00004e08 .quad -4206271379263462765
	0xa7, 0x1e, 0x6d, 0xc6, 0x4f, 0xee, 0xcb, 0xb8, //0x00004e10 .quad -5130745324059681113
	0x38, 0xdb, 0xa2, 0x3a, 0x15, 0x67, 0x08, 0xf7, //0x00004e18 .quad -646153205651940552
	0x28, 0x33, 0x04, 0xdc, 0xf1, 0x74, 0x7f, 0x73, //0x00004e20 .quad 8322499218531169064
	0x03, 0xc9, 0xa5, 0x44, 0x6d, 0x40, 0x65, 0x9a, //0x00004e28 .quad -7321374781173544701
	0xf2, 0x3f, 0x05, 0x53, 0x2e, 0x52, 0x5f, 0x50, //0x00004e30 .quad 5791438004736573426
	0x44, 0x3b, 0xcf, 0x95, 0x88, 0x90, 0xfe, 0xc0, //0x00004e38 .quad -4540032458039542972
	0xef, 0x8f, 0xc6, 0xe7, 0xb9, 0x26, 0x77, 0x64, //0x00004e40 .quad 7239297505920716783
	0x15, 0x0a, 0x43, 0xbb, 0xaa, 0x34, 0x3e, 0xf1, //0x00004e48 .quad -1063354554122040811
	0xf5, 0x19, 0xdc, 0x30, 0x34, 0x78, 0xca, 0x5e, //0x00004e50 .quad 6830403950414141941
	0x4d, 0xe6, 0x09, 0xb5, 0xea, 0xe0, 0xc6, 0x96, //0x00004e58 .quad -7582125623967357363
	0x72, 0x20, 0x13, 0x3d, 0x41, 0x16, 0x7d, 0xb6, //0x00004e60 .quad -5297053117264486286
	0xe0, 0x5f, 0x4c, 0x62, 0x25, 0x99, 0x78, 0xbc, //0x00004e68 .quad -4865971011531808800
	0x8f, 0xe8, 0x57, 0x8c, 0xd1, 0x5b, 0x1c, 0xe4, //0x00004e70 .quad -2009630378153219953
	0xd8, 0x77, 0xdf, 0xba, 0x6e, 0xbf, 0x96, 0xeb, //0x00004e78 .quad -1470777745987373096
	0x59, 0xf1, 0xb6, 0xf7, 0x62, 0xb9, 0x91, 0x8e, //0x00004e80 .quad -8173548013986844327
	0xe7, 0xaa, 0xcb, 0x34, 0xa5, 0x37, 0x3e, 0x93, //0x00004e88 .quad -7836765118883190041
	0xb0, 0xad, 0xa4, 0xb5, 0xbb, 0x27, 0x36, 0x72, //0x00004e90 .quad 8229809056225996208
	0xa1, 0x95, 0xfe, 0x81, 0x8e, 0xc5, 0x0d, 0xb8, //0x00004e98 .quad -5184270380176599647
	0x1c, 0xd9, 0x0d, 0xa3, 0xaa, 0xb1, 0xc3, 0xce, //0x00004ea0 .quad -3547796734999668452
	0x09, 0x3b, 0x7e, 0x22, 0xf2, 0x36, 0x11, 0xe6, //0x00004ea8 .quad -1868651956793361655
	0xb1, 0xa7, 0xe8, 0xa5, 0x0a, 0x4f, 0x3a, 0x21, //0x00004eb0 .quad 2394313059052595121
	0xe6, 0xe4, 0x8e, 0x55, 0x57, 0xc2, 0xca, 0x8f, //0x00004eb8 .quad -8085436500636932890
	0x9d, 0xd1, 0x62, 0x4f, 0xcd, 0xe2, 0x88, 0xa9, //0x00004ec0 .quad -6230480713039031907
	0x1f, 0x9e, 0xf2, 0x2a, 0xed, 0x72, 0xbd, 0xb3, //0x00004ec8 .quad -5495109607368778209
	0x05, 0x86, 0x3b, 0xa3, 0x80, 0x1b, 0xeb, 0x93, //0x00004ed0 .quad -7788100891298789883
	0xa7, 0x45, 0xaf, 0x75, 0xa8, 0xcf, 0xac, 0xe0, //0x00004ed8 .quad -2257200990783584857
	0xc3, 0x33, 0x05, 0x66, 0x30, 0xf1, 0x72, 0xbc, //0x00004ee0 .quad -4867563057061743677
	0x88, 0x8b, 0x8d, 0x49, 0xc9, 0x01, 0x6c, 0x8c, //0x00004ee8 .quad -8328279646880822392
	0xb4, 0x80, 0x86, 0x7f, 0x7c, 0xad, 0x8f, 0xeb, //0x00004ef0 .quad -1472767802899791692
	0x6a, 0xee, 0xf0, 0x9b, 0x3b, 0x02, 0x87, 0xaf, //0x00004ef8 .quad -5798663540173640086
	0xe1, 0x20, 0x68, 0x9f, 0xdb, 0x98, 0x73, 0xa6, //0x00004f00 .quad -6452645772052127519
	0x05, 0x2a, 0xed, 0x82, 0xca, 0xc2, 0x68, 0xdb, //0x00004f08 .quad -2636643406789662203
	0x8c, 0x14, 0xa1, 0x43, 0x89, 0x3f, 0x08, 0x88, //0x00004f10 .quad -8644589625959967604
	0x43, 0x3a, 0xd4, 0x91, 0xbe, 0x79, 0x21, 0x89, //0x00004f18 .quad -8565431156884620733
	0xb0, 0x59, 0x89, 0x94, 0x6b, 0x4f, 0x0a, 0x6a, //0x00004f20 .quad 7641007041259592112
	0xd4, 0x48, 0x49, 0x36, 0x2e, 0xd8, 0x69, 0xab, //0x00004f28 .quad -6095102927678388012
	0x1c, 0xb0, 0xab, 0x79, 0x46, 0xe3, 0x8c, 0x84, //0x00004f30 .quad -8895485272135061476
	0x09, 0x9b, 0xdb, 0xc3, 0x39, 0x4e, 0x44, 0xd6, //0x00004f38 .quad -3007192641170597111
	0x11, 0x4e, 0x0b, 0x0c, 0x0c, 0x0e, 0xd8, 0xf2, //0x00004f40 .quad -947992276657025519
	0xe5, 0x40, 0x69, 0x1a, 0xe4, 0xb0, 0xea, 0x85, //0x00004f48 .quad -8797024428372705051
	0x95, 0x21, 0x0e, 0x0f, 0x8f, 0x11, 0x8e, 0x6f, //0x00004f50 .quad 8038381691033493909
	0x1f, 0x91, 0x03, 0x21, 0x1d, 0x5d, 0x65, 0xa7, //0x00004f58 .quad -6384594517038493409
	0xfb, 0xa9, 0xd1, 0xd2, 0xf2, 0x95, 0x71, 0x4b, //0x00004f60 .quad 5436291095364479483
	0x67, 0x75, 0x44, 0x69, 0x64, 0xb4, 0x3e, 0xd1, //0x00004f68 .quad -3369057127870728857
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004f70 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	//0x00004f80 .p2align 4, 0x00
	//0x00004f80 _POW_TAB
	0x01, 0x00, 0x00, 0x00, //0x00004f80 .long 1
	0x03, 0x00, 0x00, 0x00, //0x00004f84 .long 3
	0x06, 0x00, 0x00, 0x00, //0x00004f88 .long 6
	0x09, 0x00, 0x00, 0x00, //0x00004f8c .long 9
	0x0d, 0x00, 0x00, 0x00, //0x00004f90 .long 13
	0x10, 0x00, 0x00, 0x00, //0x00004f94 .long 16
	0x13, 0x00, 0x00, 0x00, //0x00004f98 .long 19
	0x17, 0x00, 0x00, 0x00, //0x00004f9c .long 23
	0x1a, 0x00, 0x00, 0x00, //0x00004fa0 .long 26
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004fa4 .p2align 4, 0x00
	//0x00004fb0 _LSHIFT_TAB
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004fb0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004fc0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004fd0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004fe0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004ff0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005000 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005010 QUAD $0x0000000000000000  // .space 8, '\x00\x00\x00\x00\x00\x00\x00\x00'
	0x01, 0x00, 0x00, 0x00, //0x00005018 .long 1
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000501c QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000502c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000503c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000504c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000505c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000506c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000507c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x01, 0x00, 0x00, 0x00, //0x00005080 .long 1
	0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005084 QUAD $0x0000000000003532; QUAD $0x0000000000000000  // .asciz 16, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005094 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000050a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000050b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000050c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000050d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000050e4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x01, 0x00, 0x00, 0x00, //0x000050e8 .long 1
	0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000050ec QUAD $0x0000000000353231; QUAD $0x0000000000000000  // .asciz 16, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000050fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000510c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000511c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000512c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000513c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000514c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x02, 0x00, 0x00, 0x00, //0x00005150 .long 2
	0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005154 QUAD $0x0000000000353236; QUAD $0x0000000000000000  // .asciz 16, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005164 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005174 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005184 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005194 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000051a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000051b4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x02, 0x00, 0x00, 0x00, //0x000051b8 .long 2
	0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000051bc QUAD $0x0000000035323133; QUAD $0x0000000000000000  // .asciz 16, '3125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000051cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000051dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000051ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000051fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000520c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000521c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x02, 0x00, 0x00, 0x00, //0x00005220 .long 2
	0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005224 QUAD $0x0000003532363531; QUAD $0x0000000000000000  // .asciz 16, '15625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005234 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005244 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005254 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005264 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005274 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005284 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x03, 0x00, 0x00, 0x00, //0x00005288 .long 3
	0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000528c QUAD $0x0000003532313837; QUAD $0x0000000000000000  // .asciz 16, '78125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000529c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000052ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000052bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000052cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000052dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000052ec LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x03, 0x00, 0x00, 0x00, //0x000052f0 .long 3
	0x33, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000052f4 QUAD $0x0000353236303933; QUAD $0x0000000000000000  // .asciz 16, '390625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005304 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005314 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005324 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005334 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005344 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005354 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x03, 0x00, 0x00, 0x00, //0x00005358 .long 3
	0x31, 0x39, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000535c QUAD $0x0035323133353931; QUAD $0x0000000000000000  // .asciz 16, '1953125\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000536c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000537c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000538c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000539c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000053ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000053bc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x000053c0 .long 4
	0x39, 0x37, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000053c4 QUAD $0x0035323635363739; QUAD $0x0000000000000000  // .asciz 16, '9765625\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000053d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000053e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000053f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005404 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005414 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005424 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x00005428 .long 4
	0x34, 0x38, 0x38, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000542c QUAD $0x3532313832383834; QUAD $0x0000000000000000  // .asciz 16, '48828125\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000543c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000544c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000545c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000546c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000547c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000548c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x00005490 .long 4
	0x32, 0x34, 0x34, 0x31, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005494 QUAD $0x3236303431343432; QUAD $0x0000000000000035  // .asciz 16, '244140625\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000054a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000054b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000054c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000054d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000054e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000054f4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x000054f8 .long 4
	0x31, 0x32, 0x32, 0x30, 0x37, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000054fc QUAD $0x3133303730323231; QUAD $0x0000000000003532  // .asciz 16, '1220703125\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000550c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000551c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000552c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000553c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000554c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000555c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x05, 0x00, 0x00, 0x00, //0x00005560 .long 5
	0x36, 0x31, 0x30, 0x33, 0x35, 0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005564 QUAD $0x3635313533303136; QUAD $0x0000000000003532  // .asciz 16, '6103515625\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005574 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005584 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005594 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000055a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000055b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000055c4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x05, 0x00, 0x00, 0x00, //0x000055c8 .long 5
	0x33, 0x30, 0x35, 0x31, 0x37, 0x35, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000055cc QUAD $0x3837353731353033; QUAD $0x0000000000353231  // .asciz 16, '30517578125\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000055dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000055ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000055fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000560c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000561c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000562c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x05, 0x00, 0x00, 0x00, //0x00005630 .long 5
	0x31, 0x35, 0x32, 0x35, 0x38, 0x37, 0x38, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x00005634 QUAD $0x3938373835323531; QUAD $0x0000000035323630  // .asciz 16, '152587890625\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005644 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005654 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005664 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005674 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005684 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005694 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x06, 0x00, 0x00, 0x00, //0x00005698 .long 6
	0x37, 0x36, 0x32, 0x39, 0x33, 0x39, 0x34, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x0000569c QUAD $0x3534393339323637; QUAD $0x0000000035323133  // .asciz 16, '762939453125\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000056ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000056bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000056cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000056dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000056ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000056fc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x06, 0x00, 0x00, 0x00, //0x00005700 .long 6
	0x33, 0x38, 0x31, 0x34, 0x36, 0x39, 0x37, 0x32, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, //0x00005704 QUAD $0x3237393634313833; QUAD $0x0000003532363536  // .asciz 16, '3814697265625\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005714 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005724 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005734 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005744 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005754 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005764 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x06, 0x00, 0x00, 0x00, //0x00005768 .long 6
	0x31, 0x39, 0x30, 0x37, 0x33, 0x34, 0x38, 0x36, 0x33, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, //0x0000576c QUAD $0x3638343337303931; QUAD $0x0000353231383233  // .asciz 16, '19073486328125\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000577c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000578c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000579c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000057ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000057bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000057cc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x000057d0 .long 7
	0x39, 0x35, 0x33, 0x36, 0x37, 0x34, 0x33, 0x31, 0x36, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, //0x000057d4 QUAD $0x3133343736333539; QUAD $0x0000353236303436  // .asciz 16, '95367431640625\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000057e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000057f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005804 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005814 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005824 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005834 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x00005838 .long 7
	0x34, 0x37, 0x36, 0x38, 0x33, 0x37, 0x31, 0x35, 0x38, 0x32, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, //0x0000583c QUAD $0x3531373338363734; QUAD $0x0035323133303238  // .asciz 16, '476837158203125\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000584c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000585c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000586c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000587c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000588c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000589c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x000058a0 .long 7
	0x32, 0x33, 0x38, 0x34, 0x31, 0x38, 0x35, 0x37, 0x39, 0x31, 0x30, 0x31, 0x35, 0x36, 0x32, 0x35, //0x000058a4 QUAD $0x3735383134383332; QUAD $0x3532363531303139  // .asciz 16, '2384185791015625'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000058b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000058c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000058d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000058e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000058f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005904 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x00005908 .long 7
	0x31, 0x31, 0x39, 0x32, 0x30, 0x39, 0x32, 0x38, 0x39, 0x35, 0x35, 0x30, 0x37, 0x38, 0x31, 0x32, //0x0000590c QUAD $0x3832393032393131; QUAD $0x3231383730353539  // .asciz 16, '1192092895507812'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000591c QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000592c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000593c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000594c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000595c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000596c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x08, 0x00, 0x00, 0x00, //0x00005970 .long 8
	0x35, 0x39, 0x36, 0x30, 0x34, 0x36, 0x34, 0x34, 0x37, 0x37, 0x35, 0x33, 0x39, 0x30, 0x36, 0x32, //0x00005974 QUAD $0x3434363430363935; QUAD $0x3236303933353737  // .asciz 16, '5960464477539062'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005984 QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005994 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000059a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000059b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000059c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000059d4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x08, 0x00, 0x00, 0x00, //0x000059d8 .long 8
	0x32, 0x39, 0x38, 0x30, 0x32, 0x33, 0x32, 0x32, 0x33, 0x38, 0x37, 0x36, 0x39, 0x35, 0x33, 0x31, //0x000059dc QUAD $0x3232333230383932; QUAD $0x3133353936373833  // .asciz 16, '2980232238769531'
	0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000059ec QUAD $0x0000000000003532; QUAD $0x0000000000000000  // .asciz 16, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000059fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005a0c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005a1c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005a2c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005a3c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x08, 0x00, 0x00, 0x00, //0x00005a40 .long 8
	0x31, 0x34, 0x39, 0x30, 0x31, 0x31, 0x36, 0x31, 0x31, 0x39, 0x33, 0x38, 0x34, 0x37, 0x36, 0x35, //0x00005a44 QUAD $0x3136313130393431; QUAD $0x3536373438333931  // .asciz 16, '1490116119384765'
	0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005a54 QUAD $0x0000000000353236; QUAD $0x0000000000000000  // .asciz 16, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005a64 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005a74 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005a84 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005a94 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005aa4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x09, 0x00, 0x00, 0x00, //0x00005aa8 .long 9
	0x37, 0x34, 0x35, 0x30, 0x35, 0x38, 0x30, 0x35, 0x39, 0x36, 0x39, 0x32, 0x33, 0x38, 0x32, 0x38, //0x00005aac QUAD $0x3530383530353437; QUAD $0x3832383332393639  // .asciz 16, '7450580596923828'
	0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005abc QUAD $0x0000000000353231; QUAD $0x0000000000000000  // .asciz 16, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005acc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005adc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005aec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005afc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005b0c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x09, 0x00, 0x00, 0x00, //0x00005b10 .long 9
	0x33, 0x37, 0x32, 0x35, 0x32, 0x39, 0x30, 0x32, 0x39, 0x38, 0x34, 0x36, 0x31, 0x39, 0x31, 0x34, //0x00005b14 QUAD $0x3230393235323733; QUAD $0x3431393136343839  // .asciz 16, '3725290298461914'
	0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005b24 QUAD $0x0000000035323630; QUAD $0x0000000000000000  // .asciz 16, '0625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005b34 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005b44 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005b54 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005b64 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005b74 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x09, 0x00, 0x00, 0x00, //0x00005b78 .long 9
	0x31, 0x38, 0x36, 0x32, 0x36, 0x34, 0x35, 0x31, 0x34, 0x39, 0x32, 0x33, 0x30, 0x39, 0x35, 0x37, //0x00005b7c QUAD $0x3135343632363831; QUAD $0x3735393033323934  // .asciz 16, '1862645149230957'
	0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005b8c QUAD $0x0000003532313330; QUAD $0x0000000000000000  // .asciz 16, '03125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005b9c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005bac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005bbc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005bcc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005bdc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00005be0 .long 10
	0x39, 0x33, 0x31, 0x33, 0x32, 0x32, 0x35, 0x37, 0x34, 0x36, 0x31, 0x35, 0x34, 0x37, 0x38, 0x35, //0x00005be4 QUAD $0x3735323233313339; QUAD $0x3538373435313634  // .asciz 16, '9313225746154785'
	0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005bf4 QUAD $0x0000003532363531; QUAD $0x0000000000000000  // .asciz 16, '15625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005c04 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005c14 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005c24 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005c34 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005c44 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00005c48 .long 10
	0x34, 0x36, 0x35, 0x36, 0x36, 0x31, 0x32, 0x38, 0x37, 0x33, 0x30, 0x37, 0x37, 0x33, 0x39, 0x32, //0x00005c4c QUAD $0x3832313636353634; QUAD $0x3239333737303337  // .asciz 16, '4656612873077392'
	0x35, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005c5c QUAD $0x0000353231383735; QUAD $0x0000000000000000  // .asciz 16, '578125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005c6c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005c7c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005c8c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005c9c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005cac LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00005cb0 .long 10
	0x32, 0x33, 0x32, 0x38, 0x33, 0x30, 0x36, 0x34, 0x33, 0x36, 0x35, 0x33, 0x38, 0x36, 0x39, 0x36, //0x00005cb4 QUAD $0x3436303338323332; QUAD $0x3639363833353633  // .asciz 16, '2328306436538696'
	0x32, 0x38, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005cc4 QUAD $0x0035323630393832; QUAD $0x0000000000000000  // .asciz 16, '2890625\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005cd4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005ce4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005cf4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005d04 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005d14 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00005d18 .long 10
	0x31, 0x31, 0x36, 0x34, 0x31, 0x35, 0x33, 0x32, 0x31, 0x38, 0x32, 0x36, 0x39, 0x33, 0x34, 0x38, //0x00005d1c QUAD $0x3233353134363131; QUAD $0x3834333936323831  // .asciz 16, '1164153218269348'
	0x31, 0x34, 0x34, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005d2c QUAD $0x3532313335343431; QUAD $0x0000000000000000  // .asciz 16, '14453125\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005d3c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005d4c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005d5c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005d6c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005d7c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0b, 0x00, 0x00, 0x00, //0x00005d80 .long 11
	0x35, 0x38, 0x32, 0x30, 0x37, 0x36, 0x36, 0x30, 0x39, 0x31, 0x33, 0x34, 0x36, 0x37, 0x34, 0x30, //0x00005d84 QUAD $0x3036363730323835; QUAD $0x3034373634333139  // .asciz 16, '5820766091346740'
	0x37, 0x32, 0x32, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005d94 QUAD $0x3532363536323237; QUAD $0x0000000000000000  // .asciz 16, '72265625\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005da4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005db4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005dc4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005dd4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005de4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0b, 0x00, 0x00, 0x00, //0x00005de8 .long 11
	0x32, 0x39, 0x31, 0x30, 0x33, 0x38, 0x33, 0x30, 0x34, 0x35, 0x36, 0x37, 0x33, 0x33, 0x37, 0x30, //0x00005dec QUAD $0x3033383330313932; QUAD $0x3037333337363534  // .asciz 16, '2910383045673370'
	0x33, 0x36, 0x31, 0x33, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005dfc QUAD $0x3231383233313633; QUAD $0x0000000000000035  // .asciz 16, '361328125\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005e0c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005e1c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005e2c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005e3c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005e4c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0b, 0x00, 0x00, 0x00, //0x00005e50 .long 11
	0x31, 0x34, 0x35, 0x35, 0x31, 0x39, 0x31, 0x35, 0x32, 0x32, 0x38, 0x33, 0x36, 0x36, 0x38, 0x35, //0x00005e54 QUAD $0x3531393135353431; QUAD $0x3538363633383232  // .asciz 16, '1455191522836685'
	0x31, 0x38, 0x30, 0x36, 0x36, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005e64 QUAD $0x3630343636303831; QUAD $0x0000000000003532  // .asciz 16, '1806640625\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005e74 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005e84 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005e94 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005ea4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005eb4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0c, 0x00, 0x00, 0x00, //0x00005eb8 .long 12
	0x37, 0x32, 0x37, 0x35, 0x39, 0x35, 0x37, 0x36, 0x31, 0x34, 0x31, 0x38, 0x33, 0x34, 0x32, 0x35, //0x00005ebc QUAD $0x3637353935373237; QUAD $0x3532343338313431  // .asciz 16, '7275957614183425'
	0x39, 0x30, 0x33, 0x33, 0x32, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005ecc QUAD $0x3133303233333039; QUAD $0x0000000000003532  // .asciz 16, '9033203125\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005edc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005eec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005efc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005f0c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005f1c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0c, 0x00, 0x00, 0x00, //0x00005f20 .long 12
	0x33, 0x36, 0x33, 0x37, 0x39, 0x37, 0x38, 0x38, 0x30, 0x37, 0x30, 0x39, 0x31, 0x37, 0x31, 0x32, //0x00005f24 QUAD $0x3838373937333633; QUAD $0x3231373139303730  // .asciz 16, '3637978807091712'
	0x39, 0x35, 0x31, 0x36, 0x36, 0x30, 0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005f34 QUAD $0x3531303636313539; QUAD $0x0000000000353236  // .asciz 16, '95166015625\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005f44 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005f54 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005f64 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005f74 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005f84 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0c, 0x00, 0x00, 0x00, //0x00005f88 .long 12
	0x31, 0x38, 0x31, 0x38, 0x39, 0x38, 0x39, 0x34, 0x30, 0x33, 0x35, 0x34, 0x35, 0x38, 0x35, 0x36, //0x00005f8c QUAD $0x3439383938313831; QUAD $0x3635383534353330  // .asciz 16, '1818989403545856'
	0x34, 0x37, 0x35, 0x38, 0x33, 0x30, 0x30, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x00005f9c QUAD $0x3730303338353734; QUAD $0x0000000035323138  // .asciz 16, '475830078125\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005fac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005fbc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005fcc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005fdc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005fec LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x00005ff0 .long 13
	0x39, 0x30, 0x39, 0x34, 0x39, 0x34, 0x37, 0x30, 0x31, 0x37, 0x37, 0x32, 0x39, 0x32, 0x38, 0x32, //0x00005ff4 QUAD $0x3037343934393039; QUAD $0x3238323932373731  // .asciz 16, '9094947017729282'
	0x33, 0x37, 0x39, 0x31, 0x35, 0x30, 0x33, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x00006004 QUAD $0x3933303531393733; QUAD $0x0000000035323630  // .asciz 16, '379150390625\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006014 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006024 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006034 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006044 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006054 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x00006058 .long 13
	0x34, 0x35, 0x34, 0x37, 0x34, 0x37, 0x33, 0x35, 0x30, 0x38, 0x38, 0x36, 0x34, 0x36, 0x34, 0x31, //0x0000605c QUAD $0x3533373437343534; QUAD $0x3134363436383830  // .asciz 16, '4547473508864641'
	0x31, 0x38, 0x39, 0x35, 0x37, 0x35, 0x31, 0x39, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, //0x0000606c QUAD $0x3931353735393831; QUAD $0x0000003532313335  // .asciz 16, '1895751953125\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000607c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000608c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000609c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000060ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000060bc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x000060c0 .long 13
	0x32, 0x32, 0x37, 0x33, 0x37, 0x33, 0x36, 0x37, 0x35, 0x34, 0x34, 0x33, 0x32, 0x33, 0x32, 0x30, //0x000060c4 QUAD $0x3736333733373232; QUAD $0x3032333233343435  // .asciz 16, '2273736754432320'
	0x35, 0x39, 0x34, 0x37, 0x38, 0x37, 0x35, 0x39, 0x37, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, //0x000060d4 QUAD $0x3935373837343935; QUAD $0x0000353236353637  // .asciz 16, '59478759765625\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000060e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000060f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006104 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006114 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006124 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x00006128 .long 13
	0x31, 0x31, 0x33, 0x36, 0x38, 0x36, 0x38, 0x33, 0x37, 0x37, 0x32, 0x31, 0x36, 0x31, 0x36, 0x30, //0x0000612c QUAD $0x3338363836333131; QUAD $0x3036313631323737  // .asciz 16, '1136868377216160'
	0x32, 0x39, 0x37, 0x33, 0x39, 0x33, 0x37, 0x39, 0x38, 0x38, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, //0x0000613c QUAD $0x3937333933373932; QUAD $0x0035323138323838  // .asciz 16, '297393798828125\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000614c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000615c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000616c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000617c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000618c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0e, 0x00, 0x00, 0x00, //0x00006190 .long 14
	0x35, 0x36, 0x38, 0x34, 0x33, 0x34, 0x31, 0x38, 0x38, 0x36, 0x30, 0x38, 0x30, 0x38, 0x30, 0x31, //0x00006194 QUAD $0x3831343334383635; QUAD $0x3130383038303638  // .asciz 16, '5684341886080801'
	0x34, 0x38, 0x36, 0x39, 0x36, 0x38, 0x39, 0x39, 0x34, 0x31, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, //0x000061a4 QUAD $0x3939383639363834; QUAD $0x0035323630343134  // .asciz 16, '486968994140625\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000061f4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0e, 0x00, 0x00, 0x00, //0x000061f8 .long 14
	0x32, 0x38, 0x34, 0x32, 0x31, 0x37, 0x30, 0x39, 0x34, 0x33, 0x30, 0x34, 0x30, 0x34, 0x30, 0x30, //0x000061fc QUAD $0x3930373132343832; QUAD $0x3030343034303334  // .asciz 16, '2842170943040400'
	0x37, 0x34, 0x33, 0x34, 0x38, 0x34, 0x34, 0x39, 0x37, 0x30, 0x37, 0x30, 0x33, 0x31, 0x32, 0x35, //0x0000620c QUAD $0x3934343834333437; QUAD $0x3532313330373037  // .asciz 16, '7434844970703125'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000621c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000622c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000623c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000624c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000625c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0e, 0x00, 0x00, 0x00, //0x00006260 .long 14
	0x31, 0x34, 0x32, 0x31, 0x30, 0x38, 0x35, 0x34, 0x37, 0x31, 0x35, 0x32, 0x30, 0x32, 0x30, 0x30, //0x00006264 QUAD $0x3435383031323431; QUAD $0x3030323032353137  // .asciz 16, '1421085471520200'
	0x33, 0x37, 0x31, 0x37, 0x34, 0x32, 0x32, 0x34, 0x38, 0x35, 0x33, 0x35, 0x31, 0x35, 0x36, 0x32, //0x00006274 QUAD $0x3432323437313733; QUAD $0x3236353135333538  // .asciz 16, '3717422485351562'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006284 QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006294 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000062c4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0f, 0x00, 0x00, 0x00, //0x000062c8 .long 15
	0x37, 0x31, 0x30, 0x35, 0x34, 0x32, 0x37, 0x33, 0x35, 0x37, 0x36, 0x30, 0x31, 0x30, 0x30, 0x31, //0x000062cc QUAD $0x3337323435303137; QUAD $0x3130303130363735  // .asciz 16, '7105427357601001'
	0x38, 0x35, 0x38, 0x37, 0x31, 0x31, 0x32, 0x34, 0x32, 0x36, 0x37, 0x35, 0x37, 0x38, 0x31, 0x32, //0x000062dc QUAD $0x3432313137383538; QUAD $0x3231383735373632  // .asciz 16, '8587112426757812'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062ec QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000630c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000631c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000632c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0f, 0x00, 0x00, 0x00, //0x00006330 .long 15
	0x33, 0x35, 0x35, 0x32, 0x37, 0x31, 0x33, 0x36, 0x37, 0x38, 0x38, 0x30, 0x30, 0x35, 0x30, 0x30, //0x00006334 QUAD $0x3633313732353533; QUAD $0x3030353030383837  // .asciz 16, '3552713678800500'
	0x39, 0x32, 0x39, 0x33, 0x35, 0x35, 0x36, 0x32, 0x31, 0x33, 0x33, 0x37, 0x38, 0x39, 0x30, 0x36, //0x00006344 QUAD $0x3236353533393239; QUAD $0x3630393837333331  // .asciz 16, '9293556213378906'
	0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006354 QUAD $0x0000000000003532; QUAD $0x0000000000000000  // .asciz 16, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006364 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006374 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006384 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006394 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0f, 0x00, 0x00, 0x00, //0x00006398 .long 15
	0x31, 0x37, 0x37, 0x36, 0x33, 0x35, 0x36, 0x38, 0x33, 0x39, 0x34, 0x30, 0x30, 0x32, 0x35, 0x30, //0x0000639c QUAD $0x3836353336373731; QUAD $0x3035323030343933  // .asciz 16, '1776356839400250'
	0x34, 0x36, 0x34, 0x36, 0x37, 0x37, 0x38, 0x31, 0x30, 0x36, 0x36, 0x38, 0x39, 0x34, 0x35, 0x33, //0x000063ac QUAD $0x3138373736343634; QUAD $0x3335343938363630  // .asciz 16, '4646778106689453'
	0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063bc QUAD $0x0000000000353231; QUAD $0x0000000000000000  // .asciz 16, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000063fc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x00006400 .long 16
	0x38, 0x38, 0x38, 0x31, 0x37, 0x38, 0x34, 0x31, 0x39, 0x37, 0x30, 0x30, 0x31, 0x32, 0x35, 0x32, //0x00006404 QUAD $0x3134383731383838; QUAD $0x3235323130303739  // .asciz 16, '8881784197001252'
	0x33, 0x32, 0x33, 0x33, 0x38, 0x39, 0x30, 0x35, 0x33, 0x33, 0x34, 0x34, 0x37, 0x32, 0x36, 0x35, //0x00006414 QUAD $0x3530393833333233; QUAD $0x3536323734343333  // .asciz 16, '3233890533447265'
	0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006424 QUAD $0x0000000000353236; QUAD $0x0000000000000000  // .asciz 16, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006434 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006444 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006454 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006464 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x00006468 .long 16
	0x34, 0x34, 0x34, 0x30, 0x38, 0x39, 0x32, 0x30, 0x39, 0x38, 0x35, 0x30, 0x30, 0x36, 0x32, 0x36, //0x0000646c QUAD $0x3032393830343434; QUAD $0x3632363030353839  // .asciz 16, '4440892098500626'
	0x31, 0x36, 0x31, 0x36, 0x39, 0x34, 0x35, 0x32, 0x36, 0x36, 0x37, 0x32, 0x33, 0x36, 0x33, 0x32, //0x0000647c QUAD $0x3235343936313631; QUAD $0x3233363332373636  // .asciz 16, '1616945266723632'
	0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000648c QUAD $0x0000000035323138; QUAD $0x0000000000000000  // .asciz 16, '8125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000649c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000064cc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x000064d0 .long 16
	0x32, 0x32, 0x32, 0x30, 0x34, 0x34, 0x36, 0x30, 0x34, 0x39, 0x32, 0x35, 0x30, 0x33, 0x31, 0x33, //0x000064d4 QUAD $0x3036343430323232; QUAD $0x3331333035323934  // .asciz 16, '2220446049250313'
	0x30, 0x38, 0x30, 0x38, 0x34, 0x37, 0x32, 0x36, 0x33, 0x33, 0x33, 0x36, 0x31, 0x38, 0x31, 0x36, //0x000064e4 QUAD $0x3632373438303830; QUAD $0x3631383136333333  // .asciz 16, '0808472633361816'
	0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064f4 QUAD $0x0000003532363034; QUAD $0x0000000000000000  // .asciz 16, '40625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006504 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006514 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006524 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006534 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x00006538 .long 16
	0x31, 0x31, 0x31, 0x30, 0x32, 0x32, 0x33, 0x30, 0x32, 0x34, 0x36, 0x32, 0x35, 0x31, 0x35, 0x36, //0x0000653c QUAD $0x3033323230313131; QUAD $0x3635313532363432  // .asciz 16, '1110223024625156'
	0x35, 0x34, 0x30, 0x34, 0x32, 0x33, 0x36, 0x33, 0x31, 0x36, 0x36, 0x38, 0x30, 0x39, 0x30, 0x38, //0x0000654c QUAD $0x3336333234303435; QUAD $0x3830393038363631  // .asciz 16, '5404236316680908'
	0x32, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000655c QUAD $0x0000353231333032; QUAD $0x0000000000000000  // .asciz 16, '203125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000656c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000657c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000658c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000659c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x11, 0x00, 0x00, 0x00, //0x000065a0 .long 17
	0x35, 0x35, 0x35, 0x31, 0x31, 0x31, 0x35, 0x31, 0x32, 0x33, 0x31, 0x32, 0x35, 0x37, 0x38, 0x32, //0x000065a4 QUAD $0x3135313131353535; QUAD $0x3238373532313332  // .asciz 16, '5551115123125782'
	0x37, 0x30, 0x32, 0x31, 0x31, 0x38, 0x31, 0x35, 0x38, 0x33, 0x34, 0x30, 0x34, 0x35, 0x34, 0x31, //0x000065b4 QUAD $0x3531383131323037; QUAD $0x3134353430343338  // .asciz 16, '7021181583404541'
	0x30, 0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065c4 QUAD $0x0000353236353130; QUAD $0x0000000000000000  // .asciz 16, '015625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006604 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x11, 0x00, 0x00, 0x00, //0x00006608 .long 17
	0x32, 0x37, 0x37, 0x35, 0x35, 0x35, 0x37, 0x35, 0x36, 0x31, 0x35, 0x36, 0x32, 0x38, 0x39, 0x31, //0x0000660c QUAD $0x3537353535373732; QUAD $0x3139383236353136  // .asciz 16, '2775557561562891'
	0x33, 0x35, 0x31, 0x30, 0x35, 0x39, 0x30, 0x37, 0x39, 0x31, 0x37, 0x30, 0x32, 0x32, 0x37, 0x30, //0x0000661c QUAD $0x3730393530313533; QUAD $0x3037323230373139  // .asciz 16, '3510590791702270'
	0x35, 0x30, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000662c QUAD $0x0035323138373035; QUAD $0x0000000000000000  // .asciz 16, '5078125\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000663c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000664c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000665c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000666c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x11, 0x00, 0x00, 0x00, //0x00006670 .long 17
	0x31, 0x33, 0x38, 0x37, 0x37, 0x37, 0x38, 0x37, 0x38, 0x30, 0x37, 0x38, 0x31, 0x34, 0x34, 0x35, //0x00006674 QUAD $0x3738373737383331; QUAD $0x3534343138373038  // .asciz 16, '1387778780781445'
	0x36, 0x37, 0x35, 0x35, 0x32, 0x39, 0x35, 0x33, 0x39, 0x35, 0x38, 0x35, 0x31, 0x31, 0x33, 0x35, //0x00006684 QUAD $0x3335393235353736; QUAD $0x3533313135383539  // .asciz 16, '6755295395851135'
	0x32, 0x35, 0x33, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006694 QUAD $0x3532363039333532; QUAD $0x0000000000000000  // .asciz 16, '25390625\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000066d4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x12, 0x00, 0x00, 0x00, //0x000066d8 .long 18
	0x36, 0x39, 0x33, 0x38, 0x38, 0x39, 0x33, 0x39, 0x30, 0x33, 0x39, 0x30, 0x37, 0x32, 0x32, 0x38, //0x000066dc QUAD $0x3933393838333936; QUAD $0x3832323730393330  // .asciz 16, '6938893903907228'
	0x33, 0x37, 0x37, 0x36, 0x34, 0x37, 0x36, 0x39, 0x37, 0x39, 0x32, 0x35, 0x35, 0x36, 0x37, 0x36, //0x000066ec QUAD $0x3936373436373733; QUAD $0x3637363535323937  // .asciz 16, '3776476979255676'
	0x32, 0x36, 0x39, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066fc QUAD $0x3532313335393632; QUAD $0x0000000000000000  // .asciz 16, '26953125\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000670c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000671c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000672c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000673c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x12, 0x00, 0x00, 0x00, //0x00006740 .long 18
	0x33, 0x34, 0x36, 0x39, 0x34, 0x34, 0x36, 0x39, 0x35, 0x31, 0x39, 0x35, 0x33, 0x36, 0x31, 0x34, //0x00006744 QUAD $0x3936343439363433; QUAD $0x3431363335393135  // .asciz 16, '3469446951953614'
	0x31, 0x38, 0x38, 0x38, 0x32, 0x33, 0x38, 0x34, 0x38, 0x39, 0x36, 0x32, 0x37, 0x38, 0x33, 0x38, //0x00006754 QUAD $0x3438333238383831; QUAD $0x3833383732363938  // .asciz 16, '1888238489627838'
	0x31, 0x33, 0x34, 0x37, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006764 QUAD $0x3236353637343331; QUAD $0x0000000000000035  // .asciz 16, '134765625\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006774 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006784 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006794 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000067a4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x12, 0x00, 0x00, 0x00, //0x000067a8 .long 18
	0x31, 0x37, 0x33, 0x34, 0x37, 0x32, 0x33, 0x34, 0x37, 0x35, 0x39, 0x37, 0x36, 0x38, 0x30, 0x37, //0x000067ac QUAD $0x3433323734333731; QUAD $0x3730383637393537  // .asciz 16, '1734723475976807'
	0x30, 0x39, 0x34, 0x34, 0x31, 0x31, 0x39, 0x32, 0x34, 0x34, 0x38, 0x31, 0x33, 0x39, 0x31, 0x39, //0x000067bc QUAD $0x3239313134343930; QUAD $0x3931393331383434  // .asciz 16, '0944119244813919'
	0x30, 0x36, 0x37, 0x33, 0x38, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067cc QUAD $0x3138323833373630; QUAD $0x0000000000003532  // .asciz 16, '0673828125\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000680c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x13, 0x00, 0x00, 0x00, //0x00006810 .long 19
	0x38, 0x36, 0x37, 0x33, 0x36, 0x31, 0x37, 0x33, 0x37, 0x39, 0x38, 0x38, 0x34, 0x30, 0x33, 0x35, //0x00006814 QUAD $0x3337313633373638; QUAD $0x3533303438383937  // .asciz 16, '8673617379884035'
	0x34, 0x37, 0x32, 0x30, 0x35, 0x39, 0x36, 0x32, 0x32, 0x34, 0x30, 0x36, 0x39, 0x35, 0x39, 0x35, //0x00006824 QUAD $0x3236393530323734; QUAD $0x3539353936303432  // .asciz 16, '4720596224069595'
	0x33, 0x33, 0x36, 0x39, 0x31, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006834 QUAD $0x3630343139363333; QUAD $0x0000000000003532  // .asciz 16, '3369140625\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006844 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006854 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006864 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006874 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
}
 
