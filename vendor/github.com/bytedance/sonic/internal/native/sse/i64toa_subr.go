// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__i64toa = 80
)

const (
    _stack__i64toa = 8
)

const (
    _size__i64toa = 2336
)

var (
    _pcsp__i64toa = [][2]uint32{
        {1, 0},
        {170, 8},
        {171, 0},
        {505, 8},
        {506, 0},
        {637, 8},
        {638, 0},
        {1117, 8},
        {1118, 0},
        {1254, 8},
        {1255, 0},
        {1572, 8},
        {1573, 0},
        {1945, 8},
        {1946, 0},
        {2324, 8},
        {2326, 0},
    }
)

var _cfunc_i64toa = []loader.CFunc{
    {"_i64toa_entry", 0,  _entry__i64toa, 0, nil},
    {"_i64toa", _entry__i64toa, _size__i64toa, _stack__i64toa, _pcsp__i64toa},
}
