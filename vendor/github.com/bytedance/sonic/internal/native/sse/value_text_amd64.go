// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package sse

var _text_value = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, // QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x00000010 LCPI0_1
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000010 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000020 LCPI0_2
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000020 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000030 LCPI0_3
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000030 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	//0x00000040 LCPI0_4
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x00000040 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	//0x00000050 LCPI0_5
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000050 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x00000060 LCPI0_6
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000060 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000070 LCPI0_7
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000070 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x00000080 LCPI0_8
	0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, //0x00000080 QUAD $0x6565656565656565; QUAD $0x6565656565656565  // .space 16, 'eeeeeeeeeeeeeeee'
	//0x00000090 LCPI0_9
	0x00, 0x00, 0x30, 0x43, //0x00000090 .long 1127219200
	0x00, 0x00, 0x30, 0x45, //0x00000094 .long 1160773632
	0x00, 0x00, 0x00, 0x00, //0x00000098 .long 0
	0x00, 0x00, 0x00, 0x00, //0x0000009c .long 0
	//0x000000a0 LCPI0_10
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x43, //0x000000a0 .quad 4841369599423283200
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x45, //0x000000a8 .quad 4985484787499139072
	//0x000000b0 .p2align 3, 0x00
	//0x000000b0 LCPI0_11
	0x00, 0x00, 0x34, 0x26, 0xf5, 0x6b, 0x0c, 0x43, //0x000000b0 .quad 4831355200913801216
	//0x000000b8 LCPI0_12
	0x00, 0x00, 0x34, 0x26, 0xf5, 0x6b, 0x0c, 0xc3, //0x000000b8 .quad -4392016835940974592
	//0x000000c0 .p2align 4, 0x90
	//0x000000c0 _value
	0x55, //0x000000c0 pushq        %rbp
	0x48, 0x89, 0xe5, //0x000000c1 movq         %rsp, %rbp
	0x41, 0x57, //0x000000c4 pushq        %r15
	0x41, 0x56, //0x000000c6 pushq        %r14
	0x41, 0x55, //0x000000c8 pushq        %r13
	0x41, 0x54, //0x000000ca pushq        %r12
	0x53, //0x000000cc pushq        %rbx
	0x48, 0x83, 0xec, 0x40, //0x000000cd subq         $64, %rsp
	0x49, 0x89, 0xcd, //0x000000d1 movq         %rcx, %r13
	0x49, 0x89, 0xd3, //0x000000d4 movq         %rdx, %r11
	0x48, 0x89, 0xf8, //0x000000d7 movq         %rdi, %rax
	0x48, 0x89, 0xd1, //0x000000da movq         %rdx, %rcx
	0x48, 0x29, 0xf1, //0x000000dd subq         %rsi, %rcx
	0x0f, 0x83, 0x2c, 0x00, 0x00, 0x00, //0x000000e0 jae          LBB0_5
	0x42, 0x8a, 0x3c, 0x18, //0x000000e6 movb         (%rax,%r11), %dil
	0x40, 0x80, 0xff, 0x0d, //0x000000ea cmpb         $13, %dil
	0x0f, 0x84, 0x1e, 0x00, 0x00, 0x00, //0x000000ee je           LBB0_5
	0x40, 0x80, 0xff, 0x20, //0x000000f4 cmpb         $32, %dil
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x000000f8 je           LBB0_5
	0x8d, 0x57, 0xf7, //0x000000fe leal         $-9(%rdi), %edx
	0x80, 0xfa, 0x01, //0x00000101 cmpb         $1, %dl
	0x0f, 0x86, 0x08, 0x00, 0x00, 0x00, //0x00000104 jbe          LBB0_5
	0x4d, 0x89, 0xdf, //0x0000010a movq         %r11, %r15
	0xe9, 0x2c, 0x01, 0x00, 0x00, //0x0000010d jmp          LBB0_28
	//0x00000112 LBB0_5
	0x4d, 0x8d, 0x7b, 0x01, //0x00000112 leaq         $1(%r11), %r15
	0x49, 0x39, 0xf7, //0x00000116 cmpq         %rsi, %r15
	0x0f, 0x83, 0x24, 0x00, 0x00, 0x00, //0x00000119 jae          LBB0_9
	0x42, 0x8a, 0x3c, 0x38, //0x0000011f movb         (%rax,%r15), %dil
	0x40, 0x80, 0xff, 0x0d, //0x00000123 cmpb         $13, %dil
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000127 je           LBB0_9
	0x40, 0x80, 0xff, 0x20, //0x0000012d cmpb         $32, %dil
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x00000131 je           LBB0_9
	0x8d, 0x57, 0xf7, //0x00000137 leal         $-9(%rdi), %edx
	0x80, 0xfa, 0x01, //0x0000013a cmpb         $1, %dl
	0x0f, 0x87, 0xfb, 0x00, 0x00, 0x00, //0x0000013d ja           LBB0_28
	//0x00000143 LBB0_9
	0x4d, 0x8d, 0x7b, 0x02, //0x00000143 leaq         $2(%r11), %r15
	0x49, 0x39, 0xf7, //0x00000147 cmpq         %rsi, %r15
	0x0f, 0x83, 0x24, 0x00, 0x00, 0x00, //0x0000014a jae          LBB0_13
	0x42, 0x8a, 0x3c, 0x38, //0x00000150 movb         (%rax,%r15), %dil
	0x40, 0x80, 0xff, 0x0d, //0x00000154 cmpb         $13, %dil
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000158 je           LBB0_13
	0x40, 0x80, 0xff, 0x20, //0x0000015e cmpb         $32, %dil
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x00000162 je           LBB0_13
	0x8d, 0x57, 0xf7, //0x00000168 leal         $-9(%rdi), %edx
	0x80, 0xfa, 0x01, //0x0000016b cmpb         $1, %dl
	0x0f, 0x87, 0xca, 0x00, 0x00, 0x00, //0x0000016e ja           LBB0_28
	//0x00000174 LBB0_13
	0x4d, 0x8d, 0x7b, 0x03, //0x00000174 leaq         $3(%r11), %r15
	0x49, 0x39, 0xf7, //0x00000178 cmpq         %rsi, %r15
	0x0f, 0x83, 0x24, 0x00, 0x00, 0x00, //0x0000017b jae          LBB0_17
	0x42, 0x8a, 0x3c, 0x38, //0x00000181 movb         (%rax,%r15), %dil
	0x40, 0x80, 0xff, 0x0d, //0x00000185 cmpb         $13, %dil
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000189 je           LBB0_17
	0x40, 0x80, 0xff, 0x20, //0x0000018f cmpb         $32, %dil
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x00000193 je           LBB0_17
	0x8d, 0x57, 0xf7, //0x00000199 leal         $-9(%rdi), %edx
	0x80, 0xfa, 0x01, //0x0000019c cmpb         $1, %dl
	0x0f, 0x87, 0x99, 0x00, 0x00, 0x00, //0x0000019f ja           LBB0_28
	//0x000001a5 LBB0_17
	0x49, 0x8d, 0x53, 0x04, //0x000001a5 leaq         $4(%r11), %rdx
	0x48, 0x39, 0xf2, //0x000001a9 cmpq         %rsi, %rdx
	0x0f, 0x83, 0x57, 0x00, 0x00, 0x00, //0x000001ac jae          LBB0_23
	0x48, 0x39, 0xd6, //0x000001b2 cmpq         %rdx, %rsi
	0x0f, 0x84, 0x56, 0x00, 0x00, 0x00, //0x000001b5 je           LBB0_24
	0x48, 0x8d, 0x14, 0x30, //0x000001bb leaq         (%rax,%rsi), %rdx
	0x48, 0x83, 0xc1, 0x04, //0x000001bf addq         $4, %rcx
	0x4d, 0x8d, 0x7c, 0x03, 0x05, //0x000001c3 leaq         $5(%r11,%rax), %r15
	0x48, 0xbf, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000001c8 movabsq      $4294977024, %rdi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000001d2 .p2align 4, 0x90
	//0x000001e0 LBB0_20
	0x41, 0x0f, 0xbe, 0x5f, 0xff, //0x000001e0 movsbl       $-1(%r15), %ebx
	0x83, 0xfb, 0x20, //0x000001e5 cmpl         $32, %ebx
	0x0f, 0x87, 0x3a, 0x00, 0x00, 0x00, //0x000001e8 ja           LBB0_26
	0x48, 0x0f, 0xa3, 0xdf, //0x000001ee btq          %rbx, %rdi
	0x0f, 0x83, 0x30, 0x00, 0x00, 0x00, //0x000001f2 jae          LBB0_26
	0x49, 0xff, 0xc7, //0x000001f8 incq         %r15
	0x48, 0xff, 0xc1, //0x000001fb incq         %rcx
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x000001fe jne          LBB0_20
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x00000204 jmp          LBB0_25
	//0x00000209 LBB0_23
	0x49, 0x89, 0xd3, //0x00000209 movq         %rdx, %r11
	0xe9, 0xd6, 0x00, 0x00, 0x00, //0x0000020c jmp          LBB0_37
	//0x00000211 LBB0_24
	0x48, 0x01, 0xc2, //0x00000211 addq         %rax, %rdx
	//0x00000214 LBB0_25
	0x48, 0x29, 0xc2, //0x00000214 subq         %rax, %rdx
	0x49, 0x89, 0xd7, //0x00000217 movq         %rdx, %r15
	0x49, 0x39, 0xf7, //0x0000021a cmpq         %rsi, %r15
	0x0f, 0x82, 0x17, 0x00, 0x00, 0x00, //0x0000021d jb           LBB0_27
	0xe9, 0xbf, 0x00, 0x00, 0x00, //0x00000223 jmp          LBB0_37
	//0x00000228 LBB0_26
	0x48, 0x89, 0xc1, //0x00000228 movq         %rax, %rcx
	0x48, 0xf7, 0xd1, //0x0000022b notq         %rcx
	0x49, 0x01, 0xcf, //0x0000022e addq         %rcx, %r15
	0x49, 0x39, 0xf7, //0x00000231 cmpq         %rsi, %r15
	0x0f, 0x83, 0xad, 0x00, 0x00, 0x00, //0x00000234 jae          LBB0_37
	//0x0000023a LBB0_27
	0x42, 0x8a, 0x3c, 0x38, //0x0000023a movb         (%rax,%r15), %dil
	//0x0000023e LBB0_28
	0x40, 0x0f, 0xbe, 0xcf, //0x0000023e movsbl       %dil, %ecx
	0x83, 0xf9, 0x7d, //0x00000242 cmpl         $125, %ecx
	0x0f, 0x87, 0xb8, 0x04, 0x00, 0x00, //0x00000245 ja           LBB0_99
	0x4d, 0x8d, 0x5f, 0x01, //0x0000024b leaq         $1(%r15), %r11
	0x4a, 0x8d, 0x1c, 0x38, //0x0000024f leaq         (%rax,%r15), %rbx
	0x48, 0x8d, 0x15, 0x76, 0x30, 0x00, 0x00, //0x00000253 leaq         $12406(%rip), %rdx  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x0c, 0x8a, //0x0000025a movslq       (%rdx,%rcx,4), %rcx
	0x48, 0x01, 0xd1, //0x0000025e addq         %rdx, %rcx
	0xff, 0xe1, //0x00000261 jmpq         *%rcx
	//0x00000263 LBB0_30
	0x41, 0xf6, 0xc0, 0x02, //0x00000263 testb        $2, %r8b
	0x0f, 0x85, 0x94, 0x00, 0x00, 0x00, //0x00000267 jne          LBB0_39
	0x4d, 0x8b, 0x65, 0x20, //0x0000026d movq         $32(%r13), %r12
	0x49, 0x8b, 0x4d, 0x28, //0x00000271 movq         $40(%r13), %rcx
	0x48, 0x89, 0x4d, 0xa8, //0x00000275 movq         %rcx, $-88(%rbp)
	0x49, 0xc7, 0x45, 0x00, 0x09, 0x00, 0x00, 0x00, //0x00000279 movq         $9, (%r13)
	0x49, 0xc7, 0x45, 0x08, 0x00, 0x00, 0x00, 0x00, //0x00000281 movq         $0, $8(%r13)
	0x49, 0xc7, 0x45, 0x10, 0x00, 0x00, 0x00, 0x00, //0x00000289 movq         $0, $16(%r13)
	0x4d, 0x89, 0x7d, 0x18, //0x00000291 movq         %r15, $24(%r13)
	0x49, 0x39, 0xf7, //0x00000295 cmpq         %rsi, %r15
	0x0f, 0x83, 0x8e, 0x0c, 0x00, 0x00, //0x00000298 jae          LBB0_198
	0x49, 0x89, 0xd8, //0x0000029e movq         %rbx, %r8
	0x8a, 0x1b, //0x000002a1 movb         (%rbx), %bl
	0x41, 0xb9, 0x01, 0x00, 0x00, 0x00, //0x000002a3 movl         $1, %r9d
	0x4c, 0x89, 0xff, //0x000002a9 movq         %r15, %rdi
	0x80, 0xfb, 0x2d, //0x000002ac cmpb         $45, %bl
	0x0f, 0x85, 0x16, 0x00, 0x00, 0x00, //0x000002af jne          LBB0_35
	0x49, 0x39, 0xf3, //0x000002b5 cmpq         %rsi, %r11
	0x0f, 0x83, 0x6e, 0x0c, 0x00, 0x00, //0x000002b8 jae          LBB0_198
	0x42, 0x8a, 0x1c, 0x18, //0x000002be movb         (%rax,%r11), %bl
	0x41, 0xb9, 0xff, 0xff, 0xff, 0xff, //0x000002c2 movl         $-1, %r9d
	0x4c, 0x89, 0xdf, //0x000002c8 movq         %r11, %rdi
	//0x000002cb LBB0_35
	0x8d, 0x4b, 0xd0, //0x000002cb leal         $-48(%rbx), %ecx
	0x80, 0xf9, 0x0a, //0x000002ce cmpb         $10, %cl
	0x0f, 0x82, 0x5b, 0x03, 0x00, 0x00, //0x000002d1 jb           LBB0_84
	0x49, 0xc7, 0x45, 0x00, 0xfe, 0xff, 0xff, 0xff, //0x000002d7 movq         $-2, (%r13)
	0x49, 0x89, 0xfb, //0x000002df movq         %rdi, %r11
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x000002e2 jmp          LBB0_38
	//0x000002e7 LBB0_37
	0x49, 0xc7, 0x45, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000002e7 movq         $1, (%r13)
	//0x000002ef LBB0_38
	0x4c, 0x89, 0xd8, //0x000002ef movq         %r11, %rax
	0x48, 0x83, 0xc4, 0x40, //0x000002f2 addq         $64, %rsp
	0x5b, //0x000002f6 popq         %rbx
	0x41, 0x5c, //0x000002f7 popq         %r12
	0x41, 0x5d, //0x000002f9 popq         %r13
	0x41, 0x5e, //0x000002fb popq         %r14
	0x41, 0x5f, //0x000002fd popq         %r15
	0x5d, //0x000002ff popq         %rbp
	0xc3, //0x00000300 retq         
	//0x00000301 LBB0_39
	0x48, 0x89, 0x45, 0xc0, //0x00000301 movq         %rax, $-64(%rbp)
	0x4c, 0x29, 0xfe, //0x00000305 subq         %r15, %rsi
	0x31, 0xc0, //0x00000308 xorl         %eax, %eax
	0x40, 0x80, 0xff, 0x2d, //0x0000030a cmpb         $45, %dil
	0x0f, 0x94, 0xc0, //0x0000030e sete         %al
	0x48, 0x01, 0xc3, //0x00000311 addq         %rax, %rbx
	0x48, 0x29, 0xc6, //0x00000314 subq         %rax, %rsi
	0x0f, 0x84, 0xa1, 0x1d, 0x00, 0x00, //0x00000317 je           LBB0_450
	0x4c, 0x89, 0x5d, 0xb8, //0x0000031d movq         %r11, $-72(%rbp)
	0x8a, 0x03, //0x00000321 movb         (%rbx), %al
	0x8d, 0x48, 0xd0, //0x00000323 leal         $-48(%rax), %ecx
	0x80, 0xf9, 0x09, //0x00000326 cmpb         $9, %cl
	0x0f, 0x87, 0x0f, 0x0d, 0x00, 0x00, //0x00000329 ja           LBB0_220
	0x3c, 0x30, //0x0000032f cmpb         $48, %al
	0x0f, 0x85, 0x34, 0x00, 0x00, 0x00, //0x00000331 jne          LBB0_45
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00000337 movl         $1, %r11d
	0x48, 0x83, 0xfe, 0x01, //0x0000033d cmpq         $1, %rsi
	0x0f, 0x84, 0xa6, 0x06, 0x00, 0x00, //0x00000341 je           LBB0_129
	0x8a, 0x43, 0x01, //0x00000347 movb         $1(%rbx), %al
	0x04, 0xd2, //0x0000034a addb         $-46, %al
	0x3c, 0x37, //0x0000034c cmpb         $55, %al
	0x0f, 0x87, 0x99, 0x06, 0x00, 0x00, //0x0000034e ja           LBB0_129
	0x0f, 0xb6, 0xc0, //0x00000354 movzbl       %al, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00000357 movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x00000361 btq          %rax, %rcx
	0x0f, 0x83, 0x82, 0x06, 0x00, 0x00, //0x00000365 jae          LBB0_129
	//0x0000036b LBB0_45
	0x4c, 0x89, 0x6d, 0xd0, //0x0000036b movq         %r13, $-48(%rbp)
	0x48, 0x83, 0xfe, 0x10, //0x0000036f cmpq         $16, %rsi
	0x0f, 0x82, 0x51, 0x1d, 0x00, 0x00, //0x00000373 jb           LBB0_452
	0xba, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000379 movl         $2863311530, %edx
	0x48, 0x8d, 0x46, 0xf0, //0x0000037e leaq         $-16(%rsi), %rax
	0x48, 0x89, 0xc1, //0x00000382 movq         %rax, %rcx
	0x48, 0x83, 0xe1, 0xf0, //0x00000385 andq         $-16, %rcx
	0x4c, 0x8d, 0x54, 0x19, 0x10, //0x00000389 leaq         $16(%rcx,%rbx), %r10
	0x83, 0xe0, 0x0f, //0x0000038e andl         $15, %eax
	0x48, 0xc7, 0x45, 0xa8, 0xff, 0xff, 0xff, 0xff, //0x00000391 movq         $-1, $-88(%rbp)
	0xf3, 0x44, 0x0f, 0x6f, 0x05, 0x8e, 0xfc, 0xff, 0xff, //0x00000399 movdqu       $-882(%rip), %xmm8  /* LCPI0_3+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x15, 0x95, 0xfc, 0xff, 0xff, //0x000003a2 movdqu       $-875(%rip), %xmm10  /* LCPI0_4+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x0d, 0x9c, 0xfc, 0xff, 0xff, //0x000003ab movdqu       $-868(%rip), %xmm9  /* LCPI0_5+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x1d, 0xa4, 0xfc, 0xff, 0xff, //0x000003b4 movdqu       $-860(%rip), %xmm3  /* LCPI0_6+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x25, 0x5c, 0xfc, 0xff, 0xff, //0x000003bc movdqu       $-932(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x2d, 0xa4, 0xfc, 0xff, 0xff, //0x000003c4 movdqu       $-860(%rip), %xmm5  /* LCPI0_7+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x35, 0xac, 0xfc, 0xff, 0xff, //0x000003cc movdqu       $-852(%rip), %xmm6  /* LCPI0_8+0(%rip) */
	0x48, 0x81, 0xc2, 0x55, 0x55, 0x55, 0x55, //0x000003d4 addq         $1431655765, %rdx
	0x48, 0x89, 0x55, 0xc8, //0x000003db movq         %rdx, $-56(%rbp)
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x000003df movq         $-1, %r12
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x000003e6 movq         $-1, %r14
	0x48, 0x89, 0xdf, //0x000003ed movq         %rbx, %rdi
	//0x000003f0 .p2align 4, 0x90
	//0x000003f0 LBB0_47
	0x49, 0x89, 0xd8, //0x000003f0 movq         %rbx, %r8
	0xf3, 0x0f, 0x6f, 0x3f, //0x000003f3 movdqu       (%rdi), %xmm7
	0x66, 0x0f, 0x6f, 0xc7, //0x000003f7 movdqa       %xmm7, %xmm0
	0x66, 0x41, 0x0f, 0x64, 0xc0, //0x000003fb pcmpgtb      %xmm8, %xmm0
	0x66, 0x41, 0x0f, 0x6f, 0xca, //0x00000400 movdqa       %xmm10, %xmm1
	0x66, 0x0f, 0x64, 0xcf, //0x00000405 pcmpgtb      %xmm7, %xmm1
	0x66, 0x0f, 0xdb, 0xc8, //0x00000409 pand         %xmm0, %xmm1
	0x66, 0x0f, 0x6f, 0xc7, //0x0000040d movdqa       %xmm7, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc1, //0x00000411 pcmpeqb      %xmm9, %xmm0
	0x66, 0x0f, 0x6f, 0xd7, //0x00000416 movdqa       %xmm7, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x0000041a pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xeb, 0xd0, //0x0000041e por          %xmm0, %xmm2
	0x66, 0x0f, 0x6f, 0xc7, //0x00000422 movdqa       %xmm7, %xmm0
	0x66, 0x0f, 0xeb, 0xc4, //0x00000426 por          %xmm4, %xmm0
	0x66, 0x0f, 0x74, 0xc6, //0x0000042a pcmpeqb      %xmm6, %xmm0
	0x66, 0x0f, 0x74, 0xfd, //0x0000042e pcmpeqb      %xmm5, %xmm7
	0x66, 0x44, 0x0f, 0xd7, 0xc8, //0x00000432 pmovmskb     %xmm0, %r9d
	0x66, 0x0f, 0xeb, 0xc7, //0x00000437 por          %xmm7, %xmm0
	0x66, 0x0f, 0xeb, 0xca, //0x0000043b por          %xmm2, %xmm1
	0x66, 0x0f, 0xeb, 0xc8, //0x0000043f por          %xmm0, %xmm1
	0x66, 0x0f, 0xd7, 0xdf, //0x00000443 pmovmskb     %xmm7, %ebx
	0x66, 0x44, 0x0f, 0xd7, 0xea, //0x00000447 pmovmskb     %xmm2, %r13d
	0x66, 0x0f, 0xd7, 0xc9, //0x0000044c pmovmskb     %xmm1, %ecx
	0x48, 0x33, 0x4d, 0xc8, //0x00000450 xorq         $-56(%rbp), %rcx
	0x48, 0x0f, 0xbc, 0xc9, //0x00000454 bsfq         %rcx, %rcx
	0x83, 0xf9, 0x10, //0x00000458 cmpl         $16, %ecx
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x0000045b je           LBB0_49
	0xba, 0xff, 0xff, 0xff, 0xff, //0x00000461 movl         $-1, %edx
	0xd3, 0xe2, //0x00000466 shll         %cl, %edx
	0xf7, 0xd2, //0x00000468 notl         %edx
	0x21, 0xd3, //0x0000046a andl         %edx, %ebx
	0x41, 0x21, 0xd1, //0x0000046c andl         %edx, %r9d
	0x44, 0x21, 0xea, //0x0000046f andl         %r13d, %edx
	0x41, 0x89, 0xd5, //0x00000472 movl         %edx, %r13d
	//0x00000475 LBB0_49
	0x8d, 0x53, 0xff, //0x00000475 leal         $-1(%rbx), %edx
	0x21, 0xda, //0x00000478 andl         %ebx, %edx
	0x0f, 0x85, 0x87, 0x09, 0x00, 0x00, //0x0000047a jne          LBB0_177
	0x41, 0x8d, 0x51, 0xff, //0x00000480 leal         $-1(%r9), %edx
	0x44, 0x21, 0xca, //0x00000484 andl         %r9d, %edx
	0x0f, 0x85, 0x7a, 0x09, 0x00, 0x00, //0x00000487 jne          LBB0_177
	0x41, 0x8d, 0x55, 0xff, //0x0000048d leal         $-1(%r13), %edx
	0x44, 0x21, 0xea, //0x00000491 andl         %r13d, %edx
	0x0f, 0x85, 0x6d, 0x09, 0x00, 0x00, //0x00000494 jne          LBB0_177
	0x85, 0xdb, //0x0000049a testl        %ebx, %ebx
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x0000049c je           LBB0_55
	0x48, 0x89, 0xfa, //0x000004a2 movq         %rdi, %rdx
	0x4c, 0x29, 0xc2, //0x000004a5 subq         %r8, %rdx
	0x44, 0x0f, 0xbc, 0xdb, //0x000004a8 bsfl         %ebx, %r11d
	0x49, 0x01, 0xd3, //0x000004ac addq         %rdx, %r11
	0x49, 0x83, 0xfe, 0xff, //0x000004af cmpq         $-1, %r14
	0x0f, 0x85, 0x0f, 0x0c, 0x00, 0x00, //0x000004b3 jne          LBB0_229
	0x4d, 0x89, 0xde, //0x000004b9 movq         %r11, %r14
	//0x000004bc LBB0_55
	0x4c, 0x89, 0xc3, //0x000004bc movq         %r8, %rbx
	0x45, 0x85, 0xc9, //0x000004bf testl        %r9d, %r9d
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x000004c2 je           LBB0_58
	0x48, 0x89, 0xfa, //0x000004c8 movq         %rdi, %rdx
	0x48, 0x29, 0xda, //0x000004cb subq         %rbx, %rdx
	0x45, 0x0f, 0xbc, 0xd9, //0x000004ce bsfl         %r9d, %r11d
	0x49, 0x01, 0xd3, //0x000004d2 addq         %rdx, %r11
	0x49, 0x83, 0xfc, 0xff, //0x000004d5 cmpq         $-1, %r12
	0x0f, 0x85, 0x35, 0x09, 0x00, 0x00, //0x000004d9 jne          LBB0_178
	0x4d, 0x89, 0xdc, //0x000004df movq         %r11, %r12
	//0x000004e2 LBB0_58
	0x45, 0x85, 0xed, //0x000004e2 testl        %r13d, %r13d
	0x0f, 0x84, 0x1c, 0x00, 0x00, 0x00, //0x000004e5 je           LBB0_61
	0x48, 0x89, 0xfa, //0x000004eb movq         %rdi, %rdx
	0x48, 0x29, 0xda, //0x000004ee subq         %rbx, %rdx
	0x45, 0x0f, 0xbc, 0xdd, //0x000004f1 bsfl         %r13d, %r11d
	0x49, 0x01, 0xd3, //0x000004f5 addq         %rdx, %r11
	0x48, 0x83, 0x7d, 0xa8, 0xff, //0x000004f8 cmpq         $-1, $-88(%rbp)
	0x0f, 0x85, 0x11, 0x09, 0x00, 0x00, //0x000004fd jne          LBB0_178
	0x4c, 0x89, 0x5d, 0xa8, //0x00000503 movq         %r11, $-88(%rbp)
	//0x00000507 LBB0_61
	0x83, 0xf9, 0x10, //0x00000507 cmpl         $16, %ecx
	0x0f, 0x85, 0xaf, 0x00, 0x00, 0x00, //0x0000050a jne          LBB0_74
	0x48, 0x83, 0xc7, 0x10, //0x00000510 addq         $16, %rdi
	0x48, 0x83, 0xc6, 0xf0, //0x00000514 addq         $-16, %rsi
	0x48, 0x83, 0xfe, 0x0f, //0x00000518 cmpq         $15, %rsi
	0x0f, 0x87, 0xce, 0xfe, 0xff, 0xff, //0x0000051c ja           LBB0_47
	0x48, 0x85, 0xc0, //0x00000522 testq        %rax, %rax
	0x4c, 0x8b, 0x6d, 0xd0, //0x00000525 movq         $-48(%rbp), %r13
	0x0f, 0x84, 0x9a, 0x00, 0x00, 0x00, //0x00000529 je           LBB0_75
	//0x0000052f LBB0_64
	0x49, 0x8d, 0x0c, 0x02, //0x0000052f leaq         (%r10,%rax), %rcx
	0x48, 0x8d, 0x35, 0x8e, 0x2f, 0x00, 0x00, //0x00000533 leaq         $12174(%rip), %rsi  /* LJTI0_1+0(%rip) */
	0xe9, 0x2d, 0x00, 0x00, 0x00, //0x0000053a jmp          LBB0_68
	//0x0000053f LBB0_65
	0x49, 0x89, 0xfb, //0x0000053f movq         %rdi, %r11
	0x49, 0x29, 0xdb, //0x00000542 subq         %rbx, %r11
	0x48, 0x83, 0x7d, 0xa8, 0xff, //0x00000545 cmpq         $-1, $-88(%rbp)
	0x0f, 0x85, 0x87, 0x0b, 0x00, 0x00, //0x0000054a jne          LBB0_453
	0x49, 0xff, 0xcb, //0x00000550 decq         %r11
	0x4c, 0x89, 0x5d, 0xa8, //0x00000553 movq         %r11, $-88(%rbp)
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000557 .p2align 4, 0x90
	//0x00000560 LBB0_67
	0x49, 0x89, 0xfa, //0x00000560 movq         %rdi, %r10
	0x48, 0xff, 0xc8, //0x00000563 decq         %rax
	0x0f, 0x84, 0xb9, 0x0a, 0x00, 0x00, //0x00000566 je           LBB0_218
	//0x0000056c LBB0_68
	0x41, 0x0f, 0xbe, 0x12, //0x0000056c movsbl       (%r10), %edx
	0x83, 0xc2, 0xd5, //0x00000570 addl         $-43, %edx
	0x83, 0xfa, 0x3a, //0x00000573 cmpl         $58, %edx
	0x0f, 0x87, 0x4d, 0x00, 0x00, 0x00, //0x00000576 ja           LBB0_75
	0x49, 0x8d, 0x7a, 0x01, //0x0000057c leaq         $1(%r10), %rdi
	0x48, 0x63, 0x14, 0x96, //0x00000580 movslq       (%rsi,%rdx,4), %rdx
	0x48, 0x01, 0xf2, //0x00000584 addq         %rsi, %rdx
	0xff, 0xe2, //0x00000587 jmpq         *%rdx
	//0x00000589 LBB0_70
	0x49, 0x89, 0xfb, //0x00000589 movq         %rdi, %r11
	0x49, 0x29, 0xdb, //0x0000058c subq         %rbx, %r11
	0x49, 0x83, 0xfc, 0xff, //0x0000058f cmpq         $-1, %r12
	0x0f, 0x85, 0x3e, 0x0b, 0x00, 0x00, //0x00000593 jne          LBB0_453
	0x49, 0xff, 0xcb, //0x00000599 decq         %r11
	0x4d, 0x89, 0xdc, //0x0000059c movq         %r11, %r12
	0xe9, 0xbc, 0xff, 0xff, 0xff, //0x0000059f jmp          LBB0_67
	//0x000005a4 LBB0_72
	0x49, 0x89, 0xfb, //0x000005a4 movq         %rdi, %r11
	0x49, 0x29, 0xdb, //0x000005a7 subq         %rbx, %r11
	0x49, 0x83, 0xfe, 0xff, //0x000005aa cmpq         $-1, %r14
	0x0f, 0x85, 0x23, 0x0b, 0x00, 0x00, //0x000005ae jne          LBB0_453
	0x49, 0xff, 0xcb, //0x000005b4 decq         %r11
	0x4d, 0x89, 0xde, //0x000005b7 movq         %r11, %r14
	0xe9, 0xa1, 0xff, 0xff, 0xff, //0x000005ba jmp          LBB0_67
	//0x000005bf LBB0_74
	0x48, 0x01, 0xcf, //0x000005bf addq         %rcx, %rdi
	0x49, 0x89, 0xfa, //0x000005c2 movq         %rdi, %r10
	0x4c, 0x8b, 0x6d, 0xd0, //0x000005c5 movq         $-48(%rbp), %r13
	//0x000005c9 LBB0_75
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000005c9 movq         $-1, %r11
	0x4d, 0x85, 0xe4, //0x000005d0 testq        %r12, %r12
	0x0f, 0x84, 0x5f, 0x0a, 0x00, 0x00, //0x000005d3 je           LBB0_219
	//0x000005d9 LBB0_76
	0x48, 0x8b, 0x4d, 0xa8, //0x000005d9 movq         $-88(%rbp), %rcx
	0x48, 0x85, 0xc9, //0x000005dd testq        %rcx, %rcx
	0x0f, 0x84, 0x52, 0x0a, 0x00, 0x00, //0x000005e0 je           LBB0_219
	0x4d, 0x85, 0xf6, //0x000005e6 testq        %r14, %r14
	0x0f, 0x84, 0x49, 0x0a, 0x00, 0x00, //0x000005e9 je           LBB0_219
	0x49, 0x29, 0xda, //0x000005ef subq         %rbx, %r10
	0x49, 0x8d, 0x42, 0xff, //0x000005f2 leaq         $-1(%r10), %rax
	0x49, 0x39, 0xc4, //0x000005f6 cmpq         %rax, %r12
	0x0f, 0x84, 0xdf, 0x03, 0x00, 0x00, //0x000005f9 je           LBB0_127
	0x49, 0x39, 0xc6, //0x000005ff cmpq         %rax, %r14
	0x0f, 0x84, 0xd6, 0x03, 0x00, 0x00, //0x00000602 je           LBB0_127
	0x48, 0x39, 0xc1, //0x00000608 cmpq         %rax, %rcx
	0x0f, 0x84, 0xcd, 0x03, 0x00, 0x00, //0x0000060b je           LBB0_127
	0x48, 0x85, 0xc9, //0x00000611 testq        %rcx, %rcx
	0x0f, 0x8e, 0x3e, 0x06, 0x00, 0x00, //0x00000614 jle          LBB0_145
	0x48, 0x8d, 0x41, 0xff, //0x0000061a leaq         $-1(%rcx), %rax
	0x49, 0x39, 0xc4, //0x0000061e cmpq         %rax, %r12
	0x0f, 0x84, 0x31, 0x06, 0x00, 0x00, //0x00000621 je           LBB0_145
	0x48, 0xf7, 0xd1, //0x00000627 notq         %rcx
	0x49, 0x89, 0xcb, //0x0000062a movq         %rcx, %r11
	0xe9, 0xb2, 0x03, 0x00, 0x00, //0x0000062d jmp          LBB0_128
	//0x00000632 LBB0_84
	0x80, 0xfb, 0x30, //0x00000632 cmpb         $48, %bl
	0x0f, 0x85, 0x34, 0x00, 0x00, 0x00, //0x00000635 jne          LBB0_88
	0x4c, 0x8d, 0x5f, 0x01, //0x0000063b leaq         $1(%rdi), %r11
	0x48, 0x39, 0xf7, //0x0000063f cmpq         %rsi, %rdi
	0x0f, 0x83, 0xa7, 0xfc, 0xff, 0xff, //0x00000642 jae          LBB0_38
	0x42, 0x8a, 0x0c, 0x18, //0x00000648 movb         (%rax,%r11), %cl
	0x80, 0xc1, 0xd2, //0x0000064c addb         $-46, %cl
	0x80, 0xf9, 0x37, //0x0000064f cmpb         $55, %cl
	0x0f, 0x87, 0x97, 0xfc, 0xff, 0xff, //0x00000652 ja           LBB0_38
	0x0f, 0xb6, 0xc9, //0x00000658 movzbl       %cl, %ecx
	0x48, 0xba, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x0000065b movabsq      $36028797027352577, %rdx
	0x48, 0x0f, 0xa3, 0xca, //0x00000665 btq          %rcx, %rdx
	0x0f, 0x83, 0x80, 0xfc, 0xff, 0xff, //0x00000669 jae          LBB0_38
	//0x0000066f LBB0_88
	0x48, 0x39, 0xf7, //0x0000066f cmpq         %rsi, %rdi
	0x44, 0x89, 0x4d, 0xc8, //0x00000672 movl         %r9d, $-56(%rbp)
	0x0f, 0x83, 0xff, 0x05, 0x00, 0x00, //0x00000676 jae          LBB0_148
	0x48, 0xff, 0xc7, //0x0000067c incq         %rdi
	0x31, 0xc9, //0x0000067f xorl         %ecx, %ecx
	0x49, 0x89, 0xfb, //0x00000681 movq         %rdi, %r11
	0x31, 0xd2, //0x00000684 xorl         %edx, %edx
	0x45, 0x31, 0xd2, //0x00000686 xorl         %r10d, %r10d
	//0x00000689 LBB0_90
	0x83, 0xfa, 0x12, //0x00000689 cmpl         $18, %edx
	0x0f, 0x8f, 0x13, 0x00, 0x00, 0x00, //0x0000068c jg           LBB0_92
	0x0f, 0xb6, 0xdb, //0x00000692 movzbl       %bl, %ebx
	0x4b, 0x8d, 0x3c, 0x92, //0x00000695 leaq         (%r10,%r10,4), %rdi
	0x4c, 0x8d, 0x54, 0x7b, 0xd0, //0x00000699 leaq         $-48(%rbx,%rdi,2), %r10
	0xff, 0xc2, //0x0000069e incl         %edx
	0xe9, 0x02, 0x00, 0x00, 0x00, //0x000006a0 jmp          LBB0_93
	//0x000006a5 LBB0_92
	0xff, 0xc1, //0x000006a5 incl         %ecx
	//0x000006a7 LBB0_93
	0x4c, 0x39, 0xde, //0x000006a7 cmpq         %r11, %rsi
	0x0f, 0x84, 0xcc, 0x06, 0x00, 0x00, //0x000006aa je           LBB0_165
	0x42, 0x0f, 0xb6, 0x1c, 0x18, //0x000006b0 movzbl       (%rax,%r11), %ebx
	0x8d, 0x7b, 0xd0, //0x000006b5 leal         $-48(%rbx), %edi
	0x49, 0xff, 0xc3, //0x000006b8 incq         %r11
	0x40, 0x80, 0xff, 0x0a, //0x000006bb cmpb         $10, %dil
	0x0f, 0x82, 0xc4, 0xff, 0xff, 0xff, //0x000006bf jb           LBB0_90
	0x45, 0x31, 0xf6, //0x000006c5 xorl         %r14d, %r14d
	0x85, 0xc9, //0x000006c8 testl        %ecx, %ecx
	0x41, 0x0f, 0x9f, 0xc6, //0x000006ca setg         %r14b
	0x80, 0xfb, 0x2e, //0x000006ce cmpb         $46, %bl
	0x0f, 0x85, 0x49, 0x07, 0x00, 0x00, //0x000006d1 jne          LBB0_179
	0x49, 0xc7, 0x45, 0x00, 0x08, 0x00, 0x00, 0x00, //0x000006d7 movq         $8, (%r13)
	0x49, 0x39, 0xf3, //0x000006df cmpq         %rsi, %r11
	0x0f, 0x83, 0x44, 0x08, 0x00, 0x00, //0x000006e2 jae          LBB0_198
	0x42, 0x8a, 0x1c, 0x18, //0x000006e8 movb         (%rax,%r11), %bl
	0x80, 0xc3, 0xd0, //0x000006ec addb         $-48, %bl
	0x41, 0xb9, 0x08, 0x00, 0x00, 0x00, //0x000006ef movl         $8, %r9d
	0x80, 0xfb, 0x0a, //0x000006f5 cmpb         $10, %bl
	0x0f, 0x82, 0x93, 0x06, 0x00, 0x00, //0x000006f8 jb           LBB0_167
	0xe9, 0x62, 0x09, 0x00, 0x00, //0x000006fe jmp          LBB0_98
	//0x00000703 LBB0_99
	0x49, 0xc7, 0x45, 0x00, 0xfe, 0xff, 0xff, 0xff, //0x00000703 movq         $-2, (%r13)
	0x4d, 0x89, 0xfb, //0x0000070b movq         %r15, %r11
	0xe9, 0xdc, 0xfb, 0xff, 0xff, //0x0000070e jmp          LBB0_38
	//0x00000713 LBB0_100
	0x4c, 0x89, 0xfa, //0x00000713 movq         %r15, %rdx
	0x48, 0xf7, 0xd2, //0x00000716 notq         %rdx
	0x41, 0xf6, 0xc0, 0x20, //0x00000719 testb        $32, %r8b
	0x4c, 0x89, 0x6d, 0xd0, //0x0000071d movq         %r13, $-48(%rbp)
	0x48, 0x89, 0x45, 0xc0, //0x00000721 movq         %rax, $-64(%rbp)
	0x4c, 0x89, 0x5d, 0xb8, //0x00000725 movq         %r11, $-72(%rbp)
	0x0f, 0x85, 0xed, 0x02, 0x00, 0x00, //0x00000729 jne          LBB0_132
	0x49, 0x39, 0xf3, //0x0000072f cmpq         %rsi, %r11
	0x0f, 0x84, 0x87, 0x2b, 0x00, 0x00, //0x00000732 je           LBB0_733
	0x49, 0x89, 0xf4, //0x00000738 movq         %rsi, %r12
	0x4d, 0x29, 0xdc, //0x0000073b subq         %r11, %r12
	0x4e, 0x8d, 0x2c, 0x18, //0x0000073e leaq         (%rax,%r11), %r13
	0x49, 0x83, 0xfc, 0x40, //0x00000742 cmpq         $64, %r12
	0x0f, 0x82, 0x84, 0x28, 0x00, 0x00, //0x00000746 jb           LBB0_692
	0x48, 0x89, 0xc1, //0x0000074c movq         %rax, %rcx
	0x45, 0x89, 0xe6, //0x0000074f movl         %r12d, %r14d
	0x41, 0x83, 0xe6, 0x3f, //0x00000752 andl         $63, %r14d
	0x48, 0x8d, 0x44, 0x16, 0xc0, //0x00000756 leaq         $-64(%rsi,%rdx), %rax
	0x48, 0x83, 0xe0, 0xc0, //0x0000075b andq         $-64, %rax
	0x49, 0x01, 0xc7, //0x0000075f addq         %rax, %r15
	0x4a, 0x8d, 0x44, 0x39, 0x41, //0x00000762 leaq         $65(%rcx,%r15), %rax
	0x48, 0x89, 0x45, 0xa8, //0x00000767 movq         %rax, $-88(%rbp)
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000076b movq         $-1, %r8
	0x45, 0x31, 0xff, //0x00000772 xorl         %r15d, %r15d
	0xf3, 0x0f, 0x6f, 0x05, 0x83, 0xf8, 0xff, 0xff, //0x00000775 movdqu       $-1917(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0x8b, 0xf8, 0xff, 0xff, //0x0000077d movdqu       $-1909(%rip), %xmm1  /* LCPI0_1+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000785 .p2align 4, 0x90
	//0x00000790 LBB0_104
	0xf3, 0x41, 0x0f, 0x6f, 0x55, 0x00, //0x00000790 movdqu       (%r13), %xmm2
	0xf3, 0x41, 0x0f, 0x6f, 0x5d, 0x10, //0x00000796 movdqu       $16(%r13), %xmm3
	0xf3, 0x41, 0x0f, 0x6f, 0x65, 0x20, //0x0000079c movdqu       $32(%r13), %xmm4
	0xf3, 0x41, 0x0f, 0x6f, 0x6d, 0x30, //0x000007a2 movdqu       $48(%r13), %xmm5
	0x66, 0x0f, 0x6f, 0xf2, //0x000007a8 movdqa       %xmm2, %xmm6
	0x66, 0x0f, 0x74, 0xf0, //0x000007ac pcmpeqb      %xmm0, %xmm6
	0x66, 0x0f, 0xd7, 0xfe, //0x000007b0 pmovmskb     %xmm6, %edi
	0x66, 0x0f, 0x6f, 0xf3, //0x000007b4 movdqa       %xmm3, %xmm6
	0x66, 0x0f, 0x74, 0xf0, //0x000007b8 pcmpeqb      %xmm0, %xmm6
	0x66, 0x0f, 0xd7, 0xd6, //0x000007bc pmovmskb     %xmm6, %edx
	0x66, 0x0f, 0x6f, 0xf4, //0x000007c0 movdqa       %xmm4, %xmm6
	0x66, 0x0f, 0x74, 0xf0, //0x000007c4 pcmpeqb      %xmm0, %xmm6
	0x66, 0x0f, 0xd7, 0xce, //0x000007c8 pmovmskb     %xmm6, %ecx
	0x66, 0x0f, 0x6f, 0xf5, //0x000007cc movdqa       %xmm5, %xmm6
	0x66, 0x0f, 0x74, 0xf0, //0x000007d0 pcmpeqb      %xmm0, %xmm6
	0x66, 0x0f, 0xd7, 0xde, //0x000007d4 pmovmskb     %xmm6, %ebx
	0x66, 0x0f, 0x74, 0xd1, //0x000007d8 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x000007dc pmovmskb     %xmm2, %eax
	0x66, 0x0f, 0x74, 0xd9, //0x000007e0 pcmpeqb      %xmm1, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xd3, //0x000007e4 pmovmskb     %xmm3, %r10d
	0x66, 0x0f, 0x74, 0xe1, //0x000007e9 pcmpeqb      %xmm1, %xmm4
	0x66, 0x44, 0x0f, 0xd7, 0xdc, //0x000007ed pmovmskb     %xmm4, %r11d
	0x66, 0x0f, 0x74, 0xe9, //0x000007f2 pcmpeqb      %xmm1, %xmm5
	0x66, 0x44, 0x0f, 0xd7, 0xcd, //0x000007f6 pmovmskb     %xmm5, %r9d
	0x48, 0xc1, 0xe3, 0x30, //0x000007fb shlq         $48, %rbx
	0x48, 0xc1, 0xe1, 0x20, //0x000007ff shlq         $32, %rcx
	0x48, 0xc1, 0xe2, 0x10, //0x00000803 shlq         $16, %rdx
	0x48, 0x09, 0xd7, //0x00000807 orq          %rdx, %rdi
	0x48, 0x09, 0xcf, //0x0000080a orq          %rcx, %rdi
	0x49, 0xc1, 0xe1, 0x30, //0x0000080d shlq         $48, %r9
	0x49, 0xc1, 0xe3, 0x20, //0x00000811 shlq         $32, %r11
	0x49, 0xc1, 0xe2, 0x10, //0x00000815 shlq         $16, %r10
	0x4c, 0x09, 0xd0, //0x00000819 orq          %r10, %rax
	0x4c, 0x09, 0xd8, //0x0000081c orq          %r11, %rax
	0x4c, 0x09, 0xc8, //0x0000081f orq          %r9, %rax
	0x49, 0x83, 0xf8, 0xff, //0x00000822 cmpq         $-1, %r8
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00000826 jne          LBB0_106
	0x48, 0x85, 0xc0, //0x0000082c testq        %rax, %rax
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x0000082f jne          LBB0_109
	//0x00000835 LBB0_106
	0x48, 0x09, 0xdf, //0x00000835 orq          %rbx, %rdi
	0x48, 0x89, 0xc1, //0x00000838 movq         %rax, %rcx
	0x4c, 0x09, 0xf9, //0x0000083b orq          %r15, %rcx
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x0000083e jne          LBB0_110
	//0x00000844 LBB0_107
	0x48, 0x85, 0xff, //0x00000844 testq        %rdi, %rdi
	0x0f, 0x85, 0x19, 0x09, 0x00, 0x00, //0x00000847 jne          LBB0_235
	//0x0000084d LBB0_108
	0x49, 0x83, 0xc4, 0xc0, //0x0000084d addq         $-64, %r12
	0x49, 0x83, 0xc5, 0x40, //0x00000851 addq         $64, %r13
	0x49, 0x83, 0xfc, 0x3f, //0x00000855 cmpq         $63, %r12
	0x0f, 0x87, 0x31, 0xff, 0xff, 0xff, //0x00000859 ja           LBB0_104
	0xe9, 0x7b, 0x08, 0x00, 0x00, //0x0000085f jmp          LBB0_230
	//0x00000864 LBB0_109
	0x4c, 0x89, 0xe9, //0x00000864 movq         %r13, %rcx
	0x48, 0x2b, 0x4d, 0xc0, //0x00000867 subq         $-64(%rbp), %rcx
	0x4c, 0x0f, 0xbc, 0xc0, //0x0000086b bsfq         %rax, %r8
	0x49, 0x01, 0xc8, //0x0000086f addq         %rcx, %r8
	0x48, 0x09, 0xdf, //0x00000872 orq          %rbx, %rdi
	0x48, 0x89, 0xc1, //0x00000875 movq         %rax, %rcx
	0x4c, 0x09, 0xf9, //0x00000878 orq          %r15, %rcx
	0x0f, 0x84, 0xc3, 0xff, 0xff, 0xff, //0x0000087b je           LBB0_107
	//0x00000881 LBB0_110
	0x4c, 0x89, 0xf9, //0x00000881 movq         %r15, %rcx
	0x48, 0xf7, 0xd1, //0x00000884 notq         %rcx
	0x48, 0x21, 0xc1, //0x00000887 andq         %rax, %rcx
	0x48, 0x8d, 0x1c, 0x09, //0x0000088a leaq         (%rcx,%rcx), %rbx
	0x4c, 0x09, 0xfb, //0x0000088e orq          %r15, %rbx
	0x48, 0x89, 0xda, //0x00000891 movq         %rbx, %rdx
	0x48, 0xf7, 0xd2, //0x00000894 notq         %rdx
	0x48, 0x21, 0xc2, //0x00000897 andq         %rax, %rdx
	0x48, 0xb8, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000089a movabsq      $-6148914691236517206, %rax
	0x48, 0x21, 0xc2, //0x000008a4 andq         %rax, %rdx
	0x45, 0x31, 0xff, //0x000008a7 xorl         %r15d, %r15d
	0x48, 0x01, 0xca, //0x000008aa addq         %rcx, %rdx
	0x41, 0x0f, 0x92, 0xc7, //0x000008ad setb         %r15b
	0x48, 0x01, 0xd2, //0x000008b1 addq         %rdx, %rdx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000008b4 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc2, //0x000008be xorq         %rax, %rdx
	0x48, 0x21, 0xda, //0x000008c1 andq         %rbx, %rdx
	0x48, 0xf7, 0xd2, //0x000008c4 notq         %rdx
	0x48, 0x21, 0xd7, //0x000008c7 andq         %rdx, %rdi
	0x48, 0x85, 0xff, //0x000008ca testq        %rdi, %rdi
	0x0f, 0x84, 0x7a, 0xff, 0xff, 0xff, //0x000008cd je           LBB0_108
	0xe9, 0x8e, 0x08, 0x00, 0x00, //0x000008d3 jmp          LBB0_235
	//0x000008d8 LBB0_111
	0x31, 0xc9, //0x000008d8 xorl         %ecx, %ecx
	0x45, 0x85, 0xc0, //0x000008da testl        %r8d, %r8d
	0x0f, 0x99, 0xc1, //0x000008dd setns        %cl
	0xb8, 0x0b, 0x00, 0x00, 0x00, //0x000008e0 movl         $11, %eax
	0xe9, 0xdd, 0x00, 0x00, 0x00, //0x000008e5 jmp          LBB0_126
	//0x000008ea LBB0_112
	0x31, 0xc9, //0x000008ea xorl         %ecx, %ecx
	0x45, 0x85, 0xc0, //0x000008ec testl        %r8d, %r8d
	0x0f, 0x99, 0xc1, //0x000008ef setns        %cl
	0xb8, 0x0a, 0x00, 0x00, 0x00, //0x000008f2 movl         $10, %eax
	0xe9, 0xcb, 0x00, 0x00, 0x00, //0x000008f7 jmp          LBB0_126
	//0x000008fc LBB0_113
	0x49, 0xc7, 0x45, 0x00, 0x05, 0x00, 0x00, 0x00, //0x000008fc movq         $5, (%r13)
	0xe9, 0xe6, 0xf9, 0xff, 0xff, //0x00000904 jmp          LBB0_38
	//0x00000909 LBB0_114
	0x31, 0xc9, //0x00000909 xorl         %ecx, %ecx
	0x45, 0x85, 0xc0, //0x0000090b testl        %r8d, %r8d
	0x0f, 0x99, 0xc1, //0x0000090e setns        %cl
	0xb8, 0x0c, 0x00, 0x00, 0x00, //0x00000911 movl         $12, %eax
	0xe9, 0xac, 0x00, 0x00, 0x00, //0x00000916 jmp          LBB0_126
	//0x0000091b LBB0_115
	0x48, 0x8d, 0x4e, 0xfc, //0x0000091b leaq         $-4(%rsi), %rcx
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x0000091f movq         $-1, %rdx
	0x49, 0x39, 0xcf, //0x00000926 cmpq         %rcx, %r15
	0x0f, 0x83, 0xea, 0x06, 0x00, 0x00, //0x00000929 jae          LBB0_217
	0x42, 0x8b, 0x0c, 0x18, //0x0000092f movl         (%rax,%r11), %ecx
	0x81, 0xf9, 0x61, 0x6c, 0x73, 0x65, //0x00000933 cmpl         $1702063201, %ecx
	0x0f, 0x85, 0x48, 0x03, 0x00, 0x00, //0x00000939 jne          LBB0_149
	0x49, 0x83, 0xc7, 0x05, //0x0000093f addq         $5, %r15
	0xba, 0x04, 0x00, 0x00, 0x00, //0x00000943 movl         $4, %edx
	0xe9, 0xc9, 0x06, 0x00, 0x00, //0x00000948 jmp          LBB0_216
	//0x0000094d LBB0_118
	0x48, 0x8d, 0x4e, 0xfd, //0x0000094d leaq         $-3(%rsi), %rcx
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00000951 movq         $-1, %rdx
	0x49, 0x39, 0xcf, //0x00000958 cmpq         %rcx, %r15
	0x0f, 0x83, 0xb8, 0x06, 0x00, 0x00, //0x0000095b jae          LBB0_217
	0x8b, 0x0b, //0x00000961 movl         (%rbx), %ecx
	0x81, 0xf9, 0x6e, 0x75, 0x6c, 0x6c, //0x00000963 cmpl         $1819047278, %ecx
	0x0f, 0x85, 0x57, 0x03, 0x00, 0x00, //0x00000969 jne          LBB0_153
	0x49, 0x83, 0xc7, 0x04, //0x0000096f addq         $4, %r15
	0xba, 0x02, 0x00, 0x00, 0x00, //0x00000973 movl         $2, %edx
	0xe9, 0x99, 0x06, 0x00, 0x00, //0x00000978 jmp          LBB0_216
	//0x0000097d LBB0_121
	0x48, 0x8d, 0x4e, 0xfd, //0x0000097d leaq         $-3(%rsi), %rcx
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00000981 movq         $-1, %rdx
	0x49, 0x39, 0xcf, //0x00000988 cmpq         %rcx, %r15
	0x0f, 0x83, 0x88, 0x06, 0x00, 0x00, //0x0000098b jae          LBB0_217
	0x8b, 0x0b, //0x00000991 movl         (%rbx), %ecx
	0x81, 0xf9, 0x74, 0x72, 0x75, 0x65, //0x00000993 cmpl         $1702195828, %ecx
	0x0f, 0x85, 0x66, 0x03, 0x00, 0x00, //0x00000999 jne          LBB0_157
	0x49, 0x83, 0xc7, 0x04, //0x0000099f addq         $4, %r15
	0xba, 0x03, 0x00, 0x00, 0x00, //0x000009a3 movl         $3, %edx
	0xe9, 0x69, 0x06, 0x00, 0x00, //0x000009a8 jmp          LBB0_216
	//0x000009ad LBB0_124
	0x49, 0xc7, 0x45, 0x00, 0x06, 0x00, 0x00, 0x00, //0x000009ad movq         $6, (%r13)
	0xe9, 0x35, 0xf9, 0xff, 0xff, //0x000009b5 jmp          LBB0_38
	//0x000009ba LBB0_125
	0x31, 0xc9, //0x000009ba xorl         %ecx, %ecx
	0x45, 0x85, 0xc0, //0x000009bc testl        %r8d, %r8d
	0x0f, 0x99, 0xc1, //0x000009bf setns        %cl
	0xb8, 0x0d, 0x00, 0x00, 0x00, //0x000009c2 movl         $13, %eax
	//0x000009c7 LBB0_126
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x000009c7 movq         $-2, %rdx
	0x48, 0x0f, 0x48, 0xd0, //0x000009ce cmovsq       %rax, %rdx
	0x49, 0x89, 0x55, 0x00, //0x000009d2 movq         %rdx, (%r13)
	0x49, 0x29, 0xcb, //0x000009d6 subq         %rcx, %r11
	0xe9, 0x11, 0xf9, 0xff, 0xff, //0x000009d9 jmp          LBB0_38
	//0x000009de LBB0_127
	0x49, 0xf7, 0xda, //0x000009de negq         %r10
	0x4d, 0x89, 0xd3, //0x000009e1 movq         %r10, %r11
	//0x000009e4 LBB0_128
	0x4d, 0x85, 0xdb, //0x000009e4 testq        %r11, %r11
	0x0f, 0x88, 0x4b, 0x06, 0x00, 0x00, //0x000009e7 js           LBB0_219
	//0x000009ed LBB0_129
	0x4c, 0x01, 0xdb, //0x000009ed addq         %r11, %rbx
	0x49, 0x89, 0xdb, //0x000009f0 movq         %rbx, %r11
	0x4c, 0x2b, 0x5d, 0xc0, //0x000009f3 subq         $-64(%rbp), %r11
	0x48, 0x83, 0x7d, 0xb8, 0x00, //0x000009f7 cmpq         $0, $-72(%rbp)
	0x0f, 0x8e, 0x11, 0x00, 0x00, 0x00, //0x000009fc jle          LBB0_131
	0x49, 0xc7, 0x45, 0x00, 0x08, 0x00, 0x00, 0x00, //0x00000a02 movq         $8, (%r13)
	0x4d, 0x89, 0x7d, 0x18, //0x00000a0a movq         %r15, $24(%r13)
	0xe9, 0xdc, 0xf8, 0xff, 0xff, //0x00000a0e jmp          LBB0_38
	//0x00000a13 LBB0_131
	0x4d, 0x89, 0x7d, 0x00, //0x00000a13 movq         %r15, (%r13)
	0xe9, 0xd3, 0xf8, 0xff, 0xff, //0x00000a17 jmp          LBB0_38
	//0x00000a1c LBB0_132
	0x49, 0x39, 0xf3, //0x00000a1c cmpq         %rsi, %r11
	0x0f, 0x84, 0x9a, 0x28, 0x00, 0x00, //0x00000a1f je           LBB0_733
	0x49, 0x89, 0xf4, //0x00000a25 movq         %rsi, %r12
	0x4d, 0x29, 0xdc, //0x00000a28 subq         %r11, %r12
	0x4e, 0x8d, 0x2c, 0x18, //0x00000a2b leaq         (%rax,%r11), %r13
	0x49, 0x83, 0xfc, 0x40, //0x00000a2f cmpq         $64, %r12
	0x0f, 0x82, 0xb0, 0x25, 0x00, 0x00, //0x00000a33 jb           LBB0_693
	0x48, 0x89, 0xc1, //0x00000a39 movq         %rax, %rcx
	0x45, 0x89, 0xe6, //0x00000a3c movl         %r12d, %r14d
	0x41, 0x83, 0xe6, 0x3f, //0x00000a3f andl         $63, %r14d
	0x48, 0x8d, 0x44, 0x16, 0xc0, //0x00000a43 leaq         $-64(%rsi,%rdx), %rax
	0x48, 0x83, 0xe0, 0xc0, //0x00000a48 andq         $-64, %rax
	0x49, 0x01, 0xc7, //0x00000a4c addq         %rax, %r15
	0x4e, 0x8d, 0x4c, 0x39, 0x41, //0x00000a4f leaq         $65(%rcx,%r15), %r9
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000a54 movq         $-1, %r8
	0x45, 0x31, 0xff, //0x00000a5b xorl         %r15d, %r15d
	0xf3, 0x44, 0x0f, 0x6f, 0x05, 0x99, 0xf5, 0xff, 0xff, //0x00000a5e movdqu       $-2663(%rip), %xmm8  /* LCPI0_0+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0xa1, 0xf5, 0xff, 0xff, //0x00000a67 movdqu       $-2655(%rip), %xmm1  /* LCPI0_1+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x15, 0xa9, 0xf5, 0xff, 0xff, //0x00000a6f movdqu       $-2647(%rip), %xmm2  /* LCPI0_2+0(%rip) */
	0x66, 0x0f, 0x76, 0xdb, //0x00000a77 pcmpeqd      %xmm3, %xmm3
	0x90, 0x90, 0x90, 0x90, 0x90, //0x00000a7b .p2align 4, 0x90
	//0x00000a80 LBB0_135
	0xf3, 0x41, 0x0f, 0x6f, 0x7d, 0x00, //0x00000a80 movdqu       (%r13), %xmm7
	0xf3, 0x41, 0x0f, 0x6f, 0x75, 0x10, //0x00000a86 movdqu       $16(%r13), %xmm6
	0xf3, 0x41, 0x0f, 0x6f, 0x6d, 0x20, //0x00000a8c movdqu       $32(%r13), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x65, 0x30, //0x00000a92 movdqu       $48(%r13), %xmm4
	0x66, 0x0f, 0x6f, 0xc7, //0x00000a98 movdqa       %xmm7, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc0, //0x00000a9c pcmpeqb      %xmm8, %xmm0
	0x66, 0x0f, 0xd7, 0xf8, //0x00000aa1 pmovmskb     %xmm0, %edi
	0x66, 0x0f, 0x6f, 0xc6, //0x00000aa5 movdqa       %xmm6, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc0, //0x00000aa9 pcmpeqb      %xmm8, %xmm0
	0x66, 0x0f, 0xd7, 0xd8, //0x00000aae pmovmskb     %xmm0, %ebx
	0x66, 0x0f, 0x6f, 0xc5, //0x00000ab2 movdqa       %xmm5, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc0, //0x00000ab6 pcmpeqb      %xmm8, %xmm0
	0x66, 0x0f, 0xd7, 0xc8, //0x00000abb pmovmskb     %xmm0, %ecx
	0x66, 0x0f, 0x6f, 0xc4, //0x00000abf movdqa       %xmm4, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc0, //0x00000ac3 pcmpeqb      %xmm8, %xmm0
	0x66, 0x44, 0x0f, 0xd7, 0xd8, //0x00000ac8 pmovmskb     %xmm0, %r11d
	0x66, 0x0f, 0x6f, 0xc7, //0x00000acd movdqa       %xmm7, %xmm0
	0x66, 0x0f, 0x74, 0xc1, //0x00000ad1 pcmpeqb      %xmm1, %xmm0
	0x66, 0x0f, 0xd7, 0xc0, //0x00000ad5 pmovmskb     %xmm0, %eax
	0x66, 0x0f, 0x6f, 0xc6, //0x00000ad9 movdqa       %xmm6, %xmm0
	0x66, 0x0f, 0x74, 0xc1, //0x00000add pcmpeqb      %xmm1, %xmm0
	0x66, 0x0f, 0xd7, 0xd0, //0x00000ae1 pmovmskb     %xmm0, %edx
	0x66, 0x0f, 0x6f, 0xc5, //0x00000ae5 movdqa       %xmm5, %xmm0
	0x66, 0x0f, 0x74, 0xc1, //0x00000ae9 pcmpeqb      %xmm1, %xmm0
	0x48, 0xc1, 0xe3, 0x10, //0x00000aed shlq         $16, %rbx
	0x48, 0x09, 0xdf, //0x00000af1 orq          %rbx, %rdi
	0x66, 0x0f, 0xd7, 0xd8, //0x00000af4 pmovmskb     %xmm0, %ebx
	0x66, 0x0f, 0x6f, 0xc4, //0x00000af8 movdqa       %xmm4, %xmm0
	0x66, 0x0f, 0x74, 0xc1, //0x00000afc pcmpeqb      %xmm1, %xmm0
	0x48, 0xc1, 0xe1, 0x20, //0x00000b00 shlq         $32, %rcx
	0x48, 0x09, 0xcf, //0x00000b04 orq          %rcx, %rdi
	0x66, 0x0f, 0xd7, 0xc8, //0x00000b07 pmovmskb     %xmm0, %ecx
	0x66, 0x0f, 0x6f, 0xc2, //0x00000b0b movdqa       %xmm2, %xmm0
	0x66, 0x0f, 0x64, 0xc7, //0x00000b0f pcmpgtb      %xmm7, %xmm0
	0x66, 0x0f, 0x64, 0xfb, //0x00000b13 pcmpgtb      %xmm3, %xmm7
	0x66, 0x0f, 0xdb, 0xf8, //0x00000b17 pand         %xmm0, %xmm7
	0x48, 0xc1, 0xe2, 0x10, //0x00000b1b shlq         $16, %rdx
	0x48, 0x09, 0xd0, //0x00000b1f orq          %rdx, %rax
	0x66, 0x44, 0x0f, 0xd7, 0xd7, //0x00000b22 pmovmskb     %xmm7, %r10d
	0x66, 0x0f, 0x6f, 0xc2, //0x00000b27 movdqa       %xmm2, %xmm0
	0x66, 0x0f, 0x64, 0xc6, //0x00000b2b pcmpgtb      %xmm6, %xmm0
	0x66, 0x0f, 0x64, 0xf3, //0x00000b2f pcmpgtb      %xmm3, %xmm6
	0x66, 0x0f, 0xdb, 0xf0, //0x00000b33 pand         %xmm0, %xmm6
	0x48, 0xc1, 0xe3, 0x20, //0x00000b37 shlq         $32, %rbx
	0x48, 0x09, 0xd8, //0x00000b3b orq          %rbx, %rax
	0x66, 0x0f, 0xd7, 0xd6, //0x00000b3e pmovmskb     %xmm6, %edx
	0x66, 0x0f, 0x6f, 0xc2, //0x00000b42 movdqa       %xmm2, %xmm0
	0x66, 0x0f, 0x64, 0xc5, //0x00000b46 pcmpgtb      %xmm5, %xmm0
	0x66, 0x0f, 0x64, 0xeb, //0x00000b4a pcmpgtb      %xmm3, %xmm5
	0x66, 0x0f, 0xdb, 0xe8, //0x00000b4e pand         %xmm0, %xmm5
	0x48, 0xc1, 0xe1, 0x30, //0x00000b52 shlq         $48, %rcx
	0x48, 0x09, 0xc8, //0x00000b56 orq          %rcx, %rax
	0x66, 0x0f, 0xd7, 0xcd, //0x00000b59 pmovmskb     %xmm5, %ecx
	0x66, 0x0f, 0x6f, 0xc2, //0x00000b5d movdqa       %xmm2, %xmm0
	0x66, 0x0f, 0x64, 0xc4, //0x00000b61 pcmpgtb      %xmm4, %xmm0
	0x66, 0x0f, 0x64, 0xe3, //0x00000b65 pcmpgtb      %xmm3, %xmm4
	0x66, 0x0f, 0xdb, 0xe0, //0x00000b69 pand         %xmm0, %xmm4
	0x48, 0xc1, 0xe2, 0x10, //0x00000b6d shlq         $16, %rdx
	0x49, 0x09, 0xd2, //0x00000b71 orq          %rdx, %r10
	0x66, 0x0f, 0xd7, 0xdc, //0x00000b74 pmovmskb     %xmm4, %ebx
	0x49, 0xc1, 0xe3, 0x30, //0x00000b78 shlq         $48, %r11
	0x48, 0xc1, 0xe1, 0x20, //0x00000b7c shlq         $32, %rcx
	0x49, 0x83, 0xf8, 0xff, //0x00000b80 cmpq         $-1, %r8
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00000b84 jne          LBB0_137
	0x48, 0x85, 0xc0, //0x00000b8a testq        %rax, %rax
	0x0f, 0x85, 0x9c, 0x00, 0x00, 0x00, //0x00000b8d jne          LBB0_142
	//0x00000b93 LBB0_137
	0x48, 0xc1, 0xe3, 0x30, //0x00000b93 shlq         $48, %rbx
	0x49, 0x09, 0xca, //0x00000b97 orq          %rcx, %r10
	0x4c, 0x09, 0xdf, //0x00000b9a orq          %r11, %rdi
	0x48, 0x89, 0xc1, //0x00000b9d movq         %rax, %rcx
	0x4c, 0x09, 0xf9, //0x00000ba0 orq          %r15, %rcx
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x00000ba3 jne          LBB0_141
	0x49, 0x09, 0xda, //0x00000ba9 orq          %rbx, %r10
	0x48, 0x85, 0xff, //0x00000bac testq        %rdi, %rdi
	0x0f, 0x85, 0x8d, 0x00, 0x00, 0x00, //0x00000baf jne          LBB0_143
	//0x00000bb5 LBB0_139
	0x4d, 0x85, 0xd2, //0x00000bb5 testq        %r10, %r10
	0x0f, 0x85, 0x63, 0x25, 0x00, 0x00, //0x00000bb8 jne          LBB0_712
	0x49, 0x83, 0xc4, 0xc0, //0x00000bbe addq         $-64, %r12
	0x49, 0x83, 0xc5, 0x40, //0x00000bc2 addq         $64, %r13
	0x49, 0x83, 0xfc, 0x3f, //0x00000bc6 cmpq         $63, %r12
	0x0f, 0x87, 0xb0, 0xfe, 0xff, 0xff, //0x00000bca ja           LBB0_135
	0xe9, 0xd1, 0x05, 0x00, 0x00, //0x00000bd0 jmp          LBB0_239
	//0x00000bd5 LBB0_141
	0x4c, 0x89, 0xf9, //0x00000bd5 movq         %r15, %rcx
	0x48, 0xf7, 0xd1, //0x00000bd8 notq         %rcx
	0x48, 0x21, 0xc1, //0x00000bdb andq         %rax, %rcx
	0x4c, 0x8d, 0x1c, 0x09, //0x00000bde leaq         (%rcx,%rcx), %r11
	0x4d, 0x09, 0xfb, //0x00000be2 orq          %r15, %r11
	0x4c, 0x89, 0xda, //0x00000be5 movq         %r11, %rdx
	0x48, 0xf7, 0xd2, //0x00000be8 notq         %rdx
	0x48, 0x21, 0xc2, //0x00000beb andq         %rax, %rdx
	0x48, 0xb8, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000bee movabsq      $-6148914691236517206, %rax
	0x48, 0x21, 0xc2, //0x00000bf8 andq         %rax, %rdx
	0x45, 0x31, 0xff, //0x00000bfb xorl         %r15d, %r15d
	0x48, 0x01, 0xca, //0x00000bfe addq         %rcx, %rdx
	0x41, 0x0f, 0x92, 0xc7, //0x00000c01 setb         %r15b
	0x48, 0x01, 0xd2, //0x00000c05 addq         %rdx, %rdx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000c08 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc2, //0x00000c12 xorq         %rax, %rdx
	0x4c, 0x21, 0xda, //0x00000c15 andq         %r11, %rdx
	0x48, 0xf7, 0xd2, //0x00000c18 notq         %rdx
	0x48, 0x21, 0xd7, //0x00000c1b andq         %rdx, %rdi
	0x49, 0x09, 0xda, //0x00000c1e orq          %rbx, %r10
	0x48, 0x85, 0xff, //0x00000c21 testq        %rdi, %rdi
	0x0f, 0x84, 0x8b, 0xff, 0xff, 0xff, //0x00000c24 je           LBB0_139
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x00000c2a jmp          LBB0_143
	//0x00000c2f LBB0_142
	0x4c, 0x89, 0xea, //0x00000c2f movq         %r13, %rdx
	0x48, 0x2b, 0x55, 0xc0, //0x00000c32 subq         $-64(%rbp), %rdx
	0x4c, 0x0f, 0xbc, 0xc0, //0x00000c36 bsfq         %rax, %r8
	0x49, 0x01, 0xd0, //0x00000c3a addq         %rdx, %r8
	0xe9, 0x51, 0xff, 0xff, 0xff, //0x00000c3d jmp          LBB0_137
	//0x00000c42 LBB0_143
	0x48, 0x0f, 0xbc, 0xc7, //0x00000c42 bsfq         %rdi, %rax
	0x4d, 0x85, 0xd2, //0x00000c46 testq        %r10, %r10
	0x0f, 0x84, 0x13, 0x01, 0x00, 0x00, //0x00000c49 je           LBB0_162
	0x49, 0x0f, 0xbc, 0xca, //0x00000c4f bsfq         %r10, %rcx
	0xe9, 0x0f, 0x01, 0x00, 0x00, //0x00000c53 jmp          LBB0_163
	//0x00000c58 LBB0_145
	0x4c, 0x89, 0xf0, //0x00000c58 movq         %r14, %rax
	0x4c, 0x09, 0xe0, //0x00000c5b orq          %r12, %rax
	0x4d, 0x39, 0xe6, //0x00000c5e cmpq         %r12, %r14
	0x0f, 0x8c, 0xdd, 0x00, 0x00, 0x00, //0x00000c61 jl           LBB0_161
	0x48, 0x85, 0xc0, //0x00000c67 testq        %rax, %rax
	0x0f, 0x88, 0xd4, 0x00, 0x00, 0x00, //0x00000c6a js           LBB0_161
	0x49, 0xf7, 0xd6, //0x00000c70 notq         %r14
	0x4d, 0x89, 0xf3, //0x00000c73 movq         %r14, %r11
	0xe9, 0x69, 0xfd, 0xff, 0xff, //0x00000c76 jmp          LBB0_128
	//0x00000c7b LBB0_148
	0x31, 0xc9, //0x00000c7b xorl         %ecx, %ecx
	0x31, 0xd2, //0x00000c7d xorl         %edx, %edx
	0x45, 0x31, 0xd2, //0x00000c7f xorl         %r10d, %r10d
	0xe9, 0xf8, 0x00, 0x00, 0x00, //0x00000c82 jmp          LBB0_166
	//0x00000c87 LBB0_149
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x00000c87 movq         $-2, %rdx
	0x80, 0xf9, 0x61, //0x00000c8e cmpb         $97, %cl
	0x0f, 0x85, 0x57, 0x01, 0x00, 0x00, //0x00000c91 jne          LBB0_175
	0x42, 0x80, 0x7c, 0x38, 0x02, 0x6c, //0x00000c97 cmpb         $108, $2(%rax,%r15)
	0x0f, 0x85, 0x5b, 0x01, 0x00, 0x00, //0x00000c9d jne          LBB0_215
	0x42, 0x80, 0x7c, 0x38, 0x03, 0x73, //0x00000ca3 cmpb         $115, $3(%rax,%r15)
	0x0f, 0x85, 0x63, 0x03, 0x00, 0x00, //0x00000ca9 jne          LBB0_213
	0x49, 0x8d, 0x77, 0x04, //0x00000caf leaq         $4(%r15), %rsi
	0x49, 0x8d, 0x4f, 0x05, //0x00000cb3 leaq         $5(%r15), %rcx
	0x42, 0x80, 0x7c, 0x38, 0x04, 0x65, //0x00000cb7 cmpb         $101, $4(%rax,%r15)
	0x48, 0x0f, 0x44, 0xf1, //0x00000cbd cmoveq       %rcx, %rsi
	0xe9, 0x53, 0x03, 0x00, 0x00, //0x00000cc1 jmp          LBB0_217
	//0x00000cc6 LBB0_153
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x00000cc6 movq         $-2, %rdx
	0x80, 0xf9, 0x6e, //0x00000ccd cmpb         $110, %cl
	0x0f, 0x85, 0x40, 0x03, 0x00, 0x00, //0x00000cd0 jne          LBB0_216
	0x42, 0x80, 0x7c, 0x38, 0x01, 0x75, //0x00000cd6 cmpb         $117, $1(%rax,%r15)
	0x0f, 0x85, 0x14, 0x01, 0x00, 0x00, //0x00000cdc jne          LBB0_176
	0x42, 0x80, 0x7c, 0x38, 0x02, 0x6c, //0x00000ce2 cmpb         $108, $2(%rax,%r15)
	0x0f, 0x85, 0x10, 0x01, 0x00, 0x00, //0x00000ce8 jne          LBB0_215
	0x49, 0x8d, 0x77, 0x03, //0x00000cee leaq         $3(%r15), %rsi
	0x49, 0x8d, 0x4f, 0x04, //0x00000cf2 leaq         $4(%r15), %rcx
	0x42, 0x80, 0x7c, 0x38, 0x03, 0x6c, //0x00000cf6 cmpb         $108, $3(%rax,%r15)
	0x48, 0x0f, 0x44, 0xf1, //0x00000cfc cmoveq       %rcx, %rsi
	0xe9, 0x14, 0x03, 0x00, 0x00, //0x00000d00 jmp          LBB0_217
	//0x00000d05 LBB0_157
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x00000d05 movq         $-2, %rdx
	0x80, 0xf9, 0x74, //0x00000d0c cmpb         $116, %cl
	0x0f, 0x85, 0x01, 0x03, 0x00, 0x00, //0x00000d0f jne          LBB0_216
	0x42, 0x80, 0x7c, 0x38, 0x01, 0x72, //0x00000d15 cmpb         $114, $1(%rax,%r15)
	0x0f, 0x85, 0xd5, 0x00, 0x00, 0x00, //0x00000d1b jne          LBB0_176
	0x42, 0x80, 0x7c, 0x38, 0x02, 0x75, //0x00000d21 cmpb         $117, $2(%rax,%r15)
	0x0f, 0x85, 0xd1, 0x00, 0x00, 0x00, //0x00000d27 jne          LBB0_215
	0x49, 0x8d, 0x77, 0x03, //0x00000d2d leaq         $3(%r15), %rsi
	0x49, 0x8d, 0x4f, 0x04, //0x00000d31 leaq         $4(%r15), %rcx
	0x42, 0x80, 0x7c, 0x38, 0x03, 0x65, //0x00000d35 cmpb         $101, $3(%rax,%r15)
	0x48, 0x0f, 0x44, 0xf1, //0x00000d3b cmoveq       %rcx, %rsi
	0xe9, 0xd5, 0x02, 0x00, 0x00, //0x00000d3f jmp          LBB0_217
	//0x00000d44 LBB0_161
	0x48, 0x85, 0xc0, //0x00000d44 testq        %rax, %rax
	0x49, 0x8d, 0x44, 0x24, 0xff, //0x00000d47 leaq         $-1(%r12), %rax
	0x49, 0xf7, 0xd4, //0x00000d4c notq         %r12
	0x4d, 0x0f, 0x48, 0xe2, //0x00000d4f cmovsq       %r10, %r12
	0x49, 0x39, 0xc6, //0x00000d53 cmpq         %rax, %r14
	0x4d, 0x0f, 0x45, 0xe2, //0x00000d56 cmovneq      %r10, %r12
	0x4d, 0x89, 0xe3, //0x00000d5a movq         %r12, %r11
	0xe9, 0x82, 0xfc, 0xff, 0xff, //0x00000d5d jmp          LBB0_128
	//0x00000d62 LBB0_162
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00000d62 movl         $64, %ecx
	//0x00000d67 LBB0_163
	0x48, 0x8b, 0x55, 0xc0, //0x00000d67 movq         $-64(%rbp), %rdx
	0x48, 0x39, 0xc1, //0x00000d6b cmpq         %rax, %rcx
	0x0f, 0x82, 0xad, 0x23, 0x00, 0x00, //0x00000d6e jb           LBB0_712
	0x49, 0x29, 0xd5, //0x00000d74 subq         %rdx, %r13
	0xe9, 0xf2, 0x03, 0x00, 0x00, //0x00000d77 jmp          LBB0_236
	//0x00000d7c LBB0_165
	0x48, 0x89, 0xf7, //0x00000d7c movq         %rsi, %rdi
	//0x00000d7f LBB0_166
	0x45, 0x31, 0xf6, //0x00000d7f xorl         %r14d, %r14d
	0x85, 0xc9, //0x00000d82 testl        %ecx, %ecx
	0x41, 0x0f, 0x9f, 0xc6, //0x00000d84 setg         %r14b
	0x41, 0xb9, 0x09, 0x00, 0x00, 0x00, //0x00000d88 movl         $9, %r9d
	0x49, 0x89, 0xfb, //0x00000d8e movq         %rdi, %r11
	//0x00000d91 LBB0_167
	0x85, 0xc9, //0x00000d91 testl        %ecx, %ecx
	0x48, 0x89, 0x45, 0xc0, //0x00000d93 movq         %rax, $-64(%rbp)
	0x0f, 0x85, 0xa8, 0x00, 0x00, 0x00, //0x00000d97 jne          LBB0_181
	//0x00000d9d LBB0_168
	0x4d, 0x85, 0xd2, //0x00000d9d testq        %r10, %r10
	0x0f, 0x85, 0x9f, 0x00, 0x00, 0x00, //0x00000da0 jne          LBB0_181
	0x49, 0x39, 0xf3, //0x00000da6 cmpq         %rsi, %r11
	0x0f, 0x83, 0x33, 0x00, 0x00, 0x00, //0x00000da9 jae          LBB0_174
	0x44, 0x89, 0x75, 0xb8, //0x00000daf movl         %r14d, $-72(%rbp)
	0x45, 0x89, 0xde, //0x00000db3 movl         %r11d, %r14d
	0x41, 0x29, 0xf6, //0x00000db6 subl         %esi, %r14d
	0x31, 0xd2, //0x00000db9 xorl         %edx, %edx
	0x31, 0xc9, //0x00000dbb xorl         %ecx, %ecx
	0x48, 0x8b, 0x45, 0xc0, //0x00000dbd movq         $-64(%rbp), %rax
	//0x00000dc1 LBB0_171
	0x42, 0x80, 0x3c, 0x18, 0x30, //0x00000dc1 cmpb         $48, (%rax,%r11)
	0x0f, 0x85, 0x6e, 0x00, 0x00, 0x00, //0x00000dc6 jne          LBB0_180
	0x49, 0xff, 0xc3, //0x00000dcc incq         %r11
	0xff, 0xc9, //0x00000dcf decl         %ecx
	0x4c, 0x39, 0xde, //0x00000dd1 cmpq         %r11, %rsi
	0x0f, 0x85, 0xe7, 0xff, 0xff, 0xff, //0x00000dd4 jne          LBB0_171
	0x45, 0x31, 0xd2, //0x00000dda xorl         %r10d, %r10d
	0xe9, 0x70, 0x01, 0x00, 0x00, //0x00000ddd jmp          LBB0_201
	//0x00000de2 LBB0_174
	0x31, 0xc9, //0x00000de2 xorl         %ecx, %ecx
	0x31, 0xd2, //0x00000de4 xorl         %edx, %edx
	0x45, 0x31, 0xd2, //0x00000de6 xorl         %r10d, %r10d
	0xe9, 0x57, 0x00, 0x00, 0x00, //0x00000de9 jmp          LBB0_181
	//0x00000dee LBB0_175
	0x4c, 0x89, 0xde, //0x00000dee movq         %r11, %rsi
	0xe9, 0x23, 0x02, 0x00, 0x00, //0x00000df1 jmp          LBB0_217
	//0x00000df6 LBB0_176
	0x49, 0xff, 0xc7, //0x00000df6 incq         %r15
	0xe9, 0x18, 0x02, 0x00, 0x00, //0x00000df9 jmp          LBB0_216
	//0x00000dfe LBB0_215
	0x49, 0x83, 0xc7, 0x02, //0x00000dfe addq         $2, %r15
	0xe9, 0x0f, 0x02, 0x00, 0x00, //0x00000e02 jmp          LBB0_216
	//0x00000e07 LBB0_177
	0x4c, 0x89, 0xc3, //0x00000e07 movq         %r8, %rbx
	0x4c, 0x29, 0xc7, //0x00000e0a subq         %r8, %rdi
	0x44, 0x0f, 0xbc, 0xda, //0x00000e0d bsfl         %edx, %r11d
	0x49, 0x01, 0xfb, //0x00000e11 addq         %rdi, %r11
	//0x00000e14 LBB0_178
	0x49, 0xf7, 0xd3, //0x00000e14 notq         %r11
	0x4c, 0x8b, 0x6d, 0xd0, //0x00000e17 movq         $-48(%rbp), %r13
	0xe9, 0xc4, 0xfb, 0xff, 0xff, //0x00000e1b jmp          LBB0_128
	//0x00000e20 LBB0_179
	0x49, 0xff, 0xcb, //0x00000e20 decq         %r11
	0x41, 0xb9, 0x09, 0x00, 0x00, 0x00, //0x00000e23 movl         $9, %r9d
	0x85, 0xc9, //0x00000e29 testl        %ecx, %ecx
	0x48, 0x89, 0x45, 0xc0, //0x00000e2b movq         %rax, $-64(%rbp)
	0x0f, 0x84, 0x68, 0xff, 0xff, 0xff, //0x00000e2f je           LBB0_168
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x00000e35 jmp          LBB0_181
	//0x00000e3a LBB0_180
	0x45, 0x31, 0xd2, //0x00000e3a xorl         %r10d, %r10d
	0x48, 0x8b, 0x45, 0xc0, //0x00000e3d movq         $-64(%rbp), %rax
	0x44, 0x8b, 0x75, 0xb8, //0x00000e41 movl         $-72(%rbp), %r14d
	//0x00000e45 LBB0_181
	0x49, 0x39, 0xf3, //0x00000e45 cmpq         %rsi, %r11
	0x0f, 0x83, 0x3c, 0x00, 0x00, 0x00, //0x00000e48 jae          LBB0_186
	0x83, 0xfa, 0x12, //0x00000e4e cmpl         $18, %edx
	0x0f, 0x8f, 0x33, 0x00, 0x00, 0x00, //0x00000e51 jg           LBB0_186
	//0x00000e57 LBB0_183
	0x42, 0x0f, 0xb6, 0x3c, 0x18, //0x00000e57 movzbl       (%rax,%r11), %edi
	0x8d, 0x5f, 0xd0, //0x00000e5c leal         $-48(%rdi), %ebx
	0x80, 0xfb, 0x09, //0x00000e5f cmpb         $9, %bl
	0x0f, 0x87, 0x22, 0x00, 0x00, 0x00, //0x00000e62 ja           LBB0_186
	0x4b, 0x8d, 0x1c, 0x92, //0x00000e68 leaq         (%r10,%r10,4), %rbx
	0x4c, 0x8d, 0x54, 0x5f, 0xd0, //0x00000e6c leaq         $-48(%rdi,%rbx,2), %r10
	0xff, 0xc9, //0x00000e71 decl         %ecx
	0x49, 0xff, 0xc3, //0x00000e73 incq         %r11
	0x83, 0xfa, 0x11, //0x00000e76 cmpl         $17, %edx
	0x0f, 0x8f, 0x0b, 0x00, 0x00, 0x00, //0x00000e79 jg           LBB0_186
	0xff, 0xc2, //0x00000e7f incl         %edx
	0x49, 0x39, 0xf3, //0x00000e81 cmpq         %rsi, %r11
	0x0f, 0x82, 0xcd, 0xff, 0xff, 0xff, //0x00000e84 jb           LBB0_183
	//0x00000e8a LBB0_186
	0x49, 0x39, 0xf3, //0x00000e8a cmpq         %rsi, %r11
	0x0f, 0x83, 0xa9, 0x00, 0x00, 0x00, //0x00000e8d jae          LBB0_199
	0x42, 0x8a, 0x14, 0x18, //0x00000e93 movb         (%rax,%r11), %dl
	0x8d, 0x7a, 0xd0, //0x00000e97 leal         $-48(%rdx), %edi
	0x40, 0x80, 0xff, 0x09, //0x00000e9a cmpb         $9, %dil
	0x0f, 0x87, 0x28, 0x00, 0x00, 0x00, //0x00000e9e ja           LBB0_192
	0x48, 0x8d, 0x7e, 0xff, //0x00000ea4 leaq         $-1(%rsi), %rdi
	//0x00000ea8 LBB0_189
	0x4c, 0x39, 0xdf, //0x00000ea8 cmpq         %r11, %rdi
	0x0f, 0x84, 0x97, 0x00, 0x00, 0x00, //0x00000eab je           LBB0_200
	0x42, 0x0f, 0xb6, 0x54, 0x18, 0x01, //0x00000eb1 movzbl       $1(%rax,%r11), %edx
	0x49, 0xff, 0xc3, //0x00000eb7 incq         %r11
	0x8d, 0x5a, 0xd0, //0x00000eba leal         $-48(%rdx), %ebx
	0x80, 0xfb, 0x09, //0x00000ebd cmpb         $9, %bl
	0x0f, 0x86, 0xe2, 0xff, 0xff, 0xff, //0x00000ec0 jbe          LBB0_189
	0x41, 0xbe, 0x01, 0x00, 0x00, 0x00, //0x00000ec6 movl         $1, %r14d
	//0x00000ecc LBB0_192
	0x80, 0xca, 0x20, //0x00000ecc orb          $32, %dl
	0x80, 0xfa, 0x65, //0x00000ecf cmpb         $101, %dl
	0x0f, 0x85, 0x64, 0x00, 0x00, 0x00, //0x00000ed2 jne          LBB0_199
	0x49, 0x8d, 0x7b, 0x01, //0x00000ed8 leaq         $1(%r11), %rdi
	0x49, 0xc7, 0x45, 0x00, 0x08, 0x00, 0x00, 0x00, //0x00000edc movq         $8, (%r13)
	0x48, 0x39, 0xf7, //0x00000ee4 cmpq         %rsi, %rdi
	0x0f, 0x83, 0x3f, 0x00, 0x00, 0x00, //0x00000ee7 jae          LBB0_198
	0x8a, 0x14, 0x38, //0x00000eed movb         (%rax,%rdi), %dl
	0x80, 0xfa, 0x2d, //0x00000ef0 cmpb         $45, %dl
	0x0f, 0x84, 0x0f, 0x00, 0x00, 0x00, //0x00000ef3 je           LBB0_196
	0x41, 0xb9, 0x01, 0x00, 0x00, 0x00, //0x00000ef9 movl         $1, %r9d
	0x80, 0xfa, 0x2b, //0x00000eff cmpb         $43, %dl
	0x0f, 0x85, 0x4d, 0x01, 0x00, 0x00, //0x00000f02 jne          LBB0_222
	//0x00000f08 LBB0_196
	0x49, 0x83, 0xc3, 0x02, //0x00000f08 addq         $2, %r11
	0x49, 0x39, 0xf3, //0x00000f0c cmpq         %rsi, %r11
	0x0f, 0x83, 0x17, 0x00, 0x00, 0x00, //0x00000f0f jae          LBB0_198
	0x31, 0xff, //0x00000f15 xorl         %edi, %edi
	0x80, 0xfa, 0x2b, //0x00000f17 cmpb         $43, %dl
	0x40, 0x0f, 0x94, 0xc7, //0x00000f1a sete         %dil
	0x44, 0x8d, 0x4c, 0x3f, 0xff, //0x00000f1e leal         $-1(%rdi,%rdi), %r9d
	0x42, 0x8a, 0x14, 0x18, //0x00000f23 movb         (%rax,%r11), %dl
	0xe9, 0x2c, 0x01, 0x00, 0x00, //0x00000f27 jmp          LBB0_223
	//0x00000f2c LBB0_198
	0x49, 0xc7, 0x45, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00000f2c movq         $-1, (%r13)
	0x49, 0x89, 0xf3, //0x00000f34 movq         %rsi, %r11
	0xe9, 0xb3, 0xf3, 0xff, 0xff, //0x00000f37 jmp          LBB0_38
	//0x00000f3c LBB0_199
	0x44, 0x89, 0x75, 0xb8, //0x00000f3c movl         %r14d, $-72(%rbp)
	0x41, 0x89, 0xce, //0x00000f40 movl         %ecx, %r14d
	0xe9, 0x0d, 0x00, 0x00, 0x00, //0x00000f43 jmp          LBB0_202
	//0x00000f48 LBB0_200
	0xc7, 0x45, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x00000f48 movl         $1, $-72(%rbp)
	0x41, 0x89, 0xce, //0x00000f4f movl         %ecx, %r14d
	//0x00000f52 LBB0_201
	0x49, 0x89, 0xf3, //0x00000f52 movq         %rsi, %r11
	//0x00000f55 LBB0_202
	0x41, 0x83, 0xf9, 0x09, //0x00000f55 cmpl         $9, %r9d
	0x0f, 0x85, 0x37, 0x00, 0x00, 0x00, //0x00000f59 jne          LBB0_207
	0x45, 0x85, 0xf6, //0x00000f5f testl        %r14d, %r14d
	0x0f, 0x85, 0x26, 0x00, 0x00, 0x00, //0x00000f62 jne          LBB0_206
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00000f68 movabsq      $-9223372036854775808, %rax
	0x48, 0x63, 0x4d, 0xc8, //0x00000f72 movslq       $-56(%rbp), %rcx
	0x4d, 0x85, 0xd2, //0x00000f76 testq        %r10, %r10
	0x0f, 0x89, 0x56, 0x00, 0x00, 0x00, //0x00000f79 jns          LBB0_212
	0x4c, 0x89, 0xd2, //0x00000f7f movq         %r10, %rdx
	0x48, 0x21, 0xca, //0x00000f82 andq         %rcx, %rdx
	0x48, 0x39, 0xc2, //0x00000f85 cmpq         %rax, %rdx
	0x0f, 0x84, 0x47, 0x00, 0x00, 0x00, //0x00000f88 je           LBB0_212
	//0x00000f8e LBB0_206
	0x49, 0xc7, 0x45, 0x00, 0x08, 0x00, 0x00, 0x00, //0x00000f8e movq         $8, (%r13)
	//0x00000f96 LBB0_207
	0x49, 0xb9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00000f96 movabsq      $-9223372036854775808, %r9
	0x4c, 0x89, 0xd0, //0x00000fa0 movq         %r10, %rax
	0x48, 0xc1, 0xe8, 0x34, //0x00000fa3 shrq         $52, %rax
	0x0f, 0x84, 0xe2, 0x02, 0x00, 0x00, //0x00000fa7 je           LBB0_247
	//0x00000fad LBB0_208
	0x41, 0x8d, 0x86, 0x5c, 0x01, 0x00, 0x00, //0x00000fad leal         $348(%r14), %eax
	0x3d, 0xb7, 0x02, 0x00, 0x00, //0x00000fb4 cmpl         $695, %eax
	0x0f, 0x87, 0xca, 0x04, 0x00, 0x00, //0x00000fb9 ja           LBB0_268
	0x4d, 0x85, 0xd2, //0x00000fbf testq        %r10, %r10
	0x0f, 0x84, 0xb8, 0x03, 0x00, 0x00, //0x00000fc2 je           LBB0_259
	//0x00000fc8 LBB0_210
	0x49, 0x0f, 0xbd, 0xca, //0x00000fc8 bsrq         %r10, %rcx
	0x48, 0x83, 0xf1, 0x3f, //0x00000fcc xorq         $63, %rcx
	0xe9, 0xb0, 0x03, 0x00, 0x00, //0x00000fd0 jmp          LBB0_260
	//0x00000fd5 LBB0_212
	0x66, 0x49, 0x0f, 0x6e, 0xc2, //0x00000fd5 movq         %r10, %xmm0
	0x4c, 0x0f, 0xaf, 0xd1, //0x00000fda imulq        %rcx, %r10
	0x4d, 0x89, 0x55, 0x10, //0x00000fde movq         %r10, $16(%r13)
	0x66, 0x0f, 0x62, 0x05, 0xa6, 0xf0, 0xff, 0xff, //0x00000fe2 punpckldq    $-3930(%rip), %xmm0  /* LCPI0_9+0(%rip) */
	0x66, 0x0f, 0x5c, 0x05, 0xae, 0xf0, 0xff, 0xff, //0x00000fea subpd        $-3922(%rip), %xmm0  /* LCPI0_10+0(%rip) */
	0x66, 0x0f, 0x28, 0xc8, //0x00000ff2 movapd       %xmm0, %xmm1
	0x66, 0x0f, 0x15, 0xc8, //0x00000ff6 unpckhpd     %xmm0, %xmm1
	0xf2, 0x0f, 0x58, 0xc8, //0x00000ffa addsd        %xmm0, %xmm1
	0x48, 0x21, 0xc8, //0x00000ffe andq         %rcx, %rax
	0x66, 0x48, 0x0f, 0x7e, 0xc9, //0x00001001 movq         %xmm1, %rcx
	0x48, 0x09, 0xc1, //0x00001006 orq          %rax, %rcx
	0x49, 0x89, 0x4d, 0x08, //0x00001009 movq         %rcx, $8(%r13)
	0xe9, 0xdd, 0xf2, 0xff, 0xff, //0x0000100d jmp          LBB0_38
	//0x00001012 LBB0_213
	0x49, 0x83, 0xc7, 0x03, //0x00001012 addq         $3, %r15
	//0x00001016 LBB0_216
	0x4c, 0x89, 0xfe, //0x00001016 movq         %r15, %rsi
	//0x00001019 LBB0_217
	0x49, 0x89, 0x55, 0x00, //0x00001019 movq         %rdx, (%r13)
	0x49, 0x89, 0xf3, //0x0000101d movq         %rsi, %r11
	0xe9, 0xca, 0xf2, 0xff, 0xff, //0x00001020 jmp          LBB0_38
	//0x00001025 LBB0_218
	0x49, 0x89, 0xca, //0x00001025 movq         %rcx, %r10
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001028 movq         $-1, %r11
	0x4d, 0x85, 0xe4, //0x0000102f testq        %r12, %r12
	0x0f, 0x85, 0xa1, 0xf5, 0xff, 0xff, //0x00001032 jne          LBB0_76
	//0x00001038 LBB0_219
	0x49, 0xf7, 0xd3, //0x00001038 notq         %r11
	0x4c, 0x01, 0xdb, //0x0000103b addq         %r11, %rbx
	//0x0000103e LBB0_220
	0x49, 0xc7, 0xc7, 0xfe, 0xff, 0xff, 0xff, //0x0000103e movq         $-2, %r15
	//0x00001045 LBB0_221
	0x48, 0x2b, 0x5d, 0xc0, //0x00001045 subq         $-64(%rbp), %rbx
	0x49, 0x89, 0xdb, //0x00001049 movq         %rbx, %r11
	0x4d, 0x89, 0x7d, 0x00, //0x0000104c movq         %r15, (%r13)
	0xe9, 0x9a, 0xf2, 0xff, 0xff, //0x00001050 jmp          LBB0_38
	//0x00001055 LBB0_222
	0x49, 0x89, 0xfb, //0x00001055 movq         %rdi, %r11
	//0x00001058 LBB0_223
	0x8d, 0x7a, 0xd0, //0x00001058 leal         $-48(%rdx), %edi
	0x40, 0x80, 0xff, 0x09, //0x0000105b cmpb         $9, %dil
	0x0f, 0x86, 0x0d, 0x00, 0x00, 0x00, //0x0000105f jbe          LBB0_225
	//0x00001065 LBB0_98
	0x49, 0xc7, 0x45, 0x00, 0xfe, 0xff, 0xff, 0xff, //0x00001065 movq         $-2, (%r13)
	0xe9, 0x7d, 0xf2, 0xff, 0xff, //0x0000106d jmp          LBB0_38
	//0x00001072 LBB0_225
	0x44, 0x89, 0x75, 0xb8, //0x00001072 movl         %r14d, $-72(%rbp)
	0x45, 0x31, 0xf6, //0x00001076 xorl         %r14d, %r14d
	0x49, 0x39, 0xf3, //0x00001079 cmpq         %rsi, %r11
	0x0f, 0x83, 0xef, 0x01, 0x00, 0x00, //0x0000107c jae          LBB0_246
	0x48, 0x8d, 0x7e, 0xff, //0x00001082 leaq         $-1(%rsi), %rdi
	0x45, 0x31, 0xf6, //0x00001086 xorl         %r14d, %r14d
	//0x00001089 LBB0_227
	0x44, 0x89, 0xf3, //0x00001089 movl         %r14d, %ebx
	0x41, 0x81, 0xfe, 0x10, 0x27, 0x00, 0x00, //0x0000108c cmpl         $10000, %r14d
	0x8d, 0x04, 0x9b, //0x00001093 leal         (%rbx,%rbx,4), %eax
	0x0f, 0xb6, 0xd2, //0x00001096 movzbl       %dl, %edx
	0x44, 0x8d, 0x74, 0x42, 0xd0, //0x00001099 leal         $-48(%rdx,%rax,2), %r14d
	0x44, 0x0f, 0x4d, 0xf3, //0x0000109e cmovgel      %ebx, %r14d
	0x4c, 0x39, 0xdf, //0x000010a2 cmpq         %r11, %rdi
	0x0f, 0x84, 0xc3, 0x01, 0x00, 0x00, //0x000010a5 je           LBB0_245
	0x48, 0x8b, 0x45, 0xc0, //0x000010ab movq         $-64(%rbp), %rax
	0x42, 0x0f, 0xb6, 0x54, 0x18, 0x01, //0x000010af movzbl       $1(%rax,%r11), %edx
	0x49, 0xff, 0xc3, //0x000010b5 incq         %r11
	0x8d, 0x42, 0xd0, //0x000010b8 leal         $-48(%rdx), %eax
	0x3c, 0x0a, //0x000010bb cmpb         $10, %al
	0x0f, 0x82, 0xc6, 0xff, 0xff, 0xff, //0x000010bd jb           LBB0_227
	0xe9, 0xa9, 0x01, 0x00, 0x00, //0x000010c3 jmp          LBB0_246
	//0x000010c8 LBB0_229
	0x49, 0xf7, 0xd3, //0x000010c8 notq         %r11
	0x4c, 0x8b, 0x6d, 0xd0, //0x000010cb movq         $-48(%rbp), %r13
	0x4c, 0x89, 0xc3, //0x000010cf movq         %r8, %rbx
	0xe9, 0x0d, 0xf9, 0xff, 0xff, //0x000010d2 jmp          LBB0_128
	//0x000010d7 LBB0_453
	0x49, 0xf7, 0xdb, //0x000010d7 negq         %r11
	0xe9, 0x05, 0xf9, 0xff, 0xff, //0x000010da jmp          LBB0_128
	//0x000010df LBB0_230
	0x4c, 0x8b, 0x6d, 0xa8, //0x000010df movq         $-88(%rbp), %r13
	0x4d, 0x89, 0xf4, //0x000010e3 movq         %r14, %r12
	0x49, 0x83, 0xfc, 0x20, //0x000010e6 cmpq         $32, %r12
	0x0f, 0x82, 0xb1, 0x20, 0x00, 0x00, //0x000010ea jb           LBB0_717
	//0x000010f0 LBB0_231
	0xf3, 0x41, 0x0f, 0x6f, 0x45, 0x00, //0x000010f0 movdqu       (%r13), %xmm0
	0xf3, 0x41, 0x0f, 0x6f, 0x4d, 0x10, //0x000010f6 movdqu       $16(%r13), %xmm1
	0xf3, 0x0f, 0x6f, 0x15, 0xfc, 0xee, 0xff, 0xff, //0x000010fc movdqu       $-4356(%rip), %xmm2  /* LCPI0_0+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x1d, 0x04, 0xef, 0xff, 0xff, //0x00001104 movdqu       $-4348(%rip), %xmm3  /* LCPI0_1+0(%rip) */
	0x66, 0x0f, 0x6f, 0xe0, //0x0000110c movdqa       %xmm0, %xmm4
	0x66, 0x0f, 0x74, 0xe2, //0x00001110 pcmpeqb      %xmm2, %xmm4
	0x66, 0x0f, 0xd7, 0xd4, //0x00001114 pmovmskb     %xmm4, %edx
	0x66, 0x0f, 0x74, 0xd1, //0x00001118 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xfa, //0x0000111c pmovmskb     %xmm2, %edi
	0x66, 0x0f, 0x74, 0xc3, //0x00001120 pcmpeqb      %xmm3, %xmm0
	0x66, 0x0f, 0xd7, 0xc0, //0x00001124 pmovmskb     %xmm0, %eax
	0x66, 0x0f, 0x74, 0xcb, //0x00001128 pcmpeqb      %xmm3, %xmm1
	0x66, 0x0f, 0xd7, 0xc9, //0x0000112c pmovmskb     %xmm1, %ecx
	0x48, 0xc1, 0xe7, 0x10, //0x00001130 shlq         $16, %rdi
	0x48, 0xc1, 0xe1, 0x10, //0x00001134 shlq         $16, %rcx
	0x48, 0x09, 0xc8, //0x00001138 orq          %rcx, %rax
	0x49, 0x83, 0xf8, 0xff, //0x0000113b cmpq         $-1, %r8
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x0000113f jne          LBB0_233
	0x48, 0x85, 0xc0, //0x00001145 testq        %rax, %rax
	0x0f, 0x85, 0xf2, 0x1f, 0x00, 0x00, //0x00001148 jne          LBB0_714
	//0x0000114e LBB0_233
	0x48, 0x09, 0xd7, //0x0000114e orq          %rdx, %rdi
	0x48, 0x89, 0xc1, //0x00001151 movq         %rax, %rcx
	0x4c, 0x09, 0xf9, //0x00001154 orq          %r15, %rcx
	0x0f, 0x85, 0x00, 0x20, 0x00, 0x00, //0x00001157 jne          LBB0_715
	//0x0000115d LBB0_234
	0x48, 0x85, 0xff, //0x0000115d testq        %rdi, %rdi
	0x0f, 0x84, 0x33, 0x20, 0x00, 0x00, //0x00001160 je           LBB0_716
	//0x00001166 LBB0_235
	0x48, 0x0f, 0xbc, 0xc7, //0x00001166 bsfq         %rdi, %rax
	0x4c, 0x2b, 0x6d, 0xc0, //0x0000116a subq         $-64(%rbp), %r13
	//0x0000116e LBB0_236
	0x4d, 0x8d, 0x5c, 0x05, 0x01, //0x0000116e leaq         $1(%r13,%rax), %r11
	//0x00001173 LBB0_237
	0x4d, 0x85, 0xdb, //0x00001173 testq        %r11, %r11
	0x48, 0x8b, 0x4d, 0xd0, //0x00001176 movq         $-48(%rbp), %rcx
	0x0f, 0x88, 0x34, 0x21, 0x00, 0x00, //0x0000117a js           LBB0_732
	0x48, 0x8b, 0x45, 0xb8, //0x00001180 movq         $-72(%rbp), %rax
	0x48, 0x89, 0x41, 0x10, //0x00001184 movq         %rax, $16(%rcx)
	0x48, 0xc7, 0x01, 0x07, 0x00, 0x00, 0x00, //0x00001188 movq         $7, (%rcx)
	0x4d, 0x39, 0xd8, //0x0000118f cmpq         %r11, %r8
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00001192 movq         $-1, %rax
	0x49, 0x0f, 0x4c, 0xc0, //0x00001199 cmovlq       %r8, %rax
	0x48, 0x89, 0x41, 0x18, //0x0000119d movq         %rax, $24(%rcx)
	0xe9, 0x49, 0xf1, 0xff, 0xff, //0x000011a1 jmp          LBB0_38
	//0x000011a6 LBB0_239
	0x4d, 0x89, 0xcd, //0x000011a6 movq         %r9, %r13
	0x4d, 0x89, 0xf4, //0x000011a9 movq         %r14, %r12
	0x49, 0x83, 0xfc, 0x20, //0x000011ac cmpq         $32, %r12
	0x0f, 0x82, 0xc3, 0x1e, 0x00, 0x00, //0x000011b0 jb           LBB0_701
	//0x000011b6 LBB0_240
	0xf3, 0x41, 0x0f, 0x6f, 0x45, 0x00, //0x000011b6 movdqu       (%r13), %xmm0
	0xf3, 0x41, 0x0f, 0x6f, 0x4d, 0x10, //0x000011bc movdqu       $16(%r13), %xmm1
	0xf3, 0x0f, 0x6f, 0x15, 0x36, 0xee, 0xff, 0xff, //0x000011c2 movdqu       $-4554(%rip), %xmm2  /* LCPI0_0+0(%rip) */
	0x66, 0x0f, 0x6f, 0xd8, //0x000011ca movdqa       %xmm0, %xmm3
	0x66, 0x0f, 0x74, 0xda, //0x000011ce pcmpeqb      %xmm2, %xmm3
	0x66, 0x0f, 0xd7, 0xdb, //0x000011d2 pmovmskb     %xmm3, %ebx
	0x66, 0x0f, 0x74, 0xd1, //0x000011d6 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xfa, //0x000011da pmovmskb     %xmm2, %edi
	0xf3, 0x0f, 0x6f, 0x15, 0x2a, 0xee, 0xff, 0xff, //0x000011de movdqu       $-4566(%rip), %xmm2  /* LCPI0_1+0(%rip) */
	0x66, 0x0f, 0x6f, 0xd8, //0x000011e6 movdqa       %xmm0, %xmm3
	0x66, 0x0f, 0x74, 0xda, //0x000011ea pcmpeqb      %xmm2, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x000011ee pmovmskb     %xmm3, %ecx
	0x66, 0x0f, 0x74, 0xd1, //0x000011f2 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x000011f6 pmovmskb     %xmm2, %edx
	0xf3, 0x0f, 0x6f, 0x15, 0x1e, 0xee, 0xff, 0xff, //0x000011fa movdqu       $-4578(%rip), %xmm2  /* LCPI0_2+0(%rip) */
	0x66, 0x0f, 0x6f, 0xda, //0x00001202 movdqa       %xmm2, %xmm3
	0x66, 0x0f, 0x64, 0xd8, //0x00001206 pcmpgtb      %xmm0, %xmm3
	0x66, 0x0f, 0x76, 0xe4, //0x0000120a pcmpeqd      %xmm4, %xmm4
	0x66, 0x0f, 0x64, 0xc4, //0x0000120e pcmpgtb      %xmm4, %xmm0
	0x66, 0x0f, 0xdb, 0xc3, //0x00001212 pand         %xmm3, %xmm0
	0x66, 0x0f, 0x64, 0xd1, //0x00001216 pcmpgtb      %xmm1, %xmm2
	0x66, 0x0f, 0x64, 0xcc, //0x0000121a pcmpgtb      %xmm4, %xmm1
	0x66, 0x0f, 0xdb, 0xca, //0x0000121e pand         %xmm2, %xmm1
	0x66, 0x0f, 0xd7, 0xc1, //0x00001222 pmovmskb     %xmm1, %eax
	0x48, 0xc1, 0xe7, 0x10, //0x00001226 shlq         $16, %rdi
	0x48, 0xc1, 0xe2, 0x10, //0x0000122a shlq         $16, %rdx
	0x48, 0x09, 0xd1, //0x0000122e orq          %rdx, %rcx
	0x49, 0x83, 0xf8, 0xff, //0x00001231 cmpq         $-1, %r8
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00001235 jne          LBB0_242
	0x48, 0x85, 0xc9, //0x0000123b testq        %rcx, %rcx
	0x0f, 0x85, 0xe5, 0x1f, 0x00, 0x00, //0x0000123e jne          LBB0_726
	//0x00001244 LBB0_242
	0x66, 0x44, 0x0f, 0xd7, 0xc8, //0x00001244 pmovmskb     %xmm0, %r9d
	0x48, 0x09, 0xdf, //0x00001249 orq          %rbx, %rdi
	0x48, 0x89, 0xca, //0x0000124c movq         %rcx, %rdx
	0x4c, 0x09, 0xfa, //0x0000124f orq          %r15, %rdx
	0x0f, 0x85, 0xaa, 0x1d, 0x00, 0x00, //0x00001252 jne          LBB0_694
	0x48, 0xc1, 0xe0, 0x10, //0x00001258 shlq         $16, %rax
	0x48, 0x85, 0xff, //0x0000125c testq        %rdi, %rdi
	0x0f, 0x84, 0xe0, 0x1d, 0x00, 0x00, //0x0000125f je           LBB0_695
	//0x00001265 LBB0_244
	0x48, 0x0f, 0xbc, 0xcf, //0x00001265 bsfq         %rdi, %rcx
	0xe9, 0xdc, 0x1d, 0x00, 0x00, //0x00001269 jmp          LBB0_696
	//0x0000126e LBB0_245
	0x49, 0x89, 0xf3, //0x0000126e movq         %rsi, %r11
	//0x00001271 LBB0_246
	0x45, 0x0f, 0xaf, 0xf1, //0x00001271 imull        %r9d, %r14d
	0x41, 0x01, 0xce, //0x00001275 addl         %ecx, %r14d
	0x49, 0xb9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00001278 movabsq      $-9223372036854775808, %r9
	0x4c, 0x89, 0xd0, //0x00001282 movq         %r10, %rax
	0x48, 0xc1, 0xe8, 0x34, //0x00001285 shrq         $52, %rax
	0x0f, 0x85, 0x1e, 0xfd, 0xff, 0xff, //0x00001289 jne          LBB0_208
	//0x0000128f LBB0_247
	0x66, 0x49, 0x0f, 0x6e, 0xc2, //0x0000128f movq         %r10, %xmm0
	0x66, 0x0f, 0x62, 0x05, 0xf4, 0xed, 0xff, 0xff, //0x00001294 punpckldq    $-4620(%rip), %xmm0  /* LCPI0_9+0(%rip) */
	0x66, 0x0f, 0x5c, 0x05, 0xfc, 0xed, 0xff, 0xff, //0x0000129c subpd        $-4612(%rip), %xmm0  /* LCPI0_10+0(%rip) */
	0x66, 0x0f, 0x28, 0xc8, //0x000012a4 movapd       %xmm0, %xmm1
	0x66, 0x0f, 0x15, 0xc8, //0x000012a8 unpckhpd     %xmm0, %xmm1
	0xf2, 0x0f, 0x58, 0xc8, //0x000012ac addsd        %xmm0, %xmm1
	0x66, 0x48, 0x0f, 0x7e, 0xc8, //0x000012b0 movq         %xmm1, %rax
	0x8b, 0x4d, 0xc8, //0x000012b5 movl         $-56(%rbp), %ecx
	0x89, 0xce, //0x000012b8 movl         %ecx, %esi
	0xc1, 0xee, 0x1f, //0x000012ba shrl         $31, %esi
	0x48, 0xc1, 0xe6, 0x3f, //0x000012bd shlq         $63, %rsi
	0x48, 0x09, 0xc6, //0x000012c1 orq          %rax, %rsi
	0x4d, 0x85, 0xd2, //0x000012c4 testq        %r10, %r10
	0x0f, 0x84, 0x8f, 0x05, 0x00, 0x00, //0x000012c7 je           LBB0_328
	0x45, 0x85, 0xf6, //0x000012cd testl        %r14d, %r14d
	0x0f, 0x84, 0x86, 0x05, 0x00, 0x00, //0x000012d0 je           LBB0_328
	0x66, 0x48, 0x0f, 0x6e, 0xc6, //0x000012d6 movq         %rsi, %xmm0
	0x41, 0x8d, 0x46, 0xff, //0x000012db leal         $-1(%r14), %eax
	0x83, 0xf8, 0x24, //0x000012df cmpl         $36, %eax
	0x0f, 0x87, 0x27, 0x00, 0x00, 0x00, //0x000012e2 ja           LBB0_252
	0x41, 0x83, 0xfe, 0x17, //0x000012e8 cmpl         $23, %r14d
	0x0f, 0x8c, 0x43, 0x00, 0x00, 0x00, //0x000012ec jl           LBB0_254
	0x49, 0x63, 0xc6, //0x000012f2 movslq       %r14d, %rax
	0x48, 0x8d, 0x0d, 0xc4, 0x22, 0x00, 0x00, //0x000012f5 leaq         $8900(%rip), %rcx  /* _P10_TAB+0(%rip) */
	0xf2, 0x0f, 0x59, 0x84, 0xc1, 0x50, 0xff, 0xff, 0xff, //0x000012fc mulsd        $-176(%rcx,%rax,8), %xmm0
	0xb8, 0x16, 0x00, 0x00, 0x00, //0x00001305 movl         $22, %eax
	0xe9, 0x29, 0x00, 0x00, 0x00, //0x0000130a jmp          LBB0_255
	//0x0000130f LBB0_252
	0x41, 0x83, 0xfe, 0xea, //0x0000130f cmpl         $-22, %r14d
	0x0f, 0x82, 0x94, 0xfc, 0xff, 0xff, //0x00001313 jb           LBB0_208
	0x41, 0xf7, 0xde, //0x00001319 negl         %r14d
	0x49, 0x63, 0xc6, //0x0000131c movslq       %r14d, %rax
	0x48, 0x8d, 0x0d, 0x9a, 0x22, 0x00, 0x00, //0x0000131f leaq         $8858(%rip), %rcx  /* _P10_TAB+0(%rip) */
	0xf2, 0x0f, 0x5e, 0x04, 0xc1, //0x00001326 divsd        (%rcx,%rax,8), %xmm0
	0x66, 0x48, 0x0f, 0x7e, 0xc6, //0x0000132b movq         %xmm0, %rsi
	0xe9, 0x27, 0x05, 0x00, 0x00, //0x00001330 jmp          LBB0_328
	//0x00001335 LBB0_254
	0x44, 0x89, 0xf0, //0x00001335 movl         %r14d, %eax
	//0x00001338 LBB0_255
	0x66, 0x0f, 0x2e, 0x05, 0x70, 0xed, 0xff, 0xff, //0x00001338 ucomisd      $-4752(%rip), %xmm0  /* LCPI0_11+0(%rip) */
	0x0f, 0x87, 0x2a, 0x00, 0x00, 0x00, //0x00001340 ja           LBB0_258
	0xf2, 0x0f, 0x10, 0x0d, 0x6a, 0xed, 0xff, 0xff, //0x00001346 movsd        $-4758(%rip), %xmm1  /* LCPI0_12+0(%rip) */
	0x66, 0x0f, 0x2e, 0xc8, //0x0000134e ucomisd      %xmm0, %xmm1
	0x0f, 0x87, 0x18, 0x00, 0x00, 0x00, //0x00001352 ja           LBB0_258
	0x89, 0xc0, //0x00001358 movl         %eax, %eax
	0x48, 0x8d, 0x0d, 0x5f, 0x22, 0x00, 0x00, //0x0000135a leaq         $8799(%rip), %rcx  /* _P10_TAB+0(%rip) */
	0xf2, 0x0f, 0x59, 0x04, 0xc1, //0x00001361 mulsd        (%rcx,%rax,8), %xmm0
	0x66, 0x48, 0x0f, 0x7e, 0xc6, //0x00001366 movq         %xmm0, %rsi
	0xe9, 0xec, 0x04, 0x00, 0x00, //0x0000136b jmp          LBB0_328
	//0x00001370 LBB0_258
	0x41, 0x8d, 0x86, 0x5c, 0x01, 0x00, 0x00, //0x00001370 leal         $348(%r14), %eax
	0x4d, 0x85, 0xd2, //0x00001377 testq        %r10, %r10
	0x0f, 0x85, 0x48, 0xfc, 0xff, 0xff, //0x0000137a jne          LBB0_210
	//0x00001380 LBB0_259
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00001380 movl         $64, %ecx
	//0x00001385 LBB0_260
	0x4c, 0x89, 0xd6, //0x00001385 movq         %r10, %rsi
	0x48, 0x89, 0x4d, 0xa0, //0x00001388 movq         %rcx, $-96(%rbp)
	0x48, 0xd3, 0xe6, //0x0000138c shlq         %cl, %rsi
	0x89, 0xc0, //0x0000138f movl         %eax, %eax
	0x48, 0xc1, 0xe0, 0x04, //0x00001391 shlq         $4, %rax
	0x48, 0x8d, 0x0d, 0xe4, 0x22, 0x00, 0x00, //0x00001395 leaq         $8932(%rip), %rcx  /* _POW10_M128_TAB+0(%rip) */
	0x48, 0x89, 0x45, 0xb0, //0x0000139c movq         %rax, $-80(%rbp)
	0x48, 0x8b, 0x44, 0x08, 0x08, //0x000013a0 movq         $8(%rax,%rcx), %rax
	0x48, 0x89, 0x45, 0x98, //0x000013a5 movq         %rax, $-104(%rbp)
	0x48, 0xf7, 0xe6, //0x000013a9 mulq         %rsi
	0x48, 0x89, 0xc7, //0x000013ac movq         %rax, %rdi
	0x48, 0x89, 0xd3, //0x000013af movq         %rdx, %rbx
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x000013b2 andl         $511, %edx
	0x48, 0x89, 0xf1, //0x000013b8 movq         %rsi, %rcx
	0x48, 0xf7, 0xd1, //0x000013bb notq         %rcx
	0x48, 0x39, 0xc8, //0x000013be cmpq         %rcx, %rax
	0x0f, 0x86, 0x4c, 0x00, 0x00, 0x00, //0x000013c1 jbe          LBB0_265
	0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x000013c7 cmpl         $511, %edx
	0x0f, 0x85, 0x40, 0x00, 0x00, 0x00, //0x000013cd jne          LBB0_265
	0x48, 0x89, 0xf0, //0x000013d3 movq         %rsi, %rax
	0x48, 0x8b, 0x55, 0xb0, //0x000013d6 movq         $-80(%rbp), %rdx
	0x48, 0x8d, 0x35, 0x9f, 0x22, 0x00, 0x00, //0x000013da leaq         $8863(%rip), %rsi  /* _POW10_M128_TAB+0(%rip) */
	0x48, 0xf7, 0x24, 0x32, //0x000013e1 mulq         (%rdx,%rsi)
	0x48, 0x01, 0xd7, //0x000013e5 addq         %rdx, %rdi
	0x48, 0x83, 0xd3, 0x00, //0x000013e8 adcq         $0, %rbx
	0x89, 0xda, //0x000013ec movl         %ebx, %edx
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x000013ee andl         $511, %edx
	0x48, 0x39, 0xc8, //0x000013f4 cmpq         %rcx, %rax
	0x0f, 0x86, 0x16, 0x00, 0x00, 0x00, //0x000013f7 jbe          LBB0_265
	0x48, 0x83, 0xff, 0xff, //0x000013fd cmpq         $-1, %rdi
	0x0f, 0x85, 0x0c, 0x00, 0x00, 0x00, //0x00001401 jne          LBB0_265
	0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x00001407 cmpl         $511, %edx
	0x0f, 0x84, 0x76, 0x00, 0x00, 0x00, //0x0000140d je           LBB0_268
	//0x00001413 LBB0_265
	0x48, 0x89, 0xd8, //0x00001413 movq         %rbx, %rax
	0x48, 0xc1, 0xe8, 0x3f, //0x00001416 shrq         $63, %rax
	0x8d, 0x48, 0x09, //0x0000141a leal         $9(%rax), %ecx
	0x48, 0xd3, 0xeb, //0x0000141d shrq         %cl, %rbx
	0x48, 0x09, 0xfa, //0x00001420 orq          %rdi, %rdx
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x00001423 jne          LBB0_267
	0x89, 0xd9, //0x00001429 movl         %ebx, %ecx
	0x83, 0xe1, 0x03, //0x0000142b andl         $3, %ecx
	0x83, 0xf9, 0x01, //0x0000142e cmpl         $1, %ecx
	0x0f, 0x84, 0x52, 0x00, 0x00, 0x00, //0x00001431 je           LBB0_268
	//0x00001437 LBB0_267
	0x41, 0x69, 0xce, 0x6a, 0x52, 0x03, 0x00, //0x00001437 imull        $217706, %r14d, %ecx
	0xc1, 0xf9, 0x10, //0x0000143e sarl         $16, %ecx
	0x81, 0xc1, 0x3f, 0x04, 0x00, 0x00, //0x00001441 addl         $1087, %ecx
	0x4c, 0x63, 0xf1, //0x00001447 movslq       %ecx, %r14
	0x4c, 0x89, 0xf2, //0x0000144a movq         %r14, %rdx
	0x48, 0x2b, 0x55, 0xa0, //0x0000144d subq         $-96(%rbp), %rdx
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x01, //0x00001451 movabsq      $126100789566373888, %rdi
	0x48, 0x83, 0xf0, 0x01, //0x0000145b xorq         $1, %rax
	0x48, 0x29, 0xc2, //0x0000145f subq         %rax, %rdx
	0x89, 0xd8, //0x00001462 movl         %ebx, %eax
	0x83, 0xe0, 0x01, //0x00001464 andl         $1, %eax
	0x48, 0x01, 0xd8, //0x00001467 addq         %rbx, %rax
	0x48, 0x89, 0xc1, //0x0000146a movq         %rax, %rcx
	0x48, 0x21, 0xf9, //0x0000146d andq         %rdi, %rcx
	0x48, 0x83, 0xf9, 0x01, //0x00001470 cmpq         $1, %rcx
	0x48, 0x83, 0xda, 0xff, //0x00001474 sbbq         $-1, %rdx
	0x48, 0x8d, 0x72, 0xff, //0x00001478 leaq         $-1(%rdx), %rsi
	0x48, 0x81, 0xfe, 0xfd, 0x07, 0x00, 0x00, //0x0000147c cmpq         $2045, %rsi
	0x0f, 0x86, 0x63, 0x00, 0x00, 0x00, //0x00001483 jbe          LBB0_273
	//0x00001489 LBB0_268
	0x4c, 0x89, 0xd8, //0x00001489 movq         %r11, %rax
	0x4c, 0x29, 0xf8, //0x0000148c subq         %r15, %rax
	0x48, 0x8b, 0x4d, 0xa8, //0x0000148f movq         $-88(%rbp), %rcx
	0x48, 0x85, 0xc9, //0x00001493 testq        %rcx, %rcx
	0x4c, 0x89, 0xc6, //0x00001496 movq         %r8, %rsi
	0x0f, 0x84, 0xd0, 0x01, 0x00, 0x00, //0x00001499 je           LBB0_290
	0x41, 0xc6, 0x04, 0x24, 0x00, //0x0000149f movb         $0, (%r12)
	0x48, 0x83, 0xf9, 0x01, //0x000014a4 cmpq         $1, %rcx
	0x0f, 0x84, 0xc1, 0x01, 0x00, 0x00, //0x000014a8 je           LBB0_290
	0x48, 0x8d, 0x51, 0xff, //0x000014ae leaq         $-1(%rcx), %rdx
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x000014b2 movl         $1, %ecx
	0x48, 0x83, 0xfa, 0x20, //0x000014b7 cmpq         $32, %rdx
	0x0f, 0x82, 0x99, 0x01, 0x00, 0x00, //0x000014bb jb           LBB0_288
	0x48, 0x89, 0xd1, //0x000014c1 movq         %rdx, %rcx
	0x48, 0x83, 0xe1, 0xe0, //0x000014c4 andq         $-32, %rcx
	0x48, 0x8d, 0x71, 0xe0, //0x000014c8 leaq         $-32(%rcx), %rsi
	0x48, 0x89, 0xf7, //0x000014cc movq         %rsi, %rdi
	0x48, 0xc1, 0xef, 0x05, //0x000014cf shrq         $5, %rdi
	0x48, 0xff, 0xc7, //0x000014d3 incq         %rdi
	0x89, 0xfb, //0x000014d6 movl         %edi, %ebx
	0x83, 0xe3, 0x03, //0x000014d8 andl         $3, %ebx
	0x48, 0x83, 0xfe, 0x60, //0x000014db cmpq         $96, %rsi
	0x0f, 0x83, 0xe2, 0x00, 0x00, 0x00, //0x000014df jae          LBB0_281
	0x31, 0xff, //0x000014e5 xorl         %edi, %edi
	0xe9, 0x2d, 0x01, 0x00, 0x00, //0x000014e7 jmp          LBB0_283
	//0x000014ec LBB0_273
	0x48, 0x83, 0xf9, 0x01, //0x000014ec cmpq         $1, %rcx
	0xb1, 0x02, //0x000014f0 movb         $2, %cl
	0x80, 0xd9, 0x00, //0x000014f2 sbbb         $0, %cl
	0x48, 0xd3, 0xe8, //0x000014f5 shrq         %cl, %rax
	0x48, 0xc1, 0xe2, 0x34, //0x000014f8 shlq         $52, %rdx
	0x48, 0xb9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x000014fc movabsq      $4503599627370495, %rcx
	0x48, 0x21, 0xc8, //0x00001506 andq         %rcx, %rax
	0x48, 0x09, 0xd0, //0x00001509 orq          %rdx, %rax
	0x48, 0x89, 0xc6, //0x0000150c movq         %rax, %rsi
	0x4c, 0x09, 0xce, //0x0000150f orq          %r9, %rsi
	0x83, 0x7d, 0xc8, 0xff, //0x00001512 cmpl         $-1, $-56(%rbp)
	0x48, 0x0f, 0x45, 0xf0, //0x00001516 cmovneq      %rax, %rsi
	0x83, 0x7d, 0xb8, 0x00, //0x0000151a cmpl         $0, $-72(%rbp)
	0x0f, 0x84, 0x38, 0x03, 0x00, 0x00, //0x0000151e je           LBB0_328
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00001524 movl         $64, %ecx
	0x49, 0xff, 0xc2, //0x00001529 incq         %r10
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x0000152c je           LBB0_276
	0x49, 0x0f, 0xbd, 0xca, //0x00001532 bsrq         %r10, %rcx
	0x48, 0x83, 0xf1, 0x3f, //0x00001536 xorq         $63, %rcx
	//0x0000153a LBB0_276
	0x48, 0x89, 0x4d, 0xa0, //0x0000153a movq         %rcx, $-96(%rbp)
	0x49, 0xd3, 0xe2, //0x0000153e shlq         %cl, %r10
	0x48, 0x8b, 0x45, 0x98, //0x00001541 movq         $-104(%rbp), %rax
	0x49, 0xf7, 0xe2, //0x00001545 mulq         %r10
	0x48, 0x89, 0xd3, //0x00001548 movq         %rdx, %rbx
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x0000154b andl         $511, %edx
	0x4c, 0x89, 0xd1, //0x00001551 movq         %r10, %rcx
	0x48, 0xf7, 0xd1, //0x00001554 notq         %rcx
	0x48, 0x89, 0x45, 0xb8, //0x00001557 movq         %rax, $-72(%rbp)
	0x48, 0x39, 0xc8, //0x0000155b cmpq         %rcx, %rax
	0x0f, 0x86, 0xb9, 0x0a, 0x00, 0x00, //0x0000155e jbe          LBB0_629
	0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x00001564 cmpl         $511, %edx
	0x0f, 0x85, 0xad, 0x0a, 0x00, 0x00, //0x0000156a jne          LBB0_629
	0x4c, 0x89, 0xd0, //0x00001570 movq         %r10, %rax
	0x48, 0x8b, 0x55, 0xb0, //0x00001573 movq         $-80(%rbp), %rdx
	0x48, 0x8d, 0x3d, 0x02, 0x21, 0x00, 0x00, //0x00001577 leaq         $8450(%rip), %rdi  /* _POW10_M128_TAB+0(%rip) */
	0x48, 0xf7, 0x24, 0x3a, //0x0000157e mulq         (%rdx,%rdi)
	0x48, 0x8b, 0x7d, 0xb8, //0x00001582 movq         $-72(%rbp), %rdi
	0x48, 0x01, 0xd7, //0x00001586 addq         %rdx, %rdi
	0x48, 0x83, 0xd3, 0x00, //0x00001589 adcq         $0, %rbx
	0x89, 0xda, //0x0000158d movl         %ebx, %edx
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x0000158f andl         $511, %edx
	0x48, 0x39, 0xc8, //0x00001595 cmpq         %rcx, %rax
	0x48, 0x89, 0x7d, 0xb8, //0x00001598 movq         %rdi, $-72(%rbp)
	0x0f, 0x86, 0x71, 0x0a, 0x00, 0x00, //0x0000159c jbe          LBB0_628
	0x48, 0x83, 0xff, 0xff, //0x000015a2 cmpq         $-1, %rdi
	0x0f, 0x85, 0x67, 0x0a, 0x00, 0x00, //0x000015a6 jne          LBB0_628
	0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x000015ac cmpl         $511, %edx
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x01, //0x000015b2 movabsq      $126100789566373888, %rdi
	0x0f, 0x84, 0xc7, 0xfe, 0xff, 0xff, //0x000015bc je           LBB0_268
	0xe9, 0x56, 0x0a, 0x00, 0x00, //0x000015c2 jmp          LBB0_629
	//0x000015c7 LBB0_281
	0x48, 0x89, 0xde, //0x000015c7 movq         %rbx, %rsi
	0x48, 0x29, 0xfe, //0x000015ca subq         %rdi, %rsi
	0x31, 0xff, //0x000015cd xorl         %edi, %edi
	0x66, 0x0f, 0x57, 0xc0, //0x000015cf xorpd        %xmm0, %xmm0
	//0x000015d3 LBB0_282
	0x66, 0x41, 0x0f, 0x11, 0x44, 0x3c, 0x01, //0x000015d3 movupd       %xmm0, $1(%r12,%rdi)
	0x66, 0x41, 0x0f, 0x11, 0x44, 0x3c, 0x11, //0x000015da movupd       %xmm0, $17(%r12,%rdi)
	0x66, 0x41, 0x0f, 0x11, 0x44, 0x3c, 0x21, //0x000015e1 movupd       %xmm0, $33(%r12,%rdi)
	0x66, 0x41, 0x0f, 0x11, 0x44, 0x3c, 0x31, //0x000015e8 movupd       %xmm0, $49(%r12,%rdi)
	0x66, 0x41, 0x0f, 0x11, 0x44, 0x3c, 0x41, //0x000015ef movupd       %xmm0, $65(%r12,%rdi)
	0x66, 0x41, 0x0f, 0x11, 0x44, 0x3c, 0x51, //0x000015f6 movupd       %xmm0, $81(%r12,%rdi)
	0x66, 0x41, 0x0f, 0x11, 0x44, 0x3c, 0x61, //0x000015fd movupd       %xmm0, $97(%r12,%rdi)
	0x66, 0x41, 0x0f, 0x11, 0x44, 0x3c, 0x71, //0x00001604 movupd       %xmm0, $113(%r12,%rdi)
	0x48, 0x83, 0xef, 0x80, //0x0000160b subq         $-128, %rdi
	0x48, 0x83, 0xc6, 0x04, //0x0000160f addq         $4, %rsi
	0x0f, 0x85, 0xba, 0xff, 0xff, 0xff, //0x00001613 jne          LBB0_282
	//0x00001619 LBB0_283
	0x48, 0x85, 0xdb, //0x00001619 testq        %rbx, %rbx
	0x0f, 0x84, 0x28, 0x00, 0x00, 0x00, //0x0000161c je           LBB0_286
	0x48, 0xf7, 0xdb, //0x00001622 negq         %rbx
	0x66, 0x0f, 0x57, 0xc0, //0x00001625 xorpd        %xmm0, %xmm0
	//0x00001629 LBB0_285
	0x48, 0x89, 0xfe, //0x00001629 movq         %rdi, %rsi
	0x48, 0x83, 0xce, 0x01, //0x0000162c orq          $1, %rsi
	0x66, 0x41, 0x0f, 0x11, 0x04, 0x34, //0x00001630 movupd       %xmm0, (%r12,%rsi)
	0x66, 0x41, 0x0f, 0x11, 0x44, 0x34, 0x10, //0x00001636 movupd       %xmm0, $16(%r12,%rsi)
	0x48, 0x83, 0xc7, 0x20, //0x0000163d addq         $32, %rdi
	0x48, 0xff, 0xc3, //0x00001641 incq         %rbx
	0x0f, 0x85, 0xdf, 0xff, 0xff, 0xff, //0x00001644 jne          LBB0_285
	//0x0000164a LBB0_286
	0x48, 0x39, 0xca, //0x0000164a cmpq         %rcx, %rdx
	0x4c, 0x89, 0xc6, //0x0000164d movq         %r8, %rsi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x00001650 je           LBB0_290
	0x48, 0x83, 0xc9, 0x01, //0x00001656 orq          $1, %rcx
	//0x0000165a LBB0_288
	0x48, 0x8b, 0x55, 0xa8, //0x0000165a movq         $-88(%rbp), %rdx
	//0x0000165e LBB0_289
	0x41, 0xc6, 0x04, 0x0c, 0x00, //0x0000165e movb         $0, (%r12,%rcx)
	0x48, 0xff, 0xc1, //0x00001663 incq         %rcx
	0x48, 0x39, 0xca, //0x00001666 cmpq         %rcx, %rdx
	0x0f, 0x85, 0xef, 0xff, 0xff, 0xff, //0x00001669 jne          LBB0_289
	//0x0000166f LBB0_290
	0x8a, 0x16, //0x0000166f movb         (%rsi), %dl
	0x31, 0xc9, //0x00001671 xorl         %ecx, %ecx
	0x80, 0xfa, 0x2d, //0x00001673 cmpb         $45, %dl
	0x0f, 0x94, 0xc1, //0x00001676 sete         %cl
	0x45, 0x31, 0xc9, //0x00001679 xorl         %r9d, %r9d
	0x48, 0x39, 0xc8, //0x0000167c cmpq         %rcx, %rax
	0x0f, 0x8e, 0x9a, 0x00, 0x00, 0x00, //0x0000167f jle          LBB0_303
	0x88, 0x55, 0xa0, //0x00001685 movb         %dl, $-96(%rbp)
	0x4c, 0x89, 0x5d, 0xb0, //0x00001688 movq         %r11, $-80(%rbp)
	0xb2, 0x01, //0x0000168c movb         $1, %dl
	0x45, 0x31, 0xdb, //0x0000168e xorl         %r11d, %r11d
	0x45, 0x31, 0xf6, //0x00001691 xorl         %r14d, %r14d
	0x31, 0xdb, //0x00001694 xorl         %ebx, %ebx
	0x45, 0x31, 0xd2, //0x00001696 xorl         %r10d, %r10d
	0xe9, 0x22, 0x00, 0x00, 0x00, //0x00001699 jmp          LBB0_295
	//0x0000169e LBB0_292
	0x40, 0x80, 0xff, 0x30, //0x0000169e cmpb         $48, %dil
	0xba, 0x01, 0x00, 0x00, 0x00, //0x000016a2 movl         $1, %edx
	0x44, 0x0f, 0x45, 0xf2, //0x000016a7 cmovnel      %edx, %r14d
	//0x000016ab LBB0_293
	0x44, 0x89, 0xdb, //0x000016ab movl         %r11d, %ebx
	//0x000016ae LBB0_294
	0x48, 0xff, 0xc1, //0x000016ae incq         %rcx
	0x48, 0x39, 0xc1, //0x000016b1 cmpq         %rax, %rcx
	0x0f, 0x9c, 0xc2, //0x000016b4 setl         %dl
	0x48, 0x39, 0xc8, //0x000016b7 cmpq         %rcx, %rax
	0x0f, 0x84, 0x70, 0x00, 0x00, 0x00, //0x000016ba je           LBB0_304
	//0x000016c0 LBB0_295
	0x41, 0x0f, 0xb6, 0x3c, 0x08, //0x000016c0 movzbl       (%r8,%rcx), %edi
	0x8d, 0x77, 0xd0, //0x000016c5 leal         $-48(%rdi), %esi
	0x40, 0x80, 0xfe, 0x09, //0x000016c8 cmpb         $9, %sil
	0x0f, 0x87, 0x1c, 0x00, 0x00, 0x00, //0x000016cc ja           LBB0_299
	0x85, 0xdb, //0x000016d2 testl        %ebx, %ebx
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x000016d4 jne          LBB0_301
	0x40, 0x80, 0xff, 0x30, //0x000016da cmpb         $48, %dil
	0x0f, 0x85, 0x22, 0x00, 0x00, 0x00, //0x000016de jne          LBB0_301
	0x41, 0xff, 0xc9, //0x000016e4 decl         %r9d
	0x31, 0xdb, //0x000016e7 xorl         %ebx, %ebx
	0xe9, 0xc0, 0xff, 0xff, 0xff, //0x000016e9 jmp          LBB0_294
	//0x000016ee LBB0_299
	0x40, 0x80, 0xff, 0x2e, //0x000016ee cmpb         $46, %dil
	0x0f, 0x85, 0x44, 0x00, 0x00, 0x00, //0x000016f2 jne          LBB0_305
	0x41, 0x89, 0xd9, //0x000016f8 movl         %ebx, %r9d
	0x41, 0xba, 0x01, 0x00, 0x00, 0x00, //0x000016fb movl         $1, %r10d
	0xe9, 0xa8, 0xff, 0xff, 0xff, //0x00001701 jmp          LBB0_294
	//0x00001706 LBB0_301
	0x49, 0x63, 0xd3, //0x00001706 movslq       %r11d, %rdx
	0x48, 0x39, 0x55, 0xa8, //0x00001709 cmpq         %rdx, $-88(%rbp)
	0x0f, 0x86, 0x8b, 0xff, 0xff, 0xff, //0x0000170d jbe          LBB0_292
	0x41, 0x88, 0x3c, 0x14, //0x00001713 movb         %dil, (%r12,%rdx)
	0x41, 0xff, 0xc3, //0x00001717 incl         %r11d
	0xe9, 0x8c, 0xff, 0xff, 0xff, //0x0000171a jmp          LBB0_293
	//0x0000171f LBB0_303
	0x31, 0xff, //0x0000171f xorl         %edi, %edi
	0x48, 0xb9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00001721 movabsq      $4503599627370495, %rcx
	0xe9, 0x0f, 0x01, 0x00, 0x00, //0x0000172b jmp          LBB0_327
	//0x00001730 LBB0_304
	0x45, 0x85, 0xd2, //0x00001730 testl        %r10d, %r10d
	0x45, 0x0f, 0x44, 0xcb, //0x00001733 cmovel       %r11d, %r9d
	0xe9, 0xad, 0x00, 0x00, 0x00, //0x00001737 jmp          LBB0_319
	//0x0000173c LBB0_305
	0x45, 0x85, 0xd2, //0x0000173c testl        %r10d, %r10d
	0x45, 0x0f, 0x44, 0xcb, //0x0000173f cmovel       %r11d, %r9d
	0xf6, 0xc2, 0x01, //0x00001743 testb        $1, %dl
	0x0f, 0x84, 0x9d, 0x00, 0x00, 0x00, //0x00001746 je           LBB0_319
	0x40, 0x80, 0xcf, 0x20, //0x0000174c orb          $32, %dil
	0x40, 0x80, 0xff, 0x65, //0x00001750 cmpb         $101, %dil
	0x0f, 0x85, 0x8f, 0x00, 0x00, 0x00, //0x00001754 jne          LBB0_319
	0x48, 0x8d, 0x71, 0x01, //0x0000175a leaq         $1(%rcx), %rsi
	0x89, 0xf2, //0x0000175e movl         %esi, %edx
	0x41, 0x8a, 0x14, 0x10, //0x00001760 movb         (%r8,%rdx), %dl
	0x80, 0xfa, 0x2b, //0x00001764 cmpb         $43, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001767 je           LBB0_310
	0x80, 0xfa, 0x2d, //0x0000176d cmpb         $45, %dl
	0x0f, 0x85, 0x1c, 0x00, 0x00, 0x00, //0x00001770 jne          LBB0_312
	0x83, 0xc1, 0x02, //0x00001776 addl         $2, %ecx
	0xba, 0xff, 0xff, 0xff, 0xff, //0x00001779 movl         $-1, %edx
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x0000177e jmp          LBB0_311
	//0x00001783 LBB0_310
	0x83, 0xc1, 0x02, //0x00001783 addl         $2, %ecx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00001786 movl         $1, %edx
	//0x0000178b LBB0_311
	0x89, 0xce, //0x0000178b movl         %ecx, %esi
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x0000178d jmp          LBB0_313
	//0x00001792 LBB0_312
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00001792 movl         $1, %edx
	//0x00001797 LBB0_313
	0x48, 0x63, 0xf6, //0x00001797 movslq       %esi, %rsi
	0x31, 0xc9, //0x0000179a xorl         %ecx, %ecx
	0x48, 0x39, 0xf0, //0x0000179c cmpq         %rsi, %rax
	0x0f, 0x8e, 0x3b, 0x00, 0x00, 0x00, //0x0000179f jle          LBB0_318
	0x49, 0x01, 0xf7, //0x000017a5 addq         %rsi, %r15
	0x31, 0xc9, //0x000017a8 xorl         %ecx, %ecx
	//0x000017aa LBB0_315
	0x81, 0xf9, 0x0f, 0x27, 0x00, 0x00, //0x000017aa cmpl         $9999, %ecx
	0x0f, 0x8f, 0x2a, 0x00, 0x00, 0x00, //0x000017b0 jg           LBB0_318
	0x48, 0x8b, 0x45, 0xc0, //0x000017b6 movq         $-64(%rbp), %rax
	0x42, 0x0f, 0xb6, 0x04, 0x38, //0x000017ba movzbl       (%rax,%r15), %eax
	0x8d, 0x70, 0xd0, //0x000017bf leal         $-48(%rax), %esi
	0x40, 0x80, 0xfe, 0x09, //0x000017c2 cmpb         $9, %sil
	0x0f, 0x87, 0x14, 0x00, 0x00, 0x00, //0x000017c6 ja           LBB0_318
	0x8d, 0x0c, 0x89, //0x000017cc leal         (%rcx,%rcx,4), %ecx
	0x8d, 0x4c, 0x48, 0xd0, //0x000017cf leal         $-48(%rax,%rcx,2), %ecx
	0x49, 0xff, 0xc7, //0x000017d3 incq         %r15
	0x4c, 0x39, 0x7d, 0xb0, //0x000017d6 cmpq         %r15, $-80(%rbp)
	0x0f, 0x85, 0xca, 0xff, 0xff, 0xff, //0x000017da jne          LBB0_315
	//0x000017e0 LBB0_318
	0x0f, 0xaf, 0xca, //0x000017e0 imull        %edx, %ecx
	0x44, 0x01, 0xc9, //0x000017e3 addl         %r9d, %ecx
	0x41, 0x89, 0xc9, //0x000017e6 movl         %ecx, %r9d
	//0x000017e9 LBB0_319
	0x45, 0x85, 0xdb, //0x000017e9 testl        %r11d, %r11d
	0x48, 0xb9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x000017ec movabsq      $4503599627370495, %rcx
	0x4c, 0x8b, 0x7d, 0xa8, //0x000017f6 movq         $-88(%rbp), %r15
	0x0f, 0x84, 0x1e, 0x00, 0x00, 0x00, //0x000017fa je           LBB0_322
	0x31, 0xff, //0x00001800 xorl         %edi, %edi
	0x41, 0x81, 0xf9, 0x36, 0x01, 0x00, 0x00, //0x00001802 cmpl         $310, %r9d
	0x0f, 0x8e, 0x19, 0x00, 0x00, 0x00, //0x00001809 jle          LBB0_323
	0x49, 0xb9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, //0x0000180f movabsq      $9218868437227405312, %r9
	0xe9, 0x1a, 0x00, 0x00, 0x00, //0x00001819 jmp          LBB0_325
	//0x0000181e LBB0_322
	0x45, 0x31, 0xc9, //0x0000181e xorl         %r9d, %r9d
	0x31, 0xff, //0x00001821 xorl         %edi, %edi
	0xe9, 0x10, 0x00, 0x00, 0x00, //0x00001823 jmp          LBB0_325
	//0x00001828 LBB0_323
	0x41, 0x81, 0xf9, 0xb6, 0xfe, 0xff, 0xff, //0x00001828 cmpl         $-330, %r9d
	0x0f, 0x8d, 0x51, 0x00, 0x00, 0x00, //0x0000182f jge          LBB0_331
	0x45, 0x31, 0xc9, //0x00001835 xorl         %r9d, %r9d
	//0x00001838 LBB0_325
	0x4c, 0x8b, 0x5d, 0xb0, //0x00001838 movq         $-80(%rbp), %r11
	//0x0000183c LBB0_326
	0x8a, 0x55, 0xa0, //0x0000183c movb         $-96(%rbp), %dl
	//0x0000183f LBB0_327
	0x48, 0x21, 0xcf, //0x0000183f andq         %rcx, %rdi
	0x4c, 0x09, 0xcf, //0x00001842 orq          %r9, %rdi
	0x48, 0x89, 0xfe, //0x00001845 movq         %rdi, %rsi
	0x49, 0xb9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00001848 movabsq      $-9223372036854775808, %r9
	0x4c, 0x09, 0xce, //0x00001852 orq          %r9, %rsi
	0x80, 0xfa, 0x2d, //0x00001855 cmpb         $45, %dl
	0x48, 0x0f, 0x45, 0xf7, //0x00001858 cmovneq      %rdi, %rsi
	//0x0000185c LBB0_328
	0x49, 0xff, 0xc9, //0x0000185c decq         %r9
	0x49, 0x21, 0xf1, //0x0000185f andq         %rsi, %r9
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, //0x00001862 movabsq      $9218868437227405312, %rax
	0x49, 0x39, 0xc1, //0x0000186c cmpq         %rax, %r9
	0x0f, 0x85, 0x08, 0x00, 0x00, 0x00, //0x0000186f jne          LBB0_330
	0x49, 0xc7, 0x45, 0x00, 0xf8, 0xff, 0xff, 0xff, //0x00001875 movq         $-8, (%r13)
	//0x0000187d LBB0_330
	0x49, 0x89, 0x75, 0x08, //0x0000187d movq         %rsi, $8(%r13)
	0xe9, 0x69, 0xea, 0xff, 0xff, //0x00001881 jmp          LBB0_38
	//0x00001886 LBB0_331
	0x49, 0xba, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, //0x00001886 movabsq      $1152921504606846975, %r10
	0x45, 0x85, 0xc9, //0x00001890 testl        %r9d, %r9d
	0x4c, 0x89, 0x6d, 0xd0, //0x00001893 movq         %r13, $-48(%rbp)
	0x0f, 0x8e, 0x6e, 0x07, 0x00, 0x00, //0x00001897 jle          LBB0_447
	0x31, 0xff, //0x0000189d xorl         %edi, %edi
	0x44, 0x89, 0xd8, //0x0000189f movl         %r11d, %eax
	0xe9, 0x14, 0x00, 0x00, 0x00, //0x000018a2 jmp          LBB0_335
	//0x000018a7 LBB0_333
	0x89, 0xc6, //0x000018a7 movl         %eax, %esi
	//0x000018a9 LBB0_334
	0x48, 0x8b, 0x7d, 0xc0, //0x000018a9 movq         $-64(%rbp), %rdi
	0x03, 0x7d, 0xb8, //0x000018ad addl         $-72(%rbp), %edi
	0x89, 0xf0, //0x000018b0 movl         %esi, %eax
	0x45, 0x85, 0xc9, //0x000018b2 testl        %r9d, %r9d
	0x0f, 0x8e, 0x54, 0x08, 0x00, 0x00, //0x000018b5 jle          LBB0_457
	//0x000018bb LBB0_335
	0xb9, 0x1b, 0x00, 0x00, 0x00, //0x000018bb movl         $27, %ecx
	0x41, 0x83, 0xf9, 0x08, //0x000018c0 cmpl         $8, %r9d
	0x0f, 0x8f, 0x0d, 0x00, 0x00, 0x00, //0x000018c4 jg           LBB0_337
	0x44, 0x89, 0xc9, //0x000018ca movl         %r9d, %ecx
	0x48, 0x8d, 0x15, 0x3c, 0x49, 0x00, 0x00, //0x000018cd leaq         $18748(%rip), %rdx  /* _POW_TAB+0(%rip) */
	0x8b, 0x0c, 0x8a, //0x000018d4 movl         (%rdx,%rcx,4), %ecx
	//0x000018d7 LBB0_337
	0x48, 0x89, 0x7d, 0xc0, //0x000018d7 movq         %rdi, $-64(%rbp)
	0x85, 0xc0, //0x000018db testl        %eax, %eax
	0x89, 0x4d, 0xb8, //0x000018dd movl         %ecx, $-72(%rbp)
	0x0f, 0x84, 0xc1, 0xff, 0xff, 0xff, //0x000018e0 je           LBB0_333
	0x41, 0x89, 0xc8, //0x000018e6 movl         %ecx, %r8d
	0x41, 0xf7, 0xd8, //0x000018e9 negl         %r8d
	0x85, 0xc9, //0x000018ec testl        %ecx, %ecx
	0x0f, 0x84, 0xb3, 0xff, 0xff, 0xff, //0x000018ee je           LBB0_333
	0x0f, 0x88, 0x88, 0x01, 0x00, 0x00, //0x000018f4 js           LBB0_364
	//0x000018fa LBB0_340
	0x41, 0x83, 0xf8, 0xc3, //0x000018fa cmpl         $-61, %r8d
	0x0f, 0x8e, 0x21, 0x00, 0x00, 0x00, //0x000018fe jle          LBB0_344
	0xe9, 0x89, 0x03, 0x00, 0x00, //0x00001904 jmp          LBB0_394
	//0x00001909 LBB0_341
	0xff, 0xc8, //0x00001909 decl         %eax
	0x41, 0x89, 0xc3, //0x0000190b movl         %eax, %r11d
	//0x0000190e LBB0_342
	0x45, 0x85, 0xdb, //0x0000190e testl        %r11d, %r11d
	0x45, 0x0f, 0x44, 0xcb, //0x00001911 cmovel       %r11d, %r9d
	//0x00001915 LBB0_343
	0x44, 0x8d, 0x41, 0x3c, //0x00001915 leal         $60(%rcx), %r8d
	0x44, 0x89, 0xd8, //0x00001919 movl         %r11d, %eax
	0x83, 0xf9, 0x88, //0x0000191c cmpl         $-120, %ecx
	0x0f, 0x8d, 0x5e, 0x03, 0x00, 0x00, //0x0000191f jge          LBB0_393
	//0x00001925 LBB0_344
	0x44, 0x89, 0xc1, //0x00001925 movl         %r8d, %ecx
	0x48, 0x63, 0xf8, //0x00001928 movslq       %eax, %rdi
	0x31, 0xf6, //0x0000192b xorl         %esi, %esi
	0x31, 0xd2, //0x0000192d xorl         %edx, %edx
	0x90, //0x0000192f .p2align 4, 0x90
	//0x00001930 LBB0_345
	0x48, 0x39, 0xfe, //0x00001930 cmpq         %rdi, %rsi
	0x0f, 0x8d, 0x23, 0x00, 0x00, 0x00, //0x00001933 jge          LBB0_347
	0x48, 0x8d, 0x14, 0x92, //0x00001939 leaq         (%rdx,%rdx,4), %rdx
	0x49, 0x0f, 0xbe, 0x1c, 0x34, //0x0000193d movsbq       (%r12,%rsi), %rbx
	0x48, 0x8d, 0x54, 0x53, 0xd0, //0x00001942 leaq         $-48(%rbx,%rdx,2), %rdx
	0x48, 0xff, 0xc6, //0x00001947 incq         %rsi
	0x49, 0x8d, 0x5a, 0x01, //0x0000194a leaq         $1(%r10), %rbx
	0x48, 0x39, 0xda, //0x0000194e cmpq         %rbx, %rdx
	0x0f, 0x82, 0xd9, 0xff, 0xff, 0xff, //0x00001951 jb           LBB0_345
	0xe9, 0x2a, 0x00, 0x00, 0x00, //0x00001957 jmp          LBB0_349
	//0x0000195c LBB0_347
	0x48, 0x85, 0xd2, //0x0000195c testq        %rdx, %rdx
	0x0f, 0x84, 0x15, 0x01, 0x00, 0x00, //0x0000195f je           LBB0_362
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001965 .p2align 4, 0x90
	//0x00001970 LBB0_348
	0x48, 0x01, 0xd2, //0x00001970 addq         %rdx, %rdx
	0x48, 0x8d, 0x14, 0x92, //0x00001973 leaq         (%rdx,%rdx,4), %rdx
	0xff, 0xc6, //0x00001977 incl         %esi
	0x49, 0x8d, 0x7a, 0x01, //0x00001979 leaq         $1(%r10), %rdi
	0x48, 0x39, 0xfa, //0x0000197d cmpq         %rdi, %rdx
	0x0f, 0x82, 0xea, 0xff, 0xff, 0xff, //0x00001980 jb           LBB0_348
	//0x00001986 LBB0_349
	0x41, 0x29, 0xf1, //0x00001986 subl         %esi, %r9d
	0x31, 0xff, //0x00001989 xorl         %edi, %edi
	0x39, 0xc6, //0x0000198b cmpl         %eax, %esi
	0x0f, 0x8d, 0x49, 0x00, 0x00, 0x00, //0x0000198d jge          LBB0_354
	0x48, 0x63, 0xc6, //0x00001993 movslq       %esi, %rax
	0x49, 0x63, 0xf3, //0x00001996 movslq       %r11d, %rsi
	0x49, 0x8d, 0x3c, 0x04, //0x00001999 leaq         (%r12,%rax), %rdi
	0x45, 0x31, 0xdb, //0x0000199d xorl         %r11d, %r11d
	//0x000019a0 .p2align 4, 0x90
	//0x000019a0 LBB0_351
	0x48, 0x89, 0xd3, //0x000019a0 movq         %rdx, %rbx
	0x48, 0xc1, 0xeb, 0x3c, //0x000019a3 shrq         $60, %rbx
	0x4c, 0x21, 0xd2, //0x000019a7 andq         %r10, %rdx
	0x80, 0xcb, 0x30, //0x000019aa orb          $48, %bl
	0x43, 0x88, 0x1c, 0x1c, //0x000019ad movb         %bl, (%r12,%r11)
	0x48, 0x8d, 0x14, 0x92, //0x000019b1 leaq         (%rdx,%rdx,4), %rdx
	0x4a, 0x0f, 0xbe, 0x1c, 0x1f, //0x000019b5 movsbq       (%rdi,%r11), %rbx
	0x48, 0x8d, 0x54, 0x53, 0xd0, //0x000019ba leaq         $-48(%rbx,%rdx,2), %rdx
	0x4a, 0x8d, 0x5c, 0x18, 0x01, //0x000019bf leaq         $1(%rax,%r11), %rbx
	0x49, 0xff, 0xc3, //0x000019c4 incq         %r11
	0x48, 0x39, 0xf3, //0x000019c7 cmpq         %rsi, %rbx
	0x0f, 0x8c, 0xd0, 0xff, 0xff, 0xff, //0x000019ca jl           LBB0_351
	0x48, 0x85, 0xd2, //0x000019d0 testq        %rdx, %rdx
	0x0f, 0x84, 0x64, 0x00, 0x00, 0x00, //0x000019d3 je           LBB0_358
	0x44, 0x89, 0xdf, //0x000019d9 movl         %r11d, %edi
	//0x000019dc LBB0_354
	0x41, 0x89, 0xfb, //0x000019dc movl         %edi, %r11d
	0xe9, 0x28, 0x00, 0x00, 0x00, //0x000019df jmp          LBB0_356
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000019e4 .p2align 4, 0x90
	//0x000019f0 LBB0_355
	0x48, 0x85, 0xc0, //0x000019f0 testq        %rax, %rax
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x000019f3 movl         $1, %eax
	0x44, 0x0f, 0x45, 0xf0, //0x000019f8 cmovnel      %eax, %r14d
	0x48, 0x01, 0xd2, //0x000019fc addq         %rdx, %rdx
	0x48, 0x8d, 0x14, 0x92, //0x000019ff leaq         (%rdx,%rdx,4), %rdx
	0x48, 0x85, 0xd2, //0x00001a03 testq        %rdx, %rdx
	0x0f, 0x84, 0x31, 0x00, 0x00, 0x00, //0x00001a06 je           LBB0_358
	//0x00001a0c LBB0_356
	0x48, 0x89, 0xd0, //0x00001a0c movq         %rdx, %rax
	0x48, 0xc1, 0xe8, 0x3c, //0x00001a0f shrq         $60, %rax
	0x4c, 0x21, 0xd2, //0x00001a13 andq         %r10, %rdx
	0x49, 0x63, 0xf3, //0x00001a16 movslq       %r11d, %rsi
	0x49, 0x39, 0xf7, //0x00001a19 cmpq         %rsi, %r15
	0x0f, 0x86, 0xce, 0xff, 0xff, 0xff, //0x00001a1c jbe          LBB0_355
	0x0c, 0x30, //0x00001a22 orb          $48, %al
	0x41, 0x88, 0x04, 0x34, //0x00001a24 movb         %al, (%r12,%rsi)
	0xff, 0xc6, //0x00001a28 incl         %esi
	0x41, 0x89, 0xf3, //0x00001a2a movl         %esi, %r11d
	0x48, 0x01, 0xd2, //0x00001a2d addq         %rdx, %rdx
	0x48, 0x8d, 0x14, 0x92, //0x00001a30 leaq         (%rdx,%rdx,4), %rdx
	0x48, 0x85, 0xd2, //0x00001a34 testq        %rdx, %rdx
	0x0f, 0x85, 0xcf, 0xff, 0xff, 0xff, //0x00001a37 jne          LBB0_356
	//0x00001a3d LBB0_358
	0x41, 0xff, 0xc1, //0x00001a3d incl         %r9d
	0x45, 0x85, 0xdb, //0x00001a40 testl        %r11d, %r11d
	0x0f, 0x8e, 0xc5, 0xfe, 0xff, 0xff, //0x00001a43 jle          LBB0_342
	0x44, 0x89, 0xd8, //0x00001a49 movl         %r11d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x00001a4c cmpb         $48, $-1(%rax,%r12)
	0x0f, 0x85, 0xbd, 0xfe, 0xff, 0xff, //0x00001a52 jne          LBB0_343
	//0x00001a58 LBB0_360
	0x48, 0x83, 0xf8, 0x01, //0x00001a58 cmpq         $1, %rax
	0x0f, 0x8e, 0xa7, 0xfe, 0xff, 0xff, //0x00001a5c jle          LBB0_341
	0x4c, 0x8d, 0x58, 0xff, //0x00001a62 leaq         $-1(%rax), %r11
	0x41, 0x80, 0x7c, 0x04, 0xfe, 0x30, //0x00001a66 cmpb         $48, $-2(%r12,%rax)
	0x4c, 0x89, 0xd8, //0x00001a6c movq         %r11, %rax
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00001a6f je           LBB0_360
	0xe9, 0x9b, 0xfe, 0xff, 0xff, //0x00001a75 jmp          LBB0_343
	//0x00001a7a LBB0_362
	0x45, 0x31, 0xdb, //0x00001a7a xorl         %r11d, %r11d
	0xe9, 0x93, 0xfe, 0xff, 0xff, //0x00001a7d jmp          LBB0_343
	//0x00001a82 LBB0_364
	0x83, 0x7d, 0xb8, 0xc3, //0x00001a82 cmpl         $-61, $-72(%rbp)
	0x0f, 0x8f, 0x67, 0x03, 0x00, 0x00, //0x00001a86 jg           LBB0_415
	0x4c, 0x8d, 0x2d, 0xad, 0x47, 0x00, 0x00, //0x00001a8c leaq         $18349(%rip), %r13  /* _LSHIFT_TAB+0(%rip) */
	0xe9, 0x20, 0x00, 0x00, 0x00, //0x00001a93 jmp          LBB0_369
	//0x00001a98 LBB0_366
	0xff, 0xc8, //0x00001a98 decl         %eax
	0x41, 0x89, 0xc3, //0x00001a9a movl         %eax, %r11d
	//0x00001a9d LBB0_367
	0x45, 0x85, 0xdb, //0x00001a9d testl        %r11d, %r11d
	0x45, 0x0f, 0x44, 0xcb, //0x00001aa0 cmovel       %r11d, %r9d
	//0x00001aa4 LBB0_368
	0x48, 0x8b, 0x4d, 0xc8, //0x00001aa4 movq         $-56(%rbp), %rcx
	0x44, 0x8d, 0x41, 0xc4, //0x00001aa8 leal         $-60(%rcx), %r8d
	0x44, 0x89, 0xd8, //0x00001aac movl         %r11d, %eax
	0x83, 0xf9, 0x78, //0x00001aaf cmpl         $120, %ecx
	0x0f, 0x8e, 0x28, 0x03, 0x00, 0x00, //0x00001ab2 jle          LBB0_414
	//0x00001ab8 LBB0_369
	0x4c, 0x89, 0x45, 0xc8, //0x00001ab8 movq         %r8, $-56(%rbp)
	0x48, 0x63, 0xd8, //0x00001abc movslq       %eax, %rbx
	0x85, 0xdb, //0x00001abf testl        %ebx, %ebx
	0x0f, 0x84, 0x3b, 0x00, 0x00, 0x00, //0x00001ac1 je           LBB0_375
	0xb2, 0x38, //0x00001ac7 movb         $56, %dl
	0x31, 0xc9, //0x00001ac9 xorl         %ecx, %ecx
	//0x00001acb LBB0_371
	0x41, 0xb8, 0x13, 0x00, 0x00, 0x00, //0x00001acb movl         $19, %r8d
	0x48, 0x83, 0xf9, 0x2a, //0x00001ad1 cmpq         $42, %rcx
	0x0f, 0x84, 0x2d, 0x00, 0x00, 0x00, //0x00001ad5 je           LBB0_376
	0x41, 0x38, 0x14, 0x0c, //0x00001adb cmpb         %dl, (%r12,%rcx)
	0x0f, 0x85, 0x93, 0x01, 0x00, 0x00, //0x00001adf jne          LBB0_392
	0x42, 0x0f, 0xb6, 0x94, 0x29, 0x65, 0x18, 0x00, 0x00, //0x00001ae5 movzbl       $6245(%rcx,%r13), %edx
	0x48, 0xff, 0xc1, //0x00001aee incq         %rcx
	0x48, 0x39, 0xcb, //0x00001af1 cmpq         %rcx, %rbx
	0x0f, 0x85, 0xd1, 0xff, 0xff, 0xff, //0x00001af4 jne          LBB0_371
	0x84, 0xd2, //0x00001afa testb        %dl, %dl
	0x0f, 0x84, 0x06, 0x00, 0x00, 0x00, //0x00001afc je           LBB0_376
	//0x00001b02 LBB0_375
	0x41, 0xb8, 0x12, 0x00, 0x00, 0x00, //0x00001b02 movl         $18, %r8d
	//0x00001b08 LBB0_376
	0x85, 0xc0, //0x00001b08 testl        %eax, %eax
	0x0f, 0x8e, 0x1e, 0x01, 0x00, 0x00, //0x00001b0a jle          LBB0_388
	0x4d, 0x89, 0xcd, //0x00001b10 movq         %r9, %r13
	0x44, 0x01, 0xc0, //0x00001b13 addl         %r8d, %eax
	0x48, 0x98, //0x00001b16 cltq         
	0x48, 0x89, 0xc7, //0x00001b18 movq         %rax, %rdi
	0x48, 0xc1, 0xe7, 0x20, //0x00001b1b shlq         $32, %rdi
	0x48, 0xff, 0xc8, //0x00001b1f decq         %rax
	0x48, 0xff, 0xc3, //0x00001b22 incq         %rbx
	0x31, 0xc9, //0x00001b25 xorl         %ecx, %ecx
	0xe9, 0x2e, 0x00, 0x00, 0x00, //0x00001b27 jmp          LBB0_380
	0x90, 0x90, 0x90, 0x90, //0x00001b2c .p2align 4, 0x90
	//0x00001b30 LBB0_378
	0x48, 0x85, 0xc0, //0x00001b30 testq        %rax, %rax
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00001b33 movl         $1, %eax
	0x44, 0x0f, 0x45, 0xf0, //0x00001b38 cmovnel      %eax, %r14d
	//0x00001b3c LBB0_379
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00001b3c movabsq      $-4294967296, %rax
	0x48, 0x01, 0xc7, //0x00001b46 addq         %rax, %rdi
	0x49, 0x8d, 0x41, 0xff, //0x00001b49 leaq         $-1(%r9), %rax
	0x48, 0xff, 0xcb, //0x00001b4d decq         %rbx
	0x48, 0x83, 0xfb, 0x01, //0x00001b50 cmpq         $1, %rbx
	0x0f, 0x8e, 0x49, 0x00, 0x00, 0x00, //0x00001b54 jle          LBB0_382
	//0x00001b5a LBB0_380
	0x49, 0x89, 0xc1, //0x00001b5a movq         %rax, %r9
	0x41, 0x0f, 0xb6, 0x74, 0x1c, 0xfe, //0x00001b5d movzbl       $-2(%r12,%rbx), %esi
	0x48, 0xc1, 0xe6, 0x3c, //0x00001b63 shlq         $60, %rsi
	0x48, 0x01, 0xce, //0x00001b67 addq         %rcx, %rsi
	0x48, 0x89, 0xf0, //0x00001b6a movq         %rsi, %rax
	0x48, 0xb9, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00001b6d movabsq      $-3689348814741910323, %rcx
	0x48, 0xf7, 0xe1, //0x00001b77 mulq         %rcx
	0x48, 0x89, 0xd1, //0x00001b7a movq         %rdx, %rcx
	0x48, 0xc1, 0xe9, 0x03, //0x00001b7d shrq         $3, %rcx
	0x48, 0x8d, 0x04, 0x09, //0x00001b81 leaq         (%rcx,%rcx), %rax
	0x48, 0x8d, 0x14, 0x80, //0x00001b85 leaq         (%rax,%rax,4), %rdx
	0x48, 0x89, 0xf0, //0x00001b89 movq         %rsi, %rax
	0x48, 0x29, 0xd0, //0x00001b8c subq         %rdx, %rax
	0x4d, 0x39, 0xf9, //0x00001b8f cmpq         %r15, %r9
	0x0f, 0x83, 0x98, 0xff, 0xff, 0xff, //0x00001b92 jae          LBB0_378
	0x04, 0x30, //0x00001b98 addb         $48, %al
	0x43, 0x88, 0x04, 0x0c, //0x00001b9a movb         %al, (%r12,%r9)
	0xe9, 0x99, 0xff, 0xff, 0xff, //0x00001b9e jmp          LBB0_379
	//0x00001ba3 LBB0_382
	0x48, 0x83, 0xfe, 0x0a, //0x00001ba3 cmpq         $10, %rsi
	0x0f, 0x83, 0x0f, 0x00, 0x00, 0x00, //0x00001ba7 jae          LBB0_384
	0x4d, 0x89, 0xe9, //0x00001bad movq         %r13, %r9
	0x4c, 0x8d, 0x2d, 0x89, 0x46, 0x00, 0x00, //0x00001bb0 leaq         $18057(%rip), %r13  /* _LSHIFT_TAB+0(%rip) */
	0xe9, 0x72, 0x00, 0x00, 0x00, //0x00001bb7 jmp          LBB0_388
	//0x00001bbc LBB0_384
	0x49, 0x63, 0xf1, //0x00001bbc movslq       %r9d, %rsi
	0x48, 0xff, 0xce, //0x00001bbf decq         %rsi
	0x4d, 0x89, 0xe9, //0x00001bc2 movq         %r13, %r9
	0x4c, 0x8d, 0x2d, 0x74, 0x46, 0x00, 0x00, //0x00001bc5 leaq         $18036(%rip), %r13  /* _LSHIFT_TAB+0(%rip) */
	0xe9, 0x1c, 0x00, 0x00, 0x00, //0x00001bcc jmp          LBB0_386
	//0x00001bd1 LBB0_385
	0x48, 0x85, 0xc0, //0x00001bd1 testq        %rax, %rax
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00001bd4 movl         $1, %eax
	0x44, 0x0f, 0x45, 0xf0, //0x00001bd9 cmovnel      %eax, %r14d
	0x48, 0xff, 0xce, //0x00001bdd decq         %rsi
	0x48, 0x83, 0xf9, 0x09, //0x00001be0 cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x00001be4 movq         %rdx, %rcx
	0x0f, 0x86, 0x41, 0x00, 0x00, 0x00, //0x00001be7 jbe          LBB0_388
	//0x00001bed LBB0_386
	0x48, 0x89, 0xc8, //0x00001bed movq         %rcx, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00001bf0 movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x00001bfa mulq         %rdx
	0x48, 0xc1, 0xea, 0x03, //0x00001bfd shrq         $3, %rdx
	0x48, 0x8d, 0x04, 0x12, //0x00001c01 leaq         (%rdx,%rdx), %rax
	0x48, 0x8d, 0x3c, 0x80, //0x00001c05 leaq         (%rax,%rax,4), %rdi
	0x48, 0x89, 0xc8, //0x00001c09 movq         %rcx, %rax
	0x48, 0x29, 0xf8, //0x00001c0c subq         %rdi, %rax
	0x4c, 0x39, 0xfe, //0x00001c0f cmpq         %r15, %rsi
	0x0f, 0x83, 0xb9, 0xff, 0xff, 0xff, //0x00001c12 jae          LBB0_385
	0x04, 0x30, //0x00001c18 addb         $48, %al
	0x41, 0x88, 0x04, 0x34, //0x00001c1a movb         %al, (%r12,%rsi)
	0x48, 0xff, 0xce, //0x00001c1e decq         %rsi
	0x48, 0x83, 0xf9, 0x09, //0x00001c21 cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x00001c25 movq         %rdx, %rcx
	0x0f, 0x87, 0xbf, 0xff, 0xff, 0xff, //0x00001c28 ja           LBB0_386
	//0x00001c2e LBB0_388
	0x45, 0x01, 0xc3, //0x00001c2e addl         %r8d, %r11d
	0x4d, 0x63, 0xdb, //0x00001c31 movslq       %r11d, %r11
	0x4d, 0x39, 0xdf, //0x00001c34 cmpq         %r11, %r15
	0x45, 0x0f, 0x46, 0xdf, //0x00001c37 cmovbel      %r15d, %r11d
	0x45, 0x01, 0xc1, //0x00001c3b addl         %r8d, %r9d
	0x45, 0x85, 0xdb, //0x00001c3e testl        %r11d, %r11d
	0x0f, 0x8e, 0x56, 0xfe, 0xff, 0xff, //0x00001c41 jle          LBB0_367
	0x44, 0x89, 0xd8, //0x00001c47 movl         %r11d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x00001c4a cmpb         $48, $-1(%rax,%r12)
	0x0f, 0x85, 0x4e, 0xfe, 0xff, 0xff, //0x00001c50 jne          LBB0_368
	//0x00001c56 LBB0_390
	0x48, 0x83, 0xf8, 0x01, //0x00001c56 cmpq         $1, %rax
	0x0f, 0x8e, 0x38, 0xfe, 0xff, 0xff, //0x00001c5a jle          LBB0_366
	0x4c, 0x8d, 0x58, 0xff, //0x00001c60 leaq         $-1(%rax), %r11
	0x41, 0x80, 0x7c, 0x04, 0xfe, 0x30, //0x00001c64 cmpb         $48, $-2(%r12,%rax)
	0x4c, 0x89, 0xd8, //0x00001c6a movq         %r11, %rax
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00001c6d je           LBB0_390
	0xe9, 0x2c, 0xfe, 0xff, 0xff, //0x00001c73 jmp          LBB0_368
	//0x00001c78 LBB0_392
	0x0f, 0x8c, 0x84, 0xfe, 0xff, 0xff, //0x00001c78 jl           LBB0_375
	0xe9, 0x85, 0xfe, 0xff, 0xff, //0x00001c7e jmp          LBB0_376
	//0x00001c83 LBB0_393
	0x44, 0x89, 0xd8, //0x00001c83 movl         %r11d, %eax
	0x44, 0x89, 0xde, //0x00001c86 movl         %r11d, %esi
	0x45, 0x85, 0xc0, //0x00001c89 testl        %r8d, %r8d
	0x0f, 0x84, 0x17, 0xfc, 0xff, 0xff, //0x00001c8c je           LBB0_334
	//0x00001c92 LBB0_394
	0x41, 0xf7, 0xd8, //0x00001c92 negl         %r8d
	0x48, 0x63, 0xf8, //0x00001c95 movslq       %eax, %rdi
	0x31, 0xf6, //0x00001c98 xorl         %esi, %esi
	0x31, 0xd2, //0x00001c9a xorl         %edx, %edx
	//0x00001c9c LBB0_395
	0x48, 0x39, 0xfe, //0x00001c9c cmpq         %rdi, %rsi
	0x0f, 0x8d, 0x12, 0x01, 0x00, 0x00, //0x00001c9f jge          LBB0_411
	0x48, 0x8d, 0x0c, 0x92, //0x00001ca5 leaq         (%rdx,%rdx,4), %rcx
	0x49, 0x0f, 0xbe, 0x14, 0x34, //0x00001ca9 movsbq       (%r12,%rsi), %rdx
	0x48, 0x8d, 0x54, 0x4a, 0xd0, //0x00001cae leaq         $-48(%rdx,%rcx,2), %rdx
	0x48, 0xff, 0xc6, //0x00001cb3 incq         %rsi
	0x48, 0x89, 0xd3, //0x00001cb6 movq         %rdx, %rbx
	0x44, 0x89, 0xc1, //0x00001cb9 movl         %r8d, %ecx
	0x48, 0xd3, 0xeb, //0x00001cbc shrq         %cl, %rbx
	0x48, 0x85, 0xdb, //0x00001cbf testq        %rbx, %rbx
	0x0f, 0x84, 0xd4, 0xff, 0xff, 0xff, //0x00001cc2 je           LBB0_395
	//0x00001cc8 LBB0_397
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001cc8 movq         $-1, %rbx
	0x44, 0x89, 0xc1, //0x00001ccf movl         %r8d, %ecx
	0x48, 0xd3, 0xe3, //0x00001cd2 shlq         %cl, %rbx
	0x48, 0xf7, 0xd3, //0x00001cd5 notq         %rbx
	0x31, 0xff, //0x00001cd8 xorl         %edi, %edi
	0x39, 0xc6, //0x00001cda cmpl         %eax, %esi
	0x0f, 0x8d, 0x49, 0x00, 0x00, 0x00, //0x00001cdc jge          LBB0_401
	0x4c, 0x89, 0x4d, 0xc8, //0x00001ce2 movq         %r9, $-56(%rbp)
	0x4c, 0x63, 0xee, //0x00001ce6 movslq       %esi, %r13
	0x4d, 0x63, 0xcb, //0x00001ce9 movslq       %r11d, %r9
	0x4f, 0x8d, 0x1c, 0x2c, //0x00001cec leaq         (%r12,%r13), %r11
	0x31, 0xff, //0x00001cf0 xorl         %edi, %edi
	//0x00001cf2 LBB0_399
	0x48, 0x89, 0xd0, //0x00001cf2 movq         %rdx, %rax
	0x44, 0x89, 0xc1, //0x00001cf5 movl         %r8d, %ecx
	0x48, 0xd3, 0xe8, //0x00001cf8 shrq         %cl, %rax
	0x48, 0x21, 0xda, //0x00001cfb andq         %rbx, %rdx
	0x04, 0x30, //0x00001cfe addb         $48, %al
	0x41, 0x88, 0x04, 0x3c, //0x00001d00 movb         %al, (%r12,%rdi)
	0x48, 0x8d, 0x04, 0x92, //0x00001d04 leaq         (%rdx,%rdx,4), %rax
	0x49, 0x0f, 0xbe, 0x0c, 0x3b, //0x00001d08 movsbq       (%r11,%rdi), %rcx
	0x48, 0x8d, 0x54, 0x41, 0xd0, //0x00001d0d leaq         $-48(%rcx,%rax,2), %rdx
	0x49, 0x8d, 0x44, 0x3d, 0x01, //0x00001d12 leaq         $1(%r13,%rdi), %rax
	0x48, 0xff, 0xc7, //0x00001d17 incq         %rdi
	0x4c, 0x39, 0xc8, //0x00001d1a cmpq         %r9, %rax
	0x0f, 0x8c, 0xcf, 0xff, 0xff, 0xff, //0x00001d1d jl           LBB0_399
	0x4c, 0x8b, 0x6d, 0xd0, //0x00001d23 movq         $-48(%rbp), %r13
	0x4c, 0x8b, 0x4d, 0xc8, //0x00001d27 movq         $-56(%rbp), %r9
	//0x00001d2b LBB0_401
	0x41, 0x29, 0xf1, //0x00001d2b subl         %esi, %r9d
	0x41, 0x89, 0xfb, //0x00001d2e movl         %edi, %r11d
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x00001d31 jmp          LBB0_404
	//0x00001d36 LBB0_402
	0x48, 0x85, 0xc0, //0x00001d36 testq        %rax, %rax
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00001d39 movl         $1, %eax
	0x44, 0x0f, 0x45, 0xf0, //0x00001d3e cmovnel      %eax, %r14d
	//0x00001d42 LBB0_403
	0x48, 0x01, 0xd2, //0x00001d42 addq         %rdx, %rdx
	0x48, 0x8d, 0x14, 0x92, //0x00001d45 leaq         (%rdx,%rdx,4), %rdx
	//0x00001d49 LBB0_404
	0x48, 0x85, 0xd2, //0x00001d49 testq        %rdx, %rdx
	0x0f, 0x84, 0x28, 0x00, 0x00, 0x00, //0x00001d4c je           LBB0_407
	0x48, 0x89, 0xd0, //0x00001d52 movq         %rdx, %rax
	0x44, 0x89, 0xc1, //0x00001d55 movl         %r8d, %ecx
	0x48, 0xd3, 0xe8, //0x00001d58 shrq         %cl, %rax
	0x48, 0x21, 0xda, //0x00001d5b andq         %rbx, %rdx
	0x49, 0x63, 0xcb, //0x00001d5e movslq       %r11d, %rcx
	0x49, 0x39, 0xcf, //0x00001d61 cmpq         %rcx, %r15
	0x0f, 0x86, 0xcc, 0xff, 0xff, 0xff, //0x00001d64 jbe          LBB0_402
	0x04, 0x30, //0x00001d6a addb         $48, %al
	0x41, 0x88, 0x04, 0x0c, //0x00001d6c movb         %al, (%r12,%rcx)
	0xff, 0xc1, //0x00001d70 incl         %ecx
	0x41, 0x89, 0xcb, //0x00001d72 movl         %ecx, %r11d
	0xe9, 0xc8, 0xff, 0xff, 0xff, //0x00001d75 jmp          LBB0_403
	//0x00001d7a LBB0_407
	0x41, 0xff, 0xc1, //0x00001d7a incl         %r9d
	0x45, 0x85, 0xdb, //0x00001d7d testl        %r11d, %r11d
	0x0f, 0x8e, 0x49, 0x02, 0x00, 0x00, //0x00001d80 jle          LBB0_441
	0x44, 0x89, 0xd8, //0x00001d86 movl         %r11d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x00001d89 cmpb         $48, $-1(%rax,%r12)
	0x0f, 0x85, 0x43, 0x02, 0x00, 0x00, //0x00001d8f jne          LBB0_442
	//0x00001d95 LBB0_409
	0x48, 0x83, 0xf8, 0x01, //0x00001d95 cmpq         $1, %rax
	0x0f, 0x8e, 0x2b, 0x02, 0x00, 0x00, //0x00001d99 jle          LBB0_440
	0x4c, 0x8d, 0x58, 0xff, //0x00001d9f leaq         $-1(%rax), %r11
	0x41, 0x80, 0x7c, 0x04, 0xfe, 0x30, //0x00001da3 cmpb         $48, $-2(%r12,%rax)
	0x4c, 0x89, 0xd8, //0x00001da9 movq         %r11, %rax
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00001dac je           LBB0_409
	0xe9, 0x21, 0x02, 0x00, 0x00, //0x00001db2 jmp          LBB0_442
	//0x00001db7 LBB0_411
	0x48, 0x85, 0xd2, //0x00001db7 testq        %rdx, %rdx
	0x0f, 0x84, 0x3e, 0x01, 0x00, 0x00, //0x00001dba je           LBB0_430
	//0x00001dc0 LBB0_412
	0x48, 0x89, 0xd7, //0x00001dc0 movq         %rdx, %rdi
	0x44, 0x89, 0xc1, //0x00001dc3 movl         %r8d, %ecx
	0x48, 0xd3, 0xef, //0x00001dc6 shrq         %cl, %rdi
	0x48, 0x85, 0xff, //0x00001dc9 testq        %rdi, %rdi
	0x0f, 0x85, 0xf6, 0xfe, 0xff, 0xff, //0x00001dcc jne          LBB0_397
	0x48, 0x01, 0xd2, //0x00001dd2 addq         %rdx, %rdx
	0x48, 0x8d, 0x14, 0x92, //0x00001dd5 leaq         (%rdx,%rdx,4), %rdx
	0xff, 0xc6, //0x00001dd9 incl         %esi
	0xe9, 0xe0, 0xff, 0xff, 0xff, //0x00001ddb jmp          LBB0_412
	//0x00001de0 LBB0_414
	0x44, 0x89, 0xd8, //0x00001de0 movl         %r11d, %eax
	0x44, 0x89, 0xde, //0x00001de3 movl         %r11d, %esi
	0x45, 0x85, 0xc0, //0x00001de6 testl        %r8d, %r8d
	0x4c, 0x8b, 0x6d, 0xd0, //0x00001de9 movq         $-48(%rbp), %r13
	0x0f, 0x84, 0xb6, 0xfa, 0xff, 0xff, //0x00001ded je           LBB0_334
	//0x00001df3 LBB0_415
	0x44, 0x89, 0xc1, //0x00001df3 movl         %r8d, %ecx
	0x48, 0x6b, 0xf1, 0x68, //0x00001df6 imulq        $104, %rcx, %rsi
	0x48, 0x8d, 0x3d, 0x3f, 0x44, 0x00, 0x00, //0x00001dfa leaq         $17471(%rip), %rdi  /* _LSHIFT_TAB+0(%rip) */
	0x8b, 0x1c, 0x3e, //0x00001e01 movl         (%rsi,%rdi), %ebx
	0x4c, 0x63, 0xe8, //0x00001e04 movslq       %eax, %r13
	0x8a, 0x54, 0x3e, 0x04, //0x00001e07 movb         $4(%rsi,%rdi), %dl
	0x45, 0x85, 0xed, //0x00001e0b testl        %r13d, %r13d
	0x4c, 0x89, 0x4d, 0xc8, //0x00001e0e movq         %r9, $-56(%rbp)
	0x0f, 0x84, 0x29, 0x00, 0x00, 0x00, //0x00001e12 je           LBB0_420
	0x48, 0x8d, 0x74, 0x3e, 0x05, //0x00001e18 leaq         $5(%rsi,%rdi), %rsi
	0x31, 0xff, //0x00001e1d xorl         %edi, %edi
	//0x00001e1f LBB0_417
	0x84, 0xd2, //0x00001e1f testb        %dl, %dl
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x00001e21 je           LBB0_422
	0x41, 0x38, 0x14, 0x3c, //0x00001e27 cmpb         %dl, (%r12,%rdi)
	0x0f, 0x85, 0xaf, 0x01, 0x00, 0x00, //0x00001e2b jne          LBB0_443
	0x0f, 0xb6, 0x14, 0x3e, //0x00001e31 movzbl       (%rsi,%rdi), %edx
	0x48, 0xff, 0xc7, //0x00001e35 incq         %rdi
	0x49, 0x39, 0xfd, //0x00001e38 cmpq         %rdi, %r13
	0x0f, 0x85, 0xde, 0xff, 0xff, 0xff, //0x00001e3b jne          LBB0_417
	//0x00001e41 LBB0_420
	0x84, 0xd2, //0x00001e41 testb        %dl, %dl
	0x0f, 0x84, 0x02, 0x00, 0x00, 0x00, //0x00001e43 je           LBB0_422
	//0x00001e49 LBB0_421
	0xff, 0xcb, //0x00001e49 decl         %ebx
	//0x00001e4b LBB0_422
	0x85, 0xc0, //0x00001e4b testl        %eax, %eax
	0x0f, 0x8e, 0xb5, 0x00, 0x00, 0x00, //0x00001e4d jle          LBB0_431
	0x89, 0x5d, 0x98, //0x00001e53 movl         %ebx, $-104(%rbp)
	0x01, 0xd8, //0x00001e56 addl         %ebx, %eax
	0x48, 0x98, //0x00001e58 cltq         
	0x49, 0x89, 0xc1, //0x00001e5a movq         %rax, %r9
	0x49, 0xc1, 0xe1, 0x20, //0x00001e5d shlq         $32, %r9
	0x48, 0xff, 0xc8, //0x00001e61 decq         %rax
	0x49, 0xff, 0xc5, //0x00001e64 incq         %r13
	0x31, 0xf6, //0x00001e67 xorl         %esi, %esi
	0xe9, 0x2a, 0x00, 0x00, 0x00, //0x00001e69 jmp          LBB0_426
	//0x00001e6e LBB0_424
	0x48, 0x85, 0xc0, //0x00001e6e testq        %rax, %rax
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00001e71 movl         $1, %eax
	0x44, 0x0f, 0x45, 0xf0, //0x00001e76 cmovnel      %eax, %r14d
	//0x00001e7a LBB0_425
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00001e7a movabsq      $-4294967296, %rax
	0x49, 0x01, 0xc1, //0x00001e84 addq         %rax, %r9
	0x48, 0x8d, 0x47, 0xff, //0x00001e87 leaq         $-1(%rdi), %rax
	0x49, 0xff, 0xcd, //0x00001e8b decq         %r13
	0x49, 0x83, 0xfd, 0x01, //0x00001e8e cmpq         $1, %r13
	0x0f, 0x8e, 0x4c, 0x00, 0x00, 0x00, //0x00001e92 jle          LBB0_428
	//0x00001e98 LBB0_426
	0x48, 0x89, 0xc7, //0x00001e98 movq         %rax, %rdi
	0x4b, 0x0f, 0xbe, 0x5c, 0x2c, 0xfe, //0x00001e9b movsbq       $-2(%r12,%r13), %rbx
	0x48, 0x83, 0xc3, 0xd0, //0x00001ea1 addq         $-48, %rbx
	0x48, 0xd3, 0xe3, //0x00001ea5 shlq         %cl, %rbx
	0x48, 0x01, 0xf3, //0x00001ea8 addq         %rsi, %rbx
	0x48, 0x89, 0xd8, //0x00001eab movq         %rbx, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00001eae movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x00001eb8 mulq         %rdx
	0x48, 0x89, 0xd6, //0x00001ebb movq         %rdx, %rsi
	0x48, 0xc1, 0xee, 0x03, //0x00001ebe shrq         $3, %rsi
	0x48, 0x8d, 0x04, 0x36, //0x00001ec2 leaq         (%rsi,%rsi), %rax
	0x48, 0x8d, 0x14, 0x80, //0x00001ec6 leaq         (%rax,%rax,4), %rdx
	0x48, 0x89, 0xd8, //0x00001eca movq         %rbx, %rax
	0x48, 0x29, 0xd0, //0x00001ecd subq         %rdx, %rax
	0x4c, 0x39, 0xff, //0x00001ed0 cmpq         %r15, %rdi
	0x0f, 0x83, 0x95, 0xff, 0xff, 0xff, //0x00001ed3 jae          LBB0_424
	0x04, 0x30, //0x00001ed9 addb         $48, %al
	0x41, 0x88, 0x04, 0x3c, //0x00001edb movb         %al, (%r12,%rdi)
	0xe9, 0x96, 0xff, 0xff, 0xff, //0x00001edf jmp          LBB0_425
	//0x00001ee4 LBB0_428
	0x48, 0x83, 0xfb, 0x0a, //0x00001ee4 cmpq         $10, %rbx
	0x4c, 0x8b, 0x6d, 0xd0, //0x00001ee8 movq         $-48(%rbp), %r13
	0x4c, 0x8b, 0x4d, 0xc8, //0x00001eec movq         $-56(%rbp), %r9
	0x0f, 0x83, 0x1f, 0x00, 0x00, 0x00, //0x00001ef0 jae          LBB0_432
	0x8b, 0x5d, 0x98, //0x00001ef6 movl         $-104(%rbp), %ebx
	0xe9, 0x82, 0x00, 0x00, 0x00, //0x00001ef9 jmp          LBB0_436
	//0x00001efe LBB0_430
	0x45, 0x31, 0xdb, //0x00001efe xorl         %r11d, %r11d
	0x31, 0xf6, //0x00001f01 xorl         %esi, %esi
	0xe9, 0xa1, 0xf9, 0xff, 0xff, //0x00001f03 jmp          LBB0_334
	//0x00001f08 LBB0_431
	0x4c, 0x8b, 0x6d, 0xd0, //0x00001f08 movq         $-48(%rbp), %r13
	0x4c, 0x8b, 0x4d, 0xc8, //0x00001f0c movq         $-56(%rbp), %r9
	0xe9, 0x6b, 0x00, 0x00, 0x00, //0x00001f10 jmp          LBB0_436
	//0x00001f15 LBB0_432
	0x48, 0x63, 0xcf, //0x00001f15 movslq       %edi, %rcx
	0x48, 0xff, 0xc9, //0x00001f18 decq         %rcx
	0x8b, 0x5d, 0x98, //0x00001f1b movl         $-104(%rbp), %ebx
	0xe9, 0x1c, 0x00, 0x00, 0x00, //0x00001f1e jmp          LBB0_434
	//0x00001f23 LBB0_433
	0x48, 0x85, 0xc0, //0x00001f23 testq        %rax, %rax
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00001f26 movl         $1, %eax
	0x44, 0x0f, 0x45, 0xf0, //0x00001f2b cmovnel      %eax, %r14d
	0x48, 0xff, 0xc9, //0x00001f2f decq         %rcx
	0x48, 0x83, 0xfe, 0x09, //0x00001f32 cmpq         $9, %rsi
	0x48, 0x89, 0xd6, //0x00001f36 movq         %rdx, %rsi
	0x0f, 0x86, 0x41, 0x00, 0x00, 0x00, //0x00001f39 jbe          LBB0_436
	//0x00001f3f LBB0_434
	0x48, 0x89, 0xf0, //0x00001f3f movq         %rsi, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00001f42 movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x00001f4c mulq         %rdx
	0x48, 0xc1, 0xea, 0x03, //0x00001f4f shrq         $3, %rdx
	0x48, 0x8d, 0x04, 0x12, //0x00001f53 leaq         (%rdx,%rdx), %rax
	0x48, 0x8d, 0x3c, 0x80, //0x00001f57 leaq         (%rax,%rax,4), %rdi
	0x48, 0x89, 0xf0, //0x00001f5b movq         %rsi, %rax
	0x48, 0x29, 0xf8, //0x00001f5e subq         %rdi, %rax
	0x4c, 0x39, 0xf9, //0x00001f61 cmpq         %r15, %rcx
	0x0f, 0x83, 0xb9, 0xff, 0xff, 0xff, //0x00001f64 jae          LBB0_433
	0x04, 0x30, //0x00001f6a addb         $48, %al
	0x41, 0x88, 0x04, 0x0c, //0x00001f6c movb         %al, (%r12,%rcx)
	0x48, 0xff, 0xc9, //0x00001f70 decq         %rcx
	0x48, 0x83, 0xfe, 0x09, //0x00001f73 cmpq         $9, %rsi
	0x48, 0x89, 0xd6, //0x00001f77 movq         %rdx, %rsi
	0x0f, 0x87, 0xbf, 0xff, 0xff, 0xff, //0x00001f7a ja           LBB0_434
	//0x00001f80 LBB0_436
	0x41, 0x01, 0xdb, //0x00001f80 addl         %ebx, %r11d
	0x4d, 0x63, 0xdb, //0x00001f83 movslq       %r11d, %r11
	0x4d, 0x39, 0xdf, //0x00001f86 cmpq         %r11, %r15
	0x45, 0x0f, 0x46, 0xdf, //0x00001f89 cmovbel      %r15d, %r11d
	0x41, 0x01, 0xd9, //0x00001f8d addl         %ebx, %r9d
	0x45, 0x85, 0xdb, //0x00001f90 testl        %r11d, %r11d
	0x0f, 0x8e, 0x57, 0x00, 0x00, 0x00, //0x00001f93 jle          LBB0_445
	0x44, 0x89, 0xd8, //0x00001f99 movl         %r11d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x00001f9c cmpb         $48, $-1(%rax,%r12)
	0x0f, 0x85, 0x4f, 0x00, 0x00, 0x00, //0x00001fa2 jne          LBB0_446
	//0x00001fa8 LBB0_438
	0x48, 0x83, 0xf8, 0x01, //0x00001fa8 cmpq         $1, %rax
	0x0f, 0x8e, 0x39, 0x00, 0x00, 0x00, //0x00001fac jle          LBB0_444
	0x4c, 0x8d, 0x58, 0xff, //0x00001fb2 leaq         $-1(%rax), %r11
	0x41, 0x80, 0x7c, 0x04, 0xfe, 0x30, //0x00001fb6 cmpb         $48, $-2(%r12,%rax)
	0x4c, 0x89, 0xd8, //0x00001fbc movq         %r11, %rax
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00001fbf je           LBB0_438
	0xe9, 0x2d, 0x00, 0x00, 0x00, //0x00001fc5 jmp          LBB0_446
	//0x00001fca LBB0_440
	0xff, 0xc8, //0x00001fca decl         %eax
	0x41, 0x89, 0xc3, //0x00001fcc movl         %eax, %r11d
	//0x00001fcf LBB0_441
	0x45, 0x85, 0xdb, //0x00001fcf testl        %r11d, %r11d
	0x0f, 0x84, 0x17, 0x01, 0x00, 0x00, //0x00001fd2 je           LBB0_454
	//0x00001fd8 LBB0_442
	0x44, 0x89, 0xde, //0x00001fd8 movl         %r11d, %esi
	0xe9, 0xc9, 0xf8, 0xff, 0xff, //0x00001fdb jmp          LBB0_334
	//0x00001fe0 LBB0_443
	0x0f, 0x8c, 0x63, 0xfe, 0xff, 0xff, //0x00001fe0 jl           LBB0_421
	0xe9, 0x60, 0xfe, 0xff, 0xff, //0x00001fe6 jmp          LBB0_422
	//0x00001feb LBB0_444
	0xff, 0xc8, //0x00001feb decl         %eax
	0x41, 0x89, 0xc3, //0x00001fed movl         %eax, %r11d
	//0x00001ff0 LBB0_445
	0x45, 0x85, 0xdb, //0x00001ff0 testl        %r11d, %r11d
	0x45, 0x0f, 0x44, 0xcb, //0x00001ff3 cmovel       %r11d, %r9d
	//0x00001ff7 LBB0_446
	0x44, 0x89, 0xd8, //0x00001ff7 movl         %r11d, %eax
	0x44, 0x89, 0xde, //0x00001ffa movl         %r11d, %esi
	0x45, 0x85, 0xc0, //0x00001ffd testl        %r8d, %r8d
	0x0f, 0x89, 0xa3, 0xf8, 0xff, 0xff, //0x00002000 jns          LBB0_334
	0xe9, 0xef, 0xf8, 0xff, 0xff, //0x00002006 jmp          LBB0_340
	//0x0000200b LBB0_447
	0x44, 0x89, 0xde, //0x0000200b movl         %r11d, %esi
	0xe9, 0xfc, 0x00, 0x00, 0x00, //0x0000200e jmp          LBB0_457
	//0x00002013 LBB0_628
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x01, //0x00002013 movabsq      $126100789566373888, %rdi
	//0x0000201d LBB0_629
	0x48, 0x89, 0xd8, //0x0000201d movq         %rbx, %rax
	0x48, 0xc1, 0xe8, 0x3f, //0x00002020 shrq         $63, %rax
	0x8d, 0x48, 0x09, //0x00002024 leal         $9(%rax), %ecx
	0x48, 0xd3, 0xeb, //0x00002027 shrq         %cl, %rbx
	0x48, 0x0b, 0x55, 0xb8, //0x0000202a orq          $-72(%rbp), %rdx
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x0000202e jne          LBB0_631
	0x89, 0xd9, //0x00002034 movl         %ebx, %ecx
	0x83, 0xe1, 0x03, //0x00002036 andl         $3, %ecx
	0x83, 0xf9, 0x01, //0x00002039 cmpl         $1, %ecx
	0x0f, 0x84, 0x47, 0xf4, 0xff, 0xff, //0x0000203c je           LBB0_268
	//0x00002042 LBB0_631
	0x4c, 0x2b, 0x75, 0xa0, //0x00002042 subq         $-96(%rbp), %r14
	0x48, 0x83, 0xf0, 0x01, //0x00002046 xorq         $1, %rax
	0x49, 0x29, 0xc6, //0x0000204a subq         %rax, %r14
	0x89, 0xd8, //0x0000204d movl         %ebx, %eax
	0x83, 0xe0, 0x01, //0x0000204f andl         $1, %eax
	0x48, 0x01, 0xd8, //0x00002052 addq         %rbx, %rax
	0x48, 0x21, 0xc7, //0x00002055 andq         %rax, %rdi
	0x48, 0x83, 0xff, 0x01, //0x00002058 cmpq         $1, %rdi
	0x49, 0x83, 0xde, 0xff, //0x0000205c sbbq         $-1, %r14
	0x49, 0x8d, 0x4e, 0xff, //0x00002060 leaq         $-1(%r14), %rcx
	0x48, 0x81, 0xf9, 0xfd, 0x07, 0x00, 0x00, //0x00002064 cmpq         $2045, %rcx
	0x0f, 0x87, 0x18, 0xf4, 0xff, 0xff, //0x0000206b ja           LBB0_268
	0x48, 0x83, 0xff, 0x01, //0x00002071 cmpq         $1, %rdi
	0xb1, 0x02, //0x00002075 movb         $2, %cl
	0x80, 0xd9, 0x00, //0x00002077 sbbb         $0, %cl
	0x48, 0xd3, 0xe8, //0x0000207a shrq         %cl, %rax
	0x49, 0xc1, 0xe6, 0x34, //0x0000207d shlq         $52, %r14
	0x48, 0xb9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00002081 movabsq      $4503599627370495, %rcx
	0x48, 0x21, 0xc8, //0x0000208b andq         %rcx, %rax
	0x4c, 0x09, 0xf0, //0x0000208e orq          %r14, %rax
	0x48, 0x89, 0xc1, //0x00002091 movq         %rax, %rcx
	0x4c, 0x09, 0xc9, //0x00002094 orq          %r9, %rcx
	0x83, 0x7d, 0xc8, 0xff, //0x00002097 cmpl         $-1, $-56(%rbp)
	0x48, 0x0f, 0x45, 0xc8, //0x0000209b cmovneq      %rax, %rcx
	0x66, 0x48, 0x0f, 0x6e, 0xc6, //0x0000209f movq         %rsi, %xmm0
	0x66, 0x48, 0x0f, 0x6e, 0xc9, //0x000020a4 movq         %rcx, %xmm1
	0x66, 0x0f, 0x2e, 0xc1, //0x000020a9 ucomisd      %xmm1, %xmm0
	0x0f, 0x85, 0xd6, 0xf3, 0xff, 0xff, //0x000020ad jne          LBB0_268
	0x0f, 0x8b, 0xa3, 0xf7, 0xff, 0xff, //0x000020b3 jnp          LBB0_328
	0xe9, 0xcb, 0xf3, 0xff, 0xff, //0x000020b9 jmp          LBB0_268
	//0x000020be LBB0_450
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x000020be movq         $-1, %r15
	0xe9, 0x7b, 0xef, 0xff, 0xff, //0x000020c5 jmp          LBB0_221
	//0x000020ca LBB0_452
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x000020ca movq         $-1, %r14
	0x49, 0x89, 0xda, //0x000020d1 movq         %rbx, %r10
	0x48, 0x89, 0xf0, //0x000020d4 movq         %rsi, %rax
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x000020d7 movq         $-1, %r12
	0x48, 0xc7, 0x45, 0xa8, 0xff, 0xff, 0xff, 0xff, //0x000020de movq         $-1, $-88(%rbp)
	0x4c, 0x8b, 0x6d, 0xd0, //0x000020e6 movq         $-48(%rbp), %r13
	0xe9, 0x40, 0xe4, 0xff, 0xff, //0x000020ea jmp          LBB0_64
	//0x000020ef LBB0_454
	0x8b, 0x45, 0xb8, //0x000020ef movl         $-72(%rbp), %eax
	0x03, 0x45, 0xc0, //0x000020f2 addl         $-64(%rbp), %eax
	0x45, 0x31, 0xc9, //0x000020f5 xorl         %r9d, %r9d
	0x45, 0x31, 0xdb, //0x000020f8 xorl         %r11d, %r11d
	0x31, 0xf6, //0x000020fb xorl         %esi, %esi
	0x89, 0xc7, //0x000020fd movl         %eax, %edi
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x000020ff jmp          LBB0_457
	//0x00002104 LBB0_455
	0x89, 0xf0, //0x00002104 movl         %esi, %eax
	//0x00002106 LBB0_456
	0x48, 0x8b, 0x7d, 0xc0, //0x00002106 movq         $-64(%rbp), %rdi
	0x2b, 0x7d, 0xb8, //0x0000210a subl         $-72(%rbp), %edi
	0x89, 0xc6, //0x0000210d movl         %eax, %esi
	//0x0000210f LBB0_457
	0x45, 0x85, 0xc9, //0x0000210f testl        %r9d, %r9d
	0x0f, 0x88, 0x16, 0x00, 0x00, 0x00, //0x00002112 js           LBB0_460
	0x0f, 0x85, 0xaa, 0x07, 0x00, 0x00, //0x00002118 jne          LBB0_573
	0x41, 0x80, 0x3c, 0x24, 0x35, //0x0000211e cmpb         $53, (%r12)
	0x0f, 0x8c, 0x14, 0x00, 0x00, 0x00, //0x00002123 jl           LBB0_461
	0xe9, 0x9a, 0x07, 0x00, 0x00, //0x00002129 jmp          LBB0_573
	//0x0000212e LBB0_460
	0xb8, 0x1b, 0x00, 0x00, 0x00, //0x0000212e movl         $27, %eax
	0x41, 0x83, 0xf9, 0xf8, //0x00002133 cmpl         $-8, %r9d
	0x0f, 0x8c, 0x11, 0x00, 0x00, 0x00, //0x00002137 jl           LBB0_462
	//0x0000213d LBB0_461
	0x44, 0x89, 0xc8, //0x0000213d movl         %r9d, %eax
	0xf7, 0xd8, //0x00002140 negl         %eax
	0x48, 0x98, //0x00002142 cltq         
	0x48, 0x8d, 0x0d, 0xc5, 0x40, 0x00, 0x00, //0x00002144 leaq         $16581(%rip), %rcx  /* _POW_TAB+0(%rip) */
	0x8b, 0x04, 0x81, //0x0000214b movl         (%rcx,%rax,4), %eax
	//0x0000214e LBB0_462
	0x85, 0xf6, //0x0000214e testl        %esi, %esi
	0x48, 0x89, 0x7d, 0xc0, //0x00002150 movq         %rdi, $-64(%rbp)
	0x89, 0x45, 0xb8, //0x00002154 movl         %eax, $-72(%rbp)
	0x0f, 0x84, 0xa7, 0xff, 0xff, 0xff, //0x00002157 je           LBB0_455
	0x85, 0xc0, //0x0000215d testl        %eax, %eax
	0x0f, 0x84, 0x9f, 0xff, 0xff, 0xff, //0x0000215f je           LBB0_455
	0x0f, 0x8e, 0x32, 0x02, 0x00, 0x00, //0x00002165 jle          LBB0_495
	0x41, 0x89, 0xc0, //0x0000216b movl         %eax, %r8d
	0x83, 0xf8, 0x3d, //0x0000216e cmpl         $61, %eax
	0x0f, 0x8c, 0x41, 0x02, 0x00, 0x00, //0x00002171 jl           LBB0_497
	0x4c, 0x8d, 0x2d, 0xc2, 0x40, 0x00, 0x00, //0x00002177 leaq         $16578(%rip), %r13  /* _LSHIFT_TAB+0(%rip) */
	0xe9, 0x20, 0x00, 0x00, 0x00, //0x0000217e jmp          LBB0_470
	//0x00002183 LBB0_467
	0xff, 0xc8, //0x00002183 decl         %eax
	0x41, 0x89, 0xc3, //0x00002185 movl         %eax, %r11d
	//0x00002188 LBB0_468
	0x45, 0x85, 0xdb, //0x00002188 testl        %r11d, %r11d
	0x45, 0x0f, 0x44, 0xcb, //0x0000218b cmovel       %r11d, %r9d
	//0x0000218f LBB0_469
	0x48, 0x8b, 0x45, 0xc8, //0x0000218f movq         $-56(%rbp), %rax
	0x44, 0x8d, 0x40, 0xc4, //0x00002193 leal         $-60(%rax), %r8d
	0x44, 0x89, 0xde, //0x00002197 movl         %r11d, %esi
	0x83, 0xf8, 0x78, //0x0000219a cmpl         $120, %eax
	0x0f, 0x8e, 0x02, 0x02, 0x00, 0x00, //0x0000219d jle          LBB0_496
	//0x000021a3 LBB0_470
	0x4c, 0x89, 0x45, 0xc8, //0x000021a3 movq         %r8, $-56(%rbp)
	0x48, 0x63, 0xde, //0x000021a7 movslq       %esi, %rbx
	0x85, 0xdb, //0x000021aa testl        %ebx, %ebx
	0x0f, 0x84, 0x45, 0x00, 0x00, 0x00, //0x000021ac je           LBB0_476
	0xb1, 0x38, //0x000021b2 movb         $56, %cl
	0x31, 0xc0, //0x000021b4 xorl         %eax, %eax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000021b6 .p2align 4, 0x90
	//0x000021c0 LBB0_472
	0x41, 0xb8, 0x13, 0x00, 0x00, 0x00, //0x000021c0 movl         $19, %r8d
	0x48, 0x83, 0xf8, 0x2a, //0x000021c6 cmpq         $42, %rax
	0x0f, 0x84, 0x2d, 0x00, 0x00, 0x00, //0x000021ca je           LBB0_477
	0x41, 0x38, 0x0c, 0x04, //0x000021d0 cmpb         %cl, (%r12,%rax)
	0x0f, 0x85, 0xb8, 0x01, 0x00, 0x00, //0x000021d4 jne          LBB0_493
	0x42, 0x0f, 0xb6, 0x8c, 0x28, 0x65, 0x18, 0x00, 0x00, //0x000021da movzbl       $6245(%rax,%r13), %ecx
	0x48, 0xff, 0xc0, //0x000021e3 incq         %rax
	0x48, 0x39, 0xc3, //0x000021e6 cmpq         %rax, %rbx
	0x0f, 0x85, 0xd1, 0xff, 0xff, 0xff, //0x000021e9 jne          LBB0_472
	0x84, 0xc9, //0x000021ef testb        %cl, %cl
	0x0f, 0x84, 0x06, 0x00, 0x00, 0x00, //0x000021f1 je           LBB0_477
	//0x000021f7 LBB0_476
	0x41, 0xb8, 0x12, 0x00, 0x00, 0x00, //0x000021f7 movl         $18, %r8d
	//0x000021fd LBB0_477
	0x85, 0xf6, //0x000021fd testl        %esi, %esi
	0x0f, 0x8e, 0x38, 0x01, 0x00, 0x00, //0x000021ff jle          LBB0_489
	0x4d, 0x89, 0xcd, //0x00002205 movq         %r9, %r13
	0x44, 0x01, 0xc6, //0x00002208 addl         %r8d, %esi
	0x48, 0x63, 0xc6, //0x0000220b movslq       %esi, %rax
	0x48, 0x89, 0xc7, //0x0000220e movq         %rax, %rdi
	0x48, 0xc1, 0xe7, 0x20, //0x00002211 shlq         $32, %rdi
	0x48, 0xff, 0xc8, //0x00002215 decq         %rax
	0x48, 0xff, 0xc3, //0x00002218 incq         %rbx
	0x31, 0xc9, //0x0000221b xorl         %ecx, %ecx
	0xe9, 0x38, 0x00, 0x00, 0x00, //0x0000221d jmp          LBB0_481
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002222 .p2align 4, 0x90
	//0x00002230 LBB0_479
	0x48, 0x85, 0xc0, //0x00002230 testq        %rax, %rax
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00002233 movl         $1, %eax
	0x44, 0x0f, 0x45, 0xf0, //0x00002238 cmovnel      %eax, %r14d
	//0x0000223c LBB0_480
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x0000223c movabsq      $-4294967296, %rax
	0x48, 0x01, 0xc7, //0x00002246 addq         %rax, %rdi
	0x49, 0x8d, 0x41, 0xff, //0x00002249 leaq         $-1(%r9), %rax
	0x48, 0xff, 0xcb, //0x0000224d decq         %rbx
	0x48, 0x83, 0xfb, 0x01, //0x00002250 cmpq         $1, %rbx
	0x0f, 0x8e, 0x49, 0x00, 0x00, 0x00, //0x00002254 jle          LBB0_483
	//0x0000225a LBB0_481
	0x49, 0x89, 0xc1, //0x0000225a movq         %rax, %r9
	0x41, 0x0f, 0xb6, 0x74, 0x1c, 0xfe, //0x0000225d movzbl       $-2(%r12,%rbx), %esi
	0x48, 0xc1, 0xe6, 0x3c, //0x00002263 shlq         $60, %rsi
	0x48, 0x01, 0xce, //0x00002267 addq         %rcx, %rsi
	0x48, 0x89, 0xf0, //0x0000226a movq         %rsi, %rax
	0x48, 0xb9, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x0000226d movabsq      $-3689348814741910323, %rcx
	0x48, 0xf7, 0xe1, //0x00002277 mulq         %rcx
	0x48, 0x89, 0xd1, //0x0000227a movq         %rdx, %rcx
	0x48, 0xc1, 0xe9, 0x03, //0x0000227d shrq         $3, %rcx
	0x48, 0x8d, 0x04, 0x09, //0x00002281 leaq         (%rcx,%rcx), %rax
	0x48, 0x8d, 0x14, 0x80, //0x00002285 leaq         (%rax,%rax,4), %rdx
	0x48, 0x89, 0xf0, //0x00002289 movq         %rsi, %rax
	0x48, 0x29, 0xd0, //0x0000228c subq         %rdx, %rax
	0x4d, 0x39, 0xf9, //0x0000228f cmpq         %r15, %r9
	0x0f, 0x83, 0x98, 0xff, 0xff, 0xff, //0x00002292 jae          LBB0_479
	0x04, 0x30, //0x00002298 addb         $48, %al
	0x43, 0x88, 0x04, 0x0c, //0x0000229a movb         %al, (%r12,%r9)
	0xe9, 0x99, 0xff, 0xff, 0xff, //0x0000229e jmp          LBB0_480
	//0x000022a3 LBB0_483
	0x48, 0x83, 0xfe, 0x0a, //0x000022a3 cmpq         $10, %rsi
	0x0f, 0x83, 0x0f, 0x00, 0x00, 0x00, //0x000022a7 jae          LBB0_485
	0x4d, 0x89, 0xe9, //0x000022ad movq         %r13, %r9
	0x4c, 0x8d, 0x2d, 0x89, 0x3f, 0x00, 0x00, //0x000022b0 leaq         $16265(%rip), %r13  /* _LSHIFT_TAB+0(%rip) */
	0xe9, 0x81, 0x00, 0x00, 0x00, //0x000022b7 jmp          LBB0_489
	//0x000022bc LBB0_485
	0x49, 0x63, 0xf1, //0x000022bc movslq       %r9d, %rsi
	0x48, 0xff, 0xce, //0x000022bf decq         %rsi
	0x4d, 0x89, 0xe9, //0x000022c2 movq         %r13, %r9
	0x4c, 0x8d, 0x2d, 0x74, 0x3f, 0x00, 0x00, //0x000022c5 leaq         $16244(%rip), %r13  /* _LSHIFT_TAB+0(%rip) */
	0xe9, 0x2b, 0x00, 0x00, 0x00, //0x000022cc jmp          LBB0_487
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000022d1 .p2align 4, 0x90
	//0x000022e0 LBB0_486
	0x48, 0x85, 0xc0, //0x000022e0 testq        %rax, %rax
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x000022e3 movl         $1, %eax
	0x44, 0x0f, 0x45, 0xf0, //0x000022e8 cmovnel      %eax, %r14d
	0x48, 0xff, 0xce, //0x000022ec decq         %rsi
	0x48, 0x83, 0xf9, 0x09, //0x000022ef cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x000022f3 movq         %rdx, %rcx
	0x0f, 0x86, 0x41, 0x00, 0x00, 0x00, //0x000022f6 jbe          LBB0_489
	//0x000022fc LBB0_487
	0x48, 0x89, 0xc8, //0x000022fc movq         %rcx, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x000022ff movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x00002309 mulq         %rdx
	0x48, 0xc1, 0xea, 0x03, //0x0000230c shrq         $3, %rdx
	0x48, 0x8d, 0x04, 0x12, //0x00002310 leaq         (%rdx,%rdx), %rax
	0x48, 0x8d, 0x3c, 0x80, //0x00002314 leaq         (%rax,%rax,4), %rdi
	0x48, 0x89, 0xc8, //0x00002318 movq         %rcx, %rax
	0x48, 0x29, 0xf8, //0x0000231b subq         %rdi, %rax
	0x4c, 0x39, 0xfe, //0x0000231e cmpq         %r15, %rsi
	0x0f, 0x83, 0xb9, 0xff, 0xff, 0xff, //0x00002321 jae          LBB0_486
	0x04, 0x30, //0x00002327 addb         $48, %al
	0x41, 0x88, 0x04, 0x34, //0x00002329 movb         %al, (%r12,%rsi)
	0x48, 0xff, 0xce, //0x0000232d decq         %rsi
	0x48, 0x83, 0xf9, 0x09, //0x00002330 cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x00002334 movq         %rdx, %rcx
	0x0f, 0x87, 0xbf, 0xff, 0xff, 0xff, //0x00002337 ja           LBB0_487
	//0x0000233d LBB0_489
	0x45, 0x01, 0xc3, //0x0000233d addl         %r8d, %r11d
	0x4d, 0x63, 0xdb, //0x00002340 movslq       %r11d, %r11
	0x4d, 0x39, 0xdf, //0x00002343 cmpq         %r11, %r15
	0x45, 0x0f, 0x46, 0xdf, //0x00002346 cmovbel      %r15d, %r11d
	0x45, 0x01, 0xc1, //0x0000234a addl         %r8d, %r9d
	0x45, 0x85, 0xdb, //0x0000234d testl        %r11d, %r11d
	0x0f, 0x8e, 0x32, 0xfe, 0xff, 0xff, //0x00002350 jle          LBB0_468
	0x44, 0x89, 0xd8, //0x00002356 movl         %r11d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x00002359 cmpb         $48, $-1(%rax,%r12)
	0x0f, 0x85, 0x2a, 0xfe, 0xff, 0xff, //0x0000235f jne          LBB0_469
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002365 .p2align 4, 0x90
	//0x00002370 LBB0_491
	0x48, 0x83, 0xf8, 0x01, //0x00002370 cmpq         $1, %rax
	0x0f, 0x8e, 0x09, 0xfe, 0xff, 0xff, //0x00002374 jle          LBB0_467
	0x4c, 0x8d, 0x58, 0xff, //0x0000237a leaq         $-1(%rax), %r11
	0x41, 0x80, 0x7c, 0x04, 0xfe, 0x30, //0x0000237e cmpb         $48, $-2(%r12,%rax)
	0x4c, 0x89, 0xd8, //0x00002384 movq         %r11, %rax
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00002387 je           LBB0_491
	0xe9, 0xfd, 0xfd, 0xff, 0xff, //0x0000238d jmp          LBB0_469
	//0x00002392 LBB0_493
	0x0f, 0x8c, 0x5f, 0xfe, 0xff, 0xff, //0x00002392 jl           LBB0_476
	0xe9, 0x60, 0xfe, 0xff, 0xff, //0x00002398 jmp          LBB0_477
	//0x0000239d LBB0_495
	0x41, 0x89, 0xc0, //0x0000239d movl         %eax, %r8d
	0xe9, 0x07, 0x02, 0x00, 0x00, //0x000023a0 jmp          LBB0_525
	//0x000023a5 LBB0_496
	0x44, 0x89, 0xde, //0x000023a5 movl         %r11d, %esi
	0x44, 0x89, 0xd8, //0x000023a8 movl         %r11d, %eax
	0x45, 0x85, 0xc0, //0x000023ab testl        %r8d, %r8d
	0x4c, 0x8b, 0x6d, 0xd0, //0x000023ae movq         $-48(%rbp), %r13
	0x0f, 0x84, 0x4e, 0xfd, 0xff, 0xff, //0x000023b2 je           LBB0_456
	//0x000023b8 LBB0_497
	0x44, 0x89, 0xc1, //0x000023b8 movl         %r8d, %ecx
	0x48, 0x6b, 0xd1, 0x68, //0x000023bb imulq        $104, %rcx, %rdx
	0x48, 0x8d, 0x3d, 0x7a, 0x3e, 0x00, 0x00, //0x000023bf leaq         $15994(%rip), %rdi  /* _LSHIFT_TAB+0(%rip) */
	0x8b, 0x1c, 0x3a, //0x000023c6 movl         (%rdx,%rdi), %ebx
	0x4c, 0x63, 0xee, //0x000023c9 movslq       %esi, %r13
	0x8a, 0x44, 0x3a, 0x04, //0x000023cc movb         $4(%rdx,%rdi), %al
	0x45, 0x85, 0xed, //0x000023d0 testl        %r13d, %r13d
	0x4c, 0x89, 0x4d, 0xc8, //0x000023d3 movq         %r9, $-56(%rbp)
	0x0f, 0x84, 0x29, 0x00, 0x00, 0x00, //0x000023d7 je           LBB0_502
	0x48, 0x8d, 0x54, 0x3a, 0x05, //0x000023dd leaq         $5(%rdx,%rdi), %rdx
	0x31, 0xff, //0x000023e2 xorl         %edi, %edi
	//0x000023e4 LBB0_499
	0x84, 0xc0, //0x000023e4 testb        %al, %al
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x000023e6 je           LBB0_504
	0x41, 0x38, 0x04, 0x3c, //0x000023ec cmpb         %al, (%r12,%rdi)
	0x0f, 0x85, 0x90, 0x01, 0x00, 0x00, //0x000023f0 jne          LBB0_521
	0x0f, 0xb6, 0x04, 0x3a, //0x000023f6 movzbl       (%rdx,%rdi), %eax
	0x48, 0xff, 0xc7, //0x000023fa incq         %rdi
	0x49, 0x39, 0xfd, //0x000023fd cmpq         %rdi, %r13
	0x0f, 0x85, 0xde, 0xff, 0xff, 0xff, //0x00002400 jne          LBB0_499
	//0x00002406 LBB0_502
	0x84, 0xc0, //0x00002406 testb        %al, %al
	0x0f, 0x84, 0x02, 0x00, 0x00, 0x00, //0x00002408 je           LBB0_504
	//0x0000240e LBB0_503
	0xff, 0xcb, //0x0000240e decl         %ebx
	//0x00002410 LBB0_504
	0x85, 0xf6, //0x00002410 testl        %esi, %esi
	0x0f, 0x8e, 0xac, 0x00, 0x00, 0x00, //0x00002412 jle          LBB0_512
	0x89, 0x5d, 0x98, //0x00002418 movl         %ebx, $-104(%rbp)
	0x01, 0xde, //0x0000241b addl         %ebx, %esi
	0x48, 0x63, 0xc6, //0x0000241d movslq       %esi, %rax
	0x49, 0x89, 0xc1, //0x00002420 movq         %rax, %r9
	0x49, 0xc1, 0xe1, 0x20, //0x00002423 shlq         $32, %r9
	0x48, 0xff, 0xc8, //0x00002427 decq         %rax
	0x49, 0xff, 0xc5, //0x0000242a incq         %r13
	0x31, 0xf6, //0x0000242d xorl         %esi, %esi
	0xe9, 0x2a, 0x00, 0x00, 0x00, //0x0000242f jmp          LBB0_508
	//0x00002434 LBB0_506
	0x48, 0x85, 0xc0, //0x00002434 testq        %rax, %rax
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00002437 movl         $1, %eax
	0x44, 0x0f, 0x45, 0xf0, //0x0000243c cmovnel      %eax, %r14d
	//0x00002440 LBB0_507
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00002440 movabsq      $-4294967296, %rax
	0x49, 0x01, 0xc1, //0x0000244a addq         %rax, %r9
	0x48, 0x8d, 0x47, 0xff, //0x0000244d leaq         $-1(%rdi), %rax
	0x49, 0xff, 0xcd, //0x00002451 decq         %r13
	0x49, 0x83, 0xfd, 0x01, //0x00002454 cmpq         $1, %r13
	0x0f, 0x8e, 0x4c, 0x00, 0x00, 0x00, //0x00002458 jle          LBB0_510
	//0x0000245e LBB0_508
	0x48, 0x89, 0xc7, //0x0000245e movq         %rax, %rdi
	0x4b, 0x0f, 0xbe, 0x5c, 0x2c, 0xfe, //0x00002461 movsbq       $-2(%r12,%r13), %rbx
	0x48, 0x83, 0xc3, 0xd0, //0x00002467 addq         $-48, %rbx
	0x48, 0xd3, 0xe3, //0x0000246b shlq         %cl, %rbx
	0x48, 0x01, 0xf3, //0x0000246e addq         %rsi, %rbx
	0x48, 0x89, 0xd8, //0x00002471 movq         %rbx, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00002474 movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x0000247e mulq         %rdx
	0x48, 0x89, 0xd6, //0x00002481 movq         %rdx, %rsi
	0x48, 0xc1, 0xee, 0x03, //0x00002484 shrq         $3, %rsi
	0x48, 0x8d, 0x04, 0x36, //0x00002488 leaq         (%rsi,%rsi), %rax
	0x48, 0x8d, 0x14, 0x80, //0x0000248c leaq         (%rax,%rax,4), %rdx
	0x48, 0x89, 0xd8, //0x00002490 movq         %rbx, %rax
	0x48, 0x29, 0xd0, //0x00002493 subq         %rdx, %rax
	0x4c, 0x39, 0xff, //0x00002496 cmpq         %r15, %rdi
	0x0f, 0x83, 0x95, 0xff, 0xff, 0xff, //0x00002499 jae          LBB0_506
	0x04, 0x30, //0x0000249f addb         $48, %al
	0x41, 0x88, 0x04, 0x3c, //0x000024a1 movb         %al, (%r12,%rdi)
	0xe9, 0x96, 0xff, 0xff, 0xff, //0x000024a5 jmp          LBB0_507
	//0x000024aa LBB0_510
	0x48, 0x83, 0xfb, 0x0a, //0x000024aa cmpq         $10, %rbx
	0x4c, 0x8b, 0x6d, 0xd0, //0x000024ae movq         $-48(%rbp), %r13
	0x4c, 0x8b, 0x4d, 0xc8, //0x000024b2 movq         $-56(%rbp), %r9
	0x0f, 0x83, 0x15, 0x00, 0x00, 0x00, //0x000024b6 jae          LBB0_513
	0x8b, 0x5d, 0x98, //0x000024bc movl         $-104(%rbp), %ebx
	0xe9, 0x78, 0x00, 0x00, 0x00, //0x000024bf jmp          LBB0_517
	//0x000024c4 LBB0_512
	0x4c, 0x8b, 0x6d, 0xd0, //0x000024c4 movq         $-48(%rbp), %r13
	0x4c, 0x8b, 0x4d, 0xc8, //0x000024c8 movq         $-56(%rbp), %r9
	0xe9, 0x6b, 0x00, 0x00, 0x00, //0x000024cc jmp          LBB0_517
	//0x000024d1 LBB0_513
	0x48, 0x63, 0xcf, //0x000024d1 movslq       %edi, %rcx
	0x48, 0xff, 0xc9, //0x000024d4 decq         %rcx
	0x8b, 0x5d, 0x98, //0x000024d7 movl         $-104(%rbp), %ebx
	0xe9, 0x1c, 0x00, 0x00, 0x00, //0x000024da jmp          LBB0_515
	//0x000024df LBB0_514
	0x48, 0x85, 0xc0, //0x000024df testq        %rax, %rax
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x000024e2 movl         $1, %eax
	0x44, 0x0f, 0x45, 0xf0, //0x000024e7 cmovnel      %eax, %r14d
	0x48, 0xff, 0xc9, //0x000024eb decq         %rcx
	0x48, 0x83, 0xfe, 0x09, //0x000024ee cmpq         $9, %rsi
	0x48, 0x89, 0xd6, //0x000024f2 movq         %rdx, %rsi
	0x0f, 0x86, 0x41, 0x00, 0x00, 0x00, //0x000024f5 jbe          LBB0_517
	//0x000024fb LBB0_515
	0x48, 0x89, 0xf0, //0x000024fb movq         %rsi, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x000024fe movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x00002508 mulq         %rdx
	0x48, 0xc1, 0xea, 0x03, //0x0000250b shrq         $3, %rdx
	0x48, 0x8d, 0x04, 0x12, //0x0000250f leaq         (%rdx,%rdx), %rax
	0x48, 0x8d, 0x3c, 0x80, //0x00002513 leaq         (%rax,%rax,4), %rdi
	0x48, 0x89, 0xf0, //0x00002517 movq         %rsi, %rax
	0x48, 0x29, 0xf8, //0x0000251a subq         %rdi, %rax
	0x4c, 0x39, 0xf9, //0x0000251d cmpq         %r15, %rcx
	0x0f, 0x83, 0xb9, 0xff, 0xff, 0xff, //0x00002520 jae          LBB0_514
	0x04, 0x30, //0x00002526 addb         $48, %al
	0x41, 0x88, 0x04, 0x0c, //0x00002528 movb         %al, (%r12,%rcx)
	0x48, 0xff, 0xc9, //0x0000252c decq         %rcx
	0x48, 0x83, 0xfe, 0x09, //0x0000252f cmpq         $9, %rsi
	0x48, 0x89, 0xd6, //0x00002533 movq         %rdx, %rsi
	0x0f, 0x87, 0xbf, 0xff, 0xff, 0xff, //0x00002536 ja           LBB0_515
	//0x0000253c LBB0_517
	0x41, 0x01, 0xdb, //0x0000253c addl         %ebx, %r11d
	0x4d, 0x63, 0xdb, //0x0000253f movslq       %r11d, %r11
	0x4d, 0x39, 0xdf, //0x00002542 cmpq         %r11, %r15
	0x45, 0x0f, 0x46, 0xdf, //0x00002545 cmovbel      %r15d, %r11d
	0x41, 0x01, 0xd9, //0x00002549 addl         %ebx, %r9d
	0x45, 0x85, 0xdb, //0x0000254c testl        %r11d, %r11d
	0x0f, 0x8e, 0x41, 0x00, 0x00, 0x00, //0x0000254f jle          LBB0_523
	0x44, 0x89, 0xd8, //0x00002555 movl         %r11d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x00002558 cmpb         $48, $-1(%rax,%r12)
	0x0f, 0x85, 0x39, 0x00, 0x00, 0x00, //0x0000255e jne          LBB0_524
	//0x00002564 LBB0_519
	0x48, 0x83, 0xf8, 0x01, //0x00002564 cmpq         $1, %rax
	0x0f, 0x8e, 0x23, 0x00, 0x00, 0x00, //0x00002568 jle          LBB0_522
	0x4c, 0x8d, 0x58, 0xff, //0x0000256e leaq         $-1(%rax), %r11
	0x41, 0x80, 0x7c, 0x04, 0xfe, 0x30, //0x00002572 cmpb         $48, $-2(%r12,%rax)
	0x4c, 0x89, 0xd8, //0x00002578 movq         %r11, %rax
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x0000257b je           LBB0_519
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00002581 jmp          LBB0_524
	//0x00002586 LBB0_521
	0x0f, 0x8c, 0x82, 0xfe, 0xff, 0xff, //0x00002586 jl           LBB0_503
	0xe9, 0x7f, 0xfe, 0xff, 0xff, //0x0000258c jmp          LBB0_504
	//0x00002591 LBB0_522
	0xff, 0xc8, //0x00002591 decl         %eax
	0x41, 0x89, 0xc3, //0x00002593 movl         %eax, %r11d
	//0x00002596 LBB0_523
	0x45, 0x85, 0xdb, //0x00002596 testl        %r11d, %r11d
	0x45, 0x0f, 0x44, 0xcb, //0x00002599 cmovel       %r11d, %r9d
	//0x0000259d LBB0_524
	0x44, 0x89, 0xde, //0x0000259d movl         %r11d, %esi
	0x44, 0x89, 0xd8, //0x000025a0 movl         %r11d, %eax
	0x45, 0x85, 0xc0, //0x000025a3 testl        %r8d, %r8d
	0x0f, 0x89, 0x5a, 0xfb, 0xff, 0xff, //0x000025a6 jns          LBB0_456
	//0x000025ac LBB0_525
	0x41, 0x83, 0xf8, 0xc3, //0x000025ac cmpl         $-61, %r8d
	0x0f, 0x8e, 0x21, 0x00, 0x00, 0x00, //0x000025b0 jle          LBB0_529
	0xe9, 0x9e, 0x01, 0x00, 0x00, //0x000025b6 jmp          LBB0_549
	//0x000025bb LBB0_526
	0xff, 0xc9, //0x000025bb decl         %ecx
	0x41, 0x89, 0xcb, //0x000025bd movl         %ecx, %r11d
	//0x000025c0 LBB0_527
	0x45, 0x85, 0xdb, //0x000025c0 testl        %r11d, %r11d
	0x45, 0x0f, 0x44, 0xcb, //0x000025c3 cmovel       %r11d, %r9d
	//0x000025c7 LBB0_528
	0x44, 0x8d, 0x40, 0x3c, //0x000025c7 leal         $60(%rax), %r8d
	0x44, 0x89, 0xde, //0x000025cb movl         %r11d, %esi
	0x83, 0xf8, 0x88, //0x000025ce cmpl         $-120, %eax
	0x0f, 0x8d, 0x73, 0x01, 0x00, 0x00, //0x000025d1 jge          LBB0_548
	//0x000025d7 LBB0_529
	0x44, 0x89, 0xc0, //0x000025d7 movl         %r8d, %eax
	0x48, 0x63, 0xfe, //0x000025da movslq       %esi, %rdi
	0x31, 0xd2, //0x000025dd xorl         %edx, %edx
	0x31, 0xc9, //0x000025df xorl         %ecx, %ecx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000025e1 .p2align 4, 0x90
	//0x000025f0 LBB0_530
	0x48, 0x39, 0xfa, //0x000025f0 cmpq         %rdi, %rdx
	0x0f, 0x8d, 0x23, 0x00, 0x00, 0x00, //0x000025f3 jge          LBB0_532
	0x48, 0x8d, 0x0c, 0x89, //0x000025f9 leaq         (%rcx,%rcx,4), %rcx
	0x49, 0x0f, 0xbe, 0x1c, 0x14, //0x000025fd movsbq       (%r12,%rdx), %rbx
	0x48, 0x8d, 0x4c, 0x4b, 0xd0, //0x00002602 leaq         $-48(%rbx,%rcx,2), %rcx
	0x48, 0xff, 0xc2, //0x00002607 incq         %rdx
	0x49, 0x8d, 0x5a, 0x01, //0x0000260a leaq         $1(%r10), %rbx
	0x48, 0x39, 0xd9, //0x0000260e cmpq         %rbx, %rcx
	0x0f, 0x82, 0xd9, 0xff, 0xff, 0xff, //0x00002611 jb           LBB0_530
	0xe9, 0x2a, 0x00, 0x00, 0x00, //0x00002617 jmp          LBB0_534
	//0x0000261c LBB0_532
	0x48, 0x85, 0xc9, //0x0000261c testq        %rcx, %rcx
	0x0f, 0x84, 0x1d, 0x01, 0x00, 0x00, //0x0000261f je           LBB0_547
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002625 .p2align 4, 0x90
	//0x00002630 LBB0_533
	0x48, 0x01, 0xc9, //0x00002630 addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x00002633 leaq         (%rcx,%rcx,4), %rcx
	0xff, 0xc2, //0x00002637 incl         %edx
	0x49, 0x8d, 0x7a, 0x01, //0x00002639 leaq         $1(%r10), %rdi
	0x48, 0x39, 0xf9, //0x0000263d cmpq         %rdi, %rcx
	0x0f, 0x82, 0xea, 0xff, 0xff, 0xff, //0x00002640 jb           LBB0_533
	//0x00002646 LBB0_534
	0x41, 0x29, 0xd1, //0x00002646 subl         %edx, %r9d
	0x31, 0xff, //0x00002649 xorl         %edi, %edi
	0x39, 0xf2, //0x0000264b cmpl         %esi, %edx
	0x0f, 0x8d, 0x49, 0x00, 0x00, 0x00, //0x0000264d jge          LBB0_539
	0x48, 0x63, 0xd2, //0x00002653 movslq       %edx, %rdx
	0x49, 0x63, 0xf3, //0x00002656 movslq       %r11d, %rsi
	0x49, 0x8d, 0x3c, 0x14, //0x00002659 leaq         (%r12,%rdx), %rdi
	0x45, 0x31, 0xdb, //0x0000265d xorl         %r11d, %r11d
	//0x00002660 .p2align 4, 0x90
	//0x00002660 LBB0_536
	0x48, 0x89, 0xcb, //0x00002660 movq         %rcx, %rbx
	0x48, 0xc1, 0xeb, 0x3c, //0x00002663 shrq         $60, %rbx
	0x4c, 0x21, 0xd1, //0x00002667 andq         %r10, %rcx
	0x80, 0xcb, 0x30, //0x0000266a orb          $48, %bl
	0x43, 0x88, 0x1c, 0x1c, //0x0000266d movb         %bl, (%r12,%r11)
	0x48, 0x8d, 0x0c, 0x89, //0x00002671 leaq         (%rcx,%rcx,4), %rcx
	0x4a, 0x0f, 0xbe, 0x1c, 0x1f, //0x00002675 movsbq       (%rdi,%r11), %rbx
	0x48, 0x8d, 0x4c, 0x4b, 0xd0, //0x0000267a leaq         $-48(%rbx,%rcx,2), %rcx
	0x4a, 0x8d, 0x5c, 0x1a, 0x01, //0x0000267f leaq         $1(%rdx,%r11), %rbx
	0x49, 0xff, 0xc3, //0x00002684 incq         %r11
	0x48, 0x39, 0xf3, //0x00002687 cmpq         %rsi, %rbx
	0x0f, 0x8c, 0xd0, 0xff, 0xff, 0xff, //0x0000268a jl           LBB0_536
	0x48, 0x85, 0xc9, //0x00002690 testq        %rcx, %rcx
	0x0f, 0x84, 0x65, 0x00, 0x00, 0x00, //0x00002693 je           LBB0_543
	0x44, 0x89, 0xdf, //0x00002699 movl         %r11d, %edi
	//0x0000269c LBB0_539
	0x41, 0x89, 0xfb, //0x0000269c movl         %edi, %r11d
	0xe9, 0x28, 0x00, 0x00, 0x00, //0x0000269f jmp          LBB0_541
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000026a4 .p2align 4, 0x90
	//0x000026b0 LBB0_540
	0x48, 0x85, 0xd2, //0x000026b0 testq        %rdx, %rdx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x000026b3 movl         $1, %edx
	0x44, 0x0f, 0x45, 0xf2, //0x000026b8 cmovnel      %edx, %r14d
	0x48, 0x01, 0xc9, //0x000026bc addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x000026bf leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xc9, //0x000026c3 testq        %rcx, %rcx
	0x0f, 0x84, 0x32, 0x00, 0x00, 0x00, //0x000026c6 je           LBB0_543
	//0x000026cc LBB0_541
	0x48, 0x89, 0xca, //0x000026cc movq         %rcx, %rdx
	0x48, 0xc1, 0xea, 0x3c, //0x000026cf shrq         $60, %rdx
	0x4c, 0x21, 0xd1, //0x000026d3 andq         %r10, %rcx
	0x49, 0x63, 0xf3, //0x000026d6 movslq       %r11d, %rsi
	0x49, 0x39, 0xf7, //0x000026d9 cmpq         %rsi, %r15
	0x0f, 0x86, 0xce, 0xff, 0xff, 0xff, //0x000026dc jbe          LBB0_540
	0x80, 0xca, 0x30, //0x000026e2 orb          $48, %dl
	0x41, 0x88, 0x14, 0x34, //0x000026e5 movb         %dl, (%r12,%rsi)
	0xff, 0xc6, //0x000026e9 incl         %esi
	0x41, 0x89, 0xf3, //0x000026eb movl         %esi, %r11d
	0x48, 0x01, 0xc9, //0x000026ee addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x000026f1 leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xc9, //0x000026f5 testq        %rcx, %rcx
	0x0f, 0x85, 0xce, 0xff, 0xff, 0xff, //0x000026f8 jne          LBB0_541
	//0x000026fe LBB0_543
	0x41, 0xff, 0xc1, //0x000026fe incl         %r9d
	0x45, 0x85, 0xdb, //0x00002701 testl        %r11d, %r11d
	0x0f, 0x8e, 0xb6, 0xfe, 0xff, 0xff, //0x00002704 jle          LBB0_527
	0x44, 0x89, 0xd9, //0x0000270a movl         %r11d, %ecx
	0x42, 0x80, 0x7c, 0x21, 0xff, 0x30, //0x0000270d cmpb         $48, $-1(%rcx,%r12)
	0x0f, 0x85, 0xae, 0xfe, 0xff, 0xff, //0x00002713 jne          LBB0_528
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002719 .p2align 4, 0x90
	//0x00002720 LBB0_545
	0x48, 0x83, 0xf9, 0x01, //0x00002720 cmpq         $1, %rcx
	0x0f, 0x8e, 0x91, 0xfe, 0xff, 0xff, //0x00002724 jle          LBB0_526
	0x4c, 0x8d, 0x59, 0xff, //0x0000272a leaq         $-1(%rcx), %r11
	0x41, 0x80, 0x7c, 0x0c, 0xfe, 0x30, //0x0000272e cmpb         $48, $-2(%r12,%rcx)
	0x4c, 0x89, 0xd9, //0x00002734 movq         %r11, %rcx
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00002737 je           LBB0_545
	0xe9, 0x85, 0xfe, 0xff, 0xff, //0x0000273d jmp          LBB0_528
	//0x00002742 LBB0_547
	0x45, 0x31, 0xdb, //0x00002742 xorl         %r11d, %r11d
	0xe9, 0x7d, 0xfe, 0xff, 0xff, //0x00002745 jmp          LBB0_528
	//0x0000274a LBB0_548
	0x44, 0x89, 0xde, //0x0000274a movl         %r11d, %esi
	0x44, 0x89, 0xd8, //0x0000274d movl         %r11d, %eax
	0x45, 0x85, 0xc0, //0x00002750 testl        %r8d, %r8d
	0x0f, 0x84, 0xad, 0xf9, 0xff, 0xff, //0x00002753 je           LBB0_456
	//0x00002759 LBB0_549
	0x41, 0xf7, 0xd8, //0x00002759 negl         %r8d
	0x48, 0x63, 0xfe, //0x0000275c movslq       %esi, %rdi
	0x31, 0xd2, //0x0000275f xorl         %edx, %edx
	0x31, 0xc0, //0x00002761 xorl         %eax, %eax
	//0x00002763 LBB0_550
	0x48, 0x39, 0xfa, //0x00002763 cmpq         %rdi, %rdx
	0x0f, 0x8d, 0x28, 0x00, 0x00, 0x00, //0x00002766 jge          LBB0_552
	0x48, 0x8d, 0x04, 0x80, //0x0000276c leaq         (%rax,%rax,4), %rax
	0x49, 0x0f, 0xbe, 0x0c, 0x14, //0x00002770 movsbq       (%r12,%rdx), %rcx
	0x48, 0x8d, 0x44, 0x41, 0xd0, //0x00002775 leaq         $-48(%rcx,%rax,2), %rax
	0x48, 0xff, 0xc2, //0x0000277a incq         %rdx
	0x48, 0x89, 0xc3, //0x0000277d movq         %rax, %rbx
	0x44, 0x89, 0xc1, //0x00002780 movl         %r8d, %ecx
	0x48, 0xd3, 0xeb, //0x00002783 shrq         %cl, %rbx
	0x48, 0x85, 0xdb, //0x00002786 testq        %rbx, %rbx
	0x0f, 0x84, 0xd4, 0xff, 0xff, 0xff, //0x00002789 je           LBB0_550
	0xe9, 0x2e, 0x00, 0x00, 0x00, //0x0000278f jmp          LBB0_556
	//0x00002794 LBB0_552
	0x48, 0x85, 0xc0, //0x00002794 testq        %rax, %rax
	0x0f, 0x85, 0x13, 0x00, 0x00, 0x00, //0x00002797 jne          LBB0_555
	0x45, 0x31, 0xdb, //0x0000279d xorl         %r11d, %r11d
	0x31, 0xc0, //0x000027a0 xorl         %eax, %eax
	0xe9, 0x5f, 0xf9, 0xff, 0xff, //0x000027a2 jmp          LBB0_456
	//0x000027a7 LBB0_554
	0x48, 0x01, 0xc0, //0x000027a7 addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x000027aa leaq         (%rax,%rax,4), %rax
	0xff, 0xc2, //0x000027ae incl         %edx
	//0x000027b0 LBB0_555
	0x48, 0x89, 0xc7, //0x000027b0 movq         %rax, %rdi
	0x44, 0x89, 0xc1, //0x000027b3 movl         %r8d, %ecx
	0x48, 0xd3, 0xef, //0x000027b6 shrq         %cl, %rdi
	0x48, 0x85, 0xff, //0x000027b9 testq        %rdi, %rdi
	0x0f, 0x84, 0xe5, 0xff, 0xff, 0xff, //0x000027bc je           LBB0_554
	//0x000027c2 LBB0_556
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000027c2 movq         $-1, %rbx
	0x44, 0x89, 0xc1, //0x000027c9 movl         %r8d, %ecx
	0x48, 0xd3, 0xe3, //0x000027cc shlq         %cl, %rbx
	0x48, 0xf7, 0xd3, //0x000027cf notq         %rbx
	0x31, 0xff, //0x000027d2 xorl         %edi, %edi
	0x39, 0xf2, //0x000027d4 cmpl         %esi, %edx
	0x0f, 0x8d, 0x4b, 0x00, 0x00, 0x00, //0x000027d6 jge          LBB0_560
	0x4c, 0x89, 0x4d, 0xc8, //0x000027dc movq         %r9, $-56(%rbp)
	0x4c, 0x63, 0xea, //0x000027e0 movslq       %edx, %r13
	0x4d, 0x63, 0xcb, //0x000027e3 movslq       %r11d, %r9
	0x4f, 0x8d, 0x1c, 0x2c, //0x000027e6 leaq         (%r12,%r13), %r11
	0x31, 0xff, //0x000027ea xorl         %edi, %edi
	//0x000027ec LBB0_558
	0x48, 0x89, 0xc6, //0x000027ec movq         %rax, %rsi
	0x44, 0x89, 0xc1, //0x000027ef movl         %r8d, %ecx
	0x48, 0xd3, 0xee, //0x000027f2 shrq         %cl, %rsi
	0x48, 0x21, 0xd8, //0x000027f5 andq         %rbx, %rax
	0x40, 0x80, 0xc6, 0x30, //0x000027f8 addb         $48, %sil
	0x41, 0x88, 0x34, 0x3c, //0x000027fc movb         %sil, (%r12,%rdi)
	0x48, 0x8d, 0x04, 0x80, //0x00002800 leaq         (%rax,%rax,4), %rax
	0x49, 0x0f, 0xbe, 0x0c, 0x3b, //0x00002804 movsbq       (%r11,%rdi), %rcx
	0x48, 0x8d, 0x44, 0x41, 0xd0, //0x00002809 leaq         $-48(%rcx,%rax,2), %rax
	0x49, 0x8d, 0x4c, 0x3d, 0x01, //0x0000280e leaq         $1(%r13,%rdi), %rcx
	0x48, 0xff, 0xc7, //0x00002813 incq         %rdi
	0x4c, 0x39, 0xc9, //0x00002816 cmpq         %r9, %rcx
	0x0f, 0x8c, 0xcd, 0xff, 0xff, 0xff, //0x00002819 jl           LBB0_558
	0x4c, 0x8b, 0x6d, 0xd0, //0x0000281f movq         $-48(%rbp), %r13
	0x4c, 0x8b, 0x4d, 0xc8, //0x00002823 movq         $-56(%rbp), %r9
	//0x00002827 LBB0_560
	0x41, 0x29, 0xd1, //0x00002827 subl         %edx, %r9d
	0x41, 0x89, 0xfb, //0x0000282a movl         %edi, %r11d
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x0000282d jmp          LBB0_563
	//0x00002832 LBB0_561
	0x48, 0x85, 0xd2, //0x00002832 testq        %rdx, %rdx
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x00002835 movl         $1, %ecx
	0x44, 0x0f, 0x45, 0xf1, //0x0000283a cmovnel      %ecx, %r14d
	//0x0000283e LBB0_562
	0x48, 0x01, 0xc0, //0x0000283e addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x00002841 leaq         (%rax,%rax,4), %rax
	//0x00002845 LBB0_563
	0x48, 0x85, 0xc0, //0x00002845 testq        %rax, %rax
	0x0f, 0x84, 0x29, 0x00, 0x00, 0x00, //0x00002848 je           LBB0_566
	0x48, 0x89, 0xc2, //0x0000284e movq         %rax, %rdx
	0x44, 0x89, 0xc1, //0x00002851 movl         %r8d, %ecx
	0x48, 0xd3, 0xea, //0x00002854 shrq         %cl, %rdx
	0x48, 0x21, 0xd8, //0x00002857 andq         %rbx, %rax
	0x49, 0x63, 0xcb, //0x0000285a movslq       %r11d, %rcx
	0x49, 0x39, 0xcf, //0x0000285d cmpq         %rcx, %r15
	0x0f, 0x86, 0xcc, 0xff, 0xff, 0xff, //0x00002860 jbe          LBB0_561
	0x80, 0xc2, 0x30, //0x00002866 addb         $48, %dl
	0x41, 0x88, 0x14, 0x0c, //0x00002869 movb         %dl, (%r12,%rcx)
	0xff, 0xc1, //0x0000286d incl         %ecx
	0x41, 0x89, 0xcb, //0x0000286f movl         %ecx, %r11d
	0xe9, 0xc7, 0xff, 0xff, 0xff, //0x00002872 jmp          LBB0_562
	//0x00002877 LBB0_566
	0x41, 0xff, 0xc1, //0x00002877 incl         %r9d
	0x45, 0x85, 0xdb, //0x0000287a testl        %r11d, %r11d
	0x0f, 0x8e, 0x36, 0x00, 0x00, 0x00, //0x0000287d jle          LBB0_571
	0x44, 0x89, 0xd8, //0x00002883 movl         %r11d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x00002886 cmpb         $48, $-1(%rax,%r12)
	0x0f, 0x85, 0x2e, 0x00, 0x00, 0x00, //0x0000288c jne          LBB0_572
	//0x00002892 LBB0_568
	0x48, 0x83, 0xf8, 0x01, //0x00002892 cmpq         $1, %rax
	0x0f, 0x8e, 0x18, 0x00, 0x00, 0x00, //0x00002896 jle          LBB0_570
	0x4c, 0x8d, 0x58, 0xff, //0x0000289c leaq         $-1(%rax), %r11
	0x41, 0x80, 0x7c, 0x04, 0xfe, 0x30, //0x000028a0 cmpb         $48, $-2(%r12,%rax)
	0x4c, 0x89, 0xd8, //0x000028a6 movq         %r11, %rax
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x000028a9 je           LBB0_568
	0xe9, 0x0c, 0x00, 0x00, 0x00, //0x000028af jmp          LBB0_572
	//0x000028b4 LBB0_570
	0xff, 0xc8, //0x000028b4 decl         %eax
	0x41, 0x89, 0xc3, //0x000028b6 movl         %eax, %r11d
	//0x000028b9 LBB0_571
	0x45, 0x85, 0xdb, //0x000028b9 testl        %r11d, %r11d
	0x45, 0x0f, 0x44, 0xcb, //0x000028bc cmovel       %r11d, %r9d
	//0x000028c0 LBB0_572
	0x44, 0x89, 0xd8, //0x000028c0 movl         %r11d, %eax
	0xe9, 0x3e, 0xf8, 0xff, 0xff, //0x000028c3 jmp          LBB0_456
	//0x000028c8 LBB0_573
	0x81, 0xff, 0x02, 0xfc, 0xff, 0xff, //0x000028c8 cmpl         $-1022, %edi
	0x4c, 0x89, 0x4d, 0xc8, //0x000028ce movq         %r9, $-56(%rbp)
	0x0f, 0x8f, 0x9c, 0x01, 0x00, 0x00, //0x000028d2 jg           LBB0_599
	0x41, 0xb9, 0x02, 0xfc, 0xff, 0xff, //0x000028d8 movl         $-1022, %r9d
	0x85, 0xf6, //0x000028de testl        %esi, %esi
	0x0f, 0x84, 0xb5, 0x01, 0x00, 0x00, //0x000028e0 je           LBB0_602
	0x8d, 0x8f, 0xfd, 0x03, 0x00, 0x00, //0x000028e6 leal         $1021(%rdi), %ecx
	0x81, 0xff, 0xc6, 0xfb, 0xff, 0xff, //0x000028ec cmpl         $-1082, %edi
	0x0f, 0x8f, 0xb5, 0x01, 0x00, 0x00, //0x000028f2 jg           LBB0_605
	0x49, 0x8d, 0x42, 0x01, //0x000028f8 leaq         $1(%r10), %rax
	0x41, 0xbd, 0x01, 0x00, 0x00, 0x00, //0x000028fc movl         $1, %r13d
	0xe9, 0x25, 0x00, 0x00, 0x00, //0x00002902 jmp          LBB0_580
	//0x00002907 LBB0_577
	0xff, 0xc9, //0x00002907 decl         %ecx
	0x41, 0x89, 0xcb, //0x00002909 movl         %ecx, %r11d
	//0x0000290c LBB0_578
	0x45, 0x85, 0xdb, //0x0000290c testl        %r11d, %r11d
	0x48, 0x8b, 0x4d, 0xc8, //0x0000290f movq         $-56(%rbp), %rcx
	0x41, 0x0f, 0x44, 0xcb, //0x00002913 cmovel       %r11d, %ecx
	0x48, 0x89, 0x4d, 0xc8, //0x00002917 movq         %rcx, $-56(%rbp)
	//0x0000291b LBB0_579
	0x41, 0x8d, 0x48, 0x3c, //0x0000291b leal         $60(%r8), %ecx
	0x44, 0x89, 0xde, //0x0000291f movl         %r11d, %esi
	0x41, 0x83, 0xf8, 0x88, //0x00002922 cmpl         $-120, %r8d
	0x0f, 0x8d, 0x76, 0x01, 0x00, 0x00, //0x00002926 jge          LBB0_603
	//0x0000292c LBB0_580
	0x41, 0x89, 0xc8, //0x0000292c movl         %ecx, %r8d
	0x48, 0x63, 0xde, //0x0000292f movslq       %esi, %rbx
	0x31, 0xff, //0x00002932 xorl         %edi, %edi
	0x31, 0xc9, //0x00002934 xorl         %ecx, %ecx
	//0x00002936 LBB0_581
	0x48, 0x39, 0xdf, //0x00002936 cmpq         %rbx, %rdi
	0x0f, 0x8d, 0x1f, 0x00, 0x00, 0x00, //0x00002939 jge          LBB0_583
	0x48, 0x8d, 0x0c, 0x89, //0x0000293f leaq         (%rcx,%rcx,4), %rcx
	0x49, 0x0f, 0xbe, 0x14, 0x3c, //0x00002943 movsbq       (%r12,%rdi), %rdx
	0x48, 0x8d, 0x4c, 0x4a, 0xd0, //0x00002948 leaq         $-48(%rdx,%rcx,2), %rcx
	0x48, 0xff, 0xc7, //0x0000294d incq         %rdi
	0x48, 0x39, 0xc1, //0x00002950 cmpq         %rax, %rcx
	0x0f, 0x82, 0xdd, 0xff, 0xff, 0xff, //0x00002953 jb           LBB0_581
	0xe9, 0x1b, 0x00, 0x00, 0x00, //0x00002959 jmp          LBB0_585
	//0x0000295e LBB0_583
	0x48, 0x85, 0xc9, //0x0000295e testq        %rcx, %rcx
	0x0f, 0x84, 0x05, 0x01, 0x00, 0x00, //0x00002961 je           LBB0_598
	//0x00002967 LBB0_584
	0x48, 0x01, 0xc9, //0x00002967 addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x0000296a leaq         (%rcx,%rcx,4), %rcx
	0xff, 0xc7, //0x0000296e incl         %edi
	0x48, 0x39, 0xc1, //0x00002970 cmpq         %rax, %rcx
	0x0f, 0x82, 0xee, 0xff, 0xff, 0xff, //0x00002973 jb           LBB0_584
	//0x00002979 LBB0_585
	0x48, 0x8b, 0x55, 0xc8, //0x00002979 movq         $-56(%rbp), %rdx
	0x29, 0xfa, //0x0000297d subl         %edi, %edx
	0x48, 0x89, 0x55, 0xc8, //0x0000297f movq         %rdx, $-56(%rbp)
	0x31, 0xdb, //0x00002983 xorl         %ebx, %ebx
	0x39, 0xf7, //0x00002985 cmpl         %esi, %edi
	0x0f, 0x8d, 0x49, 0x00, 0x00, 0x00, //0x00002987 jge          LBB0_590
	0x48, 0x63, 0xf7, //0x0000298d movslq       %edi, %rsi
	0x49, 0x63, 0xfb, //0x00002990 movslq       %r11d, %rdi
	0x49, 0x8d, 0x1c, 0x34, //0x00002993 leaq         (%r12,%rsi), %rbx
	0x45, 0x31, 0xdb, //0x00002997 xorl         %r11d, %r11d
	//0x0000299a LBB0_587
	0x48, 0x89, 0xca, //0x0000299a movq         %rcx, %rdx
	0x48, 0xc1, 0xea, 0x3c, //0x0000299d shrq         $60, %rdx
	0x4c, 0x21, 0xd1, //0x000029a1 andq         %r10, %rcx
	0x80, 0xca, 0x30, //0x000029a4 orb          $48, %dl
	0x43, 0x88, 0x14, 0x1c, //0x000029a7 movb         %dl, (%r12,%r11)
	0x48, 0x8d, 0x0c, 0x89, //0x000029ab leaq         (%rcx,%rcx,4), %rcx
	0x4a, 0x0f, 0xbe, 0x14, 0x1b, //0x000029af movsbq       (%rbx,%r11), %rdx
	0x48, 0x8d, 0x4c, 0x4a, 0xd0, //0x000029b4 leaq         $-48(%rdx,%rcx,2), %rcx
	0x4a, 0x8d, 0x54, 0x1e, 0x01, //0x000029b9 leaq         $1(%rsi,%r11), %rdx
	0x49, 0xff, 0xc3, //0x000029be incq         %r11
	0x48, 0x39, 0xfa, //0x000029c1 cmpq         %rdi, %rdx
	0x0f, 0x8c, 0xd0, 0xff, 0xff, 0xff, //0x000029c4 jl           LBB0_587
	0x48, 0x85, 0xc9, //0x000029ca testq        %rcx, %rcx
	0x0f, 0x84, 0x55, 0x00, 0x00, 0x00, //0x000029cd je           LBB0_594
	0x44, 0x89, 0xdb, //0x000029d3 movl         %r11d, %ebx
	//0x000029d6 LBB0_590
	0x41, 0x89, 0xdb, //0x000029d6 movl         %ebx, %r11d
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x000029d9 jmp          LBB0_592
	//0x000029de LBB0_591
	0x48, 0x85, 0xf6, //0x000029de testq        %rsi, %rsi
	0x45, 0x0f, 0x45, 0xf5, //0x000029e1 cmovnel      %r13d, %r14d
	0x48, 0x01, 0xc9, //0x000029e5 addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x000029e8 leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xc9, //0x000029ec testq        %rcx, %rcx
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x000029ef je           LBB0_594
	//0x000029f5 LBB0_592
	0x48, 0x89, 0xce, //0x000029f5 movq         %rcx, %rsi
	0x48, 0xc1, 0xee, 0x3c, //0x000029f8 shrq         $60, %rsi
	0x4c, 0x21, 0xd1, //0x000029fc andq         %r10, %rcx
	0x49, 0x63, 0xfb, //0x000029ff movslq       %r11d, %rdi
	0x49, 0x39, 0xff, //0x00002a02 cmpq         %rdi, %r15
	0x0f, 0x86, 0xd3, 0xff, 0xff, 0xff, //0x00002a05 jbe          LBB0_591
	0x40, 0x80, 0xce, 0x30, //0x00002a0b orb          $48, %sil
	0x41, 0x88, 0x34, 0x3c, //0x00002a0f movb         %sil, (%r12,%rdi)
	0xff, 0xc7, //0x00002a13 incl         %edi
	0x41, 0x89, 0xfb, //0x00002a15 movl         %edi, %r11d
	0x48, 0x01, 0xc9, //0x00002a18 addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x00002a1b leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xc9, //0x00002a1f testq        %rcx, %rcx
	0x0f, 0x85, 0xcd, 0xff, 0xff, 0xff, //0x00002a22 jne          LBB0_592
	//0x00002a28 LBB0_594
	0x48, 0x8b, 0x4d, 0xc8, //0x00002a28 movq         $-56(%rbp), %rcx
	0xff, 0xc1, //0x00002a2c incl         %ecx
	0x48, 0x89, 0x4d, 0xc8, //0x00002a2e movq         %rcx, $-56(%rbp)
	0x45, 0x85, 0xdb, //0x00002a32 testl        %r11d, %r11d
	0x0f, 0x8e, 0xd1, 0xfe, 0xff, 0xff, //0x00002a35 jle          LBB0_578
	0x44, 0x89, 0xd9, //0x00002a3b movl         %r11d, %ecx
	0x42, 0x80, 0x7c, 0x21, 0xff, 0x30, //0x00002a3e cmpb         $48, $-1(%rcx,%r12)
	0x0f, 0x85, 0xd1, 0xfe, 0xff, 0xff, //0x00002a44 jne          LBB0_579
	//0x00002a4a LBB0_596
	0x48, 0x83, 0xf9, 0x01, //0x00002a4a cmpq         $1, %rcx
	0x0f, 0x8e, 0xb3, 0xfe, 0xff, 0xff, //0x00002a4e jle          LBB0_577
	0x4c, 0x8d, 0x59, 0xff, //0x00002a54 leaq         $-1(%rcx), %r11
	0x41, 0x80, 0x7c, 0x0c, 0xfe, 0x30, //0x00002a58 cmpb         $48, $-2(%r12,%rcx)
	0x4c, 0x89, 0xd9, //0x00002a5e movq         %r11, %rcx
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00002a61 je           LBB0_596
	0xe9, 0xaf, 0xfe, 0xff, 0xff, //0x00002a67 jmp          LBB0_579
	//0x00002a6c LBB0_598
	0x45, 0x31, 0xdb, //0x00002a6c xorl         %r11d, %r11d
	0xe9, 0xa7, 0xfe, 0xff, 0xff, //0x00002a6f jmp          LBB0_579
	//0x00002a74 LBB0_599
	0x81, 0xff, 0x00, 0x04, 0x00, 0x00, //0x00002a74 cmpl         $1024, %edi
	0x0f, 0x8e, 0x11, 0x00, 0x00, 0x00, //0x00002a7a jle          LBB0_601
	0x31, 0xff, //0x00002a80 xorl         %edi, %edi
	0x49, 0xb9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, //0x00002a82 movabsq      $9218868437227405312, %r9
	0xe9, 0xd4, 0x04, 0x00, 0x00, //0x00002a8c jmp          LBB0_688
	//0x00002a91 LBB0_601
	0xff, 0xcf, //0x00002a91 decl         %edi
	0x41, 0x89, 0xf9, //0x00002a93 movl         %edi, %r9d
	0xe9, 0x87, 0x01, 0x00, 0x00, //0x00002a96 jmp          LBB0_621
	//0x00002a9b LBB0_602
	0x31, 0xc0, //0x00002a9b xorl         %eax, %eax
	0xe9, 0x72, 0x03, 0x00, 0x00, //0x00002a9d jmp          LBB0_662
	//0x00002aa2 LBB0_603
	0x85, 0xc9, //0x00002aa2 testl        %ecx, %ecx
	0x0f, 0x84, 0x6f, 0x01, 0x00, 0x00, //0x00002aa4 je           LBB0_620
	0x44, 0x89, 0xde, //0x00002aaa movl         %r11d, %esi
	//0x00002aad LBB0_605
	0xf7, 0xd9, //0x00002aad negl         %ecx
	0x48, 0x63, 0xfe, //0x00002aaf movslq       %esi, %rdi
	0x31, 0xd2, //0x00002ab2 xorl         %edx, %edx
	0x31, 0xc0, //0x00002ab4 xorl         %eax, %eax
	//0x00002ab6 LBB0_606
	0x48, 0x39, 0xfa, //0x00002ab6 cmpq         %rdi, %rdx
	0x0f, 0x8d, 0x25, 0x00, 0x00, 0x00, //0x00002ab9 jge          LBB0_623
	0x48, 0x8d, 0x04, 0x80, //0x00002abf leaq         (%rax,%rax,4), %rax
	0x49, 0x0f, 0xbe, 0x1c, 0x14, //0x00002ac3 movsbq       (%r12,%rdx), %rbx
	0x48, 0x8d, 0x44, 0x43, 0xd0, //0x00002ac8 leaq         $-48(%rbx,%rax,2), %rax
	0x48, 0xff, 0xc2, //0x00002acd incq         %rdx
	0x48, 0x89, 0xc3, //0x00002ad0 movq         %rax, %rbx
	0x48, 0xd3, 0xeb, //0x00002ad3 shrq         %cl, %rbx
	0x48, 0x85, 0xdb, //0x00002ad6 testq        %rbx, %rbx
	0x0f, 0x84, 0xd7, 0xff, 0xff, 0xff, //0x00002ad9 je           LBB0_606
	0xe9, 0x30, 0x00, 0x00, 0x00, //0x00002adf jmp          LBB0_608
	//0x00002ae4 LBB0_623
	0x48, 0x85, 0xc0, //0x00002ae4 testq        %rax, %rax
	0x0f, 0x84, 0x45, 0x01, 0x00, 0x00, //0x00002ae7 je           LBB0_626
	0x48, 0x89, 0xc7, //0x00002aed movq         %rax, %rdi
	0x48, 0xd3, 0xef, //0x00002af0 shrq         %cl, %rdi
	0x48, 0x85, 0xff, //0x00002af3 testq        %rdi, %rdi
	0x0f, 0x85, 0x18, 0x00, 0x00, 0x00, //0x00002af6 jne          LBB0_608
	//0x00002afc LBB0_625
	0x48, 0x01, 0xc0, //0x00002afc addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x00002aff leaq         (%rax,%rax,4), %rax
	0xff, 0xc2, //0x00002b03 incl         %edx
	0x48, 0x89, 0xc7, //0x00002b05 movq         %rax, %rdi
	0x48, 0xd3, 0xef, //0x00002b08 shrq         %cl, %rdi
	0x48, 0x85, 0xff, //0x00002b0b testq        %rdi, %rdi
	0x0f, 0x84, 0xe8, 0xff, 0xff, 0xff, //0x00002b0e je           LBB0_625
	//0x00002b14 LBB0_608
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00002b14 movq         $-1, %rbx
	0x48, 0xd3, 0xe3, //0x00002b1b shlq         %cl, %rbx
	0x48, 0xf7, 0xd3, //0x00002b1e notq         %rbx
	0x45, 0x31, 0xc0, //0x00002b21 xorl         %r8d, %r8d
	0x39, 0xf2, //0x00002b24 cmpl         %esi, %edx
	0x0f, 0x8d, 0x3d, 0x00, 0x00, 0x00, //0x00002b26 jge          LBB0_611
	0x4c, 0x63, 0xd2, //0x00002b2c movslq       %edx, %r10
	0x4d, 0x63, 0xcb, //0x00002b2f movslq       %r11d, %r9
	0x4b, 0x8d, 0x3c, 0x14, //0x00002b32 leaq         (%r12,%r10), %rdi
	0x45, 0x31, 0xc0, //0x00002b36 xorl         %r8d, %r8d
	//0x00002b39 LBB0_610
	0x48, 0x89, 0xc6, //0x00002b39 movq         %rax, %rsi
	0x48, 0xd3, 0xee, //0x00002b3c shrq         %cl, %rsi
	0x48, 0x21, 0xd8, //0x00002b3f andq         %rbx, %rax
	0x40, 0x80, 0xc6, 0x30, //0x00002b42 addb         $48, %sil
	0x43, 0x88, 0x34, 0x04, //0x00002b46 movb         %sil, (%r12,%r8)
	0x48, 0x8d, 0x04, 0x80, //0x00002b4a leaq         (%rax,%rax,4), %rax
	0x4a, 0x0f, 0xbe, 0x34, 0x07, //0x00002b4e movsbq       (%rdi,%r8), %rsi
	0x48, 0x8d, 0x44, 0x46, 0xd0, //0x00002b53 leaq         $-48(%rsi,%rax,2), %rax
	0x4b, 0x8d, 0x74, 0x02, 0x01, //0x00002b58 leaq         $1(%r10,%r8), %rsi
	0x49, 0xff, 0xc0, //0x00002b5d incq         %r8
	0x4c, 0x39, 0xce, //0x00002b60 cmpq         %r9, %rsi
	0x0f, 0x8c, 0xd0, 0xff, 0xff, 0xff, //0x00002b63 jl           LBB0_610
	//0x00002b69 LBB0_611
	0x48, 0x8b, 0x75, 0xc8, //0x00002b69 movq         $-56(%rbp), %rsi
	0x29, 0xd6, //0x00002b6d subl         %edx, %esi
	0x48, 0x89, 0x75, 0xc8, //0x00002b6f movq         %rsi, $-56(%rbp)
	0x48, 0x85, 0xc0, //0x00002b73 testq        %rax, %rax
	0x0f, 0x84, 0x58, 0x00, 0x00, 0x00, //0x00002b76 je           LBB0_616
	0x41, 0xb9, 0x01, 0x00, 0x00, 0x00, //0x00002b7c movl         $1, %r9d
	0x48, 0x8b, 0x55, 0xa8, //0x00002b82 movq         $-88(%rbp), %rdx
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00002b86 jmp          LBB0_614
	//0x00002b8b LBB0_613
	0x48, 0x85, 0xf6, //0x00002b8b testq        %rsi, %rsi
	0x45, 0x0f, 0x45, 0xf1, //0x00002b8e cmovnel      %r9d, %r14d
	0x48, 0x01, 0xc0, //0x00002b92 addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x00002b95 leaq         (%rax,%rax,4), %rax
	0x48, 0x85, 0xc0, //0x00002b99 testq        %rax, %rax
	0x0f, 0x84, 0x32, 0x00, 0x00, 0x00, //0x00002b9c je           LBB0_616
	//0x00002ba2 LBB0_614
	0x48, 0x89, 0xc6, //0x00002ba2 movq         %rax, %rsi
	0x48, 0xd3, 0xee, //0x00002ba5 shrq         %cl, %rsi
	0x48, 0x21, 0xd8, //0x00002ba8 andq         %rbx, %rax
	0x49, 0x63, 0xf8, //0x00002bab movslq       %r8d, %rdi
	0x48, 0x39, 0xfa, //0x00002bae cmpq         %rdi, %rdx
	0x0f, 0x86, 0xd4, 0xff, 0xff, 0xff, //0x00002bb1 jbe          LBB0_613
	0x40, 0x80, 0xc6, 0x30, //0x00002bb7 addb         $48, %sil
	0x41, 0x88, 0x34, 0x3c, //0x00002bbb movb         %sil, (%r12,%rdi)
	0xff, 0xc7, //0x00002bbf incl         %edi
	0x41, 0x89, 0xf8, //0x00002bc1 movl         %edi, %r8d
	0x48, 0x01, 0xc0, //0x00002bc4 addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x00002bc7 leaq         (%rax,%rax,4), %rax
	0x48, 0x85, 0xc0, //0x00002bcb testq        %rax, %rax
	0x0f, 0x85, 0xce, 0xff, 0xff, 0xff, //0x00002bce jne          LBB0_614
	//0x00002bd4 LBB0_616
	0x48, 0x8b, 0x45, 0xc8, //0x00002bd4 movq         $-56(%rbp), %rax
	0xff, 0xc0, //0x00002bd8 incl         %eax
	0x48, 0x89, 0x45, 0xc8, //0x00002bda movq         %rax, $-56(%rbp)
	0x45, 0x85, 0xc0, //0x00002bde testl        %r8d, %r8d
	0x0f, 0x8e, 0x57, 0x00, 0x00, 0x00, //0x00002be1 jle          LBB0_634
	0x44, 0x89, 0xc0, //0x00002be7 movl         %r8d, %eax
	0x41, 0xb9, 0x02, 0xfc, 0xff, 0xff, //0x00002bea movl         $-1022, %r9d
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x00002bf0 cmpb         $48, $-1(%rax,%r12)
	0x0f, 0x85, 0x51, 0x00, 0x00, 0x00, //0x00002bf6 jne          LBB0_635
	//0x00002bfc LBB0_618
	0x48, 0x83, 0xf8, 0x01, //0x00002bfc cmpq         $1, %rax
	0x0f, 0x8e, 0x33, 0x00, 0x00, 0x00, //0x00002c00 jle          LBB0_633
	0x4c, 0x8d, 0x58, 0xff, //0x00002c06 leaq         $-1(%rax), %r11
	0x41, 0x80, 0x7c, 0x04, 0xfe, 0x30, //0x00002c0a cmpb         $48, $-2(%r12,%rax)
	0x4c, 0x89, 0xd8, //0x00002c10 movq         %r11, %rax
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00002c13 je           LBB0_618
	//0x00002c19 LBB0_620
	0x41, 0xb9, 0x02, 0xfc, 0xff, 0xff, //0x00002c19 movl         $-1022, %r9d
	0x44, 0x89, 0xde, //0x00002c1f movl         %r11d, %esi
	//0x00002c22 LBB0_621
	0x85, 0xf6, //0x00002c22 testl        %esi, %esi
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x00002c24 je           LBB0_626
	0x45, 0x89, 0xd8, //0x00002c2a movl         %r11d, %r8d
	0xe9, 0x1e, 0x00, 0x00, 0x00, //0x00002c2d jmp          LBB0_636
	//0x00002c32 LBB0_626
	0x31, 0xc0, //0x00002c32 xorl         %eax, %eax
	0xe9, 0xd7, 0x01, 0x00, 0x00, //0x00002c34 jmp          LBB0_661
	//0x00002c39 LBB0_633
	0xff, 0xc8, //0x00002c39 decl         %eax
	0x41, 0x89, 0xc0, //0x00002c3b movl         %eax, %r8d
	//0x00002c3e LBB0_634
	0x41, 0xb9, 0x02, 0xfc, 0xff, 0xff, //0x00002c3e movl         $-1022, %r9d
	0x45, 0x85, 0xc0, //0x00002c44 testl        %r8d, %r8d
	0x0f, 0x84, 0xd1, 0x02, 0x00, 0x00, //0x00002c47 je           LBB0_683
	//0x00002c4d LBB0_635
	0x44, 0x89, 0xc6, //0x00002c4d movl         %r8d, %esi
	//0x00002c50 LBB0_636
	0x4c, 0x63, 0xee, //0x00002c50 movslq       %esi, %r13
	0xb1, 0x31, //0x00002c53 movb         $49, %cl
	0x31, 0xc0, //0x00002c55 xorl         %eax, %eax
	0x41, 0xba, 0x10, 0x00, 0x00, 0x00, //0x00002c57 movl         $16, %r10d
	//0x00002c5d LBB0_637
	0x48, 0x83, 0xf8, 0x26, //0x00002c5d cmpq         $38, %rax
	0x0f, 0x84, 0x36, 0x00, 0x00, 0x00, //0x00002c61 je           LBB0_641
	0x41, 0x38, 0x0c, 0x04, //0x00002c67 cmpb         %cl, (%r12,%rax)
	0x0f, 0x85, 0x35, 0x00, 0x00, 0x00, //0x00002c6b jne          LBB0_642
	0x48, 0x8d, 0x0d, 0xc8, 0x35, 0x00, 0x00, //0x00002c71 leaq         $13768(%rip), %rcx  /* _LSHIFT_TAB+0(%rip) */
	0x0f, 0xb6, 0x8c, 0x08, 0x8d, 0x15, 0x00, 0x00, //0x00002c78 movzbl       $5517(%rax,%rcx), %ecx
	0x48, 0xff, 0xc0, //0x00002c80 incq         %rax
	0x49, 0x39, 0xc5, //0x00002c83 cmpq         %rax, %r13
	0x0f, 0x85, 0xd1, 0xff, 0xff, 0xff, //0x00002c86 jne          LBB0_637
	0x84, 0xc9, //0x00002c8c testb        %cl, %cl
	0x4c, 0x8b, 0x7d, 0xa8, //0x00002c8e movq         $-88(%rbp), %r15
	0x0f, 0x85, 0x18, 0x00, 0x00, 0x00, //0x00002c92 jne          LBB0_643
	0xe9, 0x19, 0x00, 0x00, 0x00, //0x00002c98 jmp          LBB0_644
	//0x00002c9d LBB0_641
	0x4c, 0x8b, 0x7d, 0xa8, //0x00002c9d movq         $-88(%rbp), %r15
	0xe9, 0x10, 0x00, 0x00, 0x00, //0x00002ca1 jmp          LBB0_644
	//0x00002ca6 LBB0_642
	0x4c, 0x8b, 0x7d, 0xa8, //0x00002ca6 movq         $-88(%rbp), %r15
	0x0f, 0x8d, 0x06, 0x00, 0x00, 0x00, //0x00002caa jge          LBB0_644
	//0x00002cb0 LBB0_643
	0x41, 0xba, 0x0f, 0x00, 0x00, 0x00, //0x00002cb0 movl         $15, %r10d
	//0x00002cb6 LBB0_644
	0x85, 0xf6, //0x00002cb6 testl        %esi, %esi
	0x0f, 0x8e, 0xf6, 0x00, 0x00, 0x00, //0x00002cb8 jle          LBB0_655
	0x44, 0x01, 0xd6, //0x00002cbe addl         %r10d, %esi
	0x48, 0x63, 0xfe, //0x00002cc1 movslq       %esi, %rdi
	0x48, 0xff, 0xcf, //0x00002cc4 decq         %rdi
	0x49, 0xff, 0xc5, //0x00002cc7 incq         %r13
	0x31, 0xc9, //0x00002cca xorl         %ecx, %ecx
	0x49, 0xbb, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfa, //0x00002ccc movabsq      $-432345564227567616, %r11
	0xe9, 0x1e, 0x00, 0x00, 0x00, //0x00002cd6 jmp          LBB0_648
	//0x00002cdb LBB0_646
	0x48, 0x85, 0xc0, //0x00002cdb testq        %rax, %rax
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00002cde movl         $1, %eax
	0x44, 0x0f, 0x45, 0xf0, //0x00002ce3 cmovnel      %eax, %r14d
	//0x00002ce7 LBB0_647
	0xff, 0xce, //0x00002ce7 decl         %esi
	0x48, 0xff, 0xcf, //0x00002ce9 decq         %rdi
	0x49, 0xff, 0xcd, //0x00002cec decq         %r13
	0x49, 0x83, 0xfd, 0x01, //0x00002cef cmpq         $1, %r13
	0x0f, 0x8e, 0x49, 0x00, 0x00, 0x00, //0x00002cf3 jle          LBB0_650
	//0x00002cf9 LBB0_648
	0x4b, 0x0f, 0xbe, 0x5c, 0x2c, 0xfe, //0x00002cf9 movsbq       $-2(%r12,%r13), %rbx
	0x48, 0xc1, 0xe3, 0x35, //0x00002cff shlq         $53, %rbx
	0x48, 0x01, 0xcb, //0x00002d03 addq         %rcx, %rbx
	0x4c, 0x01, 0xdb, //0x00002d06 addq         %r11, %rbx
	0x48, 0x89, 0xd8, //0x00002d09 movq         %rbx, %rax
	0x48, 0xb9, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00002d0c movabsq      $-3689348814741910323, %rcx
	0x48, 0xf7, 0xe1, //0x00002d16 mulq         %rcx
	0x48, 0x89, 0xd1, //0x00002d19 movq         %rdx, %rcx
	0x48, 0xc1, 0xe9, 0x03, //0x00002d1c shrq         $3, %rcx
	0x48, 0x8d, 0x04, 0x09, //0x00002d20 leaq         (%rcx,%rcx), %rax
	0x48, 0x8d, 0x14, 0x80, //0x00002d24 leaq         (%rax,%rax,4), %rdx
	0x48, 0x89, 0xd8, //0x00002d28 movq         %rbx, %rax
	0x48, 0x29, 0xd0, //0x00002d2b subq         %rdx, %rax
	0x4c, 0x39, 0xff, //0x00002d2e cmpq         %r15, %rdi
	0x0f, 0x83, 0xa4, 0xff, 0xff, 0xff, //0x00002d31 jae          LBB0_646
	0x04, 0x30, //0x00002d37 addb         $48, %al
	0x41, 0x88, 0x04, 0x3c, //0x00002d39 movb         %al, (%r12,%rdi)
	0xe9, 0xa5, 0xff, 0xff, 0xff, //0x00002d3d jmp          LBB0_647
	//0x00002d42 LBB0_650
	0x48, 0x83, 0xfb, 0x0a, //0x00002d42 cmpq         $10, %rbx
	0x0f, 0x82, 0x68, 0x00, 0x00, 0x00, //0x00002d46 jb           LBB0_655
	0x48, 0x63, 0xf6, //0x00002d4c movslq       %esi, %rsi
	0x48, 0xff, 0xce, //0x00002d4f decq         %rsi
	0xbf, 0x01, 0x00, 0x00, 0x00, //0x00002d52 movl         $1, %edi
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00002d57 jmp          LBB0_653
	//0x00002d5c LBB0_652
	0x48, 0x85, 0xc0, //0x00002d5c testq        %rax, %rax
	0x44, 0x0f, 0x45, 0xf7, //0x00002d5f cmovnel      %edi, %r14d
	0x48, 0xff, 0xce, //0x00002d63 decq         %rsi
	0x48, 0x83, 0xf9, 0x09, //0x00002d66 cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x00002d6a movq         %rdx, %rcx
	0x0f, 0x86, 0x41, 0x00, 0x00, 0x00, //0x00002d6d jbe          LBB0_655
	//0x00002d73 LBB0_653
	0x48, 0x89, 0xc8, //0x00002d73 movq         %rcx, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00002d76 movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x00002d80 mulq         %rdx
	0x48, 0xc1, 0xea, 0x03, //0x00002d83 shrq         $3, %rdx
	0x48, 0x8d, 0x04, 0x12, //0x00002d87 leaq         (%rdx,%rdx), %rax
	0x48, 0x8d, 0x1c, 0x80, //0x00002d8b leaq         (%rax,%rax,4), %rbx
	0x48, 0x89, 0xc8, //0x00002d8f movq         %rcx, %rax
	0x48, 0x29, 0xd8, //0x00002d92 subq         %rbx, %rax
	0x4c, 0x39, 0xfe, //0x00002d95 cmpq         %r15, %rsi
	0x0f, 0x83, 0xbe, 0xff, 0xff, 0xff, //0x00002d98 jae          LBB0_652
	0x04, 0x30, //0x00002d9e addb         $48, %al
	0x41, 0x88, 0x04, 0x34, //0x00002da0 movb         %al, (%r12,%rsi)
	0x48, 0xff, 0xce, //0x00002da4 decq         %rsi
	0x48, 0x83, 0xf9, 0x09, //0x00002da7 cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x00002dab movq         %rdx, %rcx
	0x0f, 0x87, 0xbf, 0xff, 0xff, 0xff, //0x00002dae ja           LBB0_653
	//0x00002db4 LBB0_655
	0x45, 0x01, 0xd0, //0x00002db4 addl         %r10d, %r8d
	0x49, 0x63, 0xc0, //0x00002db7 movslq       %r8d, %rax
	0x49, 0x39, 0xc7, //0x00002dba cmpq         %rax, %r15
	0x41, 0x0f, 0x46, 0xc7, //0x00002dbd cmovbel      %r15d, %eax
	0x48, 0x8b, 0x4d, 0xc8, //0x00002dc1 movq         $-56(%rbp), %rcx
	0x44, 0x01, 0xd1, //0x00002dc5 addl         %r10d, %ecx
	0x48, 0x89, 0x4d, 0xc8, //0x00002dc8 movq         %rcx, $-56(%rbp)
	0x85, 0xc0, //0x00002dcc testl        %eax, %eax
	0x0f, 0x8e, 0x34, 0x00, 0x00, 0x00, //0x00002dce jle          LBB0_660
	0x89, 0xc1, //0x00002dd4 movl         %eax, %ecx
	0x42, 0x80, 0x7c, 0x21, 0xff, 0x30, //0x00002dd6 cmpb         $48, $-1(%rcx,%r12)
	0x0f, 0x85, 0x2e, 0x00, 0x00, 0x00, //0x00002ddc jne          LBB0_661
	//0x00002de2 LBB0_657
	0x48, 0x83, 0xf9, 0x01, //0x00002de2 cmpq         $1, %rcx
	0x0f, 0x8e, 0x18, 0x00, 0x00, 0x00, //0x00002de6 jle          LBB0_659
	0x48, 0x8d, 0x41, 0xff, //0x00002dec leaq         $-1(%rcx), %rax
	0x41, 0x80, 0x7c, 0x0c, 0xfe, 0x30, //0x00002df0 cmpb         $48, $-2(%r12,%rcx)
	0x48, 0x89, 0xc1, //0x00002df6 movq         %rax, %rcx
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00002df9 je           LBB0_657
	0xe9, 0x0c, 0x00, 0x00, 0x00, //0x00002dff jmp          LBB0_661
	//0x00002e04 LBB0_659
	0xff, 0xc9, //0x00002e04 decl         %ecx
	0x89, 0xc8, //0x00002e06 movl         %ecx, %eax
	//0x00002e08 LBB0_660
	0x85, 0xc0, //0x00002e08 testl        %eax, %eax
	0x0f, 0x84, 0x0e, 0x01, 0x00, 0x00, //0x00002e0a je           LBB0_683
	//0x00002e10 LBB0_661
	0x4c, 0x8b, 0x6d, 0xd0, //0x00002e10 movq         $-48(%rbp), %r13
	//0x00002e14 LBB0_662
	0x4c, 0x8b, 0x5d, 0xb0, //0x00002e14 movq         $-80(%rbp), %r11
	0x48, 0xb9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00002e18 movabsq      $4503599627370495, %rcx
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00002e22 movq         $-1, %rdi
	0x48, 0x8b, 0x75, 0xc8, //0x00002e29 movq         $-56(%rbp), %rsi
	0x83, 0xfe, 0x14, //0x00002e2d cmpl         $20, %esi
	0x0f, 0x8f, 0x72, 0x01, 0x00, 0x00, //0x00002e30 jg           LBB0_691
	0x89, 0xf2, //0x00002e36 movl         %esi, %edx
	0x85, 0xf6, //0x00002e38 testl        %esi, %esi
	0x0f, 0x8e, 0x32, 0x00, 0x00, 0x00, //0x00002e3a jle          LBB0_668
	0x48, 0x63, 0xf0, //0x00002e40 movslq       %eax, %rsi
	0x31, 0xff, //0x00002e43 xorl         %edi, %edi
	0x31, 0xc9, //0x00002e45 xorl         %ecx, %ecx
	//0x00002e47 LBB0_665
	0x48, 0x39, 0xf7, //0x00002e47 cmpq         %rsi, %rdi
	0x0f, 0x8d, 0x1a, 0x00, 0x00, 0x00, //0x00002e4a jge          LBB0_667
	0x48, 0x8d, 0x0c, 0x89, //0x00002e50 leaq         (%rcx,%rcx,4), %rcx
	0x49, 0x0f, 0xbe, 0x1c, 0x3c, //0x00002e54 movsbq       (%r12,%rdi), %rbx
	0x48, 0x8d, 0x4c, 0x4b, 0xd0, //0x00002e59 leaq         $-48(%rbx,%rcx,2), %rcx
	0x48, 0xff, 0xc7, //0x00002e5e incq         %rdi
	0x48, 0x39, 0xfa, //0x00002e61 cmpq         %rdi, %rdx
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x00002e64 jne          LBB0_665
	//0x00002e6a LBB0_667
	0x45, 0x31, 0xc0, //0x00002e6a xorl         %r8d, %r8d
	0xe9, 0x07, 0x00, 0x00, 0x00, //0x00002e6d jmp          LBB0_669
	//0x00002e72 LBB0_668
	0x31, 0xff, //0x00002e72 xorl         %edi, %edi
	0x41, 0xb0, 0x01, //0x00002e74 movb         $1, %r8b
	0x31, 0xc9, //0x00002e77 xorl         %ecx, %ecx
	//0x00002e79 LBB0_669
	0x48, 0x8b, 0x75, 0xc8, //0x00002e79 movq         $-56(%rbp), %rsi
	0x29, 0xfe, //0x00002e7d subl         %edi, %esi
	0x0f, 0x8e, 0x4a, 0x00, 0x00, 0x00, //0x00002e7f jle          LBB0_677
	0x41, 0x89, 0xfa, //0x00002e85 movl         %edi, %r10d
	0x41, 0xf7, 0xd2, //0x00002e88 notl         %r10d
	0x44, 0x03, 0x55, 0xc8, //0x00002e8b addl         $-56(%rbp), %r10d
	0x83, 0xe6, 0x07, //0x00002e8f andl         $7, %esi
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00002e92 je           LBB0_674
	0xf7, 0xde, //0x00002e98 negl         %esi
	0x31, 0xdb, //0x00002e9a xorl         %ebx, %ebx
	//0x00002e9c LBB0_672
	0x48, 0x01, 0xc9, //0x00002e9c addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x00002e9f leaq         (%rcx,%rcx,4), %rcx
	0xff, 0xcb, //0x00002ea3 decl         %ebx
	0x39, 0xde, //0x00002ea5 cmpl         %ebx, %esi
	0x0f, 0x85, 0xef, 0xff, 0xff, 0xff, //0x00002ea7 jne          LBB0_672
	0x29, 0xdf, //0x00002ead subl         %ebx, %edi
	//0x00002eaf LBB0_674
	0x41, 0x83, 0xfa, 0x07, //0x00002eaf cmpl         $7, %r10d
	0x0f, 0x82, 0x16, 0x00, 0x00, 0x00, //0x00002eb3 jb           LBB0_677
	0x48, 0x8b, 0x75, 0xc8, //0x00002eb9 movq         $-56(%rbp), %rsi
	0x29, 0xfe, //0x00002ebd subl         %edi, %esi
	//0x00002ebf LBB0_676
	0x48, 0x69, 0xc9, 0x00, 0xe1, 0xf5, 0x05, //0x00002ebf imulq        $100000000, %rcx, %rcx
	0x83, 0xc6, 0xf8, //0x00002ec6 addl         $-8, %esi
	0x0f, 0x85, 0xf0, 0xff, 0xff, 0xff, //0x00002ec9 jne          LBB0_676
	//0x00002ecf LBB0_677
	0x31, 0xff, //0x00002ecf xorl         %edi, %edi
	0x48, 0x8b, 0x75, 0xc8, //0x00002ed1 movq         $-56(%rbp), %rsi
	0x85, 0xf6, //0x00002ed5 testl        %esi, %esi
	0x0f, 0x88, 0x51, 0x00, 0x00, 0x00, //0x00002ed7 js           LBB0_685
	0x39, 0xf0, //0x00002edd cmpl         %esi, %eax
	0x0f, 0x8e, 0x49, 0x00, 0x00, 0x00, //0x00002edf jle          LBB0_685
	0x41, 0x8a, 0x14, 0x14, //0x00002ee5 movb         (%r12,%rdx), %dl
	0xff, 0xc6, //0x00002ee9 incl         %esi
	0x39, 0xc6, //0x00002eeb cmpl         %eax, %esi
	0x0f, 0x85, 0x34, 0x00, 0x00, 0x00, //0x00002eed jne          LBB0_684
	0x80, 0xfa, 0x35, //0x00002ef3 cmpb         $53, %dl
	0x0f, 0x85, 0x2b, 0x00, 0x00, 0x00, //0x00002ef6 jne          LBB0_684
	0x45, 0x85, 0xf6, //0x00002efc testl        %r14d, %r14d
	0x40, 0x0f, 0x95, 0xc7, //0x00002eff setne        %dil
	0x41, 0x08, 0xf8, //0x00002f03 orb          %dil, %r8b
	0x0f, 0x85, 0x22, 0x00, 0x00, 0x00, //0x00002f06 jne          LBB0_685
	0x48, 0x63, 0x45, 0xc8, //0x00002f0c movslq       $-56(%rbp), %rax
	0x42, 0x8a, 0x7c, 0x20, 0xff, //0x00002f10 movb         $-1(%rax,%r12), %dil
	0x40, 0x80, 0xe7, 0x01, //0x00002f15 andb         $1, %dil
	0xe9, 0x10, 0x00, 0x00, 0x00, //0x00002f19 jmp          LBB0_685
	//0x00002f1e LBB0_683
	0x31, 0xc9, //0x00002f1e xorl         %ecx, %ecx
	0x31, 0xff, //0x00002f20 xorl         %edi, %edi
	0xe9, 0x07, 0x00, 0x00, 0x00, //0x00002f22 jmp          LBB0_685
	//0x00002f27 LBB0_684
	0x80, 0xfa, 0x34, //0x00002f27 cmpb         $52, %dl
	0x40, 0x0f, 0x9f, 0xc7, //0x00002f2a setg         %dil
	//0x00002f2e LBB0_685
	0x40, 0x0f, 0xb6, 0xff, //0x00002f2e movzbl       %dil, %edi
	0x48, 0x01, 0xcf, //0x00002f32 addq         %rcx, %rdi
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, //0x00002f35 movabsq      $9007199254740992, %rax
	0x48, 0x39, 0xc7, //0x00002f3f cmpq         %rax, %rdi
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x00002f42 jne          LBB0_689
	0x41, 0x81, 0xf9, 0xfe, 0x03, 0x00, 0x00, //0x00002f48 cmpl         $1022, %r9d
	0x0f, 0x8e, 0x3a, 0x00, 0x00, 0x00, //0x00002f4f jle          LBB0_690
	0x31, 0xff, //0x00002f55 xorl         %edi, %edi
	0x49, 0xb9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, //0x00002f57 movabsq      $9218868437227405312, %r9
	0x4c, 0x8b, 0x6d, 0xd0, //0x00002f61 movq         $-48(%rbp), %r13
	//0x00002f65 LBB0_688
	0x4c, 0x8b, 0x5d, 0xb0, //0x00002f65 movq         $-80(%rbp), %r11
	0x48, 0xb9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00002f69 movabsq      $4503599627370495, %rcx
	0xe9, 0xc4, 0xe8, 0xff, 0xff, //0x00002f73 jmp          LBB0_326
	//0x00002f78 LBB0_689
	0x4c, 0x8b, 0x6d, 0xd0, //0x00002f78 movq         $-48(%rbp), %r13
	0x4c, 0x8b, 0x5d, 0xb0, //0x00002f7c movq         $-80(%rbp), %r11
	0x48, 0xb9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00002f80 movabsq      $4503599627370495, %rcx
	0xe9, 0x19, 0x00, 0x00, 0x00, //0x00002f8a jmp          LBB0_691
	//0x00002f8f LBB0_690
	0x41, 0xff, 0xc1, //0x00002f8f incl         %r9d
	0x48, 0xb9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00002f92 movabsq      $4503599627370495, %rcx
	0x48, 0x8d, 0x79, 0x01, //0x00002f9c leaq         $1(%rcx), %rdi
	0x4c, 0x8b, 0x6d, 0xd0, //0x00002fa0 movq         $-48(%rbp), %r13
	0x4c, 0x8b, 0x5d, 0xb0, //0x00002fa4 movq         $-80(%rbp), %r11
	//0x00002fa8 LBB0_691
	0x8a, 0x55, 0xa0, //0x00002fa8 movb         $-96(%rbp), %dl
	0x48, 0x8d, 0x41, 0x01, //0x00002fab leaq         $1(%rcx), %rax
	0x48, 0x21, 0xf8, //0x00002faf andq         %rdi, %rax
	0x41, 0x81, 0xc1, 0xff, 0x03, 0x00, 0x00, //0x00002fb2 addl         $1023, %r9d
	0x41, 0x81, 0xe1, 0xff, 0x07, 0x00, 0x00, //0x00002fb9 andl         $2047, %r9d
	0x49, 0xc1, 0xe1, 0x34, //0x00002fc0 shlq         $52, %r9
	0x48, 0x85, 0xc0, //0x00002fc4 testq        %rax, %rax
	0x4c, 0x0f, 0x44, 0xc8, //0x00002fc7 cmoveq       %rax, %r9
	0xe9, 0x6f, 0xe8, 0xff, 0xff, //0x00002fcb jmp          LBB0_327
	//0x00002fd0 LBB0_692
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002fd0 movq         $-1, %r8
	0x45, 0x31, 0xff, //0x00002fd7 xorl         %r15d, %r15d
	0x49, 0x83, 0xfc, 0x20, //0x00002fda cmpq         $32, %r12
	0x0f, 0x83, 0x0c, 0xe1, 0xff, 0xff, //0x00002fde jae          LBB0_231
	0xe9, 0xb8, 0x01, 0x00, 0x00, //0x00002fe4 jmp          LBB0_717
	//0x00002fe9 LBB0_693
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002fe9 movq         $-1, %r8
	0x45, 0x31, 0xff, //0x00002ff0 xorl         %r15d, %r15d
	0x49, 0x83, 0xfc, 0x20, //0x00002ff3 cmpq         $32, %r12
	0x0f, 0x83, 0xb9, 0xe1, 0xff, 0xff, //0x00002ff7 jae          LBB0_240
	0xe9, 0x77, 0x00, 0x00, 0x00, //0x00002ffd jmp          LBB0_701
	//0x00003002 LBB0_694
	0x44, 0x89, 0xfa, //0x00003002 movl         %r15d, %edx
	0xf7, 0xd2, //0x00003005 notl         %edx
	0x21, 0xca, //0x00003007 andl         %ecx, %edx
	0x44, 0x8d, 0x14, 0x12, //0x00003009 leal         (%rdx,%rdx), %r10d
	0x45, 0x09, 0xfa, //0x0000300d orl          %r15d, %r10d
	0xbb, 0xaa, 0xaa, 0xaa, 0xaa, //0x00003010 movl         $2863311530, %ebx
	0x44, 0x31, 0xd3, //0x00003015 xorl         %r10d, %ebx
	0x21, 0xcb, //0x00003018 andl         %ecx, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000301a andl         $-1431655766, %ebx
	0x45, 0x31, 0xff, //0x00003020 xorl         %r15d, %r15d
	0x01, 0xd3, //0x00003023 addl         %edx, %ebx
	0x41, 0x0f, 0x92, 0xc7, //0x00003025 setb         %r15b
	0x01, 0xdb, //0x00003029 addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x0000302b xorl         $1431655765, %ebx
	0x44, 0x21, 0xd3, //0x00003031 andl         %r10d, %ebx
	0xf7, 0xd3, //0x00003034 notl         %ebx
	0x21, 0xdf, //0x00003036 andl         %ebx, %edi
	0x48, 0xc1, 0xe0, 0x10, //0x00003038 shlq         $16, %rax
	0x48, 0x85, 0xff, //0x0000303c testq        %rdi, %rdi
	0x0f, 0x85, 0x20, 0xe2, 0xff, 0xff, //0x0000303f jne          LBB0_244
	//0x00003045 LBB0_695
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00003045 movl         $64, %ecx
	//0x0000304a LBB0_696
	0x4c, 0x09, 0xc8, //0x0000304a orq          %r9, %rax
	0x48, 0x85, 0xff, //0x0000304d testq        %rdi, %rdi
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00003050 je           LBB0_699
	0x48, 0x85, 0xc0, //0x00003056 testq        %rax, %rax
	0x0f, 0x84, 0xa6, 0x00, 0x00, 0x00, //0x00003059 je           LBB0_709
	0x48, 0x0f, 0xbc, 0xc0, //0x0000305f bsfq         %rax, %rax
	0xe9, 0xa2, 0x00, 0x00, 0x00, //0x00003063 jmp          LBB0_710
	//0x00003068 LBB0_699
	0x48, 0x85, 0xc0, //0x00003068 testq        %rax, %rax
	0x0f, 0x85, 0xb0, 0x00, 0x00, 0x00, //0x0000306b jne          LBB0_712
	0x49, 0x83, 0xc5, 0x20, //0x00003071 addq         $32, %r13
	0x49, 0x83, 0xc4, 0xe0, //0x00003075 addq         $-32, %r12
	//0x00003079 LBB0_701
	0x4d, 0x85, 0xff, //0x00003079 testq        %r15, %r15
	0x0f, 0x85, 0xba, 0x01, 0x00, 0x00, //0x0000307c jne          LBB0_727
	0x4c, 0x89, 0xc0, //0x00003082 movq         %r8, %rax
	0x4d, 0x85, 0xe4, //0x00003085 testq        %r12, %r12
	0x0f, 0x84, 0x1b, 0x02, 0x00, 0x00, //0x00003088 je           LBB0_731
	//0x0000308e LBB0_703
	0x41, 0x0f, 0xb6, 0x4d, 0x00, //0x0000308e movzbl       (%r13), %ecx
	0x80, 0xf9, 0x22, //0x00003093 cmpb         $34, %cl
	0x0f, 0x84, 0x95, 0x00, 0x00, 0x00, //0x00003096 je           LBB0_713
	0x80, 0xf9, 0x5c, //0x0000309c cmpb         $92, %cl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x0000309f je           LBB0_707
	0x80, 0xf9, 0x20, //0x000030a5 cmpb         $32, %cl
	0x0f, 0x82, 0x73, 0x00, 0x00, 0x00, //0x000030a8 jb           LBB0_712
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000030ae movq         $-1, %rcx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x000030b5 movl         $1, %edx
	0x49, 0x01, 0xd5, //0x000030ba addq         %rdx, %r13
	0x49, 0x01, 0xcc, //0x000030bd addq         %rcx, %r12
	0x0f, 0x85, 0xc8, 0xff, 0xff, 0xff, //0x000030c0 jne          LBB0_703
	0xe9, 0xde, 0x01, 0x00, 0x00, //0x000030c6 jmp          LBB0_731
	//0x000030cb LBB0_707
	0x49, 0x83, 0xfc, 0x01, //0x000030cb cmpq         $1, %r12
	0x0f, 0x84, 0xd4, 0x01, 0x00, 0x00, //0x000030cf je           LBB0_731
	0x4c, 0x89, 0xe9, //0x000030d5 movq         %r13, %rcx
	0x48, 0x2b, 0x4d, 0xc0, //0x000030d8 subq         $-64(%rbp), %rcx
	0x48, 0x83, 0xf8, 0xff, //0x000030dc cmpq         $-1, %rax
	0x4c, 0x0f, 0x44, 0xc1, //0x000030e0 cmoveq       %rcx, %r8
	0x48, 0x0f, 0x44, 0xc1, //0x000030e4 cmoveq       %rcx, %rax
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x000030e8 movq         $-2, %rcx
	0xba, 0x02, 0x00, 0x00, 0x00, //0x000030ef movl         $2, %edx
	0x49, 0x01, 0xd5, //0x000030f4 addq         %rdx, %r13
	0x49, 0x01, 0xcc, //0x000030f7 addq         %rcx, %r12
	0x0f, 0x85, 0x8e, 0xff, 0xff, 0xff, //0x000030fa jne          LBB0_703
	0xe9, 0xa4, 0x01, 0x00, 0x00, //0x00003100 jmp          LBB0_731
	//0x00003105 LBB0_709
	0xb8, 0x40, 0x00, 0x00, 0x00, //0x00003105 movl         $64, %eax
	//0x0000310a LBB0_710
	0x48, 0x39, 0xc8, //0x0000310a cmpq         %rcx, %rax
	0x0f, 0x82, 0x0e, 0x00, 0x00, 0x00, //0x0000310d jb           LBB0_712
	0x4c, 0x2b, 0x6d, 0xc0, //0x00003113 subq         $-64(%rbp), %r13
	0x4d, 0x8d, 0x5c, 0x0d, 0x01, //0x00003117 leaq         $1(%r13,%rcx), %r11
	0xe9, 0x52, 0xe0, 0xff, 0xff, //0x0000311c jmp          LBB0_237
	//0x00003121 LBB0_712
	0x49, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x00003121 movq         $-2, %r11
	0x48, 0x8b, 0x4d, 0xd0, //0x00003128 movq         $-48(%rbp), %rcx
	0xe9, 0x83, 0x01, 0x00, 0x00, //0x0000312c jmp          LBB0_732
	//0x00003131 LBB0_713
	0x4c, 0x2b, 0x6d, 0xc0, //0x00003131 subq         $-64(%rbp), %r13
	0x49, 0xff, 0xc5, //0x00003135 incq         %r13
	0x4d, 0x89, 0xeb, //0x00003138 movq         %r13, %r11
	0xe9, 0x33, 0xe0, 0xff, 0xff, //0x0000313b jmp          LBB0_237
	//0x00003140 LBB0_714
	0x4c, 0x89, 0xe9, //0x00003140 movq         %r13, %rcx
	0x48, 0x2b, 0x4d, 0xc0, //0x00003143 subq         $-64(%rbp), %rcx
	0x4c, 0x0f, 0xbc, 0xc0, //0x00003147 bsfq         %rax, %r8
	0x49, 0x01, 0xc8, //0x0000314b addq         %rcx, %r8
	0x48, 0x09, 0xd7, //0x0000314e orq          %rdx, %rdi
	0x48, 0x89, 0xc1, //0x00003151 movq         %rax, %rcx
	0x4c, 0x09, 0xf9, //0x00003154 orq          %r15, %rcx
	0x0f, 0x84, 0x00, 0xe0, 0xff, 0xff, //0x00003157 je           LBB0_234
	//0x0000315d LBB0_715
	0x44, 0x89, 0xf9, //0x0000315d movl         %r15d, %ecx
	0xf7, 0xd1, //0x00003160 notl         %ecx
	0x21, 0xc1, //0x00003162 andl         %eax, %ecx
	0x8d, 0x14, 0x09, //0x00003164 leal         (%rcx,%rcx), %edx
	0x44, 0x09, 0xfa, //0x00003167 orl          %r15d, %edx
	0xbb, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000316a movl         $2863311530, %ebx
	0x31, 0xd3, //0x0000316f xorl         %edx, %ebx
	0x21, 0xc3, //0x00003171 andl         %eax, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x00003173 andl         $-1431655766, %ebx
	0x45, 0x31, 0xff, //0x00003179 xorl         %r15d, %r15d
	0x01, 0xcb, //0x0000317c addl         %ecx, %ebx
	0x41, 0x0f, 0x92, 0xc7, //0x0000317e setb         %r15b
	0x01, 0xdb, //0x00003182 addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x00003184 xorl         $1431655765, %ebx
	0x21, 0xd3, //0x0000318a andl         %edx, %ebx
	0xf7, 0xd3, //0x0000318c notl         %ebx
	0x21, 0xdf, //0x0000318e andl         %ebx, %edi
	0x48, 0x85, 0xff, //0x00003190 testq        %rdi, %rdi
	0x0f, 0x85, 0xcd, 0xdf, 0xff, 0xff, //0x00003193 jne          LBB0_235
	//0x00003199 LBB0_716
	0x49, 0x83, 0xc5, 0x20, //0x00003199 addq         $32, %r13
	0x49, 0x83, 0xc4, 0xe0, //0x0000319d addq         $-32, %r12
	//0x000031a1 LBB0_717
	0x4d, 0x85, 0xff, //0x000031a1 testq        %r15, %r15
	0x0f, 0x85, 0xcb, 0x00, 0x00, 0x00, //0x000031a4 jne          LBB0_729
	0x4d, 0x89, 0xc1, //0x000031aa movq         %r8, %r9
	0x4d, 0x85, 0xe4, //0x000031ad testq        %r12, %r12
	0x0f, 0x84, 0xf3, 0x00, 0x00, 0x00, //0x000031b0 je           LBB0_731
	//0x000031b6 LBB0_719
	0x48, 0x8b, 0x55, 0xc0, //0x000031b6 movq         $-64(%rbp), %rdx
	0x48, 0xf7, 0xd2, //0x000031ba notq         %rdx
	//0x000031bd LBB0_720
	0x4d, 0x8d, 0x5d, 0x01, //0x000031bd leaq         $1(%r13), %r11
	0x41, 0x0f, 0xb6, 0x45, 0x00, //0x000031c1 movzbl       (%r13), %eax
	0x3c, 0x22, //0x000031c6 cmpb         $34, %al
	0x0f, 0x84, 0x52, 0x00, 0x00, 0x00, //0x000031c8 je           LBB0_725
	0x49, 0x8d, 0x5c, 0x24, 0xff, //0x000031ce leaq         $-1(%r12), %rbx
	0x3c, 0x5c, //0x000031d3 cmpb         $92, %al
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x000031d5 je           LBB0_723
	0x49, 0x89, 0xdc, //0x000031db movq         %rbx, %r12
	0x4d, 0x89, 0xdd, //0x000031de movq         %r11, %r13
	0x48, 0x85, 0xdb, //0x000031e1 testq        %rbx, %rbx
	0x0f, 0x85, 0xd3, 0xff, 0xff, 0xff, //0x000031e4 jne          LBB0_720
	0xe9, 0xba, 0x00, 0x00, 0x00, //0x000031ea jmp          LBB0_731
	//0x000031ef LBB0_723
	0x48, 0x85, 0xdb, //0x000031ef testq        %rbx, %rbx
	0x0f, 0x84, 0xb1, 0x00, 0x00, 0x00, //0x000031f2 je           LBB0_731
	0x49, 0x01, 0xd3, //0x000031f8 addq         %rdx, %r11
	0x49, 0x83, 0xf9, 0xff, //0x000031fb cmpq         $-1, %r9
	0x4d, 0x0f, 0x44, 0xc3, //0x000031ff cmoveq       %r11, %r8
	0x4d, 0x0f, 0x44, 0xcb, //0x00003203 cmoveq       %r11, %r9
	0x49, 0x83, 0xc5, 0x02, //0x00003207 addq         $2, %r13
	0x49, 0x83, 0xc4, 0xfe, //0x0000320b addq         $-2, %r12
	0x4c, 0x89, 0xe3, //0x0000320f movq         %r12, %rbx
	0x48, 0x85, 0xdb, //0x00003212 testq        %rbx, %rbx
	0x0f, 0x85, 0xa2, 0xff, 0xff, 0xff, //0x00003215 jne          LBB0_720
	0xe9, 0x89, 0x00, 0x00, 0x00, //0x0000321b jmp          LBB0_731
	//0x00003220 LBB0_725
	0x4c, 0x2b, 0x5d, 0xc0, //0x00003220 subq         $-64(%rbp), %r11
	0xe9, 0x4a, 0xdf, 0xff, 0xff, //0x00003224 jmp          LBB0_237
	//0x00003229 LBB0_726
	0x4c, 0x89, 0xea, //0x00003229 movq         %r13, %rdx
	0x48, 0x2b, 0x55, 0xc0, //0x0000322c subq         $-64(%rbp), %rdx
	0x4c, 0x0f, 0xbc, 0xc1, //0x00003230 bsfq         %rcx, %r8
	0x49, 0x01, 0xd0, //0x00003234 addq         %rdx, %r8
	0xe9, 0x08, 0xe0, 0xff, 0xff, //0x00003237 jmp          LBB0_242
	//0x0000323c LBB0_727
	0x4d, 0x85, 0xe4, //0x0000323c testq        %r12, %r12
	0x0f, 0x84, 0x7a, 0x00, 0x00, 0x00, //0x0000323f je           LBB0_733
	0x48, 0x8b, 0x45, 0xc0, //0x00003245 movq         $-64(%rbp), %rax
	0x48, 0xf7, 0xd0, //0x00003249 notq         %rax
	0x4c, 0x01, 0xe8, //0x0000324c addq         %r13, %rax
	0x49, 0x83, 0xf8, 0xff, //0x0000324f cmpq         $-1, %r8
	0x4c, 0x89, 0xc1, //0x00003253 movq         %r8, %rcx
	0x48, 0x0f, 0x44, 0xc8, //0x00003256 cmoveq       %rax, %rcx
	0x49, 0x0f, 0x45, 0xc0, //0x0000325a cmovneq      %r8, %rax
	0x49, 0xff, 0xc5, //0x0000325e incq         %r13
	0x49, 0xff, 0xcc, //0x00003261 decq         %r12
	0x49, 0x89, 0xc8, //0x00003264 movq         %rcx, %r8
	0x4d, 0x85, 0xe4, //0x00003267 testq        %r12, %r12
	0x0f, 0x85, 0x1e, 0xfe, 0xff, 0xff, //0x0000326a jne          LBB0_703
	0xe9, 0x34, 0x00, 0x00, 0x00, //0x00003270 jmp          LBB0_731
	//0x00003275 LBB0_729
	0x4d, 0x85, 0xe4, //0x00003275 testq        %r12, %r12
	0x0f, 0x84, 0x41, 0x00, 0x00, 0x00, //0x00003278 je           LBB0_733
	0x4c, 0x8b, 0x4d, 0xc0, //0x0000327e movq         $-64(%rbp), %r9
	0x49, 0xf7, 0xd1, //0x00003282 notq         %r9
	0x4d, 0x01, 0xe9, //0x00003285 addq         %r13, %r9
	0x49, 0x83, 0xf8, 0xff, //0x00003288 cmpq         $-1, %r8
	0x4c, 0x89, 0xc0, //0x0000328c movq         %r8, %rax
	0x49, 0x0f, 0x44, 0xc1, //0x0000328f cmoveq       %r9, %rax
	0x4d, 0x0f, 0x45, 0xc8, //0x00003293 cmovneq      %r8, %r9
	0x49, 0xff, 0xc5, //0x00003297 incq         %r13
	0x49, 0xff, 0xcc, //0x0000329a decq         %r12
	0x49, 0x89, 0xc0, //0x0000329d movq         %rax, %r8
	0x4d, 0x85, 0xe4, //0x000032a0 testq        %r12, %r12
	0x0f, 0x85, 0x0d, 0xff, 0xff, 0xff, //0x000032a3 jne          LBB0_719
	//0x000032a9 LBB0_731
	0x48, 0x8b, 0x4d, 0xd0, //0x000032a9 movq         $-48(%rbp), %rcx
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000032ad movq         $-1, %r11
	//0x000032b4 LBB0_732
	0x4c, 0x89, 0x19, //0x000032b4 movq         %r11, (%rcx)
	0x49, 0x89, 0xf3, //0x000032b7 movq         %rsi, %r11
	0xe9, 0x30, 0xd0, 0xff, 0xff, //0x000032ba jmp          LBB0_38
	//0x000032bf LBB0_733
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000032bf movq         $-1, %r11
	0x48, 0x8b, 0x4d, 0xd0, //0x000032c6 movq         $-48(%rbp), %rcx
	0xe9, 0xe5, 0xff, 0xff, 0xff, //0x000032ca jmp          LBB0_732
	0x90, //0x000032cf .p2align 2, 0x90
	// // .set L0_0_set_37, LBB0_37-LJTI0_0
	// // .set L0_0_set_99, LBB0_99-LJTI0_0
	// // .set L0_0_set_100, LBB0_100-LJTI0_0
	// // .set L0_0_set_111, LBB0_111-LJTI0_0
	// // .set L0_0_set_30, LBB0_30-LJTI0_0
	// // .set L0_0_set_112, LBB0_112-LJTI0_0
	// // .set L0_0_set_113, LBB0_113-LJTI0_0
	// // .set L0_0_set_114, LBB0_114-LJTI0_0
	// // .set L0_0_set_115, LBB0_115-LJTI0_0
	// // .set L0_0_set_118, LBB0_118-LJTI0_0
	// // .set L0_0_set_121, LBB0_121-LJTI0_0
	// // .set L0_0_set_124, LBB0_124-LJTI0_0
	// // .set L0_0_set_125, LBB0_125-LJTI0_0
	//0x000032d0 LJTI0_0
	0x17, 0xd0, 0xff, 0xff, //0x000032d0 .long L0_0_set_37
	0x33, 0xd4, 0xff, 0xff, //0x000032d4 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000032d8 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000032dc .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000032e0 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000032e4 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000032e8 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000032ec .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000032f0 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000032f4 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000032f8 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000032fc .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003300 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003304 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003308 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x0000330c .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003310 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003314 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003318 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x0000331c .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003320 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003324 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003328 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x0000332c .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003330 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003334 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003338 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x0000333c .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003340 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003344 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003348 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x0000334c .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003350 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003354 .long L0_0_set_99
	0x43, 0xd4, 0xff, 0xff, //0x00003358 .long L0_0_set_100
	0x33, 0xd4, 0xff, 0xff, //0x0000335c .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003360 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003364 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003368 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x0000336c .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003370 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003374 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003378 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x0000337c .long L0_0_set_99
	0x08, 0xd6, 0xff, 0xff, //0x00003380 .long L0_0_set_111
	0x93, 0xcf, 0xff, 0xff, //0x00003384 .long L0_0_set_30
	0x33, 0xd4, 0xff, 0xff, //0x00003388 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x0000338c .long L0_0_set_99
	0x93, 0xcf, 0xff, 0xff, //0x00003390 .long L0_0_set_30
	0x93, 0xcf, 0xff, 0xff, //0x00003394 .long L0_0_set_30
	0x93, 0xcf, 0xff, 0xff, //0x00003398 .long L0_0_set_30
	0x93, 0xcf, 0xff, 0xff, //0x0000339c .long L0_0_set_30
	0x93, 0xcf, 0xff, 0xff, //0x000033a0 .long L0_0_set_30
	0x93, 0xcf, 0xff, 0xff, //0x000033a4 .long L0_0_set_30
	0x93, 0xcf, 0xff, 0xff, //0x000033a8 .long L0_0_set_30
	0x93, 0xcf, 0xff, 0xff, //0x000033ac .long L0_0_set_30
	0x93, 0xcf, 0xff, 0xff, //0x000033b0 .long L0_0_set_30
	0x93, 0xcf, 0xff, 0xff, //0x000033b4 .long L0_0_set_30
	0x1a, 0xd6, 0xff, 0xff, //0x000033b8 .long L0_0_set_112
	0x33, 0xd4, 0xff, 0xff, //0x000033bc .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000033c0 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000033c4 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000033c8 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000033cc .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000033d0 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000033d4 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000033d8 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000033dc .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000033e0 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000033e4 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000033e8 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000033ec .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000033f0 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000033f4 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000033f8 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000033fc .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003400 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003404 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003408 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x0000340c .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003410 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003414 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003418 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x0000341c .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003420 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003424 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003428 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x0000342c .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003430 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003434 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003438 .long L0_0_set_99
	0x2c, 0xd6, 0xff, 0xff, //0x0000343c .long L0_0_set_113
	0x33, 0xd4, 0xff, 0xff, //0x00003440 .long L0_0_set_99
	0x39, 0xd6, 0xff, 0xff, //0x00003444 .long L0_0_set_114
	0x33, 0xd4, 0xff, 0xff, //0x00003448 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x0000344c .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003450 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003454 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003458 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x0000345c .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003460 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003464 .long L0_0_set_99
	0x4b, 0xd6, 0xff, 0xff, //0x00003468 .long L0_0_set_115
	0x33, 0xd4, 0xff, 0xff, //0x0000346c .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003470 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003474 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003478 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x0000347c .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003480 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003484 .long L0_0_set_99
	0x7d, 0xd6, 0xff, 0xff, //0x00003488 .long L0_0_set_118
	0x33, 0xd4, 0xff, 0xff, //0x0000348c .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003490 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003494 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x00003498 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x0000349c .long L0_0_set_99
	0xad, 0xd6, 0xff, 0xff, //0x000034a0 .long L0_0_set_121
	0x33, 0xd4, 0xff, 0xff, //0x000034a4 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000034a8 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000034ac .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000034b0 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000034b4 .long L0_0_set_99
	0x33, 0xd4, 0xff, 0xff, //0x000034b8 .long L0_0_set_99
	0xdd, 0xd6, 0xff, 0xff, //0x000034bc .long L0_0_set_124
	0x33, 0xd4, 0xff, 0xff, //0x000034c0 .long L0_0_set_99
	0xea, 0xd6, 0xff, 0xff, //0x000034c4 .long L0_0_set_125
	// // .set L0_1_set_65, LBB0_65-LJTI0_1
	// // .set L0_1_set_75, LBB0_75-LJTI0_1
	// // .set L0_1_set_72, LBB0_72-LJTI0_1
	// // .set L0_1_set_67, LBB0_67-LJTI0_1
	// // .set L0_1_set_70, LBB0_70-LJTI0_1
	//0x000034c8 LJTI0_1
	0x77, 0xd0, 0xff, 0xff, //0x000034c8 .long L0_1_set_65
	0x01, 0xd1, 0xff, 0xff, //0x000034cc .long L0_1_set_75
	0x77, 0xd0, 0xff, 0xff, //0x000034d0 .long L0_1_set_65
	0xdc, 0xd0, 0xff, 0xff, //0x000034d4 .long L0_1_set_72
	0x01, 0xd1, 0xff, 0xff, //0x000034d8 .long L0_1_set_75
	0x98, 0xd0, 0xff, 0xff, //0x000034dc .long L0_1_set_67
	0x98, 0xd0, 0xff, 0xff, //0x000034e0 .long L0_1_set_67
	0x98, 0xd0, 0xff, 0xff, //0x000034e4 .long L0_1_set_67
	0x98, 0xd0, 0xff, 0xff, //0x000034e8 .long L0_1_set_67
	0x98, 0xd0, 0xff, 0xff, //0x000034ec .long L0_1_set_67
	0x98, 0xd0, 0xff, 0xff, //0x000034f0 .long L0_1_set_67
	0x98, 0xd0, 0xff, 0xff, //0x000034f4 .long L0_1_set_67
	0x98, 0xd0, 0xff, 0xff, //0x000034f8 .long L0_1_set_67
	0x98, 0xd0, 0xff, 0xff, //0x000034fc .long L0_1_set_67
	0x98, 0xd0, 0xff, 0xff, //0x00003500 .long L0_1_set_67
	0x01, 0xd1, 0xff, 0xff, //0x00003504 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003508 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x0000350c .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003510 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003514 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003518 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x0000351c .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003520 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003524 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003528 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x0000352c .long L0_1_set_75
	0xc1, 0xd0, 0xff, 0xff, //0x00003530 .long L0_1_set_70
	0x01, 0xd1, 0xff, 0xff, //0x00003534 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003538 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x0000353c .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003540 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003544 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003548 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x0000354c .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003550 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003554 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003558 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x0000355c .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003560 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003564 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003568 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x0000356c .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003570 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003574 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003578 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x0000357c .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003580 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003584 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003588 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x0000358c .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003590 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003594 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x00003598 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x0000359c .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x000035a0 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x000035a4 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x000035a8 .long L0_1_set_75
	0x01, 0xd1, 0xff, 0xff, //0x000035ac .long L0_1_set_75
	0xc1, 0xd0, 0xff, 0xff, //0x000035b0 .long L0_1_set_70
	//0x000035b4 .p2align 2, 0x00
	//0x000035b4 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x000035b4 .long 2
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000035b8 .p2align 4, 0x00
	//0x000035c0 _P10_TAB
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x3f, //0x000035c0 .quad 4607182418800017408
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0x40, //0x000035c8 .quad 4621819117588971520
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x59, 0x40, //0x000035d0 .quad 4636737291354636288
	0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x8f, 0x40, //0x000035d8 .quad 4652007308841189376
	0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 0xc3, 0x40, //0x000035e0 .quad 4666723172467343360
	0x00, 0x00, 0x00, 0x00, 0x00, 0x6a, 0xf8, 0x40, //0x000035e8 .quad 4681608360884174848
	0x00, 0x00, 0x00, 0x00, 0x80, 0x84, 0x2e, 0x41, //0x000035f0 .quad 4696837146684686336
	0x00, 0x00, 0x00, 0x00, 0xd0, 0x12, 0x63, 0x41, //0x000035f8 .quad 4711630319722168320
	0x00, 0x00, 0x00, 0x00, 0x84, 0xd7, 0x97, 0x41, //0x00003600 .quad 4726483295884279808
	0x00, 0x00, 0x00, 0x00, 0x65, 0xcd, 0xcd, 0x41, //0x00003608 .quad 4741671816366391296
	0x00, 0x00, 0x00, 0x20, 0x5f, 0xa0, 0x02, 0x42, //0x00003610 .quad 4756540486875873280
	0x00, 0x00, 0x00, 0xe8, 0x76, 0x48, 0x37, 0x42, //0x00003618 .quad 4771362005757984768
	0x00, 0x00, 0x00, 0xa2, 0x94, 0x1a, 0x6d, 0x42, //0x00003620 .quad 4786511204640096256
	0x00, 0x00, 0x40, 0xe5, 0x9c, 0x30, 0xa2, 0x42, //0x00003628 .quad 4801453603149578240
	0x00, 0x00, 0x90, 0x1e, 0xc4, 0xbc, 0xd6, 0x42, //0x00003630 .quad 4816244402031689728
	0x00, 0x00, 0x34, 0x26, 0xf5, 0x6b, 0x0c, 0x43, //0x00003638 .quad 4831355200913801216
	0x00, 0x80, 0xe0, 0x37, 0x79, 0xc3, 0x41, 0x43, //0x00003640 .quad 4846369599423283200
	0x00, 0xa0, 0xd8, 0x85, 0x57, 0x34, 0x76, 0x43, //0x00003648 .quad 4861130398305394688
	0x00, 0xc8, 0x4e, 0x67, 0x6d, 0xc1, 0xab, 0x43, //0x00003650 .quad 4876203697187506176
	0x00, 0x3d, 0x91, 0x60, 0xe4, 0x58, 0xe1, 0x43, //0x00003658 .quad 4891288408196988160
	0x40, 0x8c, 0xb5, 0x78, 0x1d, 0xaf, 0x15, 0x44, //0x00003660 .quad 4906019910204099648
	0x50, 0xef, 0xe2, 0xd6, 0xe4, 0x1a, 0x4b, 0x44, //0x00003668 .quad 4921056587992461136
	0x92, 0xd5, 0x4d, 0x06, 0xcf, 0xf0, 0x80, 0x44, //0x00003670 .quad 4936209963552724370
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003678 .p2align 4, 0x00
	//0x00003680 _POW10_M128_TAB
	0x53, 0xe4, 0x60, 0xcd, 0x69, 0xc8, 0x32, 0x17, //0x00003680 .quad 1671618768450675795
	0x88, 0x02, 0x1c, 0x08, 0xa0, 0xd5, 0x8f, 0xfa, //0x00003688 .quad -391859759250406776
	0xb4, 0x8e, 0x5c, 0x20, 0x42, 0xbd, 0x7f, 0x0e, //0x00003690 .quad 1044761730281672372
	0x95, 0x81, 0x11, 0x05, 0x84, 0xe5, 0x99, 0x9c, //0x00003698 .quad -7162441377172586091
	0x61, 0xb2, 0x73, 0xa8, 0x92, 0xac, 0x1f, 0x52, //0x000036a0 .quad 5917638181279478369
	0xfa, 0xe1, 0x55, 0x06, 0xe5, 0x5e, 0xc0, 0xc3, //0x000036a8 .quad -4341365703038344710
	0xf9, 0x9e, 0x90, 0x52, 0xb7, 0x97, 0xa7, 0xe6, //0x000036b0 .quad -1826324310255427847
	0x78, 0x5a, 0xeb, 0x47, 0x9e, 0x76, 0xb0, 0xf4, //0x000036b8 .quad -815021110370542984
	0x5c, 0x63, 0x9a, 0x93, 0xd2, 0xbe, 0x28, 0x90, //0x000036c0 .quad -8058981721550724260
	0x8b, 0x18, 0xf3, 0xec, 0x22, 0x4a, 0xee, 0x98, //0x000036c8 .quad -7426917221622671221
	0x33, 0xfc, 0x80, 0x38, 0x87, 0xee, 0x32, 0x74, //0x000036d0 .quad 8373016921771146291
	0xae, 0xde, 0x2f, 0xa8, 0xab, 0xdc, 0x29, 0xbf, //0x000036d8 .quad -4671960508600951122
	0x3f, 0x3b, 0xa1, 0x06, 0x29, 0xaa, 0x3f, 0x11, //0x000036e0 .quad 1242899115359157055
	0x5a, 0xd6, 0x3b, 0x92, 0xd6, 0x53, 0xf4, 0xee, //0x000036e8 .quad -1228264617323800998
	0x07, 0xc5, 0x24, 0xa4, 0x59, 0xca, 0xc7, 0x4a, //0x000036f0 .quad 5388497965526861063
	0xf8, 0x65, 0x65, 0x1b, 0x66, 0xb4, 0x58, 0x95, //0x000036f8 .quad -7685194413468457480
	0x49, 0xf6, 0x2d, 0x0d, 0xf0, 0xbc, 0x79, 0x5d, //0x00003700 .quad 6735622456908576329
	0x76, 0xbf, 0x3e, 0xa2, 0x7f, 0xe1, 0xae, 0xba, //0x00003708 .quad -4994806998408183946
	0xdc, 0x73, 0x79, 0x10, 0x2c, 0x2c, 0xd8, 0xf4, //0x00003710 .quad -803843965719055396
	0x53, 0x6f, 0xce, 0x8a, 0xdf, 0x99, 0x5a, 0xe9, //0x00003718 .quad -1631822729582842029
	0x69, 0xe8, 0x4b, 0x8a, 0x9b, 0x1b, 0x07, 0x79, //0x00003720 .quad 8720969558280366185
	0x94, 0x05, 0xc1, 0xb6, 0x2b, 0xa0, 0xd8, 0x91, //0x00003728 .quad -7937418233630358124
	0x84, 0xe2, 0xde, 0x6c, 0x82, 0xe2, 0x48, 0x97, //0x00003730 .quad -7545532125859093884
	0xf9, 0x46, 0x71, 0xa4, 0x36, 0xc8, 0x4e, 0xb6, //0x00003738 .quad -5310086773610559751
	0x25, 0x9b, 0x16, 0x08, 0x23, 0x1b, 0x1b, 0xfd, //0x00003740 .quad -208543120469091547
	0xb7, 0x98, 0x8d, 0x4d, 0x44, 0x7a, 0xe2, 0xe3, //0x00003748 .quad -2025922448585811785
	0xf7, 0x20, 0x0e, 0xe5, 0xf5, 0xf0, 0x30, 0xfe, //0x00003750 .quad -130339450293182217
	0x72, 0x7f, 0x78, 0xb0, 0x6a, 0x8c, 0x6d, 0x8e, //0x00003758 .quad -8183730558007214222
	0x35, 0xa9, 0x51, 0x5e, 0x33, 0x2d, 0xbd, 0xbd, //0x00003760 .quad -4774610331293865675
	0x4f, 0x9f, 0x96, 0x5c, 0x85, 0xef, 0x08, 0xb2, //0x00003768 .quad -5617977179081629873
	0x82, 0x13, 0xe6, 0x35, 0x80, 0x78, 0x2c, 0xad, //0x00003770 .quad -5968262914117332094
	0x23, 0x47, 0xbc, 0xb3, 0x66, 0x2b, 0x8b, 0xde, //0x00003778 .quad -2410785455424649437
	0x31, 0xcc, 0xaf, 0x21, 0x50, 0xcb, 0x3b, 0x4c, //0x00003780 .quad 5493207715531443249
	0x76, 0xac, 0x55, 0x30, 0x20, 0xfb, 0x16, 0x8b, //0x00003788 .quad -8424269937281487754
	0x3d, 0xbf, 0x1b, 0x2a, 0x24, 0xbe, 0x4a, 0xdf, //0x00003790 .quad -2356862392440471747
	0x93, 0x17, 0x6b, 0x3c, 0xe8, 0xb9, 0xdc, 0xad, //0x00003798 .quad -5918651403174471789
	0x0d, 0xaf, 0xa2, 0x34, 0xad, 0x6d, 0x1d, 0xd7, //0x000037a0 .quad -2946077990550589683
	0x78, 0xdd, 0x85, 0x4b, 0x62, 0xe8, 0x53, 0xd9, //0x000037a8 .quad -2786628235540701832
	0x68, 0xad, 0xe5, 0x40, 0x8c, 0x64, 0x72, 0x86, //0x000037b0 .quad -8758827771735200408
	0x6b, 0xaa, 0x33, 0x6f, 0x3d, 0x71, 0xd4, 0x87, //0x000037b8 .quad -8659171674854020501
	0xc2, 0x18, 0x1f, 0x51, 0xaf, 0xfd, 0x0e, 0x68, //0x000037c0 .quad 7498209359040551106
	0x06, 0x95, 0x00, 0xcb, 0x8c, 0x8d, 0xc9, 0xa9, //0x000037c8 .quad -6212278575140137722
	0xf2, 0xde, 0x66, 0x25, 0x1b, 0xbd, 0x12, 0x02, //0x000037d0 .quad 149389661945913074
	0x48, 0xba, 0xc0, 0xfd, 0xef, 0xf0, 0x3b, 0xd4, //0x000037d8 .quad -3153662200497784248
	0x57, 0x4b, 0x60, 0xf7, 0x30, 0xb6, 0x4b, 0x01, //0x000037e0 .quad 93368538716195671
	0x6d, 0x74, 0x98, 0xfe, 0x95, 0x76, 0xa5, 0x84, //0x000037e8 .quad -8888567902952197011
	0x2d, 0x5e, 0x38, 0x35, 0xbd, 0xa3, 0x9e, 0x41, //0x000037f0 .quad 4728396691822632493
	0x88, 0x91, 0x3e, 0x7e, 0x3b, 0xd4, 0xce, 0xa5, //0x000037f8 .quad -6499023860262858360
	0xb9, 0x75, 0x86, 0x82, 0xac, 0x4c, 0x06, 0x52, //0x00003800 .quad 5910495864778290617
	0xea, 0x35, 0xce, 0x5d, 0x4a, 0x89, 0x42, 0xcf, //0x00003808 .quad -3512093806901185046
	0x93, 0x09, 0x94, 0xd1, 0xeb, 0xef, 0x43, 0x73, //0x00003810 .quad 8305745933913819539
	0xb2, 0xe1, 0xa0, 0x7a, 0xce, 0x95, 0x89, 0x81, //0x00003818 .quad -9112587656954322510
	0xf8, 0x0b, 0xf9, 0xc5, 0xe6, 0xeb, 0x14, 0x10, //0x00003820 .quad 1158810380537498616
	0x1f, 0x1a, 0x49, 0x19, 0x42, 0xfb, 0xeb, 0xa1, //0x00003828 .quad -6779048552765515233
	0xf6, 0x4e, 0x77, 0x77, 0xe0, 0x26, 0x1a, 0xd4, //0x00003830 .quad -3163173042755514634
	0xa6, 0x60, 0x9b, 0x9f, 0x12, 0xfa, 0x66, 0xca, //0x00003838 .quad -3862124672529506138
	0xb4, 0x22, 0x55, 0x95, 0x98, 0xb0, 0x20, 0x89, //0x00003840 .quad -8565652321871781196
	0xd0, 0x38, 0x82, 0x47, 0x97, 0xb8, 0x00, 0xfd, //0x00003848 .quad -215969822234494768
	0xb0, 0x35, 0x55, 0x5d, 0x5f, 0x6e, 0xb4, 0x55, //0x00003850 .quad 6175682344898606512
	0x82, 0x63, 0xb1, 0x8c, 0x5e, 0x73, 0x20, 0x9e, //0x00003858 .quad -7052510166537641086
	0x1d, 0x83, 0xaa, 0x34, 0xf7, 0x89, 0x21, 0xeb, //0x00003860 .quad -1503769105731517667
	0x62, 0xbc, 0xdd, 0x2f, 0x36, 0x90, 0xa8, 0xc5, //0x00003868 .quad -4203951689744663454
	0xe4, 0x23, 0xd5, 0x01, 0x75, 0xec, 0xe9, 0xa5, //0x00003870 .quad -6491397400591784988
	0x7b, 0x2b, 0xd5, 0xbb, 0x43, 0xb4, 0x12, 0xf7, //0x00003878 .quad -643253593753441413
	0x6e, 0x36, 0x25, 0x21, 0xc9, 0x33, 0xb2, 0x47, //0x00003880 .quad 5166248661484910190
	0x2d, 0x3b, 0x65, 0x55, 0xaa, 0xb0, 0x6b, 0x9a, //0x00003888 .quad -7319562523736982739
	0x0a, 0x84, 0x6e, 0x69, 0xbb, 0xc0, 0x9e, 0x99, //0x00003890 .quad -7377247228426025974
	0xf8, 0x89, 0xbe, 0xea, 0xd4, 0x9c, 0x06, 0xc1, //0x00003898 .quad -4537767136243840520
	0x0d, 0x25, 0xca, 0x43, 0xea, 0x70, 0x06, 0xc0, //0x000038a0 .quad -4609873017105144563
	0x76, 0x2c, 0x6e, 0x25, 0x0a, 0x44, 0x48, 0xf1, //0x000038a8 .quad -1060522901877412746
	0x28, 0x57, 0x5e, 0x6a, 0x92, 0x06, 0x04, 0x38, //0x000038b0 .quad 4036358391950366504
	0xca, 0xdb, 0x64, 0x57, 0x86, 0x2a, 0xcd, 0x96, //0x000038b8 .quad -7580355841314464822
	0xf2, 0xec, 0xf5, 0x04, 0x37, 0x08, 0x05, 0xc6, //0x000038c0 .quad -4177924046916817678
	0xbc, 0x12, 0x3e, 0xed, 0x27, 0x75, 0x80, 0xbc, //0x000038c8 .quad -4863758783215693124
	0x2e, 0x68, 0x33, 0xc6, 0x44, 0x4a, 0x86, 0xf7, //0x000038d0 .quad -610719040218634194
	0x6b, 0x97, 0x8d, 0xe8, 0x71, 0x92, 0xa0, 0xeb, //0x000038d8 .quad -1468012460592228501
	0x1d, 0x21, 0xe0, 0xfb, 0x6a, 0xee, 0xb3, 0x7a, //0x000038e0 .quad 8841672636718129437
	0xa3, 0x7e, 0x58, 0x31, 0x87, 0x5b, 0x44, 0x93, //0x000038e8 .quad -7835036815511224669
	0x64, 0x29, 0xd8, 0xba, 0x05, 0xea, 0x60, 0x59, //0x000038f0 .quad 6440404777470273892
	0x4c, 0x9e, 0xae, 0xfd, 0x68, 0x72, 0x15, 0xb8, //0x000038f8 .quad -5182110000961642932
	0xbd, 0x33, 0x8e, 0x29, 0x87, 0x24, 0xb9, 0x6f, //0x00003900 .quad 8050505971837842365
	0xdf, 0x45, 0x1a, 0x3d, 0x03, 0xcf, 0x1a, 0xe6, //0x00003908 .quad -1865951482774665761
	0x56, 0xe0, 0xf8, 0x79, 0xd4, 0xb6, 0xd3, 0xa5, //0x00003910 .quad -6497648813669818282
	0xab, 0x6b, 0x30, 0x06, 0x62, 0xc1, 0xd0, 0x8f, //0x00003918 .quad -8083748704375247957
	0x6c, 0x18, 0x77, 0x98, 0x89, 0xa4, 0x48, 0x8f, //0x00003920 .quad -8122061017087272852
	0x96, 0x86, 0xbc, 0x87, 0xba, 0xf1, 0xc4, 0xb3, //0x00003928 .quad -5492999862041672042
	0x87, 0xde, 0x94, 0xfe, 0xab, 0xcd, 0x1a, 0x33, //0x00003930 .quad 3682481783923072647
	0x3c, 0xa8, 0xab, 0x29, 0x29, 0x2e, 0xb6, 0xe0, //0x00003938 .quad -2254563809124702148
	0x14, 0x0b, 0x1d, 0x7f, 0x8b, 0xc0, 0xf0, 0x9f, //0x00003940 .quad -6921820921902855404
	0x25, 0x49, 0x0b, 0xba, 0xd9, 0xdc, 0x71, 0x8c, //0x00003948 .quad -8326631408344020699
	0xd9, 0x4d, 0xe4, 0x5e, 0xae, 0xf0, 0xec, 0x07, //0x00003950 .quad 571095884476206553
	0x6f, 0x1b, 0x8e, 0x28, 0x10, 0x54, 0x8e, 0xaf, //0x00003958 .quad -5796603242002637969
	0x50, 0x61, 0x9d, 0xf6, 0xd9, 0x2c, 0xe8, 0xc9, //0x00003960 .quad -3897816162832129712
	0x4a, 0xa2, 0xb1, 0x32, 0x14, 0xe9, 0x71, 0xdb, //0x00003968 .quad -2634068034075909558
	0xd2, 0x5c, 0x22, 0x3a, 0x08, 0x1c, 0x31, 0xbe, //0x00003970 .quad -4741978110983775022
	0x6e, 0x05, 0xaf, 0x9f, 0xac, 0x31, 0x27, 0x89, //0x00003978 .quad -8563821548938525330
	0x06, 0xf4, 0xaa, 0x48, 0x0a, 0x63, 0xbd, 0x6d, //0x00003980 .quad 7907585416552444934
	0xca, 0xc6, 0x9a, 0xc7, 0x17, 0xfe, 0x70, 0xab, //0x00003988 .quad -6093090917745768758
	0x08, 0xb1, 0xd5, 0xda, 0xcc, 0xbb, 0x2c, 0x09, //0x00003990 .quad 661109733835780360
	0x7d, 0x78, 0x81, 0xb9, 0x9d, 0x3d, 0x4d, 0xd6, //0x00003998 .quad -3004677628754823043
	0xa5, 0x8e, 0xc5, 0x08, 0x60, 0xf5, 0xbb, 0x25, //0x000039a0 .quad 2719036592861056677
	0x4e, 0xeb, 0xf0, 0x93, 0x82, 0x46, 0xf0, 0x85, //0x000039a8 .quad -8795452545612846258
	0x4e, 0xf2, 0xf6, 0x0a, 0xb8, 0xf2, 0x2a, 0xaf, //0x000039b0 .quad -5824576295778454962
	0x21, 0x26, 0xed, 0x38, 0x23, 0x58, 0x6c, 0xa7, //0x000039b8 .quad -6382629663588669919
	0xe1, 0xae, 0xb4, 0x0d, 0x66, 0xaf, 0xf5, 0x1a, //0x000039c0 .quad 1942651667131707105
	0xaa, 0x6f, 0x28, 0x07, 0x2c, 0x6e, 0x47, 0xd1, //0x000039c8 .quad -3366601061058449494
	0x4d, 0xed, 0x90, 0xc8, 0x9f, 0x8d, 0xd9, 0x50, //0x000039d0 .quad 5825843310384704845
	0xca, 0x45, 0x79, 0x84, 0xdb, 0xa4, 0xcc, 0x82, //0x000039d8 .quad -9021654690802612790
	0xa0, 0x28, 0xb5, 0xba, 0x07, 0xf1, 0x0f, 0xe5, //0x000039e0 .quad -1941067898873894752
	0x3c, 0x97, 0x97, 0x65, 0x12, 0xce, 0x7f, 0xa3, //0x000039e8 .quad -6665382345075878084
	0xc8, 0x72, 0x62, 0xa9, 0x49, 0xed, 0x53, 0x1e, //0x000039f0 .quad 2185351144835019464
	0x0c, 0x7d, 0xfd, 0xfe, 0x96, 0xc1, 0x5f, 0xcc, //0x000039f8 .quad -3720041912917459700
	0x7a, 0x0f, 0xbb, 0x13, 0x9c, 0xe8, 0xe8, 0x25, //0x00003a00 .quad 2731688931043774330
	0x4f, 0xdc, 0xbc, 0xbe, 0xfc, 0xb1, 0x77, 0xff, //0x00003a08 .quad -38366372719436721
	0xac, 0xe9, 0x54, 0x8c, 0x61, 0x91, 0xb1, 0x77, //0x00003a10 .quad 8624834609543440812
	0xb1, 0x09, 0x36, 0xf7, 0x3d, 0xcf, 0xaa, 0x9f, //0x00003a18 .quad -6941508010590729807
	0x17, 0x24, 0x6a, 0xef, 0xb9, 0xf5, 0x9d, 0xd5, //0x00003a20 .quad -3054014793352862697
	0x1d, 0x8c, 0x03, 0x75, 0x0d, 0x83, 0x95, 0xc7, //0x00003a28 .quad -4065198994811024355
	0x1d, 0xad, 0x44, 0x6b, 0x28, 0x73, 0x05, 0x4b, //0x00003a30 .quad 5405853545163697437
	0x25, 0x6f, 0x44, 0xd2, 0xd0, 0xe3, 0x7a, 0xf9, //0x00003a38 .quad -469812725086392539
	0x32, 0xec, 0x0a, 0x43, 0xf9, 0x67, 0xe3, 0x4e, //0x00003a40 .quad 5684501474941004850
	0x77, 0xc5, 0x6a, 0x83, 0x62, 0xce, 0xec, 0x9b, //0x00003a48 .quad -7211161980820077193
	0x3f, 0xa7, 0xcd, 0x93, 0xf7, 0x41, 0x9c, 0x22, //0x00003a50 .quad 2493940825248868159
	0xd5, 0x76, 0x45, 0x24, 0xfb, 0x01, 0xe8, 0xc2, //0x00003a58 .quad -4402266457597708587
	0x0f, 0x11, 0xc1, 0x78, 0x75, 0x52, 0x43, 0x6b, //0x00003a60 .quad 7729112049988473103
	0x8a, 0xd4, 0x56, 0xed, 0x79, 0x02, 0xa2, 0xf3, //0x00003a68 .quad -891147053569747830
	0xa9, 0xaa, 0x78, 0x6b, 0x89, 0x13, 0x0a, 0x83, //0x00003a70 .quad -9004363024039368023
	0xd6, 0x44, 0x56, 0x34, 0x8c, 0x41, 0x45, 0x98, //0x00003a78 .quad -7474495936122174250
	0x53, 0xd5, 0x56, 0xc6, 0x6b, 0x98, 0xcc, 0x23, //0x00003a80 .quad 2579604275232953683
	0x0c, 0xd6, 0x6b, 0x41, 0xef, 0x91, 0x56, 0xbe, //0x00003a88 .quad -4731433901725329908
	0xa8, 0x8a, 0xec, 0xb7, 0x86, 0xbe, 0xbf, 0x2c, //0x00003a90 .quad 3224505344041192104
	0x8f, 0xcb, 0xc6, 0x11, 0x6b, 0x36, 0xec, 0xed, //0x00003a98 .quad -1302606358729274481
	0xa9, 0xd6, 0xf3, 0x32, 0x14, 0xd7, 0xf7, 0x7b, //0x00003aa0 .quad 8932844867666826921
	0x39, 0x3f, 0x1c, 0xeb, 0x02, 0xa2, 0xb3, 0x94, //0x00003aa8 .quad -7731658001846878407
	0x53, 0xcc, 0xb0, 0x3f, 0xd9, 0xcc, 0xf5, 0xda, //0x00003ab0 .quad -2669001970698630061
	0x07, 0x4f, 0xe3, 0xa5, 0x83, 0x8a, 0xe0, 0xb9, //0x00003ab8 .quad -5052886483881210105
	0x68, 0xff, 0x9c, 0x8f, 0x0f, 0x40, 0xb3, 0xd1, //0x00003ac0 .quad -3336252463373287576
	0xc9, 0x22, 0x5c, 0x8f, 0x24, 0xad, 0x58, 0xe8, //0x00003ac8 .quad -1704422086424124727
	0xa1, 0x1f, 0xc2, 0xb9, 0x09, 0x08, 0x10, 0x23, //0x00003ad0 .quad 2526528228819083169
	0xbe, 0x95, 0x99, 0xd9, 0x36, 0x6c, 0x37, 0x91, //0x00003ad8 .quad -7982792831656159810
	0x8a, 0xa7, 0x32, 0x28, 0x0c, 0x0a, 0xd4, 0xab, //0x00003ae0 .quad -6065211750830921846
	0x2d, 0xfb, 0xff, 0x8f, 0x44, 0x47, 0x85, 0xb5, //0x00003ae8 .quad -5366805021142811859
	0x6c, 0x51, 0x3f, 0x32, 0x8f, 0x0c, 0xc9, 0x16, //0x00003af0 .quad 1641857348316123500
	0xf9, 0xf9, 0xff, 0xb3, 0x15, 0x99, 0xe6, 0xe2, //0x00003af8 .quad -2096820258001126919
	0xe3, 0x92, 0x67, 0x7f, 0xd9, 0xa7, 0x3d, 0xae, //0x00003b00 .quad -5891368184943504669
	0x3b, 0xfc, 0x7f, 0x90, 0xad, 0x1f, 0xd0, 0x8d, //0x00003b08 .quad -8228041688891786181
	0x9c, 0x77, 0x41, 0xdf, 0xcf, 0x11, 0xcd, 0x99, //0x00003b10 .quad -7364210231179380836
	0x4a, 0xfb, 0x9f, 0xf4, 0x98, 0x27, 0x44, 0xb1, //0x00003b18 .quad -5673366092687344822
	0x83, 0xd5, 0x11, 0xd7, 0x43, 0x56, 0x40, 0x40, //0x00003b20 .quad 4629795266307937667
	0x1d, 0xfa, 0xc7, 0x31, 0x7f, 0x31, 0x95, 0xdd, //0x00003b28 .quad -2480021597431793123
	0x72, 0x25, 0x6b, 0x66, 0xea, 0x35, 0x28, 0x48, //0x00003b30 .quad 5199465050656154994
	0x52, 0xfc, 0x1c, 0x7f, 0xef, 0x3e, 0x7d, 0x8a, //0x00003b38 .quad -8467542526035952558
	0xcf, 0xee, 0x05, 0x00, 0x65, 0x43, 0x32, 0xda, //0x00003b40 .quad -2724040723534582065
	0x66, 0x3b, 0xe4, 0x5e, 0xab, 0x8e, 0x1c, 0xad, //0x00003b48 .quad -5972742139117552794
	0x82, 0x6a, 0x07, 0x40, 0x3e, 0xd4, 0xbe, 0x90, //0x00003b50 .quad -8016736922845615486
	0x40, 0x4a, 0x9d, 0x36, 0x56, 0xb2, 0x63, 0xd8, //0x00003b58 .quad -2854241655469553088
	0x91, 0xa2, 0x04, 0xe8, 0xa6, 0x44, 0x77, 0x5a, //0x00003b60 .quad 6518754469289960081
	0x68, 0x4e, 0x22, 0xe2, 0x75, 0x4f, 0x3e, 0x87, //0x00003b68 .quad -8701430062309552536
	0x36, 0xcb, 0x05, 0xa2, 0xd0, 0x15, 0x15, 0x71, //0x00003b70 .quad 8148443086612450102
	0x02, 0xe2, 0xaa, 0x5a, 0x53, 0xe3, 0x0d, 0xa9, //0x00003b78 .quad -6265101559459552766
	0x03, 0x3e, 0x87, 0xca, 0x44, 0x5b, 0x5a, 0x0d, //0x00003b80 .quad 962181821410786819
	0x83, 0x9a, 0x55, 0x31, 0x28, 0x5c, 0x51, 0xd3, //0x00003b88 .quad -3219690930897053053
	0xc2, 0x86, 0x94, 0xfe, 0x0a, 0x79, 0x58, 0xe8, //0x00003b90 .quad -1704479370831952190
	0x91, 0x80, 0xd5, 0x1e, 0x99, 0xd9, 0x12, 0x84, //0x00003b98 .quad -8929835859451740015
	0x72, 0xa8, 0x39, 0xbe, 0x4d, 0x97, 0x6e, 0x62, //0x00003ba0 .quad 7092772823314835570
	0xb6, 0xe0, 0x8a, 0x66, 0xff, 0x8f, 0x17, 0xa5, //0x00003ba8 .quad -6550608805887287114
	0x8f, 0x12, 0xc8, 0x2d, 0x21, 0x3d, 0x0a, 0xfb, //0x00003bb0 .quad -357406007711231345
	0xe3, 0x98, 0x2d, 0x40, 0xff, 0x73, 0x5d, 0xce, //0x00003bb8 .quad -3576574988931720989
	0x99, 0x0b, 0x9d, 0xbc, 0x34, 0x66, 0xe6, 0x7c, //0x00003bc0 .quad 8999993282035256217
	0x8e, 0x7f, 0x1c, 0x88, 0x7f, 0x68, 0xfa, 0x80, //0x00003bc8 .quad -9152888395723407474
	0x80, 0x4e, 0xc4, 0xeb, 0xc1, 0xff, 0x1f, 0x1c, //0x00003bd0 .quad 2026619565689294464
	0x72, 0x9f, 0x23, 0x6a, 0x9f, 0x02, 0x39, 0xa1, //0x00003bd8 .quad -6829424476226871438
	0x20, 0x62, 0xb5, 0x66, 0xb2, 0xff, 0x27, 0xa3, //0x00003be0 .quad -6690097579743157728
	0x4e, 0x87, 0xac, 0x44, 0x47, 0x43, 0x87, 0xc9, //0x00003be8 .quad -3925094576856201394
	0xa8, 0xba, 0x62, 0x00, 0x9f, 0xff, 0xf1, 0x4b, //0x00003bf0 .quad 5472436080603216552
	0x22, 0xa9, 0xd7, 0x15, 0x19, 0x14, 0xe9, 0xfb, //0x00003bf8 .quad -294682202642863838
	0xa9, 0xb4, 0x3d, 0x60, 0xc3, 0x3f, 0x77, 0x6f, //0x00003c00 .quad 8031958568804398249
	0xb5, 0xc9, 0xa6, 0xad, 0x8f, 0xac, 0x71, 0x9d, //0x00003c08 .quad -7101705404292871755
	0xd3, 0x21, 0x4d, 0x38, 0xb4, 0x0f, 0x55, 0xcb, //0x00003c10 .quad -3795109844276665901
	0x22, 0x7c, 0x10, 0x99, 0xb3, 0x17, 0xce, 0xc4, //0x00003c18 .quad -4265445736938701790
	0x48, 0x6a, 0x60, 0x46, 0xa1, 0x53, 0x2a, 0x7e, //0x00003c20 .quad 9091170749936331336
	0x2b, 0x9b, 0x54, 0x7f, 0xa0, 0x9d, 0x01, 0xf6, //0x00003c28 .quad -720121152745989333
	0x6d, 0x42, 0xfc, 0xcb, 0x44, 0x74, 0xda, 0x2e, //0x00003c30 .quad 3376138709496513133
	0xfb, 0xe0, 0x94, 0x4f, 0x84, 0x02, 0xc1, 0x99, //0x00003c38 .quad -7367604748107325189
	0x08, 0x53, 0xfb, 0xfe, 0x55, 0x11, 0x91, 0xfa, //0x00003c40 .quad -391512631556746488
	0x39, 0x19, 0x7a, 0x63, 0x25, 0x43, 0x31, 0xc0, //0x00003c48 .quad -4597819916706768583
	0xca, 0x27, 0xba, 0x7e, 0xab, 0x55, 0x35, 0x79, //0x00003c50 .quad 8733981247408842698
	0x88, 0x9f, 0x58, 0xbc, 0xee, 0x93, 0x3d, 0xf0, //0x00003c58 .quad -1135588877456072824
	0xde, 0x58, 0x34, 0x2f, 0x8b, 0x55, 0xc1, 0x4b, //0x00003c60 .quad 5458738279630526686
	0xb5, 0x63, 0xb7, 0x35, 0x75, 0x7c, 0x26, 0x96, //0x00003c68 .quad -7627272076051127371
	0x16, 0x6f, 0x01, 0xfb, 0xed, 0xaa, 0xb1, 0x9e, //0x00003c70 .quad -7011635205744005354
	0xa2, 0x3c, 0x25, 0x83, 0x92, 0x1b, 0xb0, 0xbb, //0x00003c78 .quad -4922404076636521310
	0xdc, 0xca, 0xc1, 0x79, 0xa9, 0x15, 0x5e, 0x46, //0x00003c80 .quad 5070514048102157020
	0xcb, 0x8b, 0xee, 0x23, 0x77, 0x22, 0x9c, 0xea, //0x00003c88 .quad -1541319077368263733
	0xc9, 0x1e, 0x19, 0xec, 0x89, 0xcd, 0xfa, 0x0b, //0x00003c90 .quad 863228270850154185
	0x5f, 0x17, 0x75, 0x76, 0x8a, 0x95, 0xa1, 0x92, //0x00003c98 .quad -7880853450996246689
	0x7b, 0x66, 0x1f, 0x67, 0xec, 0x80, 0xf9, 0xce, //0x00003ca0 .quad -3532650679864695173
	0x36, 0x5d, 0x12, 0x14, 0xed, 0xfa, 0x49, 0xb7, //0x00003ca8 .quad -5239380795317920458
	0x1a, 0x40, 0xe7, 0x80, 0x27, 0xe1, 0xb7, 0x82, //0x00003cb0 .quad -9027499368258256870
	0x84, 0xf4, 0x16, 0x59, 0xa8, 0x79, 0x1c, 0xe5, //0x00003cb8 .quad -1937539975720012668
	0x10, 0x88, 0x90, 0xb0, 0xb8, 0xec, 0xb2, 0xd1, //0x00003cc0 .quad -3336344095947716592
	0xd2, 0x58, 0xae, 0x37, 0x09, 0xcc, 0x31, 0x8f, //0x00003cc8 .quad -8128491512466089774
	0x15, 0xaa, 0xb4, 0xdc, 0xe6, 0xa7, 0x1f, 0x86, //0x00003cd0 .quad -8782116138362033643
	0x07, 0xef, 0x99, 0x85, 0x0b, 0x3f, 0xfe, 0xb2, //0x00003cd8 .quad -5548928372155224313
	0x9a, 0xd4, 0xe1, 0x93, 0xe0, 0x91, 0xa7, 0x67, //0x00003ce0 .quad 7469098900757009562
	0xc9, 0x6a, 0x00, 0x67, 0xce, 0xce, 0xbd, 0xdf, //0x00003ce8 .quad -2324474446766642487
	0xe0, 0x24, 0x6d, 0x5c, 0x2c, 0xbb, 0xc8, 0xe0, //0x00003cf0 .quad -2249342214667950880
	0xbd, 0x42, 0x60, 0x00, 0x41, 0xa1, 0xd6, 0x8b, //0x00003cf8 .quad -8370325556870233411
	0x18, 0x6e, 0x88, 0x73, 0xf7, 0xe9, 0xfa, 0x58, //0x00003d00 .quad 6411694268519837208
	0x6d, 0x53, 0x78, 0x40, 0x91, 0x49, 0xcc, 0xae, //0x00003d08 .quad -5851220927660403859
	0x9e, 0x89, 0x6a, 0x50, 0x75, 0xa4, 0x39, 0xaf, //0x00003d10 .quad -5820440219632367202
	0x48, 0x68, 0x96, 0x90, 0xf5, 0x5b, 0x7f, 0xda, //0x00003d18 .quad -2702340141148116920
	0x03, 0x96, 0x42, 0x52, 0xc9, 0x06, 0x84, 0x6d, //0x00003d20 .quad 7891439908798240259
	0x2d, 0x01, 0x5e, 0x7a, 0x79, 0x99, 0x8f, 0x88, //0x00003d28 .quad -8606491615858654931
	0x83, 0x3b, 0xd3, 0xa6, 0x7b, 0x08, 0xe5, 0xc8, //0x00003d30 .quad -3970758169284363389
	0x78, 0x81, 0xf5, 0xd8, 0xd7, 0x7f, 0xb3, 0xaa, //0x00003d38 .quad -6146428501395930760
	0x64, 0x0a, 0x88, 0x90, 0x9a, 0x4a, 0x1e, 0xfb, //0x00003d40 .quad -351761693178066332
	0xd6, 0xe1, 0x32, 0xcf, 0xcd, 0x5f, 0x60, 0xd5, //0x00003d48 .quad -3071349608317525546
	0x7f, 0x06, 0x55, 0x9a, 0xa0, 0xee, 0xf2, 0x5c, //0x00003d50 .quad 6697677969404790399
	0x26, 0xcd, 0x7f, 0xa1, 0xe0, 0x3b, 0x5c, 0x85, //0x00003d58 .quad -8837122532839535322
	0x1e, 0x48, 0xea, 0xc0, 0x48, 0xaa, 0x2f, 0xf4, //0x00003d60 .quad -851274575098787810
	0x6f, 0xc0, 0xdf, 0xc9, 0xd8, 0x4a, 0xb3, 0xa6, //0x00003d68 .quad -6434717147622031249
	0x26, 0xda, 0x24, 0xf1, 0xda, 0x94, 0x3b, 0xf1, //0x00003d70 .quad -1064093218873484762
	0x8b, 0xb0, 0x57, 0xfc, 0x8e, 0x1d, 0x60, 0xd0, //0x00003d78 .quad -3431710416100151157
	0x58, 0x08, 0xb7, 0xd6, 0x08, 0x3d, 0xc5, 0x76, //0x00003d80 .quad 8558313775058847832
	0x57, 0xce, 0xb6, 0x5d, 0x79, 0x12, 0x3c, 0x82, //0x00003d88 .quad -9062348037703676329
	0x6e, 0xca, 0x64, 0x0c, 0x4b, 0x8c, 0x76, 0x54, //0x00003d90 .quad 6086206200396171886
	0xed, 0x81, 0x24, 0xb5, 0x17, 0x17, 0xcb, 0xa2, //0x00003d98 .quad -6716249028702207507
	0x09, 0xfd, 0x7d, 0xcf, 0x5d, 0x2f, 0x94, 0xa9, //0x00003da0 .quad -6227300304786948855
	0x68, 0xa2, 0x6d, 0xa2, 0xdd, 0xdc, 0x7d, 0xcb, //0x00003da8 .quad -3783625267450371480
	0x4c, 0x7c, 0x5d, 0x43, 0x35, 0x3b, 0xf9, 0xd3, //0x00003db0 .quad -3172439362556298164
	0x02, 0x0b, 0x09, 0x0b, 0x15, 0x54, 0x5d, 0xfe, //0x00003db8 .quad -117845565885576446
	0xaf, 0x6d, 0x1a, 0x4a, 0x01, 0xc5, 0x7b, 0xc4, //0x00003dc0 .quad -4288617610811380305
	0xe1, 0xa6, 0xe5, 0x26, 0x8d, 0x54, 0xfa, 0x9e, //0x00003dc8 .quad -6991182506319567135
	0x1b, 0x09, 0xa1, 0x9c, 0x41, 0xb6, 0x9a, 0x35, //0x00003dd0 .quad 3862600023340550427
	0x9a, 0x10, 0x9f, 0x70, 0xb0, 0xe9, 0xb8, 0xc6, //0x00003dd8 .quad -4127292114472071014
	0x62, 0x4b, 0xc9, 0x03, 0xd2, 0x63, 0x01, 0xc3, //0x00003de0 .quad -4395122007679087774
	0xc0, 0xd4, 0xc6, 0x8c, 0x1c, 0x24, 0x67, 0xf8, //0x00003de8 .quad -547429124662700864
	0x1d, 0xcf, 0x5d, 0x42, 0x63, 0xde, 0xe0, 0x79, //0x00003df0 .quad 8782263791269039901
	0xf8, 0x44, 0xfc, 0xd7, 0x91, 0x76, 0x40, 0x9b, //0x00003df8 .quad -7259672230555269896
	0xe4, 0x42, 0xf5, 0x12, 0xfc, 0x15, 0x59, 0x98, //0x00003e00 .quad -7468914334623251740
	0x36, 0x56, 0xfb, 0x4d, 0x36, 0x94, 0x10, 0xc2, //0x00003e08 .quad -4462904269766699466
	0x9d, 0x93, 0xb2, 0x17, 0x7b, 0x5b, 0x6f, 0x3e, //0x00003e10 .quad 4498915137003099037
	0xc4, 0x2b, 0x7a, 0xe1, 0x43, 0xb9, 0x94, 0xf2, //0x00003e18 .quad -966944318780986428
	0x42, 0x9c, 0xcf, 0xee, 0x2c, 0x99, 0x05, 0xa7, //0x00003e20 .quad -6411550076227838910
	0x5a, 0x5b, 0xec, 0x6c, 0xca, 0xf3, 0x9c, 0x97, //0x00003e28 .quad -7521869226879198374
	0x53, 0x83, 0x83, 0x2a, 0x78, 0xff, 0xc6, 0x50, //0x00003e30 .quad 5820620459997365075
	0x31, 0x72, 0x27, 0x08, 0xbd, 0x30, 0x84, 0xbd, //0x00003e38 .quad -4790650515171610063
	0x28, 0x64, 0x24, 0x35, 0x56, 0xbf, 0xf8, 0xa4, //0x00003e40 .quad -6559282480285457368
	0xbd, 0x4e, 0x31, 0x4a, 0xec, 0x3c, 0xe5, 0xec, //0x00003e48 .quad -1376627125537124675
	0x99, 0xbe, 0x36, 0xe1, 0x95, 0x77, 0x1b, 0x87, //0x00003e50 .quad -8711237568605798759
	0x36, 0xd1, 0x5e, 0xae, 0x13, 0x46, 0x0f, 0x94, //0x00003e58 .quad -7777920981101784778
	0x3f, 0x6e, 0x84, 0x59, 0x7b, 0x55, 0xe2, 0x28, //0x00003e60 .quad 2946011094524915263
	0x84, 0x85, 0xf6, 0x99, 0x98, 0x17, 0x13, 0xb9, //0x00003e68 .quad -5110715207949843068
	0xcf, 0x89, 0xe5, 0x2f, 0xda, 0xea, 0x1a, 0x33, //0x00003e70 .quad 3682513868156144079
	0xe5, 0x26, 0x74, 0xc0, 0x7e, 0xdd, 0x57, 0xe7, //0x00003e78 .quad -1776707991509915931
	0x21, 0x76, 0xef, 0x5d, 0xc8, 0xd2, 0xf0, 0x3f, //0x00003e80 .quad 4607414176811284001
	0x4f, 0x98, 0x48, 0x38, 0x6f, 0xea, 0x96, 0x90, //0x00003e88 .quad -8027971522334779313
	0xa9, 0x53, 0x6b, 0x75, 0x7a, 0x07, 0xed, 0x0f, //0x00003e90 .quad 1147581702586717097
	0x63, 0xbe, 0x5a, 0x06, 0x0b, 0xa5, 0xbc, 0xb4, //0x00003e98 .quad -5423278384491086237
	0x94, 0x28, 0xc6, 0x12, 0x59, 0x49, 0xe8, 0xd3, //0x00003ea0 .quad -3177208890193991532
	0xfb, 0x6d, 0xf1, 0xc7, 0x4d, 0xce, 0xeb, 0xe1, //0x00003ea8 .quad -2167411962186469893
	0x5c, 0xd9, 0xbb, 0xab, 0xd7, 0x2d, 0x71, 0x64, //0x00003eb0 .quad 7237616480483531100
	0xbd, 0xe4, 0xf6, 0x9c, 0xf0, 0x60, 0x33, 0x8d, //0x00003eb8 .quad -8272161504007625539
	0xb3, 0xcf, 0xaa, 0x96, 0x4d, 0x79, 0x8d, 0xbd, //0x00003ec0 .quad -4788037454677749837
	0xec, 0x9d, 0x34, 0xc4, 0x2c, 0x39, 0x80, 0xb0, //0x00003ec8 .quad -5728515861582144020
	0xa0, 0x83, 0x55, 0xfc, 0xa0, 0xd7, 0xf0, 0xec, //0x00003ed0 .quad -1373360799919799392
	0x67, 0xc5, 0x41, 0xf5, 0x77, 0x47, 0xa0, 0xdc, //0x00003ed8 .quad -2548958808550292121
	0x44, 0x72, 0xb5, 0x9d, 0xc4, 0x86, 0x16, 0xf4, //0x00003ee0 .quad -858350499949874620
	0x60, 0x1b, 0x49, 0xf9, 0xaa, 0x2c, 0xe4, 0x89, //0x00003ee8 .quad -8510628282985014432
	0xd5, 0xce, 0x22, 0xc5, 0x75, 0x28, 0x1c, 0x31, //0x00003ef0 .quad 3538747893490044629
	0x39, 0x62, 0x9b, 0xb7, 0xd5, 0x37, 0x5d, 0xac, //0x00003ef8 .quad -6026599335303880135
	0x8b, 0x82, 0x6b, 0x36, 0x93, 0x32, 0x63, 0x7d, //0x00003f00 .quad 9035120885289943691
	0xc7, 0x3a, 0x82, 0x25, 0xcb, 0x85, 0x74, 0xd7, //0x00003f08 .quad -2921563150702462265
	0x97, 0x31, 0x03, 0x02, 0x9c, 0xff, 0x5d, 0xae, //0x00003f10 .quad -5882264492762254953
	0xbc, 0x64, 0x71, 0xf7, 0x9e, 0xd3, 0xa8, 0x86, //0x00003f18 .quad -8743505996830120772
	0xfc, 0xfd, 0x83, 0x02, 0x83, 0x7f, 0xf5, 0xd9, //0x00003f20 .quad -2741144597525430788
	0xeb, 0xbd, 0x4d, 0xb5, 0x86, 0x08, 0x53, 0xa8, //0x00003f28 .quad -6317696477610263061
	0x7b, 0xfd, 0x24, 0xc3, 0x63, 0xdf, 0x72, 0xd0, //0x00003f30 .quad -3426430746906788485
	0x66, 0x2d, 0xa1, 0x62, 0xa8, 0xca, 0x67, 0xd2, //0x00003f38 .quad -3285434578585440922
	0x6d, 0x1e, 0xf7, 0x59, 0x9e, 0xcb, 0x47, 0x42, //0x00003f40 .quad 4776009810824339053
	0x60, 0xbc, 0xa4, 0x3d, 0xa9, 0xde, 0x80, 0x83, //0x00003f48 .quad -8970925639256982432
	0x08, 0xe6, 0x74, 0xf0, 0x85, 0xbe, 0xd9, 0x52, //0x00003f50 .quad 5970012263530423816
	0x78, 0xeb, 0x0d, 0x8d, 0x53, 0x16, 0x61, 0xa4, //0x00003f58 .quad -6601971030643840136
	0x8b, 0x1f, 0x92, 0x6c, 0x27, 0x2e, 0x90, 0x67, //0x00003f60 .quad 7462515329413029771
	0x56, 0x66, 0x51, 0x70, 0xe8, 0x5b, 0x79, 0xcd, //0x00003f68 .quad -3640777769877412266
	0xb6, 0x53, 0xdb, 0xa3, 0xd8, 0x1c, 0xba, 0x00, //0x00003f70 .quad 52386062455755702
	0xf6, 0xdf, 0x32, 0x46, 0x71, 0xd9, 0x6b, 0x80, //0x00003f78 .quad -9193015133814464522
	0xa4, 0x28, 0xd2, 0xcc, 0x0e, 0xa4, 0xe8, 0x80, //0x00003f80 .quad -9157889458785081180
	0xf3, 0x97, 0xbf, 0x97, 0xcd, 0xcf, 0x86, 0xa0, //0x00003f88 .quad -6879582898840692749
	0xcd, 0xb2, 0x06, 0x80, 0x12, 0xcd, 0x22, 0x61, //0x00003f90 .quad 6999382250228200141
	0xf0, 0x7d, 0xaf, 0xfd, 0xc0, 0x83, 0xa8, 0xc8, //0x00003f98 .quad -3987792605123478032
	0x81, 0x5f, 0x08, 0x20, 0x57, 0x80, 0x6b, 0x79, //0x00003fa0 .quad 8749227812785250177
	0x6c, 0x5d, 0x1b, 0x3d, 0xb1, 0xa4, 0xd2, 0xfa, //0x00003fa8 .quad -373054737976959636
	0xb0, 0x3b, 0x05, 0x74, 0x36, 0x30, 0xe3, 0xcb, //0x00003fb0 .quad -3755104653863994448
	0x63, 0x1a, 0x31, 0xc6, 0xee, 0xa6, 0xc3, 0x9c, //0x00003fb8 .quad -7150688238876681629
	0x9c, 0x8a, 0x06, 0x11, 0x44, 0xfc, 0xdb, 0xbe, //0x00003fc0 .quad -4693880817329993060
	0xfc, 0x60, 0xbd, 0x77, 0xaa, 0x90, 0xf4, 0xc3, //0x00003fc8 .quad -4326674280168464132
	0x44, 0x2d, 0x48, 0x15, 0x55, 0xfb, 0x92, 0xee, //0x00003fd0 .quad -1255665003235103420
	0x3b, 0xb9, 0xac, 0x15, 0xd5, 0xb4, 0xf1, 0xf4, //0x00003fd8 .quad -796656831783192261
	0x4a, 0x1c, 0x4d, 0x2d, 0x15, 0xdd, 0x1b, 0x75, //0x00003fe0 .quad 8438581409832836170
	0xc5, 0xf3, 0x8b, 0x2d, 0x05, 0x11, 0x17, 0x99, //0x00003fe8 .quad -7415439547505577019
	0x5d, 0x63, 0xa0, 0x78, 0x5a, 0xd4, 0x62, 0xd2, //0x00003ff0 .quad -3286831292991118499
	0xb6, 0xf0, 0xee, 0x78, 0x46, 0xd5, 0x5c, 0xbf, //0x00003ff8 .quad -4657613415954583370
	0x34, 0x7c, 0xc8, 0x16, 0x71, 0x89, 0xfb, 0x86, //0x00004000 .quad -8720225134666286028
	0xe4, 0xac, 0x2a, 0x17, 0x98, 0x0a, 0x34, 0xef, //0x00004008 .quad -1210330751515841308
	0xa0, 0x4d, 0x3d, 0xae, 0xe6, 0x35, 0x5d, 0xd4, //0x00004010 .quad -3144297699952734816
	0x0e, 0xac, 0x7a, 0x0e, 0x9f, 0x86, 0x80, 0x95, //0x00004018 .quad -7673985747338482674
	0x09, 0xa1, 0xcc, 0x59, 0x60, 0x83, 0x74, 0x89, //0x00004020 .quad -8542058143368306423
	0x12, 0x57, 0x19, 0xd2, 0x46, 0xa8, 0xe0, 0xba, //0x00004028 .quad -4980796165745715438
	0x4b, 0xc9, 0x3f, 0x70, 0x38, 0xa4, 0xd1, 0x2b, //0x00004030 .quad 3157485376071780683
	0xd7, 0xac, 0x9f, 0x86, 0x58, 0xd2, 0x98, 0xe9, //0x00004038 .quad -1614309188754756393
	0xcf, 0xdd, 0x27, 0x46, 0xa3, 0x06, 0x63, 0x7b, //0x00004040 .quad 8890957387685944783
	0x06, 0xcc, 0x23, 0x54, 0x77, 0x83, 0xff, 0x91, //0x00004048 .quad -7926472270612804602
	0x42, 0xd5, 0xb1, 0x17, 0x4c, 0xc8, 0x3b, 0x1a, //0x00004050 .quad 1890324697752655170
	0x08, 0xbf, 0x2c, 0x29, 0x55, 0x64, 0x7f, 0xb6, //0x00004058 .quad -5296404319838617848
	0x93, 0x4a, 0x9e, 0x1d, 0x5f, 0xba, 0xca, 0x20, //0x00004060 .quad 2362905872190818963
	0xca, 0xee, 0x77, 0x73, 0x6a, 0x3d, 0x1f, 0xe4, //0x00004068 .quad -2008819381370884406
	0x9c, 0xee, 0x82, 0x72, 0x7b, 0xb4, 0x7e, 0x54, //0x00004070 .quad 6088502188546649756
	0x3e, 0xf5, 0x2a, 0x88, 0x62, 0x86, 0x93, 0x8e, //0x00004078 .quad -8173041140997884610
	0x43, 0xaa, 0x23, 0x4f, 0x9a, 0x61, 0x9e, 0xe9, //0x00004080 .quad -1612744301171463613
	0x8d, 0xb2, 0x35, 0x2a, 0xfb, 0x67, 0x38, 0xb2, //0x00004088 .quad -5604615407819967859
	0xd4, 0x94, 0xec, 0xe2, 0x00, 0xfa, 0x05, 0x64, //0x00004090 .quad 7207441660390446292
	0x31, 0x1f, 0xc3, 0xf4, 0xf9, 0x81, 0xc6, 0xde, //0x00004098 .quad -2394083241347571919
	0x04, 0xdd, 0xd3, 0x8d, 0x40, 0xbc, 0x83, 0xde, //0x000040a0 .quad -2412877989897052924
	0x7e, 0xf3, 0xf9, 0x38, 0x3c, 0x11, 0x3c, 0x8b, //0x000040a8 .quad -8413831053483314306
	0x45, 0xd4, 0x48, 0xb1, 0x50, 0xab, 0x24, 0x96, //0x000040b0 .quad -7627783505798704059
	0x5e, 0x70, 0x38, 0x47, 0x8b, 0x15, 0x0b, 0xae, //0x000040b8 .quad -5905602798426754978
	0x57, 0x09, 0x9b, 0xdd, 0x24, 0xd6, 0xad, 0x3b, //0x000040c0 .quad 4300328673033783639
	0x76, 0x8c, 0x06, 0x19, 0xee, 0xda, 0x8d, 0xd9, //0x000040c8 .quad -2770317479606055818
	0xd6, 0xe5, 0x80, 0x0a, 0xd7, 0xa5, 0x4c, 0xe5, //0x000040d0 .quad -1923980597781273130
	0xc9, 0x17, 0xa4, 0xcf, 0xd4, 0xa8, 0xf8, 0x87, //0x000040d8 .quad -8648977452394866743
	0x4c, 0x1f, 0x21, 0xcd, 0x4c, 0xcf, 0x9f, 0x5e, //0x000040e0 .quad 6818396289628184396
	0xbc, 0x1d, 0x8d, 0x03, 0x0a, 0xd3, 0xf6, 0xa9, //0x000040e8 .quad -6199535797066195524
	0x1f, 0x67, 0x69, 0x00, 0x20, 0xc3, 0x47, 0x76, //0x000040f0 .quad 8522995362035230495
	0x2b, 0x65, 0x70, 0x84, 0xcc, 0x87, 0x74, 0xd4, //0x000040f8 .quad -3137733727905356501
	0x73, 0xe0, 0x41, 0x00, 0xf4, 0xd9, 0xec, 0x29, //0x00004100 .quad 3021029092058325107
	0x3b, 0x3f, 0xc6, 0xd2, 0xdf, 0xd4, 0xc8, 0x84, //0x00004108 .quad -8878612607581929669
	0x90, 0x58, 0x52, 0x00, 0x71, 0x10, 0x68, 0xf4, //0x00004110 .quad -835399653354481520
	0x09, 0xcf, 0x77, 0xc7, 0x17, 0x0a, 0xfb, 0xa5, //0x00004118 .quad -6486579741050024183
	0xb4, 0xee, 0x66, 0x40, 0x8d, 0x14, 0x82, 0x71, //0x00004120 .quad 8179122470161673908
	0xcc, 0xc2, 0x55, 0xb9, 0x9d, 0xcc, 0x79, 0xcf, //0x00004128 .quad -3496538657885142324
	0x30, 0x55, 0x40, 0x48, 0xd8, 0x4c, 0xf1, 0xc6, //0x00004130 .quad -4111420493003729616
	0xbf, 0x99, 0xd5, 0x93, 0xe2, 0x1f, 0xac, 0x81, //0x00004138 .quad -9102865688819295809
	0x7c, 0x6a, 0x50, 0x5a, 0x0e, 0xa0, 0xad, 0xb8, //0x00004140 .quad -5139275616254662020
	0x2f, 0x00, 0xcb, 0x38, 0xdb, 0x27, 0x17, 0xa2, //0x00004148 .quad -6766896092596731857
	0x1c, 0x85, 0xe4, 0xf0, 0x11, 0x08, 0xd9, 0xa6, //0x00004150 .quad -6424094520318327524
	0x3b, 0xc0, 0xfd, 0x06, 0xd2, 0xf1, 0x9c, 0xca, //0x00004158 .quad -3846934097318526917
	0x63, 0xa6, 0x1d, 0x6d, 0x16, 0x4a, 0x8f, 0x90, //0x00004160 .quad -8030118150397909405
	0x4a, 0x30, 0xbd, 0x88, 0x46, 0x2e, 0x44, 0xfd, //0x00004168 .quad -196981603220770742
	0xfe, 0x87, 0x32, 0x04, 0x4e, 0x8e, 0x59, 0x9a, //0x00004170 .quad -7324666853212387330
	0x2e, 0x3e, 0x76, 0x15, 0xec, 0x9c, 0x4a, 0x9e, //0x00004178 .quad -7040642529654063570
	0xfd, 0x29, 0x3f, 0x85, 0xe1, 0xf1, 0xef, 0x40, //0x00004180 .quad 4679224488766679549
	0xba, 0xcd, 0xd3, 0x1a, 0x27, 0x44, 0xdd, 0xc5, //0x00004188 .quad -4189117143640191558
	0x7c, 0xf4, 0x8e, 0xe6, 0x59, 0xee, 0x2b, 0xd1, //0x00004190 .quad -3374341425896426372
	0x28, 0xc1, 0x88, 0xe1, 0x30, 0x95, 0x54, 0xf7, //0x00004198 .quad -624710411122851544
	0xce, 0x58, 0x19, 0x30, 0xf8, 0x74, 0xbb, 0x82, //0x000041a0 .quad -9026492418826348338
	0xb9, 0x78, 0xf5, 0x8c, 0x3e, 0xdd, 0x94, 0x9a, //0x000041a8 .quad -7307973034592864071
	0x01, 0xaf, 0x1f, 0x3c, 0x36, 0x52, 0x6a, 0xe3, //0x000041b0 .quad -2059743486678159615
	0xe7, 0xd6, 0x32, 0x30, 0x8e, 0x14, 0x3a, 0xc1, //0x000041b8 .quad -4523280274813692185
	0xc1, 0x9a, 0x27, 0xcb, 0xc3, 0xe6, 0x44, 0xdc, //0x000041c0 .quad -2574679358347699519
	0xa1, 0x8c, 0x3f, 0xbc, 0xb1, 0x99, 0x88, 0xf1, //0x000041c8 .quad -1042414325089727327
	0xb9, 0xc0, 0xf8, 0x5e, 0x3a, 0x10, 0xab, 0x29, //0x000041d0 .quad 3002511419460075705
	0xe5, 0xb7, 0xa7, 0x15, 0x0f, 0x60, 0xf5, 0x96, //0x000041d8 .quad -7569037980822161435
	0xe7, 0xf0, 0xb6, 0xf6, 0x48, 0xd4, 0x15, 0x74, //0x000041e0 .quad 8364825292752482535
	0xde, 0xa5, 0x11, 0xdb, 0x12, 0xb8, 0xb2, 0xbc, //0x000041e8 .quad -4849611457600313890
	0x21, 0xad, 0x64, 0x34, 0x5b, 0x49, 0x1b, 0x11, //0x000041f0 .quad 1232659579085827361
	0x56, 0x0f, 0xd6, 0x91, 0x17, 0x66, 0xdf, 0xeb, //0x000041f8 .quad -1450328303573004458
	0x34, 0xec, 0xbe, 0x00, 0xd9, 0x0d, 0xb1, 0xca, //0x00004200 .quad -3841273781498745804
	0x95, 0xc9, 0x25, 0xbb, 0xce, 0x9f, 0x6b, 0x93, //0x00004208 .quad -7823984217374209643
	0x42, 0xa7, 0xee, 0x40, 0x4f, 0x51, 0x5d, 0x3d, //0x00004210 .quad 4421779809981343554
	0xfb, 0x3b, 0xef, 0x69, 0xc2, 0x87, 0x46, 0xb8, //0x00004218 .quad -5168294253290374149
	0x12, 0x51, 0x2a, 0x11, 0xa3, 0xa5, 0xb4, 0x0c, //0x00004220 .quad 915538744049291538
	0xfa, 0x0a, 0x6b, 0x04, 0xb3, 0x29, 0x58, 0xe6, //0x00004228 .quad -1848681798185579782
	0xab, 0x72, 0xba, 0xea, 0x85, 0xe7, 0xf0, 0x47, //0x00004230 .quad 5183897733458195115
	0xdc, 0xe6, 0xc2, 0xe2, 0x0f, 0x1a, 0xf7, 0x8f, //0x00004238 .quad -8072955151507069220
	0x56, 0x0f, 0x69, 0x65, 0x67, 0x21, 0xed, 0x59, //0x00004240 .quad 6479872166822743894
	0x93, 0xa0, 0x73, 0xdb, 0x93, 0xe0, 0xf4, 0xb3, //0x00004248 .quad -5479507920956448621
	0x2c, 0x53, 0xc3, 0x3e, 0xc1, 0x69, 0x68, 0x30, //0x00004250 .quad 3488154190101041964
	0xb8, 0x88, 0x50, 0xd2, 0xb8, 0x18, 0xf2, 0xe0, //0x00004258 .quad -2237698882768172872
	0xfb, 0x13, 0x3a, 0xc7, 0x18, 0x42, 0x41, 0x1e, //0x00004260 .quad 2180096368813151227
	0x73, 0x55, 0x72, 0x83, 0x73, 0x4f, 0x97, 0x8c, //0x00004268 .quad -8316090829371189901
	0xfa, 0x98, 0x08, 0xf9, 0x9e, 0x92, 0xd1, 0xe5, //0x00004270 .quad -1886565557410948870
	0xcf, 0xea, 0x4e, 0x64, 0x50, 0x23, 0xbd, 0xaf, //0x00004278 .quad -5783427518286599473
	0x39, 0xbf, 0x4a, 0xb7, 0x46, 0xf7, 0x45, 0xdf, //0x00004280 .quad -2358206946763686087
	0x83, 0xa5, 0x62, 0x7d, 0x24, 0x6c, 0xac, 0xdb, //0x00004288 .quad -2617598379430861437
	0x83, 0xb7, 0x8e, 0x32, 0x8c, 0xba, 0x8b, 0x6b, //0x00004290 .quad 7749492695127472003
	0x72, 0xa7, 0x5d, 0xce, 0x96, 0xc3, 0x4b, 0x89, //0x00004298 .quad -8553528014785370254
	0x64, 0x65, 0x32, 0x3f, 0x2f, 0xa9, 0x6e, 0x06, //0x000042a0 .quad 463493832054564196
	0x4f, 0x11, 0xf5, 0x81, 0x7c, 0xb4, 0x9e, 0xab, //0x000042a8 .quad -6080224000054324913
	0xbd, 0xfe, 0xfe, 0x0e, 0x7b, 0x53, 0x0a, 0xc8, //0x000042b0 .quad -4032318728359182659
	0xa2, 0x55, 0x72, 0xa2, 0x9b, 0x61, 0x86, 0xd6, //0x000042b8 .quad -2988593981640518238
	0x36, 0x5f, 0x5f, 0xe9, 0x2c, 0x74, 0x06, 0xbd, //0x000042c0 .quad -4826042214438183114
	0x85, 0x75, 0x87, 0x45, 0x01, 0xfd, 0x13, 0x86, //0x000042c8 .quad -8785400266166405755
	0x04, 0x37, 0xb7, 0x23, 0x38, 0x11, 0x48, 0x2c, //0x000042d0 .quad 3190819268807046916
	0xe7, 0x52, 0xe9, 0x96, 0x41, 0xfc, 0x98, 0xa7, //0x000042d8 .quad -6370064314280619289
	0xc5, 0x04, 0xa5, 0x2c, 0x86, 0x15, 0x5a, 0xf7, //0x000042e0 .quad -623161932418579259
	0xa0, 0xa7, 0xa3, 0xfc, 0x51, 0x3b, 0x7f, 0xd1, //0x000042e8 .quad -3350894374423386208
	0xfb, 0x22, 0xe7, 0xdb, 0x73, 0x4d, 0x98, 0x9a, //0x000042f0 .quad -7307005235402693893
	0xc4, 0x48, 0xe6, 0x3d, 0x13, 0x85, 0xef, 0x82, //0x000042f8 .quad -9011838011655698236
	0xba, 0xeb, 0xe0, 0xd2, 0xd0, 0x60, 0x3e, 0xc1, //0x00004300 .quad -4522070525825979462
	0xf5, 0xda, 0x5f, 0x0d, 0x58, 0x66, 0xab, 0xa3, //0x00004308 .quad -6653111496142234891
	0xa8, 0x26, 0x99, 0x07, 0x05, 0xf9, 0x8d, 0x31, //0x00004310 .quad 3570783879572301480
	0xb3, 0xd1, 0xb7, 0x10, 0xee, 0x3f, 0x96, 0xcc, //0x00004318 .quad -3704703351750405709
	0x52, 0x70, 0x7f, 0x49, 0x46, 0x77, 0xf1, 0xfd, //0x00004320 .quad -148206168962011054
	0x1f, 0xc6, 0xe5, 0x94, 0xe9, 0xcf, 0xbb, 0xff, //0x00004328 .quad -19193171260619233
	0x33, 0xa6, 0xef, 0xed, 0x8b, 0xea, 0xb6, 0xfe, //0x00004330 .quad -92628855601256909
	0xd3, 0x9b, 0x0f, 0xfd, 0xf1, 0x61, 0xd5, 0x9f, //0x00004338 .quad -6929524759678968877
	0xc0, 0x8f, 0x6b, 0xe9, 0x2e, 0xa5, 0x64, 0xfe, //0x00004340 .quad -115786069501571136
	0xc8, 0x82, 0x53, 0x7c, 0x6e, 0xba, 0xca, 0xc7, //0x00004348 .quad -4050219931171323192
	0xb0, 0x73, 0xc6, 0xa3, 0x7a, 0xce, 0xfd, 0x3d, //0x00004350 .quad 4466953431550423984
	0x7b, 0x63, 0x68, 0x1b, 0x0a, 0x69, 0xbd, 0xf9, //0x00004358 .quad -451088895536766085
	0x4e, 0x08, 0x5c, 0xa6, 0x0c, 0xa1, 0xbe, 0x06, //0x00004360 .quad 486002885505321038
	0x2d, 0x3e, 0x21, 0x51, 0xa6, 0x61, 0x16, 0x9c, //0x00004368 .quad -7199459587351560659
	0x62, 0x0a, 0xf3, 0xcf, 0x4f, 0x49, 0x6e, 0x48, //0x00004370 .quad 5219189625309039202
	0xb8, 0x8d, 0x69, 0xe5, 0x0f, 0xfa, 0x1b, 0xc3, //0x00004378 .quad -4387638465762062920
	0xfa, 0xcc, 0xef, 0xc3, 0xa3, 0xdb, 0x89, 0x5a, //0x00004380 .quad 6523987031636299002
	0x26, 0xf1, 0xc3, 0xde, 0x93, 0xf8, 0xe2, 0xf3, //0x00004388 .quad -872862063775190746
	0x1c, 0xe0, 0x75, 0x5a, 0x46, 0x29, 0x96, 0xf8, //0x00004390 .quad -534194123654701028
	0xb7, 0x76, 0x3a, 0x6b, 0x5c, 0xdb, 0x6d, 0x98, //0x00004398 .quad -7463067817500576073
	0x23, 0x58, 0x13, 0xf1, 0x97, 0xb3, 0xbb, 0xf6, //0x000043a0 .quad -667742654568376285
	0x65, 0x14, 0x09, 0x86, 0x33, 0x52, 0x89, 0xbe, //0x000043a8 .quad -4717148753448332187
	0x2c, 0x2e, 0x58, 0xed, 0x7d, 0xa0, 0x6a, 0x74, //0x000043b0 .quad 8388693718644305452
	0x7f, 0x59, 0x8b, 0x67, 0xc0, 0xa6, 0x2b, 0xee, //0x000043b8 .quad -1284749923383027329
	0xdc, 0x1c, 0x57, 0xb4, 0x4e, 0xa4, 0xc2, 0xa8, //0x000043c0 .quad -6286281471915778852
	0xef, 0x17, 0xb7, 0x40, 0x38, 0x48, 0xdb, 0x94, //0x000043c8 .quad -7720497729755473937
	0x13, 0xe4, 0x6c, 0x61, 0x62, 0x4d, 0xf3, 0x92, //0x000043d0 .quad -7857851839894723565
	0xeb, 0xdd, 0xe4, 0x50, 0x46, 0x1a, 0x12, 0xba, //0x000043d8 .quad -5038936143766954517
	0x17, 0x1d, 0xc8, 0xf9, 0xba, 0x20, 0xb0, 0x77, //0x000043e0 .quad 8624429273841147159
	0x66, 0x15, 0x1e, 0xe5, 0xd7, 0xa0, 0x96, 0xe8, //0x000043e8 .quad -1686984161281305242
	0x2e, 0x12, 0x1d, 0xdc, 0x74, 0x14, 0xce, 0x0a, //0x000043f0 .quad 778582277723329070
	0x60, 0xcd, 0x32, 0xef, 0x86, 0x24, 0x5e, 0x91, //0x000043f8 .quad -7971894128441897632
	0xba, 0x56, 0x24, 0x13, 0x92, 0x99, 0x81, 0x0d, //0x00004400 .quad 973227847154161338
	0xb8, 0x80, 0xff, 0xaa, 0xa8, 0xad, 0xb5, 0xb5, //0x00004408 .quad -5353181642124984136
	0x69, 0x6c, 0xed, 0x97, 0xf6, 0xff, 0xe1, 0x10, //0x00004410 .quad 1216534808942701673
	0xe6, 0x60, 0xbf, 0xd5, 0x12, 0x19, 0x23, 0xe3, //0x00004418 .quad -2079791034228842266
	0xc1, 0x63, 0xf4, 0x1e, 0xfa, 0x3f, 0x8d, 0xca, //0x00004420 .quad -3851351762838199359
	0x8f, 0x9c, 0x97, 0xc5, 0xab, 0xef, 0xf5, 0x8d, //0x00004428 .quad -8217398424034108273
	0xb2, 0x7c, 0xb1, 0xa6, 0xf8, 0x8f, 0x30, 0xbd, //0x00004430 .quad -4814189703547749198
	0xb3, 0x83, 0xfd, 0xb6, 0x96, 0x6b, 0x73, 0xb1, //0x00004438 .quad -5660062011615247437
	0xde, 0xdb, 0x5d, 0xd0, 0xf6, 0xb3, 0x7c, 0xac, //0x00004440 .quad -6017737129434686498
	0xa0, 0xe4, 0xbc, 0x64, 0x7c, 0x46, 0xd0, 0xdd, //0x00004448 .quad -2463391496091671392
	0x6b, 0xa9, 0x3a, 0x42, 0x7a, 0xf0, 0xcd, 0x6b, //0x00004450 .quad 7768129340171790699
	0xe4, 0x0e, 0xf6, 0xbe, 0x0d, 0x2c, 0xa2, 0x8a, //0x00004458 .quad -8457148712698376476
	0xc6, 0x53, 0xc9, 0xd2, 0x98, 0x6c, 0xc1, 0x86, //0x00004460 .quad -8736582398494813242
	0x9d, 0x92, 0xb3, 0x2e, 0x11, 0xb7, 0x4a, 0xad, //0x00004468 .quad -5959749872445582691
	0xb7, 0xa8, 0x7b, 0x07, 0xbf, 0xc7, 0x71, 0xe8, //0x00004470 .quad -1697355961263740745
	0x44, 0x77, 0x60, 0x7a, 0xd5, 0x64, 0x9d, 0xd8, //0x00004478 .quad -2838001322129590460
	0x72, 0x49, 0xad, 0x64, 0xd7, 0x1c, 0x47, 0x11, //0x00004480 .quad 1244995533423855986
	0x8b, 0x4a, 0x7c, 0x6c, 0x05, 0x5f, 0x62, 0x87, //0x00004488 .quad -8691279853972075893
	0xcf, 0x9b, 0xd8, 0x3d, 0x0d, 0xe4, 0x98, 0xd5, //0x00004490 .quad -3055441601647567921
	0x2d, 0x5d, 0x9b, 0xc7, 0xc6, 0xf6, 0x3a, 0xa9, //0x00004498 .quad -6252413799037706963
	0xc3, 0xc2, 0x4e, 0x8d, 0x10, 0x1d, 0xff, 0x4a, //0x000044a0 .quad 5404070034795315907
	0x79, 0x34, 0x82, 0x79, 0x78, 0xb4, 0x89, 0xd3, //0x000044a8 .quad -3203831230369745799
	0xba, 0x39, 0x51, 0x58, 0x2a, 0x72, 0xdf, 0xce, //0x000044b0 .quad -3539985255894009414
	0xcb, 0x60, 0xf1, 0x4b, 0xcb, 0x10, 0x36, 0x84, //0x000044b8 .quad -8919923546622172981
	0x28, 0x88, 0x65, 0xee, 0xb4, 0x4e, 0x97, 0xc2, //0x000044c0 .quad -4424981569867511768
	0xfe, 0xb8, 0xed, 0x1e, 0xfe, 0x94, 0x43, 0xa5, //0x000044c8 .quad -6538218414850328322
	0x32, 0xea, 0xfe, 0x29, 0x62, 0x22, 0x3d, 0x73, //0x000044d0 .quad 8303831092947774002
	0x3e, 0x27, 0xa9, 0xa6, 0x3d, 0x7a, 0x94, 0xce, //0x000044d8 .quad -3561087000135522498
	0x5f, 0x52, 0x3f, 0x5a, 0x7d, 0x35, 0x06, 0x08, //0x000044e0 .quad 578208414664970847
	0x87, 0xb8, 0x29, 0x88, 0x66, 0xcc, 0x1c, 0x81, //0x000044e8 .quad -9143208402725783417
	0xf7, 0x26, 0xcf, 0xb0, 0xdc, 0xc2, 0x07, 0xca, //0x000044f0 .quad -3888925500096174345
	0xa8, 0x26, 0x34, 0x2a, 0x80, 0xff, 0x63, 0xa1, //0x000044f8 .quad -6817324484979841368
	0xb5, 0xf0, 0x02, 0xdd, 0x93, 0xb3, 0x89, 0xfc, //0x00004500 .quad -249470856692830027
	0x52, 0x30, 0xc1, 0x34, 0x60, 0xff, 0xbc, 0xc9, //0x00004508 .quad -3909969587797413806
	0xe2, 0xac, 0x43, 0xd4, 0x78, 0x20, 0xac, 0xbb, //0x00004510 .quad -4923524589293425438
	0x67, 0x7c, 0xf1, 0x41, 0x38, 0x3f, 0x2c, 0xfc, //0x00004518 .quad -275775966319379353
	0x0d, 0x4c, 0xaa, 0x84, 0x4b, 0x94, 0x4b, 0xd5, //0x00004520 .quad -3077202868308390899
	0xc0, 0xed, 0x36, 0x29, 0x83, 0xa7, 0x9b, 0x9d, //0x00004528 .quad -7089889006590693952
	0x11, 0xdf, 0xd4, 0x65, 0x5e, 0x79, 0x9e, 0x0a, //0x00004530 .quad 765182433041899281
	0x31, 0xa9, 0x84, 0xf3, 0x63, 0x91, 0x02, 0xc5, //0x00004538 .quad -4250675239810979535
	0xd5, 0x16, 0x4a, 0xff, 0xb5, 0x17, 0x46, 0x4d, //0x00004540 .quad 5568164059729762005
	0x7d, 0xd3, 0x65, 0xf0, 0xbc, 0x35, 0x43, 0xf6, //0x00004548 .quad -701658031336336515
	0x45, 0x4e, 0x8e, 0xbf, 0xd1, 0xce, 0x4b, 0x50, //0x00004550 .quad 5785945546544795205
	0x2e, 0xa4, 0x3f, 0x16, 0x96, 0x01, 0xea, 0x99, //0x00004558 .quad -7356065297226292178
	0xd6, 0xe1, 0x71, 0x2f, 0x86, 0xc2, 0x5e, 0xe4, //0x00004560 .quad -1990940103673781802
	0x39, 0x8d, 0xcf, 0x9b, 0xfb, 0x81, 0x64, 0xc0, //0x00004568 .quad -4583395603105477319
	0x4c, 0x5a, 0x4e, 0xbb, 0x27, 0x73, 0x76, 0x5d, //0x00004570 .quad 6734696907262548556
	0x88, 0x70, 0xc3, 0x82, 0x7a, 0xa2, 0x7d, 0xf0, //0x00004578 .quad -1117558485454458744
	0x6f, 0xf8, 0x10, 0xd5, 0xf8, 0x07, 0x6a, 0x3a, //0x00004580 .quad 4209185567039092847
	0x55, 0x26, 0xba, 0x91, 0x8c, 0x85, 0x4e, 0x96, //0x00004588 .quad -7616003081050118571
	0x8b, 0x36, 0x55, 0x0a, 0xf7, 0x89, 0x04, 0x89, //0x00004590 .quad -8573576096483297653
	0xea, 0xaf, 0x28, 0xb6, 0xef, 0x26, 0xe2, 0xbb, //0x00004598 .quad -4908317832885260310
	0x2e, 0x84, 0xea, 0xcc, 0x74, 0xac, 0x45, 0x2b, //0x000045a0 .quad 3118087934678041646
	0xe5, 0xdb, 0xb2, 0xa3, 0xab, 0xb0, 0xda, 0xea, //0x000045a8 .quad -1523711272679187483
	0x9d, 0x92, 0x12, 0x00, 0xc9, 0x8b, 0x0b, 0x3b, //0x000045b0 .quad 4254647968387469981
	0x6f, 0xc9, 0x4f, 0x46, 0x6b, 0xae, 0xc8, 0x92, //0x000045b8 .quad -7869848573065574033
	0x44, 0x37, 0x17, 0x40, 0xbb, 0x6e, 0xce, 0x09, //0x000045c0 .quad 706623942056949572
	0xcb, 0xbb, 0xe3, 0x17, 0x06, 0xda, 0x7a, 0xb7, //0x000045c8 .quad -5225624697904579637
	0x15, 0x05, 0x1d, 0x10, 0x6a, 0x0a, 0x42, 0xcc, //0x000045d0 .quad -3728406090856200939
	0xbd, 0xaa, 0xdc, 0x9d, 0x87, 0x90, 0x59, 0xe5, //0x000045d8 .quad -1920344853953336643
	0x2d, 0x23, 0x12, 0x4a, 0x82, 0x46, 0xa9, 0x9f, //0x000045e0 .quad -6941939825212513491
	0xb6, 0xea, 0xa9, 0xc2, 0x54, 0xfa, 0x57, 0x8f, //0x000045e8 .quad -8117744561361917258
	0xf9, 0xab, 0x96, 0xdc, 0x22, 0x98, 0x93, 0x47, //0x000045f0 .quad 5157633273766521849
	0x64, 0x65, 0x54, 0xf3, 0xe9, 0xf8, 0x2d, 0xb3, //0x000045f8 .quad -5535494683275008668
	0xf7, 0x56, 0xbc, 0x93, 0x2b, 0x7e, 0x78, 0x59, //0x00004600 .quad 6447041592208152311
	0xbd, 0x7e, 0x29, 0x70, 0x24, 0x77, 0xf9, 0xdf, //0x00004608 .quad -2307682335666372931
	0x5a, 0xb6, 0x55, 0x3c, 0xdb, 0x4e, 0xeb, 0x57, //0x00004610 .quad 6335244004343789146
	0x36, 0xef, 0x19, 0xc6, 0x76, 0xea, 0xfb, 0x8b, //0x00004618 .quad -8359830487432564938
	0xf1, 0x23, 0x6b, 0x0b, 0x92, 0x22, 0xe6, 0xed, //0x00004620 .quad -1304317031425039375
	0x03, 0x6b, 0xa0, 0x77, 0x14, 0xe5, 0xfa, 0xae, //0x00004628 .quad -5838102090863318269
	0xed, 0xec, 0x45, 0x8e, 0x36, 0xab, 0x5f, 0xe9, //0x00004630 .quad -1630396289281299219
	0xc4, 0x85, 0x88, 0x95, 0x59, 0x9e, 0xb9, 0xda, //0x00004638 .quad -2685941595151759932
	0x14, 0xb4, 0xeb, 0x18, 0x02, 0xcb, 0xdb, 0x11, //0x00004640 .quad 1286845328412881940
	0x9b, 0x53, 0x75, 0xfd, 0xf7, 0x02, 0xb4, 0x88, //0x00004648 .quad -8596242524610931813
	0x19, 0xa1, 0x26, 0x9f, 0xc2, 0xbd, 0x52, 0xd6, //0x00004650 .quad -3003129357911285479
	0x81, 0xa8, 0xd2, 0xfc, 0xb5, 0x03, 0xe1, 0xaa, //0x00004658 .quad -6133617137336276863
	0x5f, 0x49, 0xf0, 0x46, 0x33, 0x6d, 0xe7, 0x4b, //0x00004660 .quad 5469460339465668959
	0xa2, 0x52, 0x07, 0x7c, 0xa3, 0x44, 0x99, 0xd5, //0x00004668 .quad -3055335403242958174
	0xdb, 0x2d, 0x56, 0x0c, 0x40, 0xa4, 0x70, 0x6f, //0x00004670 .quad 8030098730593431003
	0xa5, 0x93, 0x84, 0x2d, 0xe6, 0xca, 0x7f, 0x85, //0x00004678 .quad -8827113654667930715
	0x52, 0xb9, 0x6b, 0x0f, 0x50, 0xcd, 0x4c, 0xcb, //0x00004680 .quad -3797434642040374958
	0x8e, 0xb8, 0xe5, 0xb8, 0x9f, 0xbd, 0xdf, 0xa6, //0x00004688 .quad -6422206049907525490
	0xa7, 0xa7, 0x46, 0x13, 0xa4, 0x00, 0x20, 0x7e, //0x00004690 .quad 9088264752731695015
	0xb2, 0x26, 0x1f, 0xa7, 0x07, 0xad, 0x97, 0xd0, //0x00004698 .quad -3416071543957018958
	0xc8, 0x28, 0x0c, 0x8c, 0x66, 0x00, 0xd4, 0x8e, //0x000046a0 .quad -8154892584824854328
	0x2f, 0x78, 0x73, 0xc8, 0x24, 0xcc, 0x5e, 0x82, //0x000046a8 .quad -9052573742614218705
	0xfa, 0x32, 0x0f, 0x2f, 0x80, 0x00, 0x89, 0x72, //0x000046b0 .quad 8253128342678483706
	0x3b, 0x56, 0x90, 0xfa, 0x2d, 0x7f, 0xf6, 0xa2, //0x000046b8 .quad -6704031159840385477
	0xb9, 0xff, 0xd2, 0x3a, 0xa0, 0x40, 0x2b, 0x4f, //0x000046c0 .quad 5704724409920716729
	0xca, 0x6b, 0x34, 0x79, 0xf9, 0x1e, 0xb4, 0xcb, //0x000046c8 .quad -3768352931373093942
	0xa8, 0xbf, 0x87, 0x49, 0xc8, 0x10, 0xf6, 0xe2, //0x000046d0 .quad -2092466524453879896
	0xbc, 0x86, 0x81, 0xd7, 0xb7, 0x26, 0xa1, 0xfe, //0x000046d8 .quad -98755145788979524
	0xc9, 0xd7, 0xf4, 0x2d, 0x7d, 0xca, 0xd9, 0x0d, //0x000046e0 .quad 998051431430019017
	0x36, 0xf4, 0xb0, 0xe6, 0x32, 0xb8, 0x24, 0x9f, //0x000046e8 .quad -6979250993759194058
	0xbb, 0x0d, 0x72, 0x79, 0x1c, 0x3d, 0x50, 0x91, //0x000046f0 .quad -7975807747567252037
	0x43, 0x31, 0x5d, 0xa0, 0x3f, 0xe6, 0xed, 0xc6, //0x000046f8 .quad -4112377723771604669
	0x2a, 0x91, 0xce, 0x97, 0x63, 0x4c, 0xa4, 0x75, //0x00004700 .quad 8476984389250486570
	0x94, 0x7d, 0x74, 0x88, 0xcf, 0x5f, 0xa9, 0xf8, //0x00004708 .quad -528786136287117932
	0xba, 0x1a, 0xe1, 0x3e, 0xbe, 0xaf, 0x86, 0xc9, //0x00004710 .quad -3925256793573221702
	0x7c, 0xce, 0x48, 0xb5, 0xe1, 0xdb, 0x69, 0x9b, //0x00004718 .quad -7248020362820530564
	0x68, 0x61, 0x99, 0xce, 0xad, 0x5b, 0xe8, 0xfb, //0x00004720 .quad -294884973539139224
	0x1b, 0x02, 0x9b, 0x22, 0xda, 0x52, 0x44, 0xc2, //0x00004728 .quad -4448339435098275301
	0xc3, 0xb9, 0x3f, 0x42, 0x99, 0x72, 0xe2, 0xfa, //0x00004730 .quad -368606216923924029
	0xa2, 0xc2, 0x41, 0xab, 0x90, 0x67, 0xd5, 0xf2, //0x00004738 .quad -948738275445456222
	0x1a, 0xd4, 0x67, 0xc9, 0x9f, 0x87, 0xcd, 0xdc, //0x00004740 .quad -2536221894791146470
	0xa5, 0x19, 0x09, 0x6b, 0xba, 0x60, 0xc5, 0x97, //0x00004748 .quad -7510490449794491995
	0x20, 0xc9, 0xc1, 0xbb, 0x87, 0xe9, 0x00, 0x54, //0x00004750 .quad 6053094668365842720
	0x0f, 0x60, 0xcb, 0x05, 0xe9, 0xb8, 0xb6, 0xbd, //0x00004758 .quad -4776427043815727089
	0x68, 0x3b, 0xb2, 0xaa, 0xe9, 0x23, 0x01, 0x29, //0x00004760 .quad 2954682317029915496
	0x13, 0x38, 0x3e, 0x47, 0x23, 0x67, 0x24, 0xed, //0x00004768 .quad -1358847786342270957
	0x21, 0x65, 0xaf, 0x0a, 0x72, 0xb6, 0xa0, 0xf9, //0x00004770 .quad -459166561069996767
	0x0b, 0xe3, 0x86, 0x0c, 0x76, 0xc0, 0x36, 0x94, //0x00004778 .quad -7766808894105001205
	0x69, 0x3e, 0x5b, 0x8d, 0x0e, 0xe4, 0x08, 0xf8, //0x00004780 .quad -573958201337495959
	0xce, 0x9b, 0xa8, 0x8f, 0x93, 0x70, 0x44, 0xb9, //0x00004788 .quad -5096825099203863602
	0x04, 0x0e, 0xb2, 0x30, 0x12, 0x1d, 0x0b, 0xb6, //0x00004790 .quad -5329133770099257852
	0xc2, 0xc2, 0x92, 0x73, 0xb8, 0x8c, 0x95, 0xe7, //0x00004798 .quad -1759345355577441598
	0xc2, 0x48, 0x6f, 0x5e, 0x2b, 0xf2, 0xc6, 0xb1, //0x000047a0 .quad -5636551615525730110
	0xb9, 0xb9, 0x3b, 0x48, 0xf3, 0x77, 0xbd, 0x90, //0x000047a8 .quad -8017119874876982855
	0xf3, 0x1a, 0x0b, 0x36, 0xb6, 0xae, 0x38, 0x1e, //0x000047b0 .quad 2177682517447613171
	0x28, 0xa8, 0x4a, 0x1a, 0xf0, 0xd5, 0xec, 0xb4, //0x000047b8 .quad -5409713825168840664
	0xb0, 0xe1, 0x8d, 0xc3, 0x63, 0xda, 0xc6, 0x25, //0x000047c0 .quad 2722103146809516464
	0x32, 0x52, 0xdd, 0x20, 0x6c, 0x0b, 0x28, 0xe2, //0x000047c8 .quad -2150456263033662926
	0x0e, 0xad, 0x38, 0x5a, 0x7e, 0x48, 0x9c, 0x57, //0x000047d0 .quad 6313000485183335694
	0x5f, 0x53, 0x8a, 0x94, 0x23, 0x07, 0x59, 0x8d, //0x000047d8 .quad -8261564192037121185
	0x51, 0xd8, 0xc6, 0xf0, 0x9d, 0x5a, 0x83, 0x2d, //0x000047e0 .quad 3279564588051781713
	0x37, 0xe8, 0xac, 0x79, 0xec, 0x48, 0xaf, 0xb0, //0x000047e8 .quad -5715269221619013577
	0x65, 0x8e, 0xf8, 0x6c, 0x45, 0x31, 0xe4, 0xf8, //0x000047f0 .quad -512230283362660763
	0x44, 0x22, 0x18, 0x98, 0x27, 0x1b, 0xdb, 0xdc, //0x000047f8 .quad -2532400508596379068
	0xff, 0x58, 0x1b, 0x64, 0xcb, 0x9e, 0x8e, 0x1b, //0x00004800 .quad 1985699082112030975
	0x6b, 0x15, 0x0f, 0xbf, 0xf8, 0xf0, 0x08, 0x8a, //0x00004808 .quad -8500279345513818773
	0x3f, 0x2f, 0x22, 0x3d, 0x7e, 0x46, 0x72, 0xe2, //0x00004810 .quad -2129562165787349185
	0xc5, 0xda, 0xd2, 0xee, 0x36, 0x2d, 0x8b, 0xac, //0x00004818 .quad -6013663163464885563
	0x0f, 0xbb, 0x6a, 0xcc, 0x1d, 0xd8, 0x0e, 0x5b, //0x00004820 .quad 6561419329620589327
	0x77, 0x91, 0x87, 0xaa, 0x84, 0xf8, 0xad, 0xd7, //0x00004828 .quad -2905392935903719049
	0xe9, 0xb4, 0xc2, 0x9f, 0x12, 0x47, 0xe9, 0x98, //0x00004830 .quad -7428327965055601431
	0xea, 0xba, 0x94, 0xea, 0x52, 0xbb, 0xcc, 0x86, //0x00004838 .quad -8733399612580906262
	0x24, 0x62, 0xb3, 0x47, 0xd7, 0x98, 0x23, 0x3f, //0x00004840 .quad 4549648098962661924
	0xa5, 0xe9, 0x39, 0xa5, 0x27, 0xea, 0x7f, 0xa8, //0x00004848 .quad -6305063497298744923
	0xad, 0x3a, 0xa0, 0x19, 0x0d, 0x7f, 0xec, 0x8e, //0x00004850 .quad -8147997931578836307
	0x0e, 0x64, 0x88, 0x8e, 0xb1, 0xe4, 0x9f, 0xd2, //0x00004858 .quad -3269643353196043250
	0xac, 0x24, 0x04, 0x30, 0x68, 0xcf, 0x53, 0x19, //0x00004860 .quad 1825030320404309164
	0x89, 0x3e, 0x15, 0xf9, 0xee, 0xee, 0xa3, 0x83, //0x00004868 .quad -8961056123388608887
	0xd7, 0x2d, 0x05, 0x3c, 0x42, 0xc3, 0xa8, 0x5f, //0x00004870 .quad 6892973918932774359
	0x2b, 0x8e, 0x5a, 0xb7, 0xaa, 0xea, 0x8c, 0xa4, //0x00004878 .quad -6589634135808373205
	0x4d, 0x79, 0x06, 0xcb, 0x12, 0xf4, 0x92, 0x37, //0x00004880 .quad 4004531380238580045
	0xb6, 0x31, 0x31, 0x65, 0x55, 0x25, 0xb0, 0xcd, //0x00004888 .quad -3625356651333078602
	0xd0, 0x0b, 0xe4, 0xbe, 0x8b, 0xd8, 0xbb, 0xe2, //0x00004890 .quad -2108853905778275376
	0x11, 0xbf, 0x3e, 0x5f, 0x55, 0x17, 0x8e, 0x80, //0x00004898 .quad -9183376934724255983
	0xc4, 0x0e, 0x9d, 0xae, 0xae, 0xce, 0x6a, 0x5b, //0x000048a0 .quad 6587304654631931588
	0xd6, 0x6e, 0x0e, 0xb7, 0x2a, 0x9d, 0xb1, 0xa0, //0x000048a8 .quad -6867535149977932074
	0x75, 0x52, 0x44, 0x5a, 0x5a, 0x82, 0x45, 0xf2, //0x000048b0 .quad -989241218564861323
	0x8b, 0x0a, 0xd2, 0x64, 0x75, 0x04, 0xde, 0xc8, //0x000048b8 .quad -3972732919045027189
	0x12, 0x67, 0xd5, 0xf0, 0xf0, 0xe2, 0xd6, 0xee, //0x000048c0 .quad -1236551523206076654
	0x2e, 0x8d, 0x06, 0xbe, 0x92, 0x85, 0x15, 0xfb, //0x000048c8 .quad -354230130378896082
	0x6b, 0x60, 0x85, 0x96, 0xd6, 0x4d, 0x46, 0x55, //0x000048d0 .quad 6144684325637283947
	0x3d, 0x18, 0xc4, 0xb6, 0x7b, 0x73, 0xed, 0x9c, //0x000048d8 .quad -7138922859127891907
	0x86, 0xb8, 0x26, 0x3c, 0x4c, 0xe1, 0x97, 0xaa, //0x000048e0 .quad -6154202648235558778
	0x4c, 0x1e, 0x75, 0xa4, 0x5a, 0xd0, 0x28, 0xc4, //0x000048e8 .quad -4311967555482476980
	0xa8, 0x66, 0x30, 0x4b, 0x9f, 0xd9, 0x3d, 0xd5, //0x000048f0 .quad -3081067291867060568
	0xdf, 0x65, 0x92, 0x4d, 0x71, 0x04, 0x33, 0xf5, //0x000048f8 .quad -778273425925708321
	0x29, 0x40, 0xfe, 0x8e, 0x03, 0xa8, 0x46, 0xe5, //0x00004900 .quad -1925667057416912855
	0xab, 0x7f, 0x7b, 0xd0, 0xc6, 0xe2, 0x3f, 0x99, //0x00004908 .quad -7403949918844649557
	0x33, 0xd0, 0xbd, 0x72, 0x04, 0x52, 0x98, 0xde, //0x00004910 .quad -2407083821771141069
	0x96, 0x5f, 0x9a, 0x84, 0x78, 0xdb, 0x8f, 0xbf, //0x00004918 .quad -4643251380128424042
	0x40, 0x44, 0x6d, 0x8f, 0x85, 0x66, 0x3e, 0x96, //0x00004920 .quad -7620540795641314240
	0x7c, 0xf7, 0xc0, 0xa5, 0x56, 0xd2, 0x73, 0xef, //0x00004928 .quad -1192378206733142148
	0xa8, 0x4a, 0xa4, 0x79, 0x13, 0x00, 0xe7, 0xdd, //0x00004930 .quad -2456994988062127448
	0xad, 0x9a, 0x98, 0x27, 0x76, 0x63, 0xa8, 0x95, //0x00004938 .quad -7662765406849295699
	0x52, 0x5d, 0x0d, 0x58, 0x18, 0xc0, 0x60, 0x55, //0x00004940 .quad 6152128301777116498
	0x59, 0xc1, 0x7e, 0xb1, 0x53, 0x7c, 0x12, 0xbb, //0x00004948 .quad -4966770740134231719
	0xa6, 0xb4, 0x10, 0x6e, 0x1e, 0xf0, 0xb8, 0xaa, //0x00004950 .quad -6144897678060768090
	0xaf, 0x71, 0xde, 0x9d, 0x68, 0x1b, 0xd7, 0xe9, //0x00004958 .quad -1596777406740401745
	0xe8, 0x70, 0xca, 0x04, 0x13, 0x96, 0xb3, 0xca, //0x00004960 .quad -3840561048787980056
	0x0d, 0x07, 0xab, 0x62, 0x21, 0x71, 0x26, 0x92, //0x00004968 .quad -7915514906853832947
	0x22, 0x0d, 0xfd, 0xc5, 0x97, 0x7b, 0x60, 0x3d, //0x00004970 .quad 4422670725869800738
	0xd1, 0xc8, 0x55, 0xbb, 0x69, 0x0d, 0xb0, 0xb6, //0x00004978 .quad -5282707615139903279
	0x6a, 0x50, 0x7c, 0xb7, 0x7d, 0x9a, 0xb8, 0x8c, //0x00004980 .quad -8306719647944912790
	0x05, 0x3b, 0x2b, 0x2a, 0xc4, 0x10, 0x5c, 0xe4, //0x00004988 .quad -1991698500497491195
	0x42, 0xb2, 0xad, 0x92, 0x8e, 0x60, 0xf3, 0x77, //0x00004990 .quad 8643358275316593218
	0xe3, 0x04, 0x5b, 0x9a, 0x7a, 0x8a, 0xb9, 0x8e, //0x00004998 .quad -8162340590452013853
	0xd3, 0x1e, 0x59, 0x37, 0xb2, 0x38, 0xf0, 0x55, //0x000049a0 .quad 6192511825718353619
	0x1c, 0xc6, 0xf1, 0x40, 0x19, 0xed, 0x67, 0xb2, //0x000049a8 .quad -5591239719637629412
	0x88, 0x66, 0x2f, 0xc5, 0xde, 0x46, 0x6c, 0x6b, //0x000049b0 .quad 7740639782147942024
	0xa3, 0x37, 0x2e, 0x91, 0x5f, 0xe8, 0x01, 0xdf, //0x000049b8 .quad -2377363631119648861
	0x15, 0xa0, 0x3d, 0x3b, 0x4b, 0xac, 0x23, 0x23, //0x000049c0 .quad 2532056854628769813
	0xc6, 0xe2, 0xbc, 0xba, 0x3b, 0x31, 0x61, 0x8b, //0x000049c8 .quad -8403381297090862394
	0x1a, 0x08, 0x0d, 0x0a, 0x5e, 0x97, 0xec, 0xab, //0x000049d0 .quad -6058300968568813542
	0x77, 0x1b, 0x6c, 0xa9, 0x8a, 0x7d, 0x39, 0xae, //0x000049d8 .quad -5892540602936190089
	0x21, 0x4a, 0x90, 0x8c, 0x35, 0xbd, 0xe7, 0x96, //0x000049e0 .quad -7572876210711016927
	0x55, 0x22, 0xc7, 0x53, 0xed, 0xdc, 0xc7, 0xd9, //0x000049e8 .quad -2753989735242849707
	0x54, 0x2e, 0xda, 0x77, 0x41, 0xd6, 0x50, 0x7e, //0x000049f0 .quad 9102010423587778132
	0x75, 0x75, 0x5c, 0x54, 0x14, 0xea, 0x1c, 0x88, //0x000049f8 .quad -8638772612167862923
	0xe9, 0xb9, 0xd0, 0xd5, 0xd1, 0x0b, 0xe5, 0xdd, //0x00004a00 .quad -2457545025797441047
	0xd2, 0x92, 0x73, 0x69, 0x99, 0x24, 0x24, 0xaa, //0x00004a08 .quad -6186779746782440750
	0x64, 0xe8, 0x44, 0x4b, 0xc6, 0x4e, 0x5e, 0x95, //0x00004a10 .quad -7683617300674189212
	0x87, 0x77, 0xd0, 0xc3, 0xbf, 0x2d, 0xad, 0xd4, //0x00004a18 .quad -3121788665050663033
	0x3e, 0x11, 0x0b, 0xef, 0x3b, 0xf1, 0x5a, 0xbd, //0x00004a20 .quad -4802260812921368258
	0xb4, 0x4a, 0x62, 0xda, 0x97, 0x3c, 0xec, 0x84, //0x00004a28 .quad -8868646943297746252
	0x8e, 0xd5, 0xcd, 0xea, 0x8a, 0xad, 0xb1, 0xec, //0x00004a30 .quad -1391139997724322418
	0x61, 0xdd, 0xfa, 0xd0, 0xbd, 0x4b, 0x27, 0xa6, //0x00004a38 .quad -6474122660694794911
	0xf2, 0x4a, 0x81, 0xa5, 0xed, 0x18, 0xde, 0x67, //0x00004a40 .quad 7484447039699372786
	0xba, 0x94, 0x39, 0x45, 0xad, 0x1e, 0xb1, 0xcf, //0x00004a48 .quad -3480967307441105734
	0xd7, 0xce, 0x70, 0x87, 0x94, 0xcf, 0xea, 0x80, //0x00004a50 .quad -9157278655470055721
	0xf4, 0xfc, 0x43, 0x4b, 0x2c, 0xb3, 0xce, 0x81, //0x00004a58 .quad -9093133594791772940
	0x8d, 0x02, 0x4d, 0xa9, 0x79, 0x83, 0x25, 0xa1, //0x00004a60 .quad -6834912300910181747
	0x31, 0xfc, 0x14, 0x5e, 0xf7, 0x5f, 0x42, 0xa2, //0x00004a68 .quad -6754730975062328271
	0x30, 0x43, 0xa0, 0x13, 0x58, 0xe4, 0x6e, 0x09, //0x00004a70 .quad 679731660717048624
	0x3e, 0x3b, 0x9a, 0x35, 0xf5, 0xf7, 0xd2, 0xca, //0x00004a78 .quad -3831727700400522434
	0xfc, 0x53, 0x88, 0x18, 0x6e, 0x9d, 0xca, 0x8b, //0x00004a80 .quad -8373707460958465028
	0x0d, 0xca, 0x00, 0x83, 0xf2, 0xb5, 0x87, 0xfd, //0x00004a88 .quad -177973607073265139
	0x7d, 0x34, 0x55, 0xcf, 0x64, 0xa2, 0x5e, 0x77, //0x00004a90 .quad 8601490892183123069
	0x48, 0x7e, 0xe0, 0x91, 0xb7, 0xd1, 0x74, 0x9e, //0x00004a98 .quad -7028762532061872568
	0x9d, 0x81, 0x2a, 0x03, 0xfe, 0x4a, 0x36, 0x95, //0x00004aa0 .quad -7694880458480647779
	0xda, 0x9d, 0x58, 0x76, 0x25, 0x06, 0x12, 0xc6, //0x00004aa8 .quad -4174267146649952806
	0x04, 0x22, 0xf5, 0x83, 0xbd, 0xdd, 0x83, 0x3a, //0x00004ab0 .quad 4216457482181353988
	0x51, 0xc5, 0xee, 0xd3, 0xae, 0x87, 0x96, 0xf7, //0x00004ab8 .quad -606147914885053103
	0x42, 0x35, 0x79, 0x72, 0x96, 0x6a, 0x92, 0xc4, //0x00004ac0 .quad -4282243101277735614
	0x52, 0x3b, 0x75, 0x44, 0xcd, 0x14, 0xbe, 0x9a, //0x00004ac8 .quad -7296371474444240046
	0x93, 0x82, 0x17, 0x0f, 0x3c, 0x05, 0xb7, 0x75, //0x00004ad0 .quad 8482254178684994195
	0x27, 0x8a, 0x92, 0x95, 0x00, 0x9a, 0x6d, 0xc1, //0x00004ad8 .quad -4508778324627912153
	0x38, 0x63, 0xdd, 0x12, 0x8b, 0xc6, 0x24, 0x53, //0x00004ae0 .quad 5991131704928854840
	0xb1, 0x2c, 0xf7, 0xba, 0x80, 0x00, 0xc9, 0xf1, //0x00004ae8 .quad -1024286887357502287
	0x03, 0x5e, 0xca, 0xeb, 0x16, 0xfc, 0xf6, 0xd3, //0x00004af0 .quad -3173071712060547581
	0xee, 0x7b, 0xda, 0x74, 0x50, 0xa0, 0x1d, 0x97, //0x00004af8 .quad -7557708332239520786
	0x84, 0xf5, 0xbc, 0xa6, 0x1c, 0xbb, 0xf4, 0x88, //0x00004b00 .quad -8578025658503072380
	0xea, 0x1a, 0x11, 0x92, 0x64, 0x08, 0xe5, 0xbc, //0x00004b08 .quad -4835449396872013078
	0xe5, 0x32, 0x6c, 0xd0, 0xe3, 0xe9, 0x31, 0x2b, //0x00004b10 .quad 3112525982153323237
	0xa5, 0x61, 0x95, 0xb6, 0x7d, 0x4a, 0x1e, 0xec, //0x00004b18 .quad -1432625727662628443
	0xcf, 0x9f, 0x43, 0x62, 0x2e, 0x32, 0xff, 0x3a, //0x00004b20 .quad 4251171748059520975
	0x07, 0x5d, 0x1d, 0x92, 0x8e, 0xee, 0x92, 0x93, //0x00004b28 .quad -7812920107430224633
	0xc2, 0x87, 0xd4, 0xfa, 0xb9, 0xfe, 0xbe, 0x09, //0x00004b30 .quad 702278666647013314
	0x49, 0xb4, 0xa4, 0x36, 0x32, 0xaa, 0x77, 0xb8, //0x00004b38 .quad -5154464115860392887
	0xb3, 0xa9, 0x89, 0x79, 0x68, 0xbe, 0x2e, 0x4c, //0x00004b40 .quad 5489534351736154547
	0x5b, 0xe1, 0x4d, 0xc4, 0xbe, 0x94, 0x95, 0xe6, //0x00004b48 .quad -1831394126398103205
	0x10, 0x0a, 0xf6, 0x4b, 0x01, 0x37, 0x9d, 0x0f, //0x00004b50 .quad 1125115960621402640
	0xd9, 0xac, 0xb0, 0x3a, 0xf7, 0x7c, 0x1d, 0x90, //0x00004b58 .quad -8062150356639896359
	0x94, 0x8c, 0xf3, 0x9e, 0xc1, 0x84, 0x84, 0x53, //0x00004b60 .quad 6018080969204141204
	0x0f, 0xd8, 0x5c, 0x09, 0x35, 0xdc, 0x24, 0xb4, //0x00004b68 .quad -5466001927372482545
	0xb9, 0x6f, 0xb0, 0x06, 0xf2, 0xa5, 0x65, 0x28, //0x00004b70 .quad 2910915193077788601
	0x13, 0x0e, 0xb4, 0x4b, 0x42, 0x13, 0x2e, 0xe1, //0x00004b78 .quad -2220816390788215277
	0xd3, 0x45, 0x2e, 0x44, 0xb7, 0x87, 0x3f, 0xf9, //0x00004b80 .quad -486521013540076077
	0xcb, 0x88, 0x50, 0x6f, 0x09, 0xcc, 0xbc, 0x8c, //0x00004b88 .quad -8305539271883716405
	0x48, 0xd7, 0x39, 0x15, 0xa5, 0x69, 0x8f, 0xf7, //0x00004b90 .quad -608151266925095096
	0xfe, 0xaa, 0x24, 0xcb, 0x0b, 0xff, 0xeb, 0xaf, //0x00004b98 .quad -5770238071427257602
	0x1b, 0x4d, 0x88, 0x5a, 0x0e, 0x44, 0x73, 0xb5, //0x00004ba0 .quad -5371875102083756773
	0xbe, 0xd5, 0xed, 0xbd, 0xce, 0xfe, 0xe6, 0xdb, //0x00004ba8 .quad -2601111570856684098
	0x30, 0x30, 0x95, 0xf8, 0x88, 0x0a, 0x68, 0x31, //0x00004bb0 .quad 3560107088838733872
	0x97, 0xa5, 0xb4, 0x36, 0x41, 0x5f, 0x70, 0x89, //0x00004bb8 .quad -8543223759426509417
	0x3d, 0x7c, 0xba, 0x36, 0x2b, 0x0d, 0xc2, 0xfd, //0x00004bc0 .quad -161552157378970563
	0xfc, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x00004bc8 .quad -6067343680855748868
	0x4c, 0x1b, 0x69, 0x04, 0x76, 0x90, 0x32, 0x3d, //0x00004bd0 .quad 4409745821703674700
	0xbc, 0x42, 0x7a, 0xe5, 0xd5, 0x94, 0xbf, 0xd6, //0x00004bd8 .quad -2972493582642298180
	0x0f, 0xb1, 0xc1, 0xc2, 0x49, 0x9a, 0x3f, 0xa6, //0x00004be0 .quad -6467280898289979121
	0xb5, 0x69, 0x6c, 0xaf, 0x05, 0xbd, 0x37, 0x86, //0x00004be8 .quad -8775337516792518219
	0x53, 0x1d, 0x72, 0x33, 0xdc, 0x80, 0xcf, 0x0f, //0x00004bf0 .quad 1139270913992301907
	0x23, 0x84, 0x47, 0x1b, 0x47, 0xac, 0xc5, 0xa7, //0x00004bf8 .quad -6357485877563259869
	0xa8, 0xa4, 0x4e, 0x40, 0x13, 0x61, 0xc3, 0xd3, //0x00004c00 .quad -3187597375937010520
	0x2b, 0x65, 0x19, 0xe2, 0x58, 0x17, 0xb7, 0xd1, //0x00004c08 .quad -3335171328526686933
	0xe9, 0x26, 0x31, 0x08, 0xac, 0x1c, 0x5a, 0x64, //0x00004c10 .quad 7231123676894144233
	0x3b, 0xdf, 0x4f, 0x8d, 0x97, 0x6e, 0x12, 0x83, //0x00004c18 .quad -9002011107970261189
	0xa3, 0x70, 0x3d, 0x0a, 0xd7, 0xa3, 0x70, 0x3d, //0x00004c20 .quad 4427218577690292387
	0x0a, 0xd7, 0xa3, 0x70, 0x3d, 0x0a, 0xd7, 0xa3, //0x00004c28 .quad -6640827866535438582
	0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00004c30 QUAD $0xcccccccccccccccc; QUAD $0xcccccccccccccccc  // .space 16, '\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004c40 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00004c48 .quad -9223372036854775808
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004c50 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa0, //0x00004c58 .quad -6917529027641081856
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004c60 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc8, //0x00004c68 .quad -4035225266123964416
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004c70 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfa, //0x00004c78 .quad -432345564227567616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004c80 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x9c, //0x00004c88 .quad -7187745005283311616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004c90 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xc3, //0x00004c98 .quad -4372995238176751616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004ca0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0xf4, //0x00004ca8 .quad -854558029293551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004cb0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x96, 0x98, //0x00004cb8 .quad -7451627795949551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004cc0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xbc, 0xbe, //0x00004cc8 .quad -4702848726509551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004cd0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x6b, 0xee, //0x00004cd8 .quad -1266874889709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004ce0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0xf9, 0x02, 0x95, //0x00004ce8 .quad -7709325833709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004cf0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x40, 0xb7, 0x43, 0xba, //0x00004cf8 .quad -5024971273709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004d00 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x10, 0xa5, 0xd4, 0xe8, //0x00004d08 .quad -1669528073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004d10 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x2a, 0xe7, 0x84, 0x91, //0x00004d18 .quad -7960984073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004d20 .quad 0
	0x00, 0x00, 0x00, 0x80, 0xf4, 0x20, 0xe6, 0xb5, //0x00004d28 .quad -5339544073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004d30 .quad 0
	0x00, 0x00, 0x00, 0xa0, 0x31, 0xa9, 0x5f, 0xe3, //0x00004d38 .quad -2062744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004d40 .quad 0
	0x00, 0x00, 0x00, 0x04, 0xbf, 0xc9, 0x1b, 0x8e, //0x00004d48 .quad -8206744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004d50 .quad 0
	0x00, 0x00, 0x00, 0xc5, 0x2e, 0xbc, 0xa2, 0xb1, //0x00004d58 .quad -5646744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004d60 .quad 0
	0x00, 0x00, 0x40, 0x76, 0x3a, 0x6b, 0x0b, 0xde, //0x00004d68 .quad -2446744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004d70 .quad 0
	0x00, 0x00, 0xe8, 0x89, 0x04, 0x23, 0xc7, 0x8a, //0x00004d78 .quad -8446744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004d80 .quad 0
	0x00, 0x00, 0x62, 0xac, 0xc5, 0xeb, 0x78, 0xad, //0x00004d88 .quad -5946744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004d90 .quad 0
	0x00, 0x80, 0x7a, 0x17, 0xb7, 0x26, 0xd7, 0xd8, //0x00004d98 .quad -2821744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004da0 .quad 0
	0x00, 0x90, 0xac, 0x6e, 0x32, 0x78, 0x86, 0x87, //0x00004da8 .quad -8681119073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004db0 .quad 0
	0x00, 0xb4, 0x57, 0x0a, 0x3f, 0x16, 0x68, 0xa9, //0x00004db8 .quad -6239712823709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004dc0 .quad 0
	0x00, 0xa1, 0xed, 0xcc, 0xce, 0x1b, 0xc2, 0xd3, //0x00004dc8 .quad -3187955011209551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004dd0 .quad 0
	0xa0, 0x84, 0x14, 0x40, 0x61, 0x51, 0x59, 0x84, //0x00004dd8 .quad -8910000909647051616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004de0 .quad 0
	0xc8, 0xa5, 0x19, 0x90, 0xb9, 0xa5, 0x6f, 0xa5, //0x00004de8 .quad -6525815118631426616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004df0 .quad 0
	0x3a, 0x0f, 0x20, 0xf4, 0x27, 0x8f, 0xcb, 0xce, //0x00004df8 .quad -3545582879861895366
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, //0x00004e00 .quad 4611686018427387904
	0x84, 0x09, 0x94, 0xf8, 0x78, 0x39, 0x3f, 0x81, //0x00004e08 .quad -9133518327554766460
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, //0x00004e10 .quad 5764607523034234880
	0xe5, 0x0b, 0xb9, 0x36, 0xd7, 0x07, 0x8f, 0xa1, //0x00004e18 .quad -6805211891016070171
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa4, //0x00004e20 .quad -6629298651489370112
	0xde, 0x4e, 0x67, 0x04, 0xcd, 0xc9, 0xf2, 0xc9, //0x00004e28 .quad -3894828845342699810
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4d, //0x00004e30 .quad 5548434740920451072
	0x96, 0x22, 0x81, 0x45, 0x40, 0x7c, 0x6f, 0xfc, //0x00004e38 .quad -256850038250986858
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xf0, //0x00004e40 .quad -1143914305352105984
	0x9d, 0xb5, 0x70, 0x2b, 0xa8, 0xad, 0xc5, 0x9d, //0x00004e48 .quad -7078060301547948643
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x6c, //0x00004e50 .quad 7793479155164643328
	0x05, 0xe3, 0x4c, 0x36, 0x12, 0x19, 0x37, 0xc5, //0x00004e58 .quad -4235889358507547899
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x32, 0xc7, //0x00004e60 .quad -4093209111326359552
	0xc6, 0x1b, 0xe0, 0xc3, 0x56, 0xdf, 0x84, 0xf6, //0x00004e68 .quad -683175679707046970
	0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x7f, 0x3c, //0x00004e70 .quad 4359273333062107136
	0x5c, 0x11, 0x6c, 0x3a, 0x96, 0x0b, 0x13, 0x9a, //0x00004e78 .quad -7344513827457986212
	0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x9f, 0x4b, //0x00004e80 .quad 5449091666327633920
	0xb3, 0x15, 0x07, 0xc9, 0x7b, 0xce, 0x97, 0xc0, //0x00004e88 .quad -4568956265895094861
	0x00, 0x00, 0x00, 0x00, 0x00, 0xd4, 0x86, 0x1e, //0x00004e90 .quad 2199678564482154496
	0x20, 0xdb, 0x48, 0xbb, 0x1a, 0xc2, 0xbd, 0xf0, //0x00004e98 .quad -1099509313941480672
	0x00, 0x00, 0x00, 0x00, 0x80, 0x44, 0x14, 0x13, //0x00004ea0 .quad 1374799102801346560
	0xf4, 0x88, 0x0d, 0xb5, 0x50, 0x99, 0x76, 0x96, //0x00004ea8 .quad -7604722348854507276
	0x00, 0x00, 0x00, 0x00, 0xa0, 0x55, 0xd9, 0x17, //0x00004eb0 .quad 1718498878501683200
	0x31, 0xeb, 0x50, 0xe2, 0xa4, 0x3f, 0x14, 0xbc, //0x00004eb8 .quad -4894216917640746191
	0x00, 0x00, 0x00, 0x00, 0x08, 0xab, 0xcf, 0x5d, //0x00004ec0 .quad 6759809616554491904
	0xfd, 0x25, 0xe5, 0x1a, 0x8e, 0x4f, 0x19, 0xeb, //0x00004ec8 .quad -1506085128623544835
	0x00, 0x00, 0x00, 0x00, 0xe5, 0xca, 0xa1, 0x5a, //0x00004ed0 .quad 6530724019560251392
	0xbe, 0x37, 0xcf, 0xd0, 0xb8, 0xd1, 0xef, 0x92, //0x00004ed8 .quad -7858832233030797378
	0x00, 0x00, 0x00, 0x40, 0x9e, 0x3d, 0x4a, 0xf1, //0x00004ee0 .quad -1059967012404461568
	0xad, 0x05, 0x03, 0x05, 0x27, 0xc6, 0xab, 0xb7, //0x00004ee8 .quad -5211854272861108819
	0x00, 0x00, 0x00, 0xd0, 0x05, 0xcd, 0x9c, 0x6d, //0x00004ef0 .quad 7898413271349198848
	0x19, 0xc7, 0x43, 0xc6, 0xb0, 0xb7, 0x96, 0xe5, //0x00004ef8 .quad -1903131822648998119
	0x00, 0x00, 0x00, 0xa2, 0x23, 0x00, 0x82, 0xe4, //0x00004f00 .quad -1981020733047832576
	0x6f, 0x5c, 0xea, 0x7b, 0xce, 0x32, 0x7e, 0x8f, //0x00004f08 .quad -8106986416796705681
	0x00, 0x00, 0x80, 0x8a, 0x2c, 0x80, 0xa2, 0xdd, //0x00004f10 .quad -2476275916309790720
	0x8b, 0xf3, 0xe4, 0x1a, 0x82, 0xbf, 0x5d, 0xb3, //0x00004f18 .quad -5522047002568494197
	0x00, 0x00, 0x20, 0xad, 0x37, 0x20, 0x0b, 0xd5, //0x00004f20 .quad -3095344895387238400
	0x6e, 0x30, 0x9e, 0xa1, 0x62, 0x2f, 0x35, 0xe0, //0x00004f28 .quad -2290872734783229842
	0x00, 0x00, 0x34, 0xcc, 0x22, 0xf4, 0x26, 0x45, //0x00004f30 .quad 4982938468024057856
	0x45, 0xde, 0x02, 0xa5, 0x9d, 0x3d, 0x21, 0x8c, //0x00004f38 .quad -8349324486880600507
	0x00, 0x00, 0x41, 0x7f, 0x2b, 0xb1, 0x70, 0x96, //0x00004f40 .quad -7606384970252091392
	0xd6, 0x95, 0x43, 0x0e, 0x05, 0x8d, 0x29, 0xaf, //0x00004f48 .quad -5824969590173362730
	0x00, 0x40, 0x11, 0x5f, 0x76, 0xdd, 0x0c, 0x3c, //0x00004f50 .quad 4327076842467049472
	0x4c, 0x7b, 0xd4, 0x51, 0x46, 0xf0, 0xf3, 0xda, //0x00004f58 .quad -2669525969289315508
	0x00, 0xc8, 0x6a, 0xfb, 0x69, 0x0a, 0x88, 0xa5, //0x00004f60 .quad -6518949010312869888
	0x0f, 0xcd, 0x24, 0xf3, 0x2b, 0x76, 0xd8, 0x88, //0x00004f68 .quad -8585982758446904049
	0x00, 0x7a, 0x45, 0x7a, 0x04, 0x0d, 0xea, 0x8e, //0x00004f70 .quad -8148686262891087360
	0x53, 0x00, 0xee, 0xef, 0xb6, 0x93, 0x0e, 0xab, //0x00004f78 .quad -6120792429631242157
	0x80, 0xd8, 0xd6, 0x98, 0x45, 0x90, 0xa4, 0x72, //0x00004f80 .quad 8260886245095692416
	0x68, 0x80, 0xe9, 0xab, 0xa4, 0x38, 0xd2, 0xd5, //0x00004f88 .quad -3039304518611664792
	0x50, 0x47, 0x86, 0x7f, 0x2b, 0xda, 0xa6, 0x47, //0x00004f90 .quad 5163053903184807760
	0x41, 0xf0, 0x71, 0xeb, 0x66, 0x63, 0xa3, 0x85, //0x00004f98 .quad -8817094351773372351
	0x24, 0xd9, 0x67, 0x5f, 0xb6, 0x90, 0x90, 0x99, //0x00004fa0 .quad -7381240676301154012
	0x51, 0x6c, 0x4e, 0xa6, 0x40, 0x3c, 0x0c, 0xa7, //0x00004fa8 .quad -6409681921289327535
	0x6d, 0xcf, 0x41, 0xf7, 0xe3, 0xb4, 0xf4, 0xff, //0x00004fb0 .quad -3178808521666707
	0x65, 0x07, 0xe2, 0xcf, 0x50, 0x4b, 0xcf, 0xd0, //0x00004fb8 .quad -3400416383184271515
	0xa4, 0x21, 0x89, 0x7a, 0x0e, 0xf1, 0xf8, 0xbf, //0x00004fc0 .quad -4613672773753429596
	0x9f, 0x44, 0xed, 0x81, 0x12, 0x8f, 0x81, 0x82, //0x00004fc8 .quad -9042789267131251553
	0x0d, 0x6a, 0x2b, 0x19, 0x52, 0x2d, 0xf7, 0xaf, //0x00004fd0 .quad -5767090967191786995
	0xc7, 0x95, 0x68, 0x22, 0xd7, 0xf2, 0x21, 0xa3, //0x00004fd8 .quad -6691800565486676537
	0x90, 0x44, 0x76, 0x9f, 0xa6, 0xf8, 0xf4, 0x9b, //0x00004fe0 .quad -7208863708989733744
	0x39, 0xbb, 0x02, 0xeb, 0x8c, 0x6f, 0xea, 0xcb, //0x00004fe8 .quad -3753064688430957767
	0xb4, 0xd5, 0x53, 0x47, 0xd0, 0x36, 0xf2, 0x02, //0x00004ff0 .quad 212292400617608628
	0x08, 0x6a, 0xc3, 0x25, 0x70, 0x0b, 0xe5, 0xfe, //0x00004ff8 .quad -79644842111309304
	0x90, 0x65, 0x94, 0x2c, 0x42, 0x62, 0xd7, 0x01, //0x00005000 .quad 132682750386005392
	0x45, 0x22, 0x9a, 0x17, 0x26, 0x27, 0x4f, 0x9f, //0x00005008 .quad -6967307053960650171
	0xf5, 0x7e, 0xb9, 0xb7, 0xd2, 0x3a, 0x4d, 0x42, //0x00005010 .quad 4777539456409894645
	0xd6, 0xaa, 0x80, 0x9d, 0xef, 0xf0, 0x22, 0xc7, //0x00005018 .quad -4097447799023424810
	0xb2, 0xde, 0xa7, 0x65, 0x87, 0x89, 0xe0, 0xd2, //0x00005020 .quad -3251447716342407502
	0x8b, 0xd5, 0xe0, 0x84, 0x2b, 0xad, 0xeb, 0xf8, //0x00005028 .quad -510123730351893109
	0x2f, 0xeb, 0x88, 0x9f, 0xf4, 0x55, 0xcc, 0x63, //0x00005030 .quad 7191217214140771119
	0x77, 0x85, 0x0c, 0x33, 0x3b, 0x4c, 0x93, 0x9b, //0x00005038 .quad -7236356359111015049
	0xfb, 0x25, 0x6b, 0xc7, 0x71, 0x6b, 0xbf, 0x3c, //0x00005040 .quad 4377335499248575995
	0xd5, 0xa6, 0xcf, 0xff, 0x49, 0x1f, 0x78, 0xc2, //0x00005048 .quad -4433759430461380907
	0x7a, 0xef, 0x45, 0x39, 0x4e, 0x46, 0xef, 0x8b, //0x00005050 .quad -8363388681221443718
	0x8a, 0x90, 0xc3, 0x7f, 0x1c, 0x27, 0x16, 0xf3, //0x00005058 .quad -930513269649338230
	0xac, 0xb5, 0xcb, 0xe3, 0xf0, 0x8b, 0x75, 0x97, //0x00005060 .quad -7532960934977096276
	0x56, 0x3a, 0xda, 0xcf, 0x71, 0xd8, 0xed, 0x97, //0x00005068 .quad -7499099821171918250
	0x17, 0xa3, 0xbe, 0x1c, 0xed, 0xee, 0x52, 0x3d, //0x00005070 .quad 4418856886560793367
	0xec, 0xc8, 0xd0, 0x43, 0x8e, 0x4e, 0xe9, 0xbd, //0x00005078 .quad -4762188758037509908
	0xdd, 0x4b, 0xee, 0x63, 0xa8, 0xaa, 0xa7, 0x4c, //0x00005080 .quad 5523571108200991709
	0x27, 0xfb, 0xc4, 0xd4, 0x31, 0xa2, 0x63, 0xed, //0x00005088 .quad -1341049929119499481
	0x6a, 0xef, 0x74, 0x3e, 0xa9, 0xca, 0xe8, 0x8f, //0x00005090 .quad -8076983103442849942
	0xf8, 0x1c, 0xfb, 0x24, 0x5f, 0x45, 0x5e, 0x94, //0x00005098 .quad -7755685233340769032
	0x44, 0x2b, 0x12, 0x8e, 0x53, 0xfd, 0xe2, 0xb3, //0x000050a0 .quad -5484542860876174524
	0x36, 0xe4, 0x39, 0xee, 0xb6, 0xd6, 0x75, 0xb9, //0x000050a8 .quad -5082920523248573386
	0x16, 0xb6, 0x96, 0x71, 0xa8, 0xbc, 0xdb, 0x60, //0x000050b0 .quad 6979379479186945558
	0x44, 0x5d, 0xc8, 0xa9, 0x64, 0x4c, 0xd3, 0xe7, //0x000050b8 .quad -1741964635633328828
	0xcd, 0x31, 0xfe, 0x46, 0xe9, 0x55, 0x89, 0xbc, //0x000050c0 .quad -4861259862362934835
	0x4a, 0x3a, 0x1d, 0xea, 0xbe, 0x0f, 0xe4, 0x90, //0x000050c8 .quad -8006256924911912374
	0x41, 0xbe, 0xbd, 0x98, 0x63, 0xab, 0xab, 0x6b, //0x000050d0 .quad 7758483227328495169
	0xdd, 0x88, 0xa4, 0xa4, 0xae, 0x13, 0x1d, 0xb5, //0x000050d8 .quad -5396135137712502563
	0xd1, 0x2d, 0xed, 0x7e, 0x3c, 0x96, 0x96, 0xc6, //0x000050e0 .quad -4136954021121544751
	0x14, 0xab, 0xcd, 0x4d, 0x9a, 0x58, 0x64, 0xe2, //0x000050e8 .quad -2133482903713240300
	0xa2, 0x3c, 0x54, 0xcf, 0xe5, 0x1d, 0x1e, 0xfc, //0x000050f0 .quad -279753253987271518
	0xec, 0x8a, 0xa0, 0x70, 0x60, 0xb7, 0x7e, 0x8d, //0x000050f8 .quad -8250955842461857044
	0xcb, 0x4b, 0x29, 0x43, 0x5f, 0xa5, 0x25, 0x3b, //0x00005100 .quad 4261994450943298507
	0xa8, 0xad, 0xc8, 0x8c, 0x38, 0x65, 0xde, 0xb0, //0x00005108 .quad -5702008784649933400
	0xbe, 0x9e, 0xf3, 0x13, 0xb7, 0x0e, 0xef, 0x49, //0x00005110 .quad 5327493063679123134
	0x12, 0xd9, 0xfa, 0xaf, 0x86, 0xfe, 0x15, 0xdd, //0x00005118 .quad -2515824962385028846
	0x37, 0x43, 0x78, 0x6c, 0x32, 0x69, 0x35, 0x6e, //0x00005120 .quad 7941369183226839863
	0xab, 0xc7, 0xfc, 0x2d, 0x14, 0xbf, 0x2d, 0x8a, //0x00005128 .quad -8489919629131724885
	0x04, 0x54, 0x96, 0x07, 0x7f, 0xc3, 0xc2, 0x49, //0x00005130 .quad 5315025460606161924
	0x96, 0xf9, 0x7b, 0x39, 0xd9, 0x2e, 0xb9, 0xac, //0x00005138 .quad -6000713517987268202
	0x06, 0xe9, 0x7b, 0xc9, 0x5e, 0x74, 0x33, 0xdc, //0x00005140 .quad -2579590211097073402
	0xfb, 0xf7, 0xda, 0x87, 0x8f, 0x7a, 0xe7, 0xd7, //0x00005148 .quad -2889205879056697349
	0xa3, 0x71, 0xed, 0x3d, 0xbb, 0x28, 0xa0, 0x69, //0x00005150 .quad 7611128154919104931
	0xfd, 0xda, 0xe8, 0xb4, 0x99, 0xac, 0xf0, 0x86, //0x00005158 .quad -8723282702051517699
	0x0c, 0xce, 0x68, 0x0d, 0xea, 0x32, 0x08, 0xc4, //0x00005160 .quad -4321147861633282548
	0xbc, 0x11, 0x23, 0x22, 0xc0, 0xd7, 0xac, 0xa8, //0x00005168 .quad -6292417359137009220
	0x90, 0x01, 0xc3, 0x90, 0xa4, 0x3f, 0x0a, 0xf5, //0x00005170 .quad -789748808614215280
	0x2b, 0xd6, 0xab, 0x2a, 0xb0, 0x0d, 0xd8, 0xd2, //0x00005178 .quad -3253835680493873621
	0xfa, 0xe0, 0x79, 0xda, 0xc6, 0x67, 0x26, 0x79, //0x00005180 .quad 8729779031470891258
	0xdb, 0x65, 0xab, 0x1a, 0x8e, 0x08, 0xc7, 0x83, //0x00005188 .quad -8951176327949752869
	0x38, 0x59, 0x18, 0x91, 0xb8, 0x01, 0x70, 0x57, //0x00005190 .quad 6300537770911226168
	0x52, 0x3f, 0x56, 0xa1, 0xb1, 0xca, 0xb8, 0xa4, //0x00005198 .quad -6577284391509803182
	0x86, 0x6f, 0x5e, 0xb5, 0x26, 0x02, 0x4c, 0xed, //0x000051a0 .quad -1347699823215743098
	0x26, 0xcf, 0xab, 0x09, 0x5e, 0xfd, 0xe6, 0xcd, //0x000051a8 .quad -3609919470959866074
	0xb4, 0x05, 0x5b, 0x31, 0x58, 0x81, 0x4f, 0x54, //0x000051b0 .quad 6075216638131242420
	0x78, 0x61, 0x0b, 0xc6, 0x5a, 0x5e, 0xb0, 0x80, //0x000051b8 .quad -9173728696990998152
	0x21, 0xc7, 0xb1, 0x3d, 0xae, 0x61, 0x63, 0x69, //0x000051c0 .quad 7594020797664053025
	0xd6, 0x39, 0x8e, 0x77, 0xf1, 0x75, 0xdc, 0xa0, //0x000051c8 .quad -6855474852811359786
	0xe9, 0x38, 0x1e, 0xcd, 0x19, 0x3a, 0xbc, 0x03, //0x000051d0 .quad 269153960225290473
	0x4c, 0xc8, 0x71, 0xd5, 0x6d, 0x93, 0x13, 0xc9, //0x000051d8 .quad -3957657547586811828
	0x23, 0xc7, 0x65, 0x40, 0xa0, 0x48, 0xab, 0x04, //0x000051e0 .quad 336442450281613091
	0x5f, 0x3a, 0xce, 0x4a, 0x49, 0x78, 0x58, 0xfb, //0x000051e8 .quad -335385916056126881
	0x76, 0x9c, 0x3f, 0x28, 0x64, 0x0d, 0xeb, 0x62, //0x000051f0 .quad 7127805559067090038
	0x7b, 0xe4, 0xc0, 0xce, 0x2d, 0x4b, 0x17, 0x9d, //0x000051f8 .quad -7127145225176161157
	0x94, 0x83, 0x4f, 0x32, 0xbd, 0xd0, 0xa5, 0x3b, //0x00005200 .quad 4298070930406474644
	0x9a, 0x1d, 0x71, 0x42, 0xf9, 0x1d, 0x5d, 0xc4, //0x00005208 .quad -4297245513042813542
	0x79, 0x64, 0xe3, 0x7e, 0xec, 0x44, 0x8f, 0xca, //0x00005210 .quad -3850783373846682503
	0x00, 0x65, 0x0d, 0x93, 0x77, 0x65, 0x74, 0xf5, //0x00005218 .quad -759870872876129024
	0xcb, 0x1e, 0x4e, 0xcf, 0x13, 0x8b, 0x99, 0x7e, //0x00005220 .quad 9122475437414293195
	0x20, 0x5f, 0xe8, 0xbb, 0x6a, 0xbf, 0x68, 0x99, //0x00005228 .quad -7392448323188662496
	0x7e, 0xa6, 0x21, 0xc3, 0xd8, 0xed, 0x3f, 0x9e, //0x00005230 .quad -7043649776941685122
	0xe8, 0x76, 0xe2, 0x6a, 0x45, 0xef, 0xc2, 0xbf, //0x00005238 .quad -4628874385558440216
	0x1e, 0x10, 0xea, 0xf3, 0x4e, 0xe9, 0xcf, 0xc5, //0x00005240 .quad -4192876202749718498
	0xa2, 0x14, 0x9b, 0xc5, 0x16, 0xab, 0xb3, 0xef, //0x00005248 .quad -1174406963520662366
	0x12, 0x4a, 0x72, 0x58, 0xd1, 0xf1, 0xa1, 0xbb, //0x00005250 .quad -4926390635932268014
	0xe5, 0xec, 0x80, 0x3b, 0xee, 0x4a, 0xd0, 0x95, //0x00005258 .quad -7651533379841495835
	0x97, 0xdc, 0x8e, 0xae, 0x45, 0x6e, 0x8a, 0x2a, //0x00005260 .quad 3065383741939440791
	0x1f, 0x28, 0x61, 0xca, 0xa9, 0x5d, 0x44, 0xbb, //0x00005268 .quad -4952730706374481889
	0xbd, 0x93, 0x32, 0x1a, 0xd7, 0x09, 0x2d, 0xf5, //0x00005270 .quad -779956341003086915
	0x26, 0x72, 0xf9, 0x3c, 0x14, 0x75, 0x15, 0xea, //0x00005278 .quad -1579227364540714458
	0x56, 0x9c, 0x5f, 0x70, 0x26, 0x26, 0x3c, 0x59, //0x00005280 .quad 6430056314514152534
	0x58, 0xe7, 0x1b, 0xa6, 0x2c, 0x69, 0x4d, 0x92, //0x00005288 .quad -7904546130479028392
	0x6c, 0x83, 0x77, 0x0c, 0xb0, 0x2f, 0x8b, 0x6f, //0x00005290 .quad 8037570393142690668
	0x2e, 0xe1, 0xa2, 0xcf, 0x77, 0xc3, 0xe0, 0xb6, //0x00005298 .quad -5268996644671397586
	0x47, 0x64, 0x95, 0x0f, 0x9c, 0xfb, 0x6d, 0x0b, //0x000052a0 .quad 823590954573587527
	0x7a, 0x99, 0x8b, 0xc3, 0x55, 0xf4, 0x98, 0xe4, //0x000052a8 .quad -1974559787411859078
	0xac, 0x5e, 0xbd, 0x89, 0x41, 0xbd, 0x24, 0x47, //0x000052b0 .quad 5126430365035880108
	0xec, 0x3f, 0x37, 0x9a, 0xb5, 0x98, 0xdf, 0x8e, //0x000052b8 .quad -8151628894773493780
	0x57, 0xb6, 0x2c, 0xec, 0x91, 0xec, 0xed, 0x58, //0x000052c0 .quad 6408037956294850135
	0xe7, 0x0f, 0xc5, 0x00, 0xe3, 0x7e, 0x97, 0xb2, //0x000052c8 .quad -5577850100039479321
	0xed, 0xe3, 0x37, 0x67, 0xb6, 0x67, 0x29, 0x2f, //0x000052d0 .quad 3398361426941174765
	0xe1, 0x53, 0xf6, 0xc0, 0x9b, 0x5e, 0x3d, 0xdf, //0x000052d8 .quad -2360626606621961247
	0x74, 0xee, 0x82, 0x00, 0xd2, 0xe0, 0x79, 0xbd, //0x000052e0 .quad -4793553135802847628
	0x6c, 0xf4, 0x99, 0x58, 0x21, 0x5b, 0x86, 0x8b, //0x000052e8 .quad -8392920656779807636
	0x11, 0xaa, 0xa3, 0x80, 0x06, 0x59, 0xd8, 0xec, //0x000052f0 .quad -1380255401326171631
	0x87, 0x71, 0xc0, 0xae, 0xe9, 0xf1, 0x67, 0xae, //0x000052f8 .quad -5879464802547371641
	0x95, 0x94, 0xcc, 0x20, 0x48, 0x6f, 0x0e, 0xe8, //0x00005300 .quad -1725319251657714539
	0xe9, 0x8d, 0x70, 0x1a, 0x64, 0xee, 0x01, 0xda, //0x00005308 .quad -2737644984756826647
	0xdd, 0xdc, 0x7f, 0x14, 0x8d, 0x05, 0x09, 0x31, //0x00005310 .quad 3533361486141316317
	0xb2, 0x58, 0x86, 0x90, 0xfe, 0x34, 0x41, 0x88, //0x00005318 .quad -8628557143114098510
	0x15, 0xd4, 0x9f, 0x59, 0xf0, 0x46, 0x4b, 0xbd, //0x00005320 .quad -4806670179178130411
	0xde, 0xee, 0xa7, 0x34, 0x3e, 0x82, 0x51, 0xaa, //0x00005328 .quad -6174010410465235234
	0x1a, 0xc9, 0x07, 0x70, 0xac, 0x18, 0x9e, 0x6c, //0x00005330 .quad 7826720331309500698
	0x96, 0xea, 0xd1, 0xc1, 0xcd, 0xe2, 0xe5, 0xd4, //0x00005338 .quad -3105826994654156138
	0xb0, 0xdd, 0x04, 0xc6, 0x6b, 0xcf, 0xe2, 0x03, //0x00005340 .quad 280014188641050032
	0x9e, 0x32, 0x23, 0x99, 0xc0, 0xad, 0x0f, 0x85, //0x00005348 .quad -8858670899299929442
	0x1c, 0x15, 0x86, 0xb7, 0x46, 0x83, 0xdb, 0x84, //0x00005350 .quad -8873354301053463268
	0x45, 0xff, 0x6b, 0xbf, 0x30, 0x99, 0x53, 0xa6, //0x00005358 .quad -6461652605697523899
	0x63, 0x9a, 0x67, 0x65, 0x18, 0x64, 0x12, 0xe6, //0x00005360 .quad -1868320839462053277
	0x16, 0xff, 0x46, 0xef, 0x7c, 0x7f, 0xe8, 0xcf, //0x00005368 .quad -3465379738694516970
	0x7e, 0xc0, 0x60, 0x3f, 0x8f, 0x7e, 0xcb, 0x4f, //0x00005370 .quad 5749828502977298558
	0x6e, 0x5f, 0x8c, 0x15, 0xae, 0x4f, 0xf1, 0x81, //0x00005378 .quad -9083391364325154962
	0x9d, 0xf0, 0x38, 0x0f, 0x33, 0x5e, 0xbe, 0xe3, //0x00005380 .quad -2036086408133152611
	0x49, 0x77, 0xef, 0x9a, 0x99, 0xa3, 0x6d, 0xa2, //0x00005388 .quad -6742553186979055799
	0xc5, 0x2c, 0x07, 0xd3, 0xbf, 0xf5, 0xad, 0x5c, //0x00005390 .quad 6678264026688335045
	0x1c, 0x55, 0xab, 0x01, 0x80, 0x0c, 0x09, 0xcb, //0x00005398 .quad -3816505465296431844
	0xf6, 0xf7, 0xc8, 0xc7, 0x2f, 0x73, 0xd9, 0x73, //0x000053a0 .quad 8347830033360418806
	0x63, 0x2a, 0x16, 0x02, 0xa0, 0x4f, 0xcb, 0xfd, //0x000053a8 .quad -158945813193151901
	0xfa, 0x9a, 0xdd, 0xdc, 0xfd, 0xe7, 0x67, 0x28, //0x000053b0 .quad 2911550761636567802
	0x7e, 0xda, 0x4d, 0x01, 0xc4, 0x11, 0x9f, 0x9e, //0x000053b8 .quad -7016870160886801794
	0xb8, 0x01, 0x15, 0x54, 0xfd, 0xe1, 0x81, 0xb2, //0x000053c0 .quad -5583933584809066056
	0x1d, 0x51, 0xa1, 0x01, 0x35, 0xd6, 0x46, 0xc6, //0x000053c8 .quad -4159401682681114339
	0x26, 0x42, 0x1a, 0xa9, 0x7c, 0x5a, 0x22, 0x1f, //0x000053d0 .quad 2243455055843443238
	0x65, 0xa5, 0x09, 0x42, 0xc2, 0x8b, 0xd8, 0xf7, //0x000053d8 .quad -587566084924005019
	0x58, 0x69, 0xb0, 0xe9, 0x8d, 0x78, 0x75, 0x33, //0x000053e0 .quad 3708002419115845976
	0x5f, 0x07, 0x46, 0x69, 0x59, 0x57, 0xe7, 0x9a, //0x000053e8 .quad -7284757830718584993
	0xae, 0x83, 0x1c, 0x64, 0xb1, 0xd6, 0x52, 0x00, //0x000053f0 .quad 23317005467419566
	0x37, 0x89, 0x97, 0xc3, 0x2f, 0x2d, 0xa1, 0xc1, //0x000053f8 .quad -4494261269970843337
	0x9a, 0xa4, 0x23, 0xbd, 0x5d, 0x8c, 0x67, 0xc0, //0x00005400 .quad -4582539761593113446
	0x84, 0x6b, 0x7d, 0xb4, 0x7b, 0x78, 0x09, 0xf2, //0x00005408 .quad -1006140569036166268
	0xe0, 0x46, 0x36, 0x96, 0xba, 0xb7, 0x40, 0xf8, //0x00005410 .quad -558244341782001952
	0x32, 0x63, 0xce, 0x50, 0x4d, 0xeb, 0x45, 0x97, //0x00005418 .quad -7546366883288685774
	0x98, 0xd8, 0xc3, 0x3b, 0xa9, 0xe5, 0x50, 0xb6, //0x00005420 .quad -5309491445654890344
	0xff, 0xfb, 0x01, 0xa5, 0x20, 0x66, 0x17, 0xbd, //0x00005428 .quad -4821272585683469313
	0xbe, 0xce, 0xb4, 0x8a, 0x13, 0x1f, 0xe5, 0xa3, //0x00005430 .quad -6636864307068612930
	0xff, 0x7a, 0x42, 0xce, 0xa8, 0x3f, 0x5d, 0xec, //0x00005438 .quad -1414904713676948737
	0x37, 0x01, 0xb1, 0x36, 0x6c, 0x33, 0x6f, 0xc6, //0x00005440 .quad -4148040191917883081
	0xdf, 0x8c, 0xe9, 0x80, 0xc9, 0x47, 0xba, 0x93, //0x00005448 .quad -7801844473689174817
	0x84, 0x41, 0x5d, 0x44, 0x47, 0x00, 0x0b, 0xb8, //0x00005450 .quad -5185050239897353852
	0x17, 0xf0, 0x23, 0xe1, 0xbb, 0xd9, 0xa8, 0xb8, //0x00005458 .quad -5140619573684080617
	0xe5, 0x91, 0x74, 0x15, 0x59, 0xc0, 0x0d, 0xa6, //0x00005460 .quad -6481312799871692315
	0x1d, 0xec, 0x6c, 0xd9, 0x2a, 0x10, 0xd3, 0xe6, //0x00005468 .quad -1814088448677712867
	0x2f, 0xdb, 0x68, 0xad, 0x37, 0x98, 0xc8, 0x87, //0x00005470 .quad -8662506518347195601
	0x92, 0x13, 0xe4, 0xc7, 0x1a, 0xea, 0x43, 0x90, //0x00005478 .quad -8051334308064652398
	0xfb, 0x11, 0xc3, 0x98, 0x45, 0xbe, 0xba, 0x29, //0x00005480 .quad 3006924907348169211
	0x77, 0x18, 0xdd, 0x79, 0xa1, 0xe4, 0x54, 0xb4, //0x00005488 .quad -5452481866653427593
	0x7a, 0xd6, 0xf3, 0xfe, 0xd6, 0x6d, 0x29, 0xf4, //0x00005490 .quad -853029884242176390
	0x94, 0x5e, 0x54, 0xd8, 0xc9, 0x1d, 0x6a, 0xe1, //0x00005498 .quad -2203916314889396588
	0x0c, 0x66, 0x58, 0x5f, 0xa6, 0xe4, 0x99, 0x18, //0x000054a0 .quad 1772699331562333708
	0x1d, 0xbb, 0x34, 0x27, 0x9e, 0x52, 0xe2, 0x8c, //0x000054a8 .quad -8294976724446954723
	0x8f, 0x7f, 0x2e, 0xf7, 0xcf, 0x5d, 0xc0, 0x5e, //0x000054b0 .quad 6827560182880305039
	0xe4, 0xe9, 0x01, 0xb1, 0x45, 0xe7, 0x1a, 0xb0, //0x000054b8 .quad -5757034887131305500
	0x73, 0x1f, 0xfa, 0xf4, 0x43, 0x75, 0x70, 0x76, //0x000054c0 .quad 8534450228600381299
	0x5d, 0x64, 0x42, 0x1d, 0x17, 0xa1, 0x21, 0xdc, //0x000054c8 .quad -2584607590486743971
	0xa8, 0x53, 0x1c, 0x79, 0x4a, 0x49, 0x06, 0x6a, //0x000054d0 .quad 7639874402088932264
	0xba, 0x7e, 0x49, 0x72, 0xae, 0x04, 0x95, 0x89, //0x000054d8 .quad -8532908771695296838
	0x92, 0x68, 0x63, 0x17, 0x9d, 0xdb, 0x87, 0x04, //0x000054e0 .quad 326470965756389522
	0x69, 0xde, 0xdb, 0x0e, 0xda, 0x45, 0xfa, 0xab, //0x000054e8 .quad -6054449946191733143
	0xb6, 0x42, 0x3c, 0x5d, 0x84, 0xd2, 0xa9, 0x45, //0x000054f0 .quad 5019774725622874806
	0x03, 0xd6, 0x92, 0x92, 0x50, 0xd7, 0xf8, 0xd6, //0x000054f8 .quad -2956376414312278525
	0xb2, 0xa9, 0x45, 0xba, 0x92, 0x23, 0x8a, 0x0b, //0x00005500 .quad 831516194300602802
	0xc2, 0xc5, 0x9b, 0x5b, 0x92, 0x86, 0x5b, 0x86, //0x00005508 .quad -8765264286586255934
	0x1e, 0x14, 0xd7, 0x68, 0x77, 0xac, 0x6c, 0x8e, //0x00005510 .quad -8183976793979022306
	0x32, 0xb7, 0x82, 0xf2, 0x36, 0x68, 0xf2, 0xa7, //0x00005518 .quad -6344894339805432014
	0x26, 0xd9, 0x0c, 0x43, 0x95, 0xd7, 0x07, 0x32, //0x00005520 .quad 3605087062808385830
	0xff, 0x64, 0x23, 0xaf, 0x44, 0x02, 0xef, 0xd1, //0x00005528 .quad -3319431906329402113
	0xb8, 0x07, 0xe8, 0x49, 0xbd, 0xe6, 0x44, 0x7f, //0x00005530 .quad 9170708441896323000
	0x1f, 0x1f, 0x76, 0xed, 0x6a, 0x61, 0x35, 0x83, //0x00005538 .quad -8992173969096958177
	0xa6, 0x09, 0x62, 0x9c, 0x6c, 0x20, 0x16, 0x5f, //0x00005540 .quad 6851699533943015846
	0xe7, 0xa6, 0xd3, 0xa8, 0xc5, 0xb9, 0x02, 0xa4, //0x00005548 .quad -6628531442943809817
	0x0f, 0x8c, 0x7a, 0xc3, 0x87, 0xa8, 0xdb, 0x36, //0x00005550 .quad 3952938399001381903
	0xa1, 0x90, 0x08, 0x13, 0x37, 0x68, 0x03, 0xcd, //0x00005558 .quad -3673978285252374367
	0x89, 0x97, 0x2c, 0xda, 0x54, 0x49, 0x49, 0xc2, //0x00005560 .quad -4446942528265218167
	0x64, 0x5a, 0xe5, 0x6b, 0x22, 0x21, 0x22, 0x80, //0x00005568 .quad -9213765455923815836
	0x6c, 0xbd, 0xb7, 0x10, 0xaa, 0x9b, 0xdb, 0xf2, //0x00005570 .quad -946992141904134804
	0xfd, 0xb0, 0xde, 0x06, 0x6b, 0xa9, 0x2a, 0xa0, //0x00005578 .quad -6905520801477381891
	0xc7, 0xac, 0xe5, 0x94, 0x94, 0x82, 0x92, 0x6f, //0x00005580 .quad 8039631859474607303
	0x3d, 0x5d, 0x96, 0xc8, 0xc5, 0x53, 0x35, 0xc8, //0x00005588 .quad -4020214983419339459
	0xf9, 0x17, 0x1f, 0xba, 0x39, 0x23, 0x77, 0xcb, //0x00005590 .quad -3785518230938904583
	0x8c, 0xf4, 0xbb, 0x3a, 0xb7, 0xa8, 0x42, 0xfa, //0x00005598 .quad -413582710846786420
	0xfb, 0x6e, 0x53, 0x14, 0x04, 0x76, 0x2a, 0xff, //0x000055a0 .quad -60105885123121413
	0xd7, 0x78, 0xb5, 0x84, 0x72, 0xa9, 0x69, 0x9c, //0x000055a8 .quad -7176018221920323369
	0xba, 0x4a, 0x68, 0x19, 0x85, 0x13, 0xf5, 0xfe, //0x000055b0 .quad -75132356403901766
	0x0d, 0xd7, 0xe2, 0x25, 0xcf, 0x13, 0x84, 0xc3, //0x000055b8 .quad -4358336758973016307
	0x69, 0x5d, 0xc2, 0x5f, 0x66, 0x58, 0xb2, 0x7e, //0x000055c0 .quad 9129456591349898601
	0xd1, 0x8c, 0x5b, 0xef, 0xc2, 0x18, 0x65, 0xf4, //0x000055c8 .quad -836234930288882479
	0x61, 0x7a, 0xd9, 0xfb, 0x3f, 0x77, 0x2f, 0xef, //0x000055d0 .quad -1211618658047395231
	0x02, 0x38, 0x99, 0xd5, 0x79, 0x2f, 0xbf, 0x98, //0x000055d8 .quad -7440175859071633406
	0xfa, 0xd8, 0xcf, 0xfa, 0x0f, 0x55, 0xfb, 0xaa, //0x000055e0 .quad -6126209340986631942
	0x03, 0x86, 0xff, 0x4a, 0x58, 0xfb, 0xee, 0xbe, //0x000055e8 .quad -4688533805412153853
	0x38, 0xcf, 0x83, 0xf9, 0x53, 0x2a, 0xba, 0x95, //0x000055f0 .quad -7657761676233289928
	0x84, 0x67, 0xbf, 0x5d, 0x2e, 0xba, 0xaa, 0xee, //0x000055f8 .quad -1248981238337804412
	0x83, 0x61, 0xf2, 0x7b, 0x74, 0x5a, 0x94, 0xdd, //0x00005600 .quad -2480258038432112253
	0xb2, 0xa0, 0x97, 0xfa, 0x5c, 0xb4, 0x2a, 0x95, //0x00005608 .quad -7698142301602209614
	0xe4, 0xf9, 0xee, 0x9a, 0x11, 0x71, 0xf9, 0x94, //0x00005610 .quad -7712008566467528220
	0xdf, 0x88, 0x3d, 0x39, 0x74, 0x61, 0x75, 0xba, //0x00005618 .quad -5010991858575374113
	0x5d, 0xb8, 0xaa, 0x01, 0x56, 0xcd, 0x37, 0x7a, //0x00005620 .quad 8806733365625141341
	0x17, 0xeb, 0x8c, 0x47, 0xd1, 0xb9, 0x12, 0xe9, //0x00005628 .quad -1652053804791829737
	0x3a, 0xb3, 0x0a, 0xc1, 0x55, 0xe0, 0x62, 0xac, //0x00005630 .quad -6025006692552756422
	0xee, 0x12, 0xb8, 0xcc, 0x22, 0xb4, 0xab, 0x91, //0x00005638 .quad -7950062655635975442
	0x09, 0x60, 0x4d, 0x31, 0x6b, 0x98, 0x7b, 0x57, //0x00005640 .quad 6303799689591218185
	0xaa, 0x17, 0xe6, 0x7f, 0x2b, 0xa1, 0x16, 0xb6, //0x00005648 .quad -5325892301117581398
	0x0b, 0xb8, 0xa0, 0xfd, 0x85, 0x7e, 0x5a, 0xed, //0x00005650 .quad -1343622424865753077
	0x94, 0x9d, 0xdf, 0x5f, 0x76, 0x49, 0x9c, 0xe3, //0x00005658 .quad -2045679357969588844
	0x07, 0x73, 0x84, 0xbe, 0x13, 0x8f, 0x58, 0x14, //0x00005660 .quad 1466078993672598279
	0x7d, 0xc2, 0xeb, 0xfb, 0xe9, 0xad, 0x41, 0x8e, //0x00005668 .quad -8196078626372074883
	0xc8, 0x8f, 0x25, 0xae, 0xd8, 0xb2, 0x6e, 0x59, //0x00005670 .quad 6444284760518135752
	0x1c, 0xb3, 0xe6, 0x7a, 0x64, 0x19, 0xd2, 0xb1, //0x00005678 .quad -5633412264537705700
	0xbb, 0xf3, 0xae, 0xd9, 0x8e, 0x5f, 0xca, 0x6f, //0x00005680 .quad 8055355950647669691
	0xe3, 0x5f, 0xa0, 0x99, 0xbd, 0x9f, 0x46, 0xde, //0x00005688 .quad -2430079312244744221
	0x54, 0x58, 0x0d, 0x48, 0xb9, 0x7b, 0xde, 0x25, //0x00005690 .quad 2728754459941099604
	0xee, 0x3b, 0x04, 0x80, 0xd6, 0x23, 0xec, 0x8a, //0x00005698 .quad -8436328597794046994
	0x6a, 0xae, 0x10, 0x9a, 0xa7, 0x1a, 0x56, 0xaf, //0x000056a0 .quad -5812428961928401302
	0xe9, 0x4a, 0x05, 0x20, 0xcc, 0x2c, 0xa7, 0xad, //0x000056a8 .quad -5933724728815170839
	0x04, 0xda, 0x94, 0x80, 0x51, 0xa1, 0x2b, 0x1b, //0x000056b0 .quad 1957835834444274180
	0xa4, 0x9d, 0x06, 0x28, 0xff, 0xf7, 0x10, 0xd9, //0x000056b8 .quad -2805469892591575644
	0x42, 0x08, 0x5d, 0xf0, 0xd2, 0x44, 0xfb, 0x90, //0x000056c0 .quad -7999724640327104446
	0x86, 0x22, 0x04, 0x79, 0xff, 0x9a, 0xaa, 0x87, //0x000056c8 .quad -8670947710510816634
	0x53, 0x4a, 0x74, 0xac, 0x07, 0x16, 0x3a, 0x35, //0x000056d0 .quad 3835402254873283155
	0x28, 0x2b, 0x45, 0x57, 0xbf, 0x41, 0x95, 0xa9, //0x000056d8 .quad -6226998619711132888
	0xe8, 0x5c, 0x91, 0x97, 0x89, 0x9b, 0x88, 0x42, //0x000056e0 .quad 4794252818591603944
	0xf2, 0x75, 0x16, 0x2d, 0x2f, 0x92, 0xfa, 0xd3, //0x000056e8 .quad -3172062256211528206
	0x11, 0xda, 0xba, 0xfe, 0x35, 0x61, 0x95, 0x69, //0x000056f0 .quad 7608094030047140369
	0xb7, 0x09, 0x2e, 0x7c, 0x5d, 0x9b, 0x7c, 0x84, //0x000056f8 .quad -8900067937773286985
	0x95, 0x90, 0x69, 0x7e, 0x83, 0xb9, 0xfa, 0x43, //0x00005700 .quad 4898431519131537557
	0x25, 0x8c, 0x39, 0xdb, 0x34, 0xc2, 0x9b, 0xa5, //0x00005708 .quad -6513398903789220827
	0xbb, 0xf4, 0x03, 0x5e, 0xe4, 0x67, 0xf9, 0x94, //0x00005710 .quad -7712018656367741765
	0x2e, 0xef, 0x07, 0x12, 0xc2, 0xb2, 0x02, 0xcf, //0x00005718 .quad -3530062611309138130
	0xf5, 0x78, 0xc2, 0xba, 0xee, 0xe0, 0x1b, 0x1d, //0x00005720 .quad 2097517367411243253
	0x7d, 0xf5, 0x44, 0x4b, 0xb9, 0xaf, 0x61, 0x81, //0x00005728 .quad -9123818159709293187
	0x32, 0x17, 0x73, 0x69, 0x2a, 0xd9, 0x62, 0x64, //0x00005730 .quad 7233582727691441970
	0xdc, 0x32, 0x16, 0x9e, 0xa7, 0x1b, 0xba, 0xa1, //0x00005738 .quad -6793086681209228580
	0xfe, 0xdc, 0xcf, 0x03, 0x75, 0x8f, 0x7b, 0x7d, //0x00005740 .quad 9041978409614302462
	0x93, 0xbf, 0x9b, 0x85, 0x91, 0xa2, 0x28, 0xca, //0x00005748 .quad -3879672333084147821
	0x3e, 0xd4, 0xc3, 0x44, 0x52, 0x73, 0xda, 0x5c, //0x00005750 .quad 6690786993590490174
	0x78, 0xaf, 0x02, 0xe7, 0x35, 0xcb, 0xb2, 0xfc, //0x00005758 .quad -237904397927796872
	0xa7, 0x64, 0xfa, 0x6a, 0x13, 0x88, 0x08, 0x3a, //0x00005760 .quad 4181741870994056359
	0xab, 0xad, 0x61, 0xb0, 0x01, 0xbf, 0xef, 0x9d, //0x00005768 .quad -7066219276345954901
	0xd0, 0xfd, 0xb8, 0x45, 0x18, 0xaa, 0x8a, 0x08, //0x00005770 .quad 615491320315182544
	0x16, 0x19, 0x7a, 0x1c, 0xc2, 0xae, 0x6b, 0xc5, //0x00005778 .quad -4221088077005055722
	0x45, 0x3d, 0x27, 0x57, 0x9e, 0x54, 0xad, 0x8a, //0x00005780 .quad -8454007886460797627
	0x5b, 0x9f, 0x98, 0xa3, 0x72, 0x9a, 0xc6, 0xf6, //0x00005788 .quad -664674077828931749
	0x4b, 0x86, 0x78, 0xf6, 0xe2, 0x54, 0xac, 0x36, //0x00005790 .quad 3939617107816777291
	0x99, 0x63, 0x3f, 0xa6, 0x87, 0x20, 0x3c, 0x9a, //0x00005798 .quad -7332950326284164199
	0xdd, 0xa7, 0x16, 0xb4, 0x1b, 0x6a, 0x57, 0x84, //0x000057a0 .quad -8910536670511192099
	0x7f, 0x3c, 0xcf, 0x8f, 0xa9, 0x28, 0xcb, 0xc0, //0x000057a8 .quad -4554501889427817345
	0xd5, 0x51, 0x1c, 0xa1, 0xa2, 0x44, 0x6d, 0x65, //0x000057b0 .quad 7308573235570561493
	0x9f, 0x0b, 0xc3, 0xf3, 0xd3, 0xf2, 0xfd, 0xf0, //0x000057b8 .quad -1081441343357383777
	0x25, 0xb3, 0xb1, 0xa4, 0xe5, 0x4a, 0x64, 0x9f, //0x000057c0 .quad -6961356773836868827
	0x43, 0xe7, 0x59, 0x78, 0xc4, 0xb7, 0x9e, 0x96, //0x000057c8 .quad -7593429867239446717
	0xee, 0x1f, 0xde, 0x0d, 0x9f, 0x5d, 0x3d, 0x87, //0x000057d0 .quad -8701695967296086034
	0x14, 0x61, 0x70, 0x96, 0xb5, 0x65, 0x46, 0xbc, //0x000057d8 .quad -4880101315621920492
	0xea, 0xa7, 0x55, 0xd1, 0x06, 0xb5, 0x0c, 0xa9, //0x000057e0 .quad -6265433940692719638
	0x59, 0x79, 0x0c, 0xfc, 0x22, 0xff, 0x57, 0xeb, //0x000057e8 .quad -1488440626100012711
	0xf2, 0x88, 0xd5, 0x42, 0x24, 0xf1, 0xa7, 0x09, //0x000057f0 .quad 695789805494438130
	0xd8, 0xcb, 0x87, 0xdd, 0x75, 0xff, 0x16, 0x93, //0x000057f8 .quad -7847804418953589800
	0x2f, 0xeb, 0x8a, 0x53, 0x6d, 0xed, 0x11, 0x0c, //0x00005800 .quad 869737256868047663
	0xce, 0xbe, 0xe9, 0x54, 0x53, 0xbf, 0xdc, 0xb7, //0x00005808 .quad -5198069505264599346
	0xfa, 0xa5, 0x6d, 0xa8, 0xc8, 0x68, 0x16, 0x8f, //0x00005810 .quad -8136200465769716230
	0x81, 0x2e, 0x24, 0x2a, 0x28, 0xef, 0xd3, 0xe5, //0x00005818 .quad -1885900863153361279
	0xbc, 0x87, 0x44, 0x69, 0x7d, 0x01, 0x6e, 0xf9, //0x00005820 .quad -473439272678684740
	0x10, 0x9d, 0x56, 0x1a, 0x79, 0x75, 0xa4, 0x8f, //0x00005828 .quad -8096217067111932656
	0xac, 0xa9, 0x95, 0xc3, 0xdc, 0x81, 0xc9, 0x37, //0x00005830 .quad 4019886927579031980
	0x55, 0x44, 0xec, 0x60, 0xd7, 0x92, 0x8d, 0xb3, //0x00005838 .quad -5508585315462527915
	0x17, 0x14, 0x7b, 0xf4, 0x53, 0xe2, 0xbb, 0x85, //0x00005840 .quad -8810199395808373737
	0x6a, 0x55, 0x27, 0x39, 0x8d, 0xf7, 0x70, 0xe0, //0x00005848 .quad -2274045625900771990
	0x8e, 0xec, 0xcc, 0x78, 0x74, 0x6d, 0x95, 0x93, //0x00005850 .quad -7812217631593927538
	0x62, 0x95, 0xb8, 0x43, 0xb8, 0x9a, 0x46, 0x8c, //0x00005858 .quad -8338807543829064350
	0xb2, 0x27, 0x00, 0x97, 0xd1, 0xc8, 0x7a, 0x38, //0x00005860 .quad 4069786015789754290
	0xbb, 0xba, 0xa6, 0x54, 0x66, 0x41, 0x58, 0xaf, //0x00005868 .quad -5811823411358942533
	0x9e, 0x31, 0xc0, 0xfc, 0x05, 0x7b, 0x99, 0x06, //0x00005870 .quad 475546501309804958
	0x6a, 0x69, 0xd0, 0xe9, 0xbf, 0x51, 0x2e, 0xdb, //0x00005878 .quad -2653093245771290262
	0x03, 0x1f, 0xf8, 0xbd, 0xe3, 0xec, 0x1f, 0x44, //0x00005880 .quad 4908902581746016003
	0xe2, 0x41, 0x22, 0xf2, 0x17, 0xf3, 0xfc, 0x88, //0x00005888 .quad -8575712306248138270
	0xc3, 0x26, 0x76, 0xad, 0x1c, 0xe8, 0x27, 0xd5, //0x00005890 .quad -3087243809672255805
	0x5a, 0xd2, 0xaa, 0xee, 0xdd, 0x2f, 0x3c, 0xab, //0x00005898 .quad -6107954364382784934
	0x74, 0xb0, 0xd3, 0xd8, 0x23, 0xe2, 0x71, 0x8a, //0x000058a0 .quad -8470740780517707660
	0xf1, 0x86, 0x55, 0x6a, 0xd5, 0x3b, 0x0b, 0xd6, //0x000058a8 .quad -3023256937051093263
	0x49, 0x4e, 0x84, 0x67, 0x56, 0x2d, 0x87, 0xf6, //0x000058b0 .quad -682526969396179383
	0x56, 0x74, 0x75, 0x62, 0x65, 0x05, 0xc7, 0x85, //0x000058b8 .quad -8807064613298015146
	0xdb, 0x61, 0x65, 0x01, 0xac, 0xf8, 0x28, 0xb4, //0x000058c0 .quad -5464844730172612133
	0x6c, 0xd1, 0x12, 0xbb, 0xbe, 0xc6, 0x38, 0xa7, //0x000058c8 .quad -6397144748195131028
	0x52, 0xba, 0xbe, 0x01, 0xd7, 0x36, 0x33, 0xe1, //0x000058d0 .quad -2219369894288377262
	0xc7, 0x85, 0xd7, 0x69, 0x6e, 0xf8, 0x06, 0xd1, //0x000058d8 .quad -3384744916816525881
	0x73, 0x34, 0x17, 0x61, 0x46, 0x02, 0xc0, 0xec, //0x000058e0 .quad -1387106183930235789
	0x9c, 0xb3, 0x26, 0x02, 0x45, 0x5b, 0xa4, 0x82, //0x000058e8 .quad -9032994600651410532
	0x90, 0x01, 0x5d, 0xf9, 0xd7, 0x02, 0xf0, 0x27, //0x000058f0 .quad 2877803288514593168
	0x84, 0x60, 0xb0, 0x42, 0x16, 0x72, 0x4d, 0xa3, //0x000058f8 .quad -6679557232386875260
	0xf4, 0x41, 0xb4, 0xf7, 0x8d, 0x03, 0xec, 0x31, //0x00005900 .quad 3597254110643241460
	0xa5, 0x78, 0x5c, 0xd3, 0x9b, 0xce, 0x20, 0xcc, //0x00005908 .quad -3737760522056206171
	0x71, 0x52, 0xa1, 0x75, 0x71, 0x04, 0x67, 0x7e, //0x00005910 .quad 9108253656731439729
	0xce, 0x96, 0x33, 0xc8, 0x42, 0x02, 0x29, 0xff, //0x00005918 .quad -60514634142869810
	0x86, 0xd3, 0x84, 0xe9, 0xc6, 0x62, 0x00, 0x0f, //0x00005920 .quad 1080972517029761926
	0x41, 0x3e, 0x20, 0xbd, 0x69, 0xa1, 0x79, 0x9f, //0x00005928 .quad -6955350673980375487
	0x68, 0x08, 0xe6, 0xa3, 0x78, 0x7b, 0xc0, 0x52, //0x00005930 .quad 5962901664714590312
	0xd1, 0x4d, 0x68, 0x2c, 0xc4, 0x09, 0x58, 0xc7, //0x00005938 .quad -4082502324048081455
	0x82, 0x8a, 0xdf, 0xcc, 0x56, 0x9a, 0x70, 0xa7, //0x00005940 .quad -6381430974388925822
	0x45, 0x61, 0x82, 0x37, 0x35, 0x0c, 0x2e, 0xf9, //0x00005948 .quad -491441886632713915
	0x91, 0xb6, 0x0b, 0x40, 0x76, 0x60, 0xa6, 0x88, //0x00005950 .quad -8600080377420466543
	0xcb, 0x7c, 0xb1, 0x42, 0xa1, 0xc7, 0xbc, 0x9b, //0x00005958 .quad -7224680206786528053
	0x35, 0xa4, 0x0e, 0xd0, 0x93, 0xf8, 0xcf, 0x6a, //0x00005960 .quad 7696643601933968437
	0xfe, 0xdb, 0x5d, 0x93, 0x89, 0xf9, 0xab, 0xc2, //0x00005968 .quad -4419164240055772162
	0x43, 0x4d, 0x12, 0xc4, 0xb8, 0xf6, 0x83, 0x05, //0x00005970 .quad 397432465562684739
	0xfe, 0x52, 0x35, 0xf8, 0xeb, 0xf7, 0x56, 0xf3, //0x00005978 .quad -912269281642327298
	0x4a, 0x70, 0x8b, 0x7a, 0x33, 0x7a, 0x72, 0xc3, //0x00005980 .quad -4363290727450709942
	0xde, 0x53, 0x21, 0x7b, 0xf3, 0x5a, 0x16, 0x98, //0x00005988 .quad -7487697328667536418
	0x5c, 0x4c, 0x2e, 0x59, 0xc0, 0x18, 0x4f, 0x74, //0x00005990 .quad 8380944645968776284
	0xd6, 0xa8, 0xe9, 0x59, 0xb0, 0xf1, 0x1b, 0xbe, //0x00005998 .quad -4747935642407032618
	0x73, 0xdf, 0x79, 0x6f, 0xf0, 0xde, 0x62, 0x11, //0x000059a0 .quad 1252808770606194547
	0x0c, 0x13, 0x64, 0x70, 0x1c, 0xee, 0xa2, 0xed, //0x000059a8 .quad -1323233534581402868
	0xa8, 0x2b, 0xac, 0x45, 0x56, 0xcb, 0xdd, 0x8a, //0x000059b0 .quad -8440366555225904216
	0xe7, 0x8b, 0x3e, 0xc6, 0xd1, 0xd4, 0x85, 0x94, //0x000059b8 .quad -7744549986754458649
	0x92, 0x36, 0x17, 0xd7, 0x2b, 0x3e, 0x95, 0x6d, //0x000059c0 .quad 7896285879677171346
	0xe1, 0x2e, 0xce, 0x37, 0x06, 0x4a, 0xa7, 0xb9, //0x000059c8 .quad -5069001465015685407
	0x37, 0x04, 0xdd, 0xcc, 0xb6, 0x8d, 0xfa, 0xc8, //0x000059d0 .quad -3964700705685699529
	0x99, 0xba, 0xc1, 0xc5, 0x87, 0x1c, 0x11, 0xe8, //0x000059d8 .quad -1724565812842218855
	0xa2, 0x22, 0x0a, 0x40, 0x92, 0x98, 0x9c, 0x1d, //0x000059e0 .quad 2133748077373825698
	0xa0, 0x14, 0x99, 0xdb, 0xd4, 0xb1, 0x0a, 0x91, //0x000059e8 .quad -7995382660667468640
	0x4b, 0xab, 0x0c, 0xd0, 0xb6, 0xbe, 0x03, 0x25, //0x000059f0 .quad 2667185096717282123
	0xc8, 0x59, 0x7f, 0x12, 0x4a, 0x5e, 0x4d, 0xb5, //0x000059f8 .quad -5382542307406947896
	0x1d, 0xd6, 0x0f, 0x84, 0x64, 0xae, 0x44, 0x2e, //0x00005a00 .quad 3333981370896602653
	0x3a, 0x30, 0x1f, 0x97, 0xdc, 0xb5, 0xa0, 0xe2, //0x00005a08 .quad -2116491865831296966
	0xd2, 0xe5, 0x89, 0xd2, 0xfe, 0xec, 0xea, 0x5c, //0x00005a10 .quad 6695424375237764562
	0x24, 0x7e, 0x73, 0xde, 0xa9, 0x71, 0xa4, 0x8d, //0x00005a18 .quad -8240336443785642460
	0x47, 0x5f, 0x2c, 0x87, 0x3e, 0xa8, 0x25, 0x74, //0x00005a20 .quad 8369280469047205703
	0xad, 0x5d, 0x10, 0x56, 0x14, 0x8e, 0x0d, 0xb1, //0x00005a28 .quad -5688734536304665171
	0x19, 0x77, 0xf7, 0x28, 0x4e, 0x12, 0x2f, 0xd1, //0x00005a30 .quad -3373457468973156583
	0x18, 0x75, 0x94, 0x6b, 0x99, 0xf1, 0x50, 0xdd, //0x00005a38 .quad -2499232151953443560
	0x6f, 0xaa, 0x9a, 0xd9, 0x70, 0x6b, 0xbd, 0x82, //0x00005a40 .quad -9025939945749304721
	0x2f, 0xc9, 0x3c, 0xe3, 0xff, 0x96, 0x52, 0x8a, //0x00005a48 .quad -8479549122611984081
	0x0b, 0x55, 0x01, 0x10, 0x4d, 0xc6, 0x6c, 0x63, //0x00005a50 .quad 7164319141522920715
	0x7b, 0xfb, 0x0b, 0xdc, 0xbf, 0x3c, 0xe7, 0xac, //0x00005a58 .quad -5987750384837592197
	0x4e, 0xaa, 0x01, 0x54, 0xe0, 0xf7, 0x47, 0x3c, //0x00005a60 .quad 4343712908476262990
	0x5a, 0xfa, 0x0e, 0xd3, 0xef, 0x0b, 0x21, 0xd8, //0x00005a68 .quad -2873001962619602342
	0x71, 0x0a, 0x81, 0x34, 0xec, 0xfa, 0xac, 0x65, //0x00005a70 .quad 7326506586225052273
	0x78, 0x5c, 0xe9, 0xe3, 0x75, 0xa7, 0x14, 0x87, //0x00005a78 .quad -8713155254278333320
	0x0d, 0x4d, 0xa1, 0x41, 0xa7, 0x39, 0x18, 0x7f, //0x00005a80 .quad 9158133232781315341
	0x96, 0xb3, 0xe3, 0x5c, 0x53, 0xd1, 0xd9, 0xa8, //0x00005a88 .quad -6279758049420528746
	0x50, 0xa0, 0x09, 0x12, 0x11, 0x48, 0xde, 0x1e, //0x00005a90 .quad 2224294504121868368
	0x7c, 0xa0, 0x1c, 0x34, 0xa8, 0x45, 0x10, 0xd3, //0x00005a98 .quad -3238011543348273028
	0x32, 0x04, 0x46, 0xab, 0x0a, 0xed, 0x4a, 0x93, //0x00005aa0 .quad -7833187971778608078
	0x4d, 0xe4, 0x91, 0x20, 0x89, 0x2b, 0xea, 0x83, //0x00005aa8 .quad -8941286242233752499
	0x3f, 0x85, 0x17, 0x56, 0x4d, 0xa8, 0x1d, 0xf8, //0x00005ab0 .quad -568112927868484289
	0x60, 0x5d, 0xb6, 0x68, 0x6b, 0xb6, 0xe4, 0xa4, //0x00005ab8 .quad -6564921784364802720
	0x8e, 0x66, 0x9d, 0xab, 0x60, 0x12, 0x25, 0x36, //0x00005ac0 .quad 3901544858591782542
	0xb9, 0xf4, 0xe3, 0x42, 0x06, 0xe4, 0x1d, 0xce, //0x00005ac8 .quad -3594466212028615495
	0x19, 0x60, 0x42, 0x6b, 0x7c, 0x2b, 0xd7, 0xc1, //0x00005ad0 .quad -4479063491021217767
	0xf3, 0x78, 0xce, 0xe9, 0x83, 0xae, 0xd2, 0x80, //0x00005ad8 .quad -9164070410158966541
	0x1f, 0xf8, 0x12, 0x86, 0x5b, 0xf6, 0x4c, 0xb2, //0x00005ae0 .quad -5598829363776522209
	0x30, 0x17, 0x42, 0xe4, 0x24, 0x5a, 0x07, 0xa1, //0x00005ae8 .quad -6843401994271320272
	0x27, 0xb6, 0x97, 0x67, 0xf2, 0x33, 0xe0, 0xde, //0x00005af0 .quad -2386850686293264857
	0xfc, 0x9c, 0x52, 0x1d, 0xae, 0x30, 0x49, 0xc9, //0x00005af8 .quad -3942566474411762436
	0xb1, 0xa3, 0x7d, 0x01, 0xef, 0x40, 0x98, 0x16, //0x00005b00 .quad 1628122660560806833
	0x3c, 0x44, 0xa7, 0xa4, 0xd9, 0x7c, 0x9b, 0xfb, //0x00005b08 .quad -316522074587315140
	0x4e, 0x86, 0xee, 0x60, 0x95, 0x28, 0x1f, 0x8e, //0x00005b10 .quad -8205795374004271538
	0xa5, 0x8a, 0xe8, 0x06, 0x08, 0x2e, 0x41, 0x9d, //0x00005b18 .quad -7115355324258153819
	0xe2, 0x27, 0x2a, 0xb9, 0xba, 0xf2, 0xa6, 0xf1, //0x00005b20 .quad -1033872180650563614
	0x4e, 0xad, 0xa2, 0x08, 0x8a, 0x79, 0x91, 0xc4, //0x00005b28 .quad -4282508136895304370
	0xdb, 0xb1, 0x74, 0x67, 0x69, 0xaf, 0x10, 0xae, //0x00005b30 .quad -5904026244240592421
	0xa2, 0x58, 0xcb, 0x8a, 0xec, 0xd7, 0xb5, 0xf5, //0x00005b38 .quad -741449152691742558
	0x29, 0xef, 0xa8, 0xe0, 0xa1, 0x6d, 0xca, 0xac, //0x00005b40 .quad -5995859411864064215
	0x65, 0x17, 0xbf, 0xd6, 0xf3, 0xa6, 0x91, 0x99, //0x00005b48 .quad -7380934748073420955
	0xf3, 0x2a, 0xd3, 0x58, 0x0a, 0x09, 0xfd, 0x17, //0x00005b50 .quad 1728547772024695539
	0x3f, 0xdd, 0x6e, 0xcc, 0xb0, 0x10, 0xf6, 0xbf, //0x00005b58 .quad -4614482416664388289
	0xb0, 0xf5, 0x07, 0xef, 0x4c, 0x4b, 0xfc, 0xdd, //0x00005b60 .quad -2451001303396518480
	0x8e, 0x94, 0x8a, 0xff, 0xdc, 0x94, 0xf3, 0xef, //0x00005b68 .quad -1156417002403097458
	0x8e, 0xf9, 0x64, 0x15, 0x10, 0xaf, 0xbd, 0x4a, //0x00005b70 .quad 5385653213018257806
	0xd9, 0x9c, 0xb6, 0x1f, 0x0a, 0x3d, 0xf8, 0x95, //0x00005b78 .quad -7640289654143017767
	0xf1, 0x37, 0xbe, 0x1a, 0xd4, 0x1a, 0x6d, 0x9d, //0x00005b80 .quad -7102991539009341455
	0x0f, 0x44, 0xa4, 0xa7, 0x4c, 0x4c, 0x76, 0xbb, //0x00005b88 .quad -4938676049251384305
	0xed, 0xc5, 0x6d, 0x21, 0x89, 0x61, 0xc8, 0x84, //0x00005b90 .quad -8878739423761676819
	0x13, 0x55, 0x8d, 0xd1, 0x5f, 0xdf, 0x53, 0xea, //0x00005b98 .quad -1561659043136842477
	0xb4, 0x9b, 0xe4, 0xb4, 0xf5, 0x3c, 0xfd, 0x32, //0x00005ba0 .quad 3674159897003727796
	0x2c, 0x55, 0xf8, 0xe2, 0x9b, 0x6b, 0x74, 0x92, //0x00005ba8 .quad -7893565929601608404
	0xa1, 0xc2, 0x1d, 0x22, 0x33, 0x8c, 0xbc, 0x3f, //0x00005bb0 .quad 4592699871254659745
	0x77, 0x6a, 0xb6, 0xdb, 0x82, 0x86, 0x11, 0xb7, //0x00005bb8 .quad -5255271393574622601
	0x4a, 0x33, 0xa5, 0xea, 0x3f, 0xaf, 0xab, 0x0f, //0x00005bc0 .quad 1129188820640936778
	0x15, 0x05, 0xa4, 0x92, 0x23, 0xe8, 0xd5, 0xe4, //0x00005bc8 .quad -1957403223540890347
	0x0e, 0x40, 0xa7, 0xf2, 0x87, 0x4d, 0xcb, 0x29, //0x00005bd0 .quad 3011586022114279438
	0x2d, 0x83, 0xa6, 0x3b, 0x16, 0xb1, 0x05, 0x8f, //0x00005bd8 .quad -8140906042354138323
	0x12, 0x10, 0x51, 0xef, 0xe9, 0x20, 0x3e, 0x74, //0x00005be0 .quad 8376168546070237202
	0xf8, 0x23, 0x90, 0xca, 0x5b, 0x1d, 0xc7, 0xb2, //0x00005be8 .quad -5564446534515285000
	0x16, 0x54, 0x25, 0x6b, 0x24, 0xa9, 0x4d, 0x91, //0x00005bf0 .quad -7976533391121755114
	0xf6, 0x2c, 0x34, 0xbd, 0xb2, 0xe4, 0x78, 0xdf, //0x00005bf8 .quad -2343872149716718346
	0x8e, 0x54, 0xf7, 0xc2, 0xb6, 0x89, 0xd0, 0x1a, //0x00005c00 .quad 1932195658189984910
	0x1a, 0x9c, 0x40, 0xb6, 0xef, 0x8e, 0xab, 0x8b, //0x00005c08 .quad -8382449121214030822
	0xb1, 0x29, 0xb5, 0x73, 0x24, 0xac, 0x84, 0xa1, //0x00005c10 .quad -6808127464117294671
	0x20, 0xc3, 0xd0, 0xa3, 0xab, 0x72, 0x96, 0xae, //0x00005c18 .quad -5866375383090150624
	0x1e, 0x74, 0xa2, 0x90, 0x2d, 0xd7, 0xe5, 0xc9, //0x00005c20 .quad -3898473311719230434
	0xe8, 0xf3, 0xc4, 0x8c, 0x56, 0x0f, 0x3c, 0xda, //0x00005c28 .quad -2721283210435300376
	0x92, 0x88, 0x65, 0x7a, 0x7c, 0xa6, 0x2f, 0x7e, //0x00005c30 .quad 9092669226243950738
	0x71, 0x18, 0xfb, 0x17, 0x96, 0x89, 0x65, 0x88, //0x00005c38 .quad -8618331034163144591
	0xb7, 0xea, 0xfe, 0x98, 0x1b, 0x90, 0xbb, 0xdd, //0x00005c40 .quad -2469221522477225289
	0x8d, 0xde, 0xf9, 0x9d, 0xfb, 0xeb, 0x7e, 0xaa, //0x00005c48 .quad -6161227774276542835
	0x65, 0xa5, 0x3e, 0x7f, 0x22, 0x74, 0x2a, 0x55, //0x00005c50 .quad 6136845133758244197
	0x31, 0x56, 0x78, 0x85, 0xfa, 0xa6, 0x1e, 0xd5, //0x00005c58 .quad -3089848699418290639
	0x5f, 0x27, 0x87, 0x8f, 0x95, 0x88, 0x3a, 0xd5, //0x00005c60 .quad -3082000819042179233
	0xde, 0x35, 0x6b, 0x93, 0x5c, 0x28, 0x33, 0x85, //0x00005c68 .quad -8848684464777513506
	0x37, 0xf1, 0x68, 0xf3, 0xba, 0x2a, 0x89, 0x8a, //0x00005c70 .quad -8464187042230111945
	0x56, 0x03, 0x46, 0xb8, 0x73, 0xf2, 0x7f, 0xa6, //0x00005c78 .quad -6449169562544503978
	0x85, 0x2d, 0x43, 0xb0, 0x69, 0x75, 0x2b, 0x2d, //0x00005c80 .quad 3254824252494523781
	0x2c, 0x84, 0x57, 0xa6, 0x10, 0xef, 0x1f, 0xd0, //0x00005c88 .quad -3449775934753242068
	0x73, 0xfc, 0x29, 0x0e, 0x62, 0x29, 0x3b, 0x9c, //0x00005c90 .quad -7189106879045698445
	0x9b, 0xb2, 0xf6, 0x67, 0x6a, 0xf5, 0x13, 0x82, //0x00005c98 .quad -9073638986861858149
	0x8f, 0x7b, 0xb4, 0x91, 0xba, 0xf3, 0x49, 0x83, //0x00005ca0 .quad -8986383598807123057
	0x42, 0x5f, 0xf4, 0x01, 0xc5, 0xf2, 0x98, 0xa2, //0x00005ca8 .quad -6730362715149934782
	0x73, 0x9a, 0x21, 0x36, 0xa9, 0x70, 0x1c, 0x24, //0x00005cb0 .quad 2602078556773259891
	0x13, 0x77, 0x71, 0x42, 0x76, 0x2f, 0x3f, 0xcb, //0x00005cb8 .quad -3801267375510030573
	0x10, 0x01, 0xaa, 0x83, 0xd3, 0x8c, 0x23, 0xed, //0x00005cc0 .quad -1359087822460813040
	0xd7, 0xd4, 0x0d, 0xd3, 0x53, 0xfb, 0x0e, 0xfe, //0x00005cc8 .quad -139898200960150313
	0xaa, 0x40, 0x4a, 0x32, 0x04, 0x38, 0x36, 0xf4, //0x00005cd0 .quad -849429889038008150
	0x06, 0xa5, 0xe8, 0x63, 0x14, 0x5d, 0xc9, 0x9e, //0x00005cd8 .quad -7004965403241175802
	0xd5, 0xd0, 0xdc, 0x3e, 0x05, 0xc6, 0x43, 0xb1, //0x00005ce0 .quad -5673473379724898091
	0x48, 0xce, 0xe2, 0x7c, 0x59, 0xb4, 0x7b, 0xc6, //0x00005ce8 .quad -4144520735624081848
	0x0a, 0x05, 0x94, 0x8e, 0x86, 0xb7, 0x94, 0xdd, //0x00005cf0 .quad -2480155706228734710
	0xda, 0x81, 0x1b, 0xdc, 0x6f, 0xa1, 0x1a, 0xf8, //0x00005cf8 .quad -568964901102714406
	0x26, 0x83, 0x1c, 0x19, 0xb4, 0xf2, 0x7c, 0xca, //0x00005d00 .quad -3855940325606653146
	0x28, 0x31, 0x91, 0xe9, 0xe5, 0xa4, 0x10, 0x9b, //0x00005d08 .quad -7273132090830278360
	0xf0, 0xa3, 0x63, 0x1f, 0x61, 0x2f, 0x1c, 0xfd, //0x00005d10 .quad -208239388580928528
	0x72, 0x7d, 0xf5, 0x63, 0x1f, 0xce, 0xd4, 0xc1, //0x00005d18 .quad -4479729095110460046
	0xec, 0x8c, 0x3c, 0x67, 0x39, 0x3b, 0x63, 0xbc, //0x00005d20 .quad -4871985254153548564
	0xcf, 0xdc, 0xf2, 0x3c, 0xa7, 0x01, 0x4a, 0xf2, //0x00005d28 .quad -987975350460687153
	0x13, 0xd8, 0x85, 0xe0, 0x03, 0x05, 0xbe, 0xd5, //0x00005d30 .quad -3044990783845967853
	0x01, 0xca, 0x17, 0x86, 0x08, 0x41, 0x6e, 0x97, //0x00005d38 .quad -7535013621679011327
	0x18, 0x4e, 0xa7, 0xd8, 0x44, 0x86, 0x2d, 0x4b, //0x00005d40 .quad 5417133557047315992
	0x82, 0xbc, 0x9d, 0xa7, 0x4a, 0xd1, 0x49, 0xbd, //0x00005d48 .quad -4807081008671376254
	0x9e, 0x21, 0xd1, 0x0e, 0xd6, 0xe7, 0xf8, 0xdd, //0x00005d50 .quad -2451955090545630818
	0xa2, 0x2b, 0x85, 0x51, 0x9d, 0x45, 0x9c, 0xec, //0x00005d58 .quad -1397165242411832414
	0x03, 0xb5, 0x42, 0xc9, 0xe5, 0x90, 0xbb, 0xca, //0x00005d60 .quad -3838314940804713213
	0x45, 0x3b, 0xf3, 0x52, 0x82, 0xab, 0xe1, 0x93, //0x00005d68 .quad -7790757304148477115
	0x43, 0x62, 0x93, 0x3b, 0x1f, 0x75, 0x6a, 0x3d, //0x00005d70 .quad 4425478360848884291
	0x17, 0x0a, 0xb0, 0xe7, 0x62, 0x16, 0xda, 0xb8, //0x00005d78 .quad -5126760611758208489
	0xd4, 0x3a, 0x78, 0x0a, 0x67, 0x12, 0xc5, 0x0c, //0x00005d80 .quad 920161932633717460
	0x9d, 0x0c, 0x9c, 0xa1, 0xfb, 0x9b, 0x10, 0xe7, //0x00005d88 .quad -1796764746270372707
	0xc5, 0x24, 0x8b, 0x66, 0x80, 0x2b, 0xfb, 0x27, //0x00005d90 .quad 2880944217109767365
	0xe2, 0x87, 0x01, 0x45, 0x7d, 0x61, 0x6a, 0x90, //0x00005d98 .quad -8040506994060064798
	0xf6, 0xed, 0x2d, 0x80, 0x60, 0xf6, 0xf9, 0xb1, //0x00005da0 .quad -5622191765467566602
	0xda, 0xe9, 0x41, 0x96, 0xdc, 0xf9, 0x84, 0xb4, //0x00005da8 .quad -5438947724147693094
	0x73, 0x69, 0x39, 0xa0, 0xf8, 0x73, 0x78, 0x5e, //0x00005db0 .quad 6807318348447705459
	0x51, 0x64, 0xd2, 0xbb, 0x53, 0x38, 0xa6, 0xe1, //0x00005db8 .quad -2186998636757228463
	0xe8, 0xe1, 0x23, 0x64, 0x7b, 0x48, 0x0b, 0xdb, //0x00005dc0 .quad -2662955059861265944
	0xb2, 0x7e, 0x63, 0x55, 0x34, 0xe3, 0x07, 0x8d, //0x00005dc8 .quad -8284403175614349646
	0x62, 0xda, 0x2c, 0x3d, 0x9a, 0x1a, 0xce, 0x91, //0x00005dd0 .quad -7940379843253970334
	0x5f, 0x5e, 0xbc, 0x6a, 0x01, 0xdc, 0x49, 0xb0, //0x00005dd8 .quad -5743817951090549153
	0xfb, 0x10, 0x78, 0xcc, 0x40, 0xa1, 0x41, 0x76, //0x00005de0 .quad 8521269269642088699
	0xf7, 0x75, 0x6b, 0xc5, 0x01, 0x53, 0x5c, 0xdc, //0x00005de8 .quad -2568086420435798537
	0x9d, 0x0a, 0xcb, 0x7f, 0xc8, 0x04, 0xe9, 0xa9, //0x00005df0 .quad -6203421752542164323
	0xba, 0x29, 0x63, 0x1b, 0xe1, 0xb3, 0xb9, 0x89, //0x00005df8 .quad -8522583040413455942
	0x44, 0xcd, 0xbd, 0x9f, 0xfa, 0x45, 0x63, 0x54, //0x00005e00 .quad 6080780864604458308
	0x29, 0xf4, 0x3b, 0x62, 0xd9, 0x20, 0x28, 0xac, //0x00005e08 .quad -6041542782089432023
	0x95, 0x40, 0xad, 0x47, 0x79, 0x17, 0x7c, 0xa9, //0x00005e10 .quad -6234081974526590827
	0x33, 0xf1, 0xca, 0xba, 0x0f, 0x29, 0x32, 0xd7, //0x00005e18 .quad -2940242459184402125
	0x5d, 0x48, 0xcc, 0xcc, 0xab, 0x8e, 0xed, 0x49, //0x00005e20 .quad 5327070802775656541
	0xc0, 0xd6, 0xbe, 0xd4, 0xa9, 0x59, 0x7f, 0x86, //0x00005e28 .quad -8755180564631333184
	0x74, 0x5a, 0xff, 0xbf, 0x56, 0xf2, 0x68, 0x5c, //0x00005e30 .quad 6658838503469570676
	0x70, 0x8c, 0xee, 0x49, 0x14, 0x30, 0x1f, 0xa8, //0x00005e38 .quad -6332289687361778576
	0x11, 0x31, 0xff, 0x6f, 0xec, 0x2e, 0x83, 0x73, //0x00005e40 .quad 8323548129336963345
	0x8c, 0x2f, 0x6a, 0x5c, 0x19, 0xfc, 0x26, 0xd2, //0x00005e48 .quad -3303676090774835316
	0xab, 0x7e, 0xff, 0xc5, 0x53, 0xfd, 0x31, 0xc8, //0x00005e50 .quad -4021154456019173717
	0xb7, 0x5d, 0xc2, 0xd9, 0x8f, 0x5d, 0x58, 0x83, //0x00005e58 .quad -8982326584375353929
	0x55, 0x5e, 0x7f, 0xb7, 0xa8, 0x7c, 0x3e, 0xba, //0x00005e60 .quad -5026443070023967147
	0x25, 0xf5, 0x32, 0xd0, 0xf3, 0x74, 0x2e, 0xa4, //0x00005e68 .quad -6616222212041804507
	0xeb, 0x35, 0x5f, 0xe5, 0xd2, 0x1b, 0xce, 0x28, //0x00005e70 .quad 2940318199324816875
	0x6f, 0xb2, 0x3f, 0xc4, 0x30, 0x12, 0x3a, 0xcd, //0x00005e78 .quad -3658591746624867729
	0xb3, 0x81, 0x5b, 0xcf, 0x63, 0xd1, 0x80, 0x79, //0x00005e80 .quad 8755227902219092403
	0x85, 0xcf, 0xa7, 0x7a, 0x5e, 0x4b, 0x44, 0x80, //0x00005e88 .quad -9204148869281624187
	0x1f, 0x62, 0x32, 0xc3, 0xbc, 0x05, 0xe1, 0xd7, //0x00005e90 .quad -2891023177508298209
	0x66, 0xc3, 0x51, 0x19, 0x36, 0x5e, 0x55, 0xa0, //0x00005e98 .quad -6893500068174642330
	0xa7, 0xfa, 0xfe, 0xf3, 0x2b, 0x47, 0xd9, 0x8d, //0x00005ea0 .quad -8225464990312760665
	0x40, 0x34, 0xa6, 0x9f, 0xc3, 0xb5, 0x6a, 0xc8, //0x00005ea8 .quad -4005189066790915008
	0x51, 0xb9, 0xfe, 0xf0, 0xf6, 0x98, 0x4f, 0xb1, //0x00005eb0 .quad -5670145219463562927
	0x50, 0xc1, 0x8f, 0x87, 0x34, 0x63, 0x85, 0xfa, //0x00005eb8 .quad -394800315061255856
	0xd3, 0x33, 0x9f, 0x56, 0x9a, 0xbf, 0xd1, 0x6e, //0x00005ec0 .quad 7985374283903742931
	0xd2, 0xd8, 0xb9, 0xd4, 0x00, 0x5e, 0x93, 0x9c, //0x00005ec8 .quad -7164279224554366766
	0xc8, 0x00, 0x47, 0xec, 0x80, 0x2f, 0x86, 0x0a, //0x00005ed0 .quad 758345818024902856
	0x07, 0x4f, 0xe8, 0x09, 0x81, 0x35, 0xb8, 0xc3, //0x00005ed8 .quad -4343663012265570553
	0xfa, 0xc0, 0x58, 0x27, 0x61, 0xbb, 0x27, 0xcd, //0x00005ee0 .quad -3663753745896259334
	0xc8, 0x62, 0x62, 0x4c, 0xe1, 0x42, 0xa6, 0xf4, //0x00005ee8 .quad -817892746904575288
	0x9c, 0x78, 0x97, 0xb8, 0x1c, 0xd5, 0x38, 0x80, //0x00005ef0 .quad -9207375118826243940
	0xbd, 0x7d, 0xbd, 0xcf, 0xcc, 0xe9, 0xe7, 0x98, //0x00005ef8 .quad -7428711994456441411
	0xc3, 0x56, 0xbd, 0xe6, 0x63, 0x0a, 0x47, 0xe0, //0x00005f00 .quad -2285846861678029117
	0x2c, 0xdd, 0xac, 0x03, 0x40, 0xe4, 0x21, 0xbf, //0x00005f08 .quad -4674203974643163860
	0x74, 0xac, 0x6c, 0xe0, 0xfc, 0xcc, 0x58, 0x18, //0x00005f10 .quad 1754377441329851508
	0x78, 0x14, 0x98, 0x04, 0x50, 0x5d, 0xea, 0xee, //0x00005f18 .quad -1231068949876566920
	0xc8, 0xeb, 0x43, 0x0c, 0x1e, 0x80, 0x37, 0x0f, //0x00005f20 .quad 1096485900831157192
	0xcb, 0x0c, 0xdf, 0x02, 0x52, 0x7a, 0x52, 0x95, //0x00005f28 .quad -7686947121313936181
	0xba, 0xe6, 0x54, 0x8f, 0x25, 0x60, 0x05, 0xd3, //0x00005f30 .quad -3241078642388441414
	0xfd, 0xcf, 0x96, 0x83, 0xe6, 0x18, 0xa7, 0xba, //0x00005f38 .quad -4996997883215032323
	0x69, 0x20, 0x2a, 0xf3, 0x2e, 0xb8, 0xc6, 0x47, //0x00005f40 .quad 5172023733869224041
	0xfd, 0x83, 0x7c, 0x24, 0x20, 0xdf, 0x50, 0xe9, //0x00005f48 .quad -1634561335591402499
	0x41, 0x54, 0xfa, 0x57, 0x1d, 0x33, 0xdc, 0x4c, //0x00005f50 .quad 5538357842881958977
	0x7e, 0xd2, 0xcd, 0x16, 0x74, 0x8b, 0xd2, 0x91, //0x00005f58 .quad -7939129862385708418
	0x52, 0xe9, 0xf8, 0xad, 0xe4, 0x3f, 0x13, 0xe0, //0x00005f60 .quad -2300424733252327086
	0x1d, 0x47, 0x81, 0x1c, 0x51, 0x2e, 0x47, 0xb6, //0x00005f68 .quad -5312226309554747619
	0xa6, 0x23, 0x77, 0xd9, 0xdd, 0x0f, 0x18, 0x58, //0x00005f70 .quad 6347841120289366950
	0xe5, 0x98, 0xa1, 0x63, 0xe5, 0xf9, 0xd8, 0xe3, //0x00005f78 .quad -2028596868516046619
	0x48, 0x76, 0xea, 0xa7, 0xea, 0x09, 0x0f, 0x57, //0x00005f80 .quad 6273243709394548296
	0x8f, 0xff, 0x44, 0x5e, 0x2f, 0x9c, 0x67, 0x8e, //0x00005f88 .quad -8185402070463610993
	0xda, 0x13, 0xe5, 0x51, 0x65, 0xcc, 0xd2, 0x2c, //0x00005f90 .quad 3229868618315797466
	0x73, 0x3f, 0xd6, 0x35, 0x3b, 0x83, 0x01, 0xb2, //0x00005f98 .quad -5620066569652125837
	0xd1, 0x58, 0x5e, 0xa6, 0x7e, 0x7f, 0x07, 0xf8, //0x00005fa0 .quad -574350245532641071
	0x4f, 0xcf, 0x4b, 0x03, 0x0a, 0xe4, 0x81, 0xde, //0x00005fa8 .quad -2413397193637769393
	0x82, 0xf7, 0xfa, 0x27, 0xaf, 0xaf, 0x04, 0xfb, //0x00005fb0 .quad -358968903457900670
	0x91, 0x61, 0x0f, 0x42, 0x86, 0x2e, 0x11, 0x8b, //0x00005fb8 .quad -8425902273664687727
	0x63, 0xb5, 0xf9, 0xf1, 0x9a, 0xdb, 0xc5, 0x79, //0x00005fc0 .quad 8774660907532399971
	0xf6, 0x39, 0x93, 0xd2, 0x27, 0x7a, 0xd5, 0xad, //0x00005fc8 .quad -5920691823653471754
	0xbc, 0x22, 0x78, 0xae, 0x81, 0x52, 0x37, 0x18, //0x00005fd0 .quad 1744954097560724156
	0x74, 0x08, 0x38, 0xc7, 0xb1, 0xd8, 0x4a, 0xd9, //0x00005fd8 .quad -2789178761139451788
	0xb5, 0x15, 0x0b, 0x0d, 0x91, 0x93, 0x22, 0x8f, //0x00005fe0 .quad -8132775725879323211
	0x48, 0x05, 0x83, 0x1c, 0x6f, 0xc7, 0xce, 0x87, //0x00005fe8 .quad -8660765753353239224
	0x22, 0xdb, 0x4d, 0x50, 0x75, 0x38, 0xeb, 0xb2, //0x00005ff0 .quad -5554283638921766110
	0x9a, 0xc6, 0xa3, 0xe3, 0x4a, 0x79, 0xc2, 0xa9, //0x00005ff8 .quad -6214271173264161126
	0xeb, 0x51, 0x61, 0xa4, 0x92, 0x06, 0xa6, 0x5f, //0x00006000 .quad 6892203506629956075
	0x41, 0xb8, 0x8c, 0x9c, 0x9d, 0x17, 0x33, 0xd4, //0x00006008 .quad -3156152948152813503
	0x33, 0xd3, 0xbc, 0xa6, 0x1b, 0xc4, 0xc7, 0xdb, //0x00006010 .quad -2609901835997359309
	0x28, 0xf3, 0xd7, 0x81, 0xc2, 0xee, 0x9f, 0x84, //0x00006018 .quad -8890124620236590296
	0x00, 0x08, 0x6c, 0x90, 0x22, 0xb5, 0xb9, 0x12, //0x00006020 .quad 1349308723430688768
	0xf3, 0xef, 0x4d, 0x22, 0x73, 0xea, 0xc7, 0xa5, //0x00006028 .quad -6500969756868349965
	0x00, 0x0a, 0x87, 0x34, 0x6b, 0x22, 0x68, 0xd7, //0x00006030 .quad -2925050114139026944
	0xef, 0x6b, 0xe1, 0xea, 0x0f, 0xe5, 0x39, 0xcf, //0x00006038 .quad -3514526177658049553
	0x40, 0x66, 0xd4, 0x00, 0x83, 0x15, 0xa1, 0xe6, //0x00006040 .quad -1828156321336891840
	0x75, 0xe3, 0xcc, 0xf2, 0x29, 0x2f, 0x84, 0x81, //0x00006048 .quad -9114107888677362827
	0xd0, 0x7f, 0x09, 0xc1, 0xe3, 0x5a, 0x49, 0x60, //0x00006050 .quad 6938176635183661008
	0x53, 0x1c, 0x80, 0x6f, 0xf4, 0x3a, 0xe5, 0xa1, //0x00006058 .quad -6780948842419315629
	0xc4, 0xdf, 0x4b, 0xb1, 0x9c, 0xb1, 0x5b, 0x38, //0x00006060 .quad 4061034775552188356
	0x68, 0x23, 0x60, 0x8b, 0xb1, 0x89, 0x5e, 0xca, //0x00006068 .quad -3864500034596756632
	0xb5, 0xd7, 0x9e, 0xdd, 0x03, 0x9e, 0x72, 0x46, //0x00006070 .quad 5076293469440235445
	0x42, 0x2c, 0x38, 0xee, 0x1d, 0x2c, 0xf6, 0xfc, //0x00006078 .quad -218939024818557886
	0xd1, 0x46, 0x83, 0x6a, 0xc2, 0xa2, 0x07, 0x6c, //0x00006080 .quad 7784369436827535057
	0xa9, 0x1b, 0xe3, 0xb4, 0x92, 0xdb, 0x19, 0x9e, //0x00006088 .quad -7054365918152680535
	0x85, 0x18, 0x24, 0x05, 0x73, 0x8b, 0x09, 0xc7, //0x00006090 .quad -4104596259247744891
	0x93, 0xe2, 0x1b, 0x62, 0x77, 0x52, 0xa0, 0xc5, //0x00006098 .quad -4206271379263462765
	0xa7, 0x1e, 0x6d, 0xc6, 0x4f, 0xee, 0xcb, 0xb8, //0x000060a0 .quad -5130745324059681113
	0x38, 0xdb, 0xa2, 0x3a, 0x15, 0x67, 0x08, 0xf7, //0x000060a8 .quad -646153205651940552
	0x28, 0x33, 0x04, 0xdc, 0xf1, 0x74, 0x7f, 0x73, //0x000060b0 .quad 8322499218531169064
	0x03, 0xc9, 0xa5, 0x44, 0x6d, 0x40, 0x65, 0x9a, //0x000060b8 .quad -7321374781173544701
	0xf2, 0x3f, 0x05, 0x53, 0x2e, 0x52, 0x5f, 0x50, //0x000060c0 .quad 5791438004736573426
	0x44, 0x3b, 0xcf, 0x95, 0x88, 0x90, 0xfe, 0xc0, //0x000060c8 .quad -4540032458039542972
	0xef, 0x8f, 0xc6, 0xe7, 0xb9, 0x26, 0x77, 0x64, //0x000060d0 .quad 7239297505920716783
	0x15, 0x0a, 0x43, 0xbb, 0xaa, 0x34, 0x3e, 0xf1, //0x000060d8 .quad -1063354554122040811
	0xf5, 0x19, 0xdc, 0x30, 0x34, 0x78, 0xca, 0x5e, //0x000060e0 .quad 6830403950414141941
	0x4d, 0xe6, 0x09, 0xb5, 0xea, 0xe0, 0xc6, 0x96, //0x000060e8 .quad -7582125623967357363
	0x72, 0x20, 0x13, 0x3d, 0x41, 0x16, 0x7d, 0xb6, //0x000060f0 .quad -5297053117264486286
	0xe0, 0x5f, 0x4c, 0x62, 0x25, 0x99, 0x78, 0xbc, //0x000060f8 .quad -4865971011531808800
	0x8f, 0xe8, 0x57, 0x8c, 0xd1, 0x5b, 0x1c, 0xe4, //0x00006100 .quad -2009630378153219953
	0xd8, 0x77, 0xdf, 0xba, 0x6e, 0xbf, 0x96, 0xeb, //0x00006108 .quad -1470777745987373096
	0x59, 0xf1, 0xb6, 0xf7, 0x62, 0xb9, 0x91, 0x8e, //0x00006110 .quad -8173548013986844327
	0xe7, 0xaa, 0xcb, 0x34, 0xa5, 0x37, 0x3e, 0x93, //0x00006118 .quad -7836765118883190041
	0xb0, 0xad, 0xa4, 0xb5, 0xbb, 0x27, 0x36, 0x72, //0x00006120 .quad 8229809056225996208
	0xa1, 0x95, 0xfe, 0x81, 0x8e, 0xc5, 0x0d, 0xb8, //0x00006128 .quad -5184270380176599647
	0x1c, 0xd9, 0x0d, 0xa3, 0xaa, 0xb1, 0xc3, 0xce, //0x00006130 .quad -3547796734999668452
	0x09, 0x3b, 0x7e, 0x22, 0xf2, 0x36, 0x11, 0xe6, //0x00006138 .quad -1868651956793361655
	0xb1, 0xa7, 0xe8, 0xa5, 0x0a, 0x4f, 0x3a, 0x21, //0x00006140 .quad 2394313059052595121
	0xe6, 0xe4, 0x8e, 0x55, 0x57, 0xc2, 0xca, 0x8f, //0x00006148 .quad -8085436500636932890
	0x9d, 0xd1, 0x62, 0x4f, 0xcd, 0xe2, 0x88, 0xa9, //0x00006150 .quad -6230480713039031907
	0x1f, 0x9e, 0xf2, 0x2a, 0xed, 0x72, 0xbd, 0xb3, //0x00006158 .quad -5495109607368778209
	0x05, 0x86, 0x3b, 0xa3, 0x80, 0x1b, 0xeb, 0x93, //0x00006160 .quad -7788100891298789883
	0xa7, 0x45, 0xaf, 0x75, 0xa8, 0xcf, 0xac, 0xe0, //0x00006168 .quad -2257200990783584857
	0xc3, 0x33, 0x05, 0x66, 0x30, 0xf1, 0x72, 0xbc, //0x00006170 .quad -4867563057061743677
	0x88, 0x8b, 0x8d, 0x49, 0xc9, 0x01, 0x6c, 0x8c, //0x00006178 .quad -8328279646880822392
	0xb4, 0x80, 0x86, 0x7f, 0x7c, 0xad, 0x8f, 0xeb, //0x00006180 .quad -1472767802899791692
	0x6a, 0xee, 0xf0, 0x9b, 0x3b, 0x02, 0x87, 0xaf, //0x00006188 .quad -5798663540173640086
	0xe1, 0x20, 0x68, 0x9f, 0xdb, 0x98, 0x73, 0xa6, //0x00006190 .quad -6452645772052127519
	0x05, 0x2a, 0xed, 0x82, 0xca, 0xc2, 0x68, 0xdb, //0x00006198 .quad -2636643406789662203
	0x8c, 0x14, 0xa1, 0x43, 0x89, 0x3f, 0x08, 0x88, //0x000061a0 .quad -8644589625959967604
	0x43, 0x3a, 0xd4, 0x91, 0xbe, 0x79, 0x21, 0x89, //0x000061a8 .quad -8565431156884620733
	0xb0, 0x59, 0x89, 0x94, 0x6b, 0x4f, 0x0a, 0x6a, //0x000061b0 .quad 7641007041259592112
	0xd4, 0x48, 0x49, 0x36, 0x2e, 0xd8, 0x69, 0xab, //0x000061b8 .quad -6095102927678388012
	0x1c, 0xb0, 0xab, 0x79, 0x46, 0xe3, 0x8c, 0x84, //0x000061c0 .quad -8895485272135061476
	0x09, 0x9b, 0xdb, 0xc3, 0x39, 0x4e, 0x44, 0xd6, //0x000061c8 .quad -3007192641170597111
	0x11, 0x4e, 0x0b, 0x0c, 0x0c, 0x0e, 0xd8, 0xf2, //0x000061d0 .quad -947992276657025519
	0xe5, 0x40, 0x69, 0x1a, 0xe4, 0xb0, 0xea, 0x85, //0x000061d8 .quad -8797024428372705051
	0x95, 0x21, 0x0e, 0x0f, 0x8f, 0x11, 0x8e, 0x6f, //0x000061e0 .quad 8038381691033493909
	0x1f, 0x91, 0x03, 0x21, 0x1d, 0x5d, 0x65, 0xa7, //0x000061e8 .quad -6384594517038493409
	0xfb, 0xa9, 0xd1, 0xd2, 0xf2, 0x95, 0x71, 0x4b, //0x000061f0 .quad 5436291095364479483
	0x67, 0x75, 0x44, 0x69, 0x64, 0xb4, 0x3e, 0xd1, //0x000061f8 .quad -3369057127870728857
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006200 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	//0x00006210 .p2align 4, 0x00
	//0x00006210 _POW_TAB
	0x01, 0x00, 0x00, 0x00, //0x00006210 .long 1
	0x03, 0x00, 0x00, 0x00, //0x00006214 .long 3
	0x06, 0x00, 0x00, 0x00, //0x00006218 .long 6
	0x09, 0x00, 0x00, 0x00, //0x0000621c .long 9
	0x0d, 0x00, 0x00, 0x00, //0x00006220 .long 13
	0x10, 0x00, 0x00, 0x00, //0x00006224 .long 16
	0x13, 0x00, 0x00, 0x00, //0x00006228 .long 19
	0x17, 0x00, 0x00, 0x00, //0x0000622c .long 23
	0x1a, 0x00, 0x00, 0x00, //0x00006230 .long 26
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006234 .p2align 4, 0x00
	//0x00006240 _LSHIFT_TAB
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006240 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006250 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006260 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006270 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006280 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006290 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062a0 QUAD $0x0000000000000000  // .space 8, '\x00\x00\x00\x00\x00\x00\x00\x00'
	0x01, 0x00, 0x00, 0x00, //0x000062a8 .long 1
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062ac QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000630c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x01, 0x00, 0x00, 0x00, //0x00006310 .long 1
	0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006314 QUAD $0x0000000000003532; QUAD $0x0000000000000000  // .asciz 16, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006324 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006334 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006344 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006354 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006364 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006374 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x01, 0x00, 0x00, 0x00, //0x00006378 .long 1
	0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000637c QUAD $0x0000000000353231; QUAD $0x0000000000000000  // .asciz 16, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000638c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000639c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000063dc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x02, 0x00, 0x00, 0x00, //0x000063e0 .long 2
	0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063e4 QUAD $0x0000000000353236; QUAD $0x0000000000000000  // .asciz 16, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006404 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006414 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006424 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006434 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006444 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x02, 0x00, 0x00, 0x00, //0x00006448 .long 2
	0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000644c QUAD $0x0000000035323133; QUAD $0x0000000000000000  // .asciz 16, '3125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000645c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000646c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000647c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000648c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000649c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000064ac LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x02, 0x00, 0x00, 0x00, //0x000064b0 .long 2
	0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064b4 QUAD $0x0000003532363531; QUAD $0x0000000000000000  // .asciz 16, '15625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006504 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006514 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x03, 0x00, 0x00, 0x00, //0x00006518 .long 3
	0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000651c QUAD $0x0000003532313837; QUAD $0x0000000000000000  // .asciz 16, '78125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000652c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000653c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000654c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000655c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000656c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000657c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x03, 0x00, 0x00, 0x00, //0x00006580 .long 3
	0x33, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006584 QUAD $0x0000353236303933; QUAD $0x0000000000000000  // .asciz 16, '390625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006594 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000065e4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x03, 0x00, 0x00, 0x00, //0x000065e8 .long 3
	0x31, 0x39, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065ec QUAD $0x0035323133353931; QUAD $0x0000000000000000  // .asciz 16, '1953125\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000660c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000661c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000662c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000663c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000664c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x00006650 .long 4
	0x39, 0x37, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006654 QUAD $0x0035323635363739; QUAD $0x0000000000000000  // .asciz 16, '9765625\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006664 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006674 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006684 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006694 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000066b4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x000066b8 .long 4
	0x34, 0x38, 0x38, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066bc QUAD $0x3532313832383834; QUAD $0x0000000000000000  // .asciz 16, '48828125\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000670c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000671c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x00006720 .long 4
	0x32, 0x34, 0x34, 0x31, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006724 QUAD $0x3236303431343432; QUAD $0x0000000000000035  // .asciz 16, '244140625\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006734 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006744 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006754 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006764 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006774 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006784 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x00006788 .long 4
	0x31, 0x32, 0x32, 0x30, 0x37, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000678c QUAD $0x3133303730323231; QUAD $0x0000000000003532  // .asciz 16, '1220703125\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000679c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000067ec LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x05, 0x00, 0x00, 0x00, //0x000067f0 .long 5
	0x36, 0x31, 0x30, 0x33, 0x35, 0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067f4 QUAD $0x3635313533303136; QUAD $0x0000000000003532  // .asciz 16, '6103515625\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006804 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006814 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006824 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006834 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006844 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006854 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x05, 0x00, 0x00, 0x00, //0x00006858 .long 5
	0x33, 0x30, 0x35, 0x31, 0x37, 0x35, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000685c QUAD $0x3837353731353033; QUAD $0x0000000000353231  // .asciz 16, '30517578125\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000686c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000687c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000688c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000689c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000068ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000068bc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x05, 0x00, 0x00, 0x00, //0x000068c0 .long 5
	0x31, 0x35, 0x32, 0x35, 0x38, 0x37, 0x38, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x000068c4 QUAD $0x3938373835323531; QUAD $0x0000000035323630  // .asciz 16, '152587890625\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000068d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000068e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000068f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006904 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006914 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006924 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x06, 0x00, 0x00, 0x00, //0x00006928 .long 6
	0x37, 0x36, 0x32, 0x39, 0x33, 0x39, 0x34, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x0000692c QUAD $0x3534393339323637; QUAD $0x0000000035323133  // .asciz 16, '762939453125\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000693c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000694c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000695c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000696c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000697c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000698c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x06, 0x00, 0x00, 0x00, //0x00006990 .long 6
	0x33, 0x38, 0x31, 0x34, 0x36, 0x39, 0x37, 0x32, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, //0x00006994 QUAD $0x3237393634313833; QUAD $0x0000003532363536  // .asciz 16, '3814697265625\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000069a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000069b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000069c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000069d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000069e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000069f4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x06, 0x00, 0x00, 0x00, //0x000069f8 .long 6
	0x31, 0x39, 0x30, 0x37, 0x33, 0x34, 0x38, 0x36, 0x33, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, //0x000069fc QUAD $0x3638343337303931; QUAD $0x0000353231383233  // .asciz 16, '19073486328125\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a0c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a1c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a2c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a3c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a4c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006a5c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x00006a60 .long 7
	0x39, 0x35, 0x33, 0x36, 0x37, 0x34, 0x33, 0x31, 0x36, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, //0x00006a64 QUAD $0x3133343736333539; QUAD $0x0000353236303436  // .asciz 16, '95367431640625\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a74 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a84 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a94 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006aa4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ab4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006ac4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x00006ac8 .long 7
	0x34, 0x37, 0x36, 0x38, 0x33, 0x37, 0x31, 0x35, 0x38, 0x32, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, //0x00006acc QUAD $0x3531373338363734; QUAD $0x0035323133303238  // .asciz 16, '476837158203125\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006adc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006aec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006afc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b0c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b1c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006b2c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x00006b30 .long 7
	0x32, 0x33, 0x38, 0x34, 0x31, 0x38, 0x35, 0x37, 0x39, 0x31, 0x30, 0x31, 0x35, 0x36, 0x32, 0x35, //0x00006b34 QUAD $0x3735383134383332; QUAD $0x3532363531303139  // .asciz 16, '2384185791015625'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b44 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b54 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b64 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b74 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b84 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006b94 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x00006b98 .long 7
	0x31, 0x31, 0x39, 0x32, 0x30, 0x39, 0x32, 0x38, 0x39, 0x35, 0x35, 0x30, 0x37, 0x38, 0x31, 0x32, //0x00006b9c QUAD $0x3832393032393131; QUAD $0x3231383730353539  // .asciz 16, '1192092895507812'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006bac QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006bbc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006bcc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006bdc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006bec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006bfc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x08, 0x00, 0x00, 0x00, //0x00006c00 .long 8
	0x35, 0x39, 0x36, 0x30, 0x34, 0x36, 0x34, 0x34, 0x37, 0x37, 0x35, 0x33, 0x39, 0x30, 0x36, 0x32, //0x00006c04 QUAD $0x3434363430363935; QUAD $0x3236303933353737  // .asciz 16, '5960464477539062'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c14 QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c24 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c34 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c44 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c54 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006c64 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x08, 0x00, 0x00, 0x00, //0x00006c68 .long 8
	0x32, 0x39, 0x38, 0x30, 0x32, 0x33, 0x32, 0x32, 0x33, 0x38, 0x37, 0x36, 0x39, 0x35, 0x33, 0x31, //0x00006c6c QUAD $0x3232333230383932; QUAD $0x3133353936373833  // .asciz 16, '2980232238769531'
	0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c7c QUAD $0x0000000000003532; QUAD $0x0000000000000000  // .asciz 16, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c8c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c9c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006cac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006cbc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006ccc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x08, 0x00, 0x00, 0x00, //0x00006cd0 .long 8
	0x31, 0x34, 0x39, 0x30, 0x31, 0x31, 0x36, 0x31, 0x31, 0x39, 0x33, 0x38, 0x34, 0x37, 0x36, 0x35, //0x00006cd4 QUAD $0x3136313130393431; QUAD $0x3536373438333931  // .asciz 16, '1490116119384765'
	0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ce4 QUAD $0x0000000000353236; QUAD $0x0000000000000000  // .asciz 16, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006cf4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d04 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d14 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d24 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006d34 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x09, 0x00, 0x00, 0x00, //0x00006d38 .long 9
	0x37, 0x34, 0x35, 0x30, 0x35, 0x38, 0x30, 0x35, 0x39, 0x36, 0x39, 0x32, 0x33, 0x38, 0x32, 0x38, //0x00006d3c QUAD $0x3530383530353437; QUAD $0x3832383332393639  // .asciz 16, '7450580596923828'
	0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d4c QUAD $0x0000000000353231; QUAD $0x0000000000000000  // .asciz 16, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d5c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d6c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d7c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d8c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006d9c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x09, 0x00, 0x00, 0x00, //0x00006da0 .long 9
	0x33, 0x37, 0x32, 0x35, 0x32, 0x39, 0x30, 0x32, 0x39, 0x38, 0x34, 0x36, 0x31, 0x39, 0x31, 0x34, //0x00006da4 QUAD $0x3230393235323733; QUAD $0x3431393136343839  // .asciz 16, '3725290298461914'
	0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006db4 QUAD $0x0000000035323630; QUAD $0x0000000000000000  // .asciz 16, '0625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006dc4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006dd4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006de4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006df4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006e04 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x09, 0x00, 0x00, 0x00, //0x00006e08 .long 9
	0x31, 0x38, 0x36, 0x32, 0x36, 0x34, 0x35, 0x31, 0x34, 0x39, 0x32, 0x33, 0x30, 0x39, 0x35, 0x37, //0x00006e0c QUAD $0x3135343632363831; QUAD $0x3735393033323934  // .asciz 16, '1862645149230957'
	0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e1c QUAD $0x0000003532313330; QUAD $0x0000000000000000  // .asciz 16, '03125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e2c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e3c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e4c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e5c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006e6c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00006e70 .long 10
	0x39, 0x33, 0x31, 0x33, 0x32, 0x32, 0x35, 0x37, 0x34, 0x36, 0x31, 0x35, 0x34, 0x37, 0x38, 0x35, //0x00006e74 QUAD $0x3735323233313339; QUAD $0x3538373435313634  // .asciz 16, '9313225746154785'
	0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e84 QUAD $0x0000003532363531; QUAD $0x0000000000000000  // .asciz 16, '15625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e94 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ea4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006eb4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ec4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006ed4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00006ed8 .long 10
	0x34, 0x36, 0x35, 0x36, 0x36, 0x31, 0x32, 0x38, 0x37, 0x33, 0x30, 0x37, 0x37, 0x33, 0x39, 0x32, //0x00006edc QUAD $0x3832313636353634; QUAD $0x3239333737303337  // .asciz 16, '4656612873077392'
	0x35, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006eec QUAD $0x0000353231383735; QUAD $0x0000000000000000  // .asciz 16, '578125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006efc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f0c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f1c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f2c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006f3c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00006f40 .long 10
	0x32, 0x33, 0x32, 0x38, 0x33, 0x30, 0x36, 0x34, 0x33, 0x36, 0x35, 0x33, 0x38, 0x36, 0x39, 0x36, //0x00006f44 QUAD $0x3436303338323332; QUAD $0x3639363833353633  // .asciz 16, '2328306436538696'
	0x32, 0x38, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f54 QUAD $0x0035323630393832; QUAD $0x0000000000000000  // .asciz 16, '2890625\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f64 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f74 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f84 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f94 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006fa4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00006fa8 .long 10
	0x31, 0x31, 0x36, 0x34, 0x31, 0x35, 0x33, 0x32, 0x31, 0x38, 0x32, 0x36, 0x39, 0x33, 0x34, 0x38, //0x00006fac QUAD $0x3233353134363131; QUAD $0x3834333936323831  // .asciz 16, '1164153218269348'
	0x31, 0x34, 0x34, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006fbc QUAD $0x3532313335343431; QUAD $0x0000000000000000  // .asciz 16, '14453125\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006fcc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006fdc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006fec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ffc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000700c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0b, 0x00, 0x00, 0x00, //0x00007010 .long 11
	0x35, 0x38, 0x32, 0x30, 0x37, 0x36, 0x36, 0x30, 0x39, 0x31, 0x33, 0x34, 0x36, 0x37, 0x34, 0x30, //0x00007014 QUAD $0x3036363730323835; QUAD $0x3034373634333139  // .asciz 16, '5820766091346740'
	0x37, 0x32, 0x32, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007024 QUAD $0x3532363536323237; QUAD $0x0000000000000000  // .asciz 16, '72265625\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007034 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007044 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007054 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007064 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007074 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0b, 0x00, 0x00, 0x00, //0x00007078 .long 11
	0x32, 0x39, 0x31, 0x30, 0x33, 0x38, 0x33, 0x30, 0x34, 0x35, 0x36, 0x37, 0x33, 0x33, 0x37, 0x30, //0x0000707c QUAD $0x3033383330313932; QUAD $0x3037333337363534  // .asciz 16, '2910383045673370'
	0x33, 0x36, 0x31, 0x33, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000708c QUAD $0x3231383233313633; QUAD $0x0000000000000035  // .asciz 16, '361328125\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000709c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000070ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000070bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000070cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000070dc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0b, 0x00, 0x00, 0x00, //0x000070e0 .long 11
	0x31, 0x34, 0x35, 0x35, 0x31, 0x39, 0x31, 0x35, 0x32, 0x32, 0x38, 0x33, 0x36, 0x36, 0x38, 0x35, //0x000070e4 QUAD $0x3531393135353431; QUAD $0x3538363633383232  // .asciz 16, '1455191522836685'
	0x31, 0x38, 0x30, 0x36, 0x36, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000070f4 QUAD $0x3630343636303831; QUAD $0x0000000000003532  // .asciz 16, '1806640625\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007104 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007114 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007124 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007134 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007144 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0c, 0x00, 0x00, 0x00, //0x00007148 .long 12
	0x37, 0x32, 0x37, 0x35, 0x39, 0x35, 0x37, 0x36, 0x31, 0x34, 0x31, 0x38, 0x33, 0x34, 0x32, 0x35, //0x0000714c QUAD $0x3637353935373237; QUAD $0x3532343338313431  // .asciz 16, '7275957614183425'
	0x39, 0x30, 0x33, 0x33, 0x32, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000715c QUAD $0x3133303233333039; QUAD $0x0000000000003532  // .asciz 16, '9033203125\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000716c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000717c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000718c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000719c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000071ac LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0c, 0x00, 0x00, 0x00, //0x000071b0 .long 12
	0x33, 0x36, 0x33, 0x37, 0x39, 0x37, 0x38, 0x38, 0x30, 0x37, 0x30, 0x39, 0x31, 0x37, 0x31, 0x32, //0x000071b4 QUAD $0x3838373937333633; QUAD $0x3231373139303730  // .asciz 16, '3637978807091712'
	0x39, 0x35, 0x31, 0x36, 0x36, 0x30, 0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000071c4 QUAD $0x3531303636313539; QUAD $0x0000000000353236  // .asciz 16, '95166015625\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000071d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000071e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000071f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007204 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007214 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0c, 0x00, 0x00, 0x00, //0x00007218 .long 12
	0x31, 0x38, 0x31, 0x38, 0x39, 0x38, 0x39, 0x34, 0x30, 0x33, 0x35, 0x34, 0x35, 0x38, 0x35, 0x36, //0x0000721c QUAD $0x3439383938313831; QUAD $0x3635383534353330  // .asciz 16, '1818989403545856'
	0x34, 0x37, 0x35, 0x38, 0x33, 0x30, 0x30, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x0000722c QUAD $0x3730303338353734; QUAD $0x0000000035323138  // .asciz 16, '475830078125\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000723c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000724c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000725c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000726c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000727c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x00007280 .long 13
	0x39, 0x30, 0x39, 0x34, 0x39, 0x34, 0x37, 0x30, 0x31, 0x37, 0x37, 0x32, 0x39, 0x32, 0x38, 0x32, //0x00007284 QUAD $0x3037343934393039; QUAD $0x3238323932373731  // .asciz 16, '9094947017729282'
	0x33, 0x37, 0x39, 0x31, 0x35, 0x30, 0x33, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x00007294 QUAD $0x3933303531393733; QUAD $0x0000000035323630  // .asciz 16, '379150390625\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000072a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000072b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000072c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000072d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000072e4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x000072e8 .long 13
	0x34, 0x35, 0x34, 0x37, 0x34, 0x37, 0x33, 0x35, 0x30, 0x38, 0x38, 0x36, 0x34, 0x36, 0x34, 0x31, //0x000072ec QUAD $0x3533373437343534; QUAD $0x3134363436383830  // .asciz 16, '4547473508864641'
	0x31, 0x38, 0x39, 0x35, 0x37, 0x35, 0x31, 0x39, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, //0x000072fc QUAD $0x3931353735393831; QUAD $0x0000003532313335  // .asciz 16, '1895751953125\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000730c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000731c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000732c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000733c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000734c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x00007350 .long 13
	0x32, 0x32, 0x37, 0x33, 0x37, 0x33, 0x36, 0x37, 0x35, 0x34, 0x34, 0x33, 0x32, 0x33, 0x32, 0x30, //0x00007354 QUAD $0x3736333733373232; QUAD $0x3032333233343435  // .asciz 16, '2273736754432320'
	0x35, 0x39, 0x34, 0x37, 0x38, 0x37, 0x35, 0x39, 0x37, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, //0x00007364 QUAD $0x3935373837343935; QUAD $0x0000353236353637  // .asciz 16, '59478759765625\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007374 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007384 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007394 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000073a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000073b4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x000073b8 .long 13
	0x31, 0x31, 0x33, 0x36, 0x38, 0x36, 0x38, 0x33, 0x37, 0x37, 0x32, 0x31, 0x36, 0x31, 0x36, 0x30, //0x000073bc QUAD $0x3338363836333131; QUAD $0x3036313631323737  // .asciz 16, '1136868377216160'
	0x32, 0x39, 0x37, 0x33, 0x39, 0x33, 0x37, 0x39, 0x38, 0x38, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, //0x000073cc QUAD $0x3937333933373932; QUAD $0x0035323138323838  // .asciz 16, '297393798828125\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000073dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000073ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000073fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000740c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000741c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0e, 0x00, 0x00, 0x00, //0x00007420 .long 14
	0x35, 0x36, 0x38, 0x34, 0x33, 0x34, 0x31, 0x38, 0x38, 0x36, 0x30, 0x38, 0x30, 0x38, 0x30, 0x31, //0x00007424 QUAD $0x3831343334383635; QUAD $0x3130383038303638  // .asciz 16, '5684341886080801'
	0x34, 0x38, 0x36, 0x39, 0x36, 0x38, 0x39, 0x39, 0x34, 0x31, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, //0x00007434 QUAD $0x3939383639363834; QUAD $0x0035323630343134  // .asciz 16, '486968994140625\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007444 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007454 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007464 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007474 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007484 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0e, 0x00, 0x00, 0x00, //0x00007488 .long 14
	0x32, 0x38, 0x34, 0x32, 0x31, 0x37, 0x30, 0x39, 0x34, 0x33, 0x30, 0x34, 0x30, 0x34, 0x30, 0x30, //0x0000748c QUAD $0x3930373132343832; QUAD $0x3030343034303334  // .asciz 16, '2842170943040400'
	0x37, 0x34, 0x33, 0x34, 0x38, 0x34, 0x34, 0x39, 0x37, 0x30, 0x37, 0x30, 0x33, 0x31, 0x32, 0x35, //0x0000749c QUAD $0x3934343834333437; QUAD $0x3532313330373037  // .asciz 16, '7434844970703125'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000074ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000074bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000074cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000074dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000074ec LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0e, 0x00, 0x00, 0x00, //0x000074f0 .long 14
	0x31, 0x34, 0x32, 0x31, 0x30, 0x38, 0x35, 0x34, 0x37, 0x31, 0x35, 0x32, 0x30, 0x32, 0x30, 0x30, //0x000074f4 QUAD $0x3435383031323431; QUAD $0x3030323032353137  // .asciz 16, '1421085471520200'
	0x33, 0x37, 0x31, 0x37, 0x34, 0x32, 0x32, 0x34, 0x38, 0x35, 0x33, 0x35, 0x31, 0x35, 0x36, 0x32, //0x00007504 QUAD $0x3432323437313733; QUAD $0x3236353135333538  // .asciz 16, '3717422485351562'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007514 QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007524 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007534 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007544 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007554 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0f, 0x00, 0x00, 0x00, //0x00007558 .long 15
	0x37, 0x31, 0x30, 0x35, 0x34, 0x32, 0x37, 0x33, 0x35, 0x37, 0x36, 0x30, 0x31, 0x30, 0x30, 0x31, //0x0000755c QUAD $0x3337323435303137; QUAD $0x3130303130363735  // .asciz 16, '7105427357601001'
	0x38, 0x35, 0x38, 0x37, 0x31, 0x31, 0x32, 0x34, 0x32, 0x36, 0x37, 0x35, 0x37, 0x38, 0x31, 0x32, //0x0000756c QUAD $0x3432313137383538; QUAD $0x3231383735373632  // .asciz 16, '8587112426757812'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000757c QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000758c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000759c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000075ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000075bc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0f, 0x00, 0x00, 0x00, //0x000075c0 .long 15
	0x33, 0x35, 0x35, 0x32, 0x37, 0x31, 0x33, 0x36, 0x37, 0x38, 0x38, 0x30, 0x30, 0x35, 0x30, 0x30, //0x000075c4 QUAD $0x3633313732353533; QUAD $0x3030353030383837  // .asciz 16, '3552713678800500'
	0x39, 0x32, 0x39, 0x33, 0x35, 0x35, 0x36, 0x32, 0x31, 0x33, 0x33, 0x37, 0x38, 0x39, 0x30, 0x36, //0x000075d4 QUAD $0x3236353533393239; QUAD $0x3630393837333331  // .asciz 16, '9293556213378906'
	0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000075e4 QUAD $0x0000000000003532; QUAD $0x0000000000000000  // .asciz 16, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000075f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007604 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007614 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007624 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0f, 0x00, 0x00, 0x00, //0x00007628 .long 15
	0x31, 0x37, 0x37, 0x36, 0x33, 0x35, 0x36, 0x38, 0x33, 0x39, 0x34, 0x30, 0x30, 0x32, 0x35, 0x30, //0x0000762c QUAD $0x3836353336373731; QUAD $0x3035323030343933  // .asciz 16, '1776356839400250'
	0x34, 0x36, 0x34, 0x36, 0x37, 0x37, 0x38, 0x31, 0x30, 0x36, 0x36, 0x38, 0x39, 0x34, 0x35, 0x33, //0x0000763c QUAD $0x3138373736343634; QUAD $0x3335343938363630  // .asciz 16, '4646778106689453'
	0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000764c QUAD $0x0000000000353231; QUAD $0x0000000000000000  // .asciz 16, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000765c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000766c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000767c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000768c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x00007690 .long 16
	0x38, 0x38, 0x38, 0x31, 0x37, 0x38, 0x34, 0x31, 0x39, 0x37, 0x30, 0x30, 0x31, 0x32, 0x35, 0x32, //0x00007694 QUAD $0x3134383731383838; QUAD $0x3235323130303739  // .asciz 16, '8881784197001252'
	0x33, 0x32, 0x33, 0x33, 0x38, 0x39, 0x30, 0x35, 0x33, 0x33, 0x34, 0x34, 0x37, 0x32, 0x36, 0x35, //0x000076a4 QUAD $0x3530393833333233; QUAD $0x3536323734343333  // .asciz 16, '3233890533447265'
	0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000076b4 QUAD $0x0000000000353236; QUAD $0x0000000000000000  // .asciz 16, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000076c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000076d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000076e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000076f4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x000076f8 .long 16
	0x34, 0x34, 0x34, 0x30, 0x38, 0x39, 0x32, 0x30, 0x39, 0x38, 0x35, 0x30, 0x30, 0x36, 0x32, 0x36, //0x000076fc QUAD $0x3032393830343434; QUAD $0x3632363030353839  // .asciz 16, '4440892098500626'
	0x31, 0x36, 0x31, 0x36, 0x39, 0x34, 0x35, 0x32, 0x36, 0x36, 0x37, 0x32, 0x33, 0x36, 0x33, 0x32, //0x0000770c QUAD $0x3235343936313631; QUAD $0x3233363332373636  // .asciz 16, '1616945266723632'
	0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000771c QUAD $0x0000000035323138; QUAD $0x0000000000000000  // .asciz 16, '8125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000772c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000773c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000774c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000775c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x00007760 .long 16
	0x32, 0x32, 0x32, 0x30, 0x34, 0x34, 0x36, 0x30, 0x34, 0x39, 0x32, 0x35, 0x30, 0x33, 0x31, 0x33, //0x00007764 QUAD $0x3036343430323232; QUAD $0x3331333035323934  // .asciz 16, '2220446049250313'
	0x30, 0x38, 0x30, 0x38, 0x34, 0x37, 0x32, 0x36, 0x33, 0x33, 0x33, 0x36, 0x31, 0x38, 0x31, 0x36, //0x00007774 QUAD $0x3632373438303830; QUAD $0x3631383136333333  // .asciz 16, '0808472633361816'
	0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007784 QUAD $0x0000003532363034; QUAD $0x0000000000000000  // .asciz 16, '40625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007794 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000077a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000077b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000077c4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x000077c8 .long 16
	0x31, 0x31, 0x31, 0x30, 0x32, 0x32, 0x33, 0x30, 0x32, 0x34, 0x36, 0x32, 0x35, 0x31, 0x35, 0x36, //0x000077cc QUAD $0x3033323230313131; QUAD $0x3635313532363432  // .asciz 16, '1110223024625156'
	0x35, 0x34, 0x30, 0x34, 0x32, 0x33, 0x36, 0x33, 0x31, 0x36, 0x36, 0x38, 0x30, 0x39, 0x30, 0x38, //0x000077dc QUAD $0x3336333234303435; QUAD $0x3830393038363631  // .asciz 16, '5404236316680908'
	0x32, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000077ec QUAD $0x0000353231333032; QUAD $0x0000000000000000  // .asciz 16, '203125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000077fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000780c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000781c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000782c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x11, 0x00, 0x00, 0x00, //0x00007830 .long 17
	0x35, 0x35, 0x35, 0x31, 0x31, 0x31, 0x35, 0x31, 0x32, 0x33, 0x31, 0x32, 0x35, 0x37, 0x38, 0x32, //0x00007834 QUAD $0x3135313131353535; QUAD $0x3238373532313332  // .asciz 16, '5551115123125782'
	0x37, 0x30, 0x32, 0x31, 0x31, 0x38, 0x31, 0x35, 0x38, 0x33, 0x34, 0x30, 0x34, 0x35, 0x34, 0x31, //0x00007844 QUAD $0x3531383131323037; QUAD $0x3134353430343338  // .asciz 16, '7021181583404541'
	0x30, 0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007854 QUAD $0x0000353236353130; QUAD $0x0000000000000000  // .asciz 16, '015625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007864 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007874 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007884 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007894 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x11, 0x00, 0x00, 0x00, //0x00007898 .long 17
	0x32, 0x37, 0x37, 0x35, 0x35, 0x35, 0x37, 0x35, 0x36, 0x31, 0x35, 0x36, 0x32, 0x38, 0x39, 0x31, //0x0000789c QUAD $0x3537353535373732; QUAD $0x3139383236353136  // .asciz 16, '2775557561562891'
	0x33, 0x35, 0x31, 0x30, 0x35, 0x39, 0x30, 0x37, 0x39, 0x31, 0x37, 0x30, 0x32, 0x32, 0x37, 0x30, //0x000078ac QUAD $0x3730393530313533; QUAD $0x3037323230373139  // .asciz 16, '3510590791702270'
	0x35, 0x30, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000078bc QUAD $0x0035323138373035; QUAD $0x0000000000000000  // .asciz 16, '5078125\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000078cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000078dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000078ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000078fc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x11, 0x00, 0x00, 0x00, //0x00007900 .long 17
	0x31, 0x33, 0x38, 0x37, 0x37, 0x37, 0x38, 0x37, 0x38, 0x30, 0x37, 0x38, 0x31, 0x34, 0x34, 0x35, //0x00007904 QUAD $0x3738373737383331; QUAD $0x3534343138373038  // .asciz 16, '1387778780781445'
	0x36, 0x37, 0x35, 0x35, 0x32, 0x39, 0x35, 0x33, 0x39, 0x35, 0x38, 0x35, 0x31, 0x31, 0x33, 0x35, //0x00007914 QUAD $0x3335393235353736; QUAD $0x3533313135383539  // .asciz 16, '6755295395851135'
	0x32, 0x35, 0x33, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007924 QUAD $0x3532363039333532; QUAD $0x0000000000000000  // .asciz 16, '25390625\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007934 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007944 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007954 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007964 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x12, 0x00, 0x00, 0x00, //0x00007968 .long 18
	0x36, 0x39, 0x33, 0x38, 0x38, 0x39, 0x33, 0x39, 0x30, 0x33, 0x39, 0x30, 0x37, 0x32, 0x32, 0x38, //0x0000796c QUAD $0x3933393838333936; QUAD $0x3832323730393330  // .asciz 16, '6938893903907228'
	0x33, 0x37, 0x37, 0x36, 0x34, 0x37, 0x36, 0x39, 0x37, 0x39, 0x32, 0x35, 0x35, 0x36, 0x37, 0x36, //0x0000797c QUAD $0x3936373436373733; QUAD $0x3637363535323937  // .asciz 16, '3776476979255676'
	0x32, 0x36, 0x39, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000798c QUAD $0x3532313335393632; QUAD $0x0000000000000000  // .asciz 16, '26953125\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000799c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000079ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000079bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000079cc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x12, 0x00, 0x00, 0x00, //0x000079d0 .long 18
	0x33, 0x34, 0x36, 0x39, 0x34, 0x34, 0x36, 0x39, 0x35, 0x31, 0x39, 0x35, 0x33, 0x36, 0x31, 0x34, //0x000079d4 QUAD $0x3936343439363433; QUAD $0x3431363335393135  // .asciz 16, '3469446951953614'
	0x31, 0x38, 0x38, 0x38, 0x32, 0x33, 0x38, 0x34, 0x38, 0x39, 0x36, 0x32, 0x37, 0x38, 0x33, 0x38, //0x000079e4 QUAD $0x3438333238383831; QUAD $0x3833383732363938  // .asciz 16, '1888238489627838'
	0x31, 0x33, 0x34, 0x37, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000079f4 QUAD $0x3236353637343331; QUAD $0x0000000000000035  // .asciz 16, '134765625\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007a04 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007a14 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007a24 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007a34 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x12, 0x00, 0x00, 0x00, //0x00007a38 .long 18
	0x31, 0x37, 0x33, 0x34, 0x37, 0x32, 0x33, 0x34, 0x37, 0x35, 0x39, 0x37, 0x36, 0x38, 0x30, 0x37, //0x00007a3c QUAD $0x3433323734333731; QUAD $0x3730383637393537  // .asciz 16, '1734723475976807'
	0x30, 0x39, 0x34, 0x34, 0x31, 0x31, 0x39, 0x32, 0x34, 0x34, 0x38, 0x31, 0x33, 0x39, 0x31, 0x39, //0x00007a4c QUAD $0x3239313134343930; QUAD $0x3931393331383434  // .asciz 16, '0944119244813919'
	0x30, 0x36, 0x37, 0x33, 0x38, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007a5c QUAD $0x3138323833373630; QUAD $0x0000000000003532  // .asciz 16, '0673828125\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007a6c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007a7c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007a8c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007a9c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x13, 0x00, 0x00, 0x00, //0x00007aa0 .long 19
	0x38, 0x36, 0x37, 0x33, 0x36, 0x31, 0x37, 0x33, 0x37, 0x39, 0x38, 0x38, 0x34, 0x30, 0x33, 0x35, //0x00007aa4 QUAD $0x3337313633373638; QUAD $0x3533303438383937  // .asciz 16, '8673617379884035'
	0x34, 0x37, 0x32, 0x30, 0x35, 0x39, 0x36, 0x32, 0x32, 0x34, 0x30, 0x36, 0x39, 0x35, 0x39, 0x35, //0x00007ab4 QUAD $0x3236393530323734; QUAD $0x3539353936303432  // .asciz 16, '4720596224069595'
	0x33, 0x33, 0x36, 0x39, 0x31, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007ac4 QUAD $0x3630343139363333; QUAD $0x0000000000003532  // .asciz 16, '3369140625\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007ad4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007ae4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007af4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007b04 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
}
 
