// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__unquote = 16
)

const (
    _stack__unquote = 88
)

const (
    _size__unquote = 2272
)

var (
    _pcsp__unquote = [][2]uint32{
        {1, 0},
        {4, 8},
        {6, 16},
        {8, 24},
        {10, 32},
        {12, 40},
        {13, 48},
        {1684, 88},
        {1688, 48},
        {1689, 40},
        {1691, 32},
        {1693, 24},
        {1695, 16},
        {1697, 8},
        {1698, 0},
        {2270, 88},
    }
)

var _cfunc_unquote = []loader.CFunc{
    {"_unquote_entry", 0,  _entry__unquote, 0, nil},
    {"_unquote", _entry__unquote, _size__unquote, _stack__unquote, _pcsp__unquote},
}
