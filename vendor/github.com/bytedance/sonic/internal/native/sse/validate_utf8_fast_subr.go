// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__validate_utf8_fast = 0
)

const (
    _stack__validate_utf8_fast = 24
)

const (
    _size__validate_utf8_fast = 536
)

var (
    _pcsp__validate_utf8_fast = [][2]uint32{
        {1, 0},
        {4, 8},
        {5, 16},
        {247, 24},
        {251, 16},
        {252, 8},
        {253, 0},
        {527, 24},
        {531, 16},
        {532, 8},
        {534, 0},
    }
)

var _cfunc_validate_utf8_fast = []loader.CFunc{
    {"_validate_utf8_fast_entry", 0,  _entry__validate_utf8_fast, 0, nil},
    {"_validate_utf8_fast", _entry__validate_utf8_fast, _size__validate_utf8_fast, _stack__validate_utf8_fast, _pcsp__validate_utf8_fast},
}
