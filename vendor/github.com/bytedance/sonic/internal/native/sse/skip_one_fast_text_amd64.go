// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package sse

var _text_skip_one_fast = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, // QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	//0x00000010 LCPI0_1
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000010 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000020 LCPI0_2
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x00000020 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	//0x00000030 LCPI0_3
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000030 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x00000040 LCPI0_4
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000040 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000050 LCPI0_5
	0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, //0x00000050 QUAD $0x7b7b7b7b7b7b7b7b; QUAD $0x7b7b7b7b7b7b7b7b  // .space 16, '{{{{{{{{{{{{{{{{'
	//0x00000060 LCPI0_6
	0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, //0x00000060 QUAD $0x5b5b5b5b5b5b5b5b; QUAD $0x5b5b5b5b5b5b5b5b  // .space 16, '[[[[[[[[[[[[[[[['
	//0x00000070 LCPI0_7
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000070 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	//0x00000080 .p2align 4, 0x90
	//0x00000080 _skip_one_fast
	0x55, //0x00000080 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000081 movq         %rsp, %rbp
	0x41, 0x57, //0x00000084 pushq        %r15
	0x41, 0x56, //0x00000086 pushq        %r14
	0x41, 0x55, //0x00000088 pushq        %r13
	0x41, 0x54, //0x0000008a pushq        %r12
	0x53, //0x0000008c pushq        %rbx
	0x48, 0x83, 0xec, 0x58, //0x0000008d subq         $88, %rsp
	0x4c, 0x8b, 0x07, //0x00000091 movq         (%rdi), %r8
	0x4c, 0x8b, 0x4f, 0x08, //0x00000094 movq         $8(%rdi), %r9
	0x48, 0x8b, 0x16, //0x00000098 movq         (%rsi), %rdx
	0x48, 0x89, 0xd0, //0x0000009b movq         %rdx, %rax
	0x4c, 0x29, 0xc8, //0x0000009e subq         %r9, %rax
	0x0f, 0x83, 0x2a, 0x00, 0x00, 0x00, //0x000000a1 jae          LBB0_5
	0x41, 0x8a, 0x0c, 0x10, //0x000000a7 movb         (%r8,%rdx), %cl
	0x80, 0xf9, 0x0d, //0x000000ab cmpb         $13, %cl
	0x0f, 0x84, 0x1d, 0x00, 0x00, 0x00, //0x000000ae je           LBB0_5
	0x80, 0xf9, 0x20, //0x000000b4 cmpb         $32, %cl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x000000b7 je           LBB0_5
	0x80, 0xc1, 0xf7, //0x000000bd addb         $-9, %cl
	0x80, 0xf9, 0x01, //0x000000c0 cmpb         $1, %cl
	0x0f, 0x86, 0x08, 0x00, 0x00, 0x00, //0x000000c3 jbe          LBB0_5
	0x49, 0x89, 0xd6, //0x000000c9 movq         %rdx, %r14
	0xe9, 0x2e, 0x01, 0x00, 0x00, //0x000000cc jmp          LBB0_27
	//0x000000d1 LBB0_5
	0x4c, 0x8d, 0x72, 0x01, //0x000000d1 leaq         $1(%rdx), %r14
	0x4d, 0x39, 0xce, //0x000000d5 cmpq         %r9, %r14
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x000000d8 jae          LBB0_9
	0x43, 0x8a, 0x0c, 0x30, //0x000000de movb         (%r8,%r14), %cl
	0x80, 0xf9, 0x0d, //0x000000e2 cmpb         $13, %cl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x000000e5 je           LBB0_9
	0x80, 0xf9, 0x20, //0x000000eb cmpb         $32, %cl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x000000ee je           LBB0_9
	0x80, 0xc1, 0xf7, //0x000000f4 addb         $-9, %cl
	0x80, 0xf9, 0x01, //0x000000f7 cmpb         $1, %cl
	0x0f, 0x87, 0xff, 0x00, 0x00, 0x00, //0x000000fa ja           LBB0_27
	//0x00000100 LBB0_9
	0x4c, 0x8d, 0x72, 0x02, //0x00000100 leaq         $2(%rdx), %r14
	0x4d, 0x39, 0xce, //0x00000104 cmpq         %r9, %r14
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x00000107 jae          LBB0_13
	0x43, 0x8a, 0x0c, 0x30, //0x0000010d movb         (%r8,%r14), %cl
	0x80, 0xf9, 0x0d, //0x00000111 cmpb         $13, %cl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000114 je           LBB0_13
	0x80, 0xf9, 0x20, //0x0000011a cmpb         $32, %cl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000011d je           LBB0_13
	0x80, 0xc1, 0xf7, //0x00000123 addb         $-9, %cl
	0x80, 0xf9, 0x01, //0x00000126 cmpb         $1, %cl
	0x0f, 0x87, 0xd0, 0x00, 0x00, 0x00, //0x00000129 ja           LBB0_27
	//0x0000012f LBB0_13
	0x4c, 0x8d, 0x72, 0x03, //0x0000012f leaq         $3(%rdx), %r14
	0x4d, 0x39, 0xce, //0x00000133 cmpq         %r9, %r14
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x00000136 jae          LBB0_17
	0x43, 0x8a, 0x0c, 0x30, //0x0000013c movb         (%r8,%r14), %cl
	0x80, 0xf9, 0x0d, //0x00000140 cmpb         $13, %cl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000143 je           LBB0_17
	0x80, 0xf9, 0x20, //0x00000149 cmpb         $32, %cl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000014c je           LBB0_17
	0x80, 0xc1, 0xf7, //0x00000152 addb         $-9, %cl
	0x80, 0xf9, 0x01, //0x00000155 cmpb         $1, %cl
	0x0f, 0x87, 0xa1, 0x00, 0x00, 0x00, //0x00000158 ja           LBB0_27
	//0x0000015e LBB0_17
	0x48, 0x8d, 0x4a, 0x04, //0x0000015e leaq         $4(%rdx), %rcx
	0x49, 0x39, 0xc9, //0x00000162 cmpq         %rcx, %r9
	0x0f, 0x86, 0x4e, 0x00, 0x00, 0x00, //0x00000165 jbe          LBB0_23
	0x49, 0x39, 0xc9, //0x0000016b cmpq         %rcx, %r9
	0x0f, 0x84, 0x54, 0x00, 0x00, 0x00, //0x0000016e je           LBB0_24
	0x4b, 0x8d, 0x0c, 0x08, //0x00000174 leaq         (%r8,%r9), %rcx
	0x48, 0x83, 0xc0, 0x04, //0x00000178 addq         $4, %rax
	0x4e, 0x8d, 0x74, 0x02, 0x05, //0x0000017c leaq         $5(%rdx,%r8), %r14
	0x48, 0xba, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00000181 movabsq      $4294977024, %rdx
	0x90, 0x90, 0x90, 0x90, 0x90, //0x0000018b .p2align 4, 0x90
	//0x00000190 LBB0_20
	0x41, 0x0f, 0xbe, 0x5e, 0xff, //0x00000190 movsbl       $-1(%r14), %ebx
	0x83, 0xfb, 0x20, //0x00000195 cmpl         $32, %ebx
	0x0f, 0x87, 0x48, 0x00, 0x00, 0x00, //0x00000198 ja           LBB0_26
	0x48, 0x0f, 0xa3, 0xda, //0x0000019e btq          %rbx, %rdx
	0x0f, 0x83, 0x3e, 0x00, 0x00, 0x00, //0x000001a2 jae          LBB0_26
	0x49, 0xff, 0xc6, //0x000001a8 incq         %r14
	0x48, 0xff, 0xc0, //0x000001ab incq         %rax
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x000001ae jne          LBB0_20
	0xe9, 0x12, 0x00, 0x00, 0x00, //0x000001b4 jmp          LBB0_25
	//0x000001b9 LBB0_23
	0x48, 0x89, 0x0e, //0x000001b9 movq         %rcx, (%rsi)
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000001bc movq         $-1, %rax
	0xe9, 0x4a, 0x01, 0x00, 0x00, //0x000001c3 jmp          LBB0_45
	//0x000001c8 LBB0_24
	0x4c, 0x01, 0xc1, //0x000001c8 addq         %r8, %rcx
	//0x000001cb LBB0_25
	0x4c, 0x29, 0xc1, //0x000001cb subq         %r8, %rcx
	0x49, 0x89, 0xce, //0x000001ce movq         %rcx, %r14
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000001d1 movq         $-1, %rax
	0x4d, 0x39, 0xce, //0x000001d8 cmpq         %r9, %r14
	0x0f, 0x82, 0x1e, 0x00, 0x00, 0x00, //0x000001db jb           LBB0_27
	0xe9, 0x2c, 0x01, 0x00, 0x00, //0x000001e1 jmp          LBB0_45
	//0x000001e6 LBB0_26
	0x4c, 0x89, 0xc0, //0x000001e6 movq         %r8, %rax
	0x48, 0xf7, 0xd0, //0x000001e9 notq         %rax
	0x49, 0x01, 0xc6, //0x000001ec addq         %rax, %r14
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000001ef movq         $-1, %rax
	0x4d, 0x39, 0xce, //0x000001f6 cmpq         %r9, %r14
	0x0f, 0x83, 0x13, 0x01, 0x00, 0x00, //0x000001f9 jae          LBB0_45
	//0x000001ff LBB0_27
	0x49, 0x8d, 0x5e, 0x01, //0x000001ff leaq         $1(%r14), %rbx
	0x48, 0x89, 0x1e, //0x00000203 movq         %rbx, (%rsi)
	0x43, 0x0f, 0xbe, 0x0c, 0x30, //0x00000206 movsbl       (%r8,%r14), %ecx
	0x83, 0xf9, 0x7b, //0x0000020b cmpl         $123, %ecx
	0x0f, 0x87, 0x20, 0x01, 0x00, 0x00, //0x0000020e ja           LBB0_47
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000214 movq         $-1, %rax
	0x48, 0x8d, 0x15, 0xaa, 0x0b, 0x00, 0x00, //0x0000021b leaq         $2986(%rip), %rdx  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x0c, 0x8a, //0x00000222 movslq       (%rdx,%rcx,4), %rcx
	0x48, 0x01, 0xd1, //0x00000226 addq         %rdx, %rcx
	0xff, 0xe1, //0x00000229 jmpq         *%rcx
	//0x0000022b LBB0_29
	0x48, 0x8b, 0x4f, 0x08, //0x0000022b movq         $8(%rdi), %rcx
	0x48, 0x89, 0xc8, //0x0000022f movq         %rcx, %rax
	0x48, 0x29, 0xd8, //0x00000232 subq         %rbx, %rax
	0x4c, 0x01, 0xc3, //0x00000235 addq         %r8, %rbx
	0x48, 0x83, 0xf8, 0x10, //0x00000238 cmpq         $16, %rax
	0x0f, 0x82, 0x7b, 0x00, 0x00, 0x00, //0x0000023c jb           LBB0_34
	0x4c, 0x29, 0xf1, //0x00000242 subq         %r14, %rcx
	0x48, 0x83, 0xc1, 0xef, //0x00000245 addq         $-17, %rcx
	0x48, 0x89, 0xca, //0x00000249 movq         %rcx, %rdx
	0x48, 0x83, 0xe2, 0xf0, //0x0000024c andq         $-16, %rdx
	0x4c, 0x01, 0xf2, //0x00000250 addq         %r14, %rdx
	0x49, 0x8d, 0x54, 0x10, 0x11, //0x00000253 leaq         $17(%r8,%rdx), %rdx
	0x83, 0xe1, 0x0f, //0x00000258 andl         $15, %ecx
	0xf3, 0x0f, 0x6f, 0x05, 0x9d, 0xfd, 0xff, 0xff, //0x0000025b movdqu       $-611(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0xa5, 0xfd, 0xff, 0xff, //0x00000263 movdqu       $-603(%rip), %xmm1  /* LCPI0_1+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x15, 0xad, 0xfd, 0xff, 0xff, //0x0000026b movdqu       $-595(%rip), %xmm2  /* LCPI0_2+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000273 .p2align 4, 0x90
	//0x00000280 LBB0_31
	0xf3, 0x0f, 0x6f, 0x1b, //0x00000280 movdqu       (%rbx), %xmm3
	0x66, 0x0f, 0x6f, 0xe3, //0x00000284 movdqa       %xmm3, %xmm4
	0x66, 0x0f, 0x74, 0xe0, //0x00000288 pcmpeqb      %xmm0, %xmm4
	0x66, 0x0f, 0xeb, 0xd9, //0x0000028c por          %xmm1, %xmm3
	0x66, 0x0f, 0x74, 0xda, //0x00000290 pcmpeqb      %xmm2, %xmm3
	0x66, 0x0f, 0xeb, 0xdc, //0x00000294 por          %xmm4, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x00000298 pmovmskb     %xmm3, %edi
	0x66, 0x85, 0xff, //0x0000029c testw        %di, %di
	0x0f, 0x85, 0x5a, 0x00, 0x00, 0x00, //0x0000029f jne          LBB0_42
	0x48, 0x83, 0xc3, 0x10, //0x000002a5 addq         $16, %rbx
	0x48, 0x83, 0xc0, 0xf0, //0x000002a9 addq         $-16, %rax
	0x48, 0x83, 0xf8, 0x0f, //0x000002ad cmpq         $15, %rax
	0x0f, 0x87, 0xc9, 0xff, 0xff, 0xff, //0x000002b1 ja           LBB0_31
	0x48, 0x89, 0xc8, //0x000002b7 movq         %rcx, %rax
	0x48, 0x89, 0xd3, //0x000002ba movq         %rdx, %rbx
	//0x000002bd LBB0_34
	0x48, 0x85, 0xc0, //0x000002bd testq        %rax, %rax
	0x0f, 0x84, 0x31, 0x00, 0x00, 0x00, //0x000002c0 je           LBB0_41
	0x48, 0x8d, 0x0c, 0x03, //0x000002c6 leaq         (%rbx,%rax), %rcx
	//0x000002ca LBB0_36
	0x0f, 0xb6, 0x13, //0x000002ca movzbl       (%rbx), %edx
	0x80, 0xfa, 0x2c, //0x000002cd cmpb         $44, %dl
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x000002d0 je           LBB0_41
	0x80, 0xfa, 0x7d, //0x000002d6 cmpb         $125, %dl
	0x0f, 0x84, 0x18, 0x00, 0x00, 0x00, //0x000002d9 je           LBB0_41
	0x80, 0xfa, 0x5d, //0x000002df cmpb         $93, %dl
	0x0f, 0x84, 0x0f, 0x00, 0x00, 0x00, //0x000002e2 je           LBB0_41
	0x48, 0xff, 0xc3, //0x000002e8 incq         %rbx
	0x48, 0xff, 0xc8, //0x000002eb decq         %rax
	0x0f, 0x85, 0xd6, 0xff, 0xff, 0xff, //0x000002ee jne          LBB0_36
	0x48, 0x89, 0xcb, //0x000002f4 movq         %rcx, %rbx
	//0x000002f7 LBB0_41
	0x4c, 0x29, 0xc3, //0x000002f7 subq         %r8, %rbx
	0xe9, 0x0d, 0x00, 0x00, 0x00, //0x000002fa jmp          LBB0_43
	//0x000002ff LBB0_42
	0x0f, 0xb7, 0xc7, //0x000002ff movzwl       %di, %eax
	0x48, 0x0f, 0xbc, 0xc0, //0x00000302 bsfq         %rax, %rax
	0x4c, 0x29, 0xc3, //0x00000306 subq         %r8, %rbx
	0x48, 0x01, 0xc3, //0x00000309 addq         %rax, %rbx
	//0x0000030c LBB0_43
	0x48, 0x89, 0x1e, //0x0000030c movq         %rbx, (%rsi)
	//0x0000030f LBB0_44
	0x4c, 0x89, 0xf0, //0x0000030f movq         %r14, %rax
	//0x00000312 LBB0_45
	0x48, 0x83, 0xc4, 0x58, //0x00000312 addq         $88, %rsp
	0x5b, //0x00000316 popq         %rbx
	0x41, 0x5c, //0x00000317 popq         %r12
	0x41, 0x5d, //0x00000319 popq         %r13
	0x41, 0x5e, //0x0000031b popq         %r14
	0x41, 0x5f, //0x0000031d popq         %r15
	0x5d, //0x0000031f popq         %rbp
	0xc3, //0x00000320 retq         
	//0x00000321 LBB0_46
	0x49, 0x8d, 0x4e, 0x04, //0x00000321 leaq         $4(%r14), %rcx
	0x48, 0x3b, 0x4f, 0x08, //0x00000325 cmpq         $8(%rdi), %rcx
	0x0f, 0x87, 0xe3, 0xff, 0xff, 0xff, //0x00000329 ja           LBB0_45
	0xe9, 0x6a, 0x05, 0x00, 0x00, //0x0000032f jmp          LBB0_83
	//0x00000334 LBB0_47
	0x4c, 0x89, 0x36, //0x00000334 movq         %r14, (%rsi)
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000337 movq         $-2, %rax
	0xe9, 0xcf, 0xff, 0xff, 0xff, //0x0000033e jmp          LBB0_45
	//0x00000343 LBB0_48
	0x4c, 0x8b, 0x4f, 0x08, //0x00000343 movq         $8(%rdi), %r9
	0x4d, 0x89, 0xcf, //0x00000347 movq         %r9, %r15
	0x49, 0x29, 0xdf, //0x0000034a subq         %rbx, %r15
	0x49, 0x83, 0xff, 0x20, //0x0000034d cmpq         $32, %r15
	0x0f, 0x8c, 0x3e, 0x0a, 0x00, 0x00, //0x00000351 jl           LBB0_117
	0x41, 0xba, 0xff, 0xff, 0xff, 0xff, //0x00000357 movl         $4294967295, %r10d
	0x4f, 0x8d, 0x1c, 0x30, //0x0000035d leaq         (%r8,%r14), %r11
	0x4d, 0x29, 0xf1, //0x00000361 subq         %r14, %r9
	0x41, 0xbd, 0x1f, 0x00, 0x00, 0x00, //0x00000364 movl         $31, %r13d
	0x45, 0x31, 0xff, //0x0000036a xorl         %r15d, %r15d
	0xf3, 0x0f, 0x6f, 0x05, 0xbb, 0xfc, 0xff, 0xff, //0x0000036d movdqu       $-837(%rip), %xmm0  /* LCPI0_3+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0xc3, 0xfc, 0xff, 0xff, //0x00000375 movdqu       $-829(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0x45, 0x31, 0xe4, //0x0000037d xorl         %r12d, %r12d
	0xe9, 0x2d, 0x00, 0x00, 0x00, //0x00000380 jmp          LBB0_50
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000385 .p2align 4, 0x90
	//0x00000390 LBB0_52
	0x45, 0x31, 0xe4, //0x00000390 xorl         %r12d, %r12d
	0x85, 0xc9, //0x00000393 testl        %ecx, %ecx
	0x0f, 0x85, 0xa6, 0x00, 0x00, 0x00, //0x00000395 jne          LBB0_110
	//0x0000039b LBB0_53
	0x49, 0x83, 0xc7, 0x20, //0x0000039b addq         $32, %r15
	0x4b, 0x8d, 0x4c, 0x29, 0xe0, //0x0000039f leaq         $-32(%r9,%r13), %rcx
	0x49, 0x83, 0xc5, 0xe0, //0x000003a4 addq         $-32, %r13
	0x48, 0x83, 0xf9, 0x3f, //0x000003a8 cmpq         $63, %rcx
	0x0f, 0x8e, 0x60, 0x09, 0x00, 0x00, //0x000003ac jle          LBB0_54
	//0x000003b2 LBB0_50
	0xf3, 0x43, 0x0f, 0x6f, 0x54, 0x3b, 0x01, //0x000003b2 movdqu       $1(%r11,%r15), %xmm2
	0xf3, 0x43, 0x0f, 0x6f, 0x5c, 0x3b, 0x11, //0x000003b9 movdqu       $17(%r11,%r15), %xmm3
	0x66, 0x0f, 0x6f, 0xe2, //0x000003c0 movdqa       %xmm2, %xmm4
	0x66, 0x0f, 0x74, 0xe0, //0x000003c4 pcmpeqb      %xmm0, %xmm4
	0x66, 0x0f, 0xd7, 0xfc, //0x000003c8 pmovmskb     %xmm4, %edi
	0x66, 0x0f, 0x6f, 0xe3, //0x000003cc movdqa       %xmm3, %xmm4
	0x66, 0x0f, 0x74, 0xe0, //0x000003d0 pcmpeqb      %xmm0, %xmm4
	0x66, 0x0f, 0xd7, 0xcc, //0x000003d4 pmovmskb     %xmm4, %ecx
	0x48, 0xc1, 0xe1, 0x10, //0x000003d8 shlq         $16, %rcx
	0x48, 0x09, 0xf9, //0x000003dc orq          %rdi, %rcx
	0x66, 0x0f, 0x74, 0xd1, //0x000003df pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xda, //0x000003e3 pmovmskb     %xmm2, %ebx
	0x66, 0x0f, 0x74, 0xd9, //0x000003e7 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x000003eb pmovmskb     %xmm3, %edi
	0x48, 0xc1, 0xe7, 0x10, //0x000003ef shlq         $16, %rdi
	0x48, 0x09, 0xdf, //0x000003f3 orq          %rbx, %rdi
	0x48, 0x89, 0xfb, //0x000003f6 movq         %rdi, %rbx
	0x4c, 0x09, 0xe3, //0x000003f9 orq          %r12, %rbx
	0x0f, 0x84, 0x8e, 0xff, 0xff, 0xff, //0x000003fc je           LBB0_52
	0x44, 0x89, 0xe3, //0x00000402 movl         %r12d, %ebx
	0x44, 0x31, 0xd3, //0x00000405 xorl         %r10d, %ebx
	0x21, 0xdf, //0x00000408 andl         %ebx, %edi
	0x8d, 0x1c, 0x3f, //0x0000040a leal         (%rdi,%rdi), %ebx
	0x44, 0x09, 0xe3, //0x0000040d orl          %r12d, %ebx
	0x41, 0x8d, 0x92, 0xab, 0xaa, 0xaa, 0xaa, //0x00000410 leal         $-1431655765(%r10), %edx
	0x31, 0xda, //0x00000417 xorl         %ebx, %edx
	0x21, 0xfa, //0x00000419 andl         %edi, %edx
	0x81, 0xe2, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000041b andl         $-1431655766, %edx
	0x45, 0x31, 0xe4, //0x00000421 xorl         %r12d, %r12d
	0x01, 0xfa, //0x00000424 addl         %edi, %edx
	0x41, 0x0f, 0x92, 0xc4, //0x00000426 setb         %r12b
	0x01, 0xd2, //0x0000042a addl         %edx, %edx
	0x81, 0xf2, 0x55, 0x55, 0x55, 0x55, //0x0000042c xorl         $1431655765, %edx
	0x21, 0xda, //0x00000432 andl         %ebx, %edx
	0x44, 0x31, 0xd2, //0x00000434 xorl         %r10d, %edx
	0x21, 0xd1, //0x00000437 andl         %edx, %ecx
	0x85, 0xc9, //0x00000439 testl        %ecx, %ecx
	0x0f, 0x84, 0x5a, 0xff, 0xff, 0xff, //0x0000043b je           LBB0_53
	//0x00000441 LBB0_110
	0x48, 0x0f, 0xbc, 0xc1, //0x00000441 bsfq         %rcx, %rax
	0x49, 0x01, 0xc3, //0x00000445 addq         %rax, %r11
	0x4d, 0x01, 0xfb, //0x00000448 addq         %r15, %r11
	0x4d, 0x29, 0xc3, //0x0000044b subq         %r8, %r11
	0x49, 0x83, 0xc3, 0x02, //0x0000044e addq         $2, %r11
	0x4c, 0x89, 0x1e, //0x00000452 movq         %r11, (%rsi)
	0xe9, 0xb5, 0xfe, 0xff, 0xff, //0x00000455 jmp          LBB0_44
	//0x0000045a LBB0_57
	0x49, 0xbd, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000045a movabsq      $6148914691236517205, %r13
	0x48, 0x8b, 0x4f, 0x08, //0x00000464 movq         $8(%rdi), %rcx
	0x48, 0x29, 0xd9, //0x00000468 subq         %rbx, %rcx
	0x49, 0x01, 0xd8, //0x0000046b addq         %rbx, %r8
	0x45, 0x31, 0xff, //0x0000046e xorl         %r15d, %r15d
	0xf3, 0x44, 0x0f, 0x6f, 0x15, 0xb6, 0xfb, 0xff, 0xff, //0x00000471 movdqu       $-1098(%rip), %xmm10  /* LCPI0_3+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0xbe, 0xfb, 0xff, 0xff, //0x0000047a movdqu       $-1090(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0x66, 0x45, 0x0f, 0x76, 0xc9, //0x00000482 pcmpeqd      %xmm9, %xmm9
	0xf3, 0x0f, 0x6f, 0x1d, 0xd1, 0xfb, 0xff, 0xff, //0x00000487 movdqu       $-1071(%rip), %xmm3  /* LCPI0_6+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x25, 0xd9, 0xfb, 0xff, 0xff, //0x0000048f movdqu       $-1063(%rip), %xmm4  /* LCPI0_7+0(%rip) */
	0x45, 0x0f, 0x57, 0xc0, //0x00000497 xorps        %xmm8, %xmm8
	0x45, 0x31, 0xd2, //0x0000049b xorl         %r10d, %r10d
	0x31, 0xd2, //0x0000049e xorl         %edx, %edx
	0x48, 0x89, 0x55, 0xc0, //0x000004a0 movq         %rdx, $-64(%rbp)
	0x45, 0x31, 0xdb, //0x000004a4 xorl         %r11d, %r11d
	0xe9, 0x60, 0x00, 0x00, 0x00, //0x000004a7 jmp          LBB0_59
	//0x000004ac LBB0_58
	0x49, 0xc1, 0xf9, 0x3f, //0x000004ac sarq         $63, %r9
	0x4c, 0x89, 0xf9, //0x000004b0 movq         %r15, %rcx
	0x48, 0xd1, 0xe9, //0x000004b3 shrq         %rcx
	0x4c, 0x21, 0xe9, //0x000004b6 andq         %r13, %rcx
	0x49, 0x29, 0xcf, //0x000004b9 subq         %rcx, %r15
	0x4c, 0x89, 0xf9, //0x000004bc movq         %r15, %rcx
	0x4c, 0x21, 0xd1, //0x000004bf andq         %r10, %rcx
	0x49, 0xc1, 0xef, 0x02, //0x000004c2 shrq         $2, %r15
	0x4d, 0x21, 0xd7, //0x000004c6 andq         %r10, %r15
	0x49, 0x01, 0xcf, //0x000004c9 addq         %rcx, %r15
	0x4c, 0x89, 0xf9, //0x000004cc movq         %r15, %rcx
	0x48, 0xc1, 0xe9, 0x04, //0x000004cf shrq         $4, %rcx
	0x4c, 0x01, 0xf9, //0x000004d3 addq         %r15, %rcx
	0x48, 0xba, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x000004d6 movabsq      $1085102592571150095, %rdx
	0x48, 0x21, 0xd1, //0x000004e0 andq         %rdx, %rcx
	0x48, 0xba, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x000004e3 movabsq      $72340172838076673, %rdx
	0x48, 0x0f, 0xaf, 0xca, //0x000004ed imulq        %rdx, %rcx
	0x48, 0xc1, 0xe9, 0x38, //0x000004f1 shrq         $56, %rcx
	0x48, 0x01, 0x4d, 0xc0, //0x000004f5 addq         %rcx, $-64(%rbp)
	0x49, 0x83, 0xc0, 0x40, //0x000004f9 addq         $64, %r8
	0x48, 0x8b, 0x4d, 0xd0, //0x000004fd movq         $-48(%rbp), %rcx
	0x48, 0x83, 0xc1, 0xc0, //0x00000501 addq         $-64, %rcx
	0x4d, 0x89, 0xcf, //0x00000505 movq         %r9, %r15
	0x4c, 0x8b, 0x55, 0xc8, //0x00000508 movq         $-56(%rbp), %r10
	//0x0000050c LBB0_59
	0x48, 0x83, 0xf9, 0x40, //0x0000050c cmpq         $64, %rcx
	0x48, 0x89, 0x4d, 0xd0, //0x00000510 movq         %rcx, $-48(%rbp)
	0x0f, 0x8c, 0x34, 0x02, 0x00, 0x00, //0x00000514 jl           LBB0_66
	//0x0000051a LBB0_60
	0xf3, 0x41, 0x0f, 0x6f, 0x00, //0x0000051a movdqu       (%r8), %xmm0
	0xf3, 0x41, 0x0f, 0x6f, 0x68, 0x10, //0x0000051f movdqu       $16(%r8), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x78, 0x20, //0x00000525 movdqu       $32(%r8), %xmm7
	0xf3, 0x41, 0x0f, 0x6f, 0x70, 0x30, //0x0000052b movdqu       $48(%r8), %xmm6
	0x66, 0x0f, 0x6f, 0xd0, //0x00000531 movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00000535 pcmpeqb      %xmm10, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xca, //0x0000053a pmovmskb     %xmm2, %r9d
	0x66, 0x0f, 0x6f, 0xd5, //0x0000053f movdqa       %xmm5, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00000543 pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x00000548 pmovmskb     %xmm2, %ecx
	0x66, 0x0f, 0x6f, 0xd7, //0x0000054c movdqa       %xmm7, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00000550 pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xda, //0x00000555 pmovmskb     %xmm2, %ebx
	0x66, 0x0f, 0x6f, 0xd6, //0x00000559 movdqa       %xmm6, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x0000055d pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x00000562 pmovmskb     %xmm2, %edx
	0x48, 0xc1, 0xe2, 0x30, //0x00000566 shlq         $48, %rdx
	0x48, 0xc1, 0xe3, 0x20, //0x0000056a shlq         $32, %rbx
	0x48, 0xc1, 0xe1, 0x10, //0x0000056e shlq         $16, %rcx
	0x49, 0x09, 0xc9, //0x00000572 orq          %rcx, %r9
	0x49, 0x09, 0xd9, //0x00000575 orq          %rbx, %r9
	0x49, 0x09, 0xd1, //0x00000578 orq          %rdx, %r9
	0x66, 0x0f, 0x6f, 0xd0, //0x0000057b movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x0000057f pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x00000583 pmovmskb     %xmm2, %ecx
	0x66, 0x0f, 0x6f, 0xd5, //0x00000587 movdqa       %xmm5, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x0000058b pcmpeqb      %xmm1, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xe2, //0x0000058f pmovmskb     %xmm2, %r12d
	0x66, 0x0f, 0x6f, 0xd7, //0x00000594 movdqa       %xmm7, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00000598 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xda, //0x0000059c pmovmskb     %xmm2, %ebx
	0x66, 0x0f, 0x6f, 0xd6, //0x000005a0 movdqa       %xmm6, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x000005a4 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x000005a8 pmovmskb     %xmm2, %edx
	0x48, 0xc1, 0xe2, 0x30, //0x000005ac shlq         $48, %rdx
	0x48, 0xc1, 0xe3, 0x20, //0x000005b0 shlq         $32, %rbx
	0x49, 0xc1, 0xe4, 0x10, //0x000005b4 shlq         $16, %r12
	0x4c, 0x09, 0xe1, //0x000005b8 orq          %r12, %rcx
	0x48, 0x09, 0xd9, //0x000005bb orq          %rbx, %rcx
	0x48, 0x09, 0xd1, //0x000005be orq          %rdx, %rcx
	0x48, 0x89, 0xca, //0x000005c1 movq         %rcx, %rdx
	0x4c, 0x09, 0xd2, //0x000005c4 orq          %r10, %rdx
	0x0f, 0x84, 0x43, 0x00, 0x00, 0x00, //0x000005c7 je           LBB0_62
	0x4c, 0x89, 0xd2, //0x000005cd movq         %r10, %rdx
	0x48, 0xf7, 0xd2, //0x000005d0 notq         %rdx
	0x48, 0x21, 0xca, //0x000005d3 andq         %rcx, %rdx
	0x4c, 0x8d, 0x24, 0x12, //0x000005d6 leaq         (%rdx,%rdx), %r12
	0x4d, 0x09, 0xd4, //0x000005da orq          %r10, %r12
	0x4d, 0x89, 0xe2, //0x000005dd movq         %r12, %r10
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000005e0 movabsq      $-6148914691236517206, %rbx
	0x49, 0x31, 0xda, //0x000005ea xorq         %rbx, %r10
	0x48, 0x21, 0xd9, //0x000005ed andq         %rbx, %rcx
	0x4c, 0x21, 0xd1, //0x000005f0 andq         %r10, %rcx
	0x31, 0xdb, //0x000005f3 xorl         %ebx, %ebx
	0x48, 0x01, 0xd1, //0x000005f5 addq         %rdx, %rcx
	0x0f, 0x92, 0xc3, //0x000005f8 setb         %bl
	0x48, 0x89, 0x5d, 0xc8, //0x000005fb movq         %rbx, $-56(%rbp)
	0x48, 0x01, 0xc9, //0x000005ff addq         %rcx, %rcx
	0x4c, 0x31, 0xe9, //0x00000602 xorq         %r13, %rcx
	0x4c, 0x21, 0xe1, //0x00000605 andq         %r12, %rcx
	0x48, 0xf7, 0xd1, //0x00000608 notq         %rcx
	0xe9, 0x0d, 0x00, 0x00, 0x00, //0x0000060b jmp          LBB0_63
	//0x00000610 LBB0_62
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000610 movq         $-1, %rcx
	0x31, 0xd2, //0x00000617 xorl         %edx, %edx
	0x48, 0x89, 0x55, 0xc8, //0x00000619 movq         %rdx, $-56(%rbp)
	//0x0000061d LBB0_63
	0x4c, 0x21, 0xc9, //0x0000061d andq         %r9, %rcx
	0x66, 0x48, 0x0f, 0x6e, 0xd1, //0x00000620 movq         %rcx, %xmm2
	0x66, 0x41, 0x0f, 0x3a, 0x44, 0xd1, 0x00, //0x00000625 pclmulqdq    $0, %xmm9, %xmm2
	0x66, 0x49, 0x0f, 0x7e, 0xd1, //0x0000062c movq         %xmm2, %r9
	0x4d, 0x31, 0xf9, //0x00000631 xorq         %r15, %r9
	0x66, 0x0f, 0x6f, 0xd0, //0x00000634 movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00000638 pcmpeqb      %xmm3, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xfa, //0x0000063c pmovmskb     %xmm2, %r15d
	0x66, 0x0f, 0x6f, 0xd5, //0x00000641 movdqa       %xmm5, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00000645 pcmpeqb      %xmm3, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xe2, //0x00000649 pmovmskb     %xmm2, %r12d
	0x66, 0x0f, 0x6f, 0xd7, //0x0000064e movdqa       %xmm7, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00000652 pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xda, //0x00000656 pmovmskb     %xmm2, %ebx
	0x66, 0x0f, 0x6f, 0xd6, //0x0000065a movdqa       %xmm6, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x0000065e pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x00000662 pmovmskb     %xmm2, %ecx
	0x48, 0xc1, 0xe1, 0x30, //0x00000666 shlq         $48, %rcx
	0x48, 0xc1, 0xe3, 0x20, //0x0000066a shlq         $32, %rbx
	0x49, 0xc1, 0xe4, 0x10, //0x0000066e shlq         $16, %r12
	0x4d, 0x09, 0xe7, //0x00000672 orq          %r12, %r15
	0x49, 0x09, 0xdf, //0x00000675 orq          %rbx, %r15
	0x49, 0x09, 0xcf, //0x00000678 orq          %rcx, %r15
	0x4c, 0x89, 0xc9, //0x0000067b movq         %r9, %rcx
	0x48, 0xf7, 0xd1, //0x0000067e notq         %rcx
	0x49, 0x21, 0xcf, //0x00000681 andq         %rcx, %r15
	0x66, 0x0f, 0x74, 0xc4, //0x00000684 pcmpeqb      %xmm4, %xmm0
	0x66, 0x44, 0x0f, 0xd7, 0xe0, //0x00000688 pmovmskb     %xmm0, %r12d
	0x66, 0x0f, 0x74, 0xec, //0x0000068d pcmpeqb      %xmm4, %xmm5
	0x66, 0x0f, 0xd7, 0xdd, //0x00000691 pmovmskb     %xmm5, %ebx
	0x66, 0x0f, 0x74, 0xfc, //0x00000695 pcmpeqb      %xmm4, %xmm7
	0x66, 0x44, 0x0f, 0xd7, 0xd7, //0x00000699 pmovmskb     %xmm7, %r10d
	0x66, 0x0f, 0x74, 0xf4, //0x0000069e pcmpeqb      %xmm4, %xmm6
	0x66, 0x44, 0x0f, 0xd7, 0xee, //0x000006a2 pmovmskb     %xmm6, %r13d
	0x49, 0xc1, 0xe5, 0x30, //0x000006a7 shlq         $48, %r13
	0x49, 0xc1, 0xe2, 0x20, //0x000006ab shlq         $32, %r10
	0x48, 0xc1, 0xe3, 0x10, //0x000006af shlq         $16, %rbx
	0x49, 0x09, 0xdc, //0x000006b3 orq          %rbx, %r12
	0x4d, 0x09, 0xd4, //0x000006b6 orq          %r10, %r12
	0x4d, 0x09, 0xec, //0x000006b9 orq          %r13, %r12
	0x49, 0xbd, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000006bc movabsq      $6148914691236517205, %r13
	0x49, 0xba, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x000006c6 movabsq      $3689348814741910323, %r10
	0x49, 0x21, 0xcc, //0x000006d0 andq         %rcx, %r12
	0x0f, 0x84, 0xd3, 0xfd, 0xff, 0xff, //0x000006d3 je           LBB0_58
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000006d9 .p2align 4, 0x90
	//0x000006e0 LBB0_64
	0x49, 0x8d, 0x54, 0x24, 0xff, //0x000006e0 leaq         $-1(%r12), %rdx
	0x48, 0x89, 0xd3, //0x000006e5 movq         %rdx, %rbx
	0x4c, 0x21, 0xfb, //0x000006e8 andq         %r15, %rbx
	0x48, 0x89, 0xd9, //0x000006eb movq         %rbx, %rcx
	0x48, 0xd1, 0xe9, //0x000006ee shrq         %rcx
	0x4c, 0x21, 0xe9, //0x000006f1 andq         %r13, %rcx
	0x48, 0x29, 0xcb, //0x000006f4 subq         %rcx, %rbx
	0x48, 0x89, 0xd9, //0x000006f7 movq         %rbx, %rcx
	0x4c, 0x21, 0xd1, //0x000006fa andq         %r10, %rcx
	0x48, 0xc1, 0xeb, 0x02, //0x000006fd shrq         $2, %rbx
	0x4c, 0x21, 0xd3, //0x00000701 andq         %r10, %rbx
	0x48, 0x01, 0xcb, //0x00000704 addq         %rcx, %rbx
	0x48, 0x89, 0xd9, //0x00000707 movq         %rbx, %rcx
	0x48, 0xc1, 0xe9, 0x04, //0x0000070a shrq         $4, %rcx
	0x48, 0x01, 0xd9, //0x0000070e addq         %rbx, %rcx
	0x48, 0xbb, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000711 movabsq      $1085102592571150095, %rbx
	0x48, 0x21, 0xd9, //0x0000071b andq         %rbx, %rcx
	0x48, 0xbb, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x0000071e movabsq      $72340172838076673, %rbx
	0x48, 0x0f, 0xaf, 0xcb, //0x00000728 imulq        %rbx, %rcx
	0x48, 0xc1, 0xe9, 0x38, //0x0000072c shrq         $56, %rcx
	0x48, 0x03, 0x4d, 0xc0, //0x00000730 addq         $-64(%rbp), %rcx
	0x4c, 0x39, 0xd9, //0x00000734 cmpq         %r11, %rcx
	0x0f, 0x86, 0xa3, 0x05, 0x00, 0x00, //0x00000737 jbe          LBB0_109
	0x49, 0xff, 0xc3, //0x0000073d incq         %r11
	0x49, 0x21, 0xd4, //0x00000740 andq         %rdx, %r12
	0x0f, 0x85, 0x97, 0xff, 0xff, 0xff, //0x00000743 jne          LBB0_64
	0xe9, 0x5e, 0xfd, 0xff, 0xff, //0x00000749 jmp          LBB0_58
	//0x0000074e LBB0_66
	0x48, 0x85, 0xc9, //0x0000074e testq        %rcx, %rcx
	0x0f, 0x8e, 0x46, 0x06, 0x00, 0x00, //0x00000751 jle          LBB0_118
	0x4c, 0x89, 0xd3, //0x00000757 movq         %r10, %rbx
	0x44, 0x0f, 0x11, 0x45, 0xb0, //0x0000075a movups       %xmm8, $-80(%rbp)
	0x44, 0x0f, 0x11, 0x45, 0xa0, //0x0000075f movups       %xmm8, $-96(%rbp)
	0x44, 0x0f, 0x11, 0x45, 0x90, //0x00000764 movups       %xmm8, $-112(%rbp)
	0x44, 0x0f, 0x11, 0x45, 0x80, //0x00000769 movups       %xmm8, $-128(%rbp)
	0x44, 0x89, 0xc1, //0x0000076e movl         %r8d, %ecx
	0x81, 0xe1, 0xff, 0x0f, 0x00, 0x00, //0x00000771 andl         $4095, %ecx
	0x81, 0xf9, 0xc1, 0x0f, 0x00, 0x00, //0x00000777 cmpl         $4033, %ecx
	0x0f, 0x82, 0x31, 0x00, 0x00, 0x00, //0x0000077d jb           LBB0_70
	0x48, 0x83, 0x7d, 0xd0, 0x20, //0x00000783 cmpq         $32, $-48(%rbp)
	0x0f, 0x82, 0x38, 0x00, 0x00, 0x00, //0x00000788 jb           LBB0_71
	0x41, 0x0f, 0x10, 0x00, //0x0000078e movups       (%r8), %xmm0
	0x0f, 0x11, 0x45, 0x80, //0x00000792 movups       %xmm0, $-128(%rbp)
	0x41, 0x0f, 0x10, 0x40, 0x10, //0x00000796 movups       $16(%r8), %xmm0
	0x0f, 0x11, 0x45, 0x90, //0x0000079b movups       %xmm0, $-112(%rbp)
	0x49, 0x83, 0xc0, 0x20, //0x0000079f addq         $32, %r8
	0x48, 0x8b, 0x4d, 0xd0, //0x000007a3 movq         $-48(%rbp), %rcx
	0x48, 0x8d, 0x51, 0xe0, //0x000007a7 leaq         $-32(%rcx), %rdx
	0x4c, 0x8d, 0x4d, 0xa0, //0x000007ab leaq         $-96(%rbp), %r9
	0xe9, 0x1a, 0x00, 0x00, 0x00, //0x000007af jmp          LBB0_72
	//0x000007b4 LBB0_70
	0x49, 0xbd, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000007b4 movabsq      $6148914691236517205, %r13
	0x49, 0x89, 0xda, //0x000007be movq         %rbx, %r10
	0xe9, 0x54, 0xfd, 0xff, 0xff, //0x000007c1 jmp          LBB0_60
	//0x000007c6 LBB0_71
	0x4c, 0x8d, 0x4d, 0x80, //0x000007c6 leaq         $-128(%rbp), %r9
	0x48, 0x8b, 0x55, 0xd0, //0x000007ca movq         $-48(%rbp), %rdx
	//0x000007ce LBB0_72
	0x48, 0x83, 0xfa, 0x10, //0x000007ce cmpq         $16, %rdx
	0x0f, 0x82, 0x49, 0x00, 0x00, 0x00, //0x000007d2 jb           LBB0_73
	0x41, 0x0f, 0x10, 0x00, //0x000007d8 movups       (%r8), %xmm0
	0x41, 0x0f, 0x11, 0x01, //0x000007dc movups       %xmm0, (%r9)
	0x49, 0x83, 0xc0, 0x10, //0x000007e0 addq         $16, %r8
	0x49, 0x83, 0xc1, 0x10, //0x000007e4 addq         $16, %r9
	0x48, 0x83, 0xc2, 0xf0, //0x000007e8 addq         $-16, %rdx
	0x48, 0x83, 0xfa, 0x08, //0x000007ec cmpq         $8, %rdx
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x000007f0 jae          LBB0_80
	//0x000007f6 LBB0_74
	0x48, 0x83, 0xfa, 0x04, //0x000007f6 cmpq         $4, %rdx
	0x0f, 0x8c, 0x47, 0x00, 0x00, 0x00, //0x000007fa jl           LBB0_75
	//0x00000800 LBB0_81
	0x41, 0x8b, 0x08, //0x00000800 movl         (%r8), %ecx
	0x41, 0x89, 0x09, //0x00000803 movl         %ecx, (%r9)
	0x49, 0x83, 0xc0, 0x04, //0x00000806 addq         $4, %r8
	0x49, 0x83, 0xc1, 0x04, //0x0000080a addq         $4, %r9
	0x48, 0x83, 0xc2, 0xfc, //0x0000080e addq         $-4, %rdx
	0x48, 0x83, 0xfa, 0x02, //0x00000812 cmpq         $2, %rdx
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x00000816 jae          LBB0_76
	0xe9, 0x44, 0x00, 0x00, 0x00, //0x0000081c jmp          LBB0_77
	//0x00000821 LBB0_73
	0x48, 0x83, 0xfa, 0x08, //0x00000821 cmpq         $8, %rdx
	0x0f, 0x82, 0xcb, 0xff, 0xff, 0xff, //0x00000825 jb           LBB0_74
	//0x0000082b LBB0_80
	0x49, 0x8b, 0x08, //0x0000082b movq         (%r8), %rcx
	0x49, 0x89, 0x09, //0x0000082e movq         %rcx, (%r9)
	0x49, 0x83, 0xc0, 0x08, //0x00000831 addq         $8, %r8
	0x49, 0x83, 0xc1, 0x08, //0x00000835 addq         $8, %r9
	0x48, 0x83, 0xc2, 0xf8, //0x00000839 addq         $-8, %rdx
	0x48, 0x83, 0xfa, 0x04, //0x0000083d cmpq         $4, %rdx
	0x0f, 0x8d, 0xb9, 0xff, 0xff, 0xff, //0x00000841 jge          LBB0_81
	//0x00000847 LBB0_75
	0x48, 0x83, 0xfa, 0x02, //0x00000847 cmpq         $2, %rdx
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x0000084b jb           LBB0_77
	//0x00000851 LBB0_76
	0x41, 0x0f, 0xb7, 0x08, //0x00000851 movzwl       (%r8), %ecx
	0x66, 0x41, 0x89, 0x09, //0x00000855 movw         %cx, (%r9)
	0x49, 0x83, 0xc0, 0x02, //0x00000859 addq         $2, %r8
	0x49, 0x83, 0xc1, 0x02, //0x0000085d addq         $2, %r9
	0x48, 0x83, 0xc2, 0xfe, //0x00000861 addq         $-2, %rdx
	//0x00000865 LBB0_77
	0x4c, 0x89, 0xc1, //0x00000865 movq         %r8, %rcx
	0x4c, 0x8d, 0x45, 0x80, //0x00000868 leaq         $-128(%rbp), %r8
	0x48, 0x85, 0xd2, //0x0000086c testq        %rdx, %rdx
	0x49, 0xbd, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000086f movabsq      $6148914691236517205, %r13
	0x49, 0x89, 0xda, //0x00000879 movq         %rbx, %r10
	0x0f, 0x84, 0x98, 0xfc, 0xff, 0xff, //0x0000087c je           LBB0_60
	0x8a, 0x09, //0x00000882 movb         (%rcx), %cl
	0x41, 0x88, 0x09, //0x00000884 movb         %cl, (%r9)
	0x4c, 0x8d, 0x45, 0x80, //0x00000887 leaq         $-128(%rbp), %r8
	0xe9, 0x8a, 0xfc, 0xff, 0xff, //0x0000088b jmp          LBB0_60
	//0x00000890 LBB0_82
	0x49, 0x8d, 0x4e, 0x05, //0x00000890 leaq         $5(%r14), %rcx
	0x48, 0x3b, 0x4f, 0x08, //0x00000894 cmpq         $8(%rdi), %rcx
	0x0f, 0x87, 0x74, 0xfa, 0xff, 0xff, //0x00000898 ja           LBB0_45
	//0x0000089e LBB0_83
	0x48, 0x89, 0x0e, //0x0000089e movq         %rcx, (%rsi)
	0xe9, 0x69, 0xfa, 0xff, 0xff, //0x000008a1 jmp          LBB0_44
	//0x000008a6 LBB0_84
	0x49, 0xbd, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000008a6 movabsq      $6148914691236517205, %r13
	0x48, 0x8b, 0x4f, 0x08, //0x000008b0 movq         $8(%rdi), %rcx
	0x48, 0x29, 0xd9, //0x000008b4 subq         %rbx, %rcx
	0x49, 0x01, 0xd8, //0x000008b7 addq         %rbx, %r8
	0x45, 0x31, 0xff, //0x000008ba xorl         %r15d, %r15d
	0xf3, 0x44, 0x0f, 0x6f, 0x15, 0x6a, 0xf7, 0xff, 0xff, //0x000008bd movdqu       $-2198(%rip), %xmm10  /* LCPI0_3+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0x72, 0xf7, 0xff, 0xff, //0x000008c6 movdqu       $-2190(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0x66, 0x45, 0x0f, 0x76, 0xc9, //0x000008ce pcmpeqd      %xmm9, %xmm9
	0xf3, 0x0f, 0x6f, 0x1d, 0x75, 0xf7, 0xff, 0xff, //0x000008d3 movdqu       $-2187(%rip), %xmm3  /* LCPI0_5+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x25, 0x3d, 0xf7, 0xff, 0xff, //0x000008db movdqu       $-2243(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0x45, 0x0f, 0x57, 0xc0, //0x000008e3 xorps        %xmm8, %xmm8
	0x45, 0x31, 0xd2, //0x000008e7 xorl         %r10d, %r10d
	0x31, 0xd2, //0x000008ea xorl         %edx, %edx
	0x48, 0x89, 0x55, 0xc0, //0x000008ec movq         %rdx, $-64(%rbp)
	0x45, 0x31, 0xdb, //0x000008f0 xorl         %r11d, %r11d
	0xe9, 0x60, 0x00, 0x00, 0x00, //0x000008f3 jmp          LBB0_86
	//0x000008f8 LBB0_85
	0x49, 0xc1, 0xf9, 0x3f, //0x000008f8 sarq         $63, %r9
	0x4c, 0x89, 0xf9, //0x000008fc movq         %r15, %rcx
	0x48, 0xd1, 0xe9, //0x000008ff shrq         %rcx
	0x4c, 0x21, 0xe9, //0x00000902 andq         %r13, %rcx
	0x49, 0x29, 0xcf, //0x00000905 subq         %rcx, %r15
	0x4c, 0x89, 0xf9, //0x00000908 movq         %r15, %rcx
	0x4c, 0x21, 0xd1, //0x0000090b andq         %r10, %rcx
	0x49, 0xc1, 0xef, 0x02, //0x0000090e shrq         $2, %r15
	0x4d, 0x21, 0xd7, //0x00000912 andq         %r10, %r15
	0x49, 0x01, 0xcf, //0x00000915 addq         %rcx, %r15
	0x4c, 0x89, 0xf9, //0x00000918 movq         %r15, %rcx
	0x48, 0xc1, 0xe9, 0x04, //0x0000091b shrq         $4, %rcx
	0x4c, 0x01, 0xf9, //0x0000091f addq         %r15, %rcx
	0x48, 0xba, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000922 movabsq      $1085102592571150095, %rdx
	0x48, 0x21, 0xd1, //0x0000092c andq         %rdx, %rcx
	0x48, 0xba, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x0000092f movabsq      $72340172838076673, %rdx
	0x48, 0x0f, 0xaf, 0xca, //0x00000939 imulq        %rdx, %rcx
	0x48, 0xc1, 0xe9, 0x38, //0x0000093d shrq         $56, %rcx
	0x48, 0x01, 0x4d, 0xc0, //0x00000941 addq         %rcx, $-64(%rbp)
	0x49, 0x83, 0xc0, 0x40, //0x00000945 addq         $64, %r8
	0x48, 0x8b, 0x4d, 0xd0, //0x00000949 movq         $-48(%rbp), %rcx
	0x48, 0x83, 0xc1, 0xc0, //0x0000094d addq         $-64, %rcx
	0x4d, 0x89, 0xcf, //0x00000951 movq         %r9, %r15
	0x4c, 0x8b, 0x55, 0xc8, //0x00000954 movq         $-56(%rbp), %r10
	//0x00000958 LBB0_86
	0x48, 0x83, 0xf9, 0x40, //0x00000958 cmpq         $64, %rcx
	0x48, 0x89, 0x4d, 0xd0, //0x0000095c movq         %rcx, $-48(%rbp)
	0x0f, 0x8c, 0x38, 0x02, 0x00, 0x00, //0x00000960 jl           LBB0_93
	//0x00000966 LBB0_87
	0xf3, 0x41, 0x0f, 0x6f, 0x00, //0x00000966 movdqu       (%r8), %xmm0
	0xf3, 0x41, 0x0f, 0x6f, 0x68, 0x10, //0x0000096b movdqu       $16(%r8), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x78, 0x20, //0x00000971 movdqu       $32(%r8), %xmm7
	0xf3, 0x41, 0x0f, 0x6f, 0x70, 0x30, //0x00000977 movdqu       $48(%r8), %xmm6
	0x66, 0x0f, 0x6f, 0xd0, //0x0000097d movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00000981 pcmpeqb      %xmm10, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xca, //0x00000986 pmovmskb     %xmm2, %r9d
	0x66, 0x0f, 0x6f, 0xd5, //0x0000098b movdqa       %xmm5, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x0000098f pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x00000994 pmovmskb     %xmm2, %ecx
	0x66, 0x0f, 0x6f, 0xd7, //0x00000998 movdqa       %xmm7, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x0000099c pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xda, //0x000009a1 pmovmskb     %xmm2, %ebx
	0x66, 0x0f, 0x6f, 0xd6, //0x000009a5 movdqa       %xmm6, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x000009a9 pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x000009ae pmovmskb     %xmm2, %edx
	0x48, 0xc1, 0xe2, 0x30, //0x000009b2 shlq         $48, %rdx
	0x48, 0xc1, 0xe3, 0x20, //0x000009b6 shlq         $32, %rbx
	0x48, 0xc1, 0xe1, 0x10, //0x000009ba shlq         $16, %rcx
	0x49, 0x09, 0xc9, //0x000009be orq          %rcx, %r9
	0x49, 0x09, 0xd9, //0x000009c1 orq          %rbx, %r9
	0x49, 0x09, 0xd1, //0x000009c4 orq          %rdx, %r9
	0x66, 0x0f, 0x6f, 0xd0, //0x000009c7 movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x000009cb pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x000009cf pmovmskb     %xmm2, %ecx
	0x66, 0x0f, 0x6f, 0xd5, //0x000009d3 movdqa       %xmm5, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x000009d7 pcmpeqb      %xmm1, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xe2, //0x000009db pmovmskb     %xmm2, %r12d
	0x66, 0x0f, 0x6f, 0xd7, //0x000009e0 movdqa       %xmm7, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x000009e4 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xda, //0x000009e8 pmovmskb     %xmm2, %ebx
	0x66, 0x0f, 0x6f, 0xd6, //0x000009ec movdqa       %xmm6, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x000009f0 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x000009f4 pmovmskb     %xmm2, %edx
	0x48, 0xc1, 0xe2, 0x30, //0x000009f8 shlq         $48, %rdx
	0x48, 0xc1, 0xe3, 0x20, //0x000009fc shlq         $32, %rbx
	0x49, 0xc1, 0xe4, 0x10, //0x00000a00 shlq         $16, %r12
	0x4c, 0x09, 0xe1, //0x00000a04 orq          %r12, %rcx
	0x48, 0x09, 0xd9, //0x00000a07 orq          %rbx, %rcx
	0x48, 0x09, 0xd1, //0x00000a0a orq          %rdx, %rcx
	0x48, 0x89, 0xca, //0x00000a0d movq         %rcx, %rdx
	0x4c, 0x09, 0xd2, //0x00000a10 orq          %r10, %rdx
	0x0f, 0x84, 0x43, 0x00, 0x00, 0x00, //0x00000a13 je           LBB0_89
	0x4c, 0x89, 0xd2, //0x00000a19 movq         %r10, %rdx
	0x48, 0xf7, 0xd2, //0x00000a1c notq         %rdx
	0x48, 0x21, 0xca, //0x00000a1f andq         %rcx, %rdx
	0x4c, 0x8d, 0x24, 0x12, //0x00000a22 leaq         (%rdx,%rdx), %r12
	0x4d, 0x09, 0xd4, //0x00000a26 orq          %r10, %r12
	0x4d, 0x89, 0xe2, //0x00000a29 movq         %r12, %r10
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000a2c movabsq      $-6148914691236517206, %rbx
	0x49, 0x31, 0xda, //0x00000a36 xorq         %rbx, %r10
	0x48, 0x21, 0xd9, //0x00000a39 andq         %rbx, %rcx
	0x4c, 0x21, 0xd1, //0x00000a3c andq         %r10, %rcx
	0x31, 0xdb, //0x00000a3f xorl         %ebx, %ebx
	0x48, 0x01, 0xd1, //0x00000a41 addq         %rdx, %rcx
	0x0f, 0x92, 0xc3, //0x00000a44 setb         %bl
	0x48, 0x89, 0x5d, 0xc8, //0x00000a47 movq         %rbx, $-56(%rbp)
	0x48, 0x01, 0xc9, //0x00000a4b addq         %rcx, %rcx
	0x4c, 0x31, 0xe9, //0x00000a4e xorq         %r13, %rcx
	0x4c, 0x21, 0xe1, //0x00000a51 andq         %r12, %rcx
	0x48, 0xf7, 0xd1, //0x00000a54 notq         %rcx
	0xe9, 0x0d, 0x00, 0x00, 0x00, //0x00000a57 jmp          LBB0_90
	//0x00000a5c LBB0_89
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000a5c movq         $-1, %rcx
	0x31, 0xd2, //0x00000a63 xorl         %edx, %edx
	0x48, 0x89, 0x55, 0xc8, //0x00000a65 movq         %rdx, $-56(%rbp)
	//0x00000a69 LBB0_90
	0x4c, 0x21, 0xc9, //0x00000a69 andq         %r9, %rcx
	0x66, 0x48, 0x0f, 0x6e, 0xd1, //0x00000a6c movq         %rcx, %xmm2
	0x66, 0x41, 0x0f, 0x3a, 0x44, 0xd1, 0x00, //0x00000a71 pclmulqdq    $0, %xmm9, %xmm2
	0x66, 0x49, 0x0f, 0x7e, 0xd1, //0x00000a78 movq         %xmm2, %r9
	0x4d, 0x31, 0xf9, //0x00000a7d xorq         %r15, %r9
	0x66, 0x0f, 0x6f, 0xd0, //0x00000a80 movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00000a84 pcmpeqb      %xmm3, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xfa, //0x00000a88 pmovmskb     %xmm2, %r15d
	0x66, 0x0f, 0x6f, 0xd5, //0x00000a8d movdqa       %xmm5, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00000a91 pcmpeqb      %xmm3, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xe2, //0x00000a95 pmovmskb     %xmm2, %r12d
	0x66, 0x0f, 0x6f, 0xd7, //0x00000a9a movdqa       %xmm7, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00000a9e pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xda, //0x00000aa2 pmovmskb     %xmm2, %ebx
	0x66, 0x0f, 0x6f, 0xd6, //0x00000aa6 movdqa       %xmm6, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00000aaa pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x00000aae pmovmskb     %xmm2, %ecx
	0x48, 0xc1, 0xe1, 0x30, //0x00000ab2 shlq         $48, %rcx
	0x48, 0xc1, 0xe3, 0x20, //0x00000ab6 shlq         $32, %rbx
	0x49, 0xc1, 0xe4, 0x10, //0x00000aba shlq         $16, %r12
	0x4d, 0x09, 0xe7, //0x00000abe orq          %r12, %r15
	0x49, 0x09, 0xdf, //0x00000ac1 orq          %rbx, %r15
	0x49, 0x09, 0xcf, //0x00000ac4 orq          %rcx, %r15
	0x4c, 0x89, 0xc9, //0x00000ac7 movq         %r9, %rcx
	0x48, 0xf7, 0xd1, //0x00000aca notq         %rcx
	0x49, 0x21, 0xcf, //0x00000acd andq         %rcx, %r15
	0x66, 0x0f, 0x74, 0xc4, //0x00000ad0 pcmpeqb      %xmm4, %xmm0
	0x66, 0x44, 0x0f, 0xd7, 0xe0, //0x00000ad4 pmovmskb     %xmm0, %r12d
	0x66, 0x0f, 0x74, 0xec, //0x00000ad9 pcmpeqb      %xmm4, %xmm5
	0x66, 0x0f, 0xd7, 0xdd, //0x00000add pmovmskb     %xmm5, %ebx
	0x66, 0x0f, 0x74, 0xfc, //0x00000ae1 pcmpeqb      %xmm4, %xmm7
	0x66, 0x44, 0x0f, 0xd7, 0xd7, //0x00000ae5 pmovmskb     %xmm7, %r10d
	0x66, 0x0f, 0x74, 0xf4, //0x00000aea pcmpeqb      %xmm4, %xmm6
	0x66, 0x44, 0x0f, 0xd7, 0xee, //0x00000aee pmovmskb     %xmm6, %r13d
	0x49, 0xc1, 0xe5, 0x30, //0x00000af3 shlq         $48, %r13
	0x49, 0xc1, 0xe2, 0x20, //0x00000af7 shlq         $32, %r10
	0x48, 0xc1, 0xe3, 0x10, //0x00000afb shlq         $16, %rbx
	0x49, 0x09, 0xdc, //0x00000aff orq          %rbx, %r12
	0x4d, 0x09, 0xd4, //0x00000b02 orq          %r10, %r12
	0x4d, 0x09, 0xec, //0x00000b05 orq          %r13, %r12
	0x49, 0xbd, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000b08 movabsq      $6148914691236517205, %r13
	0x49, 0xba, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x00000b12 movabsq      $3689348814741910323, %r10
	0x49, 0x21, 0xcc, //0x00000b1c andq         %rcx, %r12
	0x0f, 0x84, 0xd3, 0xfd, 0xff, 0xff, //0x00000b1f je           LBB0_85
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000b25 .p2align 4, 0x90
	//0x00000b30 LBB0_91
	0x49, 0x8d, 0x54, 0x24, 0xff, //0x00000b30 leaq         $-1(%r12), %rdx
	0x48, 0x89, 0xd3, //0x00000b35 movq         %rdx, %rbx
	0x4c, 0x21, 0xfb, //0x00000b38 andq         %r15, %rbx
	0x48, 0x89, 0xd9, //0x00000b3b movq         %rbx, %rcx
	0x48, 0xd1, 0xe9, //0x00000b3e shrq         %rcx
	0x4c, 0x21, 0xe9, //0x00000b41 andq         %r13, %rcx
	0x48, 0x29, 0xcb, //0x00000b44 subq         %rcx, %rbx
	0x48, 0x89, 0xd9, //0x00000b47 movq         %rbx, %rcx
	0x4c, 0x21, 0xd1, //0x00000b4a andq         %r10, %rcx
	0x48, 0xc1, 0xeb, 0x02, //0x00000b4d shrq         $2, %rbx
	0x4c, 0x21, 0xd3, //0x00000b51 andq         %r10, %rbx
	0x48, 0x01, 0xcb, //0x00000b54 addq         %rcx, %rbx
	0x48, 0x89, 0xd9, //0x00000b57 movq         %rbx, %rcx
	0x48, 0xc1, 0xe9, 0x04, //0x00000b5a shrq         $4, %rcx
	0x48, 0x01, 0xd9, //0x00000b5e addq         %rbx, %rcx
	0x48, 0xbb, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000b61 movabsq      $1085102592571150095, %rbx
	0x48, 0x21, 0xd9, //0x00000b6b andq         %rbx, %rcx
	0x48, 0xbb, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00000b6e movabsq      $72340172838076673, %rbx
	0x48, 0x0f, 0xaf, 0xcb, //0x00000b78 imulq        %rbx, %rcx
	0x48, 0xc1, 0xe9, 0x38, //0x00000b7c shrq         $56, %rcx
	0x48, 0x03, 0x4d, 0xc0, //0x00000b80 addq         $-64(%rbp), %rcx
	0x4c, 0x39, 0xd9, //0x00000b84 cmpq         %r11, %rcx
	0x0f, 0x86, 0x53, 0x01, 0x00, 0x00, //0x00000b87 jbe          LBB0_109
	0x49, 0xff, 0xc3, //0x00000b8d incq         %r11
	0x49, 0x21, 0xd4, //0x00000b90 andq         %rdx, %r12
	0x0f, 0x85, 0x97, 0xff, 0xff, 0xff, //0x00000b93 jne          LBB0_91
	0xe9, 0x5a, 0xfd, 0xff, 0xff, //0x00000b99 jmp          LBB0_85
	//0x00000b9e LBB0_93
	0x48, 0x85, 0xc9, //0x00000b9e testq        %rcx, %rcx
	0x0f, 0x8e, 0xf6, 0x01, 0x00, 0x00, //0x00000ba1 jle          LBB0_118
	0x4c, 0x89, 0xd3, //0x00000ba7 movq         %r10, %rbx
	0x44, 0x0f, 0x11, 0x45, 0xb0, //0x00000baa movups       %xmm8, $-80(%rbp)
	0x44, 0x0f, 0x11, 0x45, 0xa0, //0x00000baf movups       %xmm8, $-96(%rbp)
	0x44, 0x0f, 0x11, 0x45, 0x90, //0x00000bb4 movups       %xmm8, $-112(%rbp)
	0x44, 0x0f, 0x11, 0x45, 0x80, //0x00000bb9 movups       %xmm8, $-128(%rbp)
	0x44, 0x89, 0xc1, //0x00000bbe movl         %r8d, %ecx
	0x81, 0xe1, 0xff, 0x0f, 0x00, 0x00, //0x00000bc1 andl         $4095, %ecx
	0x81, 0xf9, 0xc1, 0x0f, 0x00, 0x00, //0x00000bc7 cmpl         $4033, %ecx
	0x0f, 0x82, 0x31, 0x00, 0x00, 0x00, //0x00000bcd jb           LBB0_97
	0x48, 0x83, 0x7d, 0xd0, 0x20, //0x00000bd3 cmpq         $32, $-48(%rbp)
	0x0f, 0x82, 0x38, 0x00, 0x00, 0x00, //0x00000bd8 jb           LBB0_98
	0x41, 0x0f, 0x10, 0x00, //0x00000bde movups       (%r8), %xmm0
	0x0f, 0x11, 0x45, 0x80, //0x00000be2 movups       %xmm0, $-128(%rbp)
	0x41, 0x0f, 0x10, 0x40, 0x10, //0x00000be6 movups       $16(%r8), %xmm0
	0x0f, 0x11, 0x45, 0x90, //0x00000beb movups       %xmm0, $-112(%rbp)
	0x49, 0x83, 0xc0, 0x20, //0x00000bef addq         $32, %r8
	0x48, 0x8b, 0x4d, 0xd0, //0x00000bf3 movq         $-48(%rbp), %rcx
	0x48, 0x8d, 0x51, 0xe0, //0x00000bf7 leaq         $-32(%rcx), %rdx
	0x4c, 0x8d, 0x4d, 0xa0, //0x00000bfb leaq         $-96(%rbp), %r9
	0xe9, 0x1a, 0x00, 0x00, 0x00, //0x00000bff jmp          LBB0_99
	//0x00000c04 LBB0_97
	0x49, 0xbd, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000c04 movabsq      $6148914691236517205, %r13
	0x49, 0x89, 0xda, //0x00000c0e movq         %rbx, %r10
	0xe9, 0x50, 0xfd, 0xff, 0xff, //0x00000c11 jmp          LBB0_87
	//0x00000c16 LBB0_98
	0x4c, 0x8d, 0x4d, 0x80, //0x00000c16 leaq         $-128(%rbp), %r9
	0x48, 0x8b, 0x55, 0xd0, //0x00000c1a movq         $-48(%rbp), %rdx
	//0x00000c1e LBB0_99
	0x48, 0x83, 0xfa, 0x10, //0x00000c1e cmpq         $16, %rdx
	0x0f, 0x82, 0x49, 0x00, 0x00, 0x00, //0x00000c22 jb           LBB0_100
	0x41, 0x0f, 0x10, 0x00, //0x00000c28 movups       (%r8), %xmm0
	0x41, 0x0f, 0x11, 0x01, //0x00000c2c movups       %xmm0, (%r9)
	0x49, 0x83, 0xc0, 0x10, //0x00000c30 addq         $16, %r8
	0x49, 0x83, 0xc1, 0x10, //0x00000c34 addq         $16, %r9
	0x48, 0x83, 0xc2, 0xf0, //0x00000c38 addq         $-16, %rdx
	0x48, 0x83, 0xfa, 0x08, //0x00000c3c cmpq         $8, %rdx
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x00000c40 jae          LBB0_107
	//0x00000c46 LBB0_101
	0x48, 0x83, 0xfa, 0x04, //0x00000c46 cmpq         $4, %rdx
	0x0f, 0x8c, 0x47, 0x00, 0x00, 0x00, //0x00000c4a jl           LBB0_102
	//0x00000c50 LBB0_108
	0x41, 0x8b, 0x08, //0x00000c50 movl         (%r8), %ecx
	0x41, 0x89, 0x09, //0x00000c53 movl         %ecx, (%r9)
	0x49, 0x83, 0xc0, 0x04, //0x00000c56 addq         $4, %r8
	0x49, 0x83, 0xc1, 0x04, //0x00000c5a addq         $4, %r9
	0x48, 0x83, 0xc2, 0xfc, //0x00000c5e addq         $-4, %rdx
	0x48, 0x83, 0xfa, 0x02, //0x00000c62 cmpq         $2, %rdx
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x00000c66 jae          LBB0_103
	0xe9, 0x44, 0x00, 0x00, 0x00, //0x00000c6c jmp          LBB0_104
	//0x00000c71 LBB0_100
	0x48, 0x83, 0xfa, 0x08, //0x00000c71 cmpq         $8, %rdx
	0x0f, 0x82, 0xcb, 0xff, 0xff, 0xff, //0x00000c75 jb           LBB0_101
	//0x00000c7b LBB0_107
	0x49, 0x8b, 0x08, //0x00000c7b movq         (%r8), %rcx
	0x49, 0x89, 0x09, //0x00000c7e movq         %rcx, (%r9)
	0x49, 0x83, 0xc0, 0x08, //0x00000c81 addq         $8, %r8
	0x49, 0x83, 0xc1, 0x08, //0x00000c85 addq         $8, %r9
	0x48, 0x83, 0xc2, 0xf8, //0x00000c89 addq         $-8, %rdx
	0x48, 0x83, 0xfa, 0x04, //0x00000c8d cmpq         $4, %rdx
	0x0f, 0x8d, 0xb9, 0xff, 0xff, 0xff, //0x00000c91 jge          LBB0_108
	//0x00000c97 LBB0_102
	0x48, 0x83, 0xfa, 0x02, //0x00000c97 cmpq         $2, %rdx
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x00000c9b jb           LBB0_104
	//0x00000ca1 LBB0_103
	0x41, 0x0f, 0xb7, 0x08, //0x00000ca1 movzwl       (%r8), %ecx
	0x66, 0x41, 0x89, 0x09, //0x00000ca5 movw         %cx, (%r9)
	0x49, 0x83, 0xc0, 0x02, //0x00000ca9 addq         $2, %r8
	0x49, 0x83, 0xc1, 0x02, //0x00000cad addq         $2, %r9
	0x48, 0x83, 0xc2, 0xfe, //0x00000cb1 addq         $-2, %rdx
	//0x00000cb5 LBB0_104
	0x4c, 0x89, 0xc1, //0x00000cb5 movq         %r8, %rcx
	0x4c, 0x8d, 0x45, 0x80, //0x00000cb8 leaq         $-128(%rbp), %r8
	0x48, 0x85, 0xd2, //0x00000cbc testq        %rdx, %rdx
	0x49, 0xbd, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000cbf movabsq      $6148914691236517205, %r13
	0x49, 0x89, 0xda, //0x00000cc9 movq         %rbx, %r10
	0x0f, 0x84, 0x94, 0xfc, 0xff, 0xff, //0x00000ccc je           LBB0_87
	0x8a, 0x09, //0x00000cd2 movb         (%rcx), %cl
	0x41, 0x88, 0x09, //0x00000cd4 movb         %cl, (%r9)
	0x4c, 0x8d, 0x45, 0x80, //0x00000cd7 leaq         $-128(%rbp), %r8
	0xe9, 0x86, 0xfc, 0xff, 0xff, //0x00000cdb jmp          LBB0_87
	//0x00000ce0 LBB0_109
	0x48, 0x8b, 0x47, 0x08, //0x00000ce0 movq         $8(%rdi), %rax
	0x49, 0x0f, 0xbc, 0xcc, //0x00000ce4 bsfq         %r12, %rcx
	0x48, 0x2b, 0x4d, 0xd0, //0x00000ce8 subq         $-48(%rbp), %rcx
	0x48, 0x8d, 0x44, 0x01, 0x01, //0x00000cec leaq         $1(%rcx,%rax), %rax
	0x48, 0x89, 0x06, //0x00000cf1 movq         %rax, (%rsi)
	0x48, 0x8b, 0x4f, 0x08, //0x00000cf4 movq         $8(%rdi), %rcx
	0x48, 0x39, 0xc8, //0x00000cf8 cmpq         %rcx, %rax
	0x48, 0x0f, 0x47, 0xc1, //0x00000cfb cmovaq       %rcx, %rax
	0x48, 0x89, 0x06, //0x00000cff movq         %rax, (%rsi)
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000d02 movq         $-1, %rax
	0x4c, 0x0f, 0x47, 0xf0, //0x00000d09 cmovaq       %rax, %r14
	0xe9, 0xfd, 0xf5, 0xff, 0xff, //0x00000d0d jmp          LBB0_44
	//0x00000d12 LBB0_54
	0x4d, 0x85, 0xe4, //0x00000d12 testq        %r12, %r12
	0x0f, 0x85, 0x8e, 0x00, 0x00, 0x00, //0x00000d15 jne          LBB0_119
	0x4b, 0x8d, 0x5c, 0x1f, 0x01, //0x00000d1b leaq         $1(%r15,%r11), %rbx
	0x49, 0xf7, 0xd7, //0x00000d20 notq         %r15
	0x4d, 0x01, 0xcf, //0x00000d23 addq         %r9, %r15
	//0x00000d26 LBB0_56
	0x4d, 0x85, 0xff, //0x00000d26 testq        %r15, %r15
	0x0f, 0x8f, 0x24, 0x00, 0x00, 0x00, //0x00000d29 jg           LBB0_113
	0xe9, 0xde, 0xf5, 0xff, 0xff, //0x00000d2f jmp          LBB0_45
	//0x00000d34 LBB0_111
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00000d34 movq         $-2, %rcx
	0xb8, 0x02, 0x00, 0x00, 0x00, //0x00000d3b movl         $2, %eax
	0x48, 0x01, 0xc3, //0x00000d40 addq         %rax, %rbx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000d43 movq         $-1, %rax
	0x49, 0x01, 0xcf, //0x00000d4a addq         %rcx, %r15
	0x0f, 0x8e, 0xbf, 0xf5, 0xff, 0xff, //0x00000d4d jle          LBB0_45
	//0x00000d53 LBB0_113
	0x0f, 0xb6, 0x03, //0x00000d53 movzbl       (%rbx), %eax
	0x3c, 0x5c, //0x00000d56 cmpb         $92, %al
	0x0f, 0x84, 0xd6, 0xff, 0xff, 0xff, //0x00000d58 je           LBB0_111
	0x3c, 0x22, //0x00000d5e cmpb         $34, %al
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x00000d60 je           LBB0_116
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000d66 movq         $-1, %rcx
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00000d6d movl         $1, %eax
	0x48, 0x01, 0xc3, //0x00000d72 addq         %rax, %rbx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000d75 movq         $-1, %rax
	0x49, 0x01, 0xcf, //0x00000d7c addq         %rcx, %r15
	0x0f, 0x8f, 0xce, 0xff, 0xff, 0xff, //0x00000d7f jg           LBB0_113
	0xe9, 0x88, 0xf5, 0xff, 0xff, //0x00000d85 jmp          LBB0_45
	//0x00000d8a LBB0_116
	0x4c, 0x29, 0xc3, //0x00000d8a subq         %r8, %rbx
	0x48, 0xff, 0xc3, //0x00000d8d incq         %rbx
	0xe9, 0x77, 0xf5, 0xff, 0xff, //0x00000d90 jmp          LBB0_43
	//0x00000d95 LBB0_117
	0x4c, 0x01, 0xc3, //0x00000d95 addq         %r8, %rbx
	0xe9, 0x89, 0xff, 0xff, 0xff, //0x00000d98 jmp          LBB0_56
	//0x00000d9d LBB0_118
	0x48, 0x8b, 0x4f, 0x08, //0x00000d9d movq         $8(%rdi), %rcx
	0x48, 0x89, 0x0e, //0x00000da1 movq         %rcx, (%rsi)
	0xe9, 0x69, 0xf5, 0xff, 0xff, //0x00000da4 jmp          LBB0_45
	//0x00000da9 LBB0_119
	0x49, 0x8d, 0x49, 0xff, //0x00000da9 leaq         $-1(%r9), %rcx
	0x4c, 0x39, 0xf9, //0x00000dad cmpq         %r15, %rcx
	0x0f, 0x84, 0x5c, 0xf5, 0xff, 0xff, //0x00000db0 je           LBB0_45
	0x4b, 0x8d, 0x5c, 0x1f, 0x02, //0x00000db6 leaq         $2(%r15,%r11), %rbx
	0x4d, 0x29, 0xf9, //0x00000dbb subq         %r15, %r9
	0x49, 0x83, 0xc1, 0xfe, //0x00000dbe addq         $-2, %r9
	0x4d, 0x89, 0xcf, //0x00000dc2 movq         %r9, %r15
	0xe9, 0x5c, 0xff, 0xff, 0xff, //0x00000dc5 jmp          LBB0_56
	0x90, 0x90, //0x00000dca .p2align 2, 0x90
	// // .set L0_0_set_45, LBB0_45-LJTI0_0
	// // .set L0_0_set_47, LBB0_47-LJTI0_0
	// // .set L0_0_set_48, LBB0_48-LJTI0_0
	// // .set L0_0_set_29, LBB0_29-LJTI0_0
	// // .set L0_0_set_57, LBB0_57-LJTI0_0
	// // .set L0_0_set_82, LBB0_82-LJTI0_0
	// // .set L0_0_set_46, LBB0_46-LJTI0_0
	// // .set L0_0_set_84, LBB0_84-LJTI0_0
	//0x00000dcc LJTI0_0
	0x46, 0xf5, 0xff, 0xff, //0x00000dcc .long L0_0_set_45
	0x68, 0xf5, 0xff, 0xff, //0x00000dd0 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000dd4 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000dd8 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000ddc .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000de0 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000de4 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000de8 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000dec .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000df0 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000df4 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000df8 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000dfc .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e00 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e04 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e08 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e0c .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e10 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e14 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e18 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e1c .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e20 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e24 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e28 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e2c .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e30 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e34 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e38 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e3c .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e40 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e44 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e48 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e4c .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e50 .long L0_0_set_47
	0x77, 0xf5, 0xff, 0xff, //0x00000e54 .long L0_0_set_48
	0x68, 0xf5, 0xff, 0xff, //0x00000e58 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e5c .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e60 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e64 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e68 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e6c .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e70 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e74 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e78 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e7c .long L0_0_set_47
	0x5f, 0xf4, 0xff, 0xff, //0x00000e80 .long L0_0_set_29
	0x68, 0xf5, 0xff, 0xff, //0x00000e84 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000e88 .long L0_0_set_47
	0x5f, 0xf4, 0xff, 0xff, //0x00000e8c .long L0_0_set_29
	0x5f, 0xf4, 0xff, 0xff, //0x00000e90 .long L0_0_set_29
	0x5f, 0xf4, 0xff, 0xff, //0x00000e94 .long L0_0_set_29
	0x5f, 0xf4, 0xff, 0xff, //0x00000e98 .long L0_0_set_29
	0x5f, 0xf4, 0xff, 0xff, //0x00000e9c .long L0_0_set_29
	0x5f, 0xf4, 0xff, 0xff, //0x00000ea0 .long L0_0_set_29
	0x5f, 0xf4, 0xff, 0xff, //0x00000ea4 .long L0_0_set_29
	0x5f, 0xf4, 0xff, 0xff, //0x00000ea8 .long L0_0_set_29
	0x5f, 0xf4, 0xff, 0xff, //0x00000eac .long L0_0_set_29
	0x5f, 0xf4, 0xff, 0xff, //0x00000eb0 .long L0_0_set_29
	0x68, 0xf5, 0xff, 0xff, //0x00000eb4 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000eb8 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000ebc .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000ec0 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000ec4 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000ec8 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000ecc .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000ed0 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000ed4 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000ed8 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000edc .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000ee0 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000ee4 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000ee8 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000eec .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000ef0 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000ef4 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000ef8 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000efc .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f00 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f04 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f08 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f0c .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f10 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f14 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f18 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f1c .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f20 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f24 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f28 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f2c .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f30 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f34 .long L0_0_set_47
	0x8e, 0xf6, 0xff, 0xff, //0x00000f38 .long L0_0_set_57
	0x68, 0xf5, 0xff, 0xff, //0x00000f3c .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f40 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f44 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f48 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f4c .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f50 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f54 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f58 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f5c .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f60 .long L0_0_set_47
	0xc4, 0xfa, 0xff, 0xff, //0x00000f64 .long L0_0_set_82
	0x68, 0xf5, 0xff, 0xff, //0x00000f68 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f6c .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f70 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f74 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f78 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f7c .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f80 .long L0_0_set_47
	0x55, 0xf5, 0xff, 0xff, //0x00000f84 .long L0_0_set_46
	0x68, 0xf5, 0xff, 0xff, //0x00000f88 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f8c .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f90 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f94 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000f98 .long L0_0_set_47
	0x55, 0xf5, 0xff, 0xff, //0x00000f9c .long L0_0_set_46
	0x68, 0xf5, 0xff, 0xff, //0x00000fa0 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000fa4 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000fa8 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000fac .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000fb0 .long L0_0_set_47
	0x68, 0xf5, 0xff, 0xff, //0x00000fb4 .long L0_0_set_47
	0xda, 0xfa, 0xff, 0xff, //0x00000fb8 .long L0_0_set_84
	//0x00000fbc .p2align 2, 0x00
	//0x00000fbc _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00000fbc .long 2
}
 
