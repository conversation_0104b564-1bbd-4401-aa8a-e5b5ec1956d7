// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package sse

var _text_get_by_path = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, // QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	//0x00000010 LCPI0_1
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000010 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000020 LCPI0_2
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x00000020 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	//0x00000030 LCPI0_3
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000030 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x00000040 LCPI0_4
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000040 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000050 LCPI0_5
	0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, //0x00000050 QUAD $0x7b7b7b7b7b7b7b7b; QUAD $0x7b7b7b7b7b7b7b7b  // .space 16, '{{{{{{{{{{{{{{{{'
	//0x00000060 LCPI0_6
	0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, //0x00000060 QUAD $0x5b5b5b5b5b5b5b5b; QUAD $0x5b5b5b5b5b5b5b5b  // .space 16, '[[[[[[[[[[[[[[[['
	//0x00000070 LCPI0_7
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000070 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	//0x00000080 LCPI0_8
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000080 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	//0x00000090 LCPI0_9
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x00000090 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	//0x000000a0 LCPI0_10
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x000000a0 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x000000b0 LCPI0_11
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x000000b0 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x000000c0 LCPI0_12
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x000000c0 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x000000d0 LCPI0_13
	0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, //0x000000d0 QUAD $0x6565656565656565; QUAD $0x6565656565656565  // .space 16, 'eeeeeeeeeeeeeeee'
	//0x000000e0 .p2align 4, 0x90
	//0x000000e0 _get_by_path
	0x55, //0x000000e0 pushq        %rbp
	0x48, 0x89, 0xe5, //0x000000e1 movq         %rsp, %rbp
	0x41, 0x57, //0x000000e4 pushq        %r15
	0x41, 0x56, //0x000000e6 pushq        %r14
	0x41, 0x55, //0x000000e8 pushq        %r13
	0x41, 0x54, //0x000000ea pushq        %r12
	0x53, //0x000000ec pushq        %rbx
	0x48, 0x81, 0xec, 0xa8, 0x00, 0x00, 0x00, //0x000000ed subq         $168, %rsp
	0x49, 0x89, 0xce, //0x000000f4 movq         %rcx, %r14
	0x49, 0x89, 0xf3, //0x000000f7 movq         %rsi, %r11
	0x49, 0x89, 0xfd, //0x000000fa movq         %rdi, %r13
	0x48, 0x8b, 0x42, 0x08, //0x000000fd movq         $8(%rdx), %rax
	0x48, 0x85, 0xc0, //0x00000101 testq        %rax, %rax
	0x48, 0x89, 0x75, 0xd0, //0x00000104 movq         %rsi, $-48(%rbp)
	0x48, 0x89, 0x7d, 0xc0, //0x00000108 movq         %rdi, $-64(%rbp)
	0x48, 0x89, 0x4d, 0xa8, //0x0000010c movq         %rcx, $-88(%rbp)
	0x0f, 0x84, 0x22, 0x2f, 0x00, 0x00, //0x00000110 je           LBB0_448
	0x4c, 0x8b, 0x0a, //0x00000116 movq         (%rdx), %r9
	0x48, 0xc1, 0xe0, 0x04, //0x00000119 shlq         $4, %rax
	0x4c, 0x01, 0xc8, //0x0000011d addq         %r9, %rax
	0x48, 0x89, 0x85, 0x30, 0xff, 0xff, 0xff, //0x00000120 movq         %rax, $-208(%rbp)
	0x4d, 0x8d, 0x45, 0x08, //0x00000127 leaq         $8(%r13), %r8
	0x49, 0x8b, 0x7d, 0x00, //0x0000012b movq         (%r13), %rdi
	0x49, 0x8b, 0x03, //0x0000012f movq         (%r11), %rax
	0x49, 0xba, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00000132 movabsq      $4294977024, %r10
	0xf3, 0x0f, 0x6f, 0x05, 0xec, 0xfe, 0xff, 0xff, //0x0000013c movdqu       $-276(%rip), %xmm0  /* LCPI0_3+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0xf4, 0xfe, 0xff, 0xff, //0x00000144 movdqu       $-268(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x2d, 0xab, 0xfe, 0xff, 0xff, //0x0000014c movdqu       $-341(%rip), %xmm13  /* LCPI0_0+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x35, 0xb2, 0xfe, 0xff, 0xff, //0x00000155 movdqu       $-334(%rip), %xmm14  /* LCPI0_1+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x25, 0xba, 0xfe, 0xff, 0xff, //0x0000015e movdqu       $-326(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0x66, 0x45, 0x0f, 0x76, 0xc9, //0x00000166 pcmpeqd      %xmm9, %xmm9
	0xf3, 0x44, 0x0f, 0x6f, 0x15, 0xec, 0xfe, 0xff, 0xff, //0x0000016b movdqu       $-276(%rip), %xmm10  /* LCPI0_6+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x1d, 0xf3, 0xfe, 0xff, 0xff, //0x00000174 movdqu       $-269(%rip), %xmm11  /* LCPI0_7+0(%rip) */
	0x66, 0x45, 0x0f, 0xef, 0xc0, //0x0000017d pxor         %xmm8, %xmm8
	0xf3, 0x44, 0x0f, 0x6f, 0x25, 0xc5, 0xfe, 0xff, 0xff, //0x00000182 movdqu       $-315(%rip), %xmm12  /* LCPI0_5+0(%rip) */
	0x4c, 0x89, 0x45, 0xb0, //0x0000018b movq         %r8, $-80(%rbp)
	//0x0000018f LBB0_2
	0x49, 0x8b, 0x08, //0x0000018f movq         (%r8), %rcx
	0x48, 0x89, 0xc2, //0x00000192 movq         %rax, %rdx
	0x48, 0x29, 0xca, //0x00000195 subq         %rcx, %rdx
	0x0f, 0x83, 0x32, 0x00, 0x00, 0x00, //0x00000198 jae          LBB0_7
	0x8a, 0x1c, 0x07, //0x0000019e movb         (%rdi,%rax), %bl
	0x80, 0xfb, 0x0d, //0x000001a1 cmpb         $13, %bl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x000001a4 je           LBB0_7
	0x80, 0xfb, 0x20, //0x000001aa cmpb         $32, %bl
	0x0f, 0x84, 0x1d, 0x00, 0x00, 0x00, //0x000001ad je           LBB0_7
	0x80, 0xc3, 0xf7, //0x000001b3 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x000001b6 cmpb         $1, %bl
	0x0f, 0x86, 0x11, 0x00, 0x00, 0x00, //0x000001b9 jbe          LBB0_7
	0x48, 0x89, 0xc6, //0x000001bf movq         %rax, %rsi
	0xe9, 0x39, 0x01, 0x00, 0x00, //0x000001c2 jmp          LBB0_27
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000001c7 .p2align 4, 0x90
	//0x000001d0 LBB0_7
	0x48, 0x8d, 0x70, 0x01, //0x000001d0 leaq         $1(%rax), %rsi
	0x48, 0x39, 0xce, //0x000001d4 cmpq         %rcx, %rsi
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000001d7 jae          LBB0_11
	0x8a, 0x1c, 0x37, //0x000001dd movb         (%rdi,%rsi), %bl
	0x80, 0xfb, 0x0d, //0x000001e0 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x000001e3 je           LBB0_11
	0x80, 0xfb, 0x20, //0x000001e9 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x000001ec je           LBB0_11
	0x80, 0xc3, 0xf7, //0x000001f2 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x000001f5 cmpb         $1, %bl
	0x0f, 0x87, 0x02, 0x01, 0x00, 0x00, //0x000001f8 ja           LBB0_27
	0x90, 0x90, //0x000001fe .p2align 4, 0x90
	//0x00000200 LBB0_11
	0x48, 0x8d, 0x70, 0x02, //0x00000200 leaq         $2(%rax), %rsi
	0x48, 0x39, 0xce, //0x00000204 cmpq         %rcx, %rsi
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000207 jae          LBB0_15
	0x8a, 0x1c, 0x37, //0x0000020d movb         (%rdi,%rsi), %bl
	0x80, 0xfb, 0x0d, //0x00000210 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000213 je           LBB0_15
	0x80, 0xfb, 0x20, //0x00000219 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x0000021c je           LBB0_15
	0x80, 0xc3, 0xf7, //0x00000222 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000225 cmpb         $1, %bl
	0x0f, 0x87, 0xd2, 0x00, 0x00, 0x00, //0x00000228 ja           LBB0_27
	0x90, 0x90, //0x0000022e .p2align 4, 0x90
	//0x00000230 LBB0_15
	0x48, 0x8d, 0x70, 0x03, //0x00000230 leaq         $3(%rax), %rsi
	0x48, 0x39, 0xce, //0x00000234 cmpq         %rcx, %rsi
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000237 jae          LBB0_19
	0x8a, 0x1c, 0x37, //0x0000023d movb         (%rdi,%rsi), %bl
	0x80, 0xfb, 0x0d, //0x00000240 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000243 je           LBB0_19
	0x80, 0xfb, 0x20, //0x00000249 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x0000024c je           LBB0_19
	0x80, 0xc3, 0xf7, //0x00000252 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000255 cmpb         $1, %bl
	0x0f, 0x87, 0xa2, 0x00, 0x00, 0x00, //0x00000258 ja           LBB0_27
	0x90, 0x90, //0x0000025e .p2align 4, 0x90
	//0x00000260 LBB0_19
	0x4c, 0x8d, 0x70, 0x04, //0x00000260 leaq         $4(%rax), %r14
	0x4c, 0x39, 0xf1, //0x00000264 cmpq         %r14, %rcx
	0x0f, 0x86, 0x63, 0x00, 0x00, 0x00, //0x00000267 jbe          LBB0_551
	0x4c, 0x39, 0xf1, //0x0000026d cmpq         %r14, %rcx
	0x0f, 0x84, 0x70, 0x00, 0x00, 0x00, //0x00000270 je           LBB0_26
	0x4c, 0x8d, 0x34, 0x0f, //0x00000276 leaq         (%rdi,%rcx), %r14
	0x48, 0x83, 0xc2, 0x04, //0x0000027a addq         $4, %rdx
	0x48, 0x89, 0xfb, //0x0000027e movq         %rdi, %rbx
	0x48, 0x8d, 0x74, 0x07, 0x05, //0x00000281 leaq         $5(%rdi,%rax), %rsi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000286 .p2align 4, 0x90
	//0x00000290 LBB0_22
	0x0f, 0xbe, 0x7e, 0xff, //0x00000290 movsbl       $-1(%rsi), %edi
	0x83, 0xff, 0x20, //0x00000294 cmpl         $32, %edi
	0x0f, 0x87, 0x7e, 0x00, 0x00, 0x00, //0x00000297 ja           LBB0_28
	0x49, 0x0f, 0xa3, 0xfa, //0x0000029d btq          %rdi, %r10
	0x0f, 0x83, 0x74, 0x00, 0x00, 0x00, //0x000002a1 jae          LBB0_28
	0x48, 0xff, 0xc6, //0x000002a7 incq         %rsi
	0x48, 0xff, 0xc2, //0x000002aa incq         %rdx
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x000002ad jne          LBB0_22
	0x48, 0x89, 0xdf, //0x000002b3 movq         %rbx, %rdi
	0x49, 0x29, 0xfe, //0x000002b6 subq         %rdi, %r14
	0x4c, 0x89, 0xf6, //0x000002b9 movq         %r14, %rsi
	0x48, 0x39, 0xce, //0x000002bc cmpq         %rcx, %rsi
	0x0f, 0x82, 0x3b, 0x00, 0x00, 0x00, //0x000002bf jb           LBB0_27
	0xe9, 0x66, 0x00, 0x00, 0x00, //0x000002c5 jmp          LBB0_29
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000002ca .p2align 4, 0x90
	//0x000002d0 LBB0_551
	0x4d, 0x89, 0x33, //0x000002d0 movq         %r14, (%r11)
	0x31, 0xc9, //0x000002d3 xorl         %ecx, %ecx
	0x49, 0x8b, 0x01, //0x000002d5 movq         (%r9), %rax
	0x48, 0x85, 0xc0, //0x000002d8 testq        %rax, %rax
	0x0f, 0x85, 0x6f, 0x00, 0x00, 0x00, //0x000002db jne          LBB0_30
	0xe9, 0x1c, 0x2f, 0x00, 0x00, //0x000002e1 jmp          LBB0_552
	//0x000002e6 LBB0_26
	0x49, 0x01, 0xfe, //0x000002e6 addq         %rdi, %r14
	0x49, 0x29, 0xfe, //0x000002e9 subq         %rdi, %r14
	0x4c, 0x89, 0xf6, //0x000002ec movq         %r14, %rsi
	0x48, 0x39, 0xce, //0x000002ef cmpq         %rcx, %rsi
	0x0f, 0x83, 0x38, 0x00, 0x00, 0x00, //0x000002f2 jae          LBB0_29
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000002f8 .p2align 4, 0x90
	//0x00000300 LBB0_27
	0x4c, 0x8d, 0x76, 0x01, //0x00000300 leaq         $1(%rsi), %r14
	0x4d, 0x89, 0x33, //0x00000304 movq         %r14, (%r11)
	0x8a, 0x0c, 0x37, //0x00000307 movb         (%rdi,%rsi), %cl
	0x49, 0x8b, 0x01, //0x0000030a movq         (%r9), %rax
	0x48, 0x85, 0xc0, //0x0000030d testq        %rax, %rax
	0x0f, 0x85, 0x3a, 0x00, 0x00, 0x00, //0x00000310 jne          LBB0_30
	0xe9, 0xe7, 0x2e, 0x00, 0x00, //0x00000316 jmp          LBB0_552
	//0x0000031b LBB0_28
	0x48, 0x89, 0xdf, //0x0000031b movq         %rbx, %rdi
	0x48, 0x89, 0xda, //0x0000031e movq         %rbx, %rdx
	0x48, 0xf7, 0xd2, //0x00000321 notq         %rdx
	0x48, 0x01, 0xd6, //0x00000324 addq         %rdx, %rsi
	0x48, 0x39, 0xce, //0x00000327 cmpq         %rcx, %rsi
	0x0f, 0x82, 0xd0, 0xff, 0xff, 0xff, //0x0000032a jb           LBB0_27
	//0x00000330 LBB0_29
	0x31, 0xc9, //0x00000330 xorl         %ecx, %ecx
	0x49, 0x89, 0xc6, //0x00000332 movq         %rax, %r14
	0x49, 0x8b, 0x01, //0x00000335 movq         (%r9), %rax
	0x48, 0x85, 0xc0, //0x00000338 testq        %rax, %rax
	0x0f, 0x84, 0xc1, 0x2e, 0x00, 0x00, //0x0000033b je           LBB0_552
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000341 .p2align 4, 0x90
	//0x00000350 LBB0_30
	0x8a, 0x40, 0x17, //0x00000350 movb         $23(%rax), %al
	0x24, 0x1f, //0x00000353 andb         $31, %al
	0x3c, 0x02, //0x00000355 cmpb         $2, %al
	0x0f, 0x84, 0x23, 0x00, 0x00, 0x00, //0x00000357 je           LBB0_344
	0x3c, 0x18, //0x0000035d cmpb         $24, %al
	0x0f, 0x85, 0x9d, 0x2e, 0x00, 0x00, //0x0000035f jne          LBB0_552
	0x80, 0xf9, 0x7b, //0x00000365 cmpb         $123, %cl
	0x4c, 0x89, 0x4d, 0xa0, //0x00000368 movq         %r9, $-96(%rbp)
	0x0f, 0x84, 0x92, 0x00, 0x00, 0x00, //0x0000036c je           LBB0_33
	0xe9, 0xcb, 0x2e, 0x00, 0x00, //0x00000372 jmp          LBB0_556
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000377 .p2align 4, 0x90
	//0x00000380 LBB0_344
	0x80, 0xf9, 0x5b, //0x00000380 cmpb         $91, %cl
	0x0f, 0x85, 0xb9, 0x2e, 0x00, 0x00, //0x00000383 jne          LBB0_556
	0x49, 0x8b, 0x41, 0x08, //0x00000389 movq         $8(%r9), %rax
	0x4c, 0x8b, 0x20, //0x0000038d movq         (%rax), %r12
	0x4d, 0x85, 0xe4, //0x00000390 testq        %r12, %r12
	0x0f, 0x88, 0x69, 0x2e, 0x00, 0x00, //0x00000393 js           LBB0_552
	0x49, 0x8b, 0x00, //0x00000399 movq         (%r8), %rax
	0x4c, 0x89, 0xf1, //0x0000039c movq         %r14, %rcx
	0x48, 0x29, 0xc1, //0x0000039f subq         %rax, %rcx
	0x0f, 0x83, 0x08, 0x1c, 0x00, 0x00, //0x000003a2 jae          LBB0_351
	0x42, 0x8a, 0x14, 0x37, //0x000003a8 movb         (%rdi,%r14), %dl
	0x80, 0xfa, 0x0d, //0x000003ac cmpb         $13, %dl
	0x0f, 0x84, 0xfb, 0x1b, 0x00, 0x00, //0x000003af je           LBB0_351
	0x80, 0xfa, 0x20, //0x000003b5 cmpb         $32, %dl
	0x0f, 0x84, 0xf2, 0x1b, 0x00, 0x00, //0x000003b8 je           LBB0_351
	0x80, 0xc2, 0xf7, //0x000003be addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x000003c1 cmpb         $1, %dl
	0x0f, 0x86, 0xe6, 0x1b, 0x00, 0x00, //0x000003c4 jbe          LBB0_351
	0x4c, 0x89, 0xf2, //0x000003ca movq         %r14, %rdx
	0xe9, 0x0f, 0x1d, 0x00, 0x00, //0x000003cd jmp          LBB0_373
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000003d2 .p2align 4, 0x90
	//0x000003e0 LBB0_248
	0x48, 0x01, 0xfe, //0x000003e0 addq         %rdi, %rsi
	0x48, 0x29, 0xfe, //0x000003e3 subq         %rdi, %rsi
	0x48, 0x89, 0xf2, //0x000003e6 movq         %rsi, %rdx
	0x48, 0x39, 0xc2, //0x000003e9 cmpq         %rax, %rdx
	0x0f, 0x83, 0x50, 0x2e, 0x00, 0x00, //0x000003ec jae          LBB0_556
	//0x000003f2 LBB0_250
	0x4c, 0x8d, 0x72, 0x01, //0x000003f2 leaq         $1(%rdx), %r14
	0x4d, 0x89, 0x33, //0x000003f6 movq         %r14, (%r11)
	0x8a, 0x04, 0x17, //0x000003f9 movb         (%rdi,%rdx), %al
	0x3c, 0x2c, //0x000003fc cmpb         $44, %al
	0x0f, 0x85, 0x1e, 0x2e, 0x00, 0x00, //0x000003fe jne          LBB0_251
	//0x00000404 LBB0_33
	0x49, 0x8b, 0x08, //0x00000404 movq         (%r8), %rcx
	0x4c, 0x89, 0xf2, //0x00000407 movq         %r14, %rdx
	0x48, 0x29, 0xca, //0x0000040a subq         %rcx, %rdx
	0x48, 0x89, 0x7d, 0xc8, //0x0000040d movq         %rdi, $-56(%rbp)
	0x0f, 0x83, 0x29, 0x00, 0x00, 0x00, //0x00000411 jae          LBB0_38
	0x42, 0x8a, 0x04, 0x37, //0x00000417 movb         (%rdi,%r14), %al
	0x3c, 0x0d, //0x0000041b cmpb         $13, %al
	0x0f, 0x84, 0x1d, 0x00, 0x00, 0x00, //0x0000041d je           LBB0_38
	0x3c, 0x20, //0x00000423 cmpb         $32, %al
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000425 je           LBB0_38
	0x04, 0xf7, //0x0000042b addb         $-9, %al
	0x3c, 0x01, //0x0000042d cmpb         $1, %al
	0x0f, 0x86, 0x0b, 0x00, 0x00, 0x00, //0x0000042f jbe          LBB0_38
	0x4c, 0x89, 0xf0, //0x00000435 movq         %r14, %rax
	0xe9, 0x39, 0x01, 0x00, 0x00, //0x00000438 jmp          LBB0_59
	0x90, 0x90, 0x90, //0x0000043d .p2align 4, 0x90
	//0x00000440 LBB0_38
	0x49, 0x8d, 0x46, 0x01, //0x00000440 leaq         $1(%r14), %rax
	0x48, 0x39, 0xc8, //0x00000444 cmpq         %rcx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000447 jae          LBB0_42
	0x8a, 0x1c, 0x07, //0x0000044d movb         (%rdi,%rax), %bl
	0x80, 0xfb, 0x0d, //0x00000450 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000453 je           LBB0_42
	0x80, 0xfb, 0x20, //0x00000459 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x0000045c je           LBB0_42
	0x80, 0xc3, 0xf7, //0x00000462 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000465 cmpb         $1, %bl
	0x0f, 0x87, 0x08, 0x01, 0x00, 0x00, //0x00000468 ja           LBB0_59
	0x90, 0x90, //0x0000046e .p2align 4, 0x90
	//0x00000470 LBB0_42
	0x49, 0x8d, 0x46, 0x02, //0x00000470 leaq         $2(%r14), %rax
	0x48, 0x39, 0xc8, //0x00000474 cmpq         %rcx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000477 jae          LBB0_46
	0x8a, 0x1c, 0x07, //0x0000047d movb         (%rdi,%rax), %bl
	0x80, 0xfb, 0x0d, //0x00000480 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000483 je           LBB0_46
	0x80, 0xfb, 0x20, //0x00000489 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x0000048c je           LBB0_46
	0x80, 0xc3, 0xf7, //0x00000492 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000495 cmpb         $1, %bl
	0x0f, 0x87, 0xd8, 0x00, 0x00, 0x00, //0x00000498 ja           LBB0_59
	0x90, 0x90, //0x0000049e .p2align 4, 0x90
	//0x000004a0 LBB0_46
	0x49, 0x8d, 0x46, 0x03, //0x000004a0 leaq         $3(%r14), %rax
	0x48, 0x39, 0xc8, //0x000004a4 cmpq         %rcx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000004a7 jae          LBB0_50
	0x8a, 0x1c, 0x07, //0x000004ad movb         (%rdi,%rax), %bl
	0x80, 0xfb, 0x0d, //0x000004b0 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x000004b3 je           LBB0_50
	0x80, 0xfb, 0x20, //0x000004b9 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x000004bc je           LBB0_50
	0x80, 0xc3, 0xf7, //0x000004c2 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x000004c5 cmpb         $1, %bl
	0x0f, 0x87, 0xa8, 0x00, 0x00, 0x00, //0x000004c8 ja           LBB0_59
	0x90, 0x90, //0x000004ce .p2align 4, 0x90
	//0x000004d0 LBB0_50
	0x49, 0x8d, 0x76, 0x04, //0x000004d0 leaq         $4(%r14), %rsi
	0x48, 0x39, 0xf1, //0x000004d4 cmpq         %rsi, %rcx
	0x0f, 0x86, 0x0d, 0x2d, 0x00, 0x00, //0x000004d7 jbe          LBB0_549
	0x48, 0x39, 0xf1, //0x000004dd cmpq         %rsi, %rcx
	0x0f, 0x84, 0x5a, 0x00, 0x00, 0x00, //0x000004e0 je           LBB0_57
	0x48, 0x8d, 0x34, 0x0f, //0x000004e6 leaq         (%rdi,%rcx), %rsi
	0x48, 0x83, 0xc2, 0x04, //0x000004ea addq         $4, %rdx
	0x4a, 0x8d, 0x44, 0x37, 0x05, //0x000004ee leaq         $5(%rdi,%r14), %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000004f3 .p2align 4, 0x90
	//0x00000500 LBB0_53
	0x0f, 0xbe, 0x78, 0xff, //0x00000500 movsbl       $-1(%rax), %edi
	0x83, 0xff, 0x20, //0x00000504 cmpl         $32, %edi
	0x0f, 0x87, 0x53, 0x00, 0x00, 0x00, //0x00000507 ja           LBB0_58
	0x49, 0x0f, 0xa3, 0xfa, //0x0000050d btq          %rdi, %r10
	0x0f, 0x83, 0x49, 0x00, 0x00, 0x00, //0x00000511 jae          LBB0_58
	0x48, 0xff, 0xc0, //0x00000517 incq         %rax
	0x48, 0xff, 0xc2, //0x0000051a incq         %rdx
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x0000051d jne          LBB0_53
	0x48, 0x8b, 0x7d, 0xc8, //0x00000523 movq         $-56(%rbp), %rdi
	0x48, 0x29, 0xfe, //0x00000527 subq         %rdi, %rsi
	0x48, 0x89, 0xf0, //0x0000052a movq         %rsi, %rax
	0x48, 0x39, 0xc8, //0x0000052d cmpq         %rcx, %rax
	0x0f, 0x82, 0x40, 0x00, 0x00, 0x00, //0x00000530 jb           LBB0_59
	0xe9, 0x07, 0x2d, 0x00, 0x00, //0x00000536 jmp          LBB0_556
	0x90, 0x90, 0x90, 0x90, 0x90, //0x0000053b .p2align 4, 0x90
	//0x00000540 LBB0_57
	0x48, 0x01, 0xfe, //0x00000540 addq         %rdi, %rsi
	0x48, 0x29, 0xfe, //0x00000543 subq         %rdi, %rsi
	0x48, 0x89, 0xf0, //0x00000546 movq         %rsi, %rax
	0x48, 0x39, 0xc8, //0x00000549 cmpq         %rcx, %rax
	0x0f, 0x82, 0x24, 0x00, 0x00, 0x00, //0x0000054c jb           LBB0_59
	0xe9, 0xeb, 0x2c, 0x00, 0x00, //0x00000552 jmp          LBB0_556
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000557 .p2align 4, 0x90
	//0x00000560 LBB0_58
	0x48, 0x8b, 0x7d, 0xc8, //0x00000560 movq         $-56(%rbp), %rdi
	0x48, 0x89, 0xfa, //0x00000564 movq         %rdi, %rdx
	0x48, 0xf7, 0xd2, //0x00000567 notq         %rdx
	0x48, 0x01, 0xd0, //0x0000056a addq         %rdx, %rax
	0x48, 0x39, 0xc8, //0x0000056d cmpq         %rcx, %rax
	0x0f, 0x83, 0xcc, 0x2c, 0x00, 0x00, //0x00000570 jae          LBB0_556
	//0x00000576 LBB0_59
	0x4c, 0x8d, 0x70, 0x01, //0x00000576 leaq         $1(%rax), %r14
	0x4d, 0x89, 0x33, //0x0000057a movq         %r14, (%r11)
	0x8a, 0x0c, 0x07, //0x0000057d movb         (%rdi,%rax), %cl
	0x80, 0xf9, 0x22, //0x00000580 cmpb         $34, %cl
	0x0f, 0x85, 0x8b, 0x2c, 0x00, 0x00, //0x00000583 jne          LBB0_553
	0x49, 0x8b, 0x18, //0x00000589 movq         (%r8), %rbx
	0x48, 0x89, 0xd9, //0x0000058c movq         %rbx, %rcx
	0x4c, 0x29, 0xf1, //0x0000058f subq         %r14, %rcx
	0x0f, 0x84, 0x55, 0x4f, 0x00, 0x00, //0x00000592 je           LBB0_950
	0x49, 0x8b, 0x51, 0x08, //0x00000598 movq         $8(%r9), %rdx
	0x48, 0x8b, 0x32, //0x0000059c movq         (%rdx), %rsi
	0x48, 0x89, 0x75, 0x90, //0x0000059f movq         %rsi, $-112(%rbp)
	0x48, 0x8b, 0x52, 0x08, //0x000005a3 movq         $8(%rdx), %rdx
	0x48, 0x89, 0x55, 0x98, //0x000005a7 movq         %rdx, $-104(%rbp)
	0x49, 0x01, 0xfe, //0x000005ab addq         %rdi, %r14
	0x48, 0x83, 0xf9, 0x40, //0x000005ae cmpq         $64, %rcx
	0x4c, 0x89, 0x75, 0xb8, //0x000005b2 movq         %r14, $-72(%rbp)
	0x48, 0x89, 0x9d, 0x38, 0xff, 0xff, 0xff, //0x000005b6 movq         %rbx, $-200(%rbp)
	0x0f, 0x82, 0x35, 0x13, 0x00, 0x00, //0x000005bd jb           LBB0_105
	0x89, 0xca, //0x000005c3 movl         %ecx, %edx
	0x83, 0xe2, 0x3f, //0x000005c5 andl         $63, %edx
	0x48, 0x89, 0x55, 0x88, //0x000005c8 movq         %rdx, $-120(%rbp)
	0x48, 0x89, 0xde, //0x000005cc movq         %rbx, %rsi
	0x48, 0x29, 0xc6, //0x000005cf subq         %rax, %rsi
	0x48, 0x83, 0xc6, 0xbf, //0x000005d2 addq         $-65, %rsi
	0x48, 0x83, 0xe6, 0xc0, //0x000005d6 andq         $-64, %rsi
	0x48, 0x01, 0xc6, //0x000005da addq         %rax, %rsi
	0x48, 0x8d, 0x54, 0x37, 0x41, //0x000005dd leaq         $65(%rdi,%rsi), %rdx
	0x48, 0x89, 0x55, 0x80, //0x000005e2 movq         %rdx, $-128(%rbp)
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000005e6 movq         $-1, %r11
	0x4c, 0x89, 0xf6, //0x000005ed movq         %r14, %rsi
	0x31, 0xdb, //0x000005f0 xorl         %ebx, %ebx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000005f2 .p2align 4, 0x90
	//0x00000600 LBB0_63
	0xf3, 0x0f, 0x6f, 0x1e, //0x00000600 movdqu       (%rsi), %xmm3
	0xf3, 0x0f, 0x6f, 0x6e, 0x10, //0x00000604 movdqu       $16(%rsi), %xmm5
	0xf3, 0x0f, 0x6f, 0x76, 0x20, //0x00000609 movdqu       $32(%rsi), %xmm6
	0xf3, 0x0f, 0x6f, 0x7e, 0x30, //0x0000060e movdqu       $48(%rsi), %xmm7
	0x66, 0x0f, 0x6f, 0xd3, //0x00000613 movdqa       %xmm3, %xmm2
	0x66, 0x0f, 0x74, 0xd0, //0x00000617 pcmpeqb      %xmm0, %xmm2
	0x66, 0x0f, 0xd7, 0xfa, //0x0000061b pmovmskb     %xmm2, %edi
	0x66, 0x0f, 0x6f, 0xd5, //0x0000061f movdqa       %xmm5, %xmm2
	0x66, 0x0f, 0x74, 0xd0, //0x00000623 pcmpeqb      %xmm0, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xe2, //0x00000627 pmovmskb     %xmm2, %r12d
	0x66, 0x0f, 0x6f, 0xd6, //0x0000062c movdqa       %xmm6, %xmm2
	0x66, 0x0f, 0x74, 0xd0, //0x00000630 pcmpeqb      %xmm0, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xfa, //0x00000634 pmovmskb     %xmm2, %r15d
	0x66, 0x0f, 0x6f, 0xd7, //0x00000639 movdqa       %xmm7, %xmm2
	0x66, 0x0f, 0x74, 0xd0, //0x0000063d pcmpeqb      %xmm0, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xc2, //0x00000641 pmovmskb     %xmm2, %r8d
	0x66, 0x0f, 0x74, 0xd9, //0x00000646 pcmpeqb      %xmm1, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xcb, //0x0000064a pmovmskb     %xmm3, %r9d
	0x66, 0x0f, 0x74, 0xe9, //0x0000064f pcmpeqb      %xmm1, %xmm5
	0x66, 0x44, 0x0f, 0xd7, 0xed, //0x00000653 pmovmskb     %xmm5, %r13d
	0x66, 0x0f, 0x74, 0xf1, //0x00000658 pcmpeqb      %xmm1, %xmm6
	0x66, 0x44, 0x0f, 0xd7, 0xd6, //0x0000065c pmovmskb     %xmm6, %r10d
	0x66, 0x0f, 0x74, 0xf9, //0x00000661 pcmpeqb      %xmm1, %xmm7
	0x66, 0x44, 0x0f, 0xd7, 0xf7, //0x00000665 pmovmskb     %xmm7, %r14d
	0x49, 0xc1, 0xe0, 0x30, //0x0000066a shlq         $48, %r8
	0x49, 0xc1, 0xe7, 0x20, //0x0000066e shlq         $32, %r15
	0x49, 0xc1, 0xe4, 0x10, //0x00000672 shlq         $16, %r12
	0x4c, 0x09, 0xe7, //0x00000676 orq          %r12, %rdi
	0x4c, 0x09, 0xff, //0x00000679 orq          %r15, %rdi
	0x49, 0xc1, 0xe6, 0x30, //0x0000067c shlq         $48, %r14
	0x49, 0xc1, 0xe2, 0x20, //0x00000680 shlq         $32, %r10
	0x49, 0xc1, 0xe5, 0x10, //0x00000684 shlq         $16, %r13
	0x4d, 0x09, 0xe9, //0x00000688 orq          %r13, %r9
	0x4d, 0x09, 0xd1, //0x0000068b orq          %r10, %r9
	0x4d, 0x09, 0xf1, //0x0000068e orq          %r14, %r9
	0x49, 0x83, 0xfb, 0xff, //0x00000691 cmpq         $-1, %r11
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00000695 jne          LBB0_65
	0x4d, 0x85, 0xc9, //0x0000069b testq        %r9, %r9
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x0000069e jne          LBB0_74
	//0x000006a4 LBB0_65
	0x4c, 0x09, 0xc7, //0x000006a4 orq          %r8, %rdi
	0x4c, 0x89, 0xca, //0x000006a7 movq         %r9, %rdx
	0x48, 0x09, 0xda, //0x000006aa orq          %rbx, %rdx
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x000006ad jne          LBB0_75
	//0x000006b3 LBB0_66
	0x48, 0x85, 0xff, //0x000006b3 testq        %rdi, %rdi
	0x0f, 0x85, 0x84, 0x00, 0x00, 0x00, //0x000006b6 jne          LBB0_76
	//0x000006bc LBB0_67
	0x48, 0x83, 0xc1, 0xc0, //0x000006bc addq         $-64, %rcx
	0x48, 0x83, 0xc6, 0x40, //0x000006c0 addq         $64, %rsi
	0x48, 0x83, 0xf9, 0x3f, //0x000006c4 cmpq         $63, %rcx
	0x0f, 0x87, 0x32, 0xff, 0xff, 0xff, //0x000006c8 ja           LBB0_63
	0xe9, 0x6d, 0x11, 0x00, 0x00, //0x000006ce jmp          LBB0_68
	//0x000006d3 LBB0_74
	0x49, 0x89, 0xf2, //0x000006d3 movq         %rsi, %r10
	0x4c, 0x2b, 0x55, 0xc8, //0x000006d6 subq         $-56(%rbp), %r10
	0x4d, 0x0f, 0xbc, 0xd9, //0x000006da bsfq         %r9, %r11
	0x4d, 0x01, 0xd3, //0x000006de addq         %r10, %r11
	0x4c, 0x09, 0xc7, //0x000006e1 orq          %r8, %rdi
	0x4c, 0x89, 0xca, //0x000006e4 movq         %r9, %rdx
	0x48, 0x09, 0xda, //0x000006e7 orq          %rbx, %rdx
	0x0f, 0x84, 0xc3, 0xff, 0xff, 0xff, //0x000006ea je           LBB0_66
	//0x000006f0 LBB0_75
	0x49, 0x89, 0xda, //0x000006f0 movq         %rbx, %r10
	0x49, 0xf7, 0xd2, //0x000006f3 notq         %r10
	0x4d, 0x21, 0xca, //0x000006f6 andq         %r9, %r10
	0x4f, 0x8d, 0x04, 0x12, //0x000006f9 leaq         (%r10,%r10), %r8
	0x49, 0x09, 0xd8, //0x000006fd orq          %rbx, %r8
	0x4d, 0x89, 0xc6, //0x00000700 movq         %r8, %r14
	0x49, 0xf7, 0xd6, //0x00000703 notq         %r14
	0x4d, 0x21, 0xce, //0x00000706 andq         %r9, %r14
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000709 movabsq      $-6148914691236517206, %rbx
	0x49, 0x21, 0xde, //0x00000713 andq         %rbx, %r14
	0x31, 0xdb, //0x00000716 xorl         %ebx, %ebx
	0x4d, 0x01, 0xd6, //0x00000718 addq         %r10, %r14
	0x0f, 0x92, 0xc3, //0x0000071b setb         %bl
	0x4d, 0x01, 0xf6, //0x0000071e addq         %r14, %r14
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000721 movabsq      $6148914691236517205, %rdx
	0x49, 0x31, 0xd6, //0x0000072b xorq         %rdx, %r14
	0x4d, 0x21, 0xc6, //0x0000072e andq         %r8, %r14
	0x49, 0xf7, 0xd6, //0x00000731 notq         %r14
	0x4c, 0x21, 0xf7, //0x00000734 andq         %r14, %rdi
	0x48, 0x85, 0xff, //0x00000737 testq        %rdi, %rdi
	0x0f, 0x84, 0x7c, 0xff, 0xff, 0xff, //0x0000073a je           LBB0_67
	//0x00000740 .p2align 4, 0x90
	//0x00000740 LBB0_76
	0x48, 0x0f, 0xbc, 0xcf, //0x00000740 bsfq         %rdi, %rcx
	0x48, 0x8b, 0x7d, 0xc8, //0x00000744 movq         $-56(%rbp), %rdi
	0x48, 0x29, 0xfe, //0x00000748 subq         %rdi, %rsi
	0x4c, 0x8d, 0x74, 0x0e, 0x01, //0x0000074b leaq         $1(%rsi,%rcx), %r14
	0x4c, 0x8b, 0x6d, 0xc0, //0x00000750 movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x4d, 0xa0, //0x00000754 movq         $-96(%rbp), %r9
	0x4c, 0x8b, 0x45, 0xb0, //0x00000758 movq         $-80(%rbp), %r8
	0x49, 0xba, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000075c movabsq      $4294977024, %r10
	0x4c, 0x8b, 0x7d, 0x90, //0x00000766 movq         $-112(%rbp), %r15
	0x4d, 0x85, 0xf6, //0x0000076a testq        %r14, %r14
	0x48, 0x8b, 0x5d, 0xb8, //0x0000076d movq         $-72(%rbp), %rbx
	0x0f, 0x88, 0x7d, 0x4d, 0x00, 0x00, //0x00000771 js           LBB0_951
	//0x00000777 LBB0_79
	0x48, 0x8b, 0x4d, 0xd0, //0x00000777 movq         $-48(%rbp), %rcx
	0x4c, 0x89, 0x31, //0x0000077b movq         %r14, (%rcx)
	0x49, 0x83, 0xfb, 0xff, //0x0000077e cmpq         $-1, %r11
	0x0f, 0x84, 0x09, 0x00, 0x00, 0x00, //0x00000782 je           LBB0_81
	0x4d, 0x39, 0xf3, //0x00000788 cmpq         %r14, %r11
	0x0f, 0x8e, 0x83, 0x11, 0x00, 0x00, //0x0000078b jle          LBB0_107
	//0x00000791 LBB0_81
	0x4c, 0x89, 0xf6, //0x00000791 movq         %r14, %rsi
	0x48, 0x29, 0xc6, //0x00000794 subq         %rax, %rsi
	0x48, 0x83, 0xc6, 0xfe, //0x00000797 addq         $-2, %rsi
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x0000079b movl         $1, %ecx
	0x48, 0x89, 0xf0, //0x000007a0 movq         %rsi, %rax
	0x48, 0x8b, 0x55, 0x98, //0x000007a3 movq         $-104(%rbp), %rdx
	0x48, 0x09, 0xd0, //0x000007a7 orq          %rdx, %rax
	0x0f, 0x84, 0x00, 0x01, 0x00, 0x00, //0x000007aa je           LBB0_92
	0x48, 0x39, 0xd6, //0x000007b0 cmpq         %rdx, %rsi
	0x4c, 0x8b, 0x5d, 0xd0, //0x000007b3 movq         $-48(%rbp), %r11
	0x0f, 0x85, 0x13, 0x01, 0x00, 0x00, //0x000007b7 jne          LBB0_93
	0x48, 0x89, 0xd0, //0x000007bd movq         %rdx, %rax
	0x48, 0x83, 0xfa, 0x10, //0x000007c0 cmpq         $16, %rdx
	0x0f, 0x82, 0x73, 0x00, 0x00, 0x00, //0x000007c4 jb           LBB0_88
	0x48, 0x83, 0xc0, 0xf0, //0x000007ca addq         $-16, %rax
	0x48, 0x89, 0xc2, //0x000007ce movq         %rax, %rdx
	0x48, 0x83, 0xe2, 0xf0, //0x000007d1 andq         $-16, %rdx
	0x48, 0x8d, 0x74, 0x13, 0x10, //0x000007d5 leaq         $16(%rbx,%rdx), %rsi
	0x48, 0x8b, 0x7d, 0x90, //0x000007da movq         $-112(%rbp), %rdi
	0x48, 0x8d, 0x7c, 0x17, 0x10, //0x000007de leaq         $16(%rdi,%rdx), %rdi
	0x83, 0xe0, 0x0f, //0x000007e3 andl         $15, %eax
	0x31, 0xdb, //0x000007e6 xorl         %ebx, %ebx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000007e8 .p2align 4, 0x90
	//0x000007f0 LBB0_85
	0x48, 0x8b, 0x55, 0xb8, //0x000007f0 movq         $-72(%rbp), %rdx
	0xf3, 0x0f, 0x6f, 0x14, 0x1a, //0x000007f4 movdqu       (%rdx,%rbx), %xmm2
	0x48, 0x8b, 0x55, 0x90, //0x000007f9 movq         $-112(%rbp), %rdx
	0xf3, 0x0f, 0x6f, 0x1c, 0x1a, //0x000007fd movdqu       (%rdx,%rbx), %xmm3
	0x66, 0x0f, 0x74, 0xda, //0x00000802 pcmpeqb      %xmm2, %xmm3
	0x66, 0x0f, 0xd7, 0xd3, //0x00000806 pmovmskb     %xmm3, %edx
	0x66, 0x83, 0xfa, 0xff, //0x0000080a cmpw         $-1, %dx
	0x0f, 0x85, 0x41, 0x01, 0x00, 0x00, //0x0000080e jne          LBB0_98
	0x48, 0x8b, 0x55, 0x98, //0x00000814 movq         $-104(%rbp), %rdx
	0x48, 0x83, 0xc2, 0xf0, //0x00000818 addq         $-16, %rdx
	0x48, 0x83, 0xc3, 0x10, //0x0000081c addq         $16, %rbx
	0x48, 0x89, 0x55, 0x98, //0x00000820 movq         %rdx, $-104(%rbp)
	0x48, 0x83, 0xfa, 0x0f, //0x00000824 cmpq         $15, %rdx
	0x0f, 0x87, 0xc2, 0xff, 0xff, 0xff, //0x00000828 ja           LBB0_85
	0x48, 0x89, 0x45, 0x98, //0x0000082e movq         %rax, $-104(%rbp)
	0x48, 0x89, 0x7d, 0x90, //0x00000832 movq         %rdi, $-112(%rbp)
	0x48, 0x89, 0xf3, //0x00000836 movq         %rsi, %rbx
	0x48, 0x8b, 0x7d, 0xc8, //0x00000839 movq         $-56(%rbp), %rdi
	//0x0000083d LBB0_88
	0x48, 0x8b, 0x75, 0x90, //0x0000083d movq         $-112(%rbp), %rsi
	0x89, 0xf0, //0x00000841 movl         %esi, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x00000843 andl         $4095, %eax
	0x3d, 0xf0, 0x0f, 0x00, 0x00, //0x00000848 cmpl         $4080, %eax
	0x0f, 0x87, 0x93, 0x00, 0x00, 0x00, //0x0000084d ja           LBB0_94
	0x89, 0xd8, //0x00000853 movl         %ebx, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x00000855 andl         $4095, %eax
	0x3d, 0xf1, 0x0f, 0x00, 0x00, //0x0000085a cmpl         $4081, %eax
	0x0f, 0x83, 0x81, 0x00, 0x00, 0x00, //0x0000085f jae          LBB0_94
	0xf3, 0x0f, 0x6f, 0x13, //0x00000865 movdqu       (%rbx), %xmm2
	0xf3, 0x0f, 0x6f, 0x1e, //0x00000869 movdqu       (%rsi), %xmm3
	0x66, 0x0f, 0x74, 0xda, //0x0000086d pcmpeqb      %xmm2, %xmm3
	0x66, 0x0f, 0xd7, 0xc3, //0x00000871 pmovmskb     %xmm3, %eax
	0x66, 0x83, 0xf8, 0xff, //0x00000875 cmpw         $-1, %ax
	0x0f, 0x84, 0xa1, 0x00, 0x00, 0x00, //0x00000879 je           LBB0_100
	0xf7, 0xd0, //0x0000087f notl         %eax
	0x0f, 0xb7, 0xc0, //0x00000881 movzwl       %ax, %eax
	0x48, 0x0f, 0xbc, 0xc0, //0x00000884 bsfq         %rax, %rax
	0x31, 0xc9, //0x00000888 xorl         %ecx, %ecx
	0x48, 0x3b, 0x45, 0x98, //0x0000088a cmpq         $-104(%rbp), %rax
	0x0f, 0x93, 0xc1, //0x0000088e setae        %cl
	0x49, 0x8b, 0x10, //0x00000891 movq         (%r8), %rdx
	0x4c, 0x89, 0xf6, //0x00000894 movq         %r14, %rsi
	0x48, 0x29, 0xd6, //0x00000897 subq         %rdx, %rsi
	0x0f, 0x82, 0x8f, 0x00, 0x00, 0x00, //0x0000089a jb           LBB0_101
	0xe9, 0xdb, 0x00, 0x00, 0x00, //0x000008a0 jmp          LBB0_156
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000008a5 .p2align 4, 0x90
	//0x000008b0 LBB0_92
	0x4c, 0x8b, 0x5d, 0xd0, //0x000008b0 movq         $-48(%rbp), %r11
	0x49, 0x8b, 0x10, //0x000008b4 movq         (%r8), %rdx
	0x4c, 0x89, 0xf6, //0x000008b7 movq         %r14, %rsi
	0x48, 0x29, 0xd6, //0x000008ba subq         %rdx, %rsi
	0x0f, 0x82, 0x6c, 0x00, 0x00, 0x00, //0x000008bd jb           LBB0_101
	0xe9, 0xb8, 0x00, 0x00, 0x00, //0x000008c3 jmp          LBB0_156
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000008c8 .p2align 4, 0x90
	//0x000008d0 LBB0_93
	0x31, 0xc9, //0x000008d0 xorl         %ecx, %ecx
	0x49, 0x8b, 0x10, //0x000008d2 movq         (%r8), %rdx
	0x4c, 0x89, 0xf6, //0x000008d5 movq         %r14, %rsi
	0x48, 0x29, 0xd6, //0x000008d8 subq         %rdx, %rsi
	0x0f, 0x82, 0x4e, 0x00, 0x00, 0x00, //0x000008db jb           LBB0_101
	0xe9, 0x9a, 0x00, 0x00, 0x00, //0x000008e1 jmp          LBB0_156
	//0x000008e6 LBB0_94
	0x48, 0x83, 0x7d, 0x98, 0x00, //0x000008e6 cmpq         $0, $-104(%rbp)
	0x0f, 0x84, 0x2f, 0x00, 0x00, 0x00, //0x000008eb je           LBB0_100
	0x31, 0xc0, //0x000008f1 xorl         %eax, %eax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000008f3 .p2align 4, 0x90
	//0x00000900 LBB0_96
	0x0f, 0xb6, 0x14, 0x03, //0x00000900 movzbl       (%rbx,%rax), %edx
	0x3a, 0x14, 0x06, //0x00000904 cmpb         (%rsi,%rax), %dl
	0x0f, 0x85, 0x62, 0x00, 0x00, 0x00, //0x00000907 jne          LBB0_99
	0x48, 0xff, 0xc0, //0x0000090d incq         %rax
	0x48, 0x39, 0x45, 0x98, //0x00000910 cmpq         %rax, $-104(%rbp)
	0x0f, 0x85, 0xe6, 0xff, 0xff, 0xff, //0x00000914 jne          LBB0_96
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000091a .p2align 4, 0x90
	//0x00000920 LBB0_100
	0x49, 0x8b, 0x10, //0x00000920 movq         (%r8), %rdx
	0x4c, 0x89, 0xf6, //0x00000923 movq         %r14, %rsi
	0x48, 0x29, 0xd6, //0x00000926 subq         %rdx, %rsi
	0x0f, 0x83, 0x51, 0x00, 0x00, 0x00, //0x00000929 jae          LBB0_156
	//0x0000092f LBB0_101
	0x42, 0x8a, 0x04, 0x37, //0x0000092f movb         (%rdi,%r14), %al
	0x3c, 0x0d, //0x00000933 cmpb         $13, %al
	0x0f, 0x84, 0x45, 0x00, 0x00, 0x00, //0x00000935 je           LBB0_156
	0x3c, 0x20, //0x0000093b cmpb         $32, %al
	0x0f, 0x84, 0x3d, 0x00, 0x00, 0x00, //0x0000093d je           LBB0_156
	0x04, 0xf7, //0x00000943 addb         $-9, %al
	0x3c, 0x01, //0x00000945 cmpb         $1, %al
	0x0f, 0x86, 0x33, 0x00, 0x00, 0x00, //0x00000947 jbe          LBB0_156
	0x4c, 0x89, 0xf0, //0x0000094d movq         %r14, %rax
	0xe9, 0x51, 0x01, 0x00, 0x00, //0x00000950 jmp          LBB0_178
	//0x00000955 LBB0_98
	0x31, 0xc9, //0x00000955 xorl         %ecx, %ecx
	0x48, 0x8b, 0x7d, 0xc8, //0x00000957 movq         $-56(%rbp), %rdi
	0x49, 0x8b, 0x10, //0x0000095b movq         (%r8), %rdx
	0x4c, 0x89, 0xf6, //0x0000095e movq         %r14, %rsi
	0x48, 0x29, 0xd6, //0x00000961 subq         %rdx, %rsi
	0x0f, 0x82, 0xc5, 0xff, 0xff, 0xff, //0x00000964 jb           LBB0_101
	0xe9, 0x11, 0x00, 0x00, 0x00, //0x0000096a jmp          LBB0_156
	//0x0000096f LBB0_99
	0x31, 0xc9, //0x0000096f xorl         %ecx, %ecx
	0x49, 0x8b, 0x10, //0x00000971 movq         (%r8), %rdx
	0x4c, 0x89, 0xf6, //0x00000974 movq         %r14, %rsi
	0x48, 0x29, 0xd6, //0x00000977 subq         %rdx, %rsi
	0x0f, 0x82, 0xaf, 0xff, 0xff, 0xff, //0x0000097a jb           LBB0_101
	//0x00000980 .p2align 4, 0x90
	//0x00000980 LBB0_156
	0x49, 0x8d, 0x46, 0x01, //0x00000980 leaq         $1(%r14), %rax
	0x48, 0x39, 0xd0, //0x00000984 cmpq         %rdx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000987 jae          LBB0_160
	0x8a, 0x1c, 0x07, //0x0000098d movb         (%rdi,%rax), %bl
	0x80, 0xfb, 0x0d, //0x00000990 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000993 je           LBB0_160
	0x80, 0xfb, 0x20, //0x00000999 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x0000099c je           LBB0_160
	0x80, 0xc3, 0xf7, //0x000009a2 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x000009a5 cmpb         $1, %bl
	0x0f, 0x87, 0xf8, 0x00, 0x00, 0x00, //0x000009a8 ja           LBB0_178
	0x90, 0x90, //0x000009ae .p2align 4, 0x90
	//0x000009b0 LBB0_160
	0x49, 0x8d, 0x46, 0x02, //0x000009b0 leaq         $2(%r14), %rax
	0x48, 0x39, 0xd0, //0x000009b4 cmpq         %rdx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000009b7 jae          LBB0_164
	0x8a, 0x1c, 0x07, //0x000009bd movb         (%rdi,%rax), %bl
	0x80, 0xfb, 0x0d, //0x000009c0 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x000009c3 je           LBB0_164
	0x80, 0xfb, 0x20, //0x000009c9 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x000009cc je           LBB0_164
	0x80, 0xc3, 0xf7, //0x000009d2 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x000009d5 cmpb         $1, %bl
	0x0f, 0x87, 0xc8, 0x00, 0x00, 0x00, //0x000009d8 ja           LBB0_178
	0x90, 0x90, //0x000009de .p2align 4, 0x90
	//0x000009e0 LBB0_164
	0x49, 0x8d, 0x46, 0x03, //0x000009e0 leaq         $3(%r14), %rax
	0x48, 0x39, 0xd0, //0x000009e4 cmpq         %rdx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000009e7 jae          LBB0_168
	0x8a, 0x1c, 0x07, //0x000009ed movb         (%rdi,%rax), %bl
	0x80, 0xfb, 0x0d, //0x000009f0 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x000009f3 je           LBB0_168
	0x80, 0xfb, 0x20, //0x000009f9 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x000009fc je           LBB0_168
	0x80, 0xc3, 0xf7, //0x00000a02 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000a05 cmpb         $1, %bl
	0x0f, 0x87, 0x98, 0x00, 0x00, 0x00, //0x00000a08 ja           LBB0_178
	0x90, 0x90, //0x00000a0e .p2align 4, 0x90
	//0x00000a10 LBB0_168
	0x49, 0x8d, 0x7e, 0x04, //0x00000a10 leaq         $4(%r14), %rdi
	0x48, 0x39, 0xfa, //0x00000a14 cmpq         %rdi, %rdx
	0x0f, 0x86, 0x1f, 0x28, 0x00, 0x00, //0x00000a17 jbe          LBB0_555
	0x48, 0x39, 0xfa, //0x00000a1d cmpq         %rdi, %rdx
	0x0f, 0x84, 0x4a, 0x00, 0x00, 0x00, //0x00000a20 je           LBB0_175
	0x48, 0x8b, 0x45, 0xc8, //0x00000a26 movq         $-56(%rbp), %rax
	0x48, 0x8d, 0x3c, 0x10, //0x00000a2a leaq         (%rax,%rdx), %rdi
	0x48, 0x83, 0xc6, 0x04, //0x00000a2e addq         $4, %rsi
	0x4a, 0x8d, 0x44, 0x30, 0x05, //0x00000a32 leaq         $5(%rax,%r14), %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000a37 .p2align 4, 0x90
	//0x00000a40 LBB0_171
	0x0f, 0xbe, 0x58, 0xff, //0x00000a40 movsbl       $-1(%rax), %ebx
	0x83, 0xfb, 0x20, //0x00000a44 cmpl         $32, %ebx
	0x0f, 0x87, 0x43, 0x00, 0x00, 0x00, //0x00000a47 ja           LBB0_177
	0x49, 0x0f, 0xa3, 0xda, //0x00000a4d btq          %rbx, %r10
	0x0f, 0x83, 0x39, 0x00, 0x00, 0x00, //0x00000a51 jae          LBB0_177
	0x48, 0xff, 0xc0, //0x00000a57 incq         %rax
	0x48, 0xff, 0xc6, //0x00000a5a incq         %rsi
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x00000a5d jne          LBB0_171
	0x48, 0x8b, 0x75, 0xc8, //0x00000a63 movq         $-56(%rbp), %rsi
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x00000a67 jmp          LBB0_176
	0x90, 0x90, 0x90, 0x90, //0x00000a6c .p2align 4, 0x90
	//0x00000a70 LBB0_175
	0x48, 0x8b, 0x75, 0xc8, //0x00000a70 movq         $-56(%rbp), %rsi
	0x48, 0x01, 0xf7, //0x00000a74 addq         %rsi, %rdi
	//0x00000a77 LBB0_176
	0x48, 0x29, 0xf7, //0x00000a77 subq         %rsi, %rdi
	0x48, 0x89, 0xf8, //0x00000a7a movq         %rdi, %rax
	0x48, 0x89, 0xf7, //0x00000a7d movq         %rsi, %rdi
	0x48, 0x39, 0xd0, //0x00000a80 cmpq         %rdx, %rax
	0x0f, 0x82, 0x1d, 0x00, 0x00, 0x00, //0x00000a83 jb           LBB0_178
	0xe9, 0xb4, 0x27, 0x00, 0x00, //0x00000a89 jmp          LBB0_556
	0x90, 0x90, //0x00000a8e .p2align 4, 0x90
	//0x00000a90 LBB0_177
	0x48, 0x8b, 0x7d, 0xc8, //0x00000a90 movq         $-56(%rbp), %rdi
	0x48, 0x89, 0xfe, //0x00000a94 movq         %rdi, %rsi
	0x48, 0xf7, 0xd6, //0x00000a97 notq         %rsi
	0x48, 0x01, 0xf0, //0x00000a9a addq         %rsi, %rax
	0x48, 0x39, 0xd0, //0x00000a9d cmpq         %rdx, %rax
	0x0f, 0x83, 0x9c, 0x27, 0x00, 0x00, //0x00000aa0 jae          LBB0_556
	//0x00000aa6 LBB0_178
	0x4c, 0x8d, 0x70, 0x01, //0x00000aa6 leaq         $1(%rax), %r14
	0x4d, 0x89, 0x33, //0x00000aaa movq         %r14, (%r11)
	0x80, 0x3c, 0x07, 0x3a, //0x00000aad cmpb         $58, (%rdi,%rax)
	0x0f, 0x85, 0x8b, 0x27, 0x00, 0x00, //0x00000ab1 jne          LBB0_556
	0x48, 0x85, 0xc9, //0x00000ab7 testq        %rcx, %rcx
	0x0f, 0x85, 0x60, 0x25, 0x00, 0x00, //0x00000aba jne          LBB0_447
	0x49, 0x8b, 0x10, //0x00000ac0 movq         (%r8), %rdx
	0x49, 0x39, 0xd6, //0x00000ac3 cmpq         %rdx, %r14
	0x0f, 0x83, 0x34, 0x00, 0x00, 0x00, //0x00000ac6 jae          LBB0_185
	0x42, 0x8a, 0x0c, 0x37, //0x00000acc movb         (%rdi,%r14), %cl
	0x80, 0xf9, 0x0d, //0x00000ad0 cmpb         $13, %cl
	0x0f, 0x84, 0x27, 0x00, 0x00, 0x00, //0x00000ad3 je           LBB0_185
	0x80, 0xf9, 0x20, //0x00000ad9 cmpb         $32, %cl
	0x0f, 0x84, 0x1e, 0x00, 0x00, 0x00, //0x00000adc je           LBB0_185
	0x80, 0xc1, 0xf7, //0x00000ae2 addb         $-9, %cl
	0x80, 0xf9, 0x01, //0x00000ae5 cmpb         $1, %cl
	0x0f, 0x86, 0x12, 0x00, 0x00, 0x00, //0x00000ae8 jbe          LBB0_185
	0x4c, 0x89, 0xf1, //0x00000aee movq         %r14, %rcx
	0xe9, 0x4a, 0x01, 0x00, 0x00, //0x00000af1 jmp          LBB0_207
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000af6 .p2align 4, 0x90
	//0x00000b00 LBB0_185
	0x48, 0x8d, 0x48, 0x02, //0x00000b00 leaq         $2(%rax), %rcx
	0x48, 0x39, 0xd1, //0x00000b04 cmpq         %rdx, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000b07 jae          LBB0_189
	0x8a, 0x1c, 0x0f, //0x00000b0d movb         (%rdi,%rcx), %bl
	0x80, 0xfb, 0x0d, //0x00000b10 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000b13 je           LBB0_189
	0x80, 0xfb, 0x20, //0x00000b19 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00000b1c je           LBB0_189
	0x80, 0xc3, 0xf7, //0x00000b22 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000b25 cmpb         $1, %bl
	0x0f, 0x87, 0x12, 0x01, 0x00, 0x00, //0x00000b28 ja           LBB0_207
	0x90, 0x90, //0x00000b2e .p2align 4, 0x90
	//0x00000b30 LBB0_189
	0x48, 0x8d, 0x48, 0x03, //0x00000b30 leaq         $3(%rax), %rcx
	0x48, 0x39, 0xd1, //0x00000b34 cmpq         %rdx, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000b37 jae          LBB0_193
	0x8a, 0x1c, 0x0f, //0x00000b3d movb         (%rdi,%rcx), %bl
	0x80, 0xfb, 0x0d, //0x00000b40 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000b43 je           LBB0_193
	0x80, 0xfb, 0x20, //0x00000b49 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00000b4c je           LBB0_193
	0x80, 0xc3, 0xf7, //0x00000b52 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000b55 cmpb         $1, %bl
	0x0f, 0x87, 0xe2, 0x00, 0x00, 0x00, //0x00000b58 ja           LBB0_207
	0x90, 0x90, //0x00000b5e .p2align 4, 0x90
	//0x00000b60 LBB0_193
	0x48, 0x8d, 0x48, 0x04, //0x00000b60 leaq         $4(%rax), %rcx
	0x48, 0x39, 0xd1, //0x00000b64 cmpq         %rdx, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000b67 jae          LBB0_197
	0x8a, 0x1c, 0x0f, //0x00000b6d movb         (%rdi,%rcx), %bl
	0x80, 0xfb, 0x0d, //0x00000b70 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000b73 je           LBB0_197
	0x80, 0xfb, 0x20, //0x00000b79 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00000b7c je           LBB0_197
	0x80, 0xc3, 0xf7, //0x00000b82 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000b85 cmpb         $1, %bl
	0x0f, 0x87, 0xb2, 0x00, 0x00, 0x00, //0x00000b88 ja           LBB0_207
	0x90, 0x90, //0x00000b8e .p2align 4, 0x90
	//0x00000b90 LBB0_197
	0x48, 0x8d, 0x70, 0x05, //0x00000b90 leaq         $5(%rax), %rsi
	0x48, 0x39, 0xf2, //0x00000b94 cmpq         %rsi, %rdx
	0x0f, 0x86, 0x63, 0x00, 0x00, 0x00, //0x00000b97 jbe          LBB0_204
	0x48, 0x39, 0xf2, //0x00000b9d cmpq         %rsi, %rdx
	0x0f, 0x84, 0x65, 0x00, 0x00, 0x00, //0x00000ba0 je           LBB0_205
	0x48, 0x8d, 0x34, 0x17, //0x00000ba6 leaq         (%rdi,%rdx), %rsi
	0x48, 0x8d, 0x4c, 0x07, 0x06, //0x00000baa leaq         $6(%rdi,%rax), %rcx
	0x48, 0x29, 0xd0, //0x00000baf subq         %rdx, %rax
	0x48, 0x83, 0xc0, 0x05, //0x00000bb2 addq         $5, %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000bb6 .p2align 4, 0x90
	//0x00000bc0 LBB0_200
	0x0f, 0xbe, 0x79, 0xff, //0x00000bc0 movsbl       $-1(%rcx), %edi
	0x83, 0xff, 0x20, //0x00000bc4 cmpl         $32, %edi
	0x0f, 0x87, 0x55, 0x00, 0x00, 0x00, //0x00000bc7 ja           LBB0_206
	0x49, 0x0f, 0xa3, 0xfa, //0x00000bcd btq          %rdi, %r10
	0x0f, 0x83, 0x4b, 0x00, 0x00, 0x00, //0x00000bd1 jae          LBB0_206
	0x48, 0xff, 0xc1, //0x00000bd7 incq         %rcx
	0x48, 0xff, 0xc0, //0x00000bda incq         %rax
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x00000bdd jne          LBB0_200
	0x48, 0x8b, 0x7d, 0xc8, //0x00000be3 movq         $-56(%rbp), %rdi
	0x48, 0x29, 0xfe, //0x00000be7 subq         %rdi, %rsi
	0x48, 0x89, 0xf1, //0x00000bea movq         %rsi, %rcx
	0x48, 0x39, 0xd1, //0x00000bed cmpq         %rdx, %rcx
	0x0f, 0x82, 0x4a, 0x00, 0x00, 0x00, //0x00000bf0 jb           LBB0_207
	0xe9, 0x45, 0x01, 0x00, 0x00, //0x00000bf6 jmp          LBB0_224
	0x90, 0x90, 0x90, 0x90, 0x90, //0x00000bfb .p2align 4, 0x90
	//0x00000c00 LBB0_204
	0x49, 0x89, 0x33, //0x00000c00 movq         %rsi, (%r11)
	0x49, 0x89, 0xf6, //0x00000c03 movq         %rsi, %r14
	0xe9, 0x35, 0x01, 0x00, 0x00, //0x00000c06 jmp          LBB0_224
	//0x00000c0b LBB0_205
	0x48, 0x01, 0xfe, //0x00000c0b addq         %rdi, %rsi
	0x48, 0x29, 0xfe, //0x00000c0e subq         %rdi, %rsi
	0x48, 0x89, 0xf1, //0x00000c11 movq         %rsi, %rcx
	0x48, 0x39, 0xd1, //0x00000c14 cmpq         %rdx, %rcx
	0x0f, 0x82, 0x23, 0x00, 0x00, 0x00, //0x00000c17 jb           LBB0_207
	0xe9, 0x1e, 0x01, 0x00, 0x00, //0x00000c1d jmp          LBB0_224
	//0x00000c22 LBB0_206
	0x48, 0x8b, 0x7d, 0xc8, //0x00000c22 movq         $-56(%rbp), %rdi
	0x48, 0x89, 0xf8, //0x00000c26 movq         %rdi, %rax
	0x48, 0xf7, 0xd0, //0x00000c29 notq         %rax
	0x48, 0x01, 0xc1, //0x00000c2c addq         %rax, %rcx
	0x48, 0x39, 0xd1, //0x00000c2f cmpq         %rdx, %rcx
	0x0f, 0x83, 0x08, 0x01, 0x00, 0x00, //0x00000c32 jae          LBB0_224
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000c38 .p2align 4, 0x90
	//0x00000c40 LBB0_207
	0x4c, 0x8d, 0x71, 0x01, //0x00000c40 leaq         $1(%rcx), %r14
	0x4d, 0x89, 0x33, //0x00000c44 movq         %r14, (%r11)
	0x0f, 0xbe, 0x04, 0x0f, //0x00000c47 movsbl       (%rdi,%rcx), %eax
	0x83, 0xf8, 0x7b, //0x00000c4b cmpl         $123, %eax
	0x0f, 0x87, 0x82, 0x03, 0x00, 0x00, //0x00000c4e ja           LBB0_265
	0x48, 0x8d, 0x15, 0x0d, 0x4d, 0x00, 0x00, //0x00000c54 leaq         $19725(%rip), %rdx  /* LJTI0_1+0(%rip) */
	0x48, 0x63, 0x04, 0x82, //0x00000c5b movslq       (%rdx,%rax,4), %rax
	0x48, 0x01, 0xd0, //0x00000c5f addq         %rdx, %rax
	0xff, 0xe0, //0x00000c62 jmpq         *%rax
	//0x00000c64 LBB0_209
	0x49, 0x8b, 0x10, //0x00000c64 movq         (%r8), %rdx
	0x48, 0x89, 0xd0, //0x00000c67 movq         %rdx, %rax
	0x4c, 0x29, 0xf0, //0x00000c6a subq         %r14, %rax
	0x49, 0x01, 0xfe, //0x00000c6d addq         %rdi, %r14
	0x48, 0x83, 0xf8, 0x10, //0x00000c70 cmpq         $16, %rax
	0x0f, 0x82, 0x66, 0x00, 0x00, 0x00, //0x00000c74 jb           LBB0_214
	0x48, 0x29, 0xca, //0x00000c7a subq         %rcx, %rdx
	0x48, 0x83, 0xc2, 0xef, //0x00000c7d addq         $-17, %rdx
	0x48, 0x89, 0xd6, //0x00000c81 movq         %rdx, %rsi
	0x48, 0x83, 0xe6, 0xf0, //0x00000c84 andq         $-16, %rsi
	0x48, 0x01, 0xce, //0x00000c88 addq         %rcx, %rsi
	0x48, 0x8d, 0x4c, 0x37, 0x11, //0x00000c8b leaq         $17(%rdi,%rsi), %rcx
	0x83, 0xe2, 0x0f, //0x00000c90 andl         $15, %edx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000c93 .p2align 4, 0x90
	//0x00000ca0 LBB0_211
	0xf3, 0x41, 0x0f, 0x6f, 0x16, //0x00000ca0 movdqu       (%r14), %xmm2
	0x66, 0x0f, 0x6f, 0xda, //0x00000ca5 movdqa       %xmm2, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdd, //0x00000ca9 pcmpeqb      %xmm13, %xmm3
	0x66, 0x41, 0x0f, 0xeb, 0xd6, //0x00000cae por          %xmm14, %xmm2
	0x66, 0x0f, 0x74, 0xd4, //0x00000cb3 pcmpeqb      %xmm4, %xmm2
	0x66, 0x0f, 0xeb, 0xd3, //0x00000cb7 por          %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xf2, //0x00000cbb pmovmskb     %xmm2, %esi
	0x66, 0x85, 0xf6, //0x00000cbf testw        %si, %si
	0x0f, 0x85, 0x68, 0x00, 0x00, 0x00, //0x00000cc2 jne          LBB0_222
	0x49, 0x83, 0xc6, 0x10, //0x00000cc8 addq         $16, %r14
	0x48, 0x83, 0xc0, 0xf0, //0x00000ccc addq         $-16, %rax
	0x48, 0x83, 0xf8, 0x0f, //0x00000cd0 cmpq         $15, %rax
	0x0f, 0x87, 0xc6, 0xff, 0xff, 0xff, //0x00000cd4 ja           LBB0_211
	0x48, 0x89, 0xd0, //0x00000cda movq         %rdx, %rax
	0x49, 0x89, 0xce, //0x00000cdd movq         %rcx, %r14
	//0x00000ce0 LBB0_214
	0x48, 0x85, 0xc0, //0x00000ce0 testq        %rax, %rax
	0x0f, 0x84, 0x32, 0x00, 0x00, 0x00, //0x00000ce3 je           LBB0_221
	0x49, 0x8d, 0x0c, 0x06, //0x00000ce9 leaq         (%r14,%rax), %rcx
	//0x00000ced LBB0_216
	0x41, 0x0f, 0xb6, 0x16, //0x00000ced movzbl       (%r14), %edx
	0x80, 0xfa, 0x2c, //0x00000cf1 cmpb         $44, %dl
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x00000cf4 je           LBB0_221
	0x80, 0xfa, 0x7d, //0x00000cfa cmpb         $125, %dl
	0x0f, 0x84, 0x18, 0x00, 0x00, 0x00, //0x00000cfd je           LBB0_221
	0x80, 0xfa, 0x5d, //0x00000d03 cmpb         $93, %dl
	0x0f, 0x84, 0x0f, 0x00, 0x00, 0x00, //0x00000d06 je           LBB0_221
	0x49, 0xff, 0xc6, //0x00000d0c incq         %r14
	0x48, 0xff, 0xc8, //0x00000d0f decq         %rax
	0x0f, 0x85, 0xd5, 0xff, 0xff, 0xff, //0x00000d12 jne          LBB0_216
	0x49, 0x89, 0xce, //0x00000d18 movq         %rcx, %r14
	//0x00000d1b LBB0_221
	0x49, 0x29, 0xfe, //0x00000d1b subq         %rdi, %r14
	0xe9, 0x1a, 0x00, 0x00, 0x00, //0x00000d1e jmp          LBB0_223
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000d23 .p2align 4, 0x90
	//0x00000d30 LBB0_222
	0x0f, 0xb7, 0xc6, //0x00000d30 movzwl       %si, %eax
	0x48, 0x0f, 0xbc, 0xc0, //0x00000d33 bsfq         %rax, %rax
	0x49, 0x29, 0xfe, //0x00000d37 subq         %rdi, %r14
	0x49, 0x01, 0xc6, //0x00000d3a addq         %rax, %r14
	//0x00000d3d LBB0_223
	0x4d, 0x89, 0x33, //0x00000d3d movq         %r14, (%r11)
	//0x00000d40 LBB0_224
	0x49, 0x8b, 0x7d, 0x00, //0x00000d40 movq         (%r13), %rdi
	0x49, 0x8b, 0x45, 0x08, //0x00000d44 movq         $8(%r13), %rax
	0x4c, 0x89, 0xf1, //0x00000d48 movq         %r14, %rcx
	0x48, 0x29, 0xc1, //0x00000d4b subq         %rax, %rcx
	0x0f, 0x83, 0x2c, 0x00, 0x00, 0x00, //0x00000d4e jae          LBB0_229
	0x42, 0x8a, 0x14, 0x37, //0x00000d54 movb         (%rdi,%r14), %dl
	0x80, 0xfa, 0x0d, //0x00000d58 cmpb         $13, %dl
	0x0f, 0x84, 0x1f, 0x00, 0x00, 0x00, //0x00000d5b je           LBB0_229
	0x80, 0xfa, 0x20, //0x00000d61 cmpb         $32, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000d64 je           LBB0_229
	0x80, 0xc2, 0xf7, //0x00000d6a addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x00000d6d cmpb         $1, %dl
	0x0f, 0x86, 0x0a, 0x00, 0x00, 0x00, //0x00000d70 jbe          LBB0_229
	0x4c, 0x89, 0xf2, //0x00000d76 movq         %r14, %rdx
	0xe9, 0x74, 0xf6, 0xff, 0xff, //0x00000d79 jmp          LBB0_250
	0x90, 0x90, //0x00000d7e .p2align 4, 0x90
	//0x00000d80 LBB0_229
	0x49, 0x8d, 0x56, 0x01, //0x00000d80 leaq         $1(%r14), %rdx
	0x48, 0x39, 0xc2, //0x00000d84 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000d87 jae          LBB0_233
	0x8a, 0x1c, 0x17, //0x00000d8d movb         (%rdi,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x00000d90 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000d93 je           LBB0_233
	0x80, 0xfb, 0x20, //0x00000d99 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00000d9c je           LBB0_233
	0x80, 0xc3, 0xf7, //0x00000da2 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000da5 cmpb         $1, %bl
	0x0f, 0x87, 0x44, 0xf6, 0xff, 0xff, //0x00000da8 ja           LBB0_250
	0x90, 0x90, //0x00000dae .p2align 4, 0x90
	//0x00000db0 LBB0_233
	0x49, 0x8d, 0x56, 0x02, //0x00000db0 leaq         $2(%r14), %rdx
	0x48, 0x39, 0xc2, //0x00000db4 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000db7 jae          LBB0_237
	0x8a, 0x1c, 0x17, //0x00000dbd movb         (%rdi,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x00000dc0 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000dc3 je           LBB0_237
	0x80, 0xfb, 0x20, //0x00000dc9 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00000dcc je           LBB0_237
	0x80, 0xc3, 0xf7, //0x00000dd2 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000dd5 cmpb         $1, %bl
	0x0f, 0x87, 0x14, 0xf6, 0xff, 0xff, //0x00000dd8 ja           LBB0_250
	0x90, 0x90, //0x00000dde .p2align 4, 0x90
	//0x00000de0 LBB0_237
	0x49, 0x8d, 0x56, 0x03, //0x00000de0 leaq         $3(%r14), %rdx
	0x48, 0x39, 0xc2, //0x00000de4 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000de7 jae          LBB0_241
	0x8a, 0x1c, 0x17, //0x00000ded movb         (%rdi,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x00000df0 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000df3 je           LBB0_241
	0x80, 0xfb, 0x20, //0x00000df9 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00000dfc je           LBB0_241
	0x80, 0xc3, 0xf7, //0x00000e02 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00000e05 cmpb         $1, %bl
	0x0f, 0x87, 0xe4, 0xf5, 0xff, 0xff, //0x00000e08 ja           LBB0_250
	0x90, 0x90, //0x00000e0e .p2align 4, 0x90
	//0x00000e10 LBB0_241
	0x49, 0x8d, 0x76, 0x04, //0x00000e10 leaq         $4(%r14), %rsi
	0x48, 0x39, 0xf0, //0x00000e14 cmpq         %rsi, %rax
	0x0f, 0x86, 0xcd, 0x23, 0x00, 0x00, //0x00000e17 jbe          LBB0_549
	0x48, 0x39, 0xf0, //0x00000e1d cmpq         %rsi, %rax
	0x0f, 0x84, 0xba, 0xf5, 0xff, 0xff, //0x00000e20 je           LBB0_248
	0x48, 0x8d, 0x34, 0x07, //0x00000e26 leaq         (%rdi,%rax), %rsi
	0x48, 0x83, 0xc1, 0x04, //0x00000e2a addq         $4, %rcx
	0x48, 0x89, 0xfb, //0x00000e2e movq         %rdi, %rbx
	0x4a, 0x8d, 0x54, 0x37, 0x05, //0x00000e31 leaq         $5(%rdi,%r14), %rdx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000e36 .p2align 4, 0x90
	//0x00000e40 LBB0_244
	0x0f, 0xbe, 0x7a, 0xff, //0x00000e40 movsbl       $-1(%rdx), %edi
	0x83, 0xff, 0x20, //0x00000e44 cmpl         $32, %edi
	0x0f, 0x87, 0x2d, 0x00, 0x00, 0x00, //0x00000e47 ja           LBB0_249
	0x49, 0x0f, 0xa3, 0xfa, //0x00000e4d btq          %rdi, %r10
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000e51 jae          LBB0_249
	0x48, 0xff, 0xc2, //0x00000e57 incq         %rdx
	0x48, 0xff, 0xc1, //0x00000e5a incq         %rcx
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x00000e5d jne          LBB0_244
	0x48, 0x89, 0xdf, //0x00000e63 movq         %rbx, %rdi
	0x48, 0x29, 0xfe, //0x00000e66 subq         %rdi, %rsi
	0x48, 0x89, 0xf2, //0x00000e69 movq         %rsi, %rdx
	0x48, 0x39, 0xc2, //0x00000e6c cmpq         %rax, %rdx
	0x0f, 0x82, 0x7d, 0xf5, 0xff, 0xff, //0x00000e6f jb           LBB0_250
	0xe9, 0xc8, 0x23, 0x00, 0x00, //0x00000e75 jmp          LBB0_556
	//0x00000e7a LBB0_249
	0x48, 0x89, 0xdf, //0x00000e7a movq         %rbx, %rdi
	0x48, 0x89, 0xd9, //0x00000e7d movq         %rbx, %rcx
	0x48, 0xf7, 0xd1, //0x00000e80 notq         %rcx
	0x48, 0x01, 0xca, //0x00000e83 addq         %rcx, %rdx
	0x48, 0x39, 0xc2, //0x00000e86 cmpq         %rax, %rdx
	0x0f, 0x82, 0x63, 0xf5, 0xff, 0xff, //0x00000e89 jb           LBB0_250
	0xe9, 0xae, 0x23, 0x00, 0x00, //0x00000e8f jmp          LBB0_556
	//0x00000e94 LBB0_252
	0x48, 0x83, 0xc1, 0x04, //0x00000e94 addq         $4, %rcx
	0x49, 0x3b, 0x08, //0x00000e98 cmpq         (%r8), %rcx
	0x0f, 0x87, 0x9f, 0xfe, 0xff, 0xff, //0x00000e9b ja           LBB0_224
	0xe9, 0x30, 0x01, 0x00, 0x00, //0x00000ea1 jmp          LBB0_265
	//0x00000ea6 LBB0_253
	0x4d, 0x89, 0xcf, //0x00000ea6 movq         %r9, %r15
	0x4d, 0x8b, 0x00, //0x00000ea9 movq         (%r8), %r8
	0x4c, 0x89, 0xc0, //0x00000eac movq         %r8, %rax
	0x4c, 0x29, 0xf0, //0x00000eaf subq         %r14, %rax
	0x48, 0x83, 0xf8, 0x20, //0x00000eb2 cmpq         $32, %rax
	0x0f, 0x8c, 0xd4, 0x0e, 0x00, 0x00, //0x00000eb6 jl           LBB0_325
	0x4c, 0x8d, 0x0c, 0x0f, //0x00000ebc leaq         (%rdi,%rcx), %r9
	0x49, 0x29, 0xc8, //0x00000ec0 subq         %rcx, %r8
	0xb9, 0x1f, 0x00, 0x00, 0x00, //0x00000ec3 movl         $31, %ecx
	0x31, 0xc0, //0x00000ec8 xorl         %eax, %eax
	0x45, 0x31, 0xd2, //0x00000eca xorl         %r10d, %r10d
	0xe9, 0x30, 0x00, 0x00, 0x00, //0x00000ecd jmp          LBB0_255
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000ed2 .p2align 4, 0x90
	//0x00000ee0 LBB0_258
	0x45, 0x31, 0xd2, //0x00000ee0 xorl         %r10d, %r10d
	0x85, 0xdb, //0x00000ee3 testl        %ebx, %ebx
	0x0f, 0x85, 0xb1, 0x00, 0x00, 0x00, //0x00000ee5 jne          LBB0_257
	//0x00000eeb LBB0_259
	0x48, 0x83, 0xc0, 0x20, //0x00000eeb addq         $32, %rax
	0x49, 0x8d, 0x54, 0x08, 0xe0, //0x00000eef leaq         $-32(%r8,%rcx), %rdx
	0x48, 0x83, 0xc1, 0xe0, //0x00000ef4 addq         $-32, %rcx
	0x48, 0x83, 0xfa, 0x3f, //0x00000ef8 cmpq         $63, %rdx
	0x0f, 0x8e, 0xcb, 0x0d, 0x00, 0x00, //0x00000efc jle          LBB0_260
	//0x00000f02 LBB0_255
	0xf3, 0x41, 0x0f, 0x6f, 0x54, 0x01, 0x01, //0x00000f02 movdqu       $1(%r9,%rax), %xmm2
	0xf3, 0x41, 0x0f, 0x6f, 0x5c, 0x01, 0x11, //0x00000f09 movdqu       $17(%r9,%rax), %xmm3
	0x66, 0x0f, 0x6f, 0xea, //0x00000f10 movdqa       %xmm2, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00000f14 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xd5, //0x00000f18 pmovmskb     %xmm5, %edx
	0x66, 0x0f, 0x6f, 0xeb, //0x00000f1c movdqa       %xmm3, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00000f20 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xdd, //0x00000f24 pmovmskb     %xmm5, %ebx
	0x48, 0xc1, 0xe3, 0x10, //0x00000f28 shlq         $16, %rbx
	0x48, 0x09, 0xd3, //0x00000f2c orq          %rdx, %rbx
	0x66, 0x0f, 0x74, 0xd1, //0x00000f2f pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xf2, //0x00000f33 pmovmskb     %xmm2, %esi
	0x66, 0x0f, 0x74, 0xd9, //0x00000f37 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xd3, //0x00000f3b pmovmskb     %xmm3, %edx
	0x48, 0xc1, 0xe2, 0x10, //0x00000f3f shlq         $16, %rdx
	0x48, 0x09, 0xf2, //0x00000f43 orq          %rsi, %rdx
	0x48, 0x89, 0xd6, //0x00000f46 movq         %rdx, %rsi
	0x4c, 0x09, 0xd6, //0x00000f49 orq          %r10, %rsi
	0x0f, 0x84, 0x8e, 0xff, 0xff, 0xff, //0x00000f4c je           LBB0_258
	0x44, 0x89, 0xd6, //0x00000f52 movl         %r10d, %esi
	0x41, 0xbc, 0xff, 0xff, 0xff, 0xff, //0x00000f55 movl         $4294967295, %r12d
	0x44, 0x31, 0xe6, //0x00000f5b xorl         %r12d, %esi
	0x21, 0xf2, //0x00000f5e andl         %esi, %edx
	0x8d, 0x34, 0x12, //0x00000f60 leal         (%rdx,%rdx), %esi
	0x44, 0x09, 0xd6, //0x00000f63 orl          %r10d, %esi
	0x41, 0x8d, 0xbc, 0x24, 0xab, 0xaa, 0xaa, 0xaa, //0x00000f66 leal         $-1431655765(%r12), %edi
	0x31, 0xf7, //0x00000f6e xorl         %esi, %edi
	0x21, 0xd7, //0x00000f70 andl         %edx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000f72 andl         $-1431655766, %edi
	0x45, 0x31, 0xd2, //0x00000f78 xorl         %r10d, %r10d
	0x01, 0xd7, //0x00000f7b addl         %edx, %edi
	0x41, 0x0f, 0x92, 0xc2, //0x00000f7d setb         %r10b
	0x01, 0xff, //0x00000f81 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00000f83 xorl         $1431655765, %edi
	0x21, 0xf7, //0x00000f89 andl         %esi, %edi
	0x44, 0x31, 0xe7, //0x00000f8b xorl         %r12d, %edi
	0x21, 0xfb, //0x00000f8e andl         %edi, %ebx
	0x48, 0x8b, 0x7d, 0xc8, //0x00000f90 movq         $-56(%rbp), %rdi
	0x85, 0xdb, //0x00000f94 testl        %ebx, %ebx
	0x0f, 0x84, 0x4f, 0xff, 0xff, 0xff, //0x00000f96 je           LBB0_259
	//0x00000f9c LBB0_257
	0x48, 0x0f, 0xbc, 0xcb, //0x00000f9c bsfq         %rbx, %rcx
	0x49, 0x01, 0xc9, //0x00000fa0 addq         %rcx, %r9
	0x49, 0x01, 0xc1, //0x00000fa3 addq         %rax, %r9
	0x49, 0x29, 0xf9, //0x00000fa6 subq         %rdi, %r9
	0x49, 0x83, 0xc1, 0x02, //0x00000fa9 addq         $2, %r9
	0x4d, 0x89, 0x0b, //0x00000fad movq         %r9, (%r11)
	0x4d, 0x89, 0xce, //0x00000fb0 movq         %r9, %r14
	0x4c, 0x8b, 0x45, 0xb0, //0x00000fb3 movq         $-80(%rbp), %r8
	0x49, 0xba, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00000fb7 movabsq      $4294977024, %r10
	0x4d, 0x89, 0xf9, //0x00000fc1 movq         %r15, %r9
	0xe9, 0x77, 0xfd, 0xff, 0xff, //0x00000fc4 jmp          LBB0_224
	//0x00000fc9 LBB0_264
	0x48, 0x83, 0xc1, 0x05, //0x00000fc9 addq         $5, %rcx
	0x49, 0x3b, 0x08, //0x00000fcd cmpq         (%r8), %rcx
	0x0f, 0x87, 0x6a, 0xfd, 0xff, 0xff, //0x00000fd0 ja           LBB0_224
	//0x00000fd6 LBB0_265
	0x49, 0x89, 0x0b, //0x00000fd6 movq         %rcx, (%r11)
	0x49, 0x89, 0xce, //0x00000fd9 movq         %rcx, %r14
	0xe9, 0x5f, 0xfd, 0xff, 0xff, //0x00000fdc jmp          LBB0_224
	//0x00000fe1 LBB0_266
	0x4d, 0x8b, 0x10, //0x00000fe1 movq         (%r8), %r10
	0x4d, 0x29, 0xf2, //0x00000fe4 subq         %r14, %r10
	0x4c, 0x01, 0xf7, //0x00000fe7 addq         %r14, %rdi
	0x45, 0x31, 0xc9, //0x00000fea xorl         %r9d, %r9d
	0x45, 0x31, 0xc0, //0x00000fed xorl         %r8d, %r8d
	0x45, 0x31, 0xdb, //0x00000ff0 xorl         %r11d, %r11d
	0x45, 0x31, 0xe4, //0x00000ff3 xorl         %r12d, %r12d
	0x49, 0x83, 0xfa, 0x40, //0x00000ff6 cmpq         $64, %r10
	0x0f, 0x8d, 0x49, 0x01, 0x00, 0x00, //0x00000ffa jge          LBB0_267
	//0x00001000 LBB0_276
	0x48, 0x8b, 0x55, 0xa0, //0x00001000 movq         $-96(%rbp), %rdx
	0x4d, 0x85, 0xd2, //0x00001004 testq        %r10, %r10
	0x0f, 0x8e, 0x12, 0x0f, 0x00, 0x00, //0x00001007 jle          LBB0_340
	0x48, 0x89, 0xf9, //0x0000100d movq         %rdi, %rcx
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x70, 0xff, 0xff, 0xff, //0x00001010 movdqu       %xmm8, $-144(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x60, 0xff, 0xff, 0xff, //0x00001019 movdqu       %xmm8, $-160(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x50, 0xff, 0xff, 0xff, //0x00001022 movdqu       %xmm8, $-176(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x40, 0xff, 0xff, 0xff, //0x0000102b movdqu       %xmm8, $-192(%rbp)
	0x89, 0xc8, //0x00001034 movl         %ecx, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x00001036 andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x0000103b cmpl         $4033, %eax
	0x0f, 0x82, 0x35, 0x00, 0x00, 0x00, //0x00001040 jb           LBB0_280
	0x49, 0x83, 0xfa, 0x20, //0x00001046 cmpq         $32, %r10
	0x0f, 0x82, 0x37, 0x00, 0x00, 0x00, //0x0000104a jb           LBB0_281
	0x0f, 0x10, 0x11, //0x00001050 movups       (%rcx), %xmm2
	0x0f, 0x11, 0x95, 0x40, 0xff, 0xff, 0xff, //0x00001053 movups       %xmm2, $-192(%rbp)
	0xf3, 0x0f, 0x6f, 0x51, 0x10, //0x0000105a movdqu       $16(%rcx), %xmm2
	0xf3, 0x0f, 0x7f, 0x95, 0x50, 0xff, 0xff, 0xff, //0x0000105f movdqu       %xmm2, $-176(%rbp)
	0x48, 0x83, 0xc1, 0x20, //0x00001067 addq         $32, %rcx
	0x49, 0x8d, 0x7a, 0xe0, //0x0000106b leaq         $-32(%r10), %rdi
	0x48, 0x8d, 0xb5, 0x60, 0xff, 0xff, 0xff, //0x0000106f leaq         $-160(%rbp), %rsi
	0xe9, 0x16, 0x00, 0x00, 0x00, //0x00001076 jmp          LBB0_282
	//0x0000107b LBB0_280
	0x4c, 0x8b, 0x6d, 0xc0, //0x0000107b movq         $-64(%rbp), %r13
	0x48, 0x89, 0xcf, //0x0000107f movq         %rcx, %rdi
	0xe9, 0xc2, 0x00, 0x00, 0x00, //0x00001082 jmp          LBB0_267
	//0x00001087 LBB0_281
	0x48, 0x8d, 0xb5, 0x40, 0xff, 0xff, 0xff, //0x00001087 leaq         $-192(%rbp), %rsi
	0x4c, 0x89, 0xd7, //0x0000108e movq         %r10, %rdi
	//0x00001091 LBB0_282
	0x48, 0x83, 0xff, 0x10, //0x00001091 cmpq         $16, %rdi
	0x0f, 0x82, 0x47, 0x00, 0x00, 0x00, //0x00001095 jb           LBB0_283
	0xf3, 0x0f, 0x6f, 0x11, //0x0000109b movdqu       (%rcx), %xmm2
	0xf3, 0x0f, 0x7f, 0x16, //0x0000109f movdqu       %xmm2, (%rsi)
	0x48, 0x83, 0xc1, 0x10, //0x000010a3 addq         $16, %rcx
	0x48, 0x83, 0xc6, 0x10, //0x000010a7 addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x000010ab addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x000010af cmpq         $8, %rdi
	0x0f, 0x83, 0x33, 0x00, 0x00, 0x00, //0x000010b3 jae          LBB0_290
	//0x000010b9 LBB0_284
	0x48, 0x83, 0xff, 0x04, //0x000010b9 cmpq         $4, %rdi
	0x0f, 0x8c, 0x45, 0x00, 0x00, 0x00, //0x000010bd jl           LBB0_285
	//0x000010c3 LBB0_291
	0x8b, 0x01, //0x000010c3 movl         (%rcx), %eax
	0x89, 0x06, //0x000010c5 movl         %eax, (%rsi)
	0x48, 0x83, 0xc1, 0x04, //0x000010c7 addq         $4, %rcx
	0x48, 0x83, 0xc6, 0x04, //0x000010cb addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x000010cf addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x000010d3 cmpq         $2, %rdi
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x000010d7 jae          LBB0_286
	0xe9, 0x42, 0x00, 0x00, 0x00, //0x000010dd jmp          LBB0_287
	//0x000010e2 LBB0_283
	0x48, 0x83, 0xff, 0x08, //0x000010e2 cmpq         $8, %rdi
	0x0f, 0x82, 0xcd, 0xff, 0xff, 0xff, //0x000010e6 jb           LBB0_284
	//0x000010ec LBB0_290
	0x48, 0x8b, 0x01, //0x000010ec movq         (%rcx), %rax
	0x48, 0x89, 0x06, //0x000010ef movq         %rax, (%rsi)
	0x48, 0x83, 0xc1, 0x08, //0x000010f2 addq         $8, %rcx
	0x48, 0x83, 0xc6, 0x08, //0x000010f6 addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x000010fa addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x000010fe cmpq         $4, %rdi
	0x0f, 0x8d, 0xbb, 0xff, 0xff, 0xff, //0x00001102 jge          LBB0_291
	//0x00001108 LBB0_285
	0x48, 0x83, 0xff, 0x02, //0x00001108 cmpq         $2, %rdi
	0x0f, 0x82, 0x12, 0x00, 0x00, 0x00, //0x0000110c jb           LBB0_287
	//0x00001112 LBB0_286
	0x0f, 0xb7, 0x01, //0x00001112 movzwl       (%rcx), %eax
	0x66, 0x89, 0x06, //0x00001115 movw         %ax, (%rsi)
	0x48, 0x83, 0xc1, 0x02, //0x00001118 addq         $2, %rcx
	0x48, 0x83, 0xc6, 0x02, //0x0000111c addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x00001120 addq         $-2, %rdi
	//0x00001124 LBB0_287
	0x48, 0x89, 0xc8, //0x00001124 movq         %rcx, %rax
	0x48, 0x8d, 0x8d, 0x40, 0xff, 0xff, 0xff, //0x00001127 leaq         $-192(%rbp), %rcx
	0x48, 0x85, 0xff, //0x0000112e testq        %rdi, %rdi
	0x48, 0x89, 0xcf, //0x00001131 movq         %rcx, %rdi
	0x4c, 0x8b, 0x6d, 0xc0, //0x00001134 movq         $-64(%rbp), %r13
	0x0f, 0x84, 0x0b, 0x00, 0x00, 0x00, //0x00001138 je           LBB0_267
	0x8a, 0x00, //0x0000113e movb         (%rax), %al
	0x88, 0x06, //0x00001140 movb         %al, (%rsi)
	0x48, 0x8d, 0xbd, 0x40, 0xff, 0xff, 0xff, //0x00001142 leaq         $-192(%rbp), %rdi
	//0x00001149 LBB0_267
	0xf3, 0x0f, 0x6f, 0x17, //0x00001149 movdqu       (%rdi), %xmm2
	0xf3, 0x0f, 0x6f, 0x6f, 0x10, //0x0000114d movdqu       $16(%rdi), %xmm5
	0xf3, 0x0f, 0x6f, 0x7f, 0x20, //0x00001152 movdqu       $32(%rdi), %xmm7
	0x48, 0x89, 0x7d, 0xc8, //0x00001157 movq         %rdi, $-56(%rbp)
	0xf3, 0x0f, 0x6f, 0x77, 0x30, //0x0000115b movdqu       $48(%rdi), %xmm6
	0x66, 0x0f, 0x6f, 0xda, //0x00001160 movdqa       %xmm2, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00001164 pcmpeqb      %xmm0, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xf3, //0x00001168 pmovmskb     %xmm3, %r14d
	0x66, 0x0f, 0x6f, 0xdd, //0x0000116d movdqa       %xmm5, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00001171 pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xc3, //0x00001175 pmovmskb     %xmm3, %eax
	0x66, 0x0f, 0x6f, 0xdf, //0x00001179 movdqa       %xmm7, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x0000117d pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x00001181 pmovmskb     %xmm3, %edi
	0x66, 0x0f, 0x6f, 0xde, //0x00001185 movdqa       %xmm6, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00001189 pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xdb, //0x0000118d pmovmskb     %xmm3, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x00001191 shlq         $48, %rbx
	0x48, 0xc1, 0xe7, 0x20, //0x00001195 shlq         $32, %rdi
	0x48, 0xc1, 0xe0, 0x10, //0x00001199 shlq         $16, %rax
	0x49, 0x09, 0xc6, //0x0000119d orq          %rax, %r14
	0x49, 0x09, 0xfe, //0x000011a0 orq          %rdi, %r14
	0x49, 0x09, 0xde, //0x000011a3 orq          %rbx, %r14
	0x66, 0x0f, 0x6f, 0xda, //0x000011a6 movdqa       %xmm2, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x000011aa pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xc3, //0x000011ae pmovmskb     %xmm3, %eax
	0x66, 0x0f, 0x6f, 0xdd, //0x000011b2 movdqa       %xmm5, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x000011b6 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x000011ba pmovmskb     %xmm3, %edi
	0x66, 0x0f, 0x6f, 0xdf, //0x000011be movdqa       %xmm7, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x000011c2 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xdb, //0x000011c6 pmovmskb     %xmm3, %ebx
	0x66, 0x0f, 0x6f, 0xde, //0x000011ca movdqa       %xmm6, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x000011ce pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x000011d2 pmovmskb     %xmm3, %ecx
	0x48, 0xc1, 0xe1, 0x30, //0x000011d6 shlq         $48, %rcx
	0x48, 0xc1, 0xe3, 0x20, //0x000011da shlq         $32, %rbx
	0x48, 0xc1, 0xe7, 0x10, //0x000011de shlq         $16, %rdi
	0x48, 0x09, 0xf8, //0x000011e2 orq          %rdi, %rax
	0x48, 0x09, 0xd8, //0x000011e5 orq          %rbx, %rax
	0x48, 0x09, 0xc8, //0x000011e8 orq          %rcx, %rax
	0x48, 0x89, 0xc1, //0x000011eb movq         %rax, %rcx
	0x4d, 0x89, 0xef, //0x000011ee movq         %r13, %r15
	0x4c, 0x09, 0xc1, //0x000011f1 orq          %r8, %rcx
	0x0f, 0x84, 0x4b, 0x00, 0x00, 0x00, //0x000011f4 je           LBB0_269
	0x4c, 0x89, 0xc1, //0x000011fa movq         %r8, %rcx
	0x48, 0xf7, 0xd1, //0x000011fd notq         %rcx
	0x48, 0x21, 0xc1, //0x00001200 andq         %rax, %rcx
	0x48, 0x8d, 0x1c, 0x09, //0x00001203 leaq         (%rcx,%rcx), %rbx
	0x4c, 0x09, 0xc3, //0x00001207 orq          %r8, %rbx
	0x48, 0x89, 0xdf, //0x0000120a movq         %rbx, %rdi
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000120d movabsq      $-6148914691236517206, %rsi
	0x48, 0x31, 0xf7, //0x00001217 xorq         %rsi, %rdi
	0x48, 0x21, 0xf0, //0x0000121a andq         %rsi, %rax
	0x48, 0x21, 0xf8, //0x0000121d andq         %rdi, %rax
	0x45, 0x31, 0xc0, //0x00001220 xorl         %r8d, %r8d
	0x48, 0x01, 0xc8, //0x00001223 addq         %rcx, %rax
	0x41, 0x0f, 0x92, 0xc0, //0x00001226 setb         %r8b
	0x48, 0x01, 0xc0, //0x0000122a addq         %rax, %rax
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000122d movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xc8, //0x00001237 xorq         %rcx, %rax
	0x48, 0x21, 0xd8, //0x0000123a andq         %rbx, %rax
	0x48, 0xf7, 0xd0, //0x0000123d notq         %rax
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00001240 jmp          LBB0_270
	//0x00001245 LBB0_269
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00001245 movq         $-1, %rax
	0x45, 0x31, 0xc0, //0x0000124c xorl         %r8d, %r8d
	//0x0000124f LBB0_270
	0x4c, 0x21, 0xf0, //0x0000124f andq         %r14, %rax
	0x66, 0x48, 0x0f, 0x6e, 0xd8, //0x00001252 movq         %rax, %xmm3
	0x66, 0x41, 0x0f, 0x3a, 0x44, 0xd9, 0x00, //0x00001257 pclmulqdq    $0, %xmm9, %xmm3
	0x66, 0x49, 0x0f, 0x7e, 0xde, //0x0000125e movq         %xmm3, %r14
	0x4d, 0x31, 0xce, //0x00001263 xorq         %r9, %r14
	0x66, 0x0f, 0x6f, 0xda, //0x00001266 movdqa       %xmm2, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xda, //0x0000126a pcmpeqb      %xmm10, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xeb, //0x0000126f pmovmskb     %xmm3, %r13d
	0x66, 0x0f, 0x6f, 0xdd, //0x00001274 movdqa       %xmm5, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xda, //0x00001278 pcmpeqb      %xmm10, %xmm3
	0x66, 0x0f, 0xd7, 0xc3, //0x0000127d pmovmskb     %xmm3, %eax
	0x66, 0x0f, 0x6f, 0xdf, //0x00001281 movdqa       %xmm7, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xda, //0x00001285 pcmpeqb      %xmm10, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x0000128a pmovmskb     %xmm3, %ecx
	0x66, 0x0f, 0x6f, 0xde, //0x0000128e movdqa       %xmm6, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xda, //0x00001292 pcmpeqb      %xmm10, %xmm3
	0x66, 0x0f, 0xd7, 0xdb, //0x00001297 pmovmskb     %xmm3, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x0000129b shlq         $48, %rbx
	0x48, 0xc1, 0xe1, 0x20, //0x0000129f shlq         $32, %rcx
	0x48, 0xc1, 0xe0, 0x10, //0x000012a3 shlq         $16, %rax
	0x49, 0x09, 0xc5, //0x000012a7 orq          %rax, %r13
	0x49, 0x09, 0xcd, //0x000012aa orq          %rcx, %r13
	0x49, 0x09, 0xdd, //0x000012ad orq          %rbx, %r13
	0x4d, 0x89, 0xf1, //0x000012b0 movq         %r14, %r9
	0x49, 0xf7, 0xd1, //0x000012b3 notq         %r9
	0x4d, 0x21, 0xcd, //0x000012b6 andq         %r9, %r13
	0x66, 0x41, 0x0f, 0x74, 0xd3, //0x000012b9 pcmpeqb      %xmm11, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x000012be pmovmskb     %xmm2, %eax
	0x66, 0x41, 0x0f, 0x74, 0xeb, //0x000012c2 pcmpeqb      %xmm11, %xmm5
	0x66, 0x0f, 0xd7, 0xdd, //0x000012c7 pmovmskb     %xmm5, %ebx
	0x66, 0x41, 0x0f, 0x74, 0xfb, //0x000012cb pcmpeqb      %xmm11, %xmm7
	0x66, 0x0f, 0xd7, 0xf7, //0x000012d0 pmovmskb     %xmm7, %esi
	0x66, 0x41, 0x0f, 0x74, 0xf3, //0x000012d4 pcmpeqb      %xmm11, %xmm6
	0x66, 0x0f, 0xd7, 0xce, //0x000012d9 pmovmskb     %xmm6, %ecx
	0x48, 0xc1, 0xe1, 0x30, //0x000012dd shlq         $48, %rcx
	0x48, 0xc1, 0xe6, 0x20, //0x000012e1 shlq         $32, %rsi
	0x48, 0xc1, 0xe3, 0x10, //0x000012e5 shlq         $16, %rbx
	0x48, 0x09, 0xd8, //0x000012e9 orq          %rbx, %rax
	0x48, 0x09, 0xf0, //0x000012ec orq          %rsi, %rax
	0x48, 0x09, 0xc8, //0x000012ef orq          %rcx, %rax
	0x48, 0xbf, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x000012f2 movabsq      $3689348814741910323, %rdi
	0x4c, 0x21, 0xc8, //0x000012fc andq         %r9, %rax
	0x0f, 0x84, 0x77, 0x00, 0x00, 0x00, //0x000012ff je           LBB0_274
	0x49, 0xb9, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00001305 movabsq      $1085102592571150095, %r9
	0x90, //0x0000130f .p2align 4, 0x90
	//0x00001310 LBB0_272
	0x48, 0x8d, 0x58, 0xff, //0x00001310 leaq         $-1(%rax), %rbx
	0x48, 0x89, 0xd9, //0x00001314 movq         %rbx, %rcx
	0x4c, 0x21, 0xe9, //0x00001317 andq         %r13, %rcx
	0x48, 0x89, 0xce, //0x0000131a movq         %rcx, %rsi
	0x48, 0xd1, 0xee, //0x0000131d shrq         %rsi
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00001320 movabsq      $6148914691236517205, %rdx
	0x48, 0x21, 0xd6, //0x0000132a andq         %rdx, %rsi
	0x48, 0x29, 0xf1, //0x0000132d subq         %rsi, %rcx
	0x48, 0x89, 0xce, //0x00001330 movq         %rcx, %rsi
	0x48, 0x21, 0xfe, //0x00001333 andq         %rdi, %rsi
	0x48, 0xc1, 0xe9, 0x02, //0x00001336 shrq         $2, %rcx
	0x48, 0x21, 0xf9, //0x0000133a andq         %rdi, %rcx
	0x48, 0x01, 0xf1, //0x0000133d addq         %rsi, %rcx
	0x48, 0x89, 0xce, //0x00001340 movq         %rcx, %rsi
	0x48, 0xc1, 0xee, 0x04, //0x00001343 shrq         $4, %rsi
	0x48, 0x01, 0xce, //0x00001347 addq         %rcx, %rsi
	0x4c, 0x21, 0xce, //0x0000134a andq         %r9, %rsi
	0x48, 0xb9, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x0000134d movabsq      $72340172838076673, %rcx
	0x48, 0x0f, 0xaf, 0xf1, //0x00001357 imulq        %rcx, %rsi
	0x48, 0xc1, 0xee, 0x38, //0x0000135b shrq         $56, %rsi
	0x4c, 0x01, 0xde, //0x0000135f addq         %r11, %rsi
	0x4c, 0x39, 0xe6, //0x00001362 cmpq         %r12, %rsi
	0x0f, 0x86, 0x98, 0x04, 0x00, 0x00, //0x00001365 jbe          LBB0_318
	0x49, 0xff, 0xc4, //0x0000136b incq         %r12
	0x48, 0x21, 0xd8, //0x0000136e andq         %rbx, %rax
	0x0f, 0x85, 0x99, 0xff, 0xff, 0xff, //0x00001371 jne          LBB0_272
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00001377 jmp          LBB0_275
	//0x0000137c LBB0_274
	0x49, 0xb9, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x0000137c movabsq      $1085102592571150095, %r9
	//0x00001386 LBB0_275
	0x49, 0xc1, 0xfe, 0x3f, //0x00001386 sarq         $63, %r14
	0x4c, 0x89, 0xe8, //0x0000138a movq         %r13, %rax
	0x48, 0xd1, 0xe8, //0x0000138d shrq         %rax
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00001390 movabsq      $6148914691236517205, %rcx
	0x48, 0x21, 0xc8, //0x0000139a andq         %rcx, %rax
	0x49, 0x29, 0xc5, //0x0000139d subq         %rax, %r13
	0x4c, 0x89, 0xe8, //0x000013a0 movq         %r13, %rax
	0x48, 0x21, 0xf8, //0x000013a3 andq         %rdi, %rax
	0x49, 0xc1, 0xed, 0x02, //0x000013a6 shrq         $2, %r13
	0x49, 0x21, 0xfd, //0x000013aa andq         %rdi, %r13
	0x49, 0x01, 0xc5, //0x000013ad addq         %rax, %r13
	0x4c, 0x89, 0xe8, //0x000013b0 movq         %r13, %rax
	0x48, 0xc1, 0xe8, 0x04, //0x000013b3 shrq         $4, %rax
	0x4c, 0x01, 0xe8, //0x000013b7 addq         %r13, %rax
	0x4c, 0x21, 0xc8, //0x000013ba andq         %r9, %rax
	0x48, 0xb9, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x000013bd movabsq      $72340172838076673, %rcx
	0x48, 0x0f, 0xaf, 0xc1, //0x000013c7 imulq        %rcx, %rax
	0x48, 0xc1, 0xe8, 0x38, //0x000013cb shrq         $56, %rax
	0x49, 0x01, 0xc3, //0x000013cf addq         %rax, %r11
	0x48, 0x8b, 0x7d, 0xc8, //0x000013d2 movq         $-56(%rbp), %rdi
	0x48, 0x83, 0xc7, 0x40, //0x000013d6 addq         $64, %rdi
	0x49, 0x83, 0xc2, 0xc0, //0x000013da addq         $-64, %r10
	0x4d, 0x89, 0xf1, //0x000013de movq         %r14, %r9
	0x4d, 0x89, 0xfd, //0x000013e1 movq         %r15, %r13
	0x49, 0x83, 0xfa, 0x40, //0x000013e4 cmpq         $64, %r10
	0x0f, 0x8d, 0x5b, 0xfd, 0xff, 0xff, //0x000013e8 jge          LBB0_267
	0xe9, 0x0d, 0xfc, 0xff, 0xff, //0x000013ee jmp          LBB0_276
	//0x000013f3 LBB0_292
	0x4d, 0x8b, 0x10, //0x000013f3 movq         (%r8), %r10
	0x4d, 0x29, 0xf2, //0x000013f6 subq         %r14, %r10
	0x4c, 0x01, 0xf7, //0x000013f9 addq         %r14, %rdi
	0x45, 0x31, 0xc9, //0x000013fc xorl         %r9d, %r9d
	0x45, 0x31, 0xc0, //0x000013ff xorl         %r8d, %r8d
	0x45, 0x31, 0xdb, //0x00001402 xorl         %r11d, %r11d
	0x45, 0x31, 0xe4, //0x00001405 xorl         %r12d, %r12d
	0x49, 0x83, 0xfa, 0x40, //0x00001408 cmpq         $64, %r10
	0x0f, 0x8d, 0x49, 0x01, 0x00, 0x00, //0x0000140c jge          LBB0_293
	//0x00001412 LBB0_302
	0x48, 0x8b, 0x55, 0xa0, //0x00001412 movq         $-96(%rbp), %rdx
	0x4d, 0x85, 0xd2, //0x00001416 testq        %r10, %r10
	0x0f, 0x8e, 0x00, 0x0b, 0x00, 0x00, //0x00001419 jle          LBB0_340
	0x48, 0x89, 0xf9, //0x0000141f movq         %rdi, %rcx
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x70, 0xff, 0xff, 0xff, //0x00001422 movdqu       %xmm8, $-144(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x60, 0xff, 0xff, 0xff, //0x0000142b movdqu       %xmm8, $-160(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x50, 0xff, 0xff, 0xff, //0x00001434 movdqu       %xmm8, $-176(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x40, 0xff, 0xff, 0xff, //0x0000143d movdqu       %xmm8, $-192(%rbp)
	0x89, 0xc8, //0x00001446 movl         %ecx, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x00001448 andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x0000144d cmpl         $4033, %eax
	0x0f, 0x82, 0x35, 0x00, 0x00, 0x00, //0x00001452 jb           LBB0_306
	0x49, 0x83, 0xfa, 0x20, //0x00001458 cmpq         $32, %r10
	0x0f, 0x82, 0x37, 0x00, 0x00, 0x00, //0x0000145c jb           LBB0_307
	0x0f, 0x10, 0x11, //0x00001462 movups       (%rcx), %xmm2
	0x0f, 0x11, 0x95, 0x40, 0xff, 0xff, 0xff, //0x00001465 movups       %xmm2, $-192(%rbp)
	0xf3, 0x0f, 0x6f, 0x51, 0x10, //0x0000146c movdqu       $16(%rcx), %xmm2
	0xf3, 0x0f, 0x7f, 0x95, 0x50, 0xff, 0xff, 0xff, //0x00001471 movdqu       %xmm2, $-176(%rbp)
	0x48, 0x83, 0xc1, 0x20, //0x00001479 addq         $32, %rcx
	0x49, 0x8d, 0x7a, 0xe0, //0x0000147d leaq         $-32(%r10), %rdi
	0x48, 0x8d, 0xb5, 0x60, 0xff, 0xff, 0xff, //0x00001481 leaq         $-160(%rbp), %rsi
	0xe9, 0x16, 0x00, 0x00, 0x00, //0x00001488 jmp          LBB0_308
	//0x0000148d LBB0_306
	0x4c, 0x8b, 0x6d, 0xc0, //0x0000148d movq         $-64(%rbp), %r13
	0x48, 0x89, 0xcf, //0x00001491 movq         %rcx, %rdi
	0xe9, 0xc2, 0x00, 0x00, 0x00, //0x00001494 jmp          LBB0_293
	//0x00001499 LBB0_307
	0x48, 0x8d, 0xb5, 0x40, 0xff, 0xff, 0xff, //0x00001499 leaq         $-192(%rbp), %rsi
	0x4c, 0x89, 0xd7, //0x000014a0 movq         %r10, %rdi
	//0x000014a3 LBB0_308
	0x48, 0x83, 0xff, 0x10, //0x000014a3 cmpq         $16, %rdi
	0x0f, 0x82, 0x47, 0x00, 0x00, 0x00, //0x000014a7 jb           LBB0_309
	0xf3, 0x0f, 0x6f, 0x11, //0x000014ad movdqu       (%rcx), %xmm2
	0xf3, 0x0f, 0x7f, 0x16, //0x000014b1 movdqu       %xmm2, (%rsi)
	0x48, 0x83, 0xc1, 0x10, //0x000014b5 addq         $16, %rcx
	0x48, 0x83, 0xc6, 0x10, //0x000014b9 addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x000014bd addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x000014c1 cmpq         $8, %rdi
	0x0f, 0x83, 0x33, 0x00, 0x00, 0x00, //0x000014c5 jae          LBB0_316
	//0x000014cb LBB0_310
	0x48, 0x83, 0xff, 0x04, //0x000014cb cmpq         $4, %rdi
	0x0f, 0x8c, 0x45, 0x00, 0x00, 0x00, //0x000014cf jl           LBB0_311
	//0x000014d5 LBB0_317
	0x8b, 0x01, //0x000014d5 movl         (%rcx), %eax
	0x89, 0x06, //0x000014d7 movl         %eax, (%rsi)
	0x48, 0x83, 0xc1, 0x04, //0x000014d9 addq         $4, %rcx
	0x48, 0x83, 0xc6, 0x04, //0x000014dd addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x000014e1 addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x000014e5 cmpq         $2, %rdi
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x000014e9 jae          LBB0_312
	0xe9, 0x42, 0x00, 0x00, 0x00, //0x000014ef jmp          LBB0_313
	//0x000014f4 LBB0_309
	0x48, 0x83, 0xff, 0x08, //0x000014f4 cmpq         $8, %rdi
	0x0f, 0x82, 0xcd, 0xff, 0xff, 0xff, //0x000014f8 jb           LBB0_310
	//0x000014fe LBB0_316
	0x48, 0x8b, 0x01, //0x000014fe movq         (%rcx), %rax
	0x48, 0x89, 0x06, //0x00001501 movq         %rax, (%rsi)
	0x48, 0x83, 0xc1, 0x08, //0x00001504 addq         $8, %rcx
	0x48, 0x83, 0xc6, 0x08, //0x00001508 addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x0000150c addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x00001510 cmpq         $4, %rdi
	0x0f, 0x8d, 0xbb, 0xff, 0xff, 0xff, //0x00001514 jge          LBB0_317
	//0x0000151a LBB0_311
	0x48, 0x83, 0xff, 0x02, //0x0000151a cmpq         $2, %rdi
	0x0f, 0x82, 0x12, 0x00, 0x00, 0x00, //0x0000151e jb           LBB0_313
	//0x00001524 LBB0_312
	0x0f, 0xb7, 0x01, //0x00001524 movzwl       (%rcx), %eax
	0x66, 0x89, 0x06, //0x00001527 movw         %ax, (%rsi)
	0x48, 0x83, 0xc1, 0x02, //0x0000152a addq         $2, %rcx
	0x48, 0x83, 0xc6, 0x02, //0x0000152e addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x00001532 addq         $-2, %rdi
	//0x00001536 LBB0_313
	0x48, 0x89, 0xc8, //0x00001536 movq         %rcx, %rax
	0x48, 0x8d, 0x8d, 0x40, 0xff, 0xff, 0xff, //0x00001539 leaq         $-192(%rbp), %rcx
	0x48, 0x85, 0xff, //0x00001540 testq        %rdi, %rdi
	0x48, 0x89, 0xcf, //0x00001543 movq         %rcx, %rdi
	0x4c, 0x8b, 0x6d, 0xc0, //0x00001546 movq         $-64(%rbp), %r13
	0x0f, 0x84, 0x0b, 0x00, 0x00, 0x00, //0x0000154a je           LBB0_293
	0x8a, 0x00, //0x00001550 movb         (%rax), %al
	0x88, 0x06, //0x00001552 movb         %al, (%rsi)
	0x48, 0x8d, 0xbd, 0x40, 0xff, 0xff, 0xff, //0x00001554 leaq         $-192(%rbp), %rdi
	//0x0000155b LBB0_293
	0xf3, 0x0f, 0x6f, 0x17, //0x0000155b movdqu       (%rdi), %xmm2
	0xf3, 0x0f, 0x6f, 0x6f, 0x10, //0x0000155f movdqu       $16(%rdi), %xmm5
	0xf3, 0x0f, 0x6f, 0x7f, 0x20, //0x00001564 movdqu       $32(%rdi), %xmm7
	0x48, 0x89, 0x7d, 0xc8, //0x00001569 movq         %rdi, $-56(%rbp)
	0xf3, 0x0f, 0x6f, 0x77, 0x30, //0x0000156d movdqu       $48(%rdi), %xmm6
	0x66, 0x0f, 0x6f, 0xda, //0x00001572 movdqa       %xmm2, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00001576 pcmpeqb      %xmm0, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xf3, //0x0000157a pmovmskb     %xmm3, %r14d
	0x66, 0x0f, 0x6f, 0xdd, //0x0000157f movdqa       %xmm5, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00001583 pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xc3, //0x00001587 pmovmskb     %xmm3, %eax
	0x66, 0x0f, 0x6f, 0xdf, //0x0000158b movdqa       %xmm7, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x0000158f pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x00001593 pmovmskb     %xmm3, %edi
	0x66, 0x0f, 0x6f, 0xde, //0x00001597 movdqa       %xmm6, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x0000159b pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xdb, //0x0000159f pmovmskb     %xmm3, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x000015a3 shlq         $48, %rbx
	0x48, 0xc1, 0xe7, 0x20, //0x000015a7 shlq         $32, %rdi
	0x48, 0xc1, 0xe0, 0x10, //0x000015ab shlq         $16, %rax
	0x49, 0x09, 0xc6, //0x000015af orq          %rax, %r14
	0x49, 0x09, 0xfe, //0x000015b2 orq          %rdi, %r14
	0x49, 0x09, 0xde, //0x000015b5 orq          %rbx, %r14
	0x66, 0x0f, 0x6f, 0xda, //0x000015b8 movdqa       %xmm2, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x000015bc pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xc3, //0x000015c0 pmovmskb     %xmm3, %eax
	0x66, 0x0f, 0x6f, 0xdd, //0x000015c4 movdqa       %xmm5, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x000015c8 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x000015cc pmovmskb     %xmm3, %edi
	0x66, 0x0f, 0x6f, 0xdf, //0x000015d0 movdqa       %xmm7, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x000015d4 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xdb, //0x000015d8 pmovmskb     %xmm3, %ebx
	0x66, 0x0f, 0x6f, 0xde, //0x000015dc movdqa       %xmm6, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x000015e0 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x000015e4 pmovmskb     %xmm3, %ecx
	0x48, 0xc1, 0xe1, 0x30, //0x000015e8 shlq         $48, %rcx
	0x48, 0xc1, 0xe3, 0x20, //0x000015ec shlq         $32, %rbx
	0x48, 0xc1, 0xe7, 0x10, //0x000015f0 shlq         $16, %rdi
	0x48, 0x09, 0xf8, //0x000015f4 orq          %rdi, %rax
	0x48, 0x09, 0xd8, //0x000015f7 orq          %rbx, %rax
	0x48, 0x09, 0xc8, //0x000015fa orq          %rcx, %rax
	0x48, 0x89, 0xc1, //0x000015fd movq         %rax, %rcx
	0x4d, 0x89, 0xef, //0x00001600 movq         %r13, %r15
	0x4c, 0x09, 0xc1, //0x00001603 orq          %r8, %rcx
	0x0f, 0x84, 0x4b, 0x00, 0x00, 0x00, //0x00001606 je           LBB0_295
	0x4c, 0x89, 0xc1, //0x0000160c movq         %r8, %rcx
	0x48, 0xf7, 0xd1, //0x0000160f notq         %rcx
	0x48, 0x21, 0xc1, //0x00001612 andq         %rax, %rcx
	0x48, 0x8d, 0x1c, 0x09, //0x00001615 leaq         (%rcx,%rcx), %rbx
	0x4c, 0x09, 0xc3, //0x00001619 orq          %r8, %rbx
	0x48, 0x89, 0xdf, //0x0000161c movq         %rbx, %rdi
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000161f movabsq      $-6148914691236517206, %rsi
	0x48, 0x31, 0xf7, //0x00001629 xorq         %rsi, %rdi
	0x48, 0x21, 0xf0, //0x0000162c andq         %rsi, %rax
	0x48, 0x21, 0xf8, //0x0000162f andq         %rdi, %rax
	0x45, 0x31, 0xc0, //0x00001632 xorl         %r8d, %r8d
	0x48, 0x01, 0xc8, //0x00001635 addq         %rcx, %rax
	0x41, 0x0f, 0x92, 0xc0, //0x00001638 setb         %r8b
	0x48, 0x01, 0xc0, //0x0000163c addq         %rax, %rax
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000163f movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xc8, //0x00001649 xorq         %rcx, %rax
	0x48, 0x21, 0xd8, //0x0000164c andq         %rbx, %rax
	0x48, 0xf7, 0xd0, //0x0000164f notq         %rax
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00001652 jmp          LBB0_296
	//0x00001657 LBB0_295
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00001657 movq         $-1, %rax
	0x45, 0x31, 0xc0, //0x0000165e xorl         %r8d, %r8d
	//0x00001661 LBB0_296
	0x4c, 0x21, 0xf0, //0x00001661 andq         %r14, %rax
	0x66, 0x48, 0x0f, 0x6e, 0xd8, //0x00001664 movq         %rax, %xmm3
	0x66, 0x41, 0x0f, 0x3a, 0x44, 0xd9, 0x00, //0x00001669 pclmulqdq    $0, %xmm9, %xmm3
	0x66, 0x49, 0x0f, 0x7e, 0xde, //0x00001670 movq         %xmm3, %r14
	0x4d, 0x31, 0xce, //0x00001675 xorq         %r9, %r14
	0x66, 0x0f, 0x6f, 0xda, //0x00001678 movdqa       %xmm2, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdc, //0x0000167c pcmpeqb      %xmm12, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xeb, //0x00001681 pmovmskb     %xmm3, %r13d
	0x66, 0x0f, 0x6f, 0xdd, //0x00001686 movdqa       %xmm5, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdc, //0x0000168a pcmpeqb      %xmm12, %xmm3
	0x66, 0x0f, 0xd7, 0xc3, //0x0000168f pmovmskb     %xmm3, %eax
	0x66, 0x0f, 0x6f, 0xdf, //0x00001693 movdqa       %xmm7, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdc, //0x00001697 pcmpeqb      %xmm12, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x0000169c pmovmskb     %xmm3, %ecx
	0x66, 0x0f, 0x6f, 0xde, //0x000016a0 movdqa       %xmm6, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdc, //0x000016a4 pcmpeqb      %xmm12, %xmm3
	0x66, 0x0f, 0xd7, 0xdb, //0x000016a9 pmovmskb     %xmm3, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x000016ad shlq         $48, %rbx
	0x48, 0xc1, 0xe1, 0x20, //0x000016b1 shlq         $32, %rcx
	0x48, 0xc1, 0xe0, 0x10, //0x000016b5 shlq         $16, %rax
	0x49, 0x09, 0xc5, //0x000016b9 orq          %rax, %r13
	0x49, 0x09, 0xcd, //0x000016bc orq          %rcx, %r13
	0x49, 0x09, 0xdd, //0x000016bf orq          %rbx, %r13
	0x4d, 0x89, 0xf1, //0x000016c2 movq         %r14, %r9
	0x49, 0xf7, 0xd1, //0x000016c5 notq         %r9
	0x4d, 0x21, 0xcd, //0x000016c8 andq         %r9, %r13
	0x66, 0x0f, 0x74, 0xd4, //0x000016cb pcmpeqb      %xmm4, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x000016cf pmovmskb     %xmm2, %eax
	0x66, 0x0f, 0x74, 0xec, //0x000016d3 pcmpeqb      %xmm4, %xmm5
	0x66, 0x0f, 0xd7, 0xdd, //0x000016d7 pmovmskb     %xmm5, %ebx
	0x66, 0x0f, 0x74, 0xfc, //0x000016db pcmpeqb      %xmm4, %xmm7
	0x66, 0x0f, 0xd7, 0xf7, //0x000016df pmovmskb     %xmm7, %esi
	0x66, 0x0f, 0x74, 0xf4, //0x000016e3 pcmpeqb      %xmm4, %xmm6
	0x66, 0x0f, 0xd7, 0xce, //0x000016e7 pmovmskb     %xmm6, %ecx
	0x48, 0xc1, 0xe1, 0x30, //0x000016eb shlq         $48, %rcx
	0x48, 0xc1, 0xe6, 0x20, //0x000016ef shlq         $32, %rsi
	0x48, 0xc1, 0xe3, 0x10, //0x000016f3 shlq         $16, %rbx
	0x48, 0x09, 0xd8, //0x000016f7 orq          %rbx, %rax
	0x48, 0x09, 0xf0, //0x000016fa orq          %rsi, %rax
	0x48, 0x09, 0xc8, //0x000016fd orq          %rcx, %rax
	0x48, 0xbf, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x00001700 movabsq      $3689348814741910323, %rdi
	0x4c, 0x21, 0xc8, //0x0000170a andq         %r9, %rax
	0x0f, 0x84, 0x79, 0x00, 0x00, 0x00, //0x0000170d je           LBB0_300
	0x49, 0xb9, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00001713 movabsq      $1085102592571150095, %r9
	0x90, 0x90, 0x90, //0x0000171d .p2align 4, 0x90
	//0x00001720 LBB0_298
	0x48, 0x8d, 0x58, 0xff, //0x00001720 leaq         $-1(%rax), %rbx
	0x48, 0x89, 0xd9, //0x00001724 movq         %rbx, %rcx
	0x4c, 0x21, 0xe9, //0x00001727 andq         %r13, %rcx
	0x48, 0x89, 0xce, //0x0000172a movq         %rcx, %rsi
	0x48, 0xd1, 0xee, //0x0000172d shrq         %rsi
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00001730 movabsq      $6148914691236517205, %rdx
	0x48, 0x21, 0xd6, //0x0000173a andq         %rdx, %rsi
	0x48, 0x29, 0xf1, //0x0000173d subq         %rsi, %rcx
	0x48, 0x89, 0xce, //0x00001740 movq         %rcx, %rsi
	0x48, 0x21, 0xfe, //0x00001743 andq         %rdi, %rsi
	0x48, 0xc1, 0xe9, 0x02, //0x00001746 shrq         $2, %rcx
	0x48, 0x21, 0xf9, //0x0000174a andq         %rdi, %rcx
	0x48, 0x01, 0xf1, //0x0000174d addq         %rsi, %rcx
	0x48, 0x89, 0xce, //0x00001750 movq         %rcx, %rsi
	0x48, 0xc1, 0xee, 0x04, //0x00001753 shrq         $4, %rsi
	0x48, 0x01, 0xce, //0x00001757 addq         %rcx, %rsi
	0x4c, 0x21, 0xce, //0x0000175a andq         %r9, %rsi
	0x48, 0xb9, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x0000175d movabsq      $72340172838076673, %rcx
	0x48, 0x0f, 0xaf, 0xf1, //0x00001767 imulq        %rcx, %rsi
	0x48, 0xc1, 0xee, 0x38, //0x0000176b shrq         $56, %rsi
	0x4c, 0x01, 0xde, //0x0000176f addq         %r11, %rsi
	0x4c, 0x39, 0xe6, //0x00001772 cmpq         %r12, %rsi
	0x0f, 0x86, 0x88, 0x00, 0x00, 0x00, //0x00001775 jbe          LBB0_318
	0x49, 0xff, 0xc4, //0x0000177b incq         %r12
	0x48, 0x21, 0xd8, //0x0000177e andq         %rbx, %rax
	0x0f, 0x85, 0x99, 0xff, 0xff, 0xff, //0x00001781 jne          LBB0_298
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00001787 jmp          LBB0_301
	//0x0000178c LBB0_300
	0x49, 0xb9, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x0000178c movabsq      $1085102592571150095, %r9
	//0x00001796 LBB0_301
	0x49, 0xc1, 0xfe, 0x3f, //0x00001796 sarq         $63, %r14
	0x4c, 0x89, 0xe8, //0x0000179a movq         %r13, %rax
	0x48, 0xd1, 0xe8, //0x0000179d shrq         %rax
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000017a0 movabsq      $6148914691236517205, %rcx
	0x48, 0x21, 0xc8, //0x000017aa andq         %rcx, %rax
	0x49, 0x29, 0xc5, //0x000017ad subq         %rax, %r13
	0x4c, 0x89, 0xe8, //0x000017b0 movq         %r13, %rax
	0x48, 0x21, 0xf8, //0x000017b3 andq         %rdi, %rax
	0x49, 0xc1, 0xed, 0x02, //0x000017b6 shrq         $2, %r13
	0x49, 0x21, 0xfd, //0x000017ba andq         %rdi, %r13
	0x49, 0x01, 0xc5, //0x000017bd addq         %rax, %r13
	0x4c, 0x89, 0xe8, //0x000017c0 movq         %r13, %rax
	0x48, 0xc1, 0xe8, 0x04, //0x000017c3 shrq         $4, %rax
	0x4c, 0x01, 0xe8, //0x000017c7 addq         %r13, %rax
	0x4c, 0x21, 0xc8, //0x000017ca andq         %r9, %rax
	0x48, 0xb9, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x000017cd movabsq      $72340172838076673, %rcx
	0x48, 0x0f, 0xaf, 0xc1, //0x000017d7 imulq        %rcx, %rax
	0x48, 0xc1, 0xe8, 0x38, //0x000017db shrq         $56, %rax
	0x49, 0x01, 0xc3, //0x000017df addq         %rax, %r11
	0x48, 0x8b, 0x7d, 0xc8, //0x000017e2 movq         $-56(%rbp), %rdi
	0x48, 0x83, 0xc7, 0x40, //0x000017e6 addq         $64, %rdi
	0x49, 0x83, 0xc2, 0xc0, //0x000017ea addq         $-64, %r10
	0x4d, 0x89, 0xf1, //0x000017ee movq         %r14, %r9
	0x4d, 0x89, 0xfd, //0x000017f1 movq         %r15, %r13
	0x49, 0x83, 0xfa, 0x40, //0x000017f4 cmpq         $64, %r10
	0x0f, 0x8d, 0x5d, 0xfd, 0xff, 0xff, //0x000017f8 jge          LBB0_293
	0xe9, 0x0f, 0xfc, 0xff, 0xff, //0x000017fe jmp          LBB0_302
	//0x00001803 LBB0_318
	0x4c, 0x8b, 0x45, 0xb0, //0x00001803 movq         $-80(%rbp), %r8
	0x49, 0x8b, 0x08, //0x00001807 movq         (%r8), %rcx
	0x48, 0x0f, 0xbc, 0xc0, //0x0000180a bsfq         %rax, %rax
	0x4c, 0x29, 0xd0, //0x0000180e subq         %r10, %rax
	0x4c, 0x8d, 0x74, 0x08, 0x01, //0x00001811 leaq         $1(%rax,%rcx), %r14
	0x4c, 0x8b, 0x5d, 0xd0, //0x00001816 movq         $-48(%rbp), %r11
	0x4d, 0x89, 0x33, //0x0000181a movq         %r14, (%r11)
	0x49, 0x8b, 0x00, //0x0000181d movq         (%r8), %rax
	0x49, 0x39, 0xc6, //0x00001820 cmpq         %rax, %r14
	0x4c, 0x0f, 0x47, 0xf0, //0x00001823 cmovaq       %rax, %r14
	0x4d, 0x89, 0x33, //0x00001827 movq         %r14, (%r11)
	0x49, 0xba, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000182a movabsq      $4294977024, %r10
	0x4d, 0x89, 0xfd, //0x00001834 movq         %r15, %r13
	0x4c, 0x8b, 0x4d, 0xa0, //0x00001837 movq         $-96(%rbp), %r9
	0xe9, 0x00, 0xf5, 0xff, 0xff, //0x0000183b jmp          LBB0_224
	//0x00001840 LBB0_68
	0x48, 0x8b, 0x4d, 0x88, //0x00001840 movq         $-120(%rbp), %rcx
	0x4c, 0x8b, 0x6d, 0xc0, //0x00001844 movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x4d, 0xa0, //0x00001848 movq         $-96(%rbp), %r9
	0x49, 0xba, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000184c movabsq      $4294977024, %r10
	0x4c, 0x8b, 0x75, 0x80, //0x00001856 movq         $-128(%rbp), %r14
	0x48, 0x83, 0xf9, 0x20, //0x0000185a cmpq         $32, %rcx
	0x0f, 0x82, 0xa7, 0x00, 0x00, 0x00, //0x0000185e jb           LBB0_106
	//0x00001864 LBB0_69
	0xf3, 0x41, 0x0f, 0x6f, 0x16, //0x00001864 movdqu       (%r14), %xmm2
	0xf3, 0x41, 0x0f, 0x6f, 0x5e, 0x10, //0x00001869 movdqu       $16(%r14), %xmm3
	0x66, 0x0f, 0x6f, 0xea, //0x0000186f movdqa       %xmm2, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00001873 pcmpeqb      %xmm0, %xmm5
	0x66, 0x44, 0x0f, 0xd7, 0xc5, //0x00001877 pmovmskb     %xmm5, %r8d
	0x66, 0x0f, 0x6f, 0xeb, //0x0000187c movdqa       %xmm3, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00001880 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xf5, //0x00001884 pmovmskb     %xmm5, %esi
	0x66, 0x0f, 0x74, 0xd1, //0x00001888 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xfa, //0x0000188c pmovmskb     %xmm2, %edi
	0x66, 0x0f, 0x74, 0xd9, //0x00001890 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xd3, //0x00001894 pmovmskb     %xmm3, %edx
	0x48, 0xc1, 0xe6, 0x10, //0x00001898 shlq         $16, %rsi
	0x48, 0xc1, 0xe2, 0x10, //0x0000189c shlq         $16, %rdx
	0x48, 0x09, 0xd7, //0x000018a0 orq          %rdx, %rdi
	0x49, 0x83, 0xfb, 0xff, //0x000018a3 cmpq         $-1, %r11
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x000018a7 jne          LBB0_71
	0x48, 0x85, 0xff, //0x000018ad testq        %rdi, %rdi
	0x0f, 0x85, 0xe3, 0x04, 0x00, 0x00, //0x000018b0 jne          LBB0_326
	//0x000018b6 LBB0_71
	0x4c, 0x09, 0xc6, //0x000018b6 orq          %r8, %rsi
	0x48, 0x89, 0xfa, //0x000018b9 movq         %rdi, %rdx
	0x48, 0x09, 0xda, //0x000018bc orq          %rbx, %rdx
	0x0f, 0x85, 0xf1, 0x04, 0x00, 0x00, //0x000018bf jne          LBB0_327
	//0x000018c5 LBB0_72
	0x4c, 0x8b, 0x45, 0xb0, //0x000018c5 movq         $-80(%rbp), %r8
	0x48, 0x8b, 0x7d, 0xc8, //0x000018c9 movq         $-56(%rbp), %rdi
	0x48, 0x85, 0xf6, //0x000018cd testq        %rsi, %rsi
	0x0f, 0x84, 0x31, 0x05, 0x00, 0x00, //0x000018d0 je           LBB0_328
	//0x000018d6 LBB0_73
	0x48, 0x0f, 0xbc, 0xce, //0x000018d6 bsfq         %rsi, %rcx
	0x49, 0x29, 0xfe, //0x000018da subq         %rdi, %r14
	0x4d, 0x8d, 0x74, 0x0e, 0x01, //0x000018dd leaq         $1(%r14,%rcx), %r14
	0x4c, 0x8b, 0x7d, 0x90, //0x000018e2 movq         $-112(%rbp), %r15
	0x4d, 0x85, 0xf6, //0x000018e6 testq        %r14, %r14
	0x48, 0x8b, 0x5d, 0xb8, //0x000018e9 movq         $-72(%rbp), %rbx
	0x0f, 0x89, 0x84, 0xee, 0xff, 0xff, //0x000018ed jns          LBB0_79
	0xe9, 0xfc, 0x3b, 0x00, 0x00, //0x000018f3 jmp          LBB0_951
	//0x000018f8 LBB0_105
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000018f8 movq         $-1, %r11
	0x31, 0xdb, //0x000018ff xorl         %ebx, %ebx
	0x48, 0x83, 0xf9, 0x20, //0x00001901 cmpq         $32, %rcx
	0x0f, 0x83, 0x59, 0xff, 0xff, 0xff, //0x00001905 jae          LBB0_69
	//0x0000190b LBB0_106
	0x48, 0x8b, 0x7d, 0xc8, //0x0000190b movq         $-56(%rbp), %rdi
	0xe9, 0xfb, 0x04, 0x00, 0x00, //0x0000190f jmp          LBB0_329
	//0x00001914 LBB0_107
	0x48, 0xc7, 0x85, 0x40, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00, //0x00001914 movq         $0, $-192(%rbp)
	0x4a, 0x8d, 0x54, 0x37, 0xff, //0x0000191f leaq         $-1(%rdi,%r14), %rdx
	0x48, 0x8b, 0x45, 0x98, //0x00001924 movq         $-104(%rbp), %rax
	0x4d, 0x8d, 0x14, 0x07, //0x00001928 leaq         (%r15,%rax), %r10
	0x48, 0x85, 0xc0, //0x0000192c testq        %rax, %rax
	0x0f, 0x8e, 0x20, 0x04, 0x00, 0x00, //0x0000192f jle          LBB0_153
	0x48, 0x39, 0xda, //0x00001935 cmpq         %rbx, %rdx
	0x4c, 0x8b, 0x5d, 0xd0, //0x00001938 movq         $-48(%rbp), %r11
	0x0f, 0x86, 0x17, 0x04, 0x00, 0x00, //0x0000193c jbe          LBB0_154
	//0x00001942 LBB0_109
	0x8a, 0x03, //0x00001942 movb         (%rbx), %al
	0x3c, 0x5c, //0x00001944 cmpb         $92, %al
	0x0f, 0x85, 0x45, 0x00, 0x00, 0x00, //0x00001946 jne          LBB0_114
	0x48, 0x89, 0xd7, //0x0000194c movq         %rdx, %rdi
	0x48, 0x29, 0xdf, //0x0000194f subq         %rbx, %rdi
	0x48, 0x85, 0xff, //0x00001952 testq        %rdi, %rdi
	0x0f, 0x8e, 0x96, 0x3d, 0x00, 0x00, //0x00001955 jle          LBB0_972
	0x49, 0x89, 0xdc, //0x0000195b movq         %rbx, %r12
	0x0f, 0xb6, 0x43, 0x01, //0x0000195e movzbl       $1(%rbx), %eax
	0x48, 0x8d, 0x0d, 0xc7, 0x47, 0x00, 0x00, //0x00001962 leaq         $18375(%rip), %rcx  /* __UnquoteTab+0(%rip) */
	0x8a, 0x1c, 0x08, //0x00001969 movb         (%rax,%rcx), %bl
	0x80, 0xfb, 0xff, //0x0000196c cmpb         $-1, %bl
	0x0f, 0x84, 0x30, 0x00, 0x00, 0x00, //0x0000196f je           LBB0_116
	0x84, 0xdb, //0x00001975 testb        %bl, %bl
	0x0f, 0x84, 0x5c, 0x3d, 0x00, 0x00, //0x00001977 je           LBB0_970
	0x88, 0x9d, 0x40, 0xff, 0xff, 0xff, //0x0000197d movb         %bl, $-192(%rbp)
	0x49, 0x83, 0xc4, 0x02, //0x00001983 addq         $2, %r12
	0xbf, 0x01, 0x00, 0x00, 0x00, //0x00001987 movl         $1, %edi
	0xe9, 0x1e, 0x01, 0x00, 0x00, //0x0000198c jmp          LBB0_127
	//0x00001991 LBB0_114
	0x41, 0x3a, 0x07, //0x00001991 cmpb         (%r15), %al
	0x0f, 0x85, 0xef, 0x03, 0x00, 0x00, //0x00001994 jne          LBB0_324
	0x48, 0xff, 0xc3, //0x0000199a incq         %rbx
	0x49, 0xff, 0xc7, //0x0000199d incq         %r15
	0xe9, 0xa7, 0x01, 0x00, 0x00, //0x000019a0 jmp          LBB0_139
	//0x000019a5 LBB0_116
	0x48, 0x83, 0xff, 0x03, //0x000019a5 cmpq         $3, %rdi
	0x0f, 0x8e, 0x3c, 0x3d, 0x00, 0x00, //0x000019a9 jle          LBB0_971
	0x49, 0x89, 0xd1, //0x000019af movq         %rdx, %r9
	0x41, 0x8b, 0x44, 0x24, 0x02, //0x000019b2 movl         $2(%r12), %eax
	0x89, 0xc6, //0x000019b7 movl         %eax, %esi
	0xf7, 0xd6, //0x000019b9 notl         %esi
	0x8d, 0x90, 0xd0, 0xcf, 0xcf, 0xcf, //0x000019bb leal         $-808464432(%rax), %edx
	0x81, 0xe6, 0x80, 0x80, 0x80, 0x80, //0x000019c1 andl         $-2139062144, %esi
	0x85, 0xd6, //0x000019c7 testl        %edx, %esi
	0x0f, 0x85, 0x61, 0x3c, 0x00, 0x00, //0x000019c9 jne          LBB0_968
	0x8d, 0x90, 0x19, 0x19, 0x19, 0x19, //0x000019cf leal         $421075225(%rax), %edx
	0x09, 0xc2, //0x000019d5 orl          %eax, %edx
	0xf7, 0xc2, 0x80, 0x80, 0x80, 0x80, //0x000019d7 testl        $-2139062144, %edx
	0x0f, 0x85, 0x4d, 0x3c, 0x00, 0x00, //0x000019dd jne          LBB0_968
	0x89, 0xc2, //0x000019e3 movl         %eax, %edx
	0x81, 0xe2, 0x7f, 0x7f, 0x7f, 0x7f, //0x000019e5 andl         $2139062143, %edx
	0xbb, 0xc0, 0xc0, 0xc0, 0xc0, //0x000019eb movl         $-1061109568, %ebx
	0x29, 0xd3, //0x000019f0 subl         %edx, %ebx
	0x8d, 0x8a, 0x46, 0x46, 0x46, 0x46, //0x000019f2 leal         $1179010630(%rdx), %ecx
	0x21, 0xf3, //0x000019f8 andl         %esi, %ebx
	0x85, 0xcb, //0x000019fa testl        %ecx, %ebx
	0x0f, 0x85, 0x2e, 0x3c, 0x00, 0x00, //0x000019fc jne          LBB0_968
	0xb9, 0xe0, 0xe0, 0xe0, 0xe0, //0x00001a02 movl         $-522133280, %ecx
	0x29, 0xd1, //0x00001a07 subl         %edx, %ecx
	0x81, 0xc2, 0x39, 0x39, 0x39, 0x39, //0x00001a09 addl         $960051513, %edx
	0x21, 0xce, //0x00001a0f andl         %ecx, %esi
	0x85, 0xd6, //0x00001a11 testl        %edx, %esi
	0x0f, 0x85, 0x17, 0x3c, 0x00, 0x00, //0x00001a13 jne          LBB0_968
	0x0f, 0xc8, //0x00001a19 bswapl       %eax
	0x89, 0xc1, //0x00001a1b movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x00001a1d shrl         $4, %ecx
	0xf7, 0xd1, //0x00001a20 notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x00001a22 andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x00001a28 leal         (%rcx,%rcx,8), %ecx
	0x25, 0x0f, 0x0f, 0x0f, 0x0f, //0x00001a2b andl         $252645135, %eax
	0x01, 0xc8, //0x00001a30 addl         %ecx, %eax
	0x89, 0xc3, //0x00001a32 movl         %eax, %ebx
	0xc1, 0xeb, 0x04, //0x00001a34 shrl         $4, %ebx
	0x09, 0xc3, //0x00001a37 orl          %eax, %ebx
	0x89, 0xde, //0x00001a39 movl         %ebx, %esi
	0xc1, 0xee, 0x08, //0x00001a3b shrl         $8, %esi
	0x81, 0xe6, 0x00, 0xff, 0x00, 0x00, //0x00001a3e andl         $65280, %esi
	0x0f, 0xb6, 0xd3, //0x00001a44 movzbl       %bl, %edx
	0x09, 0xf2, //0x00001a47 orl          %esi, %edx
	0x4d, 0x8d, 0x44, 0x24, 0x06, //0x00001a49 leaq         $6(%r12), %r8
	0x83, 0xfa, 0x7f, //0x00001a4e cmpl         $127, %edx
	0x0f, 0x86, 0x0c, 0x01, 0x00, 0x00, //0x00001a51 jbe          LBB0_141
	0x81, 0xfa, 0xff, 0x07, 0x00, 0x00, //0x00001a57 cmpl         $2047, %edx
	0x0f, 0x86, 0x10, 0x01, 0x00, 0x00, //0x00001a5d jbe          LBB0_142
	0x89, 0xd8, //0x00001a63 movl         %ebx, %eax
	0x25, 0x00, 0x00, 0xf8, 0x00, //0x00001a65 andl         $16252928, %eax
	0x3d, 0x00, 0x00, 0xd8, 0x00, //0x00001a6a cmpl         $14155776, %eax
	0x0f, 0x84, 0x22, 0x01, 0x00, 0x00, //0x00001a6f je           LBB0_143
	0xc1, 0xee, 0x0c, //0x00001a75 shrl         $12, %esi
	0x40, 0x80, 0xce, 0xe0, //0x00001a78 orb          $-32, %sil
	0x40, 0x88, 0xb5, 0x40, 0xff, 0xff, 0xff, //0x00001a7c movb         %sil, $-192(%rbp)
	0xc1, 0xea, 0x06, //0x00001a83 shrl         $6, %edx
	0x80, 0xe2, 0x3f, //0x00001a86 andb         $63, %dl
	0x80, 0xca, 0x80, //0x00001a89 orb          $-128, %dl
	0x88, 0x95, 0x41, 0xff, 0xff, 0xff, //0x00001a8c movb         %dl, $-191(%rbp)
	0x80, 0xe3, 0x3f, //0x00001a92 andb         $63, %bl
	0x80, 0xcb, 0x80, //0x00001a95 orb          $-128, %bl
	0x88, 0x9d, 0x42, 0xff, 0xff, 0xff, //0x00001a98 movb         %bl, $-190(%rbp)
	0xbf, 0x03, 0x00, 0x00, 0x00, //0x00001a9e movl         $3, %edi
	0x89, 0xf3, //0x00001aa3 movl         %esi, %ebx
	//0x00001aa5 LBB0_125
	0x4d, 0x89, 0xc4, //0x00001aa5 movq         %r8, %r12
	//0x00001aa8 LBB0_126
	0x4c, 0x8b, 0x45, 0xb0, //0x00001aa8 movq         $-80(%rbp), %r8
	0x4c, 0x89, 0xca, //0x00001aac movq         %r9, %rdx
	//0x00001aaf LBB0_127
	0x4c, 0x8d, 0x8c, 0x3d, 0x40, 0xff, 0xff, 0xff, //0x00001aaf leaq         $-192(%rbp,%rdi), %r9
	0x4d, 0x39, 0xd7, //0x00001ab7 cmpq         %r10, %r15
	0x0f, 0x83, 0x75, 0x00, 0x00, 0x00, //0x00001aba jae          LBB0_136
	0x48, 0x8d, 0x85, 0x40, 0xff, 0xff, 0xff, //0x00001ac0 leaq         $-192(%rbp), %rax
	0x49, 0x39, 0xc1, //0x00001ac7 cmpq         %rax, %r9
	0x48, 0x8b, 0x7d, 0xc8, //0x00001aca movq         $-56(%rbp), %rdi
	0x0f, 0x86, 0x55, 0x00, 0x00, 0x00, //0x00001ace jbe          LBB0_135
	0x41, 0x38, 0x1f, //0x00001ad4 cmpb         %bl, (%r15)
	0x0f, 0x85, 0x4c, 0x00, 0x00, 0x00, //0x00001ad7 jne          LBB0_135
	0x48, 0x89, 0x55, 0x98, //0x00001add movq         %rdx, $-104(%rbp)
	0x49, 0xff, 0xc7, //0x00001ae1 incq         %r15
	0x48, 0x8d, 0x85, 0x41, 0xff, 0xff, 0xff, //0x00001ae4 leaq         $-191(%rbp), %rax
	0x4c, 0x89, 0xe3, //0x00001aeb movq         %r12, %rbx
	//0x00001aee LBB0_131
	0x4c, 0x89, 0xff, //0x00001aee movq         %r15, %rdi
	0x48, 0x89, 0xc6, //0x00001af1 movq         %rax, %rsi
	0x4c, 0x39, 0xc8, //0x00001af4 cmpq         %r9, %rax
	0x0f, 0x83, 0x1c, 0x00, 0x00, 0x00, //0x00001af7 jae          LBB0_134
	0x4c, 0x39, 0xd7, //0x00001afd cmpq         %r10, %rdi
	0x0f, 0x83, 0x13, 0x00, 0x00, 0x00, //0x00001b00 jae          LBB0_134
	0x0f, 0xb6, 0x0f, //0x00001b06 movzbl       (%rdi), %ecx
	0x4c, 0x8d, 0x7f, 0x01, //0x00001b09 leaq         $1(%rdi), %r15
	0x48, 0x8d, 0x46, 0x01, //0x00001b0d leaq         $1(%rsi), %rax
	0x3a, 0x0e, //0x00001b11 cmpb         (%rsi), %cl
	0x0f, 0x84, 0xd5, 0xff, 0xff, 0xff, //0x00001b13 je           LBB0_131
	//0x00001b19 LBB0_134
	0x49, 0x89, 0xff, //0x00001b19 movq         %rdi, %r15
	0x48, 0x8b, 0x7d, 0xc8, //0x00001b1c movq         $-56(%rbp), %rdi
	0x48, 0x8b, 0x55, 0x98, //0x00001b20 movq         $-104(%rbp), %rdx
	0xe9, 0x1a, 0x00, 0x00, 0x00, //0x00001b24 jmp          LBB0_138
	//0x00001b29 LBB0_135
	0x48, 0x8d, 0xb5, 0x40, 0xff, 0xff, 0xff, //0x00001b29 leaq         $-192(%rbp), %rsi
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x00001b30 jmp          LBB0_137
	//0x00001b35 LBB0_136
	0x48, 0x8d, 0xb5, 0x40, 0xff, 0xff, 0xff, //0x00001b35 leaq         $-192(%rbp), %rsi
	0x48, 0x8b, 0x7d, 0xc8, //0x00001b3c movq         $-56(%rbp), %rdi
	//0x00001b40 LBB0_137
	0x4c, 0x89, 0xe3, //0x00001b40 movq         %r12, %rbx
	//0x00001b43 LBB0_138
	0x4c, 0x39, 0xce, //0x00001b43 cmpq         %r9, %rsi
	0x0f, 0x85, 0x3d, 0x02, 0x00, 0x00, //0x00001b46 jne          LBB0_324
	//0x00001b4c LBB0_139
	0x48, 0x39, 0xda, //0x00001b4c cmpq         %rbx, %rdx
	0x0f, 0x86, 0x04, 0x02, 0x00, 0x00, //0x00001b4f jbe          LBB0_154
	0x4d, 0x39, 0xd7, //0x00001b55 cmpq         %r10, %r15
	0x0f, 0x82, 0xe4, 0xfd, 0xff, 0xff, //0x00001b58 jb           LBB0_109
	0xe9, 0xf6, 0x01, 0x00, 0x00, //0x00001b5e jmp          LBB0_154
	//0x00001b63 LBB0_141
	0x88, 0x9d, 0x40, 0xff, 0xff, 0xff, //0x00001b63 movb         %bl, $-192(%rbp)
	0xbf, 0x01, 0x00, 0x00, 0x00, //0x00001b69 movl         $1, %edi
	0xe9, 0x32, 0xff, 0xff, 0xff, //0x00001b6e jmp          LBB0_125
	//0x00001b73 LBB0_142
	0xc1, 0xea, 0x06, //0x00001b73 shrl         $6, %edx
	0x80, 0xca, 0xc0, //0x00001b76 orb          $-64, %dl
	0x88, 0x95, 0x40, 0xff, 0xff, 0xff, //0x00001b79 movb         %dl, $-192(%rbp)
	0x80, 0xe3, 0x3f, //0x00001b7f andb         $63, %bl
	0x80, 0xcb, 0x80, //0x00001b82 orb          $-128, %bl
	0x88, 0x9d, 0x41, 0xff, 0xff, 0xff, //0x00001b85 movb         %bl, $-191(%rbp)
	0xbf, 0x02, 0x00, 0x00, 0x00, //0x00001b8b movl         $2, %edi
	0x89, 0xd3, //0x00001b90 movl         %edx, %ebx
	0xe9, 0x0e, 0xff, 0xff, 0xff, //0x00001b92 jmp          LBB0_125
	//0x00001b97 LBB0_143
	0x48, 0x83, 0xff, 0x06, //0x00001b97 cmpq         $6, %rdi
	0x0f, 0x8c, 0x82, 0x3b, 0x00, 0x00, //0x00001b9b jl           LBB0_977
	0x81, 0xfa, 0xff, 0xdb, 0x00, 0x00, //0x00001ba1 cmpl         $56319, %edx
	0x0f, 0x87, 0x76, 0x3b, 0x00, 0x00, //0x00001ba7 ja           LBB0_977
	0x41, 0x80, 0x38, 0x5c, //0x00001bad cmpb         $92, (%r8)
	0x0f, 0x85, 0x6c, 0x3b, 0x00, 0x00, //0x00001bb1 jne          LBB0_977
	0x41, 0x80, 0x7c, 0x24, 0x07, 0x75, //0x00001bb7 cmpb         $117, $7(%r12)
	0x0f, 0x85, 0x60, 0x3b, 0x00, 0x00, //0x00001bbd jne          LBB0_977
	0x4c, 0x89, 0xe0, //0x00001bc3 movq         %r12, %rax
	0x4d, 0x8d, 0x44, 0x24, 0x08, //0x00001bc6 leaq         $8(%r12), %r8
	0x41, 0x8b, 0x7c, 0x24, 0x08, //0x00001bcb movl         $8(%r12), %edi
	0x89, 0xfb, //0x00001bd0 movl         %edi, %ebx
	0xf7, 0xd3, //0x00001bd2 notl         %ebx
	0x8d, 0x87, 0xd0, 0xcf, 0xcf, 0xcf, //0x00001bd4 leal         $-808464432(%rdi), %eax
	0x81, 0xe3, 0x80, 0x80, 0x80, 0x80, //0x00001bda andl         $-2139062144, %ebx
	0x85, 0xc3, //0x00001be0 testl        %eax, %ebx
	0x0f, 0x85, 0x47, 0x3b, 0x00, 0x00, //0x00001be2 jne          LBB0_978
	0x8d, 0x87, 0x19, 0x19, 0x19, 0x19, //0x00001be8 leal         $421075225(%rdi), %eax
	0x09, 0xf8, //0x00001bee orl          %edi, %eax
	0xa9, 0x80, 0x80, 0x80, 0x80, //0x00001bf0 testl        $-2139062144, %eax
	0x0f, 0x85, 0x34, 0x3b, 0x00, 0x00, //0x00001bf5 jne          LBB0_978
	0x89, 0xf8, //0x00001bfb movl         %edi, %eax
	0x25, 0x7f, 0x7f, 0x7f, 0x7f, //0x00001bfd andl         $2139062143, %eax
	0xb9, 0xc0, 0xc0, 0xc0, 0xc0, //0x00001c02 movl         $-1061109568, %ecx
	0x29, 0xc1, //0x00001c07 subl         %eax, %ecx
	0x8d, 0xb0, 0x46, 0x46, 0x46, 0x46, //0x00001c09 leal         $1179010630(%rax), %esi
	0x21, 0xd9, //0x00001c0f andl         %ebx, %ecx
	0x85, 0xf1, //0x00001c11 testl        %esi, %ecx
	0x0f, 0x85, 0x16, 0x3b, 0x00, 0x00, //0x00001c13 jne          LBB0_978
	0xb9, 0xe0, 0xe0, 0xe0, 0xe0, //0x00001c19 movl         $-522133280, %ecx
	0x29, 0xc1, //0x00001c1e subl         %eax, %ecx
	0x05, 0x39, 0x39, 0x39, 0x39, //0x00001c20 addl         $960051513, %eax
	0x21, 0xcb, //0x00001c25 andl         %ecx, %ebx
	0x85, 0xc3, //0x00001c27 testl        %eax, %ebx
	0x0f, 0x85, 0x00, 0x3b, 0x00, 0x00, //0x00001c29 jne          LBB0_978
	0x0f, 0xcf, //0x00001c2f bswapl       %edi
	0x89, 0xf8, //0x00001c31 movl         %edi, %eax
	0xc1, 0xe8, 0x04, //0x00001c33 shrl         $4, %eax
	0xf7, 0xd0, //0x00001c36 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00001c38 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00001c3d leal         (%rax,%rax,8), %eax
	0x81, 0xe7, 0x0f, 0x0f, 0x0f, 0x0f, //0x00001c40 andl         $252645135, %edi
	0x01, 0xc7, //0x00001c46 addl         %eax, %edi
	0x89, 0xf8, //0x00001c48 movl         %edi, %eax
	0xc1, 0xe8, 0x04, //0x00001c4a shrl         $4, %eax
	0x09, 0xf8, //0x00001c4d orl          %edi, %eax
	0x89, 0xc1, //0x00001c4f movl         %eax, %ecx
	0x81, 0xe1, 0x00, 0x00, 0xfc, 0x00, //0x00001c51 andl         $16515072, %ecx
	0x81, 0xf9, 0x00, 0x00, 0xdc, 0x00, //0x00001c57 cmpl         $14417920, %ecx
	0x0f, 0x85, 0xc0, 0x3a, 0x00, 0x00, //0x00001c5d jne          LBB0_977
	0x89, 0xc1, //0x00001c63 movl         %eax, %ecx
	0xc1, 0xe9, 0x08, //0x00001c65 shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x00001c68 andl         $65280, %ecx
	0x0f, 0xb6, 0xc0, //0x00001c6e movzbl       %al, %eax
	0x09, 0xc8, //0x00001c71 orl          %ecx, %eax
	0xc1, 0xe2, 0x0a, //0x00001c73 shll         $10, %edx
	0x8d, 0x84, 0x02, 0x00, 0x24, 0xa0, 0xfc, //0x00001c76 leal         $-56613888(%rdx,%rax), %eax
	0x89, 0xc3, //0x00001c7d movl         %eax, %ebx
	0xc1, 0xeb, 0x12, //0x00001c7f shrl         $18, %ebx
	0x80, 0xcb, 0xf0, //0x00001c82 orb          $-16, %bl
	0x88, 0x9d, 0x40, 0xff, 0xff, 0xff, //0x00001c85 movb         %bl, $-192(%rbp)
	0x89, 0xc1, //0x00001c8b movl         %eax, %ecx
	0xc1, 0xe9, 0x0c, //0x00001c8d shrl         $12, %ecx
	0x80, 0xe1, 0x3f, //0x00001c90 andb         $63, %cl
	0x80, 0xc9, 0x80, //0x00001c93 orb          $-128, %cl
	0x88, 0x8d, 0x41, 0xff, 0xff, 0xff, //0x00001c96 movb         %cl, $-191(%rbp)
	0x89, 0xc1, //0x00001c9c movl         %eax, %ecx
	0xc1, 0xe9, 0x06, //0x00001c9e shrl         $6, %ecx
	0x80, 0xe1, 0x3f, //0x00001ca1 andb         $63, %cl
	0x80, 0xc9, 0x80, //0x00001ca4 orb          $-128, %cl
	0x88, 0x8d, 0x42, 0xff, 0xff, 0xff, //0x00001ca7 movb         %cl, $-190(%rbp)
	0x24, 0x3f, //0x00001cad andb         $63, %al
	0x0c, 0x80, //0x00001caf orb          $-128, %al
	0x88, 0x85, 0x43, 0xff, 0xff, 0xff, //0x00001cb1 movb         %al, $-189(%rbp)
	0x49, 0x83, 0xc4, 0x0c, //0x00001cb7 addq         $12, %r12
	0xbf, 0x04, 0x00, 0x00, 0x00, //0x00001cbb movl         $4, %edi
	0x4c, 0x8b, 0x5d, 0xd0, //0x00001cc0 movq         $-48(%rbp), %r11
	0x4c, 0x8b, 0x6d, 0xc0, //0x00001cc4 movq         $-64(%rbp), %r13
	0xe9, 0xdb, 0xfd, 0xff, 0xff, //0x00001cc8 jmp          LBB0_126
	//0x00001ccd LBB0_260
	0x4d, 0x85, 0xd2, //0x00001ccd testq        %r10, %r10
	0x0f, 0x85, 0x6d, 0x02, 0x00, 0x00, //0x00001cd0 jne          LBB0_341
	0x4a, 0x8d, 0x4c, 0x08, 0x01, //0x00001cd6 leaq         $1(%rax,%r9), %rcx
	0x48, 0xf7, 0xd0, //0x00001cdb notq         %rax
	0x4c, 0x01, 0xc0, //0x00001cde addq         %r8, %rax
	//0x00001ce1 LBB0_262
	0x49, 0xba, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001ce1 movabsq      $4294977024, %r10
	//0x00001ceb LBB0_263
	0x48, 0x85, 0xc0, //0x00001ceb testq        %rax, %rax
	0x4c, 0x8b, 0x45, 0xb0, //0x00001cee movq         $-80(%rbp), %r8
	0x4d, 0x89, 0xf9, //0x00001cf2 movq         %r15, %r9
	0x0f, 0x8f, 0x1d, 0x00, 0x00, 0x00, //0x00001cf5 jg           LBB0_320
	0xe9, 0x40, 0xf0, 0xff, 0xff, //0x00001cfb jmp          LBB0_224
	//0x00001d00 LBB0_319
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x00001d00 movq         $-2, %rdx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x00001d07 movl         $2, %esi
	0x48, 0x01, 0xf1, //0x00001d0c addq         %rsi, %rcx
	0x48, 0x01, 0xd0, //0x00001d0f addq         %rdx, %rax
	0x0f, 0x8e, 0x28, 0xf0, 0xff, 0xff, //0x00001d12 jle          LBB0_224
	//0x00001d18 LBB0_320
	0x0f, 0xb6, 0x11, //0x00001d18 movzbl       (%rcx), %edx
	0x80, 0xfa, 0x5c, //0x00001d1b cmpb         $92, %dl
	0x0f, 0x84, 0xdc, 0xff, 0xff, 0xff, //0x00001d1e je           LBB0_319
	0x80, 0xfa, 0x22, //0x00001d24 cmpb         $34, %dl
	0x0f, 0x84, 0x1d, 0x00, 0x00, 0x00, //0x00001d27 je           LBB0_323
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00001d2d movq         $-1, %rdx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00001d34 movl         $1, %esi
	0x48, 0x01, 0xf1, //0x00001d39 addq         %rsi, %rcx
	0x48, 0x01, 0xd0, //0x00001d3c addq         %rdx, %rax
	0x0f, 0x8f, 0xd3, 0xff, 0xff, 0xff, //0x00001d3f jg           LBB0_320
	0xe9, 0xf6, 0xef, 0xff, 0xff, //0x00001d45 jmp          LBB0_224
	//0x00001d4a LBB0_323
	0x48, 0x29, 0xf9, //0x00001d4a subq         %rdi, %rcx
	0x48, 0xff, 0xc1, //0x00001d4d incq         %rcx
	0xe9, 0x81, 0xf2, 0xff, 0xff, //0x00001d50 jmp          LBB0_265
	//0x00001d55 LBB0_153
	0x4c, 0x8b, 0x5d, 0xd0, //0x00001d55 movq         $-48(%rbp), %r11
	//0x00001d59 LBB0_154
	0x48, 0x31, 0xda, //0x00001d59 xorq         %rbx, %rdx
	0x4d, 0x31, 0xd7, //0x00001d5c xorq         %r10, %r15
	0x31, 0xc9, //0x00001d5f xorl         %ecx, %ecx
	0x49, 0x09, 0xd7, //0x00001d61 orq          %rdx, %r15
	0x0f, 0x94, 0xc1, //0x00001d64 sete         %cl
	//0x00001d67 LBB0_155
	0x49, 0xba, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001d67 movabsq      $4294977024, %r10
	0x4c, 0x8b, 0x4d, 0xa0, //0x00001d71 movq         $-96(%rbp), %r9
	0x49, 0x8b, 0x10, //0x00001d75 movq         (%r8), %rdx
	0x4c, 0x89, 0xf6, //0x00001d78 movq         %r14, %rsi
	0x48, 0x29, 0xd6, //0x00001d7b subq         %rdx, %rsi
	0x0f, 0x82, 0xab, 0xeb, 0xff, 0xff, //0x00001d7e jb           LBB0_101
	0xe9, 0xf7, 0xeb, 0xff, 0xff, //0x00001d84 jmp          LBB0_156
	//0x00001d89 LBB0_324
	0x31, 0xc9, //0x00001d89 xorl         %ecx, %ecx
	0xe9, 0xd7, 0xff, 0xff, 0xff, //0x00001d8b jmp          LBB0_155
	//0x00001d90 LBB0_325
	0x4a, 0x8d, 0x0c, 0x37, //0x00001d90 leaq         (%rdi,%r14), %rcx
	0xe9, 0x48, 0xff, 0xff, 0xff, //0x00001d94 jmp          LBB0_262
	//0x00001d99 LBB0_326
	0x4c, 0x89, 0xf2, //0x00001d99 movq         %r14, %rdx
	0x48, 0x2b, 0x55, 0xc8, //0x00001d9c subq         $-56(%rbp), %rdx
	0x4c, 0x0f, 0xbc, 0xdf, //0x00001da0 bsfq         %rdi, %r11
	0x49, 0x01, 0xd3, //0x00001da4 addq         %rdx, %r11
	0x4c, 0x09, 0xc6, //0x00001da7 orq          %r8, %rsi
	0x48, 0x89, 0xfa, //0x00001daa movq         %rdi, %rdx
	0x48, 0x09, 0xda, //0x00001dad orq          %rbx, %rdx
	0x0f, 0x84, 0x0f, 0xfb, 0xff, 0xff, //0x00001db0 je           LBB0_72
	//0x00001db6 LBB0_327
	0x4d, 0x89, 0xf7, //0x00001db6 movq         %r14, %r15
	0x4d, 0x89, 0xce, //0x00001db9 movq         %r9, %r14
	0x41, 0x89, 0xd9, //0x00001dbc movl         %ebx, %r9d
	0x41, 0xf7, 0xd1, //0x00001dbf notl         %r9d
	0x41, 0x21, 0xf9, //0x00001dc2 andl         %edi, %r9d
	0x47, 0x8d, 0x04, 0x09, //0x00001dc5 leal         (%r9,%r9), %r8d
	0x41, 0x09, 0xd8, //0x00001dc9 orl          %ebx, %r8d
	0x44, 0x89, 0xc2, //0x00001dcc movl         %r8d, %edx
	0xf7, 0xd2, //0x00001dcf notl         %edx
	0x21, 0xfa, //0x00001dd1 andl         %edi, %edx
	0x81, 0xe2, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001dd3 andl         $-1431655766, %edx
	0x31, 0xdb, //0x00001dd9 xorl         %ebx, %ebx
	0x44, 0x01, 0xca, //0x00001ddb addl         %r9d, %edx
	0x4d, 0x89, 0xf1, //0x00001dde movq         %r14, %r9
	0x4d, 0x89, 0xfe, //0x00001de1 movq         %r15, %r14
	0x0f, 0x92, 0xc3, //0x00001de4 setb         %bl
	0x01, 0xd2, //0x00001de7 addl         %edx, %edx
	0x81, 0xf2, 0x55, 0x55, 0x55, 0x55, //0x00001de9 xorl         $1431655765, %edx
	0x44, 0x21, 0xc2, //0x00001def andl         %r8d, %edx
	0xf7, 0xd2, //0x00001df2 notl         %edx
	0x21, 0xd6, //0x00001df4 andl         %edx, %esi
	0x4c, 0x8b, 0x45, 0xb0, //0x00001df6 movq         $-80(%rbp), %r8
	0x48, 0x8b, 0x7d, 0xc8, //0x00001dfa movq         $-56(%rbp), %rdi
	0x48, 0x85, 0xf6, //0x00001dfe testq        %rsi, %rsi
	0x0f, 0x85, 0xcf, 0xfa, 0xff, 0xff, //0x00001e01 jne          LBB0_73
	//0x00001e07 LBB0_328
	0x49, 0x83, 0xc6, 0x20, //0x00001e07 addq         $32, %r14
	0x48, 0x83, 0xc1, 0xe0, //0x00001e0b addq         $-32, %rcx
	//0x00001e0f LBB0_329
	0x48, 0x85, 0xdb, //0x00001e0f testq        %rbx, %rbx
	0x4c, 0x8b, 0x7d, 0x90, //0x00001e12 movq         $-112(%rbp), %r15
	0x0f, 0x85, 0xaf, 0x00, 0x00, 0x00, //0x00001e16 jne          LBB0_338
	0x4d, 0x89, 0xd8, //0x00001e1c movq         %r11, %r8
	0x48, 0x85, 0xc9, //0x00001e1f testq        %rcx, %rcx
	0x0f, 0x84, 0xcc, 0x36, 0x00, 0x00, //0x00001e22 je           LBB0_951
	//0x00001e28 LBB0_331
	0x48, 0xf7, 0xd7, //0x00001e28 notq         %rdi
	//0x00001e2b LBB0_332
	0x4c, 0x89, 0xf6, //0x00001e2b movq         %r14, %rsi
	0x49, 0xff, 0xc6, //0x00001e2e incq         %r14
	0x0f, 0xb6, 0x1e, //0x00001e31 movzbl       (%rsi), %ebx
	0x80, 0xfb, 0x22, //0x00001e34 cmpb         $34, %bl
	0x0f, 0x84, 0x71, 0x00, 0x00, 0x00, //0x00001e37 je           LBB0_337
	0x48, 0x89, 0xf2, //0x00001e3d movq         %rsi, %rdx
	0x48, 0x8d, 0x71, 0xff, //0x00001e40 leaq         $-1(%rcx), %rsi
	0x80, 0xfb, 0x5c, //0x00001e44 cmpb         $92, %bl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00001e47 je           LBB0_335
	0x48, 0x89, 0xf1, //0x00001e4d movq         %rsi, %rcx
	0x48, 0x85, 0xf6, //0x00001e50 testq        %rsi, %rsi
	0x4c, 0x8b, 0x7d, 0x90, //0x00001e53 movq         $-112(%rbp), %r15
	0x0f, 0x85, 0xce, 0xff, 0xff, 0xff, //0x00001e57 jne          LBB0_332
	0xe9, 0x92, 0x36, 0x00, 0x00, //0x00001e5d jmp          LBB0_951
	//0x00001e62 LBB0_335
	0x48, 0x85, 0xf6, //0x00001e62 testq        %rsi, %rsi
	0x0f, 0x84, 0x89, 0x36, 0x00, 0x00, //0x00001e65 je           LBB0_951
	0x4d, 0x89, 0xcd, //0x00001e6b movq         %r9, %r13
	0x49, 0x01, 0xfe, //0x00001e6e addq         %rdi, %r14
	0x49, 0x83, 0xf8, 0xff, //0x00001e71 cmpq         $-1, %r8
	0x4d, 0x0f, 0x44, 0xde, //0x00001e75 cmoveq       %r14, %r11
	0x4d, 0x0f, 0x44, 0xc6, //0x00001e79 cmoveq       %r14, %r8
	0x49, 0x89, 0xd6, //0x00001e7d movq         %rdx, %r14
	0x49, 0x83, 0xc6, 0x02, //0x00001e80 addq         $2, %r14
	0x48, 0x83, 0xc1, 0xfe, //0x00001e84 addq         $-2, %rcx
	0x48, 0x89, 0xce, //0x00001e88 movq         %rcx, %rsi
	0x48, 0x8b, 0x55, 0xc0, //0x00001e8b movq         $-64(%rbp), %rdx
	0x49, 0x89, 0xd5, //0x00001e8f movq         %rdx, %r13
	0x49, 0xba, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001e92 movabsq      $4294977024, %r10
	0x48, 0x85, 0xf6, //0x00001e9c testq        %rsi, %rsi
	0x4c, 0x8b, 0x7d, 0x90, //0x00001e9f movq         $-112(%rbp), %r15
	0x0f, 0x85, 0x82, 0xff, 0xff, 0xff, //0x00001ea3 jne          LBB0_332
	0xe9, 0x46, 0x36, 0x00, 0x00, //0x00001ea9 jmp          LBB0_951
	//0x00001eae LBB0_337
	0x48, 0x8b, 0x7d, 0xc8, //0x00001eae movq         $-56(%rbp), %rdi
	0x49, 0x29, 0xfe, //0x00001eb2 subq         %rdi, %r14
	0x4c, 0x8b, 0x45, 0xb0, //0x00001eb5 movq         $-80(%rbp), %r8
	0x4d, 0x85, 0xf6, //0x00001eb9 testq        %r14, %r14
	0x48, 0x8b, 0x5d, 0xb8, //0x00001ebc movq         $-72(%rbp), %rbx
	0x0f, 0x89, 0xb1, 0xe8, 0xff, 0xff, //0x00001ec0 jns          LBB0_79
	0xe9, 0x29, 0x36, 0x00, 0x00, //0x00001ec6 jmp          LBB0_951
	//0x00001ecb LBB0_338
	0x48, 0x85, 0xc9, //0x00001ecb testq        %rcx, %rcx
	0x0f, 0x84, 0x20, 0x36, 0x00, 0x00, //0x00001ece je           LBB0_951
	0x4d, 0x89, 0xcd, //0x00001ed4 movq         %r9, %r13
	0x48, 0x8b, 0x7d, 0xc8, //0x00001ed7 movq         $-56(%rbp), %rdi
	0x49, 0x89, 0xf8, //0x00001edb movq         %rdi, %r8
	0x49, 0xf7, 0xd0, //0x00001ede notq         %r8
	0x4d, 0x01, 0xf0, //0x00001ee1 addq         %r14, %r8
	0x49, 0x83, 0xfb, 0xff, //0x00001ee4 cmpq         $-1, %r11
	0x4c, 0x89, 0xda, //0x00001ee8 movq         %r11, %rdx
	0x49, 0x0f, 0x44, 0xd0, //0x00001eeb cmoveq       %r8, %rdx
	0x4d, 0x0f, 0x45, 0xc3, //0x00001eef cmovneq      %r11, %r8
	0x49, 0xff, 0xc6, //0x00001ef3 incq         %r14
	0x48, 0xff, 0xc9, //0x00001ef6 decq         %rcx
	0x49, 0x89, 0xd3, //0x00001ef9 movq         %rdx, %r11
	0x48, 0x8b, 0x55, 0xc0, //0x00001efc movq         $-64(%rbp), %rdx
	0x49, 0x89, 0xd5, //0x00001f00 movq         %rdx, %r13
	0x49, 0xba, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001f03 movabsq      $4294977024, %r10
	0x4c, 0x8b, 0x7d, 0x90, //0x00001f0d movq         $-112(%rbp), %r15
	0x48, 0x85, 0xc9, //0x00001f11 testq        %rcx, %rcx
	0x0f, 0x85, 0x0e, 0xff, 0xff, 0xff, //0x00001f14 jne          LBB0_331
	0xe9, 0xd5, 0x35, 0x00, 0x00, //0x00001f1a jmp          LBB0_951
	//0x00001f1f LBB0_340
	0x4c, 0x8b, 0x45, 0xb0, //0x00001f1f movq         $-80(%rbp), %r8
	0x4d, 0x8b, 0x30, //0x00001f23 movq         (%r8), %r14
	0x4c, 0x8b, 0x5d, 0xd0, //0x00001f26 movq         $-48(%rbp), %r11
	0x4d, 0x89, 0x33, //0x00001f2a movq         %r14, (%r11)
	0x4c, 0x8b, 0x6d, 0xc0, //0x00001f2d movq         $-64(%rbp), %r13
	0x49, 0x89, 0xd1, //0x00001f31 movq         %rdx, %r9
	0x49, 0xba, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001f34 movabsq      $4294977024, %r10
	0xe9, 0xfd, 0xed, 0xff, 0xff, //0x00001f3e jmp          LBB0_224
	//0x00001f43 LBB0_341
	0x4d, 0x89, 0xfd, //0x00001f43 movq         %r15, %r13
	0x49, 0x8d, 0x48, 0xff, //0x00001f46 leaq         $-1(%r8), %rcx
	0x48, 0x39, 0xc1, //0x00001f4a cmpq         %rax, %rcx
	0x0f, 0x85, 0x21, 0x00, 0x00, 0x00, //0x00001f4d jne          LBB0_343
	0x4c, 0x8b, 0x5d, 0xd0, //0x00001f53 movq         $-48(%rbp), %r11
	0x48, 0x8b, 0x45, 0xc0, //0x00001f57 movq         $-64(%rbp), %rax
	0x4d, 0x89, 0xe9, //0x00001f5b movq         %r13, %r9
	0x49, 0x89, 0xc5, //0x00001f5e movq         %rax, %r13
	0x4c, 0x8b, 0x45, 0xb0, //0x00001f61 movq         $-80(%rbp), %r8
	0x49, 0xba, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001f65 movabsq      $4294977024, %r10
	0xe9, 0xcc, 0xed, 0xff, 0xff, //0x00001f6f jmp          LBB0_224
	//0x00001f74 LBB0_343
	0x4a, 0x8d, 0x4c, 0x08, 0x02, //0x00001f74 leaq         $2(%rax,%r9), %rcx
	0x49, 0x29, 0xc0, //0x00001f79 subq         %rax, %r8
	0x49, 0x83, 0xc0, 0xfe, //0x00001f7c addq         $-2, %r8
	0x4c, 0x89, 0xc0, //0x00001f80 movq         %r8, %rax
	0x4c, 0x8b, 0x5d, 0xd0, //0x00001f83 movq         $-48(%rbp), %r11
	0x48, 0x8b, 0x55, 0xc0, //0x00001f87 movq         $-64(%rbp), %rdx
	0x4d, 0x89, 0xef, //0x00001f8b movq         %r13, %r15
	0x49, 0x89, 0xd5, //0x00001f8e movq         %rdx, %r13
	0x49, 0xba, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001f91 movabsq      $4294977024, %r10
	0x48, 0x8b, 0x7d, 0xc8, //0x00001f9b movq         $-56(%rbp), %rdi
	0xe9, 0x47, 0xfd, 0xff, 0xff, //0x00001f9f jmp          LBB0_263
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001fa4 .p2align 4, 0x90
	//0x00001fb0 LBB0_351
	0x49, 0x8d, 0x56, 0x01, //0x00001fb0 leaq         $1(%r14), %rdx
	0x48, 0x39, 0xc2, //0x00001fb4 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00001fb7 jae          LBB0_355
	0x8a, 0x1c, 0x17, //0x00001fbd movb         (%rdi,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x00001fc0 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00001fc3 je           LBB0_355
	0x80, 0xfb, 0x20, //0x00001fc9 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00001fcc je           LBB0_355
	0x80, 0xc3, 0xf7, //0x00001fd2 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00001fd5 cmpb         $1, %bl
	0x0f, 0x87, 0x03, 0x01, 0x00, 0x00, //0x00001fd8 ja           LBB0_373
	0x90, 0x90, //0x00001fde .p2align 4, 0x90
	//0x00001fe0 LBB0_355
	0x49, 0x8d, 0x56, 0x02, //0x00001fe0 leaq         $2(%r14), %rdx
	0x48, 0x39, 0xc2, //0x00001fe4 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00001fe7 jae          LBB0_359
	0x8a, 0x1c, 0x17, //0x00001fed movb         (%rdi,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x00001ff0 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00001ff3 je           LBB0_359
	0x80, 0xfb, 0x20, //0x00001ff9 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00001ffc je           LBB0_359
	0x80, 0xc3, 0xf7, //0x00002002 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00002005 cmpb         $1, %bl
	0x0f, 0x87, 0xd3, 0x00, 0x00, 0x00, //0x00002008 ja           LBB0_373
	0x90, 0x90, //0x0000200e .p2align 4, 0x90
	//0x00002010 LBB0_359
	0x49, 0x8d, 0x56, 0x03, //0x00002010 leaq         $3(%r14), %rdx
	0x48, 0x39, 0xc2, //0x00002014 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00002017 jae          LBB0_363
	0x8a, 0x1c, 0x17, //0x0000201d movb         (%rdi,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x00002020 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00002023 je           LBB0_363
	0x80, 0xfb, 0x20, //0x00002029 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x0000202c je           LBB0_363
	0x80, 0xc3, 0xf7, //0x00002032 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00002035 cmpb         $1, %bl
	0x0f, 0x87, 0xa3, 0x00, 0x00, 0x00, //0x00002038 ja           LBB0_373
	0x90, 0x90, //0x0000203e .p2align 4, 0x90
	//0x00002040 LBB0_363
	0x49, 0x8d, 0x76, 0x04, //0x00002040 leaq         $4(%r14), %rsi
	0x48, 0x39, 0xf0, //0x00002044 cmpq         %rsi, %rax
	0x0f, 0x86, 0x5d, 0x00, 0x00, 0x00, //0x00002047 jbe          LBB0_370
	0x48, 0x39, 0xf0, //0x0000204d cmpq         %rsi, %rax
	0x0f, 0x84, 0x5f, 0x00, 0x00, 0x00, //0x00002050 je           LBB0_371
	0x48, 0x8d, 0x34, 0x07, //0x00002056 leaq         (%rdi,%rax), %rsi
	0x48, 0x83, 0xc1, 0x04, //0x0000205a addq         $4, %rcx
	0x48, 0x89, 0xfb, //0x0000205e movq         %rdi, %rbx
	0x4a, 0x8d, 0x54, 0x37, 0x05, //0x00002061 leaq         $5(%rdi,%r14), %rdx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002066 .p2align 4, 0x90
	//0x00002070 LBB0_366
	0x0f, 0xbe, 0x7a, 0xff, //0x00002070 movsbl       $-1(%rdx), %edi
	0x83, 0xff, 0x20, //0x00002074 cmpl         $32, %edi
	0x0f, 0x87, 0x4f, 0x00, 0x00, 0x00, //0x00002077 ja           LBB0_372
	0x49, 0x0f, 0xa3, 0xfa, //0x0000207d btq          %rdi, %r10
	0x0f, 0x83, 0x45, 0x00, 0x00, 0x00, //0x00002081 jae          LBB0_372
	0x48, 0xff, 0xc2, //0x00002087 incq         %rdx
	0x48, 0xff, 0xc1, //0x0000208a incq         %rcx
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x0000208d jne          LBB0_366
	0x48, 0x89, 0xdf, //0x00002093 movq         %rbx, %rdi
	0x48, 0x29, 0xfe, //0x00002096 subq         %rdi, %rsi
	0x48, 0x89, 0xf2, //0x00002099 movq         %rsi, %rdx
	0x48, 0x39, 0xc2, //0x0000209c cmpq         %rax, %rdx
	0x0f, 0x82, 0x3c, 0x00, 0x00, 0x00, //0x0000209f jb           LBB0_373
	0xe9, 0x48, 0x00, 0x00, 0x00, //0x000020a5 jmp          LBB0_374
	//0x000020aa LBB0_370
	0x49, 0x89, 0x33, //0x000020aa movq         %rsi, (%r11)
	0x49, 0x89, 0xf6, //0x000020ad movq         %rsi, %r14
	0xe9, 0x3d, 0x00, 0x00, 0x00, //0x000020b0 jmp          LBB0_374
	//0x000020b5 LBB0_371
	0x48, 0x01, 0xfe, //0x000020b5 addq         %rdi, %rsi
	0x48, 0x29, 0xfe, //0x000020b8 subq         %rdi, %rsi
	0x48, 0x89, 0xf2, //0x000020bb movq         %rsi, %rdx
	0x48, 0x39, 0xc2, //0x000020be cmpq         %rax, %rdx
	0x0f, 0x82, 0x1a, 0x00, 0x00, 0x00, //0x000020c1 jb           LBB0_373
	0xe9, 0x26, 0x00, 0x00, 0x00, //0x000020c7 jmp          LBB0_374
	//0x000020cc LBB0_372
	0x48, 0x89, 0xdf, //0x000020cc movq         %rbx, %rdi
	0x48, 0x89, 0xd9, //0x000020cf movq         %rbx, %rcx
	0x48, 0xf7, 0xd1, //0x000020d2 notq         %rcx
	0x48, 0x01, 0xca, //0x000020d5 addq         %rcx, %rdx
	0x48, 0x39, 0xc2, //0x000020d8 cmpq         %rax, %rdx
	0x0f, 0x83, 0x11, 0x00, 0x00, 0x00, //0x000020db jae          LBB0_374
	//0x000020e1 LBB0_373
	0x4c, 0x8d, 0x72, 0x01, //0x000020e1 leaq         $1(%rdx), %r14
	0x4d, 0x89, 0x33, //0x000020e5 movq         %r14, (%r11)
	0x80, 0x3c, 0x17, 0x5d, //0x000020e8 cmpb         $93, (%rdi,%rdx)
	0x0f, 0x84, 0x38, 0x11, 0x00, 0x00, //0x000020ec je           LBB0_554
	//0x000020f2 LBB0_374
	0x49, 0xff, 0xce, //0x000020f2 decq         %r14
	0x4d, 0x89, 0x33, //0x000020f5 movq         %r14, (%r11)
	0x4d, 0x85, 0xe4, //0x000020f8 testq        %r12, %r12
	0x0f, 0x8e, 0x1f, 0x0f, 0x00, 0x00, //0x000020fb jle          LBB0_447
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002101 .p2align 4, 0x90
	//0x00002110 LBB0_375
	0x49, 0x8b, 0x00, //0x00002110 movq         (%r8), %rax
	0x4c, 0x89, 0xf2, //0x00002113 movq         %r14, %rdx
	0x48, 0x29, 0xc2, //0x00002116 subq         %rax, %rdx
	0x0f, 0x83, 0x31, 0x00, 0x00, 0x00, //0x00002119 jae          LBB0_380
	0x42, 0x8a, 0x0c, 0x37, //0x0000211f movb         (%rdi,%r14), %cl
	0x80, 0xf9, 0x0d, //0x00002123 cmpb         $13, %cl
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x00002126 je           LBB0_380
	0x80, 0xf9, 0x20, //0x0000212c cmpb         $32, %cl
	0x0f, 0x84, 0x1b, 0x00, 0x00, 0x00, //0x0000212f je           LBB0_380
	0x80, 0xc1, 0xf7, //0x00002135 addb         $-9, %cl
	0x80, 0xf9, 0x01, //0x00002138 cmpb         $1, %cl
	0x0f, 0x86, 0x0f, 0x00, 0x00, 0x00, //0x0000213b jbe          LBB0_380
	0x4c, 0x89, 0xf1, //0x00002141 movq         %r14, %rcx
	0xe9, 0x47, 0x01, 0x00, 0x00, //0x00002144 jmp          LBB0_402
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002149 .p2align 4, 0x90
	//0x00002150 LBB0_380
	0x49, 0x8d, 0x4e, 0x01, //0x00002150 leaq         $1(%r14), %rcx
	0x48, 0x39, 0xc1, //0x00002154 cmpq         %rax, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00002157 jae          LBB0_384
	0x8a, 0x1c, 0x0f, //0x0000215d movb         (%rdi,%rcx), %bl
	0x80, 0xfb, 0x0d, //0x00002160 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00002163 je           LBB0_384
	0x80, 0xfb, 0x20, //0x00002169 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x0000216c je           LBB0_384
	0x80, 0xc3, 0xf7, //0x00002172 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00002175 cmpb         $1, %bl
	0x0f, 0x87, 0x12, 0x01, 0x00, 0x00, //0x00002178 ja           LBB0_402
	0x90, 0x90, //0x0000217e .p2align 4, 0x90
	//0x00002180 LBB0_384
	0x49, 0x8d, 0x4e, 0x02, //0x00002180 leaq         $2(%r14), %rcx
	0x48, 0x39, 0xc1, //0x00002184 cmpq         %rax, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00002187 jae          LBB0_388
	0x8a, 0x1c, 0x0f, //0x0000218d movb         (%rdi,%rcx), %bl
	0x80, 0xfb, 0x0d, //0x00002190 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00002193 je           LBB0_388
	0x80, 0xfb, 0x20, //0x00002199 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x0000219c je           LBB0_388
	0x80, 0xc3, 0xf7, //0x000021a2 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x000021a5 cmpb         $1, %bl
	0x0f, 0x87, 0xe2, 0x00, 0x00, 0x00, //0x000021a8 ja           LBB0_402
	0x90, 0x90, //0x000021ae .p2align 4, 0x90
	//0x000021b0 LBB0_388
	0x49, 0x8d, 0x4e, 0x03, //0x000021b0 leaq         $3(%r14), %rcx
	0x48, 0x39, 0xc1, //0x000021b4 cmpq         %rax, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000021b7 jae          LBB0_392
	0x8a, 0x1c, 0x0f, //0x000021bd movb         (%rdi,%rcx), %bl
	0x80, 0xfb, 0x0d, //0x000021c0 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x000021c3 je           LBB0_392
	0x80, 0xfb, 0x20, //0x000021c9 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x000021cc je           LBB0_392
	0x80, 0xc3, 0xf7, //0x000021d2 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x000021d5 cmpb         $1, %bl
	0x0f, 0x87, 0xb2, 0x00, 0x00, 0x00, //0x000021d8 ja           LBB0_402
	0x90, 0x90, //0x000021de .p2align 4, 0x90
	//0x000021e0 LBB0_392
	0x49, 0x8d, 0x76, 0x04, //0x000021e0 leaq         $4(%r14), %rsi
	0x48, 0x39, 0xf0, //0x000021e4 cmpq         %rsi, %rax
	0x0f, 0x86, 0x63, 0x00, 0x00, 0x00, //0x000021e7 jbe          LBB0_399
	0x48, 0x39, 0xf0, //0x000021ed cmpq         %rsi, %rax
	0x0f, 0x84, 0x65, 0x00, 0x00, 0x00, //0x000021f0 je           LBB0_400
	0x48, 0x8d, 0x34, 0x07, //0x000021f6 leaq         (%rdi,%rax), %rsi
	0x48, 0x83, 0xc2, 0x04, //0x000021fa addq         $4, %rdx
	0x48, 0x89, 0xfb, //0x000021fe movq         %rdi, %rbx
	0x4a, 0x8d, 0x4c, 0x37, 0x05, //0x00002201 leaq         $5(%rdi,%r14), %rcx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002206 .p2align 4, 0x90
	//0x00002210 LBB0_395
	0x0f, 0xbe, 0x79, 0xff, //0x00002210 movsbl       $-1(%rcx), %edi
	0x83, 0xff, 0x20, //0x00002214 cmpl         $32, %edi
	0x0f, 0x87, 0x55, 0x00, 0x00, 0x00, //0x00002217 ja           LBB0_401
	0x49, 0x0f, 0xa3, 0xfa, //0x0000221d btq          %rdi, %r10
	0x0f, 0x83, 0x4b, 0x00, 0x00, 0x00, //0x00002221 jae          LBB0_401
	0x48, 0xff, 0xc1, //0x00002227 incq         %rcx
	0x48, 0xff, 0xc2, //0x0000222a incq         %rdx
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x0000222d jne          LBB0_395
	0x48, 0x89, 0xdf, //0x00002233 movq         %rbx, %rdi
	0x48, 0x29, 0xfe, //0x00002236 subq         %rdi, %rsi
	0x48, 0x89, 0xf1, //0x00002239 movq         %rsi, %rcx
	0x48, 0x39, 0xc1, //0x0000223c cmpq         %rax, %rcx
	0x0f, 0x82, 0x4b, 0x00, 0x00, 0x00, //0x0000223f jb           LBB0_402
	0xe9, 0x46, 0x01, 0x00, 0x00, //0x00002245 jmp          LBB0_419
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000224a .p2align 4, 0x90
	//0x00002250 LBB0_399
	0x49, 0x89, 0x33, //0x00002250 movq         %rsi, (%r11)
	0x49, 0x89, 0xf6, //0x00002253 movq         %rsi, %r14
	0xe9, 0x35, 0x01, 0x00, 0x00, //0x00002256 jmp          LBB0_419
	//0x0000225b LBB0_400
	0x48, 0x01, 0xfe, //0x0000225b addq         %rdi, %rsi
	0x48, 0x29, 0xfe, //0x0000225e subq         %rdi, %rsi
	0x48, 0x89, 0xf1, //0x00002261 movq         %rsi, %rcx
	0x48, 0x39, 0xc1, //0x00002264 cmpq         %rax, %rcx
	0x0f, 0x82, 0x23, 0x00, 0x00, 0x00, //0x00002267 jb           LBB0_402
	0xe9, 0x1e, 0x01, 0x00, 0x00, //0x0000226d jmp          LBB0_419
	//0x00002272 LBB0_401
	0x48, 0x89, 0xdf, //0x00002272 movq         %rbx, %rdi
	0x48, 0x89, 0xda, //0x00002275 movq         %rbx, %rdx
	0x48, 0xf7, 0xd2, //0x00002278 notq         %rdx
	0x48, 0x01, 0xd1, //0x0000227b addq         %rdx, %rcx
	0x48, 0x39, 0xc1, //0x0000227e cmpq         %rax, %rcx
	0x0f, 0x83, 0x09, 0x01, 0x00, 0x00, //0x00002281 jae          LBB0_419
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002287 .p2align 4, 0x90
	//0x00002290 LBB0_402
	0x4c, 0x8d, 0x71, 0x01, //0x00002290 leaq         $1(%rcx), %r14
	0x4d, 0x89, 0x33, //0x00002294 movq         %r14, (%r11)
	0x0f, 0xbe, 0x04, 0x0f, //0x00002297 movsbl       (%rdi,%rcx), %eax
	0x83, 0xf8, 0x7b, //0x0000229b cmpl         $123, %eax
	0x0f, 0x87, 0xc8, 0x03, 0x00, 0x00, //0x0000229e ja           LBB0_462
	0x48, 0x8d, 0x15, 0xcd, 0x34, 0x00, 0x00, //0x000022a4 leaq         $13517(%rip), %rdx  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x04, 0x82, //0x000022ab movslq       (%rdx,%rax,4), %rax
	0x48, 0x01, 0xd0, //0x000022af addq         %rdx, %rax
	0xff, 0xe0, //0x000022b2 jmpq         *%rax
	//0x000022b4 LBB0_404
	0x49, 0x8b, 0x10, //0x000022b4 movq         (%r8), %rdx
	0x48, 0x89, 0xd0, //0x000022b7 movq         %rdx, %rax
	0x4c, 0x29, 0xf0, //0x000022ba subq         %r14, %rax
	0x49, 0x01, 0xfe, //0x000022bd addq         %rdi, %r14
	0x48, 0x83, 0xf8, 0x10, //0x000022c0 cmpq         $16, %rax
	0x0f, 0x82, 0x66, 0x00, 0x00, 0x00, //0x000022c4 jb           LBB0_409
	0x48, 0x29, 0xca, //0x000022ca subq         %rcx, %rdx
	0x48, 0x83, 0xc2, 0xef, //0x000022cd addq         $-17, %rdx
	0x48, 0x89, 0xd6, //0x000022d1 movq         %rdx, %rsi
	0x48, 0x83, 0xe6, 0xf0, //0x000022d4 andq         $-16, %rsi
	0x48, 0x01, 0xce, //0x000022d8 addq         %rcx, %rsi
	0x48, 0x8d, 0x4c, 0x37, 0x11, //0x000022db leaq         $17(%rdi,%rsi), %rcx
	0x83, 0xe2, 0x0f, //0x000022e0 andl         $15, %edx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000022e3 .p2align 4, 0x90
	//0x000022f0 LBB0_406
	0xf3, 0x41, 0x0f, 0x6f, 0x16, //0x000022f0 movdqu       (%r14), %xmm2
	0x66, 0x0f, 0x6f, 0xda, //0x000022f5 movdqa       %xmm2, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdd, //0x000022f9 pcmpeqb      %xmm13, %xmm3
	0x66, 0x41, 0x0f, 0xeb, 0xd6, //0x000022fe por          %xmm14, %xmm2
	0x66, 0x0f, 0x74, 0xd4, //0x00002303 pcmpeqb      %xmm4, %xmm2
	0x66, 0x0f, 0xeb, 0xd3, //0x00002307 por          %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xf2, //0x0000230b pmovmskb     %xmm2, %esi
	0x66, 0x85, 0xf6, //0x0000230f testw        %si, %si
	0x0f, 0x85, 0x68, 0x00, 0x00, 0x00, //0x00002312 jne          LBB0_417
	0x49, 0x83, 0xc6, 0x10, //0x00002318 addq         $16, %r14
	0x48, 0x83, 0xc0, 0xf0, //0x0000231c addq         $-16, %rax
	0x48, 0x83, 0xf8, 0x0f, //0x00002320 cmpq         $15, %rax
	0x0f, 0x87, 0xc6, 0xff, 0xff, 0xff, //0x00002324 ja           LBB0_406
	0x48, 0x89, 0xd0, //0x0000232a movq         %rdx, %rax
	0x49, 0x89, 0xce, //0x0000232d movq         %rcx, %r14
	//0x00002330 LBB0_409
	0x48, 0x85, 0xc0, //0x00002330 testq        %rax, %rax
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x00002333 je           LBB0_416
	0x49, 0x8d, 0x0c, 0x06, //0x00002339 leaq         (%r14,%rax), %rcx
	0x90, 0x90, 0x90, //0x0000233d .p2align 4, 0x90
	//0x00002340 LBB0_411
	0x41, 0x0f, 0xb6, 0x16, //0x00002340 movzbl       (%r14), %edx
	0x80, 0xfa, 0x2c, //0x00002344 cmpb         $44, %dl
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x00002347 je           LBB0_416
	0x80, 0xfa, 0x7d, //0x0000234d cmpb         $125, %dl
	0x0f, 0x84, 0x18, 0x00, 0x00, 0x00, //0x00002350 je           LBB0_416
	0x80, 0xfa, 0x5d, //0x00002356 cmpb         $93, %dl
	0x0f, 0x84, 0x0f, 0x00, 0x00, 0x00, //0x00002359 je           LBB0_416
	0x49, 0xff, 0xc6, //0x0000235f incq         %r14
	0x48, 0xff, 0xc8, //0x00002362 decq         %rax
	0x0f, 0x85, 0xd5, 0xff, 0xff, 0xff, //0x00002365 jne          LBB0_411
	0x49, 0x89, 0xce, //0x0000236b movq         %rcx, %r14
	//0x0000236e LBB0_416
	0x49, 0x29, 0xfe, //0x0000236e subq         %rdi, %r14
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00002371 jmp          LBB0_418
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002376 .p2align 4, 0x90
	//0x00002380 LBB0_417
	0x0f, 0xb7, 0xc6, //0x00002380 movzwl       %si, %eax
	0x48, 0x0f, 0xbc, 0xc0, //0x00002383 bsfq         %rax, %rax
	0x49, 0x29, 0xfe, //0x00002387 subq         %rdi, %r14
	0x49, 0x01, 0xc6, //0x0000238a addq         %rax, %r14
	//0x0000238d LBB0_418
	0x4d, 0x89, 0x33, //0x0000238d movq         %r14, (%r11)
	//0x00002390 LBB0_419
	0x49, 0x8b, 0x7d, 0x00, //0x00002390 movq         (%r13), %rdi
	0x49, 0x8b, 0x45, 0x08, //0x00002394 movq         $8(%r13), %rax
	0x4c, 0x89, 0xf1, //0x00002398 movq         %r14, %rcx
	0x48, 0x29, 0xc1, //0x0000239b subq         %rax, %rcx
	0x0f, 0x83, 0x2c, 0x00, 0x00, 0x00, //0x0000239e jae          LBB0_424
	0x42, 0x8a, 0x14, 0x37, //0x000023a4 movb         (%rdi,%r14), %dl
	0x80, 0xfa, 0x0d, //0x000023a8 cmpb         $13, %dl
	0x0f, 0x84, 0x1f, 0x00, 0x00, 0x00, //0x000023ab je           LBB0_424
	0x80, 0xfa, 0x20, //0x000023b1 cmpb         $32, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000023b4 je           LBB0_424
	0x80, 0xc2, 0xf7, //0x000023ba addb         $-9, %dl
	0x80, 0xfa, 0x01, //0x000023bd cmpb         $1, %dl
	0x0f, 0x86, 0x0a, 0x00, 0x00, 0x00, //0x000023c0 jbe          LBB0_424
	0x4c, 0x89, 0xf2, //0x000023c6 movq         %r14, %rdx
	0xe9, 0x37, 0x01, 0x00, 0x00, //0x000023c9 jmp          LBB0_445
	0x90, 0x90, //0x000023ce .p2align 4, 0x90
	//0x000023d0 LBB0_424
	0x49, 0x8d, 0x56, 0x01, //0x000023d0 leaq         $1(%r14), %rdx
	0x48, 0x39, 0xc2, //0x000023d4 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000023d7 jae          LBB0_428
	0x8a, 0x1c, 0x17, //0x000023dd movb         (%rdi,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x000023e0 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x000023e3 je           LBB0_428
	0x80, 0xfb, 0x20, //0x000023e9 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x000023ec je           LBB0_428
	0x80, 0xc3, 0xf7, //0x000023f2 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x000023f5 cmpb         $1, %bl
	0x0f, 0x87, 0x07, 0x01, 0x00, 0x00, //0x000023f8 ja           LBB0_445
	0x90, 0x90, //0x000023fe .p2align 4, 0x90
	//0x00002400 LBB0_428
	0x49, 0x8d, 0x56, 0x02, //0x00002400 leaq         $2(%r14), %rdx
	0x48, 0x39, 0xc2, //0x00002404 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00002407 jae          LBB0_432
	0x8a, 0x1c, 0x17, //0x0000240d movb         (%rdi,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x00002410 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00002413 je           LBB0_432
	0x80, 0xfb, 0x20, //0x00002419 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x0000241c je           LBB0_432
	0x80, 0xc3, 0xf7, //0x00002422 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00002425 cmpb         $1, %bl
	0x0f, 0x87, 0xd7, 0x00, 0x00, 0x00, //0x00002428 ja           LBB0_445
	0x90, 0x90, //0x0000242e .p2align 4, 0x90
	//0x00002430 LBB0_432
	0x49, 0x8d, 0x56, 0x03, //0x00002430 leaq         $3(%r14), %rdx
	0x48, 0x39, 0xc2, //0x00002434 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00002437 jae          LBB0_436
	0x8a, 0x1c, 0x17, //0x0000243d movb         (%rdi,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x00002440 cmpb         $13, %bl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00002443 je           LBB0_436
	0x80, 0xfb, 0x20, //0x00002449 cmpb         $32, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x0000244c je           LBB0_436
	0x80, 0xc3, 0xf7, //0x00002452 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00002455 cmpb         $1, %bl
	0x0f, 0x87, 0xa7, 0x00, 0x00, 0x00, //0x00002458 ja           LBB0_445
	0x90, 0x90, //0x0000245e .p2align 4, 0x90
	//0x00002460 LBB0_436
	0x49, 0x8d, 0x76, 0x04, //0x00002460 leaq         $4(%r14), %rsi
	0x48, 0x39, 0xf0, //0x00002464 cmpq         %rsi, %rax
	0x0f, 0x86, 0x7d, 0x0d, 0x00, 0x00, //0x00002467 jbe          LBB0_549
	0x48, 0x39, 0xf0, //0x0000246d cmpq         %rsi, %rax
	0x0f, 0x84, 0x5a, 0x00, 0x00, 0x00, //0x00002470 je           LBB0_443
	0x48, 0x8d, 0x34, 0x07, //0x00002476 leaq         (%rdi,%rax), %rsi
	0x48, 0x83, 0xc1, 0x04, //0x0000247a addq         $4, %rcx
	0x48, 0x89, 0xfb, //0x0000247e movq         %rdi, %rbx
	0x4a, 0x8d, 0x54, 0x37, 0x05, //0x00002481 leaq         $5(%rdi,%r14), %rdx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002486 .p2align 4, 0x90
	//0x00002490 LBB0_439
	0x0f, 0xbe, 0x7a, 0xff, //0x00002490 movsbl       $-1(%rdx), %edi
	0x83, 0xff, 0x20, //0x00002494 cmpl         $32, %edi
	0x0f, 0x87, 0x53, 0x00, 0x00, 0x00, //0x00002497 ja           LBB0_444
	0x49, 0x0f, 0xa3, 0xfa, //0x0000249d btq          %rdi, %r10
	0x0f, 0x83, 0x49, 0x00, 0x00, 0x00, //0x000024a1 jae          LBB0_444
	0x48, 0xff, 0xc2, //0x000024a7 incq         %rdx
	0x48, 0xff, 0xc1, //0x000024aa incq         %rcx
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x000024ad jne          LBB0_439
	0x48, 0x89, 0xdf, //0x000024b3 movq         %rbx, %rdi
	0x48, 0x29, 0xfe, //0x000024b6 subq         %rdi, %rsi
	0x48, 0x89, 0xf2, //0x000024b9 movq         %rsi, %rdx
	0x48, 0x39, 0xc2, //0x000024bc cmpq         %rax, %rdx
	0x0f, 0x82, 0x40, 0x00, 0x00, 0x00, //0x000024bf jb           LBB0_445
	0xe9, 0x78, 0x0d, 0x00, 0x00, //0x000024c5 jmp          LBB0_556
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000024ca .p2align 4, 0x90
	//0x000024d0 LBB0_443
	0x48, 0x01, 0xfe, //0x000024d0 addq         %rdi, %rsi
	0x48, 0x29, 0xfe, //0x000024d3 subq         %rdi, %rsi
	0x48, 0x89, 0xf2, //0x000024d6 movq         %rsi, %rdx
	0x48, 0x39, 0xc2, //0x000024d9 cmpq         %rax, %rdx
	0x0f, 0x82, 0x23, 0x00, 0x00, 0x00, //0x000024dc jb           LBB0_445
	0xe9, 0x5b, 0x0d, 0x00, 0x00, //0x000024e2 jmp          LBB0_556
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000024e7 .p2align 4, 0x90
	//0x000024f0 LBB0_444
	0x48, 0x89, 0xdf, //0x000024f0 movq         %rbx, %rdi
	0x48, 0x89, 0xd9, //0x000024f3 movq         %rbx, %rcx
	0x48, 0xf7, 0xd1, //0x000024f6 notq         %rcx
	0x48, 0x01, 0xca, //0x000024f9 addq         %rcx, %rdx
	0x48, 0x39, 0xc2, //0x000024fc cmpq         %rax, %rdx
	0x0f, 0x83, 0x3d, 0x0d, 0x00, 0x00, //0x000024ff jae          LBB0_556
	//0x00002505 LBB0_445
	0x4c, 0x8d, 0x72, 0x01, //0x00002505 leaq         $1(%rdx), %r14
	0x4d, 0x89, 0x33, //0x00002509 movq         %r14, (%r11)
	0x8a, 0x04, 0x17, //0x0000250c movb         (%rdi,%rdx), %al
	0x3c, 0x2c, //0x0000250f cmpb         $44, %al
	0x0f, 0x85, 0xde, 0x0c, 0x00, 0x00, //0x00002511 jne          LBB0_550
	0x49, 0x83, 0xfc, 0x02, //0x00002517 cmpq         $2, %r12
	0x4d, 0x8d, 0x64, 0x24, 0xff, //0x0000251b leaq         $-1(%r12), %r12
	0x0f, 0x8d, 0xea, 0xfb, 0xff, 0xff, //0x00002520 jge          LBB0_375
	0xe9, 0xf5, 0x0a, 0x00, 0x00, //0x00002526 jmp          LBB0_447
	//0x0000252b LBB0_450
	0x48, 0x83, 0xc1, 0x04, //0x0000252b addq         $4, %rcx
	0x49, 0x3b, 0x08, //0x0000252f cmpq         (%r8), %rcx
	0x0f, 0x87, 0x58, 0xfe, 0xff, 0xff, //0x00002532 ja           LBB0_419
	0xe9, 0x2f, 0x01, 0x00, 0x00, //0x00002538 jmp          LBB0_462
	//0x0000253d LBB0_451
	0x4c, 0x89, 0x4d, 0xa0, //0x0000253d movq         %r9, $-96(%rbp)
	0x4d, 0x8b, 0x00, //0x00002541 movq         (%r8), %r8
	0x4c, 0x89, 0xc0, //0x00002544 movq         %r8, %rax
	0x4c, 0x29, 0xf0, //0x00002547 subq         %r14, %rax
	0x48, 0x83, 0xf8, 0x20, //0x0000254a cmpq         $32, %rax
	0x0f, 0x8c, 0x46, 0x0a, 0x00, 0x00, //0x0000254e jl           LBB0_521
	0x4c, 0x8d, 0x0c, 0x0f, //0x00002554 leaq         (%rdi,%rcx), %r9
	0x49, 0x29, 0xc8, //0x00002558 subq         %rcx, %r8
	0xb9, 0x1f, 0x00, 0x00, 0x00, //0x0000255b movl         $31, %ecx
	0x31, 0xc0, //0x00002560 xorl         %eax, %eax
	0x45, 0x31, 0xd2, //0x00002562 xorl         %r10d, %r10d
	0xe9, 0x28, 0x00, 0x00, 0x00, //0x00002565 jmp          LBB0_453
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000256a .p2align 4, 0x90
	//0x00002570 LBB0_456
	0x45, 0x31, 0xd2, //0x00002570 xorl         %r10d, %r10d
	0x85, 0xdb, //0x00002573 testl        %ebx, %ebx
	0x0f, 0x85, 0xb6, 0x00, 0x00, 0x00, //0x00002575 jne          LBB0_455
	//0x0000257b LBB0_457
	0x48, 0x83, 0xc0, 0x20, //0x0000257b addq         $32, %rax
	0x49, 0x8d, 0x54, 0x08, 0xe0, //0x0000257f leaq         $-32(%r8,%rcx), %rdx
	0x48, 0x83, 0xc1, 0xe0, //0x00002584 addq         $-32, %rcx
	0x48, 0x83, 0xfa, 0x3f, //0x00002588 cmpq         $63, %rdx
	0x0f, 0x8e, 0x7f, 0x09, 0x00, 0x00, //0x0000258c jle          LBB0_458
	//0x00002592 LBB0_453
	0xf3, 0x41, 0x0f, 0x6f, 0x54, 0x01, 0x01, //0x00002592 movdqu       $1(%r9,%rax), %xmm2
	0xf3, 0x41, 0x0f, 0x6f, 0x5c, 0x01, 0x11, //0x00002599 movdqu       $17(%r9,%rax), %xmm3
	0x66, 0x0f, 0x6f, 0xea, //0x000025a0 movdqa       %xmm2, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x000025a4 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xd5, //0x000025a8 pmovmskb     %xmm5, %edx
	0x66, 0x0f, 0x6f, 0xeb, //0x000025ac movdqa       %xmm3, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x000025b0 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xdd, //0x000025b4 pmovmskb     %xmm5, %ebx
	0x48, 0xc1, 0xe3, 0x10, //0x000025b8 shlq         $16, %rbx
	0x48, 0x09, 0xd3, //0x000025bc orq          %rdx, %rbx
	0x66, 0x0f, 0x74, 0xd1, //0x000025bf pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xf2, //0x000025c3 pmovmskb     %xmm2, %esi
	0x66, 0x0f, 0x74, 0xd9, //0x000025c7 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xd3, //0x000025cb pmovmskb     %xmm3, %edx
	0x48, 0xc1, 0xe2, 0x10, //0x000025cf shlq         $16, %rdx
	0x48, 0x09, 0xf2, //0x000025d3 orq          %rsi, %rdx
	0x48, 0x89, 0xd6, //0x000025d6 movq         %rdx, %rsi
	0x4c, 0x09, 0xd6, //0x000025d9 orq          %r10, %rsi
	0x0f, 0x84, 0x8e, 0xff, 0xff, 0xff, //0x000025dc je           LBB0_456
	0x44, 0x89, 0xd6, //0x000025e2 movl         %r10d, %esi
	0x41, 0xbd, 0xff, 0xff, 0xff, 0xff, //0x000025e5 movl         $4294967295, %r13d
	0x44, 0x31, 0xee, //0x000025eb xorl         %r13d, %esi
	0x21, 0xf2, //0x000025ee andl         %esi, %edx
	0x8d, 0x34, 0x12, //0x000025f0 leal         (%rdx,%rdx), %esi
	0x44, 0x09, 0xd6, //0x000025f3 orl          %r10d, %esi
	0x49, 0x89, 0xff, //0x000025f6 movq         %rdi, %r15
	0x41, 0x8d, 0xbd, 0xab, 0xaa, 0xaa, 0xaa, //0x000025f9 leal         $-1431655765(%r13), %edi
	0x31, 0xf7, //0x00002600 xorl         %esi, %edi
	0x21, 0xd7, //0x00002602 andl         %edx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002604 andl         $-1431655766, %edi
	0x45, 0x31, 0xd2, //0x0000260a xorl         %r10d, %r10d
	0x01, 0xd7, //0x0000260d addl         %edx, %edi
	0x41, 0x0f, 0x92, 0xc2, //0x0000260f setb         %r10b
	0x01, 0xff, //0x00002613 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00002615 xorl         $1431655765, %edi
	0x21, 0xf7, //0x0000261b andl         %esi, %edi
	0x44, 0x31, 0xef, //0x0000261d xorl         %r13d, %edi
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002620 movq         $-64(%rbp), %r13
	0x21, 0xfb, //0x00002624 andl         %edi, %ebx
	0x4c, 0x89, 0xff, //0x00002626 movq         %r15, %rdi
	0x85, 0xdb, //0x00002629 testl        %ebx, %ebx
	0x0f, 0x84, 0x4a, 0xff, 0xff, 0xff, //0x0000262b je           LBB0_457
	//0x00002631 LBB0_455
	0x48, 0x0f, 0xbc, 0xcb, //0x00002631 bsfq         %rbx, %rcx
	0x49, 0x01, 0xc9, //0x00002635 addq         %rcx, %r9
	0x49, 0x01, 0xc1, //0x00002638 addq         %rax, %r9
	0x49, 0x29, 0xf9, //0x0000263b subq         %rdi, %r9
	0x49, 0x83, 0xc1, 0x02, //0x0000263e addq         $2, %r9
	0x4d, 0x89, 0x0b, //0x00002642 movq         %r9, (%r11)
	0x4d, 0x89, 0xce, //0x00002645 movq         %r9, %r14
	0x4c, 0x8b, 0x45, 0xb0, //0x00002648 movq         $-80(%rbp), %r8
	0x49, 0xba, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000264c movabsq      $4294977024, %r10
	0x4c, 0x8b, 0x4d, 0xa0, //0x00002656 movq         $-96(%rbp), %r9
	0xe9, 0x31, 0xfd, 0xff, 0xff, //0x0000265a jmp          LBB0_419
	//0x0000265f LBB0_461
	0x48, 0x83, 0xc1, 0x05, //0x0000265f addq         $5, %rcx
	0x49, 0x3b, 0x08, //0x00002663 cmpq         (%r8), %rcx
	0x0f, 0x87, 0x24, 0xfd, 0xff, 0xff, //0x00002666 ja           LBB0_419
	//0x0000266c LBB0_462
	0x49, 0x89, 0x0b, //0x0000266c movq         %rcx, (%r11)
	0x49, 0x89, 0xce, //0x0000266f movq         %rcx, %r14
	0xe9, 0x19, 0xfd, 0xff, 0xff, //0x00002672 jmp          LBB0_419
	//0x00002677 LBB0_463
	0x4c, 0x89, 0x4d, 0xa0, //0x00002677 movq         %r9, $-96(%rbp)
	0x49, 0x8b, 0x08, //0x0000267b movq         (%r8), %rcx
	0x4c, 0x29, 0xf1, //0x0000267e subq         %r14, %rcx
	0x4c, 0x01, 0xf7, //0x00002681 addq         %r14, %rdi
	0x45, 0x31, 0xc9, //0x00002684 xorl         %r9d, %r9d
	0x45, 0x31, 0xc0, //0x00002687 xorl         %r8d, %r8d
	0x45, 0x31, 0xdb, //0x0000268a xorl         %r11d, %r11d
	0x45, 0x31, 0xed, //0x0000268d xorl         %r13d, %r13d
	0x48, 0x83, 0xf9, 0x40, //0x00002690 cmpq         $64, %rcx
	0x0f, 0x8d, 0x63, 0x01, 0x00, 0x00, //0x00002694 jge          LBB0_464
	//0x0000269a LBB0_474
	0x48, 0x8b, 0x45, 0xa0, //0x0000269a movq         $-96(%rbp), %rax
	0x48, 0x85, 0xc9, //0x0000269e testq        %rcx, %rcx
	0x0f, 0x8e, 0xfc, 0x08, 0x00, 0x00, //0x000026a1 jle          LBB0_522
	0x49, 0x89, 0xcf, //0x000026a7 movq         %rcx, %r15
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x70, 0xff, 0xff, 0xff, //0x000026aa movdqu       %xmm8, $-144(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x60, 0xff, 0xff, 0xff, //0x000026b3 movdqu       %xmm8, $-160(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x50, 0xff, 0xff, 0xff, //0x000026bc movdqu       %xmm8, $-176(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x40, 0xff, 0xff, 0xff, //0x000026c5 movdqu       %xmm8, $-192(%rbp)
	0x89, 0xf8, //0x000026ce movl         %edi, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x000026d0 andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x000026d5 cmpl         $4033, %eax
	0x0f, 0x82, 0x20, 0x01, 0x00, 0x00, //0x000026da jb           LBB0_465
	0x48, 0x89, 0xf8, //0x000026e0 movq         %rdi, %rax
	0x49, 0x83, 0xff, 0x20, //0x000026e3 cmpq         $32, %r15
	0x0f, 0x82, 0x2b, 0x00, 0x00, 0x00, //0x000026e7 jb           LBB0_478
	0x0f, 0x10, 0x10, //0x000026ed movups       (%rax), %xmm2
	0x0f, 0x11, 0x95, 0x40, 0xff, 0xff, 0xff, //0x000026f0 movups       %xmm2, $-192(%rbp)
	0xf3, 0x0f, 0x6f, 0x50, 0x10, //0x000026f7 movdqu       $16(%rax), %xmm2
	0xf3, 0x0f, 0x7f, 0x95, 0x50, 0xff, 0xff, 0xff, //0x000026fc movdqu       %xmm2, $-176(%rbp)
	0x48, 0x83, 0xc0, 0x20, //0x00002704 addq         $32, %rax
	0x49, 0x8d, 0x7f, 0xe0, //0x00002708 leaq         $-32(%r15), %rdi
	0x48, 0x8d, 0xb5, 0x60, 0xff, 0xff, 0xff, //0x0000270c leaq         $-160(%rbp), %rsi
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00002713 jmp          LBB0_479
	//0x00002718 LBB0_478
	0x48, 0x8d, 0xb5, 0x40, 0xff, 0xff, 0xff, //0x00002718 leaq         $-192(%rbp), %rsi
	0x4c, 0x89, 0xff, //0x0000271f movq         %r15, %rdi
	//0x00002722 LBB0_479
	0x48, 0x83, 0xff, 0x10, //0x00002722 cmpq         $16, %rdi
	0x0f, 0x82, 0x60, 0x00, 0x00, 0x00, //0x00002726 jb           LBB0_480
	0xf3, 0x0f, 0x6f, 0x10, //0x0000272c movdqu       (%rax), %xmm2
	0xf3, 0x0f, 0x7f, 0x16, //0x00002730 movdqu       %xmm2, (%rsi)
	0x48, 0x83, 0xc0, 0x10, //0x00002734 addq         $16, %rax
	0x48, 0x83, 0xc6, 0x10, //0x00002738 addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x0000273c addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x00002740 cmpq         $8, %rdi
	0x0f, 0x83, 0x4c, 0x00, 0x00, 0x00, //0x00002744 jae          LBB0_485
	//0x0000274a LBB0_481
	0x48, 0x83, 0xff, 0x04, //0x0000274a cmpq         $4, %rdi
	0x0f, 0x8c, 0x64, 0x00, 0x00, 0x00, //0x0000274e jl           LBB0_482
	//0x00002754 LBB0_486
	0x48, 0x89, 0xc2, //0x00002754 movq         %rax, %rdx
	0x8b, 0x00, //0x00002757 movl         (%rax), %eax
	0x89, 0x06, //0x00002759 movl         %eax, (%rsi)
	0x48, 0x83, 0xc2, 0x04, //0x0000275b addq         $4, %rdx
	0x48, 0x89, 0xd0, //0x0000275f movq         %rdx, %rax
	0x48, 0x83, 0xc6, 0x04, //0x00002762 addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x00002766 addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x0000276a cmpq         $2, %rdi
	0x0f, 0x83, 0x4e, 0x00, 0x00, 0x00, //0x0000276e jae          LBB0_487
	//0x00002774 LBB0_483
	0x48, 0x8d, 0x95, 0x40, 0xff, 0xff, 0xff, //0x00002774 leaq         $-192(%rbp), %rdx
	0x48, 0x85, 0xff, //0x0000277b testq        %rdi, %rdi
	0x48, 0x89, 0xd7, //0x0000277e movq         %rdx, %rdi
	0x0f, 0x85, 0x66, 0x00, 0x00, 0x00, //0x00002781 jne          LBB0_488
	0xe9, 0x74, 0x00, 0x00, 0x00, //0x00002787 jmp          LBB0_465
	//0x0000278c LBB0_480
	0x48, 0x83, 0xff, 0x08, //0x0000278c cmpq         $8, %rdi
	0x0f, 0x82, 0xb4, 0xff, 0xff, 0xff, //0x00002790 jb           LBB0_481
	//0x00002796 LBB0_485
	0x48, 0x89, 0xc2, //0x00002796 movq         %rax, %rdx
	0x48, 0x8b, 0x00, //0x00002799 movq         (%rax), %rax
	0x48, 0x89, 0x06, //0x0000279c movq         %rax, (%rsi)
	0x48, 0x83, 0xc2, 0x08, //0x0000279f addq         $8, %rdx
	0x48, 0x89, 0xd0, //0x000027a3 movq         %rdx, %rax
	0x48, 0x83, 0xc6, 0x08, //0x000027a6 addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x000027aa addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x000027ae cmpq         $4, %rdi
	0x0f, 0x8d, 0x9c, 0xff, 0xff, 0xff, //0x000027b2 jge          LBB0_486
	//0x000027b8 LBB0_482
	0x48, 0x83, 0xff, 0x02, //0x000027b8 cmpq         $2, %rdi
	0x0f, 0x82, 0xb2, 0xff, 0xff, 0xff, //0x000027bc jb           LBB0_483
	//0x000027c2 LBB0_487
	0x48, 0x89, 0xc2, //0x000027c2 movq         %rax, %rdx
	0x0f, 0xb7, 0x00, //0x000027c5 movzwl       (%rax), %eax
	0x66, 0x89, 0x06, //0x000027c8 movw         %ax, (%rsi)
	0x48, 0x83, 0xc2, 0x02, //0x000027cb addq         $2, %rdx
	0x48, 0x83, 0xc6, 0x02, //0x000027cf addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x000027d3 addq         $-2, %rdi
	0x48, 0x89, 0xd0, //0x000027d7 movq         %rdx, %rax
	0x48, 0x8d, 0x95, 0x40, 0xff, 0xff, 0xff, //0x000027da leaq         $-192(%rbp), %rdx
	0x48, 0x85, 0xff, //0x000027e1 testq        %rdi, %rdi
	0x48, 0x89, 0xd7, //0x000027e4 movq         %rdx, %rdi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x000027e7 je           LBB0_465
	//0x000027ed LBB0_488
	0x8a, 0x00, //0x000027ed movb         (%rax), %al
	0x88, 0x06, //0x000027ef movb         %al, (%rsi)
	0x48, 0x8d, 0xbd, 0x40, 0xff, 0xff, 0xff, //0x000027f1 leaq         $-192(%rbp), %rdi
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x000027f8 jmp          LBB0_465
	//0x000027fd LBB0_464
	0x49, 0x89, 0xcf, //0x000027fd movq         %rcx, %r15
	//0x00002800 LBB0_465
	0xf3, 0x0f, 0x6f, 0x17, //0x00002800 movdqu       (%rdi), %xmm2
	0xf3, 0x0f, 0x6f, 0x6f, 0x10, //0x00002804 movdqu       $16(%rdi), %xmm5
	0xf3, 0x0f, 0x6f, 0x7f, 0x20, //0x00002809 movdqu       $32(%rdi), %xmm7
	0x48, 0x89, 0x7d, 0xc8, //0x0000280e movq         %rdi, $-56(%rbp)
	0xf3, 0x0f, 0x6f, 0x77, 0x30, //0x00002812 movdqu       $48(%rdi), %xmm6
	0x66, 0x0f, 0x6f, 0xda, //0x00002817 movdqa       %xmm2, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x0000281b pcmpeqb      %xmm0, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xf3, //0x0000281f pmovmskb     %xmm3, %r14d
	0x66, 0x0f, 0x6f, 0xdd, //0x00002824 movdqa       %xmm5, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00002828 pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xc3, //0x0000282c pmovmskb     %xmm3, %eax
	0x66, 0x0f, 0x6f, 0xdf, //0x00002830 movdqa       %xmm7, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00002834 pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x00002838 pmovmskb     %xmm3, %edi
	0x66, 0x0f, 0x6f, 0xde, //0x0000283c movdqa       %xmm6, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00002840 pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xdb, //0x00002844 pmovmskb     %xmm3, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x00002848 shlq         $48, %rbx
	0x48, 0xc1, 0xe7, 0x20, //0x0000284c shlq         $32, %rdi
	0x48, 0xc1, 0xe0, 0x10, //0x00002850 shlq         $16, %rax
	0x49, 0x09, 0xc6, //0x00002854 orq          %rax, %r14
	0x49, 0x09, 0xfe, //0x00002857 orq          %rdi, %r14
	0x49, 0x09, 0xde, //0x0000285a orq          %rbx, %r14
	0x66, 0x0f, 0x6f, 0xda, //0x0000285d movdqa       %xmm2, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00002861 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xc3, //0x00002865 pmovmskb     %xmm3, %eax
	0x66, 0x0f, 0x6f, 0xdd, //0x00002869 movdqa       %xmm5, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x0000286d pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x00002871 pmovmskb     %xmm3, %edi
	0x66, 0x0f, 0x6f, 0xdf, //0x00002875 movdqa       %xmm7, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00002879 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xdb, //0x0000287d pmovmskb     %xmm3, %ebx
	0x66, 0x0f, 0x6f, 0xde, //0x00002881 movdqa       %xmm6, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00002885 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x00002889 pmovmskb     %xmm3, %ecx
	0x48, 0xc1, 0xe1, 0x30, //0x0000288d shlq         $48, %rcx
	0x48, 0xc1, 0xe3, 0x20, //0x00002891 shlq         $32, %rbx
	0x48, 0xc1, 0xe7, 0x10, //0x00002895 shlq         $16, %rdi
	0x48, 0x09, 0xf8, //0x00002899 orq          %rdi, %rax
	0x48, 0x09, 0xd8, //0x0000289c orq          %rbx, %rax
	0x48, 0x09, 0xc8, //0x0000289f orq          %rcx, %rax
	0x48, 0x89, 0xc1, //0x000028a2 movq         %rax, %rcx
	0x4c, 0x09, 0xc1, //0x000028a5 orq          %r8, %rcx
	0x0f, 0x84, 0x4b, 0x00, 0x00, 0x00, //0x000028a8 je           LBB0_467
	0x4c, 0x89, 0xc1, //0x000028ae movq         %r8, %rcx
	0x48, 0xf7, 0xd1, //0x000028b1 notq         %rcx
	0x48, 0x21, 0xc1, //0x000028b4 andq         %rax, %rcx
	0x48, 0x8d, 0x1c, 0x09, //0x000028b7 leaq         (%rcx,%rcx), %rbx
	0x4c, 0x09, 0xc3, //0x000028bb orq          %r8, %rbx
	0x48, 0x89, 0xdf, //0x000028be movq         %rbx, %rdi
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000028c1 movabsq      $-6148914691236517206, %rsi
	0x48, 0x31, 0xf7, //0x000028cb xorq         %rsi, %rdi
	0x48, 0x21, 0xf0, //0x000028ce andq         %rsi, %rax
	0x48, 0x21, 0xf8, //0x000028d1 andq         %rdi, %rax
	0x45, 0x31, 0xc0, //0x000028d4 xorl         %r8d, %r8d
	0x48, 0x01, 0xc8, //0x000028d7 addq         %rcx, %rax
	0x41, 0x0f, 0x92, 0xc0, //0x000028da setb         %r8b
	0x48, 0x01, 0xc0, //0x000028de addq         %rax, %rax
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000028e1 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xc8, //0x000028eb xorq         %rcx, %rax
	0x48, 0x21, 0xd8, //0x000028ee andq         %rbx, %rax
	0x48, 0xf7, 0xd0, //0x000028f1 notq         %rax
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x000028f4 jmp          LBB0_468
	//0x000028f9 LBB0_467
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000028f9 movq         $-1, %rax
	0x45, 0x31, 0xc0, //0x00002900 xorl         %r8d, %r8d
	//0x00002903 LBB0_468
	0x4c, 0x21, 0xf0, //0x00002903 andq         %r14, %rax
	0x66, 0x48, 0x0f, 0x6e, 0xd8, //0x00002906 movq         %rax, %xmm3
	0x66, 0x41, 0x0f, 0x3a, 0x44, 0xd9, 0x00, //0x0000290b pclmulqdq    $0, %xmm9, %xmm3
	0x66, 0x49, 0x0f, 0x7e, 0xde, //0x00002912 movq         %xmm3, %r14
	0x4d, 0x31, 0xce, //0x00002917 xorq         %r9, %r14
	0x66, 0x0f, 0x6f, 0xda, //0x0000291a movdqa       %xmm2, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdc, //0x0000291e pcmpeqb      %xmm12, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xd3, //0x00002923 pmovmskb     %xmm3, %r10d
	0x66, 0x0f, 0x6f, 0xdd, //0x00002928 movdqa       %xmm5, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdc, //0x0000292c pcmpeqb      %xmm12, %xmm3
	0x66, 0x0f, 0xd7, 0xc3, //0x00002931 pmovmskb     %xmm3, %eax
	0x66, 0x0f, 0x6f, 0xdf, //0x00002935 movdqa       %xmm7, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdc, //0x00002939 pcmpeqb      %xmm12, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x0000293e pmovmskb     %xmm3, %ecx
	0x66, 0x0f, 0x6f, 0xde, //0x00002942 movdqa       %xmm6, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdc, //0x00002946 pcmpeqb      %xmm12, %xmm3
	0x66, 0x0f, 0xd7, 0xdb, //0x0000294b pmovmskb     %xmm3, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x0000294f shlq         $48, %rbx
	0x48, 0xc1, 0xe1, 0x20, //0x00002953 shlq         $32, %rcx
	0x48, 0xc1, 0xe0, 0x10, //0x00002957 shlq         $16, %rax
	0x49, 0x09, 0xc2, //0x0000295b orq          %rax, %r10
	0x49, 0x09, 0xca, //0x0000295e orq          %rcx, %r10
	0x49, 0x09, 0xda, //0x00002961 orq          %rbx, %r10
	0x4d, 0x89, 0xf1, //0x00002964 movq         %r14, %r9
	0x49, 0xf7, 0xd1, //0x00002967 notq         %r9
	0x4d, 0x21, 0xca, //0x0000296a andq         %r9, %r10
	0x66, 0x0f, 0x74, 0xd4, //0x0000296d pcmpeqb      %xmm4, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00002971 pmovmskb     %xmm2, %eax
	0x66, 0x0f, 0x74, 0xec, //0x00002975 pcmpeqb      %xmm4, %xmm5
	0x66, 0x0f, 0xd7, 0xdd, //0x00002979 pmovmskb     %xmm5, %ebx
	0x66, 0x0f, 0x74, 0xfc, //0x0000297d pcmpeqb      %xmm4, %xmm7
	0x66, 0x0f, 0xd7, 0xf7, //0x00002981 pmovmskb     %xmm7, %esi
	0x66, 0x0f, 0x74, 0xf4, //0x00002985 pcmpeqb      %xmm4, %xmm6
	0x66, 0x0f, 0xd7, 0xce, //0x00002989 pmovmskb     %xmm6, %ecx
	0x48, 0xc1, 0xe1, 0x30, //0x0000298d shlq         $48, %rcx
	0x48, 0xc1, 0xe6, 0x20, //0x00002991 shlq         $32, %rsi
	0x48, 0xc1, 0xe3, 0x10, //0x00002995 shlq         $16, %rbx
	0x48, 0x09, 0xd8, //0x00002999 orq          %rbx, %rax
	0x48, 0x09, 0xf0, //0x0000299c orq          %rsi, %rax
	0x48, 0x09, 0xc8, //0x0000299f orq          %rcx, %rax
	0x48, 0xbf, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x000029a2 movabsq      $3689348814741910323, %rdi
	0x4c, 0x21, 0xc8, //0x000029ac andq         %r9, %rax
	0x0f, 0x84, 0x77, 0x00, 0x00, 0x00, //0x000029af je           LBB0_472
	0x49, 0xb9, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x000029b5 movabsq      $1085102592571150095, %r9
	0x90, //0x000029bf .p2align 4, 0x90
	//0x000029c0 LBB0_470
	0x48, 0x8d, 0x58, 0xff, //0x000029c0 leaq         $-1(%rax), %rbx
	0x48, 0x89, 0xd9, //0x000029c4 movq         %rbx, %rcx
	0x4c, 0x21, 0xd1, //0x000029c7 andq         %r10, %rcx
	0x48, 0x89, 0xce, //0x000029ca movq         %rcx, %rsi
	0x48, 0xd1, 0xee, //0x000029cd shrq         %rsi
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000029d0 movabsq      $6148914691236517205, %rdx
	0x48, 0x21, 0xd6, //0x000029da andq         %rdx, %rsi
	0x48, 0x29, 0xf1, //0x000029dd subq         %rsi, %rcx
	0x48, 0x89, 0xce, //0x000029e0 movq         %rcx, %rsi
	0x48, 0x21, 0xfe, //0x000029e3 andq         %rdi, %rsi
	0x48, 0xc1, 0xe9, 0x02, //0x000029e6 shrq         $2, %rcx
	0x48, 0x21, 0xf9, //0x000029ea andq         %rdi, %rcx
	0x48, 0x01, 0xf1, //0x000029ed addq         %rsi, %rcx
	0x48, 0x89, 0xce, //0x000029f0 movq         %rcx, %rsi
	0x48, 0xc1, 0xee, 0x04, //0x000029f3 shrq         $4, %rsi
	0x48, 0x01, 0xce, //0x000029f7 addq         %rcx, %rsi
	0x4c, 0x21, 0xce, //0x000029fa andq         %r9, %rsi
	0x48, 0xb9, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x000029fd movabsq      $72340172838076673, %rcx
	0x48, 0x0f, 0xaf, 0xf1, //0x00002a07 imulq        %rcx, %rsi
	0x48, 0xc1, 0xee, 0x38, //0x00002a0b shrq         $56, %rsi
	0x4c, 0x01, 0xde, //0x00002a0f addq         %r11, %rsi
	0x4c, 0x39, 0xee, //0x00002a12 cmpq         %r13, %rsi
	0x0f, 0x86, 0xb8, 0x04, 0x00, 0x00, //0x00002a15 jbe          LBB0_515
	0x49, 0xff, 0xc5, //0x00002a1b incq         %r13
	0x48, 0x21, 0xd8, //0x00002a1e andq         %rbx, %rax
	0x0f, 0x85, 0x99, 0xff, 0xff, 0xff, //0x00002a21 jne          LBB0_470
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00002a27 jmp          LBB0_473
	//0x00002a2c LBB0_472
	0x49, 0xb9, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00002a2c movabsq      $1085102592571150095, %r9
	//0x00002a36 LBB0_473
	0x49, 0xc1, 0xfe, 0x3f, //0x00002a36 sarq         $63, %r14
	0x4c, 0x89, 0xd0, //0x00002a3a movq         %r10, %rax
	0x48, 0xd1, 0xe8, //0x00002a3d shrq         %rax
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002a40 movabsq      $6148914691236517205, %rcx
	0x48, 0x21, 0xc8, //0x00002a4a andq         %rcx, %rax
	0x49, 0x29, 0xc2, //0x00002a4d subq         %rax, %r10
	0x4c, 0x89, 0xd0, //0x00002a50 movq         %r10, %rax
	0x48, 0x21, 0xf8, //0x00002a53 andq         %rdi, %rax
	0x49, 0xc1, 0xea, 0x02, //0x00002a56 shrq         $2, %r10
	0x49, 0x21, 0xfa, //0x00002a5a andq         %rdi, %r10
	0x49, 0x01, 0xc2, //0x00002a5d addq         %rax, %r10
	0x4c, 0x89, 0xd0, //0x00002a60 movq         %r10, %rax
	0x48, 0xc1, 0xe8, 0x04, //0x00002a63 shrq         $4, %rax
	0x4c, 0x01, 0xd0, //0x00002a67 addq         %r10, %rax
	0x4c, 0x21, 0xc8, //0x00002a6a andq         %r9, %rax
	0x48, 0xb9, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00002a6d movabsq      $72340172838076673, %rcx
	0x48, 0x0f, 0xaf, 0xc1, //0x00002a77 imulq        %rcx, %rax
	0x48, 0xc1, 0xe8, 0x38, //0x00002a7b shrq         $56, %rax
	0x49, 0x01, 0xc3, //0x00002a7f addq         %rax, %r11
	0x48, 0x8b, 0x7d, 0xc8, //0x00002a82 movq         $-56(%rbp), %rdi
	0x48, 0x83, 0xc7, 0x40, //0x00002a86 addq         $64, %rdi
	0x4c, 0x89, 0xf9, //0x00002a8a movq         %r15, %rcx
	0x48, 0x83, 0xc1, 0xc0, //0x00002a8d addq         $-64, %rcx
	0x4d, 0x89, 0xf1, //0x00002a91 movq         %r14, %r9
	0x48, 0x83, 0xf9, 0x40, //0x00002a94 cmpq         $64, %rcx
	0x0f, 0x8d, 0x5f, 0xfd, 0xff, 0xff, //0x00002a98 jge          LBB0_464
	0xe9, 0xf7, 0xfb, 0xff, 0xff, //0x00002a9e jmp          LBB0_474
	//0x00002aa3 LBB0_489
	0x4c, 0x89, 0x4d, 0xa0, //0x00002aa3 movq         %r9, $-96(%rbp)
	0x49, 0x8b, 0x08, //0x00002aa7 movq         (%r8), %rcx
	0x4c, 0x29, 0xf1, //0x00002aaa subq         %r14, %rcx
	0x4c, 0x01, 0xf7, //0x00002aad addq         %r14, %rdi
	0x45, 0x31, 0xc9, //0x00002ab0 xorl         %r9d, %r9d
	0x45, 0x31, 0xc0, //0x00002ab3 xorl         %r8d, %r8d
	0x45, 0x31, 0xdb, //0x00002ab6 xorl         %r11d, %r11d
	0x45, 0x31, 0xed, //0x00002ab9 xorl         %r13d, %r13d
	0x48, 0x83, 0xf9, 0x40, //0x00002abc cmpq         $64, %rcx
	0x0f, 0x8d, 0x63, 0x01, 0x00, 0x00, //0x00002ac0 jge          LBB0_490
	//0x00002ac6 LBB0_500
	0x48, 0x8b, 0x45, 0xa0, //0x00002ac6 movq         $-96(%rbp), %rax
	0x48, 0x85, 0xc9, //0x00002aca testq        %rcx, %rcx
	0x0f, 0x8e, 0xd0, 0x04, 0x00, 0x00, //0x00002acd jle          LBB0_522
	0x49, 0x89, 0xcf, //0x00002ad3 movq         %rcx, %r15
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x70, 0xff, 0xff, 0xff, //0x00002ad6 movdqu       %xmm8, $-144(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x60, 0xff, 0xff, 0xff, //0x00002adf movdqu       %xmm8, $-160(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x50, 0xff, 0xff, 0xff, //0x00002ae8 movdqu       %xmm8, $-176(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x40, 0xff, 0xff, 0xff, //0x00002af1 movdqu       %xmm8, $-192(%rbp)
	0x89, 0xf8, //0x00002afa movl         %edi, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x00002afc andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x00002b01 cmpl         $4033, %eax
	0x0f, 0x82, 0x20, 0x01, 0x00, 0x00, //0x00002b06 jb           LBB0_491
	0x48, 0x89, 0xf8, //0x00002b0c movq         %rdi, %rax
	0x49, 0x83, 0xff, 0x20, //0x00002b0f cmpq         $32, %r15
	0x0f, 0x82, 0x2b, 0x00, 0x00, 0x00, //0x00002b13 jb           LBB0_504
	0x0f, 0x10, 0x10, //0x00002b19 movups       (%rax), %xmm2
	0x0f, 0x11, 0x95, 0x40, 0xff, 0xff, 0xff, //0x00002b1c movups       %xmm2, $-192(%rbp)
	0xf3, 0x0f, 0x6f, 0x50, 0x10, //0x00002b23 movdqu       $16(%rax), %xmm2
	0xf3, 0x0f, 0x7f, 0x95, 0x50, 0xff, 0xff, 0xff, //0x00002b28 movdqu       %xmm2, $-176(%rbp)
	0x48, 0x83, 0xc0, 0x20, //0x00002b30 addq         $32, %rax
	0x49, 0x8d, 0x7f, 0xe0, //0x00002b34 leaq         $-32(%r15), %rdi
	0x48, 0x8d, 0xb5, 0x60, 0xff, 0xff, 0xff, //0x00002b38 leaq         $-160(%rbp), %rsi
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00002b3f jmp          LBB0_505
	//0x00002b44 LBB0_504
	0x48, 0x8d, 0xb5, 0x40, 0xff, 0xff, 0xff, //0x00002b44 leaq         $-192(%rbp), %rsi
	0x4c, 0x89, 0xff, //0x00002b4b movq         %r15, %rdi
	//0x00002b4e LBB0_505
	0x48, 0x83, 0xff, 0x10, //0x00002b4e cmpq         $16, %rdi
	0x0f, 0x82, 0x60, 0x00, 0x00, 0x00, //0x00002b52 jb           LBB0_506
	0xf3, 0x0f, 0x6f, 0x10, //0x00002b58 movdqu       (%rax), %xmm2
	0xf3, 0x0f, 0x7f, 0x16, //0x00002b5c movdqu       %xmm2, (%rsi)
	0x48, 0x83, 0xc0, 0x10, //0x00002b60 addq         $16, %rax
	0x48, 0x83, 0xc6, 0x10, //0x00002b64 addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x00002b68 addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x00002b6c cmpq         $8, %rdi
	0x0f, 0x83, 0x4c, 0x00, 0x00, 0x00, //0x00002b70 jae          LBB0_511
	//0x00002b76 LBB0_507
	0x48, 0x83, 0xff, 0x04, //0x00002b76 cmpq         $4, %rdi
	0x0f, 0x8c, 0x64, 0x00, 0x00, 0x00, //0x00002b7a jl           LBB0_508
	//0x00002b80 LBB0_512
	0x48, 0x89, 0xc2, //0x00002b80 movq         %rax, %rdx
	0x8b, 0x00, //0x00002b83 movl         (%rax), %eax
	0x89, 0x06, //0x00002b85 movl         %eax, (%rsi)
	0x48, 0x83, 0xc2, 0x04, //0x00002b87 addq         $4, %rdx
	0x48, 0x89, 0xd0, //0x00002b8b movq         %rdx, %rax
	0x48, 0x83, 0xc6, 0x04, //0x00002b8e addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x00002b92 addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x00002b96 cmpq         $2, %rdi
	0x0f, 0x83, 0x4e, 0x00, 0x00, 0x00, //0x00002b9a jae          LBB0_513
	//0x00002ba0 LBB0_509
	0x48, 0x8d, 0x95, 0x40, 0xff, 0xff, 0xff, //0x00002ba0 leaq         $-192(%rbp), %rdx
	0x48, 0x85, 0xff, //0x00002ba7 testq        %rdi, %rdi
	0x48, 0x89, 0xd7, //0x00002baa movq         %rdx, %rdi
	0x0f, 0x85, 0x66, 0x00, 0x00, 0x00, //0x00002bad jne          LBB0_514
	0xe9, 0x74, 0x00, 0x00, 0x00, //0x00002bb3 jmp          LBB0_491
	//0x00002bb8 LBB0_506
	0x48, 0x83, 0xff, 0x08, //0x00002bb8 cmpq         $8, %rdi
	0x0f, 0x82, 0xb4, 0xff, 0xff, 0xff, //0x00002bbc jb           LBB0_507
	//0x00002bc2 LBB0_511
	0x48, 0x89, 0xc2, //0x00002bc2 movq         %rax, %rdx
	0x48, 0x8b, 0x00, //0x00002bc5 movq         (%rax), %rax
	0x48, 0x89, 0x06, //0x00002bc8 movq         %rax, (%rsi)
	0x48, 0x83, 0xc2, 0x08, //0x00002bcb addq         $8, %rdx
	0x48, 0x89, 0xd0, //0x00002bcf movq         %rdx, %rax
	0x48, 0x83, 0xc6, 0x08, //0x00002bd2 addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x00002bd6 addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x00002bda cmpq         $4, %rdi
	0x0f, 0x8d, 0x9c, 0xff, 0xff, 0xff, //0x00002bde jge          LBB0_512
	//0x00002be4 LBB0_508
	0x48, 0x83, 0xff, 0x02, //0x00002be4 cmpq         $2, %rdi
	0x0f, 0x82, 0xb2, 0xff, 0xff, 0xff, //0x00002be8 jb           LBB0_509
	//0x00002bee LBB0_513
	0x48, 0x89, 0xc2, //0x00002bee movq         %rax, %rdx
	0x0f, 0xb7, 0x00, //0x00002bf1 movzwl       (%rax), %eax
	0x66, 0x89, 0x06, //0x00002bf4 movw         %ax, (%rsi)
	0x48, 0x83, 0xc2, 0x02, //0x00002bf7 addq         $2, %rdx
	0x48, 0x83, 0xc6, 0x02, //0x00002bfb addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x00002bff addq         $-2, %rdi
	0x48, 0x89, 0xd0, //0x00002c03 movq         %rdx, %rax
	0x48, 0x8d, 0x95, 0x40, 0xff, 0xff, 0xff, //0x00002c06 leaq         $-192(%rbp), %rdx
	0x48, 0x85, 0xff, //0x00002c0d testq        %rdi, %rdi
	0x48, 0x89, 0xd7, //0x00002c10 movq         %rdx, %rdi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00002c13 je           LBB0_491
	//0x00002c19 LBB0_514
	0x8a, 0x00, //0x00002c19 movb         (%rax), %al
	0x88, 0x06, //0x00002c1b movb         %al, (%rsi)
	0x48, 0x8d, 0xbd, 0x40, 0xff, 0xff, 0xff, //0x00002c1d leaq         $-192(%rbp), %rdi
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x00002c24 jmp          LBB0_491
	//0x00002c29 LBB0_490
	0x49, 0x89, 0xcf, //0x00002c29 movq         %rcx, %r15
	//0x00002c2c LBB0_491
	0xf3, 0x0f, 0x6f, 0x17, //0x00002c2c movdqu       (%rdi), %xmm2
	0xf3, 0x0f, 0x6f, 0x6f, 0x10, //0x00002c30 movdqu       $16(%rdi), %xmm5
	0xf3, 0x0f, 0x6f, 0x7f, 0x20, //0x00002c35 movdqu       $32(%rdi), %xmm7
	0x48, 0x89, 0x7d, 0xc8, //0x00002c3a movq         %rdi, $-56(%rbp)
	0xf3, 0x0f, 0x6f, 0x77, 0x30, //0x00002c3e movdqu       $48(%rdi), %xmm6
	0x66, 0x0f, 0x6f, 0xda, //0x00002c43 movdqa       %xmm2, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00002c47 pcmpeqb      %xmm0, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xf3, //0x00002c4b pmovmskb     %xmm3, %r14d
	0x66, 0x0f, 0x6f, 0xdd, //0x00002c50 movdqa       %xmm5, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00002c54 pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xc3, //0x00002c58 pmovmskb     %xmm3, %eax
	0x66, 0x0f, 0x6f, 0xdf, //0x00002c5c movdqa       %xmm7, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00002c60 pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x00002c64 pmovmskb     %xmm3, %edi
	0x66, 0x0f, 0x6f, 0xde, //0x00002c68 movdqa       %xmm6, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00002c6c pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xdb, //0x00002c70 pmovmskb     %xmm3, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x00002c74 shlq         $48, %rbx
	0x48, 0xc1, 0xe7, 0x20, //0x00002c78 shlq         $32, %rdi
	0x48, 0xc1, 0xe0, 0x10, //0x00002c7c shlq         $16, %rax
	0x49, 0x09, 0xc6, //0x00002c80 orq          %rax, %r14
	0x49, 0x09, 0xfe, //0x00002c83 orq          %rdi, %r14
	0x49, 0x09, 0xde, //0x00002c86 orq          %rbx, %r14
	0x66, 0x0f, 0x6f, 0xda, //0x00002c89 movdqa       %xmm2, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00002c8d pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xc3, //0x00002c91 pmovmskb     %xmm3, %eax
	0x66, 0x0f, 0x6f, 0xdd, //0x00002c95 movdqa       %xmm5, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00002c99 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x00002c9d pmovmskb     %xmm3, %edi
	0x66, 0x0f, 0x6f, 0xdf, //0x00002ca1 movdqa       %xmm7, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00002ca5 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xdb, //0x00002ca9 pmovmskb     %xmm3, %ebx
	0x66, 0x0f, 0x6f, 0xde, //0x00002cad movdqa       %xmm6, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00002cb1 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x00002cb5 pmovmskb     %xmm3, %ecx
	0x48, 0xc1, 0xe1, 0x30, //0x00002cb9 shlq         $48, %rcx
	0x48, 0xc1, 0xe3, 0x20, //0x00002cbd shlq         $32, %rbx
	0x48, 0xc1, 0xe7, 0x10, //0x00002cc1 shlq         $16, %rdi
	0x48, 0x09, 0xf8, //0x00002cc5 orq          %rdi, %rax
	0x48, 0x09, 0xd8, //0x00002cc8 orq          %rbx, %rax
	0x48, 0x09, 0xc8, //0x00002ccb orq          %rcx, %rax
	0x48, 0x89, 0xc1, //0x00002cce movq         %rax, %rcx
	0x4c, 0x09, 0xc1, //0x00002cd1 orq          %r8, %rcx
	0x0f, 0x84, 0x4b, 0x00, 0x00, 0x00, //0x00002cd4 je           LBB0_493
	0x4c, 0x89, 0xc1, //0x00002cda movq         %r8, %rcx
	0x48, 0xf7, 0xd1, //0x00002cdd notq         %rcx
	0x48, 0x21, 0xc1, //0x00002ce0 andq         %rax, %rcx
	0x48, 0x8d, 0x1c, 0x09, //0x00002ce3 leaq         (%rcx,%rcx), %rbx
	0x4c, 0x09, 0xc3, //0x00002ce7 orq          %r8, %rbx
	0x48, 0x89, 0xdf, //0x00002cea movq         %rbx, %rdi
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002ced movabsq      $-6148914691236517206, %rsi
	0x48, 0x31, 0xf7, //0x00002cf7 xorq         %rsi, %rdi
	0x48, 0x21, 0xf0, //0x00002cfa andq         %rsi, %rax
	0x48, 0x21, 0xf8, //0x00002cfd andq         %rdi, %rax
	0x45, 0x31, 0xc0, //0x00002d00 xorl         %r8d, %r8d
	0x48, 0x01, 0xc8, //0x00002d03 addq         %rcx, %rax
	0x41, 0x0f, 0x92, 0xc0, //0x00002d06 setb         %r8b
	0x48, 0x01, 0xc0, //0x00002d0a addq         %rax, %rax
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002d0d movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xc8, //0x00002d17 xorq         %rcx, %rax
	0x48, 0x21, 0xd8, //0x00002d1a andq         %rbx, %rax
	0x48, 0xf7, 0xd0, //0x00002d1d notq         %rax
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00002d20 jmp          LBB0_494
	//0x00002d25 LBB0_493
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002d25 movq         $-1, %rax
	0x45, 0x31, 0xc0, //0x00002d2c xorl         %r8d, %r8d
	//0x00002d2f LBB0_494
	0x4c, 0x21, 0xf0, //0x00002d2f andq         %r14, %rax
	0x66, 0x48, 0x0f, 0x6e, 0xd8, //0x00002d32 movq         %rax, %xmm3
	0x66, 0x41, 0x0f, 0x3a, 0x44, 0xd9, 0x00, //0x00002d37 pclmulqdq    $0, %xmm9, %xmm3
	0x66, 0x49, 0x0f, 0x7e, 0xde, //0x00002d3e movq         %xmm3, %r14
	0x4d, 0x31, 0xce, //0x00002d43 xorq         %r9, %r14
	0x66, 0x0f, 0x6f, 0xda, //0x00002d46 movdqa       %xmm2, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xda, //0x00002d4a pcmpeqb      %xmm10, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xd3, //0x00002d4f pmovmskb     %xmm3, %r10d
	0x66, 0x0f, 0x6f, 0xdd, //0x00002d54 movdqa       %xmm5, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xda, //0x00002d58 pcmpeqb      %xmm10, %xmm3
	0x66, 0x0f, 0xd7, 0xc3, //0x00002d5d pmovmskb     %xmm3, %eax
	0x66, 0x0f, 0x6f, 0xdf, //0x00002d61 movdqa       %xmm7, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xda, //0x00002d65 pcmpeqb      %xmm10, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x00002d6a pmovmskb     %xmm3, %ecx
	0x66, 0x0f, 0x6f, 0xde, //0x00002d6e movdqa       %xmm6, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xda, //0x00002d72 pcmpeqb      %xmm10, %xmm3
	0x66, 0x0f, 0xd7, 0xdb, //0x00002d77 pmovmskb     %xmm3, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x00002d7b shlq         $48, %rbx
	0x48, 0xc1, 0xe1, 0x20, //0x00002d7f shlq         $32, %rcx
	0x48, 0xc1, 0xe0, 0x10, //0x00002d83 shlq         $16, %rax
	0x49, 0x09, 0xc2, //0x00002d87 orq          %rax, %r10
	0x49, 0x09, 0xca, //0x00002d8a orq          %rcx, %r10
	0x49, 0x09, 0xda, //0x00002d8d orq          %rbx, %r10
	0x4d, 0x89, 0xf1, //0x00002d90 movq         %r14, %r9
	0x49, 0xf7, 0xd1, //0x00002d93 notq         %r9
	0x4d, 0x21, 0xca, //0x00002d96 andq         %r9, %r10
	0x66, 0x41, 0x0f, 0x74, 0xd3, //0x00002d99 pcmpeqb      %xmm11, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00002d9e pmovmskb     %xmm2, %eax
	0x66, 0x41, 0x0f, 0x74, 0xeb, //0x00002da2 pcmpeqb      %xmm11, %xmm5
	0x66, 0x0f, 0xd7, 0xdd, //0x00002da7 pmovmskb     %xmm5, %ebx
	0x66, 0x41, 0x0f, 0x74, 0xfb, //0x00002dab pcmpeqb      %xmm11, %xmm7
	0x66, 0x0f, 0xd7, 0xf7, //0x00002db0 pmovmskb     %xmm7, %esi
	0x66, 0x41, 0x0f, 0x74, 0xf3, //0x00002db4 pcmpeqb      %xmm11, %xmm6
	0x66, 0x0f, 0xd7, 0xce, //0x00002db9 pmovmskb     %xmm6, %ecx
	0x48, 0xc1, 0xe1, 0x30, //0x00002dbd shlq         $48, %rcx
	0x48, 0xc1, 0xe6, 0x20, //0x00002dc1 shlq         $32, %rsi
	0x48, 0xc1, 0xe3, 0x10, //0x00002dc5 shlq         $16, %rbx
	0x48, 0x09, 0xd8, //0x00002dc9 orq          %rbx, %rax
	0x48, 0x09, 0xf0, //0x00002dcc orq          %rsi, %rax
	0x48, 0x09, 0xc8, //0x00002dcf orq          %rcx, %rax
	0x48, 0xbf, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x00002dd2 movabsq      $3689348814741910323, %rdi
	0x4c, 0x21, 0xc8, //0x00002ddc andq         %r9, %rax
	0x0f, 0x84, 0x77, 0x00, 0x00, 0x00, //0x00002ddf je           LBB0_498
	0x49, 0xb9, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00002de5 movabsq      $1085102592571150095, %r9
	0x90, //0x00002def .p2align 4, 0x90
	//0x00002df0 LBB0_496
	0x48, 0x8d, 0x58, 0xff, //0x00002df0 leaq         $-1(%rax), %rbx
	0x48, 0x89, 0xd9, //0x00002df4 movq         %rbx, %rcx
	0x4c, 0x21, 0xd1, //0x00002df7 andq         %r10, %rcx
	0x48, 0x89, 0xce, //0x00002dfa movq         %rcx, %rsi
	0x48, 0xd1, 0xee, //0x00002dfd shrq         %rsi
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002e00 movabsq      $6148914691236517205, %rdx
	0x48, 0x21, 0xd6, //0x00002e0a andq         %rdx, %rsi
	0x48, 0x29, 0xf1, //0x00002e0d subq         %rsi, %rcx
	0x48, 0x89, 0xce, //0x00002e10 movq         %rcx, %rsi
	0x48, 0x21, 0xfe, //0x00002e13 andq         %rdi, %rsi
	0x48, 0xc1, 0xe9, 0x02, //0x00002e16 shrq         $2, %rcx
	0x48, 0x21, 0xf9, //0x00002e1a andq         %rdi, %rcx
	0x48, 0x01, 0xf1, //0x00002e1d addq         %rsi, %rcx
	0x48, 0x89, 0xce, //0x00002e20 movq         %rcx, %rsi
	0x48, 0xc1, 0xee, 0x04, //0x00002e23 shrq         $4, %rsi
	0x48, 0x01, 0xce, //0x00002e27 addq         %rcx, %rsi
	0x4c, 0x21, 0xce, //0x00002e2a andq         %r9, %rsi
	0x48, 0xb9, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00002e2d movabsq      $72340172838076673, %rcx
	0x48, 0x0f, 0xaf, 0xf1, //0x00002e37 imulq        %rcx, %rsi
	0x48, 0xc1, 0xee, 0x38, //0x00002e3b shrq         $56, %rsi
	0x4c, 0x01, 0xde, //0x00002e3f addq         %r11, %rsi
	0x4c, 0x39, 0xee, //0x00002e42 cmpq         %r13, %rsi
	0x0f, 0x86, 0x88, 0x00, 0x00, 0x00, //0x00002e45 jbe          LBB0_515
	0x49, 0xff, 0xc5, //0x00002e4b incq         %r13
	0x48, 0x21, 0xd8, //0x00002e4e andq         %rbx, %rax
	0x0f, 0x85, 0x99, 0xff, 0xff, 0xff, //0x00002e51 jne          LBB0_496
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00002e57 jmp          LBB0_499
	//0x00002e5c LBB0_498
	0x49, 0xb9, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00002e5c movabsq      $1085102592571150095, %r9
	//0x00002e66 LBB0_499
	0x49, 0xc1, 0xfe, 0x3f, //0x00002e66 sarq         $63, %r14
	0x4c, 0x89, 0xd0, //0x00002e6a movq         %r10, %rax
	0x48, 0xd1, 0xe8, //0x00002e6d shrq         %rax
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002e70 movabsq      $6148914691236517205, %rcx
	0x48, 0x21, 0xc8, //0x00002e7a andq         %rcx, %rax
	0x49, 0x29, 0xc2, //0x00002e7d subq         %rax, %r10
	0x4c, 0x89, 0xd0, //0x00002e80 movq         %r10, %rax
	0x48, 0x21, 0xf8, //0x00002e83 andq         %rdi, %rax
	0x49, 0xc1, 0xea, 0x02, //0x00002e86 shrq         $2, %r10
	0x49, 0x21, 0xfa, //0x00002e8a andq         %rdi, %r10
	0x49, 0x01, 0xc2, //0x00002e8d addq         %rax, %r10
	0x4c, 0x89, 0xd0, //0x00002e90 movq         %r10, %rax
	0x48, 0xc1, 0xe8, 0x04, //0x00002e93 shrq         $4, %rax
	0x4c, 0x01, 0xd0, //0x00002e97 addq         %r10, %rax
	0x4c, 0x21, 0xc8, //0x00002e9a andq         %r9, %rax
	0x48, 0xb9, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00002e9d movabsq      $72340172838076673, %rcx
	0x48, 0x0f, 0xaf, 0xc1, //0x00002ea7 imulq        %rcx, %rax
	0x48, 0xc1, 0xe8, 0x38, //0x00002eab shrq         $56, %rax
	0x49, 0x01, 0xc3, //0x00002eaf addq         %rax, %r11
	0x48, 0x8b, 0x7d, 0xc8, //0x00002eb2 movq         $-56(%rbp), %rdi
	0x48, 0x83, 0xc7, 0x40, //0x00002eb6 addq         $64, %rdi
	0x4c, 0x89, 0xf9, //0x00002eba movq         %r15, %rcx
	0x48, 0x83, 0xc1, 0xc0, //0x00002ebd addq         $-64, %rcx
	0x4d, 0x89, 0xf1, //0x00002ec1 movq         %r14, %r9
	0x48, 0x83, 0xf9, 0x40, //0x00002ec4 cmpq         $64, %rcx
	0x0f, 0x8d, 0x5b, 0xfd, 0xff, 0xff, //0x00002ec8 jge          LBB0_490
	0xe9, 0xf3, 0xfb, 0xff, 0xff, //0x00002ece jmp          LBB0_500
	//0x00002ed3 LBB0_515
	0x4c, 0x8b, 0x45, 0xb0, //0x00002ed3 movq         $-80(%rbp), %r8
	0x49, 0x8b, 0x08, //0x00002ed7 movq         (%r8), %rcx
	0x48, 0x0f, 0xbc, 0xc0, //0x00002eda bsfq         %rax, %rax
	0x4c, 0x29, 0xf8, //0x00002ede subq         %r15, %rax
	0x4c, 0x8d, 0x74, 0x08, 0x01, //0x00002ee1 leaq         $1(%rax,%rcx), %r14
	0x4c, 0x8b, 0x5d, 0xd0, //0x00002ee6 movq         $-48(%rbp), %r11
	0x4d, 0x89, 0x33, //0x00002eea movq         %r14, (%r11)
	0x49, 0x8b, 0x00, //0x00002eed movq         (%r8), %rax
	0x49, 0x39, 0xc6, //0x00002ef0 cmpq         %rax, %r14
	0x4c, 0x0f, 0x47, 0xf0, //0x00002ef3 cmovaq       %rax, %r14
	0x4d, 0x89, 0x33, //0x00002ef7 movq         %r14, (%r11)
	0x49, 0xba, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002efa movabsq      $4294977024, %r10
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002f04 movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x4d, 0xa0, //0x00002f08 movq         $-96(%rbp), %r9
	0xe9, 0x7f, 0xf4, 0xff, 0xff, //0x00002f0c jmp          LBB0_419
	//0x00002f11 LBB0_458
	0x4d, 0x85, 0xd2, //0x00002f11 testq        %r10, %r10
	0x0f, 0x85, 0xad, 0x00, 0x00, 0x00, //0x00002f14 jne          LBB0_523
	0x4a, 0x8d, 0x4c, 0x08, 0x01, //0x00002f1a leaq         $1(%rax,%r9), %rcx
	0x48, 0xf7, 0xd0, //0x00002f1f notq         %rax
	0x4c, 0x01, 0xc0, //0x00002f22 addq         %r8, %rax
	//0x00002f25 LBB0_460
	0x49, 0xba, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002f25 movabsq      $4294977024, %r10
	0x48, 0x85, 0xc0, //0x00002f2f testq        %rax, %rax
	0x4c, 0x8b, 0x45, 0xb0, //0x00002f32 movq         $-80(%rbp), %r8
	0x4c, 0x8b, 0x4d, 0xa0, //0x00002f36 movq         $-96(%rbp), %r9
	0x0f, 0x8f, 0x1d, 0x00, 0x00, 0x00, //0x00002f3a jg           LBB0_517
	0xe9, 0x4b, 0xf4, 0xff, 0xff, //0x00002f40 jmp          LBB0_419
	//0x00002f45 LBB0_516
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x00002f45 movq         $-2, %rdx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x00002f4c movl         $2, %esi
	0x48, 0x01, 0xf1, //0x00002f51 addq         %rsi, %rcx
	0x48, 0x01, 0xd0, //0x00002f54 addq         %rdx, %rax
	0x0f, 0x8e, 0x33, 0xf4, 0xff, 0xff, //0x00002f57 jle          LBB0_419
	//0x00002f5d LBB0_517
	0x0f, 0xb6, 0x11, //0x00002f5d movzbl       (%rcx), %edx
	0x80, 0xfa, 0x5c, //0x00002f60 cmpb         $92, %dl
	0x0f, 0x84, 0xdc, 0xff, 0xff, 0xff, //0x00002f63 je           LBB0_516
	0x80, 0xfa, 0x22, //0x00002f69 cmpb         $34, %dl
	0x0f, 0x84, 0x1d, 0x00, 0x00, 0x00, //0x00002f6c je           LBB0_520
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00002f72 movq         $-1, %rdx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00002f79 movl         $1, %esi
	0x48, 0x01, 0xf1, //0x00002f7e addq         %rsi, %rcx
	0x48, 0x01, 0xd0, //0x00002f81 addq         %rdx, %rax
	0x0f, 0x8f, 0xd3, 0xff, 0xff, 0xff, //0x00002f84 jg           LBB0_517
	0xe9, 0x01, 0xf4, 0xff, 0xff, //0x00002f8a jmp          LBB0_419
	//0x00002f8f LBB0_520
	0x48, 0x29, 0xf9, //0x00002f8f subq         %rdi, %rcx
	0x48, 0xff, 0xc1, //0x00002f92 incq         %rcx
	0xe9, 0xd2, 0xf6, 0xff, 0xff, //0x00002f95 jmp          LBB0_462
	//0x00002f9a LBB0_521
	0x4a, 0x8d, 0x0c, 0x37, //0x00002f9a leaq         (%rdi,%r14), %rcx
	0xe9, 0x82, 0xff, 0xff, 0xff, //0x00002f9e jmp          LBB0_460
	//0x00002fa3 LBB0_522
	0x4c, 0x8b, 0x45, 0xb0, //0x00002fa3 movq         $-80(%rbp), %r8
	0x4d, 0x8b, 0x30, //0x00002fa7 movq         (%r8), %r14
	0x4c, 0x8b, 0x5d, 0xd0, //0x00002faa movq         $-48(%rbp), %r11
	0x4d, 0x89, 0x33, //0x00002fae movq         %r14, (%r11)
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002fb1 movq         $-64(%rbp), %r13
	0x49, 0x89, 0xc1, //0x00002fb5 movq         %rax, %r9
	0x49, 0xba, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002fb8 movabsq      $4294977024, %r10
	0xe9, 0xc9, 0xf3, 0xff, 0xff, //0x00002fc2 jmp          LBB0_419
	//0x00002fc7 LBB0_523
	0x48, 0x8b, 0x55, 0xa0, //0x00002fc7 movq         $-96(%rbp), %rdx
	0x49, 0x8d, 0x48, 0xff, //0x00002fcb leaq         $-1(%r8), %rcx
	0x48, 0x39, 0xc1, //0x00002fcf cmpq         %rax, %rcx
	0x0f, 0x85, 0x1e, 0x00, 0x00, 0x00, //0x00002fd2 jne          LBB0_525
	0x4c, 0x8b, 0x5d, 0xd0, //0x00002fd8 movq         $-48(%rbp), %r11
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002fdc movq         $-64(%rbp), %r13
	0x49, 0x89, 0xd1, //0x00002fe0 movq         %rdx, %r9
	0x4c, 0x8b, 0x45, 0xb0, //0x00002fe3 movq         $-80(%rbp), %r8
	0x49, 0xba, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002fe7 movabsq      $4294977024, %r10
	0xe9, 0x9a, 0xf3, 0xff, 0xff, //0x00002ff1 jmp          LBB0_419
	//0x00002ff6 LBB0_525
	0x4a, 0x8d, 0x4c, 0x08, 0x02, //0x00002ff6 leaq         $2(%rax,%r9), %rcx
	0x49, 0x29, 0xc0, //0x00002ffb subq         %rax, %r8
	0x49, 0x83, 0xc0, 0xfe, //0x00002ffe addq         $-2, %r8
	0x4c, 0x89, 0xc0, //0x00003002 movq         %r8, %rax
	0x4c, 0x8b, 0x5d, 0xd0, //0x00003005 movq         $-48(%rbp), %r11
	0x4c, 0x8b, 0x6d, 0xc0, //0x00003009 movq         $-64(%rbp), %r13
	0xe9, 0x13, 0xff, 0xff, 0xff, //0x0000300d jmp          LBB0_460
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003012 .p2align 4, 0x90
	//0x00003020 LBB0_447
	0x49, 0x83, 0xc1, 0x10, //0x00003020 addq         $16, %r9
	0x4c, 0x89, 0xf0, //0x00003024 movq         %r14, %rax
	0x4c, 0x3b, 0x8d, 0x30, 0xff, 0xff, 0xff, //0x00003027 cmpq         $-208(%rbp), %r9
	0x4c, 0x8b, 0x75, 0xa8, //0x0000302e movq         $-88(%rbp), %r14
	0x0f, 0x85, 0x57, 0xd1, 0xff, 0xff, //0x00003032 jne          LBB0_2
	//0x00003038 LBB0_448
	0x4d, 0x85, 0xf6, //0x00003038 testq        %r14, %r14
	0x0f, 0x84, 0x93, 0x00, 0x00, 0x00, //0x0000303b je           LBB0_526
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00003041 movl         $1, %edx
	0x66, 0x48, 0x0f, 0x6e, 0xc2, //0x00003046 movq         %rdx, %xmm0
	0xf3, 0x41, 0x0f, 0x7f, 0x06, //0x0000304b movdqu       %xmm0, (%r14)
	0x4d, 0x8b, 0x7d, 0x00, //0x00003050 movq         (%r13), %r15
	0x4c, 0x89, 0xf8, //0x00003054 movq         %r15, %rax
	0x48, 0xf7, 0xd0, //0x00003057 notq         %rax
	0x48, 0x89, 0x45, 0xb0, //0x0000305a movq         %rax, $-80(%rbp)
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x0000305e movl         $1, %eax
	0x4c, 0x29, 0xf8, //0x00003063 subq         %r15, %rax
	0x48, 0x89, 0x45, 0xa0, //0x00003066 movq         %rax, $-96(%rbp)
	0x4d, 0x8b, 0x13, //0x0000306a movq         (%r11), %r10
	0x49, 0x8d, 0x47, 0x05, //0x0000306d leaq         $5(%r15), %rax
	0x48, 0x89, 0x45, 0x90, //0x00003071 movq         %rax, $-112(%rbp)
	0x48, 0xc7, 0x45, 0x98, 0xff, 0xff, 0xff, 0xff, //0x00003075 movq         $-1, $-104(%rbp)
	0xf3, 0x0f, 0x6f, 0x05, 0xab, 0xcf, 0xff, 0xff, //0x0000307d movdqu       $-12373(%rip), %xmm0  /* LCPI0_3+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0xb3, 0xcf, 0xff, 0xff, //0x00003085 movdqu       $-12365(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x05, 0xea, 0xcf, 0xff, 0xff, //0x0000308d movdqu       $-12310(%rip), %xmm8  /* LCPI0_8+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x1d, 0xf2, 0xcf, 0xff, 0xff, //0x00003096 movdqu       $-12302(%rip), %xmm3  /* LCPI0_9+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x0d, 0xf9, 0xcf, 0xff, 0xff, //0x0000309e movdqu       $-12295(%rip), %xmm9  /* LCPI0_10+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x15, 0x00, 0xd0, 0xff, 0xff, //0x000030a7 movdqu       $-12288(%rip), %xmm10  /* LCPI0_11+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x1d, 0x57, 0xcf, 0xff, 0xff, //0x000030b0 movdqu       $-12457(%rip), %xmm11  /* LCPI0_1+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x25, 0xfe, 0xcf, 0xff, 0xff, //0x000030b9 movdqu       $-12290(%rip), %xmm12  /* LCPI0_12+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x2d, 0x05, 0xd0, 0xff, 0xff, //0x000030c2 movdqu       $-12283(%rip), %xmm13  /* LCPI0_13+0(%rip) */
	0x4c, 0x89, 0x7d, 0xb8, //0x000030cb movq         %r15, $-72(%rbp)
	0xe9, 0x2f, 0x03, 0x00, 0x00, //0x000030cf jmp          LBB0_586
	//0x000030d4 LBB0_526
	0x4d, 0x8b, 0x45, 0x00, //0x000030d4 movq         (%r13), %r8
	0x49, 0x8b, 0x75, 0x08, //0x000030d8 movq         $8(%r13), %rsi
	0x49, 0x8b, 0x13, //0x000030dc movq         (%r11), %rdx
	0x48, 0x89, 0xd1, //0x000030df movq         %rdx, %rcx
	0x48, 0x29, 0xf1, //0x000030e2 subq         %rsi, %rcx
	0x0f, 0x83, 0x26, 0x00, 0x00, 0x00, //0x000030e5 jae          LBB0_531
	0x41, 0x8a, 0x04, 0x10, //0x000030eb movb         (%r8,%rdx), %al
	0x3c, 0x0d, //0x000030ef cmpb         $13, %al
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x000030f1 je           LBB0_531
	0x3c, 0x20, //0x000030f7 cmpb         $32, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x000030f9 je           LBB0_531
	0x04, 0xf7, //0x000030ff addb         $-9, %al
	0x3c, 0x01, //0x00003101 cmpb         $1, %al
	0x0f, 0x86, 0x08, 0x00, 0x00, 0x00, //0x00003103 jbe          LBB0_531
	0x49, 0x89, 0xd4, //0x00003109 movq         %rdx, %r12
	0xe9, 0x8a, 0x01, 0x00, 0x00, //0x0000310c jmp          LBB0_562
	//0x00003111 LBB0_531
	0x4c, 0x8d, 0x62, 0x01, //0x00003111 leaq         $1(%rdx), %r12
	0x49, 0x39, 0xf4, //0x00003115 cmpq         %rsi, %r12
	0x0f, 0x83, 0x1e, 0x00, 0x00, 0x00, //0x00003118 jae          LBB0_535
	0x43, 0x8a, 0x04, 0x20, //0x0000311e movb         (%r8,%r12), %al
	0x3c, 0x0d, //0x00003122 cmpb         $13, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00003124 je           LBB0_535
	0x3c, 0x20, //0x0000312a cmpb         $32, %al
	0x0f, 0x84, 0x0a, 0x00, 0x00, 0x00, //0x0000312c je           LBB0_535
	0x04, 0xf7, //0x00003132 addb         $-9, %al
	0x3c, 0x01, //0x00003134 cmpb         $1, %al
	0x0f, 0x87, 0x5f, 0x01, 0x00, 0x00, //0x00003136 ja           LBB0_562
	//0x0000313c LBB0_535
	0x4c, 0x8d, 0x62, 0x02, //0x0000313c leaq         $2(%rdx), %r12
	0x49, 0x39, 0xf4, //0x00003140 cmpq         %rsi, %r12
	0x0f, 0x83, 0x1e, 0x00, 0x00, 0x00, //0x00003143 jae          LBB0_539
	0x43, 0x8a, 0x04, 0x20, //0x00003149 movb         (%r8,%r12), %al
	0x3c, 0x0d, //0x0000314d cmpb         $13, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x0000314f je           LBB0_539
	0x3c, 0x20, //0x00003155 cmpb         $32, %al
	0x0f, 0x84, 0x0a, 0x00, 0x00, 0x00, //0x00003157 je           LBB0_539
	0x04, 0xf7, //0x0000315d addb         $-9, %al
	0x3c, 0x01, //0x0000315f cmpb         $1, %al
	0x0f, 0x87, 0x34, 0x01, 0x00, 0x00, //0x00003161 ja           LBB0_562
	//0x00003167 LBB0_539
	0x4c, 0x8d, 0x62, 0x03, //0x00003167 leaq         $3(%rdx), %r12
	0x49, 0x39, 0xf4, //0x0000316b cmpq         %rsi, %r12
	0x0f, 0x83, 0x1e, 0x00, 0x00, 0x00, //0x0000316e jae          LBB0_543
	0x43, 0x8a, 0x04, 0x20, //0x00003174 movb         (%r8,%r12), %al
	0x3c, 0x0d, //0x00003178 cmpb         $13, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x0000317a je           LBB0_543
	0x3c, 0x20, //0x00003180 cmpb         $32, %al
	0x0f, 0x84, 0x0a, 0x00, 0x00, 0x00, //0x00003182 je           LBB0_543
	0x04, 0xf7, //0x00003188 addb         $-9, %al
	0x3c, 0x01, //0x0000318a cmpb         $1, %al
	0x0f, 0x87, 0x09, 0x01, 0x00, 0x00, //0x0000318c ja           LBB0_562
	//0x00003192 LBB0_543
	0x48, 0x8d, 0x7a, 0x04, //0x00003192 leaq         $4(%rdx), %rdi
	0x48, 0x39, 0xfe, //0x00003196 cmpq         %rdi, %rsi
	0x0f, 0x86, 0xf0, 0x18, 0x00, 0x00, //0x00003199 jbe          LBB0_869
	0x48, 0x39, 0xfe, //0x0000319f cmpq         %rdi, %rsi
	0x0f, 0x84, 0xbc, 0x00, 0x00, 0x00, //0x000031a2 je           LBB0_559
	0x49, 0x8d, 0x3c, 0x30, //0x000031a8 leaq         (%r8,%rsi), %rdi
	0x48, 0x83, 0xc1, 0x04, //0x000031ac addq         $4, %rcx
	0x4e, 0x8d, 0x64, 0x02, 0x05, //0x000031b0 leaq         $5(%rdx,%r8), %r12
	0x48, 0xb8, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000031b5 movabsq      $4294977024, %rax
	0x90, //0x000031bf .p2align 4, 0x90
	//0x000031c0 LBB0_546
	0x41, 0x0f, 0xbe, 0x54, 0x24, 0xff, //0x000031c0 movsbl       $-1(%r12), %edx
	0x83, 0xfa, 0x20, //0x000031c6 cmpl         $32, %edx
	0x0f, 0x87, 0xb3, 0x00, 0x00, 0x00, //0x000031c9 ja           LBB0_561
	0x48, 0x0f, 0xa3, 0xd0, //0x000031cf btq          %rdx, %rax
	0x0f, 0x83, 0xa9, 0x00, 0x00, 0x00, //0x000031d3 jae          LBB0_561
	0x49, 0xff, 0xc4, //0x000031d9 incq         %r12
	0x48, 0xff, 0xc1, //0x000031dc incq         %rcx
	0x0f, 0x85, 0xdb, 0xff, 0xff, 0xff, //0x000031df jne          LBB0_546
	0xe9, 0x7d, 0x00, 0x00, 0x00, //0x000031e5 jmp          LBB0_560
	//0x000031ea LBB0_549
	0x49, 0x89, 0x33, //0x000031ea movq         %rsi, (%r11)
	0x49, 0x89, 0xf6, //0x000031ed movq         %rsi, %r14
	0xe9, 0x4d, 0x00, 0x00, 0x00, //0x000031f0 jmp          LBB0_556
	//0x000031f5 LBB0_550
	0x3c, 0x5d, //0x000031f5 cmpb         $93, %al
	0x0f, 0x84, 0x2d, 0x00, 0x00, 0x00, //0x000031f7 je           LBB0_554
	0xe9, 0x40, 0x00, 0x00, 0x00, //0x000031fd jmp          LBB0_556
	//0x00003202 LBB0_552
	0x49, 0xff, 0xce, //0x00003202 decq         %r14
	0x4d, 0x89, 0x33, //0x00003205 movq         %r14, (%r11)
	0x48, 0xc7, 0xc1, 0xde, 0xff, 0xff, 0xff, //0x00003208 movq         $-34, %rcx
	0xe9, 0x3b, 0x00, 0x00, 0x00, //0x0000320f jmp          LBB0_558
	//0x00003214 LBB0_553
	0x80, 0xf9, 0x7d, //0x00003214 cmpb         $125, %cl
	0x0f, 0x85, 0x25, 0x00, 0x00, 0x00, //0x00003217 jne          LBB0_556
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x0000321d jmp          LBB0_554
	//0x00003222 LBB0_251
	0x3c, 0x7d, //0x00003222 cmpb         $125, %al
	0x0f, 0x85, 0x18, 0x00, 0x00, 0x00, //0x00003224 jne          LBB0_556
	//0x0000322a LBB0_554
	0x49, 0xff, 0xce, //0x0000322a decq         %r14
	0x4d, 0x89, 0x33, //0x0000322d movq         %r14, (%r11)
	0x48, 0xc7, 0xc1, 0xdf, 0xff, 0xff, 0xff, //0x00003230 movq         $-33, %rcx
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x00003237 jmp          LBB0_558
	//0x0000323c LBB0_555
	0x49, 0x89, 0x3b, //0x0000323c movq         %rdi, (%r11)
	0x49, 0x89, 0xfe, //0x0000323f movq         %rdi, %r14
	//0x00003242 LBB0_556
	0x49, 0xff, 0xce, //0x00003242 decq         %r14
	0x4d, 0x89, 0x33, //0x00003245 movq         %r14, (%r11)
	//0x00003248 LBB0_557
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00003248 movq         $-2, %rcx
	//0x0000324f LBB0_558
	0x48, 0x89, 0xc8, //0x0000324f movq         %rcx, %rax
	0x48, 0x81, 0xc4, 0xa8, 0x00, 0x00, 0x00, //0x00003252 addq         $168, %rsp
	0x5b, //0x00003259 popq         %rbx
	0x41, 0x5c, //0x0000325a popq         %r12
	0x41, 0x5d, //0x0000325c popq         %r13
	0x41, 0x5e, //0x0000325e popq         %r14
	0x41, 0x5f, //0x00003260 popq         %r15
	0x5d, //0x00003262 popq         %rbp
	0xc3, //0x00003263 retq         
	//0x00003264 LBB0_559
	0x4c, 0x01, 0xc7, //0x00003264 addq         %r8, %rdi
	//0x00003267 LBB0_560
	0x4c, 0x29, 0xc7, //0x00003267 subq         %r8, %rdi
	0x49, 0x89, 0xfc, //0x0000326a movq         %rdi, %r12
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000326d movq         $-1, %rcx
	0x49, 0x39, 0xf4, //0x00003274 cmpq         %rsi, %r12
	0x0f, 0x83, 0xd2, 0xff, 0xff, 0xff, //0x00003277 jae          LBB0_558
	0xe9, 0x19, 0x00, 0x00, 0x00, //0x0000327d jmp          LBB0_562
	//0x00003282 LBB0_561
	0x4c, 0x89, 0xc0, //0x00003282 movq         %r8, %rax
	0x48, 0xf7, 0xd0, //0x00003285 notq         %rax
	0x49, 0x01, 0xc4, //0x00003288 addq         %rax, %r12
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000328b movq         $-1, %rcx
	0x49, 0x39, 0xf4, //0x00003292 cmpq         %rsi, %r12
	0x0f, 0x83, 0xb4, 0xff, 0xff, 0xff, //0x00003295 jae          LBB0_558
	//0x0000329b LBB0_562
	0x49, 0x8d, 0x7c, 0x24, 0x01, //0x0000329b leaq         $1(%r12), %rdi
	0x49, 0x89, 0x3b, //0x000032a0 movq         %rdi, (%r11)
	0x43, 0x0f, 0xbe, 0x04, 0x20, //0x000032a3 movsbl       (%r8,%r12), %eax
	0x83, 0xf8, 0x7b, //0x000032a8 cmpl         $123, %eax
	0x0f, 0x87, 0x2e, 0x18, 0x00, 0x00, //0x000032ab ja           LBB0_876
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000032b1 movq         $-1, %rcx
	0x48, 0x8d, 0x15, 0x79, 0x2c, 0x00, 0x00, //0x000032b8 leaq         $11385(%rip), %rdx  /* LJTI0_6+0(%rip) */
	0x48, 0x63, 0x04, 0x82, //0x000032bf movslq       (%rdx,%rax,4), %rax
	0x48, 0x01, 0xd0, //0x000032c3 addq         %rdx, %rax
	0xff, 0xe0, //0x000032c6 jmpq         *%rax
	//0x000032c8 LBB0_564
	0x49, 0x8b, 0x55, 0x08, //0x000032c8 movq         $8(%r13), %rdx
	0x48, 0x89, 0xd1, //0x000032cc movq         %rdx, %rcx
	0x48, 0x29, 0xf9, //0x000032cf subq         %rdi, %rcx
	0x4c, 0x01, 0xc7, //0x000032d2 addq         %r8, %rdi
	0x48, 0x83, 0xf9, 0x10, //0x000032d5 cmpq         $16, %rcx
	0x0f, 0x82, 0x6e, 0x00, 0x00, 0x00, //0x000032d9 jb           LBB0_569
	0x4c, 0x29, 0xe2, //0x000032df subq         %r12, %rdx
	0x48, 0x83, 0xc2, 0xef, //0x000032e2 addq         $-17, %rdx
	0x48, 0x89, 0xd0, //0x000032e6 movq         %rdx, %rax
	0x48, 0x83, 0xe0, 0xf0, //0x000032e9 andq         $-16, %rax
	0x4c, 0x01, 0xe0, //0x000032ed addq         %r12, %rax
	0x49, 0x8d, 0x74, 0x00, 0x11, //0x000032f0 leaq         $17(%r8,%rax), %rsi
	0x83, 0xe2, 0x0f, //0x000032f5 andl         $15, %edx
	0xf3, 0x0f, 0x6f, 0x05, 0x00, 0xcd, 0xff, 0xff, //0x000032f8 movdqu       $-13056(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0x08, 0xcd, 0xff, 0xff, //0x00003300 movdqu       $-13048(%rip), %xmm1  /* LCPI0_1+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x15, 0x10, 0xcd, 0xff, 0xff, //0x00003308 movdqu       $-13040(%rip), %xmm2  /* LCPI0_2+0(%rip) */
	//0x00003310 .p2align 4, 0x90
	//0x00003310 LBB0_566
	0xf3, 0x0f, 0x6f, 0x1f, //0x00003310 movdqu       (%rdi), %xmm3
	0x66, 0x0f, 0x6f, 0xe3, //0x00003314 movdqa       %xmm3, %xmm4
	0x66, 0x0f, 0x74, 0xe0, //0x00003318 pcmpeqb      %xmm0, %xmm4
	0x66, 0x0f, 0xeb, 0xd9, //0x0000331c por          %xmm1, %xmm3
	0x66, 0x0f, 0x74, 0xda, //0x00003320 pcmpeqb      %xmm2, %xmm3
	0x66, 0x0f, 0xeb, 0xdc, //0x00003324 por          %xmm4, %xmm3
	0x66, 0x0f, 0xd7, 0xc3, //0x00003328 pmovmskb     %xmm3, %eax
	0x66, 0x85, 0xc0, //0x0000332c testw        %ax, %ax
	0x0f, 0x85, 0x64, 0x00, 0x00, 0x00, //0x0000332f jne          LBB0_577
	0x48, 0x83, 0xc7, 0x10, //0x00003335 addq         $16, %rdi
	0x48, 0x83, 0xc1, 0xf0, //0x00003339 addq         $-16, %rcx
	0x48, 0x83, 0xf9, 0x0f, //0x0000333d cmpq         $15, %rcx
	0x0f, 0x87, 0xc9, 0xff, 0xff, 0xff, //0x00003341 ja           LBB0_566
	0x48, 0x89, 0xd1, //0x00003347 movq         %rdx, %rcx
	0x48, 0x89, 0xf7, //0x0000334a movq         %rsi, %rdi
	//0x0000334d LBB0_569
	0x48, 0x85, 0xc9, //0x0000334d testq        %rcx, %rcx
	0x0f, 0x84, 0x31, 0x00, 0x00, 0x00, //0x00003350 je           LBB0_576
	0x48, 0x8d, 0x04, 0x0f, //0x00003356 leaq         (%rdi,%rcx), %rax
	//0x0000335a LBB0_571
	0x0f, 0xb6, 0x17, //0x0000335a movzbl       (%rdi), %edx
	0x80, 0xfa, 0x2c, //0x0000335d cmpb         $44, %dl
	0x0f, 0x84, 0xa8, 0x21, 0x00, 0x00, //0x00003360 je           LBB0_952
	0x80, 0xfa, 0x7d, //0x00003366 cmpb         $125, %dl
	0x0f, 0x84, 0x9f, 0x21, 0x00, 0x00, //0x00003369 je           LBB0_952
	0x80, 0xfa, 0x5d, //0x0000336f cmpb         $93, %dl
	0x0f, 0x84, 0x96, 0x21, 0x00, 0x00, //0x00003372 je           LBB0_952
	0x48, 0xff, 0xc7, //0x00003378 incq         %rdi
	0x48, 0xff, 0xc9, //0x0000337b decq         %rcx
	0x0f, 0x85, 0xd6, 0xff, 0xff, 0xff, //0x0000337e jne          LBB0_571
	0x48, 0x89, 0xc7, //0x00003384 movq         %rax, %rdi
	//0x00003387 LBB0_576
	0x4c, 0x29, 0xc7, //0x00003387 subq         %r8, %rdi
	0x48, 0x8b, 0x45, 0xd0, //0x0000338a movq         $-48(%rbp), %rax
	0x48, 0x89, 0x38, //0x0000338e movq         %rdi, (%rax)
	0x4c, 0x89, 0xe1, //0x00003391 movq         %r12, %rcx
	0xe9, 0xb6, 0xfe, 0xff, 0xff, //0x00003394 jmp          LBB0_558
	//0x00003399 LBB0_577
	0x0f, 0xb7, 0xc0, //0x00003399 movzwl       %ax, %eax
	0x48, 0x0f, 0xbc, 0xc0, //0x0000339c bsfq         %rax, %rax
	0x4c, 0x29, 0xc7, //0x000033a0 subq         %r8, %rdi
	0x48, 0x01, 0xc7, //0x000033a3 addq         %rax, %rdi
	//0x000033a6 LBB0_578
	0x49, 0x89, 0x3b, //0x000033a6 movq         %rdi, (%r11)
	0x4c, 0x89, 0xe1, //0x000033a9 movq         %r12, %rcx
	0xe9, 0x9e, 0xfe, 0xff, 0xff, //0x000033ac jmp          LBB0_558
	//0x000033b1 LBB0_579
	0x48, 0x85, 0xc9, //0x000033b1 testq        %rcx, %rcx
	0x49, 0x8d, 0x42, 0xff, //0x000033b4 leaq         $-1(%r10), %rax
	0x49, 0xf7, 0xd2, //0x000033b8 notq         %r10
	0x4c, 0x0f, 0x48, 0xd2, //0x000033bb cmovsq       %rdx, %r10
	0x49, 0x39, 0xc0, //0x000033bf cmpq         %rax, %r8
	0x49, 0x0f, 0x44, 0xd2, //0x000033c2 cmoveq       %r10, %rdx
	0x49, 0x89, 0xd2, //0x000033c6 movq         %rdx, %r10
	//0x000033c9 LBB0_580
	0x4c, 0x8b, 0x5d, 0xd0, //0x000033c9 movq         $-48(%rbp), %r11
	//0x000033cd LBB0_581
	0x4d, 0x85, 0xd2, //0x000033cd testq        %r10, %r10
	0x0f, 0x88, 0xfa, 0x20, 0x00, 0x00, //0x000033d0 js           LBB0_942
	0x4d, 0x01, 0xca, //0x000033d6 addq         %r9, %r10
	//0x000033d9 LBB0_583
	0x4d, 0x89, 0x13, //0x000033d9 movq         %r10, (%r11)
	0x4c, 0x89, 0xc9, //0x000033dc movq         %r9, %rcx
	0x4d, 0x85, 0xc9, //0x000033df testq        %r9, %r9
	0x0f, 0x88, 0x67, 0xfe, 0xff, 0xff, //0x000033e2 js           LBB0_558
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000033e8 .p2align 4, 0x90
	//0x000033f0 LBB0_584
	0x49, 0x8b, 0x36, //0x000033f0 movq         (%r14), %rsi
	0x48, 0x89, 0xf2, //0x000033f3 movq         %rsi, %rdx
	0x48, 0x8b, 0x4d, 0x98, //0x000033f6 movq         $-104(%rbp), %rcx
	0x48, 0x85, 0xf6, //0x000033fa testq        %rsi, %rsi
	0x0f, 0x84, 0x4c, 0xfe, 0xff, 0xff, //0x000033fd je           LBB0_558
	//0x00003403 LBB0_586
	0x49, 0x8b, 0x4d, 0x08, //0x00003403 movq         $8(%r13), %rcx
	0x4c, 0x89, 0xd6, //0x00003407 movq         %r10, %rsi
	0x48, 0x29, 0xce, //0x0000340a subq         %rcx, %rsi
	0x0f, 0x83, 0x2d, 0x00, 0x00, 0x00, //0x0000340d jae          LBB0_591
	0x43, 0x8a, 0x04, 0x17, //0x00003413 movb         (%r15,%r10), %al
	0x3c, 0x0d, //0x00003417 cmpb         $13, %al
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x00003419 je           LBB0_591
	0x3c, 0x20, //0x0000341f cmpb         $32, %al
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x00003421 je           LBB0_591
	0x04, 0xf7, //0x00003427 addb         $-9, %al
	0x3c, 0x01, //0x00003429 cmpb         $1, %al
	0x0f, 0x86, 0x0f, 0x00, 0x00, 0x00, //0x0000342b jbe          LBB0_591
	0x4d, 0x89, 0xd1, //0x00003431 movq         %r10, %r9
	0xe9, 0x24, 0x01, 0x00, 0x00, //0x00003434 jmp          LBB0_612
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003439 .p2align 4, 0x90
	//0x00003440 LBB0_591
	0x4d, 0x8d, 0x4a, 0x01, //0x00003440 leaq         $1(%r10), %r9
	0x49, 0x39, 0xc9, //0x00003444 cmpq         %rcx, %r9
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00003447 jae          LBB0_595
	0x43, 0x8a, 0x1c, 0x0f, //0x0000344d movb         (%r15,%r9), %bl
	0x80, 0xfb, 0x0d, //0x00003451 cmpb         $13, %bl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00003454 je           LBB0_595
	0x80, 0xfb, 0x20, //0x0000345a cmpb         $32, %bl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000345d je           LBB0_595
	0x80, 0xc3, 0xf7, //0x00003463 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00003466 cmpb         $1, %bl
	0x0f, 0x87, 0xee, 0x00, 0x00, 0x00, //0x00003469 ja           LBB0_612
	0x90, //0x0000346f .p2align 4, 0x90
	//0x00003470 LBB0_595
	0x4d, 0x8d, 0x4a, 0x02, //0x00003470 leaq         $2(%r10), %r9
	0x49, 0x39, 0xc9, //0x00003474 cmpq         %rcx, %r9
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00003477 jae          LBB0_599
	0x43, 0x8a, 0x1c, 0x0f, //0x0000347d movb         (%r15,%r9), %bl
	0x80, 0xfb, 0x0d, //0x00003481 cmpb         $13, %bl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00003484 je           LBB0_599
	0x80, 0xfb, 0x20, //0x0000348a cmpb         $32, %bl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000348d je           LBB0_599
	0x80, 0xc3, 0xf7, //0x00003493 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x00003496 cmpb         $1, %bl
	0x0f, 0x87, 0xbe, 0x00, 0x00, 0x00, //0x00003499 ja           LBB0_612
	0x90, //0x0000349f .p2align 4, 0x90
	//0x000034a0 LBB0_599
	0x4d, 0x8d, 0x4a, 0x03, //0x000034a0 leaq         $3(%r10), %r9
	0x49, 0x39, 0xc9, //0x000034a4 cmpq         %rcx, %r9
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000034a7 jae          LBB0_603
	0x43, 0x8a, 0x1c, 0x0f, //0x000034ad movb         (%r15,%r9), %bl
	0x80, 0xfb, 0x0d, //0x000034b1 cmpb         $13, %bl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000034b4 je           LBB0_603
	0x80, 0xfb, 0x20, //0x000034ba cmpb         $32, %bl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000034bd je           LBB0_603
	0x80, 0xc3, 0xf7, //0x000034c3 addb         $-9, %bl
	0x80, 0xfb, 0x01, //0x000034c6 cmpb         $1, %bl
	0x0f, 0x87, 0x8e, 0x00, 0x00, 0x00, //0x000034c9 ja           LBB0_612
	0x90, //0x000034cf .p2align 4, 0x90
	//0x000034d0 LBB0_603
	0x49, 0x8d, 0x7a, 0x04, //0x000034d0 leaq         $4(%r10), %rdi
	0x48, 0x39, 0xf9, //0x000034d4 cmpq         %rdi, %rcx
	0x0f, 0x86, 0xb2, 0x15, 0x00, 0x00, //0x000034d7 jbe          LBB0_869
	0x48, 0x39, 0xf9, //0x000034dd cmpq         %rdi, %rcx
	0x0f, 0x84, 0x4a, 0x00, 0x00, 0x00, //0x000034e0 je           LBB0_609
	0x49, 0x8d, 0x3c, 0x0f, //0x000034e6 leaq         (%r15,%rcx), %rdi
	0x48, 0x83, 0xc6, 0x04, //0x000034ea addq         $4, %rsi
	0x4c, 0x03, 0x55, 0x90, //0x000034ee addq         $-112(%rbp), %r10
	0x4d, 0x89, 0xd1, //0x000034f2 movq         %r10, %r9
	0x48, 0xb8, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000034f5 movabsq      $4294977024, %rax
	0x90, //0x000034ff .p2align 4, 0x90
	//0x00003500 LBB0_606
	0x41, 0x0f, 0xbe, 0x59, 0xff, //0x00003500 movsbl       $-1(%r9), %ebx
	0x83, 0xfb, 0x20, //0x00003505 cmpl         $32, %ebx
	0x0f, 0x87, 0x42, 0x00, 0x00, 0x00, //0x00003508 ja           LBB0_611
	0x48, 0x0f, 0xa3, 0xd8, //0x0000350e btq          %rbx, %rax
	0x0f, 0x83, 0x38, 0x00, 0x00, 0x00, //0x00003512 jae          LBB0_611
	0x49, 0xff, 0xc1, //0x00003518 incq         %r9
	0x48, 0xff, 0xc6, //0x0000351b incq         %rsi
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x0000351e jne          LBB0_606
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00003524 jmp          LBB0_610
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003529 .p2align 4, 0x90
	//0x00003530 LBB0_609
	0x4c, 0x01, 0xff, //0x00003530 addq         %r15, %rdi
	//0x00003533 LBB0_610
	0x4c, 0x29, 0xff, //0x00003533 subq         %r15, %rdi
	0x49, 0x89, 0xf9, //0x00003536 movq         %rdi, %r9
	0x49, 0x39, 0xc9, //0x00003539 cmpq         %rcx, %r9
	0x0f, 0x82, 0x1b, 0x00, 0x00, 0x00, //0x0000353c jb           LBB0_612
	0xe9, 0x4b, 0x15, 0x00, 0x00, //0x00003542 jmp          LBB0_870
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003547 .p2align 4, 0x90
	//0x00003550 LBB0_611
	0x4c, 0x03, 0x4d, 0xb0, //0x00003550 addq         $-80(%rbp), %r9
	0x49, 0x39, 0xc9, //0x00003554 cmpq         %rcx, %r9
	0x0f, 0x83, 0x35, 0x15, 0x00, 0x00, //0x00003557 jae          LBB0_870
	//0x0000355d LBB0_612
	0x4d, 0x8d, 0x51, 0x01, //0x0000355d leaq         $1(%r9), %r10
	0x4d, 0x89, 0x13, //0x00003561 movq         %r10, (%r11)
	0x43, 0x0f, 0xbe, 0x3c, 0x0f, //0x00003564 movsbl       (%r15,%r9), %edi
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00003569 movq         $-1, %rcx
	0x85, 0xff, //0x00003570 testl        %edi, %edi
	0x0f, 0x84, 0xd7, 0xfc, 0xff, 0xff, //0x00003572 je           LBB0_558
	0x48, 0x8d, 0x72, 0xff, //0x00003578 leaq         $-1(%rdx), %rsi
	0x41, 0x8b, 0x1c, 0xd6, //0x0000357c movl         (%r14,%rdx,8), %ebx
	0x48, 0x8b, 0x45, 0x98, //0x00003580 movq         $-104(%rbp), %rax
	0x48, 0x83, 0xf8, 0xff, //0x00003584 cmpq         $-1, %rax
	0x49, 0x0f, 0x44, 0xc1, //0x00003588 cmoveq       %r9, %rax
	0x48, 0x89, 0x45, 0x98, //0x0000358c movq         %rax, $-104(%rbp)
	0xff, 0xcb, //0x00003590 decl         %ebx
	0x83, 0xfb, 0x05, //0x00003592 cmpl         $5, %ebx
	0x0f, 0x87, 0x33, 0x02, 0x00, 0x00, //0x00003595 ja           LBB0_641
	0x48, 0x8d, 0x05, 0xb6, 0x25, 0x00, 0x00, //0x0000359b leaq         $9654(%rip), %rax  /* LJTI0_2+0(%rip) */
	0x48, 0x63, 0x1c, 0x98, //0x000035a2 movslq       (%rax,%rbx,4), %rbx
	0x48, 0x01, 0xc3, //0x000035a6 addq         %rax, %rbx
	0xff, 0xe3, //0x000035a9 jmpq         *%rbx
	//0x000035ab LBB0_615
	0x83, 0xff, 0x2c, //0x000035ab cmpl         $44, %edi
	0x0f, 0x84, 0xfa, 0x04, 0x00, 0x00, //0x000035ae je           LBB0_682
	0x83, 0xff, 0x5d, //0x000035b4 cmpl         $93, %edi
	0x0f, 0x84, 0xa3, 0x04, 0x00, 0x00, //0x000035b7 je           LBB0_617
	0xe9, 0x86, 0xfc, 0xff, 0xff, //0x000035bd jmp          LBB0_557
	//0x000035c2 LBB0_618
	0x40, 0x80, 0xff, 0x5d, //0x000035c2 cmpb         $93, %dil
	0x0f, 0x84, 0x94, 0x04, 0x00, 0x00, //0x000035c6 je           LBB0_617
	0x49, 0xc7, 0x04, 0xd6, 0x01, 0x00, 0x00, 0x00, //0x000035cc movq         $1, (%r14,%rdx,8)
	0x83, 0xff, 0x7b, //0x000035d4 cmpl         $123, %edi
	0x0f, 0x86, 0xfd, 0x01, 0x00, 0x00, //0x000035d7 jbe          LBB0_620
	0xe9, 0x66, 0xfc, 0xff, 0xff, //0x000035dd jmp          LBB0_557
	//0x000035e2 LBB0_621
	0x83, 0xff, 0x2c, //0x000035e2 cmpl         $44, %edi
	0x0f, 0x85, 0x6c, 0x04, 0x00, 0x00, //0x000035e5 jne          LBB0_622
	0x48, 0x81, 0xfa, 0xff, 0x0f, 0x00, 0x00, //0x000035eb cmpq         $4095, %rdx
	0x0f, 0x8f, 0xa6, 0x14, 0x00, 0x00, //0x000035f2 jg           LBB0_955
	0x48, 0x8d, 0x42, 0x01, //0x000035f8 leaq         $1(%rdx), %rax
	0x49, 0x89, 0x06, //0x000035fc movq         %rax, (%r14)
	0x49, 0xc7, 0x44, 0xd6, 0x08, 0x03, 0x00, 0x00, 0x00, //0x000035ff movq         $3, $8(%r14,%rdx,8)
	0xe9, 0xe3, 0xfd, 0xff, 0xff, //0x00003608 jmp          LBB0_584
	//0x0000360d LBB0_623
	0x40, 0x80, 0xff, 0x22, //0x0000360d cmpb         $34, %dil
	0x0f, 0x85, 0x31, 0xfc, 0xff, 0xff, //0x00003611 jne          LBB0_557
	0x49, 0xc7, 0x04, 0xd6, 0x04, 0x00, 0x00, 0x00, //0x00003617 movq         $4, (%r14,%rdx,8)
	0x49, 0x8b, 0x45, 0x08, //0x0000361f movq         $8(%r13), %rax
	0x49, 0x89, 0xc0, //0x00003623 movq         %rax, %r8
	0x4d, 0x29, 0xd0, //0x00003626 subq         %r10, %r8
	0x0f, 0x84, 0xf9, 0x1f, 0x00, 0x00, //0x00003629 je           LBB0_967
	0x4c, 0x89, 0x55, 0xc8, //0x0000362f movq         %r10, $-56(%rbp)
	0x4d, 0x01, 0xfa, //0x00003633 addq         %r15, %r10
	0x49, 0x83, 0xf8, 0x40, //0x00003636 cmpq         $64, %r8
	0x0f, 0x82, 0x75, 0x11, 0x00, 0x00, //0x0000363a jb           LBB0_840
	0x44, 0x89, 0xc1, //0x00003640 movl         %r8d, %ecx
	0x83, 0xe1, 0x3f, //0x00003643 andl         $63, %ecx
	0x48, 0x89, 0x4d, 0x80, //0x00003646 movq         %rcx, $-128(%rbp)
	0x48, 0x89, 0xc1, //0x0000364a movq         %rax, %rcx
	0x4c, 0x29, 0xc9, //0x0000364d subq         %r9, %rcx
	0x48, 0x83, 0xc1, 0xbf, //0x00003650 addq         $-65, %rcx
	0x48, 0x83, 0xe1, 0xc0, //0x00003654 andq         $-64, %rcx
	0x4c, 0x01, 0xc9, //0x00003658 addq         %r9, %rcx
	0x49, 0x8d, 0x4c, 0x0f, 0x41, //0x0000365b leaq         $65(%r15,%rcx), %rcx
	0x48, 0x89, 0x4d, 0x88, //0x00003660 movq         %rcx, $-120(%rbp)
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00003664 movq         $-1, %r15
	0x45, 0x31, 0xed, //0x0000366b xorl         %r13d, %r13d
	0x90, 0x90, //0x0000366e .p2align 4, 0x90
	//0x00003670 LBB0_627
	0xf3, 0x41, 0x0f, 0x6f, 0x12, //0x00003670 movdqu       (%r10), %xmm2
	0xf3, 0x41, 0x0f, 0x6f, 0x6a, 0x10, //0x00003675 movdqu       $16(%r10), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x72, 0x20, //0x0000367b movdqu       $32(%r10), %xmm6
	0xf3, 0x41, 0x0f, 0x6f, 0x7a, 0x30, //0x00003681 movdqu       $48(%r10), %xmm7
	0x66, 0x0f, 0x6f, 0xe2, //0x00003687 movdqa       %xmm2, %xmm4
	0x66, 0x0f, 0x74, 0xe0, //0x0000368b pcmpeqb      %xmm0, %xmm4
	0x66, 0x0f, 0xd7, 0xdc, //0x0000368f pmovmskb     %xmm4, %ebx
	0x66, 0x0f, 0x6f, 0xe5, //0x00003693 movdqa       %xmm5, %xmm4
	0x66, 0x0f, 0x74, 0xe0, //0x00003697 pcmpeqb      %xmm0, %xmm4
	0x66, 0x0f, 0xd7, 0xcc, //0x0000369b pmovmskb     %xmm4, %ecx
	0x66, 0x0f, 0x6f, 0xe6, //0x0000369f movdqa       %xmm6, %xmm4
	0x66, 0x0f, 0x74, 0xe0, //0x000036a3 pcmpeqb      %xmm0, %xmm4
	0x66, 0x0f, 0xd7, 0xd4, //0x000036a7 pmovmskb     %xmm4, %edx
	0x66, 0x0f, 0x6f, 0xe7, //0x000036ab movdqa       %xmm7, %xmm4
	0x66, 0x0f, 0x74, 0xe0, //0x000036af pcmpeqb      %xmm0, %xmm4
	0x66, 0x0f, 0xd7, 0xfc, //0x000036b3 pmovmskb     %xmm4, %edi
	0x66, 0x0f, 0x74, 0xd1, //0x000036b7 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xf2, //0x000036bb pmovmskb     %xmm2, %esi
	0x66, 0x0f, 0x74, 0xe9, //0x000036bf pcmpeqb      %xmm1, %xmm5
	0x66, 0x44, 0x0f, 0xd7, 0xf5, //0x000036c3 pmovmskb     %xmm5, %r14d
	0x66, 0x0f, 0x74, 0xf1, //0x000036c8 pcmpeqb      %xmm1, %xmm6
	0x66, 0x44, 0x0f, 0xd7, 0xde, //0x000036cc pmovmskb     %xmm6, %r11d
	0x66, 0x0f, 0x74, 0xf9, //0x000036d1 pcmpeqb      %xmm1, %xmm7
	0x66, 0x44, 0x0f, 0xd7, 0xe7, //0x000036d5 pmovmskb     %xmm7, %r12d
	0x48, 0xc1, 0xe7, 0x30, //0x000036da shlq         $48, %rdi
	0x48, 0xc1, 0xe2, 0x20, //0x000036de shlq         $32, %rdx
	0x48, 0xc1, 0xe1, 0x10, //0x000036e2 shlq         $16, %rcx
	0x48, 0x09, 0xcb, //0x000036e6 orq          %rcx, %rbx
	0x48, 0x09, 0xd3, //0x000036e9 orq          %rdx, %rbx
	0x49, 0xc1, 0xe4, 0x30, //0x000036ec shlq         $48, %r12
	0x49, 0xc1, 0xe3, 0x20, //0x000036f0 shlq         $32, %r11
	0x49, 0xc1, 0xe6, 0x10, //0x000036f4 shlq         $16, %r14
	0x4c, 0x09, 0xf6, //0x000036f8 orq          %r14, %rsi
	0x4c, 0x09, 0xde, //0x000036fb orq          %r11, %rsi
	0x4c, 0x09, 0xe6, //0x000036fe orq          %r12, %rsi
	0x49, 0x83, 0xff, 0xff, //0x00003701 cmpq         $-1, %r15
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00003705 jne          LBB0_629
	0x48, 0x85, 0xf6, //0x0000370b testq        %rsi, %rsi
	0x0f, 0x85, 0x90, 0x00, 0x00, 0x00, //0x0000370e jne          LBB0_638
	//0x00003714 LBB0_629
	0x48, 0x09, 0xfb, //0x00003714 orq          %rdi, %rbx
	0x48, 0x89, 0xf1, //0x00003717 movq         %rsi, %rcx
	0x4c, 0x09, 0xe9, //0x0000371a orq          %r13, %rcx
	0x4c, 0x8b, 0x5d, 0xd0, //0x0000371d movq         $-48(%rbp), %r11
	0x0f, 0x85, 0x20, 0x00, 0x00, 0x00, //0x00003721 jne          LBB0_644
	0x48, 0x85, 0xdb, //0x00003727 testq        %rbx, %rbx
	0x0f, 0x85, 0x48, 0x03, 0x00, 0x00, //0x0000372a jne          LBB0_645
	//0x00003730 LBB0_631
	0x49, 0x83, 0xc0, 0xc0, //0x00003730 addq         $-64, %r8
	0x49, 0x83, 0xc2, 0x40, //0x00003734 addq         $64, %r10
	0x49, 0x83, 0xf8, 0x3f, //0x00003738 cmpq         $63, %r8
	0x0f, 0x87, 0x2e, 0xff, 0xff, 0xff, //0x0000373c ja           LBB0_627
	0xe9, 0xbd, 0x0b, 0x00, 0x00, //0x00003742 jmp          LBB0_632
	//0x00003747 LBB0_644
	0x4c, 0x89, 0xe9, //0x00003747 movq         %r13, %rcx
	0x48, 0xf7, 0xd1, //0x0000374a notq         %rcx
	0x48, 0x21, 0xf1, //0x0000374d andq         %rsi, %rcx
	0x48, 0x8d, 0x14, 0x09, //0x00003750 leaq         (%rcx,%rcx), %rdx
	0x4c, 0x09, 0xea, //0x00003754 orq          %r13, %rdx
	0x48, 0x89, 0xd7, //0x00003757 movq         %rdx, %rdi
	0x48, 0xf7, 0xd7, //0x0000375a notq         %rdi
	0x48, 0x21, 0xf7, //0x0000375d andq         %rsi, %rdi
	0x48, 0x89, 0xc6, //0x00003760 movq         %rax, %rsi
	0x48, 0xb8, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00003763 movabsq      $-6148914691236517206, %rax
	0x48, 0x21, 0xc7, //0x0000376d andq         %rax, %rdi
	0x45, 0x31, 0xed, //0x00003770 xorl         %r13d, %r13d
	0x48, 0x01, 0xcf, //0x00003773 addq         %rcx, %rdi
	0x41, 0x0f, 0x92, 0xc5, //0x00003776 setb         %r13b
	0x48, 0x01, 0xff, //0x0000377a addq         %rdi, %rdi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000377d movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc7, //0x00003787 xorq         %rax, %rdi
	0x48, 0x89, 0xf0, //0x0000378a movq         %rsi, %rax
	0x48, 0x21, 0xd7, //0x0000378d andq         %rdx, %rdi
	0x48, 0xf7, 0xd7, //0x00003790 notq         %rdi
	0x48, 0x21, 0xfb, //0x00003793 andq         %rdi, %rbx
	0x48, 0x85, 0xdb, //0x00003796 testq        %rbx, %rbx
	0x0f, 0x84, 0x91, 0xff, 0xff, 0xff, //0x00003799 je           LBB0_631
	0xe9, 0xd4, 0x02, 0x00, 0x00, //0x0000379f jmp          LBB0_645
	//0x000037a4 LBB0_638
	0x4c, 0x89, 0xd1, //0x000037a4 movq         %r10, %rcx
	0x48, 0x2b, 0x4d, 0xb8, //0x000037a7 subq         $-72(%rbp), %rcx
	0x4c, 0x0f, 0xbc, 0xfe, //0x000037ab bsfq         %rsi, %r15
	0x49, 0x01, 0xcf, //0x000037af addq         %rcx, %r15
	0xe9, 0x5d, 0xff, 0xff, 0xff, //0x000037b2 jmp          LBB0_629
	//0x000037b7 LBB0_639
	0x40, 0x80, 0xff, 0x3a, //0x000037b7 cmpb         $58, %dil
	0x0f, 0x85, 0x87, 0xfa, 0xff, 0xff, //0x000037bb jne          LBB0_557
	0x49, 0xc7, 0x04, 0xd6, 0x00, 0x00, 0x00, 0x00, //0x000037c1 movq         $0, (%r14,%rdx,8)
	0xe9, 0x22, 0xfc, 0xff, 0xff, //0x000037c9 jmp          LBB0_584
	//0x000037ce LBB0_641
	0x49, 0x89, 0x36, //0x000037ce movq         %rsi, (%r14)
	0x83, 0xff, 0x7b, //0x000037d1 cmpl         $123, %edi
	0x0f, 0x87, 0x6e, 0xfa, 0xff, 0xff, //0x000037d4 ja           LBB0_557
	//0x000037da LBB0_620
	0x4f, 0x8d, 0x24, 0x0f, //0x000037da leaq         (%r15,%r9), %r12
	0x89, 0xf8, //0x000037de movl         %edi, %eax
	0x48, 0x8d, 0x15, 0x89, 0x23, 0x00, 0x00, //0x000037e0 leaq         $9097(%rip), %rdx  /* LJTI0_3+0(%rip) */
	0x48, 0x63, 0x04, 0x82, //0x000037e7 movslq       (%rdx,%rax,4), %rax
	0x48, 0x01, 0xd0, //0x000037eb addq         %rdx, %rax
	0xff, 0xe0, //0x000037ee jmpq         *%rax
	//0x000037f0 LBB0_648
	0x49, 0x8b, 0x7d, 0x08, //0x000037f0 movq         $8(%r13), %rdi
	0x4c, 0x29, 0xcf, //0x000037f4 subq         %r9, %rdi
	0x0f, 0x84, 0xd6, 0x12, 0x00, 0x00, //0x000037f7 je           LBB0_875
	0x41, 0x80, 0x3c, 0x24, 0x30, //0x000037fd cmpb         $48, (%r12)
	0x0f, 0x85, 0x31, 0x00, 0x00, 0x00, //0x00003802 jne          LBB0_653
	0x48, 0x83, 0xff, 0x01, //0x00003808 cmpq         $1, %rdi
	0x0f, 0x84, 0xc7, 0xfb, 0xff, 0xff, //0x0000380c je           LBB0_583
	0x43, 0x8a, 0x0c, 0x17, //0x00003812 movb         (%r15,%r10), %cl
	0x80, 0xc1, 0xd2, //0x00003816 addb         $-46, %cl
	0x80, 0xf9, 0x37, //0x00003819 cmpb         $55, %cl
	0x0f, 0x87, 0xb7, 0xfb, 0xff, 0xff, //0x0000381c ja           LBB0_583
	0x0f, 0xb6, 0xc1, //0x00003822 movzbl       %cl, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00003825 movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x0000382f btq          %rax, %rcx
	0x0f, 0x83, 0xa0, 0xfb, 0xff, 0xff, //0x00003833 jae          LBB0_583
	//0x00003839 LBB0_653
	0x48, 0x83, 0xff, 0x10, //0x00003839 cmpq         $16, %rdi
	0x0f, 0x82, 0xf1, 0x0f, 0x00, 0x00, //0x0000383d jb           LBB0_845
	0x4c, 0x8d, 0x7f, 0xf0, //0x00003843 leaq         $-16(%rdi), %r15
	0x4c, 0x89, 0xf8, //0x00003847 movq         %r15, %rax
	0x48, 0x83, 0xe0, 0xf0, //0x0000384a andq         $-16, %rax
	0x4a, 0x8d, 0x44, 0x20, 0x10, //0x0000384e leaq         $16(%rax,%r12), %rax
	0x48, 0x89, 0x45, 0xc8, //0x00003853 movq         %rax, $-56(%rbp)
	0x41, 0x83, 0xe7, 0x0f, //0x00003857 andl         $15, %r15d
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x0000385b movq         $-1, %r11
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00003862 movq         $-1, %r10
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00003869 movq         $-1, %r8
	0x4c, 0x89, 0xe3, //0x00003870 movq         %r12, %rbx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003873 .p2align 4, 0x90
	//0x00003880 LBB0_655
	0xf3, 0x0f, 0x6f, 0x13, //0x00003880 movdqu       (%rbx), %xmm2
	0x66, 0x0f, 0x6f, 0xe2, //0x00003884 movdqa       %xmm2, %xmm4
	0x66, 0x41, 0x0f, 0x64, 0xe0, //0x00003888 pcmpgtb      %xmm8, %xmm4
	0x66, 0x0f, 0x6f, 0xeb, //0x0000388d movdqa       %xmm3, %xmm5
	0x66, 0x0f, 0x64, 0xea, //0x00003891 pcmpgtb      %xmm2, %xmm5
	0x66, 0x0f, 0xdb, 0xec, //0x00003895 pand         %xmm4, %xmm5
	0x66, 0x0f, 0x6f, 0xe2, //0x00003899 movdqa       %xmm2, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe1, //0x0000389d pcmpeqb      %xmm9, %xmm4
	0x66, 0x0f, 0x6f, 0xf2, //0x000038a2 movdqa       %xmm2, %xmm6
	0x66, 0x41, 0x0f, 0x74, 0xf2, //0x000038a6 pcmpeqb      %xmm10, %xmm6
	0x66, 0x0f, 0xeb, 0xf4, //0x000038ab por          %xmm4, %xmm6
	0x66, 0x0f, 0x6f, 0xe2, //0x000038af movdqa       %xmm2, %xmm4
	0x66, 0x41, 0x0f, 0xeb, 0xe3, //0x000038b3 por          %xmm11, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xd4, //0x000038b8 pcmpeqb      %xmm12, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xe5, //0x000038bd pcmpeqb      %xmm13, %xmm4
	0x66, 0x44, 0x0f, 0xd7, 0xf4, //0x000038c2 pmovmskb     %xmm4, %r14d
	0x66, 0x0f, 0xeb, 0xe2, //0x000038c7 por          %xmm2, %xmm4
	0x66, 0x0f, 0xeb, 0xee, //0x000038cb por          %xmm6, %xmm5
	0x66, 0x0f, 0xeb, 0xec, //0x000038cf por          %xmm4, %xmm5
	0x66, 0x44, 0x0f, 0xd7, 0xea, //0x000038d3 pmovmskb     %xmm2, %r13d
	0x66, 0x0f, 0xd7, 0xf6, //0x000038d8 pmovmskb     %xmm6, %esi
	0x66, 0x0f, 0xd7, 0xc5, //0x000038dc pmovmskb     %xmm5, %eax
	0xb9, 0xff, 0xff, 0xff, 0xff, //0x000038e0 movl         $4294967295, %ecx
	0x48, 0x31, 0xc8, //0x000038e5 xorq         %rcx, %rax
	0x48, 0x0f, 0xbc, 0xc8, //0x000038e8 bsfq         %rax, %rcx
	0x83, 0xf9, 0x10, //0x000038ec cmpl         $16, %ecx
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x000038ef je           LBB0_657
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x000038f5 movl         $-1, %eax
	0xd3, 0xe0, //0x000038fa shll         %cl, %eax
	0xf7, 0xd0, //0x000038fc notl         %eax
	0x41, 0x21, 0xc5, //0x000038fe andl         %eax, %r13d
	0x41, 0x21, 0xc6, //0x00003901 andl         %eax, %r14d
	0x21, 0xf0, //0x00003904 andl         %esi, %eax
	0x89, 0xc6, //0x00003906 movl         %eax, %esi
	//0x00003908 LBB0_657
	0x41, 0x8d, 0x55, 0xff, //0x00003908 leal         $-1(%r13), %edx
	0x44, 0x21, 0xea, //0x0000390c andl         %r13d, %edx
	0x0f, 0x85, 0x82, 0x0a, 0x00, 0x00, //0x0000390f jne          LBB0_807
	0x41, 0x8d, 0x56, 0xff, //0x00003915 leal         $-1(%r14), %edx
	0x44, 0x21, 0xf2, //0x00003919 andl         %r14d, %edx
	0x0f, 0x85, 0x75, 0x0a, 0x00, 0x00, //0x0000391c jne          LBB0_807
	0x8d, 0x56, 0xff, //0x00003922 leal         $-1(%rsi), %edx
	0x21, 0xf2, //0x00003925 andl         %esi, %edx
	0x0f, 0x85, 0x6a, 0x0a, 0x00, 0x00, //0x00003927 jne          LBB0_807
	0x45, 0x85, 0xed, //0x0000392d testl        %r13d, %r13d
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00003930 je           LBB0_663
	0x48, 0x89, 0xd8, //0x00003936 movq         %rbx, %rax
	0x4c, 0x29, 0xe0, //0x00003939 subq         %r12, %rax
	0x41, 0x0f, 0xbc, 0xd5, //0x0000393c bsfl         %r13d, %edx
	0x48, 0x01, 0xc2, //0x00003940 addq         %rax, %rdx
	0x49, 0x83, 0xf8, 0xff, //0x00003943 cmpq         $-1, %r8
	0x0f, 0x85, 0x2a, 0x0c, 0x00, 0x00, //0x00003947 jne          LBB0_823
	0x49, 0x89, 0xd0, //0x0000394d movq         %rdx, %r8
	//0x00003950 LBB0_663
	0x45, 0x85, 0xf6, //0x00003950 testl        %r14d, %r14d
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00003953 je           LBB0_666
	0x48, 0x89, 0xd8, //0x00003959 movq         %rbx, %rax
	0x4c, 0x29, 0xe0, //0x0000395c subq         %r12, %rax
	0x41, 0x0f, 0xbc, 0xd6, //0x0000395f bsfl         %r14d, %edx
	0x48, 0x01, 0xc2, //0x00003963 addq         %rax, %rdx
	0x49, 0x83, 0xfa, 0xff, //0x00003966 cmpq         $-1, %r10
	0x0f, 0x85, 0x07, 0x0c, 0x00, 0x00, //0x0000396a jne          LBB0_823
	0x49, 0x89, 0xd2, //0x00003970 movq         %rdx, %r10
	//0x00003973 LBB0_666
	0x4c, 0x8b, 0x75, 0xa8, //0x00003973 movq         $-88(%rbp), %r14
	0x85, 0xf6, //0x00003977 testl        %esi, %esi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x00003979 je           LBB0_669
	0x48, 0x89, 0xd8, //0x0000397f movq         %rbx, %rax
	0x4c, 0x29, 0xe0, //0x00003982 subq         %r12, %rax
	0x0f, 0xbc, 0xd6, //0x00003985 bsfl         %esi, %edx
	0x48, 0x01, 0xc2, //0x00003988 addq         %rax, %rdx
	0x49, 0x83, 0xfb, 0xff, //0x0000398b cmpq         $-1, %r11
	0x0f, 0x85, 0xf7, 0x0c, 0x00, 0x00, //0x0000398f jne          LBB0_835
	0x49, 0x89, 0xd3, //0x00003995 movq         %rdx, %r11
	//0x00003998 LBB0_669
	0x83, 0xf9, 0x10, //0x00003998 cmpl         $16, %ecx
	0x0f, 0x85, 0x24, 0x03, 0x00, 0x00, //0x0000399b jne          LBB0_708
	0x48, 0x83, 0xc3, 0x10, //0x000039a1 addq         $16, %rbx
	0x48, 0x83, 0xc7, 0xf0, //0x000039a5 addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x0f, //0x000039a9 cmpq         $15, %rdi
	0x0f, 0x87, 0xcd, 0xfe, 0xff, 0xff, //0x000039ad ja           LBB0_655
	0x4d, 0x85, 0xff, //0x000039b3 testq        %r15, %r15
	0x48, 0x8d, 0x3d, 0x8f, 0x24, 0x00, 0x00, //0x000039b6 leaq         $9359(%rip), %rdi  /* LJTI0_5+0(%rip) */
	0x0f, 0x84, 0x09, 0x03, 0x00, 0x00, //0x000039bd je           LBB0_710
	//0x000039c3 LBB0_672
	0x48, 0x8b, 0x5d, 0xc8, //0x000039c3 movq         $-56(%rbp), %rbx
	0x4a, 0x8d, 0x0c, 0x3b, //0x000039c7 leaq         (%rbx,%r15), %rcx
	0xe9, 0x2c, 0x00, 0x00, 0x00, //0x000039cb jmp          LBB0_676
	//0x000039d0 LBB0_673
	0x48, 0x89, 0xf0, //0x000039d0 movq         %rsi, %rax
	0x4c, 0x29, 0xe0, //0x000039d3 subq         %r12, %rax
	0x49, 0x83, 0xfb, 0xff, //0x000039d6 cmpq         $-1, %r11
	0x0f, 0x85, 0xda, 0x0c, 0x00, 0x00, //0x000039da jne          LBB0_837
	0x48, 0xff, 0xc8, //0x000039e0 decq         %rax
	0x49, 0x89, 0xc3, //0x000039e3 movq         %rax, %r11
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000039e6 .p2align 4, 0x90
	//0x000039f0 LBB0_675
	0x48, 0x89, 0xf3, //0x000039f0 movq         %rsi, %rbx
	0x49, 0xff, 0xcf, //0x000039f3 decq         %r15
	0x0f, 0x84, 0x92, 0x0b, 0x00, 0x00, //0x000039f6 je           LBB0_825
	//0x000039fc LBB0_676
	0x0f, 0xbe, 0x13, //0x000039fc movsbl       (%rbx), %edx
	0x83, 0xc2, 0xd5, //0x000039ff addl         $-43, %edx
	0x83, 0xfa, 0x3a, //0x00003a02 cmpl         $58, %edx
	0x0f, 0x87, 0xbd, 0x02, 0x00, 0x00, //0x00003a05 ja           LBB0_709
	0x48, 0x8d, 0x73, 0x01, //0x00003a0b leaq         $1(%rbx), %rsi
	0x48, 0x63, 0x04, 0x97, //0x00003a0f movslq       (%rdi,%rdx,4), %rax
	0x48, 0x01, 0xf8, //0x00003a13 addq         %rdi, %rax
	0xff, 0xe0, //0x00003a16 jmpq         *%rax
	//0x00003a18 LBB0_678
	0x48, 0x89, 0xf0, //0x00003a18 movq         %rsi, %rax
	0x4c, 0x29, 0xe0, //0x00003a1b subq         %r12, %rax
	0x49, 0x83, 0xfa, 0xff, //0x00003a1e cmpq         $-1, %r10
	0x0f, 0x85, 0x92, 0x0c, 0x00, 0x00, //0x00003a22 jne          LBB0_837
	0x48, 0xff, 0xc8, //0x00003a28 decq         %rax
	0x49, 0x89, 0xc2, //0x00003a2b movq         %rax, %r10
	0xe9, 0xbd, 0xff, 0xff, 0xff, //0x00003a2e jmp          LBB0_675
	//0x00003a33 LBB0_680
	0x48, 0x89, 0xf0, //0x00003a33 movq         %rsi, %rax
	0x4c, 0x29, 0xe0, //0x00003a36 subq         %r12, %rax
	0x49, 0x83, 0xf8, 0xff, //0x00003a39 cmpq         $-1, %r8
	0x0f, 0x85, 0x77, 0x0c, 0x00, 0x00, //0x00003a3d jne          LBB0_837
	0x48, 0xff, 0xc8, //0x00003a43 decq         %rax
	0x49, 0x89, 0xc0, //0x00003a46 movq         %rax, %r8
	0xe9, 0xa2, 0xff, 0xff, 0xff, //0x00003a49 jmp          LBB0_675
	//0x00003a4e LBB0_642
	0x83, 0xff, 0x22, //0x00003a4e cmpl         $34, %edi
	0x0f, 0x84, 0x79, 0x00, 0x00, 0x00, //0x00003a51 je           LBB0_686
	//0x00003a57 LBB0_622
	0x83, 0xff, 0x7d, //0x00003a57 cmpl         $125, %edi
	0x0f, 0x85, 0xe8, 0xf7, 0xff, 0xff, //0x00003a5a jne          LBB0_557
	//0x00003a60 LBB0_617
	0x49, 0x89, 0x36, //0x00003a60 movq         %rsi, (%r14)
	0x48, 0x89, 0xf2, //0x00003a63 movq         %rsi, %rdx
	0x48, 0x8b, 0x4d, 0x98, //0x00003a66 movq         $-104(%rbp), %rcx
	0x48, 0x85, 0xf6, //0x00003a6a testq        %rsi, %rsi
	0x0f, 0x85, 0x90, 0xf9, 0xff, 0xff, //0x00003a6d jne          LBB0_586
	0xe9, 0xd7, 0xf7, 0xff, 0xff, //0x00003a73 jmp          LBB0_558
	//0x00003a78 LBB0_645
	0x48, 0x0f, 0xbc, 0xcb, //0x00003a78 bsfq         %rbx, %rcx
	0x4c, 0x03, 0x55, 0xa0, //0x00003a7c addq         $-96(%rbp), %r10
	0x49, 0x01, 0xca, //0x00003a80 addq         %rcx, %r10
	0x4c, 0x8b, 0x75, 0xa8, //0x00003a83 movq         $-88(%rbp), %r14
	//0x00003a87 LBB0_646
	0x4d, 0x85, 0xd2, //0x00003a87 testq        %r10, %r10
	0x0f, 0x88, 0x24, 0x10, 0x00, 0x00, //0x00003a8a js           LBB0_872
	0x4d, 0x89, 0x13, //0x00003a90 movq         %r10, (%r11)
	0x4c, 0x89, 0xc9, //0x00003a93 movq         %r9, %rcx
	0x48, 0x83, 0x7d, 0xc8, 0x00, //0x00003a96 cmpq         $0, $-56(%rbp)
	0x4c, 0x8b, 0x6d, 0xc0, //0x00003a9b movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x7d, 0xb8, //0x00003a9f movq         $-72(%rbp), %r15
	0x0f, 0x8f, 0x47, 0xf9, 0xff, 0xff, //0x00003aa3 jg           LBB0_584
	0xe9, 0xa1, 0xf7, 0xff, 0xff, //0x00003aa9 jmp          LBB0_558
	//0x00003aae LBB0_682
	0x48, 0x81, 0xfa, 0xff, 0x0f, 0x00, 0x00, //0x00003aae cmpq         $4095, %rdx
	0x0f, 0x8f, 0xe3, 0x0f, 0x00, 0x00, //0x00003ab5 jg           LBB0_955
	0x48, 0x8d, 0x42, 0x01, //0x00003abb leaq         $1(%rdx), %rax
	0x49, 0x89, 0x06, //0x00003abf movq         %rax, (%r14)
	0x49, 0xc7, 0x44, 0xd6, 0x08, 0x00, 0x00, 0x00, 0x00, //0x00003ac2 movq         $0, $8(%r14,%rdx,8)
	0xe9, 0x20, 0xf9, 0xff, 0xff, //0x00003acb jmp          LBB0_584
	//0x00003ad0 LBB0_686
	0x49, 0xc7, 0x04, 0xd6, 0x02, 0x00, 0x00, 0x00, //0x00003ad0 movq         $2, (%r14,%rdx,8)
	0x49, 0x8b, 0x4d, 0x08, //0x00003ad8 movq         $8(%r13), %rcx
	0x49, 0x89, 0xcd, //0x00003adc movq         %rcx, %r13
	0x4d, 0x29, 0xd5, //0x00003adf subq         %r10, %r13
	0x0f, 0x84, 0x5b, 0x1b, 0x00, 0x00, //0x00003ae2 je           LBB0_974
	0x4c, 0x89, 0x55, 0xc8, //0x00003ae8 movq         %r10, $-56(%rbp)
	0x4d, 0x01, 0xfa, //0x00003aec addq         %r15, %r10
	0x49, 0x83, 0xfd, 0x40, //0x00003aef cmpq         $64, %r13
	0x48, 0x89, 0x4d, 0x88, //0x00003af3 movq         %rcx, $-120(%rbp)
	0x0f, 0x82, 0x5f, 0x0d, 0x00, 0x00, //0x00003af7 jb           LBB0_846
	0x45, 0x89, 0xee, //0x00003afd movl         %r13d, %r14d
	0x41, 0x83, 0xe6, 0x3f, //0x00003b00 andl         $63, %r14d
	0x4c, 0x29, 0xc9, //0x00003b04 subq         %r9, %rcx
	0x48, 0x83, 0xc1, 0xbf, //0x00003b07 addq         $-65, %rcx
	0x48, 0x83, 0xe1, 0xc0, //0x00003b0b andq         $-64, %rcx
	0x4c, 0x01, 0xc9, //0x00003b0f addq         %r9, %rcx
	0x49, 0x8d, 0x44, 0x0f, 0x41, //0x00003b12 leaq         $65(%r15,%rcx), %rax
	0x48, 0x89, 0x45, 0x80, //0x00003b17 movq         %rax, $-128(%rbp)
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00003b1b movq         $-1, %r12
	0x45, 0x31, 0xff, //0x00003b22 xorl         %r15d, %r15d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003b25 .p2align 4, 0x90
	//0x00003b30 LBB0_689
	0xf3, 0x41, 0x0f, 0x6f, 0x12, //0x00003b30 movdqu       (%r10), %xmm2
	0xf3, 0x41, 0x0f, 0x6f, 0x62, 0x10, //0x00003b35 movdqu       $16(%r10), %xmm4
	0xf3, 0x41, 0x0f, 0x6f, 0x6a, 0x20, //0x00003b3b movdqu       $32(%r10), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x72, 0x30, //0x00003b41 movdqu       $48(%r10), %xmm6
	0x66, 0x0f, 0x6f, 0xfa, //0x00003b47 movdqa       %xmm2, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x00003b4b pcmpeqb      %xmm0, %xmm7
	0x66, 0x0f, 0xd7, 0xf7, //0x00003b4f pmovmskb     %xmm7, %esi
	0x66, 0x0f, 0x6f, 0xfc, //0x00003b53 movdqa       %xmm4, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x00003b57 pcmpeqb      %xmm0, %xmm7
	0x66, 0x0f, 0xd7, 0xd7, //0x00003b5b pmovmskb     %xmm7, %edx
	0x66, 0x0f, 0x6f, 0xfd, //0x00003b5f movdqa       %xmm5, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x00003b63 pcmpeqb      %xmm0, %xmm7
	0x66, 0x0f, 0xd7, 0xc7, //0x00003b67 pmovmskb     %xmm7, %eax
	0x66, 0x0f, 0x6f, 0xfe, //0x00003b6b movdqa       %xmm6, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x00003b6f pcmpeqb      %xmm0, %xmm7
	0x66, 0x0f, 0xd7, 0xcf, //0x00003b73 pmovmskb     %xmm7, %ecx
	0x66, 0x0f, 0x74, 0xd1, //0x00003b77 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xfa, //0x00003b7b pmovmskb     %xmm2, %edi
	0x66, 0x0f, 0x74, 0xe1, //0x00003b7f pcmpeqb      %xmm1, %xmm4
	0x66, 0x0f, 0xd7, 0xdc, //0x00003b83 pmovmskb     %xmm4, %ebx
	0x66, 0x0f, 0x74, 0xe9, //0x00003b87 pcmpeqb      %xmm1, %xmm5
	0x66, 0x44, 0x0f, 0xd7, 0xdd, //0x00003b8b pmovmskb     %xmm5, %r11d
	0x66, 0x0f, 0x74, 0xf1, //0x00003b90 pcmpeqb      %xmm1, %xmm6
	0x66, 0x44, 0x0f, 0xd7, 0xc6, //0x00003b94 pmovmskb     %xmm6, %r8d
	0x48, 0xc1, 0xe1, 0x30, //0x00003b99 shlq         $48, %rcx
	0x48, 0xc1, 0xe0, 0x20, //0x00003b9d shlq         $32, %rax
	0x48, 0xc1, 0xe2, 0x10, //0x00003ba1 shlq         $16, %rdx
	0x48, 0x09, 0xd6, //0x00003ba5 orq          %rdx, %rsi
	0x48, 0x09, 0xc6, //0x00003ba8 orq          %rax, %rsi
	0x49, 0xc1, 0xe0, 0x30, //0x00003bab shlq         $48, %r8
	0x49, 0xc1, 0xe3, 0x20, //0x00003baf shlq         $32, %r11
	0x48, 0xc1, 0xe3, 0x10, //0x00003bb3 shlq         $16, %rbx
	0x48, 0x09, 0xdf, //0x00003bb7 orq          %rbx, %rdi
	0x4c, 0x09, 0xdf, //0x00003bba orq          %r11, %rdi
	0x4c, 0x09, 0xc7, //0x00003bbd orq          %r8, %rdi
	0x49, 0x83, 0xfc, 0xff, //0x00003bc0 cmpq         $-1, %r12
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00003bc4 jne          LBB0_691
	0x48, 0x85, 0xff, //0x00003bca testq        %rdi, %rdi
	0x0f, 0x85, 0x8a, 0x00, 0x00, 0x00, //0x00003bcd jne          LBB0_700
	//0x00003bd3 LBB0_691
	0x48, 0x09, 0xce, //0x00003bd3 orq          %rcx, %rsi
	0x48, 0x89, 0xf8, //0x00003bd6 movq         %rdi, %rax
	0x4c, 0x09, 0xf8, //0x00003bd9 orq          %r15, %rax
	0x4c, 0x8b, 0x5d, 0xd0, //0x00003bdc movq         $-48(%rbp), %r11
	0x0f, 0x85, 0x20, 0x00, 0x00, 0x00, //0x00003be0 jne          LBB0_701
	0x48, 0x85, 0xf6, //0x00003be6 testq        %rsi, %rsi
	0x0f, 0x85, 0x81, 0x00, 0x00, 0x00, //0x00003be9 jne          LBB0_702
	//0x00003bef LBB0_693
	0x49, 0x83, 0xc5, 0xc0, //0x00003bef addq         $-64, %r13
	0x49, 0x83, 0xc2, 0x40, //0x00003bf3 addq         $64, %r10
	0x49, 0x83, 0xfd, 0x3f, //0x00003bf7 cmpq         $63, %r13
	0x0f, 0x87, 0x2f, 0xff, 0xff, 0xff, //0x00003bfb ja           LBB0_689
	0xe9, 0xa8, 0x08, 0x00, 0x00, //0x00003c01 jmp          LBB0_694
	//0x00003c06 LBB0_701
	0x4c, 0x89, 0xf8, //0x00003c06 movq         %r15, %rax
	0x48, 0xf7, 0xd0, //0x00003c09 notq         %rax
	0x48, 0x21, 0xf8, //0x00003c0c andq         %rdi, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x00003c0f leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xf9, //0x00003c13 orq          %r15, %rcx
	0x48, 0x89, 0xca, //0x00003c16 movq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x00003c19 notq         %rdx
	0x48, 0x21, 0xfa, //0x00003c1c andq         %rdi, %rdx
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00003c1f movabsq      $-6148914691236517206, %rdi
	0x48, 0x21, 0xfa, //0x00003c29 andq         %rdi, %rdx
	0x45, 0x31, 0xff, //0x00003c2c xorl         %r15d, %r15d
	0x48, 0x01, 0xc2, //0x00003c2f addq         %rax, %rdx
	0x41, 0x0f, 0x92, 0xc7, //0x00003c32 setb         %r15b
	0x48, 0x01, 0xd2, //0x00003c36 addq         %rdx, %rdx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00003c39 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc2, //0x00003c43 xorq         %rax, %rdx
	0x48, 0x21, 0xca, //0x00003c46 andq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x00003c49 notq         %rdx
	0x48, 0x21, 0xd6, //0x00003c4c andq         %rdx, %rsi
	0x48, 0x85, 0xf6, //0x00003c4f testq        %rsi, %rsi
	0x0f, 0x84, 0x97, 0xff, 0xff, 0xff, //0x00003c52 je           LBB0_693
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x00003c58 jmp          LBB0_702
	//0x00003c5d LBB0_700
	0x4c, 0x89, 0xd0, //0x00003c5d movq         %r10, %rax
	0x48, 0x2b, 0x45, 0xb8, //0x00003c60 subq         $-72(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xe7, //0x00003c64 bsfq         %rdi, %r12
	0x49, 0x01, 0xc4, //0x00003c68 addq         %rax, %r12
	0xe9, 0x63, 0xff, 0xff, 0xff, //0x00003c6b jmp          LBB0_691
	//0x00003c70 LBB0_702
	0x48, 0x0f, 0xbc, 0xc6, //0x00003c70 bsfq         %rsi, %rax
	0x4c, 0x03, 0x55, 0xa0, //0x00003c74 addq         $-96(%rbp), %r10
	0x49, 0x01, 0xc2, //0x00003c78 addq         %rax, %r10
	0x4c, 0x8b, 0x6d, 0xc0, //0x00003c7b movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x75, 0xa8, //0x00003c7f movq         $-88(%rbp), %r14
	0x4c, 0x8b, 0x7d, 0xb8, //0x00003c83 movq         $-72(%rbp), %r15
	0x4d, 0x85, 0xd2, //0x00003c87 testq        %r10, %r10
	0x0f, 0x88, 0x20, 0x18, 0x00, 0x00, //0x00003c8a js           LBB0_939
	//0x00003c90 LBB0_705
	0x4d, 0x89, 0x13, //0x00003c90 movq         %r10, (%r11)
	0x4c, 0x89, 0xc9, //0x00003c93 movq         %r9, %rcx
	0x48, 0x83, 0x7d, 0xc8, 0x00, //0x00003c96 cmpq         $0, $-56(%rbp)
	0x0f, 0x8e, 0xae, 0xf5, 0xff, 0xff, //0x00003c9b jle          LBB0_558
	0x49, 0x8b, 0x06, //0x00003ca1 movq         (%r14), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00003ca4 cmpq         $4095, %rax
	0x0f, 0x8f, 0xee, 0x0d, 0x00, 0x00, //0x00003caa jg           LBB0_955
	0x48, 0x8d, 0x48, 0x01, //0x00003cb0 leaq         $1(%rax), %rcx
	0x49, 0x89, 0x0e, //0x00003cb4 movq         %rcx, (%r14)
	0x49, 0xc7, 0x44, 0xc6, 0x08, 0x04, 0x00, 0x00, 0x00, //0x00003cb7 movq         $4, $8(%r14,%rax,8)
	0xe9, 0x2b, 0xf7, 0xff, 0xff, //0x00003cc0 jmp          LBB0_584
	//0x00003cc5 LBB0_708
	0x48, 0x01, 0xcb, //0x00003cc5 addq         %rcx, %rbx
	//0x00003cc8 LBB0_709
	0x48, 0x89, 0x5d, 0xc8, //0x00003cc8 movq         %rbx, $-56(%rbp)
	//0x00003ccc LBB0_710
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00003ccc movq         $-1, %rcx
	0x4d, 0x85, 0xd2, //0x00003cd3 testq        %r10, %r10
	0x0f, 0x84, 0xf7, 0x17, 0x00, 0x00, //0x00003cd6 je           LBB0_943
	//0x00003cdc LBB0_711
	0x4d, 0x85, 0xdb, //0x00003cdc testq        %r11, %r11
	0x0f, 0x84, 0xee, 0x17, 0x00, 0x00, //0x00003cdf je           LBB0_943
	0x4d, 0x85, 0xc0, //0x00003ce5 testq        %r8, %r8
	0x0f, 0x84, 0xe5, 0x17, 0x00, 0x00, //0x00003ce8 je           LBB0_943
	0x48, 0x8b, 0x45, 0xc8, //0x00003cee movq         $-56(%rbp), %rax
	0x4c, 0x29, 0xe0, //0x00003cf2 subq         %r12, %rax
	0x48, 0x89, 0xc2, //0x00003cf5 movq         %rax, %rdx
	0x48, 0x8d, 0x48, 0xff, //0x00003cf8 leaq         $-1(%rax), %rcx
	0x49, 0x39, 0xca, //0x00003cfc cmpq         %rcx, %r10
	0x0f, 0x84, 0x3f, 0x00, 0x00, 0x00, //0x00003cff je           LBB0_719
	0x49, 0x39, 0xc8, //0x00003d05 cmpq         %rcx, %r8
	0x0f, 0x84, 0x36, 0x00, 0x00, 0x00, //0x00003d08 je           LBB0_719
	0x49, 0x39, 0xcb, //0x00003d0e cmpq         %rcx, %r11
	0x0f, 0x84, 0x2d, 0x00, 0x00, 0x00, //0x00003d11 je           LBB0_719
	0x4d, 0x85, 0xdb, //0x00003d17 testq        %r11, %r11
	0x4c, 0x8b, 0x6d, 0xc0, //0x00003d1a movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x75, 0xa8, //0x00003d1e movq         $-88(%rbp), %r14
	0x4c, 0x8b, 0x7d, 0xb8, //0x00003d22 movq         $-72(%rbp), %r15
	0x0f, 0x8e, 0x33, 0x00, 0x00, 0x00, //0x00003d26 jle          LBB0_721
	0x49, 0x8d, 0x43, 0xff, //0x00003d2c leaq         $-1(%r11), %rax
	0x49, 0x39, 0xc2, //0x00003d30 cmpq         %rax, %r10
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00003d33 je           LBB0_721
	0x49, 0xf7, 0xd3, //0x00003d39 notq         %r11
	0x4d, 0x89, 0xda, //0x00003d3c movq         %r11, %r10
	0xe9, 0x85, 0xf6, 0xff, 0xff, //0x00003d3f jmp          LBB0_580
	//0x00003d44 LBB0_719
	0x49, 0x89, 0xd2, //0x00003d44 movq         %rdx, %r10
	0x49, 0xf7, 0xda, //0x00003d47 negq         %r10
	//0x00003d4a LBB0_720
	0x4c, 0x8b, 0x5d, 0xd0, //0x00003d4a movq         $-48(%rbp), %r11
	0x4c, 0x8b, 0x6d, 0xc0, //0x00003d4e movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x75, 0xa8, //0x00003d52 movq         $-88(%rbp), %r14
	0x4c, 0x8b, 0x7d, 0xb8, //0x00003d56 movq         $-72(%rbp), %r15
	0xe9, 0x6e, 0xf6, 0xff, 0xff, //0x00003d5a jmp          LBB0_581
	//0x00003d5f LBB0_721
	0x4c, 0x89, 0xc1, //0x00003d5f movq         %r8, %rcx
	0x4c, 0x09, 0xd1, //0x00003d62 orq          %r10, %rcx
	0x4d, 0x39, 0xd0, //0x00003d65 cmpq         %r10, %r8
	0x0f, 0x8c, 0x43, 0xf6, 0xff, 0xff, //0x00003d68 jl           LBB0_579
	0x48, 0x85, 0xc9, //0x00003d6e testq        %rcx, %rcx
	0x0f, 0x88, 0x3a, 0xf6, 0xff, 0xff, //0x00003d71 js           LBB0_579
	0x49, 0xf7, 0xd0, //0x00003d77 notq         %r8
	0x4d, 0x89, 0xc2, //0x00003d7a movq         %r8, %r10
	0xe9, 0x47, 0xf6, 0xff, 0xff, //0x00003d7d jmp          LBB0_580
	//0x00003d82 LBB0_724
	0x49, 0x8b, 0x45, 0x08, //0x00003d82 movq         $8(%r13), %rax
	0x49, 0x89, 0xc5, //0x00003d86 movq         %rax, %r13
	0x4d, 0x29, 0xd5, //0x00003d89 subq         %r10, %r13
	0x0f, 0x84, 0xb1, 0x18, 0x00, 0x00, //0x00003d8c je           LBB0_974
	0x4c, 0x89, 0x55, 0xc8, //0x00003d92 movq         %r10, $-56(%rbp)
	0x4d, 0x01, 0xfa, //0x00003d96 addq         %r15, %r10
	0x49, 0x83, 0xfd, 0x40, //0x00003d99 cmpq         $64, %r13
	0x48, 0x89, 0x45, 0x88, //0x00003d9d movq         %rax, $-120(%rbp)
	0x0f, 0x82, 0x2e, 0x0b, 0x00, 0x00, //0x00003da1 jb           LBB0_850
	0x45, 0x89, 0xee, //0x00003da7 movl         %r13d, %r14d
	0x41, 0x83, 0xe6, 0x3f, //0x00003daa andl         $63, %r14d
	0x4c, 0x29, 0xc8, //0x00003dae subq         %r9, %rax
	0x48, 0x83, 0xc0, 0xbf, //0x00003db1 addq         $-65, %rax
	0x48, 0x83, 0xe0, 0xc0, //0x00003db5 andq         $-64, %rax
	0x4c, 0x01, 0xc8, //0x00003db9 addq         %r9, %rax
	0x49, 0x8d, 0x44, 0x07, 0x41, //0x00003dbc leaq         $65(%r15,%rax), %rax
	0x48, 0x89, 0x45, 0x80, //0x00003dc1 movq         %rax, $-128(%rbp)
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00003dc5 movq         $-1, %r12
	0x45, 0x31, 0xff, //0x00003dcc xorl         %r15d, %r15d
	0x90, //0x00003dcf .p2align 4, 0x90
	//0x00003dd0 LBB0_727
	0xf3, 0x41, 0x0f, 0x6f, 0x12, //0x00003dd0 movdqu       (%r10), %xmm2
	0xf3, 0x41, 0x0f, 0x6f, 0x62, 0x10, //0x00003dd5 movdqu       $16(%r10), %xmm4
	0xf3, 0x41, 0x0f, 0x6f, 0x6a, 0x20, //0x00003ddb movdqu       $32(%r10), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x72, 0x30, //0x00003de1 movdqu       $48(%r10), %xmm6
	0x66, 0x0f, 0x6f, 0xfa, //0x00003de7 movdqa       %xmm2, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x00003deb pcmpeqb      %xmm0, %xmm7
	0x66, 0x0f, 0xd7, 0xf7, //0x00003def pmovmskb     %xmm7, %esi
	0x66, 0x0f, 0x6f, 0xfc, //0x00003df3 movdqa       %xmm4, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x00003df7 pcmpeqb      %xmm0, %xmm7
	0x66, 0x0f, 0xd7, 0xc7, //0x00003dfb pmovmskb     %xmm7, %eax
	0x66, 0x0f, 0x6f, 0xfd, //0x00003dff movdqa       %xmm5, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x00003e03 pcmpeqb      %xmm0, %xmm7
	0x66, 0x0f, 0xd7, 0xd7, //0x00003e07 pmovmskb     %xmm7, %edx
	0x66, 0x0f, 0x6f, 0xfe, //0x00003e0b movdqa       %xmm6, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x00003e0f pcmpeqb      %xmm0, %xmm7
	0x66, 0x0f, 0xd7, 0xcf, //0x00003e13 pmovmskb     %xmm7, %ecx
	0x66, 0x0f, 0x74, 0xd1, //0x00003e17 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xfa, //0x00003e1b pmovmskb     %xmm2, %edi
	0x66, 0x0f, 0x74, 0xe1, //0x00003e1f pcmpeqb      %xmm1, %xmm4
	0x66, 0x0f, 0xd7, 0xdc, //0x00003e23 pmovmskb     %xmm4, %ebx
	0x66, 0x0f, 0x74, 0xe9, //0x00003e27 pcmpeqb      %xmm1, %xmm5
	0x66, 0x44, 0x0f, 0xd7, 0xdd, //0x00003e2b pmovmskb     %xmm5, %r11d
	0x66, 0x0f, 0x74, 0xf1, //0x00003e30 pcmpeqb      %xmm1, %xmm6
	0x66, 0x44, 0x0f, 0xd7, 0xc6, //0x00003e34 pmovmskb     %xmm6, %r8d
	0x48, 0xc1, 0xe1, 0x30, //0x00003e39 shlq         $48, %rcx
	0x48, 0xc1, 0xe2, 0x20, //0x00003e3d shlq         $32, %rdx
	0x48, 0xc1, 0xe0, 0x10, //0x00003e41 shlq         $16, %rax
	0x48, 0x09, 0xc6, //0x00003e45 orq          %rax, %rsi
	0x48, 0x09, 0xd6, //0x00003e48 orq          %rdx, %rsi
	0x49, 0xc1, 0xe0, 0x30, //0x00003e4b shlq         $48, %r8
	0x49, 0xc1, 0xe3, 0x20, //0x00003e4f shlq         $32, %r11
	0x48, 0xc1, 0xe3, 0x10, //0x00003e53 shlq         $16, %rbx
	0x48, 0x09, 0xdf, //0x00003e57 orq          %rbx, %rdi
	0x4c, 0x09, 0xdf, //0x00003e5a orq          %r11, %rdi
	0x4c, 0x09, 0xc7, //0x00003e5d orq          %r8, %rdi
	0x49, 0x83, 0xfc, 0xff, //0x00003e60 cmpq         $-1, %r12
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00003e64 jne          LBB0_729
	0x48, 0x85, 0xff, //0x00003e6a testq        %rdi, %rdi
	0x0f, 0x85, 0x8a, 0x00, 0x00, 0x00, //0x00003e6d jne          LBB0_738
	//0x00003e73 LBB0_729
	0x48, 0x09, 0xce, //0x00003e73 orq          %rcx, %rsi
	0x48, 0x89, 0xf8, //0x00003e76 movq         %rdi, %rax
	0x4c, 0x09, 0xf8, //0x00003e79 orq          %r15, %rax
	0x4c, 0x8b, 0x5d, 0xd0, //0x00003e7c movq         $-48(%rbp), %r11
	0x0f, 0x85, 0x20, 0x00, 0x00, 0x00, //0x00003e80 jne          LBB0_791
	0x48, 0x85, 0xf6, //0x00003e86 testq        %rsi, %rsi
	0x0f, 0x85, 0xaf, 0x03, 0x00, 0x00, //0x00003e89 jne          LBB0_792
	//0x00003e8f LBB0_731
	0x49, 0x83, 0xc5, 0xc0, //0x00003e8f addq         $-64, %r13
	0x49, 0x83, 0xc2, 0x40, //0x00003e93 addq         $64, %r10
	0x49, 0x83, 0xfd, 0x3f, //0x00003e97 cmpq         $63, %r13
	0x0f, 0x87, 0x2f, 0xff, 0xff, 0xff, //0x00003e9b ja           LBB0_727
	0xe9, 0x70, 0x08, 0x00, 0x00, //0x00003ea1 jmp          LBB0_732
	//0x00003ea6 LBB0_791
	0x4c, 0x89, 0xf8, //0x00003ea6 movq         %r15, %rax
	0x48, 0xf7, 0xd0, //0x00003ea9 notq         %rax
	0x48, 0x21, 0xf8, //0x00003eac andq         %rdi, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x00003eaf leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xf9, //0x00003eb3 orq          %r15, %rcx
	0x48, 0x89, 0xca, //0x00003eb6 movq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x00003eb9 notq         %rdx
	0x48, 0x21, 0xfa, //0x00003ebc andq         %rdi, %rdx
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00003ebf movabsq      $-6148914691236517206, %rdi
	0x48, 0x21, 0xfa, //0x00003ec9 andq         %rdi, %rdx
	0x45, 0x31, 0xff, //0x00003ecc xorl         %r15d, %r15d
	0x48, 0x01, 0xc2, //0x00003ecf addq         %rax, %rdx
	0x41, 0x0f, 0x92, 0xc7, //0x00003ed2 setb         %r15b
	0x48, 0x01, 0xd2, //0x00003ed6 addq         %rdx, %rdx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00003ed9 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc2, //0x00003ee3 xorq         %rax, %rdx
	0x48, 0x21, 0xca, //0x00003ee6 andq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x00003ee9 notq         %rdx
	0x48, 0x21, 0xd6, //0x00003eec andq         %rdx, %rsi
	0x48, 0x85, 0xf6, //0x00003eef testq        %rsi, %rsi
	0x0f, 0x84, 0x97, 0xff, 0xff, 0xff, //0x00003ef2 je           LBB0_731
	0xe9, 0x41, 0x03, 0x00, 0x00, //0x00003ef8 jmp          LBB0_792
	//0x00003efd LBB0_738
	0x4c, 0x89, 0xd0, //0x00003efd movq         %r10, %rax
	0x48, 0x2b, 0x45, 0xb8, //0x00003f00 subq         $-72(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xe7, //0x00003f04 bsfq         %rdi, %r12
	0x49, 0x01, 0xc4, //0x00003f08 addq         %rax, %r12
	0xe9, 0x63, 0xff, 0xff, 0xff, //0x00003f0b jmp          LBB0_729
	//0x00003f10 LBB0_739
	0x4d, 0x8b, 0x7d, 0x08, //0x00003f10 movq         $8(%r13), %r15
	0x4d, 0x29, 0xd7, //0x00003f14 subq         %r10, %r15
	0x0f, 0x84, 0xf9, 0x15, 0x00, 0x00, //0x00003f17 je           LBB0_953
	0x48, 0x8b, 0x45, 0xb8, //0x00003f1d movq         $-72(%rbp), %rax
	0x4c, 0x89, 0x55, 0xc8, //0x00003f21 movq         %r10, $-56(%rbp)
	0x4e, 0x8d, 0x1c, 0x10, //0x00003f25 leaq         (%rax,%r10), %r11
	0x41, 0x80, 0x3b, 0x30, //0x00003f29 cmpb         $48, (%r11)
	0x0f, 0x85, 0x37, 0x00, 0x00, 0x00, //0x00003f2d jne          LBB0_744
	0x41, 0xba, 0x01, 0x00, 0x00, 0x00, //0x00003f33 movl         $1, %r10d
	0x49, 0x83, 0xff, 0x01, //0x00003f39 cmpq         $1, %r15
	0x0f, 0x84, 0xa8, 0x07, 0x00, 0x00, //0x00003f3d je           LBB0_822
	0x41, 0x8a, 0x4b, 0x01, //0x00003f43 movb         $1(%r11), %cl
	0x80, 0xc1, 0xd2, //0x00003f47 addb         $-46, %cl
	0x80, 0xf9, 0x37, //0x00003f4a cmpb         $55, %cl
	0x0f, 0x87, 0x98, 0x07, 0x00, 0x00, //0x00003f4d ja           LBB0_822
	0x0f, 0xb6, 0xc1, //0x00003f53 movzbl       %cl, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00003f56 movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x00003f60 btq          %rax, %rcx
	0x0f, 0x83, 0x81, 0x07, 0x00, 0x00, //0x00003f64 jae          LBB0_822
	//0x00003f6a LBB0_744
	0x49, 0x83, 0xff, 0x10, //0x00003f6a cmpq         $16, %r15
	0x0f, 0x82, 0x7e, 0x09, 0x00, 0x00, //0x00003f6e jb           LBB0_851
	0x4d, 0x8d, 0x47, 0xf0, //0x00003f74 leaq         $-16(%r15), %r8
	0x4c, 0x89, 0xc0, //0x00003f78 movq         %r8, %rax
	0x48, 0x83, 0xe0, 0xf0, //0x00003f7b andq         $-16, %rax
	0x4e, 0x8d, 0x6c, 0x18, 0x10, //0x00003f7f leaq         $16(%rax,%r11), %r13
	0x41, 0x83, 0xe0, 0x0f, //0x00003f84 andl         $15, %r8d
	0x48, 0xc7, 0x45, 0x88, 0xff, 0xff, 0xff, 0xff, //0x00003f88 movq         $-1, $-120(%rbp)
	0x48, 0xc7, 0x45, 0x80, 0xff, 0xff, 0xff, 0xff, //0x00003f90 movq         $-1, $-128(%rbp)
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00003f98 movq         $-1, %r14
	0x4d, 0x89, 0xdc, //0x00003f9f movq         %r11, %r12
	//0x00003fa2 LBB0_746
	0xf3, 0x41, 0x0f, 0x6f, 0x14, 0x24, //0x00003fa2 movdqu       (%r12), %xmm2
	0x66, 0x0f, 0x6f, 0xe2, //0x00003fa8 movdqa       %xmm2, %xmm4
	0x66, 0x41, 0x0f, 0x64, 0xe0, //0x00003fac pcmpgtb      %xmm8, %xmm4
	0x66, 0x0f, 0x6f, 0xeb, //0x00003fb1 movdqa       %xmm3, %xmm5
	0x66, 0x0f, 0x64, 0xea, //0x00003fb5 pcmpgtb      %xmm2, %xmm5
	0x66, 0x0f, 0xdb, 0xec, //0x00003fb9 pand         %xmm4, %xmm5
	0x66, 0x0f, 0x6f, 0xe2, //0x00003fbd movdqa       %xmm2, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe1, //0x00003fc1 pcmpeqb      %xmm9, %xmm4
	0x66, 0x0f, 0x6f, 0xf2, //0x00003fc6 movdqa       %xmm2, %xmm6
	0x66, 0x41, 0x0f, 0x74, 0xf2, //0x00003fca pcmpeqb      %xmm10, %xmm6
	0x66, 0x0f, 0xeb, 0xf4, //0x00003fcf por          %xmm4, %xmm6
	0x66, 0x0f, 0x6f, 0xe2, //0x00003fd3 movdqa       %xmm2, %xmm4
	0x66, 0x41, 0x0f, 0xeb, 0xe3, //0x00003fd7 por          %xmm11, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xd4, //0x00003fdc pcmpeqb      %xmm12, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xe5, //0x00003fe1 pcmpeqb      %xmm13, %xmm4
	0x66, 0x0f, 0xd7, 0xd4, //0x00003fe6 pmovmskb     %xmm4, %edx
	0x66, 0x0f, 0xeb, 0xe2, //0x00003fea por          %xmm2, %xmm4
	0x66, 0x0f, 0xeb, 0xee, //0x00003fee por          %xmm6, %xmm5
	0x66, 0x0f, 0xeb, 0xec, //0x00003ff2 por          %xmm4, %xmm5
	0x66, 0x0f, 0xd7, 0xda, //0x00003ff6 pmovmskb     %xmm2, %ebx
	0x66, 0x0f, 0xd7, 0xf6, //0x00003ffa pmovmskb     %xmm6, %esi
	0x66, 0x0f, 0xd7, 0xc5, //0x00003ffe pmovmskb     %xmm5, %eax
	0xb9, 0xff, 0xff, 0xff, 0xff, //0x00004002 movl         $4294967295, %ecx
	0x48, 0x31, 0xc8, //0x00004007 xorq         %rcx, %rax
	0x48, 0x0f, 0xbc, 0xc8, //0x0000400a bsfq         %rax, %rcx
	0x83, 0xf9, 0x10, //0x0000400e cmpl         $16, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x00004011 je           LBB0_748
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00004017 movl         $-1, %eax
	0xd3, 0xe0, //0x0000401c shll         %cl, %eax
	0xf7, 0xd0, //0x0000401e notl         %eax
	0x21, 0xc3, //0x00004020 andl         %eax, %ebx
	0x21, 0xc2, //0x00004022 andl         %eax, %edx
	0x21, 0xf0, //0x00004024 andl         %esi, %eax
	0x89, 0xc6, //0x00004026 movl         %eax, %esi
	//0x00004028 LBB0_748
	0x8d, 0x7b, 0xff, //0x00004028 leal         $-1(%rbx), %edi
	0x21, 0xdf, //0x0000402b andl         %ebx, %edi
	0x0f, 0x85, 0xa2, 0x06, 0x00, 0x00, //0x0000402d jne          LBB0_838
	0x8d, 0x7a, 0xff, //0x00004033 leal         $-1(%rdx), %edi
	0x21, 0xd7, //0x00004036 andl         %edx, %edi
	0x0f, 0x85, 0x97, 0x06, 0x00, 0x00, //0x00004038 jne          LBB0_838
	0x8d, 0x7e, 0xff, //0x0000403e leal         $-1(%rsi), %edi
	0x21, 0xf7, //0x00004041 andl         %esi, %edi
	0x0f, 0x85, 0x8c, 0x06, 0x00, 0x00, //0x00004043 jne          LBB0_838
	0x85, 0xdb, //0x00004049 testl        %ebx, %ebx
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x0000404b je           LBB0_754
	0x4c, 0x89, 0xe0, //0x00004051 movq         %r12, %rax
	0x4c, 0x29, 0xd8, //0x00004054 subq         %r11, %rax
	0x44, 0x0f, 0xbc, 0xd3, //0x00004057 bsfl         %ebx, %r10d
	0x49, 0x01, 0xc2, //0x0000405b addq         %rax, %r10
	0x49, 0x83, 0xfe, 0xff, //0x0000405e cmpq         $-1, %r14
	0x0f, 0x85, 0x77, 0x06, 0x00, 0x00, //0x00004062 jne          LBB0_839
	0x4d, 0x89, 0xd6, //0x00004068 movq         %r10, %r14
	//0x0000406b LBB0_754
	0x85, 0xd2, //0x0000406b testl        %edx, %edx
	0x0f, 0x84, 0x1c, 0x00, 0x00, 0x00, //0x0000406d je           LBB0_757
	0x4c, 0x89, 0xe0, //0x00004073 movq         %r12, %rax
	0x4c, 0x29, 0xd8, //0x00004076 subq         %r11, %rax
	0x44, 0x0f, 0xbc, 0xd2, //0x00004079 bsfl         %edx, %r10d
	0x49, 0x01, 0xc2, //0x0000407d addq         %rax, %r10
	0x48, 0x83, 0x7d, 0x80, 0xff, //0x00004080 cmpq         $-1, $-128(%rbp)
	0x0f, 0x85, 0x54, 0x06, 0x00, 0x00, //0x00004085 jne          LBB0_839
	0x4c, 0x89, 0x55, 0x80, //0x0000408b movq         %r10, $-128(%rbp)
	//0x0000408f LBB0_757
	0x85, 0xf6, //0x0000408f testl        %esi, %esi
	0x0f, 0x84, 0x1c, 0x00, 0x00, 0x00, //0x00004091 je           LBB0_760
	0x4c, 0x89, 0xe0, //0x00004097 movq         %r12, %rax
	0x4c, 0x29, 0xd8, //0x0000409a subq         %r11, %rax
	0x44, 0x0f, 0xbc, 0xd6, //0x0000409d bsfl         %esi, %r10d
	0x49, 0x01, 0xc2, //0x000040a1 addq         %rax, %r10
	0x48, 0x83, 0x7d, 0x88, 0xff, //0x000040a4 cmpq         $-1, $-120(%rbp)
	0x0f, 0x85, 0x30, 0x06, 0x00, 0x00, //0x000040a9 jne          LBB0_839
	0x4c, 0x89, 0x55, 0x88, //0x000040af movq         %r10, $-120(%rbp)
	//0x000040b3 LBB0_760
	0x83, 0xf9, 0x10, //0x000040b3 cmpl         $16, %ecx
	0x0f, 0x85, 0xb8, 0x01, 0x00, 0x00, //0x000040b6 jne          LBB0_796
	0x49, 0x83, 0xc4, 0x10, //0x000040bc addq         $16, %r12
	0x49, 0x83, 0xc7, 0xf0, //0x000040c0 addq         $-16, %r15
	0x49, 0x83, 0xff, 0x0f, //0x000040c4 cmpq         $15, %r15
	0x0f, 0x87, 0xd4, 0xfe, 0xff, 0xff, //0x000040c8 ja           LBB0_746
	0x4d, 0x85, 0xc0, //0x000040ce testq        %r8, %r8
	0x48, 0x8d, 0x3d, 0x88, 0x1c, 0x00, 0x00, //0x000040d1 leaq         $7304(%rip), %rdi  /* LJTI0_4+0(%rip) */
	0x4c, 0x8b, 0x7d, 0x80, //0x000040d8 movq         $-128(%rbp), %r15
	0x48, 0x8b, 0x5d, 0x88, //0x000040dc movq         $-120(%rbp), %rbx
	0x0f, 0x84, 0x9c, 0x01, 0x00, 0x00, //0x000040e0 je           LBB0_797
	//0x000040e6 LBB0_763
	0x4b, 0x8d, 0x4c, 0x05, 0x00, //0x000040e6 leaq         (%r13,%r8), %rcx
	0xe9, 0x0c, 0x00, 0x00, 0x00, //0x000040eb jmp          LBB0_765
	//0x000040f0 LBB0_764
	0x49, 0x89, 0xd5, //0x000040f0 movq         %rdx, %r13
	0x49, 0xff, 0xc8, //0x000040f3 decq         %r8
	0x0f, 0x84, 0x20, 0x07, 0x00, 0x00, //0x000040f6 je           LBB0_844
	//0x000040fc LBB0_765
	0x41, 0x0f, 0xbe, 0x75, 0x00, //0x000040fc movsbl       (%r13), %esi
	0x83, 0xc6, 0xd5, //0x00004101 addl         $-43, %esi
	0x83, 0xfe, 0x3a, //0x00004104 cmpl         $58, %esi
	0x0f, 0x87, 0x75, 0x01, 0x00, 0x00, //0x00004107 ja           LBB0_797
	0x49, 0x8d, 0x55, 0x01, //0x0000410d leaq         $1(%r13), %rdx
	0x48, 0x63, 0x04, 0xb7, //0x00004111 movslq       (%rdi,%rsi,4), %rax
	0x48, 0x01, 0xf8, //0x00004115 addq         %rdi, %rax
	0xff, 0xe0, //0x00004118 jmpq         *%rax
	//0x0000411a LBB0_767
	0x49, 0x89, 0xd2, //0x0000411a movq         %rdx, %r10
	0x4d, 0x29, 0xda, //0x0000411d subq         %r11, %r10
	0x48, 0x83, 0xfb, 0xff, //0x00004120 cmpq         $-1, %rbx
	0x0f, 0x85, 0x9a, 0x07, 0x00, 0x00, //0x00004124 jne          LBB0_852
	0x49, 0xff, 0xca, //0x0000412a decq         %r10
	0x4c, 0x89, 0xd3, //0x0000412d movq         %r10, %rbx
	0xe9, 0xbb, 0xff, 0xff, 0xff, //0x00004130 jmp          LBB0_764
	//0x00004135 LBB0_769
	0x49, 0x89, 0xd2, //0x00004135 movq         %rdx, %r10
	0x4d, 0x29, 0xda, //0x00004138 subq         %r11, %r10
	0x49, 0x83, 0xff, 0xff, //0x0000413b cmpq         $-1, %r15
	0x0f, 0x85, 0x7f, 0x07, 0x00, 0x00, //0x0000413f jne          LBB0_852
	0x49, 0xff, 0xca, //0x00004145 decq         %r10
	0x4d, 0x89, 0xd7, //0x00004148 movq         %r10, %r15
	0xe9, 0xa0, 0xff, 0xff, 0xff, //0x0000414b jmp          LBB0_764
	//0x00004150 LBB0_771
	0x49, 0x89, 0xd2, //0x00004150 movq         %rdx, %r10
	0x4d, 0x29, 0xda, //0x00004153 subq         %r11, %r10
	0x49, 0x83, 0xfe, 0xff, //0x00004156 cmpq         $-1, %r14
	0x0f, 0x85, 0x64, 0x07, 0x00, 0x00, //0x0000415a jne          LBB0_852
	0x49, 0xff, 0xca, //0x00004160 decq         %r10
	0x4d, 0x89, 0xd6, //0x00004163 movq         %r10, %r14
	0xe9, 0x85, 0xff, 0xff, 0xff, //0x00004166 jmp          LBB0_764
	//0x0000416b LBB0_773
	0x49, 0x8b, 0x06, //0x0000416b movq         (%r14), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x0000416e cmpq         $4095, %rax
	0x0f, 0x8f, 0x24, 0x09, 0x00, 0x00, //0x00004174 jg           LBB0_955
	0x48, 0x8d, 0x48, 0x01, //0x0000417a leaq         $1(%rax), %rcx
	0x49, 0x89, 0x0e, //0x0000417e movq         %rcx, (%r14)
	0x49, 0xc7, 0x44, 0xc6, 0x08, 0x05, 0x00, 0x00, 0x00, //0x00004181 movq         $5, $8(%r14,%rax,8)
	0xe9, 0x61, 0xf2, 0xff, 0xff, //0x0000418a jmp          LBB0_584
	//0x0000418f LBB0_775
	0x49, 0x8b, 0x06, //0x0000418f movq         (%r14), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00004192 cmpq         $4095, %rax
	0x0f, 0x8f, 0x00, 0x09, 0x00, 0x00, //0x00004198 jg           LBB0_955
	0x48, 0x8d, 0x48, 0x01, //0x0000419e leaq         $1(%rax), %rcx
	0x49, 0x89, 0x0e, //0x000041a2 movq         %rcx, (%r14)
	0x49, 0xc7, 0x44, 0xc6, 0x08, 0x06, 0x00, 0x00, 0x00, //0x000041a5 movq         $6, $8(%r14,%rax,8)
	0xe9, 0x3d, 0xf2, 0xff, 0xff, //0x000041ae jmp          LBB0_584
	//0x000041b3 LBB0_777
	0x49, 0x8b, 0x55, 0x08, //0x000041b3 movq         $8(%r13), %rdx
	0x48, 0x8d, 0x42, 0xfc, //0x000041b7 leaq         $-4(%rdx), %rax
	0x49, 0x39, 0xc1, //0x000041bb cmpq         %rax, %r9
	0x0f, 0x83, 0x21, 0x13, 0x00, 0x00, //0x000041be jae          LBB0_956
	0x43, 0x8b, 0x14, 0x17, //0x000041c4 movl         (%r15,%r10), %edx
	0x81, 0xfa, 0x61, 0x6c, 0x73, 0x65, //0x000041c8 cmpl         $1702063201, %edx
	0x0f, 0x85, 0xa4, 0x13, 0x00, 0x00, //0x000041ce jne          LBB0_961
	0x4c, 0x89, 0xd0, //0x000041d4 movq         %r10, %rax
	0x4d, 0x8d, 0x51, 0x05, //0x000041d7 leaq         $5(%r9), %r10
	0xe9, 0x4a, 0x00, 0x00, 0x00, //0x000041db jmp          LBB0_790
	//0x000041e0 LBB0_780
	0x49, 0x8b, 0x55, 0x08, //0x000041e0 movq         $8(%r13), %rdx
	0x48, 0x8d, 0x42, 0xfd, //0x000041e4 leaq         $-3(%rdx), %rax
	0x49, 0x39, 0xc1, //0x000041e8 cmpq         %rax, %r9
	0x0f, 0x83, 0xf4, 0x12, 0x00, 0x00, //0x000041eb jae          LBB0_956
	0x41, 0x81, 0x3c, 0x24, 0x6e, 0x75, 0x6c, 0x6c, //0x000041f1 cmpl         $1819047278, (%r12)
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x000041f9 je           LBB0_789
	0xe9, 0xc6, 0x13, 0x00, 0x00, //0x000041ff jmp          LBB0_782
	//0x00004204 LBB0_787
	0x49, 0x8b, 0x55, 0x08, //0x00004204 movq         $8(%r13), %rdx
	0x48, 0x8d, 0x42, 0xfd, //0x00004208 leaq         $-3(%rdx), %rax
	0x49, 0x39, 0xc1, //0x0000420c cmpq         %rax, %r9
	0x0f, 0x83, 0xd0, 0x12, 0x00, 0x00, //0x0000420f jae          LBB0_956
	0x41, 0x81, 0x3c, 0x24, 0x74, 0x72, 0x75, 0x65, //0x00004215 cmpl         $1702195828, (%r12)
	0x0f, 0x85, 0x02, 0x13, 0x00, 0x00, //0x0000421d jne          LBB0_957
	//0x00004223 LBB0_789
	0x4c, 0x89, 0xd0, //0x00004223 movq         %r10, %rax
	0x4d, 0x8d, 0x51, 0x04, //0x00004226 leaq         $4(%r9), %r10
	//0x0000422a LBB0_790
	0x4d, 0x89, 0x13, //0x0000422a movq         %r10, (%r11)
	0x4c, 0x89, 0xc9, //0x0000422d movq         %r9, %rcx
	0x48, 0x85, 0xc0, //0x00004230 testq        %rax, %rax
	0x0f, 0x8f, 0xb7, 0xf1, 0xff, 0xff, //0x00004233 jg           LBB0_584
	0xe9, 0x11, 0xf0, 0xff, 0xff, //0x00004239 jmp          LBB0_558
	//0x0000423e LBB0_792
	0x48, 0x0f, 0xbc, 0xc6, //0x0000423e bsfq         %rsi, %rax
	0x4c, 0x03, 0x55, 0xa0, //0x00004242 addq         $-96(%rbp), %r10
	0x49, 0x01, 0xc2, //0x00004246 addq         %rax, %r10
	0x4c, 0x8b, 0x75, 0xa8, //0x00004249 movq         $-88(%rbp), %r14
	0x4c, 0x8b, 0x7d, 0xb8, //0x0000424d movq         $-72(%rbp), %r15
	0x4d, 0x85, 0xd2, //0x00004251 testq        %r10, %r10
	0x4c, 0x8b, 0x6d, 0xc0, //0x00004254 movq         $-64(%rbp), %r13
	0x0f, 0x88, 0x52, 0x12, 0x00, 0x00, //0x00004258 js           LBB0_939
	//0x0000425e LBB0_795
	0x4d, 0x89, 0x13, //0x0000425e movq         %r10, (%r11)
	0x4c, 0x89, 0xc9, //0x00004261 movq         %r9, %rcx
	0x48, 0x83, 0x7d, 0xc8, 0x00, //0x00004264 cmpq         $0, $-56(%rbp)
	0x0f, 0x8f, 0x81, 0xf1, 0xff, 0xff, //0x00004269 jg           LBB0_584
	0xe9, 0xdb, 0xef, 0xff, 0xff, //0x0000426f jmp          LBB0_558
	//0x00004274 LBB0_796
	0x49, 0x01, 0xcc, //0x00004274 addq         %rcx, %r12
	0x4d, 0x89, 0xe5, //0x00004277 movq         %r12, %r13
	0x4c, 0x8b, 0x7d, 0x80, //0x0000427a movq         $-128(%rbp), %r15
	0x48, 0x8b, 0x5d, 0x88, //0x0000427e movq         $-120(%rbp), %rbx
	//0x00004282 LBB0_797
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00004282 movq         $-1, %r10
	0x4d, 0x85, 0xff, //0x00004289 testq        %r15, %r15
	0x0f, 0x84, 0x8b, 0x12, 0x00, 0x00, //0x0000428c je           LBB0_954
	//0x00004292 LBB0_798
	0x48, 0x85, 0xdb, //0x00004292 testq        %rbx, %rbx
	0x0f, 0x84, 0x82, 0x12, 0x00, 0x00, //0x00004295 je           LBB0_954
	0x4d, 0x85, 0xf6, //0x0000429b testq        %r14, %r14
	0x0f, 0x84, 0x79, 0x12, 0x00, 0x00, //0x0000429e je           LBB0_954
	0x4d, 0x29, 0xdd, //0x000042a4 subq         %r11, %r13
	0x49, 0x8d, 0x4d, 0xff, //0x000042a7 leaq         $-1(%r13), %rcx
	0x49, 0x39, 0xcf, //0x000042ab cmpq         %rcx, %r15
	0x0f, 0x84, 0x3c, 0x00, 0x00, 0x00, //0x000042ae je           LBB0_806
	0x49, 0x39, 0xce, //0x000042b4 cmpq         %rcx, %r14
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x000042b7 je           LBB0_806
	0x48, 0x39, 0xcb, //0x000042bd cmpq         %rcx, %rbx
	0x0f, 0x84, 0x2a, 0x00, 0x00, 0x00, //0x000042c0 je           LBB0_806
	0x48, 0x85, 0xdb, //0x000042c6 testq        %rbx, %rbx
	0x0f, 0x8e, 0xda, 0x00, 0x00, 0x00, //0x000042c9 jle          LBB0_808
	0x48, 0x8d, 0x43, 0xff, //0x000042cf leaq         $-1(%rbx), %rax
	0x49, 0x39, 0xc7, //0x000042d3 cmpq         %rax, %r15
	0x0f, 0x84, 0xcd, 0x00, 0x00, 0x00, //0x000042d6 je           LBB0_808
	0x48, 0xf7, 0xd3, //0x000042dc notq         %rbx
	0x49, 0x89, 0xda, //0x000042df movq         %rbx, %r10
	0x4d, 0x85, 0xd2, //0x000042e2 testq        %r10, %r10
	0x0f, 0x89, 0x00, 0x04, 0x00, 0x00, //0x000042e5 jns          LBB0_822
	0xe9, 0x2d, 0x12, 0x00, 0x00, //0x000042eb jmp          LBB0_954
	//0x000042f0 LBB0_806
	0x49, 0xf7, 0xdd, //0x000042f0 negq         %r13
	0x4d, 0x89, 0xea, //0x000042f3 movq         %r13, %r10
	0x4d, 0x85, 0xd2, //0x000042f6 testq        %r10, %r10
	0x0f, 0x89, 0xec, 0x03, 0x00, 0x00, //0x000042f9 jns          LBB0_822
	0xe9, 0x19, 0x12, 0x00, 0x00, //0x000042ff jmp          LBB0_954
	//0x00004304 LBB0_632
	0x4c, 0x8b, 0x55, 0x88, //0x00004304 movq         $-120(%rbp), %r10
	0x4c, 0x8b, 0x45, 0x80, //0x00004308 movq         $-128(%rbp), %r8
	0x49, 0x83, 0xf8, 0x20, //0x0000430c cmpq         $32, %r8
	0x0f, 0x82, 0xb3, 0x04, 0x00, 0x00, //0x00004310 jb           LBB0_841
	//0x00004316 LBB0_633
	0xf3, 0x41, 0x0f, 0x6f, 0x12, //0x00004316 movdqu       (%r10), %xmm2
	0xf3, 0x41, 0x0f, 0x6f, 0x62, 0x10, //0x0000431b movdqu       $16(%r10), %xmm4
	0x66, 0x0f, 0x6f, 0xea, //0x00004321 movdqa       %xmm2, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00004325 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xdd, //0x00004329 pmovmskb     %xmm5, %ebx
	0x66, 0x0f, 0x6f, 0xec, //0x0000432d movdqa       %xmm4, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00004331 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xf5, //0x00004335 pmovmskb     %xmm5, %esi
	0x66, 0x0f, 0x74, 0xd1, //0x00004339 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xfa, //0x0000433d pmovmskb     %xmm2, %edi
	0x66, 0x0f, 0x74, 0xe1, //0x00004341 pcmpeqb      %xmm1, %xmm4
	0x66, 0x0f, 0xd7, 0xcc, //0x00004345 pmovmskb     %xmm4, %ecx
	0x48, 0xc1, 0xe6, 0x10, //0x00004349 shlq         $16, %rsi
	0x48, 0xc1, 0xe1, 0x10, //0x0000434d shlq         $16, %rcx
	0x48, 0x09, 0xcf, //0x00004351 orq          %rcx, %rdi
	0x49, 0x83, 0xff, 0xff, //0x00004354 cmpq         $-1, %r15
	0x0f, 0x85, 0x77, 0x00, 0x00, 0x00, //0x00004358 jne          LBB0_811
	0x48, 0x85, 0xff, //0x0000435e testq        %rdi, %rdi
	0x4c, 0x8b, 0x5d, 0xd0, //0x00004361 movq         $-48(%rbp), %r11
	0x4c, 0x8b, 0x75, 0xa8, //0x00004365 movq         $-88(%rbp), %r14
	0x0f, 0x85, 0xaa, 0x05, 0x00, 0x00, //0x00004369 jne          LBB0_853
	0x48, 0x09, 0xde, //0x0000436f orq          %rbx, %rsi
	0x48, 0x89, 0xf9, //0x00004372 movq         %rdi, %rcx
	0x4c, 0x09, 0xe9, //0x00004375 orq          %r13, %rcx
	0x0f, 0x85, 0x6e, 0x00, 0x00, 0x00, //0x00004378 jne          LBB0_812
	//0x0000437e LBB0_636
	0x48, 0x85, 0xf6, //0x0000437e testq        %rsi, %rsi
	0x0f, 0x84, 0x9e, 0x00, 0x00, 0x00, //0x00004381 je           LBB0_813
	//0x00004387 LBB0_637
	0x48, 0x0f, 0xbc, 0xce, //0x00004387 bsfq         %rsi, %rcx
	0x4c, 0x03, 0x55, 0xa0, //0x0000438b addq         $-96(%rbp), %r10
	0x49, 0x01, 0xca, //0x0000438f addq         %rcx, %r10
	0xe9, 0xf0, 0xf6, 0xff, 0xff, //0x00004392 jmp          LBB0_646
	//0x00004397 LBB0_807
	0x4c, 0x29, 0xe3, //0x00004397 subq         %r12, %rbx
	0x44, 0x0f, 0xbc, 0xd2, //0x0000439a bsfl         %edx, %r10d
	0x49, 0x01, 0xda, //0x0000439e addq         %rbx, %r10
	0x49, 0xf7, 0xd2, //0x000043a1 notq         %r10
	0xe9, 0xa1, 0xf9, 0xff, 0xff, //0x000043a4 jmp          LBB0_720
	//0x000043a9 LBB0_808
	0x4c, 0x89, 0xf1, //0x000043a9 movq         %r14, %rcx
	0x4c, 0x09, 0xf9, //0x000043ac orq          %r15, %rcx
	0x4d, 0x39, 0xfe, //0x000043af cmpq         %r15, %r14
	0x0f, 0x8c, 0x99, 0x01, 0x00, 0x00, //0x000043b2 jl           LBB0_820
	0x48, 0x85, 0xc9, //0x000043b8 testq        %rcx, %rcx
	0x0f, 0x88, 0x90, 0x01, 0x00, 0x00, //0x000043bb js           LBB0_820
	0x49, 0xf7, 0xd6, //0x000043c1 notq         %r14
	0x4d, 0x89, 0xf2, //0x000043c4 movq         %r14, %r10
	0x4d, 0x85, 0xd2, //0x000043c7 testq        %r10, %r10
	0x0f, 0x89, 0x1b, 0x03, 0x00, 0x00, //0x000043ca jns          LBB0_822
	0xe9, 0x48, 0x11, 0x00, 0x00, //0x000043d0 jmp          LBB0_954
	//0x000043d5 LBB0_811
	0x4c, 0x8b, 0x5d, 0xd0, //0x000043d5 movq         $-48(%rbp), %r11
	0x4c, 0x8b, 0x75, 0xa8, //0x000043d9 movq         $-88(%rbp), %r14
	0x48, 0x09, 0xde, //0x000043dd orq          %rbx, %rsi
	0x48, 0x89, 0xf9, //0x000043e0 movq         %rdi, %rcx
	0x4c, 0x09, 0xe9, //0x000043e3 orq          %r13, %rcx
	0x0f, 0x84, 0x92, 0xff, 0xff, 0xff, //0x000043e6 je           LBB0_636
	//0x000043ec LBB0_812
	0x44, 0x89, 0xe9, //0x000043ec movl         %r13d, %ecx
	0xf7, 0xd1, //0x000043ef notl         %ecx
	0x21, 0xf9, //0x000043f1 andl         %edi, %ecx
	0x8d, 0x1c, 0x09, //0x000043f3 leal         (%rcx,%rcx), %ebx
	0x44, 0x09, 0xeb, //0x000043f6 orl          %r13d, %ebx
	0x89, 0xda, //0x000043f9 movl         %ebx, %edx
	0xf7, 0xd2, //0x000043fb notl         %edx
	0x21, 0xfa, //0x000043fd andl         %edi, %edx
	0x81, 0xe2, 0xaa, 0xaa, 0xaa, 0xaa, //0x000043ff andl         $-1431655766, %edx
	0x45, 0x31, 0xed, //0x00004405 xorl         %r13d, %r13d
	0x01, 0xca, //0x00004408 addl         %ecx, %edx
	0x41, 0x0f, 0x92, 0xc5, //0x0000440a setb         %r13b
	0x01, 0xd2, //0x0000440e addl         %edx, %edx
	0x81, 0xf2, 0x55, 0x55, 0x55, 0x55, //0x00004410 xorl         $1431655765, %edx
	0x21, 0xda, //0x00004416 andl         %ebx, %edx
	0xf7, 0xd2, //0x00004418 notl         %edx
	0x21, 0xd6, //0x0000441a andl         %edx, %esi
	0x48, 0x85, 0xf6, //0x0000441c testq        %rsi, %rsi
	0x0f, 0x85, 0x62, 0xff, 0xff, 0xff, //0x0000441f jne          LBB0_637
	//0x00004425 LBB0_813
	0x49, 0x83, 0xc2, 0x20, //0x00004425 addq         $32, %r10
	0x49, 0x83, 0xc0, 0xe0, //0x00004429 addq         $-32, %r8
	0x4d, 0x85, 0xed, //0x0000442d testq        %r13, %r13
	0x0f, 0x85, 0xa4, 0x03, 0x00, 0x00, //0x00004430 jne          LBB0_842
	//0x00004436 LBB0_814
	0x4c, 0x89, 0xfa, //0x00004436 movq         %r15, %rdx
	0x4d, 0x85, 0xc0, //0x00004439 testq        %r8, %r8
	0x0f, 0x84, 0x7c, 0x06, 0x00, 0x00, //0x0000443c je           LBB0_873
	//0x00004442 LBB0_815
	0x49, 0x8d, 0x72, 0x01, //0x00004442 leaq         $1(%r10), %rsi
	0x41, 0x0f, 0xb6, 0x1a, //0x00004446 movzbl       (%r10), %ebx
	0x80, 0xfb, 0x22, //0x0000444a cmpb         $34, %bl
	0x0f, 0x84, 0x2f, 0x01, 0x00, 0x00, //0x0000444d je           LBB0_824
	0x49, 0x8d, 0x78, 0xff, //0x00004453 leaq         $-1(%r8), %rdi
	0x80, 0xfb, 0x5c, //0x00004457 cmpb         $92, %bl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x0000445a je           LBB0_818
	0x49, 0x89, 0xf8, //0x00004460 movq         %rdi, %r8
	0x49, 0x89, 0xf2, //0x00004463 movq         %rsi, %r10
	0x48, 0x85, 0xff, //0x00004466 testq        %rdi, %rdi
	0x0f, 0x85, 0xd3, 0xff, 0xff, 0xff, //0x00004469 jne          LBB0_815
	0xe9, 0x4a, 0x06, 0x00, 0x00, //0x0000446f jmp          LBB0_873
	//0x00004474 LBB0_818
	0x48, 0x85, 0xff, //0x00004474 testq        %rdi, %rdi
	0x0f, 0x84, 0xf2, 0x12, 0x00, 0x00, //0x00004477 je           LBB0_983
	0x48, 0x03, 0x75, 0xb0, //0x0000447d addq         $-80(%rbp), %rsi
	0x48, 0x83, 0xfa, 0xff, //0x00004481 cmpq         $-1, %rdx
	0x4c, 0x0f, 0x44, 0xfe, //0x00004485 cmoveq       %rsi, %r15
	0x48, 0x0f, 0x44, 0xd6, //0x00004489 cmoveq       %rsi, %rdx
	0x49, 0x83, 0xc2, 0x02, //0x0000448d addq         $2, %r10
	0x49, 0x83, 0xc0, 0xfe, //0x00004491 addq         $-2, %r8
	0x4c, 0x89, 0xc7, //0x00004495 movq         %r8, %rdi
	0x4c, 0x8b, 0x5d, 0xd0, //0x00004498 movq         $-48(%rbp), %r11
	0x4c, 0x8b, 0x75, 0xa8, //0x0000449c movq         $-88(%rbp), %r14
	0x48, 0x85, 0xff, //0x000044a0 testq        %rdi, %rdi
	0x0f, 0x85, 0x99, 0xff, 0xff, 0xff, //0x000044a3 jne          LBB0_815
	0xe9, 0x10, 0x06, 0x00, 0x00, //0x000044a9 jmp          LBB0_873
	//0x000044ae LBB0_694
	0x4c, 0x8b, 0x55, 0x80, //0x000044ae movq         $-128(%rbp), %r10
	0x4d, 0x89, 0xf5, //0x000044b2 movq         %r14, %r13
	0x49, 0x83, 0xfd, 0x20, //0x000044b5 cmpq         $32, %r13
	0x4c, 0x8b, 0x75, 0xa8, //0x000044b9 movq         $-88(%rbp), %r14
	0x0f, 0x82, 0xb1, 0x03, 0x00, 0x00, //0x000044bd jb           LBB0_847
	//0x000044c3 LBB0_695
	0xf3, 0x41, 0x0f, 0x6f, 0x12, //0x000044c3 movdqu       (%r10), %xmm2
	0xf3, 0x41, 0x0f, 0x6f, 0x62, 0x10, //0x000044c8 movdqu       $16(%r10), %xmm4
	0x66, 0x0f, 0x6f, 0xea, //0x000044ce movdqa       %xmm2, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x000044d2 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xfd, //0x000044d6 pmovmskb     %xmm5, %edi
	0x66, 0x0f, 0x6f, 0xec, //0x000044da movdqa       %xmm4, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x000044de pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xf5, //0x000044e2 pmovmskb     %xmm5, %esi
	0x66, 0x0f, 0x74, 0xd1, //0x000044e6 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x000044ea pmovmskb     %xmm2, %ecx
	0x66, 0x0f, 0x74, 0xe1, //0x000044ee pcmpeqb      %xmm1, %xmm4
	0x66, 0x0f, 0xd7, 0xc4, //0x000044f2 pmovmskb     %xmm4, %eax
	0x48, 0xc1, 0xe6, 0x10, //0x000044f6 shlq         $16, %rsi
	0x48, 0xc1, 0xe0, 0x10, //0x000044fa shlq         $16, %rax
	0x48, 0x09, 0xc1, //0x000044fe orq          %rax, %rcx
	0x49, 0x83, 0xfc, 0xff, //0x00004501 cmpq         $-1, %r12
	0x0f, 0x85, 0x9c, 0x00, 0x00, 0x00, //0x00004505 jne          LBB0_826
	0x48, 0x85, 0xc9, //0x0000450b testq        %rcx, %rcx
	0x4c, 0x8b, 0x5d, 0xd0, //0x0000450e movq         $-48(%rbp), %r11
	0x0f, 0x85, 0x23, 0x04, 0x00, 0x00, //0x00004512 jne          LBB0_854
	0x48, 0x09, 0xfe, //0x00004518 orq          %rdi, %rsi
	0x48, 0x89, 0xc8, //0x0000451b movq         %rcx, %rax
	0x4c, 0x09, 0xf8, //0x0000451e orq          %r15, %rax
	0x0f, 0x85, 0x93, 0x00, 0x00, 0x00, //0x00004521 jne          LBB0_827
	//0x00004527 LBB0_698
	0x48, 0x85, 0xf6, //0x00004527 testq        %rsi, %rsi
	0x0f, 0x84, 0xc3, 0x00, 0x00, 0x00, //0x0000452a je           LBB0_828
	//0x00004530 LBB0_699
	0x48, 0x0f, 0xbc, 0xc6, //0x00004530 bsfq         %rsi, %rax
	0x4c, 0x03, 0x55, 0xa0, //0x00004534 addq         $-96(%rbp), %r10
	0x49, 0x01, 0xc2, //0x00004538 addq         %rax, %r10
	0x4c, 0x8b, 0x6d, 0xc0, //0x0000453b movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x7d, 0xb8, //0x0000453f movq         $-72(%rbp), %r15
	0x4d, 0x85, 0xd2, //0x00004543 testq        %r10, %r10
	0x0f, 0x89, 0x44, 0xf7, 0xff, 0xff, //0x00004546 jns          LBB0_705
	0xe9, 0x5f, 0x0f, 0x00, 0x00, //0x0000454c jmp          LBB0_939
	//0x00004551 LBB0_820
	0x48, 0x85, 0xc9, //0x00004551 testq        %rcx, %rcx
	0x49, 0x8d, 0x47, 0xff, //0x00004554 leaq         $-1(%r15), %rax
	0x49, 0xf7, 0xd7, //0x00004558 notq         %r15
	0x4d, 0x0f, 0x48, 0xfd, //0x0000455b cmovsq       %r13, %r15
	0x49, 0x39, 0xc6, //0x0000455f cmpq         %rax, %r14
	0x4d, 0x0f, 0x45, 0xfd, //0x00004562 cmovneq      %r13, %r15
	0x4d, 0x89, 0xfa, //0x00004566 movq         %r15, %r10
	0x4d, 0x85, 0xd2, //0x00004569 testq        %r10, %r10
	0x0f, 0x89, 0x79, 0x01, 0x00, 0x00, //0x0000456c jns          LBB0_822
	0xe9, 0xa6, 0x0f, 0x00, 0x00, //0x00004572 jmp          LBB0_954
	//0x00004577 LBB0_823
	0x48, 0xf7, 0xd2, //0x00004577 notq         %rdx
	0x49, 0x89, 0xd2, //0x0000457a movq         %rdx, %r10
	0xe9, 0xc8, 0xf7, 0xff, 0xff, //0x0000457d jmp          LBB0_720
	//0x00004582 LBB0_824
	0x48, 0x2b, 0x75, 0xb8, //0x00004582 subq         $-72(%rbp), %rsi
	0x49, 0x89, 0xf2, //0x00004586 movq         %rsi, %r10
	0xe9, 0xf9, 0xf4, 0xff, 0xff, //0x00004589 jmp          LBB0_646
	//0x0000458e LBB0_825
	0x48, 0x89, 0x4d, 0xc8, //0x0000458e movq         %rcx, $-56(%rbp)
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00004592 movq         $-1, %rcx
	0x4d, 0x85, 0xd2, //0x00004599 testq        %r10, %r10
	0x0f, 0x85, 0x3a, 0xf7, 0xff, 0xff, //0x0000459c jne          LBB0_711
	0xe9, 0x2c, 0x0f, 0x00, 0x00, //0x000045a2 jmp          LBB0_943
	//0x000045a7 LBB0_826
	0x4c, 0x8b, 0x5d, 0xd0, //0x000045a7 movq         $-48(%rbp), %r11
	0x48, 0x09, 0xfe, //0x000045ab orq          %rdi, %rsi
	0x48, 0x89, 0xc8, //0x000045ae movq         %rcx, %rax
	0x4c, 0x09, 0xf8, //0x000045b1 orq          %r15, %rax
	0x0f, 0x84, 0x6d, 0xff, 0xff, 0xff, //0x000045b4 je           LBB0_698
	//0x000045ba LBB0_827
	0x44, 0x89, 0xf8, //0x000045ba movl         %r15d, %eax
	0xf7, 0xd0, //0x000045bd notl         %eax
	0x21, 0xc8, //0x000045bf andl         %ecx, %eax
	0x8d, 0x14, 0x00, //0x000045c1 leal         (%rax,%rax), %edx
	0x44, 0x09, 0xfa, //0x000045c4 orl          %r15d, %edx
	0x89, 0xd7, //0x000045c7 movl         %edx, %edi
	0xf7, 0xd7, //0x000045c9 notl         %edi
	0x21, 0xcf, //0x000045cb andl         %ecx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x000045cd andl         $-1431655766, %edi
	0x45, 0x31, 0xff, //0x000045d3 xorl         %r15d, %r15d
	0x01, 0xc7, //0x000045d6 addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc7, //0x000045d8 setb         %r15b
	0x01, 0xff, //0x000045dc addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x000045de xorl         $1431655765, %edi
	0x21, 0xd7, //0x000045e4 andl         %edx, %edi
	0xf7, 0xd7, //0x000045e6 notl         %edi
	0x21, 0xfe, //0x000045e8 andl         %edi, %esi
	0x48, 0x85, 0xf6, //0x000045ea testq        %rsi, %rsi
	0x0f, 0x85, 0x3d, 0xff, 0xff, 0xff, //0x000045ed jne          LBB0_699
	//0x000045f3 LBB0_828
	0x49, 0x83, 0xc2, 0x20, //0x000045f3 addq         $32, %r10
	0x49, 0x83, 0xc5, 0xe0, //0x000045f7 addq         $-32, %r13
	0x4d, 0x85, 0xff, //0x000045fb testq        %r15, %r15
	0x0f, 0x85, 0x7d, 0x02, 0x00, 0x00, //0x000045fe jne          LBB0_848
	//0x00004604 LBB0_829
	0x4c, 0x89, 0xe7, //0x00004604 movq         %r12, %rdi
	0x48, 0x8b, 0x45, 0xc0, //0x00004607 movq         $-64(%rbp), %rax
	0x4c, 0x8b, 0x7d, 0xb8, //0x0000460b movq         $-72(%rbp), %r15
	0x4d, 0x85, 0xed, //0x0000460f testq        %r13, %r13
	0x0f, 0x84, 0xa2, 0x0e, 0x00, 0x00, //0x00004612 je           LBB0_940
	//0x00004618 LBB0_830
	0x49, 0x8d, 0x4a, 0x01, //0x00004618 leaq         $1(%r10), %rcx
	0x41, 0x0f, 0xb6, 0x12, //0x0000461c movzbl       (%r10), %edx
	0x80, 0xfa, 0x22, //0x00004620 cmpb         $34, %dl
	0x0f, 0x84, 0x7a, 0x00, 0x00, 0x00, //0x00004623 je           LBB0_836
	0x49, 0x8d, 0x75, 0xff, //0x00004629 leaq         $-1(%r13), %rsi
	0x80, 0xfa, 0x5c, //0x0000462d cmpb         $92, %dl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00004630 je           LBB0_833
	0x49, 0x89, 0xf5, //0x00004636 movq         %rsi, %r13
	0x49, 0x89, 0xca, //0x00004639 movq         %rcx, %r10
	0x48, 0x85, 0xf6, //0x0000463c testq        %rsi, %rsi
	0x0f, 0x85, 0xd3, 0xff, 0xff, 0xff, //0x0000463f jne          LBB0_830
	0xe9, 0x70, 0x0e, 0x00, 0x00, //0x00004645 jmp          LBB0_940
	//0x0000464a LBB0_833
	0x48, 0x85, 0xf6, //0x0000464a testq        %rsi, %rsi
	0x0f, 0x84, 0x33, 0x04, 0x00, 0x00, //0x0000464d je           LBB0_868
	0x48, 0x03, 0x4d, 0xb0, //0x00004653 addq         $-80(%rbp), %rcx
	0x48, 0x83, 0xff, 0xff, //0x00004657 cmpq         $-1, %rdi
	0x4c, 0x0f, 0x44, 0xe1, //0x0000465b cmoveq       %rcx, %r12
	0x48, 0x0f, 0x44, 0xf9, //0x0000465f cmoveq       %rcx, %rdi
	0x49, 0x83, 0xc2, 0x02, //0x00004663 addq         $2, %r10
	0x49, 0x83, 0xc5, 0xfe, //0x00004667 addq         $-2, %r13
	0x4c, 0x89, 0xee, //0x0000466b movq         %r13, %rsi
	0x4c, 0x8b, 0x5d, 0xd0, //0x0000466e movq         $-48(%rbp), %r11
	0x48, 0x8b, 0x45, 0xc0, //0x00004672 movq         $-64(%rbp), %rax
	0x4c, 0x8b, 0x75, 0xa8, //0x00004676 movq         $-88(%rbp), %r14
	0x4c, 0x8b, 0x7d, 0xb8, //0x0000467a movq         $-72(%rbp), %r15
	0x48, 0x85, 0xf6, //0x0000467e testq        %rsi, %rsi
	0x0f, 0x85, 0x91, 0xff, 0xff, 0xff, //0x00004681 jne          LBB0_830
	0xe9, 0x2e, 0x0e, 0x00, 0x00, //0x00004687 jmp          LBB0_940
	//0x0000468c LBB0_835
	0x48, 0xf7, 0xd2, //0x0000468c notq         %rdx
	0x49, 0x89, 0xd2, //0x0000468f movq         %rdx, %r10
	0x4c, 0x8b, 0x5d, 0xd0, //0x00004692 movq         $-48(%rbp), %r11
	0x4c, 0x8b, 0x6d, 0xc0, //0x00004696 movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x7d, 0xb8, //0x0000469a movq         $-72(%rbp), %r15
	0xe9, 0x2a, 0xed, 0xff, 0xff, //0x0000469e jmp          LBB0_581
	//0x000046a3 LBB0_836
	0x4c, 0x29, 0xf9, //0x000046a3 subq         %r15, %rcx
	0x49, 0x89, 0xca, //0x000046a6 movq         %rcx, %r10
	0x49, 0x89, 0xc5, //0x000046a9 movq         %rax, %r13
	0x4d, 0x85, 0xd2, //0x000046ac testq        %r10, %r10
	0x0f, 0x89, 0xdb, 0xf5, 0xff, 0xff, //0x000046af jns          LBB0_705
	0xe9, 0xf6, 0x0d, 0x00, 0x00, //0x000046b5 jmp          LBB0_939
	//0x000046ba LBB0_837
	0x48, 0xf7, 0xd8, //0x000046ba negq         %rax
	0x4c, 0x8b, 0x5d, 0xd0, //0x000046bd movq         $-48(%rbp), %r11
	0x4c, 0x8b, 0x6d, 0xc0, //0x000046c1 movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x75, 0xa8, //0x000046c5 movq         $-88(%rbp), %r14
	0x4c, 0x8b, 0x7d, 0xb8, //0x000046c9 movq         $-72(%rbp), %r15
	0x49, 0x89, 0xc2, //0x000046cd movq         %rax, %r10
	0xe9, 0xf8, 0xec, 0xff, 0xff, //0x000046d0 jmp          LBB0_581
	//0x000046d5 LBB0_838
	0x4d, 0x29, 0xdc, //0x000046d5 subq         %r11, %r12
	0x44, 0x0f, 0xbc, 0xd7, //0x000046d8 bsfl         %edi, %r10d
	0x4d, 0x01, 0xe2, //0x000046dc addq         %r12, %r10
	//0x000046df LBB0_839
	0x49, 0xf7, 0xd2, //0x000046df notq         %r10
	0x4d, 0x85, 0xd2, //0x000046e2 testq        %r10, %r10
	0x0f, 0x88, 0x32, 0x0e, 0x00, 0x00, //0x000046e5 js           LBB0_954
	//0x000046eb LBB0_822
	0x48, 0x8b, 0x45, 0xc8, //0x000046eb movq         $-56(%rbp), %rax
	0x49, 0x01, 0xc2, //0x000046ef addq         %rax, %r10
	0x4c, 0x8b, 0x5d, 0xd0, //0x000046f2 movq         $-48(%rbp), %r11
	0x4d, 0x89, 0x13, //0x000046f6 movq         %r10, (%r11)
	0x4c, 0x89, 0xc9, //0x000046f9 movq         %r9, %rcx
	0x48, 0x85, 0xc0, //0x000046fc testq        %rax, %rax
	0x4c, 0x8b, 0x6d, 0xc0, //0x000046ff movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x75, 0xa8, //0x00004703 movq         $-88(%rbp), %r14
	0x4c, 0x8b, 0x7d, 0xb8, //0x00004707 movq         $-72(%rbp), %r15
	0x0f, 0x8f, 0xdf, 0xec, 0xff, 0xff, //0x0000470b jg           LBB0_584
	0xe9, 0x39, 0xeb, 0xff, 0xff, //0x00004711 jmp          LBB0_558
	//0x00004716 LBB0_732
	0x4c, 0x8b, 0x55, 0x80, //0x00004716 movq         $-128(%rbp), %r10
	0x4d, 0x89, 0xf5, //0x0000471a movq         %r14, %r13
	0x49, 0x83, 0xfd, 0x20, //0x0000471d cmpq         $32, %r13
	0x4c, 0x8b, 0x75, 0xa8, //0x00004721 movq         $-88(%rbp), %r14
	0x0f, 0x82, 0x90, 0x02, 0x00, 0x00, //0x00004725 jb           LBB0_858
	//0x0000472b LBB0_733
	0xf3, 0x41, 0x0f, 0x6f, 0x12, //0x0000472b movdqu       (%r10), %xmm2
	0xf3, 0x41, 0x0f, 0x6f, 0x62, 0x10, //0x00004730 movdqu       $16(%r10), %xmm4
	0x66, 0x0f, 0x6f, 0xea, //0x00004736 movdqa       %xmm2, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x0000473a pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xfd, //0x0000473e pmovmskb     %xmm5, %edi
	0x66, 0x0f, 0x6f, 0xec, //0x00004742 movdqa       %xmm4, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00004746 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xf5, //0x0000474a pmovmskb     %xmm5, %esi
	0x66, 0x0f, 0x74, 0xd1, //0x0000474e pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x00004752 pmovmskb     %xmm2, %ecx
	0x66, 0x0f, 0x74, 0xe1, //0x00004756 pcmpeqb      %xmm1, %xmm4
	0x66, 0x0f, 0xd7, 0xc4, //0x0000475a pmovmskb     %xmm4, %eax
	0x48, 0xc1, 0xe6, 0x10, //0x0000475e shlq         $16, %rsi
	0x48, 0xc1, 0xe0, 0x10, //0x00004762 shlq         $16, %rax
	0x48, 0x09, 0xc1, //0x00004766 orq          %rax, %rcx
	0x49, 0x83, 0xfc, 0xff, //0x00004769 cmpq         $-1, %r12
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x0000476d jne          LBB0_735
	0x48, 0x85, 0xc9, //0x00004773 testq        %rcx, %rcx
	0x0f, 0x85, 0xe1, 0x01, 0x00, 0x00, //0x00004776 jne          LBB0_855
	//0x0000477c LBB0_735
	0x48, 0x09, 0xfe, //0x0000477c orq          %rdi, %rsi
	0x48, 0x89, 0xc8, //0x0000477f movq         %rcx, %rax
	0x4c, 0x09, 0xf8, //0x00004782 orq          %r15, %rax
	0x0f, 0x85, 0xef, 0x01, 0x00, 0x00, //0x00004785 jne          LBB0_856
	//0x0000478b LBB0_736
	0x48, 0x85, 0xf6, //0x0000478b testq        %rsi, %rsi
	0x0f, 0x84, 0x1f, 0x02, 0x00, 0x00, //0x0000478e je           LBB0_857
	//0x00004794 LBB0_737
	0x48, 0x0f, 0xbc, 0xc6, //0x00004794 bsfq         %rsi, %rax
	0x4c, 0x03, 0x55, 0xa0, //0x00004798 addq         $-96(%rbp), %r10
	0x49, 0x01, 0xc2, //0x0000479c addq         %rax, %r10
	0x4c, 0x8b, 0x7d, 0xb8, //0x0000479f movq         $-72(%rbp), %r15
	0x4d, 0x85, 0xd2, //0x000047a3 testq        %r10, %r10
	0x4c, 0x8b, 0x6d, 0xc0, //0x000047a6 movq         $-64(%rbp), %r13
	0x0f, 0x89, 0xae, 0xfa, 0xff, 0xff, //0x000047aa jns          LBB0_795
	0xe9, 0xfb, 0x0c, 0x00, 0x00, //0x000047b0 jmp          LBB0_939
	//0x000047b5 LBB0_840
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x000047b5 movq         $-1, %r15
	0x45, 0x31, 0xed, //0x000047bc xorl         %r13d, %r13d
	0x49, 0x83, 0xf8, 0x20, //0x000047bf cmpq         $32, %r8
	0x0f, 0x83, 0x4d, 0xfb, 0xff, 0xff, //0x000047c3 jae          LBB0_633
	//0x000047c9 LBB0_841
	0x4c, 0x8b, 0x5d, 0xd0, //0x000047c9 movq         $-48(%rbp), %r11
	0x4c, 0x8b, 0x75, 0xa8, //0x000047cd movq         $-88(%rbp), %r14
	0x4d, 0x85, 0xed, //0x000047d1 testq        %r13, %r13
	0x0f, 0x84, 0x5c, 0xfc, 0xff, 0xff, //0x000047d4 je           LBB0_814
	//0x000047da LBB0_842
	0x48, 0x89, 0xc6, //0x000047da movq         %rax, %rsi
	0x4d, 0x85, 0xc0, //0x000047dd testq        %r8, %r8
	0x0f, 0x84, 0x7d, 0x0f, 0x00, 0x00, //0x000047e0 je           LBB0_982
	0x48, 0x8b, 0x45, 0xb0, //0x000047e6 movq         $-80(%rbp), %rax
	0x49, 0x8d, 0x0c, 0x02, //0x000047ea leaq         (%r10,%rax), %rcx
	0x49, 0x83, 0xff, 0xff, //0x000047ee cmpq         $-1, %r15
	0x4c, 0x89, 0xfa, //0x000047f2 movq         %r15, %rdx
	0x4c, 0x0f, 0x44, 0xf9, //0x000047f5 cmoveq       %rcx, %r15
	0x48, 0x0f, 0x44, 0xd1, //0x000047f9 cmoveq       %rcx, %rdx
	0x49, 0xff, 0xc2, //0x000047fd incq         %r10
	0x49, 0xff, 0xc8, //0x00004800 decq         %r8
	0x4c, 0x8b, 0x5d, 0xd0, //0x00004803 movq         $-48(%rbp), %r11
	0x4c, 0x8b, 0x75, 0xa8, //0x00004807 movq         $-88(%rbp), %r14
	0x48, 0x89, 0xf0, //0x0000480b movq         %rsi, %rax
	0x4d, 0x85, 0xc0, //0x0000480e testq        %r8, %r8
	0x0f, 0x85, 0x2b, 0xfc, 0xff, 0xff, //0x00004811 jne          LBB0_815
	0xe9, 0xa2, 0x02, 0x00, 0x00, //0x00004817 jmp          LBB0_873
	//0x0000481c LBB0_844
	0x49, 0x89, 0xcd, //0x0000481c movq         %rcx, %r13
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x0000481f movq         $-1, %r10
	0x4d, 0x85, 0xff, //0x00004826 testq        %r15, %r15
	0x0f, 0x85, 0x63, 0xfa, 0xff, 0xff, //0x00004829 jne          LBB0_798
	0xe9, 0xe9, 0x0c, 0x00, 0x00, //0x0000482f jmp          LBB0_954
	//0x00004834 LBB0_845
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00004834 movq         $-1, %r8
	0x4c, 0x89, 0x65, 0xc8, //0x0000483b movq         %r12, $-56(%rbp)
	0x49, 0x89, 0xff, //0x0000483f movq         %rdi, %r15
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00004842 movq         $-1, %r10
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00004849 movq         $-1, %r11
	0x48, 0x8d, 0x3d, 0xf5, 0x15, 0x00, 0x00, //0x00004850 leaq         $5621(%rip), %rdi  /* LJTI0_5+0(%rip) */
	0xe9, 0x67, 0xf1, 0xff, 0xff, //0x00004857 jmp          LBB0_672
	//0x0000485c LBB0_846
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x0000485c movq         $-1, %r12
	0x45, 0x31, 0xff, //0x00004863 xorl         %r15d, %r15d
	0x49, 0x83, 0xfd, 0x20, //0x00004866 cmpq         $32, %r13
	0x4c, 0x8b, 0x75, 0xa8, //0x0000486a movq         $-88(%rbp), %r14
	0x0f, 0x83, 0x4f, 0xfc, 0xff, 0xff, //0x0000486e jae          LBB0_695
	//0x00004874 LBB0_847
	0x4c, 0x8b, 0x5d, 0xd0, //0x00004874 movq         $-48(%rbp), %r11
	0x4d, 0x85, 0xff, //0x00004878 testq        %r15, %r15
	0x0f, 0x84, 0x83, 0xfd, 0xff, 0xff, //0x0000487b je           LBB0_829
	//0x00004881 LBB0_848
	0x4d, 0x85, 0xed, //0x00004881 testq        %r13, %r13
	0x0f, 0x84, 0xfc, 0x01, 0x00, 0x00, //0x00004884 je           LBB0_868
	0x48, 0x8b, 0x45, 0xb0, //0x0000488a movq         $-80(%rbp), %rax
	0x4c, 0x01, 0xd0, //0x0000488e addq         %r10, %rax
	0x49, 0x83, 0xfc, 0xff, //0x00004891 cmpq         $-1, %r12
	0x4c, 0x89, 0xe7, //0x00004895 movq         %r12, %rdi
	0x4c, 0x0f, 0x44, 0xe0, //0x00004898 cmoveq       %rax, %r12
	0x48, 0x0f, 0x44, 0xf8, //0x0000489c cmoveq       %rax, %rdi
	0x49, 0xff, 0xc2, //0x000048a0 incq         %r10
	0x49, 0xff, 0xcd, //0x000048a3 decq         %r13
	0x4c, 0x8b, 0x5d, 0xd0, //0x000048a6 movq         $-48(%rbp), %r11
	0x48, 0x8b, 0x45, 0xc0, //0x000048aa movq         $-64(%rbp), %rax
	0x4c, 0x8b, 0x75, 0xa8, //0x000048ae movq         $-88(%rbp), %r14
	0x4c, 0x8b, 0x7d, 0xb8, //0x000048b2 movq         $-72(%rbp), %r15
	0x4d, 0x85, 0xed, //0x000048b6 testq        %r13, %r13
	0x0f, 0x85, 0x59, 0xfd, 0xff, 0xff, //0x000048b9 jne          LBB0_830
	0xe9, 0xf6, 0x0b, 0x00, 0x00, //0x000048bf jmp          LBB0_940
	//0x000048c4 LBB0_852
	0x49, 0xf7, 0xda, //0x000048c4 negq         %r10
	0x4d, 0x85, 0xd2, //0x000048c7 testq        %r10, %r10
	0x0f, 0x89, 0x1b, 0xfe, 0xff, 0xff, //0x000048ca jns          LBB0_822
	0xe9, 0x48, 0x0c, 0x00, 0x00, //0x000048d0 jmp          LBB0_954
	//0x000048d5 LBB0_850
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x000048d5 movq         $-1, %r12
	0x45, 0x31, 0xff, //0x000048dc xorl         %r15d, %r15d
	0x49, 0x83, 0xfd, 0x20, //0x000048df cmpq         $32, %r13
	0x4c, 0x8b, 0x75, 0xa8, //0x000048e3 movq         $-88(%rbp), %r14
	0x0f, 0x83, 0x3e, 0xfe, 0xff, 0xff, //0x000048e7 jae          LBB0_733
	0xe9, 0xc9, 0x00, 0x00, 0x00, //0x000048ed jmp          LBB0_858
	//0x000048f2 LBB0_851
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x000048f2 movq         $-1, %r14
	0x4d, 0x89, 0xdd, //0x000048f9 movq         %r11, %r13
	0x4d, 0x89, 0xf8, //0x000048fc movq         %r15, %r8
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x000048ff movq         $-1, %r15
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00004906 movq         $-1, %rbx
	0x48, 0x8d, 0x3d, 0x4c, 0x14, 0x00, 0x00, //0x0000490d leaq         $5196(%rip), %rdi  /* LJTI0_4+0(%rip) */
	0xe9, 0xcd, 0xf7, 0xff, 0xff, //0x00004914 jmp          LBB0_763
	//0x00004919 LBB0_853
	0x4c, 0x89, 0xd1, //0x00004919 movq         %r10, %rcx
	0x48, 0x2b, 0x4d, 0xb8, //0x0000491c subq         $-72(%rbp), %rcx
	0x4c, 0x0f, 0xbc, 0xff, //0x00004920 bsfq         %rdi, %r15
	0x49, 0x01, 0xcf, //0x00004924 addq         %rcx, %r15
	0x48, 0x09, 0xde, //0x00004927 orq          %rbx, %rsi
	0x48, 0x89, 0xf9, //0x0000492a movq         %rdi, %rcx
	0x4c, 0x09, 0xe9, //0x0000492d orq          %r13, %rcx
	0x0f, 0x84, 0x48, 0xfa, 0xff, 0xff, //0x00004930 je           LBB0_636
	0xe9, 0xb1, 0xfa, 0xff, 0xff, //0x00004936 jmp          LBB0_812
	//0x0000493b LBB0_854
	0x4c, 0x89, 0xd0, //0x0000493b movq         %r10, %rax
	0x48, 0x2b, 0x45, 0xb8, //0x0000493e subq         $-72(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xe1, //0x00004942 bsfq         %rcx, %r12
	0x49, 0x01, 0xc4, //0x00004946 addq         %rax, %r12
	0x48, 0x09, 0xfe, //0x00004949 orq          %rdi, %rsi
	0x48, 0x89, 0xc8, //0x0000494c movq         %rcx, %rax
	0x4c, 0x09, 0xf8, //0x0000494f orq          %r15, %rax
	0x0f, 0x84, 0xcf, 0xfb, 0xff, 0xff, //0x00004952 je           LBB0_698
	0xe9, 0x5d, 0xfc, 0xff, 0xff, //0x00004958 jmp          LBB0_827
	//0x0000495d LBB0_855
	0x4c, 0x89, 0xd0, //0x0000495d movq         %r10, %rax
	0x48, 0x2b, 0x45, 0xb8, //0x00004960 subq         $-72(%rbp), %rax
	0x4c, 0x0f, 0xbc, 0xe1, //0x00004964 bsfq         %rcx, %r12
	0x49, 0x01, 0xc4, //0x00004968 addq         %rax, %r12
	0x48, 0x09, 0xfe, //0x0000496b orq          %rdi, %rsi
	0x48, 0x89, 0xc8, //0x0000496e movq         %rcx, %rax
	0x4c, 0x09, 0xf8, //0x00004971 orq          %r15, %rax
	0x0f, 0x84, 0x11, 0xfe, 0xff, 0xff, //0x00004974 je           LBB0_736
	//0x0000497a LBB0_856
	0x44, 0x89, 0xf8, //0x0000497a movl         %r15d, %eax
	0xf7, 0xd0, //0x0000497d notl         %eax
	0x21, 0xc8, //0x0000497f andl         %ecx, %eax
	0x8d, 0x14, 0x00, //0x00004981 leal         (%rax,%rax), %edx
	0x44, 0x09, 0xfa, //0x00004984 orl          %r15d, %edx
	0x89, 0xd7, //0x00004987 movl         %edx, %edi
	0xf7, 0xd7, //0x00004989 notl         %edi
	0x21, 0xcf, //0x0000498b andl         %ecx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000498d andl         $-1431655766, %edi
	0x45, 0x31, 0xff, //0x00004993 xorl         %r15d, %r15d
	0x01, 0xc7, //0x00004996 addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc7, //0x00004998 setb         %r15b
	0x01, 0xff, //0x0000499c addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x0000499e xorl         $1431655765, %edi
	0x21, 0xd7, //0x000049a4 andl         %edx, %edi
	0xf7, 0xd7, //0x000049a6 notl         %edi
	0x21, 0xfe, //0x000049a8 andl         %edi, %esi
	0x48, 0x85, 0xf6, //0x000049aa testq        %rsi, %rsi
	0x0f, 0x85, 0xe1, 0xfd, 0xff, 0xff, //0x000049ad jne          LBB0_737
	//0x000049b3 LBB0_857
	0x49, 0x83, 0xc2, 0x20, //0x000049b3 addq         $32, %r10
	0x49, 0x83, 0xc5, 0xe0, //0x000049b7 addq         $-32, %r13
	//0x000049bb LBB0_858
	0x4d, 0x85, 0xff, //0x000049bb testq        %r15, %r15
	0x0f, 0x85, 0x94, 0x00, 0x00, 0x00, //0x000049be jne          LBB0_866
	0x4c, 0x89, 0xe7, //0x000049c4 movq         %r12, %rdi
	0x4d, 0x85, 0xed, //0x000049c7 testq        %r13, %r13
	0x0f, 0x84, 0xb6, 0x00, 0x00, 0x00, //0x000049ca je           LBB0_868
	//0x000049d0 LBB0_860
	0x49, 0x8d, 0x4a, 0x01, //0x000049d0 leaq         $1(%r10), %rcx
	0x41, 0x0f, 0xb6, 0x12, //0x000049d4 movzbl       (%r10), %edx
	0x80, 0xfa, 0x22, //0x000049d8 cmpb         $34, %dl
	0x0f, 0x84, 0x53, 0x00, 0x00, 0x00, //0x000049db je           LBB0_865
	0x49, 0x8d, 0x75, 0xff, //0x000049e1 leaq         $-1(%r13), %rsi
	0x80, 0xfa, 0x5c, //0x000049e5 cmpb         $92, %dl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x000049e8 je           LBB0_863
	0x49, 0x89, 0xf5, //0x000049ee movq         %rsi, %r13
	0x49, 0x89, 0xca, //0x000049f1 movq         %rcx, %r10
	0x48, 0x85, 0xf6, //0x000049f4 testq        %rsi, %rsi
	0x0f, 0x85, 0xd3, 0xff, 0xff, 0xff, //0x000049f7 jne          LBB0_860
	0xe9, 0x84, 0x00, 0x00, 0x00, //0x000049fd jmp          LBB0_868
	//0x00004a02 LBB0_863
	0x48, 0x85, 0xf6, //0x00004a02 testq        %rsi, %rsi
	0x0f, 0x84, 0x7b, 0x00, 0x00, 0x00, //0x00004a05 je           LBB0_868
	0x48, 0x03, 0x4d, 0xb0, //0x00004a0b addq         $-80(%rbp), %rcx
	0x48, 0x83, 0xff, 0xff, //0x00004a0f cmpq         $-1, %rdi
	0x4c, 0x0f, 0x44, 0xe1, //0x00004a13 cmoveq       %rcx, %r12
	0x48, 0x0f, 0x44, 0xf9, //0x00004a17 cmoveq       %rcx, %rdi
	0x49, 0x83, 0xc2, 0x02, //0x00004a1b addq         $2, %r10
	0x49, 0x83, 0xc5, 0xfe, //0x00004a1f addq         $-2, %r13
	0x4c, 0x89, 0xee, //0x00004a23 movq         %r13, %rsi
	0x48, 0x85, 0xf6, //0x00004a26 testq        %rsi, %rsi
	0x0f, 0x85, 0xa1, 0xff, 0xff, 0xff, //0x00004a29 jne          LBB0_860
	0xe9, 0x52, 0x00, 0x00, 0x00, //0x00004a2f jmp          LBB0_868
	//0x00004a34 LBB0_865
	0x4c, 0x8b, 0x7d, 0xb8, //0x00004a34 movq         $-72(%rbp), %r15
	0x4c, 0x29, 0xf9, //0x00004a38 subq         %r15, %rcx
	0x49, 0x89, 0xca, //0x00004a3b movq         %rcx, %r10
	0x4c, 0x8b, 0x5d, 0xd0, //0x00004a3e movq         $-48(%rbp), %r11
	0x4c, 0x8b, 0x75, 0xa8, //0x00004a42 movq         $-88(%rbp), %r14
	0x4d, 0x85, 0xd2, //0x00004a46 testq        %r10, %r10
	0x4c, 0x8b, 0x6d, 0xc0, //0x00004a49 movq         $-64(%rbp), %r13
	0x0f, 0x89, 0x0b, 0xf8, 0xff, 0xff, //0x00004a4d jns          LBB0_795
	0xe9, 0x58, 0x0a, 0x00, 0x00, //0x00004a53 jmp          LBB0_939
	//0x00004a58 LBB0_866
	0x4d, 0x85, 0xed, //0x00004a58 testq        %r13, %r13
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x00004a5b je           LBB0_868
	0x48, 0x8b, 0x45, 0xb0, //0x00004a61 movq         $-80(%rbp), %rax
	0x4c, 0x01, 0xd0, //0x00004a65 addq         %r10, %rax
	0x49, 0x83, 0xfc, 0xff, //0x00004a68 cmpq         $-1, %r12
	0x4c, 0x89, 0xe7, //0x00004a6c movq         %r12, %rdi
	0x4c, 0x0f, 0x44, 0xe0, //0x00004a6f cmoveq       %rax, %r12
	0x48, 0x0f, 0x44, 0xf8, //0x00004a73 cmoveq       %rax, %rdi
	0x49, 0xff, 0xc2, //0x00004a77 incq         %r10
	0x49, 0xff, 0xcd, //0x00004a7a decq         %r13
	0x4d, 0x85, 0xed, //0x00004a7d testq        %r13, %r13
	0x0f, 0x85, 0x4a, 0xff, 0xff, 0xff, //0x00004a80 jne          LBB0_860
	//0x00004a86 LBB0_868
	0x4c, 0x8b, 0x5d, 0xd0, //0x00004a86 movq         $-48(%rbp), %r11
	0xe9, 0x2b, 0x0a, 0x00, 0x00, //0x00004a8a jmp          LBB0_940
	//0x00004a8f LBB0_869
	0x49, 0x89, 0x3b, //0x00004a8f movq         %rdi, (%r11)
	//0x00004a92 LBB0_870
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00004a92 movq         $-1, %rcx
	0xe9, 0xb1, 0xe7, 0xff, 0xff, //0x00004a99 jmp          LBB0_558
	//0x00004a9e LBB0_955
	0x48, 0xc7, 0xc1, 0xf9, 0xff, 0xff, 0xff, //0x00004a9e movq         $-7, %rcx
	0xe9, 0xa5, 0xe7, 0xff, 0xff, //0x00004aa5 jmp          LBB0_558
	//0x00004aaa LBB0_871
	0x49, 0x8d, 0x44, 0x24, 0x04, //0x00004aaa leaq         $4(%r12), %rax
	0xe9, 0x7c, 0x05, 0x00, 0x00, //0x00004aaf jmp          LBB0_912
	//0x00004ab4 LBB0_872
	0x49, 0x83, 0xfa, 0xff, //0x00004ab4 cmpq         $-1, %r10
	0x0f, 0x85, 0x0a, 0x00, 0x00, 0x00, //0x00004ab8 jne          LBB0_874
	//0x00004abe LBB0_873
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00004abe movq         $-1, %r10
	0x49, 0x89, 0xc7, //0x00004ac5 movq         %rax, %r15
	//0x00004ac8 LBB0_874
	0x4d, 0x89, 0x3b, //0x00004ac8 movq         %r15, (%r11)
	0x4c, 0x89, 0xd1, //0x00004acb movq         %r10, %rcx
	0xe9, 0x7c, 0xe7, 0xff, 0xff, //0x00004ace jmp          LBB0_558
	//0x00004ad3 LBB0_875
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00004ad3 movq         $-1, %rcx
	0xe9, 0xf4, 0x09, 0x00, 0x00, //0x00004ada jmp          LBB0_943
	//0x00004adf LBB0_876
	0x4d, 0x89, 0x23, //0x00004adf movq         %r12, (%r11)
	0xe9, 0x61, 0xe7, 0xff, 0xff, //0x00004ae2 jmp          LBB0_557
	//0x00004ae7 LBB0_877
	0x4d, 0x8b, 0x4d, 0x08, //0x00004ae7 movq         $8(%r13), %r9
	0x4d, 0x89, 0xce, //0x00004aeb movq         %r9, %r14
	0x49, 0x29, 0xfe, //0x00004aee subq         %rdi, %r14
	0x49, 0x83, 0xfe, 0x20, //0x00004af1 cmpq         $32, %r14
	0x0f, 0x8c, 0x0c, 0x0c, 0x00, 0x00, //0x00004af5 jl           LBB0_975
	0x41, 0xba, 0xff, 0xff, 0xff, 0xff, //0x00004afb movl         $4294967295, %r10d
	0x4f, 0x8d, 0x1c, 0x20, //0x00004b01 leaq         (%r8,%r12), %r11
	0x4d, 0x29, 0xe1, //0x00004b05 subq         %r12, %r9
	0xbe, 0x1f, 0x00, 0x00, 0x00, //0x00004b08 movl         $31, %esi
	0x45, 0x31, 0xf6, //0x00004b0d xorl         %r14d, %r14d
	0xf3, 0x0f, 0x6f, 0x05, 0x18, 0xb5, 0xff, 0xff, //0x00004b10 movdqu       $-19176(%rip), %xmm0  /* LCPI0_3+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0x20, 0xb5, 0xff, 0xff, //0x00004b18 movdqu       $-19168(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0x45, 0x31, 0xff, //0x00004b20 xorl         %r15d, %r15d
	0xe9, 0x22, 0x00, 0x00, 0x00, //0x00004b23 jmp          LBB0_879
	//0x00004b28 LBB0_882
	0x45, 0x31, 0xff, //0x00004b28 xorl         %r15d, %r15d
	0x85, 0xff, //0x00004b2b testl        %edi, %edi
	0x0f, 0x85, 0xa6, 0x00, 0x00, 0x00, //0x00004b2d jne          LBB0_881
	//0x00004b33 LBB0_883
	0x49, 0x83, 0xc6, 0x20, //0x00004b33 addq         $32, %r14
	0x49, 0x8d, 0x44, 0x31, 0xe0, //0x00004b37 leaq         $-32(%r9,%rsi), %rax
	0x48, 0x83, 0xc6, 0xe0, //0x00004b3c addq         $-32, %rsi
	0x48, 0x83, 0xf8, 0x3f, //0x00004b40 cmpq         $63, %rax
	0x0f, 0x8e, 0x02, 0x0b, 0x00, 0x00, //0x00004b44 jle          LBB0_884
	//0x00004b4a LBB0_879
	0xf3, 0x43, 0x0f, 0x6f, 0x54, 0x33, 0x01, //0x00004b4a movdqu       $1(%r11,%r14), %xmm2
	0xf3, 0x43, 0x0f, 0x6f, 0x5c, 0x33, 0x11, //0x00004b51 movdqu       $17(%r11,%r14), %xmm3
	0x66, 0x0f, 0x6f, 0xe2, //0x00004b58 movdqa       %xmm2, %xmm4
	0x66, 0x0f, 0x74, 0xe0, //0x00004b5c pcmpeqb      %xmm0, %xmm4
	0x66, 0x0f, 0xd7, 0xc4, //0x00004b60 pmovmskb     %xmm4, %eax
	0x66, 0x0f, 0x6f, 0xe3, //0x00004b64 movdqa       %xmm3, %xmm4
	0x66, 0x0f, 0x74, 0xe0, //0x00004b68 pcmpeqb      %xmm0, %xmm4
	0x66, 0x0f, 0xd7, 0xfc, //0x00004b6c pmovmskb     %xmm4, %edi
	0x48, 0xc1, 0xe7, 0x10, //0x00004b70 shlq         $16, %rdi
	0x48, 0x09, 0xc7, //0x00004b74 orq          %rax, %rdi
	0x66, 0x0f, 0x74, 0xd1, //0x00004b77 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xda, //0x00004b7b pmovmskb     %xmm2, %ebx
	0x66, 0x0f, 0x74, 0xd9, //0x00004b7f pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xc3, //0x00004b83 pmovmskb     %xmm3, %eax
	0x48, 0xc1, 0xe0, 0x10, //0x00004b87 shlq         $16, %rax
	0x48, 0x09, 0xd8, //0x00004b8b orq          %rbx, %rax
	0x48, 0x89, 0xc3, //0x00004b8e movq         %rax, %rbx
	0x4c, 0x09, 0xfb, //0x00004b91 orq          %r15, %rbx
	0x0f, 0x84, 0x8e, 0xff, 0xff, 0xff, //0x00004b94 je           LBB0_882
	0x44, 0x89, 0xfb, //0x00004b9a movl         %r15d, %ebx
	0x44, 0x31, 0xd3, //0x00004b9d xorl         %r10d, %ebx
	0x21, 0xd8, //0x00004ba0 andl         %ebx, %eax
	0x8d, 0x1c, 0x00, //0x00004ba2 leal         (%rax,%rax), %ebx
	0x44, 0x09, 0xfb, //0x00004ba5 orl          %r15d, %ebx
	0x41, 0x8d, 0x92, 0xab, 0xaa, 0xaa, 0xaa, //0x00004ba8 leal         $-1431655765(%r10), %edx
	0x31, 0xda, //0x00004baf xorl         %ebx, %edx
	0x21, 0xc2, //0x00004bb1 andl         %eax, %edx
	0x81, 0xe2, 0xaa, 0xaa, 0xaa, 0xaa, //0x00004bb3 andl         $-1431655766, %edx
	0x45, 0x31, 0xff, //0x00004bb9 xorl         %r15d, %r15d
	0x01, 0xc2, //0x00004bbc addl         %eax, %edx
	0x41, 0x0f, 0x92, 0xc7, //0x00004bbe setb         %r15b
	0x01, 0xd2, //0x00004bc2 addl         %edx, %edx
	0x81, 0xf2, 0x55, 0x55, 0x55, 0x55, //0x00004bc4 xorl         $1431655765, %edx
	0x21, 0xda, //0x00004bca andl         %ebx, %edx
	0x44, 0x31, 0xd2, //0x00004bcc xorl         %r10d, %edx
	0x21, 0xd7, //0x00004bcf andl         %edx, %edi
	0x85, 0xff, //0x00004bd1 testl        %edi, %edi
	0x0f, 0x84, 0x5a, 0xff, 0xff, 0xff, //0x00004bd3 je           LBB0_883
	//0x00004bd9 LBB0_881
	0x48, 0x0f, 0xbc, 0xc7, //0x00004bd9 bsfq         %rdi, %rax
	0x49, 0x01, 0xc3, //0x00004bdd addq         %rax, %r11
	0x4d, 0x01, 0xf3, //0x00004be0 addq         %r14, %r11
	0x4d, 0x29, 0xc3, //0x00004be3 subq         %r8, %r11
	0x49, 0x83, 0xc3, 0x02, //0x00004be6 addq         $2, %r11
	0x48, 0x8b, 0x45, 0xd0, //0x00004bea movq         $-48(%rbp), %rax
	0x4c, 0x89, 0x18, //0x00004bee movq         %r11, (%rax)
	0x4c, 0x89, 0xe1, //0x00004bf1 movq         %r12, %rcx
	0xe9, 0x56, 0xe6, 0xff, 0xff, //0x00004bf4 jmp          LBB0_558
	//0x00004bf9 LBB0_887
	0x49, 0xbb, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00004bf9 movabsq      $6148914691236517205, %r11
	0x49, 0x8b, 0x45, 0x08, //0x00004c03 movq         $8(%r13), %rax
	0x48, 0x29, 0xf8, //0x00004c07 subq         %rdi, %rax
	0x49, 0x01, 0xf8, //0x00004c0a addq         %rdi, %r8
	0x45, 0x31, 0xff, //0x00004c0d xorl         %r15d, %r15d
	0xf3, 0x44, 0x0f, 0x6f, 0x15, 0x17, 0xb4, 0xff, 0xff, //0x00004c10 movdqu       $-19433(%rip), %xmm10  /* LCPI0_3+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0x1f, 0xb4, 0xff, 0xff, //0x00004c19 movdqu       $-19425(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0x66, 0x45, 0x0f, 0x76, 0xc9, //0x00004c21 pcmpeqd      %xmm9, %xmm9
	0xf3, 0x0f, 0x6f, 0x1d, 0x32, 0xb4, 0xff, 0xff, //0x00004c26 movdqu       $-19406(%rip), %xmm3  /* LCPI0_6+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x25, 0x3a, 0xb4, 0xff, 0xff, //0x00004c2e movdqu       $-19398(%rip), %xmm4  /* LCPI0_7+0(%rip) */
	0x49, 0xbd, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x00004c36 movabsq      $3689348814741910323, %r13
	0x66, 0x45, 0x0f, 0xef, 0xc0, //0x00004c40 pxor         %xmm8, %xmm8
	0x31, 0xd2, //0x00004c45 xorl         %edx, %edx
	0x48, 0x89, 0x55, 0x98, //0x00004c47 movq         %rdx, $-104(%rbp)
	0x45, 0x31, 0xf6, //0x00004c4b xorl         %r14d, %r14d
	0x45, 0x31, 0xd2, //0x00004c4e xorl         %r10d, %r10d
	0x48, 0x83, 0xf8, 0x40, //0x00004c51 cmpq         $64, %rax
	0x48, 0x89, 0x45, 0xc8, //0x00004c55 movq         %rax, $-56(%rbp)
	0x4c, 0x89, 0x7d, 0x90, //0x00004c59 movq         %r15, $-112(%rbp)
	0x0f, 0x8c, 0x75, 0x02, 0x00, 0x00, //0x00004c5d jl           LBB0_896
	//0x00004c63 LBB0_890
	0xf3, 0x41, 0x0f, 0x6f, 0x00, //0x00004c63 movdqu       (%r8), %xmm0
	0xf3, 0x41, 0x0f, 0x6f, 0x68, 0x10, //0x00004c68 movdqu       $16(%r8), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x78, 0x20, //0x00004c6e movdqu       $32(%r8), %xmm7
	0xf3, 0x41, 0x0f, 0x6f, 0x70, 0x30, //0x00004c74 movdqu       $48(%r8), %xmm6
	0x66, 0x0f, 0x6f, 0xd0, //0x00004c7a movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00004c7e pcmpeqb      %xmm10, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xfa, //0x00004c83 pmovmskb     %xmm2, %r15d
	0x66, 0x0f, 0x6f, 0xd5, //0x00004c88 movdqa       %xmm5, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00004c8c pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xfa, //0x00004c91 pmovmskb     %xmm2, %edi
	0x66, 0x0f, 0x6f, 0xd7, //0x00004c95 movdqa       %xmm7, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00004c99 pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x00004c9e pmovmskb     %xmm2, %edx
	0x66, 0x0f, 0x6f, 0xd6, //0x00004ca2 movdqa       %xmm6, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00004ca6 pcmpeqb      %xmm10, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xca, //0x00004cab pmovmskb     %xmm2, %r9d
	0x49, 0xc1, 0xe1, 0x30, //0x00004cb0 shlq         $48, %r9
	0x48, 0xc1, 0xe2, 0x20, //0x00004cb4 shlq         $32, %rdx
	0x48, 0xc1, 0xe7, 0x10, //0x00004cb8 shlq         $16, %rdi
	0x49, 0x09, 0xff, //0x00004cbc orq          %rdi, %r15
	0x49, 0x09, 0xd7, //0x00004cbf orq          %rdx, %r15
	0x4d, 0x09, 0xcf, //0x00004cc2 orq          %r9, %r15
	0x66, 0x0f, 0x6f, 0xd0, //0x00004cc5 movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00004cc9 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xfa, //0x00004ccd pmovmskb     %xmm2, %edi
	0x66, 0x0f, 0x6f, 0xd5, //0x00004cd1 movdqa       %xmm5, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00004cd5 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x00004cd9 pmovmskb     %xmm2, %edx
	0x66, 0x0f, 0x6f, 0xd7, //0x00004cdd movdqa       %xmm7, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00004ce1 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00004ce5 pmovmskb     %xmm2, %eax
	0x66, 0x0f, 0x6f, 0xd6, //0x00004ce9 movdqa       %xmm6, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00004ced pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xda, //0x00004cf1 pmovmskb     %xmm2, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x00004cf5 shlq         $48, %rbx
	0x48, 0xc1, 0xe0, 0x20, //0x00004cf9 shlq         $32, %rax
	0x48, 0xc1, 0xe2, 0x10, //0x00004cfd shlq         $16, %rdx
	0x48, 0x09, 0xd7, //0x00004d01 orq          %rdx, %rdi
	0x48, 0x09, 0xc7, //0x00004d04 orq          %rax, %rdi
	0x48, 0x09, 0xdf, //0x00004d07 orq          %rbx, %rdi
	0x48, 0x89, 0xf8, //0x00004d0a movq         %rdi, %rax
	0x48, 0x8b, 0x55, 0x98, //0x00004d0d movq         $-104(%rbp), %rdx
	0x48, 0x09, 0xd0, //0x00004d11 orq          %rdx, %rax
	0x0f, 0x84, 0x43, 0x00, 0x00, 0x00, //0x00004d14 je           LBB0_892
	0x48, 0x89, 0xd0, //0x00004d1a movq         %rdx, %rax
	0x48, 0xf7, 0xd0, //0x00004d1d notq         %rax
	0x48, 0x21, 0xf8, //0x00004d20 andq         %rdi, %rax
	0x48, 0x8d, 0x1c, 0x00, //0x00004d23 leaq         (%rax,%rax), %rbx
	0x48, 0x09, 0xd3, //0x00004d27 orq          %rdx, %rbx
	0x48, 0x89, 0xda, //0x00004d2a movq         %rbx, %rdx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00004d2d movabsq      $-6148914691236517206, %rsi
	0x48, 0x31, 0xf2, //0x00004d37 xorq         %rsi, %rdx
	0x48, 0x21, 0xf7, //0x00004d3a andq         %rsi, %rdi
	0x48, 0x21, 0xd7, //0x00004d3d andq         %rdx, %rdi
	0x31, 0xd2, //0x00004d40 xorl         %edx, %edx
	0x48, 0x01, 0xc7, //0x00004d42 addq         %rax, %rdi
	0x0f, 0x92, 0xc2, //0x00004d45 setb         %dl
	0x48, 0x89, 0x55, 0x98, //0x00004d48 movq         %rdx, $-104(%rbp)
	0x48, 0x01, 0xff, //0x00004d4c addq         %rdi, %rdi
	0x4c, 0x31, 0xdf, //0x00004d4f xorq         %r11, %rdi
	0x48, 0x21, 0xdf, //0x00004d52 andq         %rbx, %rdi
	0x48, 0xf7, 0xd7, //0x00004d55 notq         %rdi
	0xe9, 0x0d, 0x00, 0x00, 0x00, //0x00004d58 jmp          LBB0_893
	//0x00004d5d LBB0_892
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00004d5d movq         $-1, %rdi
	0x31, 0xc0, //0x00004d64 xorl         %eax, %eax
	0x48, 0x89, 0x45, 0x98, //0x00004d66 movq         %rax, $-104(%rbp)
	//0x00004d6a LBB0_893
	0x48, 0x8b, 0x45, 0x90, //0x00004d6a movq         $-112(%rbp), %rax
	0x4c, 0x21, 0xff, //0x00004d6e andq         %r15, %rdi
	0x66, 0x48, 0x0f, 0x6e, 0xd7, //0x00004d71 movq         %rdi, %xmm2
	0x66, 0x41, 0x0f, 0x3a, 0x44, 0xd1, 0x00, //0x00004d76 pclmulqdq    $0, %xmm9, %xmm2
	0x66, 0x49, 0x0f, 0x7e, 0xd7, //0x00004d7d movq         %xmm2, %r15
	0x49, 0x31, 0xc7, //0x00004d82 xorq         %rax, %r15
	0x66, 0x0f, 0x6f, 0xd0, //0x00004d85 movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00004d89 pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xfa, //0x00004d8d pmovmskb     %xmm2, %edi
	0x66, 0x0f, 0x6f, 0xd5, //0x00004d91 movdqa       %xmm5, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00004d95 pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00004d99 pmovmskb     %xmm2, %eax
	0x66, 0x0f, 0x6f, 0xd7, //0x00004d9d movdqa       %xmm7, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00004da1 pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x00004da5 pmovmskb     %xmm2, %edx
	0x66, 0x0f, 0x6f, 0xd6, //0x00004da9 movdqa       %xmm6, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00004dad pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xda, //0x00004db1 pmovmskb     %xmm2, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x00004db5 shlq         $48, %rbx
	0x48, 0xc1, 0xe2, 0x20, //0x00004db9 shlq         $32, %rdx
	0x48, 0xc1, 0xe0, 0x10, //0x00004dbd shlq         $16, %rax
	0x48, 0x09, 0xc7, //0x00004dc1 orq          %rax, %rdi
	0x48, 0x09, 0xd7, //0x00004dc4 orq          %rdx, %rdi
	0x48, 0x09, 0xdf, //0x00004dc7 orq          %rbx, %rdi
	0x4d, 0x89, 0xf9, //0x00004dca movq         %r15, %r9
	0x49, 0xf7, 0xd1, //0x00004dcd notq         %r9
	0x4c, 0x21, 0xcf, //0x00004dd0 andq         %r9, %rdi
	0x66, 0x0f, 0x74, 0xc4, //0x00004dd3 pcmpeqb      %xmm4, %xmm0
	0x66, 0x0f, 0xd7, 0xd0, //0x00004dd7 pmovmskb     %xmm0, %edx
	0x66, 0x0f, 0x74, 0xec, //0x00004ddb pcmpeqb      %xmm4, %xmm5
	0x66, 0x0f, 0xd7, 0xdd, //0x00004ddf pmovmskb     %xmm5, %ebx
	0x66, 0x0f, 0x74, 0xfc, //0x00004de3 pcmpeqb      %xmm4, %xmm7
	0x66, 0x0f, 0xd7, 0xf7, //0x00004de7 pmovmskb     %xmm7, %esi
	0x66, 0x0f, 0x74, 0xf4, //0x00004deb pcmpeqb      %xmm4, %xmm6
	0x66, 0x0f, 0xd7, 0xc6, //0x00004def pmovmskb     %xmm6, %eax
	0x48, 0xc1, 0xe0, 0x30, //0x00004df3 shlq         $48, %rax
	0x48, 0xc1, 0xe6, 0x20, //0x00004df7 shlq         $32, %rsi
	0x48, 0xc1, 0xe3, 0x10, //0x00004dfb shlq         $16, %rbx
	0x48, 0x09, 0xda, //0x00004dff orq          %rbx, %rdx
	0x48, 0x09, 0xf2, //0x00004e02 orq          %rsi, %rdx
	0x48, 0x09, 0xc2, //0x00004e05 orq          %rax, %rdx
	0x48, 0xbb, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00004e08 movabsq      $1085102592571150095, %rbx
	0x4c, 0x21, 0xca, //0x00004e12 andq         %r9, %rdx
	0x0f, 0x84, 0x5d, 0x00, 0x00, 0x00, //0x00004e15 je           LBB0_888
	//0x00004e1b LBB0_894
	0x4c, 0x8d, 0x4a, 0xff, //0x00004e1b leaq         $-1(%rdx), %r9
	0x4c, 0x89, 0xc8, //0x00004e1f movq         %r9, %rax
	0x48, 0x21, 0xf8, //0x00004e22 andq         %rdi, %rax
	0x48, 0x89, 0xc6, //0x00004e25 movq         %rax, %rsi
	0x48, 0xd1, 0xee, //0x00004e28 shrq         %rsi
	0x4c, 0x21, 0xde, //0x00004e2b andq         %r11, %rsi
	0x48, 0x29, 0xf0, //0x00004e2e subq         %rsi, %rax
	0x48, 0x89, 0xc6, //0x00004e31 movq         %rax, %rsi
	0x4c, 0x21, 0xee, //0x00004e34 andq         %r13, %rsi
	0x48, 0xc1, 0xe8, 0x02, //0x00004e37 shrq         $2, %rax
	0x4c, 0x21, 0xe8, //0x00004e3b andq         %r13, %rax
	0x48, 0x01, 0xf0, //0x00004e3e addq         %rsi, %rax
	0x48, 0x89, 0xc6, //0x00004e41 movq         %rax, %rsi
	0x48, 0xc1, 0xee, 0x04, //0x00004e44 shrq         $4, %rsi
	0x48, 0x01, 0xc6, //0x00004e48 addq         %rax, %rsi
	0x48, 0x21, 0xde, //0x00004e4b andq         %rbx, %rsi
	0x48, 0xb8, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00004e4e movabsq      $72340172838076673, %rax
	0x48, 0x0f, 0xaf, 0xf0, //0x00004e58 imulq        %rax, %rsi
	0x48, 0xc1, 0xee, 0x38, //0x00004e5c shrq         $56, %rsi
	0x4c, 0x01, 0xf6, //0x00004e60 addq         %r14, %rsi
	0x4c, 0x39, 0xd6, //0x00004e63 cmpq         %r10, %rsi
	0x0f, 0x86, 0x07, 0x06, 0x00, 0x00, //0x00004e66 jbe          LBB0_938
	0x49, 0xff, 0xc2, //0x00004e6c incq         %r10
	0x4c, 0x21, 0xca, //0x00004e6f andq         %r9, %rdx
	0x0f, 0x85, 0xa3, 0xff, 0xff, 0xff, //0x00004e72 jne          LBB0_894
	//0x00004e78 LBB0_888
	0x49, 0xc1, 0xff, 0x3f, //0x00004e78 sarq         $63, %r15
	0x48, 0x89, 0xf8, //0x00004e7c movq         %rdi, %rax
	0x48, 0xd1, 0xe8, //0x00004e7f shrq         %rax
	0x4c, 0x21, 0xd8, //0x00004e82 andq         %r11, %rax
	0x48, 0x29, 0xc7, //0x00004e85 subq         %rax, %rdi
	0x48, 0x89, 0xf8, //0x00004e88 movq         %rdi, %rax
	0x4c, 0x21, 0xe8, //0x00004e8b andq         %r13, %rax
	0x48, 0xc1, 0xef, 0x02, //0x00004e8e shrq         $2, %rdi
	0x4c, 0x21, 0xef, //0x00004e92 andq         %r13, %rdi
	0x48, 0x01, 0xc7, //0x00004e95 addq         %rax, %rdi
	0x48, 0x89, 0xf8, //0x00004e98 movq         %rdi, %rax
	0x48, 0xc1, 0xe8, 0x04, //0x00004e9b shrq         $4, %rax
	0x48, 0x01, 0xf8, //0x00004e9f addq         %rdi, %rax
	0x48, 0x21, 0xd8, //0x00004ea2 andq         %rbx, %rax
	0x48, 0xba, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00004ea5 movabsq      $72340172838076673, %rdx
	0x48, 0x0f, 0xaf, 0xc2, //0x00004eaf imulq        %rdx, %rax
	0x48, 0xc1, 0xe8, 0x38, //0x00004eb3 shrq         $56, %rax
	0x49, 0x01, 0xc6, //0x00004eb7 addq         %rax, %r14
	0x49, 0x83, 0xc0, 0x40, //0x00004eba addq         $64, %r8
	0x48, 0x8b, 0x45, 0xc8, //0x00004ebe movq         $-56(%rbp), %rax
	0x48, 0x83, 0xc0, 0xc0, //0x00004ec2 addq         $-64, %rax
	0x48, 0x83, 0xf8, 0x40, //0x00004ec6 cmpq         $64, %rax
	0x48, 0x89, 0x45, 0xc8, //0x00004eca movq         %rax, $-56(%rbp)
	0x4c, 0x89, 0x7d, 0x90, //0x00004ece movq         %r15, $-112(%rbp)
	0x0f, 0x8d, 0x8b, 0xfd, 0xff, 0xff, //0x00004ed2 jge          LBB0_890
	//0x00004ed8 LBB0_896
	0x48, 0x85, 0xc0, //0x00004ed8 testq        %rax, %rax
	0x0f, 0x8e, 0x2e, 0x08, 0x00, 0x00, //0x00004edb jle          LBB0_976
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x70, 0xff, 0xff, 0xff, //0x00004ee1 movdqu       %xmm8, $-144(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x60, 0xff, 0xff, 0xff, //0x00004eea movdqu       %xmm8, $-160(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x50, 0xff, 0xff, 0xff, //0x00004ef3 movdqu       %xmm8, $-176(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x40, 0xff, 0xff, 0xff, //0x00004efc movdqu       %xmm8, $-192(%rbp)
	0x44, 0x89, 0xc0, //0x00004f05 movl         %r8d, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x00004f08 andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x00004f0d cmpl         $4033, %eax
	0x0f, 0x82, 0x4b, 0xfd, 0xff, 0xff, //0x00004f12 jb           LBB0_890
	0x48, 0x83, 0x7d, 0xc8, 0x20, //0x00004f18 cmpq         $32, $-56(%rbp)
	0x0f, 0x82, 0x31, 0x00, 0x00, 0x00, //0x00004f1d jb           LBB0_900
	0x41, 0x0f, 0x10, 0x00, //0x00004f23 movups       (%r8), %xmm0
	0x0f, 0x11, 0x85, 0x40, 0xff, 0xff, 0xff, //0x00004f27 movups       %xmm0, $-192(%rbp)
	0xf3, 0x41, 0x0f, 0x6f, 0x40, 0x10, //0x00004f2e movdqu       $16(%r8), %xmm0
	0xf3, 0x0f, 0x7f, 0x85, 0x50, 0xff, 0xff, 0xff, //0x00004f34 movdqu       %xmm0, $-176(%rbp)
	0x49, 0x83, 0xc0, 0x20, //0x00004f3c addq         $32, %r8
	0x48, 0x8b, 0x45, 0xc8, //0x00004f40 movq         $-56(%rbp), %rax
	0x48, 0x8d, 0x78, 0xe0, //0x00004f44 leaq         $-32(%rax), %rdi
	0x48, 0x8d, 0xb5, 0x60, 0xff, 0xff, 0xff, //0x00004f48 leaq         $-160(%rbp), %rsi
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x00004f4f jmp          LBB0_901
	//0x00004f54 LBB0_900
	0x48, 0x8d, 0xb5, 0x40, 0xff, 0xff, 0xff, //0x00004f54 leaq         $-192(%rbp), %rsi
	0x48, 0x8b, 0x7d, 0xc8, //0x00004f5b movq         $-56(%rbp), %rdi
	//0x00004f5f LBB0_901
	0x48, 0x83, 0xff, 0x10, //0x00004f5f cmpq         $16, %rdi
	0x0f, 0x82, 0x5c, 0x00, 0x00, 0x00, //0x00004f63 jb           LBB0_902
	0xf3, 0x41, 0x0f, 0x6f, 0x00, //0x00004f69 movdqu       (%r8), %xmm0
	0xf3, 0x0f, 0x7f, 0x06, //0x00004f6e movdqu       %xmm0, (%rsi)
	0x49, 0x83, 0xc0, 0x10, //0x00004f72 addq         $16, %r8
	0x48, 0x83, 0xc6, 0x10, //0x00004f76 addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x00004f7a addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x00004f7e cmpq         $8, %rdi
	0x0f, 0x83, 0x47, 0x00, 0x00, 0x00, //0x00004f82 jae          LBB0_907
	//0x00004f88 LBB0_903
	0x48, 0x83, 0xff, 0x04, //0x00004f88 cmpq         $4, %rdi
	0x0f, 0x8c, 0x59, 0x00, 0x00, 0x00, //0x00004f8c jl           LBB0_904
	//0x00004f92 LBB0_908
	0x41, 0x8b, 0x00, //0x00004f92 movl         (%r8), %eax
	0x89, 0x06, //0x00004f95 movl         %eax, (%rsi)
	0x49, 0x83, 0xc0, 0x04, //0x00004f97 addq         $4, %r8
	0x48, 0x83, 0xc6, 0x04, //0x00004f9b addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x00004f9f addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x00004fa3 cmpq         $2, %rdi
	0x0f, 0x83, 0x48, 0x00, 0x00, 0x00, //0x00004fa7 jae          LBB0_909
	//0x00004fad LBB0_905
	0x4c, 0x89, 0xc2, //0x00004fad movq         %r8, %rdx
	0x4c, 0x8d, 0x85, 0x40, 0xff, 0xff, 0xff, //0x00004fb0 leaq         $-192(%rbp), %r8
	0x48, 0x85, 0xff, //0x00004fb7 testq        %rdi, %rdi
	0x0f, 0x85, 0x5b, 0x00, 0x00, 0x00, //0x00004fba jne          LBB0_910
	0xe9, 0x9e, 0xfc, 0xff, 0xff, //0x00004fc0 jmp          LBB0_890
	//0x00004fc5 LBB0_902
	0x48, 0x83, 0xff, 0x08, //0x00004fc5 cmpq         $8, %rdi
	0x0f, 0x82, 0xb9, 0xff, 0xff, 0xff, //0x00004fc9 jb           LBB0_903
	//0x00004fcf LBB0_907
	0x49, 0x8b, 0x00, //0x00004fcf movq         (%r8), %rax
	0x48, 0x89, 0x06, //0x00004fd2 movq         %rax, (%rsi)
	0x49, 0x83, 0xc0, 0x08, //0x00004fd5 addq         $8, %r8
	0x48, 0x83, 0xc6, 0x08, //0x00004fd9 addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x00004fdd addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x00004fe1 cmpq         $4, %rdi
	0x0f, 0x8d, 0xa7, 0xff, 0xff, 0xff, //0x00004fe5 jge          LBB0_908
	//0x00004feb LBB0_904
	0x48, 0x83, 0xff, 0x02, //0x00004feb cmpq         $2, %rdi
	0x0f, 0x82, 0xb8, 0xff, 0xff, 0xff, //0x00004fef jb           LBB0_905
	//0x00004ff5 LBB0_909
	0x41, 0x0f, 0xb7, 0x00, //0x00004ff5 movzwl       (%r8), %eax
	0x66, 0x89, 0x06, //0x00004ff9 movw         %ax, (%rsi)
	0x49, 0x83, 0xc0, 0x02, //0x00004ffc addq         $2, %r8
	0x48, 0x83, 0xc6, 0x02, //0x00005000 addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x00005004 addq         $-2, %rdi
	0x4c, 0x89, 0xc2, //0x00005008 movq         %r8, %rdx
	0x4c, 0x8d, 0x85, 0x40, 0xff, 0xff, 0xff, //0x0000500b leaq         $-192(%rbp), %r8
	0x48, 0x85, 0xff, //0x00005012 testq        %rdi, %rdi
	0x0f, 0x84, 0x48, 0xfc, 0xff, 0xff, //0x00005015 je           LBB0_890
	//0x0000501b LBB0_910
	0x8a, 0x02, //0x0000501b movb         (%rdx), %al
	0x88, 0x06, //0x0000501d movb         %al, (%rsi)
	0x4c, 0x8d, 0x85, 0x40, 0xff, 0xff, 0xff, //0x0000501f leaq         $-192(%rbp), %r8
	0xe9, 0x38, 0xfc, 0xff, 0xff, //0x00005026 jmp          LBB0_890
	//0x0000502b LBB0_911
	0x49, 0x8d, 0x44, 0x24, 0x05, //0x0000502b leaq         $5(%r12), %rax
	//0x00005030 LBB0_912
	0x49, 0x3b, 0x45, 0x08, //0x00005030 cmpq         $8(%r13), %rax
	0x0f, 0x87, 0x15, 0xe2, 0xff, 0xff, //0x00005034 ja           LBB0_558
	0x49, 0x89, 0x03, //0x0000503a movq         %rax, (%r11)
	0x4c, 0x89, 0xe1, //0x0000503d movq         %r12, %rcx
	0xe9, 0x0a, 0xe2, 0xff, 0xff, //0x00005040 jmp          LBB0_558
	//0x00005045 LBB0_914
	0x49, 0xbb, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00005045 movabsq      $6148914691236517205, %r11
	0x49, 0x8b, 0x45, 0x08, //0x0000504f movq         $8(%r13), %rax
	0x48, 0x29, 0xf8, //0x00005053 subq         %rdi, %rax
	0x49, 0x01, 0xf8, //0x00005056 addq         %rdi, %r8
	0x31, 0xd2, //0x00005059 xorl         %edx, %edx
	0x48, 0x89, 0x55, 0x90, //0x0000505b movq         %rdx, $-112(%rbp)
	0xf3, 0x44, 0x0f, 0x6f, 0x15, 0xc8, 0xaf, 0xff, 0xff, //0x0000505f movdqu       $-20536(%rip), %xmm10  /* LCPI0_3+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0xd0, 0xaf, 0xff, 0xff, //0x00005068 movdqu       $-20528(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0x66, 0x45, 0x0f, 0x76, 0xc9, //0x00005070 pcmpeqd      %xmm9, %xmm9
	0xf3, 0x0f, 0x6f, 0x1d, 0xd3, 0xaf, 0xff, 0xff, //0x00005075 movdqu       $-20525(%rip), %xmm3  /* LCPI0_5+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x25, 0x9b, 0xaf, 0xff, 0xff, //0x0000507d movdqu       $-20581(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0x49, 0xbd, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x00005085 movabsq      $3689348814741910323, %r13
	0x66, 0x45, 0x0f, 0xef, 0xc0, //0x0000508f pxor         %xmm8, %xmm8
	0x31, 0xd2, //0x00005094 xorl         %edx, %edx
	0x48, 0x89, 0x55, 0x98, //0x00005096 movq         %rdx, $-104(%rbp)
	0x45, 0x31, 0xf6, //0x0000509a xorl         %r14d, %r14d
	0x45, 0x31, 0xd2, //0x0000509d xorl         %r10d, %r10d
	0x48, 0x83, 0xf8, 0x40, //0x000050a0 cmpq         $64, %rax
	0x48, 0x89, 0x45, 0xc8, //0x000050a4 movq         %rax, $-56(%rbp)
	0x0f, 0x8c, 0x72, 0x02, 0x00, 0x00, //0x000050a8 jl           LBB0_923
	//0x000050ae LBB0_917
	0xf3, 0x41, 0x0f, 0x6f, 0x00, //0x000050ae movdqu       (%r8), %xmm0
	0xf3, 0x41, 0x0f, 0x6f, 0x68, 0x10, //0x000050b3 movdqu       $16(%r8), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x78, 0x20, //0x000050b9 movdqu       $32(%r8), %xmm7
	0xf3, 0x41, 0x0f, 0x6f, 0x70, 0x30, //0x000050bf movdqu       $48(%r8), %xmm6
	0x66, 0x0f, 0x6f, 0xd0, //0x000050c5 movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x000050c9 pcmpeqb      %xmm10, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xfa, //0x000050ce pmovmskb     %xmm2, %r15d
	0x66, 0x0f, 0x6f, 0xd5, //0x000050d3 movdqa       %xmm5, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x000050d7 pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xfa, //0x000050dc pmovmskb     %xmm2, %edi
	0x66, 0x0f, 0x6f, 0xd7, //0x000050e0 movdqa       %xmm7, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x000050e4 pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x000050e9 pmovmskb     %xmm2, %edx
	0x66, 0x0f, 0x6f, 0xd6, //0x000050ed movdqa       %xmm6, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x000050f1 pcmpeqb      %xmm10, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xca, //0x000050f6 pmovmskb     %xmm2, %r9d
	0x49, 0xc1, 0xe1, 0x30, //0x000050fb shlq         $48, %r9
	0x48, 0xc1, 0xe2, 0x20, //0x000050ff shlq         $32, %rdx
	0x48, 0xc1, 0xe7, 0x10, //0x00005103 shlq         $16, %rdi
	0x49, 0x09, 0xff, //0x00005107 orq          %rdi, %r15
	0x49, 0x09, 0xd7, //0x0000510a orq          %rdx, %r15
	0x4d, 0x09, 0xcf, //0x0000510d orq          %r9, %r15
	0x66, 0x0f, 0x6f, 0xd0, //0x00005110 movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00005114 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xfa, //0x00005118 pmovmskb     %xmm2, %edi
	0x66, 0x0f, 0x6f, 0xd5, //0x0000511c movdqa       %xmm5, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00005120 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x00005124 pmovmskb     %xmm2, %edx
	0x66, 0x0f, 0x6f, 0xd7, //0x00005128 movdqa       %xmm7, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x0000512c pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00005130 pmovmskb     %xmm2, %eax
	0x66, 0x0f, 0x6f, 0xd6, //0x00005134 movdqa       %xmm6, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00005138 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xda, //0x0000513c pmovmskb     %xmm2, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x00005140 shlq         $48, %rbx
	0x48, 0xc1, 0xe0, 0x20, //0x00005144 shlq         $32, %rax
	0x48, 0xc1, 0xe2, 0x10, //0x00005148 shlq         $16, %rdx
	0x48, 0x09, 0xd7, //0x0000514c orq          %rdx, %rdi
	0x48, 0x09, 0xc7, //0x0000514f orq          %rax, %rdi
	0x48, 0x09, 0xdf, //0x00005152 orq          %rbx, %rdi
	0x48, 0x89, 0xf8, //0x00005155 movq         %rdi, %rax
	0x48, 0x8b, 0x55, 0x98, //0x00005158 movq         $-104(%rbp), %rdx
	0x48, 0x09, 0xd0, //0x0000515c orq          %rdx, %rax
	0x0f, 0x84, 0x43, 0x00, 0x00, 0x00, //0x0000515f je           LBB0_919
	0x48, 0x89, 0xd0, //0x00005165 movq         %rdx, %rax
	0x48, 0xf7, 0xd0, //0x00005168 notq         %rax
	0x48, 0x21, 0xf8, //0x0000516b andq         %rdi, %rax
	0x48, 0x8d, 0x1c, 0x00, //0x0000516e leaq         (%rax,%rax), %rbx
	0x48, 0x09, 0xd3, //0x00005172 orq          %rdx, %rbx
	0x48, 0x89, 0xda, //0x00005175 movq         %rbx, %rdx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00005178 movabsq      $-6148914691236517206, %rsi
	0x48, 0x31, 0xf2, //0x00005182 xorq         %rsi, %rdx
	0x48, 0x21, 0xf7, //0x00005185 andq         %rsi, %rdi
	0x48, 0x21, 0xd7, //0x00005188 andq         %rdx, %rdi
	0x31, 0xd2, //0x0000518b xorl         %edx, %edx
	0x48, 0x01, 0xc7, //0x0000518d addq         %rax, %rdi
	0x0f, 0x92, 0xc2, //0x00005190 setb         %dl
	0x48, 0x89, 0x55, 0x98, //0x00005193 movq         %rdx, $-104(%rbp)
	0x48, 0x01, 0xff, //0x00005197 addq         %rdi, %rdi
	0x4c, 0x31, 0xdf, //0x0000519a xorq         %r11, %rdi
	0x48, 0x21, 0xdf, //0x0000519d andq         %rbx, %rdi
	0x48, 0xf7, 0xd7, //0x000051a0 notq         %rdi
	0xe9, 0x0d, 0x00, 0x00, 0x00, //0x000051a3 jmp          LBB0_920
	//0x000051a8 LBB0_919
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x000051a8 movq         $-1, %rdi
	0x31, 0xc0, //0x000051af xorl         %eax, %eax
	0x48, 0x89, 0x45, 0x98, //0x000051b1 movq         %rax, $-104(%rbp)
	//0x000051b5 LBB0_920
	0x4c, 0x21, 0xff, //0x000051b5 andq         %r15, %rdi
	0x66, 0x48, 0x0f, 0x6e, 0xd7, //0x000051b8 movq         %rdi, %xmm2
	0x66, 0x41, 0x0f, 0x3a, 0x44, 0xd1, 0x00, //0x000051bd pclmulqdq    $0, %xmm9, %xmm2
	0x66, 0x49, 0x0f, 0x7e, 0xd7, //0x000051c4 movq         %xmm2, %r15
	0x4c, 0x33, 0x7d, 0x90, //0x000051c9 xorq         $-112(%rbp), %r15
	0x66, 0x0f, 0x6f, 0xd0, //0x000051cd movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x000051d1 pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xfa, //0x000051d5 pmovmskb     %xmm2, %edi
	0x66, 0x0f, 0x6f, 0xd5, //0x000051d9 movdqa       %xmm5, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x000051dd pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x000051e1 pmovmskb     %xmm2, %eax
	0x66, 0x0f, 0x6f, 0xd7, //0x000051e5 movdqa       %xmm7, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x000051e9 pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x000051ed pmovmskb     %xmm2, %edx
	0x66, 0x0f, 0x6f, 0xd6, //0x000051f1 movdqa       %xmm6, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x000051f5 pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xda, //0x000051f9 pmovmskb     %xmm2, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x000051fd shlq         $48, %rbx
	0x48, 0xc1, 0xe2, 0x20, //0x00005201 shlq         $32, %rdx
	0x48, 0xc1, 0xe0, 0x10, //0x00005205 shlq         $16, %rax
	0x48, 0x09, 0xc7, //0x00005209 orq          %rax, %rdi
	0x48, 0x09, 0xd7, //0x0000520c orq          %rdx, %rdi
	0x48, 0x09, 0xdf, //0x0000520f orq          %rbx, %rdi
	0x4d, 0x89, 0xf9, //0x00005212 movq         %r15, %r9
	0x49, 0xf7, 0xd1, //0x00005215 notq         %r9
	0x4c, 0x21, 0xcf, //0x00005218 andq         %r9, %rdi
	0x66, 0x0f, 0x74, 0xc4, //0x0000521b pcmpeqb      %xmm4, %xmm0
	0x66, 0x0f, 0xd7, 0xd0, //0x0000521f pmovmskb     %xmm0, %edx
	0x66, 0x0f, 0x74, 0xec, //0x00005223 pcmpeqb      %xmm4, %xmm5
	0x66, 0x0f, 0xd7, 0xdd, //0x00005227 pmovmskb     %xmm5, %ebx
	0x66, 0x0f, 0x74, 0xfc, //0x0000522b pcmpeqb      %xmm4, %xmm7
	0x66, 0x0f, 0xd7, 0xf7, //0x0000522f pmovmskb     %xmm7, %esi
	0x66, 0x0f, 0x74, 0xf4, //0x00005233 pcmpeqb      %xmm4, %xmm6
	0x66, 0x0f, 0xd7, 0xc6, //0x00005237 pmovmskb     %xmm6, %eax
	0x48, 0xc1, 0xe0, 0x30, //0x0000523b shlq         $48, %rax
	0x48, 0xc1, 0xe6, 0x20, //0x0000523f shlq         $32, %rsi
	0x48, 0xc1, 0xe3, 0x10, //0x00005243 shlq         $16, %rbx
	0x48, 0x09, 0xda, //0x00005247 orq          %rbx, %rdx
	0x48, 0x09, 0xf2, //0x0000524a orq          %rsi, %rdx
	0x48, 0x09, 0xc2, //0x0000524d orq          %rax, %rdx
	0x48, 0xbb, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00005250 movabsq      $1085102592571150095, %rbx
	0x4c, 0x21, 0xca, //0x0000525a andq         %r9, %rdx
	0x0f, 0x84, 0x5d, 0x00, 0x00, 0x00, //0x0000525d je           LBB0_915
	//0x00005263 LBB0_921
	0x4c, 0x8d, 0x4a, 0xff, //0x00005263 leaq         $-1(%rdx), %r9
	0x4c, 0x89, 0xc8, //0x00005267 movq         %r9, %rax
	0x48, 0x21, 0xf8, //0x0000526a andq         %rdi, %rax
	0x48, 0x89, 0xc6, //0x0000526d movq         %rax, %rsi
	0x48, 0xd1, 0xee, //0x00005270 shrq         %rsi
	0x4c, 0x21, 0xde, //0x00005273 andq         %r11, %rsi
	0x48, 0x29, 0xf0, //0x00005276 subq         %rsi, %rax
	0x48, 0x89, 0xc6, //0x00005279 movq         %rax, %rsi
	0x4c, 0x21, 0xee, //0x0000527c andq         %r13, %rsi
	0x48, 0xc1, 0xe8, 0x02, //0x0000527f shrq         $2, %rax
	0x4c, 0x21, 0xe8, //0x00005283 andq         %r13, %rax
	0x48, 0x01, 0xf0, //0x00005286 addq         %rsi, %rax
	0x48, 0x89, 0xc6, //0x00005289 movq         %rax, %rsi
	0x48, 0xc1, 0xee, 0x04, //0x0000528c shrq         $4, %rsi
	0x48, 0x01, 0xc6, //0x00005290 addq         %rax, %rsi
	0x48, 0x21, 0xde, //0x00005293 andq         %rbx, %rsi
	0x48, 0xb8, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00005296 movabsq      $72340172838076673, %rax
	0x48, 0x0f, 0xaf, 0xf0, //0x000052a0 imulq        %rax, %rsi
	0x48, 0xc1, 0xee, 0x38, //0x000052a4 shrq         $56, %rsi
	0x4c, 0x01, 0xf6, //0x000052a8 addq         %r14, %rsi
	0x4c, 0x39, 0xd6, //0x000052ab cmpq         %r10, %rsi
	0x0f, 0x86, 0xbf, 0x01, 0x00, 0x00, //0x000052ae jbe          LBB0_938
	0x49, 0xff, 0xc2, //0x000052b4 incq         %r10
	0x4c, 0x21, 0xca, //0x000052b7 andq         %r9, %rdx
	0x0f, 0x85, 0xa3, 0xff, 0xff, 0xff, //0x000052ba jne          LBB0_921
	//0x000052c0 LBB0_915
	0x49, 0xc1, 0xff, 0x3f, //0x000052c0 sarq         $63, %r15
	0x48, 0x89, 0xf8, //0x000052c4 movq         %rdi, %rax
	0x48, 0xd1, 0xe8, //0x000052c7 shrq         %rax
	0x4c, 0x21, 0xd8, //0x000052ca andq         %r11, %rax
	0x48, 0x29, 0xc7, //0x000052cd subq         %rax, %rdi
	0x48, 0x89, 0xf8, //0x000052d0 movq         %rdi, %rax
	0x4c, 0x21, 0xe8, //0x000052d3 andq         %r13, %rax
	0x48, 0xc1, 0xef, 0x02, //0x000052d6 shrq         $2, %rdi
	0x4c, 0x21, 0xef, //0x000052da andq         %r13, %rdi
	0x48, 0x01, 0xc7, //0x000052dd addq         %rax, %rdi
	0x48, 0x89, 0xf8, //0x000052e0 movq         %rdi, %rax
	0x48, 0xc1, 0xe8, 0x04, //0x000052e3 shrq         $4, %rax
	0x48, 0x01, 0xf8, //0x000052e7 addq         %rdi, %rax
	0x48, 0x21, 0xd8, //0x000052ea andq         %rbx, %rax
	0x48, 0xba, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x000052ed movabsq      $72340172838076673, %rdx
	0x48, 0x0f, 0xaf, 0xc2, //0x000052f7 imulq        %rdx, %rax
	0x48, 0xc1, 0xe8, 0x38, //0x000052fb shrq         $56, %rax
	0x49, 0x01, 0xc6, //0x000052ff addq         %rax, %r14
	0x49, 0x83, 0xc0, 0x40, //0x00005302 addq         $64, %r8
	0x48, 0x8b, 0x45, 0xc8, //0x00005306 movq         $-56(%rbp), %rax
	0x48, 0x83, 0xc0, 0xc0, //0x0000530a addq         $-64, %rax
	0x4c, 0x89, 0x7d, 0x90, //0x0000530e movq         %r15, $-112(%rbp)
	0x48, 0x83, 0xf8, 0x40, //0x00005312 cmpq         $64, %rax
	0x48, 0x89, 0x45, 0xc8, //0x00005316 movq         %rax, $-56(%rbp)
	0x0f, 0x8d, 0x8e, 0xfd, 0xff, 0xff, //0x0000531a jge          LBB0_917
	//0x00005320 LBB0_923
	0x48, 0x85, 0xc0, //0x00005320 testq        %rax, %rax
	0x0f, 0x8e, 0xe6, 0x03, 0x00, 0x00, //0x00005323 jle          LBB0_976
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x70, 0xff, 0xff, 0xff, //0x00005329 movdqu       %xmm8, $-144(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x60, 0xff, 0xff, 0xff, //0x00005332 movdqu       %xmm8, $-160(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x50, 0xff, 0xff, 0xff, //0x0000533b movdqu       %xmm8, $-176(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x40, 0xff, 0xff, 0xff, //0x00005344 movdqu       %xmm8, $-192(%rbp)
	0x44, 0x89, 0xc0, //0x0000534d movl         %r8d, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x00005350 andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x00005355 cmpl         $4033, %eax
	0x0f, 0x82, 0x4e, 0xfd, 0xff, 0xff, //0x0000535a jb           LBB0_917
	0x48, 0x83, 0x7d, 0xc8, 0x20, //0x00005360 cmpq         $32, $-56(%rbp)
	0x0f, 0x82, 0x31, 0x00, 0x00, 0x00, //0x00005365 jb           LBB0_927
	0x41, 0x0f, 0x10, 0x00, //0x0000536b movups       (%r8), %xmm0
	0x0f, 0x11, 0x85, 0x40, 0xff, 0xff, 0xff, //0x0000536f movups       %xmm0, $-192(%rbp)
	0xf3, 0x41, 0x0f, 0x6f, 0x40, 0x10, //0x00005376 movdqu       $16(%r8), %xmm0
	0xf3, 0x0f, 0x7f, 0x85, 0x50, 0xff, 0xff, 0xff, //0x0000537c movdqu       %xmm0, $-176(%rbp)
	0x49, 0x83, 0xc0, 0x20, //0x00005384 addq         $32, %r8
	0x48, 0x8b, 0x45, 0xc8, //0x00005388 movq         $-56(%rbp), %rax
	0x48, 0x8d, 0x78, 0xe0, //0x0000538c leaq         $-32(%rax), %rdi
	0x48, 0x8d, 0xb5, 0x60, 0xff, 0xff, 0xff, //0x00005390 leaq         $-160(%rbp), %rsi
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x00005397 jmp          LBB0_928
	//0x0000539c LBB0_927
	0x48, 0x8d, 0xb5, 0x40, 0xff, 0xff, 0xff, //0x0000539c leaq         $-192(%rbp), %rsi
	0x48, 0x8b, 0x7d, 0xc8, //0x000053a3 movq         $-56(%rbp), %rdi
	//0x000053a7 LBB0_928
	0x48, 0x83, 0xff, 0x10, //0x000053a7 cmpq         $16, %rdi
	0x0f, 0x82, 0x5c, 0x00, 0x00, 0x00, //0x000053ab jb           LBB0_929
	0xf3, 0x41, 0x0f, 0x6f, 0x00, //0x000053b1 movdqu       (%r8), %xmm0
	0xf3, 0x0f, 0x7f, 0x06, //0x000053b6 movdqu       %xmm0, (%rsi)
	0x49, 0x83, 0xc0, 0x10, //0x000053ba addq         $16, %r8
	0x48, 0x83, 0xc6, 0x10, //0x000053be addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x000053c2 addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x000053c6 cmpq         $8, %rdi
	0x0f, 0x83, 0x47, 0x00, 0x00, 0x00, //0x000053ca jae          LBB0_934
	//0x000053d0 LBB0_930
	0x48, 0x83, 0xff, 0x04, //0x000053d0 cmpq         $4, %rdi
	0x0f, 0x8c, 0x59, 0x00, 0x00, 0x00, //0x000053d4 jl           LBB0_931
	//0x000053da LBB0_935
	0x41, 0x8b, 0x00, //0x000053da movl         (%r8), %eax
	0x89, 0x06, //0x000053dd movl         %eax, (%rsi)
	0x49, 0x83, 0xc0, 0x04, //0x000053df addq         $4, %r8
	0x48, 0x83, 0xc6, 0x04, //0x000053e3 addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x000053e7 addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x000053eb cmpq         $2, %rdi
	0x0f, 0x83, 0x48, 0x00, 0x00, 0x00, //0x000053ef jae          LBB0_936
	//0x000053f5 LBB0_932
	0x4c, 0x89, 0xc2, //0x000053f5 movq         %r8, %rdx
	0x4c, 0x8d, 0x85, 0x40, 0xff, 0xff, 0xff, //0x000053f8 leaq         $-192(%rbp), %r8
	0x48, 0x85, 0xff, //0x000053ff testq        %rdi, %rdi
	0x0f, 0x85, 0x5b, 0x00, 0x00, 0x00, //0x00005402 jne          LBB0_937
	0xe9, 0xa1, 0xfc, 0xff, 0xff, //0x00005408 jmp          LBB0_917
	//0x0000540d LBB0_929
	0x48, 0x83, 0xff, 0x08, //0x0000540d cmpq         $8, %rdi
	0x0f, 0x82, 0xb9, 0xff, 0xff, 0xff, //0x00005411 jb           LBB0_930
	//0x00005417 LBB0_934
	0x49, 0x8b, 0x00, //0x00005417 movq         (%r8), %rax
	0x48, 0x89, 0x06, //0x0000541a movq         %rax, (%rsi)
	0x49, 0x83, 0xc0, 0x08, //0x0000541d addq         $8, %r8
	0x48, 0x83, 0xc6, 0x08, //0x00005421 addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x00005425 addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x00005429 cmpq         $4, %rdi
	0x0f, 0x8d, 0xa7, 0xff, 0xff, 0xff, //0x0000542d jge          LBB0_935
	//0x00005433 LBB0_931
	0x48, 0x83, 0xff, 0x02, //0x00005433 cmpq         $2, %rdi
	0x0f, 0x82, 0xb8, 0xff, 0xff, 0xff, //0x00005437 jb           LBB0_932
	//0x0000543d LBB0_936
	0x41, 0x0f, 0xb7, 0x00, //0x0000543d movzwl       (%r8), %eax
	0x66, 0x89, 0x06, //0x00005441 movw         %ax, (%rsi)
	0x49, 0x83, 0xc0, 0x02, //0x00005444 addq         $2, %r8
	0x48, 0x83, 0xc6, 0x02, //0x00005448 addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x0000544c addq         $-2, %rdi
	0x4c, 0x89, 0xc2, //0x00005450 movq         %r8, %rdx
	0x4c, 0x8d, 0x85, 0x40, 0xff, 0xff, 0xff, //0x00005453 leaq         $-192(%rbp), %r8
	0x48, 0x85, 0xff, //0x0000545a testq        %rdi, %rdi
	0x0f, 0x84, 0x4b, 0xfc, 0xff, 0xff, //0x0000545d je           LBB0_917
	//0x00005463 LBB0_937
	0x8a, 0x02, //0x00005463 movb         (%rdx), %al
	0x88, 0x06, //0x00005465 movb         %al, (%rsi)
	0x4c, 0x8d, 0x85, 0x40, 0xff, 0xff, 0xff, //0x00005467 leaq         $-192(%rbp), %r8
	0xe9, 0x3b, 0xfc, 0xff, 0xff, //0x0000546e jmp          LBB0_917
	//0x00005473 LBB0_938
	0x48, 0x8b, 0x75, 0xc0, //0x00005473 movq         $-64(%rbp), %rsi
	0x48, 0x8b, 0x46, 0x08, //0x00005477 movq         $8(%rsi), %rax
	0x48, 0x0f, 0xbc, 0xca, //0x0000547b bsfq         %rdx, %rcx
	0x48, 0x2b, 0x4d, 0xc8, //0x0000547f subq         $-56(%rbp), %rcx
	0x48, 0x8d, 0x44, 0x01, 0x01, //0x00005483 leaq         $1(%rcx,%rax), %rax
	0x48, 0x8b, 0x55, 0xd0, //0x00005488 movq         $-48(%rbp), %rdx
	0x48, 0x89, 0x02, //0x0000548c movq         %rax, (%rdx)
	0x48, 0x8b, 0x4e, 0x08, //0x0000548f movq         $8(%rsi), %rcx
	0x48, 0x39, 0xc8, //0x00005493 cmpq         %rcx, %rax
	0x48, 0x0f, 0x47, 0xc1, //0x00005496 cmovaq       %rcx, %rax
	0x48, 0x89, 0x02, //0x0000549a movq         %rax, (%rdx)
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000549d movq         $-1, %rax
	0x4c, 0x0f, 0x47, 0xe0, //0x000054a4 cmovaq       %rax, %r12
	0x4c, 0x89, 0xe1, //0x000054a8 movq         %r12, %rcx
	0xe9, 0x9f, 0xdd, 0xff, 0xff, //0x000054ab jmp          LBB0_558
	//0x000054b0 LBB0_939
	0x49, 0x83, 0xfa, 0xff, //0x000054b0 cmpq         $-1, %r10
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x000054b4 jne          LBB0_941
	//0x000054ba LBB0_940
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x000054ba movq         $-1, %r10
	0x4c, 0x8b, 0x65, 0x88, //0x000054c1 movq         $-120(%rbp), %r12
	//0x000054c5 LBB0_941
	0x4d, 0x89, 0x23, //0x000054c5 movq         %r12, (%r11)
	0x4c, 0x89, 0xd1, //0x000054c8 movq         %r10, %rcx
	0xe9, 0x7f, 0xdd, 0xff, 0xff, //0x000054cb jmp          LBB0_558
	//0x000054d0 LBB0_942
	0x4c, 0x89, 0xd1, //0x000054d0 movq         %r10, %rcx
	//0x000054d3 LBB0_943
	0x48, 0xf7, 0xd1, //0x000054d3 notq         %rcx
	0x49, 0x01, 0xc9, //0x000054d6 addq         %rcx, %r9
	//0x000054d9 LBB0_944
	0x48, 0x8b, 0x45, 0xd0, //0x000054d9 movq         $-48(%rbp), %rax
	0x4c, 0x89, 0x08, //0x000054dd movq         %r9, (%rax)
	0xe9, 0x63, 0xdd, 0xff, 0xff, //0x000054e0 jmp          LBB0_557
	//0x000054e5 LBB0_956
	0x49, 0x89, 0x13, //0x000054e5 movq         %rdx, (%r11)
	0xe9, 0x62, 0xdd, 0xff, 0xff, //0x000054e8 jmp          LBB0_558
	//0x000054ed LBB0_950
	0x4c, 0x89, 0xb5, 0x38, 0xff, 0xff, 0xff, //0x000054ed movq         %r14, $-200(%rbp)
	//0x000054f4 LBB0_951
	0x48, 0x8b, 0x45, 0xd0, //0x000054f4 movq         $-48(%rbp), %rax
	0x48, 0x8b, 0x8d, 0x38, 0xff, 0xff, 0xff, //0x000054f8 movq         $-200(%rbp), %rcx
	0x48, 0x89, 0x08, //0x000054ff movq         %rcx, (%rax)
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00005502 movq         $-1, %rcx
	0xe9, 0x41, 0xdd, 0xff, 0xff, //0x00005509 jmp          LBB0_558
	//0x0000550e LBB0_952
	0x4c, 0x29, 0xc7, //0x0000550e subq         %r8, %rdi
	0xe9, 0x90, 0xde, 0xff, 0xff, //0x00005511 jmp          LBB0_578
	//0x00005516 LBB0_953
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00005516 movq         $-1, %r10
	//0x0000551d LBB0_954
	0x4d, 0x29, 0xd1, //0x0000551d subq         %r10, %r9
	0xe9, 0xb4, 0xff, 0xff, 0xff, //0x00005520 jmp          LBB0_944
	//0x00005525 LBB0_957
	0x4d, 0x89, 0x0b, //0x00005525 movq         %r9, (%r11)
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00005528 movq         $-2, %rcx
	0x41, 0x80, 0x3c, 0x24, 0x74, //0x0000552f cmpb         $116, (%r12)
	0x0f, 0x85, 0x15, 0xdd, 0xff, 0xff, //0x00005534 jne          LBB0_558
	0x49, 0x8d, 0x41, 0x01, //0x0000553a leaq         $1(%r9), %rax
	0x49, 0x89, 0x03, //0x0000553e movq         %rax, (%r11)
	0x43, 0x80, 0x7c, 0x0f, 0x01, 0x72, //0x00005541 cmpb         $114, $1(%r15,%r9)
	0x0f, 0x85, 0x02, 0xdd, 0xff, 0xff, //0x00005547 jne          LBB0_558
	0x49, 0x8d, 0x41, 0x02, //0x0000554d leaq         $2(%r9), %rax
	0x49, 0x89, 0x03, //0x00005551 movq         %rax, (%r11)
	0x43, 0x80, 0x7c, 0x0f, 0x02, 0x75, //0x00005554 cmpb         $117, $2(%r15,%r9)
	0x0f, 0x85, 0xef, 0xdc, 0xff, 0xff, //0x0000555a jne          LBB0_558
	0x49, 0x8d, 0x41, 0x03, //0x00005560 leaq         $3(%r9), %rax
	0x49, 0x89, 0x03, //0x00005564 movq         %rax, (%r11)
	0x43, 0x80, 0x7c, 0x0f, 0x03, 0x65, //0x00005567 cmpb         $101, $3(%r15,%r9)
	0x0f, 0x85, 0xdc, 0xdc, 0xff, 0xff, //0x0000556d jne          LBB0_558
	0xe9, 0xa0, 0x00, 0x00, 0x00, //0x00005573 jmp          LBB0_786
	//0x00005578 LBB0_961
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00005578 movq         $-2, %rcx
	0x80, 0xfa, 0x61, //0x0000557f cmpb         $97, %dl
	0x0f, 0x85, 0xc7, 0xdc, 0xff, 0xff, //0x00005582 jne          LBB0_558
	0x49, 0x8d, 0x41, 0x02, //0x00005588 leaq         $2(%r9), %rax
	0x49, 0x89, 0x03, //0x0000558c movq         %rax, (%r11)
	0x43, 0x80, 0x7c, 0x0f, 0x02, 0x6c, //0x0000558f cmpb         $108, $2(%r15,%r9)
	0x0f, 0x85, 0xb4, 0xdc, 0xff, 0xff, //0x00005595 jne          LBB0_558
	0x49, 0x8d, 0x41, 0x03, //0x0000559b leaq         $3(%r9), %rax
	0x49, 0x89, 0x03, //0x0000559f movq         %rax, (%r11)
	0x43, 0x80, 0x7c, 0x0f, 0x03, 0x73, //0x000055a2 cmpb         $115, $3(%r15,%r9)
	0x0f, 0x85, 0xa1, 0xdc, 0xff, 0xff, //0x000055a8 jne          LBB0_558
	0x49, 0x8d, 0x41, 0x04, //0x000055ae leaq         $4(%r9), %rax
	0x49, 0x89, 0x03, //0x000055b2 movq         %rax, (%r11)
	0x43, 0x80, 0x7c, 0x0f, 0x04, 0x65, //0x000055b5 cmpb         $101, $4(%r15,%r9)
	0x0f, 0x85, 0x8e, 0xdc, 0xff, 0xff, //0x000055bb jne          LBB0_558
	0x49, 0x83, 0xc1, 0x05, //0x000055c1 addq         $5, %r9
	0xe9, 0x52, 0x00, 0x00, 0x00, //0x000055c5 jmp          LBB0_966
	//0x000055ca LBB0_782
	0x4d, 0x89, 0x0b, //0x000055ca movq         %r9, (%r11)
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x000055cd movq         $-2, %rcx
	0x41, 0x80, 0x3c, 0x24, 0x6e, //0x000055d4 cmpb         $110, (%r12)
	0x0f, 0x85, 0x70, 0xdc, 0xff, 0xff, //0x000055d9 jne          LBB0_558
	0x49, 0x8d, 0x41, 0x01, //0x000055df leaq         $1(%r9), %rax
	0x49, 0x89, 0x03, //0x000055e3 movq         %rax, (%r11)
	0x43, 0x80, 0x7c, 0x0f, 0x01, 0x75, //0x000055e6 cmpb         $117, $1(%r15,%r9)
	0x0f, 0x85, 0x5d, 0xdc, 0xff, 0xff, //0x000055ec jne          LBB0_558
	0x49, 0x8d, 0x41, 0x02, //0x000055f2 leaq         $2(%r9), %rax
	0x49, 0x89, 0x03, //0x000055f6 movq         %rax, (%r11)
	0x43, 0x80, 0x7c, 0x0f, 0x02, 0x6c, //0x000055f9 cmpb         $108, $2(%r15,%r9)
	0x0f, 0x85, 0x4a, 0xdc, 0xff, 0xff, //0x000055ff jne          LBB0_558
	0x49, 0x8d, 0x41, 0x03, //0x00005605 leaq         $3(%r9), %rax
	0x49, 0x89, 0x03, //0x00005609 movq         %rax, (%r11)
	0x43, 0x80, 0x7c, 0x0f, 0x03, 0x6c, //0x0000560c cmpb         $108, $3(%r15,%r9)
	0x0f, 0x85, 0x37, 0xdc, 0xff, 0xff, //0x00005612 jne          LBB0_558
	//0x00005618 LBB0_786
	0x49, 0x83, 0xc1, 0x04, //0x00005618 addq         $4, %r9
	//0x0000561c LBB0_966
	0x48, 0x8b, 0x45, 0xd0, //0x0000561c movq         $-48(%rbp), %rax
	0x4c, 0x89, 0x08, //0x00005620 movq         %r9, (%rax)
	0xe9, 0x27, 0xdc, 0xff, 0xff, //0x00005623 jmp          LBB0_558
	//0x00005628 LBB0_967
	0x4c, 0x89, 0xd0, //0x00005628 movq         %r10, %rax
	0xe9, 0x8e, 0xf4, 0xff, 0xff, //0x0000562b jmp          LBB0_873
	//0x00005630 LBB0_968
	0x4c, 0x89, 0xe3, //0x00005630 movq         %r12, %rbx
	0x48, 0x83, 0xc3, 0x02, //0x00005633 addq         $2, %rbx
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00005637 movq         $-2, %rcx
	0xe9, 0xb5, 0x00, 0x00, 0x00, //0x0000563e jmp          LBB0_973
	//0x00005643 LBB0_974
	0x4c, 0x89, 0x55, 0x88, //0x00005643 movq         %r10, $-120(%rbp)
	0xe9, 0x6e, 0xfe, 0xff, 0xff, //0x00005647 jmp          LBB0_940
	//0x0000564c LBB0_884
	0x4d, 0x85, 0xff, //0x0000564c testq        %r15, %r15
	0x0f, 0x85, 0xed, 0x00, 0x00, 0x00, //0x0000564f jne          LBB0_980
	0x4b, 0x8d, 0x7c, 0x1e, 0x01, //0x00005655 leaq         $1(%r14,%r11), %rdi
	0x49, 0xf7, 0xd6, //0x0000565a notq         %r14
	0x4d, 0x01, 0xce, //0x0000565d addq         %r9, %r14
	//0x00005660 LBB0_886
	0x4d, 0x85, 0xf6, //0x00005660 testq        %r14, %r14
	0x48, 0x8b, 0x55, 0xd0, //0x00005663 movq         $-48(%rbp), %rdx
	0x0f, 0x8f, 0x24, 0x00, 0x00, 0x00, //0x00005667 jg           LBB0_947
	0xe9, 0xdd, 0xdb, 0xff, 0xff, //0x0000566d jmp          LBB0_558
	//0x00005672 LBB0_945
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00005672 movq         $-2, %rax
	0xb9, 0x02, 0x00, 0x00, 0x00, //0x00005679 movl         $2, %ecx
	0x48, 0x01, 0xcf, //0x0000567e addq         %rcx, %rdi
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00005681 movq         $-1, %rcx
	0x49, 0x01, 0xc6, //0x00005688 addq         %rax, %r14
	0x0f, 0x8e, 0xbe, 0xdb, 0xff, 0xff, //0x0000568b jle          LBB0_558
	//0x00005691 LBB0_947
	0x0f, 0xb6, 0x07, //0x00005691 movzbl       (%rdi), %eax
	0x3c, 0x5c, //0x00005694 cmpb         $92, %al
	0x0f, 0x84, 0xd6, 0xff, 0xff, 0xff, //0x00005696 je           LBB0_945
	0x3c, 0x22, //0x0000569c cmpb         $34, %al
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x0000569e je           LBB0_969
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000056a4 movq         $-1, %rax
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x000056ab movl         $1, %ecx
	0x48, 0x01, 0xcf, //0x000056b0 addq         %rcx, %rdi
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000056b3 movq         $-1, %rcx
	0x49, 0x01, 0xc6, //0x000056ba addq         %rax, %r14
	0x0f, 0x8f, 0xce, 0xff, 0xff, 0xff, //0x000056bd jg           LBB0_947
	0xe9, 0x87, 0xdb, 0xff, 0xff, //0x000056c3 jmp          LBB0_558
	//0x000056c8 LBB0_969
	0x4c, 0x29, 0xc7, //0x000056c8 subq         %r8, %rdi
	0x48, 0xff, 0xc7, //0x000056cb incq         %rdi
	0x48, 0x89, 0x3a, //0x000056ce movq         %rdi, (%rdx)
	0x4c, 0x89, 0xe1, //0x000056d1 movq         %r12, %rcx
	0xe9, 0x76, 0xdb, 0xff, 0xff, //0x000056d4 jmp          LBB0_558
	//0x000056d9 LBB0_970
	0x4c, 0x89, 0xe3, //0x000056d9 movq         %r12, %rbx
	0x48, 0xff, 0xc3, //0x000056dc incq         %rbx
	0x48, 0xc7, 0xc1, 0xfd, 0xff, 0xff, 0xff, //0x000056df movq         $-3, %rcx
	0xe9, 0x0d, 0x00, 0x00, 0x00, //0x000056e6 jmp          LBB0_973
	//0x000056eb LBB0_971
	0x4c, 0x89, 0xe3, //0x000056eb movq         %r12, %rbx
	0x48, 0xff, 0xc3, //0x000056ee incq         %rbx
	//0x000056f1 LBB0_972
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000056f1 movq         $-1, %rcx
	//0x000056f8 LBB0_973
	0x48, 0x8b, 0x45, 0xc8, //0x000056f8 movq         $-56(%rbp), %rax
	0x48, 0x29, 0xc3, //0x000056fc subq         %rax, %rbx
	0x49, 0x89, 0x1b, //0x000056ff movq         %rbx, (%r11)
	0xe9, 0x48, 0xdb, 0xff, 0xff, //0x00005702 jmp          LBB0_558
	//0x00005707 LBB0_975
	0x4c, 0x01, 0xc7, //0x00005707 addq         %r8, %rdi
	0xe9, 0x51, 0xff, 0xff, 0xff, //0x0000570a jmp          LBB0_886
	//0x0000570f LBB0_976
	0x48, 0x8b, 0x45, 0xc0, //0x0000570f movq         $-64(%rbp), %rax
	0x48, 0x8b, 0x40, 0x08, //0x00005713 movq         $8(%rax), %rax
	0x48, 0x8b, 0x55, 0xd0, //0x00005717 movq         $-48(%rbp), %rdx
	0x48, 0x89, 0x02, //0x0000571b movq         %rax, (%rdx)
	0xe9, 0x2c, 0xdb, 0xff, 0xff, //0x0000571e jmp          LBB0_558
	//0x00005723 LBB0_977
	0x48, 0xc7, 0xc1, 0xfc, 0xff, 0xff, 0xff, //0x00005723 movq         $-4, %rcx
	0xe9, 0x07, 0x00, 0x00, 0x00, //0x0000572a jmp          LBB0_979
	//0x0000572f LBB0_978
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x0000572f movq         $-2, %rcx
	//0x00005736 LBB0_979
	0x4c, 0x89, 0xc3, //0x00005736 movq         %r8, %rbx
	0x4c, 0x8b, 0x5d, 0xd0, //0x00005739 movq         $-48(%rbp), %r11
	0xe9, 0xb6, 0xff, 0xff, 0xff, //0x0000573d jmp          LBB0_973
	//0x00005742 LBB0_980
	0x49, 0x8d, 0x41, 0xff, //0x00005742 leaq         $-1(%r9), %rax
	0x4c, 0x39, 0xf0, //0x00005746 cmpq         %r14, %rax
	0x0f, 0x84, 0x00, 0xdb, 0xff, 0xff, //0x00005749 je           LBB0_558
	0x4b, 0x8d, 0x7c, 0x1e, 0x02, //0x0000574f leaq         $2(%r14,%r11), %rdi
	0x4d, 0x29, 0xf1, //0x00005754 subq         %r14, %r9
	0x49, 0x83, 0xc1, 0xfe, //0x00005757 addq         $-2, %r9
	0x4d, 0x89, 0xce, //0x0000575b movq         %r9, %r14
	0xe9, 0xfd, 0xfe, 0xff, 0xff, //0x0000575e jmp          LBB0_886
	//0x00005763 LBB0_982
	0x4c, 0x8b, 0x5d, 0xd0, //0x00005763 movq         $-48(%rbp), %r11
	0x48, 0x89, 0xf0, //0x00005767 movq         %rsi, %rax
	0xe9, 0x4f, 0xf3, 0xff, 0xff, //0x0000576a jmp          LBB0_873
	//0x0000576f LBB0_983
	0x4c, 0x8b, 0x5d, 0xd0, //0x0000576f movq         $-48(%rbp), %r11
	0xe9, 0x46, 0xf3, 0xff, 0xff, //0x00005773 jmp          LBB0_873
	//0x00005778 .p2align 2, 0x90
	// // .set L0_0_set_419, LBB0_419-LJTI0_0
	// // .set L0_0_set_462, LBB0_462-LJTI0_0
	// // .set L0_0_set_451, LBB0_451-LJTI0_0
	// // .set L0_0_set_404, LBB0_404-LJTI0_0
	// // .set L0_0_set_489, LBB0_489-LJTI0_0
	// // .set L0_0_set_461, LBB0_461-LJTI0_0
	// // .set L0_0_set_450, LBB0_450-LJTI0_0
	// // .set L0_0_set_463, LBB0_463-LJTI0_0
	//0x00005778 LJTI0_0
	0x18, 0xcc, 0xff, 0xff, //0x00005778 .long L0_0_set_419
	0xf4, 0xce, 0xff, 0xff, //0x0000577c .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005780 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005784 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005788 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x0000578c .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005790 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005794 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005798 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x0000579c .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000057a0 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000057a4 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000057a8 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000057ac .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000057b0 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000057b4 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000057b8 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000057bc .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000057c0 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000057c4 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000057c8 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000057cc .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000057d0 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000057d4 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000057d8 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000057dc .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000057e0 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000057e4 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000057e8 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000057ec .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000057f0 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000057f4 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000057f8 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000057fc .long L0_0_set_462
	0xc5, 0xcd, 0xff, 0xff, //0x00005800 .long L0_0_set_451
	0xf4, 0xce, 0xff, 0xff, //0x00005804 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005808 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x0000580c .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005810 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005814 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005818 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x0000581c .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005820 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005824 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005828 .long L0_0_set_462
	0x3c, 0xcb, 0xff, 0xff, //0x0000582c .long L0_0_set_404
	0xf4, 0xce, 0xff, 0xff, //0x00005830 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005834 .long L0_0_set_462
	0x3c, 0xcb, 0xff, 0xff, //0x00005838 .long L0_0_set_404
	0x3c, 0xcb, 0xff, 0xff, //0x0000583c .long L0_0_set_404
	0x3c, 0xcb, 0xff, 0xff, //0x00005840 .long L0_0_set_404
	0x3c, 0xcb, 0xff, 0xff, //0x00005844 .long L0_0_set_404
	0x3c, 0xcb, 0xff, 0xff, //0x00005848 .long L0_0_set_404
	0x3c, 0xcb, 0xff, 0xff, //0x0000584c .long L0_0_set_404
	0x3c, 0xcb, 0xff, 0xff, //0x00005850 .long L0_0_set_404
	0x3c, 0xcb, 0xff, 0xff, //0x00005854 .long L0_0_set_404
	0x3c, 0xcb, 0xff, 0xff, //0x00005858 .long L0_0_set_404
	0x3c, 0xcb, 0xff, 0xff, //0x0000585c .long L0_0_set_404
	0xf4, 0xce, 0xff, 0xff, //0x00005860 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005864 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005868 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x0000586c .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005870 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005874 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005878 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x0000587c .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005880 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005884 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005888 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x0000588c .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005890 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005894 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005898 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x0000589c .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000058a0 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000058a4 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000058a8 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000058ac .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000058b0 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000058b4 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000058b8 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000058bc .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000058c0 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000058c4 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000058c8 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000058cc .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000058d0 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000058d4 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000058d8 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000058dc .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000058e0 .long L0_0_set_462
	0x2b, 0xd3, 0xff, 0xff, //0x000058e4 .long L0_0_set_489
	0xf4, 0xce, 0xff, 0xff, //0x000058e8 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000058ec .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000058f0 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000058f4 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000058f8 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x000058fc .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005900 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005904 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005908 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x0000590c .long L0_0_set_462
	0xe7, 0xce, 0xff, 0xff, //0x00005910 .long L0_0_set_461
	0xf4, 0xce, 0xff, 0xff, //0x00005914 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005918 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x0000591c .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005920 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005924 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005928 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x0000592c .long L0_0_set_462
	0xb3, 0xcd, 0xff, 0xff, //0x00005930 .long L0_0_set_450
	0xf4, 0xce, 0xff, 0xff, //0x00005934 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005938 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x0000593c .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005940 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005944 .long L0_0_set_462
	0xb3, 0xcd, 0xff, 0xff, //0x00005948 .long L0_0_set_450
	0xf4, 0xce, 0xff, 0xff, //0x0000594c .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005950 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005954 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005958 .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x0000595c .long L0_0_set_462
	0xf4, 0xce, 0xff, 0xff, //0x00005960 .long L0_0_set_462
	0xff, 0xce, 0xff, 0xff, //0x00005964 .long L0_0_set_463
	// // .set L0_1_set_224, LBB0_224-LJTI0_1
	// // .set L0_1_set_265, LBB0_265-LJTI0_1
	// // .set L0_1_set_253, LBB0_253-LJTI0_1
	// // .set L0_1_set_209, LBB0_209-LJTI0_1
	// // .set L0_1_set_266, LBB0_266-LJTI0_1
	// // .set L0_1_set_264, LBB0_264-LJTI0_1
	// // .set L0_1_set_252, LBB0_252-LJTI0_1
	// // .set L0_1_set_292, LBB0_292-LJTI0_1
	//0x00005968 LJTI0_1
	0xd8, 0xb3, 0xff, 0xff, //0x00005968 .long L0_1_set_224
	0x6e, 0xb6, 0xff, 0xff, //0x0000596c .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005970 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005974 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005978 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x0000597c .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005980 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005984 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005988 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x0000598c .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005990 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005994 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005998 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x0000599c .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x000059a0 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x000059a4 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x000059a8 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x000059ac .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x000059b0 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x000059b4 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x000059b8 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x000059bc .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x000059c0 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x000059c4 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x000059c8 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x000059cc .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x000059d0 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x000059d4 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x000059d8 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x000059dc .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x000059e0 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x000059e4 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x000059e8 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x000059ec .long L0_1_set_265
	0x3e, 0xb5, 0xff, 0xff, //0x000059f0 .long L0_1_set_253
	0x6e, 0xb6, 0xff, 0xff, //0x000059f4 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x000059f8 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x000059fc .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a00 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a04 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a08 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a0c .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a10 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a14 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a18 .long L0_1_set_265
	0xfc, 0xb2, 0xff, 0xff, //0x00005a1c .long L0_1_set_209
	0x6e, 0xb6, 0xff, 0xff, //0x00005a20 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a24 .long L0_1_set_265
	0xfc, 0xb2, 0xff, 0xff, //0x00005a28 .long L0_1_set_209
	0xfc, 0xb2, 0xff, 0xff, //0x00005a2c .long L0_1_set_209
	0xfc, 0xb2, 0xff, 0xff, //0x00005a30 .long L0_1_set_209
	0xfc, 0xb2, 0xff, 0xff, //0x00005a34 .long L0_1_set_209
	0xfc, 0xb2, 0xff, 0xff, //0x00005a38 .long L0_1_set_209
	0xfc, 0xb2, 0xff, 0xff, //0x00005a3c .long L0_1_set_209
	0xfc, 0xb2, 0xff, 0xff, //0x00005a40 .long L0_1_set_209
	0xfc, 0xb2, 0xff, 0xff, //0x00005a44 .long L0_1_set_209
	0xfc, 0xb2, 0xff, 0xff, //0x00005a48 .long L0_1_set_209
	0xfc, 0xb2, 0xff, 0xff, //0x00005a4c .long L0_1_set_209
	0x6e, 0xb6, 0xff, 0xff, //0x00005a50 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a54 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a58 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a5c .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a60 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a64 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a68 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a6c .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a70 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a74 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a78 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a7c .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a80 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a84 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a88 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a8c .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a90 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a94 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a98 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005a9c .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005aa0 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005aa4 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005aa8 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005aac .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005ab0 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005ab4 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005ab8 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005abc .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005ac0 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005ac4 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005ac8 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005acc .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005ad0 .long L0_1_set_265
	0x79, 0xb6, 0xff, 0xff, //0x00005ad4 .long L0_1_set_266
	0x6e, 0xb6, 0xff, 0xff, //0x00005ad8 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005adc .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005ae0 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005ae4 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005ae8 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005aec .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005af0 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005af4 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005af8 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005afc .long L0_1_set_265
	0x61, 0xb6, 0xff, 0xff, //0x00005b00 .long L0_1_set_264
	0x6e, 0xb6, 0xff, 0xff, //0x00005b04 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005b08 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005b0c .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005b10 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005b14 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005b18 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005b1c .long L0_1_set_265
	0x2c, 0xb5, 0xff, 0xff, //0x00005b20 .long L0_1_set_252
	0x6e, 0xb6, 0xff, 0xff, //0x00005b24 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005b28 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005b2c .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005b30 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005b34 .long L0_1_set_265
	0x2c, 0xb5, 0xff, 0xff, //0x00005b38 .long L0_1_set_252
	0x6e, 0xb6, 0xff, 0xff, //0x00005b3c .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005b40 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005b44 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005b48 .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005b4c .long L0_1_set_265
	0x6e, 0xb6, 0xff, 0xff, //0x00005b50 .long L0_1_set_265
	0x8b, 0xba, 0xff, 0xff, //0x00005b54 .long L0_1_set_292
	// // .set L0_2_set_615, LBB0_615-LJTI0_2
	// // .set L0_2_set_621, LBB0_621-LJTI0_2
	// // .set L0_2_set_623, LBB0_623-LJTI0_2
	// // .set L0_2_set_639, LBB0_639-LJTI0_2
	// // .set L0_2_set_618, LBB0_618-LJTI0_2
	// // .set L0_2_set_642, LBB0_642-LJTI0_2
	//0x00005b58 LJTI0_2
	0x53, 0xda, 0xff, 0xff, //0x00005b58 .long L0_2_set_615
	0x8a, 0xda, 0xff, 0xff, //0x00005b5c .long L0_2_set_621
	0xb5, 0xda, 0xff, 0xff, //0x00005b60 .long L0_2_set_623
	0x5f, 0xdc, 0xff, 0xff, //0x00005b64 .long L0_2_set_639
	0x6a, 0xda, 0xff, 0xff, //0x00005b68 .long L0_2_set_618
	0xf6, 0xde, 0xff, 0xff, //0x00005b6c .long L0_2_set_642
	// // .set L0_3_set_558, LBB0_558-LJTI0_3
	// // .set L0_3_set_557, LBB0_557-LJTI0_3
	// // .set L0_3_set_724, LBB0_724-LJTI0_3
	// // .set L0_3_set_739, LBB0_739-LJTI0_3
	// // .set L0_3_set_648, LBB0_648-LJTI0_3
	// // .set L0_3_set_773, LBB0_773-LJTI0_3
	// // .set L0_3_set_777, LBB0_777-LJTI0_3
	// // .set L0_3_set_780, LBB0_780-LJTI0_3
	// // .set L0_3_set_787, LBB0_787-LJTI0_3
	// // .set L0_3_set_775, LBB0_775-LJTI0_3
	//0x00005b70 LJTI0_3
	0xdf, 0xd6, 0xff, 0xff, //0x00005b70 .long L0_3_set_558
	0xd8, 0xd6, 0xff, 0xff, //0x00005b74 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005b78 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005b7c .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005b80 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005b84 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005b88 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005b8c .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005b90 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005b94 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005b98 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005b9c .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005ba0 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005ba4 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005ba8 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005bac .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005bb0 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005bb4 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005bb8 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005bbc .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005bc0 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005bc4 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005bc8 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005bcc .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005bd0 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005bd4 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005bd8 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005bdc .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005be0 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005be4 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005be8 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005bec .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005bf0 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005bf4 .long L0_3_set_557
	0x12, 0xe2, 0xff, 0xff, //0x00005bf8 .long L0_3_set_724
	0xd8, 0xd6, 0xff, 0xff, //0x00005bfc .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c00 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c04 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c08 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c0c .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c10 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c14 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c18 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c1c .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c20 .long L0_3_set_557
	0xa0, 0xe3, 0xff, 0xff, //0x00005c24 .long L0_3_set_739
	0xd8, 0xd6, 0xff, 0xff, //0x00005c28 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c2c .long L0_3_set_557
	0x80, 0xdc, 0xff, 0xff, //0x00005c30 .long L0_3_set_648
	0x80, 0xdc, 0xff, 0xff, //0x00005c34 .long L0_3_set_648
	0x80, 0xdc, 0xff, 0xff, //0x00005c38 .long L0_3_set_648
	0x80, 0xdc, 0xff, 0xff, //0x00005c3c .long L0_3_set_648
	0x80, 0xdc, 0xff, 0xff, //0x00005c40 .long L0_3_set_648
	0x80, 0xdc, 0xff, 0xff, //0x00005c44 .long L0_3_set_648
	0x80, 0xdc, 0xff, 0xff, //0x00005c48 .long L0_3_set_648
	0x80, 0xdc, 0xff, 0xff, //0x00005c4c .long L0_3_set_648
	0x80, 0xdc, 0xff, 0xff, //0x00005c50 .long L0_3_set_648
	0x80, 0xdc, 0xff, 0xff, //0x00005c54 .long L0_3_set_648
	0xd8, 0xd6, 0xff, 0xff, //0x00005c58 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c5c .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c60 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c64 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c68 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c6c .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c70 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c74 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c78 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c7c .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c80 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c84 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c88 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c8c .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c90 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c94 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c98 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005c9c .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005ca0 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005ca4 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005ca8 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005cac .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005cb0 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005cb4 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005cb8 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005cbc .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005cc0 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005cc4 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005cc8 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005ccc .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005cd0 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005cd4 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005cd8 .long L0_3_set_557
	0xfb, 0xe5, 0xff, 0xff, //0x00005cdc .long L0_3_set_773
	0xd8, 0xd6, 0xff, 0xff, //0x00005ce0 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005ce4 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005ce8 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005cec .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005cf0 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005cf4 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005cf8 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005cfc .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005d00 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005d04 .long L0_3_set_557
	0x43, 0xe6, 0xff, 0xff, //0x00005d08 .long L0_3_set_777
	0xd8, 0xd6, 0xff, 0xff, //0x00005d0c .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005d10 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005d14 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005d18 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005d1c .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005d20 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005d24 .long L0_3_set_557
	0x70, 0xe6, 0xff, 0xff, //0x00005d28 .long L0_3_set_780
	0xd8, 0xd6, 0xff, 0xff, //0x00005d2c .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005d30 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005d34 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005d38 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005d3c .long L0_3_set_557
	0x94, 0xe6, 0xff, 0xff, //0x00005d40 .long L0_3_set_787
	0xd8, 0xd6, 0xff, 0xff, //0x00005d44 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005d48 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005d4c .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005d50 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005d54 .long L0_3_set_557
	0xd8, 0xd6, 0xff, 0xff, //0x00005d58 .long L0_3_set_557
	0x1f, 0xe6, 0xff, 0xff, //0x00005d5c .long L0_3_set_775
	// // .set L0_4_set_767, LBB0_767-LJTI0_4
	// // .set L0_4_set_797, LBB0_797-LJTI0_4
	// // .set L0_4_set_771, LBB0_771-LJTI0_4
	// // .set L0_4_set_764, LBB0_764-LJTI0_4
	// // .set L0_4_set_769, LBB0_769-LJTI0_4
	//0x00005d60 LJTI0_4
	0xba, 0xe3, 0xff, 0xff, //0x00005d60 .long L0_4_set_767
	0x22, 0xe5, 0xff, 0xff, //0x00005d64 .long L0_4_set_797
	0xba, 0xe3, 0xff, 0xff, //0x00005d68 .long L0_4_set_767
	0xf0, 0xe3, 0xff, 0xff, //0x00005d6c .long L0_4_set_771
	0x22, 0xe5, 0xff, 0xff, //0x00005d70 .long L0_4_set_797
	0x90, 0xe3, 0xff, 0xff, //0x00005d74 .long L0_4_set_764
	0x90, 0xe3, 0xff, 0xff, //0x00005d78 .long L0_4_set_764
	0x90, 0xe3, 0xff, 0xff, //0x00005d7c .long L0_4_set_764
	0x90, 0xe3, 0xff, 0xff, //0x00005d80 .long L0_4_set_764
	0x90, 0xe3, 0xff, 0xff, //0x00005d84 .long L0_4_set_764
	0x90, 0xe3, 0xff, 0xff, //0x00005d88 .long L0_4_set_764
	0x90, 0xe3, 0xff, 0xff, //0x00005d8c .long L0_4_set_764
	0x90, 0xe3, 0xff, 0xff, //0x00005d90 .long L0_4_set_764
	0x90, 0xe3, 0xff, 0xff, //0x00005d94 .long L0_4_set_764
	0x90, 0xe3, 0xff, 0xff, //0x00005d98 .long L0_4_set_764
	0x22, 0xe5, 0xff, 0xff, //0x00005d9c .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005da0 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005da4 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005da8 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005dac .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005db0 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005db4 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005db8 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005dbc .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005dc0 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005dc4 .long L0_4_set_797
	0xd5, 0xe3, 0xff, 0xff, //0x00005dc8 .long L0_4_set_769
	0x22, 0xe5, 0xff, 0xff, //0x00005dcc .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005dd0 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005dd4 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005dd8 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005ddc .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005de0 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005de4 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005de8 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005dec .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005df0 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005df4 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005df8 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005dfc .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005e00 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005e04 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005e08 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005e0c .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005e10 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005e14 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005e18 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005e1c .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005e20 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005e24 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005e28 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005e2c .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005e30 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005e34 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005e38 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005e3c .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005e40 .long L0_4_set_797
	0x22, 0xe5, 0xff, 0xff, //0x00005e44 .long L0_4_set_797
	0xd5, 0xe3, 0xff, 0xff, //0x00005e48 .long L0_4_set_769
	// // .set L0_5_set_673, LBB0_673-LJTI0_5
	// // .set L0_5_set_709, LBB0_709-LJTI0_5
	// // .set L0_5_set_680, LBB0_680-LJTI0_5
	// // .set L0_5_set_675, LBB0_675-LJTI0_5
	// // .set L0_5_set_678, LBB0_678-LJTI0_5
	//0x00005e4c LJTI0_5
	0x84, 0xdb, 0xff, 0xff, //0x00005e4c .long L0_5_set_673
	0x7c, 0xde, 0xff, 0xff, //0x00005e50 .long L0_5_set_709
	0x84, 0xdb, 0xff, 0xff, //0x00005e54 .long L0_5_set_673
	0xe7, 0xdb, 0xff, 0xff, //0x00005e58 .long L0_5_set_680
	0x7c, 0xde, 0xff, 0xff, //0x00005e5c .long L0_5_set_709
	0xa4, 0xdb, 0xff, 0xff, //0x00005e60 .long L0_5_set_675
	0xa4, 0xdb, 0xff, 0xff, //0x00005e64 .long L0_5_set_675
	0xa4, 0xdb, 0xff, 0xff, //0x00005e68 .long L0_5_set_675
	0xa4, 0xdb, 0xff, 0xff, //0x00005e6c .long L0_5_set_675
	0xa4, 0xdb, 0xff, 0xff, //0x00005e70 .long L0_5_set_675
	0xa4, 0xdb, 0xff, 0xff, //0x00005e74 .long L0_5_set_675
	0xa4, 0xdb, 0xff, 0xff, //0x00005e78 .long L0_5_set_675
	0xa4, 0xdb, 0xff, 0xff, //0x00005e7c .long L0_5_set_675
	0xa4, 0xdb, 0xff, 0xff, //0x00005e80 .long L0_5_set_675
	0xa4, 0xdb, 0xff, 0xff, //0x00005e84 .long L0_5_set_675
	0x7c, 0xde, 0xff, 0xff, //0x00005e88 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005e8c .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005e90 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005e94 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005e98 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005e9c .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005ea0 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005ea4 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005ea8 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005eac .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005eb0 .long L0_5_set_709
	0xcc, 0xdb, 0xff, 0xff, //0x00005eb4 .long L0_5_set_678
	0x7c, 0xde, 0xff, 0xff, //0x00005eb8 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005ebc .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005ec0 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005ec4 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005ec8 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005ecc .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005ed0 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005ed4 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005ed8 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005edc .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005ee0 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005ee4 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005ee8 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005eec .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005ef0 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005ef4 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005ef8 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005efc .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005f00 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005f04 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005f08 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005f0c .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005f10 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005f14 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005f18 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005f1c .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005f20 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005f24 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005f28 .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005f2c .long L0_5_set_709
	0x7c, 0xde, 0xff, 0xff, //0x00005f30 .long L0_5_set_709
	0xcc, 0xdb, 0xff, 0xff, //0x00005f34 .long L0_5_set_678
	// // .set L0_6_set_558, LBB0_558-LJTI0_6
	// // .set L0_6_set_876, LBB0_876-LJTI0_6
	// // .set L0_6_set_877, LBB0_877-LJTI0_6
	// // .set L0_6_set_564, LBB0_564-LJTI0_6
	// // .set L0_6_set_887, LBB0_887-LJTI0_6
	// // .set L0_6_set_911, LBB0_911-LJTI0_6
	// // .set L0_6_set_871, LBB0_871-LJTI0_6
	// // .set L0_6_set_914, LBB0_914-LJTI0_6
	//0x00005f38 LJTI0_6
	0x17, 0xd3, 0xff, 0xff, //0x00005f38 .long L0_6_set_558
	0xa7, 0xeb, 0xff, 0xff, //0x00005f3c .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005f40 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005f44 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005f48 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005f4c .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005f50 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005f54 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005f58 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005f5c .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005f60 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005f64 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005f68 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005f6c .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005f70 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005f74 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005f78 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005f7c .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005f80 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005f84 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005f88 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005f8c .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005f90 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005f94 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005f98 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005f9c .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005fa0 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005fa4 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005fa8 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005fac .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005fb0 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005fb4 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005fb8 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005fbc .long L0_6_set_876
	0xaf, 0xeb, 0xff, 0xff, //0x00005fc0 .long L0_6_set_877
	0xa7, 0xeb, 0xff, 0xff, //0x00005fc4 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005fc8 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005fcc .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005fd0 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005fd4 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005fd8 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005fdc .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005fe0 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005fe4 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005fe8 .long L0_6_set_876
	0x90, 0xd3, 0xff, 0xff, //0x00005fec .long L0_6_set_564
	0xa7, 0xeb, 0xff, 0xff, //0x00005ff0 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00005ff4 .long L0_6_set_876
	0x90, 0xd3, 0xff, 0xff, //0x00005ff8 .long L0_6_set_564
	0x90, 0xd3, 0xff, 0xff, //0x00005ffc .long L0_6_set_564
	0x90, 0xd3, 0xff, 0xff, //0x00006000 .long L0_6_set_564
	0x90, 0xd3, 0xff, 0xff, //0x00006004 .long L0_6_set_564
	0x90, 0xd3, 0xff, 0xff, //0x00006008 .long L0_6_set_564
	0x90, 0xd3, 0xff, 0xff, //0x0000600c .long L0_6_set_564
	0x90, 0xd3, 0xff, 0xff, //0x00006010 .long L0_6_set_564
	0x90, 0xd3, 0xff, 0xff, //0x00006014 .long L0_6_set_564
	0x90, 0xd3, 0xff, 0xff, //0x00006018 .long L0_6_set_564
	0x90, 0xd3, 0xff, 0xff, //0x0000601c .long L0_6_set_564
	0xa7, 0xeb, 0xff, 0xff, //0x00006020 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006024 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006028 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x0000602c .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006030 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006034 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006038 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x0000603c .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006040 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006044 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006048 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x0000604c .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006050 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006054 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006058 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x0000605c .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006060 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006064 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006068 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x0000606c .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006070 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006074 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006078 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x0000607c .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006080 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006084 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006088 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x0000608c .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006090 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006094 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006098 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x0000609c .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x000060a0 .long L0_6_set_876
	0xc1, 0xec, 0xff, 0xff, //0x000060a4 .long L0_6_set_887
	0xa7, 0xeb, 0xff, 0xff, //0x000060a8 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x000060ac .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x000060b0 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x000060b4 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x000060b8 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x000060bc .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x000060c0 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x000060c4 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x000060c8 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x000060cc .long L0_6_set_876
	0xf3, 0xf0, 0xff, 0xff, //0x000060d0 .long L0_6_set_911
	0xa7, 0xeb, 0xff, 0xff, //0x000060d4 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x000060d8 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x000060dc .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x000060e0 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x000060e4 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x000060e8 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x000060ec .long L0_6_set_876
	0x72, 0xeb, 0xff, 0xff, //0x000060f0 .long L0_6_set_871
	0xa7, 0xeb, 0xff, 0xff, //0x000060f4 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x000060f8 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x000060fc .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006100 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006104 .long L0_6_set_876
	0x72, 0xeb, 0xff, 0xff, //0x00006108 .long L0_6_set_871
	0xa7, 0xeb, 0xff, 0xff, //0x0000610c .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006110 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006114 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006118 .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x0000611c .long L0_6_set_876
	0xa7, 0xeb, 0xff, 0xff, //0x00006120 .long L0_6_set_876
	0x0d, 0xf1, 0xff, 0xff, //0x00006124 .long L0_6_set_914
	//0x00006128 .p2align 2, 0x00
	//0x00006128 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00006128 .long 2
	0x00, 0x00, 0x00, 0x00, //0x0000612c .p2align 4, 0x00
	//0x00006130 __UnquoteTab
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006130 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006140 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2f, //0x00006150 QUAD $0x0000000000220000; QUAD $0x2f00000000000000  // .ascii 16, '\x00\x00"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00/'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006160 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006170 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5c, 0x00, 0x00, 0x00, //0x00006180 QUAD $0x0000000000000000; QUAD $0x0000005c00000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\\\x00\x00\x00'
	0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x00, //0x00006190 QUAD $0x000c000000080000; QUAD $0x000a000000000000  // .ascii 16, '\x00\x00\x08\x00\x00\x00\x0c\x00\x00\x00\x00\x00\x00\x00\n\x00'
	0x00, 0x00, 0x0d, 0x00, 0x09, 0xff, //0x000061a0 LONG $0x000d0000; WORD $0xff09  // .ascii 6, '\x00\x00\r\x00\t\xff'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061a6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061b6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061c6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061d6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061e6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061f6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006206 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006216 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006226 QUAD $0x0000000000000000; WORD $0x0000  // .space 10, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
}
 
