// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__f32toa = 16
)

const (
    _stack__f32toa = 48
)

const (
    _size__f32toa = 3328
)

var (
    _pcsp__f32toa = [][2]uint32{
        {1, 0},
        {4, 8},
        {6, 16},
        {8, 24},
        {10, 32},
        {12, 40},
        {3286, 48},
        {3287, 40},
        {3289, 32},
        {3291, 24},
        {3293, 16},
        {3295, 8},
        {3296, 0},
        {3318, 48},
    }
)

var _cfunc_f32toa = []loader.CFunc{
    {"_f32toa_entry", 0,  _entry__f32toa, 0, nil},
    {"_f32toa", _entry__f32toa, _size__f32toa, _stack__f32toa, _pcsp__f32toa},
}
