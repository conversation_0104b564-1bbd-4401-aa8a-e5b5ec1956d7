// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package sse

var _text_f32toa = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, // QUAD $0x3030303030303030; QUAD $0x3030303030303030  // .space 16, '0000000000000000'
	//0x00000010 .p2align 4, 0x90
	//0x00000010 _f32toa
	0x55, //0x00000010 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000011 movq         %rsp, %rbp
	0x41, 0x57, //0x00000014 pushq        %r15
	0x41, 0x56, //0x00000016 pushq        %r14
	0x41, 0x55, //0x00000018 pushq        %r13
	0x41, 0x54, //0x0000001a pushq        %r12
	0x53, //0x0000001c pushq        %rbx
	0x66, 0x0f, 0x7e, 0xc0, //0x0000001d movd         %xmm0, %eax
	0x89, 0xc1, //0x00000021 movl         %eax, %ecx
	0xc1, 0xe9, 0x17, //0x00000023 shrl         $23, %ecx
	0x0f, 0xb6, 0xd9, //0x00000026 movzbl       %cl, %ebx
	0x81, 0xfb, 0xff, 0x00, 0x00, 0x00, //0x00000029 cmpl         $255, %ebx
	0x0f, 0x84, 0xbc, 0x0c, 0x00, 0x00, //0x0000002f je           LBB0_139
	0xc6, 0x07, 0x2d, //0x00000035 movb         $45, (%rdi)
	0x41, 0x89, 0xc1, //0x00000038 movl         %eax, %r9d
	0x41, 0xc1, 0xe9, 0x1f, //0x0000003b shrl         $31, %r9d
	0x4e, 0x8d, 0x04, 0x0f, //0x0000003f leaq         (%rdi,%r9), %r8
	0xa9, 0xff, 0xff, 0xff, 0x7f, //0x00000043 testl        $2147483647, %eax
	0x0f, 0x84, 0xc6, 0x01, 0x00, 0x00, //0x00000048 je           LBB0_14
	0x25, 0xff, 0xff, 0x7f, 0x00, //0x0000004e andl         $8388607, %eax
	0x85, 0xdb, //0x00000053 testl        %ebx, %ebx
	0x0f, 0x84, 0x9e, 0x0c, 0x00, 0x00, //0x00000055 je           LBB0_140
	0x8d, 0xb0, 0x00, 0x00, 0x80, 0x00, //0x0000005b leal         $8388608(%rax), %esi
	0x44, 0x8d, 0xbb, 0x6a, 0xff, 0xff, 0xff, //0x00000061 leal         $-150(%rbx), %r15d
	0x8d, 0x4b, 0x81, //0x00000068 leal         $-127(%rbx), %ecx
	0x83, 0xf9, 0x17, //0x0000006b cmpl         $23, %ecx
	0x0f, 0x87, 0x1b, 0x00, 0x00, 0x00, //0x0000006e ja           LBB0_5
	0xb9, 0x96, 0x00, 0x00, 0x00, //0x00000074 movl         $150, %ecx
	0x29, 0xd9, //0x00000079 subl         %ebx, %ecx
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x0000007b movq         $-1, %rdx
	0x48, 0xd3, 0xe2, //0x00000082 shlq         %cl, %rdx
	0xf7, 0xd2, //0x00000085 notl         %edx
	0x85, 0xf2, //0x00000087 testl        %esi, %edx
	0x0f, 0x84, 0x12, 0x04, 0x00, 0x00, //0x00000089 je           LBB0_32
	//0x0000008f LBB0_5
	0x41, 0x89, 0xf6, //0x0000008f movl         %esi, %r14d
	0x41, 0x83, 0xe6, 0x01, //0x00000092 andl         $1, %r14d
	0x85, 0xc0, //0x00000096 testl        %eax, %eax
	0x0f, 0x94, 0xc0, //0x00000098 sete         %al
	0x83, 0xfb, 0x01, //0x0000009b cmpl         $1, %ebx
	0x0f, 0x97, 0xc1, //0x0000009e seta         %cl
	0x20, 0xc1, //0x000000a1 andb         %al, %cl
	0x0f, 0xb6, 0xc9, //0x000000a3 movzbl       %cl, %ecx
	0x41, 0x89, 0xf2, //0x000000a6 movl         %esi, %r10d
	0x41, 0xc1, 0xe2, 0x02, //0x000000a9 shll         $2, %r10d
	0x8d, 0x44, 0xb1, 0xfe, //0x000000ad leal         $-2(%rcx,%rsi,4), %eax
	0x45, 0x69, 0xdf, 0x13, 0x44, 0x13, 0x00, //0x000000b1 imull        $1262611, %r15d, %r11d
	0x31, 0xd2, //0x000000b8 xorl         %edx, %edx
	0x84, 0xc9, //0x000000ba testb        %cl, %cl
	0xb9, 0xff, 0xfe, 0x07, 0x00, //0x000000bc movl         $524031, %ecx
	0x0f, 0x44, 0xca, //0x000000c1 cmovel       %edx, %ecx
	0x41, 0x29, 0xcb, //0x000000c4 subl         %ecx, %r11d
	0x41, 0xc1, 0xfb, 0x16, //0x000000c7 sarl         $22, %r11d
	0x41, 0x69, 0xcb, 0xb1, 0x6c, 0xe5, 0xff, //0x000000cb imull        $-1741647, %r11d, %ecx
	0xc1, 0xe9, 0x13, //0x000000d2 shrl         $19, %ecx
	0x44, 0x01, 0xf9, //0x000000d5 addl         %r15d, %ecx
	0xba, 0x1f, 0x00, 0x00, 0x00, //0x000000d8 movl         $31, %edx
	0x44, 0x29, 0xda, //0x000000dd subl         %r11d, %edx
	0x48, 0x63, 0xd2, //0x000000e0 movslq       %edx, %rdx
	0x48, 0x8d, 0x1d, 0xf6, 0x0c, 0x00, 0x00, //0x000000e3 leaq         $3318(%rip), %rbx  /* _pow10_ceil_sig_f32.g+0(%rip) */
	0xfe, 0xc1, //0x000000ea incb         %cl
	0xd3, 0xe0, //0x000000ec shll         %cl, %eax
	0x4c, 0x8b, 0x24, 0xd3, //0x000000ee movq         (%rbx,%rdx,8), %r12
	0x49, 0xf7, 0xe4, //0x000000f2 mulq         %r12
	0x48, 0xc1, 0xe8, 0x20, //0x000000f5 shrq         $32, %rax
	0x31, 0xdb, //0x000000f9 xorl         %ebx, %ebx
	0x83, 0xf8, 0x01, //0x000000fb cmpl         $1, %eax
	0x0f, 0x97, 0xc3, //0x000000fe seta         %bl
	0x41, 0xd3, 0xe2, //0x00000101 shll         %cl, %r10d
	0x09, 0xd3, //0x00000104 orl          %edx, %ebx
	0x4c, 0x89, 0xd0, //0x00000106 movq         %r10, %rax
	0x49, 0xf7, 0xe4, //0x00000109 mulq         %r12
	0x49, 0x89, 0xd2, //0x0000010c movq         %rdx, %r10
	0x48, 0xc1, 0xe8, 0x20, //0x0000010f shrq         $32, %rax
	0x45, 0x31, 0xff, //0x00000113 xorl         %r15d, %r15d
	0x83, 0xf8, 0x01, //0x00000116 cmpl         $1, %eax
	0x41, 0x0f, 0x97, 0xc7, //0x00000119 seta         %r15b
	0x8d, 0x04, 0xb5, 0x02, 0x00, 0x00, 0x00, //0x0000011d leal         $2(,%rsi,4), %eax
	0xd3, 0xe0, //0x00000124 shll         %cl, %eax
	0x45, 0x09, 0xd7, //0x00000126 orl          %r10d, %r15d
	0x49, 0xf7, 0xe4, //0x00000129 mulq         %r12
	0x48, 0xc1, 0xe8, 0x20, //0x0000012c shrq         $32, %rax
	0x31, 0xc9, //0x00000130 xorl         %ecx, %ecx
	0x83, 0xf8, 0x01, //0x00000132 cmpl         $1, %eax
	0x0f, 0x97, 0xc1, //0x00000135 seta         %cl
	0x09, 0xd1, //0x00000138 orl          %edx, %ecx
	0x44, 0x01, 0xf3, //0x0000013a addl         %r14d, %ebx
	0x44, 0x29, 0xf1, //0x0000013d subl         %r14d, %ecx
	0x41, 0x83, 0xff, 0x28, //0x00000140 cmpl         $40, %r15d
	0x0f, 0x82, 0x9a, 0x00, 0x00, 0x00, //0x00000144 jb           LBB0_12
	0x44, 0x89, 0xd2, //0x0000014a movl         %r10d, %edx
	0xb8, 0xcd, 0xcc, 0xcc, 0xcc, //0x0000014d movl         $3435973837, %eax
	0x48, 0x0f, 0xaf, 0xc2, //0x00000152 imulq        %rdx, %rax
	0x48, 0xc1, 0xe8, 0x25, //0x00000156 shrq         $37, %rax
	0x41, 0x89, 0xde, //0x0000015a movl         %ebx, %r14d
	0x48, 0x8d, 0x34, 0xc5, 0x00, 0x00, 0x00, 0x00, //0x0000015d leaq         (,%rax,8), %rsi
	0x48, 0x8d, 0x14, 0xb6, //0x00000165 leaq         (%rsi,%rsi,4), %rdx
	0x4c, 0x39, 0xf2, //0x00000169 cmpq         %r14, %rdx
	0x41, 0x0f, 0x93, 0xc4, //0x0000016c setae        %r12b
	0x4c, 0x8d, 0x74, 0xb6, 0x28, //0x00000170 leaq         $40(%rsi,%rsi,4), %r14
	0x89, 0xce, //0x00000175 movl         %ecx, %esi
	0x49, 0x39, 0xf6, //0x00000177 cmpq         %rsi, %r14
	0x0f, 0x96, 0xc2, //0x0000017a setbe        %dl
	0x41, 0x38, 0xd4, //0x0000017d cmpb         %dl, %r12b
	0x0f, 0x84, 0x5e, 0x00, 0x00, 0x00, //0x00000180 je           LBB0_12
	0x45, 0x31, 0xed, //0x00000186 xorl         %r13d, %r13d
	0x49, 0x39, 0xf6, //0x00000189 cmpq         %rsi, %r14
	0x41, 0x0f, 0x96, 0xc5, //0x0000018c setbe        %r13b
	0x41, 0x01, 0xc5, //0x00000190 addl         %eax, %r13d
	0x41, 0xff, 0xc3, //0x00000193 incl         %r11d
	0x41, 0x81, 0xfd, 0xa0, 0x86, 0x01, 0x00, //0x00000196 cmpl         $100000, %r13d
	0x0f, 0x83, 0xb0, 0x00, 0x00, 0x00, //0x0000019d jae          LBB0_18
	//0x000001a3 LBB0_8
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x000001a3 movl         $1, %eax
	0x41, 0x83, 0xfd, 0x0a, //0x000001a8 cmpl         $10, %r13d
	0x0f, 0x82, 0xd4, 0x00, 0x00, 0x00, //0x000001ac jb           LBB0_22
	0xb8, 0x02, 0x00, 0x00, 0x00, //0x000001b2 movl         $2, %eax
	0x41, 0x83, 0xfd, 0x64, //0x000001b7 cmpl         $100, %r13d
	0x0f, 0x82, 0xc5, 0x00, 0x00, 0x00, //0x000001bb jb           LBB0_22
	0xb8, 0x03, 0x00, 0x00, 0x00, //0x000001c1 movl         $3, %eax
	0x41, 0x81, 0xfd, 0xe8, 0x03, 0x00, 0x00, //0x000001c6 cmpl         $1000, %r13d
	0x0f, 0x82, 0xb3, 0x00, 0x00, 0x00, //0x000001cd jb           LBB0_22
	0x41, 0x81, 0xfd, 0x10, 0x27, 0x00, 0x00, //0x000001d3 cmpl         $10000, %r13d
	0xb8, 0x05, 0x00, 0x00, 0x00, //0x000001da movl         $5, %eax
	0xe9, 0x9f, 0x00, 0x00, 0x00, //0x000001df jmp          LBB0_21
	//0x000001e4 LBB0_12
	0x4d, 0x89, 0xd6, //0x000001e4 movq         %r10, %r14
	0x49, 0xc1, 0xee, 0x02, //0x000001e7 shrq         $2, %r14
	0x44, 0x89, 0xd6, //0x000001eb movl         %r10d, %esi
	0x83, 0xe6, 0xfc, //0x000001ee andl         $-4, %esi
	0x39, 0xf3, //0x000001f1 cmpl         %esi, %ebx
	0x0f, 0x96, 0xc2, //0x000001f3 setbe        %dl
	0x8d, 0x5e, 0x04, //0x000001f6 leal         $4(%rsi), %ebx
	0x39, 0xcb, //0x000001f9 cmpl         %ecx, %ebx
	0x0f, 0x96, 0xc0, //0x000001fb setbe        %al
	0x38, 0xc2, //0x000001fe cmpb         %al, %dl
	0x0f, 0x84, 0x1d, 0x00, 0x00, 0x00, //0x00000200 je           LBB0_15
	0x45, 0x31, 0xed, //0x00000206 xorl         %r13d, %r13d
	0x39, 0xcb, //0x00000209 cmpl         %ecx, %ebx
	0x41, 0x0f, 0x96, 0xc5, //0x0000020b setbe        %r13b
	0xe9, 0x2f, 0x00, 0x00, 0x00, //0x0000020f jmp          LBB0_17
	//0x00000214 LBB0_14
	0x41, 0xc6, 0x00, 0x30, //0x00000214 movb         $48, (%r8)
	0x41, 0x29, 0xf8, //0x00000218 subl         %edi, %r8d
	0x41, 0xff, 0xc0, //0x0000021b incl         %r8d
	0xe9, 0xc0, 0x0a, 0x00, 0x00, //0x0000021e jmp          LBB0_138
	//0x00000223 LBB0_15
	0x83, 0xce, 0x02, //0x00000223 orl          $2, %esi
	0x41, 0xbd, 0x01, 0x00, 0x00, 0x00, //0x00000226 movl         $1, %r13d
	0x41, 0x39, 0xf7, //0x0000022c cmpl         %esi, %r15d
	0x0f, 0x87, 0x0e, 0x00, 0x00, 0x00, //0x0000022f ja           LBB0_17
	0x0f, 0x94, 0xc0, //0x00000235 sete         %al
	0x41, 0xc0, 0xea, 0x02, //0x00000238 shrb         $2, %r10b
	0x41, 0x20, 0xc2, //0x0000023c andb         %al, %r10b
	0x45, 0x0f, 0xb6, 0xea, //0x0000023f movzbl       %r10b, %r13d
	//0x00000243 LBB0_17
	0x45, 0x01, 0xf5, //0x00000243 addl         %r14d, %r13d
	0x41, 0x81, 0xfd, 0xa0, 0x86, 0x01, 0x00, //0x00000246 cmpl         $100000, %r13d
	0x0f, 0x82, 0x50, 0xff, 0xff, 0xff, //0x0000024d jb           LBB0_8
	//0x00000253 LBB0_18
	0xb8, 0x06, 0x00, 0x00, 0x00, //0x00000253 movl         $6, %eax
	0x41, 0x81, 0xfd, 0x40, 0x42, 0x0f, 0x00, //0x00000258 cmpl         $1000000, %r13d
	0x0f, 0x82, 0x21, 0x00, 0x00, 0x00, //0x0000025f jb           LBB0_22
	0xb8, 0x07, 0x00, 0x00, 0x00, //0x00000265 movl         $7, %eax
	0x41, 0x81, 0xfd, 0x80, 0x96, 0x98, 0x00, //0x0000026a cmpl         $10000000, %r13d
	0x0f, 0x82, 0x0f, 0x00, 0x00, 0x00, //0x00000271 jb           LBB0_22
	0x41, 0x81, 0xfd, 0x00, 0xe1, 0xf5, 0x05, //0x00000277 cmpl         $100000000, %r13d
	0xb8, 0x09, 0x00, 0x00, 0x00, //0x0000027e movl         $9, %eax
	//0x00000283 LBB0_21
	0x83, 0xd8, 0x00, //0x00000283 sbbl         $0, %eax
	//0x00000286 LBB0_22
	0x46, 0x8d, 0x14, 0x18, //0x00000286 leal         (%rax,%r11), %r10d
	0x42, 0x8d, 0x4c, 0x18, 0x05, //0x0000028a leal         $5(%rax,%r11), %ecx
	0x83, 0xf9, 0x1b, //0x0000028f cmpl         $27, %ecx
	0x0f, 0x82, 0x77, 0x00, 0x00, 0x00, //0x00000292 jb           LBB0_26
	0x89, 0xc0, //0x00000298 movl         %eax, %eax
	0x49, 0x8d, 0x5c, 0x00, 0x01, //0x0000029a leaq         $1(%r8,%rax), %rbx
	0x41, 0x81, 0xfd, 0x10, 0x27, 0x00, 0x00, //0x0000029f cmpl         $10000, %r13d
	0x0f, 0x82, 0xd9, 0x00, 0x00, 0x00, //0x000002a6 jb           LBB0_30
	0x44, 0x89, 0xe8, //0x000002ac movl         %r13d, %eax
	0x41, 0xbe, 0x59, 0x17, 0xb7, 0xd1, //0x000002af movl         $3518437209, %r14d
	0x4c, 0x0f, 0xaf, 0xf0, //0x000002b5 imulq        %rax, %r14
	0x49, 0xc1, 0xee, 0x2d, //0x000002b9 shrq         $45, %r14
	0x41, 0x69, 0xc6, 0xf0, 0xd8, 0xff, 0xff, //0x000002bd imull        $-10000, %r14d, %eax
	0x44, 0x01, 0xe8, //0x000002c4 addl         %r13d, %eax
	0x0f, 0x84, 0xa3, 0x04, 0x00, 0x00, //0x000002c7 je           LBB0_62
	0x89, 0xc1, //0x000002cd movl         %eax, %ecx
	0x48, 0x69, 0xc9, 0x1f, 0x85, 0xeb, 0x51, //0x000002cf imulq        $1374389535, %rcx, %rcx
	0x48, 0xc1, 0xe9, 0x25, //0x000002d6 shrq         $37, %rcx
	0x6b, 0xd1, 0x64, //0x000002da imull        $100, %ecx, %edx
	0x29, 0xd0, //0x000002dd subl         %edx, %eax
	0x48, 0x8d, 0x15, 0x2a, 0x0a, 0x00, 0x00, //0x000002df leaq         $2602(%rip), %rdx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x42, //0x000002e6 movzwl       (%rdx,%rax,2), %eax
	0x66, 0x89, 0x43, 0xfe, //0x000002ea movw         %ax, $-2(%rbx)
	0x0f, 0xb7, 0x04, 0x4a, //0x000002ee movzwl       (%rdx,%rcx,2), %eax
	0x66, 0x89, 0x43, 0xfc, //0x000002f2 movw         %ax, $-4(%rbx)
	0x45, 0x31, 0xc9, //0x000002f6 xorl         %r9d, %r9d
	0x48, 0x8d, 0x4b, 0xfc, //0x000002f9 leaq         $-4(%rbx), %rcx
	0x41, 0x83, 0xfe, 0x64, //0x000002fd cmpl         $100, %r14d
	0x0f, 0x83, 0x91, 0x00, 0x00, 0x00, //0x00000301 jae          LBB0_64
	//0x00000307 LBB0_31
	0x44, 0x89, 0xf2, //0x00000307 movl         %r14d, %edx
	0xe9, 0xd4, 0x00, 0x00, 0x00, //0x0000030a jmp          LBB0_66
	//0x0000030f LBB0_26
	0x41, 0x89, 0xc4, //0x0000030f movl         %eax, %r12d
	0x45, 0x85, 0xdb, //0x00000312 testl        %r11d, %r11d
	0x0f, 0x88, 0x1d, 0x02, 0x00, 0x00, //0x00000315 js           LBB0_38
	0x4b, 0x8d, 0x34, 0x20, //0x0000031b leaq         (%r8,%r12), %rsi
	0x41, 0x81, 0xfd, 0x10, 0x27, 0x00, 0x00, //0x0000031f cmpl         $10000, %r13d
	0x0f, 0x82, 0xa8, 0x02, 0x00, 0x00, //0x00000326 jb           LBB0_43
	0x44, 0x89, 0xe8, //0x0000032c movl         %r13d, %eax
	0xb9, 0x59, 0x17, 0xb7, 0xd1, //0x0000032f movl         $3518437209, %ecx
	0x48, 0x0f, 0xaf, 0xc8, //0x00000334 imulq        %rax, %rcx
	0x48, 0xc1, 0xe9, 0x2d, //0x00000338 shrq         $45, %rcx
	0x69, 0xc1, 0xf0, 0xd8, 0xff, 0xff, //0x0000033c imull        $-10000, %ecx, %eax
	0x44, 0x01, 0xe8, //0x00000342 addl         %r13d, %eax
	0x48, 0x69, 0xd0, 0x1f, 0x85, 0xeb, 0x51, //0x00000345 imulq        $1374389535, %rax, %rdx
	0x48, 0xc1, 0xea, 0x25, //0x0000034c shrq         $37, %rdx
	0x6b, 0xda, 0x64, //0x00000350 imull        $100, %edx, %ebx
	0x29, 0xd8, //0x00000353 subl         %ebx, %eax
	0x48, 0x8d, 0x1d, 0xb4, 0x09, 0x00, 0x00, //0x00000355 leaq         $2484(%rip), %rbx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x43, //0x0000035c movzwl       (%rbx,%rax,2), %eax
	0x66, 0x89, 0x46, 0xfe, //0x00000360 movw         %ax, $-2(%rsi)
	0x48, 0x8d, 0x46, 0xfc, //0x00000364 leaq         $-4(%rsi), %rax
	0x0f, 0xb7, 0x14, 0x53, //0x00000368 movzwl       (%rbx,%rdx,2), %edx
	0x66, 0x89, 0x56, 0xfc, //0x0000036c movw         %dx, $-4(%rsi)
	0x41, 0x89, 0xcd, //0x00000370 movl         %ecx, %r13d
	0x41, 0x83, 0xfd, 0x64, //0x00000373 cmpl         $100, %r13d
	0x0f, 0x83, 0x64, 0x02, 0x00, 0x00, //0x00000377 jae          LBB0_44
	//0x0000037d LBB0_29
	0x44, 0x89, 0xe9, //0x0000037d movl         %r13d, %ecx
	0xe9, 0x9e, 0x02, 0x00, 0x00, //0x00000380 jmp          LBB0_46
	//0x00000385 LBB0_30
	0x45, 0x31, 0xc9, //0x00000385 xorl         %r9d, %r9d
	0x48, 0x89, 0xd9, //0x00000388 movq         %rbx, %rcx
	0x45, 0x89, 0xee, //0x0000038b movl         %r13d, %r14d
	0x41, 0x83, 0xfe, 0x64, //0x0000038e cmpl         $100, %r14d
	0x0f, 0x82, 0x6f, 0xff, 0xff, 0xff, //0x00000392 jb           LBB0_31
	//0x00000398 LBB0_64
	0x48, 0xff, 0xc9, //0x00000398 decq         %rcx
	0x4c, 0x8d, 0x1d, 0x6e, 0x09, 0x00, 0x00, //0x0000039b leaq         $2414(%rip), %r11  /* _Digits+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000003a2 .p2align 4, 0x90
	//0x000003b0 LBB0_65
	0x44, 0x89, 0xf2, //0x000003b0 movl         %r14d, %edx
	0x48, 0x69, 0xd2, 0x1f, 0x85, 0xeb, 0x51, //0x000003b3 imulq        $1374389535, %rdx, %rdx
	0x48, 0xc1, 0xea, 0x25, //0x000003ba shrq         $37, %rdx
	0x6b, 0xc2, 0x64, //0x000003be imull        $100, %edx, %eax
	0x44, 0x89, 0xf6, //0x000003c1 movl         %r14d, %esi
	0x29, 0xc6, //0x000003c4 subl         %eax, %esi
	0x41, 0x0f, 0xb7, 0x04, 0x73, //0x000003c6 movzwl       (%r11,%rsi,2), %eax
	0x66, 0x89, 0x41, 0xff, //0x000003cb movw         %ax, $-1(%rcx)
	0x48, 0x83, 0xc1, 0xfe, //0x000003cf addq         $-2, %rcx
	0x41, 0x81, 0xfe, 0x0f, 0x27, 0x00, 0x00, //0x000003d3 cmpl         $9999, %r14d
	0x41, 0x89, 0xd6, //0x000003da movl         %edx, %r14d
	0x0f, 0x87, 0xcd, 0xff, 0xff, 0xff, //0x000003dd ja           LBB0_65
	//0x000003e3 LBB0_66
	0x49, 0x8d, 0x70, 0x01, //0x000003e3 leaq         $1(%r8), %rsi
	0x83, 0xfa, 0x0a, //0x000003e7 cmpl         $10, %edx
	0x0f, 0x82, 0x1d, 0x00, 0x00, 0x00, //0x000003ea jb           LBB0_68
	0x89, 0xd0, //0x000003f0 movl         %edx, %eax
	0x48, 0x8d, 0x0d, 0x17, 0x09, 0x00, 0x00, //0x000003f2 leaq         $2327(%rip), %rcx  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x41, //0x000003f9 movb         (%rcx,%rax,2), %dl
	0x8a, 0x44, 0x41, 0x01, //0x000003fc movb         $1(%rcx,%rax,2), %al
	0x41, 0x88, 0x50, 0x01, //0x00000400 movb         %dl, $1(%r8)
	0x41, 0x88, 0x40, 0x02, //0x00000404 movb         %al, $2(%r8)
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x00000408 jmp          LBB0_69
	//0x0000040d LBB0_68
	0x80, 0xc2, 0x30, //0x0000040d addb         $48, %dl
	0x88, 0x16, //0x00000410 movb         %dl, (%rsi)
	//0x00000412 LBB0_69
	0x4c, 0x29, 0xcb, //0x00000412 subq         %r9, %rbx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000415 .p2align 4, 0x90
	//0x00000420 LBB0_70
	0x80, 0x7b, 0xff, 0x30, //0x00000420 cmpb         $48, $-1(%rbx)
	0x48, 0x8d, 0x5b, 0xff, //0x00000424 leaq         $-1(%rbx), %rbx
	0x0f, 0x84, 0xf2, 0xff, 0xff, 0xff, //0x00000428 je           LBB0_70
	0x41, 0x88, 0x10, //0x0000042e movb         %dl, (%r8)
	0x48, 0x8d, 0x43, 0x01, //0x00000431 leaq         $1(%rbx), %rax
	0x48, 0x89, 0xc1, //0x00000435 movq         %rax, %rcx
	0x48, 0x29, 0xf1, //0x00000438 subq         %rsi, %rcx
	0x48, 0x83, 0xf9, 0x02, //0x0000043b cmpq         $2, %rcx
	0x0f, 0x8c, 0x06, 0x00, 0x00, 0x00, //0x0000043f jl           LBB0_73
	0xc6, 0x06, 0x2e, //0x00000445 movb         $46, (%rsi)
	0x48, 0x89, 0xc3, //0x00000448 movq         %rax, %rbx
	//0x0000044b LBB0_73
	0xc6, 0x03, 0x65, //0x0000044b movb         $101, (%rbx)
	0x45, 0x85, 0xd2, //0x0000044e testl        %r10d, %r10d
	0x0f, 0x8e, 0x42, 0x01, 0x00, 0x00, //0x00000451 jle          LBB0_76
	0x41, 0xff, 0xca, //0x00000457 decl         %r10d
	0xc6, 0x43, 0x01, 0x2b, //0x0000045a movb         $43, $1(%rbx)
	0x44, 0x89, 0xd0, //0x0000045e movl         %r10d, %eax
	0x83, 0xf8, 0x64, //0x00000461 cmpl         $100, %eax
	0x0f, 0x8c, 0x44, 0x01, 0x00, 0x00, //0x00000464 jl           LBB0_77
	//0x0000046a LBB0_75
	0x89, 0xc1, //0x0000046a movl         %eax, %ecx
	0xba, 0xcd, 0xcc, 0xcc, 0xcc, //0x0000046c movl         $3435973837, %edx
	0x48, 0x0f, 0xaf, 0xd1, //0x00000471 imulq        %rcx, %rdx
	0x48, 0xc1, 0xea, 0x23, //0x00000475 shrq         $35, %rdx
	0x8d, 0x0c, 0x12, //0x00000479 leal         (%rdx,%rdx), %ecx
	0x8d, 0x0c, 0x89, //0x0000047c leal         (%rcx,%rcx,4), %ecx
	0x29, 0xc8, //0x0000047f subl         %ecx, %eax
	0x48, 0x8d, 0x0d, 0x88, 0x08, 0x00, 0x00, //0x00000481 leaq         $2184(%rip), %rcx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x0c, 0x51, //0x00000488 movzwl       (%rcx,%rdx,2), %ecx
	0x66, 0x89, 0x4b, 0x02, //0x0000048c movw         %cx, $2(%rbx)
	0x0c, 0x30, //0x00000490 orb          $48, %al
	0x88, 0x43, 0x04, //0x00000492 movb         %al, $4(%rbx)
	0x48, 0x83, 0xc3, 0x05, //0x00000495 addq         $5, %rbx
	0x49, 0x89, 0xd8, //0x00000499 movq         %rbx, %r8
	0xe9, 0x3f, 0x08, 0x00, 0x00, //0x0000049c jmp          LBB0_137
	//0x000004a1 LBB0_32
	0xd3, 0xee, //0x000004a1 shrl         %cl, %esi
	0x81, 0xfe, 0xa0, 0x86, 0x01, 0x00, //0x000004a3 cmpl         $100000, %esi
	0x0f, 0x82, 0x17, 0x02, 0x00, 0x00, //0x000004a9 jb           LBB0_52
	0xb8, 0x06, 0x00, 0x00, 0x00, //0x000004af movl         $6, %eax
	0x81, 0xfe, 0x40, 0x42, 0x0f, 0x00, //0x000004b4 cmpl         $1000000, %esi
	0x0f, 0x82, 0x20, 0x00, 0x00, 0x00, //0x000004ba jb           LBB0_36
	0xb8, 0x07, 0x00, 0x00, 0x00, //0x000004c0 movl         $7, %eax
	0x81, 0xfe, 0x80, 0x96, 0x98, 0x00, //0x000004c5 cmpl         $10000000, %esi
	0x0f, 0x82, 0x0f, 0x00, 0x00, 0x00, //0x000004cb jb           LBB0_36
	0x81, 0xfe, 0x00, 0xe1, 0xf5, 0x05, //0x000004d1 cmpl         $100000000, %esi
	0xb8, 0x09, 0x00, 0x00, 0x00, //0x000004d7 movl         $9, %eax
	0x48, 0x83, 0xd8, 0x00, //0x000004dc sbbq         $0, %rax
	//0x000004e0 LBB0_36
	0x4c, 0x01, 0xc0, //0x000004e0 addq         %r8, %rax
	//0x000004e3 LBB0_37
	0x89, 0xf1, //0x000004e3 movl         %esi, %ecx
	0xba, 0x59, 0x17, 0xb7, 0xd1, //0x000004e5 movl         $3518437209, %edx
	0x48, 0x0f, 0xaf, 0xd1, //0x000004ea imulq        %rcx, %rdx
	0x48, 0xc1, 0xea, 0x2d, //0x000004ee shrq         $45, %rdx
	0x69, 0xca, 0xf0, 0xd8, 0xff, 0xff, //0x000004f2 imull        $-10000, %edx, %ecx
	0x01, 0xf1, //0x000004f8 addl         %esi, %ecx
	0x48, 0x69, 0xf1, 0x1f, 0x85, 0xeb, 0x51, //0x000004fa imulq        $1374389535, %rcx, %rsi
	0x48, 0xc1, 0xee, 0x25, //0x00000501 shrq         $37, %rsi
	0x6b, 0xde, 0x64, //0x00000505 imull        $100, %esi, %ebx
	0x29, 0xd9, //0x00000508 subl         %ebx, %ecx
	0x48, 0x8d, 0x1d, 0xff, 0x07, 0x00, 0x00, //0x0000050a leaq         $2047(%rip), %rbx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x0c, 0x4b, //0x00000511 movzwl       (%rbx,%rcx,2), %ecx
	0x66, 0x89, 0x48, 0xfe, //0x00000515 movw         %cx, $-2(%rax)
	0x0f, 0xb7, 0x0c, 0x73, //0x00000519 movzwl       (%rbx,%rsi,2), %ecx
	0x66, 0x89, 0x48, 0xfc, //0x0000051d movw         %cx, $-4(%rax)
	0x49, 0x89, 0xc1, //0x00000521 movq         %rax, %r9
	0x48, 0x83, 0xc0, 0xfc, //0x00000524 addq         $-4, %rax
	0x89, 0xd6, //0x00000528 movl         %edx, %esi
	0x83, 0xfe, 0x64, //0x0000052a cmpl         $100, %esi
	0x0f, 0x83, 0xd2, 0x01, 0x00, 0x00, //0x0000052d jae          LBB0_56
	0xe9, 0x07, 0x02, 0x00, 0x00, //0x00000533 jmp          LBB0_58
	//0x00000538 LBB0_38
	0x45, 0x85, 0xd2, //0x00000538 testl        %r10d, %r10d
	0x0f, 0x8f, 0x70, 0x04, 0x00, 0x00, //0x0000053b jg           LBB0_98
	0x66, 0x41, 0xc7, 0x00, 0x30, 0x2e, //0x00000541 movw         $11824, (%r8)
	0x49, 0x83, 0xc0, 0x02, //0x00000547 addq         $2, %r8
	0x45, 0x85, 0xd2, //0x0000054b testl        %r10d, %r10d
	0x0f, 0x89, 0x5d, 0x04, 0x00, 0x00, //0x0000054e jns          LBB0_98
	0x31, 0xf6, //0x00000554 xorl         %esi, %esi
	0x41, 0x83, 0xfa, 0xe0, //0x00000556 cmpl         $-32, %r10d
	0x0f, 0x87, 0x33, 0x04, 0x00, 0x00, //0x0000055a ja           LBB0_96
	0x45, 0x89, 0xd3, //0x00000560 movl         %r10d, %r11d
	0x41, 0xf7, 0xd3, //0x00000563 notl         %r11d
	0x49, 0xff, 0xc3, //0x00000566 incq         %r11
	0x4c, 0x89, 0xde, //0x00000569 movq         %r11, %rsi
	0x48, 0x83, 0xe6, 0xe0, //0x0000056c andq         $-32, %rsi
	0x48, 0x8d, 0x4e, 0xe0, //0x00000570 leaq         $-32(%rsi), %rcx
	0x48, 0x89, 0xc8, //0x00000574 movq         %rcx, %rax
	0x48, 0xc1, 0xe8, 0x05, //0x00000577 shrq         $5, %rax
	0x48, 0xff, 0xc0, //0x0000057b incq         %rax
	0x41, 0x89, 0xc6, //0x0000057e movl         %eax, %r14d
	0x41, 0x83, 0xe6, 0x07, //0x00000581 andl         $7, %r14d
	0x48, 0x81, 0xf9, 0xe0, 0x00, 0x00, 0x00, //0x00000585 cmpq         $224, %rcx
	0x0f, 0x83, 0x27, 0x03, 0x00, 0x00, //0x0000058c jae          LBB0_90
	0x31, 0xc0, //0x00000592 xorl         %eax, %eax
	0xe9, 0xbc, 0x03, 0x00, 0x00, //0x00000594 jmp          LBB0_92
	//0x00000599 LBB0_76
	0xc6, 0x43, 0x01, 0x2d, //0x00000599 movb         $45, $1(%rbx)
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x0000059d movl         $1, %eax
	0x44, 0x29, 0xd0, //0x000005a2 subl         %r10d, %eax
	0x83, 0xf8, 0x64, //0x000005a5 cmpl         $100, %eax
	0x0f, 0x8d, 0xbc, 0xfe, 0xff, 0xff, //0x000005a8 jge          LBB0_75
	//0x000005ae LBB0_77
	0x83, 0xf8, 0x0a, //0x000005ae cmpl         $10, %eax
	0x0f, 0x8c, 0xfe, 0x00, 0x00, 0x00, //0x000005b1 jl           LBB0_79
	0x48, 0x98, //0x000005b7 cltq         
	0x48, 0x8d, 0x0d, 0x50, 0x07, 0x00, 0x00, //0x000005b9 leaq         $1872(%rip), %rcx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x41, //0x000005c0 movzwl       (%rcx,%rax,2), %eax
	0x66, 0x89, 0x43, 0x02, //0x000005c4 movw         %ax, $2(%rbx)
	0x48, 0x83, 0xc3, 0x04, //0x000005c8 addq         $4, %rbx
	0x49, 0x89, 0xd8, //0x000005cc movq         %rbx, %r8
	0xe9, 0x0c, 0x07, 0x00, 0x00, //0x000005cf jmp          LBB0_137
	//0x000005d4 LBB0_43
	0x48, 0x89, 0xf0, //0x000005d4 movq         %rsi, %rax
	0x41, 0x83, 0xfd, 0x64, //0x000005d7 cmpl         $100, %r13d
	0x0f, 0x82, 0x9c, 0xfd, 0xff, 0xff, //0x000005db jb           LBB0_29
	//0x000005e1 LBB0_44
	0x48, 0xff, 0xc8, //0x000005e1 decq         %rax
	0x4c, 0x8d, 0x1d, 0x25, 0x07, 0x00, 0x00, //0x000005e4 leaq         $1829(%rip), %r11  /* _Digits+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, //0x000005eb .p2align 4, 0x90
	//0x000005f0 LBB0_45
	0x44, 0x89, 0xe9, //0x000005f0 movl         %r13d, %ecx
	0x48, 0x69, 0xc9, 0x1f, 0x85, 0xeb, 0x51, //0x000005f3 imulq        $1374389535, %rcx, %rcx
	0x48, 0xc1, 0xe9, 0x25, //0x000005fa shrq         $37, %rcx
	0x6b, 0xd9, 0x64, //0x000005fe imull        $100, %ecx, %ebx
	0x44, 0x89, 0xea, //0x00000601 movl         %r13d, %edx
	0x29, 0xda, //0x00000604 subl         %ebx, %edx
	0x41, 0x0f, 0xb7, 0x14, 0x53, //0x00000606 movzwl       (%r11,%rdx,2), %edx
	0x66, 0x89, 0x50, 0xff, //0x0000060b movw         %dx, $-1(%rax)
	0x48, 0x83, 0xc0, 0xfe, //0x0000060f addq         $-2, %rax
	0x41, 0x81, 0xfd, 0x0f, 0x27, 0x00, 0x00, //0x00000613 cmpl         $9999, %r13d
	0x41, 0x89, 0xcd, //0x0000061a movl         %ecx, %r13d
	0x0f, 0x87, 0xcd, 0xff, 0xff, 0xff, //0x0000061d ja           LBB0_45
	//0x00000623 LBB0_46
	0x49, 0x63, 0xc2, //0x00000623 movslq       %r10d, %rax
	0x83, 0xf9, 0x0a, //0x00000626 cmpl         $10, %ecx
	0x0f, 0x82, 0x22, 0x00, 0x00, 0x00, //0x00000629 jb           LBB0_48
	0x89, 0xc9, //0x0000062f movl         %ecx, %ecx
	0x48, 0x8d, 0x15, 0xd8, 0x06, 0x00, 0x00, //0x00000631 leaq         $1752(%rip), %rdx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x0c, 0x4a, //0x00000638 movzwl       (%rdx,%rcx,2), %ecx
	0x66, 0x41, 0x89, 0x08, //0x0000063c movw         %cx, (%r8)
	0x49, 0x01, 0xc0, //0x00000640 addq         %rax, %r8
	0x49, 0x39, 0xc4, //0x00000643 cmpq         %rax, %r12
	0x0f, 0x8c, 0x17, 0x00, 0x00, 0x00, //0x00000646 jl           LBB0_49
	0xe9, 0x8f, 0x06, 0x00, 0x00, //0x0000064c jmp          LBB0_137
	//0x00000651 LBB0_48
	0x80, 0xc1, 0x30, //0x00000651 addb         $48, %cl
	0x41, 0x88, 0x08, //0x00000654 movb         %cl, (%r8)
	0x49, 0x01, 0xc0, //0x00000657 addq         %rax, %r8
	0x49, 0x39, 0xc4, //0x0000065a cmpq         %rax, %r12
	0x0f, 0x8d, 0x7d, 0x06, 0x00, 0x00, //0x0000065d jge          LBB0_137
	//0x00000663 LBB0_49
	0x4b, 0x8d, 0x04, 0x21, //0x00000663 leaq         (%r9,%r12), %rax
	0x4c, 0x8d, 0x5c, 0x07, 0x01, //0x00000667 leaq         $1(%rdi,%rax), %r11
	0x4d, 0x39, 0xc3, //0x0000066c cmpq         %r8, %r11
	0x4d, 0x0f, 0x46, 0xd8, //0x0000066f cmovbeq      %r8, %r11
	0x4a, 0x8d, 0x0c, 0x0f, //0x00000673 leaq         (%rdi,%r9), %rcx
	0x4c, 0x01, 0xe1, //0x00000677 addq         %r12, %rcx
	0x49, 0x29, 0xcb, //0x0000067a subq         %rcx, %r11
	0x49, 0x83, 0xfb, 0x20, //0x0000067d cmpq         $32, %r11
	0x0f, 0x82, 0xf9, 0x01, 0x00, 0x00, //0x00000681 jb           LBB0_87
	0x4d, 0x89, 0xda, //0x00000687 movq         %r11, %r10
	0x49, 0x83, 0xe2, 0xe0, //0x0000068a andq         $-32, %r10
	0x49, 0x8d, 0x4a, 0xe0, //0x0000068e leaq         $-32(%r10), %rcx
	0x48, 0x89, 0xcb, //0x00000692 movq         %rcx, %rbx
	0x48, 0xc1, 0xeb, 0x05, //0x00000695 shrq         $5, %rbx
	0x48, 0xff, 0xc3, //0x00000699 incq         %rbx
	0x89, 0xda, //0x0000069c movl         %ebx, %edx
	0x83, 0xe2, 0x07, //0x0000069e andl         $7, %edx
	0x48, 0x81, 0xf9, 0xe0, 0x00, 0x00, 0x00, //0x000006a1 cmpq         $224, %rcx
	0x0f, 0x83, 0xdb, 0x00, 0x00, 0x00, //0x000006a8 jae          LBB0_80
	0x31, 0xc0, //0x000006ae xorl         %eax, %eax
	0xe9, 0x6f, 0x01, 0x00, 0x00, //0x000006b0 jmp          LBB0_82
	//0x000006b5 LBB0_79
	0x04, 0x30, //0x000006b5 addb         $48, %al
	0x88, 0x43, 0x02, //0x000006b7 movb         %al, $2(%rbx)
	0x48, 0x83, 0xc3, 0x03, //0x000006ba addq         $3, %rbx
	0x49, 0x89, 0xd8, //0x000006be movq         %rbx, %r8
	0xe9, 0x1a, 0x06, 0x00, 0x00, //0x000006c1 jmp          LBB0_137
	//0x000006c6 LBB0_52
	0x41, 0xb9, 0x01, 0x00, 0x00, 0x00, //0x000006c6 movl         $1, %r9d
	0x83, 0xfe, 0x0a, //0x000006cc cmpl         $10, %esi
	0x0f, 0x82, 0x21, 0x00, 0x00, 0x00, //0x000006cf jb           LBB0_55
	0x41, 0xb9, 0x02, 0x00, 0x00, 0x00, //0x000006d5 movl         $2, %r9d
	0x83, 0xfe, 0x64, //0x000006db cmpl         $100, %esi
	0x0f, 0x82, 0x12, 0x00, 0x00, 0x00, //0x000006de jb           LBB0_55
	0x41, 0xb9, 0x03, 0x00, 0x00, 0x00, //0x000006e4 movl         $3, %r9d
	0x81, 0xfe, 0xe8, 0x03, 0x00, 0x00, //0x000006ea cmpl         $1000, %esi
	0x0f, 0x83, 0x9e, 0x01, 0x00, 0x00, //0x000006f0 jae          LBB0_88
	//0x000006f6 LBB0_55
	0x4d, 0x01, 0xc1, //0x000006f6 addq         %r8, %r9
	0x4c, 0x89, 0xc8, //0x000006f9 movq         %r9, %rax
	0x83, 0xfe, 0x64, //0x000006fc cmpl         $100, %esi
	0x0f, 0x82, 0x3a, 0x00, 0x00, 0x00, //0x000006ff jb           LBB0_58
	//0x00000705 LBB0_56
	0x48, 0xff, 0xc8, //0x00000705 decq         %rax
	0x4c, 0x8d, 0x15, 0x01, 0x06, 0x00, 0x00, //0x00000708 leaq         $1537(%rip), %r10  /* _Digits+0(%rip) */
	0x90, //0x0000070f .p2align 4, 0x90
	//0x00000710 LBB0_57
	0x89, 0xf3, //0x00000710 movl         %esi, %ebx
	0x89, 0xf6, //0x00000712 movl         %esi, %esi
	0x48, 0x69, 0xf6, 0x1f, 0x85, 0xeb, 0x51, //0x00000714 imulq        $1374389535, %rsi, %rsi
	0x48, 0xc1, 0xee, 0x25, //0x0000071b shrq         $37, %rsi
	0x6b, 0xce, 0x64, //0x0000071f imull        $100, %esi, %ecx
	0x89, 0xda, //0x00000722 movl         %ebx, %edx
	0x29, 0xca, //0x00000724 subl         %ecx, %edx
	0x41, 0x0f, 0xb7, 0x0c, 0x52, //0x00000726 movzwl       (%r10,%rdx,2), %ecx
	0x66, 0x89, 0x48, 0xff, //0x0000072b movw         %cx, $-1(%rax)
	0x48, 0x83, 0xc0, 0xfe, //0x0000072f addq         $-2, %rax
	0x81, 0xfb, 0x0f, 0x27, 0x00, 0x00, //0x00000733 cmpl         $9999, %ebx
	0x0f, 0x87, 0xd1, 0xff, 0xff, 0xff, //0x00000739 ja           LBB0_57
	//0x0000073f LBB0_58
	0x83, 0xfe, 0x0a, //0x0000073f cmpl         $10, %esi
	0x0f, 0x82, 0x16, 0x00, 0x00, 0x00, //0x00000742 jb           LBB0_60
	0x89, 0xf0, //0x00000748 movl         %esi, %eax
	0x48, 0x8d, 0x0d, 0xbf, 0x05, 0x00, 0x00, //0x0000074a leaq         $1471(%rip), %rcx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x41, //0x00000751 movzwl       (%rcx,%rax,2), %eax
	0x66, 0x41, 0x89, 0x00, //0x00000755 movw         %ax, (%r8)
	0xe9, 0x07, 0x00, 0x00, 0x00, //0x00000759 jmp          LBB0_61
	//0x0000075e LBB0_60
	0x40, 0x80, 0xc6, 0x30, //0x0000075e addb         $48, %sil
	0x41, 0x88, 0x30, //0x00000762 movb         %sil, (%r8)
	//0x00000765 LBB0_61
	0x41, 0x29, 0xf9, //0x00000765 subl         %edi, %r9d
	0x45, 0x89, 0xc8, //0x00000768 movl         %r9d, %r8d
	0xe9, 0x73, 0x05, 0x00, 0x00, //0x0000076b jmp          LBB0_138
	//0x00000770 LBB0_62
	0x41, 0xb9, 0x04, 0x00, 0x00, 0x00, //0x00000770 movl         $4, %r9d
	0x48, 0x8d, 0x4b, 0xfc, //0x00000776 leaq         $-4(%rbx), %rcx
	0x41, 0x83, 0xfe, 0x64, //0x0000077a cmpl         $100, %r14d
	0x0f, 0x82, 0x83, 0xfb, 0xff, 0xff, //0x0000077e jb           LBB0_31
	0xe9, 0x0f, 0xfc, 0xff, 0xff, //0x00000784 jmp          LBB0_64
	//0x00000789 LBB0_80
	0x48, 0x29, 0xd3, //0x00000789 subq         %rdx, %rbx
	0x48, 0x8d, 0x8c, 0x07, 0xf0, 0x00, 0x00, 0x00, //0x0000078c leaq         $240(%rdi,%rax), %rcx
	0x31, 0xc0, //0x00000794 xorl         %eax, %eax
	0xf3, 0x0f, 0x6f, 0x05, 0x62, 0xf8, 0xff, 0xff, //0x00000796 movdqu       $-1950(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0x90, 0x90, //0x0000079e .p2align 4, 0x90
	//0x000007a0 LBB0_81
	0xf3, 0x0f, 0x7f, 0x84, 0x01, 0x10, 0xff, 0xff, 0xff, //0x000007a0 movdqu       %xmm0, $-240(%rcx,%rax)
	0xf3, 0x0f, 0x7f, 0x84, 0x01, 0x20, 0xff, 0xff, 0xff, //0x000007a9 movdqu       %xmm0, $-224(%rcx,%rax)
	0xf3, 0x0f, 0x7f, 0x84, 0x01, 0x30, 0xff, 0xff, 0xff, //0x000007b2 movdqu       %xmm0, $-208(%rcx,%rax)
	0xf3, 0x0f, 0x7f, 0x84, 0x01, 0x40, 0xff, 0xff, 0xff, //0x000007bb movdqu       %xmm0, $-192(%rcx,%rax)
	0xf3, 0x0f, 0x7f, 0x84, 0x01, 0x50, 0xff, 0xff, 0xff, //0x000007c4 movdqu       %xmm0, $-176(%rcx,%rax)
	0xf3, 0x0f, 0x7f, 0x84, 0x01, 0x60, 0xff, 0xff, 0xff, //0x000007cd movdqu       %xmm0, $-160(%rcx,%rax)
	0xf3, 0x0f, 0x7f, 0x84, 0x01, 0x70, 0xff, 0xff, 0xff, //0x000007d6 movdqu       %xmm0, $-144(%rcx,%rax)
	0xf3, 0x0f, 0x7f, 0x44, 0x01, 0x80, //0x000007df movdqu       %xmm0, $-128(%rcx,%rax)
	0xf3, 0x0f, 0x7f, 0x44, 0x01, 0x90, //0x000007e5 movdqu       %xmm0, $-112(%rcx,%rax)
	0xf3, 0x0f, 0x7f, 0x44, 0x01, 0xa0, //0x000007eb movdqu       %xmm0, $-96(%rcx,%rax)
	0xf3, 0x0f, 0x7f, 0x44, 0x01, 0xb0, //0x000007f1 movdqu       %xmm0, $-80(%rcx,%rax)
	0xf3, 0x0f, 0x7f, 0x44, 0x01, 0xc0, //0x000007f7 movdqu       %xmm0, $-64(%rcx,%rax)
	0xf3, 0x0f, 0x7f, 0x44, 0x01, 0xd0, //0x000007fd movdqu       %xmm0, $-48(%rcx,%rax)
	0xf3, 0x0f, 0x7f, 0x44, 0x01, 0xe0, //0x00000803 movdqu       %xmm0, $-32(%rcx,%rax)
	0xf3, 0x0f, 0x7f, 0x44, 0x01, 0xf0, //0x00000809 movdqu       %xmm0, $-16(%rcx,%rax)
	0xf3, 0x0f, 0x7f, 0x04, 0x01, //0x0000080f movdqu       %xmm0, (%rcx,%rax)
	0x48, 0x05, 0x00, 0x01, 0x00, 0x00, //0x00000814 addq         $256, %rax
	0x48, 0x83, 0xc3, 0xf8, //0x0000081a addq         $-8, %rbx
	0x0f, 0x85, 0x7c, 0xff, 0xff, 0xff, //0x0000081e jne          LBB0_81
	//0x00000824 LBB0_82
	0x48, 0x85, 0xd2, //0x00000824 testq        %rdx, %rdx
	0x0f, 0x84, 0x39, 0x00, 0x00, 0x00, //0x00000827 je           LBB0_85
	0x4c, 0x01, 0xc8, //0x0000082d addq         %r9, %rax
	0x4c, 0x01, 0xe0, //0x00000830 addq         %r12, %rax
	0x48, 0x8d, 0x44, 0x07, 0x10, //0x00000833 leaq         $16(%rdi,%rax), %rax
	0x48, 0xf7, 0xda, //0x00000838 negq         %rdx
	0xf3, 0x0f, 0x6f, 0x05, 0xbd, 0xf7, 0xff, 0xff, //0x0000083b movdqu       $-2115(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000843 .p2align 4, 0x90
	//0x00000850 LBB0_84
	0xf3, 0x0f, 0x7f, 0x40, 0xf0, //0x00000850 movdqu       %xmm0, $-16(%rax)
	0xf3, 0x0f, 0x7f, 0x00, //0x00000855 movdqu       %xmm0, (%rax)
	0x48, 0x83, 0xc0, 0x20, //0x00000859 addq         $32, %rax
	0x48, 0xff, 0xc2, //0x0000085d incq         %rdx
	0x0f, 0x85, 0xea, 0xff, 0xff, 0xff, //0x00000860 jne          LBB0_84
	//0x00000866 LBB0_85
	0x4d, 0x39, 0xda, //0x00000866 cmpq         %r11, %r10
	0x0f, 0x84, 0x71, 0x04, 0x00, 0x00, //0x00000869 je           LBB0_137
	0x4c, 0x01, 0xd6, //0x0000086f addq         %r10, %rsi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000872 .p2align 4, 0x90
	//0x00000880 LBB0_87
	0xc6, 0x06, 0x30, //0x00000880 movb         $48, (%rsi)
	0x48, 0xff, 0xc6, //0x00000883 incq         %rsi
	0x4c, 0x39, 0xc6, //0x00000886 cmpq         %r8, %rsi
	0x0f, 0x82, 0xf1, 0xff, 0xff, 0xff, //0x00000889 jb           LBB0_87
	0xe9, 0x4c, 0x04, 0x00, 0x00, //0x0000088f jmp          LBB0_137
	//0x00000894 LBB0_88
	0x81, 0xfe, 0x10, 0x27, 0x00, 0x00, //0x00000894 cmpl         $10000, %esi
	0x4c, 0x89, 0xc0, //0x0000089a movq         %r8, %rax
	0x48, 0x83, 0xd8, 0x00, //0x0000089d sbbq         $0, %rax
	0x48, 0x83, 0xc0, 0x05, //0x000008a1 addq         $5, %rax
	0x81, 0xfe, 0x10, 0x27, 0x00, 0x00, //0x000008a5 cmpl         $10000, %esi
	0x0f, 0x83, 0x32, 0xfc, 0xff, 0xff, //0x000008ab jae          LBB0_37
	0x49, 0x89, 0xc1, //0x000008b1 movq         %rax, %r9
	0xe9, 0x4c, 0xfe, 0xff, 0xff, //0x000008b4 jmp          LBB0_56
	//0x000008b9 LBB0_90
	0x49, 0x8d, 0x9c, 0x39, 0xf2, 0x00, 0x00, 0x00, //0x000008b9 leaq         $242(%r9,%rdi), %rbx
	0x4d, 0x89, 0xf7, //0x000008c1 movq         %r14, %r15
	0x49, 0x29, 0xc7, //0x000008c4 subq         %rax, %r15
	0x31, 0xc0, //0x000008c7 xorl         %eax, %eax
	0xf3, 0x0f, 0x6f, 0x05, 0x2f, 0xf7, 0xff, 0xff, //0x000008c9 movdqu       $-2257(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	//0x000008d1 LBB0_91
	0xf3, 0x0f, 0x7f, 0x84, 0x03, 0x10, 0xff, 0xff, 0xff, //0x000008d1 movdqu       %xmm0, $-240(%rbx,%rax)
	0xf3, 0x0f, 0x7f, 0x84, 0x03, 0x20, 0xff, 0xff, 0xff, //0x000008da movdqu       %xmm0, $-224(%rbx,%rax)
	0xf3, 0x0f, 0x7f, 0x84, 0x03, 0x30, 0xff, 0xff, 0xff, //0x000008e3 movdqu       %xmm0, $-208(%rbx,%rax)
	0xf3, 0x0f, 0x7f, 0x84, 0x03, 0x40, 0xff, 0xff, 0xff, //0x000008ec movdqu       %xmm0, $-192(%rbx,%rax)
	0xf3, 0x0f, 0x7f, 0x84, 0x03, 0x50, 0xff, 0xff, 0xff, //0x000008f5 movdqu       %xmm0, $-176(%rbx,%rax)
	0xf3, 0x0f, 0x7f, 0x84, 0x03, 0x60, 0xff, 0xff, 0xff, //0x000008fe movdqu       %xmm0, $-160(%rbx,%rax)
	0xf3, 0x0f, 0x7f, 0x84, 0x03, 0x70, 0xff, 0xff, 0xff, //0x00000907 movdqu       %xmm0, $-144(%rbx,%rax)
	0xf3, 0x0f, 0x7f, 0x44, 0x03, 0x80, //0x00000910 movdqu       %xmm0, $-128(%rbx,%rax)
	0xf3, 0x0f, 0x7f, 0x44, 0x03, 0x90, //0x00000916 movdqu       %xmm0, $-112(%rbx,%rax)
	0xf3, 0x0f, 0x7f, 0x44, 0x03, 0xa0, //0x0000091c movdqu       %xmm0, $-96(%rbx,%rax)
	0xf3, 0x0f, 0x7f, 0x44, 0x03, 0xb0, //0x00000922 movdqu       %xmm0, $-80(%rbx,%rax)
	0xf3, 0x0f, 0x7f, 0x44, 0x03, 0xc0, //0x00000928 movdqu       %xmm0, $-64(%rbx,%rax)
	0xf3, 0x0f, 0x7f, 0x44, 0x03, 0xd0, //0x0000092e movdqu       %xmm0, $-48(%rbx,%rax)
	0xf3, 0x0f, 0x7f, 0x44, 0x03, 0xe0, //0x00000934 movdqu       %xmm0, $-32(%rbx,%rax)
	0xf3, 0x0f, 0x7f, 0x44, 0x03, 0xf0, //0x0000093a movdqu       %xmm0, $-16(%rbx,%rax)
	0xf3, 0x0f, 0x7f, 0x04, 0x03, //0x00000940 movdqu       %xmm0, (%rbx,%rax)
	0x48, 0x05, 0x00, 0x01, 0x00, 0x00, //0x00000945 addq         $256, %rax
	0x49, 0x83, 0xc7, 0x08, //0x0000094b addq         $8, %r15
	0x0f, 0x85, 0x7c, 0xff, 0xff, 0xff, //0x0000094f jne          LBB0_91
	//0x00000955 LBB0_92
	0x4d, 0x85, 0xf6, //0x00000955 testq        %r14, %r14
	0x0f, 0x84, 0x29, 0x00, 0x00, 0x00, //0x00000958 je           LBB0_95
	0x4c, 0x01, 0xc8, //0x0000095e addq         %r9, %rax
	0x48, 0x8d, 0x44, 0x07, 0x12, //0x00000961 leaq         $18(%rdi,%rax), %rax
	0x49, 0xf7, 0xde, //0x00000966 negq         %r14
	0xf3, 0x0f, 0x6f, 0x05, 0x8f, 0xf6, 0xff, 0xff, //0x00000969 movdqu       $-2417(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	//0x00000971 LBB0_94
	0xf3, 0x0f, 0x7f, 0x40, 0xf0, //0x00000971 movdqu       %xmm0, $-16(%rax)
	0xf3, 0x0f, 0x7f, 0x00, //0x00000976 movdqu       %xmm0, (%rax)
	0x48, 0x83, 0xc0, 0x20, //0x0000097a addq         $32, %rax
	0x49, 0xff, 0xc6, //0x0000097e incq         %r14
	0x0f, 0x85, 0xea, 0xff, 0xff, 0xff, //0x00000981 jne          LBB0_94
	//0x00000987 LBB0_95
	0x49, 0x01, 0xf0, //0x00000987 addq         %rsi, %r8
	0x49, 0x39, 0xf3, //0x0000098a cmpq         %rsi, %r11
	0x0f, 0x84, 0x1e, 0x00, 0x00, 0x00, //0x0000098d je           LBB0_98
	//0x00000993 LBB0_96
	0x44, 0x89, 0xd0, //0x00000993 movl         %r10d, %eax
	0xf7, 0xd8, //0x00000996 negl         %eax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000998 .p2align 4, 0x90
	//0x000009a0 LBB0_97
	0x41, 0xc6, 0x00, 0x30, //0x000009a0 movb         $48, (%r8)
	0x49, 0xff, 0xc0, //0x000009a4 incq         %r8
	0xff, 0xc6, //0x000009a7 incl         %esi
	0x39, 0xc6, //0x000009a9 cmpl         %eax, %esi
	0x0f, 0x8c, 0xef, 0xff, 0xff, 0xff, //0x000009ab jl           LBB0_97
	//0x000009b1 LBB0_98
	0x4b, 0x8d, 0x04, 0x20, //0x000009b1 leaq         (%r8,%r12), %rax
	0x41, 0x81, 0xfd, 0x10, 0x27, 0x00, 0x00, //0x000009b5 cmpl         $10000, %r13d
	0x0f, 0x82, 0x63, 0x00, 0x00, 0x00, //0x000009bc jb           LBB0_101
	0x44, 0x89, 0xe9, //0x000009c2 movl         %r13d, %ecx
	0x41, 0xbb, 0x59, 0x17, 0xb7, 0xd1, //0x000009c5 movl         $3518437209, %r11d
	0x4c, 0x0f, 0xaf, 0xd9, //0x000009cb imulq        %rcx, %r11
	0x49, 0xc1, 0xeb, 0x2d, //0x000009cf shrq         $45, %r11
	0x41, 0x69, 0xcb, 0xf0, 0xd8, 0xff, 0xff, //0x000009d3 imull        $-10000, %r11d, %ecx
	0x44, 0x01, 0xe9, //0x000009da addl         %r13d, %ecx
	0x0f, 0x84, 0x87, 0x01, 0x00, 0x00, //0x000009dd je           LBB0_103
	0x89, 0xca, //0x000009e3 movl         %ecx, %edx
	0x48, 0x69, 0xd2, 0x1f, 0x85, 0xeb, 0x51, //0x000009e5 imulq        $1374389535, %rdx, %rdx
	0x48, 0xc1, 0xea, 0x25, //0x000009ec shrq         $37, %rdx
	0x6b, 0xda, 0x64, //0x000009f0 imull        $100, %edx, %ebx
	0x29, 0xd9, //0x000009f3 subl         %ebx, %ecx
	0x48, 0x8d, 0x1d, 0x14, 0x03, 0x00, 0x00, //0x000009f5 leaq         $788(%rip), %rbx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x0c, 0x4b, //0x000009fc movzwl       (%rbx,%rcx,2), %ecx
	0x66, 0x89, 0x48, 0xfe, //0x00000a00 movw         %cx, $-2(%rax)
	0x0f, 0xb7, 0x0c, 0x53, //0x00000a04 movzwl       (%rbx,%rdx,2), %ecx
	0x66, 0x89, 0x48, 0xfc, //0x00000a08 movw         %cx, $-4(%rax)
	0x45, 0x31, 0xc9, //0x00000a0c xorl         %r9d, %r9d
	0x48, 0x83, 0xc0, 0xfc, //0x00000a0f addq         $-4, %rax
	0x41, 0x83, 0xfb, 0x64, //0x00000a13 cmpl         $100, %r11d
	0x0f, 0x83, 0x18, 0x00, 0x00, 0x00, //0x00000a17 jae          LBB0_105
	//0x00000a1d LBB0_102
	0x44, 0x89, 0xd9, //0x00000a1d movl         %r11d, %ecx
	0xe9, 0x4d, 0x00, 0x00, 0x00, //0x00000a20 jmp          LBB0_107
	//0x00000a25 LBB0_101
	0x45, 0x31, 0xc9, //0x00000a25 xorl         %r9d, %r9d
	0x45, 0x89, 0xeb, //0x00000a28 movl         %r13d, %r11d
	0x41, 0x83, 0xfb, 0x64, //0x00000a2b cmpl         $100, %r11d
	0x0f, 0x82, 0xe8, 0xff, 0xff, 0xff, //0x00000a2f jb           LBB0_102
	//0x00000a35 LBB0_105
	0x48, 0xff, 0xc8, //0x00000a35 decq         %rax
	0x48, 0x8d, 0x15, 0xd1, 0x02, 0x00, 0x00, //0x00000a38 leaq         $721(%rip), %rdx  /* _Digits+0(%rip) */
	0x90, //0x00000a3f .p2align 4, 0x90
	//0x00000a40 LBB0_106
	0x44, 0x89, 0xd9, //0x00000a40 movl         %r11d, %ecx
	0x48, 0x69, 0xc9, 0x1f, 0x85, 0xeb, 0x51, //0x00000a43 imulq        $1374389535, %rcx, %rcx
	0x48, 0xc1, 0xe9, 0x25, //0x00000a4a shrq         $37, %rcx
	0x6b, 0xd9, 0x64, //0x00000a4e imull        $100, %ecx, %ebx
	0x44, 0x89, 0xde, //0x00000a51 movl         %r11d, %esi
	0x29, 0xde, //0x00000a54 subl         %ebx, %esi
	0x0f, 0xb7, 0x34, 0x72, //0x00000a56 movzwl       (%rdx,%rsi,2), %esi
	0x66, 0x89, 0x70, 0xff, //0x00000a5a movw         %si, $-1(%rax)
	0x48, 0x83, 0xc0, 0xfe, //0x00000a5e addq         $-2, %rax
	0x41, 0x81, 0xfb, 0x0f, 0x27, 0x00, 0x00, //0x00000a62 cmpl         $9999, %r11d
	0x41, 0x89, 0xcb, //0x00000a69 movl         %ecx, %r11d
	0x0f, 0x87, 0xce, 0xff, 0xff, 0xff, //0x00000a6c ja           LBB0_106
	//0x00000a72 LBB0_107
	0x83, 0xf9, 0x0a, //0x00000a72 cmpl         $10, %ecx
	0x0f, 0x82, 0x16, 0x00, 0x00, 0x00, //0x00000a75 jb           LBB0_109
	0x89, 0xc8, //0x00000a7b movl         %ecx, %eax
	0x48, 0x8d, 0x0d, 0x8c, 0x02, 0x00, 0x00, //0x00000a7d leaq         $652(%rip), %rcx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x41, //0x00000a84 movzwl       (%rcx,%rax,2), %eax
	0x66, 0x41, 0x89, 0x00, //0x00000a88 movw         %ax, (%r8)
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x00000a8c jmp          LBB0_110
	//0x00000a91 LBB0_109
	0x80, 0xc1, 0x30, //0x00000a91 addb         $48, %cl
	0x41, 0x88, 0x08, //0x00000a94 movb         %cl, (%r8)
	//0x00000a97 LBB0_110
	0x4d, 0x29, 0xcc, //0x00000a97 subq         %r9, %r12
	0x49, 0x8d, 0x74, 0x24, 0x01, //0x00000a9a leaq         $1(%r12), %rsi
	0x49, 0x8d, 0x54, 0x24, 0x11, //0x00000a9f leaq         $17(%r12), %rdx
	0x49, 0x8d, 0x44, 0x24, 0x02, //0x00000aa4 leaq         $2(%r12), %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000aa9 .p2align 4, 0x90
	//0x00000ab0 LBB0_111
	0x48, 0xff, 0xca, //0x00000ab0 decq         %rdx
	0x48, 0xff, 0xce, //0x00000ab3 decq         %rsi
	0x48, 0xff, 0xc8, //0x00000ab6 decq         %rax
	0x43, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x00000ab9 cmpb         $48, $-1(%r8,%r12)
	0x4d, 0x8d, 0x64, 0x24, 0xff, //0x00000abf leaq         $-1(%r12), %r12
	0x0f, 0x84, 0xe6, 0xff, 0xff, 0xff, //0x00000ac4 je           LBB0_111
	0x4d, 0x8d, 0x0c, 0x30, //0x00000aca leaq         (%r8,%rsi), %r9
	0x45, 0x85, 0xd2, //0x00000ace testl        %r10d, %r10d
	0x0f, 0x8e, 0x8b, 0x00, 0x00, 0x00, //0x00000ad1 jle          LBB0_116
	0x44, 0x89, 0xc9, //0x00000ad7 movl         %r9d, %ecx
	0x44, 0x29, 0xc1, //0x00000ada subl         %r8d, %ecx
	0x41, 0x39, 0xca, //0x00000add cmpl         %ecx, %r10d
	0x0f, 0x8d, 0x23, 0x00, 0x00, 0x00, //0x00000ae0 jge          LBB0_117
	0x43, 0x8d, 0x0c, 0x02, //0x00000ae6 leal         (%r10,%r8), %ecx
	0x41, 0x29, 0xc9, //0x00000aea subl         %ecx, %r9d
	0x49, 0x8d, 0x49, 0xff, //0x00000aed leaq         $-1(%r9), %rcx
	0x45, 0x89, 0xcb, //0x00000af1 movl         %r9d, %r11d
	0x41, 0x83, 0xe3, 0x03, //0x00000af4 andl         $3, %r11d
	0x48, 0x83, 0xf9, 0x03, //0x00000af8 cmpq         $3, %rcx
	0x0f, 0x83, 0x81, 0x00, 0x00, 0x00, //0x00000afc jae          LBB0_121
	0x31, 0xc9, //0x00000b02 xorl         %ecx, %ecx
	0xe9, 0xa3, 0x00, 0x00, 0x00, //0x00000b04 jmp          LBB0_124
	//0x00000b09 LBB0_117
	0x0f, 0x8e, 0x53, 0x00, 0x00, 0x00, //0x00000b09 jle          LBB0_116
	0x45, 0x01, 0xc2, //0x00000b0f addl         %r8d, %r10d
	0x45, 0x89, 0xcf, //0x00000b12 movl         %r9d, %r15d
	0x41, 0xf7, 0xd7, //0x00000b15 notl         %r15d
	0x45, 0x01, 0xd7, //0x00000b18 addl         %r10d, %r15d
	0x45, 0x31, 0xf6, //0x00000b1b xorl         %r14d, %r14d
	0x4d, 0x89, 0xcb, //0x00000b1e movq         %r9, %r11
	0x41, 0x83, 0xff, 0x1e, //0x00000b21 cmpl         $30, %r15d
	0x0f, 0x86, 0x9b, 0x01, 0x00, 0x00, //0x00000b25 jbe          LBB0_135
	0x49, 0xff, 0xc7, //0x00000b2b incq         %r15
	0x4d, 0x89, 0xfe, //0x00000b2e movq         %r15, %r14
	0x49, 0x83, 0xe6, 0xe0, //0x00000b31 andq         $-32, %r14
	0x4f, 0x8d, 0x1c, 0x30, //0x00000b35 leaq         (%r8,%r14), %r11
	0x49, 0x8d, 0x5e, 0xe0, //0x00000b39 leaq         $-32(%r14), %rbx
	0x48, 0x89, 0xd8, //0x00000b3d movq         %rbx, %rax
	0x48, 0xc1, 0xe8, 0x05, //0x00000b40 shrq         $5, %rax
	0x48, 0xff, 0xc0, //0x00000b44 incq         %rax
	0x41, 0x89, 0xc4, //0x00000b47 movl         %eax, %r12d
	0x41, 0x83, 0xe4, 0x07, //0x00000b4a andl         $7, %r12d
	0x48, 0x81, 0xfb, 0xe0, 0x00, 0x00, 0x00, //0x00000b4e cmpq         $224, %rbx
	0x0f, 0x83, 0x8f, 0x00, 0x00, 0x00, //0x00000b55 jae          LBB0_129
	0x31, 0xc0, //0x00000b5b xorl         %eax, %eax
	0xe9, 0x23, 0x01, 0x00, 0x00, //0x00000b5d jmp          LBB0_131
	//0x00000b62 LBB0_116
	0x4d, 0x89, 0xc8, //0x00000b62 movq         %r9, %r8
	0xe9, 0x76, 0x01, 0x00, 0x00, //0x00000b65 jmp          LBB0_137
	//0x00000b6a LBB0_103
	0x41, 0xb9, 0x04, 0x00, 0x00, 0x00, //0x00000b6a movl         $4, %r9d
	0x48, 0x83, 0xc0, 0xfc, //0x00000b70 addq         $-4, %rax
	0x41, 0x83, 0xfb, 0x64, //0x00000b74 cmpl         $100, %r11d
	0x0f, 0x82, 0x9f, 0xfe, 0xff, 0xff, //0x00000b78 jb           LBB0_102
	0xe9, 0xb2, 0xfe, 0xff, 0xff, //0x00000b7e jmp          LBB0_105
	//0x00000b83 LBB0_121
	0x4d, 0x89, 0xde, //0x00000b83 movq         %r11, %r14
	0x4d, 0x29, 0xce, //0x00000b86 subq         %r9, %r14
	0x31, 0xc9, //0x00000b89 xorl         %ecx, %ecx
	0x90, 0x90, 0x90, 0x90, 0x90, //0x00000b8b .p2align 4, 0x90
	//0x00000b90 LBB0_122
	0x49, 0x8d, 0x1c, 0x08, //0x00000b90 leaq         (%r8,%rcx), %rbx
	0x8b, 0x54, 0x1e, 0xfc, //0x00000b94 movl         $-4(%rsi,%rbx), %edx
	0x89, 0x54, 0x1e, 0xfd, //0x00000b98 movl         %edx, $-3(%rsi,%rbx)
	0x48, 0x83, 0xc1, 0xfc, //0x00000b9c addq         $-4, %rcx
	0x49, 0x39, 0xce, //0x00000ba0 cmpq         %rcx, %r14
	0x0f, 0x85, 0xe7, 0xff, 0xff, 0xff, //0x00000ba3 jne          LBB0_122
	0x48, 0xf7, 0xd9, //0x00000ba9 negq         %rcx
	//0x00000bac LBB0_124
	0x4d, 0x85, 0xdb, //0x00000bac testq        %r11, %r11
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x00000baf je           LBB0_127
	0x49, 0xf7, 0xdb, //0x00000bb5 negq         %r11
	0x4c, 0x89, 0xc2, //0x00000bb8 movq         %r8, %rdx
	0x48, 0x29, 0xca, //0x00000bbb subq         %rcx, %rdx
	0x31, 0xc9, //0x00000bbe xorl         %ecx, %ecx
	//0x00000bc0 .p2align 4, 0x90
	//0x00000bc0 LBB0_126
	0x48, 0x8d, 0x34, 0x0a, //0x00000bc0 leaq         (%rdx,%rcx), %rsi
	0x41, 0x0f, 0xb6, 0x1c, 0x34, //0x00000bc4 movzbl       (%r12,%rsi), %ebx
	0x41, 0x88, 0x5c, 0x34, 0x01, //0x00000bc9 movb         %bl, $1(%r12,%rsi)
	0x48, 0xff, 0xc9, //0x00000bce decq         %rcx
	0x49, 0x39, 0xcb, //0x00000bd1 cmpq         %rcx, %r11
	0x0f, 0x85, 0xe6, 0xff, 0xff, 0xff, //0x00000bd4 jne          LBB0_126
	//0x00000bda LBB0_127
	0x49, 0x63, 0xca, //0x00000bda movslq       %r10d, %rcx
	0x41, 0xc6, 0x04, 0x08, 0x2e, //0x00000bdd movb         $46, (%r8,%rcx)
	0x49, 0x01, 0xc0, //0x00000be2 addq         %rax, %r8
	0xe9, 0xf6, 0x00, 0x00, 0x00, //0x00000be5 jmp          LBB0_137
	//0x00000bea LBB0_129
	0x4c, 0x89, 0xe3, //0x00000bea movq         %r12, %rbx
	0x48, 0x29, 0xc3, //0x00000bed subq         %rax, %rbx
	0x31, 0xc0, //0x00000bf0 xorl         %eax, %eax
	0xf3, 0x0f, 0x6f, 0x05, 0x06, 0xf4, 0xff, 0xff, //0x00000bf2 movdqu       $-3066(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	//0x00000bfa LBB0_130
	0x49, 0x8d, 0x0c, 0x00, //0x00000bfa leaq         (%r8,%rax), %rcx
	0xf3, 0x0f, 0x7f, 0x04, 0x0e, //0x00000bfe movdqu       %xmm0, (%rsi,%rcx)
	0xf3, 0x0f, 0x7f, 0x44, 0x0e, 0x10, //0x00000c03 movdqu       %xmm0, $16(%rsi,%rcx)
	0xf3, 0x0f, 0x7f, 0x44, 0x0e, 0x20, //0x00000c09 movdqu       %xmm0, $32(%rsi,%rcx)
	0xf3, 0x0f, 0x7f, 0x44, 0x0e, 0x30, //0x00000c0f movdqu       %xmm0, $48(%rsi,%rcx)
	0xf3, 0x0f, 0x7f, 0x44, 0x0e, 0x40, //0x00000c15 movdqu       %xmm0, $64(%rsi,%rcx)
	0xf3, 0x0f, 0x7f, 0x44, 0x0e, 0x50, //0x00000c1b movdqu       %xmm0, $80(%rsi,%rcx)
	0xf3, 0x0f, 0x7f, 0x44, 0x0e, 0x60, //0x00000c21 movdqu       %xmm0, $96(%rsi,%rcx)
	0xf3, 0x0f, 0x7f, 0x44, 0x0e, 0x70, //0x00000c27 movdqu       %xmm0, $112(%rsi,%rcx)
	0xf3, 0x0f, 0x7f, 0x84, 0x0e, 0x80, 0x00, 0x00, 0x00, //0x00000c2d movdqu       %xmm0, $128(%rsi,%rcx)
	0xf3, 0x0f, 0x7f, 0x84, 0x0e, 0x90, 0x00, 0x00, 0x00, //0x00000c36 movdqu       %xmm0, $144(%rsi,%rcx)
	0xf3, 0x0f, 0x7f, 0x84, 0x0e, 0xa0, 0x00, 0x00, 0x00, //0x00000c3f movdqu       %xmm0, $160(%rsi,%rcx)
	0xf3, 0x0f, 0x7f, 0x84, 0x0e, 0xb0, 0x00, 0x00, 0x00, //0x00000c48 movdqu       %xmm0, $176(%rsi,%rcx)
	0xf3, 0x0f, 0x7f, 0x84, 0x0e, 0xc0, 0x00, 0x00, 0x00, //0x00000c51 movdqu       %xmm0, $192(%rsi,%rcx)
	0xf3, 0x0f, 0x7f, 0x84, 0x0e, 0xd0, 0x00, 0x00, 0x00, //0x00000c5a movdqu       %xmm0, $208(%rsi,%rcx)
	0xf3, 0x0f, 0x7f, 0x84, 0x0e, 0xe0, 0x00, 0x00, 0x00, //0x00000c63 movdqu       %xmm0, $224(%rsi,%rcx)
	0xf3, 0x0f, 0x7f, 0x84, 0x0e, 0xf0, 0x00, 0x00, 0x00, //0x00000c6c movdqu       %xmm0, $240(%rsi,%rcx)
	0x48, 0x05, 0x00, 0x01, 0x00, 0x00, //0x00000c75 addq         $256, %rax
	0x48, 0x83, 0xc3, 0x08, //0x00000c7b addq         $8, %rbx
	0x0f, 0x85, 0x75, 0xff, 0xff, 0xff, //0x00000c7f jne          LBB0_130
	//0x00000c85 LBB0_131
	0x49, 0x01, 0xf3, //0x00000c85 addq         %rsi, %r11
	0x4d, 0x85, 0xe4, //0x00000c88 testq        %r12, %r12
	0x0f, 0x84, 0x29, 0x00, 0x00, 0x00, //0x00000c8b je           LBB0_134
	0x49, 0x01, 0xc0, //0x00000c91 addq         %rax, %r8
	0x49, 0x01, 0xd0, //0x00000c94 addq         %rdx, %r8
	0x49, 0xf7, 0xdc, //0x00000c97 negq         %r12
	0xf3, 0x0f, 0x6f, 0x05, 0x5e, 0xf3, 0xff, 0xff, //0x00000c9a movdqu       $-3234(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	//0x00000ca2 LBB0_133
	0xf3, 0x41, 0x0f, 0x7f, 0x40, 0xf0, //0x00000ca2 movdqu       %xmm0, $-16(%r8)
	0xf3, 0x41, 0x0f, 0x7f, 0x00, //0x00000ca8 movdqu       %xmm0, (%r8)
	0x49, 0x83, 0xc0, 0x20, //0x00000cad addq         $32, %r8
	0x49, 0xff, 0xc4, //0x00000cb1 incq         %r12
	0x0f, 0x85, 0xe8, 0xff, 0xff, 0xff, //0x00000cb4 jne          LBB0_133
	//0x00000cba LBB0_134
	0x4d, 0x89, 0xd8, //0x00000cba movq         %r11, %r8
	0x4d, 0x39, 0xf7, //0x00000cbd cmpq         %r14, %r15
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00000cc0 je           LBB0_137
	//0x00000cc6 LBB0_135
	0x45, 0x29, 0xf2, //0x00000cc6 subl         %r14d, %r10d
	0x45, 0x29, 0xca, //0x00000cc9 subl         %r9d, %r10d
	0x4d, 0x89, 0xd8, //0x00000ccc movq         %r11, %r8
	0x90, //0x00000ccf .p2align 4, 0x90
	//0x00000cd0 LBB0_136
	0x41, 0xc6, 0x00, 0x30, //0x00000cd0 movb         $48, (%r8)
	0x49, 0xff, 0xc0, //0x00000cd4 incq         %r8
	0x41, 0xff, 0xca, //0x00000cd7 decl         %r10d
	0x0f, 0x85, 0xf0, 0xff, 0xff, 0xff, //0x00000cda jne          LBB0_136
	//0x00000ce0 LBB0_137
	0x41, 0x29, 0xf8, //0x00000ce0 subl         %edi, %r8d
	//0x00000ce3 LBB0_138
	0x44, 0x89, 0xc0, //0x00000ce3 movl         %r8d, %eax
	0x5b, //0x00000ce6 popq         %rbx
	0x41, 0x5c, //0x00000ce7 popq         %r12
	0x41, 0x5d, //0x00000ce9 popq         %r13
	0x41, 0x5e, //0x00000ceb popq         %r14
	0x41, 0x5f, //0x00000ced popq         %r15
	0x5d, //0x00000cef popq         %rbp
	0xc3, //0x00000cf0 retq         
	//0x00000cf1 LBB0_139
	0x45, 0x31, 0xc0, //0x00000cf1 xorl         %r8d, %r8d
	0xe9, 0xea, 0xff, 0xff, 0xff, //0x00000cf4 jmp          LBB0_138
	//0x00000cf9 LBB0_140
	0x41, 0xbf, 0x6b, 0xff, 0xff, 0xff, //0x00000cf9 movl         $-149, %r15d
	0x89, 0xc6, //0x00000cff movl         %eax, %esi
	0xe9, 0x89, 0xf3, 0xff, 0xff, //0x00000d01 jmp          LBB0_5
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000d06 .p2align 4, 0x00
	//0x00000d10 _Digits
	0x30, 0x30, 0x30, 0x31, 0x30, 0x32, 0x30, 0x33, 0x30, 0x34, 0x30, 0x35, 0x30, 0x36, 0x30, 0x37, //0x00000d10 QUAD $0x3330323031303030; QUAD $0x3730363035303430  // .ascii 16, '0001020304050607'
	0x30, 0x38, 0x30, 0x39, 0x31, 0x30, 0x31, 0x31, 0x31, 0x32, 0x31, 0x33, 0x31, 0x34, 0x31, 0x35, //0x00000d20 QUAD $0x3131303139303830; QUAD $0x3531343133313231  // .ascii 16, '0809101112131415'
	0x31, 0x36, 0x31, 0x37, 0x31, 0x38, 0x31, 0x39, 0x32, 0x30, 0x32, 0x31, 0x32, 0x32, 0x32, 0x33, //0x00000d30 QUAD $0x3931383137313631; QUAD $0x3332323231323032  // .ascii 16, '1617181920212223'
	0x32, 0x34, 0x32, 0x35, 0x32, 0x36, 0x32, 0x37, 0x32, 0x38, 0x32, 0x39, 0x33, 0x30, 0x33, 0x31, //0x00000d40 QUAD $0x3732363235323432; QUAD $0x3133303339323832  // .ascii 16, '2425262728293031'
	0x33, 0x32, 0x33, 0x33, 0x33, 0x34, 0x33, 0x35, 0x33, 0x36, 0x33, 0x37, 0x33, 0x38, 0x33, 0x39, //0x00000d50 QUAD $0x3533343333333233; QUAD $0x3933383337333633  // .ascii 16, '3233343536373839'
	0x34, 0x30, 0x34, 0x31, 0x34, 0x32, 0x34, 0x33, 0x34, 0x34, 0x34, 0x35, 0x34, 0x36, 0x34, 0x37, //0x00000d60 QUAD $0x3334323431343034; QUAD $0x3734363435343434  // .ascii 16, '4041424344454647'
	0x34, 0x38, 0x34, 0x39, 0x35, 0x30, 0x35, 0x31, 0x35, 0x32, 0x35, 0x33, 0x35, 0x34, 0x35, 0x35, //0x00000d70 QUAD $0x3135303539343834; QUAD $0x3535343533353235  // .ascii 16, '4849505152535455'
	0x35, 0x36, 0x35, 0x37, 0x35, 0x38, 0x35, 0x39, 0x36, 0x30, 0x36, 0x31, 0x36, 0x32, 0x36, 0x33, //0x00000d80 QUAD $0x3935383537353635; QUAD $0x3336323631363036  // .ascii 16, '5657585960616263'
	0x36, 0x34, 0x36, 0x35, 0x36, 0x36, 0x36, 0x37, 0x36, 0x38, 0x36, 0x39, 0x37, 0x30, 0x37, 0x31, //0x00000d90 QUAD $0x3736363635363436; QUAD $0x3137303739363836  // .ascii 16, '6465666768697071'
	0x37, 0x32, 0x37, 0x33, 0x37, 0x34, 0x37, 0x35, 0x37, 0x36, 0x37, 0x37, 0x37, 0x38, 0x37, 0x39, //0x00000da0 QUAD $0x3537343733373237; QUAD $0x3937383737373637  // .ascii 16, '7273747576777879'
	0x38, 0x30, 0x38, 0x31, 0x38, 0x32, 0x38, 0x33, 0x38, 0x34, 0x38, 0x35, 0x38, 0x36, 0x38, 0x37, //0x00000db0 QUAD $0x3338323831383038; QUAD $0x3738363835383438  // .ascii 16, '8081828384858687'
	0x38, 0x38, 0x38, 0x39, 0x39, 0x30, 0x39, 0x31, 0x39, 0x32, 0x39, 0x33, 0x39, 0x34, 0x39, 0x35, //0x00000dc0 QUAD $0x3139303939383838; QUAD $0x3539343933393239  // .ascii 16, '8889909192939495'
	0x39, 0x36, 0x39, 0x37, 0x39, 0x38, 0x39, 0x39, //0x00000dd0 QUAD $0x3939383937393639  // .ascii 8, '96979899'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000dd8 .p2align 4, 0x00
	//0x00000de0 _pow10_ceil_sig_f32.g
	0xf5, 0xfc, 0x43, 0x4b, 0x2c, 0xb3, 0xce, 0x81, //0x00000de0 .quad -9093133594791772939
	0x32, 0xfc, 0x14, 0x5e, 0xf7, 0x5f, 0x42, 0xa2, //0x00000de8 .quad -6754730975062328270
	0x3f, 0x3b, 0x9a, 0x35, 0xf5, 0xf7, 0xd2, 0xca, //0x00000df0 .quad -3831727700400522433
	0x0e, 0xca, 0x00, 0x83, 0xf2, 0xb5, 0x87, 0xfd, //0x00000df8 .quad -177973607073265138
	0x49, 0x7e, 0xe0, 0x91, 0xb7, 0xd1, 0x74, 0x9e, //0x00000e00 .quad -7028762532061872567
	0xdb, 0x9d, 0x58, 0x76, 0x25, 0x06, 0x12, 0xc6, //0x00000e08 .quad -4174267146649952805
	0x52, 0xc5, 0xee, 0xd3, 0xae, 0x87, 0x96, 0xf7, //0x00000e10 .quad -606147914885053102
	0x53, 0x3b, 0x75, 0x44, 0xcd, 0x14, 0xbe, 0x9a, //0x00000e18 .quad -7296371474444240045
	0x28, 0x8a, 0x92, 0x95, 0x00, 0x9a, 0x6d, 0xc1, //0x00000e20 .quad -4508778324627912152
	0xb2, 0x2c, 0xf7, 0xba, 0x80, 0x00, 0xc9, 0xf1, //0x00000e28 .quad -1024286887357502286
	0xef, 0x7b, 0xda, 0x74, 0x50, 0xa0, 0x1d, 0x97, //0x00000e30 .quad -7557708332239520785
	0xeb, 0x1a, 0x11, 0x92, 0x64, 0x08, 0xe5, 0xbc, //0x00000e38 .quad -4835449396872013077
	0xa6, 0x61, 0x95, 0xb6, 0x7d, 0x4a, 0x1e, 0xec, //0x00000e40 .quad -1432625727662628442
	0x08, 0x5d, 0x1d, 0x92, 0x8e, 0xee, 0x92, 0x93, //0x00000e48 .quad -7812920107430224632
	0x4a, 0xb4, 0xa4, 0x36, 0x32, 0xaa, 0x77, 0xb8, //0x00000e50 .quad -5154464115860392886
	0x5c, 0xe1, 0x4d, 0xc4, 0xbe, 0x94, 0x95, 0xe6, //0x00000e58 .quad -1831394126398103204
	0xda, 0xac, 0xb0, 0x3a, 0xf7, 0x7c, 0x1d, 0x90, //0x00000e60 .quad -8062150356639896358
	0x10, 0xd8, 0x5c, 0x09, 0x35, 0xdc, 0x24, 0xb4, //0x00000e68 .quad -5466001927372482544
	0x14, 0x0e, 0xb4, 0x4b, 0x42, 0x13, 0x2e, 0xe1, //0x00000e70 .quad -2220816390788215276
	0xcc, 0x88, 0x50, 0x6f, 0x09, 0xcc, 0xbc, 0x8c, //0x00000e78 .quad -8305539271883716404
	0xff, 0xaa, 0x24, 0xcb, 0x0b, 0xff, 0xeb, 0xaf, //0x00000e80 .quad -5770238071427257601
	0xbf, 0xd5, 0xed, 0xbd, 0xce, 0xfe, 0xe6, 0xdb, //0x00000e88 .quad -2601111570856684097
	0x98, 0xa5, 0xb4, 0x36, 0x41, 0x5f, 0x70, 0x89, //0x00000e90 .quad -8543223759426509416
	0xfd, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x00000e98 .quad -6067343680855748867
	0xbd, 0x42, 0x7a, 0xe5, 0xd5, 0x94, 0xbf, 0xd6, //0x00000ea0 .quad -2972493582642298179
	0xb6, 0x69, 0x6c, 0xaf, 0x05, 0xbd, 0x37, 0x86, //0x00000ea8 .quad -8775337516792518218
	0x24, 0x84, 0x47, 0x1b, 0x47, 0xac, 0xc5, 0xa7, //0x00000eb0 .quad -6357485877563259868
	0x2c, 0x65, 0x19, 0xe2, 0x58, 0x17, 0xb7, 0xd1, //0x00000eb8 .quad -3335171328526686932
	0x3c, 0xdf, 0x4f, 0x8d, 0x97, 0x6e, 0x12, 0x83, //0x00000ec0 .quad -9002011107970261188
	0x0b, 0xd7, 0xa3, 0x70, 0x3d, 0x0a, 0xd7, 0xa3, //0x00000ec8 .quad -6640827866535438581
	0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00000ed0 .quad -3689348814741910323
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00000ed8 .quad -9223372036854775808
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa0, //0x00000ee0 .quad -6917529027641081856
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc8, //0x00000ee8 .quad -4035225266123964416
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfa, //0x00000ef0 .quad -432345564227567616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x9c, //0x00000ef8 .quad -7187745005283311616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xc3, //0x00000f00 .quad -4372995238176751616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0xf4, //0x00000f08 .quad -854558029293551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x96, 0x98, //0x00000f10 .quad -7451627795949551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xbc, 0xbe, //0x00000f18 .quad -4702848726509551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x6b, 0xee, //0x00000f20 .quad -1266874889709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0xf9, 0x02, 0x95, //0x00000f28 .quad -7709325833709551616
	0x00, 0x00, 0x00, 0x00, 0x40, 0xb7, 0x43, 0xba, //0x00000f30 .quad -5024971273709551616
	0x00, 0x00, 0x00, 0x00, 0x10, 0xa5, 0xd4, 0xe8, //0x00000f38 .quad -1669528073709551616
	0x00, 0x00, 0x00, 0x00, 0x2a, 0xe7, 0x84, 0x91, //0x00000f40 .quad -7960984073709551616
	0x00, 0x00, 0x00, 0x80, 0xf4, 0x20, 0xe6, 0xb5, //0x00000f48 .quad -5339544073709551616
	0x00, 0x00, 0x00, 0xa0, 0x31, 0xa9, 0x5f, 0xe3, //0x00000f50 .quad -2062744073709551616
	0x00, 0x00, 0x00, 0x04, 0xbf, 0xc9, 0x1b, 0x8e, //0x00000f58 .quad -8206744073709551616
	0x00, 0x00, 0x00, 0xc5, 0x2e, 0xbc, 0xa2, 0xb1, //0x00000f60 .quad -5646744073709551616
	0x00, 0x00, 0x40, 0x76, 0x3a, 0x6b, 0x0b, 0xde, //0x00000f68 .quad -2446744073709551616
	0x00, 0x00, 0xe8, 0x89, 0x04, 0x23, 0xc7, 0x8a, //0x00000f70 .quad -8446744073709551616
	0x00, 0x00, 0x62, 0xac, 0xc5, 0xeb, 0x78, 0xad, //0x00000f78 .quad -5946744073709551616
	0x00, 0x80, 0x7a, 0x17, 0xb7, 0x26, 0xd7, 0xd8, //0x00000f80 .quad -2821744073709551616
	0x00, 0x90, 0xac, 0x6e, 0x32, 0x78, 0x86, 0x87, //0x00000f88 .quad -8681119073709551616
	0x00, 0xb4, 0x57, 0x0a, 0x3f, 0x16, 0x68, 0xa9, //0x00000f90 .quad -6239712823709551616
	0x00, 0xa1, 0xed, 0xcc, 0xce, 0x1b, 0xc2, 0xd3, //0x00000f98 .quad -3187955011209551616
	0xa0, 0x84, 0x14, 0x40, 0x61, 0x51, 0x59, 0x84, //0x00000fa0 .quad -8910000909647051616
	0xc8, 0xa5, 0x19, 0x90, 0xb9, 0xa5, 0x6f, 0xa5, //0x00000fa8 .quad -6525815118631426616
	0x3a, 0x0f, 0x20, 0xf4, 0x27, 0x8f, 0xcb, 0xce, //0x00000fb0 .quad -3545582879861895366
	0x85, 0x09, 0x94, 0xf8, 0x78, 0x39, 0x3f, 0x81, //0x00000fb8 .quad -9133518327554766459
	0xe6, 0x0b, 0xb9, 0x36, 0xd7, 0x07, 0x8f, 0xa1, //0x00000fc0 .quad -6805211891016070170
	0xdf, 0x4e, 0x67, 0x04, 0xcd, 0xc9, 0xf2, 0xc9, //0x00000fc8 .quad -3894828845342699809
	0x97, 0x22, 0x81, 0x45, 0x40, 0x7c, 0x6f, 0xfc, //0x00000fd0 .quad -256850038250986857
	0x9e, 0xb5, 0x70, 0x2b, 0xa8, 0xad, 0xc5, 0x9d, //0x00000fd8 .quad -7078060301547948642
	0x06, 0xe3, 0x4c, 0x36, 0x12, 0x19, 0x37, 0xc5, //0x00000fe0 .quad -4235889358507547898
	0xc7, 0x1b, 0xe0, 0xc3, 0x56, 0xdf, 0x84, 0xf6, //0x00000fe8 .quad -683175679707046969
	0x5d, 0x11, 0x6c, 0x3a, 0x96, 0x0b, 0x13, 0x9a, //0x00000ff0 .quad -7344513827457986211
	0xb4, 0x15, 0x07, 0xc9, 0x7b, 0xce, 0x97, 0xc0, //0x00000ff8 .quad -4568956265895094860
	0x21, 0xdb, 0x48, 0xbb, 0x1a, 0xc2, 0xbd, 0xf0, //0x00001000 .quad -1099509313941480671
	0xf5, 0x88, 0x0d, 0xb5, 0x50, 0x99, 0x76, 0x96, //0x00001008 .quad -7604722348854507275
	0x32, 0xeb, 0x50, 0xe2, 0xa4, 0x3f, 0x14, 0xbc, //0x00001010 .quad -4894216917640746190
	0xfe, 0x25, 0xe5, 0x1a, 0x8e, 0x4f, 0x19, 0xeb, //0x00001018 .quad -1506085128623544834
	0xbf, 0x37, 0xcf, 0xd0, 0xb8, 0xd1, 0xef, 0x92, //0x00001020 .quad -7858832233030797377
	0xae, 0x05, 0x03, 0x05, 0x27, 0xc6, 0xab, 0xb7, //0x00001028 .quad -5211854272861108818
	0x1a, 0xc7, 0x43, 0xc6, 0xb0, 0xb7, 0x96, 0xe5, //0x00001030 .quad -1903131822648998118
	0x70, 0x5c, 0xea, 0x7b, 0xce, 0x32, 0x7e, 0x8f, //0x00001038 .quad -8106986416796705680
	0x8c, 0xf3, 0xe4, 0x1a, 0x82, 0xbf, 0x5d, 0xb3, //0x00001040 .quad -5522047002568494196
}
 
