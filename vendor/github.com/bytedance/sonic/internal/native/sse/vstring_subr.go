// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__vstring = 48
)

const (
    _stack__vstring = 88
)

const (
    _size__vstring = 2436
)

var (
    _pcsp__vstring = [][2]uint32{
        {1, 0},
        {4, 8},
        {6, 16},
        {8, 24},
        {10, 32},
        {12, 40},
        {13, 48},
        {2280, 88},
        {2284, 48},
        {2285, 40},
        {2287, 32},
        {2289, 24},
        {2291, 16},
        {2293, 8},
        {2294, 0},
        {2436, 88},
    }
)

var _cfunc_vstring = []loader.CFunc{
    {"_vstring_entry", 0,  _entry__vstring, 0, nil},
    {"_vstring", _entry__vstring, _size__vstring, _stack__vstring, _pcsp__vstring},
}
