// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__f64toa = 16
)

const (
    _stack__f64toa = 56
)

const (
    _size__f64toa = 4672
)

var (
    _pcsp__f64toa = [][2]uint32{
        {1, 0},
        {4, 8},
        {6, 16},
        {8, 24},
        {10, 32},
        {12, 40},
        {13, 48},
        {4582, 56},
        {4586, 48},
        {4587, 40},
        {4589, 32},
        {4591, 24},
        {4593, 16},
        {4595, 8},
        {4596, 0},
        {4659, 56},
    }
)

var _cfunc_f64toa = []loader.CFunc{
    {"_f64toa_entry", 0,  _entry__f64toa, 0, nil},
    {"_f64toa", _entry__f64toa, _size__f64toa, _stack__f64toa, _pcsp__f64toa},
}
