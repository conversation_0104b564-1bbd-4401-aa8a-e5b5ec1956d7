// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package sse

var _text_i64toa = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x59, 0x17, 0xb7, 0xd1, 0x00, 0x00, 0x00, 0x00, // .quad 3518437209
	0x59, 0x17, 0xb7, 0xd1, 0x00, 0x00, 0x00, 0x00, //0x00000008 .quad 3518437209
	//0x00000010 LCPI0_1
	0xc5, 0x20, //0x00000010 .word 8389
	0x7b, 0x14, //0x00000012 .word 5243
	0x34, 0x33, //0x00000014 .word 13108
	0x00, 0x80, //0x00000016 .word 32768
	0xc5, 0x20, //0x00000018 .word 8389
	0x7b, 0x14, //0x0000001a .word 5243
	0x34, 0x33, //0x0000001c .word 13108
	0x00, 0x80, //0x0000001e .word 32768
	//0x00000020 LCPI0_2
	0x80, 0x00, //0x00000020 .word 128
	0x00, 0x08, //0x00000022 .word 2048
	0x00, 0x20, //0x00000024 .word 8192
	0x00, 0x80, //0x00000026 .word 32768
	0x80, 0x00, //0x00000028 .word 128
	0x00, 0x08, //0x0000002a .word 2048
	0x00, 0x20, //0x0000002c .word 8192
	0x00, 0x80, //0x0000002e .word 32768
	//0x00000030 LCPI0_3
	0x0a, 0x00, //0x00000030 .word 10
	0x0a, 0x00, //0x00000032 .word 10
	0x0a, 0x00, //0x00000034 .word 10
	0x0a, 0x00, //0x00000036 .word 10
	0x0a, 0x00, //0x00000038 .word 10
	0x0a, 0x00, //0x0000003a .word 10
	0x0a, 0x00, //0x0000003c .word 10
	0x0a, 0x00, //0x0000003e .word 10
	//0x00000040 LCPI0_4
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, //0x00000040 QUAD $0x3030303030303030; QUAD $0x3030303030303030  // .space 16, '0000000000000000'
	//0x00000050 .p2align 4, 0x90
	//0x00000050 _i64toa
	0x55, //0x00000050 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000051 movq         %rsp, %rbp
	0x48, 0x85, 0xf6, //0x00000054 testq        %rsi, %rsi
	0x0f, 0x88, 0xaf, 0x00, 0x00, 0x00, //0x00000057 js           LBB0_25
	0x48, 0x81, 0xfe, 0x0f, 0x27, 0x00, 0x00, //0x0000005d cmpq         $9999, %rsi
	0x0f, 0x87, 0xf8, 0x00, 0x00, 0x00, //0x00000064 ja           LBB0_9
	0x0f, 0xb7, 0xc6, //0x0000006a movzwl       %si, %eax
	0xc1, 0xe8, 0x02, //0x0000006d shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x00000070 imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x00000076 shrl         $17, %eax
	0x48, 0x8d, 0x14, 0x00, //0x00000079 leaq         (%rax,%rax), %rdx
	0x6b, 0xc0, 0x64, //0x0000007d imull        $100, %eax, %eax
	0x89, 0xf1, //0x00000080 movl         %esi, %ecx
	0x29, 0xc1, //0x00000082 subl         %eax, %ecx
	0x0f, 0xb7, 0xc1, //0x00000084 movzwl       %cx, %eax
	0x48, 0x01, 0xc0, //0x00000087 addq         %rax, %rax
	0x81, 0xfe, 0xe8, 0x03, 0x00, 0x00, //0x0000008a cmpl         $1000, %esi
	0x0f, 0x82, 0x16, 0x00, 0x00, 0x00, //0x00000090 jb           LBB0_4
	0x48, 0x8d, 0x0d, 0xd3, 0x08, 0x00, 0x00, //0x00000096 leaq         $2259(%rip), %rcx  /* _Digits+0(%rip) */
	0x8a, 0x0c, 0x0a, //0x0000009d movb         (%rdx,%rcx), %cl
	0x88, 0x0f, //0x000000a0 movb         %cl, (%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x000000a2 movl         $1, %ecx
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x000000a7 jmp          LBB0_5
	//0x000000ac LBB0_4
	0x31, 0xc9, //0x000000ac xorl         %ecx, %ecx
	0x83, 0xfe, 0x64, //0x000000ae cmpl         $100, %esi
	0x0f, 0x82, 0x45, 0x00, 0x00, 0x00, //0x000000b1 jb           LBB0_6
	//0x000000b7 LBB0_5
	0x0f, 0xb7, 0xd2, //0x000000b7 movzwl       %dx, %edx
	0x48, 0x83, 0xca, 0x01, //0x000000ba orq          $1, %rdx
	0x48, 0x8d, 0x35, 0xab, 0x08, 0x00, 0x00, //0x000000be leaq         $2219(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x32, //0x000000c5 movb         (%rdx,%rsi), %dl
	0x89, 0xce, //0x000000c8 movl         %ecx, %esi
	0xff, 0xc1, //0x000000ca incl         %ecx
	0x88, 0x14, 0x37, //0x000000cc movb         %dl, (%rdi,%rsi)
	//0x000000cf LBB0_7
	0x48, 0x8d, 0x15, 0x9a, 0x08, 0x00, 0x00, //0x000000cf leaq         $2202(%rip), %rdx  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x10, //0x000000d6 movb         (%rax,%rdx), %dl
	0x89, 0xce, //0x000000d9 movl         %ecx, %esi
	0xff, 0xc1, //0x000000db incl         %ecx
	0x88, 0x14, 0x37, //0x000000dd movb         %dl, (%rdi,%rsi)
	//0x000000e0 LBB0_8
	0x0f, 0xb7, 0xc0, //0x000000e0 movzwl       %ax, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000000e3 orq          $1, %rax
	0x48, 0x8d, 0x15, 0x82, 0x08, 0x00, 0x00, //0x000000e7 leaq         $2178(%rip), %rdx  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x10, //0x000000ee movb         (%rax,%rdx), %al
	0x89, 0xca, //0x000000f1 movl         %ecx, %edx
	0xff, 0xc1, //0x000000f3 incl         %ecx
	0x88, 0x04, 0x17, //0x000000f5 movb         %al, (%rdi,%rdx)
	0x89, 0xc8, //0x000000f8 movl         %ecx, %eax
	0x5d, //0x000000fa popq         %rbp
	0xc3, //0x000000fb retq         
	//0x000000fc LBB0_6
	0x31, 0xc9, //0x000000fc xorl         %ecx, %ecx
	0x83, 0xfe, 0x0a, //0x000000fe cmpl         $10, %esi
	0x0f, 0x83, 0xc8, 0xff, 0xff, 0xff, //0x00000101 jae          LBB0_7
	0xe9, 0xd4, 0xff, 0xff, 0xff, //0x00000107 jmp          LBB0_8
	//0x0000010c LBB0_25
	0xc6, 0x07, 0x2d, //0x0000010c movb         $45, (%rdi)
	0x48, 0xf7, 0xde, //0x0000010f negq         %rsi
	0x48, 0x81, 0xfe, 0x0f, 0x27, 0x00, 0x00, //0x00000112 cmpq         $9999, %rsi
	0x0f, 0x87, 0xd3, 0x01, 0x00, 0x00, //0x00000119 ja           LBB0_33
	0x0f, 0xb7, 0xc6, //0x0000011f movzwl       %si, %eax
	0xc1, 0xe8, 0x02, //0x00000122 shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x00000125 imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x0000012b shrl         $17, %eax
	0x48, 0x8d, 0x14, 0x00, //0x0000012e leaq         (%rax,%rax), %rdx
	0x6b, 0xc0, 0x64, //0x00000132 imull        $100, %eax, %eax
	0x89, 0xf1, //0x00000135 movl         %esi, %ecx
	0x29, 0xc1, //0x00000137 subl         %eax, %ecx
	0x0f, 0xb7, 0xc1, //0x00000139 movzwl       %cx, %eax
	0x48, 0x01, 0xc0, //0x0000013c addq         %rax, %rax
	0x81, 0xfe, 0xe8, 0x03, 0x00, 0x00, //0x0000013f cmpl         $1000, %esi
	0x0f, 0x82, 0xab, 0x00, 0x00, 0x00, //0x00000145 jb           LBB0_28
	0x48, 0x8d, 0x0d, 0x1e, 0x08, 0x00, 0x00, //0x0000014b leaq         $2078(%rip), %rcx  /* _Digits+0(%rip) */
	0x8a, 0x0c, 0x0a, //0x00000152 movb         (%rdx,%rcx), %cl
	0x88, 0x4f, 0x01, //0x00000155 movb         %cl, $1(%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x00000158 movl         $1, %ecx
	0xe9, 0x9f, 0x00, 0x00, 0x00, //0x0000015d jmp          LBB0_29
	//0x00000162 LBB0_9
	0x48, 0x81, 0xfe, 0xff, 0xe0, 0xf5, 0x05, //0x00000162 cmpq         $99999999, %rsi
	0x0f, 0x87, 0x18, 0x02, 0x00, 0x00, //0x00000169 ja           LBB0_17
	0x89, 0xf0, //0x0000016f movl         %esi, %eax
	0xba, 0x59, 0x17, 0xb7, 0xd1, //0x00000171 movl         $3518437209, %edx
	0x48, 0x0f, 0xaf, 0xd0, //0x00000176 imulq        %rax, %rdx
	0x48, 0xc1, 0xea, 0x2d, //0x0000017a shrq         $45, %rdx
	0x44, 0x69, 0xc2, 0x10, 0x27, 0x00, 0x00, //0x0000017e imull        $10000, %edx, %r8d
	0x89, 0xf1, //0x00000185 movl         %esi, %ecx
	0x44, 0x29, 0xc1, //0x00000187 subl         %r8d, %ecx
	0x4c, 0x69, 0xd0, 0x83, 0xde, 0x1b, 0x43, //0x0000018a imulq        $1125899907, %rax, %r10
	0x49, 0xc1, 0xea, 0x31, //0x00000191 shrq         $49, %r10
	0x41, 0x83, 0xe2, 0xfe, //0x00000195 andl         $-2, %r10d
	0x0f, 0xb7, 0xc2, //0x00000199 movzwl       %dx, %eax
	0xc1, 0xe8, 0x02, //0x0000019c shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x0000019f imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x000001a5 shrl         $17, %eax
	0x6b, 0xc0, 0x64, //0x000001a8 imull        $100, %eax, %eax
	0x29, 0xc2, //0x000001ab subl         %eax, %edx
	0x44, 0x0f, 0xb7, 0xca, //0x000001ad movzwl       %dx, %r9d
	0x4d, 0x01, 0xc9, //0x000001b1 addq         %r9, %r9
	0x0f, 0xb7, 0xc1, //0x000001b4 movzwl       %cx, %eax
	0xc1, 0xe8, 0x02, //0x000001b7 shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x000001ba imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x000001c0 shrl         $17, %eax
	0x4c, 0x8d, 0x04, 0x00, //0x000001c3 leaq         (%rax,%rax), %r8
	0x6b, 0xc0, 0x64, //0x000001c7 imull        $100, %eax, %eax
	0x29, 0xc1, //0x000001ca subl         %eax, %ecx
	0x44, 0x0f, 0xb7, 0xd9, //0x000001cc movzwl       %cx, %r11d
	0x4d, 0x01, 0xdb, //0x000001d0 addq         %r11, %r11
	0x81, 0xfe, 0x80, 0x96, 0x98, 0x00, //0x000001d3 cmpl         $10000000, %esi
	0x0f, 0x82, 0x6c, 0x00, 0x00, 0x00, //0x000001d9 jb           LBB0_12
	0x48, 0x8d, 0x05, 0x8a, 0x07, 0x00, 0x00, //0x000001df leaq         $1930(%rip), %rax  /* _Digits+0(%rip) */
	0x41, 0x8a, 0x04, 0x02, //0x000001e6 movb         (%r10,%rax), %al
	0x88, 0x07, //0x000001ea movb         %al, (%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x000001ec movl         $1, %ecx
	0xe9, 0x63, 0x00, 0x00, 0x00, //0x000001f1 jmp          LBB0_13
	//0x000001f6 LBB0_28
	0x31, 0xc9, //0x000001f6 xorl         %ecx, %ecx
	0x83, 0xfe, 0x64, //0x000001f8 cmpl         $100, %esi
	0x0f, 0x82, 0xce, 0x00, 0x00, 0x00, //0x000001fb jb           LBB0_30
	//0x00000201 LBB0_29
	0x0f, 0xb7, 0xd2, //0x00000201 movzwl       %dx, %edx
	0x48, 0x83, 0xca, 0x01, //0x00000204 orq          $1, %rdx
	0x48, 0x8d, 0x35, 0x61, 0x07, 0x00, 0x00, //0x00000208 leaq         $1889(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x32, //0x0000020f movb         (%rdx,%rsi), %dl
	0x89, 0xce, //0x00000212 movl         %ecx, %esi
	0xff, 0xc1, //0x00000214 incl         %ecx
	0x88, 0x54, 0x37, 0x01, //0x00000216 movb         %dl, $1(%rdi,%rsi)
	//0x0000021a LBB0_31
	0x48, 0x8d, 0x15, 0x4f, 0x07, 0x00, 0x00, //0x0000021a leaq         $1871(%rip), %rdx  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x10, //0x00000221 movb         (%rax,%rdx), %dl
	0x89, 0xce, //0x00000224 movl         %ecx, %esi
	0xff, 0xc1, //0x00000226 incl         %ecx
	0x88, 0x54, 0x37, 0x01, //0x00000228 movb         %dl, $1(%rdi,%rsi)
	//0x0000022c LBB0_32
	0x0f, 0xb7, 0xc0, //0x0000022c movzwl       %ax, %eax
	0x48, 0x83, 0xc8, 0x01, //0x0000022f orq          $1, %rax
	0x48, 0x8d, 0x15, 0x36, 0x07, 0x00, 0x00, //0x00000233 leaq         $1846(%rip), %rdx  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x10, //0x0000023a movb         (%rax,%rdx), %al
	0x89, 0xca, //0x0000023d movl         %ecx, %edx
	0xff, 0xc1, //0x0000023f incl         %ecx
	0x88, 0x44, 0x17, 0x01, //0x00000241 movb         %al, $1(%rdi,%rdx)
	0xff, 0xc1, //0x00000245 incl         %ecx
	0x89, 0xc8, //0x00000247 movl         %ecx, %eax
	0x5d, //0x00000249 popq         %rbp
	0xc3, //0x0000024a retq         
	//0x0000024b LBB0_12
	0x31, 0xc9, //0x0000024b xorl         %ecx, %ecx
	0x81, 0xfe, 0x40, 0x42, 0x0f, 0x00, //0x0000024d cmpl         $1000000, %esi
	0x0f, 0x82, 0x86, 0x00, 0x00, 0x00, //0x00000253 jb           LBB0_14
	//0x00000259 LBB0_13
	0x44, 0x89, 0xd0, //0x00000259 movl         %r10d, %eax
	0x48, 0x83, 0xc8, 0x01, //0x0000025c orq          $1, %rax
	0x48, 0x8d, 0x35, 0x09, 0x07, 0x00, 0x00, //0x00000260 leaq         $1801(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x30, //0x00000267 movb         (%rax,%rsi), %al
	0x89, 0xce, //0x0000026a movl         %ecx, %esi
	0xff, 0xc1, //0x0000026c incl         %ecx
	0x88, 0x04, 0x37, //0x0000026e movb         %al, (%rdi,%rsi)
	//0x00000271 LBB0_15
	0x48, 0x8d, 0x05, 0xf8, 0x06, 0x00, 0x00, //0x00000271 leaq         $1784(%rip), %rax  /* _Digits+0(%rip) */
	0x41, 0x8a, 0x04, 0x01, //0x00000278 movb         (%r9,%rax), %al
	0x89, 0xce, //0x0000027c movl         %ecx, %esi
	0xff, 0xc1, //0x0000027e incl         %ecx
	0x88, 0x04, 0x37, //0x00000280 movb         %al, (%rdi,%rsi)
	//0x00000283 LBB0_16
	0x41, 0x0f, 0xb7, 0xc1, //0x00000283 movzwl       %r9w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x00000287 orq          $1, %rax
	0x48, 0x8d, 0x35, 0xde, 0x06, 0x00, 0x00, //0x0000028b leaq         $1758(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x30, //0x00000292 movb         (%rax,%rsi), %al
	0x89, 0xca, //0x00000295 movl         %ecx, %edx
	0x88, 0x04, 0x3a, //0x00000297 movb         %al, (%rdx,%rdi)
	0x41, 0x8a, 0x04, 0x30, //0x0000029a movb         (%r8,%rsi), %al
	0x88, 0x44, 0x3a, 0x01, //0x0000029e movb         %al, $1(%rdx,%rdi)
	0x41, 0x0f, 0xb7, 0xc0, //0x000002a2 movzwl       %r8w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000002a6 orq          $1, %rax
	0x8a, 0x04, 0x30, //0x000002aa movb         (%rax,%rsi), %al
	0x88, 0x44, 0x3a, 0x02, //0x000002ad movb         %al, $2(%rdx,%rdi)
	0x41, 0x8a, 0x04, 0x33, //0x000002b1 movb         (%r11,%rsi), %al
	0x88, 0x44, 0x3a, 0x03, //0x000002b5 movb         %al, $3(%rdx,%rdi)
	0x41, 0x0f, 0xb7, 0xc3, //0x000002b9 movzwl       %r11w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000002bd orq          $1, %rax
	0x8a, 0x04, 0x30, //0x000002c1 movb         (%rax,%rsi), %al
	0x83, 0xc1, 0x05, //0x000002c4 addl         $5, %ecx
	0x88, 0x44, 0x3a, 0x04, //0x000002c7 movb         %al, $4(%rdx,%rdi)
	0x89, 0xc8, //0x000002cb movl         %ecx, %eax
	0x5d, //0x000002cd popq         %rbp
	0xc3, //0x000002ce retq         
	//0x000002cf LBB0_30
	0x31, 0xc9, //0x000002cf xorl         %ecx, %ecx
	0x83, 0xfe, 0x0a, //0x000002d1 cmpl         $10, %esi
	0x0f, 0x83, 0x40, 0xff, 0xff, 0xff, //0x000002d4 jae          LBB0_31
	0xe9, 0x4d, 0xff, 0xff, 0xff, //0x000002da jmp          LBB0_32
	//0x000002df LBB0_14
	0x31, 0xc9, //0x000002df xorl         %ecx, %ecx
	0x81, 0xfe, 0xa0, 0x86, 0x01, 0x00, //0x000002e1 cmpl         $100000, %esi
	0x0f, 0x83, 0x84, 0xff, 0xff, 0xff, //0x000002e7 jae          LBB0_15
	0xe9, 0x91, 0xff, 0xff, 0xff, //0x000002ed jmp          LBB0_16
	//0x000002f2 LBB0_33
	0x48, 0x81, 0xfe, 0xff, 0xe0, 0xf5, 0x05, //0x000002f2 cmpq         $99999999, %rsi
	0x0f, 0x87, 0x4c, 0x02, 0x00, 0x00, //0x000002f9 ja           LBB0_41
	0x89, 0xf0, //0x000002ff movl         %esi, %eax
	0xba, 0x59, 0x17, 0xb7, 0xd1, //0x00000301 movl         $3518437209, %edx
	0x48, 0x0f, 0xaf, 0xd0, //0x00000306 imulq        %rax, %rdx
	0x48, 0xc1, 0xea, 0x2d, //0x0000030a shrq         $45, %rdx
	0x44, 0x69, 0xc2, 0x10, 0x27, 0x00, 0x00, //0x0000030e imull        $10000, %edx, %r8d
	0x89, 0xf1, //0x00000315 movl         %esi, %ecx
	0x44, 0x29, 0xc1, //0x00000317 subl         %r8d, %ecx
	0x4c, 0x69, 0xd0, 0x83, 0xde, 0x1b, 0x43, //0x0000031a imulq        $1125899907, %rax, %r10
	0x49, 0xc1, 0xea, 0x31, //0x00000321 shrq         $49, %r10
	0x41, 0x83, 0xe2, 0xfe, //0x00000325 andl         $-2, %r10d
	0x0f, 0xb7, 0xc2, //0x00000329 movzwl       %dx, %eax
	0xc1, 0xe8, 0x02, //0x0000032c shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x0000032f imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x00000335 shrl         $17, %eax
	0x6b, 0xc0, 0x64, //0x00000338 imull        $100, %eax, %eax
	0x29, 0xc2, //0x0000033b subl         %eax, %edx
	0x44, 0x0f, 0xb7, 0xca, //0x0000033d movzwl       %dx, %r9d
	0x4d, 0x01, 0xc9, //0x00000341 addq         %r9, %r9
	0x0f, 0xb7, 0xc1, //0x00000344 movzwl       %cx, %eax
	0xc1, 0xe8, 0x02, //0x00000347 shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x0000034a imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x00000350 shrl         $17, %eax
	0x4c, 0x8d, 0x04, 0x00, //0x00000353 leaq         (%rax,%rax), %r8
	0x6b, 0xc0, 0x64, //0x00000357 imull        $100, %eax, %eax
	0x29, 0xc1, //0x0000035a subl         %eax, %ecx
	0x44, 0x0f, 0xb7, 0xd9, //0x0000035c movzwl       %cx, %r11d
	0x4d, 0x01, 0xdb, //0x00000360 addq         %r11, %r11
	0x81, 0xfe, 0x80, 0x96, 0x98, 0x00, //0x00000363 cmpl         $10000000, %esi
	0x0f, 0x82, 0x40, 0x01, 0x00, 0x00, //0x00000369 jb           LBB0_36
	0x48, 0x8d, 0x05, 0xfa, 0x05, 0x00, 0x00, //0x0000036f leaq         $1530(%rip), %rax  /* _Digits+0(%rip) */
	0x41, 0x8a, 0x04, 0x02, //0x00000376 movb         (%r10,%rax), %al
	0x88, 0x47, 0x01, //0x0000037a movb         %al, $1(%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x0000037d movl         $1, %ecx
	0xe9, 0x36, 0x01, 0x00, 0x00, //0x00000382 jmp          LBB0_37
	//0x00000387 LBB0_17
	0x48, 0xb9, 0x00, 0x00, 0xc1, 0x6f, 0xf2, 0x86, 0x23, 0x00, //0x00000387 movabsq      $10000000000000000, %rcx
	0x48, 0x39, 0xce, //0x00000391 cmpq         %rcx, %rsi
	0x0f, 0x83, 0xdc, 0x02, 0x00, 0x00, //0x00000394 jae          LBB0_19
	0x48, 0xb9, 0xfd, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x0000039a movabsq      $-6067343680855748867, %rcx
	0x48, 0x89, 0xf0, //0x000003a4 movq         %rsi, %rax
	0x48, 0xf7, 0xe1, //0x000003a7 mulq         %rcx
	0x48, 0xc1, 0xea, 0x1a, //0x000003aa shrq         $26, %rdx
	0x69, 0xc2, 0x00, 0xe1, 0xf5, 0x05, //0x000003ae imull        $100000000, %edx, %eax
	0x29, 0xc6, //0x000003b4 subl         %eax, %esi
	0x66, 0x0f, 0x6e, 0xc2, //0x000003b6 movd         %edx, %xmm0
	0xf3, 0x0f, 0x6f, 0x0d, 0x3e, 0xfc, 0xff, 0xff, //0x000003ba movdqu       $-962(%rip), %xmm1  /* LCPI0_0+0(%rip) */
	0x66, 0x0f, 0x6f, 0xd0, //0x000003c2 movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0xf4, 0xd1, //0x000003c6 pmuludq      %xmm1, %xmm2
	0x66, 0x0f, 0x73, 0xd2, 0x2d, //0x000003ca psrlq        $45, %xmm2
	0xb8, 0x10, 0x27, 0x00, 0x00, //0x000003cf movl         $10000, %eax
	0x66, 0x48, 0x0f, 0x6e, 0xd8, //0x000003d4 movq         %rax, %xmm3
	0x66, 0x0f, 0x6f, 0xe2, //0x000003d9 movdqa       %xmm2, %xmm4
	0x66, 0x0f, 0xf4, 0xe3, //0x000003dd pmuludq      %xmm3, %xmm4
	0x66, 0x0f, 0xfa, 0xc4, //0x000003e1 psubd        %xmm4, %xmm0
	0x66, 0x0f, 0x61, 0xd0, //0x000003e5 punpcklwd    %xmm0, %xmm2
	0x66, 0x0f, 0x73, 0xf2, 0x02, //0x000003e9 psllq        $2, %xmm2
	0xf2, 0x0f, 0x70, 0xc2, 0x50, //0x000003ee pshuflw      $80, %xmm2, %xmm0
	0x66, 0x0f, 0x70, 0xc0, 0x50, //0x000003f3 pshufd       $80, %xmm0, %xmm0
	0xf3, 0x0f, 0x6f, 0x15, 0x10, 0xfc, 0xff, 0xff, //0x000003f8 movdqu       $-1008(%rip), %xmm2  /* LCPI0_1+0(%rip) */
	0x66, 0x0f, 0xe4, 0xc2, //0x00000400 pmulhuw      %xmm2, %xmm0
	0xf3, 0x0f, 0x6f, 0x25, 0x14, 0xfc, 0xff, 0xff, //0x00000404 movdqu       $-1004(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0x66, 0x0f, 0xe4, 0xc4, //0x0000040c pmulhuw      %xmm4, %xmm0
	0xf3, 0x0f, 0x6f, 0x2d, 0x18, 0xfc, 0xff, 0xff, //0x00000410 movdqu       $-1000(%rip), %xmm5  /* LCPI0_3+0(%rip) */
	0x66, 0x0f, 0x6f, 0xf0, //0x00000418 movdqa       %xmm0, %xmm6
	0x66, 0x0f, 0xd5, 0xf5, //0x0000041c pmullw       %xmm5, %xmm6
	0x66, 0x0f, 0x73, 0xf6, 0x10, //0x00000420 psllq        $16, %xmm6
	0x66, 0x0f, 0xf9, 0xc6, //0x00000425 psubw        %xmm6, %xmm0
	0x66, 0x0f, 0x6e, 0xf6, //0x00000429 movd         %esi, %xmm6
	0x66, 0x0f, 0xf4, 0xce, //0x0000042d pmuludq      %xmm6, %xmm1
	0x66, 0x0f, 0x73, 0xd1, 0x2d, //0x00000431 psrlq        $45, %xmm1
	0x66, 0x0f, 0xf4, 0xd9, //0x00000436 pmuludq      %xmm1, %xmm3
	0x66, 0x0f, 0xfa, 0xf3, //0x0000043a psubd        %xmm3, %xmm6
	0x66, 0x0f, 0x61, 0xce, //0x0000043e punpcklwd    %xmm6, %xmm1
	0x66, 0x0f, 0x73, 0xf1, 0x02, //0x00000442 psllq        $2, %xmm1
	0xf2, 0x0f, 0x70, 0xc9, 0x50, //0x00000447 pshuflw      $80, %xmm1, %xmm1
	0x66, 0x0f, 0x70, 0xc9, 0x50, //0x0000044c pshufd       $80, %xmm1, %xmm1
	0x66, 0x0f, 0xe4, 0xca, //0x00000451 pmulhuw      %xmm2, %xmm1
	0x66, 0x0f, 0xe4, 0xcc, //0x00000455 pmulhuw      %xmm4, %xmm1
	0x66, 0x0f, 0xd5, 0xe9, //0x00000459 pmullw       %xmm1, %xmm5
	0x66, 0x0f, 0x73, 0xf5, 0x10, //0x0000045d psllq        $16, %xmm5
	0x66, 0x0f, 0xf9, 0xcd, //0x00000462 psubw        %xmm5, %xmm1
	0x66, 0x0f, 0x67, 0xc1, //0x00000466 packuswb     %xmm1, %xmm0
	0xf3, 0x0f, 0x6f, 0x0d, 0xce, 0xfb, 0xff, 0xff, //0x0000046a movdqu       $-1074(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0x66, 0x0f, 0xfc, 0xc8, //0x00000472 paddb        %xmm0, %xmm1
	0x66, 0x0f, 0xef, 0xd2, //0x00000476 pxor         %xmm2, %xmm2
	0x66, 0x0f, 0x74, 0xd0, //0x0000047a pcmpeqb      %xmm0, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x0000047e pmovmskb     %xmm2, %eax
	0x0d, 0x00, 0x80, 0x00, 0x00, //0x00000482 orl          $32768, %eax
	0x35, 0xff, 0x7f, 0xff, 0xff, //0x00000487 xorl         $-32769, %eax
	0x0f, 0xbc, 0xc0, //0x0000048c bsfl         %eax, %eax
	0xb9, 0x10, 0x00, 0x00, 0x00, //0x0000048f movl         $16, %ecx
	0x29, 0xc1, //0x00000494 subl         %eax, %ecx
	0x48, 0xc1, 0xe0, 0x04, //0x00000496 shlq         $4, %rax
	0x48, 0x8d, 0x15, 0x9f, 0x05, 0x00, 0x00, //0x0000049a leaq         $1439(%rip), %rdx  /* _VecShiftShuffles+0(%rip) */
	0x66, 0x0f, 0x38, 0x00, 0x0c, 0x10, //0x000004a1 pshufb       (%rax,%rdx), %xmm1
	0xf3, 0x0f, 0x7f, 0x0f, //0x000004a7 movdqu       %xmm1, (%rdi)
	0x89, 0xc8, //0x000004ab movl         %ecx, %eax
	0x5d, //0x000004ad popq         %rbp
	0xc3, //0x000004ae retq         
	//0x000004af LBB0_36
	0x31, 0xc9, //0x000004af xorl         %ecx, %ecx
	0x81, 0xfe, 0x40, 0x42, 0x0f, 0x00, //0x000004b1 cmpl         $1000000, %esi
	0x0f, 0x82, 0x7b, 0x00, 0x00, 0x00, //0x000004b7 jb           LBB0_38
	//0x000004bd LBB0_37
	0x44, 0x89, 0xd0, //0x000004bd movl         %r10d, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000004c0 orq          $1, %rax
	0x48, 0x8d, 0x35, 0xa5, 0x04, 0x00, 0x00, //0x000004c4 leaq         $1189(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x30, //0x000004cb movb         (%rax,%rsi), %al
	0x89, 0xce, //0x000004ce movl         %ecx, %esi
	0xff, 0xc1, //0x000004d0 incl         %ecx
	0x88, 0x44, 0x37, 0x01, //0x000004d2 movb         %al, $1(%rdi,%rsi)
	//0x000004d6 LBB0_39
	0x48, 0x8d, 0x05, 0x93, 0x04, 0x00, 0x00, //0x000004d6 leaq         $1171(%rip), %rax  /* _Digits+0(%rip) */
	0x41, 0x8a, 0x04, 0x01, //0x000004dd movb         (%r9,%rax), %al
	0x89, 0xce, //0x000004e1 movl         %ecx, %esi
	0xff, 0xc1, //0x000004e3 incl         %ecx
	0x88, 0x44, 0x37, 0x01, //0x000004e5 movb         %al, $1(%rdi,%rsi)
	//0x000004e9 LBB0_40
	0x41, 0x0f, 0xb7, 0xc1, //0x000004e9 movzwl       %r9w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000004ed orq          $1, %rax
	0x48, 0x8d, 0x35, 0x78, 0x04, 0x00, 0x00, //0x000004f1 leaq         $1144(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x30, //0x000004f8 movb         (%rax,%rsi), %al
	0x89, 0xca, //0x000004fb movl         %ecx, %edx
	0x88, 0x44, 0x17, 0x01, //0x000004fd movb         %al, $1(%rdi,%rdx)
	0x41, 0x8a, 0x04, 0x30, //0x00000501 movb         (%r8,%rsi), %al
	0x88, 0x44, 0x17, 0x02, //0x00000505 movb         %al, $2(%rdi,%rdx)
	0x41, 0x0f, 0xb7, 0xc0, //0x00000509 movzwl       %r8w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x0000050d orq          $1, %rax
	0x8a, 0x04, 0x30, //0x00000511 movb         (%rax,%rsi), %al
	0x88, 0x44, 0x17, 0x03, //0x00000514 movb         %al, $3(%rdi,%rdx)
	0x41, 0x8a, 0x04, 0x33, //0x00000518 movb         (%r11,%rsi), %al
	0x88, 0x44, 0x17, 0x04, //0x0000051c movb         %al, $4(%rdi,%rdx)
	0x41, 0x0f, 0xb7, 0xc3, //0x00000520 movzwl       %r11w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x00000524 orq          $1, %rax
	0x8a, 0x04, 0x30, //0x00000528 movb         (%rax,%rsi), %al
	0x83, 0xc1, 0x05, //0x0000052b addl         $5, %ecx
	0x88, 0x44, 0x17, 0x05, //0x0000052e movb         %al, $5(%rdi,%rdx)
	0xff, 0xc1, //0x00000532 incl         %ecx
	0x89, 0xc8, //0x00000534 movl         %ecx, %eax
	0x5d, //0x00000536 popq         %rbp
	0xc3, //0x00000537 retq         
	//0x00000538 LBB0_38
	0x31, 0xc9, //0x00000538 xorl         %ecx, %ecx
	0x81, 0xfe, 0xa0, 0x86, 0x01, 0x00, //0x0000053a cmpl         $100000, %esi
	0x0f, 0x83, 0x90, 0xff, 0xff, 0xff, //0x00000540 jae          LBB0_39
	0xe9, 0x9e, 0xff, 0xff, 0xff, //0x00000546 jmp          LBB0_40
	//0x0000054b LBB0_41
	0x48, 0xb9, 0x00, 0x00, 0xc1, 0x6f, 0xf2, 0x86, 0x23, 0x00, //0x0000054b movabsq      $10000000000000000, %rcx
	0x48, 0x39, 0xce, //0x00000555 cmpq         %rcx, %rsi
	0x0f, 0x83, 0x8d, 0x02, 0x00, 0x00, //0x00000558 jae          LBB0_43
	0x48, 0xb9, 0xfd, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x0000055e movabsq      $-6067343680855748867, %rcx
	0x48, 0x89, 0xf0, //0x00000568 movq         %rsi, %rax
	0x48, 0xf7, 0xe1, //0x0000056b mulq         %rcx
	0x48, 0xc1, 0xea, 0x1a, //0x0000056e shrq         $26, %rdx
	0x69, 0xc2, 0x00, 0xe1, 0xf5, 0x05, //0x00000572 imull        $100000000, %edx, %eax
	0x29, 0xc6, //0x00000578 subl         %eax, %esi
	0x66, 0x0f, 0x6e, 0xc2, //0x0000057a movd         %edx, %xmm0
	0xf3, 0x0f, 0x6f, 0x0d, 0x7a, 0xfa, 0xff, 0xff, //0x0000057e movdqu       $-1414(%rip), %xmm1  /* LCPI0_0+0(%rip) */
	0x66, 0x0f, 0x6f, 0xd0, //0x00000586 movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0xf4, 0xd1, //0x0000058a pmuludq      %xmm1, %xmm2
	0x66, 0x0f, 0x73, 0xd2, 0x2d, //0x0000058e psrlq        $45, %xmm2
	0xb8, 0x10, 0x27, 0x00, 0x00, //0x00000593 movl         $10000, %eax
	0x66, 0x48, 0x0f, 0x6e, 0xd8, //0x00000598 movq         %rax, %xmm3
	0x66, 0x0f, 0x6f, 0xe2, //0x0000059d movdqa       %xmm2, %xmm4
	0x66, 0x0f, 0xf4, 0xe3, //0x000005a1 pmuludq      %xmm3, %xmm4
	0x66, 0x0f, 0xfa, 0xc4, //0x000005a5 psubd        %xmm4, %xmm0
	0x66, 0x0f, 0x61, 0xd0, //0x000005a9 punpcklwd    %xmm0, %xmm2
	0x66, 0x0f, 0x73, 0xf2, 0x02, //0x000005ad psllq        $2, %xmm2
	0xf2, 0x0f, 0x70, 0xc2, 0x50, //0x000005b2 pshuflw      $80, %xmm2, %xmm0
	0x66, 0x0f, 0x70, 0xc0, 0x50, //0x000005b7 pshufd       $80, %xmm0, %xmm0
	0xf3, 0x0f, 0x6f, 0x15, 0x4c, 0xfa, 0xff, 0xff, //0x000005bc movdqu       $-1460(%rip), %xmm2  /* LCPI0_1+0(%rip) */
	0x66, 0x0f, 0xe4, 0xc2, //0x000005c4 pmulhuw      %xmm2, %xmm0
	0xf3, 0x0f, 0x6f, 0x25, 0x50, 0xfa, 0xff, 0xff, //0x000005c8 movdqu       $-1456(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0x66, 0x0f, 0xe4, 0xc4, //0x000005d0 pmulhuw      %xmm4, %xmm0
	0xf3, 0x0f, 0x6f, 0x2d, 0x54, 0xfa, 0xff, 0xff, //0x000005d4 movdqu       $-1452(%rip), %xmm5  /* LCPI0_3+0(%rip) */
	0x66, 0x0f, 0x6f, 0xf0, //0x000005dc movdqa       %xmm0, %xmm6
	0x66, 0x0f, 0xd5, 0xf5, //0x000005e0 pmullw       %xmm5, %xmm6
	0x66, 0x0f, 0x73, 0xf6, 0x10, //0x000005e4 psllq        $16, %xmm6
	0x66, 0x0f, 0xf9, 0xc6, //0x000005e9 psubw        %xmm6, %xmm0
	0x66, 0x0f, 0x6e, 0xf6, //0x000005ed movd         %esi, %xmm6
	0x66, 0x0f, 0xf4, 0xce, //0x000005f1 pmuludq      %xmm6, %xmm1
	0x66, 0x0f, 0x73, 0xd1, 0x2d, //0x000005f5 psrlq        $45, %xmm1
	0x66, 0x0f, 0xf4, 0xd9, //0x000005fa pmuludq      %xmm1, %xmm3
	0x66, 0x0f, 0xfa, 0xf3, //0x000005fe psubd        %xmm3, %xmm6
	0x66, 0x0f, 0x61, 0xce, //0x00000602 punpcklwd    %xmm6, %xmm1
	0x66, 0x0f, 0x73, 0xf1, 0x02, //0x00000606 psllq        $2, %xmm1
	0xf2, 0x0f, 0x70, 0xc9, 0x50, //0x0000060b pshuflw      $80, %xmm1, %xmm1
	0x66, 0x0f, 0x70, 0xc9, 0x50, //0x00000610 pshufd       $80, %xmm1, %xmm1
	0x66, 0x0f, 0xe4, 0xca, //0x00000615 pmulhuw      %xmm2, %xmm1
	0x66, 0x0f, 0xe4, 0xcc, //0x00000619 pmulhuw      %xmm4, %xmm1
	0x66, 0x0f, 0xd5, 0xe9, //0x0000061d pmullw       %xmm1, %xmm5
	0x66, 0x0f, 0x73, 0xf5, 0x10, //0x00000621 psllq        $16, %xmm5
	0x66, 0x0f, 0xf9, 0xcd, //0x00000626 psubw        %xmm5, %xmm1
	0x66, 0x0f, 0x67, 0xc1, //0x0000062a packuswb     %xmm1, %xmm0
	0xf3, 0x0f, 0x6f, 0x0d, 0x0a, 0xfa, 0xff, 0xff, //0x0000062e movdqu       $-1526(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0x66, 0x0f, 0xfc, 0xc8, //0x00000636 paddb        %xmm0, %xmm1
	0x66, 0x0f, 0xef, 0xd2, //0x0000063a pxor         %xmm2, %xmm2
	0x66, 0x0f, 0x74, 0xd0, //0x0000063e pcmpeqb      %xmm0, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00000642 pmovmskb     %xmm2, %eax
	0x0d, 0x00, 0x80, 0x00, 0x00, //0x00000646 orl          $32768, %eax
	0x35, 0xff, 0x7f, 0xff, 0xff, //0x0000064b xorl         $-32769, %eax
	0x0f, 0xbc, 0xc0, //0x00000650 bsfl         %eax, %eax
	0xb9, 0x10, 0x00, 0x00, 0x00, //0x00000653 movl         $16, %ecx
	0x29, 0xc1, //0x00000658 subl         %eax, %ecx
	0x48, 0xc1, 0xe0, 0x04, //0x0000065a shlq         $4, %rax
	0x48, 0x8d, 0x15, 0xdb, 0x03, 0x00, 0x00, //0x0000065e leaq         $987(%rip), %rdx  /* _VecShiftShuffles+0(%rip) */
	0x66, 0x0f, 0x38, 0x00, 0x0c, 0x10, //0x00000665 pshufb       (%rax,%rdx), %xmm1
	0xf3, 0x0f, 0x7f, 0x4f, 0x01, //0x0000066b movdqu       %xmm1, $1(%rdi)
	0xff, 0xc1, //0x00000670 incl         %ecx
	0x89, 0xc8, //0x00000672 movl         %ecx, %eax
	0x5d, //0x00000674 popq         %rbp
	0xc3, //0x00000675 retq         
	//0x00000676 LBB0_19
	0x48, 0xba, 0x57, 0x78, 0x13, 0xb1, 0x2f, 0x65, 0xa5, 0x39, //0x00000676 movabsq      $4153837486827862103, %rdx
	0x48, 0x89, 0xf0, //0x00000680 movq         %rsi, %rax
	0x48, 0xf7, 0xe2, //0x00000683 mulq         %rdx
	0x48, 0xc1, 0xea, 0x33, //0x00000686 shrq         $51, %rdx
	0x48, 0x0f, 0xaf, 0xca, //0x0000068a imulq        %rdx, %rcx
	0x48, 0x29, 0xce, //0x0000068e subq         %rcx, %rsi
	0x83, 0xfa, 0x09, //0x00000691 cmpl         $9, %edx
	0x0f, 0x87, 0x0f, 0x00, 0x00, 0x00, //0x00000694 ja           LBB0_21
	0x80, 0xc2, 0x30, //0x0000069a addb         $48, %dl
	0x88, 0x17, //0x0000069d movb         %dl, (%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x0000069f movl         $1, %ecx
	0xe9, 0x5c, 0x00, 0x00, 0x00, //0x000006a4 jmp          LBB0_24
	//0x000006a9 LBB0_21
	0x83, 0xfa, 0x63, //0x000006a9 cmpl         $99, %edx
	0x0f, 0x87, 0x1f, 0x00, 0x00, 0x00, //0x000006ac ja           LBB0_23
	0x89, 0xd0, //0x000006b2 movl         %edx, %eax
	0x48, 0x8d, 0x0d, 0xb5, 0x02, 0x00, 0x00, //0x000006b4 leaq         $693(%rip), %rcx  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x41, //0x000006bb movb         (%rcx,%rax,2), %dl
	0x8a, 0x44, 0x41, 0x01, //0x000006be movb         $1(%rcx,%rax,2), %al
	0x88, 0x17, //0x000006c2 movb         %dl, (%rdi)
	0x88, 0x47, 0x01, //0x000006c4 movb         %al, $1(%rdi)
	0xb9, 0x02, 0x00, 0x00, 0x00, //0x000006c7 movl         $2, %ecx
	0xe9, 0x34, 0x00, 0x00, 0x00, //0x000006cc jmp          LBB0_24
	//0x000006d1 LBB0_23
	0x89, 0xd0, //0x000006d1 movl         %edx, %eax
	0xc1, 0xe8, 0x02, //0x000006d3 shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x000006d6 imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x000006dc shrl         $17, %eax
	0x8d, 0x48, 0x30, //0x000006df leal         $48(%rax), %ecx
	0x88, 0x0f, //0x000006e2 movb         %cl, (%rdi)
	0x6b, 0xc0, 0x64, //0x000006e4 imull        $100, %eax, %eax
	0x29, 0xc2, //0x000006e7 subl         %eax, %edx
	0x0f, 0xb7, 0xc2, //0x000006e9 movzwl       %dx, %eax
	0x48, 0x8d, 0x0d, 0x7d, 0x02, 0x00, 0x00, //0x000006ec leaq         $637(%rip), %rcx  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x41, //0x000006f3 movb         (%rcx,%rax,2), %dl
	0x8a, 0x44, 0x41, 0x01, //0x000006f6 movb         $1(%rcx,%rax,2), %al
	0x88, 0x57, 0x01, //0x000006fa movb         %dl, $1(%rdi)
	0x88, 0x47, 0x02, //0x000006fd movb         %al, $2(%rdi)
	0xb9, 0x03, 0x00, 0x00, 0x00, //0x00000700 movl         $3, %ecx
	//0x00000705 LBB0_24
	0x48, 0xba, 0xfd, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x00000705 movabsq      $-6067343680855748867, %rdx
	0x48, 0x89, 0xf0, //0x0000070f movq         %rsi, %rax
	0x48, 0xf7, 0xe2, //0x00000712 mulq         %rdx
	0x48, 0xc1, 0xea, 0x1a, //0x00000715 shrq         $26, %rdx
	0x66, 0x0f, 0x6e, 0xc2, //0x00000719 movd         %edx, %xmm0
	0xf3, 0x0f, 0x6f, 0x0d, 0xdb, 0xf8, 0xff, 0xff, //0x0000071d movdqu       $-1829(%rip), %xmm1  /* LCPI0_0+0(%rip) */
	0x66, 0x0f, 0x6f, 0xd8, //0x00000725 movdqa       %xmm0, %xmm3
	0x66, 0x0f, 0xf4, 0xd9, //0x00000729 pmuludq      %xmm1, %xmm3
	0x66, 0x0f, 0x73, 0xd3, 0x2d, //0x0000072d psrlq        $45, %xmm3
	0xb8, 0x10, 0x27, 0x00, 0x00, //0x00000732 movl         $10000, %eax
	0x66, 0x48, 0x0f, 0x6e, 0xd0, //0x00000737 movq         %rax, %xmm2
	0x66, 0x0f, 0x6f, 0xe3, //0x0000073c movdqa       %xmm3, %xmm4
	0x66, 0x0f, 0xf4, 0xe2, //0x00000740 pmuludq      %xmm2, %xmm4
	0x66, 0x0f, 0xfa, 0xc4, //0x00000744 psubd        %xmm4, %xmm0
	0x66, 0x0f, 0x61, 0xd8, //0x00000748 punpcklwd    %xmm0, %xmm3
	0x66, 0x0f, 0x73, 0xf3, 0x02, //0x0000074c psllq        $2, %xmm3
	0xf2, 0x0f, 0x70, 0xc3, 0x50, //0x00000751 pshuflw      $80, %xmm3, %xmm0
	0x66, 0x0f, 0x70, 0xc0, 0x50, //0x00000756 pshufd       $80, %xmm0, %xmm0
	0xf3, 0x0f, 0x6f, 0x25, 0xad, 0xf8, 0xff, 0xff, //0x0000075b movdqu       $-1875(%rip), %xmm4  /* LCPI0_1+0(%rip) */
	0x66, 0x0f, 0xe4, 0xc4, //0x00000763 pmulhuw      %xmm4, %xmm0
	0xf3, 0x0f, 0x6f, 0x2d, 0xb1, 0xf8, 0xff, 0xff, //0x00000767 movdqu       $-1871(%rip), %xmm5  /* LCPI0_2+0(%rip) */
	0x66, 0x0f, 0xe4, 0xc5, //0x0000076f pmulhuw      %xmm5, %xmm0
	0xf3, 0x0f, 0x6f, 0x1d, 0xb5, 0xf8, 0xff, 0xff, //0x00000773 movdqu       $-1867(%rip), %xmm3  /* LCPI0_3+0(%rip) */
	0x66, 0x0f, 0x6f, 0xf0, //0x0000077b movdqa       %xmm0, %xmm6
	0x66, 0x0f, 0xd5, 0xf3, //0x0000077f pmullw       %xmm3, %xmm6
	0x66, 0x0f, 0x73, 0xf6, 0x10, //0x00000783 psllq        $16, %xmm6
	0x66, 0x0f, 0xf9, 0xc6, //0x00000788 psubw        %xmm6, %xmm0
	0x69, 0xc2, 0x00, 0xe1, 0xf5, 0x05, //0x0000078c imull        $100000000, %edx, %eax
	0x29, 0xc6, //0x00000792 subl         %eax, %esi
	0x66, 0x0f, 0x6e, 0xf6, //0x00000794 movd         %esi, %xmm6
	0x66, 0x0f, 0xf4, 0xce, //0x00000798 pmuludq      %xmm6, %xmm1
	0x66, 0x0f, 0x73, 0xd1, 0x2d, //0x0000079c psrlq        $45, %xmm1
	0x66, 0x0f, 0xf4, 0xd1, //0x000007a1 pmuludq      %xmm1, %xmm2
	0x66, 0x0f, 0xfa, 0xf2, //0x000007a5 psubd        %xmm2, %xmm6
	0x66, 0x0f, 0x61, 0xce, //0x000007a9 punpcklwd    %xmm6, %xmm1
	0x66, 0x0f, 0x73, 0xf1, 0x02, //0x000007ad psllq        $2, %xmm1
	0xf2, 0x0f, 0x70, 0xc9, 0x50, //0x000007b2 pshuflw      $80, %xmm1, %xmm1
	0x66, 0x0f, 0x70, 0xc9, 0x50, //0x000007b7 pshufd       $80, %xmm1, %xmm1
	0x66, 0x0f, 0xe4, 0xcc, //0x000007bc pmulhuw      %xmm4, %xmm1
	0x66, 0x0f, 0xe4, 0xcd, //0x000007c0 pmulhuw      %xmm5, %xmm1
	0x66, 0x0f, 0xd5, 0xd9, //0x000007c4 pmullw       %xmm1, %xmm3
	0x66, 0x0f, 0x73, 0xf3, 0x10, //0x000007c8 psllq        $16, %xmm3
	0x66, 0x0f, 0xf9, 0xcb, //0x000007cd psubw        %xmm3, %xmm1
	0x66, 0x0f, 0x67, 0xc1, //0x000007d1 packuswb     %xmm1, %xmm0
	0x66, 0x0f, 0xfc, 0x05, 0x63, 0xf8, 0xff, 0xff, //0x000007d5 paddb        $-1949(%rip), %xmm0  /* LCPI0_4+0(%rip) */
	0x89, 0xc8, //0x000007dd movl         %ecx, %eax
	0xf3, 0x0f, 0x7f, 0x04, 0x07, //0x000007df movdqu       %xmm0, (%rdi,%rax)
	0x83, 0xc9, 0x10, //0x000007e4 orl          $16, %ecx
	0x89, 0xc8, //0x000007e7 movl         %ecx, %eax
	0x5d, //0x000007e9 popq         %rbp
	0xc3, //0x000007ea retq         
	//0x000007eb LBB0_43
	0x48, 0xba, 0x57, 0x78, 0x13, 0xb1, 0x2f, 0x65, 0xa5, 0x39, //0x000007eb movabsq      $4153837486827862103, %rdx
	0x48, 0x89, 0xf0, //0x000007f5 movq         %rsi, %rax
	0x48, 0xf7, 0xe2, //0x000007f8 mulq         %rdx
	0x48, 0xc1, 0xea, 0x33, //0x000007fb shrq         $51, %rdx
	0x48, 0x0f, 0xaf, 0xca, //0x000007ff imulq        %rdx, %rcx
	0x48, 0x29, 0xce, //0x00000803 subq         %rcx, %rsi
	0x83, 0xfa, 0x09, //0x00000806 cmpl         $9, %edx
	0x0f, 0x87, 0x10, 0x00, 0x00, 0x00, //0x00000809 ja           LBB0_45
	0x80, 0xc2, 0x30, //0x0000080f addb         $48, %dl
	0x88, 0x57, 0x01, //0x00000812 movb         %dl, $1(%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x00000815 movl         $1, %ecx
	0xe9, 0x5e, 0x00, 0x00, 0x00, //0x0000081a jmp          LBB0_48
	//0x0000081f LBB0_45
	0x83, 0xfa, 0x63, //0x0000081f cmpl         $99, %edx
	0x0f, 0x87, 0x20, 0x00, 0x00, 0x00, //0x00000822 ja           LBB0_47
	0x89, 0xd0, //0x00000828 movl         %edx, %eax
	0x48, 0x8d, 0x0d, 0x3f, 0x01, 0x00, 0x00, //0x0000082a leaq         $319(%rip), %rcx  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x41, //0x00000831 movb         (%rcx,%rax,2), %dl
	0x8a, 0x44, 0x41, 0x01, //0x00000834 movb         $1(%rcx,%rax,2), %al
	0x88, 0x57, 0x01, //0x00000838 movb         %dl, $1(%rdi)
	0x88, 0x47, 0x02, //0x0000083b movb         %al, $2(%rdi)
	0xb9, 0x02, 0x00, 0x00, 0x00, //0x0000083e movl         $2, %ecx
	0xe9, 0x35, 0x00, 0x00, 0x00, //0x00000843 jmp          LBB0_48
	//0x00000848 LBB0_47
	0x89, 0xd0, //0x00000848 movl         %edx, %eax
	0xc1, 0xe8, 0x02, //0x0000084a shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x0000084d imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x00000853 shrl         $17, %eax
	0x8d, 0x48, 0x30, //0x00000856 leal         $48(%rax), %ecx
	0x88, 0x4f, 0x01, //0x00000859 movb         %cl, $1(%rdi)
	0x6b, 0xc0, 0x64, //0x0000085c imull        $100, %eax, %eax
	0x29, 0xc2, //0x0000085f subl         %eax, %edx
	0x0f, 0xb7, 0xc2, //0x00000861 movzwl       %dx, %eax
	0x48, 0x8d, 0x0d, 0x05, 0x01, 0x00, 0x00, //0x00000864 leaq         $261(%rip), %rcx  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x41, //0x0000086b movb         (%rcx,%rax,2), %dl
	0x8a, 0x44, 0x41, 0x01, //0x0000086e movb         $1(%rcx,%rax,2), %al
	0x88, 0x57, 0x02, //0x00000872 movb         %dl, $2(%rdi)
	0x88, 0x47, 0x03, //0x00000875 movb         %al, $3(%rdi)
	0xb9, 0x03, 0x00, 0x00, 0x00, //0x00000878 movl         $3, %ecx
	//0x0000087d LBB0_48
	0x48, 0xba, 0xfd, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x0000087d movabsq      $-6067343680855748867, %rdx
	0x48, 0x89, 0xf0, //0x00000887 movq         %rsi, %rax
	0x48, 0xf7, 0xe2, //0x0000088a mulq         %rdx
	0x48, 0xc1, 0xea, 0x1a, //0x0000088d shrq         $26, %rdx
	0x66, 0x0f, 0x6e, 0xc2, //0x00000891 movd         %edx, %xmm0
	0xf3, 0x0f, 0x6f, 0x0d, 0x63, 0xf7, 0xff, 0xff, //0x00000895 movdqu       $-2205(%rip), %xmm1  /* LCPI0_0+0(%rip) */
	0x66, 0x0f, 0x6f, 0xd8, //0x0000089d movdqa       %xmm0, %xmm3
	0x66, 0x0f, 0xf4, 0xd9, //0x000008a1 pmuludq      %xmm1, %xmm3
	0x66, 0x0f, 0x73, 0xd3, 0x2d, //0x000008a5 psrlq        $45, %xmm3
	0xb8, 0x10, 0x27, 0x00, 0x00, //0x000008aa movl         $10000, %eax
	0x66, 0x48, 0x0f, 0x6e, 0xd0, //0x000008af movq         %rax, %xmm2
	0x66, 0x0f, 0x6f, 0xe3, //0x000008b4 movdqa       %xmm3, %xmm4
	0x66, 0x0f, 0xf4, 0xe2, //0x000008b8 pmuludq      %xmm2, %xmm4
	0x66, 0x0f, 0xfa, 0xc4, //0x000008bc psubd        %xmm4, %xmm0
	0x66, 0x0f, 0x61, 0xd8, //0x000008c0 punpcklwd    %xmm0, %xmm3
	0x66, 0x0f, 0x73, 0xf3, 0x02, //0x000008c4 psllq        $2, %xmm3
	0xf2, 0x0f, 0x70, 0xc3, 0x50, //0x000008c9 pshuflw      $80, %xmm3, %xmm0
	0x66, 0x0f, 0x70, 0xc0, 0x50, //0x000008ce pshufd       $80, %xmm0, %xmm0
	0xf3, 0x0f, 0x6f, 0x25, 0x35, 0xf7, 0xff, 0xff, //0x000008d3 movdqu       $-2251(%rip), %xmm4  /* LCPI0_1+0(%rip) */
	0x66, 0x0f, 0xe4, 0xc4, //0x000008db pmulhuw      %xmm4, %xmm0
	0xf3, 0x0f, 0x6f, 0x2d, 0x39, 0xf7, 0xff, 0xff, //0x000008df movdqu       $-2247(%rip), %xmm5  /* LCPI0_2+0(%rip) */
	0x66, 0x0f, 0xe4, 0xc5, //0x000008e7 pmulhuw      %xmm5, %xmm0
	0xf3, 0x0f, 0x6f, 0x1d, 0x3d, 0xf7, 0xff, 0xff, //0x000008eb movdqu       $-2243(%rip), %xmm3  /* LCPI0_3+0(%rip) */
	0x66, 0x0f, 0x6f, 0xf0, //0x000008f3 movdqa       %xmm0, %xmm6
	0x66, 0x0f, 0xd5, 0xf3, //0x000008f7 pmullw       %xmm3, %xmm6
	0x66, 0x0f, 0x73, 0xf6, 0x10, //0x000008fb psllq        $16, %xmm6
	0x66, 0x0f, 0xf9, 0xc6, //0x00000900 psubw        %xmm6, %xmm0
	0x69, 0xc2, 0x00, 0xe1, 0xf5, 0x05, //0x00000904 imull        $100000000, %edx, %eax
	0x29, 0xc6, //0x0000090a subl         %eax, %esi
	0x66, 0x0f, 0x6e, 0xf6, //0x0000090c movd         %esi, %xmm6
	0x66, 0x0f, 0xf4, 0xce, //0x00000910 pmuludq      %xmm6, %xmm1
	0x66, 0x0f, 0x73, 0xd1, 0x2d, //0x00000914 psrlq        $45, %xmm1
	0x66, 0x0f, 0xf4, 0xd1, //0x00000919 pmuludq      %xmm1, %xmm2
	0x66, 0x0f, 0xfa, 0xf2, //0x0000091d psubd        %xmm2, %xmm6
	0x66, 0x0f, 0x61, 0xce, //0x00000921 punpcklwd    %xmm6, %xmm1
	0x66, 0x0f, 0x73, 0xf1, 0x02, //0x00000925 psllq        $2, %xmm1
	0xf2, 0x0f, 0x70, 0xc9, 0x50, //0x0000092a pshuflw      $80, %xmm1, %xmm1
	0x66, 0x0f, 0x70, 0xc9, 0x50, //0x0000092f pshufd       $80, %xmm1, %xmm1
	0x66, 0x0f, 0xe4, 0xcc, //0x00000934 pmulhuw      %xmm4, %xmm1
	0x66, 0x0f, 0xe4, 0xcd, //0x00000938 pmulhuw      %xmm5, %xmm1
	0x66, 0x0f, 0xd5, 0xd9, //0x0000093c pmullw       %xmm1, %xmm3
	0x66, 0x0f, 0x73, 0xf3, 0x10, //0x00000940 psllq        $16, %xmm3
	0x66, 0x0f, 0xf9, 0xcb, //0x00000945 psubw        %xmm3, %xmm1
	0x66, 0x0f, 0x67, 0xc1, //0x00000949 packuswb     %xmm1, %xmm0
	0x66, 0x0f, 0xfc, 0x05, 0xeb, 0xf6, 0xff, 0xff, //0x0000094d paddb        $-2325(%rip), %xmm0  /* LCPI0_4+0(%rip) */
	0x89, 0xc8, //0x00000955 movl         %ecx, %eax
	0xf3, 0x0f, 0x7f, 0x44, 0x07, 0x01, //0x00000957 movdqu       %xmm0, $1(%rdi,%rax)
	0x83, 0xc9, 0x10, //0x0000095d orl          $16, %ecx
	0xff, 0xc1, //0x00000960 incl         %ecx
	0x89, 0xc8, //0x00000962 movl         %ecx, %eax
	0x5d, //0x00000964 popq         %rbp
	0xc3, //0x00000965 retq         
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000966 .p2align 4, 0x00
	//0x00000970 _Digits
	0x30, 0x30, 0x30, 0x31, 0x30, 0x32, 0x30, 0x33, 0x30, 0x34, 0x30, 0x35, 0x30, 0x36, 0x30, 0x37, //0x00000970 QUAD $0x3330323031303030; QUAD $0x3730363035303430  // .ascii 16, '0001020304050607'
	0x30, 0x38, 0x30, 0x39, 0x31, 0x30, 0x31, 0x31, 0x31, 0x32, 0x31, 0x33, 0x31, 0x34, 0x31, 0x35, //0x00000980 QUAD $0x3131303139303830; QUAD $0x3531343133313231  // .ascii 16, '0809101112131415'
	0x31, 0x36, 0x31, 0x37, 0x31, 0x38, 0x31, 0x39, 0x32, 0x30, 0x32, 0x31, 0x32, 0x32, 0x32, 0x33, //0x00000990 QUAD $0x3931383137313631; QUAD $0x3332323231323032  // .ascii 16, '1617181920212223'
	0x32, 0x34, 0x32, 0x35, 0x32, 0x36, 0x32, 0x37, 0x32, 0x38, 0x32, 0x39, 0x33, 0x30, 0x33, 0x31, //0x000009a0 QUAD $0x3732363235323432; QUAD $0x3133303339323832  // .ascii 16, '2425262728293031'
	0x33, 0x32, 0x33, 0x33, 0x33, 0x34, 0x33, 0x35, 0x33, 0x36, 0x33, 0x37, 0x33, 0x38, 0x33, 0x39, //0x000009b0 QUAD $0x3533343333333233; QUAD $0x3933383337333633  // .ascii 16, '3233343536373839'
	0x34, 0x30, 0x34, 0x31, 0x34, 0x32, 0x34, 0x33, 0x34, 0x34, 0x34, 0x35, 0x34, 0x36, 0x34, 0x37, //0x000009c0 QUAD $0x3334323431343034; QUAD $0x3734363435343434  // .ascii 16, '4041424344454647'
	0x34, 0x38, 0x34, 0x39, 0x35, 0x30, 0x35, 0x31, 0x35, 0x32, 0x35, 0x33, 0x35, 0x34, 0x35, 0x35, //0x000009d0 QUAD $0x3135303539343834; QUAD $0x3535343533353235  // .ascii 16, '4849505152535455'
	0x35, 0x36, 0x35, 0x37, 0x35, 0x38, 0x35, 0x39, 0x36, 0x30, 0x36, 0x31, 0x36, 0x32, 0x36, 0x33, //0x000009e0 QUAD $0x3935383537353635; QUAD $0x3336323631363036  // .ascii 16, '5657585960616263'
	0x36, 0x34, 0x36, 0x35, 0x36, 0x36, 0x36, 0x37, 0x36, 0x38, 0x36, 0x39, 0x37, 0x30, 0x37, 0x31, //0x000009f0 QUAD $0x3736363635363436; QUAD $0x3137303739363836  // .ascii 16, '6465666768697071'
	0x37, 0x32, 0x37, 0x33, 0x37, 0x34, 0x37, 0x35, 0x37, 0x36, 0x37, 0x37, 0x37, 0x38, 0x37, 0x39, //0x00000a00 QUAD $0x3537343733373237; QUAD $0x3937383737373637  // .ascii 16, '7273747576777879'
	0x38, 0x30, 0x38, 0x31, 0x38, 0x32, 0x38, 0x33, 0x38, 0x34, 0x38, 0x35, 0x38, 0x36, 0x38, 0x37, //0x00000a10 QUAD $0x3338323831383038; QUAD $0x3738363835383438  // .ascii 16, '8081828384858687'
	0x38, 0x38, 0x38, 0x39, 0x39, 0x30, 0x39, 0x31, 0x39, 0x32, 0x39, 0x33, 0x39, 0x34, 0x39, 0x35, //0x00000a20 QUAD $0x3139303939383838; QUAD $0x3539343933393239  // .ascii 16, '8889909192939495'
	0x39, 0x36, 0x39, 0x37, 0x39, 0x38, 0x39, 0x39, //0x00000a30 QUAD $0x3939383937393639  // .ascii 8, '96979899'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000a38 .p2align 4, 0x00
	//0x00000a40 _VecShiftShuffles
	0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, //0x00000a40 QUAD $0x0706050403020100; QUAD $0x0f0e0d0c0b0a0908  // .ascii 16, '\x00\x01\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f'
	0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, //0x00000a50 QUAD $0x0807060504030201; QUAD $0xff0f0e0d0c0b0a09  // .ascii 16, '\x01\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff'
	0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, //0x00000a60 QUAD $0x0908070605040302; QUAD $0xffff0f0e0d0c0b0a  // .ascii 16, '\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff'
	0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, //0x00000a70 QUAD $0x0a09080706050403; QUAD $0xffffff0f0e0d0c0b  // .ascii 16, '\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff'
	0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, //0x00000a80 QUAD $0x0b0a090807060504; QUAD $0xffffffff0f0e0d0c  // .ascii 16, '\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff'
	0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00000a90 QUAD $0x0c0b0a0908070605; QUAD $0xffffffffff0f0e0d  // .ascii 16, '\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff\xff'
	0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00000aa0 QUAD $0x0d0c0b0a09080706; QUAD $0xffffffffffff0f0e  // .ascii 16, '\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff\xff\xff'
	0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00000ab0 QUAD $0x0e0d0c0b0a090807; QUAD $0xffffffffffffff0f  // .ascii 16, '\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff\xff\xff\xff'
	0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00000ac0 QUAD $0x0f0e0d0c0b0a0908; QUAD $0xffffffffffffffff  // .ascii 16, '\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff\xff\xff\xff\xff'
}
 
