// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__quote = 48
)

const (
    _stack__quote = 64
)

const (
    _size__quote = 1728
)

var (
    _pcsp__quote = [][2]uint32{
        {1, 0},
        {4, 8},
        {6, 16},
        {8, 24},
        {10, 32},
        {12, 40},
        {13, 48},
        {1681, 64},
        {1685, 48},
        {1686, 40},
        {1688, 32},
        {1690, 24},
        {1692, 16},
        {1694, 8},
        {1695, 0},
        {1722, 64},
    }
)

var _cfunc_quote = []loader.CFunc{
    {"_quote_entry", 0,  _entry__quote, 0, nil},
    {"_quote", _entry__quote, _size__quote, _stack__quote, _pcsp__quote},
}
