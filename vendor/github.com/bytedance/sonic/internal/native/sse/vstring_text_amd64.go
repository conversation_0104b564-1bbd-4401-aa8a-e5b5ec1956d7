// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package sse

var _text_vstring = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, // QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x00000010 LCPI0_1
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000010 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000020 LCPI0_2
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000020 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000030 .p2align 4, 0x90
	//0x00000030 _vstring
	0x55, //0x00000030 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000031 movq         %rsp, %rbp
	0x41, 0x57, //0x00000034 pushq        %r15
	0x41, 0x56, //0x00000036 pushq        %r14
	0x41, 0x55, //0x00000038 pushq        %r13
	0x41, 0x54, //0x0000003a pushq        %r12
	0x53, //0x0000003c pushq        %rbx
	0x48, 0x83, 0xec, 0x28, //0x0000003d subq         $40, %rsp
	0x48, 0x89, 0xd3, //0x00000041 movq         %rdx, %rbx
	0x4c, 0x8b, 0x16, //0x00000044 movq         (%rsi), %r10
	0xf6, 0xc1, 0x20, //0x00000047 testb        $32, %cl
	0x48, 0x89, 0x55, 0xb8, //0x0000004a movq         %rdx, $-72(%rbp)
	0x48, 0x89, 0x75, 0xc0, //0x0000004e movq         %rsi, $-64(%rbp)
	0x0f, 0x85, 0xc1, 0x01, 0x00, 0x00, //0x00000052 jne          LBB0_12
	0x48, 0x8b, 0x4f, 0x08, //0x00000058 movq         $8(%rdi), %rcx
	0x48, 0x89, 0xc8, //0x0000005c movq         %rcx, %rax
	0x4c, 0x29, 0xd0, //0x0000005f subq         %r10, %rax
	0x0f, 0x84, 0xd5, 0x06, 0x00, 0x00, //0x00000062 je           LBB0_55
	0x48, 0x89, 0x4d, 0xc8, //0x00000068 movq         %rcx, $-56(%rbp)
	0x48, 0x8b, 0x37, //0x0000006c movq         (%rdi), %rsi
	0x4a, 0x8d, 0x0c, 0x16, //0x0000006f leaq         (%rsi,%r10), %rcx
	0x48, 0x83, 0xf8, 0x40, //0x00000073 cmpq         $64, %rax
	0x48, 0x89, 0x75, 0xd0, //0x00000077 movq         %rsi, $-48(%rbp)
	0x0f, 0x82, 0xc8, 0x06, 0x00, 0x00, //0x0000007b jb           LBB0_56
	0x41, 0x89, 0xc5, //0x00000081 movl         %eax, %r13d
	0x41, 0x83, 0xe5, 0x3f, //0x00000084 andl         $63, %r13d
	0x48, 0x8d, 0x50, 0xc0, //0x00000088 leaq         $-64(%rax), %rdx
	0x48, 0x83, 0xe2, 0xc0, //0x0000008c andq         $-64, %rdx
	0x4c, 0x01, 0xd2, //0x00000090 addq         %r10, %rdx
	0x48, 0x8d, 0x54, 0x16, 0x40, //0x00000093 leaq         $64(%rsi,%rdx), %rdx
	0x48, 0x89, 0x55, 0xb0, //0x00000098 movq         %rdx, $-80(%rbp)
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x0000009c movq         $-1, %r11
	0x45, 0x31, 0xc0, //0x000000a3 xorl         %r8d, %r8d
	0xf3, 0x0f, 0x6f, 0x05, 0x52, 0xff, 0xff, 0xff, //0x000000a6 movdqu       $-174(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0x5a, 0xff, 0xff, 0xff, //0x000000ae movdqu       $-166(%rip), %xmm1  /* LCPI0_1+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000000b6 .p2align 4, 0x90
	//0x000000c0 LBB0_4
	0xf3, 0x0f, 0x6f, 0x11, //0x000000c0 movdqu       (%rcx), %xmm2
	0xf3, 0x0f, 0x6f, 0x59, 0x10, //0x000000c4 movdqu       $16(%rcx), %xmm3
	0xf3, 0x0f, 0x6f, 0x61, 0x20, //0x000000c9 movdqu       $32(%rcx), %xmm4
	0xf3, 0x0f, 0x6f, 0x69, 0x30, //0x000000ce movdqu       $48(%rcx), %xmm5
	0x66, 0x0f, 0x6f, 0xf2, //0x000000d3 movdqa       %xmm2, %xmm6
	0x66, 0x0f, 0x74, 0xf0, //0x000000d7 pcmpeqb      %xmm0, %xmm6
	0x66, 0x0f, 0xd7, 0xde, //0x000000db pmovmskb     %xmm6, %ebx
	0x66, 0x0f, 0x6f, 0xf3, //0x000000df movdqa       %xmm3, %xmm6
	0x66, 0x0f, 0x74, 0xf0, //0x000000e3 pcmpeqb      %xmm0, %xmm6
	0x66, 0x0f, 0xd7, 0xd6, //0x000000e7 pmovmskb     %xmm6, %edx
	0x66, 0x0f, 0x6f, 0xf4, //0x000000eb movdqa       %xmm4, %xmm6
	0x66, 0x0f, 0x74, 0xf0, //0x000000ef pcmpeqb      %xmm0, %xmm6
	0x66, 0x0f, 0xd7, 0xf6, //0x000000f3 pmovmskb     %xmm6, %esi
	0x66, 0x0f, 0x6f, 0xf5, //0x000000f7 movdqa       %xmm5, %xmm6
	0x66, 0x0f, 0x74, 0xf0, //0x000000fb pcmpeqb      %xmm0, %xmm6
	0x66, 0x0f, 0xd7, 0xfe, //0x000000ff pmovmskb     %xmm6, %edi
	0x66, 0x0f, 0x74, 0xd1, //0x00000103 pcmpeqb      %xmm1, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xfa, //0x00000107 pmovmskb     %xmm2, %r15d
	0x66, 0x0f, 0x74, 0xd9, //0x0000010c pcmpeqb      %xmm1, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xe3, //0x00000110 pmovmskb     %xmm3, %r12d
	0x66, 0x0f, 0x74, 0xe1, //0x00000115 pcmpeqb      %xmm1, %xmm4
	0x66, 0x44, 0x0f, 0xd7, 0xf4, //0x00000119 pmovmskb     %xmm4, %r14d
	0x66, 0x0f, 0x74, 0xe9, //0x0000011e pcmpeqb      %xmm1, %xmm5
	0x66, 0x44, 0x0f, 0xd7, 0xcd, //0x00000122 pmovmskb     %xmm5, %r9d
	0x48, 0xc1, 0xe7, 0x30, //0x00000127 shlq         $48, %rdi
	0x48, 0xc1, 0xe6, 0x20, //0x0000012b shlq         $32, %rsi
	0x48, 0xc1, 0xe2, 0x10, //0x0000012f shlq         $16, %rdx
	0x48, 0x09, 0xd3, //0x00000133 orq          %rdx, %rbx
	0x48, 0x09, 0xf3, //0x00000136 orq          %rsi, %rbx
	0x49, 0xc1, 0xe1, 0x30, //0x00000139 shlq         $48, %r9
	0x49, 0xc1, 0xe6, 0x20, //0x0000013d shlq         $32, %r14
	0x49, 0xc1, 0xe4, 0x10, //0x00000141 shlq         $16, %r12
	0x4d, 0x09, 0xe7, //0x00000145 orq          %r12, %r15
	0x4d, 0x09, 0xf7, //0x00000148 orq          %r14, %r15
	0x4d, 0x09, 0xcf, //0x0000014b orq          %r9, %r15
	0x49, 0x83, 0xfb, 0xff, //0x0000014e cmpq         $-1, %r11
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00000152 jne          LBB0_6
	0x4d, 0x85, 0xff, //0x00000158 testq        %r15, %r15
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x0000015b jne          LBB0_9
	//0x00000161 LBB0_6
	0x48, 0x09, 0xfb, //0x00000161 orq          %rdi, %rbx
	0x4c, 0x89, 0xfa, //0x00000164 movq         %r15, %rdx
	0x4c, 0x09, 0xc2, //0x00000167 orq          %r8, %rdx
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x0000016a jne          LBB0_10
	//0x00000170 LBB0_7
	0x48, 0x85, 0xdb, //0x00000170 testq        %rbx, %rbx
	0x0f, 0x85, 0x86, 0x00, 0x00, 0x00, //0x00000173 jne          LBB0_11
	//0x00000179 LBB0_8
	0x48, 0x83, 0xc0, 0xc0, //0x00000179 addq         $-64, %rax
	0x48, 0x83, 0xc1, 0x40, //0x0000017d addq         $64, %rcx
	0x48, 0x83, 0xf8, 0x3f, //0x00000181 cmpq         $63, %rax
	0x0f, 0x87, 0x35, 0xff, 0xff, 0xff, //0x00000185 ja           LBB0_4
	0xe9, 0x33, 0x03, 0x00, 0x00, //0x0000018b jmp          LBB0_29
	//0x00000190 LBB0_9
	0x48, 0x89, 0xca, //0x00000190 movq         %rcx, %rdx
	0x48, 0x2b, 0x55, 0xd0, //0x00000193 subq         $-48(%rbp), %rdx
	0x4d, 0x0f, 0xbc, 0xdf, //0x00000197 bsfq         %r15, %r11
	0x49, 0x01, 0xd3, //0x0000019b addq         %rdx, %r11
	0x48, 0x09, 0xfb, //0x0000019e orq          %rdi, %rbx
	0x4c, 0x89, 0xfa, //0x000001a1 movq         %r15, %rdx
	0x4c, 0x09, 0xc2, //0x000001a4 orq          %r8, %rdx
	0x0f, 0x84, 0xc3, 0xff, 0xff, 0xff, //0x000001a7 je           LBB0_7
	//0x000001ad LBB0_10
	0x4c, 0x89, 0xc2, //0x000001ad movq         %r8, %rdx
	0x48, 0xf7, 0xd2, //0x000001b0 notq         %rdx
	0x4c, 0x21, 0xfa, //0x000001b3 andq         %r15, %rdx
	0x4c, 0x8d, 0x0c, 0x12, //0x000001b6 leaq         (%rdx,%rdx), %r9
	0x4d, 0x09, 0xc1, //0x000001ba orq          %r8, %r9
	0x4c, 0x89, 0xcf, //0x000001bd movq         %r9, %rdi
	0x48, 0xf7, 0xd7, //0x000001c0 notq         %rdi
	0x4c, 0x21, 0xff, //0x000001c3 andq         %r15, %rdi
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000001c6 movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf7, //0x000001d0 andq         %rsi, %rdi
	0x45, 0x31, 0xc0, //0x000001d3 xorl         %r8d, %r8d
	0x48, 0x01, 0xd7, //0x000001d6 addq         %rdx, %rdi
	0x41, 0x0f, 0x92, 0xc0, //0x000001d9 setb         %r8b
	0x48, 0x01, 0xff, //0x000001dd addq         %rdi, %rdi
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000001e0 movabsq      $6148914691236517205, %rdx
	0x48, 0x31, 0xd7, //0x000001ea xorq         %rdx, %rdi
	0x4c, 0x21, 0xcf, //0x000001ed andq         %r9, %rdi
	0x48, 0xf7, 0xd7, //0x000001f0 notq         %rdi
	0x48, 0x21, 0xfb, //0x000001f3 andq         %rdi, %rbx
	0x48, 0x85, 0xdb, //0x000001f6 testq        %rbx, %rbx
	0x0f, 0x84, 0x7a, 0xff, 0xff, 0xff, //0x000001f9 je           LBB0_8
	//0x000001ff LBB0_11
	0x48, 0x0f, 0xbc, 0xc3, //0x000001ff bsfq         %rbx, %rax
	0x48, 0x2b, 0x4d, 0xd0, //0x00000203 subq         $-48(%rbp), %rcx
	0x48, 0x8d, 0x7c, 0x01, 0x01, //0x00000207 leaq         $1(%rcx,%rax), %rdi
	0x48, 0x8b, 0x5d, 0xb8, //0x0000020c movq         $-72(%rbp), %rbx
	0x48, 0x8b, 0x75, 0xc0, //0x00000210 movq         $-64(%rbp), %rsi
	0xe9, 0x7c, 0x02, 0x00, 0x00, //0x00000214 jmp          LBB0_27
	//0x00000219 LBB0_12
	0x48, 0x8b, 0x4f, 0x08, //0x00000219 movq         $8(%rdi), %rcx
	0x48, 0x89, 0xc8, //0x0000021d movq         %rcx, %rax
	0x4c, 0x29, 0xd0, //0x00000220 subq         %r10, %rax
	0x0f, 0x84, 0x14, 0x05, 0x00, 0x00, //0x00000223 je           LBB0_55
	0x48, 0x89, 0x4d, 0xc8, //0x00000229 movq         %rcx, $-56(%rbp)
	0x48, 0x8b, 0x3f, //0x0000022d movq         (%rdi), %rdi
	0x4a, 0x8d, 0x0c, 0x17, //0x00000230 leaq         (%rdi,%r10), %rcx
	0x48, 0x83, 0xf8, 0x40, //0x00000234 cmpq         $64, %rax
	0x48, 0x89, 0x7d, 0xd0, //0x00000238 movq         %rdi, $-48(%rbp)
	0x0f, 0x82, 0x74, 0x05, 0x00, 0x00, //0x0000023c jb           LBB0_60
	0x41, 0x89, 0xc5, //0x00000242 movl         %eax, %r13d
	0x41, 0x83, 0xe5, 0x3f, //0x00000245 andl         $63, %r13d
	0x48, 0x8d, 0x50, 0xc0, //0x00000249 leaq         $-64(%rax), %rdx
	0x48, 0x83, 0xe2, 0xc0, //0x0000024d andq         $-64, %rdx
	0x4c, 0x01, 0xd2, //0x00000251 addq         %r10, %rdx
	0x48, 0x8d, 0x54, 0x17, 0x40, //0x00000254 leaq         $64(%rdi,%rdx), %rdx
	0x48, 0x89, 0x55, 0xb0, //0x00000259 movq         %rdx, $-80(%rbp)
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x0000025d movq         $-1, %r11
	0x45, 0x31, 0xc9, //0x00000264 xorl         %r9d, %r9d
	0xf3, 0x44, 0x0f, 0x6f, 0x05, 0x90, 0xfd, 0xff, 0xff, //0x00000267 movdqu       $-624(%rip), %xmm8  /* LCPI0_0+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0x98, 0xfd, 0xff, 0xff, //0x00000270 movdqu       $-616(%rip), %xmm1  /* LCPI0_1+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x15, 0xa0, 0xfd, 0xff, 0xff, //0x00000278 movdqu       $-608(%rip), %xmm2  /* LCPI0_2+0(%rip) */
	0x66, 0x0f, 0x76, 0xdb, //0x00000280 pcmpeqd      %xmm3, %xmm3
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000284 .p2align 4, 0x90
	//0x00000290 LBB0_15
	0xf3, 0x0f, 0x6f, 0x39, //0x00000290 movdqu       (%rcx), %xmm7
	0xf3, 0x0f, 0x6f, 0x71, 0x10, //0x00000294 movdqu       $16(%rcx), %xmm6
	0xf3, 0x0f, 0x6f, 0x69, 0x20, //0x00000299 movdqu       $32(%rcx), %xmm5
	0xf3, 0x0f, 0x6f, 0x61, 0x30, //0x0000029e movdqu       $48(%rcx), %xmm4
	0x66, 0x0f, 0x6f, 0xc7, //0x000002a3 movdqa       %xmm7, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc0, //0x000002a7 pcmpeqb      %xmm8, %xmm0
	0x66, 0x0f, 0xd7, 0xd8, //0x000002ac pmovmskb     %xmm0, %ebx
	0x66, 0x0f, 0x6f, 0xc6, //0x000002b0 movdqa       %xmm6, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc0, //0x000002b4 pcmpeqb      %xmm8, %xmm0
	0x66, 0x0f, 0xd7, 0xf0, //0x000002b9 pmovmskb     %xmm0, %esi
	0x66, 0x0f, 0x6f, 0xc5, //0x000002bd movdqa       %xmm5, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc0, //0x000002c1 pcmpeqb      %xmm8, %xmm0
	0x66, 0x0f, 0xd7, 0xd0, //0x000002c6 pmovmskb     %xmm0, %edx
	0x66, 0x0f, 0x6f, 0xc4, //0x000002ca movdqa       %xmm4, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc0, //0x000002ce pcmpeqb      %xmm8, %xmm0
	0x66, 0x44, 0x0f, 0xd7, 0xe0, //0x000002d3 pmovmskb     %xmm0, %r12d
	0x66, 0x0f, 0x6f, 0xc7, //0x000002d8 movdqa       %xmm7, %xmm0
	0x66, 0x0f, 0x74, 0xc1, //0x000002dc pcmpeqb      %xmm1, %xmm0
	0x66, 0x44, 0x0f, 0xd7, 0xf8, //0x000002e0 pmovmskb     %xmm0, %r15d
	0x66, 0x0f, 0x6f, 0xc6, //0x000002e5 movdqa       %xmm6, %xmm0
	0x66, 0x0f, 0x74, 0xc1, //0x000002e9 pcmpeqb      %xmm1, %xmm0
	0x66, 0x0f, 0xd7, 0xf8, //0x000002ed pmovmskb     %xmm0, %edi
	0x66, 0x0f, 0x6f, 0xc5, //0x000002f1 movdqa       %xmm5, %xmm0
	0x66, 0x0f, 0x74, 0xc1, //0x000002f5 pcmpeqb      %xmm1, %xmm0
	0x48, 0xc1, 0xe6, 0x10, //0x000002f9 shlq         $16, %rsi
	0x48, 0x09, 0xf3, //0x000002fd orq          %rsi, %rbx
	0x66, 0x0f, 0xd7, 0xf0, //0x00000300 pmovmskb     %xmm0, %esi
	0x66, 0x0f, 0x6f, 0xc4, //0x00000304 movdqa       %xmm4, %xmm0
	0x66, 0x0f, 0x74, 0xc1, //0x00000308 pcmpeqb      %xmm1, %xmm0
	0x48, 0xc1, 0xe2, 0x20, //0x0000030c shlq         $32, %rdx
	0x48, 0x09, 0xd3, //0x00000310 orq          %rdx, %rbx
	0x66, 0x0f, 0xd7, 0xd0, //0x00000313 pmovmskb     %xmm0, %edx
	0x66, 0x0f, 0x6f, 0xc2, //0x00000317 movdqa       %xmm2, %xmm0
	0x66, 0x0f, 0x64, 0xc7, //0x0000031b pcmpgtb      %xmm7, %xmm0
	0x66, 0x0f, 0x64, 0xfb, //0x0000031f pcmpgtb      %xmm3, %xmm7
	0x66, 0x0f, 0xdb, 0xf8, //0x00000323 pand         %xmm0, %xmm7
	0x48, 0xc1, 0xe7, 0x10, //0x00000327 shlq         $16, %rdi
	0x49, 0x09, 0xff, //0x0000032b orq          %rdi, %r15
	0x66, 0x44, 0x0f, 0xd7, 0xf7, //0x0000032e pmovmskb     %xmm7, %r14d
	0x66, 0x0f, 0x6f, 0xc2, //0x00000333 movdqa       %xmm2, %xmm0
	0x66, 0x0f, 0x64, 0xc6, //0x00000337 pcmpgtb      %xmm6, %xmm0
	0x66, 0x0f, 0x64, 0xf3, //0x0000033b pcmpgtb      %xmm3, %xmm6
	0x66, 0x0f, 0xdb, 0xf0, //0x0000033f pand         %xmm0, %xmm6
	0x48, 0xc1, 0xe6, 0x20, //0x00000343 shlq         $32, %rsi
	0x49, 0x09, 0xf7, //0x00000347 orq          %rsi, %r15
	0x66, 0x0f, 0xd7, 0xf6, //0x0000034a pmovmskb     %xmm6, %esi
	0x66, 0x0f, 0x6f, 0xc2, //0x0000034e movdqa       %xmm2, %xmm0
	0x66, 0x0f, 0x64, 0xc5, //0x00000352 pcmpgtb      %xmm5, %xmm0
	0x66, 0x0f, 0x64, 0xeb, //0x00000356 pcmpgtb      %xmm3, %xmm5
	0x66, 0x0f, 0xdb, 0xe8, //0x0000035a pand         %xmm0, %xmm5
	0x48, 0xc1, 0xe2, 0x30, //0x0000035e shlq         $48, %rdx
	0x49, 0x09, 0xd7, //0x00000362 orq          %rdx, %r15
	0x66, 0x0f, 0xd7, 0xd5, //0x00000365 pmovmskb     %xmm5, %edx
	0x66, 0x0f, 0x6f, 0xc2, //0x00000369 movdqa       %xmm2, %xmm0
	0x66, 0x0f, 0x64, 0xc4, //0x0000036d pcmpgtb      %xmm4, %xmm0
	0x66, 0x0f, 0x64, 0xe3, //0x00000371 pcmpgtb      %xmm3, %xmm4
	0x66, 0x0f, 0xdb, 0xe0, //0x00000375 pand         %xmm0, %xmm4
	0x48, 0xc1, 0xe6, 0x10, //0x00000379 shlq         $16, %rsi
	0x49, 0x09, 0xf6, //0x0000037d orq          %rsi, %r14
	0x66, 0x44, 0x0f, 0xd7, 0xc4, //0x00000380 pmovmskb     %xmm4, %r8d
	0x49, 0xc1, 0xe4, 0x30, //0x00000385 shlq         $48, %r12
	0x48, 0xc1, 0xe2, 0x20, //0x00000389 shlq         $32, %rdx
	0x49, 0x83, 0xfb, 0xff, //0x0000038d cmpq         $-1, %r11
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00000391 jne          LBB0_17
	0x4d, 0x85, 0xff, //0x00000397 testq        %r15, %r15
	0x0f, 0x85, 0x9c, 0x00, 0x00, 0x00, //0x0000039a jne          LBB0_22
	//0x000003a0 LBB0_17
	0x49, 0xc1, 0xe0, 0x30, //0x000003a0 shlq         $48, %r8
	0x49, 0x09, 0xd6, //0x000003a4 orq          %rdx, %r14
	0x4c, 0x09, 0xe3, //0x000003a7 orq          %r12, %rbx
	0x4c, 0x89, 0xfa, //0x000003aa movq         %r15, %rdx
	0x4c, 0x09, 0xca, //0x000003ad orq          %r9, %rdx
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x000003b0 jne          LBB0_21
	0x4d, 0x09, 0xc6, //0x000003b6 orq          %r8, %r14
	0x48, 0x85, 0xdb, //0x000003b9 testq        %rbx, %rbx
	0x0f, 0x85, 0x8d, 0x00, 0x00, 0x00, //0x000003bc jne          LBB0_23
	//0x000003c2 LBB0_19
	0x4d, 0x85, 0xf6, //0x000003c2 testq        %r14, %r14
	0x0f, 0x85, 0x8f, 0x01, 0x00, 0x00, //0x000003c5 jne          LBB0_35
	0x48, 0x83, 0xc0, 0xc0, //0x000003cb addq         $-64, %rax
	0x48, 0x83, 0xc1, 0x40, //0x000003cf addq         $64, %rcx
	0x48, 0x83, 0xf8, 0x3f, //0x000003d3 cmpq         $63, %rax
	0x0f, 0x87, 0xb3, 0xfe, 0xff, 0xff, //0x000003d7 ja           LBB0_15
	0xe9, 0x90, 0x01, 0x00, 0x00, //0x000003dd jmp          LBB0_37
	//0x000003e2 LBB0_21
	0x4c, 0x89, 0xca, //0x000003e2 movq         %r9, %rdx
	0x48, 0xf7, 0xd2, //0x000003e5 notq         %rdx
	0x4c, 0x21, 0xfa, //0x000003e8 andq         %r15, %rdx
	0x4c, 0x8d, 0x24, 0x12, //0x000003eb leaq         (%rdx,%rdx), %r12
	0x4d, 0x09, 0xcc, //0x000003ef orq          %r9, %r12
	0x4c, 0x89, 0xe7, //0x000003f2 movq         %r12, %rdi
	0x48, 0xf7, 0xd7, //0x000003f5 notq         %rdi
	0x4c, 0x21, 0xff, //0x000003f8 andq         %r15, %rdi
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000003fb movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf7, //0x00000405 andq         %rsi, %rdi
	0x45, 0x31, 0xc9, //0x00000408 xorl         %r9d, %r9d
	0x48, 0x01, 0xd7, //0x0000040b addq         %rdx, %rdi
	0x41, 0x0f, 0x92, 0xc1, //0x0000040e setb         %r9b
	0x48, 0x01, 0xff, //0x00000412 addq         %rdi, %rdi
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000415 movabsq      $6148914691236517205, %rdx
	0x48, 0x31, 0xd7, //0x0000041f xorq         %rdx, %rdi
	0x4c, 0x21, 0xe7, //0x00000422 andq         %r12, %rdi
	0x48, 0xf7, 0xd7, //0x00000425 notq         %rdi
	0x48, 0x21, 0xfb, //0x00000428 andq         %rdi, %rbx
	0x4d, 0x09, 0xc6, //0x0000042b orq          %r8, %r14
	0x48, 0x85, 0xdb, //0x0000042e testq        %rbx, %rbx
	0x0f, 0x84, 0x8b, 0xff, 0xff, 0xff, //0x00000431 je           LBB0_19
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x00000437 jmp          LBB0_23
	//0x0000043c LBB0_22
	0x48, 0x89, 0xce, //0x0000043c movq         %rcx, %rsi
	0x48, 0x2b, 0x75, 0xd0, //0x0000043f subq         $-48(%rbp), %rsi
	0x4d, 0x0f, 0xbc, 0xdf, //0x00000443 bsfq         %r15, %r11
	0x49, 0x01, 0xf3, //0x00000447 addq         %rsi, %r11
	0xe9, 0x51, 0xff, 0xff, 0xff, //0x0000044a jmp          LBB0_17
	//0x0000044f LBB0_23
	0x48, 0x0f, 0xbc, 0xc3, //0x0000044f bsfq         %rbx, %rax
	0x4d, 0x85, 0xf6, //0x00000453 testq        %r14, %r14
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00000456 je           LBB0_25
	0x49, 0x0f, 0xbc, 0xd6, //0x0000045c bsfq         %r14, %rdx
	0x48, 0x8b, 0x5d, 0xb8, //0x00000460 movq         $-72(%rbp), %rbx
	0x48, 0x8b, 0x75, 0xc0, //0x00000464 movq         $-64(%rbp), %rsi
	0x48, 0x39, 0xc2, //0x00000468 cmpq         %rax, %rdx
	0x0f, 0x83, 0x1b, 0x00, 0x00, 0x00, //0x0000046b jae          LBB0_26
	0xe9, 0x91, 0x04, 0x00, 0x00, //0x00000471 jmp          LBB0_80
	//0x00000476 LBB0_25
	0xba, 0x40, 0x00, 0x00, 0x00, //0x00000476 movl         $64, %edx
	0x48, 0x8b, 0x5d, 0xb8, //0x0000047b movq         $-72(%rbp), %rbx
	0x48, 0x8b, 0x75, 0xc0, //0x0000047f movq         $-64(%rbp), %rsi
	0x48, 0x39, 0xc2, //0x00000483 cmpq         %rax, %rdx
	0x0f, 0x82, 0x7b, 0x04, 0x00, 0x00, //0x00000486 jb           LBB0_80
	//0x0000048c LBB0_26
	0x48, 0x2b, 0x4d, 0xd0, //0x0000048c subq         $-48(%rbp), %rcx
	0x48, 0x8d, 0x7c, 0x01, 0x01, //0x00000490 leaq         $1(%rcx,%rax), %rdi
	//0x00000495 LBB0_27
	0x48, 0x85, 0xff, //0x00000495 testq        %rdi, %rdi
	0x0f, 0x88, 0x70, 0x04, 0x00, 0x00, //0x00000498 js           LBB0_81
	0x48, 0x89, 0x3e, //0x0000049e movq         %rdi, (%rsi)
	0x4c, 0x89, 0x53, 0x10, //0x000004a1 movq         %r10, $16(%rbx)
	0x48, 0xc7, 0x03, 0x07, 0x00, 0x00, 0x00, //0x000004a5 movq         $7, (%rbx)
	0x49, 0x39, 0xfb, //0x000004ac cmpq         %rdi, %r11
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000004af movq         $-1, %rax
	0x49, 0x0f, 0x4c, 0xc3, //0x000004b6 cmovlq       %r11, %rax
	0x48, 0x89, 0x43, 0x18, //0x000004ba movq         %rax, $24(%rbx)
	0xe9, 0x55, 0x04, 0x00, 0x00, //0x000004be jmp          LBB0_83
	//0x000004c3 LBB0_29
	0x48, 0x8b, 0x4d, 0xb0, //0x000004c3 movq         $-80(%rbp), %rcx
	0x4c, 0x89, 0xe8, //0x000004c7 movq         %r13, %rax
	0x48, 0x83, 0xf8, 0x20, //0x000004ca cmpq         $32, %rax
	0x0f, 0x82, 0x89, 0x02, 0x00, 0x00, //0x000004ce jb           LBB0_57
	//0x000004d4 LBB0_30
	0xf3, 0x0f, 0x6f, 0x01, //0x000004d4 movdqu       (%rcx), %xmm0
	0xf3, 0x0f, 0x6f, 0x49, 0x10, //0x000004d8 movdqu       $16(%rcx), %xmm1
	0xf3, 0x0f, 0x6f, 0x15, 0x1b, 0xfb, 0xff, 0xff, //0x000004dd movdqu       $-1253(%rip), %xmm2  /* LCPI0_0+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x1d, 0x23, 0xfb, 0xff, 0xff, //0x000004e5 movdqu       $-1245(%rip), %xmm3  /* LCPI0_1+0(%rip) */
	0x66, 0x0f, 0x6f, 0xe0, //0x000004ed movdqa       %xmm0, %xmm4
	0x66, 0x0f, 0x74, 0xe2, //0x000004f1 pcmpeqb      %xmm2, %xmm4
	0x66, 0x0f, 0xd7, 0xd4, //0x000004f5 pmovmskb     %xmm4, %edx
	0x66, 0x0f, 0x74, 0xd1, //0x000004f9 pcmpeqb      %xmm1, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xfa, //0x000004fd pmovmskb     %xmm2, %r15d
	0x66, 0x0f, 0x74, 0xc3, //0x00000502 pcmpeqb      %xmm3, %xmm0
	0x66, 0x44, 0x0f, 0xd7, 0xf0, //0x00000506 pmovmskb     %xmm0, %r14d
	0x66, 0x0f, 0x74, 0xcb, //0x0000050b pcmpeqb      %xmm3, %xmm1
	0x66, 0x0f, 0xd7, 0xf9, //0x0000050f pmovmskb     %xmm1, %edi
	0x49, 0xc1, 0xe7, 0x10, //0x00000513 shlq         $16, %r15
	0x48, 0xc1, 0xe7, 0x10, //0x00000517 shlq         $16, %rdi
	0x49, 0x09, 0xfe, //0x0000051b orq          %rdi, %r14
	0x49, 0x83, 0xfb, 0xff, //0x0000051e cmpq         $-1, %r11
	0x0f, 0x85, 0x1b, 0x01, 0x00, 0x00, //0x00000522 jne          LBB0_43
	0x4d, 0x85, 0xf6, //0x00000528 testq        %r14, %r14
	0x48, 0x8b, 0x5d, 0xb8, //0x0000052b movq         $-72(%rbp), %rbx
	0x48, 0x8b, 0x75, 0xc0, //0x0000052f movq         $-64(%rbp), %rsi
	0x0f, 0x85, 0xee, 0x03, 0x00, 0x00, //0x00000533 jne          LBB0_84
	0x49, 0x09, 0xd7, //0x00000539 orq          %rdx, %r15
	0x4c, 0x89, 0xf2, //0x0000053c movq         %r14, %rdx
	0x4c, 0x09, 0xc2, //0x0000053f orq          %r8, %rdx
	0x0f, 0x85, 0x12, 0x01, 0x00, 0x00, //0x00000542 jne          LBB0_44
	//0x00000548 LBB0_33
	0x4d, 0x85, 0xff, //0x00000548 testq        %r15, %r15
	0x0f, 0x84, 0x4b, 0x01, 0x00, 0x00, //0x0000054b je           LBB0_45
	//0x00000551 LBB0_34
	0x49, 0x0f, 0xbc, 0xc7, //0x00000551 bsfq         %r15, %rax
	0xe9, 0x32, 0xff, 0xff, 0xff, //0x00000555 jmp          LBB0_26
	//0x0000055a LBB0_35
	0x48, 0xc7, 0xc7, 0xfe, 0xff, 0xff, 0xff, //0x0000055a movq         $-2, %rdi
	//0x00000561 LBB0_36
	0x4c, 0x8b, 0x55, 0xc8, //0x00000561 movq         $-56(%rbp), %r10
	0x48, 0x8b, 0x5d, 0xb8, //0x00000565 movq         $-72(%rbp), %rbx
	0x48, 0x8b, 0x75, 0xc0, //0x00000569 movq         $-64(%rbp), %rsi
	0xe9, 0xa0, 0x03, 0x00, 0x00, //0x0000056d jmp          LBB0_82
	//0x00000572 LBB0_37
	0x48, 0x8b, 0x4d, 0xb0, //0x00000572 movq         $-80(%rbp), %rcx
	0x4c, 0x89, 0xe8, //0x00000576 movq         %r13, %rax
	0x48, 0x8b, 0x5d, 0xb8, //0x00000579 movq         $-72(%rbp), %rbx
	0x48, 0x8b, 0x75, 0xc0, //0x0000057d movq         $-64(%rbp), %rsi
	0x48, 0x83, 0xf8, 0x20, //0x00000581 cmpq         $32, %rax
	0x0f, 0x82, 0xc0, 0x02, 0x00, 0x00, //0x00000585 jb           LBB0_68
	//0x0000058b LBB0_38
	0xf3, 0x0f, 0x6f, 0x01, //0x0000058b movdqu       (%rcx), %xmm0
	0xf3, 0x0f, 0x6f, 0x49, 0x10, //0x0000058f movdqu       $16(%rcx), %xmm1
	0xf3, 0x0f, 0x6f, 0x15, 0x64, 0xfa, 0xff, 0xff, //0x00000594 movdqu       $-1436(%rip), %xmm2  /* LCPI0_0+0(%rip) */
	0x66, 0x0f, 0x6f, 0xd8, //0x0000059c movdqa       %xmm0, %xmm3
	0x66, 0x0f, 0x74, 0xda, //0x000005a0 pcmpeqb      %xmm2, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xe3, //0x000005a4 pmovmskb     %xmm3, %r12d
	0x66, 0x0f, 0x74, 0xd1, //0x000005a9 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xfa, //0x000005ad pmovmskb     %xmm2, %edi
	0xf3, 0x0f, 0x6f, 0x15, 0x57, 0xfa, 0xff, 0xff, //0x000005b1 movdqu       $-1449(%rip), %xmm2  /* LCPI0_1+0(%rip) */
	0x66, 0x0f, 0x6f, 0xd8, //0x000005b9 movdqa       %xmm0, %xmm3
	0x66, 0x0f, 0x74, 0xda, //0x000005bd pcmpeqb      %xmm2, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xfb, //0x000005c1 pmovmskb     %xmm3, %r15d
	0x66, 0x0f, 0x74, 0xd1, //0x000005c6 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x000005ca pmovmskb     %xmm2, %edx
	0xf3, 0x0f, 0x6f, 0x15, 0x4a, 0xfa, 0xff, 0xff, //0x000005ce movdqu       $-1462(%rip), %xmm2  /* LCPI0_2+0(%rip) */
	0x66, 0x0f, 0x6f, 0xda, //0x000005d6 movdqa       %xmm2, %xmm3
	0x66, 0x0f, 0x64, 0xd8, //0x000005da pcmpgtb      %xmm0, %xmm3
	0x66, 0x0f, 0x76, 0xe4, //0x000005de pcmpeqd      %xmm4, %xmm4
	0x66, 0x0f, 0x64, 0xc4, //0x000005e2 pcmpgtb      %xmm4, %xmm0
	0x66, 0x0f, 0xdb, 0xc3, //0x000005e6 pand         %xmm3, %xmm0
	0x66, 0x0f, 0x64, 0xd1, //0x000005ea pcmpgtb      %xmm1, %xmm2
	0x66, 0x0f, 0x64, 0xcc, //0x000005ee pcmpgtb      %xmm4, %xmm1
	0x66, 0x0f, 0xdb, 0xca, //0x000005f2 pand         %xmm2, %xmm1
	0x66, 0x44, 0x0f, 0xd7, 0xf1, //0x000005f6 pmovmskb     %xmm1, %r14d
	0x48, 0xc1, 0xe7, 0x10, //0x000005fb shlq         $16, %rdi
	0x48, 0xc1, 0xe2, 0x10, //0x000005ff shlq         $16, %rdx
	0x49, 0x09, 0xd7, //0x00000603 orq          %rdx, %r15
	0x49, 0x83, 0xfb, 0xff, //0x00000606 cmpq         $-1, %r11
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x0000060a jne          LBB0_40
	0x4d, 0x85, 0xff, //0x00000610 testq        %r15, %r15
	0x0f, 0x85, 0x30, 0x03, 0x00, 0x00, //0x00000613 jne          LBB0_85
	//0x00000619 LBB0_40
	0x66, 0x44, 0x0f, 0xd7, 0xc0, //0x00000619 pmovmskb     %xmm0, %r8d
	0x4c, 0x09, 0xe7, //0x0000061e orq          %r12, %rdi
	0x4c, 0x89, 0xfa, //0x00000621 movq         %r15, %rdx
	0x4c, 0x09, 0xca, //0x00000624 orq          %r9, %rdx
	0x0f, 0x85, 0xa2, 0x01, 0x00, 0x00, //0x00000627 jne          LBB0_61
	0x49, 0xc1, 0xe6, 0x10, //0x0000062d shlq         $16, %r14
	0x48, 0x85, 0xff, //0x00000631 testq        %rdi, %rdi
	0x0f, 0x84, 0xdd, 0x01, 0x00, 0x00, //0x00000634 je           LBB0_62
	//0x0000063a LBB0_42
	0x48, 0x0f, 0xbc, 0xd7, //0x0000063a bsfq         %rdi, %rdx
	0xe9, 0xd9, 0x01, 0x00, 0x00, //0x0000063e jmp          LBB0_63
	//0x00000643 LBB0_43
	0x48, 0x8b, 0x5d, 0xb8, //0x00000643 movq         $-72(%rbp), %rbx
	0x48, 0x8b, 0x75, 0xc0, //0x00000647 movq         $-64(%rbp), %rsi
	0x49, 0x09, 0xd7, //0x0000064b orq          %rdx, %r15
	0x4c, 0x89, 0xf2, //0x0000064e movq         %r14, %rdx
	0x4c, 0x09, 0xc2, //0x00000651 orq          %r8, %rdx
	0x0f, 0x84, 0xee, 0xfe, 0xff, 0xff, //0x00000654 je           LBB0_33
	//0x0000065a LBB0_44
	0x44, 0x89, 0xc2, //0x0000065a movl         %r8d, %edx
	0xf7, 0xd2, //0x0000065d notl         %edx
	0x44, 0x21, 0xf2, //0x0000065f andl         %r14d, %edx
	0x44, 0x8d, 0x0c, 0x12, //0x00000662 leal         (%rdx,%rdx), %r9d
	0x45, 0x09, 0xc1, //0x00000666 orl          %r8d, %r9d
	0xbf, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000669 movl         $2863311530, %edi
	0x44, 0x31, 0xcf, //0x0000066e xorl         %r9d, %edi
	0x44, 0x21, 0xf7, //0x00000671 andl         %r14d, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000674 andl         $-1431655766, %edi
	0x45, 0x31, 0xc0, //0x0000067a xorl         %r8d, %r8d
	0x01, 0xd7, //0x0000067d addl         %edx, %edi
	0x41, 0x0f, 0x92, 0xc0, //0x0000067f setb         %r8b
	0x01, 0xff, //0x00000683 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00000685 xorl         $1431655765, %edi
	0x44, 0x21, 0xcf, //0x0000068b andl         %r9d, %edi
	0xf7, 0xd7, //0x0000068e notl         %edi
	0x41, 0x21, 0xff, //0x00000690 andl         %edi, %r15d
	0x4d, 0x85, 0xff, //0x00000693 testq        %r15, %r15
	0x0f, 0x85, 0xb5, 0xfe, 0xff, 0xff, //0x00000696 jne          LBB0_34
	//0x0000069c LBB0_45
	0x48, 0x83, 0xc1, 0x20, //0x0000069c addq         $32, %rcx
	0x48, 0x83, 0xc0, 0xe0, //0x000006a0 addq         $-32, %rax
	0x4d, 0x85, 0xc0, //0x000006a4 testq        %r8, %r8
	0x0f, 0x85, 0xc1, 0x00, 0x00, 0x00, //0x000006a7 jne          LBB0_58
	//0x000006ad LBB0_46
	0x4d, 0x89, 0xd8, //0x000006ad movq         %r11, %r8
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x000006b0 movq         $-1, %rdi
	0x48, 0x85, 0xc0, //0x000006b7 testq        %rax, %rax
	0x0f, 0x84, 0x4e, 0x02, 0x00, 0x00, //0x000006ba je           LBB0_81
	//0x000006c0 LBB0_47
	0x4c, 0x8b, 0x75, 0xd0, //0x000006c0 movq         $-48(%rbp), %r14
	0x49, 0xf7, 0xd6, //0x000006c4 notq         %r14
	//0x000006c7 LBB0_48
	0x4c, 0x8d, 0x79, 0x01, //0x000006c7 leaq         $1(%rcx), %r15
	0x0f, 0xb6, 0x11, //0x000006cb movzbl       (%rcx), %edx
	0x80, 0xfa, 0x22, //0x000006ce cmpb         $34, %dl
	0x0f, 0x84, 0x5a, 0x00, 0x00, 0x00, //0x000006d1 je           LBB0_53
	0x4c, 0x8d, 0x48, 0xff, //0x000006d7 leaq         $-1(%rax), %r9
	0x80, 0xfa, 0x5c, //0x000006db cmpb         $92, %dl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x000006de je           LBB0_51
	0x4c, 0x89, 0xc8, //0x000006e4 movq         %r9, %rax
	0x4c, 0x89, 0xf9, //0x000006e7 movq         %r15, %rcx
	0x4d, 0x85, 0xc9, //0x000006ea testq        %r9, %r9
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x000006ed jne          LBB0_48
	0xe9, 0x16, 0x02, 0x00, 0x00, //0x000006f3 jmp          LBB0_81
	//0x000006f8 LBB0_51
	0x4d, 0x85, 0xc9, //0x000006f8 testq        %r9, %r9
	0x0f, 0x84, 0x60, 0xfe, 0xff, 0xff, //0x000006fb je           LBB0_36
	0x4d, 0x01, 0xf7, //0x00000701 addq         %r14, %r15
	0x49, 0x83, 0xf8, 0xff, //0x00000704 cmpq         $-1, %r8
	0x4d, 0x0f, 0x44, 0xdf, //0x00000708 cmoveq       %r15, %r11
	0x4d, 0x0f, 0x44, 0xc7, //0x0000070c cmoveq       %r15, %r8
	0x48, 0x83, 0xc1, 0x02, //0x00000710 addq         $2, %rcx
	0x48, 0x83, 0xc0, 0xfe, //0x00000714 addq         $-2, %rax
	0x49, 0x89, 0xc1, //0x00000718 movq         %rax, %r9
	0x48, 0x8b, 0x5d, 0xb8, //0x0000071b movq         $-72(%rbp), %rbx
	0x48, 0x8b, 0x75, 0xc0, //0x0000071f movq         $-64(%rbp), %rsi
	0x4d, 0x85, 0xc9, //0x00000723 testq        %r9, %r9
	0x0f, 0x85, 0x9b, 0xff, 0xff, 0xff, //0x00000726 jne          LBB0_48
	0xe9, 0xdd, 0x01, 0x00, 0x00, //0x0000072c jmp          LBB0_81
	//0x00000731 LBB0_53
	0x4c, 0x2b, 0x7d, 0xd0, //0x00000731 subq         $-48(%rbp), %r15
	0x4c, 0x89, 0xff, //0x00000735 movq         %r15, %rdi
	0xe9, 0x58, 0xfd, 0xff, 0xff, //0x00000738 jmp          LBB0_27
	//0x0000073d LBB0_55
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x0000073d movq         $-1, %rdi
	0xe9, 0xc9, 0x01, 0x00, 0x00, //0x00000744 jmp          LBB0_82
	//0x00000749 LBB0_56
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00000749 movq         $-1, %r11
	0x45, 0x31, 0xc0, //0x00000750 xorl         %r8d, %r8d
	0x48, 0x83, 0xf8, 0x20, //0x00000753 cmpq         $32, %rax
	0x0f, 0x83, 0x77, 0xfd, 0xff, 0xff, //0x00000757 jae          LBB0_30
	//0x0000075d LBB0_57
	0x48, 0x8b, 0x5d, 0xb8, //0x0000075d movq         $-72(%rbp), %rbx
	0x48, 0x8b, 0x75, 0xc0, //0x00000761 movq         $-64(%rbp), %rsi
	0x4d, 0x85, 0xc0, //0x00000765 testq        %r8, %r8
	0x0f, 0x84, 0x3f, 0xff, 0xff, 0xff, //0x00000768 je           LBB0_46
	//0x0000076e LBB0_58
	0x48, 0x85, 0xc0, //0x0000076e testq        %rax, %rax
	0x0f, 0x84, 0x25, 0x02, 0x00, 0x00, //0x00000771 je           LBB0_88
	0x4c, 0x8b, 0x45, 0xd0, //0x00000777 movq         $-48(%rbp), %r8
	0x49, 0xf7, 0xd0, //0x0000077b notq         %r8
	0x49, 0x01, 0xc8, //0x0000077e addq         %rcx, %r8
	0x49, 0x83, 0xfb, 0xff, //0x00000781 cmpq         $-1, %r11
	0x4c, 0x89, 0xda, //0x00000785 movq         %r11, %rdx
	0x49, 0x0f, 0x44, 0xd0, //0x00000788 cmoveq       %r8, %rdx
	0x4d, 0x0f, 0x45, 0xc3, //0x0000078c cmovneq      %r11, %r8
	0x48, 0xff, 0xc1, //0x00000790 incq         %rcx
	0x48, 0xff, 0xc8, //0x00000793 decq         %rax
	0x49, 0x89, 0xd3, //0x00000796 movq         %rdx, %r11
	0x48, 0x8b, 0x5d, 0xb8, //0x00000799 movq         $-72(%rbp), %rbx
	0x48, 0x8b, 0x75, 0xc0, //0x0000079d movq         $-64(%rbp), %rsi
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x000007a1 movq         $-1, %rdi
	0x48, 0x85, 0xc0, //0x000007a8 testq        %rax, %rax
	0x0f, 0x85, 0x0f, 0xff, 0xff, 0xff, //0x000007ab jne          LBB0_47
	0xe9, 0x58, 0x01, 0x00, 0x00, //0x000007b1 jmp          LBB0_81
	//0x000007b6 LBB0_60
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000007b6 movq         $-1, %r11
	0x45, 0x31, 0xc9, //0x000007bd xorl         %r9d, %r9d
	0x48, 0x83, 0xf8, 0x20, //0x000007c0 cmpq         $32, %rax
	0x0f, 0x83, 0xc1, 0xfd, 0xff, 0xff, //0x000007c4 jae          LBB0_38
	0xe9, 0x7c, 0x00, 0x00, 0x00, //0x000007ca jmp          LBB0_68
	//0x000007cf LBB0_61
	0x45, 0x89, 0xcd, //0x000007cf movl         %r9d, %r13d
	0x41, 0xf7, 0xd5, //0x000007d2 notl         %r13d
	0x45, 0x21, 0xfd, //0x000007d5 andl         %r15d, %r13d
	0x47, 0x8d, 0x64, 0x2d, 0x00, //0x000007d8 leal         (%r13,%r13), %r12d
	0x45, 0x09, 0xcc, //0x000007dd orl          %r9d, %r12d
	0xba, 0xaa, 0xaa, 0xaa, 0xaa, //0x000007e0 movl         $2863311530, %edx
	0x44, 0x31, 0xe2, //0x000007e5 xorl         %r12d, %edx
	0x44, 0x21, 0xfa, //0x000007e8 andl         %r15d, %edx
	0x81, 0xe2, 0xaa, 0xaa, 0xaa, 0xaa, //0x000007eb andl         $-1431655766, %edx
	0x45, 0x31, 0xc9, //0x000007f1 xorl         %r9d, %r9d
	0x44, 0x01, 0xea, //0x000007f4 addl         %r13d, %edx
	0x41, 0x0f, 0x92, 0xc1, //0x000007f7 setb         %r9b
	0x01, 0xd2, //0x000007fb addl         %edx, %edx
	0x81, 0xf2, 0x55, 0x55, 0x55, 0x55, //0x000007fd xorl         $1431655765, %edx
	0x44, 0x21, 0xe2, //0x00000803 andl         %r12d, %edx
	0xf7, 0xd2, //0x00000806 notl         %edx
	0x21, 0xd7, //0x00000808 andl         %edx, %edi
	0x49, 0xc1, 0xe6, 0x10, //0x0000080a shlq         $16, %r14
	0x48, 0x85, 0xff, //0x0000080e testq        %rdi, %rdi
	0x0f, 0x85, 0x23, 0xfe, 0xff, 0xff, //0x00000811 jne          LBB0_42
	//0x00000817 LBB0_62
	0xba, 0x40, 0x00, 0x00, 0x00, //0x00000817 movl         $64, %edx
	//0x0000081c LBB0_63
	0x4d, 0x09, 0xc6, //0x0000081c orq          %r8, %r14
	0x48, 0x85, 0xff, //0x0000081f testq        %rdi, %rdi
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00000822 je           LBB0_66
	0x4d, 0x85, 0xf6, //0x00000828 testq        %r14, %r14
	0x0f, 0x84, 0xab, 0x00, 0x00, 0x00, //0x0000082b je           LBB0_76
	0x49, 0x0f, 0xbc, 0xc6, //0x00000831 bsfq         %r14, %rax
	0xe9, 0xa7, 0x00, 0x00, 0x00, //0x00000835 jmp          LBB0_77
	//0x0000083a LBB0_66
	0x4d, 0x85, 0xf6, //0x0000083a testq        %r14, %r14
	0x0f, 0x85, 0xc4, 0x00, 0x00, 0x00, //0x0000083d jne          LBB0_80
	0x48, 0x83, 0xc1, 0x20, //0x00000843 addq         $32, %rcx
	0x48, 0x83, 0xc0, 0xe0, //0x00000847 addq         $-32, %rax
	//0x0000084b LBB0_68
	0x4d, 0x85, 0xc9, //0x0000084b testq        %r9, %r9
	0x0f, 0x85, 0x08, 0x01, 0x00, 0x00, //0x0000084e jne          LBB0_86
	0x4d, 0x89, 0xd8, //0x00000854 movq         %r11, %r8
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00000857 movq         $-1, %rdi
	0x48, 0x85, 0xc0, //0x0000085e testq        %rax, %rax
	0x0f, 0x84, 0xa7, 0x00, 0x00, 0x00, //0x00000861 je           LBB0_81
	//0x00000867 LBB0_70
	0x0f, 0xb6, 0x11, //0x00000867 movzbl       (%rcx), %edx
	0x80, 0xfa, 0x22, //0x0000086a cmpb         $34, %dl
	0x0f, 0x84, 0x85, 0x00, 0x00, 0x00, //0x0000086d je           LBB0_79
	0x80, 0xfa, 0x5c, //0x00000873 cmpb         $92, %dl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00000876 je           LBB0_74
	0x80, 0xfa, 0x20, //0x0000087c cmpb         $32, %dl
	0x0f, 0x82, 0x82, 0x00, 0x00, 0x00, //0x0000087f jb           LBB0_80
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000885 movq         $-1, %r9
	0xba, 0x01, 0x00, 0x00, 0x00, //0x0000088c movl         $1, %edx
	0x48, 0x01, 0xd1, //0x00000891 addq         %rdx, %rcx
	0x4c, 0x01, 0xc8, //0x00000894 addq         %r9, %rax
	0x0f, 0x85, 0xca, 0xff, 0xff, 0xff, //0x00000897 jne          LBB0_70
	0xe9, 0x6c, 0x00, 0x00, 0x00, //0x0000089d jmp          LBB0_81
	//0x000008a2 LBB0_74
	0x48, 0x83, 0xf8, 0x01, //0x000008a2 cmpq         $1, %rax
	0x0f, 0x84, 0x62, 0x00, 0x00, 0x00, //0x000008a6 je           LBB0_81
	0x48, 0x89, 0xca, //0x000008ac movq         %rcx, %rdx
	0x48, 0x2b, 0x55, 0xd0, //0x000008af subq         $-48(%rbp), %rdx
	0x49, 0x83, 0xf8, 0xff, //0x000008b3 cmpq         $-1, %r8
	0x4c, 0x0f, 0x44, 0xda, //0x000008b7 cmoveq       %rdx, %r11
	0x4c, 0x0f, 0x44, 0xc2, //0x000008bb cmoveq       %rdx, %r8
	0x49, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x000008bf movq         $-2, %r9
	0xba, 0x02, 0x00, 0x00, 0x00, //0x000008c6 movl         $2, %edx
	0x48, 0x01, 0xd1, //0x000008cb addq         %rdx, %rcx
	0x4c, 0x01, 0xc8, //0x000008ce addq         %r9, %rax
	0x0f, 0x85, 0x90, 0xff, 0xff, 0xff, //0x000008d1 jne          LBB0_70
	0xe9, 0x32, 0x00, 0x00, 0x00, //0x000008d7 jmp          LBB0_81
	//0x000008dc LBB0_76
	0xb8, 0x40, 0x00, 0x00, 0x00, //0x000008dc movl         $64, %eax
	//0x000008e1 LBB0_77
	0x48, 0x39, 0xd0, //0x000008e1 cmpq         %rdx, %rax
	0x0f, 0x82, 0x1d, 0x00, 0x00, 0x00, //0x000008e4 jb           LBB0_80
	0x48, 0x2b, 0x4d, 0xd0, //0x000008ea subq         $-48(%rbp), %rcx
	0x48, 0x8d, 0x7c, 0x11, 0x01, //0x000008ee leaq         $1(%rcx,%rdx), %rdi
	0xe9, 0x9d, 0xfb, 0xff, 0xff, //0x000008f3 jmp          LBB0_27
	//0x000008f8 LBB0_79
	0x48, 0x2b, 0x4d, 0xd0, //0x000008f8 subq         $-48(%rbp), %rcx
	0x48, 0xff, 0xc1, //0x000008fc incq         %rcx
	0x48, 0x89, 0xcf, //0x000008ff movq         %rcx, %rdi
	0xe9, 0x8e, 0xfb, 0xff, 0xff, //0x00000902 jmp          LBB0_27
	//0x00000907 LBB0_80
	0x48, 0xc7, 0xc7, 0xfe, 0xff, 0xff, 0xff, //0x00000907 movq         $-2, %rdi
	//0x0000090e LBB0_81
	0x4c, 0x8b, 0x55, 0xc8, //0x0000090e movq         $-56(%rbp), %r10
	//0x00000912 LBB0_82
	0x4c, 0x89, 0x16, //0x00000912 movq         %r10, (%rsi)
	0x48, 0x89, 0x3b, //0x00000915 movq         %rdi, (%rbx)
	//0x00000918 LBB0_83
	0x48, 0x83, 0xc4, 0x28, //0x00000918 addq         $40, %rsp
	0x5b, //0x0000091c popq         %rbx
	0x41, 0x5c, //0x0000091d popq         %r12
	0x41, 0x5d, //0x0000091f popq         %r13
	0x41, 0x5e, //0x00000921 popq         %r14
	0x41, 0x5f, //0x00000923 popq         %r15
	0x5d, //0x00000925 popq         %rbp
	0xc3, //0x00000926 retq         
	//0x00000927 LBB0_84
	0x48, 0x89, 0xcf, //0x00000927 movq         %rcx, %rdi
	0x48, 0x2b, 0x7d, 0xd0, //0x0000092a subq         $-48(%rbp), %rdi
	0x4d, 0x0f, 0xbc, 0xde, //0x0000092e bsfq         %r14, %r11
	0x49, 0x01, 0xfb, //0x00000932 addq         %rdi, %r11
	0x49, 0x09, 0xd7, //0x00000935 orq          %rdx, %r15
	0x4c, 0x89, 0xf2, //0x00000938 movq         %r14, %rdx
	0x4c, 0x09, 0xc2, //0x0000093b orq          %r8, %rdx
	0x0f, 0x84, 0x04, 0xfc, 0xff, 0xff, //0x0000093e je           LBB0_33
	0xe9, 0x11, 0xfd, 0xff, 0xff, //0x00000944 jmp          LBB0_44
	//0x00000949 LBB0_85
	0x48, 0x89, 0xca, //0x00000949 movq         %rcx, %rdx
	0x48, 0x2b, 0x55, 0xd0, //0x0000094c subq         $-48(%rbp), %rdx
	0x4d, 0x0f, 0xbc, 0xdf, //0x00000950 bsfq         %r15, %r11
	0x49, 0x01, 0xd3, //0x00000954 addq         %rdx, %r11
	0xe9, 0xbd, 0xfc, 0xff, 0xff, //0x00000957 jmp          LBB0_40
	//0x0000095c LBB0_86
	0x48, 0x85, 0xc0, //0x0000095c testq        %rax, %rax
	0x0f, 0x84, 0x43, 0x00, 0x00, 0x00, //0x0000095f je           LBB0_89
	0x4c, 0x8b, 0x45, 0xd0, //0x00000965 movq         $-48(%rbp), %r8
	0x49, 0xf7, 0xd0, //0x00000969 notq         %r8
	0x49, 0x01, 0xc8, //0x0000096c addq         %rcx, %r8
	0x49, 0x83, 0xfb, 0xff, //0x0000096f cmpq         $-1, %r11
	0x4c, 0x89, 0xda, //0x00000973 movq         %r11, %rdx
	0x49, 0x0f, 0x44, 0xd0, //0x00000976 cmoveq       %r8, %rdx
	0x4d, 0x0f, 0x45, 0xc3, //0x0000097a cmovneq      %r11, %r8
	0x48, 0xff, 0xc1, //0x0000097e incq         %rcx
	0x48, 0xff, 0xc8, //0x00000981 decq         %rax
	0x49, 0x89, 0xd3, //0x00000984 movq         %rdx, %r11
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00000987 movq         $-1, %rdi
	0x48, 0x85, 0xc0, //0x0000098e testq        %rax, %rax
	0x0f, 0x85, 0xd0, 0xfe, 0xff, 0xff, //0x00000991 jne          LBB0_70
	0xe9, 0x72, 0xff, 0xff, 0xff, //0x00000997 jmp          LBB0_81
	//0x0000099c LBB0_88
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x0000099c movq         $-1, %rdi
	0xe9, 0xb9, 0xfb, 0xff, 0xff, //0x000009a3 jmp          LBB0_36
	//0x000009a8 LBB0_89
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x000009a8 movq         $-1, %rdi
	0xe9, 0x5a, 0xff, 0xff, 0xff, //0x000009af jmp          LBB0_81
	//0x000009b4 .p2align 2, 0x00
	//0x000009b4 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x000009b4 .long 2
}
 
