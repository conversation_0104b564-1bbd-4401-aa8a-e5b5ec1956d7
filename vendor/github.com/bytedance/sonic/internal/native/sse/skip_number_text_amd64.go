// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package sse

var _text_skip_number = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, // QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	//0x00000010 LCPI0_1
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x00000010 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	//0x00000020 LCPI0_2
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000020 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x00000030 LCPI0_3
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000030 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000040 LCPI0_4
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000040 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000050 LCPI0_5
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000050 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x00000060 LCPI0_6
	0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, 0x65, //0x00000060 QUAD $0x6565656565656565; QUAD $0x6565656565656565  // .space 16, 'eeeeeeeeeeeeeeee'
	//0x00000070 .p2align 4, 0x90
	//0x00000070 _skip_number
	0x55, //0x00000070 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000071 movq         %rsp, %rbp
	0x41, 0x57, //0x00000074 pushq        %r15
	0x41, 0x56, //0x00000076 pushq        %r14
	0x41, 0x55, //0x00000078 pushq        %r13
	0x41, 0x54, //0x0000007a pushq        %r12
	0x53, //0x0000007c pushq        %rbx
	0x48, 0x83, 0xec, 0x18, //0x0000007d subq         $24, %rsp
	0x48, 0x8b, 0x1f, //0x00000081 movq         (%rdi), %rbx
	0x4c, 0x8b, 0x4f, 0x08, //0x00000084 movq         $8(%rdi), %r9
	0x48, 0x8b, 0x16, //0x00000088 movq         (%rsi), %rdx
	0x49, 0x29, 0xd1, //0x0000008b subq         %rdx, %r9
	0x31, 0xc0, //0x0000008e xorl         %eax, %eax
	0x80, 0x3c, 0x13, 0x2d, //0x00000090 cmpb         $45, (%rbx,%rdx)
	0x4c, 0x8d, 0x3c, 0x13, //0x00000094 leaq         (%rbx,%rdx), %r15
	0x0f, 0x94, 0xc0, //0x00000098 sete         %al
	0x49, 0x01, 0xc7, //0x0000009b addq         %rax, %r15
	0x49, 0x29, 0xc1, //0x0000009e subq         %rax, %r9
	0x0f, 0x84, 0x03, 0x04, 0x00, 0x00, //0x000000a1 je           LBB0_1
	0x41, 0x8a, 0x3f, //0x000000a7 movb         (%r15), %dil
	0x8d, 0x4f, 0xd0, //0x000000aa leal         $-48(%rdi), %ecx
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x000000ad movq         $-2, %rax
	0x80, 0xf9, 0x09, //0x000000b4 cmpb         $9, %cl
	0x0f, 0x87, 0xc3, 0x03, 0x00, 0x00, //0x000000b7 ja           LBB0_57
	0x40, 0x80, 0xff, 0x30, //0x000000bd cmpb         $48, %dil
	0x0f, 0x85, 0x35, 0x00, 0x00, 0x00, //0x000000c1 jne          LBB0_7
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x000000c7 movl         $1, %r11d
	0x49, 0x83, 0xf9, 0x01, //0x000000cd cmpq         $1, %r9
	0x0f, 0x84, 0x7e, 0x03, 0x00, 0x00, //0x000000d1 je           LBB0_56
	0x41, 0x8a, 0x47, 0x01, //0x000000d7 movb         $1(%r15), %al
	0x04, 0xd2, //0x000000db addb         $-46, %al
	0x3c, 0x37, //0x000000dd cmpb         $55, %al
	0x0f, 0x87, 0x70, 0x03, 0x00, 0x00, //0x000000df ja           LBB0_56
	0x0f, 0xb6, 0xc0, //0x000000e5 movzbl       %al, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x000000e8 movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x000000f2 btq          %rax, %rcx
	0x0f, 0x83, 0x59, 0x03, 0x00, 0x00, //0x000000f6 jae          LBB0_56
	//0x000000fc LBB0_7
	0x48, 0x89, 0x55, 0xd0, //0x000000fc movq         %rdx, $-48(%rbp)
	0x49, 0x83, 0xf9, 0x10, //0x00000100 cmpq         $16, %r9
	0x0f, 0x82, 0xac, 0x03, 0x00, 0x00, //0x00000104 jb           LBB0_8
	0x48, 0x89, 0x5d, 0xc8, //0x0000010a movq         %rbx, $-56(%rbp)
	0x48, 0x89, 0x75, 0xc0, //0x0000010e movq         %rsi, $-64(%rbp)
	0x4d, 0x8d, 0x69, 0xf0, //0x00000112 leaq         $-16(%r9), %r13
	0x4c, 0x89, 0xe8, //0x00000116 movq         %r13, %rax
	0x48, 0x83, 0xe0, 0xf0, //0x00000119 andq         $-16, %rax
	0x4e, 0x8d, 0x64, 0x38, 0x10, //0x0000011d leaq         $16(%rax,%r15), %r12
	0x41, 0x83, 0xe5, 0x0f, //0x00000122 andl         $15, %r13d
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000126 movq         $-1, %r8
	0xf3, 0x44, 0x0f, 0x6f, 0x05, 0xca, 0xfe, 0xff, 0xff, //0x0000012d movdqu       $-310(%rip), %xmm8  /* LCPI0_0+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x15, 0xd1, 0xfe, 0xff, 0xff, //0x00000136 movdqu       $-303(%rip), %xmm10  /* LCPI0_1+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x0d, 0xd8, 0xfe, 0xff, 0xff, //0x0000013f movdqu       $-296(%rip), %xmm9  /* LCPI0_2+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x1d, 0xe0, 0xfe, 0xff, 0xff, //0x00000148 movdqu       $-288(%rip), %xmm3  /* LCPI0_3+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x25, 0xe8, 0xfe, 0xff, 0xff, //0x00000150 movdqu       $-280(%rip), %xmm4  /* LCPI0_4+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x2d, 0xf0, 0xfe, 0xff, 0xff, //0x00000158 movdqu       $-272(%rip), %xmm5  /* LCPI0_5+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x35, 0xf8, 0xfe, 0xff, 0xff, //0x00000160 movdqu       $-264(%rip), %xmm6  /* LCPI0_6+0(%rip) */
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00000168 movq         $-1, %r14
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x0000016f movq         $-1, %r10
	0x4c, 0x89, 0xfb, //0x00000176 movq         %r15, %rbx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000179 .p2align 4, 0x90
	//0x00000180 LBB0_10
	0xf3, 0x0f, 0x6f, 0x3b, //0x00000180 movdqu       (%rbx), %xmm7
	0x66, 0x0f, 0x6f, 0xc7, //0x00000184 movdqa       %xmm7, %xmm0
	0x66, 0x41, 0x0f, 0x64, 0xc0, //0x00000188 pcmpgtb      %xmm8, %xmm0
	0x66, 0x41, 0x0f, 0x6f, 0xca, //0x0000018d movdqa       %xmm10, %xmm1
	0x66, 0x0f, 0x64, 0xcf, //0x00000192 pcmpgtb      %xmm7, %xmm1
	0x66, 0x0f, 0xdb, 0xc8, //0x00000196 pand         %xmm0, %xmm1
	0x66, 0x0f, 0x6f, 0xc7, //0x0000019a movdqa       %xmm7, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc1, //0x0000019e pcmpeqb      %xmm9, %xmm0
	0x66, 0x0f, 0x6f, 0xd7, //0x000001a3 movdqa       %xmm7, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x000001a7 pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xeb, 0xd0, //0x000001ab por          %xmm0, %xmm2
	0x66, 0x0f, 0x6f, 0xc7, //0x000001af movdqa       %xmm7, %xmm0
	0x66, 0x0f, 0xeb, 0xc4, //0x000001b3 por          %xmm4, %xmm0
	0x66, 0x0f, 0x74, 0xc6, //0x000001b7 pcmpeqb      %xmm6, %xmm0
	0x66, 0x0f, 0x74, 0xfd, //0x000001bb pcmpeqb      %xmm5, %xmm7
	0x66, 0x0f, 0xd7, 0xf0, //0x000001bf pmovmskb     %xmm0, %esi
	0x66, 0x0f, 0xeb, 0xc7, //0x000001c3 por          %xmm7, %xmm0
	0x66, 0x0f, 0xeb, 0xca, //0x000001c7 por          %xmm2, %xmm1
	0x66, 0x0f, 0xeb, 0xc8, //0x000001cb por          %xmm0, %xmm1
	0x66, 0x0f, 0xd7, 0xff, //0x000001cf pmovmskb     %xmm7, %edi
	0x66, 0x0f, 0xd7, 0xc2, //0x000001d3 pmovmskb     %xmm2, %eax
	0x66, 0x0f, 0xd7, 0xc9, //0x000001d7 pmovmskb     %xmm1, %ecx
	0xba, 0xff, 0xff, 0xff, 0xff, //0x000001db movl         $4294967295, %edx
	0x48, 0x31, 0xd1, //0x000001e0 xorq         %rdx, %rcx
	0x48, 0x0f, 0xbc, 0xc9, //0x000001e3 bsfq         %rcx, %rcx
	0x83, 0xf9, 0x10, //0x000001e7 cmpl         $16, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x000001ea je           LBB0_12
	0xba, 0xff, 0xff, 0xff, 0xff, //0x000001f0 movl         $-1, %edx
	0xd3, 0xe2, //0x000001f5 shll         %cl, %edx
	0xf7, 0xd2, //0x000001f7 notl         %edx
	0x21, 0xd7, //0x000001f9 andl         %edx, %edi
	0x21, 0xd6, //0x000001fb andl         %edx, %esi
	0x21, 0xc2, //0x000001fd andl         %eax, %edx
	0x89, 0xd0, //0x000001ff movl         %edx, %eax
	//0x00000201 LBB0_12
	0x8d, 0x57, 0xff, //0x00000201 leal         $-1(%rdi), %edx
	0x21, 0xfa, //0x00000204 andl         %edi, %edx
	0x0f, 0x85, 0x27, 0x02, 0x00, 0x00, //0x00000206 jne          LBB0_13
	0x8d, 0x56, 0xff, //0x0000020c leal         $-1(%rsi), %edx
	0x21, 0xf2, //0x0000020f andl         %esi, %edx
	0x0f, 0x85, 0x1c, 0x02, 0x00, 0x00, //0x00000211 jne          LBB0_13
	0x8d, 0x50, 0xff, //0x00000217 leal         $-1(%rax), %edx
	0x21, 0xc2, //0x0000021a andl         %eax, %edx
	0x0f, 0x85, 0x11, 0x02, 0x00, 0x00, //0x0000021c jne          LBB0_13
	0x85, 0xff, //0x00000222 testl        %edi, %edi
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00000224 je           LBB0_20
	0x48, 0x89, 0xda, //0x0000022a movq         %rbx, %rdx
	0x4c, 0x29, 0xfa, //0x0000022d subq         %r15, %rdx
	0x44, 0x0f, 0xbc, 0xdf, //0x00000230 bsfl         %edi, %r11d
	0x49, 0x01, 0xd3, //0x00000234 addq         %rdx, %r11
	0x49, 0x83, 0xfa, 0xff, //0x00000237 cmpq         $-1, %r10
	0x0f, 0x85, 0xfc, 0x01, 0x00, 0x00, //0x0000023b jne          LBB0_14
	0x4d, 0x89, 0xda, //0x00000241 movq         %r11, %r10
	//0x00000244 LBB0_20
	0x85, 0xf6, //0x00000244 testl        %esi, %esi
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00000246 je           LBB0_23
	0x48, 0x89, 0xda, //0x0000024c movq         %rbx, %rdx
	0x4c, 0x29, 0xfa, //0x0000024f subq         %r15, %rdx
	0x44, 0x0f, 0xbc, 0xde, //0x00000252 bsfl         %esi, %r11d
	0x49, 0x01, 0xd3, //0x00000256 addq         %rdx, %r11
	0x49, 0x83, 0xfe, 0xff, //0x00000259 cmpq         $-1, %r14
	0x0f, 0x85, 0xda, 0x01, 0x00, 0x00, //0x0000025d jne          LBB0_14
	0x4d, 0x89, 0xde, //0x00000263 movq         %r11, %r14
	//0x00000266 LBB0_23
	0x85, 0xc0, //0x00000266 testl        %eax, %eax
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00000268 je           LBB0_26
	0x48, 0x89, 0xda, //0x0000026e movq         %rbx, %rdx
	0x4c, 0x29, 0xfa, //0x00000271 subq         %r15, %rdx
	0x44, 0x0f, 0xbc, 0xd8, //0x00000274 bsfl         %eax, %r11d
	0x49, 0x01, 0xd3, //0x00000278 addq         %rdx, %r11
	0x49, 0x83, 0xf8, 0xff, //0x0000027b cmpq         $-1, %r8
	0x0f, 0x85, 0xb8, 0x01, 0x00, 0x00, //0x0000027f jne          LBB0_14
	0x4d, 0x89, 0xd8, //0x00000285 movq         %r11, %r8
	//0x00000288 LBB0_26
	0x83, 0xf9, 0x10, //0x00000288 cmpl         $16, %ecx
	0x0f, 0x85, 0xbb, 0x00, 0x00, 0x00, //0x0000028b jne          LBB0_58
	0x48, 0x83, 0xc3, 0x10, //0x00000291 addq         $16, %rbx
	0x49, 0x83, 0xc1, 0xf0, //0x00000295 addq         $-16, %r9
	0x49, 0x83, 0xf9, 0x0f, //0x00000299 cmpq         $15, %r9
	0x0f, 0x87, 0xdd, 0xfe, 0xff, 0xff, //0x0000029d ja           LBB0_10
	0x4d, 0x85, 0xed, //0x000002a3 testq        %r13, %r13
	0x48, 0x8b, 0x75, 0xc0, //0x000002a6 movq         $-64(%rbp), %rsi
	0x48, 0x8b, 0x5d, 0xc8, //0x000002aa movq         $-56(%rbp), %rbx
	0x0f, 0x84, 0xa6, 0x00, 0x00, 0x00, //0x000002ae je           LBB0_40
	//0x000002b4 LBB0_29
	0x4b, 0x8d, 0x04, 0x2c, //0x000002b4 leaq         (%r12,%r13), %rax
	0x48, 0x8d, 0x0d, 0x19, 0x02, 0x00, 0x00, //0x000002b8 leaq         $537(%rip), %rcx  /* LJTI0_0+0(%rip) */
	0xe9, 0x18, 0x00, 0x00, 0x00, //0x000002bf jmp          LBB0_30
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000002c4 .p2align 4, 0x90
	//0x000002d0 LBB0_38
	0x49, 0x89, 0xd4, //0x000002d0 movq         %rdx, %r12
	0x49, 0xff, 0xcd, //0x000002d3 decq         %r13
	0x0f, 0x84, 0x84, 0x01, 0x00, 0x00, //0x000002d6 je           LBB0_39
	//0x000002dc LBB0_30
	0x41, 0x0f, 0xbe, 0x3c, 0x24, //0x000002dc movsbl       (%r12), %edi
	0x83, 0xc7, 0xd5, //0x000002e1 addl         $-43, %edi
	0x83, 0xff, 0x3a, //0x000002e4 cmpl         $58, %edi
	0x0f, 0x87, 0x6d, 0x00, 0x00, 0x00, //0x000002e7 ja           LBB0_40
	0x49, 0x8d, 0x54, 0x24, 0x01, //0x000002ed leaq         $1(%r12), %rdx
	0x48, 0x63, 0x3c, 0xb9, //0x000002f2 movslq       (%rcx,%rdi,4), %rdi
	0x48, 0x01, 0xcf, //0x000002f6 addq         %rcx, %rdi
	0xff, 0xe7, //0x000002f9 jmpq         *%rdi
	//0x000002fb LBB0_36
	0x49, 0x89, 0xd3, //0x000002fb movq         %rdx, %r11
	0x4d, 0x29, 0xfb, //0x000002fe subq         %r15, %r11
	0x49, 0x83, 0xf8, 0xff, //0x00000301 cmpq         $-1, %r8
	0x0f, 0x85, 0x8a, 0x01, 0x00, 0x00, //0x00000305 jne          LBB0_59
	0x49, 0xff, 0xcb, //0x0000030b decq         %r11
	0x4d, 0x89, 0xd8, //0x0000030e movq         %r11, %r8
	0xe9, 0xba, 0xff, 0xff, 0xff, //0x00000311 jmp          LBB0_38
	//0x00000316 LBB0_34
	0x49, 0x89, 0xd3, //0x00000316 movq         %rdx, %r11
	0x4d, 0x29, 0xfb, //0x00000319 subq         %r15, %r11
	0x49, 0x83, 0xfe, 0xff, //0x0000031c cmpq         $-1, %r14
	0x0f, 0x85, 0x6f, 0x01, 0x00, 0x00, //0x00000320 jne          LBB0_59
	0x49, 0xff, 0xcb, //0x00000326 decq         %r11
	0x4d, 0x89, 0xde, //0x00000329 movq         %r11, %r14
	0xe9, 0x9f, 0xff, 0xff, 0xff, //0x0000032c jmp          LBB0_38
	//0x00000331 LBB0_32
	0x49, 0x89, 0xd3, //0x00000331 movq         %rdx, %r11
	0x4d, 0x29, 0xfb, //0x00000334 subq         %r15, %r11
	0x49, 0x83, 0xfa, 0xff, //0x00000337 cmpq         $-1, %r10
	0x0f, 0x85, 0x54, 0x01, 0x00, 0x00, //0x0000033b jne          LBB0_59
	0x49, 0xff, 0xcb, //0x00000341 decq         %r11
	0x4d, 0x89, 0xda, //0x00000344 movq         %r11, %r10
	0xe9, 0x84, 0xff, 0xff, 0xff, //0x00000347 jmp          LBB0_38
	//0x0000034c LBB0_58
	0x48, 0x01, 0xcb, //0x0000034c addq         %rcx, %rbx
	0x49, 0x89, 0xdc, //0x0000034f movq         %rbx, %r12
	0x48, 0x8b, 0x75, 0xc0, //0x00000352 movq         $-64(%rbp), %rsi
	0x48, 0x8b, 0x5d, 0xc8, //0x00000356 movq         $-56(%rbp), %rbx
	//0x0000035a LBB0_40
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x0000035a movq         $-1, %r11
	0x4d, 0x85, 0xf6, //0x00000361 testq        %r14, %r14
	0x0f, 0x84, 0x09, 0x01, 0x00, 0x00, //0x00000364 je           LBB0_55
	//0x0000036a LBB0_41
	0x4d, 0x85, 0xc0, //0x0000036a testq        %r8, %r8
	0x0f, 0x84, 0x00, 0x01, 0x00, 0x00, //0x0000036d je           LBB0_55
	0x4d, 0x85, 0xd2, //0x00000373 testq        %r10, %r10
	0x48, 0x8b, 0x55, 0xd0, //0x00000376 movq         $-48(%rbp), %rdx
	0x0f, 0x84, 0xf3, 0x00, 0x00, 0x00, //0x0000037a je           LBB0_55
	0x4d, 0x29, 0xfc, //0x00000380 subq         %r15, %r12
	0x49, 0x8d, 0x44, 0x24, 0xff, //0x00000383 leaq         $-1(%r12), %rax
	0x49, 0x39, 0xc6, //0x00000388 cmpq         %rax, %r14
	0x0f, 0x84, 0x3c, 0x00, 0x00, 0x00, //0x0000038b je           LBB0_46
	0x49, 0x39, 0xc2, //0x00000391 cmpq         %rax, %r10
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x00000394 je           LBB0_46
	0x49, 0x39, 0xc0, //0x0000039a cmpq         %rax, %r8
	0x0f, 0x84, 0x2a, 0x00, 0x00, 0x00, //0x0000039d je           LBB0_46
	0x4d, 0x85, 0xc0, //0x000003a3 testq        %r8, %r8
	0x0f, 0x8e, 0x35, 0x00, 0x00, 0x00, //0x000003a6 jle          LBB0_50
	0x49, 0x8d, 0x40, 0xff, //0x000003ac leaq         $-1(%r8), %rax
	0x49, 0x39, 0xc6, //0x000003b0 cmpq         %rax, %r14
	0x0f, 0x84, 0x28, 0x00, 0x00, 0x00, //0x000003b3 je           LBB0_50
	0x49, 0xf7, 0xd0, //0x000003b9 notq         %r8
	0x4d, 0x89, 0xc3, //0x000003bc movq         %r8, %r11
	0x4d, 0x85, 0xdb, //0x000003bf testq        %r11, %r11
	0x0f, 0x89, 0x8d, 0x00, 0x00, 0x00, //0x000003c2 jns          LBB0_56
	0xe9, 0xa6, 0x00, 0x00, 0x00, //0x000003c8 jmp          LBB0_55
	//0x000003cd LBB0_46
	0x49, 0xf7, 0xdc, //0x000003cd negq         %r12
	0x4d, 0x89, 0xe3, //0x000003d0 movq         %r12, %r11
	0x4d, 0x85, 0xdb, //0x000003d3 testq        %r11, %r11
	0x0f, 0x89, 0x79, 0x00, 0x00, 0x00, //0x000003d6 jns          LBB0_56
	0xe9, 0x92, 0x00, 0x00, 0x00, //0x000003dc jmp          LBB0_55
	//0x000003e1 LBB0_50
	0x4c, 0x89, 0xd0, //0x000003e1 movq         %r10, %rax
	0x4c, 0x09, 0xf0, //0x000003e4 orq          %r14, %rax
	0x4d, 0x39, 0xf2, //0x000003e7 cmpq         %r14, %r10
	0x0f, 0x8c, 0x1d, 0x00, 0x00, 0x00, //0x000003ea jl           LBB0_53
	0x48, 0x85, 0xc0, //0x000003f0 testq        %rax, %rax
	0x0f, 0x88, 0x14, 0x00, 0x00, 0x00, //0x000003f3 js           LBB0_53
	0x49, 0xf7, 0xd2, //0x000003f9 notq         %r10
	0x4d, 0x89, 0xd3, //0x000003fc movq         %r10, %r11
	0x4d, 0x85, 0xdb, //0x000003ff testq        %r11, %r11
	0x0f, 0x89, 0x4d, 0x00, 0x00, 0x00, //0x00000402 jns          LBB0_56
	0xe9, 0x66, 0x00, 0x00, 0x00, //0x00000408 jmp          LBB0_55
	//0x0000040d LBB0_53
	0x48, 0x85, 0xc0, //0x0000040d testq        %rax, %rax
	0x49, 0x8d, 0x46, 0xff, //0x00000410 leaq         $-1(%r14), %rax
	0x49, 0xf7, 0xd6, //0x00000414 notq         %r14
	0x4d, 0x0f, 0x48, 0xf4, //0x00000417 cmovsq       %r12, %r14
	0x49, 0x39, 0xc2, //0x0000041b cmpq         %rax, %r10
	0x4d, 0x0f, 0x45, 0xf4, //0x0000041e cmovneq      %r12, %r14
	0x4d, 0x89, 0xf3, //0x00000422 movq         %r14, %r11
	0x4d, 0x85, 0xdb, //0x00000425 testq        %r11, %r11
	0x0f, 0x89, 0x27, 0x00, 0x00, 0x00, //0x00000428 jns          LBB0_56
	0xe9, 0x40, 0x00, 0x00, 0x00, //0x0000042e jmp          LBB0_55
	//0x00000433 LBB0_13
	0x4c, 0x29, 0xfb, //0x00000433 subq         %r15, %rbx
	0x44, 0x0f, 0xbc, 0xda, //0x00000436 bsfl         %edx, %r11d
	0x49, 0x01, 0xdb, //0x0000043a addq         %rbx, %r11
	//0x0000043d LBB0_14
	0x49, 0xf7, 0xd3, //0x0000043d notq         %r11
	0x48, 0x8b, 0x75, 0xc0, //0x00000440 movq         $-64(%rbp), %rsi
	0x48, 0x8b, 0x5d, 0xc8, //0x00000444 movq         $-56(%rbp), %rbx
	0x48, 0x8b, 0x55, 0xd0, //0x00000448 movq         $-48(%rbp), %rdx
	0x4d, 0x85, 0xdb, //0x0000044c testq        %r11, %r11
	0x0f, 0x88, 0x1e, 0x00, 0x00, 0x00, //0x0000044f js           LBB0_55
	//0x00000455 LBB0_56
	0x4d, 0x01, 0xdf, //0x00000455 addq         %r11, %r15
	0x48, 0x89, 0xd0, //0x00000458 movq         %rdx, %rax
	0xe9, 0x20, 0x00, 0x00, 0x00, //0x0000045b jmp          LBB0_57
	//0x00000460 LBB0_39
	0x49, 0x89, 0xc4, //0x00000460 movq         %rax, %r12
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00000463 movq         $-1, %r11
	0x4d, 0x85, 0xf6, //0x0000046a testq        %r14, %r14
	0x0f, 0x85, 0xf7, 0xfe, 0xff, 0xff, //0x0000046d jne          LBB0_41
	//0x00000473 LBB0_55
	0x49, 0xf7, 0xd3, //0x00000473 notq         %r11
	0x4d, 0x01, 0xdf, //0x00000476 addq         %r11, %r15
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000479 movq         $-2, %rax
	//0x00000480 LBB0_57
	0x49, 0x29, 0xdf, //0x00000480 subq         %rbx, %r15
	0x4c, 0x89, 0x3e, //0x00000483 movq         %r15, (%rsi)
	0x48, 0x83, 0xc4, 0x18, //0x00000486 addq         $24, %rsp
	0x5b, //0x0000048a popq         %rbx
	0x41, 0x5c, //0x0000048b popq         %r12
	0x41, 0x5d, //0x0000048d popq         %r13
	0x41, 0x5e, //0x0000048f popq         %r14
	0x41, 0x5f, //0x00000491 popq         %r15
	0x5d, //0x00000493 popq         %rbp
	0xc3, //0x00000494 retq         
	//0x00000495 LBB0_59
	0x49, 0xf7, 0xdb, //0x00000495 negq         %r11
	0x48, 0x8b, 0x55, 0xd0, //0x00000498 movq         $-48(%rbp), %rdx
	0x4d, 0x85, 0xdb, //0x0000049c testq        %r11, %r11
	0x0f, 0x89, 0xb0, 0xff, 0xff, 0xff, //0x0000049f jns          LBB0_56
	0xe9, 0xc9, 0xff, 0xff, 0xff, //0x000004a5 jmp          LBB0_55
	//0x000004aa LBB0_1
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000004aa movq         $-1, %rax
	0xe9, 0xca, 0xff, 0xff, 0xff, //0x000004b1 jmp          LBB0_57
	//0x000004b6 LBB0_8
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x000004b6 movq         $-1, %r10
	0x4d, 0x89, 0xfc, //0x000004bd movq         %r15, %r12
	0x4d, 0x89, 0xcd, //0x000004c0 movq         %r9, %r13
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x000004c3 movq         $-1, %r14
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000004ca movq         $-1, %r8
	0xe9, 0xde, 0xfd, 0xff, 0xff, //0x000004d1 jmp          LBB0_29
	0x90, 0x90, //0x000004d6 .p2align 2, 0x90
	// // .set L0_0_set_36, LBB0_36-LJTI0_0
	// // .set L0_0_set_40, LBB0_40-LJTI0_0
	// // .set L0_0_set_32, LBB0_32-LJTI0_0
	// // .set L0_0_set_38, LBB0_38-LJTI0_0
	// // .set L0_0_set_34, LBB0_34-LJTI0_0
	//0x000004d8 LJTI0_0
	0x23, 0xfe, 0xff, 0xff, //0x000004d8 .long L0_0_set_36
	0x82, 0xfe, 0xff, 0xff, //0x000004dc .long L0_0_set_40
	0x23, 0xfe, 0xff, 0xff, //0x000004e0 .long L0_0_set_36
	0x59, 0xfe, 0xff, 0xff, //0x000004e4 .long L0_0_set_32
	0x82, 0xfe, 0xff, 0xff, //0x000004e8 .long L0_0_set_40
	0xf8, 0xfd, 0xff, 0xff, //0x000004ec .long L0_0_set_38
	0xf8, 0xfd, 0xff, 0xff, //0x000004f0 .long L0_0_set_38
	0xf8, 0xfd, 0xff, 0xff, //0x000004f4 .long L0_0_set_38
	0xf8, 0xfd, 0xff, 0xff, //0x000004f8 .long L0_0_set_38
	0xf8, 0xfd, 0xff, 0xff, //0x000004fc .long L0_0_set_38
	0xf8, 0xfd, 0xff, 0xff, //0x00000500 .long L0_0_set_38
	0xf8, 0xfd, 0xff, 0xff, //0x00000504 .long L0_0_set_38
	0xf8, 0xfd, 0xff, 0xff, //0x00000508 .long L0_0_set_38
	0xf8, 0xfd, 0xff, 0xff, //0x0000050c .long L0_0_set_38
	0xf8, 0xfd, 0xff, 0xff, //0x00000510 .long L0_0_set_38
	0x82, 0xfe, 0xff, 0xff, //0x00000514 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x00000518 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x0000051c .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x00000520 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x00000524 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x00000528 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x0000052c .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x00000530 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x00000534 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x00000538 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x0000053c .long L0_0_set_40
	0x3e, 0xfe, 0xff, 0xff, //0x00000540 .long L0_0_set_34
	0x82, 0xfe, 0xff, 0xff, //0x00000544 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x00000548 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x0000054c .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x00000550 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x00000554 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x00000558 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x0000055c .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x00000560 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x00000564 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x00000568 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x0000056c .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x00000570 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x00000574 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x00000578 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x0000057c .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x00000580 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x00000584 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x00000588 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x0000058c .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x00000590 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x00000594 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x00000598 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x0000059c .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x000005a0 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x000005a4 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x000005a8 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x000005ac .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x000005b0 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x000005b4 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x000005b8 .long L0_0_set_40
	0x82, 0xfe, 0xff, 0xff, //0x000005bc .long L0_0_set_40
	0x3e, 0xfe, 0xff, 0xff, //0x000005c0 .long L0_0_set_34
	//0x000005c4 .p2align 2, 0x00
	//0x000005c4 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x000005c4 .long 2
}
 
