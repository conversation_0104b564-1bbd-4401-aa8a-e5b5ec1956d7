// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__vnumber = 48
)

const (
    _stack__vnumber = 112
)

const (
    _size__vnumber = 8952
)

var (
    _pcsp__vnumber = [][2]uint32{
        {1, 0},
        {4, 8},
        {6, 16},
        {8, 24},
        {10, 32},
        {12, 40},
        {13, 48},
        {767, 112},
        {771, 48},
        {772, 40},
        {774, 32},
        {776, 24},
        {778, 16},
        {780, 8},
        {781, 0},
        {8949, 112},
    }
)

var _cfunc_vnumber = []loader.CFunc{
    {"_vnumber_entry", 0,  _entry__vnumber, 0, nil},
    {"_vnumber", _entry__vnumber, _size__vnumber, _stack__vnumber, _pcsp__vnumber},
}
