// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__skip_array = 160
)

const (
    _stack__skip_array = 160
)

const (
    _size__skip_array = 10216
)

var (
    _pcsp__skip_array = [][2]uint32{
        {1, 0},
        {4, 8},
        {6, 16},
        {8, 24},
        {10, 32},
        {12, 40},
        {13, 48},
        {9556, 160},
        {9560, 48},
        {9561, 40},
        {9563, 32},
        {9565, 24},
        {9567, 16},
        {9569, 8},
        {9570, 0},
        {10216, 160},
    }
)

var _cfunc_skip_array = []loader.CFunc{
    {"_skip_array_entry", 0,  _entry__skip_array, 0, nil},
    {"_skip_array", _entry__skip_array, _size__skip_array, _stack__skip_array, _pcsp__skip_array},
}
