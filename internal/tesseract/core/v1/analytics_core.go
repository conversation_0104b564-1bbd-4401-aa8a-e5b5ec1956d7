package v1

import (
	"strings"

	model "tesseract-service/internal/tesseract/model/v1"
	"tesseract-service/internal/tesseract/repository"
)

// IAnalyticsCore defines the interface for analytics core business logic
type IAnalyticsCore interface {
	GetFilteredAnalyticsConfig(userRoles []string) (*model.AnalyticsConfigResponse, error)
}

// AnalyticsCore implements the analytics core business logic
type AnalyticsCore struct {
	analyticsRepo repository.IAnalyticsRepository
}

// NewAnalyticsCore creates a new analytics core instance
func NewAnalyticsCore(analyticsRepo repository.IAnalyticsRepository) IAnalyticsCore {
	return &AnalyticsCore{
		analyticsRepo: analyticsRepo,
	}
}

// GetFilteredAnalyticsConfig returns analytics configuration filtered by user roles
func (c *AnalyticsCore) GetFilteredAnalyticsConfig(userRoles []string) (*model.AnalyticsConfigResponse, error) {
	// Get the complete analytics configuration
	config, err := c.analyticsRepo.GetAnalyticsConfig()
	if err != nil {
		return nil, err
	}

	// Filter the configuration based on user roles
	filteredFunctions := c.filterAnalyticalFunctions(config.AnalyticalFunctions, userRoles)

	return &model.AnalyticsConfigResponse{
		AnalyticalFunctions: filteredFunctions,
	}, nil
}

// filterAnalyticalFunctions filters analytical functions based on user roles
func (c *AnalyticsCore) filterAnalyticalFunctions(functions []model.AnalyticalFunction, userRoles []string) []model.AnalyticalFunction {
	var filtered []model.AnalyticalFunction

	for _, function := range functions {
		// Check if user has access to this analytical function
		if c.hasAccessToAnalyticalFunction(function, userRoles) {
			// Filter business functions within this analytical function
			filteredFunction := function
			filteredFunction.BusinessFunction = c.filterBusinessFunctions(function.BusinessFunction, userRoles)

			// Only include if there are accessible business functions
			if len(filteredFunction.BusinessFunction) > 0 {
				filtered = append(filtered, filteredFunction)
			}
		}
	}

	return filtered
}

// hasAccessToAnalyticalFunction checks if user has access to an analytical function
func (c *AnalyticsCore) hasAccessToAnalyticalFunction(function model.AnalyticalFunction, userRoles []string) bool {
	// If analytical function has specific required roles, check those
	if len(function.Roles) > 0 {
		return c.hasAnyRole(function.Roles, userRoles)
	}

	// If no specific roles required for analytical function, check if user has access to any business function within it
	for _, businessFunction := range function.BusinessFunction {
		if c.hasAccessToBusinessFunction(businessFunction, userRoles) {
			return true
		}
	}

	return false
}

// filterBusinessFunctions filters business functions based on user roles
func (c *AnalyticsCore) filterBusinessFunctions(businessFunctions []model.BusinessFunction, userRoles []string) []model.BusinessFunction {
	var filtered []model.BusinessFunction

	for _, bf := range businessFunctions {
		if c.hasAccessToBusinessFunction(bf, userRoles) {
			// Filter analytics types within this business function
			filteredBF := bf
			filteredBF.AnalyticsType = c.filterAnalyticsTypes(bf.AnalyticsType, userRoles)

			// Only include if there are accessible analytics types
			if len(filteredBF.AnalyticsType) > 0 {
				filtered = append(filtered, filteredBF)
			}
		}
	}

	return filtered
}

// hasAccessToBusinessFunction checks if user has access to a business function
func (c *AnalyticsCore) hasAccessToBusinessFunction(businessFunction model.BusinessFunction, userRoles []string) bool {
	// If business function has specific required roles, check those
	if len(businessFunction.Roles) > 0 {
		return c.hasAnyRole(businessFunction.Roles, userRoles)
	}

	// If no specific roles required for business function, check if user has access to any analytics type within it
	for _, analyticsType := range businessFunction.AnalyticsType {
		if c.hasAccessToAnalyticsType(analyticsType, userRoles) {
			return true
		}
	}

	return false
}

// filterAnalyticsTypes filters analytics types based on user roles
func (c *AnalyticsCore) filterAnalyticsTypes(analyticsTypes []model.AnalyticsType, userRoles []string) []model.AnalyticsType {
	var filtered []model.AnalyticsType

	for _, at := range analyticsTypes {
		if c.hasAccessToAnalyticsType(at, userRoles) {
			// Filter dashboards within this analytics type
			filteredAT := at
			filteredAT.Dashboard = c.filterDashboards(at.Dashboard, userRoles)

			// Only include analytics type if it has at least one accessible dashboard
			if len(filteredAT.Dashboard) > 0 {
				filtered = append(filtered, filteredAT)
			}
		}
	}

	return filtered
}

// hasAccessToAnalyticsType checks if user has access to an analytics type
func (c *AnalyticsCore) hasAccessToAnalyticsType(analyticsType model.AnalyticsType, userRoles []string) bool {
	// If analytics type has specific required roles, check those
	if len(analyticsType.Roles) > 0 {
		return c.hasAnyRole(analyticsType.Roles, userRoles)
	}

	// If no specific roles required for analytics type, check if user has access to any dashboard within it
	for _, dashboard := range analyticsType.Dashboard {
		if c.hasAccessToDashboard(dashboard, userRoles) {
			return true
		}
	}

	return false
}

// filterDashboards filters dashboards based on user roles
func (c *AnalyticsCore) filterDashboards(dashboards []model.Dashboard, userRoles []string) []model.Dashboard {
	var filtered []model.Dashboard

	for _, dashboard := range dashboards {
		if c.hasAccessToDashboard(dashboard, userRoles) {
			filtered = append(filtered, dashboard)
		}
	}

	return filtered
}

// hasAccessToDashboard checks if user has access to a dashboard
func (c *AnalyticsCore) hasAccessToDashboard(dashboard model.Dashboard, userRoles []string) bool {
	// If dashboard has no specific roles, allow access
	if len(dashboard.Roles) == 0 {
		return true
	}

	// Check if user has any of the required roles for this dashboard
	return c.hasAnyRole(dashboard.Roles, userRoles)
}

// hasAnyRole checks if user has any of the required roles
func (c *AnalyticsCore) hasAnyRole(requiredRoles, userRoles []string) bool {
	// Check for admin role first
	if c.isAdminUser(userRoles) {
		return true
	}

	for _, required := range requiredRoles {
		for _, userRole := range userRoles {
			if strings.EqualFold(required, userRole) {
				return true
			}
		}
	}
	return false
}

// isAdminUser checks if user has admin privileges
func (c *AnalyticsCore) isAdminUser(userRoles []string) bool {
	for _, role := range userRoles {
		// Check for admin roles or permissions
		if strings.Contains(strings.ToUpper(role), "ADMIN") ||
			strings.Contains(strings.ToUpper(role), "ANALYTICS_ADMIN") {
			return true
		}
	}
	return false
}
