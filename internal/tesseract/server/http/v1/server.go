package v1

import (
	"encoding/json"
	"net/http"

	_Core "tesseract-service/internal/tesseract/core/v1"
	model "tesseract-service/internal/tesseract/model/v1"

	commonUtils "bitbucket.org/infracoreplatform/server-utils/common"
	"bitbucket.org/infracoreplatform/server-utils/metrics"
	metricsBase "bitbucket.org/infracoreplatform/server-utils/metrics/base"
	"github.com/gorilla/mux"
)

type HttpServer struct {
	Router        *mux.Router
	AnalyticsCore _Core.IAnalyticsCore
	Logger        commonUtils.LogglyLoggerInterface
	Metrics       metricsBase.IMetrics
	Health        metricsBase.IHealthChecker
	LoggerTracer  metrics.CompositeObserver
}

func (s HttpServer) RegisterRoutes() {
	// Register analytics config route
	s.Router.HandleFunc("/api/config/analytics/v1", s.GetAnalyticsConfig).Methods("POST")
}

// GetAnalyticsConfig handles POST requests for filtered analytics configuration
func (s *HttpServer) GetAnalyticsConfig(w http.ResponseWriter, r *http.Request) {
	// Set response headers
	w.Header().Set("Content-Type", "application/json")

	// Parse request body
	var request model.AnalyticsConfigRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		s.Logger.WithField("error", err.Error()).Error("Failed to parse request body")
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]string{
			"error": "Invalid request body",
		})
		return
	}

	// Validate request
	if len(request.Roles) == 0 {
		s.Logger.Error("Roles array is empty in request")
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]string{
			"error": "Roles array is required and cannot be empty",
		})
		return
	}

	// Get filtered analytics configuration
	response, err := s.AnalyticsCore.GetFilteredAnalyticsConfig(request.Roles)
	if err != nil {
		s.Logger.WithField("error", err.Error()).Error("Failed to get filtered analytics config")
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(map[string]string{
			"error": "Internal server error",
		})
		return
	}

	// Return successful response
	w.WriteHeader(http.StatusOK)
	if err := json.NewEncoder(w).Encode(response); err != nil {
		s.Logger.WithField("error", err.Error()).Error("Failed to encode response")
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(map[string]string{
			"error": "Failed to encode response",
		})
		return
	}

	s.Logger.WithField("roles_count", len(request.Roles)).Info("Successfully returned filtered analytics config")
}
