package v1

import (
	_Core "tesseract-service/internal/tesseract/core/v1"

	commonUtils "bitbucket.org/infracoreplatform/server-utils/common"
	"bitbucket.org/infracoreplatform/server-utils/metrics"
	metricsBase "bitbucket.org/infracoreplatform/server-utils/metrics/base"
	"github.com/gorilla/mux"
)

type HttpServer struct {
	Router       *mux.Router
	Core         _Core.ICore
	Logger       commonUtils.LogglyLoggerInterface
	Metrics      metricsBase.IMetrics
	Health       metricsBase.IHealthChecker
	LoggerTracer metrics.CompositeObserver
}

func (s HttpServer) RegisterRoutes() {
}
