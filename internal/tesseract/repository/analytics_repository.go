package repository

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"path/filepath"

	model "tesseract-service/internal/tesseract/model/v1"
)

// IAnalyticsRepository defines the interface for analytics repository
type IAnalyticsRepository interface {
	GetAnalyticsConfig() (*model.AnalyticsConfig, error)
}

// AnalyticsRepository implements the analytics repository
type AnalyticsRepository struct {
	configPath string
}

// NewAnalyticsRepository creates a new analytics repository
func NewAnalyticsRepository(configPath string) IAnalyticsRepository {
	return &AnalyticsRepository{
		configPath: configPath,
	}
}

// GetAnalyticsConfig reads and returns the analytics configuration from JSON file
func (r *AnalyticsRepository) GetAnalyticsConfig() (*model.AnalyticsConfig, error) {
	// Get the absolute path to the config file
	absPath, err := filepath.Abs(r.configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to get absolute path for config file: %w", err)
	}

	// Read the JSON file
	data, err := ioutil.ReadFile(absPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read analytics config file: %w", err)
	}

	// Parse JSON into struct
	var config model.AnalyticsConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse analytics config JSON: %w", err)
	}

	return &config, nil
}
