package v1

// AnalyticsConfigRequest represents the request payload for analytics config API
type AnalyticsConfigRequest struct {
	Roles []string `json:"roles" binding:"required"`
}

// Dashboard represents a dashboard configuration
type Dashboard struct {
	ID    string   `json:"id"`
	Title string   `json:"title"`
	URL   string   `json:"url"`
	Roles []string `json:"roles"`
}

// AnalyticsType represents an analytics type configuration
type AnalyticsType struct {
	ID        string      `json:"id"`
	Title     string      `json:"title"`
	Icon      string      `json:"icon"`
	Roles     []string    `json:"roles"`
	Dashboard []Dashboard `json:"dashboard"`
}

// BusinessFunction represents a business function configuration
type BusinessFunction struct {
	ID            string          `json:"id"`
	Title         string          `json:"title"`
	Description   string          `json:"description"`
	Icon          string          `json:"icon"`
	Roles         []string        `json:"roles"`
	AnalyticsType []AnalyticsType `json:"analyticsType"`
	Reports       bool            `json:"reports"`
	Data          bool            `json:"data"`
	Prediction    bool            `json:"prediction"`
}

// AnalyticalFunction represents an analytical function configuration
type AnalyticalFunction struct {
	ID               string             `json:"id"`
	Title            string             `json:"title"`
	Description      string             `json:"description"`
	Icon             string             `json:"icon"`
	BgColor          string             `json:"bgColor"`
	Roles            []string           `json:"roles"`
	BusinessFunction []BusinessFunction `json:"businessFunction"`
}

// AnalyticsConfig represents the complete analytics configuration
type AnalyticsConfig struct {
	AnalyticalFunctions []AnalyticalFunction `json:"analyticalFunctions"`
}

// AnalyticsConfigResponse represents the response payload for analytics config API
type AnalyticsConfigResponse struct {
	AnalyticalFunctions []AnalyticalFunction `json:"analyticalFunctions"`
}
