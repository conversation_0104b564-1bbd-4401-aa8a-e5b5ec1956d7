package services

import (
	"crypto/rsa"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/big"
	"net/http"
	model "tesseract-service/internal/tesseract/model/v1"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// AuthService handles authentication operations
type AuthService struct {
	config     *model.Config
	googleKeys map[string]*rsa.PublicKey
	lastUpdate time.Time
}

// GoogleKey represents a Google public key
type GoogleKey struct {
	Kid string `json:"kid"`
	Kty string `json:"kty"`
	Use string `json:"use"`
	N   string `json:"n"`
	E   string `json:"e"`
	Alg string `json:"alg"`
}

// GoogleKeysResponse represents the response from Google's certs endpoint
type GoogleKeysResponse struct {
	Keys []GoogleKey `json:"keys"`
}

// Claims represents JWT claims
type Claims struct {
	Sub           string `json:"sub"`
	Name          string `json:"name"`
	Email         string `json:"email"`
	Picture       string `json:"picture"`
	EmailVerified bool   `json:"email_verified"`
	jwt.RegisteredClaims
}

// NewAuthService creates a new authentication service
func NewAuthService(cfg *model.Config) *AuthService {
	service := &AuthService{
		config:     cfg,
		googleKeys: make(map[string]*rsa.PublicKey),
	}

	// Load Google public keys on startup
	if err := service.loadGoogleKeys(); err != nil {
		fmt.Printf("Warning: Failed to load Google keys on startup: %v\n", err)
	}

	return service
}

// ValidateToken validates a JWT token using Google's public keys
func (s *AuthService) ValidateToken(tokenString string) (*Claims, error) {
	// Parse the token without verification first to get the kid
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		// Check the signing method
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}

		// Get the kid from the header
		kid, ok := token.Header["kid"].(string)
		if !ok {
			return nil, fmt.Errorf("kid not found in token header")
		}

		// Refresh keys if they're old or if we don't have the required key
		if time.Since(s.lastUpdate) > time.Hour || s.googleKeys[kid] == nil {
			if err := s.loadGoogleKeys(); err != nil {
				return nil, fmt.Errorf("failed to refresh Google keys: %w", err)
			}
		}

		// Get the public key for this kid
		publicKey, exists := s.googleKeys[kid]
		if !exists {
			return nil, fmt.Errorf("public key not found for kid: %s", kid)
		}

		return publicKey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	// Check if token is valid
	if !token.Valid {
		return nil, fmt.Errorf("token is not valid")
	}

	// Extract claims
	claims, ok := token.Claims.(*Claims)
	if !ok {
		return nil, fmt.Errorf("failed to extract claims")
	}

	// Verify issuer
	if claims.Issuer != s.config.JWTIssuer && claims.Issuer != "https://"+s.config.JWTIssuer {
		return nil, fmt.Errorf("invalid issuer: %s", claims.Issuer)
	}

	// Check if token is expired
	if claims.ExpiresAt != nil && claims.ExpiresAt.Time.Before(time.Now()) {
		return nil, fmt.Errorf("token has expired")
	}

	return claims, nil
}

// loadGoogleKeys loads Google's public keys from their endpoint
func (s *AuthService) loadGoogleKeys() error {
	resp, err := http.Get(s.config.GoogleCertsURL)
	if err != nil {
		return fmt.Errorf("failed to fetch Google keys: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	var keysResponse GoogleKeysResponse
	if err := json.Unmarshal(body, &keysResponse); err != nil {
		return fmt.Errorf("failed to unmarshal keys response: %w", err)
	}

	// Convert keys to RSA public keys
	newKeys := make(map[string]*rsa.PublicKey)
	for _, key := range keysResponse.Keys {
		if key.Kty == "RSA" && key.Use == "sig" {
			publicKey, err := s.parseRSAPublicKey(key)
			if err != nil {
				fmt.Printf("Warning: Failed to parse key %s: %v\n", key.Kid, err)
				continue
			}
			newKeys[key.Kid] = publicKey
		}
	}

	s.googleKeys = newKeys
	s.lastUpdate = time.Now()

	fmt.Printf("Loaded %d Google public keys\n", len(s.googleKeys))
	return nil
}

// parseRSAPublicKey parses an RSA public key from Google's JWK format
func (s *AuthService) parseRSAPublicKey(key GoogleKey) (*rsa.PublicKey, error) {
	// Decode the base64url encoded modulus (n)
	nBytes, err := base64.RawURLEncoding.DecodeString(key.N)
	if err != nil {
		return nil, fmt.Errorf("failed to decode modulus: %w", err)
	}

	// Decode the base64url encoded exponent (e)
	eBytes, err := base64.RawURLEncoding.DecodeString(key.E)
	if err != nil {
		return nil, fmt.Errorf("failed to decode exponent: %w", err)
	}

	// Convert bytes to big integers
	n := new(big.Int).SetBytes(nBytes)
	e := new(big.Int).SetBytes(eBytes)

	// Create RSA public key
	publicKey := &rsa.PublicKey{
		N: n,
		E: int(e.Int64()),
	}

	return publicKey, nil
}
