package services

import (
	"fmt"
	"strings"
	model "tesseract-service/internal/tesseract/model/v1"
)

// ConfigService handles configuration operations
type ConfigService struct {
	config *model.Config
}

// NewConfigService creates a new configuration service
func NewConfigService(cfg *model.Config) *ConfigService {
	return &ConfigService{
		config: cfg,
	}
}

// GetFilteredAnalyticsConfig returns analytics configuration filtered by user roles
func (s *ConfigService) GetFilteredAnalyticsConfig(userRoles []string) []model.AnalyticalFunction {
	var filteredFunctions []model.AnalyticalFunction

	for _, function := range s.config.AnalyticsConfig.AnalyticalFunctions {
		// Check if user has access to this analytics function
		if s.hasAccessToAnalyticsFunction(function, userRoles) {
			// Filter business functions
			filteredFunction := function
			filteredFunction.BusinessFunction = s.filterBusinessFunctions(function.BusinessFunction, userRoles)

			// Only include if there are accessible business functions
			if len(filteredFunction.BusinessFunction) > 0 {
				filteredFunctions = append(filteredFunctions, filteredFunction)
			}
		}
	}

	return filteredFunctions
}

// GetFilteredBusinessFunctions returns business functions for an analytics function filtered by user roles
func (s *ConfigService) GetFilteredBusinessFunctions(analyticsFunction string, userRoles []string) ([]model.BusinessFunction, error) {
	// Find the analytics function
	var targetFunction *model.AnalyticalFunction
	for _, function := range s.config.AnalyticsConfig.AnalyticalFunctions {
		if function.ID == analyticsFunction {
			targetFunction = &function
			break
		}
	}

	if targetFunction == nil {
		return nil, fmt.Errorf("analytics function not found: %s", analyticsFunction)
	}

	// Check if user has access to this analytics function
	if !s.hasAccessToAnalyticsFunction(*targetFunction, userRoles) {
		return nil, fmt.Errorf("access denied to analytics function: %s", analyticsFunction)
	}

	// Filter business functions
	return s.filterBusinessFunctions(targetFunction.BusinessFunction, userRoles), nil
}

// GetFilteredAnalyticsTypes returns analytics types for a business function filtered by user roles
func (s *ConfigService) GetFilteredAnalyticsTypes(analyticsFunction, businessFunction string, userRoles []string) ([]model.AnalyticsType, error) {
	// Find the business function
	businessFunctions, err := s.GetFilteredBusinessFunctions(analyticsFunction, userRoles)
	if err != nil {
		return nil, err
	}

	var targetBusinessFunction *model.BusinessFunction
	for _, bf := range businessFunctions {
		if bf.ID == businessFunction {
			targetBusinessFunction = &bf
			break
		}
	}

	if targetBusinessFunction == nil {
		return nil, fmt.Errorf("business function not found: %s", businessFunction)
	}

	// Filter analytics types
	return s.filterAnalyticsTypes(targetBusinessFunction.AnalyticsType, userRoles), nil
}

// hasAccessToAnalyticsFunction checks if user has access to an analytics function
func (s *ConfigService) hasAccessToAnalyticsFunction(function model.AnalyticalFunction, userRoles []string) bool {
	// If analytics function has specific required roles, check those
	if len(function.RequiredRoles) > 0 {
		return s.hasAnyRole(function.RequiredRoles, userRoles)
	}

	// If no specific roles required for analytics function, check if user has access to any business function within it
	// This implements hierarchical access control: if you have access to any business function, you can see the analytics function
	for _, businessFunction := range function.BusinessFunction {
		if s.hasAccessToBusinessFunction(businessFunction, userRoles) {
			return true
		}
	}

	return false
}

// filterBusinessFunctions filters business functions based on user roles
func (s *ConfigService) filterBusinessFunctions(businessFunctions []model.BusinessFunction, userRoles []string) []model.BusinessFunction {
	var filtered []model.BusinessFunction

	for _, bf := range businessFunctions {
		if s.hasAccessToBusinessFunction(bf, userRoles) {
			// Filter analytics types within this business function
			filteredBF := bf
			filteredBF.AnalyticsType = s.filterAnalyticsTypes(bf.AnalyticsType, userRoles)

			// Only include business function if it has at least one accessible analytics type
			if len(filteredBF.AnalyticsType) > 0 {
				filtered = append(filtered, filteredBF)
			}
		}
	}

	return filtered
}

// hasAccessToBusinessFunction checks if user has access to a business function
func (s *ConfigService) hasAccessToBusinessFunction(businessFunction model.BusinessFunction, userRoles []string) bool {
	// If business function has specific required roles, check those
	if len(businessFunction.RequiredRoles) > 0 {
		return s.hasAnyRole(businessFunction.RequiredRoles, userRoles)
	}

	// If no specific roles required for business function, check if user has access to any analytics type within it
	// This implements hierarchical access control: if you have access to any analytics type, you can see the business function
	for _, analyticsType := range businessFunction.AnalyticsType {
		if s.hasAccessToAnalyticsType(analyticsType, userRoles) {
			return true
		}
	}

	return false
}

// filterAnalyticsTypes filters analytics types based on user roles
func (s *ConfigService) filterAnalyticsTypes(analyticsTypes []model.AnalyticsType, userRoles []string) []model.AnalyticsType {
	var filtered []model.AnalyticsType

	for _, at := range analyticsTypes {
		if s.hasAccessToAnalyticsType(at, userRoles) {
			// Filter dashboards within this analytics type
			filteredAT := at
			filteredAT.Dashboard = s.filterDashboards(at.Dashboard, userRoles)

			// Only include analytics type if it has at least one accessible dashboard
			if len(filteredAT.Dashboard) > 0 {
				filtered = append(filtered, filteredAT)
			}
		}
	}

	return filtered
}

// hasAccessToAnalyticsType checks if user has access to an analytics type
func (s *ConfigService) hasAccessToAnalyticsType(analyticsType model.AnalyticsType, userRoles []string) bool {
	// If analytics type has specific required roles, check those
	if len(analyticsType.RequiredRoles) > 0 {
		return s.hasAnyRole(analyticsType.RequiredRoles, userRoles)
	}

	// If no specific roles required for analytics type, check if user has access to any dashboard within it
	// This implements hierarchical access control: if you have access to any dashboard, you can see the analytics type
	for _, dashboard := range analyticsType.Dashboard {
		if s.hasAccessToDashboard(dashboard, userRoles) {
			return true
		}
	}

	return false
}

// filterDashboards filters dashboards based on user roles
func (s *ConfigService) filterDashboards(dashboards []model.Dashboard, userRoles []string) []model.Dashboard {
	var filtered []model.Dashboard

	for _, dashboard := range dashboards {
		if s.hasAccessToDashboard(dashboard, userRoles) {
			filtered = append(filtered, dashboard)
		}
	}

	return filtered
}

// hasAccessToDashboard checks if user has access to a dashboard
func (s *ConfigService) hasAccessToDashboard(dashboard model.Dashboard, userRoles []string) bool {
	// If no required roles specified, check if user has any role that includes this dashboard
	if len(dashboard.RequiredRoles) == 0 {
		dashboardKey := strings.ToUpper(strings.ReplaceAll(dashboard.ID, "-", ""))
		for _, role := range userRoles {
			if strings.Contains(strings.ToUpper(role), dashboardKey) {
				return true
			}
		}
		return false
	}

	// Check if user has any of the required roles
	return s.hasAnyRole(dashboard.RequiredRoles, userRoles)
}

// hasAnyRole checks if user has any of the required roles
func (s *ConfigService) hasAnyRole(requiredRoles, userRoles []string) bool {
	// Check for admin role first
	if s.isAdminUser(userRoles) {
		return true
	}

	for _, required := range requiredRoles {
		for _, userRole := range userRoles {
			if strings.EqualFold(required, userRole) {
				return true
			}
		}
	}
	return false
}

// isAdminUser checks if user has admin privileges
func (s *ConfigService) isAdminUser(userRoles []string) bool {
	for _, role := range userRoles {
		// Check for admin roles or permissions
		if strings.Contains(strings.ToUpper(role), "ADMIN") ||
			strings.Contains(strings.ToUpper(role), "ANALYTICS_ADMIN") {
			return true
		}
	}
	return false
}
