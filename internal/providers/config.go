package providers

import (
	"flag"
	"os"

	"github.com/kelseyhightower/envconfig"
	"gopkg.in/yaml.v3"
)

const (
	PROD = "PROD"
)

type DbConfig struct {
	Host            string `yaml:"host" envconfig:"DB_HOST"`
	Port            int    `yaml:"port" envconfig:"DB_PORT"`
	User            string `yaml:"user" envconfig:"DB_USER"`
	Password        string `yaml:"password" envconfig:"DB_PASSWORD"`
	Name            string `yaml:"name" envconfig:"DB_NAME"`
	Timeout         int    `yaml:"timeout" envconfig:"DB_CONNECTION_TIMEOUT"`
	MaxOpenConn     int    `yaml:"maxOpenConn" envconfig:"DB_MAX_OPEN_CONNECTIONS"`
	MaxIdleConn     int    `yaml:"maxIdleConn" envconfig:"DB_MAX_IDLE_CONNECTIONS"`
	SearchPath      string `yaml:"searchPath" envconfig:"DB_SEARCH_PATH"`
	ConnMaxLifetime string `yaml:"connMaxLifetime" envconfig:"DB_CONNECTION_MAX_LIFETIME"`
}

type RedisConfig struct {
	Address           string `yaml:"address" envconfig:"REDIS_LISTEN_ADDRESS"`
	DbNumber          int    `yaml:"dbNumber" envconfig:"REDIS_DB_NUMBER"`
	MaxIdleConnection string `yaml:"maxIdleConnection" envconfig:"REDIS_MAX_IDLE_CONNECTION"`
	MinIdleConnection string `yaml:"minIdleConnection" envconfig:"REDIS_MIN_IDLE_CONNECTIONS"`
}

type LogglyConfig struct {
	Token         string `yaml:"token" envconfig:"LOGGLY_TOKEN"`
	Host          string `yaml:"host" envconfig:"LOGGLY_HOST"`
	GraylogHost   string `yaml:"graylogHost" envconfig:"GRAYLOG_HOST"`
	GraylogSwitch string `yaml:"graylogSwitch" envconfig:"GRAYLOG_SWITCH"`
	Tag           string `yaml:"tag" envconfig:"LOGGLY_TAG"`
	AppName       string `yaml:"appName"  envconfig:"LOGGLY_APP_NAME"`
	AppVersion    string `yaml:"appVersion" envconfig:"LOGGLY_APP_VERSION"`
}

type S3Config struct {
	AwsAccessKeyId     string `yaml:"aws_access_key_id" envconfig:"AWS_ACCESS_KEY_ID"`
	AwsSecretAccessKey string `yaml:"aws_secret_access_key" envconfig:"AWS_SECRET_ACCESS_KEY"`
	AwsRegion          string `yaml:"aws_region" envconfig:"AWS_REGION"`
	AwsToken           string `yaml:"aws_token" envconfig:"AWS_TOKEN"`
}

type HttpConfig struct {
	Address        string   `yaml:"address" envconfig:"LISTEN_ADDRESS"`
	AllowedOrigins []string `yaml:"allowedOrigins" envconfig:"ALLOW_ORIGIN"`
}

type JwtConfig struct {
	Secret string `yaml:"secret" envconfig:"JWT_SECRET_KEY"`
}

type EnvironmentConfig struct {
	Name string `yaml:"name" envconfig:"ENV_NAME"`
}

type SharePoint struct {
	SiteURL  string `yaml:"SiteURL"`
	Username string `yaml:"Username"`
	Password string `yaml:"Password"`
}

//type GrpcConfig struct {
//	KycServiceGrpcListenAddress string `yaml:"kycServiceGrpcAddress" envconfig:"KYC_GRPC_LISTEN_ADDRESS"`
//}

type GrpcConfig struct {
	ListenAddress                     string `yaml:"address" envconfig:"GRPC_LISTEN_ADDRESS"`
	LocationServiceGrpcListenAddress  string `yaml:"locationServiceGrpcListenAddress" envconfig:"LOCATION_GRPC_LISTEN_ADDRESS"`
	CommunicationGrpcListenAddress    string `yaml:"communicationServiceGrpcListenAddress" envconfig:"COMMUNICATION_GRPC_LISTEN_ADDRESS"`
	ProductCatalogueGrpcListenAddress string `yaml:"productCatelogueGrpcListenAddress" envconfig:"PRODUCT_GRPC_LISTEN_ADDRESS"`
	/*KycServiceGrpcListenAddress      string `yaml:"kycServiceGrpcAddress" envconfig:"KYC_GRPC_LISTEN_ADDRESS"`
	SearchServiceGrpcAddress         string `yaml:"searchServiceGrpcAddress" envconfig:"SEARCH_GRPC_LISTEN_ADDRESS"`
	UserGrpcListenAddress            string `yaml:"userGrpcListenAddress" envconfig:"USER_GRPC_LISTEN_ADDRESS"`*/
}

type MetricsConfig struct {
	WhiteListedEndpoints    []string `yaml:"whiteListEndpoints"`
	WhiteListedEnvironments []string `yaml:"whiteListEnvironments"`
}
type AccountKafka struct {
	Topic         string `yaml:"topic"`
	SourceService string `yaml:"source_service"`
	GroupId       string `yaml:"group_id"`
}

type CustomerKafka struct {
	Topic         string `yaml:"topic"`
	SourceService string `yaml:"source_service"`
	GroupId       string `yaml:"group_id"`
}

type DealKafka struct {
	Topic         string `yaml:"topic"`
	SourceService string `yaml:"source_service"`
	GroupId       string `yaml:"group_id"`
}

type EcommerceKafka struct {
	Topic string `yaml:"topic"`
}

type CommunicationKafka struct {
	Topic         string `yaml:"topic"`
	SourceService string `yaml:"source_service"`
	GroupId       string `yaml:"group_id"`
}

type MessageQueue struct {
	BrokerUrls         []string           `yaml:"broker_urls" envconfig:"KAFKA_BROKER_URLS"`
	Environment        string             `yaml:"environment" envconfig:"ENV_NAME"`
	BatchSize          int                `yaml:"batch_size" envconfig:"KAFKA_BATCH_SIZE"`
	AccountKafka       AccountKafka       `yaml:"account"`
	CustomerKafka      CustomerKafka      `yaml:"customer"`
	DealServiceKafka   DealKafka          `yaml:"deal"`
	CommunicationKafka CommunicationKafka `yaml:"communication"`
	EcommerceKafka     EcommerceKafka     `yaml:"ecommerce"`
}

type ExcelDownloadPath struct {
	Path string `yaml:"path"`
}

type AuthenticationConfig struct {
	WebhookAuthFile                string   `yaml:"webhookAuthenticationFile"`
	WebhookAuthenticationEndpoints []string `yaml:"webhookAuthenticationEndpoints"`
}

type AutoDealerRepCreationConfig struct {
	Enabled bool `yaml:"enabled"`
}

type AppConfig struct {
	DbConfig                    DbConfig                    `yaml:"db"`
	RedisConfig                 RedisConfig                 `yaml:"redis"`
	LogglyConfig                LogglyConfig                `yaml:"loggly"`
	HttpConfig                  HttpConfig                  `yaml:"http"`
	JwtConfig                   JwtConfig                   `yaml:"jwt"`
	EnvironmentConfig           EnvironmentConfig           `yaml:"environment"`
	MetricsConfig               MetricsConfig               `yaml:"metrics"`
	GrpcConfig                  GrpcConfig                  `yaml:"grpc"`
	S3Config                    S3Config                    `yaml:"s3Config"`
	ProductGrpcConfig           GrpcConfig                  `yaml:"product_grpc"`
	MessageQueue                MessageQueue                `yaml:"message_queue"`
	SharePoint                  SharePoint                  `yaml:"sharepoint_auth_config"`
	ExcelDownloadPath           ExcelDownloadPath           `yaml:"excel_download_path"`
	AuthenticationConfig        AuthenticationConfig        `yaml:"authentication"`
	AutoDealerRepCreationConfig AutoDealerRepCreationConfig `yaml:"auto_dealer_rep_creation"`
}

var (
	config     *AppConfig
	configPath string
)

func loadConfig() AppConfig {
	f, err := os.Open(configPath)
	if err != nil {
		panic(err)
	}
	defer f.Close()
	var cfg AppConfig
	decoder := yaml.NewDecoder(f)
	err = decoder.Decode(&cfg)
	err = envconfig.Process("", &cfg)
	if err != nil {
		panic(err)
	}
	return cfg
}

func GetConfig(path string) (AppConfig, error) {
	if config == nil {
		flag.StringVar(&configPath, "config", path, "Path to config file")
		flag.Parse()
		cnf := loadConfig()
		config = &cnf
	}
	return *config, nil
}
