package api

import (
	"net/http"
	"strings"

	"tesseract-service/internal/services"

	"github.com/gin-gonic/gin"
)

// Handler contains all the API handlers
type Handler struct {
	authService   *services.AuthService
	configService *services.ConfigService
	userService   *services.UserService
}

// NewHandler creates a new API handler
func NewHandler(authService *services.AuthService, configService *services.ConfigService, userService *services.UserService) *Handler {
	return &Handler{
		authService:   authService,
		configService: configService,
		userService:   userService,
	}
}

// HealthCheck handles health check requests
func (h *Handler) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":  "healthy",
		"service": "tesseract-service",
		"version": "1.0.0",
	})
}

// ValidateToken handles token validation requests
func (h *Handler) ValidateToken(c *gin.Context) {
	var req struct {
		Token string `json:"token" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request body",
		})
		return
	}

	// Validate the JWT token
	claims, err := h.authService.ValidateToken(req.Token)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Invalid token",
			"details": err.Error(),
		})
		return
	}

	// Get user profile
	userProfile, err := h.userService.GetUserProfile(claims.Email)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get user profile",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"valid": true,
		"user":  userProfile,
		"claims": gin.H{
			"sub":   claims.Sub,
			"email": claims.Email,
			"name":  claims.Name,
		},
	})
}

// AuthMiddleware validates JWT tokens for protected routes
func (h *Handler) AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Authorization header required",
			})
			c.Abort()
			return
		}

		// Extract token from "Bearer <token>"
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid authorization header format",
			})
			c.Abort()
			return
		}

		token := parts[1]

		// Validate the JWT token
		claims, err := h.authService.ValidateToken(token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid token",
				"details": err.Error(),
			})
			c.Abort()
			return
		}

		// Get user profile and store in context
		userProfile, err := h.userService.GetUserProfile(claims.Email)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to get user profile",
			})
			c.Abort()
			return
		}

		// Store user info in context
		c.Set("user", userProfile)
		c.Set("claims", claims)
		c.Next()
	}
}

// GetAnalyticsConfig returns filtered analytics configuration based on user roles
func (h *Handler) GetAnalyticsConfig(c *gin.Context) {
	// Get user from context
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "User not found in context",
		})
		return
	}

	userProfile := user.(*services.UserProfile)
	userRoles := userProfile.UserPermissionAndEntityPayload.Roles

	// Get filtered configuration
	filteredConfig := h.configService.GetFilteredAnalyticsConfig(userRoles)

	c.JSON(http.StatusOK, gin.H{
		"analyticalFunctions": filteredConfig,
	})
}

// GetBusinessFunctions returns business functions for an analytics function
func (h *Handler) GetBusinessFunctions(c *gin.Context) {
	analyticsFunction := c.Param("analyticsFunction")

	// Get user from context
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "User not found in context",
		})
		return
	}

	userProfile := user.(*services.UserProfile)
	userRoles := userProfile.UserPermissionAndEntityPayload.Roles

	// Get filtered business functions
	businessFunctions, err := h.configService.GetFilteredBusinessFunctions(analyticsFunction, userRoles)
	if err != nil {
		c.JSON(http.StatusForbidden, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"businessFunctions": businessFunctions,
	})
}

// GetAnalyticsTypes returns analytics types for a business function
func (h *Handler) GetAnalyticsTypes(c *gin.Context) {
	analyticsFunction := c.Param("analyticsFunction")
	businessFunction := c.Param("businessFunction")

	// Get user from context
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "User not found in context",
		})
		return
	}

	userProfile := user.(*services.UserProfile)
	userRoles := userProfile.UserPermissionAndEntityPayload.Roles

	// Get filtered analytics types
	analyticsTypes, err := h.configService.GetFilteredAnalyticsTypes(analyticsFunction, businessFunction, userRoles)
	if err != nil {
		c.JSON(http.StatusForbidden, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"analyticsTypes": analyticsTypes,
	})
}

// GetUserProfile returns the current user's profile
func (h *Handler) GetUserProfile(c *gin.Context) {
	// Get user from context
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "User not found in context",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"user": user,
	})
}

// GetUserPermissions returns the current user's permissions
func (h *Handler) GetUserPermissions(c *gin.Context) {
	// Get user from context
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "User not found in context",
		})
		return
	}

	userProfile := user.(*services.UserProfile)

	c.JSON(http.StatusOK, gin.H{
		"roles":       userProfile.UserPermissionAndEntityPayload.Roles,
		"permissions": userProfile.UserPermissionAndEntityPayload.Permissions,
	})
}
