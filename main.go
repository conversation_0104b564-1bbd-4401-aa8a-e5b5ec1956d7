package main

import (
	"net/http"
	"tesseract-service/internal/providers"

	"github.com/gorilla/mux"
	"github.com/justinas/alice"
)

// Define IHandler interface if it doesn't exist elsewhere
type IHandler interface {
	Init()
}

func main() {
	// Load configuration from file
	config, err := providers.GetConfig("tesseract-service-configuration.yml")
	if err != nil {
		panic(err)
	}

	// Initialize logger
	var logglyFlusher func()
	logger, logglyFlusher := providers.GetLoggly(config)
	defer logglyFlusher()

	// Initialize router
	apiRouter := mux.NewRouter()

	// Setup routes
	apiRouter.HandleFunc("/ping", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("pong"))
	}).Methods("GET")

	// Initialize handlers
	handlers := make([]IHandler, 0)
	// Add your handlers here
	// handlers = append(handlers, yourHandler)

	// Initialize each handler
	for _, handler := range handlers {
		handler.Init()
	}

	// Create middleware chain
	middleware := alice.New()

	// Setup CORS
	corsMiddleware := func(h http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Access-Control-Allow-Origin", "*")
			w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
			w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

			if r.Method == "OPTIONS" {
				w.WriteHeader(http.StatusOK)
				return
			}

			h.ServeHTTP(w, r)
		})
	}

	// Apply middleware
	handler := middleware.Then(apiRouter)
	handler = corsMiddleware(handler)

	// Start server
	logger.Info("Starting server on " + config.HttpConfig.Address)
	if err := http.ListenAndServe(config.HttpConfig.Address, handler); err != nil {
		logger.WithField("err", err).Error("Error in tesseract service webserver")
	}
}
