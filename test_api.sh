#!/bin/bash

# Test script for the analytics config API

echo "Testing Analytics Config API..."

# Start the server in background
echo "Starting tesseract-service..."
./tesseract-service &
SERVER_PID=$!

# Wait for server to start
sleep 3

# Test 1: Valid request with roles
echo "Test 1: Valid request with roles"
curl -X POST http://localhost:9017/api/config/analytics/v1 \
  -H "Content-Type: application/json" \
  -d '{"roles": ["TESSERACT_RISK_SCORE_TRENDS", "TESSERACT_RISK_CATEGORY_DISTRIBUTION"]}' \
  | jq '.'

echo -e "\n"

# Test 2: Empty roles array
echo "Test 2: Empty roles array (should return error)"
curl -X POST http://localhost:9017/api/config/analytics/v1 \
  -H "Content-Type: application/json" \
  -d '{"roles": []}' \
  | jq '.'

echo -e "\n"

# Test 3: Invalid JSON
echo "Test 3: Invalid JSON (should return error)"
curl -X POST http://localhost:9017/api/config/analytics/v1 \
  -H "Content-Type: application/json" \
  -d '{"invalid": json}' \
  | jq '.'

echo -e "\n"

# Test 4: Admin role (should return everything)
echo "Test 4: Admin role (should return everything)"
curl -X POST http://localhost:9017/api/config/analytics/v1 \
  -H "Content-Type: application/json" \
  -d '{"roles": ["ANALYTICS_ADMIN"]}' \
  | jq '.'

echo -e "\n"

# Clean up
echo "Stopping server..."
kill $SERVER_PID

echo "Tests completed!"
